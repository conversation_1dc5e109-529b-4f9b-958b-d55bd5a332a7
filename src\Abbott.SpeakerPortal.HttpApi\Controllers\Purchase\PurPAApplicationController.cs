﻿using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Budget.Subbudget;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.Approve;
using Abbott.SpeakerPortal.Contracts.Common.Attachment;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPOApplication;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Filters;
using Abbott.SpeakerPortal.OEC.SpeakerLevel;
using Abbott.SpeakerPortal.Permissions;
using Abbott.SpeakerPortal.Utils;

using ClosedXML.Excel;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.CognitiveServices.Vision.ComputerVision.Models;

using MiniExcelLibs;

using Senparc.Weixin.WxOpen.Entities;

using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Volo.Abp.Application.Dtos;

using static Abbott.SpeakerPortal.Enums.DataverseEnums;
using static Microsoft.Extensions.Logging.EventSource.LoggingEventSource;

namespace Abbott.SpeakerPortal.Controllers.Purchase
{
    /// <summary>
    /// 付款申请
    /// </summary>
    [ApiExplorerSettings(GroupName = SwaggerGrouping.PROCURE_MANAGE)]
    public class PurPAApplicationController : SpeakerPortalController
    {
        private readonly IPurPAApplicationService _paApplicationService;
        public PurPAApplicationController(IPurPAApplicationService paApplicationService)
        {
            _paApplicationService = paApplicationService;
        }

        /// <summary>
        /// 采购管理-付款申请-列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult<PagedResultDto<PurPAApplicationListDto>>))]
        [Authorize(SpeakerPortalPermissions.Purchase.Payment)]
        public async Task<IActionResult> GetPAApplicationListAsync([FromBody] PAListSearchRequestDto request)
        {
            var result = await _paApplicationService.GetPAApplicationListAsync(request);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 导出付款列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [Authorize(SpeakerPortalPermissions.Purchase.PaymentExport)]
        public async Task<IActionResult> ExportPAApplication([FromBody] PAListSearchRequestDto request)
        {
            var result = await _paApplicationService.ExportPAListAsync(request);
            MemoryStream stream = new();
            stream.SaveAs(result, true, "SheetName");
            stream.Seek(0, SeekOrigin.Begin);
            return File(stream, "application/octet-stream", $"付款列表{DateTime.Now.ToString("yyyyMMddHHmmss")}.xlsx");
        }

        /// <summary>
        /// 任务中心-付款申请我发起的视角-列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult<PagedResultDto<PurPAApplicationDto>>))]
        [Authorize(SpeakerPortalPermissions.TaskCenter.LaunchPaymentApplication)]
        public async Task<IActionResult> GetPAInitiateListAsync([FromBody] PAListSearchRequestDto request)
        {
            var result = await _paApplicationService.GetPAInitiateListAsync(request);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 任务中心-我审批的付款申请-列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult<PagedResultDto<PurPAApplicationDto>>))]
        [Authorize(SpeakerPortalPermissions.TaskCenter.ApprovePaymentApplication)]
        public async Task<IActionResult> GetPAApprovalListAsync([FromBody] PAListSearchRequestDto request)
        {
            var result = await _paApplicationService.GetPAApplicationApprovalListAsync(request);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 获取付款申请详情
        /// </summary>
        /// <param name="paId"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<PADetailsResponseDto>))]
        [Authorize(SpeakerPortalPermissions.Purchase.PaymentRead)]
        [Authorize(SpeakerPortalPermissions.TaskCenter.LaunchPaymentApplication)]
        [Authorize(SpeakerPortalPermissions.TaskCenter.ApprovePaymentApplication)]
        public async Task<IActionResult> GetPAApplicationDetailsAsync(Guid paId)
        {
            //var authList = await AuthorizationService.IsGrantedAnyAsync(SpeakerPortalPermissions.Purchase.Payment, SpeakerPortalPermissions.TaskCenter.ApprovePaymentApplication, SpeakerPortalPermissions.TaskCenter.LaunchPaymentApplication);
            //if (!authList) return Forbid();

            // 启动两个异步任务
            var task1 = _paApplicationService.GetPAApplicationDetailsAsync(paId);
            var task2 = _paApplicationService.GetPAApplicicationFinancialVoucher(paId);
            var tasks = await Task.WhenAll(task1, task2).ConfigureAwait(false);
            if (!tasks[0].Success)
            {
                return Ok(tasks[0]);
            }
            else 
            {
                var result = tasks[0].Data as PADetailsResponseDto;
                if (tasks[1].Success)
                    result.PAFinancialVoucherInfo = tasks[1].Data as PAApplicicationFinancialVoucherDto;
                return Ok(MessageResult.SuccessResult(result));
            }
        }
        /// <summary>
        /// 补录赞助回报点
        /// </summary>
        /// <param name="paUploadFile"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        [Authorize(SpeakerPortalPermissions.TaskCenter.LaunchAction)]
        [ValidateParticipant(nameof(paUploadFile), PropertyName = nameof(paUploadFile.PaId))]
        public async Task<IActionResult> UploadSponsorShipAsync([FromBody] PaUploadFileDto paUploadFile)
        {
            var result = await _paApplicationService.UploadSponsorShipAsync(paUploadFile);
            return Ok(result);
        }

        /// <summary>
        /// 提交付款
        /// </summary>
        /// <param name="paDetails"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        [Authorize(SpeakerPortalPermissions.TaskCenter.LaunchAction)]
        public async Task<IActionResult> SubmitPaInvoiceAsync([FromBody] PADetailsEditDto paDetails)
        {
            var valid = await LazyServiceProvider.LazyGetService<ICommonService>().ValidateApplicantRelatedOperationAsync(paDetails.PAApplication.Id);
            if (!valid)
                return Ok(MessageResult.FailureResult(messageModel: MessageModelBase.HaveNoPermissions));

            var result = await _paApplicationService.SubmitPaInvoiceAsync(paDetails);
            return Ok(result);
        }

        /// <summary>
        /// 获取当前供应商银行信息
        /// </summary>
        /// <param name="vendorId"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<BankCardInfoDto>))]
        [Authorize(SpeakerPortalPermissions.TaskCenter.LaunchPaymentApplication)]
        public async Task<IActionResult> GetBankCardInfoAsync(Guid vendorId)
        {
            var result = await _paApplicationService.GetBankCardInfoAsync(vendorId);
            return Ok(result);
        }

        /// <summary>
        /// PA作废
        /// </summary>
        /// <param name="invalid"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        [Authorize(SpeakerPortalPermissions.TaskCenter.LaunchAction)]
        public async Task<IActionResult> PAInvalidAsync([FromBody] InvalidRequestDto invalid)
        {
            var result = await _paApplicationService.PAInvalidAsync(invalid);
            return Ok(result);
        }
        /// <summary>
        /// 付款审批任务列表
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        [HttpPost]
        [Authorize(SpeakerPortalPermissions.TaskCenter.Payment)]
        [ProducesDefaultResponseType(typeof(MessageResult<PagedResultDto<PATApprovalTaskResponseListDto>>))]
        public async Task<IActionResult> GetPAApprovalTaskList([FromBody] PurPAApprovalResquestDto requestDto)
        {
            var result = await _paApplicationService.GetPAApprovalListAsync(requestDto);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 代付款审批任务列表-导出
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        [HttpPost]
        [Authorize(SpeakerPortalPermissions.TaskCenter.PaymentExport)]
        public async Task<IActionResult> ExportPAApprovalListAsync([FromBody] PurPAApprovalResquestDto requestDto) 
        {
            try
            {
                var result = await _paApplicationService.ExportPAApprovalListAsync(requestDto);
                MemoryStream stream = new();
                stream.SaveAs(result, true, "SheetName");
                stream.Seek(0, SeekOrigin.Begin);
                return File(stream, "application/octet-stream", "PA Approval.xlsx");
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        /// <summary>
        /// 线上分单单据
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult<PAAllocationResponseDto>))]
        [Authorize(SpeakerPortalPermissions.TaskCenter.PaymentSplitOrder)]
        public async Task<IActionResult> OnlineOrderAllocation([FromBody] PAAllocationRequestDto requestDto)
        {
            var result = await _paApplicationService.OrderAllocationAsync(requestDto);
            return Ok(result);
        }
        /// <summary>
        /// 线下分单单据
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult<PAAllocationResponseDto>))]
        [Authorize(SpeakerPortalPermissions.TaskCenter.PaymentSplitOrder)]
        public async Task<IActionResult> OfflineOrderAllocation([FromBody] PAAllocationRequestDto requestDto)
        {
            var result = await _paApplicationService.OrderAllocationAsync(requestDto);
            return Ok(result);
        }
        /// <summary>
        /// 分单单据按照比例
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult<PAAllocationResponseDto>))]
        [Authorize(SpeakerPortalPermissions.TaskCenter.PaymentSplitOrder)]
        public async Task<IActionResult> OrderAllocationByPercentage([FromBody] PAAllocationPercentageRequestDto requestDto)
        {
            var result = await _paApplicationService.OrderAllocationByPercentageAsync(requestDto);
            return Ok(result);
        }

        /// <summary>
        /// 获取财务信息模块
        /// </summary>
        /// <param name="paId"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<PAApplicicationFinancialVoucherDto>))]
        //[Authorize(SpeakerPortalPermissions.TaskCenter.ApprovePaymentApplication)]
        //[Authorize(SpeakerPortalPermissions.TaskCenter.LaunchPaymentApplication)]
        public async Task<IActionResult> GetPAApplicicationFinancialVoucher(Guid paId)
        {
            //var authList = await AuthorizationService.IsGrantedAnyAsync(SpeakerPortalPermissions.TaskCenter.ApprovePaymentApplication, SpeakerPortalPermissions.TaskCenter.LaunchPaymentApplication);
            //if (!authList) return Forbid();
            var result = await _paApplicationService.GetPAApplicicationFinancialVoucher(paId);
            return Ok(result);
        }

        /// <summary>
        /// 上传财务凭证（上传分摊）
        /// </summary>
        /// <param name="excelFile"></param>
        /// <param name="paId"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult<List<PAFinancialVoucherInfoDto>>))]
        [Authorize(SpeakerPortalPermissions.TaskCenter.LaunchAction)]
        public async Task<IActionResult> UploadFinancialVoucherExcelAsync([FromForm] IFormFile excelFile, [FromForm] Guid paId)
        {
            List<PAFinancialVoucherInfoDto> paFinancialVoucherInfos = new List<PAFinancialVoucherInfoDto>();
            if (excelFile == null || excelFile.Length == 0)
            {
                return Ok(MessageResult.FailureResult("请选择文件"));
            }
            var str = Path.GetExtension(excelFile.FileName);
            string[] strings = [".xlsx", ".xls"];
            if (!strings.Contains(str))
            {
                return Ok(MessageResult.FailureResult("请上传excel文件"));
            }
            try
            {
                using (var stream = excelFile.OpenReadStream())
                {
                    // 使用ClosedXML读取Excel
                    using (var workbook = new XLWorkbook(stream))
                    {
                        // 读取第一个工作表
                        var worksheet = workbook.Worksheet(1);
                        int rowNumber = 7;
                        // 遍历行读取数据
                        foreach (var row in worksheet.RowsUsed().Skip(5)) // 跳过标题行及说明行
                        {
                            PAFinancialVoucherInfoDto paFinancialVoucherInfo = new PAFinancialVoucherInfoDto();
                            string invoiceLineAmount = row.Cell(1).GetString();
                            string companyCode = row.Cell(2).GetString();
                            string divisionCode = row.Cell(3).GetString();
                            string costCenter = row.Cell(4).GetString();
                            string natureAccount = row.Cell(5).GetString();
                            string subAccount = row.Cell(6).GetString();
                            string location = row.Cell(7).GetString();
                            var amount = decimal.TryParse(invoiceLineAmount, out _) ? decimal.Parse(invoiceLineAmount) : 0M;
                            if (amount == 0M)
                                return Ok(MessageResult.FailureResult($"行号{rowNumber}，金额不能为0！"));
                            if (!RandomHelper.IsAmountValid(amount))
                                return Ok(MessageResult.FailureResult($"行号{rowNumber}，金额最多两位小数！"));
                            paFinancialVoucherInfo.InvoiceLineAmount = amount;
                            paFinancialVoucherInfo.CompanyCode = companyCode;
                            paFinancialVoucherInfo.DivisionCode = divisionCode;
                            paFinancialVoucherInfo.CostCenter = costCenter;
                            paFinancialVoucherInfo.NatureAccount = natureAccount;
                            paFinancialVoucherInfo.SubAccount = subAccount;
                            paFinancialVoucherInfo.Location = location;
                            paFinancialVoucherInfos.Add(paFinancialVoucherInfo);
                            rowNumber++;
                        }
                    }
                }
                if (!paFinancialVoucherInfos.Any())
                    return Ok(MessageResult.FailureResult("未读取到可用数据，请检查模板文件是否正确"));
                var result = await _paApplicationService.GetPAFinancialVoucherInfoAsync(paFinancialVoucherInfos, paId);
                if (result == null)
                {
                    return Ok(MessageResult.FailureResult("未查询到相关的付款信息"));
                }
                return Ok(MessageResult.SuccessResult(result));
            }
            catch (Exception ex)
            {
                return Ok(MessageResult.FailureResult(ex.Message));
            }
        }

        /// <summary>
        /// 导出财务凭证（导出分摊）
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Authorize(SpeakerPortalPermissions.TaskCenter.LaunchAction)]
        public async Task<IActionResult> ExportFinancialVoucherExcelAsync([FromBody] List<PAFinancialVoucherInfoDto> financialVoucherInfos)
        {
            string fileName = "Financial Voucher Template.xlsx";
            var attachmentService = LazyServiceProvider.LazyGetService<IAttachmentService>();
            var result = await attachmentService.GetTemplatesFileAsync(TemplateTypes.Financial_Voucher, fileName, financialVoucherInfos);
            if (result == null)
            {
                return Ok(MessageResult.FailureResult("未找到文件模版,请联系管理员"));
            }
            string contentType = "application/octet-stream";
            return File(result, contentType, fileName);
        }

        /// <summary>
        /// 获取退回模板
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<List<ReturnReasonTemplateDto>>))]
        [Authorize(SpeakerPortalPermissions.TaskCenter.LaunchAction)]
        public async Task<IActionResult> GetWithdrawOpinionInfoAsync()
        {
            var data = await _paApplicationService.GetWithdrawOpinionInfoAsync();
            return Ok(MessageResult.SuccessResult(data));
        }

        /// <summary>
        /// 退回申请人、退回补件、退回原件
        /// </summary>
        /// <param name="pAReturnReason"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        [Authorize(SpeakerPortalPermissions.TaskCenter.LaunchAction)]
        public async Task<IActionResult> PAApplicationWithdrawAsync([FromBody] PAInsertReturnReasonInfoDto pAReturnReason)
        {
            var result = await _paApplicationService.PAApplicationWithdrawAsync(pAReturnReason);
            return Ok(result);
        }

        /// <summary>
        /// 初审完成
        /// </summary>
        /// <param name="paPreliminaryApprovalCompleted"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        [Authorize(SpeakerPortalPermissions.TaskCenter.LaunchAction)]
        public async Task<IActionResult> PreliminaryApprovalCompletedAsync([FromBody] PAPreliminaryApprovalCompletedDto paPreliminaryApprovalCompleted)
        {
            var result = await _paApplicationService.PreliminaryApprovalCompletedAsync(paPreliminaryApprovalCompleted);
            return Ok(result);
        }

        /// <summary>
        /// 财务复审完成 同意
        /// </summary>
        /// <param name="approval"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        [Authorize(SpeakerPortalPermissions.TaskCenter.LaunchAction)]
        public async Task<IActionResult> FinancialReviewApprovalAsync([FromBody] FinancialReviewApprovalDto approval)
        {
            var result = await _paApplicationService.FinancialReviewApprovalAsync(approval);
            return Ok(result);
        }

        /// <summary>
        /// 财务复审完成 同意 批量
        /// </summary>
        /// <param name="approvals"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        [Authorize(SpeakerPortalPermissions.TaskCenter.BatchPaymentApproval)]
        public async Task<IActionResult> FinancialBatchReviewApprovalAsync([FromBody] FinancialBatchReviewApprovalDto approvals)
        {
            var result = await _paApplicationService.FinancialBatchReviewApprovalAsync(approvals);
            return Ok(result);
        }

        /// <summary>
        /// 获取PA退回意见
        /// </summary>
        /// <param name="paId"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<List<PAReturnReasonInfoReponseDto>>))]
        [Authorize(SpeakerPortalPermissions.TaskCenter.ApprovePaymentApplication)]
        [Authorize(SpeakerPortalPermissions.TaskCenter.LaunchPaymentApplication)]
        public async Task<IActionResult> GetPAReturnReasonByPAIdAsync(Guid paId)
        {
            //var authList = await AuthorizationService.IsGrantedAnyAsync(SpeakerPortalPermissions.TaskCenter.ApprovePaymentApplication, SpeakerPortalPermissions.TaskCenter.LaunchPaymentApplication);
            //if (!authList) return Forbid();
            var result = await _paApplicationService.GetPAReturnReasonByPAIdAsync(paId);
            return Ok(MessageResult.SuccessResult(result));
        }
        /// <summary>
        /// 更新PA退回意见
        /// </summary>
        /// <param name="pAReturnReason"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        [Authorize(SpeakerPortalPermissions.TaskCenter.LaunchAction)]
        public async Task<IActionResult> UpdatePAReturnReasonByPAIdAsync([FromBody] List<PAUpdateReturnReasonInfoDto> pAReturnReason)
        {
            var result = await _paApplicationService.UpdatePAReturnReasonByPAIdAsync(pAReturnReason);
            return Ok(result);
        }
        /// <summary>
        /// 获取线下单据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<PATApprovalTaskResponseListDto>))]
        [Authorize(SpeakerPortalPermissions.TaskCenter.Payment)]
        public async Task<IActionResult> GetPAOfflineApproval([FromQuery] string Code)
        {
            var result = await _paApplicationService.GetPAOfflineApprovalAsync(Code);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 获取财务凭证信息填写可选值
        /// </summary>
        /// <param name="paId"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<OptionalFinancialInfoDto>))]
        [Authorize(SpeakerPortalPermissions.TaskCenter.LaunchPaymentApplication)]
        public async Task<IActionResult> GetOptionalFinancialInfoAsync(Guid paId)
        {
            var result = await _paApplicationService.GetOptionalFinancialInfoAsync(paId);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// PA根据关键字获取费用性质
        /// </summary>
        /// <param name="paId"></param>
        /// <param name="keywords"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<List<DropdownListDto<string, string>>>))]
        [Authorize(SpeakerPortalPermissions.TaskCenter.LaunchPaymentApplication)]
        public async Task<IActionResult> GetNatureAccountAsync(Guid paId, string keywords)
        {
            var result = await _paApplicationService.GetNatureAccountAsync(paId, keywords);
            return Ok(MessageResult.SuccessResult(result));
        }
        /// <summary>
        /// 获取单据Bu下的财务审批岗
        /// </summary>
        /// <param name="BuIds"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult<List<ApprovalPersonListResponseDto>>))]
        public async Task<IActionResult> GetFinancialInitialUsers([FromBody] List<Guid> BuIds)
        {
            var result = await _paApplicationService.GetFinancialInitialUsersAsync(BuIds);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// PA 撤回 （财务分单）
        /// </summary>
        /// <param name="paRevoke"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        [Authorize(SpeakerPortalPermissions.TaskCenter.LaunchAction)]
        public async Task<IActionResult> PARevokeApplicationAsync([FromBody]PARevokeDto paRevoke) 
        {
            var result = await _paApplicationService.PARevokeApplicationAsync(paRevoke);
            return Ok(result);
        }
    }
}
