﻿using Abbott.SpeakerPortal.AppServices.Integration.EpdOlineMeeting;
using Abbott.SpeakerPortal.Contracts.Integration.EpdOlineMeeting;
using Abbott.SpeakerPortal.Contracts.Integration.EpdPortal.EpdHcp;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Hangfire.Logging;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Text.Json;
using Abbott.SpeakerPortal.Extension;
using Flurl.Http;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.Common;

namespace Abbott.SpeakerPortal.AppServices.Integration.EpdPortal
{
    public class EPDHcpService : SpeakerPortalAppService, IEPDHcpService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<EPDHcpService> _logger;
        private readonly IConfiguration _configuration;
        private readonly ICommonService _commonService;

        public EPDHcpService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _logger = serviceProvider.GetService<ILogger<EPDHcpService>>();
            _configuration = serviceProvider.GetService<IConfiguration>();
            _commonService = _serviceProvider.GetService<ICommonService>();
        }

        /// <summary>
        /// 医生查询接口
        /// </summary>
        /// <returns></returns>
        public async Task<List<DoctorData>> QueryEpdDoctorsAsync(DoctorQueryRequest doctorRequest) 
        {
            var log = new SetOperationLogRequestDto();
            try
            {
                // 和EPD接口通信
                var url = _configuration["Integrations:OM:BaseUrl"]!.TrimEnd('/') + "/api/nextBpm/hcp/page";
                var headers = BuildAuthorizationHeaders();
                if (headers == null)
                {
                    _logger.LogError($"End to execute QueryEpdDoctors, failed to build authorization headers");
                    return new List<DoctorData>();
                }
                string postString = JsonSerializer.Serialize(doctorRequest);
                log = _commonService.InitOperationLog("Epd Query Epd Doctors", "医生查询", url + "|" + postString);
                var response = await url.WithHeaderJson(headers).PostStringAsync(postString);
                string responseData = await response.GetStringAsync();
                _commonService.LogResponse(log, responseData);
                var resObj = JsonSerializer.Deserialize<OmResponseDto>(responseData);
                if (resObj.success) 
                {
                    var json = JsonSerializer.Serialize(resObj.data);
                    var data = JsonSerializer.Deserialize<List<DoctorData>>(json);
                    return data;
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError($"QueryEpdDoctorsAsync Error : {ex.Message}");
                _commonService.LogResponse(log, ex.ToString(), false);
                return new List<DoctorData>();
            }
        }

        #region 签名和认证 
        private Dictionary<string, string> BuildAuthorizationHeaders()
        {
            try
            {
                var result = new Dictionary<string, string>();
                var appId = _configuration["Integrations:OM:AppId"];
                var appSecret = _configuration["Integrations:OM:AppSecret"];
                var appVersion = _configuration["Integrations:OM:AppVersion"];

                DateTime dateTime = DateTime.UtcNow;
                TimeSpan timeSpan = dateTime - new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
                long timestamp = (long)timeSpan.TotalSeconds;
                var data = $"{appId}-{appSecret}-{timestamp}-{appVersion}";

                HMACSHA256 hmac = new HMACSHA256(Encoding.UTF8.GetBytes(appSecret));
                byte[] hashValue = hmac.ComputeHash(Encoding.UTF8.GetBytes(data));
                var sign = BitConverter.ToString(hashValue).Replace("-", "").ToLowerInvariant();

                result.Add("appId", appId);
                result.Add("timestamp", timestamp.ToString());
                result.Add("appVersion", appVersion);
                result.Add("sign", sign);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to builder authorization headers, error message:" + ex.Message);
                return null;
            }

        }

        private MessageResult ValidateSign(OmAuthorizationDto request)
        {
            try
            {
                MessageResult validateResult = OmAuthorizationDto.Validate(request, _configuration);
                if (!validateResult.Success)
                {
                    return validateResult;
                }

                var appId = _configuration["Integrations:OM:AppId"];
                var appSecret = _configuration["Integrations:OM:AppSecret"];
                var appVersion = _configuration["Integrations:OM:AppVersion"];
                var data = $"{appId}-{appSecret}-{request.Timestamp}-{appVersion}";

                HMACSHA256 hmac = new HMACSHA256(Encoding.UTF8.GetBytes(appSecret));
                byte[] hashValue = hmac.ComputeHash(Encoding.UTF8.GetBytes(data));
                var sign = BitConverter.ToString(hashValue).Replace("-", "").ToLowerInvariant();

                if (sign != request.Sign)
                {
                    return MessageResult.FailureResult(new object(), new MessageModelBase { Code = 403, Message = "Failed to validate sign, invalid sign!" });
                }

                return MessageResult.SuccessResult();
            }
            catch (Exception ex)
            {
                return MessageResult.FailureResult("Failed to validate sign, error message:" + ex.Message);
            }
        }
        #endregion
    }
}
