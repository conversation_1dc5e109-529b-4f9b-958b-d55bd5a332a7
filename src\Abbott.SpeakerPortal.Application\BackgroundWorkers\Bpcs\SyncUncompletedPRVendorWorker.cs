﻿using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Integration.Dspot;
using Hangfire;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.BackgroundWorkers.Hangfire;

namespace Abbott.SpeakerPortal.BackgroundWorkers.Bpcs
{
    [AutomaticRetry(Attempts = 0)]
    public class SyncUncompletedPRVendorWorker: SpeakerPortalBackgroundWorkerBase
    {
        private IInteBpcsUncompletedPRVendorService _inteBpcsUncompletedPRVendorService;
        private IScheduleJobLogService _jobLogService;

        public SyncUncompletedPRVendorWorker(IServiceProvider serviceProvider)
        {
            _inteBpcsUncompletedPRVendorService = serviceProvider.GetService<IInteBpcsUncompletedPRVendorService>();
            _jobLogService = serviceProvider.GetService<IScheduleJobLogService>();
            CronExpression = "0 0 23 L * ?";//每月Last day 23:00
            
        }
        public override async Task DoWorkAsync(CancellationToken cancellationToken = default)
        {
            var log = _jobLogService.InitSyncLog("SyncUncompletedPRVendor");
            try
            {
                //逻辑处理
                log.Remark = await _inteBpcsUncompletedPRVendorService.SyncUncompletedPRVendor();
            }
            catch (Exception ex)
            {
                log.IsSuccess = false;
                log.Remark = ex.ToString();
            }
            finally
            {
                _jobLogService.SyncLog(log);
            }
            
        }
    }
}
