﻿using Abbott.SpeakerPortal.AppServices.Common;
using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.Approve;
using Abbott.SpeakerPortal.Contracts.Common.Common;
using Abbott.SpeakerPortal.Contracts.Common.PowerApp;
using Abbott.SpeakerPortal.Contracts.Dataverse;
using Abbott.SpeakerPortal.Contracts.Graph;
using Abbott.SpeakerPortal.Contracts.Report;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.Common.Workflow.WorkflowTasks;
using Abbott.SpeakerPortal.Extension;

using DocumentFormat.OpenXml.Drawing.Diagrams;

using Flurl.Http;

using Hangfire;
using Hangfire.Logging;

using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Logging;
using Microsoft.Identity.Client;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Extensions;

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http.Json;
using System.Net.Mail;
using System.Security.Cryptography.X509Certificates;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

using Volo.Abp.BackgroundWorkers.Hangfire;
using Volo.Abp.Data;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;

namespace Abbott.SpeakerPortal.BackgroundWorkers.Graph
{
    public class SyncUserStatusWorker : SpeakerPortalBackgroundWorkerBase
    {
        private readonly ILogger<SyncUserStatusWorker> _logger;
        private readonly IConfiguration _configuration;
        private readonly IServiceProvider _serviceProvider;
        private readonly IWebHostEnvironment _env;
        private readonly ICommonService _commonService;
        public SyncUserStatusWorker(IServiceProvider serviceProvider, IWebHostEnvironment env)
        {
            CronExpression = Cron.Daily();

            _logger = serviceProvider.GetService<ILogger<SyncUserStatusWorker>>();
            _configuration = serviceProvider.GetService<IConfiguration>();
            _serviceProvider = serviceProvider;
            _env = env;
            _commonService = _serviceProvider.GetService<ICommonService>();
        }

        public override async Task DoWorkAsync(CancellationToken cancellationToken = default)
        {
            //逻辑处理
            await DoWork();
        }

        async Task DoWork()
        {
            var jobLogService = LazyServiceProvider.LazyGetService<IScheduleJobLogService>();
            var jobLog = jobLogService.InitSyncLog("SynaUserStatus");

            try
            {
                var users = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetListAsync(a => !string.IsNullOrEmpty(EF.Property<string>(a, EntityConsts.IdentityUser.StaffCode)));
                var affectCount = await SyncUserStatus(users);
                jobLog.RecordCount = affectCount;
            }
            catch (Exception ex)
            {
                jobLog.IsSuccess = false;
                jobLog.Remark = ex.ToString();
                _logger.LogError("An erroe occured when sync user status, message:" + ex.Message);
            }
            finally
            {
                jobLogService.SyncLog(jobLog);
            }
        }

        private async Task<int> SyncUserStatus(List<IdentityUser> users)
        {
            var getTokenResult = await GetToken(_configuration);
            if (getTokenResult.Success)
            {
                var resource = _configuration["Integrations:Graph:Resource"];
                var identityUserRepository = LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>();
                var inActiveUsers = new List<IdentityUser>();
                var updatePPUsers = new List<StaffDto>();
                var dimissionPPUsers = new List<StaffDto>();
                var ppUsers = GetPPUsers(users);

                var stackPPUsers = new Stack<StaffDto>(ppUsers);

                do
                {
                    var listRequests = new List<GraphBatchRequestDto.Request>();
                    //批量提交每次最大20条
                    for (int i = 0; i < 20; i++)
                    {
                        var result = stackPPUsers.TryPop(out StaffDto user);
                        if (!result)
                            break;

                        listRequests.Add(new GraphBatchRequestDto.Request
                        {
                            Id = user.Id.ToString(),
                            Method = HttpMethods.Get,
                            Url = $"/users/{user.Email}?$select=accountEnabled,displayName"
                        });
                    }

                    //批量执行request
                    var log = new SetOperationLogRequestDto();
                    var url = $"{resource}/v1.0/$batch";
                    log = _commonService.InitOperationLog("Microsoft Graph", "用户信息/状态查询", url + "|" + getTokenResult.Data);
                    string responseData = string.Empty;
                    try
                    {
                        var request = new GraphBatchRequestDto { Requests = listRequests };
                        var response = await url.WithHeader("Authorization", $"Bearer {getTokenResult.Data}").PostAsync(JsonContent.Create(request));
                        responseData = await response.GetStringAsync();
                        _commonService.LogResponse(log, responseData);
                    }
                    catch (Exception ex)
                    {
                        _commonService.LogResponse(log, ex.ToString(), false);
                        continue;
                    }

                    var jsonSerializerOptions = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };

                    var batchResponse = JsonSerializer.Deserialize<GraphBatchResponseDto>(responseData, jsonSerializerOptions);
                    //对批量返回的结果进行匹配
                    foreach (var item in batchResponse.Responses)
                    {
                        var user = users.FirstOrDefault(a => a.Id == Guid.Parse(item.Id));
                        var ppUser = ppUsers.FirstOrDefault(a => a.Id == Guid.Parse(item.Id));
                        if (user == null || ppUser == null)
                            continue;

                        //结果为404
                        if (item.Status == (int)HttpStatusCode.NotFound)
                        {
                            //将pp user在职状态设置为离职
                            if (ppUser.JobStatus == Enums.DataverseEnums.Staff.JobStatus.Incumbency)
                            {
                                ppUser.JobStatus = Enums.DataverseEnums.Staff.JobStatus.Dimission;
                                updatePPUsers.Add(ppUser);
                                dimissionPPUsers.Add(ppUser);
                            }
                        }
                        else if (item.Status == (int)HttpStatusCode.OK)
                        {
                            var successBody = JsonSerializer.Deserialize<GraphBatchResponseDto.SuccessBody>(item.Body.ToString(), jsonSerializerOptions);
                            if (successBody != null)
                            {
                                var jobStatus = successBody.AccountEnabled ? Enums.DataverseEnums.Staff.JobStatus.Incumbency : Enums.DataverseEnums.Staff.JobStatus.Dimission;

                                if (ppUser.JobStatus != jobStatus || ppUser.Name != successBody.DisplayName)
                                {
                                    ppUser.JobStatus = jobStatus;
                                    ppUser.Name = successBody.DisplayName;
                                    updatePPUsers.Add(ppUser);
                                }

                                inActiveUsers.AddIf(!successBody.AccountEnabled, user);
                            }
                        }
                    }

                } while (stackPPUsers.Count > 0);

                //if (updateEntities.Any())
                //{
                //    await identityUserRepository.UpdateManyAsync(updateEntities);
                //}

                //修改PP员工主数据
                SavePPUsers(updatePPUsers);

                //3. 邮件通知：被锁定的用户，有进行的任务，审批，单据，则需要邮件通知到Line Manager以及运维人员
                SendLockUserEmail(dimissionPPUsers, inActiveUsers);

                return updatePPUsers.Count;
            }

            return -1;
        }

        private void SendLockUserEmail(List<StaffDto> dimissionPPUsers, List<IdentityUser> inActiveUsers)
        {
            if (dimissionPPUsers?.Any() != true || inActiveUsers?.Any() != true)
            {
                return;
            }
            try
            {
                //取出锁定用户中，有未完成任务的用户及这些用户要发送邮件的EPOLeader+HelpdeskEmail
                var dicUserToEmails = GetLockUserToEmails(dimissionPPUsers, inActiveUsers);
                if (dicUserToEmails?.Any() != true)
                {
                    return;
                }

                //取出锁定用户中，未完成任务的数量
                var (initCount, approvalCount) = GetLockUserApprovalCount(dicUserToEmails.Keys.ToList());

                var fromEmail = _configuration["SpeakerEmail:FromEmail"];
                var fromName = _configuration["SpeakerEmail:FromName"];
                //string path = AppDomain.CurrentDomain.BaseDirectory;
                //string html = File.ReadAllText($"{path}/Templates/Email/LockUserNotification.html");
                var provider = _env.WebRootFileProvider;
                string html = provider.GetFileInfo("Templates/Email/LockUserNotification.html").ReadAsString();
                var htmlTable = @"<table>
                    <tr>
                        <td colspan='4'>{Title}</td>
                    </tr>
                    <tr>
                        <td>用户名</td>
                        <td>任务状态</td>
                        <td>业务类型</td>
                        <td>数量</td>
                    </tr>
                    {CountList}
                </table>";
                var htmlTableInit = htmlTable.Replace("{Title}", "提交的单据");
                var htmlTableApproval = htmlTable.Replace("{Title}", "审批的单据");
                foreach (var item in dicUserToEmails)
                {
                    var htmlTables = "";
                    var curHtmlInit = htmlTableInit;
                    var curHtmlApproval = htmlTableApproval;
                    var curUser = inActiveUsers.FirstOrDefault(a => a.Id == item.Key);
                    var curInitCount = initCount.Where(a => a.RelatedUserId == item.Key).ToList();
                    if (curInitCount.Any())
                    {
                        var countList = curInitCount.Select(a => $@"<tr><td>{curUser?.UserName}</td>
                        <td>{a.ShowType}</td>
                        <td>{a.BusinessType}</td>
                        <td>{a.Count}</td></tr>")
                        .JoinAsString("");
                        htmlTables += curHtmlInit.Replace("{CountList}", countList);
                    }
                    var curApprovalCount = approvalCount.Where(a => a.RelatedUserId == item.Key).ToList();
                    if (curApprovalCount.Any())
                    {
                        var countList = curApprovalCount.Select(a => $@"<tr><td>{curUser?.UserName}</td>
                        <td>{a.ShowType}</td>
                        <td>{a.BusinessType}</td>
                        <td>{a.Count}</td></tr>")
                        .JoinAsString("");
                        htmlTables += curHtmlApproval.Replace("{CountList}", countList);
                    }

                    //如果没有Count,不发邮件
                    if (!curInitCount.Any() && !curApprovalCount.Any())
                    {
                        continue;
                    }

                    var curEmailSubject = $"锁定用户通知({curUser.UserName}, {curUser.Name})";
                    var curHtml = html.Replace("{UserNameInfo}", $"({curUser.UserName}, {curUser.Name})");
                    curHtml = curHtml.Replace("{HtmlTables}", htmlTables);

                    //开始发送邮件
                    var emailService = _serviceProvider.GetService<IEmailService>();
                    //异步！！
                    emailService.SendEmailAsync(new SendEmailDto
                    {
                        Subject = curEmailSubject,
                        Body = curHtml,
                        FromEmail = fromEmail,
                        FromName = fromName,
                        IsBodyHtml = true,
                        Tos = item.Value,
                        Ccs = [],
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"SendLockUserEmailAsync() Exception : {ex.Message}");
            }
        }

        private (List<ApprovalCountByUserDto>, List<ApprovalCountByUserDto>) GetLockUserApprovalCount(List<Guid> userIds)
        {
            var approveService = _serviceProvider.GetService<IApproveService>();
            var initCount = approveService.InitiatedApprovalCountABPAsync(userIds)
                .GetAwaiterResult().Data
                //要排除“Completedd”的任务Count
                .Where(a => a.ShowType != "Completed").ToList();
            var approvalCount = approveService.GetApprovalCountABPAsync(userIds)
                .GetAwaiterResult().Data;
            return (initCount, approvalCount);
        }

        /// <summary>
        /// Key:IdentifierUser.ID, Values:List<PP员工.Emais>
        /// </summary>
        private Dictionary<Guid, List<string>> GetLockUserToEmails(List<StaffDto> dimissionPPUsers, List<IdentityUser> inActiveUsers)
        {
            var dataverseService = _serviceProvider.GetService<IDataverseService>();
            var department = dataverseService.GetOrganizations().GetAwaiterResult();
            var relations = dataverseService.GetStaffDepartmentRelations().GetAwaiterResult();

            //Key:StaffDto.ID, Values:List<PP员工.Ids>
            var dicEpoLeaderIds = new Dictionary<Guid, List<Guid>>();
            foreach (var item in dimissionPPUsers)
            {
                var epoLeaderIds = relations.Where(a => a.UserId == item.Id).Join(department, a => a.DepartmentId, b => b.Id, (a, b) => b)
                    .Select(a => a.EpoLeaderId).Where(a => a.HasValue)
                    .Select(a => a.Value).Distinct().ToList();
                dicEpoLeaderIds.Add(item.Id, epoLeaderIds);
            }

            var result = new Dictionary<Guid, List<string>>();
            if (dicEpoLeaderIds?.Any() != true)
            {
                return result;
            }
            //查询所有epoLeaderIds 的Email
            var epoUserIds = dicEpoLeaderIds.SelectMany(a => a.Value);
            var epoUserList = dataverseService.GetStaffs().GetAwaiterResult()
                .Where(a => epoUserIds.Contains(a.Id)).ToList();

            //组装结果
            var helpdeskEmail = _configuration["SpeakerEmail:HelpdeskEmail"];
            foreach (var item in dicEpoLeaderIds)
            {
                var dimissionStaffCode = dimissionPPUsers.FirstOrDefault(a => a.Id == item.Key)?.StaffCode;
                var inActiveUserId = inActiveUsers.FirstOrDefault(a => string.Equals(a.GetProperty("StaffCode")?.ToString(), dimissionStaffCode, StringComparison.OrdinalIgnoreCase))?.Id;
                if (!inActiveUserId.HasValue)
                {
                    continue;
                }
                var toUserEmails = epoUserList.Where(a => item.Value.Contains(a.Id)).Select(a => a.Email).ToList();
                toUserEmails.AddIf(!string.IsNullOrWhiteSpace(helpdeskEmail), helpdeskEmail);
                result.Add(inActiveUserId.Value, toUserEmails);
            }
            return result;
        }

        private void SavePPUsers(List<StaffDto> updatePPUsers)
        {
            if (updatePPUsers.Any())
            {
                var updateEntities = new EntityCollection();
                var entities = updatePPUsers.Select(a => new Entity
                {
                    LogicalName = "spk_staffmasterdata",
                    Id = a.Id,
                    Attributes = new AttributeCollection()
                    {
                        { "spk_staffstate", new OptionSetValue((int)a.JobStatus) },
                        { "spk_name", a.Name }
                    }
                });

                updateEntities.Entities.AddRange(entities);

                var approveService = _serviceProvider.GetService<ApproveService>();
                var ppSaveResult = approveService.TransactionRequest(null, updateEntities, null);
                _logger.LogInformation($"SavePPUsers() Save to PP Result:{ppSaveResult}");
            }
        }

        private List<StaffDto> GetPPUsers(List<IdentityUser> users)
        {
            var staffIds = users.Select(a => a.Id).ToArray();
            var ppStaff = _serviceProvider.GetService<IDataverseService>().GetStaffs().GetAwaiterResult();
            return ppStaff.Where(a => staffIds.Contains(a.Id)).ToList();
        }

        private async Task<MessageResult> GetToken(IConfiguration configuration)
        {
            try
            {
                var clientId = _configuration["Integrations:Graph:ClientId"];
                var tenantId = _configuration["Integrations:Graph:TenantId"];
                var certName = _configuration["Integrations:Graph:CertName"];
                var certPassword = _configuration["Integrations:Graph:CertPassword"];
                var authority = _configuration["Integrations:Graph:Authority"];
                var resource = _configuration["Integrations:Graph:Resource"];

                var cert = new X509Certificate2(AppDomain.CurrentDomain.BaseDirectory + certName, certPassword);
                authority = $"{authority}/{tenantId}";
                var app = ConfidentialClientApplicationBuilder.Create(clientId)
                                                            .WithCertificate(cert)
                                                            .WithAuthority(authority)
                                                            .Build();

                var scopes = new string[] { $"{resource}/.default" };
                var result = await app.AcquireTokenForClient(scopes).ExecuteAsync();
                return MessageResult.SuccessResult(result.AccessToken);
            }
            catch (Exception ex)
            {
                return MessageResult.FailureResult(ex.ToString());
            }
        }
    }
}