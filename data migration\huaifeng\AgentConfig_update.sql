SELECT
 TRY_CONVERT(UNIQUEIDENTIFIER, [Id]) [Id]
,CASE [WorkflowType]
	WHEN 'SupplierApplication' THEN 1
	WHEN 'ProcurementApplication' THEN 2
	WHEN 'PurchaseOrder' THEN 3
	WHEN 'WaiverApplication' THEN 4
	WHEN 'JustificationApplication' THEN 5
	WHEN 'BiddingApplication' THEN 6
	WHEN 'GoodsReceiving' THEN 7
	WHEN 'PaymentApplication' THEN 8
	WHEN 'SpeakerAuth' THEN 9
	WHEN '' THEN 10
	END [WorkflowType]
,TRY_CONVERT(UNIQUEIDENTIFIER, [OriginalOperator]) [OriginalOperator]
,TRY_CONVERT(UNIQUEIDENTIFIER, [AgentOperator]) [AgentOperator]
,[StartDate]
,[EndDate]
,TRY_CONVERT(UNIQUEIDENTIFIER, [Creator]) [Creator]
,[Remark]
,[Status]
,[IsNotifyOriginalOperator]
,[ExtraProperties]
,'NULL' [ConcurrencyStamp]
,[CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CreatorId]) [CreatorId]
,[LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [LastModifierId]) [LastModifierId]
,[IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, [DeleterId]) [DeleterId]
,[DeletionTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [DataverseId]) [DataverseId]
INTO #AgentConfig
FROM (SELECT *,ROW_NUMBER() OVER(PARTITION BY Id ORDER BY Id) RK FROM PLATFORM_ABBOTT_STG.dbo.AgentConfig)a
WHERE RK = 1
;
--drop table #AgentConfig

USE Speaker_Portal_STG;

UPDATE a 
SET 
 a.[Id] = b.[Id]
,a.[WorkflowType] = b.[WorkflowType]
,a.[OriginalOperator] = b.[OriginalOperator]
,a.[AgentOperator] = b.[AgentOperator]
,a.[StartDate] = b.[StartDate]
,a.[EndDate] = b.[EndDate]
,a.[Creator] = b.[Creator]
,a.[Remark] = b.[Remark]
,a.[Status] = b.[Status]
,a.[IsNotifyOriginalOperator] = b.[IsNotifyOriginalOperator]
,a.[ExtraProperties] = b.[ExtraProperties]
,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = b.[CreatorId]
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = b.[LastModifierId]
,a.[IsDeleted] = b.[IsDeleted]
,a.[DeleterId] = b.[DeleterId]
,a.[DeletionTime] = b.[DeletionTime]
,a.[DataverseId] = b.[DataverseId]
FROM dbo.AgentConfig a
left join #AgentConfig  b
ON a.id=b.id;


INSERT INTO dbo.AgentConfig
(
 [Id]
,[WorkflowType]
,[OriginalOperator]
,[AgentOperator]
,[StartDate]
,[EndDate]
,[Creator]
,[Remark]
,[Status]
,[IsNotifyOriginalOperator]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[DataverseId]
)
SELECT
 [Id]
,[WorkflowType]
,[OriginalOperator]
,[AgentOperator]
,[StartDate]
,[EndDate]
,[Creator]
,[Remark]
,[Status]
,[IsNotifyOriginalOperator]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[DataverseId]
FROM #AgentConfig a
WHERE not exists (select * from dbo.AgentConfig where id=a.id);


--truncate table dbo.AgentConfig

