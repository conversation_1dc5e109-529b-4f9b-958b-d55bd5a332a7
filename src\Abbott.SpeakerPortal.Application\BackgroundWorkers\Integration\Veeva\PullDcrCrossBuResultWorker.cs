﻿using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Integration.Veeva;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.BackgroundWorkers.Integration.Veeva
{
    public class PullDcrCrossBuResultWorker : SpeakerPortalBackgroundWorkerBase
    {
        private IInteVeevaService _inteVeevaService;
        private IScheduleJobLogService _jobLogService;

        public PullDcrCrossBuResultWorker(IServiceProvider serviceProvider)
        {
            _inteVeevaService = serviceProvider.GetService<IInteVeevaService>();
            _jobLogService = serviceProvider.GetService<IScheduleJobLogService>();
            CronExpression = "0 */4 * * *";//每隔四小时执行一次

        }

        public override async Task DoWorkAsync(CancellationToken cancellationToken = default)
        {
            var log = _jobLogService.InitSyncLog("Veeva_PullDcrCrossBuResult");
            try
            {
                log.Remark = _inteVeevaService.PullDcrCrossBuResult();
            }
            catch (Exception ex)
            {
                log.IsSuccess = false;
                log.Remark = ex.ToString();
            }
            finally
            {
                _jobLogService.SyncLog(log);
            }
        }
    }
}
