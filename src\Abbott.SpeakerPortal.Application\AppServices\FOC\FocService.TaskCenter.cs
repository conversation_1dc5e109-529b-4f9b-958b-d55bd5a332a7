﻿using Abbott.SpeakerPortal.Contracts.Agent;
using Abbott.SpeakerPortal.Contracts.FOC;
using Abbott.SpeakerPortal.Contracts.STicket;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Entities.Common.Workflow.WorkflowTasks;
using Abbott.SpeakerPortal.Entities.EFlow.FOC;
using Abbott.SpeakerPortal.Entities.EFlow.STicket;
using Abbott.SpeakerPortal.Enums;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Abbott.SpeakerPortal.Extension;
using Senparc.Weixin.WxOpen.AdvancedAPIs.WxApp.WxAppJson;
using Abbott.SpeakerPortal.Contracts.Dataverse;
using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Office2010.Excel;
using Microsoft.AspNetCore.Mvc.ApplicationModels;

namespace Abbott.SpeakerPortal.AppServices.FOC
{
    public partial class FocService : SpeakerPortalAppService, IFocService
    {
        /// <summary>
        /// 获取我发起的任务
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetFOCAppliedByMeResponseDto>> GetAppliedByMeAsync(GetFOCAppliedByMeRequestDto request)
        {
            FOCStatus[] pendingProcessings = [FOCStatus.RejectedBack];
            FOCStatus[] progressings = [FOCStatus.Approving, FOCStatus.Approved, FOCStatus.PendingShipment, FOCStatus.SOICanceling];
            FOCStatus[] completeds = [FOCStatus.Rejected, FOCStatus.Shipped, FOCStatus.ApplicantTerminate];
            var agents = await LazyServiceProvider.LazyGetService<IAgencyService>().GetOriginalOperators(new GetAgentOperatorsRequestDto { BusinessTypeCategory = ResignationTransfer.TaskFormCategory.FOCRequestApplication });
            var userIds = agents.Select(a => a.Key).Distinct().ToArray();
            var FOCQuery = (await LazyServiceProvider.LazyGetService<IFocApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            var sTicketDetaiQuery = (await LazyServiceProvider.LazyGetService<IFocApplicationDetailRepository>().GetQueryableAsync()).AsNoTracking();
            var sTicketPDQuery = (await LazyServiceProvider.LazyGetService<IFocApplicationProductDetailRepository>().GetQueryableAsync()).AsNoTracking();
            //核销数量
            var writeoffQuery = sTicketDetaiQuery.GroupJoin(sTicketPDQuery, a => a.Id, b => b.FOCDetailId, (a, b) => new { a, b }).SelectMany(a => a.b.DefaultIfEmpty(), (grp, b) => new { grp.a.ParentID, b.ShippedQty, b.WriteoffQty })
                 .GroupBy(g => g.ParentID, (key, group) => new { parentId = key, ShippedQtys = group.Sum(s => s.ShippedQty), WriteoffQtys = group.Sum(s => s.WriteoffQty) })
                 ;
            var query = FOCQuery.Where(a => userIds.Contains(a.ApplyUserId))
                        .WhereIf(request.ApplicantId.HasValue, m => m.ApplyUserId == request.ApplicantId)
                        .WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), a => a.ApplicationCode.Contains(request.ApplicationCode))
                        .WhereIf(request.StartDate.HasValue, a => a.ApplyTime >= request.StartDate)
                        .WhereIf(request.EndDate.HasValue, a => a.ApplyTime <= request.EndDate.Value.Date.AddDays(1))
                        .WhereIf(!string.IsNullOrEmpty(request.ApplyDeptName), a => a.ApplyUserDeptName.Contains(request.ApplyDeptName))
                        .WhereIf(!string.IsNullOrEmpty(request.SubBudgetCode), a => a.SubBudgetCode == request.SubBudgetCode)
                        .WhereIf(request.CostCenterId.HasValue, a => a.CostCenterID == request.CostCenterId)
                        .WhereIf(!string.IsNullOrEmpty(request.ProductMCode), a => a.ProductMCode.Contains(request.ProductMCode))
                        .WhereIf(request.Status.HasValue, a => a.Status == request.Status).GroupJoin(writeoffQuery, a => a.Id, b => b.parentId, (a, b) => new { s = a, b }).SelectMany(s => s.b.DefaultIfEmpty(), (a, b) => new GetFOCAppliedByMeResponseDto
                        {
                            Id = a.s.Id,
                            ApplicationCode = a.s.ApplicationCode,
                            ApplyUser = a.s.ApplyUser,
                            ApplyUserDeptName = a.s.ApplyUserDeptName,
                            ApplyUserId = a.s.ApplyUserId,
                            ApplyUserDeptId = a.s.ApplyUserDeptId,
                            CostCenter = a.s.CostCenter,
                            CostCenterCode = a.s.CostCenterCode,
                            BudgetCode = a.s.BudgetCode,
                            SubBudgetCode = a.s.SubBudgetCode,
                            ProductName = a.s.ProductName,
                            ProductMCode = a.s.ProductMCode,
                            ProductQty = a.s.ProductQty,
                            Status = a.s.Status,
                            ApplyTime = a.s.ApplyTime,
                            ShippedQty = b.ShippedQtys,
                            WriteoffQty = b.WriteoffQtys,
                        }); ;
            //.WhereIf(request.CompanyId.HasValue, a => a.CompanyId == request.CompanyId);
            switch (request.ProcessingStatus)
            {
                case ProcessingStatus.PendingProcessing:
                    query = query.Where(m => pendingProcessings.Contains(m.Status));
                    break;
                case ProcessingStatus.Progressing:
                    query = query.Where(m => progressings.Contains(m.Status));
                    break;
                case ProcessingStatus.Completed:
                    query = query.Where(m => completeds.Contains(m.Status));
                    break;
                default:
                    break;
            }
            var count = await query.CountAsync();
            var datas = await query.OrderByDescending(a => a.ApplyTime).PagingIf(request).ToListAsync();
            //var result = ObjectMapper.Map<List<FOCApplication>, List<GetFOCAppliedByMeResponseDto>>(datas);
            return new PagedResultDto<GetFOCAppliedByMeResponseDto>(count, datas);
        }

        /// <summary>
        /// 待我审批
        /// </summary>
        /// <param name="request"></param>
        /// <param name="processingStatus"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<GettFOCApprovedByMeResponseDto>> GetApprovedByMeAsync(GetFOCApprovedByMeRequestDto request)
        {
            var sTicketQuery = (await LazyServiceProvider.LazyGetService<IFocApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            var sTicketDetaiQuery = (await LazyServiceProvider.LazyGetService<IFocApplicationDetailRepository>().GetQueryableAsync()).AsNoTracking();
            var sTicketPDQuery = (await LazyServiceProvider.LazyGetService<IFocApplicationProductDetailRepository>().GetQueryableAsync()).AsNoTracking();
            //核销数量
            var writeoffQuery = sTicketDetaiQuery.GroupJoin(sTicketPDQuery, a => a.Id, b => b.FOCDetailId, (a, b) => new { a, b }).SelectMany(a => a.b.DefaultIfEmpty(), (grp, b) => new { grp.a.ParentID, b.ShippedQty, b.WriteoffQty })
                 .GroupBy(g => g.ParentID, (key, group) => new { parentId = key, ShippedQtys = group.Sum(s => s.ShippedQty), WriteoffQtys = group.Sum(s => s.WriteoffQty) })
                 ;
            //获取代理信息
            //var agents = await LazyServiceProvider.LazyGetService<IAgencyService>().GetOriginalOperators(new GetAgentOperatorsRequestDto { BusinessTypeCategory = ResignationTransfer.TaskFormCategory.STicketRequestApplication });
            //var userIds = agents.Select(a => a.Key).Distinct().ToArray();
            var query = sTicketQuery
                        .WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), a => a.ApplicationCode.Contains(request.ApplicationCode))
                        .WhereIf(request.ApplicantId.HasValue, m => m.ApplyUserId == request.ApplicantId)
                        .WhereIf(request.StartDate.HasValue, a => a.ApplyTime >= request.StartDate)
                        .WhereIf(request.EndDate.HasValue, a => a.ApplyTime <= request.EndDate.Value.Date.AddDays(1))
                        .WhereIf(!string.IsNullOrEmpty(request.ApplyDeptName), a => a.ApplyUserDeptName.Contains(request.ApplyDeptName))
                        .WhereIf(!string.IsNullOrEmpty(request.SubBudgetCode), a => a.SubBudgetCode == request.SubBudgetCode)
                        .WhereIf(request.CostCenterId.HasValue, a => a.CostCenterID == request.CostCenterId)
                        .WhereIf(!string.IsNullOrEmpty(request.ProductMCode), a => a.ProductMCode.Contains(request.ProductMCode))
                        .WhereIf(request.Status.HasValue, a => a.Status == request.Status)
                        .GroupJoin(writeoffQuery, a => a.Id, b => b.parentId, (a, b) => new { s = a, b }).SelectMany(s => s.b.DefaultIfEmpty(), (a, b) => new GettFOCApprovedByMeResponseDto
                        {
                            Id = a.s.Id,
                            ApplicationCode = a.s.ApplicationCode,
                            ApplyUser = a.s.ApplyUser,
                            ApplyUserDeptName = a.s.ApplyUserDeptName,
                            ApplyUserId = a.s.ApplyUserId,
                            ApplyUserDeptId = a.s.ApplyUserDeptId,
                            CostCenter = a.s.CostCenter,
                            CostCenterCode = a.s.CostCenterCode,
                            BudgetCode = a.s.BudgetCode,
                            SubBudgetCode = a.s.SubBudgetCode,
                            ProductName = a.s.ProductName,
                            ProductMCode = a.s.ProductMCode,
                            ProductQty = a.s.ProductQty,
                            Status = a.s.Status,
                            ApplyTime = a.s.ApplyTime,
                            ShippedQty = b.ShippedQtys,
                            WriteoffQty = b.WriteoffQtys,
                        });
            //.WhereIf(request.CompanyId.HasValue, a => a.CompanyId == request.CompanyId);
            if (request.ProcessingStatus == ProcessingStatus.PendingProcessing)
            {
                var taskRecords = await LazyServiceProvider.LazyGetService<IDataverseService>().GetApprovelTaskAsync(CurrentUser.Id.ToString(), [WorkflowTypeName.FOCRequest], request.ProcessingStatus);
                var formIds = taskRecords.Select(a1 => a1.FormId).ToArray();
                var datas = await query.Where(m => formIds.Contains(m.Id)).ToListAsync();
                var count = datas.Count;
                var result = datas.Join(taskRecords, a => a.Id, a => a.FormId, (a, b) => new GettFOCApprovedByMeResponseDto
                {
                    Id = a.Id,
                    ApplicationCode = a.ApplicationCode,
                    ApplyUser = a.ApplyUser,
                    ApplyUserDeptName = a.ApplyUserDeptName,
                    ApplyUserId = a.ApplyUserId,
                    ApplyUserDeptId = a.ApplyUserDeptId,
                    CostCenter = a.CostCenter,
                    CostCenterCode = a.CostCenterCode,
                    BudgetCode = a.BudgetCode,
                    SubBudgetCode = a.SubBudgetCode,
                    ProductName = a.ProductName,
                    ProductMCode = a.ProductMCode,
                    ProductQty = a.ProductQty,
                    Status = a.Status,
                    ApplyTime = a.ApplyTime,
                    ShippedQty = a.ShippedQty,
                    WriteoffQty = a.WriteoffQty,
                }).OrderByDescending(o => o.ApplyTime).PagingIf(request).ToArray();
                return new PagedResultDto<GettFOCApprovedByMeResponseDto>(count, result);
            }
            else
            {
                var queryWfTask = (await LazyServiceProvider.LazyGetService<IWorkflowTaskRepository>().GetQueryableAsync()).AsNoTracking().Where(a => a.ApprovalId == CurrentUser.Id && a.Status != ApprovalOperation.Start);
                var result = query.Join(queryWfTask, a => a.Id, a => a.FormId, (a, b) => new GettFOCApprovedByMeResponseDto
                {
                    Id = a.Id,
                    ApplicationCode = a.ApplicationCode,
                    ApplyUser = a.ApplyUser,
                    ApplyUserDeptName = a.ApplyUserDeptName,
                    ApplyUserId = a.ApplyUserId,
                    ApplyUserDeptId = a.ApplyUserDeptId,
                    CostCenter = a.CostCenter,
                    CostCenterCode = a.CostCenterCode,
                    BudgetCode = a.BudgetCode,
                    SubBudgetCode = a.SubBudgetCode,
                    ProductName = a.ProductName,
                    ProductMCode = a.ProductMCode,
                    ProductQty = a.ProductQty,
                    Status = a.Status,
                    ApplyTime = a.ApplyTime,
                    ApprovalTime = b.ApprovalTime,
                    ShippedQty = a.ShippedQty,
                    WriteoffQty = a.WriteoffQty,
                }).WhereIf(request.ApprovalStartDate.HasValue, a => a.ApprovalTime >= request.ApprovalStartDate)
                .WhereIf(request.ApprovalEndDate.HasValue, a => a.ApprovalTime <= request.ApprovalEndDate.Value.Date.AddDays(1)).OrderByDescending(o => o.ApprovalTime);
                var count = await result.CountAsync();
                var datas = await result.PagingIf(request).ToListAsync();
                return new PagedResultDto<GettFOCApprovedByMeResponseDto>(count, datas);
            }
        }
    }
}