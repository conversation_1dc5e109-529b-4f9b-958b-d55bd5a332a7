SELECT
 TRY_CONVERT(UNIQUEIDENTIFIER, [Id]) [Id]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([BudgetId],'00000000-0000-0000-0000-000000000000')) [BudgetId]
,iif(BudgetType is null,'0',BudgetType) AS [BudgetType]   --改为了为空时为0，不为空时取自己
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([OperatorId],'00000000-0000-0000-0000-000000000000')) [OperatorId]
,'NULL' AS [OperatorName]
,GETDATE() AS [OperatingTime]
,0 AS [OperateType]
,CAST([OperateAmount] AS decimal(32,8)) [OperateAmount]
,[OperateContent]
,[Remark]
,'{}' AS [ExtraProperties]
,'NULL' AS [ConcurrencyStamp]
,GETDATE() AS [CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CreatorId]) [CreatorId]
,GETDATE() AS [LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [LastModifierId]) [LastModifierId]
,0 AS [IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, [DeleterId]) [DeleterId]
,GETDATE() AS [DeletionTime]
,[TargetBudgetCode]
INTO #BdHistorys
FROM PLATFORM_ABBOTT_Stg.dbo.BdHistory

--drop table #BdHistorys;
--select * from PLATFORM_ABBOTT_Stg.dbo.BdHistory
--order by [OperateAmount] desc


USE Speaker_Portal_Stg;

UPDATE a
SET
 a.[Id] = b.[Id]
,a.[BudgetId] = b.[BudgetId]
,a.[BudgetType] = b.[BudgetType]
,a.[OperatorId] = b.[OperatorId]
,a.[OperatorName] = b.[OperatorName]
,a.[OperatingTime] = b.[OperatingTime]
,a.[OperateType] = b.[OperateType]
,a.[OperateAmount] = b.[OperateAmount]
,a.[OperateContent] = b.[OperateContent]
,a.[Remark] = b.[Remark]
,a.[ExtraProperties] = b.[ExtraProperties]
,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = b.[CreatorId]
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = b.[LastModifierId]
,a.[IsDeleted] = b.[IsDeleted]
,a.[DeleterId] = b.[DeleterId]
,a.[DeletionTime] = b.[DeletionTime]
,a.[TargetBudgetCode] = b.[TargetBudgetCode]
FROM dbo.BdHistorys a
left join #BdHistorys  b
ON a.id=b.id
WHERE b.[BudgetId] IS NOT NULL

--select* from #BdHistorys WHERE BudgetId IS NULL


INSERT INTO dbo.BdHistorys
(
 [Id]
,[BudgetId]
,[BudgetType]
,[OperatorId]
,[OperatorName]
,[OperatingTime]
,[OperateType]
,[OperateAmount]
,[OperateContent]
,[Remark]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[TargetBudgetCode]
)
SELECT
 [Id]
,[BudgetId]
,[BudgetType]
,[OperatorId]
,[OperatorName]
,[OperatingTime]
,[OperateType]
,[OperateAmount]
,[OperateContent]
,[Remark]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[TargetBudgetCode]
FROM #BdHistorys a
WHERE not exists (select * from dbo.BdHistorys where id=a.id)

--alter table dbo.BdHistorys alter column [OperatorName] [nvarchar](50) NOT NULL
--alter table dbo.BdHistorys alter column [OperateContent] [nvarchar](max) NULL
--alter table dbo.BdHistorys alter column [Remark] [nvarchar](500) NULL
--alter table dbo.BdHistorys alter column [TargetBudgetCode] [nvarchar](max) NULL
--alter table dbo.BdHistorys alter column  [OperateAmount] decimal(32,8) NULL