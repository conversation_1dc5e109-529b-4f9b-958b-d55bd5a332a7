﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Abbott.SpeakerPortal.Contracts.Budget.FocMasterBudget
{
    public class AdjustBudgetQtyRequestDto
    {
        public Guid Id { get; set; }
        /// <summary>
        /// 预算负责人
        /// </summary>
        [Required]
        public Guid OwnerId { get; set; }

        /// <summary>
        /// 调整数量
        /// </summary>
        [Required]
        public int AdjustQty { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        [Required]
        public string Description { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
    }
}
