SELECT
 TRY_CONVERT(UNIQ<PERSON>IDENTIFIER, [Id]) [Id]
,TRY_CONVERT(UNIQUEIDENTIFIER, [UserId]) [UserId]
,TRY_CONVERT(UNIQUEIDENTIFIER, [BuId]) [BuId]
,[BuName]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CreatorId]) [CreatorId]
,'1900-01-01' AS [LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, IIF(CAST([LastModifierId] AS nvarchar) = '' OR CAST([LastModifierId] AS nvarchar) IS NULL , '00000000-0000-0000-0000-000000000000', CAST([LastModifierId] AS nvarchar)))[LastModifierId]
,[IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, IIF(CAST([DeleterId] AS nvarchar) = '' OR CAST([DeleterId] AS nvarchar) IS NULL , '00000000-0000-0000-0000-000000000000', CAST([DeleterId] AS nvarchar)))[DeleterId]
,'1900-01-01' AS [DeletionTime]
INTO #BdAuthorizedBudgetBus
FROM PLATFORM_ABBOTT_STG.dbo.BdAuthorizedBudgetBus;

--DROP TABLE #BdAuthorizedBudgetBus;

USE Speaker_Portal_STG;

UPDATE a 
SET
 a.UserId = b.UserId
,a.BuId = b.BuId
,a.BuName = b.BuName
,a.ExtraProperties = b.ExtraProperties
,a.ConcurrencyStamp = b.ConcurrencyStamp
,a.CreationTime = b.CreationTime
,a.CreatorId = b.CreatorId
,a.LastModificationTime = b.LastModificationTime
,a.LastModifierId = b.LastModifierId
,a.IsDeleted = b.IsDeleted
,a.DeleterId = b.DeleterId
,a.DeletionTime = b.DeletionTime
FROM dbo.BdAuthorizedBudgetBus a
left join #BdAuthorizedBudgetBus  b
ON a.id=b.id;

INSERT INTO dbo.BdAuthorizedBudgetBus
SELECT
	Id
	,UserId
	,BuId
	,BuName
	,ExtraProperties
	,ConcurrencyStamp
	,CreationTime
	,CreatorId
	,LastModificationTime
	,LastModifierId
	,IsDeleted
	,DeleterId
	,DeletionTime
FROM #BdAuthorizedBudgetBus a
WHERE not exists (select * from dbo.BdAuthorizedBudgetBus where id=a.id)

--TRUNCATE TABLE dbo.BdAuthorizedBudgetBus;