﻿using Abbott.SpeakerPortal.Dtos;
using Abbott.SpeakerPortal.Enums;

using System;
using System.Collections.Generic;
using System.Text;

namespace Abbott.SpeakerPortal.Contracts.Agent
{
    public class GetAgencyHistoryListRequestDto : PagedDto
    {
        //public WorkflowTypeName? WorkflowType { get; set; }
        /// <summary>
        /// 业务类型Id，可空，空则表示“全部”
        /// </summary>
        //public Guid? BusinessTypeId { get; set; }
        /// <summary>
        /// 业务类型名称,可空，空则表示“全部”
        /// </summary>
        public ResignationTransfer.TaskFormCategory? BusinessType { get; set; }
        /// <summary>
        /// 业务类型名称
        /// </summary>
        //public string BusinessTypeName { get; set; }

        public Guid? AgentConfigId { get; set; }
    }
}
