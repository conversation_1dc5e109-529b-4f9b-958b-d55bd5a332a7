CREATE PROCEDURE dbo.sp_VendorOrgnizations_ns
AS 
Begin
	
	select 
a.Id,
VendorId,
VendorName,
VendorOldName,
VendorEngName,
RegCertificateAddress,
PostCode,
ContactName,
ContactPhone,
ContactEmail,
WebSite,
RegisterDate,
spk_code as OrgType,
IssuingAuthority,
RegisterCode,
RegValidityStart,
RegValidityEnd,
COALESCE(cast(d2.spk_provincialadministrativecode as varchar(10)),cast(d3.spk_provincialadministrativecode as varchar(10)),cast(d.spk_provincialadministrativecode as varchar(10)),cast(d1.spk_provincialadministrativecode as varchar(10)),REPLICATE('0', 6)) as Province,
COALESCE(cast(c.spk_cityadministrativedivisioncode as varchar(10)),cast(c1.spk_cityadministrativedivisioncode as varchar(10)),REPLICATE('0', 6)) as City,
Legal,
RegisterAmount,
BusinessAuthority,
BusinessScope,
LastYearSales,
KeyIndustry,
KeyClient,
Staffs,
Aptitudes,
ApplyReason,
ExtraProperties,
ConcurrencyStamp,
CONVERT(DATETIME, LEFT(a.CreationTime, 8) + ' ' + SUBSTRING(a.CreationTime, 9, 2) + ':' + SUBSTRING(a.CreationTime, 11, 2) + ':' + SUBSTRING(a.CreationTime, 13, 2), 120) CreationTime,
g.spk_NexBPMCode as CreatorId,
case when len(a.LastModificationTime)=14 then CONVERT(DATETIME, LEFT(a.LastModificationTime, 8) + ' ' + SUBSTRING(a.LastModificationTime, 9, 2) + ':' + SUBSTRING(a.LastModificationTime, 11, 2) + ':' + SUBSTRING(a.LastModificationTime, 13, 2), 120) 
else a.LastModificationTime end as LastModificationTime,
e1.spk_NexBPMCode as LastModifierId,
IsDeleted,
DeleterId,
DeletionTime,
Shareholder
into #VendorOrgnizations
from PLATFORM_ABBOTT.dbo.VendorOrgnizations_tmp a 
left join PLATFORM_ABBOTT.dbo.spk_dictionary b
on a.OrgType=b.spk_name
left join PLATFORM_ABBOTT.dbo.spk_city c
on SUBSTRING(a.City,1,3) =SUBSTRING(c.spk_name,1,3) or a.City=c.spk_name
left join PLATFORM_ABBOTT.dbo.spk_city c1
on SUBSTRING(a.City,1,2) =SUBSTRING(c1.spk_name,1,2) and c.spk_provincenamename is null  and SUBSTRING(c.spk_name,1,3)<>N'张家港'
left join PLATFORM_ABBOTT.dbo.spk_province d
on c.spk_provincenamename = d.spk_name 
left join PLATFORM_ABBOTT.dbo.spk_province d1
on c1.spk_provincenamename = d1.spk_name 
left join PLATFORM_ABBOTT.dbo.spk_province d2
on SUBSTRING(a.Province,1,3) =SUBSTRING(d2.spk_name,1,3) or a.Province=d2.spk_name
left join PLATFORM_ABBOTT.dbo.spk_province d3
on SUBSTRING(a.Province,1,2) =SUBSTRING(d3.spk_name,1,2) and d3.spk_provincecode is null
left join PLATFORM_ABBOTT.dbo.spk_staffmasterdata g
on a.CreatorId = g.bpm_id
left join PLATFORM_ABBOTT.dbo.spk_staffmasterdata e1
on a.LastModifierId = e1.bpm_id;

--删除表
 IF OBJECT_ID(N'PLATFORM_ABBOTT.dbo.VendorOrgnizations ', N'U') IS NOT NULL
 BEGIN
		drop table PLATFORM_ABBOTT.dbo.VendorOrgnizations
		select *
        into PLATFORM_ABBOTT.dbo.VendorOrgnizations from #VendorOrgnizations
 PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
 END
 ELSE
 BEGIN
--落成实体表
 select *
    into PLATFORM_ABBOTT.dbo.VendorOrgnizations from #VendorOrgnizations
 -- select * from #vendor_tbl
 PRINT(N'落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
 END

END;