apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: speaker-ingress
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "20m"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - speaker-mp-api.oneabbott.com
    secretName: tls-speaker-mp-api-secret
  rules:
  - host: speaker-mp-api.oneabbott.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: corp-ca191-speakerapi-p
            port:
              number: 80