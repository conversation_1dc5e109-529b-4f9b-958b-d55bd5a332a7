CREATE PROCEDURE dbo.sp_PurPAApplications
AS 
BEGIN
	--初始化xml
IF OBJECT_ID(N'PLATFORM_ABBOTT.dbo.XML_6', N'U') IS NOT NULL
BEGIN
	PRINT(N'已经初始化过'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select * into PLATFORM_ABBOTT.dbo.XML_6 
	from (
		SELECT 
    ProcInstId,
    STRING_AGG(up_Id, ', ') WITHIN GROUP (ORDER BY up_Id) AS up_Id
FROM 
    (select ProcInstId,
		up_Id
	from (
	select XmlContent,ProcInstId,
	up_Id.value('.', 'nvarchar(100)') as up_Id 
	from PLATFORM_ABBOTT.dbo.ODS_T_FORMINSTANCE_GLOBAL
		CROSS APPLY XmlContent.nodes('/root/AttachmentBlock/row/up_Id') AS XMLTable(up_Id)) B
) C
		GROUP BY  ProcInstId
) fg 
PRINT(N'初始化完成'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END;

--初始化xml
IF OBJECT_ID(N'PLATFORM_ABBOTT.dbo.XML_15', N'U') IS NOT NULL
BEGIN
	PRINT(N'已经初始化过'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select * into PLATFORM_ABBOTT.dbo.XML_15 
	from (
		SELECT 
    ProcInstId,
    STRING_AGG(up_Id, ', ') WITHIN GROUP (ORDER BY up_Id) AS up_Id
FROM 
    (select ProcInstId,
		up_Id
	from (
	select XmlContent,ProcInstId,
	up_Id.value('.', 'nvarchar(100)') as up_Id 
	from PLATFORM_ABBOTT.dbo.ODS_T_FORMINSTANCE_GLOBAL
		CROSS APPLY XmlContent.nodes('/root/AttachmentBlock1/row/up_Id') AS XMLTable(up_Id)) B
) C
		GROUP BY  ProcInstId
) fg 
PRINT(N'初始化完成'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END;

with payment_info as (
	select * from (
	select c.PAFormCode,c.RefNo,PaymentDate,
	ROW_NUMBER () over(PARTITION by d.RefNo  order by d.PaymentDate desc ) rn
	from PLATFORM_ABBOTT.dbo.ODS_T_AP_REF c  
	join PLATFORM_ABBOTT.dbo.ods_T_Ebanking_Payment_Info d
	on c.RefNo =d.RefNo 
	)b
	where rn=1
),
PR_info as (
select a.serialNumber,a.ProcInstId,max(PR_No) PR_No from  PLATFORM_ABBOTT.dbo.ODS_AUTO_BIZ_T_PaymentApplication_Info a
join PLATFORM_ABBOTT.dbo.ODS_AUTO_BIZ_T_PaymentApplication_Info_PR b
on a.serialNumber=b.serialNumber and a.ProcInstId=b.ProcInstId
group by a.serialNumber,a.ProcInstId
),
Historys_info as (
select  ProcInstID,Folio,Destination,FinishDate,Emp_Id from (
select ProcInstID,Folio,isnull(SUBSTRING(Destination,CHARINDEX(':', Destination)+1,LEN(Destination)) ,'') Destination, FinishDate,
ROW_NUMBER () over(PARTITION by ProcInstID,Folio order by FinishDate desc) rn1
from PLATFORM_ABBOTT.dbo.ods_T_PROCESS_Historys
where ActName =N'单据完成'
)a
left join PLATFORM_ABBOTT.dbo.ods_T_EMPLOYEE b
on isnull(SUBSTRING(Destination,CHARINDEX(':', Destination)+1,LEN(Destination)) ,'')=Emp_AD_Account 
where rn1=1
)
select 
newid() AS Id,--
a.ProcInstId,
a.serialNumber AS ApplicationCode,--
case when b.processStatus=N'填写表单' and b.payamount='0'  then N'填写申请表' 
	when b.processStatus=N'审批中' then N'审批中'
		when (b.processStatus=N'复审完毕' or b.processStatus=N'完成') and  c.Status='V' then N'打款作废'
		when b.processStatus=N'复审完毕' or b.processStatus=N'完成'  then  
		case when PaymentDate ='' then N'已打款'
			 when PaymentDate <>''then N'打款失败'
			 when h.amlinv<> null  then N'打款中'
			 when h.amlinv = null and  u.LHREAS='APV2L'  then N'打款作废'
			 else  N'待打款'
			 end 
	when b.processStatus=N'填写表单' and b.payamount<>'0'  then N'重新发起'
	when b.processStatus=N'发起人终止' or  b.processStatus=N'终止'  then N'作废'
	when b.processStatus=N'单据接收' then N'单据接收'
	when b.processStatus=N'单据已收，等待审核'  then N'财务初审'
	when b.processStatus=N'初审完毕'  then N'财务复审'
end  AS Status,--参考下方附录状态mapping表
a.applicantEmpId AS ApplyUserId,--以该ID匹配至员工主数据
a.applicantEmpName AS ApplyUserName,--
a.applicationDate AS ApplyTime,--
a.BUId AS ApplyUserBu,--以该ID匹配至对应的组织主数据
a.BUId AS ApplyUserBuName,--以该ID查询T_Resource.Res_Code，填入查询出的Res_Name
XmlContent.value('(/root/PaymentApplication_applicantInfoBlock_MainStore/applicantDept_Text)[1]', 'nvarchar(500)') AS ApplyUserBuToDeptName,--以ProcInstId找到对应的单据xml后，在PaymentApplication_applicantInfoBlock_MainStore内查询对应字段名后填入
XmlContent.value('(/root/PaymentApplication_hiddenBlock_MainStore/ERSUrl)[1]', 'nvarchar(500)')  AS EsignPdf,--以ProcInstId找到对应的单据xml后，在PaymentApplication_hiddenBlock_MainStore内查询对应字段名后填入
case when a.submitType=N'线上递交' then '1' else '2' end AS DeliveryMode,--对于ERSUrl有值的记录：线上递交-1；线下递交/空-2
--对于ERSUrl无值的记录：统一填写为2"
XmlContent.value('(/root/PaymentApplication_ERSBlock_MainStore/MeetingRemark)[1]', 'nvarchar(500)')  AS MeetingModifyRemark,--以ProcInstId找到对应的单据xml后，在PaymentApplication_ERSBlock_MainStore内查询对应字段名后填入
XmlContent.value('(/root/PaymentApplication_ERSBlock_MainStore/AmountRemark)[1]', 'nvarchar(500)') AS AmountModifyRemark,--以ProcInstId找到对应的单据xml后，在PaymentApplication_ERSBlock_MainStore内查询对应字段名后填入
a.GR_ProcInstId AS GRId,--以该ID查询AUTO_BIZ_T_GoodsReceiveApplication_Info.ProcinstId对应的GR单号(SerialNumber)，基于单号查询出对应PurGRApplications的Id
a.GR_ProcInstId AS GRApplicationCode,--以该ID查询AUTO_BIZ_T_GoodsReceiveApplication_Info.ProcinstId对应的GR单号(SerialNumber)
XmlContent.value('(/root/PaymentApplication_hiddenBlock_MainStore/POSerialNumber)[1]', 'nvarchar(500)')  AS POId,--基于该单号查询出对应PurPOApplications中的Id 
XmlContent.value('(/root/PaymentApplication_hiddenBlock_MainStore/POSerialNumber)[1]', 'nvarchar(500)')  AS POApplicationCode,--
a.companyCode AS CompanyCode,--以该值作为Res_Data查询T_Resource中Res_Parent_Code="61a3f911b5ae4bc98cddd441833d861e"的记录，基于匹配回的Res_Code匹配至公司主数据得到公司对应的BPCS代码
XmlContent.value('(/root/PaymentApplication_PaymentInfoBlock_MainStore/exchangeRate)[1]', 'nvarchar(500)') AS ExchangeRate,--以ProcInstId找到对应的单据xml后，在PaymentApplication_PaymentInfoBlock_MainStore内查询对应字段名后填入
case when a.UrgentPayment='true' then '1' else '0' end  AS UrgentPayment,--true-1；false/空-0
XmlContent.value('(/root/PaymentApplication_PaymentInfoBlock_MainStore/type_Value)[1]', 'nvarchar(500)') AS UrgentType,--以ProcInstId找到对应的单据xml后，在PaymentApplication_PaymentInfoBlock_MainStore内查询对应编码，再对应填入配置中的Code
'' AS Region,--留空(已不使用)
XmlContent.value('(/root/PaymentApplication_PaymentInfoBlock_MainStore/branchOffice_Value)[1]', 'nvarchar(500)') AS City,--以ProcInstId找到对应的单据xml后，在PaymentApplication_PaymentInfoBlock_MainStore内查询对应字段名后填入
case when XmlContent.value('(/root/PaymentApplication_hiddenBlock_MainStore/AdvancePayment)[1]', 'nvarchar(500)')='true' then '1' else '0' end  AS AdvancePayment,--以ProcInstId找到对应的单据xml后，在PaymentApplication_hiddenBlock_MainStore内查询对应字段名后填入，true-1；false-0
case when a.lastPayment='true' then '1' else '0' end AS IsLastPayment,--true-1；false/空-0
case when b.paymentForm_Text=N'电汇' then '1'
when b.paymentForm_Text=N'AP' then '2'
when b.paymentForm_Text=N'转账支票' then '3' end AS PaymentType,--电汇-1；AP-2；转账支票-3
b.receiveTitle AS ReceivingHeader,--
XmlContent.value('(/root/PaymentApplication_PaymentInfoBlock_MainStore/receiveTitleDate)[1]', 'nvarchar(500)') AS SupplierWarehousingTime,--以ProcInstId找到对应的单据xml后，在PaymentApplication_PaymentInfoBlock_MainStore内查询对应字段名后填入
case when XmlContent.value('(/root/PaymentApplication_PaymentInfoBlock_MainStore/afterRepairInvoice)[1]', 'nvarchar(500)')='true' then '1' else '0' end  AS IsBackupInvoice,--以ProcInstId找到对应的单据xml后，在PaymentApplication_PaymentInfoBlock_MainStore内查询对应字段名，true-1；false/空-0
XmlContent.value('(/root/PaymentApplication_PaymentInfoBlock_MainStore/remark)[1]', 'nvarchar(500)') AS Remarks,--以ProcInstId找到对应的单据xml后，在PaymentApplication_PaymentInfoBlock_MainStore内查询对应字段名后填入
x6.up_id AS Attachments,--支持文档，以ProcInstId查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到AttachmentBlock下所有的up_Id，填入匹配出的NexBPM支持文件ID，多个支持文件以英文逗号分隔
x7.up_id AS Invoice,--原报价支持文档，以ProcInstId查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到AttachmentBlock1下所有的up_Id，填入匹配出的NexBPM支持文件ID，多个支持文件以英文逗号分隔
'' AS SponsorshipRewardPoints,--原BPM无此功能
a.AkritivID AS AkritivCaseID,--
CASE 
        WHEN TRY_CONVERT(INT, LEFT(a.TermCode, 2)) <> NULL THEN LEFT(a.TermCode, 2)
        WHEN TRY_CONVERT(INT, LEFT(a.TermCode, 1)) = NULL THEN LEFT(a.TermCode, 1)
        ELSE '' -- 或者返回 NULL 或者任何你认为合适的默认值
    END  AS PaymentTerms,--取"_"前的代码填入，一般为两位数字(00/30/60/...)或单个字母(A)
XmlContent.value('(/root/PaymentApplication_FinancialInfoBlock_MainStore/InvoiceDescription)[1]', 'nvarchar(500)') AS InvoiceDescription,--以ProcInstId找到对应的单据xml后，在PaymentApplication_FinancialInfoBlock_MainStore内查询对应字段名后填入
a.ReceiveDate AS ReceivedDocumentsDate,--
'{}' AS ExtraProperties,--默认填写为"{}"
'' AS ConcurrencyStamp,--?
a.applicationDate AS CreationTime,--填充为ApplyTime即可
a.applicantEmpId AS CreatorId,--填充为ApplyUserId即可
'' AS LastModificationTime,--默认为空
'' AS LastModifierId,--默认为空
'0' AS IsDeleted,--默认为0
'' AS DeleterId,--默认为空
'' AS DeletionTime,--默认为空
concat (XmlContent.value('(/root/PaymentApplication_hiddenBlock_MainStore/VendorCode)[1]', 'nvarchar(500)'),XmlContent.value('(/root/PaymentApplication_hiddenBlock_MainStore/VendorName)[1]', 'nvarchar(500)'))
 AS VendorId,--以ProcInstId找到对应的单据xml后，在PaymentApplication_hiddenBlock_MainStore内查询这两个字段，结合该单对应的公司编码CompanyCode，去查询BPCSPMFVM对应的记录(VNDERX,VEXTNM,VMCMPY)，对于匹配成功的记录，再找到其对应的BPCSAVM.ID并填入(基于VMCMPY=VCMPNY AND VNDERX=VENDOR)
b.receiveTitle AS VendorName,--
a.companyCode AS CompanyId,--以该值作为Res_Data查询T_Resource中Res_Parent_Code="61a3f911b5ae4bc98cddd441833d861e"的记录，基于匹配回的Res_Code匹配至公司主数据得到公司ID
a.company AS CompanyName,--
a.payAmount AS PayTotalAmount,--
a.ReceiveDate AS AcceptedTime,--
XmlContent.value('(/root/approvalHistoryGrid/approvalPersonEmpId)[1]', 'nvarchar(500)') AS AccepterId,--单据接收人信息，以ProcInstId查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到approvalHistoryGrid下approvalLevel为"单据接收"且action为"同意"的记录，查询approvalTime最新的记录对应的用户ID，以该ID匹配至员工主数据
XmlContent.value('(/root/approvalHistoryGrid/approvalPerson)[1]', 'nvarchar(500)') AS AccepterName,--单据接收人信息，以ProcInstId查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到approvalHistoryGrid下approvalLevel为"单据接收"且action为"同意"的记录，查询approvalTime最新的记录对应的用户姓名
 ''AS TaskType,--
 'todo'AS EstimatedPaymentDate,--
'' AS SeperateRemark,--分单备注，原BPM无此功能
 ''AS TaxStampLegal,--
 ''AS ExpenseType,--
'' AS InterceptType,--拦截类型，该字段可能无需保留
f.PR_No AS PRId,--基于该PA对应的PR申请单，查询PurPRApplications的ID后填入
case when b.processStatus=N'复审完毕' or b.processStatus=N'完成' then g.FinishDate else null end AS ApprovedDate,--对于状态为"复审完毕"/"完成"的单据，查询Actname为"单据完成"的记录，取FinishDate最大的记录填入
a.GR_ProcInstId AS VendorCode,--以该信息查询AUTO_BIZ_T_GoodsReceiveApplication_Info.ProcinstID后，基于SupplierCode填入
case when b.processStatus=N'复审完毕' or b.processStatus=N'完成' then g.Emp_Id else null end AS ApprovedUserId,--对于状态为"复审完毕"/"完成"的单据，查询Actname为"单据完成"的记录，取FinishDate最大的记录对应的该字段，移除开头的"K2SQL:"后以得到的值查询T_EMPLOYEE.Emp_AD_Account，基于查询到的Emp_Id匹配至员工主数据
concat (XmlContent.value('(/root/PaymentApplication_PaymentInfoBlock_MainStore/branchOffice_Value)[1]', 'nvarchar(500)'),XmlContent.value('(/root/PaymentApplication_PaymentInfoBlock_MainStore/branchOffice_Text)[1]', 'nvarchar(500)')) AS CityId,--以ProcInstId找到对应的单据xml后，在PaymentApplication_PaymentInfoBlock_MainStore内查询对应字段后以该组合查询出对应的城市主数据
XmlContent.value('(/root/PaymentApplication_PaymentInfoBlock_MainStore/currency)[1]', 'nvarchar(500)') AS Currency,--以ProcInstId找到对应的单据xml后，在PaymentApplication_PaymentInfoBlock_MainStore内查询对应字段
'' AS CurrencySymbol,--基于currency直接填入标准的符号
'0' AS ExpectedFloatRate,--默认填写为0
XmlContent.value('(/root/PaymentApplication_PaymentInfoBlock_MainStore/exchangeRate)[1]', 'nvarchar(500)') AS PlanRate,--以ProcInstId找到对应的单据xml后，在PaymentApplication_PaymentInfoBlock_MainStore内查询对应字段名后填入(和上方的exchange rate相同)
'' AS EmailAttachment,--紧急付款邮件附件，原BPM无此功能
a.GR_ProcInstId AS PayMethod,--以该信息查询AUTO_BIZ_T_GoodsReceiveApplication_Info.ProcinstID后，基于PRItemType填入，若AR则为1；AP则为2
XmlContent.value('(/root/PaymentApplication_PaymentInfoBlock_MainStore/TaxChangeReason)[1]', 'nvarchar(500)') AS ReasonModification,--税率修改原因，以ProcInstId找到对应的单据xml后，在PaymentApplication_PaymentInfoBlock_MainStore内查询对应字段名后填入
a.VendorScore AS VendorRating,--供应商评分
XmlContent.value('(/root/PaymentApplication_DFInfoBlock_MainStore/Dfremark)[1]', 'nvarchar(500)') AS VendorRatingRemark
--供应商评分备注，以ProcInstId找到对应的单据xml后，在PaymentApplication_DFInfoBlock_MainStore内查询对应字段名后填入
into #PurPAApplications_tmp
from PLATFORM_ABBOTT.dbo.ods_AUTO_BIZ_T_PaymentApplication_Info a --618626
left join  PLATFORM_ABBOTT.dbo.ods_Form_e37632eb82f04fbda355cffdac744166 b
on a.ProcInstId=b.ProcInstId--618626
left join (select *,ROW_NUMBER () over(PARTITION by PAFormCode order by RefNo desc) rn from PLATFORM_ABBOTT.dbo.ODS_T_AP_REF) c
on a.serialNumber=c.PAFormCode and c.rn=1--618626 去重RefNo
left join payment_info d
on c.RefNo =d.RefNo --618626
join PLATFORM_ABBOTT.dbo.ods_T_FORMINSTANCE_GLOBAL e
on a.ProcInstId=e.ProcInstId
left join PR_info f
on a.serialNumber=f.serialNumber and a.ProcInstId=f.ProcInstId
left join Historys_info g
on a.ProcInstId=g.ProcInstId and a.serialNumber=g.Folio
left join PLATFORM_ABBOTT.dbo.xml_6 x6
on a.ProcInstId=x6.ProcInstId
left join PLATFORM_ABBOTT.dbo.xml_15 x7
on a.ProcInstId=x7.ProcInstId
left join  (select *,ROW_NUMBER () over(PARTITION by AMLINV order by AMLINV desc) rn  from PLATFORM_ABBOTT.dbo.ODS_TMP_AML) h
on  h.AMLINV = SUBSTRING(a.serialNumber,2,12) and h.rn ='1'
left join PLATFORM_ABBOTT.dbo.ODS_TMP_GLH u
on cast(u.LHIAN as nvarchar(100)) = b.serialNumber


 IF OBJECT_ID(N'PLATFORM_ABBOTT.dbo.PurPAApplications_tmp ', N'U') IS NOT NULL
	BEGIN
		update a 
		set a.ProcInstId              = b.ProcInstId
           ,a.ApplicationCode         = b.ApplicationCode
           ,a.Status                  = b.Status
           ,a.ApplyUserId             = b.ApplyUserId
           ,a.ApplyUserName           = b.ApplyUserName
           ,a.ApplyTime               = b.ApplyTime
           ,a.ApplyUserBu             = b.ApplyUserBu
           ,a.ApplyUserBuName         = b.ApplyUserBuName
           ,a.ApplyUserBuToDeptName   = b.ApplyUserBuToDeptName
           ,a.EsignPdf                = b.EsignPdf
           ,a.DeliveryMode            = b.DeliveryMode
           ,a.MeetingModifyRemark     = b.MeetingModifyRemark
           ,a.AmountModifyRemark      = b.AmountModifyRemark
           ,a.GRId                    = b.GRId
           ,a.GRApplicationCode       = b.GRApplicationCode
           ,a.POId                    = b.POId
           ,a.POApplicationCode       = b.POApplicationCode
           ,a.CompanyCode             = b.CompanyCode
           ,a.ExchangeRate            = b.ExchangeRate
           ,a.UrgentPayment           = b.UrgentPayment
           ,a.UrgentType              = b.UrgentType
           ,a.Region                  = b.Region
           ,a.City                    = b.City
           ,a.AdvancePayment          = b.AdvancePayment
           ,a.IsLastPayment           = b.IsLastPayment
           ,a.PaymentType             = b.PaymentType
           ,a.ReceivingHeader         = b.ReceivingHeader
           ,a.SupplierWarehousingTime = b.SupplierWarehousingTime
           ,a.IsBackupInvoice         = b.IsBackupInvoice
           ,a.Remarks                 = b.Remarks
           ,a.Attachments             = b.Attachments
           ,a.Invoice                 = b.Invoice
           ,a.SponsorshipRewardPoints = b.SponsorshipRewardPoints
           ,a.AkritivCaseID           = b.AkritivCaseID
           ,a.PaymentTerms            = b.PaymentTerms
           ,a.InvoiceDescription      = b.InvoiceDescription
           ,a.ReceivedDocumentsDate   = b.ReceivedDocumentsDate
           ,a.ExtraProperties         = b.ExtraProperties
           ,a.ConcurrencyStamp        = b.ConcurrencyStamp
           ,a.CreationTime            = b.CreationTime
           ,a.CreatorId               = b.CreatorId
           ,a.LastModificationTime    = b.LastModificationTime
           ,a.LastModifierId          = b.LastModifierId
           ,a.IsDeleted               = b.IsDeleted
           ,a.DeleterId               = b.DeleterId
           ,a.DeletionTime            = b.DeletionTime
           ,a.VendorId                = b.VendorId
           ,a.VendorName              = b.VendorName
           ,a.CompanyId               = b.CompanyId
           ,a.CompanyName             = b.CompanyName
           ,a.PayTotalAmount          = b.PayTotalAmount
           ,a.AcceptedTime            = b.AcceptedTime
           ,a.AccepterId              = b.AccepterId
           ,a.AccepterName            = b.AccepterName
           ,a.TaskType                = b.TaskType
           ,a.EstimatedPaymentDate    = b.EstimatedPaymentDate
           ,a.SeperateRemark          = b.SeperateRemark
           ,a.TaxStampLegal           = b.TaxStampLegal
           ,a.ExpenseType             = b.ExpenseType
           ,a.InterceptType           = b.InterceptType
           ,a.PRId                    = b.PRId
           ,a.ApprovedDate            = b.ApprovedDate
           ,a.VendorCode              = b.VendorCode
           ,a.ApprovedUserId          = b.ApprovedUserId
           ,a.CityId                  = b.CityId
           ,a.Currency                = b.Currency
           ,a.CurrencySymbol          = b.CurrencySymbol
           ,a.ExpectedFloatRate       = b.ExpectedFloatRate
           ,a.PlanRate                = b.PlanRate
           ,a.EmailAttachment         = b.EmailAttachment
           ,a.PayMethod               = b.PayMethod
           ,a.ReasonModification      = b.ReasonModification
           ,a.VendorRating            = b.VendorRating
           ,a.VendorRatingRemark      = b.VendorRatingRemark
      from PLATFORM_ABBOTT.dbo.PurPAApplications_tmp a 
      left join #PurPAApplications_tmp b on a.ProcInstId = b.ProcInstId and a.ApplicationCode = b.ApplicationCode
      
      insert into PLATFORM_ABBOTT.dbo.PurPAApplications_tmp 
      select  a.Id
             ,a.ProcInstId
             ,a.ApplicationCode
             ,a.Status
             ,a.ApplyUserId
             ,a.ApplyUserName
             ,a.ApplyTime
             ,a.ApplyUserBu
             ,a.ApplyUserBuName
             ,a.ApplyUserBuToDeptName
             ,a.EsignPdf
             ,a.DeliveryMode
             ,a.MeetingModifyRemark
             ,a.AmountModifyRemark
             ,a.GRId
             ,a.GRApplicationCode
             ,a.POId
             ,a.POApplicationCode
             ,a.CompanyCode
             ,a.ExchangeRate
             ,a.UrgentPayment
             ,a.UrgentType
             ,a.Region
             ,a.City
             ,a.AdvancePayment
             ,a.IsLastPayment
             ,a.PaymentType
             ,a.ReceivingHeader
             ,a.SupplierWarehousingTime
             ,a.IsBackupInvoice
             ,a.Remarks
             ,a.Attachments
             ,a.Invoice
             ,a.SponsorshipRewardPoints
             ,a.AkritivCaseID
             ,a.PaymentTerms
             ,a.InvoiceDescription
             ,a.ReceivedDocumentsDate
             ,a.ExtraProperties
             ,a.ConcurrencyStamp
             ,a.CreationTime
             ,a.CreatorId
             ,a.LastModificationTime
             ,a.LastModifierId
             ,a.IsDeleted
             ,a.DeleterId
             ,a.DeletionTime
             ,a.VendorId
             ,a.VendorName
             ,a.CompanyId
             ,a.CompanyName
             ,a.PayTotalAmount
             ,a.AcceptedTime
             ,a.AccepterId
             ,a.AccepterName
             ,a.TaskType
             ,a.EstimatedPaymentDate
             ,a.SeperateRemark
             ,a.TaxStampLegal
             ,a.ExpenseType
             ,a.InterceptType
             ,a.PRId
             ,a.ApprovedDate
             ,a.VendorCode
             ,a.ApprovedUserId
             ,a.CityId
             ,a.Currency
             ,a.CurrencySymbol
             ,a.ExpectedFloatRate
             ,a.PlanRate
             ,a.EmailAttachment
             ,a.PayMethod
             ,a.ReasonModification
             ,a.VendorRating
             ,a.VendorRatingRemark
        FROM #PurPAApplications_tmp A
        where not exists (select * from PLATFORM_ABBOTT.dbo.PurPAApplications_tmp where 
        ProcInstId = a.ProcInstId and ApplicationCode = a.ApplicationCode)
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into PLATFORM_ABBOTT.dbo.PurPAApplications_tmp from #PurPAApplications_tmp
    -- select * from #vendor_tbl
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
END;

