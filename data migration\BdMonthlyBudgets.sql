CREATE PROCEDURE  dbo.sp_BdMonthlyBudgets
AS 
BEGIN

WITH Months AS (
    SELECT 1 AS Month
    UNION ALL
    SELECT Month + 1
    FROM Months
    WHERE Month < 12
),

Data AS (
    SELECT 
        NEWID() AS Id,
        [Id] AS SubBudgetId,
        [BudgetAmount],
        1 AS Status, -- 设置默认值为1
        1 AS Month,
        [ExtraProperties],
        [ConcurrencyStamp],
        [CreationTime],
        [CreatorId],
        [LastModificationTime],
        [LastModifierId],
        0 AS [IsDeleted],  -- 设置默认值为0
        NULL AS [DeleterId],
        NULL AS [DeletionTime]
    FROM 
        PLATFORM_ABBOTT_Dev.dbo.BdSubBudgets
		--where Id = '0410927D-FF55-4DBF-8F1C-43946FEA7CC4'
)

SELECT 
    d.Id,
    d.SubBudgetId,
    CASE WHEN m.Month = 1 THEN d.BudgetAmount ELSE 0 END AS BudgetAmount,
    d.Status,
    m.Month AS Month,
    d.ExtraProperties,
    d.ConcurrencyStamp,
    d.CreationTime,
    d.CreatorId,
    d.<PERSON>,
    d.LastModifierId,
    d.<PERSON>,
    d.<PERSON>,
    d.DeletionTime
INTO #BdMonthlyBudgets
FROM 
    Months m
CROSS JOIN
    Data d
ORDER BY 
    m.Month
	
 IF OBJECT_ID(N'PLATFORM_ABBOTT_Dev.dbo.BdMonthlyBudgets ', N'U') IS NOT NULL
	BEGIN
		update a                
		set  a.SubBudgetId         = b.SubBudgetId
            ,a.BudgetAmount        = b.BudgetAmount
            ,a.Status              = b.Status
            ,a.Month               = b.Month
            ,a.ExtraProperties     = b.ExtraProperties
            ,a.ConcurrencyStamp    = b.ConcurrencyStamp
            ,a.CreationTime        = b.CreationTime
            ,a.CreatorId           = b.CreatorId
            ,a.LastModificationTime= b.LastModificationTime
            ,a.LastModifierId      = b.LastModifierId
            ,a.IsDeleted           = b.IsDeleted
            ,a.DeleterId           = b.DeleterId
            ,a.DeletionTime        = b.DeletionTime
        from PLATFORM_ABBOTT_Dev.dbo.BdMonthlyBudgets a
        left join #BdMonthlyBudgets b on a.SubBudgetId = b.SubBudgetId and a.Month = b.Month
        
        insert into PLATFORM_ABBOTT_Dev.dbo.BdMonthlyBudgets 
        select a.Id
              ,a.SubBudgetId
              ,a.BudgetAmount
              ,a.Status
              ,a.Month
              ,a.ExtraProperties
              ,a.ConcurrencyStamp
              ,a.CreationTime
              ,a.CreatorId
              ,a.LastModificationTime
              ,a.LastModifierId
              ,a.IsDeleted
              ,a.DeleterId
              ,a.DeletionTime
		from #BdMonthlyBudgets a
		where not exists (select * from PLATFORM_ABBOTT_Dev.dbo.BdMonthlyBudgets where SubBudgetId = a.SubBudgetId and Month = a.Month)
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into PLATFORM_ABBOTT_Dev.dbo.BdMonthlyBudgets from #BdMonthlyBudgets
    -- select * from #vendor_tbl
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
END	
;

