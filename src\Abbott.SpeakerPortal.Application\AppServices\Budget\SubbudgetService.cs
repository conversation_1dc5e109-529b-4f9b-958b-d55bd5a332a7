﻿using Abbott.SpeakerPortal.Contracts.Budget;
using Abbott.SpeakerPortal.Contracts.Budget.Subbudget;
using Abbott.SpeakerPortal.Contracts.Common.Attachment;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Dataverse.Contracts;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.Budget;
using Abbott.SpeakerPortal.Entities.Budget.BdExpense;
using Abbott.SpeakerPortal.Entities.Common.Attachment;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Enums;
using Azure.Identity;

using Flurl.Util;

using ClosedXML.Excel;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;

using MiniExcelLibs;
using MiniExcelLibs.Attributes;
using MiniExcelLibs.OpenXml;

using Senparc.CO2NET.Utilities;

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Net.NetworkInformation;
using System.Reflection.Emit;
using System.ServiceModel;
using System.Text;
using System.Threading.Tasks;

using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using DocumentFormat.OpenXml.Spreadsheet;
using static Abbott.SpeakerPortal.Permissions.SpeakerPortalPermissions;
using Abbott.SpeakerPortal.Vendor.Speaker;
using Abbott.SpeakerPortal.Consts;
using Volo.Abp.DistributedLocking;
using Abbott.SpeakerPortal.Utils;
using Abbott.SpeakerPortal.Entities.Purchase.PurPOApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Contracts.Dataverse;
using static Abbott.SpeakerPortal.Enums.Purchase;
using Abbott.SpeakerPortal.Entities.EFlow.STicket;

namespace Abbott.SpeakerPortal.AppServices.Budget
{
    public class SubbudgetService : SpeakerPortalAppService, ISubbudgetService
    {
        private async Task<Guid> GetSerialNoAsync<T>(IRepository<T, Guid> repository, T entity, string prefix = null) where T : FullAuditedEntity<Guid>
        {
            await using (var handle = await LazyServiceProvider.LazyGetService<IAbpDistributedLock>().TryAcquireAsync($"{DistributedLock.DistributedLock_GenerateSerialSubBudgetNo}", TimeSpan.FromSeconds(5)))
            {
                if (handle != null)
                {
                    int count;
                    var now = DateTimeOffset.Now.Date;
                    using (DataFilter.Disable<ISoftDelete>())
                    {
                        count = await repository.CountAsync(a => a.CreationTime.Date.Year == now.Year);
                    }
                    //todo:与BPM并行期间，采购申请单号规则调整5000开始（后续可能需要还原）
                    var no = $"{prefix}{now:yy}-{++count + 5000:D4}";
                    if (entity is IGenerateSerialNo)
                    {
                        ((IGenerateSerialNo)entity).SetSerialNo(no);
                        var budget = await repository.InsertAsync(entity, true);
                        return budget.Id;
                    }
                }
            }
            throw new TimeoutException("生成子预算编号超时，请重新操作");
        }

        /// <summary>
        /// 获取成本中心根据BU
        /// </summary>
        /// <param name="BuId">The bu identifier.</param>
        /// <returns></returns>
        /// <exception cref="System.NotImplementedException"></exception>
        public async Task<MessageResult> GetCostCenterByBuAsync(Guid BuId)
        {
            //var dataverseService = LazyServiceProvider.LazyGetService<IDataverseRepository>();
            var dataverse = LazyServiceProvider.LazyGetService<IDataverseService>();
            //var query = new QueryExpression("spk_organizational_costcenter");
            //query.ColumnSet.AddColumns("spk_costcenter", "spk_organizational");
            //query.Criteria.AddCondition(new ConditionExpression("spk_organizational", ConditionOperator.Equal, BuId));
            //query.Criteria.AddCondition(new ConditionExpression("statecode", ConditionOperator.Equal, 0));

            //var etts = await dataverseService.DataverseClient.RetrieveMultipleAsync(query);

            //var datas = etts.Entities.Select(x => new GetCostCenterResponseDto
            //{
            //    Id = x.GetAttributeValue<EntityReference>("spk_costcenter").Id,
            //    CostCenterName = x.GetAttributeValue<EntityReference>("spk_costcenter").Name
            //});
            var costcenterOrBu = await dataverse.GetCostCenterOrgRelationsAsync();

            var costcenter = await dataverse.GetCostcentersAsync();
            var datas = costcenterOrBu.Where(m => m.OrgId == BuId).Join(costcenter, a => a.CostcenterId, b => b.Id, (a, b) => new GetCostCenterResponseDto
            {
                CostCenterName = $"{b.Code}-{b.Name}",
                Id = b.Id
            });
            //var empty = costcenter.FirstOrDefault(s => s.Code == "0000");
            //if (empty != null && datas.Any())
            //    datas = datas.Prepend(new GetCostCenterResponseDto { CostCenterName = $"{empty.Code}-{empty.Name}", Id = empty.Id });
            return MessageResult.SuccessResult(datas);
        }

        /// <summary>
        /// 获取大区根据BU
        /// </summary>
        /// <param name="BuId">The bu identifier.</param>
        /// <returns></returns>
        /// <exception cref="System.NotImplementedException"></exception>
        public async Task<MessageResult> GetRegionByBuAsync(Guid BuId)
        {
            //var dataverseService = LazyServiceProvider.LazyGetService<IDataverseRepository>();

            //var query = new QueryExpression("spk_organizational_district");
            //query.ColumnSet.AddColumns("spk_district");
            //query.Criteria.AddCondition(new ConditionExpression("spk_organizational", ConditionOperator.Equal, BuId));

            //var etts = await dataverseService.DataverseClient.RetrieveMultipleAsync(query);
            var dataverse = LazyServiceProvider.LazyGetService<IDataverseService>();
            var district = await dataverse.GetOrgDistrictRelationsAsync();
            var datas = district.Where(x => x.OrgId == BuId).Select(x => new GetDistrictResponseDto
            {
                Id = x.DistrictId.Value,
                DistrictName = x.DistrictName
            });

            //var datas_ordered = datas.Where(w => w.DistrictName == "全国").Union(datas.Where(w => w.DistrictName != "全国"));
            var datas_ordered = datas.OrderByDescending(x => x.DistrictName == "全国");
            return MessageResult.SuccessResult(datas_ordered);
        }

        //预算负责人：BU下属部门下面的人员。使用PR的人员选择


        /// <summary>
        /// 新增子预算-单个
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> CreateSubbudgetAsync(CreateSubbudgetRequestDto request)
        {
            var subbudgetRespository = LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>();
            var queryMasterBudget = await LazyServiceProvider.LazyGetService<IBdMasterBudgetRepository>().GetQueryableAsync();

            //同一主预算下的所有子预算的总金额不能超过主预算的金额
            var masterBudgetData = queryMasterBudget.Include(x => x.SubBudgets).FirstOrDefault(x => x.Id == request.MasterBudgetId);
            if (masterBudgetData != null)
            {
                decimal subbudgetTotalAmount = request.BudgetAmount;
                if (masterBudgetData.SubBudgets?.Count > 0)
                    subbudgetTotalAmount += masterBudgetData.SubBudgets.Sum(x => x.BudgetAmount);
                if (masterBudgetData.BudgetAmount < subbudgetTotalAmount)
                    return MessageResult.FailureResult($"金额超出主预算金额！（主预算金额{masterBudgetData.BudgetAmount:F2}，其中可拨金额{(masterBudgetData.BudgetAmount - subbudgetTotalAmount):F2}）");
            }

            var subbudget = ObjectMapper.Map<CreateSubbudgetRequestDto, BdSubBudget>(request);
            //subbudget.BudgetAmount = request.MonthlyBudgets.Where(s => s.Status).Sum(s => s.BudgetAmount);
            subbudget.Status = true;
            if (request.AttachmentInformation?.Count > 0)
                subbudget.AttachmentFile = request.AttachmentInformation?.Select(x => x.AttachmentId.ToString()).JoinAsString(",");

            List<BdSubBudgetMonthChangeHistory> changeHistories = [];
            foreach (var item in subbudget.MonthlyBudgets)
            {
                BdSubBudgetMonthChangeHistory bdSubBudgetHis = new()
                {
                    Month = item.Month,
                    OperateAmount = item.BudgetAmount,
                    Status = item.Status,
                };
                bdSubBudgetHis.SetId(Guid.NewGuid());
                changeHistories.Add(bdSubBudgetHis);
            }
            var subbudgetId = await GenerateSubCode(subbudgetRespository, subbudget, masterBudgetData.Year.HasValue ? (masterBudgetData.Year.Value < 1000 ? DateTimeOffset.Now.Year : masterBudgetData.Year.Value) : DateTimeOffset.Now.Year);

            //History
            BdHistory history = new()
            {
                OperateType = OperateType.Create,
                OperateContent = $"预算金额：{request.BudgetAmount};预算负责人：{request.OwnerName}；描述：{subbudget.Description}",
                BudgetId = subbudgetId,
                OperatorId = CurrentUser.Id.Value,
                OperatorName = CurrentUser.Name,
                OperateAmount = request.BudgetAmount,
                OperatingTime = DateTime.Now,
                Remark = request.Remark,
                BudgetType = BudgetType.SubBudget,
                SubBudgetMonthChangeHistorys = changeHistories
            };

            await SaveHistory([history]);

            return MessageResult.SuccessResult(subbudgetId);
        }
        #region  生成自动编号
        /// <summary>
        /// 生成自动编号
        /// </summary>
        /// <returns></returns>
        private async Task<Guid> GenerateSubCode(IBdSubBudgetRepository subbudgetRespository, BdSubBudget dbSubBudget, int year)
        {
            await using (var handle = await LazyServiceProvider.LazyGetService<IAbpDistributedLock>().TryAcquireAsync($"{DistributedLock.DistributedLock_GenerateSerialBudgetNo}", TimeSpan.FromSeconds(5)))
            {
                if (handle != null)
                {
                    int count = 0;
                    var strYear = year.ToString();
                    var yearNo = $"CC{strYear.Substring(strYear.Length - 2)}";
                    using (DataFilter.Disable<ISoftDelete>())
                    {
                        var queryBudget = await subbudgetRespository.GetQueryableAsync();
                        var budgetNo = queryBudget.Where(a => a.Code.Contains(yearNo))
                            //.Select(a => int.Parse(a.Code.Substring(a.Code.IndexOf('-') + 1)))
                            .OrderByDescending(a => a.Code)
                            .FirstOrDefault();
                        if (budgetNo != null)
                        {
                            count = int.Parse(budgetNo.Code.Substring(budgetNo.Code.IndexOf('-') + 1));
                        }
                    }
                    //todo:与BPM并行期间，采购申请单号规则调整5000开始（后续可能需要还原）
                    var countNo = count < 5000 ? (++count + 5000) : (++count);
                    var no = $"{yearNo}-{countNo:D4}";
                    dbSubBudget.Code = no;
                    dbSubBudget.SetId(Guid.NewGuid());
                    var sBudget = await subbudgetRespository.InsertAsync(dbSubBudget, true);
                    return sBudget.Id;
                }
            }
            throw new TimeoutException("生成子预算编号超时，请重新操作");
        }

        /// <summary>
        /// 生成自动编号 批量
        /// </summary>
        /// <returns></returns>
        private async Task BatchSGenerateubCode(IBdSubBudgetRepository subbudgetRespository, List<BdSubBudget> dbSubBudgets)
        {
            await using (var handle = await LazyServiceProvider.LazyGetService<IAbpDistributedLock>().TryAcquireAsync($"{DistributedLock.DistributedLock_GenerateSerialBudgetNo}", TimeSpan.FromSeconds(5)))
            {
                if (handle != null)
                {
                    var queryMasterBudget = await LazyServiceProvider.LazyGetService<IBdMasterBudgetRepository>().GetQueryableAsync();
                    var masterBudgetIds = dbSubBudgets.Select(x => x.MasterBudgetId).Distinct().ToList();
                    var masterBudgets = queryMasterBudget.Where(a => masterBudgetIds.Contains(a.Id)).ToList();
                    foreach (var item in dbSubBudgets)
                    {
                        int count = 0;
                        var year = masterBudgets.FirstOrDefault(a => a.Id == item.MasterBudgetId)?.Year;
                        var strYear = (year.HasValue ? (year.Value < 1000 ? DateTimeOffset.Now.Year : year.Value) : DateTimeOffset.Now.Year).ToString();
                        var yearNo = $"CC{strYear.Substring(strYear.Length - 2)}";

                        using (DataFilter.Disable<ISoftDelete>())
                        {
                            var queryBudget = await subbudgetRespository.GetQueryableAsync();
                            var budgetNo = queryBudget.Where(a => a.Code.Contains(yearNo))
                                .OrderByDescending(a => a.Code)
                                .FirstOrDefault();
                            if (budgetNo != null)
                            {
                                count = int.Parse(budgetNo.Code.Substring(budgetNo.Code.IndexOf('-') + 1));
                            }
                        }
                        //todo:与BPM并行期间，采购申请单号规则调整5000开始（后续可能需要还原）
                        var countNo = count < 5000 ? (++count + 5000) : (++count);
                        var no = $"{yearNo}-{countNo:D4}";
                        item.Code = no;
                        await subbudgetRespository.InsertAsync(item, true);
                    }
                    return;
                }
            }
            throw new TimeoutException("生成子预算编号超时，请重新操作");
        }
        #endregion
        /// <summary>
        /// 获取子预算详情
        /// </summary>
        /// <param name="subbudgetId">The subbudget identifier.</param>
        /// <returns></returns>
        public async Task<MessageResult> GetSubbudgetDetailAsync(Guid subbudgetId)
        {
            var querySubbudget = await LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>().GetQueryableAsync();
            var attachmentQuery = await LazyServiceProvider.LazyGetService<IAttachmentRepository>().GetQueryableAsync();
            var userQuery = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();

            var subbudgetDt = querySubbudget.Include(i => i.MonthlyBudgets.OrderBy(o => o.Month)).Where(x => x.Id == subbudgetId)
                .GroupJoin(userQuery, a => a.OwnerId, b => b.Id, (a, b) => new { subbudget = a, users = b })
                .SelectMany(a => a.users.DefaultIfEmpty(), (a, b) => new { a.subbudget, user = b })
                .FirstOrDefault();
            if (subbudgetDt == null)
                return MessageResult.FailureResult("找不到数据");
            var subbudgetInfo = ObjectMapper.Map<BdSubBudget, GetSubbudgetDetailResponseDto>(subbudgetDt.subbudget);

            #region Guid显示名
            var costCenters = await dataverseService.GetCostcentersAsync(subbudgetDt.subbudget.CostCenterId.ToString());
            subbudgetInfo.CostCenterName = costCenters?.FirstOrDefault()?.Name;
            //大区
            var district = await dataverseService.GetDistrict(subbudgetDt.subbudget.RegionId.ToString());
            subbudgetInfo.RegionName = district?.FirstOrDefault().Name;
            //预算负责人
            subbudgetInfo.OwnerName = subbudgetDt.user?.Name;
            #endregion

            //附件
            if (!string.IsNullOrEmpty(subbudgetDt.subbudget.AttachmentFile))
            {
                var attachmentIds = subbudgetDt.subbudget.AttachmentFile.Split(',').Select(x => Guid.Parse(x)).ToList();
                if (attachmentIds.Count() > 0)
                {
                    var attachmentEntities = attachmentQuery.Where(x => attachmentIds.Contains(x.Id)).ToList();
                    var attachmentInfo = ObjectMapper.Map<List<Attachment>, List<UploadFileResponseDto>>(attachmentEntities);
                    subbudgetInfo.AttachmentInformation = attachmentInfo;
                }
            }

            return MessageResult.SuccessResult(subbudgetInfo);
        }

        /// <summary>
        /// 调整子预算
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        /// <exception cref="System.NotImplementedException"></exception>
        public async Task<MessageResult> AdjustingSubbudgetAsync(AdjustSubbudgetRequestDto request)
        {
            var subbudgetRespository = LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>();
            var queryMasterBudget = await LazyServiceProvider.LazyGetService<IBdMasterBudgetRepository>().GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();

            var subbudget = (await subbudgetRespository.GetQueryableAsync()).Include(s => s.MonthlyBudgets).FirstOrDefault(x => x.Id == request.Id);
            if (subbudget == null)
                return MessageResult.FailureResult("找不到相应的子预算数据");

            decimal adjustTotalAmount = request.AdjustMonthAmounts.Sum(s => s.AdjustAmount.Value);
            //调整金额为负数时，判断调整金额是否大于可用金额
            if (adjustTotalAmount < 0)
            {
                if (Math.Abs(adjustTotalAmount) > subbudget.BudgetAmount - subbudget.UesdAmount)
                    return MessageResult.FailureResult($"调整金额不能大于可用金额");
            }

            //同一主预算下的所有子预算的总金额不能超过主预算的金额
            var masterBudgetData = queryMasterBudget.Include(x => x.SubBudgets).FirstOrDefault(x => x.Id == subbudget.MasterBudgetId);
            if (masterBudgetData != null)
            {
                var subbudgetTotalAmount = masterBudgetData.SubBudgets.Sum(x => x.BudgetAmount) + adjustTotalAmount;
                if (masterBudgetData.BudgetAmount < subbudgetTotalAmount)
                    return MessageResult.FailureResult($"金额超出主预算金额！（主预算金额{masterBudgetData.BudgetAmount:F2}，其中可拨金额{(masterBudgetData.BudgetAmount - subbudgetTotalAmount):F2}）");
            }

            var orgs = await dataverseService.GetOrganizations(subbudget.BuId.ToString());
            if (orgs.Count > 0)
            {
                if (orgs.First().DepartmentName == "ADC")//BU为ADC时才能设置“IsComplicanceAudits”
                    subbudget.IsComplicanceAudits = request.IsComplicanceAudits;
            }

            subbudget.CostCenterId = request.CostCenterId;
            subbudget.RegionId = request.RegionId;
            subbudget.OwnerId = request.OwnerId;
            subbudget.Description = request.Description;
            subbudget.BudgetAmount += adjustTotalAmount;
            //subbudget.AvailableAmount += subbudgetTotalAmount;
            subbudget.Remark = request.Remark;
            if (request.AttachmentInformation?.Count < 1)
                subbudget.AttachmentFile = null;
            else
                subbudget.AttachmentFile = request.AttachmentInformation.Select(x => x.AttachmentId.ToString()).JoinAsString(",");
            //修改调正预算
            var MothlyBudget = subbudget.MonthlyBudgets.OrderBy(o => o.Month);
            string Decription = string.Empty;
            List<BdSubBudgetMonthChangeHistory> changeMonths = [];
            foreach (var item in MothlyBudget)
            {
                var adjustAmount = request.AdjustMonthAmounts.FirstOrDefault(s => s.Month == item.Month);
                if (adjustAmount == null) continue;
                BdSubBudgetMonthChangeHistory bdSubBudgetHis = new();
                bdSubBudgetHis.Month = item.Month;
                bdSubBudgetHis.SetId(Guid.NewGuid());
                bool isset = false;//是否写入历史
                if (adjustAmount.AdjustAmount.HasValue && adjustAmount.AdjustAmount != 0)
                {
                    bdSubBudgetHis.OperateAmount = adjustAmount.AdjustAmount;
                    item.BudgetAmount += adjustAmount.AdjustAmount.Value;
                    isset = true;
                }
                if (item.Status != adjustAmount.Status || adjustAmount.AdjustAmount != 0)
                {
                    string EnableText = item.Status ? "是" : "否";
                    Decription += $"修改金额：{adjustAmount.AdjustAmount},修改内容：月份{(int)adjustAmount.Month}月,是否启用：{EnableText}";
                }
                if (item.Status != adjustAmount.Status) { bdSubBudgetHis.Status = adjustAmount.Status; isset = true; item.Status = adjustAmount.Status; }
                if (isset) changeMonths.Add(bdSubBudgetHis);
            }

            await subbudgetRespository.UpdateAsync(subbudget);

            //TODO History
            BdHistory history = new()
            {
                OperateType = OperateType.Update,
                OperateContent = $"调整总金额：{adjustTotalAmount};{Decription};预算负责人：{request.OwnerName}；描述：{subbudget.Description}",
                BudgetId = subbudget.Id,
                OperatorId = CurrentUser.Id.Value,
                OperatorName = CurrentUser.Name,
                OperatingTime = DateTime.Now,
                Remark = request.Remark,
                BudgetType = BudgetType.SubBudget,
                OperateAmount = adjustTotalAmount,
                SubBudgetMonthChangeHistorys = changeMonths,
            };

            await SaveHistory([history]);

            return MessageResult.SuccessResult();
        }
        /// <summary>
        /// 调拨子预算
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> TransferSubBudgetAsync(TransferSubBudgetRequestDto request)
        {
            var subbudgetRespository = LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>();
            var monthlyBudget = LazyServiceProvider.LazyGetService<IBdMonthlyBudgetRepository>();

            var originalSub = await subbudgetRespository.FirstOrDefaultAsync(x => x.Id == request.OriginalId);
            var transferSub = await subbudgetRespository.FirstOrDefaultAsync(x => x.Id == request.TransferId);
            //原预算
            var originalSubMonth = await monthlyBudget.FirstOrDefaultAsync(x => x.SubBudgetId == request.OriginalId && x.Month == request.Month);
            var queryOriginalSubMonth = await monthlyBudget.GetQueryableAsync();

            //调拨预算
            var transferSubMonth = await monthlyBudget.FirstOrDefaultAsync(x => x.SubBudgetId == request.TransferId && x.Month == request.Month);
            //获取原预算可金额
            decimal availableAmount = queryOriginalSubMonth.Where(x => x.SubBudgetId == request.OriginalId && x.Status).Sum(s => s.BudgetAmount) - originalSub.UesdAmount;

            if (originalSubMonth.BudgetAmount < request.TransferAmount)
                return MessageResult.FailureResult("超出该月份的预算金额");
            if (availableAmount < request.TransferAmount)
                return MessageResult.FailureResult("调拨金额不能大于可调拨金额");
            if (request.TransferAmount <= 0)
                return MessageResult.FailureResult("调拨金额不能为负数和0");
            //子预算金额修改
            //originalSub.AvailableAmount -= request.TransferAmount;
            originalSub.BudgetAmount -= request.TransferAmount;
            //transferSub.AvailableAmount += request.TransferAmount;
            transferSub.BudgetAmount += request.TransferAmount;
            //月份预算修改
            originalSubMonth.BudgetAmount -= request.TransferAmount;
            transferSubMonth.BudgetAmount += request.TransferAmount;

            var updateBudget = new List<BdSubBudget> { originalSub, transferSub };

            ///历史记录 Todo
            //原预算
            var orgchhis = new BdSubBudgetMonthChangeHistory() { Month = request.Month, OperateAmount = -request.TransferAmount };
            orgchhis.SetId(Guid.NewGuid());
            BdHistory orgbdHistory = new()
            {
                OperateType = OperateType.Transfer,
                OperateContent = $"调出预算：{originalSub.Code};调入预算：{transferSub.Code};月份：{(int)request.Month}月",
                BudgetId = originalSub.Id,
                OperatorId = CurrentUser.Id.Value,
                OperatorName = CurrentUser.Name,
                OperatingTime = DateTime.Now,
                Remark = request.Remark,
                BudgetType = BudgetType.SubBudget,
                OperateAmount = -request.TransferAmount,
                TargetBudgetCode = transferSub.Code,
                SubBudgetMonthChangeHistorys = [orgchhis],
            };
            //调拨到
            var thchhis = new BdSubBudgetMonthChangeHistory() { Month = request.Month, OperateAmount = request.TransferAmount };
            thchhis.SetId(Guid.NewGuid());
            BdHistory tansferbdHistory = new()
            {
                OperateType = OperateType.Transfer,
                OperateContent = $"调出预算：{originalSub.Code};调入预算：{transferSub.Code};月份：{(int)request.Month}月",
                BudgetId = transferSub.Id,
                OperatorId = CurrentUser.Id.Value,
                OperatorName = CurrentUser.Name,
                OperatingTime = DateTime.Now,
                Remark = request.Remark,
                BudgetType = BudgetType.SubBudget,
                OperateAmount = request.TransferAmount,
                TargetBudgetCode = originalSub.Code,

                SubBudgetMonthChangeHistorys = [thchhis],
            };
            await subbudgetRespository.UpdateManyAsync(updateBudget);
            await monthlyBudget.UpdateManyAsync([originalSubMonth, transferSubMonth]);
            await SaveHistory([orgbdHistory, tansferbdHistory]);
            return MessageResult.SuccessResult("操作成功");
        }
        /// <summary>
        /// 新增历史记录
        /// </summary>
        /// <param name="bdHistorys"></param>
        /// <returns></returns>
        private async Task SaveHistory(List<BdHistory> bdHistorys)
        {
            var historyQueryRepository = LazyServiceProvider.LazyGetService<IBdHistoryRepository>();
            await historyQueryRepository.InsertManyAsync(bdHistorys);
        }
        /// <summary>
        /// 获取同主预算下的子预算信息
        /// </summary>
        /// <param name="BuId"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<SubBudgetResponseDto>> GetSubBudgetListAsync(TransferRequestDto transfer)
        {
            var querySubBudgetRepository = LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>();
            var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var querySubBudget = await querySubBudgetRepository.GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            ////成本中心
            var costCenters = await dataverseService.GetCostcentersAsync();
            ////大区
            var district = await dataverseService.GetDistrict();
            var subbudget = querySubBudget.First(f => f.Id == transfer.Id);
            var query = querySubBudget.Include(s => s.MonthlyBudgets.OrderBy(o => o.Month)).Where(b => b.BuId == transfer.BuId && b.MasterBudgetId ==
                 subbudget.MasterBudgetId && transfer.Id != b.Id)
                .WhereIf(!string.IsNullOrWhiteSpace(transfer.Code), m => m.Code.Contains(transfer.Code))
                .GroupJoin(queryableUser, a => a.OwnerId, b => b.Id, (a, b) => new { a, b })
                .SelectMany(a => a.b.DefaultIfEmpty(), (a, b) => new { a = a.a, b });

            //总数
            var count = await query.CountAsync();
            //分页
            query = query.OrderBy(s => s.a.CreationTime).Skip(transfer.PageIndex * transfer.PageSize).Take(transfer.PageSize);
            var datas = query.ToList().Select(s => new SubBudgetResponseDto
            {
                Id = s.a.Id,
                Code = s.a.Code,
                Description = s.a.Description,
                OwnerId = s.a.OwnerId,
                OwnerName = s.b.Name ?? "",
                CostCenterId = s.a.CostCenterId,
                CostCenterName = costCenters.FirstOrDefault(m => m.Id == s.a.CostCenterId).Name ?? "",
                RegionId = s.a.RegionId,
                RegionName = district.FirstOrDefault(m => m.Id == s.a.RegionId)?.Name ?? "",
                SubbudgetAmount = s.a.MonthlyBudgets.First().BudgetAmount,
                MonthBudgets = s.a.MonthlyBudgets.Select(s => new MonthBudgetReponseDto { Amount = s.BudgetAmount, Month = s.Month }).ToList(),

            }).ToArray();
            var result = new PagedResultDto<SubBudgetResponseDto>(count, datas);
            return result;
        }
        /// <summary>
        /// 验证上传的Excel
        /// </summary>
        /// <param name="dynamics"></param>
        /// <returns></returns>
        public async Task<MessageResult> VarifyImportTransferSubBudgetAsync(IEnumerable<TransferSubBudgetDto> Rows)
        {
            var budgetCodes = Rows.Select(a => a.OriginalCode).Union(Rows.Select(a => a.TransferCode));
            var querySubBudgetRepository = LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>();
            var querySubBudget = (await querySubBudgetRepository.GetQueryableAsync()).AsNoTracking();
            var subBudgets = querySubBudget.Include(s => s.MonthlyBudgets).Where(m => budgetCodes.Contains(m.Code)).ToDictionary(m => m.Code);
            IList<TransferImportMessageDto> errormsg = new List<TransferImportMessageDto>();
            IList<TransferImportMessageDto> successmsg = new List<TransferImportMessageDto>();
            //int i = 1;
            var rowNo = 6;
            foreach (var item in Rows)
            {
                //++i;
                rowNo++;
                var message = string.Empty;

                var (mes, isvarify) = item.VarifyInfom();
                TransferImportMessageDto transfer = new()
                {
                    //No = i,
                    No = rowNo,
                    OriginalCode = item.OriginalCode,
                    TransferCode = item.TransferCode,
                    TransferAmount = item.TransferAmount,
                    Remark = item.Remark,
                    OriginalMonth = item.OriginalMonth,
                    TransferMonth = item.TransferMonth,
                };
                if (isvarify) message += mes;
                else
                {
                    if (subBudgets.TryGetValue(item.OriginalCode, out BdSubBudget orBudget))
                    {
                        var monthBudget = orBudget.MonthlyBudgets.First(f => f.Month == item.OriginalMonth);
                        var availableAmount = orBudget.GetAvailableAmount();
                        if (monthBudget.BudgetAmount < item.TransferAmount) message += "调拨金额超出该月金额;";
                        else if (monthBudget.Status && availableAmount < item.TransferAmount) message += "调拨金额超出可用金额;";
                        else
                        {
                            transfer.AvailableAmount = availableAmount - item.TransferAmount.Value;
                            monthBudget.BudgetAmount -= item.TransferAmount.Value;
                        }
                    }
                    else message += "来源子预算不存在;";
                    if (!subBudgets.TryGetValue(item.TransferCode, out BdSubBudget tfBuget)) message += "目标子预算不不存在;";
                    if (orBudget != null && tfBuget != null)
                    {
                        if (orBudget.MasterBudgetId != tfBuget.MasterBudgetId) message += "不能跨主预算调拨;";
                        else if (tfBuget.Code == orBudget.Code) message += "预算不能自己调拨自己;";
                    }
                }
                if (!string.IsNullOrWhiteSpace(message))
                {
                    //transfer.No = errormsg.Count + 1;
                    transfer.Message = message;
                    errormsg.Add(transfer);
                }
                else
                {
                    //transfer.No = successmsg.Count + 1;
                    successmsg.Add(transfer);
                }
            }
            if (errormsg.Count > 0 || successmsg.Count == 0)
            {
                return MessageResult.SuccessResult(new ImportDataResponseDto<TransferImportMessageDto>(errormsg, false));
            }
            return MessageResult.SuccessResult(new ImportDataResponseDto<TransferImportMessageDto>(successmsg, true));
        }
        /// <summary>
        /// 批量调拨
        /// </summary>
        /// <param name="messageDtos"></param>
        /// <returns></returns>
        public async Task<MessageResult> ImportTransferSubBudgetAsync(List<TransferImportMessageDto> messageDtos)
        {
            var budgetCodes = messageDtos.Select(a => a.OriginalCode).Union(messageDtos.Select(a => a.TransferCode));
            var querySubBudgetRepository = LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>();
            var monthlyBudgetRepository = LazyServiceProvider.LazyGetService<IBdMonthlyBudgetRepository>();
            var querySubBudget = await querySubBudgetRepository.GetQueryableAsync();
            var subBudgets = querySubBudget.Include(s => s.MonthlyBudgets).Where(m => budgetCodes.Contains(m.Code)).ToArray();
            List<BdHistory> historys = new();
            var updatedMonthlyBudgets = new List<BdMonthlyBudget>();
            foreach (var item in messageDtos)
            {
                var orBudget = subBudgets.FirstOrDefault(m => m.Code == item.OriginalCode);
                var tfBudget = subBudgets.FirstOrDefault(m => m.Code == item.TransferCode);

                orBudget.BudgetAmount -= item.TransferAmount.Value;
                var orMonth = orBudget.MonthlyBudgets.First(f => f.Month == item.OriginalMonth);
                orMonth.BudgetAmount -= item.TransferAmount.Value;

                //调拨到
                tfBudget.BudgetAmount += item.TransferAmount.Value;
                var tfMonth = tfBudget.MonthlyBudgets.First(f => f.Month == item.TransferMonth);
                tfMonth.BudgetAmount += item.TransferAmount.Value;

                updatedMonthlyBudgets.AddRange([orMonth, tfMonth]);

                var content = $"调出预算：{orBudget.Code};调入预算：{tfBudget.Code};调出月份：{(int)item.OriginalMonth}月;调入月份：{(int)item.TransferMonth}月";

                var orgchhis = new BdSubBudgetMonthChangeHistory() { Month = item.OriginalMonth.Value, OperateAmount = -item.TransferAmount };
                orgchhis.SetId(Guid.NewGuid());

                BdHistory orgbdHistory = new()
                {
                    OperateType = OperateType.Transfer,
                    OperateContent = content,
                    BudgetId = orBudget.Id,
                    OperatorId = CurrentUser.Id.Value,
                    OperatorName = CurrentUser.Name,
                    OperatingTime = DateTime.Now,
                    Remark = item.Remark,
                    BudgetType = BudgetType.SubBudget,
                    OperateAmount = -item.TransferAmount,
                    TargetBudgetCode = tfBudget.Code,
                    SubBudgetMonthChangeHistorys = [orgchhis],
                };

                //调拨到
                var thchhis = new BdSubBudgetMonthChangeHistory() { Month = item.TransferMonth.Value, OperateAmount = item.TransferAmount };
                thchhis.SetId(Guid.NewGuid());
                BdHistory tansferbdHistory = new()
                {
                    OperateType = OperateType.Transfer,
                    OperateContent = content,
                    BudgetId = tfBudget.Id,
                    OperatorId = CurrentUser.Id.Value,
                    OperatorName = CurrentUser.Name,
                    OperatingTime = DateTime.Now,
                    Remark = item.Remark,
                    BudgetType = BudgetType.SubBudget,
                    OperateAmount = item.TransferAmount,
                    TargetBudgetCode = orBudget.Code,
                    SubBudgetMonthChangeHistorys = [thchhis],
                };
                historys.AddRange([orgbdHistory, tansferbdHistory]);
            }
            await querySubBudgetRepository.UpdateManyAsync(subBudgets);
            await monthlyBudgetRepository.UpdateManyAsync(updatedMonthlyBudgets);
            await SaveHistory(historys);
            return MessageResult.SuccessResult("批量调拨成功");
        }
        /// <summary>
        /// 批量调整子预算
        /// </summary>
        /// <param name="Rows"></param>
        /// <returns></returns>
        public async Task<MessageResult> VarifyImportAdjustmentSubBudgetAsync(IEnumerable<AdjustmentBudgetDto> Rows)
        {
            var budgetCodes = Rows.Select(a => a.Code);
            var querySubBudgetRepository = LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>();
            var querySubBudget = (await querySubBudgetRepository.GetQueryableAsync()).AsNoTracking();
            var subBudgets = querySubBudget.Include(s => s.MonthlyBudgets).Where(m => budgetCodes.Contains(m.Code));
            var subBudgetDic = subBudgets.ToDictionary(m => m.Code);
            var masterids = subBudgets.Select(s => s.MasterBudgetId).ToList();
            //主预算
            var queryMasterBudgetRepository = LazyServiceProvider.LazyGetService<IBdMasterBudgetRepository>();
            var queryMasterSubBudget = (await queryMasterBudgetRepository.GetQueryableAsync()).AsNoTracking();
            //查找主预算金额与主预算金额下的所有子预算金额之和
            var mastrBudget = await queryMasterSubBudget.Include(x => x.SubBudgets).Where(m => masterids.Contains(m.Id)).Select(m => new AdjustmentCompareDto
            {
                Id = m.Id,
                MasterAmount = m.BudgetAmount,
                SubAmountSum = m.SubBudgets.Sum(s => s.BudgetAmount)
            }).Distinct().ToDictionaryAsync(d => d.Id);
            int i = 1;
            //是否验证成功
            var IsVarify = true;
            List<AdjustmentBudgetDto> adjustments = new();
            var rowNo = 9;
            foreach (var row in Rows)
            {
                rowNo++;
                //row.No = i;
                row.No = rowNo;
                var message = string.Empty;
                if (string.IsNullOrWhiteSpace(row.Code)) message += $"子预算编号不能为空;";
                var (isSuccess, mes) = row.VerificationAdjustment();
                if (isSuccess) message += mes;
                else if (subBudgetDic.TryGetValue(row.Code, out var subBudget))
                {
                    var modifyMonths = row.MonthlyBudgets.Where(m => m.Status.HasValue || (m.BudgetAmount.HasValue && m.BudgetAmount != 0));
                    if (modifyMonths.Count() == 0) { message += "请配置金额或者是否开启；"; }
                    var totalAdjustment = row.MonthlyBudgets.Sum(s => s.BudgetAmount);
                    foreach (var m in modifyMonths)
                    {
                        var monthly = subBudget.MonthlyBudgets.First(s => s.Month == m.Month);
                        if (m.Status.HasValue)
                        {
                            monthly.Status = m.Status.Value;
                        }
                        if (m.BudgetAmount.HasValue)
                        {
                            monthly.BudgetAmount += m.BudgetAmount.Value;
                            if (monthly.BudgetAmount < 0) message += $"调整后{(int)m.Month}月预算小于0;";
                        }
                    }
                    var availableAmount = subBudget.GetAvailableAmount();
                    if (availableAmount < 0) message += $"调整后可用金额小于0;";
                    var master = mastrBudget[subBudget.MasterBudgetId];
                    master.SubAmountSum += totalAdjustment.Value;
                    if (!master.IsAdjusted)
                        message += $"调整后可用金额大于主预算;";

                }
                else message += "子预算编号不存在";

                row.Message = message;
                if (!string.IsNullOrWhiteSpace(message))
                {
                    //row.No = adjustments.Count + 1;
                    adjustments.Add(row);
                    IsVarify = false;
                }
                i++;
            }

            return MessageResult.SuccessResult(new ImportDataResponseDto<AdjustmentBudgetDto>(IsVarify ? [.. Rows] : adjustments, IsVarify));
        }
        /// <summary>
        /// 批量提交调整子预算
        /// </summary>
        /// <param name="adjustments"></param>
        /// <returns></returns>
        public async Task<MessageResult> SubmitImportAdjustmentSubBudgetAsync(List<AdjustmentBudgetDto> adjustments)
        {
            var budgetCodes = adjustments.Select(a => a.Code);
            var querySubBudgetRepository = LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>();
            var querySubBudget = await querySubBudgetRepository.GetQueryableAsync();
            var subBudgets = querySubBudget.Include(s => s.MonthlyBudgets).Where(m => budgetCodes.Contains(m.Code)).ToList();
            var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var ownerIds = subBudgets.Select(m => m.OwnerId).ToList();
            var users = queryableUser.Where(m => ownerIds.Contains(m.Id));
            List<BdHistory> his = [];
            foreach (var item in adjustments)
            {
                var Budget = subBudgets.First(m => m.Code == item.Code);
                var ownerName = users.FirstOrDefault(m => m.Id == Budget.OwnerId).Name ?? "";
                var modifyMonths = item.MonthlyBudgets;
                //调整总金额
                var adjustmentTotal = modifyMonths.Sum(s => s.BudgetAmount);
                var Decription = string.Empty;
                List<BdSubBudgetMonthChangeHistory> changeMonths = [];
                foreach (var m in modifyMonths)
                {
                    if (!m.Status.HasValue && (!m.BudgetAmount.HasValue || m.BudgetAmount == 0))
                        continue;
                    //调整的月份
                    BdSubBudgetMonthChangeHistory bdSubBudget = new() { Month = m.Month };
                    bdSubBudget.SetId(Guid.NewGuid());
                    var adjustBd = Budget.MonthlyBudgets.First(f => f.Month == m.Month);
                    Decription += $"修改月份：{(int)m.Month}月；";
                    if (m.BudgetAmount.HasValue && m.BudgetAmount != 0)
                    {
                        adjustBd.BudgetAmount += m.BudgetAmount.Value;
                        Decription += $"修改金额：{m.BudgetAmount};";
                        bdSubBudget.OperateAmount = m.BudgetAmount;
                    }
                    if (m.Status.HasValue)
                    {
                        adjustBd.Status = m.Status.Value;
                        bdSubBudget.Status = m.Status;
                        Decription += $"修改内容:是否启用：{(m.Status.Value ? "是" : "否")}；";
                    }
                    if (m.BudgetAmount.HasValue || m.Status.HasValue) changeMonths.Add(bdSubBudget);
                }
                Budget.BudgetAmount += adjustmentTotal.Value;
                his.Add(new()
                {
                    OperateType = OperateType.Update,
                    OperateContent = $"调整总金额：{adjustmentTotal};预算负责人：{ownerName};描述:{item.Remark},{Decription}",
                    BudgetId = Budget.Id,
                    OperatorId = CurrentUser.Id.Value,
                    OperatorName = CurrentUser.Name,
                    OperatingTime = DateTime.Now,
                    OperateAmount = adjustmentTotal,
                    Remark = item.Remark,
                    BudgetType = BudgetType.SubBudget,
                    SubBudgetMonthChangeHistorys = changeMonths
                });
            }

            await querySubBudgetRepository.UpdateManyAsync(subBudgets);
            await SaveHistory(his);
            return MessageResult.SuccessResult("操作成功");
        }
        /// <summary>
        /// 批量新增
        /// </summary>
        /// <param name="Rows"></param>
        /// <returns></returns>
        public async Task<MessageResult> VarifyBatchCreateSubBudgetAsync(IEnumerable<CreatesSubBudgetDto> Rows)
        {
            var budgetCodes = Rows.Select(a => a.MasterBudgetCode);
            //主预算
            var queryMasterRepository = LazyServiceProvider.LazyGetService<IBdMasterBudgetRepository>();
            var queryMasterBudget = (await queryMasterRepository.GetQueryableAsync()).AsNoTracking();
            var mtBudget = queryMasterBudget.Where(m => budgetCodes.Contains(m.Code) && m.Status);
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            ////成本中心
            var costCenters = await dataverseService.GetCostcentersAsync();
            ////大区
            var district = await dataverseService.GetDistrict();
            //大区BU关系
            var districtBu = await dataverseService.GetOrgDistrictRelationsAsync();
            var districtBuDict = districtBu.ToDictionary(a => $"{a.OrgId}*{a.DistrictId}", a => a.Id);
            //成本中心BU关系
            var costCentersBu = await dataverseService.GetCostCenterOrgRelationsAsync();
            var costCentersBuDict = costCentersBu.ToDictionary(a => $"{a.OrgId}*{a.CostcenterId}", a => a.Id);
            var subBudgetDic = await mtBudget.ToDictionaryAsync(m => m.Code);
            //查找主预算金额与主预算金额下的所有子预算金额之和
            var mastrBudget = await mtBudget.Include(m => m.SubBudgets).Select(m => new CreateCompareDto
            {
                Id = m.Id,
                Code = m.Code,
                MasterAmount = m.BudgetAmount,
                BuId = m.BuId,
                SubAmountSum = m.SubBudgets.Sum(s => s.BudgetAmount)
            }).Distinct().ToDictionaryAsync(d => d.Code);
            List<CreateImportMessageDto> success = [];
            List<CreateImportMessageDto> error = [];
            string[] status = ["是", "否"];
            var rowNo = 9;
            foreach (var row in Rows)
            {
                rowNo++;
                string message = string.Empty;
                if (string.IsNullOrEmpty(row.MasterBudgetCode)) { message += "请填写主预算编号;"; }
                if (string.IsNullOrEmpty(row.CostCenter)) { message += "请填写成本中心;"; }
                if (string.IsNullOrEmpty(row.Region)) { message += "请填写大区;"; }
                //if (string.IsNullOrEmpty(row.StatusText)) { message += "请选择是否启用;"; }
                if (string.IsNullOrEmpty(row.OwnerEmail)) { message += "请填写预算负责人邮箱;"; }
                if (string.IsNullOrEmpty(row.Description)) { message += "请填写描述;"; }

                var costCenterData = costCenters.FirstOrDefault(f => f.CcenterCode == row.CostCenter);
                if (costCenterData == null) { message += "成本中心不存在;"; }
                var regionData = district.FirstOrDefault(f => f.DistrictCode == row.Region);
                if (regionData == null) { message += "大区不存在;"; }
                var user = queryableUser.FirstOrDefault(u => u.Email == row.OwnerEmail);
                if (user == null) { message += "预算负责人不存在;"; }
                if (row.Remark.Length > 300) message += "备注不能超过300字;";
                if (row.Description.Length > 200) message += "描述不能超过200字;";
                //if (!status.Contains(row.StatusText)) message += "是否启用请选择是或否;";

                var (amountMes, mes) = row.VerificationCreate();
                if (!mastrBudget.TryGetValue(row.MasterBudgetCode, out var msBudget)) { message += "主预算不存在或者已被禁用;"; }
                else if (!districtBuDict.ContainsKey($"{msBudget.BuId}*{regionData?.Id}")) message += "大区非该BU;";
                else if (!costCentersBuDict.ContainsKey($"{msBudget.BuId}*{costCenterData?.Id}")) message += "成本中心非该BU;";
                else if (amountMes) message += mes;
                else
                {
                    //判断主预算是否大于子预算
                    msBudget.SubAmountSum += row.TotalAmount;
                    if (!msBudget.IsAdjusted) message += "子预算金额大于主预算金额;";
                }
                var messageDto = ObjectMapper.Map<CreatesSubBudgetDto, CreateImportMessageDto>(row);
                messageDto.No = rowNo;
                if (string.IsNullOrEmpty(message))
                {
                    messageDto.CostCenterId = costCenterData.Id;
                    messageDto.CostCenter = costCenterData.Name;
                    messageDto.Region = regionData.Name;
                    messageDto.RegionId = regionData.Id;
                    messageDto.OwnerId = user.Id;
                    messageDto.OwnerName = user.Name;
                    //messageDto.Status = (row.StatusText == "是");
                    //messageDto.No = success.Count + 1;
                    messageDto.MasterBudgetId = msBudget.Id;
                    success.Add(messageDto);
                }
                else
                {
                    messageDto.Message = $"{message}";
                    //messageDto.No = error.Count + 1;
                    error.Add(messageDto);
                };
            }
            if (error.Count > 0)
                return MessageResult.SuccessResult(new ImportDataResponseDto<CreateImportMessageDto>(error, false));
            return MessageResult.SuccessResult(new ImportDataResponseDto<CreateImportMessageDto>(success, true));
        }
        async Task<IEnumerable<UploadFileResponseDto>> GetAttachmentInfo(string attachmentIds)
        {
            if (string.IsNullOrEmpty(attachmentIds))
                return null;
            var idArray = attachmentIds.Split(',').Select(x => Guid.Parse(x)).ToList();
            if (idArray == null || idArray.Count < 1)
                return null;
            var attachmentQuery = await LazyServiceProvider.LazyGetService<IAttachmentRepository>().GetQueryableAsync();
            var attachmentEntities = attachmentQuery.Where(x => idArray.Contains(x.Id)).ToList();
            var attachmentInfo = ObjectMapper.Map<List<Attachment>, List<UploadFileResponseDto>>(attachmentEntities);
            return attachmentInfo;

        }

        /// <summary>
        /// 获取子预算管理列表
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetSubbudgetListResponseDto>> GetSubbudgetManageListAsync(GetSubbudgetListRequestDto request, bool isPagin = true)
        {
            var querySubbudget = await LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>().GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();

            var query = querySubbudget.Include(x => x.MasterBudget).Include(x => x.MonthlyBudgets).Where(x => x.BuId == request.BuId)
                .WhereIf(!string.IsNullOrEmpty(request.MasterBudgetCode), x => x.MasterBudget.Code == request.MasterBudgetCode)
                .WhereIf(request.MasterBudgetId.HasValue, x => x.MasterBudgetId == request.MasterBudgetId)
                .WhereIf(request.CostCenterId.HasValue, x => x.CostCenterId == request.CostCenterId)
                .WhereIf(request.RegionId.HasValue, x => x.RegionId == request.RegionId)
                .WhereIf(!string.IsNullOrEmpty(request.SubbudgetCode), x => x.Code.Contains(request.SubbudgetCode))
                .WhereIf(request.Year.HasValue, x => x.MasterBudget.Year == request.Year)
                .WhereIf(!string.IsNullOrEmpty(request.Description), x => x.Description.Contains(request.Description))
                .WhereIf(request.MasterBudgetOwnerId.HasValue, x => x.MasterBudget.OwnerId == request.MasterBudgetOwnerId)
                .WhereIf(request.SubbudgetOwnerId.HasValue, x => x.OwnerId == request.SubbudgetOwnerId)
                .OrderByDescending(o => o.CreationTime).AsQueryable();
            var count = await query.CountAsync();

            var users = await dataverseService.GetStaffs(stateCode: null);
            var orgs = await dataverseService.GetOrganizations(stateCode: null);
            var coscenters = await dataverseService.GetCostcentersAsync(stateCode: null);
            var regions = await dataverseService.GetDistrict(stateCode: null);

            if (isPagin)
                query = query.Skip(request.PageIndex * request.PageSize).Take(request.PageSize);

            var pageDatas = query.ToArray().Select(x =>
            {
                var user = users.FirstOrDefault(a => a.Id == x.OwnerId);
                var response = new GetSubbudgetListResponseDto
                {
                    Id = x.Id,
                    SubbudgetCode = x.Code,
                    Description = x.Description,
                    BudgetAmount = x.BudgetAmount,
                    UsedAmount = x.UesdAmount,
                    //AvailableAmount = x.AvailableAmount,
                    MasterBudgetId = x.MasterBudgetId,
                    MasterBudgetCode = x.MasterBudget.Code,
                    BuId = x.BuId,
                    BuName = orgs.FirstOrDefault(a => a.Id == x.BuId)?.DepartmentName,
                    CostCenterId = x.CostCenterId,
                    CostCenterName = coscenters.FirstOrDefault(a => a.Id == x.CostCenterId)?.Name,
                    RegionId = x.RegionId,
                    RegionName = regions.FirstOrDefault(a => a.Id == x.RegionId).Name,
                    OwnerId = x.OwnerId,
                    OwnerName = user?.Name,
                    OwnerEmail = user?.Email,
                    Status = x.Status,
                    IsDeletable = x.UesdAmount <= 0,
                    Year = x.MasterBudget.Year,

                    Monthlies = x.MonthlyBudgets.Select(s => new MonthlyBudgetDto() { BudgetAmount = s.BudgetAmount, Month = s.Month, Status = s.Status }).ToDictionary(a => a.Month)
                };
                if (isPagin) response.AttachmentInformation = GetAttachmentInfo(x.AttachmentFile).Result?.ToList();
                if (request.BuName == "EPD")
                {
                    response.BU2 = x.Bu2;
                    response.OwnerName2 = x.Owner2;
                    response.RegionManagerName = x.RegionManagers;
                    response.LMMName = x.LMMs;
                    response.ProductManagerName = x.ProductManagers;
                }
                else if (request.BuName == "ADC")
                {
                    response.IsComplicanceAudits = x.IsComplicanceAudits;
                }
                return response;
            }).ToArray();

            var result = new PagedResultDto<GetSubbudgetListResponseDto>(count, pageDatas);
            return result;
        }

        /// <summary>
        /// 导出子预算管理列表
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetSubbudgetListResponseDto>> ExportSubbudgetManageListAsync(GetSubbudgetListRequestDto request)
        {
            var querySubbudget = await LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>().GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();

            var queryPr = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var queryBudgetReturn = await LazyServiceProvider.LazyGetService<IBdBudgetReturnRepository>().GetQueryableAsync();
            var queryPo = await LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>().GetQueryableAsync();
            var queryPa = await LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>().GetQueryableAsync();

            //status
            IEnumerable<PurPRApplicationStatus> prCommittedStatus = [PurPRApplicationStatus.Draft, PurPRApplicationStatus.Rejected, PurPRApplicationStatus.ApplicantTerminate];
            IEnumerable<PurPRApplicationStatus> prPendingAmountStatus = [PurPRApplicationStatus.Approving, PurPRApplicationStatus.Approved];
            IEnumerable<PurPRApplicationStatus> prApproveAmountStatus = [PurPRApplicationStatus.Approved, PurPRApplicationStatus.WaitForClose, PurPRApplicationStatus.Closed];
            IEnumerable<PurOrderStatus> poPendingAmountStatus = [PurOrderStatus.ApplierConfirmed, PurOrderStatus.Approving, PurOrderStatus.InitiateReceipt];
            IEnumerable<PurPAStatus.PurPAApplicationStatus> paPendingAmountStatus = [PurPAStatus.PurPAApplicationStatus.Approvaling, PurPAStatus.PurPAApplicationStatus.Reissue, PurPAStatus.PurPAApplicationStatus.DocumentReceipt, PurPAStatus.PurPAApplicationStatus.FinancialPreliminaryReview, PurPAStatus.PurPAApplicationStatus.FinancialReview, PurPAStatus.PurPAApplicationStatus.BudgetManagerApproval];
            IEnumerable<PurPAStatus.PurPAApplicationStatus> paProcessedAmountStatus = [PurPAStatus.PurPAApplicationStatus.WaitingForPayment, PurPAStatus.PurPAApplicationStatus.PaymentProgress, PurPAStatus.PurPAApplicationStatus.Paid, PurPAStatus.PurPAApplicationStatus.PaymenFailed];

            //var subBudgetId = Guid.Parse("83017CB2-2BE3-4B1F-98C6-000C5D65B396");
            //var x1 = queryPr.Where(a1 => !prCommittedStatus.Contains(a1.Status) && a1.SubBudgetId == subBudgetId).Select(a1 => a1.TotalAmountRMB);
            //var x2 = queryBudgetReturn.Where(a1 => a1.SubbudgetId == subBudgetId && !a1.ReturnSourceCode.StartsWith("P")).Select(a1 => a1.Amount);
            //var x3 = queryPr.Where(a1 => prPendingAmountStatus.Contains(a1.Status) && a1.SubBudgetId == subBudgetId).Select(a1 => a1.TotalAmountRMB);
            //var x4 = queryPr.Where(a1 => prApproveAmountStatus.Contains(a1.Status) && a1.SubBudgetId == subBudgetId).Select(a1 => a1.TotalAmountRMB);
            //var x5 = queryPr.Where(a1 => a1.SubBudgetId == subBudgetId).Join(queryPo.Where(a1 => poPendingAmountStatus.Contains(a1.Status)), a1 => a1.Id, a1 => a1.PRId, (a1, b1) => b1.TotalAmount * (decimal)b1.ExchangeRate);
            //var x6 = queryPr.Where(a1 => a1.SubBudgetId == subBudgetId).Join(queryPo.Where(a1 => a1.Status == PurOrderStatus.Closed), a1 => a1.Id, a1 => a1.PRId, (a1, b1) => new { RMBAmount = b1.TotalAmount * (decimal)b1.ExchangeRate, HsaPa = queryPa.Where(a2 => a2.POId == b1.Id).Any(a2 => a2.Status != PurPAStatus.PurPAApplicationStatus.Void) }).Where(a1 => !a1.HsaPa).Select(a1 => a1.RMBAmount);
            //var x7 = queryPr.Where(a1 => a1.SubBudgetId == subBudgetId).Join(queryPo.Where(a1 => a1.Status == PurOrderStatus.Closed), a1 => a1.Id, a1 => a1.PRId, (a1, b1) => new { RMBAmount = b1.TotalAmount * (decimal)b1.ExchangeRate, HsaPa = queryPa.Where(a2 => a2.POId == b1.Id).Any(a2 => a2.Status != PurPAStatus.PurPAApplicationStatus.Void) }).Where(a1 => a1.HsaPa).Select(a1 => a1.RMBAmount);
            //var x8 = queryPr.Where(a1 => a1.SubBudgetId == subBudgetId).Join(queryPa.Where(a1 => paPendingAmountStatus.Contains(a1.Status)), a1 => a1.Id, a1 => a1.PRId, (a1, b1) => b1.PayTotalAmount * (decimal)b1.ExchangeRate);
            //var x9 = queryPr.Where(a1 => a1.SubBudgetId == subBudgetId).Join(queryPa.Where(a1 => paProcessedAmountStatus.Contains(a1.Status)), a1 => a1.Id, a1 => a1.PRId, (a1, b1) => b1.PayTotalAmount * (decimal)b1.ExchangeRate);

            var query = querySubbudget.Include(x => x.MasterBudget).Include(x => x.MonthlyBudgets).Where(x => x.BuId == request.BuId)
                .WhereIf(!string.IsNullOrEmpty(request.MasterBudgetCode), x => x.MasterBudget.Code == request.MasterBudgetCode)
                .WhereIf(request.MasterBudgetId.HasValue, x => x.MasterBudgetId == request.MasterBudgetId)
                .WhereIf(request.CostCenterId.HasValue, x => x.CostCenterId == request.CostCenterId)
                .WhereIf(request.RegionId.HasValue, x => x.RegionId == request.RegionId)
                .WhereIf(!string.IsNullOrEmpty(request.SubbudgetCode), x => x.Code.Contains(request.SubbudgetCode))
                .WhereIf(request.Year.HasValue, x => x.MasterBudget.Year == request.Year)
                .WhereIf(!string.IsNullOrEmpty(request.Description), x => x.Description.Contains(request.Description))
                .WhereIf(request.MasterBudgetOwnerId.HasValue, x => x.MasterBudget.OwnerId == request.MasterBudgetOwnerId)
                .WhereIf(request.SubbudgetOwnerId.HasValue, x => x.OwnerId == request.SubbudgetOwnerId)
                .Select(a => new
                {
                    a.Id,
                    a.Code,
                    a.Description,
                    a.BudgetAmount,
                    a.UesdAmount,
                    a.BuId,
                    a.CostCenterId,
                    a.RegionId,
                    a.OwnerId,
                    a.Status,
                    a.Bu2,
                    a.Owner2,
                    a.RegionManagers,
                    a.ProductManagers,
                    a.LMMs,
                    a.IsComplicanceAudits,
                    a.AttachmentFile,
                    a.CreationTime,

                    a.MasterBudgetId,
                    a.MasterBudget,
                    a.MonthlyBudgets,

                    //new fields
                    //子预算所在PR的申请人民币金额，PR状态非1-草稿/4-拒绝/6-作废
                    PRCommitted = queryPr.Where(a1 => !prCommittedStatus.Contains(a1.Status) && a1.SubBudgetId == a.Id).Sum(a1 => a1.TotalAmountRMB),
                    //子预算by当前PR的返还金额，PR状态非1-草稿/4-拒绝/6-作废，且返还记录需要排除返还单号为"P"开头的，这类目前是0元结算反冲时会产生的返还，但由于0元结算时PR总金额已经更新了，这里如果算上就会重复返还了
                    PRSaving = queryBudgetReturn.Where(a1 => a1.SubbudgetId == a.Id && !a1.ReturnSourceCode.StartsWith("P")).Sum(a1 => a1.Amount),
                    //子预算所在PR的申请人民币金额，PR状态为2-审批中/3-审批通过
                    PRPendingAmount = queryPr.Where(a1 => prPendingAmountStatus.Contains(a1.Status) && a1.SubBudgetId == a.Id).Sum(a1 => a1.TotalAmountRMB),
                    //子预算所在PR的申请人民币金额，PR状态为3-审批通过/9-等待关闭/10-订单关闭
                    PRApproveAmount = queryPr.Where(a1 => prApproveAmountStatus.Contains(a1.Status) && a1.SubBudgetId == a.Id).Sum(a1 => a1.TotalAmountRMB),
                    //子预算所在PR对应PO的申请人民币金额，PO状态为1-申请人确认/2-审批中/10-发起收货(3这个状态是不是没有用？
                    POPendingAmount = queryPr.Where(a1 => a1.SubBudgetId == a.Id).Join(queryPo.Where(a1 => poPendingAmountStatus.Contains(a1.Status)), a1 => a1.Id, a1 => a1.PRId, (a1, b1) => b1.TotalAmount * (decimal)b1.ExchangeRate).Sum(),
                    //子预算所在PR对应PO的申请人民币金额，PO状态为6-关闭，且PO尚未生成PA
                    POApproveAmount = queryPr.Where(a1 => a1.SubBudgetId == a.Id).Join(queryPo.Where(a1 => a1.Status == PurOrderStatus.Closed), a1 => a1.Id, a1 => a1.PRId, (a1, b1) => new { RMBAmount = b1.TotalAmount * (decimal)b1.ExchangeRate, HsaPa = queryPa.Where(a2 => a2.POId == b1.Id).Any(a2 => a2.Status != PurPAStatus.PurPAApplicationStatus.Void) }).Where(a1 => !a1.HsaPa).Sum(a1 => a1.RMBAmount),
                    //子预算所在PR对应PO的申请人民币金额，PO状态为6-关闭，且PO已生成了PA
                    POCloseAmount = queryPr.Where(a1 => a1.SubBudgetId == a.Id).Join(queryPo.Where(a1 => a1.Status == PurOrderStatus.Closed), a1 => a1.Id, a1 => a1.PRId, (a1, b1) => new { RMBAmount = b1.TotalAmount * (decimal)b1.ExchangeRate, HsaPa = queryPa.Where(a2 => a2.POId == b1.Id).Any(a2 => a2.Status != PurPAStatus.PurPAApplicationStatus.Void) }).Where(a1 => a1.HsaPa).Sum(a1 => a1.RMBAmount),
                    //子预算所在PR对应PA的申请人民币金额，PA状态为2-审批中/7-重发起/9-单据接收/10-初审/11-复审/12-预算负责人审批
                    PAPendingAmount = queryPr.Where(a1 => a1.SubBudgetId == a.Id).Join(queryPa.Where(a1 => paPendingAmountStatus.Contains(a1.Status)), a1 => a1.Id, a1 => a1.PRId, (a1, b1) => b1.PayTotalAmount * (decimal)b1.ExchangeRate).Sum(),
                    //子预算所在PR对应PA的申请人民币金额，PA状态为3-待打款/4-打款中/5-已打款/6-打款失败
                    PAProcessedAmount = queryPr.Where(a1 => a1.SubBudgetId == a.Id).Join(queryPa.Where(a1 => paProcessedAmountStatus.Contains(a1.Status)), a1 => a1.Id, a1 => a1.PRId, (a1, b1) => b1.PayTotalAmount * (decimal)b1.ExchangeRate).Sum()
                });

            var users = await dataverseService.GetStaffs(stateCode: null);
            var orgs = await dataverseService.GetOrganizations(stateCode: null);
            var coscenters = await dataverseService.GetCostcentersAsync(stateCode: null);
            var regions = await dataverseService.GetDistrict(stateCode: null);

            var dictUsers = users.ToDictionary(a => a.Id, a => a);
            var dictOrgs = orgs.ToDictionary(a => a.Id, a => a);
            var dictCostCenters = coscenters.ToDictionary(a => a.Id, a => a);
            var dictRegions = regions.ToDictionary(a => a.Id, a => a);

            var datas = query.ToArray().Select(x =>
            {
                dictUsers.TryGetValue(x.OwnerId, out StaffDto subBudgetUser);
                dictUsers.TryGetValue(x.MasterBudget.OwnerId, out StaffDto masterBudgetUser);
                dictOrgs.TryGetValue(x.BuId, out DepartmentDto org);
                dictCostCenters.TryGetValue(x.CostCenterId, out CostcenterDto costcenter);
                dictRegions.TryGetValue(x.RegionId, out DistrictDto region);

                var response = new GetSubbudgetListResponseDto
                {
                    Id = x.Id,
                    SubbudgetCode = x.Code,
                    Description = x.Description,
                    BudgetAmount = x.BudgetAmount,
                    UsedAmount = x.UesdAmount,
                    MasterBudgetId = x.MasterBudgetId,
                    MasterBudgetCode = x.MasterBudget.Code,
                    BuId = x.BuId,
                    BuName = org?.DepartmentName,
                    CostCenterId = x.CostCenterId,
                    CostCenterName = costcenter?.Name,
                    RegionId = x.RegionId,
                    RegionName = region?.Name,
                    OwnerId = x.OwnerId,
                    OwnerName = subBudgetUser?.Name,
                    OwnerEmail = subBudgetUser?.Email,
                    Status = x.Status,
                    IsDeletable = x.UesdAmount <= 0,
                    Year = x.MasterBudget.Year,

                    //new fields
                    MasterBudgetDescription = x.MasterBudget.Description,
                    MasterBudgetOwnerName = masterBudgetUser?.Name,
                    MasterBudgetAmount = x.MasterBudget.BudgetAmount,

                    PRCommitted = decimal.Round(x.PRCommitted, 2),
                    PRSaving = decimal.Round(x.PRSaving, 2),
                    PRPendingAmount = decimal.Round(x.PRPendingAmount, 2),
                    PRApproveAmount = decimal.Round(x.PRApproveAmount, 2),
                    POPendingAmount = decimal.Round(x.POPendingAmount, 2),
                    POApproveAmount = decimal.Round(x.POApproveAmount, 2),
                    POCloseAmount = decimal.Round(x.POCloseAmount, 2),
                    PAPendingAmount = decimal.Round(x.PAPendingAmount, 2),
                    PAProcessedAmount = decimal.Round(x.PAProcessedAmount, 2),
                    //核销金额，目前没有这部分数据，直接为0即可
                    DebitNote = "0",
                    //以子预算总金额-【计算出的pr committed】+【计算出的pr saving】
                    CategoryBalance = decimal.Round(x.BudgetAmount - x.PRCommitted + x.PRSaving, 2),

                    Monthlies = x.MonthlyBudgets.Select(s => new MonthlyBudgetDto { BudgetAmount = s.BudgetAmount, Month = s.Month, Status = s.Status }).ToDictionary(a => a.Month)
                };

                if (request.BuName == "EPD")
                {
                    response.BU2 = x.Bu2;
                    response.OwnerName2 = x.Owner2;
                    response.RegionManagerName = x.RegionManagers;
                    response.LMMName = x.LMMs;
                    response.ProductManagerName = x.ProductManagers;
                }
                else if (request.BuName == "ADC")
                {
                    response.IsComplicanceAudits = x.IsComplicanceAudits;
                }
                return response;
            }).ToArray();

            var result = new PagedResultDto<GetSubbudgetListResponseDto>(datas.Length, datas);
            return result;
        }

        /// <summary>
        /// 将dynamic转为string
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        private string ConverTostring(dynamic value)
        {
            try { return value.ToString(); } catch { return ""; }
        }
        /// <summary>
        /// 批量新增
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> SubmitBatchCreateSubBudgetAsync(List<CreateImportMessageDto> request)
        {
            var subbudgetRespository = LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>();
            var query = (await subbudgetRespository.GetQueryableAsync()).AsNoTracking();
            var masterCodes = request.Select(c => c.MasterBudgetCode).ToList();
            var queryMasterRepository = LazyServiceProvider.LazyGetService<IBdMasterBudgetRepository>();
            var queryMasterBudget = (await queryMasterRepository.GetQueryableAsync()).AsNoTracking();
            var mtBudget = queryMasterBudget.Where(m => masterCodes.Contains(m.Code)).ToList();
            List<BdSubBudget> bdSubBudgets = [];
            List<BdHistory> his = [];
            //int count;
            //var now = DateTimeOffset.Now;
            //using (DataFilter.Disable<ISoftDelete>())
            //{
            //    count = await subbudgetRespository.CountAsync(a => a.CreationTime.Date.Year == now.Year);
            //}
            Dictionary<string, string> keyValues = [];
            request.ForEach(s =>
            {
                //var code = $"CC{now:yy}-{++count:D4}";
                var Id = Guid.NewGuid();
                var Amonut = s.MonthlyBudgets.Sum(s => s.BudgetAmount);
                var monthlyBudgets = ObjectMapper.Map<List<MonthlyBudgetDto>, List<BdMonthlyBudget>>(s.MonthlyBudgets);

                List<BdSubBudgetMonthChangeHistory> changeHistories = [];
                foreach (var item in monthlyBudgets)
                {
                    BdSubBudgetMonthChangeHistory bdSubBudgetHis = new()
                    {
                        Month = item.Month,
                        OperateAmount = item.BudgetAmount,
                        Status = item.Status,
                    };
                    bdSubBudgetHis.SetId(Guid.NewGuid());
                    changeHistories.Add(bdSubBudgetHis);
                }
                BdSubBudget bds = new()
                {
                    //Code = code,
                    Description = s.Description,
                    Remark = s.Remark,
                    BudgetAmount = Amonut,
                    CostCenterId = s.CostCenterId.Value,
                    RegionId = s.RegionId.Value,
                    OwnerId = s.OwnerId.Value,
                    Status = true,
                    MasterBudgetId = s.MasterBudgetId.Value,
                    BuId = mtBudget.First(m => m.Code == s.MasterBudgetCode).BuId,
                    MonthlyBudgets = monthlyBudgets
                };
                bds.SetId(Id);
                bdSubBudgets.Add(bds);
                BdHistory history = new()
                {
                    OperateType = OperateType.Create,
                    OperateContent = $"预算金额：{Amonut};预算负责人：{s.OwnerName}；描述：{s.Description}",
                    BudgetId = Id,
                    OperatorId = CurrentUser.Id.Value,
                    OperatorName = CurrentUser.Name,
                    OperatingTime = DateTime.Now,
                    OperateAmount = Amonut,
                    Remark = s.Remark,
                    BudgetType = BudgetType.SubBudget,
                    SubBudgetMonthChangeHistorys = changeHistories
                };
                his.Add(history);
            });
            //await subbudgetRespository.InsertManyAsync(bdSubBudgets);
            await BatchSGenerateubCode(subbudgetRespository, bdSubBudgets);
            await SaveHistory(his);
            return MessageResult.SuccessResult("操作成功");
        }

        /// <summary>
        /// 设置子预算状态为启用或冻结
        /// </summary>
        /// <param name="ids">The identifier.</param>
        /// <param name="status">if set to <c>true</c> [status].</param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MessageResult> SetSubbudgetStatusAsync(List<Guid> ids, bool status)
        {
            var subbudgetRespository = LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>();
            var querySubbudget = await LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>().GetQueryableAsync();

            var query = querySubbudget.Where(x => ids.Contains(x.Id));
            var count = query.Count();
            if (count < ids.Count)
                return MessageResult.FailureResult("id不存在");
            var datas = query.ToList();
            if (datas.Any(x => x.Status == status))
                return MessageResult.FailureResult(string.Format("已{0}的子预算不可{0}", status ? "启用" : "冻结"));

            //History
            var histories = new List<BdHistory>();
            datas.ForEach(x =>
            {
                x.Status = status;
                histories.Add(new BdHistory
                {
                    OperateType = status ? OperateType.Enable : OperateType.Disable,
                    OperateContent = string.Format("是否启用：{0}", status ? "是" : "否"),
                    BudgetId = x.Id,
                    OperatorId = CurrentUser.Id.Value,
                    OperatorName = CurrentUser.Name,
                    OperatingTime = DateTime.Now,
                    BudgetType = BudgetType.SubBudget
                });
            });
            await SaveHistory(histories);

            await subbudgetRespository.UpdateManyAsync(datas);

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 删除子预算
        /// </summary>
        /// <param name="ids">The ids.</param>
        /// <returns></returns>
        public async Task<MessageResult> DeleteSubbudget(List<Guid> ids)
        {
            var subbudgetRespository = LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>();
            var querySubbudget = await LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>().GetQueryableAsync();

            var query = querySubbudget.Where(x => ids.Contains(x.Id));
            var count = query.Count();
            if (count < ids.Count)
                return MessageResult.FailureResult("id不存在");
            var datas = query.ToList();
            if (datas.Any(x => x.UesdAmount > 0))
                return MessageResult.FailureResult("已用子预算不可删除");

            await subbudgetRespository.DeleteManyAsync(datas);

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 子预算批量mapping
        /// </summary>
        /// <param name="Rows"></param>
        /// <returns></returns>
        public async Task<MessageResult> VarifyBatchMappingSubBudgetAsync(IEnumerable<MappingSubBudgetDto> Rows)
        {
            var budgetCodes = Rows.Select(a => a.Code);
            //主预算
            var querySubRepository = LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>();
            var querySubBudget = (await querySubRepository.GetQueryableAsync()).AsNoTracking();
            var subBudget = querySubBudget.Where(m => budgetCodes.Contains(m.Code));
            var dicSubBudget = await subBudget.ToDictionaryAsync(x => x.Code);
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();

            var Owner2Email = Rows.Select(item => item.Owner2).ToArray();
            var RegionManagerEmail = Rows.Select(item => item.RegionManager).ToArray();
            var LMMEmail = Rows.Select(item => item.LMM).ToArray();
            var RegionalAssistantEmail = Rows.Select(item => item.RegionalAssistant).ToArray();
            var ProductManagerEmail = Rows.Select(item => item.ProductManager).ToArray();
            string[] userEmails = [.. Owner2Email, .. RegionManagerEmail, .. LMMEmail, .. RegionalAssistantEmail, .. ProductManagerEmail];
            var usereMaillist = SplitUserEmails(userEmails).Distinct();
            var users = queryableUser.Where(m => usereMaillist.Contains(m.Email)).Select(s => new { s.Email, s.Name, s.Id }).ToList();
            List<MappingSubgetMessageDto> error = [];
            List<MappingSubgetMessageDto> success = [];
            int i = 0;
            var rowNo = 6;
            foreach (var item in Rows)
            {
                rowNo++;
                i++;
                string message = string.Empty;
                MappingSubgetMessageDto mapping = new();
                //mapping.No = i;
                mapping.No = rowNo;
                mapping.Code = item.Code;
                mapping.Bu2 = item.Bu2;
                if (string.IsNullOrEmpty(item.Code)) { message += "请填写子预算编码;"; }
                else if (!dicSubBudget.ContainsKey(item.Code)) message += "未查找到子预算编码;";
                if (!string.IsNullOrEmpty(item.Owner2))
                {
                    var useremals = SplitUserEmails(item.Owner2);
                    mapping.Owner2Names = users.Where(m => useremals.Contains(m.Email)).Select(a => a.Name).JoinAsString(",");
                    mapping.Owner2Ids = users.Where(m => useremals.Contains(m.Email)).Select(a => a.Id).JoinAsString(",");
                }
                if (!string.IsNullOrEmpty(item.RegionManager))
                {
                    var useremals = SplitUserEmails(item.RegionManager);
                    mapping.RegionManagerNames = users.Where(m => useremals.Contains(m.Email)).Select(a => a.Name).JoinAsString(",");
                    mapping.RegionManagerIds = users.Where(m => useremals.Contains(m.Email)).Select(a => a.Id).JoinAsString(",");
                }
                if (!string.IsNullOrEmpty(item.LMM))
                {
                    var useremals = SplitUserEmails(item.LMM);
                    mapping.LMMNames = users.Where(m => useremals.Contains(m.Email)).Select(a => a.Name).JoinAsString(",");
                    mapping.LMMIds = users.Where(m => useremals.Contains(m.Email)).Select(a => a.Id).JoinAsString(",");
                }
                if (!string.IsNullOrEmpty(item.RegionalAssistant))
                {
                    var useremals = SplitUserEmails(item.RegionalAssistant);
                    mapping.RegionalAssistantNames = users.Where(m => useremals.Contains(m.Email)).Select(a => a.Name).JoinAsString(",");
                    mapping.RegionalAssistantIds = users.Where(m => useremals.Contains(m.Email)).Select(a => a.Id).JoinAsString(",");
                }
                if (!string.IsNullOrEmpty(item.ProductManager))
                {
                    var useremals = SplitUserEmails(item.ProductManager);
                    mapping.ProductManagerNames = users.Where(m => useremals.Contains(m.Email)).Select(a => a.Name).JoinAsString(",");
                    mapping.ProductManagerIds = users.Where(m => useremals.Contains(m.Email)).Select(a => a.Id).JoinAsString(",");
                }
                if (!string.IsNullOrWhiteSpace(message))
                {
                    mapping.Message = message;
                    error.Add(mapping);
                }
                else success.Add(mapping);
            }
            if (error.Count > 0)
            {
                return MessageResult.SuccessResult(new ImportDataResponseDto<MappingSubgetMessageDto>(error, false));
            }
            return MessageResult.SuccessResult(new ImportDataResponseDto<MappingSubgetMessageDto>(success, true));
        }

        /// <summary>
        /// 导出子预算列表
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<Stream> ExportSubbudgetListAsync(GetSubbudgetListRequestDto request)
        {
            MemoryStream stream = new();
            var response = await ExportSubbudgetManageListAsync(request);
            var properties = typeof(GetSubbudgetListResponseDto).GetProperties().ToList();
            var config = new OpenXmlConfiguration() { TableStyles = MiniExcelLibs.OpenXml.TableStyles.None, AutoFilter = false };
            var specialBu = new List<string> { BUNameConst.EPD, BUNameConst.ADC };
            if (specialBu.Contains(request.BuName))
            {
                var dynamicCols = new List<DynamicExcelColumn>();
                properties.ForEach(x =>
                {
                    var attribute = Attribute.GetCustomAttribute(x, typeof(CategoryAttribute)) as CategoryAttribute;
                    if (null != attribute && !string.IsNullOrEmpty(attribute.Category))
                        dynamicCols.Add(new DynamicExcelColumn(x.Name) { Ignore = !attribute.Category.Equals(request.BuName) });
                });
                config.DynamicColumns = [.. dynamicCols];
            }

            stream.SaveAs(response.Items, true, "Sheet1", configuration: config);
            stream.Seek(0, SeekOrigin.Begin);
            return stream;
        }

        private static List<string> SplitUserEmails(params string[] userEmails)
        {
            List<string> Emalis = [];
            foreach (var item in userEmails)
            {
                if (!string.IsNullOrWhiteSpace(item))
                {
                    var strs = item.Split(',', StringSplitOptions.RemoveEmptyEntries);
                    Emalis.AddRange(strs);
                }
            }
            return Emalis;
        }
        /// <summary>
        /// 提交批量Mapping
        /// </summary>
        /// <param name="mappings"></param>
        /// <returns></returns>
        public async Task<MessageResult> SubmitMappingSubBudgetAsync(List<MappingSubgetMessageDto> mappings)
        {
            var codes = mappings.Select(s => s.Code).Distinct().ToList();
            var querySubRepository = LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>();
            var querySubBudget = await querySubRepository.GetQueryableAsync();

            var repositorySubbudgetMapping = LazyServiceProvider.LazyGetService<IBdSubBudgetMappingRepository>();
            var querySubbudgetMapping = await repositorySubbudgetMapping.GetQueryableAsync();

            var subBudgets = querySubBudget.Where(m => codes.Contains(m.Code)).ToList();
            var origSubBudgetMappings = querySubbudgetMapping.Where(a => codes.Contains(a.Code)).ToList();

            List<BdSubBudgetMapping> subbudgetMappings = [];
            foreach (var item in mappings)
            {
                var subBudget = subBudgets.First(m => m.Code == item.Code);
                subBudget.Bu2 = item.Bu2;
                subBudget.Owner2 = item.Owner2Names;
                subBudget.RegionManagers = item.RegionManagerNames;
                subBudget.LMMs = item.LMMNames;
                subBudget.ProductManagers = item.ProductManagerNames;
                subBudget.RegionalAssistants = item.RegionalAssistantNames;

                #region 整理子预算Mapping
                if (!string.IsNullOrEmpty(item.Owner2Ids))
                {
                    var subMappings = item.Owner2Ids.Split(",").Select(x =>
                    {
                        var mapping = new BdSubBudgetMapping { Code = item.Code, SbuBudgetId = subBudget.Id };
                        if (Guid.TryParse(x, out Guid resultUserId))
                            mapping.UserId = resultUserId;
                        return mapping;
                    });
                    subbudgetMappings.AddRange(subMappings);
                }
                if (!string.IsNullOrEmpty(item.RegionManagerIds))
                {
                    var subMappings = item.RegionManagerIds.Split(",").Select(x =>
                    {
                        var mapping = new BdSubBudgetMapping { Code = item.Code, SbuBudgetId = subBudget.Id };
                        if (Guid.TryParse(x, out Guid resultUserId))
                            mapping.UserId = resultUserId;
                        return mapping;
                    });
                    subbudgetMappings.AddRange(subMappings);
                }
                if (!string.IsNullOrEmpty(item.LMMIds))
                {
                    var subMappings = item.LMMIds.Split(",").Select(x =>
                    {
                        var mapping = new BdSubBudgetMapping { Code = item.Code, SbuBudgetId = subBudget.Id };
                        if (Guid.TryParse(x, out Guid resultUserId))
                            mapping.UserId = resultUserId;
                        return mapping;
                    });
                    subbudgetMappings.AddRange(subMappings);
                }
                if (!string.IsNullOrEmpty(item.ProductManagerIds))
                {
                    var subMappings = item.ProductManagerIds.Split(",").Select(x =>
                    {
                        var mapping = new BdSubBudgetMapping { Code = item.Code, SbuBudgetId = subBudget.Id };
                        if (Guid.TryParse(x, out Guid resultUserId))
                            mapping.UserId = resultUserId;
                        return mapping;
                    });
                    subbudgetMappings.AddRange(subMappings);
                }
                if (!string.IsNullOrEmpty(item.RegionalAssistantIds))
                {
                    var subMappings = item.RegionalAssistantIds.Split(",").Select(x =>
                    {
                        var mapping = new BdSubBudgetMapping { Code = item.Code, SbuBudgetId = subBudget.Id };
                        if (Guid.TryParse(x, out Guid resultUserId))
                            mapping.UserId = resultUserId;
                        return mapping;
                    });
                    subbudgetMappings.AddRange(subMappings);
                }
                #endregion
                //把子预算负责人也加入到Mapping表中
                subbudgetMappings.Add(new BdSubBudgetMapping { Code = item.Code, SbuBudgetId = subBudget.Id, UserId = subBudget.OwnerId });
            }

            //更新子预算
            await querySubRepository.UpdateManyAsync(subBudgets);

            var toInsertMappings = subbudgetMappings.Where(x => x.UserId != Guid.Empty).DistinctBy(x => new { x.Code, x.UserId });
            //删除历史记录，再新增
            await repositorySubbudgetMapping.DeleteManyAsync(origSubBudgetMappings);
            await repositorySubbudgetMapping.InsertManyAsync(toInsertMappings);

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 判断子预算是否足够
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> CheckSubbudgetAmountSufficientAsync(UseBudgetRequestDto request)
        {
            var useSubbudgetRespository = LazyServiceProvider.LazyGetService<IBdBudgetUseRepository>();
            var queryUseSubbudget = await LazyServiceProvider.LazyGetService<IBdBudgetUseRepository>().GetQueryableAsync();
            var querySubbudget = await LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>().GetQueryableAsync();

            //check
            var totalUseAmount = request.Items.Select(x => x.UseAmount).Sum();
            var subbudgetData = querySubbudget.Include(s => s.MonthlyBudgets.Where(m => m.Status)).First(x => x.Id == request.SubbudgetId);

            var availableAmount = subbudgetData.GetAvailableAmount();
            var useSubbudgetDatas = queryUseSubbudget.Where(x => x.PrId == request.PrId && x.SubbudgetId == request.SubbudgetId).ToArray();

            if (useSubbudgetDatas.Count() > 0)
                availableAmount += useSubbudgetDatas.Select(x => x.Amount).Sum();

            if (availableAmount < totalUseAmount)
                return MessageResult.FailureResult($"预算可用金额不足，目前剩余可用金额：{availableAmount}");

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 使用子预算
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> UseSubbudgetAsync(UseBudgetRequestDto request, bool autoSave = false)
        {
            var result = await CheckSubbudgetAmountSufficientAsync(request);
            if (!result.Success)
                return result;

            var useSubbudgetRespository = LazyServiceProvider.LazyGetService<IBdBudgetUseRepository>();
            var subbudgetRespository = LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>();
            var querySubbudget = await LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>().GetQueryableAsync();

            //check
            var totalUseAmount = request.Items.Select(x => x.UseAmount).Sum();
            var subbudgetData = querySubbudget.First(x => x.Id == request.SubbudgetId);

            var datas = new List<BdBudgetUse>();

            foreach (var item in request.Items)
            {
                datas.Add(new BdBudgetUse
                {
                    PrId = request.PrId,
                    PdRowNo = item.PdRowNo,
                    SubbudgetId = request.SubbudgetId,
                    Amount = item.UseAmount,
                    OperateTime = DateTime.Now,
                    IsEnable = true
                });
            }

            //获取历史数据
            var usedHistories = await useSubbudgetRespository.GetListAsync(x => x.PrId == request.PrId && x.SubbudgetId == request.SubbudgetId);
            //将之前的使用金额返还
            var historyAmount = usedHistories.Sum(a => a.Amount);
            totalUseAmount -= historyAmount;

            //预算使用之后 对应子预算的已用金额、可用金额 相应的更新
            subbudgetData.UesdAmount += totalUseAmount;

            //subbudgetData.AvailableAmount -= totalUseAmount;

            //删除之前提交的使用明细（一个PR单、一个子预算只保留最近一次的使用明细）
            await useSubbudgetRespository.DeleteManyAsync(usedHistories, autoSave);
            await useSubbudgetRespository.InsertManyAsync(datas, autoSave);
            await subbudgetRespository.UpdateAsync(subbudgetData, autoSave);

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 退回子预算
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> ReturnSubbudgetAsync(ReturnBudgetRequestDto request)
        {
            var returnSubbudgetRespository = LazyServiceProvider.LazyGetService<IBdBudgetReturnRepository>();
            var queryReturnSubbudget = await LazyServiceProvider.LazyGetService<IBdBudgetReturnRepository>().GetQueryableAsync();
            var useSubbudgetRespository = LazyServiceProvider.LazyGetService<IBdBudgetUseRepository>();
            var queryUseSubbudget = await LazyServiceProvider.LazyGetService<IBdBudgetUseRepository>().GetQueryableAsync();
            var subbudgetRespository = LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>();
            var querySubbudget = await LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>().GetQueryableAsync();

            //check
            //var sourceId = request.Items.Select(x => x.ReturnSourceId).Distinct();
            //if (sourceId.Count() > 1)
            //    return MessageResult.FailureResult("同一个单据不能退回多次");

            //var count = await queryReturnSubbudget.CountAsync(x => x.PrId == request.PrId && x.SubbudgetId == request.SubbudgetId && sourceId.Contains(x.ReturnSourceId));
            //if (count > 0)
            //    return MessageResult.FailureResult("同一个单据不能退回多次");

            //TODO check:退回明细中 PdRowNo不应该重复，即针对同一个PR的退回，相同的行号应该合计后再请求


            //退回的金额 反馈到使用明细里面对应的行上 应该增加金额
            var rowNos = request.Items.Select(x => x.PdRowNo).Distinct();
            var useSubbudgetDatas = queryUseSubbudget.Where(x => x.PrId == request.PrId && x.SubbudgetId == request.SubbudgetId && rowNos.Contains(x.PdRowNo)).ToArray();

            var retDatasCreated = new List<BdBudgetReturn>();
            var useDatasUpdated = new List<BdBudgetUse>();

            var operateTime = DateTime.Now;
            request.Items.ToList().ForEach(x =>
            {
                //记录退回明细
                retDatasCreated.Add(new BdBudgetReturn
                {
                    PrId = request.PrId,
                    PdRowNo = x.PdRowNo,
                    SubbudgetId = request.SubbudgetId,
                    Amount = x.ReturnAmount,
                    OperateTime = operateTime,
                    IsEnable = true,
                    ReturnSourceId = x.ReturnSourceId,
                    ReturnSourceCode = x.ReturnSourceCode
                });

                var useSubbudgetInfo = useSubbudgetDatas.FirstOrDefault(u => u.PdRowNo == x.PdRowNo);
                if (useSubbudgetInfo != null)
                {
                    useSubbudgetInfo.Amount -= x.ReturnAmount;
                    useDatasUpdated.Add(useSubbudgetInfo);
                }
            });


            var totalReturnAmount = request.Items.Select(x => x.ReturnAmount).Sum();
            var subbudgetData = querySubbudget.FirstOrDefault(x => x.Id == request.SubbudgetId);
            if (subbudgetData == null)
                return MessageResult.FailureResult("未找到对应的子预算");
            subbudgetData.UesdAmount -= totalReturnAmount;
            //subbudgetData.AvailableAmount += totalReturnAmount;


            await returnSubbudgetRespository.InsertManyAsync(retDatasCreated);

            if (useDatasUpdated.Count > 0)
                await useSubbudgetRespository.UpdateManyAsync(useDatasUpdated);

            await subbudgetRespository.UpdateAsync(subbudgetData);

            return MessageResult.SuccessResult(operateTime);
        }
        /// <summary>
        ///  批量退回子预算
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> ReturnSubbudgetAsync(List<ReturnBudgetRequestDto> requests)
        {
            if (requests.Count == 0) return MessageResult.SuccessResult();
            var returnSubbudgetRespository = LazyServiceProvider.LazyGetService<IBdBudgetReturnRepository>();
            var queryReturnSubbudget = await LazyServiceProvider.LazyGetService<IBdBudgetReturnRepository>().GetQueryableAsync();
            var useSubbudgetRespository = LazyServiceProvider.LazyGetService<IBdBudgetUseRepository>();
            var queryUseSubbudget = await LazyServiceProvider.LazyGetService<IBdBudgetUseRepository>().GetQueryableAsync();
            var subbudgetRespository = LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>();
            var querySubbudget = await LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>().GetQueryableAsync();

            //check
            //var sourceId = request.Items.Select(x => x.ReturnSourceId).Distinct();
            //if (sourceId.Count() > 1)
            //    return MessageResult.FailureResult("同一个单据不能退回多次");

            //var count = await queryReturnSubbudget.CountAsync(x => x.PrId == request.PrId && x.SubbudgetId == request.SubbudgetId && sourceId.Contains(x.ReturnSourceId));
            //if (count > 0)
            //    return MessageResult.FailureResult("同一个单据不能退回多次");

            //TODO check:退回明细中 PdRowNo不应该重复，即针对同一个PR的退回，相同的行号应该合计后再请求


            //退回的金额 反馈到使用明细里面对应的行上 应该增加金额
            //退回的数量 反馈到使用明细里面对应的行上 应该增加数量
            var PrIds = requests.Select(s => s.PrId).Distinct().ToList();

            var rowNos = requests.SelectMany(s => s.Items.Select(a => a.PdRowNo)).Distinct().ToList();
            var subbudgetIds = requests.Select(s => s.SubbudgetId).Distinct().ToList();
            var useSubbudgetDatas = queryUseSubbudget.Where(x => PrIds.Contains(x.PrId) && subbudgetIds.Contains(x.SubbudgetId) && rowNos.Contains(x.PdRowNo)).ToArray();

            var subbudgetDatas = await querySubbudget.Where(m => subbudgetIds.Contains(m.Id)).ToListAsync();

            //var rowNos = requests.Items.Select(x => x.PdRowNo).Distinct();
            //var useSubbudgetDatas = queryUseSubbudget.Where(x => x.PrId == request.PrId && x.SubbudgetId == request.SubbudgetId && rowNos.Contains(x.PdRowNo)).ToArray();

            var retDatasCreated = new List<BdBudgetReturn>();
            var useDatasUpdated = new HashSet<BdBudgetUse>();

            var operateTime = DateTime.Now;
            foreach (var request in requests)
            {
                request.Items.ToList().ForEach(x =>
                {
                    //记录退回明细
                    retDatasCreated.Add(new BdBudgetReturn
                    {
                        PrId = request.PrId,
                        PdRowNo = x.PdRowNo,
                        SubbudgetId = request.SubbudgetId,
                        Amount = x.ReturnAmount,
                        OperateTime = operateTime,
                        IsEnable = true,
                        ReturnSourceId = x.ReturnSourceId,
                        ReturnSourceCode = x.ReturnSourceCode
                    });

                    var useSubbudgetInfo = useSubbudgetDatas.FirstOrDefault(u => u.PdRowNo == x.PdRowNo && u.PrId == request.PrId);
                    if (useSubbudgetInfo != null)
                    {
                        useSubbudgetInfo.Amount -= x.ReturnAmount;
                        useDatasUpdated.Add(useSubbudgetInfo);
                    }
                });
                var totalReturnAmount = request.Items.Select(x => x.ReturnAmount).Sum();
                var subbudgetData = subbudgetDatas.First(x => x.Id == request.SubbudgetId);
                subbudgetData.UesdAmount -= totalReturnAmount;
            }
            //subbudgetData.AvailableAmount += totalReturnAmount;


            await returnSubbudgetRespository.InsertManyAsync(retDatasCreated);

            if (useDatasUpdated.Count > 0)
                await useSubbudgetRespository.UpdateManyAsync(useDatasUpdated);

            await subbudgetRespository.UpdateManyAsync(subbudgetDatas);

            return MessageResult.SuccessResult(operateTime);
        }
        /// <summary>
        /// 获取子预算使用明细
        /// </summary>
        /// <param name="subbudgetId">The subbudget identifier.</param>
        /// <returns></returns>
        public async Task<IEnumerable<GetSubbudgetUseInfoResponseDto>> GetSubbudgetUseInfo(Guid subbudgetId)
        {
            var queryUseSubbudget = await LazyServiceProvider.LazyGetService<IBdBudgetUseRepository>().GetQueryableAsync();
            var queryPrApplication = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var querySTicketApplication = await LazyServiceProvider.LazyGetService<ISTicketApplicationRepository>().GetQueryableAsync();

            //var query = queryUseSubbudget.Include(x => x.PrApplication)
            //    .Where(x => x.SubbudgetId == subbudgetId && x.IsEnable);

            //var gpSubbudget = query.ToArray().GroupBy(x => new { x.PrId, x.PrApplication, x.Subbudget })
            //    .Select(g => new { g.Key.PrId, g.Key.PrApplication, g.Key.Subbudget, totalAmount = g.Sum(x => x.Amount) });

            var query = queryUseSubbudget.Where(a => a.SubbudgetId == subbudgetId && a.IsEnable);
            var prIdList = query.Where(a => a.ProcessType == BudgetProcessTypes.PR).Select(a => a.PrId).Distinct().ToList();
            var queryPr = queryPrApplication.Where(a => prIdList.Contains(a.Id));

            var gpSubbudget = query.GroupBy(x => new { x.PrId, x.SubbudgetId, x.ProcessType })
                .Select(g => new { g.Key.PrId, g.Key.SubbudgetId, g.Key.ProcessType, totalAmount = g.Sum(x => x.Amount) })
                .Join(queryPr, a => a.PrId, a => a.Id, (a, b) => new { gpSubbudget = a, pr = b });

            //STicket
            var sticketUseQuery = queryUseSubbudget.Where(a => a.ProcessType == BudgetProcessTypes.STicket && a.SubbudgetId == subbudgetId && a.IsEnable);
            var sticketIdList = query.Where(a => a.ProcessType == BudgetProcessTypes.STicket).Select(a => a.PrId).Distinct().ToList();
            var querySTicket = querySTicketApplication.Where(a => sticketIdList.Contains(a.Id));
            var gpSTicketSubbudget = sticketUseQuery.GroupBy(x => new { x.PrId, x.SubbudgetId, x.ProcessType })
                .Select(g => new { g.Key.PrId, g.Key.SubbudgetId, g.Key.ProcessType, totalAmount = g.Sum(x => x.Amount) })
                .Join(querySTicket, a => a.PrId, a => a.Id, (a, b) => new { gpSTicketSubbudget = a, sticket = b });

            var responses = new List<GetSubbudgetUseInfoResponseDto>();

            gpSubbudget.ToList().ForEach(x =>
            {
                responses.Add(new GetSubbudgetUseInfoResponseDto
                {
                    ApplyUserName = x.pr.ApplyUserIdName,
                    ApplyTime = x.pr.ApplyTime?.ToString("yyyy-MM-dd"),
                    WorkflowTypeName = "采购申请",
                    FlowStatus = x.pr.Status,
                    FlowStatusText = x.pr.Status.GetDescription(),
                    FlowNo = x.pr.ApplicationCode,
                    UseAmount = x.gpSubbudget.totalAmount
                });
            });

            gpSTicketSubbudget.ToList().ForEach(x =>
            {
                responses.Add(new GetSubbudgetUseInfoResponseDto
                {
                    ApplyUserName = x.sticket.ApplyUser,
                    ApplyTime = x.sticket.ApplyTime?.ToString("yyyy-MM-dd"),
                    WorkflowTypeName = "核销申请",
                    STicketStatus = x.sticket.Status,
                    FlowStatusText = x.sticket.Status.GetDescription(),
                    FlowNo = x.sticket.ApplicationCode,
                    UseAmount = x.gpSTicketSubbudget.totalAmount
                });
            });
            responses = responses.OrderByDescending(o => o.ApplyTime).ToList();
            return responses;
        }
        /// <summary>
        /// 通过子预算和月份查询该月的预算金额
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        public async Task<MessageResult> GetMothlyAmountAsync(GetMothlyBudgetRequestDto requestDto)
        {
            var queryBdMonthlyBudget = await LazyServiceProvider.LazyGetService<IBdMonthlyBudgetRepository>().GetQueryableAsync();
            var monthlyBudget = queryBdMonthlyBudget.AsNoTracking().FirstOrDefault(s => s.Month == requestDto.Month && s.SubBudgetId == requestDto.Id);

            if (monthlyBudget == null)
                return MessageResult.FailureResult("未找到该月份预算信息,请联系管理员!");
            MonthBudgetReponseDto month = new() { Month = monthlyBudget.Month, Amount = monthlyBudget.BudgetAmount };
            return MessageResult.SuccessResult(month);
        }
        /// <summary>
        /// 将预算分配到每个月上去
        /// </summary>
        /// <returns></returns>
        public async Task ExcuteJobAsync()
        {
            var queryBdMonthlyBudget = LazyServiceProvider.LazyGetService<IBdMonthlyBudgetRepository>();
            var querySubbudget = await LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>().GetQueryableAsync();
            var nonSpiltBudget = await querySubbudget.Include(s => s.MonthlyBudgets).Where(s => s.MonthlyBudgets.Count == 0).ToListAsync();
            List<BdMonthlyBudget> bds = new List<BdMonthlyBudget>();
            nonSpiltBudget.ForEach(s =>
                {

                    List<BdMonthlyBudget> bdMonthlies = new()
                    {
                        new BdMonthlyBudget {SubBudgetId=s.Id,BudgetAmount=s.BudgetAmount,Month=Month.Jan,Status=true },
                        new BdMonthlyBudget {SubBudgetId=s.Id,BudgetAmount=0,Month=Month.Feb,Status=false },
                        new BdMonthlyBudget {SubBudgetId=s.Id,BudgetAmount=0,Month=Month.Mar,Status=false },
                        new BdMonthlyBudget {SubBudgetId=s.Id,BudgetAmount=0,Month=Month.Apr,Status=false },
                        new BdMonthlyBudget {SubBudgetId=s.Id,BudgetAmount=0,Month=Month.May,Status=false },
                        new BdMonthlyBudget {SubBudgetId=s.Id,BudgetAmount=0,Month=Month.Jun,Status=false },
                        new BdMonthlyBudget {SubBudgetId=s.Id,BudgetAmount=0,Month=Month.Jul,Status=false },
                        new BdMonthlyBudget {SubBudgetId=s.Id,BudgetAmount=0,Month=Month.Aug,Status=false },
                        new BdMonthlyBudget {SubBudgetId=s.Id,BudgetAmount=0,Month=Month.Sept,Status=false },
                        new BdMonthlyBudget {SubBudgetId=s.Id,BudgetAmount=0,Month=Month.Oct,Status=false },
                        new BdMonthlyBudget {SubBudgetId=s.Id,BudgetAmount=0,Month=Month.Nov,Status=false },
                        new BdMonthlyBudget {SubBudgetId=s.Id,BudgetAmount=0,Month=Month.Dec,Status=false },
                    };
                    bds.AddRange(bdMonthlies);
                }
            );
            await queryBdMonthlyBudget.InsertManyAsync(bds);
        }


        public async Task<IEnumerable<Guid>> GetSubbudgetsByOwner(Guid ownerId)
        {
            var querySubBudgetRepository = LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>();
            var querySubBudget = await querySubBudgetRepository.GetQueryableAsync();

            var querySubBudgetMapping = await LazyServiceProvider.LazyGetService<IBdSubBudgetMappingRepository>().GetQueryableAsync();

            var data = querySubBudget.Where(x => x.OwnerId == ownerId).Select(x => x.Id).Concat(querySubBudgetMapping.Where(x => x.UserId == ownerId).Select(x => x.SbuBudgetId).Distinct()).Distinct().ToArray();

            return data;
        }
    }
}