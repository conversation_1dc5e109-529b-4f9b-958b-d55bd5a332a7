CREATE PROCEDURE dbo.SP_vendors
AS 
BEGIN
	--准备工作
select   c.ProcInstId,a.VCMPNY,a.VENDOR into #upid  from PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a 
join PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM b 
on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
join (select *,ROW_NUMBER () over(partition by vendorNumber,company_Value,supplierCNName order by ProcInstId desc) rn from PLATFORM_ABBOTT.dbo.ods_AUTO_BIZ_T_SupplierApplication_Info) c
on cast(VENDOR as nvarchar(255))=cast(vendorNumber as nvarchar(255)) and cast(VCMPNY as nvarchar(255))=cast(company_Value as nvarchar(255)) and cast(VEXTNM as nvarchar(255))=cast(supplierCNName as nvarchar(255)) and c.rn=1
join PLATFORM_ABBOTT.dbo.ods_Form_7a708c9568fb444a884eb5eca658975f d
on c.ProcInstId =d.ProcInstId 
where processstatus=N'终止(系统)' or processstatus=N'已完成';
select   c.ProcInstId,a.VCMPNY,a.VENDOR into #upid1 
from PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a 
join PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM b 
on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
join (select *,ROW_NUMBER () over(partition by supplierCode,supplierCNName order by ProcInstId desc) rn from PLATFORM_ABBOTT.dbo.ods_AUTO_BIZ_T_HcpLevelApplication_info) c
on cast(VENDOR as nvarchar(255))=cast(supplierCode as nvarchar(255)) and cast(VEXTNM as nvarchar(255))=cast(supplierCNName as nvarchar(255)) and c.rn=1 --and cast(VEXTNM as nvarchar(255))=cast(supplierCNName as nvarchar(255))
join PLATFORM_ABBOTT.dbo.ods_Form_663dd63299be45d69dd8f853d0a4b445 d
on c.ProcInstId =d.ProcInstId 
where processstatus=N'终止(系统)' or processstatus=N'已完成';
IF OBJECT_ID(N'dbo.xml_17', N'U') IS NOT NULL
BEGIN
PRINT(N'已经初始化'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN	
--     更新up_id  AttachmentInformation DPSCheck
--SELECT 
--    az.ProcInstId,
--    d.VCMPNY,d.VENDOR,
--    STRING_AGG(up_Id, ', ') WITHIN GROUP (ORDER BY up_Id) AS up_Id
--    into xml_17
--FROM 
--    (
    select ProcInstId,
		up_Id,FORM_Id
--		File_Name,
--		Emp_ID 
	into xml_17
	from (
	select ProcInstId,FORM_Id,
	up_Id.value('.', 'nvarchar(100)') as up_Id 
	from (
		SELECT
		XmlContent,
		ProcInstId,FORM_Id
		FROM PLATFORM_ABBOTT.dbo.ODS_T_FORMINSTANCE_GLOBAL
		where FORM_Id in ('663dd63299be45d69dd8f853d0a4b445','7a708c9568fb444a884eb5eca658975f'))A
		CROSS APPLY XmlContent.nodes('/root/AttachmentInfoGrid/row/up_Id') AS XMLTable(up_Id)) B;
--		join PLATFORM_ABBOTT.dbo.ODS_T_METAFILE tm
--		on B.up_Id=tm.file_id)az
--		join #upid d
--		on az.ProcInstId=d.ProcInstId
--		where File_Name not like N'%身份证%' and  File_Name not like N'%DPS%'
--		GROUP BY  az.ProcInstId,d.VCMPNY,d.VENDOR;
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END;

--drop table xml_17
--select *　 from xml_Vendors
IF OBJECT_ID(N'dbo.xml_Vendors', N'U') IS NOT NULL
BEGIN
PRINT(N'已经初始化'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN	
select 
	ProcInstId,
	ISNULL(XmlContent.value('(/root/SupplierApplication_agentBlock_MainStore/agentEmpId)[1]', 'NVARCHAR(255)'),'1') as agentDeptId,
	trim(XmlContent.value('(/root/SupplierApplication_applicantBaseInfoBlock_MainStore/supplierHCPType)[1]', 'nvarchar(5)')) supplierHCPType,
	XmlContent.value('(/root/SupplierApplication_supplierInfoBlock_MainStore/bankCity)[1]', 'NVARCHAR(100)') bankCity,
	ISNULL(XmlContent.value('(/root/HcpLevelApplication_agentBlock_MainStore/agentEmpId)[1]', 'NVARCHAR(100)'),'1') agentEmpId,
	XmlContent.value('(/root/SupplierApplication_applicantInfoBlock_MainStore/applicantDeptId)[1]', 'nvarchar(50)')  AS applicantDeptId,
	XmlContent.value('(/root/approvalHistoryGrid/row/action)[1]', 'nvarchar(50)')  AS ac_status
into xml_Vendors
from ODS_T_FORMINSTANCE_GLOBAL otfg 
where otfg.FORM_Id in ('663dd63299be45d69dd8f853d0a4b445','7a708c9568fb444a884eb5eca658975f');
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END;
--drop table xml_Vendors


with 
FVM_INFO as (
select *,LTRIM(RTRIM(
					 REPLACE(REPLACE( REPLACE(
									REPLACE(
										      LEFT(a.VLDRM2, 
										   CASE 
											                    WHEN CHARINDEX('SwiftCode', a.VLDRM2) > 0 THEN CHARINDEX('SwiftCode', a.VLDRM2) - 1
										                        WHEN CHARINDEX('Swift Code', a.VLDRM2) > 0 THEN CHARINDEX('Swift Code', a.VLDRM2) - 1
										                        WHEN CHARINDEX('Swift', a.VLDRM2) > 0 THEN CHARINDEX('Swift', a.VLDRM2) - 1
										                        ELSE LEN(a.VLDRM2)
										                    END),
													      '(', ''), -- Remove left parenthesis
													  ')', '')  -- Remove right parenthesis
												,' ',''),'ACCOUNT:',''))
							) BankCardNo from PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM a
),
AVM_info as ( -- 87872 总供应商
SELECT 
	*,
	case -- 逻辑判断 个人和 供应商
		when upper(trim(VTYPE)) ='NHIV' then '1'
		when upper(trim(VTYPE)) ='NLIV' then '2'
		when upper(trim(VTYPE)) ='NH' then '3'
		when upper(trim(VTYPE)) ='NL' then '4'
		when xmlVendorsType ='1' then '1'
		when xmlVendorsType ='2' then '3'
		when xmlVendorsType = '3' then '4'
		when xmlVendorsType = '4' then '2'
		else '4' 
	end as VendorType  
from (
	select 
			a2.* 
--			,sta.BPM_ID  -- 最早用户id
			,c.ProcInstId,
			d.agentDeptId,
			d.agentEmpId,
			--个人类型供应商[VTYPE]=NHIV/NLIV时，以目前BPCS内记录联系方式作为迁移的手机号(11位数字并移除前后空格，若格式不符合条件则留空);机构类型供应商[VTYPE]=NH/NL时，直接记录当前的联系方式作为迁移的号码
			case when VTYPE='NT' 
				then d.supplierHCPType 
			end as xmlVendorsType,
			d.BankCity,
			ISNULL(f.ClientId,e.DoctorCode) as MndId,
			g.spk_codes as ApsPorperty,
			COALESCE(h.LICENSE_NO, a2.VNDAD3) as CertificateCode,
			h.UPD_DATETIME,
			h.INS_DATETIME,
			h.TITLE,
			h.HOS_DEPT,
			h.HOSPITAL,
			x17.up_Id,
			sc.AriseCode,
			x17_1.up_Id as  up_Id1,
			x19.approvalTime,
			case when upper(h.TIER) ='N/A' then '' else h.TIER end as SPLevel,
			case when upper(VNDMEMO02) ='N/A' then ' ' else VNDMEMO02 end as AcademicPosition
--			case 
--				when  b.spk_NexBPMCode is not null  then b.spk_NexBPMCode 
----				else  ISNULL( d.XmlContent.value('(/root/SupplierApplication_agentBlock_MainStore/agentDeptId)[1]', 'NVARCHAR(255)'),'1')
--			end as CreatorId
		from 
		(
			SELECT 
				tt.*
			from 
			(
				SELECT  
					a1.*,
					ROW_NUMBER () over(PARTITION by Trim(NAME),Trim(VMXCRT_NO1) order by vnstat1 desc,
					COALESCE(nullif(vldate1,0),vcrdte1) desc,vcrdte1 desc, COALESCE(nullif(VLTIME1,0),VCTIME)  desc,VENDOR desc ) as rn,
					
					FIRST_value (VMXCRT_NO1) over(PARTITION by Trim(NAME),Trim(VMXCRT_NO1) order by vnstat1 desc,
					COALESCE(nullif(vldate1,0),vcrdte1) desc,vcrdte1 desc, COALESCE(nullif(VLTIME1,0),VCTIME)  desc,VENDOR desc ) as identity_id, 
					
					min(CONCAT(vcrdte1,VCTIME1))over(PARTITION by VMXCRT_NO1 ) as CreationTime,
					
					min(vcrdte1)over(PARTITION by VMXCRT_NO1) as vcrdte1_Min,
					max(CONCAT(vldate1,VLTIME1))over(PARTITION by VMXCRT_NO1 ) as vcrdte1_MAX,
--					min(VCTIME)over(PARTITION by VMXCRT_NO1) as VCTIME_Min,
					FIRST_value(VCUSER)over(PARTITION by VMXCRT_NO1 order by vcrdte1,VCTIME  ) as VCUSER1,  -- 最早用户id
					FIRST_value(VCUSER)over(PARTITION by VMXCRT_NO1 order by vcrdte1 desc,VCTIME desc  ) as VCUSER2
				from
				(
					SELECT a.*,b.VLDRM1,b.VLDRM2,b.BankCardNo,b.VLDATE,b.VLTIME,b.VEXTNM,b.VCRDTE,b.VCTIME ,VCUSER, VMBNKA as BankNo,b.VADD1,b.VADD2,b.VADD3,
						CASE WHEN len(cast(vldate as VARCHAR(255)))<=6 and vldate <> 0 
							 then cast(CONCAT('20',vldate) as int) 
							 WHEN vldate=******** AND VLTIME=173000 AND VLUSER='CNSECOFR1' --[LastModificationTime] 在********系统故障，时间错误，为空
							 THEN 0
							 else vldate end vldate1,
						CASE WHEN len(cast(VCTIME as VARCHAR(255)))<=6 and VCTIME <> 0 
							 then cast(CONCAT('0',VCTIME) as nvarchar(10)) 
							 else VCTIME end VCTIME1,
							 case when vldate=******** AND VLTIME=173000 AND VLUSER='CNSECOFR1' --[LastModificationTime] 在********系统故障，时间错误，为空
							 THEN 0 else VLTIME end VLTIME1,
						CASE WHEN len(cast(VCRDTE as VARCHAR(255)))<=6 and VCRDTE <> 0 
						then cast(CONCAT('20',VCRDTE) as int) 
						else VCRDTE end vcrdte1,
					 	case 
							when upper(trim(VTYPE)) in ('NHIV','NLIV') and VMXCRT  <>''   then  TRIM(REPLACE( REPLACE(VMXCRT,' F','') ,' M',''))   --判断个人供应商
							when upper(trim(VTYPE)) in ('NT') and (VMXCRT is null or VMXCRT  ='' ) AND  BankCardNo  IS NOT NULL THEN BankCardNo
							when upper(trim(VTYPE)) in ('NT') and (BankCardNo is null or BankCardNo  ='')  AND  VMXCRT  IS NOT NULL THEN VMXCRT
							when upper(trim(VTYPE)) in ('NT') and BankCardNo  IS NOT NULL  AND  VMXCRT  IS NOT NULL THEN VMXCRT
						    when VMXCRT is null or VMXCRT  =''  or BankCardNo='' or BankCardNo is null then cast(newid() as nvarchar(255))  
						else  TRIM(BankCardNo)
						end AS VMXCRT_NO1,
						case when upper(vnstat)='A' THEN 1 else 0 end vnstat1,
						COALESCE(a.VNDNAM,b.VEXTNM) as NAME
					from PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a 
					join FVM_INFO b 
						on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
					where [VCMPNY] in (91,79,18,20)  AND trim(VTYPE) in ('NHIV','NLIV','NH','NL','NT')
--							 and TRIM(REPLACE( REPLACE(VMXCRT,' F','') ,' M',''))  in ('130702197011050348'
--								,'652301196912100825',
--								'370283197912077527',
--								'340264197404083221',
--								'650104196202209005',
--								'110108196102113000'
--								)
				) a1 
			) tt  where tt.rn=1  
--			order by rn 
		) a2 
	 	left join  
	 	(
	 		 SELECT  
	  			vendor ,vcmpny,VNDNAM,min(ProcInstId) as ProcInstId -- 基于最小的ProcInstId优先填入最早创建记录对应的员工ID
	  			,max(ProcInstId) as MaxProcInstId
			  from PLATFORM_ABBOTT.dbo.ODS_T_VendorMasterData_Log b1
	--		  where  b1.vendor=11266
			 group by vendor ,vcmpny,VNDNAM
	 	) c
	 	 	on a2.VENDOR=c.VENDOR and a2.VCMPNY=c.VCMPNY  and COALESCE(a2.VNDNAM,a2.VEXTNM)=c.VNDNAM -- and b.spk_NexBPMCode is null
--		left join PLATFORM_ABBOTT.dbo.spk_staffmasterdata sta
--	 		on sta.spk_staffaccount=a2.VCUSER1	 	
		left join  xml_Vendors  d 
			on d.ProcInstId=c.ProcInstId --and b.spk_NexBPMCode is null 
		left join  (
			select *   from (
				SELECT  *, count(DoctorsCode) OVER(PARTITION BY DoctorsCode) AS aa from PLATFORM_ABBOTT.dbo.ODS_T_CRM_UpdateDoctorCodeLog where DoctorsCode='20_12988'
			) aaa  where  aa=1 --  这里 缺一个逻辑处理 DoctorsCode
		) f 
--		on f.DoctorsCode=e.DoctorsCode
		on concat(a2.VCMPNY,'_',a2.VENDOR)=f.DoctorsCode
		left join (
		   	select 
			    ROW_NUMBER() OVER(PARTITION BY b.f_code, a.vendorCode ORDER BY a.ProcInstId DESC) AS RowNum,
			    b.f_code as VCMPNY
			    , a.vendorCode
			    ,a.ProcInstId,
			    DoctorCode
		    from PLATFORM_ABBOTT.dbo.ODS_AUTO_BIZ_T_ProcurementApplication_Info_PR a
		    join PLATFORM_ABBOTT.dbo.ODS_AUTO_BIZ_T_ProcurementApplication_Info b on a.ProcInstId=b.ProcInstId
		    where f_code is not null and vendorCode is not null and vendorCode <>'NULL' and DoctorCode<>'' and DoctorCode<>'NULL'
		) e 
--		 on cast(e.VCMPNY as INT)=a2.VCMPNY and cast(e.vendorCode as INT)= a2.VENDOR  and e.RowNum=1
		 on  e.VCMPNY =cast(a2.VCMPNY as nvarchar(50)) and e.vendorCode  =cast(a2.VENDOR as nvarchar(50)) and e.RowNum=1
		left join 
		(
			select 
				SupplierName,
			    STRING_AGG(spk_code, ',') WITHIN GROUP (ORDER BY spk_code) AS spk_codes
			from 
			(
			    select  
			    	DISTINCT  trim(a.SupplierName) as SupplierName,b.spk_code
				from PLATFORM_ABBOTT.dbo.ODS_T_ASN_Supplier a
			    JOIN PLATFORM_ABBOTT.dbo.spk_dictionary b on a.Id=b.spk_BPMCode
			    group by a.SupplierName,b.spk_code
			) o
			group by SupplierName
		) g
		on  COALESCE(trim(a2.VNDNAM),trim(a2.VEXTNM))=trim(g.SupplierName)  
		left join 
		(
		 	SELECT 
				LICENSE_NO,
				VENDOR,
				VNDNAM,
				TIER,
				case when TITLE ='NULL' then null else TITLE end as TITLE,
				VNDMEMO02,
				UPD_DATETIME,
				INS_DATETIME,
				case when TITLE ='NULL' then null else HOS_DEPT end as HOS_DEPT,
				case when TITLE ='NULL' then null else HOSPITAL end as HOSPITAL,
				row_number() over(partition by VENDOR,VNDNAM order by COALESCE(UPD_DATETIME,INS_DATETIME) desc,LICENSE_NO ASC) as rns
			from PLATFORM_ABBOTT.dbo.ODS_T_VENDOR_TIER	 where VENDOR is not null and VENDOR <>''
		) h
		on  cast(h.VENDOR as int)=a2.VENDOR   and   COALESCE(trim(a2.VNDNAM),trim(a2.VEXTNM)) = trim(h.VNDNAM) and h.rns=1	 
				left join (SELECT 
		    az.ProcInstId,
		    d.VCMPNY,d.VENDOR,
		    STRING_AGG(up_Id, ', ') WITHIN GROUP (ORDER BY up_Id) AS up_Id
		FROM 
		    (
		    select ProcInstId,
				up_Id,
				File_Name,
				Emp_ID 
			from xml_17 B
		join PLATFORM_ABBOTT.dbo.ODS_T_METAFILE tm
		on B.up_Id=tm.file_id)az
		join #upid d
		on az.ProcInstId=d.ProcInstId
		where File_Name not like N'%身份证%' and  File_Name not like N'%DPS%'
		GROUP BY  az.ProcInstId,d.VCMPNY,d.VENDOR)x17
		on a2.VCMPNY=x17.VCMPNY and a2.VENDOR=x17.VENDOR
		left join (SELECT 
		    az.ProcInstId,
		    d.VCMPNY,d.VENDOR,
		    STRING_AGG(up_Id, ', ') WITHIN GROUP (ORDER BY up_Id) AS up_Id
		FROM 
		    (
		    select ProcInstId,
				up_Id,
				File_Name,
				Emp_ID 
			from xml_17 B
		join PLATFORM_ABBOTT.dbo.ODS_T_METAFILE tm
		on B.up_Id=tm.file_id)az
		join #upid1 d
		on az.ProcInstId=d.ProcInstId
		where File_Name not like N'%身份证%' and  File_Name not like N'%DPS%'
		GROUP BY  az.ProcInstId,d.VCMPNY,d.VENDOR) x17_1
		on a2.VCMPNY=x17_1.VCMPNY and a2.VENDOR=x17_1.VENDOR
		left join 
		(select az.ProcInstId,approvalTime,VCMPNY,VENDOR,row_number() over(partition by az.ProcInstId order by az.approvalTime desc) rn 
		from xml_19 az
		join #upid d
		on az.ProcInstId=d.ProcInstId
		where az.approvalLevel =N'财务供应商维护岗审核'
		)x19
		on a2.VCMPNY=x19.VCMPNY and a2.VENDOR=x19.VENDOR	and x19.rn=1
		left join  PLATFORM_ABBOTT.dbo.ods_T_SupplierAriseCode  sc
		on a2.VCMPNY=sc.CompanyCode and a2.VENDOR=sc.SupplierNo
	) a3
)
select 
 TRIM(REPLACE( REPLACE(a.VMXCRT,' F','') ,' M','')) as VMXCRT --辅助字段，用于关联
,a.VCMPNY as VCMPNY  --辅助字段，用于关联
,a.VENDOR as VENDOR  --辅助字段，用于关联
,a.VTYPE as VTYPE     -- 辅助字段,用于后续验证
,a.identity_id
,newid() as Id
,'00000000-0000-0000-0000-000000000000' as ApplicationId
,'SP'+cast(RIGHT(a.vcrdte1_Min,6) as varchar(8))+RIGHT('0000' + CAST((ROW_NUMBER() OVER (PARTITION BY a.vcrdte1_Min ORDER BY CreationTime)) AS VARCHAR(4)), 4)  as VendorCode
,cast(null as nvarchar(255)) as OpenId
,cast(null as nvarchar(255)) as UnionId
,case 
	when a.VendorType in ('1','2') and (len(TRIM(VPHONE))=11 or len(TRIM(VPHONE))=14) then TRIM(VPHONE)  -- 个人供应商判断
	when a.VendorType in ('3','4') then TRIM(VPHONE)  -- 机构供应商判断
 end HandPhone --- 目前电话 逻辑可能有问题
,a.VendorType as VendorType
,cast(null as nvarchar(255)) as BuCode
,case 
	when [VNSTAT] ='A' THEN 2 
	WHEN [VNSTAT] ='D' THEN 3
end [Status]
,AriseCode as EpdId
,a.MndId as MndId
,cast(null as nvarchar(255)) as VendorId
,case when VendorType ='4' then a.ApsPorperty end as ApsPorperty  -- 仅适用于类型=4，其他类型留空
,case when VendorType ='1' then a.CertificateCode end as CertificateCode
,case when VendorType ='1' then a.SPLevel end as SPLevel 
,cast(null as nvarchar(255)) as AcademicLevel
,null as AcademicPosition
,a.VLDRM1   as BankCode
,a.BankCardNo  
,up.BankCity as BankCity  -- 需要再次 关联字典表
,a.BankNo
,N'{}' as ExtraProperties
,cast(null as nvarchar(255)) as ConcurrencyStamp
,case when len(a.CreationTime)=14 
		then a.CreationTime 
		else CONCAT( SUBSTRING(a.CreationTime,1,8) ,'0',SUBSTRING(a.CreationTime,9,13)) end CreationTime
,COALESCE(sta.BPM_ID,a.agentDeptId,a.agentEmpId) as  CreatorId
,case when vcrdte1_MAX='00' then STUFF(STUFF(a.approvalTime, 5, 1, '-'), 8, 1, '-') 
	when vcrdte1_MAX is not null and len(a.vcrdte1_MAX)=14  then  vcrdte1_MAX 
	when vcrdte1_MAX is not null and len(a.vcrdte1_MAX)=13  then  CONCAT( SUBSTRING(a.vcrdte1_MAX,1,8) ,'0',SUBSTRING(a.vcrdte1_MAX,9,13))
	when vcrdte1_MAX is null then 
		case when len(a.approvalTime)=14 then a.approvalTime
		else   a.approvalTime end 
		end  as LastModificationTime
,COALESCE(sta1.BPM_ID,up.agentDeptId,up.agentEmpId) as LastModifierId
,0 as IsDeleted
,null as DeleterId
,null as DeletionTime
,null as UserId
,case when VendorType='1' then COALESCE(TITLE,VNDAD2) 
 end as PTId
,case when VendorType='1' then COALESCE(HOS_DEPT,VADD3)
 end as StandardHosDepId
,case when VendorType='1' and HOSPITAL is not null then HOSPITAL
 	  when VendorType='1' and VADD3 is null then VADD1
 	  when VendorType='1' and VADD3 is not null then  CONCAT(VADD1,VADD2)
 end as HospitalId
,case when VendorType='1' then COALESCE(HOS_DEPT,VADD3)
 end as HosDepartment
,up_id  as AttachmentInformation
,cast(null as nvarchar(255)) as Description
,1 as DraftVersion
,cast(null as nvarchar(255)) as PaymentTerm
,cast(null as nvarchar(255)) as BankCardImg
,cast(null as nvarchar(255)) as DPSCheck
,0 as SignedStatus
,cast(null as nvarchar(255)) as SignedVersion
,case when VendorType='1' and UPD_DATETIME is not null then HOSPITAL
 	  when VendorType='1' and UPD_DATETIME is null  and INS_DATETIME is not null then HOSPITAL
 	  when VendorType='1' and VADD3 is null then VADD1
 	  when VendorType='1' and VADD3 is not null then  CONCAT(VADD1,VADD2)
 end as HospitalName
,case when VendorType='1' and UPD_DATETIME is not null then TITLE
 	  when VendorType='1' and UPD_DATETIME is null  and INS_DATETIME is not null then TITLE
 	  when VendorType='1' then  VNDAD2
 end as PTName
,case when VendorType='1' and UPD_DATETIME is not null then HOS_DEPT
 	  when VendorType='1' and UPD_DATETIME is null  and INS_DATETIME is not null then HOS_DEPT
 	  when VendorType='1' then  VADD3
 end as  StandardHosDepName
,REPLACE
	(REPLACE
		(REPLACE
			(REPLACE
				(REPLACE
					( RIGHT(a.VLDRM2, 
		                        CASE 
		                            WHEN CHARINDEX('Swift', a.VLDRM2) > 0 THEN LEN(a.VLDRM2) - CHARINDEX('Swift', a.VLDRM2)
		                            WHEN CHARINDEX('SwiftCode', a.VLDRM2) > 0 THEN LEN(a.VLDRM2) - CHARINDEX('SwiftCode', a.VLDRM2)
		                            WHEN CHARINDEX('Swift Code', a.VLDRM2) > 0 THEN LEN(a.VLDRM2) - CHARINDEX('Swift Code', a.VLDRM2)
--		                            ELSE LEN(a.VLDRM2)
		            			END)
		            	,' ','')
		        ,':','')
		 ,')','')
	,'wiftcode','')
,'wift','') as BankSwiftCode
,0 as IsAcademician
,case when VendorType ='1' then case when a.AcademicPosition='NULL' then null else a.AcademicPosition end  end  as FormerBPMAcademicPosition
,cast(null as nvarchar(255)) as HCPType
,cast(null as nvarchar(255)) as RelationType
into #vendor_tb4
from AVM_Info as A
left join PLATFORM_ABBOTT.dbo.spk_staffmasterdata sta
on sta.spk_staffaccount=a.VCUSER1
left join PLATFORM_ABBOTT.dbo.spk_staffmasterdata sta1
on sta1.spk_staffaccount=a.VCUSER2
left join (
select t1.*,up1.VCMPNY,up1.VENDOR,ROW_NUMBER () over(partition by up1.VCMPNY,up1.VENDOR order by t1.ProcInstId) rn from xml_Vendors t1 
		join  #upid	up1 on t1.ProcInstId=up1.ProcInstId	
)up
on  a.VCMPNY=up.VCMPNY and a.VENDOR=up.VENDOR
--left join (	
--) b1 
--on a.VCMPNY=b1.VCMPNY and a.VENDOR=b1.VENDOR;
--where VendorType=1 and len(VMXCRT)<20
--order by VendorType

--drop table #vendor_tb4


    --删除表
    IF OBJECT_ID(N'dbo.Vendor_Tmp ', N'U') IS NOT NULL
	BEGIN
		
--		ALTER TABLE PLATFORM_ABBOTT.dbo.Vendor_Tmp ALTER COLUMN ApplicationId varchar(36) COLLATE Chinese_PRC_CI_AS NULL;

		UPDATE a
		SET 
		 a.VMXCRT = b.VMXCRT
		,a.ApplicationId = b.ApplicationId
--		,a.VendorCode = b.VendorCode
		,a.identity_id=cast(b.identity_id as Nvarchar(100))
		,a.OpenId = b.OpenId
		,a.UnionId = b.UnionId
		,a.HandPhone = b.HandPhone
		,a.VendorType = b.VendorType
		,a.BuCode = b.BuCode
		,a.Status = b.Status
		,a.EpdId = b.EpdId
		,a.MndId = b.MndId
		,a.VendorId = b.VendorId
		,a.ApsPorperty = b.ApsPorperty
		,a.CertificateCode = b.CertificateCode
		,a.SPLevel = b.SPLevel
		,a.AcademicLevel = b.AcademicLevel
		,a.AcademicPosition = b.AcademicPosition
		,a.BankCode = b.BankCode
		,a.BankCardNo = b.BankCardNo
		,a.BankCity = b.BankCity
		,a.BankNo = b.BankNo
		,a.ExtraProperties = b.ExtraProperties
		,a.ConcurrencyStamp = b.ConcurrencyStamp
		,a.CreationTime = b.CreationTime
		,a.CreatorId = b.CreatorId
		,a.LastModificationTime = b.LastModificationTime
		,a.LastModifierId = b.LastModifierId
		,a.IsDeleted = b.IsDeleted
		,a.DeleterId = b.DeleterId
		,a.DeletionTime = b.DeletionTime
		,a.UserId = b.UserId
		,a.PTId = b.PTId
		,a.StandardHosDepId = b.StandardHosDepId
		,a.HospitalId = b.HospitalId
		,a.HosDepartment = b.HosDepartment
		,a.AttachmentInformation = b.AttachmentInformation
		,a.Description = b.Description
		,a.DraftVersion = b.DraftVersion
		,a.PaymentTerm = b.PaymentTerm
		,a.BankCardImg = b.BankCardImg
		,a.DPSCheck = b.DPSCheck
		,a.SignedStatus = b.SignedStatus
		,a.SignedVersion = b.SignedVersion
		,a.HospitalName = b.HospitalName
		,a.PTName = b.PTName
		,a.StandardHosDepName = b.StandardHosDepName
		,a.[BankSwiftCode]=b.BankSwiftCode
		,a.[IsAcademician]=b.IsAcademician
		,a.[FormerBPMAcademicPosition]=b.FormerBPMAcademicPosition
		,a.[HCPType]=b.HCPType
		,a.[RelationType]=b.RelationType
		FROM dbo.Vendor_Tmp a
		left JOIN #vendor_tb4 b
		ON a.VCMPNY = b.VCMPNY AND a.VENDOR = b.VENDOR

		
		INSERT INTO PLATFORM_ABBOTT.dbo.Vendor_Tmp
		SELECT
		 a.VMXCRT
		,a.VCMPNY
		,a.VENDOR
		,a.VTYPE
		,a.identity_id
		,a.Id
		,a.ApplicationId
		,a.VendorCode
		,a.OpenId
		,a.UnionId
		,a.HandPhone
		,a.VendorType
		,a.BuCode
		,a.Status
		,a.EpdId
		,a.MndId
		,a.VendorId
		,a.ApsPorperty
		,a.CertificateCode
		,a.SPLevel
		,a.AcademicLevel
		,a.AcademicPosition
		,a.BankCode
		,a.BankCardNo
		,a.BankCity
		,a.BankNo
		,a.ExtraProperties
		,a.ConcurrencyStamp
		,a.CreationTime
		,a.CreatorId
		,a.LastModificationTime
		,a.LastModifierId
		,a.IsDeleted
		,a.DeleterId
		,a.DeletionTime
		,a.UserId
		,a.PTId
		,a.StandardHosDepId
		,a.HospitalId
		,a.HosDepartment
		,a.AttachmentInformation
		,a.Description
		,a.DraftVersion
		,a.PaymentTerm
		,a.BankCardImg
		,a.DPSCheck
		,a.SignedStatus
		,a.SignedVersion
		,a.HospitalName
		,a.PTName
		,a.StandardHosDepName
		,a.[BankSwiftCode]
		,a.[IsAcademician]
		,a.[FormerBPMAcademicPosition]
		,a.[HCPType]
		,a.[RelationType]
		FROM #vendor_tb4 a
		WHERE NOT EXISTS (SELECT * FROM dbo.Vendor_Tmp WHERE VCMPNY=a.VCMPNY AND VENDOR=a.VENDOR)
		
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into dbo.Vendor_Tmp from #vendor_tb4
    -- select * from #vendor_tbl
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	
END


