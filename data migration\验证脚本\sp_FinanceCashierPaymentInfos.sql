CREATE PROCEDURE dbo.sp_FinanceCashierPaymentInfos
AS 
BEGIN
	select 
newid() AS Id,--自动生成的uuid
id as Bpm_id,
RefNo AS PAApplicationCode,--以该编码到T_AP_REF查询到对应的PAFormCode
RefNo AS ApplyUserId,--按照PAApplicationCode，查询PurPAApplications.ApplicationCode，填入对应记录的ApplyUserId
RefNo AS ApplyUserName,--按照PAApplicationCode，查询PurPAApplications.ApplicationCode，填入对应记录的ApplyUserName
RefNo AS ApplyUserEmail,--按照PAApplicationCode，查询PurPAApplications.ApplicationCode，基于对应记录的ApplyUserId，查询到用户表中该用户的邮箱填入
RefNo AS ApplyTime,--按照PAApplicationCode，查询PurPAApplications.ApplicationCode，填入对应记录的ApplyTime
RefNo AS CompanyId,--按照PAApplicationCode，查询PurPAApplications.ApplicationCode，填入对应记录的CompanyId
RefNo AS CompanyName,--按照PAApplicationCode，查询PurPAApplications.ApplicationCode，填入对应记录的CompanyName
RefNo AS ApplyUserBu,--按照PAApplicationCode，查询PurPAApplications.ApplicationCode，填入对应记录的ApplyUserBu
RefNo AS ApplyUserBuName,--按照PAApplicationCode，查询PurPAApplications.ApplicationCode，填入对应记录的ApplyUserBuName
RefNo AS CostCenterName,--按照PAApplicationCode，查询PurPAApplications.ApplicationCode，根据查询出的PRId查询PurPRApplications.ID后填入CostCenterName
VendorName AS VendorName,--
VBankName AS BankName,--
VBankCode AS BankCardNo,--
case 
	when RemoteOrCity in ('51',N'同城') then N'同城'
	when RemoteOrCity in ('52',N'异地') then N'异地'
end AS RemoteOrCity,--51-同城；52-异地
PaymentAmount AS PaymentAmount,--
RefNo AS RefNo,--
'NULL' AS MPDate,--留空
'NULL' AS MPStatus,--留空
PaymentDate AS PaymentDate,--
ReturnDate AS RetureDate,--
Remark AS Remark,--
'{}' AS ExtraProperties,--默认填写为"{}"
'NULL' AS ConcurrencyStamp,--?
PaymentDate AS CreationTime,--填写为付款日期
'1' AS CreatorId,--无法找到记录，填写为默认的admin账号
'NULL' AS LastModificationTime,--默认为空
'NULL' AS LastModifierId,--默认为空
'0' AS IsDeleted,--默认为0
'NULL' AS DeleterId,--默认为空
'NULL' AS DeletionTime--默认为空
into #FinanceCashierPaymentInfos_tmp
from PLATFORM_ABBOTT.dbo.ods_T_Ebanking_Payment_Info a


 IF OBJECT_ID(N'PLATFORM_ABBOTT.dbo.FinanceCashierPaymentInfos_tmp ', N'U') IS NOT NULL
	BEGIN
		update a 
		set a.Bpm_id               = b.Bpm_id
           ,a.PAApplicationCode    = b.PAApplicationCode
           ,a.ApplyUserId          = b.ApplyUserId
           ,a.ApplyUserName        = b.ApplyUserName
           ,a.ApplyUserEmail       = b.ApplyUserEmail
           ,a.ApplyTime            = b.ApplyTime
           ,a.CompanyId            = b.CompanyId
           ,a.CompanyName          = b.CompanyName
           ,a.ApplyUserBu          = b.ApplyUserBu
           ,a.ApplyUserBuName      = b.ApplyUserBuName
           ,a.CostCenterName       = b.CostCenterName
           ,a.VendorName           = b.VendorName
           ,a.BankName             = b.BankName
           ,a.BankCardNo           = b.BankCardNo
           ,a.RemoteOrCity         = b.RemoteOrCity
           ,a.PaymentAmount        = b.PaymentAmount
           ,a.RefNo                = b.RefNo
           ,a.MPDate               = b.MPDate
           ,a.MPStatus             = b.MPStatus
           ,a.PaymentDate          = b.PaymentDate
           ,a.RetureDate           = b.RetureDate
           ,a.Remark               = b.Remark
           ,a.ExtraProperties      = b.ExtraProperties
           ,a.ConcurrencyStamp     = b.ConcurrencyStamp
           ,a.CreationTime         = b.CreationTime
           ,a.CreatorId            = b.CreatorId
           ,a.LastModificationTime = b.LastModificationTime
           ,a.LastModifierId       = b.LastModifierId
           ,a.IsDeleted            = b.IsDeleted
           ,a.DeleterId            = b.DeleterId
           ,a.DeletionTime         = b.DeletionTime
       from PLATFORM_ABBOTT.dbo.FinanceCashierPaymentInfos_tmp a 
       left join #FinanceCashierPaymentInfos_tmp b on a.Bpm_id = b.Bpm_id
       
       insert into PLATFORM_ABBOTT.dbo.FinanceCashierPaymentInfos_tmp 
       select a.Id
             ,a.Bpm_id
             ,a.PAApplicationCode
             ,a.ApplyUserId
             ,a.ApplyUserName
             ,a.ApplyUserEmail
             ,a.ApplyTime
             ,a.CompanyId
             ,a.CompanyName
             ,a.ApplyUserBu
             ,a.ApplyUserBuName
             ,a.CostCenterName
             ,a.VendorName
             ,a.BankName
             ,a.BankCardNo
             ,a.RemoteOrCity
             ,a.PaymentAmount
             ,a.RefNo
             ,a.MPDate
             ,a.MPStatus
             ,a.PaymentDate
             ,a.RetureDate
             ,a.Remark
             ,a.ExtraProperties
             ,a.ConcurrencyStamp
             ,a.CreationTime
             ,a.CreatorId
             ,a.LastModificationTime
             ,a.LastModifierId
             ,a.IsDeleted
             ,a.DeleterId
             ,a.DeletionTime
    from #FinanceCashierPaymentInfos_tmp a
    where not exists (select * from PLATFORM_ABBOTT.dbo.FinanceCashierPaymentInfos_tmp where Bpm_id = a.Bpm_id)
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into PLATFORM_ABBOTT.dbo.FinanceCashierPaymentInfos_tmp from #FinanceCashierPaymentInfos_tmp
    -- select * from #vendor_tbl
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
END;

