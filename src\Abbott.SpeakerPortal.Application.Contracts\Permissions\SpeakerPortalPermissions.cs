﻿using DocumentFormat.OpenXml.Drawing;

using System.Runtime.CompilerServices;

namespace Abbott.SpeakerPortal.Permissions;

public static class SpeakerPortalPermissions
{
    //一级菜单
    public const string PurchaseManagement = "Purchase";

    public const string VendorManagement = "Vendor";

    public const string TaskCenterManagement = "TaskCenter";

    public const string BudgetManagement = "Budget";

    public const string ComplianceManagement = "Compliance";

    public const string SystemManagement = "System";

    public const string ReportManagement = "Report";

    public const string CroosBuManagement = "CrossBu";

    public const string STicketManagement = "STicket";

    public const string FocManagement = "Foc";

    public const string ReturnManagement = "Return";

    public const string ExchangeManagement = "Exchange";

    public const string PurchaseAgreementManagement = "PurchaseAgreement";

    public const string ConcurManagement = "Concur";

    /// <summary>
    /// 采购
    /// </summary>
    public static class Purchase
    {
        //二级菜单 采购列表
        public const string Application = PurchaseManagement + ".Application";
        //操作
        public const string ApplicationRead = Application + ".Read";
        public const string ApplicationCRUD = Application + ".CRUD";
        public const string ApplicationExport = Application + ".Export";
        public const string ApplicationPrint = Application + ".Print";

        //二级菜单 采购订单
        public const string Order = PurchaseManagement + ".Order";
        //操作
        public const string OrderRead = Order + ".Read";
        public const string OrderCRUD = Order + ".CRUD";
        public const string OrderExport = Order + ".Export";
        public const string OrderPrint = Order + ".Print";

        //二级菜单 收货列表
        public const string Receipt = PurchaseManagement + ".Receipt";
        //操作
        public const string ReceiptRead = Receipt + ".Read";
        public const string ReceiptReturn = Receipt + ".Return";
        public const string ReceiptPrint = Receipt + ".Print";

        //二级菜单 付款列表
        public const string Payment = PurchaseManagement + ".Payment";
        //操作
        public const string PaymentRead = Payment + ".Read";
        public const string PaymentCRUD = Payment + ".CRUD";
        public const string PaymentPrint = Payment + ".Print";
        public const string PaymentExport = Payment + ".Export";

        //二级菜单 财务出纳支付管理
        public const string Finance = PurchaseManagement + ".Finance";
        //操作
        public const string FinanceCRUD = Finance + ".CRUD";
        public const string FinanceExport = Finance + ".Export";

        //二级菜单 供应商比价 
        public const string Bidding = PurchaseManagement + ".Bidding";
        //操作
        public const string BiddingCRUD = Bidding + ".CRUD";
        public const string BiddingRead = Bidding + ".Read";
        public const string BiddingExport = Bidding + ".Export";
        public const string BiddingPrint = Bidding + ".Print";

        //二级菜单 Justification
        public const string Justification = PurchaseManagement + ".Justification";
        //操作
        public const string JustificationRead = Justification + ".Read";
        public const string JustificationCRUD = Justification + ".CRUD";
        public const string JustificationExport = Justification + ".Export";
        public const string JustificationPrint = Justification + ".Print";

        //二级菜单 竞价豁免
        public const string BiddingExemption = PurchaseManagement + ".BiddingExemption";
        //操作
        public const string BiddingExemptionRead = BiddingExemption + ".Read";
        public const string BiddingExemptionCRUD = BiddingExemption + ".CRUD";
        public const string BiddingExemptionExport = BiddingExemption + ".Export";
        public const string BiddingExemptionPrint = BiddingExemption + ".Print";

        //第三方讲者
        public const string Tripartite = PurchaseManagement + ".Tripartite";
        //操作
        public const string TripartiteRead = Tripartite + ".Read";
        public const string TripartiteCRUD = Tripartite + ".CRUD";
        public const string TripartiteExport = Tripartite + ".Export";
    }

    /// <summary>
    /// 供应商
    /// </summary>
    public static class Vendor
    {
        //二级菜单 讲者列表
        public const string Speaker = VendorManagement + ".Speaker";
        //操作
        public const string SpeakerChange = Speaker + ".Change";
        public const string SpeakerActivate = Speaker + ".Activate";
        public const string SpeakerCompliance = Speaker + ".Compliance";
        public const string SpeakerExport = Speaker + ".Export";
        public const string SpeakerRead = Speaker + ".Read";
        public const string SpeakerCRUD = Speaker + ".CRUD";
        public const string SpeakerBlackList = Speaker + ".BlackList";

        //二级菜单 非HCP个人
        public const string NoneSpeaker = VendorManagement + ".NoneSpeaker";
        //操作
        public const string NoneSpeakerChange = NoneSpeaker + ".Change";
        public const string NoneSpeakerActivate = NoneSpeaker + ".Activate";
        public const string NoneSpeakerRead = NoneSpeaker + ".Read";
        public const string NoneSpeakerCRUD = NoneSpeaker + ".CRUD";
        public const string NoneSpeakerExport = NoneSpeaker + ".Export";

        //二级菜单 HCI机构
        public const string HCI = VendorManagement + ".HCI";
        //操作
        public const string HCIChange = HCI + ".Change";
        public const string HCIActivate = HCI + ".Activate";
        public const string HCIRead = HCI + ".Read";
        public const string HCICRUD = HCI + ".CRUD";
        public const string HCIExport = HCI + ".Export";
        public const string HCIBlackList = HCI + ".BlackList";

        //二级菜单 非HCI机构
        public const string NoneHCI = VendorManagement + ".NoneHCI";
        //操作
        public const string NoneHCIChange = NoneHCI + ".Change";
        public const string NoneHCIActivate = NoneHCI + ".Activate";
        public const string NoneHCIRead = NoneHCI + ".Read";
        public const string NoneHCICRUD = NoneHCI + ".CRUD";
        public const string NoneHCIExport = NoneHCI + ".Export";
        public const string NoneHCIAPSEdit = NoneHCI + ".APSEdit";

        //二级菜单 供应商查询
        public const string Query = VendorManagement + ".Query";
        //操作(导出DPS Check信息)
        public const string QueryExport = Query + ".Export";
        //供应商删除(导入待删除供应商)
        public const string QueryDelete = Query + ".Delete";

        //二级菜单 供应商申请查询
        public const string ApplicationQuery = VendorManagement + ".ApplicationQuery";
        public const string ApplicationQueryDelete = ApplicationQuery + ".Delete";
    }

    /// <summary>
    /// 任务中心
    /// </summary>
    public static class TaskCenter
    {
        //二级菜单 我发起的
        public const string Launch = TaskCenterManagement + ".Launch";
        #region Tab
        //讲者新建
        public const string LaunchSpeakerCreate = Launch + ".SpeakerCreate";
        //讲者变更
        public const string LaunchSpeakerChange = Launch + ".SpeakerChange";
        //讲者激活
        public const string LaunchSpeakerActivate = Launch + ".SpeakerActivate";
        //供应商新建
        public const string LaunchVendorCreate = Launch + ".VendorCreate";
        //供应商变更
        public const string LaunchVendorChange = Launch + ".VendorChange";
        //供应商激活
        public const string LaunchVendorActivate = Launch + ".VendorActivate";
        //采购申请
        public const string LaunchProcureApplication = Launch + ".ProcureApplication";
        //竞价豁免
        public const string LaunchBidding = Launch + ".Bidding";
        //Justification
        public const string LaunchJustification = Launch + ".Justification";
        //供应商比价
        public const string LaunchVendorComparision = Launch + ".VendorComparision";
        //采购订单
        public const string LaunchProcureOrder = Launch + ".ProcureOrder";
        //收货申请
        public const string LaunchReceiptApplication = Launch + ".ReceiptApplication";
        //付款申请
        public const string LaunchPaymentApplication = Launch + ".PaymentApplication";
        //撤回/作废/重新提交等
        public const string LaunchAction = Launch + ".Action";
        //导出
        public const string LaunchExport = Launch + ".Export";
        //打印
        public const string LaunchPrint = Launch + ".Print";
        //讲者授权
        public const string SpeakerAuthLaunch = Launch + ".SpeakerAuth";
        //核销申请
        public const string LaunchSTicketApplication = Launch + ".STicketApplication";
        //FOC申请
        public const string LaunchFocApplication = Launch + ".FocApplication";

        //退货
        public const string LaunchReturnApplication = Launch + ".ReturnApplication";

        //换货
        public const string LaunchExchangeApplication = Launch + ".ExchangeApplication";
        #endregion

        //二级菜单 我审批的
        public const string Approve = TaskCenterManagement + ".Approve";
        #region Tab
        //讲者新建
        public const string ApproveSpeakerCreate = Approve + ".SpeakerCreate";
        //讲者变更
        public const string ApproveSpeakerChange = Approve + ".SpeakerChange";
        //讲者激活
        public const string ApproveSpeakerActivate = Approve + ".SpeakerActivate";
        //供应商新建
        public const string ApproveVendorCreate = Approve + ".VendorCreate";
        //供应商变更
        public const string ApproveVendorChange = Approve + ".VendorChange";
        //供应商激活
        public const string ApproveVendorActivate = Approve + ".VendorActivate";
        //采购申请
        public const string ApproveProcureApplication = Approve + ".ProcureApplication";
        //竞价豁免
        public const string ApproveBidding = Approve + ".Bidding";
        //Justification
        public const string ApproveJustification = Approve + ".Justification";
        //供应商比价
        public const string ApproveVendorComparision = Approve + ".VendorComparision";
        //采购订单
        public const string ApproveProcureOrder = Approve + ".ProcureOrder";
        //收货申请
        public const string ApproveReceiptApplication = Approve + ".ReceiptApplication";
        //付款申请
        public const string ApprovePaymentApplication = Approve + ".PaymentApplication";
        //同意/拒绝/退回等
        public const string ApproveAction = Approve + ".Action";
        //导出
        public const string ApproveExport = Approve + ".Export";
        //打印
        public const string ApprovePrint = Approve + ".Print";
        //讲者授权
        public const string SpeakerAuthApprove = Approve + ".SpeakerAuth";

        //批量财务复审
        public const string BatchPaymentApproval = Approve + ".BatchPaymentApproval";
        //核销申请
        public const string ApproveSTicketApplication = Approve + ".STicketApplication";
        //FOC申请
        public const string ApproveFocApplication = Approve + ".FocApplication";
        //退货
        public const string ApproveReturnApplication = Approve + ".ReturnApplication";
        //换货
        public const string ApproveExchangeApplication = Approve + ".ExchangeApplication";
        #endregion

        //二级菜单 付款审批任务
        public const string Payment = TaskCenterManagement + ".Payment";
        //分单
        public const string PaymentSplitOrder = Payment + ".SplitOrder";
        //导出
        public const string PaymentExport = Payment + ".Export";

        //二级菜单 我的采购推送
        public const string ProcurePush = TaskCenterManagement + ".ProcurePush";
        //发起Bidding
        public const string ProcurePushBidding = ProcurePush + ".Bidding";
        //发起PO
        public const string ProcurePushPO = ProcurePush + ".PO";
        //退回
        public const string ProcurePushReturn = ProcurePush + ".Return";
        //导出
        public const string ProcurePushExport = ProcurePush + ".Export";

        //二级菜单 任务代理
        public const string TaskProxy = TaskCenterManagement + ".TaskProxy";
        public const string TaskProxyCRUD = TaskProxy + ".CRUD";
    }

    /// <summary>
    /// 预算管理
    /// </summary>
    public static class Budget
    {
        //二级菜单 主预算管理
        public const string Main = BudgetManagement + ".Main";
        #region 操作
        //查看
        public const string MainRead = Main + ".Read";
        //增删改
        public const string MainCRUD = Main + ".CRUD";
        //冻结
        public const string MainFreeze = Main + ".Freeze";
        //启用
        public const string MainEnable = Main + ".Enable";
        //调整
        public const string MainAdjust = Main + ".Adjust";
        //调拨
        public const string MainTransfer = Main + ".Transfer";
        //导出
        public const string MainExport = Main + ".Export";
        #endregion

        //二级菜单 子预算管理
        public const string Child = BudgetManagement + ".Child";
        #region 操作
        //查看
        public const string ChildRead = Child + ".Read";
        //增删改查
        public const string ChildCRUD = Child + ".CRUD";
        //冻结
        public const string ChildFreeze = Child + ".Freeze";
        //启用
        public const string ChildEnable = Child + ".Enable";
        //调整
        public const string ChildAdjust = Child + ".Adjust";
        //调拨
        public const string ChildTransfer = Child + ".Transfer";
        //Mapping
        public const string ChildMapping = Child + ".Mapping";
        //导出
        public const string ChildExport = Child + ".Export";
        #endregion

        //二级菜单 FOC主预算管理
        public const string FocMain = BudgetManagement + ".FocMain";
        #region 操作
        //查看
        public const string FocMainRead = FocMain + ".Read";
        //增删改
        public const string FocMainCRUD = FocMain + ".CRUD";
        //冻结
        public const string FocMainFreeze = FocMain + ".Freeze";
        //启用
        public const string FocMainEnable = FocMain + ".Enable";
        //调整
        public const string FocMainAdjust = FocMain + ".Adjust";
        //导出
        public const string FocMainExport = FocMain + ".Export";
        #endregion

        //二级菜单 FOC子预算管理
        public const string FocChild = BudgetManagement + ".FocChild";
        #region 操作
        //查看
        public const string FocChildRead = FocChild + ".Read";
        //增删改查
        public const string FocChildCRUD = FocChild + ".CRUD";
        //冻结
        public const string FocChildFreeze = FocChild + ".Freeze";
        //启用
        public const string FocChildEnable = FocChild + ".Enable";
        //调整
        public const string FocChildAdjust = FocChild + ".Adjust";
        //导出
        public const string FocChildExport = FocChild + ".Export";
        #endregion
    }

    /// <summary>
    /// 合规管理
    /// </summary>
    public static class Compliance
    {
        //二级菜单 采购列表
        public const string SpeakerLevel = ComplianceManagement + ".SpeakerLevel";
        //操作
        public const string SpeakerLevelCRUD = SpeakerLevel + ".CRUD";

        //二级菜单 计酬标准配置
        public const string Compensation = ComplianceManagement + ".Compensation";
        public const string CompensationCRUD = Compensation + ".CRUD";
        public const string CompensationRead = Compensation + ".Read";

        //二级菜单 黑名单管理
        public const string BlackList = ComplianceManagement + ".BlackList";
        public const string BlackListCRUD = BlackList + ".CRUD";

        //二级菜单 PSA上限管理
        public const string PSALimit = ComplianceManagement + ".PSALimit";
        public const string PSALimitRead = PSALimit + ".Read";
        public const string PSALimitCRUD = PSALimit + ".CRUD";
        public const string PSALimitCommonRead = PSALimit + ".CommonRead";
        public const string PSALimitCommonCRUD = PSALimit + ".CommonCRUD";

        //二级菜单 单据拦截
        public const string Interception = ComplianceManagement + ".Interception";
        public const string InterceptionCRUD = Interception + ".CRUD";
        public const string InterceptionExport = Interception + ".Export";
        public const string InterceptionReturn = Interception + ".Return";
        public const string InterceptionRelease = Interception + ".Release";
        public const string InterceptionBatchAction = Interception + ".BatchAction";

        //二级菜单 讲者授权申请
        public const string SpeakerAuth = ComplianceManagement + ".Authorization";
        public const string SpeakerAuthCRUD = SpeakerAuth + ".CRUD";
        public const string SpeakerAuthActivate = SpeakerAuth + ".Activate";
        public const string SpeakerAuthRead = SpeakerAuth + ".Read";
    }

    /// <summary>
    /// 系统配置
    /// </summary>
    public static class System
    {
        #region 角色管理
        public const string Role = SystemManagement + ".Role";
        public const string RoleCRUD = Role + ".CRUD";
        #endregion


        #region 员工用户
        public const string User = SystemManagement + ".User";
        public const string UserCRUD = User + ".CRUD";
        #endregion

        #region 采购品类
        public const string Category = SystemManagement + ".Category";
        public const string CategoryCRUD = Category + ".CRUD";
        #endregion

        #region 幻灯片管理
        public const string Slideshow = SystemManagement + ".Slideshow";
        public const string SlideshowCRUD = Slideshow + ".CRUD";
        public const string SlideshowExport = Slideshow + ".Export";
        #endregion

        #region 财务复审流转配置
        public const string FinancialReview = SystemManagement + ".FinancialReview";
        public const string FinancialReviewCRUD = FinancialReview + ".CRUD";
        public const string FinancialReviewExport = FinancialReview + ".Export";
        #endregion
    }
    /// <summary>
    /// 报表管理
    /// </summary>
    public static class Report
    {
        //二级菜单 主预算管理
        public const string WholeProcess = ReportManagement + ".WholeProcess";
        public const string WholeProcessExport = WholeProcess + ".Export";
        public const string WholeProcessPickApplyUser = WholeProcess + ".PickApplyUser";

        public const string PRAccrual = ReportManagement + ".PRAccrual";
        public const string PRAccrualExport = PRAccrual + ".Export";

        public const string EPDPurchase = ReportManagement + ".EPDPurchase";
        public const string EPDPurchaseExport = EPDPurchase + ".Export";
        public const string EPDPurchasePickApplyUser = EPDPurchase + ".PickApplyUser";

        public const string ProfessionalServices = ReportManagement + ".ProfessionalServices";
        public const string ProfessionalServicesExport = ProfessionalServices + ".Export";

        public const string MasterBudget = ReportManagement + ".MasterBudget";
        public const string MasterBudgetExport = MasterBudget + ".Export";

        public const string SubBudget = ReportManagement + ".SubBudget";
        public const string SubBudgetExport = SubBudget + ".Export";

        public const string ApplicationApprovalRecord = ReportManagement + ".ApplicationApprovalRecord";
        public const string ApplicationApprovalRecordExport = ApplicationApprovalRecord + ".Export";

        public const string User = ReportManagement + ".User";
        public const string UserExport = User + ".Export";

        public const string Organization = ReportManagement + ".Organization";
        public const string OrganizationExport = Organization + ".Export";

        public const string FinancialAuditPosition = ReportManagement + ".FinancialAuditPosition";
        public const string FinancialAuditPositionExport = FinancialAuditPosition + ".Export";

        public const string PowerBI = ReportManagement + ".PowerBI";

        public const string PaInvoice = ReportManagement + ".Invoice";
        public const string PaInvoiceExport = PaInvoice + ".Export";

    }

    /// <summary>
    /// 市场活动管理
    /// </summary>
    public static class MarketActvity
    {
        //无二级菜单
        public const string MarketActvityManagement = "MarketActvity";
        public const string MarketActvityH5Management = "MarketActvityH5";

        //操作
        public const string MarketActvityBatchAdd = MarketActvityManagement + ".BatchAdd";
        public const string MarketActvityRead = MarketActvityManagement + ".Read";
        public const string MarketActvityCRUD = MarketActvityManagement + ".CRUD";
        public const string MarketActvityExport = MarketActvityManagement + ".Export";
        public const string MarketActvityJoinActivities = MarketActvityManagement + ".JoinActivities";
        public const string MarketActvityFacorites = MarketActvityManagement + ".Facorites";

        public const string MarketActvityH5Read = MarketActvityH5Management + ".Read";
        public const string MarketActvityH5JoinActivities = MarketActvityH5Management + ".JoinActivities";
        public const string MarketActvityH5Facorites = MarketActvityH5Management + ".Facorites";
    }

    /// <summary>
    /// 客户关系管理
    /// </summary>
    public static class CustomerRelation
    {
        //无二级菜单
        public const string CustomerRelationManagement = "CustomerRelation";
        public const string CustomerRelationH5Management = "CustomerRelationH5";
        //操作
        public const string CustomerRelationBatchAdd = CustomerRelationManagement + ".BatchAdd";
        public const string CustomerRelationRead = CustomerRelationManagement + ".Read";
        public const string CustomerRelationCRUD = CustomerRelationManagement + ".CRUD";
        public const string CustomerRelationExport = CustomerRelationManagement + ".Export";

        public const string CustomerRelationH5Read = CustomerRelationH5Management + ".Read";
    }

    /// <summary>
    /// 核销单
    /// </summary>
    public static class STicket
    {
        //二级菜单 核销单列表
        public const string Application = STicketManagement + ".Application";
        //操作
        public const string ApplicationRead = Application + ".Read";
        public const string ApplicationCRUD = Application + ".CRUD";
        public const string ApplicationExport = Application + ".Export";
        public const string ApplicationPrint = Application + ".Print";
    }

    /// <summary>
    /// FOC申请单
    /// </summary>
    public static class Foc
    {
        //二级菜单 FOC申请列表
        public const string Application = FocManagement + ".Application";
        //操作
        public const string ApplicationRead = Application + ".Read";
        public const string ApplicationCRUD = Application + ".CRUD";
        public const string SelectProduct = Application + ".SelectProduct";
        public const string LogisticsRecord = Application + ".LogisticsRecord";
        public const string ApplicationPrint = Application + ".Print";
        public const string BatchUpload = Application + ".BatchUpload";
        public const string BatchExport = Application + ".BatchExport";

    }
    /// <summary>
    /// 退货申请单
    /// </summary>
    public static class Return
    {
        //二级菜单 退换货申请列表
        public const string Application = ReturnManagement + ".Application";
        //操作
        public const string ApplicationRead = Application + ".Read";
        public const string ApplicationCRUD = Application + ".CRUD";

        public const string ApplicationPrint = Application + ".Print";

    }

    /// <summary>
    /// 换货申请单
    /// </summary>
    public static class Exchange
    {
        //二级菜单 退换货申请列表
        public const string Application = ExchangeManagement + ".Application";
        //操作
        public const string ApplicationRead = Application + ".Read";
        public const string ApplicationCRUD = Application + ".CRUD";

        public const string ApplicationPrint = Application + ".Print";

    }

    /// <summary>
    /// 用餐报告管理
    /// </summary>
    public static class Concur
    {
        //二级菜单 用餐报告
        public const string MealReport = ConcurManagement + ".MealReport";
        //操作
        public const string MealReportRead = MealReport + ".Read";
        public const string MealReportImport = MealReport + ".Import";
        public const string MealReportExport = MealReport + ".Export";

        //二级菜单 员工分组配置
        public const string Employee = ConcurManagement + ".Employee";
        //操作
        public const string EmployeeRead = Employee + ".Read";
        public const string EmployeeImport = Employee + ".Import";
        public const string EmployeeExport = Employee + ".Export";

        //二级菜单 机构转换配置
        public const string Organization = ConcurManagement + ".Organization";
        //操作
        public const string OrganizationRead = Organization + ".Read";
        public const string OrganizationCRUD = Organization + ".CRUD";
        public const string OrganizationExport = Organization + ".Export";
    }


    public static class PurchaseAgreement
    {
        //二级菜单 MSA主服务协议
        public const string Msa = PurchaseAgreementManagement + ".Msa";
        //二级菜单 SOW工作说明书
        public const string Sow = PurchaseAgreementManagement + ".Sow";
        //操作
        public const string MsaRead = Msa + ".Read";
        public const string MsaCRUD = Msa + ".CRUD";
        public const string MsaExport = Msa + ".Export";

        public const string SowRead = Sow + ".Read";
        public const string SowCRUD = Sow + ".CRUD";
        public const string SowExport = Sow + ".Export";
    }
}
