CREATE PROCEDURE [dbo].sp_VendorPersonals
AS
BEGIN
	
with VMXCRT_tmp as (
 select CASE 
        WHEN PATINDEX('%[ FM]%', REVERSE(VMXCRT)) > 0 and( VTYPE ='NHIV' or VTYPE ='NLIV')
        THEN TRIM(LEFT(VMXCRT, LEN(VMXCRT) - PATINDEX('%[ FM]%', REVERSE(VMXCRT))))
        ELSE VMXCRT
    	END AS VMXCRT,VCMPNY,VENDOR
 from  PLATFORM_ABBOTT_Stg.dbo.ODS_BPCS_AVM
   where [VCMPNY] in ('91','79','18','20') 
 )
 ,
vendor_tbl as (
  select 
        --辅助列
        m.VCMPNY,
        m.VENDOR,
        m.VNDNAM,
        --
        v.VMXCRT,
         ROW_NUMBER() OVER (
                    PARTITION BY v.VMXCRT
                        ORDER BY 
                         m.VNSTAT ASC, m.VENDOR desc
                    ) AS rn,
        newid() as [Id],
        cast('' as nvarchar(500)) as [VendorId],
        (
        CASE 
            WHEN RIGHT(RTRIM(m.VMXCRT), 1) = 'F' THEN cast(N'女' as nvarchar(50) )
            WHEN RIGHT(RTRIM(m.VMXCRT), 1) = 'M' THEN cast(N'男'as nvarchar(50) )
            ELSE N'X'
        END) as [Sex],
        cast('' as nvarchar(100)) as [CardType],
        CASE 
            WHEN RIGHT(LTRIM(RTRIM(m.VMXCRT)), 1) IN ('F', 'M') THEN LEFT(LTRIM(RTRIM(m.VMXCRT)), LEN(LTRIM(RTRIM(m.VMXCRT))) - 1) 
            ELSE LTRIM(RTRIM(m.VMXCRT))
        END as [CardNo],
        cast('' as nvarchar(500)) as [CardPic],
        cast('' as nvarchar(500)) as [Province],
        cast('' as nvarchar(500)) as [City],
        cast('' as nvarchar(500)) as [Address],
        cast('' as nvarchar(500)) as [PostCode],
        '{}' as [ExtraProperties],
        'NULL' as [ConcurrencyStamp],
        cast('' as nvarchar(200)) as [CreationTime],
        cast('' as nvarchar(400)) as [CreatorId],
        cast('' as nvarchar(200)) as [LastModificationTime],
        cast('' as nvarchar(400)) as [LastModifierId],
        '0' as [IsDeleted],
        '' as [DeleterId],
        '' as [DeletionTime],
         cast('' as nvarchar(1000)) as [SPName],
        cast('' as nvarchar(1000)) as [AffiliationOrgan],
        cast('' as nvarchar(200))  as [Email],
        cast('' as nvarchar(200))  as [Type]
--    into #vendor_personal_tbl
    from PLATFORM_ABBOTT_Stg.dbo.ODS_BPCS_AVM m
    left join VMXCRT_tmp v
        on m.VENDOR=v.VENDOR and m.VCMPNY=v.VCMPNY
        where m.[VCMPNY] in ('91','79','18','20') and (VTYPE ='NHIV' or VTYPE ='NLIV') 
       ),
       vendor_tbl1 as (
  select 
        --辅助列
        m.VCMPNY,
        m.VENDOR,
        m.VNDNAM,
        --
        v.VMXCRT,
         ROW_NUMBER() OVER (
                    PARTITION BY v.VMXCRT
                        ORDER BY 
                        m.VNSTAT ASC, m.VENDOR desc
                    ) AS rn,
        newid() as [Id],
        cast('' as nvarchar(500)) as [VendorId],
        (
        CASE 
            WHEN RIGHT(RTRIM(m.VMXCRT), 1) = 'F' THEN cast(N'女' as nvarchar(50) )
            WHEN RIGHT(RTRIM(m.VMXCRT), 1) = 'M' THEN cast(N'男'as nvarchar(50) )
            ELSE N'X'
        END) as [Sex],
        cast('' as nvarchar(100)) as [CardType],
        CASE 
            WHEN RIGHT(LTRIM(RTRIM(m.VMXCRT)), 1) IN ('F', 'M') THEN LEFT(LTRIM(RTRIM(m.VMXCRT)), LEN(LTRIM(RTRIM(m.VMXCRT))) - 1) 
            ELSE LTRIM(RTRIM(m.VMXCRT))
        END as [CardNo],
        cast('' as nvarchar(500)) as [CardPic],
        cast('' as nvarchar(500)) as [Province],
        cast('' as nvarchar(500)) as [City],
        cast('' as nvarchar(500)) as [Address],
        cast('' as nvarchar(500)) as [PostCode],
        '{}' as [ExtraProperties],
        '?' as [ConcurrencyStamp],
        cast('' as nvarchar(200)) as [CreationTime],
        cast('' as nvarchar(400)) as [CreatorId],
        cast('' as nvarchar(200)) as [LastModificationTime],
        cast('' as nvarchar(400)) as [LastModifierId],
        '0' as [IsDeleted],
        '' as [DeleterId],
        '' as [DeletionTime],
         cast('' as nvarchar(1000)) as [SPName],
        cast('' as nvarchar(1000)) as [AffiliationOrgan],
        cast('' as nvarchar(200))  as [Email],
        cast('' as nvarchar(200))  as [Type]
--    into #vendor_personal_tbl
    from PLATFORM_ABBOTT_Stg.dbo.ODS_BPCS_AVM m
    left join VMXCRT_tmp v
        on m.VENDOR=v.VENDOR and m.VCMPNY=v.VCMPNY
        where m.[VCMPNY] in ('91','79','18','20') and (VTYPE ='NH' or VTYPE ='NL' or VTYPE ='NT') 
       ),
       vendor_tbl2 as (
        select *,1 as flg from vendor_tbl 
--        where rn=1 
--        or VMXCRT is null or VMXCRT=''
        union 
        select *,2 as flg from vendor_tbl1
        ) 
    select a.* ,b.[VLDRM2] as [BankCardNo]
    into #vendor_personal_tbl 
    from vendor_tbl2 a
    join PLATFORM_ABBOTT_Stg.dbo.ODS_BPCS_PMFVM b
    on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX;
    
    update a set [VendorId]=b.ID from #vendor_personal_tbl a
    left join PLATFORM_ABBOTT_Stg.dbo.Vendor_Tmp b on b.VCMPNY=a.VCMPNY and b.VENDOR=a.VENDOR
    where b.VendorType in (1,2);
   
   update a set [Type]=b.[VendorType] from #vendor_personal_tbl a
     join PLATFORM_ABBOTT_Stg.dbo.Vendor_Tmp b on b.VCMPNY=a.VCMPNY and b.VENDOR=a.VENDOR;
    

    -- update CardType,AffiliationOrgan
    PRINT('update CardType,AffiliationOrgan'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
    --AUTO_BIZ_T_SupplierApplication_Info

--    select ProcInstId,iif(max(SupplierCNName)='',concat(max(company_Value),'_',max(vendorNumber)),max(SupplierCNName)) as vendor_pk,max(idType_Text) as idType_Text,max(belongInstitution) as belongInstitution 
--     into #ProcInstId_tbl from (
--    select *  from (
--            SELECT   b.ProcInstId
--                , b.SupplierCNName
--                , ROW_NUMBER() OVER (
--                    PARTITION BY b.SupplierCNName
--                        ORDER BY 
--                        b.ProcInstId DESC
--                    ) AS rn
--                ,'' as vendorNumber
--                ,'' as company_Value
--                ,b.idType_Text
--                ,b.belongInstitution
--            from PLATFORM_ABBOTT_Stg.dbo.ODS_BPCS_AVM m
--            left JOIN PLATFORM_ABBOTT_Stg.dbo.ODS_AUTO_BIZ_T_SupplierApplication_Info b on m.VNDNAM=b.SupplierCNName
--            where b.ProcInstId is not null
--            union 
--            select   b.ProcInstId
--                , '' SupplierCNName
--                , ROW_NUMBER() OVER (
--                    PARTITION BY b.vendorNumber,b.company_Value
--                        ORDER BY 
--                        b.ProcInstId DESC
--                    ) AS rn
--                ,b.vendorNumber
--                ,b.company_Value
--                ,b.idType_Text
--                ,b.belongInstitution
--            from PLATFORM_ABBOTT_Stg.dbo.ODS_BPCS_PMFVM m
--            left JOIN PLATFORM_ABBOTT_Stg.dbo.ODS_AUTO_BIZ_T_SupplierApplication_Info b on cast(m.VNDERX as nvarchar(10))=b.vendorNumber and cast(m.VMCMPY as nvarchar(5))=b.company_Value and m.VEXTNM=b.supplierCNName
--            where b.ProcInstId is not null 
--            ) t 
--            where rn=1
--    ) tt
--    GROUP BY ProcInstId

    
	select 
	cast(VENDOR as nvarchar(100)) VENDOR,
	cast(VCMPNY as nvarchar(100)) VCMPNY,
	cast(VEXTNM as nvarchar(100)) VEXTNM,
	belongInstitution,
	idType_Text,
	ProcInstId
	into #ProcInstId_tbl
	from PLATFORM_ABBOTT_Stg.dbo.ODS_BPCS_AVM a
	left join PLATFORM_ABBOTT_Stg.dbo.ODS_BPCS_PMFVM b 
	on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
	join (select * from (
		select 
		supplierCNName,
		company_Value, 
		supplierAddress,
		SupplierCity,
		vendorNumber,
		ProcInstId,
		idType_Text,
		belongInstitution,
		ROW_NUMBER() OVER (
		            PARTITION BY supplierCNName,company_Value order by ProcInstId desc) A from PLATFORM_ABBOTT_Stg.dbo.ods_AUTO_BIZ_T_SupplierApplication_Info ) B
		where a=1  ) c
	on cast(VENDOR as nvarchar(100))=cast(vendorNumber as nvarchar(100) ) and cast(VCMPNY as nvarchar(100))=cast(company_Value as nvarchar(100)) and cast(VEXTNM as nvarchar(100))=cast(supplierCNName as nvarchar(100))
	    
    update a set CardType=b.spk_code,AffiliationOrgan=b.belongInstitution
    from  #vendor_personal_tbl a
    join (
    select spk_code,belongInstitution,VENDOR,VCMPNY from 
    #ProcInstId_tbl  b
    join PLATFORM_ABBOTT_Stg.dbo.ODS_Form_7a708c9568fb444a884eb5eca658975f c on c.ProcInstId=b.ProcInstId
    left join PLATFORM_ABBOTT_Stg.dbo.spk_dictionary d on d.spk_name=b.idType_Text
    where c.processstatus in (N'终止(系统)',N'已完成') and d.spk_type=N'证件类型')b
    on a.VENDOR=b.VENDOR and a.VCMPNY=b.VCMPNY
    


    -- update Province,City,Address
    PRINT('update Province,City'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
    update a set Province=b.SupplierProvince,City=b.SupplierCity,Address=b.IdentityCardResidence
    from  #vendor_personal_tbl a
    join (
        SELECT  b.SupplierName
            ,b.SupplierProvince
            ,b.SupplierCity
            ,b.SupplierCode
            ,b.IdentityCardResidence
        from PLATFORM_ABBOTT_Stg.dbo.ODS_BPCS_AVM m
        left JOIN PLATFORM_ABBOTT_Stg.dbo.ODS_T_SupplierExtendedInfo b on m.VNDNAM=b.SupplierName and m.VENDOR=b.SupplierCode
        union 
        select  b.SupplierName
            ,b.SupplierProvince
            ,b.SupplierCity
            ,b.SupplierCode
            ,b.IdentityCardResidence
        from PLATFORM_ABBOTT_Stg.dbo.ODS_BPCS_PMFVM m
        left JOIN PLATFORM_ABBOTT_Stg.dbo.ODS_T_SupplierExtendedInfo b on  m.VEXTNM=b.SupplierName
    ) b on b.SupplierName=a.VNDNAM and b.SupplierCode=a.VENDOR
    --  update PostCode
    PRINT('update PostCode'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
     SELECT
            VMCMPY as VCMPNY,
            VNDERX as VENDOR,
            VEXTNM as VNDNAM,
            VEMLAD as Email,
            ROW_NUMBER() OVER (
                PARTITION BY VMCMPY,VNDERX,VLDate,VLTIME
                ORDER BY 
                VCRDTE DESC
            ) AS rn
    into #BPCS_PMFVM 
    FROM
        PLATFORM_ABBOTT_Stg.dbo.ODS_BPCS_PMFVM m
    WHERE 
        (VLDATE IS NOT NULL or VLTIME IS NOT NULL)

    
    update a set PostCode=b.VPOST from #vendor_personal_tbl a
    join #BPCS_PMFVM g on a.VCMPNY=a.VCMPNY and g.VENDOR=a.VENDOR and g.rn=1
    join PLATFORM_ABBOTT_Stg.dbo.ODS_BPCS_AVM b on a.VCMPNY=b.VCMPNY and a.VENDOR=b.VENDOR

    --update SPName
    PRINT('update SPName'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
    update a set SPName=iif(a.VNDNAM=b.VNDNAM,a.VNDNAM,b.VNDNAM),Email=b.Email from #vendor_personal_tbl a
    join #BPCS_PMFVM b on b.VCMPNY=a.VCMPNY and b.VENDOR=a.VENDOR and b.rn=1
     -- update CreationTime
    PRINT('update AttachmentInformation'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
    update a set CreationTime=t.CreationTime from #vendor_personal_tbl a
    join (select min(CreationTime) as CreationTime,VMCMPY,VNDERX 
        from (select cast(VCRDTE as nvarchar(10))+cast(VCTIME as nvarchar(10)) as CreationTime,VMCMPY,VNDERX  from PLATFORM_ABBOTT_Stg.dbo.ODS_BPCS_PMFVM ) m 
        group by VMCMPY,VNDERX
    ) t on a.VCMPNY=t.VMCMPY and a.vendor=t.VNDERX
    -- update Sex
    PRINT('update Sex'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
    SELECT 
        a.VENDOR,
        a.VNDNAM,
        a.GENDER,
        ROW_NUMBER() OVER (
            PARTITION BY a.VENDOR, a.VNDNAM -- 按供应商分组
            ORDER BY 
                CASE WHEN UPD_DATETIME IS NOT NULL THEN UPD_DATETIME END DESC, -- 首先按更新时间降序
                INS_DATETIME DESC -- 若更新时间相同，则按插入时间降序
        ) AS rn
        into #T_VENDOR
    FROM 
        PLATFORM_ABBOTT_Stg.dbo.ODS_T_VENDOR_TIER a
    LEFT JOIN PLATFORM_ABBOTT_Stg.dbo.ODS_BPCS_AVM b ON a.VENDOR = cast(b.VENDOR as nvarchar(10))
    LEFT JOIN PLATFORM_ABBOTT_Stg.dbo.ODS_BPCS_PMFVM c ON c.VEXTNM = a.VNDNAM
    where ISNULL(a.GENDER,'N/A')<>'N/A' 

    update a set Sex=GENDER from #vendor_personal_tbl a
    join (SELECT 
            VENDOR,
            VNDNAM,
            MAX(GENDER) AS GENDER
        FROM #T_VENDOR  t
        WHERE  rn = 1
        GROUP BY 
            VENDOR,
            VNDNAM) g
    on a.VNDNAM=a.VNDNAM and g.VENDOR=a.VENDOR
    where a.Sex=N'X'

    update a set Sex=(
        case when d.sex=1 then N'男'
            when d.sex=2 then N'女'
            else ''
        end
    ) from #vendor_personal_tbl a
    join #ProcInstId_tbl  b  on a.VENDOR=b.VENDOR and a.VCMPNY=b.VCMPNY
    join PLATFORM_ABBOTT_Stg.dbo.ODS_Form_7a708c9568fb444a884eb5eca658975f c on c.ProcInstId=b.ProcInstId
    join PLATFORM_ABBOTT_Stg.dbo.ODS_AUTO_BIZ_T_SupplierApplication_Info d on d.ProcInstid=b.ProcInstid 
    where c.processstatus in (N'终止(系统)',N'已完成') and a.Sex=N'X'
    
    
    -- update CreatorId
    PRINT('update CreationTime'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
    update a set CreatorId=ISNULL(cast(d.XmlContent as XML).value('(/root/SupplierApplication_agentBlock_MainStore/agentDeptId)[1]', 'NVARCHAR(50)'),'00000000-0000-0000-0000-************')
    from #vendor_personal_tbl a
    join #ProcInstId_tbl  b on a.VENDOR=b.VENDOR and a.VCMPNY=b.VCMPNY
    join PLATFORM_ABBOTT_Stg.dbo.ODS_T_FORMINSTANCE_GLOBAL d on d.ProcInstid=b.ProcInstid 
    where d.XmlContent is not NULL and a.CreatorId='';
    -- update LastModificationTime
    PRINT('update LastModificationTime'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
    update a set LastModificationTime=t.LastModificationTime,LastModifierId=isnull(b.spk_NexBPMCode,'00000000-0000-0000-0000-************')
    from #vendor_personal_tbl a
    join (select max(LastModificationTime) as LastModificationTime,min(VLUSER) as VLUSER,m.VMCMPY,m.VNDERX 
            from (select cast(VLDATE as nvarchar(10))+cast(VLTIME as nvarchar(10)) as LastModificationTime,VLUSER,VMCMPY,VNDERX  from PLATFORM_ABBOTT_Stg.dbo.ODS_BPCS_PMFVM  
            ) m 
            group by m.VMCMPY,m.VNDERX
    ) t on a.VCMPNY=t.VMCMPY and a.vendor=t.VNDERX
    left join PLATFORM_ABBOTT_Stg.dbo.spk_staffmasterdata b on b.spk_staffaccount=t.VLUSER
    where t.VLUSER!='CNSECOFR1' and t.LastModificationTime!='**************';

select   c.ProcInstId,a.VCMPNY,a.VENDOR  into #upid  
from PLATFORM_ABBOTT_Stg.dbo.ODS_BPCS_AVM a 
join PLATFORM_ABBOTT_Stg.dbo.ODS_BPCS_PMFVM b 
on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
join PLATFORM_ABBOTT_Stg.dbo.ods_AUTO_BIZ_T_SupplierApplication_Info c
on cast(VENDOR as nvarchar(255))=cast(vendorNumber as nvarchar(255)) and cast(VCMPNY as nvarchar(255))=cast(company_Value as nvarchar(255)) and cast(VEXTNM as nvarchar(255))=cast(supplierCNName as nvarchar(255))
join PLATFORM_ABBOTT_Stg.dbo.ods_Form_7a708c9568fb444a884eb5eca658975f d
on c.ProcInstId =d.ProcInstId 
where processstatus=N'终止(系统)' or processstatus=N'已完成';

select ProcInstId,
		up_Id,
		File_Name,
		Emp_ID 
		into #File_Name
	from (
	select XmlContent,ProcInstId,
	up_Id.value('.', 'nvarchar(100)') as up_Id 
	from (
		SELECT
		XmlContent as  XmlContent,
		ProcInstId
		FROM PLATFORM_ABBOTT_Stg.dbo.ODS_T_FORMINSTANCE_GLOBAL)A
		CROSS APPLY XmlContent.nodes('/root/AttachmentInfoGrid/row/up_Id') AS XMLTable(up_Id)) B
		join PLATFORM_ABBOTT_Stg.dbo.ODS_T_METAFILE tm
		on B.up_Id=tm.file_id;
		
select b.ProcInstId,b.File_Name,up_id,d.VCMPNY,d.VENDOR 
into #up_id
from (
	select ProcInstId,
	case when FrontSideFileName<>null or FrontSideFileName<>'' then FrontSideFileName
	else BackSideFileName end as File_Name 
	from (
	SELECT 
	    ProcInstId, 
	    MAX(CASE 
	            WHEN File_Name LIKE N'%正面%' THEN File_Name
	            ELSE NULL
	        END) AS FrontSideFileName,
	    MAX(CASE 
	            WHEN File_Name NOT LIKE N'%正面%' AND File_Name NOT LIKE N'%反面%' THEN File_Name
	            WHEN File_Name LIKE N'%反面%' THEN File_Name
	            ELSE NULL
	        END) AS BackSideFileName
	FROM 
	    #File_Name
	    where File_Name like N'%身份证%'
	GROUP BY 
	    ProcInstId
	    )a
	)b
left join #File_Name c
on b.File_Name=c.File_Name and b.ProcInstId=c.ProcInstId   
join #upid d
on b.ProcInstId=d.ProcInstId ;

update a set a.CardPic=e.up_Id
from #vendor_personal_tbl a
join
#up_id e
on a.VCMPNY=e.VCMPNY and  a.VENDOR=e.VENDOR;

with personal_tbl as (
select 
VCMPNY,
VENDOR,
a.VMXCRT,
a.Id,
a.VendorId,
a.Sex,
a.CardType,
a.CardNo,
a.CardPic,
a.Province,
a.City,
a.Address,
a.PostCode,
a.ExtraProperties,
cast(a.ConcurrencyStamp as nvarchar(10)) ConcurrencyStamp,
a.CreationTime,
a.CreatorId,
a.LastModificationTime,
a.LastModifierId,
a.IsDeleted,
a.DeleterId,
a.DeletionTime,
a.SPName,
a.AffiliationOrgan,
a.Email from (
select 
VMXCRT,
Id,
MAX(VendorId) as VendorId,
MAX(Sex) as Sex,
MAX(CardType) as CardType,
MAX(CardNo) as CardNo,
MAX(CardPic) as CardPic,
MAX(Province) as Province,
MAX(City) as City,
MAX(Address) as Address,
MAX(PostCode) as PostCode,
MAX(ExtraProperties) as ExtraProperties,
MAX(ConcurrencyStamp) as ConcurrencyStamp,
MIN(CreationTime) as CreationTime,
MAX(CreatorId) as CreatorId,
MAX(LastModificationTime) as LastModificationTime,
MAX(LastModifierId) as LastModifierId,
MAX(IsDeleted) as IsDeleted,
MAX(DeleterId) as DeleterId,
MAX(DeletionTime) as DeletionTime,
MAX(SPName) as SPName,
MAX(AffiliationOrgan) as AffiliationOrgan,
MAX(Email) as Email
from #vendor_personal_tbl
where 
[Type] in (1,2) and 
(VMXCRT <> null or VMXCRT<>'') and rn=1
group by VMXCRT,Id
) A
join #vendor_personal_tbl b
on a.id=b.id
union 
select 
VCMPNY,
VENDOR,
VMXCRT,
Id,
VendorId,
Sex,
CardType,
CardNo,
CardPic,
Province,
City,
Address,
PostCode,
ExtraProperties,
ConcurrencyStamp,
CreationTime,
CreatorId,
LastModificationTime,
LastModifierId,
IsDeleted,
DeleterId,
DeletionTime,
SPName,
AffiliationOrgan,
Email
from(
	select 
	VCMPNY,
	VENDOR,
	VMXCRT,
	Id,
	VendorId,
	Sex,
	CardType,
	CardNo,
	CardPic,
	Province,
	City,
	Address,
	PostCode,
	ExtraProperties,
	ConcurrencyStamp,
	CreationTime,
	CreatorId,
	LastModificationTime,
	LastModifierId,
	IsDeleted,
	DeleterId,
	DeletionTime,
	SPName,
	AffiliationOrgan,
	Email,
	ROW_NUMBER() over(PARTITION by [BankCardNo],VNDNAM order by VENDOR desc ,rn asc) rn1
	from #vendor_personal_tbl
	where [Type] in (3,4) or VMXCRT= null or VMXCRT='' 
	)a
	where rn1=1
)
select * 
into #vendor_personal_tb2
from personal_tbl

--drop table #vendor_personal_tb2


--落成实体表
 IF OBJECT_ID(N'PLATFORM_ABBOTT_Stg.dbo.VendorPersonals_tmp', N'U') IS NOT NULL
BEGIN
	
	update a 
	set 
        a.VMXCRT                = b.VMXCRT
       ,a.VendorId              = b.VendorId
       ,a.Sex                   = b.Sex
       ,a.CardType              = b.CardType
       ,a.CardNo                = b.CardNo
       ,a.CardPic               = b.CardPic
       ,a.Province              = b.Province
       ,a.City                  = b.City
       ,a.Address               = b.Address
       ,a.PostCode              = b.PostCode
       ,a.ExtraProperties       = b.ExtraProperties
       ,a.ConcurrencyStamp      = b.ConcurrencyStamp
       ,a.CreationTime          = b.CreationTime
       ,a.CreatorId             = b.CreatorId
       ,a.LastModificationTime  = b.LastModificationTime
       ,a.LastModifierId        = b.LastModifierId
       ,a.IsDeleted             = b.IsDeleted
       ,a.DeleterId             = b.DeleterId
       ,a.DeletionTime          = b.DeletionTime
       ,a.SPName                = b.SPName
       ,a.AffiliationOrgan      = b.AffiliationOrgan
       ,a.Email                 = b.Email
     from PLATFORM_ABBOTT_Stg.dbo.VendorPersonals_tmp a 
     left join #vendor_personal_tb2 b on a.VCMPNY = b.VCMPNY  and a.VENDOR = b.VENDOR
     
     insert into PLATFORM_ABBOTT_Stg.dbo.VendorPersonals_tmp
     select a.VCMPNY
           ,a.VENDOR
           ,a.VMXCRT
           ,a.Id
           ,a.VendorId
           ,a.Sex
           ,a.CardType
           ,a.CardNo
           ,a.CardPic
           ,a.Province
           ,a.City
           ,a.Address
           ,a.PostCode
           ,a.ExtraProperties
           ,a.ConcurrencyStamp
           ,a.CreationTime
           ,a.CreatorId
           ,a.LastModificationTime
           ,a.LastModifierId
           ,a.IsDeleted
           ,a.DeleterId
           ,a.DeletionTime
           ,a.SPName
           ,a.AffiliationOrgan
           ,a.Email
     from #vendor_personal_tb2 a
     where not exists (select * from PLATFORM_ABBOTT_Stg.dbo.VendorPersonals_tmp where a.VCMPNY = VCMPNY  and a.VENDOR = VENDOR)
	PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))

	END
ELSE
BEGIN
	PRINT(N'开始落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	select  *  into PLATFORM_ABBOTT_Stg.dbo.VendorPersonals_tmp from #vendor_personal_tb2
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END

END

	