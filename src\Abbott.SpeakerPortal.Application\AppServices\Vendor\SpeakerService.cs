﻿using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Common.SMS;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.Common.Attachment;
using Abbott.SpeakerPortal.Entities.VendorApplicationFinancials;
using Abbott.SpeakerPortal.Entities.VendorApplicationPersonals;
using Abbott.SpeakerPortal.Entities.VendorApplications;
using Abbott.SpeakerPortal.Entities.VendorFinancials;
using Abbott.SpeakerPortal.Entities.VendorPersonals;
using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Redis;
using Abbott.SpeakerPortal.Utils;
using Abbott.SpeakerPortal.Vendor.Speaker;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using Volo.Abp.Uow;
using Abbott.SpeakerPortal.Contracts.Common.PowerApp;
using Abbott.SpeakerPortal.Contracts.Common.Approve;
using Statuses = Abbott.SpeakerPortal.Enums.Statuses;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Vendor.Speaker.TaskCenter;
using Abbott.SpeakerPortal.Entities.VendorApplicationOrgnizations;
using Abbott.SpeakerPortal.Contracts.Vendor.Speaker.TaskCenter;
using Abbott.SpeakerPortal.Entities.Common.Consent;
using Abbott.SpeakerPortal.Consent;
using Abbott.SpeakerPortal.Entities.VendorBlackLists;
using MiniExcelLibs;
using IConfiguration = Microsoft.Extensions.Configuration.IConfiguration;
using Abbott.SpeakerPortal.Entities.Integration.Dspot;
using Abbott.SpeakerPortal.Entities.VendorOrgnizations;
using Abbott.SpeakerPortal.Contracts.Integration.Dspot;
using System.Configuration;
using Microsoft.Extensions.Configuration;
using Senparc.Weixin.MP.AdvancedAPIs.Draft.DraftJson;
using Abbott.SpeakerPortal.Contracts.Agent;
using Abbott.SpeakerPortal.Contracts.Vendor;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Contracts.Budget.Subbudget;
using Abbott.SpeakerPortal.Entities.Common.Log;
using Abbott.SpeakerPortal.Contracts.Common.Common;
using Abbott.SpeakerPortal.Contracts.Vendor.Speaker;
using Abbott.SpeakerPortal.Entities.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Contracts.Integration.EpdPortal.EpdHcp;
using DocumentFormat.OpenXml.Wordprocessing;
using System.Reflection;
using DocumentFormat.OpenXml.Drawing.Charts;
using Abbott.SpeakerPortal.Enums.Integration;
using Microsoft.Identity.Client;
using Microsoft.AspNetCore.Mvc.ApplicationModels;
using Abbott.SpeakerPortal.Entities.Common.WeChat;
using Senparc.CO2NET.Extensions;
using Abbott.SpeakerPortal.ConsentServices;
using Abbott.SpeakerPortal.Contracts.Vendor.NonSpeaker;
using Abbott.SpeakerPortal.Entities.Common.Workflow.WorkflowTasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.FileProviders;
using Abbott.SpeakerPortal.Contracts.Common.BPMEmail;
using Volo.Abp.BackgroundJobs;
using Abbott.SpeakerPortal.BackgroundWorkers.BPMEmail;
using Hangfire;
using Microsoft.IdentityModel.Tokens;
using System.Security.Cryptography;
using Abbott.SpeakerPortal.Extension;
using Volo.Abp.OpenIddict;
using Abbott.SpeakerPortal.Contracts.Approval;
using static Abbott.SpeakerPortal.Permissions.SpeakerPortalPermissions;
using Senparc.Weixin.MP.AdvancedAPIs.Card;
using static Abbott.SpeakerPortal.Enums.DataverseEnums;
using Microsoft.EntityFrameworkCore.Infrastructure.Internal;
using Abbott.SpeakerPortal.Entities;
using Abbott.SpeakerPortal.Entities.Integration.Veeva;
using Abbott.SpeakerPortal.Dataverse.Contracts;
using Microsoft.Xrm.Sdk.Query;
using Microsoft.Xrm.Sdk;
using Abbott.SpeakerPortal.AppServices.Common;

namespace Abbott.SpeakerPortal.AppServices.Vendor
{
    public class SpeakerService : SpeakerPortalAppService, ISpeakerService
    {
        private readonly IConfiguration _configuration;
        private readonly IServiceProvider _serviceProvider;
        private ISpeakerService _speakerService;
        private IVendorPersonalRepository _vendorPersonalRepository;
        private IVendorRepository _vendorRepository;
        private IIdentityUserRepository _identityUserRepository;
        private ISMSService _smsService;
        private readonly ILogger<SpeakerService> _logger;
        private IDataverseService _dataverseService;
        private IRepository<VendorFinancial, Guid> _vendorFinancialRepository;
        private IAttachmentRepository _attachmentRepository;
        private readonly IApproveService approveService;
        private readonly ICommonService _commonService;

        public SpeakerService(IServiceProvider serviceProvider, IApproveService _approveService)
        {
            _serviceProvider = serviceProvider;
            _smsService = serviceProvider.GetService<ISMSService>();
            _identityUserRepository = serviceProvider.GetService<IIdentityUserRepository>();
            _logger = serviceProvider.GetService<ILogger<SpeakerService>>();
            _dataverseService = _serviceProvider.GetService<IDataverseService>();
            _vendorFinancialRepository = serviceProvider.GetService<IRepository<VendorFinancial, Guid>>();
            _attachmentRepository = serviceProvider.GetService<IAttachmentRepository>();
            approveService = _approveService;
            _configuration = _serviceProvider.GetService<IConfiguration>();
            _commonService = _serviceProvider.GetService<ICommonService>();
        }

        /// <summary>
        /// 修改手机号
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> UpdateMobileAsync(UpdateMobileRequestDto request)
        {
            //验证码校验
            var verifyCode = request.VerifyCode;
            var redis = _serviceProvider.GetService<IRedisRepository>();
            string value = redis.Database.StringGet(RedisKey.VerifyCode + request.NewMobile);
            if (string.IsNullOrEmpty(value))
                return MessageResult.FailureResult(messageModel: new MessageModelBase(404, "Invalid verification code!"));
            if (!value.Equals(verifyCode))
                return MessageResult.FailureResult(messageModel: new MessageModelBase(500, "Verification code error!"));

            //用户登录查询
            var identityUserList = await _identityUserRepository.GetListAsync();
            var identityUser = identityUserList.FirstOrDefault(f => f.PhoneNumber == request.OldMobile);
            if (identityUser == null)
                return MessageResult.FailureResult(messageModel: new MessageModelBase(400, "User does not exist!"));

            try
            {
                //用户修改
                identityUser.SetPhoneNumber(request.NewMobile, false);
                using (var scope = _serviceProvider.CreateScope())
                {
                    var uowManager = scope.ServiceProvider.GetRequiredService<IUnitOfWorkManager>();

                    using var uow = uowManager.Begin();
                    await _identityUserRepository.UpdateAsync(identityUser, true);
                    await uow.CompleteAsync();
                }

                //用户讲者查询
                var userList = await _vendorRepository.GetListAsync();
                var user = userList.FirstOrDefault(f => f.HandPhone == request.OldMobile);
                //讲者修改
                if (user != null)
                {
                    user.HandPhone = request.NewMobile;
                    await _vendorRepository.UpdateAsync(user);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"UpdateMobile Error: {ex.Message}");
                return MessageResult.FailureResult();
            }
            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 获取讲者列表
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<PagedResultDto<SpeakerListResponseDto>> GetSpeakerListAsync(SpeakerListRequestDto request)
        {
            try
            {
                var _vendorPersonalRepository = _serviceProvider.GetService<IVendorPersonalReadonlyRepository>();
                var _vendorRepository = _serviceProvider.GetService<IVendorReadonlyRepository>();
                var vendorQuery = await _vendorRepository.GetQueryableAsync();
                var vendorPersonalQuery = await _vendorPersonalRepository.GetQueryableAsync();
                var jobTitles = await _dataverseService.GetAllJobTiles(stateCode: null);
                var hospitals = await _dataverseService.GetAllHospitals(stateCode: null);
                var standardHosDeps = await _dataverseService.GetAllDepartments(stateCode: null);
                var status = EnumUtil.GetEnumIdValues<VendorStatus>().ToDictionary(a => (VendorStatus)a.Key, a => a.Value);
                var vendorApplication = await _serviceProvider.GetService<IVendorApplicationReadonlyRepository>().GetQueryableAsync();
                var vendorBlackQuery = await _serviceProvider.GetService<IVendorBlackListReadonlyRepository>().GetQueryableAsync();
                var _consentsetvice = LazyServiceProvider.GetService<IConsentService>();

                //查询我相关的草稿
                var myList = new List<string>();
                if (request.OnlyMine)
                {
                    myList = vendorApplication.Where(w => w.ApplyUserId == CurrentUser.Id && w.Status != Statuses.Saved && w.Status != Statuses.Delete && w.Status != Statuses.Rejected).Select(s => s.VendorCode).ToList();
                    if (!myList.Any())
                        return new PagedResultDto<SpeakerListResponseDto>(0, []);
                }

                //供应商个人链表供应商
                var speakers = vendorPersonalQuery.Join(vendorQuery, a => a.VendorId, b => b.Id, (a, b) => new { a.SPName, vendorQuery = b })
                    .Where(w => w.vendorQuery.VendorType == VendorTypes.HCPPerson)
                    .WhereIf(myList.Count > 0, p => myList.Contains(p.vendorQuery.VendorCode))
                    .WhereIf(!string.IsNullOrEmpty(request.VendorCode), p => p.vendorQuery.VendorCode == request.VendorCode)
                    .WhereIf(!string.IsNullOrEmpty(request.SPName), p => p.SPName == request.SPName)
                    .WhereIf(request.Status.HasValue, p => p.vendorQuery.Status == request.Status)
                    .WhereIf(request.PTId.HasValue, p => p.vendorQuery.PTId == request.PTId)
                    .WhereIf(request.HospitalId.HasValue, p => p.vendorQuery.HospitalId == request.HospitalId)
                    .WhereIf(request.StandardHosDepId.HasValue, p => p.vendorQuery.StandardHosDepId == request.StandardHosDepId)
               .Select(g => new SpeakerListResponseDto
               {
                   ID = g.vendorQuery.Id,
                   OpenId = g.vendorQuery.OpenId,
                   UserId = g.vendorQuery.UserId,
                   VendorCode = g.vendorQuery.VendorCode,
                   SPName = g.SPName,
                   Status = g.vendorQuery.Status,
                   StatusString = status[g.vendorQuery.Status],
                   //SignedStatus = g.vendorQuery.SignedStatus,
                   //SignedStatusString = g.vendorQuery.SignedStatus ? "已签署" : "未签署",
                   PTId = g.vendorQuery.PTId,
                   HospitalId = g.vendorQuery.HospitalId,
                   StandardHosDepId = g.vendorQuery.StandardHosDepId,
                   HosDepartment = g.vendorQuery.HosDepartment,
                   SPLevel = g.vendorQuery.SPLevel,
                   CreationTime = g.vendorQuery.CreationTime,
                   PTIName = g.vendorQuery.PTName,
                   HospitalName = g.vendorQuery.HospitalName,
                   StandardHosDepName = g.vendorQuery.StandardHosDepName,
               }).OrderByDescending(o => o.CreationTime).AsQueryable();

                var speakerCount = speakers.Count();
                //这里分页减少数据量查询
                var speaker = speakers.Skip(request.PageIndex * request.PageSize).Take(request.PageSize)
                    .ToList();

                //查询黑名单
                var vendorBlackAndVendorQuery = vendorBlackQuery.Where(w => w.Status == BlackStatus.Effective || w.Status == BlackStatus.ToBeEffective)
                    .Where(w => speaker.Select(s => s.ID).Contains(w.VendorId)).ToList();
                // 获取签署内容
                var consentSignedQuery = await LazyServiceProvider.LazyGetService<IConsentSignedReadonlyRepository>().GetListAsync();
                //获取需要检查的最新版签署版本号
                string consentInfo = _configuration["Consent:ConsentCode"];
                var consentResponse = await _consentsetvice.GetConsentInfoAsync([.. consentInfo.Split(",")]);


                //转换内容
                foreach (var item in speaker)
                {
                    if (item.PTId.HasValue)
                        item.PTIName = jobTitles.FirstOrDefault(f => f.Id == item.PTId.Value)?.Name;
                    if (item.HospitalId.HasValue)
                        item.HospitalName = hospitals.FirstOrDefault(P => P.Id == item.HospitalId.Value)?.Name;
                    if (item.StandardHosDepId.HasValue)
                        item.StandardHosDepName = standardHosDeps.FirstOrDefault(P => P.Id == item.StandardHosDepId.Value)?.Name;
                    if (vendorBlackAndVendorQuery.Select(s => s.VendorId).Contains(item.ID))
                    {
                        item.IsInBlackList = true;
                        var entity = vendorBlackAndVendorQuery.First(f => f.VendorId == item.ID);
                        if (entity.StartDate < DateTime.Now && entity.EndDate > DateTime.Now)
                        {
                            item.Status = VendorStatus.Exception;
                            item.StatusString = status[VendorStatus.Exception];
                        }
                    }
                    //审批状态
                    var approving = vendorApplication.FirstOrDefault(w => w.VendorCode == item.VendorCode && w.Status == Statuses.Approving);
                    item.IsApprove = approving != null;
                    item.IsApproveString = approving != null ? "审批中" : "";

                    //获取讲者签署记录，判断是否已经完全签署
                    var consentList = consentSignedQuery.Where(w => w.OneId == item.OpenId || w.AppUserId == item.UserId.ToString()).ToList();
                    var isConsent = HasBeenFullySigned(consentList, consentResponse);
                    item.SignedStatus = isConsent;
                    item.SignedStatusString = isConsent ? "已签署" : "待签署";
                    item.OpenId = "";//安全起见不返回该内容
                }

                var result = new PagedResultDto<SpeakerListResponseDto>()
                {
                    Items = speaker,
                    TotalCount = speakerCount,
                };

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError($"SpeakerService's GetSpeakerListAsync has an error : {ex.Message}");
                return null;
            }
        }


        /// <summary>
        /// 导出讲者列表
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<Stream> ExportSpeakerListAsync(SpeakerListRequestDto request)
        {
            try
            {
                var speaker = await GetSpeakerList(request, true);
                MemoryStream stream = new();
                stream.SaveAs(speaker, true, "SheetName");
                stream.Seek(0, SeekOrigin.Begin);
                return stream;
                //using var workbook = new XLWorkbook();
                //var worksheet = workbook.Worksheets.Add("Sheet1");
                //worksheet.Cell("A1").Value = "讲者编码";
                //worksheet.Cell("B1").Value = "讲者姓名";
                //worksheet.Cell("C1").Value = "状态";
                //worksheet.Cell("D1").Value = "职称";
                //worksheet.Cell("E1").Value = "所属医院";
                //worksheet.Cell("F1").Value = "标准科室";
                //worksheet.Cell("G1").Value = "讲者评级";
                //worksheet.ColumnWidth = 25;

                ////第一行
                //var range1 = worksheet.Range("A1:G1");
                ////字体颜色
                //range1.Style.Font.FontColor = XLColor.White;
                ////背景色
                //var backgroundColor = XLColor.FromArgb(68, 114, 196);
                //range1.Style.Fill.BackgroundColor = backgroundColor;
                ////字体大小
                //range1.Style.Font.FontSize = 14;
                ////是否加粗
                //range1.Style.Font.Bold = true;

                //int i = 1;
                //foreach (var item in speaker)
                //{
                //    i++;
                //    worksheet.Cell($"A{i}").Value = item.VendorCode;
                //    worksheet.Cell($"B{i}").Value = item.SPName;
                //    worksheet.Cell($"C{i}").Value = item.StatusString;
                //    worksheet.Cell($"D{i}").Value = item.PTIName;
                //    worksheet.Cell($"E{i}").Value = item.HospitalName;
                //    worksheet.Cell($"F{i}").Value = item.StandardHosDepName;
                //    worksheet.Cell($"G{i}").Value = item.SPLevel;
                //}

                //MemoryStream stream = new();
                //workbook.SaveAs(stream);
                //stream.Seek(0, SeekOrigin.Begin);
                //return stream;
            }
            catch (Exception ex)
            {
                _logger.LogError($"SpeakerService's ExceptSpeakerListAsync has an error : {ex.Message}");
                return null;
            }
        }


        /// <summary>
        /// 获取讲者信息列表
        /// </summary>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        private async Task<List<SpeakerListResponseDto>> GetSpeakerList(SpeakerListRequestDto request, bool isExcept = false)
        {
            var _vendorPersonalRepository = _serviceProvider.GetService<IVendorPersonalRepository>();
            var _vendorRepository = _serviceProvider.GetService<IVendorRepository>();
            var vendorQuery = await _vendorRepository.GetQueryableAsync();
            var vendorPersonalQuery = await _vendorPersonalRepository.GetQueryableAsync();
            var jobTitles = await _dataverseService.GetAllJobTiles();
            var hospitals = await _dataverseService.GetAllHospitals();
            var standardHosDeps = await _dataverseService.GetAllDepartments();
            var status = EnumUtil.GetEnumIdValues<VendorStatus>().ToDictionary(a => (VendorStatus)a.Key, a => a.Value);
            var vendorApplication = await _serviceProvider.GetService<IVendorApplicationRepository>().GetQueryableAsync();
            var vendorBlackQuery = await _serviceProvider.GetService<IVendorBlackListRepository>().GetQueryableAsync();
            var _consentsetvice = LazyServiceProvider.GetService<IConsentService>();

            //查询全部时，没有传讲者姓名，就返回空
            if (string.IsNullOrEmpty(request.SPName) && !request.OnlyMine) return null;


            //查询我相关的草稿
            var myList = new List<string>();
            if (request.OnlyMine)
                myList = vendorApplication.Where(w => w.ApplyUserId == CurrentUser.Id && w.Status != Statuses.Saved && w.Status != Statuses.Delete && w.Status != Statuses.Rejected).Select(s => s.VendorCode).ToList();

            //供应商个人链表供应商
            var speakers = vendorPersonalQuery.Join(vendorQuery, a => a.VendorId, b => b.Id, (a, b) => new { a.SPName, vendorQuery = b })
                //.WhereIf(request.OnlyMine, w => w.vendorQuery.CreatorId == CurrentUser.Id)
                .WhereIf(myList.Count > 0, p => myList.Contains(p.vendorQuery.VendorCode))
                .WhereIf(!string.IsNullOrEmpty(request.VendorCode), p => p.vendorQuery.VendorCode.Contains(request.VendorCode))
                .WhereIf(!string.IsNullOrEmpty(request.SPName), p => p.SPName.Contains(request.SPName))
                .WhereIf(request.Status.HasValue, p => p.vendorQuery.Status == request.Status)
                .WhereIf(request.PTId.HasValue, p => p.vendorQuery.PTId == request.PTId)
                .WhereIf(request.HospitalId.HasValue, p => p.vendorQuery.HospitalId == request.HospitalId)
                .WhereIf(request.StandardHosDepId.HasValue, p => p.vendorQuery.StandardHosDepId == request.StandardHosDepId)
           .Select(g => new SpeakerListResponseDto
           {
               ID = g.vendorQuery.Id,
               OpenId = g.vendorQuery.OpenId,
               UserId = g.vendorQuery.UserId,
               VendorCode = g.vendorQuery.VendorCode,
               SPName = g.SPName,
               Status = g.vendorQuery.Status,
               StatusString = status[g.vendorQuery.Status],
               //SignedStatus = g.vendorQuery.SignedStatus,
               //SignedStatusString = g.vendorQuery.SignedStatus ? "已签署" : "未签署",
               PTId = g.vendorQuery.PTId,
               HospitalId = g.vendorQuery.HospitalId,
               StandardHosDepId = g.vendorQuery.StandardHosDepId,
               HosDepartment = g.vendorQuery.HosDepartment,
               SPLevel = g.vendorQuery.SPLevel,
               CreationTime = g.vendorQuery.CreationTime,
               PTIName = g.vendorQuery.PTName,
               HospitalName = g.vendorQuery.HospitalName,
               StandardHosDepName = g.vendorQuery.StandardHosDepName,
           }).OrderByDescending(o => o.CreationTime).AsQueryable();

            var speaker = new List<SpeakerListResponseDto>();
            if (!isExcept)
            {
                //这里分页减少数据量查询
                speaker = speakers.Skip(request.PageIndex * request.PageSize).Take(request.PageSize)
                    .ToList();
            }
            else
            {
                speaker = speakers.ToList();
            }

            //查询黑名单
            var vendorBlackAndVendorQuery = vendorBlackQuery.Where(w => w.Status == BlackStatus.Effective || w.Status == BlackStatus.ToBeEffective)
                .Where(w => speaker.Select(s => s.ID).Contains(w.VendorId)).ToList();
            // 获取签署内容
            var consentSignedQuery = await LazyServiceProvider.LazyGetService<IConsentSignedRepository>().GetListAsync();
            //获取需要检查的最新版签署版本号
            string consentInfo = _configuration["Consent:ConsentCode"];
            var consentResponse = await _consentsetvice.GetConsentInfoAsync([.. consentInfo.Split(",")]);


            //转换内容
            foreach (var item in speaker)
            {
                if (item.PTId.HasValue)
                    item.PTIName = jobTitles.FirstOrDefault(f => f.Id == item.PTId.Value)?.Name;
                if (item.HospitalId.HasValue)
                    item.HospitalName = hospitals.FirstOrDefault(P => P.Id == item.HospitalId.Value)?.Name;
                if (item.StandardHosDepId.HasValue)
                    item.StandardHosDepName = standardHosDeps.FirstOrDefault(P => P.Id == item.StandardHosDepId.Value)?.Name;
                if (vendorBlackAndVendorQuery.Select(s => s.VendorId).Contains(item.ID))
                {
                    item.IsInBlackList = true;
                    var entity = vendorBlackAndVendorQuery.First(f => f.VendorId == item.ID);
                    if (entity.StartDate < DateTime.Now && entity.EndDate > DateTime.Now)
                    {
                        item.Status = VendorStatus.Exception;
                        item.StatusString = status[VendorStatus.Exception];
                    }
                }

                //获取讲者签署记录，判断是否已经完全签署
                var consentList = consentSignedQuery.Where(w => w.OneId == item.OpenId || w.AppUserId == item.UserId.ToString()).ToList();
                var isConsent = HasBeenFullySigned(consentList, consentResponse);
                item.SignedStatus = isConsent;
                item.SignedStatusString = isConsent ? "已签署" : "待签署";
                item.OpenId = "";//安全起见不返回该内容
            }

            return speaker;
        }

        /// <summary>
        /// 获取讲者历史申请记录列表
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<PagedResultDto<SpeakerListApplicationResponseDto>> GetSpeakerApplicationListAsync(SpeakerListApplicationRequestDto request)
        {
            var status = EnumUtil.GetEnumIdValues<Statuses>().ToDictionary(a => (Statuses)a.Key, a => a.Value);
            var applicationType = EnumUtil.GetEnumIdValues<Enums.ApplicationTypes>().ToDictionary(a => (Enums.ApplicationTypes)a.Key, a => a.Value);
            var vendorApplication = await _serviceProvider.GetService<IVendorApplicationRepository>().GetQueryableAsync();
            var vendorApplicationPersonal = await _serviceProvider.GetService<IVendorApplicationPersonalRepository>().GetQueryableAsync();
            var _identityUserRepository = _serviceProvider.GetService<IRepository<IdentityUser, Guid>>();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            try
            {
                //从草稿列表中获取所有该讲者相关的审批记录
                var speaker = vendorApplication.Where(p => p.VendorCode == request.VendorCode && p.Status != Statuses.Saved)
               .Select(g => new SpeakerListApplicationResponseDto
               {
                   Id = g.Id,
                   ApplicationType = g.ApplicationType,
                   ApplicationTypeString = applicationType[g.ApplicationType],
                   ApplicationCode = g.ApplicationCode,
                   ApplyTime = g.ApplyTime,
                   ApplyUserId = g.ApplyUserId,
                   ApplyUserBu = g.ApplyUserBu,
                   SpeakerStatus = g.Status,
                   SpeakerStatusString = status[g.Status],
                   ApplyUserBuName = g.ApplyUserBuName
               }).OrderByDescending(o => o.ApplyTime).AsQueryable();


                var pageResult = speaker.Skip(request.PageIndex * request.PageSize).Take(request.PageSize)
                    .ToList();

                //转换内容
                foreach (var item in pageResult)
                {
                    item.ApplyUserName = _identityUserRepository.FirstOrDefaultAsync(f => f.Id == item.ApplyUserId).Result?.Name;
                    //var orgs = await dataverseService.GetOrganizations(item.ApplyUserBu);
                    //if (orgs.Count != 0)
                    //    item.ApplyUserBuName = orgs.First().DepartmentName;
                }

                var result = new PagedResultDto<SpeakerListApplicationResponseDto>()
                {
                    Items = pageResult,
                    TotalCount = speaker.Count(),
                };
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError($"SpeakerService's GetSpeakerApplicationListAsync has an error : {ex.Message}");
                return null;
            }
        }


        /// <summary>
        /// 获取讲者草稿列表
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<PagedResultDto<SpeakerDraftListResponseDto>> GetSpeakerDraftListAsync(SpeakerDraftListRequestDto request)
        {
            try
            {
                var _vendorApplicationRepository = _serviceProvider.GetService<IVendorApplicationReadonlyRepository>();
                var _vendorApplicationPersonalRepository = _serviceProvider.GetService<IVendorApplicationPersonalReadonlyRepository>();
                var vendorQuery = await _vendorApplicationRepository.GetQueryableAsync();
                var vendorPersonalQuery = await _vendorApplicationPersonalRepository.GetQueryableAsync();
                var jobTitles = await _dataverseService.GetAllJobTiles(stateCode: null);
                var hospitals = await _dataverseService.GetAllHospitals(stateCode: null);
                var standardHosDeps = await _dataverseService.GetAllDepartments(stateCode: null);
                var status = EnumUtil.GetEnumIdValues<Enums.Statuses>().ToDictionary(a => (Enums.Statuses)a.Key, a => a.Value);
                var VerificationStatuses = EnumUtil.GetEnumIdValues<VerificationStatuses>().ToDictionary(a => (VerificationStatuses)a.Key, a => a.Value);
                var _consentsetvice = LazyServiceProvider.GetService<IConsentService>();


                //供应商个人链表供应商
                var speaker = vendorPersonalQuery.Join(vendorQuery, a => a.ApplicationId, b => b.Id, (a, b) => new { a.SPName, vendorQuery = b })
                    .Where(w => w.vendorQuery.ApplyUserId == CurrentUser.Id) //只查询自己的
                    .Where(w => w.vendorQuery.VendorType == VendorTypes.HCPPerson)
                    .WhereIf(request.ApplicationType.HasValue, w => w.vendorQuery.ApplicationType == request.ApplicationType.Value)
                    .WhereIf(!string.IsNullOrEmpty(request.VendorCode), p => p.vendorQuery.VendorCode.Contains(request.VendorCode))
                    .WhereIf(!string.IsNullOrEmpty(request.SPName), p => p.SPName.Contains(request.SPName))
                    .Where(w => w.vendorQuery.Status == Statuses.Saved) //草稿只能查看草稿数据
                                                                        //.WhereIf(request.Status.HasValue, p => (int)p.vendorQuery.Status == request.Status)
                    .WhereIf(request.PTId.HasValue, p => p.vendorQuery.PTId == request.PTId)
                    .WhereIf(request.HospitalId.HasValue, p => p.vendorQuery.HospitalId == request.HospitalId)
                    .WhereIf(request.StandardHosDepId.HasValue, p => p.vendorQuery.StandardHosDepId == request.StandardHosDepId)
                    .AsQueryable();
                var speakers = speaker.Select(g => new SpeakerDraftListResponseDto
                {
                    ID = g.vendorQuery.Id,
                    OpenId = g.vendorQuery.OpenId,
                    UserId = g.vendorQuery.UserId,
                    VendorCode = g.vendorQuery.VendorCode,
                    SPName = g.SPName,
                    Status = g.vendorQuery.Status,
                    StatusString = g.vendorQuery.Status != 0 ? status[g.vendorQuery.Status] : null,
                    VerificationStatus = g.vendorQuery.VerificationStatus,
                    VerificationStatusString = g.vendorQuery.VerificationStatus != 0 ? VerificationStatuses[g.vendorQuery.VerificationStatus] : null,
                    //SignedStatus = g.vendorQuery.SignedStatus,
                    //SignedStatusString = g.vendorQuery.SignedStatus ? "已签署" : "未签署",
                    BImproved = g.vendorQuery.BImproved,
                    BImprovedString = g.vendorQuery.BImproved ? "已完善" : "待完善",
                    PTId = g.vendorQuery.PTId,
                    HospitalId = g.vendorQuery.HospitalId,
                    StandardHosDepId = g.vendorQuery.StandardHosDepId,
                    SPLevel = g.vendorQuery.SPLevel,
                    CreationTime = g.vendorQuery.CreationTime,
                    BAuth = g.vendorQuery.BAuth,
                    ApplicationType = g.vendorQuery.ApplicationType
                }).OrderByDescending(o => o.CreationTime).AsQueryable();
                var pageResult = speakers.Skip(request.PageIndex * request.PageSize).Take(request.PageSize)
                    .ToList();

                //转换内容
                // 获取签署内容
                var consentSignedQuery = await LazyServiceProvider.LazyGetService<IConsentSignedReadonlyRepository>().GetListAsync();
                //获取需要检查的最新版签署版本号
                string consentInfo = _configuration["Consent:ConsentCode"];
                var consentResponse = await _consentsetvice.GetConsentInfoAsync([.. consentInfo.Split(",")]);

                foreach (var item in pageResult)
                {
                    if (item.PTId.HasValue)
                        item.PTIName = jobTitles.FirstOrDefault(f => f.Id == item.PTId.Value)?.Name;
                    if (item.HospitalId.HasValue)
                        item.HospitalName = hospitals.FirstOrDefault(P => P.Id == item.HospitalId.Value)?.Name;
                    if (item.StandardHosDepId.HasValue)
                        item.StandardHosDepName = standardHosDeps.FirstOrDefault(P => P.Id == item.StandardHosDepId.Value)?.Name;

                    //获取讲者签署记录，判断是否已经完全签署
                    var consentList = consentSignedQuery.Where(w => w.OneId == item.OpenId || w.AppUserId == item.UserId.ToString()).ToList();
                    var isConsent = HasBeenFullySigned(consentList, consentResponse);
                    item.SignedStatus = isConsent;
                    item.SignedStatusString = isConsent ? "已签署" : "待签署";
                    item.OpenId = "";//安全起见不返回该内容
                }

                var result = new PagedResultDto<SpeakerDraftListResponseDto>()
                {
                    Items = pageResult,
                    TotalCount = speakers.Count(),
                };
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError($"SpeakerService's GetSpeakerDraftListAsync has an error : {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 检查是否完全签署
        /// </summary>
        /// <returns></returns>
        private bool HasBeenFullySigned(List<ConsentSigned> consentList, List<ConsentUrlResponseDto> consentResponse)
        {
            var isConsent = false;
            if (consentResponse == null) return isConsent;
            if (consentList.Count != 0)
            {
                isConsent = true;
                foreach (var consent in consentResponse)
                {
                    var entity = consentList.OrderByDescending(o => o.CreationTime).FirstOrDefault(w => w.ConsentCode == consent.Data.ConsentCode);
                    //如果不存在说明没有签署，直接返回
                    if (entity == null)
                    {
                        isConsent = false;
                        break;
                    }
                    //如果存在但是版本不一样，说明签署的是老版本，返回false
                    if (entity.ConsentVersion != consent.Data.Version.ToString())
                    {
                        isConsent = false;
                        break;
                    }
                }
            }
            return isConsent;
        }

        /// <summary>
        /// 讲者草稿删除
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> DeleteSpeakerDraftAsync(DeleteSpeakerDraftRequestDto request)
        {
            try
            {
                var vendorApplication = _serviceProvider.GetService<IVendorApplicationRepository>();

                foreach (var item in request.ID)
                {
                    var vendorApplicationDetail = await vendorApplication.FirstOrDefaultAsync(f => f.Id == item);
                    if (vendorApplicationDetail == null)
                        return MessageResult.FailureResult($"该ID{item}没有相关记录");
                    if (vendorApplicationDetail.Status != Enums.Statuses.Saved)
                        return MessageResult.FailureResult($"该ID{vendorApplicationDetail.VendorCode}已经不属于草稿，不能删除");
                }

                await vendorApplication.DeleteManyAsync(request.ID);
                return MessageResult.SuccessResult();
            }
            catch (Exception ex)
            {
                _logger.LogError($"SpeakerService's DeleteSpeakerDraftAsync has an error : {ex.Message}");
                return MessageResult.FailureResult("删除失败");
            }
        }

        /// <summary>
        /// 获取讲者详情
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="isRetuenKey">The is return key.</param>
        /// <returns></returns>
        public async Task<MessageResult> GetSpeakerDetailAsync(Guid request, bool isRetuenKey = false)
        {
            try
            {
                var vendorQuery = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();
                var vendorPersonalQuery = await LazyServiceProvider.LazyGetService<IVendorPersonalRepository>().GetQueryableAsync();
                var financialQuery = await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync();
                var jobTitles = await _dataverseService.GetAllJobTiles(stateCode: null);
                var hospitals = await _dataverseService.GetAllHospitals(stateCode: null);
                var standardHosDeps = await _dataverseService.GetAllDepartments(stateCode: null);
                var attachmentQuery = await _attachmentRepository.GetQueryableAsync();
                var getAllCity = await _dataverseService.GetAllCity(stateCode: null);
                var getAllProvince = await _dataverseService.GetAllProvince(stateCode: null);
                var status = EnumUtil.GetEnumIdValues<VendorStatus>().ToDictionary(a => (VendorStatus)a.Key, a => a.Value);
                var _consentsetvice = LazyServiceProvider.GetService<IConsentService>();
                var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");

                //供应商个人链表供应商
                var speaker = vendorPersonalQuery
                .Join(vendorQuery, a => a.VendorId, b => b.Id, (a, b) => new { vendorPersonalQuery = a, vendorQuery = b })
                .FirstOrDefault(f => f.vendorQuery.Id == request);

                if (speaker == null) return MessageResult.FailureResult($"未找到讲者数据, 请核实Id：{request}");

                //这时再来获取Dtaverse，前面获取可能浪费性能
                var dicHcpLevels = await _dataverseService.GetDictionariesAsync(DictionaryType.HCPLevel, null);

                var speakerDetail = new SpeakerDetailResponseDto();
                speakerDetail.IsDraft = false;
                speakerDetail.ID = speaker.vendorQuery.Id;
                speakerDetail.ApplicationId = speaker.vendorQuery.ApplicationId;
                speakerDetail.DraftVersion = speaker.vendorQuery.DraftVersion;
                //speakerDetail.SignedStatus = speaker.vendorQuery.SignedStatus;
                //speakerDetail.SignedVersion = speaker.vendorQuery.SignedVersion;
                speakerDetail.Status = speaker.vendorQuery.Status;
                speakerDetail.StatusString = speaker.vendorQuery.Status != 0 ? status[speaker.vendorQuery.Status] : null;
                speakerDetail.VendorCode = speaker.vendorQuery.VendorCode;
                speakerDetail.VendorType = speaker.vendorQuery.VendorType;
                speakerDetail.SPName = speaker.vendorPersonalQuery.SPName;
                //先默认从Vendor表取，后续有ID的情况下从PP获取
                speakerDetail.PTIName = speaker.vendorQuery.PTName;
                speakerDetail.HospitalName = speaker.vendorQuery.HospitalName;
                speakerDetail.StandardHosDepName = speaker.vendorQuery.StandardHosDepName;
                //职称
                if (speaker.vendorQuery.PTId.HasValue)
                {
                    speakerDetail.PTId = speaker.vendorQuery.PTId;
                    speakerDetail.PTIName = jobTitles.FirstOrDefault(f => f.Id == speakerDetail.PTId)?.Name;
                }
                //医院
                HospitalDto hospital = null;
                if (speaker.vendorQuery.HospitalId.HasValue)
                {
                    speakerDetail.HospitalId = speaker.vendorQuery.HospitalId;
                    hospital = hospitals.FirstOrDefault(P => P.Id == speakerDetail.HospitalId);
                    speakerDetail.HospitalName = hospital?.Name;
                }
                #region  EPD医院
                //EPD医院
                var epdHospital = await GetEpdHospitalAsync(hospital, speaker.vendorQuery.EpdHospitalId, speaker.vendorQuery.HospitalName);
                if (epdHospital != null)
                {
                    speakerDetail.EpdHospitalCode = epdHospital.HospitalCode;
                    speakerDetail.EpdHospitalName = epdHospital.Name;
                }
                #endregion

                //科室
                if (speaker.vendorQuery.StandardHosDepId.HasValue)
                {
                    speakerDetail.StandardHosDepId = speaker.vendorQuery.StandardHosDepId;
                    speakerDetail.StandardHosDepName = standardHosDeps.FirstOrDefault(P => P.Id == speakerDetail.StandardHosDepId)?.Name;
                }
                speakerDetail.HosDepartment = speaker.vendorQuery.HosDepartment;
                speakerDetail.EpdId = speaker.vendorQuery.EpdId;
                speakerDetail.CertificateCode = speaker.vendorQuery.CertificateCode;
                speakerDetail.SPLevel = speaker.vendorQuery.SPLevel;
                speakerDetail.SPLevelName = dicHcpLevels.FirstOrDefault(a => a.Code.Equals(speaker.vendorQuery.SPLevel, StringComparison.OrdinalIgnoreCase))?.Name;
                speakerDetail.AcademicLevel = speaker.vendorQuery.AcademicLevel;
                speakerDetail.AcademicPositionJson = !string.IsNullOrEmpty(speaker.vendorQuery.AcademicPosition) ? JsonConvert.DeserializeObject<List<AcademicPositionDto>>(speaker.vendorQuery.AcademicPosition) : null;
                speakerDetail.IsAcademician = speaker.vendorQuery.IsAcademician;
                speakerDetail.FormerBPMAcademicPosition = speaker.vendorQuery.FormerBPMAcademicPosition;
                //个人信息
                speakerDetail.HandPhone = speaker.vendorQuery.HandPhone;

                if (!string.IsNullOrEmpty(speaker.vendorPersonalQuery.CardPic))
                {
                    speakerDetail.CardPic = attachmentQuery.Where(w => w.Id == Guid.Parse(speaker.vendorPersonalQuery.CardPic))
                    .Select(s => new AttachmentInformation
                    {
                        AttachmentId = s.Id,
                        FileName = s.FileName,
                        FileSize = s.Size.ToFileSizeString(),
                        Suffix = s.Suffix,
                        FilePath = s.FilePath
                    }).FirstOrDefault();
                }
                var randomKey = GenerateRandomKey();

                speakerDetail.CardType = speaker.vendorPersonalQuery.CardType;
                speakerDetail.Sex = speaker.vendorPersonalQuery.Sex;
                speakerDetail.CardNo = speaker.vendorPersonalQuery.CardNo;
                if (!string.IsNullOrEmpty(speakerDetail.CardNo))
                {
                    speakerDetail.CardNo = AesHelper.Decryption(speakerDetail.CardNo, insightKey);
                    //需要返回Key时，先解密再使用随机生成的Key加密CardNo返回前端
                    if (isRetuenKey)
                        speakerDetail.CardNo = AesHelper.Encryption(speakerDetail.CardNo, randomKey);
                }
                speakerDetail.Province = speaker.vendorPersonalQuery.Province;
                speakerDetail.City = speaker.vendorPersonalQuery.City;
                speakerDetail.ProvinceCity = [speakerDetail.Province, speakerDetail.City];
                speakerDetail.ProvinceCityName = getAllProvince.FirstOrDefault(f => f.Code == speakerDetail.Province)?.Name + "/" + getAllCity.FirstOrDefault(f => f.Code == speakerDetail.City)?.Name;
                speakerDetail.PostCode = speaker.vendorPersonalQuery.PostCode;
                speakerDetail.Address = speaker.vendorPersonalQuery.Address;
                //需要返回Key时，使用随机生成的Key加密Address返回前端
                if (isRetuenKey)
                {
                    if (!string.IsNullOrEmpty(speakerDetail.Address))
                        speakerDetail.Address = AesHelper.Encryption(speakerDetail.Address, randomKey);
                }
                //银行信息
                speakerDetail.BankCode = speaker.vendorQuery.BankCode;
                speakerDetail.BankName = speakerDetail.BankCode;
                speakerDetail.BankCardNo = speaker.vendorQuery.BankCardNo;
                if (!string.IsNullOrEmpty(speakerDetail.BankCardNo))
                {
                    speakerDetail.BankCardNo = AesHelper.Decryption(speakerDetail.BankCardNo, insightKey);
                    //需要返回Key时，先解密再使用随机生成的Key加密BankCardNo返回前端
                    if (isRetuenKey)
                        speakerDetail.BankCardNo = AesHelper.Encryption(speakerDetail.BankCardNo, randomKey);
                }
                if (!string.IsNullOrEmpty(speaker.vendorQuery.BankCity))
                {
                    speakerDetail.BankCity = [.. speaker.vendorQuery.BankCity.Split(",")];
                    speakerDetail.BankCityName = getAllProvince.FirstOrDefault(f => f.Code == speaker.vendorQuery.BankCity.Split(",")[0])?.Name + "/" + getAllCity.FirstOrDefault(f => f.Code == speaker.vendorQuery.BankCity.Split(",")[1])?.Name;
                }
                speakerDetail.BankNo = speaker.vendorQuery.BankNo;
                speakerDetail.BankSwiftCode = speaker.vendorQuery.BankSwiftCode;
                if (!string.IsNullOrEmpty(speaker.vendorQuery.BankCardImg))
                {
                    speakerDetail.BankCardImg = attachmentQuery.Where(w => w.Id == Guid.Parse(speaker.vendorQuery.BankCardImg))
                    .Select(s => new AttachmentInformation
                    {
                        AttachmentId = s.Id,
                        FileName = s.FileName,
                        FileSize = s.Size.ToFileSizeString(),
                        Suffix = s.Suffix,
                        FilePath = s.FilePath
                    }).FirstOrDefault();
                }

                //财务信息
                speakerDetail.FinancialInformation = financialQuery.Where(w => w.VendorId == speakerDetail.ID)
                    .Select(s => new FinancialInformation
                    {
                        ID = s.Id,
                        Company = s.Company,
                        Currency = s.Currency,
                        VendorCode = s.VendorCode,
                        AbbottBank = s.AbbottBank,
                        VendorType = s.VendorType,
                        Division = s.Division,
                        PayType = s.PayType,
                        CountryCode = s.CountryCode,
                        BankType = s.BankType,
                        FinancialVendorStatus = s.FinancialVendorStatus,
                        PaymentTerm = s.PaymentTerm,
                        PaymentTermName = _commonService.GetPaymentTermName(s.Company, s.PaymentTerm),
                        Flag = true
                    })
                    .ToList();

                //附件信息
                var attachmentIds = !string.IsNullOrEmpty(speaker.vendorQuery.AttachmentInformation) ? speaker.vendorQuery.AttachmentInformation?.Split(',').Select(x => Guid.Parse(x)).ToList() : null;
                speakerDetail.AttachmentInformation = attachmentQuery
                    .Where(x => attachmentIds.Contains(x.Id))
                    .Select(s => new AttachmentInformation
                    {
                        AttachmentId = s.Id,
                        FileName = s.FileName,
                        FileSize = s.Size.ToFileSizeString(),
                        Suffix = s.Suffix,
                        FilePath = s.FilePath
                    })
                    .ToList();
                //DPS信息
                var attachmentDPSIds = !string.IsNullOrEmpty(speaker.vendorQuery.DPSCheck) ? speaker.vendorQuery.DPSCheck?.Split(',').Select(x => Guid.Parse(x)).ToList() : null;
                speakerDetail.DPSCheck = attachmentQuery
                    .Where(x => attachmentDPSIds.Contains(x.Id))
                    .Select(s => new AttachmentInformation
                    {
                        AttachmentId = s.Id,
                        FileName = s.FileName,
                        FileSize = s.Size.ToFileSizeString(),
                        Suffix = s.Suffix,
                        FilePath = s.FilePath
                    })
                    .ToList();

                // 获取签署内容
                var consentSignedQuery = await LazyServiceProvider.LazyGetService<IConsentSignedRepository>().GetListAsync();
                //获取需要检查的最新版签署版本号
                string consentInfo = _configuration["Consent:ConsentCode"];
                var consentResponse = await _consentsetvice.GetConsentInfoAsync([.. consentInfo.Split(",")]);
                //获取讲者签署记录，判断是否已经完全签署
                var consentList = consentSignedQuery.Where(w => w.OneId == speaker.vendorQuery.OpenId || w.AppUserId == speaker.vendorQuery.UserId.ToString()).ToList();
                var isConsent = HasBeenFullySigned(consentList, consentResponse);
                speakerDetail.SignedStatus = isConsent;
                speakerDetail.SignedStatusString = isConsent ? "已签署" : "待签署";
                //需要返回Key时，将随机生成的Key返回前端
                if (isRetuenKey)
                    return MessageResult.SuccessResult(speakerDetail, randomKey);
                else
                    return MessageResult.SuccessResult(speakerDetail);
            }
            catch (Exception ex)
            {
                _logger.LogError($"SpeakerService's GetSpeakerDetailAsync has an error : {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取讲者详情
        /// </summary>
        /// <param name="vendorCode"></param>
        /// <returns></returns>
        public async Task<MessageResult> GetSpeakerDetailByCodeAsync(string request)
        {
            try
            {
                var vendorQuery = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();
                var vendorPersonalQuery = await LazyServiceProvider.LazyGetService<IVendorPersonalRepository>().GetQueryableAsync();
                var financialQuery = await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync();
                var jobTitles = await _dataverseService.GetAllJobTiles();
                var hospitals = await _dataverseService.GetAllHospitals();
                var standardHosDeps = await _dataverseService.GetAllDepartments();
                var attachmentQuery = await _attachmentRepository.GetQueryableAsync();
                var getAllCity = await _dataverseService.GetAllCity();
                var getAllProvince = await _dataverseService.GetAllProvince();
                var status = EnumUtil.GetEnumIdValues<VendorStatus>().ToDictionary(a => (VendorStatus)a.Key, a => a.Value);
                var _consentsetvice = LazyServiceProvider.GetService<IConsentService>();
                var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");

                //供应商个人链表供应商
                var speaker = vendorPersonalQuery
                .Join(vendorQuery, a => a.VendorId, b => b.Id, (a, b) => new { vendorPersonalQuery = a, vendorQuery = b })
                .FirstOrDefault(f => f.vendorQuery.VendorCode == request);

                if (speaker == null) return MessageResult.FailureResult($"未找到讲者数据, 请核实vendor code：{request}");

                //这时再来获取Dtaverse，前面获取可能浪费性能
                var dicHcpLevels = await _dataverseService.GetDictionariesAsync(DictionaryType.HCPLevel);

                var speakerDetail = new SpeakerDetailResponseDto();
                speakerDetail.IsDraft = false;
                speakerDetail.ID = speaker.vendorQuery.Id;
                speakerDetail.ApplicationId = speaker.vendorQuery.ApplicationId;
                speakerDetail.DraftVersion = speaker.vendorQuery.DraftVersion;
                //speakerDetail.SignedStatus = speaker.vendorQuery.SignedStatus;
                //speakerDetail.SignedVersion = speaker.vendorQuery.SignedVersion;
                speakerDetail.Status = speaker.vendorQuery.Status;
                speakerDetail.StatusString = speaker.vendorQuery.Status != 0 ? status[speaker.vendorQuery.Status] : null;
                speakerDetail.VendorCode = speaker.vendorQuery.VendorCode;
                speakerDetail.VendorType = speaker.vendorQuery.VendorType;
                speakerDetail.SPName = speaker.vendorPersonalQuery.SPName;

                //职称
                if (speaker.vendorQuery.PTId.HasValue)
                {
                    speakerDetail.PTId = speaker.vendorQuery.PTId;
                    speakerDetail.PTIName = jobTitles.FirstOrDefault(f => f.Id == speakerDetail.PTId)?.Name;
                }
                //医院
                HospitalDto hospital = null;
                if (speaker.vendorQuery.HospitalId.HasValue)
                {
                    speakerDetail.HospitalId = speaker.vendorQuery.HospitalId;
                    hospital = hospitals.FirstOrDefault(P => P.Id == speakerDetail.HospitalId);
                    speakerDetail.HospitalName = hospital?.Name;
                }

                #region EPD医院
                //EPD医院
                var epdHospital = await GetEpdHospitalAsync(hospital, speaker.vendorQuery.EpdHospitalId, speaker.vendorQuery.HospitalName);
                if (epdHospital != null)
                {
                    speakerDetail.EpdHospitalCode = epdHospital.HospitalCode;
                    speakerDetail.EpdHospitalName = epdHospital.Name;
                }
                #endregion

                //科室
                if (speaker.vendorQuery.StandardHosDepId.HasValue)
                {
                    speakerDetail.StandardHosDepId = speaker.vendorQuery.StandardHosDepId;
                    speakerDetail.StandardHosDepName = standardHosDeps.FirstOrDefault(P => P.Id == speakerDetail.StandardHosDepId)?.Name;
                }
                speakerDetail.HosDepartment = speaker.vendorQuery.HosDepartment;
                speakerDetail.EpdId = speaker.vendorQuery.EpdId;
                speakerDetail.CertificateCode = speaker.vendorQuery.CertificateCode;
                speakerDetail.SPLevel = speaker.vendorQuery.SPLevel;
                speakerDetail.SPLevelName = dicHcpLevels.FirstOrDefault(a => a.Code.Equals(speaker.vendorQuery.SPLevel, StringComparison.OrdinalIgnoreCase))?.Name;
                speakerDetail.AcademicLevel = speaker.vendorQuery.AcademicLevel;
                speakerDetail.AcademicPositionJson = !string.IsNullOrEmpty(speaker.vendorQuery.AcademicPosition) ? JsonConvert.DeserializeObject<List<AcademicPositionDto>>(speaker.vendorQuery.AcademicPosition) : null; speakerDetail.IsAcademician = speaker.vendorQuery.IsAcademician;
                speakerDetail.IsAcademician = speaker.vendorQuery.IsAcademician;
                speakerDetail.FormerBPMAcademicPosition = speaker.vendorQuery.FormerBPMAcademicPosition;
                //个人信息
                speakerDetail.HandPhone = speaker.vendorQuery.HandPhone;
                if (!string.IsNullOrEmpty(speaker.vendorPersonalQuery.CardPic))
                {
                    speakerDetail.CardPic = attachmentQuery.Where(w => w.Id == Guid.Parse(speaker.vendorPersonalQuery.CardPic))
                    .Select(s => new AttachmentInformation
                    {
                        AttachmentId = s.Id,
                        FileName = s.FileName,
                        FileSize = s.Size.ToFileSizeString(),
                        Suffix = s.Suffix,
                        FilePath = s.FilePath
                    }).FirstOrDefault();
                }

                speakerDetail.CardType = speaker.vendorPersonalQuery.CardType;
                speakerDetail.Sex = speaker.vendorPersonalQuery.Sex;
                speakerDetail.CardNo = speaker.vendorPersonalQuery.CardNo;
                if (!string.IsNullOrEmpty(speakerDetail.CardNo))
                    speakerDetail.CardNo = AesHelper.Decryption(speakerDetail.CardNo, insightKey);
                speakerDetail.Province = speaker.vendorPersonalQuery.Province;
                speakerDetail.City = speaker.vendorPersonalQuery.City;
                speakerDetail.ProvinceCity = [speakerDetail.Province, speakerDetail.City];
                speakerDetail.ProvinceCityName = getAllProvince.FirstOrDefault(f => f.Code == speakerDetail.Province)?.Name + "/" + getAllCity.FirstOrDefault(f => f.Code == speakerDetail.City)?.Name;
                speakerDetail.PostCode = speaker.vendorPersonalQuery.PostCode;
                speakerDetail.Address = speaker.vendorPersonalQuery.Address;
                //银行信息
                speakerDetail.BankCode = speaker.vendorQuery.BankCode;
                speakerDetail.BankName = speakerDetail.BankCode;
                speakerDetail.BankCardNo = speaker.vendorQuery.BankCardNo;
                if (!string.IsNullOrEmpty(speakerDetail.BankCardNo))
                    speakerDetail.BankCardNo = AesHelper.Decryption(speakerDetail.BankCardNo, insightKey);
                if (!string.IsNullOrEmpty(speaker.vendorQuery.BankCity))
                {
                    speakerDetail.BankCity = [.. speaker.vendorQuery.BankCity.Split(",")];
                    speakerDetail.BankCityName = getAllProvince.FirstOrDefault(f => f.Code == speaker.vendorQuery.BankCity.Split(",")[0])?.Name + "/" + getAllCity.FirstOrDefault(f => f.Code == speaker.vendorQuery.BankCity.Split(",")[1])?.Name;
                }
                speakerDetail.BankNo = speaker.vendorQuery.BankNo;
                speakerDetail.BankSwiftCode = speaker.vendorQuery.BankSwiftCode;
                if (!string.IsNullOrEmpty(speaker.vendorQuery.BankCardImg))
                {
                    speakerDetail.BankCardImg = attachmentQuery.Where(w => w.Id == Guid.Parse(speaker.vendorQuery.BankCardImg))
                    .Select(s => new AttachmentInformation
                    {
                        AttachmentId = s.Id,
                        FileName = s.FileName,
                        FileSize = s.Size.ToFileSizeString(),
                        Suffix = s.Suffix,
                        FilePath = s.FilePath
                    }).FirstOrDefault();
                }

                //财务信息
                speakerDetail.FinancialInformation = financialQuery.Where(w => w.VendorId == speakerDetail.ID)
                    .Select(s => new FinancialInformation
                    {
                        ID = s.Id,
                        Company = s.Company,
                        Currency = s.Currency,
                        VendorCode = s.VendorCode,
                        AbbottBank = s.AbbottBank,
                        VendorType = s.VendorType,
                        Division = s.Division,
                        PayType = s.PayType,
                        CountryCode = s.CountryCode,
                        BankType = s.BankType,
                        FinancialVendorStatus = s.FinancialVendorStatus,
                        PaymentTerm = s.PaymentTerm,
                        PaymentTermName = _commonService.GetPaymentTermName(s.Company, s.PaymentTerm),
                        Flag = true
                    })
                    .ToList();

                //附件信息
                var attachmentIds = !string.IsNullOrEmpty(speaker.vendorQuery.AttachmentInformation) ? speaker.vendorQuery.AttachmentInformation?.Split(',').Select(x => Guid.Parse(x)).ToList() : null;
                speakerDetail.AttachmentInformation = attachmentQuery
                    .Where(x => attachmentIds.Contains(x.Id))
                    .Select(s => new AttachmentInformation
                    {
                        AttachmentId = s.Id,
                        FileName = s.FileName,
                        FileSize = s.Size.ToFileSizeString(),
                        Suffix = s.Suffix,
                        FilePath = s.FilePath
                    })
                    .ToList();
                //DPS信息
                var attachmentDPSIds = !string.IsNullOrEmpty(speaker.vendorQuery.DPSCheck) ? speaker.vendorQuery.DPSCheck?.Split(',').Select(x => Guid.Parse(x)).ToList() : null;
                speakerDetail.DPSCheck = attachmentQuery
                    .Where(x => attachmentDPSIds.Contains(x.Id))
                    .Select(s => new AttachmentInformation
                    {
                        AttachmentId = s.Id,
                        FileName = s.FileName,
                        FileSize = s.Size.ToFileSizeString(),
                        Suffix = s.Suffix,
                        FilePath = s.FilePath
                    })
                    .ToList();

                // 获取签署内容
                var consentSignedQuery = await LazyServiceProvider.LazyGetService<IConsentSignedRepository>().GetListAsync();
                //获取需要检查的最新版签署版本号
                string consentInfo = _configuration["Consent:ConsentCode"];
                var consentResponse = await _consentsetvice.GetConsentInfoAsync([.. consentInfo.Split(",")]);
                //获取讲者签署记录，判断是否已经完全签署
                var consentList = consentSignedQuery.Where(w => w.OneId == speaker.vendorQuery.OpenId).ToList();
                var isConsent = HasBeenFullySigned(consentList, consentResponse);
                speakerDetail.SignedStatus = isConsent;
                speakerDetail.SignedStatusString = isConsent ? "已签署" : "待签署";
                return MessageResult.SuccessResult(speakerDetail);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"SpeakerService's GetSpeakerDetailAsync has an error : {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取讲者草稿详情(微信可以使用ApplicationCode调用)
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> GetSpeakerDraftDetailAsync(Guid? request)
        {
            try
            {
                var vendorQuery = await LazyServiceProvider.LazyGetService<IVendorApplicationRepository>().GetQueryableAsync();
                var vendorPersonalQuery = await LazyServiceProvider.LazyGetService<IVendorApplicationPersonalRepository>().GetQueryableAsync();
                var financialQuery = await LazyServiceProvider.LazyGetService<IVendorApplicationFinancialRepository>().GetQueryableAsync();
                var jobTitles = await _dataverseService.GetAllJobTiles();
                var hospitals = await _dataverseService.GetAllHospitals();
                var standardHosDeps = await _dataverseService.GetAllDepartments();
                var getAllCity = await _dataverseService.GetAllCity();
                var getAllProvince = await _dataverseService.GetAllProvince();
                var attachmentQuery = await _attachmentRepository.GetQueryableAsync();
                var status = EnumUtil.GetEnumIdValues<Enums.Statuses>().ToDictionary(a => (Enums.Statuses)a.Key, a => a.Value);
                var VerificationStatus = EnumUtil.GetEnumIdValues<Enums.VerificationStatuses>().ToDictionary(a => (Enums.VerificationStatuses)a.Key, a => a.Value);
                var _identityUserRepository = _serviceProvider.GetService<IRepository<IdentityUser, Guid>>();
                var commonService = LazyServiceProvider.LazyGetService<ICommonService>();
                var _consentsetvice = LazyServiceProvider.GetService<IConsentService>();
                var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
                var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");

                //供应商个人链表供应商
                var speaker = vendorPersonalQuery
                .Join(vendorQuery, a => a.ApplicationId, b => b.Id, (a, b) => new { vendorPersonalQuery = a, vendorQuery = b })
                .WhereIf(request.HasValue, p => p.vendorQuery.Id == request)
                .FirstOrDefault();

                if (speaker == null) return MessageResult.FailureResult($"未找到讲者数据, 请核实Id：{request}");

                var speakerDetail = new SpeakerDraftDetailResponseDto();
                speakerDetail.IsDraft = true;
                speakerDetail.ID = speaker.vendorQuery.Id;
                speakerDetail.VendorType = VendorTypes.HCPPerson;
                speakerDetail.ApplicationType = speaker.vendorQuery.ApplicationType;
                speakerDetail.ApplicationCode = speaker.vendorQuery.ApplicationCode;
                speakerDetail.DraftVersion = speaker.vendorQuery.DraftVersion;
                speakerDetail.BAuth = speaker.vendorQuery.BAuth;
                speakerDetail.BImproved = speaker.vendorQuery.BImproved;
                //speakerDetail.SignedStatus = speaker.vendorQuery.SignedStatus;
                speakerDetail.ApplyTime = speaker.vendorQuery.ApplyTime.ToString("yyyy-MM-dd HH:mm");
                //speakerDetail.SignedVersion = speaker.vendorQuery.SignedVersion;
                speakerDetail.Status = speaker.vendorQuery.Status;
                speakerDetail.StatusString = speaker.vendorQuery.Status != 0 ? status[speaker.vendorQuery.Status] : null;
                speakerDetail.VerificationStatus = speaker.vendorQuery.VerificationStatus;
                speakerDetail.VerificationStatusString = speaker.vendorQuery.VerificationStatus != 0 ? VerificationStatus[speaker.vendorQuery.VerificationStatus] : null;
                speakerDetail.ApplyUserId = speaker.vendorQuery.ApplyUserId;
                speakerDetail.ApplyUserName = _identityUserRepository.FirstOrDefaultAsync(f => f.Id == speakerDetail.ApplyUserId).Result?.Name;
                speakerDetail.ApplyUserBu = !string.IsNullOrEmpty(speaker.vendorQuery.ApplyUserBu) ? Guid.Parse(speaker.vendorQuery.ApplyUserBu) : Guid.Empty;
                //var applyUserBuToDept = await commonService.GetOrganizationDept(speaker.vendorQuery.ApplyUserId);
                //speakerDetail.ApplyUserBuName = applyUserBuToDept?.FirstOrDefault(x => x.Key == speakerDetail.ApplyUserBu).Value?.Split("->").Last();
                //var orgs = await dataverseService.GetOrganizations(speakerDetail.ApplyUserBu.ToString());
                //if (orgs.Count != 0)
                //    speakerDetail.ApplyUserBuName = orgs.First().DepartmentName;
                speakerDetail.ApplyUserBuName = speaker.vendorQuery.ApplyUserBuName;
                speakerDetail.CreationTime = speaker.vendorQuery.CreationTime.ToString("yyyy-MM-dd HH:mm:ss");
                speakerDetail.VendorCode = speaker.vendorQuery.VendorCode;
                speakerDetail.SPName = speaker.vendorPersonalQuery.SPName;
                //先默认从Vendor表取，后续有ID的情况下从PP获取
                speakerDetail.PTIName = speaker.vendorQuery.PTName;
                speakerDetail.HospitalName = speaker.vendorQuery.HospitalName;
                speakerDetail.StandardHosDepName = speaker.vendorQuery.StandardHosDepName;
                //职称
                if (speaker.vendorQuery.PTId.HasValue)
                {
                    speakerDetail.PTId = speaker.vendorQuery.PTId;
                    speakerDetail.PTIName = jobTitles.FirstOrDefault(f => f.Id == speakerDetail.PTId)?.Name;
                }
                //医院
                HospitalDto hospital = null;
                if (speaker.vendorQuery.HospitalId.HasValue)
                {
                    speakerDetail.HospitalId = speaker.vendorQuery.HospitalId;
                    hospital = hospitals.FirstOrDefault(P => P.Id == speakerDetail.HospitalId);
                    speakerDetail.HospitalName = hospital?.Name;
                }

                #region EPD医院
                //EPD医院
                var epdHospital = await GetEpdHospitalAsync(hospital, speaker.vendorQuery.EpdHospitalId, speaker.vendorQuery.HospitalName);
                if (epdHospital != null)
                {
                    speakerDetail.EpdHospitalCode = epdHospital.HospitalCode;
                    speakerDetail.EpdHospitalName = epdHospital.Name;
                }
                #endregion

                //科室
                if (speaker.vendorQuery.StandardHosDepId.HasValue)
                {
                    speakerDetail.StandardHosDepId = speaker.vendorQuery.StandardHosDepId;
                    speakerDetail.StandardHosDepName = standardHosDeps.FirstOrDefault(P => P.Id == speakerDetail.StandardHosDepId)?.Name;
                }
                speakerDetail.HosDepartment = speaker.vendorQuery.HosDepartment;
                speakerDetail.EpdId = speaker.vendorQuery.EpdId;
                speakerDetail.CertificateCode = speaker.vendorQuery.CertificateCode;
                speakerDetail.SPLevel = speaker.vendorQuery.SPLevel;
                speakerDetail.AcademicLevel = speaker.vendorQuery.AcademicLevel;
                speakerDetail.IsAcademician = speaker.vendorQuery.IsAcademician;
                speakerDetail.AcademicPositionJson = !string.IsNullOrEmpty(speaker.vendorQuery.AcademicPosition) ? JsonConvert.DeserializeObject<List<AcademicPositionDto>>(speaker.vendorQuery.AcademicPosition) : null;//个人信息
                speakerDetail.FormerBPMAcademicPosition = speaker.vendorQuery.FormerBPMAcademicPosition;
                speakerDetail.HandPhone = speaker.vendorQuery.HandPhone;
                if (!string.IsNullOrEmpty(speaker.vendorQuery.MobileEncrypt)) //MobileEncrypt
                {
                    string aesKey = _configuration["Integrations:EPD_HCP_Portal:AesKey256"];
                    speakerDetail.ReservedMobilePhone = JsonConvert.DeserializeObject<string[]>(AesHelper.DecryptionIV(speaker.vendorQuery.MobileEncrypt, aesKey));
                }
                if (!string.IsNullOrEmpty(speaker.vendorPersonalQuery.CardPic))
                {
                    speakerDetail.CardPic = attachmentQuery.Where(w => w.Id == Guid.Parse(speaker.vendorPersonalQuery.CardPic))
                    .Select(s => new AttachmentInformation
                    {
                        AttachmentId = s.Id,
                        FileName = s.FileName,
                        FileSize = s.Size.ToFileSizeString(),
                        Suffix = s.Suffix,
                        FilePath = s.FilePath
                    }).FirstOrDefault();
                }

                speakerDetail.CardType = speaker.vendorPersonalQuery.CardType;
                speakerDetail.Sex = speaker.vendorPersonalQuery.Sex;
                speakerDetail.CardNo = speaker.vendorPersonalQuery.CardNo;
                if (!string.IsNullOrEmpty(speakerDetail.CardNo))
                    speakerDetail.CardNo = AesHelper.Decryption(speakerDetail.CardNo, insightKey);
                speakerDetail.Province = speaker.vendorPersonalQuery.Province;
                speakerDetail.City = speaker.vendorPersonalQuery.City;
                speakerDetail.ProvinceCity = [speakerDetail.Province, speakerDetail.City];
                speakerDetail.ProvinceCityName = getAllProvince.FirstOrDefault(f => f.Code == speakerDetail.Province)?.Name + "/" + getAllCity.FirstOrDefault(f => f.Code == speakerDetail.City)?.Name;
                speakerDetail.PostCode = speaker.vendorPersonalQuery.PostCode;
                speakerDetail.Address = speaker.vendorPersonalQuery.Address;
                //银行信息
                speakerDetail.BankCode = speaker.vendorQuery.BankCode;
                speakerDetail.BankName = speakerDetail.BankCode;
                speakerDetail.BankCardNo = speaker.vendorQuery.BankCardNo;
                if (!string.IsNullOrEmpty(speakerDetail.BankCardNo))
                    speakerDetail.BankCardNo = AesHelper.Decryption(speakerDetail.BankCardNo, insightKey);
                if (!string.IsNullOrEmpty(speaker.vendorQuery.BankCity))
                {
                    speakerDetail.BankCity = [.. speaker.vendorQuery.BankCity.Split(",")];
                    speakerDetail.BankCityName = getAllProvince.FirstOrDefault(f => f.Code == speaker.vendorQuery.BankCity.Split(",")[0])?.Name + "/" + getAllCity.FirstOrDefault(f => f.Code == speaker.vendorQuery.BankCity.Split(",")[1])?.Name;
                }
                speakerDetail.BankNo = speaker.vendorQuery.BankNo;
                speakerDetail.BankSwiftCode = speaker.vendorQuery.BankSwiftCode;
                if (!string.IsNullOrEmpty(speaker.vendorQuery.BankCardImg))
                {
                    speakerDetail.BankCardImg = attachmentQuery.Where(w => w.Id == Guid.Parse(speaker.vendorQuery.BankCardImg))
                    .Select(s => new AttachmentInformation
                    {
                        AttachmentId = s.Id,
                        FileName = s.FileName,
                        FileSize = s.Size.ToFileSizeString(),
                        Suffix = s.Suffix,
                        FilePath = s.FilePath
                    }).FirstOrDefault();
                }

                //获取公司内容
                var companyList = await _dataverseService.GetCompanyList();
                var companyCurrency = await _dataverseService.GetCompanyCurrencyList();
                //财务信息
                var FinancialInformation = financialQuery.Where(w => w.ApplicationId == speakerDetail.ID)
                    .Select(s => new FinancialInformation
                    {
                        ID = s.Id,
                        Company = s.Company,
                        Currency = s.Currency,
                        VendorCode = s.VendorCode,
                        AbbottBank = s.AbbottBank,
                        VendorType = s.VendorType,
                        Division = s.Division,
                        PayType = s.PayType,
                        CountryCode = s.CountryCode,
                        BankType = s.BankType,
                        FinancialVendorStatus = s.FinancialVendorStatus,
                        PaymentTerm = s.PaymentTerm,
                        PaymentTermName = _commonService.GetPaymentTermName(s.Company, s.PaymentTerm),
                        Flag = false
                    })
                    .ToList();
                var vendorOldData = string.IsNullOrEmpty(speaker.vendorQuery.UpdatePreJson) ? null : JsonConvert.DeserializeObject<SpeakerDetailResponseDto>(speaker.vendorQuery.UpdatePreJson);
                foreach (var item in FinancialInformation)
                {
                    item.CompanyName = companyList.FirstOrDefault(f => f.CompanyCode == item.Company) != null ? companyList.FirstOrDefault(f => f.CompanyCode == item.Company).CompanyName : "";
                    item.CurrencyName = companyCurrency.FirstOrDefault(f => f.Code == item.Currency) != null ? companyCurrency.FirstOrDefault(f => f.Code == item.Currency).Name : "";
                    item.Flag = vendorOldData != null && (vendorOldData.FinancialInformation.Select(s => s.Company).Contains(item.Company));
                }
                speakerDetail.FinancialInformation = FinancialInformation;

                //附件信息
                var attachmentIds = !string.IsNullOrEmpty(speaker.vendorQuery.AttachmentInformation) ? speaker.vendorQuery.AttachmentInformation?.Split(',').Select(x => Guid.Parse(x)).ToList() : null;
                speakerDetail.AttachmentInformation = attachmentQuery
                    .Where(x => attachmentIds.Contains(x.Id))
                    .Select(s => new AttachmentInformation
                    {
                        AttachmentId = s.Id,
                        FileName = s.FileName,
                        FileSize = s.Size.ToFileSizeString(),
                        Suffix = s.Suffix,
                        FilePath = s.FilePath
                    })
                    .ToList();
                //DPS信息
                var attachmentDPSIds = !string.IsNullOrEmpty(speaker.vendorQuery.DPSCheck) ? speaker.vendorQuery.DPSCheck?.Split(',').Select(x => Guid.Parse(x)).ToList() : null;
                speakerDetail.DPSCheck = attachmentQuery
                    .Where(x => attachmentDPSIds.Contains(x.Id))
                    .Select(s => new AttachmentInformation
                    {
                        AttachmentId = s.Id,
                        FileName = s.FileName,
                        FileSize = s.Size.ToFileSizeString(),
                        Suffix = s.Suffix,
                        FilePath = s.FilePath
                    })
                    .ToList();

                // 获取签署内容
                var consentSignedQuery = await LazyServiceProvider.LazyGetService<IConsentSignedRepository>().GetListAsync();
                //获取需要检查的最新版签署版本号
                string consentInfo = _configuration["Consent:ConsentCode"];
                var consentResponse = await _consentsetvice.GetConsentInfoAsync([.. consentInfo.Split(",")]);
                //获取讲者签署记录，判断是否已经完全签署
                var consentList = consentSignedQuery.Where(w => w.OneId == speaker.vendorQuery.OpenId || w.AppUserId == speaker.vendorQuery.UserId.ToString()).ToList();
                var isConsent = HasBeenFullySigned(consentList, consentResponse);
                speakerDetail.SignedStatus = isConsent;
                speakerDetail.SignedStatusString = isConsent ? "已签署" : "待签署";

                return MessageResult.SuccessResult(speakerDetail);
            }
            catch (Exception ex)
            {
                _logger.LogError($"SpeakerService's GetSpeakerDraftDetailAsync has an error : {ex.Message}");
                return MessageResult.FailureResult($"获取失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取[None]Hcp详情（小程序扫码专用）
        /// </summary>
        /// <returns></returns>
        public async Task<MessageResult> GetSpeakerDetailByScanQRAsync(GetSpeakerDetailByScanQRRequestDto request)
        {
            var vdApplyQuery = await LazyServiceProvider.LazyGetService<IVendorApplicationRepository>().GetQueryableAsync();
            var vdApplyPersonalQuery = await LazyServiceProvider.LazyGetService<IVendorApplicationPersonalRepository>().GetQueryableAsync();

            var vdQuery = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();
            var vdPersonalQuery = await LazyServiceProvider.LazyGetService<IVendorPersonalRepository>().GetQueryableAsync();

            //职称
            var jobTitles = await _dataverseService.GetAllJobTiles();
            //医院
            var hospitals = await _dataverseService.GetAllHospitals();
            //科室
            var standardHosDeps = await _dataverseService.GetAllDepartments();

            var response = new GetSpeakerDetailByScanQRResponseDto();

            var qrValid = await CheckQRCodeValidation(request.VendorCode);
            if (!qrValid)
                return MessageResult.FailureResult($"二维码已失效");

            //查申请表
            if (request.IsDraft)
            {
                var vendorApply = vdApplyPersonalQuery
                .Join(vdApplyQuery, a => a.ApplicationId, b => b.Id, (a, b) => new { personal = a, vendor = b })
                .Where(x => x.vendor.ApplicationCode == request.VendorCode)
                .FirstOrDefault();

                if (vendorApply == null)
                    return MessageResult.FailureResult($"未找到讲者数据, 请核实Id：{request.VendorCode}");

                response.SpId = vendorApply.vendor.Id;
                response.SpName = vendorApply.personal.SPName;
                response.BindPhone = string.IsNullOrEmpty(vendorApply.vendor.HandPhone) ? false : true;
                //职称
                if (request.IsSpeaker && vendorApply.vendor.PTId.HasValue)
                    response.PtiName = jobTitles.FirstOrDefault(f => f.Id == vendorApply.vendor.PTId)?.Name;

                //医院
                if (request.IsSpeaker && vendorApply.vendor.HospitalId.HasValue)
                    response.HospitalName = hospitals.FirstOrDefault(P => P.Id == vendorApply.vendor.HospitalId)?.Name;

                //科室
                if (request.IsSpeaker && vendorApply.vendor.StandardHosDepId.HasValue)
                    response.StandardHosDepName = standardHosDeps.FirstOrDefault(P => P.Id == vendorApply.vendor.StandardHosDepId)?.Name;

                //院内科室
                if (request.IsSpeaker && !string.IsNullOrEmpty(vendorApply.vendor.HosDepartment))
                    response.HosDepartment = vendorApply.vendor.HosDepartment;

                //NonHCP存在所属机构
                if (!request.IsSpeaker && !string.IsNullOrEmpty(vendorApply.personal.AffiliationOrgan))
                    response.AffiliationOrgan = vendorApply.personal.AffiliationOrgan;

                //预留手机号码
                if (request.IsSpeaker && !string.IsNullOrEmpty(vendorApply.vendor.MobileEncrypt))//MobileEncrypt
                {
                    string aesKey = _configuration["Integrations:EPD_HCP_Portal:AesKey256"];
                    response.ReservedMobilePhone = JsonConvert.DeserializeObject<string[]>(AesHelper.DecryptionIV(vendorApply.vendor.MobileEncrypt, aesKey));
                }
            }
            //查正式表
            else
            {
                var vendor = vdPersonalQuery
                .Join(vdQuery, a => a.VendorId, b => b.Id, (a, b) => new { personal = a, vendor = b })
                .FirstOrDefault(f => f.vendor.VendorCode == request.VendorCode);

                if (vendor == null)
                    return MessageResult.FailureResult($"未找到讲者数据, 请核实Id：{request.VendorCode}");

                response.SpId = vendor.vendor.Id;
                response.SpName = vendor.personal.SPName;
                response.BindPhone = string.IsNullOrEmpty(vendor.vendor.HandPhone) ? false : true;
                //职称
                if (request.IsSpeaker && vendor.vendor.PTId.HasValue)
                    response.PtiName = jobTitles.FirstOrDefault(f => f.Id == vendor.vendor.PTId)?.Name;

                //医院
                if (request.IsSpeaker && vendor.vendor.HospitalId.HasValue)
                    response.HospitalName = hospitals.FirstOrDefault(P => P.Id == vendor.vendor.HospitalId)?.Name;

                //科室
                if (request.IsSpeaker && vendor.vendor.StandardHosDepId.HasValue)
                    response.StandardHosDepName = standardHosDeps.FirstOrDefault(P => P.Id == vendor.vendor.StandardHosDepId)?.Name;

                //院内科室
                if (request.IsSpeaker && !string.IsNullOrEmpty(vendor.vendor.HosDepartment))
                    response.HosDepartment = vendor.vendor.HosDepartment;

                //NonHCP存在所属机构
                if (!request.IsSpeaker && !string.IsNullOrEmpty(vendor.personal.AffiliationOrgan))
                    response.AffiliationOrgan = vendor.personal.AffiliationOrgan;
            }
            return MessageResult.SuccessResult(response);
        }

        private async Task<bool> CheckQRCodeValidation(string vendorCode)
        {
            var recordsRepository = LazyServiceProvider.LazyGetService<IQRCodeRecordRepository>();
            var record = await recordsRepository.FirstOrDefaultAsync(a => a.ApplicationCode == vendorCode);
            if (record == null || !record.IsEffective || record.EffectiveEndTime < DateTime.Now)
                return false;
            return true;
        }

        /// <summary>
        /// 修改草稿
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> SaveSpeakerAsync(SaveSpeakerRequestDto request)
        {
            var vendor = _serviceProvider.GetService<IVendorRepository>();
            var vendorPersonal = _serviceProvider.GetService<IVendorPersonalRepository>();
            var vendorApplication = _serviceProvider.GetService<IVendorApplicationRepository>();
            var vendorApplicationPersonal = _serviceProvider.GetService<IVendorApplicationPersonalRepository>();
            var vendorApplicationFinancial = _serviceProvider.GetService<IVendorApplicationFinancialRepository>();
            var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");
            var getAssociationGrade = await _dataverseService.GetDictionariesAsync(DictionaryType.AssociationGrade);
            var getAssociationType = await _dataverseService.GetDictionariesAsync(DictionaryType.AssociationType);
            var getAssociationJob1 = await _dataverseService.GetDictionariesAsync(DictionaryType.AssociationJob1);
            var getAssociationJob2 = await _dataverseService.GetDictionariesAsync(DictionaryType.AssociationJob2);
            var getAssociationJobStatus = await _dataverseService.GetDictionariesAsync(DictionaryType.AssociationJobStatus);
            var commonService = LazyServiceProvider.LazyGetService<ICommonService>();

            //城市处理
            if (request.ProvinceCity.Length != 2) return MessageResult.FailureResult("请选择完整城市");
            request.Province = request.ProvinceCity[0];
            request.City = request.ProvinceCity[1];

            //修改供应商申请表
            var vendorDetail = await vendorApplication.FirstOrDefaultAsync(f => f.Id == request.ID);
            if (vendorDetail == null)
                return MessageResult.FailureResult("修改数据不存在");
            if (vendorDetail.Status != Enums.Statuses.Saved && vendorDetail.Status != Enums.Statuses.Withdraw && vendorDetail.Status != Enums.Statuses.Returned)
                return MessageResult.FailureResult("修改数据状态不是草稿，不能修改");

            if (request.IsMiniProgram)//小程序请求才验证
            {
                var ErrorMsg = await ValidationCardNo(request, vendorApplication, vendorApplicationPersonal, vendor, vendorPersonal);
                if (!string.IsNullOrWhiteSpace(ErrorMsg))
                    return MessageResult.FailureResult(ErrorMsg);
            }

            vendorDetail = ObjectMapper.Map(request, vendorDetail);

            //2745【供应商管理】【讲者管理】存在EPD医生主键的讲者，由非EPD部分发起变更/激活时，会导致EPDID丢失--BE
            if (!string.IsNullOrEmpty(request.EpdId))
                vendorDetail.EpdId = request.EpdId;

            //申请人提交时，才修改发起人和发起部门
            if (vendorDetail.ApplyUserId == CurrentUser.Id)
            {
                vendorDetail.ApplyUserBu = request.ApplyUserBu?.ToString();
                #region 根据单据上选择的申请组织ID获取对应的名称、BU和全链路组织名称
                var orgs = await _dataverseService.GetOrganizations();
                var orgDto = orgs.FirstOrDefault(x => x.Id == Guid.Parse(vendorDetail.ApplyUserBu));
                if (orgDto != null)
                {
                    var orgTree2BU = await commonService.GetParentOrgs(orgDto, orgs);
                    vendorDetail.ApplyDeptName = orgTree2BU.First().DepartmentName;
                    vendorDetail.ApplyBuId = orgTree2BU.Last().Id;
                    vendorDetail.ApplyBuName = orgTree2BU.Last().DepartmentName;
                    var departementNames = orgTree2BU.GroupBy(d => d.Id)  // 根据Id分组
                        .Select(g => g.First())  // 从每组中选择第一个元素
                        .Select(x => x.DepartmentName).ToList();
                    if (departementNames.Any())
                        vendorDetail.ApplyUserBuName = string.Join('-', departementNames.ToArray().Reverse());
                }
                #endregion
            }

            vendorDetail.VendorType = VendorTypes.HCPPerson;
            //2660 保存草稿不应修改状态
            //vendorDetail.Status = Enums.Statuses.Saved;
            vendorDetail.VerificationStatus = VerificationStatuses.ToBeConfirmed;
            //从小程序过来
            //if (request.IsMiniProgram)
            //{
            //    vendorDetail.BImproved = true;
            //    //根据用户ID绑定到vendor草稿上
            //    //vendorDetail.OpenId = request.OpenId;
            //}
            ////授权了的
            //if (request.BAuth)
            //    vendorDetail.BImproved = true;
            if (request.BAuth || request.IsMiniProgram)
                vendorDetail.BAuth = true;

            vendorDetail.BImproved = VendorCommon.CheckBImprovedHCP(request);
            vendorDetail.EpdHospitalId = request.EpdHospitalCode;
            await SetHospitalIdOrName(vendorDetail, (Guid)request.HospitalId, _dataverseService);
            await SetDempartmentName(vendorDetail, (Guid)request.StandardHosDepId, _dataverseService);
            await SetProfessionalTitleName(vendorDetail, (Guid)request.PTId, _dataverseService);

            if (request.AcademicPositionJson?.Count > 0)
            {
                //学会/期刊
                request.AcademicPositionJson.ForEach(f =>
                {
                    f.Id = Guid.NewGuid().ToString();
                    f.JournalTypeName = !string.IsNullOrEmpty(f.JournalTypeName) ? f.JournalTypeName : getAssociationType.FirstOrDefault(w => w.Code == f.JournalType)?.Name;
                    f.JournalCategoryName = !string.IsNullOrEmpty(f.JournalCategoryName) ? f.JournalCategoryName : getAssociationGrade.FirstOrDefault(w => w.Code == f.JournalCategory)?.Name;
                    f.AppointmentName = !string.IsNullOrEmpty(f.AppointmentName) ? f.AppointmentName : getAssociationJob1.FirstOrDefault(w => w.Code == f.Appointment)?.Name;
                    f.AppointmentName = !string.IsNullOrEmpty(f.AppointmentName) ? f.AppointmentName : getAssociationJob2.FirstOrDefault(w => w.Code == f.Appointment)?.Name;
                    f.AppointmentStatusName = !string.IsNullOrEmpty(f.AppointmentStatusName) ? f.AppointmentStatusName : getAssociationJobStatus.FirstOrDefault(w => w.Code == f.AppointmentStatus)?.Name;
                });

                #region 将变更的学协会信息设置flag，在DCR信息验证回写时，替换设置flag=true的数据

                //学会任职信息
                var oldVendorData = string.IsNullOrEmpty(vendorDetail.UpdatePreJson) ? null : JsonConvert.DeserializeObject<SpeakerDetailResponseDto>(vendorDetail.UpdatePreJson);
                //2780【供应商管理】【讲者】学协会信息：若原来数据里面如果学协会为空，小程序提交保存的时候会报错（"Value cannot be null. (Parameter 'inner')"）
                var existAcademics = oldVendorData == null ? [] : (oldVendorData.AcademicPositionJson ?? []);
                //通过几个字段关联后，数量对不上，也意味着有变更
                var matchDatas = request.AcademicPositionJson
                .GroupJoin
                (
                    existAcademics,
                    a => new { a.JournalName, a.JournalCategory, a.JournalCategoryName, a.Appointment, a.AppointmentName, a.AppointmentStatus, a.AppointmentStatusName },
                    a => new { a.JournalName, a.JournalCategory, a.JournalCategoryName, a.Appointment, a.AppointmentName, a.AppointmentStatus, a.AppointmentStatusName },
                    (a, b) => new { NewAcademic = a, OldAcademics = b }
                )
                .SelectMany(a => a.OldAcademics.DefaultIfEmpty(), (a, b) => new { a.NewAcademic, OldAcademic = b })
                .ToArray();

                //将未匹配上的数据设置为已变更
                foreach (var item in matchDatas)
                {
                    if (item.OldAcademic == null)
                        item.NewAcademic.IsChanged = true;
                }

                #endregion

                vendorDetail.AcademicPosition = JsonConvert.SerializeObject(matchDatas.Select(a => a.NewAcademic));
            }
            else
                vendorDetail.AcademicPosition = null;

            vendorDetail.BankCity = request.BankCity?.JoinAsString(",");
            vendorDetail.BankCardImg = request.BankCardImg?.AttachmentId.ToString();
            if (!string.IsNullOrEmpty(vendorDetail.BankCardNo))
                vendorDetail.BankCardNo = AesHelper.Encryption(vendorDetail.BankCardNo, insightKey);

            vendorDetail.AttachmentInformation = request.AttachmentInformation.Count > 0 ? string.Join(",", request.AttachmentInformation.Select(s => s.AttachmentId).ToList()) : "";
            if (!string.IsNullOrWhiteSpace(request.Mobile))
            {
                vendorDetail.MobileEncrypt = request.Mobile;
            }
            await vendorApplication.UpdateAsync(vendorDetail, true);
            //修改供应商个人申请表
            var vendorPersonalDetail = await vendorApplicationPersonal.FirstOrDefaultAsync(f => f.ApplicationId == vendorDetail.Id);
            vendorPersonalDetail = ObjectMapper.Map(request, vendorPersonalDetail);
            if (!string.IsNullOrEmpty(vendorPersonalDetail.CardNo))
                vendorPersonalDetail.CardNo = AesHelper.Encryption(vendorPersonalDetail.CardNo, insightKey);
            vendorPersonalDetail.CardPic = request.CardPic?.AttachmentId.ToString();
            await vendorApplicationPersonal.UpdateAsync(vendorPersonalDetail, true);
            //修改供应商财务信息表,删除原有内容
            var financeDetailList = await vendorApplicationFinancial.GetListAsync(f => f.ApplicationId == vendorDetail.Id);
            await vendorApplicationFinancial.DeleteManyAsync(financeDetailList, true);
            //新增供应商财务信息表
            var financeNewDetailList = ObjectMapper.Map<List<FinancialInformation>, List<VendorApplicationFinancial>>(request.FinancialInformation);
            var vendorTypeCfgs = await _dataverseService.GetVendorTypeCfgAsync();
            foreach (var item in financeNewDetailList)
            {
                item.ApplicationId = (Guid)request.ID;
                if (item.FinancialVendorStatus == 0)
                    item.FinancialVendorStatus = FinancialVendorStatus.ToBeEffective;
                if (string.IsNullOrEmpty(item.PaymentTerm))
                    item.PaymentTerm = vendorTypeCfgs.FirstOrDefault(f => f.VendorType == ((int)VendorTypes.HCPPerson).ToString())?.PaymentTerms;
            }
            await vendorApplicationFinancial.InsertManyAsync(financeNewDetailList, true);

            //发送信息已完善的邮件
            if (request.IsMiniProgram)
                await SendCompleteInfoEmail((vendorDetail.Id, vendorDetail.ApplyUserId, vendorDetail.VendorType, vendorDetail.ApplicationType, vendorDetail.Status, vendorDetail.ApplyUserName, vendorPersonalDetail.SPName));

            return MessageResult.SuccessResult(request.ID);
            #region 匹配方法，待定暂时不用
            //var financeDetailUpdateList = new List<VendorApplicationFinancial>();
            //foreach (var item in financeDetailList)
            //{
            //    //如果查不到，则已被删除，执行删除操作
            //    if (request.FinancialInformation.FirstOrDefault(f=>f.ID==item.Id) == null)
            //    {
            //        await vendorApplicationFinancial.DeleteAsync(item);
            //    }
            //    else
            //    {
            //        financeDetailUpdateList.Add(item);
            //    }
            //}
            //var financeDetail = ObjectMapper.Map(request.FinancialInformation, financeDetailUpdateList);
            //var financeNewDetail = ObjectMapper.Map<List<FinancialInformation>, List<VendorApplicationFinancial>>(request.FinancialInformation.Where(w => w.ID == null).ToList());
            //var financeUpdateDetail = financeDetail.Where(w => w.Id != Guid.Empty).ToList();
            //await vendorApplicationFinancial.UpdateManyAsync(financeUpdateDetail);
            //await vendorApplicationFinancial.InsertManyAsync(financeNewDetail);
            #endregion
        }

        /// <summary>
        /// DPS Check审批通过时，更新供应商申请的哦身份证信息，且审批通过Workflow Task
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> DPSCheckApproveAsync(DPSCheckApproveRequestDto request)
        {
            var vendorRepo = _serviceProvider.GetService<IVendorRepository>();
            var vendorPersRepo = _serviceProvider.GetService<IVendorPersonalRepository>();

            var vendorApplyRepo = _serviceProvider.GetService<IVendorApplicationRepository>();
            var vendorApplyPersRepo = _serviceProvider.GetService<IVendorApplicationPersonalRepository>();

            //var approveService = LazyServiceProvider.LazyGetService<IApproveService>();

            //更新HCP/NonHCP申请的身份证信息
            var vendorApplication = await vendorApplyRepo.FirstOrDefaultAsync(f => f.Id == request.Id);
            if (vendorApplication == null)
                return MessageResult.FailureResult("修改数据不存在");
            Statuses[] editableStatus = [Statuses.Saved, Statuses.Withdraw, Statuses.Returned, Statuses.Approving];
            if (!editableStatus.Contains(vendorApplication.Status))
                return MessageResult.FailureResult("修改数据状态不是草稿，不能修改");

            var ErrorMsg = await VerifyUniqueForCardNo(vendorRepo, vendorPersRepo, vendorApplyRepo, vendorApplyPersRepo, request.Id, request.CardNo, request.CardType, vendorApplication.VendorCode);
            if (!string.IsNullOrWhiteSpace(ErrorMsg))
                return MessageResult.FailureResult(ErrorMsg);

            var vendorApplyPersonal = await vendorApplyPersRepo.FirstOrDefaultAsync(f => f.ApplicationId == request.Id);
            if (vendorApplyPersonal == null)
                return MessageResult.FailureResult("修改数据不存在");

            //更新供应商个人表的证件信息
            if (!string.IsNullOrEmpty(request.CardType))
                vendorApplyPersonal.CardType = request.CardType;

            if (!string.IsNullOrEmpty(request.CardNo))
            {
                var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");
                vendorApplyPersonal.CardNo = AesHelper.Encryption(request.CardNo, insightKey);
            }

            if (request.Sex.HasValue)
                vendorApplyPersonal.Sex = request.Sex.Value;

            if (!string.IsNullOrEmpty(request.Address.Trim()))
                vendorApplyPersonal.Address = request.Address.Trim();

            if (!string.IsNullOrEmpty(request.ProvinceCity[0]))
                vendorApplyPersonal.Province = request.ProvinceCity[0];

            if (!string.IsNullOrEmpty(request.ProvinceCity[1]))
                vendorApplyPersonal.City = request.ProvinceCity[1];

            await vendorApplyPersRepo.UpdateAsync(vendorApplyPersonal, true);

            //250318页面交互逻辑修改了，这里不做审批只做申请单的身份证信息修改
            /*
            //审批操作
            var updateApproval = new UpdateApprovalDto
            {
                BusinessFormId = request.Id.ToString(),
                Submitter = request.Submitter,
                Remark = request.Remark,
                OperationStatus = request.OperationStatus
            };
            var approvalOperation = await approveService.ApprovalOperationAsync(updateApproval);

            if (!approvalOperation.Success)
            {
                await CurrentUnitOfWork.RollbackAsync();
                return MessageResult.FailureResult("审批失败");
            }
            */
            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 授权填写
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns></returns>
        public async Task<MessageResult> AuthorizedSellersAsync(AuthorizedSellerRequestDto request)
        {
            var vendor = _serviceProvider.GetService<IVendorRepository>();
            var vendorPersonal = _serviceProvider.GetService<IVendorPersonalRepository>();
            var vendorApplication = _serviceProvider.GetService<IVendorApplicationRepository>();
            var vendorApplicationPersonal = _serviceProvider.GetService<IVendorApplicationPersonalRepository>();
            var vendorApplicationFinancial = _serviceProvider.GetService<IVendorApplicationFinancialRepository>();
            //修改供应商申请表
            var vendorDetail = await vendorApplication.FirstOrDefaultAsync(f => f.Id == request.ID);
            if (vendorDetail == null)
                return MessageResult.FailureResult("修改数据不存在");
            if (vendorDetail.Status != Enums.Statuses.Saved && vendorDetail.Status != Enums.Statuses.Withdraw && vendorDetail.Status != Enums.Statuses.Returned)
                return MessageResult.FailureResult("修改数据状态不是草稿，不能修改");

            vendorDetail.BAuth = true;
            //vendorDetail.OpenId = request.OpenId;
            await vendorApplication.UpdateAsync(vendorDetail, true);
            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 新增草稿
        /// </summary>
        /// <param name="request">请求参数</param>
        /// <returns></returns>
        public async Task<MessageResult> CreateSpeakerAsync(CreateSpeakerRequestDto request)
        {
            var vendor = _serviceProvider.GetService<IVendorRepository>();
            var vendorPersonal = _serviceProvider.GetService<IVendorPersonalRepository>();
            var vendorApplication = _serviceProvider.GetService<IVendorApplicationRepository>();
            var vendorApplicationPersonal = _serviceProvider.GetService<IVendorApplicationPersonalRepository>();
            var vendorApplicationFinancial = _serviceProvider.GetService<IVendorApplicationFinancialRepository>();
            var vendorDetail = ObjectMapper.Map<CreateSpeakerRequestDto, Entities.VendorApplications.VendorApplication>(request);
            var getAssociationGrade = await _dataverseService.GetDictionariesAsync(DictionaryType.AssociationGrade);
            var getAssociationType = await _dataverseService.GetDictionariesAsync(DictionaryType.AssociationType);
            var getAssociationJob1 = await _dataverseService.GetDictionariesAsync(DictionaryType.AssociationJob1);
            var getAssociationJob2 = await _dataverseService.GetDictionariesAsync(DictionaryType.AssociationJob2);
            var getAssociationJobStatus = await _dataverseService.GetDictionariesAsync(DictionaryType.AssociationJobStatus);
            var commonService = LazyServiceProvider.LazyGetService<ICommonService>();

            vendorDetail.VendorType = VendorTypes.HCPPerson;
            vendorDetail.Status = Enums.Statuses.Saved;
            vendorDetail.VerificationStatus = VerificationStatuses.ToBeConfirmed;
            vendorDetail.DraftVersion = 1;
            vendorDetail.AttachmentInformation = request.AttachmentInformation.Count > 0 ? string.Join(",", request.AttachmentInformation.Select(s => s.AttachmentId).ToList()) : "";

            #region 根据单据上选择的申请组织ID获取对应的名称、BU和全链路组织名称
            var orgs = await _dataverseService.GetOrganizations();
            var orgDto = orgs.FirstOrDefault(x => x.Id == Guid.Parse(vendorDetail.ApplyUserBu));
            if (orgDto != null)
            {
                var orgTree2BU = await commonService.GetParentOrgs(orgDto, orgs);
                vendorDetail.ApplyDeptName = orgTree2BU.First().DepartmentName;
                vendorDetail.ApplyBuId = orgTree2BU.Last().Id;
                vendorDetail.ApplyBuName = orgTree2BU.Last().DepartmentName;
                var departementNames = orgTree2BU.GroupBy(d => d.Id)  // 根据Id分组
                .Select(g => g.First())  // 从每组中选择第一个元素
                .Select(x => x.DepartmentName).ToList();
                if (departementNames.Any())
                    vendorDetail.ApplyUserBuName = string.Join('-', departementNames.ToArray().Reverse());
            }
            #endregion

            vendorDetail.EpdHospitalId = request.EpdHospitalCode;
            await SetHospitalIdOrName(vendorDetail, (Guid)request.HospitalId, _dataverseService);
            await SetDempartmentName(vendorDetail, (Guid)request.StandardHosDepId, _dataverseService);
            await SetProfessionalTitleName(vendorDetail, (Guid)request.PTId, _dataverseService);

            if (request.AcademicPositionJson?.Count > 0)
            {
                //学会/期刊
                request.AcademicPositionJson.ForEach(f =>
                {
                    f.Id = Guid.NewGuid().ToString();
                    f.JournalTypeName = !string.IsNullOrEmpty(f.JournalTypeName) ? f.JournalTypeName : getAssociationType.FirstOrDefault(w => w.Code == f.JournalType)?.Name;
                    f.JournalCategoryName = !string.IsNullOrEmpty(f.JournalCategoryName) ? f.JournalCategoryName : getAssociationGrade.FirstOrDefault(w => w.Code == f.JournalCategory)?.Name;
                    f.AppointmentName = !string.IsNullOrEmpty(f.AppointmentName) ? f.AppointmentName : getAssociationJob1.FirstOrDefault(w => w.Code == f.Appointment)?.Name;
                    f.AppointmentName = !string.IsNullOrEmpty(f.AppointmentName) ? f.AppointmentName : getAssociationJob2.FirstOrDefault(w => w.Code == f.Appointment)?.Name;
                    f.AppointmentStatusName = !string.IsNullOrEmpty(f.AppointmentStatusName) ? f.AppointmentStatusName : getAssociationJobStatus.FirstOrDefault(w => w.Code == f.AppointmentStatus)?.Name;
                });
                vendorDetail.AcademicPosition = JsonConvert.SerializeObject(request.AcademicPositionJson);
            }
            else
                vendorDetail.AcademicPosition = null;

            if (!string.IsNullOrWhiteSpace(request.Mobile))
            {
                vendorDetail.MobileEncrypt = request.Mobile;
            }

            //vendorDetail.ApplicationCode = await GetSerialNoAsync(vendorApplication, "V");
            //vendorDetail.VendorCode = await GetSerialNoAsync(vendorApplication, "SP");
            //var createEntity = await vendorApplication.InsertAsync(vendorDetail, true);

            await InsertAndGenerateSerialNoAsync(vendorApplication, vendorDetail);
            //新增供应商个人申请表
            var vendorPersonalDetail = ObjectMapper.Map<CreateSpeakerRequestDto, VendorApplicationPersonal>(request);
            vendorPersonalDetail.ApplicationId = vendorDetail.Id;
            var createPersonalEntity = await vendorApplicationPersonal.InsertAsync(vendorPersonalDetail, true);
            //新增供应商财务信息表
            var financeDetailList = ObjectMapper.Map<List<FinancialInformation>, List<VendorApplicationFinancial>>(request.FinancialInformation);
            var vendorTypeCfgs = await _dataverseService.GetVendorTypeCfgAsync();
            foreach (var item in financeDetailList)
            {
                item.ApplicationId = vendorDetail.Id;
                if (item.FinancialVendorStatus == 0)
                    item.FinancialVendorStatus = FinancialVendorStatus.ToBeEffective;
                item.PaymentTerm = vendorTypeCfgs.FirstOrDefault(f => f.VendorType == ((int)VendorTypes.HCPPerson).ToString())?.PaymentTerms;
            }
            await vendorApplicationFinancial.InsertManyAsync(financeDetailList, true);

            return MessageResult.SuccessResult(vendorDetail.Id);
        }

        /// <summary>
        /// 验证证件是否唯一 （个人）
        /// </summary>
        /// <param name="request"></param>
        /// <param name="vendorPersonal"></param>
        /// <param name="vendorApplicationPersonal"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        private async Task<string> ValidationData(SaveSpeakerRequestDto request, IVendorApplicationRepository vendorApplication, IVendorApplicationPersonalRepository vendorApplicationPersonal, IVendorRepository vendor, IVendorPersonalRepository vendorPersonal)
        {
            var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");
            var ErrorMsg = string.Empty;
            var EncryptionCardNo = string.Empty;
            if (!string.IsNullOrEmpty(request.CardNo))
                EncryptionCardNo = AesHelper.Encryption(request.CardNo, insightKey);
            //验证证件号
            try
            {
                //证件号码验证
                var queryVendor = await vendor.GetQueryableAsync();
                var queryVendorPers = await vendorPersonal.GetQueryableAsync();
                var duplicateCardNoVendors = queryVendorPers
                    .Where(x => x.CardType == request.CardType && x.CardNo == EncryptionCardNo)
                    .Join(queryVendor, a => a.VendorId, b => b.Id, (a, b) => new { a.SPName, a.CardNo, b.VendorCode })
                    .Where(a => a.VendorCode != request.VendorCode)//排除当前行
                    .ToArray();
                if (duplicateCardNoVendors.Length > 0)
                    duplicateCardNoVendors.ToList().ForEach(x => { ErrorMsg += $"该证件号码在{x.VendorCode}号中与{x.SPName}讲者重复;"; });

                //审批流程验证
                var queryVendorApply = await vendorApplication.GetQueryableAsync();
                var duplicateApplies = queryVendorApply
                    .Where(x => x.VendorCode == request.VendorCode && x.Status == Statuses.Approving && x.Id != request.ID)
                    .Select(x => x.VendorCode).ToArray();
                if (duplicateApplies.Length > 0)
                    duplicateApplies.ToList().ForEach(x => ErrorMsg += $"{x}申请单号讲者正在执行审批流程，请勿重复提交;");

                //证件号码验证
                var queryVendorApplyPers = await vendorApplicationPersonal.GetQueryableAsync();
                var duplicateCardNoVendorApplies = queryVendorApplyPers
                    .Where(x => x.CardType == request.CardType && x.CardNo == EncryptionCardNo)
                    .Join(queryVendorApply, a => a.ApplicationId, b => b.Id, (a, b) => new { a.SPName, b.Id, b.VendorCode, b.Status })
                    .Where(x => x.Status == Statuses.Approving && x.Id != request.ID).ToArray();
                if (duplicateCardNoVendorApplies.Length > 0)
                    duplicateCardNoVendorApplies.ToList().ForEach(x => ErrorMsg += $"该证件号码在{x.VendorCode}号中与{x.SPName}讲者重复;");

                //电话号码验证
                VendorTypes[] vendorTypes = [VendorTypes.HCPPerson, VendorTypes.NonHCPPerson];//只验证个人、排除机构
                var duplicatePhoneVendors = queryVendor.Where(x => x.VendorCode != request.VendorCode && x.HandPhone == request.HandPhone && vendorTypes.Contains(x.VendorType)).Select(x => x.VendorCode).ToArray();
                if (duplicatePhoneVendors.Length > 0)
                    duplicatePhoneVendors.ToList().ForEach(x => ErrorMsg += $"该电话号码已被{x}讲者中注册，请查证;");

                //电话号码验证
                var duplicatePhoneApplies = queryVendorApply.Where(x => x.Id != request.ID && x.HandPhone == request.HandPhone && x.Status == Statuses.Approving && vendorTypes.Contains(x.VendorType)).ToArray();
                if (duplicatePhoneApplies.Length > 0)
                    duplicatePhoneApplies.ToList().ForEach(x => ErrorMsg += $"该电话号码已被{x.VendorCode}讲者中注册，请查证;");

                //草稿版本号验证
                //验证版本号 若是更新操作或者激活操作直接提交时则不验证
                if (request.ID.HasValue)
                {
                    var vendorVersion = queryVendor.FirstOrDefault(x => x.VendorCode == request.VendorCode && x.DraftVersion + 1 != request.DraftVersion);
                    if (vendorVersion != null)
                        ErrorMsg += $"{vendorVersion.VendorCode}中版本号为V{vendorVersion.DraftVersion}，当前草稿版本号为V{request.DraftVersion}，请重新生成草稿;";
                }

                //验证黑名单
                var vendorBlackQuery = await LazyServiceProvider.LazyGetService<IVendorBlackListRepository>().GetNoFilterQueryable();
                var blackQuery = vendorBlackQuery.Join(queryVendor.Where(a => a.VendorCode == request.VendorCode), a => a.VendorId, a => a.Id, (a, b) => new { vendorBlack = a, vednor = b })
                    .Where(a => a.vendorBlack.Status == BlackStatus.Effective);
                if (blackQuery.Any())
                {
                    ErrorMsg += $"{request.VendorCode}在黑名单中，不允许提交.";
                }
            }
            catch (Exception ex)
            {
                return ErrorMsg = ex.Message;
            }

            return ErrorMsg;
        }

        /// <summary>
        /// 验证基本信息是否重复
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<List<ValidationSpeakerResponseDto>> ValidationSpeakerDataAsync(ValidationSpeakerRequestDto request)
        {
            var vendor = _serviceProvider.GetService<IVendorRepository>();
            var vendorPersonal = _serviceProvider.GetService<IVendorPersonalRepository>();
            var vendorApplication = _serviceProvider.GetService<IVendorApplicationRepository>();
            var vendorPersonalApplication = _serviceProvider.GetService<IVendorApplicationPersonalRepository>();
            var jobTitles = await _dataverseService.GetAllJobTiles();
            var hospitals = await _dataverseService.GetAllHospitals();
            var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");

            var vendorQuery = (await vendor.GetQueryableAsync()).AsNoTracking();
            var vendorPersonalQuery = (await vendorPersonal.GetQueryableAsync()).AsNoTracking();

            var vendorApplicationQuery = (await vendorApplication.GetQueryableAsync()).AsNoTracking();
            var vendorPersonalApplicationQuery = (await vendorPersonalApplication.GetQueryableAsync()).AsNoTracking();

            //正式表中已有相应数据
            var vendorByNameData = vendorQuery.Join(vendorPersonalQuery, a => a.Id, b => b.VendorId, (a, b) => new { v = a, vp = b })
                .Where(a => (!string.IsNullOrWhiteSpace(request.SPName) && a.vp.SPName == request.SPName) || (!string.IsNullOrWhiteSpace(request.CertificateCode) && a.v.CertificateCode == request.CertificateCode))
                .ToList();
            var vendors = vendorByNameData.Select(a => new ValidationSpeakerResponseDto
            {
                VendorId = a.v.Id,
                PTId = a.v.PTId,
                PTIName = a.v.PTId.HasValue ? jobTitles.FirstOrDefault(f => f.Id == a.v.PTId)?.Name : null,
                HospitalId = a.v.HospitalId,
                HospitalName = a.v.HospitalId.HasValue ? hospitals.FirstOrDefault(f => f.Id == a.v.HospitalId)?.Name : null,
                HandPhone = a.v.HandPhone,
                CertificateCode = a.v.CertificateCode,
                SPName = a.vp.SPName,
                CardType = a.vp.CardType,
                CardNo = string.IsNullOrWhiteSpace(a.vp.CardNo) ? "" : AesHelper.Decryption(a.vp.CardNo, insightKey),
                VendorCode = a.v.VendorCode,
                EpdId = a.v.EpdId,
                IsFullMatch = a.v.HospitalId == request.HospitalId
                           && a.v.StandardHosDepId == request.StandardHosDepId
                           && a.v.PTId == request.PTId
                           && a.vp.SPName == request.SPName
            })
            .ToList();

            //申请表中已有相应数据
            var vendorApplicationByNameData = vendorApplicationQuery.Where(a => a.ApplicationType == Enums.ApplicationTypes.Create && (a.Status == Enums.Statuses.Saved || a.Status == Enums.Statuses.Approving))
                .Join(vendorPersonalApplicationQuery, a => a.Id, b => b.ApplicationId, (a, b) => new { va = a, vpa = b })
                .Where(a => (!string.IsNullOrWhiteSpace(request.SPName) && a.vpa.SPName == request.SPName) || (!string.IsNullOrWhiteSpace(request.CertificateCode) && a.va.CertificateCode == request.CertificateCode))
                .ToList();
            var vendorApplications = vendorApplicationByNameData.Select(a => new ValidationSpeakerResponseDto
            {
                VendorApplicationId = a.va.Id,
                PTId = a.va.PTId,
                PTIName = a.va.PTId.HasValue ? jobTitles.FirstOrDefault(f => f.Id == a.va.PTId)?.Name : null,
                HospitalId = a.va.HospitalId,
                HospitalName = a.va.HospitalId.HasValue ? hospitals.FirstOrDefault(f => f.Id == a.va.HospitalId)?.Name : null,
                HandPhone = a.va.HandPhone,
                CertificateCode = a.va.CertificateCode,
                SPName = a.vpa.SPName,
                CardType = a.vpa.CardType,
                CardNo = string.IsNullOrWhiteSpace(a.vpa.CardNo) ? "" : AesHelper.Decryption(a.vpa.CardNo, insightKey),
                VendorCode = a.va.VendorCode,
                EpdId = a.va.EpdId,
                IsFullMatch = a.va.HospitalId == request.HospitalId
                           && a.va.StandardHosDepId == request.StandardHosDepId
                           && a.va.PTId == request.PTId
                           && a.vpa.SPName == request.SPName
            })
            .ToList();
            vendors.AddRange(vendorApplications);

            return vendors = vendors
                .WhereIf(!string.IsNullOrEmpty(request.VendorCode), w => w.VendorCode != request.VendorCode).Distinct().ToList();

            ////正式表中已有相应数据
            //var vendorData = await vendor.GetListAsync(g => g.HospitalId == request.HospitalId && g.StandardHosDepId == request.StandardHosDepId
            //&& g.PTId == request.PTId);

            ////正式表关联人员信息
            //var vendorPersonalData = await vendorPersonal.GetListAsync(g => g.SPName == request.SPName);
            //var vendorDataJoin = vendorData.Join(vendorPersonalData, a => a.Id, b => b.VendorId,
            //    (a, b) => new ValidationSpeakerResponseDto
            //    {
            //        VendorId = a.Id,
            //        PTId = a.PTId,
            //        PTIName = a.PTId.HasValue ? jobTitles.FirstOrDefault(f => f.Id == a.PTId)?.Name : null,
            //        HospitalId = a.HospitalId,
            //        HospitalName = a.HospitalId.HasValue ? hospitals.FirstOrDefault(f => f.Id == a.HospitalId)?.Name : null,
            //        HandPhone = a.HandPhone,
            //        CertificateCode = a.CertificateCode,
            //        SPName = b.SPName,
            //        CardType = b.CardType,
            //        CardNo = b.CardNo,
            //        VendorCode = a.VendorCode,
            //        EpdId = a.EpdId,
            //    }).ToList();

            ////申请表中已有相应数据
            //var vendorAppData = await vendorApplication.GetListAsync(g => g.HospitalId == request.HospitalId && g.StandardHosDepId == request.StandardHosDepId
            //&& g.PTId == request.PTId && g.ApplicationType == Enums.ApplicationTypes.Create && (g.Status == Enums.Statuses.Saved || g.Status == Enums.Statuses.Approving));

            ////申请表关联人员信息
            //var vendorAppPersonalData = await vendorPersonalApplication.GetListAsync(g => g.SPName == request.SPName);
            //var vendorAppDataJoin = vendorAppData.Join(vendorAppPersonalData, a => a.Id, b => b.ApplicationId,
            //    (a, b) => new ValidationSpeakerResponseDto
            //    {
            //        VendorApplicationId = a.Id,
            //        PTId = a.PTId,
            //        PTIName = a.PTId.HasValue ? jobTitles.FirstOrDefault(f => f.Id == a.PTId)?.Name : null,
            //        HospitalId = a.HospitalId,
            //        HospitalName = a.HospitalId.HasValue ? hospitals.FirstOrDefault(f => f.Id == a.HospitalId)?.Name : null,
            //        HandPhone = a.HandPhone,
            //        CertificateCode = a.CertificateCode,
            //        SPName = b.SPName,
            //        CardType = b.CardType,
            //        CardNo = b.CardNo,
            //        VendorCode = a.VendorCode,
            //        EpdId = a.EpdId,
            //    }).ToList();
            //vendorDataJoin.AddRange(vendorAppDataJoin);

            //foreach (var item in vendorDataJoin)
            //{
            //    if (!string.IsNullOrEmpty(item.CardNo))
            //        item.CardNo = AesHelper.Decryption(item.CardNo, insightKey);
            //}

            //return vendorDataJoin = vendorDataJoin
            //    .WhereIf(!string.IsNullOrEmpty(request.VendorCode), w => w.VendorCode != request.VendorCode).ToList();
        }

        /// <summary>
        /// 讲者提交草稿
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> SubmitSpeakerAsync(SaveSpeakerRequestDto request)
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(request.BankCardNo))
                    request.BankCardNo = request.BankCardNo.Trim().Replace("\n", "").Replace("\r", "").Replace("\t", "");
                if (!string.IsNullOrWhiteSpace(request.CardNo))
                    request.CardNo = request.CardNo.Trim().Replace("\n", "").Replace("\r", "").Replace("\t", "");

                var vendor = _serviceProvider.GetService<IVendorRepository>();
                var vendorPersonal = _serviceProvider.GetService<IVendorPersonalRepository>();
                var vendorFinancial = _serviceProvider.GetService<IVendorFinancialRepository>();
                var vendorApplication = _serviceProvider.GetService<IVendorApplicationRepository>();
                var vendorApplicationPersonal = _serviceProvider.GetService<IVendorApplicationPersonalRepository>();
                var vendorApplicationFinancial = _serviceProvider.GetService<IVendorApplicationFinancialRepository>();
                var dspot = LazyServiceProvider.GetService<IIntegrationDspotAppService>();
                var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");
                var consentSignedQuery = await LazyServiceProvider.LazyGetService<IConsentSignedRepository>().GetListAsync();
                var _consentsetvice = LazyServiceProvider.GetService<IConsentService>();
                if (request.ApplicationType != ApplicationTypes.Create)
                {
                    var applyUserId = await ValidationVendorApplicationDuplication(vendorApplication, request.VendorCode, (Guid)request.ID);
                    if (applyUserId != null)
                    {
                        var _identityUserRepository = _serviceProvider.GetService<IIdentityUserRepository>();
                        var user = await _identityUserRepository.FindAsync((Guid)applyUserId);
                        if (user != null)
                        {
                            return MessageResult.SuccessResult(messageModel: new MessageModelBase(603, $"供应商正在由{user.Name}({user.Email})申请中，无法重复申请"));
                        }
                        else
                        {
                            return MessageResult.FailureResult("供应商正在被申请，但没有找到申请人信息");
                        }
                    }
                }
                if (request.ApplicationType == Enums.ApplicationTypes.Create && !request.ID.HasValue)
                    return MessageResult.FailureResult("创建草稿请先保存再提交！");

                if (request.ApplicationType == ApplicationTypes.Active && !VendorCommon.ValidateBeforeActivation(request.FinancialInformation))
                    return MessageResult.FailureResult("财务信息待生效或待激活才能提交");

                //二要素验证
                await dspot.TwoElementsValidate(new TwoElementsRequestDataDto
                {
                    Name = request.SPName,
                    Phone = request.HandPhone
                });

                //检查变更内容，是否变更了医生信息，是否变更了公司信息 默认都要触发
                bool isChangedDoctorInfo = true, isDPSCheckInfo = true, isFinanceInfo = true;
                if (request.ApplicationType == Enums.ApplicationTypes.Update)
                {
                    var entity = await GetChangeContent(request, vendor, vendorPersonal, vendorFinancial);
                    isChangedDoctorInfo = entity.Item1;
                    isDPSCheckInfo = entity.Item2;
                    isFinanceInfo = entity.Item3;
                }

                //草稿ID
                var draftId = Guid.Empty;
                //若是更新操作或者激活操作直接提交时，草稿ID应该为空，需要先保存草稿
                var vendorApplicationData = request.ID.HasValue ? await vendorApplication.FirstOrDefaultAsync(g => g.Id == request.ID) : null;
                if (vendorApplicationData == null)
                {
                    //若没查到,则ID设为空重新生成草稿
                    request.ID = null;
                    //检查是否有相同草稿提交
                    var message = ValidationData(request, vendorApplication, vendorApplicationPersonal, vendor, vendorPersonal);
                    if (!string.IsNullOrEmpty(message.Result))
                        return MessageResult.FailureResult(message.Result);
                    //克隆草稿
                    var createVendor = await CloneSpeakerAsync(request);
                    if (!createVendor.Success) return MessageResult.FailureResult(createVendor.Message);
                    draftId = Guid.Parse(createVendor.Data.ToString());
                }
                else
                {
                    //检查是否有相同草稿提交
                    var message = ValidationData(request, vendorApplication, vendorApplicationPersonal, vendor, vendorPersonal);
                    if (!string.IsNullOrEmpty(message.Result))
                        return MessageResult.FailureResult(message.Result);
                    //修改草稿
                    var updateVendor = await SaveSpeakerAsync(request);
                    if (!updateVendor.Success) return MessageResult.FailureResult(updateVendor.Message);
                    draftId = (Guid)request.ID;
                }
                //isVeeva 是否有审批通过的Veava,isChanged 有Veava后是否有变更
                var (isVeeva, isChanged) = HasNoVeevaOrChanged(draftId, request.VendorCode, request.SPName);
                if (isVeeva)//是否有审批通过的Veava
                    isChangedDoctorInfo = isChanged;

                //获取编号
                var newEntity = await vendorApplication.FirstOrDefaultAsync(g => g.Id == draftId);
                var draftCode = newEntity.ApplicationCode;

                //YTW *************【讲者&非HCP个人】（新建&变更&激活）待签署状态不允许提交申请
                //获取需要检查的最新版签署版本号
                string consentInfo = _configuration["Consent:ConsentCode"];
                var consentResponse = await _consentsetvice.GetConsentInfoAsync([.. consentInfo.Split(",")]);
                //获取讲者签署记录，判断是否已经完全签署
                var consentList = consentSignedQuery.Where(w => w.OneId == newEntity.OpenId || w.AppUserId == newEntity.UserId.ToString()).ToList();
                var isConsent = HasBeenFullySigned(consentList, consentResponse);
                if (!isConsent && request.ApplicationType == Enums.ApplicationTypes.Create)
                    return MessageResult.FailureResult("待签署状态不能提交，请签署后重新提交");
                //创建审批任务
                var createOK = await CreateWorkflowAsync(request, draftId, draftCode, isChangedDoctorInfo, isDPSCheckInfo, isFinanceInfo, newEntity);
                if (!createOK)
                    return MessageResult.FailureResult("审批任务创建失败");

                //修改业务状态
                var vendorDetail = await vendorApplication.FirstOrDefaultAsync(f => f.Id == draftId);
                var applyUser = await LazyServiceProvider.GetService<IIdentityUserRepository>().FindAsync(vendorDetail.ApplyUserId);
                vendorDetail.ApplyUserName = applyUser?.Name;
                vendorDetail.Status = Enums.Statuses.Approving;
                vendorDetail.ApplyTime = DateTime.UtcNow.AddHours(8);
                if (!string.IsNullOrWhiteSpace(request.Mobile))
                {
                    vendorDetail.MobileEncrypt = request.Mobile;
                }
                await vendorApplication.UpdateAsync(vendorDetail, true);
                return MessageResult.SuccessResult(draftId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"SpeakerService's SubmitSpeakerAsync has an error : {ex.Message}");
                return MessageResult.FailureResult("提交失败");
            }
        }

        /// <summary>
        /// 无VeevaResultHistory：是第一次，保持：isChangedDoctorInfo = true（HasNoVeeva）
        /// 有VeevaResultHistory：非第一次，isChangedDoctorInfo = 根据是否有变更（Changed）
        /// zhx20250227 是否要考虑：对于一条新的VndApp（可能是同一个Vendor的多次提交申请，也会生成一条新的VndApp），
        /// 新VndApp没有VeevaResultHistory，会始终返回True
        /// </summary>
        /// <returns></returns>
        private (bool, bool) HasNoVeevaOrChanged(Guid vndAppId, string vendorCode, string spName)
        {
            _logger.LogInformation($"HasNoVeevaOrChanged() Begin");
            try
            {
                //通过VendorCode（医生唯一标识）找它是否已有VeevaResultHistory
                var repo = LazyServiceProvider.LazyGetService<IVeevaResultHistoryRepository>().GetQueryableAsync().GetAwaiterResult().AsNoTracking();
                var lastResult = repo.OrderByDescending(a => a.CreationTime).FirstOrDefault(a => a.VendorCode == vendorCode
                    && a.DcrResultApproveStatus == DcrResultApproveStatus.Approved.GetDescription());

                //是第一次
                if (string.IsNullOrWhiteSpace(lastResult?.VeevaResultFieldsContent))
                {
                    return (false, true);
                }

                //非第一次
                var lastRstFields = JsonConvert.DeserializeObject<VeevaResultFieldsDto>(lastResult.VeevaResultFieldsContent);
                if (lastRstFields == null)
                {
                    return (false, true);
                }

                //本次供应商申请相比最后一个Veeva验证的结果，是否有变更（Changed）
                var vndAppRepo = LazyServiceProvider.LazyGetService<IVendorApplicationRepository>().GetQueryableAsync().GetAwaiterResult().AsNoTracking();
                var vndApp = vndAppRepo.FirstOrDefault(a => a.Id == vndAppId);
                var vndAppFields = ObjectMapper.Map<VendorApplication, VeevaResultFieldsDto>(vndApp);
                vndAppFields.SPName = spName;

                #region 处理学协会 Id 和 IsChanged 不参与比较
                if (!string.IsNullOrWhiteSpace(vndAppFields.AcademicPosition))
                {
                    var academics = JsonConvert.DeserializeObject<List<AcademicPositionDto>>(vndAppFields.AcademicPosition);
                    //不需要 Id 和 IsChanged 参与比较 先把值都恢复默认值
                    academics.ForEach(a =>
                    {
                        a.Id = "";
                        a.IsChanged = false;
                    });
                    vndAppFields.AcademicPosition = JsonConvert.SerializeObject(academics);
                }
                if (!string.IsNullOrWhiteSpace(lastRstFields.AcademicPosition))
                {
                    var academics = JsonConvert.DeserializeObject<List<AcademicPositionDto>>(lastRstFields.AcademicPosition);
                    //不需要 Id 和 IsChanged 参与比较 先把值都恢复默认值
                    academics.ForEach(a =>
                    {
                        a.Id = "";
                        a.IsChanged = false;
                    });
                    lastRstFields.AcademicPosition = JsonConvert.SerializeObject(academics);
                }
                #endregion
                var result = !vndAppFields.PropertyEqualAll(lastRstFields);//学协会字段就按字符串来对比（JSON串）
                _logger.LogInformation($"HasNoVeevaOrChanged() End");
                return (true, result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"HasNoVeevaOrChanged() Error: {ex.Message}");
            }
            return (false, false);
        }

        /// <summary>
        /// 验证修改的内容
        /// </summary>
        /// <param name="request"></param>
        /// <param name="vendor"></param>
        /// <param name="vendorPersonal"></param>
        /// <param name="vendorFinancial"></param>
        /// <exception cref="NotImplementedException"></exception>
        private async Task<(bool, bool, bool)> GetChangeContent(SaveSpeakerRequestDto request, IVendorRepository vendor, IVendorPersonalRepository vendorPersonal, IVendorFinancialRepository vendorFinancial)
        {
            var isChangedDoctor = false;
            var isDPSCheck = false;
            var isFinance = false;
            var vendorQuery = await vendor.GetQueryableAsync();
            var vendorPersonalQuery = await vendorPersonal.GetQueryableAsync();
            var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");
            //供应商信息
            var vendorData = vendorQuery.Join(vendorPersonalQuery, a => a.Id, b => b.VendorId, (a, b) => new { vendorData = a, vendorPersonalData = b })
                .FirstOrDefault(f => f.vendorData.VendorCode == request.VendorCode);
            //财务信息
            var vendorFinancialData = await vendorFinancial.GetListAsync(g => g.VendorId == vendorData.vendorData.Id);

            #region 获取变动的字段

            //5001（优先）【Issue】【讲者变更】若只改了职业证书编码/是否院士
            string[] judgeDoctorInfoChangedFields = [nameof(Entities.Vendors.Vendor.HospitalId), nameof(Entities.Vendors.Vendor.StandardHosDepId), nameof(Entities.Vendors.Vendor.CertificateCode), nameof(Entities.Vendors.Vendor.IsAcademician), nameof(Entities.Vendors.Vendor.PTId), nameof(Entities.Vendors.Vendor.AcademicLevel), nameof(Entities.Vendors.Vendor.AcademicPosition), nameof(Entities.Vendors.Vendor.SPLevel), nameof(Entities.Vendors.Vendor.HosDepartment), nameof(VendorPersonal.SPName)];
            string[] judgeDpsCheckFields = [nameof(VendorPersonal.SPName), nameof(VendorPersonal.Province), nameof(VendorPersonal.City), nameof(VendorPersonal.Address), nameof(VendorPersonal.PostCode)];
            //2771[供应商管理][讲者&非HCP个人]目前允许了修改身份证号，需要身份证号修改时设置isFinanceInfo为true
            string[] judgeBankInfoChangedFields = [nameof(VendorPersonal.Sex), nameof(VendorPersonal.CardNo), nameof(Entities.Vendors.Vendor.BankCode), nameof(Entities.Vendors.Vendor.BankCardNo), nameof(Entities.Vendors.Vendor.BankCity), nameof(Entities.Vendors.Vendor.BankNo), nameof(VendorPersonal.SPName), nameof(Entities.Vendors.Vendor.BankSwiftCode), nameof(Entities.Vendors.Vendor.HandPhone)];

            //获取变更的字段列表
            var getChangeFields = (object o1, object o2) =>
            {
                var o1Properties = o1.GetType().GetProperties();
                var o2Properties = o2.GetType().GetProperties();
                var needCheckFields = judgeDoctorInfoChangedFields.Union(judgeDpsCheckFields).Union(judgeBankInfoChangedFields);

                var list = new List<string>();
                foreach (var p in o1Properties)
                {
                    //不需要比较的字段直接跳过
                    if (!needCheckFields.Contains(p.Name))
                        continue;

                    var o2Property = o2Properties.FirstOrDefault(a => a.Name == p.Name);
                    if (o2Property == null)
                        continue;

                    var o1Value = p.GetValue(o1);
                    var o2Value = o2Property.GetValue(o2);
                    //2766【供应商管理】对于空字符串和null应该都视为空，在提交供应商变更申请的时候，不应该认为发生了Change
                    if ((p.PropertyType == typeof(string) && string.IsNullOrEmpty(o1Value as string)) && (o2Property.PropertyType == typeof(string) && string.IsNullOrEmpty(o2Value as string)))
                        continue;

                    if (!object.Equals(o1Value, o2Value))
                        list.Add(p.Name);
                }
                return list;
            };

            //医师主体信息
            var changeFields = getChangeFields(vendorData.vendorData, request);
            //医师个人信息
            changeFields.AddRange(getChangeFields(vendorData.vendorPersonalData, request));
            //学会任职信息
            var academics = string.IsNullOrEmpty(vendorData.vendorData.AcademicPosition) ? [] : JsonConvert.DeserializeObject<IEnumerable<AcademicPositionDto>>(vendorData.vendorData.AcademicPosition);
            //传入的学协会
            var newAcademics = request.AcademicPositionJson ?? new List<AcademicPositionDto>();

            //数量不一致，就一定是存在变更
            if (academics.Count() != newAcademics.Count)
                changeFields.Add(nameof(vendorData.vendorData.AcademicPosition));
            else
            {
                //通过几个字段关联后，数量对不上，也意味着有变更
                var matchDatas = academics.Join
                (
                    newAcademics,
                    a => new { a.JournalName, a.JournalCategory, a.JournalCategoryName, a.Appointment, a.AppointmentName, a.AppointmentStatus, a.AppointmentStatusName },
                    a => new { a.JournalName, a.JournalCategory, a.JournalCategoryName, a.Appointment, a.AppointmentName, a.AppointmentStatus, a.AppointmentStatusName },
                    (a, b) => new { OldAcademic = a, NewAcademic = b }
                ).ToArray();
                if (matchDatas.Count() != academics.Count())
                    changeFields.Add(nameof(vendorData.vendorData.AcademicPosition));
            }

            //移除特殊字段，后面单独判断
            string[] specialFields = [nameof(vendorData.vendorPersonalData.CardNo), nameof(Entities.Vendors.Vendor.BankCardNo), nameof(Entities.Vendors.Vendor.BankCity), nameof(vendorData.vendorPersonalData.Province), nameof(vendorData.vendorPersonalData.City)];
            changeFields.RemoveAll(a => specialFields.Contains(a));

            //省份、城市
            if (request.ProvinceCity.FirstOrDefault() != vendorData.vendorPersonalData.Province)
                changeFields.Add(nameof(vendorData.vendorPersonalData.Province));
            if (request.ProvinceCity.LastOrDefault() != vendorData.vendorPersonalData.City)
                changeFields.Add(nameof(vendorData.vendorPersonalData.City));

            //银行卡号
            if (request.BankCardNo != AesHelper.Decryption(vendorData.vendorData.BankCardNo, insightKey))
                changeFields.Add(nameof(vendorData.vendorData.BankCardNo));

            //银行所在城市
            if (request.BankCity.JoinAsString(",") != vendorData.vendorData.BankCity)
                changeFields.Add(nameof(vendorData.vendorData.BankCity));

            //身份证号
            if (request.CardNo != AesHelper.Decryption(vendorData.vendorPersonalData.CardNo, insightKey))
                changeFields.Add(nameof(vendorData.vendorPersonalData.CardNo));

            //有新增或者激活行
            var isCompanyChanged = new VendorCommon().CheckFinancialChanged(request.FinancialInformation, vendorFinancialData);

            #endregion

            //判断是否需要医师信息验证审批
            if (changeFields.Intersect(judgeDoctorInfoChangedFields).Any())
                isChangedDoctor = true;

            //判断是否需要IFO DPS Check
            if (changeFields.Intersect(judgeDpsCheckFields).Any() || isCompanyChanged)
                isDPSCheck = true;

            //判断是否需要财务供应商维护岗审批
            if (changeFields.Intersect(judgeDpsCheckFields).Any() || changeFields.Intersect(judgeBankInfoChangedFields).Any() || isCompanyChanged)
                isFinance = true;

            #region Old code

            ////匹配医师信息
            //if (request.HospitalId != vendorData.vendorData.HospitalId || request.StandardHosDepId != vendorData.vendorData.StandardHosDepId ||
            //    request.PTId != vendorData.vendorData.PTId || request.AcademicLevel != vendorData.vendorData.AcademicLevel ||
            //    (request.AcademicPositionJson != null ? JsonConvert.SerializeObject(request.AcademicPositionJson) : "") != vendorData.vendorData.AcademicPosition
            //    || request.SPLevel != vendorData.vendorData.SPLevel ||
            //    request.HosDepartment != vendorData.vendorData.HosDepartment || request.CertificateCode != vendorData.vendorData.CertificateCode)
            //{
            //    isChangedDoctor = true;
            //}

            ////匹配银行信息
            //if (request.Sex != vendorData.vendorPersonalData.Sex || request.BankCode != vendorData.vendorData.BankCode ||
            //    request.BankCardNo != AesHelper.Decryption(vendorData.vendorData.BankCardNo, insightKey) || request.BankCity.JoinAsString(",") != vendorData.vendorData.BankCity ||
            //    request.BankNo != vendorData.vendorData.BankNo)
            //{
            //    isFinance = true;
            //}

            ////匹配地址信息
            //if (request.ProvinceCity[0] != vendorData.vendorPersonalData.Province || request.ProvinceCity[1] != vendorData.vendorPersonalData.City ||
            //    request.Address != vendorData.vendorPersonalData.Address || request.PostCode != vendorData.vendorPersonalData.PostCode)
            //{
            //    isDPSCheck = true;
            //    isFinance = true;
            //}
            ////匹配公司信息
            //if (request.FinancialInformation.Count != vendorFinancialData.Count)
            //{
            //    isDPSCheck = true;
            //    isFinance = true;
            //}
            ////名称
            //if (request.SPName != vendorData.vendorPersonalData.SPName)
            //{
            //    isChangedDoctor = true;
            //    isDPSCheck = true;
            //    isFinance = true;
            //}
            ////操作财务表中的激活按钮
            //if (request.FinancialInformation.FirstOrDefault(f => f.FinancialVendorStatus == FinancialVendorStatus.ToBeActivated) != null)
            //{
            //    isDPSCheck = true;
            //    isFinance = true;
            //}

            #endregion

            return (isChangedDoctor, isDPSCheck, isFinance);
        }

        /// <summary>
        /// 克隆草稿
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MessageResult> CloneSpeakerAsync(SaveSpeakerRequestDto request)
        {
            var vendor = _serviceProvider.GetService<IVendorRepository>();
            var vendorPersonal = _serviceProvider.GetService<IVendorPersonalRepository>();
            var vendorApplication = _serviceProvider.GetService<IVendorApplicationRepository>();
            var vendorApplicationPersonal = _serviceProvider.GetService<IVendorApplicationPersonalRepository>();
            var vendorApplicationFinancial = _serviceProvider.GetService<IVendorApplicationFinancialRepository>();
            var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");
            var getAssociationGrade = await _dataverseService.GetDictionariesAsync(DictionaryType.AssociationGrade);
            var getAssociationType = await _dataverseService.GetDictionariesAsync(DictionaryType.AssociationType);
            var getAssociationJob1 = await _dataverseService.GetDictionariesAsync(DictionaryType.AssociationJob1);
            var getAssociationJob2 = await _dataverseService.GetDictionariesAsync(DictionaryType.AssociationJob2);
            var getAssociationJobStatus = await _dataverseService.GetDictionariesAsync(DictionaryType.AssociationJobStatus);
            var commonService = LazyServiceProvider.LazyGetService<ICommonService>();


            //新产生数据ID清空
            request.ID = null;
            var applyUserId = await ValidationVendorApplicationDuplication(vendorApplication, request.VendorCode);
            if (applyUserId != null)
            {
                var _identityUserRepository = _serviceProvider.GetService<IIdentityUserRepository>();
                var user = await _identityUserRepository.FindAsync((Guid)applyUserId);
                if (user != null)
                {
                    return MessageResult.SuccessResult(messageModel: new MessageModelBase(603, $"供应商正在由{user.Name}({user.Email})申请中，无法重复申请"));
                }
                else
                {
                    return MessageResult.FailureResult("供应商正在被申请，但没有找到申请人信息");
                }
            }
            //城市处理
            if (request.ProvinceCity != null)
            {
                request.Province = request.ProvinceCity[0];
                request.City = request.ProvinceCity[1];
            }
            //如果是更新或激活草稿添加，判断VendorCode是否存在
            var vendorOld = await vendor.FirstOrDefaultAsync(f => f.VendorCode == request.VendorCode);
            if (vendorOld == null) return MessageResult.FailureResult($"{request.VendorCode}数据不存在于正式库，修改时请填写VendorCode");
            //查看是否存在供应商申请表
            var vendorDetailOld = await vendorApplication
                .FirstOrDefaultAsync(f => f.VendorCode == request.VendorCode &&
                f.ApplyUserId == request.ApplyUserId &&
                (f.Status == Statuses.Saved || f.Status == Statuses.Approving));
            if (vendorDetailOld != null)
                return MessageResult.FailureResult($"{vendorDetailOld.ApplicationCode}编号讲者数据已存在草稿或发起审批，请勿重复创建");
            //检查是否他人正在发起编辑审批
            var vendorDetailOldTwo = await vendorApplication
                .FirstOrDefaultAsync(f => f.VendorCode == request.VendorCode &&
                f.Status == Statuses.Approving);
            if (vendorDetailOldTwo != null)
                return MessageResult.FailureResult($"{vendorDetailOldTwo.ApplicationCode}编号讲者数据已由他人发起审批，请勿重复创建");

            var vendorDetail = ObjectMapper.Map<SaveSpeakerRequestDto, Entities.VendorApplications.VendorApplication>(request);

            //2745【供应商管理】【讲者管理】存在EPD医生主键的讲者，由非EPD部分发起变更/激活时，会导致EPDID丢失--BE
            vendorDetail.EpdId = request.EpdId ?? vendorOld.EpdId;

            vendorDetail.ApplyUserId = CurrentUser.Id ?? Guid.Empty;
            vendorDetail.ApplyUserName = CurrentUser?.Name;
            vendorDetail.ApplyUserBu = request.ApplyUserBu?.ToString();
            #region 根据单据上选择的申请组织ID获取对应的名称、BU和全链路组织名称
            var orgs = await _dataverseService.GetOrganizations();
            var orgDto = orgs.FirstOrDefault(x => x.Id == Guid.Parse(vendorDetail.ApplyUserBu));
            if (orgDto != null)
            {
                var orgTree2BU = await commonService.GetParentOrgs(orgDto, orgs);
                vendorDetail.ApplyDeptName = orgTree2BU.First().DepartmentName;
                vendorDetail.ApplyBuId = orgTree2BU.Last().Id;
                vendorDetail.ApplyBuName = orgTree2BU.Last().DepartmentName;
                var departementNames = orgTree2BU.GroupBy(d => d.Id)  // 根据Id分组
                    .Select(g => g.First())  // 从每组中选择第一个元素
                    .Select(x => x.DepartmentName).ToList();
                if (departementNames.Any())
                    vendorDetail.ApplyUserBuName = string.Join('-', departementNames.ToArray().Reverse());
            }
            #endregion
            vendorDetail.VendorType = VendorTypes.HCPPerson;
            vendorDetail.Status = Enums.Statuses.Saved;
            vendorDetail.VerificationStatus = VerificationStatuses.ToBeConfirmed;
            //vendorDetail.BImproved = request.ApplicationType != Enums.ApplicationTypes.Create;
            vendorDetail.BImproved = VendorCommon.CheckBImprovedHCP(request);
            vendorDetail.BAuth = false;
            vendorDetail.DraftVersion = request.DraftVersion + 1;
            vendorDetail.BankCity = request.BankCity?.JoinAsString(",");
            vendorDetail.BankCardImg = request.BankCardImg?.AttachmentId.ToString();
            vendorDetail.OpenId = vendorOld.OpenId;
            vendorDetail.UserId = vendorOld.UserId;
            vendorDetail.VendorId = vendorOld.VendorId;// veeva id
            vendorDetail.HCPType = vendorOld.HCPType;// veeva返回的医护人员类型
            vendorDetail.RelationType = vendorOld.RelationType;//veeva返回的职务

            vendorDetail.EpdHospitalId = request.EpdHospitalCode;
            await SetHospitalIdOrName(vendorDetail, (Guid)request.HospitalId, _dataverseService);
            await SetDempartmentName(vendorDetail, (Guid)request.StandardHosDepId, _dataverseService);
            await SetProfessionalTitleName(vendorDetail, (Guid)request.PTId, _dataverseService);

            if (request.AcademicPositionJson?.Count > 0)
            {
                //学会/期刊
                request.AcademicPositionJson?.ForEach(f =>
                {
                    f.Id = Guid.NewGuid().ToString();
                    f.JournalTypeName = !string.IsNullOrEmpty(f.JournalTypeName) ? f.JournalTypeName : getAssociationType.FirstOrDefault(w => w.Code == f.JournalType)?.Name;
                    f.JournalCategoryName = !string.IsNullOrEmpty(f.JournalCategoryName) ? f.JournalCategoryName : getAssociationGrade.FirstOrDefault(w => w.Code == f.JournalCategory)?.Name;
                    f.AppointmentName = !string.IsNullOrEmpty(f.AppointmentName) ? f.AppointmentName : getAssociationJob1.FirstOrDefault(w => w.Code == f.Appointment)?.Name;
                    f.AppointmentName = !string.IsNullOrEmpty(f.AppointmentName) ? f.AppointmentName : getAssociationJob2.FirstOrDefault(w => w.Code == f.Appointment)?.Name;
                    f.AppointmentStatusName = !string.IsNullOrEmpty(f.AppointmentStatusName) ? f.AppointmentStatusName : getAssociationJobStatus.FirstOrDefault(w => w.Code == f.AppointmentStatus)?.Name;
                });

                #region 将变更的学协会信息设置flag，在DCR信息验证回写时，替换设置flag=true的数据

                //学会任职信息
                var existAcademics = string.IsNullOrEmpty(vendorOld.AcademicPosition) ? [] : JsonConvert.DeserializeObject<IEnumerable<AcademicPositionDto>>(vendorOld.AcademicPosition);
                //通过几个字段关联后，数量对不上，也意味着有变更
                var matchDatas = request.AcademicPositionJson?
                .GroupJoin
                (
                    existAcademics,
                    a => new { a.JournalName, a.JournalCategory, a.JournalCategoryName, a.Appointment, a.AppointmentName, a.AppointmentStatus, a.AppointmentStatusName },
                    a => new { a.JournalName, a.JournalCategory, a.JournalCategoryName, a.Appointment, a.AppointmentName, a.AppointmentStatus, a.AppointmentStatusName },
                    (a, b) => new { NewAcademic = a, OldAcademics = b }
                )
                .SelectMany(a => a.OldAcademics.DefaultIfEmpty(), (a, b) => new { a.NewAcademic, OldAcademic = b })
                .ToArray();

                //将未匹配上的数据设置为已变更
                foreach (var item in matchDatas)
                {
                    if (item.OldAcademic == null)
                        item.NewAcademic.IsChanged = true;
                }

                #endregion

                vendorDetail.AcademicPosition = JsonConvert.SerializeObject(matchDatas.Select(a => a.NewAcademic));
            }
            else
                vendorDetail.AcademicPosition = null;

            vendorDetail.AttachmentInformation = request.AttachmentInformation.Count > 0 ? string.Join(",", request.AttachmentInformation.Select(s => s.AttachmentId).ToList()) : "";
            if (!string.IsNullOrEmpty(vendorDetail.BankCardNo))
                vendorDetail.BankCardNo = AesHelper.Encryption(vendorDetail.BankCardNo, insightKey);

            //记录上一版数据内容
            var getSpeakerResponse = await GetSpeakerDetailAsync(vendorOld.Id);
            if (getSpeakerResponse.Success)
            {
                var speaker = getSpeakerResponse.Data as SpeakerDetailResponseDto;
                //加密
                if (!string.IsNullOrEmpty(speaker.CardNo))
                    speaker.CardNo = AesHelper.Encryption(speaker.CardNo, insightKey);
                if (!string.IsNullOrEmpty(speaker.BankCardNo))
                    speaker.BankCardNo = AesHelper.Encryption(speaker.BankCardNo, insightKey);
                vendorDetail.UpdatePreJson = JsonConvert.SerializeObject(speaker);
            }

            //vendorDetail.ApplicationCode = await GetSerialNoAsync(vendorApplication, "V");
            //var newRecord = await vendorApplication.InsertAsync(vendorDetail, true);
            await InsertAndGenerateSerialNoAsync(vendorApplication, vendorDetail);

            //新增供应商个人申请表
            var vendorPersonalDetail = ObjectMapper.Map<SaveSpeakerRequestDto, VendorApplicationPersonal>(request);
            vendorPersonalDetail.ApplicationId = vendorDetail.Id;
            vendorPersonalDetail.CardPic = request.CardPic?.AttachmentId.ToString();
            if (!string.IsNullOrEmpty(vendorPersonalDetail.CardNo))
                vendorPersonalDetail.CardNo = AesHelper.Encryption(vendorPersonalDetail.CardNo, insightKey);
            var createPersonalEntity = await vendorApplicationPersonal.InsertAsync(vendorPersonalDetail, true);
            //新增供应商财务信息表
            var financeDetailList = ObjectMapper.Map<List<FinancialInformation>, List<VendorApplicationFinancial>>(request.FinancialInformation);
            var vendorTypeCfgs = await _dataverseService.GetVendorTypeCfgAsync();
            foreach (var item in financeDetailList)
            {
                item.ApplicationId = vendorDetail.Id;
                if (item.FinancialVendorStatus == 0)
                    item.FinancialVendorStatus = FinancialVendorStatus.ToBeEffective;
                if (string.IsNullOrEmpty(item.PaymentTerm))
                    item.PaymentTerm = vendorTypeCfgs.FirstOrDefault(f => f.VendorType == ((int)VendorTypes.HCPPerson).ToString())?.PaymentTerms;
            }
            await vendorApplicationFinancial.InsertManyAsync(financeDetailList, true);

            return MessageResult.SuccessResult(vendorDetail.Id);
        }
        /// <summary>
        /// 添加审批任务
        /// </summary>
        /// <param name="request"></param>
        /// <param name="purExemptDetail"></param>
        /// <returns></returns>
        private async Task<bool> CreateWorkflowAsync(SaveSpeakerRequestDto request, Guid draftId, string draftCode, bool isChangedDoctorInfo, bool isDPSCheckInfo, bool isFinanceInfo, VendorApplication vendorApplication)
        {
            //创建审批任务
            var exemptType = EnumUtil.GetEnumIdValues<WorkflowTypeName>().ToDictionary(a => (WorkflowTypeName)a.Key, a => a.Value);

            var name = request.ApplicationType == Enums.ApplicationTypes.Update ? exemptType[WorkflowTypeName.SpeakerChange] : exemptType[WorkflowTypeName.SpeakerRequest];
            var createApproval = new CreateApprovalDto
            {
                Name = name,
                Department = vendorApplication.ApplyUserBu,
                BusinessFormId = draftId.ToString(),
                BusinessFormNo = draftCode,
                BusinessFormName = EntitiesNameConsts.NameConsts.VendorApplication,
                Submitter = CurrentUser?.Id.ToString(),
                OriginalApprovalId = vendorApplication.ApplyUserId,
                FormData = "{\"isChangedDoctorInfo\" : " + isChangedDoctorInfo.ToString().ToLower() + ", \"isDPSCheckInfo\" : " + isDPSCheckInfo.ToString().ToLower() + ", \"isFinanceInfo\" : " + isFinanceInfo.ToString().ToLower() + "}",
                WorkflowType = request.ApplicationType == Enums.ApplicationTypes.Update ? WorkflowTypeName.SpeakerChange : WorkflowTypeName.SpeakerRequest,
                InstanceName = $"{name}-{DateTime.Now.ToString("yyMMdd-hhmmss")}"
            };
            var approvalRecord = LazyServiceProvider.LazyGetService<IInitializeApprovalRecordService>();
            await approvalRecord.InsertInitApprovalRecordAsync(new InitializeApprovalRecordDto
            {
                BusinessFormName = EntitiesNameConsts.NameConsts.VendorApplication,
                BusinessId = draftId,
                InstanceId = Guid.NewGuid(),
                Status = InitApprovalRecordStatus.Pending,
                MaxRetries = 3,
                RequestContent = JsonConvert.SerializeObject(createApproval)
            });
            return true;
        }

        private async Task SetHospitalIdOrName(Entities.VendorApplications.VendorApplication vendorApp
            , Guid HospitalId, IDataverseService dataverseService)
        {
            var ppHospitals = await dataverseService.GetAllHospitals();
            var ppExsit = ppHospitals.FirstOrDefault(a => a.Id == HospitalId);
            vendorApp.HospitalName = ppExsit != null ? ppExsit.Name : "";
        }

        private async Task SetDempartmentName(Entities.VendorApplications.VendorApplication vendorApp
            , Guid StandardHosDepId, IDataverseService dataverseService)
        {
            var ppDept = await dataverseService.GetAllDepartments();
            var ppExsit = ppDept.FirstOrDefault(a => a.Id == StandardHosDepId);
            vendorApp.StandardHosDepName = ppExsit != null ? ppExsit.Name : "";
        }

        private async Task SetProfessionalTitleName(Entities.VendorApplications.VendorApplication vendorApp
            , Guid PTId, IDataverseService dataverseService)
        {
            var ppJobTiles = await dataverseService.GetAllJobTiles();
            var ppExsit = ppJobTiles.FirstOrDefault(a => a.Id == PTId);
            vendorApp.PTName = ppExsit != null ? ppExsit.Name : "";
        }
        #region 任务中心
        /// <summary>
        /// 获取任务中心我发起的供应商列表
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<PagedResultDto<SpeakerTaskCenterListResponseDto>> GetInitiatedSpeakerListAsync(SpeakerTaskCenterListRequestDto request)
        {
            try
            {
                var vendorQuery = await LazyServiceProvider.LazyGetService<IVendorApplicationReadonlyRepository>().GetQueryableAsync();
                var vendorPersonalQuery = await LazyServiceProvider.LazyGetService<IVendorApplicationPersonalReadonlyRepository>().GetQueryableAsync();
                var vendorApplicationOrgQuery = await LazyServiceProvider.LazyGetService<IVendorApplicationOrganizationReadonlyRepository>().GetQueryableAsync();
                var identityUserQuery = await LazyServiceProvider.LazyGetService<Entities.User.IIdentityUserReadonlyRepository>().GetQueryableAsync();

                var status = EnumUtil.GetEnumIdValues<Enums.Statuses>().ToDictionary(a => (Enums.Statuses)a.Key, a => a.Value);
                var VendorType = EnumUtil.GetEnumIdValues<VendorTypes>().ToDictionary(a => (VendorTypes)a.Key, a => a.Value);

                #region 获取代理信息

                var agents = await LazyServiceProvider.LazyGetService<IAgencyService>().GetOriginalOperators(new GetAgentOperatorsRequestDto { BusinessTypeCategory = ResignationTransfer.TaskFormCategory.VendorApplication });
                var userIds = agents.Select(a => a.Key).Distinct().ToArray();
                var principalIds = userIds.Where(x => x != CurrentUser.Id.Value).ToArray();

                #endregion

                var pageResult = new List<SpeakerTaskCenterListResponseDto>();
                var responseDtosCount = 0;
                if (request.VendorType == VendorTypes.HCPPerson)
                {
                    //供应商个人链表供应商
                    var responseDtos = vendorQuery.Join(vendorPersonalQuery, a => a.Id, b => b.ApplicationId, (a, b) => new { vendorQuery = a, vendorPersonalQuery = b })
                        .GroupJoin(identityUserQuery, a => a.vendorQuery.ApplyUserId, a => a.Id, (a, b) => new { a.vendorQuery, a.vendorPersonalQuery, Users = b })
                        .SelectMany(a => a.Users.DefaultIfEmpty(), (a, b) => new { a.vendorQuery, a.vendorPersonalQuery, User = b })
                        .Where(w => (w.vendorQuery.ApplyUserId == CurrentUser.Id.Value && !w.vendorQuery.TransfereeId.HasValue) ||//申请人为自己且无转办人
                            CurrentUser.Id.Value == w.vendorQuery.TransfereeId.Value || //转办人为自己
                            principalIds.ToHashSet().Contains(w.vendorQuery.ApplyUserId))//代理人为自己
                        .WhereIf(request.ApplicationType.HasValue, w => w.vendorQuery.ApplicationType == request.ApplicationType.Value)
                        .WhereIf(request.VendorType.HasValue, p => p.vendorQuery.VendorType == request.VendorType)
                        .WhereIf(!request.VendorType.HasValue, x => x.vendorQuery.VendorType != VendorTypes.HCPPerson)
                        .WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), p => p.vendorQuery.ApplicationCode == request.ApplicationCode)
                        .WhereIf(!string.IsNullOrEmpty(request.SPName), p => p.vendorPersonalQuery.SPName.Contains(request.SPName))
                        .WhereIf(request.SubmissionStartTime.HasValue, p => p.vendorQuery.ApplyTime >= request.SubmissionStartTime)
                        .WhereIf(request.SubmissionEndTime.HasValue, p => p.vendorQuery.ApplyTime <= request.SubmissionEndTime)
                        .WhereIf(request.ApprovalStatus.HasValue, p => p.vendorQuery.Status == request.ApprovalStatus)
                        .WhereIf(request.ProcessingStatus == ProcessingStatus.PendingProcessing, w => w.vendorQuery.Status == Statuses.Returned || w.vendorQuery.Status == Statuses.Withdraw)
                        .WhereIf(request.ProcessingStatus == ProcessingStatus.Progressing, w => w.vendorQuery.Status == Statuses.Approving)
                        .WhereIf(request.ProcessingStatus == ProcessingStatus.Completed, w => w.vendorQuery.Status == Statuses.Rejected || w.vendorQuery.Status == Statuses.Passed || w.vendorQuery.Status == Statuses.Delete)
                        .Select(g => new SpeakerTaskCenterListResponseDto
                        {
                            Id = g.vendorQuery.Id,
                            VendorType = g.vendorQuery.VendorType,
                            VendorTypeName = g.vendorQuery.VendorType != 0 ? VendorType[g.vendorQuery.VendorType] : null,
                            ApplicationCode = g.vendorQuery.ApplicationCode,
                            SPName = g.vendorPersonalQuery.SPName,
                            ApplyUserId = g.vendorQuery.ApplyUserId,
                            ApplyUserBu = g.vendorQuery.ApplyUserBu,
                            ApplyUserBuName = g.vendorQuery.ApplyUserBuName,
                            ApprovalStatus = g.vendorQuery.Status,
                            ApprovalStatusString = g.vendorQuery.Status != 0 ? status[g.vendorQuery.Status] : null,
                            ApplyTime = g.vendorQuery.ApplyTime,
                            ApplyUserName = g.User.Name,
                            ApplicationType = g.vendorQuery.ApplicationType
                        });

                    responseDtosCount = responseDtos.Count();
                    pageResult = responseDtos.OrderByDescending(o => o.ApplyTime).Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToList();
                }
                else//如果不是个人，还需要查询机构表
                {
                    //供应商机构链表供应商
                    var responseDtos = vendorQuery.Join(vendorPersonalQuery, a => a.Id, b => b.ApplicationId, (a, b) => new { vendorQuery = a, vendorPersonalQuery = b })
                    .GroupJoin(identityUserQuery, a => a.vendorQuery.ApplyUserId, a => a.Id, (a, b) => new { a.vendorQuery, a.vendorPersonalQuery, Users = b })
                    .SelectMany(a => a.Users.DefaultIfEmpty(), (a, b) => new { a.vendorQuery, a.vendorPersonalQuery, User = b })
                    //.Where(w => userIds.Contains(w.vendorQuery.ApplyUserId)) //只查询自己的
                    .Where(w => (w.vendorQuery.ApplyUserId == CurrentUser.Id.Value && !w.vendorQuery.TransfereeId.HasValue) ||//申请人为自己且无转办人
                            CurrentUser.Id.Value == w.vendorQuery.TransfereeId.Value || //转办人为自己
                            principalIds.ToHashSet().Contains(w.vendorQuery.ApplyUserId))//代理人为自己
                    .WhereIf(request.ApplicationType.HasValue, w => w.vendorQuery.ApplicationType == request.ApplicationType.Value)
                    .WhereIf(request.VendorType.HasValue, p => p.vendorQuery.VendorType == request.VendorType)
                    .WhereIf(!request.VendorType.HasValue, x => x.vendorQuery.VendorType != VendorTypes.HCPPerson)
                    .WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), p => p.vendorQuery.ApplicationCode == request.ApplicationCode)
                    .WhereIf(!string.IsNullOrEmpty(request.SPName), p => p.vendorPersonalQuery.SPName.Contains(request.SPName))
                    .WhereIf(request.SubmissionStartTime.HasValue, p => p.vendorQuery.ApplyTime >= request.SubmissionStartTime)
                    .WhereIf(request.SubmissionEndTime.HasValue, p => p.vendorQuery.ApplyTime <= request.SubmissionEndTime)
                    .WhereIf(request.ApprovalStatus.HasValue, p => p.vendorQuery.Status == request.ApprovalStatus)
                    .WhereIf(request.ProcessingStatus == ProcessingStatus.PendingProcessing, w => w.vendorQuery.Status == Statuses.Returned || w.vendorQuery.Status == Statuses.Withdraw)
                    .WhereIf(request.ProcessingStatus == ProcessingStatus.Progressing, w => w.vendorQuery.Status == Statuses.Approving)
                    .WhereIf(request.ProcessingStatus == ProcessingStatus.Completed, w => w.vendorQuery.Status == Statuses.Rejected || w.vendorQuery.Status == Statuses.Passed || w.vendorQuery.Status == Statuses.Delete)
                    .Select(g => new
                    {
                        Id = g.vendorQuery.Id,
                        VendorType = g.vendorQuery.VendorType,
                        ApplicationCode = g.vendorQuery.ApplicationCode,
                        SPName = g.vendorPersonalQuery.SPName,
                        ApplyUserId = g.vendorQuery.ApplyUserId,
                        ApplyUserBu = g.vendorQuery.ApplyUserBu,
                        g.vendorQuery.ApplyUserBuName,
                        ApprovalStatus = g.vendorQuery.Status,
                        ApplyTime = g.vendorQuery.ApplyTime,
                        ApplyUserName = g.User.Name,
                        ApplicationType = g.vendorQuery.ApplicationType
                    })
                    .Union(vendorQuery.Join(vendorApplicationOrgQuery, a => a.Id, b => b.ApplicationId, (a, b) => new { vendorQuery = a, vendorPersonalQuery = b })
                        .GroupJoin(identityUserQuery, a => a.vendorQuery.ApplyUserId, a => a.Id, (a, b) => new { a.vendorQuery, a.vendorPersonalQuery, Users = b })
                        .SelectMany(a => a.Users.DefaultIfEmpty(), (a, b) => new { a.vendorQuery, a.vendorPersonalQuery, User = b })
                        //.Where(w => userIds.Contains(w.vendorQuery.ApplyUserId)) //只查询自己的
                        .Where(w => (w.vendorQuery.ApplyUserId == CurrentUser.Id.Value && !w.vendorQuery.TransfereeId.HasValue) ||//申请人为自己且无转办人
                            CurrentUser.Id.Value == w.vendorQuery.TransfereeId.Value || //转办人为自己
                            principalIds.ToHashSet().Contains(w.vendorQuery.ApplyUserId))//代理人为自己
                        .WhereIf(request.ApplicationType.HasValue, w => w.vendorQuery.ApplicationType == request.ApplicationType.Value)
                        .WhereIf(request.VendorType.HasValue, p => p.vendorQuery.VendorType == request.VendorType)
                        .WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), p => p.vendorQuery.ApplicationCode == request.ApplicationCode)
                        .WhereIf(!string.IsNullOrEmpty(request.SPName), p => p.vendorPersonalQuery.VendorName.Contains(request.SPName))
                        .WhereIf(request.SubmissionStartTime.HasValue, p => p.vendorQuery.ApplyTime >= request.SubmissionStartTime)
                        .WhereIf(request.SubmissionEndTime.HasValue, p => p.vendorQuery.ApplyTime <= request.SubmissionEndTime)
                        .WhereIf(request.ApprovalStatus.HasValue, p => p.vendorQuery.Status == request.ApprovalStatus)
                        .WhereIf(request.ProcessingStatus == ProcessingStatus.PendingProcessing, w => w.vendorQuery.Status == Statuses.Returned || w.vendorQuery.Status == Statuses.Withdraw)
                        .WhereIf(request.ProcessingStatus == ProcessingStatus.Progressing, w => w.vendorQuery.Status == Statuses.Approving)
                        .WhereIf(request.ProcessingStatus == ProcessingStatus.Completed, w => w.vendorQuery.Status == Statuses.Rejected || w.vendorQuery.Status == Statuses.Passed || w.vendorQuery.Status == Statuses.Delete)
                        .Select(g => new
                        {
                            Id = g.vendorQuery.Id,
                            VendorType = g.vendorQuery.VendorType,
                            ApplicationCode = g.vendorQuery.ApplicationCode,
                            SPName = g.vendorPersonalQuery.VendorName,
                            ApplyUserId = g.vendorQuery.ApplyUserId,
                            ApplyUserBu = g.vendorQuery.ApplyUserBu,
                            g.vendorQuery.ApplyUserBuName,
                            ApprovalStatus = g.vendorQuery.Status,
                            ApplyTime = g.vendorQuery.ApplyTime,
                            ApplyUserName = g.User.Name,
                            ApplicationType = g.vendorQuery.ApplicationType
                        })
                    ).Select(g => new SpeakerTaskCenterListResponseDto
                    {
                        Id = g.Id,
                        VendorType = g.VendorType,
                        VendorTypeName = g.VendorType != 0 ? VendorType[g.VendorType] : null,
                        ApplicationCode = g.ApplicationCode,
                        SPName = g.SPName,
                        ApplyUserId = g.ApplyUserId,
                        ApplyUserBu = g.ApplyUserBu,
                        ApplyUserBuName = g.ApplyUserBuName,
                        ApprovalStatus = g.ApprovalStatus,
                        ApprovalStatusString = g.ApprovalStatus != 0 ? status[g.ApprovalStatus] : null,
                        ApplyTime = g.ApplyTime,
                        ApplyUserName = g.ApplyUserName,
                        ApplicationType = g.ApplicationType
                    });

                    responseDtosCount = responseDtos.Count();
                    pageResult = responseDtos.OrderByDescending(o => o.ApplyTime).Skip(request.PageIndex * request.PageSize).Take(request.PageSize)
                    .ToList();
                }

                var result = new PagedResultDto<SpeakerTaskCenterListResponseDto>()
                {
                    Items = pageResult,
                    TotalCount = responseDtosCount,
                };
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError($"SpeakerService's GetInitiatedSpeakerListAsync has an error : {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取任务中心我审批的供应商列表
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<PagedResultDto<SpeakerTaskCenterListResponseDto>> GetApprovalSpeakerListAsync(SpeakerTaskCenterListRequestDto request)
        {
            try
            {
                var vendorQuery = await LazyServiceProvider.LazyGetService<IVendorApplicationReadonlyRepository>().GetQueryableAsync();
                var vendorPersonalQuery = await LazyServiceProvider.LazyGetService<IVendorApplicationPersonalReadonlyRepository>().GetQueryableAsync();
                var vendorApplicationOrgQuery = await LazyServiceProvider.LazyGetService<IVendorApplicationOrganizationReadonlyRepository>().GetQueryableAsync();
                var queryUser = await LazyServiceProvider.LazyGetService<Entities.User.IIdentityUserReadonlyRepository>().GetQueryableAsync();

                var status = EnumUtil.GetEnumIdValues<Statuses>().ToDictionary(a => (Statuses)a.Key, a => a.Value);
                var vendorTypes = EnumUtil.GetEnumIdValues<VendorTypes>().ToDictionary(a => (VendorTypes)a.Key, a => a.Value);
                var pageResult = new List<SpeakerTaskCenterListResponseDto>();
                var responseDtosCount = 0;

                //查询待处理
                if (request.ProcessingStatus == ProcessingStatus.PendingProcessing)
                {
                    var taskRecords = await _dataverseService.GetApprovelTaskAsync(CurrentUser.Id.ToString(),
                        request.VendorType == VendorTypes.HCPPerson ? [WorkflowTypeName.SpeakerRequest, WorkflowTypeName.SpeakerChange] : [WorkflowTypeName.SupplierRequestNonHCPPerson, WorkflowTypeName.SupplierRequestNonHCIInstitution, WorkflowTypeName.SupplierRequestHCIInstitution],
                        request.ProcessingStatus);

                    if (request.VendorType == VendorTypes.HCPPerson)
                    {
                        //供应商个人链表供应商
                        var datas = vendorQuery.Join(vendorPersonalQuery, a => a.Id, b => b.ApplicationId, (a, b) => new { vendorQuery = a, vendorPersonalQuery = b })
                        .Join(queryUser, a => a.vendorQuery.ApplyUserId, a => a.Id, (a, b) => new { a.vendorQuery, a.vendorPersonalQuery, User = b })
                        .Where(w => taskRecords.Select(s => s.FormId).Contains(w.vendorQuery.Id))//筛选需要展示的业务数据
                        .WhereIf(request.ApplicationType.HasValue, w => w.vendorQuery.ApplicationType == request.ApplicationType.Value)
                        .WhereIf(request.VendorType.HasValue, p => p.vendorQuery.VendorType == request.VendorType)
                        .WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), p => p.vendorQuery.ApplicationCode == request.ApplicationCode)
                        .WhereIf(!string.IsNullOrEmpty(request.SPName), p => p.vendorPersonalQuery.SPName.Contains(request.SPName))
                        .WhereIf(request.SubmissionStartTime.HasValue, p => p.vendorQuery.ApplyTime >= request.SubmissionStartTime)
                        .WhereIf(request.SubmissionEndTime.HasValue, p => p.vendorQuery.ApplyTime <= request.SubmissionEndTime)
                        .WhereIf(request.ApprovalStatus.HasValue, p => p.vendorQuery.Status == request.ApprovalStatus)
                        .Select(g => new SpeakerTaskCenterListResponseDto
                        {
                            Id = g.vendorQuery.Id,
                            VendorType = g.vendorQuery.VendorType,
                            ApplicationCode = g.vendorQuery.ApplicationCode,
                            SPName = g.vendorPersonalQuery.SPName,
                            ApplyUserId = g.vendorQuery.ApplyUserId,
                            ApplyUserName = g.User.Name,
                            ApplyUserBu = g.vendorQuery.ApplyUserBu,
                            ApplyUserBuName = g.vendorQuery.ApplyUserBuName,
                            ApprovalStatus = g.vendorQuery.Status,
                            ApplyTime = g.vendorQuery.ApplyTime,
                            ApplicationType = g.vendorQuery.ApplicationType
                        })
                        .ToArray();

                        responseDtosCount = datas.Count();
                        if (request.IsAsc)
                        {
                            pageResult = datas.Join(taskRecords, a => a.Id, a => a.FormId, (a, b) => new { Vendor = a, Task = b })
                           .OrderBy(a => a.Task.CreatedTime)
                           .Select(a =>
                           {
                               a.Vendor.VendorTypeName = a.Vendor.VendorType.HasValue ? vendorTypes[a.Vendor.VendorType.Value] : null;
                               a.Vendor.ApprovalStatusString = a.Vendor.ApprovalStatus.HasValue ? status[a.Vendor.ApprovalStatus.Value] : null;
                               a.Vendor.ApprovalStep = a.Task.Step;
                               a.Vendor.CreatedTime = a.Task.CreatedTime;

                               return a.Vendor;
                           })
                           .Skip(request.PageIndex * request.PageSize).Take(request.PageSize)
                           .ToList();
                        }
                        else
                        {
                            pageResult = datas.Join(taskRecords, a => a.Id, a => a.FormId, (a, b) => new { Vendor = a, Task = b })
                            .OrderByDescending(a => a.Task.CreatedTime)
                            .Select(a =>
                            {
                                a.Vendor.VendorTypeName = a.Vendor.VendorType.HasValue ? vendorTypes[a.Vendor.VendorType.Value] : null;
                                a.Vendor.ApprovalStatusString = a.Vendor.ApprovalStatus.HasValue ? status[a.Vendor.ApprovalStatus.Value] : null;
                                a.Vendor.ApprovalStep = a.Task.Step;
                                a.Vendor.CreatedTime = a.Task.CreatedTime;

                                return a.Vendor;
                            })
                            .Skip(request.PageIndex * request.PageSize).Take(request.PageSize)
                            .ToList();
                        }
                    }
                    else
                    {
                        //供应商机构链表供应商
                        var datas = vendorQuery.Join(vendorPersonalQuery, a => a.Id, b => b.ApplicationId, (a, b) => new { vendorQuery = a, vendorPersonalQuery = b })
                        .Join(queryUser, a => a.vendorQuery.ApplyUserId, a => a.Id, (a, b) => new { a.vendorQuery, a.vendorPersonalQuery, User = b })
                        .Where(w => taskRecords.Select(s => s.FormId).Contains(w.vendorQuery.Id))//筛选需要展示的业务数据
                        .WhereIf(request.ApplicationType.HasValue, w => w.vendorQuery.ApplicationType == request.ApplicationType.Value)
                        .WhereIf(request.VendorType.HasValue, p => p.vendorQuery.VendorType == request.VendorType)
                        .WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), p => p.vendorQuery.ApplicationCode == request.ApplicationCode)
                        .WhereIf(!string.IsNullOrEmpty(request.SPName), p => p.vendorPersonalQuery.SPName.Contains(request.SPName))
                        .WhereIf(request.SubmissionStartTime.HasValue, p => p.vendorQuery.ApplyTime >= request.SubmissionStartTime)
                        .WhereIf(request.SubmissionEndTime.HasValue, p => p.vendorQuery.ApplyTime <= request.SubmissionEndTime)
                        .WhereIf(request.ApprovalStatus.HasValue, p => p.vendorQuery.Status == request.ApprovalStatus)
                        .Select(g => new
                        {
                            Id = g.vendorQuery.Id,
                            VendorType = g.vendorQuery.VendorType,
                            ApplicationCode = g.vendorQuery.ApplicationCode,
                            SPName = g.vendorPersonalQuery.SPName,
                            ApplyUserId = g.vendorQuery.ApplyUserId,
                            ApplyUserBu = g.vendorQuery.ApplyUserBu,
                            g.vendorQuery.ApplyUserBuName,
                            ApprovalStatus = g.vendorQuery.Status,
                            ApplyTime = g.vendorQuery.ApplyTime,
                            ApplyUserName = g.User.Name,
                            ApplicationType = g.vendorQuery.ApplicationType
                        })
                        .Union(
                            vendorQuery.Join(vendorApplicationOrgQuery, a => a.Id, b => b.ApplicationId, (a, b) => new { vendorQuery = a, vendorOrgQuery = b })
                            .Join(queryUser, a => a.vendorQuery.ApplyUserId, a => a.Id, (a, b) => new { a.vendorQuery, a.vendorOrgQuery, User = b })
                           .WhereIf(request.ApplicationType.HasValue, w => w.vendorQuery.ApplicationType == request.ApplicationType.Value)
                           .WhereIf(request.VendorType.HasValue, p => p.vendorQuery.VendorType == request.VendorType)
                           .WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), p => p.vendorQuery.ApplicationCode == request.ApplicationCode)
                           .WhereIf(!string.IsNullOrEmpty(request.SPName), p => p.vendorOrgQuery.VendorName.Contains(request.SPName))
                           .WhereIf(request.SubmissionStartTime.HasValue, p => p.vendorQuery.ApplyTime >= request.SubmissionStartTime)
                           .WhereIf(request.SubmissionEndTime.HasValue, p => p.vendorQuery.ApplyTime <= request.SubmissionEndTime)
                           .WhereIf(request.ApprovalStatus.HasValue, p => p.vendorQuery.Status == request.ApprovalStatus)
                           .Where(w => taskRecords.Select(s => s.FormId).Contains(w.vendorQuery.Id))//筛选需要展示的业务数据
                           .Select(g => new
                           {
                               Id = g.vendorQuery.Id,
                               VendorType = g.vendorQuery.VendorType,
                               ApplicationCode = g.vendorQuery.ApplicationCode,
                               SPName = g.vendorOrgQuery.VendorName,
                               ApplyUserId = g.vendorQuery.ApplyUserId,
                               ApplyUserBu = g.vendorQuery.ApplyUserBu,
                               g.vendorQuery.ApplyUserBuName,
                               ApprovalStatus = g.vendorQuery.Status,
                               ApplyTime = g.vendorQuery.ApplyTime,
                               ApplyUserName = g.User.Name,
                               ApplicationType = g.vendorQuery.ApplicationType
                           })
                        )
                        .Select(g => new SpeakerTaskCenterListResponseDto
                        {
                            Id = g.Id,
                            VendorType = g.VendorType,
                            ApplicationCode = g.ApplicationCode,
                            SPName = g.SPName,
                            ApplyUserId = g.ApplyUserId,
                            ApplyUserBu = g.ApplyUserBu,
                            ApplyUserBuName = g.ApplyUserBuName,
                            ApprovalStatus = g.ApprovalStatus,
                            ApplyTime = g.ApplyTime,
                            ApplyUserName = g.ApplyUserName,
                            ApplicationType = g.ApplicationType
                        })
                        .ToArray();

                        responseDtosCount = datas.Count();

                        if (request.IsAsc)
                        {
                            pageResult = datas.Join(taskRecords, a => a.Id, a => a.FormId, (a, b) => new { Vendor = a, Task = b })
                            .OrderBy(a => a.Task.CreatedTime)
                            .Select(a =>
                            {
                                a.Vendor.VendorTypeName = a.Vendor.VendorType.HasValue ? vendorTypes[a.Vendor.VendorType.Value] : null;
                                a.Vendor.ApprovalStatusString = a.Vendor.ApprovalStatus.HasValue ? status[a.Vendor.ApprovalStatus.Value] : null;
                                a.Vendor.ApprovalStep = a.Task.Step;
                                a.Vendor.CreatedTime = a.Task.CreatedTime;

                                return a.Vendor;
                            })
                            .Skip(request.PageIndex * request.PageSize).Take(request.PageSize)
                            .ToList();
                        }
                        else
                        {
                            pageResult = datas.Join(taskRecords, a => a.Id, a => a.FormId, (a, b) => new { Vendor = a, Task = b })
                           .OrderByDescending(a => a.Task.CreatedTime)
                           .Select(a =>
                           {
                               a.Vendor.VendorTypeName = a.Vendor.VendorType.HasValue ? vendorTypes[a.Vendor.VendorType.Value] : null;
                               a.Vendor.ApprovalStatusString = a.Vendor.ApprovalStatus.HasValue ? status[a.Vendor.ApprovalStatus.Value] : null;
                               a.Vendor.ApprovalStep = a.Task.Step;
                               a.Vendor.CreatedTime = a.Task.CreatedTime;

                               return a.Vendor;
                           })
                           .Skip(request.PageIndex * request.PageSize).Take(request.PageSize)
                           .ToList();
                        }

                    }
                }
                else//已完成
                {
                    var queryWfTask = await LazyServiceProvider.LazyGetService<IWorkflowTaskReadonlyRepository>().GetQueryableAsync();
                    queryWfTask = queryWfTask.Where(a => a.ApprovalId == CurrentUser.Id);
                    if (request.VendorType == VendorTypes.HCPPerson)
                    {
                        //供应商个人链表供应商
                        var query = vendorQuery.Join(vendorPersonalQuery, a => a.Id, b => b.ApplicationId, (a, b) => new { vendorQuery = a, vendorPersonalQuery = b })
                        .Join(queryUser, a => a.vendorQuery.ApplyUserId, a => a.Id, (a, b) => new { a.vendorQuery, a.vendorPersonalQuery, User = b })
                        .Join(queryWfTask, a => a.vendorQuery.Id, a => a.FormId, (a, b) => new { a.vendorQuery, a.vendorPersonalQuery, a.User, Task = b })
                        .WhereIf(request.ApplicationType.HasValue, w => w.vendorQuery.ApplicationType == request.ApplicationType.Value)
                        .WhereIf(request.VendorType.HasValue, p => p.vendorQuery.VendorType == request.VendorType)
                        .WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), p => p.vendorQuery.ApplicationCode == request.ApplicationCode)
                        .WhereIf(!string.IsNullOrEmpty(request.SPName), p => p.vendorPersonalQuery.SPName.Contains(request.SPName))
                        .WhereIf(request.SubmissionStartTime.HasValue, p => p.vendorQuery.ApplyTime >= request.SubmissionStartTime)
                        .WhereIf(request.SubmissionEndTime.HasValue, p => p.vendorQuery.ApplyTime <= request.SubmissionEndTime)
                        .WhereIf(request.ApprovalStatus.HasValue, p => p.vendorQuery.Status == request.ApprovalStatus)
                        .Select(g => new SpeakerTaskCenterListResponseDto
                        {
                            Id = g.vendorQuery.Id,
                            VendorType = g.vendorQuery.VendorType,
                            ApplicationCode = g.vendorQuery.ApplicationCode,
                            SPName = g.vendorPersonalQuery.SPName,
                            ApplyUserId = g.vendorQuery.ApplyUserId,
                            ApplyUserName = g.User.Name,
                            ApplyUserBu = g.vendorQuery.ApplyUserBu,
                            ApplyUserBuName = g.vendorQuery.ApplyUserBuName,
                            ApprovalStatus = g.vendorQuery.Status,
                            ApplyTime = g.vendorQuery.ApplyTime,
                            ApprovalStep = g.Task.StepNo,
                            CreatedTime = g.Task.CreationTime,
                            ApprovalTime = g.Task.ApprovalTime,
                            ApplicationType = g.vendorQuery.ApplicationType
                        });

                        responseDtosCount = query.Count();
                        if (request.IsAsc)
                            pageResult = query.OrderBy(a => a.ApprovalTime).Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToList();
                        else
                            pageResult = query.OrderByDescending(a => a.ApprovalTime).Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToList();

                        pageResult.ForEach(a =>
                        {
                            a.VendorTypeName = a.VendorType.HasValue ? vendorTypes[a.VendorType.Value] : null;
                            a.ApprovalStatusString = a.ApprovalStatus.HasValue ? status[a.ApprovalStatus.Value] : null;
                        });
                    }
                    else
                    {
                        //供应商机构链表供应商
                        var query = vendorQuery.Join(vendorPersonalQuery, a => a.Id, b => b.ApplicationId, (a, b) => new { vendorQuery = a, vendorPersonalQuery = b })
                        .Join(queryUser, a => a.vendorQuery.ApplyUserId, a => a.Id, (a, b) => new { a.vendorQuery, a.vendorPersonalQuery, User = b })
                        .Join(queryWfTask, a => a.vendorQuery.Id, a => a.FormId, (a, b) => new { a.vendorQuery, a.vendorPersonalQuery, a.User, Task = b })
                        .WhereIf(request.ApplicationType.HasValue, w => w.vendorQuery.ApplicationType == request.ApplicationType.Value)
                        .WhereIf(request.VendorType.HasValue, p => p.vendorQuery.VendorType == request.VendorType)
                        .WhereIf(!request.VendorType.HasValue, a => a.vendorQuery.VendorType != VendorTypes.HCPPerson)
                        .WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), p => p.vendorQuery.ApplicationCode == request.ApplicationCode)
                        .WhereIf(!string.IsNullOrEmpty(request.SPName), p => p.vendorPersonalQuery.SPName.Contains(request.SPName))
                        .WhereIf(request.SubmissionStartTime.HasValue, p => p.vendorQuery.ApplyTime >= request.SubmissionStartTime)
                        .WhereIf(request.SubmissionEndTime.HasValue, p => p.vendorQuery.ApplyTime <= request.SubmissionEndTime)
                        .WhereIf(request.ApprovalStatus.HasValue, p => p.vendorQuery.Status == request.ApprovalStatus)
                        .Select(g => new
                        {
                            Id = g.vendorQuery.Id,
                            VendorType = g.vendorQuery.VendorType,
                            ApplicationCode = g.vendorQuery.ApplicationCode,
                            SPName = g.vendorPersonalQuery.SPName,
                            ApplyUserId = g.vendorQuery.ApplyUserId,
                            ApplyUserName = g.User.Name,
                            ApplyUserBu = g.vendorQuery.ApplyUserBu,
                            g.vendorQuery.ApplyUserBuName,
                            ApprovalStatus = g.vendorQuery.Status,
                            ApplyTime = g.vendorQuery.ApplyTime,
                            ApprovalStep = g.Task.StepNo,
                            CreatedTime = g.Task.CreationTime,
                            ApprovalTime = g.Task.ApprovalTime,
                            ApplicationType = g.vendorQuery.ApplicationType
                        })
                        .Union(
                            vendorQuery.Join(vendorApplicationOrgQuery, a => a.Id, b => b.ApplicationId, (a, b) => new { vendorQuery = a, vendorOrgQuery = b })
                            .Join(queryUser, a => a.vendorQuery.ApplyUserId, a => a.Id, (a, b) => new { a.vendorQuery, a.vendorOrgQuery, User = b })
                            .Join(queryWfTask, a => a.vendorQuery.Id, a => a.FormId, (a, b) => new { a.vendorQuery, a.vendorOrgQuery, a.User, Task = b })
                           .WhereIf(request.ApplicationType.HasValue, w => w.vendorQuery.ApplicationType == request.ApplicationType.Value)
                           .WhereIf(request.VendorType.HasValue, p => p.vendorQuery.VendorType == request.VendorType)
                           .WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), p => p.vendorQuery.ApplicationCode == request.ApplicationCode)
                           .WhereIf(!string.IsNullOrEmpty(request.SPName), p => p.vendorOrgQuery.VendorName.Contains(request.SPName))
                           .WhereIf(request.SubmissionStartTime.HasValue, p => p.vendorQuery.ApplyTime >= request.SubmissionStartTime)
                           .WhereIf(request.SubmissionEndTime.HasValue, p => p.vendorQuery.ApplyTime <= request.SubmissionEndTime)
                           .WhereIf(request.ApprovalStatus.HasValue, p => p.vendorQuery.Status == request.ApprovalStatus)
                           .Select(g => new
                           {
                               Id = g.vendorQuery.Id,
                               VendorType = g.vendorQuery.VendorType,
                               ApplicationCode = g.vendorQuery.ApplicationCode,
                               SPName = g.vendorOrgQuery.VendorName,
                               ApplyUserId = g.vendorQuery.ApplyUserId,
                               ApplyUserName = g.User.Name,
                               ApplyUserBu = g.vendorQuery.ApplyUserBu,
                               g.vendorQuery.ApplyUserBuName,
                               ApprovalStatus = g.vendorQuery.Status,
                               ApplyTime = g.vendorQuery.ApplyTime,
                               ApprovalStep = g.Task.StepNo,
                               CreatedTime = g.Task.CreationTime,
                               ApprovalTime = g.Task.ApprovalTime,
                               ApplicationType = g.vendorQuery.ApplicationType
                           })
                        )
                        .Select(g => new SpeakerTaskCenterListResponseDto
                        {
                            Id = g.Id,
                            VendorType = g.VendorType,
                            ApplicationCode = g.ApplicationCode,
                            SPName = g.SPName,
                            ApplyUserId = g.ApplyUserId,
                            ApplyUserName = g.ApplyUserName,
                            ApplyUserBu = g.ApplyUserBu,
                            ApplyUserBuName = g.ApplyUserBuName,
                            ApprovalStatus = g.ApprovalStatus,
                            ApplyTime = g.ApplyTime,
                            ApprovalStep = g.ApprovalStep,
                            CreatedTime = g.CreatedTime,
                            ApprovalTime = g.ApprovalTime,
                            ApplicationType = g.ApplicationType
                        });

                        responseDtosCount = query.Count();
                        if (request.IsAsc)
                            pageResult = query.OrderBy(a => a.ApprovalTime).Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToList();
                        else
                            pageResult = query.OrderByDescending(a => a.ApprovalTime).Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToList();

                        pageResult.ForEach(a =>
                        {
                            a.VendorTypeName = a.VendorType.HasValue ? vendorTypes[a.VendorType.Value] : null;
                            a.ApprovalStatusString = a.ApprovalStatus.HasValue ? status[a.ApprovalStatus.Value] : null;
                        });
                    }
                }

                var result = new PagedResultDto<SpeakerTaskCenterListResponseDto>()
                {
                    Items = pageResult,
                    TotalCount = responseDtosCount,
                };
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError($"SpeakerService's GetApprovalSpeakerListAsync has an error : {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 填写财务信息
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> SaveSpeakerFinancialAsync(SaveSpeakerFinancialRequestDto request)
        {
            if (request.FinancialInformation.Any(a => a.VendorCode.Length > 5)) return MessageResult.FailureResult("供应商编码不得超过5位");

            var vendorApplicationFinancial = _serviceProvider.GetService<IVendorApplicationFinancialRepository>();

            //获取供应商财务信息表
            var financeDetailList = await vendorApplicationFinancial.GetListAsync(f => f.ApplicationId == request.ID);
            var both = financeDetailList.Select(s => s.Id).ToList().SequenceEqual(request.FinancialInformation.Select(s => s.ID.Value).ToList());
            if (!both) return MessageResult.FailureResult("与原有数据不匹配");

            //修改相应供应商财务信息表
            foreach (var item in financeDetailList)
            {
                var entity = request.FinancialInformation.FirstOrDefault(w => w.ID == item.Id);
                if (entity != null)
                {
                    ObjectMapper.Map(entity, item);
                    //item.PaymentTerm = "30";
                }
            }
            await vendorApplicationFinancial.UpdateManyAsync(financeDetailList, true);
            return MessageResult.SuccessResult(request.ID);
        }

        /// <summary>
        /// 填写DPS信息
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> SaveSpeakerDPSAsync(SaveSpeakerDPSRequestDto request)
        {
            var vendorApplication = _serviceProvider.GetService<IVendorApplicationRepository>();
            //获取供应商财务信息表
            var vendorDetail = await vendorApplication.FirstOrDefaultAsync(f => f.Id == request.ID);
            if (vendorDetail == null) return MessageResult.FailureResult("未找到相关数据");

            //修改相应供APS信息表
            vendorDetail.DPSCheck = request.AttachmentInformation.Count > 0 ? string.Join(",", request.AttachmentInformation.Select(s => s.AttachmentId).ToList()) : "";

            await vendorApplication.UpdateAsync(vendorDetail, true);
            return MessageResult.SuccessResult(request.ID);
        }


        /// <summary>
        /// 获取讲者变更详情
        /// </summary>
        /// <param name="applicationId"></param>
        /// <returns></returns>
        public async Task<MessageResult> GetSpeakerChangedDetailAsync(Guid request)
        {

            var vendorQuery = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();
            var vendorPersonalQuery = await LazyServiceProvider.LazyGetService<IVendorPersonalRepository>().GetQueryableAsync();
            var vendorFinanceQuery = await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync();
            var attachmentQuery = await LazyServiceProvider.LazyGetService<IAttachmentRepository>().GetQueryableAsync();
            var commonService = LazyServiceProvider.LazyGetService<ICommonService>();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var getAllProvince = await dataverseService.GetAllProvince();
            var getAllCity = await dataverseService.GetAllCity();
            var gender = EnumUtil.GetEnumIdValues<Gender>().ToDictionary(x => x.Key, x => x.Value);
            var jobTitles = await _dataverseService.GetAllJobTiles();
            var hospitals = await _dataverseService.GetAllHospitals();
            var standardHosDeps = await _dataverseService.GetAllDepartments();
            var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");

            //获取草稿详情数据
            var draftDetail = await GetSpeakerDraftDetailAsync(request);
            if (!draftDetail.Success)
                return MessageResult.FailureResult(draftDetail.Message);
            var draftData = draftDetail.Data;
            var applicationDraft = draftData as SpeakerDraftDetailResponseDto;
            var vendorCode = applicationDraft.VendorCode;

            //基本信息映射
            var applyBaseData = ObjectMapper.Map<SpeakerDraftDetailResponseDto, SpeakerChangedDetailResponseDto>(applicationDraft);
            applyBaseData.IsDraft = true;
            applyBaseData.ID = applicationDraft.ID;

            //修改信息映射
            var applyData = ObjectMapper.Map<SpeakerDraftDetailResponseDto, SpeakerChangedDetail>(applicationDraft);
            applyData.IsDraft = true;
            applyData.BankName = applicationDraft.BankCode;
            //查询正式库数据
            var query = vendorQuery.Where(x => x.VendorCode == vendorCode)// && x.VendorType == VendorTypes.HCPPerson
                .Join(vendorPersonalQuery, v => v.Id, p => p.VendorId, (a, b) => new { vendorQuery = a, vendorPersonalQuery = b });
            var vendorntity = query.FirstOrDefault();
            if (vendorntity == null)
                return MessageResult.FailureResult("获取供应商信息失败");

            //获取正式信息
            var companyList = await _dataverseService.GetCompanyList();
            var companyCurrency = await _dataverseService.GetCompanyCurrencyList();
            var vendorAppQuery = await LazyServiceProvider.LazyGetService<IVendorApplicationRepository>().GetQueryableAsync();
            var vendorOldData = JsonConvert.DeserializeObject<SpeakerDetailResponseDto>(vendorAppQuery.First(f => f.Id == request).UpdatePreJson);
            vendorOldData.IsDraft = false;
            vendorOldData.FinancialInformation.ForEach(f =>
            {
                f.CompanyName = companyList.FirstOrDefault(w => w.CompanyCode == f.Company)?.CompanyName;
                f.CurrencyName = companyCurrency.FirstOrDefault(w => w.Code == f.Currency)?.Name;
            });
            var vendorData = ObjectMapper.Map<SpeakerDetailResponseDto, SpeakerChangedDetail>(vendorOldData);
            //解密
            if (!string.IsNullOrEmpty(vendorData.CardNo))
                vendorData.CardNo = AesHelper.Decryption(vendorData.CardNo, insightKey);

            vendorData.BankName = vendorOldData.BankCode;
            if (!string.IsNullOrEmpty(vendorData.BankCardNo))
                vendorData.BankCardNo = AesHelper.Decryption(vendorData.BankCardNo, insightKey);

            //排序前端验证需要使用
            vendorData.FinancialInformation = [.. vendorData.FinancialInformation.OrderBy(o => o.Company)];
            applyData.FinancialInformation = [.. applyData.FinancialInformation.OrderBy(o => o.Company)];
            //添加财务是否可修改的flag    false为可修改/true为不可修改
            vendorData.FinancialInformation.ForEach(f => f.Flag = true);
            applyData.FinancialInformation.ForEach(f => f.Flag = true);
            applyBaseData.FinancialInformation.ForEach(f =>
            {
                f.Flag = vendorData.FinancialInformation.Select(s => s.Company).Contains(f.Company);
            });

            //检查变更内容，是否触发DPScheck
            await GetChangeContentDPSCheck(applyBaseData);

            applyBaseData.SpeakerDetailChangedCompare = [vendorData, applyData];
            applyBaseData.BankName = applyBaseData.BankCode;
            return MessageResult.SuccessResult(applyBaseData);
        }

        /// <summary>
        /// 获取DPSCheck是否需要审批
        /// </summary>
        /// <param name="request"></param>
        /// <param name="vendorQuery"></param>
        /// <param name="vendorPersonalQuery"></param>
        /// <param name="vendorFinanceQuery"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        private async Task GetChangeContentDPSCheck(SpeakerChangedDetailResponseDto request)
        {
            var vendor = _serviceProvider.GetService<IVendorRepository>();
            var vendorPersonal = _serviceProvider.GetService<IVendorPersonalRepository>();
            var vendorFinancial = _serviceProvider.GetService<IVendorFinancialRepository>();
            var isDPSCheck = false;
            var vendorQuery = await vendor.GetQueryableAsync();
            var vendorPersonalQuery = await vendorPersonal.GetQueryableAsync();
            //供应商信息
            var vendorData = vendorQuery.Join(vendorPersonalQuery, a => a.Id, b => b.VendorId, (a, b) => new { vendorData = a, vendorPersonalData = b })
                .FirstOrDefault(f => f.vendorData.VendorCode == request.VendorCode);
            //财务信息
            var vendorFinancialData = await vendorFinancial.GetListAsync(g => g.VendorId == vendorData.vendorData.Id);

            //匹配地址信息
            if (request.ProvinceCity[0] != vendorData.vendorPersonalData.Province || request.ProvinceCity[1] != vendorData.vendorPersonalData.City ||
                request.Address != vendorData.vendorPersonalData.Address || request.PostCode != vendorData.vendorPersonalData.PostCode)
            {
                isDPSCheck = true;
            }
            //匹配公司信息
            if (request.FinancialInformation.Count != vendorFinancialData.Count)
            {
                isDPSCheck = true;
            }
            if (request.SPName != vendorData.vendorPersonalData.SPName)
            {
                isDPSCheck = true;
            }
            request.IncludeDPSCheck = isDPSCheck;
        }


        /// <summary>
        /// 审批时验证财务信息中的供应商编码
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> GetVonderCodeValidateListAsync(FinancialInformation request)
        {
            try
            {
                var queryVendorApp = await _serviceProvider.GetService<IVendorApplicationRepository>().GetQueryableAsync();
                var queryVendorAppFinancial = await _serviceProvider.GetService<IVendorApplicationFinancialRepository>().GetQueryableAsync();
                var queryVendorAppPersonal = await _serviceProvider.GetService<IVendorApplicationPersonalRepository>().GetQueryableAsync();
                var queryVendorAppOrg = await _serviceProvider.GetService<IVendorApplicationOrganizationRepository>().GetQueryableAsync();

                var vendor = await _serviceProvider.GetService<IVendorRepository>().GetQueryableAsync();
                var vendorFinancial = await _serviceProvider.GetService<IVendorFinancialRepository>().GetQueryableAsync();
                var vendorPersonal = await LazyServiceProvider.LazyGetService<IVendorPersonalRepository>().GetQueryableAsync();
                var vendorOrganization = await LazyServiceProvider.GetService<IVendorOrgnizationRepository>().GetQueryableAsync();

                //查询BPCS中是否已经建立如下供应商账号
                var bpcsAvmQuery = await LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync();
                var bpcsPmfvmQuery = await LazyServiceProvider.LazyGetService<IBpcsPmfvmRepository>().GetQueryableAsync();

                var existVendor = bpcsAvmQuery.Where(w => w.Vendor.ToString() == request.VendorCode && w.Vcmpny.ToString() == request.Company)
                    .Join(bpcsPmfvmQuery, a => new { Company = a.Vcmpny, VendorCode = a.Vendor }, a => new { Company = a.Vmcmpy, VendorCode = a.Vnderx }, (a, b) => new { Avm = a, Pmfvm = b })
                    .Select(s => new VonderCodeValidateListResponseDto
                    {
                        VendorName = s.Avm.Vndnam,
                        VendorCode = s.Avm.Vendor.ToString(),
                        Address = s.Avm.Vndad1,
                        Contacts = s.Avm.Vcon,
                        Phone = s.Avm.Vphone,
                        CardNumber = s.Avm.Vmxcrt,
                        BankNumber = s.Pmfvm.Vldrm2
                    }).ToList();
                if (existVendor.Count > 0) return MessageResult.SuccessResult(existVendor);

                #region 查询ABP中是否已经建立如下供应商账号

                //正式档案
                var queryOfficial = vendorFinancial
                    .Join(vendor, a => a.VendorId, b => b.Id, (a, b) => new { VendorFinancial = a, Vendor = b })
                    .Where(w => w.VendorFinancial.VendorCode == request.VendorCode && w.VendorFinancial.Company == request.Company);

                //申请档案
                IEnumerable<Statuses> statuses = [Statuses.Approving, Statuses.Returned, Statuses.Withdraw];
                var queryApplication = queryVendorAppFinancial
                    .Join(queryVendorApp.Where(a => statuses.Contains(a.Status)), a => a.ApplicationId, a => a.Id, (a, b) => new { VendorFinancial = a, Vendor = b })
                    .Where(a => a.VendorFinancial.VendorCode == request.VendorCode && a.VendorFinancial.Company == request.Company);

                var existABPVendor = queryOfficial.Join(vendorPersonal, a => a.Vendor.Id, a => a.VendorId, (a, b) => new { a.Vendor, a.VendorFinancial, Personal = b })
                .Select(s => new VonderCodeValidateListResponseDto
                {
                    VendorId = s.Vendor.Id,
                    VendorType = s.Vendor.VendorType,
                    VendorName = s.Personal.SPName,
                    VendorCode = s.Vendor.VendorCode,
                    Address = s.Personal.Address,
                    Contacts = "",
                    Phone = s.Vendor.HandPhone,
                    CardNumber = s.Personal.CardNo,
                    BankNumber = s.Vendor.BankCardNo
                })
                .Concat(queryOfficial.Join(vendorOrganization, a => a.Vendor.Id, a => a.VendorId, (a, b) => new { a.Vendor, a.VendorFinancial, Org = b })
                .Select(s => new VonderCodeValidateListResponseDto
                {
                    VendorId = s.Vendor.Id,
                    VendorType = s.Vendor.VendorType,
                    VendorName = s.Org.VendorName,
                    VendorCode = s.Vendor.VendorCode,
                    Address = s.Org.RegCertificateAddress,
                    Contacts = s.Org.ContactName,
                    Phone = s.Org.ContactPhone,
                    CardNumber = "",
                    BankNumber = s.Vendor.BankCardNo
                }))
                .Concat(queryApplication.Join(queryVendorAppPersonal, a => a.Vendor.Id, a => a.ApplicationId, (a, b) => new { a.Vendor, a.VendorFinancial, Personal = b })
                .Select(s => new VonderCodeValidateListResponseDto
                {
                    VendorId = s.Vendor.Id,
                    VendorType = s.Vendor.VendorType,
                    VendorName = s.Personal.SPName,
                    VendorCode = s.Vendor.VendorCode,
                    Address = s.Personal.Address,
                    Contacts = "",
                    Phone = s.Vendor.HandPhone,
                    CardNumber = s.Personal.CardNo,
                    BankNumber = s.Vendor.BankCardNo
                }))
                .Concat(queryApplication.Join(queryVendorAppOrg, a => a.Vendor.Id, a => a.ApplicationId, (a, b) => new { a.Vendor, a.VendorFinancial, Org = b })
                .Select(s => new VonderCodeValidateListResponseDto
                {
                    VendorId = s.Vendor.Id,
                    VendorType = s.Vendor.VendorType,
                    VendorName = s.Org.VendorName,
                    VendorCode = s.Vendor.VendorCode,
                    Address = s.Org.RegCertificateAddress,
                    Contacts = s.Org.ContactName,
                    Phone = s.Org.ContactPhone,
                    CardNumber = "",
                    BankNumber = s.Vendor.BankCardNo
                }))
                .ToArray();

                #endregion

                if (existABPVendor.Any())
                {
                    var insightKey = this._configuration.GetValue<string>("ApplicationInsightKey");
                    foreach (var item in existABPVendor)
                    {
                        item.BankNumber = AesHelper.Decryption(item.BankNumber, insightKey);
                        item.CardNumber = AesHelper.Decryption(item.CardNumber, insightKey);
                    }

                    existABPVendor = existABPVendor.GroupBy(a => new { a.VendorCode, a.VendorName, a.Phone, a.CardNumber, a.BankNumber, a.Contacts, a.Address })
                    .Select(a => new VonderCodeValidateListResponseDto
                    {
                        VendorCode = a.Key.VendorCode,
                        VendorName = a.Key.VendorName,
                        Phone = a.Key.Phone,
                        CardNumber = a.Key.CardNumber,
                        BankNumber = a.Key.BankNumber,
                        Contacts = a.Key.Contacts,
                        Address = a.Key.Address
                    })
                    .ToArray();

                    return MessageResult.SuccessResult(existABPVendor);
                }

                return MessageResult.SuccessResult();
            }
            catch (Exception ex)
            {
                _logger.LogError($"SpeakerService's GetVonderCodeValidateListAsync has an error : {ex.Message}");
                return MessageResult.FailureResult();
            }
        }

        /// <summary>
        /// 财务基本信息获取
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> GetFinancialInformationListAsync(SpeakerVonderCodeValidateRequestDto request)
        {
            try
            {
                var bpcsAbkQuery = await LazyServiceProvider.LazyGetService<IBpcsAbkRepository>().GetQueryableAsync();
                //var bpcsAtyQuery = await LazyServiceProvider.LazyGetService<IBpcsAtyRepository>().GetQueryableAsync();
                var bpcsZccQuery = await LazyServiceProvider.LazyGetService<IBpcsZccRepository>().GetQueryableAsync();
                var bpcsZpaQuery = await LazyServiceProvider.LazyGetService<IBpcsZpaRepository>().GetQueryableAsync();
                //var bpcsGsvQuery = await LazyServiceProvider.LazyGetService<IBpcsGsvRepository>().GetQueryableAsync();
                var paymentTermRepository = await LazyServiceProvider.LazyGetService<IBpcsAvtRepository>().GetQueryableAsync();
                var paymentTerms = paymentTermRepository.Where(w => w.Vcmpy.ToString() == request.CompanyCode).GroupBy(a => a.Vterm)
                    .Select(g => new DictionaryDto
                    {
                        Code = g.Key,
                        Name = g.FirstOrDefault().Vterm,
                    }).ToList();

                var buCodingCfgs = await _dataverseService.GetBuCodingCfgAsync();
                var buCodingCode = buCodingCfgs.Select(s => s.BuCode).Distinct().ToList();
                if (buCodingCode.FirstOrDefault(w => w == "60") == null)
                    buCodingCode.Add("60");
                var PayTypeList = bpcsZpaQuery.Where(w => w.Pcmpy.ToString() == request.CompanyCode).Select(s => s.Pkey.ToUpper().Replace("PAYTYPE", "")).Distinct().ToList();
                if (PayTypeList.FirstOrDefault(w => w == "T") == null)
                    PayTypeList.Add("T");
                var CountryCodeList = bpcsZccQuery.Where(w => w.Cccmpy.ToString() == request.CompanyCode && w.Cctabl.ToLower() == "country").Select(s => s.Cccode).Distinct().ToList();
                if (CountryCodeList.FirstOrDefault(w => w == "0000") == null)
                    CountryCodeList.Add("0000");

                var vendorTypeCfgs = await _dataverseService.GetVendorTypeCfgAsync();
                var companyListCfgs = await _dataverseService.GetCompanyList();
                //银行拼接
                var company = companyListCfgs.FirstOrDefault(w => w.CompanyCode == request.CompanyCode);
                Dictionary<string, string> AbbottBankList = new Dictionary<string, string>();
                if (company != null)
                {
                    AbbottBankList.Add("RMB", company.Bankcodermb);
                    AbbottBankList.Add("NONERMB", company.Bankcodenonrmb);
                }
                //城市处理
                if (request.ProvinceCity == null || request.ProvinceCity.Length != 2)
                    return MessageResult.FailureResult("请填写完整省市数据");
                var ciytCode = request.ProvinceCity[1];
                var BankType = VendorBankTypes.DifferentLocations;
                if (request.CompanyCode != "79" && ciytCode == "310000" || request.CompanyCode == "79" && ciytCode == "330400")
                {
                    BankType = VendorBankTypes.SameLocations;
                }

                var AbbottBank = "";
                if (request.Currency.Equals("RMB", StringComparison.CurrentCultureIgnoreCase))
                {
                    AbbottBankList.TryGetValue("RMB", out AbbottBank);
                }
                else
                {
                    AbbottBankList.TryGetValue("NONERMB", out AbbottBank);
                }
                var entity = new SpeakerVonderCodeValidateResponseDto
                {
                    Company = request.CompanyCode,
                    Currency = request.Currency,
                    VendorCode = request.VendorCode,
                    //PaymentTermList = paymentTerms.Select(s => s.Name).Distinct().ToArray(),
                    AbbottBankList = AbbottBankList.Select(s => s.Value).Distinct().ToArray(),
                    AbbottBank = AbbottBank,//bpcsAbkQuery.FirstOrDefault(f => f.Bkcmpy.ToString() == request.CompanyCode && f.Bcurr == request.Currency)?.Bank,
                                            //VendorTypeList = bpcsAbkQuery.Where(f => f.Vtcmpy.ToString() == request.CompanyCode).Select(s => s.Vtype).ToArray(),
                    VendorType = request.Currency.Equals("RMB", StringComparison.CurrentCultureIgnoreCase) ? vendorTypeCfgs.FirstOrDefault(f => f.VendorType == ((int)request.VendorType).ToString())?.BPCSTypeRMB : vendorTypeCfgs.FirstOrDefault(f => f.VendorType == ((int)request.VendorType).ToString())?.BPCSTypeNoneRMB,
                    DivisionList = buCodingCode.ToArray(),
                    Division = "60",
                    PayTypeList = PayTypeList.ToArray(),
                    PayType = "T",
                    CountryCodeList = CountryCodeList.ToArray(),
                    CountryCode = "0000",
                    BankTypeList = ["51", "52"],
                    BankType = ((int)BankType).ToString()
                };
                return MessageResult.SuccessResult(entity);
            }
            catch (Exception ex)
            {
                _logger.LogError($"SpeakerService's GetFinancialInformationListAsync has an error : {ex.Message}");
                return MessageResult.FailureResult();
            }
        }

        #endregion


        #region 供应商查询
        /// <summary>
        /// 获取供应商列表
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<PagedResultDto<SupplierListResponseDto>> GetSupplierListAsync(SupplierListRequestDto request)
        {
            try
            {
                //查询BPCS中已经建立如下供应商账号
                var bpcsAvmQuery = (await LazyServiceProvider.LazyGetService<IBpcsAvmReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
                var bpcsPmfvmQuery = (await LazyServiceProvider.LazyGetService<IBpcsPmfvmReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
                var companyList = await _dataverseService.GetCompanyList(stateCode: null);
                var vendorFinancialQuery = (await LazyServiceProvider.LazyGetService<IVendorFinancialReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
                var vendorQuery = (await LazyServiceProvider.LazyGetService<IVendorReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
                var vendorTypes = EnumUtil.GetEnumIdValues<VendorTypes>().ToDictionary(a => (VendorTypes)a.Key, a => a.Value);
                var companycode = companyList.FirstOrDefault(c => c.Id == request.Company)?.CompanyCode;
                var results = bpcsAvmQuery
                    .Join(bpcsPmfvmQuery, a => new { VendorNum = a.Vendor, Vmcmpy = a.Vcmpny }, b => new { VendorNum = b.Vnderx, Vmcmpy = b.Vmcmpy },
                    (a, b) => new { bpcsAvmQuery = a, bpcsPmfvmQuery = b })
                    .WhereIf(!string.IsNullOrWhiteSpace(companycode), w => w.bpcsAvmQuery.Vcmpny.ToString() == companycode)
                    .WhereIf(!string.IsNullOrEmpty(request.VendorNum), w => w.bpcsAvmQuery.Vendor.ToString().Contains(request.VendorNum))
                    .WhereIf(!string.IsNullOrEmpty(request.VendorName), w => w.bpcsAvmQuery.Vndnam.Contains(request.VendorName))
                    .WhereIf(!string.IsNullOrEmpty(request.VendorFullName), w => w.bpcsPmfvmQuery.Vextnm.Contains(request.VendorFullName))
                    .WhereIf(!string.IsNullOrEmpty(request.VendorType), w => w.bpcsAvmQuery.Vtype.Contains(request.VendorType))
                    .WhereIf(request.VendorExternalStatus.HasValue, w => w.bpcsAvmQuery.Vnstat == (request.VendorExternalStatus == VendorBPCSStatus.Effective ? "A" : "D"))
                    .Select(s => new SupplierListResponseDto
                    {
                        Company = s.bpcsAvmQuery.Vcmpny.ToString(),
                        VendorNum = s.bpcsAvmQuery.Vendor.ToString(),
                        Vnstat = s.bpcsAvmQuery.Vnstat,
                        //VendorExternalStatus = s.bpcsAvmQuery.Vnstat.ToUpper() == "A" ? "有效" : "失效",
                        VendorName = s.bpcsAvmQuery.Vndnam,
                        VendorFullName = s.bpcsPmfvmQuery.Vextnm,//bpcsPmfvmQuery.First(f=>f.Vnderx == s.Vendor && f.Vmcmpy == s.Vcmpny).Vextnm,
                        VendorType = s.bpcsAvmQuery.Vtype,
                        Currency = s.bpcsAvmQuery.Vcurr,
                        PaymentTerms = s.bpcsAvmQuery.Vterms,
                        Vcrdte = s.bpcsPmfvmQuery.Vcrdte,
                        Vctime = s.bpcsPmfvmQuery.Vctime,
                        //StorageDateString = s.bpcsPmfvmQuery.Vcrdte.ToString() + "+" + s.bpcsPmfvmQuery.Vctime.ToString(),//bpcsPmfvmQuery.First(f => f.Vnderx == s.Vendor && f.Vmcmpy == s.Vcmpny).Vcrdte.ToString() + "+" + bpcsPmfvmQuery.First(f => f.Vnderx == s.Vendor && f.Vmcmpy == s.Vcmpny).Vctime.ToString(),
                        StorageDateString2 = s.bpcsPmfvmQuery.Vcrdte.ToString() + s.bpcsPmfvmQuery.Vctime.ToString(),
                        IdCardNo = s.bpcsAvmQuery.Vmxcrt,
                        BankName = s.bpcsPmfvmQuery.Vldrm1,
                        BankCode = s.bpcsPmfvmQuery.Vldrm2,
                        Contacts = s.bpcsAvmQuery.Vcon,
                        PhoneNumber = s.bpcsAvmQuery.Vphone,
                        //VendorCode = "",
                        //VendorId = Guid.Empty
                    }).OrderByDescending(o => o.StorageDateString2).AsQueryable();//.ThenByDescending(o => o.Vctime)

                var resultCount = await results.CountAsync();
                //这里分页减少数据量查询
                var result = await results.PagingIf(request).ToListAsync();

                var companys = result.Select(s => s.Company).ToList();
                var vendorNums = result.Select(s => s.VendorNum).ToList();

                var vendorFinancials = await vendorFinancialQuery
                 .Join(vendorQuery, a => a.VendorId, b => b.Id, (a, b) => new
                 {
                     b.VendorCode,
                     b.Id,
                     b.VendorType,
                     a.Company,
                     FVendorCode = a.VendorCode,
                 })
                 .WhereIf(companys.Count > 0, w => companys.Contains(w.Company))
                 .WhereIf(vendorNums.Count > 0, w => vendorNums.Contains(w.FVendorCode)).TakeIf(result.Count == 0, 1).ToListAsync();
                //if (result.Count == 0)
                //{
                //  var  vendorFinancials
                //}
                // 转换内容
                foreach (var item in result)
                {
                    //时间转换
                    //item.StorageDateString = item.StorageDateString.Split("+")[0] + item.StorageDateString.Split("+")[1].PadLeft(6, '0');
                    if (!string.IsNullOrEmpty(item.StorageDateString) && item.StorageDateString.Length == 14)
                    {
                        item.StorageDate = DateTime.ParseExact(item.StorageDateString, "yyyyMMddHHmmss", null);
                    }
                    //获取abp供应商关联
                    //var vendorFinancial = vendorFinancialQuery
                    //    .Join(vendorQuery, a => a.VendorId, b => b.Id, (a, b) => new { vendorFinancialQuery = a, vendorQuery = b })
                    //    .FirstOrDefault(w => w.vendorFinancialQuery.Company == item.Company && w.vendorFinancialQuery.VendorCode == item.VendorNum);
                    var vendorFinancial = vendorFinancials.FirstOrDefault(w => w.Company == item.Company && w.FVendorCode == item.VendorNum);
                    if (vendorFinancial != null)
                    {
                        //abp财务表中存在，供应商类型使用ABP类型，关联ABP供应商编号
                        item.VendorCode = vendorFinancial.VendorCode;
                        item.VendorId = vendorFinancial.Id;
                        item.VendorTypeABP = vendorFinancial.VendorType;
                    }
                    // 公司名称
                    if (!string.IsNullOrEmpty(item.Company))
                        item.Company = companyList.FirstOrDefault(f => f.CompanyCode == item.Company).CompanyName;
                }

                return new PagedResultDto<SupplierListResponseDto>()
                {
                    Items = result,
                    TotalCount = resultCount,
                };
            }
            catch (Exception ex)
            {
                _logger.LogError($"SpeakerService's GetSpeakerListAsync has an error : {ex.Message}");
                return null;
            }
        }

        public async Task<MessageResult> VarifyVendorDelete(IEnumerable<VendorDeleteTempleteDto> rows)
        {
            var queryBpcsAvm = await LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync();
            //获取供应商全称
            var queryBpcsPmfvm = await LazyServiceProvider.LazyGetService<IBpcsPmfvmRepository>().GetQueryableAsync();

            var queryVendorFinancial = await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync();
            var queryVendor = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();

            var queryVendorApply = await LazyServiceProvider.LazyGetService<IVendorApplicationRepository>().GetQueryableAsync();
            var queryVendorApplyFinancial = await LazyServiceProvider.LazyGetService<IVendorApplicationFinancialRepository>().GetQueryableAsync();

            var queryPR = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var queryPRDetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();

            var comCodes = rows.Where(x => !string.IsNullOrEmpty(x.ComCode)).Select(x => x.ComCode).Distinct();
            var vendorCodes = rows.Where(x => !string.IsNullOrEmpty(x.VendorCode)).Select(x => x.VendorCode).Distinct();

            //Bpcs供应商
            var vendorBpcs = queryBpcsAvm.Where(x => comCodes.Contains(x.Vcmpny.ToString()) && vendorCodes.Contains(x.Vendor.ToString()))
                .Join(queryBpcsPmfvm, a => new { VendorCode = a.Vendor, comCode = a.Vcmpny },
                b => new { VendorCode = b.Vnderx, comCode = b.Vmcmpy }, (a, b) => new { bpcsAvm = a, bpcsFvm = b })
                .Select(x => new { x.bpcsAvm.Vcmpny, x.bpcsAvm.Vendor, x.bpcsAvm.Vmid, x.bpcsAvm.Vnstat, x.bpcsFvm.Vextnm }).ToArray();

            //供应商：是否为待生效、待激活
            var vendorEntities = queryVendorFinancial.Where(x => comCodes.Contains(x.Company) && vendorCodes.Contains(x.VendorCode))
                .Join(queryVendor, a => a.VendorId, b => b.Id, (a, b) => new { financial = a, vendor = b })
                .Select(x => new { x.financial.Company, x.financial.VendorCode, x.vendor.Status, VendorNo = x.vendor.VendorCode }).ToArray();

            //供应商申请：是否为审批中
            var vendorApplies = queryVendorApplyFinancial.Where(x => !string.IsNullOrEmpty(x.Company) && !string.IsNullOrEmpty(x.VendorCode) &&
                comCodes.Contains(x.Company) && vendorCodes.Contains(x.VendorCode))
                .Join(queryVendorApply, a => a.ApplicationId, b => b.Id, (a, b) => new { financial = a, vendor = b })
                .Where(x => x.vendor.Status == Statuses.Approving)
                .Select(x => new { x.financial.Company, x.financial.VendorCode, x.vendor.Status, VendorNo = x.vendor.VendorCode }).ToArray();

            //采购申请：是否为订单关闭/发起人终止/草稿
            var prEntities = queryPRDetail.Where(x => !string.IsNullOrEmpty(x.VendorCode) && vendorCodes.Contains(x.VendorCode))
                .Join(queryPR, a => a.PRApplicationId, b => b.Id, (a, b) => new { detail = a, pr = b })
                .Select(x => new { x.pr.CompanyCode, x.detail.VendorCode, x.pr.Status, x.pr.ApplicationCode }).ToArray();

            List<VendorStatus> vendorStatusPendding = [VendorStatus.ToBeEffective, VendorStatus.ToBeActivated];
            List<Enums.Purchase.PurPRApplicationStatus> prStatusUnfinished = [Enums.Purchase.PurPRApplicationStatus.Draft,
                Enums.Purchase.PurPRApplicationStatus.ApplicantTerminate, Enums.Purchase.PurPRApplicationStatus.Closed];

            List<VendorDeleteImportResponseDto> success = [];
            List<VendorDeleteImportResponseDto> error = [];

            var rowNo = 6;
            foreach (var row in rows)
            {
                rowNo++;
                var errorMsg = string.Empty;
                if (string.IsNullOrEmpty(row.ComCode)) { errorMsg += "公司编号必填；"; }
                if (string.IsNullOrEmpty(row.VendorCode)) { errorMsg += "供应商编号必填；"; }

                var dto = new VendorDeleteImportResponseDto { No = rowNo };

                var vendorInfo = vendorBpcs.FirstOrDefault(x => x.Vcmpny.ToString() == row.ComCode && x.Vendor.ToString() == row.VendorCode);
                if (vendorInfo == null)
                {
                    errorMsg += "供应商不存在；";
                    //dto.No = error.Count + 1;
                    dto.ErrorMsg = errorMsg;
                    error.Add(dto);
                    continue;
                }

                dto.ComCode = vendorInfo.Vcmpny.ToString();
                dto.VendorCode = vendorInfo.Vendor.ToString();
                dto.VendorName = vendorInfo.Vextnm;
                //s.bpcsAvmQuery.Vnstat.ToUpper() == "A" ? "有效" : "失效",
                if (!string.IsNullOrEmpty(vendorInfo.Vnstat) && vendorInfo.Vnstat.ToUpper() == "A")
                {
                    dto.VendorStatus = "有效";
                    errorMsg += "供应商非失效状态，无法删除；";
                }
                else
                    dto.VendorStatus = "失效";

                var vendor = vendorEntities?.FirstOrDefault(x => x.Company == row.ComCode && x.VendorCode == row.VendorCode);
                if (vendor != null)
                {
                    dto.LinkDocNo = vendor.VendorNo;
                    if (vendor.Status == VendorStatus.ToBeEffective) { errorMsg += "供应商关联档案待生效，无法删除；"; }
                    else if (vendor.Status == VendorStatus.ToBeActivated) { errorMsg += "供应商关联档案待激活，无法删除；"; }
                }

                var vendorApply = vendorApplies?.FirstOrDefault(x => x.Company == row.ComCode && x.VendorCode == row.VendorCode);
                if (vendorApply != null)
                {
                    dto.LinkDocNo = vendorApply.VendorNo;
                    errorMsg += "供应商关联档案审批中，无法删除；";
                }

                //公司编码+供应商编码，查询PR Detail
                var prDetail = prEntities?.FirstOrDefault(x => x.CompanyCode == row.ComCode && x.VendorCode == row.VendorCode);
                if (prDetail != null && !prStatusUnfinished.Contains(prDetail.Status))
                    errorMsg += $"该供应商在未关闭PR中，示例单号：{prDetail.ApplicationCode}；";

                if (string.IsNullOrEmpty(errorMsg))
                {
                    //dto.No = success.Count + 1;
                    success.Add(dto);
                }
                else
                {
                    //dto.No = error.Count + 1;
                    dto.ErrorMsg = errorMsg;
                    error.Add(dto);
                }
            }

            if (error.Count > 0)
                return MessageResult.SuccessResult(new ImportDataResponseDto<VendorDeleteImportResponseDto>(error, false));
            return MessageResult.SuccessResult(new ImportDataResponseDto<VendorDeleteImportResponseDto>(success, true));
        }

        public async Task<MessageResult> SubmitImportVendorDelete(List<VendorDeleteImportResponseDto> vendors)
        {
            var repositoryBpcsAvm = LazyServiceProvider.LazyGetService<IBpcsAvmRepository>();
            var queryBpcsAvm = await repositoryBpcsAvm.GetQueryableAsync();

            var repositoryBpcsPmfvm = LazyServiceProvider.LazyGetService<IBpcsPmfvmRepository>();
            var queryBpcsPmfvm = await repositoryBpcsPmfvm.GetQueryableAsync();

            var repositoryVdFc = LazyServiceProvider.LazyGetService<IVendorFinancialRepository>();
            var queryVendorFinancial = await repositoryVdFc.GetQueryableAsync();

            var repositoryVd = LazyServiceProvider.LazyGetService<IVendorRepository>();
            var queryVendor = await repositoryVd.GetQueryableAsync();

            var repositoryApply = LazyServiceProvider.LazyGetService<IVendorApplicationRepository>();
            var queryVendorApply = await repositoryApply.GetQueryableAsync();

            var repositoryApplyFc = LazyServiceProvider.LazyGetService<IVendorApplicationFinancialRepository>();
            var queryVendorApplyFinancial = await repositoryApplyFc.GetQueryableAsync();

            var commonService = _serviceProvider.GetService<ICommonService>();

            var comCodes = vendors.Where(x => !string.IsNullOrEmpty(x.ComCode)).Select(x => x.ComCode).Distinct();
            var vendorCodes = vendors.Where(x => !string.IsNullOrEmpty(x.VendorCode)).Select(x => x.VendorCode).Distinct();

            //Bpcs供应商
            var vendorBpcs = queryBpcsAvm.Where(x => comCodes.Contains(x.Vcmpny.ToString()) && vendorCodes.Contains(x.Vendor.ToString()))
                .Join(queryBpcsPmfvm, a => new { VendorCode = a.Vendor, comCode = a.Vcmpny },
                b => new { VendorCode = b.Vnderx, comCode = b.Vmcmpy }, (a, b) => new { bpcsAvm = a, bpcsFvm = b })
                .Select(x => new { x.bpcsAvm.Vcmpny, x.bpcsAvm.Vendor, x.bpcsAvm.Vmid, x.bpcsAvm.Vnstat, x.bpcsFvm.Vextnm, AvmId = x.bpcsAvm.Id, FvmId = x.bpcsFvm.Id }).ToArray();

            //供应商：是否为待生效、待激活
            var vendorEntities = queryVendorFinancial.Where(x => comCodes.Contains(x.Company) && vendorCodes.Contains(x.VendorCode))
                .Join(queryVendor, a => a.VendorId, b => b.Id, (a, b) => new { financial = a, vendor = b })
                .Select(x => new { x.financial.Company, x.financial.VendorCode, x.vendor.Status, VendorNo = x.vendor.VendorCode, VendorId = x.vendor.Id, VdFinancialId = x.financial.Id, x.vendor.VendorType, x.vendor.UserId }).ToArray();

            //供应商申请：是否为审批中
            var vendorApplies = queryVendorApplyFinancial.Where(x => !string.IsNullOrEmpty(x.Company) && !string.IsNullOrEmpty(x.VendorCode) &&
                comCodes.Contains(x.Company) && vendorCodes.Contains(x.VendorCode))
                .Join(queryVendorApply, a => a.ApplicationId, b => b.Id, (a, b) => new { financial = a, vendor = b })
                .Select(x => new { x.financial.Company, x.financial.VendorCode, x.vendor.Status, VendorNo = x.vendor.VendorCode, vdId = x.vendor.Id, fcId = x.financial.Id, x.vendor.VendorType, x.vendor.UserId }).ToArray();

            List<Guid> IdsBpcsAvm = [];
            List<Guid> IdsBpcsFvm = [];
            List<Guid> IdsVendor = [];
            List<Guid> IdsVendorFinancial = [];
            List<Guid> IdsApply = [];
            List<Guid> IdsApplyFinancial = [];
            List<Guid> IdsAbpUser = [];
            List<SetOperationLogRequestDto> delVendorLogs = [];

            List<VendorStatus> vdStatusPendding = [VendorStatus.ToBeEffective, VendorStatus.ToBeActivated];
            List<Enums.Purchase.PurPRApplicationStatus> prStatusUnfinished = [Enums.Purchase.PurPRApplicationStatus.Draft,
                Enums.Purchase.PurPRApplicationStatus.ApplicantTerminate, Enums.Purchase.PurPRApplicationStatus.Closed];

            foreach (var vendor in vendors)
            {
                var vendorInfo = vendorBpcs.Where(x => x.Vcmpny.ToString() == vendor.ComCode && x.Vendor.ToString() == vendor.VendorCode && x.Vnstat.ToUpper() != "A");
                if (null == vendorInfo || vendorInfo.Count() == 0)
                    continue;

                IdsBpcsAvm.AddRange(vendorInfo.Select(x => x.AvmId));
                IdsBpcsFvm.AddRange(vendorInfo.Select(x => x.FvmId));

                var log = new SetOperationLogRequestDto
                {
                    System = ClientIdScopeConsts.SpeakerPortal,
                    Api = "供应商删除",
                    Content = JsonConvert.SerializeObject(vendor),
                    StartTime = DateTime.Now,
                    IsSuccess = true
                };

                if (string.IsNullOrEmpty(vendor.LinkDocNo))
                {
                    delVendorLogs.Add(log);
                    continue;
                }

                log.FormNo = vendor.LinkDocNo;
                delVendorLogs.Add(log);

                var vendorEtt = vendorEntities.Where(x => x.Company == vendor.ComCode && x.VendorCode == vendor.VendorCode && !vdStatusPendding.Contains(x.Status));
                if (null != vendorEtt)
                {
                    IdsVendor.AddRange(vendorEtt.Select(x => x.VendorId).Distinct());
                    IdsVendorFinancial.AddRange(vendorEtt.Select(x => x.VdFinancialId));
                    var speaker = vendorEtt.Where(x => Guid.Empty != x.UserId && (x.VendorType == VendorTypes.HCPPerson || x.VendorType == VendorTypes.NonHCPPerson));
                    IdsAbpUser.AddRange(speaker.Select(x => x.UserId).Distinct());
                }

                var vendorApply = vendorApplies.Where(x => x.Company == vendor.ComCode && x.VendorCode == vendor.VendorCode && x.Status != Statuses.Approving);
                if (null != vendorApply)
                {
                    IdsApply.AddRange(vendorApply.Select(x => x.vdId).Distinct());
                    IdsApplyFinancial.AddRange(vendorApply.Select(x => x.fcId));
                    var speaker = vendorApply.Where(x => Guid.Empty != x.UserId && (x.VendorType == VendorTypes.HCPPerson || x.VendorType == VendorTypes.NonHCPPerson));
                    IdsAbpUser.AddRange(speaker.Select(x => x.UserId).Distinct());
                }
            }

            await repositoryBpcsAvm.DeleteManyAsync(IdsBpcsAvm.Distinct());
            await repositoryBpcsPmfvm.DeleteManyAsync(IdsBpcsFvm.Distinct());

            await repositoryVd.DeleteManyAsync(IdsVendor.Distinct());
            await repositoryVdFc.DeleteManyAsync(IdsVendorFinancial.Distinct());

            //await repositoryApply.DeleteManyAsync(IdsApply.Distinct());
            //await repositoryApplyFc.DeleteManyAsync(IdsApplyFinancial.Distinct());

            //HCP或者Non-HCP 删除对应的User
            await _identityUserRepository.DeleteManyAsync(IdsAbpUser.Distinct());

            //log 记录xx人 xx时间 删除了xx供应商
            await commonService.CreateOperationLog(delVendorLogs);

            return MessageResult.SuccessResult();
        }

        public async Task<Stream> ExportDPSCheckInfos()
        {
            var queryBpcsAvm = await LazyServiceProvider.LazyGetService<IBpcsAvmReadonlyRepository>().GetQueryableAsync();
            var queryBpcsPmfvm = await LazyServiceProvider.LazyGetService<IBpcsPmfvmReadonlyRepository>().GetQueryableAsync();

            var queryVendorFinancial = await LazyServiceProvider.LazyGetService<IVendorFinancialReadonlyRepository>().GetQueryableAsync();
            var queryVendor = await LazyServiceProvider.LazyGetService<IVendorReadonlyRepository>().GetQueryableAsync();
            var queryVdPers = await LazyServiceProvider.LazyGetService<IVendorPersonalReadonlyRepository>().GetQueryableAsync();
            var queryVdOrg = await LazyServiceProvider.LazyGetService<IVendorOrgnizationReadonlyRepository>().GetQueryableAsync();

            var companyList = await _dataverseService.GetCompanyList(stateCode: null);

            var getAllCity = await _dataverseService.GetAllCity(stateCode: null);
            var getAllProvince = await _dataverseService.GetAllProvince(stateCode: null);

            var results = queryBpcsAvm.Where(x => x.Vnstat.ToUpper() == "A")
                .Join(queryBpcsPmfvm, a => new { VendorNum = a.Vendor, Vmcmpy = a.Vcmpny }, b => new { VendorNum = b.Vnderx, b.Vmcmpy },
                (a, b) => new { bpcsAvmQuery = a, bpcsPmfvmQuery = b })
                .Select(s => new ExportDPSCheckInfosDto
                {
                    Company = s.bpcsAvmQuery.Vcmpny.ToString(),
                    VendorNum = s.bpcsAvmQuery.Vendor.ToString(),
                    //VendorExternalStatus = s.bpcsAvmQuery.Vnstat.ToUpper() == "A" ? "有效" : "失效",
                    VendorExternalStatus = "有效",
                    VendorName = s.bpcsAvmQuery.Vndnam,
                    VendorFullName = s.bpcsPmfvmQuery.Vextnm,
                    //VendorType = s.bpcsAvmQuery.Vtype,
                    StorageDateString = s.bpcsPmfvmQuery.Vcrdte.ToString() + "+" + s.bpcsPmfvmQuery.Vctime.ToString(),
                    //StorageDateString = s.bpcsPmfvmQuery.Vcrdte.ToString() + "+" + s.bpcsPmfvmQuery.Vctime.ToString().PadLeft(6, '0'),
                }).AsQueryable();

            var queryVendorEtts = queryVendorFinancial
                .Join(queryVendor, a => a.VendorId, b => b.Id, (a, b) => new { financial = a, vendor = b })
                .Join(results, a => new { com = a.financial.Company, vdcd = a.financial.VendorCode }, b => new { com = b.Company, vdcd = b.VendorNum }, (a, b) => new { a.financial, a.vendor, bpcsVendor = b })
                .Select(x => new { x.financial.Company, x.financial.VendorCode, x.vendor.Status, VendorNo = x.vendor.VendorCode, VendorId = x.vendor.Id, x.vendor.VendorType, x.bpcsVendor });

            VendorTypes[] personalVendorTypes = { VendorTypes.HCPPerson, VendorTypes.NonHCPPerson };
            VendorTypes[] orgnizationalVendorTypes = { VendorTypes.HCIAndOtherInstitutionsAR, VendorTypes.NonHCIInstitutionalAP };

            var qrVendorPersonEtts = queryVendorEtts.Where(x => personalVendorTypes.Contains(x.VendorType))
                .Join(queryVdPers, a => a.VendorId, b => b.VendorId, (a, b) => new { vendor = a, personal = new { b.Province, b.City, b.PostCode, b.Address } });
            var qrVendorOrgnizationalEtts = queryVendorEtts.Where(x => orgnizationalVendorTypes.Contains(x.VendorType))
                .Join(queryVdOrg, a => a.VendorId, b => b.VendorId, (a, b) => new { vendor = a, orgnizational = new { b.Province, b.City, b.PostCode, b.RegCertificateAddress } });

            var vendorPersonEtts = qrVendorPersonEtts.ToArray();
            var vendorOrgEtts = qrVendorOrgnizationalEtts.ToArray();

            foreach (var item in vendorPersonEtts)
            {
                //时间转换
                item.vendor.bpcsVendor.StorageDateString = item.vendor.bpcsVendor.StorageDateString.Split("+")[0] + item.vendor.bpcsVendor.StorageDateString.Split("+")[1].PadLeft(6, '0');
                if (!string.IsNullOrEmpty(item.vendor.bpcsVendor.StorageDateString) && item.vendor.bpcsVendor.StorageDateString.Length == 14)
                {
                    var dtm = DateTime.ParseExact(item.vendor.bpcsVendor.StorageDateString, "yyyyMMddHHmmss", null);
                    item.vendor.bpcsVendor.StorageDateString = dtm.ToString("yyyy-MM-dd HH:mm:ss");
                }
                item.vendor.bpcsVendor.VendorType = item.vendor.VendorType.GetDescription();

                item.vendor.bpcsVendor.Province = getAllProvince.FirstOrDefault(f => f.Code == item.personal.Province)?.Name;
                item.vendor.bpcsVendor.City = getAllCity.FirstOrDefault(f => f.Code == item.personal.City)?.Name;
                item.vendor.bpcsVendor.PostCode = item.personal.PostCode;
                item.vendor.bpcsVendor.VendorAddress = item.personal.Address;

                // 公司名称
                if (!string.IsNullOrEmpty(item.vendor.bpcsVendor.Company))
                    item.vendor.bpcsVendor.Company = companyList.FirstOrDefault(f => f.CompanyCode == item.vendor.bpcsVendor.Company).CompanyName;
            }

            foreach (var item in vendorOrgEtts)
            {
                //时间转换
                item.vendor.bpcsVendor.StorageDateString = item.vendor.bpcsVendor.StorageDateString.Split("+")[0] + item.vendor.bpcsVendor.StorageDateString.Split("+")[1].PadLeft(6, '0');
                if (!string.IsNullOrEmpty(item.vendor.bpcsVendor.StorageDateString) && item.vendor.bpcsVendor.StorageDateString.Length == 14)
                {
                    var dtm = DateTime.ParseExact(item.vendor.bpcsVendor.StorageDateString, "yyyyMMddHHmmss", null);
                    item.vendor.bpcsVendor.StorageDateString = dtm.ToString("yyyy-MM-dd HH:mm:ss");
                }
                item.vendor.bpcsVendor.VendorType = item.vendor.VendorType.GetDescription();

                item.vendor.bpcsVendor.Province = getAllProvince.FirstOrDefault(f => f.Code == item.orgnizational.Province)?.Name;
                item.vendor.bpcsVendor.City = getAllCity.FirstOrDefault(f => f.Code == item.orgnizational.City)?.Name;
                item.vendor.bpcsVendor.PostCode = item.orgnizational.PostCode;
                item.vendor.bpcsVendor.VendorAddress = item.orgnizational.RegCertificateAddress;

                // 公司名称
                if (!string.IsNullOrEmpty(item.vendor.bpcsVendor.Company))
                    item.vendor.bpcsVendor.Company = companyList.FirstOrDefault(f => f.CompanyCode == item.vendor.bpcsVendor.Company).CompanyName;
            }
            var personalDatas = vendorPersonEtts.Select(x => x.vendor.bpcsVendor);
            var datas = personalDatas.Union(vendorOrgEtts.Select(x => x.vendor.bpcsVendor));

            var responseDatas = datas.Where(x => !string.IsNullOrEmpty(x.VendorType)).OrderByDescending(x => x.StorageDateString);

            MemoryStream stream = new();
            stream.SaveAs(responseDatas, true, "SheetName");
            stream.Seek(0, SeekOrigin.Begin);
            return stream;
        }
        #endregion

        /// <summary>
        /// 根据EPD医院名和Code获取医院主数据
        /// </summary>
        /// <param name="name"></param>
        /// <param name="code"></param>
        /// <returns></returns>
        public async Task<HospitalDto> GetHospitalByEpdHospitalCode(string name, string code)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var approveService = LazyServiceProvider.GetService<ApproveService>();

            var pattern = $"*\\\\{code}\\\\*\\\\*";
            var epdHospitals = await dataverseService.GetEPDBUHospitals(pattern, 50000, 1, null);

            var ettCollection = new EntityCollection();
            bool dataverseResult;
            var veevaId = string.Empty;

            if (epdHospitals.Count == 0)
            {
                //新增EPD业务系统医院主数据
                var epdHospitalEntity = new Entity("spk_buhospitalmasterdata", Guid.NewGuid());
                epdHospitalEntity["spk_sourcesystem"] = BUHospitalConst.EPDHospitalSourceSystem;
                epdHospitalEntity["spk_name"] = name;
                epdHospitalEntity["spk_hospitalcode"] = code;
                ettCollection.Entities.Add(epdHospitalEntity);
                dataverseResult = approveService.TransactionRequest(ettCollection, null, null);
            }
            else
            {
                //更新EPD业务系统医院主数据的名称
                var epdHospital = epdHospitals.First();
                var epdHospitalEntity = new Entity("spk_buhospitalmasterdata", epdHospital.Id);
                epdHospitalEntity["spk_name"] = name;
                ettCollection.Entities.Add(epdHospitalEntity);
                dataverseResult = approveService.TransactionRequest(null, ettCollection, null);
                veevaId = epdHospital.VeevaID;
            }

            if (!dataverseResult)
                return null;

            pattern = $"\\\\{name}\\\\*\\\\";//根据医院name去匹配医院主数据
            var hospitals = await dataverseService.GetAllHospitals(pattern, 1, null);
            if (hospitals.Count == 0 && !string.IsNullOrEmpty(veevaId))
            {
                pattern = $"\\\\*\\\\*\\\\{veevaId}";//根据医院veevaId去匹配医院主数据
                hospitals = await dataverseService.GetAllHospitals(pattern, 1, null);
            }

            return hospitals.FirstOrDefault();
        }


        /// <summary>
        /// 查询EPD医生数据
        /// </summary>
        /// <param name="epdDoctorQuery"></param>
        /// <returns></returns>
        public async Task<MessageResult> QueryEpdDoctorsAsync(EPDDoctorQueryDto epdDoctorQuery)
        {
            var vendorQuery = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();
            var vendorApplicationQuery = await LazyServiceProvider.LazyGetService<IVendorApplicationRepository>().GetQueryableAsync();
            var epdHcpService = LazyServiceProvider.LazyGetService<IEPDHcpService>();
            var epdDoctors = await epdHcpService.QueryEpdDoctorsAsync(new DoctorQueryRequest
            {
                PageNum = epdDoctorQuery.PageIndex + 1,//我们分页是从0开始，EPD是从1开始
                PageSize = epdDoctorQuery.PageSize,
                Name = epdDoctorQuery.Name,
                HospitalName = epdDoctorQuery.HospitalName,
                EpdHcpCode = epdDoctorQuery.EpdHcpCode
            });

            var result = new PagedResultDto<EPDDoctorResponseDto>();
            var doctors = new List<EPDDoctorResponseDto>();
            if (!epdDoctors.Any())
                return MessageResult.SuccessResult(result);
            var epdHcpCodes = epdDoctors.Where(b => !string.IsNullOrWhiteSpace(b.EpdHcpCode)).Select(b => b.EpdHcpCode).ToList();
            //2595[供应商管理][讲者管理]EPD用户选择讲者：筛选EpdId时需要增加排除审批通过的vendorApplications数据
            Statuses[] applicationStatuses = [Statuses.Rejected, Statuses.Delete, Statuses.Passed];
            //2599[供应商管理][讲者管理]EPD用户选择讲者：筛选EpdId时应该筛选新建、变更、激活的非作废、非终止、非审批通过的vendorApplications数据（当前没有带入变更和激活的数据）
            var vendorApplications = vendorApplicationQuery.Where(a => !applicationStatuses.Contains(a.Status) && epdHcpCodes.Contains(a.EpdId)).ToList();
            var vendors = vendorQuery.Where(a => epdHcpCodes.Contains(a.EpdId)).ToList();//优先从Vendor表取（存在审批通过后直接改vendor表中VendorCode的情况）
                                                                                         //1、医院：基于接口返回的EPD医院ID，查询PP中的"业务系统医院主数据"=>查询出对应记录后，找到医院Veeva Id=>以Veeva Id查询NexBPM自己的医院主数据，以匹配回对应的医院主数据ID
                                                                                         //  若返回的EPD医院ID无法在PP中查询到任何记录，或查询到的Veeva Id无法查询出现有医院主数据，则再以医院名称精确匹配NexBPM的医院主数据名称，匹配回对应的医院主数据ID
                                                                                         //  若ID无法匹配，且医院名称也无法匹配，则将该医院名称增加为待验证医院，后续到Veeva验证后再转为正式医院
                                                                                         //2、职称：基于返回的职称名称，匹配PP中的职称主数据，填回ID；若无法匹配出任何现有数据则留空以供用户重新输入
                                                                                         //3、标准科室：与职称相同处理方式
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var bsHospitals = await dataverseService.GetAllBUHospitals();//业务系统医院主数据
            var hospitals = await dataverseService.GetAllHospitals();//医院主数据
            var jobTiles = await dataverseService.GetAllJobTiles();//职称主数据
            var departments = await dataverseService.GetAllDepartments();//科室主数据
            string aesKey = _configuration["Integrations:EPD_HCP_Portal:AesKey256"];
            doctors = epdDoctors.Select(a =>
            {
                var hospital = GetHospitalIdByEpdId(bsHospitals, hospitals, a.HospitalCode, a.HospitalName);
                string mobileEncryption = "";
                if (a.Mobile != null && a.Mobile.Any())
                {
                    List<string> mobiles = new List<string>();
                    foreach (var item in a.Mobile)
                    {
                        mobiles.Add(AesHelper.DecryptionIV(item, aesKey));
                    }
                    mobileEncryption = AesHelper.EncryptionIV(JsonConvert.SerializeObject(mobiles), aesKey);
                }
                return new EPDDoctorResponseDto
                {
                    Name = a.Name,
                    EpdHospitalName = a.HospitalName,
                    HospitalName = hospital == null ? a.HospitalName : hospital.Name,
                    HospitalCode = a.HospitalCode,
                    HospitalId = hospital?.Id,
                    IsMatch = hospital != null,//有值为匹配到了，无值为未匹配到
                    ProfessionalTitle = a.ProfessionalTitle,
                    ProfessionalTitleId = jobTiles.FirstOrDefault(j => j.Name == a.ProfessionalTitle)?.Id,
                    DepartmentName = a.DepartmentName,
                    DepartmentId = departments.FirstOrDefault(j => j.Name == a.DepartmentName)?.Id,
                    HosDepartmentName = a.HosDepartmentName,
                    EpdHcpCode = a.EpdHcpCode,
                    Mobile = mobileEncryption,
                    HcpDcrStatus = a.HcpDcrStatus,
                    HcpDcrStatusName = !string.IsNullOrWhiteSpace(a.HcpDcrStatus) && Enum.TryParse<EPDDcrStatus>(a.HcpDcrStatus.Replace("-", ""), true, out _) ? ((EPDDcrStatus)Enum.Parse(typeof(EPDDcrStatus), a.HcpDcrStatus, true)).GetDescription() : "",
                    SpeakerCode = vendors.FirstOrDefault(v => v.EpdId == a.EpdHcpCode) != null ? vendors.FirstOrDefault(v => v.EpdId == a.EpdHcpCode).VendorCode : vendorApplications.FirstOrDefault(v => v.EpdId == a.EpdHcpCode)?.VendorCode
                };
            }).ToList();
            result.Items = doctors;
            result.TotalCount = doctors.Count();
            return MessageResult.SuccessResult(result);
        }

        /// <summary>
        /// EPD关联讲者
        /// </summary>
        /// <param name="epdRelationSpeaker"></param>
        /// <returns></returns>
        public async Task<MessageResult> EpdRelationSpeakerAsync(EpdRelationSpeakerDto epdRelationSpeaker)
        {
            var vendorRepository = _serviceProvider.GetService<IVendorRepository>();
            var vendorQuery = await vendorRepository.GetQueryableAsync();
            var vendorApplicationRepository = _serviceProvider.GetService<IVendorApplicationRepository>();
            var vendorApplicationQuery = await vendorApplicationRepository.GetQueryableAsync();
            if (epdRelationSpeaker.VendorId.HasValue)
            {
                var vendor = vendorQuery.Where(a => a.Id == epdRelationSpeaker.VendorId.Value).FirstOrDefault();
                vendor.EpdId = epdRelationSpeaker.EpdId;
                await vendorRepository.UpdateAsync(vendor);
            }
            if (epdRelationSpeaker.VendorApplicationId.HasValue)
            {
                var vendorApplication = vendorApplicationQuery.Where(a => a.Id == epdRelationSpeaker.VendorApplicationId.Value).FirstOrDefault();
                vendorApplication.EpdId = epdRelationSpeaker.EpdId;
                await vendorApplicationRepository.UpdateAsync(vendorApplication);
            }
            if (epdRelationSpeaker.DraftId.HasValue)
            {
                var ids = new List<Guid>();
                ids.Add(epdRelationSpeaker.DraftId.Value);
                return await DeleteSpeakerDraftAsync(new DeleteSpeakerDraftRequestDto() { ID = ids });
            }
            return MessageResult.SuccessResult();
        }
        #region EPD 查询医生 相关私有方法
        /// <summary>
        /// 查询医院主数据ID 根据EPD ID
        /// </summary>
        /// <param name="buHospitals"></param>
        /// <param name="hospitals"></param>
        /// <param name="epdHospitalCode"></param>
        /// <returns></returns>
        private HospitalDto GetHospitalIdByEpdId(List<BUHospitalDto> buHospitals, List<HospitalDto> hospitals, string epdHospitalCode, string epdHospitalName)
        {
            HospitalDto hospital = null;
            var buHospital = buHospitals.Where(a => a.HospitalCode == epdHospitalCode).FirstOrDefault();
            if (buHospital != null && !string.IsNullOrEmpty(buHospital.VeevaID))
            {
                hospital = hospitals.Where(a => a.HcoVeevaID == buHospital.VeevaID).FirstOrDefault();
            }
            if (hospital == null && !string.IsNullOrEmpty(epdHospitalName))
            {
                hospital = hospitals.Where(a => a.Name == epdHospitalName).FirstOrDefault();
            }
            return hospital;
        }
        #endregion

        /// <summary>
        /// 获取讲着或者非HCP个人的申请或者正是档案
        /// </summary>
        /// <param name="vendorId"></param>
        /// <returns></returns>
        public async Task<IEnumerable<GetVendorApplicationOrOfficialInfoDto>> GetVendorApplicationOrOfficialInfoAsync(Guid vendorId)
        {
            var queryVendorApp = await LazyServiceProvider.LazyGetService<IVendorApplicationRepository>().GetQueryableAsync();
            var queryVendorAppPersonal = await LazyServiceProvider.LazyGetService<IVendorApplicationPersonalRepository>().GetQueryableAsync();

            var queryVendor = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();
            var queryVendorPersonal = await LazyServiceProvider.LazyGetService<IVendorPersonalRepository>().GetQueryableAsync();

            var vendorDatas = queryVendorApp.Where(a => a.Id == vendorId).Join(queryVendorAppPersonal, a => a.Id, a => a.ApplicationId, (a, b) => new GetVendorApplicationOrOfficialInfoDto
            {
                MobileEncrypt = a.MobileEncrypt,
                Phone = a.HandPhone,
                IsDraft = true
            })
            .Concat(queryVendor.Where(a => a.Id == vendorId).Join(queryVendorPersonal, a => a.Id, a => a.VendorId, (a, b) => new GetVendorApplicationOrOfficialInfoDto
            {
                Phone = a.HandPhone,
                IsDraft = false
            }))
            .ToArray();

            string aesKey = _configuration["Integrations:EPD_HCP_Portal:AesKey256"];
            foreach (var item in vendorDatas)
            {
                if (item.IsDraft)
                    item.ReservedMobilePhone = JsonConvert.DeserializeObject<string[]>(AesHelper.DecryptionIV(item.MobileEncrypt, aesKey));
                else
                    item.ReservedMobilePhone = [item.Phone];
            }

            return vendorDatas;
        }

        #region 小程序 验证身份证号码重复

        /// <summary>
        /// 验证证件是否唯一
        /// </summary>
        /// <param name="request"></param>
        /// <param name="vendorPersonal"></param>
        /// <param name="vendorApplicationPersonal"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        private async Task<string> ValidationCardNo(SaveSpeakerRequestDto request, IVendorApplicationRepository vendorApplication, IVendorApplicationPersonalRepository vendorApplicationPersonal, IVendorRepository vendor, IVendorPersonalRepository vendorPersonal)
        {
            var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");
            var ErrorMsg = string.Empty;
            var EncryptionCardNo = string.Empty;
            if (!string.IsNullOrEmpty(request.CardNo))
                EncryptionCardNo = AesHelper.Encryption(request.CardNo, insightKey);
            //验证证件号
            try
            {
                //正式表数据验证
                var queryVendor = await vendor.GetQueryableAsync();
                var queryVendorPers = await vendorPersonal.GetQueryableAsync();
                var duplicateCardNoVendors = queryVendorPers
                    .Where(x => x.CardType == request.CardType && x.CardNo == EncryptionCardNo)
                    .Join(queryVendor, a => a.VendorId, b => b.Id, (a, b) => new { a.SPName, a.CardNo, b.VendorCode })
                    .Where(a => a.VendorCode != request.VendorCode)//排除当前行
                    .ToArray();
                if (duplicateCardNoVendors.Length > 0)
                    ErrorMsg = $"身份证号码在{duplicateCardNoVendors[0].VendorCode}中重复";
                //duplicateCardNoVendors.ToList().ForEach(x => { ErrorMsg += $"该证件号码在{x.VendorCode}号中与{x.SPName}讲者重复;"; });

                //申请草稿数据验证
                var queryVendorApply = await vendorApplication.GetQueryableAsync();
                var queryVendorApplyPers = await vendorApplicationPersonal.GetQueryableAsync();
                var duplicateCardNoVendorApplies = queryVendorApplyPers
                    .Where(x => x.CardType == request.CardType && x.CardNo == EncryptionCardNo)
                    .Join(queryVendorApply, a => a.ApplicationId, b => b.Id, (a, b) => new { a.SPName, b.Id, b.VendorCode, b.Status })
                    .Where(x => x.Id != request.ID && (x.Status == Statuses.Approving || x.Status == Statuses.Returned || x.Status == Statuses.Withdraw)).ToArray();
                if (duplicateCardNoVendorApplies.Length > 0)
                    ErrorMsg = $"身份证号码在{duplicateCardNoVendorApplies[0].VendorCode}中重复";
                //duplicateCardNoVendorApplies.ToList().ForEach(x => ErrorMsg += $"该证件号码在{x.VendorCode}号中与{x.SPName}讲者重复;");
            }
            catch (Exception ex)
            {
                return ErrorMsg = ex.Message;
            }

            return ErrorMsg;
        }

        /// <summary>
        /// 验证证件是否唯一
        /// </summary>
        /// <param name="vendorRepo"></param>
        /// <param name="vendorPersRepo"></param>
        /// <param name="vendorApplyRepo"></param>
        /// <param name="vendorApplyPersRepo"></param>
        /// <param name="vendorApplyId"></param>
        /// <param name="cardNo"></param>
        /// <param name="cardType"></param>
        /// <param name="vendorCode"></param>
        /// <returns></returns>
        private async Task<string> VerifyUniqueForCardNo(IVendorRepository vendorRepo, IVendorPersonalRepository vendorPersRepo,
            IVendorApplicationRepository vendorApplyRepo, IVendorApplicationPersonalRepository vendorApplyPersRepo,
            Guid vendorApplyId, string cardNo, string cardType, string vendorCode)
        {
            var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");
            var ErrorMsg = string.Empty;
            var EncryptionCardNo = string.Empty;
            if (!string.IsNullOrEmpty(cardNo))
                EncryptionCardNo = AesHelper.Encryption(cardNo, insightKey);
            //验证证件号
            try
            {
                //正式表数据验证
                var queryVendor = await vendorRepo.GetQueryableAsync();
                var queryVendorPers = await vendorPersRepo.GetQueryableAsync();
                var duplicateCardNoVendors = queryVendorPers
                    .Where(x => x.CardType == cardType && x.CardNo == EncryptionCardNo)
                    .Join(queryVendor, a => a.VendorId, b => b.Id, (a, b) => new { a.SPName, a.CardNo, b.VendorCode })
                    .Where(a => a.VendorCode != vendorCode)//排除当前行
                    .ToArray();
                if (duplicateCardNoVendors.Length > 0)
                    ErrorMsg = $"身份证号码在{duplicateCardNoVendors[0].VendorCode}中重复";
                //duplicateCardNoVendors.ToList().ForEach(x => { ErrorMsg += $"该证件号码在{x.VendorCode}号中与{x.SPName}讲者重复;"; });

                //申请草稿数据验证
                var queryVendorApply = await vendorApplyRepo.GetQueryableAsync();
                var queryVendorApplyPers = await vendorApplyPersRepo.GetQueryableAsync();
                var duplicateCardNoVendorApplies = queryVendorApplyPers
                    .Where(x => x.CardType == cardType && x.CardNo == EncryptionCardNo)
                    .Join(queryVendorApply, a => a.ApplicationId, b => b.Id, (a, b) => new { a.SPName, b.Id, b.VendorCode, b.Status })
                    .Where(x => x.Id != vendorApplyId && (x.Status == Statuses.Approving || x.Status == Statuses.Returned || x.Status == Statuses.Withdraw)).ToArray();
                if (duplicateCardNoVendorApplies.Length > 0)
                    ErrorMsg = $"身份证号码在{duplicateCardNoVendorApplies[0].VendorCode}中重复";
                //duplicateCardNoVendorApplies.ToList().ForEach(x => ErrorMsg += $"该证件号码在{x.VendorCode}号中与{x.SPName}讲者重复;");
            }
            catch (Exception ex)
            {
                return ErrorMsg = ex.Message;
            }

            return ErrorMsg;
        }
        #endregion

        /// <summary>
        /// 判断当前登录人，如果是供应商，那么他只能看他自己的供应商信息
        /// </summary>
        /// <param name="vendorId"></param>
        /// <returns></returns>
        public async Task<bool> JudgeIfCurrentUserIsVendorAsync(Guid vendorId)
        {
            var queryVendorApp = await LazyServiceProvider.LazyGetService<IVendorApplicationRepository>().GetQueryableAsync();
            var queryVendor = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();
            var queryUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();

            var existUserAndVendors = queryUser.Where(a => a.Id == CurrentUser.Id && !string.IsNullOrEmpty(a.PhoneNumber)).Join(queryVendorApp, a => a.PhoneNumber, a => a.HandPhone, (a, b) => b.Id)
                .Concat
                (
                    queryUser.Where(a => a.Id == CurrentUser.Id && !string.IsNullOrEmpty(a.PhoneNumber)).Join(queryVendor, a => a.PhoneNumber, a => a.HandPhone, (a, b) => b.Id)
                ).ToArray();

            return !existUserAndVendors.Any() || existUserAndVendors.Any(a => a == vendorId);
        }

        /// <summary>
        /// 发送信息已完善邮件
        /// </summary>
        /// <param name="vendorApplication"></param>
        /// <param name="vendorApplicationPersonal"></param>
        /// <returns></returns>
        public async Task SendCompleteInfoEmail((Guid Id, Guid ApplyUserId, VendorTypes VendorType, ApplicationTypes ApType, Statuses VdApStatus, string ApplyUserName, string VendorName) vendorApplication)
        {
            var webRoot = LazyServiceProvider.LazyGetService<IWebHostEnvironment>().WebRootFileProvider;
            var bodyHtml = await webRoot.GetFileInfo("Templates/Email/CompletedInfoSpeakerNotice.html").ReadAsStringAsync();
            var vendorTypes = EnumUtil.GetEnumIdValues<VendorTypes>();

            var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var user = queryableUser.Where(a => a.Id == vendorApplication.ApplyUserId).FirstOrDefault();
            var vendorType = vendorTypes.FirstOrDefault(a => a.Key == (int)vendorApplication.VendorType)?.Value;

            var interfaceUrl = vendorApplication.VendorType == VendorTypes.HCPPerson ? $"/speaker/speakerDetail/{vendorApplication.Id}?isFromDraft=true" : $"/speaker/nonSpeakerDetail/{vendorApplication.Id}?draft=true";

            //2660:个人补充信息后点击提交，发送完善信息邮件中的url需要引导邀请人进入“任务中心 - 我发起的 - 新建tab下”重发起页面--BE
            if (vendorApplication.VdApStatus == Statuses.Returned || vendorApplication.VdApStatus == Statuses.Withdraw)
            {
                if (vendorApplication.VendorType == VendorTypes.HCPPerson)
                {
                    var actionType = vendorApplication.ApType == ApplicationTypes.Create ? "SPEAKER_CREATE" : vendorApplication.ApType == ApplicationTypes.Update ? "SPEAKER_CHANGE" : "SPEAKER_ACTIVATE";
                    interfaceUrl = $"/task/relaunchEdit/{actionType}/{vendorApplication.Id}?isFromDraft=true&actionType=edit&resubmit=true&applicationType={(int)vendorApplication.ApType}&breadcrumbTitle=讲者重新提交";
                }
                else
                {
                    var actionType = vendorApplication.ApType == ApplicationTypes.Create ? "VENDOR_CREATE" : vendorApplication.ApType == ApplicationTypes.Update ? "VENDOR_CHANGE" : "VENDOR_ACTIVATE";
                    interfaceUrl = $"/task/relaunchEdit/{actionType}/{vendorApplication.Id}?draft=true&vendorType=2&breadcrumbTitle=非HCP个人重新提交&isApprove=false&isComplete=false&isPending=true&isInProgress=false";
                }
            }

            bodyHtml = bodyHtml.Replace("{VendorType}", vendorType);
            bodyHtml = bodyHtml.Replace("{UserName}", user.Name);
            bodyHtml = bodyHtml.Replace("{VendorName}", vendorApplication.VendorName);
            bodyHtml = bodyHtml.Replace("{OperationTime}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            bodyHtml = bodyHtml.Replace("{ApplicationLink}", $"{_configuration["SpeakerEmail:WebHost"]?.TrimEnd('/')}{interfaceUrl}");

            var sendEmaillRecords = new InsertSendEmaillRecordDto
            {
                EmailAddress = user.Email,
                Subject = $"[NexBPM]您邀请的【{vendorType}】已完善信息，请您及时提交申请",
                Content = bodyHtml,
                SourceType = vendorApplication.VendorType == VendorTypes.HCPPerson ? EmailSourceType.HcpCompleteInfo : EmailSourceType.NonHcpCompleteInfo,
                Status = SendStatus.Pending,
                Attempts = 0,
            };

            //记录邮件，并触发邮件发送功能
            var emailService = LazyServiceProvider.LazyGetService<IEmailService>();
            await emailService.InsertSendEmaillRecordAsync([sendEmaillRecords]);
            // 调度作业
            BackgroundJob.Enqueue<SendEmailWorker>(a => a.DoWorkAsync());
        }

        /// <summary>
        /// 生成随机密钥字符串
        /// </summary>
        /// <param name="keySize"></param>
        /// <returns></returns>
        private static string GenerateRandomKey(int keySize = 32)
        {
            var str = Guid.NewGuid().ToString().Replace("-", "");
            return str;
            //byte[] key = new byte[keySize];
            //RandomNumberGenerator.Fill(key);
            //return key.JoinAsString("");
            //return Convert.ToBase64String(key);
        }
        /// <summary>
        /// 判断是否有重复申请
        /// </summary>
        /// <param name="vendorApplication"></param>
        /// <param name="vendorCode"></param>
        /// <param name="vendorApplicationId"></param>
        /// <returns></returns>
        private async Task<Guid?> ValidationVendorApplicationDuplication(IVendorApplicationRepository vendorApplication, string vendorCode, Guid? vendorApplicationId = null)
        {

            IEnumerable<Statuses> statuses = new List<Statuses> { Statuses.Saved, Statuses.Approving, Statuses.Returned, Statuses.Withdraw };
            statuses = statuses.ToHashSet();
            VendorApplication vendorapplication = null;
            if (vendorApplicationId != null)
            {
                //草稿保存，
                vendorapplication = await vendorApplication.FirstOrDefaultAsync(f => f.Id != vendorApplicationId && f.VendorCode == vendorCode && statuses.Contains(f.Status));
            }
            else
            {
                vendorapplication = await vendorApplication.FirstOrDefaultAsync(f => f.VendorCode == vendorCode && statuses.Contains(f.Status));
            }

            if (vendorapplication == null)
            {
                return null;
            }
            else
            {
                return vendorapplication.ApplyUserId;
            }
        }

        /// <summary>
        /// 获取EPD 医院
        /// </summary>
        /// <param name="hospital"></param>
        /// <param name="epdHospitalId"></param>
        /// <param name="hospitalName"></param>
        /// <returns></returns>
        private async Task<BUHospitalDto> GetEpdHospitalAsync(HospitalDto hospital, string epdHospitalId, string hospitalName)
        {
            if (!string.IsNullOrEmpty(epdHospitalId))
            {
                //1.若该供应商/申请单的EPDhospitalID有值，则以该code查询【业务系统医院主数据】中【来源系统】=【EPD HCP PORTAL】的条件下，【医院代码】= epdhospitalid的数据，找回对应的【医院名称】
                var epdHospitals = await _dataverseService.GetEPDBUHospitals(pattern: $"*\\\\{epdHospitalId}\\\\*\\\\*", pageSize: 50000, count: 1, stateCode: null);
                return epdHospitals.FirstOrDefault();
            }
            else if (hospital != null)
            {
                //2.若为空，则以供应商 / 申请单的hospitalid查询【医院主数据】中的医院，
                //a.若能查询到医院，则以医院名称匹配【业务系统医院主数据】中【来源系统】=【EPD HCP PORTAL】的条件下，【医院名称】= 【医院主数据查询出的医院名称】的数据，以该名称展示
                //b.若能查询到医院，且根据a的逻辑无法匹配出医院，则以非空的医院Veeva ID匹配【业务系统医院主数据】中【来源系统】=【EPD HCP PORTAL】的条件下，【医院Veeva ID】= 【医院主数据查询出的Veeva ID】的数据，展示匹配出的【医院名称】
                //c.若能查询到医院，且根据a & b的逻辑均无法匹配出医院,则留空
                var epdHospital = (await _dataverseService.GetEPDBUHospitals(pattern: $"*\\\\*\\\\{hospital.Name}\\\\*", pageSize: 50000, count: 1, stateCode: null)).FirstOrDefault();
                if (epdHospital != null)
                {
                    return epdHospital;
                }
                else if (!string.IsNullOrWhiteSpace(hospital.HcoVeevaID))
                {
                    var epdHospital_Veeva = (await _dataverseService.GetEPDBUHospitals(pattern: $"*\\\\*\\\\*\\\\{hospital.HcoVeevaID}", pageSize: 50000, count: 1, stateCode: null)).FirstOrDefault();
                    if (epdHospital_Veeva != null)
                    {
                        return epdHospital_Veeva;
                    }
                }
            }
            else
            {
                //d.若hospitalid为空或根据ID无法匹配到医院主数据，则以hospitalname匹配【业务系统医院主数据】中【来源系统】=【EPD HCP PORTAL】的条件下，【医院名称】= 【医院主数据查询出的医院名称】的数据，以该名称展示
                //e.若hospitalid为空或根据ID无法匹配到医院主数据，且根据d的逻辑均无法匹配出医院,则留空
                var epdHospital = (await _dataverseService.GetEPDBUHospitals(pattern: $"*\\\\*\\\\{hospitalName}\\\\*", pageSize: 50000, count: 1, stateCode: null)).FirstOrDefault();
                if (epdHospital != null)
                {
                    return epdHospital;
                }
            }
            return null;
        }
    }
}
