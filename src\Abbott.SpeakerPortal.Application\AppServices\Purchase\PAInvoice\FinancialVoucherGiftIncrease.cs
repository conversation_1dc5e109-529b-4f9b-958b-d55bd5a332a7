﻿using Abbott.SpeakerPortal.Contracts.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPAApplication.PAInvoice;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.AppServices.Purchase.PAInvoice
{
    /// <summary>
    /// 礼品赠票
    /// </summary>
    public class FinancialVoucherGiftIncrease : IFinancialVoucherInvoice
    {
        public async Task<List<FinancialVoucherInfoDto>> GetFinancialVoucherInfo(PurPAApplicationDetailDto paApplicationDetail)
        {
            decimal taxRate = 0M;
            if (!decimal.TryParse(paApplicationDetail.TaxRate, out taxRate))
            {
                taxRate = 0M;
            }
            List<FinancialVoucherInfoDto> financialVoucherInfos = new List<FinancialVoucherInfoDto>();
            // 礼品赠票 生成一条不含税财务凭证信息
            var financialVoucherInfo = new FinancialVoucherInfoDto();
            financialVoucherInfo.PAId = paApplicationDetail.PurPAApplicationId;
            financialVoucherInfo.PRDetailId = paApplicationDetail.PRDetailId;
            financialVoucherInfo.PODetailId = paApplicationDetail.PODetailId;
            financialVoucherInfo.GRHistoryId = paApplicationDetail.GRHistoryId;
            financialVoucherInfo.PAInvoiceId = paApplicationDetail.Id;
            financialVoucherInfo.InvoiceType = "GiftIncrease";
            financialVoucherInfo.GroupingDimension = GroupingDimension.TaxNotIncluded;
            var taxAmount = (paApplicationDetail.PaymentAmount / (1 + taxRate)) * taxRate;//税额
            financialVoucherInfo.InvoiceLineAmount = decimal.Round(paApplicationDetail.PaymentAmount,2);
            financialVoucherInfos.Add(financialVoucherInfo);

            // 礼品赠票 生成一条正税额财务凭证信息
            var financialVoucherInfoTaxAmount = new FinancialVoucherInfoDto();
            financialVoucherInfoTaxAmount.PAId = paApplicationDetail.PurPAApplicationId;
            financialVoucherInfoTaxAmount.PRDetailId = paApplicationDetail.PRDetailId;
            financialVoucherInfoTaxAmount.PODetailId = paApplicationDetail.PODetailId;
            financialVoucherInfoTaxAmount.GRHistoryId = paApplicationDetail.GRHistoryId;
            financialVoucherInfoTaxAmount.PAInvoiceId = paApplicationDetail.Id;
            financialVoucherInfoTaxAmount.InvoiceType = "GiftIncrease";
            financialVoucherInfoTaxAmount.GroupingDimension = GroupingDimension.PositiveTax;
            financialVoucherInfoTaxAmount.InvoiceLineAmount = decimal.Round(taxAmount,2);
            financialVoucherInfos.Add(financialVoucherInfoTaxAmount);
            // 礼品赠票 生成一条负税额财务凭证信息
            var financialVoucherInfoTaxAmount1 = new FinancialVoucherInfoDto();
            financialVoucherInfoTaxAmount1.PAId = paApplicationDetail.PurPAApplicationId;
            financialVoucherInfoTaxAmount1.PRDetailId = paApplicationDetail.PRDetailId;
            financialVoucherInfoTaxAmount1.PODetailId = paApplicationDetail.PODetailId;
            financialVoucherInfoTaxAmount1.GRHistoryId = paApplicationDetail.GRHistoryId;
            financialVoucherInfoTaxAmount1.PAInvoiceId = paApplicationDetail.Id;
            financialVoucherInfoTaxAmount1.InvoiceType = "GiftIncrease";
            financialVoucherInfoTaxAmount1.GroupingDimension = GroupingDimension.NegativeTax;
            financialVoucherInfoTaxAmount1.InvoiceLineAmount = - decimal.Round(taxAmount, 2) ;
            financialVoucherInfos.Add(financialVoucherInfoTaxAmount1);
            return await Task.FromResult(financialVoucherInfos);
        }
    }
}
