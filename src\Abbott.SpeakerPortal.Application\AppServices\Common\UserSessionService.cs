﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Redis;

namespace Abbott.SpeakerPortal.AppServices.Common
{
    /// <summary>
    /// 用户session管理
    /// </summary>
    public class UserSessionService : SpeakerPortalAppService, IUserSessionService
    {
        readonly IRedisRepository _redisRepository;

        public UserSessionService(IRedisRepository redisRepository)
        {
            _redisRepository = redisRepository;
        }

        /// <summary>
        /// 记录用户的session id
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="sessionId"></param>
        /// <returns></returns>
        public async Task CreateSessionAsync(string userId, string sessionId)
        {
            await _redisRepository.Database.HashSetAsync(RedisKey.ValidTokens, userId, sessionId);
        }

        /// <summary>
        /// 获取用户的session id
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<string> GetCurrentSessionIdAsync(string userId)
        {
            return await _redisRepository.Database.HashGetAsync(RedisKey.ValidTokens, userId);
        }

        /// <summary>
        /// 移除用户的session id
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task RemoveSessionAsync(string userId)
        {
            await _redisRepository.Database.HashDeleteAsync(RedisKey.ValidTokens, userId);
        }
    }
}
