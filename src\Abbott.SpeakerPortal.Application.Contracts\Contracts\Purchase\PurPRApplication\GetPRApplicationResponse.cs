﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Text;

using Abbott.SpeakerPortal.Contracts.Common.Attachment;

using static Abbott.SpeakerPortal.Enums.Purchase;

namespace Abbott.SpeakerPortal.Contracts.Purchase.PurPRApplication
{
    public class GetPRApplicationResponse : UpdatePRApplicationRequest
    {
        /// <summary>
        /// 是否可以作废
        /// </summary>
        public bool CanDeprecate { get; set; }

        /// <summary>
        /// 是否可以供应商确认
        /// </summary>
        public bool CanConfirm { get; set; }

        /// <summary>
        /// 是否可以撤回
        /// </summary>
        public bool CanRecall { get; set; }

        /// <summary>
        /// 特殊撤回标记（流程完结后根据次标记调用特殊撤回接口）
        /// </summary>
        public bool SpecialRecall { get; set; }

        /// <summary>
        /// 特殊作废标记
        /// </summary>
        public bool SpecialDeprecate { get; set; }

        /// <summary>
        /// 是否可以修订
        /// </summary>
        public bool CanAmend { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public List<KeyValuePair<PurPRApplicationStatus, string>> Status { get; set; } = new List<KeyValuePair<PurPRApplicationStatus, string>>();

        /// <summary>
        /// 申请人名字
        /// </summary>
        public string Applicant { get; set; }

        /// <summary>
        /// 申请时间
        /// </summary>
        public string ApplyTime { get; set; }

        /// <summary>
        /// 采购申请单号
        /// </summary>
        public string ApplicationCode { get; set; }

        /// <summary>
        /// 预算对象
        /// </summary>
        public GetBudgetInfoResponse Budget { get; set; }

        /// <summary>
        /// 成本中心名称
        /// </summary>
        public string CostCenterName { get; set; }

        /// <summary>
        /// 被代理人Name
        /// </summary>
        public string AgentIdName { get; set; }

        /// <summary>
        /// 主持人Name
        /// </summary>
        public string HostVendorIdName { get; set; }

        /// <summary>
        /// 消费大类Name
        /// </summary>
        public string ExpenseTypeName { get; set; }

        /// <summary>
        /// 消费大类Code
        /// </summary>
        public string ExpenseTypeCode { get; set; }

        /// <summary>
        /// 大区Name
        /// </summary>
        public string BudgetRegionName { get; set; }

        /// <summary>
        /// 产品Names，逗号分割
        /// </summary>
        public string ProductNames { get; set; }

        /// <summary>
        /// 公司Name
        /// </summary>
        public string CompanyIdName { get; set; }

        /// <summary>
        /// 活动日期
        /// </summary>
        public new string AcitveDate { get; set; }

        /// <summary>
        /// 活动类型
        /// </summary>
        public string ActiveTypeName { get; set; }

        /// <summary>
        /// 项目类型
        /// </summary>
        public string ProjectTypeName { get; set; }

        /// <summary>
        /// 活动覆盖医院
        /// </summary>
        public string HospitalNames { get; set; }

        /// <summary>
        /// 覆盖科室
        /// </summary>
        public string HospitalDepartmentNames { get; set; }

        /// <summary>
        /// OM 会议状态
        /// </summary>
        public string MeetingStatus { get; set; }

        /// <summary>
        /// 会议类型
        /// </summary>
        public string MeetingTypeName { get; set; }

        /// <summary>
        /// 系列会类型
        /// </summary>
        public string SerialMeetingTypeName { get; set; }

        /// <summary>
        /// 主会场PR（历史单据Name）
        /// </summary>
        public string MainMeetingPRName { get; set; }

        /// <summary>
        /// 会议主办方性质
        /// </summary>
        public string OrganizerNatureName { get; set; }

        /// <summary>
        /// 赞助类型
        /// </summary>
        public string SponsorshipTypeName { get; set; }

        /// <summary>
        /// 会议时间
        /// </summary>
        public new string MeetingDate { get; set; }

        /// <summary>
        /// 计划的市场调研主导方
        /// </summary>
        public string MarketResearchLeaderName { get; set; }

        /// <summary>
        /// 支持的附件
        /// </summary>
        public new IEnumerable<UploadFileResponseDto> SupportFiles { get; set; }

        /// <summary>
        /// 采购明细
        /// </summary>
        public new IEnumerable<GetPRApplicationDetailResponse> DetailItems { get; set; }

        /// <summary>
        /// 会议支持的预计费用项
        /// </summary>
        public new IEnumerable<GetPRApplicationCostItemResponse> CostItems { get; set; }

        /// <summary>
        /// Hcp旅行社会务费
        /// </summary>
        public new IEnumerable<GetPRApplicationHcpTravelAgencyConferenceFeeResponse> HcpTravelAgencyConferenceFees { get; set; }

        /// <summary>
        /// PO推送时的补录文件
        /// </summary>
        public IEnumerable<UploadFileResponseDto> POPushAdditionalFiles { get; set; }

        public bool ParentOrgJiaXin {  get; set; }
    }

    public class GetPRApplicationDetailResponse : CreateUpdatePRApplicationDetailRequest
    {
        /// <summary>
        /// 行号
        /// </summary>
        public int RowNo { get; set; }

        /// <summary>
        /// PR ID
        /// </summary>
        public Guid? PRId { get; set; }

        /// <summary>
        /// PR详细Id
        /// </summary>
        public Guid? PRDetailId { get; set; }

        /// <summary>
        /// 付款方式Name
        /// </summary>
        public string PayMethodName { get; set; }

        /// <summary>
        /// 预估日期
        /// </summary>
        public new string EstimateDate { get; set; }

        /// <summary>
        /// Payment term
        /// </summary>
        public string PaymentTerm { get; set; }

        /// <summary>
        /// HCP级别
        /// </summary>
        public string HcpLevel { get; set; }

        /// <summary>
        /// 所属医院
        /// </summary>
        public string Hospital { get; set; }

        /// <summary>
        /// 费用性质Name
        /// </summary>
        public string CostNatureName { get; set; }

        /// <summary>
        /// 成本中心
        /// </summary>
        public string Costcenter { get; set; }

        /// <summary>
        /// 城市主数据Name
        /// </summary>
        public string CityIdName { get; set; }

        /// <summary>
        /// 税率Name
        /// </summary>
        public string TaxRateName { get; set; }

        /// <summary>
        /// 产品Name
        /// </summary>
        public string ProductIdName { get; set; }

        /// <summary>
        /// 讲者类型Name
        /// </summary>
        public string VendorTypeName { get; set; }

        /// <summary>
        /// 幻灯片类型Name
        /// </summary>
        public string SlideTypeName { get; set; }

        /// <summary>
        /// 备选讲者Name
        /// </summary>
        public string BackUpVendorsName { get; set; }

        /// <summary>
        /// 执行人Name
        /// </summary>
        public string ExecutorName { get; set; }

        /// <summary>
        /// PR单号
        /// </summary>
        public string ApplicationCode { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 公司名
        /// </summary>
        public string CompanyName { get; set; }

        /// <summary>
        /// 公司Code
        /// </summary>
        public string CompanyCode { get; set; }

        /// <summary>
        /// PR 文件
        /// </summary>
        public UploadFileResponseDto PRFiles { get; set; }

        /// <summary>
        /// 是否是Pr late
        /// </summary>
        public bool IsPrLate { get; set; }

        /// <summary>
        /// 申请人
        /// </summary>
        public string Applicant { get; set; }
        /// <summary>
        /// 申请人bu
        /// </summary>
        public string ApplicationBu { get; set; }

        /// <summary>
        /// 申请人ID
        /// </summary>
        public Guid ApplyUserId { get; set; }

        /// <summary>
        /// 申请人名字
        /// </summary>
        public string ApplyUserName { get; set; }

        /// <summary>
        /// 申请人BU
        /// </summary>
        public Guid ApplyUserBu { get; set; }
        /// <summary>
        /// 申请人BU名
        /// </summary>
        public string ApplyUserBuName { get; set; }

        /// <summary>
        /// 申请人部门Id
        /// </summary>
        public Guid ApplyUserDept { get; set; }

        /// <summary>
        /// 申请人部门名称
        /// </summary>
        public string ApplyUserBuToDeptName { get; set; }

        /// <summary>
        /// 是否可以变更供应商
        /// </summary>
        public bool CanExchangeVendor { get; set; }

        /// <summary>
        /// 是否可以反冲
        /// </summary>
        public bool CanHedge { get; set; }

        /// <summary>
        /// 竞价豁免Id
        /// </summary>
        public Guid? BWId { get; set; }
        /// <summary>
        /// 竞价豁免Code
        /// </summary>
        public string BWApplicationCode { get; set; }

        /// <summary>
        /// 豁免模块
        /// 枚举：1.竞价豁免 2.Justification
        /// </summary>
        public ExemptType? ExemptType { get; set; }

        /// <summary>
        /// bidding ID
        /// </summary>
        public Guid? BDId { get; set; }

        /// <summary>
        /// bidding Code
        /// </summary>
        public string BDApplicationCode { get; set; }

        /// <summary>
        /// 是否推送在线会议
        /// </summary>
        public bool PushOnlineMeeting{ get; set; }

        /// <summary>
        /// 结算金额
        /// </summary>
        public decimal? SettlementAmount { get; set; }
    }

    public class GetPRApplicationCostItemResponse : CreateUpdatePRApplicationCostItemRequest
    {
        /// <summary>
        /// 支持项目Name
        /// </summary>
        public string SupportItemName { get; set; }
    }

    public class GetPRApplicationHcpTravelAgencyConferenceFeeResponse : CreateUpdateHcpTravelAgencyConferenceFeeRequest
    {
        /// <summary>
        /// 费用类型名称
        /// </summary>
        public string HcpTravelAgencyConferenceFeeTypeName { get; set; }
    }
}
