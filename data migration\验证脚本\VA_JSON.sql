IF OBJECT_ID(N'dbo.xml_23', N'U') IS NOT NULL
BEGIN
PRINT(N'已经初始化'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
select 
ProcInstId,
XmlContent.value('(/root/SupplierApplication_supplierInfoBlock_MainStore/ModifyInfo)[1]', 'nvarchar(500)') as ModifyInfo,
XmlContent.value('(/root/HcpLevelApplication_applicantBaseInfoBlock_MainStore/OldData)[1]', 'nvarchar(500)') as OldData ,
XmlContent.value('(/root/HcpLevelApplication_hiddenBlock_MainStore/OldLevel1)[1]', 'nvarchar(500)') as OldLevel1
into xml_23
from ODS_T_FORMINSTANCE_GLOBAL a  
where  FORM_Id in ('663dd63299be45d69dd8f853d0a4b445','7a708c9568fb444a884eb5eca658975f')
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END;

 SELECT 
        A.ProcInstId ,
        REPLACE(REPLACE(REPLACE(TRIM(value), CHAR(13), ''), CHAR(10), ''), CHAR(9), '') AS ModifyInfo,
        LTRIM(RTRIM(SUBSTRING(REPLACE(REPLACE(REPLACE(TRIM(value), CHAR(13), ''), CHAR(10), ''), CHAR(9), ''), 1, 
        	case when CHARINDEX('修改前:',REPLACE(REPLACE(REPLACE(TRIM(value), CHAR(13), ''), CHAR(10), ''), CHAR(9), ''))<>''
      			 then CHARINDEX('修改前:',REPLACE(REPLACE(REPLACE(TRIM(value), CHAR(13), ''), CHAR(10), ''), CHAR(9), '')) - 1 else 1 end ))) ModificationType,
        LTRIM(RTRIM(SUBSTRING(
                                    REPLACE(REPLACE(REPLACE(TRIM(value), CHAR(13), ''), CHAR(10), ''), CHAR(9), ''),
            case when CHARINDEX('修改前:',REPLACE(REPLACE(REPLACE(TRIM(value), CHAR(13), ''), CHAR(10), ''), CHAR(9), ''))<>''
      			 then CHARINDEX('修改前:', REPLACE(REPLACE(REPLACE(TRIM(value), CHAR(13), ''), CHAR(10), ''), CHAR(9), '')) + 4 else 1 end ,
            case when CHARINDEX('修改前:',REPLACE(REPLACE(REPLACE(TRIM(value), CHAR(13), ''), CHAR(10), ''), CHAR(9), ''))<>''
      			 then CHARINDEX(' 修改后:', REPLACE(REPLACE(REPLACE(TRIM(value), CHAR(13), ''), CHAR(10), ''), CHAR(9), '')) - CHARINDEX('修改前:', REPLACE(REPLACE(REPLACE(TRIM(value), CHAR(13), ''), CHAR(10), ''), CHAR(9), '')) - 3 else 1 end 
                                )))BeforeModification    
    into #xml1
    FROM xml_23 a
    CROSS APPLY STRING_SPLIT(A.ModifyInfo, ';')
    where A.ModifyInfo is not null and A.ModifyInfo<>'' and REPLACE(REPLACE(REPLACE(TRIM(value), CHAR(13), ''), CHAR(10), ''), CHAR(9), '') like N'%修改前:%' and  REPLACE(REPLACE(REPLACE(TRIM(value), CHAR(13), ''), CHAR(10), ''), CHAR(9), '') like N'%修改后：%'
;
 SELECT 
        A.ProcInstId ,A.OldData,a.OldLevel1,
        REPLACE(REPLACE(LTRIM(RTRIM(SUBSTRING(
                REPLACE(REPLACE(REPLACE(TRIM(value), CHAR(13), ''), CHAR(10), ''), CHAR(9), ''),
               1, value_1+2
            ))),':',''),'原','')  ModificationType,
        LTRIM(RTRIM(SUBSTRING(
                REPLACE(REPLACE(REPLACE(TRIM(value), CHAR(13), ''), CHAR(10), ''), CHAR(9), ''),
                CHARINDEX(':', REPLACE(REPLACE(REPLACE(TRIM(value), CHAR(13), ''), CHAR(10), ''), CHAR(9), '')) + 1,
                LEN(REPLACE(REPLACE(REPLACE(TRIM(value), CHAR(13), ''), CHAR(10), ''), CHAR(9), ''))
            )))BeforeModification    
into #xml11
from  
   ( select a.*,REPLACE(REPLACE(REPLACE(TRIM(value), CHAR(13), ''), CHAR(10), ''), CHAR(9), '') value,CHARINDEX(':', REPLACE(REPLACE(REPLACE(TRIM(value), CHAR(13), ''), CHAR(10), ''), CHAR(9), '')) -2 as value_1 FROM  xml_23 a
    join ODS_AUTO_BIZ_T_HcpLevelApplication_info b
    on  b.ProcInstId=a.ProcInstId
    CROSS APPLY STRING_SPLIT(A.OldData, ';')
    where A.OldData is not null and A.OldData<>'' and REPLACE(REPLACE(REPLACE(TRIM(value), CHAR(13), ''), CHAR(10), ''), CHAR(9), '')<>''
    and REPLACE(REPLACE(REPLACE(TRIM(value), CHAR(13), ''), CHAR(10), ''), CHAR(9), '') like '原%'
--   and  a.ProcInstId not in (select DISTINCT a.ProcInstId FROM xml_23 a
--    join ODS_AUTO_BIZ_T_HcpLevelApplication_info b
--    on  b.ProcInstId=a.ProcInstId
--    CROSS APPLY STRING_SPLIT(A.OldData, ';')
--    where REPLACE(REPLACE(REPLACE(TRIM(value), CHAR(13), ''), CHAR(10), ''), CHAR(9), '') <>'' and REPLACE(REPLACE(REPLACE(TRIM(value), CHAR(13), ''), CHAR(10), ''), CHAR(9), '') not like N'原%')
)A;
select 
ProcInstId
,max(ID) ID
,max(Mailbox)Mailbox
,max(HandPhone)HandPhone
,max(BankCity)BankCity
,max(City)City
,max(StandardHosDepName)StandardHosDepName
,max(BankCode)BankCode
,max(BankCardNo)BankCardNo
,max(IdentificationCard)IdentificationCard
,max(PTIName)PTIName
,max(CertificateCode)CertificateCode
,max(AcademicPosition)AcademicPosition
,max(HospitalName)HospitalName
,max(BankNo)BankNo
into #xml_2
from (
select 
xm.ProcInstId
,ID
,case when ModificationType=N'联系人邮箱' then BeforeModification  end as Mailbox--联系人邮箱
,case when ModificationType=N'电话' then BeforeModification end as HandPhone--电话
,case when ModificationType=N'银行城市' then BeforeModification end as BankCity--银行城市
,case when ModificationType=N'供应商城市' then BeforeModification end as City--供应商城市
,case when ModificationType=N'所属科室' then BeforeModification end as StandardHosDepName--所属科室
,case when ModificationType=N'银行名称' then BeforeModification end as BankCode--银行名称
,case when ModificationType=N'银行账号' then BeforeModification end as BankCardNo--银行账号
,case when ModificationType=N'身份证' then BeforeModification end as IdentificationCard--身份证
,case when ModificationType=N'职称' then BeforeModification end as PTIName --职称
,case when ModificationType=N'执业证书编号' then BeforeModification end as CertificateCode--执业证书编号
,case when ModificationType=N'讲者学会任职信息' then BeforeModification end as AcademicPosition--讲者学会任职信息
,case when ModificationType=N'所属医院' then BeforeModification end as HospitalName--所属医院
,case when ModificationType=N'收款银行联行号' then BeforeModification end as BankNo--收款银行联行号
from  #xml1 xm
join VendorApplications_Tmp vat
on xm.ProcInstId=vat.ProcInstId
) A
group by ProcInstId;

select 
ProcInstId
,id
,max(SPLevel) SPLevel
,max(StandardHosDepName)StandardHosDepName
,max(PTIName)PTIName
,max(AcademicPosition)AcademicPosition
,max(HospitalName)HospitalName
into #xml_21
from (
select 
xm.ProcInstId
,ID
,REPLACE(REPLACE(LTRIM(RTRIM(SUBSTRING(
                REPLACE(REPLACE(REPLACE(TRIM(OldLevel1), CHAR(13), ''), CHAR(10), ''), CHAR(9), ''),
                CHARINDEX('-', REPLACE(REPLACE(REPLACE(TRIM(OldLevel1), CHAR(13), ''), CHAR(10), ''), CHAR(9), '')) + 1,
                LEN(REPLACE(REPLACE(REPLACE(TRIM(OldLevel1), CHAR(13), ''), CHAR(10), ''), CHAR(9), ''))
            ))),'N/A',''),'GO (咨询OEC)','' ) as SPLevel
,case when ModificationType=N'所属科室' then BeforeModification end as StandardHosDepName--所属科室
,case when ModificationType=N'职称' then BeforeModification end as PTIName --职称
,case when ModificationType=N'讲者学会任职信息' then BeforeModification end as AcademicPosition--讲者学会任职信息
,case when ModificationType=N'所属医院' then BeforeModification end as HospitalName--所属医院
from  #xml11 xm
join VendorApplications_Tmp vat
on xm.ProcInstId=vat.ProcInstId
) A
group by ProcInstId,id;

--select * from #xml_3

--drop table  #xml_21

--drop table #xml_2

select 
ProcInstId
,id
,Mailbox
,HandPhone
,COALESCE(cast(d2.spk_provincialadministrativecode as nvarchar(20)),cast(d3.spk_provincialadministrativecode as nvarchar(20)))BankProvince
,COALESCE(cast(c2.spk_cityadministrativedivisioncode as  nvarchar(20)),cast(c3.spk_cityadministrativedivisioncode as  nvarchar(20)))BankCity
,COALESCE(cast(d2.spk_name as nvarchar(20)),cast(d3.spk_name as nvarchar(20)))BankProvinceName
,COALESCE(cast(c2.spk_name as  nvarchar(20)),cast(c3.spk_name as  nvarchar(20)))BankCityName
,COALESCE(cast(d.spk_provincialadministrativecode as nvarchar(20)),cast(d1.spk_provincialadministrativecode as nvarchar(20))) Province
,COALESCE(cast(c.spk_cityadministrativedivisioncode as  nvarchar(20)),cast(c1.spk_cityadministrativedivisioncode as  nvarchar(20))) City
,StandardHosDepName
,BankCode
,BankCardNo
,IdentificationCard
,PTIName
,CertificateCode
,AcademicPosition
,HospitalName
,BankNo
into #xml_3
from #xml_2 a
left join PLATFORM_ABBOTT.dbo.spk_city c
on SUBSTRING(a.City,1,3) =SUBSTRING(c.spk_name,1,3)
left join PLATFORM_ABBOTT.dbo.spk_city c1
on SUBSTRING(a.City,1,2) =SUBSTRING(c1.spk_name,1,2) and c.spk_provincenamename is null  and SUBSTRING(c1.spk_name,1,2)<>N'张家'
left join PLATFORM_ABBOTT.dbo.spk_province d
on c.spk_provincenamename = d.spk_name 
left join PLATFORM_ABBOTT.dbo.spk_province d1
on c1.spk_provincenamename = d1.spk_name 
left join PLATFORM_ABBOTT.dbo.spk_city c2
on SUBSTRING(a.BankCity,1,3) =SUBSTRING(c2.spk_name,1,3)
left join PLATFORM_ABBOTT.dbo.spk_city c3
on SUBSTRING(a.BankCity,1,2) =SUBSTRING(c3.spk_name,1,2) and c2.spk_provincenamename is null  and SUBSTRING(c3.spk_name,1,2)<>N'张家'
left join PLATFORM_ABBOTT.dbo.spk_province d2
on c2.spk_provincenamename = d2.spk_name 
left join PLATFORM_ABBOTT.dbo.spk_province d3
on c3.spk_provincenamename = d3.spk_name ;


update a set a.UpdatePreJson=b.UpdatePreJson from  VendorApplications  a
join (
select va.id ,va.ApplicationType,va.ApplicationCode,
CONCAT(
    '{',
        '"ID":"',COALESCE(vt.id,'********-0000-0000-0000-********0000'),'",',
        '"IsDraft": false ,',
        '"ApplicationId": "********-0000-0000-0000-********0000" ,',
        '"DraftVersion": 1,',
        '"SignedStatus":false ,',
        '"SignedStatusString": null,',
        '"SignedVersion": null,',
        '"Status": ',va.ApplicationType,',', --激活则为3，变更为2
        '"StatusString":null ,',
        '"VendorCode": "',va.VendorCode ,'",',
        '"SPName": "',vap.SPName ,'",', --之前填错成科室名字了
        '"PTId": "',va.PTId ,'",',
        '"PTIName": "',COALESCE(xm.PTIName,va.PTName) ,'",',
        '"HospitalId": "',va.HospitalId ,'",',
        '"HospitalName": "',COALESCE(xm.HospitalName,va.HospitalName ),'",',
        '"StandardHosDepId": "',va.StandardHosDepId ,'",',
        '"StandardHosDepName": "',va.StandardHosDepName ,'",',
        '"HosDepartment": "',COALESCE(xm.StandardHosDepName,va.HosDepartment) ,'",',
        '"EpdId": "',va.EpdId ,'",',
        '"CertificateCode": "',COALESCE(xm.CertificateCode,va.CertificateCode ),'",',
        '"SPLevel": "',va.SPLevel,'",',
        '"AcademicLevel": "',va.AcademicLevel ,'",',
        '"AcademicPosition": "',COALESCE(xm.AcademicPosition,va.AcademicPosition ),'",',
        '"HandPhone": "',COALESCE(xm.HandPhone,va.HandPhone),'",',
        '"CardPic": {',
            '"AttachmentId": "********-0000-0000-0000-********0000",',
            '"FileName": null,',
            '"Size": null,',
            '"Suffix": null,',
	    '"FilePath": null',
        '},',
        '"CardType": "',vap.CardType,'",',
        '"Sex": "',vap.Sex,'",',
        '"CardNo": "',COALESCE(xm.IdentificationCard,vap.CardNo),'",',
	'"Province": "',
	COALESCE(xm.Province,vap.Province,vao.Province,'000000') 
	,'",',
	'"City": "',
	COALESCE(xm.City,vap.City,vao.City,'000000') 
	,'",',
        '"ProvinceCity": [',
		COALESCE(xm.Province,vap.Province,vao.Province,'000000')       
        	,',',
		COALESCE(xm.City,vap.City,vao.City,'000000') 
        	,'],',--要按照上面的两个code拼接成形如["Code1","Code2"]的值，如果上面为空则为[null,null]或["Code1",null]
        N'"ProvinceCityName": "',sp.spk_name,'/',sc.spk_name,'",',--要根据省份和城市代码，到省份/城市主数据查询出对应的名称拼接字符串，形如"浙江省/杭州市"，如果上述代码为空则留作空，形如"浙江省/"或"/"
        '"PostCode": "',
	COALESCE(vap.PostCode,vao.PostCode) 
	,'",',
        '"Address": "',
	COALESCE(vap.Address,vao.RegCertificateAddress) 
	,'",',
        '"BankCode": "',COALESCE(xm.BankCode,va.BankCode),'",',
        '"BankCardNo": "',COALESCE(xm.BankCardNo,va.BankCardNo) ,'",',
        '"BankCity": ["',
--        LEFT(va.BankCity, CHARINDEX(',', va.BankCity) - 1)
        COALESCE(xm.BankProvince,LEFT(va.BankCity, CHARINDEX(',', va.BankCity) - 1),'000000')
        ,'","',
--        SUBSTRING(va.BankCity, CHARINDEX(',', va.BankCity) + 1, LEN(va.BankCity))
        COALESCE(xm.BankCity,SUBSTRING(va.BankCity, CHARINDEX(',', va.BankCity) + 1, LEN(va.BankCity)),'000000')
        ,'"],',--要按照目前填入的bankcity包含的省市编码，拼接成形如["Code1","Code2"]的值，如果为空则为[null,null]或["Code1",null]
        N'"BankCityName": "',
        xm.BankProvinceName
--        sp1.spk_name
        ,'/',
--        sc1.spk_name
         xm.BankCityName
        ,'",',--要根据省份和城市代码，到省份/城市主数据查询出对应的名称拼接字符串，形如"浙江省/杭州市"，如果上述代码为空则留作空，形如"浙江省/"或"/"
        '"BankNo": "',COALESCE(xm.BankNo,va.BankNo),'",',
        '"BankCardImg": null,',
        '"FinancialInformation": [',
            '{',
                '"ID": "',vaf.ID,'",',
                '"Company": "',vaf.Company,'",',
		'"CompanyName": "',vaf.Company,'",',
		'"Currency": "',vaf.Currency,'",',
		'"CurrencyName": "',vaf.Currency,'",',
		'"VendorCode": "',vaf.VendorCode,'",',
		'"AbbottBank": "',vaf.AbbottBank,'",',
		'"VendorType": "',vaf.VendorType,'",',
		'"Division": "',vaf.Division,'",',
		'"PayType": "',vaf.PayType,'",',
		'"PaymentTerm": "',vaf.PaymentTerm,'",',
		'"DpoCategory": null, ',--疑似比对新旧数据时没有用到这个字段，先留空，目前看到正常操作产生的数据也是空
		'"SpendingCategory": null, ',
		'"CountryCode": "',vaf.CountryCode,'",',
		'"BankType": "',vaf.BankType,'",',
		'"BankNo": null, ',
		'"PaymentTerm": "',vaf.PaymentTerm,'",',
		'"PaymentTermName": "',vaf.PaymentTerm,'_',bvt.vtmddy,N'天",',--这里要查AVT拿到天数，拼接成字符串
		'"FinancialVendorStatus": "',case when vaf.FinancialVendorStatus=2 then 4 
		when vaf.FinancialVendorStatus=3 then 3 end ,'",',--如果为激活此处应该是4-失效(此时VendorApplicationFinancials里面对应是2-待激活)；如果为变更此处应该是3-生效，与VendorApplicationFinancials保持一致
            '}',
        '],',
        '"AttachmentInformation": [],',
        '"DPSCheck": []',
    '}'
) UpdatePreJson
from VendorApplications va 
left join Vendors vt 
on va.VendorCode =vt.VendorCode 
left join VendorApplicationFinancials vaf
on va.id = vaf.applicationid
left join VendorApplicationPersonals vap
on va.id = vap.applicationid
left join VendorApplicationOrgnizations  vao
on va.id = vao.applicationid
left join spk_city sc 
on COALESCE(vap.city,vao.city)=sc.spk_cityadministrativedivisioncode
left join spk_province sp 
on COALESCE(vap.Province,vao.Province)=sp.spk_provincialadministrativecode
left join spk_city sc1 
on  SUBSTRING(va.BankCity, CHARINDEX(',', va.BankCity) + 1, LEN(va.BankCity))=cast(sc1.spk_cityadministrativedivisioncode as nvarchar(20))
left join spk_province sp1
on LEFT(va.BankCity, CHARINDEX(',', va.BankCity) - 1)=cast(sp1.spk_provincialadministrativecode as nvarchar(20))
left join ODS_TMP_AVT   bvt
on vaf.PaymentTerm = bvt.Vterm and vaf.Company = bvt.Vcmpy
left join  #xml_3  xm
on xm.id=va.id
where va.ApplicationType =3
union 
select va.id ,va.ApplicationType,va.ApplicationCode,
CONCAT(
    '{',
        '"ID":"',COALESCE(vt.id,'********-0000-0000-0000-********0000'),'",',
        '"IsDraft": false ,',
        '"ApplicationId": "********-0000-0000-0000-********0000" ,',
        '"DraftVersion": 1,',
        '"SignedStatus":false ,',
        '"SignedStatusString": null,',
        '"SignedVersion": null,',
        '"Status": ',va.ApplicationType,',', --激活则为3，变更为2
        '"StatusString":null ,',
        '"VendorCode": "',va.VendorCode ,'",',
        '"SPName": "',vap.SPName ,'",', --之前填错成科室名字了
        '"PTId": "',va.PTId ,'",',
        '"PTIName": "',COALESCE(xm.PTIName,va.PTName) ,'",',
        '"HospitalId": "',va.HospitalId ,'",',
        '"HospitalName": "',COALESCE(xm.HospitalName,va.HospitalName) ,'",',
        '"StandardHosDepId": "',va.StandardHosDepId ,'",',
        '"StandardHosDepName": "',va.StandardHosDepName ,'",',
        '"HosDepartment": "',COALESCE(xm.StandardHosDepName,va.HosDepartment) ,'",',
        '"EpdId": "',va.EpdId ,'",',
        '"CertificateCode": "',va.CertificateCode ,'",',
        '"SPLevel": "',COALESCE(xm.SPLevel,va.SPLevel),'",',
        '"AcademicLevel": "',va.AcademicLevel ,'",',
        '"AcademicPosition": "',COALESCE(xm.AcademicPosition,va.AcademicPosition) ,'",',
        '"HandPhone": "',va.HandPhone,'",',
        '"CardPic": {',
            '"AttachmentId": "********-0000-0000-0000-********0000",',
            '"FileName": null,',
            '"Size": null,',
            '"Suffix": null,',
	    '"FilePath": null',
        '},',
        '"CardType": "',vap.CardType,'",',
        '"Sex": "',vap.Sex,'",',
        '"CardNo": "',vap.CardNo,'",',
	'"Province": "',
	COALESCE(vap.Province,vao.Province,'000000') 
	,'",',
	'"City": "',
	COALESCE(vap.City,vao.City,'000000') 
	,'",',
        '"ProvinceCity": [',
		COALESCE(vap.Province,vao.Province,'000000')       
        	,',',
		COALESCE(vap.City,vao.City,'000000') 
        	,'],',--要按照上面的两个code拼接成形如["Code1","Code2"]的值，如果上面为空则为[null,null]或["Code1",null]
        N'"ProvinceCityName": "',sp.spk_name,'/',sc.spk_name,'",',--要根据省份和城市代码，到省份/城市主数据查询出对应的名称拼接字符串，形如"浙江省/杭州市"，如果上述代码为空则留作空，形如"浙江省/"或"/"
        '"PostCode": "',
	COALESCE(vap.PostCode,vao.PostCode) 
	,'",',
        '"Address": "',
	COALESCE(vap.Address,vao.RegCertificateAddress) 
	,'",',
        '"BankCode": "',va.BankCode,'",',
        '"BankCardNo": "',va.BankCardNo ,'",',
        '"BankCity": ["',
--        LEFT(va.BankCity, CHARINDEX(',', va.BankCity) - 1)
        COALESCE(LEFT(va.BankCity, CHARINDEX(',', va.BankCity) - 1),'000000')
        ,'","',
--        SUBSTRING(va.BankCity, CHARINDEX(',', va.BankCity) + 1, LEN(va.BankCity))
        COALESCE(SUBSTRING(va.BankCity, CHARINDEX(',', va.BankCity) + 1, LEN(va.BankCity)),'000000')
        ,'"],',--要按照目前填入的bankcity包含的省市编码，拼接成形如["Code1","Code2"]的值，如果为空则为[null,null]或["Code1",null]
        N'"BankCityName": "',
        sp1.spk_name,
        '/'
       , sc1.spk_name
        ,'",',--要根据省份和城市代码，到省份/城市主数据查询出对应的名称拼接字符串，形如"浙江省/杭州市"，如果上述代码为空则留作空，形如"浙江省/"或"/"
        '"BankNo": "',va.BankNo,'",',
        '"BankCardImg": null,',
        '"FinancialInformation": [',
            '{',
                '"ID": "',vaf.ID,'",',
                '"Company": "',vaf.Company,'",',
		'"CompanyName": "',vaf.Company,'",',
		'"Currency": "',vaf.Currency,'",',
		'"CurrencyName": "',vaf.Currency,'",',
		'"VendorCode": "',vaf.VendorCode,'",',
		'"AbbottBank": "',vaf.AbbottBank,'",',
		'"VendorType": "',vaf.VendorType,'",',
		'"Division": "',vaf.Division,'",',
		'"PayType": "',vaf.PayType,'",',
		'"PaymentTerm": "',vaf.PaymentTerm,'",',
		'"DpoCategory": null, ',--疑似比对新旧数据时没有用到这个字段，先留空，目前看到正常操作产生的数据也是空
		'"SpendingCategory": null, ',
		'"CountryCode": "',vaf.CountryCode,'",',
		'"BankType": "',vaf.BankType,'",',
		'"BankNo": null, ',
		'"PaymentTerm": "',vaf.PaymentTerm,'",',
		'"PaymentTermName": "',vaf.PaymentTerm,'_',bvt.vtmddy,N'天",',--这里要查AVT拿到天数，拼接成字符串
		'"FinancialVendorStatus": "',case when vaf.FinancialVendorStatus=2 then 4 
		when vaf.FinancialVendorStatus=3 then 3 end ,'",',--如果为激活此处应该是4-失效(此时VendorApplicationFinancials里面对应是2-待激活)；如果为变更此处应该是3-生效，与VendorApplicationFinancials保持一致
            '}',
        '],',
        '"AttachmentInformation": [],',
        '"DPSCheck": []',
    '}'
) UpdatePreJson
from VendorApplications va 
left join Vendors vt 
on va.VendorCode =vt.VendorCode 
left join VendorApplicationFinancials vaf
on va.id = vaf.applicationid
left join VendorApplicationPersonals vap
on va.id = vap.applicationid
left join VendorApplicationOrgnizations  vao
on va.id = vao.applicationid
left join spk_city sc 
on COALESCE(vap.city,vao.city)=sc.spk_cityadministrativedivisioncode
left join spk_province sp 
on COALESCE(vap.Province,vao.Province)=sp.spk_provincialadministrativecode
left join spk_city sc1 
on  SUBSTRING(va.BankCity, CHARINDEX(',', va.BankCity) + 1, LEN(va.BankCity))=cast(sc1.spk_cityadministrativedivisioncode as nvarchar(20))
left join spk_province sp1
on LEFT(va.BankCity, CHARINDEX(',', va.BankCity) - 1)=cast(sp1.spk_provincialadministrativecode as nvarchar(20))
left join ODS_TMP_AVT   bvt
on vaf.PaymentTerm = bvt.Vterm and vaf.Company = bvt.Vcmpy
left join  #xml_21  xm
on xm.id=va.id
where va.ApplicationType =2
) b
on a.id=b.id
where a.ApplicationType in (2,3)