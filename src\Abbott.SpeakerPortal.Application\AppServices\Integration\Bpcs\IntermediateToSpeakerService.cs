﻿using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Integration.Dspot;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Dataverse.Contracts;
using Abbott.SpeakerPortal.Entities.Integration.Dspot;
using Abbott.SpeakerPortal.Entities.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Entities.VendorFinancials;
using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.Enums.Integration;
using Abbott.SpeakerPortal.IntermediateDB.Models;
using Abbott.SpeakerPortal.IntermediateDB.Repositories;
using Abbott.SpeakerPortal.Redis;
using Abbott.SpeakerPortal.Utils;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Xrm.Sdk;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using VoloEntities = Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Microsoft.Crm.Sdk.Messages;
using Abbott.SpeakerPortal.AppServices.Integration.Bpcs;
using Abbott.SpeakerPortal.Contracts.Integration.Bpcs;
using System.Runtime.Intrinsics.X86;
using Senparc.Weixin.WxOpen.Entities;
using Abbott.SpeakerPortal.Contracts.Common.Common;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.Common.Log;
using Abbott.SpeakerPortal.Entities.Common.ScheduleJob;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.AppServices.Common;
using Abbott.SpeakerPortal.Enums;
using EFCore.BulkExtensions;
using Newtonsoft.Json;
using Volo.Abp.Guids;
using System.Net.NetworkInformation;

namespace Abbott.SpeakerPortal.AppServices.Integration.Dspot
{
    public class IntermediateToSpeakerService : SpeakerPortalAppService, IIntermediateToSpeakerService
    {
        /// <summary>
        /// The service provider
        /// </summary>
        private readonly IServiceProvider _serviceProvider;

        /// <summary>
        /// The logger
        /// </summary>
        private readonly ILogger<IntermediateToSpeakerService> _logger;

        /// <summary>
        /// The configuration
        /// </summary>
        private readonly IConfiguration _configuration;

        /// <summary>
        /// redis
        /// </summary>
        private readonly IRedisRepository _redisRepository;

        /// <summary>
        /// dataverse
        /// </summary>
        private IDataverseRepository _dataverseRepository;

        /// <summary>
        /// The dataverseService
        /// </summary>
        private readonly IDataverseService _dataverseService;

        private readonly IScheduleJobLogService _jobLogService;

        //private readonly SpeakerPortalIntermediateDbContext _intermediateDbContext;

        public IntermediateToSpeakerService(IServiceProvider serviceProvider, IRedisRepository redisRepository)
        {
            _serviceProvider = serviceProvider;
            _logger = serviceProvider.GetService<ILogger<IntermediateToSpeakerService>>();
            _configuration = serviceProvider.GetService<IConfiguration>();
            _redisRepository = redisRepository;
            _dataverseRepository = _serviceProvider.GetService<IDataverseRepository>();
            _dataverseService = serviceProvider.GetService<IDataverseService>();
            _jobLogService = serviceProvider.GetService<IScheduleJobLogService>();
        }

        public async Task<Dictionary<string, string>> SyncTablesConfigs()
        {
            var result = new Dictionary<string, string>();
            result.Add("Abk", await SyncTableAbk());
            result.Add("Avt", await SyncTableAvt());
            result.Add("Gcc", await SyncTableGcc());
            result.Add("Zcc", await SyncTableZcc());
            result.Add("Zpa", await SyncTableZpa());
            result.Add("Zrc", await SyncTableZrc());
            result.Add("Gcr", await SyncTableGcr());
            return result;
        }

        public async Task<Dictionary<string, string>> SyncTablesVendors()
        {
            var result = new Dictionary<string, string>();
            result.Add("AvmPmfvm", await SyncTableAvmPmfvm());
            return result;
        }

        public async Task<Dictionary<string, string>> SyncTablesAph()
        {
            var result = new Dictionary<string, string>();
            result.Add("Aph", await SyncTableAph());
            return result;
        }

        public async Task<Dictionary<string, string>> SyncTablesAml()
        {
            var result = new Dictionary<string, string>();
            var log = _jobLogService.InitSyncLog("BpcsSyncTables", "AML");
            try
            {
                //result.Add("Aml", await SyncTableAml());
                var toService = _serviceProvider.GetService<IIntermediateToSpeakerAmlService>();
                log.RecordCount = await toService.SyncTableIncrement();
                result.Add("Aml", null);
            }
            catch (Exception ex)
            {
                log.IsSuccess = false;
                log.Remark = ex.ToString();
                result.Add("Aml", ex.Message);
            }
            finally
            {
                _jobLogService.SyncLog(log);
            }

            return result;
        }

        public async Task<Dictionary<string, string>> SyncTablesGlh()
        {
            var result = new Dictionary<string, string>();
            var log = _jobLogService.InitSyncLog("BpcsSyncTables", "GLH");
            try
            {
                //result.Add("Glh", await SyncTableGlh());
                //result.Add("Glh", await SyncTableByType<Glh, BpcsGlh, IIntermediateGlhRepository, IBpcsGlhRepository>(GetBpcsGlhByKey, true));
                var toService = _serviceProvider.GetService<IIntermediateToSpeakerGlhService>();
                log.RecordCount = await toService.SyncTableIncrement();
                result.Add("Glh", null);
            }
            catch (Exception ex)
            {
                log.IsSuccess = false;
                log.Remark = ex.ToString();
                result.Add("Glh", ex.Message);
            }
            finally
            {
                _jobLogService.SyncLog(log);
            }
            return result;
        }

        private BpcsGlh GetBpcsGlhByKey(List<BpcsGlh> bpcsGlhs, Glh ori)
        {
            if (bpcsGlhs?.Any() != true)
            {
                return default;
            }

            return bpcsGlhs.FirstOrDefault(a => a.Lhldgr == ori.Lhldgr
                        && a.Lhbook == ori.Lhbook
                        && a.Lhyear == ori.Lhyear
                        && a.Lhperd == ori.Lhperd
                        && a.Lhjnen == ori.Lhjnen
                        && a.Lhjnln == ori.Lhjnln);
        }

        public async Task<Dictionary<string, string>> SyncTables()
        {
            var result = new Dictionary<string, string>();
            //result.Concat(await SyncTablesConfigs());
            result.Concat(await SyncTablesVendors());
            //result.Concat(await SyncTablesAph());
            //result.Concat(await SyncTablesAml());
            //result.Concat(await SyncTablesGlh());

            return result;
        }

        public async Task<string> SyncTableAbk()
        {
            var log = _jobLogService.InitSyncLog("BpcsSyncTables", "ABK");
            try
            {
                //查询
                var repoAbk = LazyServiceProvider.GetService<IIntermediateAbkRepository>();
                var query = await repoAbk.GetQueryableAsync();
                var listAbk = query.Where(a => 1 == 1).ToList();
                log.RecordCount = listAbk.Count;
                if (listAbk?.Any() != true)
                {
                    return "listAbk is null";
                }

                var repoBpcsAbk = LazyServiceProvider.GetService<IBpcsAbkRepository>();
                //先清空
                await repoBpcsAbk.DeleteDirectAsync(a => 1 == 1);
                //插入
                await repoBpcsAbk.InsertManyAsync(ObjectMapper.Map<List<Abk>, List<BpcsAbk>>(listAbk), true);

                #region 检查公司及货币并同步至PP

                //将公司相关币种同步至PP
                var newList = ObjectMapper.Map<List<Abk>, List<BpcsAbk>>(listAbk);
                var groupList = newList.Select(s => new { s.Bcurr, s.Bkcmpy }).Distinct().ToList();
                //获取ABP已有币种
                var currencyList = await _dataverseService.GetDictionariesAsync(DictionaryType.Currency);
                //判断bpcs币种是否存在于PP中
                var newCurrency = groupList.Where(w => !currencyList.Select(s => s.Code).Contains(w.Bcurr)).Select(s => s.Bcurr).Distinct().ToList();
                Dictionary<string, Guid> keyValues = [];
                if (newCurrency.Count != 0)
                {
                    //添加新币种
                    foreach (var currency in newCurrency)
                    {
                        var currencyDic = new Entity("spk_dictionary");
                        currencyDic["spk_type"] = "币种";
                        currencyDic["spk_parentcode"] = "Currency";
                        currencyDic["spk_code"] = currency;
                        currencyDic["spk_name"] = currency;
                        var newId = await _dataverseRepository.DataverseClient.CreateAsync(currencyDic);
                        keyValues.Add(currency, newId);
                    }
                }
                //获取ABP已有公司币种关系
                var companyCurrency = await _dataverseService.GetCompanyCurrencyList();
                //获取PP所有公司
                var allCompany = _dataverseService.GetCompanyList().Result.Select(s => s.CompanyCode).ToList();
                foreach (var item in groupList)
                {
                    if (!allCompany.Contains(item.Bkcmpy.ToString())) continue;
                    if (!companyCurrency.Any(f => f.CompanyCode == item.Bkcmpy.ToString() && f.Code == item.Bcurr))
                    {
                        //获取公司ID
                        var companyId = companyCurrency.First(f => f.CompanyCode == item.Bkcmpy.ToString())?.CompanyId;
                        var currencyId = Guid.Empty;
                        //缓存中获取币种ID
                        if (currencyList.FirstOrDefault(f => f.Code == item.Bcurr)?.Id != null)
                        {
                            currencyId = currencyList.FirstOrDefault(f => f.Code == item.Bcurr).Id;
                        }
                        else
                        {
                            //缓存没有，则可能为新增，从keyValues中获取ID
                            keyValues.TryGetValue(item.Bcurr, out currencyId);
                        }
                        //添加公司币种关系
                        if (companyId == null || currencyId == Guid.Empty) continue;
                        var companyCurrencyNew = new Entity("spk_companycurrency");
                        companyCurrencyNew["spk_company"] = new EntityReference("spk_companymasterdata", companyId.Value);
                        companyCurrencyNew["spk_currency"] = new EntityReference("spk_dictionary", currencyId);
                        await _dataverseRepository.DataverseClient.CreateAsync(companyCurrencyNew);
                    }
                }

                #endregion 检查公司及货币并同步至PP
            }
            catch (Exception ex)
            {
                log.IsSuccess = false;
                log.Remark = ex.ToString();
                _logger.LogError($"SyncTableAbk() Exception: {ex}");
                return ex.Message;
            }
            finally
            {
                _jobLogService.SyncLog(log);
            }
            return null;
        }

        public async Task<string> SyncTableAvt()
        {
            var log = _jobLogService.InitSyncLog("BpcsSyncTables", "AVT");
            try
            {
                //查询
                var repoAvt = LazyServiceProvider.GetService<IIntermediateAvtRepository>();
                var query = await repoAvt.GetQueryableAsync();
                var listAvt = query.Where(a => 1 == 1).ToList();
                log.RecordCount = listAvt.Count;
                if (listAvt?.Any() != true)
                {
                    return "listAvt is null";
                }

                var repoBpcsAvt = LazyServiceProvider.GetService<IBpcsAvtRepository>();
                //先清空
                await repoBpcsAvt.DeleteDirectAsync(a => 1 == 1);
                //插入
                var guidGenerator = LazyServiceProvider.LazyGetService<IGuidGenerator>();
                var datas = ObjectMapper.Map<List<Avt>, List<BpcsAvt>>(listAvt);
                datas.ForEach(a => a.SetId(guidGenerator.Create()));
                var context = await repoBpcsAvt.GetDbContextAsync();
                await context.BulkInsertAsync(datas);
            }
            catch (Exception ex)
            {
                log.IsSuccess = false;
                log.Remark = ex.ToString();
                _logger.LogError($"SyncTableAvt() Exception: {ex}");
                return ex.Message;
            }
            finally
            {
                _jobLogService.SyncLog(log);
            }

            return null;
        }

        public async Task<string> SyncTableGcc()
        {
            var log = _jobLogService.InitSyncLog("BpcsSyncTables", "GCC");
            try
            {
                //查询
                var repoGcc = LazyServiceProvider.GetService<IIntermediateGccRepository>();
                var query = await repoGcc.GetQueryableAsync();
                var listGcc = query.Where(a => 1 == 1).ToList();
                log.RecordCount = listGcc.Count;
                if (listGcc?.Any() != true)
                {
                    return "listGcc is null";
                }

                var repoBpcsGcc = LazyServiceProvider.GetService<IBpcsGccRepository>();

                //先清空
                await repoBpcsGcc.DeleteDirectAsync(a => 1 == 1);
                //插入
                var guidGenerator = LazyServiceProvider.LazyGetService<IGuidGenerator>();
                var datas = ObjectMapper.Map<List<Gcc>, List<BpcsGcc>>(listGcc);
                datas.ForEach(a => a.SetId(guidGenerator.Create()));
                var context = await repoBpcsGcc.GetDbContextAsync();
                await context.BulkInsertAsync(datas);

                //if (listGcc.Count <= INSERT_BATCH)
                //{
                //    listGcc
                //}
                ////int didCount = 0;
                ////while (didCount < listGcc.Count)
                ////{
                ////    var repoBpcsGcc1 = LazyServiceProvider.GetService<IBpcsGccRepository>();
                ////    var tempGcc = listGcc.Skip(didCount).Take(INSERT_BATCH).ToList();
                ////    //插入
                ////    await repoBpcsGcc1.InsertManyAsync(ObjectMapper.Map<List<Gcc>, List<BpcsGcc>>(tempGcc));

                ////    didCount += INSERT_BATCH;
                ////}
            }
            catch (Exception ex)
            {
                log.IsSuccess = false;
                log.Remark = ex.ToString();
                _logger.LogError($"SyncTableGcc() Exception: {ex}");
                return ex.Message;
            }
            finally
            {
                _jobLogService.SyncLog(log);
            }

            return null;
        }

        public async Task<string> SyncTableZcc()
        {
            var log = _jobLogService.InitSyncLog("BpcsSyncTables", "ZCC");
            try
            {
                //查询
                var repoZcc = LazyServiceProvider.GetService<IIntermediateZccRepository>();
                var query = await repoZcc.GetQueryableAsync();
                var listZcc = query.Where(a => 1 == 1).ToList();
                log.RecordCount = listZcc.Count;
                if (listZcc?.Any() != true)
                {
                    return "listZcc is null";
                }

                var repoBpcsZcc = LazyServiceProvider.GetService<IBpcsZccRepository>();
                //先清空
                await repoBpcsZcc.DeleteDirectAsync(a => 1 == 1);
                //插入
                var guidGenerator = LazyServiceProvider.LazyGetService<IGuidGenerator>();
                var datas = ObjectMapper.Map<List<Zcc>, List<BpcsZcc>>(listZcc);
                datas.ForEach(a => a.SetId(guidGenerator.Create()));
                var context = await repoBpcsZcc.GetDbContextAsync();
                await context.BulkInsertAsync(datas);
            }
            catch (Exception ex)
            {
                log.IsSuccess = false;
                log.Remark = ex.ToString();
                _logger.LogError($"SyncTableZcc() Exception: {ex}");
                return ex.Message;
            }
            finally
            {
                _jobLogService.SyncLog(log);
            }
            return null;
        }

        public async Task<string> SyncTableZpa()
        {
            var log = _jobLogService.InitSyncLog("BpcsSyncTables", "ZPA");
            try
            {
                //查询
                var repoZpa = LazyServiceProvider.GetService<IIntermediateZpaRepository>();
                var query = await repoZpa.GetQueryableAsync();
                var listZpa = query.Where(a => 1 == 1).ToList();
                log.RecordCount = listZpa.Count;
                if (listZpa?.Any() != true)
                {
                    return "listZpa is null";
                }

                var repoBpcsZpa = LazyServiceProvider.GetService<IBpcsZpaRepository>();
                //先清空
                await repoBpcsZpa.DeleteDirectAsync(a => 1 == 1);
                //插入
                var guidGenerator = LazyServiceProvider.LazyGetService<IGuidGenerator>();
                var datas = ObjectMapper.Map<List<Zpa>, List<BpcsZpa>>(listZpa);
                datas.ForEach(a => a.SetId(guidGenerator.Create()));
                var context = await repoBpcsZpa.GetDbContextAsync();
                await context.BulkInsertAsync(datas);
            }
            catch (Exception ex)
            {
                log.IsSuccess = false;
                log.Remark = ex.ToString();
                _logger.LogError($"SyncTableZpa() Exception: {ex}");
                return ex.Message;
            }
            finally
            {
                _jobLogService.SyncLog(log);
            }

            return null;
        }

        public async Task<string> SyncTableZrc()
        {
            var log = _jobLogService.InitSyncLog("BpcsSyncTables", "ZRC");
            try
            {
                //查询
                var repoZrc = LazyServiceProvider.GetService<IIntermediateZrcRepository>();
                var query = await repoZrc.GetQueryableAsync();
                var listZrc = query.Where(a => 1 == 1).ToList();
                log.RecordCount = listZrc.Count;
                if (listZrc?.Any() != true)
                {
                    return "listZrc is null";
                }

                var repoBpcsZrc = LazyServiceProvider.GetService<IBpcsZrcRepository>();
                //先清空
                await repoBpcsZrc.DeleteDirectAsync(a => 1 == 1);
                //插入
                var guidGenerator = LazyServiceProvider.LazyGetService<IGuidGenerator>();
                var datas = ObjectMapper.Map<List<Zrc>, List<BpcsZrc>>(listZrc);
                datas.ForEach(a => a.SetId(guidGenerator.Create()));
                var context = await repoBpcsZrc.GetDbContextAsync();
                await context.BulkInsertAsync(datas);
            }
            catch (Exception ex)
            {
                log.IsSuccess = false;
                log.Remark = ex.ToString();
                _logger.LogError($"SyncTableZrc() Exception: {ex}");
                return ex.Message;
            }
            finally
            {
                _jobLogService.SyncLog(log);
            }

            return null;
        }

        public async Task<string> SyncTableGcr()
        {
            var log = _jobLogService.InitSyncLog("BpcsSyncTables", "GCR");
            try
            {
                var toService = _serviceProvider.GetService<IIntermediateToSpeakerGcrService>();
                log.RecordCount = await toService.SyncTableFull();
                return null;
            }
            catch (Exception ex)
            {
                log.IsSuccess = false;
                log.Remark = ex.ToString();
                return ex.Message;
            }
            finally
            {
                _jobLogService.SyncLog(log);
            }

        }

        public async Task<string> SyncTableAvmPmfvm()
        {
            var log = _jobLogService.InitSyncLog("BpcsSyncTables", "Vendor_Avm join Pmfvm");
            try
            {
                var vendorFinancial = await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync();
                //查询Avm
                var repoAvm = LazyServiceProvider.GetService<IIntermediateAvmRepository>();
                var queryAvm = await repoAvm.GetQueryableAsync();
                var repoPmfvm = LazyServiceProvider.GetService<IIntermediatePmfvmRepository>();
                var queryPmfvm = await repoPmfvm.GetQueryableAsync();
                var listAvm = AddWheres(queryAvm).Join(queryPmfvm, l => new { Vnd = l.Vendor, Cmp = l.Vcmpny }, r => new { Vnd = r.Vnderx, Cmp = r.Vmcmpy }, (avm, pmfvm) => new { avm, pmfvm })
                    .ToList();
                log.RecordCount = listAvm.Count;
                if (listAvm?.Any() != true)
                {
                    return "listAph is null";
                }

                //1,查询有哪些供应商已存在表BpcsAvm+BpcsPmfvm
                var repoBpcsAvm = LazyServiceProvider.GetService<IBpcsAvmRepository>();
                var queryBpcsAvm = await repoBpcsAvm.GetQueryableAsync();
                var repoBpcsPmfvm = LazyServiceProvider.GetService<IBpcsPmfvmRepository>();
                var queryBpcsPmfvm = await repoBpcsPmfvm.GetQueryableAsync();
                var exitsBpcsAvm = queryBpcsAvm.Join(queryBpcsPmfvm, l => new { Vnd = l.Vendor, Cmp = l.Vcmpny }, r => new { Vnd = r.Vnderx, Cmp = r.Vmcmpy }, (bpcsAvm, bpcsPmfvm) => new { bpcsAvm, bpcsPmfvm })
                    .ToList();

                List<BpcsAvm> updateEntitiesBpcsAvm = new List<BpcsAvm>();
                List<BpcsPmfvm> updateEntitiesBpcsPmfvm = new List<BpcsPmfvm>();
                List<BpcsAvm> addEntitiesBpcsAvm = new List<BpcsAvm>();
                List<BpcsPmfvm> addEntitiesBpcsPmfvm = new List<BpcsPmfvm>();
                //2,已存在表BpcsAvm的，比较BpcsAvm+BpcsPmfvm是否有更新，有则Update，无则不保存
                foreach (var item in listAvm)
                {
                    var exitsEntity = exitsBpcsAvm.FirstOrDefault(a => a.bpcsAvm.Vendor == item.avm.Vendor && a.bpcsAvm.Vcmpny == item.avm.Vcmpny);
                    if (exitsEntity != null)
                    {
                        //比较BpcsAvm+BpcsPmfvm是否有更新，有则Update，无则不保存
                        if (!item.avm.PropertyEqualAll(exitsEntity.bpcsAvm))
                        {
                            var updateEntity = item.avm.AssignPropertiesTo(exitsEntity.bpcsAvm);
                            if (updateEntity != null)
                            {
                                updateEntitiesBpcsAvm.Add(updateEntity);
                            }
                        }
                        if (!item.pmfvm.PropertyEqualAll(exitsEntity.bpcsPmfvm))
                        {
                            var updateEntity = item.pmfvm.AssignPropertiesTo(exitsEntity.bpcsPmfvm);
                            if (updateEntity != null)
                            {
                                updateEntitiesBpcsPmfvm.Add(updateEntity);
                            }
                        }
                    }
                    else
                    {
                        //3,未存在表BpcsAvm的，Insert BpcsAvm+BpcsPmfvm
                        addEntitiesBpcsAvm.Add(ObjectMapper.Map<Avm, BpcsAvm>(item.avm));
                        addEntitiesBpcsPmfvm.Add(ObjectMapper.Map<Pmfvm, BpcsPmfvm>(item.pmfvm));
                    }
                }

                var context = await repoBpcsAvm.GetDbContextAsync();
                //更新
                if (updateEntitiesBpcsAvm.Any())
                {
                    await context.BulkUpdateAsync(updateEntitiesBpcsAvm);
                }
                if (updateEntitiesBpcsPmfvm.Any())
                {
                    await context.BulkUpdateAsync(updateEntitiesBpcsPmfvm);
                }

                //插入
                if (addEntitiesBpcsAvm.Any())
                {
                    //在插入主表BpcsAvm时，要对Speaker的供应商设置FinaId，修改主表时不修改FinaId
                    await SetFinaId(addEntitiesBpcsAvm);
                    addEntitiesBpcsAvm.ForEach(a => a.SetId(GuidGenerator.Create()));
                    await context.BulkInsertAsync(addEntitiesBpcsAvm);
                }
                if (addEntitiesBpcsPmfvm.Any())
                {
                    addEntitiesBpcsPmfvm.ForEach(a => a.SetId(GuidGenerator.Create()));
                    await context.BulkInsertAsync(addEntitiesBpcsPmfvm);
                }

                //在处理完新表的保存后，回写财务表
                await RewriteFina(updateEntitiesBpcsAvm, updateEntitiesBpcsPmfvm, addEntitiesBpcsAvm, addEntitiesBpcsPmfvm);

                #region 根据同步的数据判断讲者是否有效

                List<Guid> updateStatus = new List<Guid>();
                if (updateEntitiesBpcsAvm.Count != 0)
                {
                    foreach (var item in updateEntitiesBpcsAvm)
                    {
                        var exitsEntity = vendorFinancial.FirstOrDefault(f => f.Company == item.Vcmpny.ToString() && f.VendorCode == item.Vendor.ToString());
                        if (exitsEntity != null)
                        {
                            updateStatus.Add(exitsEntity.VendorId);
                        }
                    }
                }
                if (addEntitiesBpcsAvm.Count != 0)
                {
                    foreach (var item in addEntitiesBpcsAvm)
                    {
                        var exitsEntity = vendorFinancial.FirstOrDefault(f => f.Company == item.Vcmpny.ToString() && f.VendorCode == item.Vendor.ToString());
                        if (exitsEntity != null)
                        {
                            updateStatus.Add(exitsEntity.VendorId);
                        }
                    }
                }
                var vendorRepository = LazyServiceProvider.LazyGetService<IVendorRepository>();
                var vendorQuery = await vendorRepository.GetQueryableAsync();
                //需要更新状态的所有讲者
                var updateVendorList = vendorQuery.Where(w => updateStatus.Distinct().Contains(w.Id)).ToList();

                //查询讲者对应的所有账号是否全部失效,全部失效则讲者失效
                foreach (var item in updateVendorList)
                {
                    //获取讲者对应的所有供应商账号状态
                    var exitsList = vendorFinancial.Join(queryBpcsAvm, a => a.Id, b => b.FinaId, (a, b) => new { a.Id, a.VendorId, b.Vnstat }).Where(w => w.VendorId == item.Id).ToList();
                    //如果没有状态为A的账号，讲者改为失效，否则相反
                    if (exitsList.FirstOrDefault(f => f.Vnstat.Equals("A", StringComparison.CurrentCultureIgnoreCase)) == null)
                    {
                        item.Status = Enums.VendorStatus.Invalid;
                    }
                    else
                    {
                        item.Status = Enums.VendorStatus.Valid;
                    }
                }
                await context.BulkUpdateAsync(updateVendorList);

                #endregion 根据同步的数据判断讲者是否有效
            }
            catch (Exception ex)
            {
                log.IsSuccess = false;
                log.Remark = ex.ToString();
                _logger.LogError($"SyncTableAvmPmfvm() Exception: {ex}");
                return ex.Message;
            }
            finally
            {
                _jobLogService.SyncLog(log);
            }
            return null;
        }

        public async Task<string> SyncTableAph()
        {
            var log = _jobLogService.InitSyncLog("BpcsSyncTables", "APH");
            try
            {
                //查询Aph
                var repoAph = LazyServiceProvider.GetService<IIntermediateAphRepository>();
                var query = await repoAph.GetQueryableAsync();
                var listAph = query.Where(a => a.UpdateTime >= DateTime.Today.AddDays(-7)).ToList();
                log.RecordCount = listAph.Count;
                if (listAph?.Any() != true)
                {
                    return "listAph is null";
                }

                //1,查询有哪些Aph已存在表BpcsAph
                var apinvs = listAph.Select(a => a.Apinv).Distinct();
                var repoBpcsAph = LazyServiceProvider.GetService<IBpcsAphRepository>();
                var queryBpcsAph = await repoBpcsAph.GetQueryableAsync();
                var exitsBpcsAph = queryBpcsAph.Where(a => apinvs.Contains(a.Apinv)).ToList();

                List<BpcsAph> updateEntities = new List<BpcsAph>();
                List<BpcsAph> addEntities = new List<BpcsAph>();
                //2,已存在表BpcsAph的，比较BpcsAph是否有更新，有则Update，无则不保存
                foreach (var item in listAph)
                {
                    var exitsEntity = exitsBpcsAph.FirstOrDefault(a => a.Apinv == item.Apinv);
                    if (exitsEntity != null)
                        updateEntities.Add(exitsEntity);
                    else
                        //3,未存在表BpcsAph的，Insert BpcsAph
                        addEntities.Add(ObjectMapper.Map<Aph, BpcsAph>(item));
                }

                //更新
                if (updateEntities.Any())
                {
                    var context = await repoBpcsAph.GetDbContextAsync();
                    await context.BulkUpdateAsync(updateEntities);
                }
                var guidGenerator = LazyServiceProvider.LazyGetService<IGuidGenerator>();
                //插入
                if (addEntities.Any())
                {
                    var context = await repoBpcsAph.GetDbContextAsync();
                    addEntities.ForEach(a => (a as IManualSetId<Guid>).SetId(guidGenerator.Create()));
                    await context.BulkInsertAsync(addEntities);
                }

                try
                {
                    // 找到同步过去的pa number+company code, tdata2
                    //var repoEdiLogInvoice = LazyServiceProvider.GetService<IEdiLogApInvioceRepository>();
                    //var queryEdiLogInvoice = await repoEdiLogInvoice.GetQueryableAsync();
                    //List<EdiLogApInvioce> ediLogApInvioces = queryEdiLogInvoice.Where(x => x.Status == EdiSyncStatus.Pushed).ToList();
                    //if (!ediLogApInvioces.Any())
                    //{
                    //    _logger.LogWarning("No pa number need to process!");
                    //    return "No pa number need to process!";
                    //}

                    var paQuery = await LazyServiceProvider.GetService<IPurPAApplicationRepository>().GetQueryableAsync();
                    //找到aph前一周的数据, tdata1
                    DateTime today = DateTime.Now;
                    DateTime oneWeekAgo = today.AddDays(-7);
                    string startDate = oneWeekAgo.ToString("yyyyMMdd");
                    string endDate = today.ToString("yyyyMMdd");
                    //traCode = string.Concat(a.ApplicationCode.Substring(1).Substring(1), a.ApplicationCode.Substring(1).Substring(0, 1)


                    // 定义一个通用的匿名类型结构以确保一致性
                    var queryAph = queryBpcsAph.Where(x => x.UpdateDate.CompareTo(startDate) >= 0 && x.UpdateDate.CompareTo(endDate) <= 0);

                    // 原始编码 (OriCode)
                    var oriAph = queryAph
                        .GroupJoin(
                            paQuery.Select(a => new
                            {
                                OriCode = a.ApplicationCode.Substring(1),
                                CompanyCode = a.CompanyCode,
                                ApplicationCode = a.ApplicationCode
                            }),
                            aph => new { Apcmpy = aph.Apcmpy.ToString(), Apinv = aph.Apinv }, // 确保 Apcmpy 是字符串类型
                            pa => new { Apcmpy = pa.CompanyCode, Apinv = pa.OriCode },
                            (aph, pas) => new { Aph = aph, Pas = pas })
                        .SelectMany(a => a.Pas.DefaultIfEmpty(), (a, b) => new
                        {
                            AphId = a.Aph.Id,
                            Aph = a.Aph,
                            ApplicationCode = b.ApplicationCode
                        })
                        .Where(a => a.ApplicationCode != null);

                    // 转换编码 (TraCode)
                    var traAph = queryAph
                        .GroupJoin(
                            paQuery.Select(a => new
                            {
                                TraCode = string.Concat(a.ApplicationCode.Substring(1).Substring(1), a.ApplicationCode.Substring(1).Substring(0, 1)),
                                CompanyCode = a.CompanyCode,
                                ApplicationCode = a.ApplicationCode
                            }),
                            aph => new { Apcmpy = aph.Apcmpy.ToString(), Apinv = aph.Apinv }, // 确保 Apcmpy 是字符串类型
                            pa => new { Apcmpy = pa.CompanyCode, Apinv = pa.TraCode },
                            (aph, pas) => new { Aph = aph, Pas = pas })
                        .SelectMany(a => a.Pas.DefaultIfEmpty(), (a, b) => new
                        {
                            AphId = a.Aph.Id,
                            Aph = a.Aph,
                            ApplicationCode = b.ApplicationCode
                        })
                        .Where(a => a.ApplicationCode != null);

                    // 合并结果
                    var queryConcat = oriAph.Union(traAph);
                    var bpcsAphs = queryConcat.ToList();

                    if (!bpcsAphs.Any())
                    {
                        _logger.LogWarning("No found aph!");
                        return "No found aph!";
                    }

                    // tdata1和tdata2匹配，
                    // (tdata1.apinv=tdata2.lpinv or tdata1.apinv like substring(tdata2.lpinv,2,9)+'%')
                    // and tdata1.apcmpy=tdata2.lseg01
                    // tdata
                    //List<BpcsAph> matchedAphs = bpcsAphs.Where(aph =>
                    //               ediLogApInvioces.Any(
                    //                        log => aph.Apcmpy.HasValue
                    //                               && aph.Apcmpy.Value.ToString() == log.Lseg01
                    //                               && (aph.Apinv == log.Lpinv || aph.Apinv.Contains(log.Lpinv.Substring(1, 9)))))
                    //        .ToList();
                    //if (!matchedAphs.Any())
                    //{
                    //    _logger.LogWarning("No matched aph!");
                    //    return "No matched aph!";
                    //}

                    // tdata按pa number+（5位apvndr+2位phdcpx+8位phdcsq）+status，去重，tdata3
                    List<PurPAApplicationRef> distinctRefs = bpcsAphs.Select(x =>
                    {
                        return new PurPAApplicationRef
                        {
                            PAApplicationCode = x.ApplicationCode,
                            RefNo = x.Aph.Apvndr.ToString().PadLeft(5, '0') + x.Aph.Phdcpx + x.Aph.Phdcsq.ToString().PadLeft(8, '0'),
                            Status = x.Aph.Apstat
                        };
                    }).ToList();

                    // 根据tdata3的数据，更新paRefMapping，新增paRefMapping
                    var repoRef = LazyServiceProvider.GetService<IPurPAApplicationRefRepository>();
                    var queryRef = await repoRef.GetQueryableAsync();
                    var paRefKeysSet = new HashSet<string>(distinctRefs.Select(a => a.PAApplicationCode + a.RefNo));
                    var allRefs = queryRef.Where(x => paRefKeysSet.Contains(x.PAApplicationCode + x.RefNo)).ToList();
                    List<PurPAApplicationRef> updateRefs = new List<PurPAApplicationRef>();
                    List<PurPAApplicationRef> insertRefs = new List<PurPAApplicationRef>();
                    foreach (var item in distinctRefs)
                    {
                        var exitsEntity = allRefs.FirstOrDefault(a => a.PAApplicationCode == item.PAApplicationCode && a.RefNo == item.RefNo);
                        if (exitsEntity != null)
                        {
                            // 不对比ID字段
                            if (!item.PropertyEqualAll(exitsEntity, false))
                            {
                                //ID字段不能赋值，否则引起更新异常
                                var updateEntity = item.AssignPropertiesTo(exitsEntity, false);
                                if (updateEntity != null)
                                {
                                    updateRefs.Add(updateEntity);
                                }
                            }
                        }
                        else
                        {
                            insertRefs.Add(item);
                        }
                    }

                    if (updateRefs.Any())
                    {
                        var context = await repoRef.GetDbContextAsync();
                        await context.BulkUpdateAsync(updateRefs);
                    }

                    if (insertRefs.Any())
                    {
                        var context = await repoRef.GetDbContextAsync();
                        insertRefs.ForEach(a => (a as IManualSetId<Guid>).SetId(guidGenerator.Create()));
                        await context.BulkInsertAsync(insertRefs);
                    }
                }
                catch (Exception e)
                {
                    _logger.LogError($"Failed to sync map between pa and ref, Exception: {e}");
                }
            }
            catch (Exception ex)
            {
                log.IsSuccess = false;
                log.Remark = ex.ToString();
                _logger.LogError($"SyncTableAph() Exception: {ex}");
                return ex.Message;
            }
            finally
            {
                _jobLogService.SyncLog(log);
            }
            return null;
        }

        //public async Task<string> SyncTableAml()
        //{
        //    //try
        //    //{
        //    //    //查询Aml
        //    //    var repoAml = LazyServiceProvider.GetService<IIntermediateAmlRepository>();
        //    //    var query = await repoAml.GetQueryableAsync();
        //    //    var listAml = query.Where(a => 1 == 1).ToList();

        //    //    if (listAml?.Any() != true)
        //    //    {
        //    //        return "listAml is null";
        //    //    }

        //    //    //1,查询有哪些Aml已存在表BpcsAml
        //    //    var repoBpcsAml = LazyServiceProvider.GetService<IBpcsAmlRepository>();
        //    //    var queryBpcsAml = await repoBpcsAml.GetQueryableAsync();
        //    //    var exitsBpcsAml = queryBpcsAml.Where(a => 1 == 1).ToList();

        //    //    List<BpcsAml> updateEntities = new List<BpcsAml>();
        //    //    List<BpcsAml> addEntities = new List<BpcsAml>();
        //    //    //2,已存在表BpcsAml的，比较BpcsAml是否有更新，有则Update，无则不保存
        //    //    foreach (var item in listAml)
        //    //    {
        //    //        var exitsEntity = exitsBpcsAml.FirstOrDefault(a => a.Apinv == item.Apinv);
        //    //        if (exitsEntity != null)
        //    //        {
        //    //            //比较BpcsAml是否有更新，有则Update，无则不保存
        //    //            if (!item.PropertyEqualAll(exitsEntity))
        //    //            {
        //    //                var updateEntity = item.AssignPropertiesTo(exitsEntity);
        //    //                if (updateEntity != null)
        //    //                {
        //    //                    updateEntities.Add(updateEntity);
        //    //                }
        //    //            }
        //    //        }
        //    //        else
        //    //        {
        //    //            //3,未存在表BpcsAml的，Insert BpcsAml
        //    //            addEntities.Add(ObjectMapper.Map<Aml, BpcsAml>(item));
        //    //        }
        //    //    }

        //    //    //更新
        //    //    if (updateEntities.Any())
        //    //    {
        //    //        await repoBpcsAml.UpdateManyAsync(updateEntities, true);
        //    //    }
        //    //    //插入
        //    //    if (addEntities.Any())
        //    //    {
        //    //        await repoBpcsAml.InsertManyAsync(addEntities, true);
        //    //    }
        //    //}
        //    //catch (Exception ex)
        //    //{
        //    //    _logger.LogError($"SyncTableAml() Exception: {ex}");
        //    //    return ex.Message;
        //    //}

        //    return null;
        //}

        //public async Task<string> SyncTableGlh()
        //{
        //    try
        //    {
        //        //查询Glh
        //        var repoGlh = LazyServiceProvider.GetService<IIntermediateGlhRepository>();
        //        var query = await repoGlh.GetQueryableAsync();
        //        var listGlh = query.Where(a => 1 == 1).ToList();

        //        if (listGlh?.Any() != true)
        //        {
        //            return "listGlh is null";
        //        }

        //        //1,查询有哪些Glh已存在表BpcsGlh
        //        var repoBpcsGlh = LazyServiceProvider.GetService<IBpcsGlhRepository>();
        //        var queryBpcsGlh = await repoBpcsGlh.GetQueryableAsync();
        //        var exitsBpcsGlh = queryBpcsGlh.Where(a => 1 == 1).ToList();

        //        List<BpcsGlh> updateEntities = new List<BpcsGlh>();
        //        List<BpcsGlh> addEntities = new List<BpcsGlh>();
        //        //2,已存在表BpcsGlh的，比较BpcsGlh是否有更新，有则Update，无则不保存
        //        foreach (var item in listGlh)
        //        {
        //            var exitsEntity = exitsBpcsGlh.FirstOrDefault(a => a.Lhldgr == item.Lhldgr
        //                && a.Lhbook == item.Lhbook
        //                && a.Lhyear == item.Lhyear
        //                && a.Lhperd == item.Lhperd
        //                && a.Lhjnen == item.Lhjnen
        //                && a.Lhjnln == item.Lhjnln);
        //            if (exitsEntity != null)
        //            {
        //                //比较BpcsGlh是否有更新，有则Update，无则不保存
        //                if (!item.PropertyEqualAll(exitsEntity))
        //                {
        //                    var updateEntity = item.AssignPropertiesTo(exitsEntity);
        //                    if (updateEntity != null)
        //                    {
        //                        updateEntities.Add(updateEntity);
        //                    }
        //                }
        //            }
        //            else
        //            {
        //                //3,未存在表BpcsGlh的，Insert BpcsGlh
        //                addEntities.Add(ObjectMapper.Map<Glh, BpcsGlh>(item));
        //            }
        //        }

        //        //更新
        //        if (updateEntities.Any())
        //        {
        //            await repoBpcsGlh.UpdateManyAsync(updateEntities, true);
        //        }
        //        //插入
        //        if (addEntities.Any())
        //        {
        //            await repoBpcsGlh.InsertManyAsync(addEntities, true);
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError($"SyncTableGlh() Exception: {ex}");
        //        return ex.Message;
        //    }

        //    return null;
        //}

        public async Task<string> SyncTableByType<TEntityOri, TEntityTar, TRepoOri, TRepoTar>
            (Func<List<TEntityTar>, TEntityOri, TEntityTar> tarKeyPredicate, bool repoAutoSave = false)
            where TEntityOri : VoloEntities.Entity
            where TEntityTar : VoloEntities.Entity
        {
            try
            {
                //查询TEntityOri
                var repoOriT = LazyServiceProvider.GetService<TRepoOri>();
                IRepository<TEntityOri> repoOri = repoOriT as IRepository<TEntityOri>;
                if (repoOri == null)
                {
                    _logger.LogError($"SyncTableByType() repoOri is null");
                    return "repoOri is null";
                }
                var query = await repoOri.GetQueryableAsync();
                var listOri = query.Where(a => 1 == 1).ToList();

                if (listOri?.Any() != true)
                {
                    return "listOri is null";
                }

                //1,查询有哪些Ori已存在表BpcsTar
                var repoTarT = LazyServiceProvider.GetService<TRepoTar>();
                var repoTar = repoTarT as IRepository<TEntityTar>;
                var queryTar = await repoTar.GetQueryableAsync();
                var exitsTar = queryTar.Where(a => 1 == 1).ToList();

                List<TEntityTar> updateEntities = new List<TEntityTar>();
                List<TEntityTar> addEntities = new List<TEntityTar>();
                //2,已存在表Tar的，比较Tar是否有更新，有则Update，无则不保存
                foreach (var item in listOri)
                {
                    var exitsEntity = tarKeyPredicate(exitsTar, item);
                    if (exitsEntity != null)
                    {
                        //比较Tar是否有更新，有则Update，无则不保存
                        if (!item.PropertyEqualAll(exitsEntity))
                        {
                            var updateEntity = item.AssignPropertiesTo(exitsEntity);
                            if (updateEntity != null)
                            {
                                updateEntities.Add(updateEntity);
                            }
                        }
                    }
                    else
                    {
                        //3,未存在表BpcsGlh的，Insert BpcsGlh
                        addEntities.Add(ObjectMapper.Map<TEntityOri, TEntityTar>(item));
                    }
                }

                //更新
                if (updateEntities.Any())
                {
                    await repoTar.UpdateManyAsync(updateEntities, repoAutoSave);
                }
                //插入
                if (addEntities.Any())
                {
                    await repoTar.InsertManyAsync(addEntities, repoAutoSave);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"SyncTableByType() Exception: {ex}");
                return ex.Message;
            }

            return null;
        }

        private IQueryable<Avm> AddWheres(IQueryable<Avm> queryAvm)
        {
            if (queryAvm == null)
            {
                return queryAvm;
            }

            /*
             select * from BPCS_Interface_Stage_DB.dbo.[AVM]
                where VMID!='VZ' and VTYPE  not in ('NL-P','NH-P','NHIP','NLIP') and( (VCMPNY=20 and ((VTYPE in ('NHIV','NLIV') and (VENDOR<70000 or VENDOR>=90000)) or
                VTYPE  not in ('NHIV','NLIV')))
                or (VCMPNY=18 and (VTYPE in ('NHIV','NLIV') and ((VENDOR like '9%' or (VENDOR>=45000 and VENDOR<=70000)) and  len([VENDOR])>4)) or
                VTYPE  not in ('NHIV','NLIV'))
                or (VCMPNY!=20  and VCMPNY!=18))
             */
            return queryAvm.Where(a => !(new string[] { "NL-P", "NH-P", "NHIP", "NLIP" }).Contains(a.Vtype)
                && (
                    (
                        a.Vcmpny == 20
                        && (
                            (
                              (new string[] { "NHIV", "NLIV" }).Contains(a.Vtype)
                              && (
                                a.Vendor < 70000 || a.Vendor >= 90000
                              )
                            )
                            || !(new string[] { "NHIV", "NLIV" }).Contains(a.Vtype)
                          )
                    )
                    ||
                    (
                        a.Vcmpny == 18
                        && (//4806 BPCS同步供应商号段逻辑调整（将JV与trading的逻辑统一）
                            (new string[] { "NHIV", "NLIV" }).Contains(a.Vtype)
                            && (
                                a.Vendor < 70000 || a.Vendor >= 90000
                              )
                          )
                          || !(new string[] { "NHIV", "NLIV" }).Contains(a.Vtype)
                    )
                    ||
                    (
                        a.Vcmpny != 20 && a.Vcmpny != 18
                    )
                )
            );
        }

        private async Task RewriteFina(List<BpcsAvm> updateEntitiesBpcsAvm, List<BpcsPmfvm> updateEntitiesBpcsPmfvm, List<BpcsAvm> addEntitiesBpcsAvm, List<BpcsPmfvm> addEntitiesBpcsPmfvm)
        {
            List<BpcsAvm> finaBpcsAvm = new List<BpcsAvm>();
            List<BpcsPmfvm> finaBpcsPmfvm = new List<BpcsPmfvm>();
            if (updateEntitiesBpcsAvm?.Any() == true)
            {
                finaBpcsAvm.AddRange(updateEntitiesBpcsAvm.Where(a => a.FinaId != null && !a.FinaId.Equals(Guid.Empty)));
            }
            if (addEntitiesBpcsAvm?.Any() == true)
            {
                finaBpcsAvm.AddRange(addEntitiesBpcsAvm.Where(a => a.FinaId != null && !a.FinaId.Equals(Guid.Empty)));
            }

            //要回写到财务表的BpcsAvm+BpcsPmfvm
            var rewriteEntities = finaBpcsAvm.Join(updateEntitiesBpcsPmfvm.UnionBy(addEntitiesBpcsPmfvm, a => new { a.Vnderx, a.Vmcmpy }),
                l => new { Vnd = l.Vendor, Cmp = l.Vcmpny }, r => new { Vnd = r.Vnderx, Cmp = r.Vmcmpy },
                (pbcsAvm, pbcsPmfvm) => new { pbcsAvm, pbcsPmfvm })
                .ToList();
            if (rewriteEntities?.Any() != true)
            {
                return;
            }

            var repoFina = LazyServiceProvider.LazyGetService<IVendorFinancialRepository>();
            //查出要修改的财务表记录
            var finaUpdateIds = rewriteEntities.Select(r => r.pbcsAvm.FinaId);
            var finaEntites = await repoFina.GetListAsync(a => finaUpdateIds.Contains(a.Id));
            if (finaEntites?.Any() != true)
            {
                return;
            }
            //开始回写(本来想提一个方法，但匿名对象在方法里不能访问)
            foreach (var item in finaEntites)
            {
                var rewriteEntity = rewriteEntities.FirstOrDefault(a => a.pbcsAvm.FinaId == item.Id);
                item.VendorType = rewriteEntity.pbcsAvm.Vtype;
                item.PayType = rewriteEntity.pbcsAvm.Vpayty;
                item.Currency = rewriteEntity.pbcsAvm.Vcurr;
                item.CountryCode = rewriteEntity.pbcsAvm.Vcoun;
                item.BankType = rewriteEntity.pbcsPmfvm.Vldcd1;
                item.AbbottBank = rewriteEntity.pbcsAvm.Vmbank;
                item.Division = rewriteEntity.pbcsAvm.Vmref1;
                item.PaymentTerm = rewriteEntity.pbcsAvm.Vterms;
                item.DpoCategory = rewriteEntity.pbcsPmfvm.Vpals2;
                item.SpendingCategory = rewriteEntity.pbcsPmfvm.Vpals3;

                //新加字段的保存：
                item.BpcsVmid = rewriteEntity.pbcsAvm.Vmid;
                item.BpcsVnstat = rewriteEntity.pbcsAvm.Vnstat;
                if (rewriteEntity.pbcsAvm.Vnstat == "A")
                    item.FinancialVendorStatus = FinancialVendorStatus.Valid;
                if (rewriteEntity.pbcsAvm.Vnstat == "D")
                    item.FinancialVendorStatus = FinancialVendorStatus.Invalid;
                item.BpcsCreationTime = TransDecimalToDt(rewriteEntity.pbcsPmfvm.Vcrdte, rewriteEntity.pbcsPmfvm.Vctime);
            }
            await repoFina.UpdateManyAsync(finaEntites, true);
        }

        private DateTime? TransDecimalToDt(decimal? date, decimal? time)
        {
            DateTime? result = null;
            if (date == null || string.IsNullOrWhiteSpace(date.ToString()))
            {
                return null;
            }

            var dateYear = date.ToString().Substring(0, 4);
            var dateMonth = date.ToString().Substring(4, 2);
            var dateDay = date.ToString().Substring(6, 2);
            //转日期
            if (!int.TryParse(dateYear, out int intYear) ||
                !int.TryParse(dateMonth, out int intMonth) ||
                !int.TryParse(dateDay, out int intDay))
            {
                return null;
            }

            result = new DateTime(intYear, intMonth, intDay);
            if (time == null || string.IsNullOrWhiteSpace(time.ToString()))
            {
                return result;
            }

            var dateHour = time.ToString().Substring(0, 2);
            var dateMinute = date.ToString().Substring(2, 2);
            var dateSecond = date.ToString().Substring(4, 2);
            //转时间
            if (!int.TryParse(dateHour, out int intHour) ||
                !int.TryParse(dateMinute, out int intMinute) ||
                !int.TryParse(dateSecond, out int intSecond))
            {
                return result;
            }

            result.Value.AddHours(intHour);
            result.Value.AddMinutes(intMinute);
            result.Value.AddSeconds(intSecond);
            return result;
        }

        private async Task SetFinaId(List<BpcsAvm> addEntitiesBpcsAvm)
        {
            if (addEntitiesBpcsAvm?.Any() != true)
            {
                return;
            }

            var queryFina = await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync();
            //var querySql = queryFina.Join(addEntitiesBpcsAvm, l => new { Vnd = l.VendorCode, Cmp = l.Company }, r => new { Vnd = r.Vendor.ToString(), Cmp = r.Vcmpny.ToString() }, (finaEntity, bpcsAvm) => new { finaId = finaEntity.Id, bpcsAvm });
            //var queryRslt = querySql.ToList();

            //var querySql1 = addEntitiesBpcsAvm.Join(queryFina, r => new { Vnd = r.Vendor.ToString(), Cmp = r.Vcmpny.ToString() }, l => new { Vnd = l.VendorCode, Cmp = l.Company }, (bpcsAvm, finaEntity) => new { bpcsAvm, finaId = finaEntity.Id });
            //var queryRslt1 = querySql.ToList();

            var querySqlFina = from item in addEntitiesBpcsAvm
                               join t in queryFina on new { Vnd = item.Vendor.ToString(), Cmp = item.Vcmpny.ToString() } equals new { Vnd = t.VendorCode, Cmp = t.Company }
                               //select t;
                               select new { t.Id, Vnd = t.VendorCode, Cmp = t.Company };
            var finaEntities = querySqlFina.ToList();
            if (finaEntities?.Any() != true)
            {
                return;
            }
            addEntitiesBpcsAvm.ForEach(a =>
            {
                a.FinaId = finaEntities.FirstOrDefault(f => f.Vnd == a.Vendor.ToString() && f.Cmp == a.Vcmpny.ToString())?.Id;
            });

            //queryFina.Join(addEntitiesBpcsAvm, l => new { Vnd = l.VendorCode, Cmp = l.Company }, r => new { Vnd = r.Vendor.ToString(), Cmp = r.Vcmpny.ToString() }, (finaEntity, bpcsAvm) => new { finaId = finaEntity.Id, bpcsAvm })
            //    .ToList()
            //    .ForEach(a =>
            //    {
            //        a.bpcsAvm.FinaId = a.finaId;
            //    });

            //var finaEntities = queryFina.Where(l => addEntitiesBpcsAvm.Select(a => new { Vnd = a.Vendor.ToString(), Cmp = a.Vcmpny.ToString() }).Contains(new { Vnd = l.VendorCode, Cmp = l.Company }))
            //    .ToList();

            //var finaEntities = queryFina.Where(e => addEntitiesBpcsAvm.All(o => e.VendorCode == o.Vendor.ToString() && e.Company == o.Vcmpny.ToString()))
            //              .ToList();

            return;
        }

    }
}