﻿using Abbott.SpeakerPortal.Contracts.Integration.Dspot;
using Abbott.SpeakerPortal.Contracts.Integration.Veeva;
using Hangfire;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.BackgroundWorkers.Hangfire;

namespace Abbott.SpeakerPortal.BackgroundWorkers.Integration.Dspot
{
    /// <summary>
    /// 单个cron表达式不能满足需求，弃用
    /// </summary>
    public class ProcessAndMoveErrorsWorker: HangfireBackgroundWorkerBase
    {
     
        private IInteBpcsVendorAppService _inteBpcsVendorAppService;
        public ProcessAndMoveErrorsWorker(IServiceProvider serviceProvider)
        {

            _inteBpcsVendorAppService = serviceProvider.GetService<IInteBpcsVendorAppService>();
            //触发周期
            //CronExpression = Cron.Daily(8,00);
            //CronExpression = Cron.Daily(10, 30);
            //CronExpression = Cron.Daily(12, 30);
            //CronExpression = Cron.Daily(14, 30);
            //CronExpression = Cron.Daily(16, 30);
            //CronExpression = Cron.Daily(18, 30);
            //CronExpression = Cron.Daily(20, 30);
            //CronExpression = Cron.Daily(22, 00);
            //CronExpression = Cron.Daily(13, 00);
            //CronExpression = Cron.Daily(22, 30);

        }

        public override async Task DoWorkAsync(CancellationToken cancellationToken = default)
        {
            //逻辑处理
            await _inteBpcsVendorAppService.ProcessAndMoveErrors();
        }
    }
}
