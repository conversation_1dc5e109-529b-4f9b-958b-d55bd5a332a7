#Deploy a given service to AKS for all parameters

parameters:
  #Path to YAML file / folder to pass to kubectl configuration parameter
  - name: configurationYML
    type: string
  - name: branchName
    type: string
jobs:
  - deployment: DeployToAKS$(namespace) }}
    displayName: Deploy to AKS
    environment: SpeakerPortal-$(namespace)
    strategy: 
      runOnce: 
        deploy:
          steps:
            - checkout: self
            - task: Kubernetes@1
              displayName: 'kubectl apply configmap and services'
              inputs:
                connectionType: 'Azure Resource Manager'
                azureSubscriptionEndpoint: $(azureSubscriptionEndpoint)
                azureResourceGroup: $(aksResourceGroup)
                kubernetesCluster: $(aksClusterName)
                useClusterAdmin: true
                namespace: $(namespace)
                command: apply
                useConfigurationFile: true
                configuration: '$(System.DefaultWorkingDirectory)/aks/config/${{ parameters.branchName }}'
                azureSubscriptionEndpointForSecrets: $(azureSubscriptionEndpoint)
                azureContainerRegistry: $(acrName).azurecr.cn
            - bash: |
                echo '$(Build.BuildNumber)'
                echo '$(Build.BuildId)'

                sed -i 's/:__TAG_VERSION/:v$(Build.BuildNumber).$(Build.BuildId)/g' `grep -rl ":__TAG_VERSION" ./`
                
                sed -i 's/__ENV/$(namespace)/g' `grep -rl "__ENV" ./`
                
                sed -i 's/__ACR-ENV/$(namespace)/g' `grep -rl "__ACR-ENV" ./`
                
                sed -i 's/__ACR-REG/$(acrName)/g' `grep -rl "__ACR-REG" ./`
                
                sed -i 's/__ACR-DOMAIN/.azurecr.cn/g' `grep -rl "__ACR-DOMAIN" ./`
              workingDirectory: '$(System.DefaultWorkingDirectory)/aks/microservices'
              displayName: 'sed variables - all environments'

            - bash: |
                cat ${{ parameters.configurationYML }}
              displayName: 'Display effects of sed on configuration file'
            - task: Kubernetes@1
              displayName: 'kubectl apply service'
              inputs:
                connectionType: 'Azure Resource Manager'
                azureSubscriptionEndpoint: $(azureSubscriptionEndpoint)
                azureResourceGroup: $(aksResourceGroup)
                kubernetesCluster: $(aksClusterName)
                useClusterAdmin: true
                namespace: $(namespace)
                command: apply
                useConfigurationFile: true
                configuration: ${{ parameters.configurationYML }}
                azureSubscriptionEndpointForSecrets: $(azureSubscriptionEndpoint)
                azureContainerRegistry: $(acrName).azurecr.cn
