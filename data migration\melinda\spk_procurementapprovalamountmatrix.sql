select newid() as spk_NexBPMCode
,a.[spk_mainprocurement]
,a.[spk_agencyprocurement]
,a.[spk_agentprocurementpricecomparisonapprover]
,a.[spk_pricecomparisonapprover]
,a.[spk_name]
,ot.spk_NexBPMCode as [spk_organizational]
into #spk_procurementapprovalamountmatrix 
from spk_procurementapprovalamountmatrix_Tmp a
left join spk_organizationalmasterdata ot
on a.spk_organizational=ot.spk_BPMCode

IF OBJECT_ID(N'dbo.spk_procurementapprovalamountmatrix', N'U') IS NOT NULL
BEGIN
	drop table spk_procurementapprovalamountmatrix;
	select  *  into dbo.spk_procurementapprovalamountmatrix from #spk_procurementapprovalamountmatrix
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into dbo.spk_procurementapprovalamountmatrix from #spk_procurementapprovalamountmatrix
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END


