﻿using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Integration.EpdOlineMeeting;
using Abbott.SpeakerPortal.Domain.Shared.Models;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;

using System;
using System.Net;
using System.Net.Mime;
using System.Text;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.Controllers.Integration.EpdOlineMeeting
{
    [ApiExplorerSettings(GroupName = SwaggerGrouping.INTEGRATION)]
    [AllowAnonymous]
    public class EpdOmPrintPageController : SpeakerPortalController
    {
        private readonly IPrintPageOmService _printPageOmService;

        public EpdOmPrintPageController(IServiceProvider serviceProvider)
        {
            _printPageOmService = serviceProvider.GetService<IPrintPageOmService>();
        }

        [HttpPost]
        [AllowAnonymous]
        public async Task<IActionResult> GetPRPrintContent([FromBody] OmPrintPageRequestDto request)
        {
            var result = await _printPageOmService.GetPRPrintPageContent(request);
            if (result.Code == 403)
            {
                return StatusCode(403);
            }
            return Ok(result);
        }

        [HttpPost]
        [AllowAnonymous]
        public async Task<IActionResult> GetPAPrintContent([FromBody] OmPrintPageRequestDto request)
        {
            var result = await _printPageOmService.GetPAPrintPageContent(request);
            if (result.Code == 403)
            {
                return StatusCode(403);
            }
            return Ok(result);
        }

        /// <summary>
        /// 获取PR打印页Html
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public async Task<IActionResult> GetPRPrintPageAsync([FromQuery] OmAuthorizationDto request)
        {
            if (!request.Id.HasValue)
                return Ok(MessageResult.FailureResult("id is null"));

            var result = await _printPageOmService.GetPRPrintPageAsync(request);
            if (result.Code == 403)
                return Forbid();

            if (result.Success)
                return Content(result.Data?.ToString(), MediaTypeNames.Text.Html, Encoding.UTF8);
            else
                return Ok(result);
        }

        /// <summary>
        /// 获取PA打印页Html
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        public async Task<IActionResult> GetPAPrintPageAsync([FromQuery] OmAuthorizationDto request)
        {
            if (!request.Id.HasValue)
                return Ok(MessageResult.FailureResult("id is null"));

            var result = await _printPageOmService.GetPAPrintPageAsync(request);
            if (result.Code == 403)
                return Forbid();

            if (result.Success)
                return Content(result.Data?.ToString(), MediaTypeNames.Text.Html, Encoding.UTF8);
            else
                return Ok(result);
        }
    }
}
