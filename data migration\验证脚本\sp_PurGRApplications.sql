CREATE PROCEDURE dbo.sp_PurGRApplications
AS 
BEGIN

--初始化xml5 sp_PurGRApplications
--IF OBJECT_ID(N'PLATFORM_ABBOTT.dbo.XML_5', N'U') IS NOT NULL
--BEGIN
--	PRINT(N'已经初始化过'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
--END
--ELSE
--BEGIN
--select  ProcInstId,
--STRING_AGG(CountersignEmpId, ', ') WITHIN GROUP (ORDER BY CountersignEmpId) AS CountersignEmpId 
--into PLATFORM_ABBOTT.dbo.XML_5 
--from (
--select ProcInstId,CountersignEmpId.value('.', 'nvarchar(100)') as CountersignEmpId  from  PLATFORM_ABBOTT.dbo.ODS_T_FORMINSTANCE_GLOBAL otfg 
--CROSS APPLY XmlContent.nodes('/root/GoodsReceiveApplication_CountersignInfoBlock_MainStore/CountersignEmpId') AS XMLTable(CountersignEmpId))A
--group by ProcInstId
--PRINT(N'初始化完成'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
--END;


--drop table #PurGRApplications_tmp
select 
newid() AS Id,--
oabtgrai.ProcInstId,
oabtgrai.serialNumber AS ApplicationCode,--
case when processStatus=N'收货' or processStatus=N'收货人加签' or processStatus=N'PO申请人审批' then N'待收货'
when processStatus=N'完成' then N'已收货'
when processStatus=N'发起人终止' or processStatus=N'终止'  then N'终止收货'
--when processStatus=N'发货人加签' then N'加签人加签'
when processStatus=N'收货' then N'付款中'
--when processStatus=N'PO申请人审批' then N'终止审批中' 
END AS Status,--参考下方附录状态mapping表
oabtgrai.applicantEmpId AS ApplyUserId,--以该ID匹配至员工主数据
oabtgrai.applicationDate AS ApplyTime,--
POOrArNumber AS ApplyUserBu,--基于该GR对应的PR申请单，查询PurPRApplications表内对应记录的ApplyUserBu表示该单据对应费用由哪个Division申请
'' AS EsignPdf,--该字段已不再使用，默认填为空
'' AS DeliveryMode,--该字段已不再使用，默认填为空
'' AS MeetingModifyRemark,--该字段已不再使用，默认填为空
'' AS AmountModifyRemark,--该字段已不再使用，默认填为空
oabtgrai.applicantEmpId AS ProcurementPersonnelId,--以该ID匹配至员工主数据
oabtgrai.SupplierCode AS VendorId,--基于此处的SupplyCode及SupplyName，结合该单对应的公司编码CompanyId，去查询BPCSPMFVM对应的记录(VNDERX,VEXTNM,VMCMPY)，对于匹配成功的记录，再找到其对应的BPCSAVM.ID并填入(基于VMCMPY=VCMPNY AND VNDERX=VENDOR)
--oabtgrai.SupplierName as SupplyName,
case when oabtgrai.AdvancePayment='false' then 
	case when  ppt.AdvancePayment='1' and ppt.status<>N'作废' then '1'
	else '0' end
when oabtgrai.AdvancePayment='true' then '1' end AS IsAdvancePayment,--false-0, true-1
cast('' as nvarchar(500)) AS AdditionalSignerId,--以该ID匹配至员工主数据，如有多个以英文逗号分隔ID
'' AS Remarks,--默认为空
'' AS PSAIds,--默认为空(历史无此功能)
'{}' AS ExtraProperties,--默认填写为"{}"
'' AS ConcurrencyStamp,--?
oabtgrai.applicantEmpId AS CreationTime,--填充为ApplyTime即可
oabtgrai.applicationDate AS CreatorId,--填充为ApplyUserId即可
'' AS LastModificationTime,--默认为空
'' AS LastModifierId,--默认为空
'0' AS IsDeleted,--默认为0
'' AS DeleterId,--默认为空
'' AS DeletionTime,--默认为空
cast('' as nvarchar(500)) AS ApplyUserBuName,--
oabtgrai.applicantEmpName AS ApplyUserName,--
oabtgrai.SupplierName AS VendorName,--
oabtgrai.budgetNumber AS BudgetCode,--
oabtgrai.budgetNumber AS BudgetId,--以该代码匹配至子预算的代码，以找回子预算ID；若无法匹配至预算ID(例如部分Code可能形如"YXHD006"，该Code实际为与BPM对接的另一个系统中的预算ID)，对应的ID可能需要在子预算表里加入dummy数据以便找回ID
case when oabtgrai.POOrArNumber like 'O%' then oabtgrai.POOrArNumber else '' end AS PoId,--取此处以"O"开头的单据号，匹配至PurPOApplications的ID后填入
cast('' as nvarchar(500)) AS PrId,--基于该GR对应的PR申请单，查询PurPRApplications的ID后填入
oabtgrai.applicantDept_Text AS ApplyUserBuToDeptName,--
case when oabtgrai.POOrArNumber like 'O%' then oabtgrai.POOrArNumber else '' end AS PoApplicationCode,--取此处以"O"开头的单据号
cast('' as nvarchar(500)) AS PrApplicationCode,--对应PurPRApplications中的ApplicationCode
--case when a.payamount='NULL' or a.payamount='' or a.payamount is null then 0 else cast(a.payamount AS DECIMAL(18, 2)) end   payamount,
--case when a.taxRate='NULL' or a.taxRate='' or a.taxRate is null then 1 else CAST(REPLACE(a.taxRate, '%', '') AS DECIMAL(18, 2)) / 100.0+1 end taxRate,
case when oabtgrai.AdvancePayment='false' then 
	case when  ppt.AdvancePayment='1' and ppt.status<>N'作废' 
		then  
			case when a.payamount='NULL' or a.payamount='' or a.payamount is null then 0 else cast(a.payamount AS DECIMAL(18, 2)) end /(case when a.taxRate='NULL' or a.taxRate='' or a.taxRate is null then 1 else 1+CAST(REPLACE(a.taxRate, '%', '') AS DECIMAL(18, 2)) / 100.0 end )
				else  
					case when oabtgrai.AdvanceMoney='NULL' or oabtgrai.AdvanceMoney is null or oabtgrai.AdvanceMoney=''
					then 0 
					else cast(oabtgrai.AdvanceMoney AS DECIMAL(18, 2)) 
					end 
				end 
when oabtgrai.AdvancePayment='true' then 
	case when oabtgrai.AdvanceMoney='NULL' or oabtgrai.AdvanceMoney is null or oabtgrai.AdvanceMoney='' 
	then 0 
	else cast(oabtgrai.AdvanceMoney AS DECIMAL(18, 2))  
	end 
end
 AS PaymentExcludingTaxAmount,--预付款金额
Res_Code AS CompanyCode,--以该值作为Res_Data查询T_Resource中Res_Parent_Code="61a3f911b5ae4bc98cddd441833d861e"的记录，基于匹配回的Res_Code匹配至公司主数据得到公司对应的BPCS代码
case when oabtgrai.PRItemType='AR' then '1' 
when oabtgrai.PRItemType='AP' then '2' end AS PayMethod,--AR-1, AP-2 
Res_Code AS CompanyId,--以该值作为Res_Data查询T_Resource中Res_Parent_Code="61a3f911b5ae4bc98cddd441833d861e"的记录，基于匹配回的Res_Code匹配至公司主数据得到公司ID
oabtgrai.CompanyName AS CompanyName,--
oabtgrai.SupplierCode AS VendorCode,--
'' AS ReceivedTime--？
into #PurGRApplications_tmp
from PLATFORM_ABBOTT.dbo.ODS_AUTO_BIZ_T_GoodsReceiveApplication_Info oabtgrai --626855
left join PLATFORM_ABBOTT.dbo.ODS_Form_2ce08373394a4d4cace676978bfa2984 ofcadcb 
on oabtgrai.ProcInstId =ofcadcb.ProcInstId --626853
left join PLATFORM_ABBOTT.dbo.ODS_T_RESOURCE otr 
on oabtgrai.CompanyCode=otr.Res_Data  and Res_Parent_Code='61a3f911b5ae4bc98cddd441833d861e'--626853
left join (select *,ROW_NUMBER () over(PARTITION by GRId order by AdvancePayment desc) rn from PLATFORM_ABBOTT.dbo.PurPAApplications_tmp) ppt 
on ppt.GRId  =oabtgrai.ProcInstId and ppt.rn=1
left join (select *,ROW_NUMBER () over(PARTITION by GR_ProcInstId order by applicationDate desc) rn from PLATFORM_ABBOTT.dbo.ods_AUTO_BIZ_T_PaymentApplication_Info) a
on a.GR_ProcInstId =oabtgrai.ProcInstId and a.rn=1


--更新BU
update a set a.ApplyUserBuName=b.BU,a.PrId=b.prno,a.PrApplicationCode=b.prno from #PurGRApplications_tmp a
join(
select DISTINCT  BU,ProcInstId,prno from PLATFORM_ABBOTT.dbo.ods_AUTO_BIZ_T_GoodsReceiveApplication_Info_PR
)b
on a.ProcInstId =b.ProcInstId 

--更新AdditionalSignerId
update a set a.AdditionalSignerId=b.CountersignEmpId from #PurGRApplications_tmp a
join(
select * from PLATFORM_ABBOTT.dbo.xml_5
)b
on a.ProcInstId =b.ProcInstId 





IF OBJECT_ID(N'PLATFORM_ABBOTT.dbo.PurGRApplications_tmp', N'U') IS NOT NULL
BEGIN
	update a 
	set a.ProcInstId                  = b.ProcInstId
       ,a.ApplicationCode             = b.ApplicationCode
       ,a.Status                      = b.Status
       ,a.ApplyUserId                 = b.ApplyUserId
       ,a.ApplyTime                   = b.ApplyTime
       ,a.ApplyUserBu                 = b.ApplyUserBu
       ,a.EsignPdf                    = b.EsignPdf
       ,a.DeliveryMode                = b.DeliveryMode
       ,a.MeetingModifyRemark         = b.MeetingModifyRemark
       ,a.AmountModifyRemark          = b.AmountModifyRemark
       ,a.ProcurementPersonnelId      = b.ProcurementPersonnelId
       ,a.VendorId                    = b.VendorId
       ,a.IsAdvancePayment            = b.IsAdvancePayment
       ,a.AdditionalSignerId          = b.AdditionalSignerId
       ,a.Remarks                     = b.Remarks
       ,a.PSAIds                      = b.PSAIds
       ,a.ExtraProperties             = b.ExtraProperties
       ,a.ConcurrencyStamp            = b.ConcurrencyStamp
       ,a.CreationTime                = b.CreationTime
       ,a.CreatorId                   = b.CreatorId
       ,a.LastModificationTime        = b.LastModificationTime
       ,a.LastModifierId              = b.LastModifierId
       ,a.IsDeleted                   = b.IsDeleted
       ,a.DeleterId                   = b.DeleterId
       ,a.DeletionTime                = b.DeletionTime
       ,a.ApplyUserBuName             = b.ApplyUserBuName
       ,a.ApplyUserName               = b.ApplyUserName
       ,a.VendorName                  = b.VendorName
       ,a.BudgetCode                  = b.BudgetCode
       ,a.BudgetId              = b.BudgetId
       ,a.PoId                        = b.PoId
       ,a.PrId                        = b.PrId
       ,a.ApplyUserBuToDeptName       = b.ApplyUserBuToDeptName
       ,a.PoApplicationCode           = b.PoApplicationCode
       ,a.PrApplicationCode           = b.PrApplicationCode
       ,a.PaymentExcludingTaxAmount   = b.PaymentExcludingTaxAmount
       ,a.CompanyCode                 = b.CompanyCode
       ,a.PayMethod                   = b.PayMethod
       ,a.CompanyId                   = b.CompanyId
       ,a.CompanyName                 = b.CompanyName
       ,a.VendorCode                  = b.VendorCode
       ,a.ReceivedTime                = b.ReceivedTime
     from PLATFORM_ABBOTT.dbo.PurGRApplications_tmp a
     left join #PurGRApplications_tmp b on a.ProcInstId = b.ProcInstId and a.ApplicationCode = b.ApplicationCode
     
     insert into PLATFORM_ABBOTT.dbo.PurGRApplications_tmp 
     select a.Id
           ,a.ProcInstId
           ,a.ApplicationCode
           ,a.Status
           ,a.ApplyUserId
           ,a.ApplyTime
           ,a.ApplyUserBu
           ,a.EsignPdf
           ,a.DeliveryMode
           ,a.MeetingModifyRemark
           ,a.AmountModifyRemark
           ,a.ProcurementPersonnelId
           ,a.VendorId
           ,a.IsAdvancePayment
,a.AdditionalSignerId
           ,a.Remarks
           ,a.PSAIds
           ,a.ExtraProperties
           ,a.ConcurrencyStamp
           ,a.CreationTime
           ,a.CreatorId
           ,a.LastModificationTime
           ,a.LastModifierId
           ,a.IsDeleted
           ,a.DeleterId
           ,a.DeletionTime
           ,a.ApplyUserBuName
           ,a.ApplyUserName
           ,a.VendorName
           ,a.BudgetCode
           ,a.BudgetId
           ,a.PoId
           ,a.PrId
           ,a.ApplyUserBuToDeptName
           ,a.PoApplicationCode
           ,a.PrApplicationCode
           ,a.PaymentExcludingTaxAmount
           ,a.CompanyCode
           ,a.PayMethod
           ,a.CompanyId
           ,a.CompanyName
           ,a.VendorCode
           ,a.ReceivedTime
       from #PurGRApplications_tmp a 
       where not exists (select * from PLATFORM_ABBOTT.dbo.PurGRApplications_tmp where ProcInstId = a.ProcInstId and ApplicationCode = a.ApplicationCode)
	PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into PLATFORM_ABBOTT.dbo.PurGRApplications_tmp from #PurGRApplications_tmp
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END 

END;


