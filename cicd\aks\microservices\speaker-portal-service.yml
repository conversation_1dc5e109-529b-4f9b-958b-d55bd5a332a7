apiVersion: apps/v1
kind: Deployment
metadata:
  name: speaker-portal-service
  namespace: __ENV
  annotations:
    reloader.stakater.com/auto: "true"
    container.apparmor.security.beta.kubernetes.io/speaker-portal-service: "runtime/default"
spec:
  selector:
    matchLabels:
      app: speaker-portal-service
  replicas: 2
  template:
    metadata:
      labels:
        app: speaker-portal-service
    spec:
      containers:
        - name: speaker-portal-service
          image: "__ACR-REG__ACR-DOMAIN/__ACR-ENV/abbott-services-speaker-portal-service:__TAG_VERSION"
          imagePullPolicy: Always
          volumeMounts:
            - mountPath: "/mnt/secrets-store"
              name: secrets-store
              readOnly: true        
          resources:
            requests:
              memory: "1536Mi"
              cpu: "1500m"
            limits:
              memory: "5000Mi"
              cpu: "3000m"                
          securityContext:
            runAsNonRoot: true
            runAsUser: 1000
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - ALL
              add: ["NET_ADMIN", "SYS_TIME", "NET_BIND_SERVICE"]
          livenessProbe:
            httpGet:
              path: api/misc/health
              port: 5000
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 1
            successThreshold: 1
            failureThreshold: 5
          env:
          # From ConfigMap and self ref          
            - name: ASPNETCORE_ENVIRONMENT
              valueFrom:
                configMapKeyRef:
                  name: microservice-config
                  key: ASPNETCORE.ENVIRONMENT
          ports:
            - name: http
              containerPort: 80
      volumes:
      - name: secrets-store
        csi:
          driver: secrets-store.csi.k8s.io
          readOnly: true
          volumeAttributes:
            secretProviderClass: azure-kv
            
---
apiVersion: autoscaling/v2beta2
kind: HorizontalPodAutoscaler
metadata:
  name: speaker-portal-service
  namespace: __ENV
spec:
  maxReplicas: 15
  minReplicas: 2
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: speaker-portal-service
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 80
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80

---
kind: Service
apiVersion: v1
metadata:
  name: speaker-portal-service
  namespace: __ENV
spec:
  selector:
    app: speaker-portal-service
  #clusterIP: ************
  ports:
    - protocol: TCP
      port: 80
      targetPort: http