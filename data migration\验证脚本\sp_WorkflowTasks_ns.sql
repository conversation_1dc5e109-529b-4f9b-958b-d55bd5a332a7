CREATE proc sp_WorkflowTasks_ns
as
begin
	--首次全部insert
	if OBJECT_ID(N'WorkflowTasks',N'U') is null
	begin
		select newid() Id,[InstanceId],[Name],[Status],[action],[Remark],[NextApproverId],[FormId],[WorkStep],[ExtraProperties],[ConcurrencyStamp],[CreationTime],[CreatorId],[LastModificationTime],[LastModifierId],[IsDeleted],[DeleterId],[DeletionTime],[ApprovalTime],[ApprovalId],[FormName],[OriginalApprovalId],ProcInstID as _ProcInstID,OrderID as _OrderID
		into WorkflowTasks
		from WorkflowTasks_tmp;
		PRINT(N'首次新增完成');
	end
	else--否则upsert
	begin
		--update存量
		update t set
			t.[InstanceId]=tmp.[InstanceId],
			t.[Name]=tmp.[Name],
			t.[Status]=tmp.[Status],
			t.[action]=tmp.[action],
			t.[Remark]=tmp.[Remark],
			t.[NextApproverId]=tmp.[NextApproverId],
			t.[FormId]=tmp.[FormId],
			t.[WorkStep]=tmp.[WorkStep],
			t.[ExtraProperties]=tmp.[ExtraProperties],
			t.[ConcurrencyStamp]=tmp.[ConcurrencyStamp],
			t.[CreationTime]=tmp.[CreationTime],
			t.[CreatorId]=tmp.[CreatorId],
			t.[LastModificationTime]=tmp.[LastModificationTime],
			t.[LastModifierId]=tmp.[LastModifierId],
			t.[IsDeleted]=tmp.[IsDeleted],
			t.[DeleterId]=tmp.[DeleterId],
			t.[DeletionTime]=tmp.[DeletionTime],
			t.[ApprovalTime]=tmp.[ApprovalTime],
			t.[ApprovalId]=tmp.[ApprovalId],
			t.[FormName]=tmp.[FormName],
			t.[OriginalApprovalId]=tmp.[OriginalApprovalId]
		from WorkflowTasks t join WorkflowTasks_tmp tmp 
		on t._ProcInstID = tmp.ProcInstID 
		and t._OrderID = tmp.OrderID 
		and t.[Status] = tmp.[Status]
		and t.[OriginalApprovalId] = tmp.[OriginalApprovalId]
		and t.[Remark] = tmp.[Remark]
		and t.[action] = tmp.[action]
		and t.[Remark] = tmp.[Remark]
		PRINT(N'修改存量数据完成');

		--insert增量
		insert WorkflowTasks
		select newid() Id,[InstanceId],[Name],[Status],[action],[Remark],[NextApproverId],[FormId],[WorkStep],[ExtraProperties],[ConcurrencyStamp],[CreationTime],[CreatorId],[LastModificationTime],[LastModifierId],[IsDeleted],[DeleterId],[DeletionTime],[ApprovalTime],[ApprovalId],[FormName],[OriginalApprovalId],ProcInstID as _ProcInstID,OrderID as _OrderID
		from WorkflowTasks_tmp tmp
		where not exists(select * from WorkflowTasks where _ProcInstID=tmp.ProcInstID and _OrderID=tmp.OrderID);
		PRINT(N'新增增量数据完成');
	end
end;