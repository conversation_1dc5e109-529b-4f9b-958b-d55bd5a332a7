﻿using System;
using Abbott.SpeakerPortal.Dtos;

namespace Abbott.SpeakerPortal.Contracts.Budget.FocSubbudget
{
    public class GetFocSubbudgetListRequestDto : PagedDto
    {
        /// <summary>
        /// 主预算编号
        /// </summary>
        public string MasterBudgetCode { get; set; }
        /// <summary>
        /// 主预算Id
        /// </summary>
        public Guid? MasterBudgetId { get; set; }
        /// <summary>
        /// 子预算BU
        /// </summary>
        public Guid BuId { get; set; }
        /// <summary>
        /// 子预算BU名称
        /// </summary>
        public string BuName { get; set; }
        /// <summary>
        /// 子预算成本中心
        /// </summary>
        public Guid? CostCenterId { get; set; }
        /// <summary>
        /// FOC产品简称Id
        /// </summary>
        public Guid? ProductId { get; set; }
        /// <summary>
        /// 子预算编号
        /// </summary>
        public string SubbudgetCode { get; set; }
        /// <summary>
        /// 品牌信息编码
        /// </summary>
        public string ProductMCode { get; set; }
        /// <summary>
        /// 年度
        /// </summary>
        public int? Year { get; set; }
        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }
        /// <summary>
        /// 主预算负责人
        /// </summary>
        public Guid? MasterBudgetOwnerId { get; set; }
        /// <summary>
        /// 子预算负责人
        /// </summary>
        public Guid? SubbudgetOwnerId { get; set; }
    }
}
