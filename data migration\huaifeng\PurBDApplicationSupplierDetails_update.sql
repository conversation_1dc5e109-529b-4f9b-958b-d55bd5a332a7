SELECT
 TRY_CONVERT(UNIQUEIDENTIFIER, [Id]) [Id]
,TRY_CONVERT(UNIQUEIDENTIFIER, iif([BDApplicationId] is NULL,'00000000-0000-0000-0000-000000000000',[BDApplicationId])) [BDApplicationId]
,TRY_CONVERT(UNIQUEIDENTIFIER, [VendorId]) [VendorId]
,[VendorName]
,[Unit]
,isnull(CAST(CAST([Quantity] AS DECIMAL(32,8))AS INT),'0') [Quantity]
,[TotalAmount]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CreatorId]) [CreatorId]
,[LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [LastModifierId]) [LastModifierId]
,[IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, [DeleterId]) [DeleterId]
,[DeletionTime]
,isnull([UnitPrice],'0')UnitPrice
INTO #PurBDApplicationSupplierDetails
FROM (SELECT DISTINCT *,ROW_NUMBER() OVER(PARTITION BY Id ORDER BY Id) RK FROM PLATFORM_ABBOTT.dbo.PurBDApplicationSupplierDetails)a
WHERE RK = 1

--drop table #PurBDApplicationSupplierDetails

USE Speaker_Portal;

UPDATE a
SET 
 a.[Id] = b.[Id]
,a.[BDApplicationId] = b.[BDApplicationId]
,a.[VendorId] = b.[VendorId]
,a.[VendorName] = b.[VendorName]
,a.[Unit] = b.[Unit]
,a.[Quantity] = b.[Quantity]
,a.[TotalAmount] = b.[TotalAmount]
,a.[ExtraProperties] = b.[ExtraProperties]
,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = b.[CreatorId]
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = b.[LastModifierId]
,a.[IsDeleted] = b.[IsDeleted]
,a.[DeleterId] = b.[DeleterId]
,a.[DeletionTime] = b.[DeletionTime]
,a.[UnitPrice] = b.[UnitPrice]
FROM dbo.PurBDApplicationSupplierDetails a
left join #PurBDApplicationSupplierDetails  b
ON a.id=b.id


--select * from #PurBDApplicationDetails where BDApplicationId is null


INSERT INTO dbo.PurBDApplicationSupplierDetails
SELECT
 [Id]
,[BDApplicationId]
,[VendorId]
,[VendorName]
,[Unit]
,[Quantity]
,[TotalAmount]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[UnitPrice]
FROM #PurBDApplicationSupplierDetails a
WHERE not exists (select * from dbo.PurBDApplicationSupplierDetails where id=a.id)


--truncate table dbo.PurBDApplicationSupplierDetails