CREATE PROCEDURE dbo.sp_BdHistory_ns
AS 
BEGIN
	select 
a.[Id],
case when SUBSTRING(a.BudgetId,1,2)='MB' then   UPPER(bmbt.Id) 
when SUBSTRING(a.BudgetId,1,2)='CC' then   UPPER(bsbt.Id)  end [BudgetId],
a.[BudgetType],
UPPER(ss.spk_NexBPMCode) as [OperatorId],
ss.spk_name as [OperatorName],
a.[OperatingTime],
a.[OperateType],
a.[OperateAmount],
a.[OperateContent],
a.[Remark],
a.[ExtraProperties],
a.[ConcurrencyStamp],
a.[CreationTime],
UPPER(ss1.spk_NexBPMCode) as [CreatorId],
a.[LastModificationTime],
a.[LastModifierId],
a.[IsDeleted],
a.[DeleterId],
a.[DeletionTime],
a.[TargetBudgetCode]
into #BdHistory
from PLATFORM_ABBOTT_Dev.dbo.BdHistory_tmp a 
left join PLATFORM_ABBOTT_Dev.dbo.BdMasterBudgets_tmp bmbt 
on a.BudgetId =bmbt.Code 
left join PLATFORM_ABBOTT_Dev.dbo.BdSubBudgets_tmp bsbt 
on a.BudgetId =bsbt.Code 
left join PLATFORM_ABBOTT_Dev.dbo.spk_staffmasterdata ss 
on a.OperatorId =ss.bpm_id 
left join PLATFORM_ABBOTT_Dev.dbo.spk_staffmasterdata ss1 
on a.CreatorId =ss1.bpm_id 

  --删除表
    IF OBJECT_ID(N'PLATFORM_ABBOTT_Dev.dbo.BdHistory ', N'U') IS NOT NULL
	BEGIN
		drop table PLATFORM_ABBOTT_Dev.dbo.BdHistory
		select *
        into PLATFORM_ABBOTT_Dev.dbo.BdHistory from #BdHistory
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into PLATFORM_ABBOTT_Dev.dbo.BdHistory from #BdHistory
    -- select * from #vendor_tbl
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
END
