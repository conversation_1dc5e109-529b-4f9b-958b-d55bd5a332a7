﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Mime;
using System.Text;
using System.Threading.Tasks;

using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Concur;
using Abbott.SpeakerPortal.Contracts.Concur.MealReport;
using Abbott.SpeakerPortal.Contracts.Concur.OrgMapping;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Permissions;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

using MiniExcelLibs;
using MiniExcelLibs.OpenXml;

using Volo.Abp.Application.Dtos;

namespace Abbott.SpeakerPortal.Controllers.Concur
{
    [ApiExplorerSettings(GroupName = SwaggerGrouping.CONCUR)]
    public class ConcurController : SpeakerPortalController
    {
        IConcurService _concurService;

        public ConcurController(IConcurService concurService)
        {
            _concurService = concurService;
        }

        #region 员工分组配置

        /// <summary>
        /// 查询员工分组配置信息
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult<PagedResultDto<EmployeeQueryResponseDto>>))]
        [Authorize(SpeakerPortalPermissions.Concur.EmployeeRead)]
        public async Task<IActionResult> QueryEmployeeListAsync([FromBody] EmployeeQueryRequestDto request)
        {
            var result = await _concurService.QueryEmployeeListAsync(request);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 上传员工分组数据执行校验
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult<EmployeeImportValidResponseDto>))]
        [Authorize(SpeakerPortalPermissions.Concur.EmployeeImport)]
        public async Task<IActionResult> UploadEmployeeAsync(IFormFile file)
        {
            if (file == null)
                return Ok(MessageResult.FailureResult("未获取到Excel信息"));

            var buffer = await file.GetAllBytesAsync();
            var result = await _concurService.UploadEmployeeAsync(buffer);
            return Ok(result);
        }

        /// <summary>
        /// 执行导入员工分组数据
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        [Authorize(SpeakerPortalPermissions.Concur.EmployeeImport)]
        public async Task<IActionResult> ImportEmployeeAsync([FromBody] EmployeeImportRequestDto request)
        {
            var errorMessage = ModelState.SelectMany(a => a.Value.Errors, (a, b) => b.ErrorMessage).JoinAsString("；");
            if (!string.IsNullOrEmpty(errorMessage))
                return Ok(MessageResult.FailureResult(errorMessage));

            var result = await _concurService.ImportEmployeeAsync(request);
            return Ok(result);
        }

        /// <summary>
        /// 导出员工分组配置信息
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [Authorize(SpeakerPortalPermissions.Concur.EmployeeExport)]
        public async Task<IActionResult> ExportEmployeeListAsync([FromBody] EmployeeQueryRequestDto request)
        {
            request.PageIndex = 1;
            request.PageSize = int.MaxValue;

            var result = await _concurService.QueryEmployeeListAsync(request);

            var stream = new MemoryStream();
            var config = new OpenXmlConfiguration()
            {
                TableStyles = TableStyles.None,
                AutoFilter = false
            };
            stream.SaveAs(result.Items, true, "Sheet1", configuration: config);
            stream.Seek(0, SeekOrigin.Begin);
            return File(stream, MediaTypeNames.Application.Octet, $"员工分组配置列表-{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}.xlsx");
        }

        #endregion

        #region 机构转换配置

        /// <summary>
        /// 获取机构转换配置列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult<PagedResultDto<QueryOrgMappingResponseDto>>))]
        [Authorize(SpeakerPortalPermissions.Concur.OrganizationRead)]
        public async Task<IActionResult> QueryOrgMappingListAsync([FromBody] QueryOrgMappingRequestDto request)
        {
            var result = await _concurService.QueryOrgMappingListAsync(request);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 获取机构名称列表
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<IEnumerable<string>>))]
        [Authorize(SpeakerPortalPermissions.Concur.OrganizationRead)]
        public async Task<IActionResult> GetOrgNewNamesAsync(string keyword)
        {
            if (string.IsNullOrEmpty(keyword))
                return Ok(MessageResult.SuccessResult());

            var result = await _concurService.GetOrgNewNamesAsync(keyword);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 更新机构转换配置
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        [Authorize(SpeakerPortalPermissions.Concur.OrganizationCRUD)]
        public async Task<IActionResult> UpdateOrgMappingAsync([FromBody] UpdateOrgMappingRequestDto request)
        {
            var errorMessage = ModelState.SelectMany(a => a.Value.Errors, (a, b) => b.ErrorMessage).JoinAsString("；");
            if (!string.IsNullOrEmpty(errorMessage))
                return Ok(MessageResult.FailureResult(errorMessage));

            var result = await _concurService.UpdateOrgMappingAsync(request);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 导出机构转换配置列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [Authorize(SpeakerPortalPermissions.Concur.OrganizationExport)]
        public async Task<IActionResult> ExportOrgMappingListAsync([FromBody] QueryOrgMappingRequestDto request)
        {
            request.PageIndex = 1;
            request.PageSize = int.MaxValue;

            var result = await _concurService.QueryOrgMappingListAsync(request);

            var stream = new MemoryStream();
            var config = new OpenXmlConfiguration()
            {
                TableStyles = TableStyles.None,
                AutoFilter = false
            };
            stream.SaveAs(result.Items, true, "Sheet1", configuration: config);
            stream.Seek(0, SeekOrigin.Begin);
            return File(stream, MediaTypeNames.Application.Octet, $"机构转换配置列表-{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}.xlsx");
        }

        /// <summary>
        /// 获取机构配置变更历史
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<IEnumerable<QueryOrgMappingHistoryResponseDto>>))]
        [Authorize(SpeakerPortalPermissions.Concur.OrganizationRead)]
        public async Task<IActionResult> GetOrgMappingHistoryListAsync(Guid id)
        {
            var result = await _concurService.GetOrgMappingHistoryListAsync(id);
            return Ok(MessageResult.SuccessResult(result));
        }

        #endregion

        #region 用餐报告

        /// <summary>
        /// 查询用餐报告
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(PagedResultDto<MealReportQueryResponseDto>))]
        [Authorize(SpeakerPortalPermissions.Concur.MealReportRead)]
        public async Task<IActionResult> QueryMealReportListAsync([FromBody] MealReportQueryRequestDto request)
        {
            var result = await _concurService.QueryMealReportListAsync(request);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 上传用餐报告数据执行校验
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult<MealReportImportValidResponseDto>))]
        [Authorize(SpeakerPortalPermissions.Concur.MealReportImport)]
        public async Task<IActionResult> UploadMealReportAsync(IFormFile file)
        {
            if (file == null)
                return Ok(MessageResult.FailureResult("未获取到Excel信息"));

            var buffer = await file.GetAllBytesAsync();
            var result = await _concurService.UploadMealReportAsync(buffer);
            return Ok(result);
        }

        /// <summary>
        /// 导出用餐报告
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [Authorize(SpeakerPortalPermissions.Concur.MealReportExport)]
        public async Task<IActionResult> ExportMealReportListAsync([FromBody] MealReportQueryRequestDto request)
        {
            request.PageIndex = 1;
            request.PageSize = int.MaxValue;

            var result = await _concurService.QueryMealReportListAsync(request);

            var stream = new MemoryStream();
            var config = new OpenXmlConfiguration()
            {
                TableStyles = TableStyles.None,
                AutoFilter = false
            };
            stream.SaveAs(result.Items, true, "Sheet1", configuration: config);
            stream.Seek(0, SeekOrigin.Begin);
            return File(stream, MediaTypeNames.Application.Octet, $"用餐报告列表-{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}.xlsx");
        }

        #endregion
    }
}
