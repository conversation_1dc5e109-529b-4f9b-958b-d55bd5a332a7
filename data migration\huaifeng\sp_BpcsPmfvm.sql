select
VRI<PERSON>
,VMCMP<PERSON>
,VNDERX
,VEXTN<PERSON>
,VADD1
,VADD2
,VADD3
,VEMLA<PERSON>
,VARE<PERSON>
,VCRDTE
,VC<PERSON>ME
,VCUSER
,VLDATE
,VLTIME
,VLUSER
,VPDTE1
,VPDTE2
,VPREF1
,VPREF2
,VPALS1
,VP<PERSON>S2
,VPALS3
,VPALS4
,VPALS5
,VPRMK1
,VPRMK2
,VPCOD1
,VPCOD2
,VPCOD3
,VPFLG1
,VPFLG2
,VPFLG3
,VPNUC1
,VPNUC2
,VPAMT1
,VPAMT2
,VPINF1
,VPINF2
,VPINF3
,VLDTE1
,VLDTE2
,VLDRE1
,VLDRE2
,VLDAS1
,VLDAS2
,VLDAS3
,VLDAS4
,VLDAS5
,VLDRM1
,VLDRM2
,VLDCD1
,VLDCD2
,VLDFG1
,VLDFG2
,VLDFG3
,VLDNC1
,VLDNC2
,VLDAM1
,VLDAM2
,VLINF1
,VLINF2
,VLINF3
,ID
into #BPCSPMFVM
from PLATFORM_ABBOTT_STG.dbo.ODS_BPCS_PMFVM   a   

use Speaker_Portal_stg;


UPDATE a 
SET 
a.VRID          =b.VRID   
,a.VMCMPY     =b.VMCMPY 
,a.VNDERX     =b.VNDERX 
,a.VEXTNM     =b.VEXTNM 
,a.VADD1      =b.VADD1  
,a.VADD2      =b.VADD2  
,a.VADD3      =b.VADD3  
,a.VEMLAD     =b.VEMLAD 
,a.VAREAC     =b.VAREAC 
,a.VCRDTE     =b.VCRDTE 
,a.VCTIME     =b.VCTIME 
,a.VCUSER     =b.VCUSER 
,a.VLDATE     =b.VLDATE 
,a.VLTIME     =b.VLTIME 
,a.VLUSER     =b.VLUSER 
,a.VPDTE1     =b.VPDTE1 
,a.VPDTE2     =b.VPDTE2 
,a.VPREF1     =b.VPREF1 
,a.VPREF2     =b.VPREF2 
,a.VPALS1     =b.VPALS1 
,a.VPALS2     =b.VPALS2 
,a.VPALS3     =b.VPALS3 
,a.VPALS4     =b.VPALS4 
,a.VPALS5     =b.VPALS5 
,a.VPRMK1     =b.VPRMK1 
,a.VPRMK2     =b.VPRMK2 
,a.VPCOD1     =b.VPCOD1 
,a.VPCOD2     =b.VPCOD2 
,a.VPCOD3     =b.VPCOD3 
,a.VPFLG1     =b.VPFLG1 
,a.VPFLG2     =b.VPFLG2 
,a.VPFLG3     =b.VPFLG3 
,a.VPNUC1     =b.VPNUC1 
,a.VPNUC2     =b.VPNUC2 
,a.VPAMT1     =b.VPAMT1 
,a.VPAMT2     =b.VPAMT2 
,a.VPINF1     =b.VPINF1 
,a.VPINF2     =b.VPINF2 
,a.VPINF3     =b.VPINF3 
,a.VLDTE1     =b.VLDTE1 
,a.VLDTE2     =b.VLDTE2 
,a.VLDRE1     =b.VLDRE1 
,a.VLDRE2     =b.VLDRE2 
,a.VLDAS1     =b.VLDAS1 
,a.VLDAS2     =b.VLDAS2 
,a.VLDAS3     =b.VLDAS3 
,a.VLDAS4     =b.VLDAS4 
,a.VLDAS5     =b.VLDAS5 
,a.VLDRM1     =b.VLDRM1 
,a.VLDRM2     =b.VLDRM2 
,a.VLDCD1     =b.VLDCD1 
,a.VLDCD2     =b.VLDCD2 
,a.VLDFG1     =b.VLDFG1 
,a.VLDFG2     =b.VLDFG2 
,a.VLDFG3     =b.VLDFG3 
,a.VLDNC1     =b.VLDNC1 
,a.VLDNC2     =b.VLDNC2 
,a.VLDAM1     =b.VLDAM1 
,a.VLDAM2     =b.VLDAM2 
,a.VLINF1     =b.VLINF1 
,a.VLINF2     =b.VLINF2 
,a.VLINF3     =b.VLINF3     
FROM dbo.BpcsPmfvm a
left join #BpcsPmfvm  b
ON a.id=b.id;


INSERT INTO dbo.BpcsPmfvm
(
 VRID
,VMCMPY
,VNDERX
,VEXTNM
,VADD1
,VADD2
,VADD3
,VEMLAD
,VAREAC
,VCRDTE
,VCTIME
,VCUSER
,VLDATE
,VLTIME
,VLUSER
,VPDTE1
,VPDTE2
,VPREF1
,VPREF2
,VPALS1
,VPALS2
,VPALS3
,VPALS4
,VPALS5
,VPRMK1
,VPRMK2
,VPCOD1
,VPCOD2
,VPCOD3
,VPFLG1
,VPFLG2
,VPFLG3
,VPNUC1
,VPNUC2
,VPAMT1
,VPAMT2
,VPINF1
,VPINF2
,VPINF3
,VLDTE1
,VLDTE2
,VLDRE1
,VLDRE2
,VLDAS1
,VLDAS2
,VLDAS3
,VLDAS4
,VLDAS5
,VLDRM1
,VLDRM2
,VLDCD1
,VLDCD2
,VLDFG1
,VLDFG2
,VLDFG3
,VLDNC1
,VLDNC2
,VLDAM1
,VLDAM2
,VLINF1
,VLINF2
,VLINF3
,ID
)
SELECT
VRID
,VMCMPY
,VNDERX
,VEXTNM
,VADD1
,VADD2
,VADD3
,VEMLAD
,VAREAC
,VCRDTE
,VCTIME
,VCUSER
,VLDATE
,VLTIME
,VLUSER
,VPDTE1
,VPDTE2
,VPREF1
,VPREF2
,VPALS1
,VPALS2
,VPALS3
,VPALS4
,VPALS5
,VPRMK1
,VPRMK2
,VPCOD1
,VPCOD2
,VPCOD3
,VPFLG1
,VPFLG2
,VPFLG3
,VPNUC1
,VPNUC2
,VPAMT1
,VPAMT2
,VPINF1
,VPINF2
,VPINF3
,VLDTE1
,VLDTE2
,VLDRE1
,VLDRE2
,VLDAS1
,VLDAS2
,VLDAS3
,VLDAS4
,VLDAS5
,VLDRM1
,VLDRM2
,VLDCD1
,VLDCD2
,VLDFG1
,VLDFG2
,VLDFG3
,VLDNC1
,VLDNC2
,VLDAM1
,VLDAM2
,VLINF1
,VLINF2
,VLINF3
,ID
FROM #BPCSPMFVM a
WHERE not exists (select * from dbo.BPCSPMFVM where id=a.id);





