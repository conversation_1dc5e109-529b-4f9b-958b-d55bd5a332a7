CREATE PROCEDURE [dbo].sp_BdAuthorizedBudgetBus_ns
AS
BEGIN
	SELECT
	 temp.id
	,e.spk_NexBPMCode as UserId
	,f.spk_NexBPMCode as BuId
	,d.Res_Name AS BuName
	,'{}' AS ExtraProperties
	,'NULL' AS ConcurrencyStamp
	,temp.OpDate AS CreationTime
	,g.spk_NexBPMCode as CreatorId
	,NULL AS LastModificationTime
	,NULL AS LastModifierId
	,0 AS IsDeleted
	,NULL DeleterId
	,NULL DeletionTime
	,temp.Emp_Id
	, c.OperateEmpId
INTO #BdAuthorizedBudgetBus
FROM PLATFORM_ABBOTT_Stg.dbo.BdAuthorizedBudgetBus_Tmp temp
LEFT JOIN PLATFORM_ABBOTT_Stg.dbo.ODS_T_Pur_MenuRole_MainInfo c
ON temp.B_RoleId = c.RoleId
LEFT JOIN PLATFORM_ABBOTT_Stg.dbo.ODS_T_Resource d
ON c.OrgCode = d.Res_Code
LEFT JOIN PLATFORM_ABBOTT_Stg.dbo.spk_staffmasterdata e
ON temp.Emp_Id = e.bpm_id
LEFT JOIN PLATFORM_ABBOTT_Stg.dbo.spk_organizationalmasterdata f
ON c.OrgCode = f.spk_BPMCode 
LEFT JOIN PLATFORM_ABBOTT_Stg.dbo.spk_staffmasterdata g
ON c.OperateEmpId = g.bpm_id



    --删除表
    IF OBJECT_ID(N'PLATFORM_ABBOTT_Stg.dbo.BdAuthorizedBudgetBus ', N'U') IS NOT NULL
	BEGIN
		drop table PLATFORM_ABBOTT_Stg.dbo.BdAuthorizedBudgetBus
		select *
        into PLATFORM_ABBOTT_Stg.dbo.BdAuthorizedBudgetBus from #BdAuthorizedBudgetBus
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into PLATFORM_ABBOTT_Stg.dbo.BdAuthorizedBudgetBus from #BdAuthorizedBudgetBus
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
END