apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: consent-ingress
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - consent-portal-api.oneabbott.com
    secretName: tls-consent-portal-api-secret
  rules:
  - http:
      paths:
      - path: /(.*)
        pathType: Prefix
        backend:
          service:
            name: corp-191-consentapi-prd
            port:
              number: 80