﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Volo.Abp.Domain.Entities.Auditing;

namespace Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication
{
    public class PurPRApplicationDetailBackupVendor : FullAuditedEntity<Guid>
    {
        /// <summary>
        /// 主表Id
        /// </summary>
        public Guid PRApplicationId { get; set; }
        
        /// <summary>
        /// 主表Id
        /// </summary>
        public Guid PRApplicationDetailId { get; set; }

        /// <summary>
        /// 讲者Id
        /// </summary>
        public Guid VendorId { get; set; }

        /// <summary>
        /// 讲者名称
        /// </summary>
        [MaxLength(50)]
        public string VendorName { get; set; }

        /// <summary>
        /// Payment term
        /// </summary>
        [MaxLength(50)]
        public string PaymentTerm { get; set; }

        /// <summary>
        /// HCP级别
        /// </summary>
        [MaxLength(50)]
        public string HcpLevelName { get; set; }

        /// <summary>
        /// HCP级别
        /// </summary>
        [MaxLength(50)]
        public string HcpLevelCode { get; set; }

        /// <summary>
        /// 所属医院
        /// </summary>
        [MaxLength(100)]
        public string Hospital { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal Price { get; set; }

        /// <summary>
        /// XXX(单价:1000,00)
        /// </summary>
        [MaxLength(50)]
        public string Text { get; set; }
        
        /// <summary>
        /// 例外审批编号
        /// </summary>
        [MaxLength(50)]
        public string ExceptionNumber { get; set; }

        /// <summary>
        /// 总金额
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal? TotalAmount { get; set; }

        /// <summary>
        /// 总金额（RMB）
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal? TotalAmountRMB { get; set; }
    }
}
