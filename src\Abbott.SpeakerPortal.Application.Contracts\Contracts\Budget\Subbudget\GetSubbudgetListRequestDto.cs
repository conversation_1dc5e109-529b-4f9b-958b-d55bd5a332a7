﻿using Abbott.SpeakerPortal.Dtos;
using Abbott.SpeakerPortal.Enums;

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Abbott.SpeakerPortal.Contracts.Budget.Subbudget
{
    public class GetSubbudgetListRequestDto : PagedDto
    {
        /// <summary>
        /// 主预算编号
        /// </summary>
        public string MasterBudgetCode { get; set; }
        /// <summary>
        /// 主预算Id
        /// </summary>
        public Guid? MasterBudgetId { get; set; }
        /// <summary>
        /// 子预算BU
        /// </summary>
        public Guid BuId { get; set; }
        /// <summary>
        /// 子预算BU名称
        /// </summary>
        public string BuName { get; set; }
        /// <summary>
        /// 子预算成本中心
        /// </summary>
        public Guid? CostCenterId { get; set; }
        /// <summary>
        /// 子预算大区
        /// </summary>
        public Guid? RegionId { get; set; }
        /// <summary>
        /// 子预算编号
        /// </summary>
        public string SubbudgetCode { get; set; }
        /// <summary>
        /// 年度
        /// </summary>
        public int? Year { get; set; }
        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }
        /// <summary>
        /// 主预算负责人
        /// </summary>
        public Guid? MasterBudgetOwnerId { get; set; }
        /// <summary>
        /// 子预算负责人
        /// </summary>
        public Guid? SubbudgetOwnerId { get; set; }
    }
}
