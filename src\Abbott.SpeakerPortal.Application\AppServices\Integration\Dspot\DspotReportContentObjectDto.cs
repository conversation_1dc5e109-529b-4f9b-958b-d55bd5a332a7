﻿using Abbott.SpeakerPortal.Entities.Purchase.PurGRApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPOApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using System;
using System.Collections.Generic;

namespace Abbott.SpeakerPortal.Contracts.Integration.Dspot
{
    /// <summary>
    /// 全流程报表，包含需要的所有对象（PR、PO、GR、PA、出纳）
    /// </summary>
    public class DspotReportContentObjectDto
    {
        public PurPRApplication PR { get; set; }

        public PurPRApplicationDetail PRDetail { get; set; }

        public PurPOApplication PO { get; set; }

        public PurGRApplication GR { get; set; }

        public PurPAApplication PA { get; set; }

        public List<PurPAApplicationDetail> PADetails { get; set; } = new List<PurPAApplicationDetail>();

        /// <summary>
        /// 主要用于排序
        /// </summary>
        public DateTime? LastModifyDt { get; set; }
    }

    /// <summary>
    /// 主要用于Lamda查询
    /// </summary>
    public class DspotReportContentObjPrPrDDto
    {
        public PurPRApplication PR { get; set; }

        public PurPRApplicationDetail PRDetail { get; set; }
    }

    public class DspotReportContentObjPoPoDDto
    {
        public PurPOApplication PO { get; set; }

        public PurPOApplicationDetails PODetail { get; set; }
    }

    public class DspotReportContentObjGrGrDDto
    {
        public PurGRApplication GR { get; set; }

        public PurGRApplicationDetail GRDetail { get; set; }
    }

    /// <summary>
    /// 主要用于Lamda查询
    /// </summary>
    public class DspotReportContentObjPaPaDDto
    {
        public PurPAApplication PA { get; set; }

        public PurPAApplicationDetail PADetail { get; set; }
    }

    public class DspotReportContentObjWholeProcess
    {
        public PurPRApplication PR { get; set; }

        public PurPRApplicationDetail PRDetail { get; set; }

        public Guid PRDetailID { get; set; }

        public PurPOApplication PO { get; set; }

        public PurPOApplicationDetails PODetail { get; set; }

        public Guid PODetailID { get; set; }

        public PurGRApplication GR { get; set; }

        public PurGRApplicationDetail GRDetail { get; set; }

        public PurPAApplication PA { get; set; }

        public PurPAApplicationDetail PADetail { get; set; }
    }
}