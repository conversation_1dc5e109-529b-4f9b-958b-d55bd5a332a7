﻿using Abbott.SpeakerPortal.BackgroundWorkers.BPMEmail;
using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Budget.Subbudget;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.BPMEmail;
using Abbott.SpeakerPortal.Contracts.CrossBu;
using Abbott.SpeakerPortal.Contracts.Graph;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Dataverse.Contracts;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.CrossBu;
using Abbott.SpeakerPortal.Entities.CrossBu.AccessLog;
using Abbott.SpeakerPortal.Entities.CrossBu.CustomerRelationHis;
using Abbott.SpeakerPortal.Entities.CrossBu.MarketActivity;
using Abbott.SpeakerPortal.Entities.CrossBu.ShareHis;
using Abbott.SpeakerPortal.Entities.VendorPersonals;
using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Extension;
using Abbott.SpeakerPortal.Person;
using Abbott.SpeakerPortal.Utils;
using EFCore.BulkExtensions;
using Flurl.Http;
using Hangfire;
using Microsoft.AspNetCore;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Logging;
using Microsoft.Identity.Client;
using Org.BouncyCastle.Asn1.Cms;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Net;
using System.Net.Http.Json;
using System.Reflection;
using System.Security.Cryptography.X509Certificates;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Data;
using Volo.Abp.DistributedLocking;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Guids;
using Volo.Abp.Identity;
using Volo.Abp.ObjectMapping;

namespace Abbott.SpeakerPortal.AppServices.CrossBu
{
    public class CrossBuService : SpeakerPortalAppService, ICrossBuService
    {
        private readonly ILogger<CrossBuService> _logger;
        private readonly IServiceProvider _serviceProvider;
        private readonly IIdentityUserRepository _identityUserRepository;
        private readonly IDataverseService _dataverseService;
        private IDataverseRepository _dataverseRepository;
        private readonly IConfiguration _configuration;
        private readonly ICommonService _commonService;
        /// <summary>
        /// 批量事务的最大允许请求数，微软默认只支持最多同时处理1000个请求，超过则会报错
        /// </summary>
        public static readonly int ExecuteTransactionMaxmumBatchSize = 1000;

        public CrossBuService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _logger = serviceProvider.GetService<ILogger<CrossBuService>>();
            _identityUserRepository = serviceProvider.GetService<IIdentityUserRepository>();
            _dataverseService = serviceProvider.GetService<IDataverseService>();
            _configuration = _serviceProvider.GetService<IConfiguration>();
            _commonService = serviceProvider.GetService<ICommonService>();
            _dataverseRepository = _serviceProvider.GetService<IDataverseRepository>();
        }

        /// <summary>
        /// 获取年每个月是否有活动
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> QueryYearMarketActivitiesAsync(QueryYearMarketActivityRequestDto request)
        {
            var queryActivity = await LazyServiceProvider.LazyGetService<IActivityRepository>().GetQueryableAsync();
            var queryActivityBu = await LazyServiceProvider.LazyGetService<IActivityBuRepository>().GetQueryableAsync();

            if (request.Bu != null)
            {
                var ActivityBuIds = await queryActivityBu.Where(a => a.BuId == request.Bu).Select(a => a.MarketActivityId).Distinct().ToListAsync();
                queryActivity = queryActivity.Where(a => ActivityBuIds.Contains(a.Id));
            }

            if (!string.IsNullOrEmpty(request.Province))
            {
                var provinces = await _commonService.GetProvinceCity();
                var provinceCities = provinces.FirstOrDefault(a => a.Name == request.Province).Cities;
                var cityNames = provinceCities.Select(a => a.Name).Distinct().ToList();
                queryActivity = queryActivity.Where(a => cityNames.Contains(a.City));
            }

            var query = queryActivity
                .Where(a => a.ReleaseStatus == ActivityReleaseStatus.Published)
                .Where(a => (a.EndYear == null && a.StartYear == request.Year) || (a.EndYear != null && a.StartYear <= request.Year && a.EndYear >= request.Year))
                .WhereIf(!string.IsNullOrEmpty(request.City), a => a.City == request.City);

            var result = new List<QueryYearMarketActivityListResponseDto>();
            for (int i = 1; i <= 12; i++)
            {
                var monthrMarketActivity = new QueryYearMarketActivityListResponseDto()
                {
                    Year = request.Year,
                    Month = i,
                    HasValue = false
                };

                var monthCount = request.Year * 12 + i;

                foreach (var item in query)
                {
                    //开始月数
                    var monthCountStart = item.StartYear * 12;
                    if (item.StartMonth != null)
                    {
                        monthCountStart += item.StartMonth!.Value;
                    }
                    else if (item.StartQuarter != null)
                    {
                        monthCountStart += (item.StartQuarter!.Value - 1) * 3 + 1;
                    }
                    else
                    {
                        monthCountStart += i;
                    }

                    //结束月数
                    var monthCountEnd = 0;
                    if (item.EndYear != null && item.EndYear > 0)
                    {
                        monthCountEnd = item.EndYear!.Value * 12;
                        if (item.EndMonth != null)
                        {
                            monthCountEnd += item.EndMonth!.Value;
                        }
                        else if (item.EndQuarter != null)
                        {
                            monthCountEnd += item.EndQuarter!.Value * 3;
                        }
                        else
                        {
                            monthCountEnd += 12;
                        }
                    }
                    else
                    {
                        monthCountEnd = monthCountStart;
                    }

                    if (monthCountStart <= monthCount && monthCountEnd >= monthCount)
                    {
                        monthrMarketActivity.HasValue = true;
                        break;
                    }
                }

                result.Add(monthrMarketActivity);
            }
            return MessageResult.SuccessResult(result);
        }

        /// <summary>
        /// 获取市场活动列表
        /// </summary>
        /// <param name="request"></param>
        /// <param name="IsPage"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<QueryMarketActivityListResponseDto>> QueryMarketActivitiesAsync(QueryMarketActivityRequestDto request, bool IsPage = true)
        {
            var queryActivity = await LazyServiceProvider.LazyGetService<IActivityRepository>().GetQueryableAsync();
            var queryActivityDepartment = await LazyServiceProvider.LazyGetService<IActivityDepartmentRepository>().GetQueryableAsync();
            var queryActivityHospital = await LazyServiceProvider.LazyGetService<IActivityHospitalRepository>().GetQueryableAsync();
            var queryActivityBu = await LazyServiceProvider.LazyGetService<IActivityBuRepository>().GetQueryableAsync();

            //数据权限过滤，H5端不做数据过滤
            if (!request.IsMobile)
            {
                var personCenterService = LazyServiceProvider.LazyGetService<IPersonCenterService>();
                var roleLevel = personCenterService.GetMyRoleLevel();
                if (roleLevel > 0 && CurrentUser.Id != null)
                {
                    var departments = await GetUserDepartment();
                    if (departments != null)
                    {
                        var buIds = departments.Select(a => a.Id).Distinct();
                        var activityBuIds = await queryActivityBu.Where(a => buIds.Contains(a.BuId)).Select(a => a.MarketActivityId).Distinct().ToListAsync();
                        queryActivity = queryActivity.Where(a => buIds.Contains(a.BussinessUnitId.Value) || activityBuIds.Contains(a.Id));
                    }
                }
            }
            else
            {
                await CreateAssceeLog(CrossBuModule.MarketActivity);
            }
            if (request.Bu != null)
            {
                var buId = request.Bu.Value;
                var activityBuIds = await queryActivityBu.Where(a => a.BuId == buId).Select(a => a.MarketActivityId).Distinct().ToListAsync();
                queryActivity = queryActivity.Where(a => a.BussinessUnitId == buId || activityBuIds.Contains(a.Id));
            }

            //bookremark
            if (request.IsBookmark.HasValue && request.IsBookmark.Value)
            {
                var queryActivityUserInformation = await LazyServiceProvider.LazyGetService<IActivityUserInformationRepository>().GetQueryableAsync();
                var bookmarkMarketActivityIds = queryActivityUserInformation.Where(a => a.UserId == CurrentUser.Id && a.IsBookmark).Select(a => a.MarketActivityId).Distinct();
                queryActivity = queryActivity.Where(a => bookmarkMarketActivityIds.Contains(a.Id));
            }

            if (!string.IsNullOrEmpty(request.Province))
            {
                var provinces = await _commonService.GetProvinceCity();
                var provinceCities = provinces.FirstOrDefault(a => a.Name == request.Province).Cities;
                var cityNames = provinceCities.Select(a => a.Name).Distinct().ToList();
                queryActivity = queryActivity.Where(a => cityNames.Contains(a.City));
            }

            var query = queryActivity.WhereIf(request.IsMobile, m => m.ReleaseStatus == ActivityReleaseStatus.Published)
                .WhereIf(!string.IsNullOrEmpty(request.Name), a => a.Name.Contains(request.Name))
                .WhereIf(!string.IsNullOrEmpty(request.Type), a => a.Type == request.Type)
                .WhereIf(!string.IsNullOrEmpty(request.Mode), a => a.Mode == request.Mode)
                .WhereIf(!string.IsNullOrEmpty(request.City), a => a.City == request.City)
                .WhereIf(!string.IsNullOrEmpty(request.CityCode), a => a.CityCode == request.CityCode)
                .WhereIf((request.ReleaseStatus != null), a => a.ReleaseStatus == request.ReleaseStatus)
                .WhereIf((request.Year != null), a => (a.EndYear == null && a.StartYear == request.Year) || (a.EndYear != null && a.StartYear <= request.Year && a.EndYear >= request.Year))
                .OrderByDescending(o => o.StartYear).ThenByDescending(o => o.StartMonth).ThenByDescending(o => o.StartDay).ThenByDescending(o => o.StartQuarter).AsQueryable();

            //传入DepartmentId, 获取该DepartmentId的ActivityId进行过滤
            if (request.DepartmentId.HasValue)
            {
                var marketActivityIds = queryActivityDepartment.Where(a => a.DepartmentId == request.DepartmentId).Select(a => a.MarketActivityId).Distinct();
                if (marketActivityIds.ToList().Count > 0)
                {
                    query = query.Where(a => marketActivityIds.Contains(a.Id)).AsQueryable();
                }
                else
                {
                    return new PagedResultDto<QueryMarketActivityListResponseDto>();
                }
            }

            /*            if (request.Year != null && request.Month != null)
                        {
                            var monthCount = request.Year * 12 + request.Month;
                            query = query.Where(a =>
                                (a.StartYear * 12 + (a.StartMonth ?? ((a.StartQuarter - 1) * 3 + 1) ?? request.Month) <= monthCount) &&
                                ((a.EndYear * 12 + (a.EndMonth ?? (a.EndQuarter * 3) ?? 12)) >= monthCount));
                        }

                        if (request.StartDate != null && request.EndDate != null)
                        {
                            query = query.Where(a =>
                                (new DateTime(a.StartYear, a.StartMonth ?? ((a.StartQuarter - 1) * 3 + 1) ?? 1, a.StartDay ?? 1) >= request.StartDate.Value) &&
                                (new DateTime(a.EndYear ?? a.StartYear, a.EndMonth ?? (a.EndQuarter * 3) ?? 12, a.EndDay ?? 1) <= request.EndDate.Value));
                        }*/

            //过滤选月
            if (request.Year != null && request.Month != null)
            {
                var monthCount = request.Year * 12 + request.Month;
                var queryYearAndMonth = new List<Activity>();
                foreach (var item in query)
                {
                    //开始月数
                    var monthCountStart = item.StartYear * 12;
                    if (item.StartMonth != null)
                    {
                        monthCountStart += item.StartMonth!.Value;
                    }
                    else if (item.StartQuarter != null)
                    {
                        monthCountStart += (item.StartQuarter!.Value - 1) * 3 + 1;
                    }
                    else
                    {
                        if (request.Month != null && request.Month >= 1 && request.Month <= 12)
                        {
                            monthCountStart += request.Month.Value;
                        }
                    }

                    //结束月数
                    var monthCountEnd = 0;
                    if (item.EndYear != null && item.EndYear > 0)
                    {
                        monthCountEnd = item.EndYear!.Value * 12;
                        if (item.EndMonth != null)
                        {
                            monthCountEnd += item.EndMonth!.Value;
                        }
                        else if (item.EndQuarter != null)
                        {
                            monthCountEnd += item.EndQuarter!.Value * 3;
                        }
                        else
                        {
                            monthCountEnd += 12;
                        }
                    }
                    else
                    {
                        monthCountEnd = monthCountStart;
                    }

                    if (monthCountStart <= monthCount && monthCountEnd >= monthCount)
                    {
                        queryYearAndMonth.Add(item);
                    }
                }

                var ids = queryYearAndMonth.Select(a => a.Id).ToList();
                query = query.Where(a => ids.Contains(a.Id));
            }

            //时间段
            if (request.StartDate != null && request.EndDate != null)
            {
                var timeFrameactivities = new List<Activity>();
                foreach (var item in query)
                {
                    //构建活动开始时间
                    int startYear = item.StartYear;
                    int startMonth = 1;
                    int startDay = 1;

                    if (item.StartQuarter != null && item.StartQuarter >= 1 && item.StartQuarter <= 4)
                    {
                        startMonth = (item.StartQuarter.Value - 1) * 3 + 1;
                    }

                    if (item.StartMonth != null && item.StartMonth >= 1 && item.StartMonth <= 12)
                    {
                        startMonth = item.StartMonth.Value;
                    }

                    if (item.StartDay != null && item.StartDay >= 1 && item.StartDay <= 31)
                    {
                        startDay = item.StartDay.Value;
                    }

                    var startDate = new DateTime(startYear, startMonth, startDay, 0, 0, 0, DateTimeKind.Local);

                    //构建活动结束时间
                    int endMonth = 12;
                    int endDay = 1;

                    DateTime endDate;

                    if (item.EndYear != null)
                    {
                        if (item.EndQuarter == null && item.EndMonth == null)
                        {
                            endDate = new DateTime(item.EndYear.Value + 1, 0, 0, 0, 0, 0, DateTimeKind.Local);
                        }
                        else
                        {
                            if (item.EndQuarter != null && item.EndQuarter >= 1 && item.EndQuarter <= 4)
                            {
                                endMonth = (item.EndQuarter.Value - 1) * 3 + 1;
                            }
                            if (item.EndMonth != null && item.EndMonth >= 1 && item.EndMonth <= 12)
                            {
                                endMonth = item.EndMonth.Value;
                            }

                            if (item.StartDay != null && item.StartDay >= 1 && item.StartDay <= 31)
                            {
                                endDay = item.StartDay.Value;
                                endDate = new DateTime(item.EndYear.Value, endMonth, endDay, 0, 0, 0, DateTimeKind.Local);
                            }
                            else
                            {
                                endDate = new DateTime(item.EndYear.Value, endMonth + 1, 0, 0, 0, 0, DateTimeKind.Local);
                            }
                        }
                    }
                    else
                    {
                        endDate = request.EndDate.Value;
                    }

                    if (startDate >= request.EndDate.Value && startDate >= request.StartDate.Value || endDate >= request.EndDate.Value && endDate >= request.StartDate.Value)
                    {
                        timeFrameactivities.Add(item);
                    }
                }

                var ids = timeFrameactivities.Select(a => a.Id).ToList();
                query = query.Where(a => ids.Contains(a.Id));
            }

            //总数
            var count = await query.CountAsync();
            if (count == 0)
            {
                return new PagedResultDto<QueryMarketActivityListResponseDto>(count, new List<QueryMarketActivityListResponseDto>());
            }
            //分页
            query = query.PagingIf(request, IsPage);

            var datas = await query.Select(a => new QueryMarketActivityListResponseDto
            {
                Id = a.Id,
                Code = a.Code,
                Name = a.Name,
                Type = a.Type,
                Mode = a.Mode,
                StartYear = a.StartYear,
                StartQuarter = a.StartQuarter,
                StartMonth = a.StartMonth,
                StartDay = a.StartDay,
                EndYear = a.EndYear,
                EndQuarter = a.EndQuarter,
                EndMonth = a.EndMonth,
                EndDay = a.EndDay,
                CityCode = a.CityCode,
                City = a.City,
                Location = a.Location,
                Applicant = a.Applicant,
                Contact = a.Contact,
                Sponsor = a.Sponsor,
                Organizer = a.Organizer,
                Hospital = a.Hospital,
                Theme = a.Theme,
                Purpose = a.Purpose,
                Url = a.Url,
                ReleaseStatus = a.ReleaseStatus,
                BussinessUnitId = a.BussinessUnitId,
                BussinessUnitName = a.BussinessUnitName.ClearBu(),
                CreatorId = a.CreatorId,
                ReleaseTime = a.ReleaseTime,
                ParentMarketActivityId = a.ParentMarketActivityId
            }).ToListAsync();

            var activityIds = datas.Select(a => a.Id).ToList();
            var parentMarketActivityIds = datas.Where(a => a.ParentMarketActivityId != null).Select(a => a.ParentMarketActivityId.Value).ToList();

            //科室
            var departmentlDatas = await queryActivityDepartment.Where(a => activityIds.Contains(a.MarketActivityId)).ToListAsync();
            //医院
            var hospitalDatas = await queryActivityHospital.Where(a => activityIds.Contains(a.MarketActivityId)).ToListAsync();
            //BU
            var buDatas = await queryActivityBu.Where(a => activityIds.Contains(a.MarketActivityId) || parentMarketActivityIds.Contains(a.MarketActivityId)).ToListAsync();

            //user
            var userIds = datas.Where(a => a.CreatorId != null).Select(a => a.CreatorId.Value).Distinct();
            var users = await _identityUserRepository.GetListByIdsAsync(userIds);

            foreach (var data in datas)
            {
                data.Departments = departmentlDatas.Where(a => a.MarketActivityId == data.Id).DistinctBy(a => a.DepartmentId).ToDictionary(a => a.DepartmentId, a => a.Department);
                data.DepartmentNames = departmentlDatas.Where(a => a.MarketActivityId == data.Id).Select(a => a.Department).Distinct().ToList();

                var hospitals = hospitalDatas.Where(a => a.MarketActivityId == data.Id).Select(a => a.Hospital).Distinct();
                data.Hospital = string.Join(',', hospitals);

                if (data.ParentMarketActivityId != null)
                {
                    var parentMarketActivityId = data.ParentMarketActivityId.Value;
                    data.OwningBusinessUnits = buDatas.Where(a => a.MarketActivityId == parentMarketActivityId).DistinctBy(a => a.BuId).ToDictionary(a => a.BuId, a => a.Bu.ClearBu());
                    data.OwningBusinessUnitNames = buDatas.Where(a => a.MarketActivityId == parentMarketActivityId).Select(a => a.Bu.ClearBu()).Distinct().ToList();

                    var parentMarketActivity = await queryActivity.FirstOrDefaultAsync(a => a.Id == parentMarketActivityId);
                    if (parentMarketActivity != null)
                    {
                        data.BussinessUnitName = parentMarketActivity.BussinessUnitName;
                        data.BussinessUnitId = parentMarketActivity.BussinessUnitId;
                        data.City = parentMarketActivity.City;
                        data.CreatorId = parentMarketActivity.CreatorId;
                        data.ReleaseTime = parentMarketActivity.ReleaseTime;
                    }
                }
                else
                {
                    data.OwningBusinessUnits = buDatas.Where(a => a.MarketActivityId == data.Id).DistinctBy(a => a.BuId).ToDictionary(a => a.BuId, a => a.Bu.ClearBu());
                    data.OwningBusinessUnitNames = buDatas.Where(a => a.MarketActivityId == data.Id).Select(a => a.Bu.ClearBu()).Distinct().ToList();
                }

                data.ReleaseUserName = users.FirstOrDefault(a => a.Id == data.CreatorId)?.Name;
            }

            var result = new PagedResultDto<QueryMarketActivityListResponseDto>(count, datas);
            return result;
        }

        /// <summary>
        /// 检查活动重复
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> CheckMarketActivityAsync(CreateMarketActivityRequestDto request)
        {
            var activityRepository = LazyServiceProvider.LazyGetService<IActivityRepository>();
            var queryActivity = await activityRepository.GetQueryableAsync();

            var checkMarketActivityResponse = new CheckMarketActivityResponse();

            var repeatActivities = queryActivity.Where(a => (a.Id != request.Id) && a.ReleaseStatus == ActivityReleaseStatus.Published && (a.Sponsor == request.Sponsor && a.Name == request.Name && a.Type == request.Type
            && a.ParentMarketActivityId == null && a.BussinessUnitId != request.BussinessUnitId));
            if (await repeatActivities.AnyAsync())
            {
                checkMarketActivityResponse.RepeatMode = RepeatMode.CompleteOtherBuRepeat;
                checkMarketActivityResponse.ParentMarketActivityId = (await repeatActivities.FirstAsync()).Id;
            }
            checkMarketActivityResponse.RepeatMode = RepeatMode.NoRepeat;
            return MessageResult.SuccessResult(checkMarketActivityResponse);
            /*var repeatActivities = queryActivity.Where(a => (a.Id != request.Id) && a.ReleaseStatus == ActivityReleaseStatus.Published && (a.Sponsor == request.Sponsor || a.Name == request.Name));
            if (await repeatActivities.AnyAsync())
            {
                if (CurrentUser.Id == null)
                {
                    return MessageResult.FailureResult("获取当前用户失败");
                }
                var user = await _identityUserRepository.GetAsync(CurrentUser.Id.Value);
                var departmentId = user.GetProperty<Guid?>(EntityConsts.IdentityUser.MainDepartmentId);
                var allDepartment = await LazyServiceProvider.LazyGetService<IDataverseService>().GetOrganizations(departmentId.ToString(), 1);
                var department = allDepartment.FirstOrDefault();
                if (department == null)
                {
                    return MessageResult.FailureResult("获取当前用户BU失败");
                }

                var repeatSponsorAndNameActivities = repeatActivities.Where(a => a.Sponsor == request.Sponsor && a.Name == request.Name);
                if (await repeatSponsorAndNameActivities.AnyAsync())
                {
                    var sameBuRepeatSponsorAndNameActivities = repeatSponsorAndNameActivities.Where(a => a.BussinessUnitId == department.Id);
                    if (await sameBuRepeatSponsorAndNameActivities.AnyAsync())
                    {
                        var sameBuRepeatAllActivity = await sameBuRepeatSponsorAndNameActivities.FirstOrDefaultAsync(a => a.Type == request.Type);
                        if (sameBuRepeatAllActivity != null)
                        {
                            return MessageResult.FailureResult($"{department.DepartmentName}已存在该活动，请勿重复创建");
                        }
                    }

                    var otherBuRepeatSponsorAndNameActivities = repeatSponsorAndNameActivities.Where(a => a.BussinessUnitId != department.Id);
                    if (await otherBuRepeatSponsorAndNameActivities.AnyAsync())
                    {
                        var otherBuRepeatAllActivity = await otherBuRepeatSponsorAndNameActivities.FirstOrDefaultAsync(a => a.Type == request.Type);
                        if (otherBuRepeatAllActivity != null)
                        {
                            //完全重复
                            checkMarketActivityResponse.RepeatMode = RepeatMode.CompleteOtherBuRepeat;
                            checkMarketActivityResponse.ParentMarketActivityId = otherBuRepeatAllActivity.Id;
                            return MessageResult.SuccessResult(checkMarketActivityResponse);
                        }
                        //部分重复
                        checkMarketActivityResponse.RepeatMode = RepeatMode.PartialOtherBuRepeat;
                        checkMarketActivityResponse.ParentMarketActivityId = (await otherBuRepeatSponsorAndNameActivities.FirstAsync()).Id;
                        return MessageResult.SuccessResult(checkMarketActivityResponse);
                    }
                }

                //主办方相同
                var repeatSponsorAndTypeActivities = repeatActivities.Where(a => a.Sponsor == request.Sponsor && a.Name != request.Name && a.Type == request.Type);
                if (await repeatSponsorAndTypeActivities.AnyAsync())
                {
                    var otherBurepeatSponsorAndTypeActivities = repeatSponsorAndNameActivities.Where(a => a.BussinessUnitId != department.Id);
                    if (await otherBurepeatSponsorAndTypeActivities.AnyAsync())
                    {
                        var otherBurepeatSponsorAndTypeActivity = await otherBurepeatSponsorAndTypeActivities.FirstOrDefaultAsync();
                        if (otherBurepeatSponsorAndTypeActivity != null)
                        {
                            //部分重复
                            checkMarketActivityResponse.RepeatMode = RepeatMode.PartialOtherBuRepeat;
                            checkMarketActivityResponse.ParentMarketActivityId = otherBurepeatSponsorAndTypeActivity.Id;
                            return MessageResult.SuccessResult(checkMarketActivityResponse);
                        }
                    }
                }

                //活动名称相同
                var repeatNameAndTypeActivities = repeatActivities.Where(a => a.Sponsor != request.Sponsor && a.Name == request.Name && a.Type == request.Type);
                if (await repeatNameAndTypeActivities.AnyAsync())
                {
                    var otherBurepeatNameAndTypeActivities = repeatNameAndTypeActivities.Where(a => a.BussinessUnitId != department.Id);
                    if (await otherBurepeatNameAndTypeActivities.AnyAsync())
                    {
                        var otherBurepeatNameAndTypeActivity = await otherBurepeatNameAndTypeActivities.FirstOrDefaultAsync();
                        if (otherBurepeatNameAndTypeActivity != null)
                        {
                            //部分重复
                            checkMarketActivityResponse.RepeatMode = RepeatMode.PartialOtherBuRepeat;
                            checkMarketActivityResponse.ParentMarketActivityId = otherBurepeatNameAndTypeActivity.Id;
                            return MessageResult.SuccessResult(checkMarketActivityResponse);
                        }
                    }
                }
            }*/


        }

        /// <summary>
        /// 创建活动
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> CreateMarketActivityAsync(CreateMarketActivityRequestDto request)
        {
            var activityRepository = LazyServiceProvider.LazyGetService<IActivityRepository>();
            var activityDepartmentRepository = LazyServiceProvider.LazyGetService<IActivityDepartmentRepository>();
            var activityHospitalRepository = LazyServiceProvider.LazyGetService<IActivityHospitalRepository>();
            var activityBuRepository = LazyServiceProvider.LazyGetService<IActivityBuRepository>();

            var bussinessUnitId = request.BussinessUnitId;
            string bussinessUnitName = string.Empty;
            var allDepartment = await LazyServiceProvider.LazyGetService<IDataverseService>().GetOrganizations(bussinessUnitId.ToString(), 1);
            bussinessUnitName = allDepartment.FirstOrDefault()?.DepartmentName;

            var queryActivityBu = await activityBuRepository.GetQueryableAsync();
            if (request.ReleaseStatus == ActivityReleaseStatus.Joined
                && request.ParentMarketActivityId != null
                && await queryActivityBu.Where(a => a.MarketActivityId == request.ParentMarketActivityId.Value).Select(a => a.BuId).ContainsAsync(bussinessUnitId))
            {
                return MessageResult.FailureResult("所属部门已加入该活动，请勿重复创建");
            }

            var activity = new Activity()
            {
                Name = request.Name,
                Type = request.Type,
                Mode = request.Mode,
                StartYear = request.StartYear,
                StartQuarter = request.StartQuarter,
                StartMonth = request.StartMonth,
                StartDay = request.StartDay,
                EndYear = request.EndYear,
                EndQuarter = request.EndQuarter,
                EndMonth = request.EndMonth,
                EndDay = request.EndDay,
                Applicant = request.Applicant,
                Contact = request.Contact,
                CityCode = request.CityCode,
                Location = request.Location,
                Sponsor = request.Sponsor,
                Organizer = request.Organizer,
                Theme = request.Theme,
                Purpose = request.Purpose,
                Url = request.Url,
                BussinessUnitId = bussinessUnitId,
                BussinessUnitName = bussinessUnitName,
                Email = CurrentUser.Email,
                ReleaseStatus = request.ReleaseStatus,
                ParentMarketActivityId = request.ParentMarketActivityId
            };

            if (activity.ReleaseStatus == ActivityReleaseStatus.Published)
            {
                activity.ReleaseTime = DateTime.Now;
            }

            if (!string.IsNullOrEmpty(activity.CityCode))
            {
                var provinces = await _commonService.GetProvinceCity();
                var provinceCities = provinces.FirstOrDefault(a => a.Cities.Exists(b => b.Code == activity.CityCode)).Cities;
                var cityName = provinceCities.FirstOrDefault(a => a.Code == activity.CityCode).Name;
                activity.City = cityName;
            }

            var code = await GenerateCode();
            activity.Code = code.Item1;
            var offices = await _dataverseService.GetAllDepartments();
            var result = await activityRepository.InsertAsync(activity, true);
            if (result != null)
            {
                var queryActivityDepartment = await activityDepartmentRepository.GetQueryableAsync();
                var queryactivityHospital = await activityHospitalRepository.GetQueryableAsync();

                if (request.DepartmentIds.Count > 0)
                {
                    var eventDepartments = new List<ActivityDepartment>();
                    foreach (var item in request.DepartmentIds)
                    {
                        eventDepartments.Add(new ActivityDepartment { MarketActivityId = result.Id, DepartmentId = item, Department = offices.FirstOrDefault(f => f.Id == item)?.Name });
                    }
                    await activityDepartmentRepository.InsertManyAsync(eventDepartments);
                }

                if (!string.IsNullOrEmpty(request.Hospital))
                {
                    var hospitals = request.Hospital.Split(',');
                    var activityHospitals = new List<ActivityHospital>();
                    foreach (var item in hospitals)
                    {
                        activityHospitals.Add(new ActivityHospital { MarketActivityId = result.Id, Hospital = item });
                    }
                    await activityHospitalRepository.InsertManyAsync(activityHospitals);
                }

                //加入活动发邮件
                if (result.ReleaseStatus == ActivityReleaseStatus.Joined && result.ParentMarketActivityId != null)
                {
                    //加入活动，Merge BU
                    //加入活动，将活动BU合并到目标活动的BU数据中
                    var parentMarketActivityId = result.ParentMarketActivityId.Value;

                    var activityBu = await queryActivityBu.FirstOrDefaultAsync(a => a.MarketActivityId == parentMarketActivityId && a.BuId == bussinessUnitId);
                    if (activityBu == null)
                    {
                        var addActivityBu = new ActivityBu
                        {
                            MarketActivityId = parentMarketActivityId,
                            BuId = bussinessUnitId,
                            Bu = bussinessUnitName
                        };
                        await activityBuRepository.InsertAsync(addActivityBu);

                        //加入活动发邮件
                        await SendApplyToJoinEmail(result.Id);
                    }

                    //merge科室数据
                    if (request.DepartmentIds.Count > 0)
                    {
                        var existingDepartments = await queryActivityDepartment.Where(a => a.MarketActivityId == parentMarketActivityId).ToListAsync();
                        var addDepartments = new List<ActivityDepartment>();
                        foreach (var item in request.DepartmentIds)
                        {
                            if (!existingDepartments.Exists(a => a.DepartmentId == item))
                            {
                                addDepartments.Add(new ActivityDepartment { MarketActivityId = parentMarketActivityId, DepartmentId = item, Department = offices.FirstOrDefault(f => f.Id == item)?.Name });
                            }
                        }
                        await activityDepartmentRepository.InsertManyAsync(addDepartments);
                    }

                    //merge医院
                    if (!string.IsNullOrEmpty(request.Hospital))
                    {
                        var hospitals = request.Hospital.Split(',');
                        var existingHospitals = await queryactivityHospital.Where(a => a.MarketActivityId == parentMarketActivityId).ToListAsync();
                        var addHospitals = new List<ActivityHospital>();
                        foreach (var item in hospitals)
                        {
                            if (!existingHospitals.Exists(a => a.Hospital == item))
                            {
                                addHospitals.Add(new ActivityHospital { MarketActivityId = parentMarketActivityId, Hospital = item });
                            }
                        }
                        await activityHospitalRepository.InsertManyAsync(addHospitals);
                    }
                }
                return MessageResult.SuccessResult((result.Id));
            }

            return MessageResult.FailureResult("新增市场活动失败");
        }

        /// <summary>
        /// 生成活动编号
        /// </summary>
        /// <returns></returns>
        public async Task<(string, int)> GenerateCode()
        {
            await using (var handle = await LazyServiceProvider.LazyGetService<IAbpDistributedLock>().TryAcquireAsync($"{DistributedLock.DistributedLock_GenerateSerialNo}_{typeof(Activity).Name}", TimeSpan.FromSeconds(5)))
            {
                if (handle != null)
                {
                    var queryActivity = await LazyServiceProvider.LazyGetService<IActivityRepository>().GetQueryableAsync();
                    var lastActivity = await queryActivity.OrderBy(a => a.CreationTime).FirstOrDefaultAsync();
                    int count;
                    var now = DateTimeOffset.Now.Date;
                    using (DataFilter.Disable<ISoftDelete>())
                    {
                        count = await queryActivity.CountAsync(a => a.CreationTime.Date == now);
                    }
                    //todo:与BPM并行期间，采购申请单号规则调整5000开始（后续可能需要还原）
                    var no = $"M{now:yyyyMMdd}{++count:D4}";
                    return (no, count);
                }
            }

            throw new TimeoutException("生成编号超时，请重新操作");
        }

        /// <summary>
        /// 发送申请加入活动邮件
        /// </summary>
        /// <param name="apply"></param>
        /// <param name="vendorApplicationPersonal"></param>
        /// <returns></returns>
        public async Task SendApplyToJoinEmail(Guid id)
        {
            var webRoot = LazyServiceProvider.LazyGetService<IWebHostEnvironment>().WebRootFileProvider;
            var bodyHtml = await webRoot.GetFileInfo("Templates/Email/ApplyToJoinActivity.html").ReadAsStringAsync();

            var activity = await LazyServiceProvider.LazyGetService<IActivityRepository>().GetAsync(id);
            if (activity.ParentMarketActivityId != null)
            {
                var parentActivity = await LazyServiceProvider.LazyGetService<IActivityRepository>().GetAsync(activity.ParentMarketActivityId.Value);
                if (parentActivity != null)
                {
                    bodyHtml = bodyHtml.Replace("{ApplyName}", CurrentUser.Name);
                    bodyHtml = bodyHtml.Replace("{ApplyEmail}", CurrentUser.Email);
                    bodyHtml = bodyHtml.Replace("{Name}", parentActivity.Name);
                    bodyHtml = bodyHtml.Replace("{Type}", parentActivity.Type);

                    var dateString = parentActivity.StartYear.ToString() + "年"
                        + (parentActivity.StartQuarter.HasValue ? parentActivity.StartQuarter.Value.ToString() + "季度" : "")
                        + (parentActivity.StartMonth.HasValue ? parentActivity.StartMonth.Value.ToString() + "月" : "")
                        + (parentActivity.StartDay.HasValue ? parentActivity.StartDay.Value.ToString() + "日" : "");
                    bodyHtml = bodyHtml.Replace("{Date}", dateString);
                    bodyHtml = bodyHtml.Replace("{City}", parentActivity.City);
                    bodyHtml = bodyHtml.Replace("{Sponsor}", parentActivity.Sponsor);

                    var interfaceUrl = $"/activity/detail?id={activity.ParentMarketActivityId.Value}";
                    bodyHtml = bodyHtml.Replace("{ActivityLink}", $"{_configuration["SpeakerEmail:WebHost"]?.TrimEnd('/')}{interfaceUrl}");
                }

                var user = await _identityUserRepository.FindAsync(parentActivity.CreatorId.Value);
                var sendEmaillRecords = new InsertSendEmaillRecordDto
                {
                    EmailAddress = user.Email,
                    Subject = $"[NexBPM]加入您创建的【{parentActivity.Name}】市场活动",
                    Content = bodyHtml,
                    SourceType = EmailSourceType.ApplyToJoinActivity,
                    Status = SendStatus.Pending,
                    Attempts = 0,
                };

                //记录邮件，并触发邮件发送功能
                var emailService = LazyServiceProvider.LazyGetService<IEmailService>();
                await emailService.InsertSendEmaillRecordAsync([sendEmaillRecords]);
                // 调度作业
                BackgroundJob.Enqueue<SendEmailWorker>(a => a.DoWorkAsync());
            }
        }

        /// <summary>
        /// 更新市场活动数据
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> UpdateMarketActivityAsync(CreateMarketActivityRequestDto request)
        {
            var activityRepository = LazyServiceProvider.LazyGetService<IActivityRepository>();
            var activityDepartmentRepository = LazyServiceProvider.LazyGetService<IActivityDepartmentRepository>();
            var activityHospitalRepository = LazyServiceProvider.LazyGetService<IActivityHospitalRepository>();
            var activityBuRepository = LazyServiceProvider.LazyGetService<IActivityBuRepository>();

            if (request.Id.HasValue)
            {
                var queryActivity = await activityRepository.GetQueryableAsync();
                var activity = await queryActivity.FirstOrDefaultAsync(a => a.Id == request.Id);

                var originalReleaseStatus = activity.ReleaseStatus;
                if (originalReleaseStatus == ActivityReleaseStatus.Draft)
                {
                    if (request.ReleaseStatus == ActivityReleaseStatus.Published)
                    {
                        activity.ReleaseStatus = ActivityReleaseStatus.Published;
                    }
                    else if (request.ReleaseStatus == ActivityReleaseStatus.Joined)
                    {
                        if (request.ParentMarketActivityId.HasValue && (await activityRepository.GetAsync(a => a.Id == request.ParentMarketActivityId.Value)).ReleaseStatus == ActivityReleaseStatus.Published)
                        {
                            activity.ReleaseStatus = ActivityReleaseStatus.Joined;
                        }
                        else
                        {
                            return MessageResult.FailureResult("加入活动的数据为空，或者数据状态不是发布状态，无法加入活动");
                        }
                    }
                }

                if (activity != null)
                {
                    activity.Name = request.Name;
                    activity.Type = request.Type;
                    activity.Mode = request.Mode;
                    activity.StartYear = request.StartYear;
                    activity.StartQuarter = request.StartQuarter;
                    activity.StartMonth = request.StartMonth;
                    activity.StartDay = request.StartDay;
                    activity.EndYear = request.EndYear;
                    activity.EndQuarter = request.EndQuarter;
                    activity.EndMonth = request.EndMonth;
                    activity.EndDay = request.EndDay;
                    activity.Applicant = request.Applicant;
                    activity.Contact = request.Contact;
                    activity.CityCode = request.CityCode;
                    activity.City = request.City;
                    activity.Location = request.Location;
                    activity.Sponsor = request.Sponsor;
                    activity.Organizer = request.Organizer;
                    activity.Theme = request.Theme;
                    activity.Purpose = request.Purpose;
                    activity.Url = request.Url;
                    activity.ParentMarketActivityId = request.ParentMarketActivityId;

                    var result = await activityRepository.UpdateAsync(activity, true);
                    if (result != null)
                    {
                        var queryActivityDepartment = await activityDepartmentRepository.GetQueryableAsync();
                        var queryActivityHospital = await activityHospitalRepository.GetQueryableAsync();
                        var offices = await _dataverseService.GetAllDepartments();

                        if (request.DepartmentIds.Count > 0)
                        {
                            var existingDepartments = await queryActivityDepartment.Where(a => a.MarketActivityId == result.Id).ToListAsync();
                            var activityDepartments = new List<ActivityDepartment>();
                            foreach (var item in request.DepartmentIds)
                            {
                                if (!existingDepartments.Exists(a => a.DepartmentId == item))
                                {
                                    activityDepartments.Add(new ActivityDepartment { MarketActivityId = result.Id, DepartmentId = item, Department = offices.FirstOrDefault(f => f.Id == item)?.Name });
                                }
                            }
                            await activityDepartmentRepository.InsertManyAsync(activityDepartments, true);

                            var deleteDepartments = new List<ActivityDepartment>();
                            foreach (var item in existingDepartments)
                            {
                                if (!request.DepartmentIds.Exists(a => a == item.DepartmentId))
                                {
                                    deleteDepartments.Add(item);
                                }
                            }
                            await activityDepartmentRepository.DeleteManyAsync(deleteDepartments);
                        }

                        if (!string.IsNullOrEmpty(request.Hospital))
                        {
                            var hospitals = request.Hospital.Split(',');
                            var existingHospitals = await queryActivityHospital.Where(a => a.MarketActivityId == result.Id).ToListAsync();
                            var addHospitals = new List<ActivityHospital>();
                            foreach (var item in hospitals)
                            {
                                if (!existingHospitals.Exists(a => a.Hospital == item))
                                {
                                    addHospitals.Add(new ActivityHospital { MarketActivityId = result.Id, Hospital = item });
                                }
                            }
                            await activityHospitalRepository.InsertManyAsync(addHospitals, true);

                            var deleteHospitals = new List<ActivityHospital>();
                            foreach (var item in existingHospitals)
                            {
                                if (!hospitals.Contains(item.Hospital))
                                {
                                    deleteHospitals.Add(item);
                                }
                            }
                            await activityHospitalRepository.DeleteManyAsync(deleteHospitals);
                        }

                        //草稿=>加入活动
                        if (originalReleaseStatus == ActivityReleaseStatus.Draft && request.ReleaseStatus == ActivityReleaseStatus.Joined)
                        {
                            Guid? bussinessUnitId = null;
                            string bussinessUnitName = string.Empty;

                            var userId = activity.CreatorId;
                            if (userId != null)
                            {
                                var user = await _identityUserRepository.GetAsync(userId.Value);

                                var departmentId = user.GetProperty<Guid?>(EntityConsts.IdentityUser.MainDepartmentId);
                                var allDepartment = await LazyServiceProvider.LazyGetService<IDataverseService>().GetOrganizations(departmentId.ToString(), 1);

                                var department = allDepartment.FirstOrDefault();
                                if (department != null)
                                {
                                    bussinessUnitId = department.Id;
                                    bussinessUnitName = department.DepartmentName;
                                }
                                else
                                {
                                    return MessageResult.FailureResult("加入活动获取用户BU信息失败");
                                }
                            }

                            //加入活动，将活动BU合并到目标活动的BU数据中
                            var parentMarketActivityId = result.ParentMarketActivityId.Value;
                            var queryActivityBu = await activityBuRepository.GetQueryableAsync();
                            var activityBu = await queryActivityBu.FirstOrDefaultAsync(a => a.MarketActivityId == parentMarketActivityId && a.BuId == bussinessUnitId);
                            if (activityBu == null)
                            {
                                var addActivityBu = new ActivityBu
                                {
                                    MarketActivityId = parentMarketActivityId,
                                    BuId = bussinessUnitId.Value,
                                    Bu = bussinessUnitName
                                };
                                await activityBuRepository.InsertAsync(addActivityBu);

                                //加入活动发邮件
                                await SendApplyToJoinEmail(result.Id);
                            }

                            //merge科室数据
                            if (request.DepartmentIds.Count > 0)
                            {
                                var existingDepartments = await queryActivityDepartment.Where(a => a.MarketActivityId == parentMarketActivityId).ToListAsync();
                                var addDepartments = new List<ActivityDepartment>();
                                foreach (var item in request.DepartmentIds)
                                {
                                    if (!existingDepartments.Exists(a => a.DepartmentId == item))
                                    {
                                        addDepartments.Add(new ActivityDepartment { MarketActivityId = parentMarketActivityId, DepartmentId = item, Department = offices.FirstOrDefault(f => f.Id == item)?.Name });
                                    }
                                }
                                await activityDepartmentRepository.InsertManyAsync(addDepartments);
                            }

                            //merge医院
                            if (!string.IsNullOrEmpty(request.Hospital))
                            {
                                var hospitals = request.Hospital.Split(',');
                                var existingHospitals = await queryActivityHospital.Where(a => a.MarketActivityId == parentMarketActivityId).ToListAsync();
                                var addHospitals = new List<ActivityHospital>();
                                foreach (var item in hospitals)
                                {
                                    if (!existingHospitals.Exists(a => a.Hospital == item))
                                    {
                                        addHospitals.Add(new ActivityHospital { MarketActivityId = parentMarketActivityId, Hospital = item });
                                    }
                                }
                                await activityHospitalRepository.InsertManyAsync(addHospitals);
                            }
                        }

                        //编辑加入活动,医院，科室。
                        if (originalReleaseStatus == ActivityReleaseStatus.Joined && request.ReleaseStatus == ActivityReleaseStatus.Joined)
                        {
                            if (result.ParentMarketActivityId.HasValue)
                            {
                                var parentMarketActivityId = result.ParentMarketActivityId.Value;
                                var activityIds = queryActivity.Where(a => a.Id != request.Id && a.ParentMarketActivityId == parentMarketActivityId).Select(a => a.Id).Distinct();
                                if (request.DepartmentIds.Count > 0)
                                {
                                    var existingDepartments = await queryActivityDepartment.Where(a => a.MarketActivityId == parentMarketActivityId).ToListAsync();
                                    var activityDepartments = new List<ActivityDepartment>();
                                    foreach (var item in request.DepartmentIds)
                                    {
                                        if (!existingDepartments.Exists(a => a.DepartmentId == item))
                                        {
                                            activityDepartments.Add(new ActivityDepartment { MarketActivityId = parentMarketActivityId, DepartmentId = item, Department = offices.FirstOrDefault(f => f.Id == item)?.Name });
                                        }
                                    }
                                    await activityDepartmentRepository.InsertManyAsync(activityDepartments, true);

                                    var joinActivityDepartmentIds = await queryActivityDepartment.Where(a => activityIds.Contains(a.MarketActivityId)).Select(a => a.DepartmentId).Distinct().ToListAsync();
                                    var deleteDepartments = new List<ActivityDepartment>();
                                    foreach (var item in existingDepartments)
                                    {
                                        if (!request.DepartmentIds.Contains(item.DepartmentId) && !joinActivityDepartmentIds.Contains(item.DepartmentId))
                                        {
                                            deleteDepartments.Add(item);
                                        }
                                    }
                                    await activityDepartmentRepository.DeleteManyAsync(deleteDepartments);
                                }

                                if (!string.IsNullOrEmpty(request.Hospital))
                                {
                                    var hospitals = request.Hospital.Split(',');
                                    var existingHospitals = await queryActivityHospital.Where(a => a.MarketActivityId == parentMarketActivityId).ToListAsync();
                                    var addHospitals = new List<ActivityHospital>();
                                    foreach (var item in hospitals)
                                    {
                                        if (!existingHospitals.Exists(a => a.Hospital == item))
                                        {
                                            addHospitals.Add(new ActivityHospital { MarketActivityId = parentMarketActivityId, Hospital = item });
                                        }
                                    }
                                    await activityHospitalRepository.InsertManyAsync(addHospitals, true);

                                    var joinActivityHospitals = await queryActivityHospital.Where(a => activityIds.Contains(a.MarketActivityId)).Select(a => a.Hospital).Distinct().ToListAsync();
                                    var deleteHospitals = new List<ActivityHospital>();
                                    foreach (var item in existingHospitals)
                                    {
                                        if (!hospitals.Contains(item.Hospital) && !joinActivityHospitals.Contains(item.Hospital))
                                        {
                                            deleteHospitals.Add(item);
                                        }
                                    }
                                    await activityHospitalRepository.DeleteManyAsync(deleteHospitals);
                                }
                            }
                        }
                        return MessageResult.SuccessResult(result.Id);
                    }
                }
            }

            return MessageResult.FailureResult("新增市场活动失败");
        }

        /// <summary>
        /// 修改市场活动状态
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> ModifyMarketActivityStatusAsync(UpdateMarketActivityStatusRequestDto request)
        {
            var activityRepository = LazyServiceProvider.LazyGetService<IActivityRepository>();
            var id = request.Id;
            var originalActivity = await activityRepository.GetAsync(id);

            //下架发布的活动，同时处理加入他的活动也变成下架状态。
            if (originalActivity.ReleaseStatus == ActivityReleaseStatus.Published && request.Status == ActivityReleaseStatus.Removed)
            {
                if (CurrentUser.Id == originalActivity.CreatorId)
                {
                    originalActivity.ReleaseStatus = ActivityReleaseStatus.Removed;
                    await activityRepository.UpdateAsync(originalActivity);

                    var queryactivity = await activityRepository.GetQueryableAsync();
                    var activities = queryactivity.Where(a => a.ParentMarketActivityId == id && a.ReleaseStatus == ActivityReleaseStatus.Joined);
                    foreach (var item in activities)
                    {
                        item.ReleaseStatus = ActivityReleaseStatus.Removed;
                    }
                    await activityRepository.UpdateManyAsync(activities);

                    return MessageResult.SuccessResult();
                }
                else
                {
                    return MessageResult.FailureResult("只能下架自己创建的活动");
                }
            }

            return MessageResult.FailureResult("不支持的状态变更");
        }

        /// <summary>
        /// 删除市场活动数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<MessageResult> DeleteMarketActivityAsync(Guid? id)
        {
            if (id == null)
            {
                return MessageResult.FailureResult("未传入数据Id,删除市场活动失败");
            }

            var activityRepository = LazyServiceProvider.LazyGetService<IActivityRepository>();
            var activityDepartmentRepository = LazyServiceProvider.LazyGetService<IActivityDepartmentRepository>();
            var activityBuRepository = LazyServiceProvider.LazyGetService<IActivityBuRepository>();
            var activityUserInformationRepository = LazyServiceProvider.LazyGetService<IActivityUserInformationRepository>();


            var queryActivity = await activityRepository.GetQueryableAsync();
            var queryActivityDepartment = await activityDepartmentRepository.GetQueryableAsync();
            var queryActivityBu = await activityBuRepository.GetQueryableAsync();
            var queryActivityUserInformation = await activityUserInformationRepository.GetQueryableAsync();



            var activity = await queryActivity.FirstOrDefaultAsync(a => a.Id == id);
            if (activity == null)
            {
                return MessageResult.FailureResult("未查询到市场活动数据,删除市场活动失败");
            }

            await activityRepository.DeleteAsync(activity);

            //删除关联
            var activityDepartments = queryActivityDepartment.Where(a => a.Id == id);
            if (activityDepartments != null)
            {
                await activityDepartmentRepository.DeleteManyAsync(activityDepartments);
            }

            var activityBus = queryActivityBu.Where(a => a.Id == id);
            if (activityBus != null)
            {
                await activityBuRepository.DeleteManyAsync(activityBus);
            }

            var activityUserInformationS = queryActivityUserInformation.Where(a => a.Id == id);
            if (activityUserInformationS != null)
            {
                await activityUserInformationRepository.DeleteManyAsync(activityUserInformationS);
            }

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 获取市场活动详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<QueryMarketActivityResponseDto> GetMarketActivityAsync(Guid? id)
        {
            var queryActivity = await LazyServiceProvider.LazyGetService<IActivityRepository>().GetQueryableAsync();
            var queryActivityDepartment = await LazyServiceProvider.LazyGetService<IActivityDepartmentRepository>().GetQueryableAsync();
            var queryActivityHospital = await LazyServiceProvider.LazyGetService<IActivityHospitalRepository>().GetQueryableAsync();
            var queryActivityBu = await LazyServiceProvider.LazyGetService<IActivityBuRepository>().GetQueryableAsync();

            var activity = await queryActivity.FirstOrDefaultAsync(a => a.Id == id);
            if (activity == null)
            {
                return new QueryMarketActivityResponseDto();
            }

            var result = new QueryMarketActivityResponseDto
            {
                Id = activity.Id,
                Code = activity.Code,
                Name = activity.Name,
                Type = activity.Type,
                Mode = activity.Mode,
                StartYear = activity.StartYear,
                StartQuarter = activity.StartQuarter,
                StartMonth = activity.StartMonth,
                StartDay = activity.StartDay,
                EndYear = activity.EndYear,
                EndQuarter = activity.EndQuarter,
                EndMonth = activity.EndMonth,
                EndDay = activity.EndDay,
                City = activity.City,
                CityCode = activity.CityCode,
                Location = activity.Location,
                Applicant = activity.Applicant,
                Contact = activity.Contact,
                Sponsor = activity.Sponsor,
                Organizer = activity.Organizer,
                Hospital = activity.Hospital,
                Theme = activity.Theme,
                Purpose = activity.Purpose,
                Url = activity.Url,
                ReleaseStatus = activity.ReleaseStatus,
                ReleaseTime = activity.ReleaseTime,
                CreatorId = activity.CreatorId,
                BussinessUnitId = activity.BussinessUnitId,
                BussinessUnitName = activity.BussinessUnitName.ClearBu(),
                ParentMarketActivityId = activity.ParentMarketActivityId,
                ContactName = activity.Contact,
                ApplicantName = activity.Applicant
            };
            var listRequests = new List<GraphBatchRequestDto.Request>();
            var getTokenResult = await GetToken(_configuration);
            if (getTokenResult.Success)
            {
                var resource = _configuration["Integrations:Graph:Resource"];
                if (!string.IsNullOrEmpty(result.Applicant))
                    listRequests.Add(new GraphBatchRequestDto.Request
                    {
                        Id = result.Applicant,
                        Method = HttpMethods.Get,
                        Url = $"/users/{result.Applicant}?$select=displayName"
                    });
                if (!string.IsNullOrEmpty(result.Contact) && !result.Applicant.Equals(result.Contact))
                    listRequests.Add(new GraphBatchRequestDto.Request
                    {
                        Id = result.Contact,
                        Method = HttpMethods.Get,
                        Url = $"/users/{result.Contact}?$select=accountEnabled,displayName"
                    });
                //批量执行request
                var url = $"{resource}/v1.0/$batch";
                string responseData = string.Empty;
                try
                {
                    var request = new GraphBatchRequestDto { Requests = listRequests };
                    var response = await url.WithHeader("Authorization", $"Bearer {getTokenResult.Data}").PostAsync(JsonContent.Create(request));
                    responseData = await response.GetStringAsync();
                    var jsonSerializerOptions = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };

                    var batchResponse = JsonSerializer.Deserialize<GraphBatchResponseDto>(responseData, jsonSerializerOptions);
                    ////对批量返回的结果进行匹配

                    var body1 = batchResponse.Responses.FirstOrDefault(f => f.Id == result.Applicant && f.Status == (int)HttpStatusCode.OK)?.Body;
                    if (body1 != null)
                    {
                        var successBody1 = JsonSerializer.Deserialize<GraphBatchResponseDto.SuccessBody>(body1.ToString(), jsonSerializerOptions);
                        result.ApplicantName = successBody1.DisplayName;
                    }
                    var body2 = batchResponse.Responses.FirstOrDefault(f => f.Id == result.Contact && f.Status == (int)HttpStatusCode.OK)?.Body;
                    if (body2 != null)
                    {
                        var successBody2 = JsonSerializer.Deserialize<GraphBatchResponseDto.SuccessBody>(body2.ToString(), jsonSerializerOptions);
                        result.ContactName = successBody2.DisplayName;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError($"Integrations:Graph--{ex.Message}");
                }
            }
            result.Departments = (await queryActivityDepartment.Where(a => a.MarketActivityId == result.Id).ToListAsync()).DistinctBy(a => a.DepartmentId).ToDictionary(a => a.DepartmentId, a => a.Department.ClearBu());
            result.DepartmentNames = await queryActivityDepartment.Where(a => a.MarketActivityId == result.Id).Select(a => a.Department.ClearBu()).Distinct().ToListAsync();

            result.Hospitals = await queryActivityHospital.Where(a => a.MarketActivityId == result.Id).Select(a => a.Hospital).Distinct().ToListAsync();
            result.Hospital = string.Join(",", result.Hospitals);

            if (result.ParentMarketActivityId != null)
            {
                var parentMarketActivityId = result.ParentMarketActivityId.Value;
                result.OwningBusinessUnitNames = (await queryActivityBu.Where(a => a.MarketActivityId == parentMarketActivityId).Select(a => a.Bu).Distinct().ToListAsync()).Select(s => s.ClearBu()).ToList();

                var parentMarketActivity = await queryActivity.FirstOrDefaultAsync(a => a.Id == parentMarketActivityId);
                if (!result.OwningBusinessUnitNames.Contains(parentMarketActivity.BussinessUnitName.ClearBu()))
                {
                    result.OwningBusinessUnitNames.Add(parentMarketActivity.BussinessUnitName.ClearBu());
                }
            }
            else
            {
                result.OwningBusinessUnitNames = (await queryActivityBu.Where(a => a.MarketActivityId == result.Id).Select(a => a.Bu.ClearBu()).Distinct().ToListAsync()).Select(s => s.ClearBu()).ToList();
            }

            //user
            var userIds = new List<Guid>() { result.CreatorId.Value };
            var users = await _identityUserRepository.GetListByIdsAsync(userIds);
            result.ReleaseUserName = users.FirstOrDefault()?.Name;

            return result;
        }

        /// <summary>
        /// 验证转换ecxel数据
        /// </summary>
        /// <param name="Rows"></param>
        /// <returns></returns>
        public async Task<MessageResult> VarifyImportMarketActivityAsync(List<TransferMarketActivityDto> Rows)
        {
            var queryActivity = await LazyServiceProvider.LazyGetService<IActivityRepository>().GetQueryableAsync();
            var activityIds = Rows.Where(a => a.Id != null).Select(a => a.Id.Value).Distinct().ToList();
            var activities = queryActivity.Where(a => activityIds.Contains(a.Id));
            //活动名称
            var names = Rows.Select(s => s.Name).Distinct().ToArray();
            //活动类型
            var types = Rows.Select(s => s.Type).Distinct().ToArray();
            //活动主办方
            var sponsors = Rows.Select(s => s.Sponsor).Distinct().ToArray();
            var queryExist = await queryActivity.WhereIf(names.Length > 0, m => names.Contains(m.Name)).WhereIf(types.Length > 0, m => types.Contains(m.Type)).WhereIf(sponsors.Length > 0, m => sponsors.Contains(m.Sponsor)
            ).ToListAsync();

            var sameBuQuery = queryExist.Where(m => m.BussinessUnitId == Rows[0].BussinessUnitId);
            var notSameBuQuery = queryExist.Where(m => m.BussinessUnitId != Rows[0].BussinessUnitId && m.ParentMarketActivityId == null && m.ReleaseStatus == ActivityReleaseStatus.Published);

            var allCity = await LazyServiceProvider.LazyGetService<IDataverseService>().GetAllCity();
            var cityNames = Rows.Select(a => a.City).Distinct().ToList();
            var cities = allCity.Where(a => cityNames.Contains(a.Name));

            var allDepartment = await _dataverseService.GetAllDepartments();
            var departmentNames = Rows.SelectMany(a => a.Department).Distinct().ToList();
            var departments = allDepartment.Where(a => departmentNames.Contains(a.Name));

            string pattern = @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$";
            Regex regex = new(pattern, RegexOptions.IgnoreCase);

            var rowNo = 1;
            foreach (var item in Rows)
            {
                rowNo++;
                var message = string.Empty;
                item.No = rowNo;

                if (item.Id != null && !(await activities.AnyAsync(a => a.Id == item.Id)))
                {
                    message += "隐藏的A列活动Id无效，请删除Id；";
                }

                if (string.IsNullOrEmpty(item.Name))
                {
                    message += "活动名称为空；";
                }

                if (string.IsNullOrEmpty(item.Type))
                {
                    message += "活动类型为空；";
                }

                if (string.IsNullOrEmpty(item.Mode))
                {
                    message += "活动形式为空；";
                }

                if (string.IsNullOrEmpty(item.StartYearExcel))
                {
                    message += "活动开始时间-年为空";
                }
                else
                {
                    if (int.TryParse(item.StartYearExcel, out int toInt))
                    {
                        item.StartYear = toInt;
                    }
                    else
                    {
                        message += "活动开始时间-年 数据不合法，填写整数数据；";
                    }
                }

                if (!string.IsNullOrEmpty(item.StartQuarterExcel))
                {
                    if (int.TryParse(item.StartQuarterExcel, out int toInt) && toInt >= 1 && toInt <= 4)
                    {
                        item.StartQuarter = toInt;
                    }
                    else
                    {
                        message += "活动开始时间-季度 数据不合法，填写1-4整数数据；";
                    }
                }

                if (!string.IsNullOrEmpty(item.StartMonthExcel))
                {
                    if (int.TryParse(item.StartMonthExcel, out int toInt) && toInt >= 1 && toInt <= 12)
                    {
                        item.StartMonth = toInt;
                    }
                    else
                    {
                        message += "活动开始时间-月 数据不合法，填写1-12整数数据；";
                    }
                }

                if (!string.IsNullOrEmpty(item.StartDayExcel))
                {
                    if (int.TryParse(item.StartDayExcel, out int toInt) && toInt >= 1 && toInt <= 31)
                    {
                        item.StartDay = toInt;
                    }
                    else
                    {
                        message += "活动开始时间-日 数据不合法，填写1-31整数数据；";
                    }
                }

                if (!string.IsNullOrEmpty(item.EndYearExcel))
                {
                    if (int.TryParse(item.EndYearExcel, out int toInt))
                    {
                        item.EndYear = toInt;
                    }
                    else
                    {
                        message += "活动结束时间-年 数据不合法，填写整数数据；";
                    }
                }

                if (!string.IsNullOrEmpty(item.EndQuarterExcel))
                {
                    if (int.TryParse(item.EndQuarterExcel, out int toInt) && toInt >= 1 && toInt <= 4)
                    {
                        item.EndQuarter = toInt;
                    }
                    else
                    {
                        message += "活动结束时间-季度 数据不合法，填写1-4整数数据；";
                    }
                }

                if (!string.IsNullOrEmpty(item.EndMonthExcel))
                {
                    if (int.TryParse(item.EndMonthExcel, out int toInt) && toInt >= 1 && toInt <= 12)
                    {
                        item.EndMonth = toInt;
                    }
                    else
                    {
                        message += "活动结束时间-月 数据不合法，填写1-12整数数据；";
                    }
                }

                if (!string.IsNullOrEmpty(item.EndDayExcel))
                {
                    if (int.TryParse(item.EndDayExcel, out int toInt) && toInt >= 1 && toInt <= 31)
                    {
                        item.EndDay = toInt;
                    }
                    else
                    {
                        message += "活动结束时间-日 数据不合法，填写1-31整数数据；";
                    }
                }

                if (!string.IsNullOrEmpty(item.Applicant) && !regex.IsMatch(item.Applicant))
                {
                    message += "请填写正确的活动联系人1邮箱";
                }

                if (!string.IsNullOrEmpty(item.Contact) && !regex.IsMatch(item.Contact))
                {
                    message += "请填写正确的活动联系人2邮箱";
                }

                if (!string.IsNullOrEmpty(item.City))
                {
                    item.CityCode = cities.FirstOrDefault(a => item.City == a.Name)?.Code;
                }

                if (!string.IsNullOrEmpty(item.Hospital))
                {
                    item.Hospitals = item.Hospital.Split(',').ToList();
                }

                if (item.Department != null)
                {
                    item.Departments = departments.Where(a => item.Department.Contains(a.Name)).DistinctBy(a => a.Id).Select(a => new { Key = a.Id, Value = a.Name }).ToDictionary(a => a.Key, a => a.Value);
                }

                var existSameBu = sameBuQuery.FirstOrDefault(f => f.Name == item.Name && f.Type == item.Type && f.Sponsor == item.Sponsor && item.Id != f.Id);
                if (existSameBu != null)
                {
                    message += "该Bu下已存在相同活动数据";
                }

                //var createMarketActivityRequestDto = new CreateMarketActivityRequestDto()
                //{
                //    Name = item.Name,
                //    Sponsor = item.Sponsor,
                //    Type = item.Type
                //};

                //var messageResult = await CheckMarketActivityAsync(createMarketActivityRequestDto);
                //if (messageResult.Success)
                //{
                //    var checkResult = (CheckMarketActivityResponse)messageResult.Data;
                //    if (checkResult != null)
                //    {
                //        item.RepeatMode = checkResult.RepeatMode;
                //        item.ParentMarketActivityId = checkResult.ParentMarketActivityId;
                //    }
                //}
                //else
                //{
                //    item.ErrorMessage = messageResult.Message;
                //}
                Activity existActivity = null;
                if (item.Id.HasValue)
                    existActivity = notSameBuQuery.FirstOrDefault(f => f.Name == item.Name && f.Type == item.Type && f.Sponsor == item.Sponsor && item.Id != f.Id);
                else
                    existActivity = notSameBuQuery.FirstOrDefault(f => f.Name == item.Name && f.Type == item.Type && f.Sponsor == item.Sponsor);
                if (existActivity != null)
                {
                    ////var existSameBu = queryExist.FirstOrDefault(f => f.Name == item.Name && f.Type == item.Type && f.Sponsor == item.Sponsor && f.BussinessUnitId == item.BussinessUnitId);
                    //if (existActivity.Id == item.Id)
                    //{
                    //    //item.Id = exist.Id;
                    //    item.RepeatMode = RepeatMode.NoRepeat;
                    //}
                    //else
                    //{
                    item.RepeatMode = RepeatMode.CompleteOtherBuRepeat;
                    item.ParentMarketActivityId = existActivity.Id;
                    //}
                }
                else
                {
                    item.RepeatMode = RepeatMode.NoRepeat;
                }
                item.ErrorMessage = message;
            }

            return MessageResult.SuccessResult(Rows);
        }

        /// <summary>
        /// 批量提交市场活动
        /// </summary>
        /// <param name="messageDtos"></param>
        /// <returns></returns>
        public async Task<MessageResult> ImportMarketActivityAsync(List<TransferMarketActivityDto> Rows)
        {
            var activityRepository = LazyServiceProvider.LazyGetService<IActivityRepository>();
            var activityDepartmentRepository = LazyServiceProvider.LazyGetService<IActivityDepartmentRepository>();
            var activityHospitalRepository = LazyServiceProvider.LazyGetService<IActivityHospitalRepository>();
            var activityBuRepository = LazyServiceProvider.LazyGetService<IActivityBuRepository>();

            var bussinessUnitId = Rows[0].BussinessUnitId;
            string bussinessUnitName = string.Empty;
            var allDepartment = await LazyServiceProvider.LazyGetService<IDataverseService>().GetOrganizations(bussinessUnitId.ToString(), 1);
            bussinessUnitName = allDepartment.FirstOrDefault()?.DepartmentName;

            //新增数据
            var addRows = Rows.Where(a => a.Id == null);
            var (code, count) = await GenerateCode();
            foreach (var row in addRows)
            {
                var activity = new Activity()
                {
                    Name = row.Name,
                    Type = row.Type,
                    Mode = row.Mode,
                    StartYear = row.StartYear,
                    StartQuarter = row.StartQuarter,
                    StartMonth = row.StartMonth,
                    StartDay = row.StartDay,
                    EndYear = row.EndYear,
                    EndQuarter = row.EndQuarter,
                    EndMonth = row.EndMonth,
                    EndDay = row.EndDay,
                    Applicant = row.Applicant,
                    Contact = row.Contact,
                    CityCode = row.CityCode,
                    City = row.City,
                    Location = row.Location,
                    Sponsor = row.Sponsor,
                    Organizer = row.Organizer,
                    Theme = row.Theme,
                    Purpose = row.Purpose,
                    Url = row.Url,
                    BussinessUnitId = bussinessUnitId,
                    BussinessUnitName = bussinessUnitName,
                    Email = CurrentUser.Email,
                    ReleaseStatus = row.ReleaseStatus,
                    ParentMarketActivityId = row.ParentMarketActivityId
                };

                if (activity.ReleaseStatus == ActivityReleaseStatus.Published)
                {
                    activity.ReleaseTime = DateTime.Now;
                }

                activity.Code = $"M{DateTime.Now:yyyyMMdd}{count:D4}";
                count++;

                var result = await activityRepository.InsertAsync(activity, true);
                if (result != null)
                {
                    var queryActivityDepartment = await activityDepartmentRepository.GetQueryableAsync();
                    var queryactivityHospital = await activityHospitalRepository.GetQueryableAsync();
                    var queryActivityBu = await activityBuRepository.GetQueryableAsync();

                    if (row.Departments != null && row.Departments.Count > 0)
                    {
                        var addDepartments = new List<ActivityDepartment>();
                        foreach (var item in row.Departments)
                        {
                            addDepartments.Add(new ActivityDepartment { MarketActivityId = result.Id, DepartmentId = item.Key, Department = item.Value });
                        }
                        await activityDepartmentRepository.InsertManyAsync(addDepartments);
                    }

                    if (row.Hospitals != null && row.Hospitals.Count > 0)
                    {
                        var addHospitals = new List<ActivityHospital>();
                        foreach (var item in row.Hospitals)
                        {
                            addHospitals.Add(new ActivityHospital { MarketActivityId = result.Id, Hospital = item });
                        }
                        await activityHospitalRepository.InsertManyAsync(addHospitals);
                    }

                    //加入活动发邮件
                    if (result.ParentMarketActivityId != null)
                    {
                        result.ReleaseStatus = ActivityReleaseStatus.Joined;
                        //加入活动，Merge BU
                        //加入活动，将活动BU合并到目标活动的BU数据中
                        var parentMarketActivityId = result.ParentMarketActivityId.Value;
                        var activityBu = await queryActivityBu.FirstOrDefaultAsync(a => a.MarketActivityId == parentMarketActivityId && a.BuId == bussinessUnitId);
                        if (activityBu == null)
                        {
                            var addActivityBu = new ActivityBu
                            {
                                MarketActivityId = parentMarketActivityId,
                                BuId = bussinessUnitId,
                                Bu = bussinessUnitName
                            };
                            await activityBuRepository.InsertAsync(addActivityBu);
                        }

                        //merge科室数据
                        if (row.Departments != null && row.Departments.Count > 0)
                        {
                            var existingDepartments = await queryActivityDepartment.Where(a => a.MarketActivityId == parentMarketActivityId).ToListAsync();
                            var addDepartments = new List<ActivityDepartment>();
                            foreach (var item in row.Departments)
                            {
                                if (!existingDepartments.Exists(a => a.DepartmentId == item.Key))
                                {
                                    addDepartments.Add(new ActivityDepartment { MarketActivityId = parentMarketActivityId, DepartmentId = item.Key, Department = item.Value });
                                }
                            }
                            await activityDepartmentRepository.InsertManyAsync(addDepartments);
                        }

                        //merge医院
                        if (row.Hospitals != null && row.Hospitals.Count > 0)
                        {
                            var existingHospitals = await queryactivityHospital.Where(a => a.MarketActivityId == parentMarketActivityId).ToListAsync();
                            var addHospitals = new List<ActivityHospital>();
                            foreach (var item in row.Hospitals)
                            {
                                if (!existingHospitals.Exists(a => a.Hospital == item))
                                {
                                    addHospitals.Add(new ActivityHospital { MarketActivityId = parentMarketActivityId, Hospital = item });
                                }
                            }
                            await activityHospitalRepository.InsertManyAsync(addHospitals);
                        }
                    }
                }
            }

            //更新数据
            var updateRows = Rows.Where(a => a.Id != null);
            foreach (var row in updateRows)
            {
                var activity = await activityRepository.GetAsync(row.Id.Value);
                if (activity != null)
                {
                    activity.Name = row.Name;
                    activity.Type = row.Type;
                    activity.Mode = row.Mode;
                    activity.StartYear = row.StartYear;
                    activity.StartQuarter = row.StartQuarter;
                    activity.StartMonth = row.StartMonth;
                    activity.StartDay = row.StartDay;
                    activity.EndYear = row.EndYear;
                    activity.EndQuarter = row.EndQuarter;
                    activity.EndMonth = row.EndMonth;
                    activity.EndDay = row.EndDay;
                    activity.Applicant = row.Applicant;
                    activity.Contact = row.Contact;
                    activity.CityCode = row.CityCode;
                    activity.City = row.City;
                    activity.Location = row.Location;
                    activity.Sponsor = row.Sponsor;
                    activity.Organizer = row.Organizer;
                    activity.Theme = row.Theme;
                    activity.Purpose = row.Purpose;
                    activity.Url = row.Url;

                    var result = await activityRepository.UpdateAsync(activity, true);
                    if (result != null)
                    {
                        var queryActivityDepartment = await activityDepartmentRepository.GetQueryableAsync();
                        var queryActivityHospital = await activityHospitalRepository.GetQueryableAsync();

                        if (row.Departments?.Count != null && row.Departments.Count > 0)
                        {
                            var existingDepartments = await queryActivityDepartment.Where(a => a.MarketActivityId == result.Id).ToListAsync();
                            var addDepartments = new List<ActivityDepartment>();
                            foreach (var item in row.Departments)
                            {
                                if (!existingDepartments.Exists(a => a.DepartmentId == item.Key))
                                {
                                    addDepartments.Add(new ActivityDepartment { MarketActivityId = result.Id, DepartmentId = item.Key, Department = item.Value });
                                }
                            }
                            await activityDepartmentRepository.InsertManyAsync(addDepartments, true);
                        }

                        if (row.Hospitals != null && row.Hospitals.Count > 0)
                        {
                            var existingHospitals = await queryActivityHospital.Where(a => a.MarketActivityId == result.Id).ToListAsync();
                            var addHospitals = new List<ActivityHospital>();
                            foreach (var item in row.Hospitals)
                            {
                                if (!existingHospitals.Exists(a => a.Hospital == item))
                                {
                                    addHospitals.Add(new ActivityHospital { MarketActivityId = result.Id, Hospital = item });
                                }
                            }
                            await activityHospitalRepository.InsertManyAsync(addHospitals, true);

                            var deleteHospitals = new List<ActivityHospital>();
                            foreach (var item in existingHospitals)
                            {
                                if (!row.Hospitals.Contains(item.Hospital))
                                {
                                    deleteHospitals.Add(item);
                                }
                            }
                            await activityHospitalRepository.DeleteManyAsync(deleteHospitals);
                        }
                    }
                }
            }

            return MessageResult.SuccessResult("批量导入市场活动成功");
        }

        /// <summary>
        /// 收藏活动
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> UpdateMarketActivityUserInformationAsync(UpdateMarketActivityUserInformationDto request)
        {
            var activityUserInformationRepository = LazyServiceProvider.LazyGetService<IActivityUserInformationRepository>();

            var queryActivityUserInformation = await activityUserInformationRepository.GetQueryableAsync();
            if (request.Id.HasValue)
            {
                var activityUserInformation = await queryActivityUserInformation.FirstOrDefaultAsync(a => a.Id == request.Id);
                if (activityUserInformation != null)
                {
                    activityUserInformation.IsBookmark = request.IsBookmark;

                    var result = await activityUserInformationRepository.UpdateAsync(activityUserInformation);
                    if (result != null)
                    {
                        return MessageResult.SuccessResult((result.Id));
                    }
                }
            }
            else
            {
                if (CurrentUser.Id != null)
                {
                    var activityUserInformation = new ActivityUserInformation()
                    {
                        UserId = CurrentUser.Id.Value,
                        MarketActivityId = request.MarketActivityId,
                        IsBookmark = request.IsBookmark
                    };
                    var result = await activityUserInformationRepository.InsertAsync(activityUserInformation);
                    if (result != null)
                    {
                        return MessageResult.SuccessResult((result.Id));
                    }
                }
            }
            return MessageResult.FailureResult("更新市场活动用户信息失败");
        }

        /// <summary>
        /// 获取用户的收藏
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<MessageResult> GetMarketActivityUserInformationAsync(Guid? id)
        {
            if (id == null)
            {
                return MessageResult.FailureResult("未传入市场活动Id,获取活动数据失败");
            }
            if (CurrentUser.Id == null)
            {
                return MessageResult.FailureResult("获取当前用户失败");
            }

            var userId = CurrentUser.Id.Value;
            Guid? bussinessUnitId = null;
            var user = await _identityUserRepository.GetAsync(userId);

            var departmentId = user.GetProperty<Guid?>(EntityConsts.IdentityUser.MainDepartmentId);
            var allDepartment = await LazyServiceProvider.LazyGetService<IDataverseService>().GetOrganizations(departmentId.ToString(), 1);

            var department = allDepartment.FirstOrDefault();
            if (department != null)
            {
                bussinessUnitId = department.Id;
            }

            var queryActivityBu = await LazyServiceProvider.LazyGetService<IActivityBuRepository>().GetQueryableAsync();
            var activityBu = await queryActivityBu.FirstOrDefaultAsync(a => a.MarketActivityId == id && a.BuId == bussinessUnitId);

            var queryActivityUserInformation = await LazyServiceProvider.LazyGetService<IActivityUserInformationRepository>().GetQueryableAsync();
            var activityUserInformation = await queryActivityUserInformation.FirstOrDefaultAsync(a => a.MarketActivityId == id && a.UserId == CurrentUser.Id);

            var result = new GetMarketActivityUserInformationResponse
            {
                Id = activityUserInformation?.Id,
                UserId = activityUserInformation?.UserId,
                MarketActivityId = id.Value,
                IsBookmark = activityUserInformation?.IsBookmark == true,
                IsJoin = activityBu != null
            };

            return MessageResult.SuccessResult(result);
        }

        /// <summary>
        /// Creates the customer relation.
        /// </summary>
        /// <param name="requestDto">The request dto.</param>
        public async Task<Guid> CreateApplyJoinToMarketActivityAsync(CreateApplyJoinToMarketActivityDto request)
        {
            var applyJoinToMarketActivityRepository = LazyServiceProvider.LazyGetService<IJoinToMarketActivityRepository>();
            var add = new JoinToMarketActivity()
            {
                MarketActivityId = request.MarketActivityId,
                ApplyJoinToMarketActivityId = request.ApplyJoinToMarketActivityId,
                ApproveStatus = ActivityApproveStatus.PendingApproval,
                ApplicantId = request.ApplicantId
            };

            var result = await applyJoinToMarketActivityRepository.InsertAsync(add, true);
            if (result != null)
            {
                //await SendApplyToJoinEmail(result.Id);
            }
            return result.Id;
        }

        /// <summary>
        /// H5端申请加入活动
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> CreateH5ApplyJoinToMarketActivityAsync(CreateH5ApplyJoinToMarketActivityDto request)
        {
            //加入活动发邮件
            var copyMarketActivity = await GetMarketActivityAsync(request.MarketActivityId);
            var activityRepository = LazyServiceProvider.LazyGetService<IActivityRepository>();
            var activityDepartmentRepository = LazyServiceProvider.LazyGetService<IActivityDepartmentRepository>();
            var activityHospitalRepository = LazyServiceProvider.LazyGetService<IActivityHospitalRepository>();
            var activityBuRepository = LazyServiceProvider.LazyGetService<IActivityBuRepository>();

            var bussinessUnitId = request.BussinessUnitId;
            string bussinessUnitName = string.Empty;

            var allDepartment = await LazyServiceProvider.LazyGetService<IDataverseService>().GetOrganizations(bussinessUnitId.ToString(), 1);
            bussinessUnitName = allDepartment.FirstOrDefault()?.DepartmentName;

            if (copyMarketActivity.BussinessUnitId == bussinessUnitId)
            {
                return MessageResult.FailureResult("该活动是你所属BU创建的，不需要加入该活动");
            }

            if (copyMarketActivity.OwningBusinessUnits != null && copyMarketActivity.OwningBusinessUnits.ContainsKey(bussinessUnitId))
            {
                return MessageResult.FailureResult("所属部门已加入该活动，请勿重复创建");
            }

            var activity = new Activity()
            {
                Name = copyMarketActivity.Name,
                Type = copyMarketActivity.Type,
                Mode = copyMarketActivity.Mode,
                StartYear = copyMarketActivity.StartYear,
                StartQuarter = copyMarketActivity.StartQuarter,
                StartMonth = copyMarketActivity.StartMonth,
                StartDay = copyMarketActivity.StartDay,
                EndYear = copyMarketActivity.EndYear,
                EndQuarter = copyMarketActivity.EndQuarter,
                EndMonth = copyMarketActivity.EndMonth,
                EndDay = copyMarketActivity.EndDay,
                Applicant = copyMarketActivity.Applicant,
                Contact = copyMarketActivity.Contact,
                CityCode = copyMarketActivity.CityCode,
                Location = copyMarketActivity.Location,
                Sponsor = copyMarketActivity.Sponsor,
                Organizer = copyMarketActivity.Organizer,
                Hospital = copyMarketActivity.Hospital,
                Theme = copyMarketActivity.Theme,
                Purpose = copyMarketActivity.Purpose,
                Url = copyMarketActivity.Url,
                BussinessUnitId = bussinessUnitId,
                BussinessUnitName = bussinessUnitName,
                Email = CurrentUser.Email,
                ReleaseStatus = ActivityReleaseStatus.Joined,
                ParentMarketActivityId = request.MarketActivityId
            };

            var code = await GenerateCode();
            activity.Code = code.Item1;
            var result = await activityRepository.InsertAsync(activity, true);

            if (result != null)
            {
                if (copyMarketActivity.Departments != null && copyMarketActivity.Departments.Count > 0)
                {
                    var eventDepartments = new List<ActivityDepartment>();
                    foreach (var item in copyMarketActivity.Departments)
                    {
                        eventDepartments.Add(new ActivityDepartment { MarketActivityId = result.Id, DepartmentId = item.Key, Department = item.Value });
                    }
                    await activityDepartmentRepository.InsertManyAsync(eventDepartments);
                }

                if (copyMarketActivity.Hospitals != null && copyMarketActivity.Hospitals.Count > 0)
                {
                    var activityHospitals = new List<ActivityHospital>();
                    foreach (var item in copyMarketActivity.Hospitals)
                    {
                        activityHospitals.Add(new ActivityHospital { MarketActivityId = result.Id, Hospital = item });
                    }
                    await activityHospitalRepository.InsertManyAsync(activityHospitals);
                }

                if (result.ReleaseStatus == ActivityReleaseStatus.Joined && result.ParentMarketActivityId != null)
                {
                    //加入活动，将活动BU合并到目标活动的BU数据中
                    var parentMarketActivityId = result.ParentMarketActivityId.Value;
                    var queryActivityBu = await activityBuRepository.GetQueryableAsync();
                    var activityBu = await queryActivityBu.FirstOrDefaultAsync(a => a.MarketActivityId == parentMarketActivityId && a.BuId == bussinessUnitId);
                    if (activityBu == null)
                    {
                        var addActivityBu = new ActivityBu
                        {
                            MarketActivityId = parentMarketActivityId,
                            BuId = bussinessUnitId,
                            Bu = bussinessUnitName
                        };
                        await activityBuRepository.InsertAsync(addActivityBu);
                    }

                    //加入活动发邮件
                    await SendApplyToJoinEmail(result.Id);
                }
                return MessageResult.SuccessResult((result.Id));
            }

            return MessageResult.FailureResult("更新市场活动用户信息失败");
        }

        /// <summary>
        /// 创建反馈意见
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> CreateSuggestionFeedbackAsync(CreateSuggestionFeedbackRequestDto request)
        {
            var suggestionFeedbackRepository = LazyServiceProvider.LazyGetService<ISuggestionFeedbackRepository>();
            var activity = new SuggestionFeedback()
            {
                Suggestion = request.Suggestion,
                Name = CurrentUser.Name,
                Email = CurrentUser.Email
            };

            if (CurrentUser.Id != null)
            {
                var user = await _identityUserRepository.FindAsync(CurrentUser.Id.Value);
                var departmentId = user.GetProperty<Guid?>(EntityConsts.IdentityUser.MainDepartmentId);
                var allDepartment = await LazyServiceProvider.LazyGetService<IDataverseService>().GetOrganizations(departmentId.ToString(), 1);

                var departmentName = allDepartment.FirstOrDefault()?.DepartmentName;
                if (departmentName != null)
                {
                    activity.Bu = departmentName;
                }
            }

            var result = await suggestionFeedbackRepository.InsertAsync(activity);
            if (result != null)
            {
                return MessageResult.SuccessResult(result.Id);
            }
            return MessageResult.FailureResult("新增建议反馈失败");
        }

        /// <summary>
        /// 检索加入市场活动数据
        /// </summary>
        /// <param name="request"></param>
        /// <param name="IsPage"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<QueryJoinToMarketActivityListResponseDto>> QueryJoinToMarketActivitiesAsync(QueryJoinToMarketActivityRequestDto request, bool IsPage = true)
        {
            var queryJoinToMarketActivity = await LazyServiceProvider.LazyGetService<IJoinToMarketActivityRepository>().GetQueryableAsync();
            var queryActivity = await LazyServiceProvider.LazyGetService<IActivityRepository>().GetQueryableAsync();
            var queryActivityDepartment = await LazyServiceProvider.LazyGetService<IActivityDepartmentRepository>().GetQueryableAsync();
            var queryActivityBu = await LazyServiceProvider.LazyGetService<IActivityBuRepository>().GetQueryableAsync();

            if (request.ApplyOrApprove == ApplyOrApprove.Apply)
            {
                request.ApplicantId = CurrentUser.Id;
            }
            else
            {
                request.ApproverId = CurrentUser.Id;
            }

            if (request.Bu != null)
            {
                var ActivityBuIds = await queryActivityBu.Where(a => a.BuId == request.Bu).Select(a => a.MarketActivityId).Distinct().ToListAsync();
                queryActivity = queryActivity.Where(a => ActivityBuIds.Contains(a.Id));
            }

            if (!string.IsNullOrEmpty(request.Province))
            {
                var provinces = await _commonService.GetProvinceCity();
                var provinceCities = provinces.FirstOrDefault(a => a.Name == request.Province).Cities;
                var cityNames = provinceCities.Select(a => a.Name).Distinct().ToList();
                queryActivity = queryActivity.Where(a => cityNames.Contains(a.City));
            }

            var activities = queryActivity
                .WhereIf(request.ApproverId != null, a => a.CreatorId == request.ApproverId)
                .WhereIf(!string.IsNullOrEmpty(request.Name), a => a.Name.Contains(request.Name))
                .WhereIf(!string.IsNullOrEmpty(request.Type), a => a.Type == request.Type)
                .WhereIf(!string.IsNullOrEmpty(request.Mode), a => a.Mode == request.Mode)
                .WhereIf(!string.IsNullOrEmpty(request.City), a => a.City == request.City)
                .WhereIf(!string.IsNullOrEmpty(request.CityCode), a => a.CityCode == request.CityCode);

            //时间段
            if (request.StartDate != null && request.EndDate != null)
            {
                var timeFrameActivities = new List<Activity>();
                foreach (var item in activities)
                {
                    //构建活动开始时间
                    int startYear = item.StartYear;
                    int startMonth = 1;
                    int startDay = 1;

                    if (item.StartQuarter != null && item.StartQuarter >= 1 && item.StartQuarter <= 4)
                    {
                        startMonth = (item.StartQuarter.Value - 1) * 3 + 1;
                    }

                    if (item.StartMonth != null && item.StartMonth >= 1 && item.StartMonth <= 12)
                    {
                        startMonth = item.StartMonth.Value;
                    }

                    if (item.StartDay != null && item.StartDay >= 1 && item.StartDay <= 31)
                    {
                        startDay = item.StartDay.Value;
                    }

                    var startDate = new DateTime(startYear, startMonth, startDay, 0, 0, 0, DateTimeKind.Local);

                    //构建活动结束时间
                    int endMonth = 12;
                    int endDay = 1;

                    DateTime endDate;

                    if (item.EndYear != null)
                    {
                        if (item.EndQuarter == null && item.EndMonth == null)
                        {
                            endDate = new DateTime(item.EndYear.Value + 1, 0, 0, 0, 0, 0, DateTimeKind.Local);
                        }
                        else
                        {
                            if (item.EndQuarter != null && item.EndQuarter >= 1 && item.EndQuarter <= 4)
                            {
                                endMonth = (item.EndQuarter.Value - 1) * 3 + 1;
                            }
                            if (item.EndMonth != null && item.EndMonth >= 1 && item.EndMonth <= 12)
                            {
                                endMonth = item.EndMonth.Value;
                            }

                            if (item.StartDay != null && item.StartDay >= 1 && item.StartDay <= 31)
                            {
                                endDay = item.StartDay.Value;
                                endDate = new DateTime(item.EndYear.Value, endMonth, endDay, 0, 0, 0, DateTimeKind.Local);
                            }
                            else
                            {
                                endDate = new DateTime(item.EndYear.Value, endMonth + 1, 0, 0, 0, 0, DateTimeKind.Local);
                            }
                        }
                    }
                    else
                    {
                        endDate = request.EndDate.Value;
                    }

                    if (startDate >= request.EndDate.Value && startDate >= request.StartDate.Value || endDate >= request.EndDate.Value && endDate >= request.StartDate.Value)
                    {
                        timeFrameActivities.Add(item);
                    }
                }

                var ids = timeFrameActivities.Select(a => a.Id).ToList();
                activities = activities.Where(a => ids.Contains(a.Id));
            }

            var activityIds = await activities.Select(a => a.Id).Distinct().ToListAsync();
            var joinToMarketActivities = queryJoinToMarketActivity.Where(a => activityIds.Contains(a.MarketActivityId))
                .WhereIf(request.ApplicantId != null, a => a.CreatorId == request.ApplicantId)
                .WhereIf(request.ApproveStatus != null, a => a.ApproveStatus == request.ApproveStatus);

            //总数
            var count = await joinToMarketActivities.CountAsync();
            if (count == 0)
            {
                return new PagedResultDto<QueryJoinToMarketActivityListResponseDto>(count, new List<QueryJoinToMarketActivityListResponseDto>());
            }
            //分页
            joinToMarketActivities = joinToMarketActivities.OrderByDescending(a => a.CreationTime);
            joinToMarketActivities = joinToMarketActivities.PagingIf(request, IsPage);

            var datas = await joinToMarketActivities.Select(a => new QueryJoinToMarketActivityListResponseDto
            {
                Id = a.Id,
                MarketActivityId = a.MarketActivityId,
                ApplyJoinToMarketActivityId = a.ApplyJoinToMarketActivityId,
                ApproveStatus = a.ApproveStatus,
            }).ToListAsync();

            var marketActivityIds = datas.Select(a => a.MarketActivityId).ToList();
            //翻译ID
            //Department
            var departmentlDatas = await queryActivityDepartment.Where(a => activityIds.Contains(a.MarketActivityId)).ToListAsync();
            //Bu
            var buDatas = await queryActivityBu.Where(a => activityIds.Contains(a.MarketActivityId)).ToListAsync();
            //user
            var userIds = activities.Where(a => a.CreatorId != null && marketActivityIds.Contains(a.CreatorId.Value)).Select(a => a.CreatorId.Value).Distinct();
            var users = await _identityUserRepository.GetListByIdsAsync(userIds);

            foreach (var data in datas)
            {
                var marketActivity = await activities.FirstOrDefaultAsync(a => a.Id == data.MarketActivityId);
                if (marketActivity != null)
                {
                    data.Code = marketActivity.Code;
                    data.Name = marketActivity.Name;
                    data.Type = marketActivity.Type;
                    data.Mode = marketActivity.Mode;
                    data.StartYear = marketActivity.StartYear;
                    data.StartQuarter = marketActivity.StartQuarter;
                    data.StartMonth = marketActivity.StartMonth;
                    data.StartDay = marketActivity.StartDay;
                    data.EndYear = marketActivity.EndYear;
                    data.EndQuarter = marketActivity.EndQuarter;
                    data.EndMonth = marketActivity.EndMonth;
                    data.EndDay = marketActivity.EndDay;
                    data.CityCode = marketActivity.CityCode;
                    data.Location = marketActivity.Location;
                    data.Applicant = marketActivity.Applicant;
                    data.Contact = marketActivity.Contact;
                    data.Sponsor = marketActivity.Sponsor;
                    data.Organizer = marketActivity.Organizer;
                    data.Hospital = marketActivity.Hospital;
                    data.Theme = marketActivity.Theme;
                    data.Purpose = marketActivity.Purpose;
                    data.Url = marketActivity.Url;
                    data.ReleaseStatus = marketActivity.ReleaseStatus;
                    data.CreatorId = marketActivity.CreatorId;
                    data.ReleaseTime = marketActivity.ReleaseTime;
                }

                data.Departments = departmentlDatas.Where(a => a.MarketActivityId == data.Id).DistinctBy(a => a.DepartmentId).ToDictionary(a => a.DepartmentId, a => a.Department);
                data.DepartmentNames = departmentlDatas.Where(a => a.MarketActivityId == data.Id).Select(a => a.Department).Distinct().ToList();

                data.OwningBusinessUnits = buDatas.Where(a => a.MarketActivityId == data.Id).DistinctBy(a => a.BuId).ToDictionary(a => a.BuId, a => a.Bu.ClearBu());
                data.OwningBusinessUnitNames = buDatas.Where(a => a.MarketActivityId == data.Id).Select(a => a.Bu.ClearBu()).Distinct().ToList();

                data.ReleaseUserName = users.FirstOrDefault(a => a.Id == data.CreatorId)?.Name;
            }

            var result = new PagedResultDto<QueryJoinToMarketActivityListResponseDto>(count, datas);
            return result;
        }

        /// <summary>
        /// 获取加入市场活动详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<QueryJoinToMarketActivityResponseDto> GetJoinToMarketActivitiesAsync(Guid? id)
        {
            var queryJoinToMarketActivity = await LazyServiceProvider.LazyGetService<IJoinToMarketActivityRepository>().GetQueryableAsync();
            var queryActivity = await LazyServiceProvider.LazyGetService<IActivityRepository>().GetQueryableAsync();
            var queryActivityDepartment = await LazyServiceProvider.LazyGetService<IActivityDepartmentRepository>().GetQueryableAsync();
            var queryActivityBu = await LazyServiceProvider.LazyGetService<IActivityBuRepository>().GetQueryableAsync();

            var joinToMarketActivity = await queryJoinToMarketActivity.FirstOrDefaultAsync(a => a.Id == id);
            if (joinToMarketActivity == null)
            {
                return new QueryJoinToMarketActivityResponseDto();
            }

            var activity = await queryActivity.FirstOrDefaultAsync(a => a.Id == joinToMarketActivity.MarketActivityId);

            var result = new QueryJoinToMarketActivityResponseDto
            {
                Id = joinToMarketActivity.Id,
                MarketActivityId = joinToMarketActivity.MarketActivityId,
                ApplyJoinToMarketActivityId = joinToMarketActivity.ApplyJoinToMarketActivityId,
                ApproveStatus = joinToMarketActivity.ApproveStatus,
                ApplicantId = joinToMarketActivity.ApplicantId,
                Code = activity.Code,
                Name = activity.Name,
                Type = activity.Type,
                Mode = activity.Mode,
                StartYear = activity.StartYear,
                StartQuarter = activity.StartQuarter,
                StartMonth = activity.StartMonth,
                StartDay = activity.StartDay,
                EndYear = activity.EndYear,
                EndQuarter = activity.EndQuarter,
                EndMonth = activity.EndMonth,
                EndDay = activity.EndDay,
                City = activity.City,
                CityCode = activity.CityCode,
                Location = activity.Location,
                Applicant = activity.Applicant,
                Contact = activity.Contact,
                Sponsor = activity.Sponsor,
                Organizer = activity.Organizer,
                Hospital = activity.Hospital,
                Theme = activity.Theme,
                Purpose = activity.Purpose,
                Url = activity.Url,
                ReleaseStatus = activity.ReleaseStatus,
                ReleaseTime = activity.ReleaseTime,
                CreatorId = activity.CreatorId,
            };

            result.Departments = (await queryActivityDepartment.Where(a => a.MarketActivityId == result.Id).ToListAsync()).DistinctBy(a => a.DepartmentId).ToDictionary(a => a.DepartmentId, a => a.Department);
            result.DepartmentNames = await queryActivityDepartment.Where(a => a.MarketActivityId == result.Id).Select(a => a.Department).Distinct().ToListAsync();

            result.OwningBusinessUnits = (await queryActivityBu.Where(a => a.MarketActivityId == result.Id).ToListAsync()).DistinctBy(a => a.BuId).ToDictionary(a => a.BuId, a => a.Bu.ClearBu());
            result.OwningBusinessUnitNames = await queryActivityBu.Where(a => a.MarketActivityId == result.Id).Select(a => a.Bu.ClearBu()).Distinct().ToListAsync();

            //user
            var userIds = new List<Guid>() { result.CreatorId.Value };
            var users = await _identityUserRepository.GetListByIdsAsync(userIds);
            result.ReleaseUserName = users.FirstOrDefault()?.Name;

            return result;
        }

        /// <summary>
        /// 修改加入市场活动审批状态
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task ModifyJoinToMarketActivityStatusAsync(UpdateJoinToMarketActivityStatusRequestDto request)
        {
            var joinToMarketActivityRepository = LazyServiceProvider.LazyGetService<IJoinToMarketActivityRepository>();
            var joinToMarketActivity = await joinToMarketActivityRepository.GetAsync(request.Id);

            if (joinToMarketActivity.ApproveStatus == ActivityApproveStatus.PendingApproval)
            {
                var activityRepository = LazyServiceProvider.LazyGetService<IActivityRepository>();

                if (joinToMarketActivity.CreatorId != null)
                {
                    var user = await _identityUserRepository.GetAsync(joinToMarketActivity.CreatorId.Value);
                    var departmentId = user.GetProperty<Guid?>(EntityConsts.IdentityUser.MainDepartmentId);
                    var allDepartment = await LazyServiceProvider.LazyGetService<IDataverseService>().GetOrganizations(departmentId.ToString(), 1);
                    var department = allDepartment.FirstOrDefault();

                    var applyJoinToMarketActivity = await activityRepository.GetAsync(joinToMarketActivity.ApplyJoinToMarketActivityId.Value);
                    if (request.Status == ActivityApproveStatus.Joined)
                    {
                        applyJoinToMarketActivity.ReleaseStatus = ActivityReleaseStatus.Joined;

                        if (department != null)
                        {
                            var activityBuRepository = LazyServiceProvider.LazyGetService<IActivityBuRepository>();

                            //applyJoinToMarketActivity的BU加入marketActivity的BU中
                            var activityBu = new ActivityBu() { MarketActivityId = joinToMarketActivity.MarketActivityId, BuId = department.Id, Bu = department.DepartmentName };
                            await activityBuRepository.InsertAsync(activityBu);
                        }
                    }
                    else if (request.Status == ActivityApproveStatus.Rejected)
                    {
                        applyJoinToMarketActivity.ReleaseStatus = ActivityReleaseStatus.Published;
                    }
                    else if (request.Status == ActivityApproveStatus.Withdrawn)
                    {
                        applyJoinToMarketActivity.ReleaseStatus = ActivityReleaseStatus.Draft;
                    }

                    await activityRepository.UpdateAsync(applyJoinToMarketActivity);
                }

                joinToMarketActivity.ApproveStatus = request.Status;
                await joinToMarketActivityRepository.UpdateAsync(joinToMarketActivity);
            }
        }

        /// <summary>
        /// 获取客户关系
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<QueryCustomerRelationListResponseH5Dto>> QueryCustomerRelationsAsync(QueryCustomerRelationRequestDto request)
        {
            await CreateAssceeLog(CrossBuModule.CustomerInfo);
            var queryCustomerRelation = await LazyServiceProvider.LazyGetService<ICustomerRelationRepository>().GetQueryableAsync();
            var allDepartment = await LazyServiceProvider.LazyGetService<IDataverseService>().GetOrganizations();
            var query = queryCustomerRelation.AsNoTracking().Where(s => s.Status != CustomerStatus.Invalid)
                .WhereIf(!string.IsNullOrEmpty(request.Province), a => a.HospitalProvince == request.Province)
                .WhereIf(!string.IsNullOrEmpty(request.City), a => a.HospitalCity == request.City)
                .WhereIf(!string.IsNullOrEmpty(request.Department), a => a.HospitalDepartment == request.Department)

                .WhereIf(request.Bu.HasValue, a => a.BussinessUnitId == request.Bu)
                ;
            //var queryS = query.Select(s => new
            //{
            //    Hospital = string.IsNullOrEmpty(s.CleanedHospitalName) ? s.Hospital : s.CleanedHospitalName,
            //    s.HospitalProvince,
            //    s.HospitalCity,
            //    s.BussinessUnit,
            //    s.BussinessUnitId,
            //    s.DoctorId,
            //    s.DoctorName,
            //    s.DoctorTitle,
            //    s.HospitalDepartment,
            //    s.PinYinSort,
            //}).WhereIf(!string.IsNullOrEmpty(request.Keyword), a => a.Hospital.Contains(request.Keyword));
            //var groupQuery = queryS.GroupBy(a => new { a.Hospital, a.HospitalProvince, a.HospitalCity }, (key, g) => new
            //{
            //    Hospital = key.Hospital,
            //    HospitalProvince = key.HospitalProvince,
            //    HospitalCity = key.HospitalCity,
            //    gDatas = g.Select(s => s.BussinessUnit).JoinAsString(","),
            //    //BusinessUnits = g.Select(d => d.BussinessUnit).Distinct(),
            //    Count = g.Count()
            //});
            //var disQuery = queryS.Select(s => new { s.Hospital, s.PinYinSort }).Distinct();

            //var resultQuery = disQuery.Join(groupQuery, (a) => a.Hospital, (b) => b.Hospital, (a, b) => new
            //{
            //    a.Hospital,
            //    a.PinYinSort,
            //    HospitalProvince = b.HospitalProvince,
            //    HospitalCity = b.HospitalCity,
            //    b.Count,
            //    b.gDatas
            //}).OrderBy(o => o.PinYinSort);
            //var queryS = query.Select(s => new
            //{
            //    Hospital = string.IsNullOrEmpty(s.CleanedHospitalName) ? s.Hospital : s.CleanedHospitalName,
            //    s.HospitalProvince,
            //    s.HospitalCity,
            //    s.BussinessUnit,
            //    s.BussinessUnitId,
            //    s.DoctorId,
            //    s.DoctorName,
            //    s.DoctorTitle,
            //    s.HospitalDepartment,
            //    s.PinYinSort,
            //}).WhereIf(!string.IsNullOrEmpty(request.Keyword), a => a.Hospital.Contains(request.Keyword));

            //var groupQuery = queryS.GroupBy(a => new { a.Hospital, a.HospitalProvince, a.HospitalCity })
            //    .Select(g => new
            //    {
            //        g.Key.Hospital,
            //        g.Key.HospitalProvince,
            //        g.Key.HospitalCity,
            //        gDatas = string.Join(",", g.Select(s => s.BussinessUnit)),
            //        Count = g.Count()
            //    });

            //var disQuery = queryS.Select(s => new { s.Hospital, s.PinYinSort }).Distinct();

            //var resultQuery = disQuery.Join(groupQuery, a => a.Hospital, b => b.Hospital, (a, b) => new
            //{
            //    a.Hospital,
            //    a.PinYinSort,
            //    b.HospitalProvince,
            //    b.HospitalCity,
            //    b.Count,
            //    b.gDatas
            //}).OrderBy(o => o.PinYinSort);
            //var count = await resultQuery.CountAsync();
            ////var str = resultQuery.ToQueryString();
            //var datas = await resultQuery.PagingIf(request).ToListAsync();


            //var mapDatas = datas.Select(s => new QueryCustomerRelationListResponseH5Dto
            //{
            //    Hospital = s.Hospital,
            //    HospitalProvince = s.HospitalProvince,
            //    HospitalCity = s.HospitalCity,
            //    Bus = s.gDatas,
            //    Count = s.Count
            //}).ToList();
            var groupQuery = query.Select(s => new
            {
                Hospital = string.IsNullOrEmpty(s.CleanedHospitalName) ? s.Hospital : s.CleanedHospitalName,
                s.HospitalProvince,
                s.HospitalCity,
                s.BussinessUnit,
                s.BussinessUnitId,
                s.DoctorId,
                s.DoctorName,
                s.DoctorTitle,
                s.HospitalDepartment,
                s.PinYinSort,
            }).WhereIf(!string.IsNullOrEmpty(request.Keyword), a => a.Hospital.Contains(request.Keyword))
                 .GroupBy(a => new { a.Hospital, a.HospitalProvince, a.HospitalCity, a.PinYinSort }, (key, g) => new
                 {
                     Hospital = key.Hospital,
                     HospitalProvince = key.HospitalProvince,
                     HospitalCity = key.HospitalCity,
                     key.PinYinSort,
                     gDatas = string.Join(",", g.Select(s => s.BussinessUnit)),
                     //BusinessUnits = g.Select(d => d.BussinessUnit).Distinct(),
                     Count = g.Count()
                 });

            var count = await groupQuery.CountAsync();
            var datas = await groupQuery.OrderBy(s => s.PinYinSort).PagingIf(request).ToListAsync();

            var mapDatas = datas.Select(s => new QueryCustomerRelationListResponseH5Dto
            {
                Hospital = s.Hospital,
                HospitalProvince = s.HospitalProvince,
                HospitalCity = s.HospitalCity,
                Bus = s.gDatas,
                Count = s.Count
            }).ToList();
            var result = new PagedResultDto<QueryCustomerRelationListResponseH5Dto>(count, mapDatas);
            return result;
        }

        /// <summary>
        /// 获取二级页面详情
        /// </summary>
        /// <param name="hospitalName">The hospital identifier.</param>
        /// <returns></returns>
        public async Task<List<HospitalDepartment>> QueryCustomerRelationsAsync(string hospitalName, Guid? businessUnitId, string province, string city)
        {
            var queryCustomerRelation = (await LazyServiceProvider.LazyGetService<ICustomerRelationRepository>().GetQueryableAsync()).AsNoTracking();
            var query = queryCustomerRelation
                .Where(m => (m.Hospital == hospitalName || m.CleanedHospitalName == hospitalName) && m.Status != CustomerStatus.Invalid)
                .WhereIf(!string.IsNullOrEmpty(province), a => a.HospitalProvince == province)
                .WhereIf(!string.IsNullOrEmpty(city), a => a.HospitalCity == city)
                .WhereIf(businessUnitId.HasValue, a => a.BussinessUnitId == businessUnitId);
            //分组
            var groupDatas = query.GroupBy(b => b.HospitalDepartment, (key, g) => new
            {
                Department = key,
                Contract = g.Select(c => new
                {
                    Name = c.DoctorName,
                    Title = c.DoctorTitle,
                    BusinessUnits = c.BussinessUnit,
                    Emails = c.Email
                }),
                Count = g.Count()
            }).AsEnumerable();

            var datas = groupDatas.Select(s => new HospitalDepartment
            {
                Department = s.Department,
                Contracts = s.Contract.GroupBy(g => new { g.Name, g.Title }, (key, val) => new Contract
                {
                    //Name = key.Name,
                    Title = key.Title,
                    BusinessUnits = val.Select(s => s.BusinessUnits).Distinct().Select(s => s.ClearBu()).OrderBy(o => o),
                    //Emails = val.Select(s => s.Emails).Distinct(),
                    //Units = val.Select(s => new BusinessUnit { Name = s.BusinessUnits, Email = s.Emails }).Distinct()
                }),
                Count = s.Count
            }).ToList();


            /*            //所有
                        var allQuery = query.GroupBy(c => c.DoctorName).Select(c => new Contract
                        {
                            Name = c.Key,
                            Title = c.First().DoctorTitle,
                            BusinessUnits = c.Select(d => new BusinessUnit
                            {
                                Email = d.Email,
                                Name = d.BussinessUnit
                            }).Distinct()
                        });
                        var allDatas = await allQuery.ToListAsync();
                        var Count = await allQuery.CountAsync();*/
            //groupDatas.Insert(0, new HospitalDepartment { Contracts = allDatas, Department = "全部", Count = Count });
            return datas;
        }

        /// <summary>
        /// 获取客户关系-给portal端使用
        /// </summary>
        /// <param name="request"></param>
        /// <param name="IsPage"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<QueryCustomerRelationListResponsePortalDto>> QueryPortalCustomerRelationsAsync(QueryCustomerRelationRequestDto request, bool IsPage = true)
        {
            var queryCustomerRelation = await LazyServiceProvider.LazyGetService<ICustomerRelationRepository>().GetQueryableAsync();
            var query = queryCustomerRelation.AsNoTracking()
                .WhereIf(!string.IsNullOrEmpty(request.DoctorId), a => a.DoctorId.Contains(request.DoctorId))
                .WhereIf(!string.IsNullOrEmpty(request.DoctorName), a => a.DoctorName.Contains(request.DoctorName))
                .WhereIf(!string.IsNullOrEmpty(request.DoctorTitle), a => a.DoctorTitle.Contains(request.DoctorTitle))
                .WhereIf(!string.IsNullOrEmpty(request.Hospital), a => a.Hospital.Contains(request.Hospital))
                //.WhereIf(!string.IsNullOrEmpty(request.HospitalProvince), a => a.HospitalProvince.Contains(request.HospitalProvince))
                .WhereIf(!string.IsNullOrEmpty(request.CityCode), a => a.HospitalCityCode == request.CityCode)
                .WhereIf(!string.IsNullOrEmpty(request.Email), a => a.Email.Contains(request.Email))
                //.WhereIf(request.BussinessUnitId.HasValue, a => a.BussinessUnitId == request.BussinessUnitId)
                .WhereIf(request.CustomerStatus.HasValue, a => a.Status == request.CustomerStatus);
            var personCenterService = LazyServiceProvider.LazyGetService<IPersonCenterService>();
            var roleLevel = personCenterService.GetMyRoleLevel();
            if (roleLevel != RoleLevel.Admin)
            {
                var relations = await _dataverseService.GetStaffDepartmentRelations(CurrentUser.Id.Value.ToString());
                var BuIds = relations.Select(s => s.DepartmentId).ToList();
                query = query.WhereIf(BuIds.Count > 0, m => BuIds.Contains(m.BussinessUnitId.Value));
            }
            //总数
            var count = await query.CountAsync();
            //分页
            query = query.OrderByDescending(a => a.CreationTime);
            query = query.PagingIf(request, IsPage);

            var datas = (await query.ToListAsync()).Select(a => new QueryCustomerRelationListResponsePortalDto
            {
                Id = a.Id,
                DoctorId = a.DoctorId,
                DoctorName = a.DoctorName,
                DoctorTitle = a.DoctorTitle,
                HospitalId = a.HospitalId,
                Hospital = a.Hospital,
                HospitalProvince = a.HospitalProvince,
                HospitalCity = a.HospitalCity,
                HospitalDepartment = a.HospitalDepartment,
                Email = a.Email,
                BussinessUnit = a.BussinessUnit.ClearBu(),
                Status = a.Status,
                ReleaseDate = a.ReleaseDate,
                CleanedHospitalName = a.CleanedHospitalName,
                DoctorLicenseCode = a.DoctorLicenseCode,
                HCOCode = a.HCOCode,
                StandardizationPosition = a.StandardizationPosition,
                StandardizedTitle = a.StandardizedTitle,
                HPCCode = a.HPCCode,
                HospitalType = a.HospitalType,
                StandardDepartmentId = a.StandardDepartmentId,
                StandardDepartmentName = a.StandardDepartmentName,

            }).ToList();

            var result = new PagedResultDto<QueryCustomerRelationListResponsePortalDto>(count, datas);
            return result;
        }

        /// <summary>
        /// 验证转换ecxel数据
        /// </summary>
        /// <param name="Rows"></param>
        /// <returns></returns>
        public async Task<MessageResult> VarifyImportCustomerRelationAsync(IEnumerable<TransferCustomerRelationDto> Rows, Guid bussinessUnitId)
        {
            var dataverseService = _serviceProvider.GetService<IDataverseService>();
            var AllCity = await dataverseService.GetAllCity();
            var AllProvince = await dataverseService.GetAllProvince();
            //var doctorIds = Rows.Select(s => s.DoctorId).ToList();
            var query = await LazyServiceProvider.LazyGetService<ICustomerRelationRepository>().GetQueryableAsync();
            //var datas = await query.Where(s => doctorIds.Contains(s.DoctorId)).Select(s => s.DoctorId).Distinct().ToDictionaryAsync(s => s, s => s);
            var rowNo = 1;
            var isSuccess = true;
            string pattern = @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$";
            Regex regex = new(pattern, RegexOptions.IgnoreCase);

            List<TransferCustomerRelationDto> error = [];
            List<TransferCustomerRelationDto> success = [];
            var doctor = new Dictionary<string, string>();
            foreach (var item in Rows)
            {
                rowNo++;
                var message = string.Empty;
                item.No = rowNo;
                if (string.IsNullOrEmpty(item.DoctorId))
                {
                    message += "医生ID为空；";
                }
                else
                {
                    var d = doctor.GetOrDefault(item.DoctorId);
                    if (!string.IsNullOrEmpty(d))
                    {
                        message += $"医生{d}在导入文件重复；";
                    }
                    //else
                    //{
                    //    var val = datas.GetOrDefault(item.DoctorId);
                    //    if (!string.IsNullOrEmpty(val))
                    //    {
                    //        message += "医生ID已存在；";
                    //    }
                    //}
                }
                if (string.IsNullOrEmpty(item.DoctorName))
                {
                    message += "医生姓名为空；";
                }

                if (string.IsNullOrEmpty(item.DoctorTitle))
                {
                    message += "医生职称为空；";
                }

                if (string.IsNullOrEmpty(item.HospitalId))
                {
                    message += "所属医院ID为空;";
                }
                if (string.IsNullOrEmpty(item.Hospital))
                {
                    message += "所属医院名称为空;";
                }

                var provincId = default(Guid);
                if (string.IsNullOrEmpty(item.HospitalProvince))
                {
                    message += "医院所在省份为空;";
                }
                else
                {
                    var provinc = AllProvince.Find(f => f.Name == item.HospitalProvince);
                    if (provinc == null) { message += "请填写正确的省份;"; }
                    else
                    {
                        provincId = provinc.Id;
                        item.HospitalProvinceCode = provinc.Code;
                    }
                }

                if (string.IsNullOrEmpty(item.HospitalCity))
                {
                    message += "医院所在市为空;";
                }
                else
                {
                    var city = AllCity.Find(f => f.Name == item.HospitalCity);
                    if (city == null) { message += "该城市未在主数据中，请联系管理员添加;"; }
                    else if (provincId != city.ProvinceId)
                    {
                        message += "请填写正确的省市关系;";
                    }
                    else item.HospitalCityCode = city.Code;
                }

                if (string.IsNullOrEmpty(item.HospitalDepartment))
                {
                    message += "院内科室为空;";

                }

                if (string.IsNullOrEmpty(item.Email))
                {
                    message += "医生雅培联系人邮箱为空;";
                }

                else
                {
                    var strs = item.Email.Split(',')[0];
                    if (!regex.IsMatch(strs))
                    {
                        message += "请填写正确的联系人邮箱";
                    }
                }
                doctor[item.DoctorId] = item.DoctorName;
                item.ErrorMessage = message;
                if (!string.IsNullOrEmpty(message))
                {
                    error.Add(item);
                    isSuccess = false;
                }
                else success.Add(item);

            }
            //isSuccess = error.Count == 0;
            if (isSuccess)
            {
                await ImportCustomerRelationAsync(success, bussinessUnitId);
                return MessageResult.SuccessResult(new ImportDataResponseDto<TransferCustomerRelationDto>([], isSuccess));
            }
            return MessageResult.SuccessResult(new ImportDataResponseDto<TransferCustomerRelationDto>(error, isSuccess));
        }

        /// <summary>
        /// 批量提交市场活动
        /// </summary>
        /// <param name="messageDtos"></param>
        /// <returns></returns>
        public async Task<MessageResult> ImportCustomerRelationAsync(List<TransferCustomerRelationDto> Rows, Guid bussinessUnitId)
        {
            var customerRelationRepository = LazyServiceProvider.LazyGetService<ICustomerRelationRepository>();
            var query = await customerRelationRepository.GetQueryableAsync();
            var hospitals = await _dataverseService.GetAllHospitals();
            var doctorIds = Rows.Select(s => s.DoctorId).ToList();
            var datas = await query.AsNoTracking().Where(s => doctorIds.Contains(s.DoctorId) && bussinessUnitId == s.BussinessUnitId).ToListAsync();

            var hospitalNames = Rows.Select(s => s.Hospital).Distinct().ToList();
            var cleandHospitals = await query.AsNoTracking().Where(s => hospitalNames.Contains(s.Hospital) && s.CleanedHospitalName != null).ToListAsync();
            var guidGenerator = LazyServiceProvider.LazyGetService<IGuidGenerator>();
            var customerRelationHisRespository = LazyServiceProvider.LazyGetService<ICustomerRelationHistoryRepository>();

            string bussinessUnitName = string.Empty;
            var allDepartment = await LazyServiceProvider.LazyGetService<IDataverseService>().GetOrganizations(bussinessUnitId.ToString(), 1);

            var department = allDepartment.FirstOrDefault();
            if (department != null)
            {
                bussinessUnitId = department.Id;
                bussinessUnitName = department.DepartmentName;
            }

            var addCustomerRelations = new List<CustomerRelation>();

            var modifyCustomerRelations = new List<CustomerRelation>();
            var type = typeof(UpdateCustomerRelationRequestDto);

            var properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance);
            IList<CustomerRelationHistory> historys = [];

            foreach (var item in Rows)
            {
                var ppHospital = hospitals.FirstOrDefault(v => v.Name == item.Hospital);
                var cleandHospital = cleandHospitals.FirstOrDefault(v => v.Hospital == item.Hospital);
                CustomerRelation customerRelation = null;
                customerRelation = datas.FirstOrDefault(f => f.DoctorId == item.DoctorId && bussinessUnitId == f.BussinessUnitId);
                UpdateCustomerRelationRequestDto orgianlMapper = null;
                //是否是修改
                var isModify = customerRelation != null;
                if (isModify)
                {
                    modifyCustomerRelations.Add(customerRelation);
                    customerRelation.SetModify(CurrentUser.Id);
                    orgianlMapper = ObjectMapper.Map<CustomerRelation, UpdateCustomerRelationRequestDto>(customerRelation);
                }
                else
                {
                    customerRelation = new CustomerRelation();
                    customerRelation.SetId(guidGenerator.Create());
                    addCustomerRelations.Add(customerRelation);
                    customerRelation.ReleaseDate = DateTime.Now;
                    customerRelation.SetCreation(CurrentUser.Id);
                }
                customerRelation.DoctorId = item.DoctorId;
                customerRelation.DoctorName = item.DoctorName;
                customerRelation.DoctorTitle = item.DoctorTitle;
                customerRelation.DoctorLicenseCode = item.DoctorLicenseCode;
                customerRelation.HospitalId = item.HospitalId;
                customerRelation.Hospital = item.Hospital;
                customerRelation.HospitalProvince = item.HospitalProvince;
                customerRelation.HospitalCity = item.HospitalCity;
                customerRelation.HospitalDepartment = item.HospitalDepartment;
                customerRelation.Email = item.Email;
                customerRelation.BussinessUnitId = bussinessUnitId;
                customerRelation.BussinessUnit = bussinessUnitName;
                customerRelation.HospitalCityCode = item.HospitalCityCode;
                customerRelation.HospitalProvinceCode = item.HospitalProvinceCode;
                customerRelation.Status = CustomerStatus.Valid;
                customerRelation.HospitalPPId = ppHospital?.Id;

                if (cleandHospital != null)
                {
                    customerRelation.HospitalStatus = CustomerRelationHospitalStatus.Original;
                    customerRelation.CleanedHospitalName = cleandHospital.CleanedHospitalName;
                    customerRelation.HCOCode = cleandHospital.HCOCode;
                    customerRelation.HospitalType = cleandHospital.HospitalType;
                    customerRelation.HospitalPPId = cleandHospital.HospitalPPId;
                    customerRelation.PinYinSort = customerRelation.CleanedHospitalName.GetFristCharPinyin();
                }
                else if (ppHospital != null)
                {
                    customerRelation.HospitalStatus = CustomerRelationHospitalStatus.Original;
                    customerRelation.CleanedHospitalName = ppHospital.Name;
                    customerRelation.HCOCode = ppHospital.HcoVeevaID;
                    customerRelation.HospitalType = ppHospital.DetailType;
                    customerRelation.PinYinSort = ppHospital.Name.GetFristCharPinyin();
                }
                else
                {
                    customerRelation.PinYinSort = item.Hospital.GetFristCharPinyin();
                    customerRelation.HospitalStatus = CustomerRelationHospitalStatus.New;
                }
                if (isModify)
                {
                    var newlMapper = ObjectMapper.Map<CustomerRelation, UpdateCustomerRelationRequestDto>(customerRelation);
                    //加入变更历史
                    foreach (var property in properties)
                    {
                        var preValue = property.GetValue(orgianlMapper)?.ToString() ?? string.Empty;
                        var postValue = property.GetValue(newlMapper)?.ToString() ?? string.Empty;
                        var attribute = property.GetCustomAttribute<DescriptionAttribute>();
                        if (attribute == null) continue;
                        //if (preValue is Array || postValue is Array) continue;
                        if (preValue != postValue)
                        {
                            CustomerRelationHistory history = new();
                            history.CustomerRelationId = orgianlMapper.Id;
                            history.ChangeField = attribute.Description;
                            history.ChangeInitiatorId = CurrentUser.Id;
                            history.ChangeContent = $"\"{preValue}\"变更为\"{postValue}\"";
                            history.SetId(guidGenerator.Create());
                            history.SetCreation(CurrentUser.Id);
                            historys.Add(history);
                        }

                    }
                }

            }
            var context = await customerRelationRepository.GetDbContextAsync();
            await context.BulkInsertAsync(addCustomerRelations);
            await context.BulkUpdateAsync(modifyCustomerRelations);
            await context.BulkInsertAsync(historys);
            return MessageResult.SuccessResult("批量导入客户关系成功");
        }

        /// <summary>
        /// Creates the customer relation.
        /// </summary>
        /// <param name="requestDto">The request dto.</param>
        public async Task<MessageResult> CreateCustomerRelationAsync(CreateCustomerRelationRequestDto requestDto)
        {
            var customerRelationRespository = LazyServiceProvider.LazyGetService<ICustomerRelationRepository>();
            var query = await customerRelationRespository.GetQueryableAsync();
            var dataverseService = _serviceProvider.GetService<IDataverseService>();

            var cityCode = requestDto.ProvinceCity[1];
            var provinceCode = requestDto.ProvinceCity[0];
            var userQuery = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var hospitals = await _dataverseService.GetAllHospitals();
            var standardHosDeps = await _dataverseService.GetAllDepartments();
            var jobTitle = (await _dataverseService.GetAllJobTiles(requestDto.DoctorTitle)).FirstOrDefault();
            var jobTitleId = jobTitle?.Id ?? default;

            var cleandHospital = await query.AsNoTracking().FirstOrDefaultAsync(s => requestDto.Hospital == s.Hospital && s.CleanedHospitalName != null);
            //是否存在
            //var isExist = await customerRelationRespository.AnyAsync(s => s.DoctorName == requestDto.DoctorName && s.DoctorTitle ==
            //requestDto.DoctorTitle && s.HospitalDepartment == requestDto.HospitalDepartment && s.Hospital == requestDto.Hospital);
            var isExist = await customerRelationRespository.AnyAsync(s => s.DoctorId == requestDto.DoctorId && s.BussinessUnitId == requestDto.BussinessUnitId);
            if (isExist) return MessageResult.FailureResult("该医生信息已存在，请勿重复新建");
            var customerRelation = ObjectMapper.Map<CreateCustomerRelationRequestDto, CustomerRelation>(requestDto);
            customerRelation.ReleaseDate = DateTime.Now;
            customerRelation.HospitalCityCode = cityCode;
            customerRelation.HospitalProvinceCode = provinceCode;
            var city = (await dataverseService.GetAllCity(cityCode)).First();

            customerRelation.HospitalCity = city.Name;
            var provice = (await dataverseService.GetAllProvince(provinceCode)).First();
            customerRelation.HospitalProvince = provice.Name;
            var allDepartment = await LazyServiceProvider.LazyGetService<IDataverseService>().GetOrganizations(requestDto.BussinessUnitId.ToString(), 1);
            var department = allDepartment.FirstOrDefault();
            if (department != null)
            {
                customerRelation.BussinessUnitId = department.Id;
                customerRelation.BussinessUnit = department.DepartmentName;
            }
            //var vendorQuery = await QueryVendorInfo();
            //var vendor = vendorQuery.FirstOrDefault(s => s.SPName == requestDto.DoctorName && s.PTId == jobTitleId && s.HosDepartment == requestDto.HospitalDepartment && s.HospitalName == requestDto.Hospital);
            //if (vendor != null)
            //{
            //    HospitalDto hospital = default;
            //    if (vendor.HospitalId.HasValue)
            //        hospital = hospitals.FirstOrDefault(f => f.Id == vendor.HospitalId);
            //    customerRelation.HPCCode = vendor.VendorCode;
            //    customerRelation.StandardizedTitle = jobTitle.Name;
            //    customerRelation.StandardizationPosition = vendor.AcademicLevel;
            //    customerRelation.HCOCode = hospital?.HospitalCode;
            //    customerRelation.CleanedHospitalName = hospital?.Name;
            //    customerRelation.HospitalType = hospital?.Type;
            //    OfficeDto standardHosDep = default;
            //    if (vendor.StandardHosDepId.HasValue)
            //        standardHosDep = standardHosDeps.FirstOrDefault(s => s.Id == vendor.StandardHosDepId);
            //    customerRelation.StandardDepartmentName = standardHosDep?.Name;
            //    customerRelation.StandardDepartmentId = standardHosDep?.Id.ToString();
            //    customerRelation.IsPush = true;
            //    customerRelation.HospitalPPId = vendor.HospitalId;
            //}
            //else
            //{
            var ppHospital = hospitals.FirstOrDefault(v => v.Name == requestDto.Hospital);
            if (cleandHospital != null)
            {
                customerRelation.HospitalStatus = CustomerRelationHospitalStatus.Original;
                customerRelation.CleanedHospitalName = cleandHospital.CleanedHospitalName;
                customerRelation.HCOCode = cleandHospital.HCOCode;
                customerRelation.HospitalType = cleandHospital.HospitalType;
                customerRelation.HospitalPPId = cleandHospital.HospitalPPId;
                customerRelation.PinYinSort = customerRelation.CleanedHospitalName.GetFristCharPinyin();
            }
            else if (ppHospital == null)
            {
                //Entity hospital = new Entity("spk_hospitalmasterdata");
                //hospital["spk_name"] = requestDto.Hospital;
                //hospital["spk_hcoprovince"] = new EntityReference("spk_province", provice.Id);
                //hospital["spk_hcocity"] = new EntityReference("spk_city", city.Id);
                //hospital["spk_hospitalstatus"] = new OptionSetValue(923180000);
                //var hospitalId = Guid.NewGuid();
                //hospital.Id = hospitalId;
                //await _dataverseRepository.DataverseClient.CreateAsync(hospital);
                //customerRelation.HospitalPPId = hospitalId;
                customerRelation.PinYinSort = requestDto.Hospital.GetFristCharPinyin();
                customerRelation.HospitalStatus = CustomerRelationHospitalStatus.New;
            }
            else
            {
                customerRelation.HospitalPPId = ppHospital.Id;
                customerRelation.CleanedHospitalName = ppHospital.Name;
                customerRelation.HCOCode = ppHospital.HcoVeevaID;
                customerRelation.HospitalType = ppHospital.DetailType;
                customerRelation.HospitalStatus = CustomerRelationHospitalStatus.Original;
                customerRelation.PinYinSort = ppHospital.Name.GetFristCharPinyin();
            }

            //}
            await customerRelationRespository.InsertAsync(customerRelation);
            //BackgroundJob.Enqueue<PushDcrCrossBuWorker>(a => a.DoWorkAsync(CancellationToken.None));
            return MessageResult.SuccessResult("创建成功");
        }

        /// <summary>
        /// Edits the customer relation asynchronous.
        /// </summary>
        /// <param name="requestDto">The request dto.</param>
        /// <returns></returns>
        public async Task<MessageResult> EditCustomerRelationAsync(UpdateCustomerRelationRequestDto requestDto)
        {
            var customerRelationRespository = LazyServiceProvider.LazyGetService<ICustomerRelationRepository>();
            var customerRelationHisRespository = LazyServiceProvider.LazyGetService<ICustomerRelationHistoryRepository>();
            var dataverseService = _serviceProvider.GetService<IDataverseService>();
            var hospitals = await _dataverseService.GetAllHospitals();
            var query = await customerRelationRespository.GetQueryableAsync();
            var cleandHospital = await query.AsNoTracking().FirstOrDefaultAsync(s => requestDto.Hospital == s.Hospital && s.CleanedHospitalName != null);
            //是否存在
            //var isExist = await customerRelationRespository.AnyAsync(s => s.DoctorName == requestDto.DoctorName && s.DoctorTitle ==
            //requestDto.DoctorTitle && s.HospitalDepartment == requestDto.HospitalDepartment && s.Hospital == requestDto.Hospital && s.Id != requestDto.Id);
            var isExist = await customerRelationRespository.AnyAsync(s => s.DoctorId == requestDto.DoctorId && s.Id != requestDto.Id && s.BussinessUnitId == requestDto.BussinessUnitId);
            if (isExist) return MessageResult.FailureResult("该医生信息已存在");
            var orgianlData = await customerRelationRespository.GetAsync(requestDto.Id);
            var orgianlMapper = ObjectMapper.Map<CustomerRelation, UpdateCustomerRelationRequestDto>(orgianlData);
            var customerRelation = ObjectMapper.Map(requestDto, orgianlData);
            var cityCode = requestDto.ProvinceCity[1];
            var provinceCode = requestDto.ProvinceCity[0];
            var city = (await dataverseService.GetAllCity(cityCode)).First();

            requestDto.HospitalCity = customerRelation.HospitalCity = city.Name;
            var provice = (await dataverseService.GetAllProvince(provinceCode)).First();
            requestDto.HospitalProvince = customerRelation.HospitalProvince = provice.Name;
            //requestDto.HospitalCity = (await dataverseService.GetAllCity(cityCode)).First().Name;
            //requestDto.HospitalProvince = (await dataverseService.GetAllProvince(provinceCode)).First().Name;

            var type = typeof(UpdateCustomerRelationRequestDto);

            var properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance);

            IList<CustomerRelationHistory> historys = [];
            foreach (var property in properties)
            {
                var preValue = property.GetValue(orgianlMapper)?.ToString() ?? string.Empty; ;
                var postValue = property.GetValue(requestDto)?.ToString() ?? string.Empty; ;
                var attribute = property.GetCustomAttribute<DescriptionAttribute>();
                if (attribute == null) continue;
                //if (preValue is Array || postValue is Array) continue;
                if (preValue != postValue)
                {
                    if (attribute == null) continue;
                    CustomerRelationHistory history = new();
                    history.CustomerRelationId = requestDto.Id;
                    history.ChangeField = attribute.Description;
                    history.ChangeInitiatorId = CurrentUser.Id;
                    history.ChangeContent = $"\"{preValue}\"变更为\"{postValue}\"";
                    historys.Add(history);
                }

            }
            customerRelation.HospitalCityCode = cityCode;
            customerRelation.HospitalProvinceCode = provinceCode;

            var ppHospital = hospitals.FirstOrDefault(v => v.Name == requestDto.Hospital);
            if (cleandHospital != null)
            {
                customerRelation.HospitalStatus = CustomerRelationHospitalStatus.Original;
                customerRelation.CleanedHospitalName = cleandHospital.CleanedHospitalName;
                customerRelation.HCOCode = cleandHospital.HCOCode;
                customerRelation.HospitalType = cleandHospital.HospitalType;
                customerRelation.HospitalPPId = cleandHospital.HospitalPPId;
                customerRelation.PinYinSort = customerRelation.CleanedHospitalName.GetFristCharPinyin();
            }
            else if (ppHospital == null)
            {
                //Entity hospital = new Entity("spk_hospitalmasterdata");
                //hospital["spk_name"] = requestDto.Hospital;
                //hospital["spk_hcoprovince"] = new EntityReference("spk_province", provice.Id);
                //hospital["spk_hcocity"] = new EntityReference("spk_city", city.Id);
                //hospital["spk_hospitalstatus"] = new OptionSetValue(923180000);
                //var hospitalId = Guid.NewGuid();
                //hospital.Id = hospitalId;
                //await _dataverseRepository.DataverseClient.CreateAsync(hospital);
                //customerRelation.HospitalPPId = hospitalId;
                customerRelation.HospitalStatus = CustomerRelationHospitalStatus.New;
                customerRelation.PinYinSort = requestDto.Hospital.GetFristCharPinyin();
            }
            //else if (ppHospital?.Id != customerRelation.HospitalPPId)
            //{

            //}
            else
            {
                customerRelation.HospitalPPId = ppHospital.Id;
                customerRelation.CleanedHospitalName = ppHospital.Name;
                customerRelation.HCOCode = ppHospital.HcoVeevaID;
                customerRelation.HospitalType = ppHospital.DetailType;
                customerRelation.HospitalStatus = CustomerRelationHospitalStatus.Original;
                customerRelation.PinYinSort = ppHospital.Name.GetFristCharPinyin();
            }
            //存原始数据，veeva清洗可能会用到
            //var preDate = ObjectMapper.Map<CustomerRelation, CustomerRelationPreDataDto>(customerRelation);
            //customerRelation.UpdatePreJson = JsonSerializer.Serialize(preDate);
            await customerRelationRespository.UpdateAsync(customerRelation);
            await customerRelationHisRespository.InsertManyAsync(historys);
            return MessageResult.SuccessResult("修改成功");
        }

        /// <summary>
        /// Gets the customer relation asynchronous.
        /// </summary>
        /// <param name="Id">The identifier.</param>
        /// <returns></returns>
        public async Task<GetCustomerRelationResponseDto> GetCustomerRelationAsync(Guid Id)
        {
            var customerRelationRespository = LazyServiceProvider.LazyGetService<ICustomerRelationRepository>();
            var customerRelation = await customerRelationRespository.GetAsync(Id);
            var customerRelationMappingData = ObjectMapper.Map<CustomerRelation, GetCustomerRelationResponseDto>(customerRelation);

            return customerRelationMappingData;
        }

        /// <summary>
        /// Modifies the status asynchronous.
        /// </summary>
        /// <param name="requestDto">The request dto.</param>
        /// <returns></returns>
        public async Task ModifyStatusAsync(UpdateCustomerRelationStatusRequestDto requestDto)
        {
            var customerRelationRespository = LazyServiceProvider.LazyGetService<ICustomerRelationRepository>();
            var customerRelationHisRespository = LazyServiceProvider.LazyGetService<ICustomerRelationHistoryRepository>();
            var customerRelation = await customerRelationRespository.GetAsync(requestDto.Id);
            CustomerRelationHistory history = new();
            history.CustomerRelationId = requestDto.Id;
            history.ChangeField = "客户状态";
            history.ChangeInitiatorId = CurrentUser.Id;
            history.ChangeContent = $"\"{customerRelation.Status.GetDescription()}\"变更为\"{requestDto.Status.GetDescription()}\"";

            customerRelation.Status = requestDto.Status;
            await customerRelationRespository.UpdateAsync(customerRelation);

            await customerRelationHisRespository.InsertAsync(history);
        }

        /// <summary>
        /// 根据Id获取历史记录数据
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<CustomerRelationHistoryResponseDto>> GetHistoryRecordsByIdAsync(CustomerRelationHistoryRequestDto requestDto)
        {
            var hisRepository = LazyServiceProvider.LazyGetService<ICustomerRelationHistoryRepository>();
            var historyQuery = (await hisRepository.GetQueryableAsync()).AsNoTracking();
            var userQuery = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var query = historyQuery.Where(r => r.CustomerRelationId == requestDto.Id).OrderByDescending(o => o.CreationTime).GroupJoin(userQuery, (a) => a.ChangeInitiatorId, (b) => b.Id, (a, b) => new
            {
                his = a,
                user = b
            }).SelectMany(s => s.user.DefaultIfEmpty(), (a, b) => new CustomerRelationHistoryResponseDto
            {
                ChangeContent = a.his.ChangeContent,
                ChangeField = a.his.ChangeField,
                ChangeTime = a.his.ChangeTime,
                ChangeInitiatorName = b == null ? "系统自动更新" : b.Name,
                ChangeInitiatorId = a.his.ChangeInitiatorId,

            });
            var datas = await query.PagingIf(requestDto).ToListAsync();
            var count = await query.CountAsync();
            var result = new PagedResultDto<CustomerRelationHistoryResponseDto>(count, datas);
            return result;
        }
        /// <summary>
        /// Queries the vendor information.
        /// </summary>
        /// <returns></returns>
        private async Task<IQueryable<VendorDataResponseDto>> QueryVendorInfo()
        {
            var vendorPersonalQuery = (await LazyServiceProvider.LazyGetService<IVendorPersonalRepository>().GetQueryableAsync()).AsNoTracking();
            var vendorQuery = (await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync()).AsNoTracking();
            return vendorQuery.Join(vendorPersonalQuery, a => a.Id, b => b.VendorId, (a, b) => new VendorDataResponseDto
            {
                Province = b.Province,
                SPName = b.SPName,
                City = b.City,
                VendorCode = a.VendorCode,
                HospitalId = a.HospitalId,
                HospitalName = a.HospitalName,
                SPLevel = a.SPLevel,
                HosDepartment = a.HosDepartment,
                PTId = a.PTId,
                AcademicLevel = a.AcademicLevel,
                StandardHosDepId = a.StandardHosDepId,
            });

        }
        private async Task<MessageResult> GetToken(IConfiguration configuration)
        {
            try
            {
                var clientId = _configuration["Integrations:Graph:ClientId"];
                var tenantId = _configuration["Integrations:Graph:TenantId"];
                var certName = _configuration["Integrations:Graph:CertName"];
                var certPassword = _configuration["Integrations:Graph:CertPassword"];
                var authority = _configuration["Integrations:Graph:Authority"];
                var resource = _configuration["Integrations:Graph:Resource"];

                var cert = new X509Certificate2(AppDomain.CurrentDomain.BaseDirectory + certName, certPassword);
                authority = $"{authority}/{tenantId}";
                var app = ConfidentialClientApplicationBuilder.Create(clientId)
                                                            .WithCertificate(cert)
                                                            .WithAuthority(authority)
                                                            .Build();

                var scopes = new string[] { $"{resource}/.default" };
                var result = await app.AcquireTokenForClient(scopes).ExecuteAsync();
                return MessageResult.SuccessResult(result.AccessToken);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Integrations:Graph Token--{ex.Message}");
                return MessageResult.FailureResult(ex.ToString());
            }
        }
        /// <summary>
        /// 获取当前用户的部门
        /// </summary>
        /// <returns></returns>
        public async Task<List<GerUserDepartmentResponseDto>> GetUserDepartment()
        {
            if (CurrentUser.Id == null)
            {
                return [];
            }

            var relations = await _dataverseService.GetStaffDepartmentRelations(CurrentUser.Id.Value.ToString());
            var department = await _dataverseService.GetOrganizations();
            var result = relations.Join(department, a => a.DepartmentId, b => b.Id, (a, b) => b).Select(a => new GerUserDepartmentResponseDto { Id = a.Id, Name = a.DepartmentName.ClearBu() }).ToList();
            return result;
        }
        /// <summary>
        /// 删除客户关系
        /// </summary>
        /// <param name="Id">The identifier.</param>
        /// <returns></returns>
        public async Task<MessageResult> DeleteCustomerRelationAsync(Guid Id)
        {
            var customerRelationRespository = LazyServiceProvider.LazyGetService<ICustomerRelationRepository>();
            var data = await customerRelationRespository.AnyAsync(a => a.Id == Id && a.Status == CustomerStatus.Draft);
            if (!data)
            {
                return MessageResult.FailureResult("删除失败，只能删除草稿状态的数据。");
            }
            await customerRelationRespository.DeleteAsync(a => a.Id == Id);
            return MessageResult.SuccessResult("删除成功");
        }
        /// <summary>
        /// 联系分享人
        /// </summary>
        /// <param name="resquestDto">The resquest dto.</param>
        /// <returns></returns>
        public async Task<MessageResult> ContactShareAsync(ContactShareResquestDto resquestDto)
        {
            var webRoot = LazyServiceProvider.LazyGetService<IWebHostEnvironment>().WebRootFileProvider;
            var bodyHtml = await webRoot.GetFileInfo("Templates/Email/CustomerInfo.html").ReadAsStringAsync();
            //var shareRepository = LazyServiceProvider.LazyGetService<IContactShareRecordsRepository>();
            //var contactShareRecords = ObjectMapper.Map<ContactShareResquestDto, ContactShareRecords>(resquestDto);
            //await shareRepository.InsertAsync(contactShareRecords);
            //for (int i = 0; i < resquestDto.Count; i++)
            //{
            //    var item = resquestDto[i];

            //}
            resquestDto.BusinessUnits.ForEach(v =>
            {
                v = v switch { "CRDx" => "ADD", "MND" => "AND", _ => v, };
            });
            var userQuery = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetAsync(a => a.Id == CurrentUser.Id);
            var bussinessUnitId = userQuery.GetProperty<Guid?>("MainDepartmentId");
            var allDepartment = await LazyServiceProvider.LazyGetService<IDataverseService>().GetOrganizations(bussinessUnitId.ToString(), 1);

            var department = allDepartment.FirstOrDefault();
            var queryCustomerRelation = (await LazyServiceProvider.LazyGetService<ICustomerRelationRepository>().GetQueryableAsync()).AsNoTracking();
            var query = await queryCustomerRelation.Where(m => (m.Hospital == resquestDto.Hospital || m.CleanedHospitalName == resquestDto.Hospital) &&
            m.HospitalDepartment == resquestDto.Department && resquestDto.Title == m.DoctorTitle && m.Status != CustomerStatus.Invalid
            && resquestDto.BusinessUnits.Contains(m.BussinessUnit)).Select(s => new { s.BussinessUnit, s.Email }).Distinct().ToListAsync();

            List<InsertSendEmaillRecordDto> sendEmaillRecords = [];
            List<ContactShareRecords> contactShareRecords = [];
            for (int i = 0; i < query.Count; i++)
            {
                var unit = query[i];
                ContactShareRecords contactShare = new()
                {
                    Hospital = resquestDto.Hospital,
                    Department = resquestDto.Department,
                    Title = resquestDto.Title,
                    Email = unit.Email,
                    BusinessUnit = unit.BussinessUnit,
                };
                contactShareRecords.Add(contactShare);
                bodyHtml = bodyHtml.Replace("{Hospital}", resquestDto.Hospital);
                bodyHtml = bodyHtml.Replace("{Title}", resquestDto.Title);
                bodyHtml = bodyHtml.Replace("{Department}", resquestDto.Department);
                bodyHtml = bodyHtml.Replace("{Bu}", department?.DepartmentName.ClearBu());
                bodyHtml = bodyHtml.Replace("{Name}", CurrentUser.Name);
                bodyHtml = bodyHtml.Replace("{Email}", CurrentUser.Email);
                var sendEmaillRecord = new InsertSendEmaillRecordDto
                {
                    EmailAddress = unit.Email, //unit.Email,
                    Subject = $"客户信息分享申请",
                    Content = bodyHtml,
                    SourceType = EmailSourceType.CustomerInfo,
                    Status = SendStatus.Pending,
                    Attempts = 0,
                    CCAddresses = CurrentUser?.Email
                };
                var sendEmaillRecord1 = new InsertSendEmaillRecordDto
                {
                    EmailAddress = "<EMAIL>", //unit.Email,
                    Subject = $"客户信息分享申请",
                    Content = bodyHtml,
                    SourceType = EmailSourceType.CustomerInfo,
                    Status = SendStatus.Pending,
                    Attempts = 0,
                    CCAddresses = CurrentUser?.Email
                };
                sendEmaillRecords.Add(sendEmaillRecord);
                //sendEmaillRecords.Add(sendEmaillRecord1);
            }
            //记录邮件，并触发邮件发送功能
            var emailService = LazyServiceProvider.LazyGetService<IEmailService>();
            await emailService.InsertSendEmaillRecordAsync(sendEmaillRecords);

            var contactShareService = LazyServiceProvider.LazyGetService<IContactShareRecordsRepository>();
            await contactShareService.InsertManyAsync(contactShareRecords, true);
            // 调度作业
            BackgroundJob.Enqueue<SendEmailWorker>(a => a.DoWorkAsync());
            return MessageResult.SuccessResult("Success");
        }
        public async Task<MessageResult> Cleaned(IList<KeyValuePair<string, string>> valuePairs)
        {
            var hospitals = valuePairs.Select(s => s.Key).ToList();
            var customerRelationRespository = LazyServiceProvider.LazyGetService<ICustomerRelationRepository>();
            var query = await LazyServiceProvider.LazyGetService<ICustomerRelationRepository>().GetQueryableAsync();

            var datas = query.AsNoTracking().Where(s => s.CleanedHospitalName != null).ToList();

            //var group = valuePairs.GroupBy(s => s.Value);

            //foreach (var item in group)
            //{
            //    var key = item.Key;
            //    var count = item.Count();

            //    var hos = datas.Where(s => s.Hospital == key).ToList();
            //    foreach (var item2 in hos)
            //    {
            //        Random random = new Random();
            //        int number = random.Next(0, count);
            //        var value = item.ToArray()[number];
            //        item2.Hospital = value.Key;
            //        item2.CleanedHospitalName = value.Value;
            //    }

            //}
            foreach (var item in datas)
            {
                //var CleanHospitai = valuePairs.FirstOrDefault(s => s.Key == item.Hospital);
                //if (CleanHospitai.Equals(default(KeyValuePair<string, string>)))
                //{
                //    continue;
                //}
                //item.CleanedHospitalName = CleanHospitai.Value;
                item.PinYinSort = item.CleanedHospitalName.GetFristCharPinyin();
            }
            //await customerRelationRespository.UpdateManyAsync(datas);
            var context = await customerRelationRespository.GetDbContextAsync();
            //await context.BulkInsertAsync(addCustomerRelations);
            await context.BulkUpdateAsync(datas);
            return MessageResult.SuccessResult("Success");
        }
        /// <summary>
        /// Creates the asscee log.
        /// </summary>
        /// <param name="module">The module.</param>
        /// <returns></returns>
        private async Task CreateAssceeLog(CrossBuModule module)
        {
            var accessLogRepository = LazyServiceProvider.LazyGetService<IAccessLogRepository>();
            AccessLog accessLog = new() { Type = module };
            await accessLogRepository.InsertAsync(accessLog, true);
        }

        public async Task GetDashboard()
        {
            var start = DateTime.Now;
            var end = DateTime.Now;
            var queryCustomerRelation = (await LazyServiceProvider.LazyGetService<ICustomerRelationRepository>().GetQueryableAsync()).AsNoTracking();
            var queryActivity = (await LazyServiceProvider.LazyGetService<IActivityRepository>().GetQueryableAsync()).AsNoTracking();
            var queryAccessLog = (await LazyServiceProvider.LazyGetService<IAccessLogRepository>().GetQueryableAsync()).AsNoTracking();
            var queryContactShare = (await LazyServiceProvider.LazyGetService<IContactShareRecordsRepository>().GetQueryableAsync()).AsNoTracking();

            var queryableUser = (await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync()).AsNoTracking();
            var queryableRole = (await LazyServiceProvider.LazyGetService<IRepository<IdentityRole>>().GetQueryableAsync()).AsNoTracking();
            var queryableUserRole = (await LazyServiceProvider.LazyGetService<IRepository<IdentityUserRole>>().GetQueryableAsync()).AsNoTracking();

            var queryActivityUserInformation = (await LazyServiceProvider.LazyGetService<IActivityUserInformationRepository>().GetQueryableAsync()).AsNoTracking();

            var queryActivityBu = (await LazyServiceProvider.LazyGetService<IActivityBuRepository>().GetQueryableAsync()).AsNoTracking();

            var total1 = await queryCustomerRelation.CountAsync();
            var total2 = await queryActivity.CountAsync();
            var total3 = await queryActivity.CountAsync(a => a.CreationTime >= start && a.CreationTime < end.AddDays(1));
            var total4 = await queryCustomerRelation.CountAsync(a => a.CreationTime >= start && a.CreationTime < end.AddDays(1));
            var query = queryableUser.Include(m => m.Roles).SelectMany(a => a.Roles.DefaultIfEmpty(), (a, b) => new { userId = a.Id, RoleId = b.RoleId })
                .Join(queryableRole, a => a.RoleId, b => b.Id, (a, b) => new { a.userId, role = EF.Property<string>(b, "DisplayName") });
            var asseccQuery = queryAccessLog.Join(query, a => a.CreatorId, b => b.userId, (a, b) => new { a.Type, b.role });
            //客户关系访问量
            var customerAsseccQuery = asseccQuery.Where(a => a.Type == CrossBuModule.CustomerInfo).GroupBy(g => g.role, (key, val) => new { role = key, Count = val.Count() });

            // 市场活动访问量
            var activityAsseccQuery = asseccQuery.Where(a => a.Type == CrossBuModule.MarketActivity).GroupBy(g => g.role, (key, val) => new { role = key, Count = val.Count() });
            //客户关系互动量
            var total5 = await queryAccessLog.CountAsync(a => a.Type == CrossBuModule.CustomerInfo);
            var total7 = await queryContactShare.CountAsync();
            //市场活动互动量
            var total6 = await queryAccessLog.CountAsync(a => a.Type == CrossBuModule.MarketActivity);
            var total8 = await queryActivityUserInformation.CountAsync();
            var total9 = await queryActivityBu.CountAsync();
        }
        public async Task<List<GerUserDepartmentResponseDto>> GetDepartment(MenuType Type)
        {
            var queryCustomerRelation = (await LazyServiceProvider.LazyGetService<ICustomerRelationRepository>().GetQueryableAsync()).AsNoTracking();
            var queryActivity = (await LazyServiceProvider.LazyGetService<IActivityRepository>().GetQueryableAsync()).AsNoTracking();

            List<GerUserDepartmentResponseDto> gerUsers = [];
            if (MenuType.CustomerInfo == Type)
            {
                gerUsers = await queryCustomerRelation.Select(s => new GerUserDepartmentResponseDto { Id = s.BussinessUnitId.Value, Name = s.BussinessUnit }).Distinct().OrderBy(o => o.Name).ToListAsync();
            }
            else
            {
                gerUsers = await queryActivity.Select(s => new GerUserDepartmentResponseDto { Id = s.BussinessUnitId.Value, Name = s.BussinessUnitName }).Distinct().OrderBy(o => o.Name).ToListAsync();
            }

            return gerUsers;
        }
    }
}