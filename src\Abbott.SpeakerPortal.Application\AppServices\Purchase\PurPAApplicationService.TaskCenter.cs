﻿using Abbott.SpeakerPortal.AppServices.Common;
using Abbott.SpeakerPortal.AppServices.Vendor;
using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Approval;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.Approve;
using Abbott.SpeakerPortal.Contracts.Common.PowerApp;
using Abbott.SpeakerPortal.Contracts.Purchase.PurGRApplication;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.OEC.OECIntercepts;
using Abbott.SpeakerPortal.Entities.Purchase.PurGRApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Entities.VendorOrgnizations;
using Abbott.SpeakerPortal.Entities.VendorPersonals;
using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Utils;

using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc.ApplicationModels;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

using Org.BouncyCastle.Bcpg.OpenPgp;

using Senparc.CO2NET.Extensions;
using Senparc.Weixin.WxOpen.AdvancedAPIs.WxApp.WxAppJson;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Text.Json;
using System.Threading.Tasks;

using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;

using static Abbott.SpeakerPortal.Consts.EntitiesNameConsts;
using static Abbott.SpeakerPortal.Enums.DataverseEnums;
using static Abbott.SpeakerPortal.Enums.Purchase;
using static Abbott.SpeakerPortal.Enums.PurGRStatus;
using static Abbott.SpeakerPortal.Enums.PurPAStatus;

namespace Abbott.SpeakerPortal.AppServices.Purchase
{
    public partial class PurPAApplicationService : SpeakerPortalAppService, IPurPAApplicationService
    {
        /// <summary>
        /// 代付款审批任务列表
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<PATApprovalTaskResponseListDto>> GetPAApprovalListAsync(PurPAApprovalResquestDto requestDto)
        {
            var paApplicationRepository = LazyServiceProvider.LazyGetService<IPurPAApplicationReadonlyRepository>();
            var queryPA = await paApplicationRepository.GetQueryableAsync();
            var queryablePADetail = await LazyServiceProvider.LazyGetService<IPurPAApplicationDetailReadonlyRepository>().GetQueryableAsync();
            var queryableIntercept = await LazyServiceProvider.LazyGetService<IOECInterceptReadonlyRepository>().GetQueryableAsync();
            var query = queryPA.AsNoTracking()
                .Where(m => m.DeliveryMode == requestDto.DeliveryModes && requestDto.TaskType == m.TaskType)
                .WhereIf(!string.IsNullOrWhiteSpace(requestDto.ApplicationCode), m => m.ApplicationCode.Contains(requestDto.ApplicationCode))
                .WhereIf(!string.IsNullOrWhiteSpace(requestDto.VendorName), m => m.VendorName.Contains(requestDto.VendorName))
                .WhereIf(!string.IsNullOrWhiteSpace(requestDto.ApplicationDept), m => m.ApplyUserBuToDeptName.Contains(requestDto.ApplicationDept))
                .WhereIf(!string.IsNullOrWhiteSpace(requestDto.Applicat), m => m.ApplyUserName.Contains(requestDto.Applicat))
                .WhereIf(requestDto.ApplicatId!=null,m=>m.ApplyUserId==requestDto.ApplicatId)
                .WhereIf(!string.IsNullOrWhiteSpace(requestDto.AccepterName), m => m.AccepterName.Contains(requestDto.AccepterName))
                .WhereIf(requestDto.ApplyTimeStart.HasValue, m => m.ApplyTime >= requestDto.ApplyTimeStart.Value.Date)
                .WhereIf(requestDto.ApplyTimeEnd.HasValue, m => m.ApplyTime <= requestDto.ApplyTimeEnd.Value.Date.AddDays(1))
                .WhereIf(requestDto.AcceptedStartTime.HasValue, m => m.AcceptedTime >= requestDto.AcceptedStartTime.Value.Date)
                .WhereIf(requestDto.AcceptedEndTime.HasValue, m => m.AcceptedTime <= requestDto.AcceptedEndTime.Value.Date.AddDays(1))
                .WhereIf(requestDto.Status.HasValue, m => m.Status == requestDto.Status)
                .WhereIf(requestDto.ExpenseType.HasValue,m=>m.ExpenseType == requestDto.ExpenseType.Value);

            if (requestDto.TaskType == PAApprovalTaskStatus.Distributed)
            {
                query = query.OrderBy(a => a.Status == PurPAApplicationStatus.FinancialPreliminaryReview ? 0 : 1)
                .ThenByDescending(o => o.AcceptedTime).AsQueryable();
            }
            else
            {
                query = query.OrderByDescending(o => o.CreationTime).ThenBy(a => a.Status).AsQueryable();
            }

            var count = await query.CountAsync();
            query = query.Skip(requestDto.PageIndex * requestDto.PageSize).Take(requestDto.PageSize);

            var datas = query.Select(s => new PATApprovalTaskResponseListDto
            {
                Id = s.Id,
                ApplicationCode = s.ApplicationCode,
                VendorName = s.VendorName,
                ApplyDeptName = s.ApplyUserBuToDeptName,
                ApplyUserName = s.ApplyUserName,
                ApplyBuId = s.ApplyUserBu,
                ApplyTime = s.ApplyTime,
                PayTotalAmount = s.PayTotalAmount,
                Status = s.Status,
                AcceptedTime = s.AcceptedTime,
                AccepterName = s.AccepterName,
                Remark = s.SeperateRemark,
                ExpenseType = s.ExpenseType,
                //InterceptType = s.InterceptType,
                IsBackupInvoice = s.IsBackupInvoice,
            }).ToList();
            if (datas.Any())
            {
                var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
                var paDetails = queryablePADetail.GroupJoin(queryableIntercept, a => a.PRDetailId, b => b.PRDetailId, (a, b) => new { pad = a, intercepts = b })
                    .SelectMany(a => a.intercepts.DefaultIfEmpty(), (a, b) => new { a.pad, intercept = b })
                    .Where(a => datas.Select(x => x.Id).Contains(a.pad.PurPAApplicationId)).ToList();
                var interceptTypes = await dataverseService.GetDictionariesAsync(DictionaryType.InterceptTypes, null);
                foreach (var item in datas)
                {
                    var detail = paDetails.Where(a => a.pad.PurPAApplicationId == item.Id && a.intercept != null).ToList();
                    if (detail.Any())
                    {
                        List<string> interceptcodes = new List<string>();
                        foreach (var intercept in detail)
                        {
                            var codes = intercept.intercept.InterceptTypeCode.Split(',').ToList();
                            interceptcodes.AddRange(interceptTypes.Where(a => codes.Contains(a.Code)).Select(a => a.Name).ToList());
                        }
                        item.InterceptTypeText = string.Join(",", interceptcodes.Distinct());
                    }
                }
            }
            var result = new PagedResultDto<PATApprovalTaskResponseListDto>() { Items = datas, TotalCount = count };
            return result;
        }


        /// <summary>
        /// 代付款审批任务列表-导出
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        public async Task<List<ExportPAApprovalTaskDto>> ExportPAApprovalListAsync(PurPAApprovalResquestDto requestDto)
        {
            var paApplicationRepository = LazyServiceProvider.LazyGetService<IPurPAApplicationReadonlyRepository>();
            var queryPA = await paApplicationRepository.GetQueryableAsync();
            var query = queryPA
                .Where(m => m.DeliveryMode == requestDto.DeliveryModes && requestDto.TaskType == m.TaskType)
                .WhereIf(!string.IsNullOrWhiteSpace(requestDto.ApplicationCode), m => m.ApplicationCode.Contains(requestDto.ApplicationCode))
                .WhereIf(!string.IsNullOrWhiteSpace(requestDto.VendorName), m => m.VendorName.Contains(requestDto.VendorName))
                .WhereIf(!string.IsNullOrWhiteSpace(requestDto.ApplicationDept), m => m.ApplyUserBuToDeptName.Contains(requestDto.ApplicationDept))
                .WhereIf(!string.IsNullOrWhiteSpace(requestDto.Applicat), m => m.ApplyUserName.Contains(requestDto.Applicat))
                .WhereIf(!string.IsNullOrWhiteSpace(requestDto.AccepterName), m => m.AccepterName.Contains(requestDto.AccepterName))
                .WhereIf(requestDto.ApplyTimeStart.HasValue, m => m.ApplyTime >= requestDto.ApplyTimeStart.Value.Date)
                .WhereIf(requestDto.ApplyTimeEnd.HasValue, m => m.ApplyTime <= requestDto.ApplyTimeEnd.Value.Date.AddDays(1))
                .WhereIf(requestDto.AcceptedStartTime.HasValue, m => m.AcceptedTime >= requestDto.AcceptedStartTime.Value.Date)
                .WhereIf(requestDto.AcceptedEndTime.HasValue, m => m.AcceptedTime <= requestDto.AcceptedEndTime.Value.Date.AddDays(1))
                .WhereIf(requestDto.Status.HasValue, m => m.Status == requestDto.Status)
                .WhereIf(requestDto.ExpenseType.HasValue, m => m.ExpenseType == requestDto.ExpenseType.Value);

            var result = query.Select(a => new ExportPAApprovalTaskDto
            {
                ApplicationCode = a.ApplicationCode,
                VendorName = a.VendorName,
                DepartmentName = a.ApplyUserBuToDeptName,
                ApplyUserName = a.ApplyUserName,
                ApplyTime = a.ApplyTime.ToString("yyyy-MM-dd HH:mm:ss"),
                ExpenseTypeName = a.ExpenseType.HasValue? a.ExpenseType.Value.GetDescription() : "",
                AcceptedDate = a.AcceptedTime.HasValue? a.AcceptedTime.Value.ToString("yyyy-MM-dd"):"",
                AccepterName = a.AccepterName,
                SeperateRemark = a.SeperateRemark,
                PaymentAmount = a.PayTotalAmount,
                IsBackupInvoice = a.IsBackupInvoice ? "是" : "否",
                StatusName = a.Status.GetDescription(),
            })
            .ToList();
            return result;
        }

        /// <summary>
        /// 线上线下分单单据
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        public async Task<MessageResult> OrderAllocationAsync(PAAllocationRequestDto requestDto)
        {
            //创建一个事物
            using var unitWork = UnitOfWorkManager.Begin(new Volo.Abp.Uow.AbpUnitOfWorkOptions());
            var paApplicationRepository = LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>();
            var queryPA = await paApplicationRepository.GetQueryableAsync();
            var Ids = requestDto.Allocations.Select(s => s.Id).ToList();
            var query = queryPA.Where(m => Ids.Contains(m.Id)).ToList();
            //判断单据是否有在拦截中
            var intereptOrderCode = query.Where(m => m.TaskType == PAApprovalTaskStatus.Intercepted).Select(s => s.ApplicationCode);
            var intereptOrderCount = intereptOrderCode.Count();
            //待分配的
            var paddingOrder = query.Where(m => m.TaskType == PAApprovalTaskStatus.ToBeDistributed);
            //待分配的数量
            var paddingOrderCount =  paddingOrder.Count();

            var paddingOrderList = paddingOrder.ToList();
            var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var users = queryableUser.FirstOrDefault(u => u.Id == requestDto.AccepterId);
            if (users == null)
            {
                return MessageResult.FailureResult("分配用户不存在!");
            }
            var exemptType = EnumUtil.GetEnumIdValues<WorkflowTypeName>().ToDictionary(a => (WorkflowTypeName)a.Key, a => a.Value);
            List<CreateApprovalDto> CreateApprovalDtos = [];
            List<AddApprovalRecordDto> taskRecord = new List<AddApprovalRecordDto>();
            foreach (var item in paddingOrderList)
            {
                item.AcceptedTime = DateTime.Now;
                item.AccepterId = requestDto.AccepterId;
                item.AccepterName = users.Name;
                item.TaskType = PAApprovalTaskStatus.Distributed;
                item.Status = PurPAApplicationStatus.FinancialPreliminaryReview;
                item.SeperateRemark = requestDto.Allocations.First(f => f.Id == item.Id)?.Remark;
                CreateApprovalDtos.Add(new CreateApprovalDto
                {
                    Name = exemptType[WorkflowTypeName.PaymentFinanceApprove],
                    Department = item.ApplyUserBu.ToString(),
                    BusinessFormId = item.Id.ToString(),
                    BusinessFormNo = item.ApplicationCode,
                    BusinessFormName = NameConsts.PAApplication,
                    //FirstApprover = purExemptDetail.PurchaserId.ToString(),
                    Status = ApprovalPowerAppStatus.PendingForApproval,
                    Submitter = CurrentUser.Id.ToString(),
                    OriginalApprovalId = item.ApplyUserId,
                    WorkflowType = WorkflowTypeName.PaymentFinanceApprove,
                    InstanceName = $"{exemptType[WorkflowTypeName.PaymentFinanceApprove]}-{DateTime.Now.ToString("yyMMdd-hhmmss")}",
                });
                taskRecord.Add(new AddApprovalRecordDto {
                    FormId = item.Id,
                    ApprovalId = users.Id,//20250109 ytw 单据接收人
                    //OriginalApprovalId = CurrentUser.Id.Value,
                    Status = ApprovalOperation.AllocateOrder,
                    Remark = "单据接收",
                    ApprovalTime = DateTime.Now,
                    WorkStep = "单据接收",
                    Name = "单据接收"
                });
            }
            if (CreateApprovalDtos.Any())
            {
                var createOK = await CreateApprovalTask(CreateApprovalDtos);
                if (!createOK)
                {
                    //审批任务创建失败，回滚
                    await unitWork.RollbackAsync();
                    return MessageResult.FailureResult("审批任务创建失败");
                }
                await paApplicationRepository.UpdateManyAsync(paddingOrderList);
                await LazyServiceProvider.LazyGetService<IApproveService>().AddApprovalRecordAsync(taskRecord);
            }
            PAAllocationResponseDto allocationResponseDto = new()
            {
                SuccessCount = paddingOrderCount,
                FailureCount = intereptOrderCount,
            };
            if (intereptOrderCount > 0)
            {
                allocationResponseDto.IsAllSuccess = false;
                allocationResponseDto.FailureOrder = intereptOrderCode.JoinAsString(",");
                allocationResponseDto.FailureReason = "单据拦截中";
            }
            return MessageResult.SuccessResult(allocationResponseDto);
        }
        /// <summary>
        /// 分单单据按照比例
        /// </summary>
        /// <returns></returns>
        public async Task<MessageResult> OrderAllocationByPercentageAsync(PAAllocationPercentageRequestDto requestDto)
        {
            //创建一个事物
            using var unitWork = UnitOfWorkManager.Begin(new Volo.Abp.Uow.AbpUnitOfWorkOptions());
            var percentage = requestDto.AccepterPercentages.Sum(s => s.AccepterPercentage);
            //判断分单比例是否是100%
            if (percentage != 100)
            {
                return MessageResult.FailureResult("分配比例错误!");
            }

            var paApplicationRepository = LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>();
            var queryPA = await paApplicationRepository.GetQueryableAsync();
            var paddingOrder = queryPA.Where(m => m.TaskType == PAApprovalTaskStatus.ToBeDistributed && m.DeliveryMode == DeliveryModes.OnLine);

            //判断单据是否有在拦截中
            //var intereptOrderCode = query.Where(m => m.TaskType == PAApprovalTaskStatus.Intercepted).Select(s => s.ApplicationCode);
            //var intereptOrderCount = await intereptOrderCode.CountAsync();
            //待分配的
            //var paddingOrder = query.Where(m => m.TaskType == PAApprovalTaskStatus.ToBeDistributed);
            //待分配的数量
            var paddingOrderCount = await paddingOrder.CountAsync();
            var paddingOrderList = paddingOrder.ToList();

            var userIds = requestDto.AccepterPercentages.Select(m => m.AccepterId).ToList();

            var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var users = queryableUser.Where(u => userIds.Contains(u.Id)).ToList();
            var exemptType = EnumUtil.GetEnumIdValues<WorkflowTypeName>().ToDictionary(a => (WorkflowTypeName)a.Key, a => a.Value);
            List<CreateApprovalDto> CreateApprovalDtos = [];
            List<AddApprovalRecordDto> taskRecord = new List<AddApprovalRecordDto>();
            int currentIndex = 0;
            int index = 1;
            foreach (var item in requestDto.AccepterPercentages)
            {
                int num;
                if (index == requestDto.AccepterPercentages.Count())
                    num = paddingOrderCount - currentIndex;
                else
                    num = (int)Math.Round(paddingOrderCount * (item.AccepterPercentage / 100), 0, MidpointRounding.AwayFromZero);
                var allocations = paddingOrderList.Skip(currentIndex).Take(num);
                var user = users.FirstOrDefault(f => f.Id == item.AccepterId);
                if (user == null)
                    return MessageResult.FailureResult("分配用户不存在!");
                foreach (var item1 in allocations)
                {
                    item1.AcceptedTime = DateTime.Now;
                    item1.AccepterId = user.Id;
                    item1.AccepterName = user.Name;
                    item1.TaskType = PAApprovalTaskStatus.Distributed;
                    item1.Status = PurPAApplicationStatus.FinancialPreliminaryReview;
                    CreateApprovalDtos.Add(new CreateApprovalDto
                    {
                        Name = exemptType[WorkflowTypeName.PaymentFinanceApprove],
                        Department = item1.ApplyUserBu.ToString(),
                        BusinessFormId = item1.Id.ToString(),
                        BusinessFormNo = item1.ApplicationCode,
                        BusinessFormName = NameConsts.PAApplication,
                        //FirstApprover = purExemptDetail.PurchaserId.ToString(),
                        Status = ApprovalPowerAppStatus.PendingForApproval,
                        Submitter = CurrentUser.Id.ToString(),
                        OriginalApprovalId = item1.ApplyUserId,
                        WorkflowType = WorkflowTypeName.PaymentFinanceApprove,
                        InstanceName = $"{exemptType[WorkflowTypeName.PaymentFinanceApprove]}-{DateTime.Now.ToString("yyMMdd-hhmmss")}",
                    });
                    taskRecord.Add(new AddApprovalRecordDto
                    {
                        FormId = item1.Id,
                        ApprovalId = user.Id,
                        Status = ApprovalOperation.AllocateOrder,
                        Remark = "单据接收",
                        ApprovalTime = DateTime.Now,
                        WorkStep = "单据接收",
                        Name = "单据接收"
                    });
                };
                currentIndex += num;
                index++;
            }
            //更新单据
            var createOK = await CreateApprovalTask(CreateApprovalDtos);
            if (!createOK)
            {
                //await bdRepository.UpdateAsync(bdApplication);
                await unitWork.RollbackAsync();
                return MessageResult.FailureResult("审批任务创建失败");
            }
            await paApplicationRepository.UpdateManyAsync(paddingOrderList);
            await LazyServiceProvider.LazyGetService<IApproveService>().AddApprovalRecordAsync(taskRecord);

            PAAllocationResponseDto allocationResponseDto = new()
            {
                SuccessCount = paddingOrderCount,
                FailureCount = 0,
            };
            //if (intereptOrderCount > 0)
            //{
            //    allocationResponseDto.IsAllSuccess = false;
            //    allocationResponseDto.FailureOrder = intereptOrderCode.JoinAsString(",");
            //    allocationResponseDto.FailureReason = "单据拦截中";
            //}
            return MessageResult.SuccessResult(allocationResponseDto);
        }
        /// <summary>
        /// 生成审批任务
        /// </summary>
        /// <returns></returns>
        private async Task<bool> CreateApprovalTask(List<CreateApprovalDto> createApprovalDtos)
        {
            //3949【付款财务审批】财务分单时，若遇到系统卡顿导致ABP分单的状态没有更新，但是PP端已经生成了任务，再次分单的时候会导致重复生成审批任务 ytw
            var approvalRecord = LazyServiceProvider.LazyGetService<IInitializeApprovalRecordService>();
            foreach (var item in createApprovalDtos) 
            {
                //这里没考虑批量
                await approvalRecord.InsertInitApprovalRecordAsync(new InitializeApprovalRecordDto
                {
                    BusinessFormName = EntitiesNameConsts.NameConsts.PAApplication,
                    BusinessId = Guid.Parse(item.BusinessFormId),
                    InstanceId = Guid.NewGuid(),
                    Status = InitApprovalRecordStatus.Pending,
                    MaxRetries = 3,
                    RequestContent = JsonSerializer.Serialize(item)
                });
            }
            return true;
            var approveService = LazyServiceProvider.LazyGetService<IApproveService>();
            //创建审批任务
            return await approveService.InitiateApprovalAsync(createApprovalDtos);
        }
        /// <summary>
        /// 获取线下
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        public async Task<PATApprovalTaskResponseListDto> GetPAOfflineApprovalAsync(string Code)
        {
            var paApplicationRepository = LazyServiceProvider.LazyGetService<IPurPAApplicationReadonlyRepository>();
            var queryPA = await paApplicationRepository.GetQueryableAsync();
            var query = queryPA.AsNoTracking()
                .Where(m => m.DeliveryMode == DeliveryModes.OffLine && PAApprovalTaskStatus.ToBeDistributed == m.TaskType && Code == m.ApplicationCode);
            var data = query.Select(s => new PATApprovalTaskResponseListDto
            {
                Id = s.Id,
                ApplicationCode = s.ApplicationCode,
                VendorName = s.VendorName,
                ApplyDeptName = s.ApplyUserBuToDeptName,
                ApplyBuId = s.ApplyUserBu,
                ApplyUserName = s.ApplyUserName,
                ApplyTime = s.ApplyTime,
                PayTotalAmount = s.PayTotalAmount,
                Status = s.Status,
                AcceptedTime = s.AcceptedTime,
                AccepterName = s.AccepterName,
                Remark = s.SeperateRemark,
                ExpenseType = s.ExpenseType,
                //InterceptType = s.InterceptType,
                IsBackupInvoice = s.IsBackupInvoice,
            }).FirstOrDefault();
            return data;
        }
        /// <summary>
        /// 获取单据Bu下的财务审批岗
        /// </summary>
        /// <param name="BuIds"></param>
        /// <returns></returns>
        public async Task<List<ApprovalPersonListResponseDto>> GetFinancialInitialUsersAsync(List<Guid> BuIds)
        {
            BuIds = BuIds.Distinct().ToList();
            if (BuIds.Count == 0)
            {
                var paApplicationRepository = LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>();
                var queryPA = await paApplicationRepository.GetQueryableAsync();
                BuIds = queryPA.Where(m => m.TaskType == PAApprovalTaskStatus.ToBeDistributed && m.DeliveryMode == DeliveryModes.OnLine).Select(s => s.ApplyUserBu).Distinct().ToList();
            }
            //var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var approveService = LazyServiceProvider.LazyGetService<IApproveService>();
            //var staffDepartment = await dataverseService.GetStaffDepartmentRelations();
            //var userIds = staffDepartment.Where(m => BuIds.Contains(m.DepartmentId)).Select(s => s.UserId).Distinct().ToList();
            var approvalPersonList = await approveService.GetApprovalPersonListAsync(new ApprovalPersonListRequestDto { PositionType = PositionType.FinancialInitialApproval, BuIds = BuIds });
            return approvalPersonList;
        }

        /// <summary>
        /// 为全流程报表增加数据埋点
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> RecordPaCurrentProcessor(CurrentApprovalTaskDto request)
        {
            var paApplicationRepository = LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>();
            try
            {
                var pa = await paApplicationRepository.FirstOrDefaultAsync(a => a.Id == request.FormId);

                pa.CurrentProcessorIds = string.Join(",", request.Approver.Select(a => a.Id));
                pa.CurrentProcessorName = string.Join(",", request.Approver.Select(a => a.Name));
                await paApplicationRepository.UpdateAsync(pa);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"RecordPaCurrentProcessor：记录PA下一步处理人失败。{Newtonsoft.Json.JsonConvert.SerializeObject(request)}");
                return MessageResult.FailureResult();
            }

            return MessageResult.SuccessResult();
        }
    }
}
