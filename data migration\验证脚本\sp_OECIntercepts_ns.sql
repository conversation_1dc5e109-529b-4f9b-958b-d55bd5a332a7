CREATE PROCEDURE dbo.sp_OECIntercepts_ns
AS 
BEGIN
	--飞检拦截状态信息-基础信息
with OECIntercepts_ns as (
select 
oec.Id,
t1.Id AS PRDetailId,
oec.InterceptTypeCode,
oec.InterceptStatus,
t2.spk_NexBPMCode,
oec.InterceptByUserName,
oec.InterceptTime,
oec.InterceptRemark,
oec.ResolvedInterceptType,
oec.ExtraProperties,
oec.ConcurrencyStamp,
oec.CreationTime,
oec.CreatorId,
oec.LastModificationTime,
oec.LastModifierId,
oec.IsDeleted,
oec.DeleterId,
oec.DeletionTime
FROM [PLATFORM_ABBOTT].[dbo].[OECIntercepts_tmp] oec
left join
[PLATFORM_ABBOTT].[dbo].[PurPRApplicationDetails_tmp] t1
on SUBSTRING(oec.PRDetailId, 1, CHARINDEX('+', oec.PRDetailId) - 1) = t1.ProcInstId 
and SUBSTRING(oec.PRDetailId, CHARINDEX('+', oec.PRDetailId) + 1,LEN(oec.PRDetailId) - CHARINDEX('+', oec.PRDetailId)) = t1.RowNo
left join 
[PLATFORM_ABBOTT].[dbo].[spk_staffmasterdata] t2
on oec.InterceptByUserId = t2.bpm_id)
select * into #OECIntercepts_ns from OECIntercepts_ns 

 IF OBJECT_ID(N'PLATFORM_ABBOTT.dbo.OECIntercepts_ns ', N'U') IS NOT NULL
	BEGIN
		drop table PLATFORM_ABBOTT.dbo.OECIntercepts_ns
		select *
        into PLATFORM_ABBOTT.dbo.OECIntercepts_ns from #OECIntercepts_ns
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into PLATFORM_ABBOTT.dbo.OECIntercepts_ns from #OECIntercepts_ns
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END

END;