select
a.ID
,vf.id as <PERSON>a<PERSON><PERSON>
,a.VNSTAT
,a.VMID
,a.VENDOR
,a.VNDNAM
,a.VNDAD1
,a.VNDAD2
,a.VSTATE
,a.VPOST
,a.VTERMS
,a.VTYPE
,a.VPAYTO
,a.VDTLPD
,a.VDAYCL
,a.VGL
,a.V1099
,a.V1099C
,a.VPHONE
,a.VCMPNY
,a.VCURR
,a.VPAYTY
,a.V1TIME
,a.VCORPV
,a.VHOLD
,a.VHOLDT
,a.VPYTYR
,a.VDSCAV
,a.VDSCTK
,a.VDPURS
,a.VNNEXT
,a.VNGEN
,a.VNALPH
,a.VNUNAL
,a.VCON
,a.VCOUN
,a.V1099S
,a.VPAD1
,a.VPAD2
,a.VPSTE
,a.VPPST
,a.VPCON
,a.VPCOU
,a.VMFRM
,a.VMMAT
,a.VTAX
,a.VPPHN
,a.VMFSCD
,a.VMIDN<PERSON>
,a.VTAXCD
,a.VMXNBR
,a.VMXCRT
,a.VMXDTE
,a.VMXMAX
,a.VFOB
,a.VMCLA<PERSON>
,a.VMRAT<PERSON>
,a.VMDCPX
,a.VMSRCC
,a.VMSRNO
,a.VMPREQ
,a.VMRELP
,a.VMVFAX
,a.VMPFAX
,a.VMRELM
,a.VMPFRQ
,a.VMSHPC
,a.VMTIMP
,a.V<PERSON>ART
,a.VMPTYP
,a.VMTRBR
,a.VMDATN
,a.VNDAD3
,a.VPAD3
,a.VMPDAT
,a.VMROUT
,a.VMSCOM
,a.VMVCLS
,a.VMBANK
,a.VMCARR
,a.VMSOFR
,a.VMDPT1
,a.VMDPT2
,a.VMDPT3
,a.VMCAPN
,a.VMECSN
,a.VMTRMC
,a.VMAD4
,a.VMAD5
,a.VMAD6
,a.VMLANG
,a.VMPAD4
,a.VMPAD5
,a.VMPAD6
,a.VMDUN4
,a.VMUCC
,a.VMIAIG
,a.VMEAN
,a.VMSHFM
,a.VMMNTR
,a.VMCCEX
,a.VMAYTD
,a.VMBNKC
,a.VMBRNO
,a.VMBNKA
,a.VMREF1
,a.VMREF2
,a.VMREF3
,a.VMREF4
,a.VMREF5
,a.VMCRPF
,a.VMACPT
,a.VMACUR
,GETDATE() Update_TIME
,GETDATE() Update_Date
into #BPCSAVM
from PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a   
left join PLATFORM_ABBOTT.dbo.VendorFinancials_tmp vf 
on a.VENDOR =vf.VendorCode and a.VCMPNY =vf.Company ;

use Speaker_Portal;


UPDATE a 
SET 
	a.VNSTAT          =       b.VNSTAT
	,a.VMID            =       b.VMID
	,a.VENDOR          =       b.VENDOR
	,a.VNDNAM          =       b.VNDNAM
	,a.VNDAD1          =       b.VNDAD1
	,a.VNDAD2          =       b.VNDAD2
	,a.VSTATE          =       b.VSTATE
	,a.VPOST           =       b.VPOST
	,a.VTERMS          =       b.VTERMS
	,a.VTYPE           =       b.VTYPE
	,a.VPAYTO          =       b.VPAYTO
	,a.VDTLPD          =       b.VDTLPD
	,a.VDAYCL          =       b.VDAYCL
	,a.VGL             =       b.VGL
	,a.V1099           =       b.V1099
	,a.V1099C          =       b.V1099C
	,a.VPHONE          =       b.VPHONE
	,a.VCMPNY          =       b.VCMPNY
	,a.VCURR           =       b.VCURR
	,a.VPAYTY          =       b.VPAYTY
	,a.V1TIME          =       b.V1TIME
	,a.VCORPV          =       b.VCORPV
	,a.VHOLD           =       b.VHOLD
	,a.VHOLDT          =       b.VHOLDT
	,a.VPYTYR          =       b.VPYTYR
	,a.VDSCAV          =       b.VDSCAV
	,a.VDSCTK          =       b.VDSCTK
	,a.VDPURS          =       b.VDPURS
	,a.VNNEXT          =       b.VNNEXT
	,a.VNGEN           =       b.VNGEN
	,a.VNALPH          =       b.VNALPH
	,a.VNUNAL          =       b.VNUNAL
	,a.VCON            =       b.VCON
	,a.VCOUN           =       b.VCOUN
	,a.V1099S          =       b.V1099S
	,a.VPAD1           =       b.VPAD1
	,a.VPAD2           =       b.VPAD2
	,a.VPSTE           =       b.VPSTE
	,a.VPPST           =       b.VPPST
	,a.VPCON           =       b.VPCON
	,a.VPCOU           =       b.VPCOU
	,a.VMFRM           =       b.VMFRM
	,a.VMMAT           =       b.VMMAT
	,a.VTAX            =       b.VTAX
	,a.VPPHN           =       b.VPPHN
	,a.VMFSCD          =       b.VMFSCD
	,a.VMIDNM          =       b.VMIDNM
	,a.VTAXCD          =       b.VTAXCD
	,a.VMXNBR          =       b.VMXNBR
	,a.VMXCRT          =       b.VMXCRT
	,a.VMXDTE          =       b.VMXDTE
	,a.VMXMAX          =       b.VMXMAX
	,a.VFOB            =       b.VFOB
	,a.VMCLAS          =       b.VMCLAS
	,a.VMRATE          =       b.VMRATE
	,a.VMDCPX          =       b.VMDCPX
	,a.VMSRCC          =       b.VMSRCC
	,a.VMSRNO          =       b.VMSRNO
	,a.VMPREQ          =       b.VMPREQ
	,a.VMRELP          =       b.VMRELP
	,a.VMVFAX          =       b.VMVFAX
	,a.VMPFAX          =       b.VMPFAX
	,a.VMRELM          =       b.VMRELM
	,a.VMPFRQ          =       b.VMPFRQ
	,a.VMSHPC          =       b.VMSHPC
	,a.VMTIMP          =       b.VMTIMP
	,a.VMPART          =       b.VMPART
	,a.VMPTYP          =       b.VMPTYP
	,a.VMTRBR          =       b.VMTRBR
	,a.VMDATN          =       b.VMDATN
	,a.VNDAD3          =       b.VNDAD3
	,a.VPAD3           =       b.VPAD3
	,a.VMPDAT          =       b.VMPDAT
	,a.VMROUT          =       b.VMROUT
	,a.VMSCOM          =       b.VMSCOM
	,a.VMVCLS          =       b.VMVCLS
	,a.VMBANK          =       b.VMBANK
	,a.VMCARR          =       b.VMCARR
	,a.VMSOFR          =       b.VMSOFR
	,a.VMDPT1          =       b.VMDPT1
	,a.VMDPT2          =       b.VMDPT2
	,a.VMDPT3          =       b.VMDPT3
	,a.VMCAPN          =       b.VMCAPN
	,a.VMECSN          =       b.VMECSN
	,a.VMTRMC          =       b.VMTRMC
	,a.VMAD4           =       b.VMAD4
	,a.VMAD5           =       b.VMAD5
	,a.VMAD6           =       b.VMAD6
	,a.VMLANG          =       b.VMLANG
	,a.VMPAD4          =       b.VMPAD4
	,a.VMPAD5          =       b.VMPAD5
	,a.VMPAD6          =       b.VMPAD6
	,a.VMDUN4          =       b.VMDUN4
	,a.VMUCC           =       b.VMUCC
	,a.VMIAIG          =       b.VMIAIG
	,a.VMEAN           =       b.VMEAN
	,a.VMSHFM          =       b.VMSHFM
	,a.VMMNTR          =       b.VMMNTR
	,a.VMCCEX          =       b.VMCCEX
	,a.VMAYTD          =       b.VMAYTD
	,a.VMBNKC          =       b.VMBNKC
	,a.VMBRNO          =       b.VMBRNO
	,a.VMBNKA          =       b.VMBNKA
	,a.VMREF1          =       b.VMREF1
	,a.VMREF2          =       b.VMREF2
	,a.VMREF3          =       b.VMREF3
	,a.VMREF4          =       b.VMREF4
	,a.VMREF5          =       b.VMREF5
	,a.VMCRPF          =       b.VMCRPF
	,a.VMACPT          =       b.VMACPT
	,a.VMACUR          =       b.VMACUR
	,a.Update_TIME     =       b.Update_TIME
	,a.Update_Date     =       b.Update_Date
	,a.FINAID          =   b.FINAID
FROM dbo.BPCSAVM a
left join #BPCSAVM  b
ON a.id=b.id;


INSERT INTO dbo.BPCSAVM
(
VNSTAT
,VMID
,VENDOR
,VNDNAM
,VNDAD1
,VNDAD2
,VSTATE
,VPOST
,VTERMS
,VTYPE
,VPAYTO
,VDTLPD
,VDAYCL
,VGL
,V1099
,V1099C
,VPHONE
,VCMPNY
,VCURR
,VPAYTY
,V1TIME
,VCORPV
,VHOLD
,VHOLDT
,VPYTYR
,VDSCAV
,VDSCTK
,VDPURS
,VNNEXT
,VNGEN
,VNALPH
,VNUNAL
,VCON
,VCOUN
,V1099S
,VPAD1
,VPAD2
,VPSTE
,VPPST
,VPCON
,VPCOU
,VMFRM
,VMMAT
,VTAX
,VPPHN
,VMFSCD
,VMIDNM
,VTAXCD
,VMXNBR
,VMXCRT
,VMXDTE
,VMXMAX
,VFOB
,VMCLAS
,VMRATE
,VMDCPX
,VMSRCC
,VMSRNO
,VMPREQ
,VMRELP
,VMVFAX
,VMPFAX
,VMRELM
,VMPFRQ
,VMSHPC
,VMTIMP
,VMPART
,VMPTYP
,VMTRBR
,VMDATN
,VNDAD3
,VPAD3
,VMPDAT
,VMROUT
,VMSCOM
,VMVCLS
,VMBANK
,VMCARR
,VMSOFR
,VMDPT1
,VMDPT2
,VMDPT3
,VMCAPN
,VMECSN
,VMTRMC
,VMAD4
,VMAD5
,VMAD6
,VMLANG
,VMPAD4
,VMPAD5
,VMPAD6
,VMDUN4
,VMUCC
,VMIAIG
,VMEAN
,VMSHFM
,VMMNTR
,VMCCEX
,VMAYTD
,VMBNKC
,VMBRNO
,VMBNKA
,VMREF1
,VMREF2
,VMREF3
,VMREF4
,VMREF5
,VMCRPF
,VMACPT
,VMACUR
--,Update_TIME
--,Update_Date
,ID
,FINAID
)
SELECT
VNSTAT
,VMID
,VENDOR
,VNDNAM
,VNDAD1
,VNDAD2
,VSTATE
,VPOST
,VTERMS
,VTYPE
,VPAYTO
,VDTLPD
,VDAYCL
,VGL
,V1099
,V1099C
,VPHONE
,VCMPNY
,VCURR
,VPAYTY
,V1TIME
,VCORPV
,VHOLD
,VHOLDT
,VPYTYR
,VDSCAV
,VDSCTK
,VDPURS
,VNNEXT
,VNGEN
,VNALPH
,VNUNAL
,VCON
,VCOUN
,V1099S
,VPAD1
,VPAD2
,VPSTE
,VPPST
,VPCON
,VPCOU
,VMFRM
,VMMAT
,VTAX
,VPPHN
,VMFSCD
,VMIDNM
,VTAXCD
,VMXNBR
,VMXCRT
,VMXDTE
,VMXMAX
,VFOB
,VMCLAS
,VMRATE
,VMDCPX
,VMSRCC
,VMSRNO
,VMPREQ
,VMRELP
,VMVFAX
,VMPFAX
,VMRELM
,VMPFRQ
,VMSHPC
,VMTIMP
,VMPART
,VMPTYP
,VMTRBR
,VMDATN
,VNDAD3
,VPAD3
,VMPDAT
,VMROUT
,VMSCOM
,VMVCLS
,VMBANK
,VMCARR
,VMSOFR
,VMDPT1
,VMDPT2
,VMDPT3
,VMCAPN
,VMECSN
,VMTRMC
,VMAD4
,VMAD5
,VMAD6
,VMLANG
,VMPAD4
,VMPAD5
,VMPAD6
,VMDUN4
,VMUCC
,VMIAIG
,VMEAN
,VMSHFM
,VMMNTR
,VMCCEX
,VMAYTD
,VMBNKC
,VMBRNO
,VMBNKA
,VMREF1
,VMREF2
,VMREF3
,VMREF4
,VMREF5
,VMCRPF
,VMACPT
,VMACUR
--,Update_TIME
--,Update_Date
,ID
,FINAID
FROM #BPCSAVM a
WHERE not exists (select * from dbo.BPCSAVM where id=a.id);





