﻿using System;
using System.Text.Json.Serialization;
using Abbott.SpeakerPortal.Enums;

namespace Abbott.SpeakerPortal.Contracts.Budget.FocSubbudget
{
    public class FocMonthlyBudgetDto
    {
        [JsonIgnore]
        public Guid Id { get; set; } = Guid.NewGuid();
        /// <summary>
        /// 预算数量
        /// </summary>
        public int BudgetQty { get; set; }
        /// <summary>
        /// 是否可用
        /// </summary>
        public bool Status { get; set; } = false;
        /// <summary>
        /// 月份
        /// </summary>
        public Month Month { get; set; }
    }

    public class FocMonthlyBudgetNullAbleDto
    {
        [JsonIgnore]
        public Guid Id { get; set; } = Guid.NewGuid();
        /// <summary>
        /// 预算数量
        /// </summary>
        public int? BudgetQty { get; set; }
        /// <summary>
        /// 是否可用
        /// </summary>
        public bool? Status { get; set; } = false;
        /// <summary>
        /// 月份
        /// </summary>
        public Month Month { get; set; }
    }
}
