select newid() as spk_NexBPMCode,* into #spk_financialapprovalruleconfig from (
select * from spk_financialapprovalruleconfig_Tmp)A

IF OBJECT_ID(N'dbo.spk_financialapprovalruleconfig', N'U') IS NOT NULL
BEGIN
	update a 
	set a.spk_BPMCode      = b.spk_BPMCode
       ,a.spk_name         = b.spk_name
       ,a.spk_chinesevalue = b.spk_chinesevalue
       ,a.spk_englishvalue = b.spk_englishvalue
    from dbo.spk_financialapprovalruleconfig a
    join #spk_financialapprovalruleconfig b on a.spk_BPMCode = b.spk_BPMCode
    
    insert into dbo.spk_financialapprovalruleconfig
    select a.spk_NexBPMCode
          ,a.spk_BPMCode
          ,a.spk_name
          ,a.spk_chinesevalue
          ,a.spk_englishvalue
	from #spk_financialapprovalruleconfig a
	where NOT EXISTS (SELECT * FROM dbo.spk_financialapprovalruleconfig where spk_BPMCode = a.spk_BPMCode)
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into dbo.spk_financialapprovalruleconfig from #spk_financialapprovalruleconfig
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END

