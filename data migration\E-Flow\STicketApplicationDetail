CREATE PROCEDURE dbo.sp_STicketApplicationDetail_ns
AS 
begin
	/*
 * 问题一：SubVerificationStatus 逻辑模糊，字段无法找到
 * 问题二：门店表模糊，无法定位
 * 问题三：SubVerifiedAmountRMB 如同问题一
 * 问题四：SettlementRegion逻辑是否正确，得到的结果不符合预期
 */

	
select 
a.Id
,a.RowId
,b1.id as HedgeDetailId
,c.id as ParentID
,a.SubVerificationStatus
,a.CutOffStatus
,a.SubStatus
,a.SettlementEntityId
,a.SettlementEntityName
,a.SettlementEntityCode
,a.SettlementEntityType
,a.SettlementEntityChannel
,a.SettlementEntityHQCode
,a.SettlementEntityHQName
,e.spk_NexBPMCode  as ExpenseNatureId
,e.spk_costnumber  as ExpenseNatureCode
,a.ExpenseNature
,a.Quantity
,a.Price
,a.SubTotalAmountRMB
,a.SubVerifiedAmountRMB
--,a.SettlementRegion
--,a.SettlementPeriodStart
--,a.SettlementPeriodEnd
--,f.spk_NexBPMCode  as CityId
--,a.CityCode
--,a.CityName
,a.PredictDate
,a.Remark
,a.CreationTime
,a.CreatorId
,a.LastModificationTime
,a.LastModifierId
,a.IsDeleted
,a.DeleterId
,a.DeletionTime
into #STicketApplicationDetail
from STicketApplicationDetail_tmp a--263232
left join STicketApplicationDetail_tmp b1
on a.ProcInstId=b1.ProcInstId and a.RowId=b1.HedgeDetailId--263232
left join (select *,ROW_NUMBER () over(PARTITION by ApplicationCode order by ProcInstId desc ) rn  from STicketApplications_tmp) c
on a.ParentID=c.ApplicationCode  and c.rn=1
left join spk_costnature e
on a.ExpenseNatureId=e.spk_BPMCode 
--left join (select *,ROW_NUMBER () over(PARTITION by spk_citynumber order by spk_Name desc ) rn from spk_citymasterdata) f
--on a.CityId=f.spk_citynumber  and f.rn=1

--drop table #STicketApplicationDetail


 IF OBJECT_ID(N'dbo.STicketApplicationDetail ', N'U') IS NOT NULL
	BEGIN
		drop table dbo.STicketApplicationDetail
		select *
        into dbo.STicketApplicationDetail from #STicketApplicationDetail
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into dbo.STicketApplicationDetail from #STicketApplicationDetail
    -- select * from #vendor_tbl
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END




end

