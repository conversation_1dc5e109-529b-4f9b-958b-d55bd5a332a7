﻿using Abbott.SpeakerPortal.Contracts.Common;

using Microsoft.AspNetCore.Hosting;

using MigraDoc.DocumentObjectModel;
using MigraDoc.DocumentObjectModel.Tables;
using MigraDoc.Rendering;

using PdfSharp.Fonts;
using PdfSharp.Pdf;
using PdfSharp.Pdf.IO;

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using static Abbott.SpeakerPortal.AppServices.Common.ChineseFontResolver;


namespace Abbott.SpeakerPortal.AppServices.Common
{
    public class PDFService(IWebHostEnvironment env) : SpeakerPortalAppService, IPDFService
    {
        private readonly IWebHostEnvironment _env = env;
        /// <summary>
        /// 生成po的pdf
        /// </summary>
        /// <param name="dict"></param>
        /// <param name="lsitDict"></param>
        /// <returns></returns>
        public Task<MemoryStream> GeneratePoPdfAsync(Dictionary<string, string> dict, List<Dictionary<string, string>> lsitDict)
        {
            string wwwrootPath = _env.WebRootPath;
            var headerPath = Path.Combine(wwwrootPath, "images/logo/Abbott/header.jpg");

            GlobalFontSettings.FontResolver = new ChineseFontResolver(_env);
            var document = new Document();
            var _style = document.Styles["Normal"];//整体样式
            _style!.Font.Name = FamilyNames.NotoSerifSCLight;
            _style.Font.Size = 6.5;
            _style.ParagraphFormat.LineSpacingRule = LineSpacingRule.Exactly;

            var fontEnStyle = document.Styles.AddStyle("FontEnStyle", "Normal");//整体样式
            fontEnStyle!.Font.Name = FamilyNames.CALIBRI;

            var _tableStyle = document.Styles.AddStyle("Table", "Normal");//表格样式
            _tableStyle.Font.Name = _style.Font.Name;
            _tableStyle.Font.Size = _style.Font.Size;

            var _section = document.AddSection();
            _section.PageSetup = document.DefaultPageSetup.Clone();
            _section.PageSetup.PageFormat = PageFormat.Legal; //纸章规格
            //_section.PageSetup.Orientation = Orientation.Landscape;//纸张方向：横向，默认是竖向
            _section.PageSetup.TopMargin = Unit.FromMillimeter(12.7);//上边距 50
            _section.PageSetup.LeftMargin = Unit.FromMillimeter(12.7);//左边距 20
            _section.PageSetup.RightMargin = Unit.FromMillimeter(12.7);//左边距 20
            _section.PageSetup.BottomMargin = Unit.FromMillimeter(12.7);//左边距 20
            _section.PageSetup.PageWidth = Unit.FromMillimeter(215.9);
            _section.PageSetup.PageHeight = Unit.FromMillimeter(279.4);
            //小四字体英文
            MigraDoc.DocumentObjectModel.Font font12 = new MigraDoc.DocumentObjectModel.Font
            {
                Size = 12,
                Bold = true,
                Underline = Underline.Single,
                Name = FamilyNames.CALIBRI
            };
            //小六字体英文
            var font6 = new MigraDoc.DocumentObjectModel.Font
            {
                Size = 6.5,
                Name = FamilyNames.CALIBRI
            };
            var borders = new Borders
            {
                Color = Colors.Black,
            };
            //创建一个表格，并且设置边距
            var headerImage = _section.AddImage(headerPath);

            Paragraph paragraph = new();// 设置段落格式
            paragraph.Format.LineSpacing = Unit.FromMillimeter(2); // 设置空行高度为 12 磅 
            var tablewidth = _section.PageSetup.PageWidth - _section.PageSetup.LeftMargin * 2;
            var companyen = _section.AddParagraph();
            companyen.Style = fontEnStyle.Name;
            companyen.Format.Font.Size = 12;
            companyen.Format.Font.Bold = true;
            companyen.Format.Alignment = ParagraphAlignment.Center;

            companyen.AddText($"{dict["InvoiceEnglishTitle"]} - Buyer");

            var companyCn = _section.AddParagraph();
            companyCn.Format.Font.Size = 12;
            companyCn.Format.Alignment = ParagraphAlignment.Center;
            companyCn.AddText($"{dict["InvoiceTitle"]} - 买方");//发票抬头
            companyCn.Format.LineSpacing = Unit.FromMillimeter(6); // 设置空行高度为 12 磅 
            Paragraph paragraph1 = new();// 设置段落格式
            paragraph1.Format.SpaceBefore = "2pt"; // 设置空行高度为 12 磅 
            document.LastSection.Add(paragraph1);

            var title = _section.AddParagraph("采购单");
            title.Format.Font.Size = 12;
            title.Format.Font.Bold = true;
            title.Format.Alignment = ParagraphAlignment.Center;
            title.Format.Font.Underline = Underline.Single;


            title.AddFormattedText(" PURCHASE ORDER", font12);
            document.LastSection.Add(paragraph.Clone()); // 将段落添加到文档中
            //表格内容
            var vendorTable = _section.AddTable();
            vendorTable.Style = _style.Name;
            vendorTable.Borders = borders.Clone();
            //vendorTable.Borders.Left.Width = 0.5;
            //vendorTable.Borders.Right.Width = 0.5;
            vendorTable.TopPadding = 6;
            vendorTable.BottomPadding = 0;


            var vendortableWidths = new double[3];
            double w1 = tablewidth.Point / 4;
            vendortableWidths[0] = w1;
            vendortableWidths[1] = w1;
            vendortableWidths[2] = w1 * 2d;
            //生成列
            foreach (var item in vendortableWidths)
            {
                var column = vendorTable.AddColumn();
                column.Width = item;
                column.Format.Alignment = ParagraphAlignment.Left;
            }

            //vendor
            List<(string columnCn1, string columnEn1, string columnValue, string columnCn2, string columnEn2, string columnCn3, string columnEn3)> vendorData = new()
                {
                    new () {columnCn1="供应商名称",columnEn1=$" Vendor Name:",columnValue=dict["VendorName"],columnCn2="",columnCn3="定单号",columnEn3=$"  P/O#: {dict["PoNo"]}" },
                    new () {columnCn1="地址",columnEn1=$" Address:",columnValue=dict["Address1"],columnCn2="",columnCn3="请购单号 ",columnEn3=$"PRF#: {dict["PrNo"]}"},
                    new () {columnCn1="联系人",columnEn1=$" Attn:",columnValue=dict["Contact"],columnCn2="",columnCn3="",columnEn3=$"RCE No:{dict["RCENo"]}"},
                    new () {columnCn1="电话",columnEn1=$" Tel:", columnValue = dict["Tel"],columnCn2="传真",columnEn2=$" Fax:{dict["Fax"]}",columnCn3="日期",columnEn3=$" Date: {dict["Date"]}"},
                };
            foreach (var (columnCn1, columnEn1, columnValue, columnCn2, columnEn2, columnCn3, columnEn3) in vendorData)
            {
                var dataRow = vendorTable.AddRow();
                dataRow.TopPadding = 1;
                dataRow.BottomPadding = 1;
                if (string.IsNullOrEmpty(columnCn2))
                {
                    dataRow.Cells[0].AddParagraph(columnCn1).AddFormattedText(columnEn1, font6).AddFormattedText(columnValue, _style.Name);
                    dataRow.Cells[0].MergeRight = 1;
                    dataRow.Cells[2].AddParagraph(columnCn3).AddFormattedText(columnEn3, font6);
                    dataRow.Borders.Bottom.Clear();
                }
                else
                {
                    dataRow.Cells[0].AddParagraph(columnCn1).AddFormattedText(columnEn1, font6);
                    dataRow.Cells[0].Borders.Right.Color = Colors.White;
                    dataRow.Cells[1].AddParagraph(columnCn2).AddFormattedText(columnEn2, font6);
                    dataRow.Cells[2].AddParagraph(columnCn3).AddFormattedText(columnEn3, font6);
                }

            }
            //Invoice

            #region Invoice
            document.LastSection.Add(paragraph.Clone()); // 将段落添加到文档中
            //表格内容
            var InvoiceTable = _section.AddTable();
            InvoiceTable.Style = _style.Name;
            InvoiceTable.Borders = borders.Clone();
            InvoiceTable.TopPadding = 6;
            InvoiceTable.BottomPadding = 0;
            var InvoicetableWidths = new double[3];
            InvoicetableWidths[0] = w1 * 2;
            InvoicetableWidths[1] = w1;
            InvoicetableWidths[2] = w1;
            //生成列
            foreach (var item in InvoicetableWidths)
            {
                var column = InvoiceTable.AddColumn();
                column.Width = item;
                column.Format.Alignment = ParagraphAlignment.Left;
            }
            List<(string column1, string columnEn1, string column2, string columnEn2, string column3, string columnEn3)> vendornInvoiceData = new()
                {
                    new () {column1=$"发票请开: {dict["InvoiceTitle"]}",columnEn1="Invoice Receipt Addressed To",column2="",columnEn2="",column3=$"地址: {dict["Address2"]}",columnEn3="Address" },
                    new () {column1="",column2="传真" ,columnEn2=$"Fax:{dict["Fax"]}",column3="电话" ,columnEn3=$" Tel: {dict["Tel2"]}"},
                };
            foreach (var (column1, columnEn1, column2, columnEn2, column3, columnEn3) in vendornInvoiceData)
            {
                var dataRow = InvoiceTable.AddRow();
                dataRow.TopPadding = 1;
                dataRow.BottomPadding = 1;
                if (!string.IsNullOrEmpty(column1))
                {
                    dataRow.Cells[0].AddParagraph(column1);
                    dataRow.Cells[0].AddParagraph(columnEn1).Style = fontEnStyle.Name;
                    dataRow.Cells[1].AddParagraph(column3);
                    dataRow.Cells[1].AddParagraph(columnEn3).Style = fontEnStyle.Name;
                    dataRow.Cells[1].MergeRight = 1;
                    dataRow.Borders.Bottom.Clear();
                }
                else
                {
                    dataRow.Cells[0].AddParagraph("");
                    dataRow.Cells[1].AddParagraph(column2).AddFormattedText(columnEn2, font6);
                    dataRow.Cells[1].Borders.Right.Color = Colors.White;
                    dataRow.Cells[2].AddParagraph(column3).AddFormattedText(columnEn3, font6);
                }

            }
            #endregion
            //空白 段落 分隔下间距

            document.LastSection.Add(paragraph); // 将段落添加到文档中

            //prdetail


            #region prdetail
            //表格内容
            double w3 = tablewidth.Point / 9;
            var PrTable = _section.AddTable();
            PrTable.Style = _style.Name;
            PrTable.Borders = borders.Clone();
            PrTable.TopPadding = 6;
            PrTable.BottomPadding = 0;
            var prtableWidths = new double[6];
            prtableWidths[0] = w3;
            prtableWidths[1] = w3;
            prtableWidths[2] = w3 * 3;
            prtableWidths[3] = w3;
            prtableWidths[4] = w3;
            prtableWidths[5] = w3 * 2;
            //生成列
            foreach (var item in prtableWidths)
            {
                var column = PrTable.AddColumn();
                column.Width = item;
                column.Format.Alignment = ParagraphAlignment.Center;
            }
            List<(string columnCN, string columnEN)> prData = new()
                {
                    new () {columnCN=$"特别加注事项:{dict["Special"]}",columnEN="Special Instructions"},
                    new () {columnCN=$"货运方式:{dict["ShippingMode"]}",columnEN="Shipping Mode"},
                    new () {columnCN=$"送货地点:{dict["DeliveryTo"]}",columnEN="Delivery To"},
                    //new () {columnCN=$"发票收件人:{dict["PrApplyUser"]}",columnEN="Invoice To"},
                    new () {columnCN=$"付款条件:{dict["PaymentCondition"]}",columnEN="Payment Terms"},
                    new () {columnCN=$"交货日期:{dict["DeliveryDate"]}",columnEN="Delivery Date"},
                    new () {columnCN=$"质量标准/保质期:{dict["Quality"]}",columnEN="Quality Standard/Warranty Period"},
                    new () {columnCN=$"其它: {dict["Others"]}",columnEN="(Others)"},
                };
            //title
            var titleRow = PrTable.AddRow();
            titleRow.TopPadding = 3;
            titleRow.BottomPadding = 3;
            titleRow.VerticalAlignment = VerticalAlignment.Center;

            titleRow.Cells[0].AddParagraph("项目");
            titleRow.Cells[0].AddParagraph().AddFormattedText("Item", font6);
            titleRow.Cells[1].AddParagraph("数量");
            titleRow.Cells[1].AddParagraph().AddFormattedText("Qty", font6);
            titleRow.Cells[2].AddParagraph("品名");
            titleRow.Cells[2].AddParagraph().AddFormattedText("Description / Specification", font6);
            titleRow.Cells[3].AddParagraph("单位");
            titleRow.Cells[3].AddParagraph().AddFormattedText("UOM", font6);
            titleRow.Cells[4].AddParagraph("单价");
            titleRow.Cells[4].AddParagraph().AddFormattedText("Unit Price", font6);
            titleRow.Cells[5].AddParagraph("总价");
            titleRow.Cells[5].AddParagraph().AddFormattedText("Amount", font6);
            //List<int> two = new List<int>() { 1, 2 };
            int itemRow = 1;
            foreach (var item in lsitDict)
            {
                var titleContentRow = PrTable.AddRow();
                titleContentRow.TopPadding = 3;
                titleContentRow.BottomPadding = 3;
                titleContentRow.VerticalAlignment = VerticalAlignment.Center;
                titleContentRow.Cells[0].AddParagraph($"{itemRow}");
                titleContentRow.Cells[1].AddParagraph(item["Number"]);

                var cell2 = titleContentRow.Cells[2];
                Paragraph pgcell2 = cell2.AddParagraph();
                (item["Description"] ?? "").ToList()?.ForEach(o => pgcell2.AddChar(o));//自动换行 使用AddChar
                pgcell2.Format.Alignment = ParagraphAlignment.Left;

                titleContentRow.Cells[3].AddParagraph(item["Uom"]);
                titleContentRow.Cells[4].AddParagraph(item["Price"]);
                titleContentRow.Cells[5].AddParagraph(item["Amount"]);
                itemRow++;
            }
            List<(string columnCN, string columnEN, string columnContent)> prDatat = new()
                {
                    new () {columnCN="货币",columnEN="Currency:",columnContent=$"{dict["Currency"]}"},
                    new () {columnCN="运费及手续费",columnEN="Freight & Handling:",columnContent=$"{dict["Freight"]}"},
                    new () {columnCN="总额",columnEN="Total:",columnContent=$"{dict["Total"]}"},
                };
            int j = 1;
            foreach (var item in prDatat)
            {
                var titleContentRow = PrTable.AddRow();
                titleContentRow.TopPadding = 3;
                titleContentRow.BottomPadding = 3;
                titleContentRow.Format.Alignment = ParagraphAlignment.Right;
                titleContentRow.VerticalAlignment = VerticalAlignment.Center;
                titleContentRow.Cells[0].AddParagraph(item.columnCN).AddFormattedText(item.columnEN, font6);
                titleContentRow.Cells[0].MergeRight = 4;
                if (j != 3)
                    titleContentRow.Cells[0].Borders.Bottom.Clear();
                titleContentRow.Cells[5].AddParagraph(item.columnContent).Format.Alignment = ParagraphAlignment.Center;
                titleContentRow.Cells[5].VerticalAlignment = VerticalAlignment.Center;
                j++;
            }
            foreach (var (columnCN, columnEN) in prData)
            {
                var dataRow = PrTable.AddRow();
                dataRow.Format.Alignment = ParagraphAlignment.Left;
                dataRow.TopPadding = 3;
                dataRow.BottomPadding = 3;
                dataRow.Cells[0].AddParagraph(columnCN);
                dataRow.Cells[0].AddParagraph(columnEN).Style = fontEnStyle.Name;
                dataRow.Cells[0].MergeRight = 5;
            }
            #endregion

            //var stylelist = document.AddStyle("Bulletlist", "Normal");
            //stylelist.ParagraphFormat.RightIndent = 12;
            //stylelist.ParagraphFormat.TabStops.ClearAll();
            //stylelist.ParagraphFormat.TabStops.AddTabStop(Unit.FromCentimeter(0), TabAlignment.Left);
            //stylelist.ParagraphFormat.LeftIndent = "1cm";
            //stylelist.ParagraphFormat.FirstLineIndent = "-0.5cm";
            //stylelist.ParagraphFormat.SpaceBefore = 0;
            //stylelist.ParagraphFormat.SpaceAfter = 0;
            //stylelist.ParagraphFormat.ListInfo = new ListInfo
            //{
            //    ContinuePreviousList = true,
            //    ListType = ListType.NumberList1,
            //    NumberPosition = Unit.FromCentimeter(0),
            //};

            #region Notice to Vendor
            document.LastSection.Add(paragraph.Clone()); // 将段落添加到文档中
            var notice = _section.AddParagraph("卖方注意事项");
            notice.Format.Font.Bold = true;
            var noticeen = notice.AddFormattedText("Notice to Vendor", font6);

            document.LastSection.Add(paragraph.Clone()); // 将段落添加到文档中

            var text1 = "1.供应商须盖章并且回复一份采购订单的复印件作为对此采购单内容的接受确认。如供应商在收到订单后3个工作日内未提出异议的，视为接受确认此订单的内容和条款。本订单经买方发出，供应商接受确认之后即生效。";
            var Paragraph1 = _section.AddParagraph();
            text1?.ToList()?.ForEach(o => Paragraph1.AddChar(o));//自动换行 使用AddChar
            //Paragraph1.Style = "Bulletlist";
            Paragraph1.AddFormattedText("\nThe Vendor must stamp and send a return copy of this purchase order as an acknowledgement of acceptance. If the Vendor does not raise any objection within 3 working days upon receiving the purchase order, it will be deemed as acknowledgement of acceptance of the contents and terms of this purchase order. This purchase order shall become effective upon issuance by the Buyer and acceptance by the Vendor.", font6);
            document.LastSection.Add(paragraph.Clone()); // 将段落添加到文档中

            var Paragraph2 = _section.AddParagraph("2.采购订单号必须在所有发票、提单和来往信函中标明。");
            Paragraph2.AddFormattedText("\nThis purchase order number must appear on all invoices, bills of lading, and correspondence.", font6);
            document.LastSection.Add(paragraph.Clone()); // 将段落添加到文档中

            var Paragraph3 = _section.AddParagraph("3.卖方全部理解和接受本采购单所有内容（包括所附的条款和条件）及其附件。根据卖方的要求，买方已经就上述所有内容做出合理的解释，卖方已经同意该解释。");
            Paragraph3.AddFormattedText("\nAll the provision contained herein (including terms & conditions attached) and Appendixes attached hereto have been fully understood and accepted by Vendor, which are equally binding on both Parties. As per the request of Vendor, Buyer has done its duty to provide the reasonably explanations to all the terms and conditions contained herein, which has been accepted by Vendor.", font6);
            document.LastSection.Add(paragraph.Clone()); // 将段落添加到文档中

            var confirm = _section.AddParagraph("Please confirm order by signature and return!");
            confirm.Format.Font.Bold = true;
            confirm.Style = fontEnStyle.Name;
            document.LastSection.Add(paragraph.Clone()); // 将段落添加到文档中
            #endregion

            var tipsTable = _section.AddTable();
            tipsTable.Style = fontEnStyle.Name;
            tipsTable.TopPadding = 3;
            var tipsTableColumn = tipsTable.AddColumn();
            var tipsTableColumn2 = tipsTable.AddColumn();

            tipsTableColumn.Width = tipsTableColumn2.Width = tablewidth * 0.5;
            var Row = tipsTable.AddRow();
            var tipsParagraph1 = Row[0].AddParagraph();
            var tipsParagraph2 = Row[1].AddParagraph();
            tipsParagraph1.Format.Font.Bold = true;
            tipsParagraph1.AddText($"Buyer Approval Date:");

            tipsParagraph2.Format.Font.Bold = true;
            tipsParagraph2.AddText($" Vendor Signature & Date:");

            var approval = _section.AddTable();
            approval.Style = _style.Name;
            approval.TopPadding = 6;
            approval.BottomPadding = 0;

            double w4 = tablewidth.Point / 8;
            var approvalWidths = new double[5];
            approvalWidths[0] = w4 * 1.2f;
            approvalWidths[1] = w4 * 2;
            approvalWidths[2] = w4 * 0.8f;
            approvalWidths[3] = w4 * 3;
            approvalWidths[4] = w4 * 1;
            foreach (var item in approvalWidths)
            {
                var column = approval.AddColumn();
                column.Width = item;
                column.Format.Alignment = ParagraphAlignment.Left;
            }
            List<(string column1, string columnValue, string column2, string columnEn2)> appData = new()
                {
                    new () {column1="Approved by level 5:",column2="",columnValue=dict["Approved5"] },
                    new () {column1="Approved by level 4:",column2="签名/日期",columnEn2=" Signature/Date",columnValue=dict["Approved4"]},
                    new () {column1="Approved by level 3",column2="",columnValue=dict["Approved3"]},
                    new () {column1="Approved by level 2:",column2="",columnValue=dict["Approved2"]},
                    new () {column1="Approved by level 1:",column2="",columnValue=dict["Approved1"]},
                };
            foreach (var (column1, columnValue, column2, columnEn2) in appData)
            {
                var row = approval.AddRow();
                row.TopPadding = 3;
                row.BottomPadding = 3;
                row.VerticalAlignment = VerticalAlignment.Center;
                row.Cells[0].AddParagraph(column1).Style = fontEnStyle.Name;
                row.Cells[1].AddParagraph(columnValue).Format.Borders.Bottom.Width = 0.5;
                if (!string.IsNullOrEmpty(column2))
                {
                    var sign = row.Cells[3].AddParagraph(column2);
                    sign.AddFormattedText(columnEn2, font6);
                    sign.Format.Borders.Top.Width = 0.5;

                }

            }
            #region 页码
            //_section.PageSetup.DifferentFirstPageHeaderFooter = false;
            //var pager = _section.Footers.Primary.AddParagraph();
            //pager.AddPageField();
            //pager.Format.Alignment = ParagraphAlignment.Center;
            #endregion

            //生成PDF
            var pdfRenderer = new PdfDocumentRenderer();
            var memoryStream = new MemoryStream();
            pdfRenderer.Document = document;
            pdfRenderer.RenderDocument();
            pdfRenderer.PdfDocument.Save(memoryStream);
            var pdfDocument = PdfReader.Open(memoryStream);
            var pdftempPath = Path.Combine(wwwrootPath, "Templates/POTemplateTK.pdf");
            PdfDocument template = PdfReader.Open(pdftempPath, PdfDocumentOpenMode.Import);
            foreach (var item in template.Pages)
            {
                pdfDocument.AddPage(item);
            }

            pdfDocument.Save(memoryStream);
            //var outputPdfFilePath = "C:\\Users\\<USER>\\Desktop\\test\\pdfdemo.pdf";
            //保存到本地
            //using var fs = new FileStream(outputPdfFilePath, FileMode.Create);
            //byte[] bytes = new byte[memoryStream.Length];
            //memoryStream.Seek(0, SeekOrigin.Begin);
            //memoryStream.Read(bytes, 0, (int)memoryStream.Length);
            //await Task.Run(() =>
            //   {
            //       fs.Write(bytes, 0, bytes.Length);
            //   });
            return Task.Run(() => memoryStream);
        }
    }
}
