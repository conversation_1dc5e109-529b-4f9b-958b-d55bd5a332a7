﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Text;
using MiniExcelLibs.Attributes;
using System.Text.Json.Serialization;

namespace Abbott.SpeakerPortal.Contracts.Budget
{
    public class GetBudgetListResponseDto
    {
        /// <summary>
        /// 预算Id
        /// </summary>
        [ExcelIgnore]
        public Guid Id { get; set; }
        /// <summary>
        /// 预算编号
        /// </summary>
        [ExcelColumnName("预算编号")]
        public string Code { get; set; }
        /// <summary>
        /// bu
        /// </summary>
        [ExcelIgnore]
        public Guid BuId { get; set; }
        /// <summary>
        /// BU名称
        /// </summary>
        [ExcelColumnName("Bu")]
        public string BuName { get; set; }
        /// <summary>
        /// 描述
        /// </summary>
        [ExcelColumnName("描述")]
        public string Description { get; set; }
        /// <summary>
        /// 负责人Id
        /// </summary>
        [ExcelIgnore]
        public Guid OwnerId { get; set; }
        /// <summary>
        /// 负责人姓名
        /// </summary>
        [ExcelColumnName("负责人")]
        public string OwnerName { get; set; }
        /// <summary>
        /// 负责人邮箱
        /// </summary>
        [ExcelColumnName("负责人邮箱")]
        public string OwnerEmail { get; set; }
        /// <summary>
        /// 固定资产
        /// </summary>
        [ExcelIgnore]
        public bool Capital { get; set; }
        /// <summary>
        /// 固定资产
        /// </summary>
        [ExcelColumnName("固定资产")]
        [JsonIgnore]
        public string CapitalText => Capital ? "是" : "否";
        /// <summary>
        /// 主预算金额
        /// </summary>
        [ExcelColumnName("主预算金额")]
        [ExcelIgnore]
        public decimal BudgetAmount { get; set; }
        /// <summary>
        /// 主预算金额
        /// </summary>
        [ExcelColumnName("主预算金额")]
        [JsonIgnore]
        public string BudgetAmountText => BudgetAmount.ToString("N2");
        /// <summary>
        /// 子预算金额
        /// </summary>

        [ExcelColumnName("子预算金额")]
        [ExcelIgnore]
        public decimal SubBudgetAmount { get; set; }
        /// <summary>
        /// 子预算金额
        /// </summary>

        [ExcelColumnName("子预算金额")]
        [JsonIgnore]
        public string SubBudgetAmountText => SubBudgetAmount.ToString("N2");
        /// <summary>
        /// 可拨金额
        /// </summary>
        [ExcelColumnName("可拨金额")]
        [ExcelIgnore]
        public decimal AdjustableAmount { get; set; }
        /// <summary>
        /// 可拨金额
        /// </summary>
        [ExcelColumnName("可拨金额")]
        [JsonIgnore]
        public string AdjustableAmountText => AdjustableAmount.ToString("N2");
        /// <summary>
        /// 状态
        /// </summary>
        [ExcelIgnore]
        public bool Status { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        [ExcelColumnName("状态")]
        [JsonIgnore]
        public string StatusText => this.Status ? "启用" : "冻结";
        /// <summary>
        /// 年度
        /// </summary>
        [ExcelIgnore]
        public int? Year { get; set; }
        /// <summary>
        /// 是否可以删除
        /// </summary>
        [ExcelIgnore]
        public bool isDelete { get; set; }
    }

    /// <summary>
    /// 导出主预算
    /// </summary>
    public class ExportBudgetListDto
    {
        /// <summary>
        /// 预算编号
        /// </summary>
        [ExcelColumnName("预算编号")]
        public string Code { get; set; }

        /// <summary>
        /// BU名称
        /// </summary>
        [ExcelColumnName("Bu")]
        public string BuName { get; set; }
        /// <summary>
        /// 描述
        /// </summary>
        [ExcelColumnName("描述")]
        public string Description { get; set; }

        /// <summary>
        /// 负责人姓名
        /// </summary>
        [ExcelColumnName("负责人")]
        public string OwnerName { get; set; }
        /// <summary>
        /// 负责人邮箱
        /// </summary>
        [ExcelColumnName("负责人邮箱")]
        public string OwnerEmail { get; set; }
        /// <summary>
        /// 固定资产
        /// </summary>
        [ExcelIgnore]
        public bool Capital { get; set; }
        /// <summary>
        /// 固定资产
        /// </summary>
        [ExcelColumnName("固定资产")]
        [JsonIgnore]
        public string CapitalText => Capital ? "是" : "否";
        /// <summary>
        /// 主预算金额
        /// </summary>
        [ExcelColumnName("主预算金额")]
        public decimal BudgetAmount { get; set; }
        /// <summary>
        /// 子预算金额
        /// </summary>

        [ExcelColumnName("子预算金额")]
        public decimal SubBudgetAmount { get; set; }

        /// <summary>
        /// 可拨金额
        /// </summary>
        [ExcelColumnName("可拨金额")]
        public decimal AdjustableAmount { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        [ExcelIgnore]
        public bool Status { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        [ExcelColumnName("状态")]
        [JsonIgnore]
        public string StatusText => this.Status ? "启用" : "冻结";
    }
}
