trigger:
 batch: true
 branches:
   include:
     - sit

pool:
  name: 'Building'
     
steps:
- task: DockerCompose@0
  inputs:
    containerregistrytype: 'Azure Container Registry'
    azureSubscription: 'cd-serviceconnection-s'
    azureContainerRegistry: '{"loginServer":"abbottchina.azurecr.cn", "id" : "/subscriptions/615f3649-0ae2-449b-85fd-64d86db733dd/resourceGroups/RG-ABTCN-BTS-ACR/providers/Microsoft.ContainerRegistry/registries/abbottChina"}'
    dockerComposeFile: '**/docker-compose.yml'
    action: 'Build services'
    includeSourceTags: true
    includeLatestTag: true
    additionalImageTags: 'v$(Build.BuildNumber).$(Build.BuildId)'
    dockerComposeFileArgs: 'env=stag'
- task: DockerCompose@0
  inputs:
    containerregistrytype: 'Azure Container Registry'
    azureSubscription: 'cd-serviceconnection-s'
    azureContainerRegistry: '{"loginServer":"abbottchina.azurecr.cn", "id" : "/subscriptions/615f3649-0ae2-449b-85fd-64d86db733dd/resourceGroups/RG-ABTCN-BTS-ACR/providers/Microsoft.ContainerRegistry/registries/abbottChina"}'
    dockerComposeFile: '**/docker-compose.yml'
    action: 'Push services'
    includeSourceTags: true
    includeLatestTag: true
    additionalImageTags: 'v$(Build.BuildNumber).$(Build.BuildId)'
    dockerComposeFileArgs: 'env=stag'

- task: CopyFiles@2
  inputs:
    SourceFolder: '$(Build.SourcesDirectory)/cicd'
    Contents: '**'
    TargetFolder: '$(Build.ArtifactStagingDirectory)'
    CleanTargetFolder: true
    OverWrite: true
- task: PublishBuildArtifacts@1
  inputs:
    PathtoPublish: '$(Build.ArtifactStagingDirectory)'
    ArtifactName: 'drop'
    publishLocation: 'Container'
- task: Bash@3
  inputs:
    targetType: 'inline'
    script: |
      # Write your commands here   
      docker system prune --force
  condition: always()