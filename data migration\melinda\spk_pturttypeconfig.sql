select newid() as spk_NexBPMCode,*
into #spk_pturttypeconfig
from spk_pturttypeconfig_tmp

IF OBJECT_ID(N'dbo.spk_pturttypeconfig', N'U') IS NOT NULL
BEGIN
	update a 
	set a.spk_BPMCode       = b.spk_BPMCode
       ,a.spk_code          = b.spk_code
       ,a.spk_Name          = b.spk_Name
       ,a.spk_pturtTypeName = b.spk_pturtTypeName
       ,a.spk_pturttype     = b.spk_pturttype
       ,a.flg               = b.flg
    from spk_pturttypeconfig a
    join #spk_pturttypeconfig b on a.spk_BPMCode = b.spk_BPMCode
    
    insert into spk_pturttypeconfig
    select a.spk_NexBPMCode
           ,a.spk_BPMCode
           ,a.spk_code
           ,a.spk_Name
           ,a.spk_pturtTypeName
           ,a.spk_pturttype
           ,a.flg
    from #spk_pturttypeconfig a
    where not exists (select * from spk_pturttypeconfig where a.spk_BPMCode = spk_BPMCode)
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into dbo.spk_pturttypeconfig from #spk_pturttypeconfig
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
