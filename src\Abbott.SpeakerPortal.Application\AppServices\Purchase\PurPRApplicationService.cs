using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.Approve;
using Abbott.SpeakerPortal.Contracts.Common.PowerApp;
using Abbott.SpeakerPortal.Contracts.Dataverse;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Contracts.Vendor.NonSpeaker;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.OECPSASpeakerLimits;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Entities.VendorFinancials;
using Abbott.SpeakerPortal.Entities.VendorOrgnizations;
using Abbott.SpeakerPortal.Entities.VendorPersonals;
using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Utils;
using Abbott.SpeakerPortal.Extension;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Internal;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using static Abbott.SpeakerPortal.Enums.Purchase;
using static Abbott.SpeakerPortal.Consts.EntitiesNameConsts;
using Abbott.SpeakerPortal.Entities.OECPSAComPSALimits;
using Newtonsoft.Json;
using Abbott.SpeakerPortal.Entities.Common.Attachment;
using Abbott.SpeakerPortal.Contracts.Common.Attachment;
using Microsoft.AspNetCore.Mvc.ApplicationModels;
using Microsoft.Extensions.Configuration;
using Abbott.SpeakerPortal.Consent;
using Abbott.SpeakerPortal.Entities.Common.Consent;
using static Abbott.SpeakerPortal.Permissions.SpeakerPortalPermissions;
using Abbott.SpeakerPortal.Entities.Budget;
using Abbott.SpeakerPortal.Entities.Integration.Dspot;
using Abbott.SpeakerPortal.Contracts.Budget.Subbudget;
using System.Globalization;
using Abbott.SpeakerPortal.Entities.OECPSASpeakerLimitUseHistorys;
using Abbott.SpeakerPortal.Entities.OECPSASpeakerLimitExtras;
using Abbott.SpeakerPortal.Entities.OECSpeakerLevelDetails;
using Abbott.SpeakerPortal.Entities.OECPSAComExpenseTypes;
using System.Linq.Expressions;
using Abbott.SpeakerPortal.OEC.SpeakerLimit;
using Abbott.SpeakerPortal.Contracts.OEC.SpeakerLimit;
using DocumentFormat.OpenXml.Drawing;
using DocumentFormat.OpenXml.Office.CustomUI;
using Volo.Abp.Settings;
using Abbott.SpeakerPortal.Entities.Purchase.PurPOApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurGRApplication;
using Volo.Abp.Uow;
using Abbott.SpeakerPortal.Entities.OECPayStandards;
using Abbott.SpeakerPortal.Entities.OECPayStandardConfigs;
using Abbott.SpeakerPortal.Entities.OECPayStandardConfigDetails;
using Abbott.SpeakerPortal.Contracts.OEC.PayStandard;
using Abbott.SpeakerPortal.Entities.OECSpeakerAuthApplys;
using Abbott.SpeakerPortal.Entities.OEC.OECSpeakerAuthApplys;
using Abbott.SpeakerPortal.Entities.Purchase.PurBWApplication;
using System.Text.RegularExpressions;
using DocumentFormat.OpenXml.VariantTypes;
using Abbott.SpeakerPortal.Purchase.PurExemptApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurBDApplication;
using static System.Runtime.InteropServices.JavaScript.JSType;
using Abbott.SpeakerPortal.Entities.Budget.BdExpense;
using System.Linq.Dynamic.Core;
using Abbott.SpeakerPortal.Contracts.Purchase.PurGRApplication;
using MiniExcelLibs.OpenXml;
using MiniExcelLibs;
using Abbott.SpeakerPortal.Entities.Common.Workflow.WorkflowTasks;
using Abbott.SpeakerPortal.Person;
using DocumentFormat.OpenXml.Presentation;
using Abbott.SpeakerPortal.Entities.Integration.EpdOnlineMeeting;
using Abbott.SpeakerPortal.Contracts.Approval;
using DocumentFormat.OpenXml.Wordprocessing;
using Abbott.SpeakerPortal.Entities.Common.Workflow.CurrentApprovalTask;
using ClosedXML.Excel;
using Abbott.SpeakerPortal.IntermediateDB.Repositories;
using Microsoft.Extensions.DependencyInjection;
using Abbott.SpeakerPortal.IntermediateDB.Models;
using static PdfSharp.Capabilities;
using DocumentFormat.OpenXml.Office2010.Excel;
using Abbott.SpeakerPortal.Entities.Report;
using Abbott.SpeakerPortal.Contracts.Report;
using Senparc.CO2NET.Extensions;
using Microsoft.Extensions.Logging;

namespace Abbott.SpeakerPortal.AppServices.Purchase
{
    public partial class PurPRApplicationService : SpeakerPortalAppService, IPurPRApplicationService
    {
        /// <summary>
        /// 获取采购申请列表
        /// </summary>
        /// <param name="request"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetPRApplicationListResponse>> GetPRApplicationsAsync(GetPRApplicationListRequest request, PurPRApplicationStatus? status = null)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var queryPr = await LazyServiceProvider.LazyGetService<IPurPRApplicationReadonlyRepository>().GetQueryableAsync();
            var queryPrDetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailReadonlyRepository>().GetQueryableAsync();
            var queryP = queryPr.Select(a => new
            {
                a.Id,
                a.Status,
                a.ApplyUserId,
                a.AgentId,
                a.TransfereeId,
                a.ApplicationCode,
                a.ApplyUserBu,
                a.CostCenter,
                a.ApplyUserIdName,
                a.ApplyUserDeptName,
                a.BudgetCode,
                a.SubBudgetCode,
                a.CompanyId,
                a.ApplyUserDept,
                a.SubBudgetId,
                a.TotalAmount,
                a.ApplyTime,
                a.LastModificationTime,
                a.CreationTime,
                a.MeetingStatus,
            })
            .WhereIf(status.HasValue, a => a.Status == status)//区分是否为草稿箱
            .WhereIf(!status.HasValue, a => a.Status != PurPRApplicationStatus.Draft)
            .WhereIf(CurrentUser.Id.HasValue && status.HasValue && status == PurPRApplicationStatus.Draft, a => a.ApplyUserId == CurrentUser.Id)
            .WhereIf(request.ApplicatId.HasValue, a => a.ApplyUserId == request.ApplicatId)
            .WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), a => a.ApplicationCode.Contains(request.ApplicationCode))
            .WhereIf(request.Bu.HasValue, a => a.ApplyUserBu == request.Bu)
            .WhereIf(request.Costcenter.HasValue, a => a.CostCenter == request.Costcenter)
            .WhereIf(!string.IsNullOrEmpty(request.Applicant), a => a.ApplyUserIdName.Contains(request.Applicant))
            .WhereIf(!string.IsNullOrEmpty(request.ApplicationDept), a => a.ApplyUserDeptName.Contains(request.ApplicationDept))
            .WhereIf(request.Status != null, a => request.Status.Contains(a.Status))
            .WhereIf(!string.IsNullOrEmpty(request.BudgetCode), a => a.BudgetCode.Contains(request.BudgetCode))
            .WhereIf(!string.IsNullOrEmpty(request.SubBudgetCode), a => a.SubBudgetCode.Contains(request.SubBudgetCode))
            .WhereIf(request.Company.HasValue, a => a.CompanyId == request.Company);

            if (!status.HasValue)
            {
                var personCenterService = LazyServiceProvider.LazyGetService<IPersonCenterService>();
                var rolesIds = await personCenterService.GetIdsByRoleLevels(bizTypeCategory: ResignationTransfer.TaskFormCategory.PurchaseRequestApplication);
                queryP = queryP.ApplyRoleFilters(rolesIds, CurrentUser.Id.Value, x => x.ApplyUserBu, x => x.ApplyUserDept, x => x.SubBudgetId, x => x.ApplyUserId, x => x.TransfereeId, x => x.AgentId);
            }

            var query = queryP.Select(a => new
            {
                a.Id,
                a.ApplicationCode,
                a.ApplyUserId,
                a.ApplyUserDeptName,
                a.CostCenter,
                a.SubBudgetCode,
                a.CompanyId,
                a.TotalAmount,
                Date = status == null ? a.ApplyTime : a.LastModificationTime.HasValue ? a.LastModificationTime : a.CreationTime,
                a.Status,
                a.MeetingStatus,
                a.ApplyUserIdName,
            })
            .WhereIf(request.StartDate.HasValue, a => a.Date >= request.StartDate)
            .WhereIf(request.EndDate.HasValue, a => a.Date < request.EndDate.Value.Date.AddDays(1))
            .WhereIf(request.StartEstimateDate.HasValue && request.EndEstimateDate.HasValue, a => queryPrDetail.Any(a1 => a1.EstimateDate >= request.StartEstimateDate && a1.EstimateDate < request.EndEstimateDate.Value.Date.AddDays(1) && a1.PRApplicationId == a.Id));

            var getCostCenterTask = dataverseService.GetCostcentersAsync(stateCode: null);
            var getCompanyTask = dataverseService.GetCompanyList(stateCode: null);
            Task.WaitAll(getCostCenterTask, getCompanyTask);

            var allStatus = EnumUtil.GetEnumIdValues<PurPRApplicationStatus>();

            var count = query.Count();
            var queryData = query.OrderByDescending(a => a.Date).Skip(request.PageIndex * request.PageSize)
                .Take(request.PageSize).ToArray();

            var datas = queryData
                .Select(a => new GetPRApplicationListResponse
                {
                    Id = a.Id,
                    ApplicationCode = a.ApplicationCode,
                    Applicant = a.ApplyUserIdName,
                    ApplicationDept = a.ApplyUserDeptName,
                    CostCenter = getCostCenterTask.Result.FirstOrDefault(a1 => a1.Id == a.CostCenter)?.Name,
                    BudgetCode = a.SubBudgetCode,
                    Company = getCompanyTask.Result.FirstOrDefault(a1 => a1.Id == a.CompanyId)?.CompanyName,
                    TotalAmount = a.TotalAmount,
                    Date = a.Date?.ToString("yyyy-MM-dd HH:mm:ss"),
                    Status = a.Status,
                    StatusName = allStatus.FirstOrDefault(a1 => a1.Key == (int)a.Status)?.Value,
                    MeetingStatusName = GetStatusDescription(a.MeetingStatus),
                }).ToArray();

            var result = new PagedResultDto<GetPRApplicationListResponse>(count, datas);
            return result;
        }

        /// <summary>
        /// 导出采购申请列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<Stream> ExportPRApplicationAsync(GetPRApplicationListRequest request)
        {
            //await ExportPRApplication(request);
            //return null;
            var queryReport = await LazyServiceProvider.LazyGetService<IPurPRApplicationReportReadonlyRepository>().GetQueryableAsync();
            var personCenterService = LazyServiceProvider.LazyGetService<IPersonCenterService>();
            var roleLevel = personCenterService.GetMyRoleLevel();
            var guids = await personCenterService.GetIdsByRoles(roleLevel);
            switch (roleLevel)
            {
                case RoleLevel.Admin:
                    break;
                case RoleLevel.Manager:
                    if (guids.Count() > 1)
                        queryReport = queryReport.Where(x => guids.Contains(x.ApplyUserBu.Value) || x.ApplyUserId == CurrentUser.Id || x.AgentId == CurrentUser.Id);
                    else if (guids.Count() == 1)
                        queryReport = queryReport.Where(x => x.ApplyUserBu == guids.First() || x.ApplyUserId == CurrentUser.Id || x.AgentId == CurrentUser.Id);
                    break;
                case RoleLevel.Leader:
                    if (guids.Count() > 1)
                        queryReport = queryReport.Where(x => guids.Contains(x.ApplyUserDept) || x.ApplyUserId == CurrentUser.Id || x.AgentId == CurrentUser.Id);
                    else if (guids.Count() == 1)
                        queryReport = queryReport.Where(x => x.ApplyUserDept == guids.First() || x.ApplyUserId == CurrentUser.Id || x.AgentId == CurrentUser.Id);
                    break;
                case RoleLevel.Owner:
                    if (guids.Count() > 1)
                        queryReport = queryReport.Where(x => (x.SubBudgetId.HasValue && guids.Contains(x.SubBudgetId.Value)) || x.ApplyUserId == CurrentUser.Id || x.AgentId == CurrentUser.Id);
                    else if (guids.Count() == 1)
                        queryReport = queryReport.Where(x => x.SubBudgetId == guids.First() || x.ApplyUserId == CurrentUser.Id || x.AgentId == CurrentUser.Id);
                    break;
                case RoleLevel.Applicant:
                case RoleLevel.Unkonw:
                    queryReport = queryReport.WhereIf(CurrentUser.Id.HasValue, a => a.ApplyUserId == CurrentUser.Id || a.AgentId == CurrentUser.Id);
                    break;
                default:
                    break;
            }

            var query = queryReport
                .Where(a => a.Status != PurPRApplicationStatus.Draft)
                .WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), a => a.ApplicationCode.Contains(request.ApplicationCode))
                .WhereIf(request.Bu.HasValue, a => a.ApplyUserBu == request.Bu)
                .WhereIf(request.Costcenter.HasValue, a => a.CostCenter == request.Costcenter)
                .WhereIf(!string.IsNullOrEmpty(request.Applicant), a => a.ApplyUserIdName.Contains(request.Applicant))
                .WhereIf(!string.IsNullOrEmpty(request.ApplicationDept), a => a.ApplyUserDeptName.Contains(request.ApplicationDept))
                .WhereIf(request.Status != null, a => request.Status.Contains(a.Status))
                .WhereIf(!string.IsNullOrEmpty(request.BudgetCode), a => a.BudgetCode.Contains(request.BudgetCode))
                .WhereIf(!string.IsNullOrEmpty(request.SubBudgetCode), a => a.SubBudgetCode.Contains(request.SubBudgetCode))
                .WhereIf(request.Company.HasValue, a => a.CompanyId == request.Company)
                .WhereIf(request.StartDate.HasValue, a => a.ApplyTime >= request.StartDate)
                .WhereIf(request.EndDate.HasValue, a => a.ApplyTime < request.EndDate.Value.Date.AddDays(1))
                .WhereIf(request.StartEstimateDate.HasValue, a => a.EstimateDate >= request.StartEstimateDate)
                .WhereIf(request.EndEstimateDate.HasValue, a => a.EstimateDate < request.EndEstimateDate.Value.Date.AddDays(1))
                .Select(a => new ExportPRApplicationInfo()
                {
                    ApplicationCode = a.ApplicationCode,
                    ApplyTime = a.ApplyTime,
                    Applicant = a.ApplyUserIdName,
                    TotalAmountRMB = a.TotalAmountRMB,
                    Currency = a.Currency,
                    Company = a.CompanyIdName,
                    BU = a.ApplyUserBuName,
                    CostCenter = a.CostCenterName,
                    BudgetCode = a.BudgetCode,
                    BudgetDescription = a.BudgetDescription,
                    SubBudgetCode = a.SubBudgetCode,
                    SubBudgetDescription = a.SubBudgetDescription,
                    SubBudgetOwner = a.SubBudgetOwner,
                    PRStatus = a.Status.GetDescription(),
                    RowNo = a.RowNo.Value,
                    PayMethod = a.PayMethod,
                    CompanyCode = a.CompanyCode,
                    ApplyUserBu = a.ApplyUserBu.Value,
                    CostCenterCode = a.CostCenterCode,
                    CostNatureCode = a.CostNatureCode,
                    ProductCode = a.ProductCode,
                    CityId = a.CityId,
                    DetailTotalAmount = a.DetailTotalAmount,
                    DetailTotalAmountRMB = a.DetailTotalAmountRMB,
                    POVatRMB = a.POVatRMB,
                    POSavingRMB = a.POSavingRMB,
                    GRVarRMB = a.GRVarRMB,
                    PAVarRMB = a.PAVarRMB,
                    CostNature = a.CostNatureName,
                    City = a.CityIdName,
                    Product = a.ProductName,
                    Content = a.Content,
                    Vendor = a.VendorName,
                    RceNo = a.RceNo,
                    IcbAmount = a.IcbAmount,
                    CommitmentAmountRMB = a.CommitmentAmountRMB,
                    COA = a.COA,
                    CurrentApprovalNode = a.CurrentApprovalNode,
                    CurrentApprovalUser = a.CurrentApprovalUser,
                    EstimateDate = a.EstimateDate
                }).OrderByDescending(a => a.ApplyTime).ToList();

            MemoryStream stream = new();
            var config = new OpenXmlConfiguration()
            {
                TableStyles = TableStyles.None,
                AutoFilter = false

            };
            stream.SaveAs(query, true, "Sheet1", configuration: config);
            stream.Seek(0, SeekOrigin.Begin);
            return stream;
        }

        /// <summary>
        /// 获取用于选择的讲者列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetSpeakerListForChoiceResponseDto>> GetSpeakerForChoiceAsync(GetSpeakerListForChoiceRequestDto request)
        {
            var queryVendor = (await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync()).AsNoTracking();
            var queryFinancial = (await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync()).AsNoTracking();
            var queryComPsa = (await LazyServiceProvider.LazyGetService<IOECPSAComPSALimitRepository>().GetQueryableAsync()).AsNoTracking();
            var queryComExpenseType = (await LazyServiceProvider.LazyGetService<IOECPSAComExpenseTypeRepository>().GetQueryableAsync()).AsNoTracking();
            var queryPsaUseHistory = (await LazyServiceProvider.LazyGetService<IOECPSASpeakerLimitUseHistoryRepository>().GetQueryableAsync()).AsNoTracking();
            var queryBpcsAvm = (await LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync()).AsNoTracking();
            var queryBpcsAvt = (await LazyServiceProvider.LazyGetService<IBpcsAvtRepository>().GetQueryableAsync()).AsNoTracking();
            var queryBpcsPmfvm = (await LazyServiceProvider.LazyGetService<IBpcsPmfvmRepository>().GetQueryableAsync()).AsNoTracking();
            var querySpeakerLevel = (await LazyServiceProvider.LazyGetService<IOECSpeakerLevelDetailRepository>().GetQueryableAsync()).AsNoTracking();
            var querySpeakerAuthApply = (await LazyServiceProvider.LazyGetService<IOECSpeakerAuthApplyRepository>().GetQueryableAsync()).AsNoTracking();
            var querySpeakerAuthApplyUser = (await LazyServiceProvider.LazyGetService<IOECSpeakerAuthApplyUserRepository>().GetQueryableAsync()).AsNoTracking();

            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var companies = await dataverseService.GetCompanyList(request.Company.ToString());
            var companyCode = companies.FirstOrDefault()?.CompanyCode;

            //获取BU
            var orgs = await LazyServiceProvider.LazyGetService<ICommonService>().GetAllParentOrgs(request.OrgId);
            var bu = orgs.FirstOrDefault(a => a.OrganizationType == DataverseEnums.Organization.OrganizationType.Bu);
            if (bu == null)
                return default;

            var types = new string[] { "NHIV", "NT" };
            //常规讲者
            var queryNormalSpeaker = queryBpcsAvm.Where(a => types.Contains(a.Vtype) && a.Vnstat == "A" && a.Vcmpny.ToString() == companyCode)
                .Join(queryFinancial, a => a.FinaId, a => a.Id, (a, b) => new { Avm = a, Fin = b })
                .Join(queryVendor.Where(a => a.Status == VendorStatus.Valid && a.VendorType == VendorTypes.HCPPerson), a => a.Fin.VendorId, a => a.Id, (a, b) => new { a.Avm, Vendor = b })
                .Join(querySpeakerLevel.Where(a => a.BuId == bu.Id).Select(a => a.SpeakerLevelCode).Union(querySpeakerLevel.Where(a => a.BuId == Guid.Empty).Select(a => a.SpeakerLevelCode)), a => a.Vendor.SPLevel, a => a, (a, b) => a.Avm.Id);

            //按状态和时间段，筛选出授权的讲者数据
            var queryAuthVendor = querySpeakerAuthApply.Where(a => a.Status == SpeakerAuthStatus.Activated && a.ApplyUserBu == bu.Id && request.EstimatedDate >= a.StartTime && request.EstimatedDate <= a.EndTime)
                .Join(querySpeakerAuthApplyUser, a => a.Id, a => a.ApplyId, (a, b) => b.VendorCode)
                .Join(queryVendor.Where(a => a.Status == VendorStatus.Valid && a.VendorType == VendorTypes.HCPPerson), a => a, a => a.VendorCode, (a, b) => b);
            //额外授权的讲者
            var queryAuthSpeaker = queryBpcsAvm.Where(a => types.Contains(a.Vtype) && a.Vnstat == "A" && a.Vcmpny.ToString() == companyCode)
                .Join(queryFinancial, a => a.FinaId, a => a.Id, (a, b) => new { Avm = a, Fin = b })
                .Join(queryAuthVendor, a => a.Fin.VendorId, a => a.Id, (a, b) => a.Avm.Id);

            //查询常规讲者和授权讲者
            var query = queryBpcsAvm.Where(a => queryNormalSpeaker.Concat(queryAuthSpeaker).Contains(a.Id))
                .Join(queryFinancial, a => a.FinaId, a => a.Id, (a, b) => new { Avm = a, Fin = b })
                .Join(queryVendor, a => a.Fin.VendorId, a => a.Id, (a, b) => new { a.Avm, Vendor = b })
                .WhereIf(!string.IsNullOrEmpty(request.Currency), a => a.Avm.Vcurr == request.Currency)
                .WhereIf(!string.IsNullOrEmpty(request.Name), a => a.Avm.Vndnam.Contains(request.Name))
                .WhereIf(!string.IsNullOrEmpty(request.Code), a => a.Avm.Vendor.ToString().Contains(request.Code))
                .WhereIf(!string.IsNullOrEmpty(request.Level), a => a.Vendor.SPLevel == request.Level)
                .WhereIf(request.HospitalId.HasValue, a => a.Vendor.HospitalId == request.HospitalId)
                .WhereIf(request.DepartmentId.HasValue, a => a.Vendor.StandardHosDepId == request.DepartmentId);

            //通用PSA
            queryComPsa = queryComPsa.Where(a => request.EstimatedDate >= a.EffectStart && request.EstimatedDate <= (a.EffectEnd ?? DateTime.MaxValue)).OrderByDescending(a => a.EffectStart).Take(1);

            var finalQuery = query
                .GroupJoin(queryBpcsAvt, a => new { Company = a.Avm.Vcmpny, Term = a.Avm.Vterms }, a => new { Company = a.Vcmpy, Term = a.Vterm }, (a, b) => new { a.Avm, a.Vendor, Avts = b })
                .SelectMany(a => a.Avts.DefaultIfEmpty(), (a, b) => new { a.Avm, a.Vendor, Avt = b })
                .GroupJoin(queryComPsa, a => 1, a => 1, (a, b) => new { a.Avm, a.Vendor, a.Avt, ComPsas = b })
                .SelectMany(a => a.ComPsas.DefaultIfEmpty(), (a, b) => new { a.Avm, a.Vendor, a.Avt, ComPsa = b })
                .GroupJoin(queryBpcsPmfvm, a => new { Vnd = a.Avm.Vendor, Cmp = a.Avm.Vcmpny }, b => new { Vnd = b.Vnderx, Cmp = b.Vmcmpy }, (a, b) => new { a.Avm, a.Vendor, a.Avt, a.ComPsa, Pmfvms = b })
                .SelectMany(a => a.Pmfvms.DefaultIfEmpty(), (a, b) => new { a.Avm, a.Vendor, a.Avt, a.ComPsa, Pmfvm = b })
                .Select(a => new
                {
                    a.Avm.Id,
                    VendorId = a.Vendor.Id,
                    Name = a.Avm.Vndnam,
                    Code = a.Avm.Vendor.ToString(),
                    CompanyCode = a.Avm.Vcmpny.ToString(),
                    BankName = a.Pmfvm == null ? "" : a.Pmfvm.Vldrm1,
                    BankAccount = a.Pmfvm == null ? "" : a.Pmfvm.Vldrm2,
                    PaymentTerms = !string.IsNullOrEmpty(a.Avt.Vterm) && a.Avt.Vtmddy.HasValue ? $"{a.Avt.Vterm}_{a.Avt.Vtmddy}天" : null,
                    a.Vendor.Description,
                    LevelCode = a.Vendor.SPLevel,
                    a.Vendor.HospitalName,
                    a.Vendor.StandardHosDepName,
                    a.Vendor.EpdId,
                    a.Vendor.UserId,
                    a.Vendor.HandPhone,
                    a.Vendor.CertificateCode,
                    a.Vendor.HosDepartment,
                    a.Vendor.PTName,
                    ComPsaTimesLimit = a.ComPsa == null ? 0 : a.ComPsa.TimesLimit,
                    ComPsaAmountLimit = a.ComPsa == null ? 0 : a.ComPsa.AmountLimit,
                    ComPsaUsedTimes = queryPsaUseHistory.Where(a1 => a1.VendorId == a.Vendor.Id && a1.EffectiveDate.Year == request.EstimatedDate.Year && !a1.PsaExternalId.HasValue).Sum(a1 => a1.Times),
                    ComPsaUsedAmount = queryPsaUseHistory.Where(a1 => a1.VendorId == a.Vendor.Id && a1.EffectiveDate.Year == request.EstimatedDate.Year && !a1.PsaExternalId.HasValue).Sum(a1 => a1.Amount),
                    NeedCheckLimit = queryComExpenseType.Where(a1 => a1.ComPSALimitId == a.ComPsa.Id).Any(a1 => a1.ExpenseNature == request.CostNature)
                });

            var count = query.Count();
            var datas = finalQuery.Skip(request.PageIndex * request.PageSize).Take(request.PageSize).Select(a => new GetSpeakerListForChoiceResponseDto
            {
                Id = a.Id,
                VendorId = a.VendorId,
                Name = a.Name,
                Code = a.Code,
                PaymentTerms = a.PaymentTerms,
                Description = a.Description,
                LevelCode = a.LevelCode,
                Level = a.LevelCode,
                Hospital = a.HospitalName,
                Department = a.StandardHosDepName,
                EpdDoctorKey = a.EpdId,
                UserId = a.UserId,
                Phone = a.HandPhone,
                CertificateNo = a.CertificateCode,
                HosDepartment = a.HosDepartment,
                JobTitle = a.PTName,
                YearlyCount = a.ComPsaTimesLimit + a.ComPsaUsedTimes,
                YearlyAmount = a.ComPsaAmountLimit + a.ComPsaUsedAmount,
                NeedCheckLimit = a.NeedCheckLimit,
                BankName = a.BankName,
                BankAccount = a.BankAccount,
                CompanyCode = a.CompanyCode
            }).ToArray();

            if (datas.Any())
            {
                var commonService = LazyServiceProvider.LazyGetService<ICommonService>();
                var consentResponses = await commonService.GetLeatestConsentInfosAsync();
                var userIds = datas.Where(a => a.UserId.HasValue).Select(a => a.UserId.Value).ToArray();
                var consentSigneds = await commonService.GetConsentSignedInfosAsync(userIds);
                //特殊供应商文字
                var specialvendors = await dataverseService.GetSpecialvendorAsync();

                foreach (var item in datas)
                {
                    if (item.UserId.HasValue && item.UserId != Guid.Empty)
                        item.ConsentStatus = commonService.IsSignLeatestVersion(item.UserId.Value, consentResponses, consentSigneds);
                    item.BankAccount = DataMaskingHelper.MaskBankAccount(item.BankAccount);

                    var specialVendor = GetVendorName(specialvendors, item.Code, item.CompanyCode);
                    item.Name = !string.IsNullOrWhiteSpace(specialVendor) ? specialVendor : item.Name;
                }
            }

            return new PagedResultDto<GetSpeakerListForChoiceResponseDto>(count, datas);
        }

        /// <summary>
        /// 获取用于选择的非讲者供应商列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetNonSpeakerListForChoiceResponseDto>> GetNonSpeakerForChoiceAsync(GetNonSpeakerListForChoiceRequestDto request)
        {
            var queryVendor = (await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync()).AsNoTracking();
            var queryOrg = (await LazyServiceProvider.LazyGetService<IVendorOrgnizationRepository>().GetQueryableAsync()).AsNoTracking();
            var queryFinancial = (await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync()).AsNoTracking();
            var queryPersonal = (await LazyServiceProvider.LazyGetService<IVendorPersonalRepository>().GetQueryableAsync()).AsNoTracking();
            var queryUser = (await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync()).AsNoTracking();
            var queryBpcsAvm = (await LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync()).AsNoTracking();
            var queryBpcsAvt = (await LazyServiceProvider.LazyGetService<IBpcsAvtRepository>().GetQueryableAsync()).AsNoTracking();
            var queryBpcsPmfvm = (await LazyServiceProvider.LazyGetService<IBpcsPmfvmRepository>().GetQueryableAsync()).AsNoTracking();

            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var companies = await dataverseService.GetCompanyList();

            //3112（优先）【生产】【采购新建】ER类型选择供应商时不限制公司--BE
            if (request.PayMethod == PayMethods.ER)
                queryBpcsAvm = queryBpcsAvm.Where(a => a.Vnstat == "A");
            else
            {
                var companyCode = companies.FirstOrDefault(a => a.Id == request.Company)?.CompanyCode;
                string[] types = request.PayMethod == PayMethods.AP ? ["NL", "NH", "NT", "NM"] : ["NL", "NLIV", "NH", "NT", "NM"];
                queryBpcsAvm = queryBpcsAvm.Where(a => a.Vnstat == "A" && a.Vcmpny.ToString() == companyCode && types.Contains(a.Vtype));
            }

            var queryCondition = queryBpcsAvm
                .WhereIf(!string.IsNullOrEmpty(request.Currency), a => a.Vcurr == request.Currency)
                .GroupJoin(queryFinancial, a => a.FinaId, a => a.Id, (a, b) => new { Avm = a, Fins = b })
                .SelectMany(a => a.Fins.DefaultIfEmpty(), (a, b) => new { a.Avm, Fin = b })
                .GroupJoin(queryVendor, a => a.Fin.VendorId, a => a.Id, (a, b) => new { a.Avm, Vendors = b })
                .SelectMany(a => a.Vendors.DefaultIfEmpty(), (a, b) => new { a.Avm, Vendor = b })
                .GroupJoin(queryOrg, a => a.Vendor.Id, a => a.VendorId, (a, b) => new { a.Avm, a.Vendor, Orgs = b })
                .SelectMany(a => a.Orgs.DefaultIfEmpty(), (a, b) => new { a.Avm, a.Vendor, Org = b })
                .GroupJoin(queryBpcsPmfvm, a => new { Company = a.Avm.Vcmpny, a.Avm.Vendor }, a => new { Company = a.Vmcmpy, Vendor = a.Vnderx }, (a, b) => new { a.Avm, a.Vendor, a.Org, Pmfvms = b })
                .SelectMany(a => a.Pmfvms.DefaultIfEmpty(), (a, b) => new { a.Avm, a.Vendor, a.Org, Pmfvm = b })
                .GroupJoin(queryUser, a => a.Vendor.UserId, a => a.Id, (a, b) => new { a.Avm, a.Vendor, a.Org, a.Pmfvm, Users = b })
                .SelectMany(a => a.Users.DefaultIfEmpty(), (a, b) => new { a.Avm, a.Vendor, a.Org, a.Pmfvm, User = b })
                .GroupJoin(queryBpcsAvt, a => new { Company = a.Avm.Vcmpny, Term = a.Avm.Vterms }, a => new { Company = a.Vcmpy, Term = a.Vterm }, (a, b) => new { a.Avm, a.Vendor, a.Org, a.Pmfvm, a.User, Avts = b })
                .SelectMany(a => a.Avts.DefaultIfEmpty(), (a, b) => new { a.Avm, a.Vendor, a.Org, a.Pmfvm, a.User, Avt = b })
                .WhereIf(!string.IsNullOrEmpty(request.Name), a => string.IsNullOrEmpty(a.Pmfvm.Vextnm) ? a.Avm.Vndnam.Contains(request.Name) : a.Pmfvm.Vextnm.Contains(request.Name))
                .WhereIf(!string.IsNullOrEmpty(request.Code), a => a.Avm.Vendor.ToString().Contains(request.Code))
                .WhereIf(!string.IsNullOrEmpty(request.VendorType), a => a.Avm.Vtype.Contains(request.VendorType))
                .WhereIf(!string.IsNullOrEmpty(request.Contact), a => a.Avm.Vcon.Contains(request.Contact))
                .WhereIf(!string.IsNullOrEmpty(request.ContactPhone), a => a.Avm.Vphone.Contains(request.ContactPhone));

            if (request.PayMethod != PayMethods.ER)
                queryCondition = queryCondition
                    .Where(a => !(a.Avm.Vtype == "NLIV" && a.User == null) && (a.Vendor != null ? a.Vendor.Status == VendorStatus.Valid : true))//非讲者个人类型，如果没有关联上NexBPM的数据，也不显示
                    .Where(a => !(a.Avm.Vtype == "NT" && a.Vendor.VendorType == VendorTypes.HCPPerson));//不显示在Bpcs中是NT类型在NexBPM是Hcp的类型

            var query = queryCondition.Select(a => new GetNonSpeakerListForChoiceResponseDto
            {
                Id = a.Avm.Id,
                Name = string.IsNullOrEmpty(a.Pmfvm.Vextnm) ? a.Avm.Vndnam : a.Pmfvm.Vextnm,
                Code = a.Avm.Vendor.ToString(),
                PaymentTerm = a.Avt.Vterm,
                PaymentTerms = !string.IsNullOrEmpty(a.Avt.Vterm) && a.Avt.Vtmddy.HasValue ? $"{a.Avt.Vterm}_{a.Avt.Vtmddy}天" : null,
                Description = a.Vendor.Description,
                VendorType = a.Avm.Vtype,
                ContactPhone = a.Avm.Vphone,
                //ConsentStatus = a.Vendor.SignedStatus,
                RegCertificateAddress = a.Avm.Vndad1 + a.Avm.Vndad2 + a.Avm.Vndad3,
                ApsPorpertyStr = a.Vendor.ApsPorperty,
                ContactName = a.Avm.Vcon,
                ContactEmail = a.Pmfvm.Vemlad,
                BankName = a.Pmfvm == null ? "" : a.Pmfvm.Vldrm1,
                BankAccount = a.Pmfvm == null ? "" : a.Pmfvm.Vldrm2,
                UserId = a.User == null ? null : a.User.Id,
                Currency = a.Avm.Vcurr,
                CompanyCode = a.Avm.Vcmpny.ToString()
            });

            var count = query.Count();
            var datas = query.Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToArray();
            if (datas.Any())
            {
                var commonService = LazyServiceProvider.LazyGetService<ICommonService>();
                var apsProperty = await dataverseService.GetDictionariesAsync(DictionaryType.ApsProperty);
                var consentResponses = await commonService.GetLeatestConsentInfosAsync();
                var userIds = datas.Where(a => a.UserId.HasValue).Select(a => a.UserId.Value).ToArray();
                var consentSigneds = await commonService.GetConsentSignedInfosAsync(userIds);
                var currencys = datas.Where(a => !string.IsNullOrWhiteSpace(a.Currency)).Select(a => a.Currency).ToList();//获取供应商相关的币种信息
                //实时汇率 
                var queryBpcsGcc = await LazyServiceProvider.LazyGetService<IBpcsGccRepository>().GetQueryableAsync();
                var usdGcc = queryBpcsGcc.Where(a => currencys.Contains(a.Ccfrcr) && a.Cctocr == "RMB")
                    .GroupBy(a => a.Ccfrcr)//币种分组
                    .Select(a => a.OrderByDescending(a => a.Ccnvdt).FirstOrDefault())//取每组中最新一条数据
                    .ToList();
                //特殊供应商文字
                var specialvendors = await dataverseService.GetSpecialvendorAsync();

                foreach (var item in datas)
                {
                    var specialVendor = GetVendorName(specialvendors, item.Code, item.CompanyCode);
                    item.Name = !string.IsNullOrWhiteSpace(specialVendor) ? specialVendor : item.Name;
                    if (item.UserId.HasValue)
                        item.ConsentStatus = commonService.IsSignLeatestVersion(item.UserId.Value, consentResponses, consentSigneds);
                    if (!string.IsNullOrWhiteSpace(item.ApsPorpertyStr))
                    {
                        item.VendorPorperty = true;//有APS属性才为Ture
                        var aspList = item.ApsPorpertyStr.Split(',');
                        var apsList = new List<DictionaryDto>();
                        foreach (var aps in aspList)
                        {
                            var apsp = apsProperty.FirstOrDefault(a => a.Code == aps);
                            if (apsp != null)
                            {
                                apsList.Add(apsp);
                            }
                        }
                        item.ApsPorperty = aspList.ToList();
                        item.ApsPorpertys = apsList;
                    }
                    if (item.Currency == "RMB")
                    {
                        item.ExchangeRate = 1;
                        item.CurrencySymbol = "￥";
                    }
                    else
                    {
                        var currency = usdGcc.Where(a => !string.IsNullOrWhiteSpace(a.Ccfrcr) && a.Ccfrcr == item.Currency).FirstOrDefault();//获取当前行数据中币种对应的实时汇率
                        item.ExchangeRate = (float)currency.Ccnvfc.Value;
                        item.CurrencySymbol = companies.FirstOrDefault()?.CompanyCurrency.Where(a => a.Code == item.Currency).FirstOrDefault()?.CurrencySymbol;//获取币种符号
                    }
                    item.BankAccount = DataMaskingHelper.MaskBankAccount(item.BankAccount);
                }
            }

            return new PagedResultDto<GetNonSpeakerListForChoiceResponseDto>(count, datas);
        }

        /// <summary>
        /// 获取特殊供应商名称
        /// </summary>
        /// <param name="specialVendors"></param>
        /// <param name="vendorCode"></param>
        /// <param name="companyCode"></param>
        /// <returns></returns>
        private string GetVendorName(IEnumerable<SpecialVendorDto> specialVendors, string vendorCode, string companyCode)
        {
            if (string.IsNullOrWhiteSpace(vendorCode) || string.IsNullOrWhiteSpace(companyCode))
                return "";
            var specialvendor = specialVendors.FirstOrDefault(a => a.VendorCode == vendorCode && a.CompanyCode == companyCode);//特殊供应商
            if (!string.IsNullOrWhiteSpace(specialvendor?.Name))
                return specialvendor.Name;
            return "";
        }

        /// <summary>
        /// 获取员工类型的供应商
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetPersonVendorForChoiceResponseDto>> GetPersonVendorForChoiceAsync(GetPersonVendorForChoiceRequestDto request)
        {
            var queryBpcsAvm = await LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync();
            var queryBpcsAvt = await LazyServiceProvider.LazyGetService<IBpcsAvtRepository>().GetQueryableAsync();
            var queryBpcsPmfvm = await LazyServiceProvider.LazyGetService<IBpcsPmfvmRepository>().GetQueryableAsync();
            var companies = await LazyServiceProvider.LazyGetService<IDataverseService>().GetCompanyList();
            var company = companies.FirstOrDefault(a => a.Id == request.Company);

            var query = queryBpcsAvm.Where(a => a.Vtype.StartsWith("NE") && a.Vnstat == "A")
                .WhereIf(!string.IsNullOrEmpty(request.Code), a => a.Vendor.ToString().Contains(request.Code))
                .WhereIf(!string.IsNullOrEmpty(request.VendorType), a => a.Vtype.Contains(request.VendorType))
                .WhereIf(!string.IsNullOrEmpty(request.Contact), a => a.Vcon.Contains(request.Contact))
                .WhereIf(!string.IsNullOrEmpty(request.ContactPhone), a => a.Vphone.Contains(request.ContactPhone))
                .WhereIf(request.Company.HasValue, a => a.Vcmpny.ToString() == company.CompanyCode)
                .GroupJoin(queryBpcsAvt, a => new { Company = a.Vcmpny, Term = a.Vterms }, a => new { Company = a.Vcmpy, Term = a.Vterm }, (a, b) => new { Avm = a, Avts = b })
                .SelectMany(a => a.Avts.DefaultIfEmpty(), (a, b) => new { a.Avm, Avt = b })
                .GroupJoin(queryBpcsPmfvm, a => new { Company = a.Avm.Vcmpny, a.Avm.Vendor }, a => new { Company = a.Vmcmpy, Vendor = a.Vnderx }, (a, b) => new { a.Avm, a.Avt, Pmfvms = b })
                .SelectMany(a => a.Pmfvms.DefaultIfEmpty(), (a, b) => new { a.Avm, a.Avt, Pmfvm = b })
                .WhereIf(!string.IsNullOrEmpty(request.Name), a => string.IsNullOrEmpty(a.Pmfvm.Vextnm) ? a.Avm.Vndnam.Contains(request.Name) : a.Pmfvm.Vextnm.Contains(request.Name))
                .Select(a => new GetPersonVendorForChoiceResponseDto
                {
                    Id = a.Avm.Id,
                    Code = a.Avm.Vendor.ToString(),
                    Name = string.IsNullOrEmpty(a.Pmfvm.Vextnm) ? a.Avm.Vndnam : a.Pmfvm.Vextnm,
                    PaymentTerms = !string.IsNullOrEmpty(a.Avt.Vterm) && a.Avt.Vtmddy.HasValue ? $"{a.Avt.Vterm}_{a.Avt.Vtmddy}天" : null,
                    VendorType = a.Avm.Vtype,
                    Contact = a.Avm.Vcon,
                    ContactPhone = a.Avm.Vphone,
                    Company = a.Avm.Vcmpny.ToString(),
                    Email = string.IsNullOrEmpty(a.Avm.Vmdatn) ? a.Pmfvm.Vemlad : a.Avm.Vmdatn,
                });

            var count = query.Count();
            var datas = query.Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToArray();

            foreach (var item in datas)
            {
                item.Company = companies.FirstOrDefault(a => a.CompanyCode == item.Company)?.CompanyName;
            }

            return new PagedResultDto<GetPersonVendorForChoiceResponseDto>(count, datas);
        }

        /// <summary>
        /// 获取Psa例外剩余次数和金额
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<GetSurplusPsaExtraLimitResponseDto> GetPsaExtraSurplusLimitAsync(GetSurplusPsaExtraLimitRequestDto request)
        {
            //获取机构
            var orgs = await LazyServiceProvider.LazyGetService<IDataverseService>().GetOrganizations(request.OrgId.ToString());
            if (!orgs.Any())
                return default;

            //获取Bu
            var org = orgs.First();
            var parents = await LazyServiceProvider.LazyGetService<ICommonService>().GetParentOrgs(org);
            var bu = parents.FirstOrDefault(a => a.OrganizationType == DataverseEnums.Organization.OrganizationType.Bu);
            if (bu == null)
                return default;

            var queryBpcsAvm = await LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync();
            var queryFinancial = await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync();
            var queryPsaExtra = await LazyServiceProvider.LazyGetService<IOECPSASpeakerLimitExtraRepository>().GetQueryableAsync();
            var queryPsaUseHistory = await LazyServiceProvider.LazyGetService<IOECPSASpeakerLimitUseHistoryRepository>().GetQueryableAsync();

            var psaExtra = await queryBpcsAvm.Where(a => a.Id == request.VendorId)
                .Join(queryFinancial, a => a.FinaId, a => a.Id, (a, b) => new { Avm = a, Fin = b })
                .Join(queryPsaExtra.Where(a => a.ExtralAuditApplicationNo == request.ExceptionNumber && a.DivisionId == bu.Id && a.Year == request.EstimatedDate.Year && a.ModifyType == ModifyTypes.ManuallyAdded), a => a.Fin.VendorId, a => a.VendorId, (a, b) => b)
                .Select(a => new
                {
                    a.ExtralTimesRest,
                    a.ExtralAmountRest,
                    ExtraPsaUsedTimes = queryPsaUseHistory.Where(a1 => a1.PsaExternalId == a.Id).Sum(a1 => a1.Times) - a.ExtralTimesRest,
                    ExtraPsaUsedAmount = queryPsaUseHistory.Where(a1 => a1.PsaExternalId == a.Id).Sum(a1 => a1.Amount) - a.ExtralAmountRest
                })
                .FirstOrDefaultAsync();

            if (psaExtra == null)
                return default;

            return new GetSurplusPsaExtraLimitResponseDto { SurplusTimes = psaExtra.ExtralTimesRest - Math.Abs(psaExtra.ExtraPsaUsedTimes), SurplusAmount = psaExtra.ExtralAmountRest - Math.Abs(psaExtra.ExtraPsaUsedAmount) };
        }

        /// <summary>
        /// 根据组织Id获取关联的成本中心
        /// </summary>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<IEnumerable<KeyValuePair<Guid, string>>> GetCostcenterByOrgAsync(Guid? orgId)
        {
            IEnumerable<CostcenterDto> costcenters;
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            //不传org时，返回所有成本中心
            if (!orgId.HasValue)
                costcenters = await dataverseService.GetCostcentersAsync();
            else
            {
                var orgs = await dataverseService.GetOrganizations(orgId.ToString());
                if (!orgs.Any())
                    return Enumerable.Empty<KeyValuePair<Guid, string>>();

                var org = orgs.First();
                if (!org.CostcenterId.HasValue)
                    return Enumerable.Empty<KeyValuePair<Guid, string>>();

                costcenters = await dataverseService.GetCostcentersAsync(org.CostcenterId?.ToString());
            }
            return costcenters.Select(a => new KeyValuePair<Guid, string>(a.Id, a.Name));
        }

        /// <summary>
        /// 获取子预算列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetSubBudgetsResponseDto>> GetSubBudgetInfosAsync(GetSubBudgetsRequestDto request)
        {
            //获取当前机构
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            IEnumerable<DepartmentDto> orgs = await dataverseService.GetOrganizations(request.OrgId.ToString());
            if (!orgs.Any())
                return default;

            var org = orgs.First();
            //获取当前机构的上级BU
            orgs = await LazyServiceProvider.LazyGetService<ICommonService>().GetParentOrgs(org);
            var bu = orgs.FirstOrDefault(a => a.OrganizationType == DataverseEnums.Organization.OrganizationType.Bu);
            if (bu == null)
                return default;

            var queryUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var querySubBudget = await LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>().GetQueryableAsync();
            if (CurrentUser.Id.HasValue)
            {
                //如果大区是全国、或者为空就可以用所有的预算，否则只能用他所属的那个大区的预算
                var staff = await dataverseService.GetStaffs(CurrentUser.Id.ToString());
                if (staff.Any() && staff.First().DistrictId.HasValue && staff.First().DistrictName != "全国")
                    querySubBudget = querySubBudget.Where(a => a.RegionId == staff.First().DistrictId);
            }

            var query = querySubBudget.Where(a => a.BuId == bu.Id && a.CostCenterId == org.CostcenterId && true && a.Status).Include(a => a.MasterBudget)
                .Include(s => s.MonthlyBudgets.Where(s => s.Status))
                .GroupJoin(queryUser, a => a.OwnerId, a => a.Id, (a, b) => new { SubBudget = a, Users = b })
                .SelectMany(a => a.Users.DefaultIfEmpty(), (a, b) => new { a.SubBudget, User = b })
                .WhereIf(!string.IsNullOrEmpty(request.Code), a => a.SubBudget.Code.Contains(request.Code))
                .WhereIf(!string.IsNullOrEmpty(request.Description), a => a.SubBudget.Description.Contains(request.Description))
                .WhereIf(!string.IsNullOrEmpty(request.Owner), a => a.User.Name.Contains(request.Owner))
                .OrderByDescending(s => s.SubBudget.CreationTime)
                .Select(a => new GetSubBudgetsResponseDto
                {
                    Id = a.SubBudget.Id,
                    BudgetCode = a.SubBudget.Code,
                    Description = a.SubBudget.Description,
                    OrgName = bu.DepartmentName,
                    Owner = a.User.Name,
                    TotalAmount = a.SubBudget.BudgetAmount,
                    UsedAmount = a.SubBudget.UesdAmount,
                    AvailableAmount = a.SubBudget.GetAvailableAmount(),
                    Capital = a.SubBudget.MasterBudget.Capital
                });

            var count = query.Count();
            var datas = query.Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToArray();

            return new PagedResultDto<GetSubBudgetsResponseDto>(count, datas);
        }

        /// <summary>
        /// 获取预算信息
        /// </summary>
        /// <param name="subBudgetId"></param>
        /// <returns></returns>
        public async Task<GetBudgetInfoResponse> GetSubBudgetInfoAsync(Guid subBudgetId)
        {
            //查询子预算数据
            var queryUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var querySubBudget = await LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>().GetQueryableAsync();
            var budgetData = querySubBudget.Include(a => a.MasterBudget).Include(a => a.MonthlyBudgets.Where(s => s.Status))
                .GroupJoin(queryUser, a => a.OwnerId, a => a.Id, (a, b) => new { Subbudget = a, Users = b })
                .SelectMany(a => a.Users.DefaultIfEmpty(), (a, b) => new { a.Subbudget, User = b })
                .FirstOrDefault(a => a.Subbudget.Id == subBudgetId);

            if (budgetData == null)
                return default;

            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var costcenters = await dataverseService.GetCostcentersAsync(budgetData.Subbudget.CostCenterId.ToString());
            var bus = await dataverseService.GetOrganizations(budgetData.Subbudget.BuId.ToString());
            var regions = await dataverseService.GetDistrict(budgetData.Subbudget.RegionId.ToString());

            var budget = new GetBudgetInfoResponse
            {
                BudgetId = budgetData.Subbudget.Id,
                BudgetCode = budgetData.Subbudget.Code,
                Description = budgetData.Subbudget.Description,
                OrgId = bus.FirstOrDefault()?.Id,
                OrgName = bus.FirstOrDefault()?.DepartmentName,
                CostcenterId = costcenters.FirstOrDefault()?.Id,
                CostcenterName = costcenters.FirstOrDefault()?.Name,
                Lead = budgetData.User?.Name,
                TotalAmount = budgetData.Subbudget.BudgetAmount,
                UsedAmount = budgetData.Subbudget.UesdAmount,
                AvailableAmount = budgetData.Subbudget.GetAvailableAmount(),
                Capital = budgetData.Subbudget.MasterBudget.Capital
            };

            if (regions.Any())
            {
                budget.RegionId = regions.First().Id;
                budget.RegionName = regions.First().Name;
            }

            //附件
            if (!string.IsNullOrEmpty(budgetData.Subbudget.AttachmentFile))
            {
                var ids = budgetData.Subbudget.AttachmentFile.Split(",").Select(Guid.Parse);
                var attachments = await LazyServiceProvider.LazyGetService<IAttachmentRepository>().GetListAsync(a => ids.Contains(a.Id));
                budget.Files = attachments.Select(a => new UploadFileResponseDto
                {
                    AttachmentId = a.Id,
                    FileName = a.FileName,
                    FilePath = a.FilePath,
                    Success = true
                });
            }

            return budget;
        }

        /// <summary>
        /// 获取消费大类
        /// </summary>
        /// <param name="orgId">机构Id</param>
        /// <param name="isCapital">是否资产，由预算信息带出</param>
        /// <returns></returns>
        public async Task<IEnumerable<ConsumeCategorySponsorshipTypeResponseDto>> GetConsumeCategoryAsync(Guid orgId, bool isCapital)
        {
            DepartmentDto org = null;
            var allParentOrgs = await LazyServiceProvider.LazyGetService<ICommonService>().GetAllParentOrgs(orgId);
            org = allParentOrgs.FirstOrDefault(a => a.OrganizationType == DataverseEnums.Organization.OrganizationType.Affiliates);

            //do
            //{
            //    var orgs = await dataverseService.GetOrganizations(orgId.ToString());
            //    if (!orgs.Any())
            //        break;
            //    org = orgs.First();
            //    if (org.OrganizationType == DataverseEnums.Organization.OrganizationType.Affiliates)
            //        break;
            //    else if (org.ParentDepartment.HasValue)
            //        orgId = org.ParentDepartment.Value;

            //} while (org.ParentDepartment.HasValue);

            if (org == null)
                return default;

            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var consumeCategories = await dataverseService.GetConsumeCategoryAsync(org.Id.ToString(), null);
            consumeCategories = consumeCategories.Where(a => (!a.FlowType.HasValue || a.FlowType == DataverseEnums.ConsumeCategory.FlowTypes.PR) && a.IsAssets == isCapital);
            var sponsorshipTypes = await dataverseService.GetDictionariesAsync(DictionaryType.SponsorshipType);
            var responses = ObjectMapper.Map<IEnumerable<ConsumeCategoryDto>, IEnumerable<ConsumeCategorySponsorshipTypeResponseDto>>(consumeCategories);
            foreach (var item in responses)
            {
                var relations = await dataverseService.GetConsumeCategorySponsorshipTypeRelationDtosAsync(item.Id.ToString());
                item.SponsorshipTypes = sponsorshipTypes.Where(a => relations.Select(a => a.SponsorshipTypeCode).Contains(a.Code, StringComparer.CurrentCultureIgnoreCase));
            }

            return responses;
        }

        /// <summary>
        /// 根据消费大类、组织、成本中心、付款方式获取费用性质
        /// </summary>
        /// <param name="consumeId">消费大类</param>
        /// <param name="orgId">组织</param>
        /// <param name="costcenterId">成本中心</param>
        /// <param name="productIds">产品Id集合</param>
        /// <param name="payMethod">付款方式</param>
        /// <returns></returns>
        public async Task<IEnumerable<CostNatureDto>> GetCostNatureAsync(Guid consumeId, Guid orgId, Guid costcenterId, IEnumerable<Guid> productIds, PayMethods payMethod)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            //获取该消费大类下的费用性质
            var costNatures = await dataverseService.GetCostNatureAsync(consumeId.ToString());

            //2024-10-08 16:25，2173【采购管理】【采购申请】ER选择费用性质时，所有的费用性质都可选
            if (payMethod == PayMethods.ER)
                return costNatures;

            var getAllParentOrgsTask = LazyServiceProvider.LazyGetService<ICommonService>().GetAllParentOrgs(orgId);
            var getAllBuCodingCfgsTask = dataverseService.GetBuCodingCfgAsync();
            var getCostCentersTask = dataverseService.GetCostcentersAsync(costcenterId.ToString());
            Task.WaitAll(getAllParentOrgsTask, getAllBuCodingCfgsTask, getCostCentersTask);

            //获取组织所属BU
            var bu = getAllParentOrgsTask.Result.FirstOrDefault(a => a.OrganizationType == DataverseEnums.Organization.OrganizationType.Bu);
            if (bu == null)
                return Array.Empty<CostNatureDto>();

            //获取BU编码配置
            var buCodingCfg = getAllBuCodingCfgsTask.Result.FirstOrDefault(a => a.BuId == bu.Id);
            if (buCodingCfg == null)
                return Array.Empty<CostNatureDto>();

            //获取成本中心
            var costcenter = getCostCentersTask.Result.FirstOrDefault(a => a.Id == costcenterId);
            if (costcenter == null)
                return Array.Empty<CostNatureDto>();


            var buCode = buCodingCfg.BuCode;
            var costcenterCode = costcenter.Code;
            var getCoaMappingRulesTask = dataverseService.GetCoaMappingRuleAsync($"\\\\{buCode}\\\\{costcenterCode}\\\\");
            var getAllProductsTask = dataverseService.GetProductsAsync();
            var getOrgCostNatureRelationsTask = dataverseService.GetOrgCostNatureRelationAsync();
            Task.WaitAll(getCoaMappingRulesTask, getAllProductsTask, getOrgCostNatureRelationsTask);

            //获取BU、成本中心、费用性质映射关系
            var coaMappingRules = getCoaMappingRulesTask.Result.ToArray();
            if (!coaMappingRules.Any())
                return Array.Empty<CostNatureDto>();

            //获取对应产品
            var productCostNatureRelations = getAllProductsTask.Result.Where(a => productIds.Contains(a.Id) && !string.IsNullOrEmpty(a.LimitCostNatureCode)).SelectMany(a => a.LimitCostNatureCode.Split(",", StringSplitOptions.RemoveEmptyEntries)).ToArray();

            //获取BU与费用性质关系
            var orgCostNatureRelations = getOrgCostNatureRelationsTask.Result.ToArray();

            //coa与费用性质的交集
            var intersectCoas = coaMappingRules.Where(a => a.BuCode == buCode && a.CostcenterCode == costcenterCode)
                .Join(costNatures, a => a.CostNatureCode, a => a.Code, (a, b) => b);
            if (productCostNatureRelations.Any())
                intersectCoas = intersectCoas.Join(productCostNatureRelations, a => a.Code, a => a, (a, b) => a);
            //获取匹配的费用性质和未设置特定BU的费用性质
            costNatures = intersectCoas.Join(orgCostNatureRelations.Where(a => a.OrgId == bu.Id), a => a.Id, a => a.CostNatureId, (a, b) => a)
                .Union
                (
                    intersectCoas.Where(a => !orgCostNatureRelations.Select(a1 => a1.CostNatureId).Distinct().Any(a1 => a1 == a.Id))
                );

            return costNatures.Where(a => string.IsNullOrEmpty(a.PayMethods) || a.PayMethods.Split(",", StringSplitOptions.RemoveEmptyEntries).Any(a1 => a1.Equals(payMethod.ToString(), StringComparison.CurrentCultureIgnoreCase)));
        }

        /// <summary>
        /// 获取主会场PR选择列表
        /// </summary>
        /// <returns></returns>
        public async Task<PagedResultDto<GetMainVenuePRListResponse>> GetMainVenuePRsAsync(GetMainVenuePRListRequest request)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var queryablePR = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();

            var query = queryablePR.Where(a => a.Status == PurPRApplicationStatus.Approved)
                .WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), a => a.ApplicationCode.Contains(request.ApplicationCode))
                .GroupJoin(queryableUser, a => a.ApplyUserId, a => a.Id, (a, b) => new { PR = a, Users = b })
                .SelectMany(a => a.Users.DefaultIfEmpty(), (a, b) => new
                {
                    a.PR.Id,
                    a.PR.ApplicationCode,
                    Applicant = b.Name,
                    a.PR.ApplyTime,
                    a.PR.MeetingTitle,
                    a.PR.ExpenseType,
                    a.PR.ActiveHostCity,
                    a.PR.AcitveHostAddress,
                    a.PR.ActiveType,
                    a.PR.MeetingType
                });

            var getExpenseTypesTask = dataverseService.GetConsumeCategoryAsync();
            var getMeetingTypesTask = dataverseService.GetDictionariesAsync(DictionaryType.MeetingType);
            Task.WaitAll(getExpenseTypesTask, getMeetingTypesTask);

            var count = query.Count();
            var datas = query.Skip(request.PageIndex * request.PageSize)
                .Take(request.PageSize).ToArray()
                .Select(a => new GetMainVenuePRListResponse
                {
                    Id = a.Id,
                    ApplicationCode = a.ApplicationCode,
                    Applicant = a.Applicant,
                    ApplyTime = a.ApplyTime?.ToString("yyyy-MM-dd"),
                    MeetingTitle = a.MeetingTitle,
                    ExpenseType = getExpenseTypesTask.Result.FirstOrDefault(a1 => a1.Id == a.ExpenseType)?.Name,
                    ActiveHostCity = a.ActiveHostCity,
                    AcitveHostAddress = a.AcitveHostAddress,
                    ActiveType = a.ActiveType,
                    MeetingType = getMeetingTypesTask.Result.FirstOrDefault(a1 => a1.Code == a.MeetingType)?.Name
                }).ToArray();

            var result = new PagedResultDto<GetMainVenuePRListResponse>(count, datas);
            return result;
        }

        /// <summary>
        /// 创建采购申请单
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> CreatePRApplicationAsync(CreatePRApplicationRequest request)
        {
            var orgs = await LazyServiceProvider.LazyGetService<IDataverseService>().GetOrganizations(request.ApplyUserDept.ToString());
            if (!orgs.Any())
                return MessageResult.FailureResult("未找到申请人部门信息");

            var prApplicationRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>();
            var prApplication = ObjectMapper.Map<CreatePRApplicationRequest, PurPRApplication>(request);
            if (CurrentUser.Id.HasValue)
            {
                prApplication.ApplyUserId = CurrentUser.Id.Value;
                prApplication.ApplyUserIdName = CurrentUser.Name;
            }

            var messageResult = await HandlePRApplicationAsync(prApplication, request, orgs.First());
            if (!messageResult.Success)
                return messageResult;

            prApplication = messageResult.Data as PurPRApplication;
            await prApplicationRepository.InsertAsync(prApplication);

            //处理主表产品拆分信息
            messageResult = await HandlePRApplicationProductApportionmentAsync(prApplication, request.ProductApportionments);
            if (!messageResult.Success)
                return messageResult;

            //采购明细项
            messageResult = await HandlePRApplicationDetailAsync(prApplication, request.DetailItems);
            if (!messageResult.Success)
                return messageResult;

            var prApplicationDetailData = messageResult.Data as Tuple<List<PurPRApplicationDetail>, List<PurPRApplicationDetail>>;

            //会议支持的预计费用
            var costItems = HandlePRApplicationCostItemAsync(prApplication, request.CostItems);
            if (costItems?.Any() == true)
                await LazyServiceProvider.LazyGetService<IPurPRApplicationCostItemRepository>().InsertManyAsync(costItems);

            //Hcp旅行社会务费
            var conferenceFees = HandlePRApplicationHcpTravelAgencyConferenceFeeAsync(prApplication, request.HcpTravelAgencyConferenceFees);
            if (conferenceFees?.Any() == true)
                await LazyServiceProvider.LazyGetService<IPurPRApplicationHcpTravelLodgingFeeRepository>().InsertManyAsync(conferenceFees);

            await InsertAndGenerateSerialNoAsync(prApplicationRepository, prApplication, "P");
            await CurrentUnitOfWork.SaveChangesAsync();

            return MessageResult.SuccessResult(new Tuple<PurPRApplication, IEnumerable<PurPRApplicationDetail>, IEnumerable<PurPRApplicationDetail>>(prApplication, prApplicationDetailData.Item1, prApplicationDetailData.Item2));
        }

        /// <summary>
        /// 处理PR申请主表信息
        /// </summary>
        /// <param name="prApplication"></param>
        /// <param name="request"></param>
        /// <param name="orgs"></param>
        /// <returns></returns>
        async Task<MessageResult> HandlePRApplicationAsync(PurPRApplication prApplication, CreatePRApplicationRequest request, DepartmentDto org)
        {
            var orgs = await LazyServiceProvider.LazyGetService<ICommonService>().GetParentOrgs(org);
            var bu = orgs.FirstOrDefault(a => a.OrganizationType == DataverseEnums.Organization.OrganizationType.Bu);
            if (bu != null)
            {
                prApplication.ApplyUserBu = bu.Id;
                prApplication.ApplyUserBuName = bu.DepartmentName;
            }

            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            //成本中心
            if (request.CostCenter.HasValue)
            {
                var costcenters = await dataverseService.GetCostcentersAsync(request.CostCenter.ToString());
                var costcenter = costcenters.FirstOrDefault();
                if (costcenter == null)
                    return MessageResult.FailureResult($"未找到Id为{request.CostCenter}的成本中心数据");

                prApplication.CostCenterName = costcenter.Name;
                prApplication.CostCenterCode = costcenter.Code;
            }
            else
            {
                prApplication.CostCenterName = null;
                prApplication.CostCenterCode = null;
            }

            //预算
            if (request.BudgetId.HasValue)
            {
                var queryBudget = await LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>().GetQueryableAsync();
                var budget = await queryBudget.Include(a => a.MasterBudget).Select(a => new
                {
                    MasterBudget = new { a.MasterBudget.Id, a.MasterBudget.Code },
                    a.Id,
                    a.Code,
                    a.RegionId
                })
                .FirstOrDefaultAsync(a => a.Id == request.BudgetId);
                if (budget == null)
                    return MessageResult.FailureResult($"未找到Id为{request.BudgetId}的预算数据数据");

                //主预算信息
                prApplication.BudgetId = budget.MasterBudget.Id;
                prApplication.BudgetCode = budget.MasterBudget.Code;
                //子预算信息
                prApplication.SubBudgetId = budget.Id;
                prApplication.SubBudgetCode = budget.Code;
                prApplication.BudgetRegion = budget.RegionId;
                var regions = await dataverseService.GetDistrict(budget.RegionId.ToString());
                if (regions.Any())
                    prApplication.BudgetRegionName = regions.First().Name;
            }
            else
            {
                prApplication.BudgetId = null;
                prApplication.BudgetCode = null;
                //子预算信息
                prApplication.SubBudgetId = null;
                prApplication.SubBudgetCode = null;
                prApplication.BudgetRegion = null;
                prApplication.BudgetRegionName = null;
            }

            //被代理人
            if (request.AgentId.HasValue)
            {
                var queryUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
                var user = await queryUser.Select(a => new { a.Id, a.Name, a.Email }).FirstOrDefaultAsync(a => a.Id == request.AgentId);
                if (user == null)
                    return MessageResult.FailureResult($"未找到Id为{request.AgentId}的被代理人数据");

                prApplication.AgentIdName = user.Name;
                prApplication.AgentEmail = user.Email;
            }
            else
            {
                prApplication.AgentIdName = null;
                prApplication.AgentEmail = null;
            }

            //主持人
            if (request.HostVendorId.HasValue)
            {
                var queryBpcsAvm = await LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync();
                var queryBpcsPmfvm = await LazyServiceProvider.LazyGetService<IBpcsPmfvmRepository>().GetQueryableAsync();

                var vendor = await queryBpcsAvm.Where(a => a.Id == request.HostVendorId)
                    .GroupJoin(queryBpcsPmfvm, a => new { Company = a.Vcmpny, a.Vendor }, a => new { Company = a.Vmcmpy, Vendor = a.Vnderx }, (a, b) => new { Avm = a, Pmfvms = b })
                    .SelectMany(a => a.Pmfvms.DefaultIfEmpty(), (a, b) => new { a.Avm.Vndnam, b.Vemlad })
                    .FirstOrDefaultAsync();
                if (vendor == null)
                    return MessageResult.FailureResult($"未找到Id为{request.HostVendorId}的主持人数据");

                prApplication.HostVendorIdName = vendor.Vndnam;
                prApplication.HostVendorEmail = vendor?.Vemlad?.Trim();
            }
            else
            {
                prApplication.HostVendorIdName = null;
                prApplication.HostVendorEmail = null;
            }

            //消费大类
            if (request.ExpenseType.HasValue)
            {
                var consumeCategories = await dataverseService.GetConsumeCategoryAsync(request.ExpenseType.ToString());
                var consumeCategory = consumeCategories.FirstOrDefault();
                if (consumeCategory == null)
                    return MessageResult.FailureResult($"未找到Id为{request.ExpenseType}的消费大类数据");

                prApplication.ExpenseTypeCode = consumeCategory.Code;
                prApplication.ExpenseTypeName = consumeCategory.Name;
            }
            else
            {
                prApplication.ExpenseTypeCode = null;
                prApplication.ExpenseTypeName = null;
            }

            //产品
            if (request.ProductIds?.Any() == true)
            {
                var listProducts = new List<string>();
                foreach (var item in request.ProductIds)
                {
                    var ps = await dataverseService.GetProductsAsync(item);
                    if (!ps.Any())
                        return MessageResult.FailureResult($"未找到Id为{item}的产品数据");

                    listProducts.AddRange(ps.Select(a => a.Name));
                }
                prApplication.ProductIds = request.ProductIds.JoinAsString(",");
                prApplication.ProductIdsName = listProducts.JoinAsString(",");
            }
            else
            {
                prApplication.ProductIds = null;
                prApplication.ProductIdsName = null;
            }

            //公司
            if (request.CompanyId.HasValue)
            {
                var companies = await dataverseService.GetCompanyList(request.CompanyId.ToString());
                var company = companies.FirstOrDefault();
                if (company == null)
                    return MessageResult.FailureResult($"未找到Id为{request.CompanyId}的公司数据");

                prApplication.CompanyCode = company.CompanyCode;
                prApplication.CompanyIdName = company.CompanyName;
                prApplication.CompanyShortName = company.AbbrCode;
            }
            else
            {
                prApplication.CompanyCode = null;
                prApplication.CompanyIdName = null;
                prApplication.CompanyShortName = null;
            }

            //币种
            if (!string.IsNullOrEmpty(request.Currency))
            {
                var currencies = await dataverseService.GetCompanyCurrencyList();
                var currency = currencies.FirstOrDefault(a => a.CompanyId == request.CompanyId && string.Equals(a.Code, request.Currency, StringComparison.CurrentCultureIgnoreCase));
                if (currency == null)
                    return MessageResult.FailureResult($"未找到Code为{request.Currency}的币种数据");

                prApplication.CurrencySymbol = currency.CurrencySymbol;
                prApplication.PlanRate = currency.PlanRate;
                prApplication.Rate = currency.PlanRate + request.ExpectedFloatRate;
            }
            else
            {
                prApplication.CurrencySymbol = null;
                prApplication.PlanRate = 0;
                prApplication.ExpectedFloatRate = 0;
                prApplication.Rate = 0;
            }

            prApplication.Hospitals = request.Hospitals?.JoinAsString(",");
            if (request.Hospitals != null && request.Hospitals.Any())
            {
                var hospitals = await dataverseService.GetAllHospitals(stateCode: null);
                prApplication.HospitalsName = hospitals.Where(a => request.Hospitals.Contains(a.Id)).Select(a => a.Name)?.JoinAsString(",");
            }
            prApplication.HospitalDepartments = request.HospitalDepartments?.JoinAsString(",");
            if (request.HospitalDepartments != null && request.HospitalDepartments.Any())
            {
                var departments = await dataverseService.GetAllDepartments(stateCode: null);
                prApplication.HospitalDepartmentsName = departments.Where(a => request.HospitalDepartments.Contains(a.Id)).Select(a => a.Name)?.JoinAsString(",");
            }

            prApplication.SupportFiles = request.SupportFiles?.Select(a => a.AttachmentId).JoinAsString(",");
            prApplication.ApprovalEmails = request.ApprovalEmails?.Select(a => a.AttachmentId).JoinAsString(",");

            var totalAmount = request.DetailItems?.Sum(a => a.Quantity * a.UnitPrice) ?? 0;
            prApplication.TotalAmount = totalAmount;
            prApplication.TotalAmountRMB = totalAmount * (decimal)prApplication.Rate;
            //记录变动差额
            prApplication.SavingAmount = totalAmount - prApplication.TotalAmount;

            return MessageResult.SuccessResult(prApplication);
        }

        /// <summary>
        /// 处理PR申请明细数据
        /// </summary>
        /// <param name="prApplication"></param>
        /// <param name="applicationDetailRequests"></param>
        /// <returns></returns>
        async Task<MessageResult> HandlePRApplicationDetailAsync(PurPRApplication prApplication, IEnumerable<CreateUpdatePRApplicationDetailRequest> applicationDetailRequests)
        {
            List<PurPRApplicationDetail> originalPrDetails = null, newPrDetails = null;
            if (applicationDetailRequests?.Any() == true)
            {
                var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
                var queryPrDetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
                var prDetailRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>();
                var prDetailBackupVendorRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationDetailBackupVendorRepository>();
                var queryBpcsAvm = await LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync();
                var queryVendor = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();
                var queryVendorPersonal = await LazyServiceProvider.LazyGetService<IVendorPersonalRepository>().GetQueryableAsync();
                var queryFinancial = await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync();
                var queryBpcsAvt = await LazyServiceProvider.LazyGetService<IBpcsAvtRepository>().GetQueryableAsync();
                var queryBpcsPmfvm = await LazyServiceProvider.LazyGetService<IBpcsPmfvmRepository>().GetQueryableAsync();

                var vendorIds = applicationDetailRequests.Where(a => a.VendorId.HasValue).Select(a => a.VendorId.Value).ToArray();
                var executorIds = applicationDetailRequests.Where(a => a.Executor.HasValue).Select(a => a.Executor.Value).ToArray();
                originalPrDetails = queryPrDetail.Where(a => a.PRApplicationId == prApplication.Id).ToList();
                var originalBackupVendors = await prDetailBackupVendorRepository.GetListAsync(a => a.PRApplicationId == prApplication.Id);

                var vendors = queryBpcsAvm.Where(a => vendorIds.Contains(a.Id))
                    .GroupJoin(queryFinancial, a => a.FinaId, a => a.Id, (a, b) => new { Avm = a, Fins = b })
                    .SelectMany(a => a.Fins.DefaultIfEmpty(), (a, b) => new { a.Avm, Fin = b })
                    .GroupJoin(queryVendor, a => a.Fin.VendorId, a => a.Id, (a, b) => new { a.Avm, a.Fin, Vendors = b })
                    .SelectMany(a => a.Vendors.DefaultIfEmpty(), (a, b) => new { a.Avm, a.Fin, Vendor = b })
                    .GroupJoin(queryVendorPersonal, a => a.Vendor.Id, a => a.VendorId, (a, b) => new { a.Avm, a.Vendor, VendorExtensions = b })
                    .SelectMany(a => a.VendorExtensions.DefaultIfEmpty(), (a, b) => new { a.Avm, a.Vendor, VendorExtension = new { b.CardNo } })
                    .GroupJoin(queryBpcsAvt, a => new { Company = a.Avm.Vcmpny, Term = a.Avm.Vterms }, a => new { Company = a.Vcmpy, Term = a.Vterm }, (a, b) => new { a.Avm, a.Vendor, a.VendorExtension, Avts = b })
                    .SelectMany(a => a.Avts.DefaultIfEmpty(), (a, b) => new { Avm = new { a.Avm.Id, a.Avm.Vendor, a.Avm.Vmxcrt }, a.Vendor, VendorExtension = new { a.VendorExtension.CardNo }, Avt = new { b.Vterm, b.Vtmddy } })
                    .ToArray();

                var executors = queryBpcsAvm.Where(a => executorIds.Contains(a.Id))
                .GroupJoin(queryBpcsPmfvm, a => new { Company = a.Vcmpny, a.Vendor }, a => new { Company = a.Vmcmpy, Vendor = a.Vnderx }, (a, b) => new { Executor = a, ExecutorPmfvms = b })
                .SelectMany(a => a.ExecutorPmfvms.DefaultIfEmpty(), (a, b) => new { a.Executor.Id, a.Executor.Vndnam, b.Vemlad })
                .ToArray();

                newPrDetails = new List<PurPRApplicationDetail>(originalPrDetails);
                ////有原始明细行时，判断是否存在反冲的情况
                //if (newPrDetails.Any())
                //{
                //    var hedgePrDetailIds = applicationDetailRequests.Where(a => a.HedgePrDetailId.HasValue).Select(a => a.HedgePrDetailId.Value).ToArray();
                //    //如果存在反冲行
                //    if (hedgePrDetailIds.Any())
                //    {
                //        //验证是否已有后续流程
                //        var hasNextFlowPrDetailIds = await GetHasNextFlowPrDetailAsync(prApplication.Id);
                //        var intersect = hedgePrDetailIds.Intersect(hasNextFlowPrDetailIds);
                //        if (intersect.Any())
                //            return MessageResult.FailureResult($"以下明细行{originalPrDetails.Where(a => intersect.Contains(a.Id)).Select(a => a.RowNo).JoinAsString(",")}已产生后续流程，不允许反冲");
                //    }
                //}

                var index = newPrDetails.Any() ? newPrDetails.Max(a => a.RowNo) + 1 : 1;
                foreach (var item in applicationDetailRequests)
                {
                    PurPRApplicationDetail detail;
                    if (item.Id.HasValue)
                    {
                        //已提交后的数据，不允许编辑明细行，只能反冲
                        if (prApplication.ApplyTime.HasValue)
                            continue;

                        detail = newPrDetails.First(a => a.Id == item.Id);
                        detail = ObjectMapper.Map(item, detail);
                        await prDetailRepository.UpdateAsync(detail);
                    }
                    else
                    {
                        detail = ObjectMapper.Map<CreateUpdatePRApplicationDetailRequest, PurPRApplicationDetail>(item);
                        detail.RowNo = index++;
                        if (detail.HedgePrDetailId.HasValue)
                        {
                            detail.IsHedge = true;//反冲行
                            var hedgeDetail = newPrDetails.First(a => a.Id == detail.HedgePrDetailId.Value);
                            hedgeDetail.IsHedge = true;//被反冲行
                            await prDetailRepository.UpdateAsync(hedgeDetail);
                        }
                        newPrDetails.Add(detail);
                        await prDetailRepository.InsertAsync(detail);
                    }

                    //供应商信息
                    var avm = vendors.FirstOrDefault(a => a.Avm.Id == item.VendorId)?.Avm;
                    var avt = vendors.FirstOrDefault(a => a.Avm.Id == item.VendorId)?.Avt;
                    var vendor = vendors.FirstOrDefault(a => a.Avm.Id == item.VendorId)?.Vendor;
                    var vendorEx = vendors.FirstOrDefault(a => a.Avm.Id == item.VendorId)?.VendorExtension;
                    var executor = executors.FirstOrDefault(a => a.Id == item.Executor);

                    var totalAmount = item.UnitPrice * item.Quantity;
                    //未提交或者新增行时记录原始vendor信息
                    if (!prApplication.ApplyTime.HasValue || !item.Id.HasValue)
                    {
                        //修改时，记录之前的预计日期和Vendor信息
                        detail.OriginalEstimateDate = item.EstimateDate;
                        detail.OriginalVendorId = item.VendorId;
                        detail.OriginalVendorCode = avm?.Vendor.ToString();
                        detail.OriginalVendorName = item.VendorIdName;
                        detail.OriginalTotalAmount = totalAmount;
                        detail.OriginalTotalAmountRMB = totalAmount * (decimal)prApplication.Rate;
                    }

                    detail.PRApplicationId = prApplication.Id;
                    detail.VendorName = item.VendorIdName;

                    detail.TotalAmount = totalAmount;
                    detail.TotalAmountRMB = totalAmount * (decimal)prApplication.Rate;
                    detail.IsPrLate = item.EstimateDate < DateTime.Today;

                    detail.VendorCode = avm?.Vendor.ToString();
                    detail.TermCode = !string.IsNullOrEmpty(avt?.Vterm) && avt?.Vtmddy.HasValue == true ? $"{avt?.Vterm}_{avt?.Vtmddy}天" : null;
                    detail.TermCodeDays = avt?.Vtmddy?.ToString();

                    //讲者等级
                    DictionaryDto hcpLevel = null;
                    if (!string.IsNullOrEmpty(vendor?.SPLevel))
                    {
                        var hcpLevels = await dataverseService.GetDictionariesAsync(DictionaryType.HCPLevel);
                        hcpLevel = hcpLevels.FirstOrDefault(a => string.Equals(vendor.SPLevel, a.Code, StringComparison.CurrentCultureIgnoreCase));
                    }
                    detail.HcpLevelCode = hcpLevel?.Code;
                    detail.HcpLevelName = hcpLevel?.Name;

                    //所属医院
                    HospitalDto hospital = null;
                    if (vendor?.HospitalId != null)
                    {
                        var hospitals = await dataverseService.GetAllHospitals(vendor?.HospitalId.ToString());
                        hospital = hospitals.FirstOrDefault();
                    }
                    detail.Hospital = hospital?.Name;

                    //标准课室
                    OfficeDto department = null;
                    if (vendor?.StandardHosDepId != null)
                    {
                        var departments = await dataverseService.GetAllDepartments(vendor?.StandardHosDepId.ToString());
                        department = departments.FirstOrDefault();
                    }
                    detail.StandardDepartmentId = department?.Id;
                    detail.StandardDepartment = department?.Name;
                    detail.HosDepartment = vendor?.HosDepartment;
                    detail.CertificateCode = vendor?.CertificateCode;
                    detail.CardNo = avm?.Vmxcrt;

                    //费用性质
                    CostNatureDto costNature = null;
                    if (item.CostNature.HasValue)
                    {
                        var costNatures = await dataverseService.GetCostNatureAsync(item.CostNature.ToString());
                        costNature = costNatures.FirstOrDefault();
                    }
                    detail.CostNatureCode = costNature?.Code;
                    detail.CostNatureName = costNature?.Name;

                    //成本中心
                    detail.CostCenter = prApplication.CostCenter ?? Guid.Empty;
                    detail.CostCenterCode = prApplication.CostCenterCode;
                    detail.CostCenterName = prApplication.CostCenterName;

                    //城市主数据
                    string city = null;
                    if (item.CityId.HasValue)
                    {
                        var cities = await dataverseService.GetSpecialCitiesAsync(item.CityId.ToString());
                        city = cities.FirstOrDefault().CityNameCode;
                    }
                    detail.CityIdName = city;

                    //讲者身份
                    DictionaryDto identityType = null;
                    if (!string.IsNullOrEmpty(item.VendorType))
                    {
                        var identityTypes = await dataverseService.GetDictionariesAsync(DictionaryType.IdentityType);
                        identityType = identityTypes.FirstOrDefault(a => string.Equals(item.VendorType, a.Code, StringComparison.CurrentCultureIgnoreCase));
                    }
                    detail.VendorTypeName = identityType?.Name;

                    //幻灯片类型
                    DictionaryDto slideType = null;
                    if (!string.IsNullOrEmpty(item.SlideType))
                    {
                        var slideTypes = await dataverseService.GetDictionariesAsync(DictionaryType.SlideType);
                        slideType = slideTypes.FirstOrDefault(a => string.Equals(item.SlideType, a.Code, StringComparison.CurrentCultureIgnoreCase));
                    }
                    detail.SlideTypeName = slideType?.Name;

                    //执行人
                    detail.ExecutorName = executor?.Vndnam;
                    detail.ExecutorEmail = executor?.Vemlad?.Trim();

                    if (item.BackUpVendors?.Any() == true)
                    {
                        IEnumerable<PurPRApplicationDetailBackupVendor> backupVendors;
                        if (item.HedgePrDetailId.HasValue)
                            foreach (var bkv in item.BackUpVendors)
                                bkv.Price *= -1;

                        detail.BackUpVendors = JsonConvert.SerializeObject(item.BackUpVendors);
                        //先删掉之前的老数据
                        if (item.Id.HasValue)
                        {
                            await prDetailBackupVendorRepository.DeleteDirectAsync(a => a.PRApplicationDetailId == detail.Id);
                            backupVendors = originalBackupVendors.Where(a => a.PRApplicationDetailId == item.Id).ToArray();
                            //记录之前的备选讲者，后面计算psa会用到
                            item.OriginalBackUpVendors = ObjectMapper.Map<IEnumerable<PurPRApplicationDetailBackupVendor>, IEnumerable<CreateUpdatePRApplicationBackupVendor>>(backupVendors);
                        }
                        backupVendors = ObjectMapper.Map<IEnumerable<CreateUpdatePRApplicationBackupVendor>, IEnumerable<PurPRApplicationDetailBackupVendor>>(item.BackUpVendors);
                        foreach (var backupVendor in backupVendors)
                        {
                            backupVendor.PRApplicationId = prApplication.Id;
                            backupVendor.PRApplicationDetailId = detail.Id;
                            var amount = detail.Quantity * backupVendor.Price;
                            backupVendor.TotalAmount = amount;
                            backupVendor.TotalAmountRMB = amount * (decimal)prApplication.Rate;
                        }
                        await prDetailBackupVendorRepository.InsertManyAsync(backupVendors);
                    }
                    else
                    {
                        detail.BackUpVendors = null;
                        if (item.Id.HasValue)
                            await prDetailBackupVendorRepository.DeleteDirectAsync(a => a.PRApplicationDetailId == detail.Id);
                    }

                    //处理产品拆分信息
                    var messageResult = await HandlePRApplicationProductApportionmentAsync(prApplication, item.ProductApportionments, detail.Id);
                    if (!messageResult.Success)
                        return messageResult;
                }
            }

            return MessageResult.SuccessResult(new Tuple<List<PurPRApplicationDetail>, List<PurPRApplicationDetail>>(originalPrDetails, newPrDetails));
        }

        /// <summary>
        /// 处理产品拆分
        /// </summary>
        /// <param name="prApplication"></param>
        /// <param name="productApportionments"></param>
        /// <param name="prDetailId"></param>
        /// <returns></returns>
        async Task<MessageResult> HandlePRApplicationProductApportionmentAsync(PurPRApplication prApplication, IEnumerable<CreateUpdatePRApplicationProductApportionmentRequest> productApportionments, Guid? prDetailId = null)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var productApportionmentRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationProductApportionmentRepository>();
            var existsProductApportionments = await productApportionmentRepository.GetListAsync(a => a.PRApplicationId == prApplication.Id && a.PRApplicationDetailId == prDetailId);

            if (!string.IsNullOrEmpty(prApplication.ProductIds))
            {
                var products = prApplication.ProductIds.Split(",");
                //没有设置分摊的情况，默认一个产品100%
                if (products.Length == 1)
                {
                    var ps = await dataverseService.GetProductsAsync(products[0]);
                    if (!ps.Any())
                        return MessageResult.FailureResult($"未找到Id为{products[0]}的产品数据");
                    var product = ps.First();
                    //该产品是否是之前已经存在的
                    var productApportionment = existsProductApportionments.FirstOrDefault(a => a.ProductId == product.Id && a.PRApplicationDetailId == prDetailId);
                    //不存在则新增
                    if (productApportionment == null)
                        await productApportionmentRepository.InsertAsync(new PurPRApplicationProductApportionment { PRApplicationId = prApplication.Id, PRApplicationDetailId = prDetailId, ProductId = product.Id, ProductCode = product.Code, ProductName = product.Name, Ratio = 100 });
                    else
                    {
                        productApportionment.Ratio = 100;
                        await productApportionmentRepository.UpdateAsync(productApportionment);
                        //删除其他产品分摊信息
                        var others = existsProductApportionments.Where(a => a != productApportionment);
                        if (others.Any())
                            await productApportionmentRepository.DeleteManyAsync(others);
                    }
                }
                else//分摊的情况
                {
                    foreach (var item in productApportionments)
                    {
                        //判断产品是否有效
                        var ps = await dataverseService.GetProductsAsync(item.ProductId.ToString());
                        if (!ps.Any())
                            return MessageResult.FailureResult($"未找到Id为{item}的产品数据");

                        var existsProductApportionment = existsProductApportionments.FirstOrDefault(a1 => a1.ProductId == item.ProductId && a1.PRApplicationDetailId == prDetailId);
                        //在之前不存在的分摊数据作新增
                        if (existsProductApportionment == null)
                        {
                            var product = ps.First();
                            await productApportionmentRepository.InsertAsync(new PurPRApplicationProductApportionment { PRApplicationId = prApplication.Id, PRApplicationDetailId = prDetailId, ProductId = item.ProductId, ProductCode = product.Code, ProductName = product.Name, Ratio = item.Ratio });
                        }
                        else//已存在的产品分摊数据作修改
                        {
                            existsProductApportionment.Ratio = item.Ratio;
                            await productApportionmentRepository.UpdateAsync(existsProductApportionment);
                        }
                    }
                    //之前有现在没有的作删除
                    var forDeletes = existsProductApportionments.Where(a => !productApportionments.Any(a1 => a1.ProductId == a.ProductId));
                    await productApportionmentRepository.DeleteManyAsync(forDeletes);
                }
            }
            else
                await productApportionmentRepository.DeleteDirectAsync(a => a.PRApplicationId == prApplication.Id && a.PRApplicationDetailId == prDetailId);

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 会议支持的预计费用
        /// </summary>
        /// <param name="prApplication"></param>
        /// <param name="costItems"></param>
        /// <returns></returns>
        IEnumerable<PurPRApplicationCostItem> HandlePRApplicationCostItemAsync(PurPRApplication prApplication, IEnumerable<CreateUpdatePRApplicationCostItemRequest> costItems)
        {
            //会议支持的预计费用
            if (costItems != null)
            {
                foreach (var item in costItems)
                {
                    var costItem = ObjectMapper.Map<CreateUpdatePRApplicationCostItemRequest, PurPRApplicationCostItem>(item);
                    costItem.PRApplicationId = prApplication.Id;

                    yield return costItem;
                }
            }
        }

        /// <summary>
        /// Hcp旅行社会务费
        /// </summary>
        /// <param name="prApplication"></param>
        /// <param name="hcpTravelAgencyConferenceFees"></param>
        /// <returns></returns>
        IEnumerable<PurPRApplicationHcpTravelLodgingFee> HandlePRApplicationHcpTravelAgencyConferenceFeeAsync(PurPRApplication prApplication, IEnumerable<CreateUpdateHcpTravelAgencyConferenceFeeRequest> hcpTravelAgencyConferenceFees)
        {
            //Hcp旅行社会务费
            if (hcpTravelAgencyConferenceFees != null)
            {
                foreach (var item in hcpTravelAgencyConferenceFees)
                {
                    var hcpTravelFee = ObjectMapper.Map<CreateUpdateHcpTravelAgencyConferenceFeeRequest, PurPRApplicationHcpTravelLodgingFee>(item);
                    hcpTravelFee.PRApplicationId = prApplication.Id;

                    yield return hcpTravelFee;
                }
            }
        }

        /// <summary>
        /// 获取采购申请单详情
        /// </summary>
        /// <param name="id"></param>
        /// <param name="taskId"></param>
        /// <returns></returns>
        public async Task<GetPRApplicationResponse> GetPRApplicationAsync(Guid id, Guid? taskId = null)
        {
            var queryPr = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var queryPrDetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var queryPrCostItem = await LazyServiceProvider.LazyGetService<IPurPRApplicationCostItemRepository>().GetQueryableAsync();
            var queryPrHcpTravelLodgingFee = await LazyServiceProvider.LazyGetService<IPurPRApplicationHcpTravelLodgingFeeRepository>().GetQueryableAsync();
            var queryPrProductApportionment = await LazyServiceProvider.LazyGetService<IPurPRApplicationProductApportionmentRepository>().GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var queryMeetingSettlement = (await LazyServiceProvider.LazyGetService<IInteOnlineMeetingRepository>().GetQueryableAsync()).AsNoTracking();

            string meetingPrApplicationCode = string.Empty;
            PurPRApplication prApplication = null;
            PurPRApplicationDetail[] prDetailDatas = [];
            PurPRApplicationCostItem[] prCostItems = [];
            PurPRApplicationHcpTravelLodgingFee[] prHcpTravelFees = [];
            PurPRApplicationProductApportionment[] prProductApportionments = [];

            var getPrDetailRelatedsTask = Task.Run(() =>
            {
                //采购主数据
                var prData = queryPr.Where(a => a.Id == id)
                    .GroupJoin(queryPr, a => a.MainMeetingPR, a => a.Id, (a, b) => new { Pr = a, MeetingPrs = b })
                    .SelectMany(a => a.MeetingPrs.DefaultIfEmpty(), (a, b) => new { a.Pr, MeetingPr = new { b.ApplicationCode } })
                    .FirstOrDefault();

                if (prData == null)
                    return;

                meetingPrApplicationCode = prData.MeetingPr.ApplicationCode;
                prApplication = prData.Pr;

                //pr明细行数据
                prDetailDatas = queryPrDetail.Where(a => a.PRApplicationId == id).OrderBy(a => a.RowNo).ToArray();
                //会议支持的预计费用
                prCostItems = queryPrCostItem.Where(a => a.PRApplicationId == id).ToArray();
                //Hcp旅行社会务费
                prHcpTravelFees = queryPrHcpTravelLodgingFee.Where(a => a.PRApplicationId == id).ToArray();
                //产品拆分
                prProductApportionments = queryPrProductApportionment.Where(a => a.PRApplicationId == id).ToArray();
            });

            var getWorkflowTasks = Task.FromResult<IEnumerable<WorkflowTaskDto>>(null);
            //获取当前审批任务状态
            if (taskId.HasValue)
                getWorkflowTasks = dataverseService.GetWorkflowTaskAsync(id, taskId.Value);

            Task.WaitAll(getPrDetailRelatedsTask, getWorkflowTasks);

            if (prApplication == null)
                return default;

            var response = ObjectMapper.Map<PurPRApplication, GetPRApplicationResponse>(prApplication);

            //非草稿和关闭的单子都可以撤回,已经撤回退回的
            PurPRApplicationStatus[] CanRecallStatus = [PurPRApplicationStatus.Draft, PurPRApplicationStatus.Closed, PurPRApplicationStatus.RejectedBack];
            response.CanRecall = !CanRecallStatus.Contains(prApplication.Status);
            //审批通过或等待关闭状态时，说明审批流已结束，但是流程仍然可以使用特殊撤回进行撤回
            response.SpecialRecall = prApplication.Status == PurPRApplicationStatus.Approved || prApplication.Status == PurPRApplicationStatus.WaitForClose;
            //如果是使用特殊撤回的数据，就可以使用特殊作废
            var specialRecalls = await GetLatestWorkflowTasksAsync(prApplication.Id);
            var specialRecall = specialRecalls.FirstOrDefault(a => a.FormId == prApplication.Id);
            response.SpecialDeprecate = specialRecall == null ? false : specialRecall.Status == ApprovalOperation.Recall && specialRecall.InstanceId == Guid.Empty;
            //是否可以修订
            response.CanAmend = getWorkflowTasks.Result?.FirstOrDefault(a => a.ApprovalPowerAppStatus == ApprovalPowerAppStatus.PendingForApproval)?.StepNumber == WorkflowConsts.PRWorkflowConsts.PrFinApprovalStepCode;

            #region 主表数据

            response.Applicant = prApplication.ApplyUserIdName;
            if (prApplication.Status == PurPRApplicationStatus.Approved)
            {
                if (prDetailDatas.Where(a => a.PayMethod == PayMethods.AR).Any(a => a.IsVendorConfimed != true))
                    response.Status.Add(new KeyValuePair<PurPRApplicationStatus, string>(PurPRApplicationStatus.VendorConfirmed, EnumUtil.GetDescription(PurPRApplicationStatus.VendorConfirmed)));

                if (prDetailDatas.Where(a => a.PayMethod == PayMethods.AP).Any(a => a.PushFlag != PushFlagEnum.Pushed))
                    response.Status.Add(new KeyValuePair<PurPRApplicationStatus, string>(PurPRApplicationStatus.PushToPurchase, EnumUtil.GetDescription(PurPRApplicationStatus.PushToPurchase)));

                //是否可以供应商确认
                response.CanConfirm = (prApplication.IsEsignUsed == true ? prApplication.MeetingStatus == OnlineMeetingStatus.OmSettledPushed : true) && response.Status.Any(a => a.Key == PurPRApplicationStatus.VendorConfirmed);
            }
            else
                response.Status.Add(new KeyValuePair<PurPRApplicationStatus, string>(prApplication.Status, EnumUtil.GetDescription(prApplication.Status)));

            response.CanDeprecate = prApplication.IsEsignUsed == true ? string.IsNullOrEmpty(prApplication.MeetingStatus) || prApplication.MeetingStatus == OnlineMeetingStatus.NexBpmPushed : prApplication.Status == PurPRApplicationStatus.RejectedBack;

            //预算
            if (prApplication.SubBudgetId.HasValue)
                response.Budget = await GetSubBudgetInfoAsync(prApplication.SubBudgetId.Value);

            //被代理人
            response.AgentIdName = prApplication.AgentIdName;
            //主持人
            response.HostVendorIdName = prApplication.HostVendorIdName;
            //产品
            response.ProductIds = prApplication.ProductIds?.Split(",", StringSplitOptions.RemoveEmptyEntries);
            response.ProductNames = prApplication.ProductIdsName;
            //活动类型
            response.ActiveTypeName = response.ActiveType;
            //项目类型
            if (!string.IsNullOrEmpty(response.ProjectType))
            {
                var projectTypes = await dataverseService.GetDictionariesAsync(DictionaryType.ProjectType, null);
                response.ProjectTypeName = projectTypes.FirstOrDefault(a => a.Code == response.ProjectType)?.Name;
            }
            //活动覆盖医院
            if (!string.IsNullOrEmpty(prApplication.Hospitals))
            {
                var ids = prApplication.Hospitals.Split(",", StringSplitOptions.RemoveEmptyEntries);
                response.Hospitals = prApplication.Hospitals.Split(",", StringSplitOptions.RemoveEmptyEntries).Select(Guid.Parse);
                //response.HospitalNames = ids.Select(a =>
                //{
                //    var hospital = dataverseService.GetAllHospitals(a, stateCode: null).GetAwaiter().GetResult();
                //    return hospital.FirstOrDefault()?.Name;
                //}).JoinAsString(",");
            }
            response.HospitalNames = prApplication.HospitalsName;
            //活动覆盖科室
            if (!string.IsNullOrEmpty(prApplication.HospitalDepartments))
            {
                var ids = prApplication.HospitalDepartments.Split(",", StringSplitOptions.RemoveEmptyEntries);
                response.HospitalDepartments = prApplication.HospitalDepartments.Split(",", StringSplitOptions.RemoveEmptyEntries).Select(Guid.Parse);
                //response.HospitalDepartmentNames = ids.Select(a =>
                //{
                //    var dept = dataverseService.GetAllDepartments(a, null).GetAwaiter().GetResult();
                //    return dept.FirstOrDefault()?.Name;
                //}).JoinAsString(",");
            }
            response.HospitalDepartmentNames = prApplication.HospitalDepartmentsName;
            response.MeetingStatus = prApplication.MeetingStatus;
            //会议类型
            if (!string.IsNullOrEmpty(response.MeetingType))
            {
                var meetingTypes = await dataverseService.GetDictionariesAsync(DictionaryType.MeetingType, null);
                response.MeetingTypeName = meetingTypes.FirstOrDefault(a => a.Code == response.MeetingType)?.Name;
            }
            //系列会类型
            if (!string.IsNullOrEmpty(prApplication.SerialMeetingType))
            {
                var serialMeetingType = await dataverseService.GetDictionariesAsync(DictionaryType.SerialMeetingType, null);
                response.SerialMeetingTypeName = serialMeetingType.FirstOrDefault(a => a.Code == prApplication.SerialMeetingType)?.Name;
            }
            //主会场Pr
            response.MainMeetingPRName = meetingPrApplicationCode;
            //主办方性质
            if (!string.IsNullOrEmpty(response.OrganizerNature))
            {
                var organizerNatures = await dataverseService.GetDictionariesAsync(DictionaryType.OrganizerNature, null);
                response.OrganizerNatureName = organizerNatures.FirstOrDefault(a => a.Code == response.OrganizerNature)?.Name;
            }
            //赞助类型
            if (!string.IsNullOrEmpty(response.SponsorshipType))
            {
                var sponsorshipTypes = await dataverseService.GetDictionariesAsync(DictionaryType.SponsorshipType, null);
                response.SponsorshipTypeName = sponsorshipTypes.FirstOrDefault(a => a.Code == response.SponsorshipType)?.Name;
            }
            //市场调研主导方
            if (!string.IsNullOrEmpty(response.MarketResearchLeader))
            {
                var marketResearchLeaders = await dataverseService.GetDictionariesAsync(DictionaryType.MarketResearchLeader, null);
                response.MarketResearchLeaderName = marketResearchLeaders.FirstOrDefault(a => a.Code == response.MarketResearchLeader)?.Name;
            }
            //产品拆分信息
            if (prProductApportionments.Any())
                response.ProductApportionments = prProductApportionments.Where(a => !a.PRApplicationDetailId.HasValue).Select(a => new CreateUpdatePRApplicationProductApportionmentRequest { ProductId = a.ProductId, ProductName = a.ProductName, Ratio = a.Ratio });

            #endregion

            #region 明细行数据

            //采购明细
            if (prDetailDatas.Length > 0)
            {
                var settlements = queryMeetingSettlement.Where(a => a.PRApplicationId == prApplication.Id && prDetailDatas.Select(x => x.Id).Contains(a.PRDetailId)).ToList();
                var listDetail = new List<GetPRApplicationDetailResponse>();
                foreach (var item in prDetailDatas)
                {
                    var detail = ObjectMapper.Map<PurPRApplicationDetail, GetPRApplicationDetailResponse>(item);
                    if (detail.PayMethod.HasValue)
                        detail.PayMethodName = EnumUtil.GetDescription(detail.PayMethod);
                    detail.EstimateDate = item.EstimateDate?.ToString("yyyy-MM-dd");
                    detail.VendorIdName = item.VendorName;
                    detail.PaymentTerm = item.TermCode;
                    detail.HcpLevel = item.HcpLevelName;
                    //成本中心
                    detail.Costcenter = item.CostCenterName;
                    //所得税率
                    if (!string.IsNullOrEmpty(detail.TaxRate))
                    {
                        var rates = await dataverseService.GetDictionariesAsync(DictionaryType.TaxRate, null);
                        detail.TaxRateName = rates.FirstOrDefault(a => a.Code == detail.TaxRate)?.Name;
                    }
                    //备选讲者
                    if (!string.IsNullOrEmpty(item.BackUpVendors))
                    {
                        detail.BackUpVendors = JsonConvert.DeserializeObject<IEnumerable<CreateUpdatePRApplicationBackupVendor>>(item.BackUpVendors);
                        detail.BackUpVendorsName = detail.BackUpVendors?.Select(a => a.Text).JoinAsString(",");
                    }
                    //产品拆分信息
                    if (prProductApportionments.Any())
                    {
                        //正常明细行
                        if (item.HedgePrDetailId == null)
                            detail.ProductApportionments = prProductApportionments.Where(a => a.PRApplicationDetailId == detail.Id).Select(a => new CreateUpdatePRApplicationProductApportionmentRequest { ProductId = a.ProductId, ProductName = a.ProductName, Ratio = a.Ratio });
                        else//反冲行
                            detail.ProductApportionments = prProductApportionments.Where(a => a.PRApplicationDetailId == item.HedgePrDetailId).Select(a => new CreateUpdatePRApplicationProductApportionmentRequest { ProductId = a.ProductId, ProductName = a.ProductName, Ratio = a.Ratio });
                    }

                    //是否可以更改供应商
                    detail.CanExchangeVendor = (prApplication.IsEsignUsed == true ? item.MeetingStatus != OnlineMeetingStatus.OmSettledPushed : true) && response.Status.Any(a => a.Key == PurPRApplicationStatus.VendorConfirmed);
                    //是否可以反冲
                    detail.CanHedge = prApplication.IsEsignUsed == true ? string.IsNullOrEmpty(item.MeetingStatus) || item.MeetingStatus == OnlineMeetingStatus.NexBpmPushed : true;

                    CostNatureDto costNature = null;
                    if (item.CostNature.HasValue)
                    {
                        var costNatures = await dataverseService.GetCostNatureAsync(item.CostNature.ToString());
                        costNature = costNatures.FirstOrDefault();
                    }
                    detail.PushOnlineMeeting = costNature == null ? false : (costNature.PushOnlineMeeting.HasValue ? costNature.PushOnlineMeeting.Value : false);

                    detail.SettlementAmount = settlements.Where(a => a.PRDetailId == item.Id).OrderByDescending(a => a.CreationTime).FirstOrDefault()?.PayAmount;

                    listDetail.Add(detail);
                }

                response.DetailItems = listDetail;
            }

            #endregion

            //会议支持的预计费用
            if (prCostItems.Length > 0)
            {
                var listCostItems = new List<GetPRApplicationCostItemResponse>();
                foreach (var item in prCostItems)
                {
                    var costItem = ObjectMapper.Map<PurPRApplicationCostItem, GetPRApplicationCostItemResponse>(item);
                    var supportItems = await dataverseService.GetDictionariesAsync(DictionaryType.ConferenceFeeItem, null);
                    costItem.SupportItemName = supportItems.FirstOrDefault(a => a.Code == costItem.SupportItem)?.Name;
                    listCostItems.Add(costItem);
                }
                response.CostItems = listCostItems;
            }

            //Hcp旅行社会务费
            if (prHcpTravelFees.Length > 0)
            {
                var listHcpTravelFees = new List<GetPRApplicationHcpTravelAgencyConferenceFeeResponse>();
                foreach (var item in prHcpTravelFees)
                {
                    var travelFee = ObjectMapper.Map<PurPRApplicationHcpTravelLodgingFee, GetPRApplicationHcpTravelAgencyConferenceFeeResponse>(item);
                    var travelFees = await dataverseService.GetDictionariesAsync(DictionaryType.HcpTravelAgencyConferenceFeeType, null);
                    travelFee.HcpTravelAgencyConferenceFeeTypeName = travelFees.FirstOrDefault(a => a.Code == item.HcpTravelAgencyConferenceFeeType)?.Name;
                    listHcpTravelFees.Add(travelFee);
                }
                response.HcpTravelAgencyConferenceFees = listHcpTravelFees;
            }

            //支持文档
            if (!string.IsNullOrEmpty(prApplication.SupportFiles))
            {
                var attachmentIds = prApplication.SupportFiles.Split(",", StringSplitOptions.RemoveEmptyEntries).Select(Guid.Parse).ToArray();
                response.SupportFiles = await LazyServiceProvider.LazyGetService<IAttachmentService>().GetAttachmentsAsync(attachmentIds);
            }

            //支持文档
            if (!string.IsNullOrEmpty(prApplication.ApprovalEmails))
            {
                var attachmentIds = prApplication.ApprovalEmails.Split(",", StringSplitOptions.RemoveEmptyEntries).Select(Guid.Parse).ToArray();
                response.ApprovalEmails = await LazyServiceProvider.LazyGetService<IAttachmentService>().GetAttachmentsAsync(attachmentIds);
            }

            //PO推送时的补录文件
            MessageResult additionFilesResult = await GetAdditionFilesAsync(prApplication.Id);
            if (additionFilesResult.Success)
            {
                var responseDto = additionFilesResult.Data as AdditionFilesResponseDto;
                response.POPushAdditionalFiles = responseDto.AttachmentInformation;
            }

            var applyUserDeptId = response.ApplyUserDept;
            //var isParentOrgJiaXin = await LazyServiceProvider.LazyGetService<ICommonService>().IsSubOrganization(applyUserDeptId);
            //if (isParentOrgJiaXin)
            //{
            //    response.ParentOrgJiaXin = true;
            //}
            //else
            //{
            //    response.ParentOrgJiaXin = false;
            //}
            return response;
        }

        /// <summary>
        /// 修改采购申请单
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> UpdatePRApplicationAsync(UpdatePRApplicationRequest request)
        {
            if (!request.Id.HasValue)
                return MessageResult.FailureResult($"申请单Id不能为空");

            var prApplicationRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>();
            var prApplication = await prApplicationRepository.FindAsync(request.Id.Value);
            if (prApplication == null)
                return MessageResult.FailureResult($"未找到Id为{request.Id}的采购申请单");

            if (prApplication.Status != PurPRApplicationStatus.Draft && prApplication.Status != PurPRApplicationStatus.RejectedBack)
                return MessageResult.FailureResult("数据状态不是草稿/退回/撤回，不能修改/提交");

            #region 避免代理人重新提交申请单时，将原始发起人的部门覆盖

            //只能在Draft状态时，才允许修改发起部门
            if (prApplication.Status != PurPRApplicationStatus.Draft)
            {
                request.ApplyUserDept = prApplication.ApplyUserDept;
                request.ApplyUserDeptName = prApplication.ApplyUserDeptName;
            }

            #endregion

            var orgs = await LazyServiceProvider.LazyGetService<IDataverseService>().GetOrganizations(request.ApplyUserDept.ToString());
            if (!orgs.Any())
                return MessageResult.FailureResult("未找到申请人部门信息");

            prApplication = ObjectMapper.Map(request, prApplication);
            var messageResult = await HandlePRApplicationAsync(prApplication, request, orgs.First());
            if (!messageResult.Success)
                return messageResult;

            prApplication = messageResult.Data as PurPRApplication;
            await prApplicationRepository.UpdateAsync(prApplication);

            //处理主表产品拆分信息
            messageResult = await HandlePRApplicationProductApportionmentAsync(prApplication, request.ProductApportionments);
            if (!messageResult.Success)
                return messageResult;

            //采购明细项
            messageResult = await HandlePRApplicationDetailAsync(prApplication, request.DetailItems);
            if (!messageResult.Success)
                return messageResult;

            //item1为数据库原始数据，item2为原始数据+新增数据
            var prApplicationDetailData = messageResult.Data as Tuple<List<PurPRApplicationDetail>, List<PurPRApplicationDetail>>;
            var prApplicationDetailRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>();
            if (prApplicationDetailData.Item2?.Any() == true)
            {
                //删除不存在的行
                var needDeleteDatas = prApplicationDetailData.Item1.Where(a => !request.DetailItems.Any(a1 => a1.Id == a.Id)).ToArray();
                if (needDeleteDatas.Any())
                {
                    var needDeleteIds = needDeleteDatas.Select(a => a.Id).ToArray();
                    //提交之后不能删除明细行数据，只能反冲
                    if (!prApplication.ApplyTime.HasValue)
                    {
                        await prApplicationDetailRepository.DeleteManyAsync(needDeleteDatas);
                        await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailBackupVendorRepository>().DeleteDirectAsync(a => a.PRApplicationId == prApplication.Id && needDeleteIds.Contains(a.PRApplicationDetailId));
                        await LazyServiceProvider.LazyGetService<IPurPRApplicationProductApportionmentRepository>().DeleteDirectAsync(a => a.PRApplicationId == prApplication.Id && needDeleteIds.Contains(a.PRApplicationDetailId ?? Guid.Empty));
                    }
                    prApplicationDetailData.Item2.RemoveAll(a => needDeleteIds.Contains(a.Id));
                }
            }
            else
            {
                await prApplicationDetailRepository.DeleteAsync(a => a.PRApplicationId == prApplication.Id);
                await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailBackupVendorRepository>().DeleteDirectAsync(a => a.PRApplicationId == prApplication.Id);
                await LazyServiceProvider.LazyGetService<IPurPRApplicationProductApportionmentRepository>().DeleteDirectAsync(a => a.PRApplicationId == prApplication.Id);
            }

            //会议支持的预计费用
            var prApplicationCostItemRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationCostItemRepository>();
            await prApplicationCostItemRepository.DeleteAsync(a => a.PRApplicationId == prApplication.Id);
            var costItems = HandlePRApplicationCostItemAsync(prApplication, request.CostItems);
            if (costItems?.Any() == true)
                await prApplicationCostItemRepository.InsertManyAsync(costItems);

            ///Hcp旅行社会务费
            var prApplicationHcpTravelLodgingFeeRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationHcpTravelLodgingFeeRepository>();
            await prApplicationHcpTravelLodgingFeeRepository.DeleteAsync(a => a.PRApplicationId == prApplication.Id);
            var conferenceFees = HandlePRApplicationHcpTravelAgencyConferenceFeeAsync(prApplication, request.HcpTravelAgencyConferenceFees);
            if (conferenceFees?.Any() == true)
                await prApplicationHcpTravelLodgingFeeRepository.InsertManyAsync(conferenceFees);

            await CurrentUnitOfWork.SaveChangesAsync();

            return MessageResult.SuccessResult(new Tuple<PurPRApplication, IEnumerable<PurPRApplicationDetail>, IEnumerable<PurPRApplicationDetail>>(prApplication, prApplicationDetailData.Item1, prApplicationDetailData.Item2));
        }

        /// <summary>
        /// 删除采购申请单
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<MessageResult> DeletePRApplicationAsync(Guid id)
        {
            var repository = LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>();
            var prApplication = await repository.FindAsync(id);
            if (prApplication == null)
                return MessageResult.FailureResult($"未找到Id为{id}的采购申请单");

            if (prApplication.Status != PurPRApplicationStatus.Draft)
                return MessageResult.FailureResult($"只能删除草稿状态的申请单");

            await repository.DeleteAsync(prApplication);
            await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().DeleteAsync(a => a.PRApplicationId == prApplication.Id);
            await LazyServiceProvider.LazyGetService<IPurPRApplicationCostItemRepository>().DeleteAsync(a => a.PRApplicationId == prApplication.Id);
            await LazyServiceProvider.LazyGetService<IPurPRApplicationHcpTravelLodgingFeeRepository>().DeleteAsync(a => a.PRApplicationId == prApplication.Id);

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 校验采购申请数据
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        async Task<MessageResult> ValidateRequiredAsync(CreatePRApplicationRequest request, PurPRApplication prApplication, IEnumerable<PurPRApplicationDetail> detailItems)
        {
            if (!request.CostCenter.HasValue)
                return MessageResult.FailureResult("成本中心不能为空");
            if (!request.BudgetId.HasValue)
                return MessageResult.FailureResult("预算不能为空");
            if (!request.ExpenseType.HasValue)
                return MessageResult.FailureResult("消费大类不能为空");
            if (request.ProductIds == null || !request.ProductIds.Any())
                return MessageResult.FailureResult("产品不能为空");
            if (!request.CompanyId.HasValue)
                return MessageResult.FailureResult("公司不能为空");
            if (string.IsNullOrEmpty(request.Currency))
                return MessageResult.FailureResult("币种不能为空");
            var queryBpcsAvm = (await LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync()).AsNoTracking();
            var queryVendor = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();
            var queryFinancial = await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync();

            //校验活动编号
            if (!string.IsNullOrEmpty(request.ActiveNo))
            {
                var pattern = @"^(?<prefix>[MS])(?<date>\d{8})(?<code>\d{3})$";
                var match = Regex.Match(request.ActiveNo, pattern);
                if (!match.Success)
                    return MessageResult.FailureResult("会议编号必须为M或者S开头，加后面11位纯数字");

                if (!request.IgnoreActiveNumberDuplication)
                {
                    //判断是否重复
                    var queryPr = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
                    var isExists = queryPr.Any(a => a.ActiveNo == request.ActiveNo && a.Id != prApplication.Id);
                    if (isExists)
                        return MessageResult.SuccessResult(messageModel: MessageModelBase.ActiveNumberDuplication);
                }
            }

            //2566 若消费大类+费用性质下配置了讲者类型，则此处【讲者类型】及【服务时长/次数/字数】为必填
            //判断是否特定的消费大类
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var compensations = await dataverseService.GetCompensationAsync();
            var specificConsumes = compensations.Where(a => a.Consume == prApplication.ExpenseType);
            //如果是特殊的消费大类，则判断讲者身份类型是否有值
            if (specificConsumes.Any())
            {
                //3157【采购申请】ER类型不限制填写服务时长和讲者身份（不进行计酬限制），只有AR+讲者+有计酬的费用性质才需要验证--BE
                var arDetails = detailItems.Where(a => a.PayMethod == PayMethods.AR).ToList();
                if (arDetails.Any())
                {
                    #region
                    //常规讲者
                    var vendorIds = arDetails.Where(x => x.VendorId.HasValue).Select(x => x.VendorId.Value).ToList();
                    var vendors = queryBpcsAvm.Where(s => vendorIds.Contains(s.Id))
                        .Join(queryFinancial, a => a.FinaId, a => a.Id, (a, b) => new { Avm = a, Fin = b })
                        .Join(queryVendor.Where(a => a.VendorType == VendorTypes.HCPPerson), a => a.Fin.VendorId, a => a.Id, (a, b) => new { a.Avm, Vendor = b })
                        .ToList();
                    #endregion
                    foreach (var item in arDetails)
                    {
                        //跳过对冲行
                        if (item.HedgePrDetailId.HasValue)
                            continue;
                        var vendor = vendors.Where(a => a.Avm.Id == item.VendorId).FirstOrDefault();
                        //讲者 3159 针对于AR付款方式的行：只有AR+讲者（NT+VendorType：1 / NHIV）+有计酬的费用性质才需要验证（还需补充供应商类型为讲者时的限制）--YTW 20250317
                        var isHCP = vendor?.Avm?.Vtype == "NHIV" || (vendor?.Avm?.Vtype == "NT" && vendor?.Vendor?.VendorType == VendorTypes.HCPPerson);
                        if (specificConsumes.Any(a => a.Costnature == item.CostNature) && isHCP)
                        {
                            if (string.IsNullOrEmpty(item.VendorType) || !item.ServiceDuration.HasValue)
                                return MessageResult.FailureResult($"讲者类型 和 服务时长/次数/字数，不能为空");
                        }
                    }
                }
            }

            var hasBackupVendorGtVendorAmount = request.DetailItems.Where(a => !a.Id.HasValue && !a.HedgePrDetailId.HasValue && a.BackUpVendors?.Any() == true).SelectMany(a => a.BackUpVendors, (a, b) => new { a.TotalAmount, BackupVendorAmount = b.Price * a.Quantity }).Any(a => a.TotalAmount < a.BackupVendorAmount);
            if (hasBackupVendorGtVendorAmount)
                return MessageResult.FailureResult("备选讲者费用不能大于讲者");

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 验证是否所有供应商都是有效的
        /// </summary>
        /// <param name="prApplication"></param>
        /// <param name="detailItems"></param>
        /// <returns></returns>
        async Task<MessageResult> ValidateIfVendorValidAsync(PurPRApplication prApplication, IEnumerable<PurPRApplicationDetail> detailItems)
        {
            var hedgeRowIds = detailItems.Where(a => a.HedgePrDetailId.HasValue).Select(a => a.HedgePrDetailId.Value);
            var detailIds = detailItems.Where(a => a.HedgePrDetailId == null && !hedgeRowIds.Contains(a.Id)).Select(a => a.Id).ToArray();

            var queryBpcsAvm = await LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync();
            var queryVendor = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();
            var queryFinancial = await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync();
            var queryPrApplicationDetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var queryPrApplicationDetailBackupVendor = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailBackupVendorRepository>().GetQueryableAsync();

            //验证是否所有供应商都是可用状态
            var queryAvmVendor = queryBpcsAvm
                .Join(queryFinancial, a => a.FinaId, a => a.Id, (a, b) => new { Avm = a, Fin = b })
                .Join(queryVendor, a => a.Fin.VendorId, a => a.Id, (a, b) => new { a.Avm, Vendor = b });
            var vendors = queryAvmVendor
                .Join(queryPrApplicationDetail.Where(a => detailIds.Contains(a.Id)), a => a.Avm.Id, a => a.VendorId, (a, b) => new CompositeVendorDto { AvmVendorId = a.Avm.Id, NexBpmVendorId = a.Vendor.Id, VendorName = a.Avm.Vndnam, NexBpmVendorType = a.Vendor.VendorType, EpdId = a.Vendor.EpdId, NexBpmStatus = a.Vendor.Status })
                .Union
                (
                    queryAvmVendor
                    .Join(queryPrApplicationDetailBackupVendor.Where(a => a.PRApplicationId == prApplication.Id && detailIds.Contains(a.PRApplicationDetailId)), a => a.Avm.Id, a => a.VendorId, (a, b) => new CompositeVendorDto { AvmVendorId = a.Avm.Id, NexBpmVendorId = a.Vendor.Id, VendorName = a.Avm.Vndnam, NexBpmVendorType = a.Vendor.VendorType, EpdId = a.Vendor.EpdId, NexBpmStatus = a.Vendor.Status })
                )
                .ToArray();

            var invalidVendors = vendors.Where(a => a.NexBpmStatus != VendorStatus.Valid);
            if (invalidVendors.Any())
                return MessageResult.FailureResult($"以下供应商：{invalidVendors.Select(a => a.VendorName).JoinAsString("，")}的状态异常，不能提交");

            return MessageResult.SuccessResult(vendors);
        }

        /// <summary>
        /// 获取所有供应商信息
        /// </summary>
        /// <param name="prApplication"></param>
        /// <param name="detailItems"></param>
        /// <returns></returns>
        async Task<MessageResult> GetAllDetailVendorAsync(PurPRApplication prApplication, IEnumerable<PurPRApplicationDetail> detailItems)
        {
            var hedgeRowIds = detailItems.Where(a => a.HedgePrDetailId.HasValue).Select(a => a.HedgePrDetailId.Value);
            var detailIds = detailItems.Where(a => a.HedgePrDetailId == null && !hedgeRowIds.Contains(a.Id)).Select(a => a.Id).ToArray();

            var queryBpcsAvm = await LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync();
            var queryVendor = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();
            var queryFinancial = await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync();
            var queryPrApplicationDetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var queryPrApplicationDetailBackupVendor = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailBackupVendorRepository>().GetQueryableAsync();

            //验证是否所有供应商都是可用状态
            var queryAvmVendor = queryBpcsAvm
                .Join(queryFinancial, a => a.FinaId, a => a.Id, (a, b) => new { Avm = a, Fin = b })
                .Join(queryVendor, a => a.Fin.VendorId, a => a.Id, (a, b) => new { a.Avm, Vendor = b });
            var vendors = queryAvmVendor
                .Join(queryPrApplicationDetail.Where(a => detailIds.Contains(a.Id)), a => a.Avm.Id, a => a.VendorId, (a, b) => new CompositeVendorDto { AvmVendorId = a.Avm.Id, NexBpmVendorId = a.Vendor.Id, VendorName = a.Avm.Vndnam, NexBpmVendorType = a.Vendor.VendorType, EpdId = a.Vendor.EpdId, NexBpmStatus = a.Vendor.Status })
                .Union
                (
                    queryAvmVendor
                    .Join(queryPrApplicationDetailBackupVendor.Where(a => a.PRApplicationId == prApplication.Id && detailIds.Contains(a.PRApplicationDetailId)), a => a.Avm.Id, a => a.VendorId, (a, b) => new CompositeVendorDto { AvmVendorId = a.Avm.Id, NexBpmVendorId = a.Vendor.Id, VendorName = a.Avm.Vndnam, NexBpmVendorType = a.Vendor.VendorType, EpdId = a.Vendor.EpdId, NexBpmStatus = a.Vendor.Status })
                )
                .ToArray();

            return MessageResult.SuccessResult(vendors);
        }

        /// <summary>
        /// 当会议是电子签章会议时，进行验证
        /// </summary>
        /// <param name="request"></param>
        /// <param name="prApplication"></param>
        /// <param name="detailItems"></param>
        /// <param name="validVendors"></param>
        /// <returns></returns>
        async Task<MessageResult> ValidateOnlineMeetingRelatedAsync(UpdatePRApplicationRequest request, PurPRApplication prApplication, IEnumerable<PurPRApplicationDetail> detailItems, IEnumerable<CompositeVendorDto> validVendors)
        {
            //非电子签章会议系统，不进行后续验证
            if (prApplication.IsEsignUsed != true)
                return MessageResult.SuccessResult();

            //1874【采购申请】OM重发起时：只有1000和空的情况才可以新增/变更明细行信息--BE
            //2374【采购申请】重新提交：若会议已推送OM（1001），则不允许新增推送OM的有效费用性质，但应该允许新增AP行
            if (!string.IsNullOrEmpty(prApplication.MeetingStatus) && prApplication.MeetingStatus != OnlineMeetingStatus.NexBpmPushed && request.DetailItems.Any(a => a.PayMethod == PayMethods.AR && a.Id == null))
                return MessageResult.FailureResult("OM侧已更新会议状态，不允许新增明细行");

            //反冲行
            var hedgeIds = detailItems.Where(a => a.HedgePrDetailId.HasValue).Select(a => a.HedgePrDetailId.Value);
            //1874【采购申请】OM重发起时：只有1000和空的情况才可以反冲，1001不能反冲--BE; 20250122 ytw 3686 采购申请】【OM】在线会议：后端拿掉不允许反冲的提示限制
            //var cantHedgeRows = detailItems.Where(a => hedgeIds.Contains(a.Id) && !string.IsNullOrEmpty(a.MeetingStatus) && a.MeetingStatus != OnlineMeetingStatus.NexBpmPushed);
            //if (cantHedgeRows.Any())
            //    return MessageResult.FailureResult($"以下明细行{cantHedgeRows.Select(a => a.RowNo).JoinAsString(",")}在OM侧已有更新，不允许反冲");

            //验证是否存在有EPD主键的有效讲者
            if (prApplication.MeetingStatus != "1002" && validVendors.All(a => string.IsNullOrEmpty(a.EpdId)))
                return MessageResult.FailureResult("提交失败，在线会议需要添加至少一位有EPD医生主键的有效讲者");

            #region 验证特定得费用性质下，是否存在重复主讲

            var allCostNatures = await LazyServiceProvider.LazyGetService<IDataverseService>().GetCostNatureAsync();
            var prDetailDatas = detailItems.Where(a => !a.HedgePrDetailId.HasValue && !hedgeIds.Contains(a.Id))
                .Join(allCostNatures, a => a.CostNature, a => a.Id, (a, b) => new { PrDetail = a, CostNature = b })
                .Join(validVendors, a => a.PrDetail.VendorId, a => a.AvmVendorId, (a, b) => new
                {
                    a.CostNature,
                    a.PrDetail,
                    Vendor = b,
                    BackupVendors = string.IsNullOrEmpty(a.PrDetail.BackUpVendors) ? [] : JsonConvert.DeserializeObject<IEnumerable<CreateUpdatePRApplicationBackupVendor>>(a.PrDetail.BackUpVendors)
                });
            //判断是否存在有效的行：AR+讲者+主备选讲者都要有医生主键+费用性质是可推送的（EPD可接收）才能【提交】
            var validRowNonEpdIds = prDetailDatas.Where(a => a.PrDetail.PayMethod == PayMethods.AR && a.CostNature.PushOnlineMeeting == true && a.Vendor.NexBpmVendorType == VendorTypes.HCPPerson);
            //无有效数据则直接返回错误
            //var validRows = validRowNonEpdIds.Where(a => !string.IsNullOrEmpty(a.Vendor.EpdId));

            if (prApplication.MeetingStatus != "1002")
            {
                if (!validRowNonEpdIds.Any() || validRowNonEpdIds.Any(a => string.IsNullOrEmpty(a.Vendor.EpdId) || a.BackupVendors.Join(validVendors, a1 => a1.VendorId, a1 => a1.AvmVendorId, (a, b) => b).Any(a1 => string.IsNullOrEmpty(a1.EpdId))))
                    return MessageResult.FailureResult("必须要存在有效行才能提交：AR+讲者+主备选讲者都要有医生主键+费用性质是可推送在线会议");

                //if (!validRows.Where(a => !string.IsNullOrEmpty(a.Vendor.EpdId)).Any())
                //    return MessageResult.FailureResult("必须要存在有效行才能提交：AR+讲者+主备选讲者都要有医生主键+费用性质是可推送在线会议");
                //else//存在有效行时，再验证备选讲者
                //{
                //    //没有备选讲者，或者备选讲者也有epdid，则算有效行
                //    var hasValidBackupVendorRows = validRows.Any(a => a.BackupVendors.Count() == 0 || a.BackupVendors.Join(validVendors, a => a.VendorId, a => a.AvmVendorId, (a, b) => b).Any(a => !string.IsNullOrEmpty(a.EpdId)));
                //    if (!hasValidBackupVendorRows)
                //        return MessageResult.FailureResult("必须要存在有效行才能提交：AR+讲者+主备选讲者都要有医生主键+费用性质是可推送在线会议");
                //}
            }
            #endregion

            //验证主讲是否重复
            var hasDuplications = validRowNonEpdIds.GroupBy(a => a.Vendor).Any(a => a.Count() > 1);
            if (hasDuplications)
                return MessageResult.FailureResult("特定费用性质下，主讲者不能重复");

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 验证产品分摊比例
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<MessageResult> ValidatePRApplicationProductApportionmentRatioAsync(CreatePRApplicationRequest request)
        {
            if (request.ProductApportionments?.Any() == true)
            {
                //验证主表产品拆分比例是否是100%
                if (request.ProductApportionments.Sum(a => a.Ratio) != 100)
                    return Task.FromResult(MessageResult.FailureResult("主单产品分摊比例未分摊完全"));

                foreach (var item in request.DetailItems)
                {
                    if (item.ProductApportionments.Sum(a => a.Ratio) != 100)
                        return Task.FromResult(MessageResult.FailureResult("明细数据产品分摊比例未分摊完全"));
                }
            }

            return Task.FromResult(MessageResult.SuccessResult());
        }

        /// <summary>
        /// 验证是否超过计酬标准上限
        /// </summary>
        /// <param name="prApplication"></param>
        /// <param name="detailItems"></param>
        /// <returns></returns>
        async Task<MessageResult> ValidatePayStandardAsync(PurPRApplication prApplication, IEnumerable<PurPRApplicationDetail> detailItems)
        {
            var queryPayStandard = await LazyServiceProvider.LazyGetService<IOECPayStandardRepository>().GetQueryableAsync();
            var queryPayStandardConfig = await LazyServiceProvider.LazyGetService<IOECPayStandardConfigRepository>().GetQueryableAsync();
            var queryPayStandardConfigDetail = await LazyServiceProvider.LazyGetService<IOECPayStandardConfigDetailRepository>().GetQueryableAsync();

            //排除反冲行
            var hedgeRowIds = detailItems.Where(a => a.HedgePrDetailId.HasValue).Select(a => a.HedgePrDetailId.Value).ToArray();
            var rows = detailItems.Where(a => !a.HedgePrDetailId.HasValue && !hedgeRowIds.Contains(a.Id)).ToArray();
            //根据pr明细行选择的预计日期查询出符合条件的计酬配置数据
            Expression<Func<OECPayStandard, bool>> predicate = a => false;
            foreach (var row in rows)
                predicate = predicate.Or(a => row.EstimateDate >= a.EffectStart && row.EstimateDate < (a.EffectEnd.HasValue ? a.EffectEnd.Value.AddDays(1) : DateTime.MaxValue));
            //取出配置数据
            var costNatureIds = rows.Select(a => a.CostNature.Value).ToArray();
            var allPayStandardConfigs = queryPayStandard.Where(predicate)
                .Join(queryPayStandardConfig.Where(a => costNatureIds.Contains(a.ExpenseNature)), a => a.Id, a => a.PayStandardId, (a, b) => new { PayStandard = a, PayStandardConfig = b })
                .Join(queryPayStandardConfigDetail, a => a.PayStandardConfig.Id, a => a.PayStandardConfigId, (a, b) => new
                {
                    a.PayStandard.EffectStart,
                    a.PayStandard.EffectEnd,
                    a.PayStandardConfig.ActiveNature,
                    a.PayStandardConfig.ExpenseNature,
                    a.PayStandardConfig.IdentityType,
                    a.PayStandardConfig.BuId,
                    a.PayStandardConfig.CountUnit,
                    b.MinRelation,
                    b.MaxRelation,
                    b.MinServiceDuration,
                    b.MaxServiceDuration,
                    b.VLExpenseLimit
                }).ToArray();

            var identityTypes = await LazyServiceProvider.LazyGetService<IDataverseService>().GetDictionariesAsync(DictionaryType.IdentityType);

            //验证方法
            var validatePayStandard = new Func<IEnumerable<(string VendorType, DateTime? EstimateDate, Guid? costNature, int? ServiceDuration, string HcpLevelCode, decimal? TotalAmountRMB, int RowNo, string VendorName)>, MessageResult>(rows =>
            {
                //对比每行数据是否超过
                foreach (var row in rows)
                {
                    //根据时间段和身份，筛选出匹配的计酬配置数据
                    var identityType = identityTypes.FirstOrDefault(a => string.Equals(a.Code, row.VendorType, StringComparison.CurrentCultureIgnoreCase));
                    var payStandardConfigs = allPayStandardConfigs.Where(a => a.ExpenseNature == row.costNature && row.EstimateDate >= a.EffectStart && row.EstimateDate < (a.EffectEnd.HasValue ? a.EffectEnd.Value.AddDays(1) : DateTime.MaxValue) && a.IdentityType == identityType?.Id);
                    if (!payStandardConfigs.Any())
                        continue;

                    //先按BU去匹，匹配不到再匹是否有空BU的配置数据
                    if (payStandardConfigs.Any(a => a.BuId == prApplication.ApplyUserBu))
                        payStandardConfigs = payStandardConfigs.Where(a => a.BuId == prApplication.ApplyUserBu);
                    else
                        payStandardConfigs = payStandardConfigs.Where(a => !a.BuId.HasValue);
                    if (!payStandardConfigs.Any())
                        continue;

                    //匹配出指定区间的配置数据
                    var configDatas = payStandardConfigs.Select(a =>
                    {
                        var minValue = a.MinServiceDuration ?? 0;
                        var maxValue = a.MaxServiceDuration ?? int.MaxValue;

                        var data = new
                        {
                            Value1 = a.MinRelation == PayStandardConfigOperator.Lt ? minValue + 1 : minValue,
                            Value2 = a.MaxRelation == PayStandardConfigOperator.Lt ? maxValue - 1 : maxValue,
                            a.CountUnit,
                            a.VLExpenseLimit,
                        };
                        return data;
                    })
                    .Where(a => row.ServiceDuration >= a.Value1 && row.ServiceDuration <= a.Value2);
                    if (!configDatas.Any())
                        continue;

                    foreach (var configData in configDatas)
                    {
                        var configDetailItems = JsonConvert.DeserializeObject<IEnumerable<PayStandardConfigDetailItemDto>>(configData.VLExpenseLimit);
                        var configDetailItem = configDetailItems.FirstOrDefault(a => string.Equals(a.VendorLevel, row.HcpLevelCode, StringComparison.CurrentCultureIgnoreCase));
                        if (configDetailItem == null)
                            continue;

                        //固定模式直接比较金额
                        if (configDetailItem.PayLimit == PayStandardConfigDetailPayLimit.Fixed)
                        {
                            if (row.TotalAmountRMB > (decimal)configDetailItem.UnitPrice)
                                return MessageResult.FailureResult($"明细行第{row.RowNo}行{row.VendorName}的RMB金额超过了计酬标准上限（￥ {configDetailItem.UnitPrice:N2}元）");
                        }
                        else//非固定则需要换算再作比较
                        {
                            double? count = null;
                            //服务时长
                            if (configData.CountUnit == (int)CountUnit.ServiceTime)
                                count = row.ServiceDuration / 1.0 / PayStandardConst.ServiceTime;
                            //字数，按向下取整计算，如：1599/800=1
                            else if (configData.CountUnit == (int)CountUnit.WordCount)
                                count = row.ServiceDuration / PayStandardConst.WordCount;

                            if (count != null && row.TotalAmountRMB > (decimal)(configDetailItem.UnitPrice * count))
                                return MessageResult.FailureResult($"明细行第{row.RowNo}行{row.VendorName}的RMB金额超过了计酬标准上限（￥ {configDetailItem.UnitPrice * count.Value:N2}元）");
                        }
                    }
                }

                return MessageResult.SuccessResult();
            });

            //验证主讲者
            var datas = rows.Select(a => (a.VendorType, a.EstimateDate, a.CostNature, a.ServiceDuration, a.HcpLevelCode, a.TotalAmountRMB, a.RowNo, a.VendorName));
            var messageResult = validatePayStandard(datas);
            if (!messageResult.Success)
                return messageResult;

            //验证备选讲者
            datas = rows.Where(a => !string.IsNullOrEmpty(a.BackUpVendors)).SelectMany(a =>
            {
                var backupVendors = JsonConvert.DeserializeObject<IEnumerable<CreateUpdatePRApplicationBackupVendor>>(a.BackUpVendors).Select(a1 =>
                {
                    var amount = a.Quantity * a1.Price * (decimal)prApplication.Rate;
                    var data = (a.VendorType, a.EstimateDate, a.CostNature, a.ServiceDuration, a1.HcpLevelCode, amount, a.RowNo, a1.VendorIdName);
                    return data;
                });

                return backupVendors;
            });
            messageResult = validatePayStandard(datas);
            if (!messageResult.Success)
                return messageResult;

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 验证预算
        /// </summary>
        /// <param name="prApplication"></param>
        /// <param name="detailItems"></param>
        /// <returns></returns>
        async Task<MessageResult> ValidateBudgetAsync(PurPRApplication prApplication, IEnumerable<PurPRApplicationDetail> detailItems)
        {
            //如果都是0元讲者，不进行预算扣减
            var needDeductBudget = detailItems.Any(a => (a.TotalAmountRMB ?? 0) - (a.IcbAmount ?? 0) > 0);
            if (needDeductBudget)
            {
                //检查子预算是否足够
                var useBudgetRequest = new UseBudgetRequestDto
                {
                    PrId = prApplication.Id,
                    SubbudgetId = prApplication.SubBudgetId.Value,
                    Items = detailItems.Select(a => new UseInfo { PdRowNo = a.RowNo, UseAmount = (a.TotalAmountRMB ?? 0) - (a.IcbAmount ?? 0) })
                };
                var result = await LazyServiceProvider.LazyGetService<ISubbudgetService>().CheckSubbudgetAmountSufficientAsync(useBudgetRequest);
                if (result.Success)
                    result.Data = new Tuple<bool, UseBudgetRequestDto>(needDeductBudget, useBudgetRequest);

                return result;
            }

            return MessageResult.SuccessResult(new Tuple<bool, UseBudgetRequestDto>(needDeductBudget, null));
        }

        /// <summary>
        /// 校验讲者PSA上限
        /// </summary>
        /// <param name="request"></param>
        /// <param name="prApplication"></param>
        /// <param name="originalPrDetails"></param>
        /// <param name="newPrDetails"></param>
        /// <returns></returns>
        async Task<MessageResult> ValidateSpeakerPSALimitAsync(CreatePRApplicationRequest request, PurPRApplication prApplication, IEnumerable<PurPRApplicationDetail> originalPrDetails, IEnumerable<PurPRApplicationDetail> newPrDetails)
        {
            var queryBpcsAvm = await LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync();
            var queryVendor = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();
            var queryFinancial = await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync();
            var queryPrApplicationDetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var queryPrApplicationDetailBackupVendor = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailBackupVendorRepository>().GetQueryableAsync();
            var queryComPsa = await LazyServiceProvider.LazyGetService<IOECPSAComPSALimitRepository>().GetQueryableAsync();
            var queryComExpenseType = await LazyServiceProvider.LazyGetService<IOECPSAComExpenseTypeRepository>().GetQueryableAsync();
            var queryPsaUseHistory = await LazyServiceProvider.LazyGetService<IOECPSASpeakerLimitUseHistoryRepository>().GetQueryableAsync();
            var queryPsaExtra = await LazyServiceProvider.LazyGetService<IOECPSASpeakerLimitExtraRepository>().GetQueryableAsync();

            //讲者类型
            var types = new string[] { "NHIV", "NT" };

            #region 查询出现有的讲者和备选讲者psa和例外次数及金额总数和剩余数据

            var speakerLimits = queryBpcsAvm.Where(a => types.Contains(a.Vtype))//讲者
                   .Join(queryFinancial, a => a.FinaId, a => a.Id, (a, b) => new { Avm = a, Fin = b })
                   .Join(queryVendor.Where(a => a.VendorType == VendorTypes.HCPPerson), a => a.Fin.VendorId, a => a.Id, (a, b) => new { a.Avm, Vendor = b })
                   .Join(queryPrApplicationDetail.Where(a => a.PRApplicationId == prApplication.Id), a => a.Avm.Id, a => a.VendorId, (a, b) => new { a.Avm, a.Vendor, PrDetail = b, ComPsa = queryComPsa.FirstOrDefault(a1 => (b.EstimateDate.HasValue ? b.EstimateDate.Value.Year : DateTime.MinValue.Year) >= a1.EffectStart.Year && b.EstimateDate.Value.Year <= (a1.EffectEnd.HasValue ? a1.EffectEnd.Value.Year : DateTime.MaxValue.Year)) })
                   //.GroupJoin(queryComPsa, a => 1, a => 1, (a, b) => new { a.Avm, a.Vendor, a.PrDetail, ComPsas = b })
                   //.SelectMany(a => a.ComPsas.DefaultIfEmpty(), (a, b) => new { a.Avm, a.Vendor, a.PrDetail, ComPsa = b })
                   .Select(a => new
                   {
                       a.Avm,
                       a.Vendor,
                       a.PrDetail,
                       a.ComPsa,
                       CostNatureMatched = a.ComPsa != null ? queryComExpenseType.Any(a1 => a1.ComPSALimitId == a.ComPsa.Id && a1.ExpenseNature == a.PrDetail.CostNature) : false
                   })
                   .Where(a => a.CostNatureMatched)
                   .Select(a => new
                   {
                       BpcsVendorId = a.Avm.Id,
                       SpeakerVendorId = a.Vendor.Id,
                       a.PrDetail.EstimateDate,
                       a.PrDetail.RowNo,
                       a.PrDetail.VendorName,
                       a.PrDetail.CostNature,
                       a.PrDetail.ExceptionNumber,
                       TimesLimit = a.ComPsa != null ? a.ComPsa.TimesLimit : 0,
                       AmountLimit = a.ComPsa != null ? a.ComPsa.AmountLimit : 0,
                       ComPsaUsedTimes = queryPsaUseHistory.Where(a1 => a1.VendorId == a.Vendor.Id && a1.EffectiveDate.Year == a.PrDetail.EstimateDate.Value.Year && !a1.PsaExternalId.HasValue).Sum(a1 => a1.Times),
                       ComPsaUsedAmount = queryPsaUseHistory.Where(a1 => a1.VendorId == a.Vendor.Id && a1.EffectiveDate.Year == a.PrDetail.EstimateDate.Value.Year && !a1.PsaExternalId.HasValue).Sum(a1 => a1.Amount),
                       ExtTimes = queryPsaExtra.Where(a1 => a1.DivisionId == prApplication.ApplyUserBu && a1.Year == a.PrDetail.EstimateDate.Value.Year && a1.VendorId == a.Vendor.Id && a1.ExtralAuditApplicationNo == a.PrDetail.ExceptionNumber).Sum(a1 => a1.ExtralTimesRest),
                       ExtAmount = queryPsaExtra.Where(a1 => a1.DivisionId == prApplication.ApplyUserBu && a1.Year == a.PrDetail.EstimateDate.Value.Year && a1.VendorId == a.Vendor.Id && a1.ExtralAuditApplicationNo == a.PrDetail.ExceptionNumber).Sum(a1 => a1.ExtralAmountRest),
                       ExtUsedTimes = queryPsaUseHistory.Where(a1 => a1.VendorId == a.Vendor.Id && a1.EffectiveDate.Year == a.PrDetail.EstimateDate.Value.Year && a1.PsaExternalId.HasValue).Join(queryPsaExtra.Where(a1 => a1.DivisionId == prApplication.ApplyUserBu && a1.Year == a.PrDetail.EstimateDate.Value.Year && a1.VendorId == a.Vendor.Id && a1.ExtralAuditApplicationNo == a.PrDetail.ExceptionNumber), a1 => a1.PsaExternalId, a1 => a1.Id, (a1, b1) => a1).Sum(a1 => a1.Times),
                       ExtUsedAmount = queryPsaUseHistory.Where(a1 => a1.VendorId == a.Vendor.Id && a1.EffectiveDate.Year == a.PrDetail.EstimateDate.Value.Year && a1.PsaExternalId.HasValue).Join(queryPsaExtra.Where(a1 => a1.DivisionId == prApplication.ApplyUserBu && a1.Year == a.PrDetail.EstimateDate.Value.Year && a1.VendorId == a.Vendor.Id && a1.ExtralAuditApplicationNo == a.PrDetail.ExceptionNumber), a1 => a1.PsaExternalId, a1 => a1.Id, (a1, b1) => a1).Sum(a1 => a1.Amount),
                       Times = a.PrDetail.HedgePrDetailId.HasValue ? -1 : 1,
                       Amount = a.PrDetail.TotalAmountRMB
                   })
                   .Concat(//备选讲者
                       queryPrApplicationDetailBackupVendor.Where(a => a.PRApplicationId == prApplication.Id)
                       .Join(queryBpcsAvm.Where(a => types.Contains(a.Vtype)), a => a.VendorId, a => a.Id, (a, b) => new
                       {
                           BackupVendor = a,
                           Avm = b
                       })
                       .Join(queryFinancial, a => a.Avm.FinaId, a => a.Id, (a, b) => new
                       {
                           a.Avm,
                           a.BackupVendor,
                           Fin = b
                       })
                       .Join(queryVendor.Where(a => a.VendorType == VendorTypes.HCPPerson), a => a.Fin.VendorId, a => a.Id, (a, b) => new { a.Avm, a.BackupVendor, Vendor = b })
                       .Join(queryPrApplicationDetail.Where(a => a.PRApplicationId == prApplication.Id), a => a.BackupVendor.PRApplicationDetailId, a => a.Id, (a, b) => new { a.Avm, a.BackupVendor, a.Vendor, PrDetail = b, ComPsa = queryComPsa.FirstOrDefault(a1 => (b.EstimateDate.HasValue ? b.EstimateDate.Value.Year : DateTime.MinValue.Year) >= a1.EffectStart.Year && b.EstimateDate.Value.Year <= (a1.EffectEnd.HasValue ? a1.EffectEnd.Value.Year : DateTime.MaxValue.Year)) })
                       //.GroupJoin(queryComPsa, a => a.PrDetail.EstimateDate.HasValue ? a.PrDetail.EstimateDate.Value.Year : new int?(), a => new int?(a.EffectStart.Year), (a, b) => new { a.Avm, a.BackupVendor, a.Vendor, a.PrDetail, ComPsas = b })
                       //.SelectMany(a => a.ComPsas.DefaultIfEmpty(), (a, b) => new { a.Avm, a.BackupVendor, a.Vendor, a.PrDetail, ComPsa = b })
                       .Select(a => new
                       {
                           a.Avm,
                           a.Vendor,
                           a.BackupVendor,
                           a.PrDetail,
                           a.ComPsa,
                           CostNatureMatched = a.ComPsa != null ? queryComExpenseType.Any(a1 => a1.ComPSALimitId == a.ComPsa.Id && a1.ExpenseNature == a.PrDetail.CostNature) : false
                       })
                       .Where(a => a.CostNatureMatched)
                       .Select(a => new
                       {
                           BpcsVendorId = a.Avm.Id,
                           SpeakerVendorId = a.Vendor.Id,
                           a.PrDetail.EstimateDate,
                           a.PrDetail.RowNo,
                           a.BackupVendor.VendorName,
                           a.PrDetail.CostNature,
                           a.BackupVendor.ExceptionNumber,
                           TimesLimit = a.ComPsa != null ? a.ComPsa.TimesLimit : 0,
                           AmountLimit = a.ComPsa != null ? a.ComPsa.AmountLimit : 0,
                           ComPsaUsedTimes = queryPsaUseHistory.Where(a1 => a1.VendorId == a.Vendor.Id && a1.EffectiveDate.Year == a.PrDetail.EstimateDate.Value.Year && !a1.PsaExternalId.HasValue).Sum(a1 => a1.Times),
                           ComPsaUsedAmount = queryPsaUseHistory.Where(a1 => a1.VendorId == a.Vendor.Id && a1.EffectiveDate.Year == a.PrDetail.EstimateDate.Value.Year && !a1.PsaExternalId.HasValue).Sum(a1 => a1.Amount),
                           ExtTimes = queryPsaExtra.Where(a1 => a1.DivisionId == prApplication.ApplyUserBu && a1.Year == a.PrDetail.EstimateDate.Value.Year && a1.VendorId == a.Vendor.Id && a1.ExtralAuditApplicationNo == a.BackupVendor.ExceptionNumber).Sum(a1 => a1.ExtralTimesRest),
                           ExtAmount = queryPsaExtra.Where(a1 => a1.DivisionId == prApplication.ApplyUserBu && a1.Year == a.PrDetail.EstimateDate.Value.Year && a1.VendorId == a.Vendor.Id && a1.ExtralAuditApplicationNo == a.BackupVendor.ExceptionNumber).Sum(a1 => a1.ExtralAmountRest),
                           ExtUsedTimes = queryPsaUseHistory.Where(a1 => a1.VendorId == a.Vendor.Id && a1.EffectiveDate.Year == a.PrDetail.EstimateDate.Value.Year && a1.PsaExternalId.HasValue).Join(queryPsaExtra.Where(a1 => a1.DivisionId == prApplication.ApplyUserBu && a1.Year == a.PrDetail.EstimateDate.Value.Year && a1.VendorId == a.Vendor.Id && a1.ExtralAuditApplicationNo == a.BackupVendor.ExceptionNumber), a1 => a1.PsaExternalId, a1 => a1.Id, (a1, b1) => a1).Sum(a1 => a1.Times),
                           ExtUsedAmount = queryPsaUseHistory.Where(a1 => a1.VendorId == a.Vendor.Id && a1.EffectiveDate.Year == a.PrDetail.EstimateDate.Value.Year && a1.PsaExternalId.HasValue).Join(queryPsaExtra.Where(a1 => a1.DivisionId == prApplication.ApplyUserBu && a1.Year == a.PrDetail.EstimateDate.Value.Year && a1.VendorId == a.Vendor.Id && a1.ExtralAuditApplicationNo == a.BackupVendor.ExceptionNumber), a1 => a1.PsaExternalId, a1 => a1.Id, (a1, b1) => a1).Sum(a1 => a1.Amount),
                           Times = a.PrDetail.HedgePrDetailId.HasValue ? -1 : 1,
                           Amount = a.BackupVendor.TotalAmountRMB
                       }))
                   .ToArray();

            //未匹配到相应的费用性质的PSA上限设置，则不验证上限
            if (speakerLimits.Length == 0)
                return MessageResult.SuccessResult();

            var newSpeakerPsaCosts = speakerLimits.GroupBy(a => new { a.BpcsVendorId, a.SpeakerVendorId, a.EstimateDate, a.VendorName, a.CostNature, a.ExceptionNumber }).Select(a => new
            {
                a.Key.BpcsVendorId,
                a.Key.SpeakerVendorId,
                a.Key.EstimateDate,
                a.Key.VendorName,
                a.Key.CostNature,
                a.Key.ExceptionNumber,
                a.FirstOrDefault(a1 => a1.SpeakerVendorId == a.Key.SpeakerVendorId && a1.ExceptionNumber == a.Key.ExceptionNumber)?.TimesLimit,
                a.FirstOrDefault(a1 => a1.SpeakerVendorId == a.Key.SpeakerVendorId && a1.ExceptionNumber == a.Key.ExceptionNumber)?.AmountLimit,
                a.FirstOrDefault(a1 => a1.SpeakerVendorId == a.Key.SpeakerVendorId && a1.ExceptionNumber == a.Key.ExceptionNumber)?.ComPsaUsedTimes,
                a.FirstOrDefault(a1 => a1.SpeakerVendorId == a.Key.SpeakerVendorId && a1.ExceptionNumber == a.Key.ExceptionNumber)?.ComPsaUsedAmount,
                a.FirstOrDefault(a1 => a1.SpeakerVendorId == a.Key.SpeakerVendorId && a1.ExceptionNumber == a.Key.ExceptionNumber)?.ExtTimes,
                a.FirstOrDefault(a1 => a1.SpeakerVendorId == a.Key.SpeakerVendorId && a1.ExceptionNumber == a.Key.ExceptionNumber)?.ExtAmount,
                a.FirstOrDefault(a1 => a1.SpeakerVendorId == a.Key.SpeakerVendorId && a1.ExceptionNumber == a.Key.ExceptionNumber)?.ExtUsedTimes,
                a.FirstOrDefault(a1 => a1.SpeakerVendorId == a.Key.SpeakerVendorId && a1.ExceptionNumber == a.Key.ExceptionNumber)?.ExtUsedAmount,
                Times = a.Sum(a1 => a1.Times),
                Amount = a.Sum(a1 => a1.Amount)
            })
            .Select(a => new
            {
                a.BpcsVendorId,
                a.SpeakerVendorId,
                a.EstimateDate,
                a.VendorName,
                a.CostNature,
                a.ExceptionNumber,
                a.TimesLimit,
                a.AmountLimit,
                a.ComPsaUsedTimes,
                a.ComPsaUsedAmount,
                a.ExtTimes,
                a.ExtAmount,
                ExtUsedTimes = a.ExtUsedTimes - a.ExtTimes,
                ExtUsedAmount = a.ExtUsedAmount - a.ExtAmount,
                a.Times,
                a.Amount
            })
            .ToArray();

            #endregion

            #region 老数据的psa花费

            var oldSpeakerPsaCosts = originalPrDetails.Where(a => a.VendorId.HasValue).Select(a => new
            {
                VendorId = a.VendorId.Value,
                a.VendorName,
                a.EstimateDate,
                a.ExceptionNumber,
                a.CostNature,
                Times = a.HedgePrDetailId.HasValue ? -1 : 1,
                TotalAmount = a.TotalAmountRMB
            }).Concat(
                originalPrDetails.Where(a => !string.IsNullOrEmpty(a.BackUpVendors))
                .Select(a => new { PrDetail = a, BackupVendors = JsonConvert.DeserializeObject<IEnumerable<CreateUpdatePRApplicationBackupVendor>>(a.BackUpVendors) })
                .SelectMany(a => a.BackupVendors, (a, b) => new
                {
                    b.VendorId,
                    VendorName = b.VendorIdName,
                    a.PrDetail.EstimateDate,
                    b.ExceptionNumber,
                    a.PrDetail.CostNature,
                    Times = a.PrDetail.HedgePrDetailId.HasValue ? -1 : 1,
                    TotalAmount = a.PrDetail.HedgePrDetailId.HasValue ? -Math.Abs(b.Price) * a.PrDetail.Quantity * (decimal)prApplication.Rate : b.Price * a.PrDetail.Quantity * (decimal)prApplication.Rate
                })
            )
            .GroupBy(a => new { a.VendorId, a.VendorName, a.EstimateDate, a.ExceptionNumber, a.CostNature })
            .Select(a => new
            {
                a.Key.VendorId,
                a.Key.VendorName,
                a.Key.EstimateDate,
                a.Key.ExceptionNumber,
                a.Key.CostNature,
                Times = a.Sum(a1 => a1.Times),
                Amount = a.Sum(a1 => a1.TotalAmount)
            })
            .ToArray();

            //供应商Ids
            var vendorIds = oldSpeakerPsaCosts.Select(a => a.VendorId).Distinct().ToArray();
            var speakerIds = queryBpcsAvm.Where(a => vendorIds.Contains(a.Id) && types.Contains(a.Vtype)).Select(a => a.Id).ToArray();
            //筛选出只为讲者的数据
            oldSpeakerPsaCosts = oldSpeakerPsaCosts.Where(a => speakerIds.Contains(a.VendorId)).ToArray();

            //说明是首次提交，则当作没有原始PR明细行
            if (!prApplication.ApplyTime.HasValue)
                oldSpeakerPsaCosts = [];

            #endregion

            //保存前后都有的数据，则比较前后变更所产生的差值
            var intersectDatas = newSpeakerPsaCosts
                .GroupJoin(oldSpeakerPsaCosts, a => a.BpcsVendorId, a => a.VendorId, (a, b) => new { NewCost = a, OldCosts = b })
                .SelectMany(a => a.OldCosts.DefaultIfEmpty(), (a, b) => new
                {
                    a.NewCost.Times,
                    a.NewCost.Amount,
                    a.NewCost.BpcsVendorId,
                    a.NewCost.SpeakerVendorId,
                    a.NewCost.EstimateDate,
                    a.NewCost.VendorName,
                    a.NewCost.CostNature,
                    a.NewCost.ExceptionNumber,
                    a.NewCost.TimesLimit,
                    a.NewCost.AmountLimit,
                    a.NewCost.ComPsaUsedTimes,
                    a.NewCost.ComPsaUsedAmount,
                    a.NewCost.ExtTimes,
                    a.NewCost.ExtAmount,
                    a.NewCost.ExtUsedTimes,
                    a.NewCost.ExtUsedAmount,
                    SurplusTimes = a.NewCost.Times - (b?.Times ?? 0),
                    SurplusAmount = a.NewCost.Amount - (b?.Amount ?? 0)
                })
                .ToArray();

            //判断是否超出限额
            foreach (var item in intersectDatas.Where(a => a.Amount > 0))
            {
                //只有当有差额>0时，才判断psa limit是否足够
                if (item.SurplusTimes > 0 || item.SurplusAmount > 0)
                {
                    //如果有例外审批编号，则校验例外次数和金额
                    var msg = string.IsNullOrEmpty(item.ExceptionNumber) ? "通用" : "例外";
                    var surplusTimes = string.IsNullOrEmpty(item.ExceptionNumber) ? item.TimesLimit - Math.Abs(item.ComPsaUsedTimes ?? 0) : item.ExtTimes - Math.Abs(item.ExtUsedTimes ?? 0);
                    var surplusAmount = string.IsNullOrEmpty(item.ExceptionNumber) ? item.AmountLimit - Math.Abs(item.ComPsaUsedAmount ?? 0) : item.ExtAmount - Math.Abs(item.ExtUsedAmount ?? 0);

                    //判断本次使用次数或金额是否大于剩余次数或金额
                    if (item.SurplusTimes > surplusTimes || item.SurplusAmount > surplusAmount)
                        if (string.IsNullOrEmpty(item.ExceptionNumber))
                            return MessageResult.FailureResult($"PR单明细行的{item.VendorName}，{msg}次数或金额不够，剩余次数：{surplusTimes}，金额{surplusAmount:f2}");
                        else
                            return MessageResult.FailureResult($"PR单明细行的{item.VendorName}，{msg}次数或金额不够，剩余次数：{surplusTimes}，金额{surplusAmount:f2}");
                }
            }

            //收集需要扣减或返还的psa数据
            var psaDatas = intersectDatas.Where(a => a.SurplusTimes != 0 || a.SurplusAmount != 0).Select(a => new PrUseSpeakerLimitDetailRequest
            {
                VendorId = a.SpeakerVendorId,
                EffectiveDate = a.EstimateDate ?? DateTime.Today,
                ExceptionNumber = a.ExceptionNumber,
                Times = a.SurplusTimes,
                Amount = a.SurplusAmount ?? 0,
                Type1 = (a.SurplusAmount ?? 0) > 0 ? OperDetailType.Deduction : OperDetailType.Added,
                Type2 = (a.SurplusAmount ?? 0) > 0 ? ModifyTypes.Deduction : ModifyTypes.Return
            })
            .ToArray();

            return MessageResult.SuccessResult(psaDatas);
        }

        /// <summary>
        /// 判断BU为ADC，CC为8520时，金额是否大于设置的阈值
        /// </summary>
        /// <param name="prApplication"></param>
        /// <returns></returns>
        async Task<bool> JudgeStepRequiredByADC(PurPRApplication prApplication)
        {
            //判断BU为ADC，CC为8520时，金额是否大于设置的阈值
            var isStepRequiredByADC = prApplication.ApplyUserBuName == BUNameConst.ADC && prApplication.CostCenterCode == CostcenterCodeConst.CC8520;
            if (isStepRequiredByADC)
            {
                var adcNeedSalesMktApprovalRMBAmount = await LazyServiceProvider.LazyGetService<ISettingProvider>().GetOrNullAsync(SettingsConst.ADCNeedSalesMktApprovalRMBAmount);
                isStepRequiredByADC = decimal.TryParse(adcNeedSalesMktApprovalRMBAmount, out decimal x) ? prApplication.TotalAmountRMB >= x : false;
            }

            return isStepRequiredByADC;
        }

        /// <summary>
        /// 判断PR的消费大类是否要合规审计审批
        /// </summary>
        /// <param name="prApplication"></param>
        /// <returns></returns>
        async Task<bool> ConsumeIsComplianceAudit(PurPRApplication prApplication)
        {
            if (!prApplication.ExpenseType.HasValue)
                return false;
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var consumes = await dataverseService.GetConsumeCategoryAsync(prApplication.ExpenseType.ToString(), stateCode: null);
            return consumes?.FirstOrDefault()?.IsComplianceAudit == true;
        }

        /// <summary>
        /// 将RMB金额转换成美刀
        /// </summary>
        /// <param name="newPrDetails"></param>
        /// <returns></returns>
        async Task<IEnumerable<(decimal TotalAmount, string ApprovalNumber)>> RMBAmountToUSDAmount(IEnumerable<PurPRApplicationDetail> newPrDetails)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            //获取美刀货币配置
            var usdCurrencies = await dataverseService.GetCurrencyConfig(DictionaryType.CurrencyItems.USD);
            var usdCurrency = usdCurrencies.First();
            var costNatures = await dataverseService.GetCostNatureAsync(stateCode: null);
            //创建审批任务
            var purPRApplicationDetails = newPrDetails.Select(a =>
            {
                CostNatureDto costNature = null;
                if (a.CostNature.HasValue)
                    costNature = costNatures.FirstOrDefault(a1 => a1.Id == a.CostNature);

                return
                (
                    decimal.Round((a.TotalAmountRMB ?? 0) / (decimal)usdCurrency.PlanRate, 4),
                    costNature?.ApprovalNumber
                );

            }).ToArray();

            return purPRApplicationDetails;
        }

        /// <summary>
        /// 判断是否需要指定采购审批
        /// </summary>
        /// <param name="prApplication"></param>
        /// <param name="prApplicationDetails"></param>
        /// <returns></returns>
        async Task<MessageResult<bool>> JudgeSpecialPurchaseApproval(PurPRApplication prApplication, IEnumerable<PurPRApplicationDetail> prApplicationDetails)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            //获取美刀货币配置
            var specPurchaseApprovalConditions = await dataverseService.GetSpecApprovalConditionProcurementAsync();
            var matchConditions = specPurchaseApprovalConditions.Where(a => a.CompanyId == prApplication.CompanyId && a.ConsumeId == prApplication.ExpenseType);
            //未匹配上任何配置，则无需指定采购审批
            if (!matchConditions.Any())
                return MessageResult<bool>.SuccessResult(false);

            //判断只能申请AP类型：如果匹配到的配置中包含有任何只能申请AP类型的限制，则需要判断PR明细中这些类型是否有用于非AP付款方式
            var onlyAPConditions = matchConditions.Where(a => a.Type == DataverseEnums.SpecApprovalConditionProcurement.Type.OnlyAP);
            if (onlyAPConditions.Any())
            {
                IEnumerable<PurPRApplicationDetail> nonAPDetails;
                //如果费用性质没有值，则所有费用性质都需要校验
                if (onlyAPConditions.Any(a => a.CostNatureId == null))
                    nonAPDetails = prApplicationDetails.Where(a => a.PayMethod != PayMethods.AP);
                else
                    nonAPDetails = onlyAPConditions.Join(prApplicationDetails, a => a.CostNatureId, a => a.CostNature, (a, b) => b).Where(a => a.PayMethod != PayMethods.AP);

                //如果不存在未指定成本中心的配置，则验证是否符合特定成本中心
                if (onlyAPConditions.All(a => a.ExceptionCostcenterId != null))
                    nonAPDetails = nonAPDetails.Where(a => !onlyAPConditions.Any(a1 => a1.ExceptionCostcenterId == prApplication.CostCenter));

                //PR明细行中存在非AP付款方式的行
                if (nonAPDetails.Any())
                {
                    var messages = nonAPDetails.Select(a => $"{prApplication.ExpenseTypeName}-{a.CostNatureName}");
                    return MessageResult<bool>.FailureResult($"{messages.JoinAsString("，")}，只能申请AP类型，提交失败");
                }
            }

            //判断AR类型
            var arConditions = matchConditions.Where(a => a.Type == DataverseEnums.SpecApprovalConditionProcurement.Type.AR);
            if (arConditions.Any())
            {
                IEnumerable<PurPRApplicationDetail> arDetails;
                //如果费用性质没有值，则所有费用性质都适用
                if (arConditions.Any(a => a.CostNatureId == null))
                    arDetails = prApplicationDetails.Where(a => a.PayMethod == PayMethods.AR);
                else
                    arDetails = arConditions.Join(prApplicationDetails, a => a.CostNatureId, a => a.CostNature, (a, b) => b).Where(a => a.PayMethod == PayMethods.AR);

                //如果不存在未指定成本中心的配置，则验证是否符合特定成本中心
                if (arConditions.All(a => a.ExceptionCostcenterId != null))
                    arDetails = arDetails.Where(a => !arConditions.Any(a1 => a1.ExceptionCostcenterId == prApplication.CostCenter));

                //存在AR行，则需指定采购审批
                if (arDetails.Any())
                    return MessageResult<bool>.SuccessResult(true);
            }

            return MessageResult<bool>.SuccessResult(false);
        }

        /// <summary>
        /// 判断是否可以反冲
        /// </summary>
        /// <param name="prApplication"></param>
        /// <param name="applicationDetailRequests"></param>
        /// <returns></returns>
        async Task<MessageResult> JudgeCanHedge(IEnumerable<CreateUpdatePRApplicationDetailRequest> applicationDetailRequests, IEnumerable<PurPRApplicationDetail> originalPrDetails)
        {
            //有原始明细行时，判断是否存在反冲的情况
            if (originalPrDetails.Any())
            {
                //本次新增的反冲信息
                var hedgePrDetailIds = applicationDetailRequests.Where(a => !a.Id.HasValue && a.HedgePrDetailId.HasValue).Select(a => a.HedgePrDetailId.Value).ToArray();
                //如果存在反冲行
                if (hedgePrDetailIds.Any())
                {
                    var prId = originalPrDetails.First().PRApplicationId;
                    //验证是否已有后续流程
                    var hasNextFlowPrDetailIds = await GetPushedAndConfirmedPrDetailsAsync(prId);
                    var intersect = hedgePrDetailIds.Intersect(hasNextFlowPrDetailIds);
                    if (intersect.Any())
                        return MessageResult.FailureResult($"以下明细行{originalPrDetails.Where(a => intersect.Contains(a.Id)).Select(a => a.RowNo).JoinAsString(",")}已产生后续流程，不允许反冲");
                }
            }

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 验证是否超过审批矩阵0级
        /// </summary>
        /// <param name="prApplication"></param>
        /// <param name="newPrDetails"></param>
        /// <returns></returns>
        async Task<MessageResult> DetectIfExceedLevel0Async(PurPRApplication prApplication, IEnumerable<PurPRApplicationDetail> newPrDetails)
        {
            var deptId = prApplication.ApplyUserDept;
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var orgs = await dataverseService.GetOrganizations(deptId.ToString());
            if (!orgs.Any())
                return MessageResult.FailureResult("未找到申请人部门信息");

            var dept = orgs.First();
            var parentOrgs = await LazyServiceProvider.LazyGetService<ICommonService>().GetParentOrgs(dept);
            var bu = parentOrgs.FirstOrDefault(a => a.OrganizationType == DataverseEnums.Organization.OrganizationType.Bu);
            if (bu == null)
                return MessageResult.FailureResult("未找到申请人部门所属BU");

            //获取美刀货币配置
            var usdCurrencies = await dataverseService.GetCurrencyConfig(DictionaryType.CurrencyItems.USD);
            if (!usdCurrencies.Any())
                return MessageResult.FailureResult("未维护美元和人民币的汇率信息");

            var usdCurrency = usdCurrencies.First();

            #region 判断是否超过审批矩阵0级

            //获取审批矩阵配置
            var approvalMatrixCfgs = await dataverseService.GetExpenseApprovalMatrixConfigurationItemAsync();
            approvalMatrixCfgs = approvalMatrixCfgs.Where(a => a.BuId == bu.Id);
            foreach (var cfg in approvalMatrixCfgs)
            {
                //将美元换算成RMB然后做比较
                cfg.Level0Amount *= usdCurrency.PlanRate;
            }
            //反冲行
            var hedgeRowIds = newPrDetails.Where(a => a.HedgePrDetailId.HasValue).Select(a => a.HedgePrDetailId.Value).ToArray();
            //获取有效的费用性质Id
            var costNatureIds = newPrDetails.Where(a => !a.HedgePrDetailId.HasValue && !hedgeRowIds.Contains(a.Id) && a.CostNature.HasValue).Select(a => a.CostNature.Value).Distinct();
            var costNatures = await LazyServiceProvider.LazyGetService<IDataverseService>().GetCostNatureAsync();
            //获取超过0级的数据
            var exceedLevel0Datas = costNatureIds.Join(costNatures, a => a, a => a.Id, (a, b) => b).Join(approvalMatrixCfgs, a => a.ApprovalNumber, a => a.ApprovalNumber, (a, b) => b).Where(a => prApplication.TotalAmountRMB > a.Level0Amount);
            //如果有超过0级的数据，需要前端上传附件
            if (exceedLevel0Datas.Any())
                return MessageResult.SuccessResult(false);

            #endregion

            #region 判断是否超过财务审批矩阵0级

            var financialApprovalMatrixCfgs = await dataverseService.GetFinancialApprovalAmountMatrixAsync();
            //将美元换算成RMB然后做比较
            var financialApprovalMatrix = financialApprovalMatrixCfgs.Where(a => a.BuId == bu.Id && a.Level == 0).Select(a => a.Amount * usdCurrency.PlanRate);
            //如果有超过0级的数据，需要前端上传附件
            if (financialApprovalMatrix.Any(a => prApplication.TotalAmountRMB > a))
                return MessageResult.SuccessResult(false);

            #endregion

            return MessageResult.SuccessResult(true);
        }

        /// <summary>
        /// 提交采购申请
        /// </summary>
        /// <param name="prApplicationRepository"></param>
        /// <param name="purPRApplication"></param>
        /// <returns></returns>
        public async Task<MessageResult> SubmitPRApplicationAsync(UpdatePRApplicationRequest request)
        {
            MessageResult result;
            if (request.Id.HasValue)
                result = await UpdatePRApplicationAsync(request);
            else
                result = await CreatePRApplicationAsync(request);
            if (!result.Success)
            {
                result.Data = null;
                return result;
            }
            var data = result.Data as Tuple<PurPRApplication, IEnumerable<PurPRApplicationDetail>, IEnumerable<PurPRApplicationDetail>>;
            var prApplication = data.Item1;
            var originalPrDetails = data.Item2;
            var newPrDetails = data.Item3;

            //验证必填项
            result = await ValidateRequiredAsync(request, prApplication, newPrDetails);
            if (!result.Success || result.Code == MessageModelBase.ActiveNumberDuplication.Code)
                return result;

            //验证是否所有供应商都可用  #SCTASK2359209  只验证新增的行
            //判断在newPrDetails 中 id不存在于 originalPrDetails 中的数据
            var originalIds = originalPrDetails.Select(d => d.Id).ToHashSet();
            var newAddedPrDetails = newPrDetails.Where(nd => !originalIds.Contains(nd.Id)).ToArray();

            if (prApplication.Status == PurPRApplicationStatus.RejectedBack)
            {
                if (newAddedPrDetails.Any())
                {
                    result = await ValidateIfVendorValidAsync(prApplication, newAddedPrDetails);
                    if (!result.Success)
                        return result;
                }
            }
            else
            {
                result = await ValidateIfVendorValidAsync(prApplication, newPrDetails);
                if (!result.Success)
                    return result;
            }

            //获得选择的供应商信息
            result = await GetAllDetailVendorAsync(prApplication, newPrDetails);
            var vendors = result.Data as IEnumerable<CompositeVendorDto>;

            //判断是否需要指定采购审批
            var specialPurchaseApprovalResult = await JudgeSpecialPurchaseApproval(prApplication, newPrDetails);
            if (!specialPurchaseApprovalResult.Success)
                return specialPurchaseApprovalResult;

            //判断是否可以反冲
            result = await JudgeCanHedge(request.DetailItems, originalPrDetails);
            if (!result.Success)
                return result;

            //验证预算
            result = await ValidateBudgetAsync(prApplication, newPrDetails);
            if (!result.Success)
                return result;
            var budgetResult = result.Data as Tuple<bool, UseBudgetRequestDto>;

            //验证推送电子签章系统条件
            result = await ValidateOnlineMeetingRelatedAsync(request, prApplication, newPrDetails, vendors);
            if (!result.Success)
                return result;

            //验证产品分摊比例
            result = await ValidatePRApplicationProductApportionmentRatioAsync(request);
            if (!result.Success)
                return result;

            //验证是否超过计酬标准配置
            result = await ValidatePayStandardAsync(prApplication, newPrDetails);
            if (!result.Success)
                return result;

            //验证PSA上限
            result = await ValidateSpeakerPSALimitAsync(request, prApplication, originalPrDetails, newPrDetails);
            if (!result.Success)
                return result;
            //收集到的需扣减或返还的psa数据
            var psaDatas = result.Data as IEnumerable<PrUseSpeakerLimitDetailRequest>;

            //验证是否超过审批矩阵0级
            result = await DetectIfExceedLevel0Async(prApplication, newPrDetails);
            if (!result.Success)
                return result;
            else if (bool.Equals(result.Data, false) && string.IsNullOrEmpty(prApplication.ApprovalEmails))
                return MessageResult.SuccessResult(messageModel: MessageModelBase.ExceedApprovalMatrixLevel0);

            var subBudget = await LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>().FirstOrDefaultAsync(a => a.Id == prApplication.SubBudgetId);
            //将RMB金额转换成美刀
            var purPRApplicationDetails = await RMBAmountToUSDAmount(newPrDetails);
            //判断BU为ADC，CC为8520时，金额是否大于设置的阈值
            var isStepRequiredByADC = await JudgeStepRequiredByADC(prApplication);

            var isComplicanceAudits = subBudget.IsComplicanceAudits ?? false;
            if (!isComplicanceAudits && prApplication.ApplyUserBuName == BUNameConst.ADC)
                isComplicanceAudits = await ConsumeIsComplianceAudit(prApplication);

            var formData = JsonConvert.SerializeObject(new
            {
                prApplication.ApplicationCode,
                IsComplicanceAudits = isComplicanceAudits,
                prApplication.IsShowExpenseStep,
                IsStepRequiredByADC = false,
                IsSpecialPurchaseApproval = specialPurchaseApprovalResult.Data,
                PrDetails = purPRApplicationDetails.Select(a => new { a.TotalAmount, a.ApprovalNumber })
            });
            var isOk = await CreatePOWorkflowAsync(prApplication, isComplicanceAudits, isStepRequiredByADC, specialPurchaseApprovalResult.Data, formData);

            if (!isOk)
                return MessageResult.FailureResult("提交审批失败");
            else
            {
                //需要扣减预算
                if (budgetResult.Item1)
                    result = await LazyServiceProvider.LazyGetService<ISubbudgetService>().UseSubbudgetAsync(budgetResult.Item2);
                //Psa次数和金额扣减
                if (psaDatas?.Any() == true)
                    result = await LazyServiceProvider.LazyGetService<ISpeakerLimitService>().SavePrSpeakerLimitAsync(new PrUseSpeakerLimitRequest { PrApplicationId = prApplication.Id, BuId = prApplication.ApplyUserBu, Details = psaDatas });

                if (result.Success)
                {
                    //清除修订状态
                    prApplication.IsShowExpenseStep = true;
                    //更新申请的状态为“审批中”
                    prApplication.Status = PurPRApplicationStatus.Approving;
                    if (!prApplication.ApplyTime.HasValue)
                        prApplication.ApplyTime = DateTime.Now;
                    await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().UpdateAsync(prApplication);
                }
            }

            return result;
        }
        /// <summary>
        /// PR添加审批任务
        /// </summary>
        /// <param name="applyUserBu"></param>
        /// <param name="applyUserId"></param>
        /// <param name="draftId"></param>
        /// <param name="totalAmount"></param>
        /// <returns></returns>
        private async Task<bool> CreatePOWorkflowAsync(PurPRApplication prApplication, bool isComplicanceAudits, bool isStepRequiredByADC, bool isSpecialPurchaseApproval, string formData)
        {

            var approvalRecord = LazyServiceProvider.LazyGetService<IInitializeApprovalRecordService>();
            //创建审批任务
            var exemptType = EnumUtil.GetEnumIdValues<WorkflowTypeName>().ToDictionary(a => (WorkflowTypeName)a.Key, a => a.Value);

            var createApproval = new CreateApprovalDto
            {
                Name = exemptType[WorkflowTypeName.PurchaseRequest],
                Department = prApplication.ApplyUserDept.ToString(),
                BusinessFormId = prApplication.Id.ToString(),
                BusinessFormNo = prApplication.ApplicationCode,
                BusinessFormName = NameConsts.PRApplication,
                Submitter = CurrentUser.Id?.ToString(),
                OriginalApprovalId = prApplication.ApplyUserId,
                WorkflowType = WorkflowTypeName.PurchaseRequest,
                FormData = formData,
                InstanceName = $"{exemptType[WorkflowTypeName.PurchaseRequest]}-{DateTime.Now.ToString("yyMMdd-hhmmss")}",
                Remark = prApplication.Remark
            };
            await approvalRecord.InsertInitApprovalRecordAsync(new InitializeApprovalRecordDto
            {
                BusinessFormName = NameConsts.PRApplication,
                BusinessId = prApplication.Id,
                InstanceId = Guid.NewGuid(),
                Status = InitApprovalRecordStatus.Pending,
                MaxRetries = 3,
                RequestContent = JsonConvert.SerializeObject(createApproval)
            });
            return true;
        }

        /// <summary>
        /// 获取某个PR单的补录文件列表
        /// </summary>
        /// <param name="Id">The pr identifier.</param>
        /// <returns></returns>
        public async Task<MessageResult> GetAdditionFilesAsync(Guid Id)
        {
            var queryPr = LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>();
            var attachmentQuery = await LazyServiceProvider.LazyGetService<IAttachmentRepository>().GetQueryableAsync();

            var prData = await queryPr.FindAsync(x => x.Id == Id);
            if (prData == null)
                return MessageResult.FailureResult("找不到PR记录");
            var responseDt = new AdditionFilesResponseDto();
            if (!string.IsNullOrEmpty(prData.AdditionalFiles))
            {
                var attachmentIds = prData.AdditionalFiles.Split(',').Select(x => Guid.Parse(x)).ToList();
                if (attachmentIds.Count() > 0)
                {
                    var attachmentEntities = attachmentQuery.Where(x => attachmentIds.Contains(x.Id)).ToList();
                    var attachmentInfo = ObjectMapper.Map<List<Attachment>, List<UploadFileResponseDto>>(attachmentEntities);
                    responseDt.AttachmentInformation = attachmentInfo;
                }
            }
            return MessageResult.SuccessResult(responseDt);
        }

        /// <summary>
        /// 补录文件
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> AdditionFilesAsync(AdditionFilesRequestDto request)
        {
            //if (request.AttachmentInformation == null || request.AttachmentInformation.Count < 1)
            //    return MessageResult.FailureResult("补充文件为空");

            var queryPr = LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>();

            var prData = await queryPr.FindAsync(x => x.Id == request.Id);
            if (prData == null)
                return MessageResult.FailureResult("找不到PR记录");

            var newFileIds = request.AttachmentInformation.Select(x => x.AttachmentId.ToString()).JoinAsString(",");
            prData.AdditionalFiles = newFileIds;

            await queryPr.UpdateAsync(prData);

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 采购推送
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> PurchasePushAsync(List<PRDetailPushRequestDto> request)
        {
            //pdId,采购人、是否线下、线下编号、是否推送、推送时间
            if (request == null || request.Count < 1)
                return MessageResult.FailureResult("请求的明细ID为空");

            var prDetailService = LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>();
            var queryPrd = await prDetailService.GetQueryableAsync();
            var queryBW = await LazyServiceProvider.LazyGetService<IPurBWApplicationRepository>().GetQueryableAsync();

            var prId = request.First().PrId;
            var pdIds = request.Select(x => x.PRDetailId).ToArray();

            //将反冲行的状态一并修改掉
            //var prDetailData = queryPrDetail.Where(x => (pdIds.Contains(x.Id) || pdIds.Contains(x.HedgePrDetailId ?? Guid.Empty)) && x.PushFlag != PushFlagEnum.Pushed).ToList();

            var prDetails = queryPrd.Where(x => x.PRApplicationId == prId).ToArray();

            var prDetailData = prDetails.Where(x => x.PushFlag != PushFlagEnum.Pushed && (pdIds.Contains(x.Id) || pdIds.Contains(x.HedgePrDetailId ?? Guid.Empty))).ToList();

            var bwIds = prDetailData.Where(a => a.BWApplicationId.HasValue).Select(a => a.BWApplicationId.Value).ToList();

            if (bwIds.Count != 0)
            {
                var bws = queryBW.Where(a => bwIds.Contains(a.Id)).ToList();
                foreach (var bw in bws)
                {
                    if (bw.Status != PurExemptStatus.Approved)
                    {
                        return MessageResult.FailureResult($"行号为{prDetailData.FirstOrDefault(a => a.BWApplicationId == bw.Id)?.RowNo}采购申请明细，竞价豁免未审批完成，不能推送");
                    }
                }
            }
            if (prDetailData != null && prDetailData.Count > 0)
            {
                foreach (var item in prDetailData)
                {
                    var requestItem = request.FirstOrDefault(x => x.PRDetailId == item.Id);
                    if (requestItem == null)
                        continue;
                    item.PurchaserId = requestItem.PurchaserId;
                    item.IsOffline = requestItem.IsOffline;
                    item.OfflineNo = requestItem.OfflineNo;
                    item.PushFlag = PushFlagEnum.Pushed;
                    item.PushTime = DateTime.Now;
                }

                await prDetailService.UpdateManyAsync(prDetailData);
                //await CurrentUnitOfWork.SaveChangesAsync();

                #region 是否所有的AP行都已推送，AR行都已确认
                //获取对冲的行
                var hedgeDetails = prDetails.Where(a => a.HedgePrDetailId.HasValue).ToArray();
                //除对冲行外的明细行
                var expectHedgeDetails = prDetails.Where(a => !a.HedgePrDetailId.HasValue && !hedgeDetails.Any(a1 => a1.HedgePrDetailId == a.Id)).ToArray();
                //判断是否所有的AP行都已推送，AR行都已确认
                var judgement = expectHedgeDetails.Where(a => a.PayMethod == PayMethods.AP).All(a => a.PushFlag == PushFlagEnum.Pushed) && expectHedgeDetails.Where(a => a.PayMethod == PayMethods.AR).All(a => a.IsVendorConfimed == true);

                //var judgement = await IsAllPrDetailPushedAndConfirmed(prId);
                #endregion

                if (judgement)
                {
                    var prApplicationRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>();
                    var prApplication = await prApplicationRepository.FindAsync(prId);
                    prApplication.Status = PurPRApplicationStatus.WaitForClose;
                    await prApplicationRepository.UpdateAsync(prApplication);
                }
            }

            return await Task.FromResult(MessageResult.SuccessResult());
        }

        /// <summary>
        /// 获取指定PR单的采购明细->采购推送
        /// </summary>
        /// <param name="Id">The identifier.</param>
        /// <returns></returns>
        public async Task<MessageResult> GetPRDetailListForSpecificPRAsync(GetPRDetailListForSpecificPRRequestDto request)
        {
            var queryPr = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var queryPrDetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var queryBW = await LazyServiceProvider.LazyGetService<IPurBWApplicationRepository>().GetQueryableAsync();
            var pr = queryPr.FirstOrDefault(x => x.Id == request.Id);
            if (pr == null)
                return MessageResult.FailureResult("找不到采购订单");

            //分页么？
            var pdQuery = queryPr.Where(x => x.Id == request.Id)
                .Join(queryPrDetail, a => a.Id, b => b.PRApplicationId, (a, b) => new { pr = a, prDetial = b })
                .Where(x => x.prDetial.PayMethod == PayMethods.AP && x.prDetial.IsHedge == false);//排除反冲行

            var count = pdQuery.Count();
            var datas = pdQuery.Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToArray();

            var prList = datas.Select(x => x.pr).ToList();
            var pdList = datas.Select(x => x.prDetial).ToList();

            //排除反冲行
            //var hedgePrDetails = pdList.Where(a => a.HedgePrDetailId.HasValue);
            //pdList = pdList.Where(a => !hedgePrDetails.Any(a1 => a1.HedgePrDetailId == a.Id || a1.Id == a.Id)).ToList();

            var response = new GetPRDetailListForSpecificPRResponseDto();
            var pdInfo = ObjectMapper.Map<List<PurPRApplicationDetail>, List<PushDetail>>(pdList);

            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var staff = await dataverseService.GetStaffs();
            //var purchaser = await dataverseService.GetStaffListByPositionAndFlow(pr.ApplyUserBu, PositionType.MainProcurement);
            List<GetPurchasersResponseDto> purchaser = null;
            if (pr.CompanyId.HasValue)
                purchaser = GetPurchasers((Guid)pr.CompanyId);

            //From PR 成本中心、大区、公司、
            if (pr.CostCenter.HasValue)
            {
                response.CostCenter = (Guid)pr.CostCenter;
                response.CostCenterName = pr.CostCenterName;
            }

            response.PrApplicationCode = pr.ApplicationCode;

            response.ApplyUserId = pr.ApplyUserId;
            response.ApplyUserName = pr.ApplyUserIdName;
            response.ApplyUserDept = pr.ApplyUserDept;
            response.ApplyUserDeptName = pr.ApplyUserDeptName;
            response.ApplyTime = pr.ApplyTime;
            response.PurchaserList = purchaser?.Where(x => x.Id.HasValue).Select(x => new KeyValuePair<Guid, string>((Guid)x.Id, x.Name));

            var bws = new List<PurBWApplication>();
            if (pdList.Any(a => a.BWApplicationId.HasValue))
            {
                var bwIds = pdList.Where(x => x.BWApplicationId.HasValue).Select(a => a.BWApplicationId.Value).ToList();
                bws = queryBW.Where(a => bwIds.Contains(a.Id)).ToList();
            }

            var costNatures = await dataverseService.GetCostNatureAsync();//获取费用性质
            foreach (var item in pdInfo)
            {
                var bw = bws.FirstOrDefault(a => a.PRDetailIds.Contains(item.PRDetailId.ToString()));
                item.OfflineNo = bw?.ApplicationCode;
                item.BWApplictionId = bw?.Id;
                item.ExemptType = bw?.ExemptType;
                item.BudgetRegion = pr.BudgetRegion?.ToString();
                item.BudgetRegionName = pr.BudgetRegionName;
                item.CompanyId = pr.CompanyId;
                item.CompanyName = pr.CompanyIdName;

                item.EstimateDateStr = item.EstimateDate.ToString("yyyy-MM-dd");
                //大区
                item.BudgetRegion = pr.BudgetRegion?.ToString();
                item.BudgetRegionName = pr.BudgetRegionName;
                //费用性质
                if (item.CostNature.HasValue)
                {
                    var costNature = costNatures.FirstOrDefault(a => a.Id == item.CostNature);
                    item.CostNatureName = costNature?.Name;
                }
                //采购人
                if (item.PurchaserId.HasValue)
                {
                    var purchaserItem = staff.FirstOrDefault(x => x.Id == item.PurchaserId);
                    item.PurchaserName = purchaserItem.Name;
                }
                //状态
                item.PushFlagName = EnumUtil.GetDescription(item.PushFlag);
            }

            var result = new PagedResultDto<PushDetail>(count, pdInfo);
            response.PushDetailList = result;

            return MessageResult.SuccessResult(response);
        }

        public async Task<IEnumerable<KeyValuePair<Guid, string>>> GetStaffByPositionAsync(Guid dept, PositionType position, WorkflowTypeName? workflowType)
        {
            //查Dataverse 岗位与流程的配置表 //采购订单申请审批流程的"主采购循环审批岗"

            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();

            var res = await dataverseService.GetStaffListByPositionAndFlow(dept, position);

            return res;
        }

        /// <summary>
        /// 我的采购推送退回  
        /// </summary>
        /// <param name="request"></param> 
        /// <returns></returns>
        public async Task<MessageResult> RollbackAsync(RollbackRequestDto request)
        {
            var prDetail = LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>();
            var updateprDetails = (await prDetail.GetQueryableAsync()).Where(a => request.Ids.Contains(a.Id)).ToList();
            if (!updateprDetails.Any())
            {
                return MessageResult.FailureResult("退回数据不存在");
            }
            var biddingIds = updateprDetails.Select(a => a.BiddingId);
            var bdRepo = await LazyServiceProvider.LazyGetService<IPurBDApplicationRepository>().GetQueryableAsync();
            var bdApproving = bdRepo.Where(a => a.Status == PurBDStatus.Approving && biddingIds.Contains(a.Id));
            if (bdApproving.Any())
            {
                return MessageResult.FailureResult("退回数据中有bidding在审批中，不允许退回");
            }
            var poRepo = await LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>().GetQueryableAsync();
            var poDetailRepo = await LazyServiceProvider.LazyGetService<IPurPOApplicationDetailsRepository>().GetQueryableAsync();
            poDetailRepo = poDetailRepo.Where(a => a.PRDetailId.HasValue && request.Ids.Contains(a.PRDetailId.Value));
            var poApprov = poRepo.Join(poDetailRepo, a => a.Id, b => b.POApplicationId, (a, b) => new { po = a, poDetail = b })
                .Where(a => a.po.Status == PurOrderStatus.Approving || a.po.Status == PurOrderStatus.Approved
                 && a.poDetail.PRDetailId.HasValue && request.Ids.Contains(a.poDetail.PRDetailId.Value));
            if (poApprov.Any())
            {
                return MessageResult.FailureResult("退回数据中有PO在审批中或已完成审批，不允许退回");
            }

            //修改主表状态
            var prRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>();
            var prIds = updateprDetails.Select(a => a.PRApplicationId).ToArray();
            var prs = await prRepository.GetListAsync(a => prIds.Contains(a.Id));
            prs.ForEach(a =>
            {
                if (a.Status == PurPRApplicationStatus.WaitForClose)
                    a.Status = PurPRApplicationStatus.Approved;
            });
            await prRepository.UpdateManyAsync(prs);

            //修改子表状态
            updateprDetails.ForEach(p =>
            {
                p.PushFlag = PushFlagEnum.Rollback;
                p.RollbackReason = request.RollbackReason;
            });
            await prDetail.UpdateManyAsync(updateprDetails);
            // 发送采购推送退单邮件给PR申请人
            await SendRollbackEmailAsync(request, prs);
            return MessageResult.SuccessResult();
        }

        async Task SendRollbackEmailAsync(RollbackRequestDto request, List<PurPRApplication> prs)
        {
            var approveService = LazyServiceProvider.LazyGetService<IApproveService>();
            string operatorUserName = string.Empty;

            if (CurrentUser.Id.HasValue)
            {
                var userId = CurrentUser.Id.Value;
                var identityUserRepository = LazyServiceProvider.LazyGetRequiredService<IIdentityUserRepository>();
                var operatorUser = await identityUserRepository.GetAsync(userId);
                operatorUserName = operatorUser?.Name ?? string.Empty;
            }

            foreach (var pr in prs)
            {
                try
                {
                    var recipientId = pr.TransfereeId ?? pr.ApplyUserId;
                    await approveService.SendReturnOrRollbackEmail(recipientId, operatorUserName, request.RollbackReason, pr.ApplicationCode, DateTime.Now);
                }
                catch (Exception ex)
                {
                    Logger.LogError($"SendRollbackEmailAsync error:{ex.Message}");
                    return;
                }
            }
        }

        /// <summary>
        /// 判断是否所有的AP行都已推送，AR行都已确认
        /// </summary>
        /// <param name="prApplicationId"></param>
        /// <returns></returns>
        public async Task<bool> IsAllPrDetailPushedAndConfirmed(Guid prApplicationId)
        {
            var prDetailQueryable = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var prDetails = prDetailQueryable.Where(a => a.PRApplicationId == prApplicationId).ToArray();
            //获取对冲的行
            var hedgeDetails = prDetails.Where(a => a.HedgePrDetailId.HasValue).ToArray();
            //除对冲行外的明细行
            var expectHedgeDetails = prDetails.Where(a => !a.HedgePrDetailId.HasValue && !hedgeDetails.Any(a1 => a1.HedgePrDetailId == a.Id)).ToArray();
            //判断是否所有的AP行都已推送，AR行都已确认
            var judgement = expectHedgeDetails.Where(a => a.PayMethod == PayMethods.AP).All(a => a.PushFlag == PushFlagEnum.Pushed) && expectHedgeDetails.Where(a => a.PayMethod == PayMethods.AR).All(a => a.IsVendorConfimed == true);
            return judgement;
        }

        /// <summary>
        /// 获取获取已推送 或 已确认 或 已经跟online meeting有交互的pr明细行数据
        /// </summary>
        /// <param name="prId"></param>
        /// <returns></returns>
        async Task<IEnumerable<Guid>> GetPushedAndConfirmedPrDetailsAsync(Guid prId)
        {
            var queryPr = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var queryPrDetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();

            //获取已推送 或 已确认 或 已经跟online meeting有交互的数据
            var prDetailIds = queryPrDetail.Where(a => a.PRApplicationId == prId && !a.HedgePrDetailId.HasValue && (a.PushFlag == PushFlagEnum.Pushed || a.IsVendorConfimed == true || (!string.IsNullOrEmpty(a.MeetingStatus) && a.MeetingStatus != OnlineMeetingStatus.NexBpmPushed))).Select(a => a.Id).ToArray();

            return prDetailIds;
        }

        /// <summary>
        /// 根据CompanyId查询PP里的采购推送人
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="isAssistant"></param>
        /// <returns></returns>
        public List<GetPurchasersResponseDto> GetPurchasers(Guid? companyId = null, bool isAssistant = false)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            return dataverseService.GetProcurementPushConfigAsync().GetAwaiterResult()
                .WhereIf(companyId.HasValue, a => a.CompanyId == companyId)
                .Where(a => a.IsAdmin == isAssistant)
                .Select(a => new GetPurchasersResponseDto
                {
                    Id = a.EmployeeId,
                    Name = a.EmployeeName,
                })
                .DistinctBy(a => a.Id)
                .ToList();
        }

        /// <summary>
        /// 获取有后续有效流程的PR明细行Id
        /// </summary>
        /// <param name="prId"></param>
        /// <returns></returns>
        public async Task<IEnumerable<Guid>> GetHasNextFlowPrDetailAsync(Guid prId)
        {
            var queryPr = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var queryPrDetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var queryPo = await LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>().GetQueryableAsync();
            var queryPoDetail = await LazyServiceProvider.LazyGetService<IPurPOApplicationDetailsRepository>().GetQueryableAsync();
            var queryGr = await LazyServiceProvider.LazyGetService<IPurGRApplicationRepository>().GetQueryableAsync();
            var queryGrDetail = await LazyServiceProvider.LazyGetService<IPurGRApplicationDetailRepository>().GetQueryableAsync();


            queryPrDetail = queryPrDetail.Where(a => a.PRApplicationId == prId);
            var queryHedgeRowIds = queryPrDetail.Where(a => a.HedgePrDetailId.HasValue).Select(a => a.HedgePrDetailId.Value);
            var prDetails = queryPrDetail.Where(a => !queryHedgeRowIds.Contains(a.Id) || true);


            var queryHedgePrDetails = queryPr.Join(queryPrDetail.Where(a => a.PRApplicationId == prId), a => a.Id, a => a.PRApplicationId, (a, b) => b).Where(a => a.HedgePrDetailId.HasValue).Select(a => a.HedgePrDetailId);

            //判断是否有后续流程
            var hasNextFlowPrDetailIds = queryPo.Join(queryPoDetail, a => a.Id, a => a.POApplicationId, (a, b) => new { a.Status, b.PRDetailId }).Where(a => a.Status != PurOrderStatus.Invalid && a.Status != PurOrderStatus.Rejected && queryHedgePrDetails.Contains(a.PRDetailId)).Select(a => a.PRDetailId.Value)
                .Union(queryGr.Join(queryGrDetail, a => a.Id, a => a.GRApplicationId, (a, b) => new { a.Status, b.PRDetailId }).Where(a => a.Status != PurGRStatus.PurGRApplicationStatus.Termination && a.Status != PurGRStatus.PurGRApplicationStatus.Terminationed && queryHedgePrDetails.Contains(a.PRDetailId)).Select(a => a.PRDetailId))
                .ToArray();

            return hasNextFlowPrDetailIds;

            //var queryPr = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            //var queryPrDetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            //var queryGr = await LazyServiceProvider.LazyGetService<IPurGRApplicationRepository>().GetQueryableAsync();
            //var queryGrDetail = await LazyServiceProvider.LazyGetService<IPurGRApplicationDetailRepository>().GetQueryableAsync();
            //var queryBw = await LazyServiceProvider.LazyGetService<IPurBWApplicationRepository>().GetQueryableAsync();

            //PurGRStatus.PurGRApplicationStatus[] statuses = [PurGRStatus.PurGRApplicationStatus.ReceivedGoods, PurGRStatus.PurGRApplicationStatus.Terminationed];
            ////反冲行
            //var queryHedgeRows = queryPrDetail.Where(a => a.PRApplicationId == prId && a.HedgePrDetailId.HasValue).Select(a => a.HedgePrDetailId.Value);
            ////排除反冲行
            //queryPrDetail = queryPrDetail.Where(a => a.PRApplicationId == prId && !queryHedgeRows.Contains(a.Id) && !a.HedgePrDetailId.HasValue);

            //var datas = queryPrDetail.Where(a => a.PayMethod == PayMethods.AR)
            //    .Join(queryGr.Where(a => a.PrId == prId && !statuses.Contains(a.Status)), a => a.PRApplicationId, a => a.PrId, (a, b) => new { PrDetail = a, Gr = b })
            //    .Join(queryGrDetail, a => new { GRApplicationId = a.Gr.Id, PrDetailId = a.PrDetail.Id }, a => new { a.GRApplicationId, PrDetailId = a.PRDetailId }, (a, b) => new { Type = "1", PrDetailId = a.PrDetail.Id, Status = new PurGRStatus.PurGRApplicationStatus?(a.Gr.Status) })
            //    .Concat
            //    (
            //        queryPrDetail.Where(a => a.PayMethod == PayMethods.AP && a.PushFlag == PushFlagEnum.Pushed)
            //            .GroupJoin(queryGr.Where(a => a.PrId == prId && !statuses.Contains(a.Status)), a => a.PRApplicationId, a => a.PrId, (a, b) => b)
            //            .SelectMany(a => a.DefaultIfEmpty(), (a, b) => b)
            //            .GroupJoin(queryGrDetail, a => a.Id, a => a.GRApplicationId, (a, b) => new { a.Status, GrDetails = b })
            //            .SelectMany(a => a.GrDetails.DefaultIfEmpty(), (a, b) => new { Type = "2", PrDetailId = b.PRDetailId, Status = new PurGRStatus.PurGRApplicationStatus?(a.Status) })
            //    )
            //    //.Concat
            //    //(
            //    //    queryPrDetail.Where(a => a.PRApplicationId == prId && a.PayMethod == PayMethods.AP && a.PushFlag != PushFlagEnum.Pushed)
            //    //        .Join(queryBw,a=>a.,a=>a.PRId)
            //    //);
            //    ;
        }

        /// <summary>
        /// 根据成本中心获取活动类型
        /// </summary>
        /// <param name="costcenterId"></param>
        /// <returns></returns>
        public async Task<IEnumerable<KeyValuePair<string, string>>> GetActiveTypeByCostcenterAsync(Guid costcenterId)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var costcenters = await dataverseService.GetCostcentersAsync(costcenterId.ToString());
            if (!costcenters.Any())
                return Array.Empty<KeyValuePair<string, string>>();

            var costcenter = costcenters.First();
            var activeTypes = await dataverseService.GetActiveTypeCostcenterMappingsAsync();

            var list = new List<KeyValuePair<string, string>>();
            foreach (var item in activeTypes)
            {
                var costcenterCodes = item.Costcenters.Split(",", StringSplitOptions.RemoveEmptyEntries);
                if (costcenterCodes.Any(a => string.Equals(a, costcenter.Code, StringComparison.CurrentCultureIgnoreCase)))
                    list.Add(new KeyValuePair<string, string>(item.ActiveTypeName, item.ActiveTypeName));
            }

            return list;
        }

        /// <summary>
        /// 关闭采购申请单
        /// </summary>
        /// <returns></returns>
        public async Task ClosePRApplication()
        {
            var jobLogService = LazyServiceProvider.LazyGetService<IScheduleJobLogService>();
            var log = jobLogService.InitSyncLog("ClosePRApplication");
            try
            {
                var queryPr = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
                var queryPrDetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
                var queryGr = await LazyServiceProvider.LazyGetService<IPurGRApplicationRepository>().GetQueryableAsync();
                var queryGrDetail = await LazyServiceProvider.LazyGetService<IPurGRApplicationDetailRepository>().GetQueryableAsync();

                var datas = queryPr.Where(a => a.Status == Enums.Purchase.PurPRApplicationStatus.WaitForClose)
                    .Join(queryPrDetail, a => a.Id, a => a.PRApplicationId, (a, b) => new { Pr = a, PrDetail = b })
                    .GroupJoin(queryGrDetail, a => a.PrDetail.Id, a => a.PRDetailId, (a, b) => new { a.Pr, a.PrDetail, GrDetails = b })
                    .SelectMany(a => a.GrDetails.DefaultIfEmpty(), (a, b) => new { a.Pr, a.PrDetail, GrDetail = b })
                    .GroupJoin(queryGr, a => a.GrDetail.GRApplicationId, a => a.Id, (a, b) => new { a.Pr, a.PrDetail, a.GrDetail, Grs = b })
                    .SelectMany(a => a.Grs.DefaultIfEmpty(), (a, b) => new { a.Pr, a.PrDetail, a.GrDetail, Gr = b })
                    .Select(a => new { a.Pr, a.PrDetail, GrStatus = a.Gr != null ? a.Gr.Status : new Enums.PurGRStatus.PurGRApplicationStatus?() })
                    .ToArray();

                //判断每个单子是否满足关单条件
                var groupDatas = datas.GroupBy(a => a.Pr.Id);
                log.RecordCount = groupDatas.Count();
                if (!groupDatas.Any())
                    return;

                var prApplicationRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>();
                foreach (var group in groupDatas)
                {
                    bool allArApValid = false, allErIcbinValid = false;
                    //反冲行
                    var hedgeRows = datas.Where(a => a.Pr.Id == group.Key && a.PrDetail.HedgePrDetailId.HasValue).ToArray();
                    var hedgeIds = hedgeRows.Select(a => a.PrDetail.Id).Union(hedgeRows.Select(a => a.PrDetail.HedgePrDetailId.Value)).ToArray();

                    #region AR/AP行，最后一次支付完成或收货终止，且预计日期早于或等于当前日期

                    var date = DateTime.Today;
                    //排除反冲行的所有ar和ap行
                    var arApRows = group.Where(a => !hedgeIds.Contains(a.PrDetail.Id) && (a.PrDetail.PayMethod == Enums.Purchase.PayMethods.AR || a.PrDetail.PayMethod == Enums.Purchase.PayMethods.AP)).ToArray();
                    allArApValid = arApRows.All(a => a.PrDetail.EstimateDate <= date && (a.GrStatus == Enums.PurGRStatus.PurGRApplicationStatus.Terminationed || a.GrStatus == Enums.PurGRStatus.PurGRApplicationStatus.ReceivedGoods));

                    #endregion

                    #region ER/ICB in行，预计日期+30天小于或等于当前日期

                    date = DateTime.Today.AddDays(-30);
                    //排除反冲行的所有er和icb-in行
                    var erIcbinRows = group.Where(a => !hedgeIds.Contains(a.PrDetail.Id) && a.PrDetail.PayMethod == Enums.Purchase.PayMethods.ER || a.PrDetail.PayMethod == Enums.Purchase.PayMethods.ICBIn).ToArray();
                    allErIcbinValid = erIcbinRows.All(a => a.PrDetail.EstimateDate <= date);

                    #endregion

                    //两种类型都Valid，则关闭该采购申请
                    if (allArApValid && allErIcbinValid)
                    {
                        var pr = group.First().Pr;
                        pr.Status = Enums.Purchase.PurPRApplicationStatus.Closed;
                        await prApplicationRepository.UpdateAsync(pr);
                    }
                }
            }
            catch (Exception ex)
            {
                log.IsSuccess = false;
                log.Remark = ex.ToString();
            }
            finally
            {
                jobLogService.SyncLog(log);
            }

        }

        /// <summary>
        /// 判断是否可以作废OM申请
        /// 如果PR包含的行有如下数据，进行作废时需要报错禁止提交作废：
        ///1、AR行，收货状态Not in（2，3）则不准作废；
        ///2、AP行（已推送），未发起收货申请 或 者收货状态Not in（2，3）则不准作废；
        ///3、AP行（未推送），如果有还在审批中的waiver/justification，则不准作废；
        /// </summary>
        /// <param name="prId"></param>
        /// <returns></returns>
        public async Task<bool> CanDeprecteOnlineMeetingAsync(Guid prId)
        {
            var queryPr = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var queryPrDetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var queryGr = await LazyServiceProvider.LazyGetService<IPurGRApplicationRepository>().GetQueryableAsync();
            var queryGrDetail = await LazyServiceProvider.LazyGetService<IPurGRApplicationDetailRepository>().GetQueryableAsync();
            var queryBw = await LazyServiceProvider.LazyGetService<IPurBWApplicationRepository>().GetQueryableAsync();

            PurGRStatus.PurGRApplicationStatus[] statuses = [PurGRStatus.PurGRApplicationStatus.ReceivedGoods, PurGRStatus.PurGRApplicationStatus.Terminationed];
            PurExemptStatus[] bwStatuses = [PurExemptStatus.Return, PurExemptStatus.Approving, PurExemptStatus.Approved];

            //反冲行
            var queryHedgeRows = queryPrDetail.Where(a => a.PRApplicationId == prId && a.HedgePrDetailId.HasValue).Select(a => a.HedgePrDetailId.Value);
            var prDetailIds = queryPrDetail.Where(a => a.PRApplicationId == prId && !queryHedgeRows.Contains(a.Id) && !a.HedgePrDetailId.HasValue).Select(a => a.Id).ToArray();
            queryPrDetail = queryPrDetail.Where(a => a.PRApplicationId == prId && prDetailIds.Contains(a.Id));

            //查询出不准作废的数据
            var datas = queryPrDetail.Where(a => a.PayMethod == PayMethods.AR)
                .Join(queryGr.Where(a => a.PrId == prId), a => a.PRApplicationId, a => a.PrId, (a, b) => b)
                .Join(queryGrDetail, a => a.Id, a => a.GRApplicationId, (a, b) => a)
                .Select(a => new { Type = "1", CanDeprecte = statuses.Contains(a.Status) })
                .Union
                (
                    queryPrDetail.Where(a => a.PayMethod == PayMethods.AP && a.PushFlag == PushFlagEnum.Pushed)
                        .GroupJoin(queryGr.Where(a => a.PrId == prId), a => a.PRApplicationId, a => a.PrId, (a, b) => b)
                        .SelectMany(a => a.DefaultIfEmpty(), (a, b) => b)
                        .GroupJoin(queryGrDetail, a => a.Id, a => a.GRApplicationId, (a, b) => new { Gr = a, GrDetails = b })
                        .SelectMany(a => a.GrDetails.DefaultIfEmpty(), (a, b) => a.Gr)
                        .Select(a => new { Type = "2", CanDeprecte = a != null && statuses.Contains(a.Status) })
                )
                .Union
                (
                    queryPrDetail.Where(a => a.PayMethod == PayMethods.AP && a.PushFlag != PushFlagEnum.Pushed)
                        .Join(queryBw.Where(a => bwStatuses.Contains(a.Status)), a => a.BWApplicationId, a => a.Id, (a, b) => b)
                        .Select(a => new { Type = "3", CanDeprecte = false })
                )
                .ToArray();

            return datas.All(a => a.CanDeprecte);
        }

        /// <summary>
        /// 根据消费大类和费用性质获取讲者身份类型
        /// </summary>
        /// <param name="consumeId"></param>
        /// <param name="costnatureId"></param>
        /// <returns></returns>
        public async Task<IEnumerable<DictionaryDto>> GetIdentityTypesAsync(Guid consumeId, Guid costnatureId)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var compensations = await dataverseService.GetCompensationAsync();

            var identityTypeIds = compensations.Where(a => a.Consume == consumeId && a.Costnature == costnatureId).Select(a => a.Identity);
            var allIdentityTypes = await dataverseService.GetDictionariesAsync(DictionaryType.IdentityType);

            var identityTypes = identityTypeIds.Join(allIdentityTypes, a => a, a => a.Id, (a, b) => b);
            return identityTypes;
        }

        /// <summary>
        /// 获取最新一条的审批历史数据
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        async Task<IEnumerable<WorkflowTask>> GetLatestWorkflowTasksAsync(params Guid[] ids)
        {
            var queryWorkflowTask = await LazyServiceProvider.LazyGetService<IWorkflowTaskReadonlyRepository>().GetQueryableAsync();
            //查询出特殊撤回的数据
            var latestWorkflowTasks = queryWorkflowTask.Where(a => ids.Contains(a.FormId))
                .GroupBy(a => a.FormId)
                .Select(a => a.OrderByDescending(a1 => a1.ApprovalTime).First())
                .ToArray();

            return latestWorkflowTasks;
        }

        /// <summary>
        /// OM 状态名
        /// </summary>
        /// <param name="status"></param>
        /// <returns></returns>
        private string GetStatusDescription(string status)
        {
            //是否是EPD用户
            if (!RoleExtension.EPD_ROLES.Intersect(CurrentUser.Roles, StringComparer.CurrentCultureIgnoreCase).Any())
                return "";
            switch (status)
            {
                case OnlineMeetingStatus.NexBpmPushed:
                    return "已推送";
                case OnlineMeetingStatus.OmActivated:
                    return "已激活";
                case OnlineMeetingStatus.OmSettledPushed:
                    return "已结算";
                case OnlineMeetingStatus.NexBpmDeprecated:
                    return "NexBPM作废";
                case OnlineMeetingStatus.OmDeprecated:
                    return "会议系统作废";
                default:
                    return "";
            }
        }

        async Task ExportPRApplication(GetPRApplicationListRequest request)
        {
            var queryPr = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var queryPrDetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var queryMasterBudget = await LazyServiceProvider.LazyGetService<IBdMasterBudgetRepository>().GetQueryableAsync();
            var querySubBudget = await LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>().GetQueryableAsync();
            var queryUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var queryPrProductApportionment = await LazyServiceProvider.LazyGetService<IPurPRApplicationProductApportionmentRepository>().GetQueryableAsync();
            var queryBudgetReturn = await LazyServiceProvider.LazyGetService<IBdBudgetReturnRepository>().GetQueryableAsync();
            var queryPo = await LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>().GetQueryableAsync();
            var queryPoDetail = await LazyServiceProvider.LazyGetService<IPurPOApplicationDetailsRepository>().GetQueryableAsync();
            var wfQuery = await LazyServiceProvider.LazyGetService<IWfApprovalTaskRepository>().GetQueryableAsync();
            var sqlRepository = LazyServiceProvider.LazyGetService<ISqlRepository>();

            var queryPoDetailGroup = queryPo.Join(queryPoDetail, a => a.Id, a => a.POApplicationId, (a, b) => new { PO = a, PODetail = b })
                .GroupBy(a => new { a.PODetail.PRDetailId, a.PO.Status })
                .Select(a => new { PRDetailId = a.Key.PRDetailId, a.Key.Status, ExchangeRate = (decimal?)a.Max(a => a.PO.ExchangeRate), TotalAmount = (decimal?)a.Sum(x => x.PODetail.TotalAmount), TotalAmountNoTax = (decimal?)a.Sum(x => x.PODetail.TotalAmountNoTax) });

            var queryBudgetReturn1 = queryBudgetReturn.GroupBy(a => new { a.PrId, a.ReturnSourceCode, a.PdRowNo })
                .Select(a => new { PrId = (Guid?)a.Key.PrId, ReturnSourceCode = a.Key.ReturnSourceCode, PdRowNo = (int?)a.Key.PdRowNo, Count = (int?)a.Count(), Amount = (decimal?)a.Sum(x => x.Amount) });

            //取WfApprovalTasks中最近的一条审批记录
            var queryWfTask = wfQuery.GroupBy(a => a.FormId).Select(a => a.OrderByDescending(a1 => a1.CreationTime).Select(a1 => new WfApprovalTaskDto { FormNo = a1.FormNo, StepDesc = a1.StepDesc, Approver = a1.Approver }).FirstOrDefault());

            var personCenterService = LazyServiceProvider.LazyGetService<IPersonCenterService>();
            var rolesIds = await personCenterService.GetIdsByRoleLevels(bizTypeCategory: ResignationTransfer.TaskFormCategory.PurchaseRequestApplication);
            queryPr = queryPr.ApplyRoleFilters(rolesIds, CurrentUser.Id.Value, x => x.ApplyUserBu, x => x.ApplyUserDept, x => x.SubBudgetId, x => x.ApplyUserId, x => x.TransfereeId, x => x.AgentId);

            var query = queryPr
                    .Where(a => a.Status != PurPRApplicationStatus.Draft)
                    .WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), a => a.ApplicationCode.Contains(request.ApplicationCode))
                    .WhereIf(request.Bu.HasValue, a => a.ApplyUserBu == request.Bu)
                    .WhereIf(request.Costcenter.HasValue, a => a.CostCenter == request.Costcenter)
                    .WhereIf(!string.IsNullOrEmpty(request.Applicant), a => a.ApplyUserIdName.Contains(request.Applicant))
                    .WhereIf(!string.IsNullOrEmpty(request.ApplicationDept), a => a.ApplyUserDeptName.Contains(request.ApplicationDept))
                    .WhereIf(request.Status != null, a => request.Status.Contains(a.Status))
                    .WhereIf(!string.IsNullOrEmpty(request.BudgetCode), a => a.BudgetCode.Contains(request.BudgetCode))
                    .WhereIf(!string.IsNullOrEmpty(request.SubBudgetCode), a => a.SubBudgetCode.Contains(request.SubBudgetCode))
                    .WhereIf(request.Company.HasValue, a => a.CompanyId == request.Company)
                    .WhereIf(request.StartDate.HasValue, a => a.ApplyTime >= request.StartDate)
                    .WhereIf(request.EndDate.HasValue, a => a.ApplyTime < request.EndDate.Value.Date.AddDays(1))
                    .Join(queryPrDetail, a => a.Id, a => a.PRApplicationId, (a, b) => new { pr = a, prDetail = b })
                    //取主预算描述
                    .GroupJoin(queryMasterBudget, a => a.pr.BudgetId, a => a.Id, (a, b) => new { a.pr, a.prDetail, MasterBudget = b })
                    .SelectMany(a => a.MasterBudget.DefaultIfEmpty(), (a, b) => new { a.pr, a.prDetail, MasterBudgetDesc = b.Description })
                    //取子预算描述，负责人
                    .GroupJoin(querySubBudget, a => a.pr.SubBudgetId, a => a.Id, (a, b) => new { a.pr, a.prDetail, a.MasterBudgetDesc, SubBudget = b })
                    .SelectMany(a => a.SubBudget.DefaultIfEmpty(), (a, b) => new { a.pr, a.prDetail, a.MasterBudgetDesc, SubBudgetDesc = b.Description, SubBudgetOwnerId = b.OwnerId })
                    .GroupJoin(queryUser, a => a.SubBudgetOwnerId, a => a.Id, (a, b) => new { a.pr, a.prDetail, a.MasterBudgetDesc, a.SubBudgetDesc, Users = b })
                    .SelectMany(a => a.Users.DefaultIfEmpty(), (a, b) => new { a.pr, a.prDetail, a.MasterBudgetDesc, a.SubBudgetDesc, SubBudgetOwnerName = b.Name })
                    //关联产品分摊，取分摊比例，产品信息
                    .GroupJoin(queryPrProductApportionment, a => a.prDetail.HedgePrDetailId ?? a.prDetail.Id, a => a.PRApplicationDetailId, (a, b) => new { a.pr, a.prDetail, a.MasterBudgetDesc, a.SubBudgetDesc, a.SubBudgetOwnerName, prProductApportionment = b })
                    .SelectMany(a => a.prProductApportionment.DefaultIfEmpty(), (a, b) => new { a.pr, a.prDetail, a.MasterBudgetDesc, a.SubBudgetDesc, a.SubBudgetOwnerName, prProductApportionment = b })
                    //关联预算返还
                    .GroupJoin(queryBudgetReturn1, a => new { PRID = (Guid?)a.prDetail.PRApplicationId, RowNo = (int?)a.prDetail.RowNo }, a => new { PRID = a.PrId, RowNo = a.PdRowNo }, (a, b) => new { a.pr, a.prDetail, a.MasterBudgetDesc, a.SubBudgetDesc, a.SubBudgetOwnerName, a.prProductApportionment, BudgetReturns = b })
                    .SelectMany(a => a.BudgetReturns.DefaultIfEmpty(), (a, b) => new { a.pr, a.prDetail, a.MasterBudgetDesc, a.SubBudgetDesc, a.SubBudgetOwnerName, a.prProductApportionment, BudgetReturn = b })
                    //关联PO明细，用于计算POVAT(RMB)
                    .GroupJoin(queryPoDetailGroup, a => a.prDetail.Id, a => a.PRDetailId, (a, b) => new
                    {
                        a.pr,
                        a.prDetail,
                        a.MasterBudgetDesc,
                        a.SubBudgetDesc,
                        a.SubBudgetOwnerName,
                        a.prProductApportionment,
                        a.BudgetReturn,
                        PODetail = b
                    })
                    .SelectMany(a => a.PODetail.DefaultIfEmpty(), (a, b) => new
                    {
                        a.pr,
                        a.prDetail,
                        a.MasterBudgetDesc,
                        a.SubBudgetDesc,
                        a.SubBudgetOwnerName,
                        a.prProductApportionment,
                        a.BudgetReturn,
                        PODetail = b
                    })
                    .GroupJoin(queryWfTask, a => a.pr.ApplicationCode, a => a.FormNo, (a, b) => new { a.pr, a.prDetail, a.MasterBudgetDesc, a.SubBudgetDesc, a.SubBudgetOwnerName, a.prProductApportionment, a.BudgetReturn, a.PODetail, WorkflowTask = b })
                    .SelectMany(a => a.WorkflowTask.DefaultIfEmpty(), (a, b) => new { a.pr, a.prDetail, a.MasterBudgetDesc, a.SubBudgetDesc, a.SubBudgetOwnerName, a.prProductApportionment, a.BudgetReturn, a.PODetail, WorkflowTask = b })
                    .Select(a => new PurPRApplicationReportDto()
                    {
                        ApplicationCode = a.pr.ApplicationCode,
                        ApplyTime = a.pr.ApplyTime,
                        ApplyUserId = a.pr.ApplyUserId,
                        ApplyUserIdName = a.pr.ApplyUserIdName,
                        ApplyUserDept = a.pr.ApplyUserDept,
                        ApplyUserDeptName = a.pr.ApplyUserDeptName,
                        AgentId = a.pr.AgentId,
                        TotalAmountRMB = a.pr.TotalAmountRMB,
                        Currency = a.pr.Currency,
                        CompanyId = a.pr.CompanyId,
                        CompanyIdName = a.pr.CompanyIdName,
                        ApplyUserBuName = a.pr.ApplyUserBuName,
                        CostCenter = a.pr.CostCenter,
                        BudgetCode = a.pr.BudgetCode,
                        BudgetDescription = a.MasterBudgetDesc,
                        SubBudgetId = a.pr.SubBudgetId,
                        SubBudgetCode = a.pr.SubBudgetCode,
                        SubBudgetDescription = a.SubBudgetDesc,
                        SubBudgetOwner = a.SubBudgetOwnerName,
                        Status = a.pr.Status,
                        RowNo = a.prDetail.RowNo,
                        PayMethod = a.prDetail.PayMethod.GetDescription(),
                        CompanyCode = a.pr.CompanyCode,
                        ApplyUserBu = a.pr.ApplyUserBu,
                        CostCenterCode = a.pr.CostCenterCode,
                        CostCenterName = a.pr.CostCenterName,
                        CostNatureCode = a.prDetail.CostNatureCode,
                        CostNatureName = a.prDetail.CostNatureName,
                        ProductCode = a.prProductApportionment == null ? string.Empty : a.prProductApportionment.ProductCode,
                        CityId = a.prDetail.CityId,
                        DetailTotalAmount = a.prProductApportionment == null ? a.prDetail.TotalAmount * 1 : a.prDetail.TotalAmount * Convert.ToDecimal(a.prProductApportionment.Ratio / 100),//按比例分摊
                        DetailTotalAmountRMB = a.prProductApportionment == null ? a.prDetail.TotalAmountRMB * 1 : a.prDetail.TotalAmountRMB * Convert.ToDecimal(a.prProductApportionment.Ratio / 100),
                        ////当该行有PO对应的预算返还记录时，计算该行在PO内对应行(可能多行)的总含税金额 - 总不含税金额，计算结果需要转换为0 - 原计算结果
                        //POVatRMB = a.POBudgetReturn == null || a.PODetail.Status != PurOrderStatus.Closed ? 0 : 0 - ((a.PODetail.TotalAmount - a.PODetail.TotalAmountNoTax) * Convert.ToDecimal(a.prProductApportionment == null ? 1 : a.prProductApportionment.Ratio / 100) * Convert.ToDecimal(a.PODetail.ExchangeRate)),
                        ////当该行有PO对应的预算返还记录时，计算该行的PO返还金额+上述计算出的PO VAT金额，计算结果需要转换为0-原计算结果
                        //POSavingRMB = a.BudgetReturn == null ? 0 : 0 - ((a.POBudgetReturn.Amount * Convert.ToDecimal(a.prProductApportionment == null ? 1 : a.prProductApportionment.Ratio / 100)) + (0 - ((a.PODetail.TotalAmount - a.PODetail.TotalAmountNoTax) * Convert.ToDecimal(a.prProductApportionment == null ? 1 : a.prProductApportionment.Ratio / 100) * Convert.ToDecimal(a.PODetail.ExchangeRate)))),
                        //GRVarRMB = a.GRBudgetReturn == null ? 0 : 0 - (a.GRBudgetReturn.Amount * Convert.ToDecimal(a.prProductApportionment == null ? 1 : a.prProductApportionment.Ratio / 100)),
                        //PAVarRMB = a.PABudgetReturn == null ? 0 : 0 - (a.PABudgetReturn.Amount * Convert.ToDecimal(a.prProductApportionment == null ? 1 : a.prProductApportionment.Ratio / 100)),
                        //CityIdName = a.prDetail.CityIdName,
                        //ProductName = a.prProductApportionment == null ? string.Empty : a.prProductApportionment.ProductName,
                        //Content = a.prDetail.Content,
                        //VendorName = a.prDetail.VendorName,
                        //RceNo = a.prDetail.RceNo,
                        //IcbAmount = a.prDetail.IcbAmount,
                        //CommitmentAmountRMB = a.prProductApportionment == null ? null : (a.prDetail.TotalAmountRMB * (a.prProductApportionment != null ? Convert.ToDecimal(a.prProductApportionment.Ratio / 100) : 0)
                        //        + ((a.POBudgetReturn == null ? 0 : 0 - ((a.PODetail.TotalAmount - a.PODetail.TotalAmountNoTax) * Convert.ToDecimal(a.prProductApportionment.Ratio / 100) * Convert.ToDecimal(a.PODetail.ExchangeRate))) ?? 0)
                        //        + ((a.POBudgetReturn == null ? 0 : 0 - ((a.POBudgetReturn.Amount * Convert.ToDecimal(a.prProductApportionment.Ratio / 100)) + (0 - ((a.PODetail.TotalAmount - a.PODetail.TotalAmountNoTax) * Convert.ToDecimal(a.prProductApportionment.Ratio / 100) * Convert.ToDecimal(a.PODetail.ExchangeRate))))) ?? 0)
                        //        + ((a.GRBudgetReturn == null ? 0 : 0 - (a.GRBudgetReturn.Amount * Convert.ToDecimal(a.prProductApportionment.Ratio / 100))) ?? 0)
                        //        + ((a.PABudgetReturn == null ? 0 : 0 - (a.PABudgetReturn.Amount * Convert.ToDecimal(a.prProductApportionment.Ratio / 100))) ?? 0)),
                        COA = a.prProductApportionment == null ?
                        $"{a.pr.CompanyCode}.{{0}}.{a.pr.CostCenterCode}.{a.prDetail.CostNatureCode}..{a.prDetail.CityIdName.Substring(a.prDetail.CityIdName.Length - 4)}"
                        : $"{a.pr.CompanyCode}.{{0}}.{a.pr.CostCenterCode}.{a.prDetail.CostNatureCode}.{a.prProductApportionment.ProductCode}.{a.prDetail.CityIdName.Substring(a.prDetail.CityIdName.Length - 4)}",
                        CurrentApprovalNode = a.pr.Status == PurPRApplicationStatus.Approving && !string.IsNullOrEmpty(a.WorkflowTask.StepDesc) ? a.WorkflowTask.StepDesc : string.Empty,
                        CurrentApprovalUser = a.pr.Status == PurPRApplicationStatus.Approving && !string.IsNullOrEmpty(a.WorkflowTask.ApproverText) ? a.WorkflowTask.ApproverText : string.Empty,
                        EstimateDate = a.prDetail.EstimateDate
                    });

            var datas = query.ToArray();

        }
    }
}
