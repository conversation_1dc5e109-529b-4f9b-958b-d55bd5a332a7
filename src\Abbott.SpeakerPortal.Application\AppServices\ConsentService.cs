﻿using Abbott.SpeakerPortal.Consent;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.Common;
using Abbott.SpeakerPortal.Contracts.Consent;
using Abbott.SpeakerPortal.Entities.Common.Consent;
using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.WeChat;

using Flurl.Http;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

using Org.BouncyCastle.Asn1.Ocsp;

using Senparc.CO2NET.Helpers.Serializers;

using System;
using System.Collections.Generic;
using System.Linq.Dynamic.Core.Tokenizer;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.ConsentServices
{
    /// <summary>
    /// ConsentService
    /// </summary>
    public class ConsentService : SpeakerPortalAppService, IConsentService
    {
        /// <summary>
        /// The service provider
        /// </summary>
        private readonly IServiceProvider _serviceProvider;

        /// <summary>
        /// The configuration
        /// </summary>
        private readonly IConfiguration _configuration;

        /// <summary>
        /// The logger
        /// </summary>
        private readonly ILogger<ConsentService> _logger;

        private readonly ICommonService _commonService;

        /// <summary>
        /// Initializes a new instance of the <see cref="ConsentService"/> class.
        /// </summary>
        /// <param name="serviceProvider">The service provider.</param>
        public ConsentService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _configuration = _serviceProvider.GetService<IConfiguration>();
            _logger = _serviceProvider.GetService<ILogger<ConsentService>>();
            _commonService = _serviceProvider.GetService<ICommonService>();
        }

        /// <summary>
        /// Verifies the whether user has accept consent.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<ConsentVerifyResponseDto> VerifyWhetherUserHasAcceptConsentAsync(ConsentVerifyRequestDto request)
        {
            var result = new ConsentVerifyResponseDto();
            var token = await GetConsentTokenAsync();
            var log = new SetOperationLogRequestDto();
            if (string.IsNullOrEmpty(token))
            {
                return result;
            }
            try
            {
                var url = $"{_configuration["Consent:BaseUrl"]!.TrimEnd('/')}/consent/verify/v1";
                var headers = new Dictionary<string, string>
                {
                    { "Content-Type", "application/json" },
                    {"Authorization" ,token}
                };
                log = _commonService.InitOperationLog("Consent", "获取Consent信息", url + "|" + JsonSerializer.Serialize(headers));
                var response = await url.WithHeaders(headers).PostJsonAsync(request);
                string responseData = await response.GetStringAsync();
                _commonService.LogResponse(log, responseData);
                if (responseData != null)
                    result = JsonSerializer.Deserialize<ConsentVerifyResponseDto>(responseData);
                result.ApiRequest = new ApiRequestDto()
                {
                    RequestUrl = url,
                    Method = "Post",
                    RequestBody = JsonSerializer.Serialize(request)
                };
                result.ApiRequest.ResponseContent = responseData;
            }
            catch (Exception ex)
            {
                _commonService.LogResponse(log, ex.ToString(), false);
                _logger.LogError($"There is an error in ConsentService's VerifyWhetherUserHasAcceptConsentAsync,the message is {ex.Message}");
                return null;
            }
            return result;
        }

        /// <summary>
        /// 根据consent code获取最新的consent版本和附件url
        /// </summary>
        /// <param name="consentCodes">The consent codes.</param>
        /// <returns></returns>
        public async Task<List<ConsentUrlResponseDto>> GetConsentInfoAsync(List<string> consentCodes)
        {
            var log = new SetOperationLogRequestDto();
            List<ConsentUrlResponseDto> result = new();
            var token = await GetConsentTokenAsync();
            if (string.IsNullOrEmpty(token))
            {
                _logger.LogError($"There is an error in ConsentService's GetConsentInfoAsync,the token is null");
                return null;
            }
            try
            {
                foreach (var consentCode in consentCodes)
                {
                    var consentResponse = new ConsentUrlResponseDto();
                    var headers = new Dictionary<string, string>
                    {
                        {"Authorization" ,token}
                    };
                    var url = $"{_configuration["Consent:BaseUrl"]!.TrimEnd('/')}/consent/v1?code={consentCode}";
                    log = _commonService.InitOperationLog("Consent", "获取Consent信息", (url) + "|" + JsonSerializer.Serialize(headers));
                    consentResponse = await url.WithHeaders(headers).GetJsonAsync<ConsentUrlResponseDto>();
                    _commonService.LogResponse(log, JsonSerializer.Serialize(consentResponse));
                    consentResponse.ApiRequest = new ApiRequestDto() { RequestUrl = url, Method = "Get" };
                    consentResponse.ApiRequest.ResponseContent = JsonSerializer.Serialize(consentResponse);
                    result.Add(consentResponse);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
                _commonService.LogResponse(log, ex.ToString(), false);
                _logger.LogError($"There is an error in ConsentService's GetConsentInfoAsync,the message is {ex.Message}");
                return null;
            }
            return result;
        }

        /// <summary>
        /// Accepts the or reject consent asynchronous.
        /// </summary>
        /// <param name="requests">The requests.</param>
        /// <returns></returns>
        public async Task<ConsentAcceptOrRejectResponseDto> AcceptOrRejectConsentAsync(List<SetAcceptDto> requests)
        {
            ConsentAcceptOrRejectResponseDto result = new ConsentAcceptOrRejectResponseDto();
            var log = new SetOperationLogRequestDto();
            var token = await GetConsentTokenAsync();
            if (string.IsNullOrEmpty(token))
            {
                _logger.LogError($"There is an error in ConsentService's AcceptOrRejectConsentAsync,the token is null");
                return null;
            }
            try
            {
                var url = $"{_configuration["Consent:BaseUrl"]!.TrimEnd('/')}/consent/accept/v1";
                var headers = new Dictionary<string, string>
                {
                    { "Content-Type", "application/json" },
                    {"Authorization" ,token}
                };
                log = _commonService.InitOperationLog("Consent", "接受或者拒绝consent", url + "|" + JsonSerializer.Serialize(headers));
                var response = await url.WithHeaders(headers).PostJsonAsync(requests);
                string responseData = await response.GetStringAsync();
                _commonService.LogResponse(log, responseData);

                if (responseData != null)
                    result = JsonSerializer.Deserialize<ConsentAcceptOrRejectResponseDto>(responseData);
                result.ApiRequest = new ApiRequestDto()
                {
                    RequestUrl = url,
                    Method = "Post",
                    RequestBody = JsonSerializer.Serialize(requests)
                };
                result.ApiRequest.ResponseContent = responseData;


                if (result.Success && result.Code == 200)
                {
                    var ConsentSignedQuery = LazyServiceProvider.LazyGetService<IConsentSignedRepository>();
                    //记录已同意的协议和版本
                    var signedList = new List<ConsentSigned>();
                    foreach (var item in requests)
                    {
                        if (!item.ActionType.Equals("Accepted", StringComparison.CurrentCultureIgnoreCase)) continue;
                        signedList.Add(new ConsentSigned
                        {
                            OneId = item.OneId,
                            AppUserId = item.AppUserId,
                            ConsentCode = item.ConsentCode,
                            ConsentVersion = item.Version.ToString()
                        });
                    }
                    await ConsentSignedQuery.InsertManyAsync(signedList);
                }
                return result;
            }
            catch (Exception ex)
            {
                _commonService.LogResponse(log, ex.ToString(), false);
                _logger.LogError($"There is an error in ConsentService's AcceptOrRejectConsentAsync,the message is {ex.Message}");
                return null;
            }
        }


        /// <summary>
        /// 获取consent token
        /// </summary>
        /// <returns></returns>
        private async Task<string> GetConsentTokenAsync()
        {
            string bearerToken = string.Empty;
            var log = new SetOperationLogRequestDto();
            try
            {
                var url = $"{_configuration["Consent:BaseUrl"]!.TrimEnd('/')}/consent/authorize/v1";
                var headers = new Dictionary<string, string>
                {
                    { "Content-Type", "application/json" }
                };
                var parameters = new Dictionary<string, string>
                {
                        //{ "clientId", _keyVaultUtil.GetKeyVaultAsync(KeyVaultKeys.CONSENT_CLIENT_ID).Result.Value},
                        //{ "secret", _keyVaultUtil.GetKeyVaultAsync(KeyVaultKeys.CONSENT_CLIENT_SECRET).Result.Value}
                        { "clientId", _configuration["Consent:clientId"]},
                        { "secret", _configuration["Consent:secret"]}
                    };
                log = _commonService.InitOperationLog("Consent", "获取token", JsonSerializer.Serialize(parameters));
                var tokenData = await url.WithHeaders(headers).PostJsonAsync(parameters);
                _commonService.LogResponse(log, JsonSerializer.Serialize(tokenData));
                var token = await tokenData.GetStringAsync();
                var tokenDto = JsonSerializer.Deserialize<ConsentTokenDto>(token);
                if (tokenData != null && tokenDto?.Data != null && !string.IsNullOrWhiteSpace(tokenDto?.Data.AccessToken))
                    bearerToken = $"Bearer {tokenDto?.Data.AccessToken}";
            }
            catch (Exception ex)
            {
                _commonService.LogResponse(log, ex.ToString(), false);
                _logger.LogError($"There is an error in ConsentService's GetConsentTokenAsync,the message is {ex.Message}");
                Console.WriteLine(ex.Message);
            }
            return bearerToken;
        }

        /// <summary>
        /// 获取本App所有的Consent
        /// </summary>
        /// <returns></returns>
        public async Task<List<ConsentDetail>> GetConsentsAsync()
        {
            var _configuration = LazyServiceProvider.GetService<IConfiguration>();
            string consentUrl = _configuration["Consent:BaseUrl"];
            string consentCodes = _configuration["Consent:ConsentCode"];
            string consentNames = _configuration["Consent:ConsentName"];
            var codes = consentCodes.Split(',');
            var names = consentNames.Split(',');
            var token = await GetConsentTokenAsync();
            var log = new SetOperationLogRequestDto();
            var response = new List<ConsentDetail>();
            var headers = new Dictionary<string, string>
            {
                {"Authorization" ,token}
            };
            foreach (var code in codes)
            {
                var url = $"{consentUrl!.TrimEnd('/')}/consent/v1?code={code}";
                log = _commonService.InitOperationLog("Consent", "获取Consent信息", (url) + "|" + JsonSerializer.Serialize(headers));
                var res = await url.WithHeaders(headers).GetJsonAsync<ConsentUrlResponseDto>();
                _commonService.LogResponse(log, JsonSerializer.Serialize(response));
                if (res.Success)
                {
                    var dt = new ConsentDetail
                    {
                        ConsentName = names[Array.IndexOf(codes, code)],
                        Category = res.Data.Category,
                        ConsentCode = res.Data.ConsentCode,
                        Url = res.Data.Url,
                        Version = res.Data.Version
                    };
                    response.Add(dt);
                }
            }
            return response;
        }
    }
}
