
CREATE PROCEDURE dbo.sp_PurPRApplications
AS 
BEGIN
	
--采购申请单-基础信息
--初始化xml_11
--SELECT 
--	    ProcInstId,
--	    STRING_AGG(up_Id, ', ') WITHIN GROUP (ORDER BY up_Id) AS up_Id
--	    into PLATFORM_ABBOTT_Stg.dbo.XML_11
--	FROM 
--	    (select ProcInstId,
--			up_Id
--		from (
--		select XmlContent,ProcInstId,
--		up_Id.value('.', 'nvarchar(100)') as up_Id 
--		FROM PLATFORM_ABBOTT_Stg.dbo.ODS_T_FORMINSTANCE_GLOBAL
--		CROSS APPLY XmlContent.nodes('/root/QuoteAttachmentGrid/row/up_Id') AS XMLTable(up_Id)) B	
--		)q
--		GROUP BY  ProcInstId ;
--		
--初始化xml_12
IF OBJECT_ID(N'PLATFORM_ABBOTT_Stg.dbo.XML_12', N'U') IS NOT NULL
BEGIN
	PRINT(N'已经初始化过'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select b.* 
	into #xml
	from (select *  from (
	select  ROW_NUMBER() over( PARTITION by ProcInstId  order by expenseCategory_Text desc) as a,* 
	from PLATFORM_ABBOTT_Stg.dbo.ODS_AUTO_BIZ_T_ProcurementApplication_Info )B 
	where a=1) a
	join PLATFORM_ABBOTT_Stg.dbo.ODS_T_FORMINSTANCE_GLOBAL b
	on a.ProcInstId =b.ProcInstId ;	
select ProcInstId,
	RowData.value('(approvalLevel/text())[1]', 'nvarchar(50)') AS approvalLevel,
    RowData.value('(approvalPersonEmpId/text())[1]', 'nvarchar(50)') AS approvalPersonEmpId,
    RowData.value('(action/text())[1]', 'nvarchar(50)') AS action,
    RowData.value('(approvalTime/text())[1]', 'nvarchar(50)') AS approvalTime
    into PLATFORM_ABBOTT_Stg.dbo.XML_12 
	from #xml A
	CROSS APPLY XmlContent.nodes('/root/approvalHistoryGrid/row') AS XMLTable(ROWDATA)
PRINT(N'初始化完成'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
		
--创建临时表#ABTPI
PRINT N'创建临时表#ABTPI';

select * into #ABTPI from (
	select  ROW_NUMBER() over( PARTITION by ProcInstId  order by expenseCategory_Text desc) as a,* 
	from PLATFORM_ABBOTT_Stg.dbo.ODS_AUTO_BIZ_T_ProcurementApplication_Info )B 
	where a=1
PRINT(N'创建临时表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'));

--创建表PLATFORM_ABBOTT_Stg.dbo.PurPRApplications_tmp
select newid() AS Id,--自动生成的uuid
* into #PurPRApplications_tmp from (
select
DISTINCT 
--newid() AS Id,--自动生成的uuid
ABTPI.ProcInstId,
ABTPI.serialNumber AS ApplicationCode,--申请单号
processStatus AS Status,--参考下方附录状态mapping表
ABTPI.applicantEmpId AS ApplyUserId,--以该ID匹配至员工主数据
ABTPI.applicationDate AS ApplyTime,--
ABTPI.BUId AS ApplyUserBu,--以该ID匹配至对应的BU级别组织主数据
costCenterCode AS CostCenter,--以该ID匹配至对应的成本中心主数据
tpes.ParentNumber  AS BudgetId,--主预算ID，以AUTO_BIZ_T_ProcurementApplication_Info.budgetNumber查询该表中的Number，找到其所属的主预算编码，再填回对应主预算的ID，若无法查询到主预算或无法匹配至预算ID，对应的ID可能需要在主预算表里加入dummy数据以便找回ID
MeetingTheme AS MeetingTitle,--
'NULL' AS MeetingNo,--
case when Pilot='True' then '1' else '0' end  AS IsEsignUsed,--如为"True"则填写为1，否则填写为0
case when ProPushObject=N'Esign' then N'E-Sign'
when ProPushObject='Online' then N'雅会议（online meeting）'end  AS PushSystem,--如为"Online"则填写为"雅会议（online meeting）"，如为"Esign"则填写为"E-Sign"
ABTPI.SelectPersonId AS AgentId,--以该ID匹配至员工主数据
ABTPI.MeetingSelectPersonId AS HostVendorId,--以该ID匹配至BPCSPMFVM.venlad，基于匹配后的结果以VMCMPY+VNDERX组合查询BPCSAVM对应VCMPNY+VENDOR的组合，取匹配出的BPCSAVM.ID，若匹配失败则留空
ABTPI.expenseCategory_Value AS ExpenseType,--以该ID匹配至消费大类ID
ABTPI.regional_Value AS BudgetRegion,--以该ID匹配至大区ID
ABTPI.productCbo_Value AS ProductIds,--以该ID匹配至产品ID
ABTPI.f_code AS CompanyId,--以该编码匹配至公司主数据的"公司编码"得到ID
ABTPI.PRTotalAmount AS TotalAmount,--
ABTPI.remark AS Remark,--
ABTPI.lateRemark AS PrLateDescription,--
ABTPI.launchactiveCity AS ActiveLaunchCityId,--活动发起城市，以城市名称匹配至城市主数据后带回省份及城市编码
ABTPI.activeCity AS ActiveHostCityId,--活动举行城市，以城市名称匹配至城市主数据后带回省份及城市编码
ABTPI.activePlace AS AcitveHostAddress,--
ABTPI.activeDate AS AcitveDate,--
ABTPI.activetype_Value AS ActiveType,--以该编码匹配至字典中的"活动类型"得到字典Code
--XmlContent.value('(/root/SupplierApplication_supplierInfoBlock_MainStore/Projecttype_Value)[1]', 'nvarchar(255)') 
cast('todo' as nvarchar(255)) AS ProjectType,--以ProcInstId找到对应的单据xml后查询该值，以该编码匹配至字典中的"项目类型"得到字典Code
DoctorNum AS DoctorsNum,--
ConferenceTypeCode AS MeetingType,--以该编码匹配至字典中的"会议类型"得到字典Code
IsSeriesMeeting AS IsSeriesMeeting,--以该编码匹配至字典中的"系列会类型"得到字典Code(需要补充加入，对应数据迁移也需要补充)
MainPRNo AS MainMeetingPR,--基于单号匹配至本表的ApplicationCode以找回对应的主会场采购申请ID
--checkbox1/checkbox22 
--case when XmlContent.value('(/root/ProcurementApplication_ThirdPartyBlock_MainStore/checkbox1)[1]', 'nvarchar(255)')='true' then N'非营利性的非医疗保健机构（如医学会、基金会）'
--when  XmlContent.value('(/root/ProcurementApplication_ThirdPartyBlock_MainStore/checkbox22)[1]', 'nvarchar(255)')='true' then N'其他' end
cast('todo' as nvarchar(255)) AS OrganizerNature,--会议主办方性质，以ProcInstId找到对应的单据xml后查询在ProcurementApplication_ThirdPartyBlock_MainStore板块下的记录，其中checkbox1代表"非营利性的非医疗保健机构（如医学会、基金会）"，checkbox22代表"其他"，对应的value为true时则代表勾选了该选项，需要以选项文字匹配至字典中的"主办方性质"得到字典Code
--checkbox2/checkbox3/checkbox4/checkbox5/checkbox7 
--case when XmlContent.value('(/root/ProcurementApplication_ThirdPartyBlock_MainStore/checkbox2)[1]', 'nvarchar(255)')='true' then 'SponsorshipType001'
--when XmlContent.value('(/root/ProcurementApplication_ThirdPartyBlock_MainStore/checkbox3)[1]', 'nvarchar(255)')='true' then 'SponsorshipType002')
--when XmlContent.value('(/root/ProcurementApplication_ThirdPartyBlock_MainStore/checkbox4)[1]', 'nvarchar(255)')='true' then 'SponsorshipType004')
--when XmlContent.value('(/root/ProcurementApplication_ThirdPartyBlock_MainStore/checkbox5)[1]', 'nvarchar(255)')='true' then 'SponsorshipType005')
--when XmlContent.value('(/root/ProcurementApplication_ThirdPartyBlock_MainStore/checkbox7)[1]', 'nvarchar(255)')='true' then 'NotApplicable') end 
cast('todo' as nvarchar(255)) AS SponsorshipType,--赞助类型，以ProcInstId找到对应的单据xml后查询在ProcurementApplication_SelfHostedBlock_MainStore板块下的记录，其中checkbox2代表"赞助个人参加雅培自办会议"，checkbox3代表"赞助个人参加第三方会议中的雅培卫星会/专场"，checkbox4代表"赞助个人参加第三方会议（仅限医疗器械、诊断事业部以外的事业部或部门）"，checkbox5代表"赞助个人参加第三方组织的医疗程序培训（仅限医疗器械、诊断事业部的事业部）"，checkbox7代表"以上项目均不适用",对应的value为true时则代表勾选了该选项，需要以选项文字匹配至字典中的"赞助类型"得到字典Code
cast('todo' as nvarchar(255)) AS ActiveLeader,--对于消费大类为雅培自办会/赞助个人参加专业教育/活动 - 国内时，填写在ProcurementApplication_SelfHostedBlock_MainStore板块下的对应字段
--ThirdActivityLeader AS ,--对于消费大类为第三方会议支持时，填写在ProcurementApplication_ThirdPartyBlock_MainStore板块下的对应字段
--SelfMeetingName 
--XmlContent.value('(/root/ProcurementApplication_SelfHostedBlock_MainStore/SelfActivityLeader)[1]', 'nvarchar(255)')
cast('todo' as nvarchar(255))AS MeetingName,--对于消费大类为雅培自办会/赞助个人参加专业教育/活动 - 国内时，填写在ProcurementApplication_SelfHostedBlock_MainStore板块下的对应字段
--ThirdMeetingName AS ,--对于消费大类为第三方会议支持时，填写在ProcurementApplication_ThirdPartyBlock_MainStore板块下的对应字段
--ThirdConferenceOrganizer 
cast('todo' as nvarchar(255))AS OrganizerName,--
--SelfMeetingTime 
cast('todo' as nvarchar(255))AS MeetingDate,--对于消费大类为雅培自办会/赞助个人参加专业教育/活动 - 国内时，填写在ProcurementApplication_SelfHostedBlock_MainStore板块下的对应字段
--ThirdMeetingTime AS ,--对于消费大类为第三方会议支持时，填写在ProcurementApplication_ThirdPartyBlock_MainStore板块下的对应字段
'' AS MeetingLocation,--会议详细地址(新增字段，迁移时留空即可)
--SelfParticipateNumber 
cast('todo' as nvarchar(255)) AS NumberOfProfessionals,--
--checkbox6 
cast('todo' as nvarchar(255)) AS ChoiceReason,--赞助类型，以ProcInstId找到对应的单据xml后查询在ProcurementApplication_SelfHostedBlock_MainStore板块下的记录，其中checkbox6代表"确认选择对象的专业领域与会议主题相符合，且有必要参加"，该字段对应的value为true时则代表勾选了该选项，此处填写为1，否则填写为0
--ThirdObjectives 
cast('todo' as nvarchar(255)) AS SupportReason,--
'' AS RequireMediaReason,--撰写文章或录制视频的说明(新增字段，迁移时留空即可)
--SelfProfessionalField 
cast('todo' as nvarchar(255)) AS AttendeeExpertise,--
'' AS MarketResearchLeader,--计划的市场调研主导方(新增字段，迁移时留空即可)
'' AS MarketResearchCompany,--第三方市场调研公司名称(新增字段，迁移时留空即可)
'' AS MarketResearchReason,--开展该市场调研的理由和目标/目的(新增字段，迁移时留空即可)
'' AS MarketResearchResult,--计划的调研细节和交付成果(新增字段，迁移时留空即可)
'' AS MarketResearchResultUsedFor,--简要描述如何利用从本次市场调研中获得的信息/交付成果(新增字段，迁移时留空即可)
'' AS PatientInfo,--获得患者信息的计划(新增字段，迁移时留空即可)
'' AS SPChoiceAndPayStandard,--专业服务提供者的具体选择标准及计酬标准(新增字段，迁移时留空即可)
--up_id 
cast('todo' as nvarchar(2000))AS SupportFiles,--支持文档，以ProcInstId查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到QuoteAttachmentGrid下所有的up_Id，填入匹配出的NexBPM支持文件ID，多个支持文件以英文逗号分隔
'{}' AS ExtraProperties,--N/A
'' AS ConcurrencyStamp,--N/A
ABTPI.applicantEmpId AS CreationTime,--填充为ApplyTime即可
ABTPI.applicationDate AS CreatorId,--填充为ApplyUserId即可
'' AS LastModificationTime,--默认为空
'' AS LastModifierId,--默认为空
'0' AS IsDeleted,--默认为0
'' AS DeleterId,--默认为空
'' AS DeletionTime,--默认为空
--up_id 
cast('todo' as nvarchar(2000))AS AdditionalFiles,--补录文档，以ProcInstId查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到OthersAttachmentGrid下所有的up_Id，填入匹配出的NexBPM支持文件ID，多个支持文件以英文逗号分隔
ABTPI.CoverDepartment AS HospitalDepartments,--EPD的科室名称，无法匹配至医院主数据，可能需要考虑此处
ABTPI.CoverHospital AS Hospitals,--EPD的医院名称
'' AS IsIncludeHcpTravelLodgingFee,--默认为0(费用性质包含旅行社会务费时此时才会标记为1，但历史BPM中没有这个费用性质)
ABTPI.BUId AS ApplyUserBuName,--以该ID作为res_code在T_Resource中查询出对应的res_name后填入
ABTPI.MeetingControl AS ActiveNo,--
ABTPI.applicantDept_Value AS ApplyUserDept,--
ABTPI.applicantDept_Text AS ApplyUserDeptName,--
ABTPI.SelectPersonId AS AgentIdName,--以该ID作为emp_id查询T_Employee后得到emp_name以供填入
ABTPI.applicantEmpName AS ApplyUserIdName,--
----"approvalTime 
cast('todo' as nvarchar(255)) AS ApprovedDate,--对于状态为""发起人终止""的单据，查询对应ProcInstID对应的XML文件中approvalHistoryGrid内的审批记录：
----a.若最新一个""action""为""作废""，且按时间排序倒数第二个""action""为""撤回""，则查询该行内""ApprovalLevel""名称是否为""Expense循环审批""/""财务审核""/""财务总监审批""/""GM审批""/""Salesdirector""/""MKTdirector""/""合规审计岗审批""/""指定采购岗审批""则代表该单据是在审批通过前撤回并作废的，则此处留空；若""ApprovalLevel""名称并非如上任一值，则按时间排序往前找到名称为""Expense循环审批""/""财务审核""/""财务总监审批""/""GM审批""的记录中最晚一条记录的approvalTime，作为审批通过时间
----b.若最新一个""action""为""作废""，且按时间排序倒数第二个""action""为""退回""，则代表该单据被审批人退回，则此处留空
----c.若最新一个""action""为""拒绝""，则代表该单据被审批人退回，则此处留空
----d.若最新一个""action""为""通过""，则以该单号作为Scenes去查询T_TPM_InterfaceLog中Method为MeetingCancelVerification且IsSuccess为1的记录，若能找到对应记录代表该PR审批通过后在online meeting处进行了作废，此时查询该版块最新一条记录对应的approvalTime，作为审批通过时间
----e.如以上条件均无法匹配出，则留空该时间"
----"approvalTime AS ,--对于状态为""重发起""的单据，查询对应ProcInstID对应的XML文件中approvalHistoryGrid内的审批记录：
----a.若最新一个""action""为""撤回""，则查询该行内""ApprovalLevel""名称是否为""Expense循环审批""/""财务审核""/""财务总监审批""/""GM审批""/""Salesdirector""/""MKTdirector""/""合规审计岗审批""/""指定采购岗审批""则代表该单据是在审批通过前撤回的，则此处留空；若""ApprovalLevel""名称并非如上任一值，则按时间排序往前找到名称为""Expense循环审批""/""财务审核""/""财务总监审批""/""GM审批""的记录中最晚一条记录的approvalTime，作为审批通过时间
----b.若最新一个""action""为""退回""，则代表该单据被审批人退回，则此处留空
----c.如以上条件均无法匹配出，则留空该时间"
----FinishDate AS ,--对于状态为"供应商确认"/"审批完毕，等待关闭"/"财务关闭"/"完成"的单据，查询对应ProcInstID对应的最新一条记录对应的完成时间，即作为审批通过时间
tpes.ParentNumber AS BudgetCode,--主预算编码，以AUTO_BIZ_T_ProcurementApplication_Info.budgetNumber查询该表中的Number，找到其所属的主预算编码，如无法找到对应数据则留空
ABTPI.regional_Text AS BudgetRegionName,--
--Date 
cast('todo' as nvarchar(255))AS ClosedDate,--(自动关单记录)以单据SerialNumber查询该表中的PRFormCode以得到关闭时间
--FinishDate AS ,--(人工关单记录)以单据ProcInstId查询该表中的ProcInstId后，取ActName="财务关闭"的记录中，FinishDate最大的记录以得到关闭时间
ABTPI.f_code AS CompanyCode,--
ABTPI.company_Text AS CompanyIdName,--
ABTPI.f_code AS CompanyShortName,--以该编码匹配至公司主数据的"公司编码"得到ID后，填入对应的"缩写名称"
Res_Data1 AS CostCenterCode,--以AUTO_BIZ_T_ProcurementApplication_Info_PR.costCenterCode查询该表中的Res_Code后得到对应的值
oabtpaip.costCenter AS CostCenterName,--
ABTPI.expenseCategory_Value AS ExpenseTypeCode,--以该值匹配消费大类后，填入对应的"消费大类编号"
 ABTPI.expenseCategory_Text AS ExpenseTypeName,--
ABTPI.MeetingSelectPersonName AS HostVendorIdName,--主持人姓名
ABTPI.productCbo_Text AS ProductIdsName,--
'0' AS SavingAmount,--默认填为0，已不使用expenseCategory_Text
ABTPI.budgetNumber AS SubBudgetCode,--子预算编码
ABTPI.budgetNumber AS SubBudgetId,--以该代码匹配至子预算的代码，以找回子预算ID；若无法匹配至预算ID(例如部分Code可能形如"YXHD006"，该Code实际为与BPM对接的另一个系统中的预算ID)，对应的ID可能需要在子预算表里加入dummy数据以便找回ID
Emp_AD_Mail_Address AS AgentEmail,--被代理人邮箱，基于SelectPersonId对应该表的Emp_Id字段以得到对应信息
ABTPI.MeetingSelectPersonId AS HostVendorEmail,--主持人邮箱
cast('todo' as nvarchar(255)) AS MeetingStatus,--当Pilot=""true""且基于上方逻辑查询的ApprovedDate有值时才需要填入这个字段，否则该字段留空；填写逻辑如下：(如下顺序表示匹配优先级，若某一PR单已命中第一条规则，则不需要继续查询后续逻辑)
--1.在T_TPM_InterfaceLog，以PR单号查询Scenes字段，并以Method=""UpdateCampaignInActive""查询是否有记录，如有则此处标记为1003，代表BPM作废成功
--2.在T_TPM_InterfaceLog，以PR单号查询Scenes字段，并以Method=""MeetingCancelVerification""查询是否有记录，如有则此处标记为1004，代表会议系统作废成功
--3.在T_ERS_MeetingStatusInfo，以PR单号查询SerialNumber，如查询到的状态为""已结算""，则标记为1002，代表会议已结算；如查询到的状态为""已开始""/""已结束""则标记为1001，代表会议已激活
--4.如以上条件均不满足，但PR的状态是""审批完毕，等待关闭""/""财务关闭""/""完成""，则标记为1002，代表会议已结算
--5.如以上条件均不满足，但PR的状态是""发起人终止""，则标记为1003，代表BPM作废成功
--6.如以上条件均不满足，则标记为1000，代表会议推送成功，但尚未进入后续流程"
--status AS ,--
cast('todo' as nvarchar(255))  AS IsShowExpenseStep,--基于ProcInstId匹配至对应的单据xml文件，若该flag为1则此处填写为0，否则此处填写为1
--XmlContent.value('(/root/ProcurementApplication_ThirdPartyBlock_MainStore/Other)[1]', 'nvarchar(255)')  
cast('todo' as nvarchar(255)) AS OecExceptionNumber--OEC例外编号，ProcurementApplication_ThirdPartyBlock_MainStore板块下的Other
from #ABTPI ABTPI
left join PLATFORM_ABBOTT_Stg.dbo.ODS_Form_6e32f7dd7c4e49aea79949009c3bf7ae f6e
on f6e.ProcInstId =ABTPI.ProcInstId 
left join (select ProcInstId,max(costCenterCode) costCenterCode,max(costCenter) costCenter from PLATFORM_ABBOTT_Stg.dbo.ODS_AUTO_BIZ_T_ProcurementApplication_Info_PR group by ProcInstId
) oabtpaip  
on oabtpaip.ProcInstId =ABTPI.ProcInstId
left join PLATFORM_ABBOTT_Stg.dbo.ODS_T_Pur_ExpenseBudget_SubInfo tpes
on ABTPI.budgetNumber =tpes.Number 
left join PLATFORM_ABBOTT_Stg.dbo.spk_consume sc
on sc.spk_BPMCode=ABTPI.expenseCategory_Value
left join PLATFORM_ABBOTT_Stg.dbo.ods_T_RESOURCE tr
on oabtpaip.costCenterCode=tr.Res_Code
left join PLATFORM_ABBOTT_Stg.dbo.ods_T_EMPLOYEE te
on ABTPI.SelectPersonId=te.Emp_Id
--and oabtpaip.ProcInstId =tpes.ProcInstId
--left join PLATFORM_ABBOTT_Stg.dbo.ODS_T_ClosedPRRecord
--left join PLATFORM_ABBOTT_Stg.dbo.ODS_T_FORMINSTANCE_GLOBAL a
--on ABTPI.ProcInstId =a.ProcInstId
--left join PLATFORM_ABBOTT_Stg.dbo.spk_staffmasterdata ss on ABTPI.applicantEmpId=ss.bpm_id
--left join PLATFORM_ABBOTT_Stg.dbo.spk_organizationalmasterdata so on ABTPI.BUId=so.spk_BPMCode
--where ABTPI.serialNumber='P2406130005'
)A
PRINT(N'创建成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))


--批量更新
update a set a.ProjectType=b.ProjectType,
a.SponsorshipType=b.SponsorshipType,
a.ActiveLeader=b.ActiveLeader,
a.MeetingName=b.MeetingName,
a.OrganizerName=b.OrganizerName,
a.MeetingDate=b.MeetingDate,
a.NumberOfProfessionals=b.NumberOfProfessionals,
a.ChoiceReason=b.ChoiceReason,
a.SupportReason=b.SupportReason,
a.AttendeeExpertise=b.AttendeeExpertise,
a.OecExceptionNumber=b.OecExceptionNumber,
a.IsShowExpenseStep=b.IsSpecialOperate,
a.OrganizerNature=b.OrganizerNature
from #PurPRApplications_tmp a
left join (
select ABTPI.ProcInstId,spk_name,
XmlContent.value('(/root/ProcurementApplication_BudgetInfoBlock_MainStore/Projecttype_Value)[1]', 'nvarchar(255)') ProjectType,
case when XmlContent.value('(/root/ProcurementApplication_SelfHostedBlock_MainStore/checkbox2)[1]', 'nvarchar(255)')='true' then 'SponsorshipType001'
when XmlContent.value('(/root/ProcurementApplication_SelfHostedBlock_MainStore/checkbox3)[1]', 'nvarchar(255)')='true' then 'SponsorshipType002'
when XmlContent.value('(/root/ProcurementApplication_SelfHostedBlock_MainStore/checkbox4)[1]', 'nvarchar(255)')='true' then 'SponsorshipType004'
when XmlContent.value('(/root/ProcurementApplication_SelfHostedBlock_MainStore/checkbox5)[1]', 'nvarchar(255)')='true' then 'SponsorshipType005'
when XmlContent.value('(/root/ProcurementApplication_SelfHostedBlock_MainStore/checkbox7)[1]', 'nvarchar(255)')='true' then 'NotApplicable'end SponsorshipType,
case when spk_name=N'雅培自办会' or spk_name=N'赞助个人参加专业教育/活动 - 国内' 
then XmlContent.value('(/root/ProcurementApplication_SelfHostedBlock_MainStore/SelfActivityLeader)[1]', 'nvarchar(255)')
when  spk_name=N'第三方会议支持' 
then  XmlContent.value('(/root/ProcurementApplication_ThirdPartyBlock_MainStore/ThirdActivityLeader)[1]', 'nvarchar(255)')
end  AS ActiveLeader,
case when spk_name=N'雅培自办会' or spk_name=N'赞助个人参加专业教育/活动 - 国内'  
then XmlContent.value('(/root/ProcurementApplication_SelfHostedBlock_MainStore/SelfActivityLeader)[1]', 'nvarchar(255)')
when  spk_name=N'第三方会议支持' 
then XmlContent.value('(/root/ProcurementApplication_ThirdPartyBlock_MainStore/ThirdActivityLeader)[1]', 'nvarchar(255)') end
AS MeetingName,
XmlContent.value('(/root/ProcurementApplication_ThirdPartyBlock_MainStore/ThirdConferenceOrganizer)[1]', 'nvarchar(255)') 
AS OrganizerName,
case when spk_name=N'雅培自办会' or spk_name=N'赞助个人参加专业教育/活动 - 国内'  
then XmlContent.value('(/root/ProcurementApplication_SelfHostedBlock_MainStore/SelfMeetingTime)[1]', 'nvarchar(255)')
when  spk_name=N'第三方会议支持' 
then XmlContent.value('(/root/ProcurementApplication_ThirdPartyBlock_MainStore/ThirdMeetingTime)[1]', 'nvarchar(255)') end 
AS MeetingDate,
XmlContent.value('(/root/ProcurementApplication_SelfHostedBlock_MainStore/SelfParticipateNumber)[1]', 'nvarchar(255)') AS NumberOfProfessionals,
case when 
XmlContent.value('(/root/ProcurementApplication_SelfHostedBlock_MainStore/checkbox6)[1]', 'nvarchar(255)')='true' then '1'
else '0' end AS ChoiceReason,
XmlContent.value('(/root/ProcurementApplication_ThirdPartyBlock_MainStore/ThirdObjectives)[1]', 'nvarchar(255)') AS SupportReason,
XmlContent.value('(/root/ProcurementApplication_SelfHostedBlock_MainStore/SelfProfessionalField)[1]', 'nvarchar(255)') AS AttendeeExpertise,
case when XmlContent.value('(/root/ProcurementApplication_hiddenBlock_MainStore/IsSpecialOperate)[1]', 'nvarchar(255)')='1' then '0' else '0' end  AS IsSpecialOperate,
XmlContent.value('(/root/ProcurementApplication_ThirdPartyBlock_MainStore/Other)[1]', 'nvarchar(255)') AS OecExceptionNumber,
case when XmlContent.value('(/root/ProcurementApplication_ThirdPartyBlock_MainStore/checkbox1)[1]', 'nvarchar(255)')='true' then N'非营利性的非医疗保健机构（如医学会、基金会）'
when  XmlContent.value('(/root/ProcurementApplication_ThirdPartyBlock_MainStore/checkbox22)[1]', 'nvarchar(255)')='true' then N'其他' end as OrganizerNature
from #ABTPI as ABTPI 
join 
PLATFORM_ABBOTT_Stg.dbo.ODS_T_FORMINSTANCE_GLOBAL z
on ABTPI.ProcInstId =z.ProcInstId 
left join PLATFORM_ABBOTT_Stg.dbo.spk_consume sc 
on ABTPI.expenseCategory_Value=sc.spk_BPMCode
)b
on a.ProcInstId=b.ProcInstId
PRINT N'更新完成'


--更新AdditionalFiles，SupportFiles
update a set a.AdditionalFiles=b.up_Id,a.SupportFiles=b.up_Id from #PurPRApplications_tmp a
left join (
	SELECT 
	    ProcInstId,
	    STRING_AGG(up_Id, ', ') WITHIN GROUP (ORDER BY up_Id) AS up_Id
	FROM 
	    PLATFORM_ABBOTT_Stg.dbo.XML_11 q
		GROUP BY  ProcInstId
	 )b
on a.ProcInstId=b.ProcInstId
 PRINT(N'更新完成'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))

 --更新ClosedDate
update a set a.ClosedDate=b.ClosedDate from #PurPRApplications_tmp a
left join ( 
	select a.ProcInstId,[Date] as ClosedDate from #ABTPI a
	left join PLATFORM_ABBOTT_Stg.dbo.ods_T_ClosedPRRecord b
	on a.SerialNumber=b.PRFormCode
	UNION 
	select a.ProcInstId,max(FinishDate) as ClosedDate from #ABTPI a
	left join PLATFORM_ABBOTT_Stg.dbo.ods_T_PROCESS_Historys b
	on a.ProcInstId=b.ProcInstId
	where ActName=N'财务关闭'
	group by a.ProcInstId
) b
on a.ProcInstId=b.ProcInstId
PRINT(N'更新完成'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'));

WITH RankedActions AS (
    SELECT 
	    ProcInstId,
		approvalLevel,
		approvalPersonEmpId,
		[action],
		approvalTime,
        ROW_NUMBER () over (PARTITION by ProcInstId order by approvalTime desc ) AS RowNum
    FROM PLATFORM_ABBOTT_Stg.dbo.xml_12  -- 替换为你的实际表名
)
,action_info as (
SELECT 
		ProcInstId,
		approvalLevel,
		approvalPersonEmpId,
		approvalTime,
		firstAction,
		secondAction,
       CASE
           WHEN firstAction = N'作废' AND secondAction = N'退回' THEN '1'
           WHEN firstAction = N'作废' AND secondAction = N'撤回' THEN '0'
           ELSE '2' -- 或者返回其他默认值
       END AS [Result]
FROM (
    SELECT r1.ProcInstId,
    		r1.approvalLevel,
			r1.approvalPersonEmpId,
			r1.approvalTime,
           r1.[action] AS firstAction,
           r2.[action] AS secondAction
    FROM RankedActions r1
JOIN RankedActions r2 ON r1.ProcInstId = r2.ProcInstId
    WHERE r1.RowNum = 1 -- 第一条记录
      AND r2.RowNum = 2 -- 第二条记录
) AS ActionPairs
)
,scenes_info as (
SELECT 
	[Method],
	IsSuccess,
	LEFT(value, CHARINDEX('_', value + '_') - 1) AS serialNumber
FROM (SELECT [Parameter] as jsonData,[Method],IsSuccess,ID,Scenes
from PLATFORM_ABBOTT_Stg.dbo.ODS_T_TPM_InterfaceLog ) ScenesData
CROSS APPLY STRING_SPLIT(scenes, ',')
)
select a.*,
case when [Result]='0' and approvalLevel in (N'Expense循环审批',N'财务审核',N'财务总监审批',N'GM审批',N'Salesdirector',N'MKTdirector',N'合规审计岗审批',N'指定采购岗审批')
	 then 'NULL' 
	 when [Result]='0' and approvalLevel not in (N'Expense循环审批',N'财务审核',N'财务总监审批',N'GM审批',N'Salesdirector',N'MKTdirector',N'合规审计岗审批',N'指定采购岗审批')
	 then approvalTime
	 when firstAction=N'拒绝' Then 'NULL'
	 when firstAction=N'同意' Then case when  [Method]='MeetingCancelVerification' and c.IsSuccess='1' then approvalTime else 'NULL' end 
	 when approvalLevel in (N'供应商确认',N'审批完毕，等待关闭',N'财务关闭',N'完成') then d.FinishDate
	 ELSE 'NULL'
END AS ApprovedDate
--into #action_info
from action_info A
left join PLATFORM_ABBOTT_Stg.dbo.ODS_AUTO_BIZ_T_ProcurementApplication_Info B
on a.ProcInstId=b.ProcInstId 
left join scenes_info C
on b.serialNumber=c.serialNumber
left join (select ProcInstId,FinishDate,ROW_NUMBER () over(PARTITION by ProcInstId order by [FinishDate] desc) rn from PLATFORM_ABBOTT_Stg.dbo.[ods_T_PROCESS_Historys]) D
on a.ProcInstId=d.ProcInstId and rn=1

update a set a.ApprovedDate=b.ApprovedDate from #PurPRApplications_tmp a
left join (select ProcInstId,max(ApprovedDate) as ApprovedDate from #action_info group by ProcInstId) b
on a.ProcInstId=b.ProcInstId
PRINT(N'ApprovedDate更新完成'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))

update a set a.Status=b.Status from #PurPRApplications_tmp a
left join (
select 
a.ProcInstId,
case when a.Status=N'发起人终止' then 
	case when b.operate=N'作废' then '6' 
	when b.operate=N'拒绝' then '4' 
		else '6' end 
	when a.Status=N'审批完毕，等待关闭' or a.Status=N'财务关闭' then 
		case when c.PaymentType='AP' and [Result] ='9' then '9'
		else '3' end
	when a.Status=N'N/A(不迁移草稿)' then '1' 
	when a.Status=N'N/A(原BPM若未审批完，则退回到重新发起状态，让用户重新提交申请)' then '2' 
	when a.Status=N'重发起/审批中' then '5' 
	when a.Status=N'完成' then '10' 
	end as Status
from  #PurPRApplications_tmp a 
left  join  (select *,ROW_NUMBER () over(partition by serialNumber order by OperateTime desc ) rn from ods_T_Return_Reject_Info) b 
on a.ApplicationCode=b.serialNumber and rn=1
left join  (SELECT 
    ProcInstId,
    PaymentType,
    CASE 
        WHEN COUNT(CASE WHEN PurchaseEmpId  IS NULL　or PurchaseEmpId='' THEN 1 END) > 0 THEN 3
        ELSE 9
    END AS Result
FROM ods_T_Pur_PRItems_Info
GROUP BY ProcInstId,PaymentType) c
on a.ProcInstId=c.ProcInstId
) b
on a.ProcInstId=b.ProcInstId
PRINT(N'Status更新完成'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))


 IF OBJECT_ID(N'PLATFORM_ABBOTT_Stg.dbo.PurPRApplications_tmp', N'U') IS NOT NULL
BEGIN
    update a 
    set a.ProcInstId                     =b.ProcInstId
       ,a.ApplicationCode                 =b.ApplicationCode
       ,a.Status                          =b.Status
       ,a.ApplyUserId                     =b.ApplyUserId
       ,a.ApplyTime                       =b.ApplyTime
       ,a.ApplyUserBu                     =b.ApplyUserBu
       ,a.CostCenter                      =b.CostCenter
       ,a.BudgetId                        =b.BudgetId
       ,a.MeetingTitle                    =b.MeetingTitle
       ,a.MeetingNo                       =b.MeetingNo
       ,a.IsEsignUsed                     =b.IsEsignUsed
       ,a.PushSystem                      =b.PushSystem
       ,a.AgentId                         =b.AgentId
       ,a.HostVendorId                    =b.HostVendorId
       ,a.ExpenseType                     =b.ExpenseType
       ,a.BudgetRegion                    =b.BudgetRegion
       ,a.ProductIds                      =b.ProductIds
       ,a.CompanyId                       =b.CompanyId
       ,a.TotalAmount                     =b.TotalAmount
       ,a.Remark    =b.Remark
       ,a.PrLateDescription               =b.PrLateDescription
       ,a.ActiveLaunchCityId   =b.ActiveLaunchCityId
       ,a.ActiveHostCityId       =b.ActiveHostCityId
       ,a.AcitveHostAddress               =b.AcitveHostAddress
       ,a.AcitveDate                      =b.AcitveDate
       ,a.ActiveType                      =b.ActiveType
       ,a.ProjectType                     =b.ProjectType
       ,a.DoctorsNum                      =b.DoctorsNum
       ,a.MeetingType                     =b.MeetingType
       ,a.IsSeriesMeeting                 =b.IsSeriesMeeting
       ,a.MainMeetingPR                   =b.MainMeetingPR
       ,a.OrganizerNature                 =b.OrganizerNature
       ,a.SponsorshipType                 =b.SponsorshipType
       ,a.ActiveLeader                    =b.ActiveLeader
       ,a.MeetingName                     =b.MeetingName
       ,a.OrganizerName                   =b.OrganizerName
       ,a.MeetingDate                     =b.MeetingDate
       ,a.MeetingLocation                 =b.MeetingLocation
       ,a.NumberOfProfessionals           =b.NumberOfProfessionals
       ,a.ChoiceReason                    =b.ChoiceReason
       ,a.SupportReason                   =b.SupportReason
       ,a.RequireMediaReason              =b.RequireMediaReason
       ,a.AttendeeExpertise               =b.AttendeeExpertise
       ,a.MarketResearchLeader            =b.MarketResearchLeader
       ,a.MarketResearchCompany           =b.MarketResearchCompany
       ,a.MarketResearchReason            =b.MarketResearchReason
       ,a.MarketResearchResult            =b.MarketResearchResult
       ,a.MarketResearchResultUsedFor     =b.MarketResearchResultUsedFor
       ,a.PatientInfo                     =b.PatientInfo
       ,a.SPChoiceAndPayStandard          =b.SPChoiceAndPayStandard
       ,a.SupportFiles                    =b.SupportFiles
       ,a.ExtraProperties                 =b.ExtraProperties
       ,a.ConcurrencyStamp                =b.ConcurrencyStamp
       ,a.CreationTime                    =b.CreationTime
       ,a.CreatorId                       =b.CreatorId
       ,a.LastModificationTime            =b.LastModificationTime
       ,a.LastModifierId                  =b.LastModifierId
       ,a.IsDeleted                       =b.IsDeleted
       ,a.DeleterId                       =b.DeleterId
       ,a.DeletionTime                    =b.DeletionTime
       ,a.AdditionalFiles                 =b.AdditionalFiles
       ,a.HospitalDepartments             =b.HospitalDepartments
       ,a.Hospitals                       =b.Hospitals
       ,a.IsIncludeHcpTravelLodgingFee    =b.IsIncludeHcpTravelLodgingFee
       ,a.ApplyUserBuName                 =b.ApplyUserBuName
       ,a.ActiveNo                        =b.ActiveNo
       ,a.ApplyUserDept                   =b.ApplyUserDept
       ,a.ApplyUserDeptName               =b.ApplyUserDeptName
       ,a.AgentIdName                     =b.AgentIdName
       ,a.ApplyUserIdName                 =b.ApplyUserIdName
       ,a.ApprovedDate                    =b.ApprovedDate
       ,a.BudgetCode                      =b.BudgetCode
       ,a.BudgetRegionName                =b.BudgetRegionName
       ,a.ClosedDate                      =b.ClosedDate
       ,a.CompanyCode                     =b.CompanyCode
       ,a.CompanyIdName                   =b.CompanyIdName
       ,a.CompanyShortName                =b.CompanyShortName
       ,a.CostCenterCode                  =b.CostCenterCode
       ,a.CostCenterName                  =b.CostCenterName
       ,a.ExpenseTypeCode                 =b.ExpenseTypeCode
       ,a.ExpenseTypeName                 =b.ExpenseTypeName
       ,a.HostVendorIdName                =b.HostVendorIdName
       ,a.ProductIdsName                  =b.ProductIdsName
       ,a.SavingAmount                    =b.SavingAmount
       ,a.SubBudgetCode   =b.SubBudgetCode
 ,a.SubBudgetId                     =b.SubBudgetId
       ,a.AgentEmail                      =b.AgentEmail
       ,a.HostVendorEmail                 =b.HostVendorEmail
       ,a.MeetingStatus                =b.MeetingStatus
       ,a.IsShowExpenseStep               =b.IsShowExpenseStep
       ,a.OecExceptionNumber              =b.OecExceptionNumber
       ,a.TotalAmountRMB              =b.TotalAmount
	from PLATFORM_ABBOTT_Stg.dbo.PurPRApplications_tmp a
	left join #PurPRApplications_tmp b on a.ProcInstId = b.ProcInstId
	
	insert into PLATFORM_ABBOTT_Stg.dbo.PurPRApplications_tmp
	select  a.Id
			,a.ProcInstId
			,a.ApplicationCode
			,a.Status
			,a.ApplyUserId
			,a.ApplyTime
			,a.ApplyUserBu
			,a.CostCenter
			,a.BudgetId
			,a.MeetingTitle
			,a.MeetingNo
			,a.IsEsignUsed
			,a.PushSystem
			,a.AgentId
			,a.HostVendorId
			,a.ExpenseType
			,a.BudgetRegion
			,a.ProductIds
			,a.CompanyId
			,a.TotalAmount
			,a.Remark
			,a.PrLateDescription
			,a.ActiveLaunchCityId
			,a.ActiveHostCityId
			,a.AcitveHostAddress
			,a.AcitveDate
			,a.ActiveType
			,a.ProjectType
			,a.DoctorsNum
			,a.MeetingType
			,a.IsSeriesMeeting
			,a.MainMeetingPR
			,a.OrganizerNature
			,a.SponsorshipType
			,a.ActiveLeader
			,a.MeetingName
			,a.OrganizerName
			,a.MeetingDate
			,a.MeetingLocation
			,a.NumberOfProfessionals
			,a.ChoiceReason
			,a.SupportReason
			,a.RequireMediaReason
			,a.AttendeeExpertise
			,a.MarketResearchLeader
			,a.MarketResearchCompany
			,a.MarketResearchReason
			,a.MarketResearchResult
			,a.MarketResearchResultUsedFor
			,a.PatientInfo
			,a.SPChoiceAndPayStandard
			,a.SupportFiles
			,a.ExtraProperties
			,a.ConcurrencyStamp
			,a.CreationTime
			,a.CreatorId
			,a.LastModificationTime
			,a.LastModifierId
			,a.IsDeleted
			,a.DeleterId
			,a.DeletionTime
			,a.AdditionalFiles
			,a.HospitalDepartments
			,a.Hospitals
			,a.IsIncludeHcpTravelLodgingFee
			,a.ApplyUserBuName
			,a.ActiveNo
			,a.ApplyUserDept
			,a.ApplyUserDeptName
			,a.AgentIdName
			,a.ApplyUserIdName
			,a.ApprovedDate
			,a.BudgetCode
			,a.BudgetRegionName
			,a.ClosedDate
			,a.CompanyCode
			,a.CompanyIdName
			,a.CompanyShortName
			,a.CostCenterCode
			,a.CostCenterName
			,a.ExpenseTypeCode
			,a.ExpenseTypeName
			,a.HostVendorIdName
			,a.ProductIdsName
			,a.SavingAmount
			,a.SubBudgetCode
			,a.SubBudgetId
			,a.AgentEmail
			,a.HostVendorEmail
			,a.MeetingStatus
			,a.IsShowExpenseStep
			,a.OecExceptionNumber
			,a.TotalAmount as TotalAmountRMB
	from #PurPRApplications_tmp a
	where NOT EXISTS (SELECT * FROM PLATFORM_ABBOTT_Stg.dbo.PurPRApplications_tmp WHERE ProcInstId=a.ProcInstId)
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
	BEGIN
    --落成实体表
    select *,TotalAmount as TotalAmountRMB
        into PLATFORM_ABBOTT_Stg.dbo.PurPRApplications_tmp from #PurPRApplications_tmp
    -- select * from #PurPRApplications_tmp
	  PRINT(N' 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	  
end;

