CREATE PROCEDURE dbo.sp_BdMasterBudgets
AS 
BEGIN
	
select * 
into #BdMasterBudgets_tmp
from (
select 
newid() AS Id,--自动生成的uuid
a.Number AS Code,--
BUId AS BuId,--以该编码匹配至组织主数据后找回"BU"层级的组织ID
Description AS Description,--
OwnerId AS OwnerId,--以该ID匹配至员工主数据
Capital AS Capital,--
Budget AS BudgetAmount,--
IsEnable AS Status,--
case when (Remark <> null or Remark <> '') and  Remark not like '********%' then Remark else '' end  AS Remark,--以Number查询该表Number，将operateDate最早记录的对应Remark填入，若为空或填为"********"则留空
'{}' AS ExtraProperties,--默认填写为"{}"
'' AS ConcurrencyStamp,--?
min(b.OperateDate)  over(partition by a.Number)  AS CreationTime,--以Number查询该表Number，将operateDate最早记录的对应OperateDate填入
FIRST_VALUE(b.OperateEmpId) over(partition by a.Number order by  operateDate)  AS CreatorId,--以Number查询该表Number，将operateDate最早记录的对应OperateEmpId找出后以该ID匹配至员工主数据，若该值填写为1则填写为默认用户(admin)
max(b.OperateDate) over(partition by a.Number)   AS LastModificationTime,--以Number查询该表Number，将operateDate最新记录的对应OperateDate填入
FIRST_VALUE(b.OperateEmpId) over(partition by a.Number  order by  operateDate  desc )  AS LastModifierId,--以Number查询该表Number，将operateDate最新记录的对应OperateEmpId找出后以该ID匹配至员工主数据，若该值填写为1则填写为默认用户(admin)
'0' AS IsDeleted,--默认为0(BPM的删除功能是hard delete，已删除的不会有记录)
'' AS DeleterId,--默认为空
'' AS DeletionTime,--默认为空
Year AS Year,--主预算年份信息
row_number() over(partition by a.Number order by  operateDate)  rn
from PLATFORM_ABBOTT_Stg.dbo.ODS_T_Pur_ExpenseBudget_MainInfo a 
left join PLATFORM_ABBOTT_Stg.dbo.ODS_T_Pur_ExpenseBudget_LogInfo b
on a.Number =b.Number 
) z
where rn=1

 IF OBJECT_ID(N'PLATFORM_ABBOTT_Stg.dbo.BdMasterBudgets_tmp ', N'U') IS NOT NULL
	BEGIN
		update a 
		set a.Code                = b.Code
            ,a.BuId                = b.BuId
            ,a.Description         = b.Description
            ,a.OwnerId             = b.OwnerId
            ,a.Capital             = b.Capital
            ,a.BudgetAmount        = b.BudgetAmount
            ,a.Status              = b.Status
            ,a.Remark              = b.Remark
            ,a.ExtraProperties     = b.ExtraProperties
            ,a.ConcurrencyStamp    = b.ConcurrencyStamp
            ,a.CreationTime        = b.CreationTime
            ,a.CreatorId           = b.CreatorId
            ,a.LastModificationTime= b.LastModificationTime
            ,a.LastModifierId      = b.LastModifierId
            ,a.IsDeleted           = b.IsDeleted
            ,a.DeleterId           = b.DeleterId
            ,a.DeletionTime        = b.DeletionTime
            ,a.Year                = b.Year
            ,a.rn                  = b.rn
        from PLATFORM_ABBOTT_Stg.dbo.BdMasterBudgets_tmp a
        left join #BdMasterBudgets_tmp b on a.Code = b.Code
        
        insert into PLATFORM_ABBOTT_Stg.dbo.BdMasterBudgets_tmp 
        select a.Id
               ,a.Code
               ,a.BuId
               ,a.Description
               ,a.OwnerId
               ,a.Capital
               ,a.BudgetAmount
               ,a.Status
               ,a.Remark
               ,a.ExtraProperties
               ,a.ConcurrencyStamp
               ,a.CreationTime
               ,a.CreatorId
               ,a.LastModificationTime
               ,a.LastModifierId
               ,a.IsDeleted
               ,a.DeleterId
               ,a.DeletionTime
               ,a.Year
               ,a.rn
         from  #BdMasterBudgets_tmp a
         where not EXISTS (select * from PLATFORM_ABBOTT_Stg.dbo.BdMasterBudgets_tmp where Code = a.Code)
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into PLATFORM_ABBOTT_Stg.dbo.BdMasterBudgets_tmp from #BdMasterBudgets_tmp
    -- select * from #vendor_tbl
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END

END;
