﻿//using Abbott.SpeakerPortal.AppServices.STicket;
//using Abbott.SpeakerPortal.Consts;
//using Abbott.SpeakerPortal.Contracts.Common.Approve;
//using Abbott.SpeakerPortal.Contracts.Common.PowerApp;
//using Abbott.SpeakerPortal.Contracts.ReturnAndExchange;
//using Abbott.SpeakerPortal.Dataverse;
//using Abbott.SpeakerPortal.Domain.Shared.Models;
//using Abbott.SpeakerPortal.Entities.EFlow.Exchange;
//using Abbott.SpeakerPortal.Entities.EFlow.STicket;
//using Abbott.SpeakerPortal.Enums;
//using Abbott.SpeakerPortal.IntermediateDB.Repositories;
//using Abbott.SpeakerPortal.Utils;
//using Microsoft.Azure.CognitiveServices.Vision.ComputerVision.Models;
//using Microsoft.EntityFrameworkCore;
//using Microsoft.Extensions.Logging;
//using Newtonsoft.Json;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;
//using Volo.Abp.ObjectMapping;
//using static Abbott.SpeakerPortal.Consts.EntitiesNameConsts;
//using Abbott.SpeakerPortal.Extension;
//using Abbott.SpeakerPortal.Contracts.STicket;
//using Senparc.Weixin.WxOpen.Entities;
//using Volo.Abp.Application.Dtos;

//namespace Abbott.SpeakerPortal.AppServices.ReturnAndExchange
//{
//    public class ExchangeService : SpeakerPortalAppService, IExchangeService
//    {
//        private readonly ILogger<ExchangeService> _logger;
//        public ExchangeService(ILogger<ExchangeService> logger)
//        {
//            this._logger = logger;
//        }
//        /// <summary>
//        /// 创建申请单
//        /// </summary>
//        /// <param name="request">The request.</param>
//        /// <returns></returns>
//        public async Task<MessageResult> CreateExchangeApplicationAsync(CreateExChangeApplicationRequestDto request, bool isSubmit = false)
//        {
//            var exchangeReponsitory = LazyServiceProvider.LazyGetService<IExchangeApplicationReponsitory>();
//            var exchangeDetaiReponsitory = LazyServiceProvider.LazyGetService<IExchangeApplicationDetailReponsitory>();
//            var exchangeApplication = ObjectMapper.Map<CreateExChangeApplicationRequestDto, ExchangeApplication>(request);
//            var exchangeApplicationDetail = ObjectMapper.Map<List<CreateExChangeApplicationDetailRequestDto>, List<ExchangeApplicationDetail>>(request.DetailItems);
//            if (CurrentUser.Id.HasValue)
//            {
//                exchangeApplication.ApplyUserId = CurrentUser.Id.Value;
//                exchangeApplication.ApplyUser = CurrentUser.Name;
//            }
//            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
//            //成本中心

//            if (isSubmit)
//            {
//                exchangeApplication.Status = 1;
//            }
//            else
//            {
//                exchangeApplication.Status = 0;
//            }
//            var costcenters = await dataverseService.GetCostcentersAsync(request.CostCenterId.ToString());
//            var costcenter = costcenters.FirstOrDefault();
//            if (costcenter == null)
//                return MessageResult.FailureResult($"未找到Id为{request.CostCenterId}的成本中心数据");

//            exchangeApplication.CostCenterID = costcenter.Id;
//            exchangeApplication.CostCenter = costcenter.Name;
//            exchangeApplication.CostCenterCode = costcenter.Code;

//            await InsertAndGenerateSerialNoAsync(exchangeReponsitory, exchangeApplication, "T");
//            var insertExchange = await exchangeReponsitory.InsertAsync(exchangeApplication, true);
//            exchangeApplicationDetail.ForEach(v => v.ExchangenApplicationId = insertExchange.Id);
//            await exchangeDetaiReponsitory.InsertManyAsync(exchangeApplicationDetail, true);
//            return MessageResult.SuccessResult(exchangeApplication);
//        }
//        /// <summary>
//        /// 修改申请单
//        /// </summary>
//        /// <param name="request">The request.</param>
//        /// <returns></returns>
//        public async Task<MessageResult> UpdateExchangeApplicationAsync(UpdateExChangeApplicationRequestDto request, bool isSubmit = false)
//        {
//            var exchangeReponsitory = LazyServiceProvider.LazyGetService<IExchangeApplicationReponsitory>();
//            if (!request.Id.HasValue) return MessageResult.FailureResult("单据Id不能为空");
//            var exchangeApplication = await exchangeReponsitory.GetAsync(request.Id.Value);
//            var exchangeDetaiReponsitory = LazyServiceProvider.LazyGetService<IExchangeApplicationDetailReponsitory>();
//            //详情
//            var exchangeDetails = await exchangeDetaiReponsitory.GetListAsync(a => a.ExchangenApplicationId == request.Id);

//            ObjectMapper.Map<UpdateExChangeApplicationRequestDto, ExchangeApplication>(request, exchangeApplication);
//            //过滤插入的数据
//            var insertItems = request.DetailItems.Where(m => !m.Id.HasValue).ToList();
//            //编辑的数据
//            var updateItems = request.DetailItems.Where(m => m.Id.HasValue).ToList();
//            var updateItemIds = updateItems.Select(s => s.Id).ToList();

//            var InsertDetail = ObjectMapper.Map<List<CreateExChangeApplicationDetailRequestDto>, List<ExchangeApplicationDetail>>(insertItems);
//            InsertDetail.ForEach(v => v.ExchangenApplicationId = request.Id.Value);
//            if (CurrentUser.Id.HasValue)
//            {
//                exchangeApplication.ApplyUserId = CurrentUser.Id.Value;
//                exchangeApplication.ApplyUser = CurrentUser.Name;
//            }
//            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
//            //成本中心

//            var costcenters = await dataverseService.GetCostcentersAsync(request.CostCenterId.ToString());
//            var costcenter = costcenters.FirstOrDefault();
//            if (costcenter == null)
//                return MessageResult.FailureResult($"未找到Id为{request.CostCenterId}的成本中心数据");

//            exchangeApplication.CostCenterID = costcenter.Id;
//            exchangeApplication.CostCenter = costcenter.Name;
//            exchangeApplication.CostCenterCode = costcenter.Code;
//            if (isSubmit)
//            {
//                exchangeApplication.Status = 1;
//            }
//            else
//            {
//                exchangeApplication.Status = 0;
//            }

//            //删除数据
//            var deleteExchangeDetail = exchangeDetails.Where(m => !updateItemIds.Contains(m.Id));
//            await exchangeDetaiReponsitory.DeleteManyAsync(deleteExchangeDetail);
//            List<ExchangeApplicationDetail> updateDetailItems = [];
//            foreach (var item in updateItems)
//            {
//                var exchangeDetail = exchangeDetails.First(f => f.Id == item.Id);
//                ObjectMapper.Map<CreateExChangeApplicationDetailRequestDto, ExchangeApplicationDetail>(item, exchangeDetail);
//                updateDetailItems.Add(exchangeDetail);

//            }
//            await exchangeDetaiReponsitory.InsertManyAsync(InsertDetail);
//            await exchangeDetaiReponsitory.UpdateManyAsync(updateDetailItems);
//            var insertExchange = await exchangeReponsitory.UpdateAsync(exchangeApplication, true);
//            return MessageResult.SuccessResult(exchangeApplication);
//        }
//        /// <summary>
//        /// 获取申请
//        /// </summary>
//        /// <param name="Id">The identifier.</param>
//        /// <returns></returns>
//        public async Task<MessageResult> GetExchangeApplicationAsync(Guid Id)
//        {
//            var exchangeQuery = (await LazyServiceProvider.LazyGetService<IExchangeApplicationReponsitory>().GetQueryableAsync()).AsNoTracking();
//            var exchangeDetaiReponsitory = (await LazyServiceProvider.LazyGetService<IExchangeApplicationDetailReponsitory>().GetQueryableAsync()).AsNoTracking();
//            var exchangeApplication = await exchangeQuery.Where(a => a.Id == Id).GroupJoin(exchangeDetaiReponsitory, a => a.Id, b => b.ExchangenApplicationId, (a, b) => new { herder = a, details = b }).FirstOrDefaultAsync();
//            if (exchangeApplication == null) return MessageResult.FailureResult("未查找到数据!");
//            var response = ObjectMapper.Map<ExchangeApplication, GetExchangeApplicationResponseDto>(exchangeApplication.herder);

//            if (exchangeApplication.details.Any())
//            {
//                var details = ObjectMapper.Map<List<ExchangeApplicationDetail>, List<GetExchangeApplicationDetailResponseDto>>(exchangeApplication.details.ToList());
//                response.DetailItems = details;
//            }
//            return MessageResult.SuccessResult(response);
//        }

//        /// <summary>
//        /// 提交审批
//        /// </summary>
//        /// <param name="request">The request.</param>
//        /// <returns></returns>
//        public async Task<MessageResult> SubmitExchangeApplicationAsync(UpdateExChangeApplicationRequestDto request)
//        {
//            MessageResult result;
//            if (request.Id.HasValue)
//                result = await UpdateExchangeApplicationAsync(request, false);
//            else
//                result = await CreateExchangeApplicationAsync(request, false);
//            if (!result.Success)
//            {
//                result.Data = null;
//                return result;
//            }
//            var exchangeApplication = result.Data as ExchangeApplication;
//            var exemptType = EnumUtil.GetEnumIdValues<WorkflowTypeName>().ToDictionary(a => (WorkflowTypeName)a.Key, a => a.Value);
//            var isOk = await LazyServiceProvider.LazyGetService<IApproveService>().InitiateApprovalAsync(new CreateApprovalDto
//            {
//                Name = exemptType[WorkflowTypeName.STicketRequest],
//                Department = exchangeApplication.ApplyUserDeptId.ToString(),
//                BusinessFormId = exchangeApplication.Id.ToString(),
//                BusinessFormNo = exchangeApplication.ApplicationCode,
//                BusinessFormName = NameConsts.STicketApplication,
//                Submitter = CurrentUser.Id?.ToString(),
//                OriginalApprovalId = exchangeApplication.ApplyUserId,
//                WorkflowType = WorkflowTypeName.STicketRequest,
//                //FormData = JsonConvert.SerializeObject(new
//                //{
//                //    sticketApplication.ApplicationCode,
//                //    StickteDtails = sticketDetails.Select(a => new { a.TotalAmount, a.ApprovalNumber }),
//                //    IsCss = sticketApplication.ApplicationType == STicketDataSourceConst.CSS ? true : false
//                //})
//            });

//            if (!isOk)
//                return MessageResult.FailureResult("提交审批失败");
//            return MessageResult.SuccessResult();
//        }
//        public async Task GetExchangeApplicationListAsync(GetExchangeApplicationListRequestDto request)
//        {
//            var exchangeReponsitory = LazyServiceProvider.LazyGetService<IExchangeApplicationReponsitory>();
//            var exchangeDetailReponsitory = LazyServiceProvider.LazyGetService<IExchangeApplicationDetailReponsitory>();

//            var exchangeQuery = (await exchangeReponsitory.GetQueryableAsync()).AsNoTracking();
//            var detailQuery = (await exchangeDetailReponsitory.GetQueryableAsync()).AsNoTracking();

//            exchangeQuery.Select(s => new { s.Id, s.ApplicationCode, s.ApplyUserDeptId, s.ApplyUserDeptName, s.ClientCode, s.Status });

//        }
//        /// <summary>
//        /// Deletes the exchange application asynchronous.
//        /// </summary>
//        /// <param name="Id">The identifier.</param>
//        /// <returns></returns>
//        public async Task<MessageResult> DeleteExchangeApplicationAsync(Guid Id)
//        {
//            var exchangeReponsitory = LazyServiceProvider.LazyGetService<IExchangeApplicationReponsitory>();
//            var exchangeDetaiReponsitory = LazyServiceProvider.LazyGetService<IExchangeApplicationDetailReponsitory>();
//            await exchangeReponsitory.DeleteAsync(Id);
//            await exchangeDetaiReponsitory.DeleteAsync(s => s.ExchangenApplicationId == Id);
//            return MessageResult.SuccessResult("操作成功");
//        }
//        /// <summary>
//        ///获取各户信息
//        /// </summary>
//        /// <param name="request">The request.</param>
//        /// <returns></returns>
//        public async Task<PagedResultDto<ClientResponseDto>> GetClientInfoAsync(ClientRequestDto request)
//        {
//            var rcmQuery = (await LazyServiceProvider.LazyGetService<IIntermediateRcmRepository>().GetQueryableAsync()).AsNoTracking();

//            var query = rcmQuery.WhereIf(!string.IsNullOrEmpty(request.ClientName), m => request.ClientName.Contains(m.Cnme))
//                 .WhereIf(!string.IsNullOrEmpty(request.ClientCode), m => request.ClientCode.Contains(m.Cccus.ToString()))
//                 .WhereIf(!string.IsNullOrEmpty(request.CompanyCode), m => request.CompanyCode.Contains(m.Ccust.ToString()));
//            var count = await query.CountAsync();
//            var datas = await query.PagingIf(request).Select(s => new ClientResponseDto { ClientCode = s.Cccus.ToString(), ClientName = s.Cnme, CompanyCode = s.Ccust.ToString() }).ToListAsync();
//            return new PagedResultDto<ClientResponseDto>(count, datas);
//        }
//    }
//}
