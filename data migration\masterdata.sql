
--M01-1

select top 100
newid() as [Id],
'' as [ApplicationId],
b.<PERSON>endorC<PERSON> as [VendorCode],
'' as [OpenId],
'' as [UnionId],
(
CASE 
    WHEN m.[VTYPE] IN ('NHIV', 'NLIV') AND 
            PATINDEX('[1][3-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9]', m.VPHONE)>0
    THEN LTRIM(RTRIM(m.VPHONE)) -- 符合条件，保留并去除空格
    WHEN m.[VTYPE] IN ('NH', 'NL') 
    THEN m.VPHONE
    ELSE '' -- 不符合条件，留空
END
) as [HandPhone],
isnull(d.[VendorType],4) as VendorType,
'' as BuCode,
VNSTAT as [Status],
'' as [EpdId],
f.ClientId as [MndId],
'' as [VendorId],
iif(isnull(d.[VendorType],4)=4,STUFF(
    (
        select ','+spk_code  from PLATFORM_ABBOTT_Dev.dbo.ODS_T_ASN_Supplier a
        join PLATFORM_ABBOTT_Dev.dbo.spk_dictionary b on a.Id=b.spk_BPMCode
        where SupplierName=m.VNDNAM for XML path('')
    ), 1, 1, ''
),'') as [ApsPorperty],
iif(isnull(d.[VendorType],4)=1,'todo','') as [CertificateCode],
iif(isnull(d.[VendorType],4)=1,'todo','') as [SPLevel],
'' as [AcademicLevel],
iif(isnull(d.[VendorType],4)=1,'todo','') as [AcademicPosition],
g.VLDRM1 as [BankCode],
g.VLDRM2 as [BankCardNo],
'' as [BankCity],
m.VCMPNY,
m.VENDOR,

'' as [BankNo],
'' as [PTId],
'' as [StandardHosDepId],
'' as [HospitalId],
'' as [HosDepartment],
0 as [SignedStatus],
'' as [SignedVersion]
from PLATFORM_ABBOTT_BUSINESSDATA_Dev.dbo.ODS_BPCS_AVM m
left join ( SELECT  VEXTNM,
        'SP'+cast(RIGHT(VCRDTE,6) as varchar(8))+RIGHT('0000' + CAST((ROW_NUMBER() OVER (PARTITION BY VCRDTE ORDER BY VCRDTE)) AS VARCHAR(4)), 4)  as VendorCode
    FROM 
        PLATFORM_ABBOTT_BUSINESSDATA_Dev.dbo.ODS_BPCS_PMFVM) b on m.VNDNAM=b.VEXTNM
left join PLATFORM_ABBOTT_Dev.dbo.ODS_T_VendorMasterData_Log c on m.VENDOR=c.VENDOR and m.VCMPNY=c.VCMPNY and m.VNDNAM=c.VNDNAM
left join  (select ProcInstId,
            (
            case cast(isnull(XmlContent,'') as XML).value('(/root/SupplierApplication_applicantBaseInfoBlock_MainStore/supplierHCPType)[1]', 'nvarchar(1)')
            when '1' then 1
            when '2' then 3
            when '3' then 4
            when '4' then 2
            else 4
            end
            )
            as [VendorType]
            from PLATFORM_ABBOTT_Dev.dbo.ODS_T_FORMINSTANCE_GLOBAL
 ) d on d.ProcInstId=c.ProcInstId
left join (select 
ROW_NUMBER() OVER(PARTITION BY b.f_code, a.vendorCode ORDER BY a.ProcInstId DESC) AS RowNum,
b.f_code as VCMPNY
, a.vendorCode
,CONCAT(cast(b.f_code as nvarchar(5)),'_',cast(a.vendorCode as nvarchar(10))) as DoctorsCode
,a.ProcInstId
from PLATFORM_ABBOTT_Dev.dbo.ODS_AUTO_BIZ_T_ProcurementApplication_Info_PR a
join PLATFORM_ABBOTT_Dev.dbo.ODS_AUTO_BIZ_T_ProcurementApplication_Info b on a.ProcInstId=b.ProcInstId) e 
on cast(e.VCMPNY as nvarchar(5))=cast(m.VCMPNY as nvarchar(5)) and cast(e.vendorCode as nvarchar(5))=cast(m.VENDOR as nvarchar(5)) and e.RowNum=1
left join  PLATFORM_ABBOTT_BUSINESSDATA_Dev.dbo.ODS_T_CRM_UpdateDoctorCodeLog f on f.DoctorsCode=e.DoctorsCode
left join ( SELECT
        VLDRM1,
        VLDRM2,
        VMCMPY,
        VNDERX as VENDOR,
        ROW_NUMBER() OVER (
            PARTITION BY VMCMPY,VNDERX,VLDate,VLTIME
            ORDER BY 
              VCRDTE DESC
        ) AS rn
    FROM
        PLATFORM_ABBOTT_BUSINESSDATA_Dev.dbo.ODS_BPCS_PMFVM m
    WHERE 
        (VLDATE IS NOT NULL or VLTIME IS NOT NULL)
        ) g on VMCMPY=m.VCMPNY and g.VENDOR=m.VENDOR and g.rn=1

---------------