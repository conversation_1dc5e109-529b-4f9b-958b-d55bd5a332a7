﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Abbott.SpeakerPortal.Contracts.Budget.FocHistory;
using Abbott.SpeakerPortal.Entities.EFlow.FOC;
using MiniExcelLibs;
using MiniExcelLibs.OpenXml;

namespace Abbott.SpeakerPortal.AppServices.Budget
{
    public class FocHistoryService : SpeakerPortalAppService, IFocHistoryService
    {
        /// <summary>
        /// 根据Id获取FOC历史记录数据
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public async Task<List<FocHistoryRecordsResponseDto>> GetFocHistoryRecordsByIdAsync(Guid Id)
        {
            var historyQuery = await LazyServiceProvider.LazyGetService<IFocHistoryRepository>().GetQueryableAsync();
            var query = historyQuery.Where(r => r.BudgetId == Id).OrderByDescending(o => o.OperatingTime).ToList();
            var datas = ObjectMapper.Map<List<FocBudgetHistory>, List<FocHistoryRecordsResponseDto>>(query);
            return datas;
        }
        /// <summary>
        /// 导出FOC预算操作历史记录
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public async Task<Stream> ExportFocHistoryExcelAsync(Guid Id)
        {
            var historyQuery = await LazyServiceProvider.LazyGetService<IFocHistoryRepository>().GetQueryableAsync();
            var query = historyQuery.Where(r => r.BudgetId == Id).OrderByDescending(o => o.OperatingTime).ToList();
            var datas = ObjectMapper.Map<List<FocBudgetHistory>, List<FocHistoryRecordsExcelResponseDto>>(query);
            MemoryStream stream = new();
            var config = new OpenXmlConfiguration()
            {
                TableStyles = TableStyles.None,
                AutoFilter = false

            };
            stream.SaveAs(datas, true, "Sheet1", configuration: config);
            stream.Seek(0, SeekOrigin.Begin);
            return stream;
        }
    }
}
