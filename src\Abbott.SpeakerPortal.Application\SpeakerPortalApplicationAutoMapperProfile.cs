﻿using Abbott.SpeakerPortal.RoleDto;
using Abbott.SpeakerPortal.Entities.VendorApplications;
using Abbott.SpeakerPortal.User;
using Abbott.SpeakerPortal.Utils;
using AutoMapper;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Data;
using Volo.Abp.Identity;
using Volo.Abp.Users;
using Abbott.SpeakerPortal.Entities;
using Abbott.SpeakerPortal.PersonCenter;
using Abbott.SpeakerPortal.Entities.OECPSAComPSALimits;
using Abbott.SpeakerPortal.Entities.OECPSAComExpenseTypes;
using System.Collections.Generic;
using Volo.Abp.AutoMapper;
using Abbott.SpeakerPortal.Entities.OECPSAComPSALimitOperHistorys;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.OEC.SpeakerLimit;
using Abbott.SpeakerPortal.OEC.ComPSALimit;
using Abbott.SpeakerPortal.OEC.PayStandard;
using Abbott.SpeakerPortal.Entities.OECPayStandards;
using Abbott.SpeakerPortal.Entities.OECPayStandardConfigs;
using Abbott.SpeakerPortal.Entities.OECPayStandardConfigDetails;
using Abbott.SpeakerPortal.Entities.OECPSASpeakerLimitExtras;
using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.OEC.SpeakerAuth;
using Abbott.SpeakerPortal.Contracts.Dataverse;
using Abbott.SpeakerPortal.Entities.Common.Workflow.WorkflowInstances;
using System;
using Abbott.SpeakerPortal.Entities.Common.Workflow.WorkflowTasks;
using Abbott.SpeakerPortal.Entities.Purchase.PurCategoryConfig;
using Abbott.SpeakerPortal.Contracts.Purchase.PurCategoryConfig;
using Abbott.SpeakerPortal.Entities.Purchase.PurBWApplication;
using Abbott.SpeakerPortal.Purchase.PurExemptApplication;
using Abbott.SpeakerPortal.Contracts.Common.Approve;
using Abbott.SpeakerPortal.Vendor.Speaker;
using Abbott.SpeakerPortal.Contracts.Vendor.NonSpeaker;
using Abbott.SpeakerPortal.Contracts.Common.Attachment;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Contracts.Vendor.NonSpeaker.NonHcpPersonal;
using Abbott.SpeakerPortal.Contracts.Vendor.NonSpeaker.HCI;
using Abbott.SpeakerPortal.Contracts.Vendor.NonSpeaker.NonHCI;
using Abbott.SpeakerPortal.Contracts.Vendor.Speaker.TaskCenter;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPOApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPOApplication;
using Abbott.SpeakerPortal.Contracts.Common.Common;
using Abbott.SpeakerPortal.Entities.Common.Sequence;
using Abbott.SpeakerPortal.Contracts.Budget;
using Abbott.SpeakerPortal.Entities.Budget;
using Abbott.SpeakerPortal.IntermediateDB.Models;
using Abbott.SpeakerPortal.Entities.Integration.Dspot;
using Abbott.SpeakerPortal.Contracts.Budget.Subbudget;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurGRApplication;
using Abbott.SpeakerPortal.Contracts.Purchase.PurGRApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurBDApplication;
using Abbott.SpeakerPortal.Contracts.Purchase.PurBDApplication;
using Abbott.SpeakerPortal.Contracts.OEC.SpeakerLimit;
using Abbott.SpeakerPortal.Entities.OECPSASpeakerLimitUseHistorys;
using Abbott.SpeakerPortal.Contracts.Report;
using Abbott.SpeakerPortal.Contracts.OEC.Intercept;
using Abbott.SpeakerPortal.Entities.OEC.OECIntercepts;
using Abbott.SpeakerPortal.Contracts.System.Slide;
using Abbott.SpeakerPortal.Entities.SystemConfig.Slide;
using Abbott.SpeakerPortal.Entities.OECSpeakerAuthApplys;
using Abbott.SpeakerPortal.Contracts.Purchase.PurThirdPartySpeaker;
using Abbott.SpeakerPortal.Entities.Purchase.PurThirdPartySpeaker;
using Abbott.SpeakerPortal.Entities.Common.Log;
using Abbott.SpeakerPortal.Entities.SystemConfig.FinanceReview;
using Abbott.SpeakerPortal.Contracts.System.FinanceReview;
using Hangfire.Storage.Monitoring;
using Abbott.SpeakerPortal.Entities.Common.ScheduleJob;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.BPMEmail;
using Abbott.SpeakerPortal.Entities.BPMEmail;
using Abbott.SpeakerPortal.Contracts.Common.PowerApp;
using Abbott.SpeakerPortal.Entities.Common.Workflow.CurrentApprovalTask;
using Abbott.SpeakerPortal.Contracts.CrossBu;
using Abbott.SpeakerPortal.Entities.CrossBu;
using Abbott.SpeakerPortal.Bpm;
using Abbott.SpeakerPortal.Entities.Integration.Bpm;
using Abbott.SpeakerPortal.Enums.Integration;
using Abbott.SpeakerPortal.Contracts.Bpm;
using Abbott.SpeakerPortal.Contracts.Integration.EpdOlineMeeting;
using Abbott.SpeakerPortal.Entities.CrossBu.ShareHis;
using Abbott.SpeakerPortal.Contracts.Approval;
using Abbott.SpeakerPortal.Entities.Approval;
using Abbott.SpeakerPortal.Contracts.FOC;
using Abbott.SpeakerPortal.Entities.EFlow.FOC;
using Abbott.SpeakerPortal.Contracts.STicket;
using Abbott.SpeakerPortal.Entities.EFlow.STicket;
using Abbott.SpeakerPortal.Contracts.Budget.FocMasterBudget;
using Abbott.SpeakerPortal.Contracts.Budget.FocSubbudget;
using Abbott.SpeakerPortal.Contracts.PostApprovalActions;
using Abbott.SpeakerPortal.Contracts.ReturnAndExchange;
using Abbott.SpeakerPortal.Entities.EFlow.Exchange;
using Abbott.SpeakerPortal.Contracts.Budget.FocHistory;
using Abbott.SpeakerPortal.Entities.EFlow.Return;
using Abbott.SpeakerPortal.Contracts.Report.WholeProcessReport;
using Abbott.SpeakerPortal.Entities.Report.WPRActiveView;
using Abbott.SpeakerPortal.Contracts.MSA;
using Abbott.SpeakerPortal.Entities.MSA;
using Abbott.SpeakerPortal.Contracts.Concur;
using Abbott.SpeakerPortal.Entities.Concur.MealEmployee;
using Abbott.SpeakerPortal.Contracts.Concur.MealReport;
using Abbott.SpeakerPortal.Entities.Concur.MealReport;

namespace Abbott.SpeakerPortal;

public class SpeakerPortalApplicationAutoMapperProfile : Profile
{
    public SpeakerPortalApplicationAutoMapperProfile()
    {
        CreateMap<PermissionDefinition, PermissionDefinitionDto>().ReverseMap();
        CreateMap<PermissionGroupDefinition, PermissionDefinitionDto>()
            .ForMember(a => a.Children, x => x.MapFrom(f => f.Permissions))
            .ReverseMap();
        /* You can configure your AutoMapper mapping configuration here.
         * Alternatively, you can split your mapping configurations
         * into multiple profile classes for a better organization. */

        CreateMap<IdentityUser, GerUserResponseDto>()
            .ForMember(a => a.StaffCode, x => x.MapFrom(f => f.GetProperty(EntityConsts.IdentityUser.StaffCode, null).ToString()))
            .ForMember(a => a.Department, x => x.MapFrom(f => f.GetProperty(EntityConsts.IdentityUser.DepartmentId, null).ToString()))
            .ForMember(a => a.JobStatus, x => x.MapFrom(f => (int)f.GetProperty(EntityConsts.IdentityUser.JobStatus, null)))
            .ForMember(a => a.MainDepartment, x => x.MapFrom(f => (int)f.GetProperty(EntityConsts.IdentityUser.MainDepartmentId, null)))
            .ReverseMap();

        CreateMap<IdentityUserRole, UserRoleDto>()
            .ReverseMap();

        CreateMap<IdentityRole, RoleListDto>().ReverseMap();

        CreateMap<SequenceNum, SequenceNumDto>().ReverseMap();

        CreateMap<VendorApplication, VeevaResultFieldsDto>().ReverseMap();

        CreateMap<SetOperationLogRequestDto, OperationLog>();

        CreateMap<ScheduleJobLogDto, ScheduleJobLog>();

        #region Common
        CreateMap<CurrentApprovalTaskDto, WfApprovalTask>().Ignore(x => x.Approver);
        CreateMap<CreatePostApprovalActionDto, PostApprovalAction>();
        #endregion

        #region Vendor

        CreateMap<CreateSpeakerRequestDto, Entities.VendorApplications.VendorApplication>().ReverseMap();
        CreateMap<SaveSpeakerRequestDto, Entities.VendorApplications.VendorApplication>().Ignore(a => a.Status).Ignore(x => x.ApplicationCode).Ignore(a => a.VendorType).Ignore(a => a.ApplyTime)
            .Ignore(a => a.ApplyUserId).Ignore(a => a.ApplyUserName).Ignore(a => a.ApplyUserBu).Ignore(a => a.ApplyDeptName).Ignore(a => a.ApplyBuId).Ignore(a => a.ApplyBuName).Ignore(a => a.ApplyUserBuName).Ignore(a => a.EpdId).ReverseMap();
        CreateMap<CreateSpeakerRequestDto, Entities.VendorApplicationPersonals.VendorApplicationPersonal>().Ignore(a => a.Id).ReverseMap();
        CreateMap<SaveSpeakerRequestDto, Entities.VendorApplicationPersonals.VendorApplicationPersonal>().Ignore(a => a.Id).ReverseMap();
        CreateMap<FinancialInformation, Entities.VendorApplicationFinancials.VendorApplicationFinancial>().Ignore(x => x.Id).ReverseMap();

        CreateMap<NonSpeakerApplicationPersonalDto, Entities.VendorApplications.VendorApplication>().Ignore(x => x.CreationTime).Ignore(a => a.ApplyUserId).Ignore(a => a.ApplyUserBu);
        CreateMap<NonSpeakerApplicationPersonalDto, Entities.VendorApplicationPersonals.VendorApplicationPersonal>().Ignore(x => x.Id).Ignore(x => x.CreationTime);
        CreateMap<NonSpeakerApplicationOrganizationDto, Entities.VendorApplications.VendorApplication>().Ignore(a => a.DraftVersion).Ignore(a => a.ApplyUserId).Ignore(a => a.ApplyUserBu);
        CreateMap<NonSpeakerApplicationOrganizationDto, Entities.VendorApplicationOrgnizations.VendorApplicationOrganization>().Ignore(x => x.Id).Ignore(x => x.CreationTime);

        CreateMap<Entities.VendorApplications.VendorApplication, VendorApplicationPersonalDetailReponseDto>()
            .Ignore(x => x.BankCity)
            .Ignore(x => x.BankCardImg)
            .Ignore(x => x.CardPic)
            .Ignore(x => x.AttachmentInformation)
            .Ignore(x => x.DPSCheck);
        CreateMap<Entities.VendorApplicationPersonals.VendorApplicationPersonal, VendorApplicationPersonalDetailReponseDto>()
            .Ignore(x => x.ID)
            .Ignore(x => x.BankCardImg)
            .Ignore(x => x.CardPic)
            .Ignore(x => x.AttachmentInformation)
            .Ignore(x => x.DPSCheck);
        CreateMap<Entities.VendorApplications.VendorApplication, VendorApplicationOrganizationDetailResponseDto>()
            .Ignore(x => x.BankCity)
            .Ignore(x => x.BankCardImg)
            .Ignore(x => x.AttachmentInformation)
            .Ignore(x => x.DPSCheck)
            .Ignore(x => x.ApsPorperty);
        CreateMap<Entities.VendorApplicationOrgnizations.VendorApplicationOrganization, VendorApplicationOrganizationDetailResponseDto>()
            .Ignore(x => x.ID)
            .Ignore(x => x.BankCardImg)
            .Ignore(x => x.AttachmentInformation)
            .Ignore(x => x.DPSCheck)
            .Ignore(x => x.ApsPorperty);
        CreateMap<Entities.Common.Attachment.Attachment, UploadFileResponseDto>().ForMember(u => u.AttachmentId, x => x.MapFrom(p => p.Id));

        CreateMap<NonHcpCreateApplicationRequestDto, Entities.VendorApplications.VendorApplication>();
        CreateMap<NonHcpCreateApplicationRequestDto, Entities.VendorApplicationPersonals.VendorApplicationPersonal>();

        CreateMap<NonSpeakerApplicationPersonalDto, NonHcpPersonalEditBaseInfoDto>();
        CreateMap<NonHcpPersonalEditBaseInfoDto, Entities.VendorApplications.VendorApplication>();
        CreateMap<NonHcpPersonalEditBaseInfoDto, Entities.VendorApplicationPersonals.VendorApplicationPersonal>().Ignore(x => x.Id);
        #endregion

        #region 任务中心
        CreateMap<VendorApplicationPersonalDetailReponseDto, NonHcpPersonalChangedDetail>().Ignore(x => x.BankCity).Ignore(x => x.Sex);
        CreateMap<Entities.Vendors.Vendor, NonHcpPersonalChangedDetail>().Ignore(x => x.BankCity).Ignore(x => x.ProvinceCity).Ignore(x => x.CardPic).Ignore(x => x.AttachmentInformation);
        CreateMap<Entities.VendorPersonals.VendorPersonal, NonHcpPersonalChangedDetail>().Ignore(x => x.BankCity).Ignore(x => x.ProvinceCity).Ignore(x => x.CardPic);
        CreateMap<VendorApplicationPersonalDetailReponseDto, NonHcpPersonalChangedDetailResponseDto>();
        CreateMap<VendorPersonalDetailResponseDto, NonHcpPersonalChangedDetail>();
        CreateMap<VendorApplicationOrganizationDetailResponseDto, HciOrgChangedDetailResponseDto>().Ignore(a => a.ProvinceCityName);
        CreateMap<VendorOrganizationDetailResponseDto, HciOrgChangedDetail>().Ignore(a => a.ProvinceCityName);
        CreateMap<VendorApplicationOrganizationDetailResponseDto, NonHciOrgChangedDetailResponseDto>().Ignore(x => x.ProvinceCityName).Ignore(x => x.ApsPorperty);
        CreateMap<VendorOrganizationDetailResponseDto, NonHciOrgChangedDetail>().Ignore(a => a.ProvinceCityName);

        CreateMap<VendorApplicationOrganizationDetailResponseDto, HciOrgChangedDetail>().Ignore(a => a.ProvinceCityName);
        CreateMap<Entities.Vendors.Vendor, HciOrgChangedDetail>().Ignore(x => x.BankCity).Ignore(x => x.ProvinceCity).Ignore(x => x.AttachmentInformation);
        CreateMap<Entities.VendorOrgnizations.VendorOrgnization, HciOrgChangedDetail>().Ignore(x => x.BankCity).Ignore(x => x.ProvinceCity);

        CreateMap<VendorApplicationOrganizationDetailResponseDto, NonHciOrgChangedDetail>().Ignore(a => a.ProvinceCityName);
        CreateMap<Entities.Vendors.Vendor, NonHciOrgChangedDetail>().Ignore(x => x.BankCity).Ignore(x => x.ProvinceCity).Ignore(x => x.AttachmentInformation);
        CreateMap<Entities.VendorOrgnizations.VendorOrgnization, NonHciOrgChangedDetail>().Ignore(x => x.BankCity).Ignore(x => x.ProvinceCity);

        CreateMap<SpeakerDraftDetailResponseDto, SpeakerChangedDetailResponseDto>();
        CreateMap<SpeakerDraftDetailResponseDto, SpeakerChangedDetail>();
        CreateMap<SpeakerDetailResponseDto, SpeakerChangedDetail>();
        CreateMap<Entities.Vendors.Vendor, SpeakerChangedDetail>().Ignore(x => x.ProvinceCity).Ignore(x => x.BankCity).Ignore(x => x.CardPic).Ignore(x => x.BankCardImg).Ignore(x => x.AttachmentInformation).Ignore(x => x.FinancialInformation);
        CreateMap<Entities.VendorApplications.VendorApplication, Entities.Vendors.Vendor>()
            .Ignore(x => x.Status).Ignore(x => x.Id).Ignore(x => x.CreationTime).Ignore(x => x.DeleterId).Ignore(x => x.DeletionTime).Ignore(x => x.LastModifierId).Ignore(x => x.LastModificationTime);
        CreateMap<Entities.VendorApplicationPersonals.VendorApplicationPersonal, Entities.VendorPersonals.VendorPersonal>()
            .Ignore(x => x.Id).Ignore(x => x.VendorId).Ignore(x => x.CreationTime).Ignore(x => x.CreatorId).Ignore(x => x.DeleterId).Ignore(x => x.DeletionTime).Ignore(x => x.LastModifierId).Ignore(x => x.LastModificationTime);
        CreateMap<Entities.VendorApplicationFinancials.VendorApplicationFinancial, Entities.VendorFinancials.VendorFinancial>()
            .Ignore(x => x.Id).Ignore(x => x.VendorId).Ignore(x => x.CreationTime).Ignore(x => x.CreatorId).Ignore(x => x.DeleterId).Ignore(x => x.DeletionTime).Ignore(x => x.LastModifierId).Ignore(x => x.LastModificationTime);
        CreateMap<Entities.VendorApplicationOrgnizations.VendorApplicationOrganization, Entities.VendorOrgnizations.VendorOrgnization>()
            .Ignore(x => x.Id).Ignore(x => x.VendorId).Ignore(x => x.CreationTime).Ignore(x => x.CreatorId).Ignore(x => x.DeleterId).Ignore(x => x.DeletionTime).Ignore(x => x.LastModifierId).Ignore(x => x.LastModificationTime);

        CreateMap<Entities.Vendors.Vendor, VendorPersonalDetailResponseDto>().Ignore(x => x.BankCity).Ignore(x => x.ProvinceCity).Ignore(x => x.CardPic).Ignore(x => x.BankCardImg).Ignore(x => x.AttachmentInformation);
        CreateMap<Entities.VendorPersonals.VendorPersonal, VendorPersonalDetailResponseDto>().Ignore(x => x.BankCity).Ignore(x => x.ProvinceCity).Ignore(x => x.CardPic);

        CreateMap<Entities.Vendors.Vendor, VendorOrganizationDetailResponseDto>().Ignore(x => x.BankCity).Ignore(x => x.ProvinceCity).Ignore(x => x.BankCardImg).Ignore(x => x.AttachmentInformation);
        CreateMap<Entities.VendorOrgnizations.VendorOrgnization, VendorOrganizationDetailResponseDto>().Ignore(x => x.BankCity).Ignore(x => x.ProvinceCity);

        CreateMap<Entities.VendorFinancials.VendorFinancial, FinancialInformation>();

        #endregion

        #region Person Center
        CreateMap<MessageInfo, MessageDto>()
            .ForMember(a => a.ReadTime, x => x.MapFrom(b => b.ReadAt))
            .ReverseMap();

        #endregion

        #region 合规管理

        #region 单据拦截
        CreateMap<InsertOperateHistoryDto, OECInterceptOperateHistory>();
        CreateMap<OECInterceptOperateHistory, OECInterceptOperateHistoryDto>();
        #endregion

        #region 通用PSA上限配置
        CreateMap<OECPSAComPSALimit, ComPSALimitListResponseDto>()
            .ForMember(a => a.EffectStart, x => x.MapFrom(f => f.EffectStart.Year))
            .ForMember(a => a.EffectEnd, x => x.MapFrom(f => f.EffectEnd.HasValue ? f.EffectEnd.Value.Year : new int?()))
            .ForMember(a => a.LastModificationTime, x => x.MapFrom(f => ToDateTimeLongString(f.LastModificationTime)));

        CreateMap<CreateComPSALimitRequestDto, OECPSAComPSALimit>();

        CreateMap<CreateComPSALimitRequestDto, CreateComPSALimitDto>();
        CreateMap<CreateComPSALimitDto, OECPSAComPSALimit>();

        CreateMap<CreateExpenseTypeRequestDto, CreateExpenseTypeDto>();
        CreateMap<CreateExpenseTypeDto, OECPSAComExpenseType>();
        CreateMap<OECPSAComExpenseType, CreateExpenseTypeDto>();

        CreateMap<OECPSAComPSALimitOperHistory, GetComPSALimitOperHistory>().ReverseMap();

        CreateMap<NewCreateComPSALimitRequestDto, CreateComPSALimitRequestDto>()
            .ForMember(a => a.EffectStart, a => a.MapFrom(a1 => new DateTime(a1.EffectStart, 1, 1)));

        #endregion

        #region speaker limit
        CreateMap<DictionaryDto, DropdownListDto>().ReverseMap();
        CreateMap<DepartmentDto, DropdownListDto>()
            .ForMember(a => a.Name, b => b.MapFrom(c => c.DepartmentName))
            .ReverseMap();

        CreateMap<SavePSALimitDto, OECPSASpeakerLimitExtra>()
            .ReverseMap();

        CreateMap<AddSpeakerLimitUseHistoryRequestDto, OECPSASpeakerLimitUseHistory>();

        #endregion

        #region 计酬标准配置

        CreateMap<SavePayStandardDto, OECPayStandard>();
        CreateMap<PayStandardConfigDto, OECPayStandardConfig>();
        CreateMap<PayStandardConfigDetailDto, OECPayStandardConfigDetail>();

        //CreateMap<OECPayStandard, PayStandardDetailResponseDto>();
        //CreateMap<OECPayStandardConfig, PayStandardDetailConfigResponseDto>();
        //CreateMap<OECPayStandardConfigDetail, PayStandardDetailConfigDetailResponseDto>();

        #endregion

        #region 讲者授权申请
        CreateMap<OECSpeakerAuthApply, SpeakerAuthApplyDto>()
            .ForMember(a => a.SubmitTime, a => a.MapFrom(x => x.SubmitTime.ToString("yyyy-MM-dd")))
            .ForMember(a => a.StartTime, a => a.MapFrom(x => x.StartTime.ToString("yyyy-MM-dd")))
            .ForMember(a => a.EndTime, a => a.MapFrom(x => x.EndTime.ToString("yyyy-MM-dd")));
        CreateMap<CreateSpeakerAuthApplyDto, OECSpeakerAuthApply>().Ignore(a => a.Id);
        #endregion

        CreateMap<WorkflowInstanceDto, WorkflowInstance>()
            .ReverseMap();

        CreateMap<WorkflowTaskDto, WorkflowTask>()
            .ReverseMap();

        #endregion

        #region 采购管理

        #region 品类配置

        CreateMap<PurCategoryConfig, GetCategoryConfigListResponseDto>();
        CreateMap<CreateCategoryConfigRequestDto, PurCategoryConfig>();
        CreateMap<UpdateCategoryConfigRequestDto, PurCategoryConfig>();

        #endregion

        #region 采购申请

        CreateMap<ConsumeCategoryDto, ConsumeCategorySponsorshipTypeResponseDto>();
        CreateMap<CreatePRApplicationRequest, PurPRApplication>()
            .Ignore(a => a.Hospitals)
            .Ignore(a => a.HospitalDepartments)
            .Ignore(a => a.SupportFiles)
            .Ignore(a => a.ApprovalEmails)
            .Ignore(a => a.ProductIds);
        CreateMap<CreateUpdatePRApplicationDetailRequest, PurPRApplicationDetail>().Ignore(a => a.BackUpVendors);
        CreateMap<CreateUpdatePRApplicationBackupVendor, PurPRApplicationDetailBackupVendor>()
            .ForMember(a => a.VendorName, a => a.MapFrom(a1 => a1.VendorIdName))
            .ReverseMap();
        CreateMap<CreateUpdatePRApplicationCostItemRequest, PurPRApplicationCostItem>();
        CreateMap<CreateUpdateHcpTravelAgencyConferenceFeeRequest, PurPRApplicationHcpTravelLodgingFee>();
        CreateMap<PurPRApplication, GetPRApplicationResponse>()
            .Ignore(a => a.SupportFiles)
            .Ignore(a => a.ApprovalEmails)
            .Ignore(a => a.Hospitals)
            .Ignore(a => a.HospitalDepartments)
            .Ignore(a => a.Status)
            .Ignore(a => a.ProductIds)
            .ForMember(a => a.ApplyTime, a => a.MapFrom(a1 => a1.ApplyTime.HasValue ? a1.ApplyTime.Value.ToString("yyyy-MM-dd HH:mm") : null))
            .ForMember(a => a.AcitveDate, a => a.MapFrom(a1 => a1.AcitveDate.HasValue ? a1.AcitveDate.Value.ToString("yyyy-MM-dd") : null))
            .ForMember(a => a.MeetingDate, a => a.MapFrom(a1 => a1.MeetingDate.HasValue ? a1.MeetingDate.Value.ToString("yyyy-MM-dd HH:mm:ss") : null));
        CreateMap<PurPRApplicationDetail, GetPRApplicationDetailResponse>().Ignore(a => a.BackUpVendors);
        CreateMap<UpdatePRApplicationRequest, PurPRApplication>()
            .Ignore(a => a.Hospitals)
            .Ignore(a => a.HospitalDepartments)
            .Ignore(a => a.SupportFiles)
            .Ignore(a => a.ApprovalEmails)
            .Ignore(a => a.ProductIds);
        CreateMap<PurPRApplicationCostItem, GetPRApplicationCostItemResponse>();
        CreateMap<PurPRApplicationHcpTravelLodgingFee, GetPRApplicationHcpTravelAgencyConferenceFeeResponse>();

        CreateMap<PurPRApplication, PushDetail>();
        CreateMap<PurPRApplicationDetail, PushDetail>()
            .ForMember(a => a.PRDetailId, x => x.MapFrom(b => b.Id));

        CreateMap<PurPRApplicationReportDto, PurPRApplicationReport>();

        #endregion

        #region 豁免管理

        CreateMap<PurBWRequestDto, PurBWApplication>()
            .Ignore(a => a.ApplyUserId).Ignore(a => a.ApplyUserName)
            .Ignore(a => a.ApplyDeptId).Ignore(a => a.ApplyDeptName)
            //.ForMember(a => a.ApplicantId, b => b.MapFrom(c => c.ApplyUserId))
            .ReverseMap();

        CreateMap<PurBWApplication, PurBWPageResponseDto>().ReverseMap();

        CreateMap<PurBWApplication, PurBWResponseDto>().ReverseMap();

        CreateMap<PurBWApplication, PurBWResponseApprovalDto>().ReverseMap();

        #endregion

        #region 比价申请管理
        CreateMap<BDCreateOrUpdateRequestDto, PurBDApplication>().Ignore(g => g.Status).Ignore(g => g.ApplyUserId).Ignore(g => g.ApplyUserName).Ignore(g => g.ApplyUserBu).Ignore(g => g.ApplyUserBuName);
        CreateMap<BDCreateDetailRequestDto, PurBDApplicationDetail>()
            .ReverseMap();
        CreateMap<PurBDApplication, BDInfoReponseDto>().ReverseMap();
        CreateMap<PurBDApplicationDetail, BDDetailInfoReponseDto>().ReverseMap();
        CreateMap<BDCreateSupplierDetailRequestDto, PurBDApplicationSupplierDetail>()
            .ReverseMap();
        CreateMap<BDSupplierDetailInfoReponseDto, PurBDApplicationSupplierDetail>().ReverseMap();

        //CreateMap<PurBDApplication, BDApprovalResponseDto>();
        //CreateMap<BDApprovalResponseDto, BDApprovalBaseResponseDto>();
        #endregion
        #region 采购订单管理
        CreateMap<POCreateOrUpdateRequestDto, PurPOApplication>().Ignore(a => a.ApplyUserId).Ignore(a => a.ApplyUserName).Ignore(a => a.ApplyUserDept).Ignore(a => a.ApplyUserBuToDeptName).Ignore(a => a.ApplyUserBu).Ignore(a => a.ApplyUserBuName).Ignore(a => a.SowId)
            .ForMember(a => a.PRType, b => b.MapFrom(c => c.PRType == null || c.PRType.Length < 1 ? null : string.Join(',', c.PRType)));
        CreateMap<POCreateDetailRequestDto, PurPOApplicationDetails>();
        CreateMap<PurPOApplication, POInfoReponseDto>().Ignore(s => s.ApsPorperty).ForMember(d => d.Apartment, opt => opt.MapFrom(src => src.ApplyUserBuToDeptName))
            .ForMember(a => a.DeliveryDate, b => b.MapFrom(c => c.DeliveryDate.ToString("yyyy-MM-dd")))
            .ForMember(a => a.PRType, b => b.MapFrom(c => string.IsNullOrWhiteSpace(c.PRType) ? null : c.PRType.Split(',', StringSplitOptions.None)));
        CreateMap<PurPOApplicationDetails, PODetailInfoReponseDto>();
        CreateMap<PurPOApplication, POApprovalResponseDto>();
        CreateMap<POApprovalResponseDto, POApprovalBaseResponseDto>();
        CreateMap<PurBDApplicationDetail, PODetailInfoReponseDto>().Ignore(x => x.Id);
        #endregion

        #region GR收货申请
        CreateMap<PurGRApplication, PurGRApplicationResponseDto>();
        CreateMap<PurGRApplicationRequestDto, PurGRApplication>().Ignore(g => g.Id);
        CreateMap<PurGRApplicationDetailsDto, PurGRApplicationDetail>();
        CreateMap<PurGRApplicationDetail, PurGRApplicationDetailsDto>().ForMember(a => a.DetailId, b => b.MapFrom(c => c.Id));
        CreateMap<PurGRApplication, GRApplicationDetailsResponseDto>();
        CreateMap<PurGRApplicationDetail, PurGRApplicationDetailResponseDto>()
            .ForMember(a => a.DetailId, b => b.MapFrom(c => c.Id))
            .ForMember(a => a.SigningDate, b => b.MapFrom(c => c.SigningDate == System.DateTime.MinValue ? null : c.SigningDate.ToString("yyyy-MM-dd")));
        CreateMap<PurGRApplicationDetail, PurGRApplicationDetailHistory>()
        .ForMember(a => a.GRApplicationDetailId, b => b.MapFrom(c => c.Id)).Ignore(g => g.Id)
        .Ignore(g => g.CreationTime).Ignore(g => g.LastModificationTime).Ignore(a => a.CreatorId).Ignore(g => g.LastModifierId).Ignore(g => g.DeletionTime).Ignore(g => g.DeleterId);
        CreateMap<PurGRApplicationDetailHistory, PurGRApplicationDetailHistoryDto>();
        CreateMap<PurGRApplicationDetailHistory, PurGRApplicationDetailHistoryResponseDto>()
            .ForMember(a => a.SigningDate, b => b.MapFrom(c => c.SigningDate == System.DateTime.MinValue ? null : c.SigningDate.ToString("yyyy-MM-dd")));
        #endregion

        #region 付款申请
        CreateMap<PurPAApplication, PurPAApplicationDto>()
            .ForMember(a => a.AcceptedDate, b => b.MapFrom(c => c.AcceptedTime.HasValue ? c.AcceptedTime.Value.ToString("yyyy-MM-dd") : ""))
            .ForMember(a => a.ExpenseTypeName, b => b.MapFrom(c => c.ExpenseType.HasValue ? c.ExpenseType.Value.GetDescription() : ""))
            .ForMember(a => a.DeliveryModeName, b => b.MapFrom(c => c.DeliveryMode.GetDescription()));
        CreateMap<PurPAApplicationDto, PurPAApplication>().Ignore(a => a.Id);
        CreateMap<PurPAApplication, PurPAApplicationListDto>().ForMember(dest => dest.EstimatedPaymentTime, opt => opt.MapFrom(src => src.EstimatedPaymentDate));
        CreateMap<PurPAApplicationDetail, PurPAApplicationDetailResponseDto>();
        CreateMap<PurPAApplicationDetail, PurPAApplicationDetailDto>();
        CreateMap<PurPAApplicationDetailDto, PurPAApplicationDetail>().Ignore(a => a.Id);
        CreateMap<PurPAApplicationInvoice, PurPAApplicationInvoiceDto>();
        CreateMap<PurPAApplicationInvoiceDto, PurPAApplicationInvoice>().Ignore(a => a.Id);
        CreateMap<PurPAApplicationInvoice, PurPAApplicationInvoiceResponseDto>()
            .ForMember(a => a.InvoiceDate, b => b.MapFrom(c => c.InvoiceDate == System.DateTime.MinValue ? null : c.InvoiceDate.ToString("yyyy-MM-dd")));
        CreateMap<PurPAFinancialVoucherInfo, PAFinancialVoucherInfoDto>();
        CreateMap<PAFinancialVoucherInfoDto, PurPAFinancialVoucherInfo>();
        CreateMap<PAReturnReasonDto, PurPAReturnReasonInfo>().Ignore(a => a.Id);
        CreateMap<PurPAReturnReasonInfo, PAReturnReasonDto>();
        CreateMap<ReturnReasonDetailDto, PurPAReturnReasonDetail>().Ignore(a => a.Id);
        CreateMap<PurPAReturnReasonDetail, ReturnReasonDetailDto>();
        #endregion

        #region 第三方讲者

        CreateMap<SaveThirdPartySpeakerItemRequestDto, PurThirdPartySpeakerItem>();

        #endregion

        #endregion

        #region 审批管理

        CreateMap<AddApprovalRecordDto, WorkflowTask>()
            .ReverseMap();
        CreateMap<AddApprovalSetRecordIdDto, WorkflowTask>().ReverseMap();
        #endregion

        #region 预算管理
        CreateMap<CreateBudgetRequestDto, BdMasterBudget>();
        CreateMap<BdHistory, HistoryRecordsResponseDto>();
        CreateMap<BdHistory, HistoryRecordsExcelResponseDto>();
        CreateMap<CreateSubbudgetRequestDto, BdSubBudget>();
        CreateMap<BdSubBudget, GetSubbudgetDetailResponseDto>().ForMember(a => a.UsedAmount, b => b.MapFrom(s => s.UesdAmount));
        CreateMap<MonthlyBudgetDto, BdMonthlyBudget>().ReverseMap();
        CreateMap<MonthlyBudgetResponseDto, BdMonthlyBudget>().ForMember(a => a.BudgetAmount, b => b.MapFrom(s => s.Amount)).ReverseMap();
        CreateMap<CreatesSubBudgetDto, CreateImportMessageDto>().ReverseMap();
        CreateMap<GetActiveUserListResponseDto, ActiveUserExcelResponseDto>();
        CreateMap<GetBudgetListResponseDto, ExportBudgetListDto>();
        CreateMap<CreateSubBudgetMappingRequestDto, BdSubBudgetMapping>();
        #endregion

        #region Integration

        #region BPCS
        CreateMap<Abk, BpcsAbk>().ReverseMap();
        CreateMap<Avt, BpcsAvt>().ReverseMap();
        CreateMap<Gcc, BpcsGcc>().ReverseMap();
        CreateMap<Zcc, BpcsZcc>().ReverseMap();
        CreateMap<Zpa, BpcsZpa>().ReverseMap();
        CreateMap<Zrc, BpcsZrc>().ReverseMap();
        CreateMap<Avm, BpcsAvm>().ReverseMap();
        CreateMap<Pmfvm, BpcsPmfvm>().ReverseMap();
        CreateMap<Aph, BpcsAph>().ReverseMap();
        CreateMap<Aml, BpcsAml>().ReverseMap();
        CreateMap<Glh, BpcsGlh>().ReverseMap();
        CreateMap<Gcr, BpcsGcr>().ReverseMap();
        #endregion
        #region Report
        CreateMap<FullProcessReportDto, FullProcessReportByApprovalReportDto>().ReverseMap();
        #endregion
        #endregion

        #region 系统配置
        #region SlideConfig
        CreateMap<CreateSlideConfigDto, SlideConfig>();
        CreateMap<UpdateSlideConfigDto, SlideConfig>().Ignore(a => a.Id);
        CreateMap<SlideConfig, SlideConfigDto>()
            .ForMember(a => a.EffectiveDate, b => b.MapFrom(c => c.EffectiveDate.ToString("yyyy-MM-dd")))
            .ForMember(a => a.ExpirationDate, b => b.MapFrom(c => c.ExpirationDate.ToString("yyyy-MM-dd")));
        #endregion
        #region 财务复审流转配置
        CreateMap<FinanceReviewConfig, FinanceReviewConfigDto>();
        #endregion
        #endregion

        #region 邮件发送记录
        CreateMap<InsertSendEmaillRecordDto, SendEmailRecord>();
        #endregion

        #region 报表
        CreateMap<ProfessionalServicesResponseDto, ExportProfessionalTaxRepotDto>();
        CreateMap<UVW_WPR_Active, UVW_WPR_ActiveResponseDto>();
        //CreateMap<object, UVW_WPR_ActiveResponseDto>();
        #endregion

        #region CrossBu

        CreateMap<CreateCustomerRelationRequestDto, CustomerRelation>();
        CreateMap<CustomerRelation, GetCustomerRelationResponseDto>();
        CreateMap<UpdateCustomerRelationRequestDto, CustomerRelation>().Ignore(i => i.Id).ReverseMap();
        CreateMap<CustomerRelationPreDataDto, CustomerRelation>().ReverseMap();
        CreateMap<ContactShareResquestDto, ContactShareRecords>().ReverseMap();
        #endregion

        #region E-Flow
        CreateMap<CreateSTicketApplicationRequest, STicketApplication>().ReverseMap();
        CreateMap<CreateUpdateSTicketApplicationDetailRequest, STicketApplicationDetail>().ReverseMap();
        CreateMap<DictionaryDto, GetCustomerTypeResponseDto>();
        CreateMap<STicketApplication, GetSTicketApplicationResponse>().Ignore(a => a.Attachment);
        CreateMap<DictionaryDto, GetDiscountCategoryResponseDto>();
        #endregion

        #region FOC
        CreateMap<CreateFocApplicationRequest, FOCApplication>().ReverseMap();
        CreateMap<CreateUpdateFocApplicationDetailRequest, FOCApplicationDetail>().ReverseMap();
        CreateMap<FOCApplication, GetFocApplicationListResponseDto>();
        CreateMap<CreateFocProductDetailRequest, FOCApplicationProductDetail>().ReverseMap();

        CreateMap<CreateFocBudgetRequestDto, FocMasterBudget>();
        CreateMap<FocSubBudget, GetFocSubbudgetDetailResponseDto>().ForMember(a => a.UsedQty, b => b.MapFrom(s => s.UesdQty));
        CreateMap<FocMonthlyBudgetResponseDto, FocMonthlyBudget>().ForMember(a => a.BudgetQty, b => b.MapFrom(s => s.Qty)).ReverseMap();
        CreateMap<FocMonthlyBudgetDto, FocMonthlyBudget>().ReverseMap();
        CreateMap<CreatesFocSubBudgetDto, CreateSubBudgetImportMessageDto>().ReverseMap();
        CreateMap<CreateFocSubbudgetRequestDto, FocSubBudget>();
        CreateMap<DictionaryDto, GetShippingTypeResponseDto>();
        CreateMap<DictionaryDto, GetFOCTerminalListResponseDto>();
        CreateMap<FOCApplication, GetFocApplicationResponseDto>().Ignore(a => a.Attachment);
        CreateMap<FOCApplicationProductDetail, FocProductDetailRequest>().ReverseMap();
        CreateMap<FOCApplicationLogisticsDetail, FocLogisticsDetailRequest>().ReverseMap();
        CreateMap<CreateApplicationLogisticsRequest, FOCApplicationLogisticsDetail>().ReverseMap();
        CreateMap<FOCApplicationLogisticsDetail, GetFocProductLogisticsResponseDto>();
        CreateMap<FOCApplication, GetFOCAppliedByMeResponseDto>();
        CreateMap<FocBudgetHistory, FocHistoryRecordsResponseDto>();
        CreateMap<FocBudgetHistory, FocHistoryRecordsExcelResponseDto>();
        CreateMap<GetFocBudgetListResponseDto, ExportFocBudgetListDto>();
        #endregion

        CreateMap<TTpmInterfaceLog, BpmTpmInterfaceLog>().Ignore(x => x.Id)
            .ForMember(u => u.BpmId, x => x.MapFrom(p => p.Id))
            .ForMember(a => a.State, x => x.MapFrom(p => BpmInterfaceLogState.Unprocessed));
        CreateMap<BpmSettlementMeetingDto, NexBpmMeetingSettlementRequestDto>().ForMember(a => a.ActualNumber, x => x.MapFrom(p => p.ActualNumber.ToString()));

        CreateMap<InitializeApprovalRecordDto, InitializeApprovalRecord>();

        #region E-Flow
        CreateMap<STicketApplication, GetSTicketApplicationResponseDto>().ForMember(u => u.Amount, x => x.MapFrom(p => p.TotalAmountRMB));
        CreateMap<STicketApplication, GetSTicketAppliedByMeResponseDto>().ForMember(u => u.Amount, x => x.MapFrom(p => p.TotalAmountRMB));
        CreateMap<STicketApplication, GetSTicketApplicationDraftResponseDto>().ForMember(u => u.Amount, x => x.MapFrom(p => p.TotalAmountRMB)).ForMember(u => u.SavaTime, x => x.MapFrom(p => p.CreationTime));
        #endregion
        #region Rturn And Exchange
        CreateMap<CreateExChangeApplicationRequestDto<CreateExChangeApplicationDetailRequestDto>, ReturnApplication>().Ignore(s => s.Attachment);
        CreateMap<CreateExChangeApplicationDetailRequestDto, ReturnApplicationDetail>().Ignore(g => g.Id);
        CreateMap<UpdateExChangeApplicationRequestDto<CreateExChangeApplicationDetailRequestDto>, ReturnApplication>().Ignore(g => g.Id).Ignore(s => s.Attachment);

        CreateMap<CreateReturnApplicationRequestDto<CreateReturnApplicationDetailRequestDto>, ReturnApplication>().Ignore(s => s.Attachment);
        CreateMap<CreateReturnApplicationDetailRequestDto, ReturnApplicationDetail>().Ignore(g => g.Id);
        CreateMap<UpdateReturnApplicationRequestDto<CreateReturnApplicationDetailRequestDto>, ReturnApplication>().Ignore(g => g.Id).Ignore(s => s.Attachment);
        CreateMap<ReturnApplication, GetReturnApplicationResponseDto>().Ignore(a => a.Attachment);
        CreateMap<ReturnApplicationDetail, GetReturnApplicationDetailResponseDto>();
        CreateMap<ReturnApplication, GetReturnAppliedByMeResponseDto>().ForMember(f => f.ProductQuantity, x => x.MapFrom(p => p.TotalProductQuantity));
        #endregion

        #region MSA
        CreateMap<MsaPostDto, MsaMasterServiceAgreement>();
        CreateMap<SowPostDto, MsaStatementOfWork>();
        CreateMap<MsaStatementOfWork, SowResponseDto>();
        CreateMap<MsaMasterServiceAgreement, MsaInfoResponseDto>()
            .ForMember(a => a.EffectiveDate, b => b.MapFrom(c => c.EffectiveDate.HasValue ? c.EffectiveDate.Value.ToString("yyyy-MM-dd") : null))
            .ForMember(a => a.ExpiryDate, b => b.MapFrom(c => c.ExpiryDate.HasValue ? c.ExpiryDate.Value.ToString("yyyy-MM-dd") : null))
            .Ignore(a => a.Companies);
        #endregion

        #region Concur

        CreateMap<EmployeeImportValidRowDto, MealEmployee>().ReverseMap();
        CreateMap<EmployeeImportRowDto, EmployeeImportValidRowDto>()
            .ForMember(a => a.OnboardDate, a => a.MapFrom(a1 => a1.OnboardDate.HasValue ? a1.OnboardDate.Value.ToString("yyyy-MM-dd") : null))
            .ForMember(a => a.DepartureDate, a => a.MapFrom(a1 => a1.DepartureDate.HasValue ? a1.DepartureDate.Value.ToString("yyyy-MM-dd") : null));

        CreateMap<MealEmployee, EmployeeQueryResponseDto>()
            .ForMember(a => a.Date, a => a.MapFrom(a1 => a1.Date.ToString("yyyy-MM")))
            .ForMember(a => a.OnboardDate, a => a.MapFrom(a1 => a1.OnboardDate.HasValue ? a1.OnboardDate.Value.ToString("yyyy-MM-dd") : null))
            .ForMember(a => a.DepartureDate, a => a.MapFrom(a1 => a1.DepartureDate.HasValue ? a1.DepartureDate.Value.ToString("yyyy-MM-dd") : null));

        CreateMap<MealReportImportValidRowDto, MealReportImportRowDto>()
            .ForMember(a => a.SentForPaymentDate, a => a.MapFrom(a1 => a1.SentForPaymentDate.HasValue ? a1.SentForPaymentDate.Value.ToString("yyyy-MM-dd") : null))
            .ForMember(a => a.FirstSubmittedDate, a => a.MapFrom(a1 => a1.FirstSubmittedDate.HasValue ? a1.FirstSubmittedDate.Value.ToString("yyyy-MM-dd") : null))
            .ForMember(a => a.ReportDate, a => a.MapFrom(a1 => a1.ReportDate.HasValue ? a1.ReportDate.Value.ToString("yyyy-MM-dd") : null))
            .ForMember(a => a.PaidDate, a => a.MapFrom(a1 => a1.PaidDate.HasValue ? a1.PaidDate.Value.ToString("yyyy-MM-dd") : null))
            .ForMember(a => a.TransactionDate, a => a.MapFrom(a1 => a1.TransactionDate.HasValue ? a1.TransactionDate.Value.ToString("yyyy-MM-dd") : null));

        CreateMap<MealReportImportRowDto, MealReportImportValidRowDto>()
            .ForMember(a => a.SentForPaymentDate, a => a.MapFrom(a1 => !string.IsNullOrEmpty(a1.SentForPaymentDate) ? DateTime.Parse(a1.SentForPaymentDate) : new DateTime?()))
            .ForMember(a => a.FirstSubmittedDate, a => a.MapFrom(a1 => !string.IsNullOrEmpty(a1.FirstSubmittedDate) ? DateTime.Parse(a1.FirstSubmittedDate) : new DateTime?()))
            .ForMember(a => a.ReportDate, a => a.MapFrom(a1 => !string.IsNullOrEmpty(a1.ReportDate) ? DateTime.Parse(a1.ReportDate) : new DateTime?()))
            .ForMember(a => a.PaidDate, a => a.MapFrom(a1 => !string.IsNullOrEmpty(a1.PaidDate) ? DateTime.Parse(a1.PaidDate) : new DateTime?()))
            .ForMember(a => a.TransactionDate, a => a.MapFrom(a1 => !string.IsNullOrEmpty(a1.TransactionDate) ? DateTime.Parse(a1.TransactionDate) : new DateTime?()))
            .ForMember(a => a.ExpenseAmount, a => a.MapFrom(a1 => !string.IsNullOrEmpty(a1.ExpenseAmount) ? decimal.Parse(a1.ExpenseAmount) : new decimal?()))
            .ForMember(a => a.ExchangeRate, a => a.MapFrom(a1 => !string.IsNullOrEmpty(a1.ExchangeRate) ? decimal.Parse(a1.ExchangeRate) : new decimal?()))
            .ForMember(a => a.ExpenseAmountRmb, a => a.MapFrom(a1 => !string.IsNullOrEmpty(a1.ExpenseAmountRmb) ? decimal.Parse(a1.ExpenseAmountRmb) : new decimal?()))
            .ForMember(a => a.ApprovedAmount, a => a.MapFrom(a1 => !string.IsNullOrEmpty(a1.ApprovedAmount) ? decimal.Parse(a1.ApprovedAmount) : new decimal?()))
            .ForMember(a => a.AmtPerAttendee, a => a.MapFrom(a1 => !string.IsNullOrEmpty(a1.AmtPerAttendee) ? decimal.Parse(a1.AmtPerAttendee) : new decimal?()))
            .ForMember(a => a.NumberOfAttendees, a => a.MapFrom(a1 => !string.IsNullOrEmpty(a1.NumberOfAttendees) ? int.Parse(a1.NumberOfAttendees) : new int?()));

        CreateMap<MealReportImportValidRowDto, MealReport>();

        CreateMap<MealReport, MealReportQueryResponseDto>()
            .ForMember(a => a.SentForPaymentDate, a => a.MapFrom(a1 => a1.SentForPaymentDate.HasValue ? a1.SentForPaymentDate.Value.ToString("yyyy-MM-dd") : null))
            .ForMember(a => a.FirstSubmittedDate, a => a.MapFrom(a1 => a1.FirstSubmittedDate.HasValue ? a1.FirstSubmittedDate.Value.ToString("yyyy-MM-dd") : null))
            .ForMember(a => a.ReportDate, a => a.MapFrom(a1 => a1.ReportDate.HasValue ? a1.ReportDate.Value.ToString("yyyy-MM-dd") : null))
            .ForMember(a => a.PaidDate, a => a.MapFrom(a1 => a1.PaidDate.HasValue ? a1.PaidDate.Value.ToString("yyyy-MM-dd") : null))
            .ForMember(a => a.TransactionDate, a => a.MapFrom(a1 => a1.TransactionDate.HasValue ? a1.TransactionDate.Value.ToString("yyyy-MM-dd") : null))
            .ForMember(a => a.ExpenseAmount, a => a.MapFrom(a1 => a1.ExpenseAmount.HasValue ? Math.Round(a1.ExpenseAmount.Value, 2) : a1.ExpenseAmount))
            .ForMember(a => a.ExchangeRate, a => a.MapFrom(a1 => a1.ExchangeRate.HasValue ? Math.Round(a1.ExchangeRate.Value, 2) : a1.ExchangeRate))
            .ForMember(a => a.ExpenseAmountRmb, a => a.MapFrom(a1 => a1.ExpenseAmountRmb.HasValue ? Math.Round(a1.ExpenseAmountRmb.Value, 2) : a1.ExpenseAmountRmb))
            .ForMember(a => a.ApprovedAmount, a => a.MapFrom(a1 => a1.ApprovedAmount.HasValue ? Math.Round(a1.ApprovedAmount.Value, 2) : a1.ApprovedAmount))
            .ForMember(a => a.AmtPerAttendee, a => a.MapFrom(a1 => a1.AmtPerAttendee.HasValue ? Math.Round(a1.AmtPerAttendee.Value, 2) : a1.AmtPerAttendee));

        #endregion
    }

    private string ToDateTimeLongString(DateTime? date)
    {
        if (date == null)
            return "";
        return date.Value.ToString("yyyy-MM-dd hh:mm:ss");
    }

    private string ToDateTimeShortString(DateTime? date)
    {
        if (date == null)
            return "";
        return date.Value.ToString("yyyy-MM-dd");
    }
}
