select newid() as spk_NexBPMCode,a.spk_bu as spk_BPMCode
,b.spk_NexBPMCode as spk_bu
,a.spk_approvalnumber
,a.spk_level0
,a.spk_level1
,a.spk_level2
,a.spk_level3
,a.spk_level4
,a.spk_level5
,a.spk_level6
into #spk_expenseapprovalmatrixconfigurationitem
from spk_expenseapprovalmatrixconfigurationitem_Tmp a
left join spk_organizationalmasterdata b on a.spk_bu=b.spk_BPMCode

IF OBJECT_ID(N'dbo.spk_expenseapprovalmatrixconfigurationitem', N'U') IS NOT NULL
BEGIN
	update a 
	set a.spk_BPMCode         = b.spk_BPMCode
        ,a.spk_bu             = b.spk_bu
        ,a.spk_approvalnumber = b.spk_approvalnumber
        ,a.spk_level0         = b.spk_level0
        ,a.spk_level1         = b.spk_level1
        ,a.spk_level2         = b.spk_level2
        ,a.spk_level3         = b.spk_level3
        ,a.spk_level4         = b.spk_level4
        ,a.spk_level5         = b.spk_level5
        ,a.spk_level6         = b.spk_level6
    from dbo.spk_expenseapprovalmatrixconfigurationitem a
    join #spk_expenseapprovalmatrixconfigurationitem b on a.spk_BPMCode = b.spk_BPMCode
    
    insert into dbo.spk_expenseapprovalmatrixconfigurationitem
    select a.spk_NexBPMCode
          ,a.spk_BPMCode
          ,a.spk_bu
          ,a.spk_approvalnumber
          ,a.spk_level0
          ,a.spk_level1
          ,a.spk_level2
          ,a.spk_level3
          ,a.spk_level4
          ,a.spk_level5
          ,a.spk_level6
	from #spk_expenseapprovalmatrixconfigurationitem a
	where NOT EXISTS (SELECT * FROM dbo.spk_expenseapprovalmatrixconfigurationitem where a.spk_BPMCode = spk_BPMCode)
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into dbo.spk_expenseapprovalmatrixconfigurationitem from #spk_expenseapprovalmatrixconfigurationitem
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END

