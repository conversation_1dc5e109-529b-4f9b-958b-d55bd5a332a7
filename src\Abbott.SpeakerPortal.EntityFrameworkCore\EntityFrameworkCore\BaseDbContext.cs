﻿using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.EfCoreRepository.Purchase;
using Abbott.SpeakerPortal.Entities;
using Abbott.SpeakerPortal.Entities.Agent;
using Abbott.SpeakerPortal.Entities.Budget;
using Abbott.SpeakerPortal.Entities.Common.Attachment;
using Abbott.SpeakerPortal.Entities.Common.Consent;
using Abbott.SpeakerPortal.Entities.Common.Sequence;
using Abbott.SpeakerPortal.Entities.Common.WeChat;
using Abbott.SpeakerPortal.Entities.Common.Workflow.WorkflowInstances;
using Abbott.SpeakerPortal.Entities.Common.Workflow.WorkflowTasks;
using Abbott.SpeakerPortal.Entities.Integration.Dspot;
using Abbott.SpeakerPortal.Entities.Integration.EpdOnlineMeeting;
using Abbott.SpeakerPortal.Entities.Message.OECPayStandardOperHistorys;
using Abbott.SpeakerPortal.Entities.OECPayStandardConfigDetails;
using Abbott.SpeakerPortal.Entities.OECPayStandardConfigs;
using Abbott.SpeakerPortal.Entities.OECPayStandards;
using Abbott.SpeakerPortal.Entities.OECPSAComExpenseTypes;
using Abbott.SpeakerPortal.Entities.OECPSAComPSALimitOperHistorys;
using Abbott.SpeakerPortal.Entities.OECPSAComPSALimits;
using Abbott.SpeakerPortal.Entities.OECPSASpeakerLimitExtras;
using Abbott.SpeakerPortal.Entities.OECPSASpeakerLimits;
using Abbott.SpeakerPortal.Entities.OECPSASpeakerLimitUseHistorys;
using Abbott.SpeakerPortal.Entities.OECSpeakerAuthApplys;
using Abbott.SpeakerPortal.Entities.OECSpeakerLevelDetails;
using Abbott.SpeakerPortal.Entities.OECSpeakerLevelOperHistorys;
using Abbott.SpeakerPortal.Entities.Purchase.PurBDApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurCategoryConfig;
using Abbott.SpeakerPortal.Entities.Purchase.PurBWApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurGRApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPOApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Entities.VendorApplicationFinancials;
using Abbott.SpeakerPortal.Entities.VendorApplicationOrgnizations;
using Abbott.SpeakerPortal.Entities.VendorApplicationPersonals;
using Abbott.SpeakerPortal.Entities.VendorApplications;
using Abbott.SpeakerPortal.Entities.VendorBlackListOperHistorys;
using Abbott.SpeakerPortal.Entities.VendorBlackLists;
using Abbott.SpeakerPortal.Entities.VendorFinancials;
using Abbott.SpeakerPortal.Entities.VendorOrgnizations;
using Abbott.SpeakerPortal.Entities.VendorPersonals;
using Abbott.SpeakerPortal.Entities.Vendors;

using Microsoft.EntityFrameworkCore;

using System;
using System.ComponentModel.DataAnnotations;

using Volo.Abp.AuditLogging.EntityFrameworkCore;
using Volo.Abp.BackgroundJobs.EntityFrameworkCore;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore.Modeling;
using Volo.Abp.FeatureManagement.EntityFrameworkCore;
using Volo.Abp.Identity;
using Volo.Abp.Identity.EntityFrameworkCore;
using Volo.Abp.ObjectExtending;
using Volo.Abp.OpenIddict.EntityFrameworkCore;
using Volo.Abp.PermissionManagement.EntityFrameworkCore;
using Volo.Abp.SettingManagement.EntityFrameworkCore;
using Volo.Abp.TenantManagement;
using Volo.Abp.TenantManagement.EntityFrameworkCore;
using Abbott.SpeakerPortal.Entities.Purchase.PurSlideConfig;
using System.Linq;
using Abbott.SpeakerPortal.Entities.OEC.OECIntercepts;
using Abbott.SpeakerPortal.Entities.SystemConfig.Slide;
using Azure.Storage.Blobs.Models;
using Abbott.SpeakerPortal.Entities.SystemConfig.FinanceReview;
using Abbott.SpeakerPortal.Entities.OEC.OECSpeakerAuthApplys;
using Abbott.SpeakerPortal.Entities.Purchase.PurThirdPartySpeaker;
using Abbott.SpeakerPortal.Entities.Common.Log;
using Abbott.SpeakerPortal.Entities.Integration.Veeva;
using Abbott.SpeakerPortal.Entities.Common.ScheduleJob;
using Abbott.SpeakerPortal.Entities.BPMEmail;
using Abbott.SpeakerPortal.Entities.Common.Workflow.CurrentApprovalTask;
using Abbott.SpeakerPortal.Entities.CrossBu.MarketActivity;
using Abbott.SpeakerPortal.Entities.CrossBu;
using Abbott.SpeakerPortal.Entities.Report.ProfessionalServiceTax;
using Abbott.SpeakerPortal.Entities.CrossBu.CustomerRelationHis;
using Abbott.SpeakerPortal.Entities.Integration.Bpm;
using Abbott.SpeakerPortal.Entities.CrossBu.ShareHis;
using static Abbott.SpeakerPortal.Enums.PurPAStatus;
using Abbott.SpeakerPortal.Entities.CrossBu.AccessLog;
using Abbott.SpeakerPortal.Entities.Approval;
using Abbott.SpeakerPortal.Entities.UserRoles.UserExtension;
using Abbott.SpeakerPortal.Entities.Report.ApprovalRecord;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Abbott.SpeakerPortal.Entities.Report.WPRActiveView;
using Abbott.SpeakerPortal.Entities.Report.WholeProcessReport;
using Abbott.SpeakerPortal.Entities.EFlow.STicket;
using Abbott.SpeakerPortal.Entities.EFlow.FOC;
using Abbott.SpeakerPortal.Entities.EFlow.Return;
using Abbott.SpeakerPortal.Entities.EFlow.PP;
using Abbott.SpeakerPortal.Entities.EFlow.Exchange;
using Abbott.SpeakerPortal.Entities.Report.GLH;
using Abbott.SpeakerPortal.Entities.MSA;
using Abbott.SpeakerPortal.Entities.MSA.MsaCompanyMapping;
using Abbott.SpeakerPortal.Entities.Concur.MealEmployee;
using Abbott.SpeakerPortal.Entities.Concur.MealOrgMapping;
using Abbott.SpeakerPortal.Entities.Concur.MealReport;

namespace Abbott.SpeakerPortal.EntityFrameworkCore
{
    public class BaseDbContext<DbContextType> : AbpDbContext<DbContextType> where DbContextType : DbContext
    {
        public BaseDbContext(DbContextOptions<DbContextType> options) : base(options)
        {
            ObjectExtensionManager.Instance.MapEfCoreProperty<IdentityUser, string>(EntityConsts.IdentityUser.OpenId, (entityTypeBuilder, propertyBuilder) => { propertyBuilder.HasMaxLength(50); });
            ObjectExtensionManager.Instance.MapEfCoreProperty<IdentityUser, string>(EntityConsts.IdentityUser.UnionId, (entityTypeBuilder, propertyBuilder) => { propertyBuilder.HasMaxLength(50); });
            ObjectExtensionManager.Instance.MapEfCoreProperty<IdentityUser, string>(EntityConsts.IdentityUser.StaffCode, (entityTypeBuilder, propertyBuilder) => { propertyBuilder.HasMaxLength(50); });
            ObjectExtensionManager.Instance.MapEfCoreProperty<IdentityUser, string>(EntityConsts.IdentityUser.DepartmentId, (entityTypeBuilder, propertyBuilder) => { propertyBuilder.HasMaxLength(500); });
            ObjectExtensionManager.Instance.MapEfCoreProperty<IdentityUser, string>(EntityConsts.IdentityUser.SignedVersion, (entityTypeBuilder, propertyBuilder) => { propertyBuilder.HasMaxLength(200); });
            ObjectExtensionManager.Instance.MapEfCoreProperty<IdentityUser, int?>(EntityConsts.IdentityUser.JobStatus);
            ObjectExtensionManager.Instance.MapEfCoreProperty<IdentityUser, Guid?>(EntityConsts.IdentityUser.MainDepartmentId);
            ObjectExtensionManager.Instance.MapEfCoreProperty<IdentityUser, DateTime?>(EntityConsts.IdentityUser.LastLoginTime);

            ObjectExtensionManager.Instance.MapEfCoreProperty<IdentityRole, bool>(EntityConsts.IdentityRole.IsDeleted);
            ObjectExtensionManager.Instance.MapEfCoreProperty<IdentityRole, string>(EntityConsts.IdentityRole.DisplayName);
        }

        #region Entities from the modules

        /* Notice: We only implemented IIdentityDbContext and ITenantManagementDbContext
         * and replaced them for this DbContext. This allows you to perform JOIN
         * queries for the entities of these modules over the repositories easily. You
         * typically don't need that for other modules. But, if you need, you can
         * implement the DbContext interface of the needed module and use ReplaceDbContext
         * attribute just like IIdentityDbContext and ITenantManagementDbContext.
         *
         * More info: Replacing a DbContext of a module ensures that the related module
         * uses this DbContext on runtime. Otherwise, it will use its own DbContext class.
         */

        //Identity
        public DbSet<IdentityUser> Users { get; set; }

        public DbSet<UserExtension> UserExtensions { get; set; }

        public DbSet<IdentityRole> Roles { get; set; }
        public DbSet<IdentityClaimType> ClaimTypes { get; set; }
        public DbSet<OrganizationUnit> OrganizationUnits { get; set; }
        public DbSet<IdentitySecurityLog> SecurityLogs { get; set; }
        public DbSet<IdentityLinkUser> LinkUsers { get; set; }
        public DbSet<IdentityUserDelegation> UserDelegations { get; }

        // Tenant Management
        public DbSet<Tenant> Tenants { get; set; }
        public DbSet<TenantConnectionString> TenantConnectionStrings { get; set; }

        #endregion

        #region Vendor


        /// <summary>
        /// Gets or sets the vendors.
        /// </summary>
        /// <value>
        /// The vendors.
        /// </value>
        public DbSet<Entities.Vendors.Vendor> Vendors { get; set; }

        /// <summary>
        /// Gets or sets the vendor personals.
        /// </summary>
        /// <value>
        /// The vendor personals.
        /// </value>
        public DbSet<VendorPersonal> VendorPersonals { get; set; }

        /// <summary>
        /// Gets or sets the vendor orgnizations.
        /// </summary>
        /// <value>
        /// The vendor orgnizations.
        /// </value>
        public DbSet<VendorOrgnization> VendorOrgnizations { get; set; }

        /// <summary>
        /// Gets or sets the vendor financials.
        /// </summary>
        /// <value>
        /// The vendor financials.
        /// </value>
        public DbSet<VendorFinancial> VendorFinancials { get; set; }

        /// <summary>
        /// Gets or sets the vendor application.
        /// </summary>
        /// <value>
        /// The vendor application.
        /// </value>
        public DbSet<VendorApplication> VendorApplication { get; set; }

        /// <summary>
        /// Gets or sets the vendor application personals.
        /// </summary>
        /// <value>
        /// The vendor application personals.
        /// </value>
        public DbSet<VendorApplicationPersonal> VendorApplicationPersonals { get; set; }

        /// <summary>
        /// Gets or sets the vendor application orgnizations.
        /// </summary>
        /// <value>
        /// The vendor application orgnizations.
        /// </value>
        public DbSet<VendorApplicationOrganization> VendorApplicationOrgnizations { get; set; }

        /// <summary>
        /// Gets or sets the vendor application financials.
        /// </summary>
        /// <value>
        /// The vendor application financials.
        /// </value>
        public DbSet<VendorApplicationFinancial> VendorApplicationFinancials { get; set; }

        /// <summary>
        /// Gets or sets the vendor black lists.
        /// </summary>
        /// <value>
        /// The vendor black lists.
        /// </value>
        public DbSet<VendorBlackList> VendorBlackLists { get; set; }

        /// <summary>
        /// Gets or sets the vendor black list oper historys.
        /// </summary>
        /// <value>
        /// The vendor black list oper historys.
        /// </value>
        public DbSet<VendorBlackListOperHistory> VendorBlackListOperHistorys { get; set; }

        #endregion

        #region OEC

        /// <summary>
        /// Gets or sets the oec Intercepts .
        /// </summary>
        /// <value>
        /// The oec Intercepts .
        /// </value>
        public DbSet<OECIntercept> OECIntercepts { get; set; }

        /// <summary>
        /// Gets or sets the oec Intercept Operate History.
        /// </summary>
        /// <value>
        /// The oec Intercept Operate History.
        /// </value>
        public DbSet<OECInterceptOperateHistory> OECInterceptOperateHistorys { get; set; }

        /// <summary>
        /// Gets or sets the oecpsa COM expense types.
        /// </summary>
        /// <value>
        /// The oecpsa COM expense types.
        /// </value>
        public DbSet<OECPSAComExpenseType> OECPSAComExpenseTypes { get; set; }

        /// <summary>
        /// Gets or sets the oecpsa COM psa limit oper historys.
        /// </summary>
        /// <value>
        /// The oecpsa COM psa limit oper historys.
        /// </value>
        public DbSet<OECPSAComPSALimitOperHistory> OECPSAComPSALimitOperHistorys { get; set; }

        /// <summary>
        /// Gets or sets the oecpsa COM psa limits.
        /// </summary>
        /// <value>
        /// The oecpsa COM psa limits.
        /// </value>
        public DbSet<OECPSAComPSALimit> OECPSAComPSALimits { get; set; }

        /// <summary>
        /// Gets or sets the oecpsa speaker limit extras.
        /// </summary>
        /// <value>
        /// The oecpsa speaker limit extras.
        /// </value>
        public DbSet<OECPSASpeakerLimitExtra> OECPSASpeakerLimitExtras { get; set; }

        /// <summary>
        /// Gets or sets the oecpsa speaker limits.
        /// </summary>
        /// <value>
        /// The oecpsa speaker limits.
        /// </value>
        public DbSet<OECPSASpeakerLimit> OECPSASpeakerLimits { get; set; }

        /// <summary>
        /// Gets or sets the oec speaker authentication applys.
        /// </summary>
        /// <value>
        /// The oec speaker authentication applys.
        /// </value>
        public DbSet<OECSpeakerAuthApply> OECSpeakerAuthApplys { get; set; }

        /// <summary>
        /// Gets or sets the oec speaker authentication apply details.
        /// </summary>
        /// <value>
        /// The oec speaker authentication apply details.
        /// </value>
        public DbSet<OECSpeakerAuthApplyUser> OECSpeakerAuthApplyUsers { get; set; }


        /// <summary>
        /// Gets or sets the oec speaker level details.
        /// </summary>
        /// <value>
        /// The oec speaker level details.
        /// </value>
        public DbSet<OECSpeakerLevelDetail> OECSpeakerLevelDetails { get; set; }

        /// <summary>
        /// Gets or sets the oec speaker level oper historys.
        /// </summary>
        /// <value>
        /// The oec speaker level oper historys.
        /// </value>
        public DbSet<OECSpeakerLevelOperHistory> OECSpeakerLevelOperHistorys { get; set; }

        /// <summary>
        /// Gets or sets the oec pay standards.
        /// </summary>
        /// <value>
        /// The oec pay standards.
        /// </value>
        public DbSet<OECPayStandard> OECPayStandards { get; set; }

        /// <summary>
        /// Gets or sets the oec pay standard configs.
        /// </summary>
        /// <value>
        /// The oec pay standard configs.
        /// </value>
        public DbSet<OECPayStandardConfig> OECPayStandardConfigs { get; set; }

        /// <summary>
        /// Gets or sets the oec pay standard configuration details.
        /// </summary>
        /// <value>
        /// The oec pay standard configuration details.
        /// </value>
        public DbSet<OECPayStandardConfigDetail> OECPayStandardConfigDetails { get; set; }

        /// <summary>
        /// Gets or sets the oec pay standard oper historys.
        /// </summary>
        /// <value>
        /// The oec pay standard oper historys.
        /// </value>
        public DbSet<OECPayStandardOperHistory> OECPayStandardOperHistorys { get; set; }

        public DbSet<OECPSASpeakerLimitUseHistory> OECPSASpeakerLimitUseHistorys { get; set; }

        #endregion

        #region Pur

        /// <summary>
        /// 采购申请
        /// </summary>
        public DbSet<PurPRApplication> PurPRApplications { get; set; }

        /// <summary>
        /// 采购申请明细项
        /// </summary>
        public DbSet<PurPRApplicationDetail> PurPRApplicationDetails { get; set; }

        /// <summary>
        /// 采购申请产品分摊表
        /// </summary>
        public DbSet<PurPRApplicationProductApportionment> PurPRApplicationProductApportionments { get; set; }

        /// <summary>
        /// 会议支持的预计费用
        /// </summary>
        public DbSet<PurPRApplicationCostItem> PurPRApplicationCostItem { get; set; }

        /// <summary>
        /// Hcp旅行社会务费
        /// </summary>
        public DbSet<PurPRApplicationHcpTravelLodgingFee> PurPRApplicationHcpTravelLodgingFees { get; set; }

        /// <summary>
        /// Gets or sets the oec PurExemptApplication.
        /// </summary>
        /// <value>
        /// The oec prevents.
        /// </value>
        public DbSet<PurBWApplication> PurBWApplication { get; set; }

        /// <summary>
        /// 采购品类配置
        /// </summary>
        public DbSet<PurCategoryConfig> PurCategoryConfigs { get; set; }

        /// <summary>
        /// 幻灯片配置
        /// </summary>
        public DbSet<SlideConfig> SlideConfigs { get; set; }

        /// <summary>
        /// 幻灯片操作
        /// </summary>
        public DbSet<SlideOperateHistory> SlideOperateHistorys { get; set; }

        /// <summary>
        /// 比价申请
        /// </summary>
        public DbSet<PurBDApplication> PurBDApplications { get; set; }

        /// <summary>
        /// 比价申请明细
        /// </summary>
        public DbSet<PurBDApplicationDetail> PurBDApplicationDetails { get; set; }

        /// <summary>
        /// 比价申请供应商明细
        /// </summary>
        public DbSet<PurBDApplicationSupplierDetail> PurBDApplicationSupplierDetails { get; set; }

        /// <summary>
        /// 采购订单
        /// </summary>
        public DbSet<PurPOApplication> PurPOApplications { get; set; }

        /// <summary>
        /// 采购订单明细
        /// </summary>
        public DbSet<PurPOApplicationDetails> PurPOApplicationDetails { get; set; }

        /// <summary>
        /// 收货单
        /// </summary>
        public DbSet<PurGRApplication> PurGRApplications { get; set; }

        /// <summary>
        /// 收货单明细
        /// </summary>
        public DbSet<PurGRApplicationDetail> PurGRApplicationDetails { get; set; }

        /// <summary>
        /// 收货单明细历史表
        /// </summary>
        public DbSet<PurGRApplicationDetailHistory> PurGRApplicationDetailHistorys { get; set; }

        /// <summary>
        /// 付款单
        /// </summary>
        public DbSet<PurPAApplication> PurPAApplications { get; set; }

        /// <summary>
        /// 付款单明细
        /// </summary>
        public DbSet<PurPAApplicationInvoice> PurPAApplicationDetails { get; set; }

        /// <summary>
        /// 付款单发票
        /// </summary>
        public DbSet<PurPAApplicationInvoice> PurPAApplicationInvoices { get; set; }

        /// <summary>
        /// 财务凭证信息
        /// </summary>
        public DbSet<PurPAFinancialVoucherInfo> PurPAFinancialVoucherInfos { get; set; }

        /// <summary>
        /// 付款申请退单原因
        /// </summary>
        public DbSet<PurPAReturnReasonInfo> PurPAReturnReasonInfos { get; set; }

        /// <summary>
        /// 退单原因明细
        /// </summary>
        public DbSet<PurPAReturnReasonDetail> PurPAReturnReasonDetails { get; set; }

        /// <summary>
        /// 第三方讲者明细
        /// </summary>
        public DbSet<PurThirdPartySpeakerItem> PurThirdPartySpeakerItems { get; set; }

        /// <summary>
        /// 第三方讲者例外审批编号
        /// </summary>
        public DbSet<PurThirdPartySpeakerExNum> PurThirdPartySpeakerExNums { get; set; }

        /// <summary>
        /// 第三方讲者明细历史
        /// </summary>
        public DbSet<PurThirdPartySpeakerItemHistory> PurThirdPartySpeakerItemHistorys { get; set; }

        /// <summary>
        /// 采购申请报表
        /// </summary>
        public DbSet<PurPRApplicationReport> PurPRApplicationReports { get; set; }

        #endregion

        #region Common

        public DbSet<Attachment> Attachments { get; set; }

        public DbSet<WorkflowInstance> WorkflowInstances { get; set; }

        public DbSet<WorkflowTask> WorkflowTasks { get; set; }

        public DbSet<QRCodeRecord> QRCodeRecords { get; set; }

        public DbSet<ConsentSigned> ConsentSigneds { get; set; }

        public DbSet<SequenceNum> SequenceNums { get; set; }

        public DbSet<WfApprovalTask> WfApprovalTasks { get; set; }
        #endregion

        #region Integraion

        /// <summary>
        /// DSPO的WholeProcessReport任务主表
        /// </summary>
        public DbSet<InteDspotWholeTask> InteDspotWholeTasks { get; set; }

        /// <summary>
        /// DSPO的WholeProcessReport任务子表
        /// </summary>
        public DbSet<InteDspotWholeTaskSub> InteDspotWholeTaskSubs { get; set; }

        /// <summary>
        /// OnlineMeeting的会议结算表
        /// </summary>
        public DbSet<InteOnlineMeetingSettlement> InteOnlineMeetingSettlements { get; set; }

        #region Veeva
        /// <summary>
        /// Veeva 接口对接日志
        /// </summary>
        public DbSet<VeevaDCRLog> VeevaDCRLogs { get; set; }

        /// <summary>
        /// Veeva 接口对接 （医师信息验证）
        /// </summary>
        public DbSet<VeevaResultHistory> VeevaResultHistorys { get; set; }
        #endregion

        #region BPCS

        public DbSet<BpcsAbk> BpcsAbk { get; set; }

        public DbSet<BpcsAvt> BpcsAvt { get; set; }

        public DbSet<BpcsGcc> BpcsGcc { get; set; }

        public DbSet<BpcsZcc> BpcsZcc { get; set; }

        public DbSet<BpcsZpa> BpcsZpa { get; set; }

        public DbSet<BpcsZrc> BpcsZrc { get; set; }

        public DbSet<BpcsAvm> BpcsAvm { get; set; }

        public DbSet<BpcsPmfvm> BpcsPmfvm { get; set; }

        public DbSet<BpcsAph> BpcsAph { get; set; }

        public DbSet<BpcsAml> BpcsAml { get; set; }

        public DbSet<BpcsGlh> BpcsGlh { get; set; }

        public DbSet<BpcsGcr> BpcsGcr { get; set; }

        public DbSet<EdiLogVendorMaster> EdiLogVendorMasters { get; set; }

        public DbSet<EdiLogApInvioce> EdiLogApInvioces { get; set; }

        public DbSet<EdiLogUncompletedPRVendor> EdiLogUncompletedPRVendors { get; set; }

        #endregion

        #endregion

        #region 预算管理
        /// <summary>
        /// 主预算
        /// </summary>
        public DbSet<BdMasterBudget> BdMasterBudgets { get; set; }
        /// <summary>
        /// 子预算
        /// </summary>
        public DbSet<BdSubBudget> BdSubBudgets { get; set; }
        /// <summary>
        /// 月份-预算
        /// </summary>
        public DbSet<BdMonthlyBudget> BdMonthlyBudgets { get; set; }
        /// <summary>
        /// 预算历史记录
        /// </summary>
        public DbSet<BdHistory> BdHistorys { get; set; }
        /// <summary>
        /// 预算的使用
        /// </summary>
        public DbSet<BdBudgetUse> BdBudgetUses { get; set; }
        /// <summary>
        /// 预算的退回
        /// </summary>
        public DbSet<BdBudgetReturn> BdBudgetReturns { get; set; }
        /// <summary>
        /// 子预算月份变更历史
        /// </summary>
        public DbSet<BdSubBudgetMonthChangeHistory> SubBudgetMonthChangeHistorys { get; set; }
        /// <summary>
        /// 子预算与用户的关系
        /// </summary>
        public DbSet<BdSubBudgetMapping> BdSubBudgetMappings { get; set; }
        #endregion

        #region 财务出纳支付
        public DbSet<FinanceCashierPaymentInfo> FinanceCashierPaymentInfos { get; set; }

        public DbSet<PurPAApplicationRef> PurPAApplicationRefs { get; set; }
        #endregion

        #region System Config
        public DbSet<FinanceReviewConfig> FinanceReviewConfigs { get; set; }
        public DbSet<FinanceReviewConfigHistory> FinanceReviewConfigHistorys { get; set; }
        #endregion

        #region 邮件记录及处理
        public DbSet<SendEmailRecord> SendEmailRecords { get; set; }
        #endregion

        #region Cross Bu

        public DbSet<Activity> Activities { get; set; }

        public DbSet<ActivityDepartment> ActivityDepartments { get; set; }

        public DbSet<ActivityHospital> ActivityHospitals { get; set; }

        public DbSet<ActivityBu> ActivityBus { get; set; }

        public DbSet<ActivityUserInformation> ActivityUserInformations { get; set; }

        public DbSet<SuggestionFeedback> SuggestionFeedbacks { get; set; }

        public DbSet<CustomerRelation> CustomerRelations { get; set; }

        public DbSet<JoinToMarketActivity> JoinToMarketActivities { get; set; }

        public DbSet<CustomerRelationHistory> CustomerRelationHistorys { get; set; }

        public DbSet<ContactShareRecords> ContactShareRecords { get; set; }

        public DbSet<AccessLog> AccessLogs { get; set; }

        #endregion

        #region 报表管理

        /// <summary>
        /// 专业服务报税
        /// </summary>
        public DbSet<ProfessionalServiceTaxReport> ProfessionalServiceTaxReports { get; set; }

        /// <summary>
        /// 审批记录报表
        /// </summary>
        public DbSet<ApprovalRecordReport> ApprovalRecordReports { get; set; }

        /// <summary>
        /// 全流程报表的非活动数据(中间表)
        /// </summary>
        public DbSet<RPT_WholeProcessReport> PRT_WholeProcessReports { get; set; }

        /// <summary>
        /// 全流程报表的活动数据视图
        /// </summary>
        public DbSet<UVW_WPR_Active> UVW_WPR_Actives { get; set; }

        /// <summary>
        /// PA 关联GLH 支付时间表 --全流程报表需要
        /// </summary>
        public DbSet<PAJoinBpcsGlh> PAJoinBpcsGlhs { get; set; }
        #endregion

        #region 审批记录表
        /// <summary>
        /// 审批创建记录
        /// </summary>
        public DbSet<InitializeApprovalRecord> InitializeApprovalRecords { get; set; }
        #endregion

        #region EFlow-STicket
        public DbSet<StoreInfo> StoreInfos { get; set; }

        public DbSet<ClientInfo> ClientInfos { get; set; }

        public DbSet<ProductDetail> ProductDetails { get; set; }

        /// <summary>
        /// 核销申请
        /// </summary>
        public DbSet<STicketApplication> STicketApplications { get; set; }

        /// <summary>
        /// 核销申请明细项
        /// </summary>
        public DbSet<STicketApplicationDetail> STicketApplicationDetails { get; set; }

        /// <summary>
        /// FOC主预算
        /// </summary>
        public DbSet<FocMasterBudget> FocMasterBudgets { get; set; }

        /// <summary>
        /// FOC子预算
        /// </summary>
        public DbSet<FocSubBudget> FocSubBudgets { get; set; }

        /// <summary>
        /// FOC月份-预算
        /// </summary>
        public DbSet<FocMonthlyBudget> FocMonthlyBudgets { get; set; }

        /// <summary>
        /// FOC预算历史记录
        /// </summary>
        public DbSet<FocBudgetHistory> FocBudgetHistorys { get; set; }

        /// <summary>
        /// FOC子预算月份变更历史
        /// </summary>
        public DbSet<FocSubBudgetMonthChangeHistory> FocSubBudgetMonthChangeHistorys { get; set; }

        /// <summary>
        /// FOC预算使用
        /// </summary>
        public DbSet<FocBudgetUse> FocBudgetUses { get; set; }
        /// <summary>
        /// FOC预算退回
        /// </summary>
        public DbSet<FocBudgetReturn> FocBudgetReturns { get; set; }

        /// <summary>
        /// FOC申请
        /// </summary>
        public DbSet<FOCApplication> FOCApplications { get; set; }

        /// <summary>
        /// FOC申请明细
        /// </summary>
        public DbSet<FOCApplicationDetail> FOCApplicationDetails { get; set; }

        /// <summary>
        /// FOC申请明细选择产品
        /// </summary>
        public DbSet<FOCApplicationProductDetail> FOCApplicationProductDetails { get; set; }

        /// <summary>
        /// FOC申请物流信息
        /// </summary>
        public DbSet<FOCApplicationLogisticsDetail> FOCApplicationLogisticsDetails { get; set; }

        /// <summary>
        ///SOI核销结果
        /// </summary>
        public DbSet<SOIWriteoffResult> SOIWriteoffResults { get; set; }
        /// <summary>
        /// 退货申请
        /// </summary>
        /// <value>
        /// The return applications.
        /// </value>
        public DbSet<ReturnApplication> ReturnApplications { get; set; }
        /// <summary>
        /// 退货申请详情
        /// </summary>
        /// <value>
        /// The return application details.
        /// </value>
        public DbSet<ReturnApplicationDetail> ReturnApplicationDetails { get; set; }

        /// <summary>
        /// PP大区主数据
        /// </summary>
        public DbSet<PPRegion> PPRegions { get; set; }

        /// <summary>
        /// PP成本中心主数据
        /// </summary>
        public DbSet<PPCostCenter> PPCostCenters { get; set; }

        ///// <summary>
        ///// 换货申请
        ///// </summary>
        ///// <value>
        ///// The exchange applications.
        ///// </value>
        //public DbSet<ExchangeApplication> ExchangeApplications { get; set; }
        ///// <summary>
        ///// 换货详情
        ///// </summary>
        ///// <value>
        ///// The exchange application details.
        ///// </value>
        //public DbSet<ExchangeApplicationDetail> ExchangeApplicationDetails { get; set; }
        #endregion

        #region 审批结果处理
        public DbSet<PostApprovalAction> PostApprovalActions { get; set; }
        #endregion

        /// <summary>
        /// Gets or sets the message infos.
        /// </summary>
        /// <value>
        /// The message infos.
        /// </value>
        public DbSet<MessageInfo> MessageInfos { get; set; }

        /// <summary>
        /// Bpm OM 日志接口数据
        /// </summary>
        public DbSet<BpmTpmInterfaceLog> BpmTpmInterfaceLogs { get; set; }

        #region MSA
        /// <summary>
        /// 主服务协议
        /// </summary>
        public DbSet<MsaMasterServiceAgreement> MsaMasterServiceAgreements { get; set; }
        /// <summary>
        /// 工作说明书
        /// </summary>
        public DbSet<MsaStatementOfWork> MsaStatementOfWorks { get; set; }
        /// <summary>
        /// MSA与BU的多对多关系
        /// </summary>
        public DbSet<MsaBuMapping> MsaBuMappings { get; set; }

        /// <summary>
        /// Msa关联公司
        /// </summary>
        public DbSet<MsaCompanyMapping> MsaCompanyMappings { get; set; }

        /// <summary>
        /// MSA与服务类型(采购品类一级)的多对多关系
        /// </summary>
        public DbSet<MsaServiceTypeMapping> MsaServiceTypeMappings { get; set; }
        /// <summary>
        /// 主服务协议变更历史
        /// </summary>
        public DbSet<MsaHistory> MsaHistorys { get; set; }
        #endregion

        #region Concur

        /// <summary>
        /// 员工分组配置
        /// </summary>
        public DbSet<MealEmployee> MealEmployees { get; set; }

        /// <summary>
        /// 机构转换配置
        /// </summary>
        public DbSet<MealOrgMapping> MealOrgMappings { get; set; }

        /// <summary>
        /// 机构转换配置变更历史
        /// </summary>
        public DbSet<MealOrgMappingHistory> MealOrgMappingHistories { get; set; }

        /// <summary>
        /// 用餐报告
        /// </summary>
        public DbSet<MealReport> MealReports { get; set; }

        #endregion

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            /* Include modules to your migration db context */

            builder.ConfigurePermissionManagement();
            builder.ConfigureSettingManagement();
            builder.ConfigureBackgroundJobs();
            builder.ConfigureAuditLogging();
            builder.ConfigureIdentity();
            builder.ConfigureOpenIddict();
            builder.ConfigureFeatureManagement();
            builder.ConfigureTenantManagement();

            /* Configure your own tables/entities inside here */


            builder.Entity<UserExtension>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "UserExtensions", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
            });

            #region Vendor

            builder.Entity<Entities.Vendors.Vendor>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "Vendors", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                b.HasIndex(c => new
                {
                    c.ApplicationId,
                    c.VendorCode,
                    c.VendorType,
                    c.Status,
                    c.SPLevel,
                    c.HospitalId,
                    c.StandardHosDepId
                })
                .IncludeProperties(a => new { a.IsDeleted, a.UserId, a.Description, a.HospitalName, a.StandardHosDepName, a.EpdId });
            });

            builder.Entity<VendorPersonal>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "VendorPersonals", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                b.HasIndex(c => new
                {
                    c.VendorId,
                    c.CardNo
                });
            });

            builder.Entity<VendorOrgnization>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "VendorOrgnizations", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                b.HasIndex(c => new
                {
                    c.VendorId,
                    c.VendorName,
                    c.VendorEngName
                });
            });

            builder.Entity<VendorFinancial>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "VendorFinancials", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                b.HasIndex(c => new
                {
                    c.VendorId,
                    c.VendorCode
                });
            });

            builder.Entity<VendorApplication>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "VendorApplications", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                b.HasIndex(c => new
                {
                    c.VendorId,
                    c.ApplicationCode,
                    c.VendorCode
                });
                b.HasIndex(c => new
                {
                    c.IsDeleted,
                    c.CreationTime,
                });
                b.HasIndex(c => new
                {
                    c.ApplicationType,
                    c.VendorType,
                    c.ApplyTime,
                    c.ApplyUserName,
                    c.ApplyUserBu,
                    c.ApplyDeptName,
                });

                //用于任务中心，我发起的代办数量统计
                b.HasIndex(b1 => new { b1.Status }).IncludeProperties(b1 => new { b1.ApplicationType, b1.VendorType, b1.ApplyUserId, b1.IsDeleted, b1.TransfereeId });
            });

            builder.Entity<VendorApplicationPersonal>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "VendorApplicationPersonals", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                b.HasIndex(c => new
                {
                    c.ApplicationId,
                    c.CardNo
                });
            });

            builder.Entity<VendorApplicationOrganization>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "VendorApplicationOrgnizations", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                b.HasIndex(c => new
                {
                    c.ApplicationId,
                    c.VendorName,
                    c.VendorEngName
                });
            });

            builder.Entity<VendorApplicationFinancial>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "VendorApplicationFinancials", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                b.HasIndex(c => new
                {
                    c.ApplicationId,
                    c.VendorCode
                });
            });

            builder.Entity<VendorBlackList>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "VendorBlackLists", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

            });

            builder.Entity<VendorBlackListOperHistory>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "VendorBlackListOperHistorys", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
            });

            #endregion

            #region OEC

            builder.Entity<OECIntercept>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "OECIntercepts", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
                b.HasIndex(c => new
                {
                    c.PRDetailId,
                    c.InterceptTime,
                });
            });

            builder.Entity<OECInterceptOperateHistory>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "OECInterceptOperateHistorys", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
                b.HasIndex(c => new
                {
                    c.PRDetailId,
                });
            });

            builder.Entity<OECPSAComExpenseType>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "OECPSAComExpenseTypes", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                b.HasIndex(c => new
                {
                    c.ComPSALimitId,
                });
            });

            builder.Entity<OECPSAComPSALimitOperHistory>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "OECPSAComPSALimitOperHistorys", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                b.HasIndex(c => new
                {
                    c.ComPSALimitId,
                });
            });

            builder.Entity<OECPSAComPSALimit>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "OECPSAComPSALimits", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
            });


            builder.Entity<OECPSASpeakerLimitExtra>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "OECPSASpeakerLimitExtras", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                b.HasIndex(c => new
                {
                    c.ComPSALimitId,
                    c.VendorId
                });
            });

            builder.Entity<OECPSASpeakerLimit>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "OECPSASpeakerLimits", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                b.HasIndex(c => new
                {
                    c.ComPSALimitId,
                    c.VendorId
                });
            });

            builder.Entity<OECSpeakerAuthApply>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "OECSpeakerAuthApplys", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
                b.HasIndex(c => new
                {
                    c.IsDeleted,
                    c.CreationTime,
                });
            });

            builder.Entity<OECSpeakerAuthApplyUser>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "OECSpeakerAuthApplyUsers", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
                b.HasIndex(c => new
                {
                    c.VendorCode,
                });
            });

            builder.Entity<OECSpeakerLevelDetail>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "OECSpeakerLevelDetails", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                b.HasIndex(c => new
                {
                    c.SpeakerLevelCode
                });
            });

            builder.Entity<OECSpeakerLevelOperHistory>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "OECSpeakerLevelOperHistorys", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
            });

            builder.Entity<OECPayStandard>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "OECPayStandards", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
                b.HasIndex(c => new
                {
                    c.IsDeleted,
                    c.CreationTime
                });
            });

            builder.Entity<OECPayStandardConfig>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "OECPayStandardConfigs", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
            });

            builder.Entity<OECPayStandardConfigDetail>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "OECPayStandardConfigDetails", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
            });

            builder.Entity<OECPayStandardOperHistory>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "OECPayStandardOperHistorys", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
            });

            builder.Entity<OECPSASpeakerLimitUseHistory>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "OECPSASpeakerLimitUseHistorys", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                b.HasIndex(c => new
                {
                    c.VendorId,
                    c.PsaExternalId,
                    c.EffectiveDate,
                    c.BuId,
                }).IncludeProperties(a => new { a.OperDetailType, a.OperateType });
            });

            #endregion

            #region Purchase

            #region 采购申请

            builder.Entity<PurPRApplication>(a =>
            {
                a.ToTable(SpeakerPortalConsts.DbTablePrefix + "PurPRApplications", SpeakerPortalConsts.DbSchema);
                //a.HasIndex(a => a.ApplicationCode).IsUnique();
                a.HasIndex(a => new { a.Status, a.ApplyUserId, a.AgentId, a.TransfereeId, a.ApplicationCode, a.ApplyTime, a.ApplyUserIdName, a.ApplyUserDeptName, a.BudgetCode, a.SubBudgetCode, a.CompanyId })
                    .IncludeProperties(a => new { a.CostCenter, a.TotalAmount, a.IsDeleted });
                a.HasIndex(c => new
                {
                    c.IsDeleted,
                    c.CreationTime
                });
                a.HasIndex(a => a.ActiveNo).HasFilter("ActiveNo IS NOT NULL");
                //PR列表查询：审批人角色查询时：推荐Status字段索引INCLUDE ([ApplicationCode],[ApplyUserId],[ApplyTime],[ApplyUserBu],[CostCenter],[AgentId],[CompanyId],[TotalAmount],[CreationTime],[LastModificationTime],[IsDeleted],[ApplyUserDept],[ApplyUserDeptName],[ApplyUserIdName],[BudgetCode],[SubBudgetCode],[SubBudgetId],[MeetingStatus])。与任务中心的查询的Status索引的INCLUDE列进行合并(如果新建Status的INCLUDE索引会冲突)。
                //任务中心-采购申请列表查询的优化Status字段索引INCLUDE([ApplicationCode],[ApplyUserId],[ApplyTime],[CostCenter],[IsEsignUsed],[CompanyId],[TotalAmount],[IsDeleted],[ApplyUserDeptName],[SubBudgetCode],[MeetingStatus],[TransfereeId])
                a.HasIndex(a => a.Status).IncludeProperties(a => new { a.ApplicationCode, a.ApplyUserId, a.ApplyTime, a.CostCenter, a.CompanyId, a.TotalAmount, a.IsDeleted, a.ApplyUserDeptName, a.SubBudgetCode, a.MeetingStatus, a.TransfereeId, a.AgentId, a.IsEsignUsed, a.ApplyUserDept, a.ApplyUserIdName, a.SubBudgetId });
                //用于PA付款列表查询的角色权限过滤时
                a.HasIndex(a => a.ApplicationCode).IncludeProperties(a => new { a.IsDeleted, a.SubBudgetId });
                //用于第三方讲者管理列表-汇总，查询和角色权限过滤
                a.HasIndex(a => new { a.ExpenseTypeCode, a.IsOnceApproved, a.ApplicationCode }).IncludeProperties(a => new { a.Status, a.ApplyUserId, a.ApplyTime, a.ApplyUserBu, a.AgentId, a.IsDeleted, a.ApplyUserDept, a.ApplyUserDeptName, a.ApplyUserIdName, a.SubBudgetId, a.TotalAmountRMB, a.TransfereeId });

                //用于子预算导出接口ExportSubbudgetList
                a.HasIndex(a => a.SubBudgetId).IncludeProperties(a => new { a.Status, a.IsDeleted, a.TotalAmountRMB });

                a.ConfigureByConvention();
            });

            builder.Entity<PurPRApplicationDetail>(a =>
            {
                a.ToTable(SpeakerPortalConsts.DbTablePrefix + "PurPRApplicationDetails", SpeakerPortalConsts.DbSchema);
                a.HasIndex(a => new { a.PRApplicationId, a.PayMethod, a.IsVendorConfimed, a.PushFlag, a.OrderStatusFlag, a.PurchaserId, a.BiddingId, a.BWApplicationId, a.IsHedge });
                // 新增的索引(采购推送列表使用)，对应于 SQL 脚本中的 IX_PurPRApplicationDetails_PayMethod_PushFlag
                a.HasIndex(x => new { x.PayMethod, x.PushFlag })
                    .HasDatabaseName("IX_PurPRApplicationDetails_PayMethod_PushFlag")
                    .IncludeProperties(e => new
                    {
                        e.PRApplicationId,
                        e.EstimateDate,
                        e.VendorId,
                        e.Content,
                        e.Quantity,
                        e.UnitPrice,
                        e.TotalAmount,
                        e.RceNo,
                        e.IsDeleted,
                        e.PurchaserId,
                        e.PushTime,
                        e.IsOffline,
                        e.OfflineNo,
                        e.VendorName,
                        e.RowNo,
                        e.OrderStatusFlag,
                        e.BiddingId,
                        e.TotalAmountRMB,
                        e.BWApplicationId
                    });

                //用于任务中心，我发起的代办数量统计
                a.HasIndex(a => new { a.IsHedge }).IncludeProperties(a => new { a.PRApplicationId, a.IsDeleted, a.PushFlag, a.IsVendorConfimed });
                //用于采购列表，按预计日期搜索
                a.HasIndex(a => new { a.EstimateDate }).IncludeProperties(a => new { a.PRApplicationId, a.IsDeleted });

                a.ConfigureByConvention();
            });

            builder.Entity<PurPRApplicationProductApportionment>(a =>
            {
                a.ToTable(SpeakerPortalConsts.DbTablePrefix + "PurPRApplicationProductApportionments", SpeakerPortalConsts.DbSchema);
                a.HasIndex(a => new { a.PRApplicationId, a.PRApplicationDetailId });
                a.ConfigureByConvention();
            });

            builder.Entity<PurPRApplicationDetailBackupVendor>(a =>
            {
                a.ToTable(SpeakerPortalConsts.DbTablePrefix + "PurPRApplicationDetailBackupVendors", SpeakerPortalConsts.DbSchema);
                a.HasIndex(a => new { a.PRApplicationId, a.PRApplicationDetailId });
                a.ConfigureByConvention();
            });

            builder.Entity<PurPRApplicationCostItem>(a =>
            {
                a.ToTable(SpeakerPortalConsts.DbTablePrefix + "PurPRApplicationCostItems", SpeakerPortalConsts.DbSchema);
                a.HasIndex(a => a.PRApplicationId);
                a.ConfigureByConvention();
            });

            builder.Entity<PurPRApplicationHcpTravelLodgingFee>(a =>
            {
                a.ToTable(SpeakerPortalConsts.DbTablePrefix + "PurPRApplicationHcpTravelLodgingFees", SpeakerPortalConsts.DbSchema);
                a.HasIndex(a => a.PRApplicationId);
                a.ConfigureByConvention();
            });

            builder.Entity<PurPRApplicationReport>(a =>
            {
                a.ToTable(SpeakerPortalConsts.DbTablePrefix + "PurPRApplicationReports", SpeakerPortalConsts.DbSchema);
                a.HasIndex(a => new { a.Status, a.ApplicationCode, a.ApplyUserBu, a.CostCenter, a.ApplyUserIdName, a.ApplyUserDeptName, a.BudgetCode, a.SubBudgetCode, a.CompanyId, a.ApplyTime, a.EstimateDate });
                a.ConfigureByConvention();
            });

            #endregion

            #region 比价申请

            builder.Entity<PurBDApplication>(a =>
            {
                a.ToTable(SpeakerPortalConsts.DbTablePrefix + "PurBDApplications", SpeakerPortalConsts.DbSchema);
                a.HasIndex(a => new { a.ApplicationCode, a.ApplyTime, a.PRApplicationId, a.Status });
                a.ConfigureByConvention();
                a.HasIndex(a => new
                {
                    a.IsDeleted,
                    a.CreationTime
                });

                //用于任务中心，我发起的代办数量统计
                a.HasIndex(a1 => new { a1.Status }).IncludeProperties(a1 => new { a1.ApplyUserId, a1.IsDeleted, a1.TransfereeId });
            });

            builder.Entity<PurBDApplicationDetail>(a =>
            {
                a.ToTable(SpeakerPortalConsts.DbTablePrefix + "PurBDApplicationDetails", SpeakerPortalConsts.DbSchema);
                a.HasIndex(a => new { a.BDApplicationId });
                a.ConfigureByConvention();
            });

            builder.Entity<PurBDApplicationSupplierDetail>(a =>
            {
                a.ToTable(SpeakerPortalConsts.DbTablePrefix + "PurBDApplicationSupplierDetails", SpeakerPortalConsts.DbSchema);
                a.HasIndex(a => new { a.BDApplicationId });
                a.ConfigureByConvention();
            });
            #endregion

            #region 采购订单

            builder.Entity<PurPOApplication>(a =>
            {
                a.ToTable(SpeakerPortalConsts.DbTablePrefix + "PurPOApplications", SpeakerPortalConsts.DbSchema);
                a.HasIndex(a => new { a.ApplicationCode, a.ApplyTime, a.PRId, a.Status });
                a.ConfigureByConvention();
                a.HasIndex(c => new
                {
                    c.IsDeleted,
                    c.CreationTime
                });
                //1、PO列表查询-角色权限优化：ON [dbo].[PurPOApplications] ([Status]) INCLUDE([ApplyUserId], [ApplyTime], [ApplyUserBu], [POType], [CompanyId], [VendorId], [VendorPorperty], [PRType], [IsDeleted], [TotalAmount], [TotalAmountTax], [ApplyUserBuName], [ApplyUserName], [VendorName], [ApsPorperty], [PRId], [VendorCode], [ApplyUserBuToDeptName], [ApplyUserDept], [TransfereeId])
                //2、用于任务中心，我发起的代办数量统计
                //3、用于子预算导出接口ExportSubbudgetList，增加Include字段：ExchangeRate
                a.HasIndex(a1 => a1.Status).IncludeProperties(a1 => new { a1.ApplicationCode, a1.ApplyUserId, a1.ApplyTime, a1.POType, a1.CompanyId, a1.VendorId, a1.VendorPorperty, a1.PRType, a1.IsDeleted, a1.TotalAmount, a1.TotalAmountTax, a1.ApplyUserBuName, a1.ApplyUserName, a1.VendorName, a1.ApsPorperty, a1.PRId, a1.VendorCode, a1.ApplyUserBuToDeptName, a1.ApplyUserDept, a1.TransfereeId, a1.ExchangeRate });

                //用于删除操作/审批拒绝/作废时，检索Msa和Sow是否还有其他po在使用
                a.HasIndex(a => a.MsaId).HasFilter("MsaId IS NOT NULL");
                a.HasIndex(a => a.SowId).HasFilter("SowId IS NOT NULL");
            });

            builder.Entity<PurPOApplicationDetails>(a =>
            {
                a.ToTable(SpeakerPortalConsts.DbTablePrefix + "PurPOApplicationDetails", SpeakerPortalConsts.DbSchema);
                a.HasIndex(a => new { a.PRDetailId, a.POApplicationId });
                a.ConfigureByConvention();
            });

            #endregion

            #region 采购品类

            builder.Entity<PurCategoryConfig>(a =>
            {
                a.ToTable(SpeakerPortalConsts.DbTablePrefix + "PurCategoryConfigs", SpeakerPortalConsts.DbSchema);
                a.ConfigureByConvention();
            });

            #endregion

            #region 收货单申请

            builder.Entity<PurGRApplication>(a =>
            {
                a.ToTable(SpeakerPortalConsts.DbTablePrefix + "PurGRApplications", SpeakerPortalConsts.DbSchema);
                a.HasIndex(a => new { a.ApplicationCode, a.PrId, a.PoId });
                a.ConfigureByConvention();
                a.HasIndex(c => new
                {
                    c.IsDeleted,
                    c.CreationTime
                });

                //用于任务中心，我发起的代办数量统计
                a.HasIndex(a1 => new { a1.Status }).IncludeProperties(a1 => new { a1.ApplyUserId, a1.IsDeleted, a1.TransfereeId });
            });

            builder.Entity<PurGRApplicationDetail>(a =>
            {
                a.ToTable(SpeakerPortalConsts.DbTablePrefix + "PurGRApplicationDetails", SpeakerPortalConsts.DbSchema);
                a.HasIndex(a => new { a.PRDetailId });
                a.HasIndex(a => new { a.GRApplicationId, a.PRDetailId, a.PODetailId });
                a.ConfigureByConvention();
            });

            builder.Entity<PurGRApplicationDetailHistory>(a =>
            {
                a.ToTable(SpeakerPortalConsts.DbTablePrefix + "PurGRApplicationDetailHistorys", SpeakerPortalConsts.DbSchema);
                a.HasIndex(a => new { a.GRApplicationId, a.PRDetailId, a.PODetailId, a.PurPAApplicationId });
                a.ConfigureByConvention();
            });

            #endregion

            #region 付款单申请

            builder.Entity<PurPAApplication>(a =>
            {
                a.ToTable(SpeakerPortalConsts.DbTablePrefix + "PurPAApplications", SpeakerPortalConsts.DbSchema);
                a.HasIndex(a => new { a.ApplicationCode, a.PRId, a.POId, a.GRId, a.ApplyTime, a.Status, a.DeliveryMode, a.TaskType, a.AcceptedTime });
                a.ConfigureByConvention();
                a.HasIndex(c => new
                {
                    c.IsDeleted,
                    c.CreationTime
                });
                //1、GR列表的导出，角色权限过滤添加ApplicationCode、ApplyTime、GRId
                //2、用于任务中心，我发起的代办数量统计
                //3、用于子预算导出接口ExportSubbudgetList，增加Include字段：ExchangeRate, PayTotalAmount, PRId, POId
                a.HasIndex(a1 => new { a1.Status }).IncludeProperties(a1 => new { a1.ApplyUserId, a1.IsDeleted, a1.TransfereeId, a1.ApplicationCode, a1.ApplyTime, a1.GRId, a1.ExchangeRate, a1.PayTotalAmount, a1.PRId, a1.POId });
                //用于PA付款列表查询的角色权限过滤时
                a.HasIndex(a => a.PRId).IncludeProperties(a => new { a.ApplicationCode, a.Status, a.ApplyUserId, a.ApplyUserName, a.ApplyTime, a.ApplyUserBu, a.ApplyUserBuToDeptName, a.GRApplicationCode, a.POApplicationCode, a.PaymentType, a.IsBackupInvoice, a.CreationTime, a.IsDeleted, a.VendorName, a.PayTotalAmount, a.EstimatedPaymentDate, a.TransfereeId, a.ApplyUserBuToDept });

                //用于发票报表
                a.HasIndex(a => a.ApplyUserId).IncludeProperties(a => new { a.ApplicationCode, a.Status, a.ApplyUserName, a.ApplyUserBuName, a.GRId, a.GRApplicationCode, a.POId, a.POApplicationCode, a.IsBackupInvoice, a.InvoiceDescription, a.IsDeleted, a.VendorName, a.CompanyName, a.PayTotalAmount, a.EstimatedPaymentDate, a.PRId, a.ApprovedDate, a.ApprovedUserId, a.Currency });
            });

            builder.Entity<PurPAApplicationInvoice>(a =>
            {
                a.ToTable(SpeakerPortalConsts.DbTablePrefix + "PurPAApplicationInvoices", SpeakerPortalConsts.DbSchema);

                //用于发票报表
                a.HasIndex(a => new { a.PurPAApplicationId });

                a.ConfigureByConvention();
            });

            builder.Entity<PurPAApplicationDetail>(a =>
            {
                a.ToTable(SpeakerPortalConsts.DbTablePrefix + "PurPAApplicationDetails", SpeakerPortalConsts.DbSchema);
                a.HasIndex(a => new { a.PurPAApplicationId, a.PRDetailId, a.PODetailId, a.GRHistoryId, a.GRApplicationDetailId });
                a.ConfigureByConvention();
            });

            builder.Entity<PurPAFinancialVoucherInfo>(a =>
            {
                a.ToTable(SpeakerPortalConsts.DbTablePrefix + "PurPAFinancialVoucherInfos", SpeakerPortalConsts.DbSchema);
                a.ConfigureByConvention();
            });

            builder.Entity<PurPAReturnReasonInfo>(a =>
            {
                a.ToTable(SpeakerPortalConsts.DbTablePrefix + "PurPAReturnReasonInfos", SpeakerPortalConsts.DbSchema);
                a.ConfigureByConvention();
            });

            builder.Entity<PurPAReturnReasonDetail>(a =>
            {
                a.ToTable(SpeakerPortalConsts.DbTablePrefix + "PurPAReturnReasonDetails", SpeakerPortalConsts.DbSchema);
                a.ConfigureByConvention();
            });

            #endregion

            #region 豁免

            builder.Entity<PurBWApplication>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "PurBWApplications", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
                b.HasIndex(c => new
                {
                    c.IsDeleted,
                    c.CreationTime
                });

                //用于任务中心，我发起的代办数量统计
                b.HasIndex(a1 => new { a1.ExemptType, a1.Status }).IncludeProperties(a1 => new { a1.TransfereeId, a1.ApplyUserId, a1.IsDeleted });
            });

            #endregion

            #region 第三方讲者

            builder.Entity<PurThirdPartySpeakerItem>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "PurThirdPartySpeakerItems", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
            });

            builder.Entity<PurThirdPartySpeakerExNum>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "PurThirdPartySpeakerExNums", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
            });

            builder.Entity<PurThirdPartySpeakerItemHistory>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "PurThirdPartySpeakerItemHistorys", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
            });

            #endregion

            #endregion

            #region Common

            builder.Entity<Attachment>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "Attachments", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
            });

            builder.Entity<QRCodeRecord>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "QRCodeRecords", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
            });

            builder.Entity<ConsentSigned>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "ConsentSigneds", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
            });

            builder.Entity<SequenceNum>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "SequenceNums", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                b.HasIndex(a => a.Category).IsUnique();
            });

            builder.Entity<OperationLog>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "OperationLogs", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
            });

            builder.Entity<ScheduleJobLog>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "ScheduleJobLogs", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
            });

            builder.Entity<WfApprovalTask>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "WfApprovalTasks", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                b.HasIndex(c => new
                {
                    c.FormId,
                    c.FormNo,
                    c.WorkflowInstanceId,
                    c.StepNo,
                    c.BusinessType
                });
            });
            #endregion

            builder.Entity<PurSlideConfig>(a =>
            {
                a.ToTable(SpeakerPortalConsts.DbTablePrefix + "PurSlideConfigs", SpeakerPortalConsts.DbSchema);
                a.ConfigureByConvention();
            });

            builder.Entity<MessageInfo>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "MessageInfos", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                b.HasIndex(c => new
                {
                    c.OwnerId,
                });
            });


            builder.Entity<WorkflowInstance>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "WorkflowInstances", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                b.HasIndex(c => new
                {
                    c.FormId
                });
            });


            builder.Entity<WorkflowTask>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "WorkflowTasks", SpeakerPortalConsts.DbSchema);

                b.HasIndex(c => new
                {
                    c.FormId,
                    c.InstanceId,
                    c.ApprovalId,
                    c.ApprovalTime
                })
                .IncludeProperties(a => new
                {
                    a.CreationTime,
                    a.StepNo
                });
                b.HasIndex(x => x.ApprovalId)
                .IncludeProperties(e => new
                {
                    e.FormId,
                    e.IsDeleted,
                    e.ApprovalTime,
                    e.StepNo
                });
                b.ConfigureByConvention();
            });

            #region Integraion

            builder.Entity<InteDspotWholeTask>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "InteDspotWholeTasks", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                b.HasIndex(c => new
                {
                    c.IntegrationDate
                });
            });

            builder.Entity<InteDspotWholeTaskSub>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "InteDspotWholeTaskSubs", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                b.HasIndex(c => new
                {
                    c.WholeTaskId
                });
            });

            #region BPCS

            builder.Entity<BpcsAbk>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "BpcsAbk", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                b.HasKey(c => new
                {
                    c.Bkid,
                    c.Bform1
                });
            });

            builder.Entity<BpcsAvt>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "BpcsAvt", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                b.HasIndex(c => new
                {
                    c.Vcmpy,
                    c.Vterm
                }).IncludeProperties(a => new { a.Vtmid })
                .IsUnique();
            });

            builder.Entity<BpcsGcc>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "BpcsGcc", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                //b.HasNoKey();

                b.HasIndex(c => new
                {
                    c.Ccfrcr
                });
            });

            builder.Entity<BpcsZcc>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "BpcsZcc", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                //b.HasNoKey();

                b.HasIndex(c => new
                {
                    c.Cccode
                });
            });

            builder.Entity<BpcsZpa>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "BpcsZpa", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                //b.HasNoKey();
            });

            builder.Entity<BpcsZrc>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "BpcsZrc", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                //b.HasNoKey();
            });

            builder.Entity<BpcsAvm>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "BpcsAvm", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                //b.HasNoKey();

                b.HasIndex(c => new
                {
                    c.Vtype,
                    c.FinaId,
                    c.Vendor,
                    c.Vndnam,
                    c.Vcmpny,
                    c.Vterms
                }).IncludeProperties(a => new { a.Vnstat, a.Vcurr });
            });

            builder.Entity<BpcsPmfvm>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "BpcsPmfvm", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                //b.HasNoKey();

                b.HasIndex(c => new
                {
                    c.Vnderx,
                    c.Vmcmpy,
                    c.Vextnm
                });
            });

            builder.Entity<BpcsAph>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "BpcsAph", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                b.HasIndex(c => new
                {
                    c.Apinv
                });
            });

            builder.Entity<BpcsAml>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "BpcsAml", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                b.HasIndex(c => new
                {
                    c.Amlcmp,
                    c.Amlvnd,
                    c.Amlref,
                    c.Amlsub,
                    c.Amllin,
                    c.Amlinv
                });
            });

            builder.Entity<BpcsGlh>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "BpcsGlh", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                b.HasIndex(c => new
                {
                    c.Lhldgr,
                    c.Lhbook,
                    c.Lhyear,
                    c.Lhperd,
                    c.Lhjnen,
                    c.Lhjnln
                });
                b.HasIndex(c => new { c.Lhian, c.UpdateTime })
                 .IncludeProperties(c => new
                 {
                     c.Lhjnen,
                     c.Lhjnln,
                     c.Lhdram,
                     c.Lhcram,
                     c.Lhldes,
                     c.Lhjrf1,
                     c.Lhdref,
                     c.Lhreas,
                     c.Lhdate,
                     c.Lhtime
                 });
            });

            builder.Entity<BpcsGcr>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "BpcsGcr", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                b.HasIndex(c => new
                {
                    c.Crid,
                    c.Crcoa,
                    c.Crian
                }).IsUnique();
            });

            builder.Entity<EdiLogVendorMaster>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "EdiLogVendorMasters", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                b.HasIndex(c => new
                {
                    c.Vcmpny,
                    c.Vendor
                });
            });

            builder.Entity<EdiLogApInvioce>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "EdiLogApInvioces", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                b.HasIndex(c => new
                {
                    c.Lpvndr
                });
            });

            builder.Entity<EdiLogUncompletedPRVendor>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "EdiLogUncompletedPRVendors", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
                b.HasNoKey();
                b.HasIndex(c => new
                {
                    c.Vendor
                });
            });

            builder.Entity<BpcsSyncLog>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "BpcsSyncLogs", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
            });

            #endregion
            builder.Entity<InteDspotWholeTask>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "InteDspotWholeTasks", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                b.HasIndex(c => new
                {
                    c.IntegrationDate
                });
            });
            #region OM
            builder.Entity<InteOnlineMeetingSettlement>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "InteOnlineMeetingSettlement", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                b.HasIndex(c => new
                {
                    c.SerialNumber
                });
            });
            #endregion


            #region Veeva
            builder.Entity<VeevaDCRLog>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "VeevaDCRLogs", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
            });

            builder.Entity<VeevaResultHistory>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "VeevaResultHistorys", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
            });
            #endregion

            #endregion

            #region Budget
            builder.Entity<BdMasterBudget>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "BdMasterBudgets", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
                b.HasIndex(c => new
                {
                    c.Code
                });
                b.HasMany(e => e.SubBudgets)
                .WithOne(e => e.MasterBudget)
                .HasForeignKey(e => e.MasterBudgetId);

            });
            builder.Entity<BdSubBudget>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "BdSubBudgets", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
                b.HasIndex(c => new
                {
                    c.Code
                });
                b.HasMany(a => a.MonthlyBudgets)
                .WithOne(e => e.SubBudget)
                .HasForeignKey(e => e.SubBudgetId);

                //用于子预算导出接口ExportSubbudgetList
                b.HasIndex(a => new { a.BuId }).IncludeProperties(a => new { a.Code, a.MasterBudgetId, a.CostCenterId, a.RegionId, a.OwnerId, a.UesdAmount, a.AttachmentFile, a.CreationTime, a.IsDeleted, a.Description, a.Status, a.IsComplicanceAudits, a.Bu2, a.LMMs, a.Owner2, a.ProductManagers, a.RegionManagers, a.BudgetAmount });
            });
            builder.Entity<BdMonthlyBudget>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "BdMonthlyBudgets", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
                b.HasIndex(c => new
                {
                    c.Month
                });
            });
            builder.Entity<BdHistory>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "BdHistorys", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
                b.HasIndex(c => new
                {
                    c.BudgetId
                });
            });
            builder.Entity<BdBudgetUse>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "BdBudgetUses", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
            });
            builder.Entity<BdBudgetReturn>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "BdBudgetReturns", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
            });
            builder.Entity<BdAuthorizedBudgetBu>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "BdAuthorizedBudgetBus", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
                b.HasIndex(c => new
                {
                    c.UserId
                });
            });
            builder.Entity<BdSubBudgetMonthChangeHistory>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "BdSubBudgetMonthChangeHistorys", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
                b.HasIndex(c => new
                {
                    c.Month
                });
                b.HasOne(c => c.History).WithMany(m => m.SubBudgetMonthChangeHistorys).HasForeignKey(e => e.HistoryId);
            });

            builder.Entity<BdSubBudgetMapping>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "BdSubBudgetMappings", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
                b.HasIndex(c => new
                {
                    c.UserId,
                    c.Code
                });
            });

            #endregion

            #region Finance Cashier
            builder.Entity<FinanceCashierPaymentInfo>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "FinanceCashierPaymentInfos", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                b.HasIndex(c => new
                {
                    c.PAApplicationCode
                });
            });

            builder.Entity<PurPAApplicationRef>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "PurPAApplicationRefs", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
                //b.HasNoKey();

                b.HasIndex(c => new
                {
                    c.PAApplicationCode,
                    c.RefNo
                });
            });
            #endregion

            #region Agent
            builder.Entity<AgentConfig>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "AgentConfig", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
                b.HasIndex(c => new
                {
                    c.AgentOperator
                });
                b.HasMany(e => e.AgentHistories)
                .WithOne(e => e.AgentConfig)
                .HasForeignKey(e => e.AgentConfigId);
            });
            builder.Entity<AgentHistory>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "AgentHistory", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
                b.HasIndex(c => new
                {
                    c.FormCode,
                    c.FormId
                });
            });
            #endregion

            #region 系统配置
            builder.Entity<SlideConfig>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "SlideConfigs", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
                b.HasIndex(c => new
                {
                    c.SlideCode,
                });
            });

            builder.Entity<SlideOperateHistory>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "SlideOperateHistorys", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
                b.HasIndex(c => new
                {
                    c.SlideId,
                });
            });
            builder.Entity<FinanceReviewConfig>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "FinanceReviewConfigs", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
                b.HasIndex(c => new
                {
                    c.FirstReviewUserId,
                });
            });
            builder.Entity<FinanceReviewConfigHistory>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "FinanceReviewConfigHistorys", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
                b.HasIndex(c => new
                {
                    c.UserId,
                });
            });
            #endregion

            #region 邮件记录 
            builder.Entity<SendEmailRecord>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "SendEmailRecords", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
                b.HasIndex(c => new
                {
                    c.Status,
                    c.EmailAddress
                });
            });
            #endregion

            #region Cross Bu

            builder.Entity<Activity>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "Activity", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
                b.HasIndex(c => new
                {
                    c.Type,
                    c.City
                });
            });

            builder.Entity<ActivityDepartment>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "ActivityDepartment", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
            });

            builder.Entity<ActivityHospital>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "ActivityHospital", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
            });

            builder.Entity<ActivityUserInformation>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "ActivityUserInformation", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
            });

            builder.Entity<ActivityBu>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "ActivityBu", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
            });


            builder.Entity<SuggestionFeedback>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "SuggestionFeedback", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
            });

            builder.Entity<CustomerRelation>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "CustomerRelation", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
                b.HasIndex(c => c.PinYinSort);
            });

            builder.Entity<JoinToMarketActivity>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "JoinToMarketActivity", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
            });

            builder.Entity<CustomerRelationHistory>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "CustomerRelationHistorys", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
            });

            builder.Entity<ContactShareRecords>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "ContactShareRecords", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
                b.HasIndex(c => c.Hospital);
            });
            builder.Entity<AccessLog>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "AccessLogs", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
                b.HasIndex(c => c.Type);
            });
            #endregion

            #region  报表
            ///专业服务报税
            builder.Entity<ProfessionalServiceTaxReport>(a =>
            {
                a.ToTable(SpeakerPortalConsts.DbTablePrefix + "ProfessionalServiceTaxReports", SpeakerPortalConsts.DbSchema);
                a.ConfigureByConvention();
                a.HasIndex(c => new
                {
                    c.ApplicationCode
                });
            });
            //审批记录报表
            builder.Entity<ApprovalRecordReport>(a =>
            {
                a.ToTable(SpeakerPortalConsts.DbTablePrefix + "ApprovalRecordReports", SpeakerPortalConsts.DbSchema);
                a.ConfigureByConvention();
                a.HasIndex(c => new
                {
                    c.ApplicationId
                });
            });

            //全流程相关表
            builder.Entity<PAJoinBpcsGlh>(a =>
            {
                a.ToTable(SpeakerPortalConsts.DbTablePrefix + "PAJoinBpcsGlhs", SpeakerPortalConsts.DbSchema);
                a.ConfigureByConvention();
            });
            #endregion

            #region EFlow-STicket

            builder.Entity<StoreInfo>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "StoreInfo", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
            });

            builder.Entity<ClientInfo>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "ClientInfo", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
            });

            builder.Entity<ProductDetail>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "ProductDetails", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
            });

            builder.Entity<STicketApplication>(a =>
            {
                a.ToTable(SpeakerPortalConsts.DbTablePrefix + "STicketApplications", SpeakerPortalConsts.DbSchema);
                a.HasIndex(a => new { a.ApplicationCode, a.ApplyUserDeptName, a.Status, a.BudgetCode, a.SubBudgetCode, a.CostCenter, a.ExpenseTypeId, a.ApplyTime, a.ApplyUserDeptId, a.ApplyUserId, a.TransfereeId });
                a.ConfigureByConvention();
            });
            builder.Entity<FocBudgetReturn>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "FocBudgetReturns", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
            });

            builder.Entity<STicketApplicationDetail>(a =>
            {
                a.ToTable(SpeakerPortalConsts.DbTablePrefix + "STicketApplicationDetails", SpeakerPortalConsts.DbSchema);
                a.HasIndex(a => new { a.ParentID, a.StoreId });
                a.ConfigureByConvention();
            });
            builder.Entity<SOIWriteoffResult>(a =>
            {
                a.ToTable(SpeakerPortalConsts.DbTablePrefix + "SOIWriteoffResults", SpeakerPortalConsts.DbSchema);
                a.HasIndex(a => new { a.DetailId });
                a.ConfigureByConvention();
            });

            #endregion

            #region EFlow-FOC
            builder.Entity<FocMasterBudget>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "FocMasterBudgets", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
                b.HasIndex(c => new
                {
                    c.Code
                });
                b.HasMany(e => e.SubBudgets)
                .WithOne(e => e.MasterBudget)
                .HasForeignKey(e => e.MasterBudgetId);

            });

            builder.Entity<FocSubBudget>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "FocSubBudgets", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
                b.HasIndex(c => new
                {
                    c.Code
                });
                b.HasMany(a => a.MonthlyBudgets)
                .WithOne(e => e.SubBudget)
                .HasForeignKey(e => e.SubBudgetId);

            });

            builder.Entity<FocMonthlyBudget>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "FocMonthlyBudgets", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
                b.HasIndex(c => new
                {
                    c.Month
                });
            });

            builder.Entity<FocBudgetHistory>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "FocBudgetHistorys", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
                b.HasIndex(c => new
                {
                    c.BudgetId
                });
            });

            builder.Entity<FocSubBudgetMonthChangeHistory>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "FocSubBudgetMonthChangeHistorys", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
                b.HasIndex(c => new
                {
                    c.Month
                });
                b.HasOne(c => c.History).WithMany(m => m.SubBudgetMonthChangeHistorys).HasForeignKey(e => e.HistoryId);
            });

            builder.Entity<FocBudgetUse>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "FocBudgetUses", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
            });
            builder.Entity<FocBudgetReturn>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "FocBudgetReturns", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
            });

            //全流程报表的非活动数据(中间表)
            builder.Entity<RPT_WholeProcessReport>(a =>
            {
                a.ToTable(SpeakerPortalConsts.DbTablePrefix + "RPT_WholeProcessReports", SpeakerPortalConsts.DbSchema);
                a.ConfigureByConvention();
                a.HasNoKey();
            });

            builder.Entity<FOCApplication>(a =>
            {
                a.ToTable(SpeakerPortalConsts.DbTablePrefix + "FOCApplications", SpeakerPortalConsts.DbSchema);
                a.HasIndex(a => new { a.ApplicationCode, a.ApplyUserDeptName, a.CostCenterID, a.BudgetCode, a.ProductMCode, a.Status, a.ApplyTime });
                a.ConfigureByConvention();
            });

            builder.Entity<FOCApplicationDetail>(a =>
            {
                a.ToTable(SpeakerPortalConsts.DbTablePrefix + "FOCApplicationDetails", SpeakerPortalConsts.DbSchema);
                a.HasIndex(a => new { a.ParentID });
                a.ConfigureByConvention();
            });

            builder.Entity<FOCApplicationProductDetail>(a =>
            {
                a.ToTable(SpeakerPortalConsts.DbTablePrefix + "FOCApplicationProductDetails", SpeakerPortalConsts.DbSchema);
                a.HasIndex(a => new { a.FOCDetailId });
                a.ConfigureByConvention();
            });

            builder.Entity<FOCApplicationLogisticsDetail>(a =>
            {
                a.ToTable(SpeakerPortalConsts.DbTablePrefix + "FOCApplicationLogisticsDetails", SpeakerPortalConsts.DbSchema);
                a.HasIndex(a => new { a.FOCDetailId });
                a.ConfigureByConvention();
            });

            builder.Entity<PPRegion>(a =>
            {
                a.ToTable(SpeakerPortalConsts.DbTablePrefix + "PPRegions", SpeakerPortalConsts.DbSchema);
                a.HasIndex(a => new { a.RegionId });
                a.HasNoKey();
                a.ConfigureByConvention();
            });

            builder.Entity<PPCostCenter>(a =>
            {
                a.ToTable(SpeakerPortalConsts.DbTablePrefix + "PPCostCenters", SpeakerPortalConsts.DbSchema);
                a.HasIndex(a => new { a.CostCenterId });
                a.HasNoKey();
                a.ConfigureByConvention();
            });


            //全流程报表的活动数据视图
            //builder.Entity<UVW_WPR_Active>().HasNoKey().ToView("UVW_WPR_Active");
            builder.Entity<UVW_WPR_Active>(a =>
            {
                //a.HasBaseType<RPT_WholeProcessReport>();
                a.ToView(SpeakerPortalConsts.DbTablePrefix + "UVW_WPR_Active", SpeakerPortalConsts.DbSchema);
                a.HasNoKey();
            });
            #endregion

            builder.Entity<BpmTpmInterfaceLog>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "BpmTpmInterfaceLogs", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                b.HasIndex(c => new
                {
                    c.BpmId,
                    c.Method,
                    c.IsSuccess,
                    c.State,
                });
            });

            builder.Entity<InitializeApprovalRecord>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "InitializeApprovalRecords", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                b.HasIndex(c => new
                {
                    c.BusinessId,
                    c.Status,
                    c.Count,
                    c.MaxRetries,
                });
            });

            builder.Entity<PostApprovalAction>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "PostApprovalActions", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                b.HasIndex(c => new
                {
                    c.ProcessingType,
                    c.Status,
                    c.Count,
                    c.MaxRetries,
                });
            });

            #region Return And Exchange
            builder.Entity<ReturnApplication>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "ReturnApplications", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
                b.HasIndex(c =>
                    new { c.ApplicationCode, c.ApplyUserDeptName, c.Status, c.ApplyTime }
                );
            });

            builder.Entity<ReturnApplicationDetail>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "ReturnApplicationDetails", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
                b.HasIndex(c =>
                    new { c.ReturnApplicationId }
                );
            });
            //builder.Entity<ExchangeApplication>(b =>
            //{
            //    b.ToTable(SpeakerPortalConsts.DbTablePrefix + "ExchangeApplications", SpeakerPortalConsts.DbSchema);
            //    b.ConfigureByConvention();
            //    b.HasIndex(c =>
            //        new { c.ApplicationCode, c.ApplyUserDeptName, c.Status, c.ApplyTime }
            //    );
            //});
            //builder.Entity<ExchangeApplicationDetail>(b =>
            //{
            //    b.ToTable(SpeakerPortalConsts.DbTablePrefix + "ExchangeApplicationDetails", SpeakerPortalConsts.DbSchema);
            //    b.ConfigureByConvention();
            //    b.HasIndex(c =>
            //        new { c.ExchangenApplicationId }
            //    );
            //});
            #endregion

            #region MSA
            builder.Entity<MsaMasterServiceAgreement>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "MsaMasterServiceAgreements", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                b.HasIndex(c => new
                {
                    c.Code,
                    c.VendorName,
                    c.Remark,
                    c.EffectiveDate,
                    c.ExpiryDate,
                    c.IsEnabled
                });

                b.HasMany(c => c.Sows)
                .WithOne(c => c.Msa)
                .HasForeignKey(c => c.MsaId);

                b.HasMany(c => c.BuMappings)
               .WithOne(c => c.Msa)
               .HasForeignKey(c => c.MsaId);

                b.HasMany(c => c.ServiceTypeMappings)
               .WithOne(c => c.Msa)
               .HasForeignKey(c => c.MsaId);
            });

            builder.Entity<MsaStatementOfWork>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "MsaStatementOfWorks", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                //b.HasIndex(c => new
                //{
                //    c.Code,
                //    c.MsaId,
                //    c.VendorName,
                //    c.CompanyId,
                //    c.BuId,
                //    c.CategoryId,
                //});
            });

            builder.Entity<MsaBuMapping>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "MsaBuMappings", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
                b.HasIndex(c => new
                {
                    c.MsaId,
                    c.BuId
                });
            });

            builder.Entity<MsaCompanyMapping>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "MsaCompanyMappings", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
                b.HasIndex(c => new
                {
                    c.MsaId,
                    c.CompanyId
                });
            });

            builder.Entity<MsaServiceTypeMapping>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "MsaServiceTypeMappings", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
                b.HasIndex(c => new
                {
                    c.MsaId,
                    c.ServiceTypeId
                });
            });

            builder.Entity<MsaHistory>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "MsaHistorys", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                b.HasIndex(c => new
                {
                    c.SourceId,
                    c.CreationTime
                }).IsDescending(false, true);
            });
            #endregion

            #region Concur


            builder.Entity<MealEmployee>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "MealEmployees", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                //b.HasIndex(a => new
                //{
                //    a.Date,
                //    a.UPI,
                //    a.Hierarchy1,
                //    a.Hierarchy2,
                //    a.Department,
                //    a.Region
                //});

            });

            builder.Entity<MealOrgMapping>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "MealOrgMappings", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                //b.HasIndex(a => a.OriginalName);
                //b.HasIndex(a => a.NewName);
            });

            builder.Entity<MealOrgMappingHistory>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "MealOrgMappingHistories", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();
            });

            builder.Entity<MealReport>(b =>
            {
                b.ToTable(SpeakerPortalConsts.DbTablePrefix + "MealReports", SpeakerPortalConsts.DbSchema);
                b.ConfigureByConvention();

                //b.HasIndex(a => new
                //{
                //    a.Date,
                //    a.UPI,
                //    a.Hierarchy1,
                //    a.Hierarchy2,
                //    a.Department,
                //    a.Region
                //});
            });

            #endregion
        }

    }
}
