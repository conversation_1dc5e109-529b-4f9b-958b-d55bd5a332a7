﻿using Abbott.SpeakerPortal.BackgroundWorkers.BPMEmail;
using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.BPMEmail;
using Abbott.SpeakerPortal.Entities.Common.ScheduleJob;
using Abbott.SpeakerPortal.Enums;
using DocumentFormat.OpenXml.Spreadsheet;
using Hangfire;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.AppServices.Common
{
    public class ScheduleJobLogService : SpeakerPortalAppService, IScheduleJobLogService
    {

        private readonly Guid _taskId;
        private readonly IConfiguration _configuration;
        private readonly IServiceProvider serviceProvider;
        public ScheduleJobLogService(IServiceProvider serviceProvider)
        {
            _taskId = Guid.NewGuid();
            _configuration = serviceProvider.GetService<IConfiguration>();
        }
        public ScheduleJobLogDto InitSyncLog(string jobName, string subJobName)
        {

            ScheduleJobLogDto log = new ScheduleJobLogDto();
            log.TaskID = _taskId;
            log.JobName = jobName;
            log.SubJobName = subJobName;
            log.StartTime = DateTime.Now;
            return log;
        }

        public string SyncLog(ScheduleJobLogDto log, bool isSuccess = true, string remark = "")
        {
            log.EndTime = DateTime.Now;
            var dbLog = ObjectMapper.Map<ScheduleJobLogDto, ScheduleJobLog>(log);
            //数据库记录
            var logRepository = LazyServiceProvider.LazyGetService<IScheduleJobLogRepository>();
            try
            {
                logRepository.InsertAsync(dbLog).Wait();
            }
            catch (Exception ex)
            {
                return ex.Message;
            }
            return null;
        }

        /// <summary>
        /// 监控 SyncLog
        /// </summary>
        /// <returns></returns>
        public async Task MonitorSyncLog() 
        {
            DateTime now = DateTime.Now;
            DateTime startTime = now.Date.AddHours(now.Hour - 1); // 前一个小时的数据
            DateTime endTime = startTime.AddHours(1);

            var logRepository = LazyServiceProvider.LazyGetService<IScheduleJobLogRepository>();
            var querySyncLog = await logRepository.GetQueryableAsync();
            var syncLogs = querySyncLog.Where(a=>a.IsSuccess == false && a.EndTime >= startTime && a.EndTime < endTime).ToList();

            if(!syncLogs.Any())
                return;

            string helpdeskEmail = _configuration["SpeakerEmail:BPMHelpdeskEmail"];
            string content ="";
            foreach (var syncLog in syncLogs) {

                content += $"<tr>" +
                                $"<td>{syncLog.Id.ToString()}</td>" +
                                $"<td>{syncLog.JobName}</td>" +
                                $"<td>{syncLog.EndTime.ToString("yyyy-MM-dd HH:mm:ss")}</td>" +
                           $"</tr>";
            }
            var htmlContent = $"如下Job执行失败：<br>" +
                              $"<table border='1'>"+
                                    $"<tr>"+
                                        $"<th>JobId</th>" +
                                        $"<th>JobName</th>" +
                                        $"<th>JobEndTime</th>" +
                                    $"</tr>" +
                                    content +
                               $"</table>";
            var sendEmaillRecord =  new InsertSendEmaillRecordDto
            {
                EmailAddress = helpdeskEmail,
                Subject = "[NexBPM消息中心]ScheduleJobLog 执行失败提醒。",
                Content = htmlContent,
                SourceType = EmailSourceType.MonitorSyncLog,
                Status = SendStatus.Pending,
                Attempts = 0
            };
            //记录邮件，并触发邮件发送功能
            var emailService = LazyServiceProvider.LazyGetService<IEmailService>();
            await emailService.InsertSendEmaillRecordAsync([sendEmaillRecord]);
            // 调度作业
            BackgroundJob.Enqueue<SendEmailWorker>(a => a.DoWorkAsync());
        }
    }
}
