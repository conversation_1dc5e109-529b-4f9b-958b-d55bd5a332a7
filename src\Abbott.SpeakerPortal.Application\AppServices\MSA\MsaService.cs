﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.MSA;
using Abbott.SpeakerPortal.Contracts.Purchase.MSA;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.Common.Attachment;
using Abbott.SpeakerPortal.Entities.MSA;
using Abbott.SpeakerPortal.Entities.MSA.MsaCompanyMapping;
using Abbott.SpeakerPortal.Entities.Purchase.PurCategoryConfig;
using Abbott.SpeakerPortal.Entities.Purchase.PurPOApplication;
using Abbott.SpeakerPortal.Entities.VendorOrgnizations;
using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.Person;
using Abbott.SpeakerPortal.Utils;
using Abbott.SpeakerPortal.Vendor.Speaker;

using Microsoft.EntityFrameworkCore;

using MiniExcelLibs;
using MiniExcelLibs.OpenXml;

using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;

namespace Abbott.SpeakerPortal.AppServices.MSA
{
    public class MsaService : SpeakerPortalAppService, IMsaService
    {
        /// <summary>
        /// 创建Msa
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> CreateMsaAsync(MsaPostDto request)
        {
            var messageResult = await ValidateMsaLegality(request);
            if (!messageResult.Success)
                return messageResult;

            var msaRepos = LazyServiceProvider.LazyGetService<IMsaMasterServiceAgreementRepository>();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();

            var msa = ObjectMapper.Map<MsaPostDto, MsaMasterServiceAgreement>(request);
            msa.IsEnabled = true;

            //公司
            if (request.CompanyIds?.Any() == true)
            {
                var companies = await dataverseService.GetCompanyAsync(stateCode: null);
                companies = companies.Where(a => request.CompanyIds.Contains(a.Id));

                foreach (var item in companies)
                {
                    var company = new MsaCompanyMapping { CompanyId = item.Id, CompanyCode = item.CompanyCode, CompanyName = item.CompanyName };
                    company.SetId(GuidGenerator.Create());
                    msa.Companies.Add(company);
                }
            }

            //BU Mapping
            if (request.BuScope == Enums.ApplicableDepartments.All)
                msa.BuMappings.Add(new MsaBuMapping { BuName = EnumUtil.GetDescription(Enums.ApplicableDepartments.All) });
            else if (request.BuIds?.Any() == true)
            {
                var bus = await dataverseService.GetDivisions(stateCode: null);
                bus = bus.Where(a => request.BuIds.Contains(a.Id));

                foreach (var item in bus)
                {
                    var bu = new MsaBuMapping { BuId = item.Id, BuName = item.DepartmentName };
                    bu.SetId(GuidGenerator.Create());
                    msa.BuMappings.Add(bu);
                }
            }

            //ServiceType Mapping
            if (request.ServiceTypeScope == Enums.ApplicableDepartments.All)
                msa.ServiceTypeMappings.Add(new MsaServiceTypeMapping { ServiceTypeName = EnumUtil.GetDescription(Enums.ApplicableDepartments.All) });
            else if (request.ServiceTypeIds?.Any() == true)
            {
                var categories = await LazyServiceProvider.LazyGetService<IPurCategoryConfigRepository>().GetListAsync(a => a.IsEnabled && a.Hierarchy == 1);
                categories = categories.Where(a => request.ServiceTypeIds.Contains(a.Id)).ToList();

                foreach (var item in categories)
                {
                    var serviceType = new MsaServiceTypeMapping { ServiceTypeId = item.Id, ServiceTypeName = item.NameEn };
                    serviceType.SetId(GuidGenerator.Create());
                    msa.ServiceTypeMappings.Add(serviceType);
                }
            }

            //附件
            msa.AttachmentFileIds = request.AttachmentInformations?.Select(a => a.AttachmentId).JoinAsString(",");

            await InsertAndGenerateSerialNoAsync(msaRepos, msa, "MSA");

            //Change Log
            var msaHistory = new MsaHistory
            {
                SourceId = msa.Id,
                SourceCode = msa.Code,
                Remark = request.Remark,
                OperateType = Enums.MSA.MsaOperateTypes.Create
            };
            await LazyServiceProvider.LazyGetService<IMsaHistoryRepository>().InsertAsync(msaHistory);

            return MessageResult.SuccessResult(msa.Id);
        }

        /// <summary>
        /// 编辑Msa
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> UpdateMsaAsync(MsaUpdatePostDto request)
        {
            if (!request.Id.HasValue)
                return MessageResult.FailureResult("数据标识不能为空");

            var msaRepostiroy = LazyServiceProvider.LazyGetService<IMsaMasterServiceAgreementRepository>();
            var msa = await msaRepostiroy.FindAsync(request.Id.Value);
            if (msa == null)
                return MessageResult.FailureResult("指定的数据不存在");

            //修改时只允许修改这几个值
            msa.EffectiveDate = request.EffectiveDate;
            msa.ExpiryDate = request.ExpiryDate;
            msa.Remark = request.Remark;
            msa.AttachmentFileIds = request.AttachmentInformations?.Select(a => a.AttachmentId).JoinAsString(",");

            await msaRepostiroy.UpdateAsync(msa);

            //Change Log
            var msaHistory = new MsaHistory
            {
                SourceId = msa.Id,
                SourceCode = msa.Code,
                Remark = request.Remark,
                OperateType = Enums.MSA.MsaOperateTypes.Update,
                OperateContent = $"开始日期：{msa.EffectiveDate:yyyy-MM-dd}；结束日期：{msa.ExpiryDate:yyyy-MM-dd}；附件：{request.AttachmentInformations?.Select(a => a.FileName).JoinAsString(",")}；备注：{request.Remark}"
            };
            await LazyServiceProvider.LazyGetService<IMsaHistoryRepository>().InsertAsync(msaHistory);

            return MessageResult.SuccessResult(msa.Id);
        }

        /// <summary>
        /// 验证Msa数据合法性
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<MessageResult> ValidateMsaLegality(MsaPostDto request)
        {
            var messageResult = MessageResult.SuccessResult();

            if (request.BuScope == Enums.ApplicableDepartments.NotAll && request.BuIds?.Any() == false)
                messageResult = MessageResult.FailureResult("请指定关联BU");
            if (request.ServiceTypeScope == Enums.ApplicableDepartments.NotAll && request.ServiceTypeIds?.Any() == false)
                messageResult = MessageResult.FailureResult("请指定关联品类");

            return Task.FromResult(messageResult);
        }

        /// <summary>
        /// 获取Msa列表
        /// </summary>
        /// <param name="request"></param>
        /// <param name="isExport"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<MsaResponseDto>> GetMsaListAsync(MsaRequestDto request)
        {
            IEnumerable<Guid?> companyIds = [];
            //非管理员级别只能查看自己配置公司下的数据
            if (!CurrentUser.IsInRole(RoleNames.BizAdmin))
                companyIds = (await LazyServiceProvider.LazyGetService<IDataverseService>().GetProcurementPushConfigAsync(CurrentUser.Id?.ToString(), null)).Select(s => s.CompanyId).ToArray();

            var msaQuery = await LazyServiceProvider.LazyGetService<IMsaMasterServiceAgreementRepository>().GetQueryableAsync();
            var query = msaQuery
                .Include(x => x.BuMappings)
                .Include(x => x.ServiceTypeMappings)
                .Include(x => x.Companies)
                .WhereIf(!CurrentUser.IsInRole(RoleNames.BizAdmin), x => x.Companies.Any(x1 => companyIds.Contains(x1.CompanyId)))
                .WhereIf(request.OnlyShowEnabled.HasValue, a => a.IsEnabled == request.OnlyShowEnabled.Value)
                .WhereIf(request.CompanyId.HasValue, a => a.Companies.Any(a1 => a1.CompanyId == request.CompanyId))
                .WhereIf(!string.IsNullOrEmpty(request.VendorNameAccuracy), a => a.VendorName == request.VendorNameAccuracy)
                .WhereIf(!string.IsNullOrEmpty(request.MsaCode), a => a.Code.Contains(request.MsaCode))
                .WhereIf(!string.IsNullOrEmpty(request.VendorName), x => x.VendorName.Contains(request.VendorName))
                .WhereIf(!string.IsNullOrEmpty(request.Description), x => x.Remark.Contains(request.Description));

            var count = await query.CountAsync();
            var pageDatas = query.OrderByDescending(x => x.Code)
                .Skip(request.PageIndex * request.PageSize)
                .Take(request.PageSize).ToArray();

            IEnumerable<AttachmentInformation> attachmentInfos = null;
            //有数据时，并且不是导出时获取附件
            if (pageDatas.Length > 0)
            {
                var attachmentfeilds = pageDatas.Where(x => !string.IsNullOrEmpty(x.AttachmentFileIds)).SelectMany(x => x.AttachmentFileIds.Split(","), (a, b) => Guid.Parse(b)).ToArray();
                if (attachmentfeilds.Length > 0)
                {
                    var attachmentQuery = await LazyServiceProvider.LazyGetService<IAttachmentRepository>().GetQueryableAsync();
                    attachmentInfos = attachmentQuery.Where(x => attachmentfeilds.Distinct().ToHashSet().Contains(x.Id))
                    .Select(s => new AttachmentInformation
                    {
                        AttachmentId = s.Id,
                        FileName = s.FileName,
                        FileSize = s.Size.ToFileSizeString(),
                        Suffix = s.Suffix,
                        FilePath = s.FilePath
                    }).ToArray();
                }
            }

            var datas = pageDatas
                .Select(x => new MsaResponseDto
                {
                    Id = x.Id,
                    Code = x.Code,
                    VendorName = x.VendorName,
                    CompanyName = x.Companies.Select(x => x.CompanyName).JoinAsString(","),
                    BuName = x.BuMappings.Select(x => x.BuName).JoinAsString(","),
                    ServiceTypeName = x.ServiceTypeMappings.Select(x => x.ServiceTypeName).JoinAsString(","),
                    Remark = x.Remark,
                    EffectiveDate = x.EffectiveDate?.ToString("yyyy-MM-dd"),
                    ExpiryDate = x.ExpiryDate?.ToString("yyyy-MM-dd"),
                    IsEnabled = x.IsEnabled,
                    AttachmentInformations = string.IsNullOrEmpty(x.AttachmentFileIds) ? [] : attachmentInfos?.Where(a => x.AttachmentFileIds.Split(",").Select(a1 => Guid.Parse(a1)).Any(a1 => a1 == a.AttachmentId))
                }).ToArray();

            return new PagedResultDto<MsaResponseDto>(count, datas);
        }

        /// <summary>
        /// 获取Msa详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<MsaInfoResponseDto> GetMsaDetailAsync(Guid id)
        {
            var msaQuery = await LazyServiceProvider.LazyGetService<IMsaMasterServiceAgreementReadonlyRepository>().GetQueryableAsync();
            var attachmentQuery = await LazyServiceProvider.LazyGetService<IAttachmentReadonlyRepository>().GetQueryableAsync();

            var msa = await msaQuery.Include(x => x.BuMappings).Include(x => x.ServiceTypeMappings).Include(x => x.Companies).FirstOrDefaultAsync(x => x.Id == id);
            if (msa == null)
                return null;

            var response = ObjectMapper.Map<MsaMasterServiceAgreement, MsaInfoResponseDto>(msa);
            //Company
            if (msa.Companies.Any())
            {
                response.Companies = msa.Companies.Select(a => new SubObj { Id = a.CompanyId, Name = a.CompanyName, Code = a.CompanyCode });
                response.CompanyNames = response.Companies.Select(a => a.Name).JoinAsString(",");
            }

            //BU
            if (msa.BuMappings.Count == 1 && msa.BuMappings.First().BuId == Guid.Empty)
            {
                response.BuScope = Enums.ApplicableDepartments.All;
                response.Bus = [new SubObj { Id = Guid.Empty, Name = Enums.ApplicableDepartments.All.GetDescription() }];
                response.BuNames = Enums.ApplicableDepartments.All.GetDescription();
            }
            else
            {
                response.BuScope = Enums.ApplicableDepartments.NotAll;
                response.Bus = msa.BuMappings.Select(x => new SubObj { Id = x.BuId, Name = x.BuName });
                response.BuNames = response.Bus.Select(a => a.Name).JoinAsString(",");
            }

            //ServiceType
            if (msa.ServiceTypeMappings.Count == 1 && msa.ServiceTypeMappings.First().ServiceTypeId == Guid.Empty)
            {
                response.ServiceTypeScope = Enums.ApplicableDepartments.All;
                response.ServiceTypes = [new SubObj { Id = Guid.Empty, Name = Enums.ApplicableDepartments.All.GetDescription() }];
                response.ServiceTypeNames = Enums.ApplicableDepartments.All.GetDescription();
            }
            else
            {
                response.ServiceTypeScope = Enums.ApplicableDepartments.NotAll;
                response.ServiceTypes = msa.ServiceTypeMappings.Select(x => new SubObj { Id = x.ServiceTypeId, Name = x.ServiceTypeName });
                response.ServiceTypeNames = response.ServiceTypes.Select(a => a.Name).JoinAsString(",");
            }

            //附件
            if (!string.IsNullOrEmpty(msa.AttachmentFileIds))
            {
                var attachmentIds = msa.AttachmentFileIds.Split(",").Select(Guid.Parse).ToArray();
                response.AttachmentInformations = attachmentQuery.Where(x => attachmentIds.Contains(x.Id))
                .Select(s => new AttachmentInformation
                {
                    AttachmentId = s.Id,
                    FileName = s.FileName,
                    FileSize = s.Size.ToFileSizeString(),
                    Suffix = s.Suffix,
                    FilePath = s.FilePath
                })
                .ToArray();
            }

            return response;
        }

        /// <summary>
        /// 删除Msa
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task<MessageResult> DeleteMsaAsync(Guid id)
        {
            var msaRepos = LazyServiceProvider.LazyGetService<IMsaMasterServiceAgreementRepository>();
            var msa = await msaRepos.FindAsync(id);
            if (msa == null)
                return MessageResult.FailureResult("指定的数据不存在");

            var queryPo = await LazyServiceProvider.LazyGetService<IPurPOApplicationReadonlyRepository>().GetQueryableAsync();
            //判断是否有被Po使用
            var hsaRelatedPo = await queryPo.AnyAsync(a => a.MsaId == id && a.Status != Enums.Purchase.PurOrderStatus.Invalid && a.Status != Enums.Purchase.PurOrderStatus.Rejected);
            if (hsaRelatedPo)
                return MessageResult.FailureResult($"该MSA已关联PO，无法删除");

            await msaRepos.DeleteAsync(msa);
            //Change Log
            await LazyServiceProvider.LazyGetService<IMsaHistoryRepository>().InsertAsync(new MsaHistory
            {
                SourceId = msa.Id,
                SourceCode = msa.Code,
                OperateType = Enums.MSA.MsaOperateTypes.Delete
            });

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 启用/禁用Msa
        /// </summary>
        /// <param name="id"></param>
        /// <param name="enable"></param>
        /// <returns></returns>
        public async Task<MessageResult> EnableDisableMsaAsync(EnableDisableRequestDto request)
        {
            var msaRepos = LazyServiceProvider.LazyGetService<IMsaMasterServiceAgreementRepository>();

            var msa = await msaRepos.FindAsync(request.Id);
            if (msa == null)
                return MessageResult.FailureResult("指定的数据不存在");

            msa.IsEnabled = request.Enable;

            await msaRepos.UpdateAsync(msa);
            //Change Log
            await LazyServiceProvider.LazyGetService<IMsaHistoryRepository>().InsertAsync(new MsaHistory
            {
                SourceId = msa.Id,
                SourceCode = msa.Code,
                OperateType = request.Enable ? Enums.MSA.MsaOperateTypes.Enable : Enums.MSA.MsaOperateTypes.Disable
            });

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 获取Msa操作历史
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<IEnumerable<MsaHistoryResponseDto>> GetMsaHistoryAsync(Guid msaId)
        {
            var msaHisQuery = await LazyServiceProvider.LazyGetService<IMsaHistoryRepository>().GetQueryableAsync();
            var userQuery = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();

            var query = msaHisQuery.Where(x => x.SourceId == msaId)
                .GroupJoin(userQuery, a => a.CreatorId, b => b.Id, (a, b) => new { history = a, users = b })
                .SelectMany(a => a.users.DefaultIfEmpty(), (a, b) => new { a.history, userName = b.Name });

            var count = await query.CountAsync();
            var datas = query.OrderByDescending(x => x.history.CreationTime)
                .Select(x => new MsaHistoryResponseDto
                {
                    OperatorName = x.userName,
                    OperateDate = x.history.CreationTime.ToString("yyyy-MM-dd"),
                    OperateType = x.history.OperateType,
                    Content = x.history.OperateContent
                }).ToArray();
            return datas;
        }

        /// <summary>
        /// 按关键字搜索非HCI机构名称
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        public async Task<IEnumerable<string>> GetNonHCIVendorNamesAsync(string keyword)
        {
            if (string.IsNullOrEmpty(keyword))
                return [];

            var queryVendor = await LazyServiceProvider.LazyGetService<IVendorReadonlyRepository>().GetQueryableAsync();
            var queryVendorOrg = await LazyServiceProvider.LazyGetService<IVendorOrgnizationReadonlyRepository>().GetQueryableAsync();

            var datas = await queryVendor
                .Where(a => a.VendorType == Enums.VendorTypes.NonHCIInstitutionalAP)
                .Join(queryVendorOrg, a => a.Id, a => a.VendorId, (a, b) => b.VendorName)
                .Where(a => a.Contains(keyword))
                .Distinct()
                .Take(10)
                .ToArrayAsync();

            return datas;
        }
    }
}
