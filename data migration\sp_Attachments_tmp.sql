CREATE PROCEDURE dbo.sp_Attachments
AS 
BEGIN
	select 
File_Id as BPMId,--(仅用作迁移，不应该在正式表中保留),--附件ID
newid() as Id,--生成的唯一GUID
null as FunctionModule,--根据附件ID，在单据xml文件中查询到对应附件后，再根据单据类型设置对应数字，规则见底部
File_Name as FileName,--文件名
File_Size as Size,--文件大小，转换为B后填入
File_Suffixal as Suffix,--文件后缀
File_FullName as FilePath,--根据BPM原有地址找到文件后转移至NexBPM目录下，根据新的目录地址填入
'{}' as ExtraProperties,--默认填写为"{}"
null as ConcurrencyStamp,--?
CreateTime as CreationTime,--创建时间
Emp_ID as CreatorId,--导入人(以此处ID作为员工主数据中的BPM编码，匹配出员工主数据ID后填入)
null as LastModificationTime,--留空
null as LastModifierId,--留空
'0' as IsDeleted,--默认填写为0
null as DeleterId,--留空
null as DeletionTime--留空
into #Attachments_tmp
from PLATFORM_ABBOTT_Stg.dbo.ODS_T_METAFILE otm 

    --删除表
    IF OBJECT_ID(N'PLATFORM_ABBOTT_Stg.dbo.Attachments_tmp ', N'U') IS NOT NULL
	BEGIN
		update a 
		set a.BPMId                   = b.BPMId
--            ,a.FunctionModule         = b.FunctionModule
            ,a.FileName               = b.FileName
            ,a.Size                   = b.Size
            ,a.Suffix                 = b.Suffix
            ,a.FilePath               = b.FilePath
--            ,a.ExtraProperties        = b.ExtraProperties
--            ,a.ConcurrencyStamp       = b.ConcurrencyStamp
            ,a.CreationTime           = b.CreationTime
            ,a.CreatorId              = b.CreatorId
--            ,a.LastModificationTime   = b.LastModificationTime
--            ,a.LastModifierId         = b.LastModifierId
--            ,a.IsDeleted              = b.IsDeleted
--            ,a.DeleterId              = b.DeleterId
--            ,a.DeletionTime           = b.DeletionTime
        from PLATFORM_ABBOTT_Stg.dbo.Attachments_tmp a 
        left join #Attachments_tmp b on a.BPMId = b.BPMId
        
        insert into PLATFORM_ABBOTT_Stg.dbo.Attachments_tmp 
        select a.BPMId
               ,a.Id
               ,a.FunctionModule
               ,a.FileName
               ,a.Size
               ,a.Suffix
               ,a.FilePath
               ,a.ExtraProperties
               ,a.ConcurrencyStamp
               ,a.CreationTime
               ,a.CreatorId
               ,a.LastModificationTime
               ,a.LastModifierId
               ,a.IsDeleted
               ,a.DeleterId
               ,a.DeletionTime
        from #Attachments_tmp a 
        where not exists (select * from PLATFORM_ABBOTT_Stg.dbo.Attachments_tmp where a.BPMId = BPMId)
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into PLATFORM_ABBOTT_Stg.dbo.Attachments_tmp from #Attachments_tmp
    -- select * from #vendor_tbl
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	
END;
