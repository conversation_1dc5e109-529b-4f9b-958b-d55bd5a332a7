﻿

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Abbott.SpeakerPortal.AppServices.Budget;
using Abbott.SpeakerPortal.AppServices.STicket;
using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Budget.FocMasterBudget;
using Abbott.SpeakerPortal.Contracts.Budget.Subbudget;
using Abbott.SpeakerPortal.Contracts.Dataverse;
using Abbott.SpeakerPortal.Contracts.FOC;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Contracts.STicket;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Permissions;
using ClosedXML.Excel;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Volo.Abp.Application.Dtos;
using static Abbott.SpeakerPortal.Enums.DataverseEnums;

namespace Abbott.SpeakerPortal.Controllers.EFlow
{
    /// <summary>
    /// FOC
    /// </summary>
    [ApiExplorerSettings(GroupName = SwaggerGrouping.E_FLOW)]
    public class FocController : SpeakerPortalController
    {
        IFocService _focService;

        public FocController(IFocService focService)
        {
            _focService = focService;
        }

        /// <summary>
        /// 分页获取MDM产品列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<PagedResultDto<GetProductListResponseDto>>))]
        public async Task<IActionResult> GetMDMProductListAsync([FromQuery] GetProductListRequestDto request)
        {
            var data = await _focService.GetMDMProductListAsync(request);
            return Ok(MessageResult.SuccessResult(data));
        }

        /// <summary>
        /// 分页获取MDM产品MCode列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<PagedResultDto<GetProductMCodeResponseDto>>))]
        public async Task<IActionResult> GetProductMCodeListAsync([FromQuery] GetProductListRequestDto request)
        {
            var data = await _focService.GetProductMCodeListAsync(request);
            return Ok(MessageResult.SuccessResult(data));
        }

        /// <summary>
        /// 根据组织Id获取关联的FOC成本中心
        /// </summary>
        /// <param name="orgId"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<IEnumerable<KeyValuePair<Guid, string>>>))]
        public async Task<IActionResult> GetFocCostcenterByOrgAsync(Guid? orgId)
        {
            var result = await _focService.GetFocCostcenterByOrgAsync(orgId);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 获取FOC申请单详情
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<GetFocApplicationResponseDto>))]
        public async Task<IActionResult> GetFocApplicationDetailsAsync(Guid id)
        {
            var data = await _focService.GetFocApplicationDetailsAsync(id);
            return Ok(MessageResult.SuccessResult(data));
        }

        /// <summary>
        /// 获取FOC申请单列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<PagedResultDto<GetFocApplicationListResponseDto>>))]
        public async Task<IActionResult> GetFocApplicationListAsync(GetFocApplicationListRequestDto request)
        {
            var data = await _focService.GetFocApplicationListAsync(request);
            return Ok(MessageResult.SuccessResult(data));
        }

        /// <summary>
        /// 获取FOC申请单草稿列表
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult<PagedResultDto<GetFocApplicationListResponseDto>>))]
        public async Task<IActionResult> GetFocApplicationDraftList([FromBody] GetFocApplicationDraftListRequestDto request)
        {
            var data = await _focService.GetFocApplicationDraftListAsync(request);
            return Ok(MessageResult.SuccessResult(data));
        }

        /// <summary>
        /// 创建FOC申请单
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        public async Task<IActionResult> CreateFocApplicationAsync([FromBody] CreateFocApplicationRequest request)
        {
            var result = await _focService.CreateFocApplicationAsync(request);
            return Ok(result);
        }

        /// <summary>
        /// 修改FOC申请单
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        public async Task<IActionResult> UpdateFocApplicationAsync([FromBody] UpdateFocApplicationRequest request)
        {
            var result = await _focService.UpdateFocApplicationAsync(request);
            return Ok(result);
        }

        /// <summary>
        /// 保存草稿FOC申请单
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        public async Task<IActionResult> SaveDraftFocApplicationAsync([FromBody] CreateFocApplicationRequest request)
        {
            var result = await _focService.SaveDraftFocApplicationAsync(request);
            return Ok(result);
        }

        /// <summary>
        /// 修改草稿FOC申请单
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        public async Task<IActionResult> UpdateDraftFocApplicationAsync([FromBody] UpdateFocApplicationRequest request)
        {
            var result = await _focService.UpdateDraftFocApplicationAsync(request);
            return Ok(result);
        }

        /// <summary>
        /// 删除FOC申请单
        /// </summary>
        /// <param name="Id">The request.</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        public async Task<IActionResult> DeleteFocApplicationAsync([FromQuery] Guid Id)
        {
            var result = await _focService.DeleteFocApplicationAsync(Id);
            return Ok(result);
        }

        /// <summary>
        /// 提交FOC申请单
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        //[Authorize(SpeakerPortalPermissions.STicket.ApplicationCRUD)]
        public async Task<IActionResult> SubmitFocApplicationAsync([FromBody] UpdateFocApplicationRequest request)
        {
            var result = await _focService.SubmitFocApplicationAsync(request);
            return Ok(result);
        }

        /// <summary>
        /// 检查FOC申请单金额是否大于限定值
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        public async Task<IActionResult> CheckFocApplicationAmountAsync([FromBody] UpdateFocApplicationRequest request)
        {
            var result = await _focService.CheckFocApplicationAmountAsync(request);
            return Ok(result);
        }

        /// <summary>
        /// FOC申请添加产品
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        public async Task<IActionResult> CreateFocApplicationProductDetailAsync([FromBody] CreateFocApplicationProductDetailRequest request)
        {
            var result = await _focService.CreateFocApplicationProductDetailAsync(request);
            return Ok(result);
        }

        /// <summary>
        /// 获取发货类型
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<List<GetShippingTypeResponseDto>>))]
        public async Task<IActionResult> GetShippingTypeListAsync()
        {
            var data = await _focService.GetShippingTypeListAsync();
            return Ok(MessageResult.SuccessResult(data));
        }

        /// <summary>
        /// 分页获取获取客户收货代码列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<PagedResultDto<GetConsigneeResponseDto>>))]
        public async Task<IActionResult> GetConsigneeListAsync([FromQuery] GetConsigneeRequestDto request)
        {
            var data = await _focService.GetConsigneeListAsync(request);
            return Ok(MessageResult.SuccessResult(data));
        }

        /// <summary>
        /// 获取终端列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<List<GetFOCTerminalListResponseDto>>))]
        public async Task<IActionResult> GetFOCTerminalListAsync()
        {
            var data = await _focService.GetFOCTerminalListAsync();
            return Ok(MessageResult.SuccessResult(data));
        }

        /// <summary>
        /// FOC物流填写
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        public async Task<IActionResult> CreateFocProductLogisticsAsync([FromBody] List<CreateFocLogisticsRequestDto> request)
        {
            var result = await _focService.CreateFocProductLogisticsAsync(request);
            return Ok(result);
        }

        /// <summary>
        /// 获取物流发货记录
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<List<GetFocProductLogisticsResponseDto>>))]
        public async Task<IActionResult> GetFocProductLogisticsAsync(Guid focDetailId)
        {
            var data = await _focService.GetFocProductLogisticsAsync(focDetailId);
            return Ok(MessageResult.SuccessResult(data));
        }

        /// <summary>
        /// 获取FOC物流信息列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<PagedResultDto<GetFocLogisticsListResponseDto>>))]
        public async Task<IActionResult> GetFocLogisticsListAsync([FromQuery] GetFocLogisticsListRequestDto request)
        {
            var data = await _focService.GetFocLogisticsListAsync(request);
            return Ok(MessageResult.SuccessResult(data));
        }

        /// <summary>
        /// 批量上传物流信息验证
        /// </summary>
        /// <param name="formFile"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult<ImportDataResponseDto<AnalyzeFocProductLogisticsExcelResponseDto>>))]
        public async Task<IActionResult> VarifyBatchCreateProductLogisticsAsync(IFormFile formFile)
        {
            try
            {
                var str = Path.GetExtension(formFile.FileName);
                string[] strings = [".xlsx", ".xls"];
                if (!strings.Contains(str))
                {
                    return Ok(MessageResult.FailureResult("请上传excel文件"));
                }
                using MemoryStream memoryStream = new();
                formFile.CopyTo(memoryStream);
                memoryStream.Position = 0;
                using XLWorkbook xLWorkbook = new XLWorkbook(memoryStream);
                IXLWorksheet ws = xLWorkbook.Worksheets.First();
                var rows = ws.RowsUsed().Skip(5).Select(r => new CreateFocProductLogisticsExcelDto
                {
                    ApplicationCode = r.Cell(1).GetString(),
                    RowNo = r.Cell(2).GetString(),
                    ProductSCode = r.Cell(6).GetString(),
                    BpcsOrderNo = r.Cell(8).GetString(),
                    ShippedQtyText = r.Cell(10).GetString(),
                    BatchNo = r.Cell(9).GetString(),
                    ShipmentDateText = r.Cell(11).GetString(),
                    LogisticsNo = r.Cell(12).GetString(),
                }).Where(m => HasNonNullProprerties(m));
                if (rows.Count() == 0) return Ok(MessageResult.FailureResult("导入内容不能为空!"));
                var response = await _focService.CheckCreateFocProductLogisticsExcelAsync(rows);
                return Ok(response);
            }
            catch (Exception e)
            {
                return Ok(MessageResult.FailureResult(e.Message));
            }
        }

        /// <summary>
        /// 批量上传物流信息
        /// </summary>
        /// <param name="import"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        public async Task<IActionResult> SubmitBatchCreateFocProductLogisticsAsync([FromBody] ImportDataResponseDto<AnalyzeFocProductLogisticsExcelResponseDto> import)
        {
            if (!import.IsSuccess) return Ok(MessageResult.FailureResult("验证失败!请修改后重新提交"));
            var result = await _focService.SubmitBatchCreateFocProductLogisticsAsync(import);
            return Ok(result);
        }

        /// <summary>
        /// 获取FOC子预算列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<PagedResultDto<GetFocSubBudgetsResponseDto>>))]
        public async Task<IActionResult> GetFocSubBudgetInfosAsync([FromQuery] GetFocSubBudgetsRequestDto request)
        {
            var data = await _focService.GetFocSubBudgetInfosAsync(request);
            return Ok(MessageResult.SuccessResult(data));
        }

        /// <summary>
        /// 获取FOC预算信息
        /// </summary>
        /// <param name="focSubBudgetId">FOC子预算Id</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<IEnumerable<GetFocBudgetInfoResponse>>))]
        public async Task<IActionResult> GetFocBudgetInfoAsync(Guid focSubBudgetId)
        {
            var data = await _focService.GetFocSubBudgetInfoAsync(focSubBudgetId);
            return Ok(MessageResult.SuccessResult(data));
        }

        /// <summary>
        /// 获取FOC消费大类
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<IEnumerable<ConsumeCategoryDto>>))]
        public async Task<IActionResult> GetFocConsumeCategoryAsync()
        {
            var data = await _focService.GetFocConsumeCategoryAsync();
            return Ok(MessageResult.SuccessResult(data));
        }

        /// <summary>
        /// 导出物流信息excel
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> ExportLogisticsInfoExcelAsync([FromBody] Guid[] ids)
        {
            var result = await _focService.ExportLogisticsInfoExcelAsync(ids);
            return File(result, "application/octet-stream", $"FOC物流信息-{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}.xlsx");
        }

        private bool HasNonNullProprerties<T>(T obj)
        {
            var propertis = obj.GetType().GetProperties();
            foreach (var property in propertis)
            {
                var value = property.GetValue(obj) as string;
                if (!string.IsNullOrWhiteSpace(value)) return true;
            }
            return false;
        }
        /// <summary>
        /// FOC我发起列表查询
        /// </summary>
        /// <param name="request">The request dto.</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult<PagedResultDto<GetFOCAppliedByMeResponseDto>>))]
        [Authorize(SpeakerPortalPermissions.TaskCenter.LaunchFocApplication)]
        public async Task<IActionResult> GetAppliedByMe([FromBody] GetFOCAppliedByMeRequestDto request)
        {
            var data = await _focService.GetAppliedByMeAsync(request);
            return Ok(MessageResult.SuccessResult(data));
        }
        /// <summary>
        /// FOC待我审批列表查询
        /// </summary>
        /// <param name="requestDto">The request dto.</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult<PagedResultDto<GettFOCApprovedByMeResponseDto>>))]
        [Authorize(SpeakerPortalPermissions.TaskCenter.ApproveFocApplication)]
        public async Task<IActionResult> GetApprovedByMe([FromBody] GetFOCApprovedByMeRequestDto requestDto)
        {
            var data = await _focService.GetApprovedByMeAsync(requestDto);
            return Ok(MessageResult.SuccessResult(data));
        }
        /// <summary>
        /// Pushes the foc for soi.
        /// </summary>
        /// <param name="Id">The identifier.</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> PushFOCForSOI([FromQuery] Guid Id)
        {
            var data = await _focService.PushFOCForSOIAsync(Id);
            return Ok(data);
        }
    }
}
