基本规则：查询[AUTO_BIZ_T_ProcurementApplication_Info]得到历史的采购申请单据，
以单据号Join[Form_6e32f7dd7c4e49aea79949009c3bf7ae]得到采购申请状态，
以ProcInstId join [AUTO_BIZ_T_ProcurementApplication_Info_PR]得到对应的采购申请明细信息


基本规则：基于03-1查询[AUTO_BIZ_T_ProcurementApplication_Info]得到历史的采购申请单据基本信息后，
以ProcInstId join [AUTO_BIZ_T_ProcurementApplication_Info_PR]得到特定的采购申请对应的可能多条明细行信息


SELECT  * from ods_AUTO_BIZ_T_ProcurementApplication_Info

SELECT  * from ods_AUTO_BIZ_T_ProcurementApplication_Info_PR
PurPRApplications

CREATE  index index_procinstid  ON ods_AUTO_BIZ_T_ProcurementApplication_Info_PR (procinstid);
CREATE  index index_serialNumber  ON ods_AUTO_BIZ_T_ProcurementApplication_Info (serialNumber);
CREATE  index index_procinstid  ON ods_AUTO_BIZ_T_ProcurementApplication_Info (procinstid);

CREATE  index index_procinstid  ON ods_Form_92ccaf9b95de4d7d9d8b411b2a030edc (procinstid);
CREATE  index index_procinstid  ON ods_Form_fefa2338743b4ebea533c8f6c5c2bacd (procinstid);



-- 1375004  
SELECT count(*) from ods_AUTO_BIZ_T_ProcurementApplication_Info  a 
join 
ods_AUTO_BIZ_T_ProcurementApplication_Info_PR b
on a.ProcInstId = b.ProcInstId 


SELECT count(*) from  PurPRApplicationDetails  -- 1374898

SELECT  count(*)  from ods_AUTO_BIZ_T_ProcurementApplication_Info_PR  -- 1374911
SELECT  count(*)  from ods_AUTO_BIZ_T_ProcurementApplication_Info  -- 561664

--- 

CREATE  index index_procinstid  ON PurPRApplications_tmp (procinstid);


---------------vendorid 准备
--- drop table #vendor_id
 use PLATFORM_ABBOTT;
SELECT
	a.id,TRIM(b.VEMLAD) VEMLAD,cast(VNDERX as NVARCHAR(50)) as VNDERX,cast(VMCMPY as NVARCHAR(50)) as VMCMPY
	,b.[VEXTNM]
into #vendor_id
from
     ODS_BPCS_AVM a ,
	 ODS_BPCS_PMFVM b  
where
	a.VCMPNY=b.VMCMPY
	and a.VENDOR=b.VNDERX
;

--SELECT * FROM #vendor_id


SELECT 
 * 
into #vendorID	
from (
	SELECT 
		a.id,
		TRIM(b.VEMLAD) VEMLAD,
		a.VENDOR,
		a.VCMPNY,
		row_number() over(partition by TRIM(b.VEMLAD) order by case when VCMPNY =20 then 0 else VCMPNY   end)  as rn
	from 
	     ODS_BPCS_AVM a ,
		 ODS_BPCS_PMFVM b  
	where 
		a.VCMPNY=b.VMCMPY 
		and a.VENDOR=b.VNDERX 
		AND b.VEMLAD is not null 
		and b.VEMLAD <> ''
	 	and VNSTAT='A'
		AND VTYPE LIKE 'NE%'
) t1 where rn =1
	;


 ------------------xml
	
--- SELECT procinstid,PRGridPanel from  GLOBALXmlContent_temp
	
SELECT * 
into #XmlContent_PRGridPanel
from (
	select   
		a.ProcInstId,
		RowData.value('(No)[1]', 'nvarchar(120)') AS [No],
	    RowData.value('(unit)[1]', 'nvarchar(120)') AS [unit]
	from  
	GLOBALXmlContent_temp  a
	CROSS APPLY PRGridPanel.nodes('/row') AS XMLTable(ROWDATA)
) t where   case when unit='' then null else unit end is not null 
;	

CREATE  index procinstid  ON #XmlContent_PRGridPanel (procinstid);

-- DROP table #XmlContent_PRGridPanel



---------------- Items_Info 准备
--   drop table #Items_Info; 

SELECT 
	l.*,
	case when pushflag <> 1 and z.IsWaiver =N'是' then 1 else 0 end  IsOffline,
	x.spk_NexBPMCode as PurchaserId
into #Items_Info
from (
	SELECT 
		PurchaseEmpId,
		ReturnReason,
		PaymentType,
		ProcInstId,
		RowNumber,
		PushDate,
		case when WaiverFormCode ='' then null else WaiverFormCode end WaiverFormCode,
		case 
			when PaymentType='AP' AND PurchaseEmpId is not null and PurchaseEmpId<>''  then 2
			when PaymentType='AP' and ReturnReason is not null and ReturnReason<>''   then 3
			else 1
		end as pushflag
		,id,case when BiddingFormCode ='' then null ELSE BiddingFormCode end BiddingFormCode
	from ods_T_Pur_PRItems_Info
) l
LEFT JOIN 
	ODS_T_Pur_PRItems_Info_Child z
	on l.id =z.id
left join 
	spk_staffmasterdata x
	on l.PurchaseEmpId = x.bpm_id and l.PurchaseEmpId<>'';


CREATE  index procinstid  ON #Items_Info (procinstid,RowNumber);

--- select * from #Items_Info

----------数据准备

 	SELECT  case 
		when processStatus IN(N'主采购循环审批中',N'重发起') THEN 4 
		when processStatus IN(N'关闭',N'PO审批结束') THEN 5 
		when processStatus IN(N'发起人终止') THEN 6 
	END AS OrderStatusFlag
	,procinstid,
	processStatus
	into #ods_Form_92cca
	from ods_Form_92ccaf9b95de4d7d9d8b411b2a030edc
	;
	CREATE  index procinstid  ON #ods_Form_92cca (procinstid);
---------------
	SELECT 
	case 
		when processStatus IN(N'重发起',N'审批中') THEN 1 
		when processStatus IN(N'完成') THEN 2 
		when processStatus IN(N'发起人终止') THEN 3 
	END AS OrderStatusFlag
	,procinstid,processStatus
	into #ods_Form_fefa2
 	from ods_Form_fefa2338743b4ebea533c8f6c5c2bacd;

	CREATE  index procinstid  ON #ods_Form_fefa2 (procinstid);
-------------
	SELECT   
		PRNo,
		PR_Item_No 
	into #ods_AUTO_BIZ_T_Goods_temp
	from ods_AUTO_BIZ_T_GoodsReceiveApplication_Info_PR
	group by PRNo,PR_Item_No
	;

	CREATE  index procinstid  ON #ods_AUTO_BIZ_T_Goods_temp (PRNo);
---------
SELECT  distinct PRFormCode,PRNumber into #ODS_AUTO_BIZ_T_BiddingApplication from ODS_AUTO_BIZ_T_BiddingApplication_Info_PR ;
--	CREATE  index procinstid1  ON #ODS_AUTO_BIZ_T_BiddingApplication (PRFormCode,PRNumber);
--SELECT * from #ODS_AUTO_BIZ_T_BiddingApplication
-----------------
-- drop table #PurPRApplicationDetails_temp

SELECT  
	a.ProcInstId
	,a.serialNumber
	,c.id  												as PRApplicationId
	,b.payMent
	,case  
		when b.payMent ='AR' THEN 1 
		when b.payMent ='AP' THEN 2 
		when b.payMent ='ER' THEN 3 
		when b.payMent ='ICB In' THEN 4
		when b.payMent ='FG' THEN 4
	end 												AS PayMethod
	,b.expectedDate										as EstimateDate		
	,b.content											as Content	
	,b.num												as Quantity
	,h.unit												as Unit
	,b.[price]											as UnitPrice
    ,b.[rmbAmount]										as TotalAmount
    ,b.speakerType										as VendorType  
	,b.ServiceTime										as ServiceDuration
    ,case when b.rceNo='NULL' OR b.rceNo ='' then null else b.rceNo end as RceNo
    ,b.icbAmount 										as IcbAmount
    ,a.applicantEmpId									as CreatorId
    ,a.applicationDate									as CreationTime
    ,0													as IsDeleted
    ,b.vendorName										as VendorName
    ,b.No												as RowNo
    ,case when b.expectedDate<a.applicationDate then '1' else '0' end 	AS IsPrLate   -- 47 line
    ,case when b.NoType = 'R' then b.re_id else 'NULL' end  			AS HedgePrDetailId
    ,b.[IDNumber]										as CardNo
	,b.[certificateNo]									as CertificateCode
	,CONCAT(b.city,'_',b.cityCode) 						AS CityIdName
	,b.[costCenter]										as [CostCenterName]
	,b.[expenseNature]									as [CostNatureName]
	,b.[executiveEmail]									as [ExecutorEmail]
	,b.[executive]										as [ExecutorName]
	,b.[HCPLevel]										as [HcpLevelName]
	,b.[Department]										as [HosDepartment]
	,b.[SpeakInfo]										as [Hospital]
	,CASE WHEN b.[OriginalExpectedDate]='NULL' THEN NULL ELSE 	 b.[OriginalExpectedDate] END	 as [OriginalEstimateDate]
	,b.[OriginalVendorCode]								as [OriginalVendorCode]
	,b.[OriginalVendor] 								as [OriginalVendorName]
	,b.[SlideTypeName] 									as [SlideTypeName]
	,b.[Department] 									as [StandardDepartment]
	,b.speakerType										as VendorTypeName
	,b.[vendorCode]										as [VendorCode]
	,b.[rmbAmount]  									as [TotalAmountRMB]
	,b.[rmbAmount]										as [OriginalTotalAmount]
	,b.[rmbAmount]										as [OriginalTotalAmountRMB]
	,b.SlideName 										as [SlideAName] 
	,''													as BWApplicationId 		--TODO 逻辑没有搞懂
	,b.expenseNatureCode
	,b.cityCode,b.city ,a.f_code,a.productCbo_Value,b.SlideTypeId ,b.ExecutiveEmail
	,b.[No],b.CostCenter as CostCenter1,b.HCPLevel,b.TermCode as TermCode1,b.costCenterCode as CostCenterCode1
	
into #PurPRApplicationDetails_temp
from 
	ods_AUTO_BIZ_T_ProcurementApplication_Info  a 
join 
	ods_AUTO_BIZ_T_ProcurementApplication_Info_PR b
    on a.ProcInstId = b.ProcInstId 
join 
   PurPRApplications_tmp  c
   on a.ProcInstId =c.ProcInstId
left join 
	#XmlContent_PRGridPanel h
	on a.ProcInstId = h.ProcInstId and b.no =h.no
;

CREATE  index index_procinstid  ON #PurPRApplicationDetails_temp (procinstid);
CREATE  index index_serialNumber  ON #PurPRApplicationDetails_temp (serialNumber);
CREATE  index index_SlideTypeId  ON #PurPRApplicationDetails_temp (SlideTypeId);



-----------------
-- drop table  #PurPRApplicationDetails_temp1

SELECT  
	a.*
	,ss.id												as VendorId
	,d.spk_NexBPMCode 									as CostNature
	,f.spk_NexBPMCode									as CityId
    ,g.spk_NexBPMCode									as ProductId
    ,j.spk_code											as SlideType
    ,''													as SlideName  -- TODO 以名称匹配至后台配置中的幻灯片名称，填入匹配出的SPECTRA ID，如果匹配不出则留空  [SlideConfigs] BPM内不存在，线下表格数据迁移
into #PurPRApplicationDetails_temp1
from 
	#PurPRApplicationDetails_temp a
left join 
	#vendor_id ss
	on a.vendorCode = cast(ss.VNDERX as NVARCHAR(50))
		and trim(a.vendorName)  = trim(ss.VEXTNM) 
		and a.f_code  =cast(ss.VMCMPY as NVARCHAR(50))
LEFT JOIN 
	spk_costnature d
	on a.expenseNatureCode =d.spk_BPMCode
left JOIN 
	spk_citymasterdata f
	on a.cityCode =f.spk_citynumber and a.city = f.spk_Name
left join 
	spk_productmasterdata g
	on a.productCbo_Value =g.spk_BPMCode
left join 
	spk_dictionary j
	on a.SlideTypeId  =j.spk_BPMCode and j.spk_type=N'幻灯片类型' -- 1375004
;

CREATE  index index_procinstid  ON #PurPRApplicationDetails_temp1 (procinstid);

-------------


------  drop table #PurPRApplicationDetails_temp2

SELECT  
	a.*
    ,k.id												as Executor
    ,'{}'												as ExtraProperties
    ,case when l.pushflag is null then 1 else l.pushflag end as   pushflag
    ,l.PurchaserId
    ,l.PushDate											as PushTime
    ,l.ReturnReason										as RollbackReason
    ,case when l.IsOffline is null then 0 else l.IsOffline end as IsOffline
	,UPPER(sc.spk_NexBPMCode) 							as CostCenter
	,l.BiddingFormCode									as [BiddingId]   --TODO 感觉逻辑不明确
into #PurPRApplicationDetails_temp2
from 
	#PurPRApplicationDetails_temp1 a
left join 
	#vendorID k
	on trim(a.ExecutiveEmail) =k.VEMLAD
left join 
	 #Items_Info l 
	 on l.[ProcInstId] = a.ProcInstId  and l.[RowNumber]=a.[No]
left join 
	spk_costcentermasterdata sc
	on a.costCenterCode1 = sc.spk_BPMCode 
;

CREATE  index index_procinstid  ON #PurPRApplicationDetails_temp2 (procinstid);
CREATE  index index_serialNumber  ON #PurPRApplicationDetails_temp2 (serialNumber);
CREATE  index index_SlideTypeId  ON #PurPRApplicationDetails_temp2 (SlideTypeId);
CREATE  index costCenterCode1  ON #PurPRApplicationDetails_temp2 (costCenterCode1);
CREATE  index expenseNatureCode  ON #PurPRApplicationDetails_temp2 (expenseNatureCode);

--CREATE  index Res_Code  ON ods_T_RESOURCE (Res_Code);

------------------------------

---  drop table #PurPRApplicationDetails_temp3

SELECT  
	a.*
	,o.Res_Data1										as CostCenterCode

into #PurPRApplicationDetails_temp3
from 
	#PurPRApplicationDetails_temp2 a
left join 
	ods_T_RESOURCE o
	on a.costCenterCode1 =o.Res_Code and a.costCenterCode1<>'';

CREATE  index index_procinstid  ON #PurPRApplicationDetails_temp3 (procinstid);
CREATE  index index_serialNumber  ON #PurPRApplicationDetails_temp3 (serialNumber);
CREATE  index index_SlideTypeId  ON #PurPRApplicationDetails_temp3 (SlideTypeId);
CREATE  index costCenterCode1  ON #PurPRApplicationDetails_temp3 (costCenterCode1);
CREATE  index OriginalVendorCode  ON #PurPRApplicationDetails_temp3 (OriginalVendorCode);

------------------
---  drop table 	#PurPRApplicationDetails_temp4;

SELECT  
	a.*
	,s.PRNo
	,p.Res_Data1										as CostNatureCode
	,case when s.PRNo is not null then 1 else 0 end     as IsVendorConfimed     
	,dd.id												as OriginalVendorId
into #PurPRApplicationDetails_temp4
from 
	#PurPRApplicationDetails_temp3 a
left join 
	ods_T_RESOURCE p
	on a.expenseNatureCode =p.Res_Code and a.expenseNatureCode<>''
left join 
	#ods_AUTO_BIZ_T_Goods_temp s
	on   a.serialNumber =s.PRNo  and a.no=s.PR_Item_No
 LEFT JOIN 
 	#vendor_id dd
 	ON a.OriginalVendorCode =  dd.VNDERX 
 		AND  a.f_code = dd.VMCMPY 
 		and TRIM(a.VendorName) = trim(dd.VEXTNM)

;
CREATE  index index_procinstid  ON #PurPRApplicationDetails_temp4 (procinstid);
CREATE  index index_serialNumber  ON #PurPRApplicationDetails_temp4 (serialNumber);
CREATE  index index_SlideTypeId  ON #PurPRApplicationDetails_temp4 (SlideTypeId);

--------------



-----------------
	----------------------------------
		--------------------------------------------------------------------

--	drop table PurPRApplicationDetails_temp_chcke_data;

SELECT  
	a.*
	,CONCAT(jj.VTERM,'_',jj.VTMDDY) 					as [TermCode]             
	,jj.VTMDDY											as [TermCodeDays]		
	,UPPER(di3.spk_code) 								as HcpLevelCode
	,case 
		when a.payMent ='AP' AND a.prno is not null then ff.OrderStatusFlag
		when a.payMent ='AP' AND hh.PRFormCode is not null then gg.OrderStatusFlag
	end as  OrderStatusFlag
	,'' 												as [MeetingStatus]        -- 关联的表没有找到
--	,''													as BWApplicationId 		--TODO 逻辑没有搞懂
into PurPRApplicationDetails_temp_chcke_data
from 
	#PurPRApplicationDetails_temp4 a
 left join 
	spk_dictionary di3
	on a.HCPLevel  =di3.spk_Name and di3.spk_type=N'HCP级别'
 left join #ods_Form_92cca ff
 	on a.ProcInstId = ff.ProcInstId
 left join #ods_Form_fefa2 gg
 	 on a.ProcInstId = gg.ProcInstId
left join  
	#ODS_AUTO_BIZ_T_BiddingApplication  hh
	on   a.serialNumber =hh.PRFormCode  and a.no=hh.PRNumber
left JOIN 
(
	 SELECT DISTINCT  VTERM,VTMDDY   from   ODS_TMP_AVT
) jj
	on jj.VTERM = a.TermCode1	
	;


   CREATE  index index_id on  PurPRApplicationDetails_temp_chcke_data(ProcInstId,no);
		-----------------
	----------------------------------
--------------------------------------------------------------------
  
 ---- PRApplicationId 验证
	
	
SELECT a.PRApplicationId ,b.id from PurPRApplicationDetails a
full join
PurPRApplications b
 on a.PRApplicationId =b.id
 where a.PRApplicationId is null or b.id is null
	
 ---- 
   ---- 验证数据开始 #PurPRApplicationDetails   总数总数 验证
 
 select count(*) 
from 
	ods_AUTO_BIZ_T_ProcurementApplication_Info  a 
join 
	ods_AUTO_BIZ_T_ProcurementApplication_Info_PR b
    on a.ProcInstId = b.ProcInstId 
union  
select count(*) 
from PurPRApplicationDetails
 
  
   
--   drop table #PurPRApplicationDetails_temp
   ------  CreationTime   CreatorId 验证
--   CREATE  index index_id on  #PurPRApplicationDetails_temp5(ProcInstId,serialNumber)
   CREATE  index index_id on PurPRApplicationDetails(id)
   CREATE  index index_id on PurPRApplicationDetails_tmp(ProcInstId,RowNo)
   CREATE  index index_id on PurPRApplicationDetails_tmp(id)

   select a.*,ppt.ProcInstId 
   into PurPRApplicationDetails_temp_chcke_data1
	from
		PurPRApplicationDetails a
	join 
		PurPRApplicationDetails_tmp ppt 
	 on a.id =ppt.id
   ;
     CREATE  index index_id on  PurPRApplicationDetails_temp_chcke_data1(ProcInstId,RowNo);

   
   
      ----- 未通过--------------
--   	PayMethod VendorId CityId  Unit SlideName  Executor  RceNo  CreatorId   PushFlag  PurchaserId  IsOffline
--      HedgePrDetailId(逻辑)   CostNatureCode IsVendorConfimed  OriginalEstimateDate  OriginalVendorId  SlideTypeName  TermCode
--      TermCodeDays   OrderStatusFlag
 -------------------0----------------
   
declare @columnName NVARCHAR(128)  ='SlideTypeName';
declare @SQL NVARCHAR(MAX);
SET @SQL='
SELECT  
	TOP 100
	t1.ProcInstId,t2.id,t2.serialNumber,t1.[No],
	t1.'+quotename(@columnName)+',t2.'+quotename(@columnName)+'
from
  	(select * from  PurPRApplicationDetails_temp_chcke_data where  '+quotename(@columnName)+' is not null  AND '+ quotename(@columnName) +' <> ''NULL'') t1
full join
 PurPRApplicationDetails_temp_chcke_data1  t2
     on t1.ProcInstId = t2.ProcInstId and t1.No= t2.RowNo  
WHERE  

  t1.'+quotename(@columnName)+'<> t2.'+quotename(@columnName)+'
  OR (t1.'+quotename(@columnName)+' IS NULL AND t2.'+quotename(@columnName)+' IS NOT NULL)
  OR (t1.'+quotename(@columnName)+' IS NOT NULL AND t2.'+quotename(@columnName)+' IS  NULL)
 
';
SELECT  @SQL;
--EXEC sp_executesql @SQL;

SELECT  @SQL;
  ---------------------------------- 
   
   
    