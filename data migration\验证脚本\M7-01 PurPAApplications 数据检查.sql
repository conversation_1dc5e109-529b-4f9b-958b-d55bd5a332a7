use PLATFORM_ABBOTT;

-------------
--基本规则：查询[AUTO_BIZ_T_PaymentApplication_Info]得到历史的付款申请单据，
--以单据号查询[T_Pur_ProcessForm_GR_GRDetail_Info]得到对应的可能多行收货申请明细作为付款金额分摊依据，
--及[AUTO_BIZ_T_PaymentApplication_Info_PR]得到付款关联的PR明细信息

--  create index ProcInstId on  ods_AUTO_BIZ_T_PaymentApplication_Info(ProcInstId);
--  create index ProcInstId on  ods_AUTO_BIZ_T_PaymentApplication_Info(ProcInstId);
--  create index ProcInstId on  ods_AUTO_BIZ_T_PaymentApplication_Info_PR(ProcInstId);
--   create index ProcInstId on  ods_Form_e37632eb82f04fbda355cffdac744166(ProcInstId);
--   create index re_code on  ODS_T_RESOURCE(Res_Parent_Code)

-- drop table GLOBALXmlContent_temp_m7_01

SELECT 
	j.ProcInstId ,
	XmlContent.query('/root/AttachmentBlock/row') AS AttachmentBlock,
	XmlContent.query('/root/AttachmentBlock1/row') AS AttachmentBlock1,
	XmlContent.query('/root/approvalHistoryGrid/row') AS approvalHistoryGrid,
	XmlContent.value('(/root/PaymentApplication_applicantInfoBlock_MainStore/applicantDept_Text)[1]', 'nvarchar(500)') AS ApplyUserBuToDeptName,--以ProcInstId找到对应的单据xml后，在PaymentApplication_applicantInfoBlock_MainStore内查询对应字段名后填入
	XmlContent.value('(/root/PaymentApplication_hiddenBlock_MainStore/ERSUrl)[1]', 'nvarchar(500)')  AS EsignPdf,--以ProcInstId找到对应的单据xml后，在PaymentApplication_hiddenBlock_MainStore内查询对应字段名后填入
	XmlContent.value('(/root/PaymentApplication_ERSBlock_MainStore/MeetingRemark)[1]', 'nvarchar(500)')  AS MeetingModifyRemark,--以ProcInstId找到对应的单据xml后，在PaymentApplication_ERSBlock_MainStore内查询对应字段名后填入
	XmlContent.value('(/root/PaymentApplication_ERSBlock_MainStore/AmountRemark)[1]', 'nvarchar(500)') AS AmountModifyRemark,--以ProcInstId找到对应的单据xml后，在PaymentApplication_ERSBlock_MainStore内查询对应字段名后填入
	--XmlContent.value('(/root/PaymentApplication_hiddenBlock_MainStore/POSerialNumber)[1]', 'nvarchar(500)')  AS POId,--基于该单号查询出对应PurPOApplications中的Id 
	XmlContent.value('(/root/PaymentApplication_hiddenBlock_MainStore/POSerialNumber)[1]', 'nvarchar(500)')  AS POSerialNumber,--
	XmlContent.value('(/root/PaymentApplication_PaymentInfoBlock_MainStore/exchangeRate)[1]', 'nvarchar(500)') AS ExchangeRate,--以ProcInstId找到对应的单据xml后，在PaymentApplication_PaymentInfoBlock_MainStore内查询对应字段名后填入
	XmlContent.value('(/root/PaymentApplication_PaymentInfoBlock_MainStore/type_Value)[1]', 'nvarchar(500)') AS type_Value,--以ProcInstId找到对应的单据xml后，在PaymentApplication_PaymentInfoBlock_MainStore内查询对应编码，再对应填入配置中的Code
	XmlContent.value('(/root/PaymentApplication_PaymentInfoBlock_MainStore/branchOffice_Value)[1]', 'nvarchar(500)') AS branchOffice_Value,--以ProcInstId找到对应的单据xml后，在PaymentApplication_PaymentInfoBlock_MainStore内查询对应字段名后填入
	XmlContent.value('(/root/PaymentApplication_hiddenBlock_MainStore/AdvancePayment)[1]', 'nvarchar(500)') AdvancePayment,--以ProcInstId找到对应的单据xml后，在PaymentApplication_hiddenBlock_MainStore内查询对应字段名后填入，true-1；false-0
	XmlContent.value('(/root/PaymentApplication_PaymentInfoBlock_MainStore/receiveTitleDate)[1]', 'nvarchar(500)') AS receiveTitleDate,--以ProcInstId找到对应的单据xml后，在PaymentApplication_PaymentInfoBlock_MainStore内查询对应字段名后填入
	XmlContent.value('(/root/PaymentApplication_PaymentInfoBlock_MainStore/afterRepairInvoice)[1]', 'nvarchar(500)') as afterRepairInvoice,
	XmlContent.value('(/root/PaymentApplication_PaymentInfoBlock_MainStore/remark)[1]', 'nvarchar(500)') AS Remarks,--以ProcInstId找到对应的单据xml后，在PaymentApplication_PaymentInfoBlock_MainStore内查询对应字段名后填入
	XmlContent.value('(/root/PaymentApplication_FinancialInfoBlock_MainStore/InvoiceDescription)[1]', 'nvarchar(500)') AS InvoiceDescription,--以ProcInstId找到对应的单据xml后，在PaymentApplication_FinancialInfoBlock_MainStore内查询对应字段名后填入
	XmlContent.value('(/root/PaymentApplication_hiddenBlock_MainStore/VendorCode)[1]', 'nvarchar(500)')	as VendorCode,
	XmlContent.value('(/root/PaymentApplication_hiddenBlock_MainStore/VendorName)[1]', 'nvarchar(500)') as VendorName,
--	XmlContent.value('(/root/approvalHistoryGrid/approvalPersonEmpId)[1]', 'nvarchar(500)') AS approvalPersonEmpId,--单据接收人信息，以ProcInstId查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到approvalHistoryGrid下approvalLevel为"单据接收"且action为"同意"的记录，查询approvalTime最新的记录对应的用户ID，以该ID匹配至员工主数据
--	XmlContent.value('(/root/approvalHistoryGrid/approvalPerson)[1]', 'nvarchar(500)') AS approvalPerson,--单据接收人信息，以ProcInstId查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到approvalHistoryGrid下approvalLevel为"单据接收"且action为"同意"的记录，查询approvalTime最新的记录对应的用户姓名
	--XmlContent.value('(/root/PaymentApplication_PaymentInfoBlock_MainStore/branchOffice_Value)[1]', 'nvarchar(500)') as branchOffice_Value,
	XmlContent.value('(/root/PaymentApplication_PaymentInfoBlock_MainStore/branchOffice_Text)[1]', 'nvarchar(500)')  AS branchOffice_Text,--以ProcInstId找到对应的单据xml后，在PaymentApplication_PaymentInfoBlock_MainStore内查询对应字段后以该组合查询出对应的城市主数据
	XmlContent.value('(/root/PaymentApplication_PaymentInfoBlock_MainStore/currency)[1]', 'nvarchar(500)') AS Currency,--以ProcInstId找到对应的单据xml后，在PaymentApplication_PaymentInfoBlock_MainStore内查询对应字段
	--XmlContent.value('(/root/PaymentApplication_PaymentInfoBlock_MainStore/exchangeRate)[1]', 'nvarchar(500)') AS PlanRate,--以ProcInstId找到对应的单据xml后，在PaymentApplication_PaymentInfoBlock_MainStore内查询对应字段名后填入(和上方的exchange rate相同)
	XmlContent.value('(/root/PaymentApplication_PaymentInfoBlock_MainStore/TaxChangeReason)[1]', 'nvarchar(500)') AS TaxChangeReason,--税率修改原因，以ProcInstId找到对应的单据xml后，在PaymentApplication_PaymentInfoBlock_MainStore内查询对应字段名后填入
	XmlContent.value('(/root/PaymentApplication_DFInfoBlock_MainStore/Dfremark)[1]', 'nvarchar(500)') AS Dfremark,
    XmlContent.value('(/root/PaymentApplication_PaymentInfoBlock_MainStore/type_Value)[1]', 'nvarchar(500)') AS UrgentType
into GLOBALXmlContent_temp_m7_01
from  ODS_T_FORMINSTANCE_GLOBAL_bak j,
ods_AUTO_BIZ_T_PaymentApplication_Info a
where   a.ProcInstId = j.ProcInstId  
;

create index ProcInstId on  GLOBALXmlContent_temp_m7_01(ProcInstId);
--------
--SELECT * from GLOBALXmlContent_temp_m7_01
---------------------
drop table #companyCode_Temp

SELECT 
	a.Res_Data,
	b.spk_NexBPMCode 
into #companyCode_Temp
from (
	SELECT  * from ODS_T_RESOURCE otr where Res_Parent_Code ='61a3f911b5ae4bc98cddd441833d861e'
) a
left join 
	spk_companymasterdata b
	on a.Res_Code = b.spk_BPMCode;


------------
	
	
DROP TABLE  #vendor_id;
 
SELECT
	a.id
	,TRIM(b.VEMLAD) VEMLAD
	,trim(VEXTNM) VEXTNM 
	,cast(VNDERX as NVARCHAR(120)) as VNDERX1
	,cast(VMCMPY as NVARCHAR(120)) as VMCMPY1
	,VNDERX
	,VMCMPY
into #vendor_id
from
     ODS_BPCS_AVM a ,
	 ODS_BPCS_PMFVM b  
where
	a.VCMPNY=b.VMCMPY
	and a.VENDOR=b.VNDERX
;

---------
drop table #VendorId_1;

SELECT 
	ProcInstId,
	VendorCode ,
	b.VEXTNM,
	b.id,*
into #VendorId_1
from GLOBALXmlContent_temp_m7_01 a
left join 
	#vendor_id b
	on a.VendorCode = b.VNDERX1 and trim(a.VendorName) = b.VEXTNM
where b.id is not null 
;
create index #ProcInstId on #VendorId_1(ProcInstId);

SELECT  * from #VendorId_1;
------

	SELECT 
		a.ProcInstId, 
--		SerialNumber,
--		id,
--		ApplicationCode ,
		STRING_AGG( cast(b.id as nvarchar(120)), ',') WITHIN GROUP (ORDER BY b.id)  as id
	into #PurGRApplications_id
	from 
		ODS_AUTO_BIZ_T_GoodsReceiveApplication_Info a
	join 
	 	PurGRApplications b
	 	on a.SerialNumber = b.ApplicationCode
	 group by a.ProcInstId
	;
	
	CREATE index #id on #PurGRApplications_id(ProcInstId);

---------


SELECT 
	ProcInstId
	,STRING_AGG( cast(b.id as nvarchar(120)), ',') WITHIN GROUP (ORDER BY b.id)  as id
into #AttachmentBlock
from (
	select   
		a.ProcInstId
		,RowData.value('(up_Id)[1]', 'nvarchar(120)') AS up_id 
	from  
	GLOBALXmlContent_temp_m7_01  a
	CROSS APPLY AttachmentBlock.nodes('/row') AS XMLTable(ROWDATA)
) t  
  
left join
	Attachments b
	on t.up_id = b.bpmid
group by 
	ProcInstId
;
 
CREATE  index procinstid  ON #AttachmentBlock (procinstid);

-------

SELECT 
	ProcInstId
	,STRING_AGG( cast(b.id as nvarchar(120)), ',') WITHIN GROUP (ORDER BY b.id)  as id
into #AttachmentBlock1
from (
	select   
		a.ProcInstId
		,RowData.value('(up_Id)[1]', 'nvarchar(120)') AS up_id 
	from  
	GLOBALXmlContent_temp_m7_01  a
	CROSS APPLY AttachmentBlock1.nodes('/row') AS XMLTable(ROWDATA)
) t  
  
left join
	Attachments b
	on t.up_id = b.bpmid
group by 
	ProcInstId
;
 
CREATE  index procinstid  ON #AttachmentBlock1 (procinstid);

------------------

SELECT 
	t3.*,
	t4.spk_NexBPMCode 
into #ApprovedUserId
from (
		SELECT 
			t1.*,
			t2.Emp_Id ,
			t2.Emp_AD_Account 
		from (
			SELECT 
				ProcInstID ,
				FinishDate,
				Destination,
				SUBSTRING(Destination,charindex(':',Destination)+1,10000) as username,
				ROW_NUMBER ()over(partition by ProcInstID order by finishdate desc) rn 
			from  ods_T_PROCESS_Historys  a 
			where ActName =N'单据完成'
		) t1
		left JOIN 
			ODS_T_EMPLOYEE t2
		on trim(t1.username) =trim(t2.Emp_AD_Account) and t1.rn=1
		where t1.rn=1
) t3
left join 
	spk_staffmasterdata t4
	on t3.emp_id =t4.bpm_id ;


CREATE  index ProcInstID  ON #ApprovedUserId (ProcInstID);

---------------

SELECT 
	t3.*,
	t4.spk_NexBPMCode,t4.spk_staffNumber,t4.spk_name
into #GLOBAL_new_time
from (
	SELECT  * 
	from (
		SELECT 
			*,
			ROW_NUMBER ()over(PARTITION by ProcInstId order by cast(approvalTime as timestamp)   desc) as rn
		from (
			select   
				a.ProcInstId,
				RowData.value('(approvalLevel)[1]', 'nvarchar(120)') AS [approvalLevel],
			    RowData.value('(action)[1]', 'nvarchar(120)') AS [action],
			    RowData.value('(approvalTime)[1]', 'nvarchar(120)') AS [approvalTime],
			    RowData.value('(approvalPerson)[1]', 'nvarchar(120)') AS [approvalPerson],
			    RowData.value('(approvalPersonEmpId)[1]', 'nvarchar(120)') AS [approvalPersonEmpId] 
			from  
			GLOBALXmlContent_temp_m7_01  a
			CROSS APPLY approvalHistoryGrid.nodes('/row') AS XMLTable(ROWDATA)
		) t1
	) t2 where rn =1
) t3
left join 
	spk_staffmasterdata t4
	on t3.approvalPersonEmpId =t4.bpm_id
;
CREATE  index ProcInstID  ON #GLOBAL_new_time (ProcInstID);

----------------


SELECT 
	a.ProcInstId ,
	STRING_AGG( cast(n.id as nvarchar(120)), ',') WITHIN GROUP (ORDER BY n.id)  as id
into #PurPRApplications
from 
	ods_AUTO_BIZ_T_PaymentApplication_Info_PR a
left join
	PurPRApplications n
	on a.[PR_No] = n.ApplicationCode 
group by ProcInstId 
;
CREATE  index ProcInstID  ON #PurPRApplications (ProcInstID);


--------------
-- poid
drop table #PurPAApplications_temp;

SELECT  
	s.processStatus,s.payamount,a.procinstid
	,a.serialNumber														as [ApplicationCode]
	,s.[processStatus]													as [Status]              ---todo
	,g.spk_NexBPMCode  													as [ApplyUserId]
	,a.[applicantEmpName]												as [ApplyUserName]
	,a.[applicationDate]												as [ApplyTime]
	,h.spk_NexBPMCode			 										as ApplyUserBu
	,j.Res_Name															as [ApplyUserBuName]
	,case 
		 when trim(d.AdvancePayment) ='true' then 1 else 0 end			as AdvancePayment
	,d.ApplyUserBuToDeptName											as [ApplyUserBuToDeptName]
	,d.EsignPdf														    as [EsignPdf]
	,case when d.EsignPdf <>'' and d.EsignPdf  is not null
		and a.submitType =N'线上递交'  then 1 else 2 end 					as DeliveryMode
--	,a.submitType
	,d.MeetingModifyRemark 												as [MeetingModifyRemark]
	,d.AmountModifyRemark 												as [AmountModifyRemark]
	,k.id																as [GRId]
	,l.SerialNumber														as GRApplicationCode
	,z.id																as [POId]
	,d.POSerialNumber													as [POApplicationCode]
	,a.companyCode														as [CompanyCode]
	,d.exchangeRate														as [ExchangeRate]
	,case 
		 when a.UrgentPayment ='true' then 1
		 else 0 
	 end 																as [UrgentPayment]
	,case 
		when s.[paymentForm_Text]= N'电汇' 		THEN 1	
		when s.[paymentForm_Text]= N'AP' 		THEN 2	
		when s.[paymentForm_Text]= N'转账支票' 	THEN 3	
	end 																as [PaymentType]   -- 电汇-1；AP-2；转账支票-3
	,s.[receiveTitle]													as [ReceivingHeader]
	,d.receiveTitleDate													as [SupplierWarehousingTime]
	,case when d.afterRepairInvoice ='true' then '1' else '0'	end											as IsBackupInvoice
	,d.Remarks															as Remarks
	,x.id 																as Invoice
	,c.id																as Attachments
	,a.AkritivID														as [AkritivCaseID]
	,left(a.TermCode,CHARINDEX('_',a.TermCode))							as [PaymentTerms]
	,d.InvoiceDescription												as [InvoiceDescription]
	,a.[ReceiveDate]													as [ReceivedDocumentsDate]
	,'{}'																as [ExtraProperties]
	,a.[applicationDate]												as [CreationTime]
	,a.[applicantEmpId]													as [CreatorId]
	,0																	as [IsDeleted]
	,v.id 																as VendorId
	,s.[receiveTitle]													as [VendorName]
	,f.spk_NexBPMCode													as CompanyId
	,a.[company]														as [CompanyName]
	,a.[payAmount]														as [PayTotalAmount]
	,a.[ReceiveDate]													as AcceptedTime
	,'todo'																as EstimatedPaymentDate	  --todo  逻辑不明确 ，暂停
	,'todo'																as TaskType               --todo  逻辑不明确 ，暂停
	,n.id																as [PRId]
	,case when m.approvalLevel=N'单据接收' AND m.[action]=N'同意' then m.approvalPerson end 					as [AccepterName]
	,case when m.approvalLevel=N'单据接收' AND m.[action]=N'同意' then m.spk_NexBPMCode end 					as [AccepterId]
	,case when s.processStatus in (N'复审完毕',N'完成') THEN  mm.FinishDate end as [ApprovedDate]
	,case when s.processStatus in (N'复审完毕',N'完成') and  m.[action]=N'同意' then  mm.spk_NexBPMCode	END  	as [ApprovedUserId]
	,l.SupplierCode														as [VendorCode]
	,CONCAT(d.[branchOffice_Value],d.[branchOffice_Text])				as [CityId]
	,d.branchOffice_Value												as [City]
	,d.currency															as [Currency]
	,d.currency															as [CurrencySymbol]
	,0																	as [ExpectedFloatRate]
	,d.ExchangeRate														as [PlanRate]
	,case when l.PRItemType=N'AR' THEN 1 
					 when l.PRItemType=N'AP' THEN 2 END 				AS [PayMethod]
	,d.TaxChangeReason													as [ReasonModification]
	,a.[VendorScore]													as [VendorRating]
	,d.[Dfremark]														as [VendorRatingRemark]
into #PurPAApplications_temp
from 
	ods_AUTO_BIZ_T_PaymentApplication_Info a   -- 624157
left join
	ods_Form_e37632eb82f04fbda355cffdac744166 s  -- 624157
	on a.procinstid= s.procinstid
left join
	GLOBALXmlContent_temp_m7_01 d
	on a.procinstid= d.procinstid
left join 
	#companyCode_Temp f
	on a.companyCode = f.Res_Data
left JOIN 
	spk_staffmasterdata g 
	on a.applicantEmpId=g.bpm_id  
left join
	spk_organizationalmasterData h
	on  a.BUId  =  cast(h.spk_BPMCode as nvarchar(120))
left join 
	ODS_T_RESOURCE j
	on a.BUId = j.Res_Code 
left join 
	#PurGRApplications_id k
	on a.GR_ProcInstId = k.ProcInstId
left join 
	ODS_AUTO_BIZ_T_GoodsReceiveApplication_Info l
	on a.GR_ProcInstId = l.ProcInstId
left join 
	PurPOApplications	z
	on d.[POSerialNumber] = z.ApplicationCode 
left join 
	#AttachmentBlock1 x
	on a.ProcInstId = x.ProcInstId
left join 
	#AttachmentBlock c
	on a.ProcInstId = c.ProcInstId
left join 
    #VendorId_1 v
    on a.ProcInstId =v.ProcInstId
left join 
	#PurPRApplications n
	on a.ProcInstId = n.ProcInstId 
left join 
	#GLOBAL_new_time m
	on a.ProcInstId = m.ProcInstId
left join 
	#ApprovedUserId mm
	on a.ProcInstId = mm.ProcInstId
	;
create index #ProcInstId on #PurPAApplications_temp(ProcInstId);
-------
create index index_id on PurPAApplications_tmp(id);
create index index_id on PurPAApplications(id);
create index index_ProcInstId on PurPAApplications_tmp(ProcInstId);


SELECT  * from  #PurPAApplications_temp;
----------

--------- 自查  检查临时表 字段全null，可能就有问题------start

DECLARE @TableName NVARCHAR(MAX) = '#PurPAApplications_temp'; -- 检查临时表 字段全null，可能就有问题
DECLARE @SQL NVARCHAR(MAX) = '';
DECLARE @ColumnName NVARCHAR(MAX);
DECLARE @DataType NVARCHAR(50);

-- 构建动态SQL，检查每个字段是否全为 NULL 或空字符串
DECLARE db_cursor CURSOR FOR
SELECT COLUMN_NAME, DATA_TYPE
FROM tempdb.INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME LIKE @TableName + '%' -- 临时表的名称
AND TABLE_SCHEMA = 'dbo' -- 如果使用的是其他模式，请修改为对应的模式

OPEN db_cursor
FETCH NEXT FROM db_cursor INTO @ColumnName, @DataType

-- 遍历每个列
WHILE @@FETCH_STATUS = 0
BEGIN
    -- 针对字符串类型，检查是否为空字符串；对于其他类型，检查是否为 NULL
    IF @DataType IN ('nvarchar', 'varchar', 'char', 'text')  -- 字符串类型
    BEGIN
        SET @SQL = 'IF Not EXISTS (SELECT 1 FROM ' + @TableName + ' WHERE ' + QUOTENAME(@ColumnName) + ' IS NOT NULL AND ' 
                   + QUOTENAME(@ColumnName) + ' <> '''') ' + 
                   'BEGIN ' +
                   'SELECT CASE WHEN COUNT(*) = 0 THEN ''' 
                   + @ColumnName + ' is all NULL or empty''' + ' ELSE '''' END AS Result FROM ' 
                   + @TableName + ' WHERE ' + QUOTENAME(@ColumnName) + ' IS NOT NULL AND ' 
                   + QUOTENAME(@ColumnName) + ' <> ''''; ' +
                   'END';
    END
    ELSE  -- 非字符串类型，仅检查 NULL
    BEGIN
        SET @SQL = 'IF Not EXISTS (SELECT 1 FROM ' + @TableName + ' WHERE ' + QUOTENAME(@ColumnName) + ' IS NOT NULL) ' + 
                   'BEGIN ' +
                   'SELECT CASE WHEN COUNT(*) = 0 THEN ''' 
                   + @ColumnName + ' is all NULL''' + ' ELSE '''' END AS Result FROM ' 
                   + @TableName + ' WHERE ' + QUOTENAME(@ColumnName) + ' IS NOT NULL; ' +
                   'END';
    END

    -- 执行单条SQL查询
    EXEC sp_executesql @SQL;

    -- 继续获取下一个列名
    FETCH NEXT FROM db_cursor INTO @ColumnName, @DataType
END

-- 关闭游标并释放资源
CLOSE db_cursor
DEALLOCATE db_cursor


 
--------- 自查  检查临时表 字段全null，可能就有问题  ---------end
---------------------------------------------------------------

------------ 全自动对比 sql
-- pass
-- 'Status'             -- todo
-- UrgentType           -- reject todo
-- VendorId             
-- City					-- reject
--POApplicationCode     -- reject
--GRApplicationCode	    -- reject
--'EsignPdf' 		    -- reject
-- 'DeliveryMode'       -- reject
--  AmountModifyRemark  -- reject
-- IsBackupInvoice      -- reject
-- Remarks              -- reject
--Attachments           -- reject
-- Invoice              -- reject
-- InvoiceDescription   -- reject
--VendorId              -- reject
--[TaskType]             -- reject
--[EstimatedPaymentDate]  -- reject
--PRId                  -- reject
--[PRId]                -- reject
--[ApprovedDate]        -- reject
--[VendorCode]          -- reject
--[ApprovedUserId]      -- reject
-- CityId               -- reject
--Currency              -- reject
--PlanRate              -- reject
-- 


DECLARE @TableName NVARCHAR(MAX) = '#PurPAApplications_temp'; -- 检查临时表 字段全null，可能就有问题
DECLARE @TagetTableName NVARCHAR(MAX) = 'PurPAApplications'; -- 检查临时表 字段全null，可能就有问题
DECLARE @T1_BaseCLoumns NVARCHAR(MAX) = 't2.id,t1.ProcInstId '; -- 检查临时表 字段全null，可能就有问题
DECLARE @T2_BaseCLoumns NVARCHAR(MAX) = 'a.id,ppt.ProcInstId '; -- 检查临时表 字段全null，可能就有问题
DECLARE @T1_T2_ON NVARCHAR(MAX) = ' t1.ProcInstId = t2.ProcInstId ';  
DECLARE @SQL NVARCHAR(MAX) = '';
DECLARE @ColumnName NVARCHAR(MAX);
DECLARE @DataType NVARCHAR(50);
DECLARE db_cursor CURSOR FOR
SELECT COLUMN_NAME, DATA_TYPE
FROM tempdb.INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME LIKE @TableName + '%' -- 临时表的名称
AND TABLE_SCHEMA = 'dbo' -- 如果使用的是其他模式，请修改为对应的模式
and COLUMN_NAME ='VendorRatingRemark'
OPEN db_cursor
FETCH NEXT FROM db_cursor INTO @ColumnName, @DataType
-- 遍历每个列
WHILE @@FETCH_STATUS = 0
BEGIN
    -- 针对字符串类型，检查是否为空字符串；对于其他类型，检查是否为 NULL
    IF @DataType IN ('nvarchar', 'varchar', 'char', 'text')  -- 字符串类型
    BEGIN
		SET @SQL='
			SELECT TOP 100 '+@T1_BaseCLoumns+',t1.'+quotename(@columnName)+',t2.'+quotename(@columnName)+'
			from
			(
				select '+quotename(@columnName)+',ProcInstId from '+quotename(@TableName)+' 
				where  '+quotename(@columnName)+' is not null 	AND '+QUOTENAME(@ColumnName) + ' <> ''''
			 ) t1
			full join
			(
				select  '+@T2_BaseCLoumns+',a.'+quotename(@columnName)+'
				from '+quotename(@TagetTableName)+' a
				join 
					'+@TagetTableName+'_tmp ppt 
					on a.id =ppt.id
			    where a.'+quotename(@columnName)+' is not null 	AND a.'+QUOTENAME(@ColumnName) + ' <> ''''
			) t2
			     on  '+@T1_T2_ON+'
			WHERE  
			  t1.'+quotename(@columnName)+'<> t2.'+quotename(@columnName)+'
			  OR (t1.'+quotename(@columnName)+' IS NULL AND t2.'+quotename(@columnName)+' IS NOT NULL)
			  OR (t1.'+quotename(@columnName)+' IS NOT NULL AND t2.'+quotename(@columnName)+' IS  NULL)
			';
    END
    ELSE  -- 非字符串类型，仅检查 NULL
    BEGIN
		SET @SQL='
			SELECT TOP 100 '+@T1_BaseCLoumns+',t1.'+quotename(@columnName)+',t2.'+quotename(@columnName)+'
			from
			  	(select '+quotename(@columnName)+',ProcInstId from '+quotename(@TableName)+' where  '+quotename(@columnName)+' is not null ) t1
			full join
			(
				select  '+@T2_BaseCLoumns+',a.'+quotename(@columnName)+'
				from '+quotename(@TagetTableName)+' a
				join 
					'+@TagetTableName+'_tmp ppt 
					on a.id =ppt.id
			    where a.'+quotename(@columnName)+' is not null  
			) t2
			     on  '+@T1_T2_ON+'
			WHERE  
			  t1.'+quotename(@columnName)+'<> t2.'+quotename(@columnName)+'
			  OR (t1.'+quotename(@columnName)+' IS NULL AND t2.'+quotename(@columnName)+' IS NOT NULL)
			  OR (t1.'+quotename(@columnName)+' IS NOT NULL AND t2.'+quotename(@columnName)+' IS  NULL)
			';
    END
	SELECT @SQL;
    -- 继续获取下一个列名
 	-- 执行单条SQL查询
    EXEC sp_executesql @SQL;
    FETCH NEXT FROM db_cursor INTO @ColumnName, @DataType
END
CLOSE db_cursor
DEALLOCATE db_cursor










--SELECT  * from spk_pturttypeconfig
-- SELECT  UrgentPayment from   PurPAApplications



初审完毕
单据接收
单据已收，等待审核
发起人终止
复审完毕
审批中
填写表单
完成
终止
SELECT * from ODS_TMP_GLH
CREATE  index index_LHDREF on ODS_TMP_GLH(LHDREF);

DROP TABLE  #ODS_TMP_GLH;

SELECT *
into 
#ODS_TMP_GLH
from (
select LHDREF,LHREAS,ROW_NUMBER () over(PARTITION by LHDREF  ORDER by LHDATE desc ,LHTIME desc) rn from ODS_TMP_GLH 
)t
where rn=1
;
CREATE  index #index_LHDREF on #ODS_TMP_GLH(LHDREF);
---
drop table #PurPAApplications_status;

SELECT  
	b.ProcInstId,
	b.processStatus,b.serialNumber,	serialNumber1,serialNumber2,h.AMLINV,
	c.status,d.ReturnDate,u.LHREAS as LHREAS_u,u.LHDREF as LHDREF_u,j.LHREAS as LHREAS_j,j.LHDREF as LHDREF_j,
	case 
		when b.processStatus ='填写表单'  and  b.payamount='0'  then 1
		when b.processStatus ='审批中'  then 7 
		when b.processStatus ='填写表单' and  b.payamount<>'0'  then 7
		when b.processStatus in ('发起人终止' ,'终止') then 8
		when b.processStatus in ('单据接收' ,'单据已收，等待审核','初审完毕') then 9
		when b.processStatus in ('复审完毕','完成') and c.status ='V' then 12
		when b.processStatus in ('复审完毕','完成') and c.status <>'V'  and (d.ReturnDate is null or d.ReturnDate ='') then 5
		when b.processStatus in ('复审完毕','完成') and c.status <>'V'  and (d.ReturnDate is not null or d.ReturnDate <>'' ) then 6
		when b.processStatus in ('复审完毕','完成') and    h.AMLINV is not null then 4
		when b.processStatus in ('复审完毕','完成') and    TRIM(u.LHREAS)='APV2L' then 12
		when b.processStatus in ('复审完毕','完成') and    TRIM(j.LHREAS)='APV2L' then 12
		when b.processStatus in ('复审完毕','完成') then 3 
	end status_1
into #PurPAApplications_status
from 
(
	select 	
		b.ProcInstId,
		b.processStatus,
		b.serialNumber,b.payamount,
		case when b.serialNumber like 'A%' then 
			SUBSTRING(b.serialNumber,charindex('A',b.serialNumber)+1,1) end as serialNumber1,
		case when b.serialNumber like 'A%' then 
		SUBSTRING(b.serialNumber,charindex('A',b.serialNumber)+2,10000) end as serialNumber2 
	from ods_Form_e37632eb82f04fbda355cffdac744166 b
) b   

left join 
(
	select *,ROW_NUMBER () over(PARTITION by PAFormCode order by RefNo desc) rn from PLATFORM_ABBOTT.dbo.ODS_T_AP_REF
) c
	on b.serialNumber=c.PAFormCode and c.rn=1--618626 去重RefNo
left join
 (
  SELECT  RefNo,ReturnDate,PaymentDate,ROW_NUMBER () over(PARTITION by RefNo order by PaymentDate desc) rn  from  ods_T_Ebanking_Payment_Info  
 ) d
	on c.RefNo = d.RefNo and  c.rn=1 and c.Status<>'V' and d.rn =1
 left join  
 (
 	select TRIM(AMLINV) AMLINV,ROW_NUMBER () over(PARTITION by AMLINV order by AMLINV desc) rn  from PLATFORM_ABBOTT.dbo.ODS_TMP_AML where AMLINV is not null and AMLINV <>''
 ) h
 on CONCAT(serialNumber2,serialNumber1) = h.AMLINV and h.rn=1
 
 left join 
	#ODS_TMP_GLH u
  on CONCAT(serialNumber2,serialNumber1) = u.LHDREF
left join 
 	#ODS_TMP_GLH j
  on CONCAT(serialNumber1,serialNumber2) = j.LHDREF
 
  ;
 
 CREATE  index #ProcInstId on #PurPAApplications_status(ProcInstId);
-- 
-- SELECT  * FROM #PurPAApplications_status WHERE status_1 is null 


SELECT  t2.id,t1.ProcInstId ,t1.status,t2.status
from
(
	select status_1 as status,ProcInstId from #PurPAApplications_status
 ) t1
full join
(
	select  a.id,ppt.ProcInstId ,a.status
	from [PurPAApplications] a
	join 
		PurPAApplications_tmp ppt 
		on a.id =ppt.id
) t2
     on   t1.ProcInstId = t2.ProcInstId 
WHERE  
  t1.status<> t2.status
  
   