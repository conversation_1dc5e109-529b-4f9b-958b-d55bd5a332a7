-- dbo.View_JoinedTicketApplications source

-- dbo.View_JoinedTicketApplications source

CREATE VIEW View_JoinedTicketApplications AS
SELECT 
    a.Id AS ApplicationId, 
    a.ApplicationCode,
    a.ApplyTime,
    a.ApplyUser,
    a.<PERSON>C<PERSON>,
    a.SubBudgetDesc,
    a.CompanyName,
    a.ApplyUserBuName,
    a.Cityname,
    a.ProductName,
    -- 需要按照字段拼接CompanyCode+BUCode+CCCode+NatureCode+ProductCode+CityCode+currencycode
     --CONCAT(
        --a.CompanyCode, '.', 
        --a.BUCode, '.', 
        --a.Cost<PERSON>Code, '.',  
        --b.ExpenseNatureCode, '.', 
        --a.ProductC<PERSON>, '.', 
        --b.CityCode, '.', 
        --a.CurrencyCode
    ) AS COA,
    a.ClientType,
    a.ClientCode,
    a.ClientName,
    a.CostCenter,
    a.Status,
    a.<PERSON>Region,
    b.Id AS DetailId, 
    b.<PERSON>,
    --b.<PERSON>,
    b.<PERSON>, 
    b.<PERSON>, 
    b.<PERSON>Q<PERSON>,
    b.<PERSON>,
    b.<PERSON>tyType,
    b.SettlementEntityChannel,
    b.SubTotalAmountRMB,
    b.PredictDate,
    b.ExpenseNature,
    b.CityCode,
    b.Remark,
    b.SettlementPeriodStart,
    b.SettlementPeriodEnd
FROM 
    STicketApplications a
LEFT JOIN 
    STicketApplicationDetails b 
ON 
    a.id = b.parentid;