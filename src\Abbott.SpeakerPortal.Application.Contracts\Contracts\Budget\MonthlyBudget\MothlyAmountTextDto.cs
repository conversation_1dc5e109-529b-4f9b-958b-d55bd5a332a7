﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;

namespace Abbott.SpeakerPortal.Contracts.Budget
{
    public class MothlyAmountTextDto : MothlyAmountDto
    {
        /// <summary>
        /// 一月预算
        /// </summary>
        public string JanAmountText { get; set; }
        /// <summary>
        /// 一月状态
        /// </summary>
        public string JanStatusText { get; set; }
        /// <summary>
        /// 二月预算
        /// </summary>
        public string FebAmountText { get; set; }
        /// <summary>
        /// 二月状态
        /// </summary>
        public string FebStatusText { get; set; }
        /// <summary>
        /// 三月预算
        /// </summary>
        public string MarAmountText { get; set; }
        /// <summary>
        /// 三月状态
        /// </summary>
        public string MarStatusText { get; set; }
        /// <summary>
        /// 四月预算
        /// </summary>
        public string AprAmountText { get; set; }
        /// <summary>
        /// 四月状态
        /// </summary>
        public string AprStatusText { get; set; }
        /// <summary>
        /// 五月预算
        /// </summary>
        public string MayAmountText { get; set; }
        /// <summary>
        /// 五月状态
        /// </summary>
        public string MayStatusText { get; set; }
        /// <summary>
        /// 六月预算
        /// </summary>
        public string JunAmountText { get; set; }
        /// <summary>
        ///六月状态
        /// </summary>
        public string JunStatusText { get; set; }
        /// <summary>
        /// 七月预算
        /// </summary>
        public string JulAmountText { get; set; }
        /// <summary>
        /// 七月状态
        /// </summary>
        public string JulStatusText { get; set; }
        /// <summary>
        /// 八月预算
        /// </summary>
        public string AugAmountText { get; set; }
        /// <summary>
        /// 八月状态
        /// </summary>
        public string AugStatusText { get; set; }
        /// <summary>
        /// 九月预算
        /// </summary>
        public string SeptAmountText { get; set; }
        /// <summary>
        /// 九月状态
        /// </summary>
        public string SeptStatusText { get; set; }
        /// <summary>
        /// 十月预算
        /// </summary>
        public string OctAmountText { get; set; }
        /// <summary>
        /// 十月状态
        /// </summary>
        public string OctStatusText { get; set; }
        /// <summary>
        /// 十一月预算
        /// </summary>
        public string NovAmountText { get; set; }
        /// <summary>
        /// 十一月状态
        /// </summary>
        public string NovStatusText { get; set; }
        /// <summary>
        /// 十二月预算
        /// </summary>
        public string DecAmountText { get; set; }
        /// <summary>
        /// 十二月状态
        /// </summary>
        public string DecStatusText { get; set; }
        /// <summary>
        /// 总金额
        /// </summary>
        public decimal TotalAmount { get; private set; }
        /// <summary>
        /// 验证
        /// </summary>
        public (bool, string) VerificationCreate()
        {
            Type type = this.GetType();
            string[] months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sept", "Oct", "Nov", "Dec"];
            string message = string.Empty;
            int i = 1;
            string[] status = ["是", "否"];
            foreach (var item in months)
            {
                var amountProperty = type.GetProperty($"{item}AmountText");
                var amountValue = amountProperty.GetValue(this, null).ToString();
                if (string.IsNullOrEmpty(amountValue))
                    type.GetProperty($"{item}Amount").SetValue(this, 0m, null);
                else if (decimal.TryParse(amountValue, out decimal amount))
                {
                    if (amount < 0) message += $"{i}月金额必须大于0;";
                    else
                    {
                        type.GetProperty($"{item}Amount").SetValue(this, Math.Round(amount, 2), null);
                        type.GetProperty($"{item}AmountText").SetValue(this, amount.ToString("F2"), null);
                        TotalAmount += amount;
                    }
                }
                else message += $"{i}月金额不是数字;";

                var statusProperty = type.GetProperty($"{item}StatusText");
                var statusValue = statusProperty.GetValue(this, null).ToString();
                if (string.IsNullOrEmpty(statusValue))
                {
                    type.GetProperty($"{item}Status").SetValue(this, false, null);
                    statusProperty.SetValue(this, "否", null);
                }
                else if (status.Contains(statusValue))
                {
                    bool value = statusValue == "是";
                    type.GetProperty($"{item}Status").SetValue(this, value, null);
                }
                else message += $"{i}月状态只能填写是否;";
                i++;
            }
            return (!string.IsNullOrEmpty(message), message);
        }
        //批量调整
        public (bool, string) VerificationAdjustment()
        {
            Type type = this.GetType();
            string[] months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sept", "Oct", "Nov", "Dec"];
            string message = string.Empty;
            int i = 1;
            string[] status = ["是", "否"];
            foreach (var item in months)
            {
                var amountProperty = type.GetProperty($"{item}AmountText");
                var amountValue = amountProperty.GetValue(this, null).ToString();
                if (string.IsNullOrEmpty(amountValue)) { }
                else if (decimal.TryParse(amountValue, out decimal amount))
                {
                    type.GetProperty($"{item}Amount").SetValue(this, Math.Round(amount, 2), null);
                    type.GetProperty($"{item}AmountText").SetValue(this, amount.ToString("F2"), null);
                    TotalAmount += amount;
                }
                else message += $"{i}月金额不是数字;";

                var statusProperty = type.GetProperty($"{item}StatusText");
                var statusValue = statusProperty.GetValue(this, null).ToString();
                if (string.IsNullOrEmpty(statusValue)) { }
                else if (status.Contains(statusValue))
                {
                    bool value = statusValue == "是";
                    type.GetProperty($"{item}Status").SetValue(this, value, null);
                }
                else message += $"{i}月状态只能填写是否;";
                i++;
            }
            return (!string.IsNullOrEmpty(message), message);
        }
    }
    public class MothlyAmountDto
    {
        /// <summary>
        /// 一月预算
        /// </summary>
        public decimal? JanAmount { get; set; }
        /// <summary>
        /// 一月状态
        /// </summary>
        public bool? JanStatus { get; set; }
        /// <summary>
        /// 二月预算
        /// </summary>
        public decimal? FebAmount { get; set; }
        /// <summary>
        /// 二月状态
        /// </summary>
        public bool? FebStatus { get; set; }
        /// <summary>
        /// 三月预算
        /// </summary>
        public decimal? MarAmount { get; set; }
        /// <summary>
        /// 三月状态
        /// </summary>
        public bool? MarStatus { get; set; }
        /// <summary>
        /// 四月预算
        /// </summary>
        public decimal? AprAmount { get; set; }
        /// <summary>
        /// 四月状态
        /// </summary>
        public bool? AprStatus { get; set; }
        /// <summary>
        /// 五月预算
        /// </summary>
        public decimal? MayAmount { get; set; }
        /// <summary>
        /// 五月状态
        /// </summary>
        public bool? MayStatus { get; set; }
        /// <summary>
        /// 六月预算
        /// </summary>
        public decimal? JunAmount { get; set; }
        /// <summary>
        ///六月状态
        /// </summary>
        public bool? JunStatus { get; set; }
        /// <summary>
        /// 七月预算
        /// </summary>
        public decimal? JulAmount { get; set; }
        /// <summary>
        /// 七月状态
        /// </summary>
        public bool? JulStatus { get; set; }
        /// <summary>
        /// 八月预算
        /// </summary>
        public decimal? AugAmount { get; set; }
        /// <summary>
        /// 八月状态
        /// </summary>
        public bool? AugStatus { get; set; }
        /// <summary>
        /// 九月预算
        /// </summary>
        public decimal? SeptAmount { get; set; }
        /// <summary>
        /// 九月状态
        /// </summary>
        public bool? SeptStatus { get; set; }
        /// <summary>
        /// 十月预算
        /// </summary>
        public decimal? OctAmount { get; set; }
        /// <summary>
        /// 十月状态
        /// </summary>
        public bool? OctStatus { get; set; }
        /// <summary>
        /// 十一月预算
        /// </summary>
        public decimal? NovAmount { get; set; }
        /// <summary>
        /// 十一月状态
        /// </summary>
        public bool? NovStatus { get; set; }
        /// <summary>
        /// 十二月预算
        /// </summary>
        public decimal? DecAmount { get; set; }
        /// <summary>
        /// 十二月状态
        /// </summary>
        public bool? DecStatus { get; set; }
    }
}
