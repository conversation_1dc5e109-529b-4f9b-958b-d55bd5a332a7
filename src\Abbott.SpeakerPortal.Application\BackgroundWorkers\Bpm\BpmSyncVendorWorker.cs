﻿using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Bpm;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.Common;
using Abbott.SpeakerPortal.Entities.BPMEmail;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;

using DocumentFormat.OpenXml.Drawing.Charts;

using Hangfire;

using Medallion.Threading;

using Microsoft.Extensions.Configuration;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

using Volo.Abp.DistributedLocking;

namespace Abbott.SpeakerPortal.BackgroundWorkers.Bpm
{
    /// <summary>
    /// 同步vendor数据至Bpm
    /// </summary>
    public class BpmSyncVendorWorker : SpeakerPortalBackgroundWorkerBase
    {
        Guid _vendorApplicationId;

        public BpmSyncVendorWorker()
        {
            CronExpression = Cron.Never();
        }

        public override async Task DoWorkAsync(CancellationToken cancellationToken = default)
        {
            await LazyServiceProvider.LazyGetService<IBpmService>().SyncVendorToBpm(_vendorApplicationId);
        }

        public async Task DoWorkAsync(Guid vendorApplicationId)
        {
            _vendorApplicationId = vendorApplicationId;
            await DoWorkAsync(CancellationToken.None);
        }
    }
}
