CREATE PROCEDURE dbo.sp_PurPAReturnReasonInfos_ns
AS 
BEGIN
	select * 
into #WorkflowTasks 
from  PLATFORM_ABBOTT.dbo.WorkflowTasks b
where  b.FormName='PAApplication';

with T_Return_Reject_Info as (
select *,ROW_NUMBER () over(PARTITION by [Serialnumber],[OperateTime],[M&E] order by [Serialnumber],[OperateTime],[M&E]) rn  from PLATFORM_ABBOTT.dbo.ods_T_Return_Reject_Info a
where a.[procname]='Abbott.Process\PaymentApplication' and a.[returnlisttype] is not null
)
select 
newid() AS [Id],--
a.Serial<PERSON>umber,
b.id AS [PurPAApplicationId],--基于07-1迁移的申请单主信息，以该单号定位对应的[PurPAApplications].[ID]
case when [Operate]=N'退回申请人' then 1 
when [Operate]=N'退回补件' then 2
when [Operate]=N'退回原件' then 3 end AS [Node],--退回申请人-1
--退回补件-2
--退回原件-3"
case when [M&E]='Non-M&E Return List'  then '923180001'
when [M&E]='M&E Return List'  then '923180000' end AS [Type],--M&E Return List-923180000
--Non-M&E Return List-923180001"
[Reason] AS [Remark],--同一组合下多条数据的该信息一定一致，随机取一条即可
'{}' AS [ExtraProperties],--默认填写为"{}"
'' AS [ConcurrencyStamp],--?
[OperateTime] AS [CreationTime],--
ss.spk_NexBPMCode AS [CreatorId],--以该ID匹配至员工主数据
null AS [LastModificationTime],--默认为空
null AS [LastModifierId],--默认为空
0 AS [IsDeleted],--默认为0
null AS [DeleterId],--默认为空
null AS [DeletionTime],--默认为空
c.WF_ID AS [WorkFlowTaskId]--基于[Serialnumber]+[OperateTime]查询到对应的[WorkFlowTasks]记录，将匹回的ID填入
into #PurPAReturnReasonInfos
from T_Return_Reject_Info a
left join PLATFORM_ABBOTT.dbo.PurPAApplications_tmp b on a.SerialNumber=b.ApplicationCode
left join
(select b.CreationTime,ppt.id PA_ID,b.id WF_ID,PPT.ApplicationCode from
#WorkflowTasks b
join PLATFORM_ABBOTT.dbo.PurPAApplications_tmp ppt 
on b.[_ProcInstID] = ppt.ProcInstId)c
on a.SerialNumber=c.ApplicationCode and a.OperateTime=c.CreationTime
left join spk_staffmasterdata ss 
on a.OperatorEmpId=ss.bpm_id
where a.rn=1

--drop table #PurPAReturnReasonInfos



    --删除表
    IF OBJECT_ID(N'dbo.PurPAReturnReasonInfos ', N'U') IS NOT NULL
	BEGIN
		update a 
	set 
        a.Id              		 =b.Id
        ,a.SerialNumber 		  = b.SerialNumber
		,a.PurPAApplicationId     =b.PurPAApplicationId
		,a.Node                   =b.Node
		,a.[Type]                 =b.[Type]
		,a.Remark                 =b.Remark
		,a.ExtraProperties        =b.ExtraProperties
		,a.ConcurrencyStamp       =b.ConcurrencyStamp
		,a.CreationTime           =b.CreationTime
		,a.CreatorId              =b.CreatorId
		,a.LastModificationTime   =b.LastModificationTime
		,a.LastModifierId         =b.LastModifierId
		,a.IsDeleted              =b.IsDeleted
		,a.DeleterId              =b.DeleterId
		,a.DeletionTime           =b.DeletionTime
		,a.WorkFlowTaskId         =b.WorkFlowTaskId
     from PLATFORM_ABBOTT.dbo.PurPAReturnReasonInfos a 
     left join #PurPAReturnReasonInfos b 
     on a.SerialNumber = b.SerialNumber  and a.CreationTime = b.CreationTime and isnull(a.[Type],'null') = isnull(b.[Type],'null')
     
     insert into PLATFORM_ABBOTT.dbo.PurPAReturnReasonInfos
     select 
     a.Id
     ,a.SerialNumber
	 ,a.PurPAApplicationId
	 ,a.Node
	 ,a.[Type]
	 ,a.Remark
	 ,a.ExtraProperties
	 ,a.ConcurrencyStamp
	 ,a.CreationTime
	 ,a.CreatorId
	 ,a.LastModificationTime
	 ,a.LastModifierId
	 ,a.IsDeleted
	 ,a.DeleterId
	 ,a.DeletionTime
	 ,a.WorkFlowTaskId
     from #PurPAReturnReasonInfos a
     where not exists (
     select * from PLATFORM_ABBOTT.dbo.PurPAReturnReasonInfos b
     where a.SerialNumber = b.SerialNumber  and a.CreationTime = b.CreationTime and isnull(a.[Type],'null') = isnull(b.[Type],'null'))
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
   select * into  PLATFORM_ABBOTT.dbo.PurPAReturnReasonInfos
		from #PurPAReturnReasonInfos
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	
	
--	drop table PurPAReturnReasonInfos

END
