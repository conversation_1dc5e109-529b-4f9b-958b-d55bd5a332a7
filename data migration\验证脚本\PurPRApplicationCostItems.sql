--原表数据量:555228
select count(1) from PLATFORM_ABBOTT_Stg.dbo.[ods_AUTO_BIZ_T_ProcurementApplication_Info]; 
--中间层数据量：557229
select count(1) from PLATFORM_ABBOTT_Stg.dbo.[PurPRApplicationCostItems];
--目标层数据量:557233
select count(1) from Speaker_Portal_Stg.dbo.[PurPRApplicationCostItems]; 
--主表去重数据量:555197
select count(1) from(
select  ROW_NUMBER() over( PARTITION by ProcInstId  order by expenseCategory_Text desc) as a,* 
	from PLATFORM_ABBOTT_Stg.dbo.ODS_AUTO_BIZ_T_ProcurementApplication_Info
	) a where a=1--555197
--数据差别:XML中一条数据对应多条值，属于正常情况，如：ProcInstId='1452804'
select count(1)　from #ABTPI a
left join PLATFORM_ABBOTT_Stg.dbo.PurPRApplications_tmp t
on A.ProcInstID=t.ProcInstID --555197
left join 
(select 
 ProcInstId
,Project
,sum(cast(ApproximateCost as decimal)) as ApproximateCost
from PLATFORM_ABBOTT_Stg.dbo.xml_1
group by 
 ProcInstId
,Project) c
on a.ProcInstId=c.ProcInstId--557229
select ProcInstId,count(1) from xml_1 group by ProcInstId ;
select * from PLATFORM_ABBOTT_Stg.dbo.xml_1 where ProcInstId='1452804';
select * from PLATFORM_ABBOTT_Stg.dbo.PurPRApplicationCostItems_tmp ppcit   where ProcInstId='1452804';
select * from Speaker_Portal_Stg.dbo.[PurPRApplicationCostItems] where id in (
'4578DA8D-2506-438B-BCDD-764F556C6EB4'
,'E8D3262D-CA66-4722-B9A0-B2D0F3AFAA81'
,'1C88E2AD-5229-44AB-8E0D-B04D6C92E298'
,'BA89A6E2-5D7E-4F35-826F-656D85F012E1'
,'DC981ACC-9F01-4D94-9893-6705515518A8'
,'793622EC-E99B-4E0B-BF50-ABAE6AA15A0B'
,'627E517B-BF17-4179-A2F2-0906E71C2866'
,'65A0C32D-5EAA-4745-886A-B3F410BC912C');
--字段对比：取值源于PurPRApplications 
select a.CreatorId,b.CreatorId,a.CreationTime,b.CreationTime from Speaker_Portal_Stg.dbo.[PurPRApplicationCostItems] a 
join Speaker_Portal_Stg.dbo.PurPRApplications b on a.PRApplicationId=b.id --and a.CreatorId=b.CreatorId --and a.CreationTime=b.CreationTime; --557233 时间字段，创建人字段需要排查