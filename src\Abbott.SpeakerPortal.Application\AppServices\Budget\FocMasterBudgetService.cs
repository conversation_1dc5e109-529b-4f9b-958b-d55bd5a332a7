﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Budget;
using Abbott.SpeakerPortal.Entities.Budget;
using Abbott.SpeakerPortal.Entities.EFlow.FOC;
using Abbott.SpeakerPortal.Enums;
using Volo.Abp.DistributedLocking;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using System.Collections.Generic;
using Abbott.SpeakerPortal.Dataverse;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Application.Dtos;
using Abbott.SpeakerPortal.Extension;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Contracts.Budget.Subbudget;
using static Abbott.SpeakerPortal.Enums.DataverseEnums.Organization;
using Abbott.SpeakerPortal.Contracts.Budget.FocMasterBudget;
using Abbott.SpeakerPortal.Entities.EFlow.STicket;

namespace Abbott.SpeakerPortal.AppServices.Budget
{
    public class FocMasterBudgetService : SpeakerPortalAppService, IFocMasterBudgetService
    {
        /// <summary>
        /// 最大预算数量
        /// </summary>
        const int MaxQty = 999999999;

        /// <summary>
        /// 创建FOC主预算
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        public async Task CreateFocMasterBudget(CreateFocBudgetRequestDto requestDto)
        {
            var budgetRespository = LazyServiceProvider.LazyGetService<IFocMasterBudgetRepository>();
            var budget = ObjectMapper.Map<CreateFocBudgetRequestDto, FocMasterBudget>(requestDto);
            budget.Status = true;

            var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            string historyContent = string.Empty;
            historyContent += $"预算数量：{requestDto.BudgetQty}；";
            var ownerName = queryableUser.First(m => m.Id == requestDto.OwnerId).Name;
            historyContent += $"预算负责人：{ownerName}；";
            historyContent += $"描述：{requestDto.Description}；";
            FocBudgetHistory bdHistory = new();
            bdHistory.OperateType = OperateType.Create;
            bdHistory.OperateContent = historyContent;
            bdHistory.OperatorId = CurrentUser.Id.Value;
            bdHistory.OperatorName = CurrentUser.Name;
            bdHistory.OperateQty = requestDto.BudgetQty;
            bdHistory.OperatingTime = DateTime.Now;
            bdHistory.BudgetType = BudgetType.FocMasterBudget;

            bdHistory.BudgetId = await GenerateCode(budgetRespository, budget);
            //保存
            await SaveHistory([bdHistory]);
        }

        /// <summary>
        /// 获取FOC主预算列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetFocBudgetListResponseDto>> GetFocMasterBudgetListAsync(GetFocBudgetListRequestDto request, bool IsPage = true)
        {
            var queryBudget = await LazyServiceProvider.LazyGetService<IFocMasterBudgetRepository>().GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var queryAuthorizedBudgetBu = await LazyServiceProvider.LazyGetService<IBdAuthorizedBudgetBuRepository>().GetQueryableAsync();
            var BuIds = queryAuthorizedBudgetBu.Where(s => s.UserId == CurrentUser.Id).Select(s => s.BuId).ToList();
            var getBuTask = await dataverseService.GetDivisions(null);
            var query = queryBudget.Include(x => x.SubBudgets)
                .Where(m => BuIds.Contains(m.BuId))
                .WhereIf(!string.IsNullOrWhiteSpace(request.Code), m => m.Code.Contains(request.Code))
                .WhereIf(request.Year.HasValue, m => m.Year == request.Year)
                .WhereIf(request.BuId.HasValue, m => m.BuId == request.BuId)
                .WhereIf(!string.IsNullOrWhiteSpace(request.Description), m => m.Description.Contains(request.Description))
                .WhereIf(request.OwnerId.HasValue, m => m.OwnerId == request.OwnerId)
                .WhereIf(request.Status.HasValue, m => m.Status == request.Status)
                .GroupJoin(queryableUser, a => a.OwnerId, b => b.Id, (a, b) => new { budget = a, owner = b })
                 .SelectMany(a => a.owner.DefaultIfEmpty(), (a, b) => new
                 {
                     a.budget,
                     ownerName = b.Name,
                     OwnerEmail = b.Email,
                     //subBudgetAmount = a.budget.SubBudgets.Sum(s => s.AvailableAmount),
                     isDelete = a.budget.SubBudgets.Count() == 0,
                     subQty = a.budget.SubBudgets.Sum(s => s.BudgetQty)
                 });
            //总数
            var count = await query.CountAsync();
            //分页
            query = query.OrderByDescending(m => m.budget.CreationTime);
            query = query.PagingIf(request, IsPage);
            var datas = query.ToList().Select(a => new GetFocBudgetListResponseDto
            {
                Id = a.budget.Id,
                Code = a.budget.Code,
                BuId = a.budget.BuId,
                BuName = getBuTask.FirstOrDefault(a1 => a1.Id == a.budget.BuId)?.DepartmentName ?? "",
                OwnerName = a.ownerName,
                OwnerEmail = a.OwnerEmail,
                OwnerId = a.budget.OwnerId,
                Description = a.budget.Description,
                BudgetQty = a.budget.BudgetQty,
                Status = a.budget.Status,
                SubBudgetQty = a.subQty,
                Year = a.budget.Year,
                isDelete = a.isDelete
            }).ToList();
            var result = new PagedResultDto<GetFocBudgetListResponseDto>(count, datas);
            return result;
        }

        /// <summary>
        /// 删除FOC主预算
        /// </summary>
        /// <param name="Ids"></param>
        /// <returns></returns>
        public async Task<MessageResult> DeleteFocMasterBudgetByIdsAsync(List<Guid> Ids)
        {
            var queryBudgetRepository = LazyServiceProvider.LazyGetService<IFocMasterBudgetRepository>();
            var queryBudget = await queryBudgetRepository.GetQueryableAsync();
            var query = queryBudget.Include(x => x.SubBudgets).Where(m => Ids.Contains(m.Id));
            var exsitSubBudget = query.Where(m => m.SubBudgets.Count > 0);
            var exsitSubCount = await exsitSubBudget.CountAsync();
            if (exsitSubCount > 0)
            {
                var mastrCodes = exsitSubBudget.Select(s => s.Code).JoinAsString(",");
                return MessageResult.FailureResult($"{mastrCodes}子预算不为空，不能删除主预算");
            }
            await queryBudgetRepository.DeleteManyAsync(query);
            return MessageResult.SuccessResult("删除成功");
        }

        /// <summary>
        /// 冻结预算
        /// </summary>
        /// <param name="Ids"></param>
        /// <param name="Status"></param>
        /// <returns></returns>
        public async Task<MessageResult> UpdateFocBudgetStatusAsync(UpdateBudgetStatusRequestDto requestDto)
        {
            var queryBudgetRepository = LazyServiceProvider.LazyGetService<IFocMasterBudgetRepository>();
            var queryBudget = await queryBudgetRepository.GetQueryableAsync();
            var querySubBudgetRepository = LazyServiceProvider.LazyGetService<IFocSubBudgetRepository>();
            var querySubBudget = await querySubBudgetRepository.GetQueryableAsync();
            var querymas = queryBudget.Where(m => requestDto.Ids.Contains(m.Id));
            //判断是否存在与变更预算状态一致的状态
            var sameBudgetCount = await querymas.Where(m => m.Status == requestDto.Status).CountAsync();
            if (sameBudgetCount > 0)
                return MessageResult.FailureResult("请选择与操作保持一致的预算");
            //更改主预算状态
            foreach (var item in querymas)
            {
                item.Status = requestDto.Status;
            }
            //冻结操作更新所有子预算

            var subquery = querySubBudget.Where(m => requestDto.Ids.Contains(m.MasterBudgetId) && m.Status == !requestDto.Status);
            var bdHistorys = new List<FocBudgetHistory>();
            string hiscontent = requestDto.Status ? "是" : "否";
            foreach (var item in subquery)
            {
                item.Status = requestDto.Status;
                FocBudgetHistory bdHistory = new();
                bdHistory.OperateType = requestDto.Status ? OperateType.Enable : OperateType.Disable;
                bdHistory.OperateContent = $"是否启用：{hiscontent}";
                bdHistory.BudgetId = item.Id;
                bdHistory.OperatorId = CurrentUser.Id.Value;
                bdHistory.OperatorName = CurrentUser.Name;
                bdHistory.OperatingTime = DateTime.Now;
                bdHistory.BudgetType = BudgetType.FocSubBudget;
                bdHistorys.Add(bdHistory);
            }
            await querySubBudgetRepository.UpdateManyAsync(subquery);
            await queryBudgetRepository.UpdateManyAsync(querymas);
            foreach (var item in requestDto.Ids)
            {
                FocBudgetHistory bdHistory = new();
                bdHistory.OperateType = requestDto.Status ? OperateType.Enable : OperateType.Disable;
                bdHistory.OperateContent = $"是否启用：{hiscontent}";
                bdHistory.BudgetId = item;
                bdHistory.OperatorId = CurrentUser.Id.Value;
                bdHistory.OperatorName = CurrentUser.Name;
                bdHistory.OperatingTime = DateTime.Now;
                bdHistory.BudgetType = BudgetType.FocMasterBudget;
                bdHistorys.Add(bdHistory);
            }
            //保存
            await SaveHistory(bdHistorys);
            return MessageResult.SuccessResult("操作成功");
        }

        /// <summary>
        /// 调整预算数量
        /// </summary>
        /// <returns></returns>
        public async Task<MessageResult> AdjustmentBudgetQtyAsync(AdjustBudgetQtyRequestDto requestDto)
        {
            var queryBudgetRepository = LazyServiceProvider.LazyGetService<IFocMasterBudgetRepository>();
            var queryBudget = await queryBudgetRepository.GetQueryableAsync();
            var masterBudget = await queryBudget.Include(x => x.SubBudgets).FirstOrDefaultAsync(m => m.Id == requestDto.Id);
            var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            //子预算数量
            var subQty = masterBudget.SubBudgets.Sum(a => a.BudgetQty);
            var adjustAfterQty = masterBudget.BudgetQty + requestDto.AdjustQty;
            if (subQty > adjustAfterQty)
            {
                return MessageResult.FailureResult("主预算数量不得小于子预算数量");
            }
            if (adjustAfterQty > MaxQty) return MessageResult.FailureResult("调整数量超出数值范围");
            masterBudget.BudgetQty = adjustAfterQty;
            masterBudget.OwnerId = requestDto.OwnerId;
            //masterBudget.Remark = amountRequestDto.Remark;
            masterBudget.Description = requestDto.Description;
            var ownerName = queryableUser.First(m => m.Id == requestDto.OwnerId).Name;
            //历史记录
            FocBudgetHistory bdHistory = new();
            bdHistory.OperateType = OperateType.Update;
            bdHistory.OperateContent = $"调整数量：{requestDto.AdjustQty}；预算负责人：{ownerName}；描述：{requestDto.Description}";
            bdHistory.BudgetId = requestDto.Id;
            bdHistory.OperatorId = CurrentUser.Id.Value;
            bdHistory.OperatorName = CurrentUser.Name;
            bdHistory.OperatingTime = DateTime.Now;
            bdHistory.OperateQty = requestDto.AdjustQty;
            bdHistory.Remark = requestDto.Remark;
            bdHistory.BudgetType = BudgetType.FocMasterBudget;
            await queryBudgetRepository.UpdateAsync(masterBudget);
            await SaveHistory([bdHistory]);
            return MessageResult.SuccessResult("操作成功");
        }

        /// <summary>
        /// 获取同bu下的预算信息
        /// </summary>
        /// <param name="BuId"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<FocBudgetResponseDto>> GetFocBudgetListAsync(TransferRequestDto transfer)
        {
            var queryBudgetRepository = LazyServiceProvider.LazyGetService<IFocMasterBudgetRepository>();
            var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var queryBudget = await queryBudgetRepository.GetQueryableAsync();
            var datas = queryBudget.Where(b => b.BuId == transfer.BuId && b.Status && transfer.Id != b.Id).WhereIf(!string.IsNullOrWhiteSpace(transfer.Code), m => m.Code.Contains(transfer.Code))
                .GroupJoin(queryableUser, a => a.OwnerId, b => b.Id, (a, b) => new { a, b })
                .SelectMany(a => a.b.DefaultIfEmpty(), (a, b) => new { a.a, b })
                .Select(s => new FocBudgetResponseDto
                {
                    Id = s.a.Id,
                    Code = s.a.Code,
                    Description = s.a.Description,
                    OwnerId = s.a.OwnerId,
                    OwnerName = s.b.Name ?? "",
                    BudgetQty = s.a.BudgetQty,

                })
                .WhereIf(!string.IsNullOrWhiteSpace(transfer.OwnerName), m => m.OwnerName.Contains(transfer.OwnerName))
                .WhereIf(!string.IsNullOrWhiteSpace(transfer.Description), m => m.Description.Contains(transfer.Description));
            //总数
            var count = await datas.CountAsync();
            //分页
            datas = datas.Skip(transfer.PageIndex * transfer.PageSize).Take(transfer.PageSize);
            var result = new PagedResultDto<FocBudgetResponseDto>(count, datas.ToArray());
            return result;
        }

        /// <summary>
        /// 根据Id获取预算信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public async Task<BudgetQtyResponseDto> GetBudgetQtyAsync(Guid Id)
        {
            var queryBudgetRepository = LazyServiceProvider.LazyGetService<IFocMasterBudgetRepository>();
            var queryBudget = await queryBudgetRepository.GetQueryableAsync();
            var data = await queryBudget.FirstAsync(b => b.Id == Id);
            BudgetQtyResponseDto budgetQty = new() { Id = data.Id, Code = data.Code, BudgetQty = data.BudgetQty };
            return budgetQty;
        }

        /// <summary>
        /// 获取详情记录
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public async Task<GetFocBudgetListResponseDto> GetFocMasterBudgetRecordByIdAsync(Guid Id)
        {
            var queryBudget = await LazyServiceProvider.LazyGetService<IFocMasterBudgetRepository>().GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var query = await queryBudget.Include(x => x.SubBudgets).FirstAsync(m => m.Id == Id);
            var getBuTask = await dataverseService.GetDivisions();
            var SubBudgetQty = query.SubBudgets.Sum(a => a.BudgetQty);
            GetFocBudgetListResponseDto responseDto = new()
            {
                Id = query.Id,
                Code = query.Code,
                BuId = query.BuId,
                BuName = getBuTask.FirstOrDefault(a1 => a1.Id == query.BuId)?.DepartmentName ?? "",
                OwnerName = queryableUser.FirstOrDefault(m => m.Id == query.OwnerId)?.Name ?? "",
                OwnerId = query.OwnerId,
                Description = query.Description,
                BudgetQty = query.BudgetQty,
                Status = query.Status,
                SubBudgetQty = SubBudgetQty,
                Year = query.Year,
                Remark = query.Remark,
            };
            return responseDto;
        }

        /// <summary>
        /// 获取所有主数据
        /// </summary>
        /// <returns></returns>
        public async Task<IList<AllFocBudgetResponseDto>> GetAllFocBudgetListAsync(string Code)
        {
            var queryBudgetRepository = LazyServiceProvider.LazyGetService<IFocMasterBudgetRepository>();
            var queryBudget = await queryBudgetRepository.GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var getBuTask = await dataverseService.GetDivisions();

            var datas = queryBudget.Where(b => b.Status)
                .WhereIf(!string.IsNullOrWhiteSpace(Code), m => m.Code.Contains(Code)).Take(20).ToList()
                .Select(s => new AllFocBudgetResponseDto
                {
                    Id = s.Id,
                    Code = s.Code,
                    BuId = s.BuId,
                    BuName = getBuTask.FirstOrDefault(a1 => a1.Id == s.BuId)?.DepartmentName ?? "",
                }).ToArray();
            return datas;
        }

        /// <summary>
        /// 解析批量上传excel
        /// </summary>
        /// <returns></returns>
        /// <param name="excelDtos"></param>
        public async Task<MessageResult> AnalyzeCreateFocMasterBudgetExcelAsync(IEnumerable<CreateFocMasterBudgetExcelDto> excelDtos)
        {
            var queryableUser = (await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync()).AsNoTracking();
            var userEmails = excelDtos.Select(s => s.OwnerEmail).Where(m => !string.IsNullOrWhiteSpace(m)).ToList();
            var users = await queryableUser.Where(m => userEmails.Contains(m.Email)).ToDictionaryAsync(d => d.Email, v => new { v.Name, v.Id });
            var queryAuthorizedBudgetBu = await LazyServiceProvider.LazyGetService<IBdAuthorizedBudgetBuRepository>().GetQueryableAsync();
            var BuIds = queryAuthorizedBudgetBu.Where(s => s.UserId == CurrentUser.Id).Select(s => s.BuId).ToList();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var orgs = (await dataverseService.GetOrganizations()).Where(x => x.OrganizationType == OrganizationType.Bu).ToDictionary(k => k.DepartmentName, v => new { v.Id, v.DepartmentName });
            List<AnalyzeFocBudgetExcelResponseDto> success = new();
            List<AnalyzeFocBudgetExcelResponseDto> error = new();
            string[] capotal = ["是", "否"];
            int i = 0;
            var rowNo = 5;
            foreach (var item in excelDtos)
            {
                AnalyzeFocBudgetExcelResponseDto analyze = new();
                //analyze.No = ++i;
                analyze.No = ++rowNo;
                var message = string.Empty;
                if (string.IsNullOrWhiteSpace(item.YearText)) message += "请填写年度;";
                else if (int.TryParse(item.YearText, out int year))
                {
                    if (item.YearText.Length != 4) message += "请填写正确的年度";
                    else analyze.Year = year;
                }
                else message += "请填写正确的年度";

                if (string.IsNullOrWhiteSpace(item.Bu)) message += "请填写BU;";
                else if (orgs.TryGetValue(item.Bu, out var Bu))
                {
                    analyze.BuId = Bu.Id;
                    if (BuIds.IndexOf(Bu.Id) == -1) message += "请填写正确的授权BU;";
                }
                else message += "BU不存在;";
                if (string.IsNullOrWhiteSpace(item.Description)) message += "请填写描述;";

                if (string.IsNullOrWhiteSpace(item.OwnerEmail)) message += "请填写负责人邮箱;";
                else if (users.TryGetValue(item.OwnerEmail, out var user)) { analyze.OwnerId = user.Id; analyze.OwnerName = user.Name; }
                else message += "主预算负责人不存在;";

                if (string.IsNullOrWhiteSpace(item.BudgetQtyText)) message += "请填写预算数量;";
                else if (int.TryParse(item.BudgetQtyText, out int qty))
                {
                    if (qty > MaxQty) message += "超出预算填写范围;";
                    else if (qty < 0) message += "预算数量不能小于0;";
                    else analyze.BudgetQty = qty;
                }
                else message += "预算数量请填写数字;";


                analyze.YearText = item.YearText;
                analyze.Bu = item.Bu;
                analyze.Description = item.Description;
                analyze.OwnerEmail = item.OwnerEmail;
                analyze.BudgetQtyText = item.BudgetQtyText;
                analyze.Remark = item.Remark;


                if (string.IsNullOrEmpty(message)) success.Add(analyze);
                else
                {
                    analyze.Message = message;
                    error.Add(analyze);
                }

            }
            if (error.Count() > 0)
                return MessageResult.SuccessResult(new ImportDataResponseDto<AnalyzeFocBudgetExcelResponseDto>(error, false));
            return MessageResult.SuccessResult(new ImportDataResponseDto<AnalyzeFocBudgetExcelResponseDto>(success, true));
        }

        /// <summary>
        /// 批量新增主预算
        /// </summary>
        /// <returns></returns>
        /// <param name="request"></param>
        public async Task<MessageResult> CreatesFocMasterBudgetAsync(ImportDataResponseDto<AnalyzeFocBudgetExcelResponseDto> request)
        {
            var BudgetRepository = LazyServiceProvider.LazyGetService<IFocMasterBudgetRepository>();
            //int count;
            //var now = DateTimeOffset.Now;
            //using (DataFilter.Disable<ISoftDelete>())
            //{
            //    count = await BudgetRepository.CountAsync(a => a.CreationTime.Date.Year == now.Year);
            //}
            List<FocMasterBudget> budgets = new();
            List<FocBudgetHistory> historys = new();
            foreach (var item in request.Datas)
            {
                Guid Id = Guid.NewGuid();
                FocMasterBudget bdMaster = new()
                {
                    //Code = $"MB{now:yy}-{++count:D4}",
                    BuId = item.BuId.Value,
                    Description = item.Description,
                    OwnerId = item.OwnerId.Value,
                    BudgetQty = item.BudgetQty.Value,
                    Status = true,
                    Remark = item.Remark,
                    Year = item.Year,
                };
                bdMaster.SetId(Id);
                budgets.Add(bdMaster);
                //添加历史记录
                string historyContent = string.Empty;
                historyContent += $"预算数量：{item.BudgetQty}；";
                historyContent += $"预算负责人：{item.OwnerName}；";
                historyContent += $"描述：{item.Description}；";
                FocBudgetHistory bdHistory = new();
                bdHistory.OperateType = OperateType.Create;
                bdHistory.OperateContent = historyContent;
                bdHistory.OperatorId = CurrentUser.Id.Value;
                bdHistory.OperatorName = CurrentUser.Name;
                bdHistory.OperateQty = item.BudgetQty;
                bdHistory.OperatingTime = DateTime.Now;
                bdHistory.BudgetType = BudgetType.FocMasterBudget;
                bdHistory.BudgetId = Id;
                historys.Add(bdHistory);
            }
            //await BudgetRepository.InsertManyAsync(budgets);
            await GenerateBatchCode(BudgetRepository, budgets);
            await SaveHistory(historys);
            return MessageResult.SuccessResult("新增成功");
        }

        /// <summary>
        /// 生成自动编号
        /// </summary>
        /// <returns></returns>
        private async Task<Guid> GenerateCode(IFocMasterBudgetRepository masterBudgetRepository, FocMasterBudget masterBudget)
        {
            await using (var handle = await LazyServiceProvider.LazyGetService<IAbpDistributedLock>().TryAcquireAsync($"{DistributedLock.DistributedLock_GenerateSerialFocBudgetNo}", TimeSpan.FromSeconds(5)))
            {
                if (handle != null)
                {
                    int count = 0;
                    masterBudget.Year = masterBudget.Year.HasValue ? masterBudget.Year.Value < 1000 ? DateTimeOffset.Now.Year : masterBudget.Year.Value : DateTimeOffset.Now.Year;
                    using (DataFilter.Disable<ISoftDelete>())
                    {
                        var queryBudget = await masterBudgetRepository.GetQueryableAsync();
                        var budgetNo = queryBudget.Where(a => a.Year == masterBudget.Year)
                            //.Select(a => int.Parse(a.Code.Substring(a.Code.IndexOf('-') + 1)))
                            .OrderByDescending(a => a.Code)
                            .FirstOrDefault();
                        if (budgetNo != null)
                        {
                            count = int.Parse(budgetNo.Code.Substring(budgetNo.Code.IndexOf('-') + 1));
                        }
                    }
                    //todo:与BPM并行期间，采购申请单号规则调整5000开始（后续可能需要还原）
                    var countNo = count < 5000 ? ++count + 5000 : ++count;
                    var strYear = masterBudget.Year.ToString();
                    var no = $"FMB{strYear.Substring(strYear.Length - 2)}-{countNo:D4}";
                    masterBudget.Code = no;
                    var mBudget = await masterBudgetRepository.InsertAsync(masterBudget, true);
                    return mBudget.Id;
                }
            }
            throw new TimeoutException("生成主预算编号超时，请重新操作");
        }

        /// <summary>
        /// 新增历史记录
        /// </summary>
        /// <param name="bdHistorys"></param>
        /// <returns></returns>
        private async Task SaveHistory(List<FocBudgetHistory> bdHistorys)
        {
            var historyQueryRepository = LazyServiceProvider.LazyGetService<IFocBudgetHistoryRepository>();
            await historyQueryRepository.InsertManyAsync(bdHistorys);
        }

        /// <summary>
        /// 生成自动编号 批量
        /// </summary>
        /// <returns></returns>
        private async Task GenerateBatchCode(IFocMasterBudgetRepository masterBudgetRepository, List<FocMasterBudget> masterBudgets)
        {
            await using (var handle = await LazyServiceProvider.LazyGetService<IAbpDistributedLock>().TryAcquireAsync($"{DistributedLock.DistributedLock_GenerateSerialFocBudgetNo}", TimeSpan.FromSeconds(5)))
            {
                if (handle != null)
                {
                    foreach (var budget in masterBudgets)
                    {
                        int count = 0;
                        budget.Year = budget.Year.HasValue ? budget.Year.Value < 1000 ? DateTimeOffset.Now.Year : budget.Year.Value : DateTimeOffset.Now.Year;
                        using (DataFilter.Disable<ISoftDelete>())
                        {
                            var queryBudget = await masterBudgetRepository.GetQueryableAsync();
                            var budgetNo = queryBudget.Where(a => a.Year == budget.Year)
                                .OrderByDescending(a => a.Code)
                                .FirstOrDefault();
                            if (budgetNo != null)
                            {
                                count = int.Parse(budgetNo.Code.Substring(budgetNo.Code.IndexOf('-') + 1));
                            }
                        }
                        //todo:与BPM并行期间，采购申请单号规则调整5000开始（后续可能需要还原）
                        var countNo = count < 5000 ? ++count + 5000 : ++count;
                        var strYear = budget.Year.ToString();
                        var no = $"FMB{strYear.Substring(strYear.Length - 2)}-{countNo:D4}";
                        budget.Code = no;
                        await masterBudgetRepository.InsertAsync(budget, true);
                    }
                    return;
                }
            }
            throw new TimeoutException("生成主预算编号超时，请重新操作");
        }
    }
}
