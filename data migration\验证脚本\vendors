

SELECT 
	count(*) over() counts,b.VCMPNY,b.vendor,a.<PERSON>
from PLATFORM_ABBOTT.dbo.Vendors a    
inner join PLATFORM_ABBOTT.dbo.Vendor_tmp b
	on a.id = b.id 
FULL  join 
  (
  
	SELECT  VCMPNY,VNDERX from PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a 
		join PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM b 
		on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
		where [VCMPNY] in (91,79,18,20) 
  ) c 
  on  c.VCMPNY=b.VCMPNY and b.VENDOR=c.VNDERX
where c.VNDERX is null OR a.id is null
--and b.id is null


;



-- 电话号码验证  HandPhone
SELECT 
	count(*) over() counts,a.id,c.vendor,b.VCMPNY,b.vendor,a.HandPhone,TRIM(c.VPHONE) as HandPhone1,c.VTYPE,
	case when TRIM(a.HandPhone) = TRIM(c.VPHONE) then 'true' else 'false' end HandPhoneCheck 
from PLATFORM_ABBOTT.dbo.Vendors a    
inner join PLATFORM_ABBOTT.dbo.Vendor_tmp b
	on a.id = b.id 
full join 
PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM c 
  on c.VENDOR=b.VENDOR  and c.VCMPNY =b.VCMPNY
where  c.[VCMPNY] in (91,79,18,20)
and a.HandPhone = TRIM(c.VPHONE)   and c.VENDOR =10248

 
-- 类型验证   VendorType
 select LEN ('13818960563')

-- 供应商类型判断 NT 判断
WITH avm_fvm as ( 
	SELECT 
		 VCMPNY,VMCMPY,VENDOR,VNDERX,VTYPE,VNDNAM,VMXCRT,
		 case 
			 when upper(trim(VTYPE)) in ('NHIV','NLIV') and VMXCRT  <>''   then  TRIM(REPLACE( REPLACE(VMXCRT,' F','') ,' M',''))   --判断个人供应商
			 when VMXCRT is null or VMXCRT  =''  or VLDRM2='' or VLDRM2 is null then cast(newid() as nvarchar(255))
			 else  TRIM(VLDRM2)
		end AS VMXCRT_NO1 
	from PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a 
	join PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM b 
		on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
	where [VCMPNY] in (91,79,18,20) 
--	and VTYPE in ('NHIV' ,  'NLIV' , 'NH' ,  'NL' ,  'NT')
	and VTYPE in ('NT')

) ,
nt as (
	select t.VENDOR,t.VCMPNY,xmlVendors,VTYPE,ProcInstId,VMXCRT_NO1,
	 	ROW_NUMBER () over(PARTITION by VMXCRT_NO1 order by VENDOR desc ) as rn,
		case -- 逻辑判断 个人和 供应商
			when upper(trim(VTYPE)) ='NHIV' then '1'
			when upper(trim(VTYPE)) ='NLIV' then '2'
			when upper(trim(VTYPE)) ='NH' then '3'
			when upper(trim(VTYPE)) ='NL' then '4'
			when xmlVendors ='1' then '1'
			when xmlVendors ='2' then '3'
			when xmlVendors = '3' then '4'
			when xmlVendors = '4' then '2'
			else '4' 
		end as VendorType  
	from (
		select 
			a.*,c.ProcInstId,
			trim(cast(isnull(XmlContent,'') as XML).value('(/root/SupplierApplication_applicantBaseInfoBlock_MainStore/supplierHCPType)[1]', 'nvarchar(5)')) as [xmlVendors]
		from avm_fvm a
		-- NT 逻辑判断 个人和 供应商
		left join PLATFORM_ABBOTT.dbo.ODS_T_VendorMasterData_Log c
			on a.VENDOR=c.VENDOR and a.VCMPNY=c.VCMPNY and trim(a.VNDNAM)=trim(c.VNDNAM)
		left join  
			PLATFORM_ABBOTT.dbo.ODS_T_FORMINSTANCE_GLOBAL  d 
		on d.ProcInstId=c.ProcInstId
	) t
)
select count(*) over() aaaa,t1.*,t2.VENDOR,t2.VCMPNY,t2.VendorType from nt as t1
 LEFT  join (
	SELECT a.id,b.VENDOR,VCMPNY,a.VendorType  from PLATFORM_ABBOTT.dbo.Vendors a    
	  inner join PLATFORM_ABBOTT.dbo.Vendor_tmp b
		on a.id = b.id 
) t2
 on t1.VENDOR =t2.VENDOR  
 where t1.VendorType <> t2.VendorType
 order by VMXCRT_NO1,rn desc
 
 
 -- 供应商类型判断 非 NT 判断
 
 WITH avm_fvm as ( 
	SELECT 
		 VCMPNY,VMCMPY,VENDOR,VNDERX,VTYPE,VNDNAM,VMXCRT
		 ,case 
			 when upper(trim(VTYPE)) in ('NHIV','NLIV') and VMXCRT  <>''   then  TRIM(REPLACE( REPLACE(VMXCRT,' F','') ,' M',''))   --判断个人供应商
			 when VMXCRT is null or VMXCRT  =''  or VLDRM2='' or VLDRM2 is null then cast(newid() as nvarchar(255))
			 else  TRIM(VLDRM2)
		end AS VMXCRT_NO1 
	from PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a 
	join PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM b 
		on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
	where [VCMPNY] in (91,79,18,20) 
	and VTYPE in ('NHIV' ,  'NLIV' , 'NH' ,  'NL'  )
) ,
nt as (
	select t.VENDOR,t.VCMPNY, VTYPE,VMXCRT_NO1,  ROW_NUMBER () over(PARTITION by VMXCRT_NO1 order by VENDOR desc ) as rn,
		case -- 逻辑判断 个人和 供应商
			when upper(trim(VTYPE)) ='NHIV' then '1'
			when upper(trim(VTYPE)) ='NLIV' then '2'
			when upper(trim(VTYPE)) ='NH' then '3'
			when upper(trim(VTYPE)) ='NL' then '4'
			else '0' 
		end as VendorType  
	 from avm_fvm t
)
select count(*) over() aaaa,t1.*,t2.VENDOR,t2.VCMPNY,t2.VendorType 

from nt as t1
LEFT  join (
	SELECT a.id,b.VENDOR,VCMPNY,a.VendorType  from PLATFORM_ABBOTT.dbo.Vendors a    
	  inner join PLATFORM_ABBOTT.dbo.Vendor_tmp b
		on a.id = b.id 
) t2
 	on t1.VENDOR =t2.VENDOR  and t1.VCMPNY = t2.VCMPNY
 where t1.VendorType <> t2.VendorType or t2.VENDOR is null
 order by  VMXCRT_NO1
  


 -- 主表数据   
 
select 
	count(*) over() aaaaa,VLDATE,vldate1, VLTIME,vcrdte1,  VCRDTE,VCTIME,
	* 
from (
	select * from (
		SELECT  
			*,
			ROW_NUMBER () over(PARTITION by VMXCRT_NO1 order by COALESCE(nullif(vldate1,0),vcrdte1) desc,vcrdte1 desc, COALESCE(nullif(VLTIME,0),VCTIME)  desc ) as rn
		from
		(
			SELECT a.*,b.VLDATE,b.VLTIME,b.VEXTNM,b.VCRDTE,b.VCTIME,
				CASE WHEN len(cast(vldate as VARCHAR(255)))<=6 and vldate <> 0 then cast(CONCAT('20',vldate) as int) else vldate end vldate1,
				CASE WHEN len(cast(VCRDTE as VARCHAR(255)))<=6 and VCRDTE <> 0 then cast(CONCAT('20',VCRDTE) as int) else VCRDTE end vcrdte1,
			 	case 
					when upper(trim(VTYPE)) in ('NHIV','NLIV') and VMXCRT  <>''   then  TRIM(REPLACE( REPLACE(VMXCRT,' F','') ,' M',''))   --判断个人供应商
				 when VMXCRT is null or VMXCRT  =''  or VLDRM2='' or VLDRM2 is null then cast(newid() as nvarchar(255))  
				else  TRIM(VLDRM2)
				end AS VMXCRT_NO1 
			from PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a 
			join PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM b 
				on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
			where [VCMPNY] in (91,79,18,20)  AND trim(VTYPE) in ('NHIV','NLIV','NH','NL','NT')
		) a1 
	) a2 
	where a2.rn=1
) t1    -- where   t1.rn=1   --81167  86377

LEFT  join (
	SELECT a.id,b.VENDOR,VCMPNY  from PLATFORM_ABBOTT.dbo.Vendors a    
	  inner join PLATFORM_ABBOTT.dbo.Vendor_tmp b
		on a.id = b.id 
) t2
 on t1.VENDOR =t2.VENDOR  and t1.VCMPNY= t2.VCMPNY
 where   t2.VENDOR is null 


-- 主数据复查 
SELECT  Status, HandPhone, * from PLATFORM_ABBOTT.dbo.Vendor_tmp b
--where VendorType in (1,2)
where  VENDOR   in (10248,11569,
97219,
54670,
12714 ) 

 

SELECT 
VCMPNY,a.VCMPNY,a.VENDOR,VTYPE,TRIM(REPLACE( REPLACE(VMXCRT,' F','') ,' M','')) VMXCRT,VLDRM1,trim(VLDRM2) as VLDRM2,*
from  PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a 
join PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM b 
on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX 
where [VCMPNY] in (91,79,18,20)  and 
( VLDRM2  like '%110111198112280040%' or VMXCRT   like '%110111198112280040%')
11569 ;97219; 54670; 12714
-- 重复证件号  ------ 数据合并
select 
	VLDATE,vldate1, VLTIME,vcrdte1,  VCRDTE,VCTIME,CreationTime,
	count(*) over() aaaaa,
	* 
from (
	select 
		* 
	from 
	(
		SELECT  
			a1.*,
			ROW_NUMBER () over(PARTITION by VMXCRT_NO1 order by vnstat1 desc,COALESCE(nullif(vldate1,0),vcrdte1) desc,vcrdte1 desc, COALESCE(nullif(VLTIME,0),VCTIME)  desc ) as rn,
			min(CONCAT(vcrdte1,VCTIME))over(PARTITION by VMXCRT_NO1 ) as CreationTime,
			FIRST_value(VCUSER)over(PARTITION by VMXCRT_NO1 order by  vnstat1 desc,vcrdte1,VCTIME  ) as VCUSER1
		from
		(
			SELECT a.*,b.VLDATE,b.VLTIME,b.VEXTNM,b.VCRDTE,b.VCTIME ,VCUSER, VMBNKA as BankNo,
				CASE WHEN len(cast(vldate as VARCHAR(255)))<=6 and vldate <> 0 then cast(CONCAT('20',vldate) as int) else vldate end vldate1,
				CASE WHEN len(cast(VCRDTE as VARCHAR(255)))<=6 and VCRDTE <> 0 then cast(CONCAT('20',VCRDTE) as int) else VCRDTE end vcrdte1,
			 	case 
					when upper(trim(VTYPE)) in ('NHIV','NLIV') and VMXCRT  <>''   then  TRIM(REPLACE( REPLACE(VMXCRT,' F','') ,' M',''))   --判断个人供应商
				 when VMXCRT is null or VMXCRT  =''  or VLDRM2='' or VLDRM2 is null then cast(newid() as nvarchar(255))  
				else  TRIM(VLDRM2)
				end AS VMXCRT_NO1,
				case when upper(vnstat)='A' THEN 1 else 0 end vnstat1
			from PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a 
			join PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM b 
				on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
			where [VCMPNY] in (91,79,18,20)  AND trim(VTYPE) in ('NHIV','NLIV','NH','NL','NT')
					and 	TRIM(REPLACE( REPLACE(VMXCRT,' F','') ,' M',''))  in ('330225196511190099','11010819630703247x')
		) a1 
	) a2 
--	where a2.rn=1
) t1    -- where   t1.rn=1   --81167  86377
 where  TRIM(REPLACE( REPLACE(VMXCRT,' F','') ,' M',''))  in ('330225196511190099'
,'11010819630703247x'
)
 
 


--- Status 检查


 WITH avm_fvm as ( 
	SELECT 
		 a.*,  
		 case 
			 when upper(trim(VTYPE)) in ('NHIV','NLIV') and VMXCRT  <>''   then  TRIM(REPLACE( REPLACE(VMXCRT,' F','') ,' M',''))   --判断个人供应商
			 when VMXCRT is null or VMXCRT  =''  or VLDRM2='' or VLDRM2 is null then cast(newid() as nvarchar(255))
			 else  TRIM(VLDRM2)
		end AS VMXCRT_NO1 
	from PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a 
	join PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM b 
		on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
	where [VCMPNY] in (91,79,18,20) 
	and VTYPE in ('NHIV' ,  'NLIV' , 'NH' ,  'NL' ,'NT' )
) ,
nt as (
	select t.VENDOR,t.VCMPNY, VTYPE,VMXCRT_NO1,VNSTAT,  ROW_NUMBER () over(PARTITION by VMXCRT_NO1 order by VENDOR desc ) as rn,
		CASE 
	        WHEN CASE WHEN VNSTAT = 'A' THEN 1 ELSE 0 END = 1 THEN 2
	        ELSE 3
	    END AS Status
	 from avm_fvm t
)
select count(*) over() aaaa,t1.*,t2.VENDOR,t2.VCMPNY,t2.VendorType ,t2.Status

from nt as t1
LEFT  join (
	SELECT a.id,b.VENDOR,VCMPNY,a.VendorType,a.Status  from PLATFORM_ABBOTT.dbo.Vendors a    
	  inner join PLATFORM_ABBOTT.dbo.Vendor_tmp b
		on a.id = b.id 
) t2
 	on t1.VENDOR =t2.VENDOR  and t1.VCMPNY = t2.VCMPNY
 where t1.Status <> t2.Status or t2.VENDOR is null
-- order by  VMXCRT_NO1 ASC,rn ASC
 
 -- 检查  EpdId
 SELECT  DISTINCT  EpdId  FROM PLATFORM_ABBOTT.dbo.vendors
 select DoctorsCode ,count(*) from PLATFORM_ABBOTT.dbo.ods_T_CRM_UpdateDoctorCodeLog
 group by DoctorsCode HAVING  count(*)>1
 select * from   PLATFORM_ABBOTT.dbo.ods_T_CRM_UpdateDoctorCodeLog where DoctorsCode='20_10222'
 
 
 select ProcInstId,f_code,count(*)   from PLATFORM_ABBOTT.dbo.ods_AUTO_BIZ_T_ProcurementApplication_Info group by ProcInstId,f_code  
 select count(*)   from PLATFORM_ABBOTT.dbo.ODS_AUTO_BIZ_T_ProcurementApplication_Info_PR  

 
 
 
  WITH avm_fvm as ( 
	SELECT 
		 a.*,  
		 case 
			 when upper(trim(VTYPE)) in ('NHIV','NLIV') and VMXCRT  <>''   then  TRIM(REPLACE( REPLACE(VMXCRT,' F','') ,' M',''))   --判断个人供应商
			 when VMXCRT is null or VMXCRT  =''  or VLDRM2='' or VLDRM2 is null then cast(newid() as nvarchar(255))
			 else  TRIM(VLDRM2)
		end AS VMXCRT_NO1 
	from PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a 
	join PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM b 
		on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
	where [VCMPNY] in (91,79,18,20) 
	and VTYPE in ('NHIV' ,  'NLIV' , 'NH' ,  'NL','NT'  )
)  
, mind as (
	select 
		a.*,
		ISNULL(f.ClientId,'') as MndId,
		ROW_NUMBER () over(PARTITION by VMXCRT_NO1 order by VENDOR desc ) as rn 
	from avm_fvm as a
	 
	left join (
		 
		   	select 
			    ROW_NUMBER() OVER(PARTITION BY b.f_code, a.vendorCode ORDER BY a.ProcInstId DESC) AS RowNum,
			    b.f_code as VCMPNY
			    , a.vendorCode
			    ,CONCAT(cast(b.f_code as nvarchar(5)),'_',cast(a.vendorCode as nvarchar(10))) as DoctorsCode
			    ,a.ProcInstId
		    from PLATFORM_ABBOTT.dbo.ODS_AUTO_BIZ_T_ProcurementApplication_Info_PR a
		    join PLATFORM_ABBOTT.dbo.ODS_AUTO_BIZ_T_ProcurementApplication_Info b on a.ProcInstId=b.ProcInstId
		 
	) e 
	 on cast(e.VCMPNY as nvarchar(5))=cast(a.VCMPNY as nvarchar(5)) and cast(e.vendorCode as nvarchar(5))=cast(a.VENDOR as nvarchar(5)) and e.RowNum=1
	left join  (
		select * from (
			SELECT  *, ROW_NUMBER() OVER(PARTITION BY DoctorsCode ORDER BY DoctorsCode ) AS aa from PLATFORM_ABBOTT.dbo.ODS_T_CRM_UpdateDoctorCodeLog
		) aaa where aaa.aa=1
	) f on f.DoctorsCode=e.DoctorsCode
) 
select * from mind as t1
 LEFT  join (
	SELECT a.id,b.VENDOR,VCMPNY,a.MndId  from PLATFORM_ABBOTT.dbo.Vendors a    
	  inner join PLATFORM_ABBOTT.dbo.Vendor_tmp b
		on a.id = b.id 
) t2
 on t1.VENDOR =t2.VENDOR  and t1.VCMPNY= t2.VCMPNY
 where   t1.rn=1 and t1.MndId<>t2.MndId and
t2.VENDOR is null 
 





-- ApsPorperty 验证  供应商类型判断 NT 判断
WITH avm_fvm as 
( 
	SELECT 
		 VCMPNY,VMCMPY,VENDOR,VNDERX,VTYPE,VNDNAM,VMXCRT,
		 case 
			 when upper(trim(VTYPE)) in ('NHIV','NLIV') and VMXCRT  <>''   then  TRIM(REPLACE( REPLACE(VMXCRT,' F','') ,' M',''))   --判断个人供应商
			 when VMXCRT is null or VMXCRT  =''  or VLDRM2='' or VLDRM2 is null then cast(newid() as nvarchar(255))
			 else  TRIM(VLDRM2)
		end AS VMXCRT_NO1 
	from PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a 
	join PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM b 
		on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
	where [VCMPNY] in (91,79,18,20) 
	and VTYPE in ('NT')

)
,nt as 
(
	select t.VENDOR,t.VCMPNY,xmlVendors,VTYPE,ProcInstId,VMXCRT_NO1,VNDNAM,le.spk_codes as ApsPorperty,
	 	ROW_NUMBER () over(PARTITION by VMXCRT_NO1 order by VENDOR desc ) as rn,
		case -- 逻辑判断 个人和 供应商
			when upper(trim(VTYPE)) ='NHIV' then '1'
			when upper(trim(VTYPE)) ='NLIV' then '2'
			when upper(trim(VTYPE)) ='NH' then '3'
			when upper(trim(VTYPE)) ='NL' then '4'
			when xmlVendors ='1' then '1'
			when xmlVendors ='2' then '3'
			when xmlVendors = '3' then '4'
			when xmlVendors = '4' then '2'
			else '4' 
		end as VendorType  
	from (
		select 
			a.*,c.ProcInstId,
			trim(cast(isnull(XmlContent,'') as XML).value('(/root/SupplierApplication_applicantBaseInfoBlock_MainStore/supplierHCPType)[1]', 'nvarchar(5)')) as [xmlVendors]
		from avm_fvm a
		-- NT 逻辑判断 个人和 供应商
		left join PLATFORM_ABBOTT.dbo.ODS_T_VendorMasterData_Log c
			on a.VENDOR=c.VENDOR and a.VCMPNY=c.VCMPNY and trim(a.VNDNAM)=trim(c.VNDNAM)
		left join  
			PLATFORM_ABBOTT.dbo.ODS_T_FORMINSTANCE_GLOBAL  d 
		on d.ProcInstId=c.ProcInstId
	) t
	left join (
		select 
			SupplierName,
		    STRING_AGG(spk_code, ',') WITHIN GROUP (ORDER BY spk_code) AS spk_codes
		from (
		    select  
		    	DISTINCT  trim(a.SupplierName) as SupplierName,b.spk_code
			from PLATFORM_ABBOTT.dbo.ODS_T_ASN_Supplier a
		    JOIN PLATFORM_ABBOTT.dbo.spk_dictionary b on a.Id=b.spk_BPMCode
		    group by a.SupplierName,b.spk_code
		) t
		group by SupplierName
	) le
	on  trim(t.VNDNAM)=trim(le.SupplierName)
)
select count(*) over() aaaa,t1.*,t2.VENDOR,t2.VCMPNY,t2.VendorType,t2.ApsPorperty from nt as t1
 LEFT  join (
	SELECT a.id,b.VENDOR,VCMPNY,a.VendorType,a.ApsPorperty  from PLATFORM_ABBOTT.dbo.Vendors a    
	  inner join PLATFORM_ABBOTT.dbo.Vendor_tmp b
		on a.id = b.id 
) t2
 on t1.VENDOR =t2.VENDOR  and t1.VCMPNY= t2.VCMPNY
 where t1.ApsPorperty <> t2.ApsPorperty    
 order by VMXCRT_NO1,rn desc
 
 
 -- ApsPorperty 验证 供应商类型判断 非 NT 判断
 
 WITH avm_fvm as ( 
	SELECT 
		 VCMPNY,VMCMPY,VENDOR,VNDERX,VTYPE,VNDNAM,VMXCRT
		 ,case 
			 when upper(trim(VTYPE)) in ('NHIV','NLIV') and VMXCRT  <>''   then  TRIM(REPLACE( REPLACE(VMXCRT,' F','') ,' M',''))   --判断个人供应商
			 when VMXCRT is null or VMXCRT  =''  or VLDRM2='' or VLDRM2 is null then cast(newid() as nvarchar(255))
			 else  TRIM(VLDRM2)
		end AS VMXCRT_NO1 
	from PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a 
	join PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM b 
		on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
	where [VCMPNY] in (91,79,18,20) 
	and VTYPE in ('NHIV' ,  'NLIV' , 'NH' ,  'NL'  )
) ,
nt as (
	select t.VENDOR,t.VCMPNY, VTYPE,VMXCRT_NO1,VNDNAM, spk_codes as ApsPorperty , ROW_NUMBER () over(PARTITION by VMXCRT_NO1 order by VENDOR desc ) as rn,
		case -- 逻辑判断 个人和 供应商
			when upper(trim(VTYPE)) ='NHIV' then '1'
			when upper(trim(VTYPE)) ='NLIV' then '2'
			when upper(trim(VTYPE)) ='NH' then '3'
			when upper(trim(VTYPE)) ='NL' then '4'
			else '0' 
		end as VendorType  
	 from avm_fvm t 
	 left join (
		select 
			SupplierName,
		    STRING_AGG(spk_code, ',') WITHIN GROUP (ORDER BY spk_code) AS spk_codes
		from (
		    select  
		    	DISTINCT  trim(a.SupplierName) as SupplierName,b.spk_code
			from PLATFORM_ABBOTT.dbo.ODS_T_ASN_Supplier a
		    JOIN PLATFORM_ABBOTT.dbo.spk_dictionary b on a.Id=b.spk_BPMCode
		    group by a.SupplierName,b.spk_code
		) t
		group by SupplierName
	 ) le
	 on  trim(t.VNDNAM)=trim(le.SupplierName)
)
select count(*) over() aaaa,t1.*,t2.VENDOR,t2.VCMPNY,t2.VendorType 

from nt as t1
LEFT  join (
	SELECT a.id,b.VENDOR,VCMPNY,a.VendorType ,a.ApsPorperty from PLATFORM_ABBOTT.dbo.Vendors a    
	  inner join PLATFORM_ABBOTT.dbo.Vendor_tmp b
		on a.id = b.id 
) t2
 	on t1.VENDOR =t2.VENDOR  and t1.VCMPNY = t2.VCMPNY
 where t1.ApsPorperty <> t2.ApsPorperty or t2.VENDOR is null
 order by  VMXCRT_NO1
  

--- CertificateCode 检查 NT 供应商
 
 
WITH avm_fvm as 
( 
	SELECT 
		 VCMPNY,VMCMPY,VENDOR,VNDERX,VTYPE,VNDNAM,VMXCRT,VEXTNM,VNDAD3,
		 case 
			 when upper(trim(VTYPE)) in ('NHIV','NLIV') and VMXCRT  <>''   then  TRIM(REPLACE( REPLACE(VMXCRT,' F','') ,' M',''))   --判断个人供应商
			 when VMXCRT is null or VMXCRT  =''  or VLDRM2='' or VLDRM2 is null then cast(newid() as nvarchar(255))
			 else  TRIM(VLDRM2)
		end AS VMXCRT_NO1 
	from PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a 
	join PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM b 
		on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
	where [VCMPNY] in (91,79,18,20) 
	and VTYPE in ('NT')

)
,nt as 
(
	select t.VENDOR,t.VCMPNY,xmlVendors,VTYPE,ProcInstId,VMXCRT_NO1,t.VNDNAM,t.VEXTNM,COALESCE(le.LICENSE_NO, t.VNDAD3) as CertificateCode,
	 	ROW_NUMBER () over(PARTITION by VMXCRT_NO1 order by t.VENDOR desc ) as rn,
		case -- 逻辑判断 个人和 供应商
			when upper(trim(VTYPE)) ='NHIV' then '1'
			when upper(trim(VTYPE)) ='NLIV' then '2'
			when upper(trim(VTYPE)) ='NH' then '3'
			when upper(trim(VTYPE)) ='NL' then '4'
			when xmlVendors ='1' then '1'
			when xmlVendors ='2' then '3'
			when xmlVendors = '3' then '4'
			when xmlVendors = '4' then '2'
			else '4' 
		end as VendorType  
	from (
		select 
			a.*,c.ProcInstId,
			trim(cast(isnull(XmlContent,'') as XML).value('(/root/SupplierApplication_applicantBaseInfoBlock_MainStore/supplierHCPType)[1]', 'nvarchar(5)')) as [xmlVendors]
		from avm_fvm a
		-- NT 逻辑判断 个人和 供应商
		left join PLATFORM_ABBOTT.dbo.ODS_T_VendorMasterData_Log c
			on a.VENDOR=c.VENDOR and a.VCMPNY=c.VCMPNY and trim(a.VNDNAM)=trim(c.VNDNAM)
		left join  
			PLATFORM_ABBOTT.dbo.ODS_T_FORMINSTANCE_GLOBAL  d 
		on d.ProcInstId=c.ProcInstId
	) t
	left join (
	
	 	SELECT 
			LICENSE_NO,
			VENDOR,
			VNDNAM,
			row_number() over(partition by VENDOR,VNDNAM order by COALESCE(UPD_DATETIME,INS_DATETIME) desc) as rns
		from PLATFORM_ABBOTT.dbo.ODS_T_VENDOR_TIER	
	) le
		on cast(le.VENDOR as nvarchar(255))=cast(t.VENDOR as nvarchar(255)) and  
		(trim(cast(le.VNDNAM as nvarchar(255))) =trim(cast(t.VEXTNM as nvarchar(255)))  or   trim(le.VNDNAM)=trim(t.VNDNAM)  )
		and le.rns=1
	
)
select count(*) over() aaaa,t1.*,t2.VENDOR,t2.VCMPNY,t2.VendorType,t2.CertificateCode from nt as t1
 LEFT  join (
	SELECT a.id,b.VENDOR,VCMPNY,a.VendorType,a.CertificateCode  from PLATFORM_ABBOTT.dbo.Vendors a    
	  inner join PLATFORM_ABBOTT.dbo.Vendor_tmp b
		on a.id = b.id 
) t2
 on t1.VENDOR =t2.VENDOR  and t1.VCMPNY= t2.VCMPNY 
 where t1.CertificateCode <> t2.CertificateCode    and t1.rn=1 and t1.VendorType =1
-- order by VMXCRT_NO1,rn desc
 
 
 -- CertificateCode 验证 供应商类型判断 非 NT 判断
 
 WITH avm_fvm as ( 
	SELECT 
		 VCMPNY,VMCMPY,VENDOR,VNDERX,VTYPE,VNDNAM,VMXCRT,VEXTNM,VNDAD3,
		 case 
			 when upper(trim(VTYPE)) in ('NHIV','NLIV') and VMXCRT  <>''   then  TRIM(REPLACE( REPLACE(VMXCRT,' F','') ,' M',''))   --判断个人供应商
			 when VMXCRT is null or VMXCRT  =''  or VLDRM2='' or VLDRM2 is null then cast(newid() as nvarchar(255))
			 else  TRIM(VLDRM2)
		end AS VMXCRT_NO1 
	from PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a 
	join PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM b 
		on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
	where [VCMPNY] in (91,79,18,20) 
	and VTYPE in ('NHIV' ,  'NLIV' , 'NH' ,  'NL'  )
) ,
nt as (
	select t.VENDOR,t.VCMPNY, VTYPE,VMXCRT_NO1, t.VNDNAM,t.VEXTNM,COALESCE(le.LICENSE_NO, t.VNDAD3) as CertificateCode,
	
	ROW_NUMBER () over(PARTITION by VMXCRT_NO1 order by t.VENDOR desc ) as rn,
		case -- 逻辑判断 个人和 供应商
			when upper(trim(VTYPE)) ='NHIV' then '1'
			when upper(trim(VTYPE)) ='NLIV' then '2'
			when upper(trim(VTYPE)) ='NH' then '3'
			when upper(trim(VTYPE)) ='NL' then '4'
			else '0' 
		end as VendorType  
	 from avm_fvm t 
	 left join (
		 SELECT 
			LICENSE_NO,
			VENDOR,
			VNDNAM,
			row_number() over(partition by VENDOR,VNDNAM order by COALESCE(UPD_DATETIME,INS_DATETIME) desc) as rns
		from PLATFORM_ABBOTT.dbo.ODS_T_VENDOR_TIER	
	 ) le
	 on cast(le.VENDOR as nvarchar(255))=cast(t.VENDOR as nvarchar(255)) and  
		(trim(cast(le.VNDNAM as nvarchar(255))) =trim(cast(t.VEXTNM as nvarchar(255)))  or   trim(le.VNDNAM)=trim(t.VNDNAM)  )
		and le.rns=1
)
select 
	count(*) over() aaaa,t1.*,t2.VENDOR,t2.VCMPNY,t2.VendorType,t2.CertificateCode  
from nt as t1
LEFT  join (
	SELECT a.id,b.VENDOR,VCMPNY,a.VendorType ,a.CertificateCode from PLATFORM_ABBOTT.dbo.Vendors a    
	  inner join PLATFORM_ABBOTT.dbo.Vendor_tmp b
		on a.id = b.id 
) t2
 	on t1.VENDOR =t2.VENDOR  and t1.VCMPNY = t2.VCMPNY
 where t1.CertificateCode <> t2.CertificateCode    and t1.rn=1 and t1.VendorType =1
-- order by  VMXCRT_NO1
  

 
 
 -----------------------------------------
 
 
 
 
 
 
--- SPLevel 检查 NT 供应商
 
 
WITH avm_fvm as 
( 
	SELECT 
		 VCMPNY,VMCMPY,VENDOR,VNDERX,VTYPE,VNDNAM,VMXCRT,VEXTNM,VNDAD3,
		 case 
			 when upper(trim(VTYPE)) in ('NHIV','NLIV') and VMXCRT  <>''   then  TRIM(REPLACE( REPLACE(VMXCRT,' F','') ,' M',''))   --判断个人供应商
			 when VMXCRT is null or VMXCRT  =''  or VLDRM2='' or VLDRM2 is null then cast(newid() as nvarchar(255))
			 else  TRIM(VLDRM2)
		end AS VMXCRT_NO1 
	from PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a 
	join PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM b 
		on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
	where [VCMPNY] in (91,79,18,20) 
	and VTYPE in ('NT')

)
,nt as 
(
	select t.VENDOR,t.VCMPNY,xmlVendors,VTYPE,ProcInstId,VMXCRT_NO1,t.VNDNAM,t.VEXTNM,TIER,case when upper(TIER) ='N/A' then '' else TIER end as SPLevel,
	 	ROW_NUMBER () over(PARTITION by VMXCRT_NO1 order by t.VENDOR desc ) as rn,
		case -- 逻辑判断 个人和 供应商
			when upper(trim(VTYPE)) ='NHIV' then '1'
			when upper(trim(VTYPE)) ='NLIV' then '2'
			when upper(trim(VTYPE)) ='NH' then '3'
			when upper(trim(VTYPE)) ='NL' then '4'
			when xmlVendors ='1' then '1'
			when xmlVendors ='2' then '3'
			when xmlVendors = '3' then '4'
			when xmlVendors = '4' then '2'
			else '4' 
		end as VendorType  
	from (
		select 
			a.*,c.ProcInstId,
			trim(cast(isnull(XmlContent,'') as XML).value('(/root/SupplierApplication_applicantBaseInfoBlock_MainStore/supplierHCPType)[1]', 'nvarchar(5)')) as [xmlVendors]
		from avm_fvm a
		-- NT 逻辑判断 个人和 供应商
		left join PLATFORM_ABBOTT.dbo.ODS_T_VendorMasterData_Log c
			on a.VENDOR=c.VENDOR and a.VCMPNY=c.VCMPNY and trim(a.VNDNAM)=trim(c.VNDNAM)
		left join  
			PLATFORM_ABBOTT.dbo.ODS_T_FORMINSTANCE_GLOBAL  d 
		on d.ProcInstId=c.ProcInstId
	) t
	left join (
	
	 	SELECT 
			LICENSE_NO,TIER,
			VENDOR,
			VNDNAM,
			row_number() over(partition by VENDOR,VNDNAM order by COALESCE(UPD_DATETIME,INS_DATETIME) desc) as rns
		from PLATFORM_ABBOTT.dbo.ODS_T_VENDOR_TIER	
	) le
		on cast(le.VENDOR as nvarchar(255))=cast(t.VENDOR as nvarchar(255)) and  
		(trim(cast(le.VNDNAM as nvarchar(255))) =trim(cast(t.VEXTNM as nvarchar(255)))  or   trim(le.VNDNAM)=trim(t.VNDNAM)  )
		and le.rns=1
	
)
select count(*) over() aaaa,t1.*,t2.VENDOR,t2.VCMPNY,t2.VendorType,t2.SPLevel from nt as t1
 LEFT  join (
	SELECT a.id,b.VENDOR,VCMPNY,a.VendorType,a.SPLevel  from PLATFORM_ABBOTT.dbo.Vendors a    
	  inner join PLATFORM_ABBOTT.dbo.Vendor_tmp b
		on a.id = b.id 
) t2
 on t1.VENDOR =t2.VENDOR  and t1.VCMPNY= t2.VCMPNY 
 where t1.SPLevel <> t2.SPLevel    and t1.rn=1 and t1.VendorType =1
-- order by VMXCRT_NO1,rn desc
 
 
 -- SPLevel 验证 供应商类型判断 非 NT 判断
 
 WITH avm_fvm as ( 
	SELECT 
		 VCMPNY,VMCMPY,VENDOR,VNDERX,VTYPE,VNDNAM,VMXCRT,VEXTNM,VNDAD3,
		 case 
			 when upper(trim(VTYPE)) in ('NHIV','NLIV') and VMXCRT  <>''   then  TRIM(REPLACE( REPLACE(VMXCRT,' F','') ,' M',''))   --判断个人供应商
			 when VMXCRT is null or VMXCRT  =''  or VLDRM2='' or VLDRM2 is null then cast(newid() as nvarchar(255))
			 else  TRIM(VLDRM2)
		end AS VMXCRT_NO1 
	from PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a 
	join PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM b 
		on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
	where [VCMPNY] in (91,79,18,20) 
	and VTYPE in ('NHIV' ,  'NLIV' , 'NH' ,  'NL'  )
) ,
nt as (
	select t.VENDOR,t.VCMPNY, VTYPE,VMXCRT_NO1, t.VNDNAM,t.VEXTNM,TIER,case when upper(TIER) ='N/A' then '' else TIER end as SPLevel,
	
	ROW_NUMBER () over(PARTITION by VMXCRT_NO1 order by t.VENDOR desc ) as rn,
		case -- 逻辑判断 个人和 供应商
			when upper(trim(VTYPE)) ='NHIV' then '1'
			when upper(trim(VTYPE)) ='NLIV' then '2'
			when upper(trim(VTYPE)) ='NH' then '3'
			when upper(trim(VTYPE)) ='NL' then '4'
			else '0' 
		end as VendorType  
	 from avm_fvm t 
	 left join (
		 SELECT 
			LICENSE_NO,TIER,
			VENDOR,
			VNDNAM,
			row_number() over(partition by VENDOR,VNDNAM order by COALESCE(UPD_DATETIME,INS_DATETIME) desc) as rns
		from PLATFORM_ABBOTT.dbo.ODS_T_VENDOR_TIER	
	 ) le
	 on cast(le.VENDOR as nvarchar(255))=cast(t.VENDOR as nvarchar(255)) and  
		(trim(cast(le.VNDNAM as nvarchar(255))) =trim(cast(t.VEXTNM as nvarchar(255)))  or   trim(le.VNDNAM)=trim(t.VNDNAM)  )
		and le.rns=1
)
select 
	count(*) over() aaaa,t1.*,t2.VENDOR,t2.VCMPNY,t2.VendorType,t2.SPLevel  
from nt as t1
LEFT  join (
	SELECT a.id,b.VENDOR,VCMPNY,a.VendorType ,a.SPLevel from PLATFORM_ABBOTT.dbo.Vendors a    
	  inner join PLATFORM_ABBOTT.dbo.Vendor_tmp b
		on a.id = b.id 
) t2
 	on t1.VENDOR =t2.VENDOR  and t1.VCMPNY = t2.VCMPNY
 where t1.SPLevel <> t2.SPLevel    and t1.rn=1 and t1.VendorType =1
-- order by  VMXCRT_NO1
  

 
 
 -----------------------------------------
 
 
--- AcademicPosition 检查 NT 供应商
 
 
WITH avm_fvm as 
( 
	SELECT 
		 VCMPNY,VMCMPY,VENDOR,VNDERX,VTYPE,VNDNAM,VMXCRT,VEXTNM,VNDAD3,
		 case 
			 when upper(trim(VTYPE)) in ('NHIV','NLIV') and VMXCRT  <>''   then  TRIM(REPLACE( REPLACE(VMXCRT,' F','') ,' M',''))   --判断个人供应商
			 when VMXCRT is null or VMXCRT  =''  or VLDRM2='' or VLDRM2 is null then cast(newid() as nvarchar(255))
			 else  TRIM(VLDRM2)
		end AS VMXCRT_NO1 
	from PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a 
	join PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM b 
		on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
	where [VCMPNY] in (91,79,18,20) 
	and VTYPE in ('NT')

)
,nt as 
(
	select t.VENDOR,t.VCMPNY,xmlVendors,VTYPE,ProcInstId,VMXCRT_NO1,t.VNDNAM,t.VEXTNM,VNDMEMO02,case when upper(VNDMEMO02) ='N/A' then ' ' else VNDMEMO02 end as AcademicPosition,
	 	ROW_NUMBER () over(PARTITION by VMXCRT_NO1 order by t.VENDOR desc ) as rn,
		case -- 逻辑判断 个人和 供应商
			when upper(trim(VTYPE)) ='NHIV' then '1'
			when upper(trim(VTYPE)) ='NLIV' then '2'
			when upper(trim(VTYPE)) ='NH' then '3'
			when upper(trim(VTYPE)) ='NL' then '4'
			when xmlVendors ='1' then '1'
			when xmlVendors ='2' then '3'
			when xmlVendors = '3' then '4'
			when xmlVendors = '4' then '2'
			else '4' 
		end as VendorType  
	from (
		select 
			a.*,c.ProcInstId,
			trim(cast(isnull(XmlContent,'') as XML).value('(/root/SupplierApplication_applicantBaseInfoBlock_MainStore/supplierHCPType)[1]', 'nvarchar(5)')) as [xmlVendors]
		from avm_fvm a
		-- NT 逻辑判断 个人和 供应商
		left join PLATFORM_ABBOTT.dbo.ODS_T_VendorMasterData_Log c
			on a.VENDOR=c.VENDOR and a.VCMPNY=c.VCMPNY and trim(a.VNDNAM)=trim(c.VNDNAM)
		left join  
			PLATFORM_ABBOTT.dbo.ODS_T_FORMINSTANCE_GLOBAL  d 
		on d.ProcInstId=c.ProcInstId
	) t
	left join (
	
	 	SELECT 
			LICENSE_NO,TIER,VNDMEMO02,
			VENDOR,
			VNDNAM,
			row_number() over(partition by VENDOR,VNDNAM order by COALESCE(UPD_DATETIME,INS_DATETIME) desc) as rns
		from PLATFORM_ABBOTT.dbo.ODS_T_VENDOR_TIER	
	) le
		on cast(le.VENDOR as nvarchar(255))=cast(t.VENDOR as nvarchar(255)) and  
		(trim(cast(le.VNDNAM as nvarchar(255))) =trim(cast(t.VEXTNM as nvarchar(255)))  or   trim(le.VNDNAM)=trim(t.VNDNAM)  )
		and le.rns=1
	
)
select count(*) over() aaaa,t1.*,t2.VENDOR,t2.VCMPNY,t2.VendorType,t2.AcademicPosition from nt as t1
 LEFT  join (
	SELECT a.id,b.VENDOR,VCMPNY,a.VendorType,a.AcademicPosition  from PLATFORM_ABBOTT.dbo.Vendors a    
	  inner join PLATFORM_ABBOTT.dbo.Vendor_tmp b
		on a.id = b.id 
) t2
 on t1.VENDOR =t2.VENDOR  and t1.VCMPNY= t2.VCMPNY 
 where t1.AcademicPosition <>  t2.AcademicPosition     and t1.rn=1 and t1.VendorType =1
-- order by VMXCRT_NO1,rn desc --- AcademicPosition 类型问题报错
 
 
 -- AcademicPosition 验证 供应商类型判断 非 NT 判断
 
 WITH avm_fvm as ( 
	SELECT 
		 VCMPNY,VMCMPY,VENDOR,VNDERX,VTYPE,VNDNAM,VMXCRT,VEXTNM,VNDAD3,
		 case 
			 when upper(trim(VTYPE)) in ('NHIV','NLIV') and VMXCRT  <>''   then  TRIM(REPLACE( REPLACE(VMXCRT,' F','') ,' M',''))   --判断个人供应商
			 when VMXCRT is null or VMXCRT  =''  or VLDRM2='' or VLDRM2 is null then cast(newid() as nvarchar(255))
			 else  TRIM(VLDRM2)
		end AS VMXCRT_NO1 
	from PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a 
	join PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM b 
		on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
	where [VCMPNY] in (91,79,18,20) 
	and VTYPE in ('NHIV' ,  'NLIV' , 'NH' ,  'NL'  )
) ,
nt as (
	select t.VENDOR,t.VCMPNY, VTYPE,VMXCRT_NO1, t.VNDNAM,t.VEXTNM,VNDMEMO02,case when trim(VNDMEMO02) ='N/A' then ' ' else VNDMEMO02 end as AcademicPosition,
	
	ROW_NUMBER () over(PARTITION by VMXCRT_NO1 order by t.VENDOR desc ) as rn,
		case -- 逻辑判断 个人和 供应商
			when upper(trim(VTYPE)) ='NHIV' then '1'
			when upper(trim(VTYPE)) ='NLIV' then '2'
			when upper(trim(VTYPE)) ='NH' then '3'
			when upper(trim(VTYPE)) ='NL' then '4'
			else '0' 
		end as VendorType  
	 from avm_fvm t 
	 left join (
		 SELECT 
			LICENSE_NO,TIER,VNDMEMO02,
			VENDOR,
			VNDNAM,
			row_number() over(partition by VENDOR,VNDNAM order by COALESCE(UPD_DATETIME,INS_DATETIME) desc) as rns
		from PLATFORM_ABBOTT.dbo.ODS_T_VENDOR_TIER	
	 ) le
	 on cast(le.VENDOR as nvarchar(255))=cast(t.VENDOR as nvarchar(255)) and  
		(trim(cast(le.VNDNAM as nvarchar(255))) =trim(cast(t.VEXTNM as nvarchar(255)))  or   trim(le.VNDNAM)=trim(t.VNDNAM)  )
		and le.rns=1
)
select 
	count(*) over() aaaa,t1.*
--	,t2.VENDOR,t2.VCMPNY,t2.VendorType,t2.AcademicPosition  
from nt as t1
LEFT  join (
	SELECT a.id,b.VENDOR,VCMPNY,a.VendorType ,a.AcademicPosition from PLATFORM_ABBOTT.dbo.Vendors a    
	  inner join PLATFORM_ABBOTT.dbo.Vendor_tmp b
		on a.id = b.id 
) t2
 	on t1.VENDOR =t2.VENDOR  and t1.VCMPNY = t2.VCMPNY
 where t1.AcademicPosition <> t2.AcademicPosition    and t1.rn=1 and t1.VendorType =1
-- order by  VMXCRT_NO1   --- AcademicPosition 类型问题报错
  

 SELECT DISTINCT   a.AcademicPosition from PLATFORM_ABBOTT.dbo.Vendors a   
 
 
 
 
 --- BankCode 供应商 去重逻辑检查
 
 
select 
	count(*) over() aaaaa,*,t1.BankCode,t2.BankCode
--	* 
from (
	select * from (
		SELECT  
			*,
			ROW_NUMBER () over(PARTITION by VMXCRT_NO1 order by COALESCE(nullif(vldate1,0),vcrdte1) desc,vcrdte1 desc, COALESCE(nullif(VLTIME,0),VCTIME)  desc ) as rn
		from
		(
			SELECT a.*,b.VLDATE,b.VLTIME,b.VEXTNM,b.VCRDTE,b.VCTIME,b.VLDRM1 , VLDRM1 as BankCode,
				CASE WHEN len(cast(vldate as VARCHAR(255)))<=6 and vldate <> 0 then cast(CONCAT('20',vldate) as int) else vldate end vldate1,
				CASE WHEN len(cast(VCRDTE as VARCHAR(255)))<=6 and VCRDTE <> 0 then cast(CONCAT('20',VCRDTE) as int) else VCRDTE end vcrdte1,
			 	case 
					when upper(trim(VTYPE)) in ('NHIV','NLIV') and VMXCRT  <>''   then  TRIM(REPLACE( REPLACE(VMXCRT,' F','') ,' M',''))   --判断个人供应商
				 when VMXCRT is null or VMXCRT  =''  or VLDRM2='' or VLDRM2 is null then cast(newid() as nvarchar(255))  
				else  TRIM(VLDRM2)
				end AS VMXCRT_NO1 
			from PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a 
			join PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM b 
				on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
			where [VCMPNY] in (91,79,18,20)  AND trim(VTYPE) in ('NHIV','NLIV','NH','NL','NT')
		) a1 
	) a2 
	where a2.rn=1
) t1    -- where   t1.rn=1   --81167  86377

LEFT  join (
	SELECT 
		N'====>>>' as Different,a.id,b.VENDOR,VCMPNY,
		a.BankCode  
	from PLATFORM_ABBOTT.dbo.Vendors a  ,PLATFORM_ABBOTT.dbo.Vendor_tmp b
	where a.id = b.id 
) t2
 on t1.VENDOR =t2.VENDOR  and t1.VCMPNY= t2.VCMPNY
 where   t2.VENDOR is null or 
 t1.BankCode <> t2.BankCode
 
 
 

------ BankCode   供应商 去重逻辑检查 单条数据验证
select 
	VLDATE,vldate1, VLTIME,vcrdte1,  VCRDTE,VCTIME,CreationTime1,VCUSER,VCUSER1,
	count(*) over() aaaaa,
	* 
from (
	select * 
	from (
		SELECT  
			*,
			ROW_NUMBER () over(PARTITION by VMXCRT_NO1 order by COALESCE(nullif(vldate1,0),vcrdte1) desc,vcrdte1 desc, COALESCE(nullif(VLTIME,0),VCTIME)  desc ) as rn,
			FIRST_value(CONCAT(vcrdte1,VCTIME))over(PARTITION by VMXCRT_NO1 order by  vcrdte1,VCTIME  ) as CreationTime1,
			FIRST_value(VCUSER)over(PARTITION by VMXCRT_NO1 order by  vcrdte1,VCTIME  ) as VCUSER1

		from
		(
			SELECT a.*,b.VLDATE,b.VLTIME,b.VEXTNM,b.VCRDTE,b.VCTIME,VCUSER,
				CASE WHEN len(cast(vldate as VARCHAR(255)))<=6 and vldate <> 0 then cast(CONCAT('20',vldate) as int) else vldate end vldate1,
				CASE WHEN len(cast(VCRDTE as VARCHAR(255)))<=6 and VCRDTE <> 0 then cast(CONCAT('20',VCRDTE) as int) else VCRDTE end vcrdte1,
			 	case 
					when upper(trim(VTYPE)) in ('NHIV','NLIV') and VMXCRT  <>''   then  TRIM(REPLACE( REPLACE(VMXCRT,' F','') ,' M',''))   --判断个人供应商
				 when VMXCRT is null or VMXCRT  =''  or VLDRM2='' or VLDRM2 is null then cast(newid() as nvarchar(255))  
				else  TRIM(VLDRM2)
				end AS VMXCRT_NO1 
			from PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a 
			join PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM b 
				on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
			where [VCMPNY] in (91,79,18,20)  AND trim(VTYPE) in ('NHIV','NLIV','NH','NL','NT')
		) a1 
	) a2 
--	where a2.rn=1
) t1    -- where   t1.rn=1   --81167  86377
 where  TRIM(REPLACE( REPLACE(VMXCRT,' F','') ,' M',''))  in ('330225196511190099','11010819630703247x')
 

 --- BankCardNo 供应商 去重逻辑检查
 
 
select 
	count(*) over() aaaaa,*,t1.BankCardNo,t2.BankCardNo
--	* 
from (
	select * from (
		SELECT  
			*,
			ROW_NUMBER () over(PARTITION by VMXCRT_NO1 order by COALESCE(nullif(vldate1,0),vcrdte1) desc,vcrdte1 desc, COALESCE(nullif(VLTIME,0),VCTIME)  desc ) as rn
		from
		(
			SELECT a.*,b.VLDATE,b.VLTIME,b.VEXTNM,b.VCRDTE,b.VCTIME,b.VLDRM2 , VLDRM2 as BankCardNo,
				CASE WHEN len(cast(vldate as VARCHAR(255)))<=6 and vldate <> 0 then cast(CONCAT('20',vldate) as int) else vldate end vldate1,
				CASE WHEN len(cast(VCRDTE as VARCHAR(255)))<=6 and VCRDTE <> 0 then cast(CONCAT('20',VCRDTE) as int) else VCRDTE end vcrdte1,
			 	case 
					when upper(trim(VTYPE)) in ('NHIV','NLIV') and VMXCRT  <>''   then  TRIM(REPLACE( REPLACE(VMXCRT,' F','') ,' M',''))   --判断个人供应商
				 when VMXCRT is null or VMXCRT  =''  or VLDRM2='' or VLDRM2 is null then cast(newid() as nvarchar(255))  
				else  TRIM(VLDRM2)
				end AS VMXCRT_NO1 
			from PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a 
			join PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM b 
				on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
			where [VCMPNY] in (91,79,18,20)  AND trim(VTYPE) in ('NHIV','NLIV','NH','NL','NT')
		) a1 
	) a2 
	where a2.rn=1
) t1    -- where   t1.rn=1   --81167  86377

LEFT  join (
	SELECT 
		N'====>>>' as Different,a.id,b.VENDOR,VCMPNY,
		a.BankCardNo  
	from PLATFORM_ABBOTT.dbo.Vendors a  ,PLATFORM_ABBOTT.dbo.Vendor_tmp b
	where a.id = b.id 
) t2
 on t1.VENDOR =t2.VENDOR  and t1.VCMPNY= t2.VCMPNY
 where   t2.VENDOR is null or 
 t1.BankCardNo <> t2.BankCardNo

 
 

 
 
 
 
 ----  BankNo 验证 
 
 --- BankNo 供应商 去重逻辑检查
 
 
select 
	count(*) over() aaaaa,*,t1.BankNo,t2.BankNo
--	* 
from (
	select * from (
		SELECT  
			*,
			ROW_NUMBER () over(PARTITION by VMXCRT_NO1 order by COALESCE(nullif(vldate1,0),vcrdte1) desc,vcrdte1 desc, COALESCE(nullif(VLTIME,0),VCTIME)  desc ) as rn
		from
		(
			SELECT a.*,b.VLDATE,b.VLTIME,b.VEXTNM,b.VCRDTE,b.VCTIME , VMBNKA as BankNo,
				CASE WHEN len(cast(vldate as VARCHAR(255)))<=6 and vldate <> 0 then cast(CONCAT('20',vldate) as int) else vldate end vldate1,
				CASE WHEN len(cast(VCRDTE as VARCHAR(255)))<=6 and VCRDTE <> 0 then cast(CONCAT('20',VCRDTE) as int) else VCRDTE end vcrdte1,
			 	case 
					when upper(trim(VTYPE)) in ('NHIV','NLIV') and VMXCRT  <>''   then  TRIM(REPLACE( REPLACE(VMXCRT,' F','') ,' M',''))   --判断个人供应商
				 when VMXCRT is null or VMXCRT  =''  or VLDRM2='' or VLDRM2 is null then cast(newid() as nvarchar(255))  
				else  TRIM(VLDRM2)
				end AS VMXCRT_NO1 
			from PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a 
			join PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM b 
				on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
			where [VCMPNY] in (91,79,18,20)  AND trim(VTYPE) in ('NHIV','NLIV','NH','NL','NT')
		) a1 
	) a2 
	where a2.rn=1
) t1    -- where   t1.rn=1   --81167  86377

LEFT  join (
	SELECT 
		N'====>>>' as Different,a.id,b.VENDOR,VCMPNY,
		a.BankNo  
	from PLATFORM_ABBOTT.dbo.Vendors a  ,PLATFORM_ABBOTT.dbo.Vendor_tmp b
	where a.id = b.id 
) t2
 on t1.VENDOR =t2.VENDOR  and t1.VCMPNY= t2.VCMPNY
 where   t2.VENDOR is null or 
 t1.BankNo <> t2.BankNo
 
 ---[CreationTime] 验证  合并后账号需要取最早的创建时间 
 
select 
	count(*) over() aaaaa,*,t1.CreationTime,t2.CreationTime
--	* 
from (
	select * from (
		SELECT  
			*,
			ROW_NUMBER () over(PARTITION by VMXCRT_NO1 order by COALESCE(nullif(vldate1,0),vcrdte1) desc,vcrdte1 desc, COALESCE(nullif(VLTIME,0),VCTIME)  desc ) as rn,
--			FIRST_value(CONCAT(vcrdte1,VCTIME))over(PARTITION by VMXCRT_NO1 order by  vcrdte1,VCTIME  ) as CreationTime
			min(CONCAT(vcrdte1,VCTIME))over(PARTITION by VMXCRT_NO1 ) as CreationTime
--			FIRST_value(VCUSER)over(PARTITION by VMXCRT_NO1 order by  vcrdte1,VCTIME  ) as VCUSER1
		from
		(
			SELECT a.*,b.VLDATE,b.VLTIME,b.VEXTNM,b.VCRDTE,b.VCTIME , VMBNKA as BankNo,
				CASE WHEN len(cast(vldate as VARCHAR(255)))<=6 and vldate <> 0 then cast(CONCAT('20',vldate) as int) else vldate end vldate1,
				CASE WHEN len(cast(VCRDTE as VARCHAR(255)))<=6 and VCRDTE <> 0 then cast(CONCAT('20',VCRDTE) as int) else VCRDTE end vcrdte1,
			 	case 
					when upper(trim(VTYPE)) in ('NHIV','NLIV') and VMXCRT  <>''   then  TRIM(REPLACE( REPLACE(VMXCRT,' F','') ,' M',''))   --判断个人供应商
				 when VMXCRT is null or VMXCRT  =''  or VLDRM2='' or VLDRM2 is null then cast(newid() as nvarchar(255))  
				else  TRIM(VLDRM2)
				end AS VMXCRT_NO1 
			from PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a 
			join PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM b 
				on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
			where [VCMPNY] in (91,79,18,20)  AND trim(VTYPE) in ('NHIV','NLIV','NH','NL','NT')
		) a1 
	) a2 
	where a2.rn=1
) t1    -- where   t1.rn=1   --81167  86377

LEFT  join (
	SELECT 
		N'====>>>' as Different,a.id,b.VENDOR,VCMPNY,
		a.CreationTime  
	from PLATFORM_ABBOTT.dbo.Vendors a  ,PLATFORM_ABBOTT.dbo.Vendor_tmp b
	where a.id = b.id 
) t2
 on t1.VENDOR =t2.VENDOR  and t1.VCMPNY= t2.VCMPNY
 where   t2.VENDOR is null or 
 t1.CreationTime <> t2.CreationTime
 
 
 
 
 
 
 
--- [CreatorId] 验证 优先填入最早创建记录对应的员工ID

 

select 
	count(*) over() aaaaa,*  
--	,t1.CreatorId
	,t2.CreatorId
--	* 
from (
	select 
		a2.* 
		,b.spk_NexBPMCode  -- 最早用户id
		,c.ProcInstId ,ISNULL(cast(d.XmlContent as XML).value('(/root/SupplierApplication_agentBlock_MainStore/agentDeptId)[1]', 'NVARCHAR(255)'),'admin') as agentDeptId
--		case 
--			when  b.spk_NexBPMCode is null  then b.spk_NexBPMCode 
--			else ISNULL(cast(d.XmlContent as XML).value('(/root/SupplierApplication_agentBlock_MainStore/agentDeptId)[1]', 'NVARCHAR(255)'),'admin')
--		end as CreatorId
	from 
	(
		SELECT 
			*
		from 
		(
			SELECT  
				a1.*,
				ROW_NUMBER () over(PARTITION by VMXCRT_NO1 order by vnstat1 desc,COALESCE(nullif(vldate1,0),vcrdte1) desc,vcrdte1 desc, COALESCE(nullif(VLTIME,0),VCTIME)  desc ) as rn,
				min(CONCAT(vcrdte1,VCTIME))over(PARTITION by VMXCRT_NO1   ) as CreationTime,
				FIRST_value(VCUSER)over(PARTITION by VMXCRT_NO1 order by vcrdte1,VCTIME  ) as VCUSER1  -- 最早用户id
			from
			(
				SELECT a.*,b.VLDATE,b.VLTIME,b.VEXTNM,b.VCRDTE,b.VCTIME ,VCUSER, VMBNKA as BankNo,
					CASE WHEN len(cast(vldate as VARCHAR(255)))<=6 and vldate <> 0 then cast(CONCAT('20',vldate) as int) else vldate end vldate1,
					CASE WHEN len(cast(VCRDTE as VARCHAR(255)))<=6 and VCRDTE <> 0 then cast(CONCAT('20',VCRDTE) as int) else VCRDTE end vcrdte1,
				 	case 
						when upper(trim(VTYPE)) in ('NHIV','NLIV') and VMXCRT  <>''   then  TRIM(REPLACE( REPLACE(VMXCRT,' F','') ,' M',''))   --判断个人供应商
					 when VMXCRT is null or VMXCRT  =''  or VLDRM2='' or VLDRM2 is null then cast(newid() as nvarchar(255))  
					else  TRIM(VLDRM2)
					end AS VMXCRT_NO1,
					case when upper(vnstat)='A' THEN 1 else 0 end vnstat1
				from PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a 
				join PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM b 
					on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
				where [VCMPNY] in (91,79,18,20)  AND trim(VTYPE) in ('NHIV','NLIV','NH','NL','NT')
						-- and TRIM(REPLACE( REPLACE(VMXCRT,' F','') ,' M',''))  in ('330225196511190099','11010819630703247x')
			) a1 
		) tt where tt.rn=1
	) a2 
	left join PLATFORM_ABBOTT.dbo.spk_staffmasterdata b
 		on UPPER(b.spk_staffaccount)=upper(a2.VCUSER1) 
 	left join  
 	(
 		 SELECT  
  			vendor ,vcmpny,VNDNAM,min(ProcInstId) as ProcInstId -- 基于最小的ProcInstId优先填入最早创建记录对应的员工ID
		  from PLATFORM_ABBOTT.dbo.ODS_T_VendorMasterData_Log b1
		  --where  b.vendor=11266
		 group by vendor ,vcmpny,VNDNAM
 	) c
 	 	on a2.VENDOR=c.VENDOR and a2.VCMPNY=c.VCMPNY  and COALESCE(a2.VNDNAM,a2.VEXTNM)=c.VNDNAM and b.spk_NexBPMCode is null
 	
 	left join  PLATFORM_ABBOTT.dbo.ODS_T_FORMINSTANCE_GLOBAL  d 
		on d.ProcInstId=c.ProcInstId and b.spk_NexBPMCode is null  
) t1    -- where   t1.rn=1   --87717

LEFT  join (
	SELECT 
		N'====>>>' as Different,a.id,b.VENDOR,VCMPNY,
		a.CreatorId  
	from PLATFORM_ABBOTT.dbo.Vendors a  ,PLATFORM_ABBOTT.dbo.Vendor_tmp b
	where a.id = b.id 
) t2
 on t1.VENDOR =t2.VENDOR  and t1.VCMPNY= t2.VCMPNY
 where   t2.VENDOR is null or 
 t1.CreatorId <> t2.CreatorId
 
 
 ---------------------
 SELECT spk_staffaccount,count(*) from PLATFORM_ABBOTT.dbo.spk_staffmasterdata
 group by spk_staffaccount
 HAVING count(*)>1
 
 SELECT  vendor ,vcmpny,VNDNAM,count(*) from PLATFORM_ABBOTT.dbo.ODS_T_VendorMasterData_Log b
 group by vendor ,vcmpny,VNDNAM HAVING count(*)>1
 
	  SELECT  
	  	vendor ,vcmpny,VNDNAM,min(ProcInstId) as ProcInstId 
	  from PLATFORM_ABBOTT.dbo.ODS_T_VendorMasterData_Log b
	  where  b.vendor=11266
	  group by vendor ,vcmpny,VNDNAM
	  HAVING count(*)>1
 
 SELECT  VNDNAM,* from PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a where a.vendor=12139
 SELECT  VEXTNM, * from PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM b where VNDERX=12139
 
 SELECT  VNDNAM,* from PLATFORM_ABBOTT.dbo.ODS_T_VendorMasterData_Log b
 where b.vendor=11266
 
 SELECT * from PLATFORM_ABBOTT.dbo.ODS_T_FORMINSTANCE_GLOBAL group by ProcInstId HAVING  count(*)>1
 
 
-------------------------
 
 
 
 
 
 
 
--- [BankCity] 验证 

 

select 
	count(*) over() aaaaa,*  
--	,t1.CreatorId
	,t2.CreatorId
--	* 
from (
	select 
		a2.* 
		,b.spk_NexBPMCode  -- 最早用户id
		,c.ProcInstId ,ISNULL(cast(d.XmlContent as XML).value('(/root/SupplierApplication_agentBlock_MainStore/agentDeptId)[1]', 'NVARCHAR(255)'),'admin') as agentDeptId
--		case 
--			when  b.spk_NexBPMCode is null  then b.spk_NexBPMCode 
--			else ISNULL(cast(d.XmlContent as XML).value('(/root/SupplierApplication_agentBlock_MainStore/agentDeptId)[1]', 'NVARCHAR(255)'),'admin')
--		end as CreatorId
	from 
	(
		SELECT 
			*
		from 
		(
			SELECT  
				a1.*,
				ROW_NUMBER () over(PARTITION by VMXCRT_NO1 order by vnstat1 desc,COALESCE(nullif(vldate1,0),vcrdte1) desc,vcrdte1 desc, COALESCE(nullif(VLTIME,0),VCTIME)  desc ) as rn,
				min(CONCAT(vcrdte1,VCTIME))over(PARTITION by VMXCRT_NO1   ) as CreationTime,
				FIRST_value(VCUSER)over(PARTITION by VMXCRT_NO1 order by vcrdte1,VCTIME  ) as VCUSER1  -- 最早用户id
			from
			(
				SELECT a.*,b.VLDATE,b.VLTIME,b.VEXTNM,b.VCRDTE,b.VCTIME ,VCUSER, VMBNKA as BankNo,
					CASE WHEN len(cast(vldate as VARCHAR(255)))<=6 and vldate <> 0 then cast(CONCAT('20',vldate) as int) else vldate end vldate1,
					CASE WHEN len(cast(VCRDTE as VARCHAR(255)))<=6 and VCRDTE <> 0 then cast(CONCAT('20',VCRDTE) as int) else VCRDTE end vcrdte1,
				 	case 
						when upper(trim(VTYPE)) in ('NHIV','NLIV') and VMXCRT  <>''   then  TRIM(REPLACE( REPLACE(VMXCRT,' F','') ,' M',''))   --判断个人供应商
					 when VMXCRT is null or VMXCRT  =''  or VLDRM2='' or VLDRM2 is null then cast(newid() as nvarchar(255))  
					else  TRIM(VLDRM2)
					end AS VMXCRT_NO1,
					case when upper(vnstat)='A' THEN 1 else 0 end vnstat1
				from PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a 
				join PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM b 
					on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
				where [VCMPNY] in (91,79,18,20)  AND trim(VTYPE) in ('NHIV','NLIV','NH','NL','NT')
						-- and TRIM(REPLACE( REPLACE(VMXCRT,' F','') ,' M',''))  in ('330225196511190099','11010819630703247x')
			) a1 
		) tt where tt.rn=1
	) a2 
 	left join  
 	(
	  SELECT 
	  	cast(vendorNumber as int) vendorNumber,company_Value,supplierCNName,max(ProcInstid) as ProcInstid   -- 对于疑似相同的重复供应商，或可能进行过重复申请的供应商，基于最大的ProcInstId优先填入最新创建供应商对应的值
	  from  PLATFORM_ABBOTT.dbo.ods_AUTO_BIZ_T_SupplierApplication_Info c
	  where vendorNumber is not null  and vendorNumber<>''
	  group by vendorNumber,company_Value,supplierCNName  
 	) c
 	 	on a2.VENDOR=c.vendorNumber and a2.VCMPNY=c.company_Value  and COALESCE(a2.VNDNAM,a2.VEXTNM)=c.supplierCNName 
 	LEFT JOIN 
 	(
 		SELECT 
 			ProcInstid 
 		from PLATFORM_ABBOTT.dbo.ods_Form_7a708c9568fb444a884eb5eca658975f d
		where processstatus=N'终止(系统)'
		group by ProcInstid
 	) b
 	left join  PLATFORM_ABBOTT.dbo.ODS_T_FORMINSTANCE_GLOBAL  d 
		on b.ProcInstId=c.ProcInstId    
) t1    -- where   t1.rn=1   --81167  86377

LEFT  join (
	SELECT 
		N'====>>>' as Different,a.id,b.VENDOR,VCMPNY,
		a.BankCity  
	from PLATFORM_ABBOTT.dbo.Vendors a  ,PLATFORM_ABBOTT.dbo.Vendor_tmp b
	where a.id = b.id 
) t2
 on t1.VENDOR =t2.VENDOR  and t1.VCMPNY= t2.VCMPNY
 where   t2.VENDOR is null or 
 t1.CreatorId <> t2.CreatorId
 
 --------------
-- "银行城市，根据[AVM],[PMFVM]及[AUTO_BIZ_T_SupplierApplication_Info]
--表中[VENDOR=vendorNumber]+[VCMPNY=company_Value]+[VEXTNM=supplierCNName]
--(或[AVM].[VNDNAM]=[SupplierCNName])完全匹配成功作为条件，定位出对应的单据ID即[ProcInstId]，
--再到[Form_7a708c9568fb444a884eb5eca658975f]中查询[processstatus]为""终止(系统)""/""已完成""的记录对应[ProcInstId]，
--使用该ID查询[T_FORMINSTANCE_GLOBAL].[ProcInstid]，在查询出的[XmlContent]中找到标记为[bankCity]的值，
--根据这个值匹配PP中的市级主数据得到银行所在省市信息(匹配时可以尝试以原值或原值加上""市""后匹配)，如果匹配失败则留空
--(对于疑似相同的重复供应商，或可能进行过重复申请的供应商，基于最大的ProcInstId优先填入最新创建供应商对应的值)"
 SELECT vendorNumber,company_Value,supplierCNName,ProcInstid from  PLATFORM_ABBOTT.dbo.ods_AUTO_BIZ_T_SupplierApplication_Info c
 where vendorNumber =10125
 
  SELECT 
  	vendorNumber,company_Value,supplierCNName,max(ProcInstid) as ProcInstid 
  from  PLATFORM_ABBOTT.dbo.ods_AUTO_BIZ_T_SupplierApplication_Info c
  where vendorNumber =10125
  group by vendorNumber,company_Value,supplierCNName  

	00054	91	深圳市至尊汽车租赁有限公司	135385
	00057	91	上海医药物流中心有限公司	168259
	00089	91	北京京海康佰馨医药有限责任公司	258768
 SELECT * from 	 PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a where vendor=89
 
SELECT ProcInstid from PLATFORM_ABBOTT.dbo.ods_Form_7a708c9568fb444a884eb5eca658975f d
where processstatus=N'终止(系统)'
group by ProcInstid
HAVING count(*)>1

1921488
 SELECT  XmlContent from PLATFORM_ABBOTT.dbo.ODS_T_FORMINSTANCE_GLOBAL WHERE ProcInstId = 1921488