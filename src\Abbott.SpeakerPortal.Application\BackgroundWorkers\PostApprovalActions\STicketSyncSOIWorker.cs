﻿using Abbott.SpeakerPortal.Contracts.PostApprovalActions;
using Abbott.SpeakerPortal.Contracts.STicket;
using Hangfire;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.BackgroundWorkers.PostApprovalActions
{
    internal class STicketSyncSOIWorker : SpeakerPortalBackgroundWorkerBase
    {
        public STicketSyncSOIWorker()
        {
            //触发周期,每天凌晨1点
            CronExpression = "*/2 * * * *";
        }

        public override async Task DoWorkAsync(CancellationToken cancellationToken = default)
        {
            await LazyServiceProvider.LazyGetService<IPostApprovalActionService>().IntegrationAsync();
        }
    }
}
