﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Authorization;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Security.Claims;

namespace Abbott.SpeakerPortal.AppServices
{
    /// <summary>
    /// 改写鉴权方法，实现多个Authorize特性，以“或”的方式进行鉴权
    /// </summary>
    [Dependency(ReplaceServices = true)]
    [ExposeServices(typeof(IAuthorizationService), typeof(IAbpAuthorizationService))]
    public class MultipleAuthorizationService : AbpAuthorizationService, ITransientDependency
    {
        public MultipleAuthorizationService(IAuthorizationPolicyProvider policyProvider, IAuthorizationHandlerProvider handlers, ILogger<DefaultAuthorizationService> logger, IAuthorizationHandlerContextFactory contextFactory, IAuthorizationEvaluator evaluator, IOptions<AuthorizationOptions> options, ICurrentPrincipalAccessor currentPrincipalAccessor, IServiceProvider serviceProvider)
            : base(policyProvider, handlers, logger, contextFactory, evaluator, options, currentPrincipalAccessor, serviceProvider)
        {
        }
        /// <summary>
        /// 重写授权方法，将策略从需求规则中拆分出来，单独鉴权
        /// </summary>
        /// <param name="user"></param>
        /// <param name="resource"></param>
        /// <param name="requirements"></param>
        /// <returns></returns>
        public override async Task<AuthorizationResult> AuthorizeAsync(ClaimsPrincipal user, object? resource, IEnumerable<IAuthorizationRequirement> requirements)
        {
            //如果权限策略为空，则直接返回成功
            if (requirements.Count() == 0)
            {
                return AuthorizationResult.Success();
            }
            //权限策略需求规则
            var permissionRequirements = requirements.Where(p => p.ToString()!.StartsWith("PermissionRequirement:")).ToList();

            if (permissionRequirements.Count > 0)
            {
                //从需求规则中剔除权限策略
                requirements = requirements.Where(p => !permissionRequirements.Any(q => q.Equals(p))).ToList();
            }

            //校验除权限策略外的其他需求规则
            if (requirements.Count() > 0)
            {
                var result = await base.AuthorizeAsync(user, resource, requirements);
                //如果校验失败，则直接返回，无需再校验权限规则
                if (!result.Succeeded)
                {
                    return result;
                }
            }

            //校验权限策略规则 
            if (permissionRequirements.Count > 0)
            {
                var result = await base.AuthorizeAsync(user, resource, permissionRequirements);
                //校验不通过，则检查是否全部策略都不通过，如果不是，则认为通过校验，改写校验结果 
                if (!result.Succeeded && result.Failure!.FailedRequirements.Count() < permissionRequirements.Count)
                {
                    result = AuthorizationResult.Success();
                }
                return result;
            }
            return AuthorizationResult.Success();
        }
    }
}
