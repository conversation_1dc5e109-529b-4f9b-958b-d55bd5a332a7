
CREATE PROCEDURE dbo.sp_PurPRApplications_ns
AS 
BEGIN
	/*
问题1：ActiveType生成的GUID,不是会议类型GUID
问题2：IsSeriesMeeting匹配的数据和系列会无关
问题3：MeetingType如何匹配采购申请的ID
问题3：公司主数据spk_abbrcode没有回导值，导致 CompanyShortName无值
*/
	
select  
a.ID,
a.ApplicationCode,
a.Status,
UPPER(ss.spk_NexBPMCode) as ApplyUserId,
a.ApplyTime,
UPPER(soc.spk_NexBPMCode) as ApplyUserBu,
UPPER(sc.spk_NexBPMCode) as CostCenter,
UPPER(b.ID)BudgetId,
a.MeetingTitle,
a.MeetingNo,
a.IsEsignUsed,
a.PushSystem,
UPPER(c.spk_NexBPMCode) as AgentId,
a.HostVendorId,
UPPER(d.spk_NexBPMCode) as ExpenseType,
UPPER(e.spk_NexBPMCode) as BudgetRegion,
UPPER(p.spk_NexBPMCode) as ProductIds,
UPPER(o.spk_NexBPMCode) as CompanyId,
a.TotalAmount,
a.Remark,
a.PrLateDescription,
UPPER(pr.spk_NexBPMCode) as ActiveLaunchCityId,
UPPER(pr1.spk_NexBPMCode) as ActiveHostCityId,
a.AcitveHostAddress,
a.AcitveDate,
UPPER(dic.spk_code) as ActiveType,
UPPER(dict.spk_code) as ProjectType,
a.DoctorsNum,
UPPER(dicti.spk_code) as MeetingType,
UPPER(di1.spk_NexBPMCode) as IsSeriesMeeting,
a.MainMeetingPR,
UPPER(di2.spk_code) as OrganizerNature,
a.SponsorshipType,
a.ActiveLeader,
a.MeetingName,
a.OrganizerName,
a.MeetingDate,
a.MeetingLocation,
a.NumberOfProfessionals,
a.ChoiceReason,
a.SupportReason,
a.RequireMediaReason,
a.AttendeeExpertise,
a.MarketResearchLeader,
a.MarketResearchCompany,
a.MarketResearchReason,
a.MarketResearchResult,
a.MarketResearchResultUsedFor,
a.PatientInfo,
a.SPChoiceAndPayStandard,
a.SupportFiles,
a.ExtraProperties,
a.ConcurrencyStamp,
a.CreatorId as CreationTime,
UPPER(ss1.spk_NexBPMCode) as CreatorId,
a.LastModificationTime,
a.LastModifierId,
a.IsDeleted,
a.DeleterId,
a.DeletionTime,
a.AdditionalFiles,
a.HospitalDepartments,
a.Hospitals,
a.IsIncludeHcpTravelLodgingFee,
UPPER(soc2.spk_Name) as ApplyUserBuName,
a.ActiveNo,
UPPER(soc1.spk_NexBPMCode) as ApplyUserDept,
a.ApplyUserDeptName,
ss5.spk_name as AgentIdName,
a.ApplyUserIdName,
a.ApprovedDate,
a.BudgetCode,
a.BudgetRegionName,
a.ClosedDate,
a.CompanyCode,
a.CompanyIdName,
UPPER(co.spk_abbrcode) as CompanyShortName,
a.CostCenterCode,
a.CostCenterName,
UPPER(d1.spk_NexBPMCode) as ExpenseTypeCode,
a.ExpenseTypeName,
a.HostVendorIdName,
a.ProductIdsName,
a.SavingAmount,
a.SubBudgetCode,
UPPER(bd.id) as SubBudgetId,--还没做
a.AgentEmail,
a.HostVendorEmail,
a.MeetingStatus,
a.IsShowExpenseStep,
a.OecExceptionNumber,
a.TotalAmountRMB
into #PurPRApplications
from PLATFORM_ABBOTT_Stg.dbo.PurPRApplications_tmp a--543891
left join PLATFORM_ABBOTT_Stg.dbo.spk_staffmasterdata ss 
on a.ApplyUserId =ss.bpm_id 
left join PLATFORM_ABBOTT_Stg.dbo.spk_organizationalmasterdata soc 
on a.ApplyUserBu =soc.spk_BPMCode 
left join PLATFORM_ABBOTT_Stg.dbo.spk_costcentermasterdata sc
on a.CostCenter = sc.spk_BPMCode 
left join PLATFORM_ABBOTT_Stg.dbo.BdMasterBudgets_tmp b
on a.BudgetId =b.Code
left join PLATFORM_ABBOTT_Stg.dbo.spk_staffmasterdata c 
on a.AgentId =c.bpm_id 
left join PLATFORM_ABBOTT_Stg.dbo.spk_consume d
on a.ExpenseType =d.spk_BPMCode 
left join PLATFORM_ABBOTT_Stg.dbo.spk_districtmasterdata e
on a.BudgetRegion  =e.spk_BPMCode 
left join PLATFORM_ABBOTT_Stg.dbo.spk_productmasterdata p
on a.ProductIds = p.spk_BPMCode --543891
left join PLATFORM_ABBOTT_Stg.dbo.spk_companymasterdata o
on a.CompanyId =o.spk_CompanyCode --543891
left join (select *,ROW_NUMBER () over(PARTITION by spk_name order by spk_name) rn from PLATFORM_ABBOTT_Stg.dbo.spk_city) ci
on SUBSTRING(a.ActiveLaunchCityId,1,3) = SUBSTRING(ci.spk_name ,1,3)  and ci.rn=1--543891
left join PLATFORM_ABBOTT_Stg.dbo.spk_province pr
on ci.spk_provincename = pr.spk_name --544590
left join (select *,ROW_NUMBER () over(PARTITION by spk_name order by spk_name) rn from PLATFORM_ABBOTT_Stg.dbo.spk_city) ci1
on SUBSTRING(a.ActiveHostCityId,1,3) = SUBSTRING(ci1.spk_name ,1,3) and ci1.rn=1
left join PLATFORM_ABBOTT_Stg.dbo.spk_province pr1
on ci1.spk_provincename = pr1.spk_name 
left join PLATFORM_ABBOTT_Stg.dbo.spk_dictionary dic
on a.ActiveType =dic.spk_BPMCode and (dic.spk_BPMCode <> null or dic.spk_BPMCode<>'')
left join PLATFORM_ABBOTT_Stg.dbo.spk_dictionary dict
on a.ProjectType COLLATE SQL_Latin1_General_CP1_CI_AS =dict.spk_BPMCode  and (dict.spk_BPMCode <> null or dict.spk_BPMCode<>'')
left join PLATFORM_ABBOTT_Stg.dbo.spk_dictionary dicti
on a.MeetingType  =dicti.spk_BPMCode and (dicti.spk_BPMCode <> null or dicti.spk_BPMCode<>'')
left join PLATFORM_ABBOTT_Stg.dbo.spk_dictionary di1
on a.IsSeriesMeeting  =di1.spk_Name 
left join PLATFORM_ABBOTT_Stg.dbo.spk_dictionary di2
on a.OrganizerNature  COLLATE SQL_Latin1_General_CP1_CI_AS =di2.spk_Name and di2.spk_type=N'主办方性质'
left join PLATFORM_ABBOTT_Stg.dbo.spk_organizationalmasterdata soc1 
on a.ApplyUserDept =soc1.spk_BPMCode 
left join PLATFORM_ABBOTT_Stg.dbo.spk_staffmasterdata ss1 
on a.CreationTime =ss1.bpm_id 
left join PLATFORM_ABBOTT_Stg.dbo.spk_organizationalmasterdata soc2 
on a.ApplyUserBuName =soc2.spk_BPMCode 
left join PLATFORM_ABBOTT_Stg.dbo.spk_companymasterdata co
on a.CompanyShortName =co.spk_CompanyCode
left join PLATFORM_ABBOTT_Stg.dbo.spk_consume d1
on a.ExpenseTypeCode =d1.spk_BPMCode 
left join PLATFORM_ABBOTT_Stg.dbo.BdSubBudgets_tmp  bd
on a.SubBudgetId=bd.Code
left join PLATFORM_ABBOTT_Stg.dbo.spk_staffmasterdata ss5 
on a.AgentIdName =ss5.bpm_id 


IF OBJECT_ID(N'PLATFORM_ABBOTT_Stg.dbo.PurPRApplications', N'U') IS NOT NULL
BEGIN
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
    drop table PLATFORM_ABBOTT_Stg.dbo.PurPRApplications
    select *
    into PLATFORM_ABBOTT_Stg.dbo.PurPRApplications from #PurPRApplications
	END
	ELSE
	BEGIN
    --落成实体表
    select *
    into PLATFORM_ABBOTT_Stg.dbo.PurPRApplications from #PurPRApplications
    -- select * from #InteOnlineMeetingSettlement_tmp
	  PRINT(N' 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END



END;