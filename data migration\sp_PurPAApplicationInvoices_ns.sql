CREATE PROCEDURE dbo.sp_PurPAApplicationInvoices_ns
AS 
BEGIN
	SELECT 
A.[Id]
,ppt.id as [PurPAApplicationId]
,A.[InvoiceCode]
,A.[InvoiceDate]
,<PERSON><PERSON>[InvoiceTotalAmount]
,<PERSON><PERSON>[TaxAmount]
,<PERSON><PERSON>[ExcludingTaxAmount]
,A<PERSON>[ExtraProperties]
,A.[ConcurrencyStamp]
,A.[CreationTime]
,ss.spk_NexBPMCode as [CreatorId]
,A.[LastModificationTime]
,A.[LastModifierId]
,A.[IsDeleted]
,A.[DeleterId]
,A.[DeletionTime]
,A.[InvoiceType]
,A.[TaxRate]
into #PurPAApplicationInvoices
FROM PurPAApplicationInvoices_tmp A    --196426
left join (select *,ROW_NUMBER () over(PARTITION by ApplicationCode order by ProcInstId desc) rn from PLATFORM_ABBOTT_Stg.dbo.PurPAApplications_tmp ) ppt
ON A.PurPAApplicationId=PPT.ApplicationCode  and ppt.rn = 1  --196442, ApplicationCode重复
left join spk_staffmasterdata ss 
on ss.bpm_id = a.CreatorId

IF OBJECT_ID(N'PLATFORM_ABBOTT_Stg.dbo.PurPAApplicationInvoices', N'U') IS NOT NULL
BEGIN
	drop table PLATFORM_ABBOTT_Stg.dbo.PurPAApplicationInvoices
	select  *  into PLATFORM_ABBOTT_Stg.dbo.PurPAApplicationInvoices from #PurPAApplicationInvoices
	PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into PLATFORM_ABBOTT_Stg.dbo.PurPAApplicationInvoices from #PurPAApplicationInvoices
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END 

END

