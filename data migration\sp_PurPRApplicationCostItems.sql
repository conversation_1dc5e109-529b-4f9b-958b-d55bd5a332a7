CREATE PROCEDURE dbo.sp_PurPRApplicationCostItems
AS 
BEGIN
	--初始化xml
IF OBJECT_ID(N'PLATFORM_ABBOTT_stg.dbo.XML_1', N'U') IS NOT NULL
BEGIN
	PRINT(N'已经初始化过'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select * into PLATFORM_ABBOTT_STG.dbo.XML_1 
	from (
		select   fg.ProcInstId,
		rowdata.value('(Project/text())[1]', 'nvarchar(50)')  Project,
		rowdata.value('(ApproximateCost/text())[1]', 'nvarchar(100)')  ApproximateCost
		from (SELECT
				cast (XmlContent as XML) as  XmlContent,
				ProcInstId
				FROM PLATFORM_ABBOTT_STG.dbo.ODS_T_FORMINSTANCE_GLOBAL
			) fg 
CROSS APPLY XmlContent.nodes('/root/ThirdGridPanel/row') AS XMLTable(rowdata)
) b
PRINT(N'初始化完成'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
--创建临时表#ABTPI
PRINT N'创建临时表#ABTPI'
select * into #ABTPI from (
	select  ROW_NUMBER() over( PARTITION by ProcInstId  order by expenseCategory_Text desc) as a,* 
	from PLATFORM_ABBOTT_Stg.dbo.ODS_AUTO_BIZ_T_ProcurementApplication_Info )B 
	where a=1
PRINT(N'创建临时表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
--创建临时表#PurPRApplicationCostItems_tmp
select newid() AS Id,--自动生成的uuid into 
* into #PurPRApplicationCostItems_tmp  from (
select 
DISTINCT
a.ProcInstId,
t.ID AS PRApplicationId,--基于03-1迁移的申请单主信息，以ProcInstId定位对应的PurPRApplications.ID
c.Project AS SupportItem,--原BPM支持项目并非从字典中选择，而是人工填写，因此支持项目字典不应该基于历史填入的内容进行补充，此处的查询逻辑也需要调整
c.ApproximateCost AS EstimateCost,--
'{}' AS ExtraProperties,--默认填写为"{}"
'' AS ConcurrencyStamp,--?
CreationTime AS CreationTime,--与对应的PurPRApplications记录保持一致即可
CreatorId AS CreatorId,--与对应的PurPRApplications记录保持一致即可
'' AS LastModificationTime,--默认为空
'' AS LastModifierId,--默认为空
'0' AS IsDeleted,--默认为0
'' AS DeleterId,--默认为空
'' AS DeletionTime,--默认为空
c.Project AS ItemCategory--原BPM支持项目并非从字典中选择，而是人工填写，因此支持项目字典不应该基于历史填入的内容进行补充，此处的查询逻辑也需要调整
from #ABTPI as A
--left join PLATFORM_ABBOTT_Stg.dbo.ods_AUTO_BIZ_T_ProcurementApplication_Info_PR PR
--on  a.ProcInstId =PR.ProcInstId
left join PLATFORM_ABBOTT_Stg.dbo.PurPRApplications_tmp t
on A.ProcInstID=t.ProcInstID
left join 
(select 
 ProcInstId
,Project
,sum(cast(ApproximateCost as decimal)) as ApproximateCost
from PLATFORM_ABBOTT_Stg.dbo.xml_1
group by 
 ProcInstId
,Project) c
on a.ProcInstId=c.ProcInstId
)A

IF OBJECT_ID(N'PLATFORM_ABBOTT_Stg.dbo.PurPRApplicationCostItems_tmp', N'U') IS NOT NULL
BEGIN
	UPDATE a
	SET 
	 a.ProcInstId = b.ProcInstId
	,a.PRApplicationId = b.PRApplicationId
	,a.SupportItem = b.SupportItem
	,a.EstimateCost = b.EstimateCost
	--,a.ExtraProperties = b.ExtraProperties
	--,a.ConcurrencyStamp = b.ConcurrencyStamp
	,a.CreationTime = b.CreationTime
	,a.CreatorId = b.CreatorId
	--,a.LastModificationTime = b.LastModificationTime
	--,a.LastModifierId = b.LastModifierId
	--,a.IsDeleted = b.IsDeleted
	--,a.DeleterId = b.DeleterId
	--,a.DeletionTime = b.DeletionTime
	,a.ItemCategory = b.ItemCategory
	FROM PLATFORM_ABBOTT_Stg.dbo.PurPRApplicationCostItems_tmp a
	LEFT JOIN #PurPRApplicationCostItems_tmp b
	ON isnull(a.ProcInstId,1) = isnull(b.ProcInstId,1)
	AND isnull(a.SupportItem,'null') = isnull(b.SupportItem,'null')
	
	INSERT INTO PLATFORM_ABBOTT_Stg.dbo.PurPRApplicationCostItems_tmp
	(
	 Id
	,ProcInstId
	,PRApplicationId
	,SupportItem
	,EstimateCost
	,ExtraProperties
	,ConcurrencyStamp
	,CreationTime
	,CreatorId
	,LastModificationTime
	,LastModifierId
	,IsDeleted
	,DeleterId
	,DeletionTime
	,ItemCategory
	)
	SELECT
	 Id
	,ProcInstId
	,PRApplicationId
	,SupportItem
	,EstimateCost
	,ExtraProperties
	,ConcurrencyStamp
	,CreationTime
	,CreatorId
	,LastModificationTime
	,LastModifierId
	,IsDeleted
	,DeleterId
	,DeletionTime
	,ItemCategory
	FROM #PurPRApplicationCostItems_tmp a
	WHERE not exists (select * from PLATFORM_ABBOTT_Stg.dbo.PurPRApplicationCostItems_tmp where isnull(a.ProcInstId,1) = isnull(ProcInstId,1)
	AND isnull(a.SupportItem,'null') = isnull(SupportItem,'null'))
	PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into PLATFORM_ABBOTT_Stg.dbo.PurPRApplicationCostItems_tmp from #PurPRApplicationCostItems_tmp
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END

END;
