SELECT
 TRY_CONVERT(UNIQUEIDENTIFIER, [Id]) [Id]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([PRApplicationId],'00000000-0000-0000-0000-000000000000'))[PRApplicationId]
,[SerialNumber]
,NO AS [No]
,[VendorCode]
,[PdfUrl]
,PayAmount AS [PayAmount]
,[ModifyRemark]
,[ModifyAmountRemark]
,[Executive]
,[ExecutiveMail]
,StartDate AS [StartDate]
,iif(ActualNumber is null or ActualNumber ='',0,ActualNumber) AS [ActualNumber]
,'NULL' AS [ConcurrencyStamp]
,iif(CreationTime is null or CreationTime ='',GETDATE() ,CreationTime) AS [CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CreatorId]) [CreatorId]
,TRY_CONVERT(UNIQUEIDENTIFIER, [DeleterId]) [DeleterId]
,GETDATE() AS [DeletionTime]
,'{}' AS [ExtraProperties]
,0 AS [IsDeleted]
,GETDATE() AS [LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [LastModifierId]) [LastModifierId]
,TRY_CONVERT(UNIQUEIDENTIFIER, '00000000-0000-0000-0000-000000000000') [PRDetailId]
INTO #InteOnlineMeetingSettlement
FROM (SELECT DISTINCT *,ROW_NUMBER() OVER(PARTITION BY Id ORDER BY Id) RK FROM PLATFORM_ABBOTT_STG.dbo.InteOnlineMeetingSettlement)a
WHERE RK = 1



--drop table #InteOnlineMeetingSettlement
USE Speaker_Portal_STG;

UPDATE a
SET 
 a.[Id] = b.[Id]
,a.[PRApplicationId] = b.[PRApplicationId]
,a.[SerialNumber] = b.[SerialNumber]
,a.[No] = b.[No]
,a.[VendorCode] = b.[VendorCode]
,a.[PdfUrl] = b.[PdfUrl]
,a.[PayAmount] = b.[PayAmount]
,a.[ModifyRemark] = b.[ModifyRemark]
,a.[ModifyAmountRemark] = b.[ModifyAmountRemark]
,a.[Executive] = b.[Executive]
,a.[ExecutiveMail] = b.[ExecutiveMail]
,a.[StartDate] = b.[StartDate]
,a.[ActualNumber] = b.[ActualNumber]
,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = b.[CreatorId]
,a.[DeleterId] = b.[DeleterId]
,a.[DeletionTime] = b.[DeletionTime]
,a.[ExtraProperties] = b.[ExtraProperties]
,a.[IsDeleted] = b.[IsDeleted]
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = b.[LastModifierId]
,a.[PRDetailId] = b.[PRDetailId]
FROM dbo.InteOnlineMeetingSettlement a
left join #InteOnlineMeetingSettlement  b
ON a.id=b.id


--select * from #InteOnlineMeetingSettlement where PRApplicationId is null


INSERT INTO dbo.InteOnlineMeetingSettlement
SELECT
 [Id]
,[PRApplicationId]
,[SerialNumber]
,[No]
,[VendorCode]
,[PdfUrl]
,[PayAmount]
,[ModifyRemark]
,[ModifyAmountRemark]
,[Executive]
,[ExecutiveMail]
,[StartDate]
,[ActualNumber]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[DeleterId]
,[DeletionTime]
,[ExtraProperties]
,[IsDeleted]
,[LastModificationTime]
,[LastModifierId]
,[PRDetailId]
FROM #InteOnlineMeetingSettlement a
WHERE not exists (select * from dbo.InteOnlineMeetingSettlement where id=a.id)



--select
--DeletionTime
--,LastModificationTime
--,* from #InteOnlineMeetingSettlement
--where DeletionTime = 'NULL'



