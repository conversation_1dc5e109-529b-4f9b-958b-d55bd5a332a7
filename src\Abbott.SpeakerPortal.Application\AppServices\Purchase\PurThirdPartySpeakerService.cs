﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.OEC.SpeakerLimit;
using Abbott.SpeakerPortal.Contracts.Purchase.PurThirdPartySpeaker;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.Budget;
using Abbott.SpeakerPortal.Entities.OECPSAComPSALimits;
using Abbott.SpeakerPortal.Entities.OECPSASpeakerLimitExtras;
using Abbott.SpeakerPortal.Entities.OECPSASpeakerLimitUseHistorys;
using Abbott.SpeakerPortal.Entities.Purchase.PurGRApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurThirdPartySpeaker;
using Abbott.SpeakerPortal.Entities.VendorPersonals;
using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.OEC.SpeakerLimit;
using Abbott.SpeakerPortal.User;
using Abbott.SpeakerPortal.Utils;

using ClosedXML.Excel;
using MiniExcelLibs;
using MiniExcelLibs.Attributes;
using MiniExcelLibs.OpenXml;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Data;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using static Abbott.SpeakerPortal.Enums.Purchase;
using Abbott.SpeakerPortal.Extension;
using Abbott.SpeakerPortal.Person;

namespace Abbott.SpeakerPortal.AppServices.Purchase
{
    /// <summary>
    /// 第三方讲者
    /// </summary>
    public class PurThirdPartySpeakerService : SpeakerPortalAppService, IPurThirdPartySpeakerService
    {
        /// <summary>
        /// 获取第三方讲者汇总列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetThirdPartySpeakerSummaryResponseDto>> GetThirdPartySpeakerSummaryAsync(GetThirdPartySpeakerSummaryRequestDto request, bool isPage = true)
        {
            var queryPr = await LazyServiceProvider.LazyGetService<IPurPRApplicationReadonlyRepository>().GetQueryableAsync();
            var queryThirdPartySpeaker = await LazyServiceProvider.LazyGetService<IPurThirdPartySpeakerItemReadonlyRepository>().GetQueryableAsync();
            //var (filter, needFilter) = await GetFilterByRole();
            //查询消费大类为教育资助（成本支持），并且有曾经审批通过的pr申请单数据
            var query = queryPr.Where(a => a.ExpenseTypeCode == ExpenseTypeCodeConst.COSTSUPPORT && a.IsOnceApproved)
                //.WhereIf(needFilter, a => (filter.ContainsKey("Applicant") ? filter["Applicant"].Contains(a.ApplyUserId) : false)
                //                   || (filter.ContainsKey(RoleNames.Approver) ? filter[RoleNames.Approver].Contains(a.ApplyUserDept) : false)
                //                   || (filter.ContainsKey(RoleNames.DeptFinance) ? filter[RoleNames.DeptFinance].Contains(a.ApplyUserBu) : false)
                //                   || (filter.ContainsKey(RoleNames.DeptAdmin) ? filter[RoleNames.DeptAdmin].Contains(a.ApplyUserBu) : false)
                //                   || (filter.ContainsKey(RoleNames.BudgetOwner) ? filter[RoleNames.BudgetOwner].Contains(a.SubBudgetId.Value) : false)
                //    )
                .WhereIf(!string.IsNullOrEmpty(request.PrApplicationCode), a => a.ApplicationCode.Contains(request.PrApplicationCode))
                .WhereIf(!string.IsNullOrEmpty(request.ApplyDeptName), a => a.ApplyUserDeptName.Contains(request.ApplyDeptName))
                .WhereIf(!string.IsNullOrEmpty(request.ApplicantName), a => a.ApplyUserIdName.Contains(request.ApplicantName))
                .WhereIf(request.ApplicatId != null, a => a.ApplyUserId == request.ApplicatId)
                .Select(a => new
                {
                    a.Id,
                    a.ApplicationCode,
                    a.ApplyUserDeptName,
                    a.ApplyTime,
                    a.ApplyUserIdName,
                    a.ApplyUserBu,
                    a.ApplyUserDept,
                    a.ApplyUserId,
                    a.SubBudgetId,
                    a.TransfereeId,
                    a.AgentId,
                    a.TotalAmountRMB,
                    SpeakerCount = queryThirdPartySpeaker.Where(a1 => a1.PrId == a.Id).Count(),
                    TotalCount = queryThirdPartySpeaker.Where(a1 => a1.PrId == a.Id).Sum(a1 => a1.Times),
                    TotalAmount = queryThirdPartySpeaker.Where(a1 => a1.PrId == a.Id).Sum(a1 => a1.Amount),
                    a.Status
                });
            var personCenterService = LazyServiceProvider.LazyGetService<IPersonCenterService>();
            var rolesIds = await personCenterService.GetIdsByRoleLevels(bizTypeCategory: ResignationTransfer.TaskFormCategory.PurchaseRequestApplication);
            query = query.ApplyRoleFilters(rolesIds, CurrentUser.Id.Value, x => x.ApplyUserBu, x => x.ApplyUserDept, x => x.SubBudgetId, x => x.ApplyUserId, x => x.TransfereeId, x => x.AgentId);

            var allStatus = EnumUtil.GetEnumIdValues<PurPRApplicationStatus>();

            var count = query.Count();
            var datas = query.OrderByDescending(a => a.ApplicationCode)
                .PagingIf(request, isPage)
                .ToArray()
                .Select(a => new GetThirdPartySpeakerSummaryResponseDto
                {
                    Id = a.Id,
                    PrApplicationCode = a.ApplicationCode,
                    ApplyDeptName = a.ApplyUserDeptName,
                    ApplyTime = $"{a.ApplyTime:yyyy-MM-dd}",
                    ApplicantName = a.ApplyUserIdName,
                    ApplyAmount = a.TotalAmountRMB,
                    SpeakerCount = a.SpeakerCount,
                    TotalCount = a.TotalCount,
                    TotalAmount = a.TotalAmount,
                    Status = a.Status,
                    //StatusName = allStatus.FirstOrDefault(a1 => a1.Key == (int)a.Status)?.Value
                }).ToArray();

            return new PagedResultDto<GetThirdPartySpeakerSummaryResponseDto>(count, datas);
        }


        private async Task<(Dictionary<string, List<Guid>>, bool)> GetFilterByRole()
        {
            Dictionary<string, List<Guid>> filter = new Dictionary<string, List<Guid>>();
            //集团财务/合规管理员/采购专员/系统管理员--所有数据
            string[] allDataRoles = [RoleNames.Admin, RoleNames.GroupFinance, RoleNames.OEC_Admin, RoleNames.ProcurementSpecialist];
            var isAllDataRole = allDataRoles.Intersect(CurrentUser.Roles, StringComparer.CurrentCultureIgnoreCase).Any();
            if (isAllDataRole)
            {
                return (filter, false);
            }
            var queryPr = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();

            //一般申请人/工厂申请人--自己作为申请人的数据
            string[] onlyApplicationDataRoles = [RoleNames.GeneralApplicant, RoleNames.FactoryApplicant];
            var isOnlyApplicationDataRole = onlyApplicationDataRoles.Intersect(CurrentUser.Roles, StringComparer.CurrentCultureIgnoreCase).Any();
            if (isOnlyApplicationDataRole)
            {
                filter.Add("Applicant", new List<Guid>() { CurrentUser.Id.Value });
            }
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();

            var orgs = await dataverseService.GetOrganizations();
            var use = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().FirstOrDefaultAsync(a => a.Id == CurrentUser.Id.Value);
            var mainDeptId = use.GetProperty<Guid?>(EntityConsts.IdentityUser.MainDepartmentId);
            //审批人--所属部门+作为EPO Leader部门
            string[] departmentDataRoles = [RoleNames.Approver];
            var isDepartmentDataRole = departmentDataRoles.Intersect(CurrentUser.Roles, StringComparer.CurrentCultureIgnoreCase).Any();
            if (isDepartmentDataRole)
            {
                var epoLeaderDepartmentIds = orgs.Where(o => o.EpoLeaderId == CurrentUser.Id).Select(a => a.Id).ToList();
                if (mainDeptId.HasValue) epoLeaderDepartmentIds.Add(mainDeptId.Value);
                filter.Add(RoleNames.Approver, epoLeaderDepartmentIds);
            }


            //部门财务--管辖BU的数据
            string[] deptFinanceDataRoles = [RoleNames.DeptFinance];
            var isDeptFinanceDataRole = deptFinanceDataRoles.Intersect(CurrentUser.Roles, StringComparer.CurrentCultureIgnoreCase).Any();
            if (isDeptFinanceDataRole)
            {
                var authorizedBudgetBuRepository = LazyServiceProvider.LazyGetService<IBdAuthorizedBudgetBuRepository>();
                var queryAuthorizedBudgetBu = await authorizedBudgetBuRepository.GetQueryableAsync();
                var buIds = queryAuthorizedBudgetBu.Where(m => m.UserId == CurrentUser.Id).Select(s => s.BuId).ToList();
                filter.Add(RoleNames.DeptFinance, buIds);
            }
            //部门管理员--所属主部门所在BU的数据
            string[] deptAdminDataRoles = [RoleNames.DeptAdmin];
            var isDeptAdminDataRole = deptAdminDataRoles.Intersect(CurrentUser.Roles, StringComparer.CurrentCultureIgnoreCase).Any();
            if (isDeptAdminDataRole && mainDeptId.HasValue)
            {
                var org = orgs.Where(a => a.Id == mainDeptId.Value).FirstOrDefault();
                var bus = await LazyServiceProvider.LazyGetService<ICommonService>().GetParentOrgs(org);
                var bu = orgs.FirstOrDefault(a => a.OrganizationType == DataverseEnums.Organization.OrganizationType.Bu);
                filter.Add(RoleNames.DeptAdmin, new List<Guid>() { bu.Id });
            }



            //预算负责人--作为预算负责人的预算对应数据
            string[] budgetOwnerDataRoles = [RoleNames.BudgetOwner];
            var isBudgetOwnerDataRole = budgetOwnerDataRoles.Intersect(CurrentUser.Roles, StringComparer.CurrentCultureIgnoreCase).Any();
            if (isBudgetOwnerDataRole)
            {
                var queryBdSubBudget = await LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>().GetQueryableAsync();
                var budgetIds = queryBdSubBudget.Where(a => a.OwnerId == CurrentUser.Id).Select(a => a.Id).ToList();
                filter.Add(RoleNames.BudgetOwner, budgetIds);
            }
            return (filter, true);
        }


        /// <summary>
        /// 获取第三方讲者参会记录金额明细
        /// </summary>
        /// <param name="prId"></param>
        /// <returns></returns>
        public async Task<GetThirdPartySpeakerPRRelatedAmountResponseDto> GetThirdPartySpeakerPRRelatedAmountAsync(Guid prId)
        {
            var queryPr = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var queryThirdPartySpeaker = await LazyServiceProvider.LazyGetService<IPurThirdPartySpeakerItemRepository>().GetQueryableAsync();
            var queryGr = await LazyServiceProvider.LazyGetService<IPurGRApplicationRepository>().GetQueryableAsync();
            var queryGrDetailHistory = await LazyServiceProvider.LazyGetService<IPurGRApplicationDetailHistoryRepository>().GetQueryableAsync();
            var queryPa = await LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>().GetQueryableAsync();

            Enums.PurPAStatus.PurPAApplicationStatus[] paStatuses = [Enums.PurPAStatus.PurPAApplicationStatus.WaitingForPayment, Enums.PurPAStatus.PurPAApplicationStatus.PaymentProgress, Enums.PurPAStatus.PurPAApplicationStatus.Paid, Enums.PurPAStatus.PurPAApplicationStatus.PaymenFailed];

            var data = queryPr.Where(a => a.Id == prId)
                .Select(a => new GetThirdPartySpeakerPRRelatedAmountResponseDto
                {
                    PrApplicationCode = a.ApplicationCode,
                    ApplyAmount = a.TotalAmountRMB,
                    TotalTaughtFee = queryThirdPartySpeaker.Where(a1 => a1.PrId == a.Id).Sum(a1 => a1.Amount),
                    ReceivedAmount = queryGr.Where(a1 => a1.PrId == a.Id).Join(queryGrDetailHistory, a1 => a1.Id, a1 => a1.GRApplicationId, (a1, b1) => new { Amount = (decimal)a1.ExchangeRate * b1.ReceivedAmount }).Sum(a1 => a1.Amount ?? 0),
                    PaiedAmount = queryPa.Where(a1 => a1.PRId == a.Id && paStatuses.Contains(a1.Status)).Sum(a1 => a1.PayTotalAmount * (decimal)a1.ExchangeRate)
                }).FirstOrDefault();

            return data;
        }

        /// <summary>
        /// 获取第三方讲者明细列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetThirdPartySpeakerItemsResponseDto>> GetThirdPartySpeakerItemsAsync(GetThirdPartySpeakerItemsRequestDto request, bool isPage = true)
        {
            var queryThirdPartySpeaker = await LazyServiceProvider.LazyGetService<IPurThirdPartySpeakerItemRepository>().GetQueryableAsync();
            var query = queryThirdPartySpeaker.Where(a => a.PrId == request.PrId)
                .WhereIf(!string.IsNullOrEmpty(request.VendorCode), a => a.VendorCode.Contains(request.VendorCode))
                .WhereIf(!string.IsNullOrEmpty(request.VendorName), a => a.VendorName.Contains(request.VendorName))
                .WhereIf(request.HospitalId.HasValue, a => a.HospitalId == request.HospitalId)
                .WhereIf(request.StandardDeptId.HasValue, a => a.StandardDeptId == request.StandardDeptId)
                .WhereIf(request.JobTitleId.HasValue, a => a.JobTitleId == request.JobTitleId)
                .WhereIf(!string.IsNullOrEmpty(request.LevelCode), a => a.SPLevelCode.Contains(request.LevelCode))
                .OrderByDescending(a => a.CreationTime)
                .Select(a => new GetThirdPartySpeakerItemsResponseDto
                {
                    Id = a.Id,
                    VendorCode = a.VendorCode,
                    VendorName = a.VendorName,
                    HospitalName = a.HospitalName,
                    StandardDeptName = a.StandardDeptName,
                    JobTitleName = a.JobTitleName,
                    SPLevelName = a.SPLevelName,
                    Year = a.Year,
                    Count = a.Times,
                    Amount = a.Amount
                });

            var count = query.Count();
            var datas = query.PagingIf(request, isPage).ToArray();

            return new PagedResultDto<GetThirdPartySpeakerItemsResponseDto>(count, datas);
        }

        /// <summary>
        /// 获取用于选择的讲者列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetSpeakerForChoiceResponseDto>> GetSpeakerForChoiceAsync(GetSpeakerForChoiceRequestDto request)
        {
            var queryVendor = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();
            var queryVendorPersonal = await LazyServiceProvider.LazyGetService<IVendorPersonalRepository>().GetQueryableAsync();
            var queryComPsa = await LazyServiceProvider.LazyGetService<IOECPSAComPSALimitRepository>().GetQueryableAsync();
            var queryPsaUseHistory = await LazyServiceProvider.LazyGetService<IOECPSASpeakerLimitUseHistoryRepository>().GetQueryableAsync();

            //通用PSA
            queryComPsa = queryComPsa.Where(a => request.Year >= a.EffectStart.Year && request.Year <= (a.EffectEnd.HasValue ? a.EffectEnd.Value.Year : DateTime.MaxValue.Year)).OrderByDescending(a => a.EffectStart).Take(1);

            var query = queryVendor.Where(a => a.Status == VendorStatus.Valid && a.VendorType == VendorTypes.HCPPerson)
                .Join(queryVendorPersonal, a => a.Id, a => a.VendorId, (a, b) => new { Vendor = a, VendorPersonal = b })
                .WhereIf(!string.IsNullOrEmpty(request.Code), a => a.Vendor.VendorCode.Contains(request.Code))
                .WhereIf(!string.IsNullOrEmpty(request.Name), a => a.VendorPersonal.SPName.Contains(request.Name))
                .WhereIf(request.HospitalId.HasValue, a => a.Vendor.HospitalId == request.HospitalId)
                .WhereIf(request.DepartmentId.HasValue, a => a.Vendor.StandardHosDepId == request.DepartmentId)
                .WhereIf(request.JobTitleId.HasValue, a => a.Vendor.PTId == request.JobTitleId)
                .WhereIf(!string.IsNullOrEmpty(request.LevelCode), a => a.Vendor.SPLevel == request.LevelCode)
                .GroupJoin(queryComPsa, a => 1, a => 1, (a, b) => new { a.Vendor, a.VendorPersonal, ComPsas = b })
                .SelectMany(a => a.ComPsas.DefaultIfEmpty(), (a, b) => new { a.Vendor, a.VendorPersonal, ComPsa = b })
                .Select(a => new
                {
                    a.Vendor.Id,
                    Name = a.VendorPersonal.SPName,
                    Code = a.Vendor.VendorCode,
                    LevelCode = a.Vendor.SPLevel,
                    a.Vendor.HospitalName,
                    a.Vendor.StandardHosDepName,
                    a.Vendor.PTName,
                    a.Vendor.UserId,
                    ComPsaTimesLimit = a.ComPsa == null ? 0 : a.ComPsa.TimesLimit,
                    ComPsaAmountLimit = a.ComPsa == null ? 0 : a.ComPsa.AmountLimit,
                    ComPsaUsedTimes = queryPsaUseHistory.Where(a1 => a1.VendorId == a.Vendor.Id && a1.EffectiveDate.Year == request.Year && !a1.PsaExternalId.HasValue).Sum(a1 => a1.Times),
                    ComPsaUsedAmount = queryPsaUseHistory.Where(a1 => a1.VendorId == a.Vendor.Id && a1.EffectiveDate.Year == request.Year && !a1.PsaExternalId.HasValue).Sum(a1 => a1.Amount)
                });

            var count = query.Count();
            var datas = query.Skip(request.PageIndex * request.PageSize).Take(request.PageSize).Select(a => new GetSpeakerForChoiceResponseDto
            {
                Id = a.Id,
                Name = a.Name,
                Code = a.Code,
                Level = a.LevelCode,
                Hospital = a.HospitalName,
                Department = a.StandardHosDepName,
                JobTitle = a.PTName,
                UserId = a.UserId,
                YearlyCount = a.ComPsaTimesLimit + a.ComPsaUsedTimes,
                YearlyAmount = a.ComPsaAmountLimit + a.ComPsaUsedAmount,
                NeedCheckLimit = true
            }).ToArray();

            if (datas.Any())
            {
                var commonService = LazyServiceProvider.LazyGetService<ICommonService>();
                var consentResponses = await commonService.GetLeatestConsentInfosAsync();
                var userIds = datas.Where(a => a.UserId.HasValue).Select(a => a.UserId.Value).ToArray();
                var consentSigneds = await commonService.GetConsentSignedInfosAsync(userIds);

                foreach (var item in datas)
                {
                    if (item.UserId.HasValue && item.UserId != Guid.Empty)
                        item.ConsentStatus = commonService.IsSignLeatestVersion(item.UserId.Value, consentResponses, consentSigneds);
                }
            }

            return new PagedResultDto<GetSpeakerForChoiceResponseDto>(count, datas);
        }

        /// <summary>
        /// 获取通用psa上限设置及余量
        /// </summary>
        /// <param name="vendorId"></param>
        /// <param name="year"></param>
        /// <returns></returns>
        async Task<GetPsaLimitResponseDto> GetPsaLimitAsync(Guid vendorId, int year)
        {
            var queryComPsa = await LazyServiceProvider.LazyGetService<IOECPSAComPSALimitRepository>().GetQueryableAsync();
            var queryPsaUseHistory = await LazyServiceProvider.LazyGetService<IOECPSASpeakerLimitUseHistoryRepository>().GetQueryableAsync();
            //通用PSA
            var psaLimit = queryComPsa.Where(a => year >= a.EffectStart.Year && year <= (a.EffectEnd.HasValue ? a.EffectEnd.Value.Year : DateTime.MaxValue.Year)).OrderByDescending(a => a.EffectStart).Take(1)
                .Select(a => new GetPsaLimitResponseDto
                {
                    TimesLimit = a.TimesLimit,
                    AmountLimit = a.AmountLimit,
                    UsedTimesLimit = queryPsaUseHistory.Where(a1 => a1.VendorId == vendorId && a1.EffectiveDate.Year == year && !a1.PsaExternalId.HasValue).Sum(a1 => a1.Times),
                    UsedAmountLimit = queryPsaUseHistory.Where(a1 => a1.VendorId == vendorId && a1.EffectiveDate.Year == year && !a1.PsaExternalId.HasValue).Sum(a1 => a1.Amount)
                }).FirstOrDefault();

            if (psaLimit == null)
                return default;

            psaLimit.SurplusTimesLimit = psaLimit.TimesLimit + psaLimit.UsedTimesLimit;
            psaLimit.SurplusAmountLimit = psaLimit.AmountLimit + psaLimit.UsedAmountLimit;

            return psaLimit;
        }

        /// <summary>
        /// 获取例外审批次数及余量
        /// 跟PR相同BU的例外才可以使用
        /// </summary>
        /// <param name="exceptionNumber"></param>
        /// <param name="vendorId"></param>
        /// <param name="year"></param>
        /// <param name="prBuId">pr对应BU</param>
        /// <returns></returns>
        async Task<GetPsaLimitResponseDto> GetExtPsaLimitAsync(string exceptionNumber, Guid vendorId, int year, Guid prBuId)
        {
            var queryPsaExtra = await LazyServiceProvider.LazyGetService<IOECPSASpeakerLimitExtraRepository>().GetQueryableAsync();
            var queryPsaUseHistory = await LazyServiceProvider.LazyGetService<IOECPSASpeakerLimitUseHistoryRepository>().GetQueryableAsync();

            queryPsaExtra = queryPsaExtra.Where(a => a.ExtralAuditApplicationNo == exceptionNumber && a.VendorId == vendorId && a.Year == year && a.DivisionId == prBuId);

            var psaLimit = queryPsaExtra.GroupBy(a => a.ExtralAuditApplicationNo)
                .Select(a => new GetPsaLimitResponseDto
                {
                    TimesLimit = a.Sum(a1 => a1.ExtralTimesRest),
                    AmountLimit = a.Sum(a1 => a1.ExtralAmountRest),
                    SurplusTimesLimit = queryPsaExtra.Join(queryPsaUseHistory, a1 => a1.Id, a1 => a1.PsaExternalId, (a1, b1) => b1).Sum(a1 => a1.Times),
                    SurplusAmountLimit = queryPsaExtra.Join(queryPsaUseHistory, a1 => a1.Id, a1 => a1.PsaExternalId, (a1, b1) => b1).Sum(a1 => a1.Amount)
                }).FirstOrDefault();

            if (psaLimit == null)
                return default;

            psaLimit.UsedTimesLimit = psaLimit.TimesLimit - psaLimit.SurplusTimesLimit;
            psaLimit.UsedAmountLimit = psaLimit.AmountLimit - psaLimit.SurplusAmountLimit;

            return psaLimit;
        }

        /// <summary>
        /// 保存第三方讲者讲课记录
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> SaveThirdPartySpeakerItemAsync(SaveThirdPartySpeakerItemRequestDto request)
        {
            var prApplication = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetAsync(request.PrId);
            if (prApplication == null)
                return MessageResult.FailureResult($"未找到Id为{request.PrId}的Pr申请单信息");

            var diffTimes = 0;
            decimal diffAmount = 0;
            PurThirdPartySpeakerItem thirdPartySpeakerItem;
            var thirdPartySepakerItemRepository = LazyServiceProvider.LazyGetService<IPurThirdPartySpeakerItemRepository>();
            if (request.Id.HasValue)
            {
                thirdPartySpeakerItem = await thirdPartySepakerItemRepository.FindAsync(request.Id.Value);
                if (thirdPartySpeakerItem == null)
                    return MessageResult.FailureResult($"未找到Id为{request.Id}的第三方讲者讲课记录");

                //与前一次的差额
                diffTimes = (request.Times ?? 0) - thirdPartySpeakerItem.Times;
                diffAmount = (request.Amount ?? 0) - thirdPartySpeakerItem.Amount;
            }
            else
            {
                //判断是否已存在该讲者的讲课记录
                var isExistsed = await thirdPartySepakerItemRepository.AnyAsync(a => a.PrId == request.PrId && a.VendorId == request.VendorId && a.Year == request.Year);
                if (isExistsed)
                    return MessageResult.FailureResult($"该PR申请下{request.Year}年度已存在该讲者讲课记录，请勿重复添加");

                diffTimes = request.Times ?? 0;
                diffAmount = request.Amount ?? 0;

                thirdPartySpeakerItem = ObjectMapper.Map<SaveThirdPartySpeakerItemRequestDto, PurThirdPartySpeakerItem>(request);
                var queryVendor = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();
                var queryVendorPersonal = await LazyServiceProvider.LazyGetService<IVendorPersonalRepository>().GetQueryableAsync();
                var vendor = queryVendor.Where(a => a.Id == request.VendorId).Join(queryVendorPersonal, a => a.Id, a => a.VendorId, (a, b) => new
                {
                    a.VendorCode,
                    VendorName = b.SPName,
                    a.HospitalId,
                    a.HospitalName,
                    a.StandardHosDepId,
                    a.StandardHosDepName,
                    JobTitleId = a.PTId,
                    JobTitleName = a.PTName,
                    a.SPLevel
                }).FirstOrDefault();

                if (vendor == null)
                    return MessageResult.FailureResult($"未找到Id为{request.VendorId}的讲者数据");

                thirdPartySpeakerItem.VendorCode = vendor.VendorCode;
                thirdPartySpeakerItem.VendorName = vendor.VendorName;
                thirdPartySpeakerItem.HospitalId = vendor.HospitalId;
                thirdPartySpeakerItem.HospitalName = vendor.HospitalName;
                thirdPartySpeakerItem.StandardDeptId = vendor.StandardHosDepId;
                thirdPartySpeakerItem.StandardDeptName = vendor.StandardHosDepName;
                thirdPartySpeakerItem.JobTitleId = vendor.JobTitleId;
                thirdPartySpeakerItem.JobTitleName = vendor.JobTitleName;
                thirdPartySpeakerItem.SPLevelCode = vendor.SPLevel;

                if (!string.IsNullOrEmpty(vendor.SPLevel))
                {
                    var levels = await LazyServiceProvider.LazyGetService<IDataverseService>().GetDictionariesAsync(DictionaryType.HCPLevel);
                    thirdPartySpeakerItem.SPLevelName = levels.FirstOrDefault(a => a.Code == vendor.SPLevel)?.Name;
                }
            }

            var date = new DateTime(request.Year.Value, DateTime.Now.Month, DateTime.Now.Day);
            var listUseLimit = new List<PrUseSpeakerLimitDetailRequest>();
            var listExNums = new List<PurThirdPartySpeakerExNum>();

            #region 需要扣减的情况

            if (diffTimes > 0 || diffAmount > 0)
            {
                //判断psa上限是否足够本次差额
                var psaLimit = await GetPsaLimitAsync(request.VendorId.Value, request.Year.Value);
                //psa上限不够
                if (diffTimes > psaLimit?.SurplusTimesLimit || diffAmount > psaLimit?.SurplusAmountLimit)
                {
                    //返回特定代码，前端弹框让用户输入例外编号
                    if (string.IsNullOrEmpty(request.ExceptionNumber))
                        return MessageResult.SuccessResult(messageModel: MessageModelBase.PsaLimitNotEnough);

                    //例外上限
                    var extPsaLimit = await GetExtPsaLimitAsync(request.ExceptionNumber, request.VendorId.Value, request.Year.Value, prApplication.ApplyUserBu);
                    //psa上限+例外上限都不够时
                    if (extPsaLimit == null || diffTimes > psaLimit.SurplusTimesLimit + extPsaLimit?.SurplusTimesLimit || diffAmount > psaLimit.SurplusAmountLimit + extPsaLimit?.SurplusAmountLimit)
                        return MessageResult.FailureResult("例外审批编号不存在或剩余次数/金额不足，提交失败");

                    //扣减psa上限
                    var psa = new PrUseSpeakerLimitDetailRequest
                    {
                        VendorId = request.VendorId.Value,
                        Times = diffTimes >= psaLimit.SurplusTimesLimit ? psaLimit.SurplusTimesLimit : diffTimes,
                        Amount = diffAmount >= psaLimit.SurplusAmountLimit ? psaLimit.SurplusAmountLimit : diffAmount,
                        EffectiveDate = date,
                        Type1 = OperDetailType.Deduction,
                        Type2 = ModifyTypes.Deduction
                    };
                    //例外次数
                    var exLimit = new PrUseSpeakerLimitDetailRequest
                    {
                        VendorId = request.VendorId.Value,
                        Times = diffTimes - psa.Times,
                        Amount = diffAmount - psa.Amount,
                        EffectiveDate = date,
                        Type1 = OperDetailType.Deduction,
                        Type2 = ModifyTypes.Deduction,
                        ExceptionNumber = request.ExceptionNumber
                    };

                    //如果不是两个都大于0的情况，则只生成次数或金额的扣减记录
                    if (diffTimes > 0 && diffAmount < 0)
                    {
                        psa.Amount = 0;
                        exLimit.Amount = 0;
                    }
                    else if (diffAmount > 0 && diffTimes < 0)
                    {
                        psa.Times = 0;
                        exLimit.Times = 0;
                    }

                    listUseLimit.Add(psa);
                    listUseLimit.Add(exLimit);

                    listExNums.Add(new PurThirdPartySpeakerExNum { ExceptionNumber = request.ExceptionNumber, Times = exLimit.Times, Amount = exLimit.Amount });
                }
                else//psa上限足够
                {
                    var psa = new PrUseSpeakerLimitDetailRequest { VendorId = request.VendorId.Value, Times = diffTimes, Amount = diffAmount, EffectiveDate = date, Type1 = OperDetailType.Deduction, Type2 = ModifyTypes.Deduction };
                    //如果不是两个都大于0的情况，则只生成次数或金额的扣减记录
                    if (diffTimes > 0 && diffAmount < 0)
                        psa.Amount = 0;
                    else if (diffAmount > 0 && diffTimes < 0)
                        psa.Times = 0;

                    listUseLimit.Add(psa);
                }
            }

            #endregion

            #region 需要返还的情况

            if (diffTimes < 0 || diffAmount < 0)
            {
                var tempDiffTimes = diffTimes;
                var tempDiffAmount = diffAmount;
                var thirdPartySepakerExNumRepository = LazyServiceProvider.LazyGetService<IPurThirdPartySpeakerExNumRepository>();
                var exceptionNumbers = await thirdPartySepakerExNumRepository.GetListAsync(a => a.PurThirdPartySpeakerItemId == thirdPartySpeakerItem.Id && (a.Times > 0 || a.Amount > 0));
                exceptionNumbers = exceptionNumbers.OrderBy(a => a.CreationTime).ToList();
                //依次返还差额给例外审批
                foreach (var item in exceptionNumbers)
                {
                    var returnTimes = 0;
                    var returnAmount = 0m;
                    //差额比例外次数大，则返还给当前所有例外次数
                    if (Math.Abs(tempDiffTimes) > item.Times)
                    {
                        returnTimes = item.Times;
                        tempDiffTimes += item.Times;
                        item.Times = 0;
                    }
                    else//否则，返还差额次数给当前例外次数
                    {
                        returnTimes = Math.Abs(tempDiffTimes);
                        item.Times += tempDiffTimes;
                        tempDiffTimes = 0;
                    }

                    //同理，金额跟次数的逻辑一致
                    if (Math.Abs(tempDiffAmount) > item.Amount)
                    {
                        returnAmount = item.Amount;
                        tempDiffAmount += item.Amount;
                        item.Amount = 0;
                    }
                    else
                    {
                        returnAmount = Math.Abs(tempDiffAmount);
                        item.Amount += tempDiffAmount;
                        tempDiffAmount = 0;
                    }

                    var exLimit = new PrUseSpeakerLimitDetailRequest { VendorId = request.VendorId.Value, Times = returnTimes, Amount = returnAmount, EffectiveDate = date, Type1 = OperDetailType.Added, Type2 = ModifyTypes.Return, ExceptionNumber = item.ExceptionNumber };
                    //如果不是两个都小于0的情况，则只生成次数或金额的返还记录
                    if (returnTimes < 0 && returnAmount >= 0)
                        exLimit.Amount = 0;
                    if (returnAmount < 0 && returnTimes >= 0)
                        exLimit.Times = 0;

                    listUseLimit.Add(exLimit);

                    await thirdPartySepakerExNumRepository.UpdateAsync(item);

                    if (tempDiffTimes == 0 && tempDiffAmount == 0)
                        break;
                }

                //如果例外都已返还后，还有差额，则继续返还给psa
                if (tempDiffTimes < 0 || tempDiffAmount < 0)
                {
                    var psa = new PrUseSpeakerLimitDetailRequest { VendorId = request.VendorId.Value, Times = Math.Abs(tempDiffTimes), Amount = Math.Abs(tempDiffAmount), EffectiveDate = date, Type1 = OperDetailType.Added, Type2 = ModifyTypes.Return };
                    //如果不是两个都小于0的情况，则只生成次数或金额的返还记录
                    if (tempDiffTimes < 0 && tempDiffAmount >= 0)
                        psa.Amount = 0;
                    if (tempDiffAmount < 0 && tempDiffTimes >= 0)
                        psa.Times = 0;

                    listUseLimit.Add(psa);
                }
            }

            #endregion

            if (request.Id.HasValue)
            {
                //编辑时，只能修改这3个信息
                thirdPartySpeakerItem.Times = request.Times ?? 0;
                thirdPartySpeakerItem.Amount = request.Amount ?? 0;
                thirdPartySpeakerItem.Remark = request.Remark;
                await thirdPartySepakerItemRepository.UpdateAsync(thirdPartySpeakerItem);
            }
            else
                await thirdPartySepakerItemRepository.InsertAsync(thirdPartySpeakerItem);

            //增加例外使用记录
            if (listExNums.Any())
            {
                listExNums.ForEach(a => a.PurThirdPartySpeakerItemId = thirdPartySpeakerItem.Id);
                await LazyServiceProvider.LazyGetService<IPurThirdPartySpeakerExNumRepository>().InsertManyAsync(listExNums);
            }

            //保存变更历史
            await LazyServiceProvider.LazyGetService<IPurThirdPartySpeakerItemHistoryRepository>().InsertAsync(new PurThirdPartySpeakerItemHistory
            {
                PurThirdPartySpeakerItemId = thirdPartySpeakerItem.Id,
                OperatorName = CurrentUser.Name,
                Times = thirdPartySpeakerItem.Times,
                Amount = thirdPartySpeakerItem.Amount,
                Remark = thirdPartySpeakerItem.Remark
            });
            //psa和例外消耗/退回记录
            var result = await LazyServiceProvider.LazyGetService<ISpeakerLimitService>().SavePrSpeakerLimitAsync(new PrUseSpeakerLimitRequest { PrApplicationId = request.PrId, BuId = prApplication.ApplyUserBu, Details = listUseLimit });

            return result;
        }

        /// <summary>
        /// 获取讲课记录详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<GetThirdPartySpeakerItemResponseDto> GetThirdPartySpeakerItemAsync(Guid id)
        {
            var queryThirdPartySepakerItem = await LazyServiceProvider.LazyGetService<IPurThirdPartySpeakerItemRepository>().GetQueryableAsync();
            var queryPsaUseHistory = await LazyServiceProvider.LazyGetService<IOECPSASpeakerLimitUseHistoryRepository>().GetQueryableAsync();

            var thirdPartySpeakerItem = queryThirdPartySepakerItem.FirstOrDefault(a => a.Id == id);
            if (thirdPartySpeakerItem == null)
                return default;

            //通用PSA
            var psaLimit = await GetPsaLimitAsync(thirdPartySpeakerItem.VendorId, thirdPartySpeakerItem.Year);

            var data = new GetThirdPartySpeakerItemResponseDto
            {
                Year = thirdPartySpeakerItem.Year,
                VendorId = thirdPartySpeakerItem.VendorId,
                VendorName = thirdPartySpeakerItem.VendorName,
                YearlyCount = (psaLimit?.TimesLimit + psaLimit?.UsedTimesLimit) ?? 0,
                YearlyAmount = (psaLimit?.AmountLimit + psaLimit?.UsedAmountLimit) ?? 0,
                Count = thirdPartySpeakerItem.Times,
                Amount = thirdPartySpeakerItem.Amount,
                Remark = thirdPartySpeakerItem.Remark
            };

            return data;
        }

        /// <summary>
        /// 获取第三方讲者修改记录
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetThirdPartySpeakerUpdateHistoryResponseDto>> GetThirdPartySpeakerUpdateHistoryAsync(GetThirdPartySpeakerUpdateHistoryRequestDto request)
        {
            var queryHistory = await LazyServiceProvider.LazyGetService<IPurThirdPartySpeakerItemHistoryRepository>().GetQueryableAsync();
            var query = queryHistory.Where(a => a.PurThirdPartySpeakerItemId == request.ThirdPartySpeakerItemId)
            .Select(a => new GetThirdPartySpeakerUpdateHistoryResponseDto
            {
                Times = a.Times,
                Amount = a.Amount,
                OperatorName = a.OperatorName,
                Remark = a.Remark
            });

            var count = query.Count();
            var datas = query.ToArray();

            return new PagedResultDto<GetThirdPartySpeakerUpdateHistoryResponseDto>(count, datas);
        }

        /// <summary>
        /// 上传并验证第三方讲者
        /// </summary>
        /// <param name="prId"></param>
        /// <param name="buffer"></param>
        /// <returns></returns>
        public async Task<MessageResult> UploadThirdPartySpeakerToValidateAsync(Guid prId, byte[] buffer)
        {
            using var stream = new MemoryStream(buffer);
            using var xlWorkbook = new XLWorkbook(stream);
            var ws = xlWorkbook.Worksheets.FirstOrDefault();
            if (ws == null)
                return MessageResult.FailureResult("读取Excel模板失败");

            //var rows = ws.RowsUsed().Skip(5).Select(r => new UploadThirdPartySpeakerTemplateItem
            //{
            //    PrId = prId,
            //    VendorCode = r.Cell(1).GetString(),
            //    Year = r.Cell(2).GetValue<int?>(),
            //    Times = r.Cell(3).GetValue<int?>(),
            //    Amount = r.Cell(4).GetValue<decimal?>()
            //});

            var rows = ws.RowsUsed().Skip(5).Select((r, index) => new UploadThirdPartySpeakerTemplateItem
            {
                RowNo = index + 7,//7是因为模板中数据从第七行开始
                PrId = prId,
                VendorCode = r.Cell(1).GetString(),
                Year = r.Cell(2).GetValue<int?>(),
                Times = r.Cell(3).GetValue<int?>(),
                Amount = r.Cell(4).GetValue<decimal?>()
            });


            if (!rows.Any())
                return MessageResult.FailureResult("模板中没有任何有效数据");

            var prApplication = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetAsync(a => a.Id == prId);
            if (prApplication == null)
                return MessageResult.FailureResult($"未找到Id为{prId}的Pr申请单信息");

            var validateResult = await ValidateBatchUpsertThirdPartySpeakerAsync(rows);
            return MessageResult.SuccessResult(validateResult);
        }

        /// <summary>
        /// 验证上传的数据
        /// </summary>
        /// <param name="rows"></param>
        /// <returns></returns>
        async Task<UploadThirdPartySpeakerResult> ValidateBatchUpsertThirdPartySpeakerAsync(IEnumerable<UploadThirdPartySpeakerTemplateItem> rows)
        {
            var queryVendor = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();
            var queryVendorPersonal = await LazyServiceProvider.LazyGetService<IVendorPersonalRepository>().GetQueryableAsync();
            var queryComPsa = await LazyServiceProvider.LazyGetService<IOECPSAComPSALimitRepository>().GetQueryableAsync();
            var queryPsaUseHistory = await LazyServiceProvider.LazyGetService<IOECPSASpeakerLimitUseHistoryRepository>().GetQueryableAsync();
            var queryThirdPartySepakerItem = await LazyServiceProvider.LazyGetService<IPurThirdPartySpeakerItemRepository>().GetQueryableAsync();

            //查询出符合条件的psa信息
            var years = rows.Where(a => a.Year.HasValue).Select(a => a.Year.Value).Distinct();
            Expression<Func<OECPSAComPSALimit, bool>> queryComPsaPredicate = a => false;
            foreach (var year in years)
                queryComPsaPredicate = queryComPsaPredicate.Or(a => year >= a.EffectStart.Year && year <= (a.EffectEnd.HasValue ? a.EffectEnd.Value.Year : DateTime.MaxValue.Year));
            queryComPsa = queryComPsa.Where(queryComPsaPredicate);

            //根据上传的数据查询出对应的讲者
            var prId = rows.FirstOrDefault()?.PrId;
            var vendorCodes = rows.Select(a => a.VendorCode).ToArray();
            var datas = queryVendor.Where(a => vendorCodes.Contains(a.VendorCode) && a.Status == VendorStatus.Valid)
                .Join(queryVendorPersonal, a => a.Id, a => a.VendorId, (a, b) => new { Vendor = a, VendorPersonal = b })
                .Join(queryComPsa, a => 1, a => 1, (a, b) => new { a.Vendor, a.VendorPersonal, ComPsa = b })
                .GroupJoin(queryPsaUseHistory.Where(a => !a.PsaExternalId.HasValue), a => new { VendorId = a.Vendor.Id, a.ComPsa.EffectStart.Year }, a => new { a.VendorId, a.EffectiveDate.Year }, (a, b) => new { a.Vendor, a.VendorPersonal, a.ComPsa, PsaUseHistories = b })
                .SelectMany(a => a.PsaUseHistories.DefaultIfEmpty(), (a, b) => new { a.Vendor, a.VendorPersonal, a.ComPsa, PsaUseHistory = b })
                .GroupJoin(queryThirdPartySepakerItem.Where(a => a.PrId == prId), a => new { a.VendorPersonal.VendorId, a.PsaUseHistory.EffectiveDate.Year }, a => new { a.VendorId, a.Year }, (a, b) => new { a.Vendor, a.VendorPersonal, a.PsaUseHistory, a.ComPsa, ThirdPartySepakerItems = b })
                .SelectMany(a => a.ThirdPartySepakerItems.DefaultIfEmpty(), (a, b) => new { a.Vendor, a.VendorPersonal, a.PsaUseHistory, a.ComPsa, ThirdPartySepakerItem = b })
                .GroupBy(a => new { a.VendorPersonal.VendorId, a.Vendor.VendorCode, VendorName = a.VendorPersonal.SPName, a.Vendor.HospitalName, a.ComPsa.EffectStart.Year, a.ComPsa.TimesLimit, a.ComPsa.AmountLimit, Times = a.ThirdPartySepakerItem == null ? new int?() : a.ThirdPartySepakerItem.Times, Amount = a.ThirdPartySepakerItem == null ? new decimal?() : a.ThirdPartySepakerItem.Amount })
                .Select(a => new
                {
                    a.Key.VendorId,
                    a.Key.VendorCode,
                    a.Key.VendorName,
                    a.Key.HospitalName,
                    a.Key.Year,
                    OriginalTimes = a.Key.Times,
                    OriginalAmount = a.Key.Amount,
                    SurplusTimes = a.Key.TimesLimit + a.Sum(a1 => a1.PsaUseHistory.Times),
                    SurplusAmount = a.Key.AmountLimit + a.Sum(a1 => a1.PsaUseHistory.Amount)
                })
                .ToArray();

            //用于判断vendor是否有重复
            var groupVendorYears = rows.GroupBy(a => new { a.VendorCode, a.Year }).Select(a => new { a.Key.VendorCode, a.Key.Year, Count = a.Count() }).Where(a => a.Count > 1).ToArray();
            //组装最终数据
            var result = rows
                .GroupJoin(datas, a => new { a.VendorCode, a.Year }, a => new { a.VendorCode, Year = new int?(a.Year) }, (a, b) => new { ImportData = a, VendorPsaDatas = b })
                .SelectMany(a => a.VendorPsaDatas.DefaultIfEmpty(), (a, b) => new { a.ImportData, VendorPsaData = b })
                .Select((a, b) =>
                {
                    var data = new UploadThirdPartySpeakerRequestResponseDto
                    {
                        PrId = a.ImportData.PrId,
                        VendorId = a.VendorPsaData?.VendorId,
                        RowNo = a.ImportData.RowNo,
                        VendorCode = a.ImportData.VendorCode,
                        VendorName = a.VendorPsaData?.VendorName,
                        HospitalName = a.VendorPsaData?.HospitalName,
                        Year = a.ImportData.Year,
                        OriginalTimes = a.VendorPsaData?.OriginalTimes,
                        OriginalAmount = a.VendorPsaData?.OriginalAmount,
                        Times = a.ImportData.Times,
                        Amount = a.ImportData.Amount
                    };

                    //有效性验证
                    data.Message = JudgeMessage(data);
                    //前面有效性验证通过后，再验证后续逻辑
                    if (string.IsNullOrEmpty(data.Message))
                    {
                        //重复性校验
                        if (groupVendorYears.Any(a1 => string.Equals(a1.VendorCode, a.ImportData.VendorCode, StringComparison.CurrentCultureIgnoreCase) && a1.Year == a.ImportData.Year))
                            data.Message = "该讲者在待导入数据中存在重复数据，请检查";
                        else if ((a.ImportData.Times - a.VendorPsaData?.OriginalTimes ?? 0) > a.VendorPsaData?.SurplusTimes || (a.ImportData.Amount - a.VendorPsaData.OriginalAmount ?? 0) > a.VendorPsaData?.SurplusAmount)
                            data.Message = "该讲者年度剩余可讲课次数或金额不足，请从页面单独添加填写例外审批编号";
                    }

                    return data;
                })
                .ToArray();

            return new UploadThirdPartySpeakerResult { IsSuccess = result.All(a => string.IsNullOrEmpty(a.Message)), Result = result };
        }

        /// <summary>
        /// 判断并设置错误信息
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        string JudgeMessage(UploadThirdPartySpeakerRequestResponseDto data)
        {
            //判断必填项
            if (string.IsNullOrEmpty(data.VendorCode))
                return "讲者编号必填";
            if (!data.Year.HasValue)
                return "年度必填";
            if (!data.Times.HasValue)
                return "讲课次数必填";
            if (!data.Amount.HasValue)
                return "讲课金额必填";
            if (!data.VendorId.HasValue)
                return "讲者编号错误或非有效";
            if (data.Times < 0)
                return "讲课次数不能为负数";
            if (data.Amount < 0 || Math.Round(data.Amount.Value, 2) != data.Amount)
                return "讲课金额不能为负数或最多允许两位小数";

            return string.Empty;
        }

        /// <summary>
        /// 上传并更新第三方讲者信息以及Psa
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> UploadThirdPartySpeakerToUpsertAsync(IEnumerable<UploadThirdPartySpeakerRequestResponseDto> request)
        {
            if (request == null || !request.Any())
                return MessageResult.FailureResult("模板中没有任何有效数据");

            var prId = request.First().PrId;
            var prApplication = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetAsync(a => a.Id == prId);
            if (prApplication == null)
                return MessageResult.FailureResult($"未找到Id为{prId}的Pr申请单信息");

            //校验数据
            var validationDatas = request.Select(a => new UploadThirdPartySpeakerTemplateItem
            {
                PrId = a.PrId,
                VendorCode = a.VendorCode,
                Year = a.Year,
                Times = a.Times,
                Amount = a.Amount
            });
            var validateResult = await ValidateBatchUpsertThirdPartySpeakerAsync(validationDatas);
            //有任何有错误信息的数据，则返回
            if (!validateResult.IsSuccess)
                return MessageResult.FailureResult(validateResult);

            //获取已存在的第三方讲者讲课信息
            var queryThirdPartySepakerItem = await LazyServiceProvider.LazyGetService<IPurThirdPartySpeakerItemRepository>().GetQueryableAsync();
            var existsItems = queryThirdPartySepakerItem.Where(a => a.PrId == prId).ToArray();
            var unionDatas = request
                .GroupJoin(existsItems, a => new { a.PrId, a.VendorId, a.Year }, a => new { a.PrId, VendorId = new Guid?(a.VendorId), Year = new int?(a.Year) }, (a, b) => new { ImportData = a, SpeakerItems = b })
                .SelectMany(a => a.SpeakerItems.DefaultIfEmpty(), (a, b) => new SaveThirdPartySpeakerItemRequestDto
                {
                    Id = b == null ? null : b.Id,
                    PrId = a.ImportData.PrId,
                    VendorId = a.ImportData.VendorId,
                    Year = a.ImportData.Year,
                    Times = a.ImportData.Times,
                    Amount = a.ImportData.Amount
                })
                .ToArray();

            //执行保存
            foreach (var item in unionDatas)
            {
                var result = await SaveThirdPartySpeakerItemAsync(item);
                if (!result.Success)
                {
                    await CurrentUnitOfWork.RollbackAsync();
                    return result;
                }
            }

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 导出第三方讲者汇总列表
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<Stream> ExportThirdPartySpeakerSummaryAsync(GetThirdPartySpeakerSummaryRequestDto request)
        {
            //var queryPr = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            //var queryThirdPartySpeaker = await LazyServiceProvider.LazyGetService<IPurThirdPartySpeakerItemRepository>().GetQueryableAsync();
            //var (filter, needFilter) = await GetFilterByRole();
            ////查询消费大类为教育资助（成本支持），并且有曾经审批通过的pr申请单数据
            //var query = queryPr.Where(a => a.ExpenseTypeCode == ExpenseTypeCodeConst.COSTSUPPORT && a.IsOnceApproved)
            //    .WhereIf(needFilter, a => (filter.ContainsKey("Applicant") ? filter["Applicant"].Contains(a.ApplyUserId) : false)
            //                       || (filter.ContainsKey(RoleNames.Approver) ? filter[RoleNames.Approver].Contains(a.ApplyUserDept) : false)
            //                       || (filter.ContainsKey(RoleNames.DeptFinance) ? filter[RoleNames.DeptFinance].Contains(a.ApplyUserBu) : false)
            //                       || (filter.ContainsKey(RoleNames.DeptAdmin) ? filter[RoleNames.DeptAdmin].Contains(a.ApplyUserBu) : false)
            //                       || (filter.ContainsKey(RoleNames.BudgetOwner) ? filter[RoleNames.BudgetOwner].Contains(a.SubBudgetId.Value) : false)
            //        )
            //    .WhereIf(!string.IsNullOrEmpty(request.PrApplicationCode), a => a.ApplicationCode.Contains(request.PrApplicationCode))
            //    .WhereIf(!string.IsNullOrEmpty(request.ApplyDeptName), a => a.ApplyUserDeptName.Contains(request.ApplyDeptName))
            //    .WhereIf(!string.IsNullOrEmpty(request.ApplicantName), a => a.ApplyUserIdName.Contains(request.ApplicantName))
            //    .Select(a => new
            //    {
            //        a.Id,
            //        a.ApplicationCode,
            //        a.ApplyUserDeptName,
            //        a.ApplyTime,
            //        a.ApplyUserIdName,
            //        a.TotalAmountRMB,
            //        SpeakerCount = queryThirdPartySpeaker.Where(a1 => a1.PrId == a.Id).Count(),
            //        TotalCount = queryThirdPartySpeaker.Where(a1 => a1.PrId == a.Id).Sum(a1 => a1.Times),
            //        TotalAmount = queryThirdPartySpeaker.Where(a1 => a1.PrId == a.Id).Sum(a1 => a1.Amount),
            //        a.Status
            //    });

            //var allStatus = EnumUtil.GetEnumIdValues<PurPRApplicationStatus>();

            //var datas = query.OrderByDescending(a => a.ApplicationCode).ToArray()
            //    .Select(a => new GetThirdPartySpeakerSummaryResponseDto
            //    {
            //        PrApplicationCode = a.ApplicationCode,
            //        ApplyDeptName = a.ApplyUserDeptName,
            //        ApplyTime = $"{a.ApplyTime:yyyy-MM-dd}",
            //        ApplicantName = a.ApplyUserIdName,
            //        ApplyAmount = a.TotalAmountRMB,
            //        SpeakerCount = a.SpeakerCount,
            //        TotalCount = a.TotalCount,
            //        TotalAmount = a.TotalAmount,
            //        StatusName = allStatus.FirstOrDefault(a1 => a1.Key == (int)a.Status)?.Value
            //    }).ToArray();
            var pagedResult = await GetThirdPartySpeakerSummaryAsync(request, false);
            var datas = pagedResult.Items;
            MemoryStream stream = new();
            var properties = typeof(GetThirdPartySpeakerSummaryResponseDto).GetProperties().ToList();
            var config = new OpenXmlConfiguration() { TableStyles = TableStyles.None, AutoFilter = false };

            var dynamicCols = new List<DynamicExcelColumn>();
            properties.ForEach(x =>
            {
                var attribute = Attribute.GetCustomAttribute(x, typeof(CategoryAttribute)) as CategoryAttribute;
                if (null != attribute && !string.IsNullOrEmpty(attribute.Category))
                    dynamicCols.Add(new DynamicExcelColumn(x.Name));
            });
            config.DynamicColumns = [.. dynamicCols];

            stream.SaveAs(datas, true, "Sheet1", configuration: config);
            stream.Seek(0, SeekOrigin.Begin);
            return stream;
        }
    }
}
