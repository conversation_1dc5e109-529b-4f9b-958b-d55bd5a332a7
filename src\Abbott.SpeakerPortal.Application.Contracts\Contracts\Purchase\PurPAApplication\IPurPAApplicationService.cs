﻿using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.Attachment;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;

namespace Abbott.SpeakerPortal.Contracts.Purchase.PurPAApplication
{
    public partial interface IPurPAApplicationService
    {

        /// <summary>
        /// 创建付款申请数据
        /// </summary>
        /// <param name="createPaDto"></param>
        /// <returns></returns>
        Task<Guid> CreatePurPAApplicationAsync(CreatePurPAApplicationDto createPaDto);

        /// <summary>
        ///  付款列表
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<PagedResultDto<PurPAApplicationListDto>> GetPAApplicationListAsync(PAListSearchRequestDto requestDto);

        /// <summary>
        /// 导出付款列表
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<List<ExportPAApplicationDto>> ExportPAListAsync(PAListSearchRequestDto requestDto);

        /// <summary>
        /// 获取PA详情
        /// </summary>
        /// <param name="paId"></param>
        /// <returns></returns>
        Task<MessageResult> GetPAApplicationDetailsAsync(Guid paId);

        /// <summary>
        ///  收货申请我发起的视角 列表
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<PagedResultDto<PurPAApplicationDto>> GetPAInitiateListAsync(PAListSearchRequestDto requestDto);

        /// <summary>
        /// 任务中心-我审批的付款申请-列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<PagedResultDto<PurPAApplicationDto>> GetPAApplicationApprovalListAsync(PAListSearchRequestDto request);

        /// <summary>
        /// 更新补录文件
        /// </summary>
        /// <param name="paUploadFile"></param>
        /// <returns></returns>
        Task<MessageResult> UploadSponsorShipAsync(PaUploadFileDto paUploadFile);

        /// <summary>
        /// 提交付款
        /// </summary>
        /// <param name="paDetails"></param>
        /// <returns></returns>
        Task<MessageResult> SubmitPaInvoiceAsync(PADetailsEditDto paDetails);

        /// <summary>
        /// 获取当前供应商银行信息
        /// </summary>
        /// <param name="vendorId"></param>
        /// <returns></returns>
        Task<MessageResult> GetBankCardInfoAsync(Guid vendorId);

        /// <summary>
        /// PA作废
        /// </summary>
        /// <param name="invalid"></param>
        /// <returns></returns>
        Task<MessageResult> PAInvalidAsync(InvalidRequestDto invalid);

        /// <summary>
        /// 获取财务信息模块
        /// </summary>
        /// <param name="paId"></param>
        /// <returns></returns>
        Task<MessageResult> GetPAApplicicationFinancialVoucher(Guid paId);

        /// <summary>
        /// 获取财务凭证信息填写可选值
        /// </summary>
        /// <param name="paId"></param>
        /// <returns></returns>
       Task<OptionalFinancialInfoDto> GetOptionalFinancialInfoAsync(Guid paId);

        /// <summary>
        /// PA根据关键字获取费用性质
        /// </summary>
        /// <param name="paId"></param>
        /// <param name="keywords"></param>
        /// <returns></returns>
        Task<List<DropdownListDto<string, string>>> GetNatureAccountAsync(Guid paId, string keywords);

        /// <summary>
        /// 初审完成
        /// </summary>
        /// <param name="paPreliminaryApprovalCompleted"></param>
        /// <returns></returns>
        Task<MessageResult> PreliminaryApprovalCompletedAsync(PAPreliminaryApprovalCompletedDto paPreliminaryApprovalCompleted);

        /// <summary>
        /// 获取退回模板
        /// </summary>
        /// <returns></returns>
        Task<List<ReturnReasonTemplateDto>> GetWithdrawOpinionInfoAsync();

        /// <summary>
        /// 退回申请人、退回补件、退回原件
        /// </summary>
        /// <param name="pAReturnReason"></param>
        /// <returns></returns>
        Task<MessageResult> PAApplicationWithdrawAsync(PAInsertReturnReasonInfoDto pAReturnReason);

        /// <summary>
        /// 上传财务凭证信息时补全其他可填字段
        /// </summary>
        /// <param name="paFinancialVoucherInfos"></param>
        /// <param name="paId"></param>
        /// <returns></returns>
        Task<List<PAFinancialVoucherInfoDto>> GetPAFinancialVoucherInfoAsync(List<PAFinancialVoucherInfoDto> paFinancialVoucherInfos, Guid paId);

        /// <summary>
        /// 获取PA退回意见
        /// </summary>
        /// <param name="paId"></param>
        /// <returns></returns>
        Task<List<PAReturnReasonInfoReponseDto>> GetPAReturnReasonByPAIdAsync(Guid paId);

        /// <summary>
        /// 财务复审完成 同意
        /// </summary>
        /// <param name="approval"></param>
        /// <returns></returns>
        Task<MessageResult> FinancialReviewApprovalAsync(FinancialReviewApprovalDto approval);

        /// <summary>
        /// 财务复审完成 同意 批量
        /// </summary>
        /// <param name="approvals"></param>
        /// <returns></returns>
        Task<MessageResult> FinancialBatchReviewApprovalAsync(FinancialBatchReviewApprovalDto approvals);

        /// <summary>
        /// 更新PA退回意见
        /// </summary>
        /// <param name="pAReturnReason"></param>
        /// <returns></returns>
        Task<MessageResult> UpdatePAReturnReasonByPAIdAsync(List<PAUpdateReturnReasonInfoDto> pAReturnReason);

        /// <summary>
        /// PA最后一次付款后预算返还
        /// PA(返还): 最后一次付款复审通过后，退回每行[PR申请金额-已付款含税金额]
        /// </summary>
        /// <param name="paId"></param>
        /// <param name="rate"></param>
        /// <returns></returns>
        Task PABudgetRefundAsync(Guid paId,decimal rate);

        /// <summary>
        /// PA 撤回 （财务分单）
        /// </summary>
        /// <param name="paRevoke"></param>
        /// <returns></returns>
        Task<MessageResult> PARevokeApplicationAsync(PARevokeDto paRevoke);
    }
}
