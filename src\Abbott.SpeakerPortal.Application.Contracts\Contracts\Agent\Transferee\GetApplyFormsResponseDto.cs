﻿using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Utils;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using static Abbott.SpeakerPortal.Enums.ResignationTransfer;

namespace Abbott.SpeakerPortal.Contracts.Agent.Transferee
{
    public class GetApplyFormsResponseDto
    {
        /// <summary>
        /// 单据类型
        /// </summary>
        public string FormCategory { get; set; }
        public string FormCategoryName => GetFormCategoryName();
        /// <summary>
        /// 单据/审批任务Id
        /// </summary>
        public Guid Id { get; set; }
        /// <summary>
        /// 单据编号
        /// </summary>
        public string ApplicationCode { get; set; }
        /// <summary>
        /// 申请人Id
        /// </summary>
        public Guid? ApplyUserId { get; set; }
        /// <summary>
        /// 申请人姓名
        /// </summary>
        public string ApplyUserName { get; set; }
        /// <summary>
        /// 申请部门Id
        /// </summary>
        public string ApplyOrgId { get; set; }
        /// <summary>
        /// 申请部门名称
        /// </summary>
        public string ApplyOrgName { get; set; }
        /// <summary>
        /// 申请日期
        /// </summary>
        public DateTime? ApplyDate { get; set; }
        public string ApplyDateName => GetApplyDateName();
        /// <summary>
        /// 表单状态
        /// </summary>
        public int? FormStatus { get; set; }
        public string FormStatusName => GetFormStatusName();


        /// <summary>
        /// 表单上的分类
        /// </summary>
        public VendorTypes? VendorType { get; set; }
        /// <summary>
        /// 表单上的活动类型
        /// </summary>
        public ApplicationTypes? ActionType { get; set; }

        /// <summary>
        /// 豁免类型
        /// </summary>
        public Enums.Purchase.ExemptType? WaiverType { get; set; }


        private string GetFormStatusName()
        {
            if (string.IsNullOrEmpty(this.ApplicationCode) || !this.FormStatus.HasValue)
                return string.Empty;

            var res = string.Empty;
            switch (this.ApplicationCode.First())
            {
                case 'V':
                    res = ((Statuses)this.FormStatus).GetDescription();
                    break;
                case 'P':
                    res = ((Enums.Purchase.PurPRApplicationStatus)this.FormStatus).GetDescription();
                    break;
                case 'O':
                    res = ((Enums.Purchase.PurOrderStatus)this.FormStatus).GetDescription();
                    break;
                case 'B':
                    res = ((PurBDStatus)this.FormStatus).GetDescription();
                    break;
                case 'G':
                    res = ((Enums.PurGRStatus.PurGRApplicationStatus)this.FormStatus).GetDescription();
                    break;
                case 'A':
                    res = ((PurPAStatus.PurPAApplicationStatus)this.FormStatus).GetDescription();
                    break;
                case 'W':
                case 'J':
                    res = ((Enums.Purchase.PurExemptStatus)this.FormStatus).GetDescription();
                    break;
                case 'S':
                    res = this.FormCategoryName == TaskFormCategory.STicketRequestApplication.GetDescription() ? ((STicketStatus)this.FormStatus).GetDescription() : ((SpeakerAuthStatus)this.FormStatus).GetDescription();
                    break;
                case 'F':
                    res = ((FOCStatus)this.FormStatus).GetDescription();
                    break;
                default:
                    break;
            }

            return res;
        }

        private string GetFormCategoryName()
        {
            if (!string.IsNullOrEmpty(this.FormCategory))
                return this.FormCategory;

            if (string.IsNullOrEmpty(this.ApplicationCode))
                return string.Empty;

            var res = string.Empty;

            switch (this.ApplicationCode.First())
            {
                case 'V':
                    if (!this.VendorType.HasValue || !this.ActionType.HasValue)
                        break;
                    switch (this.VendorType)
                    {
                        case VendorTypes.HCPPerson:
                            if (this.ActionType == ApplicationTypes.Create)
                                res = ResignationTransfer.FormCategory.HCP_Creation.GetDescription();
                            else if (this.ActionType == ApplicationTypes.Update)
                                res = ResignationTransfer.FormCategory.HCP_Change.GetDescription();
                            else if (this.ActionType == ApplicationTypes.Active)
                                res = ResignationTransfer.FormCategory.HCP_Activation.GetDescription();
                            break;
                        case VendorTypes.NonHCPPerson:
                            if (this.ActionType == ApplicationTypes.Create)
                                res = ResignationTransfer.FormCategory.NonHCP_Creation.GetDescription();
                            else if (this.ActionType == ApplicationTypes.Update)
                                res = ResignationTransfer.FormCategory.NonHCP_Change.GetDescription();
                            else if (this.ActionType == ApplicationTypes.Active)
                                res = ResignationTransfer.FormCategory.NonHCP_Activation.GetDescription();
                            break;
                        case VendorTypes.HCIAndOtherInstitutionsAR:
                            if (this.ActionType == ApplicationTypes.Create)
                                res = ResignationTransfer.FormCategory.HCI_Creation.GetDescription();
                            else if (this.ActionType == ApplicationTypes.Update)
                                res = ResignationTransfer.FormCategory.HCI_Change.GetDescription();
                            else if (this.ActionType == ApplicationTypes.Active)
                                res = ResignationTransfer.FormCategory.HCI_Activation.GetDescription();
                            break;
                        case VendorTypes.NonHCIInstitutionalAP:
                            if (this.ActionType == ApplicationTypes.Create)
                                res = ResignationTransfer.FormCategory.NonHCI_Creation.GetDescription();
                            else if (this.ActionType == ApplicationTypes.Update)
                                res = ResignationTransfer.FormCategory.NonHCI_Change.GetDescription();
                            else if (this.ActionType == ApplicationTypes.Active)
                                res = ResignationTransfer.FormCategory.NonHCI_Activation.GetDescription();
                            break;
                        default:
                            break;
                    }
                    break;
                case 'P':
                    res = ResignationTransfer.FormCategory.PurchaseRequest.GetDescription();
                    break;
                case 'O':
                    res = ResignationTransfer.FormCategory.PurchaseOrder.GetDescription();
                    break;
                case 'B':
                    res = ResignationTransfer.FormCategory.Bidding.GetDescription();
                    break;
                case 'G':
                    res = ResignationTransfer.FormCategory.GoodsReceipt.GetDescription();
                    break;
                case 'A':
                    res = ResignationTransfer.FormCategory.PaymentApplication.GetDescription();
                    break;
                case 'W':
                case 'J':
                    if (this.WaiverType.HasValue && this.WaiverType == Enums.Purchase.ExemptType.Waiver)
                        res = ResignationTransfer.FormCategory.BiddingWaiver.GetDescription();
                    else if (this.WaiverType.HasValue && this.WaiverType == Enums.Purchase.ExemptType.Justification)
                        res = ResignationTransfer.FormCategory.BiddingWaiverJustification.GetDescription();
                    break;
                default:
                    break;
            }

            return res;
        }

        private string GetApplyDateName()
        {
            if (!this.ApplyDate.HasValue)
                return string.Empty;
            return ((DateTime)this.ApplyDate).ToString("yyyy-MM-dd");
        }
    }
}
