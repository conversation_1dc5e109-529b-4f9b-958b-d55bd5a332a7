﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Abbott.SpeakerPortal.Contracts.Budget
{
    public class BudgetResponseDto
    {
        /// <summary>
        /// 预算Id
        /// </summary>
        public Guid Id { get; set; }
        /// <summary>
        /// 预算编号
        /// </summary>
        public string Code { get; set; }
        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }
        /// <summary>
        /// 负责人Id
        /// </summary>
        public Guid OwnerId { get; set; }
        /// <summary>
        /// 负责人姓名
        /// </summary>
        public string OwnerName { get; set; }
        /// <summary>
        /// 固定资产
        /// </summary>
        public bool Capital { get; set; }
        /// <summary>
        /// 预算金额
        /// </summary>
        public decimal BudgetAmount { get; set; }
    }
}
