CREATE PROCEDURE [dbo].sp_BdAuthorizedBudgetBus
AS
BEGIN
	
	SELECT 
	 newid() as id
	,a.Emp_Id
	,TRIM(value) AS B_RoleId
	,a.OpDate
	,a.OpEmpId
	,a.IsEnable
INTO #BdAuthorizedBudgetBus_Tmp
FROM PLATFORM_ABBOTT_Stg.dbo.ODS_T_Employee_BURole_Info a
LEFT JOIN PLATFORM_ABBOTT_Stg.dbo.ODS_T_Resource b
ON a.B_RoleId = b.Res_Code
CROSS APPLY STRING_SPLIT(B_RoleId, ',') 
WHERE b.IsEnable = 1
AND b.Res_Name like N'%预算%'

    --删除表
    IF OBJECT_ID(N'PLATFORM_ABBOTT_Stg.dbo.BdAuthorizedBudgetBus_Tmp ', N'U') IS NOT NULL
	BEGIN
		drop table PLATFORM_ABBOTT_Stg.dbo.BdAuthorizedBudgetBus_Tmp
		select *
        into PLATFORM_ABBOTT_Stg.dbo.BdAuthorizedBudgetBus_Tmp from #BdAuthorizedBudgetBus_Tmp
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into PLATFORM_ABBOTT_Stg.dbo.BdAuthorizedBudgetBus_Tmp from #BdAuthorizedBudgetBus_Tmp
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	  	
END;