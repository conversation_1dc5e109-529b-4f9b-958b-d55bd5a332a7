﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;
using Abbott.SpeakerPortal.Dtos;
using Abbott.SpeakerPortal.Enums;

using Volo.Abp.Application.Dtos;

namespace Abbott.SpeakerPortal.Vendor.Speaker
{
    public class ValidationSpeakerRequestDto
    {
        /// <summary>
        /// 讲者编号
        /// </summary>
        public string VendorCode { get; set; }
        /// <summary>
        /// 讲者名称
        /// </summary>
        //[Required]
        [MaxLength(20)]
        public string SPName { get; set; }
        /// <summary>
        /// 职称Id
        /// </summary>
        [Required]
        public Guid PTId { get; set; }
        /// <summary>
        /// 所属医院Id
        /// </summary>
        //[Required]
        public Guid? HospitalId { get; set; }
        /// <summary>
        /// 标准科室Id
        /// </summary>
        [Required]
        public Guid StandardHosDepId { get; set; }

        /// <summary>
        /// 证件编号
        /// </summary>
        public string CertificateCode { get; set; }
    }
}
