﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.Contracts.Integration.EpdPortal.EpdHcp
{
    public interface IEPDHcpService
    {
        /// <summary>
        /// 医生查询接口
        /// </summary>
        /// <returns></returns>
        Task<List<DoctorData>> QueryEpdDoctorsAsync(DoctorQueryRequest doctorRequest);
    }
}
