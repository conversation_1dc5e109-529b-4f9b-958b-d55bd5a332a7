﻿using Abbott.SpeakerPortal.Localization;

using Azure.Storage.Blobs.Models;

using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Localization;

using static Abbott.SpeakerPortal.Permissions.SpeakerPortalPermissions;

namespace Abbott.SpeakerPortal.Permissions;

public class SpeakerPortalPermissionDefinitionProvider : PermissionDefinitionProvider
{
    public override void Define(IPermissionDefinitionContext context)
    {
        #region 采购
        //采购管理菜单组
        var purchaseGroup = context.AddGroup(SpeakerPortalPermissions.PurchaseManagement, L($"Permission:{SpeakerPortalPermissions.PurchaseManagement}"));
        //采购列表二级菜单
        var purchaseApplication = purchaseGroup.AddPermission(SpeakerPortalPermissions.Purchase.Application, L($"Permission:{SpeakerPortalPermissions.Purchase.Application}"));
        purchaseApplication.AddChild(SpeakerPortalPermissions.Purchase.ApplicationRead, L($"Permission:{SpeakerPortalPermissions.Purchase.ApplicationRead}"));
        purchaseApplication.AddChild(SpeakerPortalPermissions.Purchase.ApplicationCRUD, L($"Permission:{SpeakerPortalPermissions.Purchase.ApplicationCRUD}"));
        purchaseApplication.AddChild(SpeakerPortalPermissions.Purchase.ApplicationExport, L($"Permission:{SpeakerPortalPermissions.Purchase.ApplicationExport}"));
        purchaseApplication.AddChild(SpeakerPortalPermissions.Purchase.ApplicationPrint, L($"Permission:{SpeakerPortalPermissions.Purchase.ApplicationPrint}"));
        //采购订单二级菜单
        var purchaseOrder = purchaseGroup.AddPermission(SpeakerPortalPermissions.Purchase.Order, L($"Permission:{SpeakerPortalPermissions.Purchase.Order}"));
        purchaseOrder.AddChild(SpeakerPortalPermissions.Purchase.OrderRead, L($"Permission:{SpeakerPortalPermissions.Purchase.OrderRead}"));
        purchaseOrder.AddChild(SpeakerPortalPermissions.Purchase.OrderCRUD, L($"Permission:{SpeakerPortalPermissions.Purchase.OrderCRUD}"));
        purchaseOrder.AddChild(SpeakerPortalPermissions.Purchase.OrderExport, L($"Permission:{SpeakerPortalPermissions.Purchase.OrderExport}"));
        purchaseOrder.AddChild(SpeakerPortalPermissions.Purchase.OrderPrint, L($"Permission:{SpeakerPortalPermissions.Purchase.OrderPrint}"));
        //收货列表二级菜单
        var purchaseReceipt = purchaseGroup.AddPermission(SpeakerPortalPermissions.Purchase.Receipt, L($"Permission:{SpeakerPortalPermissions.Purchase.Receipt}"));
        purchaseReceipt.AddChild(SpeakerPortalPermissions.Purchase.ReceiptReturn, L($"Permission:{SpeakerPortalPermissions.Purchase.ReceiptReturn}"));
        purchaseReceipt.AddChild(SpeakerPortalPermissions.Purchase.ReceiptPrint, L($"Permission:{SpeakerPortalPermissions.Purchase.ReceiptPrint}"));
        purchaseReceipt.AddChild(SpeakerPortalPermissions.Purchase.ReceiptRead, L($"Permission:{SpeakerPortalPermissions.Purchase.ReceiptRead}"));
        //付款列表二级菜单
        var purchasePayment = purchaseGroup.AddPermission(SpeakerPortalPermissions.Purchase.Payment, L($"Permission:{SpeakerPortalPermissions.Purchase.Payment}"));
        purchasePayment.AddChild(SpeakerPortalPermissions.Purchase.PaymentPrint, L($"Permission:{SpeakerPortalPermissions.Purchase.PaymentPrint}"));
        purchasePayment.AddChild(SpeakerPortalPermissions.Purchase.PaymentRead, L($"Permission:{SpeakerPortalPermissions.Purchase.PaymentRead}"));
        purchasePayment.AddChild(SpeakerPortalPermissions.Purchase.PaymentCRUD, L($"Permission:{SpeakerPortalPermissions.Purchase.PaymentCRUD}"));
        purchasePayment.AddChild(SpeakerPortalPermissions.Purchase.PaymentExport, L($"Permission:{SpeakerPortalPermissions.Purchase.PaymentExport}"));
        //财务出纳支付管理二级菜单
        var purchaseFinance = purchaseGroup.AddPermission(SpeakerPortalPermissions.Purchase.Finance, L($"Permission:{SpeakerPortalPermissions.Purchase.Finance}"));
        purchaseFinance.AddChild(SpeakerPortalPermissions.Purchase.FinanceCRUD, L($"Permission:{SpeakerPortalPermissions.Purchase.FinanceCRUD}"));
        purchaseFinance.AddChild(SpeakerPortalPermissions.Purchase.FinanceExport, L($"Permission:{SpeakerPortalPermissions.Purchase.FinanceExport}"));
        //供应商比价二级菜单
        var purchaseBidding = purchaseGroup.AddPermission(SpeakerPortalPermissions.Purchase.Bidding, L($"Permission:{SpeakerPortalPermissions.Purchase.Bidding}"));
        purchaseBidding.AddChild(SpeakerPortalPermissions.Purchase.BiddingCRUD, L($"Permission:{SpeakerPortalPermissions.Purchase.BiddingCRUD}"));
        purchaseBidding.AddChild(SpeakerPortalPermissions.Purchase.BiddingExport, L($"Permission:{SpeakerPortalPermissions.Purchase.BiddingExport}"));
        purchaseBidding.AddChild(SpeakerPortalPermissions.Purchase.BiddingPrint, L($"Permission:{SpeakerPortalPermissions.Purchase.BiddingPrint}"));
        purchaseBidding.AddChild(SpeakerPortalPermissions.Purchase.BiddingRead, L($"Permission:{SpeakerPortalPermissions.Purchase.BiddingRead}"));
        //Justification二级菜单
        var purchaseJustification = purchaseGroup.AddPermission(SpeakerPortalPermissions.Purchase.Justification, L($"Permission:{SpeakerPortalPermissions.Purchase.Justification}"));
        purchaseJustification.AddChild(SpeakerPortalPermissions.Purchase.JustificationRead, L($"Permission:{SpeakerPortalPermissions.Purchase.JustificationRead}"));
        purchaseJustification.AddChild(SpeakerPortalPermissions.Purchase.JustificationCRUD, L($"Permission:{SpeakerPortalPermissions.Purchase.JustificationCRUD}"));
        purchaseJustification.AddChild(SpeakerPortalPermissions.Purchase.JustificationExport, L($"Permission:{SpeakerPortalPermissions.Purchase.JustificationExport}"));
        purchaseJustification.AddChild(SpeakerPortalPermissions.Purchase.JustificationPrint, L($"Permission:{SpeakerPortalPermissions.Purchase.JustificationPrint}"));
        //竞价豁免二级菜单
        var purchaseBiddingExemption = purchaseGroup.AddPermission(SpeakerPortalPermissions.Purchase.BiddingExemption, L($"Permission:{SpeakerPortalPermissions.Purchase.BiddingExemption}"));
        purchaseBiddingExemption.AddChild(SpeakerPortalPermissions.Purchase.BiddingExemptionRead, L($"Permission:{SpeakerPortalPermissions.Purchase.BiddingExemptionRead}"));
        purchaseBiddingExemption.AddChild(SpeakerPortalPermissions.Purchase.BiddingExemptionCRUD, L($"Permission:{SpeakerPortalPermissions.Purchase.BiddingExemptionCRUD}"));
        purchaseBiddingExemption.AddChild(SpeakerPortalPermissions.Purchase.BiddingExemptionExport, L($"Permission:{SpeakerPortalPermissions.Purchase.BiddingExemptionExport}"));
        purchaseBiddingExemption.AddChild(SpeakerPortalPermissions.Purchase.BiddingExemptionPrint, L($"Permission:{SpeakerPortalPermissions.Purchase.BiddingExemptionPrint}"));
        //第三方讲者二级菜单
        var purchaseTripartite = purchaseGroup.AddPermission(SpeakerPortalPermissions.Purchase.Tripartite, L($"Permission:{SpeakerPortalPermissions.Purchase.Tripartite}"));
        purchaseTripartite.AddChild(SpeakerPortalPermissions.Purchase.TripartiteRead, L($"Permission:{SpeakerPortalPermissions.Purchase.TripartiteRead}"));
        purchaseTripartite.AddChild(SpeakerPortalPermissions.Purchase.TripartiteCRUD, L($"Permission:{SpeakerPortalPermissions.Purchase.TripartiteCRUD}"));
        purchaseTripartite.AddChild(SpeakerPortalPermissions.Purchase.TripartiteExport, L($"Permission:{SpeakerPortalPermissions.Purchase.TripartiteExport}"));

        #endregion

        #region 供应商管理
        //供应商菜单组
        var vendorGroup = context.AddGroup(SpeakerPortalPermissions.VendorManagement, L($"Permission:{SpeakerPortalPermissions.VendorManagement}"));
        //讲者二级菜单
        var speaker = vendorGroup.AddPermission(SpeakerPortalPermissions.Vendor.Speaker, L($"Permission:{SpeakerPortalPermissions.Vendor.Speaker}"));
        speaker.AddChild(SpeakerPortalPermissions.Vendor.SpeakerChange, L($"Permission:{SpeakerPortalPermissions.Vendor.SpeakerChange}"));
        speaker.AddChild(SpeakerPortalPermissions.Vendor.SpeakerActivate, L($"Permission:{SpeakerPortalPermissions.Vendor.SpeakerActivate}"));
        speaker.AddChild(SpeakerPortalPermissions.Vendor.SpeakerCompliance, L($"Permission:{SpeakerPortalPermissions.Vendor.SpeakerCompliance}"));
        speaker.AddChild(SpeakerPortalPermissions.Vendor.SpeakerCRUD, L($"Permission:{SpeakerPortalPermissions.Vendor.SpeakerCRUD}"));
        speaker.AddChild(SpeakerPortalPermissions.Vendor.SpeakerExport, L($"Permission:{SpeakerPortalPermissions.Vendor.SpeakerExport}"));
        speaker.AddChild(SpeakerPortalPermissions.Vendor.SpeakerBlackList, L($"Permission:{SpeakerPortalPermissions.Vendor.SpeakerBlackList}"));
        speaker.AddChild(SpeakerPortalPermissions.Vendor.SpeakerRead, L($"Permission:{SpeakerPortalPermissions.Vendor.SpeakerRead}"));
        //非HCP个人二级菜单
        var noneSpeaker = vendorGroup.AddPermission(SpeakerPortalPermissions.Vendor.NoneSpeaker, L($"Permission:{SpeakerPortalPermissions.Vendor.NoneSpeaker}"));
        noneSpeaker.AddChild(SpeakerPortalPermissions.Vendor.NoneSpeakerChange, L($"Permission:{SpeakerPortalPermissions.Vendor.NoneSpeakerChange}"));
        noneSpeaker.AddChild(SpeakerPortalPermissions.Vendor.NoneSpeakerActivate, L($"Permission:{SpeakerPortalPermissions.Vendor.NoneSpeakerActivate}"));
        noneSpeaker.AddChild(SpeakerPortalPermissions.Vendor.NoneSpeakerCRUD, L($"Permission:{SpeakerPortalPermissions.Vendor.NoneSpeakerCRUD}"));
        noneSpeaker.AddChild(SpeakerPortalPermissions.Vendor.NoneSpeakerExport, L($"Permission:{SpeakerPortalPermissions.Vendor.NoneSpeakerExport}"));
        noneSpeaker.AddChild(SpeakerPortalPermissions.Vendor.NoneSpeakerRead, L($"Permission:{SpeakerPortalPermissions.Vendor.NoneSpeakerRead}"));
        //HCI机构二级菜单
        var hciOrg = vendorGroup.AddPermission(SpeakerPortalPermissions.Vendor.HCI, L($"Permission:{SpeakerPortalPermissions.Vendor.HCI}"));
        hciOrg.AddChild(SpeakerPortalPermissions.Vendor.HCIChange, L($"Permission:{SpeakerPortalPermissions.Vendor.HCIChange}"));
        hciOrg.AddChild(SpeakerPortalPermissions.Vendor.HCIActivate, L($"Permission:{SpeakerPortalPermissions.Vendor.HCIActivate}"));
        hciOrg.AddChild(SpeakerPortalPermissions.Vendor.HCICRUD, L($"Permission:{SpeakerPortalPermissions.Vendor.HCICRUD}"));
        hciOrg.AddChild(SpeakerPortalPermissions.Vendor.HCIExport, L($"Permission:{SpeakerPortalPermissions.Vendor.HCIExport}"));
        hciOrg.AddChild(SpeakerPortalPermissions.Vendor.HCIBlackList, L($"Permission:{SpeakerPortalPermissions.Vendor.HCIBlackList}"));
        hciOrg.AddChild(SpeakerPortalPermissions.Vendor.HCIRead, L($"Permission:{SpeakerPortalPermissions.Vendor.HCIRead}"));
        //非HCI机构二级菜单
        var noneHciOrg = vendorGroup.AddPermission(SpeakerPortalPermissions.Vendor.NoneHCI, L($"Permission:{SpeakerPortalPermissions.Vendor.NoneHCI}"));
        noneHciOrg.AddChild(SpeakerPortalPermissions.Vendor.NoneHCIChange, L($"Permission:{SpeakerPortalPermissions.Vendor.NoneHCIChange}"));
        noneHciOrg.AddChild(SpeakerPortalPermissions.Vendor.NoneHCIActivate, L($"Permission:{SpeakerPortalPermissions.Vendor.NoneHCIActivate}"));
        noneHciOrg.AddChild(SpeakerPortalPermissions.Vendor.NoneHCICRUD, L($"Permission:{SpeakerPortalPermissions.Vendor.NoneHCICRUD}"));
        noneHciOrg.AddChild(SpeakerPortalPermissions.Vendor.NoneHCIExport, L($"Permission:{SpeakerPortalPermissions.Vendor.NoneHCIExport}"));
        noneHciOrg.AddChild(SpeakerPortalPermissions.Vendor.NoneHCIAPSEdit, L($"Permission:{SpeakerPortalPermissions.Vendor.NoneHCIAPSEdit}"));
        noneHciOrg.AddChild(SpeakerPortalPermissions.Vendor.NoneHCIRead, L($"Permission:{SpeakerPortalPermissions.Vendor.NoneHCIRead}"));
        //供应商查询二级菜单
        var query = vendorGroup.AddPermission(SpeakerPortalPermissions.Vendor.Query, L($"Permission:{SpeakerPortalPermissions.Vendor.Query}"));
        query.AddChild(SpeakerPortalPermissions.Vendor.QueryExport, L($"Permission:{SpeakerPortalPermissions.Vendor.QueryExport}"));
        query.AddChild(SpeakerPortalPermissions.Vendor.QueryDelete, L($"Permission:{SpeakerPortalPermissions.Vendor.QueryDelete}"));

        //供应商申请查询二级菜单
        var applicationQuery = vendorGroup.AddPermission(SpeakerPortalPermissions.Vendor.ApplicationQuery, L($"Permission:{SpeakerPortalPermissions.Vendor.ApplicationQuery}"));
        applicationQuery.AddChild(SpeakerPortalPermissions.Vendor.ApplicationQueryDelete, L($"Permission:{SpeakerPortalPermissions.Vendor.ApplicationQueryDelete}"));
        #endregion

        #region 任务中心
        //任务中心菜单组
        var taskCenterGroup = context.AddGroup(SpeakerPortalPermissions.TaskCenterManagement, L($"Permission:{SpeakerPortalPermissions.TaskCenterManagement}"));
        //我发起的二级菜单
        var taskCenterLaunch = taskCenterGroup.AddPermission(SpeakerPortalPermissions.TaskCenter.Launch, L($"Permission:{SpeakerPortalPermissions.TaskCenter.Launch}"));
        taskCenterLaunch.AddChild(SpeakerPortalPermissions.TaskCenter.LaunchSpeakerCreate, L($"Permission:{SpeakerPortalPermissions.TaskCenter.LaunchSpeakerCreate}"));
        taskCenterLaunch.AddChild(SpeakerPortalPermissions.TaskCenter.LaunchSpeakerChange, L($"Permission:{SpeakerPortalPermissions.TaskCenter.LaunchSpeakerChange}"));
        taskCenterLaunch.AddChild(SpeakerPortalPermissions.TaskCenter.LaunchSpeakerActivate, L($"Permission:{SpeakerPortalPermissions.TaskCenter.LaunchSpeakerActivate}"));
        taskCenterLaunch.AddChild(SpeakerPortalPermissions.TaskCenter.LaunchVendorCreate, L($"Permission:{SpeakerPortalPermissions.TaskCenter.LaunchVendorCreate}"));
        taskCenterLaunch.AddChild(SpeakerPortalPermissions.TaskCenter.LaunchVendorChange, L($"Permission:{SpeakerPortalPermissions.TaskCenter.LaunchVendorChange}"));
        taskCenterLaunch.AddChild(SpeakerPortalPermissions.TaskCenter.LaunchVendorActivate, L($"Permission:{SpeakerPortalPermissions.TaskCenter.LaunchVendorActivate}"));
        taskCenterLaunch.AddChild(SpeakerPortalPermissions.TaskCenter.LaunchProcureApplication, L($"Permission:{SpeakerPortalPermissions.TaskCenter.LaunchProcureApplication}"));
        taskCenterLaunch.AddChild(SpeakerPortalPermissions.TaskCenter.LaunchBidding, L($"Permission:{SpeakerPortalPermissions.TaskCenter.LaunchBidding}"));
        taskCenterLaunch.AddChild(SpeakerPortalPermissions.TaskCenter.LaunchJustification, L($"Permission:{SpeakerPortalPermissions.TaskCenter.LaunchJustification}"));
        taskCenterLaunch.AddChild(SpeakerPortalPermissions.TaskCenter.LaunchVendorComparision, L($"Permission:{SpeakerPortalPermissions.TaskCenter.LaunchVendorComparision}"));
        taskCenterLaunch.AddChild(SpeakerPortalPermissions.TaskCenter.LaunchProcureOrder, L($"Permission:{SpeakerPortalPermissions.TaskCenter.LaunchProcureOrder}"));
        taskCenterLaunch.AddChild(SpeakerPortalPermissions.TaskCenter.LaunchReceiptApplication, L($"Permission:{SpeakerPortalPermissions.TaskCenter.LaunchReceiptApplication}"));
        taskCenterLaunch.AddChild(SpeakerPortalPermissions.TaskCenter.LaunchPaymentApplication, L($"Permission:{SpeakerPortalPermissions.TaskCenter.LaunchPaymentApplication}"));
        taskCenterLaunch.AddChild(SpeakerPortalPermissions.TaskCenter.LaunchAction, L($"Permission:{SpeakerPortalPermissions.TaskCenter.LaunchAction}"));
        taskCenterLaunch.AddChild(SpeakerPortalPermissions.TaskCenter.LaunchExport, L($"Permission:{SpeakerPortalPermissions.TaskCenter.LaunchExport}"));
        taskCenterLaunch.AddChild(SpeakerPortalPermissions.TaskCenter.LaunchPrint, L($"Permission:{SpeakerPortalPermissions.TaskCenter.LaunchPrint}"));
        taskCenterLaunch.AddChild(SpeakerPortalPermissions.TaskCenter.SpeakerAuthLaunch, L($"Permission:{SpeakerPortalPermissions.TaskCenter.SpeakerAuthLaunch}"));
        taskCenterLaunch.AddChild(SpeakerPortalPermissions.TaskCenter.LaunchSTicketApplication, L($"Permission:{SpeakerPortalPermissions.TaskCenter.LaunchSTicketApplication}"));
        taskCenterLaunch.AddChild(SpeakerPortalPermissions.TaskCenter.LaunchFocApplication, L($"Permission:{SpeakerPortalPermissions.TaskCenter.LaunchFocApplication}"));
        taskCenterLaunch.AddChild(SpeakerPortalPermissions.TaskCenter.LaunchReturnApplication, L($"Permission:{SpeakerPortalPermissions.TaskCenter.LaunchReturnApplication}"));
        taskCenterLaunch.AddChild(SpeakerPortalPermissions.TaskCenter.LaunchExchangeApplication, L($"Permission:{SpeakerPortalPermissions.TaskCenter.LaunchExchangeApplication}"));
        //我审批的二级菜单
        var taskCenterApprove = taskCenterGroup.AddPermission(SpeakerPortalPermissions.TaskCenter.Approve, L($"Permission:{SpeakerPortalPermissions.TaskCenter.Approve}"));
        taskCenterApprove.AddChild(SpeakerPortalPermissions.TaskCenter.ApproveSpeakerCreate, L($"Permission:{SpeakerPortalPermissions.TaskCenter.ApproveSpeakerCreate}"));
        taskCenterApprove.AddChild(SpeakerPortalPermissions.TaskCenter.ApproveSpeakerChange, L($"Permission:{SpeakerPortalPermissions.TaskCenter.ApproveSpeakerChange}"));
        taskCenterApprove.AddChild(SpeakerPortalPermissions.TaskCenter.ApproveSpeakerActivate, L($"Permission:{SpeakerPortalPermissions.TaskCenter.ApproveSpeakerActivate}"));
        taskCenterApprove.AddChild(SpeakerPortalPermissions.TaskCenter.ApproveVendorCreate, L($"Permission:{SpeakerPortalPermissions.TaskCenter.ApproveVendorCreate}"));
        taskCenterApprove.AddChild(SpeakerPortalPermissions.TaskCenter.ApproveVendorChange, L($"Permission:{SpeakerPortalPermissions.TaskCenter.ApproveVendorChange}"));
        taskCenterApprove.AddChild(SpeakerPortalPermissions.TaskCenter.ApproveVendorActivate, L($"Permission:{SpeakerPortalPermissions.TaskCenter.ApproveVendorActivate}"));
        taskCenterApprove.AddChild(SpeakerPortalPermissions.TaskCenter.ApproveProcureApplication, L($"Permission:{SpeakerPortalPermissions.TaskCenter.ApproveProcureApplication}"));
        taskCenterApprove.AddChild(SpeakerPortalPermissions.TaskCenter.ApproveBidding, L($"Permission:{SpeakerPortalPermissions.TaskCenter.ApproveBidding}"));
        taskCenterApprove.AddChild(SpeakerPortalPermissions.TaskCenter.ApproveJustification, L($"Permission:{SpeakerPortalPermissions.TaskCenter.ApproveJustification}"));
        taskCenterApprove.AddChild(SpeakerPortalPermissions.TaskCenter.ApproveVendorComparision, L($"Permission:{SpeakerPortalPermissions.TaskCenter.ApproveVendorComparision}"));
        taskCenterApprove.AddChild(SpeakerPortalPermissions.TaskCenter.ApproveProcureOrder, L($"Permission:{SpeakerPortalPermissions.TaskCenter.ApproveProcureOrder}"));
        taskCenterApprove.AddChild(SpeakerPortalPermissions.TaskCenter.ApproveReceiptApplication, L($"Permission:{SpeakerPortalPermissions.TaskCenter.ApproveReceiptApplication}"));
        taskCenterApprove.AddChild(SpeakerPortalPermissions.TaskCenter.ApprovePaymentApplication, L($"Permission:{SpeakerPortalPermissions.TaskCenter.ApprovePaymentApplication}"));
        taskCenterApprove.AddChild(SpeakerPortalPermissions.TaskCenter.ApproveAction, L($"Permission:{SpeakerPortalPermissions.TaskCenter.ApproveAction}"));
        taskCenterApprove.AddChild(SpeakerPortalPermissions.TaskCenter.ApproveExport, L($"Permission:{SpeakerPortalPermissions.TaskCenter.ApproveExport}"));
        taskCenterApprove.AddChild(SpeakerPortalPermissions.TaskCenter.ApprovePrint, L($"Permission:{SpeakerPortalPermissions.TaskCenter.ApprovePrint}"));
        taskCenterApprove.AddChild(SpeakerPortalPermissions.TaskCenter.SpeakerAuthApprove, L($"Permission:{SpeakerPortalPermissions.TaskCenter.SpeakerAuthApprove}"));
        taskCenterApprove.AddChild(SpeakerPortalPermissions.TaskCenter.BatchPaymentApproval, L($"Permission:{SpeakerPortalPermissions.TaskCenter.BatchPaymentApproval}"));
        taskCenterApprove.AddChild(SpeakerPortalPermissions.TaskCenter.ApproveSTicketApplication, L($"Permission:{SpeakerPortalPermissions.TaskCenter.ApproveSTicketApplication}"));
        taskCenterApprove.AddChild(SpeakerPortalPermissions.TaskCenter.ApproveFocApplication, L($"Permission:{SpeakerPortalPermissions.TaskCenter.ApproveFocApplication}"));
        taskCenterApprove.AddChild(SpeakerPortalPermissions.TaskCenter.ApproveReturnApplication, L($"Permission:{SpeakerPortalPermissions.TaskCenter.ApproveReturnApplication}"));
        taskCenterApprove.AddChild(SpeakerPortalPermissions.TaskCenter.ApproveExchangeApplication, L($"Permission:{SpeakerPortalPermissions.TaskCenter.ApproveExchangeApplication}"));
        //付款审批任务二级菜单
        var taskCenterPayment = taskCenterGroup.AddPermission(SpeakerPortalPermissions.TaskCenter.Payment, L($"Permission:{SpeakerPortalPermissions.TaskCenter.Payment}"));
        taskCenterPayment.AddChild(SpeakerPortalPermissions.TaskCenter.PaymentSplitOrder, L($"Permission:{SpeakerPortalPermissions.TaskCenter.PaymentSplitOrder}"));
        taskCenterPayment.AddChild(SpeakerPortalPermissions.TaskCenter.PaymentExport, L($"Permission:{SpeakerPortalPermissions.TaskCenter.PaymentExport}"));
        //我的采购推送二级菜单
        var taskCenterProcurePush = taskCenterGroup.AddPermission(SpeakerPortalPermissions.TaskCenter.ProcurePush, L($"Permission:{SpeakerPortalPermissions.TaskCenter.ProcurePush}"));
        taskCenterProcurePush.AddChild(SpeakerPortalPermissions.TaskCenter.ProcurePushBidding, L($"Permission:{SpeakerPortalPermissions.TaskCenter.ProcurePushBidding}"));
        taskCenterProcurePush.AddChild(SpeakerPortalPermissions.TaskCenter.ProcurePushPO, L($"Permission:{SpeakerPortalPermissions.TaskCenter.ProcurePushPO}"));
        taskCenterProcurePush.AddChild(SpeakerPortalPermissions.TaskCenter.ProcurePushReturn, L($"Permission:{SpeakerPortalPermissions.TaskCenter.ProcurePushReturn}"));
        taskCenterProcurePush.AddChild(SpeakerPortalPermissions.TaskCenter.ProcurePushExport, L($"Permission:{SpeakerPortalPermissions.TaskCenter.ProcurePushExport}"));
        //任务代理 二级菜单
        var taskCenterTaskProxy = taskCenterGroup.AddPermission(SpeakerPortalPermissions.TaskCenter.TaskProxy, L($"Permission:{SpeakerPortalPermissions.TaskCenter.TaskProxy}"));
        taskCenterTaskProxy.AddChild(SpeakerPortalPermissions.TaskCenter.TaskProxyCRUD, L($"Permission:{SpeakerPortalPermissions.TaskCenter.TaskProxyCRUD}"));
        #endregion

        #region 预算管理
        //预算管理菜单组
        var budgetGroup = context.AddGroup(SpeakerPortalPermissions.BudgetManagement, L($"Permission:{SpeakerPortalPermissions.BudgetManagement}"));
        //主预算管理二级菜单
        var mainBudget = budgetGroup.AddPermission(SpeakerPortalPermissions.Budget.Main, L($"Permission:{SpeakerPortalPermissions.Budget.Main}"));
        mainBudget.AddChild(SpeakerPortalPermissions.Budget.MainCRUD, L($"Permission:{SpeakerPortalPermissions.Budget.MainCRUD}"));
        mainBudget.AddChild(SpeakerPortalPermissions.Budget.MainFreeze, L($"Permission:{SpeakerPortalPermissions.Budget.MainFreeze}"));
        mainBudget.AddChild(SpeakerPortalPermissions.Budget.MainEnable, L($"Permission:{SpeakerPortalPermissions.Budget.MainEnable}"));
        mainBudget.AddChild(SpeakerPortalPermissions.Budget.MainAdjust, L($"Permission:{SpeakerPortalPermissions.Budget.MainAdjust}"));
        mainBudget.AddChild(SpeakerPortalPermissions.Budget.MainTransfer, L($"Permission:{SpeakerPortalPermissions.Budget.MainTransfer}"));
        mainBudget.AddChild(SpeakerPortalPermissions.Budget.MainRead, L($"Permission:{SpeakerPortalPermissions.Budget.MainRead}"));
        mainBudget.AddChild(SpeakerPortalPermissions.Budget.MainExport, L($"Permission:{SpeakerPortalPermissions.Budget.MainExport}"));
        //子预算管理二级菜单
        var subbudget = budgetGroup.AddPermission(SpeakerPortalPermissions.Budget.Child, L($"Permission:{SpeakerPortalPermissions.Budget.Child}"));
        subbudget.AddChild(SpeakerPortalPermissions.Budget.ChildCRUD, L($"Permission:{SpeakerPortalPermissions.Budget.ChildCRUD}"));
        subbudget.AddChild(SpeakerPortalPermissions.Budget.ChildFreeze, L($"Permission:{SpeakerPortalPermissions.Budget.ChildFreeze}"));
        subbudget.AddChild(SpeakerPortalPermissions.Budget.ChildEnable, L($"Permission:{SpeakerPortalPermissions.Budget.ChildEnable}"));
        subbudget.AddChild(SpeakerPortalPermissions.Budget.ChildAdjust, L($"Permission:{SpeakerPortalPermissions.Budget.ChildAdjust}"));
        subbudget.AddChild(SpeakerPortalPermissions.Budget.ChildTransfer, L($"Permission:{SpeakerPortalPermissions.Budget.ChildTransfer}"));
        subbudget.AddChild(SpeakerPortalPermissions.Budget.ChildMapping, L($"Permission:{SpeakerPortalPermissions.Budget.ChildMapping}"));
        subbudget.AddChild(SpeakerPortalPermissions.Budget.ChildRead, L($"Permission:{SpeakerPortalPermissions.Budget.ChildRead}"));
        subbudget.AddChild(SpeakerPortalPermissions.Budget.ChildExport, L($"Permission:{SpeakerPortalPermissions.Budget.ChildExport}"));
        //FOC主预算管理二级菜单
        var focMainBudget = budgetGroup.AddPermission(SpeakerPortalPermissions.Budget.FocMain, L($"Permission:{SpeakerPortalPermissions.Budget.FocMain}"));
        focMainBudget.AddChild(SpeakerPortalPermissions.Budget.FocMainCRUD, L($"Permission:{SpeakerPortalPermissions.Budget.FocMainCRUD}"));
        focMainBudget.AddChild(SpeakerPortalPermissions.Budget.FocMainFreeze, L($"Permission:{SpeakerPortalPermissions.Budget.FocMainFreeze}"));
        focMainBudget.AddChild(SpeakerPortalPermissions.Budget.FocMainEnable, L($"Permission:{SpeakerPortalPermissions.Budget.FocMainEnable}"));
        focMainBudget.AddChild(SpeakerPortalPermissions.Budget.FocMainAdjust, L($"Permission:{SpeakerPortalPermissions.Budget.FocMainAdjust}"));
        focMainBudget.AddChild(SpeakerPortalPermissions.Budget.FocMainRead, L($"Permission:{SpeakerPortalPermissions.Budget.FocMainRead}"));
        focMainBudget.AddChild(SpeakerPortalPermissions.Budget.FocMainExport, L($"Permission:{SpeakerPortalPermissions.Budget.FocMainExport}"));
        //FOC子预算管理二级菜单
        var focSubbudget = budgetGroup.AddPermission(SpeakerPortalPermissions.Budget.FocChild, L($"Permission:{SpeakerPortalPermissions.Budget.FocChild}"));
        focSubbudget.AddChild(SpeakerPortalPermissions.Budget.FocChildCRUD, L($"Permission:{SpeakerPortalPermissions.Budget.FocChildCRUD}"));
        focSubbudget.AddChild(SpeakerPortalPermissions.Budget.FocChildFreeze, L($"Permission:{SpeakerPortalPermissions.Budget.FocChildFreeze}"));
        focSubbudget.AddChild(SpeakerPortalPermissions.Budget.FocChildEnable, L($"Permission:{SpeakerPortalPermissions.Budget.FocChildEnable}"));
        focSubbudget.AddChild(SpeakerPortalPermissions.Budget.FocChildAdjust, L($"Permission:{SpeakerPortalPermissions.Budget.FocChildAdjust}"));
        focSubbudget.AddChild(SpeakerPortalPermissions.Budget.FocChildRead, L($"Permission:{SpeakerPortalPermissions.Budget.FocChildRead}"));
        focSubbudget.AddChild(SpeakerPortalPermissions.Budget.FocChildExport, L($"Permission:{SpeakerPortalPermissions.Budget.FocChildExport}"));
        #endregion

        #region 合规管理
        //合规管理菜单组
        var complianceGroup = context.AddGroup(SpeakerPortalPermissions.ComplianceManagement, L($"Permission:{SpeakerPortalPermissions.ComplianceManagement}"));
        //讲者级别二级菜单
        var complianceSpeakerLevel = complianceGroup.AddPermission(SpeakerPortalPermissions.Compliance.SpeakerLevel, L($"Permission:{SpeakerPortalPermissions.Compliance.SpeakerLevel}"));
        complianceSpeakerLevel.AddChild(SpeakerPortalPermissions.Compliance.SpeakerLevelCRUD, L($"Permission:{SpeakerPortalPermissions.Compliance.SpeakerLevelCRUD}"));
        //计酬标准配置二级菜单
        var complianceCompensation = complianceGroup.AddPermission(SpeakerPortalPermissions.Compliance.Compensation, L($"Permission:{SpeakerPortalPermissions.Compliance.Compensation}"));
        complianceCompensation.AddChild(SpeakerPortalPermissions.Compliance.CompensationCRUD, L($"Permission:{SpeakerPortalPermissions.Compliance.CompensationCRUD}"));
        complianceCompensation.AddChild(SpeakerPortalPermissions.Compliance.CompensationRead, L($"Permission:{SpeakerPortalPermissions.Compliance.CompensationRead}"));
        //黑名单管理二级菜单
        var complianceBlackList = complianceGroup.AddPermission(SpeakerPortalPermissions.Compliance.BlackList, L($"Permission:{SpeakerPortalPermissions.Compliance.BlackList}"));
        complianceBlackList.AddChild(SpeakerPortalPermissions.Compliance.BlackListCRUD, L($"Permission:{SpeakerPortalPermissions.Compliance.BlackListCRUD}"));
        //PSA上限管理二级菜单
        var compliancePSALimit = complianceGroup.AddPermission(SpeakerPortalPermissions.Compliance.PSALimit, L($"Permission:{SpeakerPortalPermissions.Compliance.PSALimit}"));
        compliancePSALimit.AddChild(SpeakerPortalPermissions.Compliance.PSALimitCRUD, L($"Permission:{SpeakerPortalPermissions.Compliance.PSALimitCRUD}"));
        compliancePSALimit.AddChild(SpeakerPortalPermissions.Compliance.PSALimitRead, L($"Permission:{SpeakerPortalPermissions.Compliance.PSALimitRead}"));
        compliancePSALimit.AddChild(SpeakerPortalPermissions.Compliance.PSALimitCommonCRUD, L($"Permission:{SpeakerPortalPermissions.Compliance.PSALimitCommonCRUD}"));
        compliancePSALimit.AddChild(SpeakerPortalPermissions.Compliance.PSALimitCommonRead, L($"Permission:{SpeakerPortalPermissions.Compliance.PSALimitCommonRead}"));
        //单据拦截二级菜单
        var complianceInterception = complianceGroup.AddPermission(SpeakerPortalPermissions.Compliance.Interception, L($"Permission:{SpeakerPortalPermissions.Compliance.Interception}"));
        complianceInterception.AddChild(SpeakerPortalPermissions.Compliance.InterceptionCRUD, L($"Permission:{SpeakerPortalPermissions.Compliance.InterceptionCRUD}"));
        complianceInterception.AddChild(SpeakerPortalPermissions.Compliance.InterceptionExport, L($"Permission:{SpeakerPortalPermissions.Compliance.InterceptionExport}"));
        complianceInterception.AddChild(SpeakerPortalPermissions.Compliance.InterceptionReturn, L($"Permission:{SpeakerPortalPermissions.Compliance.InterceptionReturn}"));
        complianceInterception.AddChild(SpeakerPortalPermissions.Compliance.InterceptionRelease, L($"Permission:{SpeakerPortalPermissions.Compliance.InterceptionRelease}"));
        complianceInterception.AddChild(SpeakerPortalPermissions.Compliance.InterceptionBatchAction, L($"Permission:{SpeakerPortalPermissions.Compliance.InterceptionBatchAction}"));
        //讲者授权申请二级菜单
        var complianceSpeakerAuth = complianceGroup.AddPermission(SpeakerPortalPermissions.Compliance.SpeakerAuth, L($"Permission:{SpeakerPortalPermissions.Compliance.SpeakerAuth}"));
        complianceSpeakerAuth.AddChild(SpeakerPortalPermissions.Compliance.SpeakerAuthCRUD, L($"Permission:{SpeakerPortalPermissions.Compliance.SpeakerAuthCRUD}"));
        complianceSpeakerAuth.AddChild(SpeakerPortalPermissions.Compliance.SpeakerAuthRead, L($"Permission:{SpeakerPortalPermissions.Compliance.SpeakerAuthRead}"));
        complianceSpeakerAuth.AddChild(SpeakerPortalPermissions.Compliance.SpeakerAuthActivate, L($"Permission:{SpeakerPortalPermissions.Compliance.SpeakerAuthActivate}"));
        #endregion

        #region 系统配置
        //添加一级菜单组
        var systemGroup = context.AddGroup(SpeakerPortalPermissions.SystemManagement, L($"Permission:{SpeakerPortalPermissions.SystemManagement}"));
        var role = systemGroup.AddPermission(SpeakerPortalPermissions.System.Role, L($"Permission:{SpeakerPortalPermissions.System.Role}"));
        role.AddChild(SpeakerPortalPermissions.System.RoleCRUD, L($"Permission:{SpeakerPortalPermissions.System.RoleCRUD}"));
        var user = systemGroup.AddPermission(SpeakerPortalPermissions.System.User, L($"Permission:{SpeakerPortalPermissions.System.User}"));
        user.AddChild(SpeakerPortalPermissions.System.UserCRUD, L($"Permission:{SpeakerPortalPermissions.System.UserCRUD}"));
        var category = systemGroup.AddPermission(SpeakerPortalPermissions.System.Category, L($"Permission:{SpeakerPortalPermissions.System.Category}"));
        category.AddChild(SpeakerPortalPermissions.System.CategoryCRUD, L($"Permission:{SpeakerPortalPermissions.System.CategoryCRUD}"));
        var slideshow = systemGroup.AddPermission(SpeakerPortalPermissions.System.Slideshow, L($"Permission:{SpeakerPortalPermissions.System.Slideshow}"));
        slideshow.AddChild(SpeakerPortalPermissions.System.SlideshowCRUD, L($"Permission:{SpeakerPortalPermissions.System.SlideshowCRUD}"));
        slideshow.AddChild(SpeakerPortalPermissions.System.SlideshowExport, L($"Permission:{SpeakerPortalPermissions.System.SlideshowExport}"));
        var financialReview = systemGroup.AddPermission(SpeakerPortalPermissions.System.FinancialReview, L($"Permission:{SpeakerPortalPermissions.System.FinancialReview}"));
        financialReview.AddChild(SpeakerPortalPermissions.System.FinancialReviewCRUD, L($"Permission:{SpeakerPortalPermissions.System.FinancialReviewCRUD}"));
        financialReview.AddChild(SpeakerPortalPermissions.System.FinancialReviewExport, L($"Permission:{SpeakerPortalPermissions.System.FinancialReviewExport}"));
        #endregion

        #region 报表管理
        //添加一级菜单组
        var reportGroup = context.AddGroup(SpeakerPortalPermissions.ReportManagement, L($"Permission:{SpeakerPortalPermissions.ReportManagement}"));
        var wholeProcess = reportGroup.AddPermission(SpeakerPortalPermissions.Report.WholeProcess, L($"Permission:{SpeakerPortalPermissions.Report.WholeProcess}"));
        wholeProcess.AddChild(SpeakerPortalPermissions.Report.WholeProcessExport, L($"Permission:{SpeakerPortalPermissions.Report.WholeProcessExport}"));
        wholeProcess.AddChild(SpeakerPortalPermissions.Report.WholeProcessPickApplyUser, L($"Permission:{SpeakerPortalPermissions.Report.WholeProcessPickApplyUser}"));

        var prAccrual = reportGroup.AddPermission(SpeakerPortalPermissions.Report.PRAccrual, L($"Permission:{SpeakerPortalPermissions.Report.PRAccrual}"));
        prAccrual.AddChild(SpeakerPortalPermissions.Report.PRAccrualExport, L($"Permission:{SpeakerPortalPermissions.Report.PRAccrualExport}"));

        var epdPurchase = reportGroup.AddPermission(SpeakerPortalPermissions.Report.EPDPurchase, L($"Permission:{SpeakerPortalPermissions.Report.EPDPurchase}"));
        epdPurchase.AddChild(SpeakerPortalPermissions.Report.EPDPurchaseExport, L($"Permission:{SpeakerPortalPermissions.Report.EPDPurchaseExport}"));
        epdPurchase.AddChild(SpeakerPortalPermissions.Report.EPDPurchasePickApplyUser, L($"Permission:{SpeakerPortalPermissions.Report.EPDPurchasePickApplyUser}"));

        var professionalServices = reportGroup.AddPermission(SpeakerPortalPermissions.Report.ProfessionalServices, L($"Permission:{SpeakerPortalPermissions.Report.ProfessionalServices}"));
        professionalServices.AddChild(SpeakerPortalPermissions.Report.ProfessionalServicesExport, L($"Permission:{SpeakerPortalPermissions.Report.ProfessionalServicesExport}"));

        var masterBudget = reportGroup.AddPermission(SpeakerPortalPermissions.Report.MasterBudget, L($"Permission:{SpeakerPortalPermissions.Report.MasterBudget}"));
        masterBudget.AddChild(SpeakerPortalPermissions.Report.MasterBudgetExport, L($"Permission:{SpeakerPortalPermissions.Report.MasterBudgetExport}"));

        var subBudget = reportGroup.AddPermission(SpeakerPortalPermissions.Report.SubBudget, L($"Permission:{SpeakerPortalPermissions.Report.SubBudget}"));
        subBudget.AddChild(SpeakerPortalPermissions.Report.SubBudgetExport, L($"Permission:{SpeakerPortalPermissions.Report.SubBudgetExport}"));

        var applicationApprovalRecord = reportGroup.AddPermission(SpeakerPortalPermissions.Report.ApplicationApprovalRecord, L($"Permission:{SpeakerPortalPermissions.Report.ApplicationApprovalRecord}"));
        applicationApprovalRecord.AddChild(SpeakerPortalPermissions.Report.ApplicationApprovalRecordExport, L($"Permission:{SpeakerPortalPermissions.Report.ApplicationApprovalRecordExport}"));

        var userReport = reportGroup.AddPermission(SpeakerPortalPermissions.Report.User, L($"Permission:{SpeakerPortalPermissions.Report.User}"));
        userReport.AddChild(SpeakerPortalPermissions.Report.UserExport, L($"Permission:{SpeakerPortalPermissions.Report.UserExport}"));

        var organization = reportGroup.AddPermission(SpeakerPortalPermissions.Report.Organization, L($"Permission:{SpeakerPortalPermissions.Report.Organization}"));
        organization.AddChild(SpeakerPortalPermissions.Report.OrganizationExport, L($"Permission:{SpeakerPortalPermissions.Report.OrganizationExport}"));

        var financialAuditPosition = reportGroup.AddPermission(SpeakerPortalPermissions.Report.FinancialAuditPosition, L($"Permission:{SpeakerPortalPermissions.Report.FinancialAuditPosition}"));
        financialAuditPosition.AddChild(SpeakerPortalPermissions.Report.FinancialAuditPositionExport, L($"Permission:{SpeakerPortalPermissions.Report.FinancialAuditPositionExport}"));

        var powerBI = reportGroup.AddPermission(SpeakerPortalPermissions.Report.PowerBI, L($"Permission:{SpeakerPortalPermissions.Report.PowerBI}"));

        var invoiceReport = reportGroup.AddPermission(SpeakerPortalPermissions.Report.PaInvoice, L($"Permission:{SpeakerPortalPermissions.Report.PaInvoice}"));
        invoiceReport.AddChild(SpeakerPortalPermissions.Report.PaInvoiceExport, L($"Permission:{SpeakerPortalPermissions.Report.PaInvoiceExport}"));

        #endregion

        #region 市场活动
        //市场活动菜单组
        var CroosBu = context.AddGroup(SpeakerPortalPermissions.CroosBuManagement, L($"Permission:{SpeakerPortalPermissions.CroosBuManagement}"));
        //市场活动二级菜单
        var marketActvity = CroosBu.AddPermission(SpeakerPortalPermissions.MarketActvity.MarketActvityManagement, L($"Permission:{SpeakerPortalPermissions.MarketActvity.MarketActvityManagement}"));
        marketActvity.AddChild(SpeakerPortalPermissions.MarketActvity.MarketActvityBatchAdd, L($"Permission:{SpeakerPortalPermissions.MarketActvity.MarketActvityBatchAdd}"));
        marketActvity.AddChild(SpeakerPortalPermissions.MarketActvity.MarketActvityRead, L($"Permission:{SpeakerPortalPermissions.MarketActvity.MarketActvityRead}"));
        marketActvity.AddChild(SpeakerPortalPermissions.MarketActvity.MarketActvityCRUD, L($"Permission:{SpeakerPortalPermissions.MarketActvity.MarketActvityCRUD}"));
        marketActvity.AddChild(SpeakerPortalPermissions.MarketActvity.MarketActvityExport, L($"Permission:{SpeakerPortalPermissions.MarketActvity.MarketActvityExport}"));
        //H5
        var marketActvityH5 = CroosBu.AddPermission(SpeakerPortalPermissions.MarketActvity.MarketActvityH5Management, L($"Permission:{SpeakerPortalPermissions.MarketActvity.MarketActvityH5Management}"));
        marketActvityH5.AddChild(SpeakerPortalPermissions.MarketActvity.MarketActvityH5Read, L($"Permission:{SpeakerPortalPermissions.MarketActvity.MarketActvityH5Read}"));
        marketActvityH5.AddChild(SpeakerPortalPermissions.MarketActvity.MarketActvityH5JoinActivities, L($"Permission:{SpeakerPortalPermissions.MarketActvity.MarketActvityH5JoinActivities}"));
        marketActvityH5.AddChild(SpeakerPortalPermissions.MarketActvity.MarketActvityH5Facorites, L($"Permission:{SpeakerPortalPermissions.MarketActvity.MarketActvityH5Facorites}"));
        #endregion

        #region 客户关系
        ////市场活动菜单组
        //var customerRelationGroup = context.AddGroup(SpeakerPortalPermissions.CustomerRelationManagement, L($"Permission:{SpeakerPortalPermissions.CustomerRelationManagement}"));
        //市场活动二级菜单
        var customerRelation = CroosBu.AddPermission(SpeakerPortalPermissions.CustomerRelation.CustomerRelationManagement, L($"Permission:{SpeakerPortalPermissions.CustomerRelation.CustomerRelationManagement}"));
        customerRelation.AddChild(SpeakerPortalPermissions.CustomerRelation.CustomerRelationBatchAdd, L($"Permission:{SpeakerPortalPermissions.CustomerRelation.CustomerRelationBatchAdd}"));
        customerRelation.AddChild(SpeakerPortalPermissions.CustomerRelation.CustomerRelationRead, L($"Permission:{SpeakerPortalPermissions.CustomerRelation.CustomerRelationRead}"));
        customerRelation.AddChild(SpeakerPortalPermissions.CustomerRelation.CustomerRelationCRUD, L($"Permission:{SpeakerPortalPermissions.CustomerRelation.CustomerRelationCRUD}"));
        customerRelation.AddChild(SpeakerPortalPermissions.CustomerRelation.CustomerRelationExport, L($"Permission:{SpeakerPortalPermissions.CustomerRelation.CustomerRelationExport}"));
        //H5
        var customerRelationH5 = CroosBu.AddPermission(SpeakerPortalPermissions.CustomerRelation.CustomerRelationH5Management, L($"Permission:{SpeakerPortalPermissions.CustomerRelation.CustomerRelationH5Management}"));
        customerRelationH5.AddChild(SpeakerPortalPermissions.CustomerRelation.CustomerRelationH5Read, L($"Permission:{SpeakerPortalPermissions.CustomerRelation.CustomerRelationH5Read}"));

        #endregion

        #region 核销单管理
        //核销管理菜单组
        var sticketGroup = context.AddGroup(SpeakerPortalPermissions.STicketManagement, L($"Permission:{SpeakerPortalPermissions.STicketManagement}"));
        //核销单列表二级菜单
        var sticketApplication = sticketGroup.AddPermission(SpeakerPortalPermissions.STicket.Application, L($"Permission:{SpeakerPortalPermissions.STicket.Application}"));
        sticketApplication.AddChild(SpeakerPortalPermissions.STicket.ApplicationRead, L($"Permission:{SpeakerPortalPermissions.STicket.ApplicationRead}"));
        sticketApplication.AddChild(SpeakerPortalPermissions.STicket.ApplicationCRUD, L($"Permission:{SpeakerPortalPermissions.STicket.ApplicationCRUD}"));
        sticketApplication.AddChild(SpeakerPortalPermissions.STicket.ApplicationExport, L($"Permission:{SpeakerPortalPermissions.STicket.ApplicationExport}"));
        sticketApplication.AddChild(SpeakerPortalPermissions.STicket.ApplicationPrint, L($"Permission:{SpeakerPortalPermissions.STicket.ApplicationPrint}"));
        #endregion

        #region FOC管理
        //FOC管理菜单组
        var focGroup = context.AddGroup(SpeakerPortalPermissions.FocManagement, L($"Permission:{SpeakerPortalPermissions.FocManagement}"));
        //FOC申请列表二级菜单
        var focApplication = focGroup.AddPermission(SpeakerPortalPermissions.Foc.Application, L($"Permission:{SpeakerPortalPermissions.Foc.Application}"));
        focApplication.AddChild(SpeakerPortalPermissions.Foc.ApplicationRead, L($"Permission:{SpeakerPortalPermissions.Foc.ApplicationRead}"));
        focApplication.AddChild(SpeakerPortalPermissions.Foc.ApplicationCRUD, L($"Permission:{SpeakerPortalPermissions.Foc.ApplicationCRUD}"));
        focApplication.AddChild(SpeakerPortalPermissions.Foc.SelectProduct, L($"Permission:{SpeakerPortalPermissions.Foc.SelectProduct}"));
        focApplication.AddChild(SpeakerPortalPermissions.Foc.LogisticsRecord, L($"Permission:{SpeakerPortalPermissions.Foc.LogisticsRecord}"));
        focApplication.AddChild(SpeakerPortalPermissions.Foc.ApplicationPrint, L($"Permission:{SpeakerPortalPermissions.Foc.ApplicationPrint}"));
        focApplication.AddChild(SpeakerPortalPermissions.Foc.BatchUpload, L($"Permission:{SpeakerPortalPermissions.Foc.BatchUpload}"));
        focApplication.AddChild(SpeakerPortalPermissions.Foc.BatchExport, L($"Permission:{SpeakerPortalPermissions.Foc.BatchExport}"));
        #endregion

        #region 退换货管理
        var renturnGroup = context.AddGroup(SpeakerPortalPermissions.ReturnManagement, L($"Permission:{SpeakerPortalPermissions.ReturnManagement}"));
        //FOC申请列表二级菜单
        var returnApplication = renturnGroup.AddPermission(SpeakerPortalPermissions.Return.Application, L($"Permission:{SpeakerPortalPermissions.Return.Application}"));
        returnApplication.AddChild(SpeakerPortalPermissions.Return.ApplicationRead, L($"Permission:{SpeakerPortalPermissions.Return.ApplicationRead}"));
        returnApplication.AddChild(SpeakerPortalPermissions.Return.ApplicationCRUD, L($"Permission:{SpeakerPortalPermissions.Return.ApplicationCRUD}"));
        returnApplication.AddChild(SpeakerPortalPermissions.Return.ApplicationPrint, L($"Permission:{SpeakerPortalPermissions.Return.ApplicationPrint}"));

        var exchangeGroup = context.AddGroup(SpeakerPortalPermissions.ExchangeManagement, L($"Permission:{SpeakerPortalPermissions.ExchangeManagement}"));
        //FOC申请列表二级菜单
        var exchangeApplication = exchangeGroup.AddPermission(SpeakerPortalPermissions.Exchange.Application, L($"Permission:{SpeakerPortalPermissions.Exchange.Application}"));
        exchangeApplication.AddChild(SpeakerPortalPermissions.Exchange.ApplicationRead, L($"Permission:{SpeakerPortalPermissions.Exchange.ApplicationRead}"));
        exchangeApplication.AddChild(SpeakerPortalPermissions.Exchange.ApplicationCRUD, L($"Permission:{SpeakerPortalPermissions.Exchange.ApplicationCRUD}"));
        exchangeApplication.AddChild(SpeakerPortalPermissions.Exchange.ApplicationPrint, L($"Permission:{SpeakerPortalPermissions.Exchange.ApplicationPrint}"));
        #endregion

        #region 采购协议MSA SOW
        var purchaseAgreementGroup = context.AddGroup(SpeakerPortalPermissions.PurchaseAgreementManagement, L($"Permission:{SpeakerPortalPermissions.PurchaseAgreementManagement}"));
        //二级菜单 MSA主服务协议
        var msa = purchaseAgreementGroup.AddPermission(SpeakerPortalPermissions.PurchaseAgreement.Msa, L($"Permission:{SpeakerPortalPermissions.PurchaseAgreement.Msa}"));
        msa.AddChild(SpeakerPortalPermissions.PurchaseAgreement.MsaRead, L($"Permission:{SpeakerPortalPermissions.PurchaseAgreement.MsaRead}"));
        msa.AddChild(SpeakerPortalPermissions.PurchaseAgreement.MsaCRUD, L($"Permission:{SpeakerPortalPermissions.PurchaseAgreement.MsaCRUD}"));
        msa.AddChild(SpeakerPortalPermissions.PurchaseAgreement.MsaExport, L($"Permission:{SpeakerPortalPermissions.PurchaseAgreement.MsaExport}"));
        //二级菜单 SOW工作说明书
        var sow = purchaseAgreementGroup.AddPermission(SpeakerPortalPermissions.PurchaseAgreement.Sow, L($"Permission:{SpeakerPortalPermissions.PurchaseAgreement.Sow}"));
        sow.AddChild(SpeakerPortalPermissions.PurchaseAgreement.SowRead, L($"Permission:{SpeakerPortalPermissions.PurchaseAgreement.SowRead}"));
        sow.AddChild(SpeakerPortalPermissions.PurchaseAgreement.SowCRUD, L($"Permission:{SpeakerPortalPermissions.PurchaseAgreement.SowCRUD}"));
        sow.AddChild(SpeakerPortalPermissions.PurchaseAgreement.SowExport, L($"Permission:{SpeakerPortalPermissions.PurchaseAgreement.SowExport}"));
        #endregion

        #region 用餐报告

        var concurGroup = context.AddGroup(SpeakerPortalPermissions.ConcurManagement, L($"Permission:{SpeakerPortalPermissions.ConcurManagement}"));
        //用餐报告列表二级菜单
        var mealReport = concurGroup.AddPermission(SpeakerPortalPermissions.Concur.MealReport, L($"Permission:{SpeakerPortalPermissions.Concur.MealReport}"));
        mealReport.AddChild(SpeakerPortalPermissions.Concur.MealReportRead, L($"Permission:{SpeakerPortalPermissions.Concur.MealReportRead}"));
        mealReport.AddChild(SpeakerPortalPermissions.Concur.MealReportImport, L($"Permission:{SpeakerPortalPermissions.Concur.MealReportImport}"));
        mealReport.AddChild(SpeakerPortalPermissions.Concur.MealReportExport, L($"Permission:{SpeakerPortalPermissions.Concur.MealReportExport}"));

        //员工分组配置列表二级菜单
        var employee = concurGroup.AddPermission(SpeakerPortalPermissions.Concur.Employee, L($"Permission:{SpeakerPortalPermissions.Concur.Employee}"));
        employee.AddChild(SpeakerPortalPermissions.Concur.EmployeeRead, L($"Permission:{SpeakerPortalPermissions.Concur.EmployeeRead}"));
        employee.AddChild(SpeakerPortalPermissions.Concur.EmployeeImport, L($"Permission:{SpeakerPortalPermissions.Concur.EmployeeImport}"));
        employee.AddChild(SpeakerPortalPermissions.Concur.EmployeeExport, L($"Permission:{SpeakerPortalPermissions.Concur.EmployeeExport}"));

        //机构转换配置列表二级菜单
        var org = concurGroup.AddPermission(SpeakerPortalPermissions.Concur.Organization, L($"Permission:{SpeakerPortalPermissions.Concur.Organization}"));
        org.AddChild(SpeakerPortalPermissions.Concur.OrganizationRead, L($"Permission:{SpeakerPortalPermissions.Concur.OrganizationRead}"));
        org.AddChild(SpeakerPortalPermissions.Concur.OrganizationCRUD, L($"Permission:{SpeakerPortalPermissions.Concur.OrganizationCRUD}"));
        org.AddChild(SpeakerPortalPermissions.Concur.OrganizationExport, L($"Permission:{SpeakerPortalPermissions.Concur.OrganizationExport}"));

        #endregion
    }

    private static LocalizableString L(string name)
    {
        return LocalizableString.Create<SpeakerPortalResource>(name);
    }
}
