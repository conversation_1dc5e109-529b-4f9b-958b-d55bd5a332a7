SELECT	
 TRY_CONVERT(UNIQUEIDENTIFIER, [Id]) [Id]
,TRY_CONVERT(UNIQUEIDENTIFIER, iif([VendorId] is NULL,'********-0000-0000-0000-************',[VendorId])) [VendorId]
,Company AS [Company]
,Currency AS [Currency]
,VendorCode AS [VendorCode]
,AbbottBank AS [AbbottBank]
,VendorType AS [VendorType]
,Division AS [Division]
,PayType AS [PayType]
,CountryCode AS [CountryCode]
,BankType AS [BankType]
,[DpoCategory]
,[PaymentTerm]
,[BankNo]
,'{}' AS [ExtraProperties]
,'' AS [ConcurrencyStamp]
,case when CreationTime is null  or CreationTime ='' then '1900-01-01 00:00:00'
else 
--TRY_CONVERT(DATETIME, 
--        SUBSTRING(CreationTime, 1, 4) + '-' +  -- YYYY
--        SUBSTRING(CreationTime, 5, 2) + '-' +  -- MM
--        SUBSTRING(CreationTime, 7, 2) + ' ' +  -- DD
--        RIGHT('0' + CAST((73411 / 3600) % 24 AS VARCHAR(2)), 2) + ':' +  -- HH
--        RIGHT('0' + CAST((73411 % 3600) / 60 AS VARCHAR(2)), 2) + ':' +  -- MM
--        RIGHT('0' + CAST(73411 % 60 AS VARCHAR(2)), 2)                   -- SS
--    ) 
CreationTime
    end AS [CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CreatorId]) [CreatorId]
,case when LastModificationTime is null  or LastModificationTime ='' then null
when LEN(LastModificationTime) <8 then null
else 
--TRY_CONVERT(DATETIME, 
--        SUBSTRING(LastModificationTime, 1, 4) + '-' +  -- YYYY
--        SUBSTRING(LastModificationTime, 5, 2) + '-' +  -- MM
--        SUBSTRING(LastModificationTime, 7, 2) + ' ' +  -- DD
--        RIGHT('0' + CAST((73411 / 3600) % 24 AS VARCHAR(2)), 2) + ':' +  -- HH
--        RIGHT('0' + CAST((73411 % 3600) / 60 AS VARCHAR(2)), 2) + ':' +  -- MM
--        RIGHT('0' + CAST(73411 % 60 AS VARCHAR(2)), 2)                   -- SS
--    )  
LastModificationTime    end AS [LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [LastModifierId]) [LastModifierId]
,0 AS [IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, [DeleterId]) [DeleterId]
,case when DeletionTime is null  or DeletionTime ='' then null
when LEN(DeletionTime) <8 then null
else 
--TRY_CONVERT(DATETIME, 
--        SUBSTRING(DeletionTime, 1, 4) + '-' +  -- YYYY
--        SUBSTRING(DeletionTime, 5, 2) + '-' +  -- MM
--        SUBSTRING(DeletionTime, 7, 2) + ' ' +  -- DD
--        RIGHT('0' + CAST((73411 / 3600) % 24 AS VARCHAR(2)), 2) + ':' +  -- HH
--        RIGHT('0' + CAST((73411 % 3600) / 60 AS VARCHAR(2)), 2) + ':' +  -- MM
--        RIGHT('0' + CAST(73411 % 60 AS VARCHAR(2)), 2)                   -- SS
--    )  
    DeletionTime end AS [DeletionTime]
,[SpendingCategory]
,case when BpcsCreationTime is null  or BpcsCreationTime ='' then null
when LEN(BpcsCreationTime) <8 then null
else 
--TRY_CONVERT(DATETIME, 
--        SUBSTRING(BpcsCreationTime, 1, 4) + '-' +  -- YYYY
--        SUBSTRING(BpcsCreationTime, 5, 2) + '-' +  -- MM
--        SUBSTRING(BpcsCreationTime, 7, 2) + ' ' +  -- DD
--        RIGHT('0' + CAST((73411 / 3600) % 24 AS VARCHAR(2)), 2) + ':' +  -- HH
--        RIGHT('0' + CAST((73411 % 3600) / 60 AS VARCHAR(2)), 2) + ':' +  -- MM
--        RIGHT('0' + CAST(73411 % 60 AS VARCHAR(2)), 2)                   -- SS
--    )  
 BpcsCreationTime   end AS [BpcsCreationTime]
,[BpcsVmid]
,[BpcsVnstat]
,[FinancialVendorStatus]
INTO #VendorFinancials
FROM (SELECT DISTINCT *,ROW_NUMBER() OVER(PARTITION BY Id ORDER BY Id) RK FROM PLATFORM_ABBOTT.dbo.[VendorFinancials])a
WHERE RK = 1;


--select * from #VendorFinancials
--
--ALTER TABLE Speaker_Portal.dbo.VendorFinancials ALTER COLUMN Company nvarchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL;
--
--
--drop table #VendorFinancials;

USE Speaker_Portal;

UPDATE a
SET
 a.[Id] = b.[Id]
--,a.[VendorId] = b.[VendorId]
--,a.[Company] = b.[Company]
--,a.[Currency] = b.[Currency]
--,a.[VendorCode] = b.[VendorCode]
--,a.[AbbottBank] = b.[AbbottBank]
--,a.[VendorType] = b.[VendorType]
--,a.[Division] = b.[Division]
--,a.[PayType] = b.[PayType]
--,a.[CountryCode] = b.[CountryCode]
--,a.[BankType] = b.[BankType]
--,a.[DpoCategory] = b.[DpoCategory]
--,a.[PaymentTerm] = b.[PaymentTerm]
--,a.[BankNo] = b.[BankNo]
--,a.[ExtraProperties] = b.[ExtraProperties]
--,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
--,a.[CreationTime] = b.[CreationTime]
--,a.[CreatorId] = b.[CreatorId]
--,a.[LastModificationTime] = b.[LastModificationTime]
--,a.[LastModifierId] = b.[LastModifierId]
--,a.[IsDeleted] = b.[IsDeleted]
--,a.[DeleterId] = b.[DeleterId]
--,a.[DeletionTime] = b.[DeletionTime]
--,a.[SpendingCategory] = b.[SpendingCategory]
--,a.[BpcsCreationTime] = b.[BpcsCreationTime]
--,a.[BpcsVmid] = b.[BpcsVmid]
--,a.[BpcsVnstat] = b.[BpcsVnstat]
,a.[FinancialVendorStatus] = b.[FinancialVendorStatus]
FROM dbo.VendorFinancials a
left join #VendorFinancials  b
ON a.id=b.id;


INSERT INTO dbo.VendorFinancials
SELECT
 [Id]
,[VendorId]
,[Company]
,[Currency]
,[VendorCode]
,[AbbottBank]
,[VendorType]
,[Division]
,[PayType]
,[CountryCode]
,[BankType]
,[DpoCategory]
,[PaymentTerm]
,[BankNo]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[SpendingCategory]
,[BpcsCreationTime]
,[BpcsVmid]
,[BpcsVnstat]
,[FinancialVendorStatus]
FROM #VendorFinancials a
WHERE not exists (select * from dbo.VendorFinancials where id=a.id);

--truncate table dbo.VendorFinancials


