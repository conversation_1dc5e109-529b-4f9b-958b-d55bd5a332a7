SELECT
 TRY_CONVERT(UNIQUEIDENTIFIER, [Id]) [Id]
--,TRY_CONVERT(UNIQUEIDENTIFIER, [ApplicationCode]) 
,[ApplicationCode]
,case 
	when Status =N'发起人终止' then '4'
	when Status =N'发起人终止' then '6'
	when Status =N'供应商确认' then '3'
	when Status =N'审批完毕，等待关闭' then '3'
	when Status =N'审批中' then '5'
	when Status =N'完成' then '10'
	when Status =N'财务关闭' then '9'
	when Status =N'重发起' then '5'
	else '0'
end
[Status]
,TRY_CONVERT(UNIQUEIDENTIFIER, [ApplyUserId]) [ApplyUserId]
,CASE WHEN [ApplyTime] IS NOT NULL
	THEN GETDATE()
	ELSE [ApplyTime]
	END [ApplyTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [ApplyUserBu]) [ApplyUserBu]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CostCenter]) [CostCenter]
,TRY_CONVERT(UNIQUEIDENTIFIER, [BudgetId]) [BudgetId]
,[MeetingTitle]
,[MeetingNo]
,[IsEsignUsed]
,[PushSystem]
,TRY_CONVERT(UNIQUEIDENTIFIER, [AgentId]) [AgentId]
,TRY_CONVERT(UNIQUEIDENTIFIER, [HostVendorId]) [HostVendorId]
,TRY_CONVERT(UNIQUEIDENTIFIER, [ExpenseType]) [ExpenseType]
,TRY_CONVERT(UNIQUEIDENTIFIER, [BudgetRegion]) [BudgetRegion]
,[ProductIds]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CompanyId]) [CompanyId]
,[TotalAmount]
,[Remark]
,[PrLateDescription]
,[ActiveLaunchCityId]
,[ActiveHostCityId]
,[AcitveHostAddress]
,CASE WHEN [AcitveDate] IS NOT NULL
	THEN GETDATE()
	ELSE [AcitveDate]
	END [AcitveDate]
,[ActiveType]
,[ProjectType]
,'0' DoctorsNum
,[MeetingType]
--,[IsSeriesMeeting]
,TRY_CONVERT(UNIQUEIDENTIFIER, [MainMeetingPR]) [MainMeetingPR]
,[OrganizerNature]
,[SponsorshipType]
,[ActiveLeader]
,[MeetingName]
,[OrganizerName]
,CASE WHEN [MeetingDate] IS NOT NULL
	THEN GETDATE()
	ELSE [MeetingDate]
	END [MeetingDate]
,[MeetingLocation]
,'0'[NumberOfProfessionals]
,[ChoiceReason]
,[SupportReason]
,[RequireMediaReason]
,[AttendeeExpertise]
,[MarketResearchLeader]
,[MarketResearchCompany]
,[MarketResearchReason]
,[MarketResearchResult]
,[MarketResearchResultUsedFor]
,[PatientInfo]
,[SPChoiceAndPayStandard]
,[SupportFiles]
,'{}' as[ExtraProperties]
,[ConcurrencyStamp]
,CASE WHEN [CreationTime] IS NOT NULL
	THEN GETDATE()
	ELSE [CreationTime]
	END [CreationTime]
--,TRY_CONVERT(UNIQUEIDENTIFIER, [CreatorId])
,NEWID() as[CreatorId]
,CASE WHEN [LastModificationTime] IS NOT NULL
	THEN GETDATE()
	ELSE [LastModificationTime]
	END [LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [LastModifierId]) [LastModifierId]
,[IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, [DeleterId]) [DeleterId]
,CASE WHEN [DeletionTime] IS NOT NULL
	THEN GETDATE()
	ELSE [DeletionTime]
	END [DeletionTime]
,[AdditionalFiles]
,[HospitalDepartments]
,[Hospitals]
,[IsIncludeHcpTravelLodgingFee]
,[ApplyUserBuName]
,[ActiveNo]
,isnull(TRY_CONVERT(UNIQUEIDENTIFIER, [ApplyUserDept]),'00000000-0000-0000-0000-000000000000') [ApplyUserDept]
,[ApplyUserDeptName]
,[AgentIdName]
,[ApplyUserIdName]
,CASE WHEN [ApprovedDate] IS NOT NULL
	THEN GETDATE()
	ELSE [ApprovedDate]
	END [ApprovedDate]
,[BudgetCode]
,[BudgetRegionName]
,CASE WHEN [ClosedDate] IS NOT NULL
	THEN GETDATE()
	ELSE [ClosedDate]
	END [ClosedDate]
,[CompanyCode]
,[CompanyIdName]
,[CompanyShortName]
,[CostCenterCode]
,[CostCenterName]
,[ExpenseTypeCode]
,[ExpenseTypeName]
,[HostVendorIdName]
,[ProductIdsName]
,[SavingAmount]
,[SubBudgetCode]
,TRY_CONVERT(UNIQUEIDENTIFIER, [SubBudgetId]) [SubBudgetId]
,[AgentEmail]
,[HostVendorEmail]
,[MeetingStatus]
,[IsShowExpenseStep]
,[OecExceptionNumber]
,NULL AS SerialMeetingType
,NULL AS [Currency]
,'0'as [ExpectedFloatRate]
,null as [IsSupportHcpTeachFee]
,null as [PlanProjectDetailAndResult]
,'0'[PlanRate]
,null as [ProjectBenefitAndObjective]
,null as [ProjectDetailAndResultUsedFor]
,'0'[Rate]
,'0' [TotalAmountRMB]
,null as [CurrencySymbol]
,null as [ApprovalEmails]
,'00000000-0000-0000-0000-000000000000' as [TransfereeId]
,null as [TransfereeName]
,'0'[IsOnceApproved]
INTO #PurPRApplications
FROM PLATFORM_ABBOTT_STG.dbo.PurPRApplications

--drop table #PurPRApplications

--ALTER TABLE Speaker_Portal_STG.dbo.PurPRApplications ALTER COLUMN MeetingTitle nvarchar(300) COLLATE SQL_Latin1_General_CP1_CI_AS NULL;
--ALTER TABLE Speaker_Portal_STG.dbo.PurPRApplications ALTER COLUMN AcitveHostAddress nvarchar(300) COLLATE SQL_Latin1_General_CP1_CI_AS NULL;
--ALTER TABLE Speaker_Portal_STG.dbo.PurPRApplications ALTER COLUMN SupportFiles nvarchar(3000) COLLATE SQL_Latin1_General_CP1_CI_AS NULL;
--ALTER TABLE Speaker_Portal_STG.dbo.PurPRApplications ALTER COLUMN ActiveLeader nvarchar(100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL;
--ALTER TABLE Speaker_Portal_STG.dbo.PurPRApplications ALTER COLUMN AdditionalFiles nvarchar(3000) COLLATE SQL_Latin1_General_CP1_CI_AS NULL;
--ALTER TABLE Speaker_Portal_STG.dbo.PurPRApplications ALTER COLUMN CostCenterName nvarchar(100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL;
--ALTER TABLE Speaker_Portal_STG.dbo.PurPRApplications ALTER COLUMN ExpenseTypeName nvarchar(100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL;
--
--
--select max(len(MeetingTitle)) from #PurPRApplications;--100 165
--select max(len(AcitveHostAddress)) from #PurPRApplications;--100 --213
--select max(len(SupportFiles)) from #PurPRApplications;--1000 1800
--select max(len(ActiveLeader)) from #PurPRApplications;--50 67
--select max(len(AdditionalFiles)) from #PurPRApplications;--1000 1800
--select max(len(CostCenterName)) from #PurPRApplications;--50 65
--select max(len(ExpenseTypeName)) from #PurPRApplications;--50 89


USE Speaker_Portal_STG;

UPDATE a
SET 
a.[Id] = b.[Id]
,a.[ApplicationCode] = b.[ApplicationCode]
,a.[Status] = b.[Status]
,a.[ApplyUserId] = b.[ApplyUserId]
,a.[ApplyTime] = b.[ApplyTime]
,a.[ApplyUserBu] = b.[ApplyUserBu]
,a.[CostCenter] = b.[CostCenter]
,a.[BudgetId] = b.[BudgetId]
,a.[MeetingTitle] = b.[MeetingTitle]
,a.[MeetingNo] = b.[MeetingNo]
,a.[IsEsignUsed] = b.[IsEsignUsed]
,a.[PushSystem] = b.[PushSystem]
,a.[AgentId] = b.[AgentId]
,a.[HostVendorId] = b.[HostVendorId]
,a.[ExpenseType] = b.[ExpenseType]
,a.[BudgetRegion] = b.[BudgetRegion]
,a.[ProductIds] = b.[ProductIds]
,a.[CompanyId] = b.[CompanyId]
,a.[TotalAmount] = b.[TotalAmount]
,a.[Remark] = b.[Remark]
,a.[PrLateDescription] = b.[PrLateDescription]
,a.[ActiveLaunchCityId] = b.[ActiveLaunchCityId]
,a.[ActiveHostCityId] = b.[ActiveHostCityId]
,a.[AcitveHostAddress] = b.[AcitveHostAddress]
,a.[AcitveDate] = b.[AcitveDate]
,a.[ActiveType] = b.[ActiveType]
,a.[ProjectType] = b.[ProjectType]
,a.[DoctorsNum] = b.[DoctorsNum]
,a.[MeetingType] = b.[MeetingType]
--,a.[IsSeriesMeeting] = #FIELD!
,a.[MainMeetingPR] = b.[MainMeetingPR]
,a.[OrganizerNature] = b.[OrganizerNature]
,a.[SponsorshipType] = b.[SponsorshipType]
,a.[ActiveLeader] = b.[ActiveLeader]
,a.[MeetingName] = b.[MeetingName]
,a.[OrganizerName] = b.[OrganizerName]
,a.[MeetingDate] = b.[MeetingDate]
,a.[MeetingLocation] = b.[MeetingLocation]
,a.[NumberOfProfessionals] = b.[NumberOfProfessionals]
,a.[ChoiceReason] = b.[ChoiceReason]
,a.[SupportReason] = b.[SupportReason]
,a.[RequireMediaReason] = b.[RequireMediaReason]
,a.[AttendeeExpertise] = b.[AttendeeExpertise]
,a.[MarketResearchLeader] = b.[MarketResearchLeader]
,a.[MarketResearchCompany] = b.[MarketResearchCompany]
,a.[MarketResearchReason] = b.[MarketResearchReason]
,a.[MarketResearchResult] = b.[MarketResearchResult]
,a.[MarketResearchResultUsedFor] = b.[MarketResearchResultUsedFor]
,a.[PatientInfo] = b.[PatientInfo]
,a.[SPChoiceAndPayStandard] = b.[SPChoiceAndPayStandard]
,a.[SupportFiles] = b.[SupportFiles]
,a.[ExtraProperties] = b.[ExtraProperties]
,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = b.[CreatorId]
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = b.[LastModifierId]
,a.[IsDeleted] = b.[IsDeleted]
,a.[DeleterId] = b.[DeleterId]
,a.[DeletionTime] = b.[DeletionTime]
,a.[AdditionalFiles] = b.[AdditionalFiles]
,a.[HospitalDepartments] = b.[HospitalDepartments]
,a.[Hospitals] = b.[Hospitals]
,a.[IsIncludeHcpTravelLodgingFee] = b.[IsIncludeHcpTravelLodgingFee]
,a.[ApplyUserBuName] = b.[ApplyUserBuName]
,a.[ActiveNo] = b.[ActiveNo]
,a.[ApplyUserDept] = b.[ApplyUserDept]
,a.[ApplyUserDeptName] = b.[ApplyUserDeptName]
,a.[AgentIdName] = b.[AgentIdName]
,a.[ApplyUserIdName] = b.[ApplyUserIdName]
,a.[ApprovedDate] = b.[ApprovedDate]
,a.[BudgetCode] = b.[BudgetCode]
,a.[BudgetRegionName] = b.[BudgetRegionName]
,a.[ClosedDate] = b.[ClosedDate]
,a.[CompanyCode] = b.[CompanyCode]
,a.[CompanyIdName] = b.[CompanyIdName]
,a.[CompanyShortName] = b.[CompanyShortName]
,a.[CostCenterCode] = b.[CostCenterCode]
,a.[CostCenterName] = b.[CostCenterName]
,a.[ExpenseTypeCode] = b.[ExpenseTypeCode]
,a.[ExpenseTypeName] = b.[ExpenseTypeName]
,a.[HostVendorIdName] = b.[HostVendorIdName]
,a.[ProductIdsName] = b.[ProductIdsName]
,a.[SavingAmount] = b.[SavingAmount]
,a.[SubBudgetCode] = b.[SubBudgetCode]
,a.[SubBudgetId] = b.[SubBudgetId]
,a.[AgentEmail] = b.[AgentEmail]
,a.[HostVendorEmail] = b.[HostVendorEmail]
,a.[MeetingStatus] = b.[MeetingStatus]
,a.[IsShowExpenseStep] = b.[IsShowExpenseStep]
,a.[OecExceptionNumber] = b.[OecExceptionNumber]
,a.[SerialMeetingType] = b.[SerialMeetingType]
,a.[Currency] = b.[Currency]
,a.[ExpectedFloatRate] = b.[ExpectedFloatRate]
,a.[IsSupportHcpTeachFee] = b.[IsSupportHcpTeachFee]
,a.[PlanProjectDetailAndResult] = b.[PlanProjectDetailAndResult]
,a.[PlanRate] = b.[PlanRate]
,a.[ProjectBenefitAndObjective] = b.[ProjectBenefitAndObjective]
,a.[ProjectDetailAndResultUsedFor] = b.[ProjectDetailAndResultUsedFor]
,a.[Rate] = b.[Rate]
,a.[TotalAmountRMB] = b.[TotalAmountRMB]
,a.[CurrencySymbol] = b.[CurrencySymbol]
,a.[ApprovalEmails] = b.[ApprovalEmails]
,a.[TransfereeId] = b.[TransfereeId]
,a.[TransfereeName] = b.[TransfereeName]
,a.[IsOnceApproved] = b.[IsOnceApproved]
FROM dbo.PurPRApplications a
left join #PurPRApplications  b
ON a.id=b.id
WHERE b.ApplicationCode IS NOT NULL;

INSERT INTO dbo.PurPRApplications
([Id]
,[ApplicationCode]
,[Status]
,[ApplyUserId]
,[ApplyTime]
,[ApplyUserBu]
,[CostCenter]
,[BudgetId]
,[MeetingTitle]
,[MeetingNo]
,[IsEsignUsed]
,[PushSystem]
,[AgentId]
,[HostVendorId]
,[ExpenseType]
,[BudgetRegion]
,[ProductIds]
,[CompanyId]
,[TotalAmount]
,[Remark]
,[PrLateDescription]
,[ActiveLaunchCityId]
,[ActiveHostCityId]
,[AcitveHostAddress]
,[AcitveDate]
,[ActiveType]
,[ProjectType]
,[DoctorsNum] --类型不匹配问题
,[MeetingType]
--,[IsSeriesMeeting]
,[MainMeetingPR]
,[OrganizerNature]
,[SponsorshipType]
,[ActiveLeader]
,[MeetingName]
,[OrganizerName]
,[MeetingDate]
,[MeetingLocation]
,[NumberOfProfessionals] --类型不匹配
,[ChoiceReason]
,[SupportReason]
,[RequireMediaReason]
,[AttendeeExpertise]
,[MarketResearchLeader]
,[MarketResearchCompany]
,[MarketResearchReason]
,[MarketResearchResult]
,[MarketResearchResultUsedFor]
,[PatientInfo]
,[SPChoiceAndPayStandard]
,[SupportFiles]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[AdditionalFiles]
,[HospitalDepartments]
,[Hospitals]
,[IsIncludeHcpTravelLodgingFee]
,[ApplyUserBuName]
,[ActiveNo]
,[ApplyUserDept]
,[ApplyUserDeptName]
,[AgentIdName]
,[ApplyUserIdName]
,[ApprovedDate]
,[BudgetCode]
,[BudgetRegionName]
,[ClosedDate]
,[CompanyCode]
,[CompanyIdName]
,[CompanyShortName]
,[CostCenterCode]
,[CostCenterName]
,[ExpenseTypeCode]
,[ExpenseTypeName]
,[HostVendorIdName]
,[ProductIdsName]
,[SavingAmount]
,[SubBudgetCode]
,[SubBudgetId]
,[AgentEmail]
,[HostVendorEmail]
,[MeetingStatus]
,[IsShowExpenseStep]
,[OecExceptionNumber]
,[SerialMeetingType]
,[Currency]
,[ExpectedFloatRate]
,[IsSupportHcpTeachFee]
,[PlanProjectDetailAndResult]
,[PlanRate]
,[ProjectBenefitAndObjective]
,[ProjectDetailAndResultUsedFor]
,[Rate]
,[TotalAmountRMB]
,[CurrencySymbol]
,[ApprovalEmails]
,[TransfereeId]
,[TransfereeName]
,[IsOnceApproved])
SELECT
 [Id]
,[ApplicationCode]
,[Status]
,[ApplyUserId]
,[ApplyTime]
,[ApplyUserBu]
,[CostCenter]
,[BudgetId]
,[MeetingTitle]
,[MeetingNo]
,[IsEsignUsed]
,[PushSystem]
,[AgentId]
,[HostVendorId]
,[ExpenseType]
,[BudgetRegion]
,[ProductIds]
,[CompanyId]
,[TotalAmount]
,[Remark]
,[PrLateDescription]
,[ActiveLaunchCityId]
,[ActiveHostCityId]
,[AcitveHostAddress]
,[AcitveDate]
,[ActiveType]
,[ProjectType]
,[DoctorsNum] --类型不匹配问题
,[MeetingType]
--,[IsSeriesMeeting]
,[MainMeetingPR]
,[OrganizerNature]
,[SponsorshipType]
,[ActiveLeader]
,[MeetingName]
,[OrganizerName]
,[MeetingDate]
,[MeetingLocation]
,[NumberOfProfessionals] --类型不匹配
,[ChoiceReason]
,[SupportReason]
,[RequireMediaReason]
,[AttendeeExpertise]
,[MarketResearchLeader]
,[MarketResearchCompany]
,[MarketResearchReason]
,[MarketResearchResult]
,[MarketResearchResultUsedFor]
,[PatientInfo]
,[SPChoiceAndPayStandard]
,[SupportFiles]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[AdditionalFiles]
,[HospitalDepartments]
,[Hospitals]
,[IsIncludeHcpTravelLodgingFee]
,[ApplyUserBuName]
,[ActiveNo]
,[ApplyUserDept]
,[ApplyUserDeptName]
,[AgentIdName]
,[ApplyUserIdName]
,[ApprovedDate]
,[BudgetCode]
,[BudgetRegionName]
,[ClosedDate]
,[CompanyCode]
,[CompanyIdName]
,[CompanyShortName]
,[CostCenterCode]
,[CostCenterName]
,[ExpenseTypeCode]
,[ExpenseTypeName]
,[HostVendorIdName]
,[ProductIdsName]
,[SavingAmount]
,[SubBudgetCode]
,[SubBudgetId]
,[AgentEmail]
,[HostVendorEmail]
,[MeetingStatus]
,[IsShowExpenseStep]
,[OecExceptionNumber]
,[SerialMeetingType]
,[Currency]
,'0'as [ExpectedFloatRate]
,[IsSupportHcpTeachFee]
,[PlanProjectDetailAndResult]
,'0'[PlanRate]
,[ProjectBenefitAndObjective]
,[ProjectDetailAndResultUsedFor]
,'0'[Rate]
,'0' [TotalAmountRMB]
,[CurrencySymbol]
,[ApprovalEmails]
,[TransfereeId]
,[TransfereeName]
,'0'[IsOnceApproved]
FROM #PurPRApplications a
WHERE not exists (select * from dbo.PurPRApplications where id=a.id)
--AND ApplicationCode IS NOT NULL


--delete from dbo.PurPRApplications

