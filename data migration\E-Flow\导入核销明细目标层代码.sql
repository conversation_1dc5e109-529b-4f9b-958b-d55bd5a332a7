

select 
TRY_CONVERT(UNIQUE<PERSON>ENTIFIER, Id) Id
, RowId
,TRY_CONVERT(UNIQUEIDENTIFIER, HedgeDetailId) HedgeDetailId
,TRY_CONVERT(UNIQUEIDENTIFIER, ParentID) ParentID
,case when SubVerificationStatus=N'部分核销' then 2
when SubVerificationStatus=N'全部核销' then 1 end SubVerificationStatus
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL('00000000-0000-0000-0000-000000000000',StoreId))StoreId
,StoreName
,StoreChannel
,StoreSubChannel
,TRY_CONVERT(UNIQUEIDENTIFIER, ExpenseNatureId) ExpenseNatureId
,ExpenseNatureCode
,ExpenseNature
,TRY_CAST(Quantity AS NUMERIC(18, 4)) Quantity
,TRY_CAST(Price AS NUMERIC(18, 4)) Price
,TRY_CAST(SubTotalAmountRMB AS NUMERIC(18, 4)) SubTotalAmountRMB
,TRY_CAST(SubVerifiedAmountRMB AS NUMERIC(18, 4)) SubVerifiedAmountRMB
,SettlementRegion
,SettlementPeriodStart
,SettlementPeriodEnd
,TRY_CONVERT(UNIQUEIDENTIFIER, CityId) CityId
,CityCode
,CityName
,PredictDate
,Remark
,CreationTime
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL(cast([CreatorId] as nvarchar(50)),'00000000-0000-0000-0000-000000000000')) [CreatorId]
,LastModificationTime
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL(cast([LastModifierId] as nvarchar(50)),'00000000-0000-0000-0000-000000000000')) [LastModifierId]
,IsDeleted
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL(cast([DeleterId] as nvarchar(50)),'00000000-0000-0000-0000-000000000000')) DeleterId 
,DeletionTime
into #STicketApplicationDetail
from PLATFORM_ABBOTT_Stg.dbo.STicketApplicationDetail

drop table #STicketApplicationDetail



USE Speaker_Portal_STG2;



INSERT INTO dbo.STicketApplicationDetails
(
Id
,RowId
,HedgeDetailId
,ParentID
,SubVerificationStatus
,StoreId --门店数据来了需要将todo取消掉
,StoreName
,StoreChannel
,StoreSubChannel
,ExpenseNatureId
,ExpenseNatureCode
,ExpenseNature
,Quantity
,Price
,SubTotalAmountRMB
,SubVerifiedAmountRMB
,SettlementRegion
,SettlementPeriodStart
,SettlementPeriodEnd
,CityId
,CityCode
,CityName
,PredictDate
,Remark
,CreationTime
,CreatorId
,LastModificationTime
,LastModifierId
,IsDeleted
,DeleterId
,DeletionTime
)
SELECT
Id
,RowId
,HedgeDetailId
,ParentID
,SubVerificationStatus
,StoreId
,StoreName
,StoreChannel
,StoreSubChannel
,ISNULL([ExpenseNatureId],'00000000-0000-0000-0000-000000000000') ExpenseNatureId
,ExpenseNatureCode
,ExpenseNature
,Quantity
,ISNULL([Price],0.0) Price
,SubTotalAmountRMB
,SubVerifiedAmountRMB
,SettlementRegion
,SettlementPeriodStart
,SettlementPeriodEnd
,CityId
,CityCode
,CityName
,PredictDate
,Remark
,'1900-01-01' as CreationTime
,CreatorId
,null LastModificationTime
,LastModifierId
,IsDeleted
,DeleterId
,null DeletionTime
FROM #STicketApplicationDetail a
WHERE not exists (select * from dbo.STicketApplicationDetails where id=a.id)