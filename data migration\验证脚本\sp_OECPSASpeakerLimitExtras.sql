CREATE proc sp_OECPSASpeakerLimitExtras
as
begin
	
    select
	a.HCPIDNumber OriginalHCPIDNumber,
	TRIM(REPLACE(REPLACE(a.HCPIDNumber,'F',''),'M','')) HCPIDNumber,
	vendor.VendorId,
	'' [ComPSALimitId],
	1 [ModifyType],
	a.BUCode [BpmDivisionId],
	org.spk_NexBPMCode [DivisionId],
	a.Amount [ExtralAmountRest],
	a.Count [ExtralTimesRest],
	'BPMHistoryRecord' [ExtralAuditApplicationNo],
	a.Year [Year],
	N'BPM历史例外次数/金额迁移' Remark,
	'' Doc,
	Cast(YEAR(getdate()) as varchar)+'-01-01' [CreationTime],
	'' [CreatorId],
	'' [LastModificationTime],
	'' [LastModifierId],
	0 [IsDeleted],
	'' [DeleterId],
	'' [DeletionTime]
	into OECPSASpeakerLimitExtras_tmp
	from ODS_T_HCPCountExceedConfig a
	left join VendorPersonals_TMP vendor on TRIM(REPLACE(REPLACE(a.HCPIDNumber,'F',''),'M',''))=vendor.CardNo
	left join spk_organizationalmasterData org on a.BUCode=org.spk_BPMCode
	
	--将临时表的数据insert到tmp表
	IF OBJECT_ID(N'OECPSASpeakerLimitExtras_tmp', N'U') IS NOT NULL
		drop table OECPSASpeakerLimitExtras_tmp;
	select * into OECPSASpeakerLimitExtras_tmp from #OECPSASpeakerLimitExtras_tmp;
	PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'));
END;
