CREATE PROCEDURE dbo.sp_PurPAApplicationRefs
AS 
BEGIN
select 
DISTINCT 
PAFormCode AS PAApplicationCode,--
RefNo AS RefNo,--
Status AS Status,--
PAFormCode AS Id--基于07-1迁移的申请单主信息，以该单号定位对应的PurPAApplications.ID
into #PurPAApplicationRefs_tmp
from PLATFORM_ABBOTT_Dev.dbo.ODS_T_AP_REF otar 


 IF OBJECT_ID(N'PLATFORM_ABBOTT_Dev.dbo.PurPAApplicationRefs_tmp ', N'U') IS NOT NULL
	BEGIN
		update a 
		set a.RefNo             = b.RefNo
           ,a.PAApplicationCode = b.PAApplicationCode
           ,a.Status            = b.Status
        from PLATFORM_ABBOTT_Dev.dbo.PurPAApplicationRefs_tmp a
        left join #PurPAApplicationRefs_tmp b on a.PAApplicationCode = b.PAApplicationCode
        and a.RefNo = b.RefNo and a.Status = b.Status
        
        insert into PLATFORM_ABBOTT_Dev.dbo.PurPAApplicationRefs_tmp 
        select a.Id
               ,a.<PERSON>fN<PERSON>
               ,a.PAApplicationCode
               ,a.Status
		from #PurPAApplicationRefs_tmp a
		where not exists (select * from PLATFORM_ABBOTT_Dev.dbo.PurPAApplicationRefs_tmp 
		where a.PAApplicationCode = PAApplicationCode and a.RefNo = RefNo and a.Status = Status)
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into PLATFORM_ABBOTT_Dev.dbo.PurPAApplicationRefs_tmp from #PurPAApplicationRefs_tmp
    -- select * from #vendor_tbl
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
END;
