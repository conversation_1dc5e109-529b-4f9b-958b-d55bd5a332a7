SELECT
 TRY_CONVERT(UNIQUEIDENTIFIER, [Id]) [Id]
,TRY_CONVERT(UNIQUEIDENTIFIER, iif([VendorId] is NULL,'00000000-0000-0000-0000-000000000000',[VendorId])) [VendorId]
,VendorName as [VendorName]
,[VendorOldName]
,[VendorEngName]
,case when RegCertificateAddress is null or RegCertificateAddress ='' then N'-' else RegCertificateAddress end as  [RegCertificateAddress] -- 转换为空的数据，需检查中间层逻辑
,case when PostCode is null or PostCode ='' then N'-' else PostCode end as [PostCode]
,case when ContactName is null or ContactName ='' then N'-' else ContactName end as [ContactName]
,[ContactPhone]
,[ContactEmail]
,[WebSite]
,case when RegisterDate is null or RegisterDate ='' or RegisterDate='0001-01-01 00:00:00' then '1900-01-01 00:00:00' else STUFF(STUFF(RegisterDate, 5, 1, '-'), 8, 1, '-') end  as [RegisterDate] --数据为空，需检查中间层逻辑
,[OrgType]
,[IssuingAuthority]
,[RegisterCode]
,case when RegValidityStart is null or RegValidityStart ='' or RegValidityStart='0001-01-01 00:00:00' then '1900-01-01 00:00:00' else STUFF(STUFF(RegValidityStart, 5, 1, '-'), 8, 1, '-') end as [RegValidityStart]
,case when RegValidityEnd is null or RegValidityEnd ='' or RegValidityEnd='0001-01-01 00:00:00' then '1900-01-01 00:00:00' else STUFF(STUFF(RegValidityEnd, 5, 1, '-'), 8, 1, '-') end as [RegValidityEnd]
,case when Province is null or Province ='' then '-' else Province  end as [Province] -- 转换为空的数据，需检查中间层逻辑
,case when City is null or City ='' then '-' else City  end as [City] -- 转换为空的数据，需检查中间层逻辑
,[Legal]
,case when RegisterAmount is null  or RegisterAmount ='' then '0' 
	  when RegisterAmount in ('不详', '无','N/A') then '0'
else RegisterAmount end AS [RegisterAmount] --字段值部分为空
,[BusinessAuthority]
,[BusinessScope]
,case when LastYearSales is null  or LastYearSales ='' then '0'
	  when LastYearSales in ('不详', '无','N/A') then '0'
else LastYearSales end AS [LastYearSales] --字段值部分为空
,[KeyIndustry]
,[KeyClient]
,case when Staffs is null  or Staffs ='' then '0' 
	  when Staffs in ('不详', '无','N/A') then '0'
else Staffs end AS [Staffs]
,[Aptitudes]
,case when ApplyReason is null  or ApplyReason ='' then '-'
else ApplyReason end [ApplyReason] -- 转换为空的数据，需检查中间层逻辑
,N'{}' [ExtraProperties] -- 转换为空的数据，需检查中间层逻辑
,N'' [ConcurrencyStamp] -- 转换为空的数据，需检查中间层逻辑
,case when CreationTime is null  or CreationTime ='' then '1900-01-01 00:00:00'
else 
--TRY_CONVERT(DATETIME, 
--        SUBSTRING(CreationTime, 1, 4) + '-' +  -- YYYY
--        SUBSTRING(CreationTime, 5, 2) + '-' +  -- MM
--        SUBSTRING(CreationTime, 7, 2) + ' ' +  -- DD
--        RIGHT('0' + CAST((73411 / 3600) % 24 AS VARCHAR(2)), 2) + ':' +  -- HH
--        RIGHT('0' + CAST((73411 % 3600) / 60 AS VARCHAR(2)), 2) + ':' +  -- MM
--        RIGHT('0' + CAST(73411 % 60 AS VARCHAR(2)), 2)                   -- SS
--    )  
 CreationTime   end [CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CreatorId]) [CreatorId]
,CASE WHEN [LastModificationTime] IS NULL
	THEN null
	ELSE [LastModificationTime]
	END [LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [LastModifierId]) [LastModifierId]
,[IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, [DeleterId]) [DeleterId]
,CASE WHEN [DeletionTime] IS NULL
	THEN null
	ELSE [DeletionTime]
	END [DeletionTime]
,[Shareholder]
INTO #VendorOrgnizations
FROM (SELECT DISTINCT *,ROW_NUMBER() OVER(PARTITION BY Id ORDER BY Id) RK FROM PLATFORM_ABBOTT.dbo.VendorOrgnizations)a
WHERE RK = 1

select max(len(ContactName)) from #VendorOrgnizations

--drop table #VendorOrgnizations
USE Speaker_Portal;
ALTER TABLE Speaker_Portal.dbo.VendorOrgnizations ALTER COLUMN ContactName nvarchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL;
alter table dbo.VendorOrgnizations
alter column [ContactPhone] nvarchar(25);
ALTER TABLE Speaker_Portal.dbo.VendorOrgnizations ALTER COLUMN RegisterAmount nvarchar(255) NULL;
ALTER TABLE Speaker_Portal.dbo.VendorOrgnizations ALTER COLUMN LastYearSales nvarchar(255) NULL;
ALTER TABLE Speaker_Portal.dbo.VendorOrgnizations ALTER COLUMN Staffs nvarchar(255) NULL;

UPDATE a
SET
 a.[Id] = b.[Id]
,a.[VendorId] = b.[VendorId]
,a.[VendorName] = b.[VendorName]
,a.[VendorOldName] = b.[VendorOldName]
,a.[VendorEngName] = b.[VendorEngName]
,a.[RegCertificateAddress] = b.[RegCertificateAddress]
,a.[PostCode] = b.[PostCode]
,a.[ContactName] = b.[ContactName]
,a.[ContactPhone] = b.[ContactPhone]
,a.[ContactEmail] = b.[ContactEmail]
,a.[WebSite] = b.[WebSite]
,a.[RegisterDate] = b.[RegisterDate]
,a.[OrgType] = b.[OrgType]
,a.[IssuingAuthority] = b.[IssuingAuthority]
,a.[RegisterCode] = b.[RegisterCode]
,a.[RegValidityStart] = b.[RegValidityStart]
,a.[RegValidityEnd] = b.[RegValidityEnd]
,a.[Province] = b.[Province]
,a.[City] = b.[City]
,a.[Legal] = b.[Legal]
,a.[RegisterAmount] = b.[RegisterAmount]
,a.[BusinessAuthority] = b.[BusinessAuthority]
,a.[BusinessScope] = b.[BusinessScope]
,a.[LastYearSales] = b.[LastYearSales]
,a.[KeyIndustry] = b.[KeyIndustry]
,a.[KeyClient] = b.[KeyClient]
,a.[Staffs] = b.[Staffs]
,a.[Aptitudes] = b.[Aptitudes]
,a.[ApplyReason] = b.[ApplyReason]
,a.[ExtraProperties] = b.[ExtraProperties]
,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = b.[CreatorId]
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = b.[LastModifierId]
,a.[IsDeleted] = b.[IsDeleted]
,a.[DeleterId] = b.[DeleterId]
,a.[DeletionTime] = b.[DeletionTime]
,a.[Shareholder] = b.[Shareholder]
FROM dbo.VendorOrgnizations a
left join #VendorOrgnizations  b
ON a.id=b.id


--字段长度问题
INSERT INTO dbo.VendorOrgnizations
(
 [Id]
,[VendorId]
,[VendorName]
,[VendorOldName]
,[VendorEngName]
,[RegCertificateAddress]
,[PostCode]
,[ContactName]
,[ContactPhone]
,[ContactEmail]
,[WebSite]
,[RegisterDate]
,[OrgType]
,[IssuingAuthority]
,[RegisterCode]
,[RegValidityStart]
,[RegValidityEnd]
,[Province]
,[City]
,[Legal]
,[RegisterAmount]
,[BusinessAuthority]
,[BusinessScope]
,[LastYearSales]
,[KeyIndustry]
,[KeyClient]
,[Staffs]
,[Aptitudes]
,[ApplyReason]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[Shareholder]
)
SELECT 
 [Id]
,[VendorId]
,[VendorName]
,[VendorOldName]
,[VendorEngName]
,[RegCertificateAddress]
,[PostCode]
,[ContactName]
,[ContactPhone]
,[ContactEmail]
,[WebSite]
,[RegisterDate]
,[OrgType]
,[IssuingAuthority]
,[RegisterCode]
,[RegValidityStart]
,[RegValidityEnd]
,[Province]
,[City]
,[Legal]
,[RegisterAmount]
,[BusinessAuthority]
,[BusinessScope]
,[LastYearSales]
,[KeyIndustry]
,[KeyClient]
,[Staffs]
,[Aptitudes]
,[ApplyReason]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[Shareholder]
FROM #VendorOrgnizations a
WHERE not exists (select * from dbo.VendorOrgnizations where id=a.id)


--truncate table dbo.VendorOrgnizations


-- Speaker_Portal.dbo.VendorOrgnizations definition

-- Drop table

-- DROP TABLE Speaker_Portal.dbo.VendorOrgnizations;



