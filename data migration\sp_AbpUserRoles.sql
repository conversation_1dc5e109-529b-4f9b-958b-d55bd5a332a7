CREATE PROCEDURE dbo.sp_AbpUserRoles
AS 
BEGIN
	select 
ss.spk_NexBPMCode  as [UserId],
case when Role_Code='N/A' then 'admin'
when Role_Code='00011000010000000000000000000000000000000000000000' then 'BizAdmin'
when Role_Code='68f4ae813a0a4f0cac056297bcf0bcc2'
or Role_Code='af6ef48ac1d04d26819a50e9997ec578'
or Role_Code='22c77703fc274e238d784e7ea7dc81f3'
or Role_Code='420840cea67e4583a2922a65c8d43205' 
or Role_Code='e9c95fc764b74d34bec11049c26b7a29' then 'GeneralApplicant'
when Role_Code='211c31acb3434a5e977bd18eabf8fc3b'
or Role_Code='3e9fad20a9bf4064b3c2276977ea422d' then 'FactoryApplicant'
when Role_Code='N/A' then 'SpeakerApplicant'
when Role_Code='00011000020000000000000000000000000000000000000000'
or Role_Code='ec63968f6361473bbe5593a3e4df8f6e' then 'Approver'
when Role_Code='64929e580c9d4a74adbbcec60322d015' or 
Role_Code='8c300f7a2261454fa3684bc7917a6ab9' or
Role_Code='1e49773ac7e64a2185401961c8e81a27'
then 'DeptFinance'
when Role_Code='N/A' then 'DeptAdmin'
when Role_Code='87bf482255bd434e8b649e1ac9504bff' then 'BudgetOwner'
when Role_Code='f22f799a22d34fb4b2ccbcd4552b6bde'
or Role_Code='320b4a5f064746dc8472aee15c7a63a4'
or Role_Code='4b9963d3d34e443189276d818b06748c' then 'OECAdmin'
when Role_Code='N/A' then 'OECInvestigator'
when Role_Code='1226653dc301417c886c2f844f40a6fc'
or Role_Code='17f27868e7b1475d9ac2c4b0a4f6a48b'
or Role_Code='2f3e342772354bf5badfcb055b649eca'
or Role_Code='41077a5dcd0d4b038ffcd13a84174ac7'
or Role_Code='5dda3b88a2184a50af1e14097c821d23'
or Role_Code='6d67dc640d9848f3875e36baf40771be'
or Role_Code='5de2df606ea3467c811ecbc6760be884'
or Role_Code='6a66a00f807c4a4fba85ec5eeadbb29e'
or Role_Code='982d2d51a82d491caea5f2e0d48bcc37' then 'GroupFinance'
when Role_Code='87fee822893940699d4ff22571a579bd' then 'ProcurementSpecialist'
when Role_Code='N/A' then 'TaxAdmin'
when Role_Code='af6ef48ac1d04d26819a50e9997ec578' then 'EPDUser'
when Role_Code='5f6ab831489345b095e6d1183e632078' then 'N/A'
when Role_Code='617a681b28114bed919f2436a644a9a1' then 'N/A'
when Role_Code='aae503b4f9d24f9b909c1886befa9c48' then 'N/A'
when Role_Code='bfd4eab6421245b082e7bf8f5e52bff1' then 'N/A'
when Role_Code='c365e7aa9d8f48e9b7d479f185fea4cf' then 'N/A'
when Role_Code='c501155e907048a4815c1f2356a1b669' then 'N/A'
when Role_Code='ceebd12e1e054477bec56385ba2d02a1' then 'N/A'
when Role_Code='d89fc6df2f0a4cd39b1da10628c88c05' then 'N/A'
when Role_Code='e683cc0f7cea4ddab8fac67829f8f16c' then 'N/A'
end as [RoleId],
'' as [TenantId],
Role_Code
into #AbpUserRoles
from ods_T_EMPLOYEE_ROLE tr
left join spk_staffmasterdata ss 
on tr.Emp_Id=ss.bpm_id 

delete from #AbpUserRoles where UserId in (
select UserId from #AbpUserRoles  a 
join (select id,Name,StaffCode from AbpUsers au  where (Name =N'余溢男 Sally Yu' and StaffCode='10528864') or (Name =N'李洁Li Jie' and StaffCode='12252716')) b
on a.UserId=b.id and ((RoleId='GroupFinance' and Name =N'李洁Li Jie' and StaffCode='12252716')  or ( RoleId='OECAdmin' and Name =N'余溢男 Sally Yu' and StaffCode='10528864'))
)

 IF OBJECT_ID(N'PLATFORM_ABBOTT_Stg.dbo.AbpUserRoles ', N'U') IS NOT NULL
	BEGIN
		drop table PLATFORM_ABBOTT_Stg.dbo.AbpUserRoles
		select *
        into PLATFORM_ABBOTT_Stg.dbo.AbpUserRoles from #AbpUserRoles
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into PLATFORM_ABBOTT_Stg.dbo.AbpUserRoles from #AbpUserRoles
    -- select * from #vendor_tbl
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
END
