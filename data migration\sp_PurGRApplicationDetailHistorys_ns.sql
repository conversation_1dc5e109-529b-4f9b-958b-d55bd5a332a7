CREATE PROCEDURE dbo.sp_PurGRApplicationDetailHistorys_ns
AS 
BEGIN
	select 
a.[Id]
,pgt.Id as [GRApplicationId]
,sp.spk_NexBPMCode as [ProductId]
,sd.spk_code as [InvoiceType]
,a.[OrderQuantity]
,a.[TotalReceivedQuantity]
,a.[DeliveryMethod]
,a.[CurrentReceivingQuantity]
,a.[CurrentSignedQuantity]
,a.[UnitPrice]
,a.[ReceivedAmount]
,a.[SigningDate]
,a.[IsArrive]
,a.[ExtraProperties]
,a.[ConcurrencyStamp]
,a.CreatorId as [CreationTime]
,ss.spk_NexBPMCode as [CreatorId]
,a.[LastModificationTime]
,a.[LastModifierId]
,a.[IsDeleted]
,a.[DeleterId]
,a.[DeletionTime]
,a.[AllocationAmount]
,a.[PRDetailId]
,a.[AllocationRMBAmount]
,a.[PODetailId]
,ppt2.Id  as [PurPAApplicationId]
,a.[PurchaseRMBAmount]
,a.[GRApplicationDetailId]
,a.[IsAdvancePayment]
,a.[ProductName]
into #PurGRApplicationDetailHistorys
from [PurGRApplicationDetailHistorys_tmp] a --651173
left join PurPRApplications_tmp ppt 
on a.GRApplicationId =ppt.ProcInstId 
left join spk_productmasterdata sp
on sp.spk_BPMCode =a.ProductId 
left join spk_staffmasterdata ss 
on a.CreationTime =ss.bpm_id --651173
left join (select *,ROW_NUMBER ()over(PARTITION by ApplicationCode order by  Id desc) rn from PurGRApplications_tmp) pgt 
on a.GRApplicationId =pgt.ProcInstId  and pgt.rn=1--651173
left join (select *,ROW_NUMBER ()over(PARTITION by ApplicationCode order by  ProcInstId desc) rn from PurPAApplications_tmp) ppt2 
on ppt2.ApplicationCode =a.PurPAApplicationId and ppt2.rn=1
left join spk_dictionary sd
on a.InvoiceType=sd.spk_Name and sd.spk_type=N'发票类型';


 IF OBJECT_ID(N'PLATFORM_ABBOTT.dbo.PurGRApplicationDetailHistorys ', N'U') IS NOT NULL
	BEGIN
		drop table PLATFORM_ABBOTT.dbo.PurGRApplicationDetailHistorys
		select *
        into PLATFORM_ABBOTT.dbo.PurGRApplicationDetailHistorys from #PurGRApplicationDetailHistorys
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into PLATFORM_ABBOTT.dbo.PurGRApplicationDetailHistorys from #PurGRApplicationDetailHistorys
    -- select * from #vendor_tbl
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END





END;