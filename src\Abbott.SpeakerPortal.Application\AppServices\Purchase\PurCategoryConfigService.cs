﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;

using Abbott.SpeakerPortal.Contracts.Purchase.PurCategoryConfig;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.Purchase.PurCategoryConfig;
using Abbott.SpeakerPortal.Entities.Purchase.PurPOApplication;
using Microsoft.EntityFrameworkCore;

using Volo.Abp.Application.Dtos;

namespace Abbott.SpeakerPortal.AppServices.Purchase
{
    public class PurCategoryConfigService : SpeakerPortalAppService, IPurCategoryConfigService
    {
        /// <summary>
        /// 获取采购品类配置列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetCategoryConfigListResponseDto>> GetCategoryConfigsAsync(GetCategoryConfigListRequestDto request)
        {
            var queryConfig = await LazyServiceProvider.LazyGetService<IPurCategoryConfigRepository>().GetQueryableAsync();
            var query = queryConfig.WhereIf(!string.IsNullOrEmpty(request.Name), a => a.NameEn.Contains(request.Name) || a.NameCn.Contains(request.Name))
            .WhereIf(request.IsEnabled.HasValue, a => a.IsEnabled == request.IsEnabled);

            var queryable = queryConfig.WhereIf(request.IsEnabled.HasValue, a => a.IsEnabled == request.IsEnabled);
            query = query.Union
                (
                    //根据第二级找到第一级
                    queryable.Join(query.Where(a => a.Hierarchy == 2), a => a.Id, a => a.ParentId, (a, b) => a)
                ).Union
                (
                    //根据第三级找到第二级
                    queryable.Join(query.Where(a => a.Hierarchy == 3), a => a.Id, a => a.ParentId, (a, b) => a)
                ).Union
                (
                    //根据第三级找到的第二级再找第一级
                    queryable.Join(
                        queryable.Join(query.Where(a => a.Hierarchy == 3), a => a.Id, a => a.ParentId, (a, b) => a)
                        , a => a.Id, a => a.ParentId, (a, b) => a
                    )
                )
                .Union
                (
                    //根据第2级找第3级
                    queryable.Join(query.Where(x => x.Hierarchy == 2), a => a.ParentId, b => b.Id, (a, b) => a)
                )
                .Union
                (
                    //根据第1级找第2级
                    queryable.Join(query.Where(x => x.Hierarchy == 1), a => a.ParentId, b => b.Id, (a, b) => a)
                )
                .Union
                (
                    //根据第1级找到的第2级再找第3级
                    queryable.Join(
                        queryable.Join(query.Where(x => x.Hierarchy == 1), a => a.ParentId, b => b.Id, (a, b) => a)//根据第1级找第2级
                        , a => a.ParentId, b => b.Id, (a, b) => a
                    )
                )
                .Distinct();

            //取第一级的数量
            var count = query.Count(a => !a.ParentId.HasValue);
            var datas = query.Where(a => !a.ParentId.HasValue).OrderByDescending(a => a.CreationTime).Skip(request.PageIndex * request.PageSize).Take(request.PageSize)
                .Join(query, a => a.Path, a => a.Path.Substring(0, 36), (a, b) => b)
                .ToArray();

            var list = new List<GetCategoryConfigListResponseDto>();
            foreach (var item in datas.Where(a => !a.ParentId.HasValue))
            {
                var dto = CombineStructure(item, datas);
                list.Add(dto);
            }

            var result = new PagedResultDto<GetCategoryConfigListResponseDto>(count, list);
            return result;
        }

        GetCategoryConfigListResponseDto CombineStructure(PurCategoryConfig config, IEnumerable<PurCategoryConfig> configs)
        {
            var dto = ObjectMapper.Map<PurCategoryConfig, GetCategoryConfigListResponseDto>(config);
            var children = configs.Where(a => a.ParentId == config.Id);
            if (children.Any())
                dto.Children = new List<GetCategoryConfigListResponseDto>();

            foreach (var child in children)
            {
                var c = CombineStructure(child, configs);
                dto.Children.Add(c);
            }

            return dto;
        }

        /// <summary>
        /// 创建采购品类配置
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> CreateCategoryConfigAsync(CreateCategoryConfigRequestDto request)
        {
            PurCategoryConfig parent = null;
            var repository = LazyServiceProvider.LazyGetService<IPurCategoryConfigRepository>();
            Guid? parentId = null;
            if (request.ParentId.HasValue)
            {
                parent = await repository.FindAsync(request.ParentId.Value);
                if (parent == null)
                    return MessageResult.FailureResult("未找到指定的父级配置");
                parentId = parent.Id;
            }

            var query = await repository.GetQueryableAsync();
            //query.WhereIf(!parentId.HasValue, x => !x.ParentId.HasValue).WhereIf(parentId.HasValue, x => x.ParentId == parentId);
            var isDuplicate = query.Any(x => x.ParentId == parentId && x.NameEn == request.NameEn);
            if (isDuplicate)
                return MessageResult.FailureResult(parentId.HasValue ? "上级品类下已有同名品类，添加失败" : "已有同名品类，添加失败");


            var config = ObjectMapper.Map<CreateCategoryConfigRequestDto, PurCategoryConfig>(request);
            await repository.InsertAsync(config);
            if (parent != null)
            {
                config.Hierarchy = parent.Hierarchy + 1;
                config.Path = $@"{parent.Path}\{config.Id}";
            }
            else
            {
                config.Hierarchy = 1;
                config.Path = config.Id.ToString();
            }

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 修改采购品类配置
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> UpdateCategoryConfigAsync(UpdateCategoryConfigRequestDto request)
        {
            PurCategoryConfig parent = null;
            var repository = LazyServiceProvider.LazyGetService<IPurCategoryConfigRepository>();
            Guid? parentId = null;
            if (request.ParentId.HasValue)
            {
                parent = await repository.FindAsync(request.ParentId.Value);
                if (parent == null)
                    return MessageResult.FailureResult("未找到指定的父级配置");
                parentId = parent.Id;
            }

            var query = await repository.GetQueryableAsync();
            var isDuplicate = query.Any(x => x.ParentId == parentId && x.NameEn == request.NameEn && x.Id != request.Id);
            if (isDuplicate)
                return MessageResult.FailureResult(parentId.HasValue ? "上级品类下已有同名品类，添加失败" : "已有同名品类，添加失败");

            var config = await repository.FindAsync(request.Id);
            if (config == null)
                return MessageResult.FailureResult("未找到指定的配置");

            config = ObjectMapper.Map(request, config);
            await repository.UpdateAsync(config);

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 获取采购品类详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<GetCategoryConfigDetailResponseDto> GetCategoryConfigDetailAsync(Guid id)
        {
            var query = await LazyServiceProvider.LazyGetService<IPurCategoryConfigRepository>().GetQueryableAsync();
            var data = query.Where(a => a.Id == id)
                .GroupJoin(query, a => a.ParentId, a => a.Id, (a, b) => new { CategoryConfig = a, Parent = b })
                .SelectMany(a => a.Parent.DefaultIfEmpty(), (a, b) => new GetCategoryConfigDetailResponseDto
                {
                    ParentId = b.Id,
                    ParentNameEn = b.NameEn,
                    ParentNameCn = b.NameCn,
                    NameEn = a.CategoryConfig.NameEn,
                    NameCn = a.CategoryConfig.NameCn,
                    Description = a.CategoryConfig.Description
                }).FirstOrDefault();

            return data;
        }

        /// <summary>
        /// 删除采购品类配置
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task<MessageResult> DeleteCategoryConfigAsync(IEnumerable<Guid> ids)
        {
            var queryPo = await LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>().GetQueryableAsync();
            var categoryRepository = LazyServiceProvider.LazyGetService<IPurCategoryConfigRepository>();
            var queryCategoryConfig = await categoryRepository.GetQueryableAsync();

            var categoryConfigs = queryCategoryConfig.Where(a => ids.Contains(a.Id)).ToList();
            List<Guid> deleteIds = new List<Guid>();
            var configs = await categoryRepository.GetListAsync();
            foreach (var categoryConfig in categoryConfigs)
            {
                if (queryPo.Any(a => a.PRType.Contains(categoryConfig.Id.ToString())))
                {
                    return MessageResult.FailureResult("品类已在PO中使用,无法删除");
                }
                deleteIds.Add(categoryConfig.Id);
                deleteIds.AddRange(CombineStructures(categoryConfig.Id, configs));
            }
            await categoryRepository.DeleteManyAsync(deleteIds);
            return MessageResult.SuccessResult();
        }

        List<Guid> CombineStructures(Guid parentId, List<PurCategoryConfig> configs)
        {
            // 查找指定父ID的所有直接子节点
            var children = configs.Where(n => n.ParentId == parentId).ToList();
            // 存储结果的列表
            var result = new List<Guid>();
            // 遍历直接子节点
            foreach (var child in children)
            {
                // 添加当前子节点的ID到结果列表
                result.Add(child.Id);
                // 递归地获取当前子节点的所有子节点ID
                result.AddRange(CombineStructures(child.Id, children));
            }
            return result;
        }

        /// <summary>
        /// 启用、禁用采购品类配置
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> EnableDisableCategoryConfigAsync(EnableDisableCategoryConfigRequestDto request)
        {
            var repository = LazyServiceProvider.LazyGetService<IPurCategoryConfigRepository>();
            var datas = await repository.GetListAsync(a => a.Path.Contains(request.Id.ToString()));
            datas.ForEach(a => a.IsEnabled = request.Enable);
            await repository.UpdateManyAsync(datas);

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 获取一级采购品类
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<IEnumerable<GetFirstLevelCategoryResponseDto>> GetFirstLevelCategoryAsync()
        {
            var queryConfig = await LazyServiceProvider.LazyGetService<IPurCategoryConfigRepository>().GetQueryableAsync();
            var query = queryConfig.Where(a => a.Hierarchy == 1 && a.IsEnabled == true);

            var count = await query.CountAsync();
            var datas = query.OrderByDescending(a => a.CreationTime)
                .Select(x => new GetFirstLevelCategoryResponseDto
                {
                    Id = x.Id,
                    NameEn = x.NameEn,
                    NameCn = x.NameCn,
                    Hierarchy = x.Hierarchy,
                    IsEnabled = x.IsEnabled,
                    Description = x.Description
                }).ToArray();

            return datas;
        }
    }
}
