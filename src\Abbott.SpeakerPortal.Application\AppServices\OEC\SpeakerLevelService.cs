﻿using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.OEC.SpeakerLevel;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Dtos;
using Abbott.SpeakerPortal.Entities.OECSpeakerLevelDetails;
using Abbott.SpeakerPortal.Entities.OECSpeakerLevelOperHistorys;
using Abbott.SpeakerPortal.OEC.SpeakerLevel;
using Abbott.SpeakerPortal.Utils;

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;

using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;

namespace Abbott.SpeakerPortal.OEC
{
    public class SpeakerLevelService : SpeakerPortalAppService, ISpeakerLevelService
    {
        /// <summary>
        /// The service provider
        /// </summary>
        private readonly IServiceProvider _serviceProvider;

        /// <summary>
        /// The logger
        /// </summary>
        private readonly ILogger<SpeakerLevelService> _logger;

        public SpeakerLevelService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _logger = serviceProvider.GetService<ILogger<SpeakerLevelService>>();
        }

        /// <summary>
        /// Speakers the level list asynchronous.
        /// </summary>
        /// <returns></returns>
        public async Task<PagedResultDto<SpeakerLevelListDto>> SpeakerLevelListAsync(PagedDto request)
        {
            var dataverse = LazyServiceProvider.LazyGetService<IDataverseService>();
            var queryUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser, Guid>>().GetQueryableAsync();
            var querySpeakerLevelDetail = await LazyServiceProvider.LazyGetService<IOECSpeakerLevelDetailRepository>().GetQueryableAsync();
            var speakerLevels = await dataverse.GetDictionariesAsync(DictionaryType.HCPLevel, null);
            var divisions = await dataverse.GetDivisions(null);

            //speaker level details
            var speakerDetails = querySpeakerLevelDetail
                .GroupJoin(queryUser, a => a.CreatorId, a => a.Id, (a, b) => new { SpeakerLevelDetail = a, Users = b })
                .SelectMany(a => a.Users.DefaultIfEmpty(), (a, b) => new
                {
                    a.SpeakerLevelDetail,
                    UserName = b.Name
                }).ToArray();

            var list = new List<SpeakerLevelListDto>();
            foreach (var level in speakerLevels)
            {
                var details = speakerDetails.Where(a => a.SpeakerLevelDetail.SpeakerLevelCode == level.Code).ToArray();
                var dept = details.Length == 1 && details.First().SpeakerLevelDetail.BuId == Guid.Empty ? EnumUtil.GetDescription(Enums.ApplicableDepartments.All) : divisions.Where(a => details.Any(a1 => a1.SpeakerLevelDetail.BuId == a.Id)).Select(a => a.DepartmentName).Distinct().JoinAsString(",");

                list.Add(new SpeakerLevelListDto
                {
                    Id = level.Code,
                    SpeakerLevel = level.Name,
                    Department = dept,
                    ModifyId = details.FirstOrDefault()?.SpeakerLevelDetail.CreatorId,
                    ModifyName = details.FirstOrDefault()?.UserName,
                    ModifyTime = details.FirstOrDefault()?.SpeakerLevelDetail.CreationTime.ToString("yyyy-MM-dd HH:mm:ss")
                });
            }

            var res = new PagedResultDto<SpeakerLevelListDto>()
            {
                Items = list,
                TotalCount = list.Count,
            };

            return res;
        }

        /// <summary>
        /// Speakers the level detail asynchronous.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns></returns>
        public async Task<SpeakerLevelDetailDto> SpeakerLevelDetailAsync(string code)
        {
            var dataverse = _serviceProvider.GetService<IDataverseService>();
            var divisions = await dataverse.GetDivisions();
            var speakerLevelDetailRepository = _serviceProvider.GetService<IRepository<OECSpeakerLevelDetail, Guid>>();
            var speakerLevelOperHistoryRepository = _serviceProvider.GetService<IRepository<OECSpeakerLevelOperHistory, Guid>>();
            var speakerLevelQueryable = await speakerLevelDetailRepository.GetQueryableAsync();
            var speakerLevelOperHistoryQueryable = await speakerLevelOperHistoryRepository.GetQueryableAsync();

            var result = new SpeakerLevelDetailDto();
            var divisionList = new List<Division>();
            var divisionIds = speakerLevelQueryable.Where(p => p.SpeakerLevelCode == code).Select(d => d.BuId).ToArray();
            if (divisionIds.Length == 1 && divisionIds.First() == Guid.Empty)
            {
                result.Type = Enums.ApplicableDepartments.All;
                divisionList.Add(new Division { Id = Guid.Empty, Name = EnumUtil.GetDescription(Enums.ApplicableDepartments.All) });
            }
            else
            {
                result.Type = Enums.ApplicableDepartments.NotAll;
                foreach (var divisionId in divisionIds)
                {
                    var name = divisions.FirstOrDefault(p => p.Id == divisionId)?.DepartmentName;
                    var division = new Division
                    {
                        Id = divisionId,
                        Name = name
                    };
                    divisionList.Add(division);
                }
            }

            result.Divisions = divisionList;

            var speakerLevelOperHistory = speakerLevelOperHistoryQueryable.Where(p => p.SpeakerLevelCode == code).OrderByDescending(d => d.CreationTime);
            var lastSpeakerLevelOper = speakerLevelOperHistory.FirstOrDefault();
            if (lastSpeakerLevelOper != null)
            {
                result.Remark = lastSpeakerLevelOper.Remark;
            }

            return result;
        }

        /// <summary>
        /// Saves the speaker level asynchronous.
        /// </summary>
        /// <param name="saveSpeakerLevelDto">The save speaker level dto.</param>
        /// <returns></returns>
        public async Task<bool> SaveSpeakerLevelAsync(SaveSpeakerLevelDto saveSpeakerLevelDto)
        {
            try
            {
                var divisions = await _serviceProvider.GetService<IDataverseService>().GetDivisions();
                var speakerLevelOperHistoryRepository = _serviceProvider.GetService<IRepository<OECSpeakerLevelOperHistory, Guid>>();
                var speakerLevelDetailRepository = _serviceProvider.GetService<IRepository<OECSpeakerLevelDetail, Guid>>();

                var applicableDepts = divisions.Where(p => saveSpeakerLevelDto.Divisions.Contains(p.Id.ToString())).Select(a => a.DepartmentName);

                var speakerLevelOperHistory = new OECSpeakerLevelOperHistory()
                {
                    SpeakerLevelCode = saveSpeakerLevelDto.Id,
                    ModifyDepartment = saveSpeakerLevelDto.ApplicableDept == Enums.ApplicableDepartments.All ? EnumUtil.GetDescription(Enums.ApplicableDepartments.All) : applicableDepts.JoinAsString(","),
                    Remark = saveSpeakerLevelDto.Remark
                };

                //变更历史
                await speakerLevelOperHistoryRepository.InsertAsync(speakerLevelOperHistory);
                //删除老数据
                await speakerLevelDetailRepository.DeleteDirectAsync(a => a.SpeakerLevelCode == saveSpeakerLevelDto.Id);
                if (saveSpeakerLevelDto.ApplicableDept == Enums.ApplicableDepartments.All)
                {
                    var speakerLevelDetail = new OECSpeakerLevelDetail { SpeakerLevelCode = saveSpeakerLevelDto.Id };
                    await speakerLevelDetailRepository.InsertAsync(speakerLevelDetail);
                }
                else
                {
                    var newSpeakerLevelDetails = divisions.Where(p => saveSpeakerLevelDto.Divisions.Contains(p.Id.ToString()))
                        .Select(p => new OECSpeakerLevelDetail
                        {
                            SpeakerLevelCode = saveSpeakerLevelDto.Id,
                            BuId = p.Id
                        })
                        .ToArray();
                    await speakerLevelDetailRepository.InsertManyAsync(newSpeakerLevelDetails);
                }
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"SpeakerLevelService's SaveSpeakerLevelAsync has an error : {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Modifies the record.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns></returns>
        public async Task<PagedResultDto<SpeakerLevelModifyRecordDto>> ModifyRecordAsync(SpeakerLevelModifyRecordRequestDto request)
        {
            try
            {
                var queryUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser, Guid>>().GetQueryableAsync();
                var speakerLevelOperHistoryQueryable = await LazyServiceProvider.LazyGetService<IRepository<OECSpeakerLevelOperHistory, Guid>>().GetQueryableAsync();
                var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();

                var results = speakerLevelOperHistoryQueryable.Where(p => p.SpeakerLevelCode == request.Id)
                    .GroupJoin(queryUser, a => a.CreatorId, a => a.Id, (a, b) => new { OperHistory = a, Users = b })
                    .SelectMany(a => a.Users.DefaultIfEmpty(), (a, b) => new { a.OperHistory, User = b })
                    .Select(p => new
                    {
                        p.OperHistory.CreationTime,
                        p.OperHistory.CreatorId,
                        p.OperHistory.ModifyDepartment,
                        p.OperHistory.Remark,
                        p.User.Name
                    })
                    .OrderByDescending(p => p.CreationTime);

                var pageResult = results.Skip(request.PageIndex * request.PageSize).Take(request.PageSize)
                    .Select(p => new SpeakerLevelModifyRecordDto()
                    {
                        ModifyTime = p.CreationTime.ToString("yyyy-MM-dd HH:mm:ss"),
                        ModifyId = p.CreatorId,
                        ModifyDepartment = p.ModifyDepartment,
                        Remark = p.Remark,
                        ModifyName = p.Name
                    }).ToList();

                var res = new PagedResultDto<SpeakerLevelModifyRecordDto>()
                {
                    Items = pageResult,
                    TotalCount = results.Count(),
                };

                return res;
            }
            catch (Exception ex)
            {
                _logger.LogError($"SpeakerLevelService's ModifyRecord has an error : {ex.Message}");
                return null;
            }
        }

    }
}
