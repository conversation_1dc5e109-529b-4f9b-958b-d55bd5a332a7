﻿using Abbott.SpeakerPortal.AppServices.Common;
using Abbott.SpeakerPortal.AppServices.System;
using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Agent;
using Abbott.SpeakerPortal.Contracts.Approval;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.Approve;
using Abbott.SpeakerPortal.Contracts.Common.PowerApp;
using Abbott.SpeakerPortal.Contracts.Dataverse;
using Abbott.SpeakerPortal.Contracts.OEC.SpeakerAuth;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.Common.Workflow.WorkflowTasks;
using Abbott.SpeakerPortal.Entities.OEC.OECSpeakerAuthApplys;
using Abbott.SpeakerPortal.Entities.OECSpeakerAuthApplys;
using Abbott.SpeakerPortal.Entities.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Entities.VendorPersonals;
using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.OEC.SpeakerAuth;
using Abbott.SpeakerPortal.Utils;
using Abbott.SpeakerPortal.Extension;

using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Wordprocessing;

using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Mvc.ApplicationModels;
using Microsoft.Extensions.Logging;

using MiniExcelLibs;

using Senparc.Weixin.MP.AdvancedAPIs.MerChant;

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Text;
using System.Threading.Tasks;

using Volo.Abp.Application.Dtos;
using Volo.Abp.ObjectMapping;

using static Abbott.SpeakerPortal.Enums.DataverseEnums.PturtTypeConfig;
using static Abbott.SpeakerPortal.Enums.PurPAStatus;
using static OpenIddict.Abstractions.OpenIddictConstants;
using System.Text.Json;
using Abbott.SpeakerPortal.BackgroundWorkers.BPMEmail;
using Hangfire;
using Abbott.SpeakerPortal.BackgroundWorkers.Approval;
using Abbott.SpeakerPortal.Person;

namespace Abbott.SpeakerPortal.AppServices.OEC
{
    /// <summary>
    ///  讲者授权申请
    /// </summary>
    public class OECSpeakerAuthService : SpeakerPortalAppService, IOECSpeakerAuthService
    {
        private readonly ILogger<OECSpeakerAuthService> _logger;
        public OECSpeakerAuthService(ILogger<OECSpeakerAuthService> logger)
        {
            _logger = logger;
        }
        public async Task<MessageResult> ActivateAsync(ActivateRequestDto request)
        {
            var speakerAuthRepository = LazyServiceProvider.LazyGetService<IOECSpeakerAuthApplyRepository>();
            var querySpeakerAuth = await speakerAuthRepository.GetQueryableAsync();
            if (string.IsNullOrWhiteSpace(request.OECApprovalNo))
                return MessageResult.FailureResult("OEC例外审批编号未填写");
            var speakerAuth = querySpeakerAuth.Where(a => a.Id == request.ApplyId).FirstOrDefault();
            if (speakerAuth == null)
                return MessageResult.FailureResult("未查询到相关讲者申请信息");
            if (speakerAuth.Status == SpeakerAuthStatus.Approvaling)
                return MessageResult.FailureResult("申请审批中，暂不能激活");
            if (speakerAuth.Status == SpeakerAuthStatus.Activated)
                return MessageResult.FailureResult("该申请已激活，无需重复激活");
            speakerAuth.Status = SpeakerAuthStatus.Activated;
            speakerAuth.OECApprovalNo = request.OECApprovalNo;
            await speakerAuthRepository.UpdateAsync(speakerAuth);
            return MessageResult.SuccessResult();
        }

        public async Task<Stream> ExportSpeakerAuthApplyUserListAsync(SpeakerAuthtUserLisRequestDto request)
        {
            try
            {
                var speakers = await GetSpeakerAuthApplyUserListAsync(request, true);
                MemoryStream stream = new();
                stream.SaveAs(speakers.Items, true, "SheetName");
                stream.Seek(0, SeekOrigin.Begin);
                return stream;
            }
            catch (Exception ex)
            {
                _logger.LogError($"ExportSpeakerAuthApplyUserListAsync error:{ex.Message}");
                return null;
            }
        }

        public async Task<List<StaffAndPositionDto>> GetOECApprovalUsersAsync(Guid departmentId)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var commonService = LazyServiceProvider.LazyGetService<ICommonService>();
            var approvalUsers = await dataverseService.GetStaffAndPositionAsync(((int)PositionType.OECApproverPosition).ToString());
            var departments = await dataverseService.GetOrganizations(departmentId.ToString());
            var department = departments.FirstOrDefault(a => a.Id == departmentId);
            var departmentToBus = await commonService.GetParentOrgs(department);
            var bu = departmentToBus.FirstOrDefault(a => a.OrganizationType == Enums.DataverseEnums.Organization.OrganizationType.Bu);
            if (bu == null)
                return null;
            return approvalUsers.Where(a => a.OrganizationId == bu.Id).ToList();//获取对应组织BU下的OEC审批人
        }

        public async Task<SpeakerAuthApplyDto> GetSpeakerAuthApplyDetailsAsync(Guid applyId)
        {
            var speakerAuthRepository = LazyServiceProvider.LazyGetService<IOECSpeakerAuthApplyRepository>();
            var querySpeakerAuth = await speakerAuthRepository.GetQueryableAsync();
            var speakerAuth = querySpeakerAuth.Where(a => a.Id == applyId).FirstOrDefault();
            return ObjectMapper.Map<OECSpeakerAuthApply, SpeakerAuthApplyDto>(speakerAuth);
        }

        public async Task<PagedResultDto<SpeakerAuthApplyUsersDto>> GetSpeakerAuthApplyUserListAsync(SpeakerAuthtUserLisRequestDto request, bool isExport = false)
        {
            var result = new PagedResultDto<SpeakerAuthApplyUsersDto>();
            var speakerAuthUserRepository = LazyServiceProvider.LazyGetService<IOECSpeakerAuthApplyUserRepository>();
            var querySpeakerAuthUser = await speakerAuthUserRepository.GetQueryableAsync();
            var queryVendor = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();
            var queryVendorPersonal = await LazyServiceProvider.LazyGetService<IVendorPersonalRepository>().GetQueryableAsync();
            var query = querySpeakerAuthUser.GroupJoin(queryVendor, a => a.VendorCode, b => b.VendorCode, (a, b) => new { sau = a, vendors = b })
                .SelectMany(a => a.vendors.DefaultIfEmpty(), (a, b) => new { a.sau, vendor = b })
                .GroupJoin(queryVendorPersonal, a => a.vendor.Id, b => b.VendorId, (a, b) => new { a.sau, a.vendor, personals = b })
                .SelectMany(a => a.personals.DefaultIfEmpty(), (a, b) => new { a.sau, a.vendor, personal = b })
                .Where(a => a.sau.ApplyId == request.ApplyId);
            var resultData = new List<SpeakerAuthApplyUsersDto>();
            if (!isExport)
            {
                var personals = query.Skip(request.PageIndex * request.PageSize).Take(request.PageSize)
                 .ToList();
                resultData = personals.Select(a => new SpeakerAuthApplyUsersDto
                {
                    SpeakerName = a.personal?.SPName,
                    VendorCode = a.sau.VendorCode,
                    PTName = a.vendor?.PTName,
                    HospitalName = a.vendor?.HospitalName,
                    StandardHosDepName = a.vendor?.StandardHosDepName,
                    HosDepartment = a.vendor?.HosDepartment,
                    SPLevel = a.vendor?.SPLevel,
                    Content = a.sau.Content,
                }).ToList();
            }
            else
            {
                var personals = query.ToList();
                resultData = personals.Select(a => new SpeakerAuthApplyUsersDto
                {
                    SpeakerName = a.personal?.SPName,
                    VendorCode = a.sau.VendorCode,
                    PTName = a.vendor?.PTName,
                    HospitalName = a.vendor?.HospitalName,
                    StandardHosDepName = a.vendor?.StandardHosDepName,
                    HosDepartment = a.vendor?.HosDepartment,
                    SPLevel = a.vendor?.SPLevel,
                    Content = a.sau.Content,
                }).ToList();
            }
            result.TotalCount = query.Count();
            result.Items = resultData;
            return result;
        }

        public async Task<PagedResultDto<SpeakerAuthApplyDto>> GetSpeakerAuthListAsync(SpeakerAuthListRequestDto request)
        {
            var result = new PagedResultDto<SpeakerAuthApplyDto>();
            var querySpeakerAuthApply = await LazyServiceProvider.LazyGetService<IOECSpeakerAuthApplyReadonlyRepository>().GetQueryableAsync();
            var query = querySpeakerAuthApply.WhereIf(!string.IsNullOrWhiteSpace(request.ApplicationCode), a => a.ApplicationCode.Contains(request.ApplicationCode))
                .WhereIf(!string.IsNullOrWhiteSpace(request.ApplyUserName), a => a.ApplyUserName.Contains(request.ApplyUserName))
                .WhereIf(!string.IsNullOrWhiteSpace(request.ApplicationDepartment), a => a.ApplyUserDeptName.Contains(request.ApplicationDepartment))
                //.WhereIf(!string.IsNullOrWhiteSpace(request.ApplyUserDeptName), a => a.ApplyUserDeptName.Contains(request.ApplyUserDeptName))
                .WhereIf(request.SubmitTimeStart.HasValue, a => a.SubmitTime >= request.SubmitTimeStart.Value.Date)
                .WhereIf(request.SubmitTimeEnd.HasValue, a => a.SubmitTime <= request.SubmitTimeEnd.Value.Date.AddDays(1))
                .WhereIf(request.StartTime.HasValue, a => a.StartTime >= request.StartTime.Value.Date)
                .WhereIf(request.EndTime.HasValue, a => a.EndTime <= request.EndTime.Value.Date.AddDays(1))
                .WhereIf(request.Status.HasValue, a => a.Status == request.Status);
            var personCenterService = LazyServiceProvider.LazyGetService<IPersonCenterService>();
            var rolesIds = await personCenterService.GetIdsByRoleLevels(bizTypeCategory: ResignationTransfer.TaskFormCategory.OECSpeakerAuthApplication);
            query = query.ApplyRoleFilters(rolesIds, CurrentUser.Id.Value, x => x.ApplyUserBu, x => x.ApplyUserDept, null, x => x.ApplyUserId, x => x.TransfereeId);
            query = query.OrderByDescending(a => a.CreationTime);
            var peakerAuthApplys = query.Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToList();
            result.TotalCount = query.Count();
            result.Items = ObjectMapper.Map<List<OECSpeakerAuthApply>, List<SpeakerAuthApplyDto>>(peakerAuthApplys);
            return result;
        }

        public async Task<PagedResultDto<SpeakerAuthApplyDto>> GetApprovalSpeakerAuthListAsync(SpeakerAuthListRequestDto request)
        {
            var result = new PagedResultDto<SpeakerAuthApplyDto>();
            var querySpeakerAuthApply = await LazyServiceProvider.LazyGetService<IOECSpeakerAuthApplyReadonlyRepository>().GetQueryableAsync();
            //待处理
            if (request.ProcessingStatus == ProcessingStatus.PendingProcessing)
            {
                var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
                //获取审批人节点
                var taskApprovelRecords = await dataverseService.GetApprovelTaskAsync(CurrentUser.Id.ToString(), [WorkflowTypeName.SpeakerAuthorization], (ProcessingStatus)request.ProcessingStatus);

                var datas = querySpeakerAuthApply.Where(a => taskApprovelRecords.Select(b => b.FormId).Contains(a.Id))
                    .WhereIf(!string.IsNullOrWhiteSpace(request.ApplicationCode), a => a.ApplicationCode.Contains(request.ApplicationCode))
                    .WhereIf(!string.IsNullOrWhiteSpace(request.ApplyUserName), a => a.ApplyUserName.Contains(request.ApplyUserName))
                    .WhereIf(!string.IsNullOrWhiteSpace(request.ApplyUserDeptName), a => request.ApplyUserDeptName.Contains(request.ApplyUserDeptName))
                    .WhereIf(request.SubmitStartTime.HasValue, a => a.SubmitTime >= request.SubmitStartTime)
                    .WhereIf(request.SubmitEndTime.HasValue, a => a.SubmitTime <= request.SubmitEndTime)
                    .ToArray();

                result.TotalCount = datas.Count();
                if (request.IsAsc)
                {
                    var peakerAuthApplys = datas.Join(taskApprovelRecords, a => a.Id, a => a.FormId, (a, b) => new { SpeakerAuth = a, Task = b })
                        .OrderBy(a => a.Task.CreatedTime)
                        .Select(a => a.SpeakerAuth)
                        .Skip(request.PageIndex * request.PageSize).Take(request.PageSize)
                        .ToList();

                    result.Items = ObjectMapper.Map<List<OECSpeakerAuthApply>, List<SpeakerAuthApplyDto>>(peakerAuthApplys);
                }
                else
                {
                    var peakerAuthApplys = datas.Join(taskApprovelRecords, a => a.Id, a => a.FormId, (a, b) => new { SpeakerAuth = a, Task = b })
                        .OrderByDescending(a => a.Task.CreatedTime)
                        .Select(a => a.SpeakerAuth)
                        .Skip(request.PageIndex * request.PageSize).Take(request.PageSize)
                        .ToList();

                    result.Items = ObjectMapper.Map<List<OECSpeakerAuthApply>, List<SpeakerAuthApplyDto>>(peakerAuthApplys);
                }

            }
            else//已完成
            {
                var queryWfTask = await LazyServiceProvider.LazyGetService<IWorkflowTaskReadonlyRepository>().GetQueryableAsync();
                var query = querySpeakerAuthApply
                    .Join(queryWfTask.Where(a => a.ApprovalId == CurrentUser.Id), a => a.Id, a => a.FormId, (a, b) => new { SpeakerAuth = a, Task = b })
                    .WhereIf(!string.IsNullOrWhiteSpace(request.ApplicationCode), a => a.SpeakerAuth.ApplicationCode.Contains(request.ApplicationCode))
                    .WhereIf(!string.IsNullOrWhiteSpace(request.ApplyUserName), a => a.SpeakerAuth.ApplyUserName.Contains(request.ApplyUserName))
                    .WhereIf(!string.IsNullOrWhiteSpace(request.ApplyUserDeptName), a => request.ApplyUserDeptName.Contains(request.ApplyUserDeptName))
                    .WhereIf(request.SubmitStartTime.HasValue, a => a.SpeakerAuth.SubmitTime >= request.SubmitStartTime)
                    .WhereIf(request.SubmitEndTime.HasValue, a => a.SpeakerAuth.SubmitTime <= request.SubmitEndTime)
                    .Select(a => new SpeakerAuthApplyDto
                    {
                        Id = a.SpeakerAuth.Id,
                        ApplicationCode = a.SpeakerAuth.ApplicationCode,
                        ApplyUserId = a.SpeakerAuth.ApplyUserId,
                        ApplyUserName = a.SpeakerAuth.ApplyUserName,
                        ApplyUserDept = a.SpeakerAuth.ApplyUserDept,
                        ApplyUserDeptName = a.SpeakerAuth.ApplyUserDeptName,
                        SpeakerQuantity = a.SpeakerAuth.SpeakerQuantity,
                        SubmitTime = a.SpeakerAuth.SubmitTime.ToString("yyyy-MM-dd"),
                        StartTime = a.SpeakerAuth.StartTime.ToString("yyyy-MM-dd"),
                        EndTime = a.SpeakerAuth.EndTime.ToString("yyyy-MM-dd"),
                        Status = a.SpeakerAuth.Status,
                        OECApprovalNo = a.SpeakerAuth.OECApprovalNo,
                        ApprovalUserId = a.SpeakerAuth.ApprovalUserId,
                        Remark = a.SpeakerAuth.Remark,
                        ApprovalTime = a.Task.ApprovalTime
                    });

                result.TotalCount = query.Count();
                if (request.IsAsc)
                    result.Items = query.OrderBy(a => a.ApprovalTime).Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToArray();
                else
                    result.Items = query.OrderByDescending(a => a.ApprovalTime).Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToArray();
            }

            return result;
        }

        public async Task<PagedResultDto<SpeakerAuthApplyDto>> GetInitiateSpeakerAuthListAsync(SpeakerAuthListRequestDto request)
        {
            var result = new PagedResultDto<SpeakerAuthApplyDto>();
            //任务中心 状态 
            SpeakerAuthStatus[] pending = [SpeakerAuthStatus.Return];//待处理
            SpeakerAuthStatus[] progressings = [SpeakerAuthStatus.Approvaling];//进行中
            SpeakerAuthStatus[] completed = [SpeakerAuthStatus.Rejected, SpeakerAuthStatus.ToBeActivated, SpeakerAuthStatus.Activated, SpeakerAuthStatus.Invalid];//已完成
            //获取代理信息
            var agents = await LazyServiceProvider.LazyGetService<IAgencyService>().GetOriginalOperators(new GetAgentOperatorsRequestDto { BusinessTypeCategory = ResignationTransfer.TaskFormCategory.OECSpeakerAuthApplication });
            var userIds = agents.Select(a => a.Key).Distinct().ToArray();
            var principalIds = userIds.Where(x => x != CurrentUser.Id.Value).ToArray();
            var querySpeakerAuthApply = await LazyServiceProvider.LazyGetService<IOECSpeakerAuthApplyReadonlyRepository>().GetQueryableAsync();
            var query = querySpeakerAuthApply
                .Where(a => (a.ApplyUserId == CurrentUser.Id.Value && !a.TransfereeId.HasValue) ||//申请人为自己且无转办人
                            CurrentUser.Id.Value == a.TransfereeId.Value || //转办人为自己
                            principalIds.ToHashSet().Contains(a.ApplyUserId))//代理人为自己
                .WhereIf(ProcessingStatus.PendingProcessing.Equals(request.ProcessingStatus), a => pending.Contains(a.Status))
                .WhereIf(ProcessingStatus.Progressing.Equals(request.ProcessingStatus), a => progressings.Contains(a.Status))
                .WhereIf(ProcessingStatus.Completed.Equals(request.ProcessingStatus), a => completed.Contains(a.Status))
                .WhereIf(!string.IsNullOrWhiteSpace(request.ApplicationCode), a => a.ApplicationCode.Contains(request.ApplicationCode))
                .WhereIf(!string.IsNullOrWhiteSpace(request.ApplyUserName), a => a.ApplyUserName.Contains(request.ApplyUserName))
                .WhereIf(!string.IsNullOrWhiteSpace(request.ApplyUserDeptName), a => request.ApplyUserDeptName.Contains(request.ApplyUserDeptName))
                .WhereIf(request.SubmitStartTime.HasValue, a => a.SubmitTime >= request.SubmitStartTime)
                .WhereIf(request.SubmitEndTime.HasValue, a => a.SubmitTime <= request.SubmitEndTime);

            result.TotalCount = query.Count();
            var peakerAuthApplys = query.OrderByDescending(a => a.CreationTime).Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToList();

            result.Items = ObjectMapper.Map<List<OECSpeakerAuthApply>, List<SpeakerAuthApplyDto>>(peakerAuthApplys);
            return result;
        }

        public async Task<MessageResult> ImportSpeakerAsync(List<SpeakerAuthUserDto> speakers)
        {
            var queryVendor = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();
            var queryVendorPersonal = await LazyServiceProvider.LazyGetService<IVendorPersonalRepository>().GetQueryableAsync();
            var speakerAuthUserErrors = new ImportSpeakerAuthApplyUsersDto();
            var speakerAuthUsers = new ImportSpeakerAuthApplyUsersDto();
            var vendorCodes = speakers.Where(b => !string.IsNullOrWhiteSpace(b.VendorCode)).Select(b => b.VendorCode).ToList();
            var vendorPersonals = queryVendor.GroupJoin(queryVendorPersonal, a => a.Id, b => b.VendorId, (a, b) => new { vendor = a, personals = b })
                .SelectMany(a => a.personals.DefaultIfEmpty(), (a, b) => new { a.vendor, personal = b })
                .Where(a => vendorCodes.Contains(a.vendor.VendorCode))
                .ToList();
            foreach (var item in speakers)
            {
                if (string.IsNullOrWhiteSpace(item.VendorCode) || string.IsNullOrWhiteSpace(item.Content))
                {
                    speakerAuthUserErrors.SpeakerAuthApplyUsers.Add(new SpeakerAuthApplyUsersDto
                    {
                        VendorCode = item.VendorCode,
                        Content = item.Content,
                        LineNo = item.LineNo,
                        ErrorMsg = SpeakerAuthApplyErrorMsg.UnfilledErrorMsg,
                    });
                    continue;
                }
                if (item.Content.Length > 100)
                {
                    speakerAuthUserErrors.SpeakerAuthApplyUsers.Add(new SpeakerAuthApplyUsersDto
                    {
                        VendorCode = item.VendorCode,
                        Content = item.Content,
                        LineNo = item.LineNo,
                        ErrorMsg = SpeakerAuthApplyErrorMsg.ExtraLongErrorMsg,
                    });
                    continue;
                }
                var vendorPersonal = vendorPersonals.Where(a => a.vendor.VendorCode == item.VendorCode).FirstOrDefault();
                if (vendorPersonal == null)
                {
                    speakerAuthUserErrors.SpeakerAuthApplyUsers.Add(new SpeakerAuthApplyUsersDto
                    {
                        VendorCode = item.VendorCode,
                        Content = item.Content,
                        LineNo = item.LineNo,
                        ErrorMsg = SpeakerAuthApplyErrorMsg.MismatchErrorMsg,
                    });
                }
                else
                {
                    speakerAuthUsers.SpeakerAuthApplyUsers.Add(new SpeakerAuthApplyUsersDto
                    {
                        SpeakerName = vendorPersonal.personal?.SPName,
                        VendorCode = item.VendorCode,
                        PTName = vendorPersonal.vendor.PTName,
                        HospitalName = vendorPersonal.vendor.HospitalName,
                        StandardHosDepName = vendorPersonal.vendor.StandardHosDepName,
                        HosDepartment = vendorPersonal.vendor.HosDepartment,
                        SPLevel = vendorPersonal.vendor.SPLevel,
                        Content = item.Content,
                        LineNo = item.LineNo,
                    });
                }
            }
            if (speakerAuthUserErrors.SpeakerAuthApplyUsers.Any())
                return MessageResult.SuccessResult(speakerAuthUserErrors);
            speakerAuthUsers.Success = true;
            return MessageResult.SuccessResult(speakerAuthUsers);
        }

        public async Task<MessageResult> SubmitSpeakerAuthApplyAsync(SubmitSpeakerAuthApplyDto request)
        {
            var speakerAuthRepository = LazyServiceProvider.LazyGetService<IOECSpeakerAuthApplyRepository>();
            var speakerAuthUserRepository = LazyServiceProvider.LazyGetService<IOECSpeakerAuthApplyUserRepository>();
            var commonService = LazyServiceProvider.LazyGetService<ICommonService>();
            var orgs = await commonService.GetAllParentOrgs(request.SpeakerAuthApply.ApplyUserDept);
            OECSpeakerAuthApply insertSpeakerAuthApply = null;
            if (request.Id.HasValue)
            {
                //重新提交
                insertSpeakerAuthApply = await speakerAuthRepository.GetAsync(request.Id.Value);
                if (insertSpeakerAuthApply == null)
                    return MessageResult.FailureResult("未查询到相关授权申请");

                if (insertSpeakerAuthApply.Status != SpeakerAuthStatus.Return)
                    return MessageResult.FailureResult("数据状态不是退回/撤回，不能重新提交");

                await speakerAuthUserRepository.DeleteAsync(a => a.ApplyId == insertSpeakerAuthApply.Id);//删除该申请中的讲者数据
                ObjectMapper.Map(request.SpeakerAuthApply, insertSpeakerAuthApply);
                insertSpeakerAuthApply.SpeakerQuantity = request.Speakers.Count;
                insertSpeakerAuthApply.Status = Enums.SpeakerAuthStatus.Approvaling;
                insertSpeakerAuthApply.StartTime = DateTime.Now;
                insertSpeakerAuthApply.ApplyUserBu = orgs.FirstOrDefault(a => a.OrganizationType == DataverseEnums.Organization.OrganizationType.Bu).Id;
                insertSpeakerAuthApply.ApplyUserBuName = orgs.FirstOrDefault(a => a.OrganizationType == DataverseEnums.Organization.OrganizationType.Bu).DepartmentName;
                await speakerAuthRepository.UpdateAsync(insertSpeakerAuthApply);
            }
            else
            {
                //新增
                var speakerAuthApply = ObjectMapper.Map<CreateSpeakerAuthApplyDto, OECSpeakerAuthApply>(request.SpeakerAuthApply);
                speakerAuthApply.SpeakerQuantity = request.Speakers.Count;
                speakerAuthApply.Status = Enums.SpeakerAuthStatus.Approvaling;
                speakerAuthApply.ApplyUserBu = orgs.FirstOrDefault(a => a.OrganizationType == DataverseEnums.Organization.OrganizationType.Bu).Id;
                speakerAuthApply.ApplyUserBuName = orgs.FirstOrDefault(a => a.OrganizationType == DataverseEnums.Organization.OrganizationType.Bu).DepartmentName;

                await InsertAndGenerateSerialNoAsync(speakerAuthRepository, speakerAuthApply, "SA");
                insertSpeakerAuthApply = speakerAuthApply;
            }
            var speakers = new List<OECSpeakerAuthApplyUser>();
            foreach (var item in request.Speakers)
            {
                speakers.Add(new OECSpeakerAuthApplyUser
                {
                    ApplyId = insertSpeakerAuthApply.Id,
                    VendorCode = item.VendorCode,
                    Content = item.Content,
                });
            }
            await speakerAuthUserRepository.InsertManyAsync(speakers);

            var userId = CurrentUser.Id.Value;
            var createWorkflow = await CreateWorkflowAsync(insertSpeakerAuthApply, userId, insertSpeakerAuthApply.ApprovalUserId);
            if (!createWorkflow)
            {
                return MessageResult.FailureResult("审批流创建失败");
            }
            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 获取下一季度日期区间
        /// </summary>
        /// <returns></returns>
        public QuarterDateTimeDto GetNextQuarterDates()
        {
            DateTime now = DateTime.Now;
            int currentMonth = now.Month;
            int nextQuarterStartMonth = 0;
            if (currentMonth <= 3)
            {
                nextQuarterStartMonth = 4;
            }
            else if (currentMonth <= 6)
            {
                nextQuarterStartMonth = 7;
            }
            else if (currentMonth <= 9)
            {
                nextQuarterStartMonth = 10;
            }
            else
            {
                // 如果日期是第四季度，则下一季度从下一年开始
                nextQuarterStartMonth = 1;
                now = now.AddYears(1);
            }
            DateTime startDate = new DateTime(now.Year, nextQuarterStartMonth, 1);
            DateTime endDate = startDate.AddMonths(3).AddDays(-1);//获取季度结束时间（3个月一个季度）
            return new QuarterDateTimeDto { StartTime = startDate.ToString("yyyy-MM-dd"), EndTime = endDate.ToString("yyyy-MM-dd") };
        }

        public async Task<List<SpeakerAuthApplyUsersDto>> GetSpeakerByDepartmentAsync(Guid orgId)
        {
            var speakerAuthUserRepository = LazyServiceProvider.LazyGetService<IOECSpeakerAuthApplyUserRepository>();
            var querySpeakerAuthUser = await speakerAuthUserRepository.GetQueryableAsync();
            var queryVendor = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();
            var queryVendorPersonal = await LazyServiceProvider.LazyGetService<IVendorPersonalRepository>().GetQueryableAsync();
            var querySpeakerAuthApply = await LazyServiceProvider.LazyGetService<IOECSpeakerAuthApplyRepository>().GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var commonService = LazyServiceProvider.LazyGetService<ICommonService>();
            var org = await dataverseService.GetOrganizations(orgId.ToString());
            var orgs = await commonService.GetChildrenOrgs(org.FirstOrDefault());
            var orgIds = orgs.Select(a => a.Id).ToList();
            DateTime nowTime = DateTime.Now;
            var query = querySpeakerAuthApply.GroupJoin(querySpeakerAuthUser, a => a.Id, b => b.ApplyId, (a, b) => new { auth = a, saus = b })
                .SelectMany(a => a.saus.DefaultIfEmpty(), (a, b) => new { a.auth, sau = b })
                .GroupJoin(queryVendor, a => a.sau.VendorCode, b => b.VendorCode, (a, b) => new { a.auth, a.sau, vendors = b })
                .SelectMany(a => a.vendors.DefaultIfEmpty(), (a, b) => new { a.auth, a.sau, vendor = b })
                .GroupJoin(queryVendorPersonal, a => a.vendor.Id, b => b.VendorId, (a, b) => new { a.auth, a.sau, a.vendor, personals = b })
                .SelectMany(a => a.personals.DefaultIfEmpty(), (a, b) => new { a.auth, a.sau, a.vendor, personal = b })
                .Where(a => orgIds.Contains(a.auth.ApplyUserDept) && a.auth.Status == SpeakerAuthStatus.Activated)
                .Where(a => a.auth.StartTime <= nowTime && a.auth.EndTime >= nowTime);

            var speakerAuthUsers = query.Select(a => new SpeakerAuthApplyUsersDto
            {
                SpeakerName = a.personal.SPName,
                VendorCode = a.sau.VendorCode,
                PTName = a.vendor.PTName,
                HospitalName = a.vendor.HospitalName,
                StandardHosDepName = a.vendor.StandardHosDepName,
                HosDepartment = a.vendor.HosDepartment,
                SPLevel = a.vendor.SPLevel,
                Content = a.sau.Content,
            })
            .ToList();
            return speakerAuthUsers;
        }

        #region 私有方法
        /// <summary>
        ///  添加审批任务
        /// </summary>
        /// <param name="request"></param>
        /// <param name="submitter"></param>
        /// <param name="firstApprover"></param>
        /// <returns></returns>
        private async Task<bool> CreateWorkflowAsync(OECSpeakerAuthApply request, Guid submitter, Guid firstApprover)
        {
            var approvalRecord = LazyServiceProvider.LazyGetService<IInitializeApprovalRecordService>();
            //创建审批任务
            var exemptType = EnumUtil.GetEnumIdValues<WorkflowTypeName>().ToDictionary(a => (WorkflowTypeName)a.Key, a => a.Value);
            var name = exemptType[WorkflowTypeName.SpeakerAuthorization];
            var createApproval = new CreateApprovalDto
            {
                Name = name,
                Department = request.ApplyUserDept.ToString(),
                BusinessFormId = request.Id.ToString(),
                BusinessFormNo = request.ApplicationCode,
                BusinessFormName = EntitiesNameConsts.NameConsts.OECSpeakerAuthApply,
                Submitter = submitter.ToString(),
                OriginalApprovalId = request.ApplyUserId,
                WorkflowType = WorkflowTypeName.SpeakerAuthorization,
                FirstApprover = firstApprover.ToString(),
                InstanceName = $"{name}-{DateTime.Now.ToString("yyMMdd-hhmmss")}",
                Remark = request.Remark
            };

            await approvalRecord.InsertInitApprovalRecordAsync(new InitializeApprovalRecordDto
            {
                BusinessFormName = EntitiesNameConsts.NameConsts.OECSpeakerAuthApply,
                BusinessId = request.Id,
                InstanceId = Guid.NewGuid(),
                Status = InitApprovalRecordStatus.Pending,
                MaxRetries = 3,
                RequestContent = JsonSerializer.Serialize(createApproval)
            });
            return true;
        }
        #endregion
    }
}
