﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

using Abbott.SpeakerPortal.User;

using Hangfire;

namespace Abbott.SpeakerPortal.BackgroundWorkers.System
{
    public class SyncUserLastLoginTimeWorker : SpeakerPortalBackgroundWorkerBase
    {
        public SyncUserLastLoginTimeWorker()
        {
            //每5分钟同步一次
            CronExpression = "0 0/5 * * * ?";
        }

        public override async Task DoWorkAsync(CancellationToken cancellationToken = default)
        {
            await LazyServiceProvider.LazyGetService<IUserService>().UpsertUserLastLoginTime();
        }
    }
}
