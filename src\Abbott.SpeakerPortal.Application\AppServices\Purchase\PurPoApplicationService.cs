using Abbott.SpeakerPortal.AppServices.Common;
using Abbott.SpeakerPortal.BackgroundWorkers.BPMEmail;
using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Agent;
using Abbott.SpeakerPortal.Contracts.Approval;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.Approve;
using Abbott.SpeakerPortal.Contracts.Common.Attachment;
using Abbott.SpeakerPortal.Contracts.Common.BPMEmail;
using Abbott.SpeakerPortal.Contracts.Common.Common;
using Abbott.SpeakerPortal.Contracts.Common.PowerApp;
using Abbott.SpeakerPortal.Contracts.Dataverse;
using Abbott.SpeakerPortal.Contracts.Purchase.PurGRApplication;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPOApplication;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.Budget;
using Abbott.SpeakerPortal.Entities.Common.Attachment;
using Abbott.SpeakerPortal.Entities.Common.Workflow.CurrentApprovalTask;
using Abbott.SpeakerPortal.Entities.Common.Workflow.WorkflowTasks;
using Abbott.SpeakerPortal.Entities.Integration.Dspot;
using Abbott.SpeakerPortal.Entities.Purchase.PurBDApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurBWApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurCategoryConfig;
using Abbott.SpeakerPortal.Entities.Purchase.PurPOApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Entities.VendorFinancials;
using Abbott.SpeakerPortal.Entities.VendorOrgnizations;
using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Person;
using Abbott.SpeakerPortal.Utils;
using Abbott.SpeakerPortal.Extension;
using Hangfire;

using Microsoft.Crm.Sdk.Messages;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MiniExcelLibs;

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;

using static Abbott.SpeakerPortal.Enums.Purchase;
using static Abbott.SpeakerPortal.Permissions.SpeakerPortalPermissions;

using IConfiguration = Microsoft.Extensions.Configuration.IConfiguration;
using Abbott.SpeakerPortal.Entities.User;
using static OpenIddict.Abstractions.OpenIddictConstants;
using Npgsql.Internal.Postgres;
using Abbott.SpeakerPortal.IntermediateDB.Models;
using Abbott.SpeakerPortal.Entities.MSA;
using Abbott.SpeakerPortal.Contracts.MSA;

namespace Abbott.SpeakerPortal.AppServices.Purchase
{
    public partial class PurPOApplicationService : SpeakerPortalAppService, IPurPOApplicationService
    {
        private const string APS_PROPERTY = "APS供应商";
        private const string UN_APS_PROPERTY = "非APS供应商";
        private readonly IServiceProvider _serviceProvider;
        private readonly IConfiguration _configuration;
        private readonly ILogger<PurPOApplicationService> _logger;
        public PurPOApplicationService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _configuration = _serviceProvider.GetService<IConfiguration>();
            _logger = serviceProvider.GetService<ILogger<PurPOApplicationService>>();
        }
        /// <summary>
        /// 获取采购申请明细
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task<IEnumerable<GetPRApplicationDetailResponse>> GetPRApplicationDetailByIdAsync(List<Guid> ids)
        {
            var queryPr = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var queryPrDetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var queryOrg = await LazyServiceProvider.LazyGetService<IVendorOrgnizationRepository>().GetQueryableAsync();
            var queryBW = await LazyServiceProvider.LazyGetService<IPurBWApplicationRepository>().GetQueryableAsync();
            var queryBD = await LazyServiceProvider.LazyGetService<IPurBDApplicationRepository>().GetQueryableAsync();
            var queryBdDetail = await LazyServiceProvider.LazyGetService<IPurBDApplicationDetailsRepository>().GetQueryableAsync();
            var prDetails = from a in queryPrDetail
                            where ids.Contains(a.Id)
                            join b in queryOrg on a.VendorId equals b.VendorId into ab
                            from b in ab.DefaultIfEmpty()
                            join c in queryPr on a.PRApplicationId equals c.Id into ac
                            from c in ac.DefaultIfEmpty()
                            join d in queryBW on a.BWApplicationId equals d.Id into ad
                            from d in ad.DefaultIfEmpty()
                            select new
                            {
                                PrDetail = a,
                                Org = b,
                                Pr = c,
                                bw = d,
                            };

            var details = await prDetails.ToListAsync();

            var bds = queryBD.Select(a => new { a.Id, a.ApplicationCode })
               .GroupJoin(queryBdDetail.Select(a => new { a.BDApplicationId, a.PRDetailId }), a => a.Id, b => b.BDApplicationId, (a, b) => new { bd = a, bdds = b })
               .SelectMany(a => a.bdds.DefaultIfEmpty(), (a, b) => new { a.bd, bdd = b })
               .Where(a => a.bdd.PRDetailId.HasValue && details.Select(b => b.PrDetail.Id).Contains(a.bdd.PRDetailId.Value))
               .ToList();

            var resultList = new List<GetPRApplicationDetailResponse>();
            foreach (var item in details)
            {
                var detail = ObjectMapper.Map<PurPRApplicationDetail, GetPRApplicationDetailResponse>(item.PrDetail);
                detail.PRId = item.Pr.Id;
                detail.PRDetailId = item.PrDetail.Id;
                detail.EstimateDate = item.PrDetail.EstimateDate?.ToString("yyyy-MM-dd");
                detail.VendorIdName = item.PrDetail.VendorName;
                detail.ApplicationCode = item.Pr.ApplicationCode;
                detail.CompanyId = item.Pr.CompanyId;
                detail.CompanyName = item.Pr.CompanyIdName;
                detail.Applicant = item.Pr.ApplyUserIdName;
                detail.ApplicationBu = item.Pr.ApplyUserBuName;
                detail.ApplyUserId = item.Pr.ApplyUserId;
                detail.ApplyUserName = item.Pr.ApplyUserIdName;
                detail.ApplyUserBu = item.Pr.ApplyUserBu;
                detail.ApplyUserBuName = item.Pr.ApplyUserBuName;
                detail.ApplyUserDept = item.Pr.ApplyUserDept;
                detail.ApplyUserBuToDeptName = item.Pr.ApplyUserDeptName;
                detail.BWId = item.bw?.Id;
                detail.BWApplicationCode = item.bw?.ApplicationCode;
                detail.ExemptType = item.bw?.ExemptType;
                detail.BDId = bds.FirstOrDefault(a => a.bdd.PRDetailId == item.PrDetail.Id)?.bd?.Id;
                detail.BDApplicationCode = bds.FirstOrDefault(a => a.bdd.PRDetailId == item.PrDetail.Id)?.bd?.ApplicationCode;
                resultList.Add(detail);
            }
            return resultList;
        }

        /// <summary>
        /// 单据金额是否超过waiver限额
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task<MessageResult> IsLimitedQuotaAsync(List<Guid> ids)
        {
            var queryVendor = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();
            var queryFinancial = await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync();
            var queryablePRdetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var queryBpcsGcc = await LazyServiceProvider.LazyGetService<IBpcsGccRepository>().GetQueryableAsync();
            var queryBpcsAvm = await LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync();
            var queryBD = await LazyServiceProvider.LazyGetService<IPurBDApplicationRepository>().GetQueryableAsync();
            var queryPR = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var prDetails = queryablePRdetail.Where(a => ids.Contains(a.Id)).ToList();
            if (!prDetails.Any())
            {
                return MessageResult.FailureResult("请选择正确的PR明细列表");
            }
            if (!prDetails.All(a => a.PRApplicationId == prDetails[0].PRApplicationId)) //PR ID 是否相同
            {
                return MessageResult.FailureResult("请选PR单号相同数据");
            }
            var isHasValid = IsHasValidBiddingAndJW(prDetails);
            // 竞价豁免（J/W ）能走到这说明已经都审批过了、PR推送（时做过校验）
            //PR推送时 选的线下（J/W ）也算审批过了
            if (isHasValid.Item1 == 0)
            {
                return MessageResult.FailureResult(isHasValid.Item2);
            }
            else if (isHasValid.Item1 == 1)
            {
                return MessageResult.SuccessResult();
            }
            var vendorId = prDetails.Where(a => a.VendorId.HasValue == true).FirstOrDefault()?.VendorId;
            var vendor = queryBpcsAvm.Where(a => a.Id == vendorId)
              .Join(queryFinancial, a => a.FinaId, a => a.Id, (a, b) => new { Avm = a, Fin = b })
              .Join(queryVendor, a => a.Fin.VendorId, a => a.Id, (a, b) => new { a.Avm, Vendor = b })
              .FirstOrDefault();
            bool isAPS = !string.IsNullOrWhiteSpace(vendor?.Vendor?.ApsPorperty);
            if (IsExceedInitiateAmount(dataverseService, isAPS, prDetails.Sum(a => a.TotalAmountRMB)))
            {
                var apsAmountStr = isAPS ? "$50,000" : "$30,000";
                //无Waiver或Bidding流程时验证
                return MessageResult.SuccessResult(new
                {
                    Title = $"订单人民币金额大于等于waiver金额控制({apsAmountStr})",
                    Description = "所选PR行人民币金额大于等于Waiver金额，必须发起Waiver或Bidding流程"
                });
            }
            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// PO保存
        /// </summary>
        /// <param name="pOCreateRequest"></param>
        /// <returns></returns>
        public async Task<MessageResult> SavePurPoApplicationAsync(POCreateOrUpdateRequestDto pOCreateRequest)
        {
            var prDetailIds = pOCreateRequest.PRApplicationDetailId.Split(',').Select(Guid.Parse).ToList();
            //设置PrId
            if (prDetailIds.Count > 0)
            {
                var queryablePRDetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
                var prDetails = queryablePRDetail.Where(a => prDetailIds.Contains(a.Id)).ToArray();
                var rate = (await LazyServiceProvider.LazyGetService<IDataverseService>().GetCurrencyConfig(DictionaryType.CurrencyItems.USD)).FirstOrDefault();

                pOCreateRequest.TotalAmountTax = pOCreateRequest.POApplicationDetails.Sum(a => a.TotalAmount);
                pOCreateRequest.TotalAmount = pOCreateRequest.POApplicationDetails.Sum(a =>
                {
                    return InvoiceType.GiftIncrease.Equals(a.InvoiceType) ? a.TotalAmount : a.TotalAmountNoTax;//实际计算不含税金额时，对于礼品增票需要视为0
                });

                pOCreateRequest.PRId = prDetails.FirstOrDefault()?.PRApplicationId;//PR主表ID
            }

            PurPOApplication poApplication;
            var querypo = await LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>().GetQueryableAsync();
            if (pOCreateRequest.POId.HasValue)
            {
                var po = await querypo.FirstOrDefaultAsync(a => a.Id == pOCreateRequest.POId.Value);
                if (po == null)
                    return MessageResult.FailureResult("数据不存在");

                PurOrderStatus[] editableStatus = [PurOrderStatus.Draft, PurOrderStatus.Return, PurOrderStatus.Withdraw];
                if (!editableStatus.Contains(po.Status))
                    return MessageResult.FailureResult("数据状态不是草稿/退回/撤回，不能修改/提交");

                poApplication = await UpdatePurPOApplication(pOCreateRequest);
            }
            else
            {
                if (!await CheckIsResubmitAsync(prDetailIds, false))
                    return MessageResult.FailureResult("已存在相关采购订单或草稿");
                poApplication = await CreatePurPOApplication(pOCreateRequest);
            }
            if (poApplication == null)
                return MessageResult.FailureResult("保存失败，稍后再试");
            return MessageResult.SuccessResult(poApplication.Id);
        }
        /// <summary>
        /// PO提交
        /// </summary>
        /// <param name="pOCreateRequest"></param>
        /// <returns></returns>
        public async Task<MessageResult> SubmitPurPoApplicationAsync(POCreateOrUpdateRequestDto pOCreateRequest)
        {
            var prDetailRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var queryablePRDetail = await prDetailRepository.GetQueryableAsync();
            //提交时做校验
            var checkFields = await CheckCreateRequiredFieldsAsync(pOCreateRequest);
            if (!checkFields.Item1)
            {
                return MessageResult.FailureResult(checkFields.Item2);
            }
            pOCreateRequest.ApplyTime = DateTime.Now;
            var checkPRAndPOMatch = await CheckPRAndPOMatchAsync(pOCreateRequest);
            if (!checkPRAndPOMatch.Item1)
            {
                return MessageResult.FailureResult(checkPRAndPOMatch.Item2);
            }
            var prDetailIds = pOCreateRequest.PRApplicationDetailId.Split(',').Select(Guid.Parse).ToList();
            var prDetails = queryablePRDetail.Where(a => prDetailIds.Contains(a.Id)).ToList();
            var rate = (await dataverseService.GetCurrencyConfig(DictionaryType.CurrencyItems.USD)).FirstOrDefault();

            pOCreateRequest.TotalAmountTax = pOCreateRequest.POApplicationDetails.Sum(a => a.TotalAmount);
            pOCreateRequest.TotalAmount = pOCreateRequest.POApplicationDetails.Sum(a =>
            {
                return InvoiceType.GiftIncrease.Equals(a.InvoiceType) ? a.TotalAmount : a.TotalAmountNoTax;//实际计算不含税金额时，对于礼品增票需要视为0
            });
            var poTotalAmountRMB = pOCreateRequest.TotalAmountTax * pOCreateRequest.ExchangeRate;
            //zhx20240726：判断是否金额超限，是则必须要PO的所有PRDetail有Bidding 或 审批通过的豁免单（审批通过后又作废的也不行），才能转PO
            if (IsExceedInitiateAmount(dataverseService, pOCreateRequest.VendorPorperty, poTotalAmountRMB))
            {//超限额才进入
                var isHasValid = IsHasValidBiddingAndJW(prDetails);
                // 竞价豁免（J/W ）能走到这说明已经都审批过了、PR推送（时做过校验）
                //PR推送时 选的线下（J/W ）也算审批过了
                if (isHasValid.Item1 == 0)
                {
                    return MessageResult.FailureResult(isHasValid.Item2);
                }
                else if (isHasValid.Item1 == 2 && pOCreateRequest.VendorPorperty == false) //被改APS为不适用的情况下
                {
                    return MessageResult.FailureResult("所选PR行人民币金额大于等于Waiver金额，必须发起Waiver或Bidding流程");
                }
            }

            //校验Po与Msa和Sow的关系
            var messageResult = await CheckPoMsaSow(pOCreateRequest);
            if (!messageResult.Success)
                return messageResult;

            var poRepository = LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>();
            var querypo = await poRepository.GetQueryableAsync();
            pOCreateRequest.PRId = prDetails.FirstOrDefault()?.PRApplicationId;//PR主表ID
            PurPOApplication poApplication;
            if (pOCreateRequest.POId.HasValue && querypo.Any(a => a.Id == pOCreateRequest.POId.Value))
            {
                var po = await querypo.FirstOrDefaultAsync(a => a.Id == pOCreateRequest.POId.Value);
                if (po == null)
                    return MessageResult.FailureResult("数据不存在");

                PurOrderStatus[] editableStatus = [PurOrderStatus.Draft, PurOrderStatus.Return, PurOrderStatus.Withdraw];
                if (!editableStatus.Contains(po.Status))
                    return MessageResult.FailureResult("数据状态不是草稿/退回/撤回，不能修改/提交");

                poApplication = await UpdatePurPOApplication(pOCreateRequest);
            }
            else
            {
                if (!await CheckIsResubmitAsync(prDetailIds))
                    return MessageResult.FailureResult("已存在相关采购订单或草稿");
                poApplication = await CreatePurPOApplication(pOCreateRequest);
            }
            if (poApplication == null)
                return MessageResult.FailureResult("保存失败，稍后再试");
            prDetails.ForEach(a =>
            {
                a.OrderStatusFlag = OrderStatusFlag.InitiateOrder;
            });
            poApplication.FirstLevelApprover = await GetFirstLevelApprover(dataverseService, poApplication.ApplyUserDept, poApplication.ApplyUserId);
            if (PurOrderType.ProformaOrder.Equals(pOCreateRequest.POType))
            {
                //形式订单直接创建收货申请
                var grApplicationService = LazyServiceProvider.LazyGetService<IPurGRApplicationService>();
                var po = new PurPOApplicationDto();
                po.Id = poApplication.Id;
                po.VendorId = poApplication.VendorId;
                po.ApplicationCode = poApplication.ApplicationCode;
                var createGr = await grApplicationService.CreateGRApplication(prDetailIds, po);
                ///GR创建成功后、变更订单状态(发起收货)
                if (createGr.Success)
                {
                    //2328 ytw 20241118 形式订单在提交的时候需要补充PurPOApplications.ApprovedDate，取提交成功的时间
                    poApplication.ApprovedDate = poApplication.ApplyTime;
                    poApplication.Status = PurOrderStatus.Closed;
                    await poRepository.UpdateAsync(poApplication);
                    await prDetailRepository.UpdateManyAsync(prDetails);
                    await POBudgetRefundAsync(poApplication);//预算返还
                }

                return createGr;
            }
            var totalAmountUsd = (poApplication.TotalAmountTax * (decimal)poApplication.ExchangeRate) / rate.PlanRate;//转人民币后转美元
            var createOK = await CreatePOWorkflowAsync(poApplication.ApplyUserDept, poApplication.ApplyUserId, poApplication.Id, poApplication.ApplicationCode, totalAmountUsd, poApplication.Remark);
            if (!createOK)
            {
                poApplication.Status = PurOrderStatus.Draft;//审批任务失败则将订单状态改为草稿
                await poRepository.UpdateAsync(poApplication);
                return MessageResult.FailureResult("审批任务创建失败");
            }
            poApplication.Status = PurOrderStatus.Approving;//创建成功后更新状态
            await poRepository.UpdateAsync(poApplication);
            await prDetailRepository.UpdateManyAsync(prDetails);
            return MessageResult.SuccessResult();
        }
        /// <summary>
        /// 我的采购推送列表
        /// </summary>
        /// <param name="request"></param>
        /// <param name="isPaging"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetPRApplicationDetailListReponse>> GetProcurementPushListAsync(GetPRApplicationDetailListRequest request, bool isPaging = true)
        {
            var sortType = request.SortOrder == "descending" ? "desc" : "asc";
            //var sortName = request.SortBy?.ToLower() == "PushTime" ? "PushTime" : "ApplyTime";
            if (string.IsNullOrWhiteSpace(request.SortBy)) request.SortBy = "pushTime";
            var sort = $"{request.SortBy} {sortType}";
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var queryablePRdetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var queryablePR = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var vendorOrg = await LazyServiceProvider.LazyGetService<IVendorOrgnizationRepository>().GetQueryableAsync();
            //po
            var queryablePO = await LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>().GetQueryableAsync();
            var queryablePodetail = await LazyServiceProvider.LazyGetService<IPurPOApplicationDetailsRepository>().GetQueryableAsync();
            //Bidding
            var queryableBidding = await LazyServiceProvider.LazyGetService<IPurBDApplicationRepository>().GetQueryableAsync();
            //BW
            var queryBw = await LazyServiceProvider.LazyGetService<IPurBWApplicationRepository>().GetQueryableAsync();
            var queryableUser = await LazyServiceProvider.LazyGetService<IIdentityUserRepository>().GetQueryableAsync();
            //采购推送公司
            var companyIds = (await dataverseService.GetProcurementPushConfigAsync(CurrentUser.Id.ToString(), null)).Select(s => s.CompanyId).ToList();
            if (request.PushFlag != IsPushPO.NotPushed && request.PushFlag != IsPushPO.Pushed)
                return default;

            OrderStatusFlag[] orderStatusNotPushed = [OrderStatusFlag.OrderReject, OrderStatusFlag.BiddingReject, OrderStatusFlag.BiddingCompleted, OrderStatusFlag.Bidding];
            OrderStatusFlag[] orderStatusPushed = [OrderStatusFlag.InitiateOrder, OrderStatusFlag.OrderCompleted];

            var queryPrd = queryablePRdetail.AsNoTracking().WhereIf(request.PushFlag == IsPushPO.NotPushed, a => a.PayMethod == PayMethods.AP && a.PushFlag == PushFlagEnum.Pushed && (a.OrderStatusFlag == null || orderStatusNotPushed.Contains(a.OrderStatusFlag.Value)))
                .WhereIf(request.PushFlag == IsPushPO.Pushed, a => a.PayMethod == PayMethods.AP && a.PushFlag == PushFlagEnum.Pushed && (a.OrderStatusFlag == null || orderStatusPushed.Contains(a.OrderStatusFlag.Value)))
                .Select(a => new ProcurementPushPRDetailDto
                {
                    Id = a.Id,
                    PRApplicationId = a.PRApplicationId,
                    BiddingId = a.BiddingId,
                    PurchaserId = a.PurchaserId,
                    BWApplicationId = a.BWApplicationId,
                    Content = a.Content,
                    VendorName = a.VendorName,
                    OrderStatusFlag = a.OrderStatusFlag,
                    EstimateDate = a.EstimateDate,
                    Quantity = a.Quantity,
                    UnitPrice = a.UnitPrice,
                    TotalAmount = a.TotalAmount,
                    TotalAmountRMB = a.TotalAmountRMB,
                    RceNo = a.RceNo,
                    VendorId = a.VendorId,
                    RowNo = a.RowNo,
                    PushTime = a.PushTime,
                    IsOffline = a.IsOffline,
                    OfflineNo = a.OfflineNo
                });

            var queryPR = queryablePR.AsNoTracking().Where(b => companyIds.Contains(b.CompanyId))
                .Select(b => new { b.ApplicationCode, b.Id, b.ApplyUserId, b.ApplyUserIdName, b.ApplyTime, b.ApplyUserBu, b.SubBudgetCode, b.BudgetCode, b.ApplyUserBuName, b.ApplyUserDeptName, b.CompanyIdName, b.CompanyId, b.Currency, b.CurrencySymbol, b.Rate });

            var query = queryPR.GroupJoin(queryPrd, a => a.Id, b => b.PRApplicationId, (a, b) => new { PR = a, prds = b })
                .SelectMany(a => a.prds.DefaultIfEmpty(), (a, b) => new { a.PR, Prd = b, pid = b.Id })
                .Where(a => a.pid != null)
                //.Where(m => companyIds.Contains(m.PR.CompanyId))
                .WhereIf(!string.IsNullOrWhiteSpace(request.ApplicationCode), m => m.PR.ApplicationCode.Contains(request.ApplicationCode))
                .WhereIf(request.StartDate.HasValue, m => m.PR.ApplyTime.Value.Date >= request.StartDate)
                .WhereIf(request.EndDate.HasValue, m => m.PR.ApplyTime.Value.Date <= request.EndDate)
                .WhereIf(request.ApplyBuId.HasValue, m => m.PR.ApplyUserBu == request.ApplyBuId.Value)
                .WhereIf(!string.IsNullOrWhiteSpace(request.ApplyUserName), m => m.PR.ApplyUserIdName.Contains(request.ApplyUserName))
                .WhereIf(!string.IsNullOrWhiteSpace(request.Content), m => m.Prd.Content.Contains(request.Content))
                .WhereIf(!string.IsNullOrWhiteSpace(request.VendorName), m => m.Prd.VendorName.Contains(request.VendorName))
                .WhereIf(request.PRUserId.HasValue, m => m.Prd.PurchaserId == request.PRUserId)
                .WhereIf(!string.IsNullOrWhiteSpace(request.BudgetCode), m => m.PR.SubBudgetCode.Contains(request.BudgetCode))
                .Select(s => new GetPRApplicationDetailListReponse
                {
                    Id = s.Prd.Id,
                    PrNo = s.PR.ApplicationCode,
                    PrId = s.PR.Id,
                    Applicant = s.PR.ApplyUserIdName,
                    ApplicantId = s.PR.ApplyUserId,
                    ApplyTime = s.PR.ApplyTime,
                    Bu = s.PR.ApplyUserBuName,
                    ApplyUserBuToDeptName = s.PR.ApplyUserDeptName,
                    Company = s.PR.CompanyIdName,
                    CompanyId = s.PR.CompanyId,
                    BudgetCode = s.PR.BudgetCode,
                    Currency = string.IsNullOrWhiteSpace(s.PR.Currency) ? "RMB" : s.PR.Currency,
                    CurrencySymbol = string.IsNullOrWhiteSpace(s.PR.CurrencySymbol) ? "￥" : s.PR.CurrencySymbol,
                    Rate = string.IsNullOrWhiteSpace(s.PR.Currency) ? 1L : s.PR.Rate,
                    EstimateDate = s.Prd.EstimateDate != null ? s.Prd.EstimateDate.Value.ToString("yyyy-MM-dd") : "",
                    Status = s.Prd.OrderStatusFlag,
                    Quantity = s.Prd.Quantity,
                    UnitPrice = s.Prd.UnitPrice,
                    TotalAmount = s.Prd.TotalAmount,
                    TotalAmountRMB = s.Prd.TotalAmountRMB,
                    Content = s.Prd.Content,
                    RceNo = s.Prd.RceNo,
                    VendorName = s.Prd.VendorName,
                    VendorId = s.Prd.VendorId,
                    LineNo = s.Prd.RowNo,
                    PurchaserId = s.Prd.PurchaserId,
                    //PurchaseName = purchaser?.Name,
                    PushTime = s.Prd.PushTime,
                    IsOffline = s.Prd.IsOffline,
                    OfflineNo = s.Prd.OfflineNo,
                    //BiddingCode = bd?.ApplicationCode,
                    BiddingId = s.Prd.BiddingId,
                    IsEnableBidding = (!s.Prd.OrderStatusFlag.HasValue || s.Prd.OrderStatusFlag == OrderStatusFlag.BiddingReject || s.Prd.OrderStatusFlag == OrderStatusFlag.OrderReject) && !s.Prd.BiddingId.HasValue,
                    IsEnablePO = !s.Prd.OrderStatusFlag.HasValue || s.Prd.OrderStatusFlag == OrderStatusFlag.BiddingCompleted || s.Prd.OrderStatusFlag == OrderStatusFlag.OrderReject || s.Prd.OrderStatusFlag == OrderStatusFlag.BiddingReject,
                    BWApplicationId = s.Prd.BWApplicationId,
                    //BWApplictionCode = bw?.ApplicationCode,
                    //ExemptType = bw?.ExemptType,
                });

            var count = await query.CountAsync();
            ////判断是否需要分页
            if (isPaging)
                query = query.OrderBy(sort).Skip(request.PageIndex * request.PageSize).Take(request.PageSize);

            var queryDatas = query.ToList();

            var bdIds = queryDatas.Where(a => a.BiddingId.HasValue).Select(a => a.BiddingId.Value).ToList();
            var purchaserIds = queryDatas.Where(a => a.PurchaserId.HasValue).Select(a => a.PurchaserId.Value).ToList();
            var bwApplicationIds = queryDatas.Where(a => a.BWApplicationId.HasValue).Select(a => a.BWApplicationId.Value).ToList();

            var bds = queryableBidding.Where(a => bdIds.Contains(a.Id)).Select(b => new { b.Id, b.ApplicationCode }).ToList();
            var purchaserUsers = queryableUser.Where(a => purchaserIds.Contains(a.Id)).Select(b => new { b.Id, b.Name }).ToList();
            var bws = queryBw.Where(a => bwApplicationIds.Contains(a.Id)).Select(b => new { b.ApplicationCode, b.Id, b.ExemptType }).ToList();

            queryDatas.ForEach(s =>
            {
                var purchaser = purchaserUsers.FirstOrDefault(a => a.Id == s.PurchaserId);
                var bd = bds.FirstOrDefault(a => a.Id == s.BiddingId);
                var bw = bws.FirstOrDefault(a => a.Id == s.BWApplicationId);
                s.PurchaseName = purchaser?.Name;
                s.BiddingCode = bd?.ApplicationCode;
                s.BWApplictionCode = bw?.ApplicationCode;
                s.ExemptType = bw?.ExemptType;
            });

            var result = new PagedResultDto<GetPRApplicationDetailListReponse>(count, queryDatas);
            return result;
        }
        /// <summary>
        /// 导出我的采购推送
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<Stream> ExportPRApplicationDetailes(GetPRApplicationDetailListRequest request)
        {
            using MemoryStream stream = new();
            List<object> list = [];
            list.AddRange((await GetProcurementPushListAsync(request, false)).Items);
            stream.SaveAs(list, true, "Sheet1");
            stream.Seek(0, SeekOrigin.Begin);
            return stream;
        }
        /// <summary>
        /// 根据ID获取单据详细信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public async Task<POInfoReponseDto> GetPurPoApplicationAsync(Guid Id)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var queryablePo = await LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>().GetQueryableAsync();
            var queryablePodetail = await LazyServiceProvider.LazyGetService<IPurPOApplicationDetailsRepository>().GetQueryableAsync();
            var vendor = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();
            var queryFinancial = await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync();
            var attachmentQuery = await LazyServiceProvider.LazyGetService<IAttachmentRepository>().GetQueryableAsync();
            var queryBpcsAvm = await LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync();
            var paymentTermRepository = await LazyServiceProvider.LazyGetService<IBpcsAvtRepository>().GetQueryableAsync();
            var poData = queryablePo.Where(a => a.Id == Id)
                .GroupJoin(queryablePodetail, a => a.Id, b => b.POApplicationId, (a, b) => new { Po = a, Pods = b })
                .FirstOrDefault();

            var po = ObjectMapper.Map<PurPOApplication, POInfoReponseDto>(poData.Po);
            if (poData.Pods.Any())
            {
                var podetais = ObjectMapper.Map<List<PurPOApplicationDetails>, List<PODetailInfoReponseDto>>(poData.Pods.ToList());
                po.PODetailInfos = podetais;
            }
            //附件信息
            var AttachmentFileIds = poData.Po?.AttachmentFile?.Split(',') ?? [];
            if (AttachmentFileIds.Any())
            {
                var attachmentList = attachmentQuery.Where(m => AttachmentFileIds.Contains(m.Id.ToString())).ToList();
                po.Files = ObjectMapper.Map<List<Attachment>, List<UploadFileResponseDto>>(attachmentList);
                foreach (var file in po.Files)
                {
                    var size = attachmentList.Where(w => w.Id == file.AttachmentId).Select(s => s.Size).First();
                    file.FileSize = size < 1048576 ? (size / 1024).ToString() + "KB" : (size / 1048576).ToString() + "MB";
                }
            }
            //获取PR明细行信息
            var prdetails = poData.Po?.PRApplicationDetailId?.Split(',') ?? [];
            if (prdetails.Any())
            {
                var prdids = prdetails.Select(Guid.Parse).ToList();
                po.PRDetails = await getPRInfo(prdids);
            }
            var aps = queryBpcsAvm.Where(a => a.Id == po.VendorId)
            .Join(queryFinancial, a => a.FinaId, a => a.Id, (a, b) => new { Avm = a, Fin = b })
            .Join(vendor, a => a.Fin.VendorId, a => a.Id, (a, b) => b.ApsPorperty)
            .FirstOrDefault();
            var apsProperty = await dataverseService.GetDictionariesAsync(DictionaryType.ApsProperty, null);
            var apsList = new List<DictionaryDto>();
            if (!string.IsNullOrWhiteSpace(aps))
            {
                foreach (var item in aps.Split(","))
                {
                    apsList.Add(apsProperty.FirstOrDefault(a => a.Code == item));
                }
            }
            po.ApsPorpertys = apsList;
            if (!string.IsNullOrWhiteSpace(poData.Po.ApsPorperty))
            {
                po.ApsPorperty = poData.Po.ApsPorperty;//选择非APS 时该字段为空
                po.ApsPorpertyName = apsList.Where(a => a.Code == po.ApsPorperty).FirstOrDefault()?.Name ?? poData.Po.ASNTypeTex;
            }
            po.ApsPorpertyOldName = poData.Po.ASNTypeTex;//前端可能不需要这个字段，使用的ApsPorpertyName
            #region 该公司可选择的PaymentTerms
            var company = (await dataverseService.GetCompanyList(po.CompanyId.ToString(), null)).FirstOrDefault();
            var paymentTerms = paymentTermRepository.Where(a => a.Vcmpy == decimal.Parse(company.CompanyCode ?? "0"))
                .Select(a => new DictionaryDto
                {
                    Code = a.Vterm,
                    Name = a.Vterm + "_" + a.Vtmddy + "天",
                    Type = a.Vtmdsc
                })
                .ToList();
            po.PaymentTerms = paymentTerms;
            #endregion

            po.ApplyName = poData.Po.ApplyUserName;
            //Po.VendorName = POdata.VendorName;
            po.IsMyInitiated = poData.Po.ApplyUserId == CurrentUser.Id;

            #region 获取采购品类名称并拼接
            if (po.PRType?.Length > 0)
            {
                var poCategory = await LazyServiceProvider.LazyGetService<IPurCategoryConfigRepository>()
                    .GetListAsync(a => po.PRType.Select(Guid.Parse).Contains(a.Id));
                po.PRTypeNameString = poCategory.Count < 1 ? null :
                    poCategory.OrderBy(a => a.Hierarchy).Select(a => a.NameEn).JoinAsString(" / ");
            }
            #endregion

            //只有采购专员和管理员能查看Msa板块的信息
            if (!CurrentUser.IsInRole(RoleNames.BizAdmin) && !CurrentUser.IsInRole(RoleNames.ProcurementSpecialist))
                po.MsaId = null;

            return po;
        }

        /// <summary>
        /// 根据采购订单详情获取相应信息
        /// </summary>
        /// <param name="guids"></param>
        /// <returns></returns>
        public async Task<List<GetPRDetailListDto>> getPRInfo(List<Guid> guids)
        {
            var queryablePRdetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var queryablePR = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var queryBW = await LazyServiceProvider.LazyGetService<IPurBWApplicationRepository>().GetQueryableAsync();
            var queryBD = await LazyServiceProvider.LazyGetService<IPurBDApplicationRepository>().GetQueryableAsync();
            var queryBdDetail = await LazyServiceProvider.LazyGetService<IPurBDApplicationDetailsRepository>().GetQueryableAsync();

            var bdQuery = queryBD.Where(a => a.Status == PurBDStatus.Approved)
                .GroupJoin(queryBdDetail, a => a.Id, b => b.BDApplicationId, (a, b) => new { bd = a, bdds = b })
                .SelectMany(a => a.bdds.DefaultIfEmpty(), (a, b) => new { a.bd, bdd = b });

            var query = queryablePR
            .Join(queryablePRdetail.Where(a => guids.Contains(a.Id)), a => a.Id, b => b.PRApplicationId, (a, b) => new { PR = a, PRDetail = b })
            .GroupJoin(queryBW, a => a.PRDetail.BWApplicationId, b => b.Id, (a, b) => new { a.PR, a.PRDetail, BWs = b })
            .SelectMany(a => a.BWs.DefaultIfEmpty(), (a, b) => new { a.PR, a.PRDetail, BW = b })
            .GroupJoin(bdQuery, a => a.PRDetail.Id, b => b.bdd.PRDetailId, (a, b) => new { a.PR, a.PRDetail, a.BW, BDS = b })
            .SelectMany(a => a.BDS.DefaultIfEmpty(), (a, b) => new { a.PR, a.PRDetail, a.BW, Bd = b.bd })
            .Select(s => new GetPRDetailListDto
            {
                Id = s.PRDetail.Id,
                ApplicationCode = s.PR.ApplicationCode,
                Applicant = s.PR.ApplyUserIdName,
                ApplicationBu = s.PR.ApplyUserBuName,
                ApplyUserBuToDeptName = s.PR.ApplyUserDeptName,
                EstimateDate = s.PRDetail.EstimateDate.HasValue ? s.PRDetail.EstimateDate.Value.ToString("yyyy-MM-dd") : null,
                Quantity = s.PRDetail.Quantity,
                Unit = s.PRDetail.Unit,
                UnitPrice = s.PRDetail.UnitPrice,
                TotalAmount = s.PRDetail.TotalAmount,
                TotalAmountRMB = s.PRDetail.TotalAmountRMB,
                TaxRate = s.PRDetail.TaxRate,
                TaxAmounts = s.PRDetail.TaxAmount,
                Content = s.PRDetail.Content,
                VendorId = s.PRDetail.VendorId,
                VendorName = s.PRDetail.VendorName,
                VendorCode = s.PRDetail.VendorCode,
                PrLateDescription = s.PR.PrLateDescription,
                RowNo = s.PRDetail.RowNo,
                RceNo = s.PRDetail.RceNo,
                Currency = s.PR.Currency,
                CurrencySymbol = s.PR.CurrencySymbol,
                PayMethod = s.PRDetail.PayMethod,
                PRId = s.PR.Id,
                BWId = s.BW != null ? s.BW.Id : null,
                BWApplicationCode = s.BW != null ? s.BW.ApplicationCode : string.Empty,
                ExemptType = s.BW != null ? s.BW.ExemptType : null,
                BDId = s.Bd != null ? s.Bd.Id : null,
                BDApplicationCode = s.Bd != null ? s.Bd.ApplicationCode : null
            });
            var datas = query.ToList();
            return datas;
        }

        /// <summary>
        /// 根据PR单号获取PR相关信息
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<GetPRInfoResponseDto> GetPRInfoAsync(POInitiateRequestDto request)
        {
            var queryBpcsPmfvm = await LazyServiceProvider.LazyGetService<IBpcsPmfvmRepository>().GetQueryableAsync();
            var queryBpcsAvm = await LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync();
            var queryFinancial = await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync();
            var queryPR = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var queryPRDetails = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var queryAttachment = await LazyServiceProvider.LazyGetService<IAttachmentRepository>().GetQueryableAsync();
            var queryVendor = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();
            var queryVendorOrg = await LazyServiceProvider.LazyGetService<IVendorOrgnizationRepository>().GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var bpcsGcc = await LazyServiceProvider.LazyGetService<IBpcsGccRepository>().GetQueryableAsync();
            var paymentTermRepository = await LazyServiceProvider.LazyGetService<IBpcsAvtRepository>().GetQueryableAsync();
            var queryableBD = await LazyServiceProvider.LazyGetService<IPurBDApplicationRepository>().GetQueryableAsync();
            var queryBDDetails = await LazyServiceProvider.LazyGetService<IPurBDApplicationDetailsRepository>().GetQueryableAsync();
            var prDetailIds = request.PRDetailIds.Split(",").Select(Guid.Parse).ToList();
            var prDetails = queryPRDetails.Where(a => prDetailIds.Contains(a.Id)).ToList();
            var pRApplication = queryPR.Where(a => a.Id == prDetails.FirstOrDefault().PRApplicationId).FirstOrDefault();
            var prInfo = new GetPRInfoResponseDto();
            if (pRApplication == null || !pRApplication.CompanyId.HasValue)
            {
                return null;
            }
            var getCompanyTask = (await dataverseService.GetCompanyList(pRApplication.CompanyId.ToString())).FirstOrDefault();
            prInfo.CompanyId = pRApplication.CompanyId.Value;
            prInfo.CompanyCode = getCompanyTask.CompanyCode;
            prInfo.InvoiceTitle = getCompanyTask.InvoiceTitle;
            prInfo.InvoiceAddress = getCompanyTask.InvoiceAddress;
            prInfo.PhoneNumber = getCompanyTask.Telephone;
            prInfo.Fax = getCompanyTask.Fax;
            prInfo.OpenBank = getCompanyTask.Bank;
            prInfo.BankAccount = getCompanyTask.BankCardNo;
            var paymentTerms = paymentTermRepository.Where(a => a.Vcmpy == decimal.Parse(getCompanyTask.CompanyCode ?? "0"))
                .Select(a => new DictionaryDto
                {
                    Code = a.Vterm,
                    Name = a.Vterm + "_" + a.Vtmddy + "天",
                    Type = a.Vtmdsc
                })
                .ToList();
            prInfo.PaymentTerms = paymentTerms;

            #region 币种 汇率
            //var companyCurrency = await dataverseService.GetCompanyCurrencyList();
            //var currency = companyCurrency.Where(a => a.CompanyId == pRApplication.CompanyId).ToList();
            //var currencyCodes = currency.Select(x => x.Code).ToList();
            //var exchangeRate = bpcsGcc.Where(a => currencyCodes.Contains(a.Ccfrcr) && a.Cctocr == "RMB").GroupBy(a => a.Ccfrcr)
            //  .Select(g => g.OrderByDescending(x => x.Ccnvdt).FirstOrDefault()).ToList();
            //var currencys = new List<CurrencyExchangeRateDto>();
            //foreach (var item in currency)
            //{
            //    currencys.Add(new CurrencyExchangeRateDto()
            //    {
            //        Id = item.Id,
            //        Name = item.Name,
            //        Code = item.Code,
            //        CompanyId = item.CompanyId,
            //        CurrencySymbol = item.CurrencySymbol,
            //        ExchangeRate = item.Code == "RMB" ? 1M : exchangeRate.Where(a => a.Ccfrcr == item.Code).FirstOrDefault()?.Ccnvfc ?? 1M  //人民币的时候汇率为1、其他汇率从bpcs取得
            //    });
            //}
            //currencys = currencys.OrderBy(a => a.Code == "RMB" ? 0 : 1).ToList();
            //prInfo.Currencys = currencys;
            if (!string.IsNullOrWhiteSpace(pRApplication.Currency))
            {
                prInfo.IsHaveCurrency = true;//PR是否有币种信息
                prInfo.Currency = pRApplication.Currency;
                prInfo.ExchangeRate = (decimal)pRApplication.Rate;
                prInfo.PlanRate = pRApplication.PlanRate;
                prInfo.ExpectedFloatRate = pRApplication.ExpectedFloatRate;
                prInfo.CurrencySymbol = pRApplication.CurrencySymbol;
            }
            #endregion

            #region 供应商信息 有Bidding 以Bidding为准 
            var vendorId = prDetails.FirstOrDefault().VendorId;
            PurBDStatus[] bdStatus = [PurBDStatus.Invalid, PurBDStatus.Rejected, PurBDStatus.Draft];
            var dbApplication = queryableBD.Where(a => !bdStatus.Contains(a.Status))//Bidding 排除草稿\作废\拒绝
                               .GroupJoin(queryBDDetails, a => a.Id, b => b.BDApplicationId, (a, b) => new { bd = a, details = b })
                               .SelectMany(s => s.details.DefaultIfEmpty(), (a, b) => new { BD = a.bd, Details = b })
                               .Where(a => !bdStatus.Contains(a.BD.Status))//排除作废和拒绝
                               .Where(a => a.Details.PRDetailId.HasValue && prDetailIds.Contains(a.Details.PRDetailId.Value))
                               .ToList();
            if (dbApplication.Any())//有Bidding 则与Bidding 为准
            {
                var bd = dbApplication.FirstOrDefault().BD;
                vendorId = dbApplication.FirstOrDefault().BD.VendorId;
                prInfo.IsBidding = true;
                prInfo.PRCorrespond = bd.PRCorrespond;
                if (CorrespondType.PRCorrespond.Equals(bd.PRCorrespond))
                {
                    var bdDetails = dbApplication.Select(a => a.Details).ToList();
                    var poDetails = ObjectMapper.Map<List<PurBDApplicationDetail>, List<PODetailInfoReponseDto>>(bdDetails);
                    prInfo.BDTOPODetails = poDetails;
                }
                else if (CorrespondType.Customize.Equals(bd.PRCorrespond))
                {
                    prInfo.BDTOPODetails = new List<PODetailInfoReponseDto>();
                }
                prInfo.Currency = bd.Currency;
                prInfo.ExchangeRate = (decimal)bd.Rate;
                prInfo.CurrencySymbol = bd.CurrencySymbol;
            }
            string currencyCode = prInfo.Currency;
            if (vendorId.HasValue)
            {
                var apsProperty = await dataverseService.GetDictionariesAsync(DictionaryType.ApsProperty);
                var query = queryBpcsAvm.Where(a => a.Id == vendorId)
                .GroupJoin(queryFinancial, a => a.FinaId, a => a.Id, (a, b) => new { Avm = a, Fins = b })
                .SelectMany(a => a.Fins.DefaultIfEmpty(), (a, b) => new { a.Avm, Fin = b })
                .GroupJoin(queryVendor, a => a.Fin.VendorId, a => a.Id, (a, b) => new { a.Avm, Vendors = b })
                .SelectMany(a => a.Vendors.DefaultIfEmpty(), (a, b) => new { a.Avm, Vendor = b })
                .GroupJoin(queryBpcsPmfvm, a => a.Avm.Vcmpny.ToString() + a.Avm.Vendor.ToString(), a => a.Vmcmpy.ToString() + a.Vnderx.ToString(), (a, b) => new { a.Avm, a.Vendor, Pmfvms = b })
                .SelectMany(a => a.Pmfvms.DefaultIfEmpty(), (a, b) => new { a.Avm, a.Vendor, Pmfvm = b });
                var vendor = query.FirstOrDefault();
                currencyCode = vendor.Avm.Vcurr;//供应商币种

                var specialvendors = await dataverseService.GetSpecialvendorAsync();//特殊供应商
                var vendorName = vendor.Pmfvm?.Vextnm;
                if (vendor.Pmfvm != null)
                {
                    var specialvendor = specialvendors.FirstOrDefault(a => a.VendorCode == vendor.Avm.Vendor.ToString() && a.CompanyCode == vendor.Avm.Vcmpny.ToString());//特殊供应商
                    if (!string.IsNullOrWhiteSpace(specialvendor?.Name))
                        vendorName = specialvendor.Name;
                }

                var vendorInfo = new PRVendorInfoDto
                {
                    VendorId = vendor.Avm?.Id,
                    VendorType = vendor.Avm?.Vtype,
                    VendorCode = vendor.Avm.Vendor.ToString(),
                    ApsPorpertyStr = vendor.Vendor?.ApsPorperty,
                    VendorName = vendorName,
                    RegCertificateAddress = vendor.Avm?.Vndad1 + vendor.Avm?.Vndad2 + vendor.Avm?.Vndad3,
                    ContactName = vendor.Avm?.Vcon,
                    ContactPhone = vendor.Avm?.Vphone,
                    ContactEmail = vendor.Pmfvm?.Vemlad,
                };
                if (!string.IsNullOrWhiteSpace(vendorInfo.ApsPorpertyStr))
                {
                    vendorInfo.VendorPorperty = true;//有APS属性才为Ture
                    var aspList = vendorInfo.ApsPorpertyStr.Split(',');
                    var apsList = new List<DictionaryDto>();
                    foreach (var item in aspList)
                    {
                        var aps = apsProperty.FirstOrDefault(a => a.Code == item);
                        if (aps != null)
                        {
                            apsList.Add(aps);
                        }
                    }
                    vendorInfo.ApsPorperty = aspList.ToList();
                    vendorInfo.ApsPorpertys = apsList;
                }
                var nonSpeakerVendor = await LazyServiceProvider.LazyGetService<IPurPRApplicationService>().GetNonSpeakerForChoiceAsync(new GetNonSpeakerListForChoiceRequestDto
                {
                    Code = vendorInfo.VendorCode,
                    PageIndex = 1,
                    PageSize = 10,
                    Company = pRApplication.CompanyId.Value,
                    PayMethod = PayMethods.AP,
                });
                vendorInfo.PaymentTerm = nonSpeakerVendor?.Items?.FirstOrDefault()?.PaymentTerm;
                prInfo.VendorInfo = vendorInfo;
            }
            #endregion

            #region PR无币种信息 且BD也未带出币种信息
            if (prInfo.IsHaveCurrency == false && string.IsNullOrWhiteSpace(prInfo.Currency))
            {
                if (vendorId.HasValue)
                {
                    if (currencyCode == "RMB")
                    {
                        prInfo.Currency = currencyCode;
                        prInfo.ExchangeRate = 1;
                        prInfo.CurrencySymbol = "￥";
                    }
                    else
                    {
                        //实时汇率 
                        var queryBpcsGcc = await LazyServiceProvider.LazyGetService<IBpcsGccRepository>().GetQueryableAsync();
                        var usdGcc = queryBpcsGcc.Where(a => a.Ccfrcr == currencyCode && a.Cctocr == "RMB").OrderByDescending(a => a.Ccnvdt).FirstOrDefault();

                        prInfo.Currency = currencyCode;
                        prInfo.ExchangeRate = (decimal)usdGcc.Ccnvfc.Value;
                        prInfo.CurrencySymbol = getCompanyTask?.CompanyCurrency.Where(a => a.Code == currencyCode).FirstOrDefault()?.CurrencySymbol;//获取币种符号
                    }
                }
            }
            if (string.IsNullOrWhiteSpace(prInfo.CurrencySymbol)) //没有币种符号情况下（历史数据本身已经发起了Bidding的情况下）（迁移过来的数据可能没有币种符号）
            {
                prInfo.CurrencySymbol = getCompanyTask?.CompanyCurrency.Where(a => a.Code == currencyCode).FirstOrDefault()?.CurrencySymbol;//获取币种符号
            }
            #endregion

            #region PR附件  BD附件
            var fileIds = new List<string>();
            if (!prInfo.IsBidding) //PR
            {
                if (!string.IsNullOrWhiteSpace(pRApplication.SupportFiles))
                    fileIds.AddRange(pRApplication.SupportFiles.Split(',').ToList());
                if (!string.IsNullOrWhiteSpace(pRApplication.AdditionalFiles))
                    fileIds.AddRange(pRApplication.AdditionalFiles.Split(',').ToList());
            }
            else
            {
                foreach (var item in dbApplication.Select(a => a.BD).Distinct())
                {
                    if (!string.IsNullOrWhiteSpace(item.AttachmentFile))
                        fileIds.AddRange(item.AttachmentFile.Split(',').ToList());
                }
            }
            if (fileIds.Any())
            {
                List<UploadFileResponseDto> prFiles = new List<UploadFileResponseDto>();
                var attachmentIds = fileIds.Select(Guid.Parse).ToList();
                var attachments = queryAttachment.Where(a => attachmentIds.Contains(a.Id)).ToList();
                foreach (var item in attachments)
                {
                    prFiles.Add(new UploadFileResponseDto()
                    {
                        AttachmentId = item.Id,
                        FileName = item.FileName,
                        FilePath = item.FilePath,
                        Success = true
                    });
                }
                prInfo.Files = prFiles;
            }
            #endregion
            return prInfo;
        }

        /// <summary>
        /// PaymentTerms
        /// </summary>
        /// <param name="VendorId"></param>
        /// <returns></returns>
        public async Task<IEnumerable<DictionaryDto>> GetPaymentTermsAsync()
        {
            var paymentTermRepository = await LazyServiceProvider.LazyGetService<IBpcsAvtRepository>().GetQueryableAsync();
            var paymentTerms = paymentTermRepository.GroupBy(a => a.Vterm)
                .Select(g => new DictionaryDto
                {
                    Code = g.Key,
                    Name = g.FirstOrDefault().Vterm + "_" + g.FirstOrDefault().Vtmddy + "天",
                    Type = g.FirstOrDefault().Vtmddy.HasValue ? g.FirstOrDefault().Vtmddy.Value.ToString() : ""
                });
            var result = paymentTerms.ToList();
            return result;
        }
        /// <summary>
        /// PaymentTerms:xx_xx_xx天
        /// </summary>
        /// <returns></returns>
        public async Task<IEnumerable<DictionaryDto>> GetThePaymentTermsAsync()
        {
            var paymentTermRepository = await LazyServiceProvider.LazyGetService<IBpcsAvtRepository>().GetQueryableAsync();
            var paymentTerms = paymentTermRepository.GroupBy(a => new { a.Vcmpy, a.Vterm, a.Vtmddy })
                .Select(g => new DictionaryDto
                {
                    Code = g.Key.Vcmpy + "_" + g.Key.Vterm + "_" + g.Key.Vtmddy,
                    Name = g.Key.Vterm + "_" + g.Key.Vtmddy + "天",
                    Type = g.FirstOrDefault().Vtmdsc
                });

            return paymentTerms.ToList();
        }

        /// <summary>
        /// 采购订单列表
        /// </summary>
        /// <returns></returns>
        public async Task<PagedResultDto<POListResponseDto>> GetPurPOApplicationListAsync(GetPOApplicationListRequest request, bool isPaging = true)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var queryablePO = await LazyServiceProvider.LazyGetService<IPurPOApplicationReadonlyRepository>().GetQueryableAsync();
            var queryVendor = await LazyServiceProvider.LazyGetService<IVendorReadonlyRepository>().GetQueryableAsync();
            var queryBpcsAvm = await LazyServiceProvider.LazyGetService<IBpcsAvmReadonlyRepository>().GetQueryableAsync();
            var queryFinancial = await LazyServiceProvider.LazyGetService<IVendorFinancialReadonlyRepository>().GetQueryableAsync();
            var queryablePr = await LazyServiceProvider.LazyGetService<IPurPRApplicationReadonlyRepository>().GetQueryableAsync();

            //任务中心 状态 
            //#2241【任务中心】【我发起的】发起收货状态的单据，在PO申请人的待处理列表找不到
            PurOrderStatus[] pending = [PurOrderStatus.ApplierConfirmed, PurOrderStatus.Return, PurOrderStatus.InitiateReceipt];//待处理
            PurOrderStatus[] progressings = [PurOrderStatus.Approving];//进行中
            PurOrderStatus[] completed = [PurOrderStatus.Rejected, PurOrderStatus.Closed, PurOrderStatus.Invalid];//已完成

            //获取代理信息
            var agents = await LazyServiceProvider.LazyGetService<IAgencyService>().GetOriginalOperators(new GetAgentOperatorsRequestDto { BusinessTypeCategory = ResignationTransfer.TaskFormCategory.PurhcaseOrderApplication });
            var userIds = agents.Select(a => a.Key).Distinct().ToArray();
            var principalIds = userIds.Where(a => a != CurrentUser.Id.Value);

            List<string> prTypes = [];
            if (request.PRType?.Count > 0)
            {
                foreach (var row in request.PRType)
                    prTypes.Add(string.Join(",", row));
            }

            var queryable = queryablePO.Where(m => m.Status != PurOrderStatus.Draft)
                .Join(queryablePr.Select(a => new { a.Id, a.ApplyUserId, a.ApplicationCode, a.ApplyUserIdName, a.ApplyUserDeptName, a.SubBudgetId }), a => a.PRId, b => b.Id, (a, b) => new { po = a, pr = b })
                .WhereIf(ProcessingStatus.PendingProcessing.Equals(request.ProcessingStatus), m => pending.Contains(m.po.Status))
                .WhereIf(ProcessingStatus.Progressing.Equals(request.ProcessingStatus), m => progressings.Contains(m.po.Status))
                .WhereIf(ProcessingStatus.Completed.Equals(request.ProcessingStatus), m => completed.Contains(m.po.Status))
                .WhereIf(!string.IsNullOrWhiteSpace(request.ApplicationCode), m => m.po.ApplicationCode.Contains(request.ApplicationCode))
                .WhereIf(!string.IsNullOrWhiteSpace(request.ApplyUserName), m => m.po.ApplyUserName.Contains(request.ApplyUserName))
                .WhereIf(request.ApplyUserId != null, m => m.po.ApplyUserId == request.ApplyUserId)
                .WhereIf(!string.IsNullOrWhiteSpace(request.VendorName), m => m.po.VendorName.Contains(request.VendorName))
                .WhereIf(!string.IsNullOrWhiteSpace(request.ApplicationDept), m => m.po.ApplyUserBuToDeptName.Contains(request.ApplicationDept))
                .WhereIf(request.VendorPorperty.HasValue, m => m.po.VendorPorperty == request.VendorPorperty)
                .WhereIf(request.StartDate.HasValue, m => m.po.ApplyTime >= request.StartDate)
                .WhereIf(request.EndDate.HasValue, m => m.po.ApplyTime <= request.EndDate.Value.Date.AddDays(1))
                .WhereIf(request.IsMyInitiated.HasValue && request.IsMyInitiated.Value, m => (m.po.ApplyUserId == CurrentUser.Id.Value && !m.po.TransfereeId.HasValue) || CurrentUser.Id.Value == m.po.TransfereeId.Value || principalIds.ToHashSet().Contains(m.po.ApplyUserId))
                .WhereIf(request.POType.HasValue, m => m.po.POType == request.POType)
                .WhereIf(request.CompanyId.HasValue, m => m.po.CompanyId == request.CompanyId)
                .WhereIf(request.Status.HasValue, m => m.po.Status == request.Status)
                .WhereIf(request.StatusList != null && request.StatusList.Count > 0, m => request.StatusList.Contains(m.po.Status))
                .WhereIf(!string.IsNullOrWhiteSpace(request.ApsPorperty), m => m.po.ApsPorperty.Contains(request.ApsPorperty))
                .WhereIf(!string.IsNullOrWhiteSpace(request.PRApplicationCode), m => m.pr.ApplicationCode.Contains(request.PRApplicationCode))
                .WhereIf(!string.IsNullOrWhiteSpace(request.PRApplyUserName), m => m.pr.ApplyUserIdName.Contains(request.PRApplyUserName))
                .WhereIf(!string.IsNullOrWhiteSpace(request.PRApplicationDept), m => m.pr.ApplyUserDeptName.Contains(request.PRApplicationDept))
                .WhereIf(request.PRApplyUserId.HasValue, m => m.pr.ApplyUserId == request.PRApplyUserId)
                .WhereIf(prTypes.Count > 0, m => prTypes.Contains(m.po.PRType))
                .Select(a => new
                {
                    a.po.Id,
                    a.po.ApplicationCode,
                    a.po.Status,
                    a.po.ApplyUserId,
                    a.po.ApplyUserName,
                    a.po.ApplyTime,
                    a.po.ApplyUserBu,
                    a.po.ApplyUserBuName,
                    a.po.ApplyUserBuToDeptName,
                    a.po.ApplyUserDept,
                    a.po.POType,
                    a.po.VendorName,
                    a.po.VendorCode,
                    a.po.VendorPorperty,
                    a.po.TotalAmount,
                    a.po.TotalAmountTax,
                    a.po.VendorId,
                    a.po.CompanyId,
                    a.po.PRId,
                    a.po.ApsPorperty,
                    a.po.PRType,
                    a.po.TransfereeId,
                    a.pr.SubBudgetId,
                    PrCode = a.pr.ApplicationCode,
                    a.pr.ApplyUserIdName,
                    a.pr.ApplyUserDeptName
                });

            if (request.IsMyInitiated == false)
            {
                var personCenterService = LazyServiceProvider.LazyGetService<IPersonCenterService>();
                var rolesIds = await personCenterService.GetIdsByRoleLevels(bizTypeCategory: ResignationTransfer.TaskFormCategory.PurhcaseOrderApplication);
                queryable = queryable.ApplyRoleFilters(rolesIds, CurrentUser.Id.Value, x => x.ApplyUserBu, x => x.ApplyUserDept, x => x.SubBudgetId, x => x.ApplyUserId, x => x.TransfereeId);
            }

            var count = queryable.Count();
            List<POListResponseDto> poApplicationList = new List<POListResponseDto>();
            //判断是否需要分页
            if (isPaging)
            {
                poApplicationList = queryable.OrderByDescending(s => s.ApplyTime).Skip(request.PageIndex * request.PageSize).Take(request.PageSize).Select(s => new POListResponseDto
                {
                    Id = s.Id,
                    ApplicationCode = s.ApplicationCode,
                    Status = s.Status,
                    ApplyUserName = s.ApplyUserName,
                    ApplyTime = s.ApplyTime.ToString("yyyy-MM-dd HH:mm:ss"),
                    ApplyBuName = s.ApplyUserBuName,
                    ApplyUserBuToDeptName = s.ApplyUserBuToDeptName,
                    POType = s.POType,
                    VendorName = s.VendorName,
                    VendorCode = s.VendorCode,
                    VendorPorperty = s.VendorPorperty,
                    TotalAmount = s.TotalAmount,
                    TotalAmountTax = s.TotalAmountTax,
                    VendorId = s.VendorId,
                    CompanyId = s.CompanyId,
                    PRId = s.PRId,
                    PRApplicationCode = s.PrCode,
                    PrApplyUserName = s.ApplyUserIdName,
                    PrApplicationDept = s.ApplyUserDeptName,
                    ApsPorperty = s.ApsPorperty,
                    PrType = s.PRType
                }).ToList();
            }
            else
            {
                poApplicationList = queryable.Select(s => new POListResponseDto
                {
                    Id = s.Id,
                    ApplicationCode = s.ApplicationCode,
                    Status = s.Status,
                    ApplyUserName = s.ApplyUserName,
                    ApplyTime = s.ApplyTime.ToString("yyyy-MM-dd HH:mm:ss"),
                    ApplyBuName = s.ApplyUserBuName,
                    ApplyUserBuToDeptName = s.ApplyUserBuToDeptName,
                    POType = s.POType,
                    VendorName = s.VendorName,
                    VendorCode = s.VendorCode,
                    VendorPorperty = s.VendorPorperty,
                    TotalAmount = s.TotalAmount,
                    TotalAmountTax = s.TotalAmountTax,
                    VendorId = s.VendorId,
                    CompanyId = s.CompanyId,
                    PRId = s.PRId,
                    PRApplicationCode = s.PrCode,
                    PrApplyUserName = s.ApplyUserIdName,
                    PrApplicationDept = s.ApplyUserDeptName,
                    ApsPorperty = s.ApsPorperty,
                    PrType = s.PRType
                }).ToList();
            }

            var vendors = queryBpcsAvm.Where(a => poApplicationList.Select(a => a.VendorId).Contains(a.Id))
                    .GroupJoin(queryFinancial, a => a.FinaId, a => a.Id, (a, b) => new { Avm = a, Fins = b })
                    .SelectMany(a => a.Fins.DefaultIfEmpty(), (a, b) => new { a.Avm, Fin = b })
                    .GroupJoin(queryVendor, a => a.Fin.VendorId, a => a.Id, (a, b) => new { a.Avm, a.Fin, Vendors = b })
                    .SelectMany(a => a.Vendors.DefaultIfEmpty(), (a, b) => new { a.Avm, a.Fin, Vendor = b })
                    .ToList();

            IEnumerable<PurCategoryConfig> categoryConfigs = null;
            var prTypeIds = poApplicationList.Where(a => !string.IsNullOrEmpty(a.PrType)).Select(a => a.PrType.Split(",")).SelectMany(a => a, (a, b) => Guid.Parse(b)).Distinct().ToArray();
            if (prTypeIds.Any())
                categoryConfigs = await LazyServiceProvider.LazyGetService<IPurCategoryConfigReadonlyRepository>().GetListAsync(a => prTypeIds.Contains(a.Id));

            var getCompanyTask = await dataverseService.GetCompanyList(stateCode: null);
            var apsProperties = await dataverseService.GetDictionariesAsync(DictionaryType.ApsProperty, null);
            poApplicationList.ForEach(s =>
            {
                s.VendorType = vendors.Where(a => s.VendorId == a.Avm.Id).FirstOrDefault()?.Vendor?.VendorType;
                s.CompanyName = s.CompanyId.HasValue ? getCompanyTask.FirstOrDefault(a => a.Id == s.CompanyId)?.CompanyName ?? "" : "";
                s.ApsPorperty = apsProperties.FirstOrDefault(a => a.Code == s.ApsPorperty)?.Name;

                if (!string.IsNullOrEmpty(s.PrType))
                {
                    var categoryConfigIds = s.PrType.Split(",").Select(Guid.Parse);
                    s.PrType = categoryConfigs.Where(a => categoryConfigIds.Contains(a.Id)).OrderBy(a => a.Hierarchy).Select(a => a.NameEn).JoinAsString(" / ");
                }
            });
            var result = new PagedResultDto<POListResponseDto>(count, poApplicationList);
            return result;
        }
        /// <summary>
        /// 导出采购订单列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<Stream> ExportPOApplication(GetPOApplicationListRequest request)
        {
            var list = await ExportPOApplicationAsync(request);
            MemoryStream stream = new();
            stream.SaveAs(list, true, "SheetName");
            stream.Seek(0, SeekOrigin.Begin);
            return stream;
        }

        public async Task<List<ExportPOListResponseDto>> ExportPOApplicationAsync(GetPOApplicationListRequest request)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var categorys = await LazyServiceProvider.LazyGetService<IPurCategoryConfigReadonlyRepository>().GetListAsync();

            if (!request.StartDate.HasValue || !request.EndDate.HasValue)
            {
                request.StartDate = DateTime.Now.Date.AddYears(-1);
                request.EndDate = DateTime.Now.Date;
            }
            var dateTimes = DatetimeHelper.GetQueryTimeList(request.StartDate.Value.Date, request.EndDate.Value.Date, 20);
            var apsProperty = await dataverseService.GetDictionariesAsync(DictionaryType.ApsProperty, null);

            var allResults = new ConcurrentBag<ExportPOListResponseDto>(); // 使用 ConcurrentBag 避免锁竞争
            var errorTimes = new ConcurrentBag<KeyValuePair<DateTime, DateTime>>();
            var tasks = new List<Task>();

            using (var semaphoreSlim = new SemaphoreSlim(20))//可同时执行20个线程
            {
                foreach (var item in dateTimes)
                {
                    tasks.Add(QueryPoExportDataAsync(request, categorys, apsProperty, allResults, errorTimes, semaphoreSlim, item));
                }
                await Task.WhenAll(tasks);
                //后续可优化，错误的再次查询  errorTimes
            }

            var result = allResults.OrderByDescending(a => a.ApplyTime).ThenBy(a => a.ApplicationCode).ToList();
            return result;
        }
        #region 我审批的
        /// <summary>
        /// PO 我审批的列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<POApprovalResponseDto>> GetPOApprovalListAsync(GetPOApprovalRequestDto request, bool isPage = true)
        {
            var result = new PagedResultDto<POApprovalResponseDto>();

            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var queryPO = await LazyServiceProvider.LazyGetService<IPurPOApplicationReadonlyRepository>().GetQueryableAsync();
            var queryPR = await LazyServiceProvider.LazyGetService<IPurPRApplicationReadonlyRepository>().GetQueryableAsync();
            var vendor = await LazyServiceProvider.LazyGetService<IVendorReadonlyRepository>().GetQueryableAsync();
            var getCompanyTask = await dataverseService.GetCompanyList(stateCode: null);

            //待处理
            if (request.ProcessingStatus == ProcessingStatus.PendingProcessing)
            {
                var taskRecords = await dataverseService.GetApprovelTaskAsync(CurrentUser.Id.ToString(), [WorkflowTypeName.MasterPurchaseOrder], request.ProcessingStatus.Value);

                var pos = queryPO.Where(a => taskRecords.Select(b => b.FormId).ToList().Contains(a.Id))
                    .Join(queryPR.Select(a => new { a.Id, a.ApplicationCode }), a => a.PRId, b => b.Id, (a, b) => new { po = a, pr = b })
                    .GroupJoin(vendor, a => a.po.VendorId, v => v.Id, (p, v) => new { p.po, p.pr, ve = vendor.FirstOrDefault() })
                    .WhereIf(!string.IsNullOrWhiteSpace(request.ApplicationCode), a => a.po.ApplicationCode.Contains(request.ApplicationCode))
                    .WhereIf(!string.IsNullOrWhiteSpace(request.PRApplicationCode), m => m.pr.ApplicationCode.Contains(request.PRApplicationCode))
                    .WhereIf(!string.IsNullOrWhiteSpace(request.ApplyUserName), a => a.po.ApplyUserName.Contains(request.ApplyUserName))
                    .WhereIf(!string.IsNullOrWhiteSpace(request.ApplyUserBuName), a => a.po.ApplyUserBuToDeptName.Contains(request.ApplyUserBuName))
                    .WhereIf(request.CompanyId != null, a => a.po.CompanyId == request.CompanyId)
                    .WhereIf(!string.IsNullOrWhiteSpace(request.VendorName), a => a.po.VendorName.Contains(request.VendorName))
                    .WhereIf(request.VendorPorperty.HasValue, a => a.po.VendorPorperty == request.VendorPorperty)
                    .WhereIf(request.StartTime.HasValue, a => a.po.ApplyTime >= request.StartTime)
                    .WhereIf(request.EndTime.HasValue, a => a.po.ApplyTime <= request.EndTime)
                    .Select(a => new POApprovalResponseDto
                    {
                        Id = a.po.Id,
                        ApplicationCode = a.po.ApplicationCode,
                        Status = a.po.Status,
                        ApplyUserName = a.po.ApplyUserName,
                        ApplyTime = a.po.ApplyTime.ToString("yyyy-MM-dd HH:mm:ss"),
                        ApplyUserBuName = a.po.ApplyUserBuName,
                        ApplyUserBuToDeptName = a.po.ApplyUserBuToDeptName,
                        POType = a.po.POType,
                        VendorName = a.po.VendorName,
                        VendorCode = a.po.VendorCode,
                        VendorPorperty = a.po.VendorPorperty,
                        TotalAmount = a.po.TotalAmount,
                        TotalAmountTax = a.po.TotalAmountTax,
                        CompanyId = a.po.CompanyId,
                        PRId = a.po.PRId,
                        PRApplicationCode = a.pr.ApplicationCode
                    })
                    .ToArray();

                result.TotalCount = pos.Length;
                if (request.IsAsc)
                {
                    result.Items = pos.Join(taskRecords, a => a.Id, a => a.FormId, (a, b) => new { Po = a, Task = b })
                    .OrderBy(a => a.Task.CreatedTime)
                    .Select(a => a.Po)
                    .ToArray();
                }
                else
                {
                    result.Items = pos.Join(taskRecords, a => a.Id, a => a.FormId, (a, b) => new { Po = a, Task = b })
                    .OrderByDescending(a => a.Task.CreatedTime)
                    .Select(a => a.Po)
                    .ToArray();
                }
            }
            else//已完成
            {
                var queryWfTask = await LazyServiceProvider.LazyGetService<IWorkflowTaskReadonlyRepository>().GetQueryableAsync();
                var queryLinq = queryPO
                    .Join(queryPR.Select(a => new { a.Id, a.ApplicationCode }), a => a.PRId, b => b.Id, (a, b) => new { po = a, pr = b })
                    .GroupJoin(vendor, a => a.po.VendorId, v => v.Id, (p, v) => new { p.po, p.pr, ve = vendor.FirstOrDefault() })
                    .WhereIf(!string.IsNullOrWhiteSpace(request.ApplicationCode), a => a.po.ApplicationCode.Contains(request.ApplicationCode))
                    .WhereIf(!string.IsNullOrWhiteSpace(request.PRApplicationCode), m => m.pr.ApplicationCode.Contains(request.PRApplicationCode))
                    .WhereIf(!string.IsNullOrWhiteSpace(request.ApplyUserName), a => a.po.ApplyUserName.Contains(request.ApplyUserName))
                    .WhereIf(!string.IsNullOrWhiteSpace(request.ApplyUserBuName), a => a.po.ApplyUserBuToDeptName.Contains(request.ApplyUserBuName))
                    .WhereIf(request.CompanyId != null, a => a.po.CompanyId == request.CompanyId)
                    .WhereIf(!string.IsNullOrWhiteSpace(request.VendorName), a => a.po.VendorName.Contains(request.VendorName))
                    .WhereIf(request.VendorPorperty.HasValue, a => a.po.VendorPorperty == request.VendorPorperty)
                    .WhereIf(request.StartTime.HasValue, a => a.po.ApplyTime >= request.StartTime)
                    .WhereIf(request.EndTime.HasValue, a => a.po.ApplyTime <= request.EndTime)
                    .Join(queryWfTask.Where(a => a.ApprovalId == CurrentUser.Id), a => a.po.Id, a => a.FormId, (a, b) => new { a.po, a.pr, Task = b })
                    .Select(a => new POApprovalResponseDto
                    {
                        Id = a.po.Id,
                        ApplicationCode = a.po.ApplicationCode,
                        Status = a.po.Status,
                        ApplyUserName = a.po.ApplyUserName,
                        ApplyTime = a.po.ApplyTime.ToString("yyyy-MM-dd HH:mm:ss"),
                        ApplyUserBuName = a.po.ApplyUserBuName,
                        ApplyUserBuToDeptName = a.po.ApplyUserBuToDeptName,
                        POType = a.po.POType,
                        VendorName = a.po.VendorName,
                        VendorCode = a.po.VendorCode,
                        VendorPorperty = a.po.VendorPorperty,
                        TotalAmount = a.po.TotalAmount,
                        TotalAmountTax = a.po.TotalAmountTax,
                        CompanyId = a.po.CompanyId,
                        PRId = a.po.PRId,
                        PRApplicationCode = a.pr.ApplicationCode,
                        ApprovalTime = a.Task.ApprovalTime
                    });

                result.TotalCount = queryLinq.Count();
                if (request.IsAsc)
                    result.Items = queryLinq.OrderBy(a => a.ApprovalTime).Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToList();
                else
                    result.Items = queryLinq.OrderByDescending(a => a.ApprovalTime).Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToList();
            }

            foreach (var item in result.Items)
                item.CompanyName = item.CompanyId.HasValue ? getCompanyTask.FirstOrDefault(x => x.Id == item.CompanyId)?.CompanyName : null;

            return result;
        }

        /// <summary>
        /// 导出我审批的列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<Stream> ExportPOApprovalListAsync(GetPOApprovalRequestDto request)
        {
            using MemoryStream stream = new();
            var pOList = await GetPOApprovalListAsync(request, false);
            List<object> list = [];
            var resultData = ObjectMapper.Map<List<POApprovalResponseDto>, List<POApprovalBaseResponseDto>>(pOList.Items.ToList());
            list.Add(resultData);
            stream.SaveAs(list, true, "Sheet1");
            stream.Seek(0, SeekOrigin.Begin);
            return stream;
        }
        #endregion

        #region 私有方法

        private async Task QueryPoExportDataAsync(GetPOApplicationListRequest request, List<PurCategoryConfig> categorys, IEnumerable<DictionaryDto> apsPropertys,
            ConcurrentBag<ExportPOListResponseDto> allResults, ConcurrentBag<KeyValuePair<DateTime, DateTime>> errorTimes,
            SemaphoreSlim semaphore, KeyValuePair<DateTime, DateTime> dateTime)
        {
            await semaphore.WaitAsync();
            try
            {
                using (var scope = _serviceProvider.CreateScope())
                {
                    var poQuery = (await scope.ServiceProvider.GetRequiredService<IPurPOApplicationReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
                    var podQuery = (await scope.ServiceProvider.GetRequiredService<IPurPOApplicationDetailsReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
                    var prQuery = (await scope.ServiceProvider.GetRequiredService<IPurPRApplicationReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
                    var prdQuery = (await scope.ServiceProvider.GetRequiredService<IPurPRApplicationDetailReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
                    var bdQuery = (await scope.ServiceProvider.GetRequiredService<IPurBDApplicationReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
                    var bddQuery = (await scope.ServiceProvider.GetRequiredService<IPurBDApplicationDetailsReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
                    var bdSupplierQuery = (await scope.ServiceProvider.GetRequiredService<IPurBDApplicationSupplierDetailsReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
                    var bwQuery = (await scope.ServiceProvider.GetRequiredService<IPurBWApplicationReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
                    var userQuery = (await scope.ServiceProvider.GetRequiredService<IIdentityUserReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
                    var subBudgetQuery = (await scope.ServiceProvider.GetRequiredService<IBdSubBudgetReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
                    var wfQuery = (await scope.ServiceProvider.GetRequiredService<IWfApprovalTaskReadonlyRepository>().GetQueryableAsync()).AsNoTracking();

                    List<string> prTypes = [];
                    if (request.PRType?.Count > 0)
                    {
                        foreach (var row in request.PRType)
                            prTypes.Add(string.Join(",", row));
                    }
                    //查询PO
                    var poQueryable = podQuery.GroupJoin(poQuery, a => a.POApplicationId, b => b.Id, (a, b) => new { pod = a, pos = b })
                        .SelectMany(a => a.pos.DefaultIfEmpty(), (a, b) => new { a.pod, po = b })
                        .GroupJoin(prQuery, a => a.po.PRId, b => b.Id, (a, b) => new { a.pod, a.po, prs = b })
                        .SelectMany(a => a.prs.DefaultIfEmpty(), (a, b) => new
                        {
                            a.po.Id,
                            a.po.PRId,
                            a.po.ApplicationCode,
                            a.po.POType,
                            a.po.ApplyTime,
                            a.po.Status,
                            a.po.DeliveryDate,
                            a.po.CompanyId,
                            a.po.ApplyUserName,
                            a.po.FirstLevelApprover,
                            a.po.Currency,
                            a.po.VendorName,
                            a.po.ApprovedDate,
                            a.po.Saving,
                            a.po.VendorPorperty,
                            a.po.ApsPorperty,
                            a.po.PRType,
                            a.po.DeliveryAddress,
                            a.po.PaymentCondition,
                            a.po.Remark,
                            a.po.ApplyUserBuToDeptName,
                            a.pod.PRDetailId,
                            a.pod.Content,
                            a.pod.UnitPrice,
                            a.pod.Quantity,
                            a.pod.TotalAmount,
                            a.pod.TotalAmountNoTax,
                            a.pod.InvoiceType,
                            a.pod.TaxRate,
                            poApplyUserId = a.po.ApplyUserId,
                            prApplyUserId = b.ApplyUserId,
                            PRApplicationCode = b.ApplicationCode,
                            PRApplyUserDeptName = b.ApplyUserDeptName
                        })
                        .Where(a => a.Status != PurOrderStatus.Draft)
                        .Where(m => m.ApplyTime > dateTime.Key)
                        .Where(m => m.ApplyTime <= dateTime.Value)
                        .WhereIf(!string.IsNullOrWhiteSpace(request.ApplicationCode), m => m.ApplicationCode.Contains(request.ApplicationCode))
                        .WhereIf(request.ApplyUserId.HasValue, m => m.poApplyUserId == request.ApplyUserId)
                        .WhereIf(!string.IsNullOrWhiteSpace(request.ApplyUserName), m => m.ApplyUserName.Contains(request.ApplyUserName))
                        .WhereIf(!string.IsNullOrWhiteSpace(request.VendorName), m => m.VendorName.Contains(request.VendorName))
                        .WhereIf(!string.IsNullOrWhiteSpace(request.ApplicationDept), m => m.ApplyUserBuToDeptName.Contains(request.ApplicationDept))
                        .WhereIf(!string.IsNullOrWhiteSpace(request.ApsPorperty), m => m.ApsPorperty.Contains(request.ApsPorperty))
                        .WhereIf(request.VendorPorperty.HasValue, m => m.VendorPorperty == request.VendorPorperty)
                        .WhereIf(request.POType.HasValue, m => m.POType == request.POType)
                        .WhereIf(request.CompanyId.HasValue, m => m.CompanyId == request.CompanyId)
                        .WhereIf(request.Status.HasValue, m => m.Status == request.Status)
                        .WhereIf(prTypes.Count > 0, m => prTypes.Contains(m.PRType))
                        .WhereIf(request.PRApplyUserId.HasValue, m => m.prApplyUserId == request.PRApplyUserId)
                        .WhereIf(!string.IsNullOrWhiteSpace(request.PRApplicationCode), m => m.PRApplicationCode.Contains(request.PRApplicationCode))
                        .WhereIf(!string.IsNullOrWhiteSpace(request.PRApplicationDept), m => m.PRApplyUserDeptName.Contains(request.PRApplicationDept));

                    var poDatas = poQueryable.ToList();//PO 数据

                    var prIds = poDatas.Select(a => a.PRId).Distinct();//PRID

                    //查询PR
                    var prQueryable = prQuery.Where(a => prIds.Contains(a.Id)).WhereIf(!string.IsNullOrWhiteSpace(request.PRApplicationCode), m => m.ApplicationCode.Contains(request.PRApplicationCode))
                        .GroupJoin(prdQuery, a => a.Id, b => b.PRApplicationId, (a, b) => new { pr = a, prds = b })
                        .SelectMany(a => a.prds.DefaultIfEmpty(), (a, b) => new
                        {
                            b.Id,
                            b.PushTime,
                            b.Content,
                            b.UnitPrice,
                            b.Quantity,
                            b.TotalAmount,
                            b.EstimateDate,
                            a.pr.ApplicationCode,
                            a.pr.SubBudgetId,
                            a.pr.ApplyUserIdName,
                            a.pr.ApplyUserBuName,
                            a.pr.ApplyUserId,
                            a.pr.ApprovedDate,
                            a.pr.CompanyIdName,
                            a.pr.SubBudgetCode,
                            a.pr.ApplyTime,
                            b.BWApplicationId,
                            PRUserEmail = userQuery.FirstOrDefault(u => u.Id == a.pr.ApplyUserId).Email
                        });

                    var prDatas = prQueryable.ToList();//PR 数据

                    var userIds = prDatas.Select(a => a.ApplyUserId).Distinct();//UserId
                    var subBudgetIds = prDatas.Select(a => a.SubBudgetId).Distinct();//子预算ID
                    var bwIds = prDatas.Where(a => a.BWApplicationId.HasValue).Select(a => a.BWApplicationId);//竞价豁免ID

                    var subBudgetDatas = subBudgetQuery.Where(a => subBudgetIds.Contains(a.Id)).Select(a => new { a.Id, a.OwnerId })
                        .Join(userQuery.Select(b => new { b.Id, b.Name }), a => a.OwnerId, b => b.Id, (a, b) => new { a.Id, a.OwnerId, b.Name }).ToList();//子预算数据

                    var bwDatas = bwQuery.Where(a => bwIds.Contains(a.Id)).Select(a => new { a.ApplicationCode, a.Id, a.ApprovedTime }).ToList();//竞价豁免数据

                    PurBDStatus[] bdStatus = [PurBDStatus.Rejected, PurBDStatus.Invalid, PurBDStatus.Draft];//排除Bidding状态
                                                                                                            //查询BD
                    var bdQueryable = bdQuery.Where(a => prIds.Contains(a.PRApplicationId.Value) && !bdStatus.Contains(a.Status))
                        .GroupJoin(bddQuery, a => a.Id, b => b.BDApplicationId, (a, b) => new { bd = a, bdds = b })
                        .SelectMany(a => a.bdds.DefaultIfEmpty(), (a, b) => new
                        {
                            a.bd.Id,
                            a.bd.Status,
                            a.bd.ApplicationCode,
                            a.bd.ApprovedTime,
                            a.bd.SingleChoice,
                            b.PRDetailId,
                        })
                        .AsQueryable();

                    var bdDatas = bdQueryable.ToList();//Bidding 数据

                    var bdSupplier = bdSupplierQuery.Where(a => bdDatas.Select(x => x.Id).Contains(a.BDApplicationId)).Select(a => new { a.BDApplicationId, a.Quantity, a.TotalAmount }).ToList();

                    var poApprovingIds = poDatas.Where(a => a.Status == PurOrderStatus.Approving).Select(a => a.Id).Distinct().ToList();
                    var wfTask = wfQuery.Where(a => poApprovingIds.Contains(a.FormId)).GroupBy(a => a.FormId).Select(a => a.OrderByDescending(x => x.CreationTime).FirstOrDefault()).ToList();

                    var datas = poDatas.Select(a =>
                    {
                        var pr = prDatas.Where(p => p.Id == a.PRDetailId).FirstOrDefault();
                        var bd = bdDatas.Where(p => p.PRDetailId == a.PRDetailId).FirstOrDefault();
                        var bw = pr == null ? null : bwDatas.Where(p => p.Id == pr.BWApplicationId).FirstOrDefault();
                        var subBudget = pr == null ? null : (pr.SubBudgetId == null ? null : subBudgetDatas.Where(p => p.Id == pr.SubBudgetId).FirstOrDefault());

                        decimal? bdAvgResult = null;
                        if (bd != null)
                        {
                            var supplier = bdSupplier.Where(x => x.BDApplicationId == bd.Id).ToList();
                            var totalAmountSum = supplier.Sum(item => item.TotalAmount);
                            var quantitySum = supplier.Sum(item => item.Quantity);
                            if (totalAmountSum == 0 || quantitySum == 0)
                                bdAvgResult = null;
                            else
                                bdAvgResult = totalAmountSum / quantitySum;
                        }

                        string apsPorpertyName = "";
                        if (!string.IsNullOrWhiteSpace(a.ApsPorperty))
                        {
                            var aps = apsPropertys.Where(x => x.Code == a.ApsPorperty).FirstOrDefault();
                            apsPorpertyName = aps?.Name;
                        }

                        return new ExportPOListResponseDto
                        {
                            ApplicationCode = a.ApplicationCode,
                            OrderTypeName = a.POType.GetDescription(),
                            ApplyTime = a.ApplyTime.ToString("yyyy-MM-dd"),
                            StatusName = a.Status.GetDescription(),
                            CurrentOperator = GetCurrentOperator(wfTask, a.Id, a.Status, a.ApplyUserName),
                            ApplyUserName = a.ApplyUserName,
                            FirstLevelApprover = a.FirstLevelApprover,
                            Currency = a.Currency,
                            VendorName = a.VendorName,
                            PrApplicationCode = pr?.ApplicationCode,
                            PrApplyUserName = pr?.ApplyUserIdName,
                            PrApplyUserEmail = pr?.PRUserEmail,
                            PrApplyTime = pr?.ApplyTime?.ToString("yyyy-MM-dd"),
                            PrApprovalDate = pr?.ApprovedDate?.ToString("yyyy-MM-dd"),
                            PushDate = pr?.PushTime?.ToString("yyyy-MM-dd"),
                            BdApplicationCode = bd?.ApplicationCode,
                            BdCompleteDate = bd?.ApprovedTime?.ToString("yyyy-MM-dd"),
                            BdTypeName = bd?.SingleChoice.GetDescription(),
                            BdAvgResult = bdAvgResult,
                            BwApplicationCode = bw?.ApplicationCode,
                            BwApprovalDate = bw?.ApprovedTime?.ToString("yyyy-MM-dd"),
                            ApprovalDate = a.ApprovedDate?.ToString("yyyy-MM-dd"),
                            DeliveryDate = a.DeliveryDate.ToString("yyyy-MM-dd"),
                            PrContent = pr?.Content,
                            PrPrice = pr?.UnitPrice,
                            PrQuantity = pr?.Quantity,
                            Content = a.Content,
                            Price = a.UnitPrice,
                            Quantity = a.Quantity,
                            //对应PR明细行单价-(PO明细行单价×数量)/PR申请数量
                            PriceDecline = pr?.UnitPrice.Value - (a.UnitPrice * (decimal)a.Quantity) / (decimal)pr?.Quantity,
                            OrderAmount = a.TotalAmount,
                            SavingAmount = a.Saving,
                            //该行对应PR明细行的金额，减去该行的总价(含税)——如果有多行同时关联至同一个PR明细行，此处每一行均填为PR明细行金额减去这些行的总含税价
                            PoNoTaxAmount = pr?.TotalAmount.Value - a.TotalAmount,
                            PoTaxAmount = a.TotalAmount - a.TotalAmountNoTax,
                            VATType = InvoiceType.InvoiceDictionary.FirstOrDefault(x => x.Key == a.InvoiceType).Value,
                            VATRate = a.TaxRate,
                            CompanyName = pr?.CompanyIdName,
                            ApplyBuName = pr?.ApplyUserBuName,
                            BudgetCode = pr?.SubBudgetCode,
                            BudgetManager = subBudget?.Name,
                            ExpectedDate = pr?.EstimateDate?.ToString("yyyy-MM-dd"),
                            Aps = a.VendorPorperty ? "APS" : "不适用",
                            ApsType = apsPorpertyName,
                            Category = GetPoCategory(categorys, a.PRType),
                            DeliveryLocation = a.DeliveryAddress,
                            PaymentCondition = a.PaymentCondition,
                            Remark = a.Remark
                        };
                    })
                    .WhereIf(!string.IsNullOrWhiteSpace(request.PRApplicationCode), m => m.PrApplicationCode.Contains(request.PRApplicationCode));

                    foreach (var item in datas.ToList())
                    {
                        allResults.Add(item);
                    }
                }
            }
            catch (Exception ex)
            {
                errorTimes.Add(dateTime);
            }
            finally
            {
                semaphore.Release();
            }
        }

        /// <summary>
        /// 获取主采购循环审批，第一审批人
        /// </summary>
        /// <param name="dataverseService"></param>
        /// <param name="deptId"></param>
        /// <param name="applyUserId"></param>
        /// <param name="applyUserName"></param>
        /// <returns></returns>
        private async Task<string> GetFirstLevelApprover(IDataverseService dataverseService, Guid deptId, Guid applyUserId)
        {
            try
            {
                var orgAndStaffs = await dataverseService.GetOrgAndStaffByFlowAsync(deptId, PositionType.MainProcurement);
                foreach (var item in orgAndStaffs)
                {
                    if (item.Staffs == null || !item.Staffs.Any())
                        continue;

                    var exCodes = item.Staffs.Where(a => a.Extensioncode.HasValue).OrderByDescending(a => a.Extensioncode).Select(a => a.Extensioncode).ToList();
                    foreach (var code in exCodes)
                    {
                        // 找到所有非申请人的工作人员作为第一审批人
                        var approvers = item.Staffs.Where(s => s.StaffId != applyUserId && s.Extensioncode == code).ToList();

                        if (approvers.Any())
                        {
                            // 返回这些审批人的姓名，用逗号分隔
                            return string.Join(", ", approvers.Select(a => a.StaffName));
                        }
                    }
                }
                // 如果没有找到合适的审批人，则返回 
                return "";
            }
            catch (Exception ex)
            {
                return "";
            }
        }


        private string GetCurrentOperator(List<WfApprovalTask> wfs, Guid poId, PurOrderStatus poStatus, string applyUserName)
        {
            string approver = "";
            //若PO状态为审批中(2)，则填入最新的审批人；
            //若PO状态为关闭或作废 / 拒绝(4, 6, 7)，则为空；
            //若PO状态非关闭(1, 5, 9, 10)，则为PO申请人；
            try
            {
                switch (poStatus)
                {
                    case PurOrderStatus.Approving:
                        var wf = wfs.Where(a => a.FormId == poId).FirstOrDefault();
                        if (wf != null)
                        {
                            var approvers = JsonSerializer.Deserialize<List<ApproverDto>>(wf.Approver);
                            approver = string.Join(",", approvers.Select(a => a.Name).ToList());
                        }

                        break;
                    case PurOrderStatus.Approved:
                        break;
                    case PurOrderStatus.ApplierConfirmed:
                    case PurOrderStatus.Return:
                    case PurOrderStatus.Withdraw:
                    case PurOrderStatus.InitiateReceipt:
                        approver = applyUserName;
                        break;
                    case PurOrderStatus.Closed:
                    case PurOrderStatus.Rejected:
                    case PurOrderStatus.Invalid:
                        approver = "";
                        break;
                }
                return approver;
            }
            catch (Exception ex)
            {
                return approver;
            }
        }

        /// <summary>
        /// 获取采购品类
        /// </summary>
        /// <param name="purCategoryConfigs"></param>
        /// <param name="categoryId"></param>
        /// <returns></returns>
        private string GetPoCategory(List<PurCategoryConfig> purCategoryConfigs, string categoryId)
        {
            if (string.IsNullOrWhiteSpace(categoryId))
                return "";
            try
            {
                var poCategory = purCategoryConfigs.Where(a => categoryId.Split(',').Select(Guid.Parse).Contains(a.Id)).ToList();
                return poCategory.Count < 1 ? null :
                    poCategory.OrderBy(a => a.Hierarchy).Select(a => a.NameEn).JoinAsString(" / ");
            }
            catch (Exception ex)
            {

                return "";
            }
        }

        /// <summary>
        /// 校验Msa和Sow跟Po的合法性
        /// </summary>
        /// <param name="poCreateOrUpdateRequestDto"></param>
        /// <returns></returns>
        async Task<MessageResult> CheckPoMsaSow(POCreateOrUpdateRequestDto poCreateOrUpdateRequestDto)
        {
            var hasMsa = poCreateOrUpdateRequestDto.MsaId.HasValue;
            //如果Sow是选择现有文件
            if (poCreateOrUpdateRequestDto.Sow?.IsAddNew == false)
            {
                if (hasMsa != poCreateOrUpdateRequestDto.Sow.Id.HasValue)
                    return MessageResult.FailureResult("主服务协议和工作说明书必须同时存在");

                var querySow = await LazyServiceProvider.LazyGetService<IMsaStatementOfWorkReadonlyRepository>().GetQueryableAsync();
                var sow = await querySow
                .Where(a => a.Id == poCreateOrUpdateRequestDto.Sow.Id)
                .Include(a => a.Msa)
                .ThenInclude(a => a.Companies)
                .FirstOrDefaultAsync();

                //判断Msa和Po的关联性是否正确
                if (!sow.Msa.Companies.Any(a => a.CompanyId == poCreateOrUpdateRequestDto.CompanyId) || sow.Msa.VendorName != poCreateOrUpdateRequestDto.VendorName)
                    return MessageResult.FailureResult("选择的主服务协议与PO的关联性不正确");

                //判断Msa和Sow的关联性是否正确
                if (sow.MsaId != poCreateOrUpdateRequestDto.MsaId || sow.BuId != poCreateOrUpdateRequestDto.ApplyUserBu || sow.CompanyId != poCreateOrUpdateRequestDto.CompanyId || sow.VendorName != poCreateOrUpdateRequestDto.VendorName)
                    return MessageResult.FailureResult("选择的主服务协议和工作说明书关联性不正确");
            }
            //新上传
            else if (hasMsa && poCreateOrUpdateRequestDto.Sow?.AttachmentInformations?.Any() == false)
                return MessageResult.FailureResult("主服务协议和工作说明书必须同时存在");

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 保存Sow
        /// </summary>
        /// <param name="poCreate"></param>
        /// <param name="po"></param>
        /// <returns></returns>
        async Task<Guid?> SaveSow(POCreateOrUpdateRequestDto poCreate, PurPOApplication po)
        {
            Guid? sowId = null;

            //sow object
            var sowDto = new SowPostDto
            {
                MsaId = po.MsaId,
                BuId = po.ApplyUserBu,
                BuName = po.ApplyUserBuName,
                VendorName = po.VendorName,
                Remark = po.Remark,
                AttachmentFileIds = poCreate.Sow.AttachmentInformations?.Select(a => a.AttachmentId).JoinAsString(",")
            };

            //公司
            if (poCreate.CompanyId.HasValue)
            {
                var companies = await LazyServiceProvider.LazyGetService<IDataverseService>().GetCompanyAsync(poCreate.CompanyId.ToString());
                var company = companies.FirstOrDefault();
                sowDto.CompanyId = company?.Id;
                sowDto.CompanyCode = company?.CompanyCode;
                sowDto.CompanyName = company?.CompanyName;
            }

            //采购品类
            if (!string.IsNullOrEmpty(po.PRType))
            {
                var firstLevelId = po.PRType.Split(",").FirstOrDefault();
                var firstLevelName = po.PRTypeName.Split("/").FirstOrDefault();

                if (Guid.TryParse(firstLevelId, out Guid categoryId))
                    sowDto.CategoryId = categoryId;

                sowDto.CategoryName = firstLevelName;
            }

            //新创建sow
            var sowService = LazyServiceProvider.LazyGetService<ISowService>();
            if (poCreate.Sow?.IsAddNew == true)
            {
                var result = await sowService.CreateSowAsync(sowDto);
                sowId = (Guid)result.Data;
            }
            else if (poCreate.Sow?.Id.HasValue == true)//修改sow
            {
                sowDto.Id = poCreate.Sow.Id;
                sowId = poCreate.Sow.Id;

                var result = await sowService.UpdateSowAsync(sowDto);
            }

            //如果po之前已经关联了sow，并且跟本次不是一个sow，这里需要删除掉之前的sow数据
            if (po.SowId.HasValue && po.SowId != sowId)
                await sowService.DeleteSowAsync(new SowDeleteDto { SowId = po.SowId.Value, PoId = po.Id });

            return sowId;
        }

        /// <summary>
        /// 创建PO数据
        /// </summary>
        /// <param name="poCreate"></param>
        /// <returns></returns>
        private async Task<PurPOApplication> CreatePurPOApplication(POCreateOrUpdateRequestDto poCreate)
        {
            var pOApplicationRepository = LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>();
            var pOApplicationDetailsRepository = LazyServiceProvider.LazyGetService<IPurPOApplicationDetailsRepository>();
            var pOApplication = ObjectMapper.Map<POCreateOrUpdateRequestDto, PurPOApplication>(poCreate);

            pOApplication.PRTypeName = await GetPRTypeNames(pOApplication.PRType);

            pOApplication.ApsPorperty = poCreate.ApsPorperty;
            pOApplication.ApplyUserId = poCreate.ApplyUserId;
            pOApplication.ApplyUserName = poCreate.ApplyUserName;
            pOApplication.ApplyUserDept = poCreate.ApplyUserDept;
            pOApplication.ApplyUserBuToDeptName = poCreate.ApplyUserBuToDeptName;
            pOApplication.ApplyUserBu = poCreate.ApplyUserBu;
            pOApplication.ApplyUserBuName = poCreate.ApplyUserBuName;

            if (poCreate.Files != null)
            {
                pOApplication.AttachmentFile = poCreate.Files.Any() ? string.Join(",", poCreate.Files.Select(a => a.AttachmentId.ToString()).ToList()) : "";
            }

            //保存Sow
            if (poCreate.MsaId.HasValue && (poCreate.Sow?.IsAddNew == true || poCreate.Sow?.Id.HasValue == true))
            {
                var result = await SaveSow(poCreate, pOApplication);
                pOApplication.SowId = result;
            }

            await InsertAndGenerateSerialNoAsync(pOApplicationRepository, pOApplication, "O");

            var pOApplicationDetails = ObjectMapper.Map<List<POCreateDetailRequestDto>, List<PurPOApplicationDetails>>(poCreate.POApplicationDetails);
            pOApplicationDetails.ForEach(a =>
            {
                a.POApplicationId = pOApplication.Id;
                if (InvoiceType.GiftIncrease.Equals(a.InvoiceType))
                {
                    //税率依然以用户填写为准，但需要视为0
                    a.TaxAmount = 0M;
                    a.TotalAmountNoTax = a.TotalAmount;
                }
            });
            await pOApplicationDetailsRepository.InsertManyAsync(pOApplicationDetails, true);
            return pOApplication;
        }

        /// <summary>
        /// 更新PO数据
        /// </summary>
        /// <param name="poCreate"></param>
        /// <returns></returns>
        private async Task<PurPOApplication> UpdatePurPOApplication(POCreateOrUpdateRequestDto poUpdate)
        {
            var pOApplicationRepository = LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>();
            var pOApplicationDetailsRepository = LazyServiceProvider.LazyGetService<IPurPOApplicationDetailsRepository>();
            var querypo = await pOApplicationRepository.GetQueryableAsync();
            //更新
            var po = querypo.Where(a => a.Id == poUpdate.POId.Value).FirstOrDefault();
            ObjectMapper.Map(poUpdate, po);

            po.PRTypeName = await GetPRTypeNames(po.PRType);

            po.ApsPorperty = poUpdate.ApsPorperty;

            //申请人提交时，才修改发起人和发起部门
            if (po.ApplyUserId == CurrentUser.Id)
            {
                po.ApplyUserDept = poUpdate.ApplyUserDept;
                po.ApplyUserBuToDeptName = poUpdate.ApplyUserBuToDeptName;
                po.ApplyUserBu = poUpdate.ApplyUserBu;
                po.ApplyUserBuName = poUpdate.ApplyUserBuName;
            }

            if (poUpdate.Files != null)
            {
                po.AttachmentFile = poUpdate.Files.Any() ? string.Join(",", poUpdate.Files.Select(a => a.AttachmentId.ToString()).ToList()) : "";
            }

            //有Msa和Sow时，保存Sow
            if (poUpdate.MsaId.HasValue && (poUpdate.Sow?.IsAddNew == true || poUpdate.Sow?.Id.HasValue == true))
            {
                var result = await SaveSow(poUpdate, po);
                po.SowId = result;
            }
            //之前有Sow时执行删除Sow操作
            else if (po.SowId.HasValue)
            {
                await LazyServiceProvider.LazyGetService<ISowService>().DeleteSowAsync(new SowDeleteDto { SowId = po.SowId.Value, PoId = po.Id });
                po.SowId = null;
            }

            var updateResult = await pOApplicationRepository.UpdateAsync(po, true);
            if (updateResult != null)
            {
                await pOApplicationDetailsRepository.DeleteDirectAsync(a => a.POApplicationId == updateResult.Id);
                var pOApplicationDetails = ObjectMapper.Map<List<POCreateDetailRequestDto>, List<PurPOApplicationDetails>>(poUpdate.POApplicationDetails);
                pOApplicationDetails.ForEach(a =>
                {
                    a.POApplicationId = updateResult.Id;
                    if (InvoiceType.GiftIncrease.Equals(a.InvoiceType))
                    {
                        //税率依然以用户填写为准，但需要视为0
                        a.TaxAmount = 0M;
                        a.TotalAmountNoTax = a.TotalAmount;
                    }
                });
                await pOApplicationDetailsRepository.InsertManyAsync(pOApplicationDetails, true);
            }
            return updateResult;
        }

        /// <summary>
        /// 根据 采购品类ID 获取采购品类名字
        /// </summary>
        /// <param name="prTypeId"></param>
        /// <returns></returns>
        private async Task<string> GetPRTypeNames(string prTypeId)
        {
            if (string.IsNullOrWhiteSpace(prTypeId))
                return "";
            var prTypeIds = prTypeId.Split(",").Select(Guid.Parse).ToList();
            var categoryConfigs = await LazyServiceProvider.LazyGetService<IPurCategoryConfigReadonlyRepository>().GetListAsync(a => prTypeIds.Contains(a.Id));
            return categoryConfigs.Where(a => prTypeIds.Contains(a.Id)).OrderBy(a => a.Hierarchy).Select(a => a.NameEn).JoinAsString(" / ");
        }

        /// <summary>
        /// PO添加审批任务
        /// </summary>
        /// <param name="applyUserBu"></param>
        /// <param name="applyUserId"></param>
        /// <param name="draftId"></param>
        /// <param name="totalAmount"></param>
        /// <returns></returns>
        private async Task<bool> CreatePOWorkflowAsync(Guid applyUserBu, Guid applyUserId, Guid draftId, string draftCode, decimal totalAmount, string remark)
        {

            var approvalRecord = LazyServiceProvider.LazyGetService<IInitializeApprovalRecordService>();
            //创建审批任务
            var exemptType = EnumUtil.GetEnumIdValues<WorkflowTypeName>().ToDictionary(a => (WorkflowTypeName)a.Key, a => a.Value);

            var createApproval = new CreateApprovalDto
            {
                Name = exemptType[WorkflowTypeName.MasterPurchaseOrder],
                Department = applyUserBu.ToString(),
                BusinessFormId = draftId.ToString(),
                BusinessFormNo = draftCode,
                BusinessFormName = EntitiesNameConsts.NameConsts.POApplication,
                Submitter = CurrentUser.Id?.ToString(),
                OriginalApprovalId = applyUserId,
                FormData = "{\"allAmount\" : " + totalAmount + "}",
                WorkflowType = WorkflowTypeName.MasterPurchaseOrder,
                InstanceName = $"{exemptType[WorkflowTypeName.MasterPurchaseOrder]}-{DateTime.Now.ToString("yyMMdd-hhmmss")}",
                Remark = remark
            };
            await approvalRecord.InsertInitApprovalRecordAsync(new InitializeApprovalRecordDto
            {
                BusinessFormName = EntitiesNameConsts.NameConsts.POApplication,
                BusinessId = draftId,
                InstanceId = Guid.NewGuid(),
                Status = InitApprovalRecordStatus.Pending,
                MaxRetries = 3,
                RequestContent = JsonSerializer.Serialize(createApproval)
            });
            return true;
        }

        /// <summary>
        /// 检查是否重复提交（创建）
        /// </summary>
        /// <param name="prdIds"></param>
        /// <param name="isDraft">是否排除草稿</param>
        /// <returns></returns>
        private async Task<bool> CheckIsResubmitAsync(List<Guid> prdIds, bool isDraft = true)
        {
            var podQuery = (await LazyServiceProvider.LazyGetService<IPurPOApplicationDetailsRepository>().GetQueryableAsync()).AsNoTracking();
            var poQuery = (await LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            List<PurOrderStatus> status = [PurOrderStatus.Invalid, PurOrderStatus.Rejected];
            if (isDraft)
                status.Add(PurOrderStatus.Draft);

            var pos = poQuery.Where(a => !status.Contains(a.Status))
                .GroupJoin(podQuery, a => a.Id, b => b.POApplicationId, (a, b) => new { po = a, pods = b })
                .SelectMany(a => a.pods.DefaultIfEmpty(), (a, b) => new { a.po, pod = b })
                .Where(a => prdIds.Contains(a.pod.PRDetailId.Value));
            if (pos.Any())
                return false;
            return true;
        }

        /// <summary>
        /// 提交时必填字段校验
        /// </summary>
        /// <param name="pOCreateRequest"></param>
        /// <returns>(bool,string)=》1、验证结果状态；2、验证结果Message</returns>
        private async Task<(bool, string)> CheckCreateRequiredFieldsAsync(POCreateOrUpdateRequestDto pOCreateRequest)
        {
            var queryVendor = await LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync();
            if (!CheckIdIsGuid(pOCreateRequest.PRApplicationDetailId))
            {
                return (false, "PR信息有误");
            }
            if (pOCreateRequest.POApplicationDetails.Any())
            {
                foreach (var item in pOCreateRequest.POApplicationDetails)
                {
                    if (!item.PRDetailId.HasValue)
                        return (false, "未关联PR明细行");
                }
            }
            var prDetailIds = pOCreateRequest.PRApplicationDetailId.Split(',').Select(Guid.Parse).ToList();
            foreach (var item in prDetailIds)
            {
                var thisPoDetail = pOCreateRequest.POApplicationDetails?.Where(a => a.PRDetailId == item)?.FirstOrDefault();
                if (thisPoDetail == null)
                    return (false, "采购明细必须与PR明细存在关联");
            }
            if (!queryVendor.Any(a => a.Id == pOCreateRequest.VendorId))
            {
                return (false, "供应商不存在");
            }
            if (string.IsNullOrWhiteSpace(pOCreateRequest.RegCertificateAddress))
            {
                return (false, "请填写供应商地址");
            }
            if (string.IsNullOrWhiteSpace(pOCreateRequest.ContactName))
            {
                return (false, "联系人不能为空");
            }
            if (string.IsNullOrWhiteSpace(pOCreateRequest.ContactPhone))
            {
                return (false, "电话不能为空");
            }
            if (!EmailValidationHelper.IsValidEmail(pOCreateRequest.ContactEmail))
            {
                return (false, "邮箱格式不正确");
            }
            //一期不做
            //if (string.IsNullOrWhiteSpace(pOCreateRequest.PRType))
            //{
            //    return (false, "采购品类未选择");
            //}
            if (string.IsNullOrWhiteSpace(pOCreateRequest.DeliveryType))
            {
                return (false, "货运方式未选择");
            }
            if (string.IsNullOrWhiteSpace(pOCreateRequest.DeliveryAddress))
            {
                return (false, "货运地点未选择");
            }
            if (string.IsNullOrWhiteSpace(pOCreateRequest.PaymentCondition))
            {
                return (false, "付款条件未选择");
            }
            return (true, "Success");
        }
        /// <summary>
        /// 检查总金额
        /// </summary>
        /// <param name="pOCreateRequest"></param>
        /// <returns>(bool,string)=》1、验证结果状态；2、验证结果Message</returns>
        private async Task<(bool, string)> CheckPRAndPOMatchAsync(POCreateOrUpdateRequestDto pOCreateRequest)
        {
            var queryable = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var prDetailIds = pOCreateRequest.PRApplicationDetailId.Split(',').Select(Guid.Parse).ToList();
            var prDetails = queryable.Where(a => prDetailIds.Contains(a.Id)).ToList();
            var poDetails = pOCreateRequest.POApplicationDetails;
            foreach (var prDetail in prDetails)
            {
                var poInPrDetail = poDetails.Where(a => a.PRDetailId == prDetail.Id).ToList();
                if (prDetail.TotalAmount < poInPrDetail.Sum(a => a.TotalAmount))
                {
                    return (false, $"采购明细总金额大于了PR明细（{prDetail.RowNo}）总金额");
                }
            }
            return (true, "Success");
        }
        /// <summary>
        /// 检查Id是否是GUID
        /// </summary>
        /// <param name="detailIds">逗号隔开的Guid</param>
        /// <returns></returns>
        private bool CheckIdIsGuid(string detailIds)
        {
            var ids = detailIds.Split(',');
            // 使用 LINQ 查询所有字符串是否都能成功转换为 Guid
            return !ids.Any(id => !Guid.TryParse(id, out _));
        }

        /// <summary>
        /// 是否超PO规则金额限制
        /// </summary>
        /// <param name="dataverseService"></param>
        /// <param name="isAPS"></param>
        /// <param name="totalAmountRMB"></param>
        /// <returns>超限：true,未超限：false</returns>
        private bool IsExceedInitiateAmount(IDataverseService dataverseService, bool isAPS, decimal? totalAmountRMB)
        {
            if (!totalAmountRMB.HasValue)
            {
                return false;
            }
            var apsProperty = isAPS ? APS_PROPERTY : UN_APS_PROPERTY;
            var poInitiateAmounts = dataverseService.GetPoInitiateAmountAsync().Result
                .FirstOrDefault(a => a.Name.Equals(apsProperty, StringComparison.OrdinalIgnoreCase));
            var rate = dataverseService.GetCurrencyConfig(DictionaryType.CurrencyItems.USD).Result
                .FirstOrDefault(a => a.CurrencyCode.Equals(DictionaryType.CurrencyItems.USD, StringComparison.OrdinalIgnoreCase));

            //任一为空，表示不超限，返回false
            if (poInitiateAmounts == null || rate == null)
            {
                return false;
            }
            return totalAmountRMB >= poInitiateAmounts.AmountLimitUSD * rate.PlanRate;
        }

        /// <summary>
        /// Bidding和竞价豁免是否有效（0：验证不过需要返回前端提示客户；1：表示验证成功金额可以超限,不需要后续执行金额超限判断,2:全部无Bidding流程和Waiver流程需要后续执行金额超限判断）
        /// </summary>
        /// <param name="prDetails"></param>
        /// <returns></returns>
        private (int, string) IsHasValidBiddingAndJW(IEnumerable<PurPRApplicationDetail> prDetails)
        {
            var queryBD = LazyServiceProvider.LazyGetService<IPurBDApplicationRepository>().GetQueryableAsync();
            var firstPrd = prDetails.FirstOrDefault();//取第一条数据 作为参考 要么都为True 要么都为False
            bool isBdOrBw = firstPrd.BiddingId.HasValue == true || firstPrd.BWApplicationId.HasValue == true || firstPrd.IsOffline == true;
            foreach (var item in prDetails)
            {
                var currentItemStatus = item.BiddingId.HasValue == true || item.BWApplicationId.HasValue == true || item.IsOffline == true;
                if (isBdOrBw != currentItemStatus)
                    return (0, "请全部选择含Bidding流程和Waiver流程列或选择不含Bidding流程和Waiver流程列");
            }

            if (prDetails.Any(a => a.BiddingId.HasValue == true)) //已经发起Bidding流程 则不验证订单人民币金额
            {
                var biddingIds = prDetails.Select(a => a.BiddingId.Value).ToList();
                PurBDStatus[] statuses = [PurBDStatus.Invalid, PurBDStatus.Rejected];
                var bds = queryBD.Result.Where(a => biddingIds.Contains(a.Id) && !statuses.Contains(a.Status)).ToList();
                foreach (var item in bds)
                {
                    if (!PurBDStatus.Approved.Equals(item.Status))
                    {
                        return (0, "含未审批完成Bidding流程列");
                    }
                }
                if (bds.Select(a => a.VendorId).Distinct().Count() > 1)
                    return (0, "Bidding供应商不相同，仅相同供应商才能发起PO");
                return (1, "成功，金额可以超限");// 不验证订单人民币金额 
            }
            if (prDetails.Any(a => a.BWApplicationId.HasValue == true || a.IsOffline == true))
            {
                //已经发起竞价豁免 只有全部是竞价豁免程序才会运行到此
                return (1, "成功，金额可以超限");
            }
            return (2, "无Bidding且无Waiver流程");
        }

        #endregion
        /// <summary>
        /// 更新PO状态——作废
        /// </summary>
        /// <param name="Ids"></param>
        /// <returns></returns>
        public async Task UpdatePOApplicationStatus(List<UpdatePOStatusDto> updatePOStatus)
        {
            var POApplication = LazyServiceProvider.GetService<IPurPOApplicationRepository>();
            var query = await POApplication.GetQueryableAsync();
            var Ids = updatePOStatus.Select(p => p.Id).ToList();
            var entities = query.Where(x => Ids.Contains(x.Id)).ToList();
            var currentUserId = CurrentUser.Id.Value;
            //2119【任务中心】【我的采购推送】发起PO后，由审批人退回，退回后申请人作废，订单审批状态状态应该显示为作废，且该单据应该回到我的采购推送列表，可重新发起PO ---BE 
            var prDetailRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>();
            var prDetailIds = entities.SelectMany(a => a.PRApplicationDetailId.Split(",").Select(Guid.Parse), (a, b) => b);
            var prDetails = await prDetailRepository.GetListAsync(a => prDetailIds.Contains(a.Id));
            prDetails.ForEach(a => a.OrderStatusFlag = OrderStatusFlag.OrderReject);

            var workflowTaskRepository = LazyServiceProvider.LazyGetService<IWorkflowTaskRepository>();
            List<WorkflowTask> tasks = new List<WorkflowTask>();
            if (entities.Any())
            {
                var msaService = LazyServiceProvider.LazyGetService<ISowService>();
                entities.ForEach(x =>
                {
                    var po = updatePOStatus.FirstOrDefault(a => a.Id == x.Id);
                    if (x.Status == PurOrderStatus.ApplierConfirmed || x.Status == PurOrderStatus.InitiateReceipt)
                    {
                        var approvalRecord = new AddApprovalRecordDto
                        {
                            FormId = po.Id,
                            ApprovalTime = DateTime.UtcNow.AddHours(8),
                            ApprovalId = currentUserId,
                            OriginalApprovalId = currentUserId,
                            Status = ApprovalOperation.Approved,
                            WorkStep = "申请人确认",
                            Remark = po.Remark,
                            Name = "申请人确认"
                        };
                        if (po.Status == PurOrderStatus.Return)
                            approvalRecord.Status = ApprovalOperation.Withdraw;
                        else approvalRecord.Status = ApprovalOperation.Rejected;
                        var taskEntity = ObjectMapper.Map<AddApprovalRecordDto, WorkflowTask>(approvalRecord);
                        tasks.Add(taskEntity);
                        x.Status = po.Status;
                    }
                    else
                    {
                        //2119【任务中心】【我的采购推送】发起PO后，由审批人退回，退回后申请人作废，订单审批状态状态应该显示为作废，且该单据应该回到我的采购推送列表，可重新发起PO ---BE 
                        x.Status = PurOrderStatus.Invalid;

                        //x.Remark = po.Remark;
                        //如果是待审批，则判断是第一次发起审批还是多次重新发起审批
                        tasks.Add(ObjectMapper.Map<AddApprovalRecordDto, WorkflowTask>(new AddApprovalRecordDto
                        {
                            FormId = po.Id,
                            ApprovalTime = DateTime.Now,
                            ApprovalId = currentUserId,
                            OriginalApprovalId = currentUserId,
                            Status = ApprovalOperation.Delete,//作废
                            WorkStep = "提交人操作",
                            Remark = po.Remark,
                            Name = "提交人操作"
                        }));
                    }
                });

                foreach (var item in entities)
                {
                    //拒绝或作废时，如果有关联的sow，需要check是否可以删除
                    if ((item.Status == PurOrderStatus.Rejected || item.Status == PurOrderStatus.Invalid) && item.SowId.HasValue)
                        await msaService.DeleteSowAsync(new SowDeleteDto { PoId = item.Id, SowId = item.SowId.Value });
                }
            }
            await workflowTaskRepository.InsertManyAsync(tasks);
            await POApplication.UpdateManyAsync(entities, true);
            await prDetailRepository.UpdateManyAsync(prDetails);
        }
        /// <summary>
        /// 获取我的推送--推送，未推送的数量
        /// </summary>
        /// <returns></returns>
        public async Task<List<TypeCountDto<IsPushPO>>> GetPRPushedOrUnPushedTotal()
        {
            var result = new List<TypeCountDto<IsPushPO>>() {
                new TypeCountDto<IsPushPO> { ShowType = IsPushPO.Pushed, Count = 0 },
                new TypeCountDto<IsPushPO>{ ShowType = IsPushPO.NotPushed, Count = 0 }
            };
            return result;//已发起拿掉;待发起也拿掉，然后从待发起列表接口取totalCount.
            var queryablePR = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var queryablePRdetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var queryablePO = await LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>().GetQueryableAsync();
            var queryablePodetail = await LazyServiceProvider.LazyGetService<IPurPOApplicationDetailsRepository>().GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var companyIds = (await dataverseService.GetProcurementPushConfigAsync(CurrentUser.Id.ToString())).Select(s => s.CompanyId).ToList();
            //var queryableBidding = await LazyServiceProvider.LazyGetService<IPurBDApplicationRepository>().GetQueryableAsync();
            //var poquery = queryablePodetail.Join(queryablePO, a => a.POApplicationId, b => b.Id, (a, b) => new { pod = a, po = b });
            var query = queryablePRdetail.Where(m => m.PayMethod == PayMethods.AP && m.PushFlag == PushFlagEnum.Pushed)
                .Join(queryablePR, a => a.PRApplicationId, b => b.Id, (a, b) => new { prd = a, Pr = b })
                .Where(m => companyIds.Contains(m.Pr.CompanyId))
                //.GroupJoin(queryableBidding, a => a.prd.BiddingId, b => b.Id, (a, b) => new { data = a, b })
                //.SelectMany(a => a.b.DefaultIfEmpty(), (a, b) => new { prd = a.data.prd, Bidding = b })
                ;

            PurOrderStatus[] poStatus = [PurOrderStatus.Invalid, PurOrderStatus.Rejected];
            var notpushcount = await query.Where(m => m.prd.OrderStatusFlag == null ||
                    OrderStatusFlag.OrderReject == m.prd.OrderStatusFlag ||
                    OrderStatusFlag.BiddingReject == m.prd.OrderStatusFlag ||
                    OrderStatusFlag.BiddingCompleted == m.prd.OrderStatusFlag ||
                    OrderStatusFlag.Bidding == m.prd.OrderStatusFlag).CountAsync();
            var pushcount = await query.Where(m => OrderStatusFlag.InitiateOrder == m.prd.OrderStatusFlag ||
                    OrderStatusFlag.OrderCompleted == m.prd.OrderStatusFlag).CountAsync();
            List<TypeCountDto<IsPushPO>> typeCountDto = new List<TypeCountDto<IsPushPO>>()
            {
                new TypeCountDto<IsPushPO>{ShowType=IsPushPO.Pushed ,Count=pushcount},
                new TypeCountDto<IsPushPO>{ShowType=IsPushPO.NotPushed ,Count=notpushcount},
            };
            return typeCountDto;
        }
        /// <summary>
        /// 获取我发起的采购订单数量
        /// </summary>
        /// <returns></returns>
        public async Task<List<TypeCountDto<ProcessingStatus>>> GetPOProcessingStatusTotal()
        {
            List<TypeCountDto<ProcessingStatus>> typeCountDto = new List<TypeCountDto<ProcessingStatus>>()
            {
                new TypeCountDto<ProcessingStatus>{ShowType= ProcessingStatus.PendingProcessing},
                new TypeCountDto<ProcessingStatus>{ShowType=ProcessingStatus.Progressing },
                new TypeCountDto<ProcessingStatus>{ShowType=ProcessingStatus.Completed },
            };
            var queryablePO = await LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>().GetQueryableAsync();
            var keyValues = queryablePO.Where(m => m.ApplyUserId == CurrentUser.Id && m.Status != PurOrderStatus.Draft).GroupBy(g => g.Status, (a, b) => new KeyValuePair<PurOrderStatus, int>(a, b.Count())).ToList();
            keyValues.ForEach(e =>
            {
                switch (e.Key)
                {
                    //pendding
                    case PurOrderStatus.ApplierConfirmed:
                        typeCountDto.FirstOrDefault(f => f.ShowType == ProcessingStatus.PendingProcessing).Count += e.Value;
                        break;
                    case PurOrderStatus.Return:
                        typeCountDto.FirstOrDefault(f => f.ShowType == ProcessingStatus.PendingProcessing).Count += e.Value;
                        break;
                    //case PurOrderStatus.Draft:
                    //    typeCountDto.FirstOrDefault(f => f.ShowType == ProcessingStatus.PendingProcessing).Count += e.Value;
                    //    break;
                    case PurOrderStatus.InitiateReceipt:
                        typeCountDto.FirstOrDefault(f => f.ShowType == ProcessingStatus.PendingProcessing).Count += e.Value;
                        break;
                    //Approving
                    case PurOrderStatus.Approving:
                        typeCountDto.FirstOrDefault(f => f.ShowType == ProcessingStatus.Progressing).Count += e.Value;
                        break;
                    //end
                    case PurOrderStatus.Rejected:
                        typeCountDto.FirstOrDefault(f => f.ShowType == ProcessingStatus.Completed).Count += e.Value;
                        break;
                    case PurOrderStatus.Closed:
                        typeCountDto.FirstOrDefault(f => f.ShowType == ProcessingStatus.Completed).Count += e.Value;
                        break;
                    case PurOrderStatus.Invalid:
                        typeCountDto.FirstOrDefault(f => f.ShowType == ProcessingStatus.Completed).Count += e.Value;
                        break;
                    default:
                        break;
                }
            });
            return typeCountDto;
        }
        /// <summary>
        ///追加附件
        /// </summary>
        /// <param name="Id"></param>
        /// <param name="uploads"></param>
        /// <returns></returns>
        public async Task AppendAttachment(UpdateBusinessAttachmentDto attachmentDto)
        {
            var POApplication = LazyServiceProvider.GetService<IPurPOApplicationRepository>();
            var query = await POApplication.GetQueryableAsync();
            var attachmentids = attachmentDto.uploads.Select(a => a.AttachmentId.ToString()).JoinAsString(",");
            var po = await POApplication.FirstOrDefaultAsync(a => a.Id == attachmentDto.Id);
            po.AttachmentFile = attachmentids;
            await POApplication.UpdateAsync(po);
        }
        /// <summary>
        /// 获取采购订单草稿列表
        /// </summary>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<PagedResultDto<POListResponseDto>> GetPOApplicationDraftList(PODraftRequestDto request)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var queryablePO = await LazyServiceProvider.LazyGetService<IPurPOApplicationReadonlyRepository>().GetQueryableAsync();
            var vendorOrg = await LazyServiceProvider.LazyGetService<IVendorOrgnizationReadonlyRepository>().GetQueryableAsync();
            var vendor = await LazyServiceProvider.LazyGetService<IVendorReadonlyRepository>().GetQueryableAsync();
            var queryableUser = await LazyServiceProvider.LazyGetService<IIdentityUserReadonlyRepository>().GetQueryableAsync();

            var queryable = queryablePO.GroupJoin(queryableUser, a => a.ApplyUserId, b => b.Id, (a, b) => new { PO = a, user = b })
                            .SelectMany(a => a.user.DefaultIfEmpty(), (a, b) => new { PO = a.PO, user = b })
                            //.GroupJoin(vendorOrg, a => a.PO.VendorId, b => b.VendorId, (a, b) => new { a.PO, a.user, Vendor = b })
                            //.SelectMany(a => a.Vendor.DefaultIfEmpty(), (a, b) => new { a.PO, a.user, VendorName = b.VendorName ?? "" })
                            //.GroupJoin(vendor, a => a.PO.VendorId, b => b.Id, (a, b) => new { a.PO, a.user, a.VendorName, Vendor = b })
                            //.SelectMany(a => a.Vendor.DefaultIfEmpty(), (a, b) => new { a.PO, a.user, a.VendorName, VendorCode = b.VendorCode ?? "" })
                            .WhereIf(!string.IsNullOrWhiteSpace(request.ApplicationCode), m => m.PO.ApplicationCode.Contains(request.ApplicationCode))
                            .WhereIf(!string.IsNullOrWhiteSpace(request.ApplicationDept), m => m.PO.ApplyUserBuToDeptName == request.ApplicationDept)
                            .WhereIf(!string.IsNullOrWhiteSpace(request.VendorName), m => m.PO.VendorName.Contains(request.VendorName))
                            .WhereIf(request.SaveStartDate.HasValue, m => m.PO.CreationTime >= request.SaveStartDate)
                            .WhereIf(request.SaveEndDate.HasValue, m => m.PO.CreationTime <= request.SaveEndDate)
                            .WhereIf(request.POType.HasValue, m => m.PO.POType == request.POType)
                            .Where(m => m.PO.Status == PurOrderStatus.Draft);

            var count = await queryable.CountAsync();
            var getBuTask = await dataverseService.GetDivisions(stateCode: null);
            var getCompanyTask = await dataverseService.GetCompanyList(stateCode: null);
            var datas = queryable.Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToList().Select(s => new POListResponseDto
            {
                Id = s.PO.Id,
                ApplicationCode = s.PO.ApplicationCode,
                Status = s.PO.Status,
                ApplyUserName = s.user?.Name ?? "",
                ApplyTime = s.PO.ApplyTime.ToString("yyyy-MM-dd HH:mm:ss"),
                ApplyBuName = s.PO.ApplyUserBuToDeptName,
                ApplyUserBuToDeptName = s.PO.ApplyUserBuToDeptName,
                POType = s.PO.POType,
                VendorName = s.PO.VendorName,
                VendorCode = s.PO.VendorCode ?? "",
                VendorPorperty = s.PO.VendorPorperty,
                TotalAmount = s.PO.TotalAmount,
                TotalAmountTax = s.PO.TotalAmountTax,
                //VendorType = s.PO.VendorType,
                CompanyName = s.PO.CompanyId.HasValue ? getCompanyTask.FirstOrDefault(a => a.Id == s.PO.CompanyId)?.CompanyName ?? "" : ""
            }).ToList();
            var result = new PagedResultDto<POListResponseDto>(count, datas);
            return result;
        }
        /// <summary>
        /// 删除草稿订单
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task<MessageResult> DeletePurPODraftRows(List<Guid> ids)
        {
            var PORepository = LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>();
            var queryablePodetailRepositor = LazyServiceProvider.LazyGetService<IPurPOApplicationDetailsRepository>();
            var queryablePo = await PORepository.GetQueryableAsync();
            var queryablePodetail = await queryablePodetailRepositor.GetQueryableAsync();
            var delPoRows = queryablePo.Where(m => ids.Contains(m.Id)).ToArray();
            var isAllDraft = delPoRows.All(a => a.Status == PurOrderStatus.Draft);
            if (!isAllDraft)
                return MessageResult.FailureResult("只能删除草稿状态的采购订单!");
            var POdetailList = queryablePodetail.Where(m => ids.Contains(m.POApplicationId));

            //删除sow
            foreach (var po in delPoRows)
            {
                if (po.SowId.HasValue)
                    await LazyServiceProvider.LazyGetService<ISowService>().DeleteSowAsync(new SowDeleteDto { SowId = po.SowId.Value, PoId = po.Id });
            }

            await PORepository.DeleteManyAsync(delPoRows, true);
            await queryablePodetailRepositor.DeleteManyAsync(POdetailList, true);
            return MessageResult.SuccessResult("success");
        }
        /// <summary>
        /// 生成PO
        /// </summary>
        /// <returns></returns>
        public async Task<Stream> GeneratePoAsync(Guid Id)
        {

            var PORepository = LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>();
            var queryablePo = await PORepository.GetQueryableAsync();

            var PddRepository = LazyServiceProvider.LazyGetService<IPurPOApplicationDetailsRepository>();
            var queryablePod = await PddRepository.GetQueryableAsync();

            var workflowTaskRepository = LazyServiceProvider.LazyGetService<IWorkflowTaskRepository>();

            var queryablePR = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var queryablePRdetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var pdfService = LazyServiceProvider.LazyGetService<IPDFService>();
            var approveService = LazyServiceProvider.LazyGetService<IApproveService>();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();

            var po = queryablePo.First(s => s.Id == Id);
            if (po.Status == PurOrderStatus.Approving)
            {
                throw new Exception("审批中不能生成PO");
            }
            var PodDatas = queryablePod.AsNoTracking().Where(po => po.POApplicationId == Id)
                .GroupJoin(queryablePRdetail, a => a.PRDetailId, b => b.Id, (a, b) => new { pod = a, prd = b })
                .SelectMany(a => a.prd.DefaultIfEmpty(), (a, b) => new { pod = a.pod, prd = b })
                .GroupJoin(queryablePR, a => a.prd.PRApplicationId, b => b.Id, (a, b) => new { pod = a.pod, prd = a.prd, pr = b })
                .SelectMany(a => a.pr.DefaultIfEmpty(), (a, b) => new { pod = a.pod, PrdRceNo = a.prd.RceNo, PrNo = b.ApplicationCode, prApplyUser = b.ApplyUserIdName })
                .OrderBy(a => a.pod.RowNo);
            //查找审批记录
            var approvalRecords = await approveService.GetApprovalRecordAsync(Id);
            var approvalData = (List<ApprovalRecordDto>)approvalRecords.Data;
            var companys = await dataverseService.GetCompanyList(po.CompanyId.Value.ToString());
            var company = companys.FirstOrDefault();
            var diclist = new List<Dictionary<string, string>>();
            //填充PDF
            if (PodDatas?.Count() > 0)
            {
                foreach (var item in PodDatas)
                {
                    var subDic = new Dictionary<string, string>
                    {
                        ["Project"] = item.pod.ApplicationCode,
                        ["Number"] = item.pod.Quantity.ToString(),
                        ["Description"] = item.pod.Content,
                        ["Uom"] = item.pod.Unit,
                        ["Price"] = item.pod.UnitPrice.ToString("F2"),
                        ["Amount"] = item.pod.TotalAmount.ToString("F2"),
                    };
                    diclist.Add(subDic);
                }
            }
            else
            {
                var subDic = new Dictionary<string, string>
                {
                    ["Project"] = "",
                    ["Number"] = "",
                    ["Description"] = "",
                    ["Uom"] = "",
                    ["Price"] = "",
                    ["Amount"] = "",
                };
                diclist.Add(subDic);
            }
            //var tabDics = new Dictionary<string, List<Dictionary<string, object>>>() { { "Podetails", diclist } };
            //组装数据
            var dics = new Dictionary<string, string>{
                   {"VendorName", po.VendorName },
                   {"PoNo", po.ApplicationCode },
                   {"Address1", po.RegCertificateAddress },
                   {"Contact", po.ContactName },
                   {"Tel", po.ContactPhone },
                   {"Fax", po.FaxNumber },
                   {"PrNo", PodDatas.FirstOrDefault()?.PrNo??"" },
                   {"RCENo", "" },
                   {"Date", po.ApplyTime.ToString("yyyy-MM-dd")},
                   {"Tel2", po.PhoneNumber },
                   //{"InvoiceTitle", po.InvoiceTitle },
                   {"Address2", po.InvoiceAddress },
                   {"Fax2", po.InvoiceFax },
                   {"Special",po.AttentionNote },
                   {"ShippingMode",po.DeliveryType },
                   {"DeliveryTo",po.DeliveryAddress },
                   {"PaymentTerms",po.PaymentTerm },
                   {"PaymentCondition",po.PaymentCondition },
                   {"DeliveryDate",po.DeliveryDate.ToString("yyyy-MM-dd")},
                   {"Quality",po.Qualitystandard },
                   {"Others",po.Others },
                   {"Currency",po.Currency },
                   {"Freight","已包含在单价内" },
                   {"Total",$"{po.CurrencySymbol}{po.TotalAmountTax:F2}"},
                   {"Approved5","" },
                   {"Approved4","" },
                   {"Approved3","" },
                   {"Approved2","" },
                   {"Approved1","" },
                   {"InvoiceTitle",company.InvoiceTitle},
                   {"InvoiceEnglishTitle",company.InvoiceEnglishTitle??""},
                   {"PrApplyUser",PodDatas.FirstOrDefault()?.prApplyUser??"" }
               };
            var i = 5;
            var instanceId = approvalData.Where(a => a.Status == ApprovalOperation.Start).MaxBy(a => a.ApprovalTime).InstanceId;
            var approvals = approvalData.Where(a => a.InstanceId == instanceId && a.WorkStep != "申请人确认").OrderBy(a => a.ApprovalTime).ToList();
            foreach (var item in approvals)
            {
                string key = $"Approved{i}";
                dics[key] = $"{item.UserName} {(item.ApprovalTime.HasValue ? item.ApprovalTime.Value.ToString("yyyy/MM/dd") : "")}";
                i--;
            }
            var pdfStream = await pdfService.GeneratePoPdfAsync(dics, diclist);
            pdfStream.Position = 0;
            ////using FileStream fileStream = new FileStream(@"C:\Users\<USER>\Desktop\test.pdf", FileMode.Create);
            ////pdfStream.Seek(0, SeekOrigin.Begin);
            ////pdfStream.CopyTo(fileStream);

            // 
            if (po.Status == PurOrderStatus.ApplierConfirmed)
            {
                var approvalRecord = new AddApprovalRecordDto
                {
                    FormId = po.Id,
                    ApprovalTime = DateTime.UtcNow.AddHours(8),
                    ApprovalId = CurrentUser.Id.Value,
                    OriginalApprovalId = CurrentUser.Id.Value,
                    Status = ApprovalOperation.Approved,
                    WorkStep = "申请人确认",
                    Name = "申请人确认/生成PO"
                };
                var taskEntity = ObjectMapper.Map<AddApprovalRecordDto, WorkflowTask>(approvalRecord);
                //如果是待审批，则判断是第一次发起审批还是多次重新发起审批
                await workflowTaskRepository.InsertAsync(taskEntity);
                po.Status = PurOrderStatus.InitiateReceipt;
                await PORepository.UpdateAsync(po);
            }
            return pdfStream;
        }
        /// <summary>
        /// 发送po邮箱
        /// </summary>
        /// <returns></returns>
        public async Task<MessageResult> SendPOEmailAsync(SendPOEmailDto emailDto)
        {
            var emailService = LazyServiceProvider.LazyGetService<IEmailService>();
            var poRepository = LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>();
            var workflowTaskRepository = LazyServiceProvider.LazyGetService<IWorkflowTaskRepository>();
            var queryablePo = await poRepository.GetQueryableAsync();
            var PoData = await queryablePo.FirstAsync(f => f.Id == emailDto.Id);
            using var pdfStream = await GeneratePoAsync(emailDto.Id);
            byte[] bytes = await pdfStream.GetAllBytesAsync();
            SendEmailDto sendEmail = new();
            sendEmail.Ccs = [.. emailDto.CarbonCopy.Split(';', StringSplitOptions.RemoveEmptyEntries)];
            sendEmail.Tos = [.. emailDto.Recipient.Split(';', StringSplitOptions.RemoveEmptyEntries)];
            sendEmail.Body = $"Dear:\n\n{emailDto.Content}\n\nBest Regards,\n{PoData.ApplyUserName}";
            sendEmail.Subject = $"{PoData.ApplicationCode} {PoData.VendorName}";
            sendEmail.FromEmail = emailDto.Sender;
            sendEmail.FromName = emailDto.Purchaser;
            var attachment = new EmailAttachment()
            {
                AttachmentName = $"采购订单{PoData.ApplicationCode}.pdf",
                AttachmentStream = bytes
            };
            sendEmail.Attachments = [attachment];

            //var uploadFile = new List<UploadFileDto>();
            //uploadFile.Add(new UploadFileDto { FileName = $"采购订单{PoData.ApplicationCode}.pdf", Size = bytes.Length, Buffer = bytes });
            //var attachmentService = LazyServiceProvider.LazyGetService<IAttachmentService>();
            //var result = await attachmentService.UploadFiles(FunctionModule.PO_Email_Attachment, uploadFile);
            //sendEmail.Files = result.Select(a => new KeyValuePair<string,string>(a.FilePath, "采购订单.pdf")).ToList();

            var isValid = IsValidEmail(sendEmail);
            if (!isValid.Item1)
                return MessageResult.FailureResult(isValid.Item2);
            //BackgroundJob.Enqueue(() => emailService.SendEmailAsync(sendEmail));//后台发邮件
            await emailService.SendEmailAsync(sendEmail);
            if (PoData.Status == PurOrderStatus.ApplierConfirmed)
            {
                var approvalRecord = new AddApprovalRecordDto
                {
                    FormId = PoData.Id,
                    ApprovalTime = DateTime.UtcNow.AddHours(8),
                    ApprovalId = CurrentUser.Id.Value,
                    Status = ApprovalOperation.Approved,
                    WorkStep = "申请人确认",
                    Name = "申请人确认/发送PO邮件"
                };
                var taskEntity = ObjectMapper.Map<AddApprovalRecordDto, WorkflowTask>(approvalRecord);
                //如果是待审批，则判断是第一次发起审批还是多次重新发起审批
                await workflowTaskRepository.InsertAsync(taskEntity);
                PoData.Status = PurOrderStatus.InitiateReceipt;
                await poRepository.UpdateAsync(PoData);
            }
            return MessageResult.SuccessResult();
        }
        /// <summary>
        /// 获取发送po邮件确认信息
        /// </summary>
        public async Task<SendPOEmailDto> GetConfirmEmailInfoAsync(Guid Id)
        {
            var queryablePo = (await LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            var queryablePR = (await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            var queryableUser = (await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync()).AsNoTracking();
            var PoData = await queryablePo.FirstAsync(f => f.Id == Id);
            var PRData = await queryablePR.FirstAsync(f => f.Id == PoData.PRId);
            SendPOEmailDto emailDto = new();

            emailDto.Id = Id;
            var poApplyInfo = queryableUser.First(s => s.Id == PoData.ApplyUserId);
            var prApplyInfo = queryableUser.First(s => s.Id == PRData.ApplyUserId);

            emailDto.PRApplicant = prApplyInfo.Name;
            emailDto.CarbonCopy = $"{prApplyInfo.Email};{poApplyInfo.Email}";
            emailDto.Purchaser = poApplyInfo.Name;
            emailDto.Sender = poApplyInfo.Email;
            emailDto.VendorName = PoData.VendorName;
            emailDto.Recipient = PoData.ContactEmail;
            emailDto.Content = "附件PO和相应条款请查收，每份完整订单的格式是1份采购单+1份条款+报价单，采购单与报价单需加盖公章/合同章，并连同条款一起加盖骑缝章发扫描件给采购部。全套原件(1份采购单+1份条款+报价单原件盖章+发票和结算单)一并请寄回给部门使用人，谢谢！";

            return emailDto;
        }

        /// <summary>
        /// 获取PR、PO、BD、BW单据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<ApplicationOrderDto> GetApplicationOrdersAsync(Guid id)
        {
            var result = new ApplicationOrderDto();
            var queryablePO = await LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>().GetQueryableAsync();
            var queryablePODetail = await LazyServiceProvider.LazyGetService<IPurPOApplicationDetailsRepository>().GetQueryableAsync();
            var queryablePR = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var queryablePRDetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var queryableBD = await LazyServiceProvider.LazyGetService<IPurBDApplicationRepository>().GetQueryableAsync();
            var queryableBDDetail = await LazyServiceProvider.LazyGetService<IPurBDApplicationDetailsRepository>().GetQueryableAsync();
            var queryableBW = await LazyServiceProvider.LazyGetService<IPurBWApplicationRepository>().GetQueryableAsync();
            var pos = queryablePO.GroupJoin(queryablePODetail, a => a.Id, b => b.POApplicationId, (a, b) => new { po = a, pods = b })
                .SelectMany(a => a.pods.DefaultIfEmpty(), (a, b) => new { a.po, pod = b })
                .Where(a => a.po.Id == id)
                .ToList();
            var prdIds = pos.Select(a => a.pod.PRDetailId).Distinct().ToList();
            var prs = queryablePR.GroupJoin(queryablePRDetail, a => a.Id, b => b.PRApplicationId, (a, b) => new { pr = a, prds = b })
                .SelectMany(a => a.prds.DefaultIfEmpty(), (a, b) => new { a.pr, prd = b })
                .Where(a => prdIds.Contains(a.prd.Id))
                .ToList();
            var bds = queryableBD.GroupJoin(queryableBDDetail, a => a.Id, b => b.BDApplicationId, (a, b) => new { bd = a, bdds = b })
                .SelectMany(a => a.bdds.DefaultIfEmpty(), (a, b) => new { a.bd, bdd = b })
                .Where(a => prdIds.Contains(a.bdd.PRDetailId) && a.bd.Status != PurBDStatus.Invalid)
                .ToList();
            var bws = queryableBW.Where(a => prdIds.Contains(a.PRDetailId) && a.Status != PurExemptStatus.Invalid).ToList();

            result.POOrder = new OrderInfo { IsAny = true, Ids = pos.Select(a => a.po.Id).Distinct().ToList() };
            result.PROrder = new OrderInfo { IsAny = true, Ids = prs.Select(a => a.pr.Id).Distinct().ToList() };
            result.BDOrder = new OrderInfo { IsAny = bds.Any(), Ids = bds?.Select(a => a.bd.Id)?.Distinct()?.ToList() };
            result.BWOrder = new OrderInfo { IsAny = bws.Any(), Ids = bws?.Select(a => a.Id)?.Distinct()?.ToList() };
            return result;
        }

        #region 私有方法
        /// <summary>
        /// 邮箱校验
        /// </summary>
        /// <param name="emailDto"></param>
        /// <returns></returns>
        private (bool, string) IsValidEmail(SendEmailDto emailDto)
        {
            try
            {
                if (!EmailValidationHelper.IsValidEmail(emailDto.FromEmail))
                    throw new Exception($"请输入有效的电子邮件地址");
                emailDto.Tos.ForEach(x =>
                {
                    if (!EmailValidationHelper.IsValidEmail(x))
                        throw new Exception($"请输入正确的收件人邮箱地址，多个请用英文分号(\";\")分隔");
                });
                emailDto.Ccs.ForEach(x =>
                {
                    if (!EmailValidationHelper.IsValidEmail(x))
                        throw new Exception($"请输入正确的抄送邮箱地址，多个请用英文分号(\";\")分隔");
                });
                return (true, "");
            }
            catch (Exception ex)
            {
                return (false, ex.Message);
            }
        }
        #endregion
    }
}
