select 
newid() as spk_NexBPMCode,* 
into #spk_staffmasterdata
from dbo.spk_staffmasterdata_Tmp

IF OBJECT_ID(N'dbo.spk_staffmasterdata', N'U') IS NOT NULL
BEGIN
	update a 
	set a.bpm_id                 = b.bpm_id
       ,a.spk_staffNumber        = b.spk_staffNumber
       ,a.spk_districtmasterdata = b.spk_districtmasterdata
       ,a.spk_gender             = b.spk_gender
       ,a.spk_name               = b.spk_name
       ,a.spk_staffaccount       = b.spk_staffaccount
       ,a.spk_staffemail         = b.spk_staffemail
       ,a.spk_stafflanguage      = b.spk_stafflanguage
       ,a.spk_staffstate         = b.spk_staffstate
       ,a.spk_staffphonenumber   = b.spk_staffphonenumber
       ,a.spk_staffRemarks       = b.spk_staffRemarks
       ,a.flg                    = b.flg
    from dbo.spk_staffmasterdata a
    left join #spk_staffmasterdata b on a.bpm_id = b.bpm_id
    
    insert into dbo.spk_staffmasterdata
    select a.spk_NexBPMCode
           ,a.bpm_id
           ,a.spk_staffNumber
           ,a.spk_districtmasterdata
           ,a.spk_gender
           ,a.spk_name
           ,a.spk_staffaccount
           ,a.spk_staffemail
           ,a.spk_stafflanguage
           ,a.spk_staffstate
           ,a.spk_staffphonenumber
           ,a.spk_staffRemarks
           ,a.flg
	from #spk_staffmasterdata a
	where NOT EXISTS (SELECT * FROM dbo.spk_staffmasterdata where a.bpm_id = bpm_id)
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into dbo.spk_staffmasterdata from #spk_staffmasterdata
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END

