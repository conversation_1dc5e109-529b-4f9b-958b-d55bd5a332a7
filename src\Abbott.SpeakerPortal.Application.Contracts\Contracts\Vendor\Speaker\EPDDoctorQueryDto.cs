﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Text.Json.Serialization;
using Abbott.SpeakerPortal.Dtos;

namespace Abbott.SpeakerPortal.Contracts.Vendor.Speaker
{
    /// <summary>
    /// 查询EPD 医生 DTO
    /// </summary>
    public class EPDDoctorQueryDto : PagedDto
    {
        /// <summary>
        /// 医生姓名（精确匹配）
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 医生所属医院（模糊匹配）
        /// </summary>
        public string HospitalName { get; set; }

        /// <summary>
        /// EPD医生主键（非必填，精确匹配）
        /// </summary>
        public string EpdHcpCode { get; set; }
    }
}
