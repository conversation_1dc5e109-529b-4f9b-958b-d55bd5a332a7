select newid() as spk_NexBPMCode,a.spk_buname as spk_BPMCode, b.spk_NexBPMCode as spk_buname,spk_code,spk_pushspecialcodes,a.flg 
into #spk_bucodingconfiguration 
from spk_bucodingconfiguration_Tmp a
left join spk_organizationalMasterData b on a.spk_buname=b.spk_BPMCode

IF OBJECT_ID(N'dbo.spk_bucodingconfiguration', N'U') IS NOT NULL
BEGIN
	update a 
	set a.spk_BPMCode           = b.spk_BPMCode
        ,a.spk_buname           = b.spk_buname
        ,a.spk_code             = b.spk_code
        ,a.spk_pushspecialcodes = b.spk_pushspecialcodes
        ,a.flg                  = b.flg 
    from spk_bucodingconfiguration a
    join #spk_bucodingconfiguration b on a.spk_BPMCode = b.spk_BPMCode
    
    insert into spk_bucodingconfiguration
    select a.spk_NexBPMCode
          ,a.spk_BPMCode
          ,a.spk_buname
          ,a.spk_code
          ,a.spk_pushspecialcodes
          ,a.flg 
    from #spk_bucodingconfiguration a
    where not exists (select * from spk_bucodingconfiguration where a.spk_BPMCode = spk_BPMCode)
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into dbo.spk_bucodingconfiguration from #spk_bucodingconfiguration
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
