CREATE PROCEDURE dbo.sp_PurPOApplications_ns
AS 
BEGIN
	/*
 * VendorId 未赋值 --完成
 * VendorPorperty字段等到开发确认逻辑  --待定
 * AttachmentFile --完成
 * PRId --完成
 * drop table #PurPOApplications
 */ 
select 
a.Id,
a.ApplicationCode,
a.PRApplicationDetailId,
a.Status,
UPPER(ss.spk_NexBPMCode) as ApplyUserId,
a.ApplyTime,
UPPER(soc.spk_NexBPMCode) as ApplyUserBu,
a.BWApplicationId,
a.BApplicationId,
a.POType,
UPPER(o.spk_NexBPMCode) as CompanyId,
UPPER(cc.ID) as VendorId,
a.VendorPorperty,
a.RegCertificateAddress,
a.ContactName,
a.ContactPhone,
a.ContactEmail,
a.FaxNumber,
a.PaymentTerm,
a.<PERSON><PERSON>,
a.ExchangeRate,
a.PRType,
a.PR<PERSON>orrespond,
a.Atten<PERSON>ote,
a.DeliveryType,
a.<PERSON>,
a.PaymentCondition,
a.Delivery<PERSON>,
a.<PERSON>,
a.<PERSON>,
a.Remark,
a.AttachmentFile,
a.ExtraProperties,
a.ConcurrencyStamp,
a.CreatorId as CreationTime,
UPPER(ss1.spk_NexBPMCode)  as CreatorId,
a.LastModificationTime,
a.LastModifierId,
a.IsDeleted,
a.DeleterId,
a.DeletionTime,
a.Saving,
a.VendorCategory,
a.BankAccount,
a.InvoiceAddress,
a.InvoiceFax,
a.InvoiceTitle,
a.OpenBank,
a.TotalAmount,
a.TotalAmountTax,
UPPER(soc2.spk_Name) as ApplyUserBuName,
a.ApplyUserName,
a.VendorName,
a.IsLate,
UPPER(di2.spk_NexBPMCode) as ApsPorperty,
a.CurrencySymbol,
a.PhoneNumber,
a.PRId,
a.ApprovedDate,
a.VendorCode,
a.ApplyUserBuToDeptName,
UPPER(soc3.spk_NexBPMCode) as ApplyUserDept
into #PurPOApplications
from PLATFORM_ABBOTT_Dev.dbo.PurPOApplications_tmp a 
left join PLATFORM_ABBOTT_Dev.dbo.spk_staffmasterdata ss 
on a.ApplyUserId =ss.bpm_id
left join PLATFORM_ABBOTT_Dev.dbo.spk_organizationalmasterdata soc 
on a.ApplyUserBu =soc.spk_BPMCode 
left join PLATFORM_ABBOTT_Dev.dbo.spk_companymasterdata o
on a.CompanyId =o.spk_BPMCode
left join PLATFORM_ABBOTT_Dev.dbo.spk_staffmasterdata ss1 
on a.CreationTime =ss1.bpm_id 
left join PLATFORM_ABBOTT_Dev.dbo.spk_organizationalmasterdata soc2 
on a.ApplyUserBuName =soc2.spk_BPMCode 
left join PLATFORM_ABBOTT_Dev.dbo.spk_dictionary di2
on a.ApsPorperty  =di2.spk_BPMCode  and di2.spk_type=N'APS属性'
left join PLATFORM_ABBOTT_Dev.dbo.spk_organizationalmasterdata soc3 
on a.ApplyUserDept =soc3.spk_BPMCode 
left join PLATFORM_ABBOTT_Dev.dbo.ods_BPCS_PMFVM f
on cast(a.SupplierCode as nvarchar(100)) = cast(f.[VNDERX]as nvarchar(255)) and cast(a.SupplierName as nvarchar(255))=cast(f.[VEXTNM]as nvarchar(255)) and cast(o.spk_CompanyCode as nvarchar(100))=cast(f.[VMCMPY]as nvarchar(255))
left join PLATFORM_ABBOTT_Dev.dbo.ODS_BPCS_AVM  cc
on f.[VMCMPY]=cc.[VCMPNY] AND f.[VNDERX]=cc.[VENDOR];

WITH SplitAtta AS (
    SELECT 
        A.id AS A_id, -- 假设A表有id字段
        TRIM(value) AS AttachmentFile
    FROM #PurPOApplications a
    CROSS APPLY STRING_SPLIT(A.AttachmentFile, ',')
),
File_id as (
	SELECT 
	    A_id,
	    B.id as B_id,
	    B.BPMId
	FROM 
	    SplitAtta
	JOIN PLATFORM_ABBOTT_Dev.dbo.Attachments_tmp B ON SplitAtta.AttachmentFile COLLATE SQL_Latin1_General_CP1_CI_AS = B.BPMId
)
select A_id,
STRING_AGG(cast(B_id as nvarchar(1000)), ',') WITHIN GROUP (ORDER BY B_id) AS B_id
into #AttachmentFile
from File_id
group by A_id;

update a set a.AttachmentFile=b.B_id from #PurPOApplications a
join #AttachmentFile b
on a.id=b.A_id
PRINT(N'update AttachmentFile'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'));

IF OBJECT_ID(N'PLATFORM_ABBOTT_Dev.dbo.PurPOApplications', N'U') IS NOT NULL
BEGIN
	PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	drop table PLATFORM_ABBOTT_Dev.dbo.PurPOApplications
	select  *  into PLATFORM_ABBOTT_Dev.dbo.PurPOApplications from #PurPOApplications
END
ELSE
BEGIN
	select  *  into PLATFORM_ABBOTT_Dev.dbo.PurPOApplications from #PurPOApplications
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END 


END
