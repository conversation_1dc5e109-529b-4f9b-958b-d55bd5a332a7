﻿using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Integration.Veeva;
using Hangfire;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.DistributedLocking;

namespace Abbott.SpeakerPortal.BackgroundWorkers.Integration.Veeva
{
    /// <summary>
    /// 超过五个自然日 邮件提醒  
    /// </summary>
    public class VeevaTimeoutSendingEmailWorker : SpeakerPortalBackgroundWorkerBase
    {
        private IScheduleJobLogService _jobLogService;
        private IInteVeevaService _inteVeevaService;
        public VeevaTimeoutSendingEmailWorker(IServiceProvider serviceProvider)
        {
            _jobLogService = serviceProvider.GetService<IScheduleJobLogService>();
            _inteVeevaService = serviceProvider.GetService<IInteVeevaService>();
            //触发周期,每天凌晨6点
            CronExpression = Cron.Daily(6);
        }

        public override async Task DoWorkAsync(CancellationToken cancellationToken = default)
        {
            //1、加锁执行 执行完自动释放锁
            await using (var handle = await LazyServiceProvider.LazyGetService<IAbpDistributedLock>().TryAcquireAsync(DistributedLock.DistributedLock_VeevaPushTimeoutSendEmail))
            {
                // 锁已被其他任务持有，跳过
                if (handle == null)
                    return;

                var log = _jobLogService.InitSyncLog("VeevaTimeoutSendingEmailWorker");
                try
                {
                    //超过五个自然日 邮件提醒  
                    await _inteVeevaService.SendingEmailTimeoutPushTask();
                }
                catch (Exception ex)
                {
                    log.IsSuccess = false;
                    log.Remark = ex.ToString();
                }
                finally
                {
                    _jobLogService.SyncLog(log);
                }
            }
        }
    }
}
