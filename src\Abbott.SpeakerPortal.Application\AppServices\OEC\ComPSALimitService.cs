﻿using Abbott.SpeakerPortal.OEC.ComPSALimit;
using Abbott.SpeakerPortal.Entities.OECPSAComExpenseTypes;
using Abbott.SpeakerPortal.Entities.OECPSAComPSALimitOperHistorys;
using Abbott.SpeakerPortal.Entities.OECPSAComPSALimits;
using Abbott.SpeakerPortal.Enums;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Senparc.NeuChar.Entities.App;
using Senparc.Weixin.WxOpen.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using SixLabors.Fonts;
using Volo.Abp.Identity;
using Volo.Abp.Uow;
using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.Utils;
using Abbott.SpeakerPortal.Contracts.OEC.ComPSALimit;
using DocumentFormat.OpenXml.Drawing;
using Abbott.SpeakerPortal.Dataverse;

namespace Abbott.SpeakerPortal.OEC
{
    public partial class ComPSALimitService : SpeakerPortalAppService, IComPSALimitService
    {
        private readonly IServiceProvider _serviceProvider;
        private IRepository<OECPSAComPSALimit, Guid> _comPSALimitRepository;
        private IOECPSAComExpenseTypeRepository _comExpenseTypeRepository;
        private IOECPSAComPSALimitOperHistoryRepository _operHistoryRepository;
        private IRepository<IdentityUser, Guid> _identityUserRepository;

        private readonly ILogger<ComPSALimitService> _logger;

        public ComPSALimitService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _logger = serviceProvider.GetService<ILogger<ComPSALimitService>>();
            _comPSALimitRepository = _serviceProvider.GetService<IRepository<OECPSAComPSALimit, Guid>>();
            _comExpenseTypeRepository = serviceProvider.GetService<IOECPSAComExpenseTypeRepository>();
            _operHistoryRepository = serviceProvider.GetService<IOECPSAComPSALimitOperHistoryRepository>();
            _identityUserRepository = serviceProvider.GetService<IRepository<IdentityUser, Guid>>();
        }

        /// <summary>
        /// 获取通用配置上限列表
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<PagedResultDto<ComPSALimitListResponseDto>> GetComPSALimitList(ComPSALimitListRequestDto request)
        {
            var userQuery = await _identityUserRepository.GetQueryableAsync();
            var psaLimitQuery = await LazyServiceProvider.LazyGetService<IOECPSAComPSALimitRepository>().GetQueryableAsync();
            var query = psaLimitQuery
                .WhereIf(!string.IsNullOrWhiteSpace(request.SerialNo), x => x.SerialNo.Contains(request.SerialNo))
                .WhereIf(request.EffectStart.HasValue, a => a.EffectStart.Year >= request.EffectStart)
                .WhereIf(request.EffectEnd.HasValue, a => (a.EffectEnd.HasValue ? a.EffectEnd.Value.Year : DateTime.MaxValue.Year) <= request.EffectEnd)
                .WhereIf(request.DataStatus.HasValue, x => x.DataStatus == request.DataStatus)
                .Select(a => new { ComPsa = a, LastModifyUserId = a.LastModifierId ?? a.CreatorId, LastModifiedAt = a.LastModificationTime ?? a.CreationTime })
                .GroupJoin(userQuery, a => a.LastModifyUserId, a => a.Id, (a, b) => new { a.ComPsa, Uses = b })
                .SelectMany(a => a.Uses.DefaultIfEmpty(), (a, b) => new { a.ComPsa, LastModifyUserName = b.Name });

            var count = query.Count();
            var datas = query
                .OrderByDescending(a => a.ComPsa.DataStatus == EffectStatus.Effective)//按照生效日期降序排序且生效行应该显示在第一行
                .ThenByDescending(a => a.ComPsa.EffectStart)
                .Skip(request.PageIndex * request.PageSize).Take(request.PageSize)
                .ToArray()
                .Select(a =>
                {
                    var data = TransToEffectState(a.ComPsa);
                    var result = ObjectMapper.Map<OECPSAComPSALimit, ComPSALimitListResponseDto>(data);
                    result.LastModifierId = a.LastModifyUserName;

                    return result;
                }).ToArray();

            var res = new PagedResultDto<ComPSALimitListResponseDto>()
            {
                Items = datas,
                TotalCount = count,
            };
            return res;
        }

        /// <summary>
        /// 新增或修改通用PSA上限配置
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> CreateOrUpdateComPSALimit(CreateComPSALimitRequestDto request)
        {
            if (!request.ExpenseList.Any())
                return MessageResult.FailureResult($"通用PSA上线配置的适用费用类型不能为空");
            //新增
            if (request.ActionType == 0)
            {
                var isExist = await _comPSALimitRepository.AnyAsync(x => x.EffectStart.Date == request.EffectStart.Date || x.EffectEnd == request.EffectStart);
                if (isExist)
                {
                    _logger.LogError($"CreateOrUpdateComPSALimit报错：已存在生效日期或失效日期相同的通用PSA上限配置");
                    return MessageResult.FailureResult($"已存在相同时间内生效的通用PSA上限配置");
                }
                //找到范围重叠的那个data，将失效日期更新为request.EffectStart的前一天
                var queryData = await _comPSALimitRepository.FirstOrDefaultAsync(x => x.EffectStart.Date < request.EffectStart.Date && (!x.EffectEnd.HasValue || x.EffectEnd.Value.Date > request.EffectStart.Date));
                if (queryData != null)
                {
                    queryData.EffectEnd = request.EffectStart.AddDays(-1);
                    await _comPSALimitRepository.UpdateAsync(queryData);
                }

                //生成序列号
                request.SerialNo = await CreateSerialNo();
                var limitDto = ObjectMapper.Map<CreateComPSALimitRequestDto, OECPSAComPSALimit>(request);
                using (var scope = _serviceProvider.CreateScope())
                {
                    var uowManager = scope.ServiceProvider.GetRequiredService<IUnitOfWorkManager>();

                    using (var uow = uowManager.Begin())
                    {
                        await _comPSALimitRepository.InsertAsync(limitDto, true);
                        await uow.CompleteAsync();
                    }
                }

                var expenseList = new List<OECPSAComExpenseType>();
                foreach (var item in request.ExpenseList)
                {
                    expenseList.Add(new OECPSAComExpenseType
                    {
                        ComPSALimitId = limitDto.Id,
                        ConsumeCategory = item.ConsumeCategory,
                        ExpenseNature = item.ExpenseNature
                    });
                }
                await _comExpenseTypeRepository.InsertManyAsync(expenseList, true);
            }
            else
            {
                try
                {
                    //修改
                    var data = await _comPSALimitRepository.FindAsync(request.Id.Value);
                    if (data == null)
                        return MessageResult.FailureResult($"未查询到Id为{request.Id.Value}的通用PSA上限配置");
                    //将当前未修改的版本保存到操作记录里
                    var comPSADetailData = await GetComPSALimitDetails(request.Id.Value);
                    var jsonData = JsonConvert.SerializeObject(comPSADetailData);

                    var history = new GetComPSALimitOperHistory
                    {
                        ComPSALimitId = (Guid)request.Id,
                        ContentDetail = jsonData,
                        Remark = request.Remark,
                    };
                    var historyEntity = ObjectMapper.Map<GetComPSALimitOperHistory, OECPSAComPSALimitOperHistory>(history);

                    data.AmountLimit = request.AmountLimit;
                    data.TimesLimit = request.TimesLimit;
                    data.Remark = request.Remark;
                    data = TransToEffectState(data);
                    //待生效的才能修改生效日期
                    if (data.DataStatus == EffectStatus.ToBeEffective)
                    {
                        var isExist = await _comPSALimitRepository.AnyAsync(x => x.Id != request.Id && (x.EffectStart == request.EffectStart || x.EffectEnd == request.EffectStart));
                        if (isExist)
                        {
                            _logger.LogError($"CreateOrUpdateComPSALimit报错：已存在生效日期或失效日期相同的通用PSA上限配置");
                            return MessageResult.FailureResult($"已存在相同时间内生效的通用PSA上限配置");
                        }
                        //找到范围重叠的那个data，将失效日期更新为request.EffectStart的前一天

                        var queryData = await _comPSALimitRepository.GetListAsync(x => x.EffectStart < request.EffectStart && (x.EffectEnd == null || x.EffectEnd > request.EffectStart));
                        foreach (var item in queryData)
                        {
                            item.EffectEnd = request.EffectStart.AddDays(-1);
                            await _comPSALimitRepository.UpdateAsync(item);
                        }
                        data.EffectStart = request.EffectStart;
                    }
                    await _comPSALimitRepository.UpdateAsync(data);

                    //更新适用费用类型
                    await _comExpenseTypeRepository.HardDeleteAsync(x => x.ComPSALimitId == request.Id.Value);
                    var expenseList = new List<OECPSAComExpenseType>();
                    foreach (var item in request.ExpenseList)
                    {
                        expenseList.Add(new OECPSAComExpenseType
                        {
                            ComPSALimitId = request.Id.Value,
                            ConsumeCategory = item.ConsumeCategory,
                            ExpenseNature = item.ExpenseNature
                        });
                    }
                    await _comExpenseTypeRepository.InsertManyAsync(expenseList, true);

                    //更新操作记录
                    await _operHistoryRepository.InsertAsync(historyEntity);

                }
                catch (Exception ex)
                {
                    _logger.LogError($"CreateOrUpdateComPSALimit报错: {ex.Message}");
                    return MessageResult.FailureResult();
                }
            }
            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 获取通用PSA上限配置信息详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<GetComPSALimitDetailsResponseDto> GetComPSALimitDetails(Guid comPSALimitId)
        {
            var expenseTypeQuery = await _comExpenseTypeRepository.GetQueryableAsync();

            var res = new GetComPSALimitDetailsResponseDto { ExpenseList = new CreateExpenseTypeDto { List = new List<CreateExpenseTypeRequestDto>() } };
            var psaLimit = await _comPSALimitRepository.GetAsync(x => x.Id == comPSALimitId);
            var psaLimitData = ObjectMapper.Map<OECPSAComPSALimit, ComPSALimitListResponseDto>(psaLimit);
            res.psaLimit = psaLimitData;
            var expenseList = expenseTypeQuery.Where(x => x.ComPSALimitId == comPSALimitId).ToList();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var consumeCategories = await dataverseService.GetConsumeCategoryAsync();
            var expenseNatures = await dataverseService.GetCostNatureAsync();
            foreach (var item in expenseList)
            {
                var expenseListData = new CreateExpenseTypeRequestDto();
                expenseListData.ConsumeCategory = item.ConsumeCategory;
                expenseListData.ConsumeCategoryName = consumeCategories.FirstOrDefault(a => a.Id == item.ConsumeCategory)?.Name;
                expenseListData.ExpenseNature = item.ExpenseNature;
                expenseListData.ExpenseNatureName = expenseNatures.FirstOrDefault(a => a.Id == item.ExpenseNature)?.Name;
                res.ExpenseList.ComPSALimitId = item.ComPSALimitId;
                res.ExpenseList.List.Add(expenseListData);
            }
            var userQuery = await _identityUserRepository.GetQueryableAsync();
            var userId = string.IsNullOrWhiteSpace(res.psaLimit.LastModifierId) ? res.psaLimit.CreatorId : Guid.Parse(res.psaLimit.LastModifierId);
            var user = userQuery.FirstOrDefault(x => x.Id == userId);
            res.psaLimit.LastModifierId = user?.Name;
            return res;
        }

        public Task<MessageResult> DeleteComPSALimit(Guid comPSALimitId)
        {
            //try
            //{
            //    var psaLimit = await _comPSALimitRepository.GetAsync(x => x.Id == comPSALimitId);
            //    if (psaLimit == null)
            //        return MessageResult.FailureResult($"找不到comPSALimitId为{comPSALimitId}的通用PSA上限配置");

            //    await _comPSALimitRepository.HardDeleteAsync(x => x.Id == comPSALimitId);
            //    await _comExpenseTypeRepository.HardDeleteAsync(x => x.ComPSALimitId == comPSALimitId);
            //    await _operHistoryRepository.HardDeleteAsync(x => x.ComPSALimitId == comPSALimitId);
            //    return MessageResult.SuccessResult();
            //}
            //catch (Exception ex)
            //{
            //    _logger.LogError($"DeleteComPSALimit报错: {ex.Message}");
            //    return MessageResult.FailureResult($"DeleteComPSALimit异常{ex.Message}");
            //}

            return Task.FromResult(MessageResult.SuccessResult());
        }

        #region 操作记录

        /// <summary>
        /// 操作日志列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetComPSALimitOperHistory>> GetPSALimitOperHistory(GetPSALimitOperHistoryRequest request)
        {
            var userQuery = await _identityUserRepository.GetQueryableAsync();
            var operHistoryQuery = await _operHistoryRepository.GetQueryableAsync();
            var historyList = operHistoryQuery.Where(x => x.ComPSALimitId == request.ComPSALimitId).ToList();
            historyList = historyList.OrderByDescending(x => x.LastModificationTime).Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToList();
            var data = ObjectMapper.Map<List<OECPSAComPSALimitOperHistory>, List<GetComPSALimitOperHistory>>(historyList);
            data = data.Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToList();
            //给操作人赋值
            var joinUserData = data.Join(userQuery, x => x.CreatorId, y => y.Id, (data, user) => new { data, user }).ToList();//CreatorId不会为null
            foreach (var item in data)
            {
                if (item.CreatorId.HasValue)
                {
                    var user = joinUserData.Find(x => x.user.Id == item.CreatorId).user;
                    item.CreatorStr = user?.Name;
                }
            }
            var res = new PagedResultDto<GetComPSALimitOperHistory>()
            {
                Items = data,
                TotalCount = data.Count(),
            };
            return res;
        }

        /// <summary>
        /// 操作日志详情
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<GetComPSALimitDetailsResponseDto> GetPSALimitOperHistoryDetails(GetPSALimitOperHistoryDetailsRequest request)
        {
            var res = new GetComPSALimitDetailsResponseDto();
            var operHistoryQuery = await _operHistoryRepository.GetQueryableAsync();
            var data = operHistoryQuery.FirstOrDefault(x => x.Id == request.Id && x.ComPSALimitId == request.ComPSALimitId);
            res = JsonConvert.DeserializeObject<GetComPSALimitDetailsResponseDto>(data.ContentDetail);
            return res;
        }

        #endregion

        #region 私有方法
        /// <summary>
        /// 生成序列号
        /// </summary>
        /// <returns></returns>
        private async Task<string> CreateSerialNo()
        {
            string serialNo = "";
            try
            {
                var nowDate = DateTime.Now.Date;
                var count = await _comPSALimitRepository.CountAsync(x => x.CreationTime.Date == nowDate);
                serialNo = $"C{nowDate.ToString("yyyyMMdd")}{string.Format("{0:D4}", count + 1)}";
            }
            catch (Exception ex)
            {
                _logger.LogError($"CreateSerialNo报错{ex.Message}");
            }
            return serialNo;
        }

        /// <summary>
        /// 生效状态赋值
        /// </summary>
        /// <param name="data">The data.</param>
        /// <returns></returns>
        private OECPSAComPSALimit TransToEffectState(OECPSAComPSALimit data)
        {
            if (data == null)
                return null;

            if (DateTime.Now.Date >= data.EffectStart.Date && (!data.EffectEnd.HasValue || data.EffectEnd.HasValue && DateTime.Now.Date <= data.EffectEnd.Value))
            {
                data.DataStatus = EffectStatus.Effective;
            }
            else if (data.EffectEnd.HasValue && DateTime.Now.Date > data.EffectEnd.Value)
            {
                data.DataStatus = EffectStatus.Expired;
            }
            else
                data.DataStatus = EffectStatus.ToBeEffective;
            return data;
        }

        #endregion
    }
}
