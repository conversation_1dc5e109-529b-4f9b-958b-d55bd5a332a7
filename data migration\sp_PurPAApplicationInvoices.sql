CREATE PROCEDURE dbo.sp_PurPAApplicationInvoices
AS 
BEGIN
	--初始化xml
--	select 
--	 a.ProcInstId,
--	 RowData.value('(invoiceNo/text())[1]', 'nvarchar(50)') AS InvoiceNo,
--	 RowData.value('(invoiceDate/text())[1]', 'nvarchar(50)') AS InvoiceDate,
--	 RowData.value('(singleInvoice_Amount/text())[1]', 'nvarchar(50)') AS SingleInvoice_Amount,
--	 RowData.value('(tax_Amount/text())[1]', 'nvarchar(50)') AS tax_Amount,
--	 RowData.value('(noTax_Amount/text())[1]', 'nvarchar(50)') AS noTax_Amount
--	 into PLATFORM_ABBOTT_Dev.dbo.XML_10
--	from PLATFORM_ABBOTT_Dev.dbo.ods_AUTO_BIZ_T_PaymentApplication_Info a 
--	join PLATFORM_ABBOTT_Dev.dbo.ODS_T_FORMINSTANCE_GLOBAL b
--	on a.ProcInstId =b.ProcInstId 
--	CROSS APPLY XmlContent.nodes('/root/InvoiceGridPanel/row') AS XMLTable(RowData)

--	drop table #PurPAApplicationInvoices_tmp
select 
newid() AS Id,--
a.ProcInstId,
a.SerialNumber AS PurPAApplicationId,--基于06-1迁移的申请单主信息，以ProcInstId定位对应的PurGRApplications.ID
c.InvoiceNo AS InvoiceCode,--以ProcInstId找到对应的单据xml后，在InvoiceGridPanel内查询出该PA申请对应的可能多条发票信息，每一个Row即作为一行，查询该字段后填入
c.InvoiceDate AS InvoiceDate,--以ProcInstId找到对应的单据xml后，在InvoiceGridPanel内查询出该PA申请对应的可能多条发票信息，每一个Row即作为一行，查询该字段后填入
c.SingleInvoice_Amount AS InvoiceTotalAmount,--以ProcInstId找到对应的单据xml后，在InvoiceGridPanel内查询出该PA申请对应的可能多条发票信息，每一个Row即作为一行，查询该字段后填入
c.tax_Amount AS TaxAmount,--以ProcInstId找到对应的单据xml后，在InvoiceGridPanel内查询出该PA申请对应的可能多条发票信息，每一个Row即作为一行，查询该字段后填入
c.noTax_Amount AS ExcludingTaxAmount,--以ProcInstId找到对应的单据xml后，在InvoiceGridPanel内查询出该PA申请对应的可能多条发票信息，每一个Row即作为一行，查询该字段后填入
'{}' AS ExtraProperties,--默认填写为"{}"
'' AS ConcurrencyStamp,--?
a.applicationDate AS CreationTime,--与对应的PurPAApplications记录保持一致即可
a.applicantEmpId AS CreatorId,--与对应的PurPAApplications记录保持一致即可
'' AS LastModificationTime,--默认为空
'' AS LastModifierId,--默认为空
'0' AS IsDeleted,--默认为0
'' AS DeleterId,--默认为空
'' AS DeletionTime,--默认为空
invoiceType AS InvoiceType,--(同一张PA在BPM内为相同发票类型)基于发票类型名称找回对应的字典Code
case when a.TaxRate = 'NULL' or a.TaxRate ='' then '0'
else CAST(REPLACE(a.TaxRate, '%', '') AS INT) / 100.0 end  AS TaxRate,--(同一张PA在BPM内为相同税率)移除百分号后除以100(例如1%转换为0.01)
ROW_NUMBER () over(PARTITION by a.ProcInstId,a.SerialNumber,c.InvoiceNo,c.InvoiceDate,c.SingleInvoice_Amount,c.tax_Amount,c.noTax_Amount order by a.ProcInstId desc ) as rowno
into #PurPAApplicationInvoices_tmp
from PLATFORM_ABBOTT_Dev.dbo.ods_AUTO_BIZ_T_PaymentApplication_Info a 
--join PLATFORM_ABBOTT_Dev.dbo.ODS_T_FORMINSTANCE_GLOBAL b
--on a.ProcInstId =b.ProcInstId 
left join PLATFORM_ABBOTT_Dev.dbo.ODS_Form_e37632eb82f04fbda355cffdac744166 d
on a.ProcInstId =d.ProcInstId 
join PLATFORM_ABBOTT_Dev.dbo.XML_10 c
on a.ProcInstId=c.ProcInstId



IF OBJECT_ID(N'PLATFORM_ABBOTT_Dev.dbo.PurPAApplicationInvoices_tmp', N'U') IS NOT NULL
BEGIN
	update a 
	set a.ProcInstId           = b.ProcInstId
       ,a.PurPAApplicationId   = b.PurPAApplicationId
       ,a.InvoiceCode          = b.InvoiceCode
       ,a.InvoiceDate          = b.InvoiceDate
       ,a.InvoiceTotalAmount   = b.InvoiceTotalAmount
       ,a.TaxAmount            = b.TaxAmount
       ,a.ExcludingTaxAmount   = b.ExcludingTaxAmount
       ,a.ExtraProperties      = b.ExtraProperties
       ,a.ConcurrencyStamp     = b.ConcurrencyStamp
       ,a.CreationTime         = b.CreationTime
       ,a.CreatorId            = b.CreatorId
       ,a.LastModificationTime = b.LastModificationTime
       ,a.LastModifierId       = b.LastModifierId
       ,a.IsDeleted            = b.IsDeleted
       ,a.DeleterId            = b.DeleterId
       ,a.DeletionTime         = b.DeletionTime
       ,a.InvoiceType          = b.InvoiceType
       ,a.TaxRate              = b.TaxRate
       ,a.rowno                = b.rowno
    from PLATFORM_ABBOTT_Dev.dbo.PurPAApplicationInvoices_tmp a
    left join #PurPAApplicationInvoices_tmp b on a.ProcInstId = b.ProcInstId and isnull(b.InvoiceCode,'null') = isnull(a.InvoiceCode,'null') and isnull(b.InvoiceDate,'null') = isnull(a.InvoiceDate,'null') 
    and a.InvoiceTotalAmount = b.InvoiceTotalAmount and a.rowno = b.rowno
	
    insert into PLATFORM_ABBOTT_Dev.dbo.PurPAApplicationInvoices_tmp
    select a.Id
          ,a.ProcInstId
          ,a.PurPAApplicationId
          ,a.InvoiceCode
          ,a.InvoiceDate
          ,a.InvoiceTotalAmount
          ,a.TaxAmount
          ,a.ExcludingTaxAmount
          ,a.ExtraProperties
          ,a.ConcurrencyStamp
          ,a.CreationTime
          ,a.CreatorId
          ,a.LastModificationTime
          ,a.LastModifierId
          ,a.IsDeleted
          ,a.DeleterId
          ,a.DeletionTime
          ,a.InvoiceType
          ,a.TaxRate
          ,a.rowno
     from #PurPAApplicationInvoices_tmp a 
     where not exists (select * from PLATFORM_ABBOTT_Dev.dbo.PurPAApplicationInvoices_tmp where ProcInstId = a.ProcInstId and isnull(InvoiceCode,'null') = isnull(a.InvoiceCode,'null') and isnull(InvoiceDate,'null') = isnull(a.InvoiceDate,'null') 
    and InvoiceTotalAmount = a.InvoiceTotalAmount and rowno = a.rowno)
	PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into PLATFORM_ABBOTT_Dev.dbo.PurPAApplicationInvoices_tmp from #PurPAApplicationInvoices_tmp
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END 

END
