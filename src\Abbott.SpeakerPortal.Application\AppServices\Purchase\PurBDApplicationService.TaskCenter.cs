﻿using Abbott.SpeakerPortal.Contracts.Agent;
using Abbott.SpeakerPortal.Contracts.Purchase.PurBDApplication;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Entities.Common.Workflow.WorkflowTasks;
using Abbott.SpeakerPortal.Entities.Purchase.PurBDApplication;
using Abbott.SpeakerPortal.Entities.VendorOrgnizations;
using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.Enums;

using MiniExcelLibs;

using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;

namespace Abbott.SpeakerPortal.AppServices.Purchase
{
    public partial class PurBDApplicationService : SpeakerPortalAppService, IPurBDApplicationService
    {
        /// <summary>
        /// 比价我发起的列表
        /// </summary>
        /// <returns></returns>
        public async Task<PagedResultDto<BDListResponseDto>> GetPurBDInitiateListAsync(BDInitiateListRequest request, bool isPaging = true)
        {
            //获取代理信息
            var agents = await LazyServiceProvider.LazyGetService<IAgencyService>().GetOriginalOperators(new GetAgentOperatorsRequestDto { BusinessTypeCategory = ResignationTransfer.TaskFormCategory.BiddingApplication });
            var userIds = agents.Select(a => a.Key).Distinct().ToArray();
            var principalIds = userIds.Where(a => a != CurrentUser.Id.Value);

            var queryableBD = await LazyServiceProvider.LazyGetService<IPurBDApplicationReadonlyRepository>().GetQueryableAsync();
            var vendorOrg = await LazyServiceProvider.LazyGetService<IVendorOrgnizationReadonlyRepository>().GetQueryableAsync();
            var vendor = await LazyServiceProvider.LazyGetService<IVendorReadonlyRepository>().GetQueryableAsync();
            var queryable = queryableBD
                .GroupJoin(vendorOrg, a => a.VendorId, b => b.VendorId, (a, b) => new { BD = a, Vendor = b })
                .SelectMany(a => a.Vendor.DefaultIfEmpty(), (a, b) => new { a.BD, VendorName = b.VendorName ?? "" })
                .GroupJoin(vendor, a => a.BD.VendorId, b => b.Id, (a, b) => new { a.BD, a.VendorName, Vendor = b })
                .SelectMany(a => a.Vendor.DefaultIfEmpty(), (a, b) => new { a.BD, a.VendorName, Vendor = b })
                .Where(w => (w.BD.ApplyUserId == CurrentUser.Id.Value && !w.BD.TransfereeId.HasValue) || CurrentUser.Id.Value == w.BD.TransfereeId.Value || principalIds.ToHashSet().Contains(w.BD.ApplyUserId))
                .WhereIf(!string.IsNullOrWhiteSpace(request.ApplicationCode), m => m.BD.ApplicationCode.Contains(request.ApplicationCode))
                .WhereIf(!string.IsNullOrWhiteSpace(request.ApplyUserName), m => m.BD.ApplyUserName.Contains(request.ApplyUserName))
                .WhereIf(!string.IsNullOrWhiteSpace(request.VendorName), m => m.BD.VendorName.Contains(request.VendorName))
                .WhereIf(request.ApplyUserBuId.HasValue, m => m.BD.ApplyUserBu == request.ApplyUserBuId)
                .WhereIf(request.StartDate.HasValue, m => m.BD.ApplyTime >= request.StartDate)
                .WhereIf(request.EndDate.HasValue, m => m.BD.ApplyTime <= request.EndDate)
                .WhereIf(request.Status.HasValue, m => m.BD.Status == request.Status);

            var status = new List<PurBDStatus>();
            switch (request.ProcessingStatus)
            {
                case ProcessingStatus.PendingProcessing:
                    status.Add(PurBDStatus.Return);
                    status.Add(PurBDStatus.Recall);
                    break;
                case ProcessingStatus.Progressing:
                    status.Add(PurBDStatus.Approving);
                    break;
                case ProcessingStatus.Completed:
                    status.Add(PurBDStatus.Rejected);
                    status.Add(PurBDStatus.Closed);
                    status.Add(PurBDStatus.Invalid);
                    status.Add(PurBDStatus.Approved);
                    break;
                default:
                    break;
            }
            queryable = queryable.Where(m => status.Contains(m.BD.Status));

            var count = queryable.Count();
            //判断是否需要分页
            if (isPaging)
            {
                queryable = queryable.OrderByDescending(o => o.BD.ApplyTime).Skip(request.PageIndex * request.PageSize).Take(request.PageSize);
            }
            //var getBuTask = await dataverseService.GetDivisions();
            var datas = queryable.ToList().Select(s => new BDListResponseDto
            {
                Id = s.BD.Id,
                ApplicationCode = s.BD.ApplicationCode,
                Status = s.BD.Status,
                ApplyUserName = s.BD.ApplyUserName,
                ApplyTime = s.BD.ApplyTime?.ToString("yyyy-MM-dd HH:mm:ss"),
                ApplyUserBuName = s.BD.ApplyUserBuName,
                VendorName = s.BD.VendorName,
                TotalAmount = s.BD.TotalAmount,
            }).ToList();
            var result = new PagedResultDto<BDListResponseDto>(count, datas);
            return result;
        }

        /// <summary>
        /// 导出我发起的比价列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<Stream> ExportBDApplicationsInitiate(BDInitiateListRequest request)
        {
            using MemoryStream stream = new();
            List<object> list = [];
            list.AddRange((await GetPurBDInitiateListAsync(request, false)).Items);
            stream.SaveAs(list, true, "Sheet1");
            stream.Seek(0, SeekOrigin.Begin);
            return stream;
        }


        /// <summary>
        /// 比价我审批的列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<BDListResponseDto>> GetBDApprovalListAsync(BDApplicationListRequest request, bool isPage = true)
        {
            var result = new PagedResultDto<BDListResponseDto>();
            var queryBD = await LazyServiceProvider.LazyGetService<IPurBDApplicationReadonlyRepository>().GetQueryableAsync();
            var vendor = await LazyServiceProvider.LazyGetService<IVendorReadonlyRepository>().GetQueryableAsync();

            //待处理
            if (request.ProcessingStatus == ProcessingStatus.PendingProcessing)
            {
                var taskRecords = await LazyServiceProvider.LazyGetService<IDataverseService>().GetApprovelTaskAsync(CurrentUser.Id.ToString(), [WorkflowTypeName.ComparePricesToBuy, WorkflowTypeName.ComparePricesToBuySpecialType], request.ProcessingStatus.Value);

                var bds = queryBD.Where(a => taskRecords.Select(b => b.FormId).ToList().Contains(a.Id))
                .GroupJoin(vendor, po => po.VendorId, v => v.Id, (po, v) => new { po, ve = vendor.FirstOrDefault() })
                .WhereIf(!string.IsNullOrWhiteSpace(request.ApplicationCode), a => a.po.ApplicationCode.Contains(request.ApplicationCode))
                .WhereIf(!string.IsNullOrWhiteSpace(request.ApplyUserName), a => a.po.ApplyUserName.Contains(request.ApplyUserName))
                .WhereIf(request.ApplyUserBuId.HasValue, a => a.po.ApplyUserBu == request.ApplyUserBuId)
                .WhereIf(!string.IsNullOrWhiteSpace(request.VendorName), a => a.po.VendorName.Contains(request.VendorName))
                .WhereIf(request.StartDate.HasValue, a => a.po.ApplyTime >= request.StartDate)
                .WhereIf(request.EndDate.HasValue, a => a.po.ApplyTime <= request.EndDate)
                .OrderByDescending(a => a.po.ApplyTime)
                .Select(a => new BDListResponseDto
                {
                    Id = a.po.Id,
                    ApplicationCode = a.po.ApplicationCode,
                    Status = a.po.Status,
                    ApplyUserName = a.po.ApplyUserName,
                    ApplyTime = a.po.ApplyTime.Value.ToString("yyyy-MM-dd HH:mm:ss"),
                    ApplyUserBuName = a.po.ApplyUserBuName,
                    VendorName = a.po.VendorName,
                    TotalAmount = a.po.TotalAmount
                })
                .ToArray();

                result.TotalCount = bds.Length;
                if (request.IsAsc)
                {
                    result.Items = bds.Join(taskRecords, a => a.Id, a => a.FormId, (a, b) => new { Bd = a, Task = b })
                    .OrderBy(a => a.Task.CreatedTime)
                    .Select(a => new BDListResponseDto
                    {
                        Id = a.Bd.Id,
                        ApplicationCode = a.Bd.ApplicationCode,
                        Status = a.Bd.Status,
                        ApplyUserName = a.Bd.ApplyUserName,
                        ApplyTime = a.Bd.ApplyTime,
                        ApplyUserBuName = a.Bd.ApplyUserBuName,
                        VendorName = a.Bd.VendorName,
                        TotalAmount = a.Bd.TotalAmount,
                        ApprovalStep = a.Task.Step
                    })
                    .ToArray();
                }
                else
                {
                    result.Items = bds.Join(taskRecords, a => a.Id, a => a.FormId, (a, b) => new { Bd = a, Task = b })
                    .OrderByDescending(a => a.Task.CreatedTime)
                    .Select(a => new BDListResponseDto
                    {
                        Id = a.Bd.Id,
                        ApplicationCode = a.Bd.ApplicationCode,
                        Status = a.Bd.Status,
                        ApplyUserName = a.Bd.ApplyUserName,
                        ApplyTime = a.Bd.ApplyTime,
                        ApplyUserBuName = a.Bd.ApplyUserBuName,
                        VendorName = a.Bd.VendorName,
                        TotalAmount = a.Bd.TotalAmount,
                        ApprovalStep = a.Task.Step
                    })
                    .ToArray();
                }
            }
            else//已完成
            {
                var queryWfTask = await LazyServiceProvider.LazyGetService<IWorkflowTaskReadonlyRepository>().GetQueryableAsync();
                var query = queryBD
                    .GroupJoin(vendor, po => po.VendorId, v => v.Id, (po, v) => new { po, ve = vendor.FirstOrDefault() })
                    .WhereIf(!string.IsNullOrWhiteSpace(request.ApplicationCode), a => a.po.ApplicationCode.Contains(request.ApplicationCode))
                    .WhereIf(!string.IsNullOrWhiteSpace(request.ApplyUserName), a => a.po.ApplyUserName.Contains(request.ApplyUserName))
                    .WhereIf(request.ApplyUserBuId.HasValue, a => a.po.ApplyUserBu == request.ApplyUserBuId)
                    .WhereIf(!string.IsNullOrWhiteSpace(request.VendorName), a => a.po.VendorName.Contains(request.VendorName))
                    .WhereIf(request.StartDate.HasValue, a => a.po.ApplyTime >= request.StartDate)
                    .WhereIf(request.EndDate.HasValue, a => a.po.ApplyTime <= request.EndDate)
                    .Join(queryWfTask.Where(a => a.ApprovalId == CurrentUser.Id), a => a.po.Id, a => a.FormId, (a, b) => new { a.po, a.ve, Task = b })
                    .Select(a => new BDListResponseDto
                    {
                        Id = a.po.Id,
                        ApplicationCode = a.po.ApplicationCode,
                        Status = a.po.Status,
                        ApplyUserName = a.po.ApplyUserName,
                        ApplyTime = a.po.ApplyTime.Value.ToString("yyyy-MM-dd HH:mm:ss"),
                        ApplyUserBuName = a.po.ApplyUserBuName,
                        VendorName = a.po.VendorName,
                        TotalAmount = a.po.TotalAmount,
                        ApprovalStep = a.Task.StepNo,
                        ApprovalTime = a.Task.ApprovalTime
                    });

                result.TotalCount = query.Count();
                if (request.IsAsc)
                    result.Items = query.OrderBy(a => a.ApprovalTime).Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToList();
                else
                    result.Items = query.OrderByDescending(a => a.ApprovalTime).Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToList();
            }

            return result;
        }

        /// <summary>
        /// 导出我审批的列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<Stream> ExportBDApprovalListAsync(BDApplicationListRequest request)
        {
            using MemoryStream stream = new();
            var bdList = await GetBDApprovalListAsync(request, false);
            List<object> list = [];
            //var resultData = ObjectMapper.Map<List<BDListResponseDto>, List<BDApprovalResponseDto>>(bdList.Items.ToList());
            //list.Add(resultData);
            stream.SaveAs(list, true, "Sheet1");
            stream.Seek(0, SeekOrigin.Begin);
            return stream;
        }
    }
}
