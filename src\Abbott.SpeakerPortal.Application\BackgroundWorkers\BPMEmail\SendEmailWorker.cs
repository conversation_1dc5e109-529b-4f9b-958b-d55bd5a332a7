﻿using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.Common;
using Abbott.SpeakerPortal.Entities.BPMEmail;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using DocumentFormat.OpenXml.Drawing.Charts;
using Hangfire;
using Medallion.Threading;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.DistributedLocking;

namespace Abbott.SpeakerPortal.BackgroundWorkers.BPMEmail
{
    /// <summary>
    /// 发送邮件Job
    /// </summary>
    public class SendEmailWorker : SpeakerPortalBackgroundWorkerBase
    {
        public SendEmailWorker()
        {
            CronExpression = Cron.Never();
        }
        public override async Task DoWorkAsync(CancellationToken cancellationToken = default)
        {
            //1、加锁执行 执行完自动释放锁
            await using (var @lock = await LazyServiceProvider.LazyGetService<IAbpDistributedLock>().TryAcquireAsync(DistributedLock.DistributedLock_BPMEmail))
            {
                if (@lock == null)
                {
                    // 锁已被其他任务持有，跳过
                    return;
                }
                var emailService = LazyServiceProvider.LazyGetService<IEmailService>();
                await emailService.SendBPMEmailAsync();
            }
        }

        public async Task DoWorkAsync()
        {
            // 使用默认的 CancellationToken
            await DoWorkAsync(CancellationToken.None);
        }
    }
}
