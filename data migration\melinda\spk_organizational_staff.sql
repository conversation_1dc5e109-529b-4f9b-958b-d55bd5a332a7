select newid() as spk_NexBPMCode,
[spk_code],
ss.spk_NexBPMCode as [spk_staffmasterdataid],
ot.spk_NexBPMCode as [spk_organizationalmasterdataid]
into #spk_organizational_staff
from spk_organizational_staff_tmp oct
join spk_organizationalmasterdata ot
on oct.spk_organizationalmasterdataid=ot.spk_BPMCode
join spk_staffmasterdata ss 
on oct.spk_staffmasterdataid =ss.bpm_id 


IF OBJECT_ID(N'dbo.spk_organizational_staff', N'U') IS NOT NULL
BEGIN
	drop table dbo.spk_organizational_staff
	select  *  into dbo.spk_organizational_staff from #spk_organizational_staff
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into dbo.spk_organizational_staff from #spk_organizational_staff
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END

