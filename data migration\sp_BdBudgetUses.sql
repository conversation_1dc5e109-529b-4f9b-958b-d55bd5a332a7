CREATE PROCEDURE dbo.sp_BdBudgetUses
AS 
BEGIN
	
	with ReturnInfo as (
	select ProcInstId,PRNumber,sum(Budget) Budget
	from PLATFORM_ABBOTT_Dev.dbo.ods_T_Pur_ExpenseBudget_ReturnInfo
	group by ProcInstId,PRNumber
),
ReturnInfo1 as (
	select DISTINCT a.ProcInstId,a.PRNumber,isnull(b.OperateDate,'') OperateDate from PLATFORM_ABBOTT_Dev.dbo.ODS_T_Pur_ExpenseBudget_UseInfo a
	left join (
	select * from (select *,row_number() over(partition by ProcInstId,PRNumber order by  operateDate desc)  rn
	from PLATFORM_ABBOTT_Dev.dbo.ods_T_Pur_ExpenseBudget_ReturnInfo)c where rn=1
	) b
	on a.ProcInstId= b.ProcInstId and a.PRNumber=b.PRNumber
)
select 
newid() AS Id,--自动生成的uuid
a.id as bpm_id,
case when d.Folio <> '' or d.Folio <> null then g1.id else g.id end AS PrId,--以该编码匹配至AUTO_BIZ_T_ProcurementApplication_Info.ProcInstId，以查询出的SerialNumber去查询出PurPRApplications对应的ID；
--若无法查询到记录则留为空"
a.PRNumber AS PdRowNo,--
a.Number AS SubbudgetId,--以该编码匹配至12-1迁移的子预算ID
case when a.IsEnable='1' then a.Budget-isnull(b.Budget,0) when a.IsEnable='0' then '0'  else '0' end AS Amount,--若IsEnable=1，此处金额填写为:
--该ProcInstId+PRNumber组合下的UseInfo内Budget，减去相同组合下ReturnInfo内Budget的总和"
--IsEnable AS ,--若IsEnable=0，此处金额需要填写为0【后续在ReturnInfo表需要插入新的记录】
case when IsEnable=1 then case when e.OperateDate='' then a.OperateDate else e.OperateDate end   end AS OperateTime,--若IsEnable=1，则取该ProcInstId+PRNumber组合下的ReturnInfo内最新记录的OperateDate，若无法找到ReturnInfo的记录，则填写为UseInfo的OperateDate
--OperateDate AS ,--若IsEnable=0，则填入该行的OperateDate
'1' AS IsEnable,--默认填写为1
'{}' AS ExtraProperties,--默认填写为"{}"
'' AS ConcurrencyStamp,--?
f.applicationDate AS CreationTime,--填充为该PR的申请时间
f.applicantEmpId AS CreatorId,--填充为该PR的申请人，即PurPRApplications.ApplyUserId
a.OperateDate AS LastModificationTime,--按上方的逻辑计算后填充为OperateTime
f.applicantEmpId AS LastModifierId,--更新逻辑比较复杂，需要查询的场景太多了，因此直接填充为该PR的申请人，即PurPRApplications.ApplyUserId
'0' AS IsDeleted,--默认填写为0
'' AS DeleterId,--默认为空
'' AS DeletionTime,--默认为空
case when d.Folio <> '' or d.Folio <> null then d.Folio else f.serialNumber end  AS Nbr,--等待开发加入对应字段，PR申请单号/批发商核销申请单号
case when (d.Folio <> '' or d.Folio <> null ) and (SUBSTRING(d.Folio,1,1)='P' or SUBSTRING(d.Folio,1,1) not in ('P','S')) then '1' 
when (d.Folio <> '' or d.Folio <> null ) and SUBSTRING(d.Folio,1,1)='S'  then '2'
when (d.Folio = '' or d.Folio  is null ) and (SUBSTRING(f.serialNumber,1,1)='P' or SUBSTRING(f.serialNumber,1,1) not in ('P','S')) then '1'
else 2 end  AS Type--等待开发加入对应字段，记录类型：采购申请/批发商核销申请
,d.Folio,SUBSTRING(d.Folio,1,1) a
into #BdBudgetUses_tmp
from PLATFORM_ABBOTT_Dev.dbo.ODS_T_Pur_ExpenseBudget_UseInfo a 
left join ReturnInfo b
on a.ProcInstId =b.ProcInstId  and a.PRNumber =b.PRNumber 
left join ReturnInfo1 e
on a.ProcInstId =e.ProcInstId  and a.PRNumber =e.PRNumber 
--left join PLATFORM_ABBOTT_Dev.dbo.ODS_AUTO_BIZ_T_ProcurementApplication_Info c
--on a.ProcInstId =c.ProcInstId 
left join (select *,ROW_NUMBER () over(PARTITION by ProcInstId order by ProcInstId) rn from PLATFORM_ABBOTT_Dev.dbo.ODS_T_PROCESS_Historys) d
on a.ProcInstId =d.ProcInstID and d.rn=1
left join (select *,ROW_NUMBER () over(PARTITION by ProcInstId order by ProcInstId) rn from PLATFORM_ABBOTT_Dev.dbo.ods_AUTO_BIZ_T_ProcurementApplication_Info) f 
on a.ProcInstId =f.ProcInstId and f.rn=1
left join (select *,ROW_NUMBER () over(PARTITION by ApplicationCode order by ProcInstId desc) rn from PLATFORM_ABBOTT_Dev.dbo.PurPRApplications_tmp) g
on f.serialNumber=g.ApplicationCode and g.rn=1
left join (select *,ROW_NUMBER () over(PARTITION by ApplicationCode order by ProcInstId desc) rn from PLATFORM_ABBOTT_Dev.dbo.PurPRApplications_tmp) g1
on d.Folio=g1.ApplicationCode and g1.rn=1



 IF OBJECT_ID(N'PLATFORM_ABBOTT_Dev.dbo.BdBudgetUses_tmp ', N'U') IS NOT NULL
	BEGIN
		update a 
		set a.bpm_id               = b.bpm_id
           ,a.PrId                 = b.PrId
           ,a.PdRowNo              = b.PdRowNo
           ,a.SubbudgetId          = b.SubbudgetId
           ,a.Amount               = b.Amount
           ,a.OperateTime          = b.OperateTime
           ,a.IsEnable             = b.IsEnable
           ,a.ExtraProperties      = b.ExtraProperties
           ,a.ConcurrencyStamp     = b.ConcurrencyStamp
           ,a.CreationTime         = b.CreationTime
           ,a.CreatorId            = b.CreatorId
           ,a.LastModificationTime = b.LastModificationTime
           ,a.LastModifierId       = b.LastModifierId
           ,a.IsDeleted            = b.IsDeleted
           ,a.DeleterId            = b.DeleterId
           ,a.DeletionTime         = b.DeletionTime
           ,a.Nbr                  = b.Nbr
           ,a.[Type]               = b.[Type]
           ,a.Folio                = b.Folio
           ,a.a                    = b.a
        from PLATFORM_ABBOTT_Dev.dbo.BdBudgetUses_tmp a
        left join #BdBudgetUses_tmp b on a.Bpm_id = b.Bpm_id
        
        insert into PLATFORM_ABBOTT_Dev.dbo.BdBudgetUses_tmp 
        select a.Id
              ,a.bpm_id
              ,a.PrId
              ,a.PdRowNo
              ,a.SubbudgetId
              ,a.Amount
              ,a.OperateTime
              ,a.IsEnable
              ,a.ExtraProperties
              ,a.ConcurrencyStamp
              ,a.CreationTime
              ,a.CreatorId
              ,a.LastModificationTime
              ,a.LastModifierId
              ,a.IsDeleted
              ,a.DeleterId
              ,a.DeletionTime
              ,a.Nbr
              ,a.[Type]
              ,a.Folio
              ,a.a
        from #BdBudgetUses_tmp a
        where not exists (select * from PLATFORM_ABBOTT_Dev.dbo.BdBudgetUses_tmp where Bpm_id = a.Bpm_id)
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into PLATFORM_ABBOTT_Dev.dbo.BdBudgetUses_tmp from #BdBudgetUses_tmp
    -- select * from #vendor_tbl
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
END;
