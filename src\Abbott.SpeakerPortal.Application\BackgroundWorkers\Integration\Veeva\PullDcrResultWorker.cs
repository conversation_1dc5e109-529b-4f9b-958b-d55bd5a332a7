﻿using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Integration.Veeva;
using Hangfire;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.BackgroundWorkers.Integration.Veeva
{
    /// <summary>
    /// 拉取DCR 验证结果
    /// </summary>
    public class PullDcrResultWorker : SpeakerPortalBackgroundWorkerBase
    {
        private IInteVeevaService _inteVeevaService;
        private IScheduleJobLogService _jobLogService;

        public PullDcrResultWorker(IServiceProvider serviceProvider) 
        {
            _inteVeevaService = serviceProvider.GetService<IInteVeevaService>();
            _jobLogService = serviceProvider.GetService<IScheduleJobLogService>();
            CronExpression = "0 */4 * * *";//每隔四小时执行一次

        }

        public override async Task DoWorkAsync(CancellationToken cancellationToken = default)
        {
            var log = _jobLogService.InitSyncLog("Veeva_PullDCRResult");
            try
            {
                log.Remark = _inteVeevaService.PullDcrResult();
            }
            catch (Exception ex)
            {
                log.IsSuccess = false;
                log.Remark = ex.ToString();
            }
            finally
            {
                _jobLogService.SyncLog(log);
            }          
        }
    }
}
