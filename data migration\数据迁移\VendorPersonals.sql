--drop table #VendorPersonals
SELECT 
 TRY_CONVERT(UNIQUEIDENTIFIER, [Id]) [Id]
,TRY_CONVERT(UNIQUEIDENTIFIER,  [VendorId] ) [VendorId]--中间层为空，需检查中间层逻辑
,CASE WHEN [Sex] = N'男' --转换中文性别为数字
	THEN 1
	ELSE 0
	END [Sex]
,ISNULL([CardType],N'NULL') [CardType]
,case when [CardNo] is null or CardNo ='' then '000000' else CardNo end as CardNo
,[CardPic]
,ISNULL([Province],N'000000') [Province] -- 转换为空的数据，需检查中间层逻辑
,ISNULL([City],N'000000') [City] -- 转换为空的数据，需检查中间层逻辑
,case when [Address] is null or Address ='' then '' else Address end [Address]
,case when [PostCode] is null or PostCode ='' then '000000' else PostCode end [PostCode]
,'{}'[ExtraProperties]
,''[ConcurrencyStamp]
,[CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CreatorId]) [CreatorId]
,[LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [LastModifierId]) [LastModifierId]
,0 [IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, CASE WHEN [DeleterId] = '' or [DeleterId] is null  THEN '00000000-0000-0000-0000-000000000000' ELSE [DeleterId] END)  [DeleterId]
,[DeletionTime]
,[SPName]
,[AffiliationOrgan]
,[Email]
INTO #VendorPersonals
FROM (SELECT DISTINCT *,ROW_NUMBER() OVER(PARTITION BY Id ORDER BY Id) RK FROM PLATFORM_ABBOTT.dbo.VendorPersonals)a
WHERE RK = 1;

--select * from #VendorPersonals where spname=N'高翔'


USE Speaker_Portal;


--select * into Speaker_Portal_STG.dbo.VendorPersonals_bak20240822 from Speaker_Portal_STG.dbo.VendorPersonals
--truncate table Speaker_Portal_STG.dbo.VendorPersonals

--select * from #VendorPersonals  

--UPDATE a
--SET 
-- a.[Id] =  TRY_CONVERT(UNIQUEIDENTIFIER, b.[Id])
--,a.[VendorId] = TRY_CONVERT(UNIQUEIDENTIFIER, b.[VendorId])
--,a.[Sex] = b.[Sex]
--,a.[CardType] = b.[CardType]
--,a.[CardNo] = b.[CardNo]
--,a.[CardPic] = b.[CardPic]
--,a.[Province] = b.[Province]
--,a.[City] = b.[City]
--,a.[Address] = b.[Address]
--,a.[PostCode] = b.[PostCode]
--,a.[ExtraProperties] = b.[ExtraProperties]
--,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
--,a.[CreationTime] = b.[CreationTime]
--,a.[CreatorId] = TRY_CONVERT(UNIQUEIDENTIFIER, b.[CreatorId])
--,a.[LastModificationTime] = b.[LastModificationTime]
--,a.[LastModifierId] = TRY_CONVERT(UNIQUEIDENTIFIER, b.[LastModifierId])
--,a.[IsDeleted] = b.[IsDeleted]
--,a.[DeleterId] = TRY_CONVERT(UNIQUEIDENTIFIER, b.[DeleterId])
--,a.[DeletionTime] = b.[DeletionTime]
--,a.[SPName] = b.[SPName]
--,a.[AffiliationOrgan] = b.[AffiliationOrgan]
--,a.[Email] = b.[Email]
--FROM dbo.VendorPersonals a
--left join #VendorPersonals  b
--ON a.id=b.id;

UPDATE a
SET 
 a.[Id] =  b.[Id]
,a.[VendorId] = b.[VendorId]
,a.[Sex] = b.[Sex]
,a.[CardType] = b.[CardType]
,a.[CardNo] = b.[CardNo]
,a.[CardPic] = b.[CardPic]
,a.[Province] = b.[Province]
,a.[City] = b.[City]
,a.[Address] = b.[Address]
,a.[PostCode] = b.[PostCode]
,a.[ExtraProperties] = b.[ExtraProperties]
,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = b.[CreatorId]
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = b.[LastModifierId]
,a.[IsDeleted] = b.[IsDeleted]
,a.[DeleterId] = b.[DeleterId]
,a.[DeletionTime] = b.[DeletionTime]
,a.[SPName] = b.[SPName]
,a.[AffiliationOrgan] = b.[AffiliationOrgan]
,a.[Email] = b.[Email]
FROM dbo.VendorPersonals a
left join #VendorPersonals  b
ON a.id=b.id;

INSERT INTO dbo.VendorPersonals
(
 [Id]
,[VendorId]
,[Sex]
,[CardType]
,[CardNo]
,[CardPic]
,[Province]
,[City]
,[Address]
,[PostCode]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[SPName]
,[AffiliationOrgan]
,[Email]
)
SELECT
 [Id]
,[VendorId]
,[Sex]
,[CardType]
,[CardNo]
,[CardPic]
,[Province]
,[City]
,[Address]
,[PostCode]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[SPName] --修改目标表的字段长度nvarchar(max)
,[AffiliationOrgan]
,[Email]
FROM #VendorPersonals a
WHERE not exists (select * from dbo.VendorPersonals where id=a.id)

--truncate table dbo.VendorPersonals
--alter table dbo.VendorPersonals
--alter column SPName nvarchar(max)
