﻿using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Abbott.SpeakerPortal.Contracts.Integration.Dspot
{
    public class ModifyEpdHcpCodeRequestDto
    {
        /// <summary>
        /// EPD医生主键_新
        /// </summary>
        [Required]
        [JsonPropertyName("epdHcpCodeNew")]
        public string EpdHcpCodeNew { get; set; }

        /// <summary>
        /// EPD医生主键_原
        /// </summary>
        [Required]
        [JsonPropertyName("epdHcpCodeOriginal")]
        public string EpdHcpCodeOriginal { get; set; }
    }
}