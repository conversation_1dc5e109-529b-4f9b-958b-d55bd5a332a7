﻿using System;
using System.Collections.Generic;
using System.Linq;
using Abbott.SpeakerPortal.Contracts.Common.Attachment;

namespace Abbott.SpeakerPortal.Contracts.Budget.FocSubbudget
{
    public class GetFocSubbudgetDetailResponseDto
    {
        /// <summary>
        /// 子预算Id
        /// </summary>
        public Guid Id { get; set; }
        /// <summary>
        /// 子预算编号
        /// </summary>
        public string Code { get; set; }
        /// <summary>
        /// BuId
        /// </summary>
        public Guid BuId { get; set; }
        /// <summary>
        /// 产品ID
        /// </summary>
        public Guid ProductId { get; set; }

        /// <summary>
        /// 产品简称
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 产品品牌信息编码
        /// </summary>
        public string ProductMCode { get; set; }

        /// <summary>
        /// 产品规格
        /// </summary>
        public string ProductUnit { get; set; }
        /// <summary>
        /// 成本中心
        /// </summary>
        public Guid CostCenterId { get; set; }
        /// <summary>
        /// 成本中心名称
        /// </summary>
        public string CostCenterName { get; set; }
        /// <summary>
        /// 大区
        /// </summary>
        public Guid RegionId { get; set; }
        /// <summary>
        /// 大区名称
        /// </summary>
        public string RegionName { get; set; }
        /// <summary>
        /// 负责人
        /// </summary>
        public Guid OwnerId { get; set; }
        /// <summary>
        /// 负责人名称
        /// </summary>
        public string OwnerName { get; set; }
        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 是否合规审计审批
        /// </summary>
        public bool IsComplicanceAudits { get; set; }

        /// <summary>
        /// 预算数量
        /// </summary>
        public int BudgetQty { get; set; }
        /// <summary>
        /// 开启数量
        /// </summary>
        public int EnableQty { get { return this.MonthlyBudgets.Where(m => m.Status).Sum(s => s.Qty); } }
        /// <summary>
        /// 已使用数量
        /// </summary>
        public int UsedQty { get; set; }
        /// <summary>
        /// 可用金额
        /// </summary>
        public decimal AvailableAmount { get { return EnableQty - UsedQty; } }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 附件
        /// </summary>
        public List<UploadFileResponseDto> AttachmentInformation { get; set; }
        /// <summary>
        /// 月份预算
        /// </summary>
        public ICollection<FocMonthlyBudgetResponseDto> MonthlyBudgets { get; set; }
    }
}
