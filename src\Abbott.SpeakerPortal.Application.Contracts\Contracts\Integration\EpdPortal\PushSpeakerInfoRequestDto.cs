﻿using System.Text.Json.Serialization;

namespace Abbott.SpeakerPortal.Contracts.Integration.Dspot
{
    public class PushSpeakerInfoRequestDto
    {
        [JsonPropertyName("type")]
        public string Type { get; set; }
        [JsonPropertyName("nextBpmHcpId")]
        public string NextBpmHcpId { get; set; }
        [Json<PERSON>ropertyName("abbottHcpId")]
        public string AbbottHcpId { get; set; }
        [JsonPropertyName("name")]
        public string Name { get; set; }
        [Json<PERSON>ropertyName("hospitalName")]
        public string HospitalName { get; set; }
        [JsonPropertyName("departmentName")]
        public string DepartmentName { get; set; }
        [Json<PERSON>ropertyName("professionalTitle")]
        public string ProfessionalTitle { get; set; }
        [JsonPropertyName("result")]
        public string Result { get; set; }
    }
}