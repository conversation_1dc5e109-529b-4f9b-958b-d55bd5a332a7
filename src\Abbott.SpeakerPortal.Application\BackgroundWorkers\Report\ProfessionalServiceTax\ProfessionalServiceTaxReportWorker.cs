﻿using Abbott.SpeakerPortal.Contracts.Integration.Dspot;
using Abbott.SpeakerPortal.Contracts.Report.ProfessionalServiceTax;
using Abbott.SpeakerPortal.Entities.Budget;
using Hangfire;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.BackgroundWorkers.Report.ProfessionalServiceTax
{
    public class ProfessionalServiceTaxReportWorker : SpeakerPortalBackgroundWorkerBase
    {
        public ProfessionalServiceTaxReportWorker()
        {
            CronExpression = Cron.Daily(5);//每天凌晨4点
        }
        public override async Task DoWorkAsync(CancellationToken cancellationToken = default)
        {
            //逻辑处理
            var professionalTaxReportService = LazyServiceProvider.LazyGetService<IProfessionalTaxReportService>();
            await professionalTaxReportService.GetGlhIncrement();

        }
    }
}
