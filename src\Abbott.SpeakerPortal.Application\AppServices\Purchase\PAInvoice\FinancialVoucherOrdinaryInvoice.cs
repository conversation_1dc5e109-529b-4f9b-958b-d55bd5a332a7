﻿using Abbott.SpeakerPortal.Contracts.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPAApplication.PAInvoice;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.AppServices.Purchase.PAInvoice
{
    /// <summary>
    /// 普通发票
    /// </summary>
    public class FinancialVoucherOrdinaryInvoice : IFinancialVoucherInvoice
    {
        public async Task<List<FinancialVoucherInfoDto>> GetFinancialVoucherInfo(PurPAApplicationDetailDto paApplicationDetail)
        {
            List<FinancialVoucherInfoDto> financialVoucherInfos = new List<FinancialVoucherInfoDto>();
            //普通发票 默认税率为0 生成一条财务凭证信息
            var financialVoucherInfo = new FinancialVoucherInfoDto();
            financialVoucherInfo.PAId = paApplicationDetail.PurPAApplicationId;
            financialVoucherInfo.PRDetailId = paApplicationDetail.PRDetailId;
            financialVoucherInfo.PODetailId = paApplicationDetail.PODetailId;
            financialVoucherInfo.GRHistoryId = paApplicationDetail.GRHistoryId;
            financialVoucherInfo.PAInvoiceId = paApplicationDetail.Id;
            financialVoucherInfo.InvoiceType = "OrdinaryInvoice";
            financialVoucherInfo.GroupingDimension = GroupingDimension.TaxNotIncluded;
            financialVoucherInfo.InvoiceLineAmount = decimal.Round(paApplicationDetail.PaymentAmount,2);
            financialVoucherInfos.Add(financialVoucherInfo);
            return await Task.FromResult(financialVoucherInfos);
        }
    }
}
