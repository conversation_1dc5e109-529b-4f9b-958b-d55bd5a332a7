SELECT
 TRY_CONVERT(UNIQUEIDENTIFIER, [Id]) [Id]
,[ApplicationCode]
,CASE [Status]
	WHEN N'待收货' THEN 1
	WHEN N'已收货' THEN 2
	WHEN N'终止收货' THEN 3
	WHEN N'加签人加签' THEN 4
	WHEN N'重发起' THEN 5
	WHEN N'付款中' THEN 6
	WHEN N'终止审批中' THEN 7
	ELSE 0
	END [Status]
,TRY_CONVERT(UNIQUEIDENTIFIER, [ApplyUserId]) [ApplyUserId]
,[ApplyTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([ApplyUserBu],'00000000-0000-0000-0000-000000000000')) [ApplyUserBu]
,[EsignPdf]
,[DeliveryMode]
,[MeetingModifyRemark]
,[AmountModifyRemark]
,TRY_CONVERT(UNIQUEIDENTIFIER, [ProcurementPersonnelId]) [ProcurementPersonnelId]
,TRY_CONVERT(UNIQUEIDENTIFIER, [VendorId]) [VendorId]
,[IsAdvancePayment]
,[AdditionalSignerId]
,[Remarks]
,[PSAIds]
,[ExtraProperties]
,'NULL' [ConcurrencyStamp]
,[CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CreatorId])[CreatorId]
,[LastModificationTime] 
,TRY_CONVERT(UNIQUEIDENTIFIER, [LastModifierId]) [LastModifierId]
,[IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, [DeleterId]) [DeleterId]
,[DeletionTime]
,[ApplyUserBuName]
,[ApplyUserName]
,[VendorName]
,[BudgetCode]
,TRY_CONVERT(UNIQUEIDENTIFIER, [BudgetId]) [BudgetId]
,TRY_CONVERT(UNIQUEIDENTIFIER, [PoId]) [PoId]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([PrId],'00000000-0000-0000-0000-000000000000')) [PrId]
,[ApplyUserBuToDeptName]
,[PoApplicationCode]
,[PrApplicationCode]
,0 [PaymentExcludingTaxAmount]
,[CompanyCode]
,[PayMethod]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CompanyId]) [CompanyId]
,[CompanyName]
,[VendorCode]
,[ReceivedTime]
,NULL [Currency]
,NULL [CurrencySymbol]
,0 [ExchangeRate]
,0 [ExpectedFloatRate]
,0 [PlanRate]
,0 [IsZeroSpeaker]
,NULL [BpcsRate]
,TRY_CONVERT(UNIQUEIDENTIFIER, '00000000-0000-0000-0000-000000000000') [TransfereeId]
, NULL [TransfereeName]
INTO #PurGRApplications
FROM (SELECT *,ROW_NUMBER() OVER(PARTITION BY Id ORDER BY Id) RK FROM PLATFORM_ABBOTT_STG.dbo.PurGRApplications)a
WHERE RK = 1
;

select * from PLATFORM_ABBOTT_STG.dbo.PurGRApplications
--drop table #PurGRApplications

USE Speaker_Portal_STG;

UPDATE a 
SET 
 a.[Id] = b.[Id]
,a.[ApplicationCode] = b.[ApplicationCode]
,a.[Status] = b.[Status]
,a.[ApplyUserId] = b.[ApplyUserId]
,a.[ApplyTime] = b.[ApplyTime]
,a.[ApplyUserBu] = b.[ApplyUserBu]
,a.[EsignPdf] = b.[EsignPdf]
,a.[DeliveryMode] = b.[DeliveryMode]
,a.[MeetingModifyRemark] = b.[MeetingModifyRemark]
,a.[AmountModifyRemark] = b.[AmountModifyRemark]
,a.[ProcurementPersonnelId] = b.[ProcurementPersonnelId]
,a.[VendorId] = b.[VendorId]
,a.[IsAdvancePayment] = b.[IsAdvancePayment]
,a.[AdditionalSignerId] = b.[AdditionalSignerId]
,a.[Remarks] = b.[Remarks]
,a.[PSAIds] = b.[PSAIds]
,a.[ExtraProperties] = b.[ExtraProperties]
,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = b.[CreatorId]
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = b.[LastModifierId]
,a.[IsDeleted] = b.[IsDeleted]
,a.[DeleterId] = b.[DeleterId]
,a.[DeletionTime] = b.[DeletionTime]
,a.[ApplyUserBuName] = b.[ApplyUserBuName]
,a.[ApplyUserName] = b.[ApplyUserName]
,a.[VendorName] = b.[VendorName]
,a.[BudgetCode] = b.[BudgetCode]
,a.[BudgetId] = b.[BudgetId]
,a.[PoId] = b.[PoId]
,a.[PrId] = b.[PrId]
,a.[ApplyUserBuToDeptName] = b.[ApplyUserBuToDeptName]
,a.[PoApplicationCode] = b.[PoApplicationCode]
,a.[PrApplicationCode] = b.[PrApplicationCode]
,a.[PaymentExcludingTaxAmount] = b.[PaymentExcludingTaxAmount]
,a.[CompanyCode] = b.[CompanyCode]
,a.[PayMethod] = b.[PayMethod]
,a.[CompanyId] = b.[CompanyId]
,a.[CompanyName] = b.[CompanyName]
,a.[VendorCode] = b.[VendorCode]
,a.[ReceivedTime] = b.[ReceivedTime]
,a.[Currency] = b.[Currency]
,a.[CurrencySymbol] = b.[CurrencySymbol]
,a.[ExchangeRate] = b.[ExchangeRate]
,a.[ExpectedFloatRate] = b.[ExpectedFloatRate]
,a.[PlanRate] = b.[PlanRate]
,a.[IsZeroSpeaker] = b.[IsZeroSpeaker]
,a.[BpcsRate] = b.[BpcsRate]
,a.[TransfereeId] = b.[TransfereeId]
,a.[TransfereeName] = b.[TransfereeName]
FROM dbo.PurGRApplications a
left join #PurGRApplications  b
ON a.id=b.id;


INSERT INTO dbo.PurGRApplications
SELECT
 [Id]
,[ApplicationCode]
,[Status]
,[ApplyUserId]
,[ApplyTime]
,[ApplyUserBu]
,[EsignPdf]
,[DeliveryMode]
,[MeetingModifyRemark]
,[AmountModifyRemark]
,[ProcurementPersonnelId]
,[VendorId]
,[IsAdvancePayment]
,[AdditionalSignerId]
,[Remarks]
,[PSAIds]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[ApplyUserBuName]
,[ApplyUserName]
,[VendorName]
,[BudgetCode]
,[BudgetId]
,[PoId]
,[PrId]
,[ApplyUserBuToDeptName]
,[PoApplicationCode]
,[PrApplicationCode]
,[PaymentExcludingTaxAmount]
,[CompanyCode]
,[PayMethod]
,[CompanyId]
,[CompanyName]
,[VendorCode]
,[ReceivedTime]
,[Currency]
,[CurrencySymbol]
,[ExchangeRate]
,[ExpectedFloatRate]
,[PlanRate]
,[IsZeroSpeaker]
,[BpcsRate]
,[TransfereeId]
,[TransfereeName]
FROM #PurGRApplications a
WHERE not exists (select * from dbo.PurGRApplications where id=a.id);

--truncate table dbo.PurGRApplications

--alter table Speaker_Portal_STG.dbo.PurGRApplications alter column [ApplicationCode] [nvarchar](50) NULL
--alter table Speaker_Portal_STG.dbo.PurGRApplications alter column [ApplyUserBuName] [nvarchar](500) NULL
--alter table Speaker_Portal_STG.dbo.PurGRApplications alter column [BudgetCode] [nvarchar](50) NULL
--alter table Speaker_Portal_STG.dbo.PurGRApplications alter column [PrApplicationCode] [nvarchar](500) NULL
--alter table Speaker_Portal_STG.dbo.PurGRApplications alter column [CompanyCode] [nvarchar](max) NULL