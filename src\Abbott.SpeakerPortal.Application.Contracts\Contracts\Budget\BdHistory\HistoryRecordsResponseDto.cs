﻿using Abbott.SpeakerPortal.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Abbott.SpeakerPortal.Contracts.Budget
{
    public class HistoryRecordsResponseDto
    {
        public Guid Id { get; set; }
        ///// <summary>
        ///// 主预算,子预算ID
        ///// </summary>
        ////public Guid BudgetId { get; set; }
        ///// <summary>
        ///// 预算类型
        ///// </summary>
        ////public BudgetType BudgetType { get; set; }
        /// <summary>
        /// 操作人ID
        /// </summary>
        public Guid OperatorId { get; set; }
        /// <summary>
        /// 操作人
        /// </summary>
        public string OperatorName { get; set; }
        /// <summary>
        /// 操作时间
        /// </summary>
        public DateTime OperatingTime { get; set; }
        /// <summary>
        /// 操作状态
        /// </summary>
        public OperateType OperateType { get; set; }
        /// <summary>
        /// 操作金额
        /// </summary>
        public decimal? OperateAmount { get; set; }
        /// <summary>
        /// 操作内容
        /// </summary>
        public string OperateContent { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
    }
}
