create proc sp_PurBWApplications_ns
as
begin
	--首次全部insert
	IF OBJECT_ID(N'PurBWApplications', N'U') IS NULL
	begin
		select 
			NEWID() Id,[ApplicationCode],[ApplyDeptId],[ApplyDeptName],[ApplyTime],[ApplyUserId],[ApplyUserName],[AttachmentIds],[CompanyCode],[CompanyId],[CompanyName],[ConcurrencyStamp],[CreationTime],[CreatorId],[DeleterId],[DeletionTime],[DivisionId],[DivisionName],[EstDeliveryDate],[ExemptType],[ExtraProperties],[GoodsServicesRequested],[IsDeleted],[JustificationTypeId],[JustificationTypeText],[LastModificationTime],[LastModifierId],[PRApplicationCode],[PRDetailId],[PRId],[ProjectDescription],[ProjectName],[PurchaserId],[RceNo],[Remark],[RequisitionAmount],[Status],[VendorCode],[VendorId],[VendorName],[WaiveReasonId],[WaiveReasonText],[WaiveRequest]
		into PurBWApplications
		from PurBWApplications_tmp;
		PRINT(N'首次新增完成');
	end
	else--否则upsert
	begin
		--update存量
		update t set 
			t.[ApplyDeptId]=tmp.ApplyDeptId,
			t.[ApplyDeptName]=tmp.ApplyDeptName,
			t.[ApplyTime]=tmp.ApplyTime,
			t.[ApplyUserId]=tmp.[ApplyUserId],
			t.[ApplyUserName]=tmp.[ApplyUserName],
			t.[AttachmentIds]=tmp.[AttachmentIds],
			t.[CompanyCode]=tmp.[CompanyCode],
			t.[CompanyId]=tmp.[CompanyId],
			t.[CompanyName]=tmp.[CompanyName],
			t.[ConcurrencyStamp]=tmp.[ConcurrencyStamp],
			t.[CreationTime]=tmp.[CreationTime],
			t.[CreatorId]=tmp.[CreatorId],
			t.[DeleterId]=tmp.[DeleterId],
			t.[DeletionTime]=tmp.[DeletionTime],
			t.[DivisionId]=tmp.[DivisionId],
			t.[DivisionName]=tmp.[DivisionName],
			t.[EstDeliveryDate]=tmp.[EstDeliveryDate],
			t.[ExemptType]=tmp.[ExemptType],
			t.[ExtraProperties]=tmp.[ExtraProperties],
			t.[GoodsServicesRequested]=tmp.[GoodsServicesRequested],
			t.[IsDeleted]=tmp.[IsDeleted],
			t.[JustificationTypeId]=tmp.[JustificationTypeId],
			t.[JustificationTypeText]=tmp.[JustificationTypeText],
			t.[LastModificationTime]=tmp.[LastModificationTime],
			t.[LastModifierId]=tmp.[LastModifierId],
			t.[PRApplicationCode]=tmp.[PRApplicationCode],
			t.[PRDetailId]=tmp.[PRDetailId],
			t.[PRId]=tmp.[PRId],
			t.[ProjectDescription]=tmp.[ProjectDescription],
			t.[ProjectName]=tmp.[ProjectName],
			t.[PurchaserId]=tmp.[PurchaserId],
			t.[RceNo]=tmp.[RceNo],
			t.[Remark]=tmp.[Remark],
			t.[RequisitionAmount]=tmp.[RequisitionAmount],
			t.[Status]=tmp.[Status],
			t.[VendorCode]=tmp.[VendorCode],
			t.[VendorId]=tmp.[VendorId],
			t.[VendorName]=tmp.[VendorName],
			t.[WaiveReasonId]=tmp.[WaiveReasonId],
			t.[WaiveReasonText]=tmp.[WaiveReasonText],
			t.[WaiveRequest]=tmp.[WaiveRequest]
		from PurBWApplications t join PurBWApplications_tmp tmp on t.ApplicationCode=tmp.ApplicationCode;
		PRINT(N'修改存量数据完成');

		--insert增量
		insert PurBWApplications select NEWID() Id,[ApplicationCode],[ApplyDeptId],[ApplyDeptName],[ApplyTime],[ApplyUserId],[ApplyUserName],[AttachmentIds],[CompanyCode],[CompanyId],[CompanyName],[ConcurrencyStamp],[CreationTime],[CreatorId],[DeleterId],[DeletionTime],[DivisionId],[DivisionName],[EstDeliveryDate],[ExemptType],[ExtraProperties],[GoodsServicesRequested],[IsDeleted],[JustificationTypeId],[JustificationTypeText],[LastModificationTime],[LastModifierId],[PRApplicationCode],[PRDetailId],[PRId],[ProjectDescription],[ProjectName],[PurchaserId],[RceNo],[Remark],[RequisitionAmount],[Status],[VendorCode],[VendorId],[VendorName],[WaiveReasonId],[WaiveReasonText],[WaiveRequest]
		from PurBWApplications_tmp tmp where not exists(select * from PurBWApplications where ApplicationCode=tmp.ApplicationCode);
		PRINT(N'新增增量数据完成');
	end
end