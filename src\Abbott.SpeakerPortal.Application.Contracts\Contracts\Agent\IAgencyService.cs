﻿using Abbott.SpeakerPortal.Contracts.Agent.Transferee;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.PersonCenter;

using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.Contracts.Agent
{
    public interface IAgencyService
    {
        /// <summary>
        /// 新增代理配置
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        Task<MessageResult> CreateAgentConfigAsync(CreateAgentConfigRequestDto request);

        /// <summary>
        /// 获取代理配置列表
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        Task<MessageResult> GetAgentConfigListAsync(GetAgentConfigListRequestDto request);

        /// <summary>
        /// 设置代理配置项的状态（启用或禁用）
        /// </summary>
        /// <param name="agentId">The agent identifier.</param>
        /// <param name="status">if set to <c>true</c> [status].</param>
        /// <returns></returns>
        Task<MessageResult> SetAgentStatusAsync(Guid agentId, bool status);

        /// <summary>
        /// 获取代理历史记录
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        Task<MessageResult> GetAgencyHistoryListAsync(GetAgencyHistoryListRequestDto request);

        /// <summary>
        /// 根据代理操作人获取原操作人
        /// 默认情况下，代理操作人即当前登录用户
        /// </summary>
        /// <param name="request">代理操作人Id</param>
        /// <returns></returns>
        Task<IEnumerable<KeyValuePair<Guid, string>>> GetOriginalOperators(GetAgentOperatorsRequestDto request);

        /// <summary>
        /// 根据代理操作人获取原操作人(含代理人)
        /// 默认情况下，代理操作人即当前登录用户
        /// </summary>
        /// <param name="request">代理操作人Id</param>
        /// <returns>当前代理人的所有目前有效的原操作人(包含自己)，任务中心我发起的所有流程数量统计</returns>
        Task<IEnumerable<(Guid userId, ResignationTransfer.TaskFormCategory? category)>> GetOriginalOperatorsForTaskCenterPendingCount(GetAgentOperatorsRequestDto request);

        /// <summary>
        /// 根据原操作人获取代理人
        /// </summary>
        /// <param name="request">原操作人Id和业务单据类型</param>
        /// <returns></returns>
        Task<KeyValuePair<Guid, string>?> GetAgentByOriginalOperator(GetAgentByOriginalOperatorRequestDto request = null);


        #region 离职转办
        /// <summary>
        /// 获取离职的申请人
        /// </summary>
        /// <returns></returns>
        Task<MessageResult> GetDepartingApplicant(string name);

        /// <summary>
        /// 获取转办人
        /// </summary>
        /// <returns></returns>
        Task<MessageResult> GetTransfereeUsers();

        /// <summary>
        /// 获取待转办的申请单据
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        Task<MessageResult> GetApplyForms4Transferee(GetApplyFormsRequestDto request);

        /// <summary>
        /// 设置申请单据的转办人
        /// </summary>
        /// <param name="request">The requet.</param>
        /// <returns></returns>
        Task<MessageResult> SetTransfereeUser4ApplyForms(SetTransfereeUserRequestDto request);

        /// <summary>
        /// 获取待转办的审批任务
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        Task<MessageResult> GetWorkflowTask4Transferee(GetWorkflowTaskRequestDto request);

        /// <summary>
        /// 设置审批任务的转办人
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        Task<MessageResult> SetTransferreUser4WorkflowTask(SetTransfereeUserRequestDto request);

        #endregion
    }
}
