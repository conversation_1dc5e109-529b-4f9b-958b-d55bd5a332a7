﻿using System;
using System.Text.Json.Serialization;
using MiniExcelLibs.Attributes;

namespace Abbott.SpeakerPortal.Contracts.Budget.FocMasterBudget
{
    public class GetFocBudgetListResponseDto
    {
        /// <summary>
        /// 预算Id
        /// </summary>
        [ExcelIgnore]
        public Guid Id { get; set; }

        /// <summary>
        /// 预算编号
        /// </summary>
        [ExcelColumnName("预算编号")]
        public string Code { get; set; }

        /// <summary>
        /// bu
        /// </summary>
        [ExcelIgnore]
        public Guid BuId { get; set; }

        /// <summary>
        /// BU名称
        /// </summary>
        [ExcelColumnName("Bu")]
        public string BuName { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        [ExcelColumnName("描述")]
        public string Description { get; set; }

        /// <summary>
        /// 负责人Id
        /// </summary>
        [ExcelIgnore]
        public Guid OwnerId { get; set; }

        /// <summary>
        /// 负责人姓名
        /// </summary>
        [ExcelColumnName("负责人")]
        public string OwnerName { get; set; }

        /// <summary>
        /// 负责人邮箱
        /// </summary>
        [ExcelColumnName("负责人邮箱")]
        public string OwnerEmail { get; set; }

        /// <summary>
        /// 主预算数量
        /// </summary>
        [ExcelColumnName("主预算数量")]
        public int BudgetQty { get; set; }

        /// <summary>
        /// 子预算数量
        /// </summary>

        [ExcelColumnName("子预算数量")]
        public int SubBudgetQty { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [ExcelIgnore]
        public bool Status { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        [ExcelColumnName("状态")]
        [JsonIgnore]
        public string StatusText => Status ? "启用" : "冻结";

        /// <summary>
        /// 年度
        /// </summary>
        [ExcelIgnore]
        public int? Year { get; set; }

        /// <summary>
        /// 是否可以删除
        /// </summary>
        [ExcelIgnore]
        public bool isDelete { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [ExcelIgnore]
        public string Remark { get; set; }
    }

    /// <summary>
    /// 导出主预算
    /// </summary>
    public class ExportFocBudgetListDto
    {
        /// <summary>
        /// 预算编号
        /// </summary>
        [ExcelColumnName("预算编号")]
        public string Code { get; set; }

        /// <summary>
        /// BU名称
        /// </summary>
        [ExcelColumnName("Bu")]
        public string BuName { get; set; }
        /// <summary>
        /// 描述
        /// </summary>
        [ExcelColumnName("描述")]
        public string Description { get; set; }

        /// <summary>
        /// 负责人姓名
        /// </summary>
        [ExcelColumnName("负责人")]
        public string OwnerName { get; set; }
        /// <summary>
        /// 负责人邮箱
        /// </summary>
        [ExcelColumnName("负责人邮箱")]
        public string OwnerEmail { get; set; }
        /// <summary>
        /// 主预算数量
        /// </summary>
        [ExcelColumnName("主预算数量")]
        public int BudgetQty { get; set; }
        /// <summary>
        /// 子预算数量
        /// </summary>

        [ExcelColumnName("子预算数量")]
        public int SubBudgetQty { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        [ExcelIgnore]
        public bool Status { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        [ExcelColumnName("状态")]
        [JsonIgnore]
        public string StatusText => Status ? "启用" : "冻结";
    }
}
