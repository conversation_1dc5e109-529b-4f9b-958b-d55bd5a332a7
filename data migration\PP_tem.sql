--配置数据-特殊财务审批条件-条件适用的BU 先不迁移
--select * from (
--select 
--value as spk_organizationalmasterdataid,
--sct.spk_name  as spk_costnatureid
--from PLATFORM_ABBOTT_dev.dbo.ODS_T_RESOURCE_EXTEND trs
--join PLATFORM_ABBOTT_dev.dbo.spk_costnature_Tmp sct 
--on trs.Res_Code=sct.spk_BPMCode
--cross apply STRING_SPLIT(trs.Data02,',')
--) A
--where spk_organizationalmasterdataid != '' 

--活动类型
SELECT 
Res_Code as [spk_BPMCode],
Res_Name as [spk_Name],
Res_Data as [spk_costcentercode],
case when IsEnable=0 then N'失效' else N'有效' end flg
into spk_activitytype_tmp
from ODS_T_RESOURCE
where Res_Parent_Code='ed06e42558b54262b113df8838299025' --and IsEnable=1 

--字典主数据
SELECT * INTO spk_dictionary_Tmp from (
--字典主数据
select 
spk_BPMCode,
N'APS-' + RIGHT('000' + CAST(b AS NVARCHAR(3)), 3) as spk_code,
spk_Name,
spk_type,
spk_parentcode,
flg
 from (
SELECT 
Res_Code AS  spk_BPMCode,
'' AS spk_code,
Res_Name AS spk_Name,
N'APS属性' AS spk_type,
N'ApsPorperty' AS spk_parentcode,
case when IsEnable=0 then N'失效' else N'有效' end flg,
ROW_NUMBER() over(order by Res_Name desc) b
from ODS_T_RESOURCE
WHERE Res_Parent_Code='d47fa11fe0bb4d6a83bcb56e4b8d7123' ) A
UNION ALL
select 
spk_BPMCode,
spk_Name as spk_code,
spk_Name,
spk_type,
spk_parentcode,
flg
 from (
 SELECT 
Res_Code AS  spk_BPMCode,
'' AS spk_code,
Res_Name AS spk_Name,
N'HCP级别' AS spk_type,
N'HCPLevel' AS spk_parentcode,
case when IsEnable=0 then N'失效' else N'有效' end flg,
ROW_NUMBER() over(order by Res_Name desc) b
from ODS_T_RESOURCE
where Res_Parent_Code='902c2ebf5a5643c6b62a4ad3dddd8b59'
and Res_Name in ('T1','T2','T3','T4','T5'))A
UNION ALL
select 
spk_BPMCode,
N'DPO-' + RIGHT('000' + CAST(b AS NVARCHAR(3)), 3) as spk_code,
spk_Name,
spk_type,
spk_parentcode,
flg
 from(
 SELECT 
Res_Code AS  spk_BPMCode,
'' AS spk_code,
Res_Name AS spk_Name,
N'DPO分类' AS spk_type,
N'DPOCategory' AS spk_parentcode,
case when IsEnable=0 then N'失效' else N'有效' end flg,
ROW_NUMBER() over(order by Res_Name desc) b
from ODS_T_RESOURCE
where Res_Parent_Code='0fa00f84cfe147d697323bb6995b568d')A
UNION ALL
-- select 
-- spk_BPMCode,
-- N'Spending-' + RIGHT('000' + CAST(b AS NVARCHAR(3)), 3) as spk_code,
-- spk_Name,
-- spk_type,
-- spk_parentcode,
-- flg
--  from( 
-- SELECT 
-- Res_Code AS  spk_BPMCode,
-- '' AS spk_code,
-- Res_Name AS spk_Name,
-- N'Spending分类' AS spk_type,
-- N'SpendingCategory' AS spk_parentcode,
-- case when IsEnable=0 then N'失效' else N'有效' end flg,
-- ROW_NUMBER() over(order by Res_Name desc) b
-- from ODS_T_RESOURCE
-- where Res_Parent_Code='b58c3d39bd4240cbb86974d1fdd9f9fc' )A
-- UNION ALL
select 
spk_BPMCode,
N'RCAT-' + RIGHT('00' + CAST(b AS NVARCHAR(2)), 2) as spk_code,
spk_Name,
spk_type,
spk_parentcode,
flg
 from( 
 SELECT 
Res_Code AS  spk_BPMCode,
'' AS spk_code,
Res_Name AS spk_Name,
N'登记证书机构类型' AS spk_type,
N'RegisteredCertificateAuthorityType' AS spk_parentcode,
case when IsEnable=0 then N'失效' else N'有效' end flg,
ROW_NUMBER() over(order by Res_Name desc) b
from ODS_T_RESOURCE
where Res_Parent_Code='169fc211887445a2a450e847ce5cbeb9')A
UNION ALL
SELECT 
Res_Code AS  spk_BPMCode,
Res_Name AS spk_code,
Res_Name AS spk_Name,
N'付款方式' AS spk_type,
N'PayMethod' AS spk_parentcode,
case when IsEnable=0 then N'失效' else N'有效' end flg
from ODS_T_RESOURCE
where Res_Parent_Code='332e391ee4a24ae1a109b6d2168a88d5' 
and Res_Name in ('AP','AR','ER')
UNION ALL
SELECT 
Res_Code AS  spk_BPMCode,
case when res_name='A类-内部EPD材料'then 'Category-A'
when res_name='C类-外部讲者材料（待审核）'then 'Category-C'
when res_name='B类-外部讲者材料（已审核）'then 'Category-B'
else  'NotApplicable' end AS spk_code,
Res_Name AS spk_Name,
N'幻灯片类型' AS spk_type,
N'SlideType' AS spk_parentcode,
case when IsEnable=0 then N'失效' else N'有效' end flg
from ODS_T_RESOURCE
where Res_Parent_Code='6f393ec968d845b1bffd3c63363e40c3' 
UNION ALL
select 
spk_BPMCode,
'SlideName' + RIGHT('0000' + CAST(b AS NVARCHAR(4)), 4) as spk_code,
spk_Name,
spk_type,
spk_parentcode,
flg
 from( 
 SELECT 
Res_Code AS  spk_BPMCode,
'' AS spk_code,
Res_Name AS spk_Name,
N'幻灯片名称' AS spk_type,
N'SlideName' AS spk_parentcode,
case when IsEnable=0 then N'失效' else N'有效' end flg,
ROW_NUMBER() over(order by Res_Name desc) b
from ODS_T_RESOURCE
where Res_Parent_Code='e876da0252184d998741a5e321c99314' )A
UNION ALL
SELECT 
Res_Code AS  spk_BPMCode,
case when res_name='线下'then 'Offline'
when res_name='线上'then 'Offline'
else 'OnlineAndOffline'
end AS spk_code,
Res_Name AS spk_Name,
N'会议类型' AS spk_type,
N'MeetingType' AS spk_parentcode,
case when IsEnable=0 then N'失效' else N'有效' end flg
from ODS_T_RESOURCE
where Res_Parent_Code='f6390426fc5f4059a747bb9522689d6f' 
UNION ALL
select 
spk_BPMCode,
N'Active' + RIGHT('000' + CAST(b AS NVARCHAR(3)), 3) as spk_code,
spk_Name,
spk_type,
spk_parentcode,
flg
 from(  
SELECT 
Res_Code AS  spk_BPMCode,
'' AS spk_code,
Res_Name AS spk_Name,
N'活动类型' AS spk_type,
N'ActiveType' AS spk_parentcode,
case when IsEnable=0 then N'失效' else N'有效' end flg,
ROW_NUMBER() over(order by Res_Name desc) b
from ODS_T_RESOURCE
where Res_Parent_Code='f827b72cb108413280a636b706db409c' )A
UNION ALL
select 
spk_BPMCode,
N'ProjectType' + RIGHT('000' + CAST(b AS NVARCHAR(3)), 3) as spk_code,
spk_Name,
spk_type,
spk_parentcode,
flg
 from( 
 SELECT 
Res_Code AS  spk_BPMCode,
'' AS spk_code,
Res_Name AS spk_Name,
N'项目类型' AS spk_type,
N'ProjectType' AS spk_parentcode,
case when IsEnable=0 then N'失效' else N'有效' end flg,
ROW_NUMBER() over(order by Res_Name desc) b
from ODS_T_RESOURCE
where Res_Parent_Code='a7c1c22d420a43e58d5cd26481cd4f27' )A
UNION ALL
SELECT 
Res_Code AS  spk_BPMCode,
Res_Name AS spk_code,
Res_Name AS spk_Name,
N'送货地址' AS spk_type,
N'DeliveryAddress' AS spk_parentcode,
case when IsEnable=0 then N'失效' else N'有效' end flg
from ODS_T_RESOURCE
where Res_Parent_Code='047130de98de4f7b971f59a2e3b34913' 
UNION ALL
SELECT 
Res_Code AS  spk_BPMCode,
Res_Data AS spk_code,
Res_Name AS spk_Name,
N'供应商打分标准' AS spk_type,
N'RatingStandard' AS spk_parentcode,
case when IsEnable=0 then N'失效' else N'有效' end flg
from ODS_T_RESOURCE
where Res_Parent_Code='2cf0042fe5644bcdb5502d94a4165895' 
--UNION ALL
--SELECT 
--Res_Code AS  spk_BPMCode,
--REPLACE(REPLACE(SUBSTRING(Res_Name, 1, PATINDEX(N'%[^a-zA-Z%, ]%', Res_Name) - 1), ' ', ''), ',', '') AS spk_code, 
--Res_Name AS spk_Name,
--case when Res_Name=N'Other 其他' then  N'其他紧急' else N'紧急' end AS spk_type,
--REPLACE(REPLACE(SUBSTRING(Res_Name, 1, PATINDEX(N'%[^a-zA-Z%, ]%', Res_Name) - 1), ' ', ''), ',', '') as spk_parentcode
--from ODS_T_RESOURCE
--where Res_Parent_Code='4e94feede80f44a1ae5aa7ef80eaf2d0' and IsEnable=1 
UNION ALL
select spk_BPMCode,
N'SerialMeetingType' + RIGHT('00' + CAST(b AS NVARCHAR(2)), 2)as spk_code,
spk_Name,
spk_type,
spk_parentcode,
flg
 from (
SELECT 
Res_Code AS  spk_BPMCode,
'' as spk_code,
Res_Name AS spk_Name,
N'系列会类型' AS spk_type,
N'SerialMeetingType' AS spk_parentcode,
case when IsEnable=0 then N'失效' else N'有效' end flg,
ROW_NUMBER() over(order by Res_Name desc) b
from ODS_T_RESOURCE
where Res_Parent_Code='98b5cb673d2a444b803d7901a583cfe7' 
)A
)B

--配置数据-BU编码配置(及特殊推送编码)
SELECT * INTO spk_bucodingconfiguration_Tmp from (select * from (
select 
Res_Code as spk_buname,
b.Data12 as spk_code,
iif(b.Data12 in ('61','62'),'60',b.Data12)as spk_pushspecialcodes,
case when IsEnable=0 then N'失效' else N'有效' end flg
from ODS_T_RESOURCE a
join ODS_T_ORGANIZATION_EXTEND b on a.Res_Code=b.Org_Code
where res_data = 'a6fb7322c66244c58698bb009342369b')a
)B

--配置数据-城市主数据
SELECT * INTO spk_citymasterdata_Tmp from (
--配置数据-城市主数据
select 
max(Res_Code) as spk_BPMCode,
Res_Name as spk_Name,
Res_Data as spk_citynumber,
case when IsEnable=0 then N'失效' else N'有效' end flg
from ODS_T_RESOURCE tr
where Res_Parent_Code='da4e31c2037649808667fc084bbabb1f' 
group by Res_Name,Res_Data,IsEnable )B

--组织主数据
select
b.Child_Org_Code as [spk_BPMCode],
c.Res_Name as [spk_Name],
c.Res_Name as [spk_chineseName],
c.Res_Name as [spk_englishName],
d_Org_Struct_Type.[Res_Name] as [spk_organizationType],
a.Data02 as [spk_epolevel],
a.Data04 as [spk_epoleader],
b.[Parent_Org_Code] as [spk_parentorganization],
a.Data09 as [spk_costcenter],
c.Res_Data1 as [spk_orgCode],
c.Res_CodeData2 as [spk_orgParentCode],
case when c.IsEnable=0 then N'失效' else N'有效' end flg
into spk_organizationalmasterdata_Tmp 
from ODS_T_ORGANIZATION_STRUCTURE b
left join   ODS_T_ORGANIZATION_EXTEND a on a.Org_Code=b.Child_Org_Code
left join ODS_T_RESOURCE c on c.Res_Code=b.Child_Org_Code
left join ODS_T_RESOURCE d_Org_Struct_Type on d_Org_Struct_Type.Res_Code  =c.Res_Data


--人员主数据
SELECT * INTO spk_staffmasterdata_Tmp from (
select 
TEMP.Emp_Id  as bpm_id,
TEMP.[Emp_Code] as [spk_staffNumber],
RES1.Res_Name as spk_districtmasterdata,
RES2.Res_Name as spk_gender,
TEMP.Emp_Name as spk_name,
TEMP.Emp_AD_Account as spk_staffaccount,
TEMP.Emp_AD_Mail_Address as spk_staffemail,
Iif(TEMP.Emp_Language='CN',N'中文',N'英文') as spk_stafflanguage,
RES3.Res_Name as spk_staffstate,
TEMP.Emp_TelPhone as spk_staffphonenumber,
TEMP.[Emp_Remark] as [spk_staffRemarks]
from ODS_T_EMPLOYEE as  TEMP
left join ODS_T_EMPLOYEE_EXTEND as TEE 
on TEE.Emp_Id=TEMP.Emp_Id
left join ODS_T_RESOURCE RES1
ON TEE.Data02=RES1.RES_CODE
left join ODS_T_RESOURCE RES2
ON TEMP.Emp_Gender=RES2.RES_CODE
left join ODS_T_RESOURCE RES3
ON TEMP.Emp_Status=RES3.RES_CODE ) B

--配置数据-产品主数据
SELECT * INTO spk_productmasterdata_Tmp from (--配置数据-产品主数据
select 
Res_Code as spk_BPMCode,
Res_Name as spk_name,
Res_Name as spk_chinesevalue,
Res_Name as spk_englishvalue,
Res_CodeData2 as spk_productcode,
Res_CodeData3 as spk_limitexpensecode,
case when IsEnable=0 then N'失效' else N'有效' end flg
from ODS_T_RESOURCE tr
where Res_Parent_Code='0fdd24db428743148e6a11be45c77161')B

--配置数据-产品主数据与BU映射关系
SELECT * INTO spk_organizational_product_Tmp from (--配置数据-产品主数据与BU映射关系
SELECT 
Res_code as spk_product,
value as spk_bu,
case when IsEnable=0 then N'失效' else N'有效' end flg
from ODS_T_RESOURCE tre 
cross apply STRING_SPLIT(tre.Res_Data1,',')
where Res_Parent_Code='0fdd24db428743148e6a11be45c77161')B

--配置数据-成本中心主数据与BU映射关系    
SELECT * INTO spk_organizational_costcenter_Tmp from (
select 
Res_Code as spk_costcenter,
value as spk_organizational,
'' as spk_name,
case when IsEnable=0 then N'失效' else N'有效' end flg
 from ODS_T_RESOURCE 
 cross apply STRING_SPLIT(Res_CodeData2,',')
where Res_Parent_Code='bdcecc445f614d79aad2d957ab1a0cb5' 
)B

--角色关系
select 
Emp_Id as [UserId],
Role_Code as [RoleId],
'' TenantId
into AbpUserRoles_tmp
from ods_T_EMPLOYEE_ROLE dter 

--配置数据-消费大类
SELECT * INTO spk_consume_Tmp from (--消费大类维护

SELECT  
tr.Res_Code as spk_BPMCode,
max(case when Res_LangCode='CN' then trd.Res_Content else '' end) as spk_name,
max(case when Res_LangCode='EN' then trd.Res_Content else '' end) as spk_englishname,
'' as spk_number,
max(IIF(tr.Res_CodeData3 IS NULL OR tr.Res_CodeData3 ='','0',tr.Res_CodeData3))as spk_isspecial,
max(IIF(tr.Res_CodeData2 IS NULL OR tr.Res_CodeData2='','0',tr.Res_CodeData2)) as spk_isvendortype,
max(IIF(tr.Res_Data1 IS NULL OR tr.Res_Data1='','0',tr.Res_Data1)) as spk_isassets,
'' as spk_isconferencefees,
'' as spk_isdspot,
'' as spk_precode,
max(A.Res_Data) as spk_organizational,
case when tr.IsEnable=0 then N'失效' else N'有效' end flg
from ODS_T_RESOURCE tr
LEFT JOIN (SELECT  
Res_Data,RES_CODE
from ODS_T_RESOURCE tr
where Res_Parent_Code= 'd777d7d29bc14cbda83c0d10bfc59534')A 
ON a.RES_CODE=TR.Res_Parent_Code 
left join ODS_T_RESOURCE_DESC trd
on tr.res_code=trd.res_code
where Res_Parent_Code= '97e42885f68d487c819d64b63b61866c' 
group by tr.Res_Code,tr.IsEnable


union all

SELECT  
tr.Res_Code as spk_BPMCode,
max(case when Res_LangCode='CN' then trd.Res_Content else '' end) as spk_name,
max(case when Res_LangCode='EN' then trd.Res_Content else '' end) as spk_englishname,
'' as spk_number,
max(IIF(tr.Res_CodeData3 IS NULL OR tr.Res_CodeData3 ='','0',tr.Res_CodeData3))as spk_isspecial,
max(IIF(tr.Res_CodeData2 IS NULL OR tr.Res_CodeData2='','0',tr.Res_CodeData2)) as spk_isvendortype,
max(IIF(tr.Res_Data1 IS NULL OR tr.Res_Data1='','0',tr.Res_Data1)) as spk_isassets,
'' as spk_isconferencefees,
'' as spk_isdspot,
'' as spk_precode,
max(A.Res_Data) as spk_organizational,
case when tr.IsEnable=0 then N'失效' else N'有效' end flg
from ODS_T_RESOURCE tr
LEFT JOIN (SELECT  
Res_Data,RES_CODE
from ODS_T_RESOURCE tr
where Res_Parent_Code= 'd777d7d29bc14cbda83c0d10bfc59534' )A 
ON a.RES_CODE=TR.Res_Parent_Code 
left join ODS_T_RESOURCE_DESC trd
on tr.res_code=trd.res_code
where Res_Parent_Code= '574a180851f1453ba16a68d557fd2135'
group by tr.Res_Code,tr.IsEnable

union all

SELECT  
tr.Res_Code as spk_BPMCode,
max(case when Res_LangCode='CN' then trd.Res_Content else '' end) as spk_name,
max(case when Res_LangCode='EN' then trd.Res_Content else '' end) as spk_englishname,
'' as spk_number,
max(IIF(tr.Res_CodeData3 IS NULL OR tr.Res_CodeData3 ='','0',tr.Res_CodeData3))as spk_isspecial,
max(IIF(tr.Res_CodeData2 IS NULL OR tr.Res_CodeData2='','0',tr.Res_CodeData2)) as spk_isvendortype,
max(IIF(tr.Res_Data1 IS NULL OR tr.Res_Data1='','0',tr.Res_Data1)) as spk_isassets,
'' as spk_isconferencefees,
'' as spk_isdspot,
'' as spk_precode,
max(A.Res_Data) as spk_organizational,
case when tr.IsEnable=0 then N'失效' else N'有效' end flg
from ODS_T_RESOURCE tr
LEFT JOIN (SELECT  
Res_Data,RES_CODE
from ODS_T_RESOURCE tr
where Res_Parent_Code= 'd777d7d29bc14cbda83c0d10bfc59534' )A 
ON a.RES_CODE=TR.Res_Parent_Code 
left join ODS_T_RESOURCE_DESC trd
on tr.res_code=trd.res_code
where Res_Parent_Code= '486f35c639c748949d81681d7c0246ae' 
group by tr.Res_Code,tr.IsEnable )B

--配置数据-Expense审批矩阵
SELECT * INTO spk_expenseapprovalmatrixconfigurationitem_Tmp from (--Expense审批矩阵
select 
Buid as  spk_bu,
int as spk_approvalnumber,
SUM(CASE WHEN ORGlEVEL = 0 THEN Amount ELSE 0 END)/a.Res_CodeData3 AS spk_level0,
SUM(CASE WHEN ORGlEVEL = 1 THEN Amount ELSE 0 END)/a.Res_CodeData3 AS spk_level1,
SUM(CASE WHEN ORGlEVEL = 2 THEN Amount ELSE 0 END)/a.Res_CodeData3 AS spk_level2,
SUM(CASE WHEN ORGlEVEL = 3 THEN Amount ELSE 0 END)/a.Res_CodeData3 AS spk_level3,
SUM(CASE WHEN ORGlEVEL = 4 THEN Amount ELSE 0 END)/a.Res_CodeData3 AS spk_level4,
SUM(CASE WHEN ORGlEVEL = 5 THEN Amount ELSE 0 END)/a.Res_CodeData3 AS spk_level5,
SUM(CASE WHEN ORGlEVEL = 6 THEN Amount ELSE 0 END)/a.Res_CodeData3 AS spk_level6
from ODS_T_Pur_ProcessConfig_ExpenseApproval_MainInfo TPPEM,(select Res_code,Res_CodeData3 from ODS_T_RESOURCE where res_code='bebe81b186274d65ab651e7a75f41b87') as A
--left join ODS_T_RESOURCE RES1
--ON TPPEM.BUId=RES1.RES_CODE
where int not in (7,9)
group by int,Buid,Res_CodeData3 )B


--配置数据-公司主数据
SELECT * INTO spk_companymasterdata_Tmp from (
select 
tr.Res_Code as spk_BPMCode,
tr.Res_Name as spk_Name,
tr.Res_Name as spk_chinesevalue,
tr.Res_Name as spk_englishvalue,
tre.Data04 as spk_fax,
tr.Res_Data1 as spk_CompanyCode,
tre.Data02 as spk_invoiceaddress,
tre.Data01 as spk_invoicetitle,
tre.Data11 as spk_openbank,
tre.Data03 as spk_phone,
'' as spk_abbrcode,
'' as spk_bankcity,
tre.Data12 as spk_bankaccount,
case when tr.IsEnable=0 then N'失效' else N'有效' end flg
from ODS_T_RESOURCE tr
left join  ODS_T_RESOURCE_EXTEND tre
on tr.Res_Code  =tre.Res_Code 
where Res_Parent_Code='61a3f911b5ae4bc98cddd441833d861e'
and tr.Res_Name in ('JV','TRADING','YYKJ','JX Plant') )B

--配置数据-特殊财务审批条件-小于5K美金必须财务审批/财务总监与GM绑定/财务与财务总监同一人时需跳过财务总监审批
SELECT * INTO spk_financialapprovalruleconfig_Tmp from (--配置数据-特殊财务审批条件-小于5K美金必须财务审批/财务总监与GM绑定/财务与财务总监同一人时需跳过财务总监审批
select 
Res_Code as spk_BPMCode,
Res_Name as spk_name,
Res_Name as spk_chinesevalue,
Res_Name as spk_englishvalue
from ODS_T_RESOURCE
where Res_Code in ('c13812c31e67405b9678d760f8d29de3','237a422c94e84bd6a1c4dcae6e6f04cf','bebe81b186274d65ab651e7a75f41b87')
 )B

--财务审批金额矩阵
SELECT * INTO spk_financialapprovalamountmatrix_Tmp from (--财务审批金额矩阵
select 
TPP.BUID as spk_bu,
Amount/a.Res_CodeData3  as spk_procurementfinance,
ApprovalLevel as spk_name
from ODS_T_Pur_PRFinancialApproval TPP
, (select Res_code,Res_CodeData3 from ODS_T_RESOURCE where res_code='bebe81b186274d65ab651e7a75f41b87') as A 
 )B

--配置数据-城市主数据与BU映射关系
SELECT * INTO spk_organizationalmasterdata_citymasterdata_Tmp from (
--配置数据-城市主数据与BU映射关系
select 
Location as spk_citymasterdataid,
BU as spk_organizationalmasterdataid,
'no' as spk_BU
from ODS_T_BU_Location
union all
select 
Res_Data1 as spk_citymasterdataid,
[Res_Data] as spk_organizationalmasterdataid,
'yes' as spk_BU
from ODS_T_RESOURCE otr 
where [Res_parent_code]='ee73f548dcdd498f9fd06c869fb0ed14'
)B

--配置数据-BU成本中心费用性质Mapping
SELECT * INTO spk_coamappingrule_Tmp from (select 
Division as spk_bucode,
CostCenter as spk_costcentercode,
ExpenseNature as spk_expensenaturecode
 from ODS_T_BPCS_Special_Mapping )B

select 
'' as [spk_code],
Emp_Id  as [spk_staffmasterdataid],
ORGCODE as [spk_organizationalmasterdataid]
into spk_organizational_staff_tmp
from ODS_T_EMPLOYEE_ORGANIZATION oteo 
where isEnable=1 

--岗位Mapping
--岗位主数据映射
select 
N'Active-' + CONVERT(NVARCHAR(8),GETDATE() , 112) +  '-'+RIGHT('0000' + CAST(b AS NVARCHAR(4)), 4) as [spk_Name],
[spk_staffname],
[spk_staffBPMCode],
[spk_organization],
[spk_orgBPMCode],
case when spk_position='5185c8eea4a24ac7a11568802d9d8561'  and spk_extentioncode2 = 'NHIV' then N'IFO DPS Check(讲者)' 
when spk_position='5185c8eea4a24ac7a11568802d9d8561'  and spk_extentioncode2 = 'NH' then N'IFO DPS Check审批岗(HCI机构)' 
when spk_position='5185c8eea4a24ac7a11568802d9d8561'  and spk_extentioncode2 = 'NLIV' then N'IFO DPS Check审批岗(非HCP个人)' 
when spk_position='df14bababbfe410389ae7a72bbd5d6f3'   then N'MKT director审批岗' 
when spk_position='af3a8ddafb3c41a3bc88ca9f5f23cc3c'   then N'MKTReview' 
when spk_position='5ecb14915c534a34846b726ec75251b1'   then N'Sales director审批岗' 
when spk_position='b62c6d1994be42d29b324b1ff05811f5'   then N'STP Header审批岗' 
when spk_position='5fcc88d136864fd9b4eed4f39baff2c5'   then N'STP Manager审批岗' 
when spk_position='e20d35d2e2974c6e8026889789fdb95e'   then N'比价审批人岗' 
when spk_position='40f21340ef574f31aa820ab6046c5c3f'   then N'财务供应商维护岗' 
when spk_position='5177851b620e45e0bb223dc9ab303014'   then N'财务供应商预审岗' 
when spk_position='7c559a293c56400dbd58c7b6cbacbc91'   then N'财务审批' 
when spk_position='02b417d7dd3640ebb9a72a8b09ce5331'   then N'财务总监岗' 
when spk_position='3371c8dc3f0d4b6782a9fb1d57e7abc4'   then N'采购供应商审批岗' 
when spk_position='9b7b4ee681044672a1468f46360ac458'   then N'合规审批岗' 
when spk_position='f23235507e014eaab914cdbd819a9c1b'   then N'主采购岗' 
when spk_position='02d30c961d434c109e9dacaa776afa6a'   then N'特殊采购审批岗' 
end as [spk_position],
case when spk_position='5185c8eea4a24ac7a11568802d9d8561'  and spk_extentioncode2 = 'NHIV' then N''
when spk_position='5185c8eea4a24ac7a11568802d9d8561'  and spk_extentioncode2 = 'NH' then N''
when spk_position='5185c8eea4a24ac7a11568802d9d8561'  and spk_extentioncode2 = 'NLIV' then N'' 
when spk_position='df14bababbfe410389ae7a72bbd5d6f3'   then N'' 
when spk_position='af3a8ddafb3c41a3bc88ca9f5f23cc3c'   then N''
when spk_position='5ecb14915c534a34846b726ec75251b1'   then N'' 
when spk_position='b62c6d1994be42d29b324b1ff05811f5'   then N''
when spk_position='5fcc88d136864fd9b4eed4f39baff2c5'   then N'' 
when spk_position='e20d35d2e2974c6e8026889789fdb95e'   then spk_extentioncode2
when spk_position='40f21340ef574f31aa820ab6046c5c3f'   then N'' 
when spk_position='5177851b620e45e0bb223dc9ab303014'   then N'' 
when spk_position='7c559a293c56400dbd58c7b6cbacbc91'   then spk_extentioncode2 
when spk_position='02b417d7dd3640ebb9a72a8b09ce5331'   then N''
when spk_position='3371c8dc3f0d4b6782a9fb1d57e7abc4'   then spk_extentioncode2
when spk_position='9b7b4ee681044672a1468f46360ac458'   then N'' 
when spk_position='f23235507e014eaab914cdbd819a9c1b'   then spk_extentioncode2
when spk_position='02d30c961d434c109e9dacaa776afa6a'   then N'' 
end[spk_extentioncode2],
[spk_extensioncodeId]
into spk_extensioncode_tmp
from (select 
'' as spk_Name,
Emp_ID  as [spk_staffname],
emp_id as [spk_staffBPMCode],
ORGCODE as [spk_organization],
ORGCODE as [spk_orgBPMCode],
jobcode as [spk_position],
TRIM(value) as [spk_extentioncode2],
'' as [spk_extensioncodeId],
ROW_NUMBER() over(order by Emp_ID desc) b,
case when jobcode='40f21340ef574f31aa820ab6046c5c3f' then ROW_NUMBER () over( partition by Emp_ID,ORGCODE order by Emp_ID,ORGCODE)
else 1 end as rn
from ODS_T_EMPLOYEE_JOB
CROSS APPLY STRING_SPLIT(DATA2, ',')) a 
where spk_position in (
'5185c8eea4a24ac7a11568802d9d8561'
,'df14bababbfe410389ae7a72bbd5d6f3'
,'af3a8ddafb3c41a3bc88ca9f5f23cc3c'
,'5ecb14915c534a34846b726ec75251b1'
,'b62c6d1994be42d29b324b1ff05811f5'
,'5fcc88d136864fd9b4eed4f39baff2c5'
,'e20d35d2e2974c6e8026889789fdb95e'
,'40f21340ef574f31aa820ab6046c5c3f'
,'5177851b620e45e0bb223dc9ab303014'
,'7c559a293c56400dbd58c7b6cbacbc91'
,'02b417d7dd3640ebb9a72a8b09ce5331'
,'3371c8dc3f0d4b6782a9fb1d57e7abc4'
,'9b7b4ee681044672a1468f46360ac458'
,'f23235507e014eaab914cdbd819a9c1b'
,'02d30c961d434c109e9dacaa776afa6a') and rn=1




--配置数据-费用性质维护
--费用性质维护
SELECT  
tr.Res_Code as spk_BPMCode,
max(case when Res_LangCode='CN' then trd.Res_Content else '' end) as spk_name,
max(case when Res_LangCode='EN' then trd.Res_Content else '' end) as spk_englishname,
max(tr.[Res_Data1])  as spk_costnumber,
max(tr.[Res_Parent_Code])as [spk_consume],
max(tr.[Res_Parent_Code])as  spk_consumeBPMCode,
max(tr.[Res_CodeData3]) as [spk_paymentmethod],
max(tr.[Res_CodeData2]) as [spk_approvalnumber],
max(tre.Data01) as spk_isBPCS,
'' as spk_pushonlinemeeting,
'' as spk_unit,
max(tre.Data03)  as spk_AR,
'' as spk_costnatureId
into spk_costnature_tmp
from ODS_T_RESOURCE tr
left join ODS_T_RESOURCE_DESC trd
on tr.res_code=trd.res_code
left join ODS_T_RESOURCE_EXTEND tre
on tr.res_code=tre.res_code
join spk_consume_Tmp sct 
on tr.Res_parent_code=sct.spk_BPMCode 
group by tr.Res_Code 

--配置数据-特殊财务审批条件-条件适用的BU
SELECT * INTO spk_financialapprovalruleandbu_Tmp from (--配置数据-特殊财务审批条件-条件适用的BU
select 
value as spk_bu,
'' as spk_name,
Res_Code as spk_rulename,
case when IsEnable=0 then N'失效' else N'有效' end flg
from ODS_T_RESOURCE
cross apply STRING_SPLIT(Res_Data,',')
where [Res_parent_code]='a09cacdf6a99451b89086c0368f024bf' 
union all
select 
Res_CodeData2 as spk_bu,
'' as spk_name,
Res_Parent_Code as spk_rulename,
case when IsEnable=0 then N'失效' else N'有效' end flg
from ODS_T_RESOURCE
where [Res_parent_code]='c13812c31e67405b9678d760f8d29de3' 
 )B

--配置数据-特殊采购审批条件-部分费用性质AR需采购额外审批/部分费用性质仅可申请AP
SELECT * INTO spk_specapprovalconditionforprocurement_Tmp from (--配置数据-特殊采购审批条件-部分费用性质AR需采购额外审批/部分费用性质仅可申请AP
select 
Res_CodeData3 as spk_costcenter,
Res_Data as spk_company,
case when Res_Name=2 then N'发起AR需采购审批' when res_name=1 then N'只能申请AP类型' else '' end as spk_type,
Res_Name as spk_Name,
Res_Data1 as spk_consumercategories,
Res_CodeData2 as spk_natureofexpenses,
case when IsEnable=0 then N'失效' else N'有效' end flg
from ODS_T_RESOURCE
where Res_parent_code='3fbd868a9afb4276898080af3062d126')B

--采购审批金额矩阵
SELECT * INTO spk_procurementapprovalamountmatrix_Tmp from (--采购审批金额矩阵
select 
tma.[Amount]as [spk_mainprocurement],
'' as [spk_agencyprocurement],
'' as [spk_agentprocurementpricecomparisonapprover],
otba.[Amount] as [spk_pricecomparisonapprover],
tma.[Level] as [spk_name],
tma.Filiale as [spk_organizational]
from ODS_T_MainPurchaseAmount TMA
left join ODS_T_BiddingAmount otba 
on TMA.Filiale=otba.Filiale
 )B

--配置数据-大区主数据
SELECT * INTO spk_districtmasterdata_Tmp from (
--配置数据-大区主数据
select 
spk_BPMCode,
spk_name,
spk_chinesevalue,
spk_englishvalue,
N'Dis-' + RIGHT('000' + CAST(b AS NVARCHAR(3)), 3) as spk_districtcode,
spk_paymentmethod,
flg
from (
select 
Res_Code as spk_BPMCode,
Res_Name as spk_name,
Res_Name as spk_chinesevalue,
Res_Name as spk_englishvalue,
'' as spk_districtcode,
Res_CodeData3 as spk_paymentmethod,
ROW_NUMBER() over(order by Res_Name desc) b,
case when IsEnable=0 then N'失效' else N'有效' end flg
from ODS_T_RESOURCE tr
where Res_Parent_Code='0d1d966d65cb4399a2655a20ba962320' )A )B

--配置数据-成本中心主数据
select * INTO spk_costcentermasterdata_Tmp  from (
select 
CAST(Res_Code AS  NVARCHAR(50)) as spk_BPMCode,
CAST(Res_Name as NVARCHAR(255)) as spk_name,
CAST(Res_Name as NVARCHAR(255)) as spk_chinesevalue,
CAST(Res_Name as NVARCHAR(255)) as spk_englishvalue,
CAST(Res_Data1 as NVARCHAR(1000)) as spk_costcentercode,
CAST(N'CCenter-' + RIGHT('00000' + CAST(r_num AS NVARCHAR(5)), 5)  AS NVARCHAR(50)) as spk_ccentercoder_num,
CAST(Res_CodeData3 as NVARCHAR(1000))as spk_subsidiary,
flg
from (
    select Res_Code,Res_Name,Res_Data1,Res_CodeData3,
    ROW_NUMBER() over(order by Res_Name desc) r_num,
	case when IsEnable=0 then N'失效' else N'有效' end flg
    from ODS_T_RESOURCE 
    where Res_Parent_Code='bdcecc445f614d79aad2d957ab1a0cb5' 
    ) a)B
	
--配置数据-大区主数据与BU映射关系
SELECT * INTO spk_organizational_district_Tmp from (
select otre.Res_name as spk_district,value as spk_organizational,
case when IsEnable=0 then N'失效' else N'有效' end flg
 from ODS_T_RESOURCE otre 
 cross apply STRING_SPLIT(Res_CodeData2,',')
where Res_Parent_Code='0d1d966d65cb4399a2655a20ba962320')B

--紧急付款类型配置
SELECT 
Res_Code AS  spk_BPMCode,
REPLACE(REPLACE(SUBSTRING(Res_Name, 1, PATINDEX(N'%[^a-zA-Z%, ]%', Res_Name) - 1), ' ', ''), ',', '') AS spk_code, 
Res_Name AS spk_Name,
Res_Name AS spk_pturtTypeName,
case when Res_Name=N'Other 其他' then  N'其他紧急' else N'紧急' end AS spk_pturttype,
case when IsEnable=0 then N'失效' else N'有效' end flg
into spk_pturttypeconfig_tmp
from ODS_T_RESOURCE
where Res_Parent_Code='4e94feede80f44a1ae5aa7ef80eaf2d0' 

--城市与BU的关系
SELECT 
CONCAT(otr.Res_Name ,otr.Res_Data)  as [spk_city],
otr2.res_code [spk_company],
case when otr.IsEnable=0 then N'失效' else N'有效' end flg
into spk_companymasterdata_citymasterdata_tmp
from ODS_T_RESOURCE otr 
left join ODS_T_RESOURCE otr2 
on otr2.Res_Parent_Code ='61a3f911b5ae4bc98cddd441833d861e' and otr.Res_Data1=otr2.Res_Data 
where otr.[Res_Parent_Code]='da4e31c2037649808667fc084bbabb1f'


--Business flow type
if object_id('spk_agentconfiguration_tmp','u') is not null
	drop table spk_agentconfiguration_tmp;
select
a.ID BpmId,
a.[ProcessName] OriginalProcessName,
case
	when a.ProcessName='All' then ''
	when a.ProcessName='Abbott.Process\SupplierApplication' or a.ProcessName='Abbott.Process\HcpLevelApplication' then 'SupplierApplication' --供应商申请
	when a.ProcessName='Abbott.Process\ProcurementApplication' then 'ProcurementApplication' --采购申请
	when a.ProcessName='Abbott.Process\PurchaseOrderApplication' then 'PurchaseOrder' --采购订单申请
	when a.ProcessName='Abbott.Process\WaiverApplication' then 'WaiverApplication' --竞价豁免申请
	when a.ProcessName='Abbott.Process\BiddingApplication' then 'BiddingApplication' --比价申请
	when a.ProcessName='Abbott.Process\GoodsReceiveApplication' then 'GoodsReceiving' --收货申请
	when a.ProcessName='Abbott.Process\PaymentApplication' then 'PaymentApplication' --付款申请
	else a.ProcessName
end ProcessName,
bizFlowType.spk_businessflowtypeid,
a.SrcEmpID SrcEmpID,
staff.spk_NexBPMCode StaffId,
a.DestEmpID DestEmpID,
agent.spk_NexBPMCode AgentId,
creator.spk_NexBPMCode CreatorId,
a.StartTime,
a.EndTime,
a.CreateTime,
a.Description,
staff.spk_name+'的代理：'+agent.spk_name+'('+isnull(bizFlowType.spk_businesstypedescription,'所有')+'流程)' Title,
a.IsEnable,
a.IsRemind
into [spk_agentconfiguration_tmp]
from ODS_T_WARRANTY_DESC a
join spk_staffmasterdata staff on a.SrcEmpID=staff.bpm_id
join spk_staffmasterdata agent on a.[DestEmpId]=agent.bpm_id
join spk_staffmasterdata creator on a.[LastUpdateEmpID]=creator.bpm_id
left join (select distinct * from spk_businessflowtype) bizFlowType on
(case
	when a.ProcessName='Abbott.Process\SupplierApplication' or a.ProcessName='Abbott.Process\HcpLevelApplication' then 'SupplierApplication' --供应商申请
	when a.ProcessName='Abbott.Process\ProcurementApplication' then 'ProcurementApplication' --采购申请
	when a.ProcessName='Abbott.Process\PurchaseOrderApplication' then 'PurchaseOrder' --采购订单申请
	when a.ProcessName='Abbott.Process\WaiverApplication' then 'WaiverApplication' --竞价豁免申请
	when a.ProcessName='Abbott.Process\BiddingApplication' then 'BiddingApplication' --比价申请
	when a.ProcessName='Abbott.Process\GoodsReceiveApplication' then 'GoodsReceiving' --收货申请
	when a.ProcessName='Abbott.Process\PaymentApplication' then 'PaymentApplication' --付款申请
	else a.ProcessName
end)=bizFlowType.spk_name
where a.ProcessName not in('Abbott.Process\GoodsReturnApplication','Abbott.Process\WholesalerVerificationApplication') --排除这两种类型，这属于Eflow