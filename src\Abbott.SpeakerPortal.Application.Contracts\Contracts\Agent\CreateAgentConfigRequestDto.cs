﻿using Abbott.SpeakerPortal.Enums;

using System;
using System.Collections.Generic;
using System.Text;

namespace Abbott.SpeakerPortal.Contracts.Agent
{
    public class CreateAgentConfigRequestDto
    {
        /// <summary>
        /// 审批流程类型
        /// </summary>
        //public WorkflowTypeName WorkflowType { get; set; }
        /// <summary>
        /// 业务类型Id，可空，空则表示“全部”
        /// </summary>
        //public Guid? BusinessTypeId { get; set; }
        /// <summary>
        /// 业务类型名称
        /// </summary>
        //public string BusinessTypeName { get; set; }
        /// <summary>
        /// 业务类型名称,可空，空则表示“全部”
        /// </summary>
        public ResignationTransfer.TaskFormCategory? BusinessType { get; set; }
        /// <summary>
        /// 原审批人
        /// </summary>
        public Guid OriginalApprover { get; set; }
        /// <summary>
        /// 代理人
        /// </summary>
        public Guid Agent { get; set; }
        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime StartDate { get; set; }
        /// <summary>
        /// 截止日期
        /// </summary>
        public DateTime EndDate { get; set; }
        /// <summary>
        /// 是否提醒原审批人
        /// </summary>
        public bool IsNotifyOriginalApprover { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
    }
}
