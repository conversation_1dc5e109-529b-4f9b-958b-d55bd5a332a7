CREATE PROCEDURE dbo.sp_BdHistory
AS 
BEGIN 
 
	select 
newid() AS Id,--自动生成的uuid
id as bpm_id,
Number AS BudgetId,--以""MB""开头时代表主预算，基于该信息匹配至11-1迁移的主预算ID；
--以""CC""开头时代表子预算，基于该信息匹配至12-1迁移的子预算ID"
case when SUBSTRING(Number,1,2)='MB' then '1' 
when SUBSTRING(Number,1,2)='CC' then '2' end   AS BudgetType,--以""MB""开头时代表主预算，填写为1；
--以""CC""开头时代表子预算，填写为2"
OperateEmpId AS OperatorId,--以该ID匹配至员工主数据，若该值填写为1则填写为默认用户(admin)
OperateEmpId AS OperatorName,--以该ID匹配至T_Employee.Emp_Id，将匹配回的Emp_Name填入
OperateDate AS OperatingTime,--
case when UPPER(Type) ='E' then '1' 
when UPPER(Type)='F' then '2' 
when UPPER(Type)='N' then '3' 
when UPPER(Type)='C' then '4' 
when UPPER(Type)='A' then '6' 
end  AS OperateType,--E-启用-1
--F-冻结-2
--N-新增-3
--C-调整-4
--A-调拨-6"
case when Type='N' then substring(Content,CHARINDEX(N'预算金额',Content)+5,CHARINDEX(N'预算负责人',Content)-7)
else CASE WHEN  Amount<>'' OR Amount<>NULL THEN Amount ELSE '0' END   end AS OperateAmount,--对于Type为N的记录，部分Amount可能为空，需要基于如下逻辑查询出Amount：
--基于Content中""预算金额:""后，直到"";预算负责人...""前的数字填入，原BPM处理方式可参考存储过程SP_GetBudgetLog中语句：
--{case when b.Type='N' then replace(substring(Content,CHARINDEX('预算金额',Content)+5,CHARINDEX('预算负责人',Content)-6),';','')}"
Content AS OperateContent,--
Remark AS Remark,--
'{}' AS ExtraProperties,--默认填写为"{}"
'NULL' AS ConcurrencyStamp,--?
OperateDate AS CreationTime,--填充为OperatingTime即可
OperateEmpId AS CreatorId,--填充为OperatorId即可
'NULL' AS LastModificationTime,--默认为空
'NULL' AS LastModifierId,--默认为空
'0' AS IsDeleted,--默认为0(BPM的删除功能是hard delete，已删除的不会有记录)
'NULL' AS DeleterId,--默认为空
'NULL' AS DeletionTime,--默认为空 
CASE WHEN substring(Content,0,6)=N'被调整编号' THEN  substring(Content,CHARINDEX(N'被调整编号',Content)+6,CHARINDEX(N'调整金额',Content)-8)
WHEN substring(Content,0,6)=N'调整至编号' THEN  substring(Content,CHARINDEX(N'调整至编号',Content)+6,CHARINDEX(N'调整金额',Content)-8)
ELSE '' END 
AS TargetBudgetCode--对于Type为A的记录，需要基于如下逻辑查询被调整的预算编号信息：
--基于Content内的数据，取""被调整编号:""或""调整至编号:""后，直到"";调整金额...""前的预算代码"
INTO #BdHistory_tmp
from PLATFORM_ABBOTT.dbo.ODS_T_Pur_ExpenseBudget_LogInfo otpebli 


 IF OBJECT_ID(N'PLATFORM_ABBOTT.dbo.BdHistory_tmp ', N'U') IS NOT NULL
	BEGIN
		update a 
		set a.bpm_id               = b.bpm_id
           ,a.BudgetId             = b.BudgetId
           ,a.BudgetType           = b.BudgetType
           ,a.OperatorId           = b.OperatorId
           ,a.OperatorName         = b.OperatorName
           ,a.OperatingTime        = b.OperatingTime
           ,a.OperateType          = b.OperateType
           ,a.OperateAmount        = b.OperateAmount
           ,a.OperateContent       = b.OperateContent
           ,a.Remark               = b.Remark
           ,a.ExtraProperties      = b.ExtraProperties
           ,a.ConcurrencyStamp     = b.ConcurrencyStamp
           ,a.CreationTime         = b.CreationTime
           ,a.CreatorId            = b.CreatorId
           ,a.LastModificationTime = b.LastModificationTime
           ,a.LastModifierId       = b.LastModifierId
           ,a.IsDeleted            = b.IsDeleted
           ,a.DeleterId            = b.DeleterId
           ,a.DeletionTime         = b.DeletionTime
           ,a.TargetBudgetCode     = b.TargetBudgetCode
         from PLATFORM_ABBOTT.dbo.BdHistory_tmp a
         left join #BdHistory_tmp b on a.Bpm_id = b.Bpm_id
         
         insert into PLATFORM_ABBOTT.dbo.BdHistory_tmp 
         select a.Id
               ,a.bpm_id
               ,a.BudgetId
               ,a.BudgetType
               ,a.OperatorId
               ,a.OperatorName
               ,a.OperatingTime
               ,a.OperateType
               ,a.OperateAmount
               ,a.OperateContent
               ,a.Remark
               ,a.ExtraProperties
               ,a.ConcurrencyStamp
               ,a.CreationTime
               ,a.CreatorId
               ,a.LastModificationTime
               ,a.LastModifierId
               ,a.IsDeleted
               ,a.DeleterId
,a.DeletionTime
 ,a.TargetBudgetCode
         from #BdHistory_tmp a
         where not exists (select * from PLATFORM_ABBOTT.dbo.BdHistory_tmp where Bpm_id = a.Bpm_id)
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into PLATFORM_ABBOTT.dbo.BdHistory_tmp from #BdHistory_tmp
    -- select * from #vendor_tbl
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END

END;

