﻿using Abbott.SpeakerPortal.Contracts.Integration.Dspot;

using Hangfire;

using Microsoft.Extensions.DependencyInjection;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

using Volo.Abp.BackgroundWorkers.Hangfire;

namespace Abbott.SpeakerPortal.BackgroundWorkers.Bpcs
{
    public class SyncTableVendorsWorker : SpeakerPortalBackgroundWorkerBase
    {
        public SyncTableVendorsWorker()
        {
            CronExpression = Cron.Daily(1);//每天凌晨1点
        }

        public override async Task DoWorkAsync(CancellationToken cancellationToken = default)
        {
            //逻辑处理
            await LazyServiceProvider.LazyGetService<IIntermediateToSpeakerService>().SyncTableAvmPmfvm();
        }
    }
}
