﻿using System;
using System.Collections.Generic;
using System.Text;

using Abbott.SpeakerPortal.Dtos;

using MiniExcelLibs.Attributes;

namespace Abbott.SpeakerPortal.Contracts.Concur.MealReport
{
    public class MealReportQueryRequestDto : PagedDto
    {
        /// <summary>
        /// 首次提交日期1
        /// </summary>
        public DateTime? FirstSubmittedDate1 { get; set; }

        /// <summary>
        /// 首次提交日期2
        /// </summary>
        public DateTime? FirstSubmittedDate2 { get; set; }

        /// <summary>
        /// 交易日期1
        /// </summary>
        public DateTime? TransactionDate1 { get; set; }

        /// <summary>
        /// 交易日期2
        /// </summary>
        public DateTime? TransactionDate2 { get; set; }

        /// <summary>
        /// 审批状态
        /// </summary>
        public string ApprovalStatus { get; set; }

        /// <summary>
        /// 付款日期1
        /// </summary>
        public DateTime? PaidDate1 { get; set; }

        /// <summary>
        /// 付款日期2
        /// </summary>
        public DateTime? PaidDate2 { get; set; }

        /// <summary>
        /// 付款状态
        /// </summary>
        public string PaymentStatus { get; set; }

        /// <summary>
        /// 员工分组层级1
        /// </summary>
        public string EmployeeHierarchy1 { get; set; }

        /// <summary>
        /// 员工分组层级1
        /// </summary>
        public string EmployeeHierarchy2 { get; set; }

        /// <summary>
        /// 职能部门
        /// </summary>
        public string EmployeeDepartment { get; set; }

        /// <summary>
        /// 大区
        /// </summary>
        public string EmployeeRegion { get; set; }

        /// <summary>
        /// 工作城市
        /// </summary>
        public string EmployeeCity { get; set; }
    }
}
