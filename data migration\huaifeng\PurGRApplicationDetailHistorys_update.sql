SELECT
 TRY_CONVERT(UNIQUEIDENTIFIER, [Id]) [Id]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([GRApplicationId],'00000000-0000-0000-0000-000000000000')) [GRApplicationId]
,TRY_CONVERT(UNIQUEIDENTIFIER, [ProductId]) [ProductId]
,[InvoiceType]
,ISNULL([OrderQuantity],0) [OrderQuantity]
,IIF([TotalReceivedQuantity] = '','0.0',[TotalReceivedQuantity]) [TotalReceivedQuantity]
,[DeliveryMethod]
,IIF(CurrentReceivingQuantity = '','0.0',CurrentReceivingQuantity) [CurrentReceivingQuantity]
,IIF([CurrentSignedQuantity] = '','0.0',[CurrentSignedQuantity]) [CurrentSignedQuantity]
,ISNULL([UnitPrice],0) [UnitPrice]
,[ReceivedAmount]
,ISNULL([SigningDate],GETDATE()) [SigningDate]
,[IsArrive]
,[ExtraProperties]
,'NULL' [ConcurrencyStamp]
,[CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CreatorId]) [CreatorId]
,[LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [LastModifierId]) [LastModifierId]
,[IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, [DeleterId]) [DeleterId]
,[DeletionTime]
,[AllocationAmount]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([PRDetailId],'00000000-0000-0000-0000-000000000000')) [PRDetailId]
,[AllocationRMBAmount]
,TRY_CONVERT(UNIQUEIDENTIFIER, [PODetailId]) [PODetailId]
,TRY_CONVERT(UNIQUEIDENTIFIER, [PurPAApplicationId]) [PurPAApplicationId]
,[PurchaseRMBAmount]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([GRApplicationDetailId],'00000000-0000-0000-0000-000000000000')) [GRApplicationDetailId]
,0 [IsAdvancePayment]
,[ProductName]
INTO #PurGRApplicationDetailHistorys
FROM (SELECT *,ROW_NUMBER() OVER(PARTITION BY Id ORDER BY Id) RK FROM PLATFORM_ABBOTT_STG.dbo.PurGRApplicationDetailHistorys)a
WHERE RK = 1
;
--drop table #PurGRApplicationDetailHistorys

USE Speaker_Portal_STG;

UPDATE a 
SET 
 a.[Id] = b.[Id]
,a.[GRApplicationId] = b.[GRApplicationId]
,a.[ProductId] = b.[ProductId]
,a.[InvoiceType] = b.[InvoiceType]
,a.[OrderQuantity] = b.[OrderQuantity]
,a.[TotalReceivedQuantity] = b.[TotalReceivedQuantity]
,a.[DeliveryMethod] = b.[DeliveryMethod]
,a.[CurrentReceivingQuantity] = b.[CurrentReceivingQuantity]
,a.[CurrentSignedQuantity] = b.[CurrentSignedQuantity]
,a.[UnitPrice] = b.[UnitPrice]
,a.[ReceivedAmount] = b.[ReceivedAmount]
,a.[SigningDate] = b.[SigningDate]
,a.[IsArrive] = b.[IsArrive]
,a.[ExtraProperties] = b.[ExtraProperties]
,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = b.[CreatorId]
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = b.[LastModifierId]
,a.[IsDeleted] = b.[IsDeleted]
,a.[DeleterId] = b.[DeleterId]
,a.[DeletionTime] = b.[DeletionTime]
,a.[AllocationAmount] = b.[AllocationAmount]
,a.[PRDetailId] = b.[PRDetailId]
,a.[AllocationRMBAmount] = b.[AllocationRMBAmount]
,a.[PODetailId] = b.[PODetailId]
,a.[PurPAApplicationId] = b.[PurPAApplicationId]
,a.[PurchaseRMBAmount] = b.[PurchaseRMBAmount]
,a.[GRApplicationDetailId] = b.[GRApplicationDetailId]
,a.[IsAdvancePayment] = b.[IsAdvancePayment]
,a.[ProductName] = b.[ProductName]
FROM dbo.PurGRApplicationDetailHistorys a
left join #PurGRApplicationDetailHistorys  b
ON a.id=b.id;


INSERT INTO dbo.PurGRApplicationDetailHistorys
(
 [Id]
,[GRApplicationId]
,[ProductId]
,[InvoiceType]
,[OrderQuantity]
,[TotalReceivedQuantity]
,[DeliveryMethod]
,[CurrentReceivingQuantity]
,[CurrentSignedQuantity]
,[UnitPrice]
,[ReceivedAmount]
,[SigningDate]
,[IsArrive]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[AllocationAmount]
,[PRDetailId]
,[AllocationRMBAmount]
,[PODetailId]
,[PurPAApplicationId]
,[PurchaseRMBAmount]
,[GRApplicationDetailId]
,[IsAdvancePayment]
,[ProductName]
)
SELECT
 [Id]
,[GRApplicationId]
,[ProductId]
,[InvoiceType]
,[OrderQuantity]
,[TotalReceivedQuantity]
,[DeliveryMethod]
,[CurrentReceivingQuantity]
,[CurrentSignedQuantity]
,[UnitPrice]
,[ReceivedAmount]
,[SigningDate]
,[IsArrive]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[AllocationAmount]
,[PRDetailId]
,[AllocationRMBAmount]
,[PODetailId]
,[PurPAApplicationId]
,[PurchaseRMBAmount]
,[GRApplicationDetailId]
,[IsAdvancePayment]
,[ProductName]
FROM #PurGRApplicationDetailHistorys a
WHERE not exists (select * from dbo.PurGRApplicationDetailHistorys where id=a.id);


--truncate table dbo.PurGRApplicationDetailHistorys

