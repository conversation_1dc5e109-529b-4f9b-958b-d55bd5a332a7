﻿using Microsoft.AspNetCore.Hosting;
using PdfSharp.Fonts;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.AppServices.Common
{
    internal class ChineseFontResolver : IFontResolver
    {
        private readonly IWebHostEnvironment _env;
        public ChineseFontResolver(IWebHostEnvironment env)
        {
            this._env = env;
        }
        ///// <summary>
        ///// 字体作为嵌入资源所在程序集
        ///// </summary>
        //public static string FontAssemblyString { get; set; } = "RedisTest";
        ///// <summary>
        ///// 字体作为嵌入资源所在命名空间
        ///// </summary>
        //public static string FontNamespace { get; set; } = "RedisTest.Font";
        /// <summary>
        /// 字体名称
        /// </summary>
        public static class FamilyNames
        {
            // This implementation considers each font face as its own family.

            /// <summary>
            /// 华文
            /// </summary>
            public const string STKAITI = "STKAITI.TTF";
            /// <summary>
            /// 宋体
            /// </summary>
            public const string SIMSUN = "SIMSUN.TTC";
            /// <summary>
            /// CALIBRI
            /// </summary>
            public const string CALIBRI = "CALIBRI.TTF";
            /// <summary>
            /// NotoSerifSC
            /// </summary>
            public const string NotoSerifSCLight = "NotoSerifSC-Light.ttf";
            public const string NotoSerifSCRegular = "NotoSerifSC-Regular.ttf";
        }
        /// <summary>
        /// Selects a physical font face based on the specified information
        /// of a required typeface.
        /// </summary>
        /// <param name="familyName">Name of the font family.</param>
        /// <param name="isBold">Set to <c>true</c> when a bold font face
        ///  is required.</param>
        /// <param name="isItalic">Set to <c>true</c> when an italic font face 
        /// is required.</param>
        /// <returns>
        /// Information about the physical font, or null if the request cannot be satisfied.
        /// </returns>
        public FontResolverInfo? ResolveTypeface(string familyName, bool isBold, bool isItalic)
        {
            // Note: PDFsharp calls ResolveTypeface only once for each unique combination
            // of familyName, isBold, and isItalic. 

            return new FontResolverInfo(familyName, isBold, isItalic);
            // Return null means that the typeface cannot be resolved and PDFsharp forwards
            // the typeface request depending on PDFsharp build flavor and operating system.
            // Alternatively forward call to PlatformFontResolver.
            //return PlatformFontResolver.ResolveTypeface(familyName, isBold, isItalic);
        }

        /// <summary>
        /// Gets the bytes of a physical font face with specified face name.
        /// </summary>
        /// <param name="faceName">A face name previously retrieved by ResolveTypeface.</param>
        /// <returns>
        /// The bits of the font.
        /// </returns>
        public byte[]? GetFont(string faceName)
        {
            // Note: PDFsharp never calls GetFont twice with the same face name.
            // Note: If a typeface is resolved by the PlatformFontResolver.ResolveTypeface
            //       you never come here.
            //var name = $"{FontNamespace}.{faceName}";

            //using Stream stream = Assembly.Load(FontAssemblyString).GetManifestResourceStream(name) ?? throw new ArgumentException("No resource named '" + name + "'.");
            string wwwrootPath = _env.WebRootPath;
            var fontPath = Path.Combine(wwwrootPath, $"fonts/{faceName}");
            //var path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, $"Font/{faceName}");

            using Stream stream = File.OpenRead(fontPath);
            int num = (int)stream.Length;
            byte[] array = new byte[num];
            stream.Read(array, 0, num);
            // Return the bytes of a font.
            return array;
        }
    }
}
