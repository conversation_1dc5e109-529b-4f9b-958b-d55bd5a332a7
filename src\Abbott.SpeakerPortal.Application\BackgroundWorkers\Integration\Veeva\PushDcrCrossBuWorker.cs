﻿using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Integration.Veeva;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.DistributedLocking;

namespace Abbott.SpeakerPortal.BackgroundWorkers.Integration.Veeva
{
    public class PushDcrCrossBuWorker : SpeakerPortalBackgroundWorkerBase
    {
        private IScheduleJobLogService _jobLogService;
        private IInteVeevaService _inteVeevaService;

        public PushDcrCrossBuWorker(IServiceProvider serviceProvider)
        {
            _jobLogService = serviceProvider.GetService<IScheduleJobLogService>();
            _inteVeevaService = serviceProvider.GetService<IInteVeevaService>();
        }

        public override async Task DoWorkAsync(CancellationToken cancellationToken = default)
        {
            //1、加锁执行 执行完自动释放锁
            await using (var handle = await LazyServiceProvider.LazyGetService<IAbpDistributedLock>().TryAcquireAsync(DistributedLock.DistributedLock_PushDcrCroosBuWorkder))
            {
                // 锁已被其他任务持有，跳过
                if (handle == null)
                    return;

                var log = _jobLogService.InitSyncLog("PushDcrCrossBuWorker");
                try
                {
                    //读取ABP数据表WfApprovalTasks的医师信息相关的未处理过的记录 摘出要进行DCR推送的信息
                    await _inteVeevaService.PushDcrByCrossBuSubmitTask(log);
                }
                catch (Exception ex)
                {
                    log.IsSuccess = false;
                    log.Remark = ex.ToString();
                }
                finally
                {
                    _jobLogService.SyncLog(log);
                }
            }
        }
    }
}
