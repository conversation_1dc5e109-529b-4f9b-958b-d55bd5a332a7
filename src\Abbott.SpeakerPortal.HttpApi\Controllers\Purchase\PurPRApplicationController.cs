using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Dataverse;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Contracts.Vendor.NonSpeaker;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Filters;
using Abbott.SpeakerPortal.Permissions;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

using Volo.Abp.Application.Dtos;

namespace Abbott.SpeakerPortal.Controllers.Purchase
{
    /// <summary>
    /// 采购申请
    /// </summary>
    [ApiExplorerSettings(GroupName = SwaggerGrouping.PROCURE_MANAGE)]
    public partial class PurPRApplicationController : SpeakerPortalController
    {
        IPurPRApplicationService _prApplicationService;

        public PurPRApplicationController(IPurPRApplicationService prApplicationService)
        {
            _prApplicationService = prApplicationService;
        }

        /// <summary>
        /// 获取采购申请列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult<PagedResultDto<GetPRApplicationListResponse>>))]
        public async Task<IActionResult> GetPRApplicationsAsync([FromBody] GetPRApplicationListRequest request)
        {
            var result = await _prApplicationService.GetPRApplicationsAsync(request);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 导出采购申请列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> ExportPRApplicationsAsync([FromBody] GetPRApplicationListRequest request)
        {
            var result = await _prApplicationService.ExportPRApplicationAsync(request);
            return File(result, "application/octet-stream", $"采购申请列表-{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}.xlsx");
        }

        /// <summary>
        /// 获取采购申请草稿列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult<PagedResultDto<GetPRApplicationListResponse>>))]
        public async Task<IActionResult> GetDraftPRApplicationsAsync([FromBody] GetPRApplicationListRequest request)
        {
            var result = await _prApplicationService.GetPRApplicationsAsync(request, Enums.Purchase.PurPRApplicationStatus.Draft);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 获取产品下拉列表
        /// </summary>
        /// <param name="orgId">机构Id</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<IEnumerable<ProductDto>>))]
        public async Task<IActionResult> GetProductsAsync(Guid orgId)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var dept = await dataverseService.GetOrganizations(orgId.ToString());
            if (!dept.Any())
                return Ok(MessageResult.SuccessResult());

            var parents = await LazyServiceProvider.LazyGetService<ICommonService>().GetParentOrgs(dept.First());
            var bu = parents.FirstOrDefault(a => a.OrganizationType == DataverseEnums.Organization.OrganizationType.Bu);
            if (bu == null)
                return Ok(MessageResult.SuccessResult());

            var buProducts = await dataverseService.GetOrgProductRelationsAsync(bu.Id.ToString());
            var products = await dataverseService.GetProductsAsync();
            return Ok(MessageResult.SuccessResult(buProducts.Join(products, a => a.ProductId, b => b.Id, (a, b) => new
            {
                b.Id,
                b.Name
            })));
        }

        /// <summary>
        /// 根据公司Id获取关联的货币信息
        /// </summary>
        /// <param name="companyId">公司Id</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<IEnumerable<CurrencyDto>>))]
        public async Task<IActionResult> GetCompanyCurrencyAsync(Guid companyId)
        {
            var currencies = await LazyServiceProvider.LazyGetService<IDataverseService>().GetCompanyCurrencyList();
            var datas = currencies.Where(a => a.CompanyId == companyId).ToArray();

            return Ok(MessageResult.SuccessResult(datas));
        }

        /// <summary>
        /// 获取用于选择的讲者列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<PagedResultDto<GetSpeakerListForChoiceResponseDto>>))]
        public async Task<IActionResult> GetSpeakerForChoiceAsync([FromQuery] GetSpeakerListForChoiceRequestDto request)
        {
            var data = await _prApplicationService.GetSpeakerForChoiceAsync(request);
            return Ok(MessageResult.SuccessResult(data));
        }

        /// <summary>
        /// 获取用于选择的非讲者供应商列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<PagedResultDto<GetNonSpeakerListForChoiceResponseDto>>))]
        public async Task<IActionResult> GetNonSpeakerForChoiceAsync([FromQuery] GetNonSpeakerListForChoiceRequestDto request)
        {
            var data = await _prApplicationService.GetNonSpeakerForChoiceAsync(request);
            return Ok(MessageResult.SuccessResult(data));
        }

        /// <summary>
        /// 获取用于选择的执行人列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<PagedResultDto<GetPersonVendorForChoiceResponseDto>>))]
        public async Task<IActionResult> GetExecutorForChoiceAsync([FromQuery] GetPersonVendorForChoiceRequestDto request)
        {
            var data = await _prApplicationService.GetPersonVendorForChoiceAsync(request);
            return Ok(MessageResult.SuccessResult(data));
        }

        /// <summary>
        /// 获取用于选择的主持人列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<PagedResultDto<GetPersonVendorForChoiceResponseDto>>))]
        public async Task<IActionResult> GetModeratorForChoiceAsync([FromQuery] GetPersonVendorForChoiceRequestDto request)
        {
            var data = await _prApplicationService.GetPersonVendorForChoiceAsync(request);
            return Ok(MessageResult.SuccessResult(data));
        }

        /// <summary>
        /// 获取Psa例外剩余次数和金额
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<GetSurplusPsaExtraLimitResponseDto>))]
        public async Task<IActionResult> GetPsaExtraSurplusLimitAsync([FromQuery] GetSurplusPsaExtraLimitRequestDto request)
        {
            var data = await _prApplicationService.GetPsaExtraSurplusLimitAsync(request);
            return Ok(MessageResult.SuccessResult(data));
        }

        /// <summary>
        /// 获取子预算列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<PagedResultDto<GetSubBudgetsResponseDto>>))]
        public async Task<IActionResult> GetSubBudgetInfosAsync([FromQuery] GetSubBudgetsRequestDto request)
        {
            var data = await _prApplicationService.GetSubBudgetInfosAsync(request);
            return Ok(MessageResult.SuccessResult(data));
        }

        /// <summary>
        /// 获取预算信息
        /// </summary>
        /// <param name="subBudgetId">子预算Id</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<IEnumerable<GetBudgetInfoResponse>>))]
        public async Task<IActionResult> GetBudgetInfoAsync(Guid subBudgetId)
        {
            var data = await _prApplicationService.GetSubBudgetInfoAsync(subBudgetId);
            return Ok(MessageResult.SuccessResult(data));
        }

        /// <summary>
        /// 获取消费大类
        /// </summary>
        /// <param name="orgId">机构Id</param>
        /// <param name="isCapital">是否资产，由预算信息带出</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<IEnumerable<ConsumeCategoryDto>>))]
        public async Task<IActionResult> GetConsumeCategoryAsync(Guid orgId, bool isCapital)
        {
            var data = await _prApplicationService.GetConsumeCategoryAsync(orgId, isCapital);
            return Ok(MessageResult.SuccessResult(data));
        }

        /// <summary>
        /// 根据消费大类、组织、成本中心、产品、付款方式获取费用性质
        /// </summary>
        /// <param name="consumeId">消费大类</param>
        /// <param name="orgId">组织</param>
        /// <param name="costcenterId">成本中心</param>
        /// <param name="productIds">产品Id集合</param>
        /// <param name="payMethod">付款方式</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<IEnumerable<ConsumeCategoryDto>>))]
        public async Task<IActionResult> GetCostNatureAsync(Guid consumeId, Guid orgId, Guid costcenterId, string productIds, Enums.Purchase.PayMethods payMethod)
        {
            var pIds = productIds.Split(",", StringSplitOptions.RemoveEmptyEntries).Select(Guid.Parse);
            var data = await _prApplicationService.GetCostNatureAsync(consumeId, orgId, costcenterId, pIds, payMethod);
            return Ok(MessageResult.SuccessResult(data));
        }

        /// <summary>
        /// 获取主会场PR选择列表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<PagedResultDto<GetMainVenuePRListResponse>>))]
        public async Task<IActionResult> GetMainVenuePRsAsync([FromQuery] GetMainVenuePRListRequest request)
        {
            var result = await _prApplicationService.GetMainVenuePRsAsync(request);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 创建采购申请单
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        [Authorize(SpeakerPortalPermissions.Purchase.ApplicationCRUD)]
        public async Task<IActionResult> CreatePRApplicationAsync([FromBody] CreatePRApplicationRequest request)
        {
            var result = await _prApplicationService.CreatePRApplicationAsync(request);
            return Ok(result);
        }

        /// <summary>
        /// 获取采购申请单详情
        /// </summary>
        /// <param name="id"></param>
        /// <param name="taskId"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<GetPRApplicationResponse>))]
        [Authorize(SpeakerPortalPermissions.Purchase.ApplicationRead)]
        [Authorize(SpeakerPortalPermissions.TaskCenter.LaunchProcureApplication)]
        [Authorize(SpeakerPortalPermissions.TaskCenter.ApproveProcureApplication)]
        public async Task<IActionResult> GetPRApplicationAsync(Guid id, Guid? taskId = null)
        {
            var result = await _prApplicationService.GetPRApplicationAsync(id, taskId);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 修改采购申请单
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        [Authorize(SpeakerPortalPermissions.Purchase.ApplicationCRUD)]
        [ValidateParticipant(nameof(request), PropertyName = nameof(UpdatePRApplicationRequest.Id))]
        public async Task<IActionResult> UpdatePRApplicationAsync([FromBody] UpdatePRApplicationRequest request)
        {
            var result = await _prApplicationService.UpdatePRApplicationAsync(request);
            return Ok(result);
        }

        /// <summary>
        /// 删除采购申请单
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        [Authorize(SpeakerPortalPermissions.Purchase.ApplicationCRUD)]
        public async Task<IActionResult> DeletePRApplicationAsync(Guid id)
        {
            var result = await _prApplicationService.DeletePRApplicationAsync(id);
            return Ok(result);
        }

        /// <summary>
        /// 提交采购申请
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        [Authorize(SpeakerPortalPermissions.Purchase.ApplicationCRUD)]
        [Authorize(SpeakerPortalPermissions.TaskCenter.LaunchAction)]
        [ValidateParticipant(nameof(request), PropertyName = nameof(UpdatePRApplicationRequest.Id))]
        public async Task<IActionResult> SubmitPRApplicationAsync([FromBody] UpdatePRApplicationRequest request)
        {
            var result = await _prApplicationService.SubmitPRApplicationAsync(request);
            if (!result.Success || (result.Success && result.Code != MessageModelBase.SuccessMessage.Code))
                await CurrentUnitOfWork.RollbackAsync();

            return Ok(result);
        }

        /// <summary>
        /// 根据组织Id获取关联的成本中心
        /// </summary>
        /// <param name="orgId"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<IEnumerable<KeyValuePair<Guid, string>>>))]
        public async Task<IActionResult> GetCostcenterByOrgAsync(Guid? orgId)
        {
            var result = await _prApplicationService.GetCostcenterByOrgAsync(orgId);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 获取某个PR单的补录文件列表
        /// </summary>
        /// <param name="Id">The identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<AdditionFilesResponseDto>))]
        public async Task<IActionResult> GetAdditionFilesAsync(Guid Id)
        {
            var result = await _prApplicationService.GetAdditionFilesAsync(Id);
            return Ok(result);
        }

        /// <summary>
        /// 补录文件：为PR单补录文件
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        [Authorize(SpeakerPortalPermissions.Purchase.ApplicationCRUD)]
        [Authorize(SpeakerPortalPermissions.TaskCenter.LaunchAction)]
        public async Task<IActionResult> AdditionFilesAsync([FromBody] AdditionFilesRequestDto request)
        {
            var result = await _prApplicationService.AdditionFilesAsync(request);
            return Ok(result);
        }

        /// <summary>
        /// 根据CompanyId查询PP里的采购推送人
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="isAssistant"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<IEnumerable<GetPurchasersResponseDto>>))]
        public IActionResult GetPurchasers(Guid? companyId = null, bool isAssistant = false)
        {
            var result = _prApplicationService.GetPurchasers(companyId, isAssistant);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 根据成本中心获取活动类型
        /// </summary>
        /// <param name="costcenterId"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<IEnumerable<KeyValuePair<string, string>>>))]
        public async Task<IActionResult> GetActiveTypeByCostcenterAsync(Guid costcenterId)
        {
            var result = await _prApplicationService.GetActiveTypeByCostcenterAsync(costcenterId);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 根据消费大类和费用性质获取讲者身份类型
        /// </summary>
        /// <param name="consumeId"></param>
        /// <param name="costnatureId"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<IEnumerable<DictionaryDto>>))]
        public async Task<IActionResult> GetIdentityTypesAsync(Guid consumeId, Guid costnatureId)
        {
            var result = await _prApplicationService.GetIdentityTypesAsync(consumeId, costnatureId);
            return Ok(MessageResult.SuccessResult(result));
        }
    }
}
