﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Abbott.SpeakerPortal.Contracts.Budget.Subbudget
{
    public class CreateCompareDto
    {
        public Guid Id { get; set; }
        public string Code { get; set; }
        public Guid BuId { get; set; }
        public decimal MasterAmount { get; set; }

        public decimal SubAmountSum { get; set; }

        public bool IsAdjusted { get { return this.MasterAmount >= this.SubAmountSum; } }
    }
}
