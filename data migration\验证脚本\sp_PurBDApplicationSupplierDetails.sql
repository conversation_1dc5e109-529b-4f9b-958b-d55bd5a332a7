CREATE PROCEDURE dbo.sp_PurBDApplicationSupplierDetails
AS 
BEGIN

SELECT 
NEWID()AS Id,--自动生成的uuid
a.ProcInstId,
c1.CompanyId,
C.id AS BDApplicationId,--
d.Code AS VendorId,--以ProcInstId查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到OtherGridPanel下的对应字段(可能有多行)；基于查询到的单行内code及对应content，结合该单对应PR单的公司编码CompanyId，去查询BPCSPMFVM对应的记录(VNDERX,VEXTNM,VMCMPY)，对于匹配成功的记录，再找到其对应的BPCSAVM.ID并填入(基于VMCMPY=VCMPNY AND VNDERX=VENDOR)
a.content AS VendorName,--
'' AS Unit,--BPM无该字段
d.Number AS Quantity,--以ProcInstId查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到OtherGridPanel下的对应字段(可能有多行)
Price AS TotalAmount,--
'{}' AS ExtraProperties,--默认填写为"{}"
'' AS ConcurrencyStamp,--?
c.CreationTime AS CreationTime,--与对应的PurBDApplications记录保持一致即可
c.CreatorId AS CreatorId,--与对应的PurBDApplications记录保持一致即可
'' AS LastModificationTime,--默认为空
'' AS LastModifierId,--默认为空
'0' AS IsDeleted,--默认为0
'' AS DeleterId,--默认为空
'' AS DeletionTime,--默认为空
d.UnitPrice AS UnitPrice--以ProcInstId查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到OtherGridPanel下的对应字段(可能有多行)
into #PurBDApplicationSupplierDetails_tmp
FROM PLATFORM_ABBOTT.dbo.ODS_AUTO_BIZ_T_BiddingApplication_Info_Other a --11932
left join PLATFORM_ABBOTT.dbo.ODS_AUTO_BIZ_T_BiddingApplication_Info A1
on a1.ProcInstId=a.ProcInstId--11932
left join PLATFORM_ABBOTT.dbo.PurBDApplications_tmp c --11932
on a1.ProcInstId=c.ProcInstId
LEFT JOIN (select ProcInstId,content, max(code) code, sum(cast(isnull(Number,'0.0') as decimal)) Number, sum(cast(UnitPrice as decimal)) UnitPrice from PLATFORM_ABBOTT.dbo.XML_3 group by ProcInstId,content) D --11932
ON A.ProcInstId=D.ProcInstId and a.content=d.content  
left join PLATFORM_ABBOTT.dbo.PurPRApplications_tmp c1 -- 11932
on c.ProcInstId  =c1.ProcInstId 
--20240904修改
--FROM PLATFORM_ABBOTT.dbo.ODS_AUTO_BIZ_T_BiddingApplication_Info_Other a
--left join PLATFORM_ABBOTT.dbo.ODS_AUTO_BIZ_T_BiddingApplication_Info A1
--on a1.ProcInstId=a.ProcInstId--11929
--left join PLATFORM_ABBOTT.dbo.PurBDApplications_tmp c
--on a1.serialNumber=c.ApplicationCode--15368
--LEFT JOIN PLATFORM_ABBOTT.dbo.XML_3 D
--ON A.ProcInstId=D.ProcInstId and a.content=d.content
--left join PLATFORM_ABBOTT.dbo.PurPRApplications_tmp c1
--on c.PRApplicationDetailId  =c1.ApplicationCode

IF OBJECT_ID(N'PLATFORM_ABBOTT.dbo.PurBDApplicationSupplierDetails_tmp', N'U') IS NOT NULL
BEGIN
	update a 
	set a.ProcInstId            = b.ProcInstId
       ,a.CompanyId             = b.CompanyId
       ,a.BDApplicationId       = b.BDApplicationId
       ,a.VendorId              = b.VendorId
       ,a.VendorName            = b.VendorName
       ,a.Unit                  = b.Unit
       ,a.Quantity              = b.Quantity
       ,a.TotalAmount           = b.TotalAmount
       ,a.ExtraProperties       = b.ExtraProperties
       ,a.ConcurrencyStamp      = b.ConcurrencyStamp
       ,a.CreationTime          = b.CreationTime
       ,a.CreatorId             = b.CreatorId
       ,a.LastModificationTime  = b.LastModificationTime
       ,a.LastModifierId        = b.LastModifierId
       ,a.IsDeleted             = b.IsDeleted
       ,a.DeleterId             = b.DeleterId
       ,a.DeletionTime          = b.DeletionTime
       ,a.UnitPrice             = b.UnitPrice
    from PLATFORM_ABBOTT.dbo.PurBDApplicationSupplierDetails_tmp a
    left join #PurBDApplicationSupplierDetails_tmp b on a.ProcInstId = b.ProcInstId
    
    insert into PLATFORM_ABBOTT.dbo.PurBDApplicationSupplierDetails_tmp
    select a.Id
          ,a.ProcInstId
          ,a.CompanyId
          ,a.BDApplicationId
          ,a.VendorId
          ,a.VendorName
          ,a.Unit
          ,a.Quantity
          ,a.TotalAmount
          ,a.ExtraProperties
          ,a.ConcurrencyStamp
          ,a.CreationTime
          ,a.CreatorId
          ,a.LastModificationTime
          ,a.LastModifierId
          ,a.IsDeleted
          ,a.DeleterId
         ,a.DeletionTime
          ,a.UnitPrice
 from #PurBDApplicationSupplierDetails_tmp a
    where not exists (select * from PLATFORM_ABBOTT.dbo.PurBDApplicationSupplierDetails_tmp where ProcInstId = a.ProcInstId)
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into PLATFORM_ABBOTT.dbo.PurBDApplicationSupplierDetails_tmp from #PurBDApplicationSupplierDetails_tmp
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END

END
