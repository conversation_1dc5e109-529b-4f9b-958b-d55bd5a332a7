﻿using Abbott.SpeakerPortal.AppServices.Common;
using Abbott.SpeakerPortal.BackgroundWorkers.Integration.Veeva;
using Abbott.SpeakerPortal.BackgroundWorkers.Report;
using Abbott.SpeakerPortal.BackgroundWorkers.Report.ProfessionalServiceTax;
using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.Cognitive;
using Abbott.SpeakerPortal.Contracts.Integration.Dspot;
using Abbott.SpeakerPortal.Contracts.Integration.EpdOlineMeeting;
using Abbott.SpeakerPortal.Contracts.Integration.EpdOlineMeeting.BPM;
using Abbott.SpeakerPortal.Contracts.Integration.Veeva;
using Abbott.SpeakerPortal.Contracts.Report.ProfessionalServiceTax;
using Abbott.SpeakerPortal.Contracts.STicket;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Filters;
using Abbott.SpeakerPortal.IAppServices;
using Flurl.Http;
using Hangfire;
using Abbott.SpeakerPortal.Extension;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

using Senparc.Weixin.WxOpen.Entities;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Mime;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Abbott.SpeakerPortal.Contracts.Common.PowerApp;
using Abbott.SpeakerPortal.Entities.User;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.SettingManagement;
using Abbott.SpeakerPortal.Redis;
using StackExchange.Redis;
using System.Net;

namespace Abbott.SpeakerPortal.Controllers
{
    /// <summary>
    /// 用于联调测试一些集成接口
    /// </summary>
    public class TestController : SpeakerPortalController
    {
        private IIntegrationDspotAppService _integrationDspotAppService;
        private IInteBpcsVendorAppService _inteBpcsVendorAppService;
        private IInteBpcsPAAppService _inteBpcsPAAppService;
        private IInteBpcsUncompletedPRVendorService _inteBpcsUncompletedPRVendorService;
        private IInteBpcsIntermediateService _inteBpcsIntermediateService;
        private IIntermediateToSpeakerService _intermediateToSpeakerService;
        private IInteVeevaService _inteVeevaService;
        private IHttpRequestService _httpRequestService;
        private ISTicketService _sticketService;

        public TestController(IServiceProvider serviceProvider)
        {
            _integrationDspotAppService = serviceProvider.GetService<IIntegrationDspotAppService>();
            _inteBpcsVendorAppService = serviceProvider.GetService<IInteBpcsVendorAppService>();
            _inteBpcsPAAppService = serviceProvider.GetService<IInteBpcsPAAppService>();
            _inteBpcsUncompletedPRVendorService = serviceProvider.GetService<IInteBpcsUncompletedPRVendorService>();
            _inteBpcsIntermediateService = serviceProvider.GetService<IInteBpcsIntermediateService>();
            _intermediateToSpeakerService = serviceProvider.GetService<IIntermediateToSpeakerService>();
            _inteVeevaService = serviceProvider.GetService<IInteVeevaService>();
            _httpRequestService = serviceProvider.GetService<IHttpRequestService>();
            _sticketService = serviceProvider.GetService<ISTicketService>();
        }

        [HttpPost]
        public async Task<IActionResult> TestRedisAsync()
        {
            var redisRepository = LazyServiceProvider.LazyGetService<IRedisRepository>();
            var trans = redisRepository.Database.CreateTransaction();
            _ = trans.HashSetAsync(Consts.RedisKey.UserLastLoginTime, "XXX", "123");
            _ = trans.HashDeleteAsync(Consts.RedisKey.UserLastLoginTime, "XXX");
            _ = trans.HashSetAsync(Consts.RedisKey.UserLastLoginTime, "XXX", "123");
            var committed = trans.Execute();

            return Ok(MessageResult.SuccessResult(committed));
        }

        [HttpPost]
        public async Task<OkObjectResult> TestSendReturnOrRollbackEmailAsync()
        {
            var approveService = LazyServiceProvider.LazyGetService<IApproveService>();
            string operatorUserName = string.Empty;
            if (CurrentUser.Id.HasValue)
            {
                var userId = CurrentUser.Id.Value;
                var identityUserRepository = LazyServiceProvider.LazyGetRequiredService<IIdentityUserRepository>();
                var operatorUser = await identityUserRepository.GetAsync(userId);
                operatorUserName = operatorUser?.Name ?? string.Empty;
            }

            await approveService.SendReturnOrRollbackEmail(CurrentUser.Id.Value, operatorUserName, "TestReason", "testcode1", DateTime.Now);

            return Ok(MessageResult.SuccessResult("OK"));
        }


        [HttpGet]
        public async Task<IActionResult> TestIntegrationDspotReport(string value)
        {
            var datas = await _integrationDspotAppService.SyncWholeProcessReport();
            return Ok(MessageResult.SuccessResult(datas));
        }

        [HttpPost]
        public async Task<IActionResult> TestIntegrationDspotTwoElements([FromBody] TwoElementsRequestDataDto request)
        {
            var result = await _integrationDspotAppService.TwoElementsValidate(request);

            return Ok(result);
        }

        /// <summary>
        /// 测试推送状态到CSS
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> TestPushApprovalToCSS([FromBody] PushSTicketApprovalResultDto request)
        {
            var result = await _sticketService.STicketApprovalResultPushAsync(request);
            return Ok(result);
        }

        [HttpGet]
        public async Task<IActionResult> TestInteBpcsVendor(string value)
        {
            var datas = await _inteBpcsVendorAppService.TestSyncNewVendor();
            return Ok(MessageResult.SuccessResult(datas));
        }

        [HttpGet]
        public async Task<IActionResult> TestInteBpcsVendorByAppId(string value, List<Guid> pushFinaIds)
        {
            var testId = "97D42EF2-CF1E-4981-6137-3A1202D45E33";
            testId = "8EA316D5-298E-755C-06AE-3A1217FA8EF3";
            //testId = "7B14C072-37A5-4844-8002-3A12035F46DE";
            testId = "58BEEDE3-389D-5976-5B66-3A12A64597EE";
            testId = "17447402-1D43-2995-77A6-3A12A64597EE";
            testId = "0BBC7C94-5AAE-8278-BF72-3A16C796EAD1";
            testId = string.IsNullOrWhiteSpace(value) ? testId : value;
            var datas = await _inteBpcsVendorAppService.SyncVendorByAppId(Guid.Parse(testId), pushFinaIds);
            return Ok(MessageResult.SuccessResult(data: datas));
        }

        [HttpGet]
        public async Task<IActionResult> TestSequenceNumUpdateToDB(string value, List<Guid> pushFinaIds)
        {
            var result = await LazyServiceProvider.LazyGetService<ISequenceNumService>().UpdateToDB();
            return Ok(MessageResult.SuccessResult(data: result));
        }

        [HttpGet]
        public async Task<IActionResult> TestInteBpcsPAByPAId(string value)
        {
            var testId = "E63CF625-E348-0C8D-F927-3A12FE84D6C7";
            testId = string.IsNullOrWhiteSpace(value) ? testId : value;
            var datas = await _inteBpcsPAAppService.SyncPAById(Guid.Parse(testId));
            return Ok(MessageResult.SuccessResult(data: datas));
        }

        [HttpGet]
        public async Task<IActionResult> TestInteBpcsUncompletedPRVendor()
        {
            var datas = await _inteBpcsUncompletedPRVendorService.SyncUncompletedPRVendor();
            return Ok(MessageResult.SuccessResult(data: datas));
        }

        [HttpGet]
        public async Task<IActionResult> TestProcessAndMoveErrors(string value)
        {
            var datas = await _inteBpcsVendorAppService.ProcessAndMoveErrors();
            return Ok(MessageResult.SuccessResult(datas));
        }

        [HttpGet]
        public async Task<IActionResult> TestInteBpcsVendorMove(string value)
        {
            var datas = await _inteBpcsVendorAppService.TestMoveEDIFile();
            return Ok(MessageResult.SuccessResult(datas));
        }

        [HttpGet]
        public async Task<IActionResult> TestInteBpcsSyncTablesTest(string value)
        {
            var datas = await _inteBpcsIntermediateService.SyncTablesTest();
            return Ok(MessageResult.SuccessResult(datas));
        }

        [HttpGet]
        public async Task<IActionResult> TestInteBpcsSyncTablesConfigs(string value)
        {
            var result = await _intermediateToSpeakerService.SyncTablesConfigs();
            return Ok(MessageResult.SuccessResult(data: result));
        }

        [HttpGet]
        public async Task<IActionResult> TestInteBpcsSyncTablesVendors(string value)
        {
            var result = await _intermediateToSpeakerService.SyncTablesVendors();
            return Ok(MessageResult.SuccessResult(data: result));
        }

        [HttpGet]
        public async Task<IActionResult> TestInteBpcsSyncTablesAPH(string value)
        {
            var result = await _intermediateToSpeakerService.SyncTablesAph();
            return Ok(MessageResult.SuccessResult(data: result));
        }

        [HttpGet]
        public async Task<IActionResult> TestInteBpcsSyncTablesAML(string value)
        {
            var result = await _intermediateToSpeakerService.SyncTablesAml();
            return Ok(MessageResult.SuccessResult(data: result));
        }

        [HttpGet]
        public async Task<IActionResult> TestInteBpcsSyncTablesGLH(string value)
        {
            var result = await _intermediateToSpeakerService.SyncTablesGlh();
            return Ok(MessageResult.SuccessResult(data: result));
        }

        [HttpGet]
        public async Task<IActionResult> TestInteBpcsSyncTables(string value)
        {
            var result = await _intermediateToSpeakerService.SyncTables();
            return Ok(MessageResult.SuccessResult(data: result));
        }

        [HttpGet]
        public IActionResult TestInteVeevaPushDcr(string value)
        {
            var result = LazyServiceProvider.LazyGetService<IInteVeevaService>().PushDcr(Guid.Parse(value));
            return Ok(string.IsNullOrWhiteSpace(result) ? MessageResult.SuccessResult() : MessageResult.FailureResult(message: result));
        }

        [HttpGet]
        public IActionResult TestInteVeevaPullDcrResult(string value)
        {
            var result = LazyServiceProvider.LazyGetService<IInteVeevaService>().PullDcrResult(Guid.Parse(value));
            return Ok(string.IsNullOrWhiteSpace(result) ? MessageResult.SuccessResult() : MessageResult.FailureResult(message: result));
        }

        [HttpGet]
        public IActionResult TestInteVeevaPullDcrResult1()
        {
            var result = _inteVeevaService.PullDcrResult();
            return Ok(string.IsNullOrWhiteSpace(result) ? MessageResult.SuccessResult() : MessageResult.FailureResult(message: result));
        }

        [HttpGet]
        public IActionResult TestInteVeevaResultHistoryOldData()
        {
            var result = _inteVeevaService.SaveVeevaResultHistoryOldData();
            return Ok(string.IsNullOrWhiteSpace(result) ? MessageResult.SuccessResult() : MessageResult.FailureResult(message: result));
        }

        [HttpPost]
        public async Task<IActionResult> TestCognitive(IFormFile file)
        {
            var cognitiveService = LazyServiceProvider.LazyGetService<ICognitiveService>();
            var result = await cognitiveService.GetCognizeResultAsync(file.GetAllBytes());

            return Ok(MessageResult.SuccessResult(result));
        }

        [HttpGet]
        public IActionResult Sha256(string srcString)
        {
            using SHA256 sha256 = SHA256.Create();
            byte[] bytes_sha256_in = Encoding.UTF8.GetBytes(srcString);
            byte[] bytes_sha256_out = sha256.ComputeHash(bytes_sha256_in);
            string str_sha256_out = BitConverter.ToString(bytes_sha256_out);
            str_sha256_out = str_sha256_out.Replace("-", "");
            return Ok(MessageResult.SuccessResult(str_sha256_out));
        }

        [HttpGet]
        public IActionResult DeleteHospitalFromPP(string hospitalId)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var result = dataverseService.DeleteHospitalFromPP(new List<Guid> { Guid.Parse(hospitalId), Guid.Parse("63facf34-5398-ef11-8a6b-0017fa06fba8") });
            return Ok(MessageResult.SuccessResult(result));
        }

        [HttpGet]
        public async Task<IActionResult> CallGetDataFromRedis()
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();

            //await dataverseService.GetCompanyAsync(stateCode: null);
            //await dataverseService.GetCompanyCurrencyAsync(stateCode: null);
            //await dataverseService.GetCompanyCurrencyExtAsync(stateCode: null);
            //await dataverseService.GetCompanyCurrencyInfoAsync(stateCode: null);

            //await dataverseService.GetCostNatureAsync(stateCode: null);
            //await dataverseService.GetOrganizations(stateCode: null);
            //await dataverseService.GetBuCodingCfgAsync(stateCode: null);
            //await dataverseService.GetCostcentersAsync(stateCode: null);
            await dataverseService.GetCoaMappingRuleAsync(stateCode: null);
            //await dataverseService.GetProductsAsync(stateCode: null);
            await dataverseService.GetOrgCostNatureRelationAsync();

            var result = await dataverseService.GetCompanyAndOrgRelationAsync(stateCode: null);

            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// bpm OM 测试
        /// </summary>
        /// <param name="bpmRequest"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> BpmOmPushAsync([FromQuery] BpmRequestDto bpmRequest)
        {
            var bpmOmAppService = LazyServiceProvider.LazyGetService<IBpmOmAppService>();
            var result = await bpmOmAppService.TestBpmOmPushAsync(bpmRequest);
            return Ok(result);
        }

        /// <summary>
        /// 同步讲者数据到Bpm
        /// </summary>
        /// <param name="vendorApplicationId"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult BpmSyncVendorWorker(Guid vendorApplicationId)
        {
            //同步数据到Bpm数据库
            BackgroundJob.Enqueue<BackgroundWorkers.Bpm.BpmSyncVendorWorker>(a => a.DoWorkAsync(vendorApplicationId));
            return Ok(MessageResult.SuccessResult());
        }

        /// <summary>
        /// 上传讲者附件到veeva的sftp服务器
        /// </summary>
        /// <param name="attachmentIdStrings"></param>
        /// <param name="requestId"></param>
        /// <returns></returns>
        [HttpGet]
        public IActionResult UploadToVeevaSftp(string attachmentIdStrings, Guid requestId)
        {
            var path = LazyServiceProvider.LazyGetService<IInteVeevaService>().UploadToVeevaSftp(attachmentIdStrings, requestId);
            return Ok(MessageResult.SuccessResult(path));
        }

        /// <summary>
        /// 测试推送dcr
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult DoPushDcrWorker()
        {
            //同步数据到Bpm数据库
            BackgroundJob.Enqueue<PushDcrWorker>(a => a.DoWorkAsync(CancellationToken.None));
            return Ok(MessageResult.SuccessResult());
        }

        /// <summary>
        /// 执行专业服务报税报表报表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult DoTaxReportWorker()
        {
            BackgroundJob.Enqueue<ProfessionalServiceTaxReportWorker>(a => a.DoWorkAsync(CancellationToken.None));
            return Ok(MessageResult.SuccessResult());
        }
        /// <summary>
        /// 测试推送dcr
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult DoPushCrossBuDcrWorker()
        {
            //同步数据到Bpm数据库
            BackgroundJob.Enqueue<PushDcrCrossBuWorker>(a => a.DoWorkAsync(CancellationToken.None));
            return Ok(MessageResult.SuccessResult());
        }

        /// <summary>
        /// 测试开关
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> TestOff()
        {
            var onOff = await LazyServiceProvider.LazyGetService<ISettingRepository>().FindAsync(SettingsConst.OmBpmInterfaceOnAndOff, "G", null);
            return Ok(MessageResult.SuccessResult(onOff));
        }

        /// <summary>
        /// 测试生成采购申请数据Job
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult TestPRApplicationReportWorker()
        {
            BackgroundJob.Enqueue<GeneratePRApplicationReportWorker>(a => a.DoWorkAsync(CancellationToken.None));
            return Ok(MessageResult.SuccessResult());
        }

        /// <summary>
        /// 读写库
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> TestReadWriteDbAsync()
        {
            var dbContext = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetDbContextAsync();
            var updateability = await dbContext.Database.SqlQueryRaw<UpdateabilityResult>("SELECT DATABASEPROPERTYEX(DB_NAME(), 'Updateability') AS Updateability").FirstOrDefaultAsync();

            return Ok(MessageResult.SuccessResult(updateability));
        }

        /// <summary>
        /// 只读库
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> TestReadonlyDbAsync()
        {
            var dbContext = await LazyServiceProvider.LazyGetService<IPurPRApplicationReadonlyRepository>().GetDbContextAsync();
            var updateability = await dbContext.Database.SqlQueryRaw<UpdateabilityResult>("SELECT DATABASEPROPERTYEX(DB_NAME(), 'Updateability') AS Updateability").FirstOrDefaultAsync();

            return Ok(MessageResult.SuccessResult(updateability));
        }

        /// <summary>
        /// 读写&只读库
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> TestRemixDbAsync()
        {
            var dbContext = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var pr1 = dbContext.FirstOrDefaultAsync();
            var readonlyDbContext = await LazyServiceProvider.LazyGetService<IPurPRApplicationReadonlyRepository>().GetQueryableAsync();
            var pr2 = readonlyDbContext.FirstOrDefaultAsync();

            Task.WaitAll(pr1, pr2);

            return Ok(MessageResult.SuccessResult(new { Pr1 = pr1, Pr2 = pr2 }));
        }

        class UpdateabilityResult
        {
            public string Updateability { get; set; }
        }

        /// <summary>
        /// 测试同步门店主数据到BPM
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult STicketStoreInfoSyncWorker()
        {
            //同步数据到Bpm数据库
            BackgroundJob.Enqueue<BackgroundWorkers.STicket.StoreInfoSyncWorker>(a => a.DoWorkAsync(CancellationToken.None));
            return Ok(MessageResult.SuccessResult());
        }

        /// <summary>
        /// 测试同步产品主数据到BPM
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult STicketProductInfoSyncWorker()
        {
            //同步数据到Bpm数据库
            BackgroundJob.Enqueue<BackgroundWorkers.STicket.ProductDetailSyncWorker>(a => a.DoWorkAsync(CancellationToken.None));
            return Ok(MessageResult.SuccessResult());
        }

        /// <summary>
        /// 测试同步客户主数据到BPM
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult STicketClientInfoSyncWorker()
        {
            //同步数据到Bpm数据库
            BackgroundJob.Enqueue<BackgroundWorkers.STicket.ClientInfoSyncWorker>(a => a.DoWorkAsync(CancellationToken.None));
            return Ok(MessageResult.SuccessResult());
        }

        /// <summary>
        /// 测试同步PP Region主数据到BPM
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult PPRegionSyncWorker()
        {
            //同步数据到Bpm数据库
            BackgroundJob.Enqueue<BackgroundWorkers.STicket.PPRegionSyncWorker>(a => a.DoWorkAsync(CancellationToken.None));
            return Ok(MessageResult.SuccessResult());
        }

        /// <summary>
        /// 测试同步PP CostCenter主数据到BPM
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult PPCostCenterSyncWorker()
        {
            //同步数据到Bpm数据库
            BackgroundJob.Enqueue<BackgroundWorkers.STicket.PPCostCenterSyncWorker>(a => a.DoWorkAsync(CancellationToken.None));
            return Ok(MessageResult.SuccessResult());
        }

        [HttpGet]
        public async Task<IActionResult> TestHttpGetToken()
        {
            var result = await _httpRequestService.GetSOITokenAsync();
            return Ok(result);
        }
        [HttpGet]
        public async Task<IActionResult> TestPushData()
        {
            var token = await _httpRequestService.GetSOITokenAsync();
            var mockRequest = new STicketPushSOIRequestDto
            {
                PoRequest = "郑激 Ji Zheng",
                PoPostDate = new DateTime(2024, 11, 7).ToString("yyyy-MM-dd"),
                PoFormNo = "S2411070001",
                CategoryCode = "CC24-1926",
                CategoryDes = "24_GTN_Chargeback_Food_Q2_员工内卖补差",
                CategoryOwner = "魏乐飞 LEFEI WEI",
                CategoryBUName = "AND",
                CategoryDept = "AND-AND A-Institutional-Trade",
                PoExpenseCategory = "批发商核销",
                PoCompany = "TRADING",
                PoBUDesc = "AND",
                PoProduct = "All products-食品",
                ItmWSName = "上药健康科学有限公司",
                //ItmWSCode = "20-620037",
                CustCode = "620037",
                //ItmPayType = "Hospital",
                PoRemark = "xxxx",
                PoAmount = 2075.4800m.ToString(),
                PoStatus = "生效",
                Details = new()
                    {
                        new STicketPushSOIDetailRequestDto
                        {
                            ItmNo = "1",
                            ItmCOA = "20.62.8524.14510.0700.00ZZ.00",
                            SettlementObjectCode = "W23160",
                            SettlementObjectName = "上药健康科学有限公司",
                            SettlementObjectType = "电商",
                            ExpenseNatureName = "Chargeback/S&D",
                            CostCenterName = "Sales Support Institutional Trade",
                            City = "北一10",
                            ItmDesc = "North,CSS结算周期：2024-04-01~2024-06-30",
                            ItmRegion = "north",
                            ItmPeriodStart = new DateTime(2024, 1, 1).ToString("yyyy-MM-dd"),
                            ItmPeriodEnd = new DateTime(2024, 1, 31).ToString("yyyy-MM-dd"),
                            ItmAmount = 2075.4800m.ToString(),
                            YYMM ="2024-06"
                        }
                    }
            };
            var appkey = "b7860726bfd14fa293679bc1d11e24e4";
            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            string encryptText = $"appkey={appkey}method=ReceiverEPODatatimestamp={timestamp}";
            var Sign = EncryptSHA3(encryptText);
            var requestData = new
            {
                appkey = appkey,
                accessToken = token,
                timestamp = timestamp,
                method = "ReceiverEPOData",
                Sign = Sign,
                data = new
                {
                    poRequest = "郑激 Ji Zheng",
                    poPostDate = "2024-11-07 00:00:00",
                    poFormNo = "S2411070001",
                    categoryCode = "CC24-1926",
                    categoryDes = "24_GTN_Chargeback_Food_Q2_员工内卖补差",
                    categoryOwner = "魏乐飞 LEFEI WEI",
                    categoryBUName = "AND",
                    categoryDept = "AND-AND A-Institutional-Trade",
                    poExpenseCategory = "批发商核销",
                    poCompany = "TRADING",
                    poBUDesc = "AND",
                    poProduct = "All products-食品",
                    itmWSName = "20-620037",
                    custCode = "620037",
                    poRemark = "这是一段头备注",
                    poAmount = "2098.2200",
                    poStatus = "生效",
                    details = new[]
       {
            new
            {
                itmNo = "1",
                itmCOA = "20.62.8524.14510.0700.00ZZ.00",
                settlementObjectCode = "DP066995",
                settlementObjectName = "上海闵行开市客贸易有限公司",
                settlementObjectType = "线下商超门店",
                expenseNatureid = "N01",
                expenseNatureName = "Chargeback/S&D",
                costCenterName = "Sales Support Institutional Trade",
                city = "北一10",
                itmDesc = "这是一段单据明细行备注",
                itmRegion = "north",
                itmPeriodStart = "2024-01-01",
                itmPeriodEnd = "2024-01-31",
                itmAmount = "1000.0000",
                YYMM = "2024-02"
            },
            new
            {
                itmNo = "2",
                itmCOA = "20.62.8524.14510.0700.00ZZ.00",
                settlementObjectCode = "DP066995",
                settlementObjectName = "上海闵行开市客贸易有限公司",
                settlementObjectType = "线下商超门店",
                expenseNatureid = "N01",
                expenseNatureName = "Chargeback/S&D",
                costCenterName = "Sales Support Institutional Trade",
                city = "北一10",
                itmDesc = "这是一段单据明细行备注",
                itmRegion = "north",
                itmPeriodStart = "2024-01-01",
                itmPeriodEnd = "2024-01-31",
                itmAmount = "1098.2200",
                YYMM = "2024-03"
            }
        }
                }
            };
            var json = JsonSerializer.Serialize(requestData);
            var result = await _httpRequestService.HttpPostAsync<MessageResult>(json, "https://ani-dynastytest.oneabbott.com/SOIWebAPI/api/EPO/ReceiverEPOData");
            //var str = await "https://ani-dynastytest.oneabbott.com/SOIWebAPI/api/EPO/ReceiverEPOData".WithHeader("Content-Type", "application/json; charset=utf-8").PostJsonAsync(json).ReceiveString();
            return Ok(result);
        }
        private static string EncryptSHA3(string text)
        {
            var hashAlgorithm = new Org.BouncyCastle.Crypto.Digests.Sha3Digest(512);
            // Choose correct encoding based on your usecase
            byte[] input = Encoding.ASCII.GetBytes(text);
            hashAlgorithm.BlockUpdate(input, 0, input.Length);
            byte[] result = new byte[64]; // 512 / 8 = 64
            hashAlgorithm.DoFinal(result, 0);
            string hashString = BitConverter.ToString(result);
            hashString = hashString.Replace("-", "").ToLowerInvariant();
            return hashString;
        }

        [HttpGet]
        [AllowAnonymous]
        public IActionResult GetClientIp()
        {
            var ip = string.Empty;
            // 优先从 X-Forwarded-For 获取真实 IP（适用于反向代理场景）
            var forwardedFor = HttpContext.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (!string.IsNullOrEmpty(forwardedFor))
            {
                // X-Forwarded-For 可能包含多个 IP，取第一个
                ip = forwardedFor.Split(',').FirstOrDefault();
                //if (IPAddress.TryParse(ip, out _))
                //{
                //    return ip;
                //}
            }
            else
                ip = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";

            // 否则使用 RemoteIpAddress
            return Ok(MessageResult.SuccessResult(ip));
        }
    }
}