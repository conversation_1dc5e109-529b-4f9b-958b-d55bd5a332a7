﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Abbott.SpeakerPortal.Contracts.Budget.Subbudget
{
    public class CreateSubBudgetMappingRequestDto
    {
        /// <summary>
        /// 子预算编号
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string Code { get; set; }

        /// <summary>
        /// 子预算Id
        /// </summary>
        [Required]
        public Guid SbuBudgetId { get; set; }

        /// <summary>
        /// 用户Id
        /// </summary>
        [Required]
        public Guid UserId { get; set; }

        /// <summary>
        /// 用户姓名
        /// </summary>
        [MaxLength(50)]
        public string UserName { get; set; }
    }
}
