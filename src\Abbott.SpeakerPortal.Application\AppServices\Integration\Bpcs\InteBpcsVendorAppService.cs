﻿using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Integration.Dspot;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Entities.Integration.Dspot;
using Abbott.SpeakerPortal.Entities.VendorApplicationFinancials;
using Abbott.SpeakerPortal.Entities.VendorApplicationOrgnizations;
using Abbott.SpeakerPortal.Entities.VendorApplicationPersonals;
using Abbott.SpeakerPortal.Entities.VendorApplications;
using Abbott.SpeakerPortal.Enums.Integration;
using Abbott.SpeakerPortal.Extension;
using Abbott.SpeakerPortal.Utils;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Renci.SshNet;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.AppServices.Integration.Dspot
{
    public class InteBpcsVendorAppService : SpeakerPortalAppService, IInteBpcsVendorAppService
    {
        /// <summary>
        /// The service provider
        /// </summary>
        private readonly IServiceProvider _serviceProvider;

        /// <summary>
        /// The logger
        /// </summary>
        private readonly ILogger<InteBpcsVendorAppService> _logger;

        /// <summary>
        /// The configuration
        /// </summary>
        private readonly IConfiguration _configuration;

        /// <summary>
        /// The dataverseService
        /// </summary>
        private readonly IDataverseService _dataverseService;

        private readonly IScheduleJobLogService _jobLogService;

        private readonly string _applicationInsightKey;

        private readonly string _sftpIp;
        private readonly int _sftpPort = 22;
        private readonly string _sftpUser;
        private readonly string _sftpPwd;

        public InteBpcsVendorAppService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _logger = serviceProvider.GetService<ILogger<InteBpcsVendorAppService>>();
            _configuration = serviceProvider.GetService<IConfiguration>();
            _dataverseService = serviceProvider.GetService<IDataverseService>();
            _jobLogService = serviceProvider.GetService<IScheduleJobLogService>();
            _applicationInsightKey = _configuration.GetValue<string>("ApplicationInsightKey");

            //加载初始化需要的数据
            _sftpIp = _configuration["Integrations:BPCS:SftpIP"];
            _sftpUser = _configuration["Integrations:BPCS:SftpUser"];
            _sftpPwd = _configuration["Integrations:BPCS:SftpPwd"];
        }

        public async Task<string> TestSyncNewVendor()
        {
            bool result = false;

            var testTxtPath = @"C:\Abbott\TestInteBPCS\txt_Vendor_EDI.txt";
            var sftpVendorFolder = @"/SP_ALL_TEST/AD_VND/WIP/";
            var sftpVendorPath = Path.Combine(sftpVendorFolder, Path.GetFileName(testTxtPath));

            try
            {
                //创建连接对象
                using (var client = new SftpClient(_sftpIp, _sftpPort, _sftpUser, _sftpPwd))
                {
                    client.Connect(); //连接

                    using (var fs = File.OpenRead(testTxtPath).CreateMemoryStream())
                    {
                        if (fs != null && fs.Length > 0)
                        {
                            client.BufferSize = 4 * 1024 * 1024;
                            fs.Seek(0, SeekOrigin.Begin);

                            client.UploadFile(fs, sftpVendorPath);
                            result = true;
                        }
                    }
                }

                //return result;
            }
            catch (Exception ex)
            {
                _logger.LogError($"SyncNewVendor() Exception: {ex}");
            }

            return null;
        }

        /// <summary>
        /// 根据供应商申请表的ID，组装EDI同步给BPCS
        /// </summary>
        /// <param name="vndAppId">供应商申请Id</param>
        /// <returns></returns>
        public async Task<string> SyncVendorByAppId(Guid vndAppId, IEnumerable<Guid> pushFinaIds = null)
        {
            if (vndAppId == null || vndAppId == Guid.Empty)
            {
                _logger.LogError($"SyncVendorByAppId() vndAppId is null");
                return "vndAppId is null";
            }

            try
            {
                var queryApp = (await LazyServiceProvider.LazyGetService<IVendorApplicationRepository>().GetQueryableAsync()).AsNoTracking();
                var appEntity = queryApp.FirstOrDefault(a => a.Id == vndAppId);
                if (appEntity == null)
                {
                    _logger.LogError($"SyncVendorByAppId() appEntity is null");
                    return "appEntity is null";
                }

                //查询要用到的Entities
                var (appFinaEntities, appPerEntity, appOrgEntity) = await GetAboutEntities(vndAppId, pushFinaIds);

                //没有 供应商申请财务信息 数据，则返回
                if (appFinaEntities?.Any() != true)
                {
                    _logger.LogError($"SyncVendorByAppId() appFinaEntities?.Any() != null");
                    return "appFinaEntities is null";
                }

                var seqNumService = _serviceProvider.GetService<ISequenceNumService>();
                //组装EDI文件的内容，一条财务信息组装一个文本文件
                Dictionary<string, string[]> dicUploadEdiInputs = new Dictionary<string, string[]>();
                List<EdiLogVendorMaster> listEdiLogVM = new List<EdiLogVendorMaster>();
                foreach (var item in appFinaEntities)
                {
                    var (ediLine, ediLogVM) = GetEdiLines(appEntity, item, appPerEntity, appOrgEntity);
                    dicUploadEdiInputs.Add(item.Id.ToString(), new string[2] { CalcSftpVndPath(seqNumService, item), ediLine });
                    listEdiLogVM.Add(ediLogVM);
                }

                if (dicUploadEdiInputs.Count < 1)
                {
                    _logger.LogError($"SyncVendorByAppId() dicUploadEdiInputs.Count < 1");
                    return "Upload Edi Inputs is null";
                }

                //推送到BPCS的SFTP
                var _sftpService = _serviceProvider.GetService<IInteBpcsSFTPService>();
                var uploadResult = _sftpService.UploadToBatch(dicUploadEdiInputs);
                //根据推送结果修改EdiLogVM.Status
                if (listEdiLogVM?.Any() == true)
                {
                    var ediLogStatus = uploadResult ? EdiSyncStatus.Pushed : EdiSyncStatus.Init;
                    listEdiLogVM.ForEach(a =>
                    {
                        a.Status = ediLogStatus;
                    });
                    //保存表 EdiLogVendorMaster
                    var repoVM = LazyServiceProvider.LazyGetService<IEdiLogVendorMasterRepository>();
                    repoVM.UpdateManyAsync(listEdiLogVM).GetAwaiter().GetResult();
                }
                if (!uploadResult)
                {
                    _logger.LogError($"uploadResult is false");
                    return "Upload to SFTP is failed";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"SyncVendorByAppId() Exception: {ex}");
                return ex.Message;
            }

            return null;
        }

        public async Task<string> TestMoveEDIFile()
        {
            var testTxtPath = @"C:\Abbott\TestInteBPCS\txt_Vendor_EDI.txt";
            var testTxtFileName = Path.GetFileName(testTxtPath);
            var sftpVendorFolder = @"/SP_ALL_TEST/AD_VND/WIP";
            var sftpVendorPath = Path.Combine(sftpVendorFolder, testTxtFileName);
            var sftpVendorArchiveErrorFolder = @"/SP_ALL_TEST/AD_VND/ARCHIVE/ERROR";

            try
            {
                using (var sftp = new SftpClient(_sftpIp, _sftpPort, _sftpUser, _sftpPwd))
                {
                    sftp.Connect();

                    // 确认源目录存在
                    if (sftp.Exists(sftpVendorFolder))
                    {
                        // 确认目标路径存在
                        if (!sftp.Exists(sftpVendorArchiveErrorFolder))
                        {
                            sftp.CreateDirectory(sftpVendorArchiveErrorFolder);
                        }

                        // 完整的源文件路径
                        var sourceFilePath = Path.Combine(sftpVendorFolder, testTxtFileName);
                        // 目标文件路径
                        var destinationFilePath = Path.Combine(sftpVendorArchiveErrorFolder, testTxtFileName);

                        // 移动文件
                        sftp.RenameFile(sourceFilePath, destinationFilePath);
                    }

                    sftp.Disconnect();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"MoveEDIFile() Exception: {ex}");
            }

            return null;
        }

        public async Task<string> ProcessAndMoveErrors()
        {
            var log = _jobLogService.InitSyncLog("ProcessAndMoveErrors");
            try
            {
                var _sftpService = _serviceProvider.GetService<IInteBpcsSFTPService>();
                //获取所有Vnd的L2目录，用于处理所有供应商L2级下的Error
                string folderLvl1 = _configuration["Integrations:BPCS:SftpFolderLvl1"];
                var pathsL2 = _sftpService.GetSub(folderLvl1, true, null, $"_{InteBpcsDataType.VND}");

                if (pathsL2?.Any() != true)
                {
                    return null;
                }

                string folderLvl3Error = _configuration["Integrations:BPCS:SftpFolderLvl3Error"];
                //循环每个L2目录，挨个处理
                foreach (var item in pathsL2)
                {
                    var itemL3Error = Path.Combine(item, folderLvl3Error);
                    //获取目录下的.txt文件
                    var ediFiles = _sftpService.GetSub(itemL3Error, false, null, $".txt");
                    if (ediFiles?.Any() != true)
                    {
                        continue;
                    }
                    var txtsLines = _sftpService.ReadTxtsLines(ediFiles);//每个文件可能有多行

                    //根据读到的结果，回写EdiLog日志表
                    await SaveEdiErrorMsg(txtsLines);

                    //移动目录
                    string folderLvl3Archive = _configuration["Integrations:BPCS:SftpFolderLvl3Archive"];
                    string folderLvl4Error = _configuration["Integrations:BPCS:SftpFolderLvl4Error"];
                    _sftpService.MoveEDIFilesByFolder(itemL3Error, Path.Combine(item, folderLvl3Archive, folderLvl4Error));
                }
                return null;
            }
            catch (Exception ex)
            {
                log.IsSuccess = false;
                log.Remark = ex.ToString();
                _logger.LogError($"ProcessAndMoveErrors() Exception: {ex}");
                return ex.Message;
            }
            finally
            {
                _jobLogService.SyncLog(log);
            }
        }

        private async Task SaveEdiErrorMsg(Dictionary<string, string[]> txtsLines)
        {
            if (txtsLines?.Any() != true)
            {
                return;
            }

            var edilogVmResult = new List<EdiLogVendorMaster>();
            //获取用于筛选EdiLogVM日志表的联合主键
            foreach (var item in txtsLines)
            {
                if (item.Value?.Any() != true)
                {
                    continue;
                }

                foreach (var iLine in item.Value)
                {
                    var lineContent = iLine.Split('|');
                    if (lineContent.Length < 3)
                    {
                        continue;
                    }
                    edilogVmResult.Add(new EdiLogVendorMaster
                    {
                        Vcmpny = lineContent[0].ToDecimal(),
                        Vendor = lineContent[1].ToDecimal(),
                        Errmsg = lineContent[lineContent.Length - 1]
                    });
                }
            }

            if (edilogVmResult?.Any() != true)
            {
                return;
            }

            //保存到表EdiLogVM.ErrMsg
            var repoEdiLogVm = LazyServiceProvider.LazyGetService<IEdiLogVendorMasterRepository>();
            var queryEdiLogVm = await repoEdiLogVm.GetQueryableAsync();
            var updateEntities = from item in edilogVmResult
                               join t in queryEdiLogVm on new { Vnd = item.Vendor, Cmp = item.Vcmpny } equals new { Vnd = t.Vendor, Cmp = t.Vcmpny }
                               select t;

            if (updateEntities?.Any() != true)
            {
                return;
            }

            foreach (var item in updateEntities)
            {
                item.TimeProcessFromBpcs = DateTime.Now;
                item.Errmsg = edilogVmResult.FirstOrDefault(a => a.Vendor == item.Vendor && a.Vcmpny == item.Vcmpny)?
                    .Errmsg ?? item.Errmsg;
                item.Status = string.IsNullOrWhiteSpace(item.Errmsg) ? EdiSyncStatus.Processed : EdiSyncStatus.ProcessedError;
            }
            await repoEdiLogVm.UpdateManyAsync(updateEntities);
        }

        private async Task<(List<VendorApplicationFinancial>, VendorApplicationPersonal, VendorApplicationOrganization)> GetAboutEntities(Guid vndAppId, IEnumerable<Guid> pushFinaIds = null)
        {
            var queryAppFina = (await LazyServiceProvider.LazyGetService<IVendorApplicationFinancialRepository>().GetQueryableAsync()).AsNoTracking();
            var queryAppPer = (await LazyServiceProvider.LazyGetService<IVendorApplicationPersonalRepository>().GetQueryableAsync()).AsNoTracking();
            var queryAppOrg = (await LazyServiceProvider.LazyGetService<IVendorApplicationOrganizationRepository>().GetQueryableAsync()).AsNoTracking();

            //供应商申请财务信息(1:N)
            var appFinaEntities = queryAppFina.Where(a => a.ApplicationId == vndAppId)
                .WhereIf(pushFinaIds?.Any() == true, a => pushFinaIds.Contains(a.Id))
                .ToList();

            //供应商申请个人信息(1:1)
            var appPerEntity = queryAppPer.FirstOrDefault(a => a.ApplicationId == vndAppId);

            //供应商申请机构信息(1:1)
            var appOrgEntity = queryAppOrg.FirstOrDefault(a => a.ApplicationId == vndAppId);

            return (appFinaEntities, appPerEntity, appOrgEntity);
        }

        private (string, EdiLogVendorMaster) GetEdiLines(Entities.VendorApplications.VendorApplication appEntity, VendorApplicationFinancial appFinaEntity, VendorApplicationPersonal appPerEntity, VendorApplicationOrganization appOrgEntity)
        {
            //要根据申请财务信息表来组装每一行
            if (appFinaEntity == null)
            {
                _logger.LogError($"GetEdiLines() appFinaEntity == null");
                return (null, null);
            }

            //组装实体 EdiLogVendorMaster，保存EDI日志表
            EdiLogVendorMaster ediLogVM = new EdiLogVendorMaster()
            {
                TimeSendToBpcs = DateTime.Now,
                VendorAppFinaId = appFinaEntity.Id
            };
            //银行卡号，身份证号解密
            appEntity.BankCardNo = AesHelper.Decryption(appEntity.BankCardNo, _applicationInsightKey);
            if(!string.IsNullOrWhiteSpace(appEntity.BankCardNo))
                appEntity.BankCardNo = appEntity.BankCardNo.Trim().Replace("\n", "").Replace("\r", "").Replace("\t", "");

            if (!string.IsNullOrEmpty(appPerEntity?.CardNo))
                appPerEntity.CardNo = AesHelper.Decryption(appPerEntity.CardNo, _applicationInsightKey);

            if(!string.IsNullOrWhiteSpace(appPerEntity?.CardNo))
                appPerEntity.CardNo = appPerEntity.CardNo.Trim().Replace("\n", "").Replace("\r", "").Replace("\t", "");

            //有部分字段，数据类型不匹配，单独赋值
            ediLogVM.Vcmpny = appFinaEntity.Company.ToDecimal();
            ediLogVM.Vendor = appFinaEntity.VendorCode.ToDecimal();
            //证件号+性别，传EDI时分开2个字段传，BPCS存DB合成一个字段存的，我们的EdiLog表也合成一个字段存
            var cardGender = CalcCardGender(appEntity, appPerEntity);
            ediLogVM.Vmxcrt = string.Join(' ', cardGender);
            var vndAds = CalcVndAds(appEntity, appOrgEntity).GetAwaiter().GetResult();
            ediLogVM.Vndad1 = vndAds[0];
            ediLogVM.Vndad2 = vndAds[1];
            ediLogVM.Vndad3 = vndAds[2];
            var vadd = CalcVndAddresses(appEntity).GetAwaiter().GetResult();
            ediLogVM.Vadd1 = vadd[0];
            ediLogVM.Vadd2 = vadd[1];
            //financial表DpoCategory字段从PP里重新读取
            if(string.IsNullOrWhiteSpace(appFinaEntity.DpoCategory))
                appFinaEntity.DpoCategory = CalcDpoCategory((int)appEntity.VendorType);

            var result = $"{(appFinaEntity.Company)}|"
                    + $"{appFinaEntity.VendorCode}|"
                    + $"{ediLogVM.Vtype = appFinaEntity.VendorType}|"
                    + $"{ediLogVM.Vpayty = appFinaEntity.PayType}|"
                    + $"{ediLogVM.Vndnam = CalcVndName(appEntity, appPerEntity, appOrgEntity)}|"

                    + $"{ediLogVM.Vcurr = appFinaEntity.Currency}|"
                    + $"{string.Join('|', cardGender)}|"
                    + $"{ediLogVM.Vcoun = appFinaEntity.CountryCode}|"
                    //机构地址需要按长度拆分；个人供应商就是分别填充在各个字段内，长度是不会超过30的
                    //返回3个字段：VNDAD1、VNDAD2、VNDAD3
                    + $"{string.Join('|', vndAds)}|"//这里有3个字段联系地址、职称（原医院）、职业证书编号
                    + $"{ediLogVM.Vpost = CalcPostCode(appEntity, appPerEntity, appOrgEntity)}|"

                    + $"{ediLogVM.Vcon = CalcContactName(appEntity, appOrgEntity)}|"
                    + $"{ediLogVM.Vphone = CalcTelNo(appEntity, appOrgEntity)}|"
                    + $"{ediLogVM.Vmvfax = null}|"//传真，直接留空
                    + $"{ediLogVM.Vemlad = CalcEmailAddress(appEntity, appPerEntity, appOrgEntity)}|"
                    //3075（优先）【BPCS集成】推送供应商/付款申请EDI时，目前没有去除字符串前后空格/换行符，会导致推送数据读取失败。
                    + $"{ediLogVM.Vldrm1 = appEntity.BankCode?.Trim()}|"

                    //3081（优先）【BPCS】币种为非RMB时：目前推送到BPCS的地方没有拼接SwiftCode
                    + $"{ediLogVM.Vldrm2 = appEntity.BankCardNo}" + (string.Equals(appFinaEntity.Currency, "RMB", StringComparison.CurrentCultureIgnoreCase) ? "|" : $" SWIFT CODE:{appEntity.BankSwiftCode}|")
                    + $"{ediLogVM.Vldcd1 = appFinaEntity.BankType}|"
                    + $"{ediLogVM.Vmbnka = appEntity.BankNo}|"
                    + $"{ediLogVM.Vmbank = appFinaEntity.AbbottBank}|"
                    + $"{ediLogVM.Vmref1 = appFinaEntity.Division}|"

                    + $"{ediLogVM.Vterms = appFinaEntity.PaymentTerm}|"
                    + $"{ediLogVM.Vmdcpx = appFinaEntity.Company}|"
                    + $"{string.Join('|', vadd)}|"//返回2个字段：VADD1和VADD2
                    + $"{ediLogVM.Vadd3 = CalcStandardHosDepId(appEntity).GetAwaiter().GetResult()}|"
                    + $"|||||"

                    + $"{ediLogVM.VPALS2 = appFinaEntity.DpoCategory}|"
                    + $"{ediLogVM.VPALS3 = appFinaEntity.SpendingCategory}|"
                    + $"{ediLogVM.VPALS4 = appFinaEntity.Division}|"
                    + "|||||||||||||||||||||||||||||||||"//加上Error字段，最后共34个空字段，Error字段之后不能有|，所以33根|。
                    ;

            //保存表 EdiLogVendorMaster
            var repoVM = LazyServiceProvider.LazyGetService<IEdiLogVendorMasterRepository>();
            var ediLogVMSaved = repoVM.InsertAsync(ediLogVM,true).GetAwaiter().GetResult();

            return (result, ediLogVMSaved);
        }

        private string CalcVndName(Entities.VendorApplications.VendorApplication appEntity, VendorApplicationPersonal appPerEntity, VendorApplicationOrganization appOrgEntity)
        {
            if (appEntity?.VendorType == null)
            {
                return null;
            }

            switch (appEntity.VendorType)
            {
                case Enums.VendorTypes.HCPPerson:
                case Enums.VendorTypes.NonHCPPerson:
                    return appPerEntity?.SPName;

                case Enums.VendorTypes.HCIAndOtherInstitutionsAR:
                case Enums.VendorTypes.NonHCIInstitutionalAP:
                    return appOrgEntity?.VendorName;

                default:
                    return null;
            }
        }

        private List<string> CalcCardGender(Entities.VendorApplications.VendorApplication appEntity, VendorApplicationPersonal appPerEntity)
        {
            var result = new List<string>() { null, null };
            if (appEntity?.VendorType == null)
            {
                return result;
            }

            switch (appEntity.VendorType)
            {
                case Enums.VendorTypes.HCPPerson:
                case Enums.VendorTypes.NonHCPPerson:
                    //拼接时，如果某一个为空，不拼空格
                    if (string.IsNullOrEmpty(appPerEntity?.CardNo) || appPerEntity?.Sex == null)
                    {
                        //return $"{appPerEntity?.CardNo}{(appPerEntity?.Sex == null ? "" : (appPerEntity?.Sex == Enums.Gender.Male ? "M" : "F"))}";
                        result[0] = (appPerEntity?.CardNo);
                        result[1] = (appPerEntity?.Sex == null ? "" : (appPerEntity?.Sex == Enums.Gender.Male ? "M" : "F"));
                        return result;
                    }
                    else
                    {
                        //return $"{appPerEntity?.CardNo} {(appPerEntity?.Sex == Enums.Gender.Male ? "M" : "F")}";
                        result[0] = (appPerEntity?.CardNo);
                        result[1] = (appPerEntity?.Sex == Enums.Gender.Male ? "M" : "F");
                        return result;
                    }

                default:
                    return result;
            }
        }

        private async Task<List<string>> CalcVndAds(Entities.VendorApplications.VendorApplication appEntity, VendorApplicationOrganization appOrgEntity)
        {
            //返回3个字段：VNDAD1、VNDAD2、VNDAD3
            //机构地址需要按长度拆分；个人供应商就是分别填充 <空>|职称|职业证书编号 在各个字段内，长度是不会超过30的

            var result = new List<string>() { "", "", "" };
            if (appEntity?.VendorType == null)
            {
                return result;
            }

            switch (appEntity.VendorType)
            {
                case Enums.VendorTypes.HCPPerson:
                case Enums.VendorTypes.NonHCPPerson:
                    //VNDAD2:职称：从PP的职称主数据表找职称
                    try
                    {
                        if (appEntity.PTId != null && appEntity.PTId != Guid.Empty)
                        {
                            var jobTiles = await _dataverseService.GetAllJobTiles();
                            result[1] = jobTiles?.Any() != true ? result[1] : jobTiles.FirstOrDefault(a => a.Id == appEntity.PTId.Value)?.Name;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"CalcVndAds() _dataverseService.GetAllJobTiles() Exception: {ex}");
                    }
                    //VNDAD3:职业证书编号：主表.CertificateCode
                    result[2] = appEntity.CertificateCode;
                    return result;

                case Enums.VendorTypes.HCIAndOtherInstitutionsAR:
                case Enums.VendorTypes.NonHCIInstitutionalAP:
                    result[0] = appOrgEntity?.RegCertificateAddress;
                    return result;

                default:
                    return result;
            }
        }

        private string CalcPostCode(Entities.VendorApplications.VendorApplication appEntity, VendorApplicationPersonal appPerEntity, VendorApplicationOrganization appOrgEntity)
        {
            if (appEntity?.VendorType == null)
            {
                return null;
            }

            switch (appEntity.VendorType)
            {
                case Enums.VendorTypes.HCPPerson:
                case Enums.VendorTypes.NonHCPPerson:
                    return appPerEntity?.PostCode;

                case Enums.VendorTypes.HCIAndOtherInstitutionsAR:
                case Enums.VendorTypes.NonHCIInstitutionalAP:
                    return appOrgEntity?.PostCode;

                default:
                    return null;
            }
        }

        private string CalcContactName(Entities.VendorApplications.VendorApplication appEntity, VendorApplicationOrganization appOrgEntity)
        {
            if (appEntity?.VendorType == null)
            {
                return null;
            }

            switch (appEntity.VendorType)
            {
                case Enums.VendorTypes.HCIAndOtherInstitutionsAR:
                case Enums.VendorTypes.NonHCIInstitutionalAP:
                    return appOrgEntity?.ContactName;

                default:
                    return null;
            }
        }

        private string CalcTelNo(Entities.VendorApplications.VendorApplication appEntity, VendorApplicationOrganization appOrgEntity)
        {
            if (appEntity?.VendorType == null)
            {
                return null;
            }

            switch (appEntity.VendorType)
            {
                case Enums.VendorTypes.HCPPerson:
                case Enums.VendorTypes.NonHCPPerson:
                    return appEntity?.HandPhone;

                case Enums.VendorTypes.HCIAndOtherInstitutionsAR:
                case Enums.VendorTypes.NonHCIInstitutionalAP:
                    return appOrgEntity?.ContactPhone;

                default:
                    return null;
            }
        }

        private string CalcEmailAddress(Entities.VendorApplications.VendorApplication appEntity, VendorApplicationPersonal appPerEntity, VendorApplicationOrganization appOrgEntity)
        {
            if (appEntity?.VendorType == null)
            {
                return null;
            }

            switch (appEntity.VendorType)
            {
                case Enums.VendorTypes.HCPPerson:
                case Enums.VendorTypes.NonHCPPerson:
                    return appPerEntity?.Email;

                case Enums.VendorTypes.HCIAndOtherInstitutionsAR:
                case Enums.VendorTypes.NonHCIInstitutionalAP:
                    return appOrgEntity?.ContactEmail;

                default:
                    return null;
            }
        }

        /// <summary>
        /// 返回分隔后的VADD1和VADD2
        /// 只需把医院名字放到ADD1字段 20240924
        /// </summary>
        /// <param name="appEntity"></param>
        /// <returns></returns>
        private async Task<List<string>> CalcVndAddresses(Entities.VendorApplications.VendorApplication appEntity)
        {
            //主表。所属医院，去PP的医院主数据表，找医院名字。分隔到VADD1和VADD2
            var result = new List<string> { "", "" };
            if (appEntity?.HospitalId == null)
            {
                return result;
            }

            try
            {
                var allHospitals = await _dataverseService.GetAllHospitals();
                result[0] = allHospitals?.Any() != true ? result[0] : allHospitals.FirstOrDefault(a => a.Id == appEntity.HospitalId.Value)?.Name;
            }
            catch (Exception ex)
            {
                _logger.LogError($"CalcVndAddresses() _dataverseService.GetAllHospitals() Exception: {ex}");
            }
            return result;
        }

        private async Task<string> CalcStandardHosDepId(Entities.VendorApplications.VendorApplication appEntity)
        {
            //appEntity.StandardHosDepId
            //主表。标准科室（StandardHosDepId），去PP的科室主数据表找科室名称
            string result = null;
            if (appEntity?.StandardHosDepId == null)
            {
                return result;
            }

            try
            {
                var allDepts = await _dataverseService.GetAllDepartments();
                result = allDepts?.Any() != true ? null : allDepts.FirstOrDefault(a => a.Id == appEntity.StandardHosDepId.Value)?.Name;
            }
            catch (Exception ex)
            {
                _logger.LogError($"CalcStandardHosDepId() _dataverseService.GetAllDepartments() Exception: {ex}");
            }
            return result;
        }

        private string CalcSftpVndPath(ISequenceNumService seqNumService, VendorApplicationFinancial appFina)
        {
            string folderLvl1 = _configuration["Integrations:BPCS:SftpFolderLvl1"];

            //获取公司的缩写名称
            var allCompany = _dataverseService.GetCompanyList().GetAwaiter().GetResult();
            string folderLvl2 = $"{(allCompany?.Any() != true ? "" : allCompany.FirstOrDefault(a => a.CompanyCode == appFina.Company)?.AbbrCode)}_{InteBpcsDataType.VND}";

            string folderLvl3 = _configuration["Integrations:BPCS:SftpFolderLvl3Upload"];

            //计算文件名，Vendor：V+7位序列号（从0000001开始）
            var fileName = seqNumService.GetNextFileNameAndIncrease(Enums.SequenceNumCategory.V);

            //正反斜杠都行；最前面要不要"/"也都行
            //return $"/{folderLvl1}/{folderLvl2}/{folderLvl3}/V0000001_{DateTime.Now.ToString("yyyy-MM-dd_HHmmss")}.TXT";
            return Path.Combine("/", folderLvl1, folderLvl2, folderLvl3, fileName);
        }

        private string CalcDpoCategory(int vendorType)
        {
            var vendorTypes = _dataverseService.GetVendorTypeCfgAsync().GetAwaiterResult();
            string dpoCategoryName = vendorTypes?.Where(a => a.VendorType == vendorType.ToString()).FirstOrDefault()?.DpoCategoryCode;
            return dpoCategoryName;
        }
    }
}