select  newid()  as spk_NexBPMCode,spk_BPMCode,spk_name,spk_chinesevalue,spk_englishvalue,spk_districtcode,spk_paymentmethod,flg 
into #spk_districtmasterdata
from spk_districtmasterdata_Tmp

IF OBJECT_ID(N'dbo.spk_districtmasterdata', N'U') IS NOT NULL
BEGIN
	update a 
	set a.spk_BPMCode        = b.spk_BPMCode
        ,a.spk_name          = b.spk_name
        ,a.spk_chinesevalue  = b.spk_chinesevalue
        ,a.spk_englishvalue  = b.spk_englishvalue
        ,a.spk_districtcode  = b.spk_districtcode
        ,a.spk_paymentmethod = b.spk_paymentmethod
        ,a.flg               = b.flg 
    from dbo.spk_districtmasterdata a
    join #spk_districtmasterdata b on a.spk_BPMCode = b.spk_BPMCode
    
    insert into dbo.spk_districtmasterdata
    select a.spk_NexBPMCode
          ,a.spk_BPMCode
          ,a.spk_name
          ,a.spk_chinesevalue
          ,a.spk_englishvalue
          ,a.spk_districtcode
          ,a.spk_paymentmethod
          ,a.flg 
	from #spk_districtmasterdata a
	where NOT EXISTS (SELECT * FROM dbo.spk_districtmasterdata where spk_BPMCode = a.spk_BPMCode)
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into dbo.spk_districtmasterdata from #spk_districtmasterdata
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END