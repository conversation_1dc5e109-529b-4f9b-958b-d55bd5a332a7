CREATE VIEW View_JoinedTicketApplications AS
SELECT 
    a.Id AS ApplicationId, 
    a.ApplicationCode,
    a.ApplyTime,
    a.Apply<PERSON><PERSON>,
    a.SubBudgetCode,
    a.SubBudgetDesc,
    a.CompanyName,
    a.ApplyUserBuName,
    a.Cityname,
    a.ProductName,
    -- 需要按照字段拼接CompanyCode+BUCode+CCCode+NatureCode+ProductCode+CityCode+currencycode
     CONCAT(
        a.CompanyCode, '.', 
        a.BUCode, '.', 
        a.CostCenterCode, '.',  
        b.ExpenseNatureCode, '.', 
        a.ProductCode, '.', 
        b.CityCode, '.', 
        a.CurrencyCode
    ) AS COA,
    a.ClientType,
    a.ClientCode,
    a.ClientName,
    a.CostCenter,
    a.Status,
    a.SubBudgetRegion,
    b.Id AS DetailId, 
    b.RowId,
    --b.DiscountCategory,
    --SettlementEntityCode，SettlementEntityName还在确认逻辑
    CASE 
        WHEN b.CreationTime = '1900-01-01 00:00:00.000' THEN b.StoreId 
        ELSE b.SettlementEntityCode 
    END AS SettlementEntityCode,
    CASE 
        WHEN b.CreationTime = '1900-01-01 00:00:00.000' THEN b.StoreName 
        ELSE b.SettlementEntityName 
    END AS SettlementEntityName,
    b.SettlementEntityHQCode,
    b.SettlementEntityHQName,
    b.SettlementEntityType,
    b.SettlementEntityChannel,
    b.SubTotalAmountRMB,
    b.PredictDate,
    b.ExpenseNature,
    b.CityCode,
    b.Remark,
    b.SettlementPeriodStart,
    b.SettlementPeriodEnd
FROM 
    STicketApplications a
LEFT JOIN 
    STicketApplicationDetails b 
ON 
    a.id = b.parentid;