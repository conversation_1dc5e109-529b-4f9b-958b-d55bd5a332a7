--[AbpUserRoles]
use PLATFORM_ABBOTT;
--drop table  #AbpUserRoles
select 
DISTINCT 
TRY_CONVERT(UNIQUEIDENTIFIER,[UserId])UserId,
[RoleId],
TRY_CONVERT(UNIQUEIDENTIFIER,[TenantId])[TenantId]
into #AbpUserRoles
from dbo.AbpUserRoles
where (RoleId <> null or RoleId <> '') and RoleId<>'N/A';

use Speaker_Portal

--update更新
update a 
set 
a.RoleId= TRY_CONVERT(UNIQUEIDENTIFIER,b.RoleId)
,a.TenantId= TRY_CONVERT(UNIQUEIDENTIFIER,b.TenantId)
from AbpUserRoles a
left join dbo.AbpRoles c
on a.RoleId=c.Name COLLATE SQL_Latin1_General_CP1_CI_AS
left join #AbpUserRoles  b
on a.UserId=b.UserId
;

--insert增量
insert dbo.AbpUserRoles
select 
TRY_CONVERT(UNIQUEIDENTIFIER, a.UserId)UserId,
b.id as RoleId,
TRY_CONVERT(UNIQUEIDENTIFIER,a.TenantId)TenantId
from #AbpUserRoles a
left join dbo.AbpRoles b
on a.RoleId=b.Name COLLATE SQL_Latin1_General_CP1_CI_AS
where 
not exists (select * from dbo.AbpUserRoles c where UserId=a.UserId)
and 
a.UserId is not null  and a.RoleId is not null;
;

