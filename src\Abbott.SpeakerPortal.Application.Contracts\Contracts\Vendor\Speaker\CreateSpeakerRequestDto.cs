﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

using Abbott.SpeakerPortal.Enums;

using Volo.Abp.Application.Dtos;

namespace Abbott.SpeakerPortal.Vendor.Speaker
{
    public class CreateSpeakerRequestDto
    {
        /// <summary>
        /// 操作类型
        /// </summary>
        [Required]
        public ApplicationTypes ApplicationType { get; set; }
        /// <summary>
        /// 讲者ID
        /// </summary>
        public Guid? ID { get; set; }
        /// <summary>
        ///申请人ID
        /// </summary>
        [Required]
        public Guid? ApplyUserId { get; set; }
        /// <summary>
        /// 申请人
        /// </summary>
        public string ApplyUserName { get; set; }
        /// <summary>
        ///部门ID
        /// </summary>
        [Required]
        public Guid? ApplyUserBu { get; set; }
        /// <summary>
        /// 部门
        /// </summary>
        public string ApplyUserBuName { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public string Status { get; set; }
        /// <summary>
        /// 讲者编码
        /// </summary>
        public string VendorCode { get; set; }
        /// <summary>
        /// 医生主键
        /// </summary>
        public string EpdId { get; set; }
        /// <summary>
        /// EPD医生手机号（AES256加密）
        /// 字符串数组
        /// </summary>
        public string Mobile { get; set; }
        /// <summary>
        /// 讲者名称
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string SPName { get; set; }
        /// <summary>
        /// 职称Id
        /// </summary>
        [Required]
        public Guid? PTId { get; set; }
        /// <summary>
        /// 职称
        /// </summary>
        public string PTIName { get; set; }
        /// <summary>
        /// 所属医院Id
        /// </summary>
        [Required]
        public Guid? HospitalId { get; set; }
        /// <summary>
        /// 所属医院
        /// </summary>
        public string HospitalName { get; set; }

        /// <summary>
        /// EPD医院Code，提交BU是EPD时填写
        /// </summary>
        public string EpdHospitalCode { get; set; }

        /// <summary>
        /// 标准科室Id
        /// </summary>
        [Required]
        public Guid? StandardHosDepId { get; set; }
        /// <summary>
        /// 标准科室
        /// </summary>
        public string StandardHosDepName { get; set; }
        /// <summary>
        /// 院内科室
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string HosDepartment { get; set; }
        /// <summary>
        /// 执业证书编码
        /// </summary>
        //[Required] 20241013 ytw bug#2290 改为非必填
        [MaxLength(50)]
        public string CertificateCode { get; set; }
        /// <summary>
        /// 学术级别
        /// </summary>
        [MaxLength(100)]
        public string AcademicLevel { get; set; }
        /// <summary>
        /// 学会任职
        /// </summary>
        public List<AcademicPositionDto> AcademicPositionJson { get; set; }
        /// <summary>
        /// 是否是院士
        /// </summary>
        public bool IsAcademician { get; set; }
        /// <summary>
        /// 原BPM学会任职
        /// </summary>
        public string FormerBPMAcademicPosition { get; set; }
        /// <summary>
        /// 财务信息
        /// </summary>
        public List<FinancialInformation> FinancialInformation { get; set; }
        /// <summary>
        /// 附件信息
        /// </summary>
        public List<AttachmentInformation> AttachmentInformation { get; set; }
    }
}
