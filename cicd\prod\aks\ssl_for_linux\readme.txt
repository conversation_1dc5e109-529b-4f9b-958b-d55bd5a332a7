pfx convert to cer:
openssl pkcs12 -nodes -nokeys -in speaker-portal-api.oneabbott.com.pfx  -out speaker-portal-api.oneabbott.com.cer
openssl pkcs12 -nodes -nocerts -in speaker-portal-api.oneabbott.com.pfx  -out speaker-portal-api.oneabbott.com.key

openssl pkcs12 -nodes -nokeys -in speaker-int-api.oneabbott.com.pfx  -out speaker-int-api.oneabbott.com.cer
openssl pkcs12 -nodes -nocerts -in speaker-int-api.oneabbott.com.pfx  -out speaker-int-api.oneabbott.com.key


cmd:
kubectl create secret tls tls-speaker-portal-api-secret -n default --key=speaker-portal-api.oneabbott.com.key --cert=speaker-portal-api.oneabbott.com.cer
kubectl create secret tls tls-speaker-int-api-secret -n default --key=speaker-int-api.oneabbott.com.key --cert=speaker-int-api.oneabbott.com.cer
