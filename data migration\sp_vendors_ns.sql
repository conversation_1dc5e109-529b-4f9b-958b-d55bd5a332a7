CREATE PROCEDURE dbo.sp_vendors_ns
AS 
BEGIN
	--CREATE TABLE PLATFORM_ABBOTT_Stg.dbo.spk_occupationaltitlemasterdata (
--	spk_NexBPMCode nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	spk_name nvarchar(255) COLLATE Chinese_PRC_CI_AS NULL,
--	spk_ordernumber nvarchar(255) COLLATE Chinese_PRC_CI_AS NULL,
--	spk_chinesevalue varchar(255) COLLATE Chinese_PRC_CI_AS NOT NULL,
--	spk_chineseremark nvarchar(255) COLLATE Chinese_PRC_CI_AS NULL,
--	spk_englishvalue nvarchar(255) COLLATE Chinese_PRC_CI_AS NULL,
--	spk_englishremark nvarchar(1000) COLLATE Chinese_PRC_CI_AS NULL
--);
--CREATE TABLE PLATFORM_ABBOTT_Stg.dbo.spk_departmentmasterdata (
--	spk_NexBPMCode nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	spk_name nvarchar(255) COLLATE Chinese_PRC_CI_AS NULL,
--	ownerid nvarchar(255) COLLATE Chinese_PRC_CI_AS NULL
--);
--
--CREATE TABLE PLATFORM_ABBOTT_Stg.dbo.spk_hospitalmasterdata (
--	spk_NexBPMCode nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	spk_name nvarchar(255) COLLATE Chinese_PRC_CI_AS NULL,
--	spk_hospitalcode nvarchar(255) COLLATE Chinese_PRC_CI_AS NULL,
--	spk_hospitalstatus varchar(255) COLLATE Chinese_PRC_CI_AS NOT NULL,
--	spk_ordernumber nvarchar(255) COLLATE Chinese_PRC_CI_AS NULL,
--	spk_chinesevalue nvarchar(255) COLLATE Chinese_PRC_CI_AS NULL,
--	spk_chineseremark nvarchar(1000) COLLATE Chinese_PRC_CI_AS NULL,
--	spk_englishvalue nvarchar(255) COLLATE Chinese_PRC_CI_AS NULL,
--	spk_englishremark nvarchar(1000) COLLATE Chinese_PRC_CI_AS NULL
--);

select 
DISTINCT 
a.Id,
ApplicationId,
VendorCode,
OpenId,
UnionId,
HandPhone,
VendorType,
BuCode,
Status,
EpdId,
MndId,
VendorId,
ApsPorperty,
CertificateCode,
isnull(spk_code,'') as SPLevel,
AcademicLevel,
AcademicPosition,
BankCode,
BankCardNo,
case when BankCity <> null or BankCity <> '' then concat(spk_provincialadministrativecode,',',spk_cityadministrativedivisioncode) else '' end as BankCity,
BankNo,
ExtraProperties,
ConcurrencyStamp,
CreationTime,
e.spk_NexBPMCode as CreatorId,
LastModificationTime,
LastModifierId,
IsDeleted,
DeleterId,
DeletionTime,
UserId,
f.spk_NexBPMCode as PTId,
g.spk_NexBPMCode as StandardHosDepId,
h.spk_NexBPMCode as HospitalId,
HosDepartment,
AttachmentInformation,
Description,
DraftVersion,
PaymentTerm,
BankCardImg,
DPSCheck,
SignedStatus,
SignedVersion,
HospitalName,
PTName,
StandardHosDepName,
[BankSwiftCode],
[IsAcademician],
[FormerBPMAcademicPosition],
[HCPType],
[RelationType]
into #Vendors
from PLATFORM_ABBOTT_Stg.dbo.Vendor_Tmp a 
left join PLATFORM_ABBOTT_Stg.dbo.spk_dictionary sd 
on a.SPLevel =sd.spk_Name 
left join PLATFORM_ABBOTT_Stg.dbo.spk_city c
on a.BankCity =c.spk_name
left join PLATFORM_ABBOTT_Stg.dbo.spk_province d
on c.spk_provincename = d.spk_name 
left join PLATFORM_ABBOTT_Stg.dbo.spk_staffmasterdata e
on a.CreatorId = e.bpm_id
left join PLATFORM_ABBOTT_Stg.dbo.spk_occupationaltitlemasterdata f
on a.PTId=f.spk_name
left join PLATFORM_ABBOTT_Stg.dbo.spk_departmentmasterdata g
on a.StandardHosDepId=g.spk_name
left join PLATFORM_ABBOTT_Stg.dbo.spk_hospitalmasterdata h
on a.HospitalId=h.spk_name;


WITH SplitAtta AS (
    SELECT 
        A.id AS A_id, -- 假设A表有id字段
        TRIM(value) AS AttachmentInformation
    FROM #Vendors a
    CROSS APPLY STRING_SPLIT(A.AttachmentInformation, ',')
),
File_id as (
	SELECT 
	    A_id,
	    B.id as B_id,
	    B.BPMId
	FROM 
	    SplitAtta
	JOIN PLATFORM_ABBOTT_Stg.dbo.Attachments_tmp B ON SplitAtta.AttachmentInformation = B.BPMId
)
select A_id,
STRING_AGG(cast(B_id as nvarchar(1000)), ',') WITHIN GROUP (ORDER BY B_id) AS B_id
into #AttachmentInformation
from File_id
group by A_id;

update a set a.AttachmentInformation=b.B_id from #vendors a
join #AttachmentInformation b
on a.id=b.A_id
PRINT(N'update AttachmentInformation'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'));

    --删除表
    IF OBJECT_ID(N'PLATFORM_ABBOTT_Stg.dbo.vendors ', N'U') IS NOT NULL
	BEGIN
		drop table PLATFORM_ABBOTT_Stg.dbo.vendors
		select *
        into PLATFORM_ABBOTT_Stg.dbo.vendors from #vendors
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into PLATFORM_ABBOTT_Stg.dbo.vendors from #vendors
    -- select * from #vendor_tbl
	  PRINT(N'落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END

END;

