CREATE PROCEDURE dbo.sp_VendorOrgnizations_ns
AS 
Begin
	select 
a.Id,
VendorId,
VendorName,
VendorOldName,
VendorEngName,
RegCertificateAddress,
PostCode,
ContactName,
ContactPhone,
ContactEmail,
WebSite,
RegisterDate,
spk_code as OrgType,
IssuingAuthority,
RegisterCode,
RegValidityStart,
RegValidityEnd,
spk_provincialadministrativecode as Province,
spk_cityadministrativedivisioncode as City,
Legal,
RegisterAmount,
BusinessAuthority,
BusinessScope,
LastYearSales,
KeyIndustry,
KeyClient,
Staffs,
Aptitudes,
ApplyReason,
ExtraProperties,
ConcurrencyStamp,
CreationTime,
g.spk_NexBPMCode as CreatorId,
LastModificationTime,
LastModifierId,
IsDeleted,
DeleterId,
DeletionTime,
Shareholder
into #VendorOrgnizations
from PLATFORM_ABBOTT_Dev.dbo.VendorOrgnizations_tmp a 
left join PLATFORM_ABBOTT_Dev.dbo.spk_dictionary b
on a.OrgType=b.spk_name
left join (select SUBSTRING(spk_name,1,2) name ,* from PLATFORM_ABBOTT_Dev.dbo.spk_city) E
on e.name=SUBSTRING(a.City,1,2) 
left join (select SUBSTRING(spk_name,1,2) name ,* from PLATFORM_ABBOTT_Dev.dbo.spk_province ) d
on SUBSTRING(a.Province,1,2)  = d.name
left join PLATFORM_ABBOTT_Dev.dbo.spk_staffmasterdata g
on a.CreatorId = g.bpm_id

--删除表
 IF OBJECT_ID(N'PLATFORM_ABBOTT_Dev.dbo.VendorOrgnizations ', N'U') IS NOT NULL
 BEGIN
		drop table PLATFORM_ABBOTT_Dev.dbo.VendorOrgnizations
		select *
        into PLATFORM_ABBOTT_Dev.dbo.VendorOrgnizations from #VendorOrgnizations
 PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
 END
 ELSE
 BEGIN
--落成实体表
 select *
    into PLATFORM_ABBOTT_Dev.dbo.VendorOrgnizations from #VendorOrgnizations
 -- select * from #vendor_tbl
 PRINT(N'落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
 END

END
