﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Abbott.SpeakerPortal.Contracts.Budget
{
    public class AnalyzeExcelResponseDto : CreateMasterBudgetExcelDto
    {
        /// <summary>
        /// 行号
        /// </summary>
        public int No { get; set; }
        /// <summary>
        /// 整型年度,数字类型，前端不需要展示
        /// </summary>
        public int? Year { get; set; }
        /// <summary>
        /// BuId
        /// </summary>
        public Guid? BuId { get; set; }
        /// <summary>
        /// 负责人姓名
        /// </summary>
        public string OwnerName { get; set; }
        /// <summary>
        /// 负责人Id
        /// </summary>
        public Guid? OwnerId { get; set; }
        /// <summary>
        /// 预算金额,数字类型，前端不需要展示
        /// </summary>
        public decimal? BudgetAmount { get; set; }
        /// <summary>
        /// 是否固定资产,bool类型，前端不需要展示
        /// </summary>
        public bool? Capital { get; set; }
        /// <summary>
        /// 错误信息
        /// </summary>
        public string Message { get; set; }
    }
}
