using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading.Tasks;

using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Dataverse;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Enums;

using Volo.Abp.Application.Dtos;

using static Abbott.SpeakerPortal.Enums.Purchase;

namespace Abbott.SpeakerPortal.Contracts.Purchase.PurPRApplication
{
    public partial interface IPurPRApplicationService
    {
        /// <summary>
        /// 获取采购申请列表
        /// </summary>
        /// <param name="request"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        Task<PagedResultDto<GetPRApplicationListResponse>> GetPRApplicationsAsync(GetPRApplicationListRequest request, PurPRApplicationStatus? status = null);

        /// <summary>
        /// 导出采购申请列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<Stream> ExportPRApplicationAsync(GetPRApplicationListRequest request);

        /// <summary>
        /// 获取用于选择的讲者列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<PagedResultDto<GetSpeakerListForChoiceResponseDto>> GetSpeakerForChoiceAsync(GetSpeakerListForChoiceRequestDto request);

        /// <summary>
        /// 获取用于选择的非讲者供应商列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<PagedResultDto<GetNonSpeakerListForChoiceResponseDto>> GetNonSpeakerForChoiceAsync(GetNonSpeakerListForChoiceRequestDto request);

        /// <summary>
        /// 获取员工类型的供应商
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<PagedResultDto<GetPersonVendorForChoiceResponseDto>> GetPersonVendorForChoiceAsync(GetPersonVendorForChoiceRequestDto request);

        /// <summary>
        /// 获取Psa例外剩余次数和金额
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<GetSurplusPsaExtraLimitResponseDto> GetPsaExtraSurplusLimitAsync(GetSurplusPsaExtraLimitRequestDto request);

        /// <summary>
        /// 根据组织Id获取关联的成本中心
        /// </summary>
        /// <param name="orgId"></param>
        /// <returns></returns>
        Task<IEnumerable<KeyValuePair<Guid, string>>> GetCostcenterByOrgAsync(Guid? orgId);

        /// <summary>
        /// 获取子预算列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<PagedResultDto<GetSubBudgetsResponseDto>> GetSubBudgetInfosAsync(GetSubBudgetsRequestDto request);

        /// <summary>
        /// 获取预算信息
        /// </summary>
        /// <param name="subBudgetId"></param>
        /// <returns></returns>
        Task<GetBudgetInfoResponse> GetSubBudgetInfoAsync(Guid subBudgetId);

        /// <summary>
        /// 获取消费大类
        /// </summary>
        /// <param name="orgId">机构Id</param>
        /// <param name="isCapital">是否资产，由预算信息带出</param>
        /// <returns></returns>
        Task<IEnumerable<ConsumeCategorySponsorshipTypeResponseDto>> GetConsumeCategoryAsync(Guid orgId, bool isCapital);

        /// <summary>
        /// 根据消费大类、组织、成本中心、付款方式获取费用性质
        /// </summary>
        /// <param name="consumeId">消费大类</param>
        /// <param name="orgId">组织</param>
        /// <param name="costcenterId">成本中心</param>
        /// <param name="productIds">产品Id集合</param>
        /// <param name="payMethod">付款方式</param>
        /// <returns></returns>
        Task<IEnumerable<CostNatureDto>> GetCostNatureAsync(Guid consumeId, Guid orgId, Guid costcenterId, IEnumerable<Guid> productIds, PayMethods payMethod);

        /// <summary>
        /// 获取主会场PR选择列表
        /// </summary>
        /// <returns></returns>
        Task<PagedResultDto<GetMainVenuePRListResponse>> GetMainVenuePRsAsync(GetMainVenuePRListRequest request);

        /// <summary>
        /// 创建采购申请单
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<MessageResult> CreatePRApplicationAsync(CreatePRApplicationRequest request);

        /// <summary>
        /// 获取采购申请单详情
        /// </summary>
        /// <param name="id"></param>
        /// <param name="taskId"></param>
        /// <returns></returns>
        Task<GetPRApplicationResponse> GetPRApplicationAsync(Guid id, Guid? taskId = null);

        /// <summary>
        /// 修改采购申请单
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<MessageResult> UpdatePRApplicationAsync(UpdatePRApplicationRequest request);

        /// <summary>
        /// 删除采购申请单
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<MessageResult> DeletePRApplicationAsync(Guid id);

        /// <summary>
        /// 提交采购申请
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<MessageResult> SubmitPRApplicationAsync(UpdatePRApplicationRequest request);

        /// <summary>
        /// 获取某个PR单的补录文件列表
        /// </summary>
        /// <param name="Id">The identifier.</param>
        /// <returns></returns>
        Task<MessageResult> GetAdditionFilesAsync(Guid Id);

        /// <summary>
        /// 补录文件
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        Task<MessageResult> AdditionFilesAsync(AdditionFilesRequestDto request);

        /// <summary>
        /// 采购推送
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        Task<MessageResult> PurchasePushAsync(List<PRDetailPushRequestDto> request);

        /// <summary>
        /// 我的采购推送退回
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<MessageResult> RollbackAsync(RollbackRequestDto request);


        /// <summary>
        /// 任务中心-我发起的-指定的PR单-明细列表
        /// </summary>
        /// <param name="request">The identifier.</param>
        /// <returns></returns>
        Task<MessageResult> GetPRDetailListForSpecificPRAsync(GetPRDetailListForSpecificPRRequestDto request);

        Task<IEnumerable<KeyValuePair<Guid, string>>> GetStaffByPositionAsync(Guid dept, PositionType position, WorkflowTypeName? workflowType);

        /// <summary>
        /// 判断是否所有的AP行都已推送，AR行都已确认
        /// </summary>
        /// <param name="prApplicationId"></param>
        /// <returns></returns>
        Task<bool> IsAllPrDetailPushedAndConfirmed(Guid prApplicationId);

        /// <summary>
        /// 根据CompanyId查询PP里的采购推送人
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="isAssistant"></param>
        /// <returns></returns>
        List<GetPurchasersResponseDto> GetPurchasers(Guid? companyId = null, bool isAssistant = false);

        /// <summary>
        /// 获取有后续有效流程的PR明细行Id
        /// </summary>
        /// <param name="prId"></param>
        /// <returns></returns>
        Task<IEnumerable<Guid>> GetHasNextFlowPrDetailAsync(Guid prId);

        /// <summary>
        /// 根据成本中心获取活动类型
        /// </summary>
        /// <param name="costcenterId"></param>
        /// <returns></returns>
        Task<IEnumerable<KeyValuePair<string, string>>> GetActiveTypeByCostcenterAsync(Guid costcenterId);

        /// <summary>
        /// 关闭采购申请单
        /// </summary>
        /// <returns></returns>
        Task ClosePRApplication();

        /// <summary>
        /// 判断是否可以作废OM申请
        /// 如果PR包含的行有如下数据，进行作废时需要报错禁止提交作废：
        ///1、AR行，收货状态Not in（2，3）则不准作废；
        ///2、AP行（已推送），未发起收货申请 或 者收货状态Not in（2，3）则不准作废；
        ///3、AP行（未推送），如果有还在审批中的waiver/justification，则不准作废；
        /// </summary>
        /// <param name="prId"></param>
        /// <returns></returns>
        Task<bool> CanDeprecteOnlineMeetingAsync(Guid prId);

        /// <summary>
        /// 根据消费大类和费用性质获取讲者身份类型
        /// </summary>
        /// <param name="consumeId"></param>
        /// <param name="costnatureId"></param>
        /// <returns></returns>
        Task<IEnumerable<DictionaryDto>> GetIdentityTypesAsync(Guid consumeId, Guid costnatureId);
    }
}
