﻿using Abbott.SpeakerPortal.Enums;
using MiniExcelLibs.Attributes;
using System;
using System.Collections.Generic;
using System.Text;

namespace Abbott.SpeakerPortal.Contracts.Budget.Subbudget
{
    public class TransferImportMessageDto
    {
        /// <summary>
        /// 序号
        /// </summary>
        public int No { get; set; }
        /// <summary>
        /// 被调拨预算
        /// </summary>
        public string OriginalCode { get; set; }
        /// <summary>
        /// 调拨预算
        /// </summary>
        public string TransferCode { get; set; }
        /// <summary>
        /// 可用金额
        /// </summary>
        public decimal? AvailableAmount { get; set; }
        /// <summary>
        /// 调拨金额
        /// </summary>
        public decimal? TransferAmount { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 来源月份
        /// </summary>
        public Month? OriginalMonth { get; set; }
        /// <summary>
        /// 调拨月份
        /// </summary>
        public Month? TransferMonth { get; set; }
        /// <summary>
        /// 错误信息
        /// </summary>
        public string Message {  get; set; }
    }
}
