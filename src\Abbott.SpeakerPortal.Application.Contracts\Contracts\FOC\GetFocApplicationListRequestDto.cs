﻿using System;
using Abbott.SpeakerPortal.Dtos;
using Abbott.SpeakerPortal.Enums;

namespace Abbott.SpeakerPortal.Contracts.FOC
{
    public class GetFocApplicationListRequestDto : PagedDto
    {
        /// <summary>
        /// 申请单号
        /// </summary>
        public string ApplicationCode { get; set; }

        /// <summary>
        /// 申请部门名称
        /// </summary>
        public string ApplyDeptName { get; set; }

        /// <summary>
        /// 成本中心Id
        /// </summary>
        public Guid? CostCenterId { get; set; }

        /// <summary>
        /// 子预算编号
        /// </summary>
        public string SubBudgetCode { get; set; }

        /// <summary>
        /// 品牌信息编码
        /// </summary>
        public string ProductMCode { get; set; }

        /// <summary>
        /// 申请人Id
        /// </summary>
        public Guid? ApplyUserId { get; set; }

        /// <summary>
        /// 主预算编号
        /// </summary>
        public string BudgetCode { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public FOCStatus? Status { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndDate { get; set; }
    }
}
