﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Abbott.SpeakerPortal.Contracts.Agent.Transferee
{
    public class SetTransfereeUserRequestDto
    {
        /// <summary>
        /// 单据列表
        /// </summary>
        public List<ApplicationInfo> ApplicationItems { get; set; }
        /// <summary>
        /// 转办人Id
        /// </summary>
        public Guid TransfereeUserId { get; set; }
        /// <summary>
        /// 转办人姓名
        /// </summary>
        public string TransfereeUserName { get; set; }
    }

    /// <summary>
    /// 单据信息
    /// </summary>
    public class ApplicationInfo
    {
        /// <summary>
        /// 单据编号
        /// </summary>
        public string ApplicationCode { get; set; }
        /// <summary>
        /// 单据记录Id
        /// </summary>
        public Guid Id { get; set; }
    }
}
