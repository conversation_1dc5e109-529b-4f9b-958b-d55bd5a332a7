SELECT
 [Id]
,[ApplicationId]
,[VendorCode]
,[OpenId]
,[UnionId]
,[HandPhone]
,[VendorType]
,[BuCode]
,[Status]
,NULL [BpcsId]
,[EpdId]
,[MndId]
,[VendorId]
,[ApsPorperty]
,[CertificateCode]
,[SPLevel]
,[AcademicLevel]
,[AcademicPosition]
,[BankCode]
,[BankCardNo]
,[BankCity]
,[BankNo]
,[ExtraProperties]
,[ConcurrencyStamp]
,convert(datetime,SUBSTRING([CreationTime],1,8),112) [CreationTime]
,[CreatorId]
,CASE WHEN [LastModificationTime] IS NOT NULL
	THEN null
	ELSE [LastModificationTime]
	end [LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,CASE WHEN [DeletionTime] IS NOT NULL
	THEN null
	ELSE [DeletionTime]
	end [DeletionTime]
,[UserId]
,[PTId]
,[StandardHosDepId]
,[HospitalId]
,[HosDepartment]
,[AttachmentInformation]
,[Description]
,[DraftVersion]
,[PaymentTerm]
,[BankCardImg]
,[DPSCheck]
,[SignedStatus]
,[SignedVersion]
,[HospitalName]
,[PTName]
,[StandardHosDepName]
,NULL [BankSwiftCode]
,NULL [IsAcademician]
,NULL [FormerBPMAcademicPosition]
INTO #Vendors
FROM PLATFORM_ABBOTT_STG.dbo.Vendors
;



USE Speaker_Portal_STG;

UPDATE a 
SET 
 a.[Id] = TRY_CONVERT(UNIQUEIDENTIFIER, b.[Id])
,a.[ApplicationId] = TRY_CONVERT(UNIQUEIDENTIFIER, b.[ApplicationId])
,a.[VendorCode] = b.[VendorCode]
,a.[OpenId] = b.[OpenId]
,a.[UnionId] = b.[UnionId]
,a.[HandPhone] = b.[HandPhone]
,a.[VendorType] = b.[VendorType]
,a.[BuCode] = b.[BuCode]
,a.[Status] = b.[Status]
,a.[EpdId] = b.[EpdId]
,a.[MndId] = b.[MndId]
,a.[VendorId] = b.[VendorId]
,a.[ApsPorperty] = b.[ApsPorperty]
,a.[CertificateCode] = b.[CertificateCode]
,a.[SPLevel] = b.[SPLevel]
,a.[AcademicLevel] = b.[AcademicLevel]
,a.[AcademicPosition] = b.[AcademicPosition]
,a.[BankCode] = b.[BankCode]
,a.[BankCardNo] = b.[BankCardNo]
,a.[BankCity] = b.[BankCity]
,a.[BankNo] = b.[BankNo]
,a.[ExtraProperties] = b.[ExtraProperties]
,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = TRY_CONVERT(UNIQUEIDENTIFIER, b.[CreatorId])
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = TRY_CONVERT(UNIQUEIDENTIFIER, b.[LastModifierId])
,a.[IsDeleted] = b.[IsDeleted]
,a.[DeleterId] = TRY_CONVERT(UNIQUEIDENTIFIER, b.[DeleterId])
,a.[DeletionTime] = b.[DeletionTime]
,a.[UserId] = TRY_CONVERT(UNIQUEIDENTIFIER, b.[UserId])
,a.[PTId] = TRY_CONVERT(UNIQUEIDENTIFIER, b.[PTId])
,a.[StandardHosDepId] = TRY_CONVERT(UNIQUEIDENTIFIER, b.[StandardHosDepId])
,a.[HospitalId] = TRY_CONVERT(UNIQUEIDENTIFIER, b.[HospitalId])
,a.[HosDepartment] = b.[HosDepartment]
,a.[AttachmentInformation] = b.[AttachmentInformation]
,a.[Description] = b.[Description]
,a.[DraftVersion] = b.[DraftVersion]
,a.[PaymentTerm] = b.[PaymentTerm]
,a.[BankCardImg] = b.[BankCardImg]
,a.[DPSCheck] = b.[DPSCheck]
,a.[SignedStatus] = b.[SignedStatus]
,a.[SignedVersion] = b.[SignedVersion]
,a.[HospitalName] = b.[HospitalName]
,a.[PTName] = b.[PTName]
,a.[StandardHosDepName] = b.[StandardHosDepName]
FROM dbo.Vendors a
left join #Vendors  b
ON a.id=b.id;


INSERT INTO dbo.Vendors
--([Id]
--,[ApplicationId]
--,[VendorCode]
--,[OpenId]
--,[UnionId]
--,[HandPhone]
--,[VendorType]
--,[BuCode]
--,[Status]
--,[BpcsId]
--,[EpdId]
--,[MndId]
--,[VendorId]
--,[ApsPorperty]
--,[CertificateCode]
--,[SPLevel]
--,[AcademicLevel]
--,[AcademicPosition]
--,[BankCode]
--,[BankCardNo]
--,[BankCity]
--,[BankNo]
--,[ExtraProperties]
--,[ConcurrencyStamp]
--,[CreationTime]
--,[CreatorId]
--,[LastModificationTime]
--,[LastModifierId]
--,[IsDeleted]
--,[DeleterId]
--,[DeletionTime]
--,[UserId]
--,[PTId]
--,[StandardHosDepId]
--,[HospitalId]
--,[HosDepartment]
--,[AttachmentInformation]
--,[Description]
--,[DraftVersion]
--,[PaymentTerm]
--,[BankCardImg]
--,[DPSCheck]
--,[SignedStatus]
--,[SignedVersion]
--,[HospitalName]
--,[PTName]
--,[StandardHosDepName]
--,[BankSwiftCode]
--,[IsAcademician]
--,[FormerBPMAcademicPosition]
--)
SELECT
 TRY_CONVERT(UNIQUEIDENTIFIER, [Id]) [Id]
,[ApplicationId]
,[VendorCode]
,[OpenId]
,[UnionId]
,[HandPhone]
,[VendorType]
,[BuCode]
,[Status]
,[BpcsId]
,[EpdId]
,[MndId]--目标表长度改为[nvarchar](36) NULL
,[VendorId]
,[ApsPorperty]
,[CertificateCode]
,[SPLevel]
,[AcademicLevel]
,[AcademicPosition]
,[BankCode]
,[BankCardNo]
,[BankCity]
,[BankNo]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CreatorId])
,[LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [LastModifierId])
,[IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, [DeleterId])
,[DeletionTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, NEWID()) [UserId] --中间层为空
,TRY_CONVERT(UNIQUEIDENTIFIER, [PTId])
,TRY_CONVERT(UNIQUEIDENTIFIER, [StandardHosDepId])
,TRY_CONVERT(UNIQUEIDENTIFIER, [HospitalId])
,[HosDepartment] --目标表长度改为[nvarchar](100) NULL
,[AttachmentInformation] --目标表长度改为[nvarchar](max) NULL
,[Description]
,[DraftVersion]
,[PaymentTerm]
,[BankCardImg]
,[DPSCheck]
,[SignedStatus]
,[SignedVersion]
,[HospitalName]
,[PTName]
,[StandardHosDepName]
,NULL [BankSwiftCode]
,'' [IsAcademician]
,NULL [FormerBPMAcademicPosition]
FROM #Vendors a
WHERE not exists (select * from dbo.Vendors where id=a.id);



--select [MndId],[HosDepartment],[AttachmentInformation],* from #Vendors

--alter table Speaker_Portal_STG.dbo.Vendors
--alter column [MndId] [nvarchar](36) NULL
--;
--alter table Speaker_Portal_STG.dbo.Vendors
--alter column [HosDepartment] [nvarchar](100) NULL
--;
--alter table Speaker_Portal_STG.dbo.Vendors
--alter column [AttachmentInformation] [nvarchar](max) NULL
--;