SELECT
 TRY_CONVERT(UNIQUEIDENTIFIER, [Id]) [Id]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([POApplicationId],'00000000-0000-0000-0000-000000000000')) [POApplicationId]
,[Content]
,iif(Quantity is null,0,Quantity) AS [Quantity]
,[Unit]
,iif(UnitPrice is null,0,UnitPrice) AS [UnitPrice]
,[InvoiceType]
,iif(TotalAmount is null,0,TotalAmount) AS [TotalAmount]
,[TaxRate]
,iif(TaxAmount is null,0,TaxAmount) AS [TaxAmount]
,[ApplicationCode]
,'{}' AS [ExtraProperties]
,'NULL' AS [ConcurrencyStamp]
,GETDATE() AS [CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CreatorId]) [CreatorId]
,[LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [LastModifierId]) [LastModifierId]
,0 AS [IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, [DeleterId]) [DeleterId]
,[DeletionTime]
,iif(TotalAmountNoTax is null,0,TotalAmountNoTax) AS [TotalAmountNoTax]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([PRDetailId],'00000000-0000-0000-0000-000000000000')) [PRDetailId]
,iif(Price is null,0,Price) AS [Price]
,iif(RowNo is null,0,RowNo) AS [RowNo]
--,[PORowNo]目标库无此字段
INTO #PurPOApplicationDetails
FROM (SELECT DISTINCT *,ROW_NUMBER() OVER(PARTITION BY Id ORDER BY Id) RK FROM PLATFORM_ABBOTT_STG.dbo.PurPOApplicationDetails)a
WHERE RK = 1

--drop table #PurPOApplicationDetails

--select * from PLATFORM_ABBOTT_STG.dbo.PurPOApplicationDetails

USE Speaker_Portal_STG;

UPDATE a
SET 
 a.[Id] = b.[Id]
,a.[POApplicationId] = b.[POApplicationId]
,a.[Content] = b.[Content]
,a.[Quantity] = b.[Quantity]
,a.[Unit] = b.[Unit]
,a.[UnitPrice] = b.[UnitPrice]
,a.[InvoiceType] = b.[InvoiceType]
,a.[TotalAmount] = b.[TotalAmount]
,a.[TaxRate] = b.[TaxRate]
,a.[TaxAmount] = b.[TaxAmount]
,a.[ApplicationCode] = b.[ApplicationCode]
,a.[ExtraProperties] = b.[ExtraProperties]
,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = b.[CreatorId]
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = b.[LastModifierId]
,a.[IsDeleted] = b.[IsDeleted]
,a.[DeleterId] = b.[DeleterId]
,a.[DeletionTime] = b.[DeletionTime]
,a.[TotalAmountNoTax] = b.[TotalAmountNoTax]
,a.[PRDetailId] = b.[PRDetailId]
,a.[Price] = b.[Price]
,a.[RowNo] = b.[RowNo]
--,a.[PORowNo] = b.[PORowNo]
FROM dbo.PurPOApplicationDetails a
left join #PurPOApplicationDetails  b
ON a.id=b.id





INSERT INTO dbo.PurPOApplicationDetails
(
 [Id]
,[POApplicationId]
,[Content]
,[Quantity]
,[Unit]
,[UnitPrice]
,[InvoiceType]
,[TotalAmount]
,[TaxRate]
,[TaxAmount]
,[ApplicationCode]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[TotalAmountNoTax]
,[PRDetailId]
,[Price]
,[RowNo]
--,[PORowNo]
)
SELECT
 [Id]
,[POApplicationId]
,[Content]
,[Quantity]
,[Unit]
,[UnitPrice]
,[InvoiceType]
,[TotalAmount]
,[TaxRate]
,[TaxAmount]
,[ApplicationCode]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[TotalAmountNoTax]
,[PRDetailId]
,[Price]
,[RowNo]
--,[PORowNo]
FROM #PurPOApplicationDetails a
WHERE not exists (select * from dbo.PurPOApplicationDetails where id=a.id)


--truncate table dbo.PurPOApplicationDetails

-- alter table dbo.PurPOApplicationDetails alter column [Unit] [nvarchar](50) NULL
-- alter table dbo.PurPOApplicationDetails alter column [InvoiceType] [nvarchar](255) NULL
-- alter table dbo.PurPOApplicationDetails alter column [ApplicationCode] [nvarchar](50) NULL