﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Abbott.SpeakerPortal.Contracts.Vendor.Speaker
{
    /// <summary>
    /// EPD 医生数据返回 DTO
    /// </summary>
    public class EPDDoctorResponseDto
    {
        /// <summary>
        /// 医生姓名
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 医生所属医院
        /// </summary>
        public string HospitalName { get; set; }

        /// <summary>
        /// EPD医生所属医院
        /// </summary>
        public string EpdHospitalName { get; set; }

        /// <summary>
        /// EPD医院ID
        /// </summary>
        public string HospitalCode { get; set; }

        /// <summary>
        /// PP端医院主数据ID
        /// </summary>
        public Guid? HospitalId { get; set; }

        /// <summary>
        /// 是否匹配到 PP端医院主数据
        /// </summary>
        public bool IsMatch { get; set; }

        /// <summary>
        /// 职称
        /// </summary>
        public string ProfessionalTitle { get; set; }

        /// <summary>
        /// PP职称主数据ID
        /// </summary>
        public Guid? ProfessionalTitleId { get; set; }

        /// <summary>
        /// 所属标准科室
        /// </summary>
        public string DepartmentName { get; set; }

        /// <summary>
        /// 所属标准科室Id
        /// </summary>
        public Guid? DepartmentId { get; set; }

        /// <summary>
        /// 所属院内科室
        /// </summary>
        public string HosDepartmentName { get; set; }

        /// <summary>
        /// EPD医生主键
        /// </summary>
        public string EpdHcpCode { get; set; }

        /// <summary>
        /// 医生手机号（AES256加密）
        /// 字符串数组
        /// </summary>
        public string Mobile { get; set; }

        /// <summary>
        /// 基本信息验证结果
        /// </summary>
        public string HcpDcrStatus { get; set; }

        /// <summary>
        /// 基本信息验证结果名字
        /// </summary>
        public string HcpDcrStatusName { get; set; }

        /// <summary>
        /// 讲者编码
        /// </summary>
        public string SpeakerCode { get; set; }
    }
}
