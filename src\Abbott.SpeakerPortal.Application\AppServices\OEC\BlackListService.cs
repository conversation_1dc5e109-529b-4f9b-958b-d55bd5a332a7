﻿using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.OEC.BlackList;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.Integration.Dspot;
using Abbott.SpeakerPortal.Entities.User;
using Abbott.SpeakerPortal.Entities.VendorBlackListOperHistorys;
using Abbott.SpeakerPortal.Entities.VendorBlackLists;
using Abbott.SpeakerPortal.Entities.VendorFinancials;
using Abbott.SpeakerPortal.Entities.VendorOrgnizations;
using Abbott.SpeakerPortal.Entities.VendorPersonals;
using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.OEC.BlackList;
using Abbott.SpeakerPortal.OEC.SpeakerLimit;
using Abbott.SpeakerPortal.Utils;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;

using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;

namespace Abbott.SpeakerPortal.OEC
{
    public class BlackListService : SpeakerPortalAppService, IBlackListService
    {
        private readonly IServiceProvider _serviceProvider;
        private IVendorBlackListRepository vendorBlackListRepository;
        private IVendorRepository vendorRepository;
        private IVendorPersonalRepository vendorPersonalRepository;
        private IVendorOrgnizationRepository vendorOrgnizationRepository;
        private IVendorBlackListOperHistoryRepository vendorBlackListOperHistoryRepository;
        private readonly ILogger<BlackListService> _logger;
        private IRepository<IdentityUser, Guid> _identityUserRepository;
        private ISpeakerLimitService _speakerLimitService;
        private IScheduleJobLogService _jobLogService;

        public BlackListService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            vendorBlackListRepository = serviceProvider.GetService<IVendorBlackListRepository>();
            vendorRepository = serviceProvider.GetService<IVendorRepository>();
            vendorPersonalRepository = serviceProvider.GetService<IVendorPersonalRepository>();
            vendorOrgnizationRepository = serviceProvider.GetService<IVendorOrgnizationRepository>();
            vendorBlackListOperHistoryRepository = serviceProvider.GetService<IVendorBlackListOperHistoryRepository>();
            _identityUserRepository = serviceProvider.GetService<IRepository<IdentityUser, Guid>>();
            _logger = serviceProvider.GetService<ILogger<BlackListService>>();
            _speakerLimitService = serviceProvider.GetService<ISpeakerLimitService>();
            _jobLogService = serviceProvider.GetService<IScheduleJobLogService>();
        }

        /// <summary>
        /// 讲者黑名单列表和HCI机构黑名单列表。Type 讲者：0, HCI机构：1
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetSpeakerBlackListResponseDto>> GetSpeakerBlackList(GetBlackListRequestDto request)
        {
            var data = new List<GetSpeakerBlackListResponseDto>();

            var userQuery = await LazyServiceProvider.LazyGetService<Entities.User.IIdentityUserRepository>().GetQueryableAsync();
            var vendorBlackQuery = await LazyServiceProvider.LazyGetService<IVendorBlackListRepository>().GetNoFilterQueryable();
            var vendorQuery = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();
            var vendorPersonalQuery = await LazyServiceProvider.LazyGetService<IVendorPersonalRepository>().GetQueryableAsync();
            var vendorOrgnizationQuery = await LazyServiceProvider.LazyGetService<IVendorOrgnizationRepository>().GetQueryableAsync();

            if (request.Type == 0)
            {
                #region 讲者数据
                var vendorBlackAndVendorQuery = vendorQuery.Where(a => a.VendorType == VendorTypes.HCPPerson)
                    .Select(x => new { VendorId = x.Id, x.VendorCode })
                    .Join(vendorBlackQuery.WhereIf(request.BlackStatus.HasValue, a => (int)a.Status == request.BlackStatus.Value), x => x.VendorId, y => y.VendorId, (vendorData, vendorBlackData) => new { vendorData, vendorBlackData });

                var vendorPersonalAndBlack = vendorPersonalQuery.WhereIf(!string.IsNullOrWhiteSpace(request.Name), x => x.SPName.Contains(request.Name))
                    .Join(vendorBlackAndVendorQuery, y => y.VendorId, x => x.vendorData.VendorId, (vendorPersonalData, vendorBlackAndVendorData) => new { vendorPersonalData, vendorBlackAndVendorData }).ToList();

                foreach (var item in vendorPersonalAndBlack)
                {
                    var latestDatetime = CompareTreeTime(item.vendorBlackAndVendorData.vendorBlackData);
                    var statusData = TransformBlackListStatus(item.vendorBlackAndVendorData.vendorBlackData);
                    data.Add(new GetSpeakerBlackListResponseDto
                    {
                        VendorId = item.vendorBlackAndVendorData.vendorData.VendorId,
                        VendorCode = item.vendorBlackAndVendorData.vendorData.VendorCode,
                        VendorBlackId = item.vendorBlackAndVendorData.vendorBlackData.Id,
                        //VendorPersonalInfoId SPName
                        VendorPersonalInfoId = item.vendorPersonalData.Id,
                        SPName = item.vendorPersonalData.SPName,
                        CreatedTime = item.vendorBlackAndVendorData.vendorBlackData.CreationTime.ToString("yyyy-MM-dd HH:mm:ss"),
                        OperateTime = latestDatetime.Item1.ToString("yyyy-MM-dd HH:mm:ss"),
                        OperateBy = latestDatetime.Item2.ToString(),
                        StartDate = item.vendorBlackAndVendorData.vendorBlackData.StartDate.ToString("yyyy-MM-dd"),
                        EndDate = item.vendorBlackAndVendorData.vendorBlackData.EndDate?.ToString("yyyy-MM-dd"),
                        Status = EnumUtil.GetDescription(statusData),
                        StatusId = (int)statusData
                    });
                }
                #endregion
            }
            else
            {
                #region HCI机构
                var vendorBlackAndVendorQuery = vendorQuery.Where(a => a.VendorType == VendorTypes.HCIAndOtherInstitutionsAR)
                    .Select(x => new { VendorId = x.Id, x.VendorCode })
                    .Join(vendorBlackQuery.WhereIf(request.BlackStatus.HasValue, a => (int)a.Status == request.BlackStatus.Value), x => x.VendorId, y => y.VendorId, (vendorData, vendorBlackData) => new { vendorData, vendorBlackData });

                var vendorOrgnizationAndBlack = vendorOrgnizationQuery.WhereIf(!string.IsNullOrWhiteSpace(request.Name), x => x.VendorName.Contains(request.Name))
                    .Join(vendorBlackAndVendorQuery, y => y.VendorId, x => x.vendorData.VendorId, (vendorOrgnizationData, vendorBlackAndVendorData) => new { vendorOrgnizationData, vendorBlackAndVendorData }).ToArray();

                foreach (var item in vendorOrgnizationAndBlack)
                {
                    var latestDatetime = CompareTreeTime(item.vendorBlackAndVendorData.vendorBlackData);
                    var statusData = TransformBlackListStatus(item.vendorBlackAndVendorData.vendorBlackData);
                    data.Add(new GetSpeakerBlackListResponseDto
                    {
                        VendorId = item.vendorBlackAndVendorData.vendorData.VendorId,
                        VendorCode = item.vendorBlackAndVendorData.vendorData.VendorCode,
                        VendorBlackId = item.vendorBlackAndVendorData.vendorBlackData.Id,
                        // VendorOrgnizationInfoId SPName
                        VendorOrgnizationInfoId = item.vendorOrgnizationData.Id,
                        SPName = item.vendorOrgnizationData.VendorName,
                        CreatedTime = item.vendorBlackAndVendorData.vendorBlackData.CreationTime.ToString("yyyy-MM-dd HH:mm:ss"),
                        OperateTime = latestDatetime.Item1.ToString("yyyy-MM-dd HH:mm:ss"),
                        OperateBy = latestDatetime.Item2.ToString(),
                        StartDate = item.vendorBlackAndVendorData.vendorBlackData.StartDate.ToString("yyyy-MM-dd"),
                        EndDate = item.vendorBlackAndVendorData.vendorBlackData.EndDate?.ToString("yyyy-MM-dd"),
                        Status = EnumUtil.GetDescription(statusData),
                        StatusId = (int)statusData
                    });
                }

                #region 预添加的HCI机构黑名单
                var preHciBlackListData = vendorBlackQuery
                    //.Where(x => x.VendorId == Guid.Empty)
                    .WhereIf(!string.IsNullOrEmpty(request.Name), x => x.SPName.Contains(request.Name))
                    .WhereIf(request.BlackStatus.HasValue, x => (int)x.Status == request.BlackStatus).ToArray();

                foreach (var item in preHciBlackListData)
                {
                    var latestDatetime = CompareTreeTime(item);
                    var statusData = TransformBlackListStatus(item);
                    data.Add(new GetSpeakerBlackListResponseDto
                    {
                        VendorId = item.VendorId,
                        VendorCode = string.Empty,
                        VendorBlackId = item.Id,
                        // VendorOrgnizationInfoId SPName
                        VendorOrgnizationInfoId = Guid.Empty,
                        SPName = item.SPName,
                        CreatedTime = item.CreationTime.ToString("yyyy-MM-dd HH:mm:ss"),
                        OperateTime = latestDatetime.Item1.ToString("yyyy-MM-dd HH:mm:ss"),
                        OperateBy = latestDatetime.Item2.ToString(),
                        StartDate = item.StartDate.ToString("yyyy-MM-dd"),
                        EndDate = item.EndDate?.ToString("yyyy-MM-dd"),
                        Status = EnumUtil.GetDescription(statusData),
                        StatusId = (int)statusData
                    });
                }
                #endregion

                #endregion
            }
            var dataCount = data.Count();
            data = data.OrderByDescending(x => x.OperateTime).Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToList();
            var joinUserData = data.Join(userQuery, x => Guid.Parse(x.OperateBy), y => y.Id, (data, user) => new { user.Id, user.Name }).ToList();

            //UpdatedBy赋值
            foreach (var item in data)
            {
                if (!string.IsNullOrWhiteSpace(item.OperateBy))
                {
                    var user = joinUserData.FirstOrDefault(x => x.Id == Guid.Parse(item.OperateBy));
                    item.OperateBy = user?.Name;
                }
            }
            var response = new PagedResultDto<GetSpeakerBlackListResponseDto>()
            {
                Items = data,
                TotalCount = dataCount,
            };
            return response;
        }

        /// <summary>
        /// 移除黑名单
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> RemoveBlackList(RemoveBlackListRequestDto request)
        {
            try
            {
                var vendorBlack = await vendorBlackListRepository.FirstOrDefaultAsync(x => x.VendorId == request.VendorId);
                if (vendorBlack == null)
                {
                    _logger.LogError($"RemoveBlackList 移除黑名单Error: vendorId为：{request.VendorId} 的黑名单数据不存在或已经被移除黑名单，不能再移除");
                    return MessageResult.FailureResult($"vendorId为：{request.VendorId} 的黑名单数据不存在或已经被移除黑名单，不能再移除");
                }
                TransformBlackListStatus(vendorBlack);
                //if (vendorBlack.Status != BlackStatus.Effective)
                //{
                //    _logger.LogError($"RemoveBlackList 移除黑名单Error: vendorId为：{request.VendorId}, 黑名单状态为有效的才能移除黑名单");
                //    return MessageResult.FailureResult($"vendorId为：{request.VendorId}, 黑名单状态为有效的才能移除黑名单");
                //}
                vendorBlack.Status = BlackStatus.Removed;
                await vendorBlackListRepository.UpdateAsync(vendorBlack);

                //查询讲者
                var vendorRepository = LazyServiceProvider.GetService<IVendorRepository>();
                var vendorEnt = await vendorRepository.FirstOrDefaultAsync(g => g.Id == request.VendorId);
                //获取讲者对应的所有供应商账号状态
                var vendorFinancial = await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync();
                //var queryBpcsAvm = await LazyServiceProvider.GetService<IBpcsAvmRepository>().GetQueryableAsync();
                //var exitsList = vendorFinancial.Join(queryBpcsAvm, a => a.Id, b => b.FinaId, (a, b) => new { a.Id, a.VendorId, b.Vnstat }).Where(w => w.VendorId == vendorEnt.Id).ToList();
                ////如果没有状态为A的账号，讲者改为失效，否则相反
                //if (exitsList.FirstOrDefault(f => f.Vnstat.Equals("A", StringComparison.CurrentCultureIgnoreCase)) == null)
                //    vendorEnt.Status = VendorStatus.Invalid;
                //else
                //    vendorEnt.Status = VendorStatus.Valid;

                //YTW 20241029 2519 非有效状态的供应商（待激活/待生效），加入黑名单再移除黑名单后，不应该将供应商状态变为失效
                var financials = vendorFinancial.Where(w => w.VendorId == vendorEnt.Id).ToList();
                DetermineVendorStatus(financials, vendorEnt);
                await vendorRepository.UpdateAsync(vendorEnt);

                //await vendorBlackListRepository.DeleteAsync(vendorBlack.Id);
                await vendorBlackListOperHistoryRepository.InsertAsync(
                    new VendorBlackListOperHistory
                    {
                        VendorId = vendorBlack.VendorId,
                        VendorBlackId = vendorBlack.Id,
                        OperType = BlackOperType.JoinOut,
                        StartDate = vendorBlack.StartDate,
                        EndDate = vendorBlack.EndDate,
                        Remark = string.IsNullOrWhiteSpace(request.Remark) ? "" : request.Remark
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError($"RemoveBlackList Error: {ex.Message}");
                return MessageResult.FailureResult();
            }
            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 新增或编辑黑名单
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> CreateOrEditBlackList(CreateOrEditBlackListRequestDto request)
        {
            var vendorBlackQuery = await vendorBlackListRepository.GetNoFilterQueryable();
            if (request.EndDate == null) request.EndDate = DateTime.MaxValue;
            try
            {
                //vendorId是否存在
                var vendorData = await vendorRepository.GetAsync(x => x.Id == request.VendorId);
                if (vendorData == null)
                {
                    _logger.LogError($"CreateOrEditBlackList 黑名单Error: vendorId为：{request.VendorId} 的供应商不存在");
                    return MessageResult.FailureResult($"vendorId：{request.VendorId} 不存在");
                }

                var today = DateTime.Today;
                if (request.StartDate < today)
                    return MessageResult.FailureResult($"起始日期错误：起始日期不能小于今天");
                if (request.EndDate < request.StartDate)
                    return MessageResult.FailureResult($"截止日期错误：截止日期必须晚于起始日期");

                VendorBlackList newBlackList = null;
                if (request.ActionType == 0)
                {
                    //var exsistVendorBlack = vendorBlackQuery.FirstOrDefault(x => x.VendorId == request.VendorId && x.Status != BlackStatus.Removed && x.Status != BlackStatus.Expired && x.IsDeleted == false);
                    var exsistVendorBlack = vendorBlackQuery.FirstOrDefault(x => x.VendorId == request.VendorId);
                    if (exsistVendorBlack != null)
                    {
                        //判断并更新黑名单状态：待生效->已生效
                        if (DateTime.Now > exsistVendorBlack.StartDate && exsistVendorBlack.Status == BlackStatus.ToBeEffective)
                            exsistVendorBlack.Status = BlackStatus.Effective;
                        //判断并更新黑名单状态：已生效->过期
                        if (exsistVendorBlack.EndDate.HasValue && DateTime.Now > exsistVendorBlack.EndDate && exsistVendorBlack.Status == BlackStatus.Effective)
                            exsistVendorBlack.Status = BlackStatus.Expired;
                        //待生效或已生效的数据存在 则不能再添加
                        if (exsistVendorBlack.Status == BlackStatus.ToBeEffective || exsistVendorBlack.Status == BlackStatus.Effective)
                        {
                            _logger.LogError($"CreateOrEditBlackList 黑名单Error: vendorId为：{request.VendorId} 的黑名单数据已存在");
                            return MessageResult.FailureResult($"vendorId为：{request.VendorId} 的黑名单数据已存在");
                        }

                        //更新黑名单数据->成为新的黑名单数据
                        exsistVendorBlack.StartDate = request.StartDate;
                        exsistVendorBlack.EndDate = request.EndDate;
                        exsistVendorBlack.Status = BlackStatus.ToBeEffective;
                        TransformBlackListStatus(exsistVendorBlack);
                        newBlackList = await vendorBlackListRepository.UpdateAsync(exsistVendorBlack);
                    }
                    else
                    {
                        var data = new VendorBlackList
                        {
                            VendorId = request.VendorId,
                            StartDate = request.StartDate,
                            EndDate = request.EndDate
                        };
                        TransformBlackListStatus(data);
                        newBlackList = await vendorBlackListRepository.InsertAsync(data);
                    }
                    //查询讲者
                    var vendorRepository = LazyServiceProvider.GetService<IVendorRepository>();
                    var vendorEnt = await vendorRepository.FirstOrDefaultAsync(g => g.Id == request.VendorId);
                    //满足条件则同步讲者状态为异常
                    if (request.StartDate <= today && request.EndDate > today)
                        vendorEnt.Status = VendorStatus.Exception;
                    await vendorRepository.UpdateAsync(vendorEnt);
                }
                else
                {
                    //编辑
                    var vendorBlack = vendorBlackQuery.FirstOrDefault(x => x.VendorId == request.VendorId);
                    if (vendorBlack != null)
                    {
                        vendorBlack.StartDate = request.StartDate;
                        vendorBlack.EndDate = request.EndDate;
                        vendorBlack.IsDeleted = false;//软删除的数据再编辑变成可用数据
                        TransformBlackListStatus(vendorBlack);
                        newBlackList = await vendorBlackListRepository.UpdateAsync(vendorBlack);

                        //查询讲者
                        var vendorRepository = LazyServiceProvider.GetService<IVendorRepository>();
                        var vendorEnt = await vendorRepository.FirstOrDefaultAsync(g => g.Id == request.VendorId);
                        //满足条件则同步讲者状态为异常
                        if (request.StartDate <= today && request.EndDate > today)
                            vendorEnt.Status = VendorStatus.Exception;
                        else
                        {
                            //获取讲者对应的所有供应商账号状态
                            var vendorFinancial = await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync();
                            var queryBpcsAvm = await LazyServiceProvider.GetService<IBpcsAvmRepository>().GetQueryableAsync();
                            var exitsList = vendorFinancial.Join(queryBpcsAvm, a => a.Id, b => b.FinaId, (a, b) => new { a.Id, a.VendorId, b.Vnstat }).Where(w => w.VendorId == vendorEnt.Id).ToList();
                            //如果没有状态为A的账号，讲者改为失效，否则相反
                            if (exitsList.FirstOrDefault(f => f.Vnstat.Equals("A", StringComparison.CurrentCultureIgnoreCase)) == null)
                                vendorEnt.Status = VendorStatus.Invalid;
                            else
                                vendorEnt.Status = VendorStatus.Valid;
                        }
                        await vendorRepository.UpdateAsync(vendorEnt);
                    }
                    else
                        return MessageResult.FailureResult("找不到该供应商的黑名单数据");
                }

                await vendorBlackListOperHistoryRepository.InsertAsync(new VendorBlackListOperHistory
                {
                    VendorBlackId = newBlackList.Id,
                    VendorId = newBlackList.VendorId,
                    OperPresonal = CurrentUser.Id.Value,
                    OperType = request.ActionType == 0 ? BlackOperType.JoinIn : BlackOperType.Edit,
                    StartDate = newBlackList.StartDate,
                    EndDate = newBlackList.EndDate,
                    Remark = string.IsNullOrWhiteSpace(request.Remark) ? null : request.Remark,
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"GetBlackListDetail Error: {ex.Message}");
                return MessageResult.FailureResult();
            }
            return MessageResult.SuccessResult();
        }


        public async Task<MessageResult> CreateOrEditBlackListHci(CreateOrEditBlackListHciRequestDto request)
        {
            var vendorBlackQuery = await vendorBlackListRepository.GetNoFilterQueryable();
            if (request.EndDate == null) request.EndDate = DateTime.MaxValue;
            request.SPName = request.SPName?.Trim();
            try
            {
                if (string.IsNullOrEmpty(request.SPName))
                    return MessageResult.FailureResult($"机构名称错误：机构名称为空");

                var today = DateTime.Today;
                if (request.StartDate < today && request.ActionType != 2)
                    return MessageResult.FailureResult($"起始日期错误：起始日期不能小于今天");
                if (request.EndDate < request.StartDate && request.ActionType != 2)
                    return MessageResult.FailureResult($"截止日期错误：截止日期必须晚于起始日期");

                VendorBlackList newBlackList = null;
                BlackOperType blackOperType = BlackOperType.JoinIn;

                Entities.Vendors.Vendor vendorEntity = null;

                //查询机构
                var vendorOrgRepository = LazyServiceProvider.GetService<IVendorOrgnizationRepository>();
                var vendorOrg = await vendorOrgRepository.FirstOrDefaultAsync(g => g.VendorName == request.SPName);
                if (vendorOrg != null)
                {
                    var vendorRepository = LazyServiceProvider.GetService<IVendorRepository>();
                    vendorEntity = await vendorRepository.FirstOrDefaultAsync(g => g.Id == vendorOrg.VendorId && g.VendorType == VendorTypes.HCIAndOtherInstitutionsAR);
                    //if (vendorEntity.VendorType != VendorTypes.HCIAndOtherInstitutionsAR)
                    //    return MessageResult.FailureResult($"供应商类型错误，仅HCI机构供应商能加入HCI机构黑名单");
                }

                //添加黑名单
                if (request.ActionType == 0)
                {
                    //SPName是否存在
                    var datetimeNow = DateTime.Now;
                    var exsistVendorBlack = vendorBlackQuery.FirstOrDefault(x => x.SPName == request.SPName);

                    if (exsistVendorBlack != null)
                    {
                        //判断并更新黑名单状态：待生效->已生效
                        if (DateTime.Now > exsistVendorBlack.StartDate && exsistVendorBlack.Status == BlackStatus.ToBeEffective)
                            exsistVendorBlack.Status = BlackStatus.Effective;
                        //判断并更新黑名单状态：已生效->过期
                        if (exsistVendorBlack.EndDate.HasValue && DateTime.Now > exsistVendorBlack.EndDate && exsistVendorBlack.Status == BlackStatus.Effective)
                            exsistVendorBlack.Status = BlackStatus.Expired;

                        //待生效或以生效的数据存在 则不能再添加
                        if (exsistVendorBlack.Status == BlackStatus.ToBeEffective || exsistVendorBlack.Status == BlackStatus.Effective)
                        {
                            _logger.LogError($"CreateOrEditBlackListHci 黑名单Error: 机构名称：{request.SPName} 的供应商已经存在黑名单");
                            return MessageResult.FailureResult($"机构名称：{request.SPName} 的供应商已经存在黑名单");
                        }

                        //更新黑名单数据->成为新的黑名单数据
                        exsistVendorBlack.StartDate = request.StartDate;
                        exsistVendorBlack.EndDate = request.EndDate;
                        exsistVendorBlack.Status = BlackStatus.ToBeEffective;
                        TransformBlackListStatus(exsistVendorBlack);
                        if (vendorEntity != null && exsistVendorBlack.VendorId == Guid.Empty)
                            exsistVendorBlack.VendorId = vendorEntity.Id;
                        newBlackList = await vendorBlackListRepository.UpdateAsync(exsistVendorBlack);
                    }
                    else
                    {
                        var data = new VendorBlackList
                        {
                            VendorId = Guid.Empty,
                            SPName = request.SPName,
                            StartDate = request.StartDate,
                            EndDate = request.EndDate
                        };

                        TransformBlackListStatus(data);
                        if (vendorEntity != null && data.VendorId == Guid.Empty)
                            data.VendorId = vendorEntity.Id;
                        newBlackList = await vendorBlackListRepository.InsertAsync(data);
                    }
                    //满足条件则同步机构状态为异常
                    if (vendorEntity != null && newBlackList.Status == BlackStatus.Effective)
                    {
                        vendorEntity.Status = VendorStatus.Exception;
                        await vendorRepository.UpdateAsync(vendorEntity);
                    }
                }
                else if (request.ActionType == 1)
                {
                    //编辑 只能编辑时间 哪些状态的可以编辑(未移除的均可以编辑)
                    var vendorBlack = vendorBlackQuery.FirstOrDefault(x => x.SPName == request.SPName && x.Status != BlackStatus.Removed);
                    if (vendorBlack == null)
                    {
                        _logger.LogError($"CreateOrEditBlackList 不能编辑: 未找到符合：{request.SPName} 的可编辑黑名单数据");
                        return MessageResult.FailureResult($"未找到符合：{request.SPName} 的可编辑黑名单数据");
                    }

                    blackOperType = BlackOperType.Edit;

                    vendorBlack.StartDate = request.StartDate;
                    vendorBlack.EndDate = request.EndDate;
                    vendorBlack.IsDeleted = false;//软删除的数据再编辑变成可用数据
                    TransformBlackListStatus(vendorBlack);
                    if (vendorEntity != null && vendorBlack.VendorId == Guid.Empty)
                        vendorBlack.VendorId = vendorEntity.Id;
                    newBlackList = await vendorBlackListRepository.UpdateAsync(vendorBlack);

                    //满足条件则同步机构状态为异常
                    if (vendorEntity != null && newBlackList.Status == BlackStatus.Effective)
                    {
                        vendorEntity.Status = VendorStatus.Exception;
                        await vendorRepository.UpdateAsync(vendorEntity);
                    }
                    else if (vendorEntity != null)
                    {
                        //获取讲者对应的所有供应商账号状态
                        var vendorFinancial = await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync();
                        var queryBpcsAvm = await LazyServiceProvider.GetService<IBpcsAvmRepository>().GetQueryableAsync();
                        var exitsList = vendorFinancial.Join(queryBpcsAvm, a => a.Id, b => b.FinaId, (a, b) => new { a.Id, a.VendorId, b.Vnstat }).Where(w => w.VendorId == vendorEntity.Id).ToList();
                        //如果没有状态为A的账号，讲者改为失效，否则相反
                        if (exitsList.FirstOrDefault(f => f.Vnstat.Equals("A", StringComparison.CurrentCultureIgnoreCase)) == null)
                            vendorEntity.Status = VendorStatus.Invalid;
                        else
                            vendorEntity.Status = VendorStatus.Valid;
                        await vendorRepository.UpdateAsync(vendorEntity);
                    }
                }
                else if (request.ActionType == 2)
                {
                    //移除
                    blackOperType = BlackOperType.JoinOut;
                    var vendorBlack = vendorBlackQuery.FirstOrDefault(x => x.SPName == request.SPName);
                    if (vendorBlack == null)
                    {
                        _logger.LogError($"CreateOrEditBlackList 移除黑名单Error: 机构名称为：{request.SPName} 的黑名单数据不存在或已经被移除黑名单，不能再移除");
                        return MessageResult.FailureResult($"机构名称为：{request.SPName} 的黑名单数据不存在或已经被移除黑名单，不能再移除");
                    }

                    //if (vendorBlack.Status != BlackStatus.Effective)
                    //{
                    //    _logger.LogError($"CreateOrEditBlackList 移除黑名单Error: 机构名称为：{request.SPName}, 黑名单状态为有效的才能移除黑名单");
                    //    return MessageResult.FailureResult($"机构名称为：{request.SPName}, 黑名单状态为有效的才能移除黑名单");
                    //}

                    vendorBlack.Status = BlackStatus.Removed;
                    newBlackList = await vendorBlackListRepository.UpdateAsync(vendorBlack);

                    if (vendorEntity != null)
                    {
                        //获取讲者对应的所有供应商账号状态
                        var vendorFinancial = await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync();
                        var queryBpcsAvm = await LazyServiceProvider.GetService<IBpcsAvmRepository>().GetQueryableAsync();
                        var exitsList = vendorFinancial.Join(queryBpcsAvm, a => a.Id, b => b.FinaId, (a, b) => new { a.Id, a.VendorId, b.Vnstat }).Where(w => w.VendorId == vendorEntity.Id).ToList();
                        //如果没有状态为A的账号，讲者改为失效，否则相反
                        if (exitsList.FirstOrDefault(f => f.Vnstat.Equals("A", StringComparison.CurrentCultureIgnoreCase)) == null)
                            vendorEntity.Status = VendorStatus.Invalid;
                        else
                            vendorEntity.Status = VendorStatus.Valid;
                        await vendorRepository.UpdateAsync(vendorEntity);
                    }
                }

                await vendorBlackListOperHistoryRepository.InsertAsync(new VendorBlackListOperHistory
                {
                    VendorBlackId = newBlackList.Id,
                    VendorId = newBlackList.VendorId,
                    OperPresonal = CurrentUser.Id.Value,
                    OperType = blackOperType,
                    StartDate = newBlackList.StartDate,
                    EndDate = newBlackList.EndDate,
                    Remark = string.IsNullOrWhiteSpace(request.Remark) ? null : request.Remark,
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"CreateOrEditBlackListHci Error: {ex.Message}");
                return MessageResult.FailureResult();
            }
            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 讲者黑名单详情
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<GetSpeakerBlackListDetailResponseDto> GetBlackListDetail(GetBlackListDetailRequestDto request)
        {
            var response = new GetSpeakerBlackListDetailResponseDto();
            var vendorBlackQuery = await vendorBlackListRepository.GetNoFilterQueryable();
            try
            {
                if (request.Type == 0)
                {
                    var speakerInfo = await _speakerLimitService.GetSpeakerInfoAsync(request.VendorId);
                    var vendorBlack = vendorBlackQuery.FirstOrDefault(x => x.VendorId == request.VendorId);
                    if (speakerInfo != null && vendorBlack != null)
                    {
                        var blackHistoryList = await vendorBlackListOperHistoryRepository.GetListAsync(x => x.VendorBlackId == vendorBlack.Id);
                        var blackHistory = blackHistoryList.OrderByDescending(x => x.CreationTime).FirstOrDefault();
                        response.speakerInfoDetailDto = (SpeakerInfoDetailDto)speakerInfo.Data;
                        response.CreatedTime = vendorBlack.CreationTime;
                        response.UpdatedBy = vendorBlack.CreatorId.HasValue ? vendorBlack.CreatorId.ToString() : "";
                        response.StartDate = vendorBlack.StartDate;
                        response.EndDate = vendorBlack.EndDate?.ToString("yyyy") != "9999" ? vendorBlack.EndDate : null;
                        response.Remark = blackHistory?.Remark;

                        //UpdatedBy赋值
                        if (!string.IsNullOrWhiteSpace(response.UpdatedBy))
                        {
                            var userData = await _identityUserRepository.GetAsync(x => x.Id.ToString() == response.UpdatedBy);
                            response.UpdatedBy = userData?.Name;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"GetBlackListDetail Error: {ex.Message}");
            }
            return response;
        }


        public async Task<GetBlackListDetailHciResponseDto> GetBlackListDetailHCI(GetBlackListDetailHciRequestDto request)
        {
            GetBlackListDetailHciResponseDto response = null;
            var vendorBlackQuery = await vendorBlackListRepository.GetNoFilterQueryable();
            var vendorOrgQuery = await vendorOrgnizationRepository.GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();

            Guid blackListId = Guid.Empty;
            try
            {
                if (!request.VendorId.HasValue && string.IsNullOrEmpty(request.SPName))
                    return null;
                if (request.VendorId.HasValue)
                {
                    var blackOrg = vendorBlackQuery.Where(x => x.VendorId == request.VendorId)
                        .Join(vendorOrgQuery, a => a.VendorId, a => a.VendorId, (a, b) => new { black = a, vendroOrg = b }).FirstOrDefault();
                    if (blackOrg != null)
                    {
                        var provinces = await dataverseService.GetAllProvince();
                        var cities = await dataverseService.GetAllCity();
                        blackListId = blackOrg.black.Id;
                        response = new GetBlackListDetailHciResponseDto()
                        {
                            SPName = blackOrg.vendroOrg.VendorName,
                            StartDate = blackOrg.black.StartDate,
                            EndDate = blackOrg.black.EndDate,
                            CreatedTime = blackOrg.black.CreationTime,
                            UpdatedBy = blackOrg.black.CreatorId.HasValue ? blackOrg.black.CreatorId.ToString() : ""
                        };
                        if (!string.IsNullOrEmpty(blackOrg.vendroOrg.OrgType))
                        {
                            var orgTypes = await dataverseService.GetDictionariesAsync(DictionaryType.RegisteredCertificateAuthorityType);
                            response.OrgType = orgTypes.FirstOrDefault(x => x.Code == blackOrg.vendroOrg.OrgType)?.Name;
                        }
                        if (!string.IsNullOrEmpty(blackOrg.vendroOrg.Province))
                            response.Province = provinces.FirstOrDefault(x => x.Code == blackOrg.vendroOrg.Province)?.Name;
                        if (!string.IsNullOrEmpty(blackOrg.vendroOrg.City))
                            response.City = cities.FirstOrDefault(x => x.Code == blackOrg.vendroOrg.City)?.Name;
                        //UpdatedBy赋值
                        if (!string.IsNullOrWhiteSpace(response.UpdatedBy))
                        {
                            var userData = await _identityUserRepository.GetAsync(x => x.Id.ToString() == response.UpdatedBy);
                            response.UpdatedBy = userData?.Name;
                        }
                    }
                }
                else if (!string.IsNullOrEmpty(request.SPName))
                {
                    var black = vendorBlackQuery.FirstOrDefault(x => x.SPName == request.SPName);
                    if (black != null)
                    {
                        blackListId = black.Id;
                        response = new GetBlackListDetailHciResponseDto()
                        {
                            SPName = black.SPName,
                            StartDate = black.StartDate,
                            EndDate = black.EndDate?.ToString("yyyy") != "9999" ? black.EndDate : null
                        };
                    }
                }

                if (blackListId != Guid.Empty)
                {
                    var blackHistoryList = await vendorBlackListOperHistoryRepository.GetListAsync(x => x.VendorBlackId == blackListId);
                    var blackHistory = blackHistoryList.OrderByDescending(x => x.CreationTime).FirstOrDefault();
                    response.Remark = blackHistory?.Remark;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"GetBlackListDetail Error: {ex.Message}");
            }
            return response;
        }

        public async Task<PagedResultDto<GetOperHistoryListResponseDto>> GetOperHistoryList(GetOperHistoryListRequestDto request)
        {
            var data = new List<GetOperHistoryListResponseDto>();
            try
            {
                var blackHistoryList = await vendorBlackListOperHistoryRepository.GetListAsync(x => x.VendorBlackId == request.VendorBlackId);
                blackHistoryList = blackHistoryList.OrderByDescending(x => x.LastModificationTime).OrderByDescending(x => x.CreationTime).ToList();
                var blackHistoryListCount = blackHistoryList.Count();
                blackHistoryList = blackHistoryList.Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToList();
                foreach (var black in blackHistoryList)
                {
                    data.Add(new GetOperHistoryListResponseDto
                    {
                        LastModificationTime = (black.LastModificationTime.HasValue ? black.LastModificationTime.Value : (black.DeletionTime.HasValue ? black.DeletionTime.Value : black.CreationTime)).ToString("yyyy-MM-dd HH:mm:ss"),
                        UpdatedBy = black.LastModifierId.HasValue ? black.LastModifierId.ToString() : (black.DeleterId.HasValue ? black.DeleterId.ToString() : black.CreatorId.ToString()),
                        OperType = black.OperType,
                        OperTypeName = EnumUtil.GetDescription(black.OperType),
                        StartDate = black.StartDate.ToString("yyyy-MM-dd"),
                        EndDate = black.EndDate?.ToString("yyyy") != "9999" ? black.EndDate?.ToString("yyyy-MM-dd") : "永久",
                        Remark = black.Remark
                    });
                }

                //UpdatedBy赋值
                foreach (var item in data)
                {
                    if (!string.IsNullOrWhiteSpace(item.UpdatedBy))
                    {
                        var userData = await _identityUserRepository.GetAsync(x => x.Id.ToString() == item.UpdatedBy);
                        item.UpdatedBy = userData?.Name;
                    }
                }

                var response = new PagedResultDto<GetOperHistoryListResponseDto>()
                {
                    Items = data,
                    TotalCount = blackHistoryListCount,
                };
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError($"GetOperHistoryList Error: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 修改状态
        /// </summary>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task UpdateStatus()
        {
            var log = _jobLogService.InitSyncLog("OEC_BlackList");
            try
            {
                var vendorBlackListRepository = LazyServiceProvider.GetService<IVendorBlackListRepository>();
                var vendorRepository = LazyServiceProvider.GetService<IVendorRepository>();
                var vendorBlackListOperHistoryRepository = LazyServiceProvider.GetService<IVendorBlackListOperHistoryRepository>();
                var vendorBlackQuery = await vendorBlackListRepository.GetNoFilterQueryable();
                var vendorQuery = await vendorRepository.GetQueryableAsync();

                #region 
                var vendorBlackAndVendorQuery = vendorQuery.Select(x => new { VendorId = x.Id, x.VendorCode })
                    .Join(vendorBlackQuery, x => x.VendorId, y => y.VendorId, (vendorData, vendorBlackData) => vendorBlackData).ToList();
                log.RecordCount = vendorBlackAndVendorQuery.Count;
                foreach (var item in vendorBlackAndVendorQuery)
                {
                    var latestDatetime = CompareTreeTime(item);
                    var statusData = TransformBlackListStatus(item);
                    item.Status = statusData;
                    //查询供应商
                    var vendorEnt = await vendorRepository.FirstOrDefaultAsync(g => g.Id == item.VendorId);
                    //满足条件则同步供应商状态为异常
                    if (statusData == BlackStatus.Effective)
                        vendorEnt.Status = VendorStatus.Exception;
                    else
                    {
                        //获取对应的所有供应商账号状态
                        var vendorFinancial = await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync();
                        var queryBpcsAvm = await LazyServiceProvider.GetService<IBpcsAvmRepository>().GetQueryableAsync();
                        var exitsList = vendorFinancial.Join(queryBpcsAvm, a => a.Id, b => b.FinaId, (a, b) => new { a.Id, a.VendorId, b.Vnstat }).Where(w => w.VendorId == vendorEnt.Id).ToList();
                        //如果没有状态为A的账号，供应商改为失效，否则相反
                        if (exitsList.FirstOrDefault(f => f.Vnstat.Equals("A", StringComparison.CurrentCultureIgnoreCase)) == null)
                            vendorEnt.Status = VendorStatus.Invalid;
                        else
                            vendorEnt.Status = VendorStatus.Valid;
                    }
                    await vendorRepository.UpdateAsync(vendorEnt);
                }
                try
                {
                    await vendorBlackListRepository.UpdateManyAsync(vendorBlackAndVendorQuery);
                }
                catch (Exception ex)
                {
                    _logger.LogError($"{DateTime.Now:yyyy-MM-dd HH:mm:ss}BlackListAppService UpdateStatus Error: {ex.Message}");
                    throw;
                }
            }
            catch (Exception ex)
            {
                log.IsSuccess = false;
                log.Remark = ex.ToString();
            }
            finally
            {
                _jobLogService.SyncLog(log);
            }
            #endregion
        }


        #region 私有方法

        private Tuple<DateTime, Guid> CompareTreeTime(VendorBlackList data)
        {
            //返回最新的操作人，操作时间
            DateTime maxTime = data.CreationTime;
            Guid id = data.CreatorId.Value;
            try
            {
                if (data.LastModificationTime.HasValue)
                {
                    if (data.DeletionTime.HasValue)
                    {
                        var bCompare = DateTime.Compare(data.LastModificationTime.Value, data.DeletionTime.Value);
                        if (bCompare == 1)
                        {
                            maxTime = data.LastModificationTime.Value;
                            id = data.LastModifierId.Value;
                        }
                        else
                        {
                            maxTime = data.DeletionTime.Value;
                            id = data.DeleterId.Value;
                        }
                    }

                    //
                    var Compare = DateTime.Compare(data.LastModificationTime.Value, data.CreationTime);
                    if (Compare == 1)
                    {
                        maxTime = data.LastModificationTime.Value;
                        id = data.LastModifierId.Value;
                    }
                    else
                    {
                        maxTime = data.CreationTime;
                        id = data.CreatorId.Value;
                    }
                }
                else
                {
                    if (data.DeletionTime.HasValue)
                    {
                        var bCompare = DateTime.Compare(data.DeletionTime.Value, data.CreationTime);
                        if (bCompare == 1)
                        {
                            maxTime = data.DeletionTime.Value;
                            id = data.DeleterId.Value;
                        }
                        else
                        {
                            maxTime = data.CreationTime;
                            id = data.CreatorId.Value;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"CompareTreeTime Error: {ex.Message}");
            }
            return new Tuple<DateTime, Guid>(maxTime, id);
        }


        private BlackStatus TransformBlackListStatus(VendorBlackList data)
        {
            var status = BlackStatus.Effective;
            var nowDate = DateTime.Now.Date;
            if (data.Status == BlackStatus.Removed)
                return data.Status;

            if (data.IsDeleted)
            {
                status = BlackStatus.Removed;
                return status;
            }
            if (nowDate >= data.StartDate.Date && (!data.EndDate.HasValue || (data.EndDate.HasValue && nowDate <= data.EndDate.Value.Date)))
            {
                status = BlackStatus.Effective;
            }
            else if (nowDate < data.StartDate.Date)
            {
                status = BlackStatus.ToBeEffective;
            }
            else if (nowDate > data.EndDate)
            {
                status = BlackStatus.Expired;
            }
            data.Status = status;
            return status;
        }

        /// <summary>
        /// 供应商状态
        /// </summary>
        /// <param name="financials"></param>
        /// <param name="vendorEnt"></param>
        private void DetermineVendorStatus(IEnumerable<VendorFinancial> financials, Entities.Vendors.Vendor vendorEnt)
        {
            if (financials.Any(a => a.FinancialVendorStatus == FinancialVendorStatus.Valid))
            {
                vendorEnt.Status = VendorStatus.Valid;
            }
            else if (financials.All(a => a.FinancialVendorStatus == FinancialVendorStatus.Invalid))
            {
                vendorEnt.Status = VendorStatus.Invalid;
            }
            else if (financials.All(a => a.FinancialVendorStatus == FinancialVendorStatus.ToBeEffective))
            {
                vendorEnt.Status = VendorStatus.ToBeEffective;
            }
            else if (financials.All(a => a.FinancialVendorStatus == FinancialVendorStatus.ToBeActivated))
            {
                vendorEnt.Status = VendorStatus.ToBeActivated;
            }
            else if (financials.Any(a => a.FinancialVendorStatus == FinancialVendorStatus.Invalid))
            {
                vendorEnt.Status = VendorStatus.Invalid;
            }
            else if (financials.Any(a => a.FinancialVendorStatus == FinancialVendorStatus.ToBeEffective ||
                                         a.FinancialVendorStatus == FinancialVendorStatus.ToBeActivated))
            {
                vendorEnt.Status = VendorStatus.ToBeEffective;
            }
            else
            {
                vendorEnt.Status = VendorStatus.ToBeEffective;
            }
        }

        #endregion
    }
}
