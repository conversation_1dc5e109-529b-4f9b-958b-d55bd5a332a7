CREATE PROCEDURE dbo.sp_BdBudgetReturns_ns
AS 
BEGIN 
	
select 
a1.[Id],
--a1.[PrId],
UPPER(ppt.id) as PrId,
a1.[PdRowNo],
UPPER(bsbt.id) as [SubbudgetId],
a1.[Amount],
a1.[OperateTime],
a1.[IsEnable],
case when SUBSTRING(a1.ReturnSourceId,1,1)='O'  then UPPER(pp.id)
	 when SUBSTRING(a1.ReturnSourceId,1,1)='G'  then UPPER(pp1.id) 
	 when SUBSTRING(a1.ReturnSourceId,1,1)='A'  then UPPER(pp2.id) 
	 when SUBSTRING(a1.ReturnSourceId,1,1)='P'  then UPPER(pp3.id) 
	end as [ReturnSourceId],
a1.[ReturnSourceCode],
a1.[ExtraProperties],
a1.[ConcurrencyStamp],
a1.[CreationTime],
case when SUBSTRING(a1.ReturnSourceId,1,1)='O'  then UPPER(pp.id)
	 when SUBSTRING(a1.ReturnSourceId,1,1)='G'  then UPPER(pp1.id) 
	 when SUBSTRING(a1.ReturnSourceId,1,1)='A'  then UPPER(pp2.id) 
	 when SUBSTRING(a1.ReturnSourceId,1,1)='P'  then UPPER(pp3.id) 
	end as [CreatorId],
a1.[LastModificationTime],
a1.[LastModifierId],
a1.[IsDeleted],
a1.[DeleterId],
a1.[DeletionTime],
a1.flag 
into #BdBudgetReturns
from PLATFORM_ABBOTT_STG.dbo.BdBudgetReturns_tmp a1   --355115
left join (select *,ROW_NUMBER () over(PARTITION by ApplicationCode order by ProcInstId) rn from PLATFORM_ABBOTT_STG.dbo.PurPRApplications_tmp) ppt 
on a1.PrId = ppt.ApplicationCode and ppt.rn = 1                   --355123,ApplicationCode重复
left join PLATFORM_ABBOTT_STG.dbo.BdSubBudgets_tmp bsbt 
on a1.SubbudgetId =bsbt.Code 
left join (select *,ROW_NUMBER () over(PARTITION by ApplicationCode order by ApplicationCode) rn from PLATFORM_ABBOTT_STG.dbo.PurPOApplications_tmp) pp 
on a1.ReturnSourceId=pp.ApplicationCode and pp.rn=1
left join (select *,ROW_NUMBER () over(PARTITION by ApplicationCode order by ApplicationCode) rn from PLATFORM_ABBOTT_STG.dbo.PurGRApplications_tmp) pp1 
on a1.ReturnSourceId=pp1.ApplicationCode and pp1.rn=1
left join (select *,ROW_NUMBER () over(PARTITION by ApplicationCode order by ApplicationCode) rn from PLATFORM_ABBOTT_STG.dbo.PurPAApplications_tmp) pp2 
on a1.ReturnSourceId=pp2.ApplicationCode and pp2.rn=1
left join (select *,ROW_NUMBER () over(PARTITION by ApplicationCode order by ApplicationCode) rn from PLATFORM_ABBOTT_STG.dbo.PurPRApplications_tmp) pp3 
on a1.ReturnSourceId=pp3.ApplicationCode and pp3.rn=1


 IF OBJECT_ID(N'PLATFORM_ABBOTT_STG.dbo.BdBudgetReturns ', N'U') IS NOT NULL
	BEGIN
		drop table PLATFORM_ABBOTT_STG.dbo.BdBudgetReturns
		select *
        into PLATFORM_ABBOTT_STG.dbo.BdBudgetReturns from #BdBudgetReturns
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into PLATFORM_ABBOTT_STG.dbo.BdBudgetReturns from #BdBudgetReturns
    -- select * from #vendor_tbl
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END

END;