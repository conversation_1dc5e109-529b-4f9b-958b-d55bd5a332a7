SELECT
 TRY_CONVERT(UNIQUE<PERSON>ENTIFIER, [Id]) [Id]
,TRY_CONVERT(UNIQUEIDENTIFIER, [PRApplicationId]) [PRApplicationId]
,[PayMethod]
,[EstimateDate]
,TRY_CONVERT(UNIQUEIDENTIFIER, [VendorId]) [VendorId]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CostNature]) [CostNature]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CityId]) [CityId]
,[Content]
,[Quantity]
,[Unit]
,[UnitPrice]
,[TotalAmount]
,[TaxRate]
,CASE WHEN [TaxAmount] = '' or [TaxAmount] is null
	THEN  0
	ELSE [TaxAmount]
	END [TaxAmount]
,TRY_CONVERT(UNIQUEIDENTIFIER, [ProductId]) [ProductId]
,[VendorType]
,[AcademyName]
,[AcademyJob]
,[SlideType]
,[SlideName]
,35 AS [ServiceDuration]
,[BackUpVendors]
,TRY_CONVERT(UNIQUE<PERSON>ENTIFIER, [Executor]) [Executor]
,[RceNo]
,[IcbAmount]
,'{}' AS [ExtraProperties]
,'NULL' AS [ConcurrencyStamp]
,GETDATE() AS [CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CreatorId]) [CreatorId]
,[LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [LastModifierId]) [LastModifierId]
,[IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, [DeleterId]) [DeleterId]
,[DeletionTime]
,CASE WHEN [PushFlag] = '' or [PushFlag] is null
	THEN  0
	ELSE [PushFlag]
	END [PushFlag]
,TRY_CONVERT(UNIQUEIDENTIFIER, [PurchaserId]) [PurchaserId]
,[PushTime]
,[RollbackReason]
,[IsOffline]
,[OfflineNo]
,[VendorName]
,[RowNo]
,[IsPrLate]
,TRY_CONVERT(UNIQUEIDENTIFIER, [HedgePrDetailId]) [HedgePrDetailId]
,[CardNo]
,[CertificateCode]
,[CityIdName]
,TRY_CONVERT(UNIQUEIDENTIFIER, NEWID()) [CostCenter]
,[CostCenterCode]
,[CostCenterName]
,[CostNatureCode]
,[CostNatureName]
,[ExecutorEmail]
,[ExecutorName]
,[HcpLevelName]
,[HosDepartment]
,[Hospital]
,[IsVendorConfimed]
,GETDATE() AS [OriginalEstimateDate]
,[OriginalVendorCode]
,TRY_CONVERT(UNIQUEIDENTIFIER, [OriginalVendorId]) [OriginalVendorId]
,[OriginalVendorName]
,[SlideTypeName]
,[StandardDepartment]
,TRY_CONVERT(UNIQUEIDENTIFIER, [StandardDepartmentId]) [StandardDepartmentId]
,[TermCode]
,[TermCodeDays]
,[VendorCode]
,[VendorTypeName]
,[HcpLevelCode]
,[OrderStatusFlag]
,TRY_CONVERT(UNIQUEIDENTIFIER, [BiddingId]) [BiddingId]
,[ExceptionNumber]
,0 AS [TotalAmountRMB]
,0 AS [OriginalTotalAmount]
,0 AS [OriginalTotalAmountRMB]
,NULL AS [MeetingStatus]
,'00000000-0000-0000-0000-000000000000' AS [bwId]
INTO #PurPRApplicationDetails
FROM (SELECT DISTINCT *,ROW_NUMBER() OVER(PARTITION BY Id ORDER BY Id) RK FROM PLATFORM_ABBOTT.dbo.PurPRApplicationDetails) a
WHERE RK = 1

--drop table #PurPRApplicationDetails

USE Speaker_Portal;

UPDATE a
SET 
--  a.[Id] = b.[Id]
 a.[PRApplicationId] = b.[PRApplicationId]
,a.[PayMethod] = b.[PayMethod]
,a.[EstimateDate] = b.[EstimateDate]
,a.[VendorId] = b.[VendorId]
,a.[CostNature] = b.[CostNature]
,a.[CityId] = b.[CityId]
,a.[Content] = b.[Content]
,a.[Quantity] = b.[Quantity]
,a.[Unit] = b.[Unit]
,a.[UnitPrice] = b.[UnitPrice]
,a.[TotalAmount] = b.[TotalAmount]
,a.[TaxRate] = b.[TaxRate]
,a.[TaxAmount] = b.[TaxAmount]
,a.[ProductId] = b.[ProductId]
,a.[VendorType] = b.[VendorType]
,a.[AcademyName] = b.[AcademyName]
,a.[AcademyJob] = b.[AcademyJob]
,a.[SlideType] = b.[SlideType]
,a.[SlideName] = b.[SlideName]
,a.[ServiceDuration] = b.[ServiceDuration]
,a.[BackUpVendors] = b.[BackUpVendors]
,a.[Executor] = b.[Executor]
,a.[RceNo] = b.[RceNo]
,a.[IcbAmount] = b.[IcbAmount]
,a.[ExtraProperties] = b.[ExtraProperties]
,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = b.[CreatorId]
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = b.[LastModifierId]
,a.[IsDeleted] = b.[IsDeleted]
,a.[DeleterId] = b.[DeleterId]
,a.[DeletionTime] = b.[DeletionTime]
,a.[PushFlag] = b.[PushFlag]
,a.[PurchaserId] = b.[PurchaserId]
,a.[PushTime] = b.[PushTime]
,a.[RollbackReason] = b.[RollbackReason]
,a.[IsOffline] = b.[IsOffline]
,a.[OfflineNo] = b.[OfflineNo]
,a.[VendorName] = b.[VendorName]
,a.[RowNo] = b.[RowNo]
,a.[IsPrLate] = b.[IsPrLate]
,a.[HedgePrDetailId] = b.[HedgePrDetailId]
,a.[CardNo] = b.[CardNo]
,a.[CertificateCode] = b.[CertificateCode]
,a.[CityIdName] = b.[CityIdName]
,a.[CostCenter] = b.[CostCenter]
,a.[CostCenterCode] = b.[CostCenterCode]
,a.[CostCenterName] = b.[CostCenterName]
,a.[CostNatureCode] = b.[CostNatureCode]
,a.[CostNatureName] = b.[CostNatureName]
,a.[ExecutorEmail] = b.[ExecutorEmail]
,a.[ExecutorName] = b.[ExecutorName]
,a.[HcpLevelName] = b.[HcpLevelName]
,a.[HosDepartment] = b.[HosDepartment]
,a.[Hospital] = b.[Hospital]
,a.[IsVendorConfimed] = b.[IsVendorConfimed]
,a.[OriginalEstimateDate] = b.[OriginalEstimateDate]
,a.[OriginalVendorCode] = b.[OriginalVendorCode]
,a.[OriginalVendorId] = b.[OriginalVendorId]
,a.[OriginalVendorName] = b.[OriginalVendorName]
,a.[SlideTypeName] = b.[SlideTypeName]
,a.[StandardDepartment] = b.[StandardDepartment]
,a.[StandardDepartmentId] = b.[StandardDepartmentId]
,a.[TermCode] = b.[TermCode]
,a.[TermCodeDays] = b.[TermCodeDays]
,a.[VendorCode] = b.[VendorCode]
,a.[VendorTypeName] = b.[VendorTypeName]
,a.[HcpLevelCode] = b.[HcpLevelCode]
,a.[OrderStatusFlag] = b.[OrderStatusFlag]
,a.[BiddingId] = b.[BiddingId]
,a.[ExceptionNumber] = b.[ExceptionNumber]
,a.[TotalAmountRMB] = b.[TotalAmountRMB]
,a.[OriginalTotalAmount] = b.[OriginalTotalAmount]
,a.[OriginalTotalAmountRMB] = b.[OriginalTotalAmountRMB]
,a.[MeetingStatus] = b.[MeetingStatus]
,a.[bwId] = b.[bwId]
FROM dbo.PurPRApplicationDetails a
left join #PurPRApplicationDetails  b
ON a.id=b.id




INSERT INTO dbo.PurPRApplicationDetails
(
 [Id]
,[PRApplicationId]
,[PayMethod]
,[EstimateDate]
,[VendorId]
,[CostNature]
,[CityId]
,[Content]
,[Quantity]
,[Unit]
,[UnitPrice]
,[TotalAmount]
,[TaxRate]
,[TaxAmount]
,[ProductId]
,[VendorType]
,[AcademyName]
,[AcademyJob]
,[SlideType]
,[SlideName]
,[ServiceDuration]
,[BackUpVendors]
,[Executor]
,[RceNo]
,[IcbAmount]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[PushFlag]
,[PurchaserId]
,[PushTime]
,[RollbackReason]
,[IsOffline]
,[OfflineNo]
,[VendorName]
,[RowNo]
,[IsPrLate]
,[HedgePrDetailId]
,[CardNo]
,[CertificateCode]
,[CityIdName]
,[CostCenter]
,[CostCenterCode]
,[CostCenterName]
,[CostNatureCode]
,[CostNatureName]
,[ExecutorEmail]
,[ExecutorName]
,[HcpLevelName]
,[HosDepartment]
,[Hospital]
,[IsVendorConfimed]
,[OriginalEstimateDate]
,[OriginalVendorCode]
,[OriginalVendorId]
,[OriginalVendorName]
,[SlideTypeName]
,[StandardDepartment]
,[StandardDepartmentId]
,[TermCode]
,[TermCodeDays]
,[VendorCode]
,[VendorTypeName]
,[HcpLevelCode]
,[OrderStatusFlag]
,[BiddingId]
,[ExceptionNumber]
,[TotalAmountRMB]
,[OriginalTotalAmount]
,[OriginalTotalAmountRMB]
,[MeetingStatus]
--,[bwId]
)
SELECT
 [Id]
,[PRApplicationId]
,[PayMethod]
,[EstimateDate]
,[VendorId]
,[CostNature]
,[CityId]
,[Content]
,[Quantity]
,[Unit]
,[UnitPrice]
,[TotalAmount]
,[TaxRate]
,[TaxAmount]
,[ProductId]
,[VendorType]
,[AcademyName]
,[AcademyJob]
,[SlideType]
,[SlideName]
,[ServiceDuration]
,[BackUpVendors]
,[Executor]
,[RceNo]
,[IcbAmount]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[PushFlag]
,[PurchaserId]
,[PushTime]
,[RollbackReason]
,[IsOffline]
,[OfflineNo]
,[VendorName]
,[RowNo]
,[IsPrLate]
,[HedgePrDetailId]
,[CardNo]
,[CertificateCode]
,[CityIdName]
,[CostCenter]
,[CostCenterCode]
,[CostCenterName]
,[CostNatureCode]
,[CostNatureName]
,[ExecutorEmail]
,[ExecutorName]
,[HcpLevelName]
,[HosDepartment]
,[Hospital]
,[IsVendorConfimed]
,[OriginalEstimateDate]
,[OriginalVendorCode]
,[OriginalVendorId]
,[OriginalVendorName]
,[SlideTypeName]
,[StandardDepartment]
,[StandardDepartmentId]
,[TermCode]
,[TermCodeDays]
,[VendorCode]
,[VendorTypeName]
,[HcpLevelCode]
,[OrderStatusFlag]
,[BiddingId]
,[ExceptionNumber]
,[TotalAmountRMB]
,[OriginalTotalAmount]
,[OriginalTotalAmountRMB]
,[MeetingStatus]
--,[bwId]
FROM #PurPRApplicationDetails a
WHERE not exists (select * from dbo.PurPRApplicationDetails where id=a.id)


--select top 1000 * from #PurPRApplicationDetails order by OriginalEstimateDate desc

--truncate table dbo.PurPRApplicationDetails

--select cast(OriginalEstimateDate as datetime2) from #PurPRApplicationDetails

/*
alter table dbo.PurPRApplicationDetails
alter column [RollbackReason] nvarchar(500)

alter table dbo.PurPRApplicationDetails
alter column [CostCenterCode] [nvarchar](max) NULL

alter table dbo.PurPRApplicationDetails
alter column [CostCenterName] [nvarchar](200) NULL

alter table dbo.PurPRApplicationDetails
alter column [ExecutorEmail] [nvarchar](100) NULL

alter table dbo.PurPRApplicationDetails
alter column [ExecutorName] [nvarchar](max) NULL

alter table dbo.PurPRApplicationDetails
alter column [HosDepartment] [nvarchar](max) NULL

alter table dbo.PurPRApplicationDetails
alter column [StandardDepartment] [nvarchar](max) NULL
*/
