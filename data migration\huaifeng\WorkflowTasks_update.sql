SELECT
 TRY_CONVERT(UNIQUEIDENTIFIER, [Id]) [Id]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([InstanceId],'00000000-0000-0000-0000-000000000000')) [InstanceId]
,[Name]
,ISNULL([Status],-1) [Status]
,[Remark]
,TRY_CONVERT(UNIQUEIDENTIFIER, [NextApproverId]) [NextApproverId]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([FormId],'00000000-0000-0000-0000-000000000000')) [FormId]
,[WorkStep]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CreatorId]) [CreatorId]
,[LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [LastModifierId]) [LastModifierId]
,[IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, [DeleterId]) [DeleterId]
,[DeletionTime]
,[ApprovalTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [ApprovalId]) [ApprovalId]
,[FormName]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([OriginalApprovalId],'00000000-0000-0000-0000-000000000000')) [OriginalApprovalId]
INTO #WorkflowTasks
FROM (SELECT *,ROW_NUMBER() OVER(PARTITION BY Id ORDER BY Id) RK FROM PLATFORM_ABBOTT_Dev.dbo.WorkflowTasks)a
WHERE RK = 1
;

select count(*) from PLATFORM_ABBOTT_Dev.dbo.WorkflowTasks
--drop table #AgentHistory

USE Speaker_Portal_Dev;

UPDATE a 
SET 
 a.[Id] = b.[Id]
,a.[InstanceId] = b.[InstanceId]
,a.[Name] = b.[Name]
,a.[Status] = b.[Status]
,a.[Remark] = b.[Remark]
,a.[NextApproverId] = b.[NextApproverId]
,a.[FormId] = b.[FormId]
,a.[WorkStep] = b.[WorkStep]
,a.[ExtraProperties] = b.[ExtraProperties]
,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = b.[CreatorId]
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = b.[LastModifierId]
,a.[IsDeleted] = b.[IsDeleted]
,a.[DeleterId] = b.[DeleterId]
,a.[DeletionTime] = b.[DeletionTime]
,a.[ApprovalTime] = b.[ApprovalTime]
,a.[ApprovalId] = b.[ApprovalId]
,a.[FormName] = b.[FormName]
,a.[OriginalApprovalId] = b.[OriginalApprovalId]
FROM dbo.WorkflowTasks a
left join #WorkflowTasks  b
ON a.id=b.id;


INSERT INTO dbo.WorkflowTasks
(
 [Id]
,[InstanceId]
,[Name]
,[Status]
,[Remark]
,[NextApproverId]
,[FormId]
,[WorkStep]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[ApprovalTime]
,[ApprovalId]
,[FormName]
,[OriginalApprovalId]
)
SELECT
 [Id]
,[InstanceId]
,[Name]
,[Status]
,[Remark]
,[NextApproverId]
,[FormId]
,[WorkStep]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[ApprovalTime]
,[ApprovalId]
,[FormName]
,[OriginalApprovalId]
FROM #WorkflowTasks a
WHERE not exists (select * from dbo.WorkflowTasks where id=a.id);

--truncate table dbo.AgentHistory

--alter table dbo.WorkflowTasks alter column [Name] [nvarchar](max) NOT NULL
--alter table dbo.WorkflowTasks alter column [Remark] [nvarchar](max) NULL
--alter table dbo.WorkflowTasks alter column [WorkStep] [nvarchar](200) NULL