﻿using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPAApplication.PAInvoice;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.AppServices.Purchase.PAInvoice
{
    public class PAFinancialVoucher
    {
        private readonly Dictionary<string, IFinancialVoucherInvoice> _financialVoucher;
        public PAFinancialVoucher()
        {
            _financialVoucher = new Dictionary<string, IFinancialVoucherInvoice>
            {
                { InvoiceType.EmployeeGift,new FinancialVoucherEmployeeGift()},//员工礼品
                { InvoiceType.GeneralVATInvoice,new FinancialVoucherGeneralVATlnvoice()},//增值税普通发票
                { InvoiceType.GiftIncrease,new FinancialVoucherGiftIncrease()},//礼品赠票
                { InvoiceType.OrdinaryInvoice,new FinancialVoucherOrdinaryInvoice()},//普通发票
                { InvoiceType.SpecialVATInvoice,new FinancialVoucherSpecialVATInvoice()},//增值税专用发票
                { InvoiceType.NonInvoice,new FinancialVoucherNonInvoice()},//无发票（AR付款方式的、直接从PR到PA）
            };
        }

        public async Task<List<FinancialVoucherInfoDto>> GetFinancialVoucherInfo(PurPAApplicationDetailDto paApplicationDetail)
        {
            if (!_financialVoucher.TryGetValue(paApplicationDetail.InvoiceType, out var financialVoucher))
            {
                throw new ArgumentException($"Unsupported invoice type: {paApplicationDetail.InvoiceType}");
            }

            return await financialVoucher.GetFinancialVoucherInfo(paApplicationDetail);
        }
    }
}
