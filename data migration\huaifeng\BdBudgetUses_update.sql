SELECT
 TRY_CONVERT(UNIQUEIDENTIFIER, a.[Id]) [Id]
,TRY_CONVERT(UNIQUEIDENTIFIER, IIF(a.[PrId] is null,'00000000-0000-0000-0000-000000000000',a.[PrId])) [PrId]
,[PdRowNo]
,TRY_CONVERT(UNIQUEIDENTIFIER, IIF(b.[id] is null,'00000000-0000-0000-0000-000000000000',b.[id])) [SubbudgetId]
,[Amount]
,GETDATE() AS [OperateTime]
,1 AS [IsEnable]
,'{}' AS [ExtraProperties]
,'NULL' AS [ConcurrencyStamp]
,GETDATE() AS [CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, a.[CreatorId]) [CreatorId]
,a.[LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, a.[LastModifierId]) [LastModifierId]
,0 AS [IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, a.[DeleterId]) [DeleterId]
,a.[DeletionTime]
--,[nbr] 目标层无此字段
--,[type] 目标层无此字段
INTO #BdBudgetUses
FROM PLATFORM_ABBOTT_STG.dbo.BdBudgetUses AS a
LEFT JOIN PLATFORM_ABBOTT_Stg.dbo.BdSubBudgets AS b
ON a.SubBudgetId = b.id
WHERE SubbudgetId IS NOT NULL 
and [PrId] IS NOT NULL-- 源表逻辑，该字段有的未关联上值

--drop table #BdBudgetUses

USE Speaker_Portal_STG;

UPDATE a
SET
-- a.[Id] = b.[Id]
 a.[PrId] = b.[PrId]
,a.[PdRowNo] = b.[PdRowNo]
,a.[SubbudgetId] = b.[SubbudgetId]
,a.[Amount] = b.[Amount]
,a.[OperateTime] = b.[OperateTime]
,a.[IsEnable] = b.[IsEnable]
,a.[ExtraProperties] = b.[ExtraProperties]
,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = b.[CreatorId]
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = b.[LastModifierId]
,a.[IsDeleted] = b.[IsDeleted]
,a.[DeleterId] = b.[DeleterId]
,a.[DeletionTime] = b.[DeletionTime]
--,a.[nbr] = b.[nbr] 目标层无此字段
--,a.[type] = b.[type] 目标层无此字段
FROM dbo.BdBudgetUses a
left join #BdBudgetUses  b
ON a.id=b.id


--select* from #BdBudgetUses WHERE PrId IS NULL


INSERT INTO dbo.BdBudgetUses
SELECT
 [Id]
,[PrId]
,[PdRowNo]
,[SubbudgetId]
,[Amount]
,[OperateTime]
,[IsEnable]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
--,[nbr] 目标层无此字段
--,[type] 目标层无此字段
FROM #BdBudgetUses a
WHERE not exists (select * from dbo.BdBudgetUses where id=a.id)
