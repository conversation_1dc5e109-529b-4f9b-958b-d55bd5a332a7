﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Abbott.SpeakerPortal.Contracts.Vendor.Speaker
{
    /// <summary>
    /// EPD关联 讲者请求DTO
    /// </summary>
    public class EpdRelationSpeakerDto
    {
        /// <summary>
        /// 讲者Id(Vendor 表Id)
        /// </summary>
        public Guid? VendorId { get; set; }
        /// <summary>
        /// 讲者申请Id(VendorApplication 表Id)
        /// </summary>
        public Guid? VendorApplicationId { get; set; }
        /// <summary>
        /// epdId
        /// </summary>
        public string EpdId { get; set; }

        /// <summary>
        /// 草稿ID
        /// </summary>
       public Guid? DraftId { get; set; }
}
}
