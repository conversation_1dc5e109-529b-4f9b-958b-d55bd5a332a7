﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text.Json.Serialization;
using Abbott.SpeakerPortal.Contracts.Common.Attachment;
using Abbott.SpeakerPortal.Enums;
using MiniExcelLibs.Attributes;

namespace Abbott.SpeakerPortal.Contracts.Budget.FocSubbudget
{
    public class GetFocSubbudgetListResponseDto
    {
        /// <summary>
        /// 子预算Id
        /// </summary>
        [ExcelIgnore]
        public Guid Id { get; set; }
        /// <summary>
        /// 子预算编号
        /// </summary>
        [ExcelColumnName("子预算编号")]
        public string SubbudgetCode { get; set; }
        /// <summary>
        /// 主预算编号
        /// </summary>
        [ExcelColumnName("主预算编号")]
        public string MasterBudgetCode { get; set; }
        /// <summary>
        /// 子预算BU名称
        /// </summary>
        [ExcelColumnName("BU")]
        public string BuName { get; set; }
        /// <summary>
        /// 子预算成本中心名称
        /// </summary>
        [ExcelColumnName("成本中心")]
        public string CostCenterName { get; set; }
        /// <summary>
        /// 子预算大区名称
        /// </summary>
        [ExcelColumnName("大区")]
        public string RegionName { get; set; }
        /// <summary>
        /// 描述
        /// </summary>
        [ExcelColumnName("描述")]
        public string Description { get; set; }
        /// <summary>
        /// 子预算负责人姓名
        /// </summary>
        [ExcelColumnName("负责人")]
        public string OwnerName { get; set; }
        /// <summary>
        /// 子预算负责人邮箱
        /// </summary>
        [ExcelColumnName("负责人邮箱")]
        public string OwnerEmail { get; set; }
        /// <summary>
        /// 品牌信息编码
        /// </summary>
        [ExcelColumnName("品牌信息编码")]
        public string ProductMCode { get; set; }
        /// <summary>
        /// 产品简称
        /// </summary>
        [ExcelColumnName("产品简称")]
        public string ProductName { get; set; }
        /// <summary>
        /// 子预算状态文本
        /// </summary>
        [ExcelColumnName("子预算状态")]
        [JsonIgnore]
        public string StatusText => Status ? "开启" : "冻结";
        [ExcelColumnName("已开启数量")]
        [JsonIgnore]
        public string EnableQtyText => EnableQty.ToString();
        [ExcelColumnName("已用数量")]
        [JsonIgnore]
        public string UsedQtyText => UsedQty.ToString();
        [ExcelColumnName("可用数量")]
        [JsonIgnore]
        public string AvailableQtyText => AvailableQty.ToString();
        /// <summary>
        /// 年度
        /// </summary>
        [ExcelColumnName("年度")]
        public int? Year { get; set; }
        [ExcelColumnName("预算数量")]
        [JsonIgnore]
        public string BudgetQtyText => BudgetQty.ToString("N2");
        /// <summary>
        /// 子预算金额
        /// </summary>
        [ExcelColumnName("预算数量")]
        [ExcelIgnore]
        public decimal BudgetQty { get; set; }
        /// <summary>
        /// 已用金额
        /// </summary>
        [ExcelColumnName("已用数量")]
        [ExcelIgnore]
        public int UsedQty { get; set; }
        /// <summary>
        /// 可用数量
        /// </summary>
        [ExcelIgnore]
        public int AvailableQty { get { return this.EnableQty - this.UsedQty; } }
        /// <summary>
        /// 启用数量
        /// </summary>
        [ExcelIgnore]
        public int EnableQty { get { return this.Monthlies.Where(m => m.Value.Status).Sum(s => s.Value.BudgetQty); } }

        /// <summary>
        /// 主预算Id
        /// </summary>
        [ExcelIgnore]
        public Guid MasterBudgetId { get; set; }

        /// <summary>
        /// 子预算BuId
        /// </summary>
        [ExcelIgnore]
        public Guid BuId { get; set; }
        /// <summary>
        /// 子预算成本中心Id
        /// </summary>
        [ExcelIgnore]
        public Guid CostCenterId { get; set; }
        /// <summary>
        /// 子预算大区Id
        /// </summary>
        [ExcelIgnore]
        public Guid RegionId { get; set; }
        /// <summary>
        /// 子预算负责人Id
        /// </summary>
        [ExcelIgnore]
        public Guid OwnerId { get; set; }

        #region 月份导出文本
        /// <summary>
        /// 一月预算
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("一月预算")]
        public string JanQtyText => JanQty?.ToString("N2");
        /// <summary>
        /// 一月状态
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("一月状态")]
        public string JanStatusText => JanStatus == true ? "是" : "否";
        /// <summary>
        /// 二月预算
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("二月预算")]
        public string FebQtyText => FebQty?.ToString("N2");
        /// <summary>
        /// 二月状态
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("二月状态")]
        public string FebStatusText => FebStatus == true ? "是" : "否";
        /// <summary>
        /// 三月预算
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("三月预算")]
        public string MarQtyText => MarQty?.ToString("N2");
        /// <summary>
        /// 三月状态
        /// </summary>
        [ExcelColumnName("三月状态")]
        [JsonIgnore]
        public string MarStatusText => MarStatus == true ? "是" : "否";
        /// <summary>
        /// 四月预算
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("四月预算")]
        public string AprQtyText => AprQty?.ToString("N2");
        /// <summary>
        /// 四月状态
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("四月状态")]
        public string AprStatusText => AprStatus == true ? "是" : "否";
        /// <summary>
        /// 五月预算
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("五月预算")]
        public string MayQtyText => MayQty?.ToString("N2");
        /// <summary>
        /// 五月状态
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("五月状态")]
        public string MayStatusText => MayStatus == true ? "是" : "否";
        /// <summary>
        /// 六月预算
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("六月预算")]
        public string JunQtyText => JunQty?.ToString("N2");
        /// <summary>
        ///六月状态
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("六月状态")]
        public string JunStatusText => JunStatus == true ? "是" : "否";
        /// <summary>
        /// 七月预算
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("七月预算")]
        public string JulQtyText => JulQty?.ToString("N2");
        /// <summary>
        /// 七月状态
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("七月状态")]
        public string JulStatusText => JulStatus == true ? "是" : "否";
        /// <summary>
        /// 八月预算
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("八月预算")]
        public string AugQtyText => AugQty?.ToString("N2");
        /// <summary>
        /// 八月状态
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("八月状态")]
        public string AugStatusText => AugStatus == true ? "是" : "否";
        /// <summary>
        /// 九月预算
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("九月预算")]
        public string SeptQtyText => SeptQty?.ToString("N2");
        /// <summary>
        /// 九月状态
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("九月状态")]
        public string SeptStatusText => SeptStatus == true ? "是" : "否";
        /// <summary>
        /// 十月预算
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("十月预算")]
        public string OctQtyText => OctQty?.ToString("N2");
        /// <summary>
        /// 十月状态
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("十月状态")]
        public string OctStatusText => OctStatus == true ? "是" : "否";
        /// <summary>
        /// 十一月预算
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("十一月预算")]
        public string NovQtyText => NovQty?.ToString("N2");
        /// <summary>
        /// 十一月状态
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("十一月状态")]
        public string NovStatusText => NovStatus == true ? "是" : "否";
        /// <summary>
        /// 十二月预算
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("十二月预算")]
        public string DecQtyText => DecQty?.ToString("N2");
        /// <summary>
        /// 十二月状态
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("十二月状态")]
        public string DecStatusText => DecStatus == true ? "是" : "否";
        #endregion
        #region EPD特有字段
        /// <summary>
        /// EPD特有BU2
        /// </summary>
        [Category("EPD")]
        [ExcelColumnName("BU2")]
        public string BU2 { get; set; }
        ///// <summary>
        ///// EPD特有负责人Id
        ///// </summary>
        //public Guid? OwnerId2 { get; set; }
        /// <summary>
        /// EPD特有负责人姓名
        /// </summary>
        [Category("EPD")]
        [ExcelColumnName("负责人")]
        public string OwnerName2 { get; set; }
        ///// <summary>
        ///// EPD特有大区经理Id
        ///// </summary>
        //public Guid? RegionManagerId { get; set; }
        /// <summary>
        /// EPD特有大区经理姓名
        /// </summary>
        [Category("EPD")]
        [ExcelColumnName("大区经理")]
        public string RegionManagerName { get; set; }
        ///// <summary>
        ///// EPD特有LMM Id
        ///// </summary>
        //public Guid? LMMId { get; set; }
        /// <summary>
        /// EPD特有LMM姓名
        /// </summary>
        [Category("EPD")]
        [ExcelColumnName("LMM")]
        public string LMMName { get; set; }
        ///// <summary>
        ///// 产品经理Id
        ///// </summary>
        //public Guid? ProductManagerId { get; set; }
        /// <summary>
        /// 产品经理姓名
        /// </summary>
        [Category("EPD")]
        [ExcelColumnName("产品经理")]
        public string ProductManagerName { get; set; }
        #endregion

        #region ADC特有字段
        /// <summary>
        /// ADC特有是否合规审计
        /// </summary>
        [Category("ADC")]
        [ExcelColumnName("合规审计")]
        public bool? IsComplicanceAudits { get; set; }
        #endregion

        /// <summary>
        /// 状态
        /// </summary>
        [ExcelIgnore]
        public bool Status { get; set; }

        /// <summary>
        /// 是否可删除
        /// </summary>
        [ExcelIgnore]
        public bool IsDeletable { get; set; }
        /// <summary>
        /// 附件
        /// </summary>
        [ExcelIgnore]
        public List<UploadFileResponseDto> AttachmentInformation { get; set; }
        /// <summary>
        /// 一月预算
        /// </summary>
        [ExcelIgnore]
        public int? JanQty { get { return Monthlies.GetOrDefault(Month.Jan)?.BudgetQty; } }
        /// <summary>
        /// 一月状态
        /// </summary>
        [ExcelIgnore]
        public bool? JanStatus { get { return Monthlies.GetOrDefault(Month.Jan)?.Status; } }
        /// <summary>
        /// 二月预算
        /// </summary>
        [ExcelIgnore]
        public int? FebQty { get { return Monthlies.GetOrDefault(Month.Feb)?.BudgetQty; } }
        /// <summary>
        /// 二月状态
        /// </summary>
        [ExcelIgnore]
        public bool? FebStatus { get { return Monthlies.GetOrDefault(Month.Feb)?.Status; } }
        /// <summary>
        /// 三月预算
        /// </summary>
        [ExcelIgnore]
        public int? MarQty { get { return Monthlies.GetOrDefault(Month.Mar)?.BudgetQty; } }
        /// <summary>
        /// 三月状态
        /// </summary>
        [ExcelIgnore]
        public bool? MarStatus { get { return Monthlies.GetOrDefault(Month.Mar)?.Status; } }
        /// <summary>
        /// 四月预算
        /// </summary>
        [ExcelIgnore]
        public int? AprQty { get { return Monthlies.GetOrDefault(Month.Apr)?.BudgetQty; } }
        /// <summary>
        /// 四月状态
        /// </summary>
        [ExcelIgnore]
        public bool? AprStatus { get { return Monthlies.GetOrDefault(Month.Apr)?.Status; } }
        /// <summary>
        /// 五月预算
        /// </summary>
        [ExcelIgnore]
        public int? MayQty { get { return Monthlies.GetOrDefault(Month.May)?.BudgetQty; } }
        /// <summary>
        /// 五月状态
        /// </summary>
        [ExcelIgnore]
        public bool? MayStatus { get { return Monthlies.GetOrDefault(Month.May)?.Status; } }
        /// <summary>
        /// 六月预算
        /// </summary>
        [ExcelIgnore]
        public int? JunQty { get { return Monthlies.GetOrDefault(Month.Jun)?.BudgetQty; } }
        /// <summary>
        ///六月状态
        /// </summary>
        [ExcelIgnore]
        public bool? JunStatus { get { return Monthlies.GetOrDefault(Month.Jun)?.Status; } }
        /// <summary>
        /// 七月预算
        /// </summary>
        [ExcelIgnore]
        public int? JulQty { get { return Monthlies.GetOrDefault(Month.Jul)?.BudgetQty; } }
        /// <summary>
        /// 七月状态
        /// </summary>
        [ExcelIgnore]
        public bool? JulStatus { get { return Monthlies.GetOrDefault(Month.Jul)?.Status; } }
        /// <summary>
        /// 八月预算
        /// </summary>
        [ExcelIgnore]
        public int? AugQty { get { return Monthlies.GetOrDefault(Month.Aug)?.BudgetQty; } }
        /// <summary>
        /// 八月状态
        /// </summary>
        [ExcelIgnore]
        public bool? AugStatus { get { return Monthlies.GetOrDefault(Month.Aug)?.Status; } }
        /// <summary>
        /// 九月预算
        /// </summary>
        [ExcelIgnore]
        public int? SeptQty { get { return Monthlies.GetOrDefault(Month.Sept)?.BudgetQty; } }
        /// <summary>
        /// 九月状态
        /// </summary>
        [ExcelIgnore]
        public bool? SeptStatus { get { return Monthlies.GetOrDefault(Month.Sept)?.Status; } }
        /// <summary>
        /// 十月预算
        /// </summary>
        [ExcelIgnore]
        public int? OctQty { get { return Monthlies.GetOrDefault(Month.Oct)?.BudgetQty; } }
        /// <summary>
        /// 十月状态
        /// </summary>
        [ExcelIgnore]
        public bool? OctStatus { get { return Monthlies.GetOrDefault(Month.Oct)?.Status; } }
        /// <summary>
        /// 十一月预算
        /// </summary>
        [ExcelIgnore]
        public int? NovQty { get { return Monthlies.GetOrDefault(Month.Nov)?.BudgetQty; } }
        /// <summary>
        /// 十一月状态
        /// </summary>
        [ExcelIgnore]
        public bool? NovStatus { get => Monthlies.GetOrDefault(Month.Nov)?.Status; }
        /// <summary>
        /// 十二月预算
        /// </summary>
        [ExcelIgnore]
        public int? DecQty { get { return Monthlies.GetOrDefault(Month.Dec)?.BudgetQty; } }
        /// <summary>
        /// 十二月状态
        /// </summary>
        [ExcelIgnore]
        public bool? DecStatus { get { return Monthlies.GetOrDefault(Month.Dec)?.Status; } }
        [JsonIgnore]
        [ExcelIgnore]
        public Dictionary<Month, FocMonthlyBudgetDto> Monthlies { get; set; }
    }
}
