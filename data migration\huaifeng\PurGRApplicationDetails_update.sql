SELECT
 TRY_CONVERT(UNIQUEIDENTIFIER, [Id]) [Id]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([GRApplicationId],'00000000-0000-0000-0000-000000000000')) [GRApplicationId]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([ProductId],'00000000-0000-0000-0000-000000000000')) [ProductId]
,[InvoiceType]
,ISNULL([OrderQuantity],0) [OrderQuantity]
,IIF([TotalReceivedQuantity] = '',0.0,CAST([TotalReceivedQuantity] as decimal(32,8))) [TotalReceivedQuantity]
,[DeliveryMethod]
,IIF([CurrentReceivingQuantity] = '',0.0,CAST([CurrentReceivingQuantity] as decimal(32,8))) [CurrentReceivingQuantity]
,IIF([CurrentSignedQuantity] = '',0.0,CAST([CurrentSignedQuantity] as decimal(32,8))) [CurrentSignedQuantity]
,ISNULL([UnitPrice],0) [UnitPrice]
,[ReceivedAmount]
,ISNULL([SigningDate],GETDATE()) [SigningDate]
,[IsArrive]
,[ExtraProperties]
,'NULL' [ConcurrencyStamp]
,isnull([CreationTime],'1900-01-01')CreationTime
,TRY_CONVERT(UNIQUEIDENTIFIER, [CreatorId]) [CreatorId]
,[LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [LastModifierId]) [LastModifierId]
,[IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, [DeleterId]) [DeleterId]
,[DeletionTime]
,[AllocationAmount]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([PRDetailId],'00000000-0000-0000-0000-000000000000')) [PRDetailId]
,[AllocationRMBAmount]
,TRY_CONVERT(UNIQUEIDENTIFIER, [PODetailId]) [PODetailId]
,[PurchaseRMBAmount]
,[IsAdvancePayment]
,[ProductName]
INTO #PurGRApplicationDetails
FROM (SELECT *,ROW_NUMBER() OVER(PARTITION BY Id ORDER BY Id) RK FROM PLATFORM_ABBOTT_STG.dbo.PurGRApplicationDetails)a
WHERE RK = 1
;
--drop table #PurGRApplicationDetails

USE Speaker_Portal_STG;

UPDATE a 
SET 
 a.[Id] = b.[Id]
,a.[GRApplicationId] = b.[GRApplicationId]
,a.[ProductId] = b.[ProductId]
,a.[InvoiceType] = b.[InvoiceType]
,a.[OrderQuantity] = b.[OrderQuantity]
,a.[TotalReceivedQuantity] = b.[TotalReceivedQuantity]
,a.[DeliveryMethod] = b.[DeliveryMethod]
,a.[CurrentReceivingQuantity] = b.[CurrentReceivingQuantity]
,a.[CurrentSignedQuantity] = b.[CurrentSignedQuantity]
,a.[UnitPrice] = b.[UnitPrice]
,a.[ReceivedAmount] = b.[ReceivedAmount]
,a.[SigningDate] = b.[SigningDate]
,a.[IsArrive] = b.[IsArrive]
,a.[ExtraProperties] = b.[ExtraProperties]
,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = b.[CreatorId]
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = b.[LastModifierId]
,a.[IsDeleted] = b.[IsDeleted]
,a.[DeleterId] = b.[DeleterId]
,a.[DeletionTime] = b.[DeletionTime]
,a.[AllocationAmount] = b.[AllocationAmount]
,a.[PRDetailId] = b.[PRDetailId]
,a.[AllocationRMBAmount] = b.[AllocationRMBAmount]
,a.[PODetailId] = b.[PODetailId]
,a.[PurchaseRMBAmount] = b.[PurchaseRMBAmount]
,a.[IsAdvancePayment] = b.[IsAdvancePayment]
,a.[ProductName] = b.[ProductName]
FROM dbo.PurGRApplicationDetails a
left join #PurGRApplicationDetails  b
ON a.id=b.id;


INSERT INTO dbo.PurGRApplicationDetails
SELECT
 [Id]
,[GRApplicationId]
,[ProductId]
,[InvoiceType]
,[OrderQuantity]
,[TotalReceivedQuantity]
,[DeliveryMethod]
,[CurrentReceivingQuantity]
,[CurrentSignedQuantity]
,[UnitPrice]
,[ReceivedAmount]
,[SigningDate]
,[IsArrive]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[AllocationAmount]
,[PRDetailId]
,[AllocationRMBAmount]
,[PODetailId]
,[PurchaseRMBAmount]
,[IsAdvancePayment]
,[ProductName]
FROM #PurGRApplicationDetails a
WHERE not exists (select * from dbo.PurGRApplicationDetails where id=a.id);

--truncate table dbo.PurGRApplicationDetails
