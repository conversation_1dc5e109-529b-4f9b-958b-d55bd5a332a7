﻿using Abbott.SpeakerPortal.Contracts.Common.Attachment;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Vendor.Speaker;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Abbott.SpeakerPortal.Contracts.Vendor.Speaker.TaskCenter
{
    /// <summary>
    /// 讲者变更详情-返回DTO
    /// </summary>
    public class SpeakerChangedDetailResponseDto
    {
        /// <summary>
        /// 讲者ID
        /// </summary>
        public Guid ID { get; set; }
        /// <summary>
        /// 讲者类型
        /// </summary>
        public VendorTypes VendorType { get; set; }
        /// <summary>
        /// 讲者编号
        /// </summary>
        public string VendorCode { get; set; }
        /// <summary>
        /// 操作类型
        /// </summary>
        public ApplicationTypes ApplicationType { get; set; }
        /// <summary>
        /// 申请编号
        /// </summary>
        public string ApplicationCode { get; set; }
        /// <summary>
        /// 状态值
        /// </summary>
        public Statuses Status { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public string StatusString { get; set; }
        /// <summary>
        ///申请人ID
        /// </summary>
        public Guid ApplyUserId { get; set; }
        /// <summary>
        /// 申请人
        /// </summary>
        public string ApplyUserName { get; set; }
        /// <summary>
        ///部门ID
        /// </summary>
        public Guid ApplyUserBu { get; set; }
        /// <summary>
        /// 部门
        /// </summary>
        public string ApplyUserBuName { get; set; }
        /// <summary>
        /// 申请时间
        /// </summary>
        public string ApplyTime { get; set; }

        /// <summary>
        /// 是否是草稿
        /// </summary>
        public bool IsDraft { get; set; }
        /// <summary>
        /// 草稿版本
        /// </summary>
        public int DraftVersion { get; set; }
        /// <summary>
        /// 讲者姓名
        /// </summary>
        public string SPName { get; set; }
        /// <summary>
        /// 职称Id
        /// </summary>
        public Guid? PTId { get; set; }
        /// <summary>
        /// 职称
        /// </summary>
        public string PTIName { get; set; }
        /// <summary>
        /// 所属医院Id
        /// </summary>
        public Guid? HospitalId { get; set; }
        /// <summary>
        /// 所属医院
        /// </summary>
        public string HospitalName { get; set; }
        /// <summary>
        /// EPD医院Code，提交BU是EPD时填写
        /// </summary>
        public string EpdHospitalCode { get; set; }
        public string EpdHospitalName { get; set; }
        /// <summary>
        /// 标准科室Id
        /// </summary>
        public Guid? StandardHosDepId { get; set; }
        /// <summary>
        /// 标准科室
        /// </summary>
        public string StandardHosDepName { get; set; }
        /// <summary>
        /// 院内科室
        /// </summary>
        public string HosDepartment { get; set; }
        /// <summary>
        /// 医生主键
        /// </summary>
        public string EpdId { get; set; }
        /// <summary>
        /// 执业证书编码
        /// </summary>
        public string CertificateCode { get; set; }
        /// <summary>
        /// 讲者级别
        /// </summary>
        public string SPLevel { get; set; }
        /// <summary>
        /// 学术级别
        /// </summary>
        public string AcademicLevel { get; set; }
        /// <summary>
        /// 学会任职
        /// </summary>
        public List<AcademicPositionDto> AcademicPositionJson { get; set; }
        /// <summary>
        /// 是否是院士
        /// </summary>
        public bool IsAcademician { get; set; }
        /// <summary>
        /// 手机号码
        /// </summary>
        public string HandPhone { get; set; }
        /// <summary>
        /// 证件照片
        /// </summary>
        public AttachmentInformation CardPic { get; set; }
        ///<summary>
        /// 证件类型
        ///</summary>
        public string CardType { get; set; }
        /// <summary>
        /// 性别
        /// </summary>
        public Gender? Sex { get; set; }
        /// <summary>
        /// 证件编码
        /// </summary>
        public string CardNo { get; set; }
        /// <summary>
        /// 身份证省份
        /// </summary>
        public string Province { get; set; }
        /// <summary>
        /// 身份证省城市
        /// </summary>
        public string City { get; set; }
        /// <summary>
        /// 身份证省份城市
        /// </summary>
        public string[] ProvinceCity { get; set; }
        /// <summary>
        /// 身份证省份城市
        /// </summary>
        public string ProvinceCityName { get; set; }
        /// <summary>
        /// 身份证住址邮编
        /// </summary>
        public string PostCode { get; set; }
        /// <summary>
        /// 身份证住址
        /// </summary>
        public string Address { get; set; }
        /// <summary>
        /// 银行名称
        /// </summary>
        public string BankName { get; set; }
        /// <summary>
        /// 银行名称
        /// </summary>
        public string BankCode { get; set; }
        /// <summary>
        /// 银行账号
        /// </summary>
        public string BankCardNo { get; set; }
        /// <summary>
        /// 银行所在城市
        /// </summary>
        public string[] BankCity { get; set; }
        /// <summary>
        /// 银行所在城市
        /// </summary>
        public string BankCityName { get; set; }
        /// <summary>
        /// 收款银行联行号
        /// </summary>
        public string BankNo { get; set; }
        /// <summary>
        /// 银行SwiftCode
        /// </summary>
        public string BankSwiftCode { get; set; }

        /// <summary>
        /// 是否有DPSCheck
        /// </summary>
        public bool IncludeDPSCheck { get; set; }

        /// <summary>
        /// 银行卡正面照片
        /// </summary>
        public AttachmentInformation BankCardImg { get; set; }
        /// <summary>
        /// 原BPM学会任职
        /// </summary>
        public string FormerBPMAcademicPosition { get; set; }
        /// <summary>
        /// 财务信息
        /// </summary>
        public List<FinancialInformation> FinancialInformation { get; set; }
        /// <summary>
        /// 附件信息
        /// </summary>
        public List<AttachmentInformation> AttachmentInformation { get; set; }
        /// <summary>
        /// DPSCheck
        /// </summary>
        public List<AttachmentInformation> DPSCheck { get; set; }
        
        /// <summary>
        /// 变更前后明细
        /// </summary>
        public List<SpeakerChangedDetail> SpeakerDetailChangedCompare { get; set; }
    }

    public class SpeakerChangedDetail
    {
        /// <summary>
        /// 是否是草稿
        /// </summary>
        public bool IsDraft { get; set; }
        /// <summary>
        /// 草稿版本
        /// </summary>
        public int DraftVersion { get; set; }
        /// <summary>
        /// 讲者姓名
        /// </summary>
        public string SPName { get; set; }
        /// <summary>
        /// 职称Id
        /// </summary>
        public Guid? PTId { get; set; }
        /// <summary>
        /// 职称
        /// </summary>
        public string PTIName { get; set; }
        /// <summary>
        /// 所属医院Id
        /// </summary>
        public Guid? HospitalId { get; set; }
        /// <summary>
        /// 所属医院
        /// </summary>
        public string HospitalName { get; set; }
        /// <summary>
        /// 标准科室Id
        /// </summary>
        public Guid? StandardHosDepId { get; set; }
        /// <summary>
        /// 标准科室
        /// </summary>
        public string StandardHosDepName { get; set; }
        /// <summary>
        /// 院内科室
        /// </summary>
        public string HosDepartment { get; set; }
        /// <summary>
        /// 医生主键
        /// </summary>
        public string EpdId { get; set; }
        /// <summary>
        /// 执业证书编码
        /// </summary>
        public string CertificateCode { get; set; }
        /// <summary>
        /// 讲者级别
        /// </summary>
        public string SPLevel { get; set; }
        /// <summary>
        /// 学术级别
        /// </summary>
        public string AcademicLevel { get; set; }
        /// <summary>
        /// 学会任职
        /// </summary>
        public List<AcademicPositionDto> AcademicPositionJson { get; set; }
        /// <summary>
        /// 是否是院士
        /// </summary>
        public bool IsAcademician { get; set; }

        /// <summary>
        /// 手机号码
        /// </summary>
        public string HandPhone { get; set; }
        /// <summary>
        /// 证件照片
        /// </summary>
        public AttachmentInformation CardPic { get; set; }
        ///<summary>
        /// 证件类型
        ///</summary>
        public string CardType { get; set; }
        /// <summary>
        /// 性别
        /// </summary>
        public Gender? Sex { get; set; }
        /// <summary>
        /// 证件编码
        /// </summary>
        public string CardNo { get; set; }
        /// <summary>
        /// 身份证省份
        /// </summary>
        public string Province { get; set; }
        /// <summary>
        /// 身份证省城市
        /// </summary>
        public string City { get; set; }
        /// <summary>
        /// 身份证省份城市
        /// </summary>
        public string[] ProvinceCity { get; set; }
        /// <summary>
        /// 身份证省份城市
        /// </summary>
        public string ProvinceCityName { get; set; }
        /// <summary>
        /// 身份证住址邮编
        /// </summary>
        public string PostCode { get; set; }
        /// <summary>
        /// 身份证住址
        /// </summary>
        public string Address { get; set; }
        /// <summary>
        /// 银行名称
        /// </summary>
        public string BankName { get; set; }
        /// <summary>
        /// 银行名称
        /// </summary>
        public string BankCode { get; set; }
        /// <summary>
        /// 银行账号
        /// </summary>
        public string BankCardNo { get; set; }
        /// <summary>
        /// 银行所在城市
        /// </summary>
        public string[] BankCity { get; set; }
        /// <summary>
        /// 银行所在城市
        /// </summary>
        public string BankCityName { get; set; }
        /// <summary>
        /// 收款银行联行号
        /// </summary>
        public string BankNo { get; set; }
        /// <summary>
        /// 银行卡正面照片
        /// </summary>
        public AttachmentInformation BankCardImg { get; set; }
        /// <summary>
        /// 银行SwiftCode
        /// </summary>
        public string BankSwiftCode { get; set; }

        /// <summary>
        /// 财务信息
        /// </summary>
        public List<FinancialInformation> FinancialInformation { get; set; }
        /// <summary>
        /// 附件信息
        /// </summary>
        public List<AttachmentInformation> AttachmentInformation { get; set; }
    }
}
