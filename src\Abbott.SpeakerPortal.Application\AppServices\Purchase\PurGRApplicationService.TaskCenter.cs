﻿using Abbott.SpeakerPortal.AppServices.Common;
using Abbott.SpeakerPortal.AppServices.Vendor;
using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Approval;
using Abbott.SpeakerPortal.Contracts.Budget.Subbudget;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.Approve;
using Abbott.SpeakerPortal.Contracts.Common.PowerApp;
using Abbott.SpeakerPortal.Contracts.Common.SMS;
using Abbott.SpeakerPortal.Contracts.Purchase.PurGRApplication;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.Budget.BdExpense;
using Abbott.SpeakerPortal.Entities.Common.Attachment;
using Abbott.SpeakerPortal.Entities.Purchase.PurGRApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPOApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Entities.VendorApplicationFinancials;
using Abbott.SpeakerPortal.Entities.VendorApplications;
using Abbott.SpeakerPortal.Entities.VendorFinancials;
using Abbott.SpeakerPortal.Entities.VendorOrgnizations;
using Abbott.SpeakerPortal.Entities.VendorPersonals;
using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Utils;
using Abbott.SpeakerPortal.Vendor.Speaker;
using DocumentFormat.OpenXml.VariantTypes;
using DocumentFormat.OpenXml.Wordprocessing;
using Microsoft.Azure.CognitiveServices.Vision.ComputerVision.Models;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Senparc.CO2NET.Extensions;
using Senparc.Weixin.MP.AdvancedAPIs.MerChant;
using Senparc.Weixin.WxOpen.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using Volo.Abp.ObjectMapping;
using static Abbott.SpeakerPortal.Enums.Purchase;
using static Abbott.SpeakerPortal.Enums.PurGRStatus;

namespace Abbott.SpeakerPortal.AppServices.Purchase
{
    public partial class PurGRApplicationService : SpeakerPortalAppService, IPurGRApplicationService
    {
        /// <summary>
        /// 终止收货
        /// </summary>
        /// <param name="grId"></param>
        /// <param name="reason"></param>
        /// <returns></returns>
        public async Task<MessageResult> AbrogationReceiveAsync(Guid grId, string reason = "")
        {
            var grApplicationRepository = LazyServiceProvider.GetService<IPurGRApplicationRepository>();
            var poQuery = await LazyServiceProvider.GetService<IPurPOApplicationRepository>().GetQueryableAsync();
            var prQuery = await LazyServiceProvider.GetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var grQuery = await grApplicationRepository.GetQueryableAsync();
            var grDetailQuery = await LazyServiceProvider.GetService<IPurGRApplicationDetailRepository>().GetQueryableAsync();
            var prDetailQuery = await LazyServiceProvider.GetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var paQuery = await LazyServiceProvider.GetService<IPurPAApplicationRepository>().GetQueryableAsync();
            var paInvoiceQuery = await LazyServiceProvider.GetService<IPurPAApplicationDetailRepository>().GetQueryableAsync();
            var poDetailsQuery = await LazyServiceProvider.GetService<IPurPOApplicationDetailsRepository>().GetQueryableAsync();
            var approveService = LazyServiceProvider.LazyGetService<IApproveService>();
            var gr = grQuery.Where(a => a.Id == grId).FirstOrDefault();

            if (gr == null || (gr.Status != PurGRApplicationStatus.ToBeReceived && gr.Status != PurGRApplicationStatus.Returned))
                return MessageResult.FailureResult("未找到符合条件的相关收货单");

            switch (gr.PayMethod)
            {
                case PayMethods.AR: //1.AR支付方式的收货申请直接终止成功，流程结束，预算及讲者的金额都进行返还
                    gr.Status = PurGRApplicationStatus.Terminationed;
                    await grApplicationRepository.UpdateAsync(gr, true);
                    var pr = prQuery.Where(a => a.Id == gr.PrId).FirstOrDefault();
                    var grDetails = grDetailQuery.Where(a => a.GRApplicationId == gr.Id).ToList();
                    var prDetails = prDetailQuery.Where(a => grDetails.Select(b => b.PRDetailId).ToList().Contains(a.Id)).ToList();

                    var pas = paQuery.GroupJoin(paInvoiceQuery, a => a.Id, b => b.PurPAApplicationId, (a, b) => new { pa = a, pads = b })
                        .SelectMany(a => a.pads.DefaultIfEmpty(), (a, b) => new { a.pa, pad = b })
                        .Where(a => a.pa.Status != PurPAStatus.PurPAApplicationStatus.Void && grDetails.Select(x => x.PRDetailId).ToList().Contains(a.pad.PRDetailId))
                        .ToList(); //排除作废的
                    var paInvoices = pas.Select(a => a.pad).ToList();//GR对应的PR 查找的PA明细
                    #region AR付款方式 预算返还
                    //AR付款方式=> GR(返还): 终止收货时，退回每行[PR申请金额-已付款含税金额]
                    var useBudgetRequest = new ReturnBudgetRequestDto
                    {
                        PrId = gr.PrId,
                        SubbudgetId = pr.SubBudgetId.Value,
                        Items = prDetails.Select(a => new ReturnInfo
                        {
                            PdRowNo = a.RowNo,
                            ReturnAmount = a.TotalAmount > 0 ? (a.TotalAmount.Value - paInvoices.Where(x => x.PRDetailId == a.Id).Sum(x => x.PaymentAmount)) * (decimal)gr.ExchangeRate : 0M,
                            ReturnSourceId = gr.Id,
                            ReturnSourceCode = gr.ApplicationCode
                        })
                    };
                    if (useBudgetRequest.Items.Any(a => a.ReturnAmount > 0))
                    {
                        useBudgetRequest.Items = useBudgetRequest.Items.Where(a => a.ReturnAmount > 0);//返还未使用完的预算
                        await LazyServiceProvider.LazyGetService<ISubbudgetService>().ReturnSubbudgetAsync(useBudgetRequest);
                    }
                    #endregion
                    break;
                case PayMethods.AP:
                    //2.PO单的收货申请终止收货提交后，需要PO的采购人进行审批，审批通过后终止收货成功，流程结束，预算及讲者的金额都进行返还；审批不通过则终止收货失败"
                    //创建审批任务
                    if (gr.PoId.HasValue)
                    {
                        var po = poQuery.Where(a => a.Id == gr.PoId).FirstOrDefault();
                        var draftIds = new List<Guid>() { po.ApplyUserId };
                        var createOK = await CreateWorkflowAsync(gr, draftIds, WorkflowTypeName.TerminationOfReceipt);//PO申请人为采购人
                        if (!createOK)
                            return MessageResult.FailureResult("审批任务创建失败");
                        //修改业务状态
                        gr.Status = PurGRApplicationStatus.Termination;
                        await grApplicationRepository.UpdateAsync(gr, true);
                    }
                    else
                    {
                        return MessageResult.FailureResult("PO不存在，请确认");
                    }
                    break;
                default:
                    return MessageResult.FailureResult("该付款方式的收货申请单不能终止收货");
            }
            await approveService.AddApprovalRecordAsync(new AddApprovalRecordDto()
            {
                FormId = gr.Id,
                ApprovalId = CurrentUser.Id.Value,
                OriginalApprovalId = CurrentUser.Id.Value,
                Status = ApprovalOperation.Receive,
                Remark = reason,
                ApprovalTime = DateTime.Now,
                WorkStep = "终止收货",
                Name = "终止收货"
            });
            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 收货操作
        /// </summary>
        /// <param name="prDetailId"></param>
        public async Task<MessageResult> UpdateGRApplicationAsync(PurGRApplicationRequestDto request)
        {
            var _paApplicationService = LazyServiceProvider.GetService<IPurPAApplicationService>();
            var _identityUserRepository = LazyServiceProvider.GetService<IRepository<IdentityUser, Guid>>();
            var _dataverseService = LazyServiceProvider.GetService<IDataverseService>();
            var vendor = await LazyServiceProvider.GetService<IVendorRepository>().GetQueryableAsync();
            var vendorPersonal = await LazyServiceProvider.GetService<IVendorPersonalRepository>().GetQueryableAsync();
            var vendorOrg = await LazyServiceProvider.LazyGetService<IVendorOrgnizationRepository>().GetQueryableAsync();
            var commonService = LazyServiceProvider.LazyGetService<ICommonService>();
            var prApplicationDetailRepository = LazyServiceProvider.GetService<IPurPRApplicationDetailRepository>();
            var queryPrApplicationDetail = await prApplicationDetailRepository.GetQueryableAsync();
            var queryPr = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var grApplicationRepository = LazyServiceProvider.GetService<IPurGRApplicationRepository>();
            var grApplicationDetailRepository = LazyServiceProvider.GetService<IPurGRApplicationDetailRepository>();
            var grDetailHistoryRepository = LazyServiceProvider.GetService<IPurGRApplicationDetailHistoryRepository>();
            var queryHistory = await grDetailHistoryRepository.GetQueryableAsync();
            var queryPoDetail = await LazyServiceProvider.GetService<IPurPOApplicationDetailsRepository>().GetQueryableAsync();
            var queryPo = await LazyServiceProvider.GetService<IPurPOApplicationRepository>().GetQueryableAsync();
            var approveService = LazyServiceProvider.LazyGetService<IApproveService>();
            var prQuery = await LazyServiceProvider.GetService<IPurPRApplicationRepository>().GetQueryableAsync();

            //修改GR申请单
            var entityGRApplication = await grApplicationRepository.FirstOrDefaultAsync(f => f.Id == request.Id && (f.Status == PurGRApplicationStatus.ToBeReceived || f.Status == PurGRApplicationStatus.Returned));
            if (entityGRApplication == null) return MessageResult.FailureResult("未找到符合条件的相关收货单");
            var pr = prQuery.Where(a => a.Id == entityGRApplication.PrId).FirstOrDefault();
            if (request.PurGRApplicationDetails == null || !request.PurGRApplicationDetails.Any())
                return MessageResult.FailureResult("收货明细不能为空");

            var entityGRDetail = (await grApplicationDetailRepository.GetQueryableAsync()).Where(f => f.GRApplicationId == request.Id).ToList();
            var prDetailIds = entityGRDetail.Select(a => a.PRDetailId).Distinct().ToList();
            var prDetail = queryPrApplicationDetail.Where(a => prDetailIds.Contains(a.Id))
               .GroupJoin(queryPr, pd => pd.PRApplicationId, pr => pr.Id, (pd, pr) => new { pd, p = pr.FirstOrDefault() })
               .ToList();
            //AR都是每一行生成一张收货单,有一个AR则表示为AR方式 并且 是0元讲者
            if (prDetail.Any(a => a.pd.PayMethod == PayMethods.AR) && prDetail.Any(a => (a.pd.TotalAmount ?? 0) == 0))//0元讲者
            {
                if ((request.PSAFiles == null || (!request.PSAFiles.Any())) && (prDetail.FirstOrDefault().p.IsEsignUsed.HasValue == false || prDetail.FirstOrDefault().p.IsEsignUsed == false))//非电子签章会议并且PSA附件没有上传需要提示
                    return MessageResult.FailureResult("0元讲者请上传PSA文件");
                var updateGR = ObjectMapper.Map(request, entityGRApplication);
                if (request.PSAFiles != null)
                    updateGR.PSAIds = string.Join(',', request.PSAFiles.Select(a => a.AttachmentId).ToList());
                //如果有加签人，则需要走审批流程
                if (!string.IsNullOrWhiteSpace(request.AdditionalSignerId))
                {
                    //创建审批任务
                    var draftIds = entityGRApplication.AdditionalSignerId.Split(',').Select(Guid.Parse).ToList();
                    var createOK = await CreateWorkflowAsync(entityGRApplication, draftIds);
                    if (!createOK)
                        return MessageResult.FailureResult("审批任务创建失败");

                    //修改业务状态
                    entityGRApplication.Status = PurGRApplicationStatus.SignedBy;
                    entityGRApplication.ApplyTime = DateTime.UtcNow.AddHours(8);
                }
                else
                {
                    updateGR.Status = PurGRApplicationStatus.ReceivedGoods;
                    updateGR.ReceivedTime = DateTime.Now;
                }
                await grApplicationRepository.UpdateAsync(updateGR, true);
                return MessageResult.SuccessResult();
            }
            var historys = queryHistory.Where(a => a.GRApplicationId == entityGRApplication.Id).ToList();
            var prDetails = queryPrApplicationDetail.Where(a => a.PRApplicationId == entityGRApplication.PrId).ToList(); //PrId 业务流程上是必须存在的字段，PoId 可空
            PurPOApplication po = null;
            List<PurPOApplicationDetails> poDetails = new List<PurPOApplicationDetails>();
            if (entityGRApplication.PoId.HasValue)
            {
                po = queryPo.Where(a => a.Id == entityGRApplication.PoId.Value).FirstOrDefault();
                poDetails = queryPoDetail.Where(a => a.POApplicationId == po.Id).ToList();
            }
            if (entityGRApplication.PayMethod == PayMethods.AR && request.IsAdvancePayment) //AR明细行，若费用性质标记为"是否允许预付款"=yes  则可以预付款，否则不允许预付款
            {
                var prDetailByAR = queryPrApplicationDetail.Where(a => prDetailIds.Contains(a.Id)).FirstOrDefault();//GR AR 付款方式只对应一条PR明细
                var costNature = (await _dataverseService.GetCostNatureAsync(prDetailByAR.CostNature.Value.ToString())).FirstOrDefault();
                if (costNature.IsAdvancePayment != true)
                    return MessageResult.FailureResult($"费用性质（{prDetailByAR.CostNatureName}）:不允许预付款");
            }

            if (!entityGRApplication.IsAdvancePayment && request.IsAdvancePayment)//本次收货操作前的 是否预付款
            {
                var grApplicationDetails = new List<PurGRApplicationDetailsDto>();
                var totalAmount = entityGRDetail.Sum(a => a.OrderQuantity * a.UnitPrice);//所有可收金额
                if (request.PaymentExcludingTaxAmount > totalAmount)
                    return MessageResult.FailureResult("预付款金额不能大于可收货总金额");
                foreach (var item in entityGRDetail)
                {
                    var detail = ObjectMapper.Map<PurGRApplicationDetail, PurGRApplicationDetailsDto>(item);
                    detail.IsAdvancePayment = true;
                    var taxRate = 0M;
                    float exchangeRate = entityGRApplication.ExchangeRate;
                    if (PayMethods.AP.Equals(entityGRApplication.PayMethod) && entityGRApplication.PoId.HasValue)
                    {
                        var thisPoDetail = poDetails.Where(a => a.Id == item.PODetailId).FirstOrDefault();
                        if (!InvoiceType.GiftIncrease.Equals(thisPoDetail.InvoiceType)) //礼品增票 税率为0
                        {
                            taxRate = decimal.Parse(thisPoDetail?.TaxRate ?? "0");
                        }
                    }
                    var receivedAmount = (request.PaymentExcludingTaxAmount ?? 0M) * ((item.OrderQuantity * item.UnitPrice) / totalAmount);

                    detail.ReceivedAmount = receivedAmount * (1 + taxRate);//计算含税金额  签收金额存含税金额
                    detail.SigningDate = DateTime.Now;
                    detail.AllocationAmount = receivedAmount; //PR分摊 本身预付款是不含税金额，此处不需要税率计算
                    detail.AllocationRMBAmount = detail.AllocationAmount * (decimal)exchangeRate;
                    detail.TaxRate = taxRate;
                    grApplicationDetails.Add(detail);
                }
                request.PurGRApplicationDetails = grApplicationDetails;//替换前端请求参数
            }
            else
            {
                foreach (var item in request.PurGRApplicationDetails)
                {
                    //本次收到需要大于等于本次签收不是预付款才校验
                    if (item.CurrentReceivingQuantity < item.CurrentSignedQuantity)
                        return MessageResult.FailureResult("本次收到需要大于等于本次签收");
                }
            }
            if (request.PurGRApplicationDetails.Any(a => a.ReceivedAmount > 0) == false)
            {
                return MessageResult.FailureResult("至少需要有一个品名的签收金额大于0");
            }

            foreach (var item in prDetailIds)//GR 对应的PR 明细ID
            {
                var prDetailTotalAmount = prDetails.FirstOrDefault(a => a.Id == item).TotalAmount ?? 0M;
                var receivedAmount = historys.Where(a => a.PRDetailId == item).Sum(a => a.ReceivedAmount ?? 0);//PR详情已收货人民币金额（含税）
                var thisReceivedAmount = request.PurGRApplicationDetails.Where(a => a.PRDetailId == item).Sum(a => a.ReceivedAmount) ?? 0M;//本次次签收人民币金额
                if (Math.Round(receivedAmount + thisReceivedAmount, 2) > Math.Round(prDetailTotalAmount, 2))
                    return MessageResult.FailureResult("已签收金额加本次签收金额不能大于PR总金额");
            }

            if (PayMethods.AP.Equals(entityGRApplication.PayMethod))//AP 从PO来的数据
            {
                var rate = (await _dataverseService.GetCurrencyConfig(DictionaryType.CurrencyItems.USD)).FirstOrDefault();
                foreach (var item in request.PurGRApplicationDetails)
                {
                    var poDetail = poDetails.Where(a => a.Id == item.PODetailId.Value).FirstOrDefault();
                    var totalAmount10 = (poDetail.TotalAmount * (1M + 0.1M)) * request.ExchangeRate;//PO含税的10%
                    var totalAmount685 = poDetail.TotalAmount * request.ExchangeRate + (rate.PlanRate * 10000);//PO含税金额6.85万人民币(1万美元)
                    var grTotalAmount = (historys.Where(a => a.PODetailId == item.PODetailId).Sum(a => a.ReceivedAmount) ?? 0M) * request.ExchangeRate;//已经收货金额人民币
                    var receivedAmount = (item.ReceivedAmount ?? 0M) * request.ExchangeRate;//本次收货人民币金额
                    if (grTotalAmount + receivedAmount > totalAmount10 || grTotalAmount + receivedAmount > totalAmount685)
                        return MessageResult.FailureResult("收货总金额不能超过PO含税总金额的10%且不能超过PO含税总金额1万美元");
                }
            }
            #region 1、收货预算（扣除）使用  比PO多收才扣除
            var useBudgetRequest = new ReturnBudgetRequestDto();
            if (PayMethods.AP.Equals(entityGRApplication.PayMethod))//AP 从PO来的数据
            {
                var prItemDetails = prDetails.Where(a => request.PurGRApplicationDetails.Where(x => x.ReceivedAmount > 0).Select(x => x.PRDetailId).Distinct().ToList().Contains(a.Id)).ToList();//当前GR明细对应的PR （并且签收金额要大于0的）
                //收货金额不含税 = 历史签收+本次签收
                //发起收货->增加归还预算记录 = -(收货金额不含税-PO不含税金额)
                useBudgetRequest = new ReturnBudgetRequestDto
                {
                    PrId = pr.Id,
                    SubbudgetId = pr.SubBudgetId.Value,
                    Items = prItemDetails.Select(a =>
                    {
                        var poDetail = poDetails.Where(o => o.PRDetailId == a.Id).ToList();
                        var grTotalAmount = historys.Where(h => h.PRDetailId == a.Id).Sum(h =>
                        {
                            var taxRate = 0M;
                            var pod = poDetail.FirstOrDefault(x => x.Id == h.PODetailId);
                            if (!InvoiceType.GiftIncrease.Equals(pod.InvoiceType))
                            {
                                decimal.TryParse(pod?.TaxRate, out taxRate);
                            }
                            return h.ReceivedAmount > 0 ? (h.ReceivedAmount / (1 + taxRate)) : 0M;
                        }) * request.ExchangeRate;//已经收货金额人民币(不含税)

                        var receivedAmount = request.PurGRApplicationDetails.Where(g => g.PRDetailId == a.Id).Sum(g =>
                        {
                            var taxRate = 0M;
                            var pod = poDetail.FirstOrDefault(x => x.Id == g.PODetailId);
                            if (!InvoiceType.GiftIncrease.Equals(pod.InvoiceType))
                            {
                                decimal.TryParse(pod?.TaxRate, out taxRate);
                            }
                            return g.ReceivedAmount > 0 ? (g.ReceivedAmount / (1 + taxRate)) : 0M;
                        }) * request.ExchangeRate;//PR明细本次收货人民币金额(不含税)
                        var totalAmount = grTotalAmount + receivedAmount;//含税金额(不含税)
                        var amountNoTax = poDetail.Sum(p => p.TotalAmountNoTax) * request.ExchangeRate;//PO不含税人民币
                        var returnAmount = -(totalAmount - amountNoTax);//转人民币
                        return new ReturnInfo()
                        {
                            PdRowNo = a.RowNo,
                            ReturnSourceId = entityGRApplication.Id,
                            ReturnSourceCode = entityGRApplication.ApplicationCode,
                            ReturnAmount = returnAmount.HasValue ? returnAmount.Value : 0M//转人民币
                        };
                    })
                };
                if (useBudgetRequest.Items.Any(a => a.ReturnAmount < 0))
                {
                    useBudgetRequest.Items = useBudgetRequest.Items.Where(a => a.ReturnAmount < 0);
                    UseBudgetRequestDto useBudget = new UseBudgetRequestDto()
                    {
                        PrId = useBudgetRequest.PrId,
                        SubbudgetId = useBudgetRequest.SubbudgetId,
                        Items = useBudgetRequest.Items.Select(a => new UseInfo
                        {
                            PdRowNo = a.PdRowNo,
                            UseAmount = -a.ReturnAmount//使用预算取反
                        })
                    };
                    var useResult = await LazyServiceProvider.LazyGetService<ISubbudgetService>().CheckSubbudgetAmountSufficientAsync(useBudget);
                    if (!useResult.Success)
                    {
                        return useResult;//预算不够
                    }
                }
            }
            #endregion

            var entityGR = ObjectMapper.Map(request, entityGRApplication);
            await grApplicationRepository.UpdateAsync(entityGR, true);
            //添加GR明细
            foreach (var item in entityGRDetail)
            {
                var requestGRDetail = request.PurGRApplicationDetails.Where(o => o.DetailId == item.Id).FirstOrDefault();
                ObjectMapper.Map(requestGRDetail, item);
                item.CurrentSignedQuantity = requestGRDetail.CurrentSignedQuantity;
                item.CurrentReceivingQuantity = requestGRDetail.CurrentReceivingQuantity;
            }
            await grApplicationDetailRepository.UpdateManyAsync(entityGRDetail, true);
            //如果有加签人，则需要走审批流程
            if (!string.IsNullOrWhiteSpace(request.AdditionalSignerId))
            {
                //创建审批任务
                var draftIds = entityGRApplication.AdditionalSignerId.Split(',').Select(Guid.Parse).ToList();
                var createOK = await CreateWorkflowAsync(entityGRApplication, draftIds);
                if (!createOK)
                    return MessageResult.FailureResult("审批任务创建失败");

                //修改业务状态
                entityGRApplication.Status = PurGRApplicationStatus.SignedBy;
                entityGRApplication.ApplyTime = DateTime.UtcNow.AddHours(8);
            }
            else
            {
                var grDetailHistorys = ObjectMapper.Map<List<PurGRApplicationDetail>, List<PurGRApplicationDetailHistory>>(entityGRDetail.ToList());
                if (request.IsAdvancePayment && !historys.Any())
                    grDetailHistorys.ForEach(a => a.IsAdvancePayment = true);
                await grDetailHistoryRepository.InsertManyAsync(grDetailHistorys, true);
                //发起付款款申请 每创建一条收款申请 需要更新 历史收货记录中的收款申请ID、更改GR收货状态
                await _paApplicationService.CreatePurPAApplicationAsync(new CreatePurPAApplicationDto()
                {
                    GRId = entityGRApplication.Id,
                    GrDetailHistoryIds = grDetailHistorys.Select(a => a.Id).ToList(),
                });
                entityGRApplication.Status = PurGRApplicationStatus.PaymentProgress;
                await approveService.AddApprovalRecordAsync(new AddApprovalRecordDto()
                {
                    FormId = entityGRApplication.Id,
                    ApprovalId = entityGRApplication.ApplyUserId,
                    Status = ApprovalOperation.Receive,
                    Remark = request.Remarks,
                    ApprovalTime = DateTime.Now,
                    WorkStep = "收货",
                    Name = "收货"
                });//创建收货审批历史记录（假的审批记录—不走真正流程-为审批历史新增可查询记录）
            }
            #region 2、收货预算（扣除）使用  比PO多收才扣除
            if (PayMethods.AP.Equals(entityGRApplication.PayMethod))//AP 从PO来的数据
            {
                if (useBudgetRequest.Items.Any(a => a.ReturnAmount < 0))
                {
                    useBudgetRequest.Items = useBudgetRequest.Items.Where(a => a.ReturnAmount < 0);
                    var rsult = await LazyServiceProvider.LazyGetService<ISubbudgetService>().ReturnSubbudgetAsync(useBudgetRequest);
                    if (rsult.Success)
                    {
                        //记录使用时间，加签人退回 返还后清空、加签人同意 后PA复审通过后清空（这个时候PA不能作废了)
                        entityGRApplication.UseBudgetTime = (DateTime)rsult.Data;
                    }
                }
            }
            #endregion
            await grApplicationRepository.UpdateAsync(entityGRApplication, true);
            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 多使用预算后的返还(支持GR、PA)
        /// </summary>
        /// <param name="prId"></param>
        /// <param name="sourceId"></param>
        /// <param name="useBudgetTime"></param>
        /// <returns></returns>
        public async Task ReturnSubbudgetAsync(Guid prId, Guid sourceId, DateTime useBudgetTime)
        {
            var returnSubbudgetQuery = await LazyServiceProvider.LazyGetService<IBdBudgetReturnRepository>().GetQueryableAsync();
            var subbudget = returnSubbudgetQuery.Where(a => a.PrId == prId && a.ReturnSourceId == sourceId && a.OperateTime == useBudgetTime).ToList();
            if (subbudget.Any())
            {
                var returnInfo = new List<ReturnInfo>();
                foreach (var item in subbudget)
                {
                    returnInfo.Add(new ReturnInfo
                    {
                        PdRowNo = item.PdRowNo,
                        ReturnSourceId = item.ReturnSourceId,
                        ReturnSourceCode = item.ReturnSourceCode,
                        ReturnAmount = -item.Amount,//因为提交时的使用 传入的负数 这里取反
                    });
                }
                var useBudgetRequest = new ReturnBudgetRequestDto
                {
                    PrId = prId,
                    SubbudgetId = subbudget.FirstOrDefault().SubbudgetId,
                    Items = returnInfo
                };
                await LazyServiceProvider.LazyGetService<ISubbudgetService>().ReturnSubbudgetAsync(useBudgetRequest);//多使用的预算返还
            }
        }

        /// <summary>
        /// 添加审批任务
        /// </summary>
        /// <param name="request"></param>
        /// <param name="purExemptDetail"></param>
        /// <returns></returns>
        private async Task<bool> CreateWorkflowAsync(PurGRApplication request, List<Guid> draftIds, WorkflowTypeName workflowTypeName = WorkflowTypeName.ReceiptRequest)
        {
            var approvalRecord = LazyServiceProvider.LazyGetService<IInitializeApprovalRecordService>();
            //创建审批任务
            var exemptType = EnumUtil.GetEnumIdValues<WorkflowTypeName>().ToDictionary(a => (WorkflowTypeName)a.Key, a => a.Value);

            var createApproval = new CreateApprovalDto
            {
                Name = exemptType[workflowTypeName],
                Department = request.ApplyUserBu.ToString(),
                BusinessFormId = request.Id.ToString(),
                BusinessFormNo = request.ApplicationCode,
                BusinessFormName = EntitiesNameConsts.NameConsts.GRApplication,
                Submitter = CurrentUser.Id?.ToString(),
                OriginalApprovalId = request.ApplyUserId,
                //FormData = JsonSerializer.Serialize(new { approver = draftIds }),
                FirstApprover = string.Join(",", draftIds),
                WorkflowType = workflowTypeName,
                InstanceName = $"{exemptType[workflowTypeName]}-{DateTime.Now.ToString("yyMMdd-hhmmss")}",
                //Remark = request.Remarks
            };
            await approvalRecord.InsertInitApprovalRecordAsync(new InitializeApprovalRecordDto
            {
                BusinessFormName = EntitiesNameConsts.NameConsts.GRApplication,
                BusinessId = request.Id,
                InstanceId = Guid.NewGuid(),
                Status = InitApprovalRecordStatus.Pending,
                MaxRetries = 3,
                RequestContent = JsonSerializer.Serialize(createApproval),
            });
            return true;
        }

        /// <summary>
        /// 终止收货(把GR的部分明细行进行终止收货)
        /// </summary>
        /// <param name="paId">来源PA单的Id</param>
        /// <param name="paCode">来源PA单的Code</param>
        /// <param name="grId">要处理的GR</param>
        /// <param name="excludePRDIds">要排除的PRDIds，其他的明细行要终止收货</param>
        /// <param name="subBudgetId">PR关联的子预算Id</param>
        /// <returns></returns>
        public async Task<MessageResult> AbrogationReceiveWithPartGRAsync(Guid paId, string paCode, Guid grId, IEnumerable<Guid> excludePRDIds, Guid subBudgetId)
        {
            var grQuery = await LazyServiceProvider.GetService<IPurGRApplicationRepository>().GetQueryableAsync();

            var grDetailQuery = await LazyServiceProvider.GetService<IPurGRApplicationDetailRepository>().GetQueryableAsync();
            var prDetailQuery = await LazyServiceProvider.GetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var gr = grQuery.Where(a => a.Id == grId).FirstOrDefault();

            var paQuery = await LazyServiceProvider.GetService<IPurPAApplicationRepository>().GetQueryableAsync();
            var paInvoiceQuery = await LazyServiceProvider.GetService<IPurPAApplicationDetailRepository>().GetQueryableAsync();

            var grDetails = grDetailQuery.Where(a => a.GRApplicationId == grId).ToList();
            var prIdsToReturnBudget = grDetails.Select(x => x.PRDetailId).Except(excludePRDIds);
            if (!prIdsToReturnBudget.Any())
                return MessageResult.SuccessResult();

            var prDetails = prDetailQuery.Where(a => prIdsToReturnBudget.ToHashSet().Contains(a.Id)).Select(a => new { a.Id, a.RowNo, a.TotalAmount }).ToList();

            var pas = paQuery.GroupJoin(paInvoiceQuery, a => a.Id, b => b.PurPAApplicationId, (a, b) => new { pa = a, pads = b })
                .SelectMany(a => a.pads.DefaultIfEmpty(), (a, b) => new { a.pa, pad = b })
                .Where(a => a.pa.Status != PurPAStatus.PurPAApplicationStatus.Void && prIdsToReturnBudget.ToHashSet().Contains(a.pad.PRDetailId))
                .ToList(); //排除作废的
            var paInvoices = pas.Select(a => a.pad).ToList();//GR对应的PR 查找的PA明细

            //AR付款方式=> GR(返还): 终止收货时，退回每行[PR申请金额-已付款含税金额]
            var useBudgetRequest = new ReturnBudgetRequestDto
            {
                PrId = gr.PrId,
                SubbudgetId = subBudgetId,
                Items = prDetails.Select(a => new ReturnInfo
                {
                    PdRowNo = a.RowNo,
                    ReturnAmount = a.TotalAmount > 0 ? (a.TotalAmount.Value - paInvoices.Where(x => x.PRDetailId == a.Id).Sum(x => x.PaymentAmount)) * (decimal)gr.ExchangeRate : 0M,
                    ReturnSourceId = paId,
                    ReturnSourceCode = paCode
                })
            };
            if (useBudgetRequest.Items.Any(a => a.ReturnAmount > 0))
            {
                useBudgetRequest.Items = useBudgetRequest.Items.Where(a => a.ReturnAmount > 0);//返还未使用完的预算
                await LazyServiceProvider.LazyGetService<ISubbudgetService>().ReturnSubbudgetAsync(useBudgetRequest);
            }

            return MessageResult.SuccessResult();
        }
    }
}
