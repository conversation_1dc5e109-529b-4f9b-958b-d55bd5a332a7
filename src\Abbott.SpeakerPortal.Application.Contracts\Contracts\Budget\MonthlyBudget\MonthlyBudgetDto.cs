﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Text;
using Abbott.SpeakerPortal.Enums;
using System.Text.Json.Serialization;

namespace Abbott.SpeakerPortal.Contracts.Budget
{
    public class MonthlyBudgetDto
    {
        [JsonIgnore]
        public Guid Id { get; set; } = Guid.NewGuid();
        /// <summary>
        /// 预算金额
        /// </summary>
        public decimal BudgetAmount { get; set; }
        /// <summary>
        /// 是否可用
        /// </summary>
        public bool Status { get; set; } = false;
        /// <summary>
        /// 月份
        /// </summary>
        public Month Month { get; set; }
    }
    public class MonthlyBudgetNullAbleDto
    {
        [JsonIgnore]
        public Guid Id { get; set; } = Guid.NewGuid();
        /// <summary>
        /// 预算金额
        /// </summary>
        public decimal? BudgetAmount { get; set; }
        /// <summary>
        /// 是否可用
        /// </summary>
        public bool? Status { get; set; } = false;
        /// <summary>
        /// 月份
        /// </summary>
        public Month Month { get; set; }
    }
}
