﻿using Abbott.SpeakerPortal.Dtos;
using Abbott.SpeakerPortal.Enums;
using System;
using static Abbott.SpeakerPortal.Enums.PurPAStatus;

namespace Abbott.SpeakerPortal.Contracts.Purchase.PurPAApplication
{
    public class PAListSearchRequestDto : PagedDto
    {
        /// <summary>
        /// 任务中心处理状态
        /// </summary>
        public ProcessingStatus? ProcessingStatus { get; set; }
        /// <summary>
        /// 申请单编号
        /// </summary>
        public string ApplicationCode { get; set; }

        /// <summary>
        /// Pr申请单编号
        /// </summary>
        public string PrApplicationCode { get; set; }

        /// <summary>
        /// Po申请单编号
        /// </summary>
        public string PoApplicationCode { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string VendorName { get; set; }

        /// <summary>
        /// 申请部门名
        /// </summary>
        public string ApplyUserBuName { get; set; }

        /// <summary>
        /// 申请人名字
        /// </summary>
        public string ApplyUserName { get; set; }
        /// <summary>
        /// 申请人ID
        /// </summary>
        public Guid? ApplyUserId { get; set; }
        /// <summary>
        /// 开始申请时间
        /// </summary>
        public DateTime? StartApplyTime { get; set; }

        /// <summary>
        /// 结束申请时间
        /// </summary>
        public DateTime? EndApplyTime { get; set; }

        /// <summary>
        /// 订单状态（收货申请状态）
        /// </summary>
        public PurPAApplicationStatus? Status { get; set; }

        /// <summary>
        /// 收货单号
        /// </summary>
        public string GRApplicationCode { get; set; }

        /// <summary>
        /// (支付)付款形式
        /// </summary>
        public PaymentTypes? PaymentType { get; set; }

        /// <summary>
        /// 接收开始时间
        /// </summary>
        public DateTime? AcceptedStartTime { get; set; }

        /// <summary>
        /// 接收结束时间
        /// </summary>
        public DateTime? AcceptedEndTime { get; set; }

        /// <summary>
        /// 接收人
        /// </summary>
        public string AccepterName { get; set; }

        /// <summary>
        /// 费用类型
        /// </summary>
        public PAExpenseType? ExpenseType { get; set; }

        /// <summary>
        /// 递交方式
        /// </summary>
        public DeliveryModes? DeliveryMode { get; set; }
        
        /// <summary>
        /// 是否升序
        /// </summary>
        public bool IsAsc { get; set; }
    }
}
