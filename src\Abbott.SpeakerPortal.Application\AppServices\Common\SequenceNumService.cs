﻿using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.Common;
using Abbott.SpeakerPortal.Contracts.Dataverse;
using Abbott.SpeakerPortal.Contracts.System.User;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.Common.Attachment;
using Abbott.SpeakerPortal.Entities.Common.Sequence;
using Abbott.SpeakerPortal.Entities.UserRoles.UserExtension;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Redis;
using AutoMapper.Internal.Mappers;
using EFCore.BulkExtensions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using Volo.Abp.DistributedLocking;
using Volo.Abp.Domain.Repositories;
using RedisKey = Abbott.SpeakerPortal.Consts.RedisKey;

namespace Abbott.SpeakerPortal.AppServices.Common
{
    public class SequenceNumService : SpeakerPortalAppService, ISequenceNumService
    {
        /// <summary>
        /// The service provider
        /// </summary>
        private readonly IServiceProvider _serviceProvider;

        /// <summary>
        /// The logger
        /// </summary>
        private readonly ILogger<SequenceNumService> _logger;

        readonly IRedisRepository _redisRepository;

        public SequenceNumService(IServiceProvider serviceProvider, IRedisRepository redisRepository)
        {
            _serviceProvider = serviceProvider;
            _logger = serviceProvider.GetService<ILogger<SequenceNumService>>();
            _redisRepository = redisRepository;
        }

        public string GetNextFileNameAndIncrease(SequenceNumCategory category)
        {
            //seqNum不会为空
            var seqNum = GetNextNumAndIncrease(category);

            return $"{seqNum.Category}{seqNum.NextNum.ToString($"D{seqNum.NumLength}")}.TXT";
        }

        public SequenceNumDto GetNextNumAndIncrease(SequenceNumCategory category)
        {
            SequenceNumDto result = null;
            switch (category)
            {
                //以下每个Case单独用一把锁！
                case SequenceNumCategory.V:
                    //var repo = LazyServiceProvider.LazyGetService<ISequenceNumRepository>();
                    ////获取NextNum
                    //var seqNumV = repo.GetAsync(a=>a.Category == SequenceNumCategory.V.ToString()).GetAwaiter().GetResult();
                    //result = seqNumV.NextNum;
                    ////自增NextNum
                    //seqNumV.NextNum += 1;
                    //repo.UpdateAsync(seqNumV, true).GetAwaiter().GetResult();

                    result = DoGetNumDtoAndIncreaseRedis(category).Result;
                    return result;

                case SequenceNumCategory.A:
                    result = DoGetNumDtoAndIncreaseRedis(category).Result;
                    return result;

                case SequenceNumCategory.SPV:
                    result = DoGetNumDtoAndIncreaseRedis(category).Result;
                    return result;

                default:
                    return new SequenceNumDto();
            }
        }

        async Task<SequenceNumDto> DoGetNumDtoAndIncrease(SequenceNumCategory category)
        {
            await using (var handle = await LazyServiceProvider.LazyGetService<IAbpDistributedLock>().TryAcquireAsync(DistributedLock.DistributedLock_GenerateSequenceNumForFileName, TimeSpan.FromSeconds(10)))
            {
                if (handle != null)
                {
                    var repo = LazyServiceProvider.LazyGetService<ISequenceNumRepository>();
                    //获取NextNum
                    var seqNum = repo.GetAsync(a => a.Category == category.ToString()).GetAwaiter().GetResult();
                    if (seqNum == null)
                    {
                        return new SequenceNumDto();
                    }

                    var result = ObjectMapper.Map<SequenceNum, SequenceNumDto>(seqNum);
                    //SequenceNumDto result = new SequenceNumDto();
                    //result.Category = seqNum.Category;
                    //result.NextNum = seqNum.NextNum;
                    //result.NumLength = seqNum.NumLength;

                    //自增NextNum
                    ++seqNum.NextNum;
                    //如果自增后的NextNum长度已超过NumLength，则循环从1开始
                    seqNum.NextNum = seqNum.NextNum.ToString().Length > seqNum.NumLength ? 1 : seqNum.NextNum;
                    repo.UpdateAsync(seqNum, true).GetAwaiter().GetResult();

                    return result;
                }
                else
                    throw new Exception("生成文件编号超时");
            }
        }

        async Task<SequenceNumDto> DoGetNumDtoAndIncreaseRedis(SequenceNumCategory category)
        {
            await using (var handle = await LazyServiceProvider.LazyGetService<IAbpDistributedLock>().TryAcquireAsync(DistributedLock.DistributedLock_GenerateSequenceNumForFileName, TimeSpan.FromSeconds(10)))
            {
                if (handle != null)
                {
                    //从Redis取
                    IEnumerable<HashEntry> entries;
                    if (!_redisRepository.Database.KeyExists(RedisKey.BPCS_EDI_SequenceNum))
                    {
                        var repo = LazyServiceProvider.LazyGetService<ISequenceNumRepository>();
                        //获取NextNum
                        var seqNum = repo.GetAsync(a => a.Category == category.ToString()).GetAwaiter().GetResult();
                        
                        //如果DB里都没有，直接返回Dto，不加到Redis不加到DB
                        if (seqNum == null)
                        {
                            return new SequenceNumDto();
                        }

                        //_redisRepository.Database.HashSet(RedisKey.BPCS_EDI_SequenceNum, category.ToString(), JsonConvert.SerializeObject(seqNum));
                        _redisRepository.Database.HashSet(RedisKey.BPCS_EDI_SequenceNum, category.ToString(), JsonConvert.SerializeObject(ObjectMapper.Map<SequenceNum, SequenceNumDto>(seqNum)));
                        
                    }
                    //_redisRepository.Database.HashDelete(RedisKey.BPCS_EDI_SequenceNum, category.ToString());
                    //在从Redis取，一定有
                    entries = _redisRepository.Database.HashScan(RedisKey.BPCS_EDI_SequenceNum, $"{category}");// *category*
                    if (entries?.Any() != true)
                    {
                        return new SequenceNumDto();
                    }

                    var seqNumRedis = JsonConvert.DeserializeObject<SequenceNumDto>(entries.First().Value.ToString());

                    //自增NextNum
                    ++seqNumRedis.NextNum;
                    //如果自增后的NextNum长度已超过NumLength，则循环从1开始
                    seqNumRedis.NextNum = seqNumRedis.NextNum.ToString().Length > seqNumRedis.NumLength ? 1 : seqNumRedis.NextNum;
                    seqNumRedis.LastModificationTime = DateTime.Now;
                    //自增后存回Redis
                    _redisRepository.Database.HashSet(RedisKey.BPCS_EDI_SequenceNum, category.ToString(), JsonConvert.SerializeObject(seqNumRedis));

                    return seqNumRedis;
                }
                else
                    throw new Exception("生成文件编号超时");
            }
        }

        /// <summary>
        /// 更新SequenceNum回DB
        /// </summary>
        /// <returns></returns>
        public async Task<MessageResult> UpdateToDB()
        {
            var redisRepository = LazyServiceProvider.LazyGetService<IRedisRepository>();
            var redisEntries = redisRepository.Database.HashGetAll(RedisKey.BPCS_EDI_SequenceNum);
            if (redisEntries?.Any() == false)
                return MessageResult.SuccessResult();

            var seqNums = redisEntries.Select(a =>
            {
                var seqNumDto = Newtonsoft.Json.JsonConvert.DeserializeObject<SequenceNumDto>(a.Value);
                var entity = ObjectMapper.Map<SequenceNumDto, SequenceNum>(seqNumDto);
                //entity.SetId(seqNumDto.Id);
                return entity;
            });
            //var seqNumDtos = redisEntries.Select(a => Newtonsoft.Json.JsonConvert.DeserializeObject<SequenceNumDto>(a.Value));

            var repoSeqNum = LazyServiceProvider.LazyGetService<ISequenceNumRepository>();
            var dbContext = await repoSeqNum.GetDbContextAsync();
            await dbContext.BulkInsertOrUpdateAsync(seqNums);

            return MessageResult.SuccessResult();
        }
    }
}