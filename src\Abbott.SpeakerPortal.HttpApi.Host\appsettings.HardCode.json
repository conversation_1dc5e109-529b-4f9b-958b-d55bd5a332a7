{
  "App": {
    "SelfUrl": "https://localhost:44340",
    "CorsOrigins": "https://*.oneabbott.com,https://*.abbott.com.cn",
    "RedirectAllowedUrls": ""
  },
  "ConnectionStrings": {
    "BpmDb": "Server=WQ00261Q;Database=PLATFORM_ABBOTT_BUSINESSDATA;TrustServerCertificate=True;User ID=svc_ca191_nexbpm_s;Password=********************************"
  },
  "KeyVaultHost": "https://kv-corp-ca191-d.vault.azure.cn",
  "ApplicationInsightKey": "jc1Go9osCN032Mj7Pgs96V3ofw7HE6C7",
  "KeyVault": {
    //部署云环境时LocalKeyVault可以直接删除
    "LocalKeyVault": {
      "AuthorityUrl": "https://login.chinacloudapi.cn",
      "ClientId": "5aaaf607-4515-4e1a-b35f-95761437bf84",
      "TenantId": "3f82d7c2-9067-40f4-aa48-6e6b9204edfb",
      "ClientSecret": "**********************************"
    }
  },
  "AzureAd": {
    "Instance": "https://login.microsoftonline.com/",
    "CallbackPath": "/login"
  },
  "AuthServer": {
    "Authority": "https://localhost:44340",
    "RequireHttpsMetadata": "false",
    "SwaggerClientId": "SpeakerPortal_Swagger"
  },
  "Consent": {
    "BaseUrl": "https://consent-portal-api-d.oneabbott.com/"
  },
  //微信小程序
  "WxApp": {
    "WxToken": "https://api.weixin.qq.com",
    "WeChatUrl": "pages/informationConfirmation/index",
    "QRwidth": "300",
    "Enversion": "develop"
  },
  "WcApp": {
    "WcToken": "https://qyapi.weixin.qq.com"
  },
  "Blob": {
    "ContainerName": "speaker-portal-file"
  },
  "SpeakerEmail": {
    "Env": "D",
    "WebHost": "https://speaker-portal-d.oneabbott.com/",
    "SmtpServer": "mail.oneabbott.com",
    "FromEmail": "<EMAIL>",
    "FromName": "NexBPM Admin Test",
    "HelpdeskEmail": "<EMAIL>",
    "BPMHelpdeskEmail": "<EMAIL>",
    "VeevaTimeoutCCEmail": "<EMAIL>,<EMAIL>"
  },
    "Integrations": {
        "DSpot": {
            "Url_WholeProcessReport": "https://iddc2.deloitte.com.cn/drsp/api/sampling/whole",
            "Url_WholeNotify": "https://speaker-int-api-d.abbott.com.cn/api/integrationnotify/whole",
            "Push_Count": "3",

            "TwoElementsUrl": "https://iddc2.deloitte.com.cn/drsp/api/twoElements/getResult",
            "TwoElementsBatchUrl": "https://iddc2.deloitte.com.cn/drsp/api/twoElements/getBatchResult"
        },
        "BPCS": {
            "SftpFolderLvl3Archive": "ARCHIVE",
            "SftpFolderLvl3Error": "ERROR",
            "SftpFolderLvl3Upload": "WIP",
            "SftpFolderLvl4Error": "ERROR"
        },
        "OM": {
            //"BaseUrl": "http://epd-hcp-portal-dev.1kuaizhuan.cn",
            "PrintUrl": "https://speaker-portal-s.oneabbott.com/",
            "BaseUrl": "https://epd-hcpportal-s.oneabbott.com",
            "AppId": "next-bpm",
            "AppSecret": "E53ssz3PnbQSXkLdFv",
            "AppVersion": "1.0"
        },
        "BPMOM": {
            "BaseUrl": "https://epd-olmt-integration-api-s.oneabbott.com",
            "AppSecret": "********************************"
        },
        "Veeva": {
            "ApiUrlHost": "https://service-cn.veevaopendata.com:9320/",
            "ApiUrlToken": "api/v1/security/login",
            "ApiUrlEntrance": "api/v1/opendataapi/entrance/",

            "Provider": "db", //[ db, ldap ]
            "Refresh": true,
            //"SftpIP": "abtcnsftpstg.blob.core.chinacloudapi.cn",
            //"SftpUser": "abtcnsftpstg.ca191-corp-nbpm.ca191nbpmappuse",
            //"SftpPwd": "QGWA8Fi7l8+gn/6QhadF7I7zc4o7ZZUz",
            //"SftpPort": "22"

            "SftpIP": "*************",
            "SftpUser": "FIN_SP_S",
            "SftpPwd": "oxfozPb9c[",
            "SftpPort": "22",
            "AES256Key": "2ab6e6a92beaeea4a4ac1dc68def66e1de7fc0cd77d6656e4f0f0e8f61fe6d2f",
            "AES256IV": "775a0276322d5c9fe7765cdb0e03bf9b"
        },
        "Graph": {
            "ClientId": "3a7444c3-5549-4ec0-9183-d041d8f6be28",
            "TenantId": "5b268d57-2a6f-4e04-b0de-6938583d5ebc",
            "CertName": "[7643] - GIS - China Speaker Portal Hub - Dev.pfx",
            "CertPassword": "fdgf#pJOySSvbfs",
            "Authority": "https://login.microsoftonline.com",
            "Resource": "https://graph.microsoft.com"
        },
        "EPD_HCP_Portal": {
            "AesKey256": "********************************"
        },
        "SOI": {
            "AppKey": "b7860726bfd14fa293679bc1d11e24e4",
            "AppSecret": "8/a5dinzgpjx+ff5c9u01hlNE6gzHa9MShy8mCdGCyg=",
            "BaseUrl": "https://ani-dynastytest.oneabbott.com"
        },
        "MDM": {
            "AppId": "UXCAO8ID4K2E83EW",
            "ApiKey": "84o53b9jEcahmSVkSJ5i6G883oMFLbOA",
            "AppSecret": "A420F74C3B18EC28A4F7A2AD4167EAD6E6A00F3F6BC6BD231A0518A267C4CC90902551F1584D6D20FCA4C89BFF55EA1A5BBBFD2F60B8A2B8187AFFFE7DCB44EE",
            "SubscriptionKey": "571c5262d31a4e52b7cc21a10b9d5a99",
            "BaseUrl": "https://apicenter-s.abbott.com.cn/mnd"
        },
        "CSS": {
            "AppSecret": "********************************",
            "BaseUrl": "http://andcss.garnier-smp.com:2009/api"
        }
    },
  "Hangfire": {
    "UserName": "admin",
    "Password": "^Abbott@2024$",
    "DigestRealm": "Abbott hangfire dashboard",
    "Nonce": "cc0912446a063e882b938190297fd06a9c49c744",
    "Opaque": "4d9997009b1936c7ff4b611e1dc24ae151458fdf"
  }
}
