-- dbo.vw_FOCDetails source

--------------------------------------------
 CREATE  VIEW vw_FOCDetails AS
 SELECT
    fa.ApplicationCode,
    fa.ApplyUser,
    fa.ApplyTime,
    fa.BudgetBuName,
    fa.ApplyUserDeptName,
    fa.CostCenter,
    fa.BudgetCode,
    fa.BudgetDescription,
    fa.BudgetRegion,
    fa.ProductMCode,
    fa.ProductName AS ApplicationProductName,
    fa.ProductQty AS FOCApplications_ProductQty,
    fa.NatureName, --changed on 0320
    fa.ShippingTypeName,
    fa.TerminalName,
    fa.ClientCode,
    fa.ClientName,
    fa.ReceivingCode,
    fa.Remark,
    -- 替换 Name1 和 Name2 为逻辑判断的值
    expenseApprover.UserName AS Name1, -- 循环审批-Expense 的审批人
    financeApprover.UserName AS Name2, -- 财务循环审批-Finance 的审批人
    fa.Status,
    fad.Id AS FOCDetailId,
    fad.CityName,
    fad.ConsigneeAddress,
    fad.ConsigneeName,
    fad.ConsigneePhone,
    fad.Description,
    fad.ActivityStartDate,
    fapd.ProductSCode,
    fapd.ProductName AS DetailProductName,
    fapd.ProductQty,
    fapd.WriteoffQty,
    fapd.VerificationStatus,
    fapd.ShippedQty,
    ISNULL(fapd.WriteoffQty, 0) - ISNULL(fapd.ShippedQty, 0) AS WaitingQty,
    fald.BatchNo,
    fald.ShippedQty AS LogisticsShippedQty,
    fald.BpcsOrderNo,
    fald.ShipmentDate,
    at.ExpenseAppvalTime,
    at.FinanceAppvalTime
FROM FOCApplications AS fa
LEFT JOIN FOCApplicationDetails AS fad ON fa.Id = fad.ParentID
LEFT JOIN FOCApplicationProductDetails AS fapd ON fad.Id = fapd.FOCDetailId
LEFT JOIN FOCApplicationLogisticsDetails AS fald ON fapd.FOCDetailId = fald.FOCDetailId AND fapd.ProductSCode = fald.ProductSCode
LEFT JOIN (
    -- 审批时间子查询
    SELECT
        ex.FormId,
        ex.[ApprovalTime] AS ExpenseAppvalTime,
        fn.[ApprovalTime] AS FinanceAppvalTime,
        ex.[ApprovalId] AS EApprover,
        fn.[ApprovalId] AS FApprover
    FROM (
        SELECT
            [FormId],
            [ApprovalTime],
            [ApprovalId],
            ROW_NUMBER() OVER (PARTITION BY [FormId] ORDER BY [ApprovalTime] DESC) AS wf
        FROM [Speaker_Portal_Dev].[dbo].[WorkflowTasks]
        WHERE WorkStep LIKE N'%循环审批-Expense%'
    ) AS ex
    LEFT JOIN (
        SELECT
            [FormId],
            [ApprovalTime],
            [ApprovalId],
            ROW_NUMBER() OVER (PARTITION BY [FormId] ORDER BY [ApprovalTime] DESC) AS wf
        FROM [Speaker_Portal_Dev].[dbo].[WorkflowTasks]
        WHERE 
        WorkStep LIKE N'%财务循环审批-Finance%' 
    ) AS fn ON ex.FormId = fn.FormId AND fn.wf = 1
    WHERE ex.wf = 1
) AS at ON fa.Id = at.FormId
-- 获取 Name1 和 Name2 的逻辑
LEFT JOIN [dbo].[AbpUsers] AS expenseApprover ON at.EApprover = expenseApprover.Id
LEFT JOIN [dbo].[AbpUsers] AS financeApprover ON at.FApprover = financeApprover.Id;