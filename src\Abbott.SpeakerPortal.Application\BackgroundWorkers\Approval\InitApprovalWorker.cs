﻿using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Approval;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.PowerApp;
using Hangfire;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.DistributedLocking;

namespace Abbott.SpeakerPortal.BackgroundWorkers.Approval
{
    public class InitApprovalWorker : SpeakerPortalBackgroundWorkerBase
    {
        public InitApprovalWorker()
        {
            CronExpression = "0 0/2 * * * ?";//每两分钟执行一次
        }
        public async override Task DoWorkAsync(CancellationToken cancellationToken = default)
        {
            //1、加锁执行 执行完自动释放锁
            await using (var @lock = await LazyServiceProvider.LazyGetService<IAbpDistributedLock>().TryAcquireAsync(DistributedLock.DistributedLock_InitApprovalWorkder, TimeSpan.FromSeconds(5)))
            {
                if (@lock == null)
                {
                    // 锁已被其他任务持有，跳过
                    return;
                }
                var approvalRecordService = LazyServiceProvider.LazyGetService<IInitializeApprovalRecordService>();
                await approvalRecordService.PushApprovalRecordAsync();
            }
        }
        //public async Task DoWorkAsync()
        //{
        //    // 使用默认的 CancellationToken
        //    await DoWorkAsync(CancellationToken.None);
        //}
    }
}
