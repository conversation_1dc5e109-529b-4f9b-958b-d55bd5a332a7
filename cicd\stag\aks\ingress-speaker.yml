apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: speaker-ingress
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "20m"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - speaker-portal-api-s.oneabbott.com
    secretName: tls-speaker-portal-api-s-secret
  - hosts:
    - speaker-portal-s.oneabbott.com
    secretName: tls-speaker-portal-s-secret
  rules:
  - host: speaker-portal-api-s.oneabbott.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: corp-ca191-speakerapi-s
            port:
              number: 80
  - host: speaker-portal-s.oneabbott.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: corp-ca191-speakerh5-s
            port:
              number: 80