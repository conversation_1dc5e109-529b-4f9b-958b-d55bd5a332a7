with vendorApp4 as (
	select 
		vt.id ,--此处填入对应VendorApplications的Id，用于标记该行为供应商申请单对应的财务信息
		vt.ProcInstId,vt.CreatorId,
		tsi.company_Value AS Company,--
		tsi.currency_Text AS Currency,--
		tsi.vendorNumber AS VendorCode,--
		tsi.bankCode AS AbbottBank,--
		tsi.vendorType AS VendorType,--
		tsi.divisionCode AS Division,--
		tsi.paymentType_Text AS PayType,--
		tsi.ctryCode_Text AS CountryCode,--
		cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/BankType_Value)[1]', 'nvarchar(255)')  AS BankType,--
		case when  sd.spk_code <>'' and sd.spk_code is not null then sd.spk_code end AS DpoCategory,--根据填入的值与DPO Category字典匹配后填入字典编码
		cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/TermCode_Value)[1]', 'nvarchar(255)')  AS PaymentTerm,--
		SpendingCategory    AS SpendingCategory--此处填写的值就是字典编码，不需要额外匹配(但如果识别到了不存在于字典的值可能需要加入字典或前端展示时直接展示为code)
	from PLATFORM_ABBOTT.dbo.ODS_AUTO_BIZ_T_SupplierApplication_Info tsi
	join (
	--	select * from PLATFORM_ABBOTT.dbo.ODS_T_FORMINSTANCE_GLOBAL where 
		select ProcInstid,XmlContent from PLATFORM_ABBOTT.dbo.ods_T_FORMINSTANCE_GLOBAL 
			where FORM_Id ='663dd63299be45d69dd8f853d0a4b445' or FORM_Id = '7a708c9568fb444a884eb5eca658975f'
	) a
		on tsi.ProcInstId =a.ProcInstId
	left join PLATFORM_ABBOTT.dbo.VendorApplications_Tmp vt
		on tsi.ProcInstId =vt.ProcInstId
	left join spk_dictionary sd 
		on  DpoCategory =sd.spk_Name 
	
	
)
SELECT id,Company,Currency,VendorCode,AbbottBank,VendorType,Division,PayType,CountryCode,BankType,PaymentTerm,DpoCategory,SpendingCategory  from vendorApp4 t1 
--where t1.ProcInstId is null
--DpoCategory 独立关联 验证
--where id ='7ABC1B25-CF3A-44A9-8FF3-D19512765560'
except
--union all 
--7ABC1B25-CF3A-44A9-8FF3-D19512765560
--03886CE9-9616-4F0F-ACEA-D1D8BA163CD8
--8CBEDBAB-A5F0-415C-8B4A-D4CA6CD017CD

SELECT ApplicationId  ,Company,Currency,VendorCode,AbbottBank,VendorType,Division,PayType,CountryCode,BankType,PaymentTerm,DpoCategory,SpendingCategory from PLATFORM_ABBOTT.dbo.VendorApplicationFinancials t2

--where  ApplicationId = '7ABC1B25-CF3A-44A9-8FF3-D19512765560'

--SELECT * from PLATFORM_ABBOTT.dbo.VendorApplicationFinancials t2



--SELECT ProcInstId,count(*) from PLATFORM_ABBOTT.dbo.VendorApplications_Tmp
--group by ProcInstId
--HAVING count(*)>1

--SELECT * from spk_dictionary
--
--SELECT spk_NexBPMCode from spk_staffmasterdata where bpm_id='b43fe667d6e6418a985ddcc017c58262'