﻿using System;
using System.Collections.Generic;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Abbott.SpeakerPortal.Contracts.Budget.Subbudget;
using System.IO;
using DocumentFormat.OpenXml.EMMA;

namespace Abbott.SpeakerPortal.Contracts.Budget.FocSubbudget
{
    public interface IFocSubbudgetService
    {
        /// <summary>
        /// 单个新增子预算
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        Task<MessageResult> CreateFocSubbudgetAsync(CreateFocSubbudgetRequestDto request);

        /// <summary>
        /// 获取子预算详情
        /// </summary>
        /// <param name="subbudgetId">The subbudget identifier.</param>
        /// <returns></returns>
        Task<MessageResult> GetFocSubbudgetDetailAsync(Guid subbudgetId);

        /// <summary>
        /// 调整子预算
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        Task<MessageResult> AdjustingFocSubbudgetAsync(AdjustFocSubbudgetRequestDto request);

        /// <summary>
        /// 批量调整子预算
        /// </summary>
        /// <param name="Rows"></param>
        /// <returns></returns>
        Task<MessageResult> VarifyImportAdjustmentFocSubBudgetAsync(IEnumerable<AdjustmentFocBudgetDto> Rows);

        /// <summary>
        /// 批量提交调整子预算
        /// </summary>
        /// <param name="adjustments"></param>
        /// <returns></returns>
        Task<MessageResult> SubmitImportAdjustmentFocSubBudgetAsync(List<AdjustmentFocBudgetDto> adjustments);

        /// <summary>
        /// 获取子预算管理列表
        /// </summary>
        /// <param name="request">筛选条件</param>
        /// <param name="isPaging">是否分页，默认是</param>
        /// <returns></returns>
        Task<PagedResultDto<GetFocSubbudgetListResponseDto>> GetFocSubbudgetManageListAsync(GetFocSubbudgetListRequestDto request, bool isPaging = true);

        /// <summary>
        /// 批量新增
        /// </summary>
        /// <param name="Rows"></param>
        /// <returns></returns>
        Task<MessageResult> VarifyBatchCreateFocSubBudgetAsync(IEnumerable<CreatesFocSubBudgetDto> Rows);

        /// <summary>
        /// 批量新增
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<MessageResult> SubmitBatchCreateFocSubBudgetAsync(List<CreateSubBudgetImportMessageDto> request);

        /// <summary>
        /// 设置子预算状态（冻结false、启用true）
        /// </summary>
        /// <param name="ids">子预算Id列表</param>
        /// <param name="status">状态：false冻结、true启用</param>
        /// <returns></returns>
        Task<MessageResult> SetFocSubbudgetStatusAsync(List<Guid> ids, bool status);

        /// <summary>
        /// 删除子预算
        /// </summary>
        /// <param name="ids">子预算Id列表</param>
        /// <returns></returns>
        Task<MessageResult> DeleteFocSubbudget(List<Guid> ids);

        /// <summary>
        /// 导出子预算列表
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        Task<Stream> ExportFocSubbudgetListAsync(GetFocSubbudgetListRequestDto request);

        /// <summary>
        /// 判断子预算是否足够
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<MessageResult> CheckFocSubbudgetQtySufficientAsync(UseFocBudgetRequestDto request);

        /// <summary>
        /// 使用子预算（含反冲）
        /// </summary>
        /// <param name="request"></param>
        /// <param name="autoSave"></param>
        /// <returns></returns>
        Task<MessageResult> UseFocSubbudgetAsync(UseFocBudgetRequestDto request, bool autoSave = false);
        /// <summary>
        /// 退回FOC子预算
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        Task<MessageResult> ReturnFocSubbudgetAsync(List<ReturnFocBudgetRequestDto> requests);

        /// <summary>
        /// 获取FOC子预算使用明细
        /// </summary>
        /// <param name="subbudgetId">The subbudget identifier.</param>
        /// <returns></returns>
        Task<IEnumerable<GetFocSubbudgetUseInfoResponseDto>> GetFocSubbudgetUseInfo(Guid subbudgetId);
        Task<List<Guid>> GetSubbudgetsByOwner(Guid ownerId);
    }
}
