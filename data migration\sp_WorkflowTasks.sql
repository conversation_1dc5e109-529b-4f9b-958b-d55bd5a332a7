create proc sp_WorkflowTasks
	@clearData bit=0
as
begin
	--是否清空临时表的数据
	if(@clearData=1 and OBJECT_ID(N'XML_approvalHistoryGrid',N'U') is not null)
		truncate table XML_approvalHistoryGrid;

	--展开xml节点
	declare @maxProcId int, @currentProcId int, @pageSize int;
	set @pageSize=100000;
	select @maxProcId=max(ProcInstId) from ODS_T_FORMINSTANCE_GLOBAL;
	RAISERROR('@maxProcId当前值: %d', 0, 1, @maxProcId) WITH NOWAIT;

	if OBJECT_ID(N'XML_approvalHistoryGrid',N'U') is null
		set @currentProcId=0;
	else
		select @currentProcId=isnull(max(ProcInstId),0) from XML_approvalHistoryGrid;
	RAISERROR('@currentProcId当前值: %d', 0, 1, @currentProcId) WITH NOWAIT;

	--分批次解析xml数据
	while @currentProcId<@maxProcId
	begin
		if OBJECT_ID(N'XML_approvalHistoryGrid',N'U') is null
		begin
			select
				distinct
				a.ProcInstId,
				row.value('(approvalActInstDestId)[1]','varchar(50)') approvalActInstDestId,
				row.value('(approvalInfo)[1]','nvarchar(1000)') approvalInfo,
				row.value('(approverOrigjinal)[1]','nvarchar(50)') approverOrigjinal,
				row.value('(action)[1]','nvarchar(50)') action
			into XML_approvalHistoryGrid
			from ODS_T_FORMINSTANCE_GLOBAL a
			cross apply a.XmlContent.nodes('/root/approvalHistoryGrid/row') AS XMLTable(row)
			where a.ProcInstId>@currentProcId and a.ProcInstId<=@currentProcId+@pageSize;
		end
		else
		begin
			insert XML_approvalHistoryGrid
			select
				distinct
				a.ProcInstId,
				row.value('(approvalActInstDestId)[1]','varchar(50)') approvalActInstDestId,
				row.value('(approvalInfo)[1]','nvarchar(1000)') approvalInfo,
				row.value('(approverOrigjinal)[1]','nvarchar(50)') approverOrigjinal,
				row.value('(action)[1]','nvarchar(50)') action
			from ODS_T_FORMINSTANCE_GLOBAL a
			cross apply a.XmlContent.nodes('/root/approvalHistoryGrid/row') AS XMLTable(row)
			where a.ProcInstId>@currentProcId and a.ProcInstId<=@currentProcId+@pageSize;
		end

		set @currentProcId=@currentProcId+@pageSize;
		RAISERROR('@currentProcId当前值: %d', 0, 1, @currentProcId) WITH NOWAIT;

	end

	--组装数据
	select
		'' [InstanceId],
		a.ProcInstID,
		a.OrderID,
		a.Folio,
		(case
			when a.Folio like 'V%' or a.Folio like 'L%' then 'SupplierRequest'
			when a.Folio like 'W%' then N'豁免申请Waiver'
			when a.Folio like 'P%' then N'采购申请'
			when a.Folio like 'B%' then 'ComparePricesToBuy'
			when a.Folio like 'O%' then 'MasterPurchaseOrder'
			when a.Folio like 'G%' then 'ReceiptRequest'
			when a.Folio like 'A%' then 'PaymentRequest'
			else null
		end+'-'+FORMAT(a.StartDate,N'yyMMdd')+'-'+FORMAT(a.StartDate,N'HHmmss')) [Name],
		grid.action,
		case
			when grid.action=N'重发起' then 0
			when grid.action in(N'同意', N'初审完成', N'复审完成') then 2
			when grid.action=N'拒绝' then 3
			when grid.action in(N'退回', N'退回申请人', N'退回原件', N'退回补件', N'退回初审补件', N'退回一级采购') then 4
			when grid.action=N'撤回' then 5
			when grid.action=N'作废' then 6
			when grid.action=N'确认' then 7
			when grid.action=N'发起收货' then 8
			when grid.action=N'加签' then 9
			when grid.action=N'退回DPSCheck' then 10
			when grid.action=N'退回补件' then 11
			when grid.action=N'退回初审补件' then 12
			when grid.action=N'退回申请人' then 13
			when grid.action=N'退回原件' then 14
			when grid.action=N'修正' then 15
			when grid.action=N'终止收货' then 16
			when grid.action=N'收货' then 17
		end [Status],
		grid.approvalInfo Remark,
		'' [NextApproverId],
		case
			when a.Folio like 'V%' or a.Folio like 'L%' then (select top 1 Id from VendorApplications_Tmp where ApplicationCode=a.Folio)
			when a.Folio like 'W%' then (select top 1 Id from PurBWApplications where ApplicationCode=a.Folio)
			when a.Folio like 'P%' then (select top 1 Id from PurPRApplications_tmp where ApplicationCode=a.Folio)
			when a.Folio like 'B%' then (select top 1 Id from PurBDApplications_tmp where ApplicationCode=a.Folio)
			when a.Folio like 'O%' then (select top 1 Id from PurPOApplications_tmp where ApplicationCode=a.Folio)
			when a.Folio like 'G%' then (select top 1 Id from PurGRApplications_tmp where ApplicationCode=a.Folio)
			when a.Folio like 'A%' then (select top 1 Id from PurPAApplications_tmp where ApplicationCode=a.Folio)
		end [FormId],
		a.ActName WorkStep,
		'{}' [ExtraProperties],
		'' [ConcurrencyStamp],
		a.StartDate [CreationTime],
		a.Destination,
		staff.spk_NexBPMCode [CreatorId],
		'' [LastModificationTime],
		'' [LastModifierId],
		0 [IsDeleted],
		'' [DeleterId],
		'' [DeletionTime],
		a.FinishDate [ApprovalTime],
		staff.spk_NexBPMCode [ApprovalId],
		case
			when a.Folio like 'V%' or a.Folio like 'L%' then 'VendorApplication'
			when a.Folio like 'W%' then 'PurExemptWaiver'
			when a.Folio like 'P%' then 'PRApplication'
			when a.Folio like 'B%' then 'BDApplication'
			when a.Folio like 'O%' then 'POApplication'
			when a.Folio like 'G%' then 'GRApplication'
			when a.Folio like 'A%' then 'PAApplication'
		end [FormName],
		grid.approverOrigjinal,
		originalStaff.spk_NexBPMCode [OriginalApprovalId]
	into #WorkflowTasks_tmp
	from ODS_T_PROCESS_Historys a
	join XML_approvalHistoryGrid grid on a.ProcInstID=grid.ProcInstId and a.ActInstDestID=grid.approvalActInstDestId
	join ODS_T_EMPLOYEE emp on a.Destination=emp.K2UserID
	join spk_staffmasterdata staff on emp.Emp_Id=staff.bpm_id
	left join ODS_T_EMPLOYEE originalEmp on grid.approverOrigjinal=originalEmp.Emp_Name
	left join spk_staffmasterdata originalStaff on originalEmp.Emp_Id=originalStaff.bpm_id
	where a.Folio not like 'T%' and a.Folio not like 'S%'

	--将数据插入临时表
	if OBJECT_ID(N'WorkflowTasks_tmp',N'U') is null
		select * into WorkflowTasks_tmp from #WorkflowTasks_tmp
	else
	begin
		drop table WorkflowTasks_tmp;
		select * into WorkflowTasks_tmp from #WorkflowTasks_tmp;
	end
end
