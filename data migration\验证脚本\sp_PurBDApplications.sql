CREATE PROCEDURE dbo.sp_PurBDApplications
AS 
begin	
--基本规则：查询[AUTO_BIZ_T_BiddingApplication_Info]得到历史的比价申请单据，
--以单据号Join[Form_fefa2338743b4ebea533c8f6c5c2bacd]得到比价申请状态，
--以ProcInstId join [AUTO_BIZ_T_BiddingApplication_Info_PR]得到对应的可能多行比价申请明细信息

with status_process as (
	select
		 ProcInstId
		,case 
			when ActName = N'重发起' then N'作废'
			when ActName <> N'重发起' then N'已拒绝'
		 end as ActName
	from(
		select
			 ProcInstId
			,FinishDate
			,ActName
			,row_number () over(partition by ProcInstId order by FinishDate desc) as rn
		from PLATFORM_ABBOTT.dbo.ODS_T_PROCESS_Historys
	)A
	where rn = '1'
)
select 
newid() AS Id,--自动生成的uuid
A.ProcInstId,
a.serialNumber AS ApplicationCode,--申请单号
--d.PRNumber,
case when b.processStatus=N'N/A(不迁移草稿)' then N'草稿'
when b.processStatus=N'N/A(原BPM若未审批完，则退回到重新发起状态，让用户重新提交申请)' then N'审批中'
when b.processStatus=N'完成' then N'已通过'
when b.processStatus=N'重发起' or b.processStatus=N'审批中' then N'退回'
when b.processStatus=N'发起人终止' then d.ActName
end AS Status,--参考下方附录状态mapping表
a.applicantEmpId AS ApplyUserId,--以该ID匹配至员工主数据
a.applicantEmpName AS ApplyUserName,--
a.applicationDate AS ApplyTime,--
a.applicantDeptId AS ApplyUserBu,--以该ID匹配至对应的组织主数据
a.applicantDept_Text AS ApplyUserBuName,--
XmlContent.value('(/root/BiddingApplication_PRBlock_MainStore/SelectPersonId)[1]', 'nvarchar(255)')  AS BudgetManagerUserId,--以该ID匹配至员工主数据
XmlContent.value('(/root/BiddingApplication_PRBlock_MainStore/SelectPersonName)[1]', 'nvarchar(255)') AS BudgetManagerUserName,--
a.SupplyCode AS VendorId,--基于此处的SupplyCode及SupplyName，结合该单对应PR单的公司编码CompanyId，去查询BPCSPMFVM对应的记录(VNDERX,VEXTNM,VMCMPY)，对于匹配成功的记录，再找到其对应的BPCSAVM.ID并填入(基于VMCMPY=VCMPNY AND VNDERX=VENDOR)
a.SupplyName AS VendorName,--
a.currency AS Currency,--
--BiddingChk/AnnualContract/SupplementaryFee/OfflineBidding 
case when XmlContent.value('(/root/BiddingApplication_BidSupplyBlock_MainStore/BiddingChk)[1]', 'nvarchar(255)')='true' then '1' 
when XmlContent.value('(/root/BiddingApplication_BidSupplyBlock_MainStore/AnnualContract)[1]', 'nvarchar(255)')='true' then '2' 
when XmlContent.value('(/root/BiddingApplication_BidSupplyBlock_MainStore/SupplementaryFee)[1]', 'nvarchar(255)')='true' then '3' 
when XmlContent.value('(/root/BiddingApplication_BidSupplyBlock_MainStore/OfflineBidding)[1]', 'nvarchar(255)')='true' then '4'
else '5' end AS SingleChoice,--以Bidding主表的ProcInstId查询单据对应的xml文件，基于列举出的field是否为""true""决定此处填入的值：
----BiddingChk=true——填入1，代表有六个月同类比价结果
----AnnualContract=true——填入2，代表有确定价格的年度合同
----SupplementaryFee=true——填入3，代表是增补费用
----OfflineBidding=true——填入4，代表存在线下比价
----全部为false——填入5，代表是一般比价，不存在例外情况"
XmlContent.value('(/root/BiddingApplication_hiddenBlock_MainStore/BiddingResult1)[1]', 'nvarchar(255)') AS BDApplicationId,--仅当BiddingChk为True时查询，此处填入了带格式的单号，需要查询B开头外加10位数字的一串单号，作为其同类比价结果对应的历史比价单号；单号填入BDApplicationCode内，单号对应的比价单ID填入BDApplicationId内
XmlContent.value('(/root/BiddingApplication_hiddenBlock_MainStore/BiddingResult1)[1]', 'nvarchar(255)') AS BDApplicationCode,--
case when a.PRApproved='Yes' then '1'
when a.PRApproved='No' then '2' end AS PRCorrespond,--Yes——填入1，代表与PR行一一对应；No——填入2，代表采购自定义
a.explain AS Explain,--
a.remark AS OtherSupplierExplain,--
c.up_id AS AttachmentFile,--支持文档，以ProcInstId查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到QuoteAttachmentGrid下所有的up_Id，填入匹配出的NexBPM支持文件ID，多个支持文件以英文逗号分隔
'{}' AS ExtraProperties,--默认填写为"{}"
'' AS ConcurrencyStamp,--?
a.applicationDate AS CreationTime,--填充为ApplyTime即可
a.applicantEmpId AS CreatorId,--填充为ApplyUserId即可
'' AS LastModificationTime,--默认为空
'' AS LastModifierId,--默认为空
'0' AS IsDeleted,--默认为0
'' AS DeleterId,--默认为空
'' AS DeletionTime,--默认为空
a.amount AS TotalAmount,--
SupplyCode AS VendorCode
into #PurBDApplications_tmp
from PLATFORM_ABBOTT.dbo.ods_AUTO_BIZ_T_BiddingApplication_Info a
Join PLATFORM_ABBOTT.dbo.ods_Form_fefa2338743b4ebea533c8f6c5c2bacd b
on a.ProcInstId =b.ProcInstId 
left join PLATFORM_ABBOTT.dbo.ODS_T_FORMINSTANCE_GLOBAL otfg 
on a.ProcInstId =otfg.ProcInstId 
left join PLATFORM_ABBOTT.dbo.XML_22  c
on a.ProcInstId=c.ProcInstId
left join status_process d
on a.ProcInstId = d.ProcInstId


--更新BDApplicationId,BDApplicationCode
update a set a.BDApplicationCode=b.TextPart,A.BDApplicationId=B.NumberPart from #PurBDApplications_tmp a
join (
SELECT ProcInstId,LEFT(NumberPart, LEN(NumberPart) - 1) NumberPart,TextPart FROM (
SELECT ProcInstId,
    LTRIM(RTRIM(SUBSTRING(BDApplicationId, CHARINDEX('''', BDApplicationId) + 1, CHARINDEX(')', BDApplicationId) - CHARINDEX('''', BDApplicationId) - 1))) AS NumberPart,
    SUBSTRING(
        SUBSTRING(BDApplicationId, CHARINDEX('>', BDApplicationId) + 1, CHARINDEX('</', BDApplicationId) - CHARINDEX('>', BDApplicationId) - 1),
        1,
        CHARINDEX('</', BDApplicationId) - CHARINDEX('>', BDApplicationId) - 1
    ) AS TextPart
FROM #PurBDApplications_tmp where BDApplicationCode is not null and BDApplicationCode!=''
)A
)B
ON A.ProcInstId=B.ProcInstId



IF OBJECT_ID(N'PLATFORM_ABBOTT.dbo.PurBDApplications_tmp', N'U') IS NOT NULL
BEGIN
	update a 
	set a.ProcInstId            = b.ProcInstId
       ,a.ApplicationCode       = b.ApplicationCode
       ,a.Status                = b.Status
       ,a.ApplyUserId           = b.ApplyUserId
       ,a.ApplyUserName         = b.ApplyUserName
       ,a.ApplyTime             = b.ApplyTime
       ,a.ApplyUserBu           = b.ApplyUserBu
       ,a.ApplyUserBuName       = b.ApplyUserBuName
       ,a.BudgetManagerUserId   = b.BudgetManagerUserId
       ,a.BudgetManagerUserName = b.BudgetManagerUserName
       ,a.VendorId              = b.VendorId
       ,a.VendorName            = b.VendorName
       ,a.Currency              = b.Currency
       ,a.SingleChoice          = b.SingleChoice
       ,a.BDApplicationId       = b.BDApplicationId
       ,a.BDApplicationCode     = b.BDApplicationCode
       ,a.PRCorrespond          = b.PRCorrespond
       ,a.Explain               = b.Explain
       ,a.OtherSupplierExplain  = b.OtherSupplierExplain
       ,a.AttachmentFile        = b.AttachmentFile
       ,a.ExtraProperties       = b.ExtraProperties
       ,a.ConcurrencyStamp      = b.ConcurrencyStamp
       ,a.CreationTime          = b.CreationTime
       ,a.CreatorId             = b.CreatorId
       ,a.LastModificationTime  = b.LastModificationTime
       ,a.LastModifierId        = b.LastModifierId
       ,a.IsDeleted             = b.IsDeleted
       ,a.DeleterId             = b.DeleterId
       ,a.DeletionTime          = b.DeletionTime
       ,a.TotalAmount           = b.TotalAmount
       ,a.VendorCode            = b.VendorCode
    from PLATFORM_ABBOTT.dbo.PurBDApplications_tmp a
    left join #PurBDApplications_tmp b on a.ProcInstId = b.ProcInstId 
    
    insert into PLATFORM_ABBOTT.dbo.PurBDApplications_tmp
    select a.Id
          ,a.ProcInstId
          ,a.ApplicationCode
          ,a.Status
          ,a.ApplyUserId
          ,a.ApplyUserName
          ,a.ApplyTime
          ,a.ApplyUserBu
          ,a.ApplyUserBuName
          ,a.BudgetManagerUserId
          ,a.BudgetManagerUserName
          ,a.VendorId
          ,a.VendorName
          ,a.Currency
          ,a.SingleChoice
          ,a.BDApplicationId
          ,a.BDApplicationCode
          ,a.PRCorrespond
          ,a.Explain
          ,a.OtherSupplierExplain
          ,a.AttachmentFile
          ,a.ExtraProperties
          ,a.ConcurrencyStamp
          ,a.CreationTime
          ,a.CreatorId
          ,a.LastModificationTime
          ,a.LastModifierId
          ,a.IsDeleted
          ,a.DeleterId
          ,a.DeletionTime
          ,a.TotalAmount
          ,a.VendorCode
    from #PurBDApplications_tmp a
    where not exists (select * from PLATFORM_ABBOTT.dbo.PurBDApplications_tmp where a.ProcInstId = ProcInstId)
	PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into PLATFORM_ABBOTT.dbo.PurBDApplications_tmp from #PurBDApplications_tmp
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END

end
