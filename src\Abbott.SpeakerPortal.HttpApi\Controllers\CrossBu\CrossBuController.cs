﻿using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Budget.Subbudget;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.Attachment;
using Abbott.SpeakerPortal.Contracts.Common.PowerApp;
using Abbott.SpeakerPortal.Contracts.Common.SMS;
using Abbott.SpeakerPortal.Contracts.CrossBu;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Permissions;
using ClosedXML.Excel;
using DocumentFormat.OpenXml.Office2010.Excel;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Path = System.IO.Path;

namespace Abbott.SpeakerPortal.Controllers.CrossBu
{
    [ApiExplorerSettings(GroupName = SwaggerGrouping.CROSS_BU)]
    public class CrossBuController : SpeakerPortalController
    {
        private readonly ICrossBuService _crossBuService;

        public CrossBuController(IServiceProvider serviceProvider)
        {
            _crossBuService = serviceProvider.GetService<ICrossBuService>();
        }

        /// <summary>
        /// 获取但年每个月是否有活动
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet]
        [Authorize(SpeakerPortalPermissions.MarketActvity.MarketActvityRead)]
        [Authorize(SpeakerPortalPermissions.MarketActvity.MarketActvityH5Read)]
        [ProducesDefaultResponseType(typeof(MessageResult<List<QueryYearMarketActivityListResponseDto>>))]
        public async Task<IActionResult> QueryYearMarketActivitiesAsync([FromQuery] QueryYearMarketActivityRequestDto request)
        {
            var result = await _crossBuService.QueryYearMarketActivitiesAsync(request);
            return Ok(result);
        }

        /// <summary>
        /// 获取市场活动列表,H5
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet]
        [Authorize(SpeakerPortalPermissions.MarketActvity.MarketActvityH5Read)]
        [ProducesDefaultResponseType(typeof(MessageResult<PagedResultDto<QueryMarketActivityListResponseDto>>))]
        public async Task<IActionResult> QueryMarketActivities([FromQuery] QueryMarketActivityRequestDto request)
        {
            request.IsMobile = true;
            var result = await _crossBuService.QueryMarketActivitiesAsync(request);
            return Ok(MessageResult.SuccessResult(result));
        }
        /// <summary>
        /// 获取市场活动列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet]
        [Authorize(SpeakerPortalPermissions.MarketActvity.MarketActvityRead)]
        [ProducesDefaultResponseType(typeof(MessageResult<PagedResultDto<QueryMarketActivityListResponseDto>>))]
        public async Task<IActionResult> QueryMarketActivitiesByPortal([FromQuery] QueryMarketActivityRequestDto request)
        {
            var result = await _crossBuService.QueryMarketActivitiesAsync(request);
            return Ok(MessageResult.SuccessResult(result));
        }
        /// <summary>
        /// 检查活动重复
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [Authorize(SpeakerPortalPermissions.MarketActvity.MarketActvityCRUD)]
        [ProducesDefaultResponseType(typeof(MessageResult<CheckMarketActivityResponse>))]
        public async Task<IActionResult> CheckMarketActivityAsync([FromBody] CreateMarketActivityRequestDto request)
        {
            var response = await _crossBuService.CheckMarketActivityAsync(request);
            return Ok(response);
        }

        /// <summary>
        /// 创建活动
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult<Guid>))]
        [Authorize(SpeakerPortalPermissions.MarketActvity.MarketActvityCRUD)]
        public async Task<IActionResult> CreateMarketActivityAsync([FromBody] CreateMarketActivityRequestDto request)
        {
            var response = await _crossBuService.CreateMarketActivityAsync(request);
            return Ok(response);
        }

        /// <summary>
        /// 更新市场活动数据
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult<Guid>))]
        [Authorize(SpeakerPortalPermissions.MarketActvity.MarketActvityCRUD)]
        public async Task<IActionResult> UpdateMarketActivityAsync([FromBody] CreateMarketActivityRequestDto request)
        {
            var response = await _crossBuService.UpdateMarketActivityAsync(request);
            return Ok(response);
        }

        /// <summary>
        /// 修改活动状态
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult<Guid>))]
        [Authorize(SpeakerPortalPermissions.MarketActvity.MarketActvityCRUD)]
        public async Task<IActionResult> ModifyMarketActivityStatusAsync([FromBody] UpdateMarketActivityStatusRequestDto request)
        {
            var response = await _crossBuService.ModifyMarketActivityStatusAsync(request);
            return Ok(response);
        }

        /// <summary>
        /// 删除市场活动数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpDelete]
        [ProducesDefaultResponseType(typeof(MessageResult<Guid>))]
        [Authorize(SpeakerPortalPermissions.MarketActvity.MarketActvityCRUD)]
        public async Task<IActionResult> DeleteMarketActivityAsync([FromQuery] Guid? id)
        {
            var response = await _crossBuService.DeleteMarketActivityAsync(id);
            return Ok(response);
        }

        /// <summary>
        /// 获取市场活动
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Authorize(SpeakerPortalPermissions.MarketActvity.MarketActvityRead)]
        [Authorize(SpeakerPortalPermissions.MarketActvity.MarketActvityH5Read)]
        [ProducesDefaultResponseType(typeof(MessageResult<QueryMarketActivityResponseDto>))]
        public async Task<IActionResult> GetMarketActivity([FromQuery] Guid? id)
        {
            if (id == null)
            {
                return Ok(MessageResult.FailureResult("未传入数据Id,获取市场活动失败"));
            }

            var result = await _crossBuService.GetMarketActivityAsync(id);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 市场活动导出
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [Authorize(SpeakerPortalPermissions.MarketActvity.MarketActvityExport)]
        public async Task<IActionResult> ExportMarketActivity([FromBody] QueryMarketActivityRequestDto request)
        {
            request.ReleaseStatus = ActivityReleaseStatus.Published;
            var pageData = await _crossBuService.QueryMarketActivitiesAsync(request, false);
            var marketActivities = pageData.Items;

            var attachmentService = LazyServiceProvider.LazyGetService<IAttachmentService>();

            var fieldInfo = TemplateTypes.MarketActivty_Create.GetType().GetField(TemplateTypes.MarketActivty_Create.ToString());
            var templateName = fieldInfo.GetCustomAttribute<CategoryAttribute>().Category;

            var templatesFile = await attachmentService.GetTemplatesFileAsync(TemplateTypes.MarketActivty_Create, templateName);
            if (templatesFile == null)
            {
                return Ok(MessageResult.FailureResult("未获取到文件模版,请联系管理员"));
            }

            using MemoryStream memoryStream = new();
            await templatesFile.CopyToAsync(memoryStream);
            memoryStream.Position = 0;

            try
            {
                using (XLWorkbook xLWorkbook = new XLWorkbook(memoryStream))
                {
                    IXLWorksheet workbook = xLWorkbook.Worksheets.First();

                    for (int i = 0; i < marketActivities.Count; i++)
                    {
                        //第一行数据是表头
                        var row = workbook.Row(i + 2);
                        row.Cell(1).Value = marketActivities[i].Id.ToString();
                        row.Cell(2).Value = marketActivities[i].Name;
                        row.Cell(3).Value = marketActivities[i].Type;
                        row.Cell(4).Value = marketActivities[i].Mode;
                        row.Cell(5).Value = marketActivities[i].StartYear;
                        row.Cell(6).Value = marketActivities[i].StartQuarter;
                        row.Cell(7).Value = marketActivities[i].StartMonth;
                        row.Cell(8).Value = marketActivities[i].StartDay;
                        row.Cell(9).Value = marketActivities[i].EndYear;
                        row.Cell(10).Value = marketActivities[i].EndQuarter;
                        row.Cell(11).Value = marketActivities[i].EndMonth;
                        row.Cell(12).Value = marketActivities[i].EndDay;
                        row.Cell(13).Value = marketActivities[i].Applicant;
                        row.Cell(14).Value = marketActivities[i].Contact;
                        row.Cell(15).Value = marketActivities[i].City;
                        row.Cell(16).Value = marketActivities[i].Location;
                        row.Cell(17).Value = marketActivities[i].Sponsor;
                        row.Cell(18).Value = marketActivities[i].Organizer;
                        row.Cell(19).Value = marketActivities[i].Hospital;
                        row.Cell(20).Value = marketActivities[i].Theme;

                        if (marketActivities[i].Departments.Values != null)
                        {
                            var count = marketActivities[i].Departments.Values.Count;
                            int n = 0;
                            foreach (var item in marketActivities[i].Departments.Values)
                            {
                                if (n < count && n < 3)
                                {
                                    row.Cell(21 + n).Value = item;
                                }
                                n++;
                            }
                        }
                        row.Cell(24).Value = marketActivities[i].Url;
                        row.Cell(25).Value = marketActivities[i].Purpose;
                    }

                    // 保存到内存流
                    var stream = new MemoryStream();
                    xLWorkbook.SaveAs(stream);
                    stream.Seek(0, SeekOrigin.Begin);
                    string fileName = $"市场活动-{DateTime.Now:yyyyMMddHHmmss}.xlsx";
                    string encodedFileName = Uri.EscapeDataString(fileName); // 对文件名进行URL编码

                    Response.Headers.Append("Content-Disposition", $"attachment;filename={encodedFileName};filename*=UTF-8''{encodedFileName}");
                    //Response.Headers.Append(nameof(fileName), fileName);
                    return File(stream, "application/octet-stream");
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, "Internal server error" + ex);
            }
        }

        /// <summary>
        /// 验证上传数据是否正确
        /// </summary>
        /// <param name="bussinessUnitId"></param>
        /// <param name="formFile"></param>
        /// <returns></returns>
        [HttpPost]
        [Authorize(SpeakerPortalPermissions.MarketActvity.MarketActvityBatchAdd)]
        [ProducesDefaultResponseType(typeof(MessageResult<List<TransferMarketActivityDto>>))]
        public async Task<IActionResult> VarifyImportMarketActivityAsync(Guid bussinessUnitId, IFormFile formFile)
        {
            try
            {
                var str = Path.GetExtension(formFile.FileName);
                string[] strings = [".xlsx", ".xls"];
                if (!strings.Contains(str))
                {
                    return Ok(MessageResult.FailureResult("请上传excel文件"));
                }
                using MemoryStream memoryStream = new();
                await formFile.CopyToAsync(memoryStream);
                memoryStream.Position = 0;
                using XLWorkbook xLWorkbook = new XLWorkbook(memoryStream);
                IXLWorksheet ws = xLWorkbook.Worksheets.First();
                var rows = ws.Rows().Skip(1).Select(r =>
                new TransferMarketActivityDto
                {
                    Id = !string.IsNullOrEmpty(r.Cell(1).GetValue<string>()) ? Guid.Parse(r.Cell(1).GetValue<string>()) : null,
                    Name = r.Cell(2).GetValue<string>(),
                    Type = r.Cell(3).GetValue<string>(),
                    Mode = r.Cell(4).GetValue<string>(),
                    StartYearExcel = r.Cell(5).GetValue<string>(),
                    StartQuarterExcel = r.Cell(6).GetValue<string>(),
                    StartMonthExcel = r.Cell(7).GetValue<string>(),
                    StartDayExcel = r.Cell(8).GetValue<string>(),
                    EndYearExcel = r.Cell(9).GetValue<string>(),
                    EndQuarterExcel = r.Cell(10).GetValue<string>(),
                    EndMonthExcel = r.Cell(11).GetValue<string>(),
                    EndDayExcel = r.Cell(12).GetValue<string>(),
                    Applicant = r.Cell(13).GetValue<string>(),
                    Contact = r.Cell(14).GetValue<string>(),
                    City = r.Cell(15).GetValue<string>(),
                    Location = r.Cell(16).GetValue<string>(),
                    Sponsor = r.Cell(17).GetValue<string>(),
                    Organizer = r.Cell(18).GetValue<string>(),
                    Hospital = r.Cell(19).GetValue<string>(),
                    Theme = r.Cell(20).GetValue<string>(),
                    Department = new List<string>() { r.Cell(21).GetValue<string>(), r.Cell(22).GetValue<string>(), r.Cell(23).GetValue<string>() },
                    Url = r.Cell(24).GetValue<string>(),
                    Purpose = r.Cell(25).GetValue<string>(),
                    BussinessUnitId = bussinessUnitId
                });

                if (!rows.Any()) return Ok(MessageResult.FailureResult("导入内容为空!"));
                var response = await _crossBuService.VarifyImportMarketActivityAsync(rows.ToList());
                return Ok(response);
            }
            catch (Exception e)
            {
                return Ok(MessageResult.FailureResult(e.Message));
            }
        }

        /// <summary>
        /// 提交市场活动数据
        /// </summary>
        /// <param name="import"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        [Authorize(SpeakerPortalPermissions.MarketActvity.MarketActvityBatchAdd)]
        public async Task<IActionResult> ImportMarketActivityAsync([FromBody] ImportDataResponseDto<TransferMarketActivityDto> import)
        {
            //if (!import.IsSuccess) return Ok(MessageResult.FailureResult("验证失败!请修改后重新提交"));

            var response = await _crossBuService.ImportMarketActivityAsync([.. import.Datas]);
            return Ok(response);
        }

        /// <summary>
        /// 更新用户的收藏
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        [Authorize(SpeakerPortalPermissions.MarketActvity.MarketActvityH5Facorites)]
        public async Task<IActionResult> UpdateMarketActivityUserInformationAsync([FromBody] UpdateMarketActivityUserInformationDto request)
        {
            var response = await _crossBuService.UpdateMarketActivityUserInformationAsync(request);
            return Ok(response);
        }

        /// <summary>
        /// 获取用户的收藏
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Authorize(SpeakerPortalPermissions.MarketActvity.MarketActvityH5Read)]
        [ProducesDefaultResponseType(typeof(MessageResult<GetMarketActivityUserInformationResponse>))]
        public async Task<IActionResult> GetMarketActivityUserInformationAsync([FromQuery] Guid? id)
        {
            var response = await _crossBuService.GetMarketActivityUserInformationAsync(id);
            return Ok(response);
        }

        /// <summary>
        /// 创建意见反馈
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult<Guid>))]
        //[Authorize(SpeakerPortalPermissions.MarketActvity.MarketActvityCRUD)]
        //[Authorize(SpeakerPortalPermissions.CustomerRelation.CustomerRelationCRUD)]
        public async Task<IActionResult> CreateSuggestionFeedbackAsync([FromBody] CreateSuggestionFeedbackRequestDto request)
        {
            var response = await _crossBuService.CreateSuggestionFeedbackAsync(request);
            return Ok(response);
        }

        /// <summary>
        /// 筛选加入市场活动申请
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [Authorize(SpeakerPortalPermissions.MarketActvity.MarketActvityRead)]
        [ProducesDefaultResponseType(typeof(MessageResult<PagedResultDto<QueryJoinToMarketActivityListResponseDto>>))]
        public async Task<IActionResult> QueryJoinToMarketActivitiesAsync([FromBody] QueryJoinToMarketActivityRequestDto request)
        {
            var result = await _crossBuService.QueryJoinToMarketActivitiesAsync(request);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 修改加入市场活动审批状态
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult<Guid>))]
        [Authorize(SpeakerPortalPermissions.MarketActvity.MarketActvityCRUD)]
        public async Task<IActionResult> ModifyJoinToMarketActivityStatusAsync([FromBody] UpdateJoinToMarketActivityStatusRequestDto request)
        {
            await _crossBuService.ModifyJoinToMarketActivityStatusAsync(request);
            return Ok(MessageResult.SuccessResult("Success"));
        }

        /// <summary>
        /// H5加入活动
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult<Guid>))]
        [Authorize(SpeakerPortalPermissions.MarketActvity.MarketActvityH5JoinActivities)]
        public async Task<IActionResult> CreateH5ApplyJoinToMarketActivityAsync([FromBody] CreateH5ApplyJoinToMarketActivityDto request)
        {
            var result = await _crossBuService.CreateH5ApplyJoinToMarketActivityAsync(request);
            return Ok(result);
        }

        [HttpGet]
        [Authorize(SpeakerPortalPermissions.CustomerRelation.CustomerRelationH5Read)]
        [ProducesDefaultResponseType(typeof(MessageResult<PagedResultDto<QueryCustomerRelationListResponseH5Dto>>))]
        public async Task<IActionResult> QueryCustomerRelationsAsync([FromQuery] QueryCustomerRelationRequestDto request)
        {
            var result = await _crossBuService.QueryCustomerRelationsAsync(request);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 获取加入市场活动详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [Authorize(SpeakerPortalPermissions.MarketActvity.MarketActvityCRUD)]
        [ProducesDefaultResponseType(typeof(MessageResult<QueryMarketActivityResponseDto>))]
        public async Task<IActionResult> GetJoinToMarketActivitiesAsync([FromQuery] Guid? id)
        {
            if (id == null)
            {
                return Ok(MessageResult.FailureResult("未传入数据Id,获取加入市场活动失败"));
            }

            var result = await _crossBuService.GetJoinToMarketActivitiesAsync(id);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 获取第二级
        /// </summary>
        /// <param name="requestDto">The hospital identifier.</param>
        /// <returns></returns>
        [HttpPost]
        [Authorize(SpeakerPortalPermissions.CustomerRelation.CustomerRelationH5Read)]
        [ProducesDefaultResponseType(typeof(MessageResult<List<HospitalDepartment>>))]
        public async Task<IActionResult> QuerySecondCustomerRelationsAsync([FromBody] CustomerRelatedH5RequestDto requestDto)
        {
            var result = await _crossBuService.QueryCustomerRelationsAsync(requestDto.HospitalName, requestDto.Bu, requestDto.Province, requestDto.City);
            return Ok(MessageResult.SuccessResult(result));
        }
        /// <summary>
        /// 查询客户关系列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet]
        [Authorize(SpeakerPortalPermissions.CustomerRelation.CustomerRelationRead)]
        [ProducesDefaultResponseType(typeof(MessageResult<List<HospitalDepartment>>))]
        public async Task<IActionResult> QueryPortalCustomerRelationsAsync([FromQuery] QueryCustomerRelationRequestDto request)
        {
            var result = await _crossBuService.QueryPortalCustomerRelationsAsync(request);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 验证上传数据是否正确
        /// </summary>
        /// <param name="formFile"></param>
        /// <param name="bussinessUnitId"></param>
        /// <returns></returns>
        [HttpPost]
        [Authorize(SpeakerPortalPermissions.CustomerRelation.CustomerRelationBatchAdd)]
        [ProducesDefaultResponseType(typeof(MessageResult<ImportDataResponseDto<TransferCustomerRelationDto>>))]
        public async Task<IActionResult> VarifyImportCustomerRelationAsync([FromForm] IFormFile formFile, [FromForm] Guid bussinessUnitId)
        {
            try
            {
                var str = Path.GetExtension(formFile.FileName);
                string[] strings = [".xlsx", ".xls"];
                if (!strings.Contains(str))
                {
                    return Ok(MessageResult.FailureResult("请上传excel文件"));
                }
                if (bussinessUnitId == default)
                {
                    return Ok(MessageResult.FailureResult("请选择BU"));
                }
                using MemoryStream memoryStream = new();
                await formFile.CopyToAsync(memoryStream);
                memoryStream.Position = 0;
                using XLWorkbook xLWorkbook = new XLWorkbook(memoryStream);
                IXLWorksheet ws = xLWorkbook.Worksheets.First();
                var rows = ws.Rows().Skip(2).Select(r =>
                new TransferCustomerRelationDto
                {
                    DoctorId = r.Cell(1).GetValue<string>(),
                    DoctorName = r.Cell(2).GetValue<string>(),
                    DoctorTitle = r.Cell(3).GetValue<string>(),
                    DoctorLicenseCode = r.Cell(4).GetValue<string>(),
                    HospitalId = r.Cell(5).GetValue<string>(),
                    Hospital = r.Cell(6).GetValue<string>(),
                    HospitalProvince = r.Cell(7).GetValue<string>(),
                    HospitalCity = r.Cell(8).GetValue<string>(),
                    HospitalDepartment = r.Cell(9).GetValue<string>(),
                    Email = r.Cell(10).GetValue<string>(),
                });

                if (!rows.Any()) return Ok(MessageResult.FailureResult("导入内容为空!"));
                var response = await _crossBuService.VarifyImportCustomerRelationAsync(rows, bussinessUnitId);
                return Ok(response);
            }
            catch (Exception e)
            {
                return Ok(MessageResult.FailureResult(e.Message));
            }
        }

        /// <summary>
        /// 提交市场活动数据
        /// </summary>
        /// <param name="import"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        [Authorize(SpeakerPortalPermissions.CustomerRelation.CustomerRelationBatchAdd)]
        //[RequestSizeLimit(104857600)]
        public async Task<IActionResult> ImportCustomerRelationAsync([FromBody] ImportDataResponseDto<TransferCustomerRelationDto> import)
        {
            if (!import.IsSuccess) return Ok(MessageResult.FailureResult("验证失败!请修改后重新提交"));
            var response = await _crossBuService.ImportCustomerRelationAsync([.. import.Datas], Guid.NewGuid());
            return Ok(response);
        }

        /// <summary>
        /// 市场活动导出
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [Authorize(SpeakerPortalPermissions.CustomerRelation.CustomerRelationExport)]
        public async Task<IActionResult> ExportCustomerRelation([FromBody] QueryCustomerRelationRequestDto request)
        {
            var pageData = await _crossBuService.QueryPortalCustomerRelationsAsync(request, false);
            var customerRelations = pageData.Items;

            var attachmentService = LazyServiceProvider.LazyGetService<IAttachmentService>();

            var fieldInfo = TemplateTypes.CustomerRelation_Create.GetType().GetField(TemplateTypes.CustomerRelation_Export.ToString());
            var templateName = fieldInfo.GetCustomAttribute<CategoryAttribute>().Category;

            var templatesFile = await attachmentService.GetTemplatesFileAsync(TemplateTypes.CustomerRelation_Export, templateName);
            if (templatesFile == null)
            {
                return Ok(MessageResult.FailureResult("未获取到文件模版,请联系管理员"));
            }

            using MemoryStream memoryStream = new();
            await templatesFile.CopyToAsync(memoryStream);
            memoryStream.Position = 0;

            try
            {
                using (XLWorkbook xLWorkbook = new XLWorkbook(memoryStream))
                {
                    IXLWorksheet workbook = xLWorkbook.Worksheets.First();

                    for (int i = 0; i < customerRelations.Count; i++)
                    {
                        //第一行数据是填写说明，第二行数据是表头

                        var row = workbook.Row(i + 3);
                        row.Cell(1).Value = customerRelations[i].DoctorId;
                        row.Cell(2).Value = customerRelations[i].DoctorName;
                        row.Cell(3).Value = customerRelations[i].DoctorTitle;
                        row.Cell(4).Value = customerRelations[i].DoctorLicenseCode;
                        row.Cell(5).Value = customerRelations[i].HospitalId;
                        row.Cell(6).Value = customerRelations[i].Hospital;
                        row.Cell(7).Value = customerRelations[i].HospitalProvince;
                        row.Cell(8).Value = customerRelations[i].HospitalCity;
                        row.Cell(9).Value = customerRelations[i].HospitalDepartment;
                        row.Cell(10).Value = customerRelations[i].Email;
                        row.Cell(11).Value = customerRelations[i].HPCCode;
                        row.Cell(12).Value = customerRelations[i].StandardizedTitle;
                        row.Cell(13).Value = customerRelations[i].StandardizationPosition;
                        row.Cell(14).Value = customerRelations[i].HCOCode;
                        row.Cell(15).Value = customerRelations[i].CleanedHospitalName;
                        row.Cell(16).Value = customerRelations[i].HospitalType;
                        row.Cell(17).Value = customerRelations[i].StandardDepartmentId;
                        row.Cell(18).Value = customerRelations[i].StandardDepartmentName;
                        row.Cell(19).Value = customerRelations[i].StatusText;
                    }

                    // 保存到内存流
                    var stream = new MemoryStream();
                    xLWorkbook.SaveAs(stream);
                    stream.Seek(0, SeekOrigin.Begin);
                    return File(stream, "application/octet-stream", $"客户信息-{DateTime.Now:yyyyMMddHHmmss}.xlsx");
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, "Internal server error" + ex);
            }
        }
        /// <summary>
        /// Creates the customer relation asynchronous.
        /// </summary>
        /// <param name="requestDto">The request dto.</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        [Authorize(SpeakerPortalPermissions.MarketActvity.MarketActvityCRUD)]
        public async Task<IActionResult> CreateCustomerRelation([FromBody] CreateCustomerRelationRequestDto requestDto)
        {
            if (!ModelState.IsValid)
            {
                var errorFields = ModelState
                  .SelectMany(x => x.Value.Errors)
                  .Select(s => s.ErrorMessage)
                  .JoinAsString(";");
                return Ok(MessageResult.FailureResult(errorFields));
            }
            var result = await _crossBuService.CreateCustomerRelationAsync(requestDto);
            return Ok(result);
        }
        /// <summary>
        /// Edits the customer relation.
        /// </summary>
        /// <param name="requestDto">The request dto.</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        [Authorize(SpeakerPortalPermissions.MarketActvity.MarketActvityCRUD)]
        public async Task<IActionResult> EditCustomerRelation([FromBody] UpdateCustomerRelationRequestDto requestDto)
        {
            if (!ModelState.IsValid)
            {
                var errorFields = ModelState.Select(x => x.Value.Errors).Select(x => x.Select(s => s.ErrorMessage).JoinAsString(";")).JoinAsString(";");
                return Ok(MessageResult.FailureResult(errorFields));
            }
            var result = await _crossBuService.EditCustomerRelationAsync(requestDto);
            return Ok(result);
        }
        /// <summary>
        /// Gets the customer relation asynchronous.
        /// </summary>
        /// <param name="Id">The identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<GetCustomerRelationResponseDto>))]
        [Authorize(SpeakerPortalPermissions.CustomerRelation.CustomerRelationRead)]
        public async Task<IActionResult> GetCustomerRelation([FromQuery] Guid Id)
        {
            var result = await _crossBuService.GetCustomerRelationAsync(Id);
            return Ok(MessageResult.SuccessResult(result));
        }
        /// <summary>
        /// Modifies the status.
        /// </summary>
        /// <param name="requestDto">The request dto.</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        [Authorize(SpeakerPortalPermissions.MarketActvity.MarketActvityCRUD)]
        public async Task<IActionResult> ModifyStatus([FromBody] UpdateCustomerRelationStatusRequestDto requestDto)
        {
            await _crossBuService.ModifyStatusAsync(requestDto);
            return Ok(MessageResult.SuccessResult("Success"));
        }
        /// <summary>
        /// 获取历史变更记录
        /// </summary>
        /// <param name="requestDto">The request dto.</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult<PagedResultDto<CustomerRelationHistoryResponseDto>>))]
        [Authorize(SpeakerPortalPermissions.CustomerRelation.CustomerRelationRead)]
        public async Task<IActionResult> GetCustomerRelationChangeHis([FromBody] CustomerRelationHistoryRequestDto requestDto)
        {
            var result = await _crossBuService.GetHistoryRecordsByIdAsync(requestDto);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 获取当前用户的部门
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<List<GerUserDepartmentResponseDto>>))]
        public async Task<IActionResult> GetUserDepartment()
        {
            if (CurrentUser.Id == null)
            {
                Ok(MessageResult.FailureResult("获取当前用户失败"));
            }
            //if (Type.HasValue)
            //{
            //    var result = await _crossBuService.GetDepartment(Type.Value);
            //    return Ok(MessageResult.SuccessResult(result));
            //}
            //else
            //{

            //}
            var result = await _crossBuService.GetUserDepartment();
            return Ok(MessageResult.SuccessResult(result));

        }
        /// <summary>
        ///删除客户关系
        /// </summary>
        /// <param name="Id">The identifier.</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        [Authorize(SpeakerPortalPermissions.CustomerRelation.CustomerRelationCRUD)]
        public async Task<IActionResult> DeleteCustomerRelation([FromQuery] Guid Id)
        {
            var result = await _crossBuService.DeleteCustomerRelationAsync(Id);
            return Ok(result);
        }
        /// <summary>
        /// 联系分享人，并发邮件
        /// </summary>
        /// <param name="resquestDto">The resquest dto.</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        //[Authorize(SpeakerPortalPermissions.CustomerRelation.CustomerRelationRead)]
        public async Task<IActionResult> ContactShare([FromBody] ContactShareResquestDto resquestDto)
        {
            var result = await _crossBuService.ContactShareAsync(resquestDto);
            return Ok(result);
        }


        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        //[Authorize(SpeakerPortalPermissions.CustomerRelation.CustomerRelationRead)]
        public async Task<IActionResult> Cleaned([FromForm] IFormFile formFile)
        {
            //using MemoryStream memoryStream = new();
            //await formFile.CopyToAsync(memoryStream);
            //memoryStream.Position = 0;
            //using XLWorkbook xLWorkbook = new XLWorkbook(memoryStream);
            //IXLWorksheet ws = xLWorkbook.Worksheets.First();
            //var rows = ws.Rows().Skip(1).Select(r => new KeyValuePair<string, string>(r.Cell(1).GetValue<string>(), r.Cell(2).GetValue<string>()));
            var result = await _crossBuService.Cleaned([]);
            return Ok(result);
        }
    }
}
