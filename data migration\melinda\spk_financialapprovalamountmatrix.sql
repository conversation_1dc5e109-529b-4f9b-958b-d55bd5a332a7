select newid() as spk_NexBPMCode,* into #spk_financialapprovalamountmatrix from (
select ft.spk_bu as spk_BPMCode,ot.spk_NexBPMCode as spk_bu,ft.spk_procurementfinance,ft.spk_name  from spk_financialapprovalamountmatrix_Tmp ft
join spk_organizationalmasterdata ot
on ot.spk_BPMCode=ft.spk_bu) A

IF OBJECT_ID(N'dbo.spk_financialapprovalamountmatrix', N'U') IS NOT NULL
BEGIN
	update a 
	set a.spk_BPMCode             = b.spk_BPMCode
        ,a.spk_bu                 = b.spk_bu
        ,a.spk_procurementfinance = b.spk_procurementfinance
        ,a.spk_name               = b.spk_name
    from dbo.spk_financialapprovalamountmatrix a
    join #spk_financialapprovalamountmatrix b on a.spk_BPMCode = b.spk_BPMCode
    
    insert into dbo.spk_financialapprovalamountmatrix
    select a.spk_NexBPMCode
          ,a.spk_BPMCode
          ,a.spk_bu
          ,a.spk_procurementfinance
          ,a.spk_name
	from #spk_financialapprovalamountmatrix a
	where NOT EXISTS (SELECT * FROM dbo.spk_financialapprovalamountmatrix where a.spk_BPMCode = spk_BPMCode)
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into dbo.spk_financialapprovalamountmatrix from #spk_financialapprovalamountmatrix
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
