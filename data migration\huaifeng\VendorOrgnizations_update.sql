SELECT
 TRY_CONVERT(UNIQUEIDENTIFIER, [Id]) [Id]
,TRY_CONVERT(UNIQUEIDENTIFIER, iif([VendorId] is NULL,'00000000-0000-0000-0000-000000000000',[VendorId])) [VendorId]
,VendorName as [VendorName]
,[VendorOldName]
,[VendorEngName]
,N'NULL' [RegCertificateAddress] -- 转换为空的数据，需检查中间层逻辑
,N'NULL'[PostCode]
,N'NULL'[ContactName]
,[ContactPhone]
,[ContactEmail]
,[WebSite]
,GETDATE() [RegisterDate] --数据为空，需检查中间层逻辑
,[OrgType]
,[IssuingAuthority]
,[RegisterCode]
,GETDATE()[RegValidityStart]
,GETDATE()[RegValidityEnd]
,N'NULL' [Province] -- 转换为空的数据，需检查中间层逻辑
,N'NULL' [City] -- 转换为空的数据，需检查中间层逻辑
,[Legal]
,0 AS [RegisterAmount] --字段值部分为空
,[BusinessAuthority]
,[BusinessScope]
,0 AS [LastYearSales] --字段值部分为空
,[KeyIndustry]
,[KeyClient]
,0 [Staffs]
,[Aptitudes]
,N'NULL' [ApplyReason] -- 转换为空的数据，需检查中间层逻辑
,N'{}' [ExtraProperties] -- 转换为空的数据，需检查中间层逻辑
,N'NULL' [ConcurrencyStamp] -- 转换为空的数据，需检查中间层逻辑
,GETDATE() [CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CreatorId]) [CreatorId]
,CASE WHEN [LastModificationTime] IS NOT NULL
	THEN GETDATE()
	ELSE [LastModificationTime]
	END [LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [LastModifierId]) [LastModifierId]
,[IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, [DeleterId]) [DeleterId]
,CASE WHEN [DeletionTime] IS NOT NULL
	THEN GETDATE()
	ELSE [DeletionTime]
	END [DeletionTime]
,[Shareholder]
INTO #VendorOrgnizations
FROM (SELECT DISTINCT *,ROW_NUMBER() OVER(PARTITION BY Id ORDER BY Id) RK FROM PLATFORM_ABBOTT_STG.dbo.VendorOrgnizations)a
WHERE RK = 1

--select * from PLATFORM_ABBOTT_STG.dbo.VendorOrgnizations

--drop table #VendorOrgnizations
USE Speaker_Portal_STG;

UPDATE a
SET
 a.[Id] = b.[Id]
,a.[VendorId] = b.[VendorId]
,a.[VendorName] = b.[VendorName]
,a.[VendorOldName] = b.[VendorOldName]
,a.[VendorEngName] = b.[VendorEngName]
,a.[RegCertificateAddress] = b.[RegCertificateAddress]
,a.[PostCode] = b.[PostCode]
,a.[ContactName] = b.[ContactName]
,a.[ContactPhone] = b.[ContactPhone]
,a.[ContactEmail] = b.[ContactEmail]
,a.[WebSite] = b.[WebSite]
,a.[RegisterDate] = b.[RegisterDate]
,a.[OrgType] = b.[OrgType]
,a.[IssuingAuthority] = b.[IssuingAuthority]
,a.[RegisterCode] = b.[RegisterCode]
,a.[RegValidityStart] = b.[RegValidityStart]
,a.[RegValidityEnd] = b.[RegValidityEnd]
,a.[Province] = b.[Province]
,a.[City] = b.[City]
,a.[Legal] = b.[Legal]
,a.[RegisterAmount] = b.[RegisterAmount]
,a.[BusinessAuthority] = b.[BusinessAuthority]
,a.[BusinessScope] = b.[BusinessScope]
,a.[LastYearSales] = b.[LastYearSales]
,a.[KeyIndustry] = b.[KeyIndustry]
,a.[KeyClient] = b.[KeyClient]
,a.[Staffs] = b.[Staffs]
,a.[Aptitudes] = b.[Aptitudes]
,a.[ApplyReason] = b.[ApplyReason]
,a.[ExtraProperties] = b.[ExtraProperties]
,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = b.[CreatorId]
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = b.[LastModifierId]
,a.[IsDeleted] = b.[IsDeleted]
,a.[DeleterId] = b.[DeleterId]
,a.[DeletionTime] = b.[DeletionTime]
,a.[Shareholder] = b.[Shareholder]
FROM dbo.VendorOrgnizations a
left join #VendorOrgnizations  b
ON a.id=b.id


--字段长度问题
INSERT INTO dbo.VendorOrgnizations
(
 [Id]
,[VendorId]
,[VendorName]
,[VendorOldName]
,[VendorEngName]
,[RegCertificateAddress]
,[PostCode]
,[ContactName]
,[ContactPhone]
,[ContactEmail]
,[WebSite]
,[RegisterDate]
,[OrgType]
,[IssuingAuthority]
,[RegisterCode]
,[RegValidityStart]
,[RegValidityEnd]
,[Province]
,[City]
,[Legal]
,[RegisterAmount]
,[BusinessAuthority]
,[BusinessScope]
,[LastYearSales]
,[KeyIndustry]
,[KeyClient]
,[Staffs]
,[Aptitudes]
,[ApplyReason]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[Shareholder]
)
SELECT 
 [Id]
,[VendorId]
,[VendorName]
,[VendorOldName]
,[VendorEngName]
,[RegCertificateAddress]
,[PostCode]
,[ContactName]
,[ContactPhone]
,[ContactEmail]
,[WebSite]
,[RegisterDate]
,[OrgType]
,[IssuingAuthority]
,[RegisterCode]
,[RegValidityStart]
,[RegValidityEnd]
,[Province]
,[City]
,[Legal]
,[RegisterAmount]
,[BusinessAuthority]
,[BusinessScope]
,[LastYearSales]
,[KeyIndustry]
,[KeyClient]
,[Staffs]
,[Aptitudes]
,[ApplyReason]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[Shareholder]
FROM #VendorOrgnizations a
WHERE not exists (select * from dbo.VendorOrgnizations where id=a.id)


--truncate table dbo.VendorOrgnizations

--alter table dbo.VendorOrgnizations
--alter column [ContactPhone] nvarchar(25)