﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Budget.FocSubbudget;
using Abbott.SpeakerPortal.Contracts.Budget.Subbudget;
using Abbott.SpeakerPortal.Contracts.Common.Attachment;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.Budget;
using Abbott.SpeakerPortal.Entities.Budget.BdExpense;
using Abbott.SpeakerPortal.Entities.Common.Attachment;
using Abbott.SpeakerPortal.Entities.EFlow.FOC;
using Abbott.SpeakerPortal.Entities.EFlow.STicket;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Enums;
using Microsoft.EntityFrameworkCore;
using MiniExcelLibs;
using MiniExcelLibs.Attributes;
using MiniExcelLibs.OpenXml;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.DistributedLocking;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using static Abbott.SpeakerPortal.Permissions.SpeakerPortalPermissions;

namespace Abbott.SpeakerPortal.AppServices.Budget
{
    public class FocSubbudgetService : SpeakerPortalAppService, IFocSubbudgetService
    {
        /// <summary>
        /// 新增子预算-单个
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> CreateFocSubbudgetAsync(CreateFocSubbudgetRequestDto request)
        {
            var subbudgetRespository = LazyServiceProvider.LazyGetService<IFocSubBudgetRepository>();
            var queryMasterBudget = await LazyServiceProvider.LazyGetService<IFocMasterBudgetRepository>().GetQueryableAsync();

            //同一主预算下的所有子预算的总金额不能超过主预算的金额
            var masterBudgetData = queryMasterBudget.Include(x => x.SubBudgets).FirstOrDefault(x => x.Id == request.MasterBudgetId);
            if (masterBudgetData != null)
            {
                int subbudgetTotalQty = request.BudgetQty;
                if (masterBudgetData.SubBudgets?.Count > 0)
                    subbudgetTotalQty += masterBudgetData.SubBudgets.Sum(x => x.BudgetQty);
                if (masterBudgetData.BudgetQty < subbudgetTotalQty)
                    return MessageResult.FailureResult($"数量超出主预算数量！（主预算数量{masterBudgetData.BudgetQty}，其中可用数量{(masterBudgetData.BudgetQty - subbudgetTotalQty)}）");
            }

            var subbudget = ObjectMapper.Map<CreateFocSubbudgetRequestDto, FocSubBudget>(request);
            var productQuery = await LazyServiceProvider.LazyGetService<IProductDetailsRepository>().GetQueryableAsync();
            var product = await productQuery.FirstOrDefaultAsync(a => a.Id == request.ProductId);
            if (product != null)
            {
                subbudget.ProductName = product.ProductShortNameEN;
                subbudget.ProductMCode = product.ProductMCode;
                subbudget.ProductUnit = product.Unit;
            }
            //subbudget.BudgetAmount = request.MonthlyBudgets.Where(s => s.Status).Sum(s => s.BudgetAmount);
            subbudget.Status = true;
            if (request.AttachmentInformation?.Count > 0)
                subbudget.AttachmentFile = request.AttachmentInformation?.Select(x => x.AttachmentId.ToString()).JoinAsString(",");

            List<FocSubBudgetMonthChangeHistory> changeHistories = [];
            foreach (var item in subbudget.MonthlyBudgets)
            {
                FocSubBudgetMonthChangeHistory bdSubBudgetHis = new()
                {
                    Month = item.Month,
                    OperateQty = item.BudgetQty,
                    Status = item.Status,
                };
                bdSubBudgetHis.SetId(Guid.NewGuid());
                changeHistories.Add(bdSubBudgetHis);
            }
            var subbudgetId = await GenerateSubCode(subbudgetRespository, subbudget, masterBudgetData.Year.HasValue ? (masterBudgetData.Year.Value < 1000 ? DateTimeOffset.Now.Year : masterBudgetData.Year.Value) : DateTimeOffset.Now.Year);

            //History
            FocBudgetHistory history = new()
            {
                OperateType = OperateType.Create,
                OperateContent = $"预算数量：{request.BudgetQty}；预算负责人：{request.OwnerName}；描述：{subbudget.Description}",
                BudgetId = subbudgetId,
                OperatorId = CurrentUser.Id.Value,
                OperatorName = CurrentUser.Name,
                OperateQty = request.BudgetQty,
                OperatingTime = DateTime.Now,
                Remark = request.Remark,
                BudgetType = BudgetType.FocSubBudget,
                SubBudgetMonthChangeHistorys = changeHistories
            };

            await SaveHistory([history]);

            return MessageResult.SuccessResult(subbudgetId);
        }

        /// <summary>
        /// 调整子预算
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        /// <exception cref="System.NotImplementedException"></exception>
        public async Task<MessageResult> AdjustingFocSubbudgetAsync(AdjustFocSubbudgetRequestDto request)
        {
            var subbudgetRespository = LazyServiceProvider.LazyGetService<IFocSubBudgetRepository>();
            var queryMasterBudget = await LazyServiceProvider.LazyGetService<IFocMasterBudgetRepository>().GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();

            var subbudget = (await subbudgetRespository.GetQueryableAsync()).Include(s => s.MonthlyBudgets).FirstOrDefault(x => x.Id == request.Id);
            if (subbudget == null)
                return MessageResult.FailureResult("找不到相应的子预算数据");

            int adjustTotalQty = request.AdjustFocMonthQtys.Sum(s => s.AdjustQty.Value);
            //调整数量为负数时，判断调整数量是否大于可用数量
            if (adjustTotalQty < 0)
            {
                if (Math.Abs(adjustTotalQty) > subbudget.BudgetQty - subbudget.UesdQty)
                    return MessageResult.FailureResult($"调整数量不能大于可用数量");
            }

            //同一主预算下的所有子预算的总数量不能超过主预算的数量
            var masterBudgetData = queryMasterBudget.Include(x => x.SubBudgets).FirstOrDefault(x => x.Id == subbudget.MasterBudgetId);
            if (masterBudgetData != null)
            {
                var subbudgetTotalQty = masterBudgetData.SubBudgets.Sum(x => x.BudgetQty) + adjustTotalQty;
                if (masterBudgetData.BudgetQty < subbudgetTotalQty)
                    return MessageResult.FailureResult($"数量超出主预算数量！（主预算数量{masterBudgetData.BudgetQty:F2}，其中可用数量{(masterBudgetData.BudgetQty - subbudgetTotalQty):F2}）");
            }

            var orgs = await dataverseService.GetOrganizations(subbudget.BuId.ToString());
            if (orgs.Count > 0)
            {
                if (orgs.First().DepartmentName == "ADC")//BU为ADC时才能设置“IsComplicanceAudits”
                    subbudget.IsComplicanceAudits = request.IsComplicanceAudits;
            }

            subbudget.CostCenterId = request.CostCenterId;
            subbudget.RegionId = request.RegionId;
            subbudget.OwnerId = request.OwnerId;
            subbudget.Description = request.Description;
            subbudget.BudgetQty += adjustTotalQty;
            //subbudget.AvailableAmount += subbudgetTotalAmount;
            subbudget.Remark = request.Remark;
            if (request.AttachmentInformation?.Count < 1)
                subbudget.AttachmentFile = null;
            else
                subbudget.AttachmentFile = request.AttachmentInformation.Select(x => x.AttachmentId.ToString()).JoinAsString(",");
            //修改调正预算
            var MothlyBudget = subbudget.MonthlyBudgets.OrderBy(o => o.Month);
            string Decription = string.Empty;
            List<FocSubBudgetMonthChangeHistory> changeMonths = [];
            foreach (var item in MothlyBudget)
            {
                var adjustQty = request.AdjustFocMonthQtys.FirstOrDefault(s => s.Month == item.Month);
                if (adjustQty == null) continue;
                FocSubBudgetMonthChangeHistory bdSubBudgetHis = new();
                bdSubBudgetHis.Month = item.Month;
                bdSubBudgetHis.SetId(Guid.NewGuid());
                bool isset = false;//是否写入历史
                if (adjustQty.AdjustQty.HasValue && adjustQty.AdjustQty != 0)
                {
                    bdSubBudgetHis.OperateQty = adjustQty.AdjustQty;
                    item.BudgetQty += adjustQty.AdjustQty.Value;
                    isset = true;
                }
                if (item.Status != adjustQty.Status || adjustQty.AdjustQty != 0)
                {
                    string EnableText = item.Status ? "是" : "否";
                    Decription += $"修改数量：{adjustQty.AdjustQty}，修改内容：月份{(int)adjustQty.Month}月，是否启用：{EnableText}";
                }
                if (item.Status != adjustQty.Status) { bdSubBudgetHis.Status = adjustQty.Status; isset = true; item.Status = adjustQty.Status; }
                if (isset) changeMonths.Add(bdSubBudgetHis);
            }

            await subbudgetRespository.UpdateAsync(subbudget);

            FocBudgetHistory history = new()
            {
                OperateType = OperateType.Update,
                OperateContent = $"调整总数量：{adjustTotalQty}；{Decription}；预算负责人：{request.OwnerName}；描述：{subbudget.Description}",
                BudgetId = subbudget.Id,
                OperatorId = CurrentUser.Id.Value,
                OperatorName = CurrentUser.Name,
                OperatingTime = DateTime.Now,
                Remark = request.Remark,
                BudgetType = BudgetType.FocSubBudget,
                OperateQty = adjustTotalQty,
                SubBudgetMonthChangeHistorys = changeMonths,
            };

            await SaveHistory([history]);

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 删除子预算
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MessageResult> DeleteFocSubbudget(List<Guid> ids)
        {
            var subbudgetRespository = LazyServiceProvider.LazyGetService<IFocSubBudgetRepository>();
            var querySubbudget = await LazyServiceProvider.LazyGetService<IFocSubBudgetRepository>().GetQueryableAsync();

            var query = querySubbudget.Where(x => ids.Contains(x.Id));
            var count = query.Count();
            if (count < ids.Count)
                return MessageResult.FailureResult("id不存在");
            var datas = query.ToList();
            if (datas.Any(x => x.UesdQty > 0))
                return MessageResult.FailureResult("已用子预算不可删除");

            await subbudgetRespository.DeleteManyAsync(datas);

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 导出子预算列表
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<Stream> ExportFocSubbudgetListAsync(GetFocSubbudgetListRequestDto request)
        {
            MemoryStream stream = new();
            var response = await GetFocSubbudgetManageListAsync(request, false);
            var properties = typeof(GetFocSubbudgetListResponseDto).GetProperties().ToList();
            var config = new OpenXmlConfiguration() { TableStyles = MiniExcelLibs.OpenXml.TableStyles.None, AutoFilter = false };
            var specialBu = new List<string> { "EPD", "ADC" };
            if (specialBu.Contains(request.BuName))
            {
                var dynamicCols = new List<DynamicExcelColumn>();
                properties.ForEach(x =>
                {
                    var attribute = Attribute.GetCustomAttribute(x, typeof(CategoryAttribute)) as CategoryAttribute;
                    if (null != attribute && !string.IsNullOrEmpty(attribute.Category))
                        dynamicCols.Add(new DynamicExcelColumn(x.Name) { Ignore = !attribute.Category.Equals(request.BuName) });
                });
                config.DynamicColumns = [.. dynamicCols];
            }

            stream.SaveAs(response.Items, true, "Sheet1", configuration: config);
            stream.Seek(0, SeekOrigin.Begin);
            return stream;
        }

        /// <summary>
        /// 获取子预算详情
        /// </summary>
        /// <param name="subbudgetId"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MessageResult> GetFocSubbudgetDetailAsync(Guid subbudgetId)
        {
            var querySubbudget = await LazyServiceProvider.LazyGetService<IFocSubBudgetRepository>().GetQueryableAsync();
            var attachmentQuery = await LazyServiceProvider.LazyGetService<IAttachmentRepository>().GetQueryableAsync();
            var userQuery = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();

            var subbudgetDt = querySubbudget.Include(i => i.MonthlyBudgets.OrderBy(o => o.Month)).Where(x => x.Id == subbudgetId)
                .GroupJoin(userQuery, a => a.OwnerId, b => b.Id, (a, b) => new { subbudget = a, users = b })
                .SelectMany(a => a.users.DefaultIfEmpty(), (a, b) => new { a.subbudget, user = b })
                .FirstOrDefault();
            if (subbudgetDt == null)
                return MessageResult.FailureResult("找不到数据");
            var subbudgetInfo = ObjectMapper.Map<FocSubBudget, GetFocSubbudgetDetailResponseDto>(subbudgetDt.subbudget);

            #region Guid显示名
            var costCenters = await dataverseService.GetCostcentersAsync(subbudgetDt.subbudget.CostCenterId.ToString());
            subbudgetInfo.CostCenterName = costCenters?.FirstOrDefault()?.Name;
            //大区
            var district = await dataverseService.GetDistrict(subbudgetDt.subbudget.RegionId.ToString());
            subbudgetInfo.RegionName = district?.FirstOrDefault().Name;
            //预算负责人
            subbudgetInfo.OwnerName = subbudgetDt.user?.Name;
            #endregion

            //附件
            if (!string.IsNullOrEmpty(subbudgetDt.subbudget.AttachmentFile))
            {
                var attachmentIds = subbudgetDt.subbudget.AttachmentFile.Split(',').Select(x => Guid.Parse(x)).ToList();
                if (attachmentIds.Count() > 0)
                {
                    var attachmentEntities = attachmentQuery.Where(x => attachmentIds.Contains(x.Id)).ToList();
                    var attachmentInfo = ObjectMapper.Map<List<Attachment>, List<UploadFileResponseDto>>(attachmentEntities);
                    subbudgetInfo.AttachmentInformation = attachmentInfo;
                }
            }

            return MessageResult.SuccessResult(subbudgetInfo);
        }

        /// <summary>
        /// 获取子预算管理列表
        /// </summary>
        /// <param name="request"></param>
        /// <param name="isPaging"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<PagedResultDto<GetFocSubbudgetListResponseDto>> GetFocSubbudgetManageListAsync(GetFocSubbudgetListRequestDto request, bool isPaging = true)
        {
            var querySubbudget = await LazyServiceProvider.LazyGetService<IFocSubBudgetRepository>().GetQueryableAsync();
            var attachmentQuery = await LazyServiceProvider.LazyGetService<IAttachmentRepository>().GetQueryableAsync();
            var userQuery = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();

            var query = querySubbudget.Include(x => x.MasterBudget).Include(x => x.MonthlyBudgets).Where(x => x.BuId == request.BuId)
                .WhereIf(!string.IsNullOrEmpty(request.MasterBudgetCode), x => x.MasterBudget.Code == request.MasterBudgetCode)
                .WhereIf(request.MasterBudgetId.HasValue, x => x.MasterBudgetId == request.MasterBudgetId)
                .WhereIf(request.CostCenterId.HasValue, x => x.CostCenterId == request.CostCenterId)
                //.WhereIf(request.ProductId.HasValue, x => x.ProductId == request.ProductId)
                .WhereIf(!string.IsNullOrEmpty(request.SubbudgetCode), x => x.Code == request.SubbudgetCode)
                .WhereIf(!string.IsNullOrEmpty(request.ProductMCode), x => x.ProductMCode == request.ProductMCode)
                .WhereIf(request.Year.HasValue, x => x.MasterBudget.Year == request.Year)
                .WhereIf(!string.IsNullOrEmpty(request.Description), x => x.Description.Contains(request.Description))
                .WhereIf(request.MasterBudgetOwnerId.HasValue, x => x.MasterBudget.OwnerId == request.MasterBudgetOwnerId)
                .WhereIf(request.SubbudgetOwnerId.HasValue, x => x.OwnerId == request.SubbudgetOwnerId)
                .OrderByDescending(o => o.CreationTime).AsQueryable();
            var count = await query.CountAsync();

            var users = await dataverseService.GetStaffs(stateCode: null);
            var orgs = await dataverseService.GetOrganizations(stateCode: null);
            var coscenters = await dataverseService.GetCostcentersAsync(stateCode: null);
            var regions = await dataverseService.GetDistrict(stateCode: null);

            if (isPaging)
                query = query.Skip(request.PageIndex * request.PageSize).Take(request.PageSize);

            var pageDatas = query.ToArray().Select(x =>
            {
                var user = users.FirstOrDefault(a => a.Id == x.OwnerId);
                var response = new GetFocSubbudgetListResponseDto
                {
                    Id = x.Id,
                    SubbudgetCode = x.Code,
                    Description = x.Description,
                    BudgetQty = x.BudgetQty,
                    UsedQty = x.UesdQty,
                    //AvailableAmount = x.AvailableAmount,
                    MasterBudgetId = x.MasterBudgetId,
                    MasterBudgetCode = x.MasterBudget.Code,
                    BuId = x.BuId,
                    BuName = orgs.FirstOrDefault(a => a.Id == x.BuId)?.DepartmentName,
                    CostCenterId = x.CostCenterId,
                    CostCenterName = coscenters.FirstOrDefault(a => a.Id == x.CostCenterId)?.Name,
                    RegionId = x.RegionId,
                    RegionName = regions.FirstOrDefault(a => a.Id == x.RegionId).Name,
                    OwnerId = x.OwnerId,
                    OwnerName = user?.Name,
                    OwnerEmail = user?.Email,
                    ProductMCode = x.ProductMCode,
                    ProductName = x.ProductName,
                    Status = x.Status,
                    IsDeletable = x.UesdQty <= 0,
                    Year = x.MasterBudget.Year,

                    Monthlies = x.MonthlyBudgets.Select(s => new FocMonthlyBudgetDto() { BudgetQty = s.BudgetQty, Month = s.Month, Status = s.Status }).ToDictionary(a => a.Month)
                };
                if (isPaging) response.AttachmentInformation = GetAttachmentInfo(x.AttachmentFile).Result?.ToList();
                if (request.BuName == "EPD")
                {
                    response.BU2 = x.Bu2;
                    response.OwnerName2 = x.Owner2;
                    response.RegionManagerName = x.RegionManagers;
                    response.LMMName = x.LMMs;
                    response.ProductManagerName = x.ProductManagers;
                }
                else if (request.BuName == "ADC")
                {
                    response.IsComplicanceAudits = x.IsComplicanceAudits;
                }
                return response;
            }).ToArray();

            var result = new PagedResultDto<GetFocSubbudgetListResponseDto>(count, pageDatas);
            return result;
        }

        /// <summary>
        /// 设置子预算状态为启用或冻结
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MessageResult> SetFocSubbudgetStatusAsync(List<Guid> ids, bool status)
        {
            var subbudgetRespository = LazyServiceProvider.LazyGetService<IFocSubBudgetRepository>();
            var querySubbudget = await LazyServiceProvider.LazyGetService<IFocSubBudgetRepository>().GetQueryableAsync();

            var query = querySubbudget.Where(x => ids.Contains(x.Id));
            var count = query.Count();
            if (count < ids.Count)
                return MessageResult.FailureResult("id不存在");
            var datas = query.ToList();
            if (datas.Any(x => x.Status == status))
                return MessageResult.FailureResult(string.Format("已{0}的子预算不可{0}", status ? "启用" : "冻结"));

            //History
            var histories = new List<FocBudgetHistory>();
            datas.ForEach(x =>
            {
                x.Status = status;
                histories.Add(new FocBudgetHistory
                {
                    OperateType = status ? OperateType.Enable : OperateType.Disable,
                    OperateContent = string.Format("是否启用：{0}", status ? "是" : "否"),
                    BudgetId = x.Id,
                    OperatorId = CurrentUser.Id.Value,
                    OperatorName = CurrentUser.Name,
                    OperatingTime = DateTime.Now,
                    BudgetType = BudgetType.FocSubBudget
                });
            });
            await SaveHistory(histories);

            await subbudgetRespository.UpdateManyAsync(datas);

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 批量新增
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MessageResult> SubmitBatchCreateFocSubBudgetAsync(List<CreateSubBudgetImportMessageDto> request)
        {
            var subbudgetRespository = LazyServiceProvider.LazyGetService<IFocSubBudgetRepository>();
            var query = (await subbudgetRespository.GetQueryableAsync()).AsNoTracking();
            var masterCodes = request.Select(c => c.MasterBudgetCode).ToList();
            var queryMasterRepository = LazyServiceProvider.LazyGetService<IFocMasterBudgetRepository>();
            var queryMasterBudget = (await queryMasterRepository.GetQueryableAsync()).AsNoTracking();
            var mtBudget = queryMasterBudget.Where(m => masterCodes.Contains(m.Code)).ToList();
            List<FocSubBudget> bdSubBudgets = [];
            List<FocBudgetHistory> his = [];
            Dictionary<string, string> keyValues = [];
            request.ForEach(s =>
            {
                //var code = $"CC{now:yy}-{++count:D4}";
                var Id = Guid.NewGuid();
                var Qty = s.MonthlyBudgets.Sum(s => s.BudgetQty);
                var monthlyBudgets = ObjectMapper.Map<List<FocMonthlyBudgetDto>, List<FocMonthlyBudget>>(s.MonthlyBudgets);

                List<FocSubBudgetMonthChangeHistory> changeHistories = [];
                foreach (var item in monthlyBudgets)
                {
                    FocSubBudgetMonthChangeHistory bdSubBudgetHis = new()
                    {
                        Month = item.Month,
                        OperateQty = item.BudgetQty,
                        Status = item.Status,
                    };
                    bdSubBudgetHis.SetId(Guid.NewGuid());
                    changeHistories.Add(bdSubBudgetHis);
                }
                FocSubBudget bds = new()
                {
                    //Code = code,
                    Description = s.Description,
                    Remark = s.Remark,
                    BudgetQty = Qty,
                    CostCenterId = s.CostCenterId.Value,
                    RegionId = s.RegionId.Value,
                    OwnerId = s.OwnerId.Value,
                    Status = true,
                    MasterBudgetId = s.MasterBudgetId.Value,
                    ProductMCode = s.ProductMCode,
                    ProductName = s.ProductName,
                    ProductUnit = s.ProductUnit,
                    BuId = mtBudget.First(m => m.Code == s.MasterBudgetCode).BuId,
                    MonthlyBudgets = monthlyBudgets
                };
                bds.SetId(Id);
                bdSubBudgets.Add(bds);
                FocBudgetHistory history = new()
                {
                    OperateType = OperateType.Create,
                    OperateContent = $"预算数量：{Qty}；预算负责人：{s.OwnerName}；描述：{s.Description}",
                    BudgetId = Id,
                    OperatorId = CurrentUser.Id.Value,
                    OperatorName = CurrentUser.Name,
                    OperatingTime = DateTime.Now,
                    OperateQty = Qty,
                    Remark = s.Remark,
                    BudgetType = BudgetType.FocSubBudget,
                    SubBudgetMonthChangeHistorys = changeHistories
                };
                his.Add(history);
            });
            //await subbudgetRespository.InsertManyAsync(bdSubBudgets);
            await BatchSGenerateubCode(subbudgetRespository, bdSubBudgets);
            await SaveHistory(his);
            return MessageResult.SuccessResult("操作成功");
        }

        /// <summary>
        /// 批量提交调整子预算
        /// </summary>
        /// <param name="adjustments"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MessageResult> SubmitImportAdjustmentFocSubBudgetAsync(List<AdjustmentFocBudgetDto> adjustments)
        {
            var budgetCodes = adjustments.Select(a => a.Code);
            var querySubBudgetRepository = LazyServiceProvider.LazyGetService<IFocSubBudgetRepository>();
            var querySubBudget = await querySubBudgetRepository.GetQueryableAsync();
            var subBudgets = querySubBudget.Include(s => s.MonthlyBudgets).Where(m => budgetCodes.Contains(m.Code)).ToList();
            var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var ownerIds = subBudgets.Select(m => m.OwnerId).ToList();
            var users = queryableUser.Where(m => ownerIds.Contains(m.Id));
            List<FocBudgetHistory> his = [];
            foreach (var item in adjustments)
            {
                var Budget = subBudgets.First(m => m.Code == item.Code);
                var ownerName = users.FirstOrDefault(m => m.Id == Budget.OwnerId).Name ?? "";
                var modifyMonths = item.MonthlyBudgets;
                //调整总金额
                var adjustmentTotal = modifyMonths.Sum(s => s.BudgetQty);
                var Decription = string.Empty;
                List<FocSubBudgetMonthChangeHistory> changeMonths = [];
                foreach (var m in modifyMonths)
                {
                    if (!m.Status.HasValue && (!m.BudgetQty.HasValue || m.BudgetQty == 0))
                        continue;
                    //调整的月份
                    FocSubBudgetMonthChangeHistory bdSubBudget = new() { Month = m.Month };
                    bdSubBudget.SetId(Guid.NewGuid());
                    var adjustBd = Budget.MonthlyBudgets.First(f => f.Month == m.Month);
                    Decription += $"修改月份：{(int)m.Month}月；";
                    if (m.BudgetQty.HasValue && m.BudgetQty != 0)
                    {
                        adjustBd.BudgetQty += m.BudgetQty.Value;
                        Decription += $"修改数量：{m.BudgetQty}；";
                        bdSubBudget.OperateQty = m.BudgetQty;
                    }
                    if (m.Status.HasValue)
                    {
                        adjustBd.Status = m.Status.Value;
                        bdSubBudget.Status = m.Status;
                        Decription += $"修改内容：是否启用：{(m.Status.Value ? "是" : "否")}";
                    }
                    if (m.BudgetQty.HasValue || m.Status.HasValue) changeMonths.Add(bdSubBudget);
                }
                Budget.BudgetQty += adjustmentTotal.Value;
                his.Add(new()
                {
                    OperateType = OperateType.Update,
                    OperateContent = $"调整总数量：{adjustmentTotal}；预算负责人：{ownerName}；描述：{item.Remark}，{Decription}",
                    BudgetId = Budget.Id,
                    OperatorId = CurrentUser.Id.Value,
                    OperatorName = CurrentUser.Name,
                    OperatingTime = DateTime.Now,
                    OperateQty = adjustmentTotal,
                    Remark = item.Remark,
                    BudgetType = BudgetType.FocSubBudget,
                    SubBudgetMonthChangeHistorys = changeMonths
                });
            }

            await querySubBudgetRepository.UpdateManyAsync(subBudgets);
            await SaveHistory(his);
            return MessageResult.SuccessResult("操作成功");
        }

        /// <summary>
        /// 批量新增
        /// </summary>
        /// <param name="Rows"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MessageResult> VarifyBatchCreateFocSubBudgetAsync(IEnumerable<CreatesFocSubBudgetDto> Rows)
        {
            var budgetCodes = Rows.Select(a => a.MasterBudgetCode);
            //主预算
            var queryMasterRepository = LazyServiceProvider.LazyGetService<IFocMasterBudgetRepository>();
            var queryMasterBudget = (await queryMasterRepository.GetQueryableAsync()).AsNoTracking();
            var mtBudget = queryMasterBudget.Where(m => budgetCodes.Contains(m.Code) && m.Status);
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            ////成本中心
            var costCenters = await dataverseService.GetCostcentersAsync();
            ////大区
            var district = await dataverseService.GetDistrict();
            //大区BU关系
            var districtBu = await dataverseService.GetOrgDistrictRelationsAsync();
            var districtBuDict = districtBu.ToDictionary(a => $"{a.OrgId}*{a.DistrictId}", a => a.Id);
            //成本中心BU关系
            var costCentersBu = await dataverseService.GetCostCenterOrgRelationsAsync();
            var costCentersBuDict = costCentersBu.ToDictionary(a => $"{a.OrgId}*{a.CostcenterId}", a => a.Id);
            var subBudgetDic = await mtBudget.ToDictionaryAsync(m => m.Code);
            var productQuery = await LazyServiceProvider.LazyGetService<IProductDetailsRepository>().GetQueryableAsync();
            //查找主预算金额与主预算金额下的所有子预算金额之和
            var mastrBudget = await mtBudget.Include(m => m.SubBudgets).Select(m => new CreateFocBudgetCompareDto
            {
                Id = m.Id,
                Code = m.Code,
                MasterQty = m.BudgetQty,
                BuId = m.BuId,
                SubQtySum = m.SubBudgets.Sum(s => s.BudgetQty)
            }).Distinct().ToDictionaryAsync(d => d.Code);
            List<CreateSubBudgetImportMessageDto> success = [];
            List<CreateSubBudgetImportMessageDto> error = [];
            string[] status = ["是", "否"];
            var rowNo = 9;
            foreach (var row in Rows)
            {
                rowNo++;
                string message = string.Empty;
                if (string.IsNullOrEmpty(row.MasterBudgetCode)) { message += "请填写主预算编号;"; }
                if (string.IsNullOrEmpty(row.CostCenter)) { message += "请填写成本中心;"; }
                if (string.IsNullOrEmpty(row.Region)) { message += "请填写大区;"; }
                //if (string.IsNullOrEmpty(row.StatusText)) { message += "请选择是否启用;"; }
                if (string.IsNullOrEmpty(row.ProductMCode)) { message += "请填写品牌信息编码;"; }
                if (string.IsNullOrEmpty(row.OwnerEmail)) { message += "请填写预算负责人邮箱;"; }
                if (string.IsNullOrEmpty(row.Description)) { message += "请填写描述;"; }

                var costCenterData = costCenters.FirstOrDefault(f => f.CcenterCode == row.CostCenter);
                if (costCenterData == null) { message += "成本中心不存在;"; }
                var regionData = district.FirstOrDefault(f => f.DistrictCode == row.Region);
                if (regionData == null) { message += "大区不存在;"; }

                var product = await productQuery.FirstOrDefaultAsync(a => a.ProductMCode == row.ProductMCode && a.IsActive && a.UseFlagM);
                if (product == null) { message += "产品品牌信息编码不存在;"; }
                var user = queryableUser.FirstOrDefault(u => u.Email == row.OwnerEmail);
                if (user == null) { message += "预算负责人不存在;"; }
                if (row.Remark.Length > 300) message += "备注不能超过300字;";
                if (row.Description.Length > 200) message += "描述不能超过200字;";
                //if (!status.Contains(row.StatusText)) message += "是否启用请选择是或否;";

                var (amountMes, mes) = row.VerificationCreate();
                if (!mastrBudget.TryGetValue(row.MasterBudgetCode, out var msBudget)) { message += "主预算不存在或者已被禁用;"; }
                else if (!districtBuDict.ContainsKey($"{msBudget.BuId}*{regionData?.Id}")) message += "大区非该BU;";
                else if (!costCentersBuDict.ContainsKey($"{msBudget.BuId}*{costCenterData?.Id}")) message += "成本中心非该BU;";
                else if (amountMes) message += mes;
                else
                {
                    //判断主预算是否大于子预算
                    msBudget.SubQtySum += row.TotalQty;
                    if (!msBudget.IsAdjusted) message += "子预算数量大于主预算数量;";
                }
                var messageDto = ObjectMapper.Map<CreatesFocSubBudgetDto, CreateSubBudgetImportMessageDto>(row);
                messageDto.No = rowNo;
                if (string.IsNullOrEmpty(message))
                {
                    messageDto.CostCenterId = costCenterData.Id;
                    messageDto.CostCenter = costCenterData.Name;
                    messageDto.Region = regionData.Name;
                    messageDto.RegionId = regionData.Id;
                    messageDto.OwnerId = user.Id;
                    messageDto.OwnerName = user.Name;
                    messageDto.ProductId = product.Id;
                    messageDto.ProductName = product.ProductShortNameEN;
                    messageDto.ProductMCode = product.ProductMCode;
                    messageDto.ProductUnit = product.Unit;
                    //messageDto.Status = (row.StatusText == "是");
                    //messageDto.No = success.Count + 1;
                    messageDto.MasterBudgetId = msBudget.Id;
                    success.Add(messageDto);
                }
                else
                {
                    if (user != null)
                        messageDto.OwnerName = user.Name;
                    if (product != null)
                        messageDto.ProductName = product.ProductShortNameEN;
                    messageDto.Message = $"{message}";
                    //messageDto.No = error.Count + 1;
                    error.Add(messageDto);
                };
            }
            if (error.Count > 0)
                return MessageResult.SuccessResult(new ImportDataResponseDto<CreateSubBudgetImportMessageDto>(error, false));
            return MessageResult.SuccessResult(new ImportDataResponseDto<CreateSubBudgetImportMessageDto>(success, true));
        }

        /// <summary>
        /// 批量调整子预算
        /// </summary>
        /// <param name="Rows"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MessageResult> VarifyImportAdjustmentFocSubBudgetAsync(IEnumerable<AdjustmentFocBudgetDto> Rows)
        {
            var budgetCodes = Rows.Select(a => a.Code);
            var querySubBudgetRepository = LazyServiceProvider.LazyGetService<IFocSubBudgetRepository>();
            var querySubBudget = (await querySubBudgetRepository.GetQueryableAsync()).AsNoTracking();
            var subBudgets = querySubBudget.Include(s => s.MonthlyBudgets).Where(m => budgetCodes.Contains(m.Code));
            var subBudgetDic = subBudgets.ToDictionary(m => m.Code);
            var masterids = subBudgets.Select(s => s.MasterBudgetId).ToList();
            //主预算
            var queryMasterBudgetRepository = LazyServiceProvider.LazyGetService<IFocMasterBudgetRepository>();
            var queryMasterSubBudget = (await queryMasterBudgetRepository.GetQueryableAsync()).AsNoTracking();
            //查找主预算数量与主预算数量下的所有子预算数量之和
            var mastrBudget = await queryMasterSubBudget.Include(x => x.SubBudgets).Where(m => masterids.Contains(m.Id)).Select(m => new AdjustmentFocBudgetCompareDto
            {
                Id = m.Id,
                MasterQty = m.BudgetQty,
                SubQtySum = m.SubBudgets.Sum(s => s.BudgetQty)
            }).Distinct().ToDictionaryAsync(d => d.Id);
            int i = 1;
            //是否验证成功
            var IsVarify = true;
            List<AdjustmentFocBudgetDto> adjustments = new();
            var rowNo = 9;
            foreach (var row in Rows)
            {
                rowNo++;
                //row.No = i;
                row.No = rowNo;
                var message = string.Empty;
                if (string.IsNullOrWhiteSpace(row.Code)) message += $"子预算编号不能为空;";
                var (isSuccess, mes) = row.VerificationAdjustment();
                if (isSuccess) message += mes;
                else if (subBudgetDic.TryGetValue(row.Code, out var subBudget))
                {
                    var modifyMonths = row.MonthlyBudgets.Where(m => m.Status.HasValue || (m.BudgetQty.HasValue && m.BudgetQty != 0));
                    if (modifyMonths.Count() == 0) { message += "请配置数量或者是否开启；"; }
                    var totalAdjustment = row.MonthlyBudgets.Sum(s => s.BudgetQty);
                    foreach (var m in modifyMonths)
                    {
                        var monthly = subBudget.MonthlyBudgets.First(s => s.Month == m.Month);
                        if (m.Status.HasValue)
                        {
                            monthly.Status = m.Status.Value;
                        }
                        if (m.BudgetQty.HasValue)
                        {
                            monthly.BudgetQty += m.BudgetQty.Value;
                            if (monthly.BudgetQty < 0) message += $"调整后{(int)m.Month}月预算小于0;";
                        }
                    }
                    var availableQty = subBudget.GetAvailableQty();
                    if (availableQty < 0) message += $"调整后可用数量小于0;";
                    var master = mastrBudget[subBudget.MasterBudgetId];
                    master.SubQtySum += totalAdjustment.Value;
                    if (!master.IsAdjusted)
                        message += $"调整后可用数量大于主预算;";

                }
                else message += "子预算编号不存在";

                row.Message = message;
                if (!string.IsNullOrWhiteSpace(message))
                {
                    //row.No = adjustments.Count + 1;
                    adjustments.Add(row);
                    IsVarify = false;
                }
                i++;
            }

            return MessageResult.SuccessResult(new ImportDataResponseDto<AdjustmentFocBudgetDto>(IsVarify ? [.. Rows] : adjustments, IsVarify));
        }

        /// <summary>
        /// 判断子预算是否足够
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> CheckFocSubbudgetQtySufficientAsync(UseFocBudgetRequestDto request)
        {
            var useSubbudgetRespository = LazyServiceProvider.LazyGetService<IFocBudgetUseRepository>();
            var queryUseSubbudget = await LazyServiceProvider.LazyGetService<IFocBudgetUseRepository>().GetQueryableAsync();
            var querySubbudget = await LazyServiceProvider.LazyGetService<IFocSubBudgetRepository>().GetQueryableAsync();

            //check
            var totalUseQty = request.Items.Select(x => x.UseQty).Sum();
            var subbudgetData = querySubbudget.Include(s => s.MonthlyBudgets.Where(m => m.Status)).First(x => x.Id == request.SubbudgetId);

            var availableQty = subbudgetData.GetAvailableQty();
            var useSubbudgetDatas = queryUseSubbudget.Where(x => x.FocApplicationId == request.FocApplicationId && x.SubbudgetId == request.SubbudgetId).ToArray();

            if (useSubbudgetDatas.Count() > 0)
                availableQty += useSubbudgetDatas.Select(x => x.Qty).Sum();

            if (availableQty < totalUseQty)
                return MessageResult.FailureResult($"预算可用数量不足，目前剩余可用数量：{availableQty}");

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 使用子预算
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> UseFocSubbudgetAsync(UseFocBudgetRequestDto request, bool autoSave = false)
        {
            var result = await CheckFocSubbudgetQtySufficientAsync(request);
            if (!result.Success)
                return result;

            var useSubbudgetRespository = LazyServiceProvider.LazyGetService<IFocBudgetUseRepository>();
            var subbudgetRespository = LazyServiceProvider.LazyGetService<IFocSubBudgetRepository>();
            var querySubbudget = await LazyServiceProvider.LazyGetService<IFocSubBudgetRepository>().GetQueryableAsync();

            //check
            var totalUseQty = request.Items.Select(x => x.UseQty).Sum();
            var subbudgetData = querySubbudget.First(x => x.Id == request.SubbudgetId);

            var datas = new List<FocBudgetUse>();

            foreach (var item in request.Items)
            {
                datas.Add(new FocBudgetUse
                {
                    FocApplicationId = request.FocApplicationId,
                    RowNo = item.RowNo,
                    SubbudgetId = request.SubbudgetId,
                    Qty = item.UseQty,
                    OperateTime = DateTime.Now,
                    IsEnable = true
                });
            }

            //获取历史数据
            var usedHistories = await useSubbudgetRespository.GetListAsync(x => x.FocApplicationId == request.FocApplicationId && x.SubbudgetId == request.SubbudgetId);
            //将之前的使用数量返还
            var historyQty = usedHistories.Sum(a => a.Qty);
            totalUseQty -= historyQty;

            //预算使用之后 对应子预算的已用数量、可用数量 相应的更新
            subbudgetData.UesdQty += totalUseQty;

            //subbudgetData.AvailableAmount -= totalUseAmount;

            //删除之前提交的使用明细（一个PR单、一个子预算只保留最近一次的使用明细）
            await useSubbudgetRespository.DeleteManyAsync(usedHistories, autoSave);
            await useSubbudgetRespository.InsertManyAsync(datas, autoSave);
            await subbudgetRespository.UpdateAsync(subbudgetData, autoSave);

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 获取FOC子预算使用明细
        /// </summary>
        /// <param name="subbudgetId">The subbudget identifier.</param>
        /// <returns></returns>
        public async Task<IEnumerable<GetFocSubbudgetUseInfoResponseDto>> GetFocSubbudgetUseInfo(Guid subbudgetId)
        {
            var queryUseSubbudget = await LazyServiceProvider.LazyGetService<IFocBudgetUseRepository>().GetQueryableAsync();

            var query = queryUseSubbudget.Include(x => x.FocApplication)
                .Where(x => x.SubbudgetId == subbudgetId && x.IsEnable);

            var gpSubbudget = query.ToArray().GroupBy(x => new { x.FocApplicationId, x.FocApplication, x.Subbudget })
                .Select(g => new { g.Key.FocApplicationId, g.Key.FocApplication, g.Key.Subbudget, totalQty = g.Sum(x => x.Qty) });

            var responses = new List<GetFocSubbudgetUseInfoResponseDto>();

            gpSubbudget.ToList().ForEach(x =>
            {
                responses.Add(new GetFocSubbudgetUseInfoResponseDto
                {
                    ApplyUserName = x.FocApplication.ApplyUser,
                    ApplyTime = x.FocApplication.ApplyTime?.ToString("yyyy-MM-dd"),
                    WorkflowTypeName = "FOC申请",
                    FlowStatus = x.FocApplication.Status,
                    FlowNo = x.FocApplication.ApplicationCode,
                    UseQty = x.totalQty
                });
            });
            responses = responses.OrderByDescending(x => x.ApplyTime).ToList();
            return responses;
        }

        /// <summary>
        /// 退回FOC子预算
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> ReturnFocSubbudgetAsync(List<ReturnFocBudgetRequestDto> requests)
        {
            if (requests.Count == 0) return MessageResult.SuccessResult();
            var returnSubbudgetRespository = LazyServiceProvider.LazyGetService<IFocBudgetReturnRepository>();
            var queryReturnSubbudget = await LazyServiceProvider.LazyGetService<IFocBudgetReturnRepository>().GetQueryableAsync();
            var useSubbudgetRespository = LazyServiceProvider.LazyGetService<IFocBudgetUseRepository>();
            var queryUseSubbudget = await LazyServiceProvider.LazyGetService<IFocBudgetUseRepository>().GetQueryableAsync();
            var subbudgetRespository = LazyServiceProvider.LazyGetService<IFocSubBudgetRepository>();
            var querySubbudget = await LazyServiceProvider.LazyGetService<IFocSubBudgetRepository>().GetQueryableAsync();

            //check
            //var sourceId = request.Items.Select(x => x.ReturnSourceId).Distinct();
            //if (sourceId.Count() > 1)
            //    return MessageResult.FailureResult("同一个单据不能退回多次");

            //var count = await queryReturnSubbudget.CountAsync(x => x.PrId == request.PrId && x.SubbudgetId == request.SubbudgetId && sourceId.Contains(x.ReturnSourceId));
            //if (count > 0)
            //    return MessageResult.FailureResult("同一个单据不能退回多次");

            //TODO check:退回明细中 PdRowNo不应该重复，即针对同一个PR的退回，相同的行号应该合计后再请求


            //退回的数量 反馈到使用明细里面对应的行上 应该增加数量
            var FocIds = requests.Select(s => s.FocId).Distinct().ToList();

            var rowNos = requests.SelectMany(s => s.Items.Select(a => a.PdRowNo)).Distinct().ToList();
            var subbudgetIds = requests.Select(s => s.SubbudgetId).Distinct().ToList();
            var useSubbudgetDatas = queryUseSubbudget.Where(x => FocIds.Contains(x.FocApplicationId) && subbudgetIds.Contains(x.SubbudgetId) && rowNos.Contains(x.RowNo)).ToArray();

            var subbudgetDatas = await querySubbudget.Where(m => subbudgetIds.Contains(m.Id)).ToListAsync();
            var retDatasCreated = new List<FocBudgetReturn>();
            var useDatasUpdated = new HashSet<FocBudgetUse>();

            var operateTime = DateTime.Now;

            foreach (var request in requests)
            {
                request.Items.ToList().ForEach(x =>
                {
                    //记录退回明细
                    retDatasCreated.Add(new FocBudgetReturn
                    {
                        FocApplicationId = request.FocId,
                        RowNo = x.PdRowNo,
                        SubbudgetId = request.SubbudgetId,
                        Qty = x.ReturnQty,
                        OperateTime = operateTime,
                        IsEnable = true,
                        ReturnSourceId = x.ReturnSourceId,
                        ReturnSourceCode = x.ReturnSourceCode
                    });

                    var useSubbudgetInfo = useSubbudgetDatas.FirstOrDefault(u => u.RowNo == x.PdRowNo && u.FocApplicationId == request.FocId);
                    if (useSubbudgetInfo != null)
                    {
                        useSubbudgetInfo.Qty -= x.ReturnQty;
                        useDatasUpdated.Add(useSubbudgetInfo);
                    }
                });

                var totalReturnAmount = request.Items.Select(x => x.ReturnQty).Sum();
                var subbudgetData = subbudgetDatas.First(x => x.Id == request.SubbudgetId);
                subbudgetData.UesdQty -= totalReturnAmount;
            }

            //subbudgetData.AvailableAmount += totalReturnAmount;


            await returnSubbudgetRespository.InsertManyAsync(retDatasCreated);

            if (useDatasUpdated.Count > 0)
                await useSubbudgetRespository.UpdateManyAsync(useDatasUpdated);

            await subbudgetRespository.UpdateManyAsync(subbudgetDatas);

            return MessageResult.SuccessResult(operateTime);
        }

        /// <summary>
        /// 新增历史记录
        /// </summary>
        /// <param name="bdHistorys"></param>
        /// <returns></returns>
        private async Task SaveHistory(List<FocBudgetHistory> bdHistorys)
        {
            var historyQueryRepository = LazyServiceProvider.LazyGetService<IFocBudgetHistoryRepository>();
            await historyQueryRepository.InsertManyAsync(bdHistorys);
        }

        async Task<IEnumerable<UploadFileResponseDto>> GetAttachmentInfo(string attachmentIds)
        {
            if (string.IsNullOrEmpty(attachmentIds))
                return null;
            var idArray = attachmentIds.Split(',').Select(x => Guid.Parse(x)).ToList();
            if (idArray == null || idArray.Count < 1)
                return null;
            var attachmentQuery = await LazyServiceProvider.LazyGetService<IAttachmentRepository>().GetQueryableAsync();
            var attachmentEntities = attachmentQuery.Where(x => idArray.Contains(x.Id)).ToList();
            var attachmentInfo = ObjectMapper.Map<List<Attachment>, List<UploadFileResponseDto>>(attachmentEntities);
            return attachmentInfo;

        }

        /// <summary>
        /// 生成自动编号
        /// </summary>
        /// <returns></returns>
        private async Task<Guid> GenerateSubCode(IFocSubBudgetRepository subbudgetRespository, FocSubBudget dbSubBudget, int year)
        {
            await using (var handle = await LazyServiceProvider.LazyGetService<IAbpDistributedLock>().TryAcquireAsync($"{DistributedLock.DistributedLock_GenerateSerialFocSubBudgetNo}", TimeSpan.FromSeconds(5)))
            {
                if (handle != null)
                {
                    int count = 0;
                    var strYear = year.ToString();
                    var yearNo = $"FCC{strYear.Substring(strYear.Length - 2)}";
                    using (DataFilter.Disable<ISoftDelete>())
                    {
                        var queryBudget = await subbudgetRespository.GetQueryableAsync();
                        var budgetNo = queryBudget.Where(a => a.Code.Contains(yearNo))
                            //.Select(a => int.Parse(a.Code.Substring(a.Code.IndexOf('-') + 1)))
                            .OrderByDescending(a => a.Code)
                            .FirstOrDefault();
                        if (budgetNo != null)
                        {
                            count = int.Parse(budgetNo.Code.Substring(budgetNo.Code.IndexOf('-') + 1));
                        }
                    }
                    //todo:与BPM并行期间，采购申请单号规则调整5000开始（后续可能需要还原）
                    var countNo = count < 5000 ? (++count + 5000) : (++count);
                    var no = $"{yearNo}-{countNo:D4}";
                    dbSubBudget.Code = no;
                    dbSubBudget.SetId(Guid.NewGuid());
                    var sBudget = await subbudgetRespository.InsertAsync(dbSubBudget, true);
                    return sBudget.Id;
                }
            }
            throw new TimeoutException("生成子预算编号超时，请重新操作");
        }

        /// <summary>
        /// 生成自动编号 批量
        /// </summary>
        /// <returns></returns>
        private async Task BatchSGenerateubCode(IFocSubBudgetRepository subbudgetRespository, List<FocSubBudget> dbSubBudgets)
        {
            await using (var handle = await LazyServiceProvider.LazyGetService<IAbpDistributedLock>().TryAcquireAsync($"{DistributedLock.DistributedLock_GenerateSerialFocSubBudgetNo}", TimeSpan.FromSeconds(5)))
            {
                if (handle != null)
                {
                    var queryMasterBudget = await LazyServiceProvider.LazyGetService<IFocMasterBudgetRepository>().GetQueryableAsync();
                    var masterBudgetIds = dbSubBudgets.Select(x => x.MasterBudgetId).Distinct().ToList();
                    var masterBudgets = queryMasterBudget.Where(a => masterBudgetIds.Contains(a.Id)).ToList();
                    foreach (var item in dbSubBudgets)
                    {
                        int count = 0;
                        var year = masterBudgets.FirstOrDefault(a => a.Id == item.MasterBudgetId)?.Year;
                        var strYear = (year.HasValue ? (year.Value < 1000 ? DateTimeOffset.Now.Year : year.Value) : DateTimeOffset.Now.Year).ToString();
                        var yearNo = $"FCC{strYear.Substring(strYear.Length - 2)}";

                        using (DataFilter.Disable<ISoftDelete>())
                        {
                            var queryBudget = await subbudgetRespository.GetQueryableAsync();
                            var budgetNo = queryBudget.Where(a => a.Code.Contains(yearNo))
                                .OrderByDescending(a => a.Code)
                                .FirstOrDefault();
                            if (budgetNo != null)
                            {
                                count = int.Parse(budgetNo.Code.Substring(budgetNo.Code.IndexOf('-') + 1));
                            }
                        }
                        //todo:与BPM并行期间，采购申请单号规则调整5000开始（后续可能需要还原）
                        var countNo = count < 5000 ? (++count + 5000) : (++count);
                        var no = $"{yearNo}-{countNo:D4}";
                        item.Code = no;
                        await subbudgetRespository.InsertAsync(item, true);
                    }
                    return;
                }
            }
            throw new TimeoutException("生成子预算编号超时，请重新操作");
        }
        public async Task<List<Guid>> GetSubbudgetsByOwner(Guid ownerId)
        {
            var querySubBudgetRepository = LazyServiceProvider.LazyGetService<IFocSubBudgetRepository>();
            var querySubBudget = await querySubBudgetRepository.GetQueryableAsync();

            var data = await querySubBudget.Where(x => x.OwnerId == ownerId).Select(x => x.Id).Distinct().ToListAsync();

            return data;
        }
    }
}
