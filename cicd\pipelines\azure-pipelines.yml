# trigger:
#   branches:
#     include:
#       - develop
#   paths:
#     include:
#       - src/*
#       - cicd/pipelines/common/*
#       - cicd/aks/microservices/speaker-portal-service.yml
#       - cicd/docker-compose/docker-compose-speaker-portal-service.yml
# pool:
#   name: 'Default'
# #  demands: Agent.Name -equals ua00683p

# parameters: 
#   - name: "environmentOverride"
#     displayName: Override Environment based on branch
#     type: string
#     default: none
#     values:
#     - none
#     - dev
#     - stage
#     - master

# extends:
#   template: cicd/pipelines/common/build-push-deploy.yml
#   parameters:
#     dockerComposeFile: 'cicd/docker-compose/docker-compose-speaker-portal-service.yml'
#     configurationYML: $(System.DefaultWorkingDirectory)/cicd/aks/microservices/speaker-portal-service.yml
#     environmentOverride: ${{ parameters.environmentOverride }}
