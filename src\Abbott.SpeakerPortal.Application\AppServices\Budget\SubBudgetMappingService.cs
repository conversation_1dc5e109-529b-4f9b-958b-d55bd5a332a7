﻿using Abbott.SpeakerPortal.Contracts.Approval;
using Abbott.SpeakerPortal.Contracts.Budget.Subbudget;
using Abbott.SpeakerPortal.Contracts.Vendor.NonSpeaker;
using Abbott.SpeakerPortal.Entities.Approval;
using Abbott.SpeakerPortal.Entities.Budget;
using Abbott.SpeakerPortal.Entities.VendorApplicationOrgnizations;
using AutoMapper.Internal.Mappers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.ObjectMapping;

namespace Abbott.SpeakerPortal.AppServices.Budget
{
    public class SubBudgetMappingService : SpeakerPortalAppService, ISubBudgetMappingService
    {
        public async Task InsertManyRecord(IEnumerable<CreateSubbudgetRequestDto> requests)
        {
            var repository = LazyServiceProvider.LazyGetService<IBdSubBudgetMappingRepository>();
            var datas = ObjectMapper.Map<IEnumerable<CreateSubbudgetRequestDto>, IEnumerable<BdSubBudgetMapping>>(requests);
            await repository.InsertManyAsync(datas);
        }
    }
}
