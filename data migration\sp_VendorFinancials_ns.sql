CREATE PROCEDURE dbo.sp_VendorFinancials_ns
AS 
BEGIN
	
select 
Id,
VendorId,
Company,
Currency,
VendorCode,
AbbottBank,
VendorType,
Division,
PayType,
CountryCode,
BankType,
DpoCategory,
PaymentTerm,
BankNo,
ExtraProperties,
ConcurrencyStamp,
CONVERT(DATETIME, LEFT(a.CreationTime, 8) + ' ' + SUBSTRING(a.CreationTime, 9, 2) + ':' + SUBSTRING(a.CreationTime, 11, 2) + ':' + SUBSTRING(a.CreationTime, 13, 2), 120) CreationTime,
g.spk_NexBPMCode as CreatorId,
case when len(a.LastModificationTime)=14 then CONVERT(DATETIME, LEFT(a.LastModificationTime, 8) + ' ' + SUBSTRING(a.LastModificationTime, 9, 2) + ':' + SUBSTRING(a.LastModificationTime, 11, 2) + ':' + SUBSTRING(a.LastModificationTime, 13, 2), 120) 
else a.LastModificationTime end as LastModificationTime,
e1.spk_NexBPMCode as LastModifierId,
IsDeleted,
DeleterId,
DeletionTime,
SpendingCategory,
BpcsCreationTime,
BpcsVmid,
BpcsVnstat  
into #VendorFinancials
from PLATFORM_ABBOTT.dbo.VendorFinancials_tmp a 
left join PLATFORM_ABBOTT.dbo.spk_staffmasterdata g
on a.CreatorId = g.bpm_id
left join PLATFORM_ABBOTT.dbo.spk_staffmasterdata e1
on a.LastModifierId = e1.bpm_id;


--删除表
 IF OBJECT_ID(N'PLATFORM_ABBOTT.dbo.VendorFinancials ', N'U') IS NOT NULL
 BEGIN
		drop table PLATFORM_ABBOTT.dbo.VendorFinancials
		select *
        into PLATFORM_ABBOTT.dbo.VendorFinancials from #VendorFinancials
 PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
 END
 ELSE
 BEGIN
--落成实体表
 select *
    into PLATFORM_ABBOTT.dbo.VendorFinancials from #VendorFinancials
 -- select * from #vendor_tbl
 PRINT(N'落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
 END
END;