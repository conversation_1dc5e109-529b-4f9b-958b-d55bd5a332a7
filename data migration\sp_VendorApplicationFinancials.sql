CREATE PROCEDURE dbo.sp_VendorApplicationFinancials
AS 
BEGIN
	--供应商申请单-财务板块信息
select * into #VendorApplicationFinancials_Tmp from (
select 
newid() AS Id,--自动生成的uuid
vt.id AS ApplicationId,--此处填入对应VendorApplications的Id，用于标记该行为供应商申请单对应的财务信息
tsi.company_Value AS Company,--
tsi.currency_Text AS Currency,--
tsi.vendorNumber AS VendorCode,--
tsi.bankCode AS AbbottBank,--
tsi.vendorType AS VendorType,--
tsi.divisionCode AS Division,--
tsi.paymentType_Text AS PayType,--
tsi.ctryCode_Text AS CountryCode,--
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/BankType_Value)[1]', 'nvarchar(255)')  AS BankType,--
DPOCategory AS DpoCategory,--根据填入的值与DPO Category字典匹配后填入字典编码
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/TermCode_Value)[1]', 'nvarchar(255)')  AS PaymentTerm,--
'' AS BankNo,--
'{}' AS ExtraProperties,--默认填写为"{}"
'' AS ConcurrencyStamp,--?
vt.CreationTime AS CreationTime,--与对应的VendorApplications记录保持一致即可
vt.CreatorId AS CreatorId,--与对应的VendorApplications记录保持一致即可
'' AS LastModificationTime,--默认为空
'' AS LastModifierId,--默认为空
'0'AS IsDeleted,--默认为0
'' AS DeleterId,--默认为空
'' AS DeletionTime,--默认为空
UPPER(SUBSTRING(SpendingCategory,1,5)) AS SpendingCategory--此处填写的值就是字典编码，不需要额外匹配(但如果识别到了不存在于字典的值可能需要加入字典或前端展示时直接展示为code)
from PLATFORM_ABBOTT_Stg.dbo.ODS_AUTO_BIZ_T_SupplierApplication_Info tsi
join  PLATFORM_ABBOTT_Stg.dbo.ODS_T_FORMINSTANCE_GLOBAL a
on tsi.ProcInstId =a.ProcInstId
join PLATFORM_ABBOTT_Stg.dbo.VendorApplications_Tmp vt
on tsi.ProcInstId =vt.ProcInstId
)A
--写入目标表
IF OBJECT_ID(N'PLATFORM_ABBOTT_Stg.dbo.VendorApplicationFinancials_Tmp', N'U') IS NOT NULL
BEGIN
	PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
		UPDATE a
		SET 
		a.ApplicationId = b.ApplicationId
		,a.Company = b.Company
		,a.Currency = b.Currency
		,a.AbbottBank = b.AbbottBank
		,a.VendorType = b.VendorType
		,a.Division = b.Division
		,a.PayType = b.PayType
		,a.CountryCode = b.CountryCode
		,a.BankType = b.BankType
		,a.DpoCategory = b.DpoCategory
		,a.PaymentTerm = b.PaymentTerm
		,a.BankNo = b.BankNo
		,a.ExtraProperties = b.ExtraProperties
		,a.ConcurrencyStamp = b.ConcurrencyStamp
		,a.CreatorId = b.CreatorId
		,a.LastModificationTime = b.LastModificationTime
		,a.LastModifierId = b.LastModifierId
		,a.IsDeleted = b.IsDeleted
		,a.DeleterId = b.DeleterId
		,a.DeletionTime = b.DeletionTime
		,a.SpendingCategory = b.SpendingCategory
		FROM PLATFORM_ABBOTT_Stg.dbo.VendorApplicationFinancials_Tmp a
		LEFT JOIN #VendorApplicationFinancials_Tmp b
		ON a.VendorCode = b.VendorCode AND a.CreationTime = b.CreationTime
		
		INSERT INTO PLATFORM_ABBOTT_Stg.dbo.VendorApplicationFinancials_Tmp
		SELECT
		 a.Id
		,a.ApplicationId
		,a.Company
		,a.Currency
		,a.VendorCode
		,a.AbbottBank
		,a.VendorType
		,a.Division
		,a.PayType
		,a.CountryCode
		,a.BankType
		,a.DpoCategory
		,a.PaymentTerm
		,a.BankNo
		,a.ExtraProperties
		,a.ConcurrencyStamp
		,a.CreationTime
		,a.CreatorId
		,a.LastModificationTime
		,a.LastModifierId
		,a.IsDeleted
		,a.DeleterId
		,a.DeletionTime
		,a.SpendingCategory
		FROM #VendorApplicationFinancials_Tmp a
		WHERE NOT EXISTS (SELECT * FROM PLATFORM_ABBOTT_Stg.dbo.VendorApplicationFinancials_Tmp WHERE VendorCode=a.VendorCode AND CreationTime=a.CreationTime)
END
ELSE
BEGIN
	select * into PLATFORM_ABBOTT_Stg.dbo.VendorApplicationFinancials_Tmp from #VendorApplicationFinancials_Tmp
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END

END

