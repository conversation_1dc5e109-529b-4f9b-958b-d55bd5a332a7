CREATE PROCEDURE dbo.sp_PurPRApplicationCostItems_ns
AS 
BEGIN			
	/*
	1.ItemCategory，SupportItem字段不明确
 */
select 
a.Id,
ppt.id as PRApplicationId,
a.SupportItem,
a.EstimateCost,
a.ExtraProperties,
a.ConcurrencyStamp,
UPPER(ss.spk_NexBPMCode)  as CreatorId,
ppt.CreatorId as CreationTime,
a.LastModificationTime,
a.LastModifierId,
a.Is<PERSON>eleted,
a.DeleterId,
a.DeletionTime,
a.ItemCategory
into #PurPRApplicationCostItems
from PLATFORM_ABBOTT_Dev.dbo.PurPRApplicationCostItems_tmp a
left join PLATFORM_ABBOTT_Dev.dbo.PurPRApplications_tmp ppt 
on a.ProcInstId =ppt.ProcInstId 
left join PLATFORM_ABBOTT_Dev.dbo.spk_staffmasterdata ss 
on ppt.CreationTime =ss.bpm_id 

IF OBJECT_ID(N'PLATFORM_ABBOTT_Dev.dbo.PurPRApplicationCostItems', N'U') IS NOT NULL
BEGIN
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
    drop table PLATFORM_ABBOTT_Dev.dbo.PurPRApplicationCostItems
    select *
    into PLATFORM_ABBOTT_Dev.dbo.PurPRApplicationCostItems from #PurPRApplicationCostItems
	END
	ELSE
	BEGIN
    --落成实体表
    select *
    into PLATFORM_ABBOTT_Dev.dbo.PurPRApplicationCostItems from #PurPRApplicationCostItems
	  PRINT(N' 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
END
