CREATE PROCEDURE dbo.sp_VendorPersonals_ns
AS 
begin
select 
a.Id,
a.<PERSON>,
a.<PERSON>,
a.CardType,
a.<PERSON><PERSON>,
b.Id as CardPic,
spk_provincialadministrativecode as Province,
spk_cityadministrativedivisioncode as City,
a.Address,
a.PostCode,
a.ExtraProperties,
a.ConcurrencyStamp,
a.CreationTime,
g.spk_NexBPMCode as CreatorId,
a.LastModificationTime,
a.LastModifierId,
a.<PERSON>,
a.DeleterId,
a.DeletionTime,
a.SPName,
a.AffiliationOrgan,
a.Email 
into #VendorPersonals
from PLATFORM_ABBOTT_Dev.dbo.VendorPersonals_tmp a 
left join PLATFORM_ABBOTT_Dev.dbo.Attachments_tmp b
on a.CardPic COLLATE SQL_Latin1_General_CP1_CI_AS = b.BPMId COLLATE SQL_Latin1_General_CP1_CI_AS
left join (select SUBSTRING(spk_name,1,2) name ,* from PLATFORM_ABBOTT_Dev.dbo.spk_province ) d
on SUBSTRING(a.Province,1,2) COLLATE SQL_Latin1_General_CP1_CI_AS  = d.name
left join (select SUBSTRING(spk_name,1,2) name ,* from PLATFORM_ABBOTT_Dev.dbo.spk_city) E
on e.name=SUBSTRING(a.City,1,2) COLLATE SQL_Latin1_General_CP1_CI_AS
left join PLATFORM_ABBOTT_Dev.dbo.spk_staffmasterdata g
on a.CreatorId COLLATE SQL_Latin1_General_CP1_CI_AS = g.bpm_id
--删除表
 IF OBJECT_ID(N'PLATFORM_ABBOTT_Dev.dbo.VendorPersonals ', N'U') IS NOT NULL
 BEGIN
		drop table PLATFORM_ABBOTT_Dev.dbo.VendorPersonals
		select *
        into PLATFORM_ABBOTT_Dev.dbo.VendorPersonals from #VendorPersonals
 PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
 END
 ELSE
 BEGIN
--落成实体表
 select *
    into PLATFORM_ABBOTT_Dev.dbo.VendorPersonals from #VendorPersonals
 -- select * from #vendor_tbl
 PRINT(N'落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
 END


end
