﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace Abbott.SpeakerPortal.Contracts.Budget.Subbudget
{
    public class UseBudgetRequestDto
    {
        /// <summary>
        /// 采购申请单Id
        /// </summary>
        [Required]
        public Guid PrId { get; set; }
        /// <summary>
        /// 子预算Id
        /// </summary>
        [Required]
        public Guid SubbudgetId { get; set; }
        /// <summary>
        /// 使用详情列表
        /// </summary>
        [Required]
        public IEnumerable<UseInfo> Items { get; set; }
    }

    public class UseInfo
    {
        /// <summary>
        /// 采购明细的行号
        /// </summary>
        [Required]
        public int PdRowNo { get; set; }
        /// <summary>
        /// 使用金额
        /// </summary>
        [Required]
        public decimal UseAmount { get; set; }
    }
}
