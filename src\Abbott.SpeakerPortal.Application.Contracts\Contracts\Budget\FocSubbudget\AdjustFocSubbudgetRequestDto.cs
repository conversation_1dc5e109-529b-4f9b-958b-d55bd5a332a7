﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Abbott.SpeakerPortal.Contracts.Common.Attachment;

namespace Abbott.SpeakerPortal.Contracts.Budget.FocSubbudget
{
    public class AdjustFocSubbudgetRequestDto
    {
        /// <summary>
        /// 子预算Id
        /// </summary>
        [Required]
        public Guid Id { get; set; }
        /// <summary>
        /// 成本中心
        /// </summary>
        [Required]
        public Guid CostCenterId { get; set; }
        /// <summary>
        /// 大区
        /// </summary>
        [Required]
        public Guid RegionId { get; set; }
        /// <summary>
        /// 负责人Id
        /// </summary>
        [Required]
        public Guid OwnerId { get; set; }
        /// <summary>
        /// 负责人姓名
        /// </summary>
        [Required]
        public string OwnerName { get; set; }
        /// <summary>
        /// 描述
        /// </summary>
        [Required]
        public string Description { get; set; }

        /// <summary>
        /// 是否合规审计审批
        /// </summary>
        public bool? IsComplicanceAudits { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 附件
        /// </summary>
        public List<UploadFileResponseDto> AttachmentInformation { get; set; }
        /// <summary>
        /// 调整月份数量
        /// </summary>
        public List<AdjustFocMonthQtyRequestDto> AdjustFocMonthQtys { get; set; }
    }
}
