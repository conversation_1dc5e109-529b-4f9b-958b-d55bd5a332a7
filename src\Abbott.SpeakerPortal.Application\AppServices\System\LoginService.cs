﻿using System;
using System.Data;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

using Abbott.SpeakerPortal.IAppServices;

using Microsoft.Extensions.DependencyInjection;

using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using Volo.Abp.Users;
using IdentityModel;
using Abbott.SpeakerPortal.Redis;
using Abbott.SpeakerPortal.Consts;
using Microsoft.IdentityModel.Tokens;
using Microsoft.Extensions.Options;
using Abbott.SpeakerPortal.Login;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Abbott.SpeakerPortal.Contracts.Common;
using OpenIddict.Abstractions;
using Abbott.SpeakerPortal.Contracts.Common.Common;
using Abbott.SpeakerPortal.AppServices.Common;
using Microsoft.AspNetCore.Http;
using Org.BouncyCastle.Asn1.Ocsp;

namespace Abbott.SpeakerPortal.AppServices
{
    public class LoginService : SpeakerPortalAppService, ILoginService
    {
        IServiceProvider _serviceProvider;
        ICommonService _commonService;
        public LoginService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _commonService = _serviceProvider.GetService<ICommonService>();
        }

        /// <summary>
        /// Gets the JWKS.
        /// </summary>
        /// <returns></returns>
        public async Task<IEnumerable<JsonWebKey>> GetJwksAsync()
        {
            var log = new SetOperationLogRequestDto();

            try
            {


                string value = string.Empty;
                var redis = _serviceProvider.GetService<IRedisRepository>();
                value = redis.Database.StringGet(RedisKey.Jwks);
                if (string.IsNullOrEmpty(value))
                {
                    var adSettings = _serviceProvider.GetService<IOptions<AzureAdSettings>>();
                    string url = string.Format(adSettings.Value.JwksUrl, adSettings.Value.TenantId);
                    log = _commonService.InitOperationLog("Microsoft Azure AD", "获取JWKS", url);
                    using (var client = new HttpClient())
                    {
                        value = await client.GetStringAsync(url);
                    }
                    _commonService.LogResponse(log, value);
                    var jt = JToken.Parse(value);
                    value = JsonConvert.SerializeObject(jt.First.First);

                    redis.Database.StringSet(RedisKey.Jwks, value, TimeSpan.FromDays(1));
                }

                var jwks = JsonConvert.DeserializeObject<IEnumerable<JsonWebKey>>(value);
                return jwks;
            }
            catch (Exception ex)
            {
                _commonService.LogResponse(log, ex.ToString(), false);
                return null;
            }
        }

        /// <summary>
        /// Logouts this instance.
        /// </summary>
        /// <returns></returns>
        public async Task<bool> Logout()
        {
            //移除session
            await LazyServiceProvider.LazyGetService<IUserSessionService>().RemoveSessionAsync(CurrentUser.UserName);

            var dict = new Dictionary<string, string>
            {
                { "UserName", CurrentUser.UserName }
            };
            LazyServiceProvider.LazyGetService<IApplicationInsightService>().TrackEvent(ApplicationInsightEventNames.UserAuthLoginOut, dict);

            return true;
        }
    }
}
