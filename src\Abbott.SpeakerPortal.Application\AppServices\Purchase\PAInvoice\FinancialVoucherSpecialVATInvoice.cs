﻿using Abbott.SpeakerPortal.Contracts.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPAApplication.PAInvoice;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.AppServices.Purchase.PAInvoice
{
    /// <summary>
    /// 增值税专用发票
    /// </summary>
    public class FinancialVoucherSpecialVATInvoice : IFinancialVoucherInvoice
    {
        public async Task<List<FinancialVoucherInfoDto>> GetFinancialVoucherInfo(PurPAApplicationDetailDto paApplicationDetail)
        {
            decimal taxRate = 0M;
            if (!decimal.TryParse(paApplicationDetail.TaxRate, out taxRate))
            {
                taxRate = 0M;
            }
            var taxAmount = (paApplicationDetail.PaymentAmount/(1+ taxRate))* taxRate;//税额
            List<FinancialVoucherInfoDto> financialVoucherInfos = new List<FinancialVoucherInfoDto>();
            //增值税专用发票 生成一条不含税财务凭证信息
            var financialVoucherInfo = new FinancialVoucherInfoDto();
            financialVoucherInfo.PAId = paApplicationDetail.PurPAApplicationId;
            financialVoucherInfo.PRDetailId = paApplicationDetail.PRDetailId;
            financialVoucherInfo.PODetailId = paApplicationDetail.PODetailId;
            financialVoucherInfo.GRHistoryId = paApplicationDetail.GRHistoryId;
            financialVoucherInfo.PAInvoiceId = paApplicationDetail.Id;
            financialVoucherInfo.InvoiceType = "SpecialVATInvoice";
            financialVoucherInfo.GroupingDimension = GroupingDimension.TaxNotIncluded;
            financialVoucherInfo.InvoiceLineAmount = decimal.Round(paApplicationDetail.PaymentAmount- taxAmount,2);
            financialVoucherInfos.Add(financialVoucherInfo);

            //增值税专用发票 生成一条税额财务凭证信息
            var financialVoucherInfoTaxAmount = new FinancialVoucherInfoDto();
            financialVoucherInfoTaxAmount.PAId = paApplicationDetail.PurPAApplicationId;
            financialVoucherInfoTaxAmount.PRDetailId = paApplicationDetail.PRDetailId;
            financialVoucherInfoTaxAmount.PODetailId = paApplicationDetail.PODetailId;
            financialVoucherInfoTaxAmount.GRHistoryId = paApplicationDetail.GRHistoryId;
            financialVoucherInfoTaxAmount.PAInvoiceId = paApplicationDetail.Id;
            financialVoucherInfoTaxAmount.InvoiceType = "SpecialVATInvoice";
            financialVoucherInfoTaxAmount.GroupingDimension = GroupingDimension.TaxIncluded;
            financialVoucherInfoTaxAmount.InvoiceLineAmount = decimal.Round(taxAmount,2);
            financialVoucherInfos.Add(financialVoucherInfoTaxAmount);
            return await Task.FromResult(financialVoucherInfos);
        }
    }
}
