CREATE PROCEDURE dbo.sp_VendorOrgnizations
AS 
BEGIN

--	drop table #VendorOrgnizations_tmp;
with VMXCRT_tmp as (
 select CASE 
        WHEN PATINDEX('%[ FM]%', REVERSE(VMXCRT)) > 0 and( VTYPE ='NHIV' or VTYPE ='NLIV')
        THEN TRIM(LEFT(VMXCRT, LEN(VMXCRT) - PATINDEX('%[ FM]%', REVERSE(VMXCRT))))
        ELSE VMXCRT
    	END AS VMXCRT,VCMPNY,VENDOR
 from  PLATFORM_ABBOTT_Stg.dbo.ODS_BPCS_AVM
   where [VCMPNY] in ('91','79','18','20') 
 )
select newid() AS Id,--自动生成的uuid 
*
into #VendorOrgnizations_tmp
from (
SELECT DISTINCT 
a.VCMPNY,--辅助列
a.VENDOR,--辅助列
a.VNDNAM,--辅助列
a.VNSTAT,--辅助列
v.VMXCRT,--辅助列
VendorType,--辅助列
b.[VLDRM2] as [BankCardNo],--辅助列
case when vt.VendorType='3' or vt.VendorType='4' then vt.ID end AS VendorId,--基于M01-1合并的结果，对于VendorType=3/4的机构类型供应商，此处填入M01-1生成的ID，用于标记该行为机构供应商对应的扩展信息
VEXTNM AS VendorName,--供应商名称(由于基于名称识别重复及合并，不会出现不一致的情况，但可能会存在即使合并后依然重名的供应商，需要单独进行分析)
cast('todo' as nvarchar(100)) AS VendorOldName,--曾用名，根据AVM/PMFVM及AUTO_BIZ_T_SupplierApplication_Info表中VENDOR=vendorNumber+VCMPNY=company_Value+VEXTNM=supplierCNName(或AVM.VNDNAM=SupplierCNName)完全匹配成功作为条件，定位出对应的单据ID即ProcInstId，再到Form_7a708c9568fb444a884eb5eca658975f中查询processstatus为""终止(系统)""/""已完成""的记录对应ProcInstId，使用该ID查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到对应值
--(对于疑似相同的重复供应商，基于最大的ProcinstId取最新的非空数据)"
cast('todo' as nvarchar(100)) AS VendorEngName,--英文名，根据AVM/PMFVM及AUTO_BIZ_T_SupplierApplication_Info表中VENDOR=vendorNumber+VCMPNY=company_Value+VEXTNM=supplierCNName(或AVM.VNDNAM=SupplierCNName)完全匹配成功作为条件，定位出对应的单据ID即ProcInstId，再到Form_7a708c9568fb444a884eb5eca658975f中查询processstatus为""终止(系统)""/""已完成""的记录对应ProcInstId，使用该ID查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到对应值
--(对于疑似相同的重复供应商，基于最大的ProcinstId取最新的非空数据)"
case when vt.VendorType='4' then
a.VNDAD1+a.VNDAD2+a.VNDAD3
when vt.VendorType='3' then c.supplierAddress
end AS RegCertificateAddress,--地址，适用于非HCI机构类型供应商(VTYPE=NL,或VTYPE=NT且根据M01-1的逻辑识别为非HCI机构)，根据这三个字段拼接而成
--(对于疑似相同的重复供应商，基于BPCS_PMFVM中最新更新时间<VLDATE&VLTIME>对应的非空数据为准，若多条数据的更新时间相同则以最新创建时间<VCRDTE&VCTIME>为准)"
--"supplierAddress AS ,--适用于HCI机构类型供应商(VTYPE=NH,或VTYPE=NT且根据M01-1逻辑的识别为HCI机构)，
--若VNDAD1非空则与非HCI机构使用相同逻辑拼接形成地址，否则：
--根据AVM.VCMPNY=AUTO_BIZ_T_SupplierApplication_Info.company_Value且PMFVM.VEXTNM=SupplierCNName，或AVM.VNDNAM=SupplierCNName匹配，查询出对应的supplierAddress
--(对于疑似相同的重复供应商，基于BPCS_PMFVM中最新更新时间<VLDATE&VLTIME>对应的非空数据为准，若多条数据的更新时间相同则以最新创建时间<VCRDTE&VCTIME>为准)"
VPOST AS PostCode,--邮编
--(对于疑似相同的重复供应商，基于BPCS_PMFVM中最新更新时间<VLDATE&VLTIME>对应的非空数据为准，若多条数据的更新时间相同则以最新创建时间<VCRDTE&VCTIME>为准)"
VCON AS ContactName,--联系人
--(对于疑似相同的重复供应商，基于VENDOR+VCMPNY查询PMFVM中的VNDERX+VMCMPY，再定位至对应的更新时间/创建时间，使用最新数据对应的信息)"
VPHONE AS ContactPhone,--电话
--(对于疑似相同的重复供应商，基于VENDOR+VCMPNY查询PMFVM中的VNDERX+VMCMPY，再定位至对应的更新时间/创建时间，使用最新数据对应的信息)"
VEMLAD AS ContactEmail,--邮箱
--(对于疑似相同的重复供应商，基于VENDOR+VCMPNY查询PMFVM中的VNDERX+VMCMPY，再定位至对应的更新时间/创建时间，使用最新数据对应的信息)"
case when vt.VendorType='3' then  VNDMEMO01 else '' end  AS WebSite,--机构网站，仅type=3有该值，type=4时填写为空
--根据AVM/PMFVM及T_VENDORINFO_NH表中VCMPNY=CompanyCode+VEXTNM=VendorName(或AVM.VNDNAM=VendorName)完全匹配成功作为条件查询
--(对于疑似相同的重复供应商，基于VENDOR+VCMPNY查询PMFVM中的VNDERX+VMCMPY，再定位至对应的更新时间/创建时间，使用最新数据对应的信息)"
case when vt.VendorType='3' then  VNDMEMO02  when vt.VendorType='4' then '0001-01-01 00:00:00' end  AS RegisterDate,--成立日期，仅type=3有该值，type=4时填写为""0001-01-01 00:00:00""
--根据AVM/PMFVM及T_VENDORINFO_NH表中VCMPNY=CompanyCode+VEXTNM=VendorName(或AVM.VNDNAM=VendorName)完全匹配成功作为条件查询
--(对于疑似相同的重复供应商，基于VENDOR+VCMPNY查询PMFVM中的VNDERX+VMCMPY，再定位至对应的更新时间/创建时间，使用最新数据对应的信息)"
case when vt.VendorType='3' then  VNDMEMO03  when vt.VendorType='4' then '' end   AS OrgType,--机构类型，仅type=3有该值，type=4时填写为空，需要基于填入的值匹配对应的字典后，以字典code填入
--根据AVM/PMFVM及T_VENDORINFO_NH表中VCMPNY=CompanyCode+VEXTNM=VendorName(或AVM.VNDNAM=VendorName)完全匹配成功作为条件查询
--(对于疑似相同的重复供应商，基于VENDOR+VCMPNY查询PMFVM中的VNDERX+VMCMPY，再定位至对应的更新时间/创建时间，使用最新数据对应的信息)"
case when vt.VendorType='3' then  VNDMEMO04  when vt.VendorType='4' then '' end  AS IssuingAuthority,--发证机关，仅type=3有该值，type=4时填写为空
--根据AVM/PMFVM及T_VENDORINFO_NH表中VCMPNY=CompanyCode+VEXTNM=VendorName(或AVM.VNDNAM=VendorName)完全匹配成功作为条件查询
--(对于疑似相同的重复供应商，基于VENDOR+VCMPNY查询PMFVM中的VNDERX+VMCMPY，再定位至对应的更新时间/创建时间，使用最新数据对应的信息)"
case when vt.VendorType='3' then  VNDMEMO05  when vt.VendorType='4' then '' end   AS RegisterCode,--统一社会信用代码，仅type=3有该值，type=4时填写为空
--根据AVM/PMFVM及T_VENDORINFO_NH表中VCMPNY=CompanyCode+VEXTNM=VendorName(或AVM.VNDNAM=VendorName)完全匹配成功作为条件查询
--(对于疑似相同的重复供应商，基于VENDOR+VCMPNY查询PMFVM中的VNDERX+VMCMPY，再定位至对应的更新时间/创建时间，使用最新数据对应的信息)"
case when vt.VendorType='3' then  VNDMEMO06  when vt.VendorType='4' then '0001-01-01 00:00:00' end  AS RegValidityStart,--登记证书有效期的开始日期，仅type=3有该值，type=4时填写为""0001-01-01 00:00:00""
--根据AVM/PMFVM及T_VENDORINFO_NH表中VCMPNY=CompanyCode+VEXTNM=VendorName(或AVM.VNDNAM=VendorName)完全匹配成功作为条件查询
--(对于疑似相同的重复供应商，基于VENDOR+VCMPNY查询PMFVM中的VNDERX+VMCMPY，再定位至对应的更新时间/创建时间，使用最新数据对应的信息)"
case when vt.VendorType='3' then  VNDMEMO07  when vt.VendorType='4' then '0001-01-01 00:00:00' end  AS RegValidityEnd,--登记证书有效期的结束日期，仅type=3有该值，type=4时填写为""0001-01-01 00:00:00""
--根据AVM/PMFVM及T_VENDORINFO_NH表中VCMPNY=CompanyCode+VEXTNM=VendorName(或AVM.VNDNAM=VendorName)完全匹配成功作为条件查询
--(对于疑似相同的重复供应商，基于VENDOR+VCMPNY查询PMFVM中的VNDERX+VMCMPY，再定位至对应的更新时间/创建时间，使用最新数据对应的信息)"
COALESCE(SupplierProvince,c.SupplierCity)  AS Province,--根据AVM/PMFVM及T_SupplierExtendedInfo表中VENDOR=SupplierCode+VEXTNM=supplierName(或AVM.VNDNAM=SupplierCNName)完全匹配成功作为条件，查询出的Province及City即为省份和城市。若在T_SupplierExtendedInfo无法匹配或查询出为空，则根据AVM/PMFVM及AUTO_BIZ_T_SupplierApplication_Info表中VENDOR=vendorNumber+VCMPNY=company_Value+VEXTNM=supplierCNName(或AVM.VNDNAM=SupplierCNName)完全匹配成功作为条件，查询出SupplierCity即为城市信息，省份未在该表中存储需要补回。对于查询结果，若省份非空，则以省份先匹配PP中的省级主数据(匹配时建议按是否为子字符串作为查询条件，避免自治区无法匹配出)，再基于该省份下属城市对查询出的城市进行匹配；若省份为空，或省份匹配失败则将城市与所有城市名匹配，根据匹配出的城市再反填回省份信息；如果省份和城市都匹配失败则留空
--(对于疑似相同的重复供应商，基于VENDOR+VCMPNY查询PMFVM中的VNDERX+VMCMPY，再定位至对应的更新时间/创建时间，使用最新数据对应的省份城市信息)"
--SupplierCity AS ,--
COALESCE(ts.SupplierCity,c.SupplierCity) AS City,--参考省份的逻辑即可
--SupplierCity AS ,--
case when vt.VendorType='3' then  VNDMEMO09  when vt.VendorType='4' then '' end  AS Legal,--法定代表人，仅type=3有该值，type=4时填写为空
--根据AVM/PMFVM及T_VENDORINFO_NH表中VCMPNY=CompanyCode+VEXTNM=VendorName(或AVM.VNDNAM=VendorName)完全匹配成功作为条件查询
--(对于疑似相同的重复供应商，基于VENDOR+VCMPNY查询PMFVM中的VNDERX+VMCMPY，再定位至对应的更新时间/创建时间，使用最新数据对应的信息)"
case when vt.VendorType='3' then  VNDMEMO10  when vt.VendorType='4' then '0' end  AS RegisterAmount,--注册资金，仅type=3有该值，type=4时填写为0，需要根据填入的文字转换为数字
--根据AVM/PMFVM及T_VENDORINFO_NH表中VCMPNY=CompanyCode+VEXTNM=VendorName(或AVM.VNDNAM=VendorName)完全匹配成功作为条件查询
--(对于疑似相同的重复供应商，基于VENDOR+VCMPNY查询PMFVM中的VNDERX+VMCMPY，再定位至对应的更新时间/创建时间，使用最新数据对应的信息)"
case when vt.VendorType='3' then  VNDMEMO11  when vt.VendorType='4' then '' end  AS BusinessAuthority,--业务主管单位，仅type=3有该值，type=4时填写为空
--根据AVM/PMFVM及T_VENDORINFO_NH表中VCMPNY=CompanyCode+VEXTNM=VendorName(或AVM.VNDNAM=VendorName)完全匹配成功作为条件查询
--(对于疑似相同的重复供应商，基于VENDOR+VCMPNY查询PMFVM中的VNDERX+VMCMPY，再定位至对应的更新时间/创建时间，使用最新数据对应的信息)"
case when vt.VendorType='3' then  VNDMEMO12  end  AS BusinessScope,--业务范围(第一行对应type=3，第二行对应type=4)
--Type=3：根据AVM/PMFVM及T_VENDORINFO_NH表中VCMPNY=CompanyCode+VEXTNM=VendorName(或AVM.VNDNAM=VendorName)完全匹配成功作为条件查询
--(对于疑似相同的重复供应商，基于VENDOR+VCMPNY查询PMFVM中的VNDERX+VMCMPY，再定位至对应的更新时间/创建时间，使用最新数据对应的信息)
--Type=4：根据AVM/PMFVM及AUTO_BIZ_T_SupplierApplication_Info表中VENDOR=vendorNumber+VCMPNY=company_Value+VEXTNM=supplierCNName(或AVM.VNDNAM=SupplierCNName)完全匹配成功作为条件，定位出对应的单据ID即ProcInstId，再到Form_7a708c9568fb444a884eb5eca658975f中查询processstatus为""终止(系统)""/""已完成""的记录对应ProcInstId，使用该ID查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到对应值
--(对于疑似相同的重复供应商，基于较大的ProcinstId对应非空数据，作为最新数据对应的信息)"
--serviceArea AS ,--
cast('todo' as nvarchar(100)) AS LastYearSales,--去年销售额，仅type=4有该值，type=3时填写为0，需要根据填入的文字转换为数字
--根据AVM/PMFVM及AUTO_BIZ_T_SupplierApplication_Info表中VENDOR=vendorNumber+VCMPNY=company_Value+VEXTNM=supplierCNName(或AVM.VNDNAM=SupplierCNName)完全匹配成功作为条件，定位出对应的单据ID即ProcInstId，再到Form_7a708c9568fb444a884eb5eca658975f中查询processstatus为""终止(系统)""/""已完成""的记录对应ProcInstId，使用该ID查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到对应值
--(对于疑似相同的重复供应商，基于较大的ProcinstId对应非空数据，作为最新数据对应的信息)"
cast('todo' as nvarchar(100)) AS KeyIndustry,--主要工业，仅type=4有该值，type=3时填写为空
--根据AVM/PMFVM及AUTO_BIZ_T_SupplierApplication_Info表中VENDOR=vendorNumber+VCMPNY=company_Value+VEXTNM=supplierCNName(或AVM.VNDNAM=SupplierCNName)完全匹配成功作为条件，定位出对应的单据ID即ProcInstId，再到Form_7a708c9568fb444a884eb5eca658975f中查询processstatus为""终止(系统)""/""已完成""的记录对应ProcInstId，使用该ID查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到对应值
--(对于疑似相同的重复供应商，基于较大的ProcinstId对应非空数据，作为最新数据对应的信息)"
cast('todo' as nvarchar(100)) AS KeyClient,--主要客户，仅type=4有该值，type=3时填写为空
--根据AVM/PMFVM及AUTO_BIZ_T_SupplierApplication_Info表中VENDOR=vendorNumber+VCMPNY=company_Value+VEXTNM=supplierCNName(或AVM.VNDNAM=SupplierCNName)完全匹配成功作为条件，定位出对应的单据ID即ProcInstId，再到Form_7a708c9568fb444a884eb5eca658975f中查询processstatus为""终止(系统)""/""已完成""的记录对应ProcInstId，使用该ID查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到对应值
--(对于疑似相同的重复供应商，基于较大的ProcinstId对应非空数据，作为最新数据对应的信息)"
cast('todo' as nvarchar(100)) AS Staffs,--雇员人数，仅type=4有该值，type=3时填写为0，需要根据填入的文字转换为数字
--根据AVM/PMFVM及AUTO_BIZ_T_SupplierApplication_Info表中VENDOR=vendorNumber+VCMPNY=company_Value+VEXTNM=supplierCNName(或AVM.VNDNAM=SupplierCNName)完全匹配成功作为条件，定位出对应的单据ID即ProcInstId，再到Form_7a708c9568fb444a884eb5eca658975f中查询processstatus为""终止(系统)""/""已完成""的记录对应ProcInstId，使用该ID查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到对应值
--(对于疑似相同的重复供应商，基于较大的ProcinstId对应非空数据，作为最新数据对应的信息)"
cast('todo' as nvarchar(100)) AS Aptitudes,--认证信息，仅type=4有该值，type=3时填写为空(目前ABP处该数据填写在了Vendors.CertificateCode内，不确定是否会进行调整)
--根据AVM/PMFVM及AUTO_BIZ_T_SupplierApplication_Info表中VENDOR=vendorNumber+VCMPNY=company_Value+VEXTNM=supplierCNName(或AVM.VNDNAM=SupplierCNName)完全匹配成功作为条件，定位出对应的单据ID即ProcInstId，再到Form_7a708c9568fb444a884eb5eca658975f中查询processstatus为""终止(系统)""/""已完成""的记录对应ProcInstId，使用该ID查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到对应值
--(对于疑似相同的重复供应商，基于较大的ProcinstId对应非空数据，作为最新数据对应的信息)"
cast('todo' as nvarchar(100)) AS ApplyReason,--申请理由
--根据AVM/PMFVM及AUTO_BIZ_T_SupplierApplication_Info表中VENDOR=vendorNumber+VCMPNY=company_Value+VEXTNM=supplierCNName(或AVM.VNDNAM=SupplierCNName)完全匹配成功作为条件，定位出对应的单据ID即ProcInstId，再到Form_7a708c9568fb444a884eb5eca658975f中查询processstatus为""终止(系统)""/""已完成""的记录对应ProcInstId，使用该ID查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到对应值
--(对于疑似相同的重复供应商，基于较大的ProcinstId对应非空数据，作为最新数据对应的信息)"
'{}' AS ExtraProperties,--默认填写为"{}"
'' AS ConcurrencyStamp,--?
CreationTime AS CreationTime,--与对应的Vendors记录保持一致即可
CreatorId AS CreatorId,--与对应的Vendors记录保持一致即可
LastModificationTime AS LastModificationTime,--与对应的Vendors记录保持一致即可
LastModifierId AS LastModifierId,--与对应的Vendors记录保持一致即可
'0' AS IsDeleted,--默认为0
'' AS DeleterId,--默认为空
'' AS DeletionTime,--默认为空,
cast('' as nvarchar(100)) AS Shareholder--股东，仅type=4有该值，type=3时填写为0(需要补充字段)
--根据AVM/PMFVM及AUTO_BIZ_T_SupplierApplication_Info表中VENDOR=vendorNumber+VCMPNY=company_Value+VEXTNM=supplierCNName(或AVM.VNDNAM=SupplierCNName)完全匹配成功作为条件，定位出对应的单据ID即ProcInstId，再到Form_7a708c9568fb444a884eb5eca658975f中查询processstatus为""终止(系统)""/""已完成""的记录对应ProcInstId，使用该ID查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到对应值
--(对于疑似相同的重复供应商，基于较大的ProcinstId对应非空数据，作为最新数据对应的信息)"
from PLATFORM_ABBOTT_Stg.dbo.ODS_BPCS_AVM a 
left join PLATFORM_ABBOTT_Stg.dbo.ODS_BPCS_PMFVM b 
on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
left join (
	select * from (
	select 
	supplierCNName,
	company_Value, 
	supplierAddress,
	SupplierCity,
	ProcInstId,
	ROW_NUMBER() OVER (
	            PARTITION BY supplierCNName,company_Value order by ProcInstId desc) A from PLATFORM_ABBOTT_Stg.dbo.ods_AUTO_BIZ_T_SupplierApplication_Info ) B
	where a=1  
) c
on a.VCMPNY =c.company_Value  and b.VEXTNM =c.supplierCNName 
left join PLATFORM_ABBOTT_Stg.dbo.Vendor_Tmp vt
on a.VCMPNY=vt.VCMPNY and a.VENDOR=vt.VENDOR
left join PLATFORM_ABBOTT_Stg.dbo.ods_T_VENDORINFO_NH vtn
on a.VCMPNY =vtn.CompanyCode and b.VEXTNM =vtn.VendorName 
left join PLATFORM_ABBOTT_Stg.dbo.ods_T_SupplierExtendedInfo ts
on a.vendor=ts.SupplierCode and b.VEXTNM=ts.supplierName and b.VNDERX=ts.SupplierCode
left join VMXCRT_tmp v
on a.VENDOR=v.VENDOR and a.VCMPNY=v.VCMPNY
where  a.VCMPNY in ('91','79','18','20') and  (VTYPE ='NHIV' or VTYPE ='NLIV' or VTYPE ='NH' or VTYPE ='NL' or VTYPE ='NT')
)a


 PRINT(N'创建临时表VendorOrgnizations_tmp完成'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
--创建临时表#ProcInstId
select 
cast(VENDOR as nvarchar(100)) VENDOR,
cast(VCMPNY as nvarchar(100)) VCMPNY,
cast(VEXTNM as nvarchar(100)) VEXTNM,
ProcInstId
into #ProcInstId
from PLATFORM_ABBOTT_Stg.dbo.ODS_BPCS_AVM a
left join PLATFORM_ABBOTT_Stg.dbo.ODS_BPCS_PMFVM b 
on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
join (select * from (
	select 
	supplierCNName,
	company_Value, 
	supplierAddress,
	SupplierCity,
	vendorNumber,
	ProcInstId,
	ROW_NUMBER() OVER (
	            PARTITION BY supplierCNName,company_Value order by ProcInstId desc) A from PLATFORM_ABBOTT_Stg.dbo.ods_AUTO_BIZ_T_SupplierApplication_Info ) B
	where a=1  ) c
on cast(VENDOR as nvarchar(100))=cast(vendorNumber as nvarchar(100) ) and cast(VCMPNY as nvarchar(100))=cast(company_Value as nvarchar(100)) and cast(VEXTNM as nvarchar(100))=cast(supplierCNName as nvarchar(100))
 PRINT(N'创建临时表ProcInstId完成'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'));
--更新
update a set VendorOldName=z.supplierOldName,
a.VendorEngName=z.supplierENName,
a.BusinessScope=z.serviceArea,
a.LastYearSales=z.saleAmount,
a.KeyIndustry =z.mainIndustry,
a.KeyClient=z.mainCustomer,
a.Staffs=z.empNumber,
a.Aptitudes=z.certification,
a.ApplyReason=z.comEvaluation,
a.Shareholder=z.shareholder
from #VendorOrgnizations_tmp a
left join (
select c.*,
cast(d.XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/supplierOldName)[1]', 'NVARCHAR(50)') supplierOldName,
cast(d.XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/supplierENName)[1]', 'NVARCHAR(50)') supplierENName,
case when VendorType='4' then 
cast(d.XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/serviceArea)[1]', 'NVARCHAR(50)') end serviceArea,
cast(d.XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/saleAmount)[1]', 'NVARCHAR(50)') saleAmount,
cast(d.XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/mainIndustry)[1]', 'NVARCHAR(50)') mainIndustry,
cast(d.XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/mainCustomer)[1]', 'NVARCHAR(50)') mainCustomer,
cast(d.XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/empNumber)[1]', 'NVARCHAR(50)') empNumber,
cast(d.XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/certification)[1]', 'NVARCHAR(50)') certification,
cast(d.XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/comEvaluation)[1]', 'NVARCHAR(50)') comEvaluation,
cast(d.XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/shareholder)[1]', 'NVARCHAR(50)') shareholder
from (
select a.ProcInstId,VENDOR,VCMPNY,VEXTNM from  #ProcInstId a join PLATFORM_ABBOTT_Stg.dbo.ods_Form_7a708c9568fb444a884eb5eca658975f b on a.ProcInstId=b.ProcInstId
 where processstatus=N'终止(系统)' or processstatus=N'已完成') C
 join PLATFORM_ABBOTT_Stg.dbo.ODS_T_FORMINSTANCE_GLOBAL D
 on c.ProcInstId=d.ProcInstId
 left join PLATFORM_ABBOTT_Stg.dbo.Vendor_Tmp vt
on C.VCMPNY=vt.VCMPNY and C.VENDOR=vt.VENDOR) z
on z.VENDOR=a.VENDOR and a.VCMPNY=z.VCMPNY
PRINT(N'更新完成'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'));



with VendorOrgnizations as (
select 
a.VMXCRT,
VCMPNY,
VENDOR,
a.Id,
a.VendorId,
a.VendorName,
a.VendorOldName,
a.VendorEngName,
a.RegCertificateAddress,
a.PostCode,
a.ContactName,
a.ContactPhone,
a.ContactEmail,
a.WebSite,
a.RegisterDate,
a.OrgType,
a.IssuingAuthority,
a.RegisterCode,
a.RegValidityStart,
a.RegValidityEnd,
a.Province,
a.City,
a.Legal,
a.RegisterAmount,
a.BusinessAuthority,
a.BusinessScope,
a.LastYearSales,
a.KeyIndustry,
a.KeyClient,
a.Staffs,
a.Aptitudes,
a.ApplyReason,
a.ExtraProperties,
a.ConcurrencyStamp,
a.CreationTime,
a.CreatorId,
a.LastModificationTime,
a.LastModifierId,
a.IsDeleted,
a.DeleterId,
a.DeletionTime,
a.Shareholder
from (
select 
VMXCRT,
Id,
MAX(VendorId) as VendorId,
MAX(VendorName) as VendorName,
MAX(VendorOldName) as VendorOldName,
MAX(VendorEngName) as VendorEngName,
MAX(RegCertificateAddress) as RegCertificateAddress,
MAX(PostCode) as PostCode,
MAX(ContactName) as ContactName,
MAX(ContactPhone) as ContactPhone,
MAX(ContactEmail) as ContactEmail,
MAX(WebSite) as WebSite,
MAX(RegisterDate) as RegisterDate,
MAX(OrgType) as OrgType,
MAX(IssuingAuthority) as IssuingAuthority,
MAX(RegisterCode) as RegisterCode,
MAX(RegValidityStart) as RegValidityStart,
MAX(RegValidityEnd) as RegValidityEnd,
MAX(Province) as Province,
MAX(City) as City,
MAX(Legal) as Legal,
MAX(RegisterAmount) as RegisterAmount,
MAX(BusinessAuthority) as BusinessAuthority,
MAX(BusinessScope) as BusinessScope,
MAX(LastYearSales) as LastYearSales,
MAX(KeyIndustry) as KeyIndustry,
MAX(KeyClient) as KeyClient,
MAX(Staffs) as Staffs,
MAX(Aptitudes) as Aptitudes,
MAX(ApplyReason) as ApplyReason,
MAX(ExtraProperties) as ExtraProperties,
MAX(ConcurrencyStamp) as ConcurrencyStamp,
MIN(CreationTime) as CreationTime,
MAX(CreatorId) as CreatorId,
MAX(LastModificationTime) as LastModificationTime,
MAX(LastModifierId) as LastModifierId,
MAX(IsDeleted) as IsDeleted,
MAX(DeleterId) as DeleterId,
MAX(DeletionTime) as DeletionTime,
MAX(Shareholder) as Shareholder
from (select *, ROW_NUMBER() OVER (
                    PARTITION BY VMXCRT
                        ORDER BY 
                        VNSTAT ASC, VENDOR desc
                    ) AS rn from #VendorOrgnizations_tmp) c
where VendorType in (1,2) and 
(VMXCRT <> null or VMXCRT<>'') and rn=1
group by VMXCRT,ID
) a
join #VendorOrgnizations_tmp b
on a.id = b.id
union 
select 
VMXCRT,
VCMPNY,
VENDOR,
Id,
VendorId,
VendorName,
VendorOldName,
VendorEngName,
RegCertificateAddress,
PostCode,
ContactName,
ContactPhone,
ContactEmail,
WebSite,
RegisterDate,
OrgType,
IssuingAuthority,
RegisterCode,
RegValidityStart,
RegValidityEnd,
Province,
City,
Legal,
RegisterAmount,
BusinessAuthority,
BusinessScope,
LastYearSales,
KeyIndustry,
KeyClient,
Staffs,
Aptitudes,
ApplyReason,
ExtraProperties,
ConcurrencyStamp,
CreationTime,
CreatorId,
LastModificationTime,
LastModifierId,
IsDeleted,
DeleterId,
DeletionTime,
Shareholder
from (
	select 
	VMXCRT,
	VCMPNY,
	VENDOR,
	Id,
	VendorId,
	VendorName,
	VendorOldName,
	VendorEngName,
	RegCertificateAddress,
	PostCode,
	ContactName,
	ContactPhone,
	ContactEmail,
	WebSite,
	RegisterDate,
	OrgType,
	IssuingAuthority,
	RegisterCode,
	RegValidityStart,
	RegValidityEnd,
	Province,
	City,
	Legal,
	RegisterAmount,
	BusinessAuthority,
	BusinessScope,
	LastYearSales,
	KeyIndustry,
	KeyClient,
	Staffs,
	Aptitudes,
	ApplyReason,
	ExtraProperties,
	ConcurrencyStamp,
	CreationTime,
	CreatorId,
	LastModificationTime,
	LastModifierId,
	IsDeleted,
	DeleterId,
	DeletionTime,
	Shareholder,
	ROW_NUMBER() over(PARTITION by [BankCardNo],VNDNAM order by VENDOR desc ,VNSTAT ASC, VENDOR desc) rn1
	from #VendorOrgnizations_tmp
	where VendorType in (3,4) or
	VMXCRT= null or VMXCRT=''
	)a
	where rn1=1
)
select * 
into #VendorOrgnizations_tmp1
from VendorOrgnizations



--落成实体表
 IF OBJECT_ID(N'PLATFORM_ABBOTT_Stg.dbo.VendorOrgnizations_tmp', N'U') IS NOT NULL
BEGIN
	
	update a 
	set  a.VMXCRT                  = b.VMXCRT
        ,a.VCMPNY                  = b.VCMPNY
        ,a.VENDOR                  = b.VENDOR
        ,a.VendorId                = b.VendorId
        ,a.VendorName              = b.VendorName
        ,a.VendorOldName           = b.VendorOldName
        ,a.VendorEngName           = b.VendorEngName
        ,a.RegCertificateAddress   = b.RegCertificateAddress
        ,a.PostCode                = b.PostCode
        ,a.ContactName             = b.ContactName
        ,a.ContactPhone            = b.ContactPhone
        ,a.ContactEmail            = b.ContactEmail
        ,a.WebSite                 = b.WebSite
        ,a.RegisterDate            = b.RegisterDate
        ,a.OrgType                 = b.OrgType
        ,a.IssuingAuthority        = b.IssuingAuthority
        ,a.RegisterCode            = b.RegisterCode
        ,a.RegValidityStart        = b.RegValidityStart
        ,a.RegValidityEnd          = b.RegValidityEnd
        ,a.Province                = b.Province
        ,a.City                    = b.City
        ,a.Legal                   = b.Legal
        ,a.RegisterAmount          = b.RegisterAmount
        ,a.BusinessAuthority       = b.BusinessAuthority
        ,a.BusinessScope           = b.BusinessScope
        ,a.LastYearSales           = b.LastYearSales
        ,a.KeyIndustry             = b.KeyIndustry
        ,a.KeyClient               = b.KeyClient
        ,a.Staffs                  = b.Staffs
        ,a.Aptitudes               = b.Aptitudes
        ,a.ApplyReason             = b.ApplyReason
        ,a.ExtraProperties         = b.ExtraProperties
        ,a.ConcurrencyStamp        = b.ConcurrencyStamp
        ,a.CreationTime            = b.CreationTime
        ,a.CreatorId               = b.CreatorId
        ,a.LastModificationTime    = b.LastModificationTime
        ,a.LastModifierId          = b.LastModifierId
        ,a.IsDeleted               = b.IsDeleted
        ,a.DeleterId               = b.DeleterId
        ,a.DeletionTime            = b.DeletionTime
        ,a.Shareholder             = b.Shareholder
    from PLATFORM_ABBOTT_Stg.dbo.VendorOrgnizations_tmp a
    left join #VendorOrgnizations_tmp1 b on a.VCMPNY = b.VCMPNY and a.VENDOR = b.VENDOR
    
    insert into PLATFORM_ABBOTT_Stg.dbo.VendorOrgnizations_tmp
    select a.VMXCRT
           ,a.VCMPNY
           ,a.VENDOR
           ,a.Id
           ,a.VendorId
           ,a.VendorName
           ,a.VendorOldName
           ,a.VendorEngName
           ,a.RegCertificateAddress
           ,a.PostCode
           ,a.ContactName
           ,a.ContactPhone
           ,a.ContactEmail
           ,a.WebSite
           ,a.RegisterDate
           ,a.OrgType
           ,a.IssuingAuthority
           ,a.RegisterCode
           ,a.RegValidityStart
           ,a.RegValidityEnd
           ,a.Province
           ,a.City
           ,a.Legal
           ,a.RegisterAmount
           ,a.BusinessAuthority
           ,a.BusinessScope
           ,a.LastYearSales
           ,a.KeyIndustry
           ,a.KeyClient
           ,a.Staffs
           ,a.Aptitudes
           ,a.ApplyReason
           ,a.ExtraProperties
           ,a.ConcurrencyStamp
           ,a.CreationTime
           ,a.CreatorId
           ,a.LastModificationTime
           ,a.LastModifierId
           ,a.IsDeleted
           ,a.DeleterId
           ,a.DeletionTime
           ,a.Shareholder
	from #VendorOrgnizations_tmp1 a 
	where not exists (select * from PLATFORM_ABBOTT_Stg.dbo.VendorOrgnizations_tmp where a.VCMPNY = VCMPNY and a.VENDOR = VENDOR)
	
	PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into PLATFORM_ABBOTT_Stg.dbo.VendorOrgnizations_tmp from #VendorOrgnizations_tmp1
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
END;
