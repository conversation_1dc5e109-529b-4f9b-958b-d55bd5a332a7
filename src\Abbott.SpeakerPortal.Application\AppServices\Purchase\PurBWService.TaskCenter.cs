﻿using Abbott.SpeakerPortal.Contracts.Purchase.PurBDApplication;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Entities.Purchase.PurBDApplication;
using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Purchase.PurExemptApplication;
using Abbott.SpeakerPortal.Extension;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using System.Linq;
using Abbott.SpeakerPortal.Entities.Purchase.PurBWApplication;
using static Abbott.SpeakerPortal.Enums.Purchase;
using Microsoft.Extensions.Logging;
using System;
using Abbott.SpeakerPortal.Entities.Common.Workflow.WorkflowTasks;
using Abbott.SpeakerPortal.Contracts.Agent;

namespace Abbott.SpeakerPortal.AppServices.Purchase
{
    public partial class PurBWService : SpeakerPortalAppService, IPurBWService
    {
        /// <summary>
        /// 豁免单 我审批的 列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public PagedResultDto<PurBWPageResponseApprovalDto> GetPageMyApproval(PurBWPageTaskRequestDto request, bool isPage = true)
        {
            try
            {
                var result = new PagedResultDto<PurBWPageResponseApprovalDto>();
                var queryBW = LazyServiceProvider.LazyGetService<IPurBWApplicationReadonlyRepository>().GetQueryableAsync().GetAwaiterResult();

                //待处理
                if (request.ProcessingStatus == ProcessingStatus.PendingProcessing)
                {
                    var workflowType = request.ExemptType == Enums.Purchase.ExemptType.Waiver ? WorkflowTypeName.ExemptWaiver : WorkflowTypeName.ExemptJustification;
                    var taskRecords = LazyServiceProvider.LazyGetService<IDataverseService>().GetApprovelTaskAsync(CurrentUser.Id.ToString(), [workflowType], request.ProcessingStatus).GetAwaiterResult();

                    var bws = queryBW.Where(a => taskRecords.Select(b => b.FormId).Contains(a.Id))
                        .Where(a => a.ExemptType == request.ExemptType)
                        .WhereIf(!string.IsNullOrWhiteSpace(request.ApplicationCode), a => a.ApplicationCode.Contains(request.ApplicationCode))
                        .WhereIf(!string.IsNullOrWhiteSpace(request.ApplyDeptName), a => a.ApplyDeptName.Contains(request.ApplyDeptName))
                        .WhereIf(!string.IsNullOrWhiteSpace(request.ApplyUserName), a => a.ApplyUserName.Contains(request.ApplyUserName))
                        .WhereIf(!string.IsNullOrWhiteSpace(request.VendorName), a => a.VendorName.Contains(request.VendorName))
                        .WhereIf(request.Status.HasValue, a => a.Status == request.Status.Value)
                        .WhereIf(request.ApplyStartTime.HasValue, a => a.ApplyTime >= request.ApplyStartTime)
                        .WhereIf(request.ApplyEndTime.HasValue, a => a.ApplyTime <= request.ApplyEndTime)
                        .ToArray();

                    result.TotalCount = bws.Count();

                    if (isPage)
                    {
                        if (request.IsAsc)
                        {
                            result.Items = bws.Join(taskRecords, a => a.Id, a => a.FormId, (a, b) => new { Bw = a, Task = b })
                                .OrderBy(a => a.Task.CreatedTime)
                                .Skip(request.PageIndex * request.PageSize).Take(request.PageSize)
                                .Select(g => new PurBWPageResponseApprovalDto
                                {
                                    Id = g.Bw.Id,
                                    ApplicationCode = g.Bw.ApplicationCode,
                                    ApplyDeptId = g.Bw.ApplyDeptId,
                                    ApplyDeptName = g.Bw.ApplyDeptName,
                                    ApplyUserId = g.Bw.ApplyUserId,
                                    ApplyUserName = g.Bw.ApplyUserName,
                                    RequisitionAmount = g.Bw.RequisitionAmount,
                                    VendorId = g.Bw.VendorId,
                                    VendorName = g.Bw.VendorName,
                                    ApplyTime = g.Bw.ApplyTime,
                                    Status = g.Bw.Status,
                                    CreationTime = g.Bw.CreationTime,
                                    TaskId = g.Task.TaskId,
                                    StepNo = g.Task.Step
                                }).ToList();
                        }
                        else
                        {
                            result.Items = bws.Join(taskRecords, a => a.Id, a => a.FormId, (a, b) => new { Bw = a, Task = b })
                                .OrderByDescending(a => a.Task.CreatedTime)
                                .Skip(request.PageIndex * request.PageSize).Take(request.PageSize)
                                .Select(g => new PurBWPageResponseApprovalDto
                                {
                                    Id = g.Bw.Id,
                                    ApplicationCode = g.Bw.ApplicationCode,
                                    ApplyDeptId = g.Bw.ApplyDeptId,
                                    ApplyDeptName = g.Bw.ApplyDeptName,
                                    ApplyUserId = g.Bw.ApplyUserId,
                                    ApplyUserName = g.Bw.ApplyUserName,
                                    RequisitionAmount = g.Bw.RequisitionAmount,
                                    VendorId = g.Bw.VendorId,
                                    VendorName = g.Bw.VendorName,
                                    ApplyTime = g.Bw.ApplyTime,
                                    Status = g.Bw.Status,
                                    CreationTime = g.Bw.CreationTime,
                                    TaskId = g.Task.TaskId,
                                    StepNo = g.Task.Step
                                }).ToList();
                        }
                    }
                    else
                    {
                        if (request.IsAsc)
                        {
                            result.Items = bws.Join(taskRecords, a => a.Id, a => a.FormId, (a, b) => new { Bw = a, Task = b })
                                .OrderBy(a => a.Task.CreatedTime)
                                .Select(g => new PurBWPageResponseApprovalDto
                                {
                                    Id = g.Bw.Id,
                                    ApplicationCode = g.Bw.ApplicationCode,
                                    ApplyDeptId = g.Bw.ApplyDeptId,
                                    ApplyDeptName = g.Bw.ApplyDeptName,
                                    ApplyUserId = g.Bw.ApplyUserId,
                                    ApplyUserName = g.Bw.ApplyUserName,
                                    RequisitionAmount = g.Bw.RequisitionAmount,
                                    VendorId = g.Bw.VendorId,
                                    VendorName = g.Bw.VendorName,
                                    ApplyTime = g.Bw.ApplyTime,
                                    Status = g.Bw.Status,
                                    CreationTime = g.Bw.CreationTime,
                                    TaskId = g.Task.TaskId,
                                    StepNo = g.Task.Step
                                }).ToList();
                        }
                        else
                        {
                            result.Items = bws.Join(taskRecords, a => a.Id, a => a.FormId, (a, b) => new { Bw = a, Task = b })
                                .OrderByDescending(a => a.Task.CreatedTime)
                                .Select(g => new PurBWPageResponseApprovalDto
                                {
                                    Id = g.Bw.Id,
                                    ApplicationCode = g.Bw.ApplicationCode,
                                    ApplyDeptId = g.Bw.ApplyDeptId,
                                    ApplyDeptName = g.Bw.ApplyDeptName,
                                    ApplyUserId = g.Bw.ApplyUserId,
                                    ApplyUserName = g.Bw.ApplyUserName,
                                    RequisitionAmount = g.Bw.RequisitionAmount,
                                    VendorId = g.Bw.VendorId,
                                    VendorName = g.Bw.VendorName,
                                    ApplyTime = g.Bw.ApplyTime,
                                    Status = g.Bw.Status,
                                    CreationTime = g.Bw.CreationTime,
                                    TaskId = g.Task.TaskId,
                                    StepNo = g.Task.Step
                                }).ToList();
                        }
                    }
                }
                else//已完成
                {
                    var queryWfTask = LazyServiceProvider.LazyGetService<IWorkflowTaskReadonlyRepository>().GetQueryableAsync().GetAwaiterResult();
                    var query = queryBW.Where(a => a.ExemptType == request.ExemptType)
                        .WhereIf(!string.IsNullOrWhiteSpace(request.ApplicationCode), a => a.ApplicationCode.Contains(request.ApplicationCode))
                        .WhereIf(!string.IsNullOrWhiteSpace(request.ApplyDeptName), a => a.ApplyDeptName.Contains(request.ApplyDeptName))
                        .WhereIf(!string.IsNullOrWhiteSpace(request.ApplyUserName), a => a.ApplyUserName.Contains(request.ApplyUserName))
                        .WhereIf(!string.IsNullOrWhiteSpace(request.VendorName), a => a.VendorName.Contains(request.VendorName))
                        .WhereIf(request.Status.HasValue, a => a.Status == request.Status.Value)
                        .WhereIf(request.ApplyStartTime.HasValue, a => a.ApplyTime >= request.ApplyStartTime)
                        .WhereIf(request.ApplyEndTime.HasValue, a => a.ApplyTime <= request.ApplyEndTime)
                        .Join(queryWfTask.Where(a => a.ApprovalId == CurrentUser.Id), a => a.Id, a => a.FormId, (a, b) => new { Bw = a, Task = b });

                    result.TotalCount = query.Count();
                    if (request.IsAsc)
                        result.Items = query.OrderBy(a => a.Task.ApprovalTime)
                            .PagingIf(isPage, request.PageIndex, request.PageSize)
                            .Select(g => new PurBWPageResponseApprovalDto
                            {
                                Id = g.Bw.Id,
                                ApplicationCode = g.Bw.ApplicationCode,
                                ApplyDeptId = g.Bw.ApplyDeptId,
                                ApplyDeptName = g.Bw.ApplyDeptName,
                                ApplyUserId = g.Bw.ApplyUserId,
                                ApplyUserName = g.Bw.ApplyUserName,
                                RequisitionAmount = g.Bw.RequisitionAmount,
                                VendorId = g.Bw.VendorId,
                                VendorName = g.Bw.VendorName,
                                ApplyTime = g.Bw.ApplyTime,
                                Status = g.Bw.Status,
                                CreationTime = g.Bw.CreationTime,
                                TaskId = g.Task.Id,
                                StepNo = g.Task.StepNo,
                                ApprovalTime = g.Task.ApprovalTime
                            }).ToList();
                    else
                        result.Items = query.OrderByDescending(a => a.Task.ApprovalTime)
                            .PagingIf(isPage, request.PageIndex, request.PageSize)
                            .Select(g => new PurBWPageResponseApprovalDto
                            {
                                Id = g.Bw.Id,
                                ApplicationCode = g.Bw.ApplicationCode,
                                ApplyDeptId = g.Bw.ApplyDeptId,
                                ApplyDeptName = g.Bw.ApplyDeptName,
                                ApplyUserId = g.Bw.ApplyUserId,
                                ApplyUserName = g.Bw.ApplyUserName,
                                RequisitionAmount = g.Bw.RequisitionAmount,
                                VendorId = g.Bw.VendorId,
                                VendorName = g.Bw.VendorName,
                                ApplyTime = g.Bw.ApplyTime,
                                Status = g.Bw.Status,
                                CreationTime = g.Bw.CreationTime,
                                TaskId = g.Task.Id,
                                StepNo = g.Task.StepNo,
                                ApprovalTime = g.Task.ApprovalTime
                            }).ToList();
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError($"PurBWService's GetPageMyApproval() has an error : {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 豁免单 我发起的 列表
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public PagedResultDto<PurBWPageResponseDto> GetPageMyApply(PurBWPageTaskRequestDto request, bool isPage = true)
        {
            try
            {
                var result = new PagedResultDto<PurBWPageResponseDto>();

                //不查PP的Task，直接查豁免表，大Tab分别查以下状态：
                //待处理（退回、撤回）：重新发起；
                //进行中：审批中；
                //已完成：通过/拒绝/作废。
                PurExemptStatus[] filterStatus = request.ProcessingStatus switch
                {
                    ProcessingStatus.PendingProcessing => [PurExemptStatus.Return],
                    ProcessingStatus.Progressing => [PurExemptStatus.Approving],
                    ProcessingStatus.Completed => [PurExemptStatus.Approved, PurExemptStatus.Rejected, PurExemptStatus.Invalid],
                    _ => [PurExemptStatus.Return],//默认为“待处理”
                };

                var queryBW = LazyServiceProvider.LazyGetService<IPurBWApplicationReadonlyRepository>().GetQueryableAsync().GetAwaiterResult();
                var agents = LazyServiceProvider.LazyGetService<IAgencyService>().GetOriginalOperators(new GetAgentOperatorsRequestDto
                {
                    BusinessTypeCategory = request.ExemptType == ExemptType.Waiver ? ResignationTransfer.TaskFormCategory.BiddingWaiverApplication : ResignationTransfer.TaskFormCategory.JustificationApplication
                }).Result;
                var userIds = agents.Select(a => a.Key).Distinct().ToArray();
                var principalIds = userIds.Where(a => a != CurrentUser.Id.Value).ToArray();
                var query = queryBW.Where(a => (a.ApplyUserId == CurrentUser.Id && !a.TransfereeId.HasValue) || CurrentUser.Id.Value == a.TransfereeId.Value || principalIds.ToHashSet().Contains(a.ApplyUserId))//提交人为当前UserId
                    .Where(a => filterStatus.Contains(a.Status))
                    .Where(a => a.ExemptType == request.ExemptType)
                    .WhereIf(!string.IsNullOrWhiteSpace(request.ApplicationCode), a => a.ApplicationCode.Contains(request.ApplicationCode))
                    .WhereIf(!string.IsNullOrWhiteSpace(request.ApplyDeptName), a => a.ApplyDeptName.Contains(request.ApplyDeptName))
                    .WhereIf(!string.IsNullOrWhiteSpace(request.ApplyUserName), a => a.ApplyUserName.Contains(request.ApplyUserName))
                    .WhereIf(!string.IsNullOrWhiteSpace(request.VendorName), a => a.VendorName.Contains(request.VendorName))
                    .WhereIf(request.Status.HasValue, a => a.Status == request.Status.Value)
                    .WhereIf(request.ApplyStartTime.HasValue, a => a.ApplyTime >= request.ApplyStartTime)
                    .WhereIf(request.ApplyEndTime.HasValue, a => a.ApplyTime <= request.ApplyEndTime);

                var count = query.Count();
                var data = query.OrderByDescending(a => a.ApplyTime)//我发起的 列表按申请时间排序
                    .PagingIf(isPage, request.PageIndex, request.PageSize)
                    //先不ToList()，这里只查出需要的列（以下Select只会查出需要的列）
                    .Select(g => new PurBWPageResponseDto
                    {
                        Id = g.Id,
                        ApplicationCode = g.ApplicationCode,
                        ApplyDeptId = g.ApplyDeptId,
                        ApplyDeptName = g.ApplyDeptName,
                        ApplyUserId = g.ApplyUserId,
                        ApplyUserName = g.ApplyUserName,
                        RequisitionAmount = g.RequisitionAmount,
                        VendorId = g.VendorId,
                        VendorName = g.VendorName,
                        ApplyTime = g.ApplyTime,
                        Status = g.Status,
                        CreationTime = g.CreationTime,
                    }).ToList();

                result.TotalCount = count;
                result.Items = data;
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError($"PurBWService's GetPageMyApply() has an error : {ex.Message}");
                return null;
            }
        }
    }
}