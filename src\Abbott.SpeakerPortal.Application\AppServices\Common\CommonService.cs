﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

using Abbott.SpeakerPortal.Consent;
using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Budget;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.Attachment;
using Abbott.SpeakerPortal.Contracts.Common.Blob;
using Abbott.SpeakerPortal.Contracts.Common.Cognitive;
using Abbott.SpeakerPortal.Contracts.Common.Common;
using Abbott.SpeakerPortal.Contracts.Common.InvoiceTax;
using Abbott.SpeakerPortal.Contracts.Dataverse;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.Budget;
using Abbott.SpeakerPortal.Entities.Common.Consent;
using Abbott.SpeakerPortal.Entities.Common.Log;
using Abbott.SpeakerPortal.Entities.Integration.Dspot;
using Abbott.SpeakerPortal.Redis;

using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MiniExcelLibs.OpenXml;
using MiniExcelLibs;
using Senparc.CO2NET.Utilities;

using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Data;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using Abbott.SpeakerPortal.Entities.VendorApplications;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPOApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurBDApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurBWApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurGRApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Contracts.Agent;
using DocumentFormat.OpenXml.Vml.Office;
using Abbott.SpeakerPortal.Contracts.Agent.Transferee;
using Abbott.SpeakerPortal.Models;
using Volo.Abp.OpenIddict.Applications;
using System.Text.Json;
using Microsoft.AspNetCore.Authentication;
using Abbott.SpeakerPortal.Entities.OECSpeakerAuthApplys;
using Abbott.SpeakerPortal.Entities.EFlow.STicket;
using Abbott.SpeakerPortal.Entities.EFlow.FOC;
using Abbott.SpeakerPortal.Entities.EFlow.Return;

namespace Abbott.SpeakerPortal.AppServices.Common
{
    public class CommonService : SpeakerPortalAppService, ICommonService
    {
        private const string DEPT_CONNECT_STR = "-";
        private readonly IWebHostEnvironment _env;
        public CommonService(IWebHostEnvironment env)
        {
            this._env = env;
        }

        /// <summary>
        /// 获取省市树形数据
        /// </summary>
        /// <returns></returns>
        public async Task<List<ProvinceCityDto>> GetProvinceCity()
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            List<ProvinceCityDto> provinceCityDtos = new List<ProvinceCityDto>();
            List<ProvinceDto> provinceList = await dataverseService.GetAllProvince();
            List<CityDto> cityList = await dataverseService.GetAllCity();
            var provinceList_ordered = provinceList.OrderBy(o => o.Name, StringComparer.Create(CultureInfo.GetCultureInfo("zh-cn"), true)).ToList();
            var cityList_ordered = cityList.OrderBy(o => o.Name, StringComparer.Create(CultureInfo.GetCultureInfo("zh-cn"), true)).ToList();

            foreach (var item in provinceList_ordered)
            {
                var cityGroup = cityList_ordered.Where(c => c.ProvinceId == item.Id).ToList();
                provinceCityDtos.Add(new ProvinceCityDto()
                {
                    Id = item.Id,
                    Name = item.Name,
                    Code = item.Code,
                    Cities = cityGroup
                });
            }

            return provinceCityDtos;
        }

        /// <summary>
        /// 获取部门类型的组织数据(与指定用户有关系的组织)
        /// </summary>
        /// <returns></returns>
        public async Task<IEnumerable<KeyValuePair<Guid, string>>> GetOrganizationDept(Guid userId)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var orgs = await dataverseService.GetOrganizations();
            var staffOrgRelations = await dataverseService.GetStaffDepartmentRelations();

            var staffOrgItems = staffOrgRelations.Where(x => x.UserId == userId);
            if (staffOrgItems == null)
                return null;

            var orgNameTree = new List<string>();
            List<KeyValuePair<Guid, string>> datas = [];
            foreach (var item in staffOrgItems)
            {
                orgNameTree.Clear();
                DepartmentDto dept = null;
                var firstOrgId = item.DepartmentId;
                var orgId = firstOrgId;
                bool isFindNext = true;
                do
                {
                    dept = orgs.FirstOrDefault(x => x.Id == orgId);
                    if (dept == null)
                        break;
                    orgNameTree.Add(dept.DepartmentName);
                    if (dept.OrganizationType != Enums.DataverseEnums.Organization.OrganizationType.Dept || !dept.ParentDepartment.HasValue)
                        isFindNext = false;
                    else
                        orgId = dept.ParentDepartment.Value;
                } while (isFindNext);

                if (orgNameTree.Count == 0)
                    continue;

                orgNameTree.Reverse();
                var txt = orgNameTree.JoinAsString(DEPT_CONNECT_STR);
                datas.Add(new KeyValuePair<Guid, string>(firstOrgId, txt));
            }
            if (datas.Count < 1)
                return null;
            return datas;
        }

        /// <summary>
        /// 获取部门类型的组织数据(与指定用户有关系的组织)，包含每个部门所属BU
        /// </summary>
        /// <returns></returns>
        public async Task<IEnumerable<DepartmentWithBuDto>> GetOrganizationDeptWithBU(Guid userId)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var orgs = await dataverseService.GetOrganizations();
            var staffOrgRelations = await dataverseService.GetStaffDepartmentRelations();

            var staffOrgItems = staffOrgRelations.Where(x => x.UserId == userId);
            if (staffOrgItems == null)
                return null;

            var result = new List<DepartmentWithBuDto>();
            foreach (var item in staffOrgItems)
            {
                //当前item的部门数据
                var curUserDeptBu = new DepartmentWithBuDto { Id = item.DepartmentId };
                var curDeptId = item.DepartmentId;

                var levelsName = new List<string>();
                while (true)
                {
                    var curDept = orgs.FirstOrDefault(x => x.Id == curDeptId);
                    if (curDept == null)
                        break;

                    //设置最低级的部门名
                    if (curDeptId == curUserDeptBu.Id)
                    {
                        curUserDeptBu.Name = curDept.DepartmentName;
                    }

                    //整理FullName
                    levelsName.Add(curDept.DepartmentName);

                    if (curDept.OrganizationType == Enums.DataverseEnums.Organization.OrganizationType.Bu || !curDept.ParentDepartment.HasValue)
                    {
                        //找到层级为BU后，设置Bu，再Break
                        curUserDeptBu.Bu = curDept;
                        break;
                    }
                    else
                    {
                        //继续往上找
                        curDeptId = curDept.ParentDepartment.Value;
                    }
                }

                if (levelsName.Count > 0)
                {
                    levelsName.Reverse();
                    curUserDeptBu.FullName = levelsName.JoinAsString(DEPT_CONNECT_STR);
                }
                result.Add(curUserDeptBu);
            }
            return result;
        }

        /// <summary>
        /// 根据传入的组织机构获取所有下级组织机构
        /// </summary>
        /// <param name="org"></param>
        /// <param name="orgs"></param>
        /// <returns></returns>
        public async Task<IEnumerable<DepartmentDto>> GetChildrenOrgs(DepartmentDto org, List<DepartmentDto> orgs = null)
        {
            var list = new List<DepartmentDto>();
            if (orgs == null)
                orgs = await LazyServiceProvider.LazyGetService<IDataverseService>().GetOrganizations();

            var children = orgs.Where(a => a.ParentDepartment == org.Id);
            foreach (var item in children)
            {
                var datas = await GetChildrenOrgs(item, orgs);
                list.AddRange(datas);
            }

            list.Add(org);
            return list;
        }
        /// <summary>
        /// 根据传入的组织机构获取所有下级组织机构
        /// </summary>
        /// <param name="Ids"></param>
        /// <param name="orgs"></param>
        /// <returns></returns>
        public async Task<IEnumerable<DepartmentDto>> GetChildrenOrgs(List<Guid> Ids, List<DepartmentDto> orgs = null)
        {
            var list = new List<DepartmentDto>();
            if (Ids.Count == 0) return list;
            if (orgs == null)
                orgs = await LazyServiceProvider.LazyGetService<IDataverseService>().GetOrganizations();

            var children = orgs.Where(a => a.ParentDepartment.HasValue && Ids.Contains(a.ParentDepartment.Value));
            if (children.Any())
            {
                list.AddRange(children.ToList());
                var pIds = children.Select(m => m.Id).ToList();
                var datas = await GetChildrenOrgs(pIds, orgs);
                list.AddRange(datas);
            }
            return list;
        }
        /// <summary>
        /// 根据传入的组织机构向上获取所有父级组织机构至BU
        /// </summary>
        /// <param name="org"></param>
        /// <param name="orgs"></param>
        /// <returns></returns>
        public async Task<IEnumerable<DepartmentDto>> GetParentOrgs(DepartmentDto org, List<DepartmentDto> orgs = null)
        {
            if (orgs == null)
                orgs = await LazyServiceProvider.LazyGetService<IDataverseService>().GetOrganizations();

            var list = new List<DepartmentDto>() { org };
            if (org.OrganizationType == Enums.DataverseEnums.Organization.OrganizationType.Bu)
                return list;

            var parent = orgs.FirstOrDefault(a => a.Id == org?.ParentDepartment);
            if (parent != null)
            {
                list.Add(parent);

                if (parent.OrganizationType == Enums.DataverseEnums.Organization.OrganizationType.Bu)
                    return list;

                var ls = await GetParentOrgs(parent, orgs);
                list.AddRange(ls);
            }

            return list;
        }

        /// <summary>
        /// 获取所有上级组织
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="orgs"></param>
        /// <returns></returns>
        public async Task<IEnumerable<DepartmentDto>> GetAllParentOrgs(Guid orgId, List<DepartmentDto> orgs = null)
        {
            if (orgs == null)
                orgs = await LazyServiceProvider.LazyGetService<IDataverseService>().GetOrganizations();

            List<DepartmentDto> list = null;
            var org = orgs.FirstOrDefault(a => a.Id == orgId);
            if (org != null)
            {
                list = new List<DepartmentDto>() { org };
                if (org.ParentDepartment.HasValue)
                {
                    var result = await GetAllParentOrgs(org.ParentDepartment.Value, orgs);
                    list.AddRange(result);
                }
            }

            return list;
        }

        /// <summary>
        /// 根据传入的组织机构向上获取所有父级组织机构至分公司
        /// </summary>
        /// <param name="org"></param>
        /// <param name="orgs"></param>
        /// <returns></returns>
        public async Task<IEnumerable<DepartmentDto>> GetAffiliatesOrgs(DepartmentDto org, List<DepartmentDto> orgs = null)
        {
            if (orgs == null)
                orgs = await LazyServiceProvider.LazyGetService<IDataverseService>().GetOrganizations();

            var list = new List<DepartmentDto>() { org };
            if (org.OrganizationType == Enums.DataverseEnums.Organization.OrganizationType.Affiliates)
                return list;

            var parent = orgs.FirstOrDefault(a => a.Id == org?.ParentDepartment);
            if (parent != null)
            {
                list.Add(parent);

                if (parent.OrganizationType == Enums.DataverseEnums.Organization.OrganizationType.Affiliates)
                    return list;

                var ls = await GetAffiliatesOrgs(parent, orgs);
                list.AddRange(ls);
            }
            return list;
        }

        /// <summary>
        /// 获取组织，模糊查询
        /// </summary>
        /// <param name="keyword">The keyword.</param>
        /// <param name="count">The count.</param>
        /// <returns></returns>
        public async Task<IEnumerable<DepartmentDto>> GetOrgsAsync(string keyword, int count = 200)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var orgs = await dataverseService.GetOrganizations(keyword, count);
            return orgs;
        }

        /// <summary>
        /// 获取内部可用用户列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetActiveUserListResponseDto>> GetInternalActiveUsersAsync(GetActiveUserListRequestDto request)
        {
            //获取选择的部门及子部门
            List<DepartmentDto> orgs;
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            if (!request.DepartmentId.HasValue)
            {
                var org = await dataverseService.GetOrganizations(request.ApplicantOrg.ToString());
                //orgs = await GetParentOrgs(org.FirstOrDefault());
                var applicantOrgBu = (await GetParentOrgs(org.FirstOrDefault())).Where(a => a.OrganizationType == Enums.DataverseEnums.Organization.OrganizationType.Bu).FirstOrDefault();//bu
                orgs = (await GetChildrenOrgs(applicantOrgBu)).ToList();
                orgs.Add(applicantOrgBu);
            }
            else
                orgs = [new DepartmentDto { Id = request.DepartmentId.Value }];

            //获取部门关联的人员
            var relations = new List<StaffDepartmentRelationDto>();
            var redisRepository = LazyServiceProvider.LazyGetService<IRedisRepository>();
            var relationsAll = (await dataverseService.GetStaffDepartmentRelations()).ToList();//获取所有员工机构关系数据
            foreach (var item in orgs)
            {
                var rs = relationsAll.Where(a => a.DepartmentId == item.Id);
                relations.AddRange(rs);
            }

            var userIds = relations.Select(a => a.UserId);
            var queryUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser, Guid>>().GetQueryableAsync();
            var query = queryUser.Where(a => userIds.Contains(a.Id) && a.IsActive)
                .WhereIf(!string.IsNullOrEmpty(request.StaffCode), a => EF.Property<string>(a, EntityConsts.IdentityUser.StaffCode).Contains(request.StaffCode))
                .WhereIf(!string.IsNullOrEmpty(request.Name), a => a.Name.Contains(request.Name))
                .WhereIf(!string.IsNullOrEmpty(request.Email), a => a.Email.Contains(request.Email))
                .WhereIf(!string.IsNullOrEmpty(request.AdAccount), a => a.UserName.Contains(request.AdAccount))
                .Select(a => new GetActiveUserListResponseDto
                {
                    Id = a.Id,
                    Name = a.Name,
                    StaffCode = EF.Property<string>(a, EntityConsts.IdentityUser.StaffCode),
                    Email = a.Email,
                    OrganizationName = EF.Property<string>(a, EntityConsts.IdentityUser.DepartmentId),
                    AdAccount = a.UserName
                });

            var count = query.Count();
            var datas = query.Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToArray();
            foreach (var item in datas)
            {
                var departments = relations.Where(a => a.UserId == item.Id)
                    .Join(orgs, a => a.DepartmentId, a => a.Id, (a, b) => b);

                item.OrganizationName = string.Join(",", departments.Select(a => a.DepartmentName).Distinct());
            }

            var result = new PagedResultDto<GetActiveUserListResponseDto>(count, datas);
            return result;
        }

        /// <summary>
        /// 获取所有内部可用用户列表
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetActiveUserListResponseDto>> GetAllInternalActiveUsersAsync(GetActiveUserListRequestDto request)
        {
            //获取选择的部门及子部门
            IEnumerable<DepartmentDto> orgs;
            IEnumerable<Guid> userIds = null;
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            List<StaffDepartmentRelationDto> relations = new List<StaffDepartmentRelationDto>();
            relations.AddRange(await dataverseService.GetStaffDepartmentRelations());
            if (!request.DepartmentId.HasValue)//BuId 主&子预算时加的，其他地方如果要用这个字段需要评估下是否合适
                request.DepartmentId = request.BuId;

            if (request.DepartmentId.HasValue)
            {
                var org = await dataverseService.GetOrganizations(request.DepartmentId.ToString());
                orgs = await GetChildrenOrgs(org.FirstOrDefault());
                //获取部门关联的人员                
                var orgIds = orgs.Select(o => o.Id);
                userIds = relations.Where(r => orgIds.Contains(r.DepartmentId)).Select(r => r.UserId).Distinct();

                if (!userIds.Any())
                    return new PagedResultDto<GetActiveUserListResponseDto>(0, null);
                //获取部门关联的人员
                //var redisRepository = LazyServiceProvider.LazyGetService<IRedisRepository>();
                //foreach (var item in orgs)
                //{
                //    var rs = await dataverseService.GetStaffDepartmentRelations(item.Id.ToString());
                //    relations.AddRange(rs);
                //}
                //userIds = relations.Select(a => a.UserId);

                //if (!userIds.Any())
                //    return new PagedResultDto<GetActiveUserListResponseDto>(0, null);
            }
            else
            {
                orgs = await LazyServiceProvider.LazyGetService<IDataverseService>().GetOrganizations();
            }
            //IsActive:禁用false/启用true；jobStatus:在职1/离职0
            var queryUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser, Guid>>().GetQueryableAsync();
            var query = queryUser.Where(a => !string.IsNullOrEmpty(EF.Property<string>(a, EntityConsts.IdentityUser.StaffCode)))
                .WhereIf((userIds != null && userIds.Any()), a => userIds.Contains(a.Id))
                .WhereIf(!string.IsNullOrEmpty(request.StaffCode), a => EF.Property<string>(a, EntityConsts.IdentityUser.StaffCode).Contains(request.StaffCode))
                .WhereIf(!string.IsNullOrEmpty(request.Name), a => a.Name.Contains(request.Name))
                .WhereIf(!string.IsNullOrEmpty(request.Email), a => a.Email.Contains(request.Email))
                .WhereIf(!string.IsNullOrEmpty(request.AdAccount), a => a.UserName.Contains(request.AdAccount))
                .Select(a => new GetActiveUserListResponseDto
                {
                    Id = a.Id,
                    Name = a.Name,
                    StaffCode = EF.Property<string>(a, EntityConsts.IdentityUser.StaffCode),
                    Email = a.Email,
                    OrganizationName = EF.Property<string>(a, EntityConsts.IdentityUser.DepartmentId),
                    AdAccount = a.UserName
                });

            var count = query.Count();
            var datas = query.Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToArray();
            foreach (var item in datas)
            {
                var departments = relations.Where(a => a.UserId == item.Id)
                    .Join(orgs, a => a.DepartmentId, a => a.Id, (a, b) => b);

                item.OrganizationName = string.Join(",", departments.Select(a => a.DepartmentName));
            }

            var result = new PagedResultDto<GetActiveUserListResponseDto>(count, datas);
            return result;
        }

        /// <summary>
        /// 导出所有内部可用用户列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<Stream> ExportAllInternalActiveUsersAsync(GetActiveUserListRequestDto request)
        {
            request.PageIndex = 1;
            request.PageSize = int.MaxValue;
            var result = await GetAllInternalActiveUsersAsync(request);
            var datas = ObjectMapper.Map<List<GetActiveUserListResponseDto>, List<ActiveUserExcelResponseDto>>(result.Items.ToList());
            MemoryStream stream = new();
            var config = new OpenXmlConfiguration()
            {
                TableStyles = TableStyles.None,
                AutoFilter = false,

            };
            stream.SaveAs(datas, true, "Sheet1", configuration: config);
            stream.Seek(0, SeekOrigin.Begin);

            return stream;
        }

        public async Task<IEnumerable<InvoiceTaxRateDto>> GetInvoiceTypeTaxRateAsync()
        {
            var result = new List<InvoiceTaxRateDto>();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var invoiceDictionarys = await dataverseService.GetDictionariesAsync(DictionaryType.InvoiceType);
            var taxRateDictionarys = await dataverseService.GetDictionariesAsync(DictionaryType.TaxRate);
            var invoiceTaxMapp = await dataverseService.GetInvoiceTaxRateMappAsync();
            foreach (var item in invoiceDictionarys)
            {
                var invoice = new InvoiceTaxRateDto();
                invoice.Id = item.Id;
                invoice.Name = item.Name;
                invoice.Code = item.Code;
                invoice.Type = item.Type;
                var taxRates = invoiceTaxMapp.Where(a => a.InvoiceTypeId == item.Id).Select(a => a.TaxRateId).ToList();
                invoice.Children = taxRateDictionarys.Where(a => taxRates.Contains(a.Id)).ToList();
                result.Add(invoice);
            }
            return result;
        }

        /// <summary>
        /// 获取紧急付款类型配置
        /// </summary>
        /// <returns></returns>
        public async Task<IEnumerable<DictionaryDto>> GetPturtTypeDictionaryAsync()
        {
            var result = new List<DictionaryDto>();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var pturtTypes = await dataverseService.GetPturtTypeConfigDtoAsync();
            var pturtTypeCaches = pturtTypes.OrderBy(x => x.SortNo).ThenBy(x => x.Name);
            foreach (var item in pturtTypeCaches)
            {
                var pturtType = new DictionaryDto();
                pturtType.Id = item.Id;
                pturtType.Name = item.Name;
                pturtType.Code = item.Code;
                pturtType.Type = item.IsAttachmentRequired ? "IsAttachmentRequired" : null;
                pturtType.IsAttachmentRequired = item.IsAttachmentRequired;
                result.Add(pturtType);
            }
            return result;
        }
        /// <summary>
        /// 获取员工数据
        /// </summary>
        /// <returns></returns>
        public async Task<IList<GetUserDropdownListResponseDto>> GetUserDropDownLsitAsync()
        {
            var queryUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser, Guid>>().GetQueryableAsync();
            var data = queryUser.Select(x => new GetUserDropdownListResponseDto { Id = x.Id, Name = x.Name, }).ToArray();
            return data;
        }

        /// <summary>
        /// 识别身份证信息
        /// </summary>
        /// <param name="blobName"></param>
        /// <returns></returns>
        public async Task<MessageResult> CognizeIdCard(string blobName)
        {
            IEnumerable<string> cognizeResults;
            var stream = await LazyServiceProvider.LazyGetService<IBlobService>().DownloadStream(blobName);
            if (stream == null)
                return MessageResult.FailureResult("未找到要识别的图片");

            cognizeResults = await LazyServiceProvider.LazyGetService<ICognitiveService>().GetCognizeResultAsync(stream.GetBuffer());

            var line = cognizeResults.Select(a => a.Replace(" ", "")).JoinAsString("");
            var result = line.Split(["姓名", "性别", "民族", "出生", "住址", "公民身份号码"], StringSplitOptions.RemoveEmptyEntries).ToArray();
            if (result.Length != 6)
                return MessageResult.FailureResult("请上传正确的身份证照片");

            var data = new CognitiveIdCardResponseDto
            {
                Name = result[0],
                Gender = result[1],
                Address = result[4],
                Code = result[5]
            };

            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            string regex = "(?<province>[^省]+自治区|.*?省|.*?行政区|.*?市)?(?<city>[^市]+自治州|.*?地区|.*?行政单位|.+盟|市辖区|.*?市|.*?县)?";
            var match = Regex.Match(data.Address, regex);
            //province
            if (!string.IsNullOrEmpty(match.Groups["province"].Value))
            {
                data.ProvinceName = match.Groups["province"].Value;
                var provinces = await dataverseService.GetAllProvince(data.ProvinceName);
                data.ProvinceCode = provinces.FirstOrDefault()?.Code;
            }
            //city
            if (!string.IsNullOrEmpty(match.Groups["city"].Value))
            {
                data.CityName = match.Groups["city"].Value;
                var provinces = await dataverseService.GetAllCity(data.CityName);
                data.CityCode = provinces.FirstOrDefault()?.Code;
            }

            //直辖市处理
            string[] municipalitys = ["北京市", "上海市", "天津市", "重庆市"];
            if (string.IsNullOrWhiteSpace(data.CityName) && municipalitys.Contains(data.ProvinceName))
            {
                data.CityName = data.ProvinceName;
                var provinces = await dataverseService.GetAllCity(data.CityName);
                data.CityCode = provinces.FirstOrDefault()?.Code;
            }

            return MessageResult.SuccessResult(data);
        }
        /// <summary>
        /// 生成Pdf
        /// </summary>
        /// <param name="values"></param>
        /// <param name="TemplateWordPath">word模版路径,只能小于3页</param>
        /// <param name="tableDics">列表模板</param>
        /// <param name="TemplatePDFPath">pdf模版路径</param>
        /// <returns></returns>
        public Task<Stream> GeneratePDFAsync(Dictionary<string, object> values, string TemplateWordPath, Dictionary<string, List<Dictionary<string, object>>> tableDics = null,
             string TemplatePDFPath = null)
        {
            //var path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Templates");
            /* var pdfstream = Task.Run(() =>
             {
                 //
                 Stream pdfStream = new MemoryStream();
                 var path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Templates");
                 DirectoryInfo di = new DirectoryInfo(path);
                 var fileInfos = di.GetFiles(TemplateWordPath, SearchOption.AllDirectories);
                 var wordBytes = fileInfos[0].OpenRead();
                 //根据模版生成word
                 WordTemplateReplace(pdfStream, wordBytes, values, tableDics);
                 //如果pdf模版为空,直接返回
                 if (string.IsNullOrWhiteSpace(TemplatePDFPath))
                 {
                     return pdfStream;
                 }
                 PdfDocument pdf = new PdfDocument();
                 //打开pdf模版
                 var pdfTemplateStream = di.GetFiles(TemplatePDFPath, SearchOption.AllDirectories)[0].OpenRead();
                 //Stream pdfTemplateStream = new FileStream(path + TemplatePDFPath, FileMode.Open, FileAccess.Read);

                 //合并pdf
                 PdfDocumentBase pdfmerge = PdfDocument.MergeFiles([pdfStream, pdfTemplateStream]);
                 Stream pdfMegerStream = new MemoryStream();
                 //保存pdf文件流
                 pdfmerge.Save(pdfMegerStream, Spire.Pdf.FileFormat.PDF);
                 pdfStream.Dispose();
                 pdfTemplateStream.Dispose();
                 return pdfMegerStream;

             }
             );
             return pdfstream;*/
            return null;

        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="newWordstream"></param>
        /// <param name="tempPath"></param>
        /// <param name="textDic"></param>
        /// <param name="tableDics"></param>
        /* private void WordTemplateReplace(Stream newWordStream, Stream tempStream, Dictionary<string, object> textDic, Dictionary<string, List<Dictionary<string, object>>> tableDics = null)
         {
             tempStream.Position = 0;
             var doc = DocX.Load(tempStream);  // 加载 Word 模板文件
             #region 字段替换文字
             foreach (var paragraph in doc.Paragraphs)   // 遍历当前 Word 文件中的所有（段落）段
             {
                 foreach (var texts in textDic)
                 {
                     try
                     {
                         StringReplaceTextOptions stringReplaceText = new();
                         stringReplaceText.SearchValue = "{{" + texts.Key + "}}";
                         stringReplaceText.NewValue = texts.Value?.ToString() ?? "";
                         paragraph.ReplaceText(stringReplaceText);  // 替换段落中的文字
                     }
                     catch (Exception ex)
                     {
                         // 不处理
                         continue;
                     }
                 }
             }

             #endregion
             #region 替换表格数据

             if (tableDics != null)
             {
                 foreach (var tableDic in tableDics)
                 {
                     Table targetTable = null!;
                     Row targetRow = null!;
                     int matchRowIndex = -1;
                     // 查找目标表格
                     foreach (var table in doc.Tables)   // 遍历当前 Word 文件中的所有表格
                     {
                         int rowIndex = 1;
                         foreach (var row in table.Rows) // 遍历表格中的每一行
                         {
                             foreach (var cell in row.Cells)     //遍历每一行中的每一列
                             {
                                 foreach (var paragraph in cell.Paragraphs)  // 遍历当前表格里的所有（段落）段
                                 {
                                     if (paragraph.Text.Contains("{{" + tableDic.Key + "."))
                                     {
                                         targetTable = table;
                                         targetRow = row;
                                         matchRowIndex = rowIndex;
                                         goto insertRow;
                                     }
                                 }
                             }
                             rowIndex++;
                         }
                     }

                 insertRow:
                     if (targetTable != null)
                     {
                         var inserIndex = matchRowIndex;
                         foreach (var listdic in tableDic.Value)
                         {
                             targetTable.InsertRow(targetRow, inserIndex, true);
                             foreach (var cell in targetTable.Rows[inserIndex].Cells)
                             {
                                 foreach (var paragraph in cell.Paragraphs)  // 遍历当前表格里的所有（段落）段
                                 {
                                     foreach (var texts in listdic)
                                     {
                                         try
                                         {
                                             var key = "{{" + tableDic.Key + "." + texts.Key + "}}";
                                             StringReplaceTextOptions stringReplaceText = new StringReplaceTextOptions();
                                             stringReplaceText.SearchValue = key;
                                             stringReplaceText.NewValue = texts.Value?.ToString() ?? "";
                                             paragraph.ReplaceText(stringReplaceText);
                                         }
                                         catch (Exception)
                                         {

                                             continue;
                                         }

                                     }
                                 }
                             }
                             inserIndex++;
                         }
                         targetTable.RemoveRow(matchRowIndex - 1);
                     }
                 }
             }
             #endregion
             doc.SaveAs(newWordStream);
         }
         private void WordTemplateReplace(Stream newWordStream, Stream tempStream, Dictionary<string, object> values, Dictionary<string, List<Dictionary<string, object>>> tableDics = null)
         {
             Spire.Doc.Document document = new Spire.Doc.Document();
             document.LoadFromStream(tempStream, Spire.Doc.FileFormat.Docx);

             foreach (var value in values)
             {
                 var key = "{{" + value.Key + "}}";
                 document.Replace(key, value.Value?.ToString() ?? "", false, true);
             }
             var sec = document.Sections[0];
             foreach (var item in tableDics)
             {
                 Spire.Doc.Table targetTable = null;
                 TableRow targetRow = null;
                 int matchRowIndex = -1;
                 for (int i = 0; i < sec.Tables.Count; i++)
                 {
                     var rows = sec.Tables[i].Rows;
                     for (int j = 0; j < rows.Count; j++)
                     {
                         var row = rows[j];
                         if (row.Document.GetText().Contains("{{" + item.Key + "."))
                         {
                             targetRow = row;
                             targetTable = sec.Tables[i] as Spire.Doc.Table;
                             matchRowIndex = j;
                             goto insertRow;
                         }
                     }
                 }
             insertRow:
                 if (targetTable != null)
                 {
                     var inserIndex = matchRowIndex;
                     foreach (var listdic in item.Value)
                     {
                         targetTable.Rows.Insert(inserIndex, targetRow);
                         foreach (var dic in listdic)
                         {
                             targetTable.Rows[inserIndex + 1].Document.Replace("{{" + item.Key + "." + dic.Key + "}}", dic.Value?.ToString() ?? "", false, true);
                         }
                         inserIndex++;
                     }
                     targetTable.Rows.Remove(targetRow);
                 }
             }
             string wwwrootPath = _env.WebRootPath;
             var fontPath = Path.Combine(wwwrootPath, "fonts");
             document.SetCustomFontsFolders(fontPath);
             document.SaveToStream(newWordStream, Spire.Doc.FileFormat.PDF);
             //document.SaveToFile("C:\\Users\\<USER>\\Desktop\\test\\aaa.docx", Spire.Doc.FileFormat.Docx);
         }*/
        #region ****
        //private void WordTemplateReplace()
        //{
        //    PdfDocument pdf = new PdfDocument();
        //    var page = pdf.Pages[0];
        //    PdfTextReplacer textReplacer = new(page);
        //    PdfTextReplaceOptions options = new PdfTextReplaceOptions();
        //    options.ReplaceType = PdfTextReplaceOptions.ReplaceActionType.WholeWord;
        //    options.ReplaceType = PdfTextReplaceOptions.ReplaceActionType.AutofitWidth;
        //    textReplacer.Options = options;
        //    textReplacer.ReplaceText("a","b");

        //    PdfTableExtractor tableExtractor = new(page);
        //    page.Document.
        //} 
        #endregion
        /// <summary>
        /// 获取所有费用性质
        /// </summary>
        public async Task<MessageResult> GetAllCostNatureAsync()
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var datas = await dataverseService.GetCostNatureAsync();
            var CostNatures = datas.Select(s => new DropDownListDto { Id = s.Id, Name = s.Name });
            return MessageResult.SuccessResult(CostNatures);
        }
        //}


        #region Consent status

        /// <summary>
        /// 获取Consent最新版本协议信息
        /// </summary>
        /// <returns></returns>
        public async Task<IEnumerable<ConsentUrlResponseDto>> GetLeatestConsentInfosAsync()
        {
            var cfg = LazyServiceProvider.LazyGetService<Microsoft.Extensions.Configuration.IConfiguration>();
            var codes = cfg["Consent:ConsentCode"]?.Split(",", StringSplitOptions.RemoveEmptyEntries).ToList();
            var consentResponse = await LazyServiceProvider.LazyGetService<IConsentService>().GetConsentInfoAsync(codes);

            return consentResponse;
        }

        /// <summary>
        /// 获取Consent的签署信息
        /// </summary>
        /// <param name="userIds"></param>
        /// <returns></returns>
        public async Task<IEnumerable<(Guid UserId, string ConsentCode, string ConsentVersion)>> GetConsentSignedInfosAsync(IEnumerable<Guid> userIds)
        {
            // 获取签署内容
            var uids = userIds.Distinct().Select(a => a.ToString()).ToArray();
            var query = await LazyServiceProvider.LazyGetService<IConsentSignedRepository>().GetQueryableAsync();
            var consentSigneds = query.Where(a => uids.Contains(a.AppUserId))
                .Select(a => new { UserId = a.AppUserId, a.ConsentCode, a.ConsentVersion }).ToArray();

            return consentSigneds.Select(a => (Guid.Parse(a.UserId), a.ConsentCode, a.ConsentVersion));
        }

        /// <summary>
        /// 判断User是否签署了最新协议
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="consentResponses"></param>
        /// <param name="consentSigneds"></param>
        /// <returns></returns>
        public bool IsSignLeatestVersion(Guid userId, IEnumerable<ConsentUrlResponseDto> consentResponses, IEnumerable<(Guid UserId, string ConsentCode, string ConsentVersion)> consentSigneds)
        {
            var signStatus = consentResponses
                .GroupJoin(consentSigneds.Where(a => a.UserId == userId), a => $"{a.Data?.ConsentCode}_{a.Data?.Version}", a => $"{a.ConsentCode}_{a.ConsentVersion}", (a, b) => new { Consent = a, SignStatus = b.Where(a1 => !string.IsNullOrEmpty(a1.ConsentCode)).Select(a1 => new { a1.ConsentCode, a1.ConsentVersion }) })
                .SelectMany(a => a.SignStatus.DefaultIfEmpty(), (a, b) => new { a.Consent, SignStatus = b });

            return signStatus.All(a => a.SignStatus != null);
        }

        #endregion

        /// <summary>
        /// 新建操作日志
        /// 系统可以参考这个类：ClientIdScopeConsts
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> CreateOperationLog(List<SetOperationLogRequestDto> request)
        {
            var logRepository = LazyServiceProvider.LazyGetService<IOperationLogRepository>();

            try
            {
                var logs = ObjectMapper.Map<List<SetOperationLogRequestDto>, List<OperationLog>>(request);

                await logRepository.InsertManyAsync(logs, true);

                return MessageResult.SuccessResult();
            }
            catch (Exception ex)
            {
                return MessageResult.FailureResult(ex.Message);
            }
        }

        public SetOperationLogRequestDto InitOperationLog(string system, string api, string content)
        {
            SetOperationLogRequestDto log = new SetOperationLogRequestDto();
            log.System = system;
            log.Api = api;
            log.Content = content;
            log.StartTime = DateTime.Now;
            return log;
        }

        //public async Task LogResponse(SetOperationLogRequestDto log, string response, bool success = true)
        //{
        //    log.Response = response;
        //    log.EndTime = DateTime.Now;
        //    log.IsSuccess = success;

        //   await CreateOperationLog(new List<SetOperationLogRequestDto> { log });
        //}

        public void LogResponse(SetOperationLogRequestDto log, string response, bool success = true)
        {
            log.Response = response;
            log.EndTime = DateTime.Now;
            log.IsSuccess = success;
            CreateOperationLog(new List<SetOperationLogRequestDto> { log }).Wait();
        }

        public string GetPaymentTermName(string company, string paymentTermCode)
        {
            if (string.IsNullOrEmpty(paymentTermCode))
            {
                return "";
            }
            //部分payment_term特殊处理下,数据库中[79_45_45]格式
            if (paymentTermCode.Contains("_"))
            {
                var pTs = paymentTermCode.Split("_");
                if (pTs.Length > 2)
                {
                    paymentTermCode = pTs[1];
                }
            }
            var paymentTerms = LazyServiceProvider.LazyGetService<IBpcsAvtRepository>().GetQueryableAsync().GetAwaiter().GetResult();
            var paymentTerm = paymentTerms.FirstOrDefault(p => p.Vcmpy.ToString() == company && p.Vterm == paymentTermCode);
            if (paymentTerm == null)
            {
                return "";
            }
            return paymentTerm.Vterm + "_" + paymentTerm.Vtmddy + "天";
        }

        /// <summary>
        /// 申请人相关的操作仅限申请人/代理人/转办人可以操作
        /// </summary>
        /// <param name="businessFormId">业务Id</param>
        /// <returns></returns>
        public async Task<bool> ValidateApplicantRelatedOperationAsync(Guid businessFormId)
        {
            var queryVendor = await LazyServiceProvider.LazyGetService<IVendorApplicationRepository>().GetQueryableAsync();
            var queryPr = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var queryPo = await LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>().GetQueryableAsync();
            var queryBd = await LazyServiceProvider.LazyGetService<IPurBDApplicationRepository>().GetQueryableAsync();
            var queryBw = await LazyServiceProvider.LazyGetService<IPurBWApplicationRepository>().GetQueryableAsync();
            var queryGr = await LazyServiceProvider.LazyGetService<IPurGRApplicationRepository>().GetQueryableAsync();
            var queryPa = await LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>().GetQueryableAsync();
            var querySa = await LazyServiceProvider.LazyGetService<IOECSpeakerAuthApplyRepository>().GetQueryableAsync();
            var querySTickte = await LazyServiceProvider.LazyGetService<ISTicketApplicationRepository>().GetQueryableAsync();
            var queryFoc = await LazyServiceProvider.LazyGetService<IFocApplicationRepository>().GetQueryableAsync();
            var queryRe = await LazyServiceProvider.LazyGetService<IReturnApplicationReponsitory>().GetQueryableAsync();
            var data = queryVendor.Where(a => a.Id == businessFormId).Select(a => new { a.ApplyUserId, a.TransfereeId, BusinessType = ResignationTransfer.TaskFormCategory.VendorApplication })
            .Concat(queryPr.Where(a => a.Id == businessFormId).Select(a => new { a.ApplyUserId, a.TransfereeId, BusinessType = ResignationTransfer.TaskFormCategory.PurchaseRequestApplication }))
            .Concat(queryPo.Where(a => a.Id == businessFormId).Select(a => new { a.ApplyUserId, a.TransfereeId, BusinessType = ResignationTransfer.TaskFormCategory.PurhcaseOrderApplication }))
            .Concat(queryBd.Where(a => a.Id == businessFormId).Select(a => new { a.ApplyUserId, a.TransfereeId, BusinessType = ResignationTransfer.TaskFormCategory.BiddingApplication }))
            .Concat(queryBw.Where(a => a.Id == businessFormId).Select(a => new { a.ApplyUserId, a.TransfereeId, BusinessType = ResignationTransfer.TaskFormCategory.BiddingWaiverApplication }))
            .Concat(queryGr.Where(a => a.Id == businessFormId).Select(a => new { a.ApplyUserId, a.TransfereeId, BusinessType = ResignationTransfer.TaskFormCategory.GoodsReceiptApplication }))
            .Concat(queryPa.Where(a => a.Id == businessFormId).Select(a => new { a.ApplyUserId, a.TransfereeId, BusinessType = ResignationTransfer.TaskFormCategory.PaymentApplication }))
            .Concat(querySa.Where(a => a.Id == businessFormId).Select(a => new { a.ApplyUserId, a.TransfereeId, BusinessType = ResignationTransfer.TaskFormCategory.OECSpeakerAuthApplication }))
            .Concat(querySTickte.Where(a => a.Id == businessFormId).Select(a => new { a.ApplyUserId, a.TransfereeId, BusinessType = ResignationTransfer.TaskFormCategory.STicketRequestApplication }))
            .Concat(queryFoc.Where(a => a.Id == businessFormId).Select(a => new { a.ApplyUserId, a.TransfereeId, BusinessType = ResignationTransfer.TaskFormCategory.FOCRequestApplication }))
            .Concat(queryRe.Where(a => a.Id == businessFormId).Select(a => new { a.ApplyUserId, a.TransfereeId, BusinessType = ResignationTransfer.TaskFormCategory.ReturnAndExchangeRequest }))
            .FirstOrDefault();

            if (data == null)
                return false;

            //获取代理信息
            var agents = await LazyServiceProvider.LazyGetService<IAgencyService>().GetOriginalOperators(new GetAgentOperatorsRequestDto { BusinessTypeCategory = data.BusinessType });
            //如果存在，代表具有访问的权利
            if (agents.Any(a => a.Key == data.ApplyUserId))
                return true;

            //没有权利再以转办人的身份去检测
            if (data.TransfereeId.HasValue)
            {
                agents = await LazyServiceProvider.LazyGetService<IAgencyService>().GetOriginalOperators(new GetAgentOperatorsRequestDto { BusinessTypeCategory = data.BusinessType });
                return agents.Any(a => a.Key == data.TransfereeId);
            }

            return false;
        }


        public async Task<MessageResult> GetComByOrgAsync(Guid OrgId)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();

            var orgs = await dataverseService.GetOrganizations(OrgId.ToString());

            if (orgs == null || orgs.Count < 1)
                return MessageResult.FailureResult("未找到相关组织");

            var sourceOrg = orgs.First();
            var branckOfficeId = Guid.Empty;
            if (sourceOrg.OrganizationType == DataverseEnums.Organization.OrganizationType.Affiliates)
                branckOfficeId = sourceOrg.Id;
            else
            {
                var parentOrgs = await GetAllParentOrgs(sourceOrg.Id);
                var parentBranchOffice = parentOrgs?.FirstOrDefault(x => x.OrganizationType == DataverseEnums.Organization.OrganizationType.Affiliates);
                if (parentBranchOffice != null)
                    branckOfficeId = parentBranchOffice.Id;
            }

            if (branckOfficeId == Guid.Empty)
                return MessageResult.FailureResult("未找到相关的分公司");


            var comOrg = await dataverseService.GetCompanyAndOrgRelationAsync(stateCode: null);
            comOrg = comOrg.ToList();

            if (comOrg == null || !comOrg.Any())
                return MessageResult.FailureResult("找不到公司与组织的关系的缓存");

            var relations = comOrg.Where(x => x.OrgId.HasValue && x.OrgId == branckOfficeId).ToList();

            if (relations.Count == 0)
                return MessageResult.FailureResult("找不到对应的公司与组织的关系数据");

            var comIds = relations.Where(x => x.CompanyId.HasValue).Select(x => x.CompanyId).Distinct();

            var comCurrency = await dataverseService.GetCompanyCurrencyInfoAsync(stateCode: null);

            if (!comCurrency.Any())
                return MessageResult.FailureResult("获取公司币种信息为空");

            var data = comCurrency.Where(x => comIds.Contains(x.Id));

            return MessageResult.SuccessResult(data.OrderByDescending(x => x.CompanyName == "TRADING"));
        }


        public async Task<IEnumerable<Guid>> GetAllChildrenOrgs(Guid userId)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var orgs = await dataverseService.GetOrganizations();

            var epoLeaderOrgs = orgs.Where(x => x.EpoLeaderId.HasValue && x.EpoLeaderId == userId);
            var relationOrgs = await dataverseService.GetStaffDepartmentRelations(userId.ToString(), null);
            //var relatedOrgs = await GetOrganizationDept(userId);
            //var relatedOrgIds = relatedOrgs.Select(x => x.Key);
            var sourceOrgIds = epoLeaderOrgs.Select(x => x.Id).Concat(relationOrgs.Select(x => x.DepartmentId));
            return sourceOrgIds;
            /*
            var epoLeaders = epoLeaderOrgs.ToList();
            var relations = relationOrgs.ToList();

            var orgList = sourceOrgIds.ToList();
            List<DepartmentDto> datas = [];
            foreach (var id in sourceOrgIds)
            {
                var org = orgs.FirstOrDefault(x => x.Id == id);
                var childs = await GetChildrenOrgs(org, orgs);
                datas.AddRange(childs);
            }
            return datas.Select(x=>x.Id).Distinct();
            */
        }

        /// <summary>
        /// 获取openiddict application的扩展信息
        /// </summary>
        /// <param name="clientId"></param>
        /// <returns></returns>
        public async Task<OpenIddictApplicationExtraProperty> GetOpenIddictApplicationExtraPropertyAsync(string clientId)
        {
            var openidAppRepository = LazyServiceProvider.LazyGetRequiredService<IOpenIddictApplicationRepository>();
            var openidApplication = await openidAppRepository.FindByClientIdAsync(clientId);
            var jsonDoc = JsonDocument.Parse(openidApplication.Properties);
            var jsonProperties = jsonDoc.RootElement.GetString(nameof(OpenIddictApplicationExtraProperty));
            var property = JsonSerializer.Deserialize<OpenIddictApplicationExtraProperty>(jsonProperties);

            return property;
        }


        public async Task<bool> IsSubOrganization(Guid theOrg, DataverseEnums.Organization.OrganizationType targetOrgType = DataverseEnums.Organization.OrganizationType.Affiliates, string targetOrgName = AffiliateNameConst.JXFactory)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var orgs = await dataverseService.GetOrganizations(pattern: theOrg.ToString(), stateCode: null);
            foreach (var org in orgs)
            {
                if (org.DepartmentName == targetOrgName && org.OrganizationType == targetOrgType)
                    return true;
                else if (org.OrganizationType == targetOrgType)
                    return false;
                else
                {
                    var parentOrgs = await dataverseService.GetOrganizations(org.ParentDepartment?.ToString());
                    foreach (var parentOrg in parentOrgs)
                    {
                        return await IsSubOrganization(parentOrg.Id, targetOrgType, targetOrgName);
                    }
                    return false;
                }
            }
            return false;
        }

        /// <summary>
        /// 清理系统日志
        /// </summary>
        /// <returns></returns>
        public async Task ClearSystemLogAsync()
        {
            var now = DateTime.Now;
            //Security log保留一年
            await LazyServiceProvider.LazyGetService<IRepository<IdentitySecurityLog>>().DeleteDirectAsync(a => a.CreationTime < now.AddYears(-1));

            var dbContext = await LazyServiceProvider.LazyGetService<IOperationLogRepository>().GetDbContextAsync();
            dbContext.Database.SetCommandTimeout(60 * 60);
            //Audit log保留180天
            var pre180Days = now.AddDays(-180).ToString("yyyy-MM-dd HH:mm:ss");
            await dbContext.Database.ExecuteSqlRawAsync($"DELETE AbpEntityPropertyChanges WHERE EXISTS(SELECT 1 FROM AbpEntityChanges a1 WHERE a1.ChangeTime<@p0 AND a1.Id=EntityChangeId)", pre180Days);
            await dbContext.Database.ExecuteSqlRawAsync($"DELETE AbpEntityChanges WHERE ChangeTime<@p0", pre180Days);

            await dbContext.Database.ExecuteSqlRawAsync($"DELETE AbpAuditLogActions WHERE EXISTS(SELECT 1 FROM AbpAuditLogs a1 WHERE a1.ExecutionTime<@p0 AND a1.Id=AuditLogId)", pre180Days);
            await dbContext.Database.ExecuteSqlRawAsync($"DELETE AbpAuditLogs WHERE ExecutionTime<@p0", pre180Days);

            //Integration log保留一年
            await LazyServiceProvider.LazyGetService<IOperationLogRepository>().DeleteDirectAsync(a => a.CreationTime < now.AddYears(-1));
            await LazyServiceProvider.LazyGetService<Entities.Integration.Veeva.IVeevaDCRLogRepository>().DeleteDirectAsync(a => a.CreationTime < now.AddYears(-1));

            //其他log保留90天
            await LazyServiceProvider.LazyGetService<Entities.Common.ScheduleJob.IScheduleJobLogRepository>().DeleteDirectAsync(a => a.StartTime < now.AddDays(-90));
            await dbContext.Database.ExecuteSqlRawAsync($"DELETE DbLogs where TimeStamp<@p0", now.AddDays(-90).ToString("yyyy-MM-dd HH:mm:ss"));
        }
        /// <summary>
        /// 获取授权bu下所有子部门
        /// </summary>
        /// <returns></returns>
        public async Task<List<KeyValuePair<IEnumerable<string>, IEnumerable<Guid>>>> GetAuthorizedAllDepts(List<(Guid, string)> users)
        {
            List<KeyValuePair<IEnumerable<string>, IEnumerable<Guid>>> a = [];
            if (users.Count == 0 || users == null) return a;
            var userIds = users.Select(s => s.Item1).ToList();
            var queryAuthorizedBudgetBu = await LazyServiceProvider.LazyGetService<IBdAuthorizedBudgetBuReadonlyRepository>().GetQueryableAsync();
            var buUserIds = queryAuthorizedBudgetBu.Where(x => userIds.Contains(x.UserId))
                .Select(s => new { s.UserId, s.BuId }).ToList();
            var staffOrgItems = buUserIds.Join(users, a => a.UserId, b => b.Item1, (a, b) => new { a.BuId, emial = b.Item2 })
                .GroupBy(g => g.BuId, (k, v) => new { deptIds = k, emails = v.Select(s => s.emial).Distinct() });
            if (staffOrgItems == null)
                return a;
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var orgs = await dataverseService.GetOrganizations();

            foreach (var item in staffOrgItems)
            {
                var allDeptIds = await GetChildrenOrgs([item.deptIds], orgs);
                a.Add(new KeyValuePair<IEnumerable<string>, IEnumerable<Guid>>(item.emails, allDeptIds.Select(s => s.Id)));
            }
            return a;
        }
    }
}
