﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <RootNamespace>Abbott.SpeakerPortal</RootNamespace>
    <PreserveCompilationReferences>true</PreserveCompilationReferences>
	  <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <UserSecretsId>Abbott.SpeakerPortal-4681b4fd-151f-4221-84a4-929d86723e4c</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..\..</DockerfileContext>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Identity" Version="1.13.2" />
    <PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.6.0" />
    <PackageReference Include="Hangfire.MemoryStorage" Version="1.8.1.1" />
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.22.0" />
    <PackageReference Include="Microsoft.Identity.Client" Version="4.69.1" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.19.4" />
    <PackageReference Include="Npgsql" Version="8.0.6" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.Async" Version="1.5.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="7.5.0" />
    <PackageReference Include="Serilog.Sinks.MSSqlServer" Version="6.6.0" />
    <PackageReference Include="System.IO.Packaging" Version="9.0.2" />
    <PackageReference Include="Volo.Abp.AspNetCore.MultiTenancy" Version="8.0.0" />
    <PackageReference Include="Volo.Abp.Autofac" Version="8.0.0" />
    <PackageReference Include="Volo.Abp.AspNetCore.Serilog" Version="8.0.0" />
    <PackageReference Include="Volo.Abp.Swashbuckle" Version="8.0.0" />
    <PackageReference Include="Volo.Abp.Account.Web.OpenIddict" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Abbott.SpeakerPortal.Application\Abbott.SpeakerPortal.Application.csproj" />
    <ProjectReference Include="..\Abbott.SpeakerPortal.EntityFrameworkCore\Abbott.SpeakerPortal.EntityFrameworkCore.csproj" />
    <ProjectReference Include="..\Abbott.SpeakerPortal.HttpApi\Abbott.SpeakerPortal.HttpApi.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Logs\**" />
    <Content Remove="Logs\**" />
    <EmbeddedResource Remove="Logs\**" />
    <None Remove="Logs\**" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="ConfigKeyVaultHelper.cs" />
  </ItemGroup>

  <ItemGroup>
    <_ContentIncludedByDefault Remove="wwwroot\Templates\Email\ReturnNoticeToApplicant.html" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Certificate.pfx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="openiddict.pfx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="[7643] - GIS - China Speaker Portal Hub - Dev.pfx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="[7643] - GIS - China Speaker Portal Hub - Prod.pfx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="[7643] - GIS - China Speaker Portal Hub - Stage.pfx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="wwwroot\images\logo\Abbott\" />
    <Folder Include="wwwroot\Templates\Veeva\" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="wwwroot\1H02ly0Ew5.txt">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\cdE7N27f76.txt">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Update="wwwroot\Templates\Email\VeevaTimeoutEmail.html">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Update="wwwroot\Templates\POTemplateTK.pdf">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>
