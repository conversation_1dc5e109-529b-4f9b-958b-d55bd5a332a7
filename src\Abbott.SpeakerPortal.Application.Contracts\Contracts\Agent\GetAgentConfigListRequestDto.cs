﻿using Abbott.SpeakerPortal.Dtos;
using Abbott.SpeakerPortal.Enums;

using System;
using System.Collections.Generic;
using System.Text;
using System.Text.Json.Serialization;

namespace Abbott.SpeakerPortal.Contracts.Agent
{
    public class GetAgentConfigListRequestDto : PagedDto
    {
        //public WorkflowTypeName? WorkflowType { get; set; }
        /// <summary>
        /// 业务类型Id，可空，空则表示“全部”
        /// </summary>
        //public Guid? BusinessTypeId { get; set; }
        /// <summary>
        /// 业务类型名称,可空，空则表示“全部”
        /// </summary>
        public ResignationTransfer.TaskFormCategory? BusinessType { get; set; }
        /// <summary>
        /// 业务类型名称
        /// </summary>
        //public string BusinessTypeName { get; set; }
        public bool? Status { get; set; }

        /// <summary>
        /// 是否是管理员
        /// </summary>
        [JsonIgnore]
        public bool IsAdmin { get; set; }=false;
        /// <summary>
        /// 代理人ID
        /// </summary>
        public Guid? Agent { get; set; }
        /// <summary>
        /// 原操作人ID
        /// </summary>
        public Guid? OriginalOperator { get; set; }

    }
}
