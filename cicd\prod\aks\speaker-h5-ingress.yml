apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: speaker-h5-ingress
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "20m"

spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - speaker-portal-h5.abbott.com.cn
    secretName: tls-speaker-portal-h5-secret
  rules:
  - host: speaker-portal-h5.abbott.com.cn
    http:
      paths:
      - path: /(.*)/mobile(.*)
        pathType: Prefix
        backend:
          service:
            name: corp-ca191-speakerh5-pc-p
            port:
              number: 80
      - path: /crossBuLogin
        pathType: Prefix
        backend:
          service:
            name: corp-ca191-speakerh5-pc-p
            port:
              number: 80
      - path: /login
        pathType: Prefix
        backend:
          service:
            name: corp-ca191-speakerh5-pc-p
            port:
              number: 80
      - path: /dashboard
        pathType: Prefix
        backend:
          service:
            name: corp-ca191-speakerh5-pc-p
            port:
              number: 80
      - path: /assets(.*)
        pathType: Prefix
        backend:
          service:
            name: corp-ca191-speakerh5-pc-p
            port:
              number: 80
      - path: /WW_verify_hXpspD3AQMjrL6vY.txt
        pathType: Prefix
        backend:
          service:
            name: corp-ca191-speakerh5-pc-p
            port:
              number: 80
      - path: /WW_verify_I2BvyyUhpDFnMzql.txt
        pathType: Prefix
        backend:
          service:
            name: corp-ca191-speakerh5-pc-p
            port:
              number: 80
      - path: /WW_verify_M5Uf0edsqtMRrvz0.txt
        pathType: Prefix
        backend:
          service:
            name: corp-ca191-speakerh5-pc-p
            port:
              number: 80