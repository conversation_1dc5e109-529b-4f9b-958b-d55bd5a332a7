apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: speaker-ingress
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "20m"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - speaker-portal-api.oneabbott.com
    secretName: tls-speaker-portal-api-secret
  - hosts:
    - speaker-portal.oneabbott.com
    secretName: tls-speaker-portal-secret
  - hosts:
    - speaker-portal-h5.abbott.com.cn
    secretName: tls-speaker-portal-h5-secret
  rules:
  - host: speaker-portal-api.oneabbott.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: corp-ca191-speakerapi-p
            port:
              number: 80
  - host: speaker-portal.oneabbott.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: corp-ca191-speakerh5-p
            port:
              number: 80
  - host: speaker-portal-h5.abbott.com.cn
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: corp-ca191-speakerh5-pc-p
            port:
              number: 80