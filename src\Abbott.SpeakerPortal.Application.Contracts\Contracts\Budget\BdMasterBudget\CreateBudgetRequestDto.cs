﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Abbott.SpeakerPortal.Contracts.Budget
{
    public class CreateBudgetRequestDto
    {
        /// <summary>
        /// bu
        /// </summary>
        [Required(ErrorMessage = "请填写Bu")]
        public Guid BuId { get; set; }
        /// <summary>
        /// 描述
        /// </summary>
        [Required(ErrorMessage = "请填写描述")]
        public string Description { get; set; }
        /// <summary>
        /// 负责人Id
        /// </summary>
        [Required(ErrorMessage = "请填写负责人")]
        public Guid OwnerId { get; set; }
        /// <summary>
        /// 固定资产
        /// </summary>
        public bool Capital { get; set; }
        /// <summary>
        /// 主预算金额
        /// </summary>
        [Required(ErrorMessage = "请填写预算金额")]
        [Range(0, 99999999999999, ErrorMessage = "预算金额不能小于0")]
        public decimal BudgetAmount { get; set; }
        ///// <summary>
        ///// 状态
        ///// </summary>
        //public bool Status { get; set; }
        /// <summary>
        /// 年度
        /// </summary>
        [Required(ErrorMessage = "年度必填")]
        public int Year { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
    }
}
