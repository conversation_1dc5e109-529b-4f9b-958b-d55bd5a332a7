﻿using Abbott.SpeakerPortal.Contracts.Common.SMS;
using Flurl.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Text;
using System.Threading.Tasks;
using System.Security.Cryptography;
using Abbott.SpeakerPortal.Redis;
using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Enums;
using System.Collections.Generic;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.VendorApplications;
using System.Linq;
using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.Contracts.Common.Common;
using Abbott.SpeakerPortal.Contracts.Common;
using AlibabaCloud.SDK.Dysmsapi20170525.Models;
using AlibabaCloud.OpenApiClient.Models;
using Org.BouncyCastle.Asn1.Ocsp;


namespace Abbott.SpeakerPortal.AppServices.Common
{
    public class SMSService : SpeakerPortalAppService, ISMSService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<SMSService> _logger;
        IServiceProvider _serviceProvider;
        private readonly ICommonService _commonService;

        public SMSService(IServiceProvider serviceProvider)
        {
            _configuration = serviceProvider.GetService<IConfiguration>();
            _logger = serviceProvider.GetService<ILogger<SMSService>>();
            _serviceProvider = serviceProvider;
            _commonService = _serviceProvider.GetService<ICommonService>();
        }

        public async Task<MessageResult> SendSMS(SendSMSRequestDto request)
        {
            var redis = _serviceProvider.GetService<IRedisRepository>();
            var result = new SendSMSResponseDto();
            var log = new SetOperationLogRequestDto();
            try
            {
                string host = _configuration.GetSection("SMS").GetValue<string>("Url");
                string appId = _configuration.GetSection("SMS").GetValue<string>("AppId");
                string secretKey = _configuration.GetSection("SMS").GetValue<string>("SecretKey");
                var url = $"{host}/simpleinter/sendSMS";
                if (string.IsNullOrEmpty(host) || string.IsNullOrEmpty(appId) || string.IsNullOrEmpty(secretKey))
                {
                    _logger.LogError("SendSMS接口:Url、AppId、SecretKey不能为空");
                    return MessageResult.FailureResult("SendSMS接口:Url、AppId、SecretKey不能为空");
                }

                var verifyCode = string.Empty;
                var contentSMS = string.Empty;
                switch (request.ContentType)
                {
                    case (int)SMSType.Verification:
                        var rd = new Random();
                        verifyCode = rd.Next(100000, 999999).ToString();
                        contentSMS = $"【雅培贸易】您的短信验证码为{verifyCode}，该验证码5分钟内有效。为了您的隐私安全，切勿将验证码泄露于他人。如非本人操作，请忽略本短信。";
                        break;
                    case (int)SMSType.Activation:
                        contentSMS = $"【雅培贸易】您的的账号已成功激活，请登录【雅培讲者平台】查询。";
                        break;
                    default:
                        contentSMS = request.Content;
                        break;
                }
                string timeStamp = DateTimeOffset.UtcNow.ToString("yyyyMMddHHmmss");
                string sign = $"{appId}{secretKey}{timeStamp}";
                sign = GetMd532(sign);
                var bodyParam = new
                {
                    appId = appId,
                    timestamp = timeStamp,
                    sign = sign,
                    mobiles = request.Mobiles,
                    content = contentSMS,
                };
                log = _commonService.InitOperationLog("短信平台", "发送短信", url + "|" + JsonConvert.SerializeObject(bodyParam));
                var response = await url.PostUrlEncodedAsync(bodyParam);
                string responseData = await response.GetStringAsync();
                _commonService.LogResponse(log, responseData);
                _logger.LogInformation($"SendSMS接口:responseData:{responseData}");
                if (responseData != null)
                    result = JsonConvert.DeserializeObject<SendSMSResponseDto>(responseData);

                if (result?.Code != "SUCCESS")
                {
                    _logger.LogError($"SendSMS调用报错,ErrCode={result?.Code},Json{responseData}");
                    return MessageResult.FailureResult($"发送短信失败：{result?.Code}");
                }

                //存入redis5分钟，后续拿来验证
                if (!string.IsNullOrEmpty(verifyCode))
                    redis.Database.StringSet(RedisKey.VerifyCode + request.Mobiles, verifyCode, TimeSpan.FromMinutes(5));

                return MessageResult.SuccessResult();
            }
            catch (Exception ex)
            {
                _commonService.LogResponse(log, ex.ToString(), false);
                _logger.LogError($"SendSMS Exception:{ex.Message}");
                return MessageResult.FailureResult($"发送短信异常：{ex.Message}");
            }
        }

        ///// <summary>
        ///// 阿里云发送短信
        ///// </summary>
        ///// <param name="request"></param>
        ///// <returns></returns>
        //public async Task<MessageResult> SendSMS(SendSMSRequestDto request)
        //{
        //    var redis = _serviceProvider.GetService<IRedisRepository>();
        //    //说明才发送过短信，不能重复发送
        //    if (await redis.Database.KeyExistsAsync(RedisKey.RecentFlag + request.Mobiles))
        //        return MessageResult.FailureResult("重新发送请3分钟之后再试");
        //    var log = new SetOperationLogRequestDto();
        //    try
        //    {
        //        string accessKeyId = _configuration.GetSection("SMS").GetValue<string>("AccessKeyId");
        //        string accessKeySecret = _configuration.GetSection("SMS").GetValue<string>("AccessKeySecret");
        //        string regionId = _configuration.GetSection("SMS").GetValue<string>("RegionId");
        //        string signName = _configuration.GetSection("SMS").GetValue<string>("SignName");
        //        string templateCode = _configuration.GetSection("SMS").GetValue<string>("TemplateCode");

        //        var verifyCode = new Random().Next(100000, 999999).ToString();
        //        Config config = new Config
        //        {
        //            AccessKeyId = accessKeyId,
        //            AccessKeySecret = accessKeySecret,
        //        };
        //        // Endpoint 请参考 https://api.aliyun.com/product/Dysmsapi
        //        config.Endpoint = "dysmsapi.aliyuncs.com";
        //        config.RegionId = regionId;
        //        var client = new AlibabaCloud.SDK.Dysmsapi20170525.Client(config);
        //        //请求
        //        SendSmsRequest sendSmsRequest = new SendSmsRequest
        //        {
        //            PhoneNumbers = request.Mobiles,
        //            SignName = signName,
        //            TemplateCode = templateCode, // 短信模板CODE,SMS_474910900
        //            TemplateParam = $"{{\"code\":\"{verifyCode}\"}}", // 短信模板参数
        //        };
        //        log = _commonService.InitOperationLog("短信平台", "发送短信：", JsonConvert.SerializeObject(sendSmsRequest));
        //        var smsResponse = client.SendSmsWithOptions(sendSmsRequest, new AlibabaCloud.TeaUtil.Models.RuntimeOptions());

        //        //记录日志
        //        var json = string.Empty;
        //        if (smsResponse != null)
        //            json = JsonConvert.SerializeObject(smsResponse);
        //        _commonService.LogResponse(log, json);

        //        //成功发送短信后，记录flag，防止重复发送
        //        await redis.Database.StringSetAsync(RedisKey.RecentFlag + request.Mobiles, string.Empty, TimeSpan.FromMinutes(3));

        //        //存入redis5分钟，后续拿来验证
        //        if (!string.IsNullOrEmpty(verifyCode))
        //            await redis.Database.StringSetAsync(RedisKey.VerifyCode + request.Mobiles, verifyCode, TimeSpan.FromMinutes(5));

        //        return MessageResult.SuccessResult();
        //    }
        //    catch (Exception ex)
        //    {
        //        _commonService.LogResponse(log, ex.ToString(), false);
        //        _logger.LogError($"SendSMS Exception:{ex.Message}");
        //        return MessageResult.FailureResult($"发送短信异常：{ex.Message}");
        //    }
        //}


        /// <summary>
        /// 阿里云发送短信
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        //public async Task<MessageResult> SendSMS(SendSMSRequestDto request)
        //{
        //    var redis = _serviceProvider.GetService<IRedisRepository>();
        //    //说明才发送过短信，不能重复发送
        //    if (await redis.Database.KeyExistsAsync(RedisKey.RecentFlag + request.Mobiles))
        //        return MessageResult.FailureResult("重新发送请1分钟之后再试");

        //    var log = new SetOperationLogRequestDto();
        //    try
        //    {
        //        string accessKeyId = _configuration.GetSection("SMS").GetValue<string>("AccessKeyId");
        //        string accessKeySecret = _configuration.GetSection("SMS").GetValue<string>("AccessKeySecret");
        //        string regionId = _configuration.GetSection("SMS").GetValue<string>("RegionId");
        //        string signName = _configuration.GetSection("SMS").GetValue<string>("SignName");
        //        string templateCode = _configuration.GetSection("SMS").GetValue<string>("TemplateCode");

        //        // 创建默认的阿里云客户端配置
        //        var profile = DefaultProfile.GetProfile(regionId, accessKeyId, accessKeySecret);
        //        // 显式指定端点
        //        DefaultProfile.AddEndpoint(regionId, regionId, "Dysmsapi", "dysmsapi.aliyuncs.com");

        //        var verifyCode = new Random().Next(100000, 999999).ToString();
        //        // 创建发送短信请求
        //        var smsRequest = new SendSmsRequest
        //        {
        //            PhoneNumbers = request.Mobiles, // 接收短信的手机号码
        //            SignName = signName, // 短信签名名称
        //            TemplateCode = templateCode, // 短信模板CODE,SMS_474910900
        //            TemplateParam = $"{{\"code\":\"{verifyCode}\"}}" // 短信模板参数
        //        };
        //        // 创建一个客户端实例
        //        var client = new DefaultAcsClient(profile);
        //        log = _commonService.InitOperationLog("短信平台", "发送短信：", JsonConvert.SerializeObject(smsRequest));

        //        // 发送短信
        //        var smsResponse = client.GetAcsResponse(smsRequest);

        //        //记录日志
        //        var json = string.Empty;
        //        if (smsResponse != null)
        //            json = JsonConvert.SerializeObject(smsResponse);
        //        _commonService.LogResponse(log, json);

        //        if (!string.Equals(smsResponse?.Code, "OK", StringComparison.CurrentCultureIgnoreCase))
        //            return MessageResult.FailureResult($"发送短信失败：{smsResponse?.Code}");

        //        //成功发送短信后，记录flag，防止重复发送
        //        await redis.Database.StringSetAsync(RedisKey.RecentFlag + request.Mobiles, string.Empty, TimeSpan.FromMinutes(1));

        //        //存入redis5分钟，后续拿来验证
        //        if (!string.IsNullOrEmpty(verifyCode))
        //            await redis.Database.StringSetAsync(RedisKey.VerifyCode + request.Mobiles, verifyCode, TimeSpan.FromMinutes(5));

        //        return MessageResult.SuccessResult();
        //    }
        //    catch (Exception ex)
        //    {
        //        _commonService.LogResponse(log, ex.ToString(), false);
        //        _logger.LogError($"SendSMS Exception:{ex.Message}");
        //        return MessageResult.FailureResult($"发送短信异常：{ex.Message}");
        //    }
        //}

        /// <summary>
        /// 验证码验证
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> VerifySMS(VerifySMSRequestDto request)
        {
            try
            {
                var verifyCode = request.VerifyCode;
                var redis = _serviceProvider.GetService<IRedisRepository>();
                string value = redis.Database.StringGet(RedisKey.VerifyCode + request.Mobile);
                if (string.IsNullOrEmpty(value))
                {
                    return await Task.FromResult(MessageResult.FailureResult(new MessageModelBase(404, "Invalid verification code!")));
                }
                if (!value.Equals(verifyCode))
                {
                    return await Task.FromResult(MessageResult.FailureResult(new MessageModelBase(500, "Verification code error!")));
                }
                return await Task.FromResult(MessageResult.SuccessResult());
            }
            catch (Exception ex)
            {
                _logger.LogError($"SendSMS Exception:{ex.Message}");
                return MessageResult.FailureResult("Validation failed!");
            }
        }
        /// <summary>
        /// 大写的md5 32位加密
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        private string GetMd532(string str)
        {
            var md5 = MD5.Create();
            byte[] bytes = Encoding.UTF8.GetBytes(str);
            string result = BitConverter.ToString(md5.ComputeHash(bytes));
            return result.Replace("-", "");
        }

        /// <summary>
        /// 旧手机号验证
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> VerifyOldPhone(string vendorCode, string oldPhone)
        {
            var queryVendorApp = await LazyServiceProvider.LazyGetService<IVendorApplicationRepository>().GetQueryableAsync();
            var queryVendor = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();

            var existVendors = queryVendorApp.Where(a => a.ApplicationCode == vendorCode && a.HandPhone == oldPhone).Select(a => a.HandPhone)
                .Concat
                (
                    queryVendor.Where(a => a.VendorCode == vendorCode && a.HandPhone == oldPhone).Select(a => a.HandPhone)
                ).ToArray();

            return MessageResult.SuccessResult(existVendors.Any());
        }
    }
}
