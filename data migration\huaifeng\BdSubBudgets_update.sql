SELECT
 TRY_CONVERT(UNIQUEIDENTIFIER, [Id]) [Id]
,[Code]
,TRY_CONVERT(UNIQUEIDENTIFIER, [MasterBudgetId]) [MasterBudgetId]
,TRY_CONVERT(UNIQUEIDENTIFIER, IIF([CostCenterId] = '' or [CostCenterId] is null,'00000000-0000-0000-0000-000000000000',[CostCenterId])) [CostCenterId]
,TRY_CONVERT(UNIQUEIDENTIFIER, IIF([RegionId] = '' or [RegionId] is null,'00000000-0000-0000-0000-000000000000',[RegionId])) [RegionId]
,TRY_CONVERT(UNIQUEIDENTIFIER, IIF([BuId] = '' or [BuId] is null,'00000000-0000-0000-0000-000000000000',[BuId])) [BuId]
,TRY_CONVERT(UNIQUEIDENTIFIER, IIF([OwnerId] = '' or [OwnerId] is null,'00000000-0000-0000-0000-000000000000',[OwnerId])) [OwnerId]
,iif(UesdAmount is null,0,UesdAmount) AS [UesdAmount]
--,[AvailableAmount],目标层无此字段
,[AttachmentFile]
,[ExtraProperties]
,'NULL' AS [ConcurrencyStamp]
,GETDATE() AS [CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CreatorId]) [CreatorId]
,[LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [LastModifierId]) [LastModifierId]
,0 AS [IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, [DeleterId]) [DeleterId]
,[DeletionTime]
,[Description]
,[Remark]
,Status AS [Status]
,[IsComplicanceAudits]
,[Bu2]
,[LMMs]
,[Owner2]
,[ProductManagers]
,[RegionManagers]
,[RegionalAssistants]
,iif(BudgetAmount is null,0,BudgetAmount)  AS [BudgetAmount]
INTO #BdSubBudgets
FROM PLATFORM_ABBOTT_STG.dbo.BdSubBudgets

--select * from PLATFORM_ABBOTT_STG.dbo.BdSubBudgets
--select [CostCenterId],* FROM PLATFORM_ABBOTT_STG.dbo.BdSubBudgets
--where [CostCenterId] is null or [CostCenterId] = ''

--drop table #BdSubBudgets

USE Speaker_Portal_STG;

UPDATE a
SET
 a.[Id] = b.[Id]
,a.[Code] = b.[Code]
,a.[MasterBudgetId] = b.[MasterBudgetId]
,a.[CostCenterId] = b.[CostCenterId]
,a.[RegionId] = b.[RegionId]
,a.[BuId] = b.[BuId]
,a.[OwnerId] = b.[OwnerId]
,a.[UesdAmount] = b.[UesdAmount]
--,a.[AvailableAmount] = b.[AvailableAmount] 目标层无此字段
,a.[AttachmentFile] = b.[AttachmentFile]
,a.[ExtraProperties] = b.[ExtraProperties]
,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = b.[CreatorId]
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = b.[LastModifierId]
,a.[IsDeleted] = b.[IsDeleted]
,a.[DeleterId] = b.[DeleterId]
,a.[DeletionTime] = b.[DeletionTime]
,a.[Description] = b.[Description]
,a.[Remark] = b.[Remark]
,a.[Status] = b.[Status]
,a.[IsComplicanceAudits] = b.[IsComplicanceAudits]
,a.[Bu2] = b.[Bu2]
,a.[LMMs] = b.[LMMs]
,a.[Owner2] = b.[Owner2]
,a.[ProductManagers] = b.[ProductManagers]
,a.[RegionManagers] = b.[RegionManagers]
,a.[RegionalAssistants] = b.[RegionalAssistants]
,a.[BudgetAmount] = b.[BudgetAmount]
FROM dbo.BdSubBudgets a
left join #BdSubBudgets  b
ON a.id=b.id

--select* from #BdSubBudgets WHERE CostCenterId IS NULL


INSERT INTO dbo.BdSubBudgets
SELECT
 [Id]
,[Code]
,[MasterBudgetId]
,[CostCenterId]
,[RegionId]
,[BuId]
,[OwnerId]
,[UesdAmount]
--,[AvailableAmount] 目标层无此字段
,[AttachmentFile]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[Description]
,[Remark]
,[Status]
,[IsComplicanceAudits]
,[Bu2]
,[LMMs]
,[Owner2]
,[ProductManagers]
,[RegionManagers]
,[RegionalAssistants]
,[BudgetAmount]
FROM #BdSubBudgets a
WHERE not exists (select * from dbo.BdSubBudgets where id=a.id)


