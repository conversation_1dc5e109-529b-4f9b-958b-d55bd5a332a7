CREATE PROCEDURE dbo.sp_InteOnlineMeetingSettlement
AS 
BEGIN
	
with InterfaceLog_info as (
SELECT 
	[Method],
	IsSuccess,
	[Date],
	value AS serialNumber
FROM (SELECT [Parameter] as jsonData,[Method],IsSuccess,ID,Scenes,[Date]
from PLATFORM_ABBOTT_Stg.dbo.ODS_T_TPM_InterfaceLog ) ScenesData
CROSS APPLY STRING_SPLIT(scenes, ',')
)
select 
newid() AS Id,--自动生成的uuid
a.SerialNumber AS PRApplicationId,--基于03-1迁移的申请单主信息，以PR单号定位对应的PurPRApplications.ID
--若serialnumber无法匹配至任何记录，说明记录为测试数据，该条数据需要跳过"
a.SerialNumber AS SerialNumber,--
[no] AS No,--
vendorCode AS VendorCode,--
PdfUrl AS PdfUrl,--
PayAmount AS PayAmount,--
ModifyRemark AS ModifyRemark,--
ModifyAmountRemark AS ModifyAmountRemark,--
Executive AS Executive,--
ExecutiveMail AS ExecutiveMail,--
StartDate AS StartDate,--
ActualNumber AS ActualNumber,--
'NULL' AS ConcurrencyStamp,--?
[Date] AS CreationTime,--按"SerialNumber_No"拼接后查询该表中Method=PostSettlementInfo的记录，填入对应的Date，若有多条记录则去最新Date为准
'1' AS CreatorId,--统一填写为admin账号
'NULL' AS DeleterId,--默认为空
'NULL' AS DeletionTime,--默认为空
'{}' AS ExtraProperties,--默认填写为"{}"
'0' AS IsDeleted,--默认为0
'NULL' AS LastModificationTime,--默认为空
'NULL' AS LastModifierId,--默认为空
concat(a.SerialNumber,no) AS PRDetailId--按单号+行号，定位至PurPRApplicationDetails.id
into #InteOnlineMeetingSettlement_tmp
from PLATFORM_ABBOTT_Stg.dbo.ods_T_ERS_MeetingSettlementInfo a 
left join (select serialNumber,max([Date]) as [Date] from InterfaceLog_info group by serialNumber) b
on concat(a.serialNumber,'_',No)=b.serialNumber

IF OBJECT_ID(N'PLATFORM_ABBOTT_Stg.dbo.InteOnlineMeetingSettlement_tmp', N'U') IS NOT NULL
BEGIN
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	UPDATE a
	SET
	 a.PRApplicationId = b.PRApplicationId
	,a.PdfUrl = b.PdfUrl
	,a.PayAmount = b.PayAmount
	,a.ModifyRemark = b.ModifyRemark
	,a.ModifyAmountRemark = b.ModifyAmountRemark
	,a.Executive = b.Executive
	,a.ExecutiveMail = b.ExecutiveMail
	,a.StartDate = b.StartDate
	,a.ActualNumber = b.ActualNumber
	,a.ConcurrencyStamp = b.ConcurrencyStamp
	,a.CreationTime = b.CreationTime
	,a.CreatorId = b.CreatorId
	,a.DeleterId = b.DeleterId
	,a.DeletionTime = b.DeletionTime
	,a.ExtraProperties = b.ExtraProperties
	,a.IsDeleted = b.IsDeleted
	,a.LastModificationTime = b.LastModificationTime
	,a.LastModifierId = b.LastModifierId
	,a.PRDetailId = b.PRDetailId
	FROM PLATFORM_ABBOTT_Stg.dbo.InteOnlineMeetingSettlement_tmp a
	LEFT JOIN #InteOnlineMeetingSettlement_tmp b
	ON a.SerialNumber = b.SerialNumber 
	AND a.VendorCode = b.VendorCode
	AND a.[No] = b.[No]
	
	INSERT INTO PLATFORM_ABBOTT_Stg.dbo.InteOnlineMeetingSettlement_tmp
	SELECT
	 a.Id
	,a.PRApplicationId
	,a.SerialNumber
	,a.[No]
	,a.VendorCode
	,a.PdfUrl
	,a.PayAmount
	,a.ModifyRemark
	,a.ModifyAmountRemark
	,a.Executive
	,a.ExecutiveMail
	,a.StartDate
	,a.ActualNumber
	,a.ConcurrencyStamp
	,a.CreationTime
	,a.CreatorId
	,a.DeleterId
	,a.DeletionTime
	,a.ExtraProperties
	,a.IsDeleted
	,a.LastModificationTime
	,a.LastModifierId
	,a.PRDetailId
	FROM #InteOnlineMeetingSettlement_tmp a
	WHERE NOT EXISTS (
		SELECT 
		* 
		FROM PLATFORM_ABBOTT_Stg.dbo.InteOnlineMeetingSettlement_tmp 
		WHERE 1=1
		AND SerialNumber=a.SerialNumber 
		AND VendorCode=a.VendorCode
		AND [No]=a.[No]
	)
	END
	ELSE
	BEGIN
    --落成实体表
    select *
    into PLATFORM_ABBOTT_Stg.dbo.InteOnlineMeetingSettlement_tmp from #InteOnlineMeetingSettlement_tmp
    -- select * from #InteOnlineMeetingSettlement_tmp
	  PRINT(N' 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END


END
