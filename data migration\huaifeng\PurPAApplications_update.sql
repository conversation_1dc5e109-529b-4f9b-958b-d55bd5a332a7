SELECT
 TRY_CONVERT(UNIQUEIDENTIFIER, [Id]) [Id]
,[ApplicationCode]
,[Status]
,TRY_CONVERT(UNIQUEIDENTIFIER, [ApplyUserId]) [ApplyUserId]
,[ApplyUserName]
,[ApplyTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [ApplyUserBu]) [ApplyUserBu]
,[ApplyUserBuName]
,[ApplyUserBuToDeptName]
,[EsignPdf]
,[DeliveryMode]
,[MeetingModifyRemark]
,[AmountModifyRemark]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([GRId],'00000000-0000-0000-0000-000000000000')) [GRId]
,[GRApplicationCode]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([POId],'00000000-0000-0000-0000-000000000000')) [POId]
,[POApplicationCode]
,[CompanyCode]
,ISNULL([ExchangeRate],0) [ExchangeRate]
,[UrgentPayment]
,[UrgentType]
,[Region]
,[City]
,[AdvancePayment]
,[IsLastPayment]
,ISNULL([PaymentType],0) [PaymentType]
,[ReceivingHeader]
,ISNULL([SupplierWarehousingTime],GETDATE()) [SupplierWarehousingTime]
,[IsBackupInvoice]
,[Remarks]
,[Attachments]
,[Invoice]
,[SponsorshipRewardPoints]
,[AkritivCaseID]
,[PaymentTerms]
,[InvoiceDescription]
,[ReceivedDocumentsDate]
,[ExtraProperties]
,'NULL' [ConcurrencyStamp]
,[CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CreatorId]) [CreatorId]
,GETDATE() [LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [LastModifierId]) [LastModifierId]
,[IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, [DeleterId]) [DeleterId]
,GETDATE() [DeletionTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [VendorId]) [VendorId]
,[VendorName]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CompanyId]) [CompanyId]
,[CompanyName]
,IIF([PayTotalAmount] = 'NULL' or [PayTotalAmount] = '','0.0',PayTotalAmount) PayTotalAmount
,[AcceptedTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([AccepterId],'00000000-0000-0000-0000-000000000000')) [AccepterId]
,[AccepterName]
,0 [TaskType]
,GETDATE() [EstimatedPaymentDate]
,[SeperateRemark]
,0 [TaxStampLegal]
,0 [ExpenseType]
,0 [InterceptType]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([PRId],'00000000-0000-0000-0000-000000000000')) [PRId]
,[ApprovedDate]
,[VendorCode]
,TRY_CONVERT(UNIQUEIDENTIFIER, [ApprovedUserId]) [ApprovedUserId]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CityId]) [CityId]
,[Currency]
,[CurrencySymbol]
,[ExpectedFloatRate]
,ISNULL([PlanRate],0) [PlanRate]
,[EmailAttachment]
,[PayMethod]
,[ReasonModification]
,[VendorRating]
,[VendorRatingRemark]
,TRY_CONVERT(UNIQUEIDENTIFIER, '00000000-0000-0000-0000-000000000000') [TransfereeId]
,NULL [TransfereeName]
,0 [SendBackType]
INTO #PurPAApplications
FROM (SELECT *,ROW_NUMBER() OVER(PARTITION BY Id ORDER BY Id) RK FROM PLATFORM_ABBOTT_STG.dbo.PurPAApplications)a
WHERE RK = 1
;
--drop table #PurPAApplications

USE Speaker_Portal_STG;

UPDATE a 
SET 
 a.[Id] = b.[Id]
,a.[ApplicationCode] = b.[ApplicationCode]
,a.[Status] = b.[Status]
,a.[ApplyUserId] = b.[ApplyUserId]
,a.[ApplyUserName] = b.[ApplyUserName]
,a.[ApplyTime] = b.[ApplyTime]
,a.[ApplyUserBu] = b.[ApplyUserBu]
,a.[ApplyUserBuName] = b.[ApplyUserBuName]
,a.[ApplyUserBuToDeptName] = b.[ApplyUserBuToDeptName]
,a.[EsignPdf] = b.[EsignPdf]
,a.[DeliveryMode] = b.[DeliveryMode]
,a.[MeetingModifyRemark] = b.[MeetingModifyRemark]
,a.[AmountModifyRemark] = b.[AmountModifyRemark]
,a.[GRId] = b.[GRId]
,a.[GRApplicationCode] = b.[GRApplicationCode]
,a.[POId] = b.[POId]
,a.[POApplicationCode] = b.[POApplicationCode]
,a.[CompanyCode] = b.[CompanyCode]
,a.[ExchangeRate] = b.[ExchangeRate]
,a.[UrgentPayment] = b.[UrgentPayment]
,a.[UrgentType] = b.[UrgentType]
,a.[Region] = b.[Region]
,a.[CityCode] = b.[City]
,a.[AdvancePayment] = b.[AdvancePayment]
,a.[IsLastPayment] = b.[IsLastPayment]
,a.[PaymentType] = b.[PaymentType]
,a.[ReceivingHeader] = b.[ReceivingHeader]
,a.[SupplierWarehousingTime] = b.[SupplierWarehousingTime]
,a.[IsBackupInvoice] = b.[IsBackupInvoice]
,a.[Remarks] = b.[Remarks]
,a.[Attachments] = b.[Attachments]
,a.[Invoice] = b.[Invoice]
,a.[SponsorshipRewardPoints] = b.[SponsorshipRewardPoints]
,a.[AkritivCaseID] = b.[AkritivCaseID]
,a.[PaymentTerms] = b.[PaymentTerms]
,a.[InvoiceDescription] = b.[InvoiceDescription]
,a.[ReceivedDocumentsDate] = b.[ReceivedDocumentsDate]
,a.[ExtraProperties] = b.[ExtraProperties]
,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = b.[CreatorId]
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = b.[LastModifierId]
,a.[IsDeleted] = b.[IsDeleted]
,a.[DeleterId] = b.[DeleterId]
,a.[DeletionTime] = b.[DeletionTime]
,a.[VendorId] = b.[VendorId]
,a.[VendorName] = b.[VendorName]
,a.[CompanyId] = b.[CompanyId]
,a.[CompanyName] = b.[CompanyName]
,a.[PayTotalAmount] = b.[PayTotalAmount]
,a.[AcceptedTime] = b.[AcceptedTime]
,a.[AccepterId] = b.[AccepterId]
,a.[AccepterName] = b.[AccepterName]
,a.[TaskType] = b.[TaskType]
,a.[EstimatedPaymentDate] = b.[EstimatedPaymentDate]
,a.[SeperateRemark] = b.[SeperateRemark]
,a.[TaxStampLegal] = b.[TaxStampLegal]
,a.[ExpenseType] = b.[ExpenseType]
,a.[InterceptType] = b.[InterceptType]
,a.[PRId] = b.[PRId]
,a.[ApprovedDate] = b.[ApprovedDate]
,a.[VendorCode] = b.[VendorCode]
,a.[ApprovedUserId] = b.[ApprovedUserId]
,a.[CityId] = b.[CityId]
,a.[Currency] = b.[Currency]
,a.[CurrencySymbol] = b.[CurrencySymbol]
,a.[ExpectedFloatRate] = b.[ExpectedFloatRate]
,a.[PlanRate] = b.[PlanRate]
,a.[EmailAttachment] = b.[EmailAttachment]
,a.[PayMethod] = b.[PayMethod]
,a.[ReasonModification] = b.[ReasonModification]
,a.[VendorRating] = b.[VendorRating]
,a.[VendorRatingRemark] = b.[VendorRatingRemark]
,a.[TransfereeId] = b.[TransfereeId]
,a.[TransfereeName] = b.[TransfereeName]
,a.[SendBackType] = b.[SendBackType]
FROM dbo.PurPAApplications a
left join #PurPAApplications  b
ON a.id=b.id;


INSERT INTO dbo.PurPAApplications
(
 [Id]
,[ApplicationCode]
,[Status]
,[ApplyUserId]
,[ApplyUserName]
,[ApplyTime]
,[ApplyUserBu]
,[ApplyUserBuName]
,[ApplyUserBuToDeptName]
,[EsignPdf]
,[DeliveryMode]
,[MeetingModifyRemark]
,[AmountModifyRemark]
,[GRId]
,[GRApplicationCode]
,[POId]
,[POApplicationCode]
,[CompanyCode]
,[ExchangeRate]
,[UrgentPayment]
,[UrgentType]
,[Region]
,[CityCode]
,[AdvancePayment]
,[IsLastPayment]
,[PaymentType]
,[ReceivingHeader]
,[SupplierWarehousingTime]
,[IsBackupInvoice]
,[Remarks]
,[Attachments]
,[Invoice]
,[SponsorshipRewardPoints]
,[AkritivCaseID]
,[PaymentTerms]
,[InvoiceDescription]
,[ReceivedDocumentsDate]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[VendorId]
,[VendorName]
,[CompanyId]
,[CompanyName]
,[PayTotalAmount]
,[AcceptedTime]
,[AccepterId]
,[AccepterName]
,[TaskType]
,[EstimatedPaymentDate]
,[SeperateRemark]
,[TaxStampLegal]
,[ExpenseType]
,[InterceptType]
,[PRId]
,[ApprovedDate]
,[VendorCode]
,[ApprovedUserId]
,[CityId]
,[Currency]
,[CurrencySymbol]
,[ExpectedFloatRate]
,[PlanRate]
,[EmailAttachment]
,[PayMethod]
,[ReasonModification]
,[VendorRating]
,[VendorRatingRemark]
,[TransfereeId]
,[TransfereeName]
,[SendBackType]
)
SELECT
 [Id]
,[ApplicationCode]
,[Status]
,[ApplyUserId]
,[ApplyUserName]
,[ApplyTime]
,[ApplyUserBu]
,[ApplyUserBuName]
,[ApplyUserBuToDeptName]
,[EsignPdf]
,[DeliveryMode]
,[MeetingModifyRemark]
,[AmountModifyRemark]
,[GRId]
,[GRApplicationCode]
,[POId]
,[POApplicationCode]
,[CompanyCode]
,[ExchangeRate]
,[UrgentPayment]
,[UrgentType]
,[Region]
,[City]
,[AdvancePayment]
,[IsLastPayment]
,[PaymentType]
,[ReceivingHeader]
,[SupplierWarehousingTime]
,[IsBackupInvoice]
,[Remarks]
,[Attachments]
,[Invoice]
,[SponsorshipRewardPoints]
,[AkritivCaseID]
,[PaymentTerms]
,[InvoiceDescription]
,[ReceivedDocumentsDate]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[VendorId]
,[VendorName]
,[CompanyId]
,[CompanyName]
,[PayTotalAmount]
,[AcceptedTime]
,[AccepterId]
,[AccepterName]
,[TaskType]
,[EstimatedPaymentDate]
,[SeperateRemark]
,[TaxStampLegal]
,[ExpenseType]
,[InterceptType]
,[PRId]
,[ApprovedDate]
,[VendorCode]
,[ApprovedUserId]
,[CityId]
,[Currency]
,[CurrencySymbol]
,[ExpectedFloatRate]
,[PlanRate]
,[EmailAttachment]
,[PayMethod]
,[ReasonModification]
,[VendorRating]
,[VendorRatingRemark]
,[TransfereeId]
,[TransfereeName]
,[SendBackType]
FROM #PurPAApplications a
WHERE not exists (select * from dbo.PurPAApplications where id=a.id);

--truncate table dbo.PurPAApplications

--alter table Speaker_Portal_STG.dbo.PurPAApplications alter column [ApplicationCode] [nvarchar](50) NOT NULL
--alter table Speaker_Portal_STG.dbo.PurPAApplications alter column [ApplyUserBuName] [nvarchar](255) NULL
--alter table Speaker_Portal_STG.dbo.PurPAApplications alter column [ApplyUserBuToDeptName] [nvarchar](500) NULL
--alter table Speaker_Portal_STG.dbo.PurPAApplications alter column [EsignPdf] [nvarchar](500) NULL
--alter table Speaker_Portal_STG.dbo.PurPAApplications alter column [POApplicationCode] [nvarchar](500) NULL
--alter table Speaker_Portal_STG.dbo.PurPAApplications alter column [UrgentType] [nvarchar](500) NULL
--alter table Speaker_Portal_STG.dbo.PurPAApplications alter column [CityCode] [nvarchar](500) NULL
--alter table Speaker_Portal_STG.dbo.PurPAApplications alter column [ReceivingHeader] [nvarchar](500) NULL
--alter table Speaker_Portal_STG.dbo.PurPAApplications alter column [Attachments] [nvarchar](max) NULL
--alter table Speaker_Portal_STG.dbo.PurPAApplications alter column [Invoice] [nvarchar](max) NULL
--alter table Speaker_Portal_STG.dbo.PurPAApplications alter column [InvoiceDescription] [nvarchar](500) NULL
--alter table Speaker_Portal_STG.dbo.PurPAApplications alter column [VendorName] [nvarchar](500) NULL
--alter table Speaker_Portal_STG.dbo.PurPAApplications alter column [AccepterName] [nvarchar](500) NULL
--alter table Speaker_Portal_STG.dbo.PurPAApplications alter column [Currency] [nvarchar](500) NULL
--alter table Speaker_Portal_STG.dbo.PurPAApplications alter column [VendorRating] [nvarchar](50) NULL
