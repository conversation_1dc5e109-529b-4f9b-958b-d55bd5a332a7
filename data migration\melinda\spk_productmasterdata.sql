select newid()  as spk_NexBPMCode,spk_BPMCode,spk_name,spk_chinesevalue,spk_englishvalue,spk_productcode,spk_limitexpensecode,flg
into #spk_productmasterdata from(
select * from spk_productmasterdata_Tmp)A

IF OBJECT_ID(N'dbo.spk_productmasterdata', N'U') IS NOT NULL
BEGIN
	update a 
	set  a.spk_BPMCode           = b.spk_BPMCode         
        ,a.spk_name              = b.spk_name            
        ,a.spk_chinesevalue      = b.spk_chinesevalue    
        ,a.spk_englishvalue      = b.spk_englishvalue    
        ,a.spk_productcode       = b.spk_productcode     
        ,a.spk_limitexpensecode  = b.spk_limitexpensecode
        ,a.flg                   = b.flg
    from spk_productmasterdata a
    join #spk_productmasterdata b on a.spk_BPMCode = b.spk_BPMCode
    
    insert into spk_productmasterdata
    select a.spk_NexBPMCode
          ,a.spk_BPMCode
          ,a.spk_name
          ,a.spk_chinesevalue
          ,a.spk_englishvalue
          ,a.spk_productcode
          ,a.spk_limitexpensecode
          ,a.flg
    from #spk_productmasterdata a
    where not exists (select * from spk_productmasterdata where a.spk_BPMCode = spk_BPMCode)
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into dbo.spk_productmasterdata from #spk_productmasterdata
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
