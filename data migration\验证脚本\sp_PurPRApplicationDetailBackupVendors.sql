CREATE PROCEDURE dbo.sp_PurPRApplicationDetailBackupVendors
AS 
BEGIN
	--基本规则：查询[AUTO_BIZ_T_ProcurementApplication_Info]得到历史的采购申请单据，以ProcInstId+No join [AUTO_BIZ_T_ProcurementApplication_Info_Alternative]得到特定的采购申请明细行对应的可能多个备选讲者信息
select 
--newid() AS [Id],--自动生成的uuid
a.ProcInstId,
b.no,
ppt.Id AS [PRApplicationId],--基于03-1迁移的申请单主信息，以ProcInstId定位对应的[PurPRApplications].[ID]
c.id AS [PRApplicationDetailId],--基于03-2迁移的申请单明细行信息，以ProcInstId+No定位对应的[PurPRApplicationDetails].[ID]
oba.ID AS [VendorId],--基于此处的[SupplierNo]及[SupplierName]，结合[AUTO_BIZ_T_ProcurementApplication]该单的公司编码[f_code]，去查询[BPCSPMFVM]对应的记录([VNDERX],[VEXTNM],[VMCMPY])，对于匹配成功的记录，再找到其对应的[BPCSAVM].[ID]并填入(基于[VMCMPY]=[VCMPNY] AND [VNDERX]=[VENDOR])
b.[SupplierName] AS [VendorName],--
b.[TermCodeDays] AS [PaymentTerm],--
b.[HCPLevel] AS [HcpLevelName],--
b.[HCPLevel] AS [HcpLevelCode],--以该值匹配至HCP级别字典以找回字典Code
b.[SpeakInfo] AS [Hospital],--
CONVERT(decimal(10, 2), b.[Amount]/c.Quantity) AS [Price],--以该金额除以对应PR明细行的[Quantity]得到单价
concat( b.[SupplierName],'(单价：',isnull(CONVERT(decimal(10, 2), b.[Amount]/c.Quantity),0),')') AS [Text],--拼接为"{VendorName} (单价: {Price})"(Amount基于上方逻辑计算得到单价)
'{}' AS [ExtraProperties],--默认填写为"{}"
'NULL' AS [ConcurrencyStamp],--?
a.[applicationDate]  AS [CreationTime],--与对应的[PurPRApplications]记录保持一致即可
a.[applicantEmpId] AS [CreatorId],--与对应的[PurPRApplications]记录保持一致即可
null AS [LastModificationTime],--默认为空
null AS [LastModifierId],--默认为空
'0' AS [IsDeleted],--默认为0
null AS [DeleterId],--默认为空
null AS [DeletionTime],--默认为空
null AS [ExceptionNumber],--留空(历史单据无该数据)
oba.VCMPNY,
oba.VENDOR
into #ProcurementApplication1
from (select *,ROW_NUMBER ()over(PARTITION by ProcInstId order by expenseCategory_Text desc ) rn from ods_AUTO_BIZ_T_ProcurementApplication_Info) a
join ods_AUTO_BIZ_T_ProcurementApplication_Info_Alternative b
on a.ProcInstId=b.ProcInstId  and a.rn=1
left join PurPRApplications_tmp ppt  
on b.[ProcInstID]=ppt.ProcInstId 
left join PurPRApplicationDetails_tmp c
on b.[ProcInstID]=c.ProcInstID and b.[No]=c.RowNo
left join ODS_BPCS_PMFVM obp   
on b.SupplierNo=cast(VNDERX as nvarchar(255)) and b.SupplierName =cast(VEXTNM as nvarchar(255)) and f_code=cast(VMCMPY as nvarchar(255))
left join ODS_BPCS_AVM oba 
on obp.VMCMPY =oba.VCMPNY  and  obp.VNDERX = oba.VENDOR ;
--left join ODS_AUTO_BIZ_T_ProcurementApplication_Info_PR d 
--on b.[ProcInstID]=d.ProcInstId  and b.[No]=d.[No] 
WITH SplitData AS (
    SELECT 
        ProcInstID as ID, -- 假设 ID 是主键或其他唯一标识符
        AlternateVendorName,
        AlternateVendorCode,
        No,
        Price,
        VendorName
    FROM ods_AUTO_BIZ_T_ProcurementApplication_Info_PR
    where  AlternateVendorcode is not null or AlternateVendorcode<>''
),
SplitNames AS (
    SELECT 
        ID,
        No,
        Price,
        VendorName,
        value AS AlternateVendorName,
        ROW_NUMBER() OVER (PARTITION BY ID ORDER BY (SELECT NULL)) AS RowNum
    FROM SplitData
    CROSS APPLY STRING_SPLIT(AlternateVendorName, ',')
),
SplitCodes AS (
    SELECT 
        ID,
        No,
        Price,
        VendorName,
        value AS AlternateVendorCode,
        ROW_NUMBER() OVER (PARTITION BY ID ORDER BY (SELECT NULL)) AS RowNum
    FROM SplitData
    CROSS APPLY STRING_SPLIT(AlternateVendorCode, ',')
),
pr_info as (
SELECT 
    sn.ID as ProcInstID,
    sn.No,
    sn.Price,
    sn.VendorName,
    AlternateVendorName,
    AlternateVendorCode
FROM SplitNames SN
JOIN SplitCodes SC ON SN.RowNum = SC.RowNum AND SN.ID = SC.ID)
select 
--newid() AS [Id],--自动生成的uuid
DISTINCT
a.ProcInstId,
b.no,
ppt.Id  AS [PRApplicationId],--基于03-1迁移的申请单主信息，以ProcInstId定位对应的[PurPRApplications].[ID]
c.id AS [PRApplicationDetailId],--基于03-2迁移的申请单明细行信息，以ProcInstId+No定位对应的[PurPRApplicationDetails].[ID]
oba.ID AS [VendorId],--基于此处的[AlternativeVendorCode]及[AlternativeVendorName]，结合[AUTO_BIZ_T_ProcurementApplication]该单的公司编码[f_code]，去查询[BPCSPMFVM]对应的记录([VNDERX],[VEXTNM],[VMCMPY])，对于匹配成功的记录，再找到其对应的[BPCSAVM].[ID]并填入(基于[VMCMPY]=[VCMPNY] AND [VNDERX]=[VENDOR])
--若有多个备选讲者，此处Code及Name都会以英文逗号分隔，且顺序是一一对应的，需要拆为多行"
b.[AlternateVendorName] AS [VendorName],--
concat('{',oba.VTERMS,'}_{',ota.VTMDDY,'}') AS [PaymentTerm],--基于上方[VendorId]匹配成功的[BPCSAVM]记录，找到帐期Code并拼接成"{VTERM}_{VTMDDY}天"的格式
vt.SPLevel AS [HcpLevelName],--基于上方[VendorId]匹配成功的记录，找到该供应商主数据对应的级别名并填入
vt.SPLevel AS [HcpLevelCode],--基于上方[VendorId]匹配成功的记录，找到该供应商主数据对应的级别Code并填入
vt.HospitalName AS [Hospital],--基于上方[VendorId]匹配成功的记录，找到该供应商主数据对应的医院名并填入
b.[Price] AS [Price],--
concat( b.[AlternateVendorName],'(单价：',isnull(CONVERT(decimal(10, 2), b.Price),0),')') AS [Text],--拼接为"{VendorName} (单价: {Price})"
'{}' AS [ExtraProperties],--默认填写为"{}"
'' AS [ConcurrencyStamp],--?
ppt.CreatorId AS [CreationTime],--与对应的[PurPRApplications]记录保持一致即可
ppt.CreationTime AS [CreatorId],--与对应的[PurPRApplications]记录保持一致即可
null AS [LastModificationTime],--默认为空
null AS [LastModifierId],--默认为空
'0' AS [IsDeleted],--默认为0
null AS [DeleterId],--默认为空
null AS [DeletionTime],--默认为空
null AS [ExceptionNumber],--留空(历史单据无该数据)
oba.VCMPNY,
oba.VENDOR
into #ProcurementApplication 
from (select *,ROW_NUMBER ()over(PARTITION by ProcInstId order by expenseCategory_Text desc ) rn from ods_AUTO_BIZ_T_ProcurementApplication_Info) a
join pr_info b
on a.ProcInstId=b.ProcInstId  and a.rn=1
left join PurPRApplications_tmp ppt  
on b.[ProcInstID]=ppt.ProcInstId 
left join PurPRApplicationDetails_tmp c
on b.[ProcInstID]=c.ProcInstID and b.[No]=c.RowNo
left join ODS_BPCS_PMFVM obp   
on b.AlternatevendorCode=cast(obp.VNDERX as nvarchar(255)) and b.AlternatevendorName =cast(obp.VEXTNM as nvarchar(255)) and f_code=cast(obp.VMCMPY as nvarchar(255))
left join ODS_BPCS_AVM oba 
on obp.VMCMPY =oba.VCMPNY  and  obp.VNDERX = oba.VENDOR 
left join (select *,ROW_NUMBER () over(partition by VTERM order by VTERM)rn  from ODS_TMP_AVT) ota 
on cast(ota.VTERM as nvarchar(50)) =cast(oba.VTERMS as nvarchar(50)) and ota.rn=1--6162728
left join Vendor_Tmp vt 
on oba.VCMPNY=vt.VCMPNY  and oba.VENDOR =vt.VENDOR 
where not exists (
select * from (
select DISTINCT  a.ProcInstId,a.No from ods_AUTO_BIZ_T_ProcurementApplication_Info_PR a --1362233
join ODS_AUTO_BIZ_T_ProcurementApplication_Info_Alternative b
on  a.ProcInstId =b.ProcInstId and a.no=b.no
where AlternatevendorName is not null and  AlternatevendorName<>'' and  AlternatevendorName <>N'NULL'
)a--127145 
where a.ProcInstId=b.ProcInstId and a.no=b.no
) and AlternatevendorName is not null and  AlternatevendorName<>'' and  AlternatevendorName <>N'NULL';

--drop table #PurPRApplicationDetailBackupVendors;
select newid() AS [Id],--自动生成的uuid
* 
into #PurPRApplicationDetailBackupVendors_tmp
from 
(select * from #ProcurementApplication 
union 
select * from #ProcurementApplication1 )
a;
--删除表
IF OBJECT_ID(N'PLATFORM_ABBOTT.dbo.PurPRApplicationDetailBackupVendors_tmp ', N'U') IS NOT NULL
BEGIN
	update a 
	set a.ProcInstId             = b.ProcInstId
       ,a.no                     = b.no
       ,a.PRApplicationId        = b.PRApplicationId
       ,a.PRApplicationDetailId  = b.PRApplicationDetailId
       ,a.VendorId               = b.VendorId
       ,a.VendorName             = b.VendorName
       ,a.PaymentTerm            = b.PaymentTerm
       ,a.HcpLevelName           = b.HcpLevelName
       ,a.HcpLevelCode           = b.HcpLevelCode
       ,a.Hospital               = b.Hospital
       ,a.Price                  = b.Price
--       ,a.Text                   = b.Text
--       ,a.ExtraProperties        = b.ExtraProperties
--       ,a.ConcurrencyStamp       = b.ConcurrencyStamp
       ,a.CreationTime           = b.CreationTime
       ,a.CreatorId              = b.CreatorId
       ,a.LastModificationTime   = b.LastModificationTime
       ,a.LastModifierId         = b.LastModifierId
--       ,a.IsDeleted              = b.IsDeleted
       ,a.DeleterId              = b.DeleterId
       ,a.DeletionTime = b.DeletionTime
    ,a.ExceptionNumber        = b.ExceptionNumber
       ,a.VCMPNY                 = b.VCMPNY
       ,a.VENDOR                 = b.VENDOR
     from PLATFORM_ABBOTT.dbo.PurPRApplicationDetailBackupVendors_tmp a
     left join #PurPRApplicationDetailBackupVendors_tmp b
     on a.ProcInstId = b.ProcInstId and a.no = b.no and a.VendorName = b.VendorName and isnull(b.Hospital,'null') = isnull(a.Hospital,'null')
      and isnull(b.VCMPNY,1) = isnull(a.VCMPNY,1) and isnull(b.VENDOR,1) = isnull(a.VENDOR,1)
     
     insert into PLATFORM_ABBOTT.dbo.PurPRApplicationDetailBackupVendors_tmp
     select a.Id
            ,a.ProcInstId
            ,a.no
            ,a.PRApplicationId
            ,a.PRApplicationDetailId
            ,a.VendorId
            ,a.VendorName
            ,a.PaymentTerm
            ,a.HcpLevelName
            ,a.HcpLevelCode
            ,a.Hospital
            ,a.Price
            ,a.Text
            ,a.ExtraProperties
            ,a.ConcurrencyStamp
            ,a.CreationTime
            ,a.CreatorId
            ,a.LastModificationTime
            ,a.LastModifierId
            ,a.IsDeleted
            ,a.DeleterId
            ,a.DeletionTime
            ,a.ExceptionNumber
            ,a.VCMPNY
            ,a.VENDOR
      from #PurPRApplicationDetailBackupVendors_tmp a
      where not exists (select * from PLATFORM_ABBOTT.dbo.PurPRApplicationDetailBackupVendors_tmp where 
      ProcInstId = a.ProcInstId and no = a.no and VendorName = a.VendorName and isnull(Hospital,'null') = isnull(a.Hospital,'null')
      and isnull(VCMPNY,1) = isnull(a.VCMPNY,1) and isnull(VENDOR,1) = isnull(a.VENDOR,1))
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
--落成实体表
select *
    into PLATFORM_ABBOTT.dbo.PurPRApplicationDetailBackupVendors_tmp from #PurPRApplicationDetailBackupVendors_tmp
-- select * from #vendor_tbl
  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END

END

