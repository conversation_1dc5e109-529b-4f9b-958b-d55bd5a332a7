﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.FOC;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.EFlow.FOC;
using Abbott.SpeakerPortal.Entities.EFlow.STicket;
using Abbott.SpeakerPortal.Enums;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;
using Abbott.SpeakerPortal.Extension;
using Volo.Abp.Identity;
using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.STicket;
using Microsoft.Extensions.DependencyInjection;
using Abbott.SpeakerPortal.Entities.Integration.Dspot;
using Senparc.Weixin.WxOpen.Entities;
using Abbott.SpeakerPortal.Entities.Budget;
using Abbott.SpeakerPortal.IntermediateDB.Repositories;
using Volo.Abp.ObjectMapping;
using Abbott.SpeakerPortal.Contracts.Common.Attachment;
using static Abbott.SpeakerPortal.Enums.DataverseEnums.Organization;
using DocumentFormat.OpenXml.Drawing.Spreadsheet;
using Abbott.SpeakerPortal.Contracts.Budget.FocMasterBudget;
using Abbott.SpeakerPortal.Contracts.Budget.Subbudget;
using Abbott.SpeakerPortal.Contracts.Dataverse;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPRApplication;
using DocumentFormat.OpenXml.Bibliography;
using Abbott.SpeakerPortal.Entities.Common.Attachment;
using static Abbott.SpeakerPortal.Enums.DataverseEnums;
using Abbott.SpeakerPortal.Contracts.Common.Approve;
using Abbott.SpeakerPortal.Contracts.Common.PowerApp;
using Abbott.SpeakerPortal.Utils;
using Newtonsoft.Json;
using static Abbott.SpeakerPortal.Consts.EntitiesNameConsts;
using Abbott.SpeakerPortal.Contracts.Budget.FocSubbudget;
using Abbott.SpeakerPortal.Entities.Budget.BdExpense;
using System.IO;
using MiniExcelLibs.OpenXml;
using MiniExcelLibs;
using Json = System.Text.Json;
using System.Text.RegularExpressions;
using DocumentFormat.OpenXml.Wordprocessing;
using Microsoft.EntityFrameworkCore.Metadata;
using Senparc.Weixin.MP.AdvancedAPIs.MerChant;
using Abbott.SpeakerPortal.AppServices.Common;
using Abbott.SpeakerPortal.Contracts.Common.Common;
using Abbott.SpeakerPortal.AppServices.STicket;
using Microsoft.Extensions.Logging;
using Senparc.Weixin.MP.AdvancedAPIs.Wxa.MerchantJson;
using Abbott.SpeakerPortal.Entities.EFlow.PP;
using Volo.Abp.Guids;
using EFCore.BulkExtensions;
using SysIConfiguration = Microsoft.Extensions.Configuration;
using static Abbott.SpeakerPortal.Permissions.SpeakerPortalPermissions;
using Microsoft.Extensions.Configuration;
using Senparc.NeuChar.Helpers;
using Hangfire.Logging;
using Abbott.SpeakerPortal.BackgroundWorkers.BPMEmail;
using Abbott.SpeakerPortal.Contracts.Common.BPMEmail;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.FileProviders;
using Hangfire;
using Abbott.SpeakerPortal.IntermediateDB.Models;
using Flurl.Util;
using Abbott.SpeakerPortal.Person;

namespace Abbott.SpeakerPortal.AppServices.FOC
{
    public partial class FocService : SpeakerPortalAppService, IFocService
    {
        /// <summary>
        /// 最大发货数量
        /// </summary>
        const int MaxQty = 999999999;

        private readonly IServiceProvider _serviceProvider;
        private readonly ICommonService _commonService;
        private readonly ILogger<FocService> _logger;
        private readonly SysIConfiguration.IConfiguration _configuration;
        private IHttpRequestService _httpRequestService;
        public FocService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _commonService = serviceProvider.GetService<ICommonService>();
            _logger = serviceProvider.GetService<ILogger<FocService>>();
            _configuration = serviceProvider.GetService<SysIConfiguration.IConfiguration>();
            _httpRequestService = serviceProvider.GetService<IHttpRequestService>();
        }

        /// <summary>
        /// 分页获取MDM产品列表
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetProductListResponseDto>> GetMDMProductListAsync(GetProductListRequestDto request)
        {
            var productQuery = await LazyServiceProvider.LazyGetService<IProductDetailsRepository>().GetQueryableAsync();
            var query = productQuery
                .Where(a => a.IsActive && a.UseFlagM)
                .WhereIf(!string.IsNullOrEmpty(request.ProductShortName), a => a.ShortName.Contains(request.ProductShortName))
                .WhereIf(!string.IsNullOrEmpty(request.ProductMCode), a => a.ProductMCode.Contains(request.ProductMCode));

            var result = query.Select(a => new GetProductListResponseDto
            {
                ProductId = a.Id,
                ProductName = a.ProductNameCN,
                ProductShortName = a.ShortName,
                ProductMCode = a.ProductMCode,
                ProductUnit = a.Unit,
                ProductSCode = a.ProductSCode,
                ProductMultiPack = a.MultiPack
            }).OrderByDescending(a => a.ProductSCode);
            var count = result.Count();
            var datas = result.Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToArray();

            return new PagedResultDto<GetProductListResponseDto>(count, datas);
        }

        /// <summary>
        /// 分页获取MDM产品MCode列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetProductMCodeResponseDto>> GetProductMCodeListAsync(GetProductListRequestDto request)
        {
            var productQuery = await LazyServiceProvider.LazyGetService<IProductDetailsRepository>().GetQueryableAsync();
            var query = productQuery.Where(a => a.UseFlagM)
                .WhereIf(!string.IsNullOrEmpty(request.ProductShortName), a => a.ShortName.Contains(request.ProductShortName))
                .WhereIf(!string.IsNullOrEmpty(request.ProductMCode), a => a.ProductMCode.Contains(request.ProductMCode));

            var result = query.GroupBy(a => new { a.ProductShortNameEN, a.ProductMCode, a.Unit })
                .Select(g => new GetProductMCodeResponseDto
                {
                    ProductId = g.First().Id,
                    ProductShortName = g.Key.ProductShortNameEN,
                    ProductMCode = g.Key.ProductMCode,
                    ProductUnit = g.Key.Unit
                })
                .OrderBy(a => a.ProductMCode);

            var count = result.Count();
            var datas = result.Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToArray();

            return new PagedResultDto<GetProductMCodeResponseDto>(count, datas);
        }

        /// <summary>
        /// 根据组织Id获取关联的FOC成本中心
        /// </summary>
        /// <param name="orgId"></param>
        /// <returns></returns>
        public async Task<IEnumerable<KeyValuePair<Guid, string>>> GetFocCostcenterByOrgAsync(Guid? orgId)
        {
            IEnumerable<CostcenterDto> costcenters;
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            //不传org时，返回所有成本中心
            if (!orgId.HasValue)
                costcenters = await dataverseService.GetCostcentersAsync();
            else
            {
                var orgs = await dataverseService.GetOrganizations(orgId.ToString());
                if (!orgs.Any())
                    return Enumerable.Empty<KeyValuePair<Guid, string>>();

                var org = orgs.First();
                if (!org.FocCostcenterId.HasValue)
                    return Enumerable.Empty<KeyValuePair<Guid, string>>();

                costcenters = await dataverseService.GetCostcentersAsync(org.FocCostcenterId?.ToString());
            }
            return costcenters.Select(a => new KeyValuePair<Guid, string>(a.Id, a.Name));
        }

        /// <summary>
        /// 获取FOC申请单详情
        /// </summary>
        /// <param name="id">FOC申请单Id</param>
        /// <returns></returns>
        public async Task<GetFocApplicationResponseDto> GetFocApplicationDetailsAsync(Guid id)
        {
            var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");
            var queryFoc = await LazyServiceProvider.LazyGetService<IFocApplicationRepository>().GetQueryableAsync();
            var queryFocDetail = await LazyServiceProvider.LazyGetService<IFocApplicationDetailRepository>().GetQueryableAsync();
            var queryFocProductDetail = await LazyServiceProvider.LazyGetService<IFocApplicationProductDetailRepository>().GetQueryableAsync();
            var queryFocLogisticsDetail = await LazyServiceProvider.LazyGetService<IFocApplicationLogisticsDetailRepository>().GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var usdCurrencies = await dataverseService.GetCurrencyConfig(DictionaryType.CurrencyItems.USD);

            var focApplication = await queryFoc.Where(a => a.Id == id).FirstOrDefaultAsync();
            if (focApplication == null)
            {
                return default;
            }

            var focApplicationDetail = await queryFocDetail.Where(a => a.ParentID == id).OrderBy(a => a.RowId).ToListAsync();

            var response = ObjectMapper.Map<FOCApplication, GetFocApplicationResponseDto>(focApplication);

            //费用性质
            var expenseNatures = await dataverseService.GetCostNatureAsync();
            var nature = expenseNatures.FirstOrDefault(a => a.Id == response.NatureId);
            if (nature != null)
            {
                response.NatureName = nature.Name;
            }
            //获取发货类型
            var shippingTypeList = await dataverseService.GetDictionariesAsync(DictionaryType.ShippingTypes);
            var shippingType = shippingTypeList.Where(a => a.Id == response.ShippingTypeId).FirstOrDefault();
            if (shippingType != null)
            {
                response.ShippingTypeName = shippingType.Name;
            }
            //收货人地址
            response.ConsigneeAddress = focApplication.ConsigneeAddress;
            if (!string.IsNullOrEmpty(response.ConsigneeAddress))
                response.ConsigneeAddress = AesHelper.Decryption(response.ConsigneeAddress, insightKey);
            //收货人名称
            response.ConsigneeName = focApplication.ConsigneeName;
            if (!string.IsNullOrEmpty(response.ConsigneeName))
                response.ConsigneeName = AesHelper.Decryption(response.ConsigneeName, insightKey);
            //收货人电话
            response.ConsigneePhone = focApplication.ConsigneePhone;
            if (!string.IsNullOrEmpty(response.ConsigneePhone))
                response.ConsigneePhone = AesHelper.Decryption(response.ConsigneePhone, insightKey);

            if (focApplicationDetail.Count > 0)
            {
                var detailItemMaps = ObjectMapper.Map<List<FOCApplicationDetail>, List<CreateUpdateFocApplicationDetailRequest>>(focApplicationDetail);
                detailItemMaps.ForEach(a =>
                {
                    a.ConsigneeAddress = !string.IsNullOrEmpty(a.ConsigneeAddress) ? AesHelper.Decryption(a.ConsigneeAddress, insightKey) : string.Empty;
                    a.ConsigneeName = !string.IsNullOrEmpty(a.ConsigneeName) ? AesHelper.Decryption(a.ConsigneeName, insightKey) : string.Empty;
                    a.ConsigneePhone = !string.IsNullOrEmpty(a.ConsigneePhone) ? AesHelper.Decryption(a.ConsigneePhone, insightKey) : string.Empty;
                });

                var focProductDetailIds = focApplicationDetail.Select(a => a.Id).ToList();
                var focProductDetail = queryFocProductDetail.Where(a => focProductDetailIds.Contains(a.FOCDetailId)).ToList();
                var focLogisticsDetail = queryFocLogisticsDetail.Where(a => focProductDetailIds.Contains(a.FOCDetailId)).ToList();
                if (focProductDetail.Count > 0)
                {
                    var productDetailMaps = ObjectMapper.Map<List<FOCApplicationProductDetail>, List<FocProductDetailRequest>>(focProductDetail);
                    var logisticsDetailMaps = ObjectMapper.Map<List<FOCApplicationLogisticsDetail>, List<FocLogisticsDetailRequest>>(focLogisticsDetail);
                    foreach (var item in detailItemMaps)
                    {
                        item.ProductDetailItems = productDetailMaps.Where(a => a.FOCDetailId == item.Id).ToList();
                        foreach (var productItem in item.ProductDetailItems)
                        {
                            productItem.LogisticsDetailItems = logisticsDetailMaps.Where(a => a.ProductSCode == productItem.ProductSCode && item.Id == a.FOCDetailId).ToList();
                        }
                    }
                }
                response.DetailItems = detailItemMaps;
            }
            //附件
            if (!string.IsNullOrEmpty(focApplication.Attachment))
            {
                var attachmentIds = focApplication.Attachment.Split(",", StringSplitOptions.RemoveEmptyEntries).Select(Guid.Parse).ToArray();
                response.Attachment = await LazyServiceProvider.LazyGetService<IAttachmentService>().GetAttachmentsAsync(attachmentIds);
            }

            if (focApplication.SubBudgetId.HasValue)
            {
                var queryBudget = await LazyServiceProvider.LazyGetService<IFocSubBudgetRepository>().GetQueryableAsync();
                var budget = await queryBudget.Include(a => a.MonthlyBudgets).Select(a => new
                {
                    a.Id,
                    a.Code,
                    a.RegionId,
                    a.AttachmentFile,
                    a.OwnerId,
                    a.BuId,
                    a.CostCenterId,
                    a.BudgetQty,
                    a.UesdQty,
                    a.MonthlyBudgets,
                    a.Description
                })
                .FirstOrDefaultAsync(a => a.Id == focApplication.SubBudgetId);
                if (budget != null)
                {
                    //子预算信息
                    response.BudgetDescription = budget.Description;
                    response.BudgetQty = budget.BudgetQty;
                    response.BudgetUsedQty = budget.UesdQty;
                    response.BudgetAvailableQty = budget.MonthlyBudgets.Where(a => a.Status).Sum(a => a.BudgetQty) - budget.UesdQty;

                    //BU
                    //var dataverseService = _serviceProvider.GetService<IDataverseService>();
                    var orgs = (await dataverseService.GetOrganizations()).Where(x => x.OrganizationType == OrganizationType.Bu);
                    var bu = orgs.FirstOrDefault(a => a.Id == budget.BuId);
                    if (bu != null)
                    {
                        response.BudgetBuName = bu.DepartmentName;
                    }
                    //成本中心
                    var costcenters = await dataverseService.GetCostcentersAsync(budget.CostCenterId.ToString());
                    var costcenter = costcenters.FirstOrDefault();
                    if (costcenter != null)
                    {
                        response.CostCenter = costcenter.Name;
                    }
                }
                //预算附件
                if (!string.IsNullOrEmpty(budget.AttachmentFile))
                {
                    var attachmentIds = budget.AttachmentFile.Split(",", StringSplitOptions.RemoveEmptyEntries).Select(Guid.Parse).ToArray();
                    response.BudgeAttachment = await LazyServiceProvider.LazyGetService<IAttachmentService>().GetAttachmentsAsync(attachmentIds);
                }
                response.BudgetFile = budget.AttachmentFile;
            }

            //检查申请单金额
            if (response.DetailItems != null)
            {
                var usdCurrency = usdCurrencies.First();
                var focRemindAmt = (await dataverseService.GetDictionariesAsync(DictionaryType.FOCRemindAmount)).FirstOrDefault();
                var productCosts = await dataverseService.GetProductCostAsync();
                var productCost = productCosts.Where(a => a.ProductMcode == response.ProductMCode).First();
                if (usdCurrency != null && focRemindAmt != null && productCost != null)
                {
                    var focSumQty = response.DetailItems.Sum(a => a.ProductQuantity);
                    decimal sumAmount = decimal.Round(productCost.CostValue * focSumQty / (decimal)usdCurrency.PlanRate, 4);
                    var remindAmt = Convert.ToDecimal(focRemindAmt.Name);
                    if (sumAmount >= remindAmt)
                    {
                        response.IsExceedAmount = true;
                    }
                }
            }

            return response;
        }

        /// <summary>
        /// 获取FOC申请单列表
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetFocApplicationListResponseDto>> GetFocApplicationListAsync(GetFocApplicationListRequestDto request)
        {
            var focApplicationQuery = (await LazyServiceProvider.LazyGetService<IFocApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            var focDetailQuery = (await LazyServiceProvider.LazyGetService<IFocApplicationDetailRepository>().GetQueryableAsync()).AsNoTracking();
            var focPDQuery = (await LazyServiceProvider.LazyGetService<IFocApplicationProductDetailRepository>().GetQueryableAsync()).AsNoTracking();
            //核销数量
            var writeoffQuery = focDetailQuery.GroupJoin(focPDQuery, a => a.Id, b => b.FOCDetailId, (a, b) => new { a, b }).SelectMany(a => a.b.DefaultIfEmpty(), (grp, b) => new { grp.a.ParentID, b.ShippedQty, b.WriteoffQty })
                 .GroupBy(g => g.ParentID, (key, group) => new { parentId = key, ShippedQtys = group.Sum(s => s.ShippedQty), WriteoffQtys = group.Sum(s => s.WriteoffQty) })
                 ;
            var personCenterService = LazyServiceProvider.LazyGetService<IPersonCenterService>();
            var commonService = LazyServiceProvider.LazyGetService<ICommonService>();
            var roleLevel = personCenterService.GetEFlowMyRoleLevel(true);

            //var roleLevel = rolesIds.MinBy(m => m.Key);
            switch (roleLevel)
            {
                case RoleLevel.Admin:
                    break;
                case RoleLevel.Manager:
                    {
                        var rolekyes = await personCenterService.GetIdsByRoleLevels([roleLevel]);
                        var buIds = rolekyes.GetValueOrDefault(RoleLevel.Manager)?.ToList() ?? [];
                        var depts = await commonService.GetChildrenOrgs(buIds.ToList());
                        var deptIds = depts.Select(s => s.Id).ToList();
                        deptIds.AddRange(buIds);
                        deptIds.Add(Guid.NewGuid());
                        focApplicationQuery = focApplicationQuery.Where(m => deptIds.Contains(m.ApplyUserDeptId) || m.ApplyUserId == CurrentUser.Id || m.TransfereeId == CurrentUser.Id);
                    }
                    break;
                case RoleLevel.Owner:
                    {
                        var focSubbudget = LazyServiceProvider.LazyGetService<IFocSubbudgetService>();
                        List<Guid> budgetIds = await focSubbudget.GetSubbudgetsByOwner(CurrentUser.Id.Value);
                        budgetIds.Add(Guid.NewGuid());
                        focApplicationQuery = focApplicationQuery.Where(m => (m.SubBudgetId.HasValue && budgetIds.Contains(m.SubBudgetId.Value)) || m.ApplyUserId == CurrentUser.Id || m.TransfereeId == CurrentUser.Id);
                    }
                    break;
                case RoleLevel.Leader:
                    {
                        var orgIds = (await commonService.GetAllChildrenOrgs(CurrentUser.Id.Value)).ToHashSet();
                        var depts = await commonService.GetChildrenOrgs(orgIds.ToList());
                        var deptIds = depts.Select(s => s.Id).ToList();
                        deptIds.AddRange(orgIds);
                        deptIds.Add(Guid.NewGuid());
                        focApplicationQuery = focApplicationQuery.Where(m => deptIds.Contains(m.ApplyUserDeptId));
                    }
                    break;
                default:
                    focApplicationQuery = focApplicationQuery.Where(m => m.ApplyUserId == CurrentUser.Id || m.TransfereeId == CurrentUser.Id);
                    break;
            }
            var query = focApplicationQuery.Where(m => m.Status != FOCStatus.Draft).WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), a => a.ApplicationCode.Contains(request.ApplicationCode))
                .WhereIf(!string.IsNullOrEmpty(request.ApplyDeptName), a => a.ApplyUserDeptName.Contains(request.ApplyDeptName))
                .WhereIf(request.CostCenterId.HasValue, a => a.CostCenterID == request.CostCenterId)
                .WhereIf(!string.IsNullOrEmpty(request.SubBudgetCode), a => a.SubBudgetCode.Contains(request.SubBudgetCode))
                .WhereIf(!string.IsNullOrEmpty(request.BudgetCode), a => a.BudgetCode.Contains(request.BudgetCode))
                .WhereIf(!string.IsNullOrEmpty(request.ProductMCode), a => a.ProductMCode.Contains(request.ProductMCode))
                .WhereIf(request.Status.HasValue, a => a.Status == request.Status)
                .WhereIf(request.StartDate.HasValue, a => a.ApplyTime >= request.StartDate)
                .WhereIf(request.EndDate.HasValue, a => a.ApplyTime <= request.EndDate.Value.AddDays(1))
                .WhereIf(request.ApplyUserId.HasValue, a => a.ApplyUserId == request.ApplyUserId)
                 .GroupJoin(writeoffQuery, a => a.Id, b => b.parentId, (a, b) => new { s = a, b }).SelectMany(s => s.b.DefaultIfEmpty(), (a, b) => new GetFocApplicationListResponseDto
                 {
                     Id = a.s.Id,
                     ApplicationCode = a.s.ApplicationCode,
                     ApplyUser = a.s.ApplyUser,
                     ApplyUserDeptName = a.s.ApplyUserDeptName,
                     ApplyUserId = a.s.ApplyUserId,
                     ApplyUserDeptId = a.s.ApplyUserDeptId,
                     CostCenter = a.s.CostCenter,
                     CostCenterCode = a.s.CostCenterCode,
                     BudgetCode = a.s.BudgetCode,
                     SubBudgetCode = a.s.SubBudgetCode,
                     ProductName = a.s.ProductName,
                     ProductMCode = a.s.ProductMCode,
                     ProductQty = a.s.ProductQty,
                     Status = a.s.Status,
                     ApplyTime = a.s.ApplyTime,
                     ShippedQty = b.ShippedQtys,
                     WriteoffQty = b.WriteoffQtys,
                 })
                ;
            var count = await query.CountAsync();
            var datas = await query.OrderByDescending(a => a.ApplyTime).PagingIf(request).ToListAsync();
            //var result = ObjectMapper.Map<List<FOCApplication>, List<GetFocApplicationListResponseDto>>(datas);
            return new PagedResultDto<GetFocApplicationListResponseDto>(count, datas);
        }

        /// <summary>
        /// 获取FOC申请单草稿列表
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetFocApplicationListResponseDto>> GetFocApplicationDraftListAsync(GetFocApplicationDraftListRequestDto request)
        {
            var focApplicationQuery = (await LazyServiceProvider.LazyGetService<IFocApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            var sTicketDetaiQuery = (await LazyServiceProvider.LazyGetService<IFocApplicationDetailRepository>().GetQueryableAsync()).AsNoTracking();
            var sTicketPDQuery = (await LazyServiceProvider.LazyGetService<IFocApplicationProductDetailRepository>().GetQueryableAsync()).AsNoTracking();
            //核销数量
            var writeoffQuery = sTicketDetaiQuery.GroupJoin(sTicketPDQuery, a => a.Id, b => b.FOCDetailId, (a, b) => new { a, b }).SelectMany(a => a.b.DefaultIfEmpty(), (grp, b) => new { grp.a.ParentID, b.ShippedQty, b.WriteoffQty })
                 .GroupBy(g => g.ParentID, (key, group) => new { parentId = key, ShippedQtys = group.Sum(s => s.ShippedQty), WriteoffQtys = group.Sum(s => s.WriteoffQty) })
                 ;
            var query = focApplicationQuery.Where(m => m.Status == FOCStatus.Draft && (m.ApplyUserId == CurrentUser.Id || m.TransfereeId == CurrentUser.Id)).WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), a => a.ApplicationCode.Contains(request.ApplicationCode))
                .WhereIf(!string.IsNullOrEmpty(request.ApplyDeptName), a => a.ApplyUserDeptName.Contains(request.ApplyDeptName))
                .WhereIf(request.CostCenterId.HasValue, a => a.CostCenterID == request.CostCenterId)
                .WhereIf(!string.IsNullOrEmpty(request.SubBudgetCode), a => a.SubBudgetCode.Contains(request.SubBudgetCode))
                .WhereIf(!string.IsNullOrEmpty(request.BudgetCode), a => a.BudgetCode.Contains(request.BudgetCode))
                .WhereIf(!string.IsNullOrEmpty(request.ProductMCode), a => a.ProductMCode.Contains(request.ProductMCode))
                .WhereIf(request.StartDate.HasValue, a => a.CreationTime >= request.StartDate)
                .WhereIf(request.EndDate.HasValue, a => a.CreationTime <= request.EndDate.Value.AddDays(1))
                .WhereIf(request.ApplicantId.HasValue, a => a.ApplyUserId == request.ApplicantId)
                 .GroupJoin(writeoffQuery, a => a.Id, b => b.parentId, (a, b) => new { s = a, b }).SelectMany(s => s.b.DefaultIfEmpty(), (a, b) => new GetFocApplicationListResponseDto
                 {
                     Id = a.s.Id,
                     ApplicationCode = a.s.ApplicationCode,
                     ApplyUser = a.s.ApplyUser,
                     ApplyUserDeptName = a.s.ApplyUserDeptName,
                     ApplyUserId = a.s.ApplyUserId,
                     ApplyUserDeptId = a.s.ApplyUserDeptId,
                     CostCenter = a.s.CostCenter,
                     CostCenterCode = a.s.CostCenterCode,
                     BudgetCode = a.s.BudgetCode,
                     SubBudgetCode = a.s.SubBudgetCode,
                     ProductName = a.s.ProductName,
                     ProductMCode = a.s.ProductMCode,
                     ProductQty = a.s.ProductQty,
                     Status = a.s.Status,
                     ApplyTime = a.s.CreationTime,
                     ShippedQty = b.ShippedQtys,
                     WriteoffQty = b.WriteoffQtys,
                 })
                ;
            var count = await query.CountAsync();
            var datas = await query.OrderByDescending(a => a.ApplyTime).PagingIf(request).ToListAsync();
            //var result = ObjectMapper.Map<List<FOCApplication>, List<GetFocApplicationListResponseDto>>(datas);
            return new PagedResultDto<GetFocApplicationListResponseDto>(count, datas);
        }

        /// <summary>
        /// FOC敏感信息新增记录日志
        /// </summary>
        /// <param name="focApplication"></param>
        private void RecordCreateTrackEventLog(FOCApplication focApplication)
        {
            var _identityUserRepository = _serviceProvider.GetService<IRepository<IdentityUser, Guid>>();
            var operatorName = _identityUserRepository.FirstOrDefaultAsync(f => f.Id == CurrentUser.Id).Result?.Name;
            string dateString = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fffffff");
            //记录FOC申请收货人地址信息变更记录
            if (!string.IsNullOrEmpty(focApplication.ConsigneeAddress))
            {
                var dict = new Dictionary<string, string>
                {
                    { "Applicationld", focApplication.Id.ToString()  },
                    { "OperatorName", operatorName },
                    { "Date", dateString },
                    { "BeforeChange", string.Empty },
                    { "AfterChange", focApplication.ConsigneeAddress }
                };
                LazyServiceProvider.LazyGetService<IApplicationInsightService>().TrackEvent(ApplicationInsightEventNames.ConsigneeAddressChange, dict);
            }
            //记录FOC申请收货人名称信息变更记录
            if (!string.IsNullOrEmpty(focApplication.ConsigneeName))
            {
                var dict = new Dictionary<string, string>
                {
                    { "Applicationld", focApplication.Id.ToString()  },
                    { "OperatorName", operatorName },
                    { "Date", dateString },
                    { "BeforeChange", string.Empty },
                    { "AfterChange", focApplication.ConsigneeName }
                };
                LazyServiceProvider.LazyGetService<IApplicationInsightService>().TrackEvent(ApplicationInsightEventNames.ConsigneeNameChange, dict);
            }
            //记录FOC申请收货人手机信息变更记录
            if (!string.IsNullOrEmpty(focApplication.ConsigneePhone))
            {
                var dict = new Dictionary<string, string>
                {
                    { "Applicationld", focApplication.Id.ToString()  },
                    { "OperatorName", operatorName },
                    { "Date", dateString },
                    { "BeforeChange", string.Empty },
                    { "AfterChange", focApplication.ConsigneePhone }
                };
                LazyServiceProvider.LazyGetService<IApplicationInsightService>().TrackEvent(ApplicationInsightEventNames.ConsigneePhoneChange, dict);
            }
        }

        /// <summary>
        /// 创建FOC申请单
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> CreateFocApplicationAsync(CreateFocApplicationRequest request)
        {
            var orgs = await LazyServiceProvider.LazyGetService<IDataverseService>().GetOrganizations(request.ApplyUserDeptId.ToString());
            if (!orgs.Any())
                return MessageResult.FailureResult("未找到申请人部门信息");

            var focApplicationRepository = LazyServiceProvider.LazyGetService<IFocApplicationRepository>();
            var focApplicationDetailsRepository = LazyServiceProvider.LazyGetService<IFocApplicationDetailRepository>();
            var focApplication = ObjectMapper.Map<CreateFocApplicationRequest, FOCApplication>(request);
            if (CurrentUser.Id.HasValue)
            {
                focApplication.ApplyUserId = CurrentUser.Id.Value;
                focApplication.ApplyUser = CurrentUser.Name;
            }

            var messageResult = await HandleFocApplicationAsync(focApplication, request, orgs.First());
            if (!messageResult.Success)
                return messageResult;

            focApplication = messageResult.Data as FOCApplication;
            await InsertAndGenerateSerialNoAsync(focApplicationRepository, focApplication, "F");

            RecordCreateTrackEventLog(focApplication);

            //FOC明细保存
            messageResult = await HandleFocApplicationDetailAsync(focApplication, request.DetailItems);
            if (!messageResult.Success)
                return messageResult;
            var focApplicationDetailData = messageResult.Data as Tuple<List<FOCApplicationDetail>, List<FOCApplicationDetail>>;
            //var focApplicationDetails = ObjectMapper.Map<List<CreateUpdateFocApplicationDetailRequest>, List<FOCApplicationDetail>>(request.DetailItems);
            //int rowId = 1;
            //focApplicationDetails.ForEach(a =>
            //{
            //    a.RowId = rowId++;
            //    a.ParentID = focApplication.Id;
            //});
            //await focApplicationDetailsRepository.InsertManyAsync(focApplicationDetails);

            return MessageResult.SuccessResult(new Tuple<FOCApplication, List<FOCApplicationDetail>, List<FOCApplicationDetail>>(focApplication, focApplicationDetailData.Item1, focApplicationDetailData.Item2));
        }

        /// <summary>
        /// 修改FOC申请单
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> UpdateFocApplicationAsync(UpdateFocApplicationRequest request)
        {
            if (!request.Id.HasValue)
                return MessageResult.FailureResult($"申请单Id不能为空");

            var focApplicationRepository = LazyServiceProvider.LazyGetService<IFocApplicationRepository>();
            var focApplication = await focApplicationRepository.FindAsync(request.Id.Value);
            if (focApplication == null)
                return MessageResult.FailureResult($"未找到Id为{request.Id}的FOC申请单");

            #region 避免代理人重新提交申请单时，将原始发起人的部门覆盖

            //只能在Draft状态时，才允许修改发起部门
            if (focApplication.Status != 0)
            {
                request.ApplyUserDeptId = focApplication.ApplyUserDeptId;
                request.ApplyUserDeptName = focApplication.ApplyUserDeptName;
            }

            #endregion

            var orgs = await LazyServiceProvider.LazyGetService<IDataverseService>().GetOrganizations(request.ApplyUserDeptId.ToString());
            if (!orgs.Any())
                return MessageResult.FailureResult("未找到申请人部门信息");

            focApplication = ObjectMapper.Map(request, focApplication);

            var messageResult = await HandleFocApplicationAsync(focApplication, request, orgs.First());
            if (!messageResult.Success)
                return messageResult;

            focApplication = messageResult.Data as FOCApplication;
            await focApplicationRepository.UpdateAsync(focApplication);

            //FOC明细保存
            messageResult = await HandleFocApplicationDetailAsync(focApplication, request.DetailItems);
            if (!messageResult.Success)
                return messageResult;

            //item1为数据库原始数据，item2为原始数据+新增数据
            var focApplicationDetailData = messageResult.Data as Tuple<List<FOCApplicationDetail>, List<FOCApplicationDetail>>;
            var focApplicationDetailRepository = LazyServiceProvider.LazyGetService<IFocApplicationDetailRepository>();
            if (focApplicationDetailData.Item2?.Any() == true)
            {
                //删除不存在的行
                var needDeleteDatas = focApplicationDetailData.Item1.Where(a => !request.DetailItems.Any(a1 => a1.Id == a.Id)).ToArray();
                if (needDeleteDatas.Any())
                {
                    var needDeleteIds = needDeleteDatas.Select(a => a.Id).ToArray();
                    //提交之后不能删除明细行数据，只能反冲
                    if (!focApplication.ApplyTime.HasValue)
                    {
                        await focApplicationDetailRepository.DeleteManyAsync(needDeleteDatas);
                    }
                    focApplicationDetailData.Item2.RemoveAll(a => needDeleteIds.Contains(a.Id));
                }
            }
            else
            {
                await focApplicationDetailRepository.DeleteAsync(a => a.ParentID == focApplication.Id);
            }
            await CurrentUnitOfWork.SaveChangesAsync();
            return MessageResult.SuccessResult(new Tuple<FOCApplication, List<FOCApplicationDetail>, List<FOCApplicationDetail>>(focApplication, focApplicationDetailData.Item1, focApplicationDetailData.Item2));
        }

        /// <summary>
        /// 保存草稿FOC申请单
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> SaveDraftFocApplicationAsync(CreateFocApplicationRequest request)
        {
            var orgs = await LazyServiceProvider.LazyGetService<IDataverseService>().GetOrganizations(request.ApplyUserDeptId.ToString());
            if (!orgs.Any())
                return MessageResult.FailureResult("未找到申请人部门信息");

            var focApplicationRepository = LazyServiceProvider.LazyGetService<IFocApplicationRepository>();
            var focApplicationDetailsRepository = LazyServiceProvider.LazyGetService<IFocApplicationDetailRepository>();
            var focApplication = ObjectMapper.Map<CreateFocApplicationRequest, FOCApplication>(request);
            if (CurrentUser.Id.HasValue)
            {
                focApplication.ApplyUserId = CurrentUser.Id.Value;
                focApplication.ApplyUser = CurrentUser.Name;
            }

            var messageResult = await HandleDraftFocApplicationAsync(focApplication, request, orgs.First());
            if (!messageResult.Success)
                return messageResult;

            focApplication = messageResult.Data as FOCApplication;
            await InsertAndGenerateSerialNoAsync(focApplicationRepository, focApplication, "F");

            RecordCreateTrackEventLog(focApplication);

            //FOC草稿明细保存
            messageResult = await HandleDraftFocApplicationDetailAsync(focApplication, request.DetailItems);
            if (!messageResult.Success)
                return messageResult;
            var focApplicationDetailData = messageResult.Data as Tuple<List<FOCApplicationDetail>, List<FOCApplicationDetail>>;

            return MessageResult.SuccessResult(new Tuple<FOCApplication, List<FOCApplicationDetail>, List<FOCApplicationDetail>>(focApplication, focApplicationDetailData.Item1, focApplicationDetailData.Item2));
        }

        /// <summary>
        /// 修改草稿FOC申请单
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> UpdateDraftFocApplicationAsync(UpdateFocApplicationRequest request)
        {
            if (!request.Id.HasValue)
                return MessageResult.FailureResult($"申请单Id不能为空");

            var focApplicationRepository = LazyServiceProvider.LazyGetService<IFocApplicationRepository>();
            var focApplication = await focApplicationRepository.FindAsync(request.Id.Value);
            if (focApplication == null)
                return MessageResult.FailureResult($"未找到Id为{request.Id}的FOC申请单");

            #region 避免代理人重新提交申请单时，将原始发起人的部门覆盖

            //只能在Draft状态时，才允许修改发起部门
            if (focApplication.Status != 0)
            {
                request.ApplyUserDeptId = focApplication.ApplyUserDeptId;
                request.ApplyUserDeptName = focApplication.ApplyUserDeptName;
            }

            #endregion

            var orgs = await LazyServiceProvider.LazyGetService<IDataverseService>().GetOrganizations(request.ApplyUserDeptId.ToString());
            if (!orgs.Any())
                return MessageResult.FailureResult("未找到申请人部门信息");

            focApplication = ObjectMapper.Map(request, focApplication);

            var messageResult = await HandleDraftFocApplicationAsync(focApplication, request, orgs.First());
            if (!messageResult.Success)
                return messageResult;

            focApplication = messageResult.Data as FOCApplication;
            await focApplicationRepository.UpdateAsync(focApplication);

            //FOC草稿明细保存
            messageResult = await HandleDraftFocApplicationDetailAsync(focApplication, request.DetailItems);
            if (!messageResult.Success)
                return messageResult;

            //item1为数据库原始数据，item2为原始数据+新增数据
            var focApplicationDetailData = messageResult.Data as Tuple<List<FOCApplicationDetail>, List<FOCApplicationDetail>>;
            var focApplicationDetailRepository = LazyServiceProvider.LazyGetService<IFocApplicationDetailRepository>();
            if (focApplicationDetailData.Item2?.Any() == true)
            {
                //删除不存在的行
                var needDeleteDatas = focApplicationDetailData.Item1.Where(a => !request.DetailItems.Any(a1 => a1.Id == a.Id)).ToArray();
                if (needDeleteDatas.Any())
                {
                    var needDeleteIds = needDeleteDatas.Select(a => a.Id).ToArray();
                    //提交之后不能删除明细行数据，只能反冲
                    if (!focApplication.ApplyTime.HasValue)
                    {
                        await focApplicationDetailRepository.DeleteManyAsync(needDeleteDatas);
                    }
                    focApplicationDetailData.Item2.RemoveAll(a => needDeleteIds.Contains(a.Id));
                }
            }
            else
            {
                await focApplicationDetailRepository.DeleteAsync(a => a.ParentID == focApplication.Id);
            }
            await CurrentUnitOfWork.SaveChangesAsync();
            return MessageResult.SuccessResult(new Tuple<FOCApplication, List<FOCApplicationDetail>, List<FOCApplicationDetail>>(focApplication, focApplicationDetailData.Item1, focApplicationDetailData.Item2));
        }

        /// <summary>
        /// 删除FOC申请单
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public async Task<MessageResult> DeleteFocApplicationAsync(Guid Id)
        {
            var focApplicationResponse = LazyServiceProvider.LazyGetService<IFocApplicationRepository>();
            await focApplicationResponse.DeleteAsync(Id);
            return MessageResult.SuccessResult("Succsee");
        }

        /// <summary>
        /// 提交FOC申请单
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> SubmitFocApplicationAsync(UpdateFocApplicationRequest request)
        {
            MessageResult result;
            if (request.Id.HasValue)
                result = await UpdateFocApplicationAsync(request);
            else
                result = await CreateFocApplicationAsync(request);
            if (!result.Success)
            {
                throw new Exception(result.Message);
            }
            var data = result.Data as Tuple<FOCApplication, List<FOCApplicationDetail>, List<FOCApplicationDetail>>;
            var focApplication = data.Item1;
            var originalApplicationDetails = data.Item2;
            var sticketApplicationDetails = data.Item3;

            //验证预算
            result = await ValidateBudgetAsync(focApplication, sticketApplicationDetails);
            if (!result.Success)
                return result;
            var budgetResult = result.Data as UseFocBudgetRequestDto;

            //取费用性质Approval Number
            var focDetails = await TransferNatureApprovalNumber(sticketApplicationDetails, focApplication.NatureId.Value, focApplication.ProductMCode);

            //提交审批
            var exemptType = EnumUtil.GetEnumIdValues<WorkflowTypeName>().ToDictionary(a => (WorkflowTypeName)a.Key, a => a.Value);
            var isOk = await LazyServiceProvider.LazyGetService<IApproveService>().InitiateApprovalAsync(new CreateApprovalDto
            {
                Name = exemptType[WorkflowTypeName.FOCRequest],
                Department = focApplication.ApplyUserDeptId.ToString(),
                BusinessFormId = focApplication.Id.ToString(),
                BusinessFormNo = focApplication.ApplicationCode,
                BusinessFormName = NameConsts.FOCApplication,
                Submitter = CurrentUser.Id?.ToString(),
                OriginalApprovalId = focApplication.ApplyUserId,
                WorkflowType = WorkflowTypeName.FOCRequest,
                FormData = JsonConvert.SerializeObject(new
                {
                    focApplication.ApplicationCode,
                    StickteDtails = focDetails.Select(a => new { a.TotalAmount, a.ApprovalNumber }),
                    IsCss = true
                })
            });

            if (!isOk)
                return MessageResult.FailureResult("提交审批失败");
            else
            {
                //扣减预算
                result = await LazyServiceProvider.LazyGetService<IFocSubbudgetService>().UseFocSubbudgetAsync(budgetResult);

                if (result.Success)
                {
                    //更新申请的状态为“审批中”
                    focApplication.Status = FOCStatus.Approving;
                    focApplication.ApplyTime = DateTime.Now;
                    await LazyServiceProvider.LazyGetService<IFocApplicationRepository>().UpdateAsync(focApplication);
                }
            }

            return result;
        }

        /// <summary>
        /// 检查FOC申请单金额是否大于限定值
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> CheckFocApplicationAmountAsync(UpdateFocApplicationRequest request)
        {

            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            //获取美刀货币配置
            var usdCurrencies = await dataverseService.GetCurrencyConfig(DictionaryType.CurrencyItems.USD);
            var usdCurrency = usdCurrencies.First();
            if (usdCurrency == null)
            {
                return MessageResult.FailureResult("未找到美刀货币配置");
            }
            //获取产品成本值
            var productCosts = await dataverseService.GetProductCostAsync();
            var productCost = productCosts.Where(a => a.ProductMcode == request.ProductMCode).First();
            if (productCost == null)
            {
                return MessageResult.FailureResult($"未找到{request.ProductMCode}产品成本值");
            }

            var focSumQty = request.DetailItems.Sum(a => a.ProductQuantity);

            decimal sumAmount = decimal.Round(productCost.CostValue * focSumQty / (decimal)usdCurrency.PlanRate, 4);

            var focRemindAmt = (await dataverseService.GetDictionariesAsync(DictionaryType.FOCRemindAmount)).FirstOrDefault();
            if (focRemindAmt != null)
            {
                var remindAmt = Convert.ToDecimal(focRemindAmt.Name);
                bool isExceedAmount = false;
                if (sumAmount >= remindAmt)
                {
                    isExceedAmount = true;
                }
                var checkResult = new FocApplicationAmountCheckResponseDto
                {
                    IsExceedAmount = isExceedAmount,
                    SumAmount = sumAmount,
                };
                return MessageResult.SuccessResult(checkResult);
            }
            else
            {
                return MessageResult.FailureResult($"未设置FOC提醒金额");
            }
        }

        /// <summary>
        /// FOC申请添加产品
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> CreateFocApplicationProductDetailAsync(CreateFocApplicationProductDetailRequest request)
        {
            var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");
            var focApplicationRepository = LazyServiceProvider.LazyGetService<IFocApplicationRepository>();
            var focProductDetailRepository = LazyServiceProvider.LazyGetService<IFocApplicationProductDetailRepository>();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var expenseNature = await dataverseService.GetCostNatureAsync();

            var focApplicationQuery = await focApplicationRepository.GetQueryableAsync();
            var focApplication = focApplicationQuery.FirstOrDefault(a => a.Id == request.Id);
            if (focApplication != null)
            {
                focApplication.ReceivingCode = request.ReceivingCode;
                focApplication.ConsigneeAddress = !string.IsNullOrEmpty(request.ConsigneeAddress) ? AesHelper.Encryption(request.ConsigneeAddress, insightKey) : string.Empty;
                focApplication.ConsigneeName = !string.IsNullOrEmpty(request.ConsigneeName) ? AesHelper.Encryption(request.ConsigneeName, insightKey) : string.Empty;
                focApplication.ConsigneePhone = !string.IsNullOrEmpty(request.ConsigneePhone) ? AesHelper.Encryption(request.ConsigneePhone, insightKey) : string.Empty;

                focApplication.Status = FOCStatus.SOICanceling;

                //费用性质为核销后随单，更新状态为已完成
                var soiVerifyType = expenseNature.FirstOrDefault(f => f.Id == focApplication.NatureId)?.SoiVerifyType;
                if (soiVerifyType == SoiVerifyType.VerifiedWithOrder)
                {
                    focApplication.Status = FOCStatus.Shipped;
                }

                await focApplicationRepository.UpdateAsync(focApplication);
            }
            else
            {
                return MessageResult.FailureResult("未找到FOC申请单信息");
            }
            var isMultipProdscode = request.ProductDetailItems.GroupBy(g => new { g.FOCDetailId, g.ProductSCode }, (k, v) => new { k, count = v.Count() }).Any(a => a.count > 1);
            if (isMultipProdscode) return MessageResult.FailureResult("同一个地址下面不能有多个相同产品Code");
            //FOC明细产品保存
            var focProductDetail = ObjectMapper.Map<List<CreateFocProductDetailRequest>, List<FOCApplicationProductDetail>>(request.ProductDetailItems);
            int rowId = 1;
            focProductDetail.ForEach(a =>
            {
                a.RowId = rowId++;
            });
            await focProductDetailRepository.InsertManyAsync(focProductDetail, true);
            var msg = await PushFOCForSOIAsync(request.Id);
            if (!msg.Success) throw new Exception(msg.Message);
            return msg;
        }

        /// <summary>
        /// 获取发货类型
        /// </summary>
        /// <returns></returns>
        public async Task<List<GetShippingTypeResponseDto>> GetShippingTypeListAsync()
        {
            var _dataverseService = _serviceProvider.GetService<IDataverseService>();
            var customerTypeList = (await _dataverseService.GetDictionariesAsync(DictionaryType.ShippingTypes)).ToList();
            var result = ObjectMapper.Map<List<DictionaryDto>, List<GetShippingTypeResponseDto>>(customerTypeList);
            return result;
        }

        /// <summary>
        /// 获取客户收货代码列表
        /// </summary>
        /// <returns></returns>
        public async Task<PagedResultDto<GetConsigneeResponseDto>> GetConsigneeListAsync(GetConsigneeRequestDto request)
        {
            var queryEst = await LazyServiceProvider.GetService<IIntermediateEstRepository>().GetQueryableAsync();
            //var dataverseService = _serviceProvider.GetService<IDataverseService>();

            decimal customerCode = 0;
            if (!string.IsNullOrEmpty(request.CustomerCode))
            {
                if (request.CustomerCode.Contains('-'))
                {
                    string[] code = request.CustomerCode.Split('-');
                    customerCode = decimal.Parse(code[1]);
                }
                else
                    customerCode = decimal.Parse(request.CustomerCode);
            }

            ////获取FOC终端仓库
            //var warehouseList = await dataverseService.GetDictionariesAsync(DictionaryType.FOCTerminalsWarehouse);
            //var whCodeList = warehouseList.Select(a => a.Code).ToList();

            var result = queryEst
                .Where(a => a.Tcust == customerCode)
                .Select(a => new GetConsigneeResponseDto
                {
                    ReceivingCode = a.Tcust.ToString() + "-" + a.Tship.ToString(),
                    Name = a.Tname,
                    ConsigneeAddress = a.Tadr1 + a.Tadr2 + a.Tadr3,
                    ConsigneeName = a.Tatn,
                    ConsigneePhone = a.Tphone
                }).ToList();

            var responses = new List<GetConsigneeResponseDto>();
            foreach (var item in result)
            {
                //if (whCodeList.Contains(item.ReceivingCode))
                //{
                //    var warehouse = warehouseList.Where(a => a.Code == item.ReceivingCode).FirstOrDefault();
                //    var data = JsonConvert.DeserializeObject<TerminalConsigneeDto>(warehouse.Name);
                //    item.ConsigneeAddress = data.Address;
                //    item.ConsigneePhone = data.Phone;
                //    item.ConsigneeName = data.Consignee;
                //}
                responses.Add(new GetConsigneeResponseDto
                {
                    ReceivingCode = item.ReceivingCode,
                    Name = item.Name,
                    ConsigneeAddress = item.ConsigneeAddress,
                    ConsigneeName = item.ConsigneeName,
                    ConsigneePhone = item.ConsigneePhone,
                });
            }
            var count = responses.Count();
            var datas = responses.Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToArray();

            return new PagedResultDto<GetConsigneeResponseDto>(count, datas);
        }

        /// <summary>
        /// 获取终端列表
        /// </summary>
        /// <returns></returns>
        public async Task<List<GetFOCTerminalListResponseDto>> GetFOCTerminalListAsync()
        {
            var dataverseService = _serviceProvider.GetService<IDataverseService>();
            var terminalList = (await dataverseService.GetDictionariesAsync(DictionaryType.FocTerminals)).ToList();
            ////获取CRM EC仓库
            //var warehouseList = await dataverseService.GetDictionariesAsync(DictionaryType.FOCTerminalsWarehouse);
            //var warehouse = warehouseList.FirstOrDefault();
            //string warehouseName = string.Empty;
            //if (warehouse != null)
            //    warehouseName = warehouse.Type;
            var result = ObjectMapper.Map<List<DictionaryDto>, List<GetFOCTerminalListResponseDto>>(terminalList);
            //foreach (var item in result)
            //{
            //    if (item.Name == warehouseName)
            //    {
            //        var data = JsonConvert.DeserializeObject<TerminalConsigneeDto>(warehouse.Name);
            //        item.ReceivingCode = warehouse.Code;
            //        item.ConsigneeAddress = data.Address;
            //        item.ConsigneePhone = data.Phone;
            //        item.ConsigneeName = data.Consignee;
            //    }
            //}
            return result;
        }

        /// <summary>
        /// FOC物流填写
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> CreateFocProductLogisticsAsync(List<CreateFocLogisticsRequestDto> request)
        {
            var focApplicationRepository = LazyServiceProvider.LazyGetService<IFocApplicationRepository>();
            var focApplicationQuery = await focApplicationRepository.GetQueryableAsync();
            var focApplicationDetailRepository = LazyServiceProvider.LazyGetService<IFocApplicationDetailRepository>();
            var focApplicationDetailQuery = await focApplicationDetailRepository.GetQueryableAsync();
            var focProductDetailRepository = LazyServiceProvider.LazyGetService<IFocApplicationProductDetailRepository>();
            var focProductDetailQuery = await focProductDetailRepository.GetQueryableAsync();
            var focLogisticsRepository = LazyServiceProvider.LazyGetService<IFocApplicationLogisticsDetailRepository>();
            var focLogisticsQuery = await focLogisticsRepository.GetQueryableAsync();

            var focDetailId = request.FirstOrDefault().FOCDetailId;
            var focApplicationId = focApplicationDetailQuery.FirstOrDefault(a => a.Id == focDetailId).ParentID;

            var result = await ValidateFocLogisticsDetails(request);
            if (!result.Success)
            {
                result.Data = null;
                return result;
            }

            int sumShippedQty = 0;

            bool isSendEmail = false;
            //bool isSOIVerification = false;
            //代发货数量
            List<int> pandingShippedQtys = [];
            //核销状态
            List<VerificationStatusEnum> VerificationStatus = [];
            foreach (var item in request)
            {
                int sumPrdShippedQty = 0;
                //FOC物流明细保存
                foreach (var productItem in item.ProductDetailItems)
                {
                    var focLogisticsDetailsMap = ObjectMapper.Map<List<CreateApplicationLogisticsRequest>, List<FOCApplicationLogisticsDetail>>(productItem.LogisticsDetailItems);

                    //var logisticsDetailList = focLogisticsQuery.Where(a => a.FOCDetailId == item.FOCDetailId && a.ProductSCode == productItem.ProductSCode).ToList();
                    //if (logisticsDetailList.Count > 0)
                    //    await focLogisticsRepository.DeleteDirectAsync(a => logisticsDetailList.Select(a => a.Id).Contains(a.Id));

                    //int rowId = 1;
                    //focLogisticsDetailsMap.ForEach(a =>
                    //{
                    //    a.RowId = rowId++;
                    //    a.ProductSCode = productItem.ProductSCode;
                    //    a.ProductName = productItem.ProductName;
                    //    a.FOCDetailId = item.FOCDetailId;
                    //});
                    //await focLogisticsRepository.InsertManyAsync(focLogisticsDetailsMap);
                    if (productItem.LogisticsDetailItems != null)
                    {
                        var logisticsDetailList = new List<FOCApplicationLogisticsDetail>();
                        foreach (var logisticsItem in productItem.LogisticsDetailItems)
                        {
                            var logisticsDetail = focLogisticsQuery.Where(a => a.Id == logisticsItem.Id).FirstOrDefault();
                            if (logisticsDetail != null)
                            {
                                logisticsDetail.ShippedQty = logisticsItem.ShippedQty;
                                logisticsDetail.LogisticsNo = logisticsItem.LogisticsNo;
                                logisticsDetail.ShipmentDate = logisticsItem.ShipmentDate;
                                logisticsDetailList.Add(logisticsDetail);
                            }
                        }
                        await focLogisticsRepository.UpdateManyAsync(logisticsDetailList);

                        //修改已发货数量
                        var productDetail = focProductDetailQuery.FirstOrDefault(a => a.FOCDetailId == item.FOCDetailId && a.ProductSCode == productItem.ProductSCode);
                        if (productDetail != null)
                        {
                            var shippedQty = productItem.LogisticsDetailItems.Where(a => a.ShippedQty.HasValue).Sum(a => a.ShippedQty).Value;
                            //修改已发货数量
                            productDetail.ShippedQty = shippedQty;
                            sumPrdShippedQty += shippedQty;
                            sumShippedQty += shippedQty;


                            await focProductDetailRepository.UpdateAsync(productDetail);

                            //if (productDetail.VerificationStatus == VerificationStatusEnum.Pending || productDetail.VerificationStatus == VerificationStatusEnum.Partial)
                            //{
                            //    if (productDetail.WriteoffQty == shippedQty)
                            //        isSOIVerification = true;
                            //}
                            //待发货数量
                            var pandingShippedQty = productDetail.WriteoffQty - productDetail.ShippedQty;
                            pandingShippedQtys.Add(pandingShippedQty);
                            VerificationStatus.Add(productDetail.VerificationStatus);
                        }

                    }
                    else
                    {
                        var pandingShippedQty = productItem.WriteoffQty - productItem.ShippedQty;
                        pandingShippedQtys.Add(pandingShippedQty);
                        VerificationStatus.Add(productItem.VerificationStatus);
                    }
                }
                //更新发货状态
                //var focDetailId = request.FirstOrDefault().FOCDetailId;
                var focDetails = focApplicationDetailQuery.FirstOrDefault(a => a.Id == item.FOCDetailId);
                if (sumPrdShippedQty > 0)
                {
                    var productQty = focDetails.ProductQuantity;
                    if (sumPrdShippedQty < productQty)
                        focDetails.ShippingStatus = FOCShippingStatus.PartialShipped;
                    else if (sumPrdShippedQty == productQty)
                        focDetails.ShippingStatus = FOCShippingStatus.Shipped;
                    isSendEmail = true;
                    await focApplicationDetailRepository.UpdateAsync(focDetails);
                }
            }

            /*           FOCApplication focData = null;
                       //查询FOC申请单产品总数量
                       var focSumProductQty = focApplicationDetailQuery.Where(a => a.ParentID == focApplicationId).Sum(a => a.ProductQuantity);
                       var focApplication = focApplicationQuery.FirstOrDefault(a => a.Id == focApplicationId);
                       if (sumShippedQty == focSumProductQty)
                       {
                           if (focApplication != null)
                           {
                               focApplication.Status = FOCStatus.Shipped;
                               isSendEmail = true;
                               await focApplicationRepository.UpdateAsync(focApplication);
                           }
                           focData = focApplication;
                       }
                       else
                       {
                           //更新申请单状态为SOI核销中
                           if (isSOIVerification == true && focApplication != null)
                           {
                               focApplication.Status = FOCStatus.SOICanceling;
                               await focApplicationRepository.UpdateAsync(focApplication);
                           }
                           #region 20250427 修改
                           else if (sumShippedQty < focSumProductQty)
                           {
                               if (focApplication != null)
                               {
                                   focApplication.Status = FOCStatus.PendingShipment;
                                   await focApplicationRepository.UpdateAsync(focApplication);
                               }
                           }
                           #endregion
                       }*/
            FOCApplication focData = null;
            var focApplication = focApplicationQuery.FirstOrDefault(a => a.Id == focApplicationId);
            if (pandingShippedQtys.Any(a => a > 0))
                focApplication.Status = FOCStatus.PendingShipment;
            else if (VerificationStatus.All(a => a == VerificationStatusEnum.Completed || a == VerificationStatusEnum.Expired))
            {
                focApplication.Status = FOCStatus.Shipped;
                isSendEmail = true;
                focData = focApplication;
            }
            else
                focApplication.Status = FOCStatus.SOICanceling;

            await focApplicationRepository.UpdateAsync(focApplication);
            if (isSendEmail)
            {
                if (focData == null) focData = focApplicationQuery.FirstOrDefault(a => a.Id == focApplicationId);

                await SendChooseProductEmail([focData]);
            }
            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 获取物流发货记录
        /// </summary>
        /// <param name="focDetailId"></param>
        /// <returns></returns>
        public async Task<List<GetFocProductLogisticsResponseDto>> GetFocProductLogisticsAsync(Guid focDetailId)
        {
            var focLogisticsQuery = await LazyServiceProvider.LazyGetService<IFocApplicationLogisticsDetailRepository>().GetQueryableAsync();
            var queryList = focLogisticsQuery.Where(a => a.FOCDetailId == focDetailId).ToList();

            var result = ObjectMapper.Map<List<FOCApplicationLogisticsDetail>, List<GetFocProductLogisticsResponseDto>>(queryList);

            return result;
        }

        /// <summary>
        /// 获取FOC物流信息列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<PagedResultDto<GetFocLogisticsListResponseDto>> GetFocLogisticsListAsync(GetFocLogisticsListRequestDto request)
        {
            var focApplicationQuery = await LazyServiceProvider.LazyGetService<IFocApplicationRepository>().GetQueryableAsync();
            var focApplicationDetailQuery = await LazyServiceProvider.LazyGetService<IFocApplicationDetailRepository>().GetQueryableAsync();
            var focApplicationProductQuery = await LazyServiceProvider.LazyGetService<IFocApplicationProductDetailRepository>().GetQueryableAsync();

            var query = focApplicationQuery
                .Where(a => /*a.Status == FOCStatus.Shipping ||*/ a.Status == FOCStatus.Shipped)
                .WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), a => a.ApplicationCode.Contains(request.ApplicationCode))
                .WhereIf(!string.IsNullOrEmpty(request.ProductMCode), a => a.ProductMCode.Contains(request.ProductMCode))
                .WhereIf(request.Status.HasValue, a => a.Status == request.Status)
                .WhereIf(request.StartDate.HasValue, a => a.ApplyTime >= request.StartDate)
                .WhereIf(request.EndDate.HasValue, a => a.ApplyTime <= request.EndDate.Value.AddDays(1))
                .Join(focApplicationDetailQuery, a => a.Id, a => a.ParentID, (a, b) => new { foc = a, focDetail = b })
                .Join(focApplicationProductQuery, a => a.focDetail.Id, a => a.FOCDetailId, (a, b) => new { a.foc, a.focDetail, productDetail = b })
                //.WhereIf(!string.IsNullOrEmpty(request.BpcsOrderNo), a => a.focDetail.BpcsOrderNo.Contains(request.BpcsOrderNo))
                .WhereIf(request.ShippingStatus.HasValue, a => a.focDetail.ShippingStatus == request.ShippingStatus)
                .Select(a => new GetFocLogisticsListResponseDto()
                {
                    FocDetailId = a.focDetail.Id,
                    ApplicationCode = a.foc.ApplicationCode,
                    RowId = a.focDetail.RowId,
                    ProductName = a.foc.ProductName,
                    ProductMCode = a.foc.ProductMCode,
                    ConsigneeAddress = a.focDetail.ConsigneeAddress,
                    ProductSCode = a.productDetail.ProductSCode,
                    //BpcsOrderNo = a.focDetail.BpcsOrderNo,
                    ProductQty = a.productDetail.ProductQty,
                    WriteoffQty = a.productDetail.WriteoffQty,
                    ShippedQty = a.productDetail.ShippedQty,
                    SubmitTime = a.foc.ApplyTime,
                    ShippingStatus = a.focDetail.ShippingStatus,
                    ShippingStatusText = a.focDetail.ShippingStatus.GetDescription(),
                    ApplyStatus = a.foc.Status,
                    ApplyStatusText = a.foc.Status.GetDescription()
                });

            var count = query.Count();
            var datas = query.Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToArray();

            return new PagedResultDto<GetFocLogisticsListResponseDto>(count, datas);
        }

        /// <summary>
        /// 解析批量上传物流信息excel
        /// </summary>
        /// <param name="excelDtos"></param>
        /// <returns></returns>
        public async Task<MessageResult> CheckCreateFocProductLogisticsExcelAsync(IEnumerable<CreateFocProductLogisticsExcelDto> excelDtos)
        {
            var productDetailQuery = await LazyServiceProvider.LazyGetService<IProductDetailsRepository>().GetQueryableAsync();
            var productSCodeList = excelDtos.Select(a => a.ProductSCode).Where(a => !string.IsNullOrWhiteSpace(a)).ToList();
            var productDetails = productDetailQuery.Where(a => productSCodeList.Contains(a.ProductSCode));

            var focApplicationQuery = await LazyServiceProvider.LazyGetService<IFocApplicationRepository>().GetQueryableAsync();
            var applicationCodeList = excelDtos.Select(a => a.ApplicationCode).Where(a => !string.IsNullOrWhiteSpace(a)).ToList();
            var focApplicationCodes = focApplicationQuery.Where(a => applicationCodeList.Contains(a.ApplicationCode));
            var focApplicationIds = focApplicationQuery.Select(a => a.Id).ToList();

            var focApplicationDetailQuery = await LazyServiceProvider.LazyGetService<IFocApplicationDetailRepository>().GetQueryableAsync();
            var focApplicationDetails = focApplicationDetailQuery.Where(a => focApplicationIds.Contains(a.ParentID));
            var focProductDetailRepository = LazyServiceProvider.LazyGetService<IFocApplicationProductDetailRepository>();
            var focProductDetailQuery = await focProductDetailRepository.GetQueryableAsync();
            var focLogisticsRepository = LazyServiceProvider.LazyGetService<IFocApplicationLogisticsDetailRepository>();
            var focLogisticsQuery = await focLogisticsRepository.GetQueryableAsync();

            var bpcsCodes = excelDtos.Select(a => a.BpcsOrderNo).Where(a => !string.IsNullOrWhiteSpace(a)).ToList();
            var bpcsLogisticsQuery = focLogisticsQuery.Where(a => bpcsCodes.Contains(a.BpcsOrderNo));

            var query = bpcsLogisticsQuery
                .Join(focApplicationDetailQuery, a => a.FOCDetailId, a => a.Id, (a, b) => new { logistics = a, focDetail = b })
                .Join(focApplicationQuery, a => a.focDetail.ParentID, a => a.Id, (a, b) => new { a.logistics, a.focDetail, foc = b })
                .Select(a => new
                {
                    FocId = a.foc.Id,
                    a.foc.ApplicationCode,
                    LogisticsId = a.logistics.Id,
                    a.logistics.FOCDetailId,
                    a.logistics.BpcsOrderNo,
                    a.logistics.BatchNo,
                }).ToList();

            List<AnalyzeFocProductLogisticsExcelResponseDto> success = new();
            List<AnalyzeFocProductLogisticsExcelResponseDto> error = new();
            FOCStatus[] shippingStatus = [FOCStatus.PendingShipment, FOCStatus.SOICanceling, FOCStatus.Shipped];

            var rowNo = 5;
            var sumShippedQty = 0;
            foreach (var item in excelDtos)
            {
                AnalyzeFocProductLogisticsExcelResponseDto analyze = new();
                analyze.No = ++rowNo;
                var message = string.Empty;

                if (string.IsNullOrEmpty(item.ApplicationCode))
                    message += "请填写FOC申请单号;";

                if (string.IsNullOrEmpty(item.RowNo))
                    message += "请填写FOC申请单行号;";

                if (string.IsNullOrEmpty(item.ProductSCode))
                    message += "请填写产品S Code;";
                else
                {
                    var productDetail = productDetails.FirstOrDefault(a => a.ProductSCode == item.ProductSCode);
                    if (productDetail == null)
                    {
                        message += "产品S Code不存在;";
                    }
                }

                if (string.IsNullOrEmpty(item.BpcsOrderNo))
                    message += "请填写BPCS单号;";

                if (string.IsNullOrEmpty(item.BatchNo))
                    message += "请填写产品批次号;";

                if (string.IsNullOrEmpty(item.ShippedQtyText))
                    message += "请填写发货数量;";
                else if (int.TryParse(item.ShippedQtyText, out int qty))
                {
                    if (qty > MaxQty) message += "超出发货数量填写范围;";
                    else if (qty < 0) message += "发货数量不能小于0;";
                    else analyze.ShippedQty = qty;
                }
                else message += "发货数量请填写数字;";

                if (string.IsNullOrEmpty(item.ShipmentDateText))
                    message += "请填写发货日期;";
                else if (DateTime.TryParse(item.ShipmentDateText, out DateTime shipmentDate))
                {
                    analyze.ShipmentDate = shipmentDate;
                }
                else message += "发货日期格式不正确;";

                if (string.IsNullOrEmpty(item.LogisticsNo))
                    message += "请填写物流单号;";

                var focApplication = focApplicationQuery.FirstOrDefault(a => a.ApplicationCode == item.ApplicationCode);
                if (focApplication == null)
                {
                    message += $"未找到申请单号信息;";
                }
                else
                {
                    if (!shippingStatus.Contains(focApplication.Status))
                        message += $"单据状态不支持发货;";
                    var focApplicationDetail = focApplicationDetails.FirstOrDefault(a => a.ParentID == focApplication.Id && a.RowId == int.Parse(item.RowNo));
                    if (focApplicationDetail == null)
                    {
                        message += $"未找到申请单对应行号信息;";
                    }
                    else
                    {
                        var focProductDetail = focProductDetailQuery.FirstOrDefault(a => a.FOCDetailId == focApplicationDetail.Id && a.ProductSCode == item.ProductSCode);
                        if (focProductDetail != null)
                        {
                            //var shippedQty = int.Parse(item.ShippedQtyText);
                            var excelDatas = excelDtos.Where(a => a.ApplicationCode == item.ApplicationCode && a.RowNo == item.RowNo && a.ProductSCode == item.ProductSCode);
                            sumShippedQty = excelDatas.ToList().Sum(a => int.Parse(a.ShippedQtyText));
                            //var pendingQty = focProductDetail.WriteoffQty - focProductDetail.ShippedQty;//待发货数量
                            var pendingQty = focProductDetail.WriteoffQty;//待发货数量
                            if (sumShippedQty > pendingQty)
                            {
                                message += $"发货数量超过上限;";
                            }
                        }
                        else
                        {
                            message += $"无法匹配发货地址或产品信息;";
                        }

                        //物流明细
                        var logisticsDetail = bpcsLogisticsQuery.FirstOrDefault(a => a.FOCDetailId == focApplicationDetail.Id && a.ProductSCode == item.ProductSCode && a.BatchNo == item.BatchNo);
                        if (logisticsDetail == null)
                        {
                            message += $"无法匹配BPCS单号和产品批次号的物流信息;";
                        }
                    }
                }

                //判断BPCS单号是否在其他申请单下
                //if (query.Count > 0)
                //{
                //    var bpcsLogisticsDatas = query.FirstOrDefault(a => a.BpcsOrderNo == item.BpcsOrderNo && a.ApplicationCode != item.ApplicationCode);
                //    if (bpcsLogisticsDatas != null)
                //        message += $"BPCS单号重复;";
                //}

                analyze.ApplicationCode = item.ApplicationCode;
                analyze.RowNo = item.RowNo;
                analyze.ProductSCode = item.ProductSCode;
                analyze.BpcsOrderNo = item.BpcsOrderNo;
                analyze.ShippedQtyText = item.ShippedQtyText;
                analyze.BatchNo = item.BatchNo;
                analyze.ShipmentDateText = item.ShipmentDateText;
                analyze.LogisticsNo = item.LogisticsNo;
                //analyze.Remark = item.Remark;

                if (string.IsNullOrEmpty(message))
                {
                    success.Add(analyze);
                }
                else
                {
                    analyze.Message = message;
                    error.Add(analyze);
                }
            }
            if (error.Count() > 0)
                return MessageResult.SuccessResult(new ImportDataResponseDto<AnalyzeFocProductLogisticsExcelResponseDto>(error, false));
            return MessageResult.SuccessResult(new ImportDataResponseDto<AnalyzeFocProductLogisticsExcelResponseDto>(success, true));
        }

        /// <summary>
        /// 批量上传物流信息
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> SubmitBatchCreateFocProductLogisticsAsync(ImportDataResponseDto<AnalyzeFocProductLogisticsExcelResponseDto> request)
        {
            var focApplicationRepository = LazyServiceProvider.LazyGetService<IFocApplicationRepository>();
            var focApplicationQuery = await focApplicationRepository.GetQueryableAsync();
            var applicationCodeList = request.Datas.Select(a => a.ApplicationCode).Where(a => !string.IsNullOrWhiteSpace(a)).ToList();
            var focApplicationCodes = focApplicationQuery.Where(a => applicationCodeList.Contains(a.ApplicationCode));
            var focApplicationIds = focApplicationQuery.Select(a => a.Id).ToList();

            var focApplicationDetailRepository = LazyServiceProvider.LazyGetService<IFocApplicationDetailRepository>();
            var focApplicationDetailQuery = await focApplicationDetailRepository.GetQueryableAsync();
            var focApplicationDetails = focApplicationDetailQuery.Where(a => focApplicationIds.Contains(a.ParentID));
            var focLogisticsRepository = LazyServiceProvider.LazyGetService<IFocApplicationLogisticsDetailRepository>();
            var focLogisticsQuery = await focLogisticsRepository.GetQueryableAsync();

            var focProductDetailRepository = LazyServiceProvider.LazyGetService<IFocApplicationProductDetailRepository>();
            var focProductDetailQuery = await focProductDetailRepository.GetQueryableAsync();

            var focLogisticsDetailRepository = LazyServiceProvider.LazyGetService<IFocApplicationLogisticsDetailRepository>();
            var focLogisticsDetailQuery = await focLogisticsDetailRepository.GetQueryableAsync();

            var bpcsCodes = request.Datas.Select(a => a.BpcsOrderNo).Where(a => !string.IsNullOrWhiteSpace(a)).ToList();
            var bpcsLogisticsQuery = focLogisticsQuery.Where(a => bpcsCodes.Contains(a.BpcsOrderNo));
            //var query = bpcsLogisticsQuery
            //    .Join(focApplicationDetailQuery, a => a.FOCDetailId, a => a.Id, (a, b) => new { logistics = a, focDetail = b })
            //    .Join(focApplicationQuery, a => a.focDetail.ParentID, a => a.Id, (a, b) => new { a.logistics, a.focDetail, foc = b })
            //    .Select(a => new
            //    {
            //        FocId = a.foc.Id,
            //        a.foc.ApplicationCode,
            //        LogisticsId = a.logistics.Id,
            //        a.logistics.FOCDetailId,
            //        a.logistics.BpcsOrderNo,
            //        a.logistics.BatchNo,
            //    }).ToList();
            FOCStatus[] shippingStatus = [FOCStatus.PendingShipment, FOCStatus.SOICanceling, FOCStatus.Shipped];
            //需要发邮件的申请单
            List<FOCApplication> sendEmailApps = [];
            int sumRowQty = 0;
            foreach (var item in request.Datas)
            {
                bool isSendEmail = false;
                bool isSOIVerification = false;
                var sumPrdShippedQty = 0;
                var focApplication = focApplicationQuery.FirstOrDefault(a => a.ApplicationCode == item.ApplicationCode);
                if (focApplication == null)
                    return MessageResult.FailureResult("未找到申请单号信息;");
                if (!shippingStatus.Contains(focApplication.Status))
                    return MessageResult.FailureResult("单据状态不支持发货;");
                var focApplicationDetail = focApplicationDetails.FirstOrDefault(a => a.ParentID == focApplication.Id && a.RowId == int.Parse(item.RowNo));
                if (focApplicationDetail == null)
                    return MessageResult.FailureResult("未找到申请单对应行号信息;");
                var focProductDetail = focProductDetailQuery.FirstOrDefault(a => a.FOCDetailId == focApplicationDetail.Id && a.ProductSCode == item.ProductSCode);
                if (focProductDetail != null)
                {
                    //var shippedQty = int.Parse(item.ShippedQtyText);
                    var excelDatas = request.Datas.Where(a => a.ApplicationCode == item.ApplicationCode && a.RowNo == item.RowNo && a.ProductSCode == item.ProductSCode);
                    sumPrdShippedQty = excelDatas.ToList().Sum(a => int.Parse(a.ShippedQtyText));
                    sumRowQty = request.Datas.Where(a => a.ApplicationCode == item.ApplicationCode && a.RowNo == item.RowNo).ToList().Sum(a => int.Parse(a.ShippedQtyText));

                    //var pendingQty = focProductDetail.WriteoffQty - focProductDetail.ShippedQty;//待发货数量
                    var pendingQty = focProductDetail.WriteoffQty;//待发货数量
                    if (sumPrdShippedQty > pendingQty)
                    {
                        return MessageResult.FailureResult("发货数量超过上限;");
                    }
                }
                else
                {
                    return MessageResult.FailureResult("无法匹配发货地址或产品信息;");
                }

                //物流明细
                var logisticsDetail = bpcsLogisticsQuery.FirstOrDefault(a => a.FOCDetailId == focApplicationDetail.Id && a.ProductSCode == item.ProductSCode && a.BatchNo == item.BatchNo && a.BpcsOrderNo == item.BpcsOrderNo);
                if (logisticsDetail == null)
                {
                    MessageResult.FailureResult("无法匹配BPCS单号和产品批次号的物流信息;");
                }

                //判断BPCS单号是否在其他申请单下
                //if (query.Count > 0)
                //{
                //    var bpcsLogisticsDatas = query.FirstOrDefault(a => a.BpcsOrderNo == item.BpcsOrderNo && a.ApplicationCode != item.ApplicationCode);
                //    if (bpcsLogisticsDatas != null)
                //        return MessageResult.FailureResult("BPCS单号重复;");
                //}

                #region 更新物流记录逻辑
                //获取包含此FOC申请单+S Code+BPCS单号的物流记录
                //var removeLogistics = focLogisticsQuery.Where(a => a.FOCDetailId == focApplicationDetail.Id && a.ProductSCode == item.ProductSCode && a.BpcsOrderNo == item.BpcsOrderNo).Select(a => a.Id).ToList();
                //if (removeLogistics.Any())
                //    await focLogisticsRepository.DeleteDirectAsync(a => removeLogistics.Contains(a.Id));
                if (focProductDetail.VerificationStatus == VerificationStatusEnum.Pending || focProductDetail.VerificationStatus == VerificationStatusEnum.Partial)
                {
                    if (focProductDetail.WriteoffQty == sumPrdShippedQty)
                        isSOIVerification = true;
                }

                //修改已发货数量
                focProductDetail.ShippedQty = sumPrdShippedQty;
                //sumPrdShippedQty += sumPrdShippedQty;
                //sumShippedQty += sumPrdShippedQty;
                await focProductDetailRepository.UpdateAsync(focProductDetail);
                //物流信息
                logisticsDetail.ShipmentDate = Convert.ToDateTime(item.ShipmentDateText);
                logisticsDetail.ShippedQty = int.Parse(item.ShippedQtyText);
                logisticsDetail.LogisticsNo = item.LogisticsNo;
                await focLogisticsRepository.UpdateAsync(logisticsDetail);

                //更新发货状态
                //var focDetails = focApplicationDetailQuery.FirstOrDefault(a => a.Id == focApplicationDetail.Id);
                if (sumRowQty > 0)
                {
                    var productQty = focApplicationDetail.ProductQuantity;
                    if (sumRowQty < productQty)
                        focApplicationDetail.ShippingStatus = FOCShippingStatus.PartialShipped;
                    else if (sumRowQty == productQty)
                        focApplicationDetail.ShippingStatus = FOCShippingStatus.Shipped;
                    isSendEmail = true;
                    await focApplicationDetailRepository.UpdateAsync(focApplicationDetail);
                }
                #endregion

                //查询FOC申请单产品总数量
                var focSumProductQty = focApplicationDetailQuery.Where(a => a.ParentID == focApplication.Id).Sum(a => a.ProductQuantity);
                var excelSumFocQty = request.Datas.Where(a => a.ApplicationCode == item.ApplicationCode).ToList().Sum(a => int.Parse(a.ShippedQtyText));
                if (excelSumFocQty == focSumProductQty)
                {
                    //var focApplication = focApplicationQuery.FirstOrDefault(a => a.Id == focApplicationId);
                    if (focApplication != null)
                    {
                        focApplication.Status = FOCStatus.Shipped;
                        isSendEmail = true;
                        await focApplicationRepository.UpdateAsync(focApplication);
                    }
                }
                else
                {
                    //更新申请单状态为SOI核销中
                    if (isSOIVerification == true && focApplication != null)
                    {
                        focApplication.Status = FOCStatus.SOICanceling;
                        await focApplicationRepository.UpdateAsync(focApplication);
                    }
                }
                if (isSendEmail)
                {
                    sendEmailApps.Add(focApplication);
                }
            }
            //if (logisticsDetails.Count > 0)
            //    await focLogisticsRepository.InsertManyAsync(logisticsDetails);
            await SendChooseProductEmail(sendEmailApps);
            return MessageResult.SuccessResult("操作成功");
        }

        /// <summary>
        /// 批量上传物流信息 - backup
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        private async Task<MessageResult> BatchCreateFocProductLogisticsAsync(ImportDataResponseDto<AnalyzeFocProductLogisticsExcelResponseDto> request)
        {
            var focApplicationRepository = LazyServiceProvider.LazyGetService<IFocApplicationRepository>();
            var focApplicationQuery = await focApplicationRepository.GetQueryableAsync();
            var applicationCodeList = request.Datas.Select(a => a.ApplicationCode).Where(a => !string.IsNullOrWhiteSpace(a)).ToList();
            var focApplicationCodes = focApplicationQuery.Where(a => applicationCodeList.Contains(a.ApplicationCode));
            var focApplicationIds = focApplicationQuery.Select(a => a.Id).ToList();

            var focApplicationDetailRepository = LazyServiceProvider.LazyGetService<IFocApplicationDetailRepository>();
            var focApplicationDetailQuery = await focApplicationDetailRepository.GetQueryableAsync();
            var focApplicationDetails = focApplicationDetailQuery.Where(a => focApplicationIds.Contains(a.ParentID));
            var focLogisticsRepository = LazyServiceProvider.LazyGetService<IFocApplicationLogisticsDetailRepository>();
            var focLogisticsQuery = await focLogisticsRepository.GetQueryableAsync();

            var focProductDetailRepository = LazyServiceProvider.LazyGetService<IFocApplicationProductDetailRepository>();
            var focProductDetailQuery = await focProductDetailRepository.GetQueryableAsync();

            var focLogisticsDetailRepository = LazyServiceProvider.LazyGetService<IFocApplicationLogisticsDetailRepository>();
            var focLogisticsDetailQuery = await focLogisticsDetailRepository.GetQueryableAsync();

            var bpcsCodes = request.Datas.Select(a => a.BpcsOrderNo).Where(a => !string.IsNullOrWhiteSpace(a)).ToList();
            var bpcsLogisticsQuery = focLogisticsQuery.Where(a => bpcsCodes.Contains(a.BpcsOrderNo));
            var query = bpcsLogisticsQuery
                .Join(focApplicationDetailQuery, a => a.FOCDetailId, a => a.Id, (a, b) => new { logistics = a, focDetail = b })
                .Join(focApplicationQuery, a => a.focDetail.ParentID, a => a.Id, (a, b) => new { a.logistics, a.focDetail, foc = b })
                .Select(a => new
                {
                    FocId = a.foc.Id,
                    a.foc.ApplicationCode,
                    LogisticsId = a.logistics.Id,
                    a.logistics.FOCDetailId,
                    a.logistics.BpcsOrderNo,
                    a.logistics.BatchNo,
                }).ToList();
            FOCStatus[] shippingStatus = [FOCStatus.PendingShipment, FOCStatus.SOICanceling, FOCStatus.Shipped];

            //List<FOCApplicationLogisticsDetail> logisticsDetails = [];
            int sumRowQty = 0;
            foreach (var item in request.Datas)
            {
                var sumPrdShippedQty = 0;
                var focApplication = focApplicationQuery.FirstOrDefault(a => a.ApplicationCode == item.ApplicationCode);
                if (focApplication == null)
                    return MessageResult.FailureResult("未找到申请单号信息;");
                if (!shippingStatus.Contains(focApplication.Status))
                    return MessageResult.FailureResult("单据状态不支持发货;");
                var focApplicationDetail = focApplicationDetails.FirstOrDefault(a => a.ParentID == focApplication.Id && a.RowId == int.Parse(item.RowNo));
                if (focApplicationDetail == null)
                    return MessageResult.FailureResult("未找到申请单对应行号信息;");
                var focProductDetail = focProductDetailQuery.FirstOrDefault(a => a.FOCDetailId == focApplicationDetail.Id && a.ProductSCode == item.ProductSCode);
                if (focProductDetail != null)
                {
                    //var shippedQty = int.Parse(item.ShippedQtyText);
                    var excelDatas = request.Datas.Where(a => a.ApplicationCode == item.ApplicationCode && a.RowNo == item.RowNo && a.ProductSCode == item.ProductSCode);
                    sumPrdShippedQty = excelDatas.ToList().Sum(a => int.Parse(a.ShippedQtyText));
                    sumRowQty = request.Datas.Where(a => a.ApplicationCode == item.ApplicationCode && a.RowNo == item.RowNo).ToList().Sum(a => int.Parse(a.ShippedQtyText));

                    //var pendingQty = focProductDetail.WriteoffQty - focProductDetail.ShippedQty;//待发货数量
                    var pendingQty = focProductDetail.WriteoffQty;//待发货数量
                    if (sumPrdShippedQty > pendingQty)
                    {
                        return MessageResult.FailureResult("发货数量超过上限;");
                    }
                }
                else
                {
                    return MessageResult.FailureResult("无法匹配发货地址或产品信息;");
                }

                //判断BPCS单号是否在其他申请单下
                if (query.Count > 0)
                {
                    var bpcsLogisticsDatas = query.FirstOrDefault(a => a.BpcsOrderNo == item.BpcsOrderNo && a.ApplicationCode != item.ApplicationCode);
                    if (bpcsLogisticsDatas != null)
                        return MessageResult.FailureResult("BPCS单号重复;");
                }

                #region 更新物流记录逻辑
                //修改已发货数量
                focProductDetail.ShippedQty = sumPrdShippedQty;
                //sumPrdShippedQty += sumPrdShippedQty;
                //sumShippedQty += sumPrdShippedQty;
                await focProductDetailRepository.UpdateAsync(focProductDetail);
                //物流信息
                var logisticsDetail = focLogisticsQuery.FirstOrDefault(a => a.FOCDetailId == focApplicationDetail.Id && a.ProductSCode == item.ProductSCode && a.BpcsOrderNo == item.BpcsOrderNo && a.BatchNo == item.BatchNo);
                if (logisticsDetail != null)
                {
                    logisticsDetail.ShippedQty = int.Parse(item.ShippedQtyText);
                    logisticsDetail.ShipmentDate = Convert.ToDateTime(item.ShipmentDateText);
                    await focLogisticsRepository.UpdateAsync(logisticsDetail);
                }
                else
                {
                    int rowId = 1;
                    var logistics = new FOCApplicationLogisticsDetail()
                    {
                        RowId = rowId,
                        FOCDetailId = focApplicationDetail.Id,
                        ProductSCode = item.ProductSCode,
                        ProductName = focProductDetail.ProductName,
                        ShippedQty = int.Parse(item.ShippedQtyText),
                        BatchNo = item.BatchNo,
                        BpcsOrderNo = item.BpcsOrderNo,
                        ShipmentDate = Convert.ToDateTime(item.ShipmentDateText)
                    };
                    await focLogisticsRepository.InsertAsync(logistics);

                    //获取包含此FOC申请单+BPCS单号的物流记录
                    var removeLogistics = query.Where(a => a.ApplicationCode == item.ApplicationCode && a.BpcsOrderNo == item.BpcsOrderNo).Select(a => a.LogisticsId).ToList();
                    if (removeLogistics.Any())
                        await focLogisticsRepository.DeleteDirectAsync(a => removeLogistics.Contains(a.Id));
                }
                //更新发货状态
                //var focDetails = focApplicationDetailQuery.FirstOrDefault(a => a.Id == focApplicationDetail.Id);
                if (sumRowQty > 0)
                {
                    var productQty = focApplicationDetail.ProductQuantity;
                    if (sumRowQty < productQty)
                        focApplicationDetail.ShippingStatus = FOCShippingStatus.PartialShipped;
                    else if (sumRowQty == productQty)
                        focApplicationDetail.ShippingStatus = FOCShippingStatus.Shipped;

                    await focApplicationDetailRepository.UpdateAsync(focApplicationDetail);
                }
                #endregion

                //查询FOC申请单产品总数量
                var focSumProductQty = focApplicationDetailQuery.Where(a => a.ParentID == focApplication.Id).Sum(a => a.ProductQuantity);
                var excelSumFocQty = request.Datas.Where(a => a.ApplicationCode == item.ApplicationCode).ToList().Sum(a => int.Parse(a.ShippedQtyText));
                if (excelSumFocQty == focSumProductQty)
                {
                    //var focApplication = focApplicationQuery.FirstOrDefault(a => a.Id == focApplicationId);
                    if (focApplication != null)
                    {
                        focApplication.Status = FOCStatus.Shipped;
                        await focApplicationRepository.UpdateAsync(focApplication);
                    }
                }
            }
            //if (logisticsDetails.Count > 0)
            //    await focLogisticsRepository.InsertManyAsync(logisticsDetails);

            return MessageResult.SuccessResult("操作成功");
        }

        /// <summary>
        /// 获取FOC子预算列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetFocSubBudgetsResponseDto>> GetFocSubBudgetInfosAsync(GetFocSubBudgetsRequestDto request)
        {
            //获取当前机构
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            IEnumerable<DepartmentDto> orgs = await dataverseService.GetOrganizations(request.OrgId.ToString());
            if (!orgs.Any())
                return default;

            var org = orgs.First();
            //获取当前机构的上级BU
            orgs = await LazyServiceProvider.LazyGetService<ICommonService>().GetParentOrgs(org);
            var bu = orgs.FirstOrDefault(a => a.OrganizationType == DataverseEnums.Organization.OrganizationType.Bu);
            if (bu == null)
                return default;

            var queryUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var querySubBudget = await LazyServiceProvider.LazyGetService<IFocSubBudgetRepository>().GetQueryableAsync();
            if (CurrentUser.Id.HasValue)
            {
                //如果大区是全国、或者为空就可以用所有的预算，否则只能用他所属的那个大区的预算
                var staff = await dataverseService.GetStaffs(CurrentUser.Id.ToString());
                if (staff.Any() && staff.First().DistrictId.HasValue && staff.First().DistrictName != "全国")
                    querySubBudget = querySubBudget.Where(a => a.RegionId == staff.First().DistrictId);
            }

            var query = querySubBudget.Where(a => a.BuId == bu.Id && a.CostCenterId == org.FocCostcenterId && true && a.Status).Include(a => a.MasterBudget)
                .Include(s => s.MonthlyBudgets.Where(s => s.Status))
                .GroupJoin(queryUser, a => a.OwnerId, a => a.Id, (a, b) => new { SubBudget = a, Users = b })
                .SelectMany(a => a.Users.DefaultIfEmpty(), (a, b) => new { a.SubBudget, User = b })
                .WhereIf(!string.IsNullOrEmpty(request.Code), a => a.SubBudget.Code.Contains(request.Code))
                .WhereIf(!string.IsNullOrEmpty(request.Description), a => a.SubBudget.Description.Contains(request.Description))
                .WhereIf(!string.IsNullOrEmpty(request.Owner), a => a.User.Name.Contains(request.Owner))
                .WhereIf(!string.IsNullOrEmpty(request.ProductMCode), a => a.SubBudget.ProductMCode == request.ProductMCode)
                .WhereIf(!string.IsNullOrEmpty(request.ProductName), a => a.SubBudget.ProductName.Contains(request.ProductName))
                .OrderByDescending(s => s.SubBudget.CreationTime)
                .Select(a => new GetFocSubBudgetsResponseDto
                {
                    Id = a.SubBudget.Id,
                    BudgetCode = a.SubBudget.Code,
                    Description = a.SubBudget.Description,
                    OrgName = bu.DepartmentName,
                    Owner = a.User.Name,
                    ProductName = a.SubBudget.ProductName,
                    TotalQty = a.SubBudget.BudgetQty,
                    UsedQty = a.SubBudget.UesdQty,
                    AvailableQty = a.SubBudget.GetAvailableQty()
                });

            var count = query.Count();
            var datas = query.Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToArray();

            return new PagedResultDto<GetFocSubBudgetsResponseDto>(count, datas);
        }

        /// <summary>
        /// 获取FOC预算信息
        /// </summary>
        /// <param name="focSubBudgetId"></param>
        /// <returns></returns>
        public async Task<GetFocBudgetInfoResponse> GetFocSubBudgetInfoAsync(Guid focSubBudgetId)
        {
            //查询子预算数据
            var queryUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            //var queryProduct = await LazyServiceProvider.LazyGetService<IProductDetailsRepository>().GetQueryableAsync();
            var querySubBudget = await LazyServiceProvider.LazyGetService<IFocSubBudgetRepository>().GetQueryableAsync();
            var budgetData = querySubBudget.Include(a => a.MasterBudget).Include(a => a.MonthlyBudgets.Where(s => s.Status))
                .GroupJoin(queryUser, a => a.OwnerId, a => a.Id, (a, b) => new { Subbudget = a, Users = b })
                .SelectMany(a => a.Users.DefaultIfEmpty(), (a, b) => new { a.Subbudget, User = b })
                //.GroupJoin(queryProduct, a => a.Subbudget.ProductId, a => a.Id, (a, b) => new { a.Subbudget, a.User, Product = b })
                //.SelectMany(a => a.Product.DefaultIfEmpty(), (a, b) => new { a.Subbudget, a.User, Product = b })
                .FirstOrDefault(a => a.Subbudget.Id == focSubBudgetId);

            if (budgetData == null)
                return default;

            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var costcenters = await dataverseService.GetCostcentersAsync(budgetData.Subbudget.CostCenterId.ToString());
            var bus = await dataverseService.GetOrganizations(budgetData.Subbudget.BuId.ToString());
            var regions = await dataverseService.GetDistrict(budgetData.Subbudget.RegionId.ToString());

            var budget = new GetFocBudgetInfoResponse
            {
                BudgetId = budgetData.Subbudget.Id,
                BudgetCode = budgetData.Subbudget.Code,
                Description = budgetData.Subbudget.Description,
                OrgId = bus.FirstOrDefault()?.Id,
                OrgName = bus.FirstOrDefault()?.DepartmentName,
                CostcenterId = costcenters.FirstOrDefault()?.Id,
                CostcenterName = costcenters.FirstOrDefault()?.Name,
                ProductId = budgetData.Subbudget.ProductId,
                ProductName = budgetData.Subbudget.ProductName,
                ProductMCode = budgetData.Subbudget.ProductMCode,
                ProductUnit = budgetData.Subbudget.ProductUnit,
                Lead = budgetData.User?.Name,
                TotalQty = budgetData.Subbudget.BudgetQty,
                UsedQty = budgetData.Subbudget.UesdQty,
                AvailableQty = budgetData.Subbudget.GetAvailableQty()
            };

            if (regions.Any())
            {
                budget.RegionId = regions.First().Id;
                budget.RegionName = regions.First().Name;
            }

            //附件
            if (!string.IsNullOrEmpty(budgetData.Subbudget.AttachmentFile))
            {
                var ids = budgetData.Subbudget.AttachmentFile.Split(",").Select(Guid.Parse);
                var attachments = await LazyServiceProvider.LazyGetService<IAttachmentRepository>().GetListAsync(a => ids.Contains(a.Id));
                budget.Files = attachments.Select(a => new UploadFileResponseDto
                {
                    AttachmentId = a.Id,
                    FileName = a.FileName,
                    FilePath = a.FilePath,
                    Success = true
                });
            }

            return budget;
        }

        /// <summary>
        /// 获取FOC消费大类
        /// </summary>
        /// <returns></returns>
        public async Task<IEnumerable<ConsumeCategoryDto>> GetFocConsumeCategoryAsync()
        {
            var _dataverseService = _serviceProvider.GetService<IDataverseService>();
            var consumeCategories = await _dataverseService.GetConsumeCategoryAsync();

            var focConsumeCategories = consumeCategories.Where(a => a.FlowType == ConsumeCategory.FlowTypes.FOC);
            return focConsumeCategories;
        }

        /// <summary>
        /// 导出物流信息
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public async Task<Stream> ExportLogisticsInfoExcelAsync(Guid[] ids)
        {
            var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");
            var focApplicationRepository = LazyServiceProvider.LazyGetService<IFocApplicationRepository>();
            var focApplicationQuery = await focApplicationRepository.GetQueryableAsync();
            var focDetailRepository = LazyServiceProvider.LazyGetService<IFocApplicationDetailRepository>();
            var focDetailQuery = await focDetailRepository.GetQueryableAsync();
            var focProductDetailRepository = LazyServiceProvider.LazyGetService<IFocApplicationProductDetailRepository>();
            var focProductDetailQuery = await focProductDetailRepository.GetQueryableAsync();
            var focLogisticsDetailRepository = LazyServiceProvider.LazyGetService<IFocApplicationLogisticsDetailRepository>();
            var focLogisticsDetailQuery = await focLogisticsDetailRepository.GetQueryableAsync();

            var datas = await focApplicationQuery.WhereIf(ids.Length > 0, a => ids.Contains(a.Id))
                .GroupJoin(focDetailQuery, a => a.Id, a => a.ParentID, (a, b) => new { foc = a, focDetail = b })
                .SelectMany(a => a.focDetail.DefaultIfEmpty(), (a, b) => new { foc = a, focDetail = b })
                .GroupJoin(focProductDetailQuery, a => a.focDetail.Id, a => a.FOCDetailId, (a, b) => new { a.foc, a.focDetail, productDetail = b })
                .SelectMany(a => a.productDetail.DefaultIfEmpty(), (a, b) => new { a.foc, a.focDetail, productDetail = b })
                .GroupJoin(focLogisticsDetailQuery, a => new { a.productDetail.FOCDetailId, a.productDetail.ProductSCode }, a => new { a.FOCDetailId, a.ProductSCode },
                        (a, b) => new { a.foc, a.focDetail, a.productDetail, logisticsDetail = b })
                .SelectMany(a => a.logisticsDetail.DefaultIfEmpty(), (a, b) => new { a.foc, a.focDetail, a.productDetail, logisticsDetail = b })
                .Select(a => new LogisticsInfoExcelResponseDto()
                {
                    ApplicationCode = a.foc.foc.ApplicationCode,
                    ApplyUser = a.foc.foc.ApplyUser,
                    ApplyTime = a.foc.foc.ApplyTime,
                    ApprovedDate = a.foc.foc.ApprovedDate,
                    ApplyUserDeptName = a.foc.foc.ApplyUserDeptName,
                    NatureName = a.foc.foc.NatureName,
                    RowId = a.focDetail != null ? a.focDetail.RowId : 0,
                    ConsigneeAddress = a.focDetail != null && !string.IsNullOrEmpty(a.focDetail.ConsigneeAddress) ? AesHelper.Decryption(a.focDetail.ConsigneeAddress, insightKey) : string.Empty,
                    ConsigneeName = a.focDetail != null && !string.IsNullOrEmpty(a.focDetail.ConsigneeName) ? AesHelper.Decryption(a.focDetail.ConsigneeName, insightKey) : string.Empty,
                    ConsigneePhone = a.focDetail != null && !string.IsNullOrEmpty(a.focDetail.ConsigneePhone) ? AesHelper.Decryption(a.focDetail.ConsigneePhone, insightKey) : string.Empty,
                    ProductSCode = a.productDetail != null ? a.productDetail.ProductSCode : string.Empty,
                    ProductName = a.productDetail != null ? a.productDetail.ProductName : string.Empty,
                    PendingQty = a.productDetail != null ? (a.productDetail.WriteoffQty - a.productDetail.ShippedQty) : null,
                    BpcsOrderNo = a.logisticsDetail != null && !string.IsNullOrEmpty(a.logisticsDetail.BpcsOrderNo) ? a.logisticsDetail.BpcsOrderNo : string.Empty,
                    BatchNo = a.logisticsDetail != null && !string.IsNullOrEmpty(a.logisticsDetail.BatchNo) ? a.logisticsDetail.BatchNo : string.Empty,
                    ShippedQty = a.logisticsDetail != null && a.logisticsDetail.ShippedQty.HasValue ? a.logisticsDetail.ShippedQty : null,
                    ShipmentDate = a.logisticsDetail != null && a.logisticsDetail.ShipmentDate.HasValue ? a.logisticsDetail.ShipmentDate : null,
                    TerminalName = a.foc.foc.TerminalName,
                    ReceivingCode = a.foc.foc.ReceivingCode,
                    LogisticsNo = a.logisticsDetail != null && !string.IsNullOrEmpty(a.logisticsDetail.LogisticsNo) ? a.logisticsDetail.LogisticsNo : string.Empty,
                    ApplicateQty = a.productDetail != null ? (a.productDetail.ProductQty) : null,
                }).ToArrayAsync();
            MemoryStream stream = new();
            var config = new OpenXmlConfiguration()
            {
                TableStyles = TableStyles.None,
                AutoFilter = false

            };
            stream.SaveAs(datas, true, "Sheet1", configuration: config);
            stream.Seek(0, SeekOrigin.Begin);
            return stream;
        }

        /// <summary>
        /// 同步PP Region数据
        /// </summary>
        /// <returns></returns>
        public async Task<string> SyncPPRegionDataAsync()
        {
            try
            {
                var listForAdd = new List<PPRegion>();
                var listForUpdate = new List<PPRegion>();
                var regionRepository = LazyServiceProvider.LazyGetService<IPPRegionRepository>();
                var regionQuery = await regionRepository.GetQueryableAsync();
                var guidGenerator = LazyServiceProvider.LazyGetService<IGuidGenerator>();
                var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
                var regionData = (await dataverseService.GetDistrict()).ToList();
                foreach (var item in regionData)
                {
                    var existDatas = regionQuery.FirstOrDefault(a => a.RegionId == item.Id);
                    //存在数据则修改
                    if (existDatas != null)
                    {
                        existDatas.SetModification();
                        listForUpdate.Add(existDatas);
                    }
                    else
                    {
                        var region = new PPRegion();
                        region.SetId(guidGenerator.Create());
                        region.RegionId = item.Id;
                        region.Name = item.Name;
                        region.DistrictCode = item.DistrictCode;
                        region.StateCode = item.StateCode;

                        region.SetCreation();
                        listForAdd.Add(region);
                    }
                }

                var context = await regionRepository.GetDbContextAsync();
                if (listForAdd.Count > 0)
                    await context.BulkInsertAsync(listForAdd);
                if (listForUpdate.Count > 0)
                    await context.BulkUpdateAsync(listForUpdate, options =>
                    {
                        options.UpdateByProperties = [nameof(PPRegion.RegionId)];
                    });

                return string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"SyncPPRegionDataAsync Errorr :{ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 同步PP CostCenter数据
        /// </summary>
        /// <returns></returns>
        public async Task<string> SyncPPCostCenterDataAsync()
        {
            try
            {
                var listForAdd = new List<PPCostCenter>();
                var listForUpdate = new List<PPCostCenter>();
                var costCenterRepository = LazyServiceProvider.LazyGetService<IPPCostCenterRepository>();
                var costCenterQuery = await costCenterRepository.GetQueryableAsync();
                var guidGenerator = LazyServiceProvider.LazyGetService<IGuidGenerator>();
                var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
                var costCenterData = (await dataverseService.GetCostcentersAsync()).ToList();
                foreach (var item in costCenterData)
                {
                    var existDatas = costCenterQuery.FirstOrDefault(a => a.CostCenterId == item.Id);
                    //存在数据则修改
                    if (existDatas != null)
                    {
                        existDatas.SetModification();
                        listForUpdate.Add(existDatas);
                    }
                    else
                    {
                        var costCenter = new PPCostCenter();
                        costCenter.SetId(guidGenerator.Create());
                        costCenter.CostCenterId = item.Id;
                        costCenter.Code = item.Code;
                        costCenter.Name = item.Name;
                        costCenter.NameEn = item.NameEn;
                        costCenter.CcenterCode = item.CcenterCode;
                        costCenter.StateCode = item.StateCode;

                        costCenter.SetCreation();
                        listForAdd.Add(costCenter);
                    }
                }

                var context = await costCenterRepository.GetDbContextAsync();
                if (listForAdd.Count > 0)
                    await context.BulkInsertAsync(listForAdd);
                if (listForUpdate.Count > 0)
                    await context.BulkUpdateAsync(listForUpdate, options =>
                    {
                        options.UpdateByProperties = [nameof(PPCostCenter.CostCenterId)];
                    });

                return string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"SyncPPCostCenterDataAsync Errorr :{ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 处理FOC申请主表信息
        /// </summary>
        /// <param name="focApplication">The foc application.</param>
        /// <param name="request">The request.</param>
        /// <param name="org">The org.</param>
        /// <returns></returns>
        async Task<MessageResult> HandleFocApplicationAsync(FOCApplication focApplication, CreateFocApplicationRequest request, DepartmentDto org)
        {
            var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");
            var orgs = await LazyServiceProvider.LazyGetService<ICommonService>().GetParentOrgs(org);
            //var bu = orgs.FirstOrDefault(a => a.OrganizationType == DataverseEnums.Organization.OrganizationType.Bu);
            //if (bu != null)
            //{
            //    focApplication.ApplyUserBuId = bu.Id;
            //    focApplication.ApplyUserBuName = bu.DepartmentName;
            //}

            //部门
            if (org != null)
            {
                focApplication.ApplyUserDeptId = org.Id;
                focApplication.ApplyUserDeptName = org.DepartmentName;
            }
            else
            {
                return MessageResult.FailureResult($"请选择部门");
            }

            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            //成本中心
            if (request.CostCenterId.HasValue)
            {
                var costcenters = await dataverseService.GetCostcentersAsync(request.CostCenterId.ToString());
                var costcenter = costcenters.FirstOrDefault();
                if (costcenter == null)
                    return MessageResult.FailureResult($"未找到Id为{request.CostCenterId}的成本中心数据");

                focApplication.CostCenterID = costcenter.Id;
                focApplication.CostCenter = costcenter.Name;
                focApplication.CostCenterCode = costcenter.Code;
            }
            else
            {
                return MessageResult.FailureResult($"请选择成本中心");
            }

            //产品
            if (request.ProductId.HasValue)
            {
                var productQuery = await LazyServiceProvider.LazyGetService<IProductDetailsRepository>().GetQueryableAsync();
                var product = await productQuery.FirstOrDefaultAsync(a => a.Id == request.ProductId);
                if (product == null)
                    return MessageResult.FailureResult($"未找到Id为{request.ProductId}的产品数据");
                focApplication.ProductName = product.ProductShortNameEN;
                focApplication.ProductMCode = product.ProductMCode;
                focApplication.ProductUnit = product.Unit;
                //focApplication.ProductMultiPack = int.Parse(product.MultiPack);
            }
            else
            {
                focApplication.ProductId = null;
                focApplication.ProductName = request.ProductName;
                focApplication.ProductMCode = request.ProductMCode;
                focApplication.ProductUnit = request.ProductUnit;
                //focApplication.ProductMultiPack = 0;
            }

            //合计M Code下单数量
            //if (request.MCodeQty.HasValue)
            //    focApplication.MCodeQty = request.MCodeQty.Value;

            //合计产品数量
            var productQty = request.DetailItems.Sum(a => a.ProductQuantity);
            //if (request.ProductQty.HasValue)
            focApplication.ProductQty = productQty;

            //预算
            if (request.SubBudgetId.HasValue)
            {
                var queryBudget = await LazyServiceProvider.LazyGetService<IFocSubBudgetRepository>().GetQueryableAsync();
                var budget = await queryBudget.Include(a => a.MasterBudget).Include(a => a.MonthlyBudgets).Select(a => new
                {
                    MasterBudget = new { a.MasterBudget.Id, a.MasterBudget.Code, a.MasterBudget.BuId },
                    a.Id,
                    a.Code,
                    a.RegionId,
                    a.ProductMCode,
                    a.AttachmentFile,
                    a.UesdQty,
                    a.BudgetQty,
                    a.Description,
                    BudgetAvailableQty = a.GetAvailableQty(),
                    a.OwnerId
                })
                .FirstOrDefaultAsync(a => a.Id == request.SubBudgetId);
                if (budget == null)
                    return MessageResult.FailureResult($"未找到Id为{request.SubBudgetId}的FOC预算数据");

                //主预算信息
                focApplication.BudgetId = budget.MasterBudget.Id;
                focApplication.BudgetCode = budget.MasterBudget.Code;
                //focApplication.BudgetMCode = budget.MasterBudget.ProductMCode;
                focApplication.BudgetBuId = budget.MasterBudget.BuId;
                var bu = await dataverseService.GetOrganizations(budget.MasterBudget.BuId.ToString());
                if (bu.Any())
                    focApplication.BudgetBuName = bu.First().DepartmentName;

                //子预算信息
                focApplication.SubBudgetId = budget.Id;
                focApplication.SubBudgetCode = budget.Code;
                focApplication.BudgetRegionId = budget.RegionId;
                focApplication.BudgetMCode = budget.ProductMCode;
                focApplication.BudgetUsedQty = budget.UesdQty;
                focApplication.BudgetQty = budget.BudgetQty;
                focApplication.BudgetAvailableQty = budget.BudgetAvailableQty;
                focApplication.BudgetDescription = budget.Description;
                var regions = await dataverseService.GetDistrict(budget.RegionId.ToString());
                if (regions.Any())
                    focApplication.BudgetRegion = regions.First().Name;

                var queryUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
                var ownerUser = await queryUser.Select(a => new { a.Id, a.Name }).FirstOrDefaultAsync(a => a.Id == budget.OwnerId);
                if (ownerUser != null)
                {
                    focApplication.BudgetOwner = ownerUser.Name;
                }
                focApplication.BudgetOwnerId = budget.OwnerId;
                focApplication.BudgetFile = budget.AttachmentFile;
            }
            else
            {
                return MessageResult.FailureResult($"请选择预算");
            }

            //FOC Nature
            if (request.NatureId.HasValue)
            {
                var expenseNatures = await dataverseService.GetCostNatureAsync();
                var nature = expenseNatures.FirstOrDefault(a => a.Id == request.NatureId);
                if (nature == null)
                    return MessageResult.FailureResult($"未找到Id为{request.NatureId}的费用性质数据");
                focApplication.NatureId = nature.Id;
                focApplication.NatureName = nature.Name;
                focApplication.NatureDescription = nature.Description;
            }
            else
            {
                return MessageResult.FailureResult($"请选择费用性质");
            }

            //发货类型
            if (request.ShippingTypeId.HasValue)
            {
                //获取发货类型
                var shippingTypeList = await dataverseService.GetDictionariesAsync(DictionaryType.ShippingTypes);
                var shippingType = shippingTypeList.Where(a => a.Id == request.ShippingTypeId).FirstOrDefault();
                if (shippingType == null)
                    return MessageResult.FailureResult($"未找到Id为{request.ShippingTypeId}的发货类型数据");
                focApplication.ShippingTypeName = shippingType.Name;
            }
            else
            {
                return MessageResult.FailureResult($"请选择发货类型");
            }

            //客户
            if (request.ClientId.HasValue)
            {
                var queryStore = await LazyServiceProvider.LazyGetService<IStoreInfoRepository>().GetQueryableAsync();
                var client = await queryStore.FirstOrDefaultAsync(a => a.Id == request.ClientId);
                if (client == null)
                    return MessageResult.FailureResult($"未找到Id为{request.ClientId}的客户数据");
                focApplication.ClientCode = client.StoreCode;
                focApplication.ClientName = client.StoreName;
            }
            else
            {
                if (!string.IsNullOrEmpty(request.ClientCode))
                    focApplication.ClientCode = request.ClientCode;
                else
                    focApplication.ClientCode = null;

                if (!string.IsNullOrEmpty(request.ClientName))
                    focApplication.ClientName = request.ClientName;
                else
                    focApplication.ClientName = null;
            }

            //终端
            if (request.TerminalId.HasValue)
            {
                //获取终端/其它
                var terminalList = await dataverseService.GetDictionariesAsync(DictionaryType.FocTerminals);
                var terminal = terminalList.Where(a => a.Id == request.TerminalId).FirstOrDefault();
                if (terminal == null)
                    return MessageResult.FailureResult($"未找到Id为{request.TerminalId}的终端数据");
                focApplication.TerminalName = terminal.Name;
            }
            else
            {
                focApplication.TerminalName = null;
            }

            //客户收货代码
            if (!string.IsNullOrEmpty(request.ReceivingCode))
            {
                focApplication.ReceivingCode = request.ReceivingCode;
                if (!string.IsNullOrEmpty(request.ConsigneeAddress))
                    focApplication.ConsigneeAddress = AesHelper.Encryption(request.ConsigneeAddress, insightKey);
                if (!string.IsNullOrEmpty(request.ConsigneeName))
                    focApplication.ConsigneeName = AesHelper.Encryption(request.ConsigneeName, insightKey);
                if (!string.IsNullOrEmpty(request.ConsigneePhone))
                    focApplication.ConsigneePhone = AesHelper.Encryption(request.ConsigneePhone, insightKey);
            }

            focApplication.Attachment = request.Attachment?.Select(a => a.AttachmentId).JoinAsString(",");
            focApplication.Status = FOCStatus.Draft;

            return MessageResult.SuccessResult(focApplication);
        }
        /// <summary>
        /// 处理FOC申请明细数据
        /// </summary>
        /// <param name="sticketApplication"></param>
        /// <param name="applicationDetailRequests"></param>
        /// <returns></returns>
        async Task<MessageResult> HandleFocApplicationDetailAsync(FOCApplication focApplication, IEnumerable<CreateUpdateFocApplicationDetailRequest> applicationDetailRequests)
        {
            var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");
            List<FOCApplicationDetail> originalFocDetails = null, newFocDetails = null;
            if (applicationDetailRequests?.Any() == true)
            {
                var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
                var queryFocDetail = await LazyServiceProvider.LazyGetService<IFocApplicationDetailRepository>().GetQueryableAsync();
                var focDetailRepository = LazyServiceProvider.LazyGetService<IFocApplicationDetailRepository>();
                originalFocDetails = queryFocDetail.Where(a => a.ParentID == focApplication.Id).ToList();

                newFocDetails = new List<FOCApplicationDetail>(originalFocDetails);

                var index = newFocDetails.Any() ? newFocDetails.Max(a => a.RowId) + 1 : 1;
                foreach (var item in applicationDetailRequests)
                {
                    FOCApplicationDetail detail;
                    if (item.Id.HasValue)
                    {
                        //已提交后的数据，不允许编辑明细行，只能反冲
                        if (focApplication.ApplyTime.HasValue)
                            continue;

                        detail = newFocDetails.First(a => a.Id == item.Id);
                        detail = ObjectMapper.Map(item, detail);
                        await focDetailRepository.UpdateAsync(detail);
                    }
                    else
                    {
                        detail = ObjectMapper.Map<CreateUpdateFocApplicationDetailRequest, FOCApplicationDetail>(item);
                        detail.RowId = index++;
                        detail.ParentID = focApplication.Id;
                        if (!string.IsNullOrEmpty(detail.ConsigneeAddress))
                            detail.ConsigneeAddress = AesHelper.Encryption(detail.ConsigneeAddress, insightKey);
                        if (!string.IsNullOrEmpty(detail.ConsigneeName))
                            detail.ConsigneeName = AesHelper.Encryption(detail.ConsigneeName, insightKey);
                        if (!string.IsNullOrEmpty(detail.ConsigneePhone))
                            detail.ConsigneePhone = AesHelper.Encryption(detail.ConsigneePhone, insightKey);

                        //城市主数据
                        CityMasterDataDto city = null;
                        if (item.CityId.HasValue)
                        {
                            var cities = await dataverseService.GetSpecialCitiesAsync(item.CityId.ToString());
                            city = cities.FirstOrDefault();
                        }
                        else
                        {
                            return MessageResult.FailureResult($"请选择明细行城市");
                        }
                        detail.CityName = city?.Name;

                        if (!item.ActivityStartDate.HasValue)
                            return MessageResult.FailureResult($"请选择明细行活动开始日期");

                        if (string.IsNullOrEmpty(item.Description))
                            return MessageResult.FailureResult($"请输入明细行描述");

                        if (item.HedgeDetailId.HasValue)
                        {
                            detail.IsHedge = true;//反冲行
                            var hedgeDetail = newFocDetails.First(a => a.Id == detail.HedgeDetailId.Value);
                            hedgeDetail.IsHedge = true;//被反冲行
                            await focDetailRepository.UpdateAsync(hedgeDetail);
                        }

                        newFocDetails.Add(detail);
                        await focDetailRepository.InsertAsync(detail);
                    }
                }
            }

            return MessageResult.SuccessResult(new Tuple<List<FOCApplicationDetail>, List<FOCApplicationDetail>>(originalFocDetails, newFocDetails));
        }

        /// <summary>
        /// 处理FOC申请主表信息
        /// </summary>
        /// <param name="focApplication">The foc application.</param>
        /// <param name="request">The request.</param>
        /// <param name="org">The org.</param>
        /// <returns></returns>
        async Task<MessageResult> HandleDraftFocApplicationAsync(FOCApplication focApplication, CreateFocApplicationRequest request, DepartmentDto org)
        {
            var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");
            var orgs = await LazyServiceProvider.LazyGetService<ICommonService>().GetParentOrgs(org);

            //部门
            if (org != null)
            {
                focApplication.ApplyUserDeptId = org.Id;
                focApplication.ApplyUserDeptName = org.DepartmentName;
            }

            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            //成本中心
            if (request.CostCenterId.HasValue)
            {
                var costcenters = await dataverseService.GetCostcentersAsync(request.CostCenterId.ToString());
                var costcenter = costcenters.FirstOrDefault();
                if (costcenter == null)
                    return MessageResult.FailureResult($"未找到Id为{request.CostCenterId}的成本中心数据");

                focApplication.CostCenterID = costcenter.Id;
                focApplication.CostCenter = costcenter.Name;
                focApplication.CostCenterCode = costcenter.Code;
            }
            else
            {
                focApplication.CostCenterID = null;
                focApplication.CostCenter = null;
                focApplication.CostCenterCode = null;
            }

            //产品
            if (request.ProductId.HasValue)
            {
                var productQuery = await LazyServiceProvider.LazyGetService<IProductDetailsRepository>().GetQueryableAsync();
                var product = await productQuery.FirstOrDefaultAsync(a => a.Id == request.ProductId);
                if (product == null)
                    return MessageResult.FailureResult($"未找到Id为{request.ProductId}的产品数据");
                focApplication.ProductName = product.ProductShortNameEN;
                focApplication.ProductMCode = product.ProductMCode;
                focApplication.ProductUnit = product.Unit;
                //focApplication.ProductMultiPack = int.Parse(product.MultiPack);
            }
            else
            {
                focApplication.ProductId = null;
                focApplication.ProductName = request.ProductName;
                focApplication.ProductMCode = request.ProductMCode;
                focApplication.ProductUnit = request.ProductUnit;
                //focApplication.ProductMultiPack = 0;
            }

            //合计M Code下单数量
            //if (request.MCodeQty.HasValue)
            //    focApplication.MCodeQty = request.MCodeQty.Value;

            //合计产品数量
            focApplication.ProductQty = 0;
            if (request.DetailItems != null)
            {
                var productQty = request.DetailItems.Sum(a => a.ProductQuantity);
                //if (request.ProductQty.HasValue)
                focApplication.ProductQty = productQty;
            }

            //预算
            if (request.SubBudgetId.HasValue)
            {
                var queryBudget = await LazyServiceProvider.LazyGetService<IFocSubBudgetRepository>().GetQueryableAsync();
                var budget = await queryBudget.Include(a => a.MasterBudget).Include(a => a.MonthlyBudgets).Select(a => new
                {
                    MasterBudget = new { a.MasterBudget.Id, a.MasterBudget.Code, a.MasterBudget.BuId },
                    a.Id,
                    a.Code,
                    a.RegionId,
                    a.ProductMCode,
                    a.AttachmentFile,
                    a.UesdQty,
                    a.BudgetQty,
                    a.Description,
                    BudgetAvailableQty = a.GetAvailableQty(),
                    a.OwnerId
                })
                .FirstOrDefaultAsync(a => a.Id == request.SubBudgetId);
                if (budget == null)
                    return MessageResult.FailureResult($"未找到Id为{request.SubBudgetId}的FOC预算数据");

                //主预算信息
                focApplication.BudgetId = budget.MasterBudget.Id;
                focApplication.BudgetCode = budget.MasterBudget.Code;
                //focApplication.BudgetMCode = budget.MasterBudget.ProductMCode;
                focApplication.BudgetBuId = budget.MasterBudget.BuId;
                var bu = await dataverseService.GetOrganizations(budget.MasterBudget.BuId.ToString());
                if (bu.Any())
                    focApplication.BudgetBuName = bu.First().DepartmentName;

                //子预算信息
                focApplication.SubBudgetId = budget.Id;
                focApplication.SubBudgetCode = budget.Code;
                focApplication.BudgetRegionId = budget.RegionId;
                focApplication.BudgetMCode = budget.ProductMCode;
                focApplication.BudgetUsedQty = budget.UesdQty;
                focApplication.BudgetQty = budget.BudgetQty;
                focApplication.BudgetAvailableQty = budget.BudgetAvailableQty;
                focApplication.BudgetDescription = budget.Description;
                var regions = await dataverseService.GetDistrict(budget.RegionId.ToString());
                if (regions.Any())
                    focApplication.BudgetRegion = regions.First().Name;

                var queryUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
                var ownerUser = await queryUser.Select(a => new { a.Id, a.Name }).FirstOrDefaultAsync(a => a.Id == budget.OwnerId);
                if (ownerUser != null)
                {
                    focApplication.BudgetOwner = ownerUser.Name;
                }
                focApplication.BudgetOwnerId = budget.OwnerId;
                focApplication.BudgetFile = budget.AttachmentFile;
            }
            else
            {
                focApplication.BudgetId = null;
                focApplication.BudgetCode = null;
                focApplication.BudgetBuId = null;
                focApplication.BudgetBuName = null;
                focApplication.SubBudgetId = null;
                focApplication.SubBudgetCode = null;
                focApplication.BudgetRegionId = null;
                focApplication.BudgetMCode = null;
                focApplication.BudgetUsedQty = null;
                focApplication.BudgetQty = null;
                focApplication.BudgetAvailableQty = null;
                focApplication.BudgetDescription = null;
                focApplication.BudgetRegion = null;
                focApplication.BudgetOwner = null;
                focApplication.BudgetOwnerId = null;
                focApplication.BudgetFile = null;
            }

            //FOC Nature
            if (request.NatureId.HasValue)
            {
                var expenseNatures = await dataverseService.GetCostNatureAsync();
                var nature = expenseNatures.FirstOrDefault(a => a.Id == request.NatureId);
                if (nature == null)
                    return MessageResult.FailureResult($"未找到Id为{request.NatureId}的费用性质数据");
                focApplication.NatureId = nature.Id;
                focApplication.NatureName = nature.Name;
                focApplication.NatureDescription = nature.Description;
            }
            else
            {
                focApplication.NatureId = null;
                focApplication.NatureName = null;
                focApplication.NatureDescription = null;
            }

            //发货类型
            if (request.ShippingTypeId.HasValue)
            {
                //获取发货类型
                var shippingTypeList = await dataverseService.GetDictionariesAsync(DictionaryType.ShippingTypes);
                var shippingType = shippingTypeList.Where(a => a.Id == request.ShippingTypeId).FirstOrDefault();
                if (shippingType == null)
                    return MessageResult.FailureResult($"未找到Id为{request.ShippingTypeId}的发货类型数据");
                focApplication.ShippingTypeName = shippingType.Name;
            }
            else
            {
                focApplication.ShippingTypeId = null;
                focApplication.ShippingTypeName = null;
            }

            //客户
            if (request.ClientId.HasValue)
            {
                var queryStore = await LazyServiceProvider.LazyGetService<IStoreInfoRepository>().GetQueryableAsync();
                var client = await queryStore.FirstOrDefaultAsync(a => a.Id == request.ClientId);
                if (client == null)
                    return MessageResult.FailureResult($"未找到Id为{request.ClientId}的客户数据");
                focApplication.ClientCode = client.StoreCode;
                focApplication.ClientName = client.StoreName;
            }
            else
            {
                if (!string.IsNullOrEmpty(request.ClientCode))
                    focApplication.ClientCode = request.ClientCode;
                else
                    focApplication.ClientCode = null;

                if (!string.IsNullOrEmpty(request.ClientName))
                    focApplication.ClientName = request.ClientName;
                else
                    focApplication.ClientName = null;
            }

            //终端
            if (request.TerminalId.HasValue)
            {
                //获取终端/其它
                var terminalList = await dataverseService.GetDictionariesAsync(DictionaryType.FocTerminals);
                var terminal = terminalList.Where(a => a.Id == request.TerminalId).FirstOrDefault();
                if (terminal == null)
                    return MessageResult.FailureResult($"未找到Id为{request.TerminalId}的终端数据");
                focApplication.TerminalName = terminal.Name;
            }
            else
            {
                focApplication.TerminalName = null;
            }

            //客户收货代码
            if (!string.IsNullOrEmpty(request.ReceivingCode))
            {
                focApplication.ReceivingCode = request.ReceivingCode;
                if (!string.IsNullOrEmpty(request.ConsigneeAddress))
                    focApplication.ConsigneeAddress = AesHelper.Encryption(request.ConsigneeAddress, insightKey);
                if (!string.IsNullOrEmpty(request.ConsigneeName))
                    focApplication.ConsigneeName = AesHelper.Encryption(request.ConsigneeName, insightKey);
                if (!string.IsNullOrEmpty(request.ConsigneePhone))
                    focApplication.ConsigneePhone = AesHelper.Encryption(request.ConsigneePhone, insightKey);
            }

            focApplication.Attachment = request.Attachment?.Select(a => a.AttachmentId).JoinAsString(",");
            focApplication.Status = FOCStatus.Draft;

            return MessageResult.SuccessResult(focApplication);
        }

        /// <summary>
        /// 处理FOC申请明细数据
        /// </summary>
        /// <param name="sticketApplication"></param>
        /// <param name="applicationDetailRequests"></param>
        /// <returns></returns>
        async Task<MessageResult> HandleDraftFocApplicationDetailAsync(FOCApplication focApplication, IEnumerable<CreateUpdateFocApplicationDetailRequest> applicationDetailRequests)
        {
            var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");
            List<FOCApplicationDetail> originalFocDetails = null, newFocDetails = null;
            if (applicationDetailRequests?.Any() == true)
            {
                var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
                var queryFocDetail = await LazyServiceProvider.LazyGetService<IFocApplicationDetailRepository>().GetQueryableAsync();
                var focDetailRepository = LazyServiceProvider.LazyGetService<IFocApplicationDetailRepository>();
                originalFocDetails = queryFocDetail.Where(a => a.ParentID == focApplication.Id).ToList();

                newFocDetails = new List<FOCApplicationDetail>(originalFocDetails);

                var index = newFocDetails.Any() ? newFocDetails.Max(a => a.RowId) + 1 : 1;
                foreach (var item in applicationDetailRequests)
                {
                    FOCApplicationDetail detail;
                    if (item.Id.HasValue)
                    {
                        //已提交后的数据，不允许编辑明细行，只能反冲
                        if (focApplication.ApplyTime.HasValue)
                            continue;

                        detail = newFocDetails.First(a => a.Id == item.Id);
                        detail = ObjectMapper.Map(item, detail);
                        await focDetailRepository.UpdateAsync(detail);
                    }
                    else
                    {
                        detail = ObjectMapper.Map<CreateUpdateFocApplicationDetailRequest, FOCApplicationDetail>(item);
                        detail.RowId = index++;
                        detail.ParentID = focApplication.Id;
                        if (!string.IsNullOrEmpty(detail.ConsigneeAddress))
                            detail.ConsigneeAddress = AesHelper.Encryption(detail.ConsigneeAddress, insightKey);
                        if (!string.IsNullOrEmpty(detail.ConsigneeName))
                            detail.ConsigneeName = AesHelper.Encryption(detail.ConsigneeName, insightKey);
                        if (!string.IsNullOrEmpty(detail.ConsigneePhone))
                            detail.ConsigneePhone = AesHelper.Encryption(detail.ConsigneePhone, insightKey);

                        //城市主数据
                        CityMasterDataDto city = null;
                        if (item.CityId.HasValue)
                        {
                            var cities = await dataverseService.GetSpecialCitiesAsync(item.CityId.ToString());
                            city = cities.FirstOrDefault();
                        }
                        detail.CityName = city?.Name;

                        if (item.HedgeDetailId.HasValue)
                        {
                            detail.IsHedge = true;//反冲行
                            var hedgeDetail = newFocDetails.First(a => a.Id == detail.HedgeDetailId.Value);
                            hedgeDetail.IsHedge = true;//被反冲行
                            await focDetailRepository.UpdateAsync(hedgeDetail);
                        }

                        newFocDetails.Add(detail);
                        await focDetailRepository.InsertAsync(detail);
                    }
                }
            }

            return MessageResult.SuccessResult(new Tuple<List<FOCApplicationDetail>, List<FOCApplicationDetail>>(originalFocDetails, newFocDetails));
        }

        /// <summary>
        /// 转换费用性质的Approval Number
        /// </summary>
        /// <param name="newPrDetails"></param>
        /// <returns></returns>
        async Task<IEnumerable<(decimal TotalAmount, string ApprovalNumber)>> TransferNatureApprovalNumber(List<FOCApplicationDetail> focDetails, Guid natureId, string productMCode)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            //获取美刀货币配置
            var usdCurrencies = await dataverseService.GetCurrencyConfig(DictionaryType.CurrencyItems.USD);
            var usdCurrency = usdCurrencies.First();
            var costNatures = await dataverseService.GetCostNatureAsync(stateCode: null);
            //获取产品成本值
            var productCosts = await dataverseService.GetProductCostAsync();
            var productCost = productCosts.Where(a => a.ProductMcode == productMCode).First();
            //创建审批任务
            var focApplicationDetails = focDetails.Select(a =>
            {
                var costNatures = dataverseService.GetCostNatureAsync(natureId.ToString()).Result;
                var costNature = costNatures.FirstOrDefault();

                return
                (
                    decimal.Round((productCost.CostValue * a.ProductQuantity) / (decimal)usdCurrency.PlanRate, 4),
                    costNature?.ApprovalNumber
                );

            }).ToArray();

            return focApplicationDetails;
        }

        /// <summary>
        /// 验证预算
        /// </summary>
        /// <param name="focApplication"></param>
        /// <param name="detailItems"></param>
        /// <returns></returns>
        async Task<MessageResult> ValidateBudgetAsync(FOCApplication focApplication, IEnumerable<FOCApplicationDetail> detailItems)
        {
            //检查子预算是否足够
            var useBudgetRequest = new UseFocBudgetRequestDto
            {
                FocApplicationId = focApplication.Id,
                SubbudgetId = focApplication.SubBudgetId.Value,
                Items = detailItems.Select(a => new FocUseInfo { RowNo = a.RowId, UseQty = a.ProductQuantity })
            };
            var result = await LazyServiceProvider.LazyGetService<IFocSubbudgetService>().CheckFocSubbudgetQtySufficientAsync(useBudgetRequest);
            if (result.Success)
                result.Data = useBudgetRequest;

            return result;
        }

        /// <summary>
        /// 物流填写验证明细
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        async Task<MessageResult> ValidateFocLogisticsDetails(List<CreateFocLogisticsRequestDto> request)
        {
            var focDetailIds = request.Select(a => a.FOCDetailId).ToList();
            var focApplicationQuery = await LazyServiceProvider.LazyGetService<IFocApplicationRepository>().GetQueryableAsync();
            var focApplicationDetailQuery = await LazyServiceProvider.LazyGetService<IFocApplicationDetailRepository>().GetQueryableAsync();
            //focApplicationDetailQuery = focApplicationDetailQuery.Where(a => focDetailIds.Contains(a.Id));
            var focProductDetailRepository = LazyServiceProvider.LazyGetService<IFocApplicationProductDetailRepository>();
            var focProductDetailQuery = await focProductDetailRepository.GetQueryableAsync();
            var focLogisticsQuery = await LazyServiceProvider.LazyGetService<IFocApplicationLogisticsDetailRepository>().GetQueryableAsync();

            //var focDetailId = focDetailIds.FirstOrDefault();
            //var focApplicationId = focApplicationDetailQuery.FirstOrDefault(a => a.Id == focDetailId).ParentID;

            //var bpcsOrderNos = request
            //.SelectMany(foc => foc.ProductDetailItems)
            //.SelectMany(item => item.LogisticsDetailItems)
            //.Select(logistics => logistics.BpcsOrderNo)
            //.ToList();

            //var query = focLogisticsQuery.Where(a => bpcsOrderNos.Contains(a.BpcsOrderNo))
            //    .Join(focApplicationDetailQuery, a => a.FOCDetailId, a => a.Id, (a, b) => new { logistics = a, focDetail = b })
            //    .Join(focApplicationQuery, a => a.focDetail.ParentID, a => a.Id, (a, b) => new { a.logistics, a.focDetail, foc = b })
            //    .Select(a => new
            //    {
            //        FocId = a.foc.Id,
            //        a.foc.ApplicationCode,
            //        LogisticsId = a.logistics.Id,
            //        a.logistics.FOCDetailId,
            //        a.logistics.BpcsOrderNo,
            //        a.logistics.BatchNo,
            //    }).ToList();

            foreach (var item in request)
            {
                foreach (var productItem in item.ProductDetailItems)
                {
                    var logisticsDetails = productItem.LogisticsDetailItems;
                    if (logisticsDetails != null)
                    {
                        foreach (var logisticsItem in logisticsDetails)
                        {
                            if (logisticsItem.ShippedQty.HasValue)
                            {
                                //var bpcsNo = logisticsItem.BpcsOrderNo;
                                //if (!string.IsNullOrEmpty(logisticsItem.BpcsOrderNo))
                                //{
                                //    //判断BPCS单号是否在其他申请单下
                                //    var bpcsLogisticsDatas = query.FirstOrDefault(a => a.BpcsOrderNo == logisticsItem.BpcsOrderNo && a.FocId != focApplicationId);
                                //    if (bpcsLogisticsDatas != null)
                                //        return MessageResult.FailureResult($"产品{productItem.ProductSCode} 填写的BPCS单号重复");
                                //}
                                if (logisticsItem.ShippedQty > 0)
                                {
                                    var sumShippedQty = logisticsDetails.Where(a => a.ShippedQty.HasValue).Sum(a => a.ShippedQty).Value;
                                    var productDetail = focProductDetailQuery.FirstOrDefault(a => a.FOCDetailId == item.FOCDetailId && a.ProductSCode == productItem.ProductSCode);
                                    if (productDetail != null)
                                    {
                                        //待发货数量
                                        //var pendingQty = productDetail.ProductQty - productDetail.ShippedQty;
                                        var pendingQty = productDetail.WriteoffQty;
                                        if (sumShippedQty > pendingQty)
                                        {
                                            return MessageResult.FailureResult($"产品{productItem.ProductSCode} 的发货数量多于待发货数量");
                                        }
                                    }
                                }
                                else
                                {
                                    return MessageResult.FailureResult($"产品{productItem.ProductSCode} 的发货数量必须大于0");
                                }
                            }
                        }
                    }
                }
            }
            return MessageResult.SuccessResult();
        }
        public async Task<MessageResult> PushFOCForSOIAsync(Guid Id)
        {
            var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");
            var appkey = _configuration["Integrations:SOI:AppKey"];
            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            string encryptText = $"appkey={appkey}method=ReceiverEPODatatimestamp={timestamp}";
            var Sign = encryptText.EncryptSHA3();
            var token = await _httpRequestService.GetSOITokenAsync();
            var focApplicationRepository = LazyServiceProvider.LazyGetService<IFocApplicationRepository>();
            var focApplicationQuery = (await focApplicationRepository.GetQueryableAsync()).AsNoTracking();
            var focDetailRepository = LazyServiceProvider.LazyGetService<IFocApplicationDetailRepository>();
            var focDetailQuery = (await focDetailRepository.GetQueryableAsync()).AsNoTracking();
            var focProductDetailRepository = LazyServiceProvider.LazyGetService<IFocApplicationProductDetailRepository>();
            var focProductDetailQuery = (await focProductDetailRepository.GetQueryableAsync()).AsNoTracking();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var expenseNature = await dataverseService.GetCostNatureAsync();
            var queryBudget = (await LazyServiceProvider.LazyGetService<IFocSubBudgetRepository>().GetQueryableAsync()).AsNoTracking();
            var focJoinQuery = focDetailQuery.Where(d => d.IsHedge == false).GroupJoin(focProductDetailQuery, a => a.Id, b => b.FOCDetailId, (a, b) => new
            {
                focd = a,
                b,
            }).SelectMany(a => a.b.DefaultIfEmpty(), (a, b) => new
            {
                a.focd.Id,
                b.RowId,
                a.focd.CityName,
                a.focd.ParentID,
                a.focd.ConsigneeName,
                a.focd.ConsigneeAddress,
                a.focd.ConsigneePhone,
                a.focd.Description,
                a.focd.ProductQuantity,
                a.focd.ActivityStartDate,
                ProductSCode = b == null ? string.Empty : b.ProductSCode,
                ProductName = b == null ? string.Empty : b.ProductName,
                productQty = b == null ? 0 : b.ProductQty,
                //ProductMultiPack = b == null ? null : b?.ProductMultiPack,

            });
            var queryUser = (await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync()).AsNoTracking();
            var foc = await focApplicationQuery.GroupJoin(focJoinQuery, a => a.Id, b => b.ParentID, (a, b) => new { main = a, details = b }).FirstAsync(s => s.main.Id == Id);
            var focData = foc.main;
            var focdetail = foc.details;
            var soiVerifyType = expenseNature.FirstOrDefault(f => f.Id == focData.NatureId)?.SoiVerifyType ?? SoiVerifyType.DirectDelivery;
            var focType = soiVerifyType.GetDescription();

            var subBudget = await queryBudget.FirstAsync(s => s.Id == focData.SubBudgetId);
            PushFOCApplicationRequestDto requestDto = new()
            {
                UserDspName = focData.ApplyUser,
                FocPostDate = focData.ApplyTime?.ToString("yyyy-MM-dd HH:mm:dd"),
                FocCode = focData.ApplicationCode,
                FocBcCode = focData.SubBudgetCode,
                FocBcDesc = subBudget.Description,
                OwnerUser = (await queryUser.FirstAsync(f => f.Id == subBudget.OwnerId)).UserName,
                BuDesc = focData.BudgetBuName,
                FocCcCode = focData.CostCenter,
                FocnNatureDesc = focData.NatureDescription,
                SoiVerifyType = focType,
                FocType = focData.ShippingTypeName == "发货至经销商" ? "1" : "2",
                FocAgencyCode = focData.ClientCode,
                FocAgencyName = focData.ClientName,
                Itmshiptocode = string.IsNullOrEmpty(focData.ReceivingCode) ? "1" : focData.ReceivingCode.Split('-')[1],
                FocRemarks = focData.Remark,
                //FocQty = focData.ProductQty,
            };
            List<int> indexlist = [];
            var prodQuantity = 0;
            foreach (var item in focdetail)
            {
                indexlist.Clear();
                var addr = !string.IsNullOrEmpty(item.ConsigneeAddress) ? AesHelper.Decryption(item.ConsigneeAddress, insightKey) : item.ConsigneeAddress;
                indexlist.AddRange([addr.IndexOf('省'), addr.IndexOf('市'), addr.IndexOf('区')]);
                var filterIndexs = indexlist.Where(m => m >= 0);
                var minIndex = 3;
                if (filterIndexs.Any())
                    minIndex = filterIndexs.Min() + 1;
                var replaceLenght = addr.Length - minIndex - 1;
                string pattern = $@"(.{{{minIndex}}}).{{{replaceLenght}}}(.{{{1}}})";

                var phone = !string.IsNullOrEmpty(item.ConsigneePhone) ? AesHelper.Decryption(item.ConsigneePhone, insightKey) : item.ConsigneePhone;
                var name = !string.IsNullOrEmpty(item.ConsigneeName) ? AesHelper.Decryption(item.ConsigneeName, insightKey) : item.ConsigneeName;
                FOCDetailDto fOCDetail = new()
                {
                    ItmNo = item.RowId.ToString(),
                    ItmLoc = item.CityName,
                    ItmConsignee = $"{DesensitizeName(name)} {Regex.Replace(phone, $@"(\d{{{3}}})\d{{{phone.Length - 7}}}(\d{{{4}}})", "$1****$2")}",
                    ItmAddr = Regex.Replace(addr, pattern, "$1****$2"),
                    //ItmConsigneePhone = ,
                    ItmDesc = item.Description,
                    ItmProduct = item.ProductSCode,
                    ItmProductName = item.ProductName,
                    ItmSpec = focData.ProductUnit,
                    ItmQty = item.productQty,
                    ItmActivityStartDate = item.ActivityStartDate?.ToString("yyyy-MM"),
                };
                prodQuantity += item.productQty;
                requestDto.Details.Add(fOCDetail);
            }
            requestDto.FocQty = prodQuantity;
            //var json = Json.JsonSerializer.Serialize(requestDto);
            //return json;
            var requestData = new
            {
                appkey = appkey,
                accessToken = token,
                timestamp = timestamp,
                method = "ReceiverEPOData",
                Sign = Sign,
                data = requestDto
            };
            var json = Json.JsonSerializer.Serialize(requestData);
            var result = await _httpRequestService.HttpPostAsync<MessageResult>(json, $"{_configuration["Integrations:SOI:BaseUrl"]}/SOIWebAPI/api/EFOC/ReceiverEFOCData", formNo: requestDto.FocCode);
            if (!result.Success)
            {
                return MessageResult.FailureResult(result.Message);
            }
            return MessageResult.SuccessResult();
        }
        private static string DesensitizeName(string name)
        {
            if (name.Length == 2)
            {
                return Regex.Replace(name, @"(?<=.).", "*");
            }
            else if (name.Length > 2)
            {
                return Regex.Replace(name, @"(?<=^.).(?=.*.$)", "*");
            }
            return name;
        }
        /// <summary>
        /// SOI核销结果返回接口
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> GetSOIWriteOffResultAsync(List<WriteOffResultRequestDto> request)
        {
            SetOperationLogRequestDto log = _commonService.InitOperationLog("SOI", "FOC核销", Json.JsonSerializer.Serialize(request));
            try
            {
                //request = request.Distinct().OrderBy(o => o.Actiontimestamp).ToList();
                var focRepository = LazyServiceProvider.LazyGetService<IFocApplicationRepository>();
                var queryfoc = (await focRepository.GetQueryableAsync());

                var PONos = request.Select(s => s.PoNo).ToList();
                var focsQuery = queryfoc.Where(s => PONos.Contains(s.ApplicationCode)).AsEnumerable();
                //记录所有造作时间，
                //var operationTimes = request.Select(s => s.Actiontimestamp).ToList();
                //var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
                ////var focs = focsQuery.Select(s => new { s.Id, s.ApplicationCode });
                //var expenseNature = await dataverseService.GetCostNatureAsync();
                //var focIds = focs.Select(s => s.Id).ToList();

                //if (focIds.Count == 0) return MessageResult.FailureResult("申请编号错误，未查到相应的申请单");
                var focDetailRepository = LazyServiceProvider.LazyGetService<IFocApplicationDetailRepository>();
                var focProductDetailRepository = LazyServiceProvider.LazyGetService<IFocApplicationProductDetailRepository>();
                var logisticsRepository = LazyServiceProvider.LazyGetService<IFocApplicationLogisticsDetailRepository>();
                var ISOIWriteoffResultReponsitory = LazyServiceProvider.LazyGetService<ISOIWriteoffResultReponsitory>();

                //var SOIWriteoffResultDatas = (await ISOIWriteoffResultReponsitory.GetQueryableAsync()).AsNoTracking().Where(m => operationTimes.Contains(m.ActionTimeStamp.Value)).AsEnumerable();
                var focDetailQuery = await focDetailRepository.GetQueryableAsync();
                var focProductQuery = await focProductDetailRepository.GetQueryableAsync();
                var logisticsQuery = await logisticsRepository.GetQueryableAsync();
                var lineNumbers = request.Select(s => s.LineNumber).ToList();

                if (lineNumbers.Count == 0) return MessageResult.SuccessResult();
                //查询数据
                var datas = await queryfoc.Join(focDetailQuery, a => a.Id, b => b.ParentID, (a, b) => new
                {
                    foc = a,
                    detail = b
                }).Join(focProductQuery, a => a.detail.Id, b => b.FOCDetailId, (a, b) => new { a.foc, a.foc.ApplicationCode, a.detail, prod = b })
                 .Where(m => PONos.Contains(m.ApplicationCode)).ToListAsync();

                if (datas.Count == 0) return MessageResult.FailureResult("申请编号错误，未查到相应的申请单");

                //获取详情Id
                var detailIds = datas.Select(s => s.detail.Id).Distinct().ToArray();
                //产品S code
                var productSCodes = datas.Select(s => s.prod.ProductSCode).Distinct().ToArray();

                var logisticsDatas = await logisticsQuery.Where(l => detailIds.Contains(l.FOCDetailId) && productSCodes.Contains(l.ProductSCode)).ToListAsync();
                IList<SOIWriteoffResult> SOIWriteoffResults = [];
                IList<FOCApplicationProductDetail> productDetails = [];
                //截止状态的详情
                HashSet<Guid> expireApplis = [];
                HashSet<FOCApplication> focApplications = [];
                List<ReturnFocBudgetRequestDto> returnFocBudgets = [];
                //不需要处理的
                List<FOCApplicationLogisticsDetail> noProcessingLogistics = [];
                //新增的
                List<FOCApplicationLogisticsDetail> insertLogistics = [];
                //删除的取消
                List<FOCApplicationLogisticsDetail> deleteLogistics = [];
                //结算
                //List<FOCApplicationLogisticsDetail> settlmentLogistics = [];
                //已核销金额
                var subVerifiedAmount = decimal.Zero;
                foreach (var item in request)
                {
                    var mes = item.VerifySoiFocWriteOff();
                    if (!string.IsNullOrEmpty(mes)) throw new Exception(mes);
                    var foc = datas.FirstOrDefault(f => f.ApplicationCode == item.PoNo && f.prod.RowId == item.LineNumber);
                    if (foc == null) throw new Exception($"申请编号错误，未查到相应的申请：{item.PoNo},行号{item.LineNumber}");
                    //var isPostOpreation = SOIWriteoffResultDatas.Any(f => f.DetailId == foc.prod.Id && f.ActionTimeStamp == item.Actiontimestamp);
                    var focApplication = foc.foc;
                    //if (isPostOpreation) continue;
                    subVerifiedAmount = decimal.Zero;
                    //产品详情
                    var prodDetail = foc.prod;
                    var detail = foc.detail;
                    //var soiVerifyType = expenseNature.FirstOrDefault(f => f.Id == focApplication.NatureId)?.SoiVerifyType;
                    SOIWriteoffResult sOIWriteoffResult = new()
                    {
                        DetailId = prodDetail.Id,
                        IsPass = item.Pass,
                        CancelStatus = item.CancelStatus,
                        FailureReason = item.CancelReason,
                        WriteOffQuantity = item.Qty,
                        ActionTimeStamp = item.Actiontimestamp,
                        Status = item.Status,
                        LockStatus = item.LockStatus,
                        Flag = item.Flag,
                        Type = item.Type,
                        SettlementQuantity = item.SettlementQty,
                        OrderNumber = item.OrderNumber,
                        BatchNumber = item.BatchNumber,
                        ItmProduct = item.ItmProduct
                    };
                    SOIWriteoffResults.Add(sOIWriteoffResult);
                    if (item.Flag == SettlementStatus.Settlement)
                    {
                        //当前detail详情数据
                        //var thisDetailLogisticsDatas = logisticsDatas.Where(l => detail.Id == l.FOCDetailId);
                        //settlmentLogistics.AddRange(thisDetailLogisticsDatas);
                        //var settlementExistLogics = thisDetailLogisticsDatas.Where(l => item.OrderNumber == l.BpcsOrderNo && l.BatchNo == item.BatchNumber);//
                        var settlementExistLogics = logisticsDatas.Where(l => item.OrderNumber == l.BpcsOrderNo);//&& l.BatchNo == item.BatchNumber

                        //删除该BPCS单号下的所有logistics数据
                        foreach (var settlement in settlementExistLogics)
                        {
                            var isExistsInDeleteLogistics = deleteLogistics.Any(a => a.Id == settlement.Id);
                            if (!isExistsInDeleteLogistics)
                                deleteLogistics.Add(settlement);
                        }

                        FOCApplicationLogisticsDetail applicationLogisticsDetail = new()
                        {
                            FOCDetailId = detail.Id,
                            ProductSCode = prodDetail.ProductSCode,
                            ProductName = prodDetail.ProductName,
                            BpcsOrderNo = item.OrderNumber,
                            BatchNo = item.BatchNumber,
                        };
                        insertLogistics.Add(applicationLogisticsDetail);

                        //if (settlementExistLogics.Any())
                        //{
                        //    foreach (var settlement in settlementExistLogics)
                        //    {
                        //        var noProcessing = noProcessingLogistics.Any(a => a.Id == settlement.Id);
                        //        if (!noProcessing)
                        //            noProcessingLogistics.Add(settlement);
                        //    }
                        //}
                        //if (settlementLogics.Any())
                        //{
                        //    foreach (var settlement in settlementLogics)
                        //    {
                        //        var noProcessing = noProcessingLogistics.Any(a => a.Id == settlement.Id);
                        //        if (!noProcessing)
                        //            noProcessingLogistics.Add(settlement);
                        //    }
                        //}
                        //else
                        //{
                        //    FOCApplicationLogisticsDetail applicationLogisticsDetail = new()
                        //    {
                        //        FOCDetailId = detail.Id,
                        //        ProductSCode = prodDetail.ProductSCode,
                        //        ProductName = prodDetail.ProductName,
                        //        BpcsOrderNo = item.OrderNumber,
                        //        BatchNo = item.BatchNumber,
                        //    };
                        //    insertLogistics.Add(applicationLogisticsDetail);
                        //}
                    }
                    else if (item.Flag == SettlementStatus.CancelSettlement)
                    {
                        //当前detail详情数据
                        var cancelLogics = logisticsDatas.Where(l => item.OrderNumber == l.BpcsOrderNo);
                        foreach (var settlement in cancelLogics)
                        {
                            var isExistsInDeleteLogistics = deleteLogistics.Any(a => a.Id == settlement.Id);
                            if (!isExistsInDeleteLogistics)
                                deleteLogistics.Add(settlement);
                        }
                    }
                    else
                    {
                        subVerifiedAmount = prodDetail.WriteoffQty + item.Qty.Value;
                        if (subVerifiedAmount > prodDetail.ProductQty)
                            throw new Exception($"核销数量超过详情总数量。单号:{item.PoNo};行号:{item.LineNumber};总数量:{prodDetail.ProductQty}");
                        else if (subVerifiedAmount < 0) throw new Exception($"核销后核销数量小于0。单号:{item.PoNo};行号:{item.LineNumber};核销数量:{subVerifiedAmount}");
                        else
                            prodDetail.WriteoffQty = (int)subVerifiedAmount;
                        if (item.LockStatus == FreezeStatus.Ended)
                        {
                            if (prodDetail.ProductQty > prodDetail.WriteoffQty)
                            {
                                prodDetail.VerificationStatus = VerificationStatusEnum.Expired;
                                //核销截止，返回预算
                                var returnFocBudgetRequest = new ReturnFocBudgetRequestDto
                                {
                                    FocId = focApplication.Id,
                                    SubbudgetId = focApplication.SubBudgetId.Value,

                                };
                                var returnItem = new FocReturnInfo()
                                {
                                    PdRowNo = detail.RowId,
                                    ReturnQty = prodDetail.ProductQty - prodDetail.WriteoffQty,
                                    ReturnSourceId = prodDetail.Id,
                                    ReturnSourceCode = focApplication.ApplicationCode,
                                };
                                returnFocBudgetRequest.Items = [returnItem];
                                returnFocBudgets.Add(returnFocBudgetRequest);
                            }
                            else prodDetail.VerificationStatus = VerificationStatusEnum.Completed;
                        }
                        else
                        {
                            prodDetail.VerificationStatus = (prodDetail.ProductQty > prodDetail.WriteoffQty)
                                ? VerificationStatusEnum.Partial
                                : VerificationStatusEnum.Completed;
                        }
                        expireApplis.Add(focApplication.Id);
                        var isExist = focApplications.FirstOrDefault(s => s.Id == foc.foc.Id);
                        //if (isExist == null && (soiVerifyType != SoiVerifyType.VerifiedWithOrder || focApplication.Status != FOCStatus.Shipped))
                        if (isExist == null)
                        {
                            focApplication.Status = FOCStatus.PendingShipment;
                            focApplications.Add(focApplication);
                        }

                        productDetails.Add(prodDetail);
                    }
                }
                var result = await LazyServiceProvider.LazyGetService<IFocSubbudgetService>().ReturnFocSubbudgetAsync(returnFocBudgets);
                if (!result.Success) throw new Exception(result.Message);
                await focProductDetailRepository.UpdateManyAsync(productDetails, true);
                //截止数据
                if (expireApplis.Count > 0)
                {
                    var expireDatas = queryfoc.Join(focDetailQuery, a => a.Id, b => b.ParentID, (a, b) => new
                    {
                        focId = a.Id,
                        detailId = b.Id,
                    }).Join(focProductQuery, a => a.detailId, b => b.FOCDetailId, (a, b) => new { a.focId, b.VerificationStatus, b.WriteoffQty, b.ShippedQty })
                  .Where(m => expireApplis.Contains(m.focId)).AsEnumerable();
                    var isChangeCompleted = expireDatas.GroupBy(s => s.focId, (key, d) => new
                    {
                        key,
                        isAllExpire = d.All(a => (a.WriteoffQty - a.ShippedQty) == 0 && (a.VerificationStatus == VerificationStatusEnum.Expired || a.VerificationStatus == VerificationStatusEnum.Completed))
                    }).Where(m => m.isAllExpire);
                    foreach (var item in isChangeCompleted)//所有明细行截止，主单变成已完成
                    {
                        var compfocApplication = focApplications.FirstOrDefault(f => f.Id == item.key);
                        if (compfocApplication != null)
                            compfocApplication.Status = FOCStatus.Shipped;
                    }
                }
                await focRepository.UpdateManyAsync(focApplications);
                //结算逻辑
                //if (settlmentLogistics.Count > 0)
                //{
                //    //排除掉不处理的单据
                //    var delete = settlmentLogistics.ExceptBy(noProcessingLogistics.Select(s => s.Id), key => key.Id).DistinctBy(d => d.Id).ToList();
                //    deleteLogistics.AddRange(delete);
                //}
                if (insertLogistics.Count > 0)
                    await logisticsRepository.InsertManyAsync(insertLogistics);
                //20250429注释掉
                if (deleteLogistics.Count > 0)
                    await logisticsRepository.DeleteManyAsync(deleteLogistics.DistinctBy(d => d.Id).ToList());
                //发送结算提醒邮件
                await SendSettlementEmail(focApplications);
                await ISOIWriteoffResultReponsitory.InsertManyAsync(SOIWriteoffResults, true);
                _commonService.LogResponse(log, "success");
                return MessageResult.SuccessResult("提交成功!");
            }
            catch (Exception e)
            {
                _commonService.LogResponse(log, e.Message, false);
                return MessageResult.FailureResult(e.Message);

            }
        }
        async Task SendChooseProductEmail(List<FOCApplication> infos)
        {
            if (infos.Count == 0) return;
            infos = infos.DistinctBy(a => a.Id).ToList();
            var webRoot = LazyServiceProvider.LazyGetService<IWebHostEnvironment>().WebRootFileProvider;
            var html = await webRoot.GetFileInfo("Templates/Email/FOCShipments.html").ReadAsStringAsync();
            var _identityUserRepository = _serviceProvider.GetService<IIdentityUserRepository>();
            var applyUserIds = infos.Select(s => s.ApplyUserId).Distinct().ToList();
            var users = await _identityUserRepository.GetListByIdsAsync(applyUserIds);
            List<InsertSendEmaillRecordDto> sendEmaillRecordls = [];
            foreach (var info in infos)
            {
                var bodyHtml = html;
                bodyHtml = bodyHtml.Replace("{Name}", info.ApplyUser);
                bodyHtml = bodyHtml.Replace("{Applicate}", info.ApplyUser);
                bodyHtml = bodyHtml.Replace("{ApplicationCode}", info.ApplicationCode);
                bodyHtml = bodyHtml.Replace("{ApplicationType}", "FOC申请");
                bodyHtml = bodyHtml.Replace("{Link}", $"{_configuration["SpeakerEmail:WebHost"]?.TrimEnd('/')}/foc/logisticsDetail/{info.Id}?type=detail");
                var user = users.FirstOrDefault(f => f.Id == info.ApplyUserId);
                if (user == null) continue;
                var sendEmaillRecords = new InsertSendEmaillRecordDto
                {
                    EmailAddress = user.Email,
                    Subject = $"NexBPM消息中心]由{info.ApplyUser}发起的【FOC申请】进行产品发货",
                    Content = bodyHtml,
                    SourceType = EmailSourceType.Shipments,
                    Status = SendStatus.Pending,
                    Attempts = 0,
                };
                sendEmaillRecordls.Add(sendEmaillRecords);
            }
            //记录邮件，并触发邮件发送功能
            var emailService = LazyServiceProvider.LazyGetService<IEmailService>();
            await emailService.InsertSendEmaillRecordAsync(sendEmaillRecordls);
            // 调度作业
            BackgroundJob.Enqueue<SendEmailWorker>(a => a.DoWorkAsync());
        }
        async Task SendSettlementEmail(HashSet<FOCApplication> infos)
        {
            if (infos.Count == 0) return;
            infos = infos.Where(a => a.Status != FOCStatus.Shipped).ToHashSet();
            var webRoot = LazyServiceProvider.LazyGetService<IWebHostEnvironment>().WebRootFileProvider;
            var bodyHtml = await webRoot.GetFileInfo("Templates/Email/FOCSettlement.html").ReadAsStringAsync();
            List<InsertSendEmaillRecordDto> sendEmaillRecordls = [];
            var _identityUserRepository = _serviceProvider.GetService<Volo.Abp.Identity.IIdentityUserRepository>();
            var _identityRoleRepository = _serviceProvider.GetService<IIdentityRoleRepository>();
            var role = await _identityRoleRepository.FindByNormalizedNameAsync(RoleNames.FOCSupplyChain, false);

            var userList = await _identityUserRepository.GetListByNormalizedRoleNameAsync(role.NormalizedName, false);
            if (userList.Count == 0) return;
            //
            var commonService = LazyServiceProvider.LazyGetService<ICommonService>();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var emailUsers = userList.Select(s => (s.Id, s.Email)).ToList();
            var sendemailBus = await commonService.GetAuthorizedAllDepts(emailUsers);
            //
            var greeting = string.Empty;
            foreach (var info in infos)
            {
                var sendemails = sendemailBus.Where(m => m.Value.Contains(info.ApplyUserDeptId)).SelectMany(s => s.Key).Distinct().JoinAsString(",");
                if (string.IsNullOrEmpty(sendemails)) continue;
                bodyHtml = bodyHtml.Replace("{Applicate}", info.ApplyUser);
                bodyHtml = bodyHtml.Replace("{ApplicationType}", "FOC申请");
                bodyHtml = bodyHtml.Replace("{ApplicationCode}", info.ApplicationCode);
                bodyHtml = bodyHtml.Replace("{Link}", $"{_configuration["SpeakerEmail:WebHost"]?.TrimEnd('/')}/foc/logisticsDetail/{info.Id}?type=detail");
                bodyHtml = bodyHtml.Replace("{Greeting}", "Dear all：");
                var sendEmaillRecords = new InsertSendEmaillRecordDto
                {
                    EmailAddress = sendemails,
                    Subject = $"[NexBPM消息中心]由{info.ApplyUser}发起的【FOC申请】在SOI内进行核销",
                    Content = bodyHtml,
                    SourceType = EmailSourceType.FOCSettlement,
                    Status = SendStatus.Pending,
                    Attempts = 0,
                };
                sendEmaillRecordls.Add(sendEmaillRecords);
            }

            //记录邮件，并触发邮件发送功能
            var emailService = LazyServiceProvider.LazyGetService<IEmailService>();
            await emailService.InsertSendEmaillRecordAsync(sendEmaillRecordls);
            // 调度作业
            BackgroundJob.Enqueue<SendEmailWorker>(a => a.DoWorkAsync());
        }
    }
}
