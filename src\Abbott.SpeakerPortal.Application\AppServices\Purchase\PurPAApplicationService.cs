﻿using Abbott.SpeakerPortal.AppServices.Purchase.PAInvoice;
using Abbott.SpeakerPortal.BackgroundWorkers.BPMEmail;
using Abbott.SpeakerPortal.BackgroundWorkers.Integration.BpmOm;
using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Agent;
using Abbott.SpeakerPortal.Contracts.Approval;
using Abbott.SpeakerPortal.Contracts.Budget.Subbudget;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.Approve;
using Abbott.SpeakerPortal.Contracts.Common.Attachment;
using Abbott.SpeakerPortal.Contracts.Common.BPMEmail;
using Abbott.SpeakerPortal.Contracts.Common.PowerApp;
using Abbott.SpeakerPortal.Contracts.Dataverse;
using Abbott.SpeakerPortal.Contracts.Integration.EpdOlineMeeting;
using Abbott.SpeakerPortal.Contracts.Integration.EpdOlineMeeting.BPM;
using Abbott.SpeakerPortal.Contracts.Purchase.PurGRApplication;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPAApplication.PAInvoice;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPOApplication;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Contracts.Report;
using Abbott.SpeakerPortal.Contracts.System.FinanceReview;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.Budget;
using Abbott.SpeakerPortal.Entities.Common.Attachment;
using Abbott.SpeakerPortal.Entities.Common.Workflow.CurrentApprovalTask;
using Abbott.SpeakerPortal.Entities.Common.Workflow.WorkflowTasks;
using Abbott.SpeakerPortal.Entities.Integration.Dspot;
using Abbott.SpeakerPortal.Entities.Integration.EpdOnlineMeeting;
using Abbott.SpeakerPortal.Entities.OEC.OECIntercepts;
using Abbott.SpeakerPortal.Entities.Purchase.PurGRApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPOApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Entities.Report.ApprovalRecord;
using Abbott.SpeakerPortal.Entities.VendorFinancials;
using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Person;
using Abbott.SpeakerPortal.Utils;
using Abbott.SpeakerPortal.Extension;

using ClosedXML.Excel;

using DocumentFormat.OpenXml.ExtendedProperties;
using DocumentFormat.OpenXml.Office2010.ExcelAc;
using DocumentFormat.OpenXml.Spreadsheet;
using DocumentFormat.OpenXml.Wordprocessing;

using Hangfire;

using Microsoft.AspNetCore.Mvc.ApplicationModels;
using Microsoft.Azure.CognitiveServices.Vision.ComputerVision.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

using Senparc.Weixin.WxOpen.AdvancedAPIs.WxApp.WxAppJson;

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Net.Mail;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;

using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using Volo.Abp.SettingManagement;
using Volo.Abp.Uow;

using static Abbott.SpeakerPortal.Enums.DataverseEnums.PturtTypeConfig;
using static Abbott.SpeakerPortal.Enums.DataverseEnums.Returnreason;
using static Abbott.SpeakerPortal.Enums.OECInterceptTypes;
using static Abbott.SpeakerPortal.Enums.Purchase;
using static Abbott.SpeakerPortal.Enums.PurGRStatus;
using static Abbott.SpeakerPortal.Enums.PurPAStatus;
using static Abbott.SpeakerPortal.Permissions.SpeakerPortalPermissions;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace Abbott.SpeakerPortal.AppServices.Purchase
{
    public partial class PurPAApplicationService : SpeakerPortalAppService, IPurPAApplicationService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<PurPAApplicationService> _logger;
        private readonly IConfiguration _configuration;
        public PurPAApplicationService(ILogger<PurPAApplicationService> logger, IServiceProvider serviceProvider)
        {
            _logger = logger;
            _serviceProvider = serviceProvider;
            _configuration = _serviceProvider.GetService<IConfiguration>();
        }
        /// <summary>
        /// 创建付款申请数据
        /// </summary>
        /// <param name="createPaDto"></param>
        /// <returns></returns>
        public async Task<Guid> CreatePurPAApplicationAsync(CreatePurPAApplicationDto createPaDto)
        {
            var paApplicationRepository = LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>();
            var paDetailRepository = LazyServiceProvider.LazyGetService<IPurPAApplicationDetailRepository>();
            var queryPr = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var queryPrDetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var querGr = await LazyServiceProvider.LazyGetService<IPurGRApplicationRepository>().GetQueryableAsync();
            var queryGrDetail = await LazyServiceProvider.LazyGetService<IPurGRApplicationDetailRepository>().GetQueryableAsync();
            var queryPo = await LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>().GetQueryableAsync();
            var queryPoDetail = await LazyServiceProvider.LazyGetService<IPurPOApplicationDetailsRepository>().GetQueryableAsync();
            var prDetailHistoryRepository = LazyServiceProvider.LazyGetService<IPurGRApplicationDetailHistoryRepository>();
            var queryBpcsAvm = (await LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync()).AsNoTracking();
            var bpcsPmfvm = (await LazyServiceProvider.LazyGetService<IBpcsPmfvmRepository>().GetQueryableAsync()).AsNoTracking();
            var queryBpcsAvt = (await LazyServiceProvider.LazyGetService<IBpcsAvtRepository>().GetQueryableAsync()).AsNoTracking();
            var queryOnlineMeeting = await LazyServiceProvider.LazyGetService<IInteOnlineMeetingRepository>().GetQueryableAsync();
            var queryPrDetailHistory = await prDetailHistoryRepository.GetQueryableAsync();
            var queryVendor = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();
            var queryFinancial = await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var grApplication = querGr.Where(a => a.Id == createPaDto.GRId).FirstOrDefault();
            var prAppLication = queryPr.Where(a => a.Id == grApplication.PrId).FirstOrDefault();
            var grDetailHistorys = queryPrDetailHistory.Where(a => createPaDto.GrDetailHistoryIds.Contains(a.Id)).ToList();
            var paApplication = new PurPAApplication();
            PurPOApplication poApplication = null;
            paApplication.PayMethod = PayMethods.AR;//默认付款方式AR
            paApplication.DeliveryMode = DeliveryModes.OffLine;
            paApplication.Status = PurPAApplicationStatus.FillIn;
            paApplication.ApplyUserId = prAppLication.ApplyUserId;
            paApplication.ApplyUserName = prAppLication.ApplyUserIdName;
            paApplication.ApplyTime = DateTime.Now;
            paApplication.ApplyUserBu = prAppLication.ApplyUserBu;
            paApplication.ApplyUserBuName = prAppLication.ApplyUserBuName;
            paApplication.ApplyUserBuToDept = prAppLication.ApplyUserDept;
            paApplication.ApplyUserBuToDeptName = prAppLication.ApplyUserDeptName;
            paApplication.VendorId = poApplication == null ? grApplication.VendorId : poApplication.VendorId;
            paApplication.VendorName = poApplication == null ? grApplication.VendorName : poApplication.VendorName;
            paApplication.VendorCode = poApplication == null ? grApplication.VendorCode : poApplication.VendorCode;
            paApplication.GRId = grApplication.Id;
            paApplication.GRApplicationCode = grApplication.ApplicationCode;
            paApplication.POId = grApplication.PoId;
            paApplication.PRId = grApplication.PrId;
            paApplication.POApplicationCode = grApplication.PoApplicationCode;
            paApplication.CompanyCode = grApplication.CompanyCode;
            paApplication.CompanyId = grApplication.CompanyId;
            paApplication.CompanyName = grApplication.CompanyName;
            paApplication.Currency = grApplication.Currency;
            paApplication.ExchangeRate = grApplication.ExchangeRate;
            paApplication.PlanRate = grApplication.PlanRate;
            paApplication.ExpectedFloatRate = grApplication.ExpectedFloatRate;
            paApplication.CurrencySymbol = grApplication.CurrencySymbol;
            paApplication.TransfereeId = grApplication.TransfereeId;
            paApplication.TransfereeName = grApplication.TransfereeName;
            paApplication.UrgentPayment = false;
            paApplication.AdvancePayment = grDetailHistorys?.FirstOrDefault().IsAdvancePayment ?? false;
            paApplication.IsLastPayment = false;
            paApplication.ReceivingHeader = grApplication.VendorName;
            paApplication.IsBackupInvoice = false;
            paApplication.PayTotalAmount = grDetailHistorys.Sum(a => a.ReceivedAmount ?? 0M);
            paApplication.ExpenseType = PAExpenseType.Other;
            paApplication.TaxStampLegal = true;//默认合法
            if (grApplication.PoId.HasValue)
            {
                poApplication = queryPo.Where(a => a.Id == grApplication.PoId).FirstOrDefault();
                paApplication.PayMethod = PayMethods.AP;//PO带入的付款方式都是AP
                paApplication.PaymentTerms = poApplication.PaymentTerm;
            }
            else
            {
                //4024 [付款申请]"帐期"期望在初审前可默认填入  20250306 ytw
                var bpcsAvm = queryBpcsAvm.Where(a => a.Id == paApplication.VendorId).FirstOrDefault();
                paApplication.PaymentTerms = bpcsAvm.Vterms;
            }

            //供应商入库时间
            if (paApplication.VendorId.HasValue)
            {
                var store = queryBpcsAvm.Where(a => a.Id == paApplication.VendorId.Value)
                    .Join(bpcsPmfvm,
                    avm => new { vendor = avm.Vendor, Vcmpny = avm.Vcmpny },
                    pmfvm => new { vendor = pmfvm.Vnderx, Vcmpny = pmfvm.Vmcmpy },
                    (avm, pmfvm) => new
                    {
                        pmfvm.Vcrdte,
                        pmfvm.Vctime
                    })
                    .FirstOrDefault();
                string dateTimeString = $"{store.Vcrdte}{store.Vctime?.ToString().PadLeft(6, '0')}";
                DateTime dateTime;
                DateTime.TryParseExact(dateTimeString, "yyyyMMddHHmmss", CultureInfo.InvariantCulture, DateTimeStyles.None, out dateTime);
                paApplication.SupplierWarehousingTime = dateTime;
            }
            if (!string.IsNullOrWhiteSpace(grApplication.VendorCode))
            {
                var travelagencys = await dataverseService.GetTravelagencyAsync();
                //常规讲者
                var vendor = queryBpcsAvm.Where(s => s.Id == grApplication.VendorId)
                    .Join(queryFinancial, a => a.FinaId, a => a.Id, (a, b) => new { Avm = a, Fin = b })
                    .Join(queryVendor.Where(a => a.VendorType == VendorTypes.HCPPerson), a => a.Fin.VendorId, a => a.Id, (a, b) => new { a.Avm, Vendor = b })
                    .FirstOrDefault();
                //讲者
                //1、非外币：bpcsAVM里NHIV类型
                //2、外币：bpcsAVM里是NT，Vendors表对应的VendorType = 1
                if (vendor?.Avm?.Vtype == "NHIV" || (vendor?.Avm?.Vtype == "NT" && vendor?.Vendor?.VendorType == VendorTypes.HCPPerson))
                    paApplication.ExpenseType = PAExpenseType.LectureFee;
                else if (travelagencys.Select(a => a.Name).Contains(grApplication.VendorName))
                    paApplication.ExpenseType = PAExpenseType.TravelAgency;
            }
            var om = queryOnlineMeeting.Where(a => grDetailHistorys.Select(x => x.PRDetailId).Distinct().ToList().Contains(a.PRDetailId)).OrderByDescending(a => a.CreationTime).FirstOrDefault();
            if (paApplication.PayMethod == PayMethods.AR && om != null)
            {
                paApplication.DeliveryMode = DeliveryModes.OnLine;//AR 递交方式有OM则默认线上递交
                paApplication.IsLastPayment = true;//CR#51-5315对于在线会议结算行相关PA，请生成付款申请时默认【最后一次付款】=【是】，无需用户再手动切换，最后一次付款本身的逻辑不受影响
            }
                
            var paInvoices = new List<PurPAApplicationDetail>();

            await InsertAndGenerateSerialNoAsync(paApplicationRepository, paApplication, "A");

            var prDetailIds = grDetailHistorys.Select(r => r.PODetailId).ToList();
            var poDetail = queryPoDetail.Where(a => prDetailIds.Contains(a.Id)).ToList();
            foreach (var item in grDetailHistorys.Where(a => a.ReceivedAmount > 0).ToList())//创建发票子表
            {
                var paInvoice = new PurPAApplicationDetail();
                paInvoice.PRId = grApplication.PrId;
                paInvoice.POId = grApplication.PoId;
                paInvoice.PurPAApplicationId = paApplication.Id;
                paInvoice.PRDetailId = item.PRDetailId;
                paInvoice.PODetailId = item.PODetailId;
                paInvoice.GRApplicationDetailId = item.GRApplicationDetailId;
                paInvoice.GRHistoryId = item.Id;
                paInvoice.ProductName = item.ProductName;
                if (PayMethods.AP.Equals(grApplication.PayMethod))
                {
                    paInvoice.InvoiceType = poDetail?.Where(a => a.Id == item.PODetailId)?.FirstOrDefault()?.InvoiceType ?? InvoiceType.OrdinaryInvoice;
                    paInvoice.TaxRate = poDetail?.Where(a => a.Id == item.PODetailId)?.FirstOrDefault()?.TaxRate ?? "0";
                }
                paInvoice.PaymentAmount = item.ReceivedAmount ?? 0M;
                if (PayMethods.AR.Equals(grApplication.PayMethod))
                {
                    paInvoice.InvoiceType = InvoiceType.OrdinaryInvoice;//普通发票
                    paInvoice.TaxRate = "0";//税率默认为
                    if (om != null)
                        paInvoice.PaymentAmount = om.PayAmount;
                }
                if (paApplication.ExpenseType == PAExpenseType.LectureFee)//费用类型为‘讲课费’时发票类型全部默认为无发票
                    paInvoice.InvoiceType = InvoiceType.NonInvoice;
                paInvoices.Add(paInvoice);
            }
            var prDetails = queryPrDetail.Where(a => paInvoices.Select(x => x.PRDetailId).ToList().Contains(a.Id)).ToList();
            if (prDetails.Any() && prDetails.All(a => a.CityId == prDetails.First().CityId))
            {
                paApplication.CityId = prDetails.First().CityId;
            }
            await paDetailRepository.InsertManyAsync(paInvoices, true);
            #region 操作 GR 收货历史 相关
            grDetailHistorys.ForEach(a => { a.PurPAApplicationId = paApplication.Id; });
            await prDetailHistoryRepository.UpdateManyAsync(grDetailHistorys, true);
            #endregion
            return paApplication.Id;
        }

        #region 付款列表
        /// <summary>
        ///  付款列表
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<PurPAApplicationListDto>> GetPAApplicationListAsync(PAListSearchRequestDto requestDto)
        {
            var result = new PagedResultDto<PurPAApplicationListDto>();
            var queryPa = await LazyServiceProvider.LazyGetService<IPurPAApplicationReadonlyRepository>().GetQueryableAsync();
            var queryPr = await LazyServiceProvider.LazyGetService<IPurPRApplicationReadonlyRepository>().GetQueryableAsync();
            var queryBpcsGlh = await LazyServiceProvider.LazyGetService<IBpcsGlhReadonlyRepository>().GetQueryableAsync();
            var queryableFinanceCashierPaymentInfo = (await LazyServiceProvider.LazyGetService<IFinanceCashierPaymentInfoReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
            var queryRef = (await LazyServiceProvider.GetService<IPurPAApplicationRefReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
            var queryPaLinq = queryPa.AsNoTracking().Select(a => new
            {
                a.Id,
                a.PRId,
                a.Status,
                a.ApplyTime,
                a.ApplyUserId,
                a.ApplyUserName,
                a.ApplyUserBu,
                a.ApplyUserBuToDept,
                a.ApplyUserBuToDeptName,
                a.PaymentType,
                a.VendorName,
                a.ApplicationCode,
                a.GRApplicationCode,
                a.POApplicationCode,
                a.CreationTime,
                a.TransfereeId,
                a.PayTotalAmount,
                a.EstimatedPaymentDate,
                a.IsBackupInvoice,
                a.POId,
                a.GRId,
            })
            .GroupJoin(queryPr.AsNoTracking().Select(a => new { a.Id, a.SubBudgetId, a.ApplicationCode }), a => a.PRId, b => b.Id, (a, b) => new { pa = a, prs = b })
            .SelectMany(a => a.prs.DefaultIfEmpty(), (a, b) => new { a.pa, SubBudgetId = b.SubBudgetId, PrApplicationCode = b.ApplicationCode })
            .WhereIf(requestDto.Status.HasValue, a => a.pa.Status.Equals(requestDto.Status))
            .WhereIf(requestDto.StartApplyTime.HasValue, a => a.pa.ApplyTime >= requestDto.StartApplyTime.Value.Date)
            .WhereIf(requestDto.EndApplyTime.HasValue, a => a.pa.ApplyTime <= requestDto.EndApplyTime.Value.Date.AddDays(1))
            .WhereIf(!string.IsNullOrWhiteSpace(requestDto.ApplyUserName), a => a.pa.ApplyUserName.Contains(requestDto.ApplyUserName))
            .WhereIf(requestDto.ApplyUserId != null, a => a.pa.ApplyUserId == requestDto.ApplyUserId)
            .WhereIf(!string.IsNullOrWhiteSpace(requestDto.ApplyUserBuName), a => a.pa.ApplyUserBuToDeptName.Contains(requestDto.ApplyUserBuName))
            .WhereIf(requestDto.PaymentType.HasValue, a => a.pa.PaymentType.Equals(requestDto.PaymentType))
            .WhereIf(!string.IsNullOrWhiteSpace(requestDto.VendorName), a => a.pa.VendorName.Contains(requestDto.VendorName))
            .WhereIf(!string.IsNullOrWhiteSpace(requestDto.ApplicationCode), a => a.pa.ApplicationCode.Contains(requestDto.ApplicationCode))
            .WhereIf(!string.IsNullOrWhiteSpace(requestDto.GRApplicationCode), a => a.pa.GRApplicationCode.Contains(requestDto.GRApplicationCode))
            .WhereIf(!string.IsNullOrWhiteSpace(requestDto.PrApplicationCode), a => a.PrApplicationCode.Contains(requestDto.PrApplicationCode))
            .WhereIf(!string.IsNullOrWhiteSpace(requestDto.PoApplicationCode), a => a.pa.POApplicationCode.Contains(requestDto.PoApplicationCode))
            .Select(a => new
            {
                a.pa.Id,
                a.pa.PRId,
                a.pa.Status,
                a.pa.ApplyTime,
                a.pa.ApplyUserId,
                a.pa.ApplyUserName,
                a.pa.ApplyUserBu,
                a.pa.ApplyUserBuToDept,
                a.pa.ApplyUserBuToDeptName,
                a.pa.PaymentType,
                a.pa.VendorName,
                a.pa.ApplicationCode,
                a.pa.GRApplicationCode,
                a.pa.POApplicationCode,
                a.pa.CreationTime,
                a.pa.TransfereeId,
                a.pa.PayTotalAmount,
                a.pa.EstimatedPaymentDate,
                a.pa.IsBackupInvoice,
                a.SubBudgetId,
                a.PrApplicationCode,
                a.pa.POId,
                a.pa.GRId,
            });

            var personCenterService = LazyServiceProvider.LazyGetService<IPersonCenterService>();
            var rolesIds = await personCenterService.GetIdsByRoleLevels(bizTypeCategory: ResignationTransfer.TaskFormCategory.PaymentApplication);
            queryPaLinq = queryPaLinq.ApplyRoleFilters(rolesIds, CurrentUser.Id.Value, x => x.ApplyUserBu, x => x.ApplyUserBuToDept, x => x.SubBudgetId, x => x.ApplyUserId, x => x.TransfereeId);

            queryPaLinq = queryPaLinq.OrderByDescending(a => a.CreationTime);

            result.TotalCount = queryPaLinq.Count();
            var queryData = queryPaLinq.Skip(requestDto.PageIndex * requestDto.PageSize).Take(requestDto.PageSize).Select(a => new PurPAApplicationListDto
            {
                Id = a.Id,
                PRId = a.PRId,
                Status = a.Status,
                ApplyTime = a.ApplyTime,
                ApplyUserId = a.ApplyUserId,
                ApplyUserName = a.ApplyUserName,
                ApplyUserBu = a.ApplyUserBu,
                ApplyUserBuToDept = a.ApplyUserBuToDept,
                ApplyUserBuToDeptName = a.ApplyUserBuToDeptName,
                PaymentType = a.PaymentType,
                VendorName = a.VendorName,
                ApplicationCode = a.ApplicationCode,
                GRApplicationCode = a.GRApplicationCode,
                POApplicationCode = a.POApplicationCode,
                CreationTime = a.CreationTime,
                TransfereeId = a.TransfereeId,
                PayTotalAmount = a.PayTotalAmount,
                EstimatedPaymentTime = a.EstimatedPaymentDate.HasValue ? a.EstimatedPaymentDate.Value.ToString("yyyy-MM-dd") : null,
                IsBackupInvoice = a.IsBackupInvoice,
                PrSubBudgetId = a.SubBudgetId,
                PrApplicationCode = a.PrApplicationCode,
                GRId = a.GRId,
                POId = a.POId,
            }).ToList();


            #region 支付时间 处理
            var paApplicationCodes = queryData.Select(a => a.ApplicationCode).ToList();
            var cashierPayments = queryableFinanceCashierPaymentInfo.Where(a => paApplicationCodes.Contains(a.PAApplicationCode)).ToList();
            var refs = queryRef.Where(a => paApplicationCodes.Contains(a.PAApplicationCode)).ToList();
            queryData.ForEach(a =>
            {
                a.OnlinePaymentTime = cashierPayments.Where(x => x.PAApplicationCode == a.ApplicationCode).OrderByDescending(x => x.CreationTime).FirstOrDefault()?.PaymentDate?.ToString("yyyy-MM-dd");
                //a.PrApplicationCode = queryData.FirstOrDefault(a1 => a1.Id == a.Id)?.PrApplicationCode;
                var nos = refs.Where(x => x.PAApplicationCode == a.ApplicationCode).Select(x => x.RefNo).ToList();
                a.RefNo = string.Join(",", nos);
            });
            #endregion
            result.Items = queryData;
            return result;
        }

        /// <summary>
        ///  付款列表-导出
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        public async Task<List<ExportPAApplicationDto>> ExportPAListAsync(PAListSearchRequestDto requestDto)
        {
            if (!requestDto.StartApplyTime.HasValue || !requestDto.EndApplyTime.HasValue)
            {
                requestDto.StartApplyTime = DateTime.Now.Date.AddYears(-1);
                requestDto.EndApplyTime = DateTime.Now.Date;
            }
            var dateTime = new KeyValuePair<DateTime, DateTime>(requestDto.StartApplyTime.Value.Date, requestDto.EndApplyTime.Value.Date);
            //var dateTimes = DatetimeHelper.GetQueryTimeList(requestDto.StartApplyTime.Value.Date, requestDto.EndApplyTime.Value.Date, 20);

            var queryPa = await LazyServiceProvider.LazyGetService<IPurPAApplicationReadonlyRepository>().GetQueryableAsync();
            int itemsPerPage = 2000;
            int totalCount = queryPa.Where(a => a.ApplyTime > dateTime.Key && a.ApplyTime <= dateTime.Value.AddDays(1)).Count();
            var totalPages = CalculateTotalPages(totalCount, itemsPerPage);

            var allResults = new ConcurrentBag<ExportPAApplicationDto>(); // 使用 ConcurrentBag 避免锁竞争
            var errorTimes = new ConcurrentBag<KeyValuePair<DateTime, DateTime>>();
            var tasks = new List<Task>();

            var personCenterService = LazyServiceProvider.LazyGetService<IPersonCenterService>();
            var rolesIds = await personCenterService.GetIdsByRoleLevels(bizTypeCategory: ResignationTransfer.TaskFormCategory.PaymentApplication);

            using (var semaphoreSlim = new SemaphoreSlim(20))//可同时执行20个线程
            {

                for (int i = 0; i < totalPages; i++)
                {
                    int pageIndex = i; // 防止闭包捕获循环变量的问题
                    tasks.Add(GetExportPADataAsync(pageIndex, itemsPerPage, requestDto, dateTime, semaphoreSlim, allResults, errorTimes, rolesIds));
                }
                //foreach (var item in dateTimes)
                //{
                //    tasks.Add(GetExportPADataAsync(requestDto,item, semaphoreSlim, allResults, errorTimes));
                //}
                await Task.WhenAll(tasks);
                //后续可优化，错误的再次查询  errorTimes
            }

            var result = allResults.OrderByDescending(a => a.ApplyTime).ThenBy(a => a.ApplicationCode).ToList();
            return result;
        }
        private async Task GetExportPADataAsync(int pageIndex, int itemsPerPage, PAListSearchRequestDto requestDto, KeyValuePair<DateTime, DateTime> dateTime, SemaphoreSlim semaphore,
            ConcurrentBag<ExportPAApplicationDto> resultList, ConcurrentBag<KeyValuePair<DateTime, DateTime>> errorTimes, Dictionary<RoleLevel, IEnumerable<Guid>> rolesIds)
        {
            await semaphore.WaitAsync();
            try
            {
                using (var scope = _serviceProvider.CreateScope())
                {
                    var paRepository = scope.ServiceProvider.GetRequiredService<IPurPAApplicationReadonlyRepository>();
                    var paQuery = await paRepository.GetQueryableAsync();
                    //var padRepository = await scope.ServiceProvider.GetRequiredService<IPurPAApplicationDetailRepository>().GetQueryableAsync();
                    var prRepository = scope.ServiceProvider.GetRequiredService<IPurPRApplicationReadonlyRepository>();
                    var prQuery = await prRepository.GetQueryableAsync();
                    var wfRepository = scope.ServiceProvider.GetRequiredService<IWfApprovalTaskReadonlyRepository>();
                    var wfQuery = await wfRepository.GetQueryableAsync();
                    //var queryBpcsAml = await scope.ServiceProvider.GetRequiredService<IBpcsAmlRepository>().GetQueryableAsync();
                    var queryBpcsGlh = await scope.ServiceProvider.GetRequiredService<IBpcsGlhReadonlyRepository>().GetQueryableAsync();
                    var queryableFinanceCashierPaymentInfo = await scope.ServiceProvider.GetRequiredService<IFinanceCashierPaymentInfoReadonlyRepository>().GetQueryableAsync();

                    var queryPaLinq = paQuery.Where(a => a.ApplyTime > dateTime.Key && a.ApplyTime <= dateTime.Value.AddDays(1))
                        .GroupJoin(prQuery.Select(a => new { a.Id, a.SubBudgetId, a.ApplicationCode, a.CostCenterName }), a => a.PRId, b => b.Id, (a, b) => new { pa = a, prs = b })
                        .SelectMany(a => a.prs.DefaultIfEmpty(), (a, b) => new
                        {
                            a.pa.Id,
                            a.pa.ApplyUserName,
                            a.pa.ApplyUserBuName,
                            a.pa.ApplyUserBuToDeptName,
                            a.pa.PaymentType,
                            a.pa.VendorName,
                            a.pa.ApplicationCode,
                            a.pa.GRApplicationCode,
                            a.pa.ApplyUserBu,
                            a.pa.ApplyUserBuToDept,
                            a.pa.TransfereeId,
                            a.pa.Status,
                            a.pa.ApplyUserId,
                            a.pa.PRId,
                            a.pa.ApplyTime,
                            a.pa.PayTotalAmount,
                            a.pa.ManagerApprovalTime,
                            a.pa.AcceptedTime,
                            a.pa.ApprovedDate,
                            a.pa.TaskType,
                            a.pa.CompanyName,
                            a.pa.POApplicationCode,
                            a.pa.AccepterName,
                            b.SubBudgetId,
                            prApplicationCode = b.ApplicationCode,
                            b.CostCenterName,
                        })
                        .WhereIf(requestDto.Status.HasValue, a => a.Status.Equals(requestDto.Status))
                        .WhereIf(!string.IsNullOrWhiteSpace(requestDto.ApplyUserName), a => a.ApplyUserName.Contains(requestDto.ApplyUserName))
                        .WhereIf(!string.IsNullOrWhiteSpace(requestDto.ApplyUserBuName), a => a.ApplyUserBuToDeptName.Contains(requestDto.ApplyUserBuName))
                        .WhereIf(requestDto.PaymentType.HasValue, a => a.PaymentType.Equals(requestDto.PaymentType))
                        .WhereIf(!string.IsNullOrWhiteSpace(requestDto.VendorName), a => a.VendorName.Contains(requestDto.VendorName))
                        .WhereIf(!string.IsNullOrWhiteSpace(requestDto.ApplicationCode), a => a.ApplicationCode.Contains(requestDto.ApplicationCode))
                        .WhereIf(!string.IsNullOrWhiteSpace(requestDto.GRApplicationCode), a => a.GRApplicationCode.Contains(requestDto.GRApplicationCode));

                    queryPaLinq = queryPaLinq.ApplyRoleFilters(rolesIds, CurrentUser.Id.Value, x => x.ApplyUserBu, x => x.ApplyUserBuToDept, x => x.SubBudgetId, x => x.ApplyUserId, x => x.TransfereeId);

                    queryPaLinq = queryPaLinq.Skip(pageIndex * itemsPerPage)
                        .Take(itemsPerPage);

                    var paDatas = queryPaLinq.ToList();

                    PurPAApplicationStatus[] paStatus = [PurPAApplicationStatus.Approvaling, PurPAApplicationStatus.FinancialPreliminaryReview, PurPAApplicationStatus.FinancialReview, PurPAApplicationStatus.BudgetManagerApproval];
                    var paApplicationCodes = new HashSet<string>();
                    var oriCodes = new HashSet<string>();
                    var traCodes = new HashSet<string>();
                    var paIds = new HashSet<Guid>();
                    var poApprovingIds = new HashSet<Guid>();
                    var prIds = new HashSet<Guid>();
                    foreach (var data in paDatas)
                    {
                        paIds.Add(data.Id);
                        prIds.Add(data.PRId);
                        paApplicationCodes.Add(data.ApplicationCode);
                        if (paStatus.Contains(data.Status))
                            poApprovingIds.Add(data.Id);

                        var applicationCode = data.ApplicationCode.Substring(1);
                        var oriCode = applicationCode;
                        var traCode = string.Concat(applicationCode.Substring(1), applicationCode.Substring(0, 1));
                        oriCodes.Add(oriCode);
                        traCodes.Add(traCode);
                    }

                    //var prs = prQuery.Where(a => prIds.Contains(a.Id)).ToList();

                    //var pads = padRepository.Where(a => paIds.Contains(a.PurPAApplicationId)).ToList();

                    //当前审批人
                    var wfTask = wfQuery.Where(a => poApprovingIds.Contains(a.FormId)).GroupBy(a => a.FormId).Select(a => a.OrderByDescending(x => x.CreationTime).FirstOrDefault()).ToList();

                    //MPDates
                    //OriCode = s.pa.ApplicationCode.Substring(1),
                    //TraCode = string.Concat(s.pa.ApplicationCode.Substring(1).Substring(1), s.pa.ApplicationCode.Substring(1).Substring(0, 1)),
                    var allGlhs = queryBpcsGlh
                        .Where(a => oriCodes.Contains(a.Lhdref) || traCodes.Contains(a.Lhdref))
                        .Where(a => a.Lhreas == "APMPL" || a.Lhreas == "APPYL")
                        .ToList();

                    //财务出纳支付数据
                    var paymentInfos = queryableFinanceCashierPaymentInfo.Where(a => paApplicationCodes.Contains(a.PAApplicationCode)).ToList();

                    var resultData = paDatas.Select(a =>
                    {
                        var oriCode = a.ApplicationCode.Substring(1);
                        var traCode = string.Concat(a.ApplicationCode.Substring(1).Substring(1), a.ApplicationCode.Substring(1).Substring(0, 1));
                        var glhs = allGlhs.Where(x => x.Lhdref == oriCode || x.Lhdref == traCode).ToList();

                        //var pr = prs.Where(x => x.Id == a.PRId).FirstOrDefault();
                        var paymentInfo = paymentInfos.Where(x => x.PAApplicationCode == a.ApplicationCode).OrderByDescending(x => x.CreationTime).FirstOrDefault();
                        return new ExportPAApplicationDto
                        {
                            ApplicationCode = a.ApplicationCode,
                            ApplyDate = a.ApplyTime.ToString("yyyy-MM-dd"),
                            ApplyTime = a.ApplyTime,
                            ApplyUserName = a.ApplyUserName,
                            TotalAmount = a.PayTotalAmount,
                            ManagerApprovalDate = a.ManagerApprovalTime?.ToString("yyyy-MM-dd"),
                            AcceptedDate = a.AcceptedTime?.ToString("yyyy-MM-dd"),
                            ApprovedDate = a.ApprovedDate?.ToString("yyyy-MM-dd"),
                            StatusName = a.Status.GetDescription(),
                            InterceptStatusName = a.TaskType != PAApprovalTaskStatus.Intercepted ? "" : a.TaskType.GetDescription(),
                            CurrentApprover = GetCurrentOperator(wfTask, a.Id, a.Status, a.ApplyUserName, a.AccepterName),
                            //PRApplicationCode = pr?.ApplicationCode,
                            PRApplicationCode = a.prApplicationCode,
                            CompanyName = a.CompanyName,
                            ApplyUserBuName = a.ApplyUserBuName,
                            POApplicationCode = a.POApplicationCode,
                            //CostCenterName = pr?.CostCenterName,
                            CostCenterName = a.CostCenterName,
                            //MPDate = GetMPDate(glhs),
                            VendorName = a.VendorName,
                            BankDate = paymentInfo?.PaymentDate?.ToString("yyyy-MM-dd"),
                        };
                    });

                    foreach (var item in resultData)
                    {
                        resultList.Add(item);
                    }
                }
            }
            catch (Exception ex)
            {
                errorTimes.Add(dateTime);
            }
            finally
            {
                semaphore.Release();
            }
        }

        public static int CalculateTotalPages(int totalItems, int itemsPerPage)
        {
            if (itemsPerPage <= 0) throw new ArgumentException("Items per page must be greater than zero.", nameof(itemsPerPage));

            // 使用 Math.Ceiling 来确保如果有剩余部分也会被计为一页
            return (int)Math.Ceiling((double)totalItems / itemsPerPage);
        }

        /// <summary>
        /// 获取bpcs 付款日期
        /// </summary>
        /// <param name="glhs"></param>
        /// <returns></returns>
        private string GetMPDate(List<BpcsGlh> glhs)
        {
            var paidGlh = glhs.Select(a =>
            {
                string dateTimeString = $"{a.Lhdate}{a.Lhtime}";
                DateTime dateTime;
                bool isParsed = DateTime.TryParseExact(dateTimeString, "yyyyMMddHHmmss", CultureInfo.InvariantCulture, DateTimeStyles.None, out dateTime);
                return new { a, paidTime = dateTime };
            }).OrderByDescending(a => a.paidTime).FirstOrDefault();
            return paidGlh?.paidTime.ToString("yyyy-MM-dd");
        }

        /// <summary>
        /// 获取当前操作人
        /// </summary>
        /// <param name="wfs"></param>
        /// <param name="paId"></param>
        /// <param name="paStatus"></param>
        /// <param name="applyUserName"></param>
        /// <param name="accepterName"></param>
        /// <returns></returns>
        private string GetCurrentOperator(List<WfApprovalTask> wfs, Guid paId, PurPAApplicationStatus paStatus, string applyUserName, string accepterName)
        {
            string approver = "";
            try
            {
                //若状态为审批中 / 财务初审 / 财务复审则填入当前的workflow审批人，若状态为填写表单 / 重发起则为PA的申请人
                switch (paStatus)
                {
                    case PurPAApplicationStatus.FillIn:
                    case PurPAApplicationStatus.Reissue:
                        approver = applyUserName;
                        break;
                    case PurPAApplicationStatus.Approvaling:
                    case PurPAApplicationStatus.FinancialPreliminaryReview:
                    case PurPAApplicationStatus.FinancialReview:
                        var wf = wfs.Where(a => a.FormId == paId).FirstOrDefault();
                        if (wf != null)
                        {
                            var approvers = JsonSerializer.Deserialize<List<ApproverDto>>(wf.Approver);
                            approver = string.Join(",", approvers.Select(a => a.Name).ToList());
                        }
                        break;
                    case PurPAApplicationStatus.WaitingForPayment:
                    case PurPAApplicationStatus.PaymentProgress:
                    case PurPAApplicationStatus.Paid:
                    case PurPAApplicationStatus.PaymenFailed:
                    case PurPAApplicationStatus.Void:
                        break;
                    case PurPAApplicationStatus.DocumentReceipt:
                        approver = accepterName;
                        break;
                }
                return approver;
            }
            catch (Exception ex)
            {
                return approver;
            }
        }

        /// <summary>
        /// PA获取Bpcs对应数据
        /// </summary>
        /// <param name="pAApplications"></param>
        /// <param name="bpcsAmls"></param>
        /// <param name="bpcsGlhs"></param>
        private static IQueryable<MPDto> GetBpcsInfo(IQueryable<PurPAApplication> pAApplications, IQueryable<BpcsAml> bpcsAmls, IQueryable<BpcsGlh> bpcsGlhs)
        {
            var bpcsDatas = bpcsGlhs.Where(a => a.Lhreas == "APIIL" || a.Lhreas == "APV2L").OrderByDescending(s => s.UpdateTime)
            .GroupBy(g => g.Lhdref, (a, b) => new
            {
                key = a,//PA code
                //b.Last().Lhdref,//
                b.First().Lhjnen,
            })
            .GroupJoin(bpcsAmls.Select(a => new { a.Amlinv, a.Amlpda }), a => a.Lhjnen, b => b.Amlinv, (a, b) => new { aml = a, glhs = b })
            .SelectMany(s => s.glhs.DefaultIfEmpty(), (a, b) => new { a.aml.key, a.aml.Lhjnen, b.Amlpda });

            var Mp1 = pAApplications.Select(s => new { s.Id, code = s.ApplicationCode.Substring(1) })
          .GroupJoin(bpcsDatas, a => a.code, b => b.key, (a, b) => new { PA = a, bpcs = b })
          .SelectMany(s => s.bpcs.DefaultIfEmpty(), (a, b) => new { a.PA.Id, a.PA.code, b.Lhjnen, b.Amlpda });
            var Mp = Mp1.GroupJoin(bpcsDatas, a => string.Concat(a.code.Substring(1), a.code.Substring(0, 1)), b => b.key, (a, b) => new { PA = a, bpcsV2 = b })
            .SelectMany(s => s.bpcsV2.DefaultIfEmpty(), (a, b) => new MPDto
            {
                Id = a.PA.Id,
                Lhjnen = b.Lhjnen != null ? b.Lhjnen : a.PA.Lhjnen,
                Amlpda = b.Amlpda != null ? b.Amlpda : a.PA.Amlpda,
            });
            return Mp;
        }
        #endregion

        /// <summary>
        /// 获取PA详情
        /// </summary>
        /// <param name="paId"></param>
        /// <returns></returns>
        public async Task<MessageResult> GetPAApplicationDetailsAsync(Guid paId)
        {
            var result = new PADetailsResponseDto();
            var queryPr = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var queryPrDetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var queryGrDetailsHistory = await LazyServiceProvider.LazyGetService<IPurGRApplicationDetailHistoryRepository>().GetQueryableAsync();
            var queryPa = await LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>().GetQueryableAsync();
            var queryPaDetail = await LazyServiceProvider.LazyGetService<IPurPAApplicationDetailRepository>().GetQueryableAsync();
            var queryPaInvoice = await LazyServiceProvider.LazyGetService<IPurPAApplicationInvoiceRepository>().GetQueryableAsync();
            var queryAttachment = await LazyServiceProvider.LazyGetService<IAttachmentRepository>().GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var queryOnlineMeeting = await LazyServiceProvider.LazyGetService<IInteOnlineMeetingRepository>().GetQueryableAsync();
            var queryPrProductApportionment = await LazyServiceProvider.LazyGetService<IPurPRApplicationProductApportionmentRepository>().GetQueryableAsync();
            var paApplication = queryPa.Where(a => a.Id == paId).FirstOrDefault();
            if (paApplication == null)
                return MessageResult.SuccessResult("未查询到该付款申请数据");
            result.PAApplication = ObjectMapper.Map<PurPAApplication, PurPAApplicationDto>(paApplication);

            //产品拆分
            var prProductApportionments = queryPrProductApportionment.AsNoTracking().Where(a => a.PRApplicationId == paApplication.PRId).ToArray();

            if (paApplication.CityId.HasValue)
            {
                var specialCities = (await dataverseService.GetSpecialCitiesAsync(paApplication.CityId.ToString(), null)).FirstOrDefault();
                result.PAApplication.CityId = specialCities.Id;
                result.PAApplication.CityName = specialCities?.Name ?? "";//城市名称
                result.PAApplication.CityCode = specialCities.CityCode;
            }
            var paDetails = queryPaDetail.Where(a => a.PurPAApplicationId == paApplication.Id).ToList();
            result.PAApplicationDetails = ObjectMapper.Map<List<PurPAApplicationDetail>, List<PurPAApplicationDetailResponseDto>>(paDetails);
            var paInvoices = queryPaInvoice.Where(a => a.PurPAApplicationId == paApplication.Id).ToList();
            result.PAApplicationInvoices = ObjectMapper.Map<List<PurPAApplicationInvoice>, List<PurPAApplicationInvoiceResponseDto>>(paInvoices);
            #region 电子签章会议系统PDF
            //OM  根据付款方式来 递交方式为线上递交（可以修改）
            if (PayMethods.AR == paApplication.PayMethod)
            {
                var omAuthorizationService = LazyServiceProvider.LazyGetService<IOmAuthorizationService>();
                var om = queryOnlineMeeting.Where(a => paDetails.Select(x => x.PRDetailId).Distinct().ToList().Contains(a.PRDetailId)).OrderByDescending(a => a.CreationTime).FirstOrDefault();
                if (om != null)
                {
                    result.IsOnlineMeeting = true;
                    if (paApplication.Status == PurPAApplicationStatus.FillIn || paApplication.Status == PurPAApplicationStatus.FillIn)
                        result.PAApplicationDetails.ForEach(a => { a.PaymentAmount = om.PayAmount; });
                }
                result.PAApplication.MeetingModifyRemark = om?.ModifyRemark;
                result.PAApplication.AmountModifyRemark = om?.ModifyAmountRemark;
                if (om != null && !string.IsNullOrWhiteSpace(om.PdfUrl))
                {
                    var omHeaders = omAuthorizationService.BuildAuthorizationHeaders();
                    result.PAApplication.EsignPdf = om.PdfUrl + "?" + string.Join("&", omHeaders.Select(kv => $"{kv.Key}={kv.Value}"));
                }
            }
            #endregion

            var grDetailsHistorys = queryGrDetailsHistory.AsNoTracking().IgnoreQueryFilters().Where(a => a.PurPAApplicationId == paApplication.Id && a.ReceivedAmount > 0).ToList();//本次付款对于的历史收货物历史
            #region 收货信息
            result.GRApplicationDetails = ObjectMapper.Map<List<PurGRApplicationDetailHistory>, List<PurGRApplicationDetailHistoryResponseDto>>(grDetailsHistorys);
            #endregion
            #region PR分摊信息
            var prDetailsIds = paDetails.Select(a => a.PRDetailId).Distinct().ToList();
            var prHistorys = queryGrDetailsHistory.AsNoTracking().Where(a => prDetailsIds.Contains(a.PRDetailId)).ToList();//本次付款PR所有已收货历史
            //var prDetails = queryPrDetail.Where(a => prDetailsIds.Contains(a.Id)).ToList();
            //var prId = prDetails?.FirstOrDefault()?.PRApplicationId;
            //var pr = queryPr.Where(a => a.Id == prId).FirstOrDefault();

            var prRelateds = queryPrDetail.AsNoTracking().Where(a => prDetailsIds.Contains(a.Id))
                .Join(queryPr.AsNoTracking().Select(a => new { a.Id, a.ApplicationCode, a.ApplyUserBuName, a.ExpenseTypeCode }), a => a.PRApplicationId, b => b.Id, (a, b) => new { PrDetail = a, Pr = b });

            //var productsAsync = await dataverseService.GetProductsAsync();
            var prApportions = new List<PRApportionDetailsDto>();
            foreach (var prRelated in prRelateds)
            {
                var pr = prRelated.Pr;
                var item = prRelated.PrDetail;

                var prApportion = new PRApportionDetailsDto();
                prApportion.PRId = pr.Id;
                prApportion.PRDetailId = item.Id;
                prApportion.PRApplicationCode = pr.ApplicationCode;
                prApportion.CostNatureName = item.CostNatureName;
                prApportion.CostCenterName = item.CostCenterName;
                prApportion.ApplyUserBuName = pr.ApplyUserBuName;
                //产品拆分
                if (prProductApportionments.Any())
                    prApportion.ProductApportionments = prProductApportionments.Where(a => a.PRApplicationDetailId == item.Id).Select(a => new CreateUpdatePRApplicationProductApportionmentRequest { ProductId = a.ProductId, ProductName = a.ProductName, Ratio = a.Ratio });
                prApportion.ApportionAmount = grDetailsHistorys.Where(a => a.PRDetailId == item.Id).Sum(a => a.AllocationAmount) ?? 0M;
                prApportion.ApportionRMBAmount = grDetailsHistorys.Where(a => a.PRDetailId == item.Id).Sum(a => a.AllocationRMBAmount) ?? 0M;
                prApportion.PurchaseRMBAmount = grDetailsHistorys.Where(a => a.PRDetailId == item.Id).Sum(a => a.PurchaseRMBAmount) ?? 0M;
                prApportion.ReceivedAmount = prHistorys.Where(a => a.PRDetailId == item.Id).Sum(a => a.ReceivedAmount) ?? 0M;
                prApportion.ReceivedRMBAmount = (prHistorys.Where(a => a.PRDetailId == item.Id).Sum(a => a.ReceivedAmount) ?? 0M) * (decimal)paApplication.ExchangeRate;
                prApportion.EstimateDate = item.EstimateDate.HasValue ? item.EstimateDate.Value.ToString("yyyy-MM-dd") : "";
                prApportion.RowNo = item.RowNo;
                prApportion.CityIdName = item.CityIdName;
                prApportion.Content = item.Content;
                prApportion.TotalAmount = item.TotalAmount;
                prApportions.Add(prApportion);
            }
            result.PRApportionDetails = prApportions;
            result.ExpenseTypeCode = prRelateds.FirstOrDefault()?.Pr?.ExpenseTypeCode;
            #endregion

            #region 附件相关
            var attachments = new Dictionary<string, string>()
            {
                { "Attachments", paApplication.Attachments },
                { "Invoice", paApplication.Invoice },
                { "SponsorshipRewardPoints", paApplication.SponsorshipRewardPoints },
                { "EmailAttachment", paApplication.EmailAttachment }
            };
            var attachmentIds = AddFileIdsToList(attachments);
            var fileIds = new List<Guid>();
            foreach (var item in attachmentIds)
            {
                if (item.Value != null && item.Value.Any())
                    fileIds.AddRange(item.Value);
            }
            var files = queryAttachment.Where(a => fileIds.Contains(a.Id)).ToList();

            foreach (var item in attachmentIds)
            {
                if (item.Value == null && !item.Value.Any())
                    continue;
                switch (item.Key)
                {
                    case "Attachments":
                        var attachment_file = files.Where(a => item.Value.Contains(a.Id)).ToList();
                        result.Files = ObjectMapper.Map<List<Entities.Common.Attachment.Attachment>, List<UploadFileResponseDto>>(attachment_file);
                        result.Files.ForEach(a =>
                        {
                            var size = attachment_file.Where(x => x.Id == a.AttachmentId).First().Size;
                            a.FileSize = size < 1048576 ? (size / 1024).ToString() + "KB" : (size / 1048576).ToString() + "MB";
                        });
                        break;
                    case "Invoice":
                        var invoice_file = files.Where(a => item.Value.Contains(a.Id)).ToList();
                        result.InvoiceFiles = ObjectMapper.Map<List<Entities.Common.Attachment.Attachment>, List<UploadFileResponseDto>>(invoice_file);
                        result.InvoiceFiles.ForEach(a =>
                        {
                            var size = invoice_file.Where(x => x.Id == a.AttachmentId).First().Size;
                            a.FileSize = size < 1048576 ? (size / 1024).ToString() + "KB" : (size / 1048576).ToString() + "MB";
                        });
                        break;
                    case "SponsorshipRewardPoints":
                        var sponsorshipRewardPoints_file = files.Where(a => item.Value.Contains(a.Id)).ToList();
                        result.SponsorshipFiles = ObjectMapper.Map<List<Entities.Common.Attachment.Attachment>, List<UploadFileResponseDto>>(sponsorshipRewardPoints_file);
                        result.SponsorshipFiles.ForEach(a =>
                        {
                            var size = sponsorshipRewardPoints_file.Where(x => x.Id == a.AttachmentId).First().Size;
                            a.FileSize = size < 1048576 ? (size / 1024).ToString() + "KB" : (size / 1048576).ToString() + "MB";
                        });
                        break;
                    case "EmailAttachment":
                        var emailAttachment_file = files.Where(a => item.Value.Contains(a.Id)).ToList();
                        result.EmailAttachments = ObjectMapper.Map<List<Entities.Common.Attachment.Attachment>, List<UploadFileResponseDto>>(emailAttachment_file);
                        result.EmailAttachments.ForEach(a =>
                        {
                            var size = emailAttachment_file.Where(x => x.Id == a.AttachmentId).First().Size;
                            a.FileSize = size < 1048576 ? (size / 1024).ToString() + "KB" : (size / 1048576).ToString() + "MB";
                        });
                        break;
                }
            }
            #endregion

            //PurPAApplicationStatus[] paStatus = [PurPAApplicationStatus.FinancialPreliminaryReview, PurPAApplicationStatus.FinancialReview, 
            //    PurPAApplicationStatus.WaitingForPayment, PurPAApplicationStatus.PaymentProgress,PurPAApplicationStatus.Paid,PurPAApplicationStatus.PaymenFailed];
            //if (paStatus.Contains(paApplication.Status))
            //{
            //    var messageResult = await GetPAApplicicationFinancialVoucher(paId);
            //    result.PAFinancialVoucherInfo = messageResult.Data as PAApplicicationFinancialVoucherDto;
            //}
            return MessageResult.SuccessResult(result);
        }

        /// <summary>
        /// 附件ID
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="list"></param>
        private Dictionary<string, List<Guid>> AddFileIdsToList(Dictionary<string, string> filsIds)
        {
            var result = new Dictionary<string, List<Guid>>();
            foreach (var item in filsIds)
            {
                if (!string.IsNullOrWhiteSpace(item.Value))
                {
                    try
                    {
                        var fileIds = item.Value.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                                         .Select(Guid.Parse)
                                         .ToList();
                        result.Add(item.Key, fileIds);
                    }
                    catch (Exception ex)
                    {

                    }
                }
            }
            return result;
        }

        #region 任务中心 付款申请我发起的视角
        /// <summary>
        ///  收货申请我发起的视角 列表
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<PurPAApplicationDto>> GetPAInitiateListAsync(PAListSearchRequestDto requestDto)
        {
            var result = new PagedResultDto<PurPAApplicationDto>();
            //任务中心 状态 
            PurPAApplicationStatus[] pending = [PurPAApplicationStatus.FillIn, PurPAApplicationStatus.Reissue];//待处理
            PurPAApplicationStatus[] progressings = [PurPAApplicationStatus.Approvaling, PurPAApplicationStatus.PaymentProgress, PurPAApplicationStatus.WaitingForPayment, PurPAApplicationStatus.DocumentReceipt, PurPAApplicationStatus.FinancialPreliminaryReview, PurPAApplicationStatus.FinancialReview, PurPAApplicationStatus.BudgetManagerApproval];//进行中
            PurPAApplicationStatus[] completed = [PurPAApplicationStatus.PaymenFailed, PurPAApplicationStatus.Void, PurPAApplicationStatus.Paid];//已完成
            //获取代理信息
            var agents = await LazyServiceProvider.LazyGetService<IAgencyService>().GetOriginalOperators(new GetAgentOperatorsRequestDto { BusinessTypeCategory = ResignationTransfer.TaskFormCategory.PaymentApplication });
            var userIds = agents.Select(a => a.Key).Distinct().ToArray();
            var principalIds = userIds.Where(a => a != CurrentUser.Id.Value);
            var queryPa = await LazyServiceProvider.LazyGetService<IPurPAApplicationReadonlyRepository>().GetQueryableAsync();
            var queryPr = await LazyServiceProvider.LazyGetService<IPurPRApplicationReadonlyRepository>().GetQueryableAsync();

            var queryPaLinq = queryPa.AsNoTracking().Where(a => (a.ApplyUserId == CurrentUser.Id.Value && !a.TransfereeId.HasValue) || CurrentUser.Id.Value == a.TransfereeId.Value || principalIds.ToHashSet().Contains(a.ApplyUserId))
                .GroupJoin(queryPr.AsNoTracking().Select(a => new { a.Id, a.SubBudgetId, a.ApplicationCode, a.ExpenseTypeCode }), a => a.PRId, b => b.Id, (a, b) => new { pa = a, prs = b })
                .SelectMany(a => a.prs.DefaultIfEmpty(), (a, b) => new { a.pa, pr = b })
                .WhereIf(ProcessingStatus.PendingProcessing.Equals(requestDto.ProcessingStatus), a => pending.Contains(a.pa.Status))
                .WhereIf(ProcessingStatus.Progressing.Equals(requestDto.ProcessingStatus), a => progressings.Contains(a.pa.Status))
                .WhereIf(ProcessingStatus.Completed.Equals(requestDto.ProcessingStatus), a => completed.Contains(a.pa.Status))
                .WhereIf(requestDto.Status.HasValue, a => a.pa.Status.Equals(requestDto.Status))
                .WhereIf(requestDto.StartApplyTime.HasValue, a => a.pa.ApplyTime >= requestDto.StartApplyTime.Value.Date)
                .WhereIf(requestDto.EndApplyTime.HasValue, a => a.pa.ApplyTime <= requestDto.EndApplyTime.Value.Date.AddDays(1))
                .WhereIf(!string.IsNullOrWhiteSpace(requestDto.ApplyUserBuName), a => a.pa.ApplyUserBuToDeptName.Contains(requestDto.ApplyUserBuName))
                .WhereIf(requestDto.PaymentType.HasValue, a => a.pa.PaymentType.Equals(requestDto.PaymentType))
                .WhereIf(!string.IsNullOrWhiteSpace(requestDto.VendorName), a => a.pa.VendorName.Contains(requestDto.VendorName))
                .WhereIf(!string.IsNullOrWhiteSpace(requestDto.ApplicationCode), a => a.pa.ApplicationCode.Contains(requestDto.ApplicationCode))
                .WhereIf(!string.IsNullOrWhiteSpace(requestDto.GRApplicationCode), a => a.pa.GRApplicationCode.Contains(requestDto.GRApplicationCode))
                .WhereIf(!string.IsNullOrWhiteSpace(requestDto.PrApplicationCode), a => a.pr.ApplicationCode.Contains(requestDto.PrApplicationCode))
                .WhereIf(!string.IsNullOrWhiteSpace(requestDto.PoApplicationCode), a => a.pa.POApplicationCode.Contains(requestDto.PoApplicationCode))
                .WhereIf(requestDto.AcceptedStartTime.HasValue, a => a.pa.AcceptedTime >= requestDto.AcceptedStartTime.Value.Date)
                .WhereIf(requestDto.AcceptedEndTime.HasValue, a => a.pa.AcceptedTime <= requestDto.AcceptedEndTime.Value.Date.AddDays(1))
                .WhereIf(!string.IsNullOrWhiteSpace(requestDto.AccepterName), a => a.pa.AccepterName.Contains(requestDto.AccepterName))
                .WhereIf(requestDto.DeliveryMode.HasValue, m => m.pa.DeliveryMode == requestDto.DeliveryMode);

            result.TotalCount = queryPaLinq.Count();
            var queryData = queryPaLinq.OrderByDescending(a => a.pa.CreationTime).Skip(requestDto.PageIndex * requestDto.PageSize).Take(requestDto.PageSize).ToList();
            result.Items = ObjectMapper.Map<List<PurPAApplication>, List<PurPAApplicationDto>>(queryData.Select(a => a.pa).ToList());
            foreach (var item in result.Items)
            {
                var data = queryData.FirstOrDefault(a => a.pa.Id == item.Id);

                item.ExpenseTypeCode = data?.pr?.ExpenseTypeCode;
                item.PrApplicationCode = data?.pr?.ApplicationCode;
            }
            return result;
        }
        #endregion

        #region 任务中心 我审批的
        /// <summary>
        /// 任务中心-我审批的付款申请-列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<PurPAApplicationDto>> GetPAApplicationApprovalListAsync(PAListSearchRequestDto request)
        {
            var result = new PagedResultDto<PurPAApplicationDto>();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var queryPA = await LazyServiceProvider.LazyGetService<IPurPAApplicationReadonlyRepository>().GetQueryableAsync();
            var queryPr = await LazyServiceProvider.LazyGetService<IPurPRApplicationReadonlyRepository>().GetQueryableAsync();
            //待处理
            if (request.ProcessingStatus == ProcessingStatus.PendingProcessing)
            {
                var taskRecords = await dataverseService.GetApprovelTaskAsync(CurrentUser.Id.ToString(), [WorkflowTypeName.PaymentRequest, WorkflowTypeName.PaymentFinanceApprove], (ProcessingStatus)request.ProcessingStatus);

                var pas = queryPA.Where(a => taskRecords.Select(b => b.FormId).ToList().Contains(a.Id))
                    .GroupJoin(queryPr.AsNoTracking().Select(a => new { a.Id, a.SubBudgetId, a.ApplicationCode, a.ExpenseTypeCode }), a => a.PRId, b => b.Id, (a, b) => new { pa = a, prs = b })
                    .SelectMany(a => a.prs.DefaultIfEmpty(), (a, b) => new { a.pa, pr = b })
                    .WhereIf(!string.IsNullOrWhiteSpace(request.ApplyUserBuName), a => a.pa.ApplyUserBuName.Contains(request.ApplyUserBuName))
                    .WhereIf(!string.IsNullOrWhiteSpace(request.ApplicationCode), a => a.pa.ApplicationCode.Contains(request.ApplicationCode))
                    .WhereIf(request.Status.HasValue, a => a.pa.Status == request.Status)
                    .WhereIf(request.PaymentType.HasValue, a => a.pa.PaymentType == request.PaymentType)
                    .WhereIf(!string.IsNullOrWhiteSpace(request.VendorName), a => a.pa.VendorName.Contains(request.VendorName))
                    .WhereIf(request.StartApplyTime.HasValue, a => a.pa.ApplyTime >= request.StartApplyTime.Value.Date)
                    .WhereIf(request.EndApplyTime.HasValue, a => a.pa.ApplyTime <= request.EndApplyTime.Value.Date.AddDays(1))
                    .WhereIf(!string.IsNullOrWhiteSpace(request.ApplyUserName), a => a.pa.ApplyUserName == request.ApplyUserName)
                    .WhereIf(!string.IsNullOrWhiteSpace(request.GRApplicationCode), a => a.pa.GRApplicationCode.Contains(request.GRApplicationCode))
                    .WhereIf(!string.IsNullOrWhiteSpace(request.PrApplicationCode), a => a.pr.ApplicationCode.Contains(request.PrApplicationCode))
                    .WhereIf(!string.IsNullOrWhiteSpace(request.PoApplicationCode), a => a.pa.POApplicationCode.Contains(request.PoApplicationCode))
                    .WhereIf(request.AcceptedStartTime.HasValue, a => a.pa.AcceptedTime >= request.AcceptedStartTime.Value.Date)
                    .WhereIf(request.AcceptedEndTime.HasValue, a => a.pa.AcceptedTime <= request.AcceptedEndTime.Value.Date.AddDays(1))
                    .WhereIf(!string.IsNullOrWhiteSpace(request.AccepterName), a => a.pa.AccepterName.Contains(request.AccepterName))
                    .WhereIf(request.ExpenseType.HasValue, m => m.pa.ExpenseType == request.ExpenseType.Value)
                    .WhereIf(request.DeliveryMode.HasValue, m => m.pa.DeliveryMode == request.DeliveryMode)
                    .ToArray();

                result.TotalCount = pas.Length;
                if (request.IsAsc)
                {
                    var datas = pas.Join(taskRecords, a => a.pa.Id, a => a.FormId, (a, b) => new { Pa = a.pa, Pr = a.pr, Task = b })
                    .OrderBy(a => a.Pa.AcceptedTime)
                    .Select(a => new { a.Pa, a.Pr })
                    .Skip(request.PageIndex * request.PageSize).Take(request.PageSize)
                    .ToArray();

                    result.Items = ObjectMapper.Map<IEnumerable<PurPAApplication>, List<PurPAApplicationDto>>(datas.Select(a => a.Pa).ToList());

                    foreach (var item in result.Items)
                    {
                        var data = datas.FirstOrDefault(a => a.Pa.Id == item.Id);

                        item.PrApplicationCode = data?.Pr?.ApplicationCode;
                    }
                }
                else
                {
                    var datas = pas.Join(taskRecords, a => a.pa.Id, a => a.FormId, (a, b) => new { Pa = a.pa, Pr = a.pr, Task = b })
                    .OrderByDescending(a => a.Pa.AcceptedTime)
                    .Select(a => new { a.Pa, a.Pr })
                    .Skip(request.PageIndex * request.PageSize).Take(request.PageSize)
                    .ToArray();

                    result.Items = ObjectMapper.Map<IEnumerable<PurPAApplication>, List<PurPAApplicationDto>>(datas.Select(a => a.Pa).ToList());

                    foreach (var item in result.Items)
                    {
                        var data = datas.FirstOrDefault(a => a.Pa.Id == item.Id);

                        item.PrApplicationCode = data?.Pr?.ApplicationCode;
                    }
                }
            }
            else//已完成
            {
                var queryWfTask = await LazyServiceProvider.LazyGetService<IWorkflowTaskReadonlyRepository>().GetQueryableAsync();
                var queryLinq = queryPA
                    .WhereIf(!string.IsNullOrWhiteSpace(request.ApplyUserBuName), a => a.ApplyUserBuName.Contains(request.ApplyUserBuName))
                    .WhereIf(!string.IsNullOrWhiteSpace(request.ApplicationCode), a => a.ApplicationCode.Contains(request.ApplicationCode))
                    .WhereIf(request.Status.HasValue, a => a.Status == request.Status)
                    .WhereIf(request.PaymentType.HasValue, a => a.PaymentType == request.PaymentType)
                    .WhereIf(!string.IsNullOrWhiteSpace(request.VendorName), a => a.VendorName.Contains(request.VendorName))
                    .WhereIf(request.StartApplyTime.HasValue, a => a.ApplyTime >= request.StartApplyTime.Value.Date)
                    .WhereIf(request.EndApplyTime.HasValue, a => a.ApplyTime <= request.EndApplyTime.Value.Date.AddDays(1))
                    .WhereIf(!string.IsNullOrWhiteSpace(request.ApplyUserName), a => a.ApplyUserName == request.ApplyUserName)
                    .WhereIf(!string.IsNullOrWhiteSpace(request.GRApplicationCode), a => a.GRApplicationCode.Contains(request.GRApplicationCode))
                    .WhereIf(request.AcceptedStartTime.HasValue, a => a.AcceptedTime >= request.AcceptedStartTime.Value.Date)
                    .WhereIf(request.AcceptedEndTime.HasValue, a => a.AcceptedTime <= request.AcceptedEndTime.Value.Date.AddDays(1))
                    .WhereIf(!string.IsNullOrWhiteSpace(request.AccepterName), a => a.AccepterName.Contains(request.AccepterName))
                    .WhereIf(request.ExpenseType.HasValue, m => m.ExpenseType == request.ExpenseType.Value)
                    .WhereIf(request.DeliveryMode.HasValue, m => m.DeliveryMode == request.DeliveryMode)
                    .Join(queryWfTask.Where(a => a.ApprovalId == CurrentUser.Id), a => a.Id, a => a.FormId, (a, b) => new { Pa = a, Task = b })
                    .GroupJoin(queryPr.AsNoTracking().Select(a => new { a.Id, a.SubBudgetId, a.ApplicationCode, a.ExpenseTypeCode }), a => a.Pa.PRId, b => b.Id, (a, b) => new { Pa = a.Pa, Task = a.Task, prs = b })
                    .SelectMany(a => a.prs.DefaultIfEmpty(), (a, b) => new { a.Pa, a.Task, Pr = b });

                result.TotalCount = queryLinq.Count();

                if (request.IsAsc)
                    result.Items = queryLinq.OrderBy(a => a.Task.ApprovalTime).Select(a => new { a.Pa, a.Pr, a.Task.ApprovalTime }).Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToArray()
                    .Select(a =>
                    {
                        var data = ObjectMapper.Map<PurPAApplication, PurPAApplicationDto>(a.Pa);
                        data.ApprovalTime = a.ApprovalTime;
                        data.PrApplicationCode = a.Pr?.ApplicationCode;

                        return data;
                    })
                    .ToArray();
                else
                    result.Items = queryLinq.OrderByDescending(a => a.Task.ApprovalTime).Select(a => new { a.Pa, a.Pr, a.Task.ApprovalTime }).Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToArray()
                    .Select(a =>
                    {
                        var data = ObjectMapper.Map<PurPAApplication, PurPAApplicationDto>(a.Pa);
                        data.ApprovalTime = a.ApprovalTime;
                        data.PrApplicationCode = a.Pr?.ApplicationCode;
                        return data;
                    })
                    .ToArray();
            }

            return result;
        }
        #endregion
        /// <summary>
        /// 更新补录文件
        /// </summary>
        /// <param name="paUploadFile"></param>
        /// <returns></returns>
        public async Task<MessageResult> UploadSponsorShipAsync(PaUploadFileDto paUploadFile)
        {
            var paApplicationRepository = LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>();
            var queryPa = await paApplicationRepository.GetQueryableAsync();
            var pa = queryPa.Where(a => a.Id == paUploadFile.PaId).FirstOrDefault();
            if (pa == null)
                return MessageResult.FailureResult("未查询到该付款申请数据");
            if (!paUploadFile.Files.Any())
                return MessageResult.FailureResult("请先上传附件");
            List<Guid> sponsorshipId = new List<Guid>();
            if (!string.IsNullOrWhiteSpace(pa.SponsorshipRewardPoints))
            {
                var ids = pa.SponsorshipRewardPoints.Split(',').Select(Guid.Parse).ToList();
                sponsorshipId.AddRange(ids);
            }
            sponsorshipId.AddRange(paUploadFile.Files.Select(a => a.AttachmentId));
            pa.SponsorshipRewardPoints = string.Join(",", sponsorshipId);
            var paupdate = await paApplicationRepository.UpdateAsync(pa);
            return MessageResult.SuccessResult(paupdate);
        }

        /// <summary>
        /// 提交付款
        /// </summary>
        /// <param name="paDetails"></param>
        /// <returns></returns>
        public async Task<MessageResult> SubmitPaInvoiceAsync(PADetailsEditDto paDetailEditDto)
        {
            if (paDetailEditDto.PAApplicationDetails.Sum(a => a.PaymentAmount) <= 0)
                return MessageResult.FailureResult("总付款金额必须大于0");

            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var paRepository = LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>();
            var queryPa = await paRepository.GetQueryableAsync();
            var paDetailRepository = LazyServiceProvider.LazyGetService<IPurPAApplicationDetailRepository>();
            var queryPaDetail = await paDetailRepository.GetQueryableAsync();
            var paInvoiceRepository = LazyServiceProvider.LazyGetService<IPurPAApplicationInvoiceRepository>();
            var queryPaInvoice = await paInvoiceRepository.GetQueryableAsync();
            var queryGr = await LazyServiceProvider.LazyGetService<IPurGRApplicationRepository>().GetQueryableAsync();
            var queryGrDetailHistory = await LazyServiceProvider.LazyGetService<IPurGRApplicationDetailHistoryRepository>().GetQueryableAsync();
            var queryPr = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var queryPrDetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var queryPoDetail = await LazyServiceProvider.LazyGetService<IPurPOApplicationDetailsRepository>().GetQueryableAsync();
            var pa = queryPa.Where(a => a.Id == paDetailEditDto.PAApplication.Id).FirstOrDefault();

            PurPAApplicationStatus[] submittableStatus = [PurPAApplicationStatus.FillIn, PurPAApplicationStatus.Reissue];
            if (!submittableStatus.Contains(pa.Status))
                return MessageResult.FailureResult("单据状态错误，提交失败");
            var isExpire = await VendorIsExpireAsync(new List<(string, Guid)> { (pa.ApplicationCode, pa.VendorId.Value) }, VendorExpireType.SUBMIT);
            if (!isExpire.Item1)
                return MessageResult.FailureResult(isExpire.Item2);
            var deliveryMode = pa.DeliveryMode;
            var historys = queryGrDetailHistory.Where(a => a.GRApplicationId == pa.GRId).ToList();//所有已收货记录
            var paGrDetailHistorys = historys.Where(a => a.PurPAApplicationId == pa.Id).ToList();//本次付款对应的收货记录 含签收金额为0的
            var paDetails = queryPaDetail.Where(a => a.PurPAApplicationId == paDetailEditDto.PAApplication.Id).ToList();

            var verifyIntercept = await VerifyInterceptAsync(paDetailEditDto.PAApplicationDetails, paDetailEditDto.PAApplication.Id);
            if (!verifyIntercept.Item1)
                return MessageResult.FailureResult(verifyIntercept.Item2);
            bool isNonInvoice = false;//是否无发票  AR无发票则不验证发票
            if (paDetailEditDto.PAApplication.PayMethod == PayMethods.AR)
                isNonInvoice = paDetailEditDto.PAApplicationDetails.FirstOrDefault().InvoiceType == InvoiceType.NonInvoice;
            if (!isNonInvoice)
            {
                var invoiceTotalAmount = paDetailEditDto.PAApplicationInvoices.Sum(a => a.InvoiceTotalAmount);//发票总金额
                var paymentAmount = paDetailEditDto.PAApplicationDetails.Sum(a => a.PaymentAmount);
                if (paDetailEditDto.PAApplication.IsBackupInvoice == false && Math.Round(paymentAmount, 2) > Math.Round(invoiceTotalAmount, 2))
                    return MessageResult.FailureResult("发票总金额必须大于等于支付金额");
                //1392【任务中心】【我发起的】（付款调整）税率调大时，不应该提示“请填写税率修改原因”（需求为：税率调小时显示税率修改原因模块）
                //var taxRateIsChange = from a in paDetailEditDto.PAApplicationDetails
                //                      join b in paDetails on a.Id equals b.Id
                //                      where a.TaxRate != b.TaxRate
                //                      select new { a.Id, a.TaxRate, oldTaxRate = b.TaxRate };
                var taxRateIsChange = paDetailEditDto.PAApplicationDetails.Join(paDetails, a => a.Id, a => a.Id, (a, b) =>
                {
                    decimal.TryParse(a.TaxRate, out decimal newTaxRate);
                    decimal.TryParse(b.TaxRate, out decimal oldTaxRate);
                    return new { a.Id, NewTaxRate = newTaxRate, OldTaxRate = oldTaxRate };
                })
                .Where(a => a.NewTaxRate < a.OldTaxRate);

                if (taxRateIsChange.Any() && string.IsNullOrWhiteSpace(paDetailEditDto.PAApplication.ReasonModification))
                    return MessageResult.FailureResult("请填写税率修改原因");

            }
            // 提醒勾选最后一次付款 
            var gr = queryGr.FirstOrDefault(a => a.Id == paDetailEditDto.PAApplication.GRId);
            //1404【任务中心】【我发起的】（付款调整）预付款：支付不含税必须严格等于预付款不含税（另注意：预付款可以调整税率，只需保证不含税金额相等即可）
            if (paDetailEditDto.PAApplication.AdvancePayment)
            {
                var payAmountWithoutTax = paDetailEditDto.PAApplicationDetails.Select(a =>
                {
                    decimal.TryParse(a.TaxRate, out decimal taxRate);
                    return a.PaymentAmount / (1 + taxRate);
                }).Sum();

                if (Math.Round(payAmountWithoutTax, 2) != Math.Round(gr.PaymentExcludingTaxAmount ?? 0, 2))
                    return MessageResult.FailureResult("支付不含税金额必须等于预付款不含税金额");
            }
            else
            {
                foreach (var item in paDetailEditDto.PAApplicationDetails)
                {
                    var receivedAmount = paGrDetailHistorys.Where(a => a.Id == item.GRHistoryId).Sum(x => x.ReceivedAmount);
                    if (Math.Round(item.PaymentAmount, 2) > Math.Round(receivedAmount ?? 0, 2))
                        return MessageResult.FailureResult($"{item.ProductName}:支付金额（不含税）必须小于等于签收金额（不含税）");
                }
            }

            if (!gr.IsAdvancePayment && !paDetailEditDto.PAApplication.IsLastPayment)//不是预付款才做验证,预付款可以无限收货
            {
                var isAllPayFinish = true;
                foreach (var item in paGrDetailHistorys)
                {
                    var isPayFinish = IsPayFinish(item, historys);
                    if (!isPayFinish) // 只要有一项未完成支付，就标记为false并结束循环
                    {
                        isAllPayFinish = false;
                        break;
                    }
                }
                if (isAllPayFinish)
                    return MessageResult.FailureResult("已收货金额大于等于可收货金额，请勾选最后一次付款后再提交");
            }
            if (paDetailEditDto.PAApplication.UrgentPayment && paDetailEditDto.PAApplication.IsAttachmentRequired && (paDetailEditDto.EmailAttachments == null || !paDetailEditDto.EmailAttachments.Any()))
            {
                return MessageResult.FailureResult("请上传紧急付款审批邮件");
            }
            var updatePa = ObjectMapper.Map(paDetailEditDto.PAApplication, pa);
            updatePa.PayTotalAmount = paDetailEditDto.PAApplicationDetails.Sum(a => a.PaymentAmount);
            if (paDetailEditDto.PAApplication.CityId.HasValue)
            {
                var specialCities = (await dataverseService.GetSpecialCitiesAsync(paDetailEditDto.PAApplication.CityId.ToString())).FirstOrDefault();
                updatePa.CityId = specialCities.Id;
                updatePa.CityCode = specialCities.CityCode;
            }
            if (paDetailEditDto.Files != null && paDetailEditDto.Files.Any())
            {
                var fileIds = paDetailEditDto.Files.Select(f => f.AttachmentId).ToList();
                updatePa.Attachments = string.Join(",", fileIds);
            }
            if (paDetailEditDto.InvoiceFiles != null && paDetailEditDto.InvoiceFiles.Any())
            {
                var fileIds = paDetailEditDto.InvoiceFiles.Select(f => f.AttachmentId).ToList();
                updatePa.Invoice = string.Join(",", fileIds);
            }
            if (paDetailEditDto.SponsorshipFiles != null && paDetailEditDto.SponsorshipFiles.Any())
            {
                var fileIds = paDetailEditDto.SponsorshipFiles.Select(f => f.AttachmentId).ToList();
                updatePa.SponsorshipRewardPoints = string.Join(",", fileIds);
            }
            if (paDetailEditDto.EmailAttachments != null && paDetailEditDto.EmailAttachments.Any())
            {
                var fileIds = paDetailEditDto.EmailAttachments.Select(f => f.AttachmentId).ToList();
                updatePa.EmailAttachment = string.Join(",", fileIds);
            }
            foreach (var item in paDetails)
            {
                var updatePaInvoice = paDetailEditDto.PAApplicationDetails.Where(a => a.Id == item.Id).FirstOrDefault();
                if (updatePaInvoice != null)
                    ObjectMapper.Map(updatePaInvoice, item);//映射更新paInvoice
            }
            List<PurPAApplicationInvoice> insertpaInvoices = null;
            if (!paDetailEditDto.PAApplication.IsBackupInvoice && isNonInvoice == false)
            {
                if (!paDetailEditDto.PAApplicationInvoices.Any())
                    return MessageResult.FailureResult("请填写发票信息");
                foreach (var item in paDetailEditDto.PAApplicationDetails)
                {
                    if (!paDetailEditDto.PAApplicationInvoices.Any(a => a.InvoiceType == item.InvoiceType && a.TaxRate == a.TaxRate))
                    {
                        return MessageResult.FailureResult($"支付明细信息与发票信息中，发票类型/税率/总金额不一致，请检查");
                    }
                }
                foreach (var item in paDetailEditDto.PAApplicationInvoices)
                {
                    if (!paDetailEditDto.PAApplicationDetails.Any(a => a.InvoiceType == item.InvoiceType && a.TaxRate == a.TaxRate))
                    {
                        return MessageResult.FailureResult($"支付明细信息与发票信息中，发票类型/税率/总金额不一致，请检查");
                    }
                }
                var groupInvoiceAndRate = paDetailEditDto.PAApplicationDetails.GroupBy(a => new { a.InvoiceType, a.TaxRate }).ToList();
                foreach (var item in groupInvoiceAndRate)
                {
                    var getInvoiceType = await dataverseService.GetDictionariesAsync(DictionaryType.InvoiceType);
                    var getTaxRateTask = await dataverseService.GetDictionariesAsync(DictionaryType.TaxRate);
                    var detailTotalAmount = paDetailEditDto.PAApplicationDetails.Where(a => a.InvoiceType == item.Key.InvoiceType && a.TaxRate == item.Key.TaxRate).Sum(a => a.PaymentAmount * (decimal)pa.ExchangeRate);//转换人民币
                    var invoiceTotalAmount = paDetailEditDto.PAApplicationInvoices.Where(a => a.InvoiceType == item.Key.InvoiceType && a.TaxRate == item.Key.TaxRate).Sum(a => a.InvoiceTotalAmount);//发票本身就是人民币
                    if (Math.Round(detailTotalAmount, 2) > Math.Round(invoiceTotalAmount, 2))
                        return MessageResult.FailureResult($"支付明细信息与发票信息中，发票类型/税率/总金额不一致，请检查");
                    //return MessageResult.FailureResult($"{getInvoiceType.Where(a => a.Code == item.Key.InvoiceType).FirstOrDefault()?.Name}税率{getTaxRateTask.Where(a => a.Code == item.Key.TaxRate).FirstOrDefault()?.Name ?? "0"},支付总金额不得大于发票金额");
                }
                insertpaInvoices = ObjectMapper.Map<List<PurPAApplicationInvoiceDto>, List<PurPAApplicationInvoice>>(paDetailEditDto.PAApplicationInvoices);
                insertpaInvoices.ForEach(a => { a.PurPAApplicationId = pa.Id; });
            }

            #region 税率是否改小
            string firstApprover = "";//审批人=》PO申请人
            bool isTaxRateSmall = false;//税率是否改小
            var poDetails = new List<PurPOApplicationDetails>();
            if (pa.PayMethod == PayMethods.AP)
            {
                var queryPo = await LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>().GetQueryableAsync();
                var po = queryPo.Where(a => a.Id == pa.POId).FirstOrDefault();
                poDetails = queryPoDetail.Where(a => paDetails.Select(a => a.PODetailId).ToList().Contains(a.Id)).ToList();
                foreach (var item in poDetails)
                {
                    var thisPoDetail = paDetails.Where(a => a.PODetailId == item.Id).FirstOrDefault();
                    decimal oldTaxRate = 0;
                    decimal taxRate = 0;
                    decimal.TryParse(item.TaxRate, out oldTaxRate);
                    decimal.TryParse(thisPoDetail.TaxRate, out taxRate);
                    if (taxRate < oldTaxRate)
                    {
                        isTaxRateSmall = true;//税率改小了
                        break;
                    }
                }
                firstApprover = po.ApplyUserId.ToString();
            }
            #endregion
            #region 1、付款预算使用
            //PA(扣除): 由于PA金额为含税金额，如果改小税率会导致不含税金额变大，此处可能额外扣除[PA不含税金额 - 签收不含税金额]，此处需要校验预算是否充足，提交的时候需要扣除预算
            var prDetailIds = paDetailEditDto.PAApplicationDetails.Select(a => a.PRDetailId).ToList();
            var prDetails = queryPrDetail.Where(a => prDetailIds.Contains(a.Id)).ToList();
            var pr = queryPr.Where(a => a.Id == pa.PRId).FirstOrDefault();
            //付款申请提交->增加归还预算记录 = -(PA不含税-GR不含税)    --调小税率(比GR小)
            var useBudgetRequest = new ReturnBudgetRequestDto
            {
                PrId = pa.PRId,
                SubbudgetId = pr.SubBudgetId.Value,
                Items = prDetails.Select(a =>
                {
                    var paDetailByPrDetail = paDetailEditDto.PAApplicationDetails.Where(x => x.PRDetailId == a.Id).ToList();//一个PR明细可存在多个PA明细
                    //PA不含税
                    var paNoTaxAmount = paDetailByPrDetail.Sum(x =>
                    {
                        decimal taxRate = 0M;
                        if (!InvoiceType.GiftIncrease.Equals(x.InvoiceType))
                        {
                            decimal.TryParse(x.TaxRate, out taxRate);
                        }
                        return x.PaymentAmount > 0 ? x.PaymentAmount / (1 + taxRate) : 0M;
                    });
                    //本次GR不含税
                    var grNoTaxAmount = paGrDetailHistorys.Where(x => x.PRDetailId == a.Id).Sum(x =>
                    {
                        decimal taxRate = 0M;
                        if (x.PODetailId.HasValue)
                        {
                            var pod = poDetails.Where(o => o.Id == x.PODetailId).FirstOrDefault();
                            if (pod == null)
                                return 0M;
                            if (!InvoiceType.GiftIncrease.Equals(pod.InvoiceType))
                            {
                                decimal.TryParse(pod.TaxRate, out taxRate);
                            }
                        }
                        return x.ReceivedAmount > 0 ? x.ReceivedAmount.Value / (1 + taxRate) : 0M;
                    });
                    //PA不含税 大于 GR不含税 时才为负数（才增加归还预算）
                    var returnAmount = (paNoTaxAmount - grNoTaxAmount) * (decimal)pa.ExchangeRate;
                    return new ReturnInfo
                    {
                        PdRowNo = a.RowNo,
                        ReturnAmount = -returnAmount,
                        ReturnSourceId = pa.Id,
                        ReturnSourceCode = pa.ApplicationCode
                    };
                })
            };
            if (useBudgetRequest.Items.Any(a => a.ReturnAmount < 0))
            {
                //增加归还预算
                useBudgetRequest.Items = useBudgetRequest.Items.Where(a => a.ReturnAmount < 0);
                UseBudgetRequestDto useBudget = new UseBudgetRequestDto()
                {
                    PrId = useBudgetRequest.PrId,
                    SubbudgetId = useBudgetRequest.SubbudgetId,
                    Items = useBudgetRequest.Items.Select(a => new UseInfo
                    {
                        PdRowNo = a.PdRowNo,
                        UseAmount = -a.ReturnAmount//使用预算取反
                    })
                };
                var useResult = await LazyServiceProvider.LazyGetService<ISubbudgetService>().CheckSubbudgetAmountSufficientAsync(useBudget);
                if (!useResult.Success)
                {
                    return useResult;//预算不够
                }
            }
            #endregion
            //创建审批流
            var createOk = await CreateWorkflowAsync(updatePa, firstApprover, isTaxRateSmall);
            if (!createOk)
                return MessageResult.FailureResult("PA审批流创建失败");
            #region 2、付款预算使用
            if (useBudgetRequest.Items.Any(a => a.ReturnAmount < 0))
            {
                //增加归还预算
                useBudgetRequest.Items = useBudgetRequest.Items.Where(a => a.ReturnAmount < 0);
                var result = await LazyServiceProvider.LazyGetService<ISubbudgetService>().ReturnSubbudgetAsync(useBudgetRequest);
                if (result.Success)
                {
                    //记录使用时间，加签人退回 返还后清空、加签人同意 后PA复审通过后清空（这个时候PA不能作废了)
                    updatePa.UseBudgetTime = (DateTime)result.Data;
                }
            }
            #endregion
            updatePa.Status = PurPAApplicationStatus.Approvaling;//变更状态
            await paRepository.UpdateAsync(updatePa);
            await paDetailRepository.UpdateManyAsync(paDetails);
            if (insertpaInvoices != null)
            {
                await paInvoiceRepository.DeleteAsync(a => a.PurPAApplicationId == pa.Id);//重新提交时删除之前的发票信息
                await paInvoiceRepository.InsertManyAsync(insertpaInvoices);//新增发票信息表
            }
            if (updatePa.PayMethod == PayMethods.AR && deliveryMode != updatePa.DeliveryMode && updatePa.DeliveryMode == DeliveryModes.OffLine)
            {
                var onOff = await LazyServiceProvider.LazyGetService<ISettingRepository>().FindAsync(SettingsConst.OmBpmInterfaceOnAndOff, "G", null);
                if (onOff?.Value == "OFF")
                {
                    // 付款申请线下递交，需要推送状态给om
                    var integrationOmAppService = LazyServiceProvider.GetService<IIntegrationOmAppService>();
                    await integrationOmAppService.OmUpdateMeetingSpeakerStatus(pa.ApplicationCode, "线下递交", "");
                }
                else
                {
                    //调度作业  老BPM
                    BackgroundJob.Enqueue<BpmOmWorker>(a => a.DoWorkAsync(new BpmRequestDto() { PaNo = pa.ApplicationCode, Status = "线下递交", Remark = "", InterfaceType = Enums.Integration.OmBpmType.UpdateStatus }));
                }
            }
            return MessageResult.SuccessResult();
        }
        private bool IsPayFinish(PurGRApplicationDetailHistory history, List<PurGRApplicationDetailHistory> historys)
        {
            //1.按数量 - 已收到 + 收到 >= 订单数量 
            //2.按比例 - 已收到 + 收到 >= 100 %
            //3.按金额 - 历史签收金额之和 >= 数量 * 单价
            var thishistorys = historys.Where(a => a.GRApplicationDetailId == history.GRApplicationDetailId).ToList();
            bool isFinish = false;
            switch (history.DeliveryMethod)
            {
                case DeliveryMethod.ByQuantity: //1.按数量 - 已收到 + 收到 >= 订单数量 
                    if (thishistorys.Sum(a => a.CurrentReceivingQuantity ?? 0M) >= history.OrderQuantity)
                        isFinish = true;
                    break;
                case DeliveryMethod.ByProportion: //2.按比例 - 已收到 + 收到 >= 100 %
                    if ((thishistorys.Sum(a => a.CurrentReceivingQuantity ?? 0M) / 100M) >= 1)
                        isFinish = true;
                    break;
                case DeliveryMethod.ByAmount://3.按金额 - 历史签收金额之和 >= 数量 * 单价
                    if (thishistorys.Sum(a => a.CurrentReceivingQuantity ?? 0M) >= (history.OrderQuantity * history.UnitPrice))
                        isFinish = true;
                    break;
            }
            return isFinish;
        }

        /// <summary>
        /// 获取当前供应商银行信息
        /// </summary>
        /// <param name="vendorId"></param>
        /// <returns></returns>
        public async Task<MessageResult> GetBankCardInfoAsync(Guid vendorId)
        {
            var bpcsAvmRepository = await LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync();
            var bpcsPmfvm = await LazyServiceProvider.LazyGetService<IBpcsPmfvmRepository>().GetQueryableAsync();
            var bankCardInfo = bpcsAvmRepository.Where(a => a.Id == vendorId)
                .Join(bpcsPmfvm,
                avm => new { vendor = avm.Vendor, Vcmpny = avm.Vcmpny },
                pmfvm => new { vendor = pmfvm.Vnderx, Vcmpny = pmfvm.Vmcmpy },
                (avm, pmfvm) => new BankCardInfoDto
                {
                    BankName = pmfvm.Vldrm1,
                    BankCard = pmfvm.Vldrm2
                })
                .FirstOrDefault();
            if (bankCardInfo == null)
                return MessageResult.FailureResult("供应商银行信息未录入，请确认");
            return MessageResult.SuccessResult(bankCardInfo);
        }
        /// <summary>
        /// PA作废
        /// </summary>
        /// <param name="paId"></param>
        /// <param name="remarks"></param>
        /// <returns></returns>
        public async Task<MessageResult> PAInvalidAsync(InvalidRequestDto invalid)
        {
            var paRepository = LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>();
            var grRepository = LazyServiceProvider.LazyGetService<IPurGRApplicationRepository>();
            var grDetailsHistoryRepository = LazyServiceProvider.LazyGetService<IPurGRApplicationDetailHistoryRepository>();
            var approveService = LazyServiceProvider.LazyGetService<IApproveService>();
            var queryPa = await paRepository.GetQueryableAsync();
            var queryGr = await grRepository.GetQueryableAsync();
            var queryGrDetailsHistory = await grDetailsHistoryRepository.GetQueryableAsync();
            var pa = queryPa.Where(a => a.Id == invalid.Id).FirstOrDefault();
            //if (pa.ApplyUserId != CurrentUser.Id)
            //    return MessageResult.FailureResult("您不是申请人，无权作废该付款申请");
            var agents = await LazyServiceProvider.LazyGetService<IAgencyService>().GetOriginalOperators(new GetAgentOperatorsRequestDto() { BusinessTypeCategory = ResignationTransfer.TaskFormCategory.PaymentApplication });
            var originalUserIds = agents.Select(a => a.Key).Distinct().ToArray();
            if (originalUserIds.Length == 0 || (!originalUserIds.Contains(pa.ApplyUserId) && (!pa.TransfereeId.HasValue || !originalUserIds.Contains(pa.TransfereeId.Value))))
                return MessageResult.FailureResult("您不是申请人，无权作废该付款申请");

            if (PurPAApplicationStatus.Reissue != pa.Status && PurPAApplicationStatus.FillIn != pa.Status)
                return MessageResult.FailureResult("该付款申请当前状态不能作废");


            var gr = queryGr.Where(a => a.Id == pa.GRId).FirstOrDefault();
            var grDetailsHistory = queryGrDetailsHistory.Where(a => a.PurPAApplicationId == pa.Id).ToList();
            gr.Status = PurGRApplicationStatus.ToBeReceived;//GR变更状态为待收货
            pa.Status = PurPAApplicationStatus.Void;//PA变更状态为作废
            await paRepository.UpdateAsync(pa);
            var grPAs = queryPa.Where(a => a.Status != PurPAApplicationStatus.Void && a.GRId == pa.GRId).ToList();
            if (grPAs.Count == 1 && grPAs.Any(a => a.Id == pa.Id))
            {
                gr.IsAdvancePayment = false;//GR仅有当前作废PA时可以将 GR是否预付款恢复默认值。以便后续重先收货时可以更改预付款
            }
            if (gr.UseBudgetTime.HasValue)
                await LazyServiceProvider.LazyGetService<IPurGRApplicationService>().ReturnSubbudgetAsync(gr.PrId, gr.Id, gr.UseBudgetTime.Value);//预算返还
            gr.UseBudgetTime = null;//清除GR提交时使用预算时间，待后续收货使用
            await grRepository.UpdateAsync(gr);
            await grDetailsHistoryRepository.DeleteManyAsync(grDetailsHistory);//删除GR历史
            await approveService.AddApprovalRecordAsync(new AddApprovalRecordDto()
            {
                FormId = pa.Id,
                ApprovalId = CurrentUser.Id.Value,
                OriginalApprovalId = pa.ApplyUserId,
                Status = ApprovalOperation.Delete,
                Remark = invalid.Remark,
                ApprovalTime = DateTime.Now,
                WorkStep = "付款申请作废",
                Name = "付款申请作废"
            });//创建付款审批历史记录（假的审批记录—不走真正流程-为审批历史新增可查询记录）


            var onOff = await LazyServiceProvider.LazyGetService<ISettingRepository>().FindAsync(SettingsConst.OmBpmInterfaceOnAndOff, "G", null);
            if (onOff?.Value == "OFF")
            {
                // 付款申请作废，需要推送状态给om
                var integrationOmAppService = LazyServiceProvider.GetService<IIntegrationOmAppService>();
                await integrationOmAppService.OmUpdateMeetingSpeakerStatus(pa.ApplicationCode, "作废", invalid.Remark);
            }
            else
            {
                //调度作业  老BPM
                BackgroundJob.Enqueue<BpmOmWorker>(a => a.DoWorkAsync(new BpmRequestDto() { PaNo = pa.ApplicationCode, Status = "作废", Remark = invalid.Remark, InterfaceType = Enums.Integration.OmBpmType.UpdateStatus }));
            }

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 获取财务信息模块
        /// </summary>
        /// <param name="paId"></param>
        /// <returns></returns>
        public async Task<MessageResult> GetPAApplicicationFinancialVoucher(Guid paId)
        {
            var result = new PAApplicicationFinancialVoucherDto();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var queryablePR = (await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            var queryPrDetails = (await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync()).AsNoTracking();
            var queryPa = (await LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            var queryPaDetail = (await LazyServiceProvider.LazyGetService<IPurPAApplicationDetailRepository>().GetQueryableAsync()).AsNoTracking();
            var queryPaVoucherInfo = (await LazyServiceProvider.LazyGetService<IPurPAFinancialVoucherInfoRepository>().GetQueryableAsync()).AsNoTracking();
            var avtRepository = (await LazyServiceProvider.LazyGetService<IBpcsAvtRepository>().GetQueryableAsync()).AsNoTracking();
            var queryBpcsAvm = (await LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync()).AsNoTracking();
            var queryPRProduct = (await LazyServiceProvider.LazyGetService<IPurPRApplicationProductApportionmentRepository>().GetQueryableAsync()).AsNoTracking();
            var pa = queryPa.Where(a => a.Id == paId).FirstOrDefault();
            if (pa == null)
                return MessageResult.SuccessResult("未查询到该付款申请");

            result.AkritivCaseID = pa.AkritivCaseID;
            result.ReceivedDocumentsDate = pa.AcceptedTime.HasValue ? pa.AcceptedTime.Value.ToString("yyyy-MM-dd") : "";
            result.TaxStampLegal = pa.TaxStampLegal;
            result.Status = pa.Status;
            result.PAId = pa.Id;
            var paVoucherInfo = queryPaVoucherInfo.Where(a => a.PAId == pa.Id).ToList();
            if (paVoucherInfo.Any())
            {
                var paFinancialVoucherInfos = ObjectMapper.Map<List<PurPAFinancialVoucherInfo>, List<PAFinancialVoucherInfoDto>>(paVoucherInfo);
                result.PAFinancialVoucherInfos = paFinancialVoucherInfos;
                result.PaymentTerms = pa.PaymentTerms;
                result.InvoiceDescription = pa.InvoiceDescription;
                result.EstimatedPaymentDate = pa.EstimatedPaymentDate.HasValue ? pa.EstimatedPaymentDate.Value.ToString("yyyy-MM-dd") : "";
            }
            else
            {
                var bpcsAvm = queryBpcsAvm.Where(a => a.Id == pa.VendorId)
                    .GroupJoin(avtRepository, a => new { Company = a.Vcmpny, Term = a.Vterms }, b => new { Company = b.Vcmpy, Term = b.Vterm }, (a, b) => new { avm = a, avts = b })
                    .SelectMany(a => a.avts.DefaultIfEmpty(), (a, b) => new { a.avm, avt = b })
                    .FirstOrDefault();

                if (pa.PayMethod == PayMethods.AR)
                {
                    result.PaymentTerms = bpcsAvm?.avt?.Vterm ?? "00";//数据库里存code
                }
                else
                {
                    result.PaymentTerms = pa.PaymentTerms;
                }
                var paDetails = queryPaDetail.Where(a => a.PurPAApplicationId == pa.Id).ToList();

                var (invoiceDate, invoiceDateTime) = await GetInvoiceDate(pa, paDetails);

                result.InvoiceDate = invoiceDateTime.HasValue ? invoiceDateTime.Value : pa.CreationTime;
                if (pa.UrgentPayment)//紧急付款就自动改成00_0天
                    result.PaymentTerms = "00";
                result.InvoiceDescription = PAExpenseType.LectureFee.Equals(pa.ExpenseType) ? $"{pa.ExpenseType.GetDescription()}{pa.ApplicationCode}" : pa.InvoiceDescription;
                //InvoiceDate+PaymentTerms 天数 时间
                var vtmddy = avtRepository.Where(a => a.Vterm == result.PaymentTerms).FirstOrDefault()?.Vtmddy;
                result.EstimatedPaymentDate = invoiceDateTime.HasValue ? invoiceDateTime.Value.AddDays((double)(vtmddy ?? 0M)).ToString("yyyy-MM-dd") : pa.CreationTime.AddDays((double)(vtmddy ?? 0M)).ToString("yyyy-MM-dd");

                List<PAFinancialVoucherInfoDto> paFinancialVoucherInfos = new List<PAFinancialVoucherInfoDto>();
                PAFinancialVoucher paFinancialVoucher = new PAFinancialVoucher();
                List<FinancialVoucherInfoDto> voucherInfoInvoices = new List<FinancialVoucherInfoDto>();

                //按照PRDetails 拆分财务凭证信息1、不含税金额  2、税额
                foreach (var item in paDetails)
                {
                    var paInvoice = ObjectMapper.Map<PurPAApplicationDetail, PurPAApplicationDetailDto>(item);
                    if (string.IsNullOrWhiteSpace(item.InvoiceType))
                    {
                        paInvoice.InvoiceType = "NonInvoice";
                    }
                    //1、按品名及发票类型拆分为财务凭证信息
                    var voucherInfoInvoice = await paFinancialVoucher.GetFinancialVoucherInfo(paInvoice);
                    voucherInfoInvoices.AddRange(voucherInfoInvoice);
                }
                var prdIds = paDetails.Select(a => a.PRDetailId).Distinct().ToList();
                var prDetails = queryPrDetails.Where(a => prdIds.Contains(a.Id)).ToList();
                var prId = prDetails.FirstOrDefault().PRApplicationId;
                var pr = queryablePR.Where(a => a.Id == prId).FirstOrDefault();//查询对应的PR主表数据
                var prUserBu = (await dataverseService.GetBuCodingCfgAsync(pr.ApplyUserBu.ToString())).FirstOrDefault();
                var bpcsVendor = bpcsAvm?.avm;
                var prProducts = queryPRProduct.Where(a => a.PRApplicationId == prId && a.PRApplicationDetailId.HasValue && prdIds.Contains(a.PRApplicationDetailId.Value)).ToList();//产品拆分数据

                var expenseTypes = await dataverseService.GetConsumeCategoryAsync(pr.ExpenseType.ToString());
                var expenseType = expenseTypes.FirstOrDefault();
                var specialCities = await dataverseService.GetOrgSpecialCityRelationAsync();
                var ppProducts = await dataverseService.GetProductsAsync();

                //2、根据PR汇总
                foreach (var prdId in prdIds)
                {
                    var prd = prDetails.Where(a => a.Id == prdId).FirstOrDefault();
                    var products = prProducts.Where(a => a.PRApplicationDetailId == prdId).ToList();
                    var values = Enum.GetValues(typeof(GroupingDimension));
                    foreach (GroupingDimension value in values)
                    {
                        var prVoucherInfoInvoice = voucherInfoInvoices.Where(a => a.PRDetailId == prdId && a.GroupingDimension == value).ToList();
                        if (prVoucherInfoInvoice == null || !prVoucherInfoInvoice.Any())
                            continue;

                        //5035急急急[付款申请][财务初审]初审时目前会比较凭证总金额是否等于PA总金额，此处需要每一行分别四舍五入之后加总再比对，另外在按产品分摊生成凭证时也需要四舍五入
                        prVoucherInfoInvoice.ForEach(a => a.InvoiceLineAmount = a.InvoiceLineAmount.HasValue ? decimal.Round(a.InvoiceLineAmount.Value, 2) : 0);

                        switch (value)
                        {
                            case GroupingDimension.TaxNotIncluded:
                                foreach (var item in products)
                                {
                                    PAFinancialVoucherInfoDto taxNotIncluded = new PAFinancialVoucherInfoDto();
                                    taxNotIncluded.PAId = pa.Id;
                                    taxNotIncluded.IsOriginal = true;
                                    taxNotIncluded.PRDetailId = prdId;
                                    taxNotIncluded.InvoiceLineAmount = prVoucherInfoInvoice.Sum(a => a.InvoiceLineAmount) * (decimal)(item.Ratio / 100);
                                    taxNotIncluded.VendorName = pa.VendorCode;

                                    taxNotIncluded.IsTax = false;
                                    taxNotIncluded.NatureAccount = pa.AdvancePayment ? "00890" : prd.CostNatureCode;
                                    taxNotIncluded.DivisionCode = IsBSAccount(taxNotIncluded.NatureAccount, pa.CompanyCode) ? prUserBu.PushSpecialCode : prUserBu.BuCode;
                                    taxNotIncluded.CostCenter = IsBSAccount(taxNotIncluded.NatureAccount, pa.CompanyCode) ? "0000" : pr.CostCenterCode;
                                    //taxNotIncluded.SubAccount = prd.CostNatureCode == CostNatureCode.Caring ? "8821" : item.ProductCode;//产品Code
                                    //费用性质编码为0开头时：按产品Id查询产品主数据中的限定费用性质编码，匹配上则直接取产品code，否则就是0000
                                    if (IsBSAccount(taxNotIncluded.NatureAccount, pa.CompanyCode))
                                    {
                                        var ppProduct = ppProducts.FirstOrDefault(a => a.Id == item.ProductId);
                                        taxNotIncluded.SubAccount = ppProduct?.LimitCostNatureCode?.Split(",").Contains(taxNotIncluded.NatureAccount) == true ? ppProduct.Code : taxNotIncluded.NatureAccount == CostNatureCode.C_02550 ? "6813" : "0000";
                                    }
                                    else
                                        taxNotIncluded.SubAccount = item.ProductCode;

                                    taxNotIncluded.Location = GetLocationAsync(pr, taxNotIncluded.NatureAccount, pa, GroupingDimension.TaxNotIncluded, specialCities);

                                    taxNotIncluded.InvoiceReference = pa.ApplicationCode.Replace("A", "");
                                    taxNotIncluded.InvoiceDate = invoiceDate;
                                    taxNotIncluded.InvoiceDescription = PAExpenseType.LectureFee.Equals(pa.ExpenseType) ? $"{pa.ExpenseType.GetDescription()}{pa.ApplicationCode}" : pa.InvoiceDescription;
                                    taxNotIncluded.InvoiceReceiptReference = pa.ApplicationCode.Replace("A", "");
                                    taxNotIncluded.Bank = bpcsVendor?.Vmbank;
                                    taxNotIncluded.InvoiceReceiptDate = pa.AcceptedTime?.ToString("yyyyMMdd");
                                    taxNotIncluded.CurrencyCode = pa.Currency;
                                    taxNotIncluded.RecognitionRate = (decimal)pa.ExchangeRate;
                                    taxNotIncluded.CompanyCode = pr.CompanyCode;
                                    paFinancialVoucherInfos.Add(taxNotIncluded);
                                }
                                break;
                            case GroupingDimension.TaxIncluded:
                                PAFinancialVoucherInfoDto taxIncluded = new PAFinancialVoucherInfoDto();
                                taxIncluded.PAId = pa.Id;
                                taxIncluded.IsOriginal = true;
                                taxIncluded.PRDetailId = prdId;
                                taxIncluded.InvoiceLineAmount = prVoucherInfoInvoice.Sum(a => a.InvoiceLineAmount);
                                taxIncluded.VendorName = pa.VendorCode;

                                taxIncluded.IsTax = true;
                                taxIncluded.DivisionCode = prUserBu.PushSpecialCode;//PR的BU编码，根据PP内关联关系转换(61->60,62->60)
                                taxIncluded.CostCenter = "0000";
                                taxIncluded.NatureAccount = "02550";

                                taxIncluded.SubAccount = pa.CompanyCode == "79" ? "6810" : prd.CostNatureCode == CostNatureCode.C_00450 ? "6813" : expenseType?.IsAssets == true ? "6814" : "6816";
                                taxIncluded.Location = GetLocationAsync(pr, prd.CostNatureCode, pa, GroupingDimension.TaxIncluded, specialCities);

                                taxIncluded.InvoiceReference = pa.ApplicationCode.Replace("A", "");
                                taxIncluded.InvoiceDate = invoiceDate;
                                taxIncluded.InvoiceDescription = PAExpenseType.LectureFee.Equals(pa.ExpenseType) ? $"{pa.ExpenseType.GetDescription()}{pa.ApplicationCode}" : pa.InvoiceDescription;
                                taxIncluded.InvoiceReceiptReference = pa.ApplicationCode.Replace("A", "");
                                taxIncluded.Bank = bpcsVendor?.Vmbank;
                                taxIncluded.InvoiceReceiptDate = pa.AcceptedTime?.ToString("yyyyMMdd");
                                taxIncluded.CurrencyCode = pa.Currency;
                                taxIncluded.RecognitionRate = (decimal)pa.ExchangeRate;
                                taxIncluded.CompanyCode = pr.CompanyCode;
                                paFinancialVoucherInfos.Add(taxIncluded);
                                break;
                            case GroupingDimension.PositiveTax:
                                PAFinancialVoucherInfoDto positiveTax = new PAFinancialVoucherInfoDto();
                                positiveTax.PAId = pa.Id;
                                positiveTax.IsOriginal = true;
                                positiveTax.PRDetailId = prdId;
                                positiveTax.InvoiceLineAmount = prVoucherInfoInvoice.Sum(a => a.InvoiceLineAmount);
                                positiveTax.VendorName = pa.VendorCode;

                                positiveTax.IsTax = true;
                                positiveTax.DivisionCode = prUserBu.PushSpecialCode;//PR的BU编码，根据PP内关联关系转换(61->60,62->60)
                                positiveTax.CostCenter = "0000";
                                positiveTax.NatureAccount = "02550";

                                positiveTax.SubAccount = pa.CompanyCode == "79" ? "6810" : expenseType?.IsAssets == true ? "6814" : "6812";
                                positiveTax.Location = GetLocationAsync(pr, prd.CostNatureCode, pa, GroupingDimension.PositiveTax, specialCities);

                                positiveTax.InvoiceReference = pa.ApplicationCode.Replace("A", "");
                                positiveTax.InvoiceDate = invoiceDate;
                                positiveTax.InvoiceDescription = PAExpenseType.LectureFee.Equals(pa.ExpenseType) ? $"{pa.ExpenseType.GetDescription()}{pa.ApplicationCode}" : pa.InvoiceDescription;
                                positiveTax.InvoiceReceiptReference = pa.ApplicationCode.Replace("A", "");
                                positiveTax.Bank = bpcsVendor?.Vmbank;
                                positiveTax.InvoiceReceiptDate = pa.AcceptedTime?.ToString("yyyyMMdd");
                                positiveTax.CurrencyCode = pa.Currency;
                                positiveTax.RecognitionRate = (decimal)pa.ExchangeRate;
                                positiveTax.CompanyCode = pr.CompanyCode;
                                paFinancialVoucherInfos.Add(positiveTax);
                                break;
                            case GroupingDimension.NegativeTax:
                                PAFinancialVoucherInfoDto negativeTax = new PAFinancialVoucherInfoDto();
                                negativeTax.PAId = pa.Id;
                                negativeTax.IsOriginal = true;
                                negativeTax.PRDetailId = prdId;
                                negativeTax.InvoiceLineAmount = prVoucherInfoInvoice.Sum(a => a.InvoiceLineAmount);
                                negativeTax.VendorName = pa.VendorCode;

                                negativeTax.IsTax = true;
                                negativeTax.DivisionCode = prUserBu.PushSpecialCode;//PR的BU编码，根据PP内关联关系转换(61->60,62->60)
                                negativeTax.CostCenter = "0000";
                                negativeTax.NatureAccount = "02550";
                                negativeTax.SubAccount = pa.CompanyCode == "79" ? "6810" : expenseType?.IsAssets == true ? "6814" : "6824";
                                negativeTax.Location = GetLocationAsync(pr, prd.CostNatureCode, pa, GroupingDimension.NegativeTax, specialCities);

                                negativeTax.InvoiceReference = pa.ApplicationCode.Replace("A", "");
                                negativeTax.InvoiceDate = invoiceDate;
                                negativeTax.InvoiceDescription = PAExpenseType.LectureFee.Equals(pa.ExpenseType) ? $"{pa.ExpenseType.GetDescription()}{pa.ApplicationCode}" : pa.InvoiceDescription;
                                negativeTax.InvoiceReceiptReference = pa.ApplicationCode.Replace("A", "");
                                negativeTax.Bank = bpcsVendor?.Vmbank;
                                negativeTax.InvoiceReceiptDate = pa.AcceptedTime?.ToString("yyyyMMdd");
                                negativeTax.CurrencyCode = pa.Currency;
                                negativeTax.RecognitionRate = (decimal)pa.ExchangeRate;
                                negativeTax.CompanyCode = pr.CompanyCode;
                                paFinancialVoucherInfos.Add(negativeTax);
                                break;
                        }
                    }
                }
                result.PAFinancialVoucherInfos = paFinancialVoucherInfos;
            }
            return MessageResult.SuccessResult(result);
        }

        /// <summary>
        /// 费用性质是BS Account
        /// </summary>
        /// <returns></returns>
        private bool IsBSAccount(string natureAccount, string companyCode)
        {
            if (string.IsNullOrWhiteSpace(natureAccount))
                return false;

            if (natureAccount.StartsWith("0") && !natureAccount.StartsWith("0067"))
            {
                return true;
            }
            else if (natureAccount.StartsWith("0067"))
            {
                if (companyCode != "20")
                {
                    //79\91\18等 --嘉兴工厂，按现在逻辑处理
                    return true;
                }
                else
                {
                    //20 雅培贸易 
                    return false;
                }
            }
            //不满足以上情况，为非BS Account
            return false;
        }

        /// <summary>
        /// 获取城市code
        /// </summary>
        /// <param name="pr"></param>
        /// <param name="natureAccount"></param>
        /// <param name="pa"></param>
        /// <param name="groupingDimension"></param>
        /// <returns></returns>
        string GetLocationAsync(PurPRApplication pr, string natureAccount, PurPAApplication pa, GroupingDimension groupingDimension, IEnumerable<OrgSpecialCityRelationDto> specialCities)
        {
            //var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            //var orgSpecialCityRelations = await dataverseService.GetOrgSpecialCityRelationAsync(pr.ApplyUserBu.ToString());
            //var cityIds = orgSpecialCityRelations.Select(a => a.CityId);
            //var specialCities = await dataverseService.GetOrgSpecialCityRelationAsync();
            var hasRestricted = specialCities.Any(a => a.OrgId == pr.ApplyUserBu && a.IsRestricted == true);

            //限定城市则返回PA的城市code
            if (hasRestricted)
                return pa.CityCode;
            else
            {
                //非不含税时或固定资产时，返回0000
                if (groupingDimension != GroupingDimension.TaxNotIncluded || IsBSAccount(natureAccount, pa.CompanyCode))
                    return "0000";
            }

            return pa.CityCode;
        }

        /// <summary>
        /// 获取财务凭证信息填写可选值
        /// </summary>
        /// <param name="paId"></param>
        /// <returns></returns>
        public async Task<OptionalFinancialInfoDto> GetOptionalFinancialInfoAsync(Guid paId)
        {
            var result = new OptionalFinancialInfoDto();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var queryablePR = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var queryPrDetails = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var queryPa = await LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>().GetQueryableAsync();
            var queryPaInvoice = await LazyServiceProvider.LazyGetService<IPurPAApplicationDetailRepository>().GetQueryableAsync();
            var queryPaVoucherInfo = await LazyServiceProvider.LazyGetService<IPurPAFinancialVoucherInfoRepository>().GetQueryableAsync();
            var avtRepository = await LazyServiceProvider.LazyGetService<IBpcsAvtRepository>().GetQueryableAsync();
            var queryBpcsAvm = await LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync();
            var pa = queryPa.Where(a => a.Id == paId).FirstOrDefault();
            if (pa == null)
                return null;
            var paInvoices = queryPaInvoice.Where(a => a.PurPAApplicationId == pa.Id).ToList();
            var prdIds = paInvoices.Select(a => a.PRDetailId).Distinct().ToList();
            var prDetails = queryPrDetails.Where(a => prdIds.Contains(a.Id)).ToList();
            var prId = prDetails.FirstOrDefault().PRApplicationId;
            var pr = queryablePR.Where(a => a.Id == prId).FirstOrDefault();//查询对应的PR主表数据
            var prUserBu = (await dataverseService.GetBuCodingCfgAsync(pr.ApplyUserBu.ToString())).FirstOrDefault();
            // Division Code: 仅当申请部门对应Division的Code，为61/62时才可改，且仅可修改为自身或60，只能下拉里选择，不允许填写
            var divisionCodes = new List<DropdownListDto<string, string>>();
            if (prUserBu?.BuCode == "61" || prUserBu?.BuCode == "62")
            {
                divisionCodes.Add(new DropdownListDto<string, string>(prUserBu.BuCode, prUserBu.BuCode));
                divisionCodes.Add(new DropdownListDto<string, string>("60", "60"));
                result.DivisionCode = divisionCodes;
            }
            //Cost Center:仅当为非税金行才可修改，修改时的可选项根据用户的不同角色会有区分，只能下拉里选择，不允许填写
            var costcenters = await dataverseService.GetCostcentersAsync();
            var costcenter = new List<DropdownListDto<string, string>>();
            costcenters.ToList().ForEach(a =>
            {
                costcenter.Add(new DropdownListDto<string, string>(a.Code, a.Code));
            });
            result.CostCenter = costcenter.GroupBy(dto => dto.Key)
                        .Select(group => group.First())
                        .ToList();

            //Sub Account: 始终可以修改，但税金/非税金行的可选项有差异:税金行-指定的列表、非税金行-原产品编码或“0000”，只能下拉里选择，不允许填写
            var subAccountAll = await dataverseService.GetProductsAsync();
            var subAccounts = new List<DropdownListDto<string, string>>();

            #region 非税金行 原产品编码或“0000”
            foreach (var item in prDetails)
            {
                subAccounts.Add(new DropdownListDto<string, string>("0000", "0000", item.Id.ToString()));
                if (item.ProductId.HasValue == false)
                    continue;
                var prProduct = subAccountAll.Where(a => a.Id == item.ProductId).FirstOrDefault();
                subAccounts.Add(new DropdownListDto<string, string>(prProduct.Code, prProduct.Code, item.Id.ToString()));
            }
            result.SubAccount = subAccounts.GroupBy(dto => new { dto.Key, dto.Type })
                        .Select(group => group.First())
                        .ToList();
            #endregion

            #region 税金行 指定的列表
            var subAccountTaxs = new List<DropdownListDto<string, string>>()
            {
                new DropdownListDto<string, string>("0000", "0000"),
                new DropdownListDto<string, string>("0700", "0700"),
                new DropdownListDto<string, string>("6810", "6810"),
                new DropdownListDto<string, string>("6812", "6812"),
                new DropdownListDto<string, string>("6813", "6813"),
                new DropdownListDto<string, string>("6814", "6814"),
                new DropdownListDto<string, string>("6816", "6816"),
                new DropdownListDto<string, string>("6824", "6824")
            };
            result.SubAccountTax = subAccountTaxs;
            #endregion
            return result;
        }

        /// <summary>
        /// PA根据关键字获取费用性质
        /// </summary>
        /// <param name="paId"></param>
        /// <param name="keywords"></param>
        /// <returns></returns>
        public async Task<List<DropdownListDto<string, string>>> GetNatureAccountAsync(Guid paId, string keywords)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var queryPa = await LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>().GetQueryableAsync();
            var pa = queryPa.Where(a => a.Id == paId).FirstOrDefault();
            if (pa == null)
                return null;
            var orgs = await dataverseService.GetOrganizations(pa.ApplyUserBu.ToString());
            if (!orgs.Any())
                return null;
            //获取分公司
            var org = orgs.FirstOrDefault();
            var affiliates = await LazyServiceProvider.LazyGetService<ICommonService>().GetAffiliatesOrgs(org);
            var affiliate = affiliates.FirstOrDefault(a => a.OrganizationType == DataverseEnums.Organization.OrganizationType.Affiliates);
            if (affiliate == null)
                return null;
            var affiliateConsumeCategory = await dataverseService.GetConsumeCategoryAsync(affiliate.Id.ToString());//更加机构ID 获取消费大类
            affiliateConsumeCategory = affiliateConsumeCategory.Where(a => !a.FlowType.HasValue || a.FlowType == DataverseEnums.ConsumeCategory.FlowTypes.PR);
            var costNatures = new List<DropdownListDto<string, string>>();
            foreach (var item in affiliateConsumeCategory)
            {
                var costNature = await dataverseService.GetCostNatureAsync(item.Id.ToString());
                if (costNature.Any())
                    costNature.ToList().ForEach(a =>
                    {
                        costNatures.Add(new DropdownListDto<string, string>(a.Code, a.Name));
                    });
            }
            costNatures = costNatures.GroupBy(dto => dto.Key)
                        .Select(group => group.First())
                        .ToList();
            costNatures = costNatures.WhereIf(!string.IsNullOrWhiteSpace(keywords), a => a.Key.Contains(keywords) || a.Value.Contains(keywords)).ToList();
            return costNatures;
        }

        /// <summary>
        /// 初审完成
        /// </summary>
        /// <param name="paPreliminaryApprovalCompleted"></param>
        /// <returns></returns>
        public async Task<MessageResult> PreliminaryApprovalCompletedAsync(PAPreliminaryApprovalCompletedDto paPreliminaryApprovalCompleted)
        {
            var paRepository = LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>();
            var queryPa = await paRepository.GetQueryableAsync();
            var paVoucherRepository = LazyServiceProvider.LazyGetService<IPurPAFinancialVoucherInfoRepository>();
            var queryPaVoucherInfo = await paVoucherRepository.GetQueryableAsync();
            var approveService = LazyServiceProvider.LazyGetService<IApproveService>();
            if (paPreliminaryApprovalCompleted.PAFinancialVoucherInfos == null || !paPreliminaryApprovalCompleted.PAFinancialVoucherInfos.Any())
                return MessageResult.FailureResult("请填写财务凭证信息");
            var pa = queryPa.Where(a => a.Id == paPreliminaryApprovalCompleted.PAId).FirstOrDefault();
            var isExpire = await VendorIsExpireAsync(new List<(string, Guid)> { (pa.ApplicationCode, pa.VendorId.Value) }, VendorExpireType.APPROVE);
            if (!isExpire.Item1)
            {
                #region 供应商失效通知申请人
                var userQuery = (await LazyServiceProvider.LazyGetService<IRepository<IdentityUser, Guid>>().GetQueryableAsync()).AsNoTracking();
                var user = userQuery.FirstOrDefault(a => a.Id == pa.ApplyUserId);
                var sendEmaillRecords = new InsertSendEmaillRecordDto
                {
                    EmailAddress = user.Email,
                    Subject = "[NexBPM消息中心]您发起的付款申请{ApplicationCode}中供应商已失效，请及时激活供应商。",
                    Content = JsonSerializer.Serialize(new VendorExpireNoticeToApplicanDto
                    {
                        UserName = user.Name,
                        ApplicationCode = pa.ApplicationCode,
                        VendorName = pa.VendorName,
                        Company = pa.CompanyName,
                        VendorCode = pa.VendorCode,
                        ApplicationLink = $"{_configuration["SpeakerEmail:WebHost"]?.TrimEnd('/')}/procure/payment/detail/{pa.Id}",
                    }),
                    SourceType = EmailSourceType.VendorExpireNoticeToApplican,
                    Status = SendStatus.Pending,
                    Attempts = 0,
                };
                //记录邮件，并触发邮件发送功能
                var emailService = LazyServiceProvider.LazyGetService<IEmailService>();
                await emailService.InsertSendEmaillRecordAsync([sendEmaillRecords]);
                // 调度作业
                BackgroundJob.Enqueue<SendEmailWorker>(a => a.DoWorkAsync());
                #endregion
                return MessageResult.FailureResult(isExpire.Item2);
            }
            var invoiceLineAmount = paPreliminaryApprovalCompleted.PAFinancialVoucherInfos.Sum(a => a.InvoiceLineAmount.HasValue ? decimal.Round(a.InvoiceLineAmount.Value, 2) : 0);
            if (Math.Round(invoiceLineAmount, 2) != Math.Round(pa.PayTotalAmount, 2))
                return MessageResult.FailureResult("财务凭证信息总金额与付款总金额不相等");
            var secondReviewCirculationAmount = await LazyServiceProvider.LazyGetService<ISettingRepository>().FindAsync(SettingsConst.SecondReviewCirculationAmount, "G", null);//复审流转金额
            Guid nextApproverUserId = CurrentUser.Id.Value;
            if (pa.DeliveryMode == DeliveryModes.OnLine && Math.Round(decimal.Parse(secondReviewCirculationAmount?.Value), 2) < Math.Round((pa.PayTotalAmount * (decimal)pa.ExchangeRate), 2))
            {
                //支付人民币金额大于了流转人民币金额 如果配置了复审人则需要复审人审批、否则自己审批  且线上递交的
                var financeReviewConfigUser = await LazyServiceProvider.LazyGetService<IFinanceReviewConfigService>().GetSecondByFirstReviewAsync(CurrentUser.Id.Value);
                if (financeReviewConfigUser != null && financeReviewConfigUser.SecondReviewUserId.HasValue)
                {
                    nextApproverUserId = financeReviewConfigUser.SecondReviewUserId.Value;
                }
            }
            List<UpdateApprovalDto> updateApprovals = new List<UpdateApprovalDto>();
            var updateApproval = new UpdateApprovalDto();
            updateApproval.BusinessFormId = pa.Id.ToString();
            updateApproval.Submitter = CurrentUser.Id.Value;//审批人
            updateApproval.Remark = paPreliminaryApprovalCompleted.Remark;
            updateApproval.OperationStatus = ApprovalOperation.Approved;
            updateApproval.NextApprover = nextApproverUserId;
            updateApprovals.Add(updateApproval);
            var approvalOperation = await approveService.ApprovalOperationAsync(updateApprovals);//审批同意
            if (approvalOperation.Success)
            {
                pa.AkritivCaseID = paPreliminaryApprovalCompleted.AkritivCaseID;
                pa.PaymentTerms = paPreliminaryApprovalCompleted.PaymentTerms;
                pa.InvoiceDescription = paPreliminaryApprovalCompleted.InvoiceDescription.Replace("\t", "").Replace("\n", "");
                pa.ReceivedDocumentsDate = paPreliminaryApprovalCompleted.ReceivedDocumentsDate;
                pa.EstimatedPaymentDate = paPreliminaryApprovalCompleted.EstimatedPaymentDate;
                pa.Status = PurPAApplicationStatus.FinancialReview;
                pa.ApprovedUserId = updateApproval.NextApprover;//财务复审人
                pa.TaxStampLegal = paPreliminaryApprovalCompleted.TaxStampLegal;//根据前端传入更新税票是否合法
                await paRepository.UpdateAsync(pa);//更新PA

                ///处理财务凭证信息数据
                await paVoucherRepository.DeleteDirectAsync(a => a.PAId == pa.Id);
                var financialVoucherInfos = ObjectMapper.Map<List<PAFinancialVoucherInfoDto>, List<PurPAFinancialVoucherInfo>>(paPreliminaryApprovalCompleted.PAFinancialVoucherInfos);
                financialVoucherInfos.ForEach(a =>
                {
                    a.PAId = pa.Id;
                    a.User = CurrentUser.UserName;
                });
                await paVoucherRepository.InsertManyAsync(financialVoucherInfos);
            }
            return approvalOperation;
        }
        /// <summary>
        /// 上传财务凭证信息时补全其他可填字段
        /// </summary>
        /// <param name="paFinancialVoucherInfos"></param>
        /// <param name="paId"></param>
        /// <returns></returns>
        public async Task<List<PAFinancialVoucherInfoDto>> GetPAFinancialVoucherInfoAsync(List<PAFinancialVoucherInfoDto> paFinancialVoucherInfos, Guid paId)
        {
            var result = new PAApplicicationFinancialVoucherDto();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var queryablePR = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var queryPrDetails = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var queryPa = await LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>().GetQueryableAsync();
            var queryPaInvoice = await LazyServiceProvider.LazyGetService<IPurPAApplicationDetailRepository>().GetQueryableAsync();
            var queryPaVoucherInfo = await LazyServiceProvider.LazyGetService<IPurPAFinancialVoucherInfoRepository>().GetQueryableAsync();
            var avtRepository = await LazyServiceProvider.LazyGetService<IBpcsAvtRepository>().GetQueryableAsync();
            var queryBpcsAvm = await LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync();
            var pa = queryPa.Where(a => a.Id == paId).FirstOrDefault();
            if (pa == null)
                return null;
            var paDetails = queryPaInvoice.Where(a => a.PurPAApplicationId == pa.Id).ToList();
            var prd = queryPrDetails.Where(a => a.Id == paDetails.First().PRDetailId).FirstOrDefault();//选的PR供应商都相同
            var bpcsVendor = queryBpcsAvm.Where(a => a.Id == pa.VendorId).FirstOrDefault();

            var (invoiceDate, invoiceDateTime) = await GetInvoiceDate(pa, paDetails);
            foreach (var item in paFinancialVoucherInfos)
            {
                item.VendorName = prd.VendorCode;
                item.InvoiceReference = pa.ApplicationCode.Replace("A", "");
                item.InvoiceDate = invoiceDate;
                item.InvoiceDescription = !string.IsNullOrEmpty(pa.InvoiceDescription) ? pa.InvoiceDescription : (PAExpenseType.LectureFee.Equals(pa.ExpenseType) ? $"{pa.ExpenseType.GetDescription()}{pa.ApplicationCode.Replace("A", "")}" : "");
                item.InvoiceReceiptReference = pa.ApplicationCode.Replace("A", "");
                item.Bank = bpcsVendor?.Vmbank;
                item.InvoiceReceiptDate = pa.AcceptedTime?.ToString("yyyyMMdd");
                item.CurrencyCode = pa.Currency;
                item.RecognitionRate = (decimal)pa.ExchangeRate;
            }
            return paFinancialVoucherInfos;
        }

        /// <summary>
        /// 退回申请人、退回补件、退回原件
        /// </summary>
        /// <param name="pAReturnReason"></param>
        /// <returns></returns>
        public async Task<MessageResult> PAApplicationWithdrawAsync(PAInsertReturnReasonInfoDto pAReturnReason)
        {
            var approveService = LazyServiceProvider.LazyGetService<IApproveService>();
            var paRepository = LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>();
            var queryPa = await paRepository.GetQueryableAsync();
            var paReturnReasonInfoRepository = LazyServiceProvider.LazyGetService<IPurPAReturnReasonInfoRepository>();
            var paReturnReasonDetailRepository = LazyServiceProvider.LazyGetService<IPurPAReturnReasonDetailRepository>();
            if (pAReturnReason.ReturnReasonDetails == null || !pAReturnReason.ReturnReasonDetails.Any())
                return MessageResult.FailureResult("请填写退回类别");
            var pa = queryPa.Where(a => a.Id == pAReturnReason.PurPAApplicationId).FirstOrDefault();
            if (pa == null)
                return MessageResult.FailureResult("未查询到付款申请信息");
            Guid worktaskId = Guid.Empty;
            string operationType = "";
            bool isDeleteFinInfo = true;
            switch (pAReturnReason.Node)
            {
                case WithdrawNodes.Applicant:
                    //财务初审：退回申请人时填写退回理由，退回后给申请人发送站内信及消息提醒邮件，申请人可重新发起审批
                    //财务复审：退回申请人时填写退回理由，退回后给申请人发送站内信及消息提醒邮件，申请人可重新发起审批
                    var updateApproval = new UpdateApprovalDto();
                    updateApproval.BusinessFormId = pa.Id.ToString();
                    updateApproval.Submitter = CurrentUser.Id.Value;//提交人
                    updateApproval.Remark = pAReturnReason.Remark;
                    updateApproval.OperationStatus = ApprovalOperation.ReturnToApplicant;
                    var approvalOperation = await approveService.ApprovalOperationAsync(updateApproval);
                    if (approvalOperation.Success)
                    {
                        worktaskId = (Guid)approvalOperation.Data;
                        pa.SendBackType = WithdrawNodes.Applicant;//回调时使用
                        await paRepository.UpdateAsync(pa);
                    }
                    //operationType = NotifyApplicantOperationType.THSQR;
                    break;
                case WithdrawNodes.AdditionalFile:
                    PurPAApplicationStatus[] paStatus = [PurPAApplicationStatus.FinancialReview, PurPAApplicationStatus.BudgetManagerApproval];
                    if (paStatus.Contains(pa.Status))
                    {
                        //财务复审:退回初审补件时填写退回理由，退回后给财务初审人员发生站内信及消息提醒邮件，财务初审人员需要重新进行审批
                        var additionalFileApproval = new UpdateApprovalDto();
                        additionalFileApproval.BusinessFormId = pa.Id.ToString();
                        additionalFileApproval.Submitter = CurrentUser.Id.Value;//提交人
                        additionalFileApproval.Remark = pAReturnReason.Remark;
                        additionalFileApproval.OperationStatus = ApprovalOperation.ReturnToPreliminary;
                        additionalFileApproval.Step = "100";//PP端财务初审固定值  此处指定步骤的退回流程没有结束，不会回调、所以直接处理后续业务
                        var additionalFileApprovalOperation = await approveService.ApprovalOperationAsync(additionalFileApproval);
                        if (additionalFileApprovalOperation.Success)
                        {
                            worktaskId = (Guid)additionalFileApprovalOperation.Data;
                            pa.Status = PurPAApplicationStatus.FinancialPreliminaryReview;//退回到财务初审
                            await paRepository.UpdateAsync(pa);
                        }
                        operationType = NotifyApplicantOperationType.THCSBJ;
                    }
                    else
                    {
                        //财务初审:退回补件时选择退回意见模板，退回后，PA单推送退回清单给到OM会议系统，在OM会议系统进行补录后重新进行财务初审
                        var approval = new AddApprovalRecordDto()
                        {
                            FormId = pa.Id,
                            ApprovalId = CurrentUser.Id.Value,//提交人,
                            Status = ApprovalOperation.ReturnToPatch,
                            Remark = pAReturnReason.Remark,
                            ApprovalTime = DateTime.Now,
                            WorkStep = "退回补件",
                            Name = "退回补件"
                        };
                        //不影响流程 只修改审批历史记录 财务初审
                        var task = await approveService.AddApprovalRecordAsync(approval);//创建退回补件审批历史记录（假的审批记录—不走真正流程-为审批历史新增可查询记录）
                        worktaskId = task.Item2 ?? Guid.Empty;
                        operationType = NotifyApplicantOperationType.THBJ;
                        isDeleteFinInfo = false;
                    }
                    break;
                case WithdrawNodes.OriginalFile:
                    //财务初审：退回原件时填写退回理由，退回后给单据接收人发送站内信及消息提醒邮件，单据接收人需要重新接收单据
                    //财务复审：退回原件时填写退回理由，退回后给单据接收人发送站内信及消息提醒邮件，单据接收人需要重新接收单据
                    var originalFileApproval = new UpdateApprovalDto();
                    originalFileApproval.BusinessFormId = pa.Id.ToString();
                    originalFileApproval.Submitter = CurrentUser.Id.Value;//提交人
                    originalFileApproval.Remark = pAReturnReason.Remark;
                    originalFileApproval.OperationStatus = ApprovalOperation.ReturnOriginal;
                    var originalFileApprovalOperation = await approveService.ApprovalOperationAsync(originalFileApproval);
                    if (originalFileApprovalOperation.Success)
                    {
                        worktaskId = (Guid)originalFileApprovalOperation.Data;
                        pa.SendBackType = WithdrawNodes.OriginalFile;
                        await paRepository.UpdateAsync(pa);
                    }
                    operationType = NotifyApplicantOperationType.THYJ;
                    break;
            }
            var paReturnReasonDetails = new List<PurPAReturnReasonDetail>();
            foreach (var item in pAReturnReason.ReturnReasonDetails)
            {
                if (!(item.List != null && item.List.Any()))
                    continue;

                var paReturnReasonInfo = new PurPAReturnReasonInfo()
                {
                    WorkFlowTaskId = worktaskId,
                    PurPAApplicationId = pa.Id,
                    Node = pAReturnReason.Node,
                    Type = item.Value,
                    Remark = pAReturnReason.Remark,
                };
                var paReturnReason = await paReturnReasonInfoRepository.InsertAsync(paReturnReasonInfo);
                foreach (var detail in item.List)
                {
                    paReturnReasonDetails.Add(new PurPAReturnReasonDetail
                    {
                        ReturnReasonId = paReturnReason.Id,
                        MainReason = detail.MainReason,
                        SubReason = detail.SubReason,
                        IsSolved = detail.IsSolved,
                    });
                }
            }
            if (paReturnReasonDetails.Any())
                await paReturnReasonDetailRepository.InsertManyAsync(paReturnReasonDetails);
            if (!string.IsNullOrWhiteSpace(operationType))
                await SendEmailReturnNoticeToApplicantAsync(pa, operationType, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            if (isDeleteFinInfo)
                await LazyServiceProvider.LazyGetService<IPurPAFinancialVoucherInfoRepository>().DeleteDirectAsync(a => a.PAId == pa.Id);//退回 删除PA财务凭证信息
            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 获取退回模板
        /// </summary>
        /// <returns></returns>
        public async Task<List<ReturnReasonTemplateDto>> GetWithdrawOpinionInfoAsync()
        {
            var result = new List<ReturnReasonTemplateDto>();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var paReturnReasons = await dataverseService.GetPaReturnReasonAsync();
            var returnSubReason = await dataverseService.GetReturnSubReasonAsync();
            var returnTypes = EnumUtil.GetEnumIdValues<ReturnType>();
            foreach (var item in returnTypes)
            {
                var paReturnReason = paReturnReasons.Where(a => a.ReturnType == (ReturnType)item.Key).ToList();
                ReturnReasonTemplateDto ReturnReasonTemplate = new ReturnReasonTemplateDto();
                ReturnReasonTemplate.Template = item;
                List<ReturnReasonMain> returnReasonMains = new List<ReturnReasonMain>();
                foreach (var paReason in paReturnReason)
                {
                    var reasonSubs = returnSubReason.Where(a => a.BelongMainReasonId == paReason.Id).ToList();
                    ReturnReasonMain returnReason = new ReturnReasonMain();
                    returnReason.Reason = paReason.MainReason;
                    returnReason.ReturnReasonSub = reasonSubs.Select(x => new ReturnReasonSub { Reason = x.ReturnSubreason }).ToList();
                    returnReasonMains.Add(returnReason);
                }
                ReturnReasonTemplate.Reason = returnReasonMains.OrderBy(a =>
                {
                    // 使用正则表达式匹配前缀和数字
                    var match = Regex.Match(a.Reason, @"^([ABCDE]?)(\d*)");
                    if (match.Success)
                    {
                        var prefix = match.Groups[1].Value;
                        var number = match.Groups[2].Value;

                        // 如果没有前缀，则排在最后
                        if (string.IsNullOrEmpty(prefix))
                        {
                            return (5, 0);
                        }
                        // 根据前缀排序
                        int prefixOrder = prefix switch
                        {
                            "A" => 0,
                            "B" => 1,
                            "C" => 2,
                            "D" => 3,
                            "E" => 4,
                            _ => 5,
                        };

                        // 将数字部分转换为整数，如果没有数字则默认为0
                        int num = string.IsNullOrEmpty(number) ? 0 : int.Parse(number);
                        return (prefixOrder, num);
                    }
                    else
                    {
                        // 如果没有匹配到前缀和数字，则排在最后
                        return (5, 0);
                    }
                }).ThenBy(a => a.Reason) // 如果需要按名称排序，可以在内部再进行一次排序
                .ToList();
                result.Add(ReturnReasonTemplate);
            }
            return result;
        }

        /// <summary>
        /// 获取PA退回意见
        /// </summary>
        /// <param name="paId"></param>
        /// <returns></returns>
        public async Task<List<PAReturnReasonInfoReponseDto>> GetPAReturnReasonByPAIdAsync(Guid paId)
        {
            var queryPAReturnReasonInfo = await LazyServiceProvider.LazyGetService<IPurPAReturnReasonInfoRepository>().GetQueryableAsync();
            var queryReturnReasonDetail = await LazyServiceProvider.LazyGetService<IPurPAReturnReasonDetailRepository>().GetQueryableAsync();

            var returnReasons = queryPAReturnReasonInfo.Where(a => a.PurPAApplicationId == paId).ToList();
            if (!returnReasons.Any())
                return new List<PAReturnReasonInfoReponseDto>();
            var query = queryReturnReasonDetail.GroupJoin(queryPAReturnReasonInfo.Where(x => x.PurPAApplicationId == paId), a => a.ReturnReasonId, b => b.Id, (a, b) => new { detail = a, Info = b })
                        .SelectMany(a => a.Info.DefaultIfEmpty(), (a, b) => new { a.detail, info = b })
                        .Where(a => a.info.PurPAApplicationId == paId);

            var returnReasonInfos = query.ToList()
                        .Select(a => new PAReturnReasonInfoReponseDto
                        {
                            Id = a.detail.Id,
                            ReturnReasonId = a.detail.ReturnReasonId,
                            Node = a.info.Node,
                            Type = a.info.Type,
                            Remark = a.info.Remark,
                            TypeName = a.info.Type.GetDescription(),
                            MainReason = a.detail.MainReason,
                            SubReason = a.detail.SubReason,
                            IsSolved = a.detail.IsSolved,
                            WorkFlowTaskId = a.info.WorkFlowTaskId,
                        }).ToList();
            return returnReasonInfos;
        }

        #region 财务复审完成
        /// <summary>
        /// 财务复审完成 同意 单个（后续可能不用这个接口）
        /// </summary>
        /// <param name="approval"></param>
        /// <returns></returns>
        public async Task<MessageResult> FinancialReviewApprovalAsync(FinancialReviewApprovalDto approval)
        {
            var approveService = LazyServiceProvider.LazyGetService<IApproveService>();
            var paRepository = LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>();
            var queryPa = await paRepository.GetQueryableAsync();
            //var queryBpcsGcc = await LazyServiceProvider.LazyGetService<IBpcsGccRepository>().GetQueryableAsync();
            var pa = queryPa.Where(a => a.Id == approval.PaId).FirstOrDefault();
            var isExpire = await VendorIsExpireAsync(new List<(string, Guid)> { (pa.ApplicationCode, pa.VendorId.Value) }, VendorExpireType.APPROVE);
            if (!isExpire.Item1)
            {
                #region 供应商失效通知申请人
                var userQuery = (await LazyServiceProvider.LazyGetService<IRepository<IdentityUser, Guid>>().GetQueryableAsync()).AsNoTracking();
                var user = userQuery.FirstOrDefault(a => a.Id == pa.ApplyUserId);
                var sendEmaillRecords = new InsertSendEmaillRecordDto
                {
                    EmailAddress = user.Email,
                    Subject = "[NexBPM消息中心]您发起的付款申请{ApplicationCode}中供应商已失效，请及时激活供应商。",
                    Content = JsonSerializer.Serialize(new VendorExpireNoticeToApplicanDto
                    {
                        UserName = user.Name,
                        ApplicationCode = pa.ApplicationCode,
                        VendorName = pa.VendorName,
                        Company = pa.CompanyName,
                        VendorCode = pa.VendorCode,
                        ApplicationLink = $"{_configuration["SpeakerEmail:WebHost"]?.TrimEnd('/')}/procure/payment/detail/{pa.Id}",
                    }),
                    SourceType = EmailSourceType.VendorExpireNoticeToApplican,
                    Status = SendStatus.Pending,
                    Attempts = 0,
                };
                //记录邮件，并触发邮件发送功能
                var emailService = LazyServiceProvider.LazyGetService<IEmailService>();
                await emailService.InsertSendEmaillRecordAsync([sendEmaillRecords]);
                // 调度作业
                BackgroundJob.Enqueue<SendEmailWorker>(a => a.DoWorkAsync());
                #endregion
                return MessageResult.FailureResult(isExpire.Item2);
            }
            if (pa == null)
                return MessageResult.FailureResult("未查询到相关付款申请");
            PurPAApplicationStatus[] paStatus = [PurPAApplicationStatus.FinancialReview, PurPAApplicationStatus.BudgetManagerApproval];
            if (!paStatus.Contains(pa.Status))
                return MessageResult.FailureResult("当前付款申请单状态不能进行财务复审");
            //var usdGcc = queryBpcsGcc.Where(a => a.Ccfrcr == pa.Currency && a.Cctocr == "RMB").OrderByDescending(a => a.Ccnvdt).FirstOrDefault();
            //var rate = 1M;
            //if (usdGcc != null)
            //{
            //    rate = usdGcc.Ccnvfc.Value;
            //}
            //List<UpdateApprovalDto> updateApprovals = new List<UpdateApprovalDto>();
            var updateApproval = new UpdateApprovalDto
            {
                BusinessFormId = pa.Id.ToString(),
                Submitter = CurrentUser.Id.Value,//审批人
                Remark = approval.Remark,
                OperationStatus = ApprovalOperation.Approved,
                BusinessFormNextData = JsonSerializer.Serialize(new { isBudgetOwner = false })
            };
            //var isSubBudget = false;
            //if (pa.Status == PurPAApplicationStatus.FinancialReview && pa.ExchangeRate < (float)rate)//实际汇率 > PA的汇率
            //{
            //    var prQuery = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            //    var querySubbudget = await LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>().GetQueryableAsync();
            //    var budget = prQuery.Join(querySubbudget, a => a.SubBudgetId, b => b.Id, (a, b) => new { a.Id, b.OwnerId })
            //        .Where(a => a.Id == pa.PRId)
            //        .FirstOrDefault();
            //    updateApproval.NextApprover = budget?.OwnerId;
            //    updateApproval.BusinessFormNextData = JsonSerializer.Serialize(new { isBudgetOwner = true });
            //    pa.Status = PurPAApplicationStatus.BudgetManagerApproval;
            //    isSubBudget = true;
            //}
            //updateApprovals.Add(updateApproval);
            var approvalOperation = await approveService.ApprovalOperationAsync([updateApproval]);//审批同意
            //if (approvalOperation.Success)
            //{
            //    if (isSubBudget)
            //        await paRepository.UpdateAsync(pa);
            //}
            return approvalOperation;
        }

        /// <summary>
        /// 财务复审完成 同意 批量
        /// </summary>
        /// <param name="approval"></param>
        /// <returns></returns>
        public async Task<MessageResult> FinancialBatchReviewApprovalAsync(FinancialBatchReviewApprovalDto approvals)
        {
            var approveService = LazyServiceProvider.LazyGetService<IApproveService>();
            var paRepository = LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>();
            var queryPa = await paRepository.GetQueryableAsync();
            //var queryBpcsGcc = await LazyServiceProvider.LazyGetService<IBpcsGccRepository>().GetQueryableAsync();
            var prQuery = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var querySubbudget = await LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>().GetQueryableAsync();

            var pas = queryPa.Where(a => approvals.PaIds.Contains(a.Id)).ToList();
            //(pa.ApplicationCode, pa.VendorCode, pa.CompanyCode) 
            var isExpire = await VendorIsExpireAsync(pas.Select(a => (a.ApplicationCode, a.VendorId.Value)).ToList(), VendorExpireType.BATCH_APPROVE);
            if (!isExpire.Item1)
            {
                #region 供应商失效通知申请人
                var userQuery = (await LazyServiceProvider.LazyGetService<IRepository<IdentityUser, Guid>>().GetQueryableAsync()).AsNoTracking();
                var users = userQuery.Where(a => pas.Select(p => p.ApplyUserId).Distinct().Contains(a.Id)).ToList();
                foreach (var expire in isExpire.Item3)
                {
                    var expirePa = pas.Where(a => a.ApplicationCode == expire).FirstOrDefault();
                    var user = users.FirstOrDefault(a => a.Id == expirePa.ApplyUserId);
                    var sendEmaillRecords = new InsertSendEmaillRecordDto
                    {
                        EmailAddress = user.Email,
                        Subject = "[NexBPM消息中心]您发起的付款申请{ApplicationCode}中供应商已失效，请及时激活供应商。",
                        Content = JsonSerializer.Serialize(new VendorExpireNoticeToApplicanDto
                        {
                            UserName = user.Name,
                            ApplicationCode = expirePa.ApplicationCode,
                            VendorName = expirePa.VendorName,
                            Company = expirePa.CompanyName,
                            VendorCode = expirePa.VendorCode,
                            ApplicationLink = $"{_configuration["SpeakerEmail:WebHost"]?.TrimEnd('/')}/procure/payment/detail/{expirePa.Id}",
                        }),
                        SourceType = EmailSourceType.VendorExpireNoticeToApplican,
                        Status = SendStatus.Pending,
                        Attempts = 0,
                    };
                    //记录邮件，并触发邮件发送功能
                    var emailService = LazyServiceProvider.LazyGetService<IEmailService>();
                    await emailService.InsertSendEmaillRecordAsync([sendEmaillRecords]);
                }
                // 调度作业
                BackgroundJob.Enqueue<SendEmailWorker>(a => a.DoWorkAsync());
                #endregion
                return MessageResult.FailureResult(isExpire.Item2);
            }

            if (!pas.Any())
                return MessageResult.FailureResult("未查询到相关付款申请");
            PurPAApplicationStatus[] paStatus = [PurPAApplicationStatus.FinancialReview, PurPAApplicationStatus.BudgetManagerApproval];
            foreach (var item in pas)
            {
                if (!paStatus.Contains(item.Status))
                {
                    return MessageResult.FailureResult("包含不可完成财务复审的申请单");
                }
            }

            //var usdGccs = queryBpcsGcc.Where(a => pas.Select(x => x.Currency).Distinct().Contains(a.Ccfrcr) && a.Cctocr == "RMB").OrderByDescending(a => a.Ccnvdt).ToList();
            List<UpdateApprovalDto> updateApprovals = [];
            //var updatePas = new List<PurPAApplication>();
            foreach (var pa in pas)
            {
                //var usdGcc = usdGccs.Where(a => a.Ccfrcr == pa.Currency && a.Cctocr == "RMB").OrderByDescending(a => a.Ccnvdt).FirstOrDefault();
                //var rate = 1M;
                //if (usdGcc != null)
                //{
                //    rate = usdGcc.Ccnvfc.Value;
                //}
                var updateApproval = new UpdateApprovalDto
                {
                    BusinessFormId = pa.Id.ToString(),
                    Submitter = CurrentUser.Id.Value,//审批人
                    Remark = approvals.Remark,
                    OperationStatus = ApprovalOperation.Approved,
                    BusinessFormNextData = JsonSerializer.Serialize(new { isBudgetOwner = false })
                };
                //if (pa.Status == PurPAApplicationStatus.FinancialReview && pa.ExchangeRate < (float)rate)//实际汇率 > PA的汇率
                //{
                //    var budget = prQuery.Join(querySubbudget, a => a.SubBudgetId, b => b.Id, (a, b) => new { a.Id, b.OwnerId })
                //        .Where(a => a.Id == pa.PRId)
                //        .FirstOrDefault();
                //    if (budget == null || budget?.OwnerId == null)
                //        return MessageResult.FailureResult("预算负责人不存在");
                //    updateApproval.NextApprover = budget?.OwnerId;
                //    updateApproval.BusinessFormNextData = JsonSerializer.Serialize(new { isBudgetOwner = true });
                //    pa.Status = PurPAApplicationStatus.BudgetManagerApproval;
                //    updatePas.Add(pa);
                //}
                updateApprovals.Add(updateApproval);
            }
            var approvalOperation = await approveService.ApprovalOperationAsync(updateApprovals);//审批同意
            //if (approvalOperation.Success)
            //{
            //    if (updatePas.Any())
            //        await paRepository.UpdateManyAsync(updatePas);
            //}
            return approvalOperation;
        }
        #endregion


        /// <summary>
        /// 更新PA退回意见
        /// </summary>
        /// <param name="pAReturnReason"></param>
        /// <returns></returns>
        public async Task<MessageResult> UpdatePAReturnReasonByPAIdAsync(List<PAUpdateReturnReasonInfoDto> pAReturnReason)
        {
            var approvalRecordReportRepostory = LazyServiceProvider.LazyGetService<IApprovalRecordReportRepostory>();
            var paReturnReasonDetailRepository = LazyServiceProvider.LazyGetService<IPurPAReturnReasonDetailRepository>();
            var queryReturnReasonDetail = await paReturnReasonDetailRepository.GetQueryableAsync();
            if (pAReturnReason == null || !pAReturnReason.Any())
                return MessageResult.FailureResult("无可更新数据");
            var ids = pAReturnReason.Select(a => a.Id).ToList();
            var returnReasons = queryReturnReasonDetail.Where(a => ids.Contains(a.Id)).ToList();
            returnReasons.ForEach(a =>
            {
                a.IsSolved = pAReturnReason.Where(x => x.Id == a.Id).FirstOrDefault().IsSolved;
            });
            await paReturnReasonDetailRepository.UpdateManyAsync(returnReasons);
            //处理审批记录报表 PA 退回原因 是否解决
            var paApprovalRecordReports = await approvalRecordReportRepostory.GetListAsync(a => a.ApplicationType == ApplicationDocumentType.Payment && a.ReturnDetailId.HasValue && ids.Contains(a.ReturnDetailId.Value));
            paApprovalRecordReports.ForEach(a =>
            {
                var item = returnReasons.FirstOrDefault(x => x.Id == a.ReturnDetailId);
                if (item != null)
                    a.IsSolved = item.IsSolved;
            });
            await approvalRecordReportRepostory.UpdateManyAsync(paApprovalRecordReports);
            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// PA最后一次付款后预算返还
        /// AP =》PA(返还): 最后一次付款复审通过后退回每行[PO不含税金额-已付款不含税金额+GR产生的预算扣除金额]
        /// AR =》PA(返还): 最后一次付款复审通过后，退回每行[PR申请金额-已付款含税金额]
        /// </summary>
        /// <param name="paId"></param>
        /// <param name="rate">实时汇率</param>
        /// <returns></returns>
        public async Task PABudgetRefundAsync(Guid paId, decimal rate)
        {
            var paQuery = await LazyServiceProvider.GetService<IPurPAApplicationRepository>().GetQueryableAsync();
            var pa = paQuery.Where(a => a.Id == paId).FirstOrDefault();
            if (!pa.IsLastPayment)
                return;
            var prQuery = await LazyServiceProvider.GetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var prDetailRepository = LazyServiceProvider.GetService<IPurPRApplicationDetailRepository>();
            var prDetailQuery = await prDetailRepository.GetQueryableAsync();
            var paInvoiceQuery = await LazyServiceProvider.GetService<IPurPAApplicationDetailRepository>().GetQueryableAsync();
            var poQuery = await LazyServiceProvider.GetService<IPurPOApplicationRepository>().GetQueryableAsync();
            var poQueryDetail = await LazyServiceProvider.GetService<IPurPOApplicationDetailsRepository>().GetQueryableAsync();
            var grQuery = await LazyServiceProvider.GetService<IPurGRApplicationRepository>().GetQueryableAsync();
            var grQueryDetailHistory = await LazyServiceProvider.GetService<IPurGRApplicationDetailHistoryRepository>().GetQueryableAsync();

            var pas = paQuery.GroupJoin(paInvoiceQuery, a => a.Id, b => b.PurPAApplicationId, (a, b) => new { pa = a, pads = b })
                .SelectMany(a => a.pads.DefaultIfEmpty(), (a, b) => new { a.pa, pad = b })
                .Where(a => a.pa.Status != PurPAApplicationStatus.Void && a.pa.GRId == pa.GRId)//排除作废PA, 根据PA中GR 获取所有PA明细
                .ToList();
            var paInvoices = pas.Select(a => a.pad).ToList();//所有PA明细对应的付款

            var prDetails = prDetailQuery.Where(a => paInvoices.Select(b => b.PRDetailId).Contains(a.Id)).ToList();//所有付款对应的PR明细行
            var prDetailIds = prDetails.Select(a => a.Id).ToList();
            var pr = prQuery.Where(a => a.Id == pa.PRId).FirstOrDefault();
            if (PayMethods.AR == pa.PayMethod)
            {
                //AR =》PA(返还): 最后一次付款复审通过后，退回每行[PR申请金额-已付款含税金额]
                var useBudgetRequest = new ReturnBudgetRequestDto
                {
                    PrId = pr.Id,
                    SubbudgetId = pr.SubBudgetId.Value,
                    Items = prDetails.Select(a =>
                    {
                        //PR申请金额
                        var prTotalAmount = a.TotalAmount > 0 ? a.TotalAmount.Value : 0M;
                        //已付款含税金额
                        var paTotalAmount = paInvoices.Where(x => x.PRDetailId == a.Id).Sum(x => x.PaymentAmount);
                        return new ReturnInfo
                        {
                            PdRowNo = a.RowNo,
                            ReturnAmount = (prTotalAmount * (decimal)pa.ExchangeRate) - (paTotalAmount * rate),
                            ReturnSourceId = pa.Id,
                            ReturnSourceCode = pa.ApplicationCode
                        };
                    })
                };
                if (useBudgetRequest.Items.Any(a => a.ReturnAmount > 0))
                {
                    useBudgetRequest.Items = useBudgetRequest.Items.Where(a => a.ReturnAmount != 0);//大于0才说明有返还\小于0 可能是由于实时汇率大于了之前选择的汇率
                    await LazyServiceProvider.LazyGetService<ISubbudgetService>().ReturnSubbudgetAsync(useBudgetRequest);
                }
            }
            else if (PayMethods.AP == pa.PayMethod)
            {
                PurOrderStatus[] poStatus = [PurOrderStatus.Invalid, PurOrderStatus.Rejected, PurOrderStatus.Draft];
                var poDetails = poQueryDetail.Join(poQuery.Select(a => new { a.Id, a.Status }), a => a.POApplicationId, b => b.Id, (a, b) => new { pod = a, po = b })
                           .Where(a => !poStatus.Contains(a.po.Status))
                           .Where(a => a.po.Id == pa.POId && prDetailIds.Contains(a.pod.PRDetailId.Value))
                           .Select(a => a.pod).ToList();
                PurGRApplicationStatus[] grStatus = [PurGRApplicationStatus.Terminationed];
                var grDetails = grQueryDetailHistory.Join(grQuery.Select(a => new { a.Id, a.Status }), a => a.GRApplicationId, b => b.Id, (a, b) => new { grdh = a, gr = b })
                    .Where(a => a.gr.Id == pa.GRId && !grStatus.Contains(a.gr.Status))
                    .ToList();

                //PA(返还): 最后一次付款复审通过后，退回每行[PO不含税金额-已付款不含税金额]
                var useBudgetRequest = new ReturnBudgetRequestDto
                {
                    PrId = pr.Id,
                    SubbudgetId = pr.SubBudgetId.Value,
                    Items = prDetails.Select(a =>
                    {
                        //PO不含税金额
                        var poNoTaxAmount = poDetails.Where(x => x.PRDetailId == a.Id).Sum(x => x.TotalAmountNoTax);
                        //已付款不含税金额
                        var paNoTaxAmount = paInvoices.Where(x => x.PRDetailId == a.Id).Sum(x =>
                        {
                            decimal poTaxRate = 0M;
                            decimal paTaxRate = 0M;
                            var podTaxRate = poDetails.FirstOrDefault(pod => pod.Id == x.PODetailId)?.TaxRate;//AP类型的PA一定会有PO
                            if (!InvoiceType.GiftIncrease.Equals(x.InvoiceType))
                            {
                                decimal.TryParse(podTaxRate, out poTaxRate);
                                decimal.TryParse(x.TaxRate, out paTaxRate);
                            }
                            return x.PaymentAmount / (1 + (poTaxRate > paTaxRate ? poTaxRate : paTaxRate));
                        });
                        //GR产生的预算扣除金额
                        var grBudgetUsage = 0M;
                        //GR不含税金额
                        var grNoTaxAmount = grDetails.Where(h => h.grdh?.PRDetailId == a.Id).Sum(h =>
                        {
                            var taxRate = 0M;
                            var pod = poDetails.FirstOrDefault(x => x.Id == h.grdh?.PODetailId);
                            if (!InvoiceType.GiftIncrease.Equals(pod.InvoiceType))
                            {
                                decimal.TryParse(pod?.TaxRate, out taxRate);
                            }
                            return h.grdh?.ReceivedAmount > 0 ? (h.grdh?.ReceivedAmount / (1 + taxRate)) : 0M;
                        });
                        if (grNoTaxAmount > 0 && (grNoTaxAmount - poNoTaxAmount) > 0)
                            grBudgetUsage = grNoTaxAmount.Value - poNoTaxAmount;
                        return new ReturnInfo
                        {
                            PdRowNo = a.RowNo,
                            ReturnAmount = (poNoTaxAmount * (decimal)pa.ExchangeRate) - (paNoTaxAmount * rate) + (grBudgetUsage * (decimal)pa.ExchangeRate),//PO不含税金额-已付款不含税金额+GR产生的预算扣除金额
                            ReturnSourceId = pa.Id,
                            ReturnSourceCode = pa.ApplicationCode
                        };
                    })
                };
                if (useBudgetRequest.Items.Any(a => a.ReturnAmount != 0))
                {
                    useBudgetRequest.Items = useBudgetRequest.Items.Where(a => a.ReturnAmount != 0);//大于0才说明有返还,小于0 可能是由于实时汇率大于了之前选择的汇率
                    await LazyServiceProvider.LazyGetService<ISubbudgetService>().ReturnSubbudgetAsync(useBudgetRequest);
                }
            }

            //PA最后一次付款，复审通过后，PA进行预算返回是把此PA对应GR明细的PRDetails进行了预算返回，此GR如果有其他明细则对应的PRDetails没有进行预算返回，故采用终止收货的逻辑对此GR的其他明细的PRDetails进行终止收货的预算返回
            await LazyServiceProvider.LazyGetService<IPurGRApplicationService>().AbrogationReceiveWithPartGRAsync(pa.Id, pa.ApplicationCode, pa.GRId, prDetailIds, pr.SubBudgetId.Value);
        }

        /// <summary>
        /// PA 撤回 （财务分单）
        /// </summary>
        /// <param name="paRevoke"></param>
        /// <returns></returns>
        public async Task<MessageResult> PARevokeApplicationAsync(PARevokeDto paRevoke)
        {
            var paApplication = LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>();
            var approveService = LazyServiceProvider.LazyGetService<IApproveService>();
            var query = await paApplication.GetQueryableAsync();
            var pa = query.Where(x => paRevoke.Id == x.Id).FirstOrDefault();
            if (pa.Status != PurPAApplicationStatus.DocumentReceipt)
                return MessageResult.FailureResult("当前状态不能撤回");
            if (pa.UseBudgetTime.HasValue)
                await LazyServiceProvider.LazyGetService<IPurGRApplicationService>().ReturnSubbudgetAsync(pa.PRId, pa.Id, pa.UseBudgetTime.Value);//预算返还
            pa.Status = PurPAApplicationStatus.FillIn;
            pa.TaskType = null;//移除付款审批任务
            pa.UseBudgetTime = null;//清除PA提交时使用预算时间，待后续提交使用
            await paApplication.UpdateAsync(pa, true);
            var approval = new AddApprovalRecordDto()//撤回
            {
                FormId = pa.Id,
                ApprovalId = CurrentUser.Id.Value,
                OriginalApprovalId = pa.ApplyUserId,
                Status = ApprovalOperation.Recall,
                Remark = paRevoke.Remark,
                ApprovalTime = DateTime.Now,
                WorkStep = "提交人操作",
                Name = "提交人操作"
            };
            //不影响流程 只修改审批历史记录
            var task = await approveService.AddApprovalRecordAsync(approval);//创建退回补件审批历史记录（假的审批记录—不走真正流程-为审批历史新增可查询记录）
            return MessageResult.SuccessResult();
        }

        #region  私有方法
        /// <summary>
        /// 财务凭证获取发票时间
        /// </summary>
        /// <param name="pa"></param>
        /// <param name="grHistoryIds"></param>
        /// <returns></returns>
        private async Task<(string, DateTime?)> GetInvoiceDate(PurPAApplication pa, List<PurPAApplicationDetail> paDetails)
        {
            var queryBpcsAvm = (await LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync()).AsNoTracking();
            var queryFinancial = await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync();
            var queryVendor = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();
            //常规讲者
            var vendor = queryBpcsAvm.Where(s => s.Id == pa.VendorId)
                .Join(queryFinancial, a => a.FinaId, a => a.Id, (a, b) => new { Avm = a, Fin = b })
                .Join(queryVendor.Where(a => a.VendorType == VendorTypes.HCIAndOtherInstitutionsAR), a => a.Fin.VendorId, a => a.Id, (a, b) => new { a.Avm, Vendor = b })
                .FirstOrDefault();
            //讲者=> 1、非外币：bpcsAVM里NH类型 2、外币：bpcsAVM里是NT，Vendors表对应的VendorType = 3
            if (vendor?.Avm?.Vtype == "NH" || (vendor?.Avm?.Vtype == "NT" && vendor?.Vendor?.VendorType == VendorTypes.HCIAndOtherInstitutionsAR))
            {
                //HCI
                //1）若对应后补发票——单据接收日期
                if (pa.IsBackupInvoice == true)
                {
                    return (pa.AcceptedTime?.ToString("yyyyMMdd"), pa.AcceptedTime);
                }
                //2）有发票——PA单据中的最新发票日期     下面第三点
            }
            else
            {
                //非HCI
                //1）若对应为无发票——最新单据接收日期
                if (paDetails.Any(a => a.InvoiceType == InvoiceType.NonInvoice))
                {
                    return (pa.AcceptedTime?.ToString("yyyyMMdd"), pa.AcceptedTime);
                }
                //2）若对应后补发票——收货单中签收日期(如多行签收日期不同则取最新日期)
                if (pa.IsBackupInvoice == true)
                {
                    var grHistoryIds = paDetails.Where(a => a.GRHistoryId.HasValue).Select(x => x.GRHistoryId.Value).ToList();
                    var queryableGrdHistory = (await LazyServiceProvider.LazyGetService<IPurGRApplicationDetailHistoryRepository>().GetQueryableAsync()).AsNoTracking();
                    var grdh = queryableGrdHistory.Where(a => grHistoryIds.Contains(a.Id)).OrderByDescending(a => a.SigningDate).FirstOrDefault();
                    return (grdh?.SigningDate.ToString("yyyyMMdd"), grdh?.SigningDate);
                }
            }
            //3）其他情况——PA单据中的最新发票日期  没有发票则返回null
            var paInvoiceQuery = (await LazyServiceProvider.LazyGetService<IPurPAApplicationInvoiceRepository>().GetQueryableAsync()).AsNoTracking();
            var paInvoice = paInvoiceQuery.Where(a => a.PurPAApplicationId == pa.Id).OrderByDescending(a => a.InvoiceDate).FirstOrDefault();
            return (paInvoice?.InvoiceDate.ToString("yyyyMMdd"), paInvoice?.InvoiceDate);
        }

        /// <summary>
        /// 拦截 退回金额上限 校验
        /// </summary>
        /// <param name="paDetails"></param>
        /// <param name="paId"></param>
        /// <returns></returns>
        private async Task<(bool, string)> VerifyInterceptAsync(List<PurPAApplicationDetailDto> paDetails, Guid paId)
        {
            var queryIntercept = await LazyServiceProvider.GetService<IOECInterceptRepository>().GetQueryableAsync();
            var queryInterceptOperateHistory = await LazyServiceProvider.GetService<IOECInterceptOperateHistoryRepository>().GetQueryableAsync();
            var queryPADetail = await LazyServiceProvider.GetService<IPurPAApplicationDetailRepository>().GetQueryableAsync();
            var queryPA = await LazyServiceProvider.GetService<IPurPAApplicationRepository>().GetQueryableAsync();
            var prDetailId = paDetails.Select(a => a.PRDetailId).Distinct().ToList();//该PA对应的PRDetail ID
            var interceptHistorys = queryIntercept.GroupJoin(queryInterceptOperateHistory, a => a.Id, b => b.InterceptId, (a, b) => new { intercept = a, operates = b })
                .SelectMany(a => a.operates.DefaultIfEmpty(), (a, b) => new { a.intercept, operate = b })
                .Where(a => a.operate != null && a.intercept.InterceptTime <= a.operate.CreationTime)
                .Where(a => prDetailId.Contains(a.intercept.PRDetailId))
                .ToList();//排除操作历史在拦截时间之前的数据

            if (!interceptHistorys.Any())
                return (true, "无拦截相关信息");
            var intercepts = interceptHistorys.Select(a => a.intercept).GroupBy(a => a.Id).Select(a => a.FirstOrDefault()).ToList();//所有PA对应的拦截信息
            var operateHistorys = interceptHistorys.Select(a => a.operate).ToList();
            var sendBackedIntercepts = intercepts.Where(a => a.InterceptStatus == InterceptStatus.SendBacked).ToList();//只处理退回信息
            if (!sendBackedIntercepts.Any())
                return (true, "不存在退回拦截信息");
            var pas = queryPA.GroupJoin(queryPADetail, a => a.Id, b => b.PurPAApplicationId, (a, b) => new { pa = a, pads = b })
                .SelectMany(a => a.pads.DefaultIfEmpty(), (a, b) => new { a.pa, pad = b })
                .Where(a => sendBackedIntercepts.Select(x => x.PRDetailId).ToList().Contains(a.pad.PRDetailId) && a.pa.Status != PurPAApplicationStatus.Void)//排除作废PA单
                .ToList();//首次提交也会有一条数据
            var exchangeRate = pas.FirstOrDefault().pa.ExchangeRate;//PR 相关PA汇率都相同
            var groupPRDetails = paDetails.GroupBy(a => a.PRDetailId).Select(a => a.FirstOrDefault()).ToList();//本次收货所含的PR明细ID
            var groups = groupPRDetails.Where(a => intercepts.Where(x => x.InterceptStatus == InterceptStatus.SendBacked).Select(x => x.PRDetailId).Contains(a.PRDetailId)).ToList();
            var paHistorys = pas.Where(a => a.pa.Id != paId).ToList();//pr明细相关的本次付款以外的付款
            if (!paHistorys.Any())//没有历史付款情况
            {
                foreach (var item in groups)
                {
                    var investigate = operateHistorys.Where(a => a.PRDetailId == item.PRDetailId && (a.SolveInterceptType ?? string.Empty).Contains("Investigate") && a.OperateType == InterceptOperateType.SendBack).OrderBy(a => a.SendBackLimitAmount).FirstOrDefault();
                    if (investigate != null)
                    {
                        if ((paDetails.Where(a => a.PRDetailId == item.PRDetailId).Sum(a => a.PaymentAmount) * (decimal)exchangeRate) > investigate.SendBackLimitAmount)//付款金额不能大于拦截退回（调查类型）金额
                            return (false, $"付款金额超上限￥ {Math.Round(investigate.SendBackLimitAmount ?? 0, 2)}元");
                    }
                    else
                    {
                        var oecLimitAmount = operateHistorys.Where(a => a.PRDetailId == item.PRDetailId && a.OperateType == InterceptOperateType.SendBack).OrderBy(a => a.SendBackLimitAmount).FirstOrDefault();
                        if ((paDetails.Where(a => a.PRDetailId == item.PRDetailId).Sum(a => a.PaymentAmount) * (decimal)exchangeRate) > oecLimitAmount.SendBackLimitAmount)//付款金额不能大于拦截退回（OEC类型）金额
                            return (false, $"付款金额超上限￥ {Math.Round(oecLimitAmount.SendBackLimitAmount ?? 0, 2)}元");
                    }
                }
            }
            else
            {
                foreach (var item in groups)
                {
                    var investigate = operateHistorys.Where(a => a.PRDetailId == item.PRDetailId && (a.SolveInterceptType ?? string.Empty).Contains("Investigate") && a.OperateType == InterceptOperateType.SendBack).OrderBy(a => a.SendBackLimitAmount).FirstOrDefault();
                    //需要历史加本次进行比较本身历史已经比拦截多 提示 用户作废本次付款申请
                    if (investigate != null)
                    {
                        var historyPayAmount = paHistorys.Where(a => a.pad.PRDetailId == item.PRDetailId).Sum(a => a.pad.PaymentAmount);
                        if (historyPayAmount > investigate.SendBackLimitAmount)
                            return (false, $"已付款金额大于退回金额上限￥ {Math.Round(investigate.SendBackLimitAmount ?? 0, 2)}元,请作废本次付款申请");
                        var thisPayAmount = paDetails.Where(a => a.PRDetailId == item.PRDetailId).Sum(a => a.PaymentAmount);
                        if (((historyPayAmount + thisPayAmount) * (decimal)exchangeRate) > investigate.SendBackLimitAmount)//付款金额不能大于拦截退回（调查类型）金额
                            return (false, $"付款金额大于退回金额上限￥ {Math.Round(investigate.SendBackLimitAmount ?? 0, 2)}元,请修改付款申请金额");
                    }
                    else
                    {
                        var oecLimitAmount = operateHistorys.Where(a => a.PRDetailId == item.PRDetailId && a.OperateType == InterceptOperateType.SendBack).OrderBy(a => a.SendBackLimitAmount).FirstOrDefault();
                        var historyPayAmount = paHistorys.Where(a => a.pad.PRDetailId == item.PRDetailId).Sum(a => a.pad.PaymentAmount);
                        if (historyPayAmount > oecLimitAmount.SendBackLimitAmount)
                            return (false, $"已付款金额大于退回金额上限￥ {Math.Round(oecLimitAmount.SendBackLimitAmount ?? 0, 2)}元,请作废本次付款申请");
                        var thisPayAmount = paDetails.Where(a => a.PRDetailId == item.PRDetailId).Sum(a => a.PaymentAmount);
                        if (((historyPayAmount + thisPayAmount) * (decimal)exchangeRate) > oecLimitAmount.SendBackLimitAmount)//付款金额不能大于拦截退回（调查类型）金额
                            return (false, $"付款金额大于退回金额上限￥ {Math.Round(oecLimitAmount.SendBackLimitAmount ?? 0, 2)}元,请修改付款申请金额");
                    }
                }
            }
            return (true, "付款金额未超过退回金额上限");
        }

        /// <summary>
        /// PA 退回通知申请人
        /// </summary>
        /// <param name="pa"></param>
        /// <param name="operationType"></param>
        /// <param name="returnTime"></param>
        /// <returns></returns>
        private async Task SendEmailReturnNoticeToApplicantAsync(PurPAApplication pa, string operationType, string returnTime)
        {
            var userQuery = (await LazyServiceProvider.LazyGetService<IRepository<IdentityUser, Guid>>().GetQueryableAsync()).AsNoTracking();

            var userIdsToNotify = new List<Guid>();
            var agentService = LazyServiceProvider.LazyGetService<IAgencyService>();
            var agentRequest = new GetAgentByOriginalOperatorRequestDto { OriginalOperatorId = pa.ApplyUserId, BusinessTypeCategory = ResignationTransfer.TaskFormCategory.PaymentApplication };
            userIdsToNotify.Add(pa.TransfereeId.HasValue ? pa.TransfereeId.Value : pa.ApplyUserId);
            var agent = await agentService.GetAgentByOriginalOperator(agentRequest);
            if (agent.HasValue)
                userIdsToNotify.Add(agent.Value.Key);

            var users = userQuery.Where(a => userIdsToNotify.Distinct().ToHashSet().Contains(a.Id)).ToArray();
            string applicationLink = $"{_configuration["SpeakerEmail:WebHost"]?.TrimEnd('/')}/procure/payment/detail/{pa.Id}";
            if (operationType == NotifyApplicantOperationType.THSQR)
                applicationLink = $"{_configuration["SpeakerEmail:WebHost"]?.TrimEnd('/')}/task/launch";
            var sendEmaillRecords = users.Select(user => new InsertSendEmaillRecordDto
            {
                EmailAddress = user.Email,
                Subject = "[NexBPM消息中心]您申请的付款申请：{ApplicationCode}已被【{OperationType}】。",
                Content = JsonSerializer.Serialize(new ReturnNoticeToApplicantEmailDto
                {
                    ApplicationCode = pa.ApplicationCode,
                    OperationType = operationType,
                    ReturnTime = returnTime,
                    UserName = user.Name,
                    ApplicationLink = applicationLink,
                }),
                SourceType = EmailSourceType.ReturnNoticeToApplicant,
                Status = SendStatus.Pending,
                Attempts = 0,
            });
            //记录邮件，并触发邮件发送功能
            var emailService = LazyServiceProvider.LazyGetService<IEmailService>();
            await emailService.InsertSendEmaillRecordAsync(sendEmaillRecords.ToList());
            // 调度作业
            BackgroundJob.Enqueue<SendEmailWorker>(a => a.DoWorkAsync());
        }
        #endregion
        /// <summary>
        /// 添加审批任务
        /// </summary>
        /// <param name="request"></param>
        /// <param name="firstApprover"></param>
        /// <param name="isTaxRateSmall"></param>
        /// <returns></returns>
        private async Task<bool> CreateWorkflowAsync(PurPAApplication request, string firstApprover = "", bool isTaxRateSmall = false)
        {
            var approvalRecord = LazyServiceProvider.LazyGetService<IInitializeApprovalRecordService>();
            UrgentType urgent = UrgentType.Default;
            if (request.UrgentPayment)
            {
                var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
                var urgentType = (await dataverseService.GetPturtTypeConfigDtoAsync(request.UrgentType)).FirstOrDefault();
                urgent = urgentType.PturtTypeName;
            }
            string type = "normal";
            switch (urgent)
            {
                case UrgentType.Urgent:
                    //1835 用户提交付款申请后，对于 “其他紧急” 类型继续保留STP Manager/Header审批
                    //紧急改为 不紧急
                    //type = "urgent";
                    break;
                case UrgentType.OtherUrgent:
                    type = "otherUrgent";
                    break;
            }
            //创建审批任务
            var exemptType = EnumUtil.GetEnumIdValues<WorkflowTypeName>().ToDictionary(a => (WorkflowTypeName)a.Key, a => a.Value);
            var createApproval = new CreateApprovalDto
            {
                Name = exemptType[WorkflowTypeName.PaymentRequest],
                Department = request.ApplyUserBuToDept.ToString(),
                BusinessFormId = request.Id.ToString(),
                BusinessFormNo = request.ApplicationCode,
                BusinessFormName = EntitiesNameConsts.NameConsts.PAApplication,
                Submitter = CurrentUser.Id?.ToString(),
                OriginalApprovalId = request.ApplyUserId,
                FormData = JsonSerializer.Serialize(new { isTaxRateDecreases = isTaxRateSmall, urgentType = type }),
                WorkflowType = WorkflowTypeName.PaymentRequest,
                InstanceName = $"{exemptType[WorkflowTypeName.PaymentRequest]}-{DateTime.Now.ToString("yyMMdd-hhmmss")}",
                Remark = request.Remarks
            };
            if (isTaxRateSmall)
                createApproval.FirstApprover = firstApprover;
            await approvalRecord.InsertInitApprovalRecordAsync(new InitializeApprovalRecordDto
            {
                BusinessFormName = EntitiesNameConsts.NameConsts.PAApplication,
                BusinessId = request.Id,
                InstanceId = Guid.NewGuid(),
                Status = InitApprovalRecordStatus.Pending,
                MaxRetries = 3,
                RequestContent = JsonSerializer.Serialize(createApproval)
            });
            return true;

        }

        /// <summary>
        /// 供应商是否失效
        /// </summary>
        /// <param name="paVendorAndCompany"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        private async Task<(bool, string, List<string>)> VendorIsExpireAsync(List<(string, Guid)> paVendorAndCompany, string type)
        {
            var avmQuery = (await LazyServiceProvider.GetService<IBpcsAvmRepository>().GetQueryableAsync()).AsNoTracking();
            var vcCodes = paVendorAndCompany.Select(a => new { ApplicationCode = a.Item1, vendorId = a.Item2 }).ToList();
            var avms = avmQuery.Where(a => vcCodes.Select(x => x.vendorId).Contains(a.Id)).ToList();
            bool isExpire = true;
            string expireMsg = "";
            List<string> applicationCodes = new List<string>();
            foreach (var item in vcCodes)
            {
                var avm = avms.FirstOrDefault(a => a.Id == item.vendorId);
                if (avm.Vnstat == "D")
                {
                    isExpire = false;
                    applicationCodes.Add(item.ApplicationCode);
                    if (VendorExpireType.BATCH_APPROVE == type)
                        expireMsg += $"{item.ApplicationCode} 供应商已失效，审批失败；";
                    else if (VendorExpireType.SUBMIT == type)
                        expireMsg = "供应商已失效，提交失败！";
                    else if (VendorExpireType.APPROVE == type)
                        expireMsg = "供应商已失效，审批失败！";
                }
            }
            return (isExpire, expireMsg, applicationCodes);
        }
    }
}
