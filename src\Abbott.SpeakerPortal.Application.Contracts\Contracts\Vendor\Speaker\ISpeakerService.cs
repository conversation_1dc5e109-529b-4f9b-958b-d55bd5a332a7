﻿using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Vendor.Speaker;
using System.IO;
using System;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using System.Collections.Generic;
using Abbott.SpeakerPortal.Vendor.Speaker.TaskCenter;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Contracts.Vendor;
using Abbott.SpeakerPortal.Contracts.Vendor.Speaker;
using Abbott.SpeakerPortal.Enums;

namespace Abbott.SpeakerPortal.Vendor.Speaker
{
    public interface ISpeakerService
    {
        Task<MessageResult> UpdateMobileAsync(UpdateMobileRequestDto UpdateMobileRequestDto);

        Task<PagedResultDto<SpeakerListResponseDto>> GetSpeakerListAsync(SpeakerListRequestDto request);

        Task<Stream> ExportSpeakerListAsync(SpeakerListRequestDto request);
        Task<PagedResultDto<SpeakerListApplicationResponseDto>> GetSpeakerApplicationListAsync(SpeakerListApplicationRequestDto request);

        Task<MessageResult> GetSpeakerDetailAsync(Guid request, bool isRetuenKey = false);

        Task<MessageResult> GetSpeakerDetailByCodeAsync(string request);

        Task<MessageResult> GetSpeakerDraftDetailAsync(Guid? request);

        Task<MessageResult> GetSpeakerDetailByScanQRAsync(GetSpeakerDetailByScanQRRequestDto request);

        Task<PagedResultDto<SpeakerDraftListResponseDto>> GetSpeakerDraftListAsync(SpeakerDraftListRequestDto request);

        Task<MessageResult> DeleteSpeakerDraftAsync(DeleteSpeakerDraftRequestDto request);

        Task<MessageResult> CreateSpeakerAsync(CreateSpeakerRequestDto request);

        Task<MessageResult> SaveSpeakerAsync(SaveSpeakerRequestDto request);

        /// <summary>
        /// DPS Check审批时更新身份证信息并审批通过Workflow Task。
        /// [Non]HCP类型的申请单，在DPS Check审批节点允许审批人识别证件后直接替换填写的身份证信息，不需要退回申请人处重新填写
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<MessageResult> DPSCheckApproveAsync(DPSCheckApproveRequestDto request);

        Task<MessageResult> CloneSpeakerAsync(SaveSpeakerRequestDto request);

        Task<MessageResult> AuthorizedSellersAsync(AuthorizedSellerRequestDto request);

        Task<List<ValidationSpeakerResponseDto>> ValidationSpeakerDataAsync(ValidationSpeakerRequestDto request);

        Task<MessageResult> SubmitSpeakerAsync(SaveSpeakerRequestDto request);

        Task<PagedResultDto<SpeakerTaskCenterListResponseDto>> GetInitiatedSpeakerListAsync(SpeakerTaskCenterListRequestDto request);

        Task<PagedResultDto<SpeakerTaskCenterListResponseDto>> GetApprovalSpeakerListAsync(SpeakerTaskCenterListRequestDto request);

        Task<MessageResult> SaveSpeakerFinancialAsync(SaveSpeakerFinancialRequestDto request);

        Task<MessageResult> SaveSpeakerDPSAsync(SaveSpeakerDPSRequestDto request);

        Task<MessageResult> GetSpeakerChangedDetailAsync(Guid request);

        Task<MessageResult> GetVonderCodeValidateListAsync(FinancialInformation request);

        Task<MessageResult> GetFinancialInformationListAsync(SpeakerVonderCodeValidateRequestDto request);

        #region 供应商查询
        Task<PagedResultDto<SupplierListResponseDto>> GetSupplierListAsync(SupplierListRequestDto request);

        /// <summary>
        /// 验证删除供应商文档数据
        /// </summary>
        /// <param name="rows">The rows.</param>
        /// <returns></returns>
        Task<MessageResult> VarifyVendorDelete(IEnumerable<VendorDeleteTempleteDto> rows);

        /// <summary>
        /// 提交供应商删除
        /// </summary>
        /// <param name="vendors">The vendors.</param>
        /// <returns></returns>
        Task<MessageResult> SubmitImportVendorDelete(List<VendorDeleteImportResponseDto> vendors);

        /// <summary>
        /// 导出DPS Check信息
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        Task<Stream> ExportDPSCheckInfos();
        #endregion

        /// <summary>
        /// 根据EPD医院名和Code获取医院主数据
        /// </summary>
        /// <param name="name"></param>
        /// <param name="code"></param>
        /// <returns></returns>
        Task<HospitalDto> GetHospitalByEpdHospitalCode(string name, string code);

        /// <summary>
        /// 查询EPD医生数据
        /// </summary>
        /// <param name="epdDoctorQuery"></param>
        /// <returns></returns>
        Task<MessageResult> QueryEpdDoctorsAsync(EPDDoctorQueryDto epdDoctorQuery);

        /// <summary>
        /// EPD关联讲者
        /// </summary>
        /// <param name="epdRelationSpeaker"></param>
        /// <returns></returns>
        Task<MessageResult> EpdRelationSpeakerAsync(EpdRelationSpeakerDto epdRelationSpeaker);

        /// <summary>
        /// 获取讲着或者非HCP个人的申请或者正是档案
        /// </summary>
        /// <param name="vendorId"></param>
        /// <returns></returns>
        Task<IEnumerable<GetVendorApplicationOrOfficialInfoDto>> GetVendorApplicationOrOfficialInfoAsync(Guid vendorId);

        /// <summary>
        /// 判断当前登录人，如果是供应商，那么他只能看他自己的供应商信息
        /// </summary>
        /// <param name="vendorId"></param>
        /// <returns></returns>
        Task<bool> JudgeIfCurrentUserIsVendorAsync(Guid vendorId);

        /// <summary>
        /// 发送信息已完善邮件
        /// </summary>
        /// <param name="vendorApplication"></param>
        /// <returns></returns>
        Task SendCompleteInfoEmail((Guid Id, Guid ApplyUserId, VendorTypes VendorType, ApplicationTypes ApType, Statuses VdApStatus, string ApplyUserName, string VendorName) vendorApplication);
    }
}
