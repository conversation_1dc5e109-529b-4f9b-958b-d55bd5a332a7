select  newid() as spk_NexBPMCode, a.spk_BPMCode,a.spk_name,a.spk_englishname,a.spk_costnumber,b.spk_NexBPMCode as spk_consume,spk_paymentmethod
,spk_approvalnumber,iif(ISNULL(spk_pushonlinemeeting,'')='','No','Yes') as spk_pushonlinemeeting,spk_unit,iif(ISNULL(spk_ar,'')='','No','Yes') as spk_ar,iif(ISNULL(spk_isBPCS,'')='','No','Yes') as spk_isBPCS
into #spk_costnature
from spk_costnature_Tmp a
left join spk_consume b on a.spk_consume=b.spk_BPMCode

IF OBJECT_ID(N'dbo.spk_costnature', N'U') IS NOT NULL
BEGIN
	update a 
	set a.spk_BPMCode            = b.spk_BPMCode
       ,a.spk_name               = b.spk_name
       ,a.spk_englishname        = b.spk_englishname
       ,a.spk_costnumber         = b.spk_costnumber
       ,a.spk_consume            = b.spk_consume
       ,a.spk_paymentmethod      = b.spk_paymentmethod
       ,a.spk_approvalnumber     = b.spk_approvalnumber
       ,a.spk_pushonlinemeeting  = b.spk_pushonlinemeeting
       ,a.spk_unit               = b.spk_unit
       ,a.spk_ar                 = b.spk_ar
       ,a.spk_isBPCS             = b.spk_isBPCS
    from dbo.spk_costnature a
    join #spk_costnature b on a.spk_BPMCode = b.spk_BPMCode
    
    insert into dbo.spk_costnature
    select a.spk_NexBPMCode
           ,a.spk_BPMCode
           ,a.spk_name
           ,a.spk_englishname
           ,a.spk_costnumber
           ,a.spk_consume
           ,a.spk_paymentmethod
           ,a.spk_approvalnumber
           ,a.spk_pushonlinemeeting
           ,a.spk_unit
           ,a.spk_ar
           ,a.spk_isBPCS
	from #spk_costnature a
	where NOT EXISTS (SELECT * FROM dbo.spk_costnature where a.spk_BPMCode = spk_BPMCode)
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into dbo.spk_costnature from #spk_costnature
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END


