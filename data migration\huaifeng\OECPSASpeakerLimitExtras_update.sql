
DROP TABLE #OECPSASpeakerLimitExtras
SELECT 
 TRY_CONVERT(UNIQUEIDENTIFIER, [ID]) [ID]
,case when VendorId is null or VendorId='' then '00000000-0000-0000-0000-000000000000' else VendorId end [VendorId]
,case when ComPSALimitId is null or ComPSALimitId='' then '00000000-0000-0000-0000-000000000000' else ComPSALimitId end [ComPSALimitId]
,[ModifyType]
,TRY_CONVERT(UNIQUEIDENTIFIER, [DivisionId]) as  [DivisionId]
,[ExtralAmountRest]
,[ExtralTimesRest]
,[ExtralAuditApplicationNo]
,[Year]
,[Remark]
,[Doc]
,[CreationTime]
,case when CreatorId is null or CreatorId='' then '00000000-0000-0000-0000-000000000000' else CreatorId end [CreatorId]
,[LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER,IIF ([LastModifierId] = '' OR [LastModifierId] IS NULL ,'00000000-0000-0000-0000-000000000000',[LastModifierId]))[LastModifierId]
,[IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER,IIF ([DeleterId] = '' OR [DeleterId] IS NULL ,'00000000-0000-0000-0000-000000000000',[DeleterId]))[DeleterId]
,[DeletionTime]
INTO #OECPSASpeakerLimitExtras
FROM PLATFORM_ABBOTT_STG.dbo.OECPSASpeakerLimitExtras

--DROP TABLE #OECPSASpeakerLimitExtras


USE Speaker_Portal_STG;




UPDATE a
SET  
 a.[VendorId] = b.[VendorId]
,a.[ComPSALimitId] = b.[ComPSALimitId]
,a.[ModifyType] = b.[ModifyType]
,a.[DivisionId] = b.[DivisionId]
,a.[ExtralAmountRest] = b.[ExtralAmountRest]
,a.[ExtralTimesRest] = b.[ExtralTimesRest]
,a.[ExtralAuditApplicationNo] = b.[ExtralAuditApplicationNo]
,a.[Year] = b.[Year]
,a.[Remark] = b.[Remark]
,a.[Doc] = b.[Doc]
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = b.[CreatorId]
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = b.[LastModifierId]
,a.[IsDeleted] = b.[IsDeleted]
,a.[DeleterId] = b.[DeleterId]
,a.[DeletionTime] = b.[DeletionTime]
FROM dbo.OECPSASpeakerLimitExtras a
LEFT JOIN #OECPSASpeakerLimitExtras b
ON a.id=b.id


INSERT INTO dbo.OECPSASpeakerLimitExtras
SELECT
 [Id]
,[VendorId]
,[ComPSALimitId]
,[ModifyType]
,[DivisionId]
,[ExtralAmountRest]
,[ExtralTimesRest]
,[ExtralAuditApplicationNo]
,[Year]
,[Remark]
,[Doc]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
FROM #OECPSASpeakerLimitExtras a
WHERE not exists (select * from dbo.OECPSASpeakerLimitExtras where id=a.id)

--TRUNCATE TABLE dbo.OECPSASpeakerLimitExtras
