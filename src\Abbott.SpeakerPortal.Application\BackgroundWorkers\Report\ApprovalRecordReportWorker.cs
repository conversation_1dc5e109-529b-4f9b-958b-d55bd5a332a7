﻿using Abbott.SpeakerPortal.Contracts.Report;
using Abbott.SpeakerPortal.Contracts.Report.ProfessionalServiceTax;
using Hangfire;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.BackgroundWorkers.Report
{
    public class ApprovalRecordReportWorker : SpeakerPortalBackgroundWorkerBase
    {
        public ApprovalRecordReportWorker()
        {
            CronExpression = Cron.Hourly(1);//每小时执行一次，增量取前一个小时段内数据
        }

        public override async Task DoWorkAsync(CancellationToken cancellationToken = default)
        {
            //逻辑处理
            var reportService = LazyServiceProvider.LazyGetService<IReportService>();
            await reportService.PushApprovalRecordReportAsync();
        }
    }
}
