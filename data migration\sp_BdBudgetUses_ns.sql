CREATE PROCEDURE dbo.sp_BdBudgetUses_ns
AS 
begin
	select 
a.[Id],
a.[PrId],
a.[PdRowNo],
bd.id as [SubbudgetId],
a.[Amount],
a.[OperateTime],
a.[IsEnable],
a.[ExtraProperties],
a.[ConcurrencyStamp],
a.[CreationTime],
UPPER(ss1.spk_NexBPMCode) as CreatorId,
a.LastModificationTime,
UPPER(ss2.spk_NexBPMCode) as LastModifierId,
a.[IsDeleted],
a.[DeleterId],
a.[DeletionTime],
a.nbr,
a.[type]
into #BdBudgetUses
from PLATFORM_ABBOTT_Dev.dbo.BdBudgetUses_tmp a
left join PLATFORM_ABBOTT_Dev.dbo.spk_staffmasterdata ss1 
on a.CreatorId =ss1.bpm_id 
left join PLATFORM_ABBOTT_Dev.dbo.spk_staffmasterdata ss2 
on a.LastModifierId =ss2.bpm_id 
left join PLATFORM_ABBOTT_Dev.dbo.BdSubBudgets bd
on a.SubbudgetId=bd.code


    --删除表
    IF OBJECT_ID(N'PLATFORM_ABBOTT_Dev.dbo.BdBudgetUses ', N'U') IS NOT NULL
	BEGIN
		drop table PLATFORM_ABBOTT_Dev.dbo.BdBudgetUses
		select *
        into PLATFORM_ABBOTT_Dev.dbo.BdBudgetUses from #BdBudgetUses
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into PLATFORM_ABBOTT_Dev.dbo.BdBudgetUses from #BdBudgetUses
    -- select * from #vendor_tbl
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
end
