﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Abbott.SpeakerPortal.Contracts.Budget.Subbudget
{
    public class CreatesSubBudgetDto: MothlyAmountTextDto
    {
        /// <summary>
        /// 主预算编码
        /// </summary>
        public string MasterBudgetCode { get; set; }
        /// <summary>
        /// 成本中心
        /// </summary>
        public string CostCenter { get; set; }
        /// <summary>
        /// 大区
        /// </summary>
        public string Region { get; set; }

        /// <summary>
        /// 负责人姓名
        /// </summary>
        public string OwnerEmail { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 金额
        /// </summary>
        public string Description { get; set; }
        ///// <summary>
        ///// 是否开启
        ///// </summary>
        //public string StatusText { get; set; }
    }
}
