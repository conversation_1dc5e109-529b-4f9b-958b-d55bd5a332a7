select newid() as spk_NexBPMCode,
c.spk_NexBPMCode as [spk_city],
ot.spk_NexBPMCode as [spk_company]
into #spk_companymasterdata_citymasterdata
from spk_companymasterdata_citymasterdata_tmp a
join spk_companymasterdata ot
on a.spk_company=ot.spk_BPMCode     --数据量减少
join spk_citymasterdata c
on concat(c.spk_Name,c.spk_citynumber)=a.spk_city

IF OBJECT_ID(N'dbo.spk_companymasterdata_citymasterdata', N'U') IS NOT NULL
BEGIN
	drop table spk_companymasterdata_citymasterdata;
	select  *  into dbo.spk_companymasterdata_citymasterdata from #spk_companymasterdata_citymasterdata
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into dbo.spk_companymasterdata_citymasterdata from #spk_companymasterdata_citymasterdata
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END