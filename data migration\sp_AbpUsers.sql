CREATE PROCEDURE dbo.sp_AbpUsers
AS 
BEGIN
	select 
ss.spk_NexBPMCode  as ID,                          --员工ID
'' as TenantId,                                      --留空
ss.spk_staffaccount as UserName,                         --账号(原始数据)
ss.spk_staffaccount as NormalizedUserName,               --账号(全部大写)
ss.spk_Name as Name,                                     --员工姓名
'' as Surname,                                       --留空
ss.spk_staffemail as Email,                              --邮箱(原始数据)
ss.spk_staffemail as NormalizedEmail,                    --邮箱(全部大写)
'0' as EmailConfirmed,                                --默认填写为0
'' as PasswordHash,                                  --留空(未来不会有密码)
'' as SecurityStamp,                                 --?
'0' as IsExternal,                                    --默认填写为0
ss.spk_staffPhoneNumber as PhoneNumber,                  --电话
'0' as PhoneNumberConfirmed,                          --默认填写为0
'1' as IsActive,                                      --默认填写为1
'0' as TwoFactorEnabled,                              --默认填写为0
'' as LockoutEnd,                                    --留空
'0' as LockoutEnabled,                                --默认填写为0
'0' as AccessFailedCount,                             --默认填写为0
'{}' as ExtraProperties,                               --默认填写为"{}"
'' as ConcurrencyStamp,                              --?
GETDATE()  as CreationTime,                           --填写为导入时间
ss1.spk_NexBPMCode as CreatorId,                                     --填写为admin的ID
'' as LastModificationTime,                          --留空
'' as LastModifierId,                                --留空
'0' as IsDeleted,                                     --默认填写为0
'' as DeleterId,                                     --留空
'' as DeletionTime,                                  --留空
'' as OpenId,                                        --留空
ss.spk_staffNumber as StaffCode,                         --工号
'' as DepartmentId,                                  --留空
ss.spk_staffState as JobStatus,                          --1-在职；0-离职
so.spk_NexBPMCode  as MainDepartmentId,   --以ISMAIN=1得到主部门ID，根据组织主数据填回NexBPM对应的ID
'' as LastLoginTime,                                 --留空
'' as UnionId,                                       --留空
'0' as EntityVersion,                                 --默认填写为0
'' as LastPasswordChangeTime,                        --留空
'0' as ShouldChangePasswordOnNextLogin,               --默认填写为0
'' as SignedVersion                                --留空
into #AbpUsers
from spk_staffmasterdata ss 
left join ods_T_EMPLOYEE_ORGANIZATION o
on o.Emp_Id=ss.bpm_id and ISMAIN=1
left join spk_organizationalMasterData so
on o.ORGCODE=so.spk_BPMCode 
left join spk_staffmasterdata ss1
on 'admin'=ss1.spk_staffNumber

 IF OBJECT_ID(N'PLATFORM_ABBOTT.dbo.AbpUsers ', N'U') IS NOT NULL
	BEGIN
		drop table PLATFORM_ABBOTT.dbo.AbpUsers
		select *
        into PLATFORM_ABBOTT.dbo.AbpUsers from #AbpUsers
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into PLATFORM_ABBOTT.dbo.AbpUsers from #AbpUsers
    -- select * from #vendor_tbl
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
END
