﻿using Abbott.SpeakerPortal.Contracts.Common.PowerApp;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Report;
using Abbott.SpeakerPortal.Entities.Purchase.PurPOApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using DocumentFormat.OpenXml.Bibliography;
using System;
using System.Data;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Abbott.SpeakerPortal.Entities.Purchase.PurGRApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPAApplication;
using Microsoft.EntityFrameworkCore;
using Abbott.SpeakerPortal.Entities.Budget;
using DocumentFormat.OpenXml.ExtendedProperties;
using Senparc.Weixin.MP.AdvancedAPIs.MerChant;
using DocumentFormat.OpenXml.Wordprocessing;
using static System.Runtime.InteropServices.JavaScript.JSType;
using Volo.Abp.SettingManagement;
using Abbott.SpeakerPortal.IntermediateDB.Models;
using static Abbott.SpeakerPortal.Enums.PurPAStatus;
using static Abbott.SpeakerPortal.Enums.Purchase;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using Org.BouncyCastle.Pqc.Crypto.Lms;
using Senparc.Weixin.WxOpen.AdvancedAPIs.WxApp.WxAppJson;
using System.Linq.Dynamic.Core;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Contracts.Dataverse;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.User;
using IdentityModel;
using DocumentFormat.OpenXml.Office2010.Excel;
using DocumentFormat.OpenXml.Drawing;
using Abbott.SpeakerPortal.Entities.Integration.Dspot;
using System.Reflection.Emit;
using System.Security.Cryptography;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPOApplication;
using Abbott.SpeakerPortal.Dataverse.Contracts;
using Microsoft.Xrm.Sdk.Query;
using Microsoft.Xrm.Sdk;
using Microsoft.AspNetCore.Http.HttpResults;
using Irony.Parsing;
using static System.Runtime.CompilerServices.RuntimeHelpers;
using System.IO;
using MiniExcelLibs.OpenXml;
using MiniExcelLibs;
using Abbott.SpeakerPortal.Entities.Integration.EpdOnlineMeeting;
using DocumentFormat.OpenXml.Office2010.Word;
using Abbott.SpeakerPortal.Entities.Budget.BdExpense;
using DocumentFormat.OpenXml.Math;
using Abbott.SpeakerPortal.Entities.VendorApplications;
using Abbott.SpeakerPortal.Entities.VendorFinancials;
using Microsoft.Extensions.Azure;
using System.Threading;
using Microsoft.AspNetCore.Mvc.ApplicationModels;
using DocumentFormat.OpenXml.Drawing.Diagrams;
using Abbott.SpeakerPortal.Entities.Report;
using Volo.Abp.Data;
using static Azure.Core.HttpHeader;
using PdfSharp.Pdf.Content.Objects;
using static Abbott.SpeakerPortal.Enums.DataverseEnums.Staff;
using DocumentFormat.OpenXml.Drawing.Charts;
using Abbott.SpeakerPortal.Extension;
using Azure.Storage.Blobs.Models;
using static Abbott.SpeakerPortal.Enums.DataverseEnums.Returnreason;
using static Abbott.SpeakerPortal.Enums.DataverseEnums;
using System.Reflection;
using System.ComponentModel;
using Abbott.SpeakerPortal.Contracts.Report.EpdReport;
using Microsoft.Data.SqlClient;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Contracts.Report.WholeProcessReport;
using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Person;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Entities.Report.ProfessionalServiceTax;
using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.Dtos;
using Microsoft.Extensions.DependencyInjection;
using DataTable = System.Data.DataTable;
using Newtonsoft.Json;
using Abbott.SpeakerPortal.Entities.Report.ApprovalRecord;
using Abbott.SpeakerPortal.Entities.Common.Workflow.WorkflowTasks;
using static PdfSharp.Capabilities;
using PdfSharp;
using System.Drawing.Printing;
using static Abbott.SpeakerPortal.Permissions.SpeakerPortalPermissions;
using Abbott.SpeakerPortal.Entities.VendorApplicationPersonals;
using Abbott.SpeakerPortal.Entities.VendorApplicationOrgnizations;
using System.Xml.Linq;
using Abbott.SpeakerPortal.Entities.VendorApplicationFinancials;
using Abbott.SpeakerPortal.Entities.Purchase.PurBDApplication;
using Npgsql.Internal.Postgres;
using Abbott.SpeakerPortal.Entities.Purchase.PurBWApplication;
using Abbott.SpeakerPortal.Entities.OECSpeakerAuthApplys;
using Abbott.SpeakerPortal.Entities.Common.Workflow.CurrentApprovalTask;
using Abbott.SpeakerPortal.Utils;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Contracts.Budget;
using Volo.Abp.ObjectMapping;
using EFCore.BulkExtensions;
using Volo.Abp.Guids;
using Abbott.SpeakerPortal.AppServices.Integration.Dspot;
using Microsoft.Extensions.Logging;
using static Abbott.SpeakerPortal.Enums.ResignationTransfer;
using System.Collections.Concurrent;
using Abbott.SpeakerPortal.Entities.Report.WPRActiveView;
using Org.BouncyCastle.Asn1.Cmp;
using Abbott.SpeakerPortal.Entities.Report.WholeProcessReport;
using static Abbott.SpeakerPortal.Consts.EntitiesNameConsts;
using Abbott.SpeakerPortal.Contracts.Report.ApprovalRecord;
using Abbott.SpeakerPortal.Entities.User;
using Abbott.SpeakerPortal.Entities.Role;
using Abbott.SpeakerPortal.Contracts.Report.InvoiceReport;
using Abbott.SpeakerPortal.Entities.Report.GLH;
using Abbott.SpeakerPortal.Redis;

namespace Abbott.SpeakerPortal.AppServices.Report
{
    public class ReportService : SpeakerPortalAppService, IReportService
    {
        /// <summary>
        /// The service provider
        /// </summary>
        private readonly IServiceProvider _serviceProvider;
        private IDataverseRepository _dataverseRepository;
        /// <summary>
        /// The logger
        /// </summary>
        private readonly ILogger<ReportService> _logger;

        public ReportService(IServiceProvider serviceProvider)
        {
            _dataverseRepository = serviceProvider.GetService<IDataverseRepository>(); ;
            _logger = serviceProvider.GetService<ILogger<ReportService>>();
            _serviceProvider = serviceProvider;
        }
        /// <summary>
        /// 全流程报表
        /// </summary>
        /// <returns></returns>
        public async Task<PagedResultDto<FullProcessReportByApprovalReportDto>> GetFullProcessReportAsync(FullProcessRequestDto requestDto, bool isPage = true)
        {
            var queryablePo = (await LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            var queryablePOdetail = (await LazyServiceProvider.LazyGetService<IPurPOApplicationDetailsRepository>().GetQueryableAsync()).AsNoTracking();
            var queryablePR = (await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            var queryablePRdetail = (await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync()).AsNoTracking();
            var queryableGR = (await LazyServiceProvider.LazyGetService<IPurGRApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            var queryableGRHis = (await LazyServiceProvider.LazyGetService<IPurGRApplicationDetailHistoryRepository>().GetQueryableAsync()).AsNoTracking();
            var queryablePA = (await LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            var queryablePADetail = (await LazyServiceProvider.LazyGetService<IPurPAApplicationDetailRepository>().GetQueryableAsync()).AsNoTracking();
            var queryMasterBudget = (await LazyServiceProvider.LazyGetService<IBdMasterBudgetRepository>().GetQueryableAsync()).AsNoTracking();
            var queryableUser = (await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync()).AsNoTracking();

            var paymentInfos = (await LazyServiceProvider.LazyGetService<IFinanceCashierPaymentInfoRepository>().GetQueryableAsync()).AsNoTracking();
            var bpcsAmlQuery = (await LazyServiceProvider.LazyGetService<IBpcsAmlRepository>().GetQueryableAsync()).AsNoTracking();
            var bpcsGlhQuery = (await LazyServiceProvider.LazyGetService<IBpcsGlhRepository>().GetQueryableAsync()).AsNoTracking();

            //获取pp数据
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var dicCities = (await dataverseService.GetSpecialCitiesAsync(stateCode: null)).ToDictionary(s => s.Id, s => s.CityCode);
            var dicProducts = (await dataverseService.GetProductsAsync(stateCode: null)).ToDictionary(s => s.Id, s => s.Code);
            var dicBuCodingCfg = (await dataverseService.GetBuCodingCfgAsync(stateCode: null)).ToDictionary(s => s.BuId, s => s.BuCode);
            //bpcs数据
            var bpcsAvmQuery = (await LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync()).AsNoTracking();
            var bpcsPmfvmQuery = (await LazyServiceProvider.LazyGetService<IBpcsPmfvmRepository>().GetQueryableAsync())
                .AsNoTracking();
            var queryPRInfo = QueryPRInfo(queryablePR, queryablePRdetail, queryMasterBudget, queryableUser);
            var queryPOInfo = QueryPOInfo(queryablePo, queryablePOdetail);
            var queryGRInfo = QueryGRInfo(queryableGR, queryableGRHis, bpcsAvmQuery, bpcsPmfvmQuery);
            var queryPAInfo = QueryPAInfo(queryablePA, queryablePADetail, paymentInfos);

            var queryMpInfo = GetBpcsInfo(queryablePA, bpcsAmlQuery, bpcsGlhQuery);

            var queryData = queryPRInfo.GroupJoin(queryPOInfo, a => a.Id, b => b.PRId, (Pr, Pos) => new { Pr, Pos })
                .SelectMany(a => a.Pos.DefaultIfEmpty(), (a, Po) => new { a.Pr, Po })
                .GroupJoin(queryGRInfo, a => a.Pr.Id, b => b.PRId, (PrPo, Grs) => new { PrPo.Pr, PrPo.Po, Grs })
                .SelectMany(s => s.Grs.DefaultIfEmpty(), (PrPo, Gr) => new { PrPo.Pr, PrPo.Po, Gr })
                .GroupJoin(queryPAInfo, a => a.Gr.GRId, b => b.GrId, (a, PA) => new { a.Pr, a.Po, a.Gr, PA })
                .SelectMany(a => a.PA.DefaultIfEmpty(), (a, PA) => new { a.Pr, a.Po, a.Gr, PA })
                .GroupJoin(queryMpInfo, a => a.PA.PAId, b => b.Id, (a, b) => new { a.Pr, a.Po, a.Gr, a.PA, bpcs = b })
                .SelectMany(a => a.bpcs.DefaultIfEmpty(), (a, bpcs) => new FullProcessReportDto
                {
                    Id = a.Pr.Id,
                    PrCode = a.Pr.PrCode,
                    PostDate = a.Pr.PostDate,
                    PRInitiator = a.Pr.PRInitiator,
                    Principal = a.Pr.Principal,
                    Company = a.Pr.Company,
                    Bu = a.Pr.Bu,
                    BudgetCode = a.Pr.BudgetCode,
                    BudgetOwner = a.Pr.BudgetOwner,
                    BudgetAmount = a.Pr.BudgetAmount,
                    PRAmount = a.Pr.PRAmount,
                    ExpenseTypeName = a.Pr.ExpenseTypeName,
                    PRStatus = a.Pr.PRStatus,
                    PRItemNo = a.Pr.PRItemNo,
                    PRItemPaymenType = a.Pr.PRItemPaymenType,
                    HCPTIER = a.Pr.HCPTIER,
                    HCPHospital = a.Pr.HCPHospital,
                    OriginalVendorName = a.Pr.OriginalVendorName,
                    VendorName = a.Pr.VendorName,
                    VendorType = a.Pr.VendorType,
                    PRItemAmount = a.Pr.PRItemAmount,
                    OrignalActivityDate = a.Pr.OrignalActivityDate,
                    PRContent = a.Pr.PRContent,
                    IsPushed = a.Pr.IsPushed,
                    PushDate = a.Pr.PushDate,
                    TotalProcessDays = a.Pr.TotalProcessDays,
                    PRApproveDate = a.Pr.PRApproveDate,
                    PRItemProcessDays = a.Pr.PRItemProcessDays,
                    POId = a.Po.POId,
                    POCode = a.Po.POCode,
                    POPostDate = a.Po.POPostDate,
                    POInitiator = a.Po.POInitiator,
                    POAmountTax = a.Po.POAmountTax,
                    POAmount = a.Po.POAmount,
                    POTotalBalanceAmount = a.Po.POTotalBalanceAmount,
                    POVAT = a.Po.POVAT,
                    POVATNoTax = a.Po.POVATNoTax,
                    POStatus = a.Po.POStatus,
                    GRId = a.Gr.GRId,
                    GRCode = a.Gr.GRCode,
                    GRVendorName = a.Gr.GRVendorName,
                    GRVendorCode = a.Gr.GRVendorCode,
                    BpcsPmfvmVcrdte = a.Gr.bpcsPmfvmVcrdte,
                    BpcsPmfvmVctime = a.Gr.bpcsPmfvmVctime,
                    GRAmount = a.Gr.GRAmount,
                    GRAmountNoTax = a.Gr.GRAmountNoTax,
                    GRVAT = a.Gr.GRVAT,
                    HistoricalReceiptAmount = a.Gr.HistoricalReceiptAmount,
                    HistoricalReceiptAmountNoTax = a.Gr.HistoricalReceiptAmountNoTax,
                    HistoricalReceiptAmountTax = a.Gr.HistoricalReceiptAmountTax,
                    GRDate = a.Gr.GRDate,
                    GRStatus = a.Gr.GRStatus,
                    PAId = a.PA.PAId,
                    PACode = a.PA.PACode,
                    IsLastPayment = a.PA.IsLastPayment,
                    LatestPA = a.PA.LatestPA,
                    DateOfLatestPA = a.PA.DateOfLatestPA,
                    PAPostDate = a.PA.PAPostDate,
                    PAAmount = a.PA.PAAmount,
                    PAAmountNoTax = a.PA.PAAmountNoTax,
                    PAVAT = a.PA.PAVAT,
                    PATotalAmountPaid = a.PA.PATotalAmountPaid,
                    PATotalAmountPaidNoTax = a.PA.PATotalAmountPaidNoTax,
                    PATotalAmountPaidTax = a.PA.PATotalAmountPaidTax,
                    VendorScore = a.PA.VendorScore,
                    IFOReceivedPADate = a.PA.IFOReceivedPADate,
                    IFOReceivedPAPostProcessDays = a.PA.IFOReceivedPAPostProcessDays,
                    PAReviewDate = a.PA.PAReviewDate,
                    PAStatus = a.PA.PAStatus,
                    POApproveDate = a.Po.POApproveDate,
                    POProcessDays = a.Po.POProcessDays,
                    PRPODays = a.Pr.PushDate == null || a.Gr.GrPostDate == null ? null : (a.Gr.GrPostDate - a.Pr.PushDate).Value.Days,
                    PAPostPOApproveProcessDays = a.Po.POApproveDate == null || a.PA.PAPostDate == null ? null : (a.PA.PAPostDate - a.Po.POApproveDate).Value.Days,
                    PAProcessDays = a.PA.PAProcessDays,
                    CompanyCode = a.Pr.CompanyCode,
                    BuId = a.Pr.BuId,
                    CostCenterCode = a.Pr.CostCenterCode,
                    CostNatureCode = a.Pr.CostNatureCode,
                    ProductId = a.Pr.ProductId,
                    CityId = a.Pr.CityId,
                    EBankingDate = a.PA.EBankingDate,
                    EBankingStatus = a.PA.EBankingStatus,
                    EBankingComment = a.PA.EBankingComment,
                    BPCSAPNo = bpcs.Lhjnen,
                    MPDateInt = bpcs.Amlpda,
                    IsRecoil = a.Pr.IsRecoil,
                });
            var count = await queryData.CountAsync();
            queryData = queryData.PagingIf(requestDto, isPage);
            var dataList = await queryData.ToListAsync();
            var worlflowtaskIds = new List<string>();
            dataList.ForEach(v =>
            {
                worlflowtaskIds.Add(v.Id.ToString());
                //if (v.GRId.HasValue) worlflowtaskIds.Add(v.GRId.ToString());
                if (v.POId.HasValue) worlflowtaskIds.Add(v.POId.ToString());
                if (v.PAId.HasValue) worlflowtaskIds.Add(v.PAId.ToString());
                var bucode = string.Empty;
                var productcode = string.Empty;
                var citycode = string.Empty;
                if (v.CityId.HasValue) citycode = dicCities.GetValueOrDefault(v.CityId.Value);
                if (v.ProductId.HasValue) productcode = dicProducts.GetValueOrDefault(v.ProductId.Value);
                if (v.BuId.HasValue) bucode = dicBuCodingCfg.GetValueOrDefault(v.BuId.Value);
                v.COA = $"{v.CompanyCode}.{bucode}." +
                $"{v.CostCenterCode}.{v.CostNatureCode}.{productcode}." +
                $"{citycode}";
            });
            //根据业务id查找审批人
            var workflowApprover = await GetWorkFlowTaskByIds(worlflowtaskIds);
            var datas = ObjectMapper.Map<List<FullProcessReportDto>, List<FullProcessReportByApprovalReportDto>>(dataList);

            datas.ForEach(v =>
            {
                //查找Pr审批人
                var prApprover = workflowApprover.GetValueOrDefault(v.Id);
                v.PrFinallyExpenseApprover = prApprover?.PrBusinessApprovalName;
                v.PRCurrentProcessor = prApprover?.CurrentApprovalName;
                v.PrFinallyExpenseApprovedDate = prApprover?.PrBusinessApprovalTime;
                v.PrFinanceFinallyApprover = prApprover?.PrFinanceApprovalName;
                v.PrFinanceFinallyApprovalDate = prApprover?.PrFinanceApprovalTime;

                if (v.PAId.HasValue)
                {
                    var PAApprover = workflowApprover.GetValueOrDefault(v.PAId.Value);
                    v.PACurrentProcessor = PAApprover?.CurrentApprovalName;
                }
                if (v.POId.HasValue)
                {
                    var POApprover = workflowApprover.GetValueOrDefault(v.POId.Value);
                    v.POCurrentProcessor = POApprover?.CurrentApprovalName;
                    v.POFirstApprover = POApprover?.FirstApprovalName;
                }
            });
            return new PagedResultDto<FullProcessReportByApprovalReportDto>() { Items = datas, TotalCount = count };
        }
        /// <summary>
        /// 导出全流程报表
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        public async Task<Stream> ExportToExcelFullProcessReportAsync(WholeProcessReportRequestDto requestDto)
        {
            object datas;
            //暂时加一个开关，用于全流程报表切换查询方式，此Key存在则查询ActiveView的方式
            var isLookupView = await LazyServiceProvider.LazyGetService<IRedisRepository>().Database.KeyExistsAsync(Consts.RedisKey.IsLookupView);
            if (isLookupView)
            {
                requestDto.PageSize = int.MaxValue;
                var result = await GetWprRptViewListAsync(requestDto);
                datas = result.Items;
            }
            else
            {
                var result = await GetWprReportListAsync(requestDto, false);
                datas = result.Items;
            }

            MemoryStream stream = new();
            var config = new OpenXmlConfiguration()
            {
                TableStyles = TableStyles.None,
                AutoFilter = false
            };
            stream.SaveAs(datas, true, "Sheet1", configuration: config);
            stream.Seek(0, SeekOrigin.Begin);
            return stream;
        }

        /// <summary>
        /// 导出OEC全流程报表
        /// </summary>
        /// <returns></returns>
        public async Task<Stream> ExportToExcelOECWholeProcessReportAsync()
        {
            var dtm = DateTime.Now;
            var request = new WholeProcessReportRequestDto
            {
                PrPostDateEnd = dtm,
                PrPostDateStart = dtm.AddYears(-2)
            };
            //var result = await ExportOECReportListAsync(request);
            var result = await GetWprReportListAsync(request, false);
            MemoryStream stream = new();
            var config = new OpenXmlConfiguration()
            {
                TableStyles = TableStyles.None,
                AutoFilter = false
            };
            stream.SaveAs(result.Items, true, "Sheet1", configuration: config);
            stream.Seek(0, SeekOrigin.Begin);
            return stream;
        }
        /// <summary>
        /// Accrual Report
        /// </summary>
        /// <returns></returns>
        public async Task<PagedResultDto<AccrualReportResponseDto>> GetAccrualReportAsync(AccrualReportRequestDto requestDto, bool isPage = true)
        {
            var queryablePR = (await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            var queryablePRdetail = (await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync()).AsNoTracking();
            var queryablePRProduct = (await LazyServiceProvider.LazyGetService<IPurPRApplicationProductApportionmentRepository>().GetQueryableAsync()).AsNoTracking();
            var queryablePA = (await LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            var queryablePADetail = (await LazyServiceProvider.LazyGetService<IPurPAApplicationDetailRepository>().GetQueryableAsync()).AsNoTracking();
            var querySubBudget = (await LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>().GetQueryableAsync()).AsNoTracking();
            var queryBudgetReturn = (await LazyServiceProvider.LazyGetService<IBdBudgetReturnRepository>().GetQueryableAsync()).AsNoTracking();
            var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            //获取pp主数据
            //var products = (await dataverseService.GetProductsAsync()).ToDictionary(s => s.Id, s => s.Code);
            var dicCities = (await dataverseService.GetSpecialCitiesAsync(stateCode: null)).ToDictionary(s => s.Id, s => s.CityCode);
            var dicBuCodingCfg = (await dataverseService.GetBuCodingCfgAsync(stateCode: null)).ToDictionary(s => s.BuId, s => s.BuCode);
            //BpcsGrh表
            var bpcsGlhQuery = (await LazyServiceProvider.LazyGetService<IBpcsGlhRepository>().GetQueryableAsync()).AsNoTracking().Where(a => a.Lhreas == "APIIL" || a.Lhreas == "APV2L");
            var bpcsGcrQuery = (await LazyServiceProvider.LazyGetService<IBpcsGcrRepository>().GetQueryableAsync()).AsNoTracking().Where(a => a.Crsg05 != "9999");
            //去掉反冲行Prd
            //var hedgePrDetails = queryablePRdetail.Where(m => !queryablePRdetail.Any(other => other.HedgePrDetailId == m.Id) && m.HedgePrDetailId == null);
            var hedgePrDetails = queryablePRdetail.GroupJoin(queryablePRdetail, a => a.Id, b => b.HedgePrDetailId, (a, b) => new { Prd = a, hedge = b }).SelectMany(a => a.hedge.DefaultIfEmpty(), (a, b) => new { a.Prd, b }).Where(m => m.Prd.HedgePrDetailId == null && m.b == null).Select(s => s.Prd);

            //var queryJoinPA = hedgePrDetails.GroupJoin(queryablePADetail, a => a.Id, b => b.PRDetailId, (a, b) => new { hedge = a, pad = b })
            //      .SelectMany(a => a.pad.DefaultIfEmpty(), (a, b) => new { a.hedge, pad = b })
            //      .Join(queryablePA, a => a.pad.PurPAApplicationId, b => b.Id, (a, b) => new { a.hedge, pa = b });

            //查找每个Pr付款中最大的Pa
            var maxPaCode = queryablePA.GroupBy(g => g.PRId, p => p.ApplicationCode, (key, value) => new { PrId = key, PACodeMax = value.Max() });
            var minPaCode = queryablePA.GroupBy(g => g.PRId, p => p.ApplicationCode, (key, value) => new { PrId = key, PACodeMin = value.Min() });

            var budgetQuery = querySubBudget.GroupJoin(queryableUser, a => a.OwnerId, b => b.Id, (a, b) => new { Budget = a, owner = b })
                    .SelectMany(a => a.owner.DefaultIfEmpty(), (a, b) => new { a.Budget, ownerName = b.Name });
            var query = queryablePR.GroupJoin(hedgePrDetails, a => a.Id, b => b.PRApplicationId, (a, b) => new { Pr = a, Prd = b })
                 .SelectMany(a => a.Prd.DefaultIfEmpty(), (a, b) => new { Pr = a.Pr, Prd = b })
                 .GroupJoin(budgetQuery, a => a.Pr.SubBudgetId, b => b.Budget.Id, (a, b) => new { a, b })
                 .SelectMany(a => a.b.DefaultIfEmpty(), (Pr, b) => new { Pr.a.Pr, Pr.a.Prd, b.Budget, b.ownerName })
                 .GroupJoin(queryablePRProduct, a => new { a.Prd.PRApplicationId, PRApplicationDetailId = a.Prd.Id }, a => new { a.PRApplicationId, PRApplicationDetailId = a.PRApplicationDetailId ?? Guid.Empty }, (a, b) => new { a.Pr, a.Prd, a.Budget, a.ownerName, ProductApportionments = b })
                 .SelectMany(a => a.ProductApportionments.DefaultIfEmpty(), (a, b) => new { a.Pr, a.Prd, a.Budget, a.ownerName, ProductApportionment = b })
                 .GroupJoin(queryablePA, a => a.Pr.Id, b => b.PRId, (a, b) => new { a.Pr, a.Prd, a.Budget, a.ownerName, a.ProductApportionment, Pa = b })
                 .SelectMany(a => a.Pa.DefaultIfEmpty(), (a, b) => new AccrualReportResponseDto
                 {
                     Id = a.Pr.Id,
                     PrCode = a.Pr.ApplicationCode,
                     PAFormCode = b.ApplicationCode,
                     PostDate = a.Pr.ApplyTime,
                     Initiator = a.Pr.ApplyUserIdName,
                     InitiatorId = a.Pr.ApplyUserId,
                     ItemNo = a.Prd.RowNo,
                     BudgetCode = a.Budget.Code,
                     BudgetDescription = a.Budget.Description,
                     BudgetOwner = a.ownerName,
                     BudgetOwnerId = a.Budget.OwnerId,
                     Company = a.Pr.CompanyIdName,
                     CompanyId = a.Pr.CompanyId,
                     BU = a.Pr.ApplyUserBuName,
                     Product = a.ProductApportionment.ProductName,
                     ProductId = a.ProductApportionment == null ? null : a.ProductApportionment.ProductId,
                     COA = a.Pr.CompanyCode + ".{0}" + a.Prd.CostCenterCode + "." + a.Prd.CostNatureCode + "." + a.ProductApportionment.ProductCode + ".{1}",
                     VendorName = a.Prd.VendorName,
                     PRAmount = a.Prd.TotalAmountRMB * (a.ProductApportionment == null ? null : (decimal)a.ProductApportionment.Ratio) / 100,
                     ExpectDate = a.Prd.OriginalEstimateDate,
                     PayType = a.Prd.PayMethod,
                     ExpenseNature = a.Prd.CostNatureName,
                     ExpenseNatureId = a.Prd.CostNature,
                     City = a.Prd.CityIdName,
                     CityId = a.Prd.CityId,
                     CostCenterName = a.Pr.CostCenterName,
                     CostCenterId = a.Pr.CostCenter,
                     PRDescription = a.Prd.Content,
                     APPostingDate = b.ApprovedDate,
                     ApprovalLevel = a.Pr.Status,
                     Flag = "EPO"
                 });
            //查询epo数据
            var queryEpo = query.GroupJoin(minPaCode, a => a.Id, min => min.PrId, (Pr, min) => new { accrual = Pr, min })
                             .SelectMany(a => a.min.DefaultIfEmpty(), (a, b) => new AccrualReportResponseDto
                             {
                                 Id = a.accrual.Id,
                                 PrCode = a.accrual.PrCode,
                                 PAFormCode = a.accrual.PAFormCode,
                                 PostDate = a.accrual.PostDate,
                                 Initiator = a.accrual.Initiator,
                                 InitiatorId = a.accrual.InitiatorId,
                                 ItemNo = a.accrual.ItemNo,
                                 BudgetCode = a.accrual.BudgetCode,
                                 BudgetDescription = a.accrual.BudgetDescription,
                                 BudgetOwner = a.accrual.BudgetOwner,
                                 BudgetOwnerId = a.accrual.BudgetOwnerId,
                                 Company = a.accrual.Company,
                                 CompanyId = a.accrual.CompanyId,
                                 BU = a.accrual.BU,
                                 Product = a.accrual.Product,
                                 ProductId = a.accrual.ProductId,
                                 COA = a.accrual.COA,
                                 VendorName = a.accrual.VendorName,
                                 PRAmount = a.accrual.PAFormCode == null || b.PACodeMin == a.accrual.PAFormCode ? a.accrual.PRAmount : 0, //如果没有Pa返回行金额，如果有判断是否是最新的的code,是的话就赋值PRAmount，反之赋值0
                                 ExpectDate = a.accrual.ExpectDate,
                                 PayType = a.accrual.PayType,
                                 ExpenseNature = a.accrual.ExpenseNature,
                                 ExpenseNatureId = a.accrual.ExpenseNatureId,
                                 City = a.accrual.City,
                                 CityId = a.accrual.CityId,
                                 CostCenterName = a.accrual.CostCenterName,
                                 CostCenterId = a.accrual.CostCenterId,
                                 PRDescription = a.accrual.PRDescription,
                                 APPostingDate = a.accrual.APPostingDate,
                                 ApprovalLevel = a.accrual.ApprovalLevel,
                                 BPCSDescription = "",
                                 Flag = "EPO"
                             });

            //查询GR/VAR数据
            var GRVARQuery = query.GroupJoin(maxPaCode, a => a.Id, Max => Max.PrId, (Pr, max) => new { accrual = Pr, max })
                .SelectMany(a => a.max.DefaultIfEmpty(), (a, b) => new { a.accrual, PACodeMax = b.PACodeMax })
                .Join(queryBudgetReturn.Where(m => m.ReturnSourceCode.Substring(0, 1) == "G"), a => new { PrId = a.accrual.Id, PdRowNo = a.accrual.ItemNo ?? 0 }, b => new { b.PrId, b.PdRowNo }, (a, b) => new AccrualReportResponseDto
                {
                    Id = a.accrual.Id,
                    PrCode = a.accrual.PrCode,
                    PAFormCode = b.Amount > 0 ? a.PACodeMax : "",
                    PostDate = a.accrual.PostDate,
                    Initiator = a.accrual.Initiator,
                    InitiatorId = a.accrual.InitiatorId,
                    ItemNo = a.accrual.ItemNo,
                    BudgetCode = a.accrual.BudgetCode,
                    BudgetDescription = a.accrual.BudgetDescription,
                    BudgetOwner = a.accrual.BudgetOwner,
                    BudgetOwnerId = a.accrual.BudgetOwnerId,
                    Company = a.accrual.Company,
                    CompanyId = a.accrual.CompanyId,
                    BU = a.accrual.BU,
                    Product = a.accrual.Product,
                    ProductId = a.accrual.ProductId,
                    COA = a.accrual.COA,
                    VendorName = a.accrual.VendorName,
                    PRAmount = b.Amount,
                    ExpectDate = a.accrual.ExpectDate,
                    PayType = a.accrual.PayType,
                    ExpenseNature = a.accrual.ExpenseNature,
                    ExpenseNatureId = a.accrual.ExpenseNatureId,
                    City = a.accrual.City,
                    CityId = a.accrual.CityId,
                    CostCenterName = a.accrual.CostCenterName,
                    CostCenterId = a.accrual.CostCenterId,
                    PRDescription = a.accrual.PRDescription,
                    APPostingDate = a.accrual.APPostingDate,
                    ApprovalLevel = a.accrual.ApprovalLevel,
                    BPCSDescription = "",
                    Flag = "GR/VAR"//po/gr +最新Pa <0 PAFormCode 空
                });
            //查询PO/VAR
            var POVARQuery = query.GroupJoin(minPaCode, a => a.Id, min => min.PrId, (Pr, min) => new { accrual = Pr, min })
                 .SelectMany(a => a.min.DefaultIfEmpty(), (a, b) => new { a.accrual, PACodeMin = b.PACodeMin })
                 .Join(queryBudgetReturn.Where(m => m.ReturnSourceCode.Substring(0, 1) == "O"), a => new { PrId = a.accrual.Id, PdRowNo = a.accrual.ItemNo ?? 0 }, b => new { b.PrId, b.PdRowNo }, (a, b) => new AccrualReportResponseDto
                 {
                     Id = a.accrual.Id,
                     PrCode = a.accrual.PrCode,
                     PAFormCode = b.Amount > 0 ? a.PACodeMin : "",
                     PostDate = a.accrual.PostDate,
                     Initiator = a.accrual.Initiator,
                     InitiatorId = a.accrual.InitiatorId,
                     ItemNo = a.accrual.ItemNo,
                     BudgetCode = a.accrual.BudgetCode,
                     BudgetDescription = a.accrual.BudgetDescription,
                     BudgetOwner = a.accrual.BudgetOwner,
                     BudgetOwnerId = a.accrual.BudgetOwnerId,
                     Company = a.accrual.Company,
                     CompanyId = a.accrual.CompanyId,
                     BU = a.accrual.BU,
                     Product = a.accrual.Product,
                     ProductId = a.accrual.ProductId,
                     COA = a.accrual.COA,
                     VendorName = a.accrual.VendorName,
                     PRAmount = b.Amount,
                     ExpectDate = a.accrual.ExpectDate,
                     PayType = a.accrual.PayType,
                     ExpenseNature = a.accrual.ExpenseNature,
                     ExpenseNatureId = a.accrual.ExpenseNatureId,
                     City = a.accrual.City,
                     CityId = a.accrual.CityId,
                     CostCenterName = a.accrual.CostCenterName,
                     CostCenterId = a.accrual.CostCenterId,
                     PRDescription = a.accrual.PRDescription,
                     APPostingDate = a.accrual.APPostingDate,
                     ApprovalLevel = a.accrual.ApprovalLevel,
                     BPCSDescription = "",
                     Flag = "PO/VAR"//po/gr +最新Pa <0 PAFormCode 空
                 });
            // PA/epo min GR/max
            var PAVARQuery = query.Join(queryBudgetReturn.Where(b => b.ReturnSourceCode.Substring(0, 1) == "A"), a => new { PrId = a.Id, PdRowNo = a.ItemNo ?? 0 }, b => new { b.PrId, b.PdRowNo }, (a, b) => new AccrualReportResponseDto
            {
                Id = a.Id,
                PrCode = a.PrCode,
                PAFormCode = a.PAFormCode,
                PostDate = a.PostDate,
                Initiator = a.Initiator,
                InitiatorId = a.InitiatorId,
                ItemNo = a.ItemNo,
                BudgetCode = a.BudgetCode,
                BudgetDescription = a.BudgetDescription,
                BudgetOwner = a.BudgetOwner,
                BudgetOwnerId = a.BudgetOwnerId,
                Company = a.Company,
                CompanyId = a.CompanyId,
                BU = a.BU,
                Product = a.Product,
                ProductId = a.ProductId,
                COA = a.COA,
                VendorName = a.VendorName,
                PRAmount = b.Amount,
                ExpectDate = a.ExpectDate,
                PayType = a.PayType,
                ExpenseNature = a.ExpenseNature,
                ExpenseNatureId = a.ExpenseNatureId,
                City = a.City,
                CityId = a.CityId,
                CostCenterName = a.CostCenterName,
                CostCenterId = a.CostCenterId,
                PRDescription = a.PRDescription,
                APPostingDate = a.APPostingDate,
                ApprovalLevel = a.ApprovalLevel,
                BPCSDescription = "",
                Flag = "PA/VAR"//po/gr +最新Pa <0 PAFormCode 空
            });
            var VARQuery = PAVARQuery.Concat(POVARQuery).Concat(GRVARQuery);
            //Pa join Pr
            var PAPRQuery = queryablePA.Join(queryablePR, a => a.PRId, b => b.Id, (PA, Pr) => new { PrCode = Pr.ApplicationCode, PAFormCode = PA.ApplicationCode, SubPaCode = PA.ApplicationCode.Substring(1), ApprovalLevel = Pr.Status, APPostingDate = PA.ApprovedDate, PRPostDate = Pr.ApplyTime, Id = Pr.Id });

            var BpcsglhQuery = PAPRQuery.Join(bpcsGlhQuery, a => a.SubPaCode, b => b.Lhdref, (a, b) => new { Pa = a, glh = b }).Join(bpcsGcrQuery, a => a.glh.Lhian, b => b.Crian, (a, b) => new AccrualReportResponseDto
            {
                Id = a.Pa.Id,
                PrCode = a.Pa.PrCode,
                PAFormCode = a.Pa.PAFormCode,
                PostDate = a.Pa.PRPostDate,
                Initiator = "",
                InitiatorId = null,
                ItemNo = null,
                BudgetCode = "",
                BudgetDescription = "",
                BudgetOwner = "",
                BudgetOwnerId = null,
                Company = b.Crsg01,
                CompanyId = null,
                BU = "",
                Product = "",
                ProductId = null,
                COA = (b.Crsg01 != null ? b.Crsg01 + "." : "") +
                      (b.Crsg02 != null ? b.Crsg02 + "." : "") +
                      (b.Crsg03 != null ? b.Crsg03 + "." : "") +
                      (b.Crsg04 != null ? b.Crsg04 + "." : "") +
                      (b.Crsg05 != null ? b.Crsg05 + "." : "") +
                      (b.Crsg06 != null ? b.Crsg06 : "") +
                      (b.Crsg07 != null ? "." + b.Crsg07 : ""),
                VendorName = a.glh.Lhdref,
                PRAmount = a.glh.Lhdram != 0 ? -a.glh.Lhdram : a.glh.Lhcram,
                ExpectDate = null,
                PayType = null,
                ExpenseNature = "",
                ExpenseNatureId = null,
                City = "",
                CityId = null,
                CostCenterName = "",
                CostCenterId = null,
                PRDescription = "",
                BPCSDescription = a.glh.Lhdref,
                APPostingDate = a.Pa.APPostingDate,
                ApprovalLevel = a.Pa.ApprovalLevel,
                Flag = a.glh.Lhreas == "APIIL" ? "A/P" : a.glh.Lhreas == "APV2L" ? "VOID" : ""
            });

            var BpcsglhNewQuery = PAPRQuery.Join(bpcsGlhQuery, a => string.Concat(a.SubPaCode.Substring(1), a.SubPaCode.Substring(0, 1)), b => b.Lhdref, (a, b) => new { Pa = a, glh = b }).Join(bpcsGcrQuery, a => a.glh.Lhian, b => b.Crian, (a, b) => new AccrualReportResponseDto
            {
                Id = a.Pa.Id,
                PrCode = a.Pa.PrCode,
                PAFormCode = a.Pa.PAFormCode,
                PostDate = a.Pa.PRPostDate,
                Initiator = "",
                InitiatorId = null,
                ItemNo = null,
                BudgetCode = "",
                BudgetDescription = "",
                BudgetOwner = "",
                BudgetOwnerId = null,
                Company = b.Crsg01,
                CompanyId = null,
                BU = "",
                Product = "",
                ProductId = null,
                COA = (b.Crsg01 != null ? b.Crsg01 + "." : "") +
                      (b.Crsg02 != null ? b.Crsg02 + "." : "") +
                      (b.Crsg03 != null ? b.Crsg03 + "." : "") +
                      (b.Crsg04 != null ? b.Crsg04 + "." : "") +
                      (b.Crsg05 != null ? b.Crsg05 + "." : "") +
                      (b.Crsg06 != null ? b.Crsg06 : "") +
                      (b.Crsg07 != null ? "." + b.Crsg07 : ""),
                VendorName = a.glh.Lhdref,
                PRAmount = a.glh.Lhdram != 0 ? -a.glh.Lhdram : a.glh.Lhcram,
                ExpectDate = null,
                PayType = null,
                ExpenseNature = "",
                ExpenseNatureId = null,
                City = "",
                CityId = null,
                CostCenterName = "",
                CostCenterId = null,
                PRDescription = "",
                BPCSDescription = a.glh.Lhdref,
                APPostingDate = a.Pa.APPostingDate,
                ApprovalLevel = a.Pa.ApprovalLevel,
                Flag = "A/P New"
            });
            var queryData = queryEpo.Concat(VARQuery).Concat(BpcsglhQuery).Concat(BpcsglhNewQuery);
            queryData = queryData.WhereIf(!requestDto.PaCode.IsNullOrWhiteSpace(), m => m.PAFormCode == requestDto.PaCode)
                .WhereIf(!requestDto.PrCode.IsNullOrWhiteSpace(), m => m.PrCode == requestDto.PrCode)
                .WhereIf(requestDto.PrPostDateStart.HasValue, m => m.PostDate >= requestDto.PrPostDateStart)
                .WhereIf(requestDto.PrPostDateEnd.HasValue, m => m.PostDate < requestDto.PrPostDateEnd.Value.AddDays(1))
                .WhereIf(requestDto.PrApplyUserId.HasValue, m => m.InitiatorId == requestDto.PrApplyUserId)
                .WhereIf(requestDto.SbOwnerId.HasValue, m => m.BudgetOwnerId == requestDto.SbOwnerId)
                .WhereIf(!requestDto.SbCode.IsNullOrWhiteSpace(), m => m.BudgetCode == requestDto.SbCode)
                .WhereIf(requestDto.CompanyId.HasValue, m => m.CompanyId == requestDto.CompanyId)
                .WhereIf(requestDto.PrBu.HasValue, m => m.BuId == requestDto.PrBu)
                .WhereIf(requestDto.ProductId.HasValue, m => m.ProductId == requestDto.ProductId)
                .WhereIf(requestDto.Location.HasValue, m => m.CityId == requestDto.Location)
                .WhereIf(requestDto.CostCenter.HasValue, m => m.CostCenterId == requestDto.CostCenter)
                .WhereIf(requestDto.PayMethod.HasValue, m => m.PayType == requestDto.PayMethod)
                .WhereIf(requestDto.ExpenseNatureId.HasValue, m => m.ExpenseNatureId == requestDto.ExpenseNatureId)
                .WhereIf(requestDto.ExpectStartDate.HasValue, m => m.ExpectDate >= requestDto.ExpectStartDate)
                .WhereIf(requestDto.ExpectEndDate.HasValue, m => m.ExpectDate < requestDto.ExpectEndDate.Value.AddDays(1))
                //.WhereIf(requestDto.ApprovalLevel.HasValue, m => m.ApprovalLevel == requestDto.ApprovalLevel)
                ;
            queryData = queryData.OrderByDescending(o => o.PostDate).AsQueryable();
            var Count = await queryData.CountAsync();
            queryData = queryData.PagingIf(requestDto, isPage);
            var datas = await queryData.ToListAsync();
            datas.ForEach(v =>
            {
                var BuCode = string.Empty;
                //var ProductCode = string.Empty;
                var CitycCode = string.Empty;
                if (v.CityId.HasValue) CitycCode = dicCities.GetValueOrDefault(v.CityId.Value);
                //if (v.ProductId.HasValue) ProductCode = products.GetValueOrDefault(v.ProductId.Value);
                if (v.BuId.HasValue) BuCode = dicBuCodingCfg.GetValueOrDefault(v.BuId.Value);
                v.COA = string.Format(v.COA, BuCode, CitycCode);
            });
            return new PagedResultDto<AccrualReportResponseDto>() { Items = datas, TotalCount = Count };
        }

        /// <summary>
        /// 导出Accrual报表
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        public async Task<Stream> ExportToExcelAccrualReportAsync(AccrualReportRequestDto requestDto)
        {
            //var result = await GetAccrualReportAsync(requestDto, false);
            var result = await GetAcrReportListAsync(requestDto, false);
            MemoryStream stream = new();
            var config = new OpenXmlConfiguration()
            {
                TableStyles = TableStyles.None,
                AutoFilter = false

            };
            stream.SaveAs(result.Items, true, "Sheet1", configuration: config);
            stream.Seek(0, SeekOrigin.Begin);
            return stream;
        }
        /// <summary>
        /// OEC全流程报表
        /// </summary>
        /// <returns></returns>
        public async Task<PagedResultDto<OECFullProcessReportDto>> GetOECFullProcessReportAsync()
        {
            var queryablePR = (await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            var queryablePRdetail = (await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync()).AsNoTracking();
            var queryableGR = (await LazyServiceProvider.LazyGetService<IPurGRApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            var queryablePA = (await LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>().GetQueryableAsync()).AsNoTracking();

            var queryablePO = (await LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            var queryablePOd = (await LazyServiceProvider.LazyGetService<IPurPOApplicationDetailsRepository>().GetQueryableAsync()).AsNoTracking();
            var querySubBudget = (await LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>().GetQueryableAsync()).AsNoTracking();
            var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var PoQuery = queryablePO.GroupJoin(queryablePOd, a => a.Id, b => b.POApplicationId, (a, b) => new { a, b })
                .SelectMany(a => a.b.DefaultIfEmpty(), (a, b) => new { Po = a.a, Pod = b });

            var query = queryablePR.GroupJoin(queryablePRdetail, a => a.Id, b => b.PRApplicationId, (a, b) => new { Pr = a, Prd = b })
                                 .SelectMany(a => a.Prd.DefaultIfEmpty(), (a, b) => new { Pr = a.Pr, Prd = b })
                                 .GroupJoin(querySubBudget, a => a.Pr.SubBudgetId, b => b.Id, (a, b) => new { a, b })
                                 .SelectMany(a => a.b.DefaultIfEmpty(), (Pr, b) => new { Pr.a.Pr, Pr.a.Prd, Budget = b })
                                 .GroupJoin(queryableGR, a => a.Pr.Id, b => b.PrId, (a, b) => new { a.Pr, a.Prd, a.Budget, Gr = b })
                                 .SelectMany(a => a.Gr.DefaultIfEmpty(), (a, b) => new { a.Pr, a.Prd, a.Budget, Gr = b })
                                 .GroupJoin(queryablePA, a => a.Gr.Id, b => b.GRId, (a, b) => new { a.Pr, a.Prd, a.Budget, a.Gr, Pa = b })
                                 .SelectMany(a => a.Pa.DefaultIfEmpty(), (a, b) => new { a.Pr, a.Prd, a.Budget, a.Gr, Pa = b })
                                 .GroupJoin(PoQuery, a => a.Gr.PoId, b => b.Po.Id, (a, b) => new { a.Pr, a.Prd, a.Budget, a.Gr, a.Pa, Po = b })
                                 .SelectMany(a => a.Po.DefaultIfEmpty(), (a, b) => new { a.Pr, a.Prd, a.Budget, a.Gr, a.Pa, Po = b.Po, Pod = b.Pod })
                                 .GroupJoin(queryablePR, a => a.Pr.MainMeetingPR, b => b.Id, (a, b) => new { a.Pr, a.Prd, a.Budget, a.Gr, a.Pa, Po = a.Po, a.Pod, b })
                                 .SelectMany(a => a.b.DefaultIfEmpty(), (a, b) => new { a.Pr, a.Prd, a.Budget, a.Gr, a.Pa, a.Po, a.Pod, MainMeetingPR = b.ApplicationCode ?? "" })
                                 .Select(a => new OECFullProcessReportDto
                                 {
                                     PRCode = a.Pr.ApplicationCode,
                                     PostDate = a.Pr.ApplyTime,
                                     PRInitiator = a.Pr.ApplyUserIdName,
                                     ApplyUserDeptName = a.Pr.ApplyUserDeptName,
                                     CostCenterName = a.Pr.CostCenterName,
                                     ActualInitiator = a.Pr.AgentIdName,
                                     HostVendorName = a.Pr.HostVendorIdName,
                                     Product = a.Pr.ProductIdsName,
                                     Bu = a.Pr.ApplyUserBuName,
                                     BudgetCode = a.Pr.SubBudgetCode,
                                     BudgetDescription = a.Budget.Description,
                                     BudgetOwner = a.Budget.OwnerId,
                                     Region = a.Pr.BudgetRegionName,
                                     MeetingType = a.Pr.MeetingType,
                                     MeetingTitle = a.Pr.MeetingTitle,
                                     ActiveNo = a.Pr.ActiveNo,
                                     IsEsignUsed = a.Pr.IsEsignUsed,
                                     PushSystem = a.Pr.PushSystem,
                                     ActiveLeader = a.Pr.ActiveLeader,
                                     ExpenseType = a.Pr.ExpenseType,
                                     CostNatureName = a.Prd.CostNatureName,
                                     ActiveHostCity = a.Pr.ActiveHostCity,
                                     AcitveHostAddress = a.Pr.AcitveHostAddress,
                                     ActiveType = a.Pr.ActiveType,
                                     ProjectType = a.Pr.ProjectType,
                                     HospitalDepartments = a.Pr.HospitalDepartments,
                                     //a.Pr.MeetingType,
                                     SerialMeetingType = a.Pr.SerialMeetingType,
                                     //这个要pr自关联
                                     MainMeetingPRCode = a.MainMeetingPR,
                                     Hospitals = a.Pr.Hospitals,
                                     DoctorsNum = a.Pr.DoctorsNum,
                                     Remark = a.Pr.Remark,
                                     TotalAmount = a.Pr.TotalAmount,
                                     PRStatus = a.Pr.Status,
                                     PayMethod = a.Prd.PayMethod,
                                     HcpLevelName = a.Prd.HcpLevelName,
                                     HcpHospital = a.Prd.Hospital,
                                     OriginalVendorName = a.Prd.OriginalVendorName,
                                     VendorName = a.Prd.VendorName,
                                     GrVendorName = a.Gr.VendorName,
                                     //GrVendorCode=a.Gr.VendorId,
                                     GrVendorType = a.Prd.VendorTypeName,
                                     CityIdName = a.Prd.CityIdName,
                                     //讲者类型
                                     VendorType = a.Prd.VendorTypeName,
                                     SlideTypeName = a.Prd.SlideTypeName,
                                     SlideName = a.Prd.SlideName,
                                     ServiceDuration = a.Prd.ServiceDuration,
                                     BackUpVendors = a.Prd.BackUpVendors,
                                     ExecutorName = a.Prd.ExecutorName,
                                     PaymentTerm = a.Po.PaymentTerm,
                                     PrdTotalAmount = a.Prd.TotalAmount,
                                     //财务科目todo
                                     EstimateDate = a.Prd.EstimateDate,
                                     Content = a.Prd.Content,
                                     PRApproveDate = "",
                                     POCode = a.Po.ApplicationCode,
                                     POPostDate = a.Po.ApplyTime,
                                     POAmount = a.Po.TotalAmount,
                                     POStatus = a.Po.Status,
                                     GRCode = a.Gr.ApplicationCode,
                                     GRAmount = a.Gr.PaymentExcludingTaxAmount,
                                     //待确定
                                     //GRDate=a.Gr.RE,
                                     GRStatus = a.Gr.Status,
                                     PACode = a.Pa.ApplicationCode,
                                     PAPostDate = a.Pa.ApplyTime,
                                     PAAmount = a.Pa.PayTotalAmount,
                                     PAStatus = a.Pa.Status,
                                     //待确定
                                     EBankingDate = a.Pa.ApplyTime,
                                     Finished = true,
                                 });
            var Count = await query.CountAsync();
            var datas = query.Take(10).ToList();

            return new PagedResultDto<OECFullProcessReportDto>() { Items = datas, TotalCount = Count };
        }
        /// <summary>
        /// EPD全流程报表
        /// </summary>
        /// <returns></returns>
        public async Task<PagedResultDto<EPDProcessReportDto>> GetEPDProcessReportAsync(EPDReportRequestDto requestDto, bool isPage = true)
        {
            var queryablePR = (await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            var queryablePRdetail = (await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync()).AsNoTracking();
            var querySubBudget = (await LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>().GetQueryableAsync()).AsNoTracking();
            var queryableUser = (await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync()).AsNoTracking();
            var queryableGR = (await LazyServiceProvider.LazyGetService<IPurGRApplicationRepository>().GetQueryableAsync()).AsNoTracking();

            var backupVendor = (await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailBackupVendorRepository>().GetQueryableAsync()).AsNoTracking();
            var onlineMeeting = (await LazyServiceProvider.LazyGetService<IInteOnlineMeetingRepository>().GetQueryableAsync()).AsNoTracking();

            var budgetQuery = querySubBudget.GroupJoin(queryableUser, a => a.OwnerId, b => b.Id, (a, b) => new { Budget = a, owner = b })
                .SelectMany(a => a.owner.DefaultIfEmpty(), (a, b) => new { a.Budget, ownerName = b.Name });
            //获取Pr相关数据
            var PrInfo = queryablePR
                .GroupJoin(onlineMeeting, a => a.Id, b => b.PRApplicationId, (a, b) => new { PR = a, onlineMeeting = b })
                .SelectMany(a => a.onlineMeeting.DefaultIfEmpty(), (a, b) => new { a.PR, Om = b })
                .GroupJoin(queryablePR, a => a.PR.Id, b => b.MainMeetingPR, (a, b) => new { PR = a.PR, a.Om, mm = b })
                .SelectMany(a => a.mm.DefaultIfEmpty(), (a, b) => new
                {
                    a.PR,
                    a.Om,
                    Mm = b,

                }).GroupJoin(budgetQuery, a => a.PR.SubBudgetId, b => b.Budget.Id, (a, b) => new { a.PR, a.Mm, a.Om, Budget = b })
                 .SelectMany(a => a.Budget.DefaultIfEmpty(), (a, b) => new PrInfo
                 {
                     Id = a.PR.Id,
                     PRCode = a.PR.ApplicationCode,
                     PostDate = a.PR.ApplyTime,
                     PRInitiator = a.PR.ApplyUserIdName,
                     Department = a.PR.ApplyUserDeptName,
                     BudgetCode = a.PR.SubBudgetCode,
                     BudgetOwnerName = b.ownerName,
                     LMM = b.Budget.LMMs,
                     RegionalAssistants = b.Budget.RegionalAssistants,
                     Bu2 = b.Budget.Bu2,
                     TotalAmount = a.PR.TotalAmount,
                     BudgetRegionName = a.PR.BudgetRegionName,
                     ProductIdsName = a.PR.ProductIdsName,
                     ActiveType = a.PR.ActiveType,
                     ActiveHostCity = a.PR.ActiveHostCity,
                     AgentIdName = a.PR.AgentIdName,
                     ProjectType = a.PR.ProjectType,
                     MeetingType = a.PR.MeetingType,
                     Hospitals = a.PR.Hospitals,
                     HospitalsName = a.PR.HospitalsName,
                     HospitalDepartments = a.PR.HospitalDepartments,
                     HospitalDepartmentsName = a.PR.HospitalDepartmentsName,
                     DoctorsNum = a.PR.DoctorsNum,
                     MeetingTitle = a.PR.MeetingTitle,
                     IsEsignUsed = a.PR.IsEsignUsed,
                     SerialMeetingType = a.PR.SerialMeetingType,
                     MainMeetingPR = a.Mm.ApplicationCode,
                     CommittedAmount = a.PR.TotalAmount,
                     ActualNumber = a.Om.ActualNumber,
                     HostVendorIdName = a.PR.HostVendorIdName,

                 });
            //获取跟Pr detail项目数据
            var PRDetailInfo = queryablePRdetail.GroupJoin(backupVendor, a => a.Id, b => b.PRApplicationDetailId, (a, b) => new PRDetailInfo
            {
                Id = a.Id,
                PRId = a.PRApplicationId,
                PayMethod = a.PayMethod,
                OriginalEstimateDate = a.OriginalEstimateDate,
                EstimateDate = a.EstimateDate,
                Content = a.Content,
                ExecutorName = a.ExecutorName,
                VendorName = a.VendorName,
                VendorType = a.VendorTypeName,
                Hospital = a.Hospital,
                StandardDepartment = a.StandardDepartment,
                SlideTypeName = a.SlideTypeName,
                SlideName = a.SlideName,
                ServiceDuration = a.ServiceDuration,
                CostNatureName = a.CostNatureName,
                HcpLevelName = a.HcpLevelName,
                BackUpVendors = b.Select(a => a.VendorName).JoinAsString(","),

            });
            var query = PrInfo.GroupJoin(PRDetailInfo, a => a.Id, b => b.PRId, (a, b) => new { PR = a, Prd = b })
                 .SelectMany(a => a.Prd.DefaultIfEmpty(), (a, b) => new { a.PR, Prd = b })
                 .GroupJoin(queryableGR, a => a.PR.Id, b => b.PrId, (a, b) => new { a.PR, a.Prd, Gr = b })
                 .SelectMany(a => a.Gr.DefaultIfEmpty(), (a, b) => new EPDProcessReportDto
                 {
                     PRCode = a.PR.PRCode,
                     PostDate = a.PR.PostDate,
                     PRInitiator = a.PR.PRInitiator,
                     Department = a.PR.Department,
                     BudgetCode = a.PR.BudgetCode,
                     BudgetOwnerName = a.PR.BudgetOwnerName,
                     LMM = a.PR.LMM,
                     RegionalAssistants = a.PR.RegionalAssistants,
                     Bu2 = a.PR.Bu2,
                     PayMethod = a.Prd.PayMethod,
                     TotalAmount = a.PR.TotalAmount,
                     CommittedAmount = a.PR.TotalAmount,
                     //大区
                     BudgetRegionName = a.PR.BudgetRegionName,
                     ProductIdsName = a.PR.ProductIdsName,
                     OriginalEstimateDate = a.Prd.OriginalEstimateDate,
                     EstimateDate = a.Prd.EstimateDate,
                     //实际活动日期（GR日期）
                     GRApplyTime = b.ApplyTime,
                     ActiveType = a.PR.ActiveType,
                     Content = a.Prd.Content,
                     ActiveHostCity = a.PR.ActiveHostCity,
                     AgentIdName = a.PR.AgentIdName,
                     ExecutorName = a.Prd.ExecutorName,
                     ProjectType = a.PR.ProjectType,
                     MeetingType = a.PR.MeetingType,
                     VendorName = a.Prd.VendorName,
                     VendorType = a.Prd.VendorType,
                     Hospital = a.Prd.Hospital,
                     StandardDepartment = a.Prd.StandardDepartment,
                     Hospitals = a.PR.Hospitals,
                     HospitalDepartments = a.PR.HospitalDepartments,
                     HospitalsNames = a.PR.HospitalsName,
                     HospitalDepartmentsName = a.PR.HospitalDepartmentsName,
                     DoctorsNum = a.PR.DoctorsNum,
                     MeetingTitle = a.PR.MeetingTitle,
                     SlideTypeName = a.Prd.SlideTypeName,
                     SlideName = a.Prd.SlideName,
                     HostVendorIdName = a.PR.HostVendorIdName,
                     ServiceDuration = a.Prd.ServiceDuration,
                     BackUpVendors = a.Prd.BackUpVendors,
                     CostNatureName = a.Prd.CostNatureName,
                     //实际服务时长
                     ActualNumber = a.PR.ActualNumber,
                     IsEsignUsed = a.PR.IsEsignUsed,
                     SerialMeetingType = a.PR.SerialMeetingType,
                     MainMeetingPR = a.PR.MainMeetingPR,
                     GRStatus = b.Status,
                     DeliveryMode = b.DeliveryMode,
                     HcpLevelName = a.Prd.HcpLevelName,
                 })
                 ;
            //var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            //var hospitals = (await dataverseService.GetAllHospitals(stateCode: null)).ToDictionary(s => s.Id, s => s.Name);
            //var department = (await dataverseService.GetAllDepartments(stateCode: null)).ToDictionary(s => s.Id, s => s.Name);
            var Count = await query.CountAsync();
            query = query.OrderByDescending(o => o.PostDate).PagingIf(requestDto, isPage);
            var datas = query.ToList();
            //datas.ForEach(e =>
            //{
            //    //e.HospitalName = GetHospitalsName(e.Hospital, hospitals);
            //    e.HospitalsNames = GetHospitalsName(e.Hospitals, hospitals);
            //    e.HospitalDepartmentsName = GetDepartmentName(e.HospitalDepartments, department);
            //});
            return new PagedResultDto<EPDProcessReportDto>() { Items = datas, TotalCount = Count };
        }
        /// <summary>
        /// Accrual报表
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        public async Task<Stream> ExportToExcelEPDProcessReportAsync(EPDReportRequestDto requestDto)
        {
            var result = await GetEpdReportListAsync(requestDto, false);
            MemoryStream stream = new();
            var config = new OpenXmlConfiguration()
            {
                TableStyles = TableStyles.None,
                AutoFilter = false
            };
            stream.SaveAs(result.Items, true, "Sheet1", configuration: config);
            stream.Seek(0, SeekOrigin.Begin);
            return stream;
        }
        /// <summary>
        /// 获取医院
        /// </summary>
        /// <param name="hospitalIds"></param>
        /// <param name="hospitals"></param>
        /// <returns></returns>
        private static string GetHospitalsName(string hospitalIds, Dictionary<Guid, string> hospitals)
        {
            if (string.IsNullOrWhiteSpace(hospitalIds)) return "";
            var Ids = hospitalIds.Split(',', StringSplitOptions.RemoveEmptyEntries).Select(s => Guid.Parse(s)).ToList();
            var names = hospitals.Where(m => Ids.Contains(m.Key)).Select(s => s.Value).JoinAsString(",");
            return names;
        }
        private static string GetDepartmentName(string departments, Dictionary<Guid, string> offices)
        {
            if (string.IsNullOrWhiteSpace(departments)) return "";
            var Ids = departments.Split(',', StringSplitOptions.RemoveEmptyEntries).Select(Guid.Parse).ToList();
            var names = offices.Where(m => Ids.Contains(m.Key)).Select(s => s.Value).JoinAsString(",");
            return names;
        }
        private static string GetProductName(Guid? productId, IEnumerable<ProductDto> offices)
        {
            if (!productId.HasValue) return "";
            var Code = offices.FirstOrDefault(m => m.Id == productId)?.Code;
            return Code;
        }
        /// <summary>
        /// 查询Pr信息
        /// </summary>
        /// <param name="pRApplications"></param>
        /// <param name="pRApplicationDetails"></param>
        /// <returns></returns>
        private IQueryable<PRReportDto> QueryPRInfo(
            IQueryable<PurPRApplication> pRApplications,
            IQueryable<PurPRApplicationDetail> pRApplicationDetails,
            IQueryable<BdMasterBudget> bdMasters,
            IQueryable<IdentityUser> identityUsers
            )
        {
            var budgetQuery = bdMasters.GroupJoin(identityUsers, a => a.OwnerId, b => b.Id, (a, b) => new { Budget = a, owner = b })
        .SelectMany(a => a.owner.DefaultIfEmpty(), (a, b) => new { a.Budget.Id, a.Budget.BudgetAmount, ownerName = b.Name });
            return pRApplications.GroupJoin(pRApplicationDetails, a => a.Id, b => b.PRApplicationId, (a, b) => new { Pr = a, Prd = b })
                .SelectMany(a => a.Prd.DefaultIfEmpty(), (a, b) => new { a.Pr, Prd = b })
                .GroupJoin(budgetQuery, a => a.Pr.BudgetId, b => b.Id, (a, b) => new { a.Pr, a.Prd, budget = b })
                 .SelectMany(a => a.budget.DefaultIfEmpty(), (a, b) => new PRReportDto
                 {
                     Id = a.Pr.Id,
                     PrCode = a.Pr.ApplicationCode,
                     PostDate = a.Pr.ApplyTime,
                     PRInitiator = a.Pr.ApplyUserIdName,
                     Principal = a.Pr.AgentIdName,
                     Company = a.Pr.CompanyIdName,
                     Bu = a.Pr.ApplyUserBuName,
                     BudgetCode = a.Pr.BudgetCode,
                     BudgetOwner = b.ownerName,
                     BudgetAmount = b.BudgetAmount,
                     PRAmount = a.Pr.TotalAmount,
                     ExpenseTypeName = a.Pr.ExpenseTypeName,
                     PRStatus = a.Pr.Status,
                     PRItemNo = a.Prd.RowNo,
                     PRItemPaymenType = a.Prd.PayMethod,
                     HCPTIER = a.Prd.HcpLevelName,
                     HCPHospital = a.Prd.Hospital,
                     OriginalVendorName = a.Prd.OriginalVendorName,
                     VendorName = a.Prd.VendorName,
                     VendorType = a.Prd.VendorTypeName,
                     PRItemAmount = a.Prd.TaxAmount,
                     OrignalActivityDate = a.Prd.EstimateDate,
                     PRContent = a.Prd.Content,
                     IsPushed = a.Prd.PushFlag == PushFlagEnum.Pushed,
                     PushDate = a.Prd.PushTime,
                     TotalProcessDays = a.Pr.ApprovedDate == null ? null : (a.Pr.ApprovedDate.Value - a.Pr.CreationTime).Days,
                     PRApproveDate = a.Pr.ApprovedDate,
                     PRItemProcessDays = a.Prd.PushTime == null ? null : (a.Prd.PushTime.Value - a.Pr.CreationTime).Days,
                     CompanyCode = a.Pr.CompanyCode,
                     BuId = a.Pr.ApplyUserBu,
                     CostCenterCode = a.Pr.CostCenterCode,
                     CostNatureCode = a.Prd.CostNatureCode,
                     ProductId = a.Prd.ProductId,
                     CityId = a.Prd.CityId,
                     IsRecoil = a.Prd.HedgePrDetailId != null,
                 });
        }
        /// <summary>
        /// 查询Po信息
        /// </summary>
        /// <param name="pOApplications"></param>
        /// <param name="pOApplicationDetails"></param>
        /// <returns></returns>
        private static IQueryable<POReportDto> QueryPOInfo(IQueryable<PurPOApplication> pOApplications, IQueryable<PurPOApplicationDetails> pOApplicationDetails) =>
                        pOApplications.GroupJoin(pOApplicationDetails, a => a.Id, b => b.POApplicationId, (a, b) => new { PO = a, Pod = b })
            .SelectMany(a => a.Pod.DefaultIfEmpty(), (a, b) => new POReportDto
            {
                POId = a.PO.Id,
                PRId = a.PO.PRId,
                POCode = a.PO.ApplicationCode,
                POPostDate = a.PO.ApplyTime,
                POInitiator = a.PO.ApplyUserName,
                POAmountTax = a.PO.TotalAmountTax,
                POAmount = a.PO.TotalAmount,
                POTotalBalanceAmount = b.TotalAmount - b.TotalAmountNoTax,
                POVAT = b.TaxAmount,
                POVATNoTax = b.TotalAmount - b.TotalAmountNoTax - b.TaxAmount,
                POStatus = a.PO.Status,
                POApproveDate = a.PO.ApprovedDate,
                POProcessDays = a.PO.ApprovedDate == null ? null : (a.PO.ApprovedDate.Value - a.PO.CreationTime).Days,
            });
        /// <summary>
        /// 获取Gr
        /// </summary>
        /// <param name="purGRApplications"></param>
        /// <param name="grHistories"></param>
        /// <param name="bpcsAvms"></param>
        /// <param name="bpcsPmfvms"></param>
        /// <returns></returns>
        private static IQueryable<GRReportDto> QueryGRInfo(IQueryable<PurGRApplication> purGRApplications,
            IQueryable<PurGRApplicationDetailHistory> grHistories,
            IQueryable<BpcsAvm> bpcsAvms, IQueryable<BpcsPmfvm> bpcsPmfvms)
        {
            var listData = bpcsAvms.Join(bpcsPmfvms, a => new { VendorNum = a.Vendor, Vmcmpy = a.Vcmpny },
                b => new { VendorNum = b.Vnderx, b.Vmcmpy }, (a, b) => new { bpcsAvmId = a.Id, bpcsPmfvmVcrdte = b.Vcrdte, bpcsPmfvmVctime = b.Vctime });
            return purGRApplications
                 .GroupJoin(listData, a => a.VendorId, b => b.bpcsAvmId, (a, b) => new { Gr = a, bpcs = b }).SelectMany(a => a.bpcs.DefaultIfEmpty(), (a, b) => new { Gr = a.Gr, Bpcs = b })
                 .GroupJoin(grHistories, a => a.Gr.Id, b => b.GRApplicationId, (a, b) => new { Gr = a.Gr, a.Bpcs, Grh = b })
                    .Select(a => new GRReportDto
                    {
                        GRId = a.Gr.Id,
                        PRId = a.Gr.PrId,
                        GRCode = a.Gr.ApplicationCode,
                        GRVendorName = a.Gr.VendorName,
                        GRVendorCode = a.Gr.VendorCode,
                        GrPostDate = a.Gr.ApplyTime,
                        bpcsPmfvmVcrdte = a.Bpcs.bpcsPmfvmVcrdte,
                        bpcsPmfvmVctime = a.Bpcs.bpcsPmfvmVctime,
                        GRAmount = a.Gr.PaymentExcludingTaxAmount,
                        GRAmountNoTax = a.Gr.PaymentExcludingTaxAmount,
                        GRVAT = a.Grh.Sum(s => s.ReceivedAmount),
                        HistoricalReceiptAmount = a.Grh.Sum(s => s.ReceivedAmount),
                        HistoricalReceiptAmountNoTax = a.Grh.Sum(s => s.ReceivedAmount),
                        HistoricalReceiptAmountTax = a.Grh.Sum(s => s.ReceivedAmount),
                        GRDate = a.Gr.ApplyTime,
                        GRStatus = a.Gr.Status,
                    });
        }
        /// <summary>
        /// 获取PA信息
        /// </summary>
        /// <param name="pAApplications"></param>
        /// <param name="applicationInvoices"></param>
        /// <returns></returns>
        private static IQueryable<PAReportDto> QueryPAInfo(IQueryable<PurPAApplication> pAApplications, IQueryable<PurPAApplicationDetail> applicationDetails, IQueryable<FinanceCashierPaymentInfo> finances)
        {
            //获取金额
            var InvoiceAmount = applicationDetails.Select(m => new
            {
                m.PurPAApplicationId,
                Amount = m.PaymentAmount,
                AmountNoTax = m.PaymentAmount * (1 - (string.IsNullOrEmpty(m.TaxRate) ? 0 : Convert.ToDecimal(m.TaxRate))),
                taxes = m.PaymentAmount * (string.IsNullOrEmpty(m.TaxRate) ? 0 : Convert.ToDecimal(m.TaxRate)),
            }).GroupBy(g => g.PurPAApplicationId, p => p, (key, b) => new
            {
                PAId = key,
                Amount = b.Sum(a => a.Amount),
                AmountNoTax = b.Sum(a => a.AmountNoTax),
                taxes = b.Sum(a => a.taxes),
            });
            //网银打款日期
            var ebanking = finances.OrderByDescending(o => o.PaymentDate).GroupBy(g => g.PAApplicationCode, p => p, (key, s) => new
            {
                PACode = key,
                s.First().PaymentDate,
                s.First().Remark,
                s.First().MPStatus,
            });
            //最新
            var latestdDate = pAApplications.OrderByDescending(c => c.CreationTime).GroupBy(a => a.GRId, g => g, (key, d) => new { PaId = d.First().Id, LatestData = d.First().ApplyTime });
            //财务复审通过
            var approvalAmount = pAApplications.Where(m => m.Status == PurPAApplicationStatus.FinancialReview).Join(InvoiceAmount, a => a.Id, b => b.PAId, (a, b) => new { a.Id, b.Amount, b.AmountNoTax, b.taxes });
            return pAApplications
                .GroupJoin(latestdDate, a => a.Id, b => b.PaId, (a, b) => new { PA = a, latestds = b })
                .SelectMany(a => a.latestds.DefaultIfEmpty(), (a, b) => new { a.PA, latestds = b })
                .Join(InvoiceAmount, a => a.PA.Id, b => b.PAId, (a, b) => new { PA = a.PA, a.latestds, tax = b })
                .GroupJoin(approvalAmount, a => a.PA.Id, b => b.Id, (a, b) => new { PA = a.PA, a.latestds, a.tax, RPA = b })
                .SelectMany(a => a.RPA.DefaultIfEmpty(), (a, b) => new { a.PA, a.latestds, a.tax, RPA = b })
                .GroupJoin(ebanking, a => a.PA.ApplicationCode, b => b.PACode, (a, b) => new { a.PA, a.latestds, a.tax, a.RPA, ebanking = b })

                .SelectMany(a => a.ebanking.DefaultIfEmpty(), (a, b) => new PAReportDto
                {
                    PAId = a.PA.Id,
                    GrId = a.PA.GRId,
                    PACode = a.PA.ApplicationCode,
                    IsLastPayment = a.PA.IsLastPayment,
                    LatestPA = a.PA.Id == a.latestds.PaId,
                    DateOfLatestPA = a.latestds.LatestData,
                    PAPostDate = a.PA.ApplyTime,
                    PAAmount = a.PA.PayTotalAmount,
                    PAAmountNoTax = a.tax.AmountNoTax,
                    PAVAT = a.tax.taxes,
                    PATotalAmountPaid = a.RPA.Amount,
                    PATotalAmountPaidNoTax = a.RPA.AmountNoTax,
                    PATotalAmountPaidTax = a.RPA.taxes,
                    VendorScore = 0,
                    IFOReceivedPADate = a.PA.AcceptedTime,
                    IFOReceivedPAPostProcessDays = a.PA.AcceptedTime == null ? null : (a.PA.AcceptedTime - a.PA.ApplyTime).Value.Days,
                    PAReviewDate = a.PA.ApprovedDate,
                    PAStatus = a.PA.Status,
                    PAProcessDays = a.PA.ApprovedDate == null ? null : (a.PA.ApprovedDate - a.PA.ApplyTime).Value.Days,
                    EBankingDate = b.PaymentDate,
                    EBankingStatus = b.MPStatus,
                    EBankingComment = b.Remark,
                });
        }
        /// <summary>
        /// PA获取Bpcs对应数据
        /// </summary>
        /// <param name="pAApplications"></param>
        /// <param name="bpcsAmls"></param>
        /// <param name="bpcsGlhs"></param>
        private static IQueryable<MPDto> GetBpcsInfo(IQueryable<PurPAApplication> pAApplications, IQueryable<BpcsAml> bpcsAmls, IQueryable<BpcsGlh> bpcsGlhs)
        {
            var bpcsDatas = bpcsGlhs.Where(a => a.Lhreas == "APIIL" || a.Lhreas == "APV2L").OrderByDescending(s => s.UpdateTime)
            .GroupBy(g => g.Lhdref, (a, b) => new
            {
                key = a,//PA code
                //b.Last().Lhdref,//
                b.First().Lhjnen,
            })
            .GroupJoin(bpcsAmls, a => a.Lhjnen, b => b.Amlinv, (a, b) => new { aml = a, glhs = b })
            .SelectMany(s => s.glhs.DefaultIfEmpty(), (a, b) => new { a.aml.key, a.aml.Lhjnen, b.Amlpda });

            var Mp1 = pAApplications.Select(s => new { s.Id, code = s.ApplicationCode.Substring(1) })
          .GroupJoin(bpcsDatas, a => a.code, b => b.key, (a, b) => new { PA = a, bpcs = b })
          .SelectMany(s => s.bpcs.DefaultIfEmpty(), (a, b) => new { a.PA.Id, a.PA.code, b.Lhjnen, b.Amlpda });
            var Mp = Mp1.GroupJoin(bpcsDatas, a => string.Concat(a.code.Substring(1), a.code.Substring(0, 1)), b => b.key, (a, b) => new { PA = a, bpcsV2 = b })
            .SelectMany(s => s.bpcsV2.DefaultIfEmpty(), (a, b) => new MPDto
            {
                Id = a.PA.Id,
                Lhjnen = b.Lhjnen != null ? b.Lhjnen : a.PA.Lhjnen,
                Amlpda = b.Amlpda != null ? b.Amlpda : a.PA.Amlpda,
            });
            return Mp;
        }
        /// <summary>
        /// 根据id获取审批人
        /// </summary>
        /// <param name="Ids"></param>
        private async Task<Dictionary<Guid, ReportApproverDto>> GetWorkFlowTaskByIds(List<string> Ids)
        {
            var queryInstans = new QueryExpression("spk_workflowinstance");
            var query = new QueryExpression("spk_workflowtask");
            query.Criteria.AddCondition(new ConditionExpression("spk_businessformid", ConditionOperator.In, Ids));
            query.ColumnSet.AddColumns("spk_approvalstatus", "spk_approver", "spk_businessformid", "modifiedon");
            query.AddOrder("createdon", OrderType.Descending);
            LinkEntity linkEntity = query.AddLink("spk_workflowstep", "spk_workflowstep", "spk_workflowstepid", JoinOperator.Inner);
            linkEntity.Columns.AddColumns("spk_step");
            linkEntity.EntityAlias = "step";
            var entities = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);
            var datas = entities.Select(
                  s => new
                  {
                      Id = Guid.Parse(s.GetAttributeValue<string>("spk_businessformid")),
                      approverId = s.GetAttributeValue<EntityReference>("spk_approver")?.Id,
                      approverName = s.GetAttributeValue<EntityReference>("spk_approver")?.Name,
                      approvalStatus = s.GetAttributeValue<OptionSetValue>("spk_approvalstatus").Value,
                      ModifiedOn = s.GetAttributeValue<DateTime>("modifiedon"),
                      step = int.Parse(s.GetAttributeValue<AliasedValue>("step.spk_step").Value.ToString())
                  });
            var queryApprover = datas.GroupBy(s => s.Id, g => g, (key, b) =>
               {
                   //费用循环审批step步骤小于等于700，取最大的一步
                   var PrBusiness = b.Where(a => a.step <= 700).MaxBy(s => s.step);
                   //财务审批step1100，1200.
                   var PrFinance = b.Where(a => a.step >= 1100).MaxBy(s => s.step);
                   return new ReportApproverDto
                   {
                       Id = key,
                       FirstApprovalName = b.FirstOrDefault()?.approverName,//审批第一步审批
                       //LastApprovalName = ,//最后一步审批
                       PrBusinessApprovalName = PrBusiness?.approverName,
                       //审批过后的修改时间
                       PrBusinessApprovalTime = PrBusiness != null && PrBusiness?.approvalStatus != 100000000 ? PrBusiness.ModifiedOn : null,
                       PrFinanceApprovalName = PrFinance?.approverName,
                       //审批过后的修改时间
                       PrFinanceApprovalTime = PrFinance != null && PrFinance?.approvalStatus != 100000000 ? PrFinance?.ModifiedOn : null,
                       CurrentApprovalName = b.FirstOrDefault(a => a.approvalStatus == 100000000)?.approverName ?? b.LastOrDefault()?.approverName,//当前审批
                   };
               });
            return queryApprover.ToDictionary(s => s.Id);
        }
        /// <summary>
        /// 专业服务费报税
        /// </summary>
        /// <param name="requestDto"></param>
        /// <param name="isPage"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<ProfessionalServicesResponseDto>> ProfessionalServicesAsync(ProfessionalServicesRequestDto requestDto, bool isPage = true)
        {
            var result = new PagedResultDto<ProfessionalServicesResponseDto>();
            var taxReportQuery = await LazyServiceProvider.LazyGetService<IProfessionalServiceTaxReportReadonlyRepostory>().GetQueryableAsync();
            var vendorQuery = await LazyServiceProvider.LazyGetService<IVendorReadonlyRepository>().GetQueryableAsync();
            var bpcsAvmQuery = await LazyServiceProvider.LazyGetService<IBpcsAvmReadonlyRepository>().GetQueryableAsync();
            var query = taxReportQuery
                .GroupJoin(vendorQuery.Select(a => new { a.Id, a.HandPhone }), a => a.VendorApplicationId, b => b.Id, (a, b) => new { taxReport = a, venders = b })
                .SelectMany(a => a.venders.DefaultIfEmpty(), (a, b) => new { a.taxReport, b.HandPhone })
                .Join(bpcsAvmQuery.Select(a => new { a.Id, a.Vmxcrt, a.Vtype }), a => a.taxReport.VendorId, b => b.Id, (a, b) => new { a.taxReport, a.HandPhone, b.Vmxcrt, b.Vtype })
                .WhereIf(!string.IsNullOrWhiteSpace(requestDto.PAApplicationCode), a => a.taxReport.ApplicationCode.Contains(requestDto.PAApplicationCode))
                .WhereIf(requestDto.Company.HasValue, a => a.taxReport.CompanyId == requestDto.Company.Value)
                .WhereIf(!string.IsNullOrWhiteSpace(requestDto.VendorCode), a => a.taxReport.VendorCode.Contains(requestDto.VendorCode))
                .WhereIf(!string.IsNullOrWhiteSpace(requestDto.BpcsPaidStatus), a => a.taxReport.PaidType == requestDto.BpcsPaidStatus)
                .WhereIf(requestDto.CostNatureCode != null && requestDto.CostNatureCode.Any(), a => requestDto.CostNatureCode.Contains(a.taxReport.CostNatureCode))
                .WhereIf(requestDto.BpcsPaidStarDate.HasValue, a => a.taxReport.PaidTime >= requestDto.BpcsPaidStarDate.Value.Date)
                .WhereIf(requestDto.BpcsPaidEndDate.HasValue, a => a.taxReport.PaidTime < requestDto.BpcsPaidEndDate.Value.Date.AddDays(1))
                .WhereIf(requestDto.UpdateStarDate.HasValue, a => a.taxReport.BpcsUpdateTime >= requestDto.UpdateStarDate.Value.Date)
                .WhereIf(requestDto.UpdateEndDate.HasValue, a => a.taxReport.BpcsUpdateTime < requestDto.UpdateEndDate.Value.Date.AddDays(1))
                .Select(a => new ProfessionalServicesResponseDto
                {
                    PAId = a.taxReport.PAId,
                    ApplicationCode = a.taxReport.ApplicationCode,
                    ApItemNo = a.taxReport.APNo,
                    ApDescription = a.taxReport.APDescribe,
                    ApplyUserName = a.taxReport.ApplyUserName,
                    BuName = a.taxReport.ApplyUserBuName,
                    COA = a.taxReport.COA,
                    CompanyName = a.taxReport.CompanyName,
                    VendorCode = a.taxReport.VendorCode,
                    VendorType = (a.taxReport.VendorType == null || a.taxReport.VendorType == "") ? a.Vtype : a.taxReport.VendorType,
                    VendorName = a.taxReport.VendorName,
                    PhoneNumber = a.HandPhone,
                    //2938 身份证号需要去掉空格和F/M，只保留证件编码  20250218 ytw
                    IDCard = string.IsNullOrWhiteSpace(a.Vmxcrt) ? "" : a.Vmxcrt.Replace(" M", "").Replace(" F", ""),
                    PaidAmount = a.taxReport.PaidAmount,
                    BpcsPaidType = a.taxReport.PaidType,
                    BpcsPaidTime = a.taxReport.PaidTime,
                    BpcsUpdateTime = a.taxReport.BpcsUpdateTime,
                })
                .WhereIf(requestDto.VendorType != null && requestDto.VendorType.Any(), a => requestDto.VendorType.Contains(a.VendorType));

            if (isPage)
            {
                var queryData = query.OrderByDescending(a => a.ApplicationCode).ThenBy(a => a.ApItemNo).Skip(requestDto.PageIndex * requestDto.PageSize)
                    .Take(requestDto.PageSize)
                    .ToList();
                result.TotalCount = query.Count();
                result.Items = queryData;
            }
            else
            {
                result.Items = query.OrderByDescending(a => a.ApplicationCode).ThenBy(a => a.ApItemNo).ToList();
            }
            return result;
            #region OLD 
            //var queryablePA = (await LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            //var queryablePADetail = (await LazyServiceProvider.LazyGetService<IPurPAApplicationDetailRepository>().GetQueryableAsync()).AsNoTracking();
            //var queryableVendors = (await LazyServiceProvider.LazyGetService<IVendorApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            //var queryableVendorFinancials = (await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync()).AsNoTracking();
            ////BpcsGrh表
            //var bpcsGlhQuery = (await LazyServiceProvider.LazyGetService<IBpcsGlhRepository>().GetQueryableAsync()).AsNoTracking().Where(a => a.Lhreas == "APIIL" || a.Lhreas == "APV2L");
            //var bpcsGcrQuery = (await LazyServiceProvider.LazyGetService<IBpcsGcrRepository>().GetQueryableAsync()).AsNoTracking().Where(a => a.Crsg05 != "9999");
            //var bpcsAvmQuery = (await LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync()).AsNoTracking();
            //var bpcsAmlQuery = (await LazyServiceProvider.LazyGetService<IBpcsAmlRepository>().GetQueryableAsync()).AsNoTracking();

            //var queryVendorInfo = queryableVendors.Join(queryableVendorFinancials, a => a.Id, b => b.VendorId, (a, b) => new { a.HandPhone, b.VendorCode, b.Company }).Join(bpcsAvmQuery, a => new { a.VendorCode, a.Company },
            //    b => new { VendorCode = b.Vendor.ToString(), Company = b.Vcmpny.ToString() }, (a, b) => new { Id = b.Id, a.HandPhone, a.VendorCode, a.Company, b.Vtype, b.Vmxcrt });
            ////查询coa数据
            //var bpcsQuery = bpcsGlhQuery.Join(bpcsGcrQuery, a => a.Lhian, b => b.Crian, (a, b) => new
            //{
            //    COA = (b.Crsg01 != null ? b.Crsg01 + "." : "") +
            //          (b.Crsg02 != null ? b.Crsg02 + "." : "") +
            //          (b.Crsg03 != null ? b.Crsg03 + "." : "") +
            //          (b.Crsg04 != null ? b.Crsg04 + "." : "") +
            //          (b.Crsg05 != null ? b.Crsg05 + "." : "") +
            //          (b.Crsg06 != null ? b.Crsg06 : "") +
            //          (b.Crsg07 != null ? "." + b.Crsg07 : ""),
            //    Lhldes = a.Lhldes,//AP明细行描述
            //    Lhjnln = a.Lhjnln,//自增长的数字
            //    amount = a.Lhdram != 0 ? a.Lhdram : a.Lhcram,
            //    a.Lhdref,
            //    a.Lhjnen,
            //    a.UpdateTime,
            //    Lhreas = a.Lhreas,
            //}).GroupJoin(bpcsAmlQuery, a => a.Lhjnen, b => b.Amlinv, (a, aml) => new { glh = a, aml }).SelectMany(s => s.aml.DefaultIfEmpty(), (a, aml) => new
            //{
            //    a.glh.COA,
            //    a.glh.Lhldes,
            //    a.glh.Lhjnln,
            //    a.glh.amount,
            //    a.glh.Lhdref,
            //    a.glh.Lhjnen,
            //    glhUpdateTime = a.glh.UpdateTime,
            //    aml.Amlinv,
            //    aml.Amlpda,
            //    amlUpdateTime = aml.UpdateTime,
            //    Lhreas = a.glh.Lhreas,
            //});
            ////查询供应商相关数据
            //var queryFirst = queryablePA.GroupJoin(queryVendorInfo, a => a.VendorId, b => b.Id, (Pa, vendor) => new { Pa, vendor })
            //    .SelectMany(a => a.vendor.DefaultIfEmpty(), (a, b) => new { a.Pa, vendor = b })
            //    .Select(s => new
            //    {
            //        s.Pa.ApplicationCode,
            //        SpiltCode = s.Pa.ApplicationCode.Substring(1),
            //        s.Pa.ApplyUserName,
            //        s.Pa.ApplyUserBuName,
            //        s.Pa.CompanyName,
            //        s.Pa.VendorCode,
            //        s.Pa.VendorName,
            //        s.vendor.HandPhone,
            //        s.vendor.Vmxcrt,
            //        s.vendor.Vtype,
            //        s.Pa.CreationTime,
            //    })
            //    ;
            ////查询bpcs里面的数据
            //var queryBpcsData = queryFirst.GroupJoin(bpcsQuery, a => a.SpiltCode, b => b.Lhdref, (a, b) => new { pa = a, bpcs = b }).SelectMany(s => s.bpcs.DefaultIfEmpty(), (a, bpcs) => new BpcsDto
            //{
            //    ApplicationCode = a.pa.ApplicationCode,//PA申请单号
            //    ApplyUserName = a.pa.ApplyUserName,//PA申请人
            //    ApplyUserBuName = a.pa.ApplyUserBuName,//申请人所属BU
            //    CompanyName = a.pa.CompanyName,//PA申请所属公司
            //    VendorCode = a.pa.VendorCode,//供应商编码
            //    VendorName = a.pa.VendorName,//供应商名称
            //    HandPhone = a.pa.HandPhone,//讲者手机号
            //    Vmxcrt = a.pa.Vmxcrt,//讲者身份证号
            //    Vtype = a.pa.Vtype,//供应商类型
            //    Lhjnln = bpcs.Lhjnln,
            //    Lhldes = bpcs.Lhldes,
            //    COA = bpcs.COA,
            //    amount = bpcs.amount,
            //    Lhdref = bpcs.Lhdref,
            //    Lhjnen = bpcs.Lhjnen,
            //    glhUpdateTime = bpcs.glhUpdateTime,
            //    Amlinv = bpcs.Amlinv,
            //    Amlpda = bpcs.Amlpda,
            //    amlUpdateTime = bpcs.amlUpdateTime,
            //    Lhreas = bpcs.Lhreas,
            //    CreationTime = a.pa.CreationTime
            //});
            //////已经同步到bpcs,
            ////var syncBpcsQuery = queryBpcsData.Where(m => m.Lhdref != null);
            //////未同步到bpcs
            ////var notSyncBpcsQuery = queryBpcsData.Where(m => m.Lhdref == null);
            ////查询转置后的数据
            //var queryTranspositionData = queryFirst.Join(bpcsQuery, a => string.Concat(a.SpiltCode.Substring(1), a.SpiltCode.Substring(0, 1)), b => b.Lhdref, (a, bpcs) => new BpcsDto
            //{
            //    ApplicationCode = a.ApplicationCode,//PA申请单号
            //    ApplyUserName = a.ApplyUserName,//PA申请人
            //    ApplyUserBuName = a.ApplyUserBuName,//申请人所属BU
            //    CompanyName = a.CompanyName,//PA申请所属公司
            //    VendorCode = a.VendorCode,//供应商编码
            //    VendorName = a.VendorName,//供应商名称
            //    HandPhone = a.HandPhone,//讲者手机号
            //    Vmxcrt = a.Vmxcrt,//讲者身份证号
            //    Vtype = a.Vtype,//供应商类型
            //    Lhjnln = bpcs.Lhjnln,
            //    Lhldes = bpcs.Lhldes,
            //    COA = bpcs.COA,
            //    amount = bpcs.amount,
            //    Lhdref = bpcs.Lhdref,
            //    Lhjnen = bpcs.Lhjnen,
            //    glhUpdateTime = bpcs.glhUpdateTime,
            //    Amlinv = bpcs.Amlinv,//支付状态
            //    Amlpda = bpcs.Amlpda,
            //    amlUpdateTime = bpcs.amlUpdateTime,
            //    Lhreas = bpcs.Lhreas,
            //    CreationTime = a.CreationTime
            //});
            ////根据行号和申请单号取最新的数据行
            //var querylatestData = queryBpcsData.Concat(queryTranspositionData).OrderByDescending(o => o.CreationTime).ThenByDescending(o => o.glhUpdateTime)
            //    .GroupBy(x => new { x.ApplicationCode, x.Lhjnln }, g => g, (key, s) => s.FirstOrDefault()); //.GroupBy(x => new { x.ApplicationCode, x.Lhjnln }, (a, g) => g.First()).OrderByDescending(o => o.CreationTime)
            ////querylatestData = querylatestData.OrderByDescending(s => s.CreationTime);//这里加排序会报错，转不了sql，如果没办法只有写sql实现了
            ////var Count = await querylatestData.CountAsync();
            //querylatestData = querylatestData.PagingIf(requestDto, isPage);//.OrderByDescending(o => o.ApplicationCode)
            //var query = querylatestData.AsEnumerable().Select(s => new ProfessionalServicesResponseDto
            //{
            //    ApplicationCode = s.ApplicationCode,
            //    ApItemNo = s.Lhjnln,
            //    ApDescription = s.Lhldes,
            //    ApplyUserName = s.ApplyUserName,
            //    BuName = s.ApplyUserBuName,
            //    COA = s.COA,
            //    CompanyName = s.CompanyName,
            //    VendorCode = s.VendorCode,
            //    VendorType = s.Vtype,
            //    VendorName = s.VendorName,
            //    PhoneNumber = s.HandPhone,
            //    IDCard = s.Vmxcrt,
            //    PaidAmount = s.amount,
            //    BpcsPaidType = s.Amlinv != null ? "Paid" : s.Lhreas == "APV2L" ? "Void" : "Unpaid",//这里必须要先tolist,不然不能转义成sql
            //    BpcsPaidDate = s.Amlpda,
            //    UpdateDate = s.Amlinv != null ? s.amlUpdateTime : s.glhUpdateTime,
            //})
            //.WhereIf(!string.IsNullOrWhiteSpace(requestDto.PAApplicationCode), a => a.ApplicationCode.Contains(requestDto.PAApplicationCode))
            //.WhereIf(!string.IsNullOrWhiteSpace(requestDto.CompanyName), a => a.CompanyName.Contains(requestDto.CompanyName))
            //.WhereIf(!string.IsNullOrWhiteSpace(requestDto.VendorCode), a => a.VendorCode.Contains(requestDto.VendorCode))
            //.WhereIf(!string.IsNullOrWhiteSpace(requestDto.VendorType), a => a.VendorType.Contains(requestDto.VendorType))
            //.WhereIf(!string.IsNullOrWhiteSpace(requestDto.PaidType), a => a.BpcsPaidType.Contains(requestDto.PaidType))
            //.WhereIf(requestDto.StartUpdateDate.HasValue, a => a.UpdateDate >= requestDto.StartUpdateDate.Value.Date)
            //.WhereIf(requestDto.UpdateDateEnd.HasValue, a => a.UpdateDate < requestDto.UpdateDateEnd.Value.Date.AddDays(1));

            //var result = new PagedResultDto<ProfessionalServicesResponseDto>();
            //if (isPage)
            //{
            //    var queryData = query.OrderByDescending(a => a.UpdateDate).Skip(requestDto.PageIndex * requestDto.PageSize)
            //        .Take(requestDto.PageSize)
            //        .ToList();
            //    result.TotalCount = query.Count();
            //    result.Items = queryData;
            //}
            //else 
            //{
            //    result.Items = query.ToList();
            //}
            //return result;
            #endregion
        }
        /// <summary>
        /// 主预算记录报表
        /// </summary>
        public async Task<PagedResultDto<MasterBudgetResponseDto>> MasterBudgetReportAsync(MasterBudgetReportRequestDto request, bool isPage = true)
        {
            var queryMasterBudget = (await LazyServiceProvider.LazyGetService<IBdMasterBudgetReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
            var historyQuery = (await LazyServiceProvider.LazyGetService<IBdHistoryReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
            var queryableUser = (await LazyServiceProvider.LazyGetService<IIdentityUserReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
            var budget = queryMasterBudget.WhereIf(request.OwnerId.HasValue, m => m.OwnerId == request.OwnerId)
                .WhereIf(request.BuId.HasValue, m => m.BuId == request.BuId)
                .WhereIf(request.StartYear.HasValue, m => m.Year >= request.StartYear)
                .WhereIf(request.EndYear.HasValue, m => m.Year <= request.EndYear)
                .Join(queryableUser, a => a.OwnerId, b => b.Id, (bd, user) => new { bd.Id, bd.Code, bd.Description, user.Name, user.Email });
            var history = historyQuery.Join(queryableUser, a => a.CreatorId, b => b.Id, (hs, user) => new { hs.CreationTime, hs.OperateType, hs.OperateAmount, hs.Remark, hs.OperateContent, user.Name, TargetBudgetCode = hs.TargetBudgetCode, hs.BudgetId });

            var query = budget.GroupJoin(history, a => a.Id, b => b.BudgetId, (budget, history) => new
            {
                budget,
                history
            }).SelectMany(a => a.history.DefaultIfEmpty(), (a, his) => new MasterBudgetResponseDto
            {
                Code = a.budget.Code,
                Description = a.budget.Description,
                Name = a.budget.Name,
                Email = a.budget.Email,
                OperateName = his.Name,
                CreationTime = his.CreationTime,
                OperateAmount = his.OperateAmount,
                OperateType = his.OperateType,
                Remark = his.Remark,
                OperateContent = his.OperateContent,
                TargetBudgetCode = his.TargetBudgetCode,
                YearMonth = his.CreationTime.ToString("yyyy/MM")

            }).WhereIf(!string.IsNullOrEmpty(request.BudgetCode), m => m.Code.Contains(request.BudgetCode))
            .WhereIf(request.StartDate.HasValue, m => m.CreationTime >= request.StartDate)
            .WhereIf(request.EndDate.HasValue, m => m.CreationTime < request.EndDate.Value.AddDays(1))
            .OrderByDescending(o => o.Code).AsQueryable();
            var Count = await query.CountAsync();
            query = query.PagingIf(request, isPage);
            var datas = await query.ToListAsync();
            return new PagedResultDto<MasterBudgetResponseDto>() { Items = datas, TotalCount = Count };
        }
        /// <summary>
        /// 子预算预算记录报表
        /// </summary>
        /// <returns></returns>
        public async Task<PagedResultDto<SubBudgetResponseDto>> SubBudgetReportAsync(SubBudgetReportRequestDto request, bool isPage = true)
        {
            var querySubbudget = (await LazyServiceProvider.LazyGetService<IBdSubBudgetReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
            var historyQuery = (await LazyServiceProvider.LazyGetService<IBdHistoryReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
            var queryableUser = (await LazyServiceProvider.LazyGetService<IIdentityUserReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
            var queryableMonthly = (await LazyServiceProvider.LazyGetService<IBdMonthlyBudgetReadonlyRepository>().GetQueryableAsync()).AsNoTracking();

            var budget = querySubbudget.WhereIf(request.OwnerId.HasValue, m => m.OwnerId == request.OwnerId).Include(i => i.MasterBudget).Include(i => i.MonthlyBudgets)
                .WhereIf(request.BuId.HasValue, m => m.BuId == request.BuId)
                .WhereIf(request.StartYear.HasValue, m => m.MasterBudget.Year >= request.StartYear)
                .WhereIf(request.EndYear.HasValue, m => m.MasterBudget.Year <= request.EndYear)
                .Join(queryableUser, a => a.OwnerId, b => b.Id, (bd, user) => new { bd.Id, bd.Code, bd.Description, user.Name, user.Email, MasterCode = bd.MasterBudget.Code, Monthly = bd.MonthlyBudgets });
            var history = historyQuery.Include(i => i.SubBudgetMonthChangeHistorys).Join(queryableUser, a => a.CreatorId, b => b.Id, (hs, user) => new
            {
                hs.CreationTime,
                hs.OperateType,
                hs.OperateAmount,
                hs.Remark,
                hs.OperateContent,
                user.Name,
                TargetBudgetCode = hs.TargetBudgetCode,
                hs.BudgetId,
                ChangeMonths = hs.SubBudgetMonthChangeHistorys
            });

            var query = budget.Join(history, a => a.Id, b => b.BudgetId, (a, his) => new
            {
                Id = a.Id,
                Code = a.Code,
                Description = a.Description,
                Name = a.Name,
                Email = a.Email,
                OperateName = his.Name,
                CreationTime = his.CreationTime,
                OperateAmount = his.OperateAmount,
                OperateType = his.OperateType,
                Remark = his.Remark,
                OperateContent = his.OperateContent,
                TargetBudgetCode = his.TargetBudgetCode,
                ChangeMonths = his.ChangeMonths,
                Monthly = a.Monthly,
                MasterCode = a.MasterCode
            }).WhereIf(!string.IsNullOrEmpty(request.BudgetCode), m => m.MasterCode.Contains(request.BudgetCode))
            .WhereIf(!string.IsNullOrEmpty(request.SubBudgetCode), m => m.Code.Contains(request.SubBudgetCode))
            .WhereIf(request.StartDate.HasValue, m => m.CreationTime >= request.StartDate)
            .WhereIf(request.EndDate.HasValue, m => m.CreationTime < request.EndDate.Value.AddDays(1)).OrderByDescending(o => o.Code).AsQueryable();
            var Count = await query.CountAsync();
            query = query.PagingIf(request, isPage);

            var datas = (await query.ToListAsync()).Select(s => new SubBudgetResponseDto
            {
                Id = s.Id,
                Code = s.Code,
                Description = s.Description,
                OwnerName = s.Name,
                OwnerEmail = s.Email,
                OperateName = s.Name,
                CreationTime = s.CreationTime,
                OperateAmount = s.OperateAmount,
                OperateType = s.OperateType,
                MasterCode = s.MasterCode,
                Remark = s.Remark,
                OperateContent = s.OperateContent,
                TargetBudgetCode = s.TargetBudgetCode,
                YearMonth = s.CreationTime.ToString("yyyy/MM"),
                Monthlys = s.ChangeMonths.ToDictionary(k => k.Month, v => new AmountStutas { Amount = v.OperateAmount, Status = v.Status }),
            }).ToList();
            return new PagedResultDto<SubBudgetResponseDto>() { Items = datas, TotalCount = Count };
            //var a = await sqlRepository.SqlQueryRow<SubBudgetResponseDto>("SELECT [Id],[Remark] FROM [BdHistorys]");

            //var b = a.Where(x => x.Remark != null).Skip(2).Take(20).ToArray();
        }

        #region 申请审批记录报表 OLD

        /// <summary>
        /// 申请审批记录报表
        /// </summary>
        /// <returns></returns>
        public async Task<PagedResultDto<ApplicationApprovalRecordReportReponseDto>> ApplicationApprovalRecordReportAsync(ApplicationApprovalRecordReportRequestDto request, bool isPage = true)
        {
            var approvalRecordReportQuery = (await LazyServiceProvider.LazyGetService<IApprovalRecordReportReadonlyRepostory>().GetQueryableAsync()).AsNoTracking();
            var personCenterService = LazyServiceProvider.LazyGetService<IPersonCenterService>();
            var roleLevel = personCenterService.GetMyRoleLevel();
            TaskFormCategory? taskFormCategory = null;
            switch (request.ApplicationType)
            {
                case ApplicationDocumentType.Supplier:
                    taskFormCategory = TaskFormCategory.VendorApplication;
                    break;
                case ApplicationDocumentType.Purchase:
                    taskFormCategory = TaskFormCategory.PurchaseRequestApplication;
                    break;
                case ApplicationDocumentType.Receipt:
                    taskFormCategory = TaskFormCategory.GoodsReceiptApplication;
                    break;
                case ApplicationDocumentType.PurchaseOrder:
                    taskFormCategory = TaskFormCategory.PurhcaseOrderApplication;
                    break;
                case ApplicationDocumentType.PriceComparison:
                    taskFormCategory = TaskFormCategory.BiddingApplication;
                    break;
                case ApplicationDocumentType.BiddingExemption:
                    taskFormCategory = TaskFormCategory.BiddingWaiverApplication;
                    break;
                case ApplicationDocumentType.Justification:
                    taskFormCategory = TaskFormCategory.JustificationApplication;
                    break;
                case ApplicationDocumentType.Payment:
                    taskFormCategory = TaskFormCategory.PaymentApplication;
                    break;
                case ApplicationDocumentType.SpeakerAuthorization:
                    taskFormCategory = TaskFormCategory.OECSpeakerAuthApplication;
                    break;
                default:
                    break;
            }
            var ids = await personCenterService.GetIdsByRoles(roleLevel, taskFormCategory);

            //5030急急急【申请审批记录报表】查询列表及导出时，请不要排除"是否解决"="是"的
            var query = approvalRecordReportQuery
            .WhereIf(request.StartDate.HasValue, m => m.OperateTime >= request.StartDate)
            .WhereIf(request.EndDate.HasValue, m => m.OperateTime < request.EndDate.Value.AddDays(1))
            .WhereIf(request.ApplicationType.HasValue, m => m.ApplicationType == request.ApplicationType)
            .WhereIf(request.Status != null && request.Status.Any(), m => m.OperateStatus != null && request.Status.Contains(m.OperateStatus.Value))
            .WhereIf(request.OperatorId.HasValue, m => m.OperateUserId == request.OperatorId)
            .WhereIf(request.ApplicantId.HasValue, m => m.ApplyUserId == request.ApplicantId)
            .WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), m => m.ApplicationCode.Contains(request.ApplicationCode));
            switch (roleLevel)
            {
                case RoleLevel.Admin:
                    break;
                case RoleLevel.Manager:
                case RoleLevel.Leader:
                case RoleLevel.Owner:
                case RoleLevel.Applicant:
                case RoleLevel.Unkonw:
                default:
                    query = query.WhereIf(CurrentUser.Id.HasValue, a => ids.Contains(a.ApplyUserId.Value) || ids.Contains(a.AgentId ?? Guid.Empty) || ids.Contains(a.TransfereeId ?? Guid.Empty));
                    break;
            }
            query = query.OrderByDescending(a => a.OperateTime);
            var count = 0;
            if (isPage)
            {
                count = query.Count();
                query = query.Skip(request.PageIndex * request.PageSize).Take(request.PageSize);
            }

            var result = query.Select(a => new ApplicationApprovalRecordReportReponseDto
            {
                Id = a.ApplicationId,
                ApplicationCode = a.ApplicationCode,
                ApplyTime = a.ApplyTime,
                ApplyUserName = a.ApplyUserName,
                ApplyDeptName = a.ApplyUserDeptName,
                ApplyDeptId = a.ApplyUserDeptId,
                ApprovalName = a.OperateUserName,
                WorkStep = a.WorkStep,
                Status = a.OperateStatus,
                Remark = a.OperateRemark,
                ApprovalTime = a.OperateTime,
                PreviousTime = a.PreviousTime,
                ApplyType = a.ApplicationType,
                VendorProfileType = a.VendorProfileType,
                VendorName = a.VendorName,
                VAType = a.VAType,
                PAReturnType = a.PAReturnME,
                PAReturnTypeText = a.PAReturnMainReason,
                PAReturnDetailType = a.PAReturnSubReason,
                IsSolved = a.IsSolved,
                Company = a.Company == "20" ? PPCompanyNames.TRADING : (a.Company == "91" ? PPCompanyNames.YYKJ : (a.Company == "79" ? PPCompanyNames.JX_Plant : (a.Company == "18" ? PPCompanyNames.JV : ""))),
                VendorStatus = a.VendorStatus,
                VendorCode = a.VendorCode,
            }).ToList();
            return new PagedResultDto<ApplicationApprovalRecordReportReponseDto>(count, result);
        }

        /// <summary>
        /// 申请审批记录报表 (弃用)
        /// </summary>
        /// <returns></returns>
        public async Task<PagedResultDto<ApplicationApprovalRecordReportReponseDto>> ApplicationApprovalRecordReportAsync_Back(ApplicationApprovalRecordReportRequestDto request, bool isPage = true)
        {
            var sqlRepository = LazyServiceProvider.LazyGetService<ISqlRepository>();
            var sql = @"select
	pur.[Id],
	 [ApplicationCode],
	 [ApplyTime],
	 [ApplyUserName],
	 [ApplyDeptName],
	 [ApplyDeptId],
	 [ApplyType],
	 [VendorProfileType],
	 [VendorName],
	 [VAType],
	 [PAReturnME],
	 [PAReturnType],
	 [PAReturnDetailType],
	 [VendorCode],
	 [Company],
	 [VendorStatus],
	 U.[Name] as [ApprovalName],
	 [ApprovalId],
	 [WorkStep],
	 [Status],
	 [Remark],
	 [ApprovalTime],
	 [PreviousTime] 
	 FROM (
	 	SELECT Approved.[Id],
		 Approved.[ApplicationCode],
		 Approved.[ApplyTime],
		 Approved.[ApplyUserName],
		 Approved.[ApplyDeptName],
		 Approved.[ApplyDeptId],
		 Approved.[ApplyType],
		 Approved.[VendorProfileType],
		 Approved.[VendorName],
		 Approved.[VAType],
		 Approved.[PAReturnME],
		 Approved.[PAReturnType],
		 Approved.[PAReturnDetailType],
		 Approved.[VendorCode],
		 Approved.[Company],
		 Approved.[VendorStatus],
		 workflow.ApprovalId AS [ApprovalId],
		 workflow.[WorkStep],
		 workflow.[Status],
		 workflow.[Remark],
		 workflow.[ApprovalTime],
		 workflow.[PreviousTime]
		FROM 
    (SELECT A.[Id],
        [ApplicationCode],
        ApplyTime,
        ApplyUserIdName AS ApplyUserName,
        ApplyUserDeptName AS ApplyDeptName,
		null AS ApplyDeptId,
        2 AS ApplyType,--采购申请
		NULL AS VendorProfileType,NULL AS VendorName,NULL AS VAType,NULL AS PAReturnME,NULL AS PAReturnType,NULL AS PAReturnDetailType
		,NULL AS VendorCode,NULL AS Company,NULL AS VendorStatus
    FROM [PurPRApplications] A WITH(NOLOCK) --PA
    WHERE A.IsDeleted = 0
    UNION ALL SELECT A.[Id],
        [ApplicationCode],
        ApplyTime,
        ApplyUserName AS ApplyUserName,
        ApplyUserBuToDeptName AS ApplyDeptName,
		null AS ApplyDeptId,
        8 AS ApplyType,--采购订单申请
		NULL AS VendorProfileType, NULL AS VendorName,NULL AS VAType,NULL AS PAReturnME,NULL AS PAReturnType, NULL AS PAReturnDetailType
		,NULL AS VendorCode,NULL AS Company,NULL AS VendorStatus
    FROM [PurPOApplications] A WITH(NOLOCK) --PO
    WHERE A.IsDeleted = 0
    UNION ALL SELECT A.[Id],
        [ApplicationCode],
        ApplyTime,
        ApplyUserName AS ApplyUserName,
        ApplyUserBuToDeptName AS ApplyDeptName,
		null AS ApplyDeptId,
        4 AS ApplyType,--收货申请
		NULL AS VendorProfileType,NULL AS VendorName,NULL AS VAType, NULL AS PAReturnME,NULL AS PAReturnType,NULL AS PAReturnDetailType
		,NULL AS VendorCode,NULL AS Company,NULL AS VendorStatus
    FROM [PurGRApplications] A WITH(NOLOCK) --GR
    WHERE A.IsDeleted = 0
    UNION ALL SELECT A.[Id],
        [ApplicationCode],
        ApplyTime,
        ApplyUserName AS ApplyUserName,
        ApplyUserBuName AS ApplyDeptName,
		null AS ApplyDeptId,
        16 AS ApplyType,NULL AS VendorProfileType,NULL AS VendorName,NULL AS VAType,NULL AS PAReturnME, NULL AS PAReturnType,NULL AS PAReturnDetailType
        ,NULL AS VendorCode,NULL AS Company,NULL AS VendorStatus
    FROM [PurBDApplications] A WITH(NOLOCK) --Bidding
    WHERE A.IsDeleted = 0
    UNION ALL SELECT A.[Id],
        [ApplicationCode],
        ApplyTime,
        [Name] AS ApplyUserName,
        null AS ApplyDeptName,
		CONVERT(uniqueidentifier,ApplyUserBu) AS ApplyDeptId,
        0 AS ApplyType,
        A.VendorType AS VendorProfileType,
        CASE
        WHEN A.VendorType <= 2 THEN
        P.SPName
        ELSE O.VendorName
        END AS VendorName,
        ApplicationType AS VAType, NULL AS PAReturnME, NULL AS PAReturnType, NULL AS PAReturnDetailType
        ,F.VendorCode,F.Company,F.FinancialVendorStatus as VendorStatus
    FROM [VendorApplications] A WITH(NOLOCK) --vendor
    LEFT JOIN [AbpUsers] U WITH(NOLOCK)
        ON A.ApplyUserId = U.Id
    LEFT JOIN [VendorApplicationOrgnizations] O WITH(NOLOCK)
        ON A.Id = O.ApplicationId
    LEFT JOIN [VendorApplicationPersonals] P WITH(NOLOCK)
        ON A.Id = P.ApplicationId
    LEFT JOIN [VendorApplicationFinancials] F WITH(NOLOCK)
    	ON A.Id = F.ApplicationId
    WHERE A.IsDeleted = 0
    UNION ALL SELECT A.[Id],
        [ApplicationCode],
        ApplyTime,
        ApplyUserName,
        [ApplyDeptName],
		null AS ApplyDeptId,
        CASE
        WHEN [ExemptType] = 1 THEN
        32
        ELSE 64
        END AS ApplyType,NULL AS VendorProfileType,NULL AS VendorName,NULL AS VAType,NULL AS PAReturnME,NULL AS PAReturnType,NULL AS PAReturnDetailType
        ,NULL AS VendorCode,NULL AS Company,NULL AS VendorStatus
    FROM [PurBWApplications] A WITH(NOLOCK)
    WHERE A.IsDeleted = 0 --bw
) AS Approved --PA
	left join     
	(SELECT [FormId],
        [WorkStep],
        [Status],
        [Remark],
        [ApprovalId],
        LAG(ApprovalTime)
        OVER (PARTITION BY [FormId]
    ORDER BY [ApprovalTime]) AS PreviousTime, [ApprovalTime]
    FROM [WorkflowTasks] WITH(NOLOCK)) as workflow on Approved.Id = workflow.FormId
    UNION ALL SELECT A.[Id],
        [ApplicationCode],
        ApplyTime,
        ApplyUserName,
        [ApplyUserBuToDeptName] AS ApplyDeptName,
		null AS ApplyDeptId,
        128 AS ApplyType,NULL AS VendorProfileType,NULL AS VendorName,NULL AS VAType,Pri.[Type] AS PAReturnME,prid.MainReason AS PAReturnType,prid.SubReason AS PAReturnDetailType,
        NULL AS VendorCode,NULL AS Company,NULL AS VendorStatus,
		workflow.ApprovalId AS [ApprovalId],
		workflow.[WorkStep],
        workflow.[Status],
        workflow.[Remark],
        workflow.[ApprovalTime],
        workflow.[PreviousTime]
    FROM [PurPAApplications] A WITH(NOLOCK)
 left join (SELECT
		[Id],
		[FormId],
        [WorkStep],
        [Status],
        [Remark],
        [ApprovalId],
        LAG(ApprovalTime)
        OVER (PARTITION BY [FormId]
    ORDER BY [ApprovalTime]) AS PreviousTime, [ApprovalTime]
    FROM [WorkflowTasks] WITH(NOLOCK)) as workflow on A.Id = workflow.FormId 
	left join [PurPAReturnReasonInfos] Pri WITH(NOLOCK) on pri.PurPAApplicationId = a.Id and pri.WorkFlowTaskId = workflow.Id
	left join [PurPAReturnReasonDetails] prid WITH(NOLOCK) on pri.Id = prid.ReturnReasonId
    WHERE A.IsDeleted = 0) as pur LEFT JOIN [AbpUsers] U ON pur.ApprovalId = U.Id";
            //获取pp数据
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var queryableUser = (await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync()).AsNoTracking();
            var department = (await dataverseService.GetOrganizations(stateCode: null)).ToDictionary(s => s.Id, s => s.DepartmentName);
            var query = await sqlRepository.SqlQueryRow<ApplicationApprovalRecordReportReponseDto>(sql);
            string operatorName = string.Empty, applicantName = string.Empty;

            if (request.OperatorId.HasValue)
            {
                operatorName = (await queryableUser.FirstOrDefaultAsync(f => f.Id == request.OperatorId))?.Name;
            }
            if (request.ApplicantId.HasValue)
            {
                applicantName = (await queryableUser.FirstOrDefaultAsync(f => f.Id == request.ApplicantId))?.Name;
            }
            var Count = await query.CountAsync();
            query = query.WhereIf(request.StartDate.HasValue, m => m.ApprovalTime >= request.StartDate)
            .WhereIf(request.EndDate.HasValue, m => m.ApprovalTime < request.EndDate.Value.AddDays(1))
            .WhereIf(request.ApplicationType.HasValue, m => m.ApplyType == request.ApplicationType)
            //.WhereIf(request.Status.HasValue, m => m.Status == request.Status)
            .WhereIf(!string.IsNullOrEmpty(operatorName), m => m.ApprovalName == operatorName)
            .WhereIf(!string.IsNullOrEmpty(applicantName), m => m.ApplyUserName == applicantName)
            .WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), m => m.ApplicationCode.Contains(request.ApplicationCode))
            .OrderByDescending(o => o.ApplyTime).ThenByDescending(t => t.ApprovalTime).PagingIf(request, isPage);
            var datas = await query.ToListAsync();
            foreach (var item in datas)
            {
                if (item.ApplyDeptId.HasValue) item.ApplyDeptName = department.GetOrDefault(item.ApplyDeptId.Value);
                if (!string.IsNullOrWhiteSpace(item.PAReturnME))
                {
                    var me = int.Parse(item.PAReturnME);
                    var returnType = (ReturnType)me;
                    var type = returnType.GetType().GetField(returnType.ToString())?.GetCustomAttribute<DescriptionAttribute>();
                    //item.PAReturnME = type?.Description;
                }
            }
            return new PagedResultDto<ApplicationApprovalRecordReportReponseDto>() { Items = datas, TotalCount = Count };
        }

        /// <summary>
        /// 刷WorkflowTasks 表FormName、ApplicationCode字段
        /// </summary>
        /// <returns></returns>
        public async Task FillWorkflowTasksFormNameAsync()
        {

            var sqlRepository = LazyServiceProvider.LazyGetService<ISqlRepository>();
            await sqlRepository.ExecuteSqlRawAsync(ApprovalRecordReportSQLConst.UpdateWorkFlowTaskFormNameSQL, 180);
        }

        /// <summary>
        /// 处理审批记录报表--每小时执行前一个小时的数据 
        /// </summary>
        /// <returns></returns>
        public async Task PushApprovalRecordReportAsync()
        {
            //每小时执行一次
            DateTime now = DateTime.Now;
            DateTime startTime = now.Date.AddHours(now.Hour - 2);//前一个小时的数据
            DateTime endTime = startTime.AddHours(1);

            //全量时 使用SQL脚本去跑 不在这里处理

            //以下是增量数据
            var sqlRepository = LazyServiceProvider.LazyGetService<ISqlRepository>();
            try
            {
                var parameters = new[] {
                    new SqlParameter("@creationTime_start", SqlDbType.DateTime) { Value = startTime },
                    new SqlParameter("@creationTime_end", SqlDbType.DateTime) { Value = endTime },
                };
                var vendorApprovalRecord = sqlRepository.ExecuteSqlRawTaskAsync(ApprovalRecordReportSQLConst.VendorApprovalRecordReportSQL, 600, parameters);
                var prApprovalRecord = sqlRepository.ExecuteSqlRawTaskAsync(ApprovalRecordReportSQLConst.PRApprovalRecordReportSQL, 600, parameters);
                var bwApprovalRecord = sqlRepository.ExecuteSqlRawTaskAsync(ApprovalRecordReportSQLConst.BWApprovalRecordReportSQL, 600, parameters);
                var poApprovalRecord = sqlRepository.ExecuteSqlRawTaskAsync(ApprovalRecordReportSQLConst.POApprovalRecordReportSQL, 600, parameters);
                var bdApprovalRecord = sqlRepository.ExecuteSqlRawTaskAsync(ApprovalRecordReportSQLConst.BDApprovalRecordReportSQL, 600, parameters);
                var grApprovalRecord = sqlRepository.ExecuteSqlRawTaskAsync(ApprovalRecordReportSQLConst.GRApprovalRecordReportSQL, 600, parameters);
                var paApprovalRecord = sqlRepository.ExecuteSqlRawTaskAsync(ApprovalRecordReportSQLConst.PAApprovalRecordReportSQL, 600, parameters);
                var saApprovalRecord = sqlRepository.ExecuteSqlRawTaskAsync(ApprovalRecordReportSQLConst.SAApprovalRecordReportSQL, 600, parameters);
                await Task.WhenAll(vendorApprovalRecord, prApprovalRecord, bwApprovalRecord, poApprovalRecord, bdApprovalRecord, grApprovalRecord, paApprovalRecord, saApprovalRecord);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"PushApprovalRecordReportAsync() Error Message:{ex.Message},ExecuteTime:{now.ToString("yyyy-MM-dd HH:mm:ss")},StartTime:{startTime.ToString("yyyy-MM-dd HH:mm:ss")},EndTime:{endTime.ToString("yyyy-MM-dd HH:mm:ss")}");
                return;
            }
            _logger.LogInformation($"PushApprovalRecordReportAsync() Success ExecuteTime:{now.ToString("yyyy-MM-dd HH:mm:ss")},StartTime:{startTime.ToString("yyyy-MM-dd HH:mm:ss")},EndTime:{endTime.ToString("yyyy-MM-dd HH:mm:ss")}");
        }
        #endregion

        #region 申请审批记录报表 OLD

        /// <summary>
        /// 处理审批记录报表
        /// </summary>
        /// <returns></returns>
        public async Task PushApprovalRecordReportAsync_old()
        {
            //取更新时间大于前一天 0点~24点
            var updateTime = DateTime.Now.Date.AddDays(-1);
            bool isIncrement = await LazyServiceProvider.LazyGetService<IApprovalRecordReportRepostory>().AnyAsync();//有数据则增量、无数据则全量

            var workflowTaskQuery = (await LazyServiceProvider.LazyGetService<IWorkflowTaskRepository>().GetQueryableAsync()).AsNoTracking();
            //全量时 数量时间短
            DateTime creationTime = DateTime.Parse("2024-01-01 00:00:00");
            var query = workflowTaskQuery.WhereIf(isIncrement == false, a => a.CreationTime > creationTime).WhereIf(isIncrement, a => a.CreationTime >= updateTime && a.CreationTime < updateTime.AddDays(1));
            var wfRepository = LazyServiceProvider.LazyGetService<IWorkflowTaskRepository>();
            var batchSize = 1000;
            var totalRecords = await query.CountAsync();

            var tasks = new List<Task>();
            using (var semaphoreSlim = new SemaphoreSlim(30))//可同时执行30个线程
            {
                for (int i = 0; i < totalRecords; i += batchSize)
                {
                    tasks.Add(ProcessBatchAsync_old(semaphoreSlim, isIncrement, creationTime, updateTime, i, batchSize));
                }
                await Task.WhenAll(tasks);
            }

        }

        /// <summary>
        /// 批量处理审批记录报表
        /// </summary>
        /// <param name="semaphore"></param>
        /// <param name="isIncrement"></param>
        /// <param name="creationTime"></param>
        /// <param name="updateTime"></param>
        /// <param name="skip"></param>
        /// <param name="take"></param>
        /// <returns></returns>
        private async Task ProcessBatchAsync_old(SemaphoreSlim semaphore, bool isIncrement, DateTime creationTime, DateTime updateTime, int skip, int take)
        {
            await semaphore.WaitAsync();
            try
            {
                using (var scope = _serviceProvider.CreateScope())
                {
                    var wftQuery = (await scope.ServiceProvider.GetRequiredService<IWorkflowTaskRepository>().GetQueryableAsync()).AsNoTracking();
                    var userQuery = (await scope.ServiceProvider.GetRequiredService<IRepository<IdentityUser>>().GetQueryableAsync()).AsNoTracking();

                    var batch = wftQuery
                        .WhereIf(isIncrement == false, a => a.CreationTime > creationTime)
                        .WhereIf(isIncrement, a => a.CreationTime >= updateTime && a.CreationTime < updateTime.AddDays(1))
                        .GroupJoin(userQuery, a => a.ApprovalId, b => b.Id, (a, b) => new { wf = a, us = b })
                        .SelectMany(a => a.us.DefaultIfEmpty(), (a, b) => new { a.wf, u = b })
                        .Skip(skip)
                        .Take(take)
                        .ToList();

                    var wfTasks = batch.Select(a => a.wf).ToList();
                    var users = batch.Where(a => a.u != null).Distinct().Select(a => a.u).ToList();

                    var vendorApprovalRecord = VendorApprovalRecordAsync_old(scope, wfTasks, users);
                    var prApprovalRecord = PRApprovalRecordAsync_old(scope, wfTasks, users);
                    var bwApprovalRecord = BWApprovalRecordAsync_old(scope, wfTasks, users);
                    var poApprovalRecord = POApprovalRecordAsync_old(scope, wfTasks, users);
                    var bdApprovalRecord = BDApprovalRecordAsync_old(scope, wfTasks, users);
                    var grApprovalRecord = GRApprovalRecordAsync_old(scope, wfTasks, users);
                    var paApprovalRecord = PAApprovalRecordAsync_old(scope, wfTasks, users);
                    var saApprovalRecord = SAApprovalRecordAsync_old(scope, wfTasks, users);
                    await Task.WhenAll(vendorApprovalRecord, prApprovalRecord, bwApprovalRecord, poApprovalRecord, bdApprovalRecord, grApprovalRecord, paApprovalRecord, saApprovalRecord);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"WorkflowTaskRecordRepot error:{ex.Message}");
            }
            finally
            {
                semaphore.Release();
            }
        }

        /// <summary>
        /// 供应商审批记录处理
        /// </summary>
        /// <param name="isIncrement">是否增量</param>
        /// <returns></returns>
        private async Task VendorApprovalRecordAsync_old(IServiceScope serviceScope, List<WorkflowTask> workflowTasks, List<IdentityUser> users)
        {
            try
            {
                var approvalRecordReportRepostory = serviceScope.ServiceProvider.GetRequiredService<IApprovalRecordReportRepostory>();
                ///VendorApplications
                var vendorQuery = (await serviceScope.ServiceProvider.GetRequiredService<IVendorApplicationRepository>().GetQueryableAsync()).AsNoTracking();
                //VendorApplicationOrgnizations
                var vendorApplicationOrgnizationQuery = (await serviceScope.ServiceProvider.GetRequiredService<IVendorApplicationOrganizationRepository>().GetQueryableAsync()).AsNoTracking();
                //VendorApplicationPersonals
                var vendorApplicationPersonalsQuery = (await serviceScope.ServiceProvider.GetRequiredService<IVendorApplicationPersonalRepository>().GetQueryableAsync()).AsNoTracking();
                //VendorApplicationFinancials 
                var vendorApplicationFinancialsQuery = (await serviceScope.ServiceProvider.GetRequiredService<IVendorApplicationFinancialRepository>().GetQueryableAsync()).AsNoTracking();

                var query = vendorQuery.Where(a => workflowTasks.Select(x => x.FormId).Distinct().Contains(a.Id))
                    .GroupJoin(vendorApplicationOrgnizationQuery, a => a.Id, b => b.ApplicationId, (a, b) => new { Va = a, Vos = b })
                    .SelectMany(a => a.Vos.DefaultIfEmpty(), (a, b) => new { a.Va, Vo = b })
                    .GroupJoin(vendorApplicationPersonalsQuery, a => a.Va.Id, b => b.ApplicationId, (a, b) => new { a.Va, a.Vo, Vps = b })
                    .SelectMany(a => a.Vps.DefaultIfEmpty(), (a, b) => new { a.Va, a.Vo, Vp = b })
                    .GroupJoin(vendorApplicationFinancialsQuery, a => a.Va.Id, b => b.ApplicationId, (a, b) => new { a.Va, a.Vo, a.Vp, Vfs = b })//可能对应多个财务信息
                    .SelectMany(a => a.Vfs.DefaultIfEmpty(), (a, b) => new { a.Va, a.Vo, a.Vp, Vf = b });

                var vendors = query.ToList();
                if (!vendors.Any())
                    return;
                var wfRepository = serviceScope.ServiceProvider.GetRequiredService<IWorkflowTaskRepository>();
                var wfQuery = (await wfRepository.GetQueryableAsync()).AsNoTracking();
                var guidGenerator = LazyServiceProvider.LazyGetService<IGuidGenerator>();

                var wfsQuery = wfQuery.Where(wt => vendors.Select(v => v.Va.Id).Contains(wt.FormId)).Select(wt => new
                {
                    wt.Id,
                    PreviousTime = wfQuery.Where(w => w.FormId == wt.FormId && w.ApprovalTime < wt.ApprovalTime).Max(w => (DateTime?)w.ApprovalTime)
                });

                var pr_wfs = wfsQuery.ToList();

                var insertApprovalRecordReports = new List<ApprovalRecordReport>();
                var thisWorkflowTasks = workflowTasks.Where(a => vendors.Select(x => x.Va.Id).Contains(a.FormId));
                foreach (var item in thisWorkflowTasks)
                {
                    var vds = vendors.Where(a => a.Va.Id == item.FormId).ToList();//多个财务信息Join出多条数据
                    foreach (var vendor in vds)
                    {
                        Guid? deptId = Guid.TryParse(vendor.Va.ApplyUserBu, out var parsedGuid) ? parsedGuid : null;
                        var user = users.FirstOrDefault(u => u.Id == item.ApprovalId);
                        try
                        {
                            var approvalRecord = new ApprovalRecordReport()
                            {
                                TransfereeId = vendor.Va.TransfereeId,
                                BuId = vendor.Va.ApplyBuId,
                                ApplyUserDeptId = deptId,
                                //SubBudgetId = vendor.,
                                ApplyUserId = vendor.Va.ApplyUserId,
                                ApplicationType = ApplicationDocumentType.Supplier,
                                ApplicationId = vendor.Va.Id,
                                ApplicationCode = vendor.Va.ApplicationCode,
                                ApplyTime = vendor.Va.ApplyTime,
                                ApplyUserName = vendor.Va.ApplyUserName,
                                ApplyUserDeptName = vendor.Va.ApplyUserBuName,
                                WorkStep = item.WorkStep,
                                OperateUserId = item.ApprovalId,
                                OperateUserName = user?.Name ?? "",
                                OperateTime = item.ApprovalTime,
                                OperateStatus = item.Status,
                                OperateRemark = item.Remark,
                                PreviousTime = pr_wfs.FirstOrDefault(a => a.Id == item.Id)?.PreviousTime,
                                VendorProfileType = vendor.Va.VendorType,
                                //VendorId = vendor.
                                VendorName = (int)vendor.Va.VendorType <= 2 ? vendor.Vp?.SPName : vendor.Vo?.VendorName,
                                VAType = vendor.Va.ApplicationType,
                                Company = vendor.Vf?.Company,
                                VendorCode = vendor.Vf?.VendorCode,
                                VendorStatus = vendor.Vf?.FinancialVendorStatus,
                            };
                            approvalRecord.SetId(guidGenerator.Create());
                            insertApprovalRecordReports.Add(approvalRecord);
                        }
                        catch (Exception e)
                        {
                            _logger.LogError(e, $"WorkflowTaskRecordRepot VendorApprovalRecordAsync {item.Id} {e.Message}");
                        }
                    }
                }
                var context = await approvalRecordReportRepostory.GetDbContextAsync();
                await context.BulkInsertAsync(insertApprovalRecordReports);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"WorkflowTaskRecordRepot VendorApprovalRecordAsync {ex.Message}");
            }
        }
        /// <summary>
        /// PR审批记录处理
        /// </summary>
        /// <param name="isIncrement">是否增量</param>
        /// <returns></returns>
        private async Task PRApprovalRecordAsync_old(IServiceScope serviceScope, List<WorkflowTask> workflowTasks, List<IdentityUser> users)
        {
            try
            {
                var approvalRecordReportRepostory = serviceScope.ServiceProvider.GetRequiredService<IApprovalRecordReportRepostory>();
                var prRepostory = serviceScope.ServiceProvider.GetRequiredService<IPurPRApplicationRepository>();
                var prQuery = (await prRepostory.GetQueryableAsync()).AsNoTracking();
                var wfQuery = (await serviceScope.ServiceProvider.GetRequiredService<IWorkflowTaskRepository>().GetQueryableAsync()).AsNoTracking();
                var guidGenerator = LazyServiceProvider.LazyGetService<IGuidGenerator>();
                var prs = prQuery.Where(a => workflowTasks.Select(x => x.FormId).Distinct().Contains(a.Id)).ToList();

                if (!prs.Any())
                    return;
                var wfsQuery = wfQuery.Where(a => prs.Select(x => x.Id).Contains(a.FormId)).Select(wt => new
                {
                    wt.Id,
                    PreviousTime = wfQuery.Where(w => w.FormId == wt.FormId && w.ApprovalTime < wt.ApprovalTime).Max(w => (DateTime?)w.ApprovalTime)
                });
                var pr_wfs = wfsQuery.ToList();

                var insertApprovalRecordReports = new List<ApprovalRecordReport>();
                var thisWorkflowTasks = workflowTasks.Where(a => prs.Select(x => x.Id).Contains(a.FormId));
                foreach (var item in thisWorkflowTasks)
                {
                    try
                    {
                        var pr = prs.FirstOrDefault(a => a.Id == item.FormId);
                        var approvalRecord = new ApprovalRecordReport()
                        {
                            TransfereeId = pr.TransfereeId,
                            AgentId = pr.AgentId,
                            BuId = pr.ApplyUserBu,
                            ApplyUserDeptId = pr.ApplyUserDept,
                            SubBudgetId = pr.SubBudgetId,
                            ApplyUserId = pr.ApplyUserId,
                            ApplicationType = ApplicationDocumentType.Purchase,
                            ApplicationId = pr.Id,
                            ApplicationCode = pr.ApplicationCode,
                            ApplyTime = pr.ApplyTime,
                            ApplyUserName = pr.ApplyUserIdName,
                            ApplyUserDeptName = pr.ApplyUserDeptName,
                            WorkStep = item.WorkStep,
                            OperateUserId = item.ApprovalId,
                            OperateUserName = users.FirstOrDefault(a => a.Id == item.ApprovalId)?.Name,
                            OperateTime = item.ApprovalTime,
                            OperateStatus = item.Status,
                            OperateRemark = item.Remark,
                            PreviousTime = pr_wfs.FirstOrDefault(a => a.Id == item.Id)?.PreviousTime,
                        };
                        approvalRecord.SetId(guidGenerator.Create());
                        insertApprovalRecordReports.Add(approvalRecord);
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(e, $"WorkflowTaskRecordRepot PRApprovalRecordAsync {item.Id} {e.Message}");
                    }
                }
                var context = await approvalRecordReportRepostory.GetDbContextAsync();
                await context.BulkInsertAsync(insertApprovalRecordReports);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"WorkflowTaskRecordRepot PRApprovalRecordAsync {ex.Message}");
            }
        }
        /// <summary>
        /// PO审批记录处理
        /// </summary>
        /// <param name="isIncrement">是否增量</param>
        /// <returns></returns>
        private async Task POApprovalRecordAsync_old(IServiceScope serviceScope, List<WorkflowTask> workflowTasks, List<IdentityUser> users)
        {
            try
            {
                var approvalRecordReportRepostory = serviceScope.ServiceProvider.GetRequiredService<IApprovalRecordReportRepostory>();
                var poQuery = (await serviceScope.ServiceProvider.GetRequiredService<IPurPOApplicationRepository>().GetQueryableAsync()).AsNoTracking();
                var prQuery = (await serviceScope.ServiceProvider.GetRequiredService<IPurPRApplicationRepository>().GetQueryableAsync()).AsNoTracking();
                var wfQuery = (await serviceScope.ServiceProvider.GetRequiredService<IWorkflowTaskRepository>().GetQueryableAsync()).AsNoTracking();
                var guidGenerator = LazyServiceProvider.LazyGetService<IGuidGenerator>();
                var pos = poQuery.Where(a => workflowTasks.Select(x => x.FormId).Distinct().Contains(a.Id))
                    .GroupJoin(prQuery.Select(a => new { a.Id, a.SubBudgetId }), a => a.PRId, b => b.Id, (a, b) => new { po = a, prs = b })
                    .SelectMany(a => a.prs.DefaultIfEmpty(), (a, b) => new { a.po, pr = b }).ToList();
                if (!pos.Any())
                    return;
                var wfsQuery = wfQuery.Where(a => pos.Select(x => x.po.Id).Contains(a.FormId)).Select(wt => new
                {
                    wt.Id,
                    PreviousTime = wfQuery.Where(w => w.FormId == wt.FormId && w.ApprovalTime < wt.ApprovalTime).Max(w => (DateTime?)w.ApprovalTime)
                });
                var pr_wfs = wfsQuery.ToList();

                var insertApprovalRecordReports = new List<ApprovalRecordReport>();
                var thisWorkflowTasks = workflowTasks.Where(a => pos.Select(x => x.po.Id).Contains(a.FormId));
                foreach (var item in thisWorkflowTasks)
                {
                    try
                    {
                        var po = pos.FirstOrDefault(a => a.po.Id == item.FormId);
                        var approvalRecord = new ApprovalRecordReport()
                        {
                            TransfereeId = po.po.TransfereeId,
                            BuId = po.po.ApplyUserBu,
                            ApplyUserDeptId = po.po.ApplyUserDept,
                            SubBudgetId = po.pr?.SubBudgetId,
                            ApplyUserId = po.po.ApplyUserId,
                            ApplicationType = ApplicationDocumentType.PurchaseOrder,
                            ApplicationId = po.po.Id,
                            ApplicationCode = po.po.ApplicationCode,
                            ApplyTime = po.po.ApplyTime,
                            ApplyUserName = po.po.ApplyUserName,
                            ApplyUserDeptName = po.po.ApplyUserBuToDeptName,
                            WorkStep = item.WorkStep,
                            OperateUserId = item.ApprovalId,
                            OperateUserName = users.FirstOrDefault(a => a.Id == item.ApprovalId)?.Name,
                            OperateTime = item.ApprovalTime,
                            OperateStatus = item.Status,
                            OperateRemark = item.Remark,
                            PreviousTime = pr_wfs.FirstOrDefault(a => a.Id == item.Id)?.PreviousTime,
                            //VendorId = po.po.VendorId,
                            //VendorName = po.po.VendorName,
                            //VendorCode = po.po.VendorCode,
                        };
                        approvalRecord.SetId(guidGenerator.Create());
                        insertApprovalRecordReports.Add(approvalRecord);
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(e, $"WorkflowTaskRecordRepot POApprovalRecordAsync {item.Id} {e.Message}");
                    }
                }
                var context = await approvalRecordReportRepostory.GetDbContextAsync();
                await context.BulkInsertAsync(insertApprovalRecordReports);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"WorkflowTaskRecordRepot POApprovalRecordAsync {ex.Message}");
            }
        }

        /// <summary>
        /// BD审批记录处理
        /// </summary>
        /// <param name="isIncrement">是否增量</param>
        /// <returns></returns>
        private async Task BDApprovalRecordAsync_old(IServiceScope serviceScope, List<WorkflowTask> workflowTasks, List<IdentityUser> users)
        {
            try
            {
                var approvalRecordReportRepostory = serviceScope.ServiceProvider.GetRequiredService<IApprovalRecordReportRepostory>();
                var bdRepostory = serviceScope.ServiceProvider.GetRequiredService<IPurBDApplicationRepository>();
                var bdQuery = (await bdRepostory.GetQueryableAsync()).AsNoTracking();
                var prQuery = (await serviceScope.ServiceProvider.GetRequiredService<IPurPRApplicationRepository>().GetQueryableAsync()).AsNoTracking();
                var wfQuery = (await serviceScope.ServiceProvider.GetRequiredService<IWorkflowTaskRepository>().GetQueryableAsync()).AsNoTracking();
                var guidGenerator = LazyServiceProvider.LazyGetService<IGuidGenerator>();
                var bds = bdQuery.Where(a => workflowTasks.Select(x => x.FormId).Distinct().Contains(a.Id))
                    .GroupJoin(prQuery, a => a.PRApplicationId, b => b.Id, (a, b) => new { bd = a, prs = b })
                    .SelectMany(a => a.prs.DefaultIfEmpty(), (a, b) => new { a.bd, pr = b })
                    .ToList();
                if (!bds.Any())
                    return;

                var wfsQuery = wfQuery.Where(a => bds.Select(x => x.bd.Id).Contains(a.FormId)).Select(wt => new
                {
                    wt.Id,
                    PreviousTime = wfQuery.Where(w => w.FormId == wt.FormId && w.ApprovalTime < wt.ApprovalTime).Max(w => (DateTime?)w.ApprovalTime)
                });
                var pr_wfs = wfsQuery.ToList();

                var insertApprovalRecordReports = new List<ApprovalRecordReport>();
                var thisWorkflowTasks = workflowTasks.Where(a => bds.Select(x => x.bd.Id).Contains(a.FormId));
                foreach (var item in thisWorkflowTasks)
                {
                    try
                    {
                        var bd = bds.FirstOrDefault(a => a.bd.Id == item.FormId);
                        var approvalRecord = new ApprovalRecordReport()
                        {
                            TransfereeId = bd.bd.TransfereeId,
                            BuId = bd.bd.ApplyUserBu,
                            ApplyUserDeptId = bd.pr?.ApplyUserDept,
                            SubBudgetId = bd.pr?.SubBudgetId,
                            ApplyUserId = bd.bd.ApplyUserId,
                            ApplicationType = ApplicationDocumentType.PriceComparison,
                            ApplicationId = bd.bd.Id,
                            ApplicationCode = bd.bd.ApplicationCode,
                            ApplyTime = bd.bd.ApplyTime,
                            ApplyUserName = bd.bd.ApplyUserName,
                            ApplyUserDeptName = bd.pr?.ApplyUserDeptName,
                            WorkStep = item.WorkStep,
                            OperateUserId = item.ApprovalId,
                            OperateUserName = users.FirstOrDefault(a => a.Id == item.ApprovalId)?.Name,
                            OperateTime = item.ApprovalTime,
                            OperateStatus = item.Status,
                            OperateRemark = item.Remark,
                            PreviousTime = pr_wfs.FirstOrDefault(a => a.Id == item.Id)?.PreviousTime,
                        };
                        approvalRecord.SetId(guidGenerator.Create());
                        insertApprovalRecordReports.Add(approvalRecord);
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(e, $"WorkflowTaskRecordRepot BDApprovalRecordAsync {item.Id} {e.Message}");
                    }
                }
                var context = await approvalRecordReportRepostory.GetDbContextAsync();
                await context.BulkInsertAsync(insertApprovalRecordReports);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"WorkflowTaskRecordRepot BDApprovalRecordAsync {ex.Message}");
            }
        }

        /// <summary>
        /// GR审批记录处理
        /// </summary>
        /// <param name="isIncrement">是否增量</param>
        /// <returns></returns>
        private async Task GRApprovalRecordAsync_old(IServiceScope serviceScope, List<WorkflowTask> workflowTasks, List<IdentityUser> users)
        {
            try
            {
                var approvalRecordReportRepostory = serviceScope.ServiceProvider.GetRequiredService<IApprovalRecordReportRepostory>();
                var grQuery = (await serviceScope.ServiceProvider.GetRequiredService<IPurGRApplicationRepository>().GetQueryableAsync()).AsNoTracking();
                var wfQuery = (await serviceScope.ServiceProvider.GetRequiredService<IWorkflowTaskRepository>().GetQueryableAsync()).AsNoTracking();
                var guidGenerator = LazyServiceProvider.LazyGetService<IGuidGenerator>();
                var grs = grQuery.Where(a => workflowTasks.Select(x => x.FormId).Distinct().Contains(a.Id))
                    .ToList();
                if (!grs.Any())
                    return;

                var wfsQuery = wfQuery.Where(a => grs.Select(x => x.Id).Contains(a.FormId)).Select(wt => new
                {
                    wt.Id,
                    PreviousTime = wfQuery.Where(w => w.FormId == wt.FormId && w.ApprovalTime < wt.ApprovalTime).Max(w => (DateTime?)w.ApprovalTime)
                });
                var pr_wfs = wfsQuery.ToList();

                var insertApprovalRecordReports = new List<ApprovalRecordReport>();

                var thisWorkflowTasks = workflowTasks.Where(a => grs.Select(x => x.Id).Contains(a.FormId));
                foreach (var item in thisWorkflowTasks)
                {
                    try
                    {
                        var gr = grs.FirstOrDefault(a => a.Id == item.FormId);
                        var approvalRecord = new ApprovalRecordReport()
                        {
                            TransfereeId = gr.TransfereeId,
                            BuId = gr.ApplyUserBu,
                            ApplyUserDeptId = gr.ApplyUserBuToDeptId,
                            SubBudgetId = gr.SubBudgetId,
                            ApplyUserId = gr.ApplyUserId,
                            ApplicationType = ApplicationDocumentType.Receipt,
                            ApplicationId = gr.Id,
                            ApplicationCode = gr.ApplicationCode,
                            ApplyTime = gr.ApplyTime,
                            ApplyUserName = gr.ApplyUserName,
                            ApplyUserDeptName = gr.ApplyUserBuToDeptName,
                            WorkStep = item.WorkStep,
                            OperateUserId = item.ApprovalId,
                            OperateUserName = users.FirstOrDefault(a => a.Id == item.ApprovalId)?.Name,
                            OperateTime = item.ApprovalTime,
                            OperateStatus = item.Status,
                            OperateRemark = item.Remark,
                            PreviousTime = pr_wfs.FirstOrDefault(a => a.Id == item.Id)?.PreviousTime,
                        };
                        approvalRecord.SetId(guidGenerator.Create());
                        insertApprovalRecordReports.Add(approvalRecord);
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(e, $"WorkflowTaskRecordRepot GRApprovalRecordAsync {item.Id} {e.Message}");
                    }
                }
                var context = await approvalRecordReportRepostory.GetDbContextAsync();
                await context.BulkInsertAsync(insertApprovalRecordReports);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"WorkflowTaskRecordRepot GRApprovalRecordAsync {ex.Message}");
            }
        }

        /// <summary>
        /// PA审批记录处理
        /// </summary>
        /// <param name="isIncrement">是否增量</param>
        /// <returns></returns>
        private async Task PAApprovalRecordAsync_old(IServiceScope serviceScope, List<WorkflowTask> workflowTasks, List<IdentityUser> users)
        {
            try
            {
                var approvalRecordReportRepostory = serviceScope.ServiceProvider.GetRequiredService<IApprovalRecordReportRepostory>();
                var paQuery = (await serviceScope.ServiceProvider.GetRequiredService<IPurPAApplicationRepository>().GetQueryableAsync()).AsNoTracking();
                //PurPAReturnReasonInfos
                var paReturnReasonInfoQuery = (await serviceScope.ServiceProvider.GetRequiredService<IPurPAReturnReasonInfoRepository>().GetQueryableAsync()).AsNoTracking();
                //PurPAReturnReasonDetails
                var paReturnReasonDetailsQuery = (await serviceScope.ServiceProvider.GetRequiredService<IPurPAReturnReasonDetailRepository>().GetQueryableAsync()).AsNoTracking();
                var prQuery = (await serviceScope.ServiceProvider.GetRequiredService<IPurPRApplicationRepository>().GetQueryableAsync()).AsNoTracking();
                var wfQuery = (await serviceScope.ServiceProvider.GetRequiredService<IWorkflowTaskRepository>().GetQueryableAsync()).AsNoTracking();
                var guidGenerator = LazyServiceProvider.LazyGetService<IGuidGenerator>();

                var query = paQuery.Where(a => workflowTasks.Select(x => x.FormId).Distinct().Contains(a.Id))
                    .GroupJoin(paReturnReasonInfoQuery, a => a.Id, b => b.PurPAApplicationId, (a, b) => new { pa = a, ris = b })
                    .SelectMany(a => a.ris.DefaultIfEmpty(), (a, b) => new { a.pa, ri = b })
                    .GroupJoin(paReturnReasonDetailsQuery, a => a.ri.Id, b => b.ReturnReasonId, (a, b) => new { a.pa, a.ri, rids = b })
                    .SelectMany(a => a.rids.DefaultIfEmpty(), (a, b) => new { a.pa, a.ri, rid = b })
                    .GroupJoin(prQuery, a => a.pa.PRId, b => b.Id, (a, b) => new { a.pa, a.ri, a.rid, prs = b })
                    .SelectMany(a => a.prs.DefaultIfEmpty(), (a, b) => new
                    {
                        a.pa,
                        ReturnId = a.ri == null ? new Guid?() : a.ri.Id,
                        PAReturnME = a.ri == null ? (ReturnType?)null : (ReturnType?)a.ri.Type,
                        WorkFlowTaskId = a.ri == null ? new Guid?() : a.ri.WorkFlowTaskId,
                        ReturnDetailId = a.rid == null ? new Guid?() : a.rid.Id,
                        MainReason = a.rid == null ? "" : a.rid.MainReason,
                        SubReason = a.rid == null ? "" : a.rid.SubReason,
                        IsSolved = a.rid == null ? (bool?)null : a.rid.IsSolved,
                        SubBudgetId = b == null ? new Guid?() : b.SubBudgetId,
                    });
                var pas = query.ToList();
                if (!pas.Any())
                    return;
                var paIds = pas.Select(x => x.pa.Id).Distinct().ToList();
                var wfsQuery = wfQuery.Where(a => paIds.Contains(a.FormId)).Select(wt => new
                {
                    wt.Id,
                    PreviousTime = wfQuery.Where(w => w.FormId == wt.FormId && w.ApprovalTime < wt.ApprovalTime).Max(w => (DateTime?)w.ApprovalTime)
                });
                var pr_wfs = wfsQuery.ToList();

                var insertApprovalRecordReports = new List<ApprovalRecordReport>();

                var paWorkflowTasks = pas.Where(a => a.WorkFlowTaskId == null).ToList();//无特殊退回
                var pa_rWorkflowTasks = pas.Where(a => a.WorkFlowTaskId != null).ToList();//有特殊退回

                var thisWorkflowTasks = workflowTasks.Where(a => paWorkflowTasks.Select(x => x.pa.Id).Contains(a.FormId));
                foreach (var item in thisWorkflowTasks)
                {
                    try
                    {
                        var pa = paWorkflowTasks.FirstOrDefault(a => a.pa.Id == item.FormId);
                        var approvalRecord = new ApprovalRecordReport()
                        {
                            TransfereeId = pa.pa.TransfereeId,
                            BuId = pa.pa.ApplyUserBu,
                            ApplyUserDeptId = pa.pa.ApplyUserBuToDept,
                            SubBudgetId = pa.SubBudgetId,
                            ApplyUserId = pa.pa.ApplyUserId,
                            ApplicationType = ApplicationDocumentType.Payment,
                            ApplicationId = pa.pa.Id,
                            ApplicationCode = pa.pa.ApplicationCode,
                            ApplyTime = pa.pa.ApplyTime,
                            ApplyUserName = pa.pa.ApplyUserName,
                            ApplyUserDeptName = pa.pa.ApplyUserBuToDeptName,
                            WorkStep = item.WorkStep,
                            OperateUserId = item.ApprovalId,
                            OperateUserName = users.FirstOrDefault(a => a.Id == item.ApprovalId)?.Name,
                            OperateTime = item.ApprovalTime,
                            OperateStatus = item.Status,
                            OperateRemark = item.Remark,
                            PreviousTime = pr_wfs.FirstOrDefault(a => a.Id == item.Id)?.PreviousTime,
                        };
                        approvalRecord.SetId(guidGenerator.Create());
                        insertApprovalRecordReports.Add(approvalRecord);
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(e, $"WorkflowTaskRecordRepot PAApprovalRecordAsync {item.Id} {e.Message}");
                    }
                }
                var thisRWorkflowTasks = workflowTasks.Where(a => pa_rWorkflowTasks.Select(x => x.pa.Id).Contains(a.FormId));
                foreach (var item in thisRWorkflowTasks)
                {
                    var rpas = pa_rWorkflowTasks.Where(a => a.pa.Id == item.FormId && a.WorkFlowTaskId == item.Id).ToList();
                    try
                    {
                        foreach (var pa in rpas)
                        {
                            var approvalRecord = new ApprovalRecordReport()
                            {
                                TransfereeId = pa.pa.TransfereeId,
                                BuId = pa.pa.ApplyUserBu,
                                ApplyUserDeptId = pa.pa.ApplyUserBuToDept,
                                SubBudgetId = pa.SubBudgetId,
                                ApplyUserId = pa.pa.ApplyUserId,
                                ApplicationType = ApplicationDocumentType.Payment,
                                ApplicationId = pa.pa.Id,
                                ApplicationCode = pa.pa.ApplicationCode,
                                ApplyTime = pa.pa.ApplyTime,
                                ApplyUserName = pa.pa.ApplyUserName,
                                ApplyUserDeptName = pa.pa.ApplyUserBuToDeptName,
                                WorkStep = item.WorkStep,
                                OperateUserId = item.ApprovalId,
                                OperateUserName = users.FirstOrDefault(a => a.Id == item.ApprovalId)?.Name,
                                OperateTime = item.ApprovalTime,
                                OperateStatus = item.Status,
                                OperateRemark = item.Remark,
                                PreviousTime = pr_wfs.FirstOrDefault(a => a.Id == item.Id)?.PreviousTime,
                                ReturnId = pa.ReturnId,
                                ReturnDetailId = pa.ReturnDetailId,
                                PAReturnME = pa.PAReturnME,
                                PAReturnMainReason = pa.MainReason,
                                PAReturnSubReason = pa.SubReason,
                                IsSolved = pa.IsSolved,
                            };
                            approvalRecord.SetId(guidGenerator.Create());
                            insertApprovalRecordReports.Add(approvalRecord);
                        }
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(e, $"WorkflowTaskRecordRepot PAApprovalRecordAsync {item.Id} {e.Message}");
                    }
                }
                var context = await approvalRecordReportRepostory.GetDbContextAsync();
                await context.BulkInsertAsync(insertApprovalRecordReports);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"WorkflowTaskRecordRepot PAApprovalRecordAsync {ex.Message}");
            }
        }

        /// <summary>
        /// BW审批记录处理
        /// </summary>
        /// <param name="isIncrement">是否增量</param>
        /// <returns></returns>
        private async Task BWApprovalRecordAsync_old(IServiceScope serviceScope, List<WorkflowTask> workflowTasks, List<IdentityUser> users)
        {
            try
            {
                var approvalRecordReportRepostory = serviceScope.ServiceProvider.GetRequiredService<IApprovalRecordReportRepostory>();
                var bwQuery = (await serviceScope.ServiceProvider.GetRequiredService<IPurBWApplicationRepository>().GetQueryableAsync()).AsNoTracking();
                var prQuery = (await serviceScope.ServiceProvider.GetRequiredService<IPurPRApplicationRepository>().GetQueryableAsync()).AsNoTracking();
                var wfQuery = (await serviceScope.ServiceProvider.GetRequiredService<IWorkflowTaskRepository>().GetQueryableAsync()).AsNoTracking();
                var guidGenerator = LazyServiceProvider.LazyGetService<IGuidGenerator>();
                var bws = bwQuery.Where(a => workflowTasks.Select(x => x.FormId).Distinct().Contains(a.Id))
                    .GroupJoin(prQuery.Select(a => new { a.Id, a.SubBudgetId }), a => a.PRId, b => b.Id, (a, b) => new { bw = a, prs = b })
                    .SelectMany(a => a.prs.DefaultIfEmpty(), (a, b) => new { a.bw, pr = b })
                    .ToList();

                if (!bws.Any())
                    return;
                var wfsQuery = wfQuery.Where(a => bws.Select(x => x.bw.Id).Contains(a.FormId)).Select(wt => new
                {
                    wt.Id,
                    PreviousTime = wfQuery.Where(w => w.FormId == wt.FormId && w.ApprovalTime < wt.ApprovalTime).Max(w => (DateTime?)w.ApprovalTime)
                });
                var pr_wfs = wfsQuery.ToList();

                var insertApprovalRecordReports = new List<ApprovalRecordReport>();
                var thisWorkflowTasks = workflowTasks.Where(a => bws.Select(x => x.bw.Id).Contains(a.FormId));
                foreach (var item in thisWorkflowTasks)
                {
                    try
                    {
                        var bw = bws.FirstOrDefault(a => a.bw.Id == item.FormId);
                        var approvalRecord = new ApprovalRecordReport()
                        {
                            TransfereeId = bw.bw.TransfereeId,
                            BuId = bw.bw.DivisionId,
                            ApplyUserDeptId = bw.bw.ApplyDeptId,
                            SubBudgetId = bw.pr?.SubBudgetId,
                            ApplyUserId = bw.bw.ApplyUserId,
                            ApplicationType = bw.bw.ExemptType == ExemptType.Justification ? ApplicationDocumentType.Justification : ApplicationDocumentType.BiddingExemption,
                            ApplicationId = bw.bw.Id,
                            ApplicationCode = bw.bw.ApplicationCode,
                            ApplyTime = bw.bw.ApplyTime,
                            ApplyUserName = bw.bw.ApplyUserName,
                            ApplyUserDeptName = bw.bw.ApplyDeptName,
                            WorkStep = item.WorkStep,
                            OperateUserId = item.ApprovalId,
                            OperateUserName = users.FirstOrDefault(a => a.Id == item.ApprovalId)?.Name,
                            OperateTime = item.ApprovalTime,
                            OperateStatus = item.Status,
                            OperateRemark = item.Remark,
                            PreviousTime = pr_wfs.FirstOrDefault(a => a.Id == item.Id)?.PreviousTime,
                        };
                        approvalRecord.SetId(guidGenerator.Create());
                        insertApprovalRecordReports.Add(approvalRecord);
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(e, $"BWApprovalRecordAsync {item.Id} {e.Message}");
                    }
                }
                var context = await approvalRecordReportRepostory.GetDbContextAsync();
                await context.BulkInsertAsync(insertApprovalRecordReports);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"WorkflowTaskRecordRepot BWApprovalRecordAsync {ex.Message}");
            }
        }

        /// <summary>
        /// 讲者授权申请 审批记录处理
        /// </summary>
        /// <param name="isIncrement">是否增量</param>
        /// <returns></returns>
        private async Task SAApprovalRecordAsync_old(IServiceScope serviceScope, List<WorkflowTask> workflowTasks, List<IdentityUser> users)
        {
            try
            {
                var approvalRecordReportRepostory = serviceScope.ServiceProvider.GetRequiredService<IApprovalRecordReportRepostory>();
                var saQuery = (await serviceScope.ServiceProvider.GetRequiredService<IOECSpeakerAuthApplyRepository>().GetQueryableAsync()).AsNoTracking();
                var wfQuery = (await serviceScope.ServiceProvider.GetRequiredService<IWorkflowTaskRepository>().GetQueryableAsync()).AsNoTracking();
                var guidGenerator = LazyServiceProvider.LazyGetService<IGuidGenerator>();
                var sas = saQuery.Where(a => workflowTasks.Select(x => x.FormId).Distinct().Contains(a.Id))
                    .ToList();

                if (!sas.Any())
                    return;

                var wfsQuery = wfQuery.Where(a => sas.Select(x => x.Id).Contains(a.FormId)).Select(wt => new
                {
                    wt.Id,
                    PreviousTime = wfQuery.Where(w => w.FormId == wt.FormId && w.ApprovalTime < wt.ApprovalTime).Max(w => (DateTime?)w.ApprovalTime)
                });
                var pr_wfs = wfsQuery.ToList();

                var insertApprovalRecordReports = new List<ApprovalRecordReport>();
                var thisWorkflowTasks = workflowTasks.Where(a => sas.Select(x => x.Id).Contains(a.FormId));
                foreach (var item in thisWorkflowTasks)
                {
                    try
                    {
                        var sa = sas.FirstOrDefault(a => a.Id == item.FormId);
                        var approvalRecord = new ApprovalRecordReport()
                        {
                            TransfereeId = sa.TransfereeId,
                            BuId = sa.ApplyUserBu,
                            ApplyUserDeptId = sa.ApplyUserDept,
                            //SubBudgetId = sa.SubBudgetId,//预算无关
                            ApplyUserId = sa.ApplyUserId,
                            ApplicationType = ApplicationDocumentType.SpeakerAuthorization,
                            ApplicationId = sa.Id,
                            ApplicationCode = sa.ApplicationCode,
                            ApplyTime = sa.SubmitTime,
                            ApplyUserName = sa.ApplyUserName,
                            ApplyUserDeptName = sa.ApplyUserDeptName,
                            WorkStep = item.WorkStep,
                            OperateUserId = item.ApprovalId,
                            OperateUserName = users.FirstOrDefault(a => a.Id == item.ApprovalId)?.Name,
                            OperateTime = item.ApprovalTime,
                            OperateStatus = item.Status,
                            OperateRemark = item.Remark,
                            PreviousTime = pr_wfs.FirstOrDefault(a => a.Id == item.Id)?.PreviousTime,
                        };
                        approvalRecord.SetId(guidGenerator.Create());
                        insertApprovalRecordReports.Add(approvalRecord);
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(e, $"WorkflowTaskRecordRepot SAApprovalRecordAsync {item.Id} {e.Message}");
                    }
                }
                var context = await approvalRecordReportRepostory.GetDbContextAsync();
                await context.BulkInsertAsync(insertApprovalRecordReports);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"WorkflowTaskRecordRepot SAApprovalRecordAsync {ex.Message}");
            }
        }

        #endregion
        /// <summary>
        /// 用户报表
        /// </summary>
        /// <returns></returns>
        public async Task<PagedResultDto<UserReportDot>> UserReportAsync(UserReportRequestDto request, bool isPage = true)
        {
            var personCenterService = LazyServiceProvider.LazyGetService<IPersonCenterService>();
            var roleLevel = personCenterService.GetMyRoleLevel();
            RoleLevel[] allowLevel = [RoleLevel.Admin, RoleLevel.Manager];
            if (!allowLevel.Contains(roleLevel))// 没有数据权限
                return new PagedResultDto<UserReportDot>();
            var ids = await personCenterService.GetIdsByRoles(roleLevel);

            var queryableUser = await LazyServiceProvider.LazyGetService<IIdentityUserReadonlyRepository>().GetQueryableAsync();
            var queryableRole = await LazyServiceProvider.LazyGetService<IIdentityRoleReadonlyRepository>().GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var staffs = await dataverseService.GetStaffs(stateCode: null);
            var staffDict = staffs.ToDictionary(a => a.Id);
            var department = (await dataverseService.GetOrganizations()).AsEnumerable();
            var departmentDict = department.ToDictionary(s => s.Id);
            var costcenters = await dataverseService.GetCostcentersAsync(stateCode: null);
            var StaffDepartment = await dataverseService.GetStaffDepartmentRelations();
            //var district = await dataverseService.GetDistrict();
            var DepartmentCost = department.GroupJoin(costcenters, a => a.CostcenterId, b => b.Id, (org, b) => new { org, cost = b.FirstOrDefault() }).Select(s => new
            {
                Id = s.org.Id,
                s.org.DepartmentName,
                s.org.ParentDepartment,
                s.org.OrgCode,
                s.org.OrgParentCode,
                Name = s.cost?.Name ?? "",
                Code = s.cost?.Code ?? "",
                s.org.FocCostcenterId,
            }).GroupJoin(costcenters, a => a.FocCostcenterId, b => b.Id, (org, b) => new { org, cost = b.FirstOrDefault() }).Select(s => new
            {
                Id = s.org.Id,
                s.org.DepartmentName,
                s.org.ParentDepartment,
                s.org.OrgCode,
                s.org.OrgParentCode,
                Name = s.org.Name,
                Code = s.org.Code,
                FOCCostcenterName = s.cost?.Name ?? "",
                FOCCostcenterCode = s.cost?.Code ?? "",
            });
            var deptCost = StaffDepartment.Join(DepartmentCost, a => a.DepartmentId, b => b.Id, (a, b) => new { a.UserId, b }).GroupBy(s => new { s.UserId, s.b.Id }, (key, val) => new DeptCostCenter
            {
                Id = key.UserId,
                DepartmentId = key.Id,
                DepartmentNames = val.Select(s => s.b.DepartmentName).Where(m => !m.IsNullOrWhiteSpace()).JoinAsString("/"),
                OrgCodes = val.Select(s => s.b.OrgCode).Where(m => !m.IsNullOrWhiteSpace()).JoinAsString("/"),
                OrgParentCodes = val.Select(s => s.b.OrgParentCode).Where(m => !m.IsNullOrWhiteSpace()).JoinAsString("/"),
                CostcenterNames = val.Select(s => s.b.Name).Where(m => !m.IsNullOrWhiteSpace()).JoinAsString("/"),
                CostcenterCodes = val.Select(s => s.b.Code).Where(m => !m.IsNullOrWhiteSpace()).JoinAsString("/"),
                FOCCostcenterName = val.Select(s => s.b.FOCCostcenterName).Where(m => !m.IsNullOrWhiteSpace()).JoinAsString("/"),
                FOCCostcenterCode = val.Select(s => s.b.FOCCostcenterCode).Where(m => !m.IsNullOrWhiteSpace()).JoinAsString("/"),
            });
            var userDatas = await queryableUser.Where(a => a.IsActive).Include(m => m.Roles)
                      .SelectMany(a => a.Roles.DefaultIfEmpty(), (a, b) => new { user = a, RoleId = b.RoleId })
                      .GroupJoin(queryableRole, a => a.RoleId, b => b.Id, (a, b) => new { user = a.user, roles = string.Join(",", b.Select(s => EF.Property<string>(s, "DisplayName"))) })
                      .GroupBy(a => a.user.Id, (key, val) => new
                      {
                          Id = key,
                          StaffCode = EF.Property<string>(val.First().user, "StaffCode"),
                          UserName = val.First().user.UserName,
                          Name = val.First().user.Name,
                          Email = val.First().user.Email,
                          JobStatus = EF.Property<int?>(val.First().user, "JobStatus"),
                          Roles = val.Select(s => s.roles).JoinAsString(","),
                          MainDeptId = EF.Property<Guid?>(val.First().user, "MainDepartmentId"),
                      }).ToListAsync();
            var emunmerable = userDatas.GroupJoin(deptCost, a => a.Id, b => b.Id, (a, b) => new { userinfo = a, b }).SelectMany(s => s.b.DefaultIfEmpty(), (a, b) =>
            {
                var dis = staffDict.GetOrDefault(a.userinfo.Id);

                var (buid, buName) = GetUserBu(departmentDict, b?.DepartmentId);
                return new UserReportDot
                {
                    Id = a.userinfo.Id,
                    StaffCode = a.userinfo.StaffCode,
                    BuId = buid,
                    BuName = buName,
                    Name = a.userinfo?.Name ?? "",
                    Account = a.userinfo.UserName,
                    Email = a.userinfo.Email,
                    Role = a.userinfo.Roles,
                    JobStatus = a.userinfo.JobStatus,
                    DepartmentNames = b?.DepartmentNames ?? "",
                    OrgCodes = b?.OrgCodes,
                    OrgParentCodes = b?.OrgParentCodes ?? "",
                    CostcenterNames = b?.CostcenterNames ?? "",
                    CostCenterCodes = b?.CostcenterCodes ?? "",
                    FOCCostcenterNames = b?.FOCCostcenterName ?? "",
                    FOCCostCenterCodes = b?.FOCCostcenterCode ?? "",
                    RegionId = dis?.DistrictId,
                    RegionName = dis?.DistrictName,
                };
            }).WhereIf(request.BuId.HasValue, m => m.BuId == request.BuId)
                .WhereIf(!request.DepartmentName.IsNullOrWhiteSpace(), m => m.DepartmentNames.Contains(request.DepartmentName))
                .WhereIf(!request.CostCenterCode.IsNullOrWhiteSpace(), m => m.CostCenterCodes.Contains(request.CostCenterCode))
                .WhereIf(!request.Name.IsNullOrWhiteSpace(), m => m.Name.Contains(request.Name))
                .WhereIf(!request.Account.IsNullOrWhiteSpace(), m => m.Account.Contains(request.Account))
                .WhereIf(!request.Email.IsNullOrWhiteSpace(), m => m.Email.Contains(request.Email))
                .WhereIf(request.RegionId.HasValue, m => m.RegionId == request.RegionId)
                .WhereIf(request.JobStatus.HasValue, m => m.JobStatus == request.JobStatus);

            switch (roleLevel)
            {
                case RoleLevel.Admin:
                    break;
                case RoleLevel.Manager:
                    emunmerable = emunmerable.Where(a => ids.Contains(a.BuId ?? Guid.Empty));
                    break;
                case RoleLevel.Leader:
                case RoleLevel.Owner:
                case RoleLevel.Applicant:
                case RoleLevel.Unkonw:
                default:
                    break;
            }

            //var emunmerable = userDatas.GroupJoin(deptCost, a => a.Id, b => b.Id, (a, b) => new { userinfo = a, b }).SelectMany(s => s.b.DefaultIfEmpty(), (a, b) => new UserReportDot
            //{
            //    Id = a.userinfo.Id,
            //    StaffCode = a.userinfo.StaffCode,
            //    Bu = GetUserBu(departmentDict, a.userinfo.MainDeptId),
            //    Name = a.userinfo.Name,
            //    UserName = a.userinfo.UserName,
            //    Email = a.userinfo.Email,
            //    Role = a.userinfo.Roles,
            //    JobStatus = a.userinfo.JobStatus,
            //    DepartmentNames = b?.DepartmentNames,
            //    OrgCodes = b?.OrgCodes,
            //    OrgParentCodes = b?.OrgParentCodes,
            //    CostcenterNames = b?.CostcenterNames,
            //    CostcenterCodes = b?.CostcenterCodes,
            //}).Join(staffs, a => a.Id, b => b.Id, (a, b) => new UserReportDot {
            //    Id = a.Id,
            //    StaffCode = a.StaffCode,
            //    Bu =a.Bu,
            //    Name = a.Name,
            //    UserName = a.UserName,
            //    Email = a.Email,
            //    Role = a.Role,
            //    JobStatus = a.JobStatus,
            //    DepartmentNames = a.DepartmentNames,
            //    OrgCodes = a.OrgCodes,
            //    OrgParentCodes = a.OrgParentCodes,
            //    CostcenterNames = a.CostcenterNames,
            //    CostcenterCodes = a.CostcenterCodes,
            //    RegionId = b.DistrictId,
            //    RegionName = a.RegionName,
            //});join有点慢
            var Count = emunmerable.Count();
            var datas = emunmerable.PagingIf(request, isPage).ToList();

            return new PagedResultDto<UserReportDot>() { Items = datas, TotalCount = Count };
        }
        /// <summary>
        /// 查询用户主部门BU
        /// </summary>
        /// <param name="keyValues"></param>
        /// <param name="Id"></param>
        /// <returns></returns>
        private static (Guid, string) GetUserBu(Dictionary<Guid, DepartmentDto> keyValues, Guid? Id)
        {
            if (!Id.HasValue) return (Guid.Empty, "");
            DepartmentDto dept = keyValues.GetValueOrDefault(Id.Value);
            while (dept != null && dept.OrganizationType != Enums.DataverseEnums.Organization.OrganizationType.Bu)
            {
                if (dept.ParentDepartment.HasValue)
                    dept = keyValues.GetValueOrDefault(dept.ParentDepartment.Value);
                else dept = null;
            }
            if (dept != null) return (dept.Id, dept.DepartmentName);
            return (Guid.Empty, "");
        }
        /// <summary>
        /// 组织报表
        /// </summary>
        /// <returns></returns>
        public async Task<PagedResultDto<OrganizationReportResponseDto>> OrganizationReportAsync(OrganizationReportRequestDto request, bool isPage = true)
        {
            var personCenterService = LazyServiceProvider.LazyGetService<IPersonCenterService>();
            var roleLevel = personCenterService.GetMyRoleLevel();
            RoleLevel[] allowLevel = [RoleLevel.Admin, RoleLevel.Manager];
            if (!allowLevel.Contains(roleLevel))// 没有数据权限
                return new PagedResultDto<OrganizationReportResponseDto>();
            var ids = await personCenterService.GetIdsByRoles(roleLevel);

            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var department = (await dataverseService.GetOrganizations(stateCode: null));
            var departmentDict = department.ToDictionary(s => s.Id);
            var query = new QueryExpression("spk_organizationalmasterdata");
            query.ColumnSet.AddColumns("spk_name", "spk_epolevel", "spk_epoleader", "spk_parentorganization", "spk_costcenter", "spk_orgparentcode", "spk_orgcode", "spk_bpmcode");
            query.Criteria.AddCondition(new ConditionExpression("statecode", ConditionOperator.Equal, 0));
            var cost = query.AddLink("spk_costcentermasterdata", "spk_costcenter", "spk_costcentermasterdataid", JoinOperator.LeftOuter);
            cost.Columns.AddColumns("spk_name", "spk_costcentercode");
            cost.EntityAlias = "cost";
            var foccost = query.AddLink("spk_costcentermasterdata", "spk_foccostcenter", "spk_costcentermasterdataid", JoinOperator.LeftOuter);
            foccost.Columns.AddColumns("spk_name", "spk_costcentercode");
            foccost.EntityAlias = "foccost";
            var user = query.AddLink("spk_staffmasterdata", "spk_epoleader", "spk_staffmasterdataid", JoinOperator.LeftOuter);
            user.Columns.AddColumns("spk_name", "spk_staffaccount", "spk_staffemail");
            user.EntityAlias = "user";
            var entities = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);
            var queryEntities = entities.Select(s =>
            {
                var (buId, buName) = GetUserBu(departmentDict, s.Id);
                return new OrganizationReportResponseDto
                {
                    Name = s.GetAttributeValue<string>("spk_name"),
                    BuName = buName,
                    BuId = buId,
                    OrgParentCode = s.GetAttributeValue<string>("spk_orgparentcode"),
                    OrgCode = s.GetAttributeValue<string>("spk_orgcode"),
                    EpoLevel = s.Contains("spk_epolevel") ? s.GetAttributeValue<int>("spk_epolevel") : null,
                    CostCenterName = s.GetAttributeValue<AliasedValue>("cost.spk_name")?.Value.ToString() ?? "",
                    CostCenterCode = s.GetAttributeValue<AliasedValue>("cost.spk_costcentercode")?.Value.ToString() ?? "",
                    FOCCostCenterName = s.GetAttributeValue<AliasedValue>("foccost.spk_name")?.Value.ToString() ?? "",
                    FOCCostCenterCode = s.GetAttributeValue<AliasedValue>("foccost.spk_costcentercode")?.Value.ToString() ?? "",
                    UserName = s.GetAttributeValue<AliasedValue>("user.spk_name")?.Value.ToString() ?? "",
                    StaffAccount = s.GetAttributeValue<AliasedValue>("user.spk_staffaccount")?.Value.ToString() ?? "",
                    Email = s.GetAttributeValue<AliasedValue>("user.spk_staffemail")?.Value.ToString() ?? "",
                };
            }).WhereIf(request.BuId.HasValue, m => m.BuId == request.BuId)
                .WhereIf(!request.DepartmentName.IsNullOrWhiteSpace(), m => m.Name.Contains(request.DepartmentName))
                .WhereIf(!request.CostCenterCode.IsNullOrWhiteSpace(), m => m.CostCenterCode.Contains(request.CostCenterCode))
                .WhereIf(!request.UserName.IsNullOrWhiteSpace(), m => m.UserName.Contains(request.UserName))
                .WhereIf(!request.Account.IsNullOrWhiteSpace(), m => m.StaffAccount.Contains(request.Account))
                .WhereIf(!request.Email.IsNullOrWhiteSpace(), m => m.Email.Contains(request.Email));

            switch (roleLevel)
            {
                case RoleLevel.Admin:
                    break;
                case RoleLevel.Manager:
                    queryEntities = queryEntities.Where(a => ids.Contains(a.BuId ?? Guid.Empty));
                    break;
                case RoleLevel.Leader:
                case RoleLevel.Owner:
                case RoleLevel.Applicant:
                case RoleLevel.Unkonw:
                default:
                    break;
            }

            var Count = queryEntities.Count();
            var datas = queryEntities.PagingIf(request, isPage).ToArray();

            return new PagedResultDto<OrganizationReportResponseDto>() { Items = datas, TotalCount = Count };
        }
        /// <summary>
        /// 财务审核岗位报表
        /// </summary>
        /// <returns></returns>
        public async Task<PagedResultDto<FinancialAuditPositionReponseDto>> FinancialAuditPositionReportAsync(FinancialAuditRequestDto request, bool isPage = true)
        {
            var personCenterService = LazyServiceProvider.LazyGetService<IPersonCenterService>();
            var roleLevel = personCenterService.GetMyRoleLevel();
            RoleLevel[] allowLevel = [RoleLevel.Admin, RoleLevel.Manager];
            if (!allowLevel.Contains(roleLevel))// 没有数据权限
                return new PagedResultDto<FinancialAuditPositionReponseDto>();
            var ids = await personCenterService.GetIdsByRoles(roleLevel);

            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var department = (await dataverseService.GetOrganizations(stateCode: null));
            var departmentDict = department.ToDictionary(s => s.Id);
            var query = new QueryExpression("spk_extensioncode");
            query.ColumnSet.AddColumns("spk_position", "spk_organization", "spk_staffname", "spk_extensioncode2");
            query.Criteria.AddCondition(new ConditionExpression("statecode", ConditionOperator.Equal, 0));

            var linkPosition = query.AddLink("spk_positionmasterdata", "spk_position", "spk_positionmasterdataid");
            linkPosition.EntityAlias = "position";
            linkPosition.LinkCriteria.AddCondition(new ConditionExpression("spk_positiontypename", ConditionOperator.Equal, (int)PositionType.FinancialApproval));//******** ytw 只查询财务审批岗位
            linkPosition.LinkCriteria.AddCondition(new ConditionExpression("statecode", ConditionOperator.Equal, 0));

            var user = query.AddLink("spk_staffmasterdata", "spk_staffname", "spk_staffmasterdataid");
            user.Columns.AddColumns("spk_name", "spk_staffaccount", "spk_staffemail");
            user.LinkCriteria.AddCondition(new ConditionExpression("statecode", ConditionOperator.Equal, 0));
            user.EntityAlias = "user";
            var org = query.AddLink("spk_organizationalmasterdata", "spk_organization", "spk_organizationalmasterdataid");
            org.Columns.AddColumns("spk_orgcode");
            org.LinkCriteria.AddCondition(new ConditionExpression("statecode", ConditionOperator.Equal, 0));
            org.EntityAlias = "org";
            var query1 = new QueryExpression("spk_financialapprovalamountmatrix");
            query1.ColumnSet.AddColumns("spk_bu", "spk_procurementfinance", "spk_name");
            query1.Criteria.AddCondition(new ConditionExpression("statecode", ConditionOperator.Equal, 0));
            var query2 = new QueryExpression("spk_currencyconfig");
            query2.ColumnSet.AddColumns("spk_rmbplanrate");
            var currency = query2.AddLink("spk_dictionary", "spk_currency", "spk_dictionaryid");
            currency.LinkCriteria.AddCondition(new ConditionExpression("spk_code", ConditionOperator.Equal, "USD"));
            currency.EntityAlias = "alias";
            //审批岗位数据
            var entities = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);
            //审批矩阵金额
            var entities1 = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query1);
            var entities2 = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query2);
            //美元兑人名币汇率
            var planRate = entities2.First().GetAttributeValue<decimal>("spk_rmbplanrate");
            //审批矩阵金额
            var queryData = entities1.Select(s => new
            {
                Level = s.GetAttributeValue<string>("spk_name"),
                BuId = s.GetAttributeValue<EntityReference>("spk_bu").Id,
                ProcurementFinance = s.GetAttributeValue<Money>("spk_procurementfinance")?.Value,
            }).ToDictionary(k => $"{k.BuId}*{k.Level}", v => v.ProcurementFinance);
            var querydata = entities.Select(s =>
            {
                var (BuId, BuName) = GetUserBu(departmentDict, s.GetAttributeValue<EntityReference>("spk_organization")?.Id);
                var Level = s.GetAttributeValue<int>("spk_extensioncode2");
                var ProcurementFinance = queryData.GetValueOrDefault($"{BuId}*{Level}");
                return new FinancialAuditPositionReponseDto
                {
                    BuId = BuId,
                    BuName = BuName,
                    OrgName = s.GetAttributeValue<EntityReference>("spk_organization").Name,
                    OrgCode = s.GetAttributeValue<AliasedValue>("org.spk_orgcode")?.Value.ToString() ?? "",
                    UserName = s.GetAttributeValue<AliasedValue>("user.spk_name")?.Value.ToString() ?? "",
                    Account = s.GetAttributeValue<AliasedValue>("user.spk_staffaccount")?.Value.ToString() ?? "",
                    Email = s.GetAttributeValue<AliasedValue>("user.spk_staffemail")?.Value.ToString() ?? "",
                    Level = Level,
                    PositionName = s.GetAttributeValue<EntityReference>("spk_position")?.Name,
                    ProcurementFinance = ProcurementFinance,
                    RMBProcurementFinance = ProcurementFinance.HasValue ? Math.Round(ProcurementFinance.Value * planRate, 4) : ProcurementFinance,
                };
            });

            querydata = querydata.WhereIf(request.BuId.HasValue, m => m.BuId == request.BuId)
                .WhereIf(!request.OrgName.IsNullOrWhiteSpace(), m => m.OrgName.Contains(request.OrgName))
                .WhereIf(!request.UserName.IsNullOrWhiteSpace(), m => m.UserName.Contains(request.UserName));
            switch (roleLevel)
            {
                case RoleLevel.Admin:
                    break;
                case RoleLevel.Manager:
                    querydata = querydata.Where(a => ids.Contains(a.BuId ?? Guid.Empty));
                    break;
                case RoleLevel.Leader:
                case RoleLevel.Owner:
                case RoleLevel.Applicant:
                case RoleLevel.Unkonw:
                default:
                    break;
            }

            var Count = querydata.Count();
            querydata = querydata.PagingIf(request, isPage);
            var datas = querydata.ToArray();
            return new PagedResultDto<FinancialAuditPositionReponseDto>() { Items = datas, TotalCount = Count };
        }



        public async Task<int> TestCallSP()
        {
            var sqlRepository = LazyServiceProvider.LazyGetService<ISqlRepository>();

            var outputParameter = new SqlParameter("@allRowsCnt", SqlDbType.Int) { Direction = ParameterDirection.Output };

            var parameters = new[] { new SqlParameter("@getRowsCnt", new Random().Next(10)), outputParameter };

            var query = await sqlRepository.SqlQueryRow<GetReportLisEpdResponseDto>("EXEC [USP_RPT_Test] @getRowsCnt, @allRowsCnt out", parameters);
            var datas = await query.ToListAsync();

            Console.WriteLine($"====={datas[0].PrCode}=====");

            Console.WriteLine($"=====输出参数：{parameters[1].Value}");

            return 1;
        }

        public async Task<int> GenerateReportEPDAsync()
        {
            var sqlRepository = LazyServiceProvider.LazyGetService<ISqlRepository>();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();

            var epdOrgs = await dataverseService.GetOrganizations("EPD", stateCode: null);
            var epdBuId = epdOrgs.FirstOrDefault(x => x.DepartmentName == "EPD" && x.OrganizationType == Organization.OrganizationType.Bu)?.Id;

            if (!epdBuId.HasValue)
                return 0;

            await UpdateDictsTabs(sqlRepository);

            var outputParameter = new SqlParameter("@AllRowsCnt", SqlDbType.Int) { Direction = ParameterDirection.Output };

            var parameters = new[] {
                outputParameter,
                new SqlParameter("@BuId", epdBuId.Value.ToString())
            };

            //var query = await sqlRepository.SqlQueryRow<int>("EXEC [USP_RPT_EPD] @AllRowsCnt out", parameters);
            //没有返回值的执行
            //var query = await sqlRepository.ExecuteSqlRaw("EXEC [USP_RPT_EPD] @AllRowsCnt out", 60, parameters);
            var query = await sqlRepository.ExecuteSqlRawAsync("EXEC [dbo].[USP_RPT_EPD] @AllRowsCnt out,@BuId", 1800, parameters);//timeout时间根据脚本执行耗时加倍

            Console.WriteLine($"===================*************==================输出参数：{outputParameter.Value}");

            return (int)outputParameter.Value;
        }

        public async Task<int> GenerateReporWholeProcessAsync()
        {
            var sqlRepository = LazyServiceProvider.LazyGetService<ISqlRepository>();

            await UpdateDictsTabs(sqlRepository);

            var outputParameter = new SqlParameter("@AllRowsCnt", SqlDbType.Int) { Direction = ParameterDirection.Output };

            var parameters = new[] { outputParameter };

            var query = await sqlRepository.ExecuteSqlRawAsync("EXEC [dbo].[USP_RPT_WholeProcess] @AllRowsCnt out", 1800, parameters);

            Console.WriteLine($"===================*************==================输出参数：{outputParameter.Value}");

            return (int)outputParameter.Value;
        }

        public async Task<int> GenerateReportAccrualAsync()
        {
            var sqlRepository = LazyServiceProvider.LazyGetService<ISqlRepository>();

            await UpdateDictsTabs(sqlRepository);

            var outputParameter = new SqlParameter("@AllRowsCnt", SqlDbType.Int) { Direction = ParameterDirection.Output };

            var parameters = new[] { outputParameter };

            var query = await sqlRepository.ExecuteSqlRawAsync("EXEC [dbo].[USP_RPT_Accrual] @AllRowsCnt out", 1800, parameters);

            Console.WriteLine($"===================*************==================输出参数：{outputParameter.Value}");

            return (int)outputParameter.Value;
        }

        /// <summary>
        /// 生成采购申请导出数据
        /// </summary>
        /// <returns></returns>
        public async Task<bool> GeneratePRApplicationReportAsync()
        {
            var queryPr = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var queryPrDetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            //有反冲行采购申请明细
            var queryPrDetailHedge = queryPrDetail.Where(a => a.HedgePrDetailId != null);
            //没有反冲行采购申请明细
            var queryPrDetailNotHedge = queryPrDetail.Where(a => a.HedgePrDetailId == null);
            var queryMasterBudget = await LazyServiceProvider.LazyGetService<IBdMasterBudgetRepository>().GetQueryableAsync();
            var querySubBudget = await LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>().GetQueryableAsync();
            var queryUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var queryPrProductApportionment = await LazyServiceProvider.LazyGetService<IPurPRApplicationProductApportionmentRepository>().GetQueryableAsync();
            var queryBudgetReturn = await LazyServiceProvider.LazyGetService<IBdBudgetReturnRepository>().GetQueryableAsync();
            var queryPo = await LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>().GetQueryableAsync();
            queryPo = queryPo.Where(a => a.Status != PurOrderStatus.Rejected && a.Status != PurOrderStatus.Invalid && a.Status != PurOrderStatus.Draft);
            var queryPoDetail = await LazyServiceProvider.LazyGetService<IPurPOApplicationDetailsRepository>().GetQueryableAsync();
            var wfQuery = await LazyServiceProvider.LazyGetService<IWfApprovalTaskRepository>().GetQueryableAsync();
            var sqlRepository = LazyServiceProvider.LazyGetService<ISqlRepository>();

            var queryPoDetailGroup = queryPo.Join(queryPoDetail, a => a.Id, a => a.POApplicationId, (a, b) => new { PO = a, PODetail = b })
                .GroupBy(a => new { a.PODetail.PRDetailId, a.PO.Status })
                .Select(a => new { PRDetailId = a.Key.PRDetailId, a.Key.Status, ExchangeRate = (decimal?)a.Max(a => a.PO.ExchangeRate), TotalAmount = (decimal?)a.Sum(x => x.PODetail.TotalAmount), TotalAmountNoTax = (decimal?)a.Sum(x => x.PODetail.TotalAmountNoTax) });

            //预算返还,按明细行累加，用于计算POSaving(RMB)    GRVar(RMB)  PAVar(RMB)
            var queryBRGroupO = queryBudgetReturn.Where(b => b.ReturnSourceCode.StartsWith("O")).GroupBy(a => new { a.PrId, a.PdRowNo })
                .Select(a => new { PrId = (Guid?)a.Key.PrId, PdRowNo = (int?)a.Key.PdRowNo, Count = (int?)a.Count(), Amount = (decimal?)a.Sum(x => x.Amount) });
            var queryBRGroupG = queryBudgetReturn.Where(b => b.ReturnSourceCode.StartsWith("G")).GroupBy(a => new { a.PrId, a.PdRowNo })
                .Select(a => new { PrId = (Guid?)a.Key.PrId, PdRowNo = (int?)a.Key.PdRowNo, Count = (int?)a.Count(), Amount = (decimal?)a.Sum(x => x.Amount) });
            var queryBRGroupA = queryBudgetReturn.Where(b => b.ReturnSourceCode.StartsWith("A")).GroupBy(a => new { a.PrId, a.PdRowNo })
                .Select(a => new { PrId = (Guid?)a.Key.PrId, PdRowNo = (int?)a.Key.PdRowNo, Count = (int?)a.Count(), Amount = (decimal?)a.Sum(x => x.Amount) });

            //取WfApprovalTasks中最近的一条审批记录
            var sql = @"SELECT *
                FROM WfApprovalTasks AS t1
                WHERE t1.CreationTime = (
                    SELECT MAX(t2.CreationTime)
                    FROM WfApprovalTasks AS t2
                    WHERE t2.FormNo = t1.FormNo
                )";

            var wfSqlQuery = await sqlRepository.SqlQueryRow<WfApprovalTaskDto>(sql);

            try
            {
                var query = queryPr
                    .Where(a => a.Status != PurPRApplicationStatus.Draft)
                    .Where(a => a.ApplyTime >= Convert.ToDateTime("2022-01-01 00:00:00"))
                    //.Where(a => a.ApplyTime < Convert.ToDateTime("2025-02-13 23:59:59"))
                    //.Where(a => a.ApplicationCode == "P1907010007")
                    .Join(queryPrDetailNotHedge, a => a.Id, a => a.PRApplicationId, (a, b) => new { pr = a, prDetail = b })
                    //取主预算描述
                    .GroupJoin(queryMasterBudget, a => a.pr.BudgetId, a => a.Id, (a, b) => new { a.pr, a.prDetail, MasterBudget = b })
                    .SelectMany(a => a.MasterBudget.DefaultIfEmpty(), (a, b) => new { a.pr, a.prDetail, MasterBudgetDesc = b.Description })
                    //取子预算描述，负责人
                    .GroupJoin(querySubBudget, a => a.pr.SubBudgetId, a => a.Id, (a, b) => new { a.pr, a.prDetail, a.MasterBudgetDesc, SubBudget = b })
                    .SelectMany(a => a.SubBudget.DefaultIfEmpty(), (a, b) => new { a.pr, a.prDetail, a.MasterBudgetDesc, SubBudgetDesc = b.Description, SubBudgetOwnerId = b.OwnerId })
                    .GroupJoin(queryUser, a => a.SubBudgetOwnerId, a => a.Id, (a, b) => new { a.pr, a.prDetail, a.MasterBudgetDesc, a.SubBudgetDesc, Users = b })
                    .SelectMany(a => a.Users.DefaultIfEmpty(), (a, b) => new { a.pr, a.prDetail, a.MasterBudgetDesc, a.SubBudgetDesc, SubBudgetOwnerName = b.Name })
                    //关联产品分摊，取分摊比例，产品信息
                    .GroupJoin(queryPrProductApportionment, a => a.prDetail.Id, a => a.PRApplicationDetailId, (a, b) => new
                    { a.pr, a.prDetail, a.MasterBudgetDesc, a.SubBudgetDesc, a.SubBudgetOwnerName, prProductApportionment = b })
                    .SelectMany(a => a.prProductApportionment.DefaultIfEmpty(), (a, b) => new { a.pr, a.prDetail, a.MasterBudgetDesc, a.SubBudgetDesc, a.SubBudgetOwnerName, prProductApportionment = b })
                    //关联预算返还
                    .GroupJoin(queryBRGroupO, a => new { PRID = (Guid?)a.prDetail.PRApplicationId, RowNo = (int?)a.prDetail.RowNo }, a => new { PRID = a.PrId, RowNo = a.PdRowNo },
                                (a, b) => new { a.pr, a.prDetail, a.MasterBudgetDesc, a.SubBudgetDesc, a.SubBudgetOwnerName, a.prProductApportionment, POBudgetReturn = b })
                    .SelectMany(a => a.POBudgetReturn.DefaultIfEmpty(), (a, b) => new { a.pr, a.prDetail, a.MasterBudgetDesc, a.SubBudgetDesc, a.SubBudgetOwnerName, a.prProductApportionment, POBudgetReturn = b })
                    .GroupJoin(queryBRGroupG, a => new { PRID = (Guid?)a.prDetail.PRApplicationId, RowNo = (int?)a.prDetail.RowNo }, a => new { PRID = a.PrId, RowNo = a.PdRowNo },
                                (a, b) => new { a.pr, a.prDetail, a.MasterBudgetDesc, a.SubBudgetDesc, a.SubBudgetOwnerName, a.prProductApportionment, a.POBudgetReturn, GRBudgetReturn = b })
                    .SelectMany(a => a.GRBudgetReturn.DefaultIfEmpty(), (a, b) => new { a.pr, a.prDetail, a.MasterBudgetDesc, a.SubBudgetDesc, a.SubBudgetOwnerName, a.prProductApportionment, a.POBudgetReturn, GRBudgetReturn = b })
                    .GroupJoin(queryBRGroupA, a => new { PRID = (Guid?)a.prDetail.PRApplicationId, RowNo = (int?)a.prDetail.RowNo }, a => new { PRID = a.PrId, RowNo = a.PdRowNo },
                                (a, b) => new { a.pr, a.prDetail, a.MasterBudgetDesc, a.SubBudgetDesc, a.SubBudgetOwnerName, a.prProductApportionment, a.POBudgetReturn, a.GRBudgetReturn, PABudgetReturn = b })
                    .SelectMany(a => a.PABudgetReturn.DefaultIfEmpty(), (a, b) => new { a.pr, a.prDetail, a.MasterBudgetDesc, a.SubBudgetDesc, a.SubBudgetOwnerName, a.prProductApportionment, a.POBudgetReturn, a.GRBudgetReturn, PABudgetReturn = b })

                    //关联PO明细，用于计算POVAT(RMB)
                    .GroupJoin(queryPoDetailGroup, a => a.prDetail.Id, a => a.PRDetailId,
                                (a, b) => new
                                {
                                    a.pr,
                                    a.prDetail,
                                    a.MasterBudgetDesc,
                                    a.SubBudgetDesc,
                                    a.SubBudgetOwnerName,
                                    a.prProductApportionment,
                                    a.POBudgetReturn,
                                    a.GRBudgetReturn,
                                    a.PABudgetReturn,
                                    PODetail = b

                                })
                    .SelectMany(a => a.PODetail.DefaultIfEmpty(),
                                (a, b) => new
                                {
                                    a.pr,
                                    a.prDetail,
                                    a.MasterBudgetDesc,
                                    a.SubBudgetDesc,
                                    a.SubBudgetOwnerName,
                                    a.POBudgetReturn,
                                    a.GRBudgetReturn,
                                    a.PABudgetReturn,
                                    a.prProductApportionment,
                                    PODetail = b
                                })
                    .GroupJoin(wfSqlQuery, a => a.pr.ApplicationCode, a => a.FormNo, (a, b) => new
                    { a.pr, a.prDetail, a.MasterBudgetDesc, a.SubBudgetDesc, a.SubBudgetOwnerName, a.POBudgetReturn, a.GRBudgetReturn, a.PABudgetReturn, a.prProductApportionment, a.PODetail, WorkflowTask = b })
                    .SelectMany(a => a.WorkflowTask.DefaultIfEmpty(), (a, b) => new { a.pr, a.prDetail, a.MasterBudgetDesc, a.SubBudgetDesc, a.SubBudgetOwnerName, a.POBudgetReturn, a.GRBudgetReturn, a.PABudgetReturn, a.prProductApportionment, a.PODetail, WorkflowTask = b })
                    .Union
                    (
                    queryPr
                    .Where(a => a.Status != PurPRApplicationStatus.Draft)
                    .Where(a => a.ApplyTime >= Convert.ToDateTime("2022-01-01 00:00:00"))
                    //.Where(a => a.ApplyTime < Convert.ToDateTime("2025-02-13 23:59:59"))
                    .Join(queryPrDetailHedge, a => a.Id, a => a.PRApplicationId, (a, b) => new { pr = a, prDetail = b })
                    //取主预算描述
                    .GroupJoin(queryMasterBudget, a => a.pr.BudgetId, a => a.Id, (a, b) => new { a.pr, a.prDetail, MasterBudget = b })
                    .SelectMany(a => a.MasterBudget.DefaultIfEmpty(), (a, b) => new { a.pr, a.prDetail, MasterBudgetDesc = b.Description })
                    //取子预算描述，负责人
                    .GroupJoin(querySubBudget, a => a.pr.SubBudgetId, a => a.Id, (a, b) => new { a.pr, a.prDetail, a.MasterBudgetDesc, SubBudget = b })
                    .SelectMany(a => a.SubBudget.DefaultIfEmpty(), (a, b) => new { a.pr, a.prDetail, a.MasterBudgetDesc, SubBudgetDesc = b.Description, SubBudgetOwnerId = b.OwnerId })
                    .GroupJoin(queryUser, a => a.SubBudgetOwnerId, a => a.Id, (a, b) => new { a.pr, a.prDetail, a.MasterBudgetDesc, a.SubBudgetDesc, Users = b })
                    .SelectMany(a => a.Users.DefaultIfEmpty(), (a, b) => new { a.pr, a.prDetail, a.MasterBudgetDesc, a.SubBudgetDesc, SubBudgetOwnerName = b.Name })
                    //关联产品分摊，取分摊比例，产品信息
                    .GroupJoin(queryPrProductApportionment, a => a.prDetail.HedgePrDetailId, a => a.PRApplicationDetailId, (a, b) => new
                    { a.pr, a.prDetail, a.MasterBudgetDesc, a.SubBudgetDesc, a.SubBudgetOwnerName, prProductApportionment = b })
                    .SelectMany(a => a.prProductApportionment.DefaultIfEmpty(), (a, b) => new { a.pr, a.prDetail, a.MasterBudgetDesc, a.SubBudgetDesc, a.SubBudgetOwnerName, prProductApportionment = b })
                    //关联预算返还
                    .GroupJoin(queryBRGroupO, a => new { PRID = (Guid?)a.prDetail.PRApplicationId, RowNo = (int?)a.prDetail.RowNo }, a => new { PRID = a.PrId, RowNo = a.PdRowNo },
                                (a, b) => new { a.pr, a.prDetail, a.MasterBudgetDesc, a.SubBudgetDesc, a.SubBudgetOwnerName, a.prProductApportionment, POBudgetReturn = b })
                    .SelectMany(a => a.POBudgetReturn.DefaultIfEmpty(), (a, b) => new { a.pr, a.prDetail, a.MasterBudgetDesc, a.SubBudgetDesc, a.SubBudgetOwnerName, a.prProductApportionment, POBudgetReturn = b })
                    .GroupJoin(queryBRGroupG, a => new { PRID = (Guid?)a.prDetail.PRApplicationId, RowNo = (int?)a.prDetail.RowNo }, a => new { PRID = a.PrId, RowNo = a.PdRowNo },
                                (a, b) => new { a.pr, a.prDetail, a.MasterBudgetDesc, a.SubBudgetDesc, a.SubBudgetOwnerName, a.prProductApportionment, a.POBudgetReturn, GRBudgetReturn = b })
                    .SelectMany(a => a.GRBudgetReturn.DefaultIfEmpty(), (a, b) => new { a.pr, a.prDetail, a.MasterBudgetDesc, a.SubBudgetDesc, a.SubBudgetOwnerName, a.prProductApportionment, a.POBudgetReturn, GRBudgetReturn = b })
                    .GroupJoin(queryBRGroupA, a => new { PRID = (Guid?)a.prDetail.PRApplicationId, RowNo = (int?)a.prDetail.RowNo }, a => new { PRID = a.PrId, RowNo = a.PdRowNo },
                                (a, b) => new { a.pr, a.prDetail, a.MasterBudgetDesc, a.SubBudgetDesc, a.SubBudgetOwnerName, a.prProductApportionment, a.POBudgetReturn, a.GRBudgetReturn, PABudgetReturn = b })
                    .SelectMany(a => a.PABudgetReturn.DefaultIfEmpty(), (a, b) => new { a.pr, a.prDetail, a.MasterBudgetDesc, a.SubBudgetDesc, a.SubBudgetOwnerName, a.prProductApportionment, a.POBudgetReturn, a.GRBudgetReturn, PABudgetReturn = b })

                    //关联PO明细，用于计算POVAT(RMB)
                    .GroupJoin(queryPoDetailGroup, a => a.prDetail.Id, a => a.PRDetailId,
                                (a, b) => new
                                {
                                    a.pr,
                                    a.prDetail,
                                    a.MasterBudgetDesc,
                                    a.SubBudgetDesc,
                                    a.SubBudgetOwnerName,
                                    a.prProductApportionment,
                                    a.POBudgetReturn,
                                    a.GRBudgetReturn,
                                    a.PABudgetReturn,
                                    PODetail = b

                                })
                    .SelectMany(a => a.PODetail.DefaultIfEmpty(),
                                (a, b) => new
                                {
                                    a.pr,
                                    a.prDetail,
                                    a.MasterBudgetDesc,
                                    a.SubBudgetDesc,
                                    a.SubBudgetOwnerName,
                                    a.POBudgetReturn,
                                    a.GRBudgetReturn,
                                    a.PABudgetReturn,
                                    a.prProductApportionment,
                                    PODetail = b
                                })
                    .GroupJoin(wfSqlQuery, a => a.pr.ApplicationCode, a => a.FormNo, (a, b) => new
                    { a.pr, a.prDetail, a.MasterBudgetDesc, a.SubBudgetDesc, a.SubBudgetOwnerName, a.POBudgetReturn, a.GRBudgetReturn, a.PABudgetReturn, a.prProductApportionment, a.PODetail, WorkflowTask = b })
                    .SelectMany(a => a.WorkflowTask.DefaultIfEmpty(), (a, b) => new { a.pr, a.prDetail, a.MasterBudgetDesc, a.SubBudgetDesc, a.SubBudgetOwnerName, a.POBudgetReturn, a.GRBudgetReturn, a.PABudgetReturn, a.prProductApportionment, a.PODetail, WorkflowTask = b })
                    )
                    .Select(a => new PurPRApplicationReportDto()
                    {
                        ApplicationCode = a.pr.ApplicationCode,
                        ApplyTime = a.pr.ApplyTime,
                        ApplyUserId = a.pr.ApplyUserId,
                        ApplyUserIdName = a.pr.ApplyUserIdName,
                        ApplyUserDept = a.pr.ApplyUserDept,
                        ApplyUserDeptName = a.pr.ApplyUserDeptName,
                        AgentId = a.pr.AgentId,
                        TotalAmountRMB = a.pr.TotalAmountRMB,
                        Currency = a.pr.Currency,
                        CompanyId = a.pr.CompanyId,
                        CompanyIdName = a.pr.CompanyIdName,
                        ApplyUserBuName = a.pr.ApplyUserBuName,
                        CostCenter = a.pr.CostCenter,
                        BudgetCode = a.pr.BudgetCode,
                        BudgetDescription = a.MasterBudgetDesc,
                        SubBudgetId = a.pr.SubBudgetId,
                        SubBudgetCode = a.pr.SubBudgetCode,
                        SubBudgetDescription = a.SubBudgetDesc,
                        SubBudgetOwner = a.SubBudgetOwnerName,
                        Status = a.pr.Status,
                        RowNo = a.prDetail.RowNo,
                        PayMethod = a.prDetail.PayMethod.GetDescription(),
                        CompanyCode = a.pr.CompanyCode,
                        ApplyUserBu = a.pr.ApplyUserBu,
                        CostCenterCode = a.pr.CostCenterCode,
                        CostCenterName = a.pr.CostCenterName,
                        CostNatureCode = a.prDetail.CostNatureCode,
                        CostNatureName = a.prDetail.CostNatureName,
                        ProductCode = a.prProductApportionment == null ? string.Empty : a.prProductApportionment.ProductCode,
                        CityId = a.prDetail.CityId,
                        DetailTotalAmount = a.prProductApportionment == null ? a.prDetail.TotalAmount * 1 : a.prDetail.TotalAmount * Convert.ToDecimal(a.prProductApportionment.Ratio / 100),//按比例分摊
                        DetailTotalAmountRMB = a.prProductApportionment == null ? a.prDetail.TotalAmountRMB * 1 : a.prDetail.TotalAmountRMB * Convert.ToDecimal(a.prProductApportionment.Ratio / 100),
                        //当该行有PO对应的预算返还记录时，计算该行在PO内对应行(可能多行)的总含税金额 - 总不含税金额，计算结果需要转换为0 - 原计算结果
                        POVatRMB = a.POBudgetReturn == null || a.PODetail.Status != PurOrderStatus.Closed ? 0 : 0 - ((a.PODetail.TotalAmount - a.PODetail.TotalAmountNoTax) * Convert.ToDecimal(a.prProductApportionment == null ? 1 : a.prProductApportionment.Ratio / 100) * Convert.ToDecimal(a.PODetail.ExchangeRate)),
                        //当该行有PO对应的预算返还记录时，计算该行的PO返还金额+上述计算出的PO VAT金额，计算结果需要转换为0-原计算结果
                        POSavingRMB = a.POBudgetReturn == null ? 0 : 0 - ((a.POBudgetReturn.Amount * Convert.ToDecimal(a.prProductApportionment == null ? 1 : a.prProductApportionment.Ratio / 100)) + (0 - ((a.PODetail.TotalAmount - a.PODetail.TotalAmountNoTax) * Convert.ToDecimal(a.prProductApportionment == null ? 1 : a.prProductApportionment.Ratio / 100) * Convert.ToDecimal(a.PODetail.ExchangeRate)))),
                        GRVarRMB = a.GRBudgetReturn == null ? 0 : 0 - (a.GRBudgetReturn.Amount * Convert.ToDecimal(a.prProductApportionment == null ? 1 : a.prProductApportionment.Ratio / 100)),
                        PAVarRMB = a.PABudgetReturn == null ? 0 : 0 - (a.PABudgetReturn.Amount * Convert.ToDecimal(a.prProductApportionment == null ? 1 : a.prProductApportionment.Ratio / 100)),
                        CityIdName = a.prDetail.CityIdName,
                        ProductName = a.prProductApportionment == null ? string.Empty : a.prProductApportionment.ProductName,
                        Content = a.prDetail.Content,
                        VendorName = a.prDetail.VendorName,
                        RceNo = a.prDetail.RceNo,
                        IcbAmount = a.prDetail.IcbAmount,
                        CommitmentAmountRMB = a.prProductApportionment == null ? null : (a.prDetail.TotalAmountRMB * (a.prProductApportionment != null ? Convert.ToDecimal(a.prProductApportionment.Ratio / 100) : 0)
                                + ((a.POBudgetReturn == null || a.PODetail.Status != PurOrderStatus.Closed ? 0 : 0 - ((a.PODetail.TotalAmount - a.PODetail.TotalAmountNoTax) * Convert.ToDecimal(a.prProductApportionment.Ratio / 100) * Convert.ToDecimal(a.PODetail.ExchangeRate))) ?? 0)
                                + ((a.POBudgetReturn == null ? 0 : 0 - ((a.POBudgetReturn.Amount * Convert.ToDecimal(a.prProductApportionment.Ratio / 100)) + (0 - ((a.PODetail.TotalAmount - a.PODetail.TotalAmountNoTax) * Convert.ToDecimal(a.prProductApportionment.Ratio / 100) * Convert.ToDecimal(a.PODetail.ExchangeRate))))) ?? 0)
                                + ((a.GRBudgetReturn == null ? 0 : 0 - (a.GRBudgetReturn.Amount * Convert.ToDecimal(a.prProductApportionment.Ratio / 100))) ?? 0)
                                + ((a.PABudgetReturn == null ? 0 : 0 - (a.PABudgetReturn.Amount * Convert.ToDecimal(a.prProductApportionment.Ratio / 100))) ?? 0)),
                        COA = a.prProductApportionment == null ?
                        $"{a.pr.CompanyCode}.{{0}}.{a.pr.CostCenterCode}.{a.prDetail.CostNatureCode}..{a.prDetail.CityIdName.Substring(a.prDetail.CityIdName.Length - 4)}"
                        : $"{a.pr.CompanyCode}.{{0}}.{a.pr.CostCenterCode}.{a.prDetail.CostNatureCode}.{a.prProductApportionment.ProductCode}.{a.prDetail.CityIdName.Substring(a.prDetail.CityIdName.Length - 4)}",
                        CurrentApprovalNode = a.pr.Status == PurPRApplicationStatus.Approving && !string.IsNullOrEmpty(a.WorkflowTask.StepDesc) ? a.WorkflowTask.StepDesc : string.Empty,
                        CurrentApprovalUser = a.pr.Status == PurPRApplicationStatus.Approving && !string.IsNullOrEmpty(a.WorkflowTask.ApproverText) ? a.WorkflowTask.ApproverText : string.Empty,
                        EstimateDate = a.prDetail.EstimateDate
                    })
                    .ToList();

                var buCodingCfgs = await LazyServiceProvider.LazyGetService<IDataverseService>().GetBuCodingCfgAsync(stateCode: null);
                var dictBuCodingCfg = buCodingCfgs.GroupBy(a => a.BuId).ToDictionary(a => a.Key, a => a.FirstOrDefault().BuCode);

                var count = query.Count();
                var pageSize = 1000;
                var pageNumber = count / pageSize;
                var prReportRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationReportRepository>();

                var tableName = "dbo.PurPRApplicationReports";
                var clearnSql = $@"
                IF OBJECT_ID('{tableName}', 'U') IS NOT NULL
                BEGIN
                    TRUNCATE TABLE {tableName};
                END";
                var truncatePrData = await sqlRepository.ExecuteSqlRawAsync(clearnSql);

                var guidGenerator = LazyServiceProvider.LazyGetService<IGuidGenerator>();
                for (int i = 0; i < pageNumber + 1; i++)
                {
                    var pagedData = query.Skip(i * pageSize).Take(pageSize).ToList();

                    var datas = ObjectMapper.Map<List<PurPRApplicationReportDto>, List<PurPRApplicationReport>>(pagedData);
                    datas.ForEach(a =>
                    {
                        (a as IManualSetId<Guid>).SetId(guidGenerator.Create());

                        string buCode = a.ApplyUserBuName;
                        if (a.ApplyUserBu.HasValue && dictBuCodingCfg.TryGetValue(a.ApplyUserBu.Value, out string code))
                            buCode = code;

                        a.COA = string.Format(a.COA, buCode);
                    });
                    //await prReportRepository.InsertManyAsync(datas, true);
                    var context = await prReportRepository.GetDbContextAsync();
                    await context.BulkInsertAsync(datas);
                }

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"GeneratePRApplicationReportAsync Error :{ex.Message}");
            }

            return true;
        }

        /// <summary>
        /// 更新字典数据(存储过程需要的来自PP的基础数据)
        /// </summary>
        /// <param name="sqlRepository"></param>
        /// <returns></returns>
        private async Task<int> UpdateDictsTabs(ISqlRepository sqlRepository)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var dicBuCodingCfg = (await dataverseService.GetBuCodingCfgAsync(stateCode: null)).ToDictionary(s => s.BuId, s => string.IsNullOrEmpty(s.BuCode) ? "" : s.BuCode);
            var dicCities = (await dataverseService.GetSpecialCitiesAsync(stateCode: null)).ToDictionary(s => s.Id, s => string.IsNullOrEmpty(s.CityCode) ? "" : s.CityCode);
            var dicCompany = (await dataverseService.GetCompanyAsync(stateCode: null)).ToDictionary(x => x.CompanyCode, x => string.IsNullOrEmpty(x.CompanyName) ? "" : x.CompanyName);
            var dicProjectType = (await dataverseService.GetDictionariesAsync(type: DictionaryType.ProjectType, stateCode: null)).ToDictionary(s => s.Code, s => string.IsNullOrEmpty(s.Name) ? "" : s.Name);

            var tableName = "dbo.RPT_Tab_BuIdCode";
            var clearnSql = $@"
            IF OBJECT_ID('{tableName}', 'U') IS NOT NULL
            BEGIN
                TRUNCATE TABLE {tableName};
            END";
            var clearnRes = await sqlRepository.ExecuteSqlRawAsync(clearnSql);
            var batchSize = 20;
            var batches = dicBuCodingCfg.Select((kv, index) => new { kv.Key, kv.Value, Index = index }).GroupBy(a => a.Index / batchSize).Select(b => b.Select(c => new { c.Key, c.Value }).ToList()).ToList();
            foreach (var batch in batches)
            {
                var sqlBuilder = new StringBuilder();
                sqlBuilder.Append($"INSERT INTO {tableName} (Id, Code) VALUES ");
                var values = batch.Select(kv => $"('{kv.Key}', '{kv.Value}')");
                sqlBuilder.Append(string.Join(", ", values));
                sqlBuilder.Append(';');

                var sql = sqlBuilder.ToString();
                var insRes = await sqlRepository.ExecuteSqlRawAsync(sql);
            }

            tableName = "dbo.RPT_Tab_CityIdCode";
            clearnSql = $@"
            IF OBJECT_ID('{tableName}', 'U') IS NOT NULL
            BEGIN
                TRUNCATE TABLE {tableName};
            END";
            clearnRes = await sqlRepository.ExecuteSqlRawAsync(clearnSql);
            batches = dicCities.Select((kv, index) => new { kv.Key, kv.Value, Index = index }).GroupBy(a => a.Index / batchSize).Select(b => b.Select(c => new { c.Key, c.Value }).ToList()).ToList();
            foreach (var batch in batches)
            {
                var sqlBuilder = new StringBuilder();
                sqlBuilder.Append($"INSERT INTO {tableName} (Id, Code) VALUES ");
                var values = batch.Select(kv => $"('{kv.Key}', '{kv.Value}')");
                sqlBuilder.Append(string.Join(", ", values));
                sqlBuilder.Append(';');

                var sql = sqlBuilder.ToString();
                var insRes = await sqlRepository.ExecuteSqlRawAsync(sql);
            }

            tableName = "dbo.RPT_Tab_CompanyCodeName";
            clearnSql = $@"
            IF OBJECT_ID('{tableName}', 'U') IS NOT NULL
            BEGIN
                TRUNCATE TABLE {tableName};
            END";
            clearnRes = await sqlRepository.ExecuteSqlRawAsync(clearnSql);
            var comBatches = dicCompany.Select((kv, index) => new { kv.Key, kv.Value, Index = index }).GroupBy(a => a.Index / batchSize).Select(b => b.Select(c => new { c.Key, c.Value }).ToList()).ToList();
            foreach (var batch in comBatches)
            {
                var sqlBuilder = new StringBuilder();
                sqlBuilder.Append($"INSERT INTO {tableName} (Code, Name) VALUES ");
                var values = batch.Select(kv => $"('{kv.Key}', N'{kv.Value}')");
                sqlBuilder.Append(string.Join(", ", values));
                sqlBuilder.Append(';');

                var sql = sqlBuilder.ToString();
                var insRes = await sqlRepository.ExecuteSqlRawAsync(sql);
            }

            tableName = "dbo.RPT_Tab_ProjectType";
            clearnSql = $@"
            IF OBJECT_ID('{tableName}', 'U') IS NOT NULL
            BEGIN
                TRUNCATE TABLE {tableName};
            END";
            clearnRes = await sqlRepository.ExecuteSqlRawAsync(clearnSql);
            var projectBatches = dicProjectType.Select((kv, index) => new { kv.Key, kv.Value, Index = index }).GroupBy(a => a.Index / batchSize).Select(b => b.Select(c => new { c.Key, c.Value }).ToList()).ToList();
            foreach (var batch in projectBatches)
            {
                var sqlBuilder = new StringBuilder();
                sqlBuilder.Append($"INSERT INTO {tableName} (Code, Name) VALUES ");
                var values = batch.Select(kv => $"('{kv.Key}', N'{kv.Value}')");
                sqlBuilder.Append(string.Join(", ", values));
                sqlBuilder.Append(';');

                var sql = sqlBuilder.ToString();
                var insRes = await sqlRepository.ExecuteSqlRawAsync(sql);
            }

            return 1;
        }
        /// <summary>
        /// 更新角色及其数据范围的数据(用于查询报表时控制数据权限)
        /// </summary>
        /// <param name="sqlRepository"></param>
        /// <param name="tableName"></param>
        /// <returns></returns>
        private async Task<int> UpdateRolesIds(ISqlRepository sqlRepository, string tableName)
        {
            var personCenterService = LazyServiceProvider.LazyGetService<IPersonCenterService>();
            var rolesIds = await personCenterService.GetIdsByRoleLevels();

            var clearnSql = $@"
            IF OBJECT_ID('{tableName}', 'U') IS NOT NULL
            BEGIN
                TRUNCATE TABLE {tableName};
            END";
            var clearnRes = await sqlRepository.ExecuteSqlRawAsync(clearnSql);
            foreach (var kv in rolesIds)
            {
                var batchSize = 20;
                var batches = kv.Value.Select((a, index) => new { kv.Key, Value = a, Index = index }).GroupBy(a => a.Index / batchSize).Select(b => b.Select(c => new { c.Key, c.Value }).ToList()).ToList();
                foreach (var batch in batches)
                {
                    var sqlBuilder = new StringBuilder();
                    sqlBuilder.Append($"INSERT INTO {tableName} (RoleLevel, GuidValue) VALUES ");
                    var values = batch.Select(a => $"({(int)a.Key}, '{a.Value}')");
                    sqlBuilder.Append(string.Join(", ", values));
                    sqlBuilder.Append(';');

                    var sql = sqlBuilder.ToString();
                    var insRes = await sqlRepository.ExecuteSqlRawAsync(sql);
                }
            }

            return 1;
        }

        /// <summary>
        /// EPD采购报表（新）
        /// </summary>
        /// <param name="request"></param>
        /// <param name="isPage"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetReportLisEpdResponseDto>> GetEpdReportListAsync(EPDReportRequestDto request, bool isPage = true)
        {
            var sqlRepository = LazyServiceProvider.LazyGetService<ISqlRepository>();

            var outputParameter = new SqlParameter("@PgTotalCount", SqlDbType.Int) { Direction = ParameterDirection.Output };

            var sqlparams = new List<SqlParameter>();
            var dynamicParamsStr = string.Empty;

            sqlparams.AddRange([
                new SqlParameter("@PgIndex", request.PageIndex),
                new SqlParameter("@PgSize", isPage? request.PageSize:0),
                outputParameter]);
            dynamicParamsStr += "@PgIndex,@PgSize,@PgTotalCount out";

            var resRoles = await UpdateRolesIds(sqlRepository, "dbo.RPT_ParsedGUIDs_EPD");

            GetParametersEPD(request, sqlparams, ref dynamicParamsStr);

            var query = await sqlRepository.SqlQueryRow<GetReportLisEpdResponseDto>($"EXEC [USP_GetEpdReport] {dynamicParamsStr}", 120, [.. sqlparams]);
            var datas = await query.ToListAsync();

            return new PagedResultDto<GetReportLisEpdResponseDto>() { Items = datas, TotalCount = (int)outputParameter.Value };
        }
        #region 全流程报表相关
        public async Task<PagedResultDto<GetReportListWholeProcessResponseDto>> GetWprReportListAsync(WholeProcessReportRequestDto request, bool isPage = true)
        {
            var sqlRepository = LazyServiceProvider.LazyGetService<ISqlRepository>();

            var outputParameter = new SqlParameter("@PgTotalCount", SqlDbType.Int) { Direction = ParameterDirection.Output };

            var sqlparams = new List<SqlParameter>();
            var dynamicParamsStr = string.Empty;

            sqlparams.AddRange([
                    new SqlParameter("@PgIndex", request.PageIndex),
                    new SqlParameter("@PgSize", isPage? request.PageSize:0),//导出数据时不分页
                    outputParameter]);
            dynamicParamsStr += "@PgIndex,@PgSize,@PgTotalCount out";

            var resRoles = await UpdateRolesIds(sqlRepository, "dbo.RPT_ParsedGUIDs_WPR");

            GetParametersWPR(request, sqlparams, ref dynamicParamsStr);

            var query = await sqlRepository.SqlQueryRow<GetReportListWholeProcessResponseDto>($"EXEC [USP_GetWholeProcessReport] {dynamicParamsStr}", 120, [.. sqlparams]);
            var datas = await query.ToListAsync();
            datas.ForEach(a =>
            {
                a.PACurrentProcessor = "财务初审" == a.PurPAApplicationStatusText ? "" : a.PACurrentProcessor;
            });

            return new PagedResultDto<GetReportListWholeProcessResponseDto>() { Items = datas, TotalCount = (int)outputParameter.Value };
        }

        public async Task<IEnumerable<GetReportListWholeProcessResponseDto>> ExportOECReportListAsync(WholeProcessReportRequestDto request, bool isPage = false)
        {
            var sqlRepository = LazyServiceProvider.LazyGetService<ISqlRepository>();

            var outputParameter = new SqlParameter("@PgTotalCount", SqlDbType.Int) { Direction = ParameterDirection.Output };

            var sqlparams = new List<SqlParameter>();
            var dynamicParamsStr = string.Empty;

            sqlparams.AddRange([
                    new SqlParameter("@PgIndex", request.PageIndex),
                    new SqlParameter("@PgSize", isPage? request.PageSize:0),//导出数据时不分页
                    outputParameter]);
            dynamicParamsStr += "@PgIndex,@PgSize,@PgTotalCount out";

            #region check查询条件request的传值是否符合角色级别
            var personCenterService = LazyServiceProvider.LazyGetService<IPersonCenterService>();
            var roleLevel = personCenterService.GetMyRoleLevel();
            var ids = await personCenterService.GetIdsByRoles(roleLevel);
            sqlparams.AddRange([
                new SqlParameter("@RoleLevel", (int)roleLevel),
                new SqlParameter("@Ids", roleLevel==RoleLevel.Admin||!ids.Any()?"":ids.JoinAsString(","))]);
            dynamicParamsStr += ",@RoleLevel,@Ids";
            #endregion

            GetParametersWPR(request, sqlparams, ref dynamicParamsStr);

            var query = await sqlRepository.SqlQueryRow<GetReportListWholeProcessResponseDto>($"EXEC [USP_GetWholeProcessReport] {dynamicParamsStr}", 120, [.. sqlparams]);
            var datas = query.AsEnumerable();

            return datas;
        }

        /// <summary>
        /// 全流程报表 PAJoinGLH 相关数据入库中间表
        /// </summary>
        /// <returns></returns>
        public async Task FillPAJoinBpcsGlhsAsync()
        {
            var sqlRepository = LazyServiceProvider.LazyGetService<ISqlRepository>();
            await sqlRepository.ExecuteSqlRawAsync(SQLConst.PAJoinBpcsGlhSQL, 600);
        }
        #endregion
        public async Task<PagedResultDto<AccrualReportResponseDto>> GetAcrReportListAsync(AccrualReportRequestDto request, bool isPage = true)
        {
            var sqlRepository = LazyServiceProvider.LazyGetService<ISqlRepository>();

            var outputParameter = new SqlParameter("@PgTotalCount", SqlDbType.Int) { Direction = ParameterDirection.Output };

            var sqlparams = new List<SqlParameter>();
            var dynamicParamsStr = string.Empty;

            sqlparams.AddRange([
                    new SqlParameter("@PgIndex", request.PageIndex),
                    new SqlParameter("@PgSize", isPage? request.PageSize:0),
                    outputParameter]);
            dynamicParamsStr += "@PgIndex,@PgSize,@PgTotalCount out";

            #region check查询条件request的传值是否符合角色级别
            var personCenterService = LazyServiceProvider.LazyGetService<IPersonCenterService>();
            var roleLevel = personCenterService.GetMyRoleLevel();
            var ids = await personCenterService.GetIdsByRoles(roleLevel);
            sqlparams.AddRange([
                new SqlParameter("@RoleLevel", (int)roleLevel),
                new SqlParameter("@Ids", roleLevel==RoleLevel.Admin||!ids.Any()?"":ids.JoinAsString(","))]);
            dynamicParamsStr += ",@RoleLevel,@Ids";
            #endregion

            GetParametersAcr(request, sqlparams, ref dynamicParamsStr);

            var query = await sqlRepository.SqlQueryRow<AccrualReportResponseDto>($"EXEC [USP_GetAccrualReport] {dynamicParamsStr}", 120, [.. sqlparams]);
            var datas = await query.ToListAsync();

            Console.WriteLine($"===================*************==================输出参数：{outputParameter.Value}");

            return new PagedResultDto<AccrualReportResponseDto>() { Items = datas, TotalCount = (int)outputParameter.Value };
        }


        public async Task<PagedResultDto<UVW_WPR_ActiveResponseDto>> GetWprRptViewListAsync(WholeProcessReportRequestDto request)
        {
            var queryWprView = await LazyServiceProvider.LazyGetService<IUVW_WPR_ActiveReadonlyRepository>().GetQueryableAsync();
            var queryWpr = await LazyServiceProvider.LazyGetService<IRPT_WholeProcessReportReadonlyRepository>().GetQueryableAsync();

            var personCenterService = LazyServiceProvider.LazyGetService<IPersonCenterService>();
            var rolesIds = await personCenterService.GetIdsByRoleLevels(bizTypeCategory: ResignationTransfer.TaskFormCategory.PurchaseRequestApplication);

            //var query = queryWprView.Select(x => new { x.PrId, x.PrCode, x.PRItemNo, x.PRInitiator, x.PostDate, x.PRAmount, x.PRItemAmount, x.PoId, x.PoCode })
            var queryActive = queryWprView.Select(x => new
            {
                IsActive = 1,
                x.PrId,
                x.PrCode,
                x.PostDate,
                x.PRInitiator,
                x.Principal,
                x.Company,
                x.CompanyCode,
                x.BU,
                x.BuId,
                x.ApplyUserId,
                x.AgentId,
                x.TransfereeId,
                x.CompanyId,
                x.ApplyUserDept,
                x.SbOwnerId,
                x.BudgetId,
                x.ExpenseType,
                x.prdOriginalEstimateDate,
                x.BudgetCode,
                x.BudgetOwner,
                x.BudgetAmount,
                x.PRAmount,
                x.ExpenseTypeName,
                x.ExpenseNature,
                x.PRStatusText,
                x.PRCurrentProcessor,
                x.TotalProcessDays,
                x.PRItemNo,
                x.PRItemPaymenTypeText,
                x.HCPTier,
                x.HCPHospital,
                x.OriginalVendorName,
                x.VendorName,
                x.VendorType,
                x.PRItemAmount,
                x.COA,
                x.OrignalActivityDate,
                x.PRContent,
                x.RceNo,
                x.IcbAmount,
                x.IsPushedText,
                x.PRApproveDate,
                x.PrFinallyExpenseApprover,
                x.PrFinallyExpenseApprovedDate,
                x.PrFinanceFinallyApprover,
                x.PrFinanceFinallyApprovalDate,
                x.PushDate,
                x.PRItemProcessDays,
                x.PoId,
                x.POCode,
                x.POPostDate,
                x.POInitiator,
                x.POAmountTax,
                x.POAmount,
                x.POTotalBalanceAmount,
                x.POVAT,
                x.POVATNoTax,
                x.POStatus,
                x.POStatusText,
                x.POCurrentProcessor,
                x.POFirstApprover,
                x.PRType,
                x.PRPODays,
                x.POApproveDate,
                x.POProcessDays,
                x.IsRecoil,
                x.CostCenterCode,
                x.CostNatureCode,
                x.ProductId,
                x.ProductCode,
                x.CityId,
                x.GrId,
                x.GRCode,
                x.GRAmount,
                x.GRAmountNoTax,
                x.GRVAT,
                x.HistoricalReceiptAmount,
                x.HistoricalReceiptAmountNoTax,
                x.HistoricalReceiptAmountTax,
                x.GRDate,
                x.GRStatus,
                x.GRStatusText,
                x.GRVendorName,
                x.IsNotSameVendor,
                x.GRVendorCode,
                x.BpcsPmfvmVcrdte,
                x.BpcsPmfvmVctime,
                x.BpcsCreate,
                x.PaId,
                x.PACode,
                x.IsLastPaymentText,
                x.LatestPAText,
                x.DateOfLatestPA,
                x.PAPostDate,
                x.PAPostPOApproveProcessDays,
                x.PAAmount,
                x.PAAmountNoTax,
                x.PAVAT,
                x.PATotalAmountPaid,
                x.PATotalAmountPaidNoTax,
                x.PATotalAmountPaidTax,
                x.VendorScore,
                x.IFOReceivedPADate,
                x.IFOReceivedPAPostProcessDays,
                x.PAReviewDate,
                x.PAStatus,
                x.PurPAApplicationStatusText,
                x.PACurrentProcessor,
                x.PAProcessDays,
                x.BPCSAPNo,
                x.MPDate,
                x.MPPAPostingDays,
                x.EBankingDate,
                x.EBankingStatus,
                x.EBankingComment,
                x.EbankingMPDays,
                x.IsItemEnd,
                x.FinishedText
            })
                .WhereIf(!string.IsNullOrEmpty(request.PrCode), x => x.PrCode.Contains(request.PrCode))
                .WhereIf(request.PrPostDateStart.HasValue, x => x.PostDate >= request.PrPostDateStart.Value)
                .WhereIf(request.PrPostDateEnd.HasValue, x => x.PostDate < request.PrPostDateEnd.Value.AddDays(1))
                .WhereIf(request.PrApplyUserId.HasValue, x => x.ApplyUserId == request.PrApplyUserId.Value)
                .WhereIf(request.EPDAgentId.HasValue, x => x.AgentId == request.EPDAgentId.Value)
                .WhereIf(request.CompanyId.HasValue, x => x.CompanyId == request.CompanyId.Value)
                .WhereIf(request.PrBu.HasValue, x => x.BuId == request.PrBu.Value)
                .WhereIf(!string.IsNullOrEmpty(request.SbCode), x => x.BudgetCode.Contains(request.SbCode))
                .WhereIf(request.SbOwnerId.HasValue, x => x.SbOwnerId == request.SbOwnerId.Value)
                .WhereIf(request.ExpenseTypeId.HasValue, x => x.ExpenseType == request.ExpenseTypeId.Value)
                .WhereIf(!string.IsNullOrEmpty(request.VendorName), x => x.VendorName.Contains(request.VendorName))
                .WhereIf(request.OrignalActivityDateStart.HasValue, x => x.prdOriginalEstimateDate >= request.OrignalActivityDateStart.Value)
                .WhereIf(request.OrignalActivityDateEnd.HasValue, x => x.prdOriginalEstimateDate >= request.OrignalActivityDateEnd.Value.AddDays(1))
                .WhereIf(!string.IsNullOrEmpty(request.PrContent), x => x.PRContent.Contains(request.PrContent))
                .WhereIf(!string.IsNullOrEmpty(request.PoCode), x => x.POCode.Contains(request.PoCode))
                .WhereIf(!string.IsNullOrEmpty(request.PoFirstApproverId), x => x.POFirstApprover == request.PoFirstApproverId)
                .WhereIf(!string.IsNullOrEmpty(request.PaCode), x => x.PACode.Contains(request.PaCode))
                .WhereIf(request.IsNotSameVendor.HasValue, x => x.IsNotSameVendor == request.IsNotSameVendor.Value);
            queryActive = queryActive.ApplyRoleFilters(rolesIds, CurrentUser.Id.Value, x => x.BuId, x => x.ApplyUserDept, x => x.BudgetId, x => x.ApplyUserId, x => x.TransfereeId, x => x.AgentId);
            //union RPT_WholeProcess
            var queryInactive = queryWpr.Select(x => new
            {
                IsActive = 0,
                x.PrId,
                x.PrCode,
                x.PostDate,
                x.PRInitiator,
                x.Principal,
                x.Company,
                x.CompanyCode,
                x.BU,
                x.BuId,
                x.ApplyUserId,
                x.AgentId,
                x.TransfereeId,
                x.CompanyId,
                x.ApplyUserDept,
                x.SbOwnerId,
                x.BudgetId,
                x.ExpenseType,
                x.prdOriginalEstimateDate,
                x.BudgetCode,
                x.BudgetOwner,
                x.BudgetAmount,
                x.PRAmount,
                x.ExpenseTypeName,
                x.ExpenseNature,
                x.PRStatusText,
                x.PRCurrentProcessor,
                x.TotalProcessDays,
                x.PRItemNo,
                x.PRItemPaymenTypeText,
                x.HCPTier,
                x.HCPHospital,
                x.OriginalVendorName,
                x.VendorName,
                x.VendorType,
                x.PRItemAmount,
                x.COA,
                x.OrignalActivityDate,
                x.PRContent,
                x.RceNo,
                x.IcbAmount,
                x.IsPushedText,
                x.PRApproveDate,
                x.PrFinallyExpenseApprover,
                x.PrFinallyExpenseApprovedDate,
                x.PrFinanceFinallyApprover,
                x.PrFinanceFinallyApprovalDate,
                x.PushDate,
                x.PRItemProcessDays,
                x.PoId,
                x.POCode,
                x.POPostDate,
                x.POInitiator,
                x.POAmountTax,
                x.POAmount,
                x.POTotalBalanceAmount,
                x.POVAT,
                x.POVATNoTax,
                x.POStatus,
                x.POStatusText,
                x.POCurrentProcessor,
                x.POFirstApprover,
                x.PRType,
                x.PRPODays,
                x.POApproveDate,
                x.POProcessDays,
                x.IsRecoil,
                x.CostCenterCode,
                x.CostNatureCode,
                x.ProductId,
                x.ProductCode,
                x.CityId,
                x.GrId,
                x.GRCode,
                x.GRAmount,
                x.GRAmountNoTax,
                x.GRVAT,
                x.HistoricalReceiptAmount,
                x.HistoricalReceiptAmountNoTax,
                x.HistoricalReceiptAmountTax,
                x.GRDate,
                x.GRStatus,
                x.GRStatusText,
                x.GRVendorName,
                x.IsNotSameVendor,
                x.GRVendorCode,
                x.BpcsPmfvmVcrdte,
                x.BpcsPmfvmVctime,
                x.BpcsCreate,
                x.PaId,
                x.PACode,
                x.IsLastPaymentText,
                x.LatestPAText,
                x.DateOfLatestPA,
                x.PAPostDate,
                x.PAPostPOApproveProcessDays,
                x.PAAmount,
                x.PAAmountNoTax,
                x.PAVAT,
                x.PATotalAmountPaid,
                x.PATotalAmountPaidNoTax,
                x.PATotalAmountPaidTax,
                x.VendorScore,
                x.IFOReceivedPADate,
                x.IFOReceivedPAPostProcessDays,
                x.PAReviewDate,
                x.PAStatus,
                x.PurPAApplicationStatusText,
                x.PACurrentProcessor,
                x.PAProcessDays,
                x.BPCSAPNo,
                x.MPDate,
                x.MPPAPostingDays,
                x.EBankingDate,
                x.EBankingStatus,
                x.EBankingComment,
                x.EbankingMPDays,
                x.IsItemEnd,
                x.FinishedText
            })
                .WhereIf(!string.IsNullOrEmpty(request.PrCode), x => x.PrCode.Contains(request.PrCode))
                .WhereIf(request.PrPostDateStart.HasValue, x => x.PostDate >= request.PrPostDateStart.Value)
                .WhereIf(request.PrPostDateEnd.HasValue, x => x.PostDate < request.PrPostDateEnd.Value.AddDays(1))
                .WhereIf(request.PrApplyUserId.HasValue, x => x.ApplyUserId == request.PrApplyUserId.Value)
                .WhereIf(request.EPDAgentId.HasValue, x => x.AgentId == request.EPDAgentId.Value)
                .WhereIf(request.CompanyId.HasValue, x => x.CompanyId == request.CompanyId.Value)
                .WhereIf(request.PrBu.HasValue, x => x.BuId == request.PrBu.Value)
                .WhereIf(!string.IsNullOrEmpty(request.SbCode), x => x.BudgetCode.Contains(request.SbCode))
                .WhereIf(request.SbOwnerId.HasValue, x => x.SbOwnerId == request.SbOwnerId.Value)
                .WhereIf(request.ExpenseTypeId.HasValue, x => x.ExpenseType == request.ExpenseTypeId.Value)
                .WhereIf(!string.IsNullOrEmpty(request.VendorName), x => x.VendorName.Contains(request.VendorName))
                .WhereIf(request.OrignalActivityDateStart.HasValue, x => x.prdOriginalEstimateDate >= request.OrignalActivityDateStart.Value)
                .WhereIf(request.OrignalActivityDateEnd.HasValue, x => x.prdOriginalEstimateDate >= request.OrignalActivityDateEnd.Value.AddDays(1))
                .WhereIf(!string.IsNullOrEmpty(request.PrContent), x => x.PRContent.Contains(request.PrContent))
                .WhereIf(!string.IsNullOrEmpty(request.PoCode), x => x.POCode.Contains(request.PoCode))
                .WhereIf(!string.IsNullOrEmpty(request.PoFirstApproverId), x => x.POFirstApprover == request.PoFirstApproverId)
                .WhereIf(!string.IsNullOrEmpty(request.PaCode), x => x.PACode.Contains(request.PaCode))
                .WhereIf(request.IsNotSameVendor.HasValue, x => x.IsNotSameVendor == request.IsNotSameVendor.Value);
            queryInactive = queryInactive.ApplyRoleFilters(rolesIds, CurrentUser.Id.Value, x => x.BuId, x => x.ApplyUserDept, x => x.BudgetId, x => x.ApplyUserId, x => x.TransfereeId, x => x.AgentId);
            var queryUnion = queryActive.Concat(queryInactive);
            var count = queryUnion.Count();
            var queryData = queryUnion.OrderByDescending(x => x.PostDate).Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToArray();
            //var datas = ObjectMapper.Map<IEnumerable<object>, IEnumerable<UVW_WPR_ActiveResponseDto>>(queryData);
            var datas = queryData
                .Select(x => new UVW_WPR_ActiveResponseDto
                {
                    IsActive = x.IsActive,
                    PrId = x.PrId,
                    PrCode = x.PrCode,
                    PostDate = x.PostDate,
                    PRInitiator = x.PRInitiator,
                    Principal = x.Principal,
                    Company = x.Company,
                    CompanyCode = x.CompanyCode,
                    BU = x.BU,
                    BuId = x.BuId,
                    BudgetCode = x.BudgetCode,
                    BudgetOwner = x.BudgetOwner,
                    BudgetAmount = x.BudgetAmount,
                    PRAmount = x.PRAmount,
                    ExpenseTypeName = x.ExpenseTypeName,
                    ExpenseNature = x.ExpenseNature,
                    PRStatusText = x.PRStatusText,
                    PRCurrentProcessor = x.PRCurrentProcessor,
                    TotalProcessDays = x.TotalProcessDays,
                    PRItemNo = x.PRItemNo,
                    PRItemPaymenTypeText = x.PRItemPaymenTypeText,
                    HCPTier = x.HCPTier,
                    HCPHospital = x.HCPHospital,
                    OriginalVendorName = x.OriginalVendorName,
                    VendorName = x.VendorName,
                    VendorType = x.VendorType,
                    PRItemAmount = x.PRItemAmount,
                    COA = x.COA,
                    OrignalActivityDate = x.OrignalActivityDate,
                    PRContent = x.PRContent,
                    RceNo = x.RceNo,
                    IcbAmount = x.IcbAmount,
                    IsPushedText = x.IsPushedText,
                    PRApproveDate = x.PRApproveDate,
                    PrFinallyExpenseApprover = x.PrFinallyExpenseApprover,
                    PrFinallyExpenseApprovedDate = x.PrFinallyExpenseApprovedDate,
                    PrFinanceFinallyApprover = x.PrFinanceFinallyApprover,
                    PrFinanceFinallyApprovalDate = x.PrFinanceFinallyApprovalDate,
                    PushDate = x.PushDate,
                    PRItemProcessDays = x.PRItemProcessDays,
                    PoId = x.PoId,
                    POCode = x.POCode,
                    POPostDate = x.POPostDate,
                    POInitiator = x.POInitiator,
                    POAmountTax = x.POAmountTax,
                    POAmount = x.POAmount,
                    POTotalBalanceAmount = x.POTotalBalanceAmount,
                    POVAT = x.POVAT,
                    POVATNoTax = x.POVATNoTax,
                    POStatus = x.POStatus,
                    POStatusText = x.POStatusText,
                    POCurrentProcessor = x.POCurrentProcessor,
                    POFirstApprover = x.POFirstApprover,
                    PRType = x.PRType,
                    PRPODays = x.PRPODays,
                    POApproveDate = x.POApproveDate,
                    POProcessDays = x.POProcessDays,
                    IsRecoil = x.IsRecoil,
                    CostCenterCode = x.CostCenterCode,
                    CostNatureCode = x.CostNatureCode,
                    ProductId = x.ProductId,
                    ProductCode = x.ProductCode,
                    CityId = x.CityId,
                    GrId = x.GrId,
                    GRCode = x.GRCode,
                    GRAmount = x.GRAmount,
                    GRAmountNoTax = x.GRAmountNoTax,
                    GRVAT = x.GRVAT,
                    HistoricalReceiptAmount = x.HistoricalReceiptAmount,
                    HistoricalReceiptAmountNoTax = x.HistoricalReceiptAmountNoTax,
                    HistoricalReceiptAmountTax = x.HistoricalReceiptAmountTax,
                    GRDate = x.GRDate,
                    GRStatus = x.GRStatus,
                    GRStatusText = x.GRStatusText,
                    GRVendorName = x.GRVendorName,
                    GRVendorCode = x.GRVendorCode,
                    BpcsPmfvmVcrdte = x.BpcsPmfvmVcrdte,
                    BpcsPmfvmVctime = x.BpcsPmfvmVctime,
                    BpcsCreate = x.BpcsCreate,
                    PaId = x.PaId,
                    PACode = x.PACode,
                    IsLastPaymentText = x.IsLastPaymentText,
                    LatestPAText = x.LatestPAText,
                    DateOfLatestPA = x.DateOfLatestPA,
                    PAPostDate = x.PAPostDate,
                    PAPostPOApproveProcessDays = x.PAPostPOApproveProcessDays,
                    PAAmount = x.PAAmount,
                    PAAmountNoTax = x.PAAmountNoTax,
                    PAVAT = x.PAVAT,
                    PATotalAmountPaid = x.PATotalAmountPaid,
                    PATotalAmountPaidNoTax = x.PATotalAmountPaidNoTax,
                    PATotalAmountPaidTax = x.PATotalAmountPaidTax,
                    VendorScore = x.VendorScore,
                    IFOReceivedPADate = x.IFOReceivedPADate,
                    IFOReceivedPAPostProcessDays = x.IFOReceivedPAPostProcessDays,
                    PAReviewDate = x.PAReviewDate,
                    PAStatus = x.PAStatus,
                    PurPAApplicationStatusText = x.PurPAApplicationStatusText,
                    PACurrentProcessor = "财务初审" == x.PurPAApplicationStatusText ? "" : x.PACurrentProcessor,
                    PAProcessDays = x.PAProcessDays,
                    BPCSAPNo = x.BPCSAPNo,
                    MPDate = x.MPDate,
                    MPPAPostingDays = x.MPPAPostingDays,
                    EBankingDate = x.EBankingDate,
                    EBankingStatus = x.EBankingStatus,
                    EBankingComment = x.EBankingComment,
                    EbankingMPDays = x.EbankingMPDays,
                    IsItemEnd = x.IsItemEnd,
                    FinishedText = x.FinishedText
                }).ToArray();
            var result = new PagedResultDto<UVW_WPR_ActiveResponseDto>(count, datas);
            return result;
        }

        private static void GetParametersAcr(AccrualReportRequestDto request, List<SqlParameter> sqlparams, ref string dynamicParamsStr)
        {
            sqlparams.Add(new SqlParameter("@PrCode", string.IsNullOrWhiteSpace(request.PrCode) ? "" : request.PrCode));
            dynamicParamsStr += ",@PrCode";

            sqlparams.Add(new SqlParameter("@PrPostDateStart", !request.PrPostDateStart.HasValue ? "" : request.PrPostDateStart.Value.ToString("yyyy-MM-dd")));
            dynamicParamsStr += ",@PrPostDateStart";

            sqlparams.Add(new SqlParameter("@PrPostDateEnd", !request.PrPostDateEnd.HasValue ? "" : request.PrPostDateEnd.Value.AddDays(1).ToString("yyyy-MM-dd")));
            dynamicParamsStr += ",@PrPostDateEnd";

            sqlparams.Add(new SqlParameter("@PrApplyUserId", !request.PrApplyUserId.HasValue ? "" : request.PrApplyUserId.ToString()));
            dynamicParamsStr += ",@PrApplyUserId";

            sqlparams.Add(new SqlParameter("@CompanyId", !request.CompanyId.HasValue ? "" : request.CompanyId.ToString()));
            dynamicParamsStr += ",@CompanyId";

            sqlparams.Add(new SqlParameter("@PrBu", !request.PrBu.HasValue ? "" : request.PrBu.ToString()));
            dynamicParamsStr += ",@PrBu";

            sqlparams.Add(new SqlParameter("@PrProductId", !request.ProductId.HasValue ? "" : request.ProductId.ToString()));
            dynamicParamsStr += ",@PrProductId";

            sqlparams.Add(new SqlParameter("@SbCode", string.IsNullOrWhiteSpace(request.SbCode) ? "" : request.SbCode));
            dynamicParamsStr += ",@SbCode";

            sqlparams.Add(new SqlParameter("@SbOwnerId", !request.SbOwnerId.HasValue ? "" : request.SbOwnerId.ToString()));
            dynamicParamsStr += ",@SbOwnerId";

            sqlparams.Add(new SqlParameter("@ExpenseNatureId", !request.ExpenseNatureId.HasValue ? "" : request.ExpenseNatureId.ToString()));
            dynamicParamsStr += ",@ExpenseNatureId";

            sqlparams.Add(new SqlParameter("@Location", !request.Location.HasValue ? "" : request.Location.ToString()));
            dynamicParamsStr += ",@Location";

            sqlparams.Add(new SqlParameter("@CostCenter", !request.CostCenter.HasValue ? "" : request.CostCenter.ToString()));
            dynamicParamsStr += ",@CostCenter";

            sqlparams.Add(new SqlParameter("@ApprovalLevel", !request.ApprovalLevel.HasValue ? DBNull.Value : request.ApprovalLevel.Value));
            dynamicParamsStr += ",@ApprovalLevel";

            sqlparams.Add(new SqlParameter("@VendorName", string.IsNullOrWhiteSpace(request.VendorName) ? "" : request.VendorName));
            dynamicParamsStr += ",@VendorName";

            sqlparams.Add(new SqlParameter("@ExpectStartDate", !request.ExpectStartDate.HasValue ? "" : request.ExpectStartDate.Value.ToString("yyyy-MM-dd")));
            dynamicParamsStr += ",@ExpectStartDate";

            sqlparams.Add(new SqlParameter("@ExpectEndDate", !request.ExpectEndDate.HasValue ? "" : request.ExpectEndDate.Value.AddDays(1).ToString("yyyy-MM-dd")));
            dynamicParamsStr += ",@ExpectEndDate";

            sqlparams.Add(new SqlParameter("@PaCode", string.IsNullOrWhiteSpace(request.PaCode) ? "" : request.PaCode));
            dynamicParamsStr += ",@PaCode";

            sqlparams.Add(new SqlParameter("@IsClosedPR", !request.IsClosedPR.HasValue ? DBNull.Value : request.IsClosedPR.Value));
            dynamicParamsStr += ",@IsClosedPR";

            sqlparams.Add(new SqlParameter("@PayMethod", !request.PayMethod.HasValue ? 0 : (int)request.PayMethod.Value));
            dynamicParamsStr += ",@PayMethod";
        }

        /// <summary>
        /// 拼装全流程报表的查询参数
        /// </summary>
        /// <param name="request"></param>
        /// <param name="sqlparams"></param>
        /// <param name="dynamicParamsStr"></param>
        private static void GetParametersWPR(WholeProcessReportRequestDto request, List<SqlParameter> sqlparams, ref string dynamicParamsStr)
        {
            sqlparams.Add(new SqlParameter("@PrCode", string.IsNullOrWhiteSpace(request.PrCode) ? "" : request.PrCode));
            dynamicParamsStr += ",@PrCode";

            sqlparams.Add(new SqlParameter("@PrPostDateStart", !request.PrPostDateStart.HasValue ? "" : request.PrPostDateStart.Value.ToString("yyyy-MM-dd")));
            dynamicParamsStr += ",@PrPostDateStart";

            sqlparams.Add(new SqlParameter("@PrPostDateEnd", !request.PrPostDateEnd.HasValue ? "" : request.PrPostDateEnd.Value.ToString("yyyy-MM-dd")));
            dynamicParamsStr += ",@PrPostDateEnd";

            sqlparams.Add(new SqlParameter("@PrApplyUserId", !request.PrApplyUserId.HasValue ? "" : request.PrApplyUserId.ToString()));
            dynamicParamsStr += ",@PrApplyUserId";

            sqlparams.Add(new SqlParameter("@EPDAgentId", !request.EPDAgentId.HasValue ? "" : request.EPDAgentId.ToString()));
            dynamicParamsStr += ",@EPDAgentId";

            sqlparams.Add(new SqlParameter("@CompanyId", !request.CompanyId.HasValue ? "" : request.CompanyId.ToString()));
            dynamicParamsStr += ",@CompanyId";

            sqlparams.Add(new SqlParameter("@PrBu", !request.PrBu.HasValue ? "" : request.PrBu.ToString()));
            dynamicParamsStr += ",@PrBu";

            sqlparams.Add(new SqlParameter("@SbCode", string.IsNullOrWhiteSpace(request.SbCode) ? "" : request.SbCode));
            dynamicParamsStr += ",@SbCode";

            sqlparams.Add(new SqlParameter("@SbOwnerId", !request.SbOwnerId.HasValue ? "" : request.SbOwnerId.ToString()));
            dynamicParamsStr += ",@SbOwnerId";

            sqlparams.Add(new SqlParameter("@ExpenseTypeId", !request.ExpenseTypeId.HasValue ? "" : request.ExpenseTypeId.ToString()));
            dynamicParamsStr += ",@ExpenseTypeId";

            sqlparams.Add(new SqlParameter("@VendorName", string.IsNullOrWhiteSpace(request.VendorName) ? "" : request.VendorName));
            dynamicParamsStr += ",@VendorName";

            sqlparams.Add(new SqlParameter("@OrignalActivityDateStart", !request.OrignalActivityDateStart.HasValue ? "" : request.OrignalActivityDateStart.Value.ToString("yyyy-MM-dd")));
            dynamicParamsStr += ",@OrignalActivityDateStart";

            sqlparams.Add(new SqlParameter("@OrignalActivityDateEnd", !request.OrignalActivityDateEnd.HasValue ? "" : request.OrignalActivityDateEnd.Value.ToString("yyyy-MM-dd")));
            dynamicParamsStr += ",@OrignalActivityDateEnd";

            sqlparams.Add(new SqlParameter("@PrContent", string.IsNullOrWhiteSpace(request.PrContent) ? "" : request.PrContent));
            dynamicParamsStr += ",@PrContent";

            sqlparams.Add(new SqlParameter("@PoCode", string.IsNullOrWhiteSpace(request.PoCode) ? "" : request.PoCode));
            dynamicParamsStr += ",@PoCode";

            sqlparams.Add(new SqlParameter("@PoFirstApproverId", string.IsNullOrWhiteSpace(request.PoFirstApproverId) ? "" : request.PoFirstApproverId));
            dynamicParamsStr += ",@PoFirstApproverId";

            sqlparams.Add(new SqlParameter("@PaCode", string.IsNullOrWhiteSpace(request.PaCode) ? "" : request.PaCode));
            dynamicParamsStr += ",@PaCode";

            sqlparams.Add(new SqlParameter("@IsNotSameVendor", !request.IsNotSameVendor.HasValue ? DBNull.Value : request.IsNotSameVendor.Value));
            dynamicParamsStr += ",@IsNotSameVendor";
        }

        private static void GetParametersEPD(EPDReportRequestDto request, List<SqlParameter> sqlparams, ref string dynamicParamsStr)
        {
            sqlparams.Add(new SqlParameter("@PrCode", string.IsNullOrWhiteSpace(request.PrCode) ? "" : request.PrCode));
            dynamicParamsStr += ",@PrCode";

            sqlparams.Add(new SqlParameter("@PrPostDateStart", string.IsNullOrWhiteSpace(request.PrPostDateStart) ? "" : request.PrPostDateStart));
            dynamicParamsStr += ",@PrPostDateStart";

            sqlparams.Add(new SqlParameter("@PrPostDateEnd", string.IsNullOrWhiteSpace(request.PrPostDateEnd) ? "" : request.PrPostDateEnd));
            dynamicParamsStr += ",@PrPostDateEnd";

            sqlparams.Add(new SqlParameter("@OrignalActivityDateStart", string.IsNullOrWhiteSpace(request.OrignalActivityDateStart) ? "" : request.OrignalActivityDateStart));
            dynamicParamsStr += ",@OrignalActivityDateStart";

            sqlparams.Add(new SqlParameter("@OrignalActivityDateEnd", string.IsNullOrWhiteSpace(request.OrignalActivityDateEnd) ? "" : request.OrignalActivityDateEnd));
            dynamicParamsStr += ",@OrignalActivityDateEnd";

            sqlparams.Add(new SqlParameter("@VendorName", string.IsNullOrWhiteSpace(request.VendorName) ? "" : request.VendorName));
            dynamicParamsStr += ",@VendorName";

            sqlparams.Add(new SqlParameter("@IsEsignUsed", !request.IsEsignUsed.HasValue ? DBNull.Value : request.IsEsignUsed.Value));
            dynamicParamsStr += ",@IsEsignUsed";
        }

        private static void GetParameters(object o, List<SqlParameter> sqlparams, string dynamicParamsStr)
        {
            var props = o.GetType().GetProperties();
            string[] excludeFields = [nameof(PagedDto.PageSize), nameof(PagedDto.PageIndex)];
            foreach (var p in props)
            {
                if (excludeFields.Contains(p.Name))
                    continue;

                var value = p.GetValue(o);

                if ((p.PropertyType == typeof(string)))
                {
                    //&& string.IsNullOrEmpty(value as string)
                    sqlparams.Add(new SqlParameter($"@{p.Name}", string.IsNullOrWhiteSpace(value as string) ? "" : value));
                }
                continue;
            }
        }

        /// <summary>
        /// 获取发票报表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<InvoiceReportResponseDto>> GetInvoiceReportAsync(InvoiceReportRequestDto request)
        {
            var queryPaApplication = await LazyServiceProvider.LazyGetService<IPurPAApplicationReadonlyRepository>().GetQueryableAsync();
            var queryPaApplicationInvoice = await LazyServiceProvider.LazyGetService<IPurPAApplicationInvoiceReadonlyRepository>().GetQueryableAsync();
            var queryPrApplication = await LazyServiceProvider.LazyGetService<IPurPRApplicationReadonlyRepository>().GetQueryableAsync();
            var queryPaJoinGlh = await LazyServiceProvider.LazyGetService<IPAJoinBpcsGlhReadonlyRepository>().GetQueryableAsync();
            var queryUser = await LazyServiceProvider.LazyGetService<IIdentityUserReadonlyRepository>().GetQueryableAsync();

            //后补发票时
            var queryPaAndInvoice = queryPaApplication
                .GroupJoin(queryPaApplicationInvoice, a => a.Id, a => a.PurPAApplicationId, (a, b) => new { Pa = a, PaInvoices = b })
                .SelectMany(a => a.PaInvoices.DefaultIfEmpty(), (a, b) => new { a.Pa, PaInvoice = b });

            //非后补发票时，过滤掉没有发票的数据
            if (!request.IsSupplementary)
                queryPaAndInvoice = queryPaApplication.Join(queryPaApplicationInvoice, a => a.Id, a => a.PurPAApplicationId, (a, b) => new { Pa = a, PaInvoice = b });

            var query = queryPaAndInvoice
            .Where(a => a.Pa.IsBackupInvoice == request.IsSupplementary)
            .WhereIf(!string.IsNullOrEmpty(request.PaNo), a => a.Pa.ApplicationCode.Contains(request.PaNo))
            .WhereIf(request.ApplicantId.HasValue, a => a.Pa.ApplyUserId == request.ApplicantId)
            .WhereIf(!string.IsNullOrEmpty(request.VendorName), a => a.Pa.VendorName.Contains(request.VendorName))
            .WhereIf(request.BuId.HasValue, a => a.Pa.ApplyUserBu == request.BuId)
            .WhereIf(!string.IsNullOrEmpty(request.PoNo), a => a.Pa.POApplicationCode.Contains(request.PoNo))
            .WhereIf(request.ReviewStartAt.HasValue && request.ReviewEndAt.HasValue, a => a.Pa.ApprovedDate >= request.ReviewStartAt && a.Pa.ApprovedDate < request.ReviewEndAt.Value.AddDays(1))
            .WhereIf(!string.IsNullOrEmpty(request.InvoiceNo), a => a.PaInvoice.InvoiceCode.Contains(request.InvoiceNo))
            .Join(queryUser, a => a.Pa.ApplyUserId, a => a.Id, (a, b) => new { a.Pa, a.PaInvoice, Applicant = b })
            .Join(queryPrApplication, a => a.Pa.PRId, a => a.Id, (a, b) => new { a.Pa, a.PaInvoice, a.Applicant, Pr = b })
            .WhereIf(!string.IsNullOrEmpty(request.PrNo), a => a.Pr.ApplicationCode.Contains(request.PrNo))
            .GroupJoin(queryPaJoinGlh, a => a.Pa.Id, a => a.Id, (a, b) => new { a.Pa, a.PaInvoice, a.Applicant, a.Pr, PaJoinGlhs = b })
            .SelectMany(a => a.PaJoinGlhs.DefaultIfEmpty(), (a, b) => new { a.Pa, a.PaInvoice, a.Applicant, a.Pr, PaJoinGlh = b })
            .GroupJoin(queryUser, a => a.Pa.ApprovedUserId, a => a.Id, (a, b) => new { a.Pa, a.PaInvoice, a.Applicant, a.Pr, a.PaJoinGlh, ApprovedUsers = b })
            .SelectMany(a => a.ApprovedUsers.DefaultIfEmpty(), (a, b) => new { a.Pa, a.PaInvoice, a.Applicant, a.Pr, a.PaJoinGlh, ApprovedUser = b })
            .Select(a => new InvoiceReportResponseDto
            {
                PaId = a.Pa.Id,
                PaNo = a.Pa.ApplicationCode,
                ApplicantName = a.Pa.ApplyUserName,
                ApplicantEmail = a.Applicant.Email,
                VendorName = a.Pa.VendorName,
                Currency = a.Pa.Currency,
                PaTotalAmount = a.Pa != null ? Math.Round(a.Pa.PayTotalAmount, 2) : null,
                Company = a.Pa.CompanyName,
                BuName = a.Pa.ApplyUserBuName,
                PrId = a.Pa.PRId,
                PrNo = a.Pr.ApplicationCode,
                PoId = a.Pa.POId,
                PoNo = a.Pa.POApplicationCode,
                GrId = a.Pa.GRId,
                GrNo = a.Pa.GRApplicationCode,
                TmpPaStatus = a.Pa.Status,
                ApNo = a.PaJoinGlh.Lhjnen,
                InvoiceDescription = a.Pa.InvoiceDescription,
                TmpDueDate = a.Pa.EstimatedPaymentDate,
                TmpIsSupplementary = a.Pa.IsBackupInvoice,
                Reviewer = a.ApprovedUser.Name,
                TmpReviewAt = a.Pa.ApprovedDate,
                InvoiceType = a.PaInvoice.InvoiceType,
                InvoiceNo = a.PaInvoice.InvoiceCode,
                TmpInvoiceDate = a.PaInvoice.InvoiceDate,
                TaxRate = !string.IsNullOrEmpty(a.PaInvoice.TaxRate) ? $"{(int)(decimal.Parse(a.PaInvoice.TaxRate) * 100)}%" : null,
                TaxAmount = a.PaInvoice != null ? Math.Round(a.PaInvoice.TaxAmount, 2) : null,
                InvoiceAmountRmb = a.PaInvoice != null ? Math.Round(a.PaInvoice.InvoiceTotalAmount, 2) : null
            });

            var count = query.Count();
            var datas = query.OrderByDescending(a => a.PaNo).Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToArray();
            var invoiceTypes = await LazyServiceProvider.LazyGetService<IDataverseService>().GetDictionariesAsync(DictionaryType.InvoiceType);
            var dictInvoiceTypes = invoiceTypes.ToDictionary(a => a.Code, a => a);
            DictionaryDto dictionaryDto;
            foreach (var item in datas)
            {
                if (!string.IsNullOrEmpty(item.InvoiceType))
                    if (dictInvoiceTypes.TryGetValue(item.InvoiceType, out dictionaryDto))
                        item.InvoiceType = dictionaryDto.Name;
            }

            return new PagedResultDto<InvoiceReportResponseDto>(count, datas);
        }
    }
}