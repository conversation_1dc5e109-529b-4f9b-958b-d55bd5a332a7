﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;
using System.Text.Json.Serialization;

namespace Abbott.SpeakerPortal.Contracts.Bpm
{
    public class BpmSettlementMeetingDto
    {
        [JsonPropertyName("serialNumber")]
        public string SerialNumber { get; set; }

        [JsonPropertyName("no")]
        public string No { get; set; }

        [JsonPropertyName("vendorCode")]
        public string VendorCode { get; set; }

        [JsonPropertyName("pdfUrl")]
        public string PdfUrl { get; set; }

        [JsonPropertyName("payAmount")]
        public string PayAmount { get; set; }

        [Json<PERSON>ropertyName("modifyRemark")]
        public string ModifyRemark { get; set; }

        [Json<PERSON>ropertyName("modifyAmountRemark")]
        public string ModifyAmountRemark { get; set; }

        [Json<PERSON>ropertyName("executive")]
        public string Executive { get; set; }

        [<PERSON><PERSON><PERSON>ropertyN<PERSON>("executiveMail")]
        public string ExecutiveMail { get; set; }

        [JsonPropertyName("startDate")]
        public string StartDate { get; set; }

        [JsonPropertyName("actualNumber")]
        public int ActualNumber { get; set; }
    }
}
