CREATE PROCEDURE dbo.sp_STicketApplications_ns
AS 
begin
	/*
 * 问题一：ClientId，ClientType目前没有生产库权限，并且ClientType没有表能查到
 * 问题二：productCbo_Text 产品名称关联，会有一些数据在目前中间库没有，需要排查PP中数据是否人工上传，没有同步生产中间库。
 */
	

select 
a.id
,ApplicationCode
,a.CssCode
,a.ApplicationType
,a.Status
,a.ApplyTime
,ss.spk_NexBPMCode  as ApplyUserId
,a.ApplyUser
,bu.spk_code as ApplyUserBUCode
,sod.spk_NexBPMCode as ApplyUserDeptId
,a.ApplyUserDeptName
,sc.spk_NexBPMCode as CostCenterId
,sc.spk_costcentercode as CostCenterCode
,sc.spk_name as CostCenter
,sod1.spk_NexBPMCode  as ApplyUserBuId
,sod1.spk_Name  as ApplyUserBuName
,bsbt.MasterBudgetId as BudgetId
,bmb.Code as  BudgetCode
,bsbt.id as SubBudgetId
,a.SubBudgetCode
,a.SubBudgetDesc
,sd.spk_NexBPMCode as SubBudgetRegionId
,a.SubBudgetRegion
,bsbt.OwnerId as BudgetOwnerId
,a.BudgetOwner
,bsbt.AttachmentFile as BudgetFile
,a.BudgetUsedAmount
,a.BudgetAvailableAmount
,a.BudgetAmount
,a.TotalAmountRMB
,sc1.spk_NexBPMCode as ExpenseTypeId
,sc1.spk_name as ExpenseTypeName
,sp.spk_NexBPMCode as ProductId
,a.ProductName
,sp.spk_productcode as ProductCode
,scd.spk_NexBPMCode  as CompanyId
,scd.spk_CompanyCode  as CompanyCode
,a.CompanyName
--,spc.id 
,null as ClientId
,a.ClientCode
,a.ClientName
,a.ClientTypeId
--,spc1.id 
,null as ClientType
,a.Attachment
,a.Remark
,a.TransfereeId
,a.TransfereeName
,a.DataSource
,a.CreationTime
,a.CreatorId
,a.LastModificationTime
,a.LastModifierId
,a.IsDeleted
,a.DeleterId
,a.DeletionTime
,f.spk_NexBPMCode as CityId
,a.CityCode
,a.CityName
,a.Content
,a.SettlementPeriodStart
,a.SettlementPeriodEnd
into #STicketApplications
from STicketApplications_tmp a
left join spk_staffmasterdata ss 
on a.ApplyUserId=ss.bpm_id 
left join spk_organizationalmasterData sod 
on a.ApplyUserDeptId=sod.spk_BPMCode 
left join spk_costcentermasterdata sc 
on a.CostCenterId=sc.spk_BPMCode 
left join spk_organizationalmasterData sod1
on a.ApplyUserBuId=sod1.spk_BPMCode 
left join BdSubBudgets bsbt
on a.BudgetId=bsbt.Code 
left join BdMasterBudgets bmb 
on bsbt.MasterBudgetId=bmb.Id 
left join spk_districtmasterdata sd
on a.SubBudgetRegionId=sd.spk_BPMCode 
left join spk_consume sc1
on a.ExpenseTypeId=sc1.spk_BPMCode 
left join (select *,ROW_NUMBER () over(PARTITION by spk_name order by spk_NexBPMCode desc) rn  from spk_productmasterdata
where flg=N'有效') sp
on a.ProductId=sp.spk_BPMCode and sp.rn=1
left join spk_companymasterdata scd
on a.CompanyId=scd.spk_Name 
left join spk_bucodingconfiguration bu
on a.ApplyUserBUCode=bu.spk_BPMCode
left join (select *,ROW_NUMBER () over(PARTITION by spk_citynumber order by spk_Name desc ) rn from spk_citymasterdata) f
on a.CityId=f.spk_Name and a.CityCode=f.spk_citynumber and f.rn=1
--left join Speaker_Portal.dbo.ClientInfo spc
--on a.ClientId=spc.CustomerCode

--drop table #STicketApplications



 IF OBJECT_ID(N'dbo.STicketApplications ', N'U') IS NOT NULL
	BEGIN
		drop table dbo.STicketApplications
		select *
        into dbo.STicketApplications from #STicketApplications
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into dbo.STicketApplications from #STicketApplications
    -- select * from #vendor_tbl
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END


end
