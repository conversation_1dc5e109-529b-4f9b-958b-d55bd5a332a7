﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Text.Json.Serialization;

namespace Abbott.SpeakerPortal.Contracts.Integration.EpdPortal.EpdHcp
{
    /// <summary>
    /// 查询医生 请求DTO
    /// </summary>
    public class DoctorQueryRequest
    {
        /// <summary>
        /// 页码
        /// </summary>
        [JsonPropertyName("pageNum")]
        public int PageNum { get; set; }

        /// <summary>
        /// 页数
        /// </summary>
        [JsonPropertyName("pageSize")]
        public int PageSize { get; set; }

        /// <summary>
        /// 医生姓名（精确匹配）
        /// </summary>
        [JsonPropertyName("hcpName")]
        public string Name { get; set; }

        /// <summary>
        /// 医生所属医院（模糊匹配）
        /// </summary>
        [JsonPropertyName("hospitalName")]
        public string HospitalName { get; set; }

        /// <summary>
        /// EPD医生主键（非必填，精确匹配）
        /// </summary>
        [JsonPropertyName("abbottHcpId")]
        public string EpdHcpCode { get; set; }
    }
}
