﻿using Abbott.SpeakerPortal.Contracts.Common.Attachment;
using Abbott.SpeakerPortal.Enums;
using Senparc.Weixin.MP.AdvancedAPIs.Semantic;

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;

namespace Abbott.SpeakerPortal.Contracts.Budget.Subbudget
{
    public class CreateSubbudgetRequestDto
    {
        /// <summary>
        /// 主预算
        /// </summary>
        [Required]
        public Guid MasterBudgetId { get; set; }
        /// <summary>
        /// BU
        /// </summary>
        [Required]
        public Guid BuId { get; set; }
        /// <summary>
        /// 成本中心
        /// </summary>
        [Required]
        public Guid CostCenterId { get; set; }
        /// <summary>
        /// 大区
        /// </summary>
        [Required]
        public Guid RegionId { get; set; }
        /// <summary>
        /// 负责人Id
        /// </summary>
        [Required]
        public Guid OwnerId { get; set; }
        /// <summary>
        /// 负责人姓名
        /// </summary>
        [Required]
        public string OwnerName { get; set; }
        /// <summary>
        /// 总预算金额
        /// </summary>
        //[Required]
        [JsonIgnore]
        public decimal BudgetAmount
        {
            get
            {
                return JanAmount + FebAmount + MarAmount + AprAmount + MayAmount + JunAmount + JulAmount + AugAmount + SeptAmount + OctAmount + NovAmount + DecAmount;
            }
        }
        /// <summary>
        /// 可用金额
        /// </summary>
        //[Required]
        [JsonIgnore]
        public decimal AvailableAmount
        {
            get
            {
                return this.MonthlyBudgets.Where(s => s.Status).Sum(s => BudgetAmount);
            }
        }
        /// <summary>
        /// 描述
        /// </summary>
        [Required]
        public string Description { get; set; }
        /// <summary>
        /// 是否合规审计审批
        /// </summary>
        public bool IsComplicanceAudits { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 附件
        /// </summary>
        public List<UploadFileResponseDto> AttachmentInformation { get; set; }
        /// <summary>
        /// 一月预算
        /// </summary>
        [Required]
        [Range(0, 99999999999999, ErrorMessage = "预算金额不能小于0")]
        public decimal JanAmount { get; set; }
        /// <summary>
        /// 一月状态
        /// </summary>
        [Required]
        public bool JanStatus { get; set; }
        /// <summary>
        /// 二月预算
        /// </summary>
        [Required]
        [Range(0, 99999999999999, ErrorMessage = "预算金额不能小于0")]
        public decimal FebAmount { get; set; }
        /// <summary>
        /// 二月状态
        /// </summary>
        [Required]
        public bool FebStatus { get; set; }
        /// <summary>
        /// 三月预算
        /// </summary>
        [Required]
        [Range(0, 99999999999999, ErrorMessage = "预算金额不能小于0")]
        public decimal MarAmount { get; set; }
        /// <summary>
        /// 三月状态
        /// </summary>
        [Required]
        public bool MarStatus { get; set; }
        /// <summary>
        /// 四月预算
        /// </summary>
        [Required]
        [Range(0, 99999999999999, ErrorMessage = "预算金额不能小于0")]
        public decimal AprAmount { get; set; }
        /// <summary>
        /// 四月状态
        /// </summary>
        [Required]
        public bool AprStatus { get; set; }
        /// <summary>
        /// 五月预算
        /// </summary>
        [Required]
        [Range(0, 99999999999999, ErrorMessage = "预算金额不能小于0")]
        public decimal MayAmount { get; set; }
        /// <summary>
        /// 五月状态
        /// </summary>
        [Required]
        public bool MayStatus { get; set; }
        /// <summary>
        /// 六月预算
        /// </summary>
        [Required]
        [Range(0, 99999999999999, ErrorMessage = "预算金额不能小于0")]
        public decimal JunAmount { get; set; }
        /// <summary>
        ///六月状态
        /// </summary>
        [Required]
        public bool JunStatus { get; set; }
        /// <summary>
        /// 七月预算
        /// </summary>
        [Required]
        [Range(0, 99999999999999, ErrorMessage = "预算金额不能小于0")]
        public decimal JulAmount { get; set; }
        /// <summary>
        /// 七月状态
        /// </summary>
        [Required]
        public bool JulStatus { get; set; }
        /// <summary>
        /// 八月预算
        /// </summary>
        [Required]
        [Range(0, 99999999999999, ErrorMessage = "预算金额不能小于0")]
        public decimal AugAmount { get; set; }
        /// <summary>
        /// 八月状态
        /// </summary>
        [Required]
        public bool AugStatus { get; set; }
        /// <summary>
        /// 九月预算
        /// </summary>
        [Required]
        [Range(0, 99999999999999, ErrorMessage = "预算金额不能小于0")]
        public decimal SeptAmount { get; set; }
        /// <summary>
        /// 九月状态
        /// </summary>
        [Required]
        public bool SeptStatus { get; set; }
        /// <summary>
        /// 十月预算
        /// </summary>
        [Required]
        [Range(0, 99999999999999, ErrorMessage = "预算金额不能小于0")]
        public decimal OctAmount { get; set; }
        /// <summary>
        /// 十月状态
        /// </summary>
        [Required]
        public bool OctStatus { get; set; }
        /// <summary>
        /// 十一月预算
        /// </summary>
        [Required]
        [Range(0, 99999999999999, ErrorMessage = "预算金额不能小于0")]
        public decimal NovAmount { get; set; }
        /// <summary>
        /// 十一月状态
        /// </summary>
        [Required]
        public bool NovStatus { get; set; }
        /// <summary>
        /// 十二月预算
        /// </summary>
        [Required]
        [Range(0, 99999999999999, ErrorMessage = "预算金额不能小于0")]
        public decimal DecAmount { get; set; }
        /// <summary>
        /// 十二月状态
        /// </summary>
        [Required]
        public bool DecStatus { get; set; }
        [JsonIgnore]
        public ICollection<MonthlyBudgetDto> MonthlyBudgets
        {
            get
            {

                return new List<MonthlyBudgetDto>()
                {
                    new MonthlyBudgetDto{ BudgetAmount=JanAmount,Status=JanStatus,Month=Month.Jan},
                    new MonthlyBudgetDto{ BudgetAmount=FebAmount,Status=FebStatus,Month=Month.Feb},
                    new MonthlyBudgetDto{ BudgetAmount=MarAmount,Status=MarStatus,Month=Month.Mar},
                    new MonthlyBudgetDto{ BudgetAmount=AprAmount,Status=AprStatus,Month=Month.Apr},
                    new MonthlyBudgetDto{ BudgetAmount=MayAmount,Status=MayStatus,Month=Month.May},
                    new MonthlyBudgetDto{ BudgetAmount=JunAmount,Status=JunStatus,Month=Month.Jun},
                    new MonthlyBudgetDto{ BudgetAmount=JulAmount,Status=JulStatus,Month=Month.Jul},
                    new MonthlyBudgetDto{ BudgetAmount=AugAmount,Status=AugStatus,Month=Month.Aug},
                    new MonthlyBudgetDto{ BudgetAmount=SeptAmount,Status=SeptStatus,Month=Month.Sept},
                    new MonthlyBudgetDto{ BudgetAmount=OctAmount,Status=OctStatus,Month=Month.Oct},
                    new MonthlyBudgetDto{ BudgetAmount=NovAmount,Status=NovStatus,Month=Month.Nov},
                    new MonthlyBudgetDto{ BudgetAmount=DecAmount,Status=DecStatus,Month=Month.Dec},
                };

            }
        }
    }
}
