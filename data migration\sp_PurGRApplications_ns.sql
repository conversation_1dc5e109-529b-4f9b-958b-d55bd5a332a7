CREATE PROCEDURE dbo.sp_PurGRApplications_ns
AS 
BEGIN
	/*
 * VendorId
 */
	
drop table  #PurGRApplications
select 
a.Id,
a.ApplicationCode,
a.Status,
UPPER(ss.spk_NexBPMCode) as ApplyUserId,
a.ApplyTime,
UPPER(soc.spk_NexBPMCode) as ApplyUserBu,
a.<PERSON>sign<PERSON>df,
a.DeliveryMode,
a.MeetingModifyRemark,
a.AmountModifyRemark,
UPPER(ss2.spk_NexBPMCode) as ProcurementPersonnelId,
UPPER(avm.id) VendorId,
a.<PERSON>dvancePayment,
UPPER(ss3.spk_NexBPMCode)  as  AdditionalSignerId,
a.<PERSON>,
a.PSAIds,
a.ExtraProperties,
a.ConcurrencyStamp,
a.CreatorId as CreationTime,
UPPER(ss1.spk_NexBPMCode)  as CreatorId,
a.LastModificationTime,
a.LastModifierId,
a.<PERSON>,
a.<PERSON>,
a.<PERSON>etion<PERSON>,
a.ApplyUserBuName,
a.ApplyUser<PERSON>ame,
a.<PERSON>endor<PERSON>,
a.BudgetCode,
UPPER(ppt2.id) as BudgetId,
UPPER(ppt.id) as PoId,
UPPER(ppt1.id) as PrId,
a.ApplyUserBuToDeptName,
a.PoApplicationCode,
a.PrApplicationCode,
a.PaymentExcludingTaxAmount,
o.spk_companycode as CompanyCode,
a.PayMethod,
UPPER(o.spk_NexBPMCode) as CompanyId,
a.CompanyName,
a.VendorCode,
a.ReceivedTime
into #PurGRApplications
from PLATFORM_ABBOTT.dbo.PurGRApplications_tmp a 
left join PLATFORM_ABBOTT.dbo.spk_staffmasterdata ss 
on a.ApplyUserId =ss.bpm_id     --626853
left join (select *,ROW_NUMBER () over(PARTITION by ApplicationCode order by ProcInstId desc) rn from PLATFORM_ABBOTT.dbo.PurPRApplications_tmp ) pt
on a.ApplyUserBu =pt.ApplicationCode and pt.rn=1       --ApplicationCode重复
left join PLATFORM_ABBOTT.dbo.spk_organizationalmasterdata soc 
on pt.ApplyUserBu =soc.spk_BPMCode 
left join PLATFORM_ABBOTT.dbo.spk_companymasterdata o
on a.CompanyId =o.spk_BPMCode
left join PLATFORM_ABBOTT.dbo.spk_staffmasterdata ss1 
on a.CreationTime =ss1.bpm_id 
left join PLATFORM_ABBOTT.dbo.spk_organizationalmasterdata soc2 
on a.ApplyUserBuName =soc2.spk_BPMCode COLLATE SQL_Latin1_General_CP1_CI_AS
left join PLATFORM_ABBOTT.dbo.spk_staffmasterdata ss2 
on a.ProcurementPersonnelId =ss2.bpm_id
left join PLATFORM_ABBOTT.dbo.spk_staffmasterdata ss3 
on a.AdditionalSignerId =ss3.bpm_id COLLATE SQL_Latin1_General_CP1_CI_AS
left join (select *,ROW_NUMBER () over(PARTITION by ApplicationCode order by ApplicationCode) rn from PLATFORM_ABBOTT.dbo.PurPOApplications_tmp ) ppt
on a.PoId =ppt.ApplicationCode and ppt.rn=1
left join (select *,ROW_NUMBER () over(PARTITION by ApplicationCode order by ApplicationCode) rn from PLATFORM_ABBOTT.dbo.PurPRApplications_tmp ) ppt1
on a.PrId =ppt1.ApplicationCode COLLATE SQL_Latin1_General_CP1_CI_AS and ppt1.rn=1
left join (select *,ROW_NUMBER () over(PARTITION by code order by code) rn from PLATFORM_ABBOTT.dbo.BdSubBudgets_tmp  ) ppt2
on a.BudgetId =ppt2.code and ppt2.rn=1
left join 
PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM fvm
on a.vendorid=cast([VNDERX] as nvarchar(100)) and a.vendorname=cast([VEXTNM] as nvarchar(100)) and o.spk_companycode=cast([VMCMPY] as nvarchar(100))
left join PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM avm
on [VMCMPY]=[VCMPNY] AND [VNDERX]=[VENDOR]

 --删除表
    IF OBJECT_ID(N'PLATFORM_ABBOTT.dbo.PurGRApplications ', N'U') IS NOT NULL
	BEGIN
		drop table PLATFORM_ABBOTT.dbo.PurGRApplications
		select *
        into PLATFORM_ABBOTT.dbo.PurGRApplications from #PurGRApplications
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into PLATFORM_ABBOTT.dbo.PurGRApplications from #PurGRApplications
    -- select * from #vendor_tbl
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END

END;


