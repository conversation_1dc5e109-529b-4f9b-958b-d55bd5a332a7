﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text.Json.Serialization;
using Abbott.SpeakerPortal.Contracts.Common.Attachment;
using Abbott.SpeakerPortal.Enums;

namespace Abbott.SpeakerPortal.Contracts.Budget.FocSubbudget
{
    public class CreateFocSubbudgetRequestDto
    {
        /// <summary>
        /// 主预算
        /// </summary>
        [Required]
        public Guid MasterBudgetId { get; set; }
        /// <summary>
        /// BU
        /// </summary>
        [Required]
        public Guid BuId { get; set; }
        /// <summary>
        /// 成本中心
        /// </summary>
        [Required]
        public Guid CostCenterId { get; set; }
        /// <summary>
        /// 大区
        /// </summary>
        [Required]
        public Guid RegionId { get; set; }
        /// <summary>
        /// 负责人Id
        /// </summary>
        [Required]
        public Guid OwnerId { get; set; }
        /// <summary>
        /// 负责人姓名
        /// </summary>
        public string OwnerName { get; set; }

        /// <summary>
        /// 产品ID
        /// </summary>
        [Required]
        public Guid ProductId { get; set; }

        /// <summary>
        /// 产品简称
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 产品品牌信息编码
        /// </summary>
        public string ProductMCode { get; set; }

        /// <summary>
        /// 产品规格
        /// </summary>
        public string ProductUnit { get; set; }

        /// <summary>
        /// 总预算数量
        /// </summary>
        //[Required]
        [JsonIgnore]
        public int BudgetQty
        {
            get
            {
                return JanQty + FebQty + MarQty + AprQty + MayQty + JunQty + JulQty + AugQty + SeptQty + OctQty + NovQty + DecQty;
            }
        }
        /// <summary>
        /// 可用金额
        /// </summary>
        //[Required]
        [JsonIgnore]
        public int AvailableQty
        {
            get
            {
                return this.MonthlyBudgets.Where(s => s.Status).Sum(s => BudgetQty);
            }
        }
        /// <summary>
        /// 描述
        /// </summary>
        [Required]
        public string Description { get; set; }
        /// <summary>
        /// 是否合规审计审批
        /// </summary>
        public bool IsComplicanceAudits { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 附件
        /// </summary>
        public List<UploadFileResponseDto> AttachmentInformation { get; set; }
        /// <summary>
        /// 一月预算
        /// </summary>
        [Required]
        [Range(0, 999999999, ErrorMessage = "预算数量不能小于0")]
        public int JanQty { get; set; }
        /// <summary>
        /// 一月状态
        /// </summary>
        [Required]
        public bool JanStatus { get; set; }
        /// <summary>
        /// 二月预算
        /// </summary>
        [Required]
        [Range(0, 999999999, ErrorMessage = "预算数量不能小于0")]
        public int FebQty { get; set; }
        /// <summary>
        /// 二月状态
        /// </summary>
        [Required]
        public bool FebStatus { get; set; }
        /// <summary>
        /// 三月预算
        /// </summary>
        [Required]
        [Range(0, 999999999, ErrorMessage = "预算数量不能小于0")]
        public int MarQty { get; set; }
        /// <summary>
        /// 三月状态
        /// </summary>
        [Required]
        public bool MarStatus { get; set; }
        /// <summary>
        /// 四月预算
        /// </summary>
        [Required]
        [Range(0, 999999999, ErrorMessage = "预算数量不能小于0")]
        public int AprQty { get; set; }
        /// <summary>
        /// 四月状态
        /// </summary>
        [Required]
        public bool AprStatus { get; set; }
        /// <summary>
        /// 五月预算
        /// </summary>
        [Required]
        [Range(0, 999999999, ErrorMessage = "预算数量不能小于0")]
        public int MayQty { get; set; }
        /// <summary>
        /// 五月状态
        /// </summary>
        [Required]
        public bool MayStatus { get; set; }
        /// <summary>
        /// 六月预算
        /// </summary>
        [Required]
        [Range(0, 999999999, ErrorMessage = "预算数量不能小于0")]
        public int JunQty { get; set; }
        /// <summary>
        ///六月状态
        /// </summary>
        [Required]
        public bool JunStatus { get; set; }
        /// <summary>
        /// 七月预算
        /// </summary>
        [Required]
        [Range(0, 999999999, ErrorMessage = "预算数量不能小于0")]
        public int JulQty { get; set; }
        /// <summary>
        /// 七月状态
        /// </summary>
        [Required]
        public bool JulStatus { get; set; }
        /// <summary>
        /// 八月预算
        /// </summary>
        [Required]
        [Range(0, 999999999, ErrorMessage = "预算数量不能小于0")]
        public int AugQty { get; set; }
        /// <summary>
        /// 八月状态
        /// </summary>
        [Required]
        public bool AugStatus { get; set; }
        /// <summary>
        /// 九月预算
        /// </summary>
        [Required]
        [Range(0, 999999999, ErrorMessage = "预算数量不能小于0")]
        public int SeptQty { get; set; }
        /// <summary>
        /// 九月状态
        /// </summary>
        [Required]
        public bool SeptStatus { get; set; }
        /// <summary>
        /// 十月预算
        /// </summary>
        [Required]
        [Range(0, 999999999, ErrorMessage = "预算数量不能小于0")]
        public int OctQty { get; set; }
        /// <summary>
        /// 十月状态
        /// </summary>
        [Required]
        public bool OctStatus { get; set; }
        /// <summary>
        /// 十一月预算
        /// </summary>
        [Required]
        [Range(0, 999999999, ErrorMessage = "预算数量不能小于0")]
        public int NovQty { get; set; }
        /// <summary>
        /// 十一月状态
        /// </summary>
        [Required]
        public bool NovStatus { get; set; }
        /// <summary>
        /// 十二月预算
        /// </summary>
        [Required]
        [Range(0, 999999999, ErrorMessage = "预算数量不能小于0")]
        public int DecQty { get; set; }
        /// <summary>
        /// 十二月状态
        /// </summary>
        [Required]
        public bool DecStatus { get; set; }
        [JsonIgnore]
        public ICollection<FocMonthlyBudgetDto> MonthlyBudgets
        {
            get
            {

                return new List<FocMonthlyBudgetDto>()
                {
                    new FocMonthlyBudgetDto{ BudgetQty=JanQty,Status=JanStatus,Month=Month.Jan},
                    new FocMonthlyBudgetDto{ BudgetQty=FebQty,Status=FebStatus,Month=Month.Feb},
                    new FocMonthlyBudgetDto{ BudgetQty=MarQty,Status=MarStatus,Month=Month.Mar},
                    new FocMonthlyBudgetDto{ BudgetQty=AprQty,Status=AprStatus,Month=Month.Apr},
                    new FocMonthlyBudgetDto{ BudgetQty=MayQty,Status=MayStatus,Month=Month.May},
                    new FocMonthlyBudgetDto{ BudgetQty=JunQty,Status=JunStatus,Month=Month.Jun},
                    new FocMonthlyBudgetDto{ BudgetQty=JulQty,Status=JulStatus,Month=Month.Jul},
                    new FocMonthlyBudgetDto{ BudgetQty=AugQty,Status=AugStatus,Month=Month.Aug},
                    new FocMonthlyBudgetDto{ BudgetQty=SeptQty,Status=SeptStatus,Month=Month.Sept},
                    new FocMonthlyBudgetDto{ BudgetQty=OctQty,Status=OctStatus,Month=Month.Oct},
                    new FocMonthlyBudgetDto{ BudgetQty=NovQty,Status=NovStatus,Month=Month.Nov},
                    new FocMonthlyBudgetDto{ BudgetQty=DecQty,Status=DecStatus,Month=Month.Dec},
                };

            }
        }
    }
}
