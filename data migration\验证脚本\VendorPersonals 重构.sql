-- TODO 需要 M1-01 主表过滤 1和2的 供应商
--准备工作
select   c.ProcInstId,a.VCMPNY,a.VENDOR into #upid  from PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a 
join PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM b 
on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
join (select *,ROW_NUMBER () over(partition by vendorNumber,company_Value,supplierCNName order by ProcInstId desc) rn from PLATFORM_ABBOTT.dbo.ods_AUTO_BIZ_T_SupplierApplication_Info) c
on cast(VENDOR as nvarchar(255))=cast(vendorNumber as nvarchar(255)) and cast(VCMPNY as nvarchar(255))=cast(company_Value as nvarchar(255)) and cast(VEXTNM as nvarchar(255))=cast(supplierCNName as nvarchar(255)) and c.rn=1
join PLATFORM_ABBOTT.dbo.ods_Form_7a708c9568fb444a884eb5eca658975f d
on c.ProcInstId =d.ProcInstId 
where processstatus=N'终止(系统)' or processstatus=N'已完成';
select   c.ProcInstId,a.VCMPNY,a.VENDOR into #upid1 
from PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a 
join PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM b 
on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
join (select *,ROW_NUMBER () over(partition by supplierCode,supplierCNName order by ProcInstId desc) rn from PLATFORM_ABBOTT.dbo.ods_AUTO_BIZ_T_HcpLevelApplication_info) c
on cast(VENDOR as nvarchar(255))=cast(supplierCode as nvarchar(255)) and cast(VEXTNM as nvarchar(255))=cast(supplierCNName as nvarchar(255)) and c.rn=1 --and cast(VEXTNM as nvarchar(255))=cast(supplierCNName as nvarchar(255))
join PLATFORM_ABBOTT.dbo.ods_Form_663dd63299be45d69dd8f853d0a4b445 d
on c.ProcInstId =d.ProcInstId 
where processstatus=N'终止(系统)' or processstatus=N'已完成';


with  vendInfo as
(
	SELECT 
		case 
			when VMXCRT like '% F' then  cast(N'女' as nvarchar(50))
			when VMXCRT like '% M' then  cast(N'男' as nvarchar(50))	
		end vmSex
		,* 
	from 
	(
		SELECT 
			tt.*
		from 
		(
			SELECT  
					a1.*
				from
				(
					SELECT a.*,b.VEMLAD,b.VLDRM1,b.VLDRM2,b.VLDATE,b.VLTIME,b.VEXTNM,b.VCRDTE,b.VCTIME ,VCUSER, VMBNKA as BankNo,
						CASE WHEN len(cast(vldate as VARCHAR(255)))<=6 and vldate <> 0 
							 then cast(CONCAT('20',vldate) as int) 
							 WHEN vldate=******** AND VLTIME=173000 AND VLUSER='CNSECOFR1' --[LastModificationTime] 在********系统故障，时间错误，为空
							 THEN 0
							 else vldate end vldate1,
						CASE WHEN len(cast(VCTIME as VARCHAR(255)))<=6 and VCTIME <> 0 
							 then cast(CONCAT('0',VCTIME) as nvarchar(10)) 
							 else VCTIME end VCTIME1,
							 case when vldate=******** AND VLTIME=173000 AND VLUSER='CNSECOFR1' --[LastModificationTime] 在********系统故障，时间错误，为空
							 THEN 0 else VLTIME end VLTIME1,
						CASE WHEN len(cast(VCRDTE as VARCHAR(255)))<=6 and VCRDTE <> 0 
						then cast(CONCAT('20',VCRDTE) as int) 
						else VCRDTE end vcrdte1,
						case when upper(vnstat)='A' THEN 1 else 0 end vnstat1,
						COALESCE(a.VNDNAM,b.VEXTNM) as NAME
					from PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a 
					join PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM b 
						on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
					join VENDOR_TMP vt 
					on a.VCMPNY=VT.VCMPNY and a.VENDOR=VT.VENDOR and VendorType in (1,2) 
--				where [VCMPNY] in (91,79,18,20)  AND trim(VTYPE) in ('NHIV','NLIV','NT')  -- ,'NH','NL' 非个人供应商去掉，加速查询 TODO 检查点
				--		 and TRIM(REPLACE( REPLACE(VMXCRT,' F','') ,' M',''))  in ('140402198012160020','110105196201020439')
			) a1 
		) tt --where tt.rn=1 
	) a2
)
select 
	 a2.VMXCRT 	as VMXCRT --辅助字段，用于关联
	,a2.VCMPNY 		as VCMPNY  --辅助字段，用于关联
	,a2.VENDOR 		as VENDOR  --辅助字段，用于关联
	,a2.VTYPE 		as VTYPE     -- 辅助字段,用于后续验证
	,newid() 		as Id
	,vt.id           as [VendorId]   --- TODO  待M1-1 完成取值
	,case 
		when a2.vmSex is not null then  a2.vmSex
		when h.GENDER is not null and h.GENDER <>'N/A'  then  h.GENDER 
		else c.sex
	end as					[Sex]
	,c.idType_Text as		[CardType]  --- TODO 需要继续处理  还需要加工为code 关联字典表
	,TRIM(REPLACE( REPLACE(a2.VMXCRT,' F','') ,' M','')) as		[CardNo]
	,x17.up_Id  as		[CardPic]   --- TODO  后面 继续 处理  在查询出的[XmlContent]中找到AttachmentInfoGrid下所有的[up_Id]，并按照[up_FileName]包含文字"身份证"的条件筛选出所有身份证相关支持文件ID
	,
	case 
		when  e.supplierprovince <>'' and e.supplierprovince is not null then e.supplierprovince
		else  c.supplierprovince 
		end as Province --- TODO  需要 继续处理	
	,
	case 
		when  e.suppliercity <>'' and e.suppliercity is not null then e.suppliercity
		else  c.SupplierCity
		end as [City] --- TODO  需要 继续处理
	,e.IdentityCardResidence as	[Address]
	,a2.VPOST 				 as [PostCode]
	,'{}' 					 as [ExtraProperties]
	,cast(null as nvarchar(100))  as   [ConcurrencyStamp]
	,vt.CreationTime as		[CreationTime]   --- TODO   待M1-1 完成取值
	,vt.CreatorId as		[CreatorId]     --- TODO    待M1-1 完成取值
	,vt.LastModificationTime as		[LastModificationTime]   --- TODO   待M1-1 完成取值
	,vt.LastModifierId  as		[LastModifierId]       --- TODO   待M1-1 完成取值
	,0 as		[IsDeleted]      -- 默认空
	,cast(null as nvarchar(100)) as		[DeleterId]      -- 默认空
	,cast(null as nvarchar(100)) as		[DeletionTime]   -- 默认空
	,case when len(a2.VEXTNM)>len(a2.VNDNAM) then a2.VEXTNM else a2.VNDNAM end as	 [SPName]
	,c.belongInstitution		 as	[AffiliationOrgan]
	,a2.VEMLAD as		[Email]
	into #VendorPersonals_tmp
from vendInfo a2
left join 
(
 	SELECT 
		LICENSE_NO,
		VENDOR,
		VNDNAM,
		TIER,
		VNDMEMO02,GENDER,
		row_number() over(partition by VENDOR,VNDNAM order by COALESCE(UPD_DATETIME,INS_DATETIME) desc) as rns
	from PLATFORM_ABBOTT.dbo.ODS_T_VENDOR_TIER	 where VENDOR is not null and VENDOR <>''
) h
	on  vmSex is null and cast(h.VENDOR as int)=a2.VENDOR   and   (trim(a2.VNDNAM) =trim(h.VNDNAM) or  trim(a2.VEXTNM) = trim(h.VNDNAM))  and h.rns=1
left join
(
	SELECT 
  		cast(c.vendorNumber as int) vendorNumber,c.company_Value,c.supplierCNName,
  		case 
  			when processstatus is not null and c.sex =1 then N'男'  -- 终止(系统) /完成
  			when processstatus is not null and c.sex =2 then N'女' 
  		end sex,
  		idType_Text,
  		NULL AS supplierprovince,  -- TODO 查询省份
  		c.SupplierCity  SupplierCity,  -- 终止(系统) /完成
  		case when processstatus is not null then c.belongInstitution end belongInstitution,  -- 终止(系统) /完成
--  		j.XmlContent.query('(/root/AttachmentInfoGrid/row)')  XmlContent,  --TODO 解析身份证 正反面，在上一层
		row_number() over(partition by c.vendorNumber,c.company_Value order by  c.ProcInstid desc) as rns
	from  PLATFORM_ABBOTT.dbo.ods_AUTO_BIZ_T_SupplierApplication_Info c
	left join
	PLATFORM_ABBOTT.dbo.ods_Form_7a708c9568fb444a884eb5eca658975f r 
		on  c.ProcInstid=r.ProcInstid and c.vendorNumber is not null  and c.vendorNumber<>'' and  (r.processstatus =N'终止(系统)' or r.processstatus ='完成')
	where  c.vendorNumber is not null  and c.vendorNumber<>''
) c 	
	on a2.VENDOR=c.vendorNumber and a2.VCMPNY=c.company_Value  and ( a2.VEXTNM =c.supplierCNName or a2.VNDNAM =c.supplierCNName) and c.rns=1
left join 
(
	select SupplierCode as VENDOR, supplierName as VEXTNM,
		supplierprovince,suppliercity,IdentityCardResidence
	from PLATFORM_ABBOTT.dbo.ods_T_SupplierExtendedInfo
) e 
	on a2.VENDOR =e.VENDOR and (a2.VEXTNM =e.VEXTNM or a2.VNDNAM =e.VEXTNM)
left join Vendor_Tmp vt 
on a2.VENDOR =vt.VENDOR and a2.VCMPNY=vt.VCMPNY 
left join (
SELECT 
		    az.ProcInstId,
		    d.VCMPNY,d.VENDOR,
		    STRING_AGG(up_Id, ', ') WITHIN GROUP (ORDER BY up_Id) AS up_Id
		FROM 
		    (
		    SELECT t1.ProcInstId, 
			    up_Id,
			    File_Name,
			    Emp_ID
			,row_number() over(partition by t1.ProcInstId order by rnr desc)	rn 		
			from (
				SELECT 
			    ProcInstId, 
			    up_Id,
			    File_Name,
			    Emp_ID,
			    case  
				when File_Name LIKE N'%正面%' then 2
				 when    File_Name  like N'%反面%' then 1
				     else 3
					end as rnr
			FROM 
			    ( select ProcInstId,
						up_Id,
						File_Name,
						Emp_ID 
					from xml_17 B
				join PLATFORM_ABBOTT.dbo.ODS_T_METAFILE tm
				on B.up_Id=tm.file_id) a3
			    where File_Name like N'%身份证%'
			) t1
			)az
		join #upid d
		on az.ProcInstId=d.ProcInstId
		where az.rn=1
		GROUP BY  az.ProcInstId,d.VCMPNY,d.VENDOR
		)x17
		on a2.VCMPNY=x17.VCMPNY and a2.VENDOR=x17.VENDOR 
where  VendorType in (1,2)

--落成实体表
 IF OBJECT_ID(N'PLATFORM_ABBOTT_Stg.dbo.VendorPersonals_tmp', N'U') IS NOT NULL
BEGIN
	
	update a 
	set 
        a.VMXCRT                = b.VMXCRT
       ,a.VendorId              = b.VendorId
       ,a.Sex                   = b.Sex
       ,a.CardType              = b.CardType
       ,a.CardNo                = b.CardNo
       ,a.CardPic               = b.CardPic
       ,a.Province              = b.Province
       ,a.City                  = b.City
       ,a.Address               = b.Address
       ,a.PostCode              = b.PostCode
       ,a.ExtraProperties       = b.ExtraProperties
       ,a.ConcurrencyStamp      = b.ConcurrencyStamp
       ,a.CreationTime          = b.CreationTime
       ,a.CreatorId             = b.CreatorId
       ,a.LastModificationTime  = b.LastModificationTime
       ,a.LastModifierId        = b.LastModifierId
       ,a.IsDeleted             = b.IsDeleted
       ,a.DeleterId             = b.DeleterId
       ,a.DeletionTime          = b.DeletionTime
       ,a.SPName                = b.SPName
       ,a.AffiliationOrgan      = b.AffiliationOrgan
       ,a.Email                 = b.Email
     from PLATFORM_ABBOTT.dbo.VendorPersonals_tmp a 
     left join #VendorPersonals_tmp b on a.VCMPNY = b.VCMPNY  and a.VENDOR = b.VENDOR
     
     insert into PLATFORM_ABBOTT.dbo.VendorPersonals_tmp
     select a.VCMPNY
           ,a.VENDOR
           ,a.VMXCRT
           ,a.VTYPE
           ,a.Id
           ,a.VendorId
           ,a.Sex
           ,a.CardType
           ,a.CardNo
           ,a.CardPic
           ,a.Province
           ,a.City
           ,a.Address
           ,a.PostCode
           ,a.ExtraProperties
           ,a.ConcurrencyStamp
           ,a.CreationTime
           ,a.CreatorId
           ,a.LastModificationTime
           ,a.LastModifierId
           ,a.IsDeleted
           ,a.DeleterId
           ,a.DeletionTime
           ,a.SPName
           ,a.AffiliationOrgan
           ,a.Email
     from #VendorPersonals_tmp a
     where not exists (select * from PLATFORM_ABBOTT.dbo.VendorPersonals_tmp where a.VCMPNY = VCMPNY  and a.VENDOR = VENDOR)
	PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))

	END
ELSE
BEGIN
	PRINT(N'开始落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	select  *  into PLATFORM_ABBOTT.dbo.VendorPersonals_tmp from #VendorPersonals_tmp
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END


--drop table VendorPersonals_tmp