﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Abbott.SpeakerPortal.Contracts.Budget
{
    public class AdjustBudgetAmountRequestDto
    {
        public Guid Id { get; set; }
        /// <summary>
        /// 预算负责人
        /// </summary>
        [Required]
        public Guid OwnerId { get; set; }
        /// <summary>
        /// 调整金额
        /// </summary>
        [Required]
        public decimal AdjustAmount {  get; set; }
        /// <summary>
        /// 描述
        /// </summary>
        [Required]
        public string Description { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }

    }
}
