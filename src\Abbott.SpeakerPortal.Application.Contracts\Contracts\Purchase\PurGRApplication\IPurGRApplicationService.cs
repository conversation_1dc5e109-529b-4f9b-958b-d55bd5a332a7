﻿using Abbott.SpeakerPortal.Domain.Shared.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;

namespace Abbott.SpeakerPortal.Contracts.Purchase.PurGRApplication
{
    public interface IPurGRApplicationService
    {
        /// <summary>
        /// 采购管理—收货列表
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<PagedResultDto<PurGRApplicationResponseDto>> GetPurGRApplicationListAsync(GRListSearchRequestDto requestDto);

        /// <summary>
        /// 采购管理—收货列表-导出
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<List<ExportGRApplicationDto>> ExportGRApplicationListAsync(GRListSearchRequestDto requestDto);

        /// <summary>
        /// 查询GR详情
        /// </summary>
        /// <param name="grId"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        Task<GRApplicationDetailsResponseDto> GetPurGRApplicationDetailsAsync(Guid grId);

        /// <summary>
        /// 创建收货单
        /// </summary>
        /// <param name="prDetailIds"></param>
        /// <param name="queryPO"></param>
        /// <returns></returns>
        Task<MessageResult> CreateGRApplication(List<Guid> prDetailIds, PurPOApplicationDto queryPO = null);

        /// <summary>
        /// 收货操作
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<MessageResult> UpdateGRApplicationAsync(PurGRApplicationRequestDto request);

        /// <summary>
        /// 获取收货历史记录
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<PagedResultDto<PurGRApplicationDetailHistoryResponseDto>> GetGrDetailHistoryListAsync(GRHistoryRequestDto request);

        /// <summary>
        /// 审批-我审批的收货申请列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<PagedResultDto<PurGRApplicationResponseDto>> GetGRApprovalListAsync(GRListSearchRequestDto request);

        /// <summary>
        /// 审批-我审批的收货申请列表-导出
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<List<ExportGRApplicationDto>> ExportGRApprovalListAsync(GRListSearchRequestDto request);

        /// <summary>
        ///  收货申请我发起的视角 列表
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<PagedResultDto<PurGRApplicationResponseDto>> GetGRInitiateListAsync(GRListSearchRequestDto requestDto);

        /// <summary>
        /// 收货申请我发起的视角-导出
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<List<ExportGRApplicationDto>> ExportGRInitiateListAsync(GRListSearchRequestDto request);

        /// <summary>
        /// 终止收货
        /// </summary>
        /// <param name="grId"></param>
        /// <param name="reason"></param>
        /// <returns></returns>
        Task<MessageResult> AbrogationReceiveAsync(Guid grId, string reason = "");

        /// <summary>
        /// 多使用预算后的返还(支持GR、PA)
        /// </summary>
        /// <param name="prId"></param>
        /// <param name="sourceId"></param>
        /// <param name="useBudgetTime"></param>
        /// <returns></returns>
        Task ReturnSubbudgetAsync(Guid prId, Guid sourceId, DateTime useBudgetTime);

        /// <summary>
        /// 终止收货(把GR的部分明细行进行终止收货)
        /// </summary>
        /// <param name="paId">来源PA单的Id</param>
        /// <param name="paCode">来源PA单的Code</param>
        /// <param name="grId">要处理的GR</param>
        /// <param name="excludePRDIds">要排除的PRDIds，其他的明细行要终止收货</param>
        /// <param name="subBudgetId">PR关联的子预算Id</param>
        /// <returns></returns>
        Task<MessageResult> AbrogationReceiveWithPartGRAsync(Guid paId, string paCode, Guid grId, IEnumerable<Guid> excludePRDIds, Guid subBudgetId);
    }
}
