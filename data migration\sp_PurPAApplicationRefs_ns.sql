CREATE PROCEDURE dbo.sp_PurPAApplicationRefs_ns
AS 
BEGIN
	select 
a.[PAApplicationCode]
,a.[RefNo]
,a.[Status]
,ppt.id as [Id]
into #PurPAApplicationRefs
from PurPAApplicationRefs_tmp   a    --576615
left join (select *,ROW_NUMBER () over(PARTITION by ApplicationCode order by ProcInstId desc) rn from PLATFORM_ABBOTT_STG.dbo.PurPAApplications_tmp ) ppt
on a.id=ppt.ApplicationCode and ppt.rn = 1         --576628,ApplicationCode重复


IF OBJECT_ID(N'PLATFORM_ABBOTT_STG.dbo.PurPAApplicationRefs', N'U') IS NOT NULL
BEGIN
	drop table PLATFORM_ABBOTT_STG.dbo.PurPAApplicationRefs
	select  *  into PLATFORM_ABBOTT_STG.dbo.PurPAApplicationRefs from #PurPAApplicationRefs
	PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into PLATFORM_ABBOTT_STG.dbo.PurPAApplicationRefs from #PurPAApplicationRefs
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END 

END
