﻿using Abbott.SpeakerPortal.Contracts.Common;
using Hangfire;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.BackgroundWorkers.System
{
    public class SyncLogMonitorWorker : SpeakerPortalBackgroundWorkerBase
    {
        public SyncLogMonitorWorker()
        {
            CronExpression = Cron.Hourly(1);
        }

        /// <summary>
        ///  邮件监控 SyncLog 
        /// </summary>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public override async Task DoWorkAsync(CancellationToken cancellationToken = default)
        {
            var scheduleJobLogService = LazyServiceProvider.LazyGetService<IScheduleJobLogService>();
            await scheduleJobLogService.MonitorSyncLog();
        }
    }
}
