CREATE PROCEDURE dbo.sp_PurGRApplicationDetails_ns
AS 
BEGIN
	select 
a.[Id]
,pgt.Id as [GRApplicationId]
,sp.spk_NexBPMCode as [ProductId]
,sd.spk_code as [InvoiceType]
,a.[OrderQuantity]
,a.[TotalReceivedQuantity]
,a.[DeliveryMethod]
,a.[CurrentReceivingQuantity]
,a.[CurrentSignedQuantity]
,a.[UnitPrice]
,a.[ReceivedAmount]
,a.[SigningDate]
,a.[IsArrive]
,a.[ExtraProperties]
,a.[ConcurrencyStamp]
,a.CreatorId as [CreationTime]
,ss.spk_NexBPMCode as [CreatorId]
,a.[LastModificationTime]
,a.[LastModifierId]
,a.[IsDeleted]
,a.[DeleterId]
,a.[DeletionTime]
,a.[AllocationAmount]
,a.[PRDetailId]
,a.[AllocationRMBAmount]
,a.[PODetailId]
,a.[PurchaseRMBAmount]
,a.[IsAdvancePayment]
,a.[ProductName]
into #PurGRApplicationDetails
from PurGRApplicationDetails_tmp a 
left join PurGRApplications_tmp pgt 
on a.GRApplicationId =pgt.ProcInstId 
left join spk_productmasterdata sp
on sp.spk_BPMCode =a.ProductId 
left join spk_staffmasterdata ss 
on a.CreationTime =ss.bpm_id 
left join spk_dictionary sd
on a.InvoiceType=sd.spk_Name and sd.spk_type=N'发票类型'

select * from PLATFORM_ABBOTT.dbo.spk_dictionary sd 

 IF OBJECT_ID(N'PLATFORM_ABBOTT.dbo.PurGRApplicationDetails ', N'U') IS NOT NULL
	BEGIN
		drop table PLATFORM_ABBOTT.dbo.PurGRApplicationDetails
		select *
        into PLATFORM_ABBOTT.dbo.PurGRApplicationDetails from #PurGRApplicationDetails
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into PLATFORM_ABBOTT.dbo.PurGRApplicationDetails from #PurGRApplicationDetails
    -- select * from #vendor_tbl
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END





END;