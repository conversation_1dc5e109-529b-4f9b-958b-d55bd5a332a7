﻿using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.Approve;
using Abbott.SpeakerPortal.Contracts.Common.Common;
using Abbott.SpeakerPortal.Contracts.Common.PowerApp;
using Abbott.SpeakerPortal.Contracts.Integration.Veeva;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Entities.Integration.Veeva;
using Abbott.SpeakerPortal.Entities.VendorApplicationPersonals;
using Abbott.SpeakerPortal.Entities.VendorApplications;
using Abbott.SpeakerPortal.Entities.VendorPersonals;
using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.Entities.Common.Workflow.CurrentApprovalTask;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Enums.Integration;
using Abbott.SpeakerPortal.Extension;
using Abbott.SpeakerPortal.Utils;
using Abbott.SpeakerPortal.Vendor.Speaker;

using Flurl.Http;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Xrm.Sdk;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;

using Volo.Abp.Data;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;

using VndEntities = Abbott.SpeakerPortal.Entities.Vendors;
using DocumentFormat.OpenXml.Bibliography;
using Abbott.SpeakerPortal.Contracts.Common.Attachment;
using Microsoft.AspNetCore.Http.Metadata;
using System.IO;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.Common.Sequence;
using NewtonsoftJson = Newtonsoft.Json;
using System.ComponentModel;
using Volo.Abp.Validation.StringValues;
using Abbott.SpeakerPortal.Entities.Integration.Dspot;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Entities.VendorFinancials;
using static Abbott.SpeakerPortal.Permissions.SpeakerPortalPermissions;
using Abbott.SpeakerPortal.Contracts.Common.BPMEmail;
using Abbott.SpeakerPortal.BackgroundWorkers.BPMEmail;
using Hangfire;

namespace Abbott.SpeakerPortal.AppServices.Integration.Veeva
{
    public partial class InteVeevaService : SpeakerPortalAppService, IInteVeevaService
    {
        /// <summary>
        /// The service provider
        /// </summary>
        private readonly IServiceProvider _serviceProvider;

        /// <summary>
        /// The logger
        /// </summary>
        private readonly ILogger<InteVeevaService> _logger;

        /// <summary>
        /// The configuration
        /// </summary>
        private readonly IConfiguration _configuration;

        /// <summary>
        /// The dataverseService
        /// </summary>
        private readonly IDataverseService _dataverseService;

        private readonly ICommonService _commonService;

        private readonly string _apiHost;
        private readonly string _apiToken;
        private readonly string _apiEntrance;
        private readonly string _userName;
        private readonly string _password;
        private readonly string _provider;
        private readonly bool _refresh;
        private readonly string _helpdeskEmail;
        private readonly string _veevaTimeoutCCEmail;
        public InteVeevaService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _logger = serviceProvider.GetService<ILogger<InteVeevaService>>();
            _configuration = serviceProvider.GetService<IConfiguration>();
            _dataverseService = serviceProvider.GetService<IDataverseService>();
            _commonService = serviceProvider.GetService<ICommonService>();
            _apiHost = _configuration["Integrations:Veeva:ApiUrlHost"];
            _apiToken = _configuration["Integrations:Veeva:ApiUrlToken"];
            _apiEntrance = _configuration["Integrations:Veeva:ApiUrlEntrance"];
            _userName = _configuration["Integrations:Veeva:UserName"];
            _password = _configuration["Integrations:Veeva:Password"];
            _provider = _configuration["Integrations:Veeva:Provider"];
            _refresh = _configuration.GetValue("Integrations:Veeva:Refresh", false);
            _helpdeskEmail = _configuration["SpeakerEmail:BPMHelpdeskEmail"];
            _veevaTimeoutCCEmail = _configuration["SpeakerEmail:VeevaTimeoutCCEmail"];
        }

        public VeevaTokenRespDto GetToken()
        {
            //token api Url
            var url = $"{_apiHost}{_apiToken}";
            if (string.IsNullOrWhiteSpace(url))
            {
                return default;
            }

            try
            {
                var request = new VeevaTokenReqDto()
                {
                    UserName = _userName,
                    Password = _password,
                    Provider = _provider,
                    Refresh = _refresh,
                };

                //TODO:待记录API日志，记录每个调出API的信息
                var response = url.WithHeaderJson().PostJsonAsync(request).GetAwaiterResult();
                return response.GetJsonAsync<VeevaTokenRespDto>().GetAwaiterResult();
            }
            catch (Exception ex)
            {
                _logger.LogError($"GetToken() Exception: {ex}");
                return default;
            }
        }

        public string PushDcr(Guid vndAppId)
        {
            var (isCallVeeva, vndApp, vendorOldData) = IsCallVeeva(vndAppId);
            if (!isCallVeeva)
            {
                return $"is Not Changed Doctor, vndAppId: {vndAppId}";
            }

            //Entrance api Url
            var url = $"{_apiHost}{_apiEntrance}";
            if (string.IsNullOrWhiteSpace(url))
            {
                //return MessageResult.FailureResult("url is null");
                return $"url is null, vndAppId: {vndAppId}";
            }
            var log = new SetOperationLogRequestDto();
            try
            {
                //先获取Token
                var reqToken = GetToken();
                if (reqToken == null || string.IsNullOrWhiteSpace(reqToken.AccessToken))
                {
                    return $"reqToken is null, vndAppId: {vndAppId}";
                }
                var headers = new Dictionary<string, string>
                {
                    {"Authorization" ,$"Bearer {reqToken.AccessToken}"}
                };

                var request = TidyDcrReqDto(vndApp, vendorOldData);
                log = _commonService.InitOperationLog("Veeva", "向Veeva推送医生进行验证", url + "|" + JsonSerializer.Serialize(request));
                //TODO:待记录API日志，记录每个调出API的信息
                var response = url.WithHeaderJson(headers).PostJsonAsync(request).GetAwaiterResult();
                string responseResult = response.GetStringAsync().GetAwaiterResult();
                _commonService.LogResponse(log, (responseResult));
                //保存Response到表 VendorApplication
                SaveRespDcr(vndApp, responseResult);
                var respObj = response.GetJsonAsync<VeevaDcrRespDto>().GetAwaiterResult();
                //记录dcr log
                DCRLog(request, respObj, vndApp);
                return respObj.ResponseStatus == "0" ? null : respObj.ResponseMessage;
            }
            catch (Exception ex)
            {
                _commonService.LogResponse(log, ex.ToString(), false);
                _logger.LogError($"PushDcr() , vndAppId: {vndAppId}, Exception: {ex}");
                return ex.Message;
            }
        }

        public async Task PushDcrByWfApprovalTask(ScheduleJobLogDto log)
        {
            var _wfApprovalTaskRepository = _serviceProvider.GetService<IWfApprovalTaskRepository>();

            var query = await _wfApprovalTaskRepository.GetQueryableAsync();

            var targetWorkflowType = new List<WorkflowTypeName> { WorkflowTypeName.SpeakerRequest, WorkflowTypeName.SpeakerChange };
            var tasks = query.Where(x => !x.Flag1 && targetWorkflowType.Contains(x.WorkflowTypeOption) && x.StepNo == "400").Take(100).ToArray();
            if (tasks.Length == 0)
                return;

            var response = string.Empty;

            foreach (var task in tasks)
            {
                response = string.Empty;

                if (string.IsNullOrEmpty(task.FormData))
                    response = PushDcr(task.FormId);
                else
                {
                    var formData = JsonSerializer.Deserialize<ApprovalFomrDataDto>(task.FormData);
                    if (formData.isChangedDoctorInfo)
                        response = PushDcr(task.FormId);
                }
                if (string.IsNullOrEmpty(response))//push成功
                    task.Flag1 = true;
            }

            var updatedTasks = tasks.Where(x => x.Flag1);
            await _wfApprovalTaskRepository.UpdateManyAsync(updatedTasks);

            return;
        }

        public string PushDcrByYearlyReview(Guid vndId)
        {
            var log = new SetOperationLogRequestDto();
            try
            {
                var repoApp = LazyServiceProvider.LazyGetService<IVendorRepository>();
                var vnd = repoApp.GetAsync(a => a.Id == vndId).GetAwaiterResult();
                var url = $"{_apiHost}{_apiEntrance}";
                //先获取Token
                var reqToken = GetToken();
                if (reqToken == null || string.IsNullOrWhiteSpace(reqToken.AccessToken))
                {
                    return $"reqToken is null";
                }
                var headers = new Dictionary<string, string>
                {
                    {"Authorization" ,$"Bearer {reqToken.AccessToken}"}
                };

                var request = TidyDcrReqDtoByYearlyReview(vnd);
                log = _commonService.InitOperationLog("Veeva", "向Veeva推送医生进行验证By年度更新", url + "|" + JsonSerializer.Serialize(request));
                //TODO:待记录API日志，记录每个调出API的信息
                var response = url.WithHeaderJson(headers).PostJsonAsync(request).GetAwaiterResult();
                string responseResult = response.GetStringAsync().GetAwaiterResult();
                _commonService.LogResponse(log, (responseResult));
                var respObj = response.GetJsonAsync<VeevaDcrRespDto>().GetAwaiterResult();
                //记录dcr log
                DCRLog(request, respObj, null, vnd);
                return respObj.ResponseStatus == "0" ? null : respObj.ResponseMessage;
            }
            catch (Exception ex)
            {
                _commonService.LogResponse(log, ex.ToString(), false);
                _logger.LogError($"PushDcr() , vndId: {vndId}, Exception: {ex}");
                return ex.Message;
            }
        }

        /// <summary>
        /// 根据推送记录，拉取结果（测试接口）
        /// </summary>
        /// <param name="vndAppId"></param>
        /// <returns></returns>
        public string PullDcrResult(Guid vndAppId)
        {
            if (vndAppId == Guid.Empty)
            {
                return "vndAppId is Empty";
            }
            var log = new SetOperationLogRequestDto();

            try
            {
                var vndApp = GetVndApp(vndAppId);
                if (vndApp == null || string.IsNullOrWhiteSpace(vndApp.PushVeevaResp))
                {
                    return "vndApp is null";
                }

                var pushResp = JsonSerializer.Deserialize<VeevaDcrRespDto>(vndApp.PushVeevaResp);
                if (pushResp == null)
                {
                    return "pushResp is null";
                }

                if (pushResp.ResponseStatus != "0")
                {
                    return "pushResp.ResponseStatus is not '0'";
                }

                //Entrance api Url
                var url = $"{_apiHost}{_apiEntrance}";
                if (string.IsNullOrWhiteSpace(url))
                {
                    //return MessageResult.FailureResult("url is null");
                    return $"url is null, vndAppId: {vndAppId}";
                }
                //先获取Token
                var reqToken = GetToken();
                if (reqToken == null || string.IsNullOrWhiteSpace(reqToken.AccessToken))
                {
                    return $"reqToken is null, vndAppId: {vndAppId}";
                }
                var headers = new Dictionary<string, string>
                {
                    {"Authorization" ,$"Bearer {reqToken.AccessToken}"}
                };

                var request = TidyDcrResultReqDto(pushResp);
                log = _commonService.InitOperationLog("Veeva", "向Veeva获取医生验证结果", url + "|" + JsonSerializer.Serialize(request));
                //TODO:待记录API日志，记录每个调出API的信息
                var respObj = new VeevaDcrResultRespDto();
                try
                {
                    var response = url.WithHeaderJson(headers).PostJsonAsync(request).GetAwaiterResult();
                    respObj = response.GetJsonAsync<VeevaDcrResultRespDto>().GetAwaiterResult();
                }
                catch (Exception ex)
                {
                    _commonService.LogResponse(log, ex.ToString(), false);
                    throw;
                }

                _commonService.LogResponse(log, JsonSerializer.Serialize(respObj));
                //处理Veeva验证结果
                ProcessDcrResult(request, respObj, vndApp);

                return respObj.ResponseStatus == "0" ? null : respObj.ResponseMessage;
            }
            catch (Exception ex)
            {
                _logger.LogError($"PullDcrResult() , vndAppId: {vndAppId}, Exception: {ex}");
                return ex.Message;
            }
        }

        /// <summary>
        /// 根据推送记录，拉取结果
        /// </summary>
        /// <returns></returns>
        public string PullDcrResult()
        {

            var log = new SetOperationLogRequestDto();

            try
            {
                //Entrance api Url
                var url = $"{_apiHost}{_apiEntrance}";
                if (string.IsNullOrWhiteSpace(url))
                {
                    //return MessageResult.FailureResult("url is null");
                    return $"url is null";
                }


                var (request, veevaDcrLog) = TidyDcrResultReqDto();
                if (veevaDcrLog.Count == 0) return "has no dcrid need to pull";

                //获取Token
                var reqToken = GetToken();
                if (reqToken == null || string.IsNullOrWhiteSpace(reqToken.AccessToken))
                {
                    return $"reqToken is null";
                }
                var headers = new Dictionary<string, string>
                {
                    {"Authorization" ,$"Bearer {reqToken.AccessToken}"}
                };
                log = _commonService.InitOperationLog("Veeva", "向Veeva批量获取医生验证结果", url + "|" + JsonSerializer.Serialize(request));
                var respObj = new VeevaDcrResultRespDto();
                try
                {
                    var response = url.WithHeaderJson(headers).PostJsonAsync(request).GetAwaiterResult();
                    respObj = response.GetJsonAsync<VeevaDcrResultRespDto>().GetAwaiterResult();
                }
                catch (Exception ex)
                {
                    _commonService.LogResponse(log, ex.ToString(), false);
                    throw;
                }

                _commonService.LogResponse(log, JsonSerializer.Serialize(respObj));
                //处理Veeva验证结果
                ProcessDcrResult(request, respObj, veevaDcrLog);

                return respObj.ResponseStatus == "0" ? null : respObj.ResponseMessage;
            }
            catch (Exception ex)
            {
                _logger.LogError($"PullDcrResult()  Exception: {ex}");
                return ex.Message;
            }
        }

        public string SaveVeevaResultHistoryOldData()
        {
            _logger.LogInformation($"SaveVeevaResultHistoryOldData() Begin");

            var queryDcrLog = LazyServiceProvider.LazyGetService<IVeevaDCRLogRepository>().GetQueryableAsync().GetAwaiterResult();
            var repoHistory = LazyServiceProvider.LazyGetService<IVeevaResultHistoryRepository>();
            var queryHistory = repoHistory.GetQueryableAsync().GetAwaiterResult();

            //查询表VeevaDCRLogs里未保存到表VeevaResultHistorys的记录，（VeevaDCRLogs.BusinessFormId==VeevaResultHistorys.VndAppId）
            // 使用LINQ查询
            var queryTodoDcrLogs = queryDcrLog.Where(a => !queryHistory.Any(b => b.VndAppId == a.BusinessFormId));
            var todoDcrLogs = queryTodoDcrLogs.ToList();
            if (todoDcrLogs?.Any() != true)
            {
                _logger.LogInformation($"SaveVeevaResultHistoryOldData() Return: todoDcrLogs?.Any() != true");
                return "没有需要处理的VeevaDCRLogs";
            }

            //每2-3条VeevaDCRLogs（hcp_dcr、speaker_dcr、hco_dcr）属于一条VendorApplication，所以按VeevaDCRLogs.BusinessFormId来循环取数并处理
            var busiFormIds = todoDcrLogs.Select(a => a.BusinessFormId).Distinct().ToList();
            //先取出所有DcrLogs相关的VendorApplication，以备后用
            var queryVndApp = LazyServiceProvider.LazyGetService<IVendorApplicationRepository>().GetQueryableAsync().GetAwaiterResult();
            var listTodoVndApp = queryVndApp.Where(va => queryTodoDcrLogs.Select(a => a.BusinessFormId).Distinct().Any(a => a == va.Id))
                .ToList();
            //.Select(a => new { a.Id, a.VendorCode })
            //.ToDictionary(a => a.Id, a => a.VendorCode);
            //循环查到的dicTodoVndApp，理论是数据是todoDcrLogs.BusinessFormId对应的，因为必须要有VndApp才有意义，所以循环listTodoVndApp
            var historyOldData = listTodoVndApp.Select(va =>
            {
                //反序列化VeevaDCRLogs.ResultContent,并取其entity_data，然后取相关字段组装VeevaResultFieldsDto
                var curVndAppDcrLogs = todoDcrLogs.Where(a => a.BusinessFormId == va.Id);
                var curDcrLogHcp = curVndAppDcrLogs.FirstOrDefault(a => a.RequestType == DcrRequestType.hcp_dcr.GetDescription())?.ResultContent;
                var curDcrLogSpeaker = curVndAppDcrLogs.FirstOrDefault(a => a.RequestType == DcrRequestType.speaker_dcr.GetDescription())?.ResultContent;
                if (curDcrLogHcp == null || curDcrLogSpeaker == null)
                {
                    return null;
                }
                var dcrResultHcpEntityData = NewtonsoftJson.JsonConvert.DeserializeObject<VeevaDcrResultRespEntityDataHcpDto>(curDcrLogHcp);
                var dcrResultSpeakerEntityData = NewtonsoftJson.JsonConvert.DeserializeObject<VeevaDcrResultRespEntityDataSpeakerDto>(curDcrLogSpeaker);

                //取相关字段组装VeevaResultFieldsDto
                var fieldsDto = TidyVeevaResultHistoryForOldData(va, dcrResultHcpEntityData, dcrResultSpeakerEntityData);
                if (fieldsDto == null)
                {
                    return null;
                }

                return new VeevaResultHistory
                {
                    VndAppId = va.Id,
                    VendorCode = va.VendorCode,
                    DcrLogRequestId = curVndAppDcrLogs.FirstOrDefault()?.RequestID,
                    DcrResultApproveStatus = dcrResultHcpEntityData.dcr_result,
                    VeevaResultFieldsContent = NewtonsoftJson.JsonConvert.SerializeObject(fieldsDto),
                };
            }).Where(a => a != null);

            if (historyOldData?.Any() != true)
            {
                _logger.LogInformation($"SaveVeevaResultHistoryOldData() Return: historyOldData?.Any() != true");
                return "整理后的要保存的VeevaResultHistory为空";
            }

            repoHistory.InsertManyAsync(historyOldData).GetAwaiterResult();

            _logger.LogInformation($"SaveVeevaResultHistoryOldData() End");
            return null;
        }

        private VeevaResultFieldsDto TidyVeevaResultHistoryForOldData(VendorApplication vndApp
            , VeevaDcrResultRespEntityDataHcpDto dcrResultHcpEntity
            , VeevaDcrResultRespEntityDataSpeakerDto dcrResultSpeakerEntity)
        {
            _logger.LogInformation($"TidyVeevaResultHistoryForOldData() Begin");
            //如果验证失败，直接返回null
            if (string.Equals(dcrResultHcpEntity.dcr_result, DcrResultApproveStatus.Rejected.GetDescription(), StringComparison.OrdinalIgnoreCase))
            {
                _logger.LogInformation($"TidyVeevaResultHistoryForOldData() Return: dcrResultHcpEntity.dcr_result == DcrResultApproveStatus.Rejected");
                return null;
            }
            //先根据返回的SPLevel，如果SPLevel在我方没有，直接返回null
            var spLvlCode = CalcSpLvl(dcrResultSpeakerEntity.verify_speaker_level);
            if (spLvlCode == null)
            {
                _logger.LogInformation($"TidyVeevaResultHistoryForOldData() Return: spLvlCode == null");
                return null;
            }

            var result = new VeevaResultFieldsDto();
            result.VendorId = dcrResultHcpEntity.verify_vid__v;
            //2892【Veeva集成】veeva返回change_request结果的时候如果院内科室返回成空，会把院内科室刷成空（希望无论change还是add，Veeva传空时统一保取原申请单的值）
            if (!string.IsNullOrWhiteSpace(dcrResultSpeakerEntity.verify_department_name))
                result.HosDepartment = dcrResultSpeakerEntity.verify_department_name;
            var standardHosDepId = CalcStandardHosDepId(dcrResultSpeakerEntity.verify_primary_department_class__v);
            result.StandardHosDepId = standardHosDepId.HasValue ? standardHosDepId.Value : vndApp.StandardHosDepId;
            var ptId = CalcPtId(dcrResultSpeakerEntity.verify_professional_title__v);
            result.PTId = ptId.HasValue ? ptId.Value : vndApp.PTId;
            result.PTName = ptId.HasValue ? dcrResultSpeakerEntity.verify_professional_title__v : vndApp.PTName;
            //如果验证失败，前面已经返回null
            //result.VerificationStatus = VerificationStatuses.Valid;
            var academicLvlCode = CalcAcademicLvl(dcrResultSpeakerEntity.verify_academic_title__v);
            result.AcademicLevel = academicLvlCode ?? vndApp.AcademicLevel;
            result.SPLevel = spLvlCode ?? vndApp.SPLevel;
            //回写字段添加 职务和HcpType
            result.RelationType = dcrResultHcpEntity.verify_primary_relation_type__v;
            result.HCPType = dcrResultHcpEntity.verify_hcp_type__v;
            result.CertificateCode = dcrResultSpeakerEntity.license__v?.Any() == true
                ? dcrResultSpeakerEntity.license__v[0].license_number__v
                : vndApp.CertificateCode;
            //2793【Veeva集成】回写字段少了EPDID，需将Veeva返回的verify_speaker_code写入库里epdid字段
            if (!string.IsNullOrWhiteSpace(dcrResultSpeakerEntity.verify_speaker_code) && !string.Equals(dcrResultSpeakerEntity.verify_speaker_code, "None", StringComparison.CurrentCultureIgnoreCase))
                result.EpdId = dcrResultSpeakerEntity.verify_speaker_code;

            //2905[供应商管理][讲者管理]对于现有学协会信息为空的数据，DCR回写时操作失败
            if (!string.IsNullOrWhiteSpace(vndApp.AcademicPosition))
            {
                //回写学协会信息
                var existAcademics = Newtonsoft.Json.JsonConvert.DeserializeObject<IEnumerable<AcademicPositionDto>>(vndApp.AcademicPosition);
                //存在有变化的学协会信息
                if (existAcademics.Any(a => a.IsChanged))
                {
                    var getAssociationGradeTask = _dataverseService.GetDictionariesAsync(DictionaryType.AssociationGrade);
                    var getAssociationJob2Task = _dataverseService.GetDictionariesAsync(DictionaryType.AssociationJob2);
                    var getAssociationJobStatusTask = _dataverseService.GetDictionariesAsync(DictionaryType.AssociationJobStatus);
                    var getAssociationType = _dataverseService.GetDictionariesAsync(DictionaryType.AssociationType);
                    Task.WaitAll(getAssociationGradeTask, getAssociationJob2Task, getAssociationJobStatusTask, getAssociationType);

                    //veeva回来的学协会信息
                    var validAcademics = dcrResultSpeakerEntity.association_periodicals.Select(a =>
                    {
                        //3729[讲者管理][医师信息验证]返回的学协会信息中，Journal Type没有成功回填
                        var associationType = getAssociationType.Result.FirstOrDefault(a1 => a1.Name == a.verify_org_type__v);

                        var data = new AcademicPositionDto
                        {
                            JournalName = a.verify_normative_org_name__v,
                            JournalType = associationType?.Code,
                            JournalTypeName = associationType?.Name,
                            JournalCategoryName = a.verify_org_grade__v,
                            JournalCategory = getAssociationGradeTask.Result.FirstOrDefault(a1 => a1.Name == a.verify_org_grade__v)?.Code,
                            AppointmentName = a.verify_org_relation_type__v,
                            Appointment = getAssociationJob2Task.Result.FirstOrDefault(a1 => a1.Name == a.verify_org_relation_type__v)?.Code,
                            AppointmentStatusName = a.verify_org_relation_status__v,
                            AppointmentStatus = getAssociationJobStatusTask.Result.FirstOrDefault(a1 => a1.Name == a.verify_org_relation_status__v)?.Code
                        };

                        return data;
                    });
                    //将未变更的学协会信息+veeva验证之后的学协会信息，组成新的集合
                    var newAcademics = existAcademics.Where(a => !a.IsChanged).Concat(validAcademics);
                    //回写到学协会信息中
                    result.AcademicPosition = Newtonsoft.Json.JsonConvert.SerializeObject(newAcademics);
                }
            }

            _logger.LogInformation($"TidyVeevaResultHistoryForOldData() End");
            return result;
        }

        private string ProcessDcrResult(VeevaDcrReqDto request, VeevaDcrResultRespDto respObj, List<VeevaDCRLog> veevaDcrLogs)
        {
            if (request == null || respObj == null || respObj.ResponseStatus != "0")
            {
                return "request is null or respObj is null or respObj.ResponseStatus != 0";
            }

            //记录结果日志
            DCRResultLog(respObj, veevaDcrLogs);

            var formIdS = veevaDcrLogs.DistinctBy(a => a.BusinessFormId).Select(a => a.BusinessFormId);
            //取每个VndApp对应的veevaDcrLogs里的RequestId，以便关联到底是哪一次提交的Veeva
            var requestIds = veevaDcrLogs.Select(a => new { a.BusinessFormId, a.RequestID }).Distinct();

            //更新申请表，循环更新
            var repoVnd = LazyServiceProvider.LazyGetService<IVendorRepository>();
            var repoVndApp = LazyServiceProvider.LazyGetService<IVendorApplicationRepository>();
            var vndApps = repoVndApp.GetListAsync(a => formIdS.Contains(a.Id)).GetAwaiterResult();
            var deletePPHcos = new List<KeyValuePair<Guid, Guid?>>();//需要删除的PP端医院ID
            List<VeevaApprovalOperationDto> veevaApprovals = new List<VeevaApprovalOperationDto>();
            List<Guid> errorFormId = new List<Guid>();
            foreach (var vndApp in vndApps)
            {
                try
                {
                    var hcp_dcrid = veevaDcrLogs.Where(a => a.BusinessFormId == vndApp.Id && a.RequestType == DcrRequestType.hcp_dcr.GetDescription()).OrderByDescending(a => a.CreationTime).FirstOrDefault()?.DCRID;
                    var speaker_dcrid = veevaDcrLogs.Where(a => a.BusinessFormId == vndApp.Id && a.RequestType == DcrRequestType.speaker_dcr.GetDescription()).OrderByDescending(a => a.CreationTime).FirstOrDefault()?.DCRID;
                    var hco_dcrid = veevaDcrLogs.Where(a => a.BusinessFormId == vndApp.Id && a.RequestType == DcrRequestType.hco_dcr.GetDescription()).OrderByDescending(a => a.CreationTime).FirstOrDefault()?.DCRID;

                    var dcrResultHcp = respObj.data?.hcp_dcr_result?.Any() != true ? null
                    : respObj.data?.hcp_dcr_result.FirstOrDefault(a => a.status.Equals(DcrResultStatus.Completed.GetDescription(), StringComparison.OrdinalIgnoreCase)
                        && a.dcr_id == hcp_dcrid);
                    var dcrResultHcpEntity = dcrResultHcp?.entity_data;

                    var dcrResultSpeaker = respObj.data?.speaker_dcr_results?.Any() != true ? null
                  : respObj.data?.speaker_dcr_results.FirstOrDefault(a => a.status.Equals(DcrResultStatus.Completed.GetDescription(), StringComparison.OrdinalIgnoreCase)
                      && a.dcr_id == speaker_dcrid);
                    var dcrResultSpeakerEntity = dcrResultSpeaker?.entity_data;

                    var dcrResultHco = respObj.data?.hco_dcr_result?.Any() != true ? null
                        : respObj.data?.hco_dcr_result.FirstOrDefault(a => a.status.Equals(DcrResultStatus.Completed.GetDescription(), StringComparison.OrdinalIgnoreCase)
                            && a.dcr_id == hco_dcrid);
                    var dcrResultHcoEntity = dcrResultHco?.entity_data;

                    //zhx20250227:添加参数RequestId，以保存到表VeevaResultHistory
                    //Speaker信息：（基础信息+讲者信息）
                    var resultHcpSpeaker = ProcessDcrResultHcpSpeaker(vndApp, dcrResultHcpEntity, dcrResultSpeakerEntity, requestIds.FirstOrDefault(a => a.BusinessFormId == vndApp.Id)?.RequestID);

                    //医院信息：PP医院主数据，且医院会被标记为已验证
                    var resultHco = ProcessDcrResultHco(vndApp.HospitalId, dcrResultHcoEntity);

                    if (vndApp.HospitalId.HasValue)
                        deletePPHcos.Add(new KeyValuePair<Guid, Guid?>(vndApp.HospitalId.Value, resultHco.Item2));//记录医院ID

                    if (dcrResultHcpEntity != null && dcrResultSpeakerEntity != null)
                    {
                        //speakerApprove,true:Veeva验证通过，false:Veeve验证失败
                        var speakerApprove = resultHcpSpeaker //resultHcpSpeaker为true时，dcrResultHcp，dcrResultHcpEntity 一定不是null
                            && dcrResultHcp.status.Equals(DcrResultStatus.Completed.GetDescription(), StringComparison.OrdinalIgnoreCase) == true
                            && dcrResultHcpEntity.dcr_result.Equals(DcrResultApproveStatus.Approved.GetDescription(), StringComparison.OrdinalIgnoreCase) == true
                            ? DcrResultApproveStatus.Approved : DcrResultApproveStatus.Rejected;
                        //审批流操作
                        veevaApprovals.Add(new VeevaApprovalOperationDto()
                        {
                            Id = vndApp.Id,
                            ApplyUserId = vndApp.ApplyUserId,
                            SpeakerApprove = speakerApprove,
                            Remark = dcrResultHcpEntity.dcr_coment + " " + dcrResultSpeakerEntity.verify_approve_comment__v,
                        });
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"ProcessDcrResult Error VendorApplicationId({vndApp.Id}):{ex.Message}");
                    continue;
                }
                //var result = resultHcpSpeaker ? null : "Process Speaker Failed. ";
                //result += resultHco ? null : "Process Hco Failed. ";
                //result += resultApproval ? null : "Process Approval Operation Failed. ";
            }
            DeleteHospitalOrUpdateVendorAsync(repoVndApp, repoVnd, deletePPHcos);//更新和删除PP医院
                                                                                 //更新vendor表，年度讲者更新需要通过dcr获取讲者级别

            var vnds = repoVnd.GetListAsync(a => formIdS.Contains(a.Id)).GetAwaiterResult();
            foreach (var vnd in vnds)
            {
                try
                {
                    var hcp_dcrid = veevaDcrLogs.Where(a => a.BusinessFormId == vnd.Id && a.RequestType == DcrRequestType.hcp_dcr.GetDescription()).FirstOrDefault()?.DCRID;
                    var speaker_dcrid = veevaDcrLogs.Where(a => a.BusinessFormId == vnd.Id && a.RequestType == DcrRequestType.speaker_dcr.GetDescription()).FirstOrDefault()?.DCRID;

                    var dcrResultHcp = respObj.data?.hcp_dcr_result?.Any() != true ? null
                    : respObj.data?.hcp_dcr_result.FirstOrDefault(a => a.status.Equals(DcrResultStatus.Completed.GetDescription(), StringComparison.OrdinalIgnoreCase)
                        && a.dcr_id == hcp_dcrid);
                    var dcrResultHcpEntity = dcrResultHcp?.entity_data;

                    var dcrResultSpeaker = respObj.data?.speaker_dcr_results?.Any() != true ? null
                  : respObj.data?.speaker_dcr_results.FirstOrDefault(a => a.status.Equals(DcrResultStatus.Completed.GetDescription(), StringComparison.OrdinalIgnoreCase)
                      && a.dcr_id == speaker_dcrid);
                    var dcrResultSpeakerEntity = dcrResultSpeaker?.entity_data;
                    //只更新讲者级别                
                    var spLvlCode = CalcSpLvl(dcrResultSpeakerEntity.verify_speaker_level);
                    if (spLvlCode != null)
                    {
                        vnd.SPLevel = spLvlCode;
                        repoVnd.UpdateAsync(vnd).GetAwaiter().GetResult();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"ProcessDcrResult Error VendorId({vnd.Id}):{ex.Message}");
                    continue;
                }
            }

            if (veevaApprovals.Any())
            {
                //PP 端审批流操作
                var resultApproval = ApprovalOperationAuto(veevaApprovals);
                var hcpAndSpeakerDcrLogs = veevaDcrLogs.GroupBy(a => a.RequestID).SelectMany(a => a.Where(a1 => a1.RequestType == DcrRequestType.hcp_dcr.GetDescription() || a1.RequestType == DcrRequestType.speaker_dcr.GetDescription())).ToList();
                //整体执行成功时，再检索是否有失败的项
                if (resultApproval.Success)
                {
                    var results = resultApproval.Data as List<UpdateApprovalResponseDto>;
                    foreach (var item in hcpAndSpeakerDcrLogs)
                    {
                        var result = results.FirstOrDefault(a => Guid.Parse(a.BusinessFormId) == item.BusinessFormId);
                        if (result == null || !result.IsSuccess)
                            item.DCRResultResponseMessage += result?.OperationMessage;
                        else
                            item.DCRResultResponseMessage = "更新成功";
                    }
                }
                else//失败则把状态都刷为processing
                    hcpAndSpeakerDcrLogs.ForEach(a =>
                    {
                        a.DCRResultResponseStatus = DcrResultStatus.Processing.GetDescription();
                        a.DCRResultResponseMessage = resultApproval.Message;
                    });
            }

            LazyServiceProvider.LazyGetService<IVeevaDCRLogRepository>().UpdateManyAsync(veevaDcrLogs).GetAwaiterResult();

            return "";
        }

        /// <summary>
        /// 更新和删除PP医院
        /// </summary>
        /// <param name="vendorApplicationRepository"></param>
        /// <param name="vendorRepository"></param>
        /// <param name="hospitalIds"></param>
        private void DeleteHospitalOrUpdateVendorAsync(IVendorApplicationRepository vendorApplicationRepository, IVendorRepository vendorRepository, List<KeyValuePair<Guid, Guid?>> hospitalIds)
        {
            try
            {
                _logger.LogWarning($"DeleteHospitalOrUpdateVendorAsync start: {JsonSerializer.Serialize(hospitalIds)}");
                var dataverse = LazyServiceProvider.LazyGetService<IDataverseService>();
                var hospitalId = hospitalIds.Where(a => a.Value.HasValue).Select(a => a.Key).ToList();//BPM原数据医院ID 需要删除的ID
                if (!hospitalId.Any())
                    return;
                _logger.LogWarning($"DeleteHospitalOrUpdateVendorAsync delete: {JsonSerializer.Serialize(hospitalId)}");
                //删除PP端医院
                //var ppHospitalIds = hospitalIds.Where(a => a.Value.HasValue).Select(a => a.Value.Value).ToList();//已验证数据、需要更新到BPM
                dataverse.DeleteHospitalFromPP(hospitalId);
                var vendorApplications = vendorApplicationRepository.GetListAsync(a => a.HospitalId.HasValue && hospitalId.Contains(a.HospitalId.Value)).GetAwaiterResult();
                foreach (var item in vendorApplications)
                {
                    var newHospitalId = hospitalIds.Where(a => a.Value.HasValue).FirstOrDefault(a => a.Key == item.HospitalId.Value);//Value 为已验证数据、需要更新到BPM
                    item.HospitalId = newHospitalId.Value;
                }
                if (vendorApplications.Count > 0)
                    vendorApplicationRepository.UpdateManyAsync(vendorApplications).GetAwaiterResult();
                var vendors = vendorRepository.GetListAsync(a => a.HospitalId.HasValue && hospitalId.Contains(a.HospitalId.Value)).GetAwaiterResult();
                foreach (var item in vendors)
                {
                    var newHospitalId = hospitalIds.Where(a => a.Value.HasValue).FirstOrDefault(a => a.Key == item.HospitalId.Value).Value;
                    item.HospitalId = newHospitalId.Value;
                }
                if (vendors.Count > 0)
                    vendorRepository.UpdateManyAsync(vendors).GetAwaiterResult();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"DeleteHospitalOrUpdateVendorAsync Error: {ex.Message}");
            }
        }

        private string ProcessDcrResult(VeevaDcrReqDto request, VeevaDcrResultRespDto respObj, VendorApplication vndApp)
        {
            if (request == null || respObj == null || respObj.ResponseStatus != "0")
            {
                return "request is null or respObj is null or respObj.ResponseStatus != 0";
            }

            var dcrResultHcp = respObj.data?.hcp_dcr_result?.Any() != true ? null
                : respObj.data?.hcp_dcr_result.FirstOrDefault(a => a.status.Equals(DcrResultStatus.Completed.GetDescription(), StringComparison.OrdinalIgnoreCase)
                    && request.Data?.hcp_dcr_result_entity.Contains(a.dcr_id));
            var dcrResultHcpEntity = dcrResultHcp?.entity_data;
            var dcrResultSpeaker = respObj.data?.speaker_dcr_results?.Any() != true ? null
                : respObj.data?.speaker_dcr_results.FirstOrDefault(a => a.status.Equals(DcrResultStatus.Completed.GetDescription(), StringComparison.OrdinalIgnoreCase)
                    && request.Data?.speaker_dcr_results_entity.Contains(a.dcr_id));
            var dcrResultSpeakerEntity = dcrResultSpeaker?.entity_data;
            var dcrResultHco = respObj.data?.hco_dcr_result?.Any() != true ? null
                : respObj.data?.hco_dcr_result.FirstOrDefault(a => a.status.Equals(DcrResultStatus.Completed.GetDescription(), StringComparison.OrdinalIgnoreCase)
                    && request.Data?.hco_dcr_result_entity.Contains(a.dcr_id));
            var dcrResultHcoEntity = dcrResultHco?.entity_data;

            //Speaker信息：（基础信息+讲者信息）
            var resultHcpSpeaker = ProcessDcrResultHcpSpeaker(vndApp, dcrResultHcpEntity, dcrResultSpeakerEntity);

            //医院信息：PP医院主数据，且医院会被标记为已验证
            var resultHco = ProcessDcrResultHco(vndApp.HospitalId, dcrResultHcoEntity);

            //speakerApprove,true:Veeva验证通过，false:Veeve验证失败
            var speakerApprove = resultHcpSpeaker //resultHcpSpeaker为true时，dcrResultHcp，dcrResultHcpEntity 一定不是null
                && dcrResultHcp.status.Equals(DcrResultStatus.Completed.GetDescription(), StringComparison.OrdinalIgnoreCase) == true
                && dcrResultHcpEntity.dcr_result.Equals(DcrResultApproveStatus.Approved.GetDescription(), StringComparison.OrdinalIgnoreCase) == true
                ? DcrResultApproveStatus.Approved : DcrResultApproveStatus.Rejected;

            //审批流操作
            List<VeevaApprovalOperationDto> veevaApprovals = new List<VeevaApprovalOperationDto>();
            veevaApprovals.Add(new VeevaApprovalOperationDto()
            {
                Id = vndApp.Id,
                ApplyUserId = vndApp.ApplyUserId,
                SpeakerApprove = speakerApprove,
                Remark = dcrResultHcpEntity.dcr_coment + " " + dcrResultSpeakerEntity.verify_approve_comment__v,
            });
            var resultApproval = ApprovalOperationAuto(veevaApprovals);

            var result = resultHcpSpeaker ? null : "Process Speaker Failed. ";
            result += resultHco.Item1 ? null : "Process Hco Failed. ";
            result += resultApproval.Success ? null : "Process Approval Operation Failed. ";
            return result;
        }

        /// <summary>
        /// PP审批流程操作  VeevaApprovalOperationDto
        /// </summary>
        /// <param name="speakerApprove"></param>
        /// <param name="dcr_coment"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        private MessageResult ApprovalOperationAuto(List<VeevaApprovalOperationDto> veevaApprovals)
        {
            var approveService = LazyServiceProvider.LazyGetService<IApproveService>();
            var userQuery = LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>();

            List<UpdateApprovalDto> updateApprovals = new List<UpdateApprovalDto>();
            foreach (var approval in veevaApprovals)
            {
                var updateApproval = new UpdateApprovalDto();
                updateApproval.BusinessFormId = approval.Id.ToString();
                // 后台线程，不能获取当前用户ID
                var veeva = userQuery.FirstOrDefaultAsync(x => !string.IsNullOrEmpty(x.UserName) && x.UserName.ToLower().Contains("veeva")).Result;
                //updateApproval.Submitter = CurrentUser.Id.Value;//提交人
                updateApproval.Submitter = veeva != null ? veeva.Id : approval.ApplyUserId;//提交人
                updateApproval.Remark = approval.Remark;
                //veeva只要不是成功，就给PP withdraw
                updateApproval.OperationStatus = approval.SpeakerApprove == DcrResultApproveStatus.Approved ? ApprovalOperation.Approved : ApprovalOperation.Withdraw;
                updateApprovals.Add(updateApproval);
            }

            var approvalOperation = approveService.ApprovalOperationAsync(updateApprovals, true, false).Result;
            return approvalOperation;
        }

        private bool ProcessDcrResultHcpSpeaker(VendorApplication vndApp
            , VeevaDcrResultRespEntityDataHcpDto dcrResultHcpEntity
            , VeevaDcrResultRespEntityDataSpeakerDto dcrResultSpeakerEntity
            , Guid? dcrLogRequestId = null)
        {
            if (vndApp?.Id == null || dcrResultHcpEntity == null || dcrResultSpeakerEntity == null)
            {
                return false;
            }

            var repoVndApp = LazyServiceProvider.LazyGetService<IVendorApplicationRepository>();
            //验证失败时，只写vndApp.VerificationStatus
            if (string.Equals(dcrResultHcpEntity.dcr_result, DcrResultApproveStatus.Rejected.GetDescription(), StringComparison.OrdinalIgnoreCase))
            {
                vndApp.VerificationStatus = VerificationStatuses.Invalid;
                repoVndApp.UpdateAsync(vndApp).GetAwaiterResult();
                return true;
            }

            //先根据返回的SPLevel，如果SPLevel在我方没有，只记API日志，其它任何字段不修改，待下次取验证结果
            var spLvlCode = CalcSpLvl(dcrResultSpeakerEntity.verify_speaker_level);
            if (spLvlCode == null)
            {
                //TODO:为SPLevel不对的场景，待记录API日志
                return true;
            }

            var vndAppPer = GetVndAppPer(vndApp.Id);

            //回写 VendorApplication
            vndApp.VendorId = dcrResultHcpEntity.verify_vid__v;
            //2892【Veeva集成】veeva返回change_request结果的时候如果院内科室返回成空，会把院内科室刷成空（希望无论change还是add，Veeva传空时统一保取原申请单的值）
            if (!string.IsNullOrWhiteSpace(dcrResultSpeakerEntity.verify_department_name))
                vndApp.HosDepartment = dcrResultSpeakerEntity.verify_department_name.SpecialCharRemove();//特殊字符移除
            var standardHosDepId = CalcStandardHosDepId(dcrResultSpeakerEntity.verify_primary_department_class__v);
            vndApp.StandardHosDepId = standardHosDepId.HasValue ? standardHosDepId.Value : vndApp.StandardHosDepId;
            var ptId = CalcPtId(dcrResultSpeakerEntity.verify_professional_title__v);
            vndApp.PTId = ptId.HasValue ? ptId.Value : vndApp.PTId;
            vndApp.PTName = ptId.HasValue ? dcrResultSpeakerEntity.verify_professional_title__v : vndApp.PTName;
            vndApp.VerificationStatus = VerificationStatuses.Valid;
            var academicLvlCode = CalcAcademicLvl(dcrResultSpeakerEntity.verify_academic_title__v);
            vndApp.AcademicLevel = academicLvlCode ?? vndApp.AcademicLevel;
            vndApp.SPLevel = spLvlCode ?? vndApp.SPLevel;
            //回写字段添加 职务和HcpType
            vndApp.RelationType = dcrResultHcpEntity.verify_primary_relation_type__v;
            vndApp.HCPType = dcrResultHcpEntity.verify_hcp_type__v;

            vndApp.CertificateCode = dcrResultSpeakerEntity.license__v?.Any() == true
            ? dcrResultSpeakerEntity.license__v[0].license_number__v
            : vndApp.CertificateCode;

            //2793【Veeva集成】回写字段少了EPDID，需将Veeva返回的verify_speaker_code写入库里epdid字段
            if (!string.IsNullOrWhiteSpace(dcrResultSpeakerEntity.verify_speaker_code) && !string.Equals(dcrResultSpeakerEntity.verify_speaker_code, "None", StringComparison.CurrentCultureIgnoreCase))
                vndApp.EpdId = dcrResultSpeakerEntity.verify_speaker_code;

            //回写 VendorApplicationPersonal
            if (vndAppPer != null)
            {
                //2750 【Veeva集成】当Veeva返回的speaker_result中：verify_gender_v和verify_speaker_name_v为空时，则回写speaker数据时时需取申请单原始值

                //if (!string.IsNullOrEmpty(dcrResultSpeakerEntity.verify_speaker_name__v))
                //    vndAppPer.SPName = dcrResultSpeakerEntity.verify_speaker_name__v;//4790 Veeva集成】讲者姓名无需根据Veeva返回的结果进行更新，默认都取NexBPM中存的讲者姓名即可

                if (!string.IsNullOrEmpty(dcrResultSpeakerEntity.verify_gender__v))
                    vndAppPer.Sex = dcrResultSpeakerEntity.verify_gender__v == "男" ? Gender.Male : Gender.Woman;
                //vndAppPer.CardNo = dcrResultSpeakerEntity.license__v?.Any() == true
                //    ? dcrResultSpeakerEntity.license__v[0].license_number__v
                //    : vndAppPer.CardNo;
            }

            IEnumerable<AcademicPositionDto> validAcademics = [];
            //回写学协会信息
            var existAcademics = string.IsNullOrEmpty(vndApp.AcademicPosition) ? [] : Newtonsoft.Json.JsonConvert.DeserializeObject<IEnumerable<AcademicPositionDto>>(vndApp.AcademicPosition);
            //4730【Issue】【供应商管理】【讲者】Veeva验证回来新增了学协会信息，但没有拼接到协会信息中
            if (dcrResultSpeakerEntity.association_periodicals.Any())
            {
                var getAssociationGradeTask = _dataverseService.GetDictionariesAsync(DictionaryType.AssociationGrade);
                var getAssociationJob2Task = _dataverseService.GetDictionariesAsync(DictionaryType.AssociationJob2);
                var getAssociationJobStatusTask = _dataverseService.GetDictionariesAsync(DictionaryType.AssociationJobStatus);
                var getAssociationType = _dataverseService.GetDictionariesAsync(DictionaryType.AssociationType);
                Task.WaitAll(getAssociationGradeTask, getAssociationJob2Task, getAssociationJobStatusTask, getAssociationType);

                //veeva回来的学协会信息
                validAcademics = dcrResultSpeakerEntity.association_periodicals.Select(a =>
                {
                    //3729[讲者管理][医师信息验证]返回的学协会信息中，Journal Type没有成功回填
                    var associationType = getAssociationType.Result.FirstOrDefault(a1 => a1.Name == a.verify_org_type__v);

                    var data = new AcademicPositionDto
                    {
                        Id = Guid.NewGuid().ToString(),
                        JournalName = a.verify_normative_org_name__v,
                        JournalType = associationType?.Code,
                        JournalTypeName = associationType?.Name,
                        JournalCategoryName = a.verify_org_grade__v,
                        JournalCategory = getAssociationGradeTask.Result.FirstOrDefault(a1 => a1.Name == a.verify_org_grade__v)?.Code,
                        AppointmentName = a.verify_org_relation_type__v,
                        Appointment = getAssociationJob2Task.Result.FirstOrDefault(a1 => a1.Name == a.verify_org_relation_type__v)?.Code,
                        AppointmentStatusName = a.verify_org_relation_status__v,
                        AppointmentStatus = getAssociationJobStatusTask.Result.FirstOrDefault(a1 => a1.Name == a.verify_org_relation_status__v)?.Code
                    };

                    return data;
                });
            }
            //将未变更的学协会信息+veeva验证之后的学协会信息，组成新的集合
            var newAcademics = existAcademics.Where(a => !a.IsChanged).Concat(validAcademics);
            //回写到学协会信息中
            vndApp.AcademicPosition = Newtonsoft.Json.JsonConvert.SerializeObject(newAcademics);

            repoVndApp.UpdateAsync(vndApp).GetAwaiterResult();

            //zhx20250226: 保存VeevaResultHistory     vndAppPer?.SPName 用于验证是否变更
            SaveVeevaResultHistory(vndApp, dcrResultHcpEntity, dcrLogRequestId, vndAppPer?.SPName);

            if (vndAppPer != null)
            {
                var repoVndAppPer = LazyServiceProvider.LazyGetService<IVendorApplicationPersonalRepository>();
                repoVndAppPer.UpdateAsync(vndAppPer).GetAwaiterResult();
            }
            return true;
        }

        private void SaveVeevaResultHistory(VendorApplication vndApp, VeevaDcrResultRespEntityDataHcpDto dcrResultHcpEntity, Guid? dcrLogRequestId, string spName)
        {
            _logger.LogInformation($"SaveVeevaResultHistory() Begin VAID:{vndApp?.Id}");
            if (vndApp == null)
            {
                _logger.LogInformation($"SaveVeevaResultHistory() Return by: vndApp == null");
                return;
            }

            try
            {
                var fieldsDto = ObjectMapper.Map<VendorApplication, VeevaResultFieldsDto>(vndApp);
                fieldsDto.SPName = spName;
                var entity = new VeevaResultHistory
                {
                    VndAppId = vndApp.Id,
                    VendorCode = vndApp.VendorCode,
                    DcrLogRequestId = dcrLogRequestId,
                    DcrResultApproveStatus = dcrResultHcpEntity.dcr_result,
                    VeevaResultFieldsContent = NewtonsoftJson.JsonConvert.SerializeObject(fieldsDto),
                };
                var repo = LazyServiceProvider.LazyGetService<IVeevaResultHistoryRepository>();
                var saveResult = repo.InsertAsync(entity).GetAwaiterResult();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"SaveVeevaResultHistory() VAID:{vndApp?.Id} Error: {ex.Message}");
            }
            _logger.LogInformation($"SaveVeevaResultHistory() VAID:{vndApp?.Id} End");
        }

        private string CalcSpLvl(string verify_speaker_level)
        {
            if (string.IsNullOrWhiteSpace(verify_speaker_level))
            {
                return null;
            }
            var dicHcpLevels = GetHcpLevels();
            var item = dicHcpLevels.Where(a => a.Value.Name.Equals(verify_speaker_level, StringComparison.OrdinalIgnoreCase))
                .Select(a => a.Value).FirstOrDefault();
            return item?.Code;
        }

        /// <summary>
        /// 找PP，找到就存其Code，未找到先返回Null。未找到时是否要怎么处理，待确认。
        /// </summary>
        private string CalcAcademicLvl(string verify_academic_title__v)
        {
            if (string.IsNullOrWhiteSpace(verify_academic_title__v))
            {
                return null;
            }
            var dicAcademicLevel = GetAcademicLevels();
            var item = dicAcademicLevel.Where(a => a.Value.Name.Equals(verify_academic_title__v, StringComparison.OrdinalIgnoreCase))
                .Select(a => a.Value).FirstOrDefault();
            return item?.Code;
        }

        /// <summary>
        /// 看回来的标准科室名称，在PP有否，有则查出其Id，无则先新增再查出其Id，返回此Id
        /// </summary>
        private Guid? CalcStandardHosDepId(string verify_primary_department_class__v)
        {
            if (string.IsNullOrWhiteSpace(verify_primary_department_class__v))
            {
                return null;
            }
            var standartDept = GetStandarDepartments();
            var existEtt = standartDept?.Values.FirstOrDefault(a => a.Name.Equals(verify_primary_department_class__v, StringComparison.OrdinalIgnoreCase));
            if (existEtt != null)
            {
                return existEtt.Id;
            }

            //新增后再返回Id
            existEtt = new OfficeDto { Name = verify_primary_department_class__v };
            return _dataverseService.AddDeptAsync(existEtt).GetAwaiterResult();
        }

        /// <summary>
        /// 逻辑类似：CalcStandardHosDepId()
        /// </summary>
        private Guid? CalcPtId(string verify_professional_title__v)
        {
            if (string.IsNullOrWhiteSpace(verify_professional_title__v))
            {
                return null;
            }
            var jobTitle = GetPTs();
            var existEtt = jobTitle?.Values.FirstOrDefault(a => a.Name.Equals(verify_professional_title__v, StringComparison.OrdinalIgnoreCase));
            if (existEtt != null)
            {
                return existEtt.Id;
            }

            //新增后再返回Id
            existEtt = new JobTitleDto { Name = verify_professional_title__v };
            return _dataverseService.AddJobTitleAsync(existEtt).GetAwaiterResult();
        }

        private (bool, Guid?) ProcessDcrResultHco(Guid? id, VeevaDcrResultRespEntityDataHcoDto dcrResultHcoEntity)
        {
            if (!id.HasValue || dcrResultHcoEntity == null
                || !string.Equals(dcrResultHcoEntity.dcr_result, DcrResultApproveStatus.Approved.GetDescription(), StringComparison.OrdinalIgnoreCase))
            {
                return (false, null);
            }

            var dataverse = LazyServiceProvider.LazyGetService<IDataverseService>();

            var enums = EnumUtil.GetEnumIdValues<DataverseEnums.SpkHospitalMasterData.HospitalStatus>();

            var dicFields = new Dictionary<string, object>();
            dicFields.Add("spk_name", dcrResultHcoEntity.verified_corporate_name__v);
            dicFields.Add("spk_hospitalcode", dcrResultHcoEntity.verify_hco_code);

            //状态
            var status = enums.FirstOrDefault(a => a.Value == dcrResultHcoEntity.verified_hco_status__v);
            if (status != null && !string.IsNullOrWhiteSpace(dcrResultHcoEntity.verify_hco_code))
            {
                var hospitals = dataverse.GetAllHospitals(dcrResultHcoEntity.verify_hco_code).GetAwaiterResult();//模糊搜索HospitalCode 数据
                var pphospital = hospitals.FirstOrDefault(a => a.HospitalCode == dcrResultHcoEntity.verify_hco_code);//精确获取医院
                if (pphospital != null && pphospital.Id != id)
                { //通过医院Code查询到的医院跟vendor申请医院ID 不相同，说明该医院已经被验证过了
                    AddFieldsHcoDcr(dataverse, dicFields, dcrResultHcoEntity);
                    _dataverseService.UpdateHospitalAsync(pphospital.Id, dicFields).GetAwaiterResult();
                    return (false, pphospital.Id);
                }
            }
            if (status != null)
                dicFields.Add("spk_hospitalstatus", new OptionSetValue(status.Key));//已验证
            else
                dicFields.Add("spk_hospitalstatus", new OptionSetValue((int)DataverseEnums.SpkHospitalMasterData.HospitalStatus.Undetermined));//已验证
            AddFieldsHcoDcr(dataverse, dicFields, dcrResultHcoEntity);
            return (_dataverseService.UpdateHospitalAsync(id.Value, dicFields).GetAwaiterResult(), null);
        }

        /// <summary>
        ///  AddFields
        /// </summary>
        /// <param name="dataverse"></param>
        /// <param name="dicFields"></param>
        /// <param name="dcrResultHcoEntity"></param>
        private void AddFieldsHcoDcr(IDataverseService dataverse, Dictionary<string, object> dicFields, VeevaDcrResultRespEntityDataHcoDto dcrResultHcoEntity)
        {
            var getProvincesTask = dataverse.GetAllProvince();
            var getCitiesTask = dataverse.GetAllCity();
            var getHcoLevelTask = dataverse.GetDictionariesAsync(DictionaryType.HcoLevel);
            Task.WaitAll(getProvincesTask, getCitiesTask, getHcoLevelTask);


            dicFields.Add("spk_chinesevalue", dcrResultHcoEntity.verified_corporate_name__v);
            dicFields.Add("spk_englishvalue", dcrResultHcoEntity.verified_corporate_name__v);

            #region 2024-10-21新增字段，2353【Veeva对接】从EPD获取的hco_dcr_result中数据丢失---优先处理

            dicFields.Add("spk_hcoveevaid", dcrResultHcoEntity.verify_vid__v);

            //省份
            var hospital = getProvincesTask.Result.FirstOrDefault(a => a.Name == dcrResultHcoEntity.verified_administrative_area__v);
            if (hospital != null)
                dicFields.Add("spk_hcoprovince", new EntityReference(DataverseEntitiesConsts.Province, hospital.Id));
            else
                dicFields.Add("spk_hcoprovince", null);

            //城市
            var city = getCitiesTask.Result.FirstOrDefault(a => a.Name == dcrResultHcoEntity.verified_locality__v);
            if (city != null)
                dicFields.Add("spk_hcocity", new EntityReference(DataverseEntitiesConsts.City, city.Id));
            else
                dicFields.Add("spk_hcocity", null);

            //等级
            var hcoLevel = getHcoLevelTask.Result.FirstOrDefault(a => a.Name == dcrResultHcoEntity.verified_hco_grade__v);
            if (hcoLevel != null)
                dicFields.Add("spk_hcolevel", new EntityReference(DataverseEntitiesConsts.Dictionary, hcoLevel.Id));
            else
                dicFields.Add("spk_hcolevel", null);

            dicFields.Add("spk_hcotype", dcrResultHcoEntity.verified_hco_property__v);
            dicFields.Add("spk_hcodetailtype", dcrResultHcoEntity.verified_hco_type__v);
            dicFields.Add("spk_hcophone", dcrResultHcoEntity.verified_phone_1__v);
            dicFields.Add("spk_uscicode", dcrResultHcoEntity.verified_organization_code__v);

            //纬度
            if (decimal.TryParse(dcrResultHcoEntity.verified_cn_latitude__v, out decimal latitude))
                dicFields.Add("spk_latitude", latitude);
            else
                dicFields.Add("spk_latitude", null);

            //经度
            if (decimal.TryParse(dcrResultHcoEntity.verified_cn_longitude__v, out decimal longitude))
                dicFields.Add("spk_longitude", longitude);
            else
                dicFields.Add("spk_longitude", null);

            //dicFields.Add("spk_districtlevel", dcrResultHcoEntity.);//区县类别
            dicFields.Add("spk_district", dcrResultHcoEntity.verified_sub_administrative_area__v);
            dicFields.Add("spk_postalcode", dcrResultHcoEntity.verified_post_code__v);
            dicFields.Add("spk_subscribeexponent", dcrResultHcoEntity.verified_subscribe_exponent__v);
            dicFields.Add("spk_address", dcrResultHcoEntity.verified_address_line__v);
            dicFields.Add("spk_alternatename", dcrResultHcoEntity.verified_alternate_name_1__v);

            #endregion
        }

        private VeevaDcrReqDto TidyDcrResultReqDto(VeevaDcrRespDto pushResp)
        {
            var hcpDcrId = pushResp?.Data?.hcp_dcr?.hcp_dcr_response_status == "0"
                    ? pushResp?.Data?.hcp_dcr?.dcr_id : null;
            var speakerDcrId = pushResp?.Data?.speaker_dcr?.speaker_dcr_response_status == "0"
                ? pushResp?.Data?.speaker_dcr?.dcr_id : null;
            var hcoDcrId = pushResp?.Data?.hco_dcr?.hco_dcr_response_status == "0"
                ? pushResp?.Data?.hco_dcr?.dcr_id : null;

            var requestType = new List<string> { };
            requestType.AddIf(!string.IsNullOrWhiteSpace(hcpDcrId), "hcp_dcr_result");
            requestType.AddIf(!string.IsNullOrWhiteSpace(speakerDcrId), "speaker_dcr_results");
            requestType.AddIf(!string.IsNullOrWhiteSpace(hcoDcrId), "hco_dcr_result");

            var hcpIds = !string.IsNullOrWhiteSpace(hcpDcrId)
                ? new List<string> { hcpDcrId } : null;
            var speakerIds = !string.IsNullOrWhiteSpace(speakerDcrId)
                ? new List<string> { speakerDcrId } : null;
            var hcoIds = !string.IsNullOrWhiteSpace(hcoDcrId)
                ? new List<string> { hcoDcrId } : null;

            return new VeevaDcrReqDto()
            {
                RequestId = Guid.NewGuid().ToString(),
                RequestType = requestType,
                Data = new
                {
                    hcp_dcr_result_entity = hcpIds,
                    speaker_dcr_results_entity = speakerIds,
                    hco_dcr_result_entity = hcoIds,
                }
            };
        }


        private (VeevaDcrReqDto, List<VeevaDCRLog>) TidyDcrResultReqDto(FlagType flag = FlagType.NexBpm)
        {
            var repoVeevaDCRLog = LazyServiceProvider.LazyGetService<IVeevaDCRLogRepository>();
            var pullList = repoVeevaDCRLog.GetListAsync(a => !string.IsNullOrEmpty(a.DCRID)
            && a.DCRResponseStatus == "0" && a.DCRResultResponseStatus != DcrResultStatus.Completed.GetDescription() && a.FlagType == flag).GetAwaiterResult();
            //组装请求数据
            var dcrRequestType = pullList.DistinctBy(a => a.RequestType).Select(a => a.RequestType);
            var dcrRequestResultType = new List<string>();
            foreach (var item in dcrRequestType)
            {
                if (item == DcrRequestType.speaker_dcr.GetDescription())
                {
                    dcrRequestResultType.Add("speaker_dcr_results");
                }
                else
                {
                    dcrRequestResultType.Add($"{item}_result");
                }
            }
            var hcpIds = new List<string>();
            var speakerIds = new List<string>();
            var hcoIds = new List<string>();

            foreach (var item in pullList)
            {
                if (item.RequestType == DcrRequestType.speaker_dcr.GetDescription())
                {
                    speakerIds.Add(item.DCRID);
                }
                else if (item.RequestType == DcrRequestType.hcp_dcr.GetDescription())
                {
                    hcpIds.Add(item.DCRID);
                }
                else
                {
                    hcoIds.Add(item.DCRID);
                }
            }

            var reqDto = new VeevaDcrReqDto()
            {
                RequestId = Guid.NewGuid().ToString(),
                RequestType = dcrRequestResultType,
                Data = new
                {
                    hcp_dcr_result_entity = hcpIds.Count > 0 ? hcpIds : null,
                    speaker_dcr_results_entity = speakerIds.Count > 0 ? speakerIds : null,
                    hco_dcr_result_entity = hcoIds.Count > 0 ? hcoIds : null,
                }
            };
            return (reqDto, pullList);
        }

        private void SaveRespDcr(VendorApplication vndApp, string respString)
        {
            if (vndApp == null || string.IsNullOrWhiteSpace(respString))
            {
                return;
            }

            vndApp.PushVeevaResp = respString;

            var repoVndApp = LazyServiceProvider.LazyGetService<IVendorApplicationRepository>();
            repoVndApp.UpdateAsync(vndApp).GetAwaiterResult();
        }

        private (bool, VendorApplication, SpeakerDetailResponseDto) IsCallVeeva(Guid vndAppId)
        {
            VendorApplication vndApp = GetVndApp(vndAppId);
            var vendorOldData = string.IsNullOrWhiteSpace(vndApp?.UpdatePreJson) ? null : JsonSerializer.Deserialize<SpeakerDetailResponseDto>(vndApp?.UpdatePreJson);
            if (vndApp == null)
            {
                return (false, null, null);
            }

            //新增、激活都要走Veeva验证
            if (vndApp.ApplicationType == Enums.ApplicationTypes.Create
                || vndApp.ApplicationType == Enums.ApplicationTypes.Active)
            {
                return (true, vndApp, vendorOldData);
            }
            else if (vndApp.ApplicationType == Enums.ApplicationTypes.Update)
            {
                //修改时，医师信息是否有修改，有才需要Call Veeva
                if (vendorOldData == null ||
                    vendorOldData.HospitalId != vndApp.HospitalId || vendorOldData.StandardHosDepId != vndApp.StandardHosDepId ||
                    vendorOldData.PTId != vndApp.PTId || vendorOldData.AcademicLevel != vndApp.AcademicLevel ||
                    (vendorOldData.AcademicPositionJson != null ? JsonSerializer.Serialize(vendorOldData.AcademicPositionJson) : "") != vndApp.AcademicPosition
                    || vendorOldData.SPLevel != vndApp.SPLevel ||
                    vendorOldData.HosDepartment != vndApp.HosDepartment || vendorOldData.CertificateCode != vndApp.CertificateCode)
                {
                    return (true, vndApp, vendorOldData);
                }
            }
            return (false, vndApp, vendorOldData);
        }

        private VendorApplication GetVndApp(Guid? vndAppId)
        {
            if (!vndAppId.HasValue)
            {
                return null;
            }
            var repoVndApp = LazyServiceProvider.LazyGetService<IVendorApplicationRepository>();
            var vndApp = repoVndApp.GetAsync(a => a.Id == vndAppId.Value).GetAwaiterResult();
            return vndApp;
        }

        private VendorApplicationPersonal GetVndAppPer(Guid? vndAppId)
        {
            if (!vndAppId.HasValue)
            {
                return null;
            }
            var repoVndAppPer = LazyServiceProvider.LazyGetService<IVendorApplicationPersonalRepository>();
            var queryVndAppPer = repoVndAppPer.GetQueryableAsync().GetAwaiterResult();
            return queryVndAppPer.FirstOrDefault(a => a.ApplicationId == vndAppId.Value);
        }

        private VendorPersonal GetVndPer(Guid vndId)
        {
            var repoVndPer = LazyServiceProvider.LazyGetService<IVendorPersonalRepository>();
            var queryVndAppPer = repoVndPer.GetQueryableAsync().GetAwaiterResult();
            return queryVndAppPer.FirstOrDefault(a => a.VendorId == vndId);
        }

        private VendorApplication GetLastVndApp(SpeakerDetailResponseDto vendorOldData)
        {
            if (vendorOldData == null)
            {
                return null;
            }
            var repoVndApp = LazyServiceProvider.LazyGetService<IVendorApplicationRepository>();
            var vndApp = repoVndApp.FirstOrDefaultAsync(a => a.VendorCode == vendorOldData.VendorCode
                && a.DraftVersion == vendorOldData.DraftVersion
                && a.Status == Statuses.Passed).GetAwaiterResult();
            return vndApp;
        }

        private VndEntities.Vendor GetVendorByVendorCode(string vndCode)
        {
            if (string.IsNullOrWhiteSpace(vndCode))
            {
                return null;
            }
            var repoVnd = LazyServiceProvider.LazyGetService<IVendorRepository>();
            var vnd = repoVnd.FirstOrDefaultAsync(a => a.VendorCode == vndCode).GetAwaiterResult();
            return vnd;
        }

        private string TransHcpStatus(bool isAdd, VendorStatus? status, ApplicationTypes applicationType)
        {
            //2856【Veeva集成】讲者激活：speaker_dcr.hcp_status、hcp_dcr.original_hcp_status__v和hcp_dcr. request_hcp_status__v都应该默认设置为Active
            if (applicationType == ApplicationTypes.Active)
                return "Active";

            if (isAdd || !status.HasValue)
            {
                return "Undetermined";
            }

            switch (status.Value)
            {
                case VendorStatus.Valid:
                    return "Active";

                case VendorStatus.Invalid:
                    return "Inactive";
                //case VendorStatus.Draft:
                //case VendorStatus.Abolition:
                //case VendorStatus.Exception:
                //case VendorStatus.ToBeEffective:
                //case VendorStatus.ToBeActivated:
                default:
                    return "Undetermined";
            }
        }

        private string TransHcoStatus(bool? isAdd)
        {
            if (isAdd == false)
            {
                return "Active";
            }
            return "Inactive";
        }


        private VeevaDcrReqDto TidyDcrReqDtoByYearlyReview(VndEntities.Vendor vnd)
        {
            var vndPer = GetVndPer(vnd.Id);
            var dicHcpLevels = GetHcpLevels();
            var dicAcademicLevel = GetAcademicLevels();
            var pts = GetPTs();
            var standartDept = GetStandarDepartments();
            var dicHospitals = GetHospitals(new List<Guid?> { vnd.HospitalId });

            //基础信息 data.hcp_dcr_entity
            var hcpDcrEntity = new
            {
                dcr_type = "CHANGE_REQUEST",
                note = (string)null,
                customer_dcr_id = Guid.NewGuid(),
                file_url = new List<string> { },//支持件链接 TODO:多个文件用|隔开
                hcp_code = vnd.VendorCode,

                previous_dcr_id = (string)null,
                vid__v = vnd.VendorId,

                original_full_name__v = vndPer.SPName,
                request_full_name__v = vndPer.SPName,
                original_gender__v = vndPer.Sex.GetDescription() ?? "未知",
                request_gender__v = vndPer.Sex.GetDescription() ?? "未知",
                original_department_name = vnd.HosDepartment,
                request_department_name = vnd.HosDepartment,
                original_primary_department_class__v = standartDept.GetValue(vnd.StandardHosDepId)?.Name,
                request_primary_department_class__v = standartDept.GetValue(vnd.StandardHosDepId)?.Name,
                original_specialty_1__v = standartDept.GetValue(vnd.StandardHosDepId)?.Specialty,//讲者专长 对应标准科室的专长
                request_specialty_1__v = standartDept.GetValue(vnd.StandardHosDepId)?.Specialty,//讲者专长 对应标准科室的专长
                original_primary_relation_type__v = vnd.RelationType,//讲者职务 不涉及修改，只是从veeva拿过来保存。
                request_primary_relation_type__v = vnd.RelationType,//讲者职务
                original_professional_title__v = pts.GetValue(vnd.PTId)?.Name,
                request_professional_title__v = pts.GetValue(vnd.PTId)?.Name,
                original_hcp_status__v = "Active",
                request_hcp_status__v = "Active",
                original_academic_title__v = dicAcademicLevel.GetValue(vnd.AcademicLevel)?.Name,
                request_academic_title__v = dicAcademicLevel.GetValue(vnd.AcademicLevel)?.Name,
                original_hcp_type__v = vnd.HCPType,//HCP 类型 参考职务
                request_hcp_type__v = vnd.HCPType,//HCP 类型 
                license__v = new List<dynamic>
                {
                    new
                    {
                        original_license_number__v=vnd.CertificateCode,//HCP 执业医师编码原始值
                        request_license_number__v=vnd.CertificateCode,//HCP 执业医师编码请求值
                    }
                },

                //parent_hcos__v：用供应商申请表的所属医院字段去查PP（hco_dcr也一样），PP的医院要加好多东西。。。
                parent_hcos__v = new List<dynamic>
                {
                    new
                    {
                        original_hco_code = dicHospitals.GetValue(vnd.HospitalId)?.HospitalCode,
                        request_hco_code = dicHospitals.GetValue(   vnd.HospitalId)?.HospitalCode,
                        original_hco_vid = dicHospitals.GetValue(vnd.HospitalId)?.HcoVeevaID,
                        request_hco_vid = dicHospitals.GetValue(vnd.HospitalId)?.HcoVeevaID,
                        original_hco_name = dicHospitals.GetValue(vnd.HospitalId)?.Name,
                        request_hco_name = dicHospitals.GetValue(vnd.HospitalId)?.Name,
                        original_hco_province=dicHospitals.GetValue(vnd.HospitalId)?.ProvinceName,
                        request_hco_province=dicHospitals.GetValue(vnd.HospitalId)?.ProvinceName,
                        original_hco_city=dicHospitals.GetValue(vnd.HospitalId)?.CityName,
                        request_hco_city=dicHospitals.GetValue(vnd.HospitalId)?.CityName,
                    }
                }
            };

            //讲者信息：data.speaker_dcr_entity
            //isAdd时，用vndApp只填original，!isAdd时，用vendorOldData填original，vndApp填request
            var speakerDcr = new VeevaSpeakerDcrEntityReqDto()
            {
                OriginalEntity = new VeevaOriginalEntityDto
                {
                    customer_dcr_id = Guid.NewGuid().ToString(),//.ToString("N"),
                    hcp_code = vnd.VendorCode,
                    speaker_code = vnd.EpdId,
                    dcr_type = "CHANGE_REQUEST",
                    previous_dcr_id = null,
                    note = null,
                    speaker_name = vndPer.SPName,
                    vid__v = vnd.VendorId,
                    speaker_level = dicHcpLevels.GetValue(vnd.SPLevel)?.Name,
                    gender = vndPer?.Sex.GetDescription() ?? "未知",

                    primary_department_class = standartDept.GetValue(vnd.StandardHosDepId)?.Name,
                    department_name = vnd.HosDepartment,
                    specialty = standartDept.GetValue(vnd.StandardHosDepId)?.Specialty,//讲者专长 
                    primary_relation_type = vnd.RelationType,//讲者职务 不涉及更新
                    professional_title = pts.GetValue(vnd.PTId)?.Name,

                    academic_title = dicAcademicLevel.GetValue(vnd.AcademicLevel)?.Name,
                    hcp_status = "Active",
                    hcp_type = vnd.HCPType,//HCP 类型，参考职务字段
                    parent_hcos__v = new List<VeevaParentHcosVDto> {
                         new VeevaParentHcosVDto()
                         {
                              hco_code=dicHospitals.GetValue(vnd.HospitalId)?.HospitalCode,
                              corporate_name=dicHospitals.GetValue(vnd.HospitalId)?.Name,
                              hco_grade=dicHospitals.GetValue(vnd.HospitalId)?.Level,
                              city=dicHospitals.GetValue(vnd.HospitalId)?.CityName,
                              province=dicHospitals.GetValue(vnd.HospitalId)?.ProvinceName,
                              hco_vid__v= dicHospitals.GetValue(vnd.HospitalId)?.HcoVeevaID,
                         }
                    },//用vndApp.HospitalId去PP查医院主数据
                    license = new List<VeevaLicenseDto>
                    {
                        new VeevaLicenseDto
                        {
                            license_number = vnd.CertificateCode,
                        }
                    },//TODO:
                },
                //requested_entity 结构本身 可以为 { }  可以为 ""  可以没有  但不能为 null
                RequestedEntity = new VeevaRequestedEntityDto
                {
                    speaker_level = dicHcpLevels.GetValue(vnd.SPLevel)?.Name,
                    professional_title = pts.GetValue(vnd.PTId)?.Name,
                },
                //学协会/期刊社任职
                AssociationPeriodicals = new List<VeevaAssociationPeriodical>
                {

                },

                //TODO?
                attachment_url = new List<string>
                {
                }
            };
            var requestType = new List<string> { "hcp_dcr", "speaker_dcr" };
            return new VeevaDcrReqDto()
            {
                RequestId = Guid.NewGuid().ToString(),
                RequestType = requestType,
                Data = new
                {
                    speaker_dcr_entity = speakerDcr,
                    hcp_dcr_entity = hcpDcrEntity
                }
            };
        }


        private VeevaDcrReqDto TidyDcrReqDto(VendorApplication vndApp, SpeakerDetailResponseDto vendorOldData)
        {
            var vndAppPer = GetVndAppPer(vndApp.Id);

            //是否新增，逻辑修改为判断是否有VeevaID，Veevaid就走change，没veevaid就走add
            //var isAdd = vndApp.ApplicationType == Enums.ApplicationTypes.Create;
            var isAdd = string.IsNullOrEmpty(vndApp.VendorId);

            var vndAppOld = isAdd ? null : GetLastVndApp(vendorOldData);
            //var vnd = isAdd ? null : GetVendorByVendorCode(vndApp.VendorCode);//先不查，就用vendorOldData
            var dicHcpLevels = GetHcpLevels();
            var dicAcademicLevel = GetAcademicLevels();
            var pts = GetPTs();
            var standartDept = GetStandarDepartments();
            var dicHospitals = GetHospitals(new List<Guid?> { vndApp.HospitalId, vendorOldData?.HospitalId });
            var (hcpPreDcrId, speakerPreDcrId, hcoPreDcrId) = GetPreviousDcrID(vndApp.Id);
            //基础信息 data.hcp_dcr_entity
            var hcpDcrEntity = new HcpDcrEntityDto
            {
                dcr_type = isAdd ? "ADD_REQUEST" : "CHANGE_REQUEST",
                note = (string)null,
                customer_dcr_id = Guid.NewGuid(),
                file_url = new List<string> { },//支持件链接 TODO:多个文件用|隔开
                hcp_code = vndApp.VendorCode,

                previous_dcr_id = (string)null,//hcpPreDcrId,
                vid__v = vndApp.VendorId,

                original_full_name__v = vendorOldData?.SPName,
                request_full_name__v = vndAppPer?.SPName,
                original_gender__v = isAdd ? "未知" : vndAppPer?.Sex.GetDescription() ?? "未知",
                request_gender__v = vndAppPer?.Sex.GetDescription() ?? "未知",
                original_department_name = vendorOldData?.HosDepartment,
                request_department_name = vndApp.HosDepartment,
                original_primary_department_class__v = standartDept.GetValue(vendorOldData?.StandardHosDepId)?.Name,
                request_primary_department_class__v = standartDept.GetValue(vndApp.StandardHosDepId)?.Name,
                original_specialty_1__v = standartDept.GetValue(vendorOldData?.StandardHosDepId)?.Specialty,//讲者专长 对应标准科室的专长
                request_specialty_1__v = standartDept.GetValue(vndApp.StandardHosDepId)?.Specialty,//讲者专长 对应标准科室的专长
                original_primary_relation_type__v = vndApp.RelationType,//讲者职务 不涉及修改，只是从veeva拿过来保存。
                request_primary_relation_type__v = vndApp.RelationType,//讲者职务
                original_professional_title__v = pts.GetValue(vendorOldData?.PTId)?.Name,
                request_professional_title__v = pts.GetValue(vndApp.PTId)?.Name,
                original_hcp_status__v = TransHcpStatus(isAdd, vendorOldData?.Status, vndApp.ApplicationType),
                //request_hcp_status__v = TransHcpStatus(isAdd, vendorOldData?.Status, vndApp.ApplicationType),//与org保持一致
                request_hcp_status__v = "Active",
                original_academic_title__v = dicAcademicLevel.GetValue(vendorOldData?.AcademicLevel)?.Name,
                request_academic_title__v = dicAcademicLevel.GetValue(vndApp.AcademicLevel)?.Name,
                original_hcp_type__v = vndApp.HCPType,//HCP 类型 参考职务
                request_hcp_type__v = vndApp.HCPType,//HCP 类型 
                license__v = new List<LicenseDto>
                {
                    new LicenseDto
                    {
                        original_license_number__v=vendorOldData?.CertificateCode,//HCP 执业医师编码原始值
                        request_license_number__v=vndApp?.CertificateCode,//HCP 执业医师编码请求值
                    }
                },

                //parent_hcos__v：用供应商申请表的所属医院字段去查PP（hco_dcr也一样），PP的医院要加好多东西。。。
                parent_hcos__v = new List<ParentHcosVDto>
                {
                    new ParentHcosVDto
                    {
                        original_hco_code = dicHospitals.GetValue(vendorOldData?.HospitalId)?.HospitalCode,
                        request_hco_code = dicHospitals.GetValue(vndApp.HospitalId)?.HospitalCode,
                        original_hco_vid = dicHospitals.GetValue(vendorOldData?.HospitalId)?.HcoVeevaID,
                        request_hco_vid = dicHospitals.GetValue(vndApp.HospitalId)?.HcoVeevaID,
                        original_hco_name = dicHospitals.GetValue(vendorOldData?.HospitalId)?.Name,
                        request_hco_name = dicHospitals.GetValue(vndApp.HospitalId)?.Name,
                        original_hco_province=dicHospitals.GetValue(vendorOldData?.HospitalId)?.ProvinceName,
                        request_hco_province=dicHospitals.GetValue(vndApp.HospitalId)?.ProvinceName,
                        original_hco_city=dicHospitals.GetValue(vendorOldData?.HospitalId)?.CityName,
                        request_hco_city=dicHospitals.GetValue(vndApp.HospitalId)?.CityName,
                    }
                }
            };

            //讲者信息：data.speaker_dcr_entity
            //isAdd时，用vndApp只填original，!isAdd时，用vendorOldData填original，vndApp填request
            var speakerDcr = new VeevaSpeakerDcrEntityReqDto()
            {
                OriginalEntity = new VeevaOriginalEntityDto
                {
                    customer_dcr_id = Guid.NewGuid().ToString(),//.ToString("N"),
                    hcp_code = vndApp?.VendorCode,
                    speaker_code = isAdd ? vndApp?.EpdId : vendorOldData?.EpdId,
                    dcr_type = isAdd ? "ADD_REQUEST" : "CHANGE_REQUEST",
                    previous_dcr_id = null, //speakerPreDcrId,

                    note = null,
                    speaker_name = isAdd ? vndAppPer?.SPName : vendorOldData?.SPName,
                    vid__v = isAdd ? vndApp.VendorId : vndAppOld?.VendorId,
                    speaker_level = isAdd ? dicHcpLevels.GetValue(vndApp.SPLevel)?.Name
                        : dicHcpLevels.GetValue(vendorOldData?.SPLevel)?.Name,
                    gender = vndAppPer?.Sex.GetDescription() ?? "未知",

                    primary_department_class = isAdd ? standartDept.GetValue(vndApp.StandardHosDepId)?.Name
                        : standartDept.GetValue(vendorOldData?.StandardHosDepId)?.Name,
                    department_name = isAdd ? vndApp.HosDepartment : vendorOldData?.HosDepartment,
                    specialty = standartDept.GetValue(isAdd ? vndApp.StandardHosDepId : vendorOldData?.StandardHosDepId)?.Specialty,//讲者专长 
                    primary_relation_type = vndApp.RelationType,//讲者职务 不涉及更新
                    professional_title = isAdd ? pts.GetValue(vndApp.PTId)?.Name
                        : pts.GetValue(vendorOldData?.PTId)?.Name,
                    //4582 ytw 20250313【Veeva集成】在change request时，如果我们查询到原始学术级别为空，即转换为 “无学术头衔“ 填入original_entity
                    academic_title = isAdd ? dicAcademicLevel.GetValue(vndApp.AcademicLevel)?.Name
                        : (string.IsNullOrWhiteSpace(vendorOldData?.AcademicLevel) ? "无学术头衔" : dicAcademicLevel.GetValue(vendorOldData?.AcademicLevel)?.Name),
                    hcp_status = TransHcpStatus(isAdd, vendorOldData?.Status, vndApp.ApplicationType),
                    hcp_type = vndApp.HCPType,//HCP 类型，参考职务字段
                    parent_hcos__v = new List<VeevaParentHcosVDto> {
                         new VeevaParentHcosVDto()
                         {
                              hco_code=dicHospitals.GetValue(isAdd?vndApp.HospitalId: vendorOldData?.HospitalId)?.HospitalCode,
                              corporate_name=dicHospitals.GetValue(isAdd?vndApp.HospitalId: vendorOldData?.HospitalId)?.Name,
                              hco_grade=dicHospitals.GetValue(isAdd?vndApp.HospitalId: vendorOldData?.HospitalId)?.Level,
                              city=dicHospitals.GetValue(isAdd?vndApp.HospitalId: vendorOldData?.HospitalId)?.CityName,
                              province=dicHospitals.GetValue(isAdd?vndApp.HospitalId: vendorOldData?.HospitalId)?.ProvinceName,
                              hco_vid__v= dicHospitals.GetValue(isAdd?vndApp.HospitalId: vendorOldData?.HospitalId)?.HcoVeevaID,
                         }
                    },//用vndApp.HospitalId去PP查医院主数据
                    license = new List<VeevaLicenseDto>
                    {
                        new VeevaLicenseDto
                        {
                            license_number = isAdd ? vndApp?.CertificateCode : vendorOldData?.CertificateCode,
                        }
                    },//TODO:
                },
                //requested_entity 结构本身 可以为 { }  可以为 ""  可以没有  但不能为 null
                RequestedEntity = isAdd ? ""
                : new VeevaRequestedEntityDto
                {
                    speaker_level = dicHcpLevels.GetValue(vndApp.SPLevel)?.Name,
                    professional_title = pts.GetValue(vndApp.PTId)?.Name,
                },

                //学协会/期刊社任职
                AssociationPeriodicals = new List<VeevaAssociationPeriodical>
                {

                },

                //TODO?
                attachment_url = new List<string>
                {
                }
            };
            //speaker dcr 学协会/期刊社任职
            //只支持新增学会，不支持学会信息变更
            //Change 数据，不需要把已有学会信息提交。如果提交，视为新增学会信息
            if (!string.IsNullOrEmpty(vndApp.AcademicPosition))
            {
                if (isAdd)
                {
                    try
                    {
                        var apList = Newtonsoft.Json.JsonConvert.DeserializeObject<List<AcademicPositionDto>>(vndApp.AcademicPosition);
                        foreach (var a in apList)
                        {
                            speakerDcr.AssociationPeriodicals.Add(new VeevaAssociationPeriodical()
                            {
                                org_grade__c = a.JournalCategoryName, //机构级别
                                org_name__c = a.JournalName,//社会机构名称
                                org_type__c = a.JournalTypeName,//社会机构类型
                                org_verify_status_c = "",//社会机构验证状态, 找不到此字段的对应，在接口文档里是非必填
                                org_relation_type__c = a.AppointmentName,//讲者在机构的任职
                                org_relation_status__c = a.AppointmentStatusName//讲者在机构任职状态
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"Veeva_Speaker_DCR_AcademicPosition_Deserialize,{vndApp.AcademicPosition}, Exception: {ex}");
                    }
                }
                else//变更时，只发送变更的学协会数据
                {
                    var apList = Newtonsoft.Json.JsonConvert.DeserializeObject<List<AcademicPositionDto>>(vndApp.AcademicPosition);
                    var changedAcademics = apList.Where(a => a.IsChanged).Select(a => new VeevaAssociationPeriodical
                    {
                        org_grade__c = a.JournalCategoryName, //机构级别
                        org_name__c = a.JournalName,//社会机构名称
                        org_type__c = a.JournalTypeName,//社会机构类型
                        org_verify_status_c = "",//社会机构验证状态, 找不到此字段的对应，在接口文档里是非必填
                        org_relation_type__c = a.AppointmentName,//讲者在机构的任职
                        org_relation_status__c = a.AppointmentStatusName//讲者在机构任职状态
                    });
                    if (changedAcademics.Any())
                        speakerDcr.AssociationPeriodicals.AddRange(changedAcademics);
                }
            }

            //医院信息，文档里必填字段已补充完整
            var hcoDcrEntity = dicHospitals.GetValue(vndApp.HospitalId)?.IsAdd == true
                ? new
                {
                    dcr_type = "ADD_REQUEST",
                    note = (string)null,
                    customer_dcr_id = Guid.NewGuid(),
                    file_url = new List<string> { },//支持件链接 TODO:多个文件用|隔开
                    previous_dcr_id = (string)null,//hcoPreDcrId,
                    hco_code = dicHospitals.GetValue(vndApp.HospitalId)?.HospitalCode,
                    vid__v = dicHospitals.GetValue(vndApp.HospitalId)?.HcoVeevaID,//
                    request_corporate_name = dicHospitals.GetValue(vndApp.HospitalId)?.Name,
                    request_administrative_area = dicHospitals.GetValue(vndApp.HospitalId)?.ProvinceName,  //机构所在省份请求值
                    request_locality = dicHospitals.GetValue(vndApp.HospitalId)?.CityName,  //机构所在城市请求值
                    //request_organization_code = dicHospitals.GetValue(vndApp.HospitalId)?.HospitalCode,
                    //request_hco_status = TransHcoStatus(dicHospitals.GetValue(vndApp.HospitalId)?.IsAdd),
                    //original_corporate_name= dicHospitals.GetValue(vendorOldData?.HospitalId)?.Name,
                    //original_alternate_name_1 = "11111111111",//TODO:需要PP里加字段？
                    //request_alternate_name_1 = "1111111111111",//TODO:需要PP里加字段？
                    //original_alternate_name_2
                    //request_alternate_name_2
                    //...4
                    //original_hco_status=                    
                    //original_hco_type
                    //request_hco_type="1111111111111111"//TODO:需要PP里加字段？
                    //original_hco_grade
                    //request_hco_grade="1111111111111111"//TODO:需要PP里加字段？
                    //original_hco_property
                    //request_hco_property="1111111111111111"//TODO:需要PP里加字段？
                    //original_organization_code

                    //original_administrative_area
                    //request_administrative_area = "1111111111111111"//TODO:需要PP里加字段？
                    //original_locality
                    //request_locality = "1111111111111111"//TODO:需要PP里加字段？
                    /*
                    original_sub_administrative_area
                    request_sub_administrative_area
                    original_address_line
                    request_address_line
                    original_post_code
                    request_post_code
                    original_cn_longitude
                    request_cn_longitude
                    original_cn_latitude
                    request_cn_latitude

                     */
                }
                : null;

            var requestId = Guid.NewGuid();
            var requestType = new List<string> { "hcp_dcr", "speaker_dcr" };
            requestType.AddIf(hcoDcrEntity != null, "hco_dcr");

            //推送附件，2696【Veeva集成】speaker_dcr推送时：需附加attachment_url（sftp目录地址），将除身份证和dpcCheck外的附件传到此attachment_url供Veeva查询
            if (!string.IsNullOrEmpty(vndApp.AttachmentInformation))
            {
                var path = UploadToVeevaSftp(vndApp.AttachmentInformation, requestId);
                if (!string.IsNullOrEmpty(path))
                    speakerDcr.attachment_url.Add(path);
            }

            //zhx20250228:将该VendorCode的最近一次Veeva验证通过的结果加到Original的字段
            SetLastVeevaResultFieldsToOriginal(hcpDcrEntity, speakerDcr, vndApp.VendorCode, isAdd);

            return new VeevaDcrReqDto()
            {
                RequestId = requestId.ToString(),
                RequestType = requestType,
                Data = new
                {
                    speaker_dcr_entity = speakerDcr,
                    hcp_dcr_entity = hcpDcrEntity,
                    hco_dcr_entity = hcoDcrEntity,
                }
            };
        }

        private void SetLastVeevaResultFieldsToOriginal(HcpDcrEntityDto hcpDcrEntity, VeevaSpeakerDcrEntityReqDto speakerDcr, string vendorCode, bool isAdd)
        {
            _logger.LogInformation($"SetLastVeevaResultFieldsToOriginal() Begin");

            try
            {
                var lastRstFields = GetLastVeevaRstFields(vendorCode);
                if (lastRstFields == null)//查不到则保持从原来 vendorApp 表中UpdatePreJson 取值，老数据没有迁移到VeevaResultHistory表导致  
                    return;
                //lastRstFields.VendorId应该是唯一标识，应该都是Veeva给过来的，依旧回传
                //在hcpDcrEntity、speakerDcr 里都已赋值,speakerDcr的参照前面的逻辑：isAdd ? vndApp.VendorId : vndAppOld?.VendorId,
                if (!isAdd)
                {
                    speakerDcr.OriginalEntity.vid__v = lastRstFields?.VendorId;
                }

                //院内科室：lastRstFields.HosDepartment
                //拉取Veeva结果时，如果dcrResultSpeakerEntity.verify_department_name为空，则保留的原值vndApp.HosDepartment，
                //已向Wendy确认，再次提交Veeva时，若上次Veeva结果是空，也传我方的原值给Veeva。
                //而在上次拉Veeva的结果时若Veeva的HosDepartment为空就保留的原值vndApp.HosDepartment并存到lastRstFields.HosDepartment，所以这里只管取lastRstFields?.HosDepartment即可
                hcpDcrEntity.original_department_name = lastRstFields?.HosDepartment;
                speakerDcr.OriginalEntity.department_name = lastRstFields?.HosDepartment;

                //lastRstFields.StandardHosDepId
                var standartDept = GetStandarDepartments();
                hcpDcrEntity.original_primary_department_class__v =
                    speakerDcr.OriginalEntity.primary_department_class = standartDept.GetValue(lastRstFields?.StandardHosDepId)?.Name;

                //lastRstFields.PTId
                //lastRstFields.PTName
                var pts = GetPTs();
                hcpDcrEntity.original_professional_title__v =
                    speakerDcr.OriginalEntity.professional_title = pts.GetValue(lastRstFields?.PTId)?.Name;

                //lastRstFields.VerificationStatus

                //lastRstFields.AcademicLevel
                var dicAcademicLevel = GetAcademicLevels();
                hcpDcrEntity.original_academic_title__v =
                    speakerDcr.OriginalEntity.academic_title = string.IsNullOrWhiteSpace(lastRstFields?.AcademicLevel) ? "无学术头衔" : dicAcademicLevel.GetValue(lastRstFields?.AcademicLevel)?.Name;

                //lastRstFields.SPLevel
                var dicHcpLevels = GetHcpLevels();
                speakerDcr.OriginalEntity.speaker_level = dicHcpLevels.GetValue(lastRstFields?.SPLevel)?.Name;

                //lastRstFields.RelationType
                //讲者职务 不涉及修改，只是从veeva拿过来保存。
                hcpDcrEntity.original_primary_relation_type__v =
                    speakerDcr.OriginalEntity.primary_relation_type = lastRstFields?.RelationType;

                //lastRstFields.HCPType
                //HCP 类型 参考职务
                hcpDcrEntity.original_hcp_type__v =
                    speakerDcr.OriginalEntity.hcp_type = lastRstFields?.HCPType;

                //lastRstFields.CertificateCode //HCP 执业医师编码请求值
                //HcpDcrEntity里的该值，只重新设original_license_number__v的值，request_license_number__v保持前面已有的值
                if (hcpDcrEntity.license__v != null && hcpDcrEntity.license__v?[0] != null)
                {
                    //之前的逻辑是新增时，Original，放的本次新增的值，否则是放前一次正式的值。
                    //现在修改后，新增时都给的空，否则就是有VeevaHistory时给的上次Veeva的值。
                    hcpDcrEntity.license__v[0].original_license_number__v = lastRstFields?.CertificateCode;
                }
                //speakerDcrEntity里的该值，参照前面的逻辑，isAdd ? vndApp?.CertificateCode : vendorOldData?.CertificateCode,
                //isAdd时保持不变，否则将赋值lastRstFields?.CertificateCode，
                if (!isAdd && speakerDcr?.OriginalEntity?.license?.Any() == true)
                {
                    speakerDcr.OriginalEntity.license[0].license_number = lastRstFields?.CertificateCode;
                }

                //lastRstFields.EpdId
                speakerDcr.OriginalEntity.speaker_code = lastRstFields?.EpdId;

                //lastRstFields.AcademicPosition
                //已向Wendy确认：保持之前已有的逻辑，即，变更的提交时只传VndApp.AcademicPosition里isChange==true的，
                //并且它没在speakerDcr.OriginalEntity下，是speakerDcr.AssociationPeriodicals，所以应该不是传上次验证结果。

                _logger.LogInformation($"SetLastVeevaResultFieldsToOriginal() End");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"SetLastVeevaResultFieldsToOriginal() Error: {ex.Message}");
            }
        }

        private VeevaResultFieldsDto GetLastVeevaRstFields(string vendorCode)
        {
            //通过VendorCode（医生唯一标识）找它是否已有VeevaResultHistory
            var repo = LazyServiceProvider.LazyGetService<IVeevaResultHistoryRepository>().GetQueryableAsync().GetAwaiterResult().AsNoTracking();
            var lastResult = repo.OrderByDescending(a => a.CreationTime).FirstOrDefault(a => a.VendorCode == vendorCode
                && a.DcrResultApproveStatus == DcrResultApproveStatus.Approved.GetDescription());

            return lastResult?.VeevaResultFieldsContent == null ? null :
                NewtonsoftJson.JsonConvert.DeserializeObject<VeevaResultFieldsDto>(lastResult?.VeevaResultFieldsContent);
        }

        #region 查询Dataverse（Redis或PP）

        private Dictionary<string, DictionaryDto> GetHcpLevels(IEnumerable<string> codes = null)
        {
            var dicMeetingTypes = _dataverseService.GetDictionariesAsync(DictionaryType.HCPLevel).GetAwaiterResult();
            return dicMeetingTypes.WhereIf(codes?.Any() == true, a => codes.Contains(a.Code)).ToDictionary(a => a.Code, a => a);
        }

        private Dictionary<string, DictionaryDto> GetAcademicLevels()
        {
            var dicMeetingTypes = _dataverseService.GetDictionariesAsync(DictionaryType.LearningLevel).GetAwaiterResult();
            return dicMeetingTypes.ToDictionary(a => a.Code, a => a);
        }

        private Dictionary<Guid, JobTitleDto> GetPTs()
        {
            var jobTiles = _dataverseService.GetAllJobTiles().GetAwaiterResult();
            return jobTiles.ToDictionary(a => a.Id, a => a);
        }

        private Dictionary<Guid, OfficeDto> GetStandarDepartments()
        {
            return _dataverseService.GetAllDepartments().GetAwaiterResult()
                .ToDictionary(a => a.Id, a => a);
        }

        private Dictionary<Guid, HospitalDto> GetHospitals(List<Guid?> ids)
        {
            if (ids?.Any() != true)
            {
                return new Dictionary<Guid, HospitalDto>();
            }
            ids = ids.Where(a => a != null).Distinct().ToList();

            return _dataverseService.GetAllHospitals().GetAwaiter().GetResult()
                .Where(a => ids.Contains(a.Id)).ToDictionary(a => a.Id, a => a);
        }

        private Dictionary<Guid, DepartmentDto> GetOrganizations()
        {
            return _dataverseService.GetOrganizations().GetAwaiter().GetResult().ToDictionary(a => a.Id, a => a);
        }

        #endregion 查询Dataverse（Redis或PP）

        private void DCRLog(VeevaDcrReqDto requestDto, VeevaDcrRespDto respDto, VendorApplication vndApp = null, VndEntities.Vendor vnd = null)
        {

            //IdentityUser firstUser = null;
            //Guid? deptId=Guid.Empty;
            //Dictionary<Guid, DepartmentDto> dicOrg=new Dictionary<Guid, DepartmentDto>();
            //if (vndApp != null)
            //{
            //    firstUser =_serviceProvider.GetService<IRepository<IdentityUser>>().FirstOrDefaultAsync(a => a.Id == vndApp.ApplyUserId).GetAwaiterResult();
            //    deptId = firstUser.GetProperty<Guid?>(EntityConsts.IdentityUser.MainDepartmentId);
            //    dicOrg = GetOrganizations();
            //}  
            List<VeevaDCRLog> logs = new List<VeevaDCRLog>();
            //hcp speaker hco 分开记录
            foreach (var item in requestDto.RequestType)
            {
                var log = new VeevaDCRLog();
                log.BusinessFormId = vndApp == null ? vnd.Id : vndApp.Id;//年度更新调用dcr，记录vendor表ID
                log.RequestType = item;
                log.RequestID = Guid.Parse(requestDto.RequestId);
                if (vndApp != null)
                {
                    log.ApplyUserDept = Guid.Parse(vndApp.ApplyUserBu);
                    log.ApplyUserDeptName = vndApp.ApplyDeptName;
                    log.ApplyUserBu = vndApp.ApplyBuId.Value;
                    log.ApplyUserBuName = vndApp.ApplyBuName;
                }

                if (item == DcrRequestType.hcp_dcr.GetDescription())
                {
                    log.CustomerDCRID = requestDto.Data.hcp_dcr_entity?.customer_dcr_id;
                    log.DCRType = requestDto.Data.hcp_dcr_entity?.dcr_type;
                    log.RequestContent = JsonSerializer.Serialize(requestDto.Data.hcp_dcr_entity);
                    log.DCRResponseStatus = respDto.Data?.hcp_dcr?.hcp_dcr_response_status;
                    log.DCRResponseMessage = respDto.Data?.hcp_dcr?.message;
                    // 没有状态码，从父节点获取
                    if (string.IsNullOrEmpty(log.DCRResponseStatus))
                    {
                        log.DCRResponseStatus = respDto.ResponseStatus;
                        log.DCRResponseMessage = respDto.ResponseMessage;
                    }
                    log.DCRID = respDto.Data?.hcp_dcr?.dcr_id;
                }
                else if (item == DcrRequestType.speaker_dcr.GetDescription())
                {
                    log.CustomerDCRID = Guid.Parse(requestDto.Data.speaker_dcr_entity?.OriginalEntity?.customer_dcr_id);
                    log.DCRType = requestDto.Data.speaker_dcr_entity?.OriginalEntity?.dcr_type;
                    log.RequestContent = JsonSerializer.Serialize(requestDto.Data.speaker_dcr_entity);

                    log.DCRResponseStatus = respDto.Data?.speaker_dcr?.speaker_dcr_response_status;
                    log.DCRResponseMessage = respDto.Data?.speaker_dcr?.message;
                    // 没有状态码，从父节点获取
                    if (string.IsNullOrEmpty(log.DCRResponseStatus))
                    {
                        log.DCRResponseStatus = respDto.ResponseStatus;
                        log.DCRResponseMessage = respDto.ResponseMessage;
                    }
                    log.DCRID = respDto.Data?.speaker_dcr?.dcr_id;

                }
                else
                {
                    log.CustomerDCRID = requestDto.Data.hco_dcr_entity?.customer_dcr_id;
                    log.DCRType = requestDto.Data.hco_dcr_entity?.dcr_type;
                    log.RequestContent = JsonSerializer.Serialize(requestDto.Data.hco_dcr_entity);
                    log.DCRResponseStatus = respDto.Data?.hco_dcr?.hco_dcr_response_status;
                    log.DCRResponseMessage = respDto.Data?.hco_dcr?.message;
                    // 没有状态码，从父节点获取
                    if (string.IsNullOrEmpty(log.DCRResponseStatus))
                    {
                        log.DCRResponseStatus = respDto.ResponseStatus;
                        log.DCRResponseMessage = respDto.ResponseMessage;
                    }
                    log.DCRID = respDto.Data?.hco_dcr?.dcr_id;
                }
                logs.Add(log);
            }
            var repoVeevaDCRLog = LazyServiceProvider.LazyGetService<IVeevaDCRLogRepository>();
            repoVeevaDCRLog.InsertManyAsync(logs, true).GetAwaiterResult();
        }

        private void DCRResultLog(VeevaDcrResultRespDto respObj, List<VeevaDCRLog> veevaDcrLogs)
        {

            foreach (var item in veevaDcrLogs)
            {
                if (item.RequestType == DcrRequestType.hcp_dcr.GetDescription())
                {
                    var hcp_dcr_r = respObj.data?.hcp_dcr_result?.Where(a => a.dcr_id == item.DCRID).SingleOrDefault();
                    if (hcp_dcr_r != null)
                    {
                        item.ResultContent = JsonSerializer.Serialize(hcp_dcr_r);
                        item.DCRResultResponseStatus = hcp_dcr_r.status;
                        item.DCRResultResponseMessage = hcp_dcr_r.message;
                        item.DCRCreateTime = hcp_dcr_r.created_date;
                        item.DCRCompleteTime = hcp_dcr_r.completed_date;
                    }
                }
                else if (item.RequestType == DcrRequestType.speaker_dcr.GetDescription())
                {
                    var speaker_dcr_r = respObj.data?.speaker_dcr_results?.Where(a => a.dcr_id == item.DCRID).SingleOrDefault();
                    if (speaker_dcr_r != null)
                    {
                        item.ResultContent = JsonSerializer.Serialize(speaker_dcr_r);
                        item.DCRResultResponseStatus = speaker_dcr_r.status;
                        item.DCRResultResponseMessage = speaker_dcr_r.message;
                        item.DCRCreateTime = speaker_dcr_r.created_date;
                        item.DCRCompleteTime = speaker_dcr_r.completed_date;
                    }
                }
                else
                {
                    var hco_dcr_r = respObj.data?.hco_dcr_result?.Where(a => a.dcr_id == item.DCRID).SingleOrDefault();
                    if (hco_dcr_r != null)
                    {
                        item.ResultContent = JsonSerializer.Serialize(hco_dcr_r);
                        item.DCRResultResponseStatus = hco_dcr_r.status;
                        item.DCRResultResponseMessage = hco_dcr_r.message;
                        item.DCRCreateTime = hco_dcr_r.created_date;
                        item.DCRCompleteTime = hco_dcr_r.completed_date;
                    }
                }
            }

            //var repoVeevaDCRLog = LazyServiceProvider.LazyGetService<IVeevaDCRLogRepository>();
            //repoVeevaDCRLog.UpdateManyAsync(veevaDcrLogs, true).GetAwaiterResult();
        }

        private (string, string, string) GetPreviousDcrID(Guid vndId)
        {
            var repoVeevaDCRLog = LazyServiceProvider.LazyGetService<IVeevaDCRLogRepository>();
            var dcrLogs = repoVeevaDCRLog.GetListAsync(a => a.BusinessFormId == vndId).GetAwaiterResult();
            if (dcrLogs.Count == 0)
            {
                return ("", "", "");
            }
            else
            {
                var hcpDcr = dcrLogs.Where(a => a.RequestType == DcrRequestType.hcp_dcr.GetDescription()).OrderByDescending(a => a.CreationTime).FirstOrDefault();
                var speakerDcr = dcrLogs.Where(a => a.RequestType == DcrRequestType.speaker_dcr.GetDescription()).OrderByDescending(a => a.CreationTime).FirstOrDefault();
                var hcoDcr = dcrLogs.Where(a => a.RequestType == DcrRequestType.hco_dcr.GetDescription()).OrderByDescending(a => a.CreationTime).FirstOrDefault();
                return (hcpDcr?.DCRID, speakerDcr?.DCRID, hcoDcr?.DCRID);
            }
        }

        /// <summary>
        /// 上传附件至veeva的sftp服务器
        /// </summary>
        /// <param name="attachmentIdStrings"></param>
        /// <param name="requestId"></param>
        public string UploadToVeevaSftp(string attachmentIdStrings, Guid requestId)
        {
            var attachmentService = LazyServiceProvider.LazyGetService<IAttachmentService>();
            var attachmentIds = attachmentIdStrings.Split(",").Select(Guid.Parse).ToArray();
            var attachments = attachmentService.GetAttachmentsAsync(attachmentIds).GetAwaiterResult();
            attachments = attachments.Where(a => !a.FileName.Contains("身份证") && !a.FileName.Contains("银行卡"));

            if (!attachments.Any())
                return null;

            var root = "/VendorAttachments";
            var directory = Path.Combine(root, requestId.ToString());
            var sftpService = LazyServiceProvider.LazyGetService<IInteVeevaSftpService>();
            //打开连接
            sftpService.SftpClient.Connect();

            //创建目录
            if (!sftpService.SftpClient.Exists(root))
                sftpService.SftpClient.CreateDirectory(root);
            if (!sftpService.SftpClient.Exists(directory))
                sftpService.SftpClient.CreateDirectory(directory);

            //下载附件然后再上传到veeva的sftp
            foreach (var item in attachments)
            {
                var stream = attachmentService.DownloadStream(item.FilePath).GetAwaiterResult();
                var filePath = Path.Combine(directory, Path.GetFileName(item.FilePath));
                stream.Seek(0, SeekOrigin.Begin);
                sftpService.SftpClient.UploadFile(stream, filePath, true);
                stream.Close();
            }

            //关闭连接
            sftpService.SftpClient.Disconnect();

            //2959（优先）【Veeva】医师信息验证支持件传送要求调整
            return directory;
        }
        #region Job 相关
        /// <summary>
        /// 超过五个自然日 邮件提醒
        /// </summary>
        /// <returns></returns>
        public async Task SendingEmailTimeoutPushTask() 
        {
            DateTime pushTime = DateTime.Now.Date.AddDays(-5);
            var veevaDCRLogQuery = ( await LazyServiceProvider.LazyGetService<IVeevaDCRLogRepository>().GetQueryableAsync()).AsNoTracking();
            var queryVndApp = (await LazyServiceProvider.LazyGetService<IVendorApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            var veevaDCRLogs = veevaDCRLogQuery.Where(a => !string.IsNullOrEmpty(a.DCRID) && 
                a.DCRResponseStatus == "0" && 
                a.DCRResultResponseStatus != DcrResultStatus.Completed.GetDescription() && 
                a.FlagType == FlagType.NexBpm &&
                a.RequestType == "speaker_dcr"&&
                a.CreationTime < pushTime
            )
            .ToList();
            
            var listTodoVndApp = queryVndApp.Where(va => veevaDCRLogs.Select(a => a.BusinessFormId).Distinct().Any(a => a == va.Id))
                .ToList();

            var sendEmaillRecords = new InsertSendEmaillRecordDto
            {
                EmailAddress = _helpdeskEmail,
                CCAddresses = _veevaTimeoutCCEmail,
                Subject = "[NexBPM] 医师信息验证长期未返回提醒。",
                Content = JsonSerializer.Serialize(listTodoVndApp.Select(a=>a.ApplicationCode).ToList()),
                SourceType = EmailSourceType.VeevaPushEmail,
                Status = SendStatus.Pending,
                Attempts = 0,
            };
            //记录邮件，并触发邮件发送功能
            var emailService = LazyServiceProvider.LazyGetService<IEmailService>();
            await emailService.InsertSendEmaillRecordAsync([sendEmaillRecords]);
            // 调度作业
            BackgroundJob.Enqueue<SendEmailWorker>(a => a.DoWorkAsync());
        }
        /// <summary>
        /// 变更Hco_DcrLog 状态
        /// </summary>
        /// <returns></returns>
        public async Task UpdateHcoDcrLogTask() 
        {
            var dcrLogRepository = LazyServiceProvider.LazyGetService<IVeevaDCRLogRepository>();
            var veevaDCRLogQuery = await dcrLogRepository.GetQueryableAsync();

            var hco_dcrLogs = veevaDCRLogQuery.Where(a => !string.IsNullOrEmpty(a.DCRID) &&
                a.DCRResponseStatus == "0" &&
                a.DCRResultResponseStatus != DcrResultStatus.Completed.GetDescription() &&
                a.FlagType == FlagType.NexBpm &&
                a.RequestType == "hco_dcr"
            );
            var speaker_dcrLogs = veevaDCRLogQuery.Where(a => !string.IsNullOrEmpty(a.DCRID) &&
                a.DCRResponseStatus == "0" &&
                a.DCRResultResponseStatus == DcrResultStatus.Completed.GetDescription() &&
                a.FlagType == FlagType.NexBpm &&
                a.RequestType == "speaker_dcr");

            var veevaDCRLogsQuery = hco_dcrLogs.Join(speaker_dcrLogs,
                a => a.RequestID,
                b => b.RequestID,
                (a, b) => new { hco_dcr = a, speaker_dcr = b });

            var veevaDCRLogs = veevaDCRLogsQuery.ToList();

            var updateHcoDcrLogs = veevaDCRLogs.Select(a => a.hco_dcr).ToList();
            if (updateHcoDcrLogs.Any()) 
            {
                foreach (var item in updateHcoDcrLogs)
                {
                    item.DCRResultResponseStatus = DcrResultStatus.Completed.GetDescription();
                    item.DCRResultResponseMessage = "更新成功";
                }
                await dcrLogRepository.UpdateManyAsync(updateHcoDcrLogs);
            }
        }
        #endregion
    }
}