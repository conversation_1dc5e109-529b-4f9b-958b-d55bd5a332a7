SELECT
 TRY_CONVERT(UNIQ<PERSON>IDENTIFIER, [Id]) [Id]
,[Code]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([BuId],'00000000-0000-0000-0000-000000000000')) [BuId]
,[Description]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([OwnerId],'00000000-0000-0000-0000-000000000000')) [OwnerId]
,iif(Capital is null,0,Capital) AS [Capital]
,iif(BudgetAmount is null,0,Capital) AS [BudgetAmount]
,iif(Status is null,0,Capital) [Status]
,[Remark]
,'{}' AS [ExtraProperties]
,'NULL' AS [ConcurrencyStamp]
,GETDATE() AS [CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CreatorId]) [CreatorId]
,[LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [LastModifierId]) [LastModifierId]
,0 AS [IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, [DeleterId]) [DeleterId]
,[DeletionTime]
,[Year]
INTO #BdMasterBudgets
FROM PLATFORM_ABBOTT_STG.dbo.BdMasterBudgets

-- DROP TABLE #BdMasterBudgets

USE Speaker_Portal_STG;

UPDATE a
SET
 a.[Id] = b.[Id]
,a.[Code] = b.[Code]
,a.[BuId] = b.[BuId]
,a.[Description] = b.[Description]
,a.[OwnerId] = b.[OwnerId]
,a.[Capital] = b.[Capital]
,a.[BudgetAmount] = b.[BudgetAmount]
,a.[Status] = b.[Status]
,a.[Remark] = b.[Remark]
,a.[ExtraProperties] = b.[ExtraProperties]
,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = b.[CreatorId]
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = b.[LastModifierId]
,a.[IsDeleted] = b.[IsDeleted]
,a.[DeleterId] = b.[DeleterId]
,a.[DeletionTime] = b.[DeletionTime]
,a.[Year] = b.[Year]
FROM dbo.BdMasterBudgets a
left join #BdMasterBudgets  b
ON a.id=b.id


--select* from #BdMasterBudgets WHERE BuId IS NULL


INSERT INTO dbo.BdMasterBudgets
SELECT
 [Id]
,[Code]
,[BuId]
,[Description]
,[OwnerId]
,[Capital]
,[BudgetAmount]
,[Status]
,[Remark]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[Year]
FROM #BdMasterBudgets a
WHERE not exists (select * from dbo.BdMasterBudgets where id=a.id)




--delete from dbo.BdMasterBudgets


