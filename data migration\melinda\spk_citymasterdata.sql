select newid()  as spk_NexBPMCode,spk_BPMCode,spk_Name,spk_citynumber ,flg 
into #spk_citymasterdata 
from spk_citymasterdata_Tmp


IF OBJECT_ID(N'dbo.spk_citymasterdata', N'U') IS NOT NULL
BEGIN
	update a 
	set a.spk_BPMCode    = b.spk_BPMCode
       ,a.spk_Name       = b.spk_Name
       ,a.spk_citynumber = b.spk_citynumber
       ,a.flg            = b.flg 
    from dbo.spk_citymasterdata a
    join #spk_citymasterdata b on a.spk_BPMCode = b.spk_BPMCode
    
    insert into dbo.spk_citymasterdata
    select a.spk_NexBPMCode
           ,a.spk_BPMCode
           ,a.spk_Name
           ,a.spk_citynumber
           ,a.flg 
	from #spk_citymasterdata a
	where NOT EXISTS (SELECT * FROM dbo.spk_citymasterdata where spk_BPMCode = a.spk_BPMCode)
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into dbo.spk_citymasterdata from #spk_citymasterdata
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END