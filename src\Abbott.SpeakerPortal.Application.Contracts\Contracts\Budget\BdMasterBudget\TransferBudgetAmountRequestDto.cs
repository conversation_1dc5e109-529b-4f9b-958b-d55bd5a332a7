﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Abbott.SpeakerPortal.Contracts.Budget
{
    public class TransferBudgetAmountRequestDto
    {
        /// <summary>
        /// 分摊原预算Id
        /// </summary>
        [Required]
        public Guid OriginalId { get; set; }
        /// <summary>
        /// 调拨到预算Id
        /// </summary>
        [Required]
        public Guid TransferId { get; set; }
        /// <summary>
        /// 调拨金额
        /// </summary>
        [Required]
        public decimal TransferAmount { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
    }
}
