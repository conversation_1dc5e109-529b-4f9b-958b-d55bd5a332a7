-- Speaker_<PERSON>_Dev.dbo.AgentHistory definition

-- Drop table

-- DROP TABLE Speaker_Portal_Dev.dbo.AgentHistory;

CREATE TABLE Speaker_Portal_Dev.dbo.AgentHistory (
	Id uniqueidentifier NOT NULL,
	AgentConfigId uniqueidentifier NOT NULL,
	WorkflowType int NULL,
	AgentTime datetime2 NOT NULL,
	FormId uniqueidentifier NOT NULL,
	FormCode nvarchar(20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	ExtraProperties nvarchar(MAX) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	ConcurrencyStamp nvarchar(40) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	CreationTime datetime2 NOT NULL,
	CreatorId uniqueidentifier NULL,
	LastModificationTime datetime2 NULL,
	LastModifierId uniqueidentifier NULL,
	IsDeleted bit DEFAULT CONVERT([bit],(0)) NOT NULL,
	DeleterId uniqueidentifier NULL,
	DeletionTime datetime2 NULL,
	CONSTRAINT PK_AgentHistory PRIMARY KEY (Id)
);
 CREATE NONCLUSTERED INDEX IX_AgentHistory_AgentConfigId ON dbo.AgentHistory (  AgentConfigId ASC  )  
	 WITH (  PAD_INDEX = OFF ,FILLFACTOR = 100  ,SORT_IN_TEMPDB = OFF , IGNORE_DUP_KEY = OFF , STATISTICS_NORECOMPUTE = OFF , ONLINE = OFF , ALLOW_ROW_LOCKS = ON , ALLOW_PAGE_LOCKS = ON  )
	 ON [PRIMARY ] ;
 CREATE NONCLUSTERED INDEX IX_AgentHistory_FormCode_FormId ON dbo.AgentHistory (  FormCode ASC  , FormId ASC  )  
	 WITH (  PAD_INDEX = OFF ,FILLFACTOR = 100  ,SORT_IN_TEMPDB = OFF , IGNORE_DUP_KEY = OFF , STATISTICS_NORECOMPUTE = OFF , ONLINE = OFF , ALLOW_ROW_LOCKS = ON , ALLOW_PAGE_LOCKS = ON  )
	 ON [PRIMARY ] ;


-- Speaker_Portal_Dev.dbo.AgentHistory foreign keys

ALTER TABLE Speaker_Portal_Dev.dbo.AgentHistory ADD CONSTRAINT FK_AgentHistory_AgentConfig_AgentConfigId FOREIGN KEY (AgentConfigId) REFERENCES Speaker_Portal_Dev.dbo.AgentConfig(Id) ON DELETE CASCADE;