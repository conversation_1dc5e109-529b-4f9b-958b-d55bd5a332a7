CREATE PROCEDURE dbo.sp_VendorApplications_ns
AS 
BEGIN
select 
vat.[Id]
,vat.[ApplicationType]
,vat.[ApplicationCode]
,vat.[VendorCode]
,vat.[OpenId]
,vat.[UnionId]
,vat.[HandPhone]
,vat.[VendorType]
,vat.[Status]
,ss.spk_NexBPMCode  as [ApplyUserId]
,sod.spk_NexBPMCode  as [ApplyUserBu]
,vat.[ApplyTime]
,vat.[VerificationStatus]
,vat.[BpcsId]
,vat.[EpdId]
,vat.[MndId]
,vat.[VendorId]
,vat.[ApsPorperty]
,vat.[LastVerifyStartTime]
,vat.[LastVerifyEndTime]
,vat.[CertificateCode]
,vat.[SPLevel]
,vat.[AcademicLevel]
,vat.[AcademicPosition]
,vat.[BankCode]
,vat.[BankCardNo]
,vat.[BankCity]
,vat.[BankNo]
,vat.[ExtraProperties]
,vat.[ConcurrencyStamp]
,vat.[CreationTime]
,ss1.spk_NexBPMCode as  [CreatorId]
,vat.[LastModificationTime]
,vat.[LastModifierId]
,vat.[IsDeleted]
,vat.[DeleterId]
,vat.[DeletionTime]
,vat.[BAuth]
,vat.[UserId]
,vat.[PTId]
,vat.[StandardHosDepId]
,vat.[HospitalId]
,vat.[HosDepartment]
,vat.[BankCardImg]
,vat.[AttachmentInformation]
,vat.[BImproved]
,vat.[DraftVersion]
,vat.[SignedStatus]
,vat.[SignedVersion]
,vat.[DPSCheck]
,vat.[HospitalName]
,vat.[PTName]
,vat.[StandardHosDepName]
,vat.[EpdHospitalId]
,vat.[MobileEncrypt]
,vat.[UpdatePreJson]
,vat.[PushVeevaResp]
,vat.[BankSwiftCode]
,vat.[IsAcademician]
,vat.[TransfereeId]
,vat.[TransfereeName]
,vat.[FormerBPMAcademicPosition]
,vat.[HCPType]
,vat.[RelationType]
,vat.[ApplyBuId]
,vat.[ApplyBuName]
,vat.[ApplyDeptName]
,vat.[ApplyUserBuName]
,vat.[ApplyUserName]
into #VendorApplications
from VendorApplications_Tmp vat 
left join spk_staffmasterdata ss 
on vat.ApplyUserId COLLATE SQL_Latin1_General_CP1_CI_AS =ss.bpm_id 
left join spk_organizationalmasterData sod 
on vat.ApplyUserBu COLLATE SQL_Latin1_General_CP1_CI_AS = sod.spk_BPMCode 
left join spk_staffmasterdata ss1
on vat.CreatorId  COLLATE SQL_Latin1_General_CP1_CI_AS =ss1.bpm_id ;

WITH SplitAtta AS (
    SELECT 
        A.id AS A_id, -- 假设A表有id字段
        TRIM(value) AS AttachmentInformation
    FROM #VendorApplications a
    CROSS APPLY STRING_SPLIT(A.AttachmentInformation, ',')
),
File_id as (
	SELECT 
	    A_id,
	    B.id as B_id,
	    B.BPMId
	FROM 
	    SplitAtta
	JOIN PLATFORM_ABBOTT.dbo.Attachments_tmp B 
	ON SplitAtta.AttachmentInformation COLLATE SQL_Latin1_General_CP1_CI_As= B.BPMId
)
select A_id,
STRING_AGG(cast(B_id as nvarchar(1000)), ',') WITHIN GROUP (ORDER BY B_id) AS B_id
into #AttachmentInformation
from File_id
group by A_id

update a set a.AttachmentInformation=b.B_id,a.DPSCheck=b.B_id from #VendorApplications a
join #AttachmentInformation b
on a.id=b.A_id
PRINT(N'update AttachmentInformation'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))

 IF OBJECT_ID(N'PLATFORM_ABBOTT.dbo.VendorApplications ', N'U') IS NOT NULL
	BEGIN
		drop table PLATFORM_ABBOTT.dbo.VendorApplications
		select *
        into PLATFORM_ABBOTT.dbo.VendorApplications from #VendorApplications
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into PLATFORM_ABBOTT.dbo.VendorApplications from #VendorApplications
    -- select * from #vendor_tbl
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
END;