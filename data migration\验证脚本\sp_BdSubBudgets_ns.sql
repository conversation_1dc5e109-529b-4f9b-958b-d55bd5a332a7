CREATE PROCEDURE dbo.sp_BdSubBudgets_ns
AS 
BEGIN
	select 
a.[Id],
a.[Code],
UPPER(bmbt.Id) as  [MasterBudgetId],
UPPER(sc.spk_NexBPMCode) as [CostCenterId],
UPPER(e.spk_NexBPMCode) as [RegionId],
UPPER(soc.spk_NexBPMCode) as [BuId],
UPPER(ss.spk_NexBPMCode) as [OwnerId],
a.[UesdAmount],
a.[AvailableAmount],
cast(a.[AttachmentFile] as nvarchar(4000)) as [AttachmentFile],
a.[ExtraProperties],
a.[ConcurrencyStamp],
a.[CreationTime],
UPPER(ss1.spk_NexBPMCode) as [CreatorId],
a.[LastModificationTime],
UPPER(ss2.spk_NexBPMCode) as [LastModifierId],
a.[IsDeleted],
a.[DeleterId],
a.[DeletionTime],
a.[Description],
a.[Remark],
a.[Status],
a.[IsComplicanceAudits],
a.[Bu2],
a.[LMMs],
a.[Owner2],
a.[ProductManagers],
a.[RegionManagers],
a.[RegionalAssistants],
a.[BudgetAmount]
into #BdSubBudgets
from PLATFORM_ABBOTT.dbo.BdSubBudgets_tmp a
left join PLATFORM_ABBOTT.dbo.BdMasterBudgets_tmp bmbt 
on a.MasterBudgetId =bmbt.Code 
left join PLATFORM_ABBOTT.dbo.spk_costcentermasterdata sc
on a.CostCenterId = sc.spk_BPMCode 
left join PLATFORM_ABBOTT.dbo.spk_districtmasterdata e
on a.RegionId  =e.spk_BPMCode 
left join PLATFORM_ABBOTT.dbo.spk_organizationalmasterdata soc 
on a.BuId =soc.spk_BPMCode 
left join PLATFORM_ABBOTT.dbo.spk_staffmasterdata ss 
on a.OwnerId =ss.bpm_id 
left join PLATFORM_ABBOTT.dbo.spk_staffmasterdata ss1 
on a.CreatorId =ss1.bpm_id 
left join PLATFORM_ABBOTT.dbo.spk_staffmasterdata ss2 
on a.LastModifierId=ss2.bpm_id 

UPDATE a set a.AttachmentFile=b.AttachmentFile 
from #BdSubBudgets a
left join (select STRING_AGG(cast(b.Id as varchar(36)) ,',') as AttachmentFile,a.id
			from PLATFORM_ABBOTT.dbo.BdSubBudgets_tmp a
			left join Attachments b on a.AttachmentFile =b.BPMId
			GROUP by b.id,a.id) b 
		on a.id=b.id

    --删除表
    IF OBJECT_ID(N'PLATFORM_ABBOTT.dbo.BdSubBudgets ', N'U') IS NOT NULL
	BEGIN
		drop table PLATFORM_ABBOTT.dbo.BdSubBudgets
		select *
        into PLATFORM_ABBOTT.dbo.BdSubBudgets from #BdSubBudgets
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into PLATFORM_ABBOTT.dbo.BdSubBudgets from #BdSubBudgets
    -- select * from #vendor_tbl
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END

END;