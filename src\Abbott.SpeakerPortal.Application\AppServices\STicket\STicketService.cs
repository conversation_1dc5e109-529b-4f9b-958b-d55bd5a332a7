﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using Json = System.Text.Json;
using System.Threading.Tasks;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.Common;
using Abbott.SpeakerPortal.Contracts.STicket;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.Budget;
using Abbott.SpeakerPortal.Entities.EFlow.STicket;
using Abbott.SpeakerPortal.Entities.Integration.Dspot;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Enums;
using EFCore.BulkExtensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Guids;
using Volo.Abp.Identity;
using Abbott.SpeakerPortal.Extension;
using Volo.Abp.ObjectMapping;
using Abbott.SpeakerPortal.Consts;
using static Abbott.SpeakerPortal.Enums.DataverseEnums.Organization;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Contracts.Dataverse;
using static Abbott.SpeakerPortal.Enums.DataverseEnums;
using static Abbott.SpeakerPortal.Enums.Purchase;
using Abbott.SpeakerPortal.Contracts.Common.Attachment;
using DocumentFormat.OpenXml.Drawing.Spreadsheet;
using Volo.Abp.Data;
using Senparc.Weixin.WxOpen.Entities;
using DocumentFormat.OpenXml.Bibliography;
using Abbott.SpeakerPortal.Contracts.Common.Approve;
using Abbott.SpeakerPortal.Contracts.Common.PowerApp;
using Abbott.SpeakerPortal.Utils;
using Newtonsoft.Json;
using static Abbott.SpeakerPortal.Consts.EntitiesNameConsts;
using Abbott.SpeakerPortal.AppServices.Common;
using Flurl.Http;
using Abbott.SpeakerPortal.Person;
using static Abbott.SpeakerPortal.Permissions.SpeakerPortalPermissions;
using Abbott.SpeakerPortal.Contracts.Budget.Subbudget;
using Abbott.SpeakerPortal.Entities.Budget.BdExpense;
using DocumentFormat.OpenXml.Office2010.Excel;
using DocumentFormat.OpenXml.Wordprocessing;
using System.Security.Cryptography.X509Certificates;
using Senparc.Weixin.MP.AdvancedAPIs.MerChant;
using System.Collections;
using Hangfire.Logging;
using PdfSharp;
using DocumentFormat.OpenXml.Drawing.Charts;
using Microsoft.VisualBasic;
using Senparc.CO2NET.Helpers.Serializers;
using Microsoft.AspNetCore.Http;
using System.IO;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.FileProviders;
using Abbott.SpeakerPortal.Contracts.Common.BPMEmail;
using Hangfire;
using Abbott.SpeakerPortal.BackgroundWorkers.BPMEmail;
using Abbott.SpeakerPortal.IntermediateDB.Repositories;
using Abbott.SpeakerPortal.Contracts.Budget.FocSubbudget;
using Abbott.SpeakerPortal.Contracts.FOC;
using Microsoft.AspNetCore.Http.Extensions;
using Abbott.SpeakerPortal.Contracts.Integration;

namespace Abbott.SpeakerPortal.AppServices.STicket
{
    public partial class STicketService : SpeakerPortalAppService, ISTicketService
    {
        private readonly ILogger<STicketService> _logger;
        private readonly IConfiguration _configuration;
        private readonly ICommonService _commonService;
        private readonly IServiceProvider _serviceProvider;
        private IHttpRequestService _httpRequestService;

        private readonly string _apiHost;
        private readonly string _apiToken;
        private readonly string _apiEntrance;
        private readonly string _userName;
        private readonly string _password;
        private readonly string _provider;
        private readonly bool _refresh;

        public STicketService(IServiceProvider serviceProvider)
        {
            _logger = serviceProvider.GetService<ILogger<STicketService>>();
            _configuration = serviceProvider.GetService<IConfiguration>();
            _commonService = serviceProvider.GetService<ICommonService>();
            _serviceProvider = serviceProvider;
            _apiHost = _configuration["Integrations:Veeva:ApiUrlHost"];
            _apiToken = _configuration["Integrations:Veeva:ApiUrlToken"];
            _apiEntrance = _configuration["Integrations:Veeva:ApiUrlEntrance"];
            _userName = _configuration["Integrations:Veeva:UserName"];
            _password = _configuration["Integrations:Veeva:Password"];
            _provider = _configuration["Integrations:Veeva:Provider"];
            _refresh = _configuration.GetValue("Integrations:Veeva:Refresh", false);
            _httpRequestService = serviceProvider.GetService<IHttpRequestService>();
        }
        public async Task<string> SyncStoreInfos()
        {
            var log = new SetOperationLogRequestDto { System = "MDM", Api = "SyncStoreInfos" };
            try
            {
                var result = await GetMdmTokenAsync();
                if (result?.State != MdmTokenResponseDto.MdmTokenResponseState.Success)
                    return result?.Message;

                //请求接口
                bool hasMoreData = true;
                var request = new MDMRequestDto { page_num = 1, page_size = 200 };
                request.query_starttime = DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd") + " 00:00:00";
                request.query_endtime = DateTime.Now.ToStringFormat();

                var storeQuery = await LazyServiceProvider.LazyGetService<IStoreInfoRepository>().GetQueryableAsync();
                //默认查询前一天到当前时间, 第一次执行全量查询
                if (storeQuery.Count() == 0)
                    request.query_starttime = "1900-01-01 00:00:00";

                var url = _configuration["Integrations:MDM:BaseUrl"]!.TrimEnd('/') + $"/kong/mdm/api/customer/V1.0.0";
                var subscriptionKey = _configuration["Integrations:MDM:SubscriptionKey"];
                var apiKey = _configuration["Integrations:MDM:ApiKey"];
                var headers = new Dictionary<string, string>
                {
                    { "Ocp-Apim-Subscription-Key", subscriptionKey },
                    { "apiKey", apiKey },
                    { "X-Token", result.Data.Token}
                };

                while (hasMoreData)
                {
                    log = _commonService.InitOperationLog("MDM", "SyncStoreInfos", url + "|" + JsonConvert.SerializeObject(request) + "|" + JsonConvert.SerializeObject(headers));
                    //发送POST请求
                    var response = await url.WithHeaders(headers).PostJsonAsync(request);
                    string responseData = await response.GetStringAsync();
                    //序列化返回结果
                    var resObj = JsonConvert.DeserializeObject<StoreResponseDto>(responseData);

                    _commonService.LogResponse(log, JsonConvert.SerializeObject(resObj));
                    //处理接口返回结果
                    await ProcessStoresResult(resObj.Data);

                    //检查是否再次调用接口，如果返回数据等于pageSize数量，判断还有数据需查询则再次调用接口
                    hasMoreData = resObj.Data.Count == request.page_size;
                    request.page_num++;
                }
                return "success";
            }
            catch (Exception ex)
            {
                _commonService.LogResponse(log, ex.ToString(), false);
                Logger.LogError(ex, ex.Message);

                throw;
            }
        }

        public async Task<string> SyncClientInfos()
        {
            var queryRcm = await LazyServiceProvider.GetService<IIntermediateRcmRepository>().GetQueryableAsync();
            var rcmList = queryRcm.Select(a => new ClientDataResponseDto
            {
                CustomerCode = a.Ccust.ToString(),
                CompanyCode = a.Ccmpy.ToString(),
                FullCustomerCode = a.Ccmpy.ToString() + "-" + a.Ccust.ToString(),
                CustomerName = a.Cnme.ToString(),
                CustomerAddress = a.Cad1.ToString() + a.Cad2.ToString() + a.Cad3.ToString(),
            }).ToList();

            //处理接口返回结果
            await ProcessClientsResult(rcmList);

            return "success";
        }

        public async Task<string> SyncProductDetails()
        {
            var log = new SetOperationLogRequestDto { System = "MDM", Api = "SyncProductDetails" };
            try
            {
                var result = await GetMdmTokenAsync();
                if (result?.State != MdmTokenResponseDto.MdmTokenResponseState.Success)
                    return result?.Message;

                //请求接口
                bool hasMoreData = true;
                var productQuery = await LazyServiceProvider.LazyGetService<IProductDetailsRepository>().GetQueryableAsync();
                var request = new MDMRequestDto { page_num = 1, page_size = 200 };
                request.query_starttime = DateTime.Now.AddDays(-1).ToString("yyyy-MM-dd") + " 00:00:00";
                request.query_endtime = DateTime.Now.ToStringFormat();
                //默认查询前一天到当前时间, 第一次执行全量查询
                if (productQuery.Count() == 0)
                    request.query_starttime = "1900-01-01 00:00:00";

                var url = _configuration["Integrations:MDM:BaseUrl"]!.TrimEnd('/') + $"/kong/mdm/api/product/V1.0.0";
                var subscriptionKey = _configuration["Integrations:MDM:SubscriptionKey"];
                var apiKey = _configuration["Integrations:MDM:ApiKey"];
                var headers = new Dictionary<string, string>
                {
                    { "Ocp-Apim-Subscription-Key", subscriptionKey },
                    { "apiKey", apiKey },
                    { "X-Token", result.Data.Token}
                };

                while (hasMoreData)
                {
                    log = _commonService.InitOperationLog("MDM", "SyncProductDetails", url + "|" + JsonConvert.SerializeObject(request) + "|" + JsonConvert.SerializeObject(headers));
                    //发送POST请求
                    var response = await url.WithHeaders(headers).PostJsonAsync(request);
                    string responseData = await response.GetStringAsync();
                    //序列化返回结果
                    var resObj = JsonConvert.DeserializeObject<ProductDataResponseDto>(responseData);

                    _commonService.LogResponse(log, JsonConvert.SerializeObject(resObj));
                    //处理接口返回结果
                    await ProcessProductsResult(resObj.Data);

                    //检查是否再次调用接口，如果返回数据等于pageSize数量，判断还有数据需查询则再次调用接口
                    hasMoreData = resObj.Data.Count == request.page_size;
                    request.page_num++;
                }

                return "success";
            }
            catch (Exception ex)
            {
                _commonService.LogResponse(log, ex.ToString(), false);
                Logger.LogError(ex, ex.Message);

                throw;
            }
        }

        private async Task<string> ProcessStoresResult(List<StoreData> storeDatas)
        {
            var customerIds = storeDatas.Where(a => !string.IsNullOrEmpty(a.cust_id)).Select(a => a.cust_id).ToArray();
            if (!customerIds.Any())
                return "No Store Data";

            var dictForAdd = new Dictionary<string, StoreInfo>();
            var hashsetForUpdate = new HashSet<StoreInfo>();
            var storeRepository = LazyServiceProvider.LazyGetService<IStoreInfoRepository>();
            var guidGenerator = LazyServiceProvider.LazyGetService<IGuidGenerator>();
            var existStoreList = await storeRepository.GetListAsync(a => customerIds.Contains(a.CustomerId));
            var dictExistStore = existStoreList.GroupBy(a => new { a.DataSource, a.CustomerId }).ToDictionary(a => $"{a.Key.DataSource}_{a.Key.CustomerId}", a => a.Last());
            foreach (var item in storeDatas.Where(a => a.is_current == 1))
            {
                var key = $"{item.data_src}_{item.cust_id}";
                dictExistStore.TryGetValue(key, out StoreInfo store);
                //存在数据则修改
                if (store != null)
                {
                    store.SetModification();
                    hashsetForUpdate.Add(store);
                }
                else//新增
                {
                    store = new StoreInfo();
                    store.SetId(guidGenerator.Create());
                    store.SetCreation();
                    dictForAdd[key] = store;
                }

                store.DataSource = item.data_src;
                store.StoreCode = item.cust_str1;
                store.CustomerId = item.cust_id;
                store.StoreName = item.cust_name_cn;
                store.ProvinceName = item.prvn_name;
                store.CityName = item.city_name;
                store.ParentCustomerId = item.headquarter_code;
                store.BusinessType = item.business_type_name;
                store.Type = item.cust_istarget;
                store.ChannelCN = item.channel;
                store.ChannelEN = item.channel_en;
                store.SubChannelCN = item.sub_channel;
                store.SubChannelEN = item.sub_channel_en;
                store.IsActive = string.Equals(item.use_flag, "Y", StringComparison.CurrentCultureIgnoreCase);
            }

            var context = await storeRepository.GetDbContextAsync();
            if (dictForAdd.Count > 0)
                await context.BulkInsertAsync(dictForAdd.Select(a => a.Value).ToArray());
            if (hashsetForUpdate.Count > 0)
                await context.BulkUpdateAsync(hashsetForUpdate);

            return string.Empty;
        }

        private async Task<string> ProcessClientsResult(List<ClientDataResponseDto> clientDatas)
        {
            var log = new SetOperationLogRequestDto();
            try
            {
                var customerCodes = clientDatas.Where(a => !string.IsNullOrEmpty(a.FullCustomerCode)).Select(a => a.FullCustomerCode).ToArray();
                if (!customerCodes.Any())
                    return "No Client Data";

                var dictForAdd = new Dictionary<string, ClientInfo>();
                var hashsetForUpdate = new HashSet<ClientInfo>();
                var storeRepository = LazyServiceProvider.LazyGetService<IClientInfoRepository>();
                var guidGenerator = LazyServiceProvider.LazyGetService<IGuidGenerator>();
                var existClientList = await storeRepository.GetListAsync(a => customerCodes.Contains(a.FullCustomerCode));
                var dictExistClient = existClientList.GroupBy(a => a.FullCustomerCode).ToDictionary(a => a.Key, a => a.Last());

                foreach (var item in clientDatas)
                {
                    dictExistClient.TryGetValue(item.FullCustomerCode, out ClientInfo client);
                    //存在数据则修改
                    if (client != null)
                    {
                        client.SetModification();
                        hashsetForUpdate.Add(client);
                    }
                    else//新增
                    {
                        client = new ClientInfo();
                        client.SetId(guidGenerator.Create());
                        client.IsActive = true;
                        client.SetCreation();
                        dictForAdd[item.FullCustomerCode] = client;
                    }

                    client.CompanyCode = item.CompanyCode;
                    client.CustomerCode = item.CustomerCode;
                    client.FullCustomerCode = item.FullCustomerCode;
                    client.CustomerName = item.CustomerName;
                    client.CustomerAddress = item.CustomerAddress;
                }

                log = _commonService.InitOperationLog("EFlow", "SyncClientInfos", "新增数量:" + dictForAdd.Count + ";修改数量:" + hashsetForUpdate.Count);

                var context = await storeRepository.GetDbContextAsync();
                if (dictForAdd.Count > 0)
                    await context.BulkInsertAsync(dictForAdd.Select(a => a.Value).ToArray());
                if (hashsetForUpdate.Count > 0)
                    await context.BulkUpdateAsync(hashsetForUpdate);

                _commonService.LogResponse(log, "Sync success");
            }
            catch (Exception ex)
            {
                _commonService.LogResponse(log, ex.ToString(), false);
                throw;
            }
            return "success";
        }

        private async Task<string> ProcessProductsResult(List<ProductData> productDatas)
        {
            var productCodes = productDatas.Where(a => !string.IsNullOrEmpty(a.bpcs_code)).Select(a => a.bpcs_code).ToArray();
            if (!productCodes.Any())
                return "No Product Data";

            var dictForAdd = new Dictionary<string, ProductDetail>();
            var hashsetForUpdate = new List<ProductDetail>();
            var productRepository = LazyServiceProvider.LazyGetService<IProductDetailsRepository>();
            var guidGenerator = LazyServiceProvider.LazyGetService<IGuidGenerator>();
            var existProductList = await productRepository.GetListAsync(a => productCodes.Contains(a.ProductSCode));
            var dictExistProduct = existProductList.GroupBy(a => a.ProductSCode).ToDictionary(a => a.Key, a => a.Last());

            foreach (var item in productDatas.Where(a => a.is_current == 1))
            {
                dictExistProduct.TryGetValue(item.bpcs_code, out ProductDetail product);
                //存在数据则修改
                if (product != null)
                {
                    product.SetModification();
                    hashsetForUpdate.Add(product);
                }
                else//新增
                {
                    product = new ProductDetail();
                    product.SetId(guidGenerator.Create());
                    product.SetCreation();
                    dictForAdd[item.bpcs_code] = product;
                }

                product.ProductSCode = item.bpcs_code;
                product.ProductNameCN = item.bpcs_name_cn;
                product.ProductNameEN = item.bpcs_name_en;
                product.ProductMCode = item.product_brand_code;
                product.ProductShortNameEN = item.product_short_name;
                product.BrandCN = item.brand_cn;
                product.BrandEN = item.brand_en;
                product.SubBrandCN = item.sub_brand_cn;
                product.SubBrandEN = item.sub_brand_en;
                product.Taste = item.taste;
                product.Unit = item.unit;
                product.MultiPack = item.multi_pack;
                product.SalesPackageSize = item.sales_product_package_size;
                product.ProductBrandGroup = item.pdt_brand;
                product.ProductLine = item.pdt_line;
                product.IsActive = string.Equals(item.use_flag, "Y", StringComparison.CurrentCultureIgnoreCase);
                product.Expr = item.e_expr;
                product.UseFlagM = string.Equals(item.use_flag, "Y", StringComparison.CurrentCultureIgnoreCase);
                product.UpdateTime = Convert.ToDateTime(item.upt_time);
                product.ShortName = item.product_short_name;
                product.UpdateTimeM = Convert.ToDateTime(item.upt_time_m);
                product.MBrandCN = item.brand_cn;
                product.MBrandEN = item.brand_en;
            }

            var context = await productRepository.GetDbContextAsync();
            if (dictForAdd.Count > 0)
                await context.BulkInsertAsync(dictForAdd.Select(a => a.Value).ToArray());
            if (hashsetForUpdate.Count > 0)
                await context.BulkUpdateAsync(hashsetForUpdate);

            return string.Empty;
        }

        /// <summary>
        /// 获取子预算列表
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetSubBudgetsResponse>> GetSubBudgetListAsync(GetSubBudgetsRequest request)
        {
            //获取当前机构
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            IEnumerable<DepartmentDto> orgs = await dataverseService.GetOrganizations(request.OrgId.ToString());
            if (!orgs.Any())
                return default;

            var org = orgs.First();
            //获取当前机构的上级BU
            orgs = await LazyServiceProvider.LazyGetService<ICommonService>().GetParentOrgs(org);
            var bu = orgs.FirstOrDefault(a => a.OrganizationType == DataverseEnums.Organization.OrganizationType.Bu);
            if (bu == null)
                return default;

            var queryUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var querySubBudget = await LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>().GetQueryableAsync();
            if (CurrentUser.Id.HasValue)
            {
                //如果大区是全国、或者为空就可以用所有的预算，否则只能用他所属的那个大区的预算
                var staff = await dataverseService.GetStaffs(CurrentUser.Id.ToString());
                if (staff.Any() && staff.First().DistrictId.HasValue && staff.First().DistrictName != "全国")
                    querySubBudget = querySubBudget.Where(a => a.RegionId == staff.First().DistrictId);
            }

            var query = querySubBudget.Where(a => a.BuId == bu.Id && a.CostCenterId == org.CostcenterId && true && a.Status).Include(a => a.MasterBudget)
                .Include(s => s.MonthlyBudgets.Where(s => s.Status))
                .GroupJoin(queryUser, a => a.OwnerId, a => a.Id, (a, b) => new { SubBudget = a, Users = b })
                .SelectMany(a => a.Users.DefaultIfEmpty(), (a, b) => new { a.SubBudget, User = b })
                .WhereIf(!string.IsNullOrEmpty(request.Code), a => a.SubBudget.Code.Contains(request.Code))
                .WhereIf(!string.IsNullOrEmpty(request.Description), a => a.SubBudget.Description.Contains(request.Description))
                .WhereIf(!string.IsNullOrEmpty(request.Owner), a => a.User.Name.Contains(request.Owner))
                .WhereIf(request.BusinessUnitId.HasValue, a => a.SubBudget.BuId == request.BusinessUnitId)
                .OrderByDescending(s => s.SubBudget.CreationTime)
                .Select(a => new GetSubBudgetsResponse
                {
                    Id = a.SubBudget.Id,
                    BudgetCode = a.SubBudget.Code,
                    Description = a.SubBudget.Description,
                    OrgName = bu.DepartmentName,
                    Owner = a.User.Name,
                    TotalAmount = a.SubBudget.BudgetAmount,
                    UsedAmount = a.SubBudget.UesdAmount,
                    AvailableAmount = a.SubBudget.GetAvailableAmount(),
                    Capital = a.SubBudget.MasterBudget.Capital
                });

            var count = query.Count();
            var datas = query.Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToArray();

            return new PagedResultDto<GetSubBudgetsResponse>(count, datas);
        }

        /// <summary>
        /// 获取核销申请单详情
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns></returns>
        public async Task<GetSTicketApplicationResponse> GetSTicketApplicationAsync(Guid id)
        {
            var querySTicket = await LazyServiceProvider.LazyGetService<ISTicketApplicationRepository>().GetQueryableAsync();
            var querySTicketDetail = await LazyServiceProvider.LazyGetService<ISTicketApplicationDetailRepository>().GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var usdCurrencies = await dataverseService.GetCurrencyConfig(DictionaryType.CurrencyItems.USD);

            var sticketApplication = querySTicket.Where(a => a.Id == id).FirstOrDefault();
            if (sticketApplication == null)
            {
                return default;
            }
            var sticketApplicationDetail = querySTicketDetail.Where(a => a.ParentID == id);
            var detailItemMaps = sticketApplicationDetail.GroupJoin(sticketApplicationDetail, a => a.Id, b => b.HedgeDetailId, (detail, heges) => new { detail, heges })
                .SelectMany(s => s.heges.DefaultIfEmpty(), (a, hege) => new CreateUpdateSTicketApplicationDetailRequest
                {
                    Id = a.detail.Id,
                    RowId = a.detail.RowId,
                    HedgeDetailId = a.detail.HedgeDetailId,
                    IsHedge = a.detail.IsHedge,
                    StoreId = a.detail.StoreId,
                    StoreName = a.detail.StoreName,
                    StoreChannel = a.detail.StoreChannel,
                    StoreSubChannel = a.detail.StoreSubChannel,
                    ExpenseNatureId = a.detail.ExpenseNatureId,
                    ExpenseNatureCode = a.detail.ExpenseNatureCode,
                    ExpenseNature = a.detail.ExpenseNature,
                    ExpenseNatureDesc = a.detail.ExpenseNatureDesc,
                    Quantity = a.detail.Quantity,
                    Price = a.detail.Price,
                    SubTotalAmountRMB = a.detail.SubTotalAmountRMB,
                    SubVerifiedAmountRMB = a.detail.SubVerifiedAmountRMB == null ? 0 : a.detail.SubVerifiedAmountRMB,
                    SettlementAmount = a.detail.SettlementAmount == null ? 0 : a.detail.SettlementAmount,
                    SettlementRegion = a.detail.SettlementRegion,
                    Description = a.detail.Description,
                    SettlementPeriodStart = a.detail.SettlementPeriodStart,
                    SettlementPeriodEnd = a.detail.SettlementPeriodEnd,
                    CityId = a.detail.CityId,
                    CityCode = a.detail.CityCode,
                    CityName = a.detail.CityName,
                    PredictDate = a.detail.PredictDate,
                    SettlementEntityId = a.detail.SettlementEntityId,
                    SettlementEntityCode = a.detail.SettlementEntityCode,
                    SettlementEntityName = a.detail.SettlementEntityName,
                    SettlementEntityType = a.detail.SettlementEntityType,
                    SettlementEntityChannel = a.detail.SettlementEntityChannel,
                    SettlementEntityHQCode = a.detail.SettlementEntityHQCode,
                    SettlementEntityHQName = a.detail.SettlementEntityHQName,
                    SubVerificationStatus = a.detail.SubVerificationStatus,
                    CutOffStatus = a.detail.CutOffStatus,
                    Remark = a.detail.Remark,
                    IsEdit = hege == null
                }).OrderBy(o => o.RowId).ToList();
            var response = ObjectMapper.Map<STicketApplication, GetSTicketApplicationResponse>(sticketApplication);
            if (detailItemMaps.Count > 0)
            {
                //var detailItemMaps = ObjectMapper.Map<List<STicketApplicationDetail>, List<CreateUpdateSTicketApplicationDetailRequest>>(sticketApplicationDetail);
                response.DetailItems = detailItemMaps;
            }
            //附件
            if (!string.IsNullOrEmpty(sticketApplication.Attachment))
            {
                var attachmentIds = sticketApplication.Attachment.Split(",", StringSplitOptions.RemoveEmptyEntries).Select(Guid.Parse).ToArray();
                response.Attachment = await LazyServiceProvider.LazyGetService<IAttachmentService>().GetAttachmentsAsync(attachmentIds);
            }
            //预算
            if (sticketApplication.SubBudgetId.HasValue)
            {
                var queryBudget = await LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>().GetQueryableAsync();
                var budget = await queryBudget.Include(a => a.MonthlyBudgets).Select(a => new
                {
                    a.Id,
                    a.Code,
                    a.RegionId,
                    a.AttachmentFile,
                    a.OwnerId,
                    a.BuId,
                    a.CostCenterId,
                    a.BudgetAmount,
                    a.UesdAmount,
                    a.MonthlyBudgets,
                    a.Description
                })
                .FirstOrDefaultAsync(a => a.Id == sticketApplication.SubBudgetId);
                if (budget != null)
                {
                    //子预算信息
                    response.SubBudgetDescription = budget.Description;
                    response.BudgetAmount = budget.BudgetAmount;
                    response.BudgetUsedAmount = budget.UesdAmount;
                    response.BudgetAvailableAmount = budget.MonthlyBudgets.Where(a => a.Status).Sum(a => a.BudgetAmount) - budget.UesdAmount;

                    //BU
                    //var dataverseService = _serviceProvider.GetService<IDataverseService>();
                    var orgs = (await dataverseService.GetOrganizations()).Where(x => x.OrganizationType == OrganizationType.Bu);
                    var bu = orgs.FirstOrDefault(a => a.Id == budget.BuId);
                    if (bu != null)
                    {
                        response.SubBudgetBuName = bu.DepartmentName;
                    }
                    //成本中心
                    var costcenters = await dataverseService.GetCostcentersAsync(budget.CostCenterId.ToString());
                    var costcenter = costcenters.FirstOrDefault();
                    if (costcenter != null)
                    {
                        response.SubBudgeCostCenter = costcenter.Name;
                    }
                }
                //预算附件
                if (!string.IsNullOrEmpty(budget.AttachmentFile))
                {
                    var attachmentIds = budget.AttachmentFile.Split(",", StringSplitOptions.RemoveEmptyEntries).Select(Guid.Parse).ToArray();
                    response.BudgeAttachment = await LazyServiceProvider.LazyGetService<IAttachmentService>().GetAttachmentsAsync(attachmentIds);
                }
                response.BudgetFile = budget.AttachmentFile;

                //检查申请单金额
                if (response.DetailItems != null)
                {
                    var usdCurrency = usdCurrencies.First();
                    var focRemindAmt = (await dataverseService.GetDictionariesAsync(DictionaryType.FOCRemindAmount)).FirstOrDefault();
                    if (usdCurrency != null && focRemindAmt != null)
                    {
                        var subTotalAmount = response.DetailItems.Where(a => a.SubTotalAmountRMB.HasValue).Sum(a => a.SubTotalAmountRMB).Value;
                        decimal sumAmount = decimal.Round(subTotalAmount / (decimal)usdCurrency.PlanRate, 4);
                        var remindAmt = Convert.ToDecimal(focRemindAmt.Name);
                        if (sumAmount >= remindAmt)
                        {
                            response.IsExceedAmount = true;
                        }
                    }
                }
            }
            return response;
        }

        /// <summary>
        /// 获取核销单消费大类列表
        /// </summary>
        /// <returns></returns>
        public async Task<IEnumerable<ConsumeCategoryDto>> GetSTicketConsumeCategoryByTypeAsync(ConsumeCategory.FlowTypes flowType)
        {
            var _dataverseService = _serviceProvider.GetService<IDataverseService>();
            var consumeCategories = await _dataverseService.GetConsumeCategoryAsync();
            var consumeCategoriesList = consumeCategories.ToList();
            //根据流程类型过滤消费大类
            var sticketConsumeCategories = consumeCategories.Where(a => a.FlowType == flowType);
            return sticketConsumeCategories;
        }

        /// <summary>
        /// 根据消费大类获取费用性质
        /// </summary>
        /// <param name="consumeId">The consume identifier.</param>
        /// <param name="orgId">The org identifier.</param>
        /// <param name="costCenterId">The costcenter identifier.</param>
        /// <returns></returns>
        public async Task<IEnumerable<CostNatureDto>> GetCostNatureByConsumeAsync(Guid consumeId, Guid orgId, Guid costCenterId)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            //获取该消费大类下的费用性质
            var costNatures = await dataverseService.GetCostNatureAsync(consumeId.ToString());

            //获取组织所属BU
            var parents = await LazyServiceProvider.LazyGetService<ICommonService>().GetAllParentOrgs(orgId);
            var bu = parents.FirstOrDefault(a => a.OrganizationType == DataverseEnums.Organization.OrganizationType.Bu);
            if (bu == null)
                return Array.Empty<CostNatureDto>();

            //获取BU编码配置
            var buCodingCfgs = await dataverseService.GetBuCodingCfgAsync(bu.Id.ToString());
            if (!buCodingCfgs.Any())
                return Array.Empty<CostNatureDto>();

            //获取成本中心
            var costcenters = await dataverseService.GetCostcentersAsync(costCenterId.ToString());
            if (!costcenters.Any())
                return Array.Empty<CostNatureDto>();

            var buCode = buCodingCfgs.First().BuCode;
            var costcenterCode = costcenters.First().Code;
            //获取BU、成本中心、费用性质映射关系
            var coaMappingRules = await dataverseService.GetCoaMappingRuleAsync($"?{buCode}?{costcenterCode}?");
            if (!coaMappingRules.Any())
                return Array.Empty<CostNatureDto>();

            //获取BU与费用性质关系
            var orgCostNatureRelations = await dataverseService.GetOrgCostNatureRelationAsync();
            //coa与费用性质的交集
            var intersectCoas = coaMappingRules.Where(a => a.BuCode == buCode && a.CostcenterCode == costcenterCode)
                .Join(costNatures, a => a.CostNatureCode, a => a.Code, (a, b) => b);
            //获取匹配的费用性质和未设置特定BU的费用性质
            costNatures = intersectCoas.Join(orgCostNatureRelations.Where(a => a.OrgId == bu.Id), a => a.Id, a => a.CostNatureId, (a, b) => a);

            return costNatures;
        }

        /// <summary>
        /// 预算信息查询接口 - CSS使用
        /// </summary>
        /// <param name="employeeId"></param>
        /// <returns></returns>
        public async Task<MessageResult> GetSubBudgetInfoAsync(string employeeId)
        {
            var log = new SetOperationLogRequestDto();
            log = _commonService.InitOperationLog("CSS", "GetSubBudgetInfoAsync", $"employeeId={employeeId}");
            object returnNull = null;
            try
            {
                //获取用户信息
                var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
                var user = queryableUser.FirstOrDefault(a => a.UserName == employeeId);
                if (user != null)
                {
                    //获取当前机构
                    var userDeptId = user.GetProperty<Guid?>(EntityConsts.IdentityUser.MainDepartmentId);
                    var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
                    IEnumerable<DepartmentDto> orgs = await dataverseService.GetOrganizations(userDeptId.HasValue ? userDeptId.Value.ToString() : Guid.NewGuid().ToString());
                    if (!orgs.Any())
                        return MessageResult.FailureResult($"未找到{employeeId}的组织信息");

                    var org = orgs.First();
                    //获取当前机构的上级BU
                    orgs = await LazyServiceProvider.LazyGetService<ICommonService>().GetParentOrgs(org);
                    var bu = orgs.FirstOrDefault(a => a.OrganizationType == OrganizationType.Bu);
                    if (bu == null)
                        return MessageResult.FailureResult($"未找到{employeeId}的上级BU信息");

                    var querySubBudget = await LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>().GetQueryableAsync();

                    //如果大区是全国、或者为空就可以用所有的预算，否则只能用他所属的那个大区的预算
                    var staff = await dataverseService.GetStaffs(user.Id.ToString());
                    if (staff.Any() && staff.First().DistrictId.HasValue && staff.First().DistrictName != "全国")
                        querySubBudget = querySubBudget.Where(a => a.RegionId == staff.First().DistrictId);

                    var query = querySubBudget.Where(a => a.BuId == bu.Id && a.CostCenterId == org.CostcenterId && a.Status && a.IsDeleted == false)
                        .Include(a => a.MasterBudget).Where(a => a.MasterBudget.Status == true && a.IsDeleted == false)
                        .Include(s => s.MonthlyBudgets)
                        .GroupJoin(queryableUser, a => a.OwnerId, a => a.Id, (a, b) => new { SubBudget = a, Users = b })
                        .SelectMany(a => a.Users.DefaultIfEmpty(), (a, b) => new { a.SubBudget, User = b })
                        .OrderByDescending(s => s.SubBudget.CreationTime)
                    .Select(a => new CSSGetSubBudgetInfoResponse
                    {
                        BudgetId = a.SubBudget.Code,
                        BudgetDescription = a.SubBudget.Description,
                        BudgetOwnerName = a.User.Name,
                        BudgetAmount = a.SubBudget.BudgetAmount,
                        BudgetCanUseAmount = a.SubBudget.GetAvailableAmount()
                    });
                    //记录日志
                    _commonService.LogResponse(log, JsonConvert.SerializeObject(query.ToList()));
                    return MessageResult.SuccessResult(query.ToList());
                }
                else
                {
                    var errorMsg = $"未找到{employeeId}的用户信息";
                    _commonService.LogResponse(log, errorMsg, false);

                    return MessageResult.FailureResult(errorMsg, returnNull);
                }
            }
            catch (Exception ex)
            {
                var msg = ex.Message;
                _commonService.LogResponse(log, msg, false);
                return MessageResult.FailureResult(msg, returnNull);
            }
        }

        /// <summary>
        /// 批发商核销新流程推送接口 - CSS使用
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> STicketApplicationPushAsync(PushSTicketApplicationRequest request)
        {
            var log = new SetOperationLogRequestDto();
            log = _commonService.InitOperationLog("CSS", "STicketApplicationPushAsync", JsonConvert.SerializeObject(request));
            try
            {
                //await GetCSSFile();
                // 验证字段是否为空
                var validateResult = ValidatePushSTicketRequired(request);
                if (!string.IsNullOrEmpty(validateResult))
                {
                    var errorDto = new CSSPushSTicketResponse { CSSNumber = request.CSSNumber };
                    errorDto.ErrorCode = 1;
                    errorDto.CheckMessage = validateResult;
                    _commonService.LogResponse(log, JsonConvert.SerializeObject(errorDto));
                    return MessageResult.FailureResult(errorDto);
                }

                // 验证推送的值是否存在
                var validateIfExist = await ValidateIfValueValidAsync(request);
                if (!validateIfExist.Success)
                {
                    var errorDto = new CSSPushSTicketResponse { CSSNumber = request.CSSNumber };
                    if (validateIfExist.Message.Contains('-'))
                    {
                        var errorMessages = validateIfExist.Message.Split('-');
                        errorDto.ErrorCode = int.Parse(errorMessages[0]);
                        errorDto.CheckMessage = errorMessages[1];
                        _commonService.LogResponse(log, JsonConvert.SerializeObject(errorDto));
                    }
                    return MessageResult.FailureResult(errorDto);
                }

                var applicationPushData = validateIfExist.Data as Tuple<STicketApplication, List<CreateUpdateSTicketApplicationDetailRequest>>;
                var application = applicationPushData.Item1;
                var applicationDetails = applicationPushData.Item2;

                var sticketApplicationRepository = LazyServiceProvider.LazyGetService<ISTicketApplicationRepository>();
                var sticketApplicationDetailsRepository = LazyServiceProvider.LazyGetService<ISTicketApplicationDetailRepository>();
                // 保存主信息
                await InsertAndGenerateSerialNoAsync(sticketApplicationRepository, application, "S");

                // 保存明细信息
                var sticketApplicationDetails = ObjectMapper.Map<List<CreateUpdateSTicketApplicationDetailRequest>, List<STicketApplicationDetail>>(applicationDetails);
                sticketApplicationDetails.ForEach(a =>
                {
                    a.ParentID = application.Id;
                });
                await sticketApplicationDetailsRepository.InsertManyAsync(sticketApplicationDetails);

                _commonService.LogResponse(log, "CSS推送核销单成功");

                //验证预算
                var result = await ValidateBudgetAsync(application, sticketApplicationDetails);
                if (!result.Success)
                    return result;
                //var budgetResult = result.Data as Tuple<bool, UseBudgetRequestDto>;
                var budgetResult = result.Data as UseBudgetRequestDto;

                //取费用性质Approval Number
                var sticketDetails = await TransferNatureApprovalNumber(sticketApplicationDetails);

                //获取审批提交人
                var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
                var submitter = queryableUser.Where(a => a.UserName == request.applicantEmpId).FirstOrDefault();

                //提交审批
                var exemptType = EnumUtil.GetEnumIdValues<WorkflowTypeName>().ToDictionary(a => (WorkflowTypeName)a.Key, a => a.Value);
                var isOk = await LazyServiceProvider.LazyGetService<IApproveService>().InitiateApprovalAsync(new CreateApprovalDto
                {
                    Name = exemptType[WorkflowTypeName.STicketRequest],
                    Department = application.ApplyUserDeptId.ToString(),
                    BusinessFormId = application.Id.ToString(),
                    BusinessFormNo = application.ApplicationCode,
                    BusinessFormName = NameConsts.STicketApplication,
                    Submitter = submitter.Id.ToString(),
                    OriginalApprovalId = application.ApplyUserId,
                    WorkflowType = WorkflowTypeName.STicketRequest,
                    FormData = JsonConvert.SerializeObject(new
                    {
                        application.ApplicationCode,
                        StickteDtails = sticketDetails.Select(a => new { a.TotalAmount, a.ApprovalNumber }),
                        IsCss = true
                    })
                });

                if (!isOk)
                {
                    var errorDto = new CSSPushSTicketResponse { CSSNumber = request.CSSNumber };
                    errorDto.ErrorCode = 1;
                    errorDto.CheckMessage = "提交审批失败";
                    _commonService.LogResponse(log, JsonConvert.SerializeObject(errorDto));
                    return MessageResult.FailureResult(errorDto);
                }
                else
                {
                    //扣减预算
                    result = await UseSubbudgetAsync(budgetResult);
                }

                var successDto = new CSSPushSTicketResponse
                {
                    CSSNumber = application.CssCode,
                    CheckResult = true,
                    SerialNumber = application.ApplicationCode,
                    ErrorCode = null,
                    CheckMessage = string.Empty,
                };
                return MessageResult.SuccessResult(successDto);
            }
            catch (Exception ex)
            {
                _commonService.LogResponse(log, ex.ToString(), false);
                var errorDto = new CSSPushSTicketResponse { CSSNumber = request.CSSNumber };
                errorDto.ErrorCode = null;
                errorDto.CheckMessage = "推送失败:" + ex;
                _commonService.LogResponse(log, JsonConvert.SerializeObject(errorDto));
                return MessageResult.FailureResult(errorDto);
                //return MessageResult.FailureResult($"推送失败"+ex.Message);
            }
        }

        /// <summary>
        /// 推送SOI状态
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> STicketSOIResultPushAsync(STicketApplication request)
        {
            var approvalResult = string.Empty;
            if (request.ApplicationType == STicketDataSourceConst.CSS)
            {
                switch (request.Status)
                {
                    case STicketStatus.SOIInProcess:
                        approvalResult = "2";
                        break;
                    case STicketStatus.PendingSettlement:
                        approvalResult = "3";
                        break;
                    case STicketStatus.Settled:
                        approvalResult = "4";
                        break;
                    case STicketStatus.SOICancelled:
                        approvalResult = "5";
                        break;

                    default:
                        break;
                }
                PushSTicketApprovalResultDto approvalDto = new PushSTicketApprovalResultDto
                {
                    CSSNumber = request.CssCode,
                    SerialNumber = request.ApplicationCode,
                    ApprovalResult = approvalResult,
                    ApprovedName = string.Empty,
                    ApprovedTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    ApprovedRemark = string.Empty,
                };
                await STicketApprovalResultPushAsync(approvalDto);
            }
            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 批发商核销流程审批结果推送 - CSS使用
        /// </summary>
        /// <param name="resultDto"></param>
        /// <returns></returns>
        public async Task<MessageResult> STicketApprovalResultPushAsync(PushSTicketApprovalResultDto approvalDto)
        {
            var log = new SetOperationLogRequestDto();

            var url = _configuration["Integrations:CSS:BaseUrl"]!.TrimEnd('/') + $"/Bpm/ApprovalResult";
            var appSecret = _configuration["Integrations:CSS:AppSecret"];
            long unixTimestamp = DateTimeOffset.Now.ToUnixTimeSeconds();
            var parameters = new Dictionary<string, string>
            {
                {"channel", "NexBPM" },
                {"timestamp" ,unixTimestamp.ToString()}
            };
            string approveObj = JsonConvert.SerializeObject(approvalDto);//业务数据（json字符串）
            string signature = CreateSign(parameters, appSecret, "sha256", approveObj);
            //发送POST请求
            var requestBody = new CSSApprovalResultPushDto
            {
                channel = "NexBPM",
                timestamp = unixTimestamp.ToString(),
                sign = signature,
                data = approvalDto
            };
            var headers = new Dictionary<string, string>
            {
                { "Content-Type", "application/json" }
            };
            log = _commonService.InitOperationLog("CSS", "STicketApprovalResultPushAsync", JsonConvert.SerializeObject(requestBody));
            var response = await url.WithHeaders(headers).PostJsonAsync(requestBody);
            var responseData = await response.GetStringAsync();
            //序列化返回结果
            var resObj = JsonConvert.DeserializeObject<CSSResponseDto>(responseData);
            //记录日志
            _commonService.LogResponse(log, responseData);

            return MessageResult.SuccessResult(resObj);
        }

        /// <summary>
        /// 创建核销申请单
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> CreateSTicketApplicationAsync(CreateSTicketApplicationRequest request)
        {
            var orgs = await LazyServiceProvider.LazyGetService<IDataverseService>().GetOrganizations(request.ApplyUserDeptId.ToString());
            if (!orgs.Any())
                return MessageResult.FailureResult("未找到申请人部门信息");

            var sticketApplicationRepository = LazyServiceProvider.LazyGetService<ISTicketApplicationRepository>();
            var sticketApplicationDetailsRepository = LazyServiceProvider.LazyGetService<ISTicketApplicationDetailRepository>();
            var sticketApplication = ObjectMapper.Map<CreateSTicketApplicationRequest, STicketApplication>(request);
            if (CurrentUser.Id.HasValue)
            {
                sticketApplication.ApplyUserId = CurrentUser.Id.Value;
                sticketApplication.ApplyUser = CurrentUser.Name;
            }

            var messageResult = await HandleSTicketApplicationAsync(sticketApplication, request, orgs.First());
            if (!messageResult.Success)
                return messageResult;

            sticketApplication = messageResult.Data as STicketApplication;

            await InsertAndGenerateSerialNoAsync(sticketApplicationRepository, sticketApplication, "S");

            //核销明细保存
            messageResult = await HandleSTicketApplicationDetailAsync(sticketApplication, request.DetailItems);
            if (!messageResult.Success)
                return messageResult;
            var sticketApplicationDetailData = messageResult.Data as Tuple<List<STicketApplicationDetail>, List<STicketApplicationDetail>>;

            await CurrentUnitOfWork.SaveChangesAsync();
            return MessageResult.SuccessResult(new Tuple<STicketApplication, List<STicketApplicationDetail>, List<STicketApplicationDetail>>(sticketApplication, sticketApplicationDetailData.Item1, sticketApplicationDetailData.Item2));
        }

        /// <summary>
        /// 修改核销申请单
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> UpdateSTicketApplicationAsync(UpdateSTicketApplicationRequest request)
        {
            if (!request.Id.HasValue)
                return MessageResult.FailureResult($"申请单Id不能为空");

            var sticketApplicationRepository = LazyServiceProvider.LazyGetService<ISTicketApplicationRepository>();
            var sticketApplication = await sticketApplicationRepository.FindAsync(request.Id.Value);
            if (sticketApplication == null)
                return MessageResult.FailureResult($"未找到Id为{request.Id}的核销申请单");

            #region 避免代理人重新提交申请单时，将原始发起人的部门覆盖

            //只能在Draft状态时，才允许修改发起部门
            if (sticketApplication.Status != 0)
            {
                request.ApplyUserDeptId = sticketApplication.ApplyUserDeptId;
                request.ApplyUserDeptName = sticketApplication.ApplyUserDeptName;
            }

            #endregion

            var orgs = await LazyServiceProvider.LazyGetService<IDataverseService>().GetOrganizations(request.ApplyUserDeptId.ToString());
            if (!orgs.Any())
                return MessageResult.FailureResult("未找到申请人部门信息");

            sticketApplication = ObjectMapper.Map(request, sticketApplication);

            var messageResult = await HandleSTicketApplicationAsync(sticketApplication, request, orgs.First());
            if (!messageResult.Success)
                return messageResult;

            sticketApplication = messageResult.Data as STicketApplication;
            await sticketApplicationRepository.UpdateAsync(sticketApplication);

            //核销明细保存
            messageResult = await HandleSTicketApplicationDetailAsync(sticketApplication, request.DetailItems);
            if (!messageResult.Success)
                return messageResult;

            //item1为数据库原始数据，item2为原始数据+新增数据
            var sticketApplicationDetailData = messageResult.Data as Tuple<List<STicketApplicationDetail>, List<STicketApplicationDetail>>;
            var sticketApplicationDetailRepository = LazyServiceProvider.LazyGetService<ISTicketApplicationDetailRepository>();
            if (sticketApplicationDetailData.Item2?.Any() == true)
            {
                //删除不存在的行
                var needDeleteDatas = sticketApplicationDetailData.Item1.Where(a => !request.DetailItems.Any(a1 => a1.Id == a.Id)).ToArray();
                if (needDeleteDatas.Any())
                {
                    var needDeleteIds = needDeleteDatas.Select(a => a.Id).ToArray();
                    //提交之后不能删除明细行数据，只能反冲
                    if (!sticketApplication.ApplyTime.HasValue)
                    {
                        await sticketApplicationDetailRepository.DeleteManyAsync(needDeleteDatas);
                    }
                    sticketApplicationDetailData.Item2.RemoveAll(a => needDeleteIds.Contains(a.Id));
                }
            }
            else
            {
                await sticketApplicationDetailRepository.DeleteAsync(a => a.ParentID == sticketApplication.Id);
            }

            await CurrentUnitOfWork.SaveChangesAsync();
            return MessageResult.SuccessResult(new Tuple<STicketApplication, List<STicketApplicationDetail>, List<STicketApplicationDetail>>(sticketApplication, sticketApplicationDetailData.Item1, sticketApplicationDetailData.Item2));
        }

        /// <summary>
        /// 提交核销申请单
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> SubmitSTicketApplicationAsync(UpdateSTicketApplicationRequest request)
        {
            MessageResult result;
            if (request.Id.HasValue)
                result = await UpdateSTicketApplicationAsync(request);
            else
                result = await CreateSTicketApplicationAsync(request);
            if (!result.Success)
            {
                result.Data = null;
                return result;
            }
            var data = result.Data as Tuple<STicketApplication, List<STicketApplicationDetail>, List<STicketApplicationDetail>>;
            var sticketApplication = data.Item1;
            var originalApplicationDetails = data.Item2;
            var sticketApplicationDetails = data.Item3;

            //验证预算
            result = await ValidateBudgetAsync(sticketApplication, sticketApplicationDetails);
            if (!result.Success)
                return result;
            //var budgetResult = result.Data as Tuple<bool, UseBudgetRequestDto>;
            var budgetResult = result.Data as UseBudgetRequestDto;

            //取费用性质Approval Number
            var sticketDetails = await TransferNatureApprovalNumber(sticketApplicationDetails);

            //提交审批
            var exemptType = EnumUtil.GetEnumIdValues<WorkflowTypeName>().ToDictionary(a => (WorkflowTypeName)a.Key, a => a.Value);
            var isOk = await LazyServiceProvider.LazyGetService<IApproveService>().InitiateApprovalAsync(new CreateApprovalDto
            {
                Name = exemptType[WorkflowTypeName.STicketRequest],
                Department = sticketApplication.ApplyUserDeptId.ToString(),
                BusinessFormId = sticketApplication.Id.ToString(),
                BusinessFormNo = sticketApplication.ApplicationCode,
                BusinessFormName = NameConsts.STicketApplication,
                Submitter = CurrentUser.Id?.ToString(),
                OriginalApprovalId = sticketApplication.ApplyUserId,
                WorkflowType = WorkflowTypeName.STicketRequest,
                FormData = JsonConvert.SerializeObject(new
                {
                    sticketApplication.ApplicationCode,
                    StickteDtails = sticketDetails.Select(a => new { a.TotalAmount, a.ApprovalNumber }),
                    IsCss = true
                    //IsCss = sticketApplication.ApplicationType == STicketDataSourceConst.CSS ? true : false
                })
            });

            if (!isOk)
                return MessageResult.FailureResult("提交审批失败");
            else
            {
                //扣减预算
                result = await UseSubbudgetAsync(budgetResult);

                if (result.Success)
                {
                    //更新申请的状态为“审批中”
                    sticketApplication.Status = STicketStatus.Approving;
                    sticketApplication.ApplyTime = DateTime.Now;
                    await LazyServiceProvider.LazyGetService<ISTicketApplicationRepository>().UpdateAsync(sticketApplication);
                }
            }

            return result;
        }

        /// <summary>
        /// 检查核销申请单金额是否大于限定值
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> CheckSTicketApplicationAmountAsync(UpdateSTicketApplicationRequest request)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            //获取美刀货币配置
            var usdCurrencies = await dataverseService.GetCurrencyConfig(DictionaryType.CurrencyItems.USD);
            var usdCurrency = usdCurrencies.First();
            if (usdCurrency == null)
            {
                return MessageResult.FailureResult("未找到美刀货币配置");
            }

            var subTotalAmount = request.DetailItems.Where(a => a.SubTotalAmountRMB.HasValue).Sum(a => a.SubTotalAmountRMB).Value;
            decimal sumAmount = decimal.Round(subTotalAmount / (decimal)usdCurrency.PlanRate, 4);

            var focRemindAmt = (await dataverseService.GetDictionariesAsync(DictionaryType.FOCRemindAmount)).FirstOrDefault();
            if (focRemindAmt != null)
            {
                var remindAmt = Convert.ToDecimal(focRemindAmt.Name);
                bool isExceedAmount = false;
                if (sumAmount >= remindAmt)
                {
                    isExceedAmount = true;
                }
                var checkResult = new FocApplicationAmountCheckResponseDto
                {
                    IsExceedAmount = isExceedAmount,
                    SumAmount = sumAmount,
                };
                return MessageResult.SuccessResult(checkResult);
            }
            else
            {
                return MessageResult.FailureResult($"未设置FOC提醒金额");
            }
        }

        /// <summary>
        /// 转换费用性质的Approval Number
        /// </summary>
        /// <param name="newPrDetails"></param>
        /// <returns></returns>
        async Task<IEnumerable<(decimal TotalAmount, string ApprovalNumber)>> TransferNatureApprovalNumber(List<STicketApplicationDetail> sticketDetails)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            //获取美刀货币配置
            var usdCurrencies = await dataverseService.GetCurrencyConfig(DictionaryType.CurrencyItems.USD);
            var usdCurrency = usdCurrencies.First();
            var costNatures = await dataverseService.GetCostNatureAsync(stateCode: null);
            //创建审批任务
            var sticketApplicationDetails = sticketDetails.Select(a =>
            {
                var costNatures = dataverseService.GetCostNatureAsync(a.ExpenseNatureId.ToString()).Result;
                var costNature = costNatures.FirstOrDefault();

                return
                (
                    //a.SubTotalAmountRMB,
                    decimal.Round(a.SubTotalAmountRMB / (decimal)usdCurrency.PlanRate, 4),
                    costNature?.ApprovalNumber
                );

            }).ToArray();

            return sticketApplicationDetails;
        }

        /// <summary>
        /// 处理核销申请主表信息
        /// </summary>
        /// <param name="prApplication">The pr application.</param>
        /// <param name="request">The request.</param>
        /// <param name="org">The org.</param>
        /// <returns></returns>
        async Task<MessageResult> HandleSTicketApplicationAsync(STicketApplication sticketApplication, CreateSTicketApplicationRequest request, DepartmentDto org)
        {
            var orgs = await LazyServiceProvider.LazyGetService<ICommonService>().GetParentOrgs(org);
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var bu = orgs.FirstOrDefault(a => a.OrganizationType == DataverseEnums.Organization.OrganizationType.Bu);
            if (bu != null)
            {
                sticketApplication.ApplyUserBuId = bu.Id;
                sticketApplication.ApplyUserBuName = bu.DepartmentName;
                //获取BU编码配置
                var buCodingCfgs = await dataverseService.GetBuCodingCfgAsync(bu.Id.ToString());
                var buCodingCfg = buCodingCfgs.First();
                if (buCodingCfg != null)
                    sticketApplication.BUCode = buCodingCfg.BuCode;
            }

            //部门
            if (org != null)
            {
                sticketApplication.ApplyUserDeptId = org.Id;
                sticketApplication.ApplyUserDeptName = org.DepartmentName;
            }

            //成本中心
            if (request.CostCenterId.HasValue)
            {
                var costcenters = await dataverseService.GetCostcentersAsync(request.CostCenterId.ToString());
                var costcenter = costcenters.FirstOrDefault();
                if (costcenter == null)
                    return MessageResult.FailureResult($"未找到Id为{request.CostCenterId}的成本中心数据");

                sticketApplication.CostCenterID = costcenter.Id;
                sticketApplication.CostCenter = costcenter.Name;
                sticketApplication.CostCenterCode = costcenter.Code;
            }
            else
            {
                sticketApplication.CostCenterID = null;
                sticketApplication.CostCenter = null;
                sticketApplication.CostCenterCode = null;
            }

            //预算
            if (request.SubBudgetId.HasValue)
            {
                var queryBudget = await LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>().GetQueryableAsync();
                var budget = await queryBudget.Include(a => a.MasterBudget).Select(a => new
                {
                    MasterBudget = new { a.MasterBudget.Id, a.MasterBudget.Code },
                    a.Id,
                    a.Code,
                    a.RegionId,
                    a.AttachmentFile,
                    a.UesdAmount,
                    a.BudgetAmount,
                    a.Description,
                    a.OwnerId
                })
                .FirstOrDefaultAsync(a => a.Id == request.SubBudgetId);
                if (budget == null)
                    return MessageResult.FailureResult($"未找到Id为{request.SubBudgetId}的预算数据");

                //主预算信息
                sticketApplication.BudgetId = budget.MasterBudget.Id;
                sticketApplication.BudgetCode = budget.MasterBudget.Code;
                //子预算信息
                sticketApplication.SubBudgetId = budget.Id;
                sticketApplication.SubBudgetCode = budget.Code;
                sticketApplication.SubBudgetRegionId = budget.RegionId;
                sticketApplication.BudgetUsedAmount = budget.UesdAmount;
                sticketApplication.BudgetAmount = budget.BudgetAmount;
                sticketApplication.BudgetAvailableAmount = budget.BudgetAmount - budget.UesdAmount;
                sticketApplication.SubBudgetDesc = budget.Description;
                var regions = await dataverseService.GetDistrict(budget.RegionId.ToString());
                if (regions.Any())
                    sticketApplication.SubBudgetRegion = regions.First().Name;

                var queryUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
                var ownerUser = await queryUser.Select(a => new { a.Id, a.Name }).FirstOrDefaultAsync(a => a.Id == budget.OwnerId);
                if (ownerUser != null)
                {
                    sticketApplication.BudgetOwner = ownerUser.Name;
                }
                sticketApplication.BudgetOwnerId = budget.OwnerId;
                sticketApplication.BudgetFile = budget.AttachmentFile;
            }
            else
            {
                sticketApplication.BudgetId = null;
                sticketApplication.BudgetCode = null;
                //子预算信息
                sticketApplication.SubBudgetId = null;
                sticketApplication.SubBudgetCode = null;
                sticketApplication.SubBudgetRegionId = null;
                sticketApplication.SubBudgetRegion = null;

                sticketApplication.BudgetOwnerId = null;
                sticketApplication.BudgetOwner = null;
                sticketApplication.BudgetFile = null;
            }

            //被代理人
            if (request.AgentId.HasValue)
            {
                var queryUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
                var user = await queryUser.Select(a => new { a.Id, a.Name, a.Email }).FirstOrDefaultAsync(a => a.Id == request.AgentId);
                if (user == null)
                    return MessageResult.FailureResult($"未找到Id为{request.AgentId}的被代理人数据");

                sticketApplication.AgentIdName = user.Name;
                sticketApplication.AgentEmail = user.Email;
            }
            else
            {
                sticketApplication.AgentIdName = null;
                sticketApplication.AgentEmail = null;
            }

            //消费大类
            if (request.ExpenseTypeId.HasValue)
            {
                var consumeCategories = await dataverseService.GetConsumeCategoryAsync(request.ExpenseTypeId.ToString());
                var consumeCategory = consumeCategories.FirstOrDefault();
                if (consumeCategory == null)
                    return MessageResult.FailureResult($"未找到Id为{request.ExpenseTypeId}的消费大类数据");

                sticketApplication.ExpenseTypeId = request.ExpenseTypeId.Value; //消费大类Id
                sticketApplication.ExpenseTypeName = consumeCategory.Name;
            }
            else
            {
                sticketApplication.ExpenseTypeId = null;
                sticketApplication.ExpenseTypeName = null;
            }

            //产品
            if (request.ProductId.HasValue)
            {
                string strProductId = request.ProductId.Value.ToString();
                var product = await dataverseService.GetProductsAsync(strProductId);
                if (!product.Any())
                {
                    return MessageResult.FailureResult($"未找到Id为{request.ProductId}的产品数据");
                }
                sticketApplication.ProductId = request.ProductId.Value;
                sticketApplication.ProductCode = product.Select(a => a.Code).FirstOrDefault();
                sticketApplication.ProductName = product.Select(a => a.Name).FirstOrDefault();
            }
            else
            {
                sticketApplication.ProductId = null;
                sticketApplication.ProductCode = null;
                sticketApplication.ProductName = null;
            }

            //公司
            if (request.CompanyId.HasValue)
            {
                var companies = await dataverseService.GetCompanyList(request.CompanyId.ToString());
                var company = companies.FirstOrDefault();
                if (company == null)
                    return MessageResult.FailureResult($"未找到Id为{request.CompanyId}的公司数据");

                sticketApplication.CompanyCode = company.CompanyCode;
                sticketApplication.CompanyName = company.CompanyName;
            }
            else
            {
                sticketApplication.CompanyId = null;
                sticketApplication.CompanyCode = null;
                sticketApplication.CompanyName = null;
            }

            //客户
            if (request.ClientId.HasValue)
            {
                var queryStore = await LazyServiceProvider.LazyGetService<IStoreInfoRepository>().GetQueryableAsync();
                var client = await queryStore.FirstOrDefaultAsync(a => a.Id == request.ClientId);
                if (client == null)
                    return MessageResult.FailureResult($"未找到Id为{request.ClientId}的客户数据");
                sticketApplication.ClientCode = client.StoreCode;
                sticketApplication.ClientName = client.StoreName;
            }
            else
            {
                sticketApplication.ClientId = null;
                sticketApplication.ClientCode = null;
                sticketApplication.ClientName = null;
            }

            //客户类型
            if (request.ClientTypeId.HasValue)
            {
                //获取客户类型
                var customerTypeList = await dataverseService.GetDictionariesAsync(DictionaryType.CustomerType);
                var customerType = customerTypeList.Where(a => a.Id == request.ClientTypeId).FirstOrDefault();
                if (customerType == null)
                    return MessageResult.FailureResult($"未找到Id为{request.ClientTypeId}的客户类型数据");
                sticketApplication.ClientType = customerType.Name;
            }
            else
            {
                sticketApplication.ClientTypeId = null;
                sticketApplication.ClientType = null;
            }

            //折扣大类
            if (request.DiscountCategoryId.HasValue)
            {
                //获取折扣大类
                var discountCategories = await dataverseService.GetDictionariesAsync(DictionaryType.DiscountCategory);
                var discountCategory = discountCategories.Where(a => a.Id == request.DiscountCategoryId).FirstOrDefault();
                if (discountCategory == null)
                    return MessageResult.FailureResult($"未找到Id为{request.DiscountCategoryId}的折扣大类数据");
                sticketApplication.DiscountCategory = discountCategory.Name;
            }
            else
            {
                sticketApplication.DiscountCategory = null;
            }

            //城市
            if (request.CityId.HasValue)
            {
                //城市主数据
                var cities = await dataverseService.GetSpecialCitiesAsync(request.CityId.ToString());
                var city = cities.FirstOrDefault();
                if (city == null)
                    return MessageResult.FailureResult($"未找到Id为{request.CityId}的城市数据");
                sticketApplication.CityCode = city?.CityCode;
                sticketApplication.CityName = city?.Name;
            }
            else
            {
                sticketApplication.CityCode = null;
                sticketApplication.CityName = null;
            }

            if (request.SettlementPeriodStart.HasValue)
                sticketApplication.SettlementPeriodStart = request.SettlementPeriodStart.Value;

            if (request.SettlementPeriodEnd.HasValue)
                sticketApplication.SettlementPeriodEnd = request.SettlementPeriodEnd.Value;

            sticketApplication.Content = request.Content;
            sticketApplication.Attachment = request.Attachment?.Select(a => a.AttachmentId).JoinAsString(",");
            sticketApplication.Status = STicketStatus.Draft;

            var totalAmount = request.DetailItems?.Sum(a => a.Quantity * a.Price) ?? 0;
            sticketApplication.TotalAmountRMB = totalAmount;
            sticketApplication.CurrencyCode = DictionaryType.CurrencyItems.RMB;
            sticketApplication.ApplicationType = STicketDataSourceConst.NexBPM;

            return MessageResult.SuccessResult(sticketApplication);
        }

        /// <summary>
        /// 分页获取客户列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetClientListResponseDto>> GetClientListAsync(GetClientListRequestDto request)
        {
            var storeQuery = await LazyServiceProvider.LazyGetService<IStoreInfoRepository>().GetQueryableAsync();

            var query = storeQuery
                .Where(a => a.DataSource == CustomerDataSource.Dealer && !string.IsNullOrEmpty(a.StoreCode))
                .WhereIf(!string.IsNullOrEmpty(request.CustomerCode), a => a.StoreCode.Contains(request.CustomerCode))
                .WhereIf(!string.IsNullOrEmpty(request.CustomerName), a => a.StoreName.Contains(request.CustomerName));

            var queryResult = query.Select(a => new GetClientListResponseDto
            {
                Id = a.Id,
                CustomerCode = a.StoreCode,
                CustomerName = a.StoreName
            });

            var count = queryResult.Count();
            var datas = queryResult.Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToArray();

            return new PagedResultDto<GetClientListResponseDto>(count, datas);
        }

        /// <summary>
        /// 分页获取门店列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetStoreListResponseDto>> GetStoreListAsync(GetStoreListRequestDto request)
        {
            var storeQuery = await LazyServiceProvider.LazyGetService<IStoreInfoRepository>().GetQueryableAsync();

            var query = storeQuery.WhereIf(!string.IsNullOrEmpty(request.StoreCode), a => a.StoreCode.Contains(request.StoreCode))
                .WhereIf(!string.IsNullOrEmpty(request.StoreName), a => a.StoreName.Contains(request.StoreName))
                .WhereIf(!string.IsNullOrEmpty(request.Channel), a => a.ChannelCN.Equals(request.Channel))
                .WhereIf(!string.IsNullOrEmpty(request.SubChannel), a => a.SubChannelCN.Equals(request.SubChannel));

            var queryResult = query.Select(a => new GetStoreListResponseDto
            {
                StoreId = a.Id,
                StoreCode = a.StoreCode,
                StoreName = a.StoreName,
                Channel = a.ChannelCN,
                SubChannel = a.SubChannelCN
            });

            var count = queryResult.Count();
            var datas = queryResult.Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToArray();

            return new PagedResultDto<GetStoreListResponseDto>(count, datas);
        }

        /// <summary>
        /// 分页获取结算对象列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetSettlementEntityResponseDto>> GetSettlementEntityListAsync(GetSettlementEntityRequestDto request)
        {
            var storeQuery = await LazyServiceProvider.LazyGetService<IStoreInfoRepository>().GetQueryableAsync();

            var query = storeQuery.WhereIf(!string.IsNullOrEmpty(request.SettlementEntityCode), a => a.CustomerId.Contains(request.SettlementEntityCode))
                .WhereIf(!string.IsNullOrEmpty(request.SettlementEntityName), a => a.StoreName.Contains(request.SettlementEntityName))
                .WhereIf(!string.IsNullOrEmpty(request.SettlementEntityType), a => a.DataSource.Equals(request.SettlementEntityType))
                .GroupJoin(storeQuery, a => a.ParentCustomerId, a => a.CustomerId, (a, b) => new { customer = a, parentCustomer = b })
                .SelectMany(a => a.parentCustomer.DefaultIfEmpty(), (a, b) => new { a.customer, parentCustomer = b });

            var queryResult = query.Select(a => new GetSettlementEntityResponseDto
            {
                Id = a.customer.Id,
                SettlementEntityCode = a.customer.CustomerId,
                SettlementEntityName = a.customer.StoreName,
                SettlementEntityType = a.customer.DataSource,
                SettlementEntityChannel = a.customer.SubChannelCN,
                SettlementEntityHQCode = a.customer.ParentCustomerId,
                SettlementEntityHQName = a.parentCustomer.StoreName,
            });

            var count = queryResult.Count();
            var datas = queryResult.Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToArray();

            return new PagedResultDto<GetSettlementEntityResponseDto>(count, datas);
        }

        /// <summary>
        /// 获取客户类型列表
        /// </summary>
        /// <returns></returns>
        public async Task<List<GetCustomerTypeResponseDto>> GetCustomerTypeListAsync()
        {
            var _dataverseService = _serviceProvider.GetService<IDataverseService>();
            var customerTypeList = (await _dataverseService.GetDictionariesAsync(DictionaryType.CustomerType)).ToList();
            var result = ObjectMapper.Map<List<DictionaryDto>, List<GetCustomerTypeResponseDto>>(customerTypeList);
            return result;
        }

        /// <summary>
        /// 获取BU列表
        /// </summary>
        /// <returns></returns>
        public async Task<List<DepartmentDto>> GetBusinessUnitListAsync()
        {
            var _dataverseService = _serviceProvider.GetService<IDataverseService>();
            var businessUnitList = (await _dataverseService.GetOrganizations()).Where(x => x.OrganizationType == OrganizationType.Bu).OrderBy(a => a.DepartmentName).ToList();
            return businessUnitList;
        }

        /// <summary>
        /// 获取门店渠道列表
        /// </summary>
        /// <returns></returns>
        public async Task<List<string>> GetStoreChannelList()
        {
            var storeQuery = await LazyServiceProvider.LazyGetService<IStoreInfoRepository>().GetQueryableAsync();
            var channelList = storeQuery.Select(a => a.ChannelCN).Distinct().ToList();
            return channelList;
        }

        /// <summary>
        /// 获取门店子渠道列表
        /// </summary>
        /// <returns></returns>
        public async Task<List<string>> GetStoreSubChannelList()
        {
            var storeQuery = await LazyServiceProvider.LazyGetService<IStoreInfoRepository>().GetQueryableAsync();
            var subChannelList = storeQuery.Select(a => a.SubChannelCN).Distinct().ToList();
            return subChannelList;
        }

        /// <summary>
        /// 获取结算对象类型
        /// </summary>
        /// <returns></returns>
        public async Task<List<string>> GetSettlementTypeList()
        {
            var storeQuery = await LazyServiceProvider.LazyGetService<IStoreInfoRepository>().GetQueryableAsync();
            var dataSourceList = storeQuery.Select(a => a.DataSource).Distinct().ToList();
            return dataSourceList;
        }

        /// <summary>
        /// 核销申请列表查询
        /// </summary>
        /// <param name="requestDto">The request dto.</param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetSTicketApplicationResponseDto>> GetSTicketApplicationAsync(GetSTicketApplicationRequestDto requestDto)
        {
            var sTicketQuery = (await LazyServiceProvider.LazyGetService<ISTicketApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            var query = sTicketQuery.Where(m => m.Status != STicketStatus.Draft).WhereIf(!string.IsNullOrEmpty(requestDto.ApplicationCode), m => m.ApplicationCode.Contains(requestDto.ApplicationCode))
                  .WhereIf(!string.IsNullOrEmpty(requestDto.ApplyDeptName), m => m.ApplyUserDeptName.Contains(requestDto.ApplyDeptName))
                  //.WhereIf(!string.IsNullOrEmpty(requestDto.CostCenter), m => m.CostCenter.Contains(requestDto.CostCenter))
                  .WhereIf(requestDto.Status.HasValue, m => m.Status == requestDto.Status)
                  .WhereIf(!string.IsNullOrEmpty(requestDto.SubBudgetCode), m => m.SubBudgetCode == requestDto.SubBudgetCode)
                  .WhereIf(requestDto.ExpenseTypeId.HasValue, m => m.ExpenseTypeId == requestDto.ExpenseTypeId)
                  //查询结算周期有交集的数据
                  .WhereIf(requestDto.SettlementPeriodStart.HasValue && requestDto.SettlementPeriodEnd.HasValue, m => m.SettlementPeriodStart <= requestDto.SettlementPeriodEnd && m.SettlementPeriodEnd >= requestDto.SettlementPeriodStart)
                  .WhereIf(requestDto.StartDate.HasValue, m => m.ApplyTime >= requestDto.StartDate)
                  .WhereIf(requestDto.EndDate.HasValue, m => m.ApplyTime < requestDto.EndDate.Value.AddDays(1))
                  .WhereIf(requestDto.ApplicantId.HasValue, m => m.ApplyUserId == requestDto.ApplicantId)
                  .WhereIf(!requestDto.IsAll, m => m.ApplyUserId == CurrentUser.Id);

            var personCenterService = LazyServiceProvider.LazyGetService<IPersonCenterService>();
            var commonService = LazyServiceProvider.LazyGetService<ICommonService>();
            var roleLevel = personCenterService.GetEFlowMyRoleLevel();

            //var roleLevel = rolesIds.MinBy(m => m.Key);
            var rolekyes = await personCenterService.GetIdsByRoleLevels([roleLevel]);
            switch (roleLevel)
            {
                case RoleLevel.Admin:
                    break;
                case RoleLevel.Manager:
                    {
                        var buIds = rolekyes.GetValueOrDefault(RoleLevel.Manager)?.ToList() ?? [];
                        var depts = await commonService.GetChildrenOrgs(buIds.ToList());
                        var deptIds = depts.Select(s => s.Id).ToList();
                        deptIds.AddRange(buIds);
                        deptIds.Add(Guid.NewGuid());
                        query = query.Where(m => deptIds.Contains(m.ApplyUserDeptId) || m.ApplyUserId == CurrentUser.Id || m.TransfereeId == CurrentUser.Id);
                    }
                    break;
                case RoleLevel.Owner:
                    {
                        var budgetIds = (rolekyes.GetValueOrDefault(RoleLevel.Owner))?.ToList() ?? [];
                        budgetIds.Add(Guid.NewGuid());
                        query = query.Where(m => (m.SubBudgetId.HasValue && budgetIds.Contains(m.SubBudgetId.Value)) || m.ApplyUserId == CurrentUser.Id || m.TransfereeId == CurrentUser.Id);
                    }
                    break;
                case RoleLevel.Leader:
                    {
                        var orgIds = (await commonService.GetAllChildrenOrgs(CurrentUser.Id.Value)).ToHashSet();
                        var depts = await commonService.GetChildrenOrgs(orgIds.ToList());
                        var deptIds = depts.Select(s => s.Id).ToList();
                        deptIds.AddRange(orgIds);
                        deptIds.Add(Guid.NewGuid());
                        query = query.Where(m => deptIds.Contains(m.ApplyUserDeptId));
                    }
                    break;
                default:
                    query = query.Where(m => m.ApplyUserId == CurrentUser.Id || m.TransfereeId == CurrentUser.Id);
                    break;
            }
            var count = await query.CountAsync();
            var datas = await query.OrderByDescending(o => o.ApplyTime).PagingIf(requestDto).ToListAsync();
            var result = ObjectMapper.Map<List<STicketApplication>, List<GetSTicketApplicationResponseDto>>(datas);
            return new PagedResultDto<GetSTicketApplicationResponseDto>(count, result);
        }

        /// <summary>
        /// 核销申请列表查询
        /// </summary>
        /// <param name="requestDto">The request dto.</param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetSTicketApplicationDraftResponseDto>> GetSTicketDraftApplicationAsync(GetSTicketDraftApplicationRequestDto requestDto)
        {
            var sTicketQuery = (await LazyServiceProvider.LazyGetService<ISTicketApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            var sTicketDetailQuery = (await LazyServiceProvider.LazyGetService<ISTicketApplicationDetailRepository>().GetQueryableAsync()).AsNoTracking();
            var query = sTicketQuery.Where(m => m.Status == STicketStatus.Draft && (m.ApplyUserId == CurrentUser.Id || m.TransfereeId == CurrentUser.Id)).WhereIf(!string.IsNullOrEmpty(requestDto.ApplicationCode), m => m.ApplicationCode.Contains(requestDto.ApplicationCode))
                  .WhereIf(!string.IsNullOrEmpty(requestDto.ApplyDeptName), m => m.ApplyUserDeptName.Contains(requestDto.ApplyDeptName))
                  //.WhereIf(!string.IsNullOrEmpty(requestDto.CostCenter), m => m.CostCenter.Contains(requestDto.CostCenter))
                  .WhereIf(!string.IsNullOrEmpty(requestDto.SubBudgetCode), m => m.SubBudgetCode == requestDto.SubBudgetCode)
                  .WhereIf(requestDto.ExpenseTypeId.HasValue, m => m.ExpenseTypeId == requestDto.ExpenseTypeId)
                  //.WhereIf(requestDto.ExpenseNatureId.HasValue, m => m.ExpenseTypeId == requestDto.ExpenseTypeId)
                  //查询结算周期有交集的数据
                  .WhereIf(requestDto.SettlementPeriodStart.HasValue && requestDto.SettlementPeriodEnd.HasValue, m => m.SettlementPeriodStart <= requestDto.SettlementPeriodEnd && m.SettlementPeriodEnd >= requestDto.SettlementPeriodStart)
                  .WhereIf(requestDto.StartDate.HasValue, m => m.CreationTime >= requestDto.StartDate)
                  .WhereIf(requestDto.EndDate.HasValue, m => m.CreationTime < requestDto.EndDate.Value.AddDays(1))
                  .WhereIf(requestDto.ApplicantId.HasValue, m => m.ApplyUserId == requestDto.ApplicantId);

            var personCenterService = LazyServiceProvider.LazyGetService<IPersonCenterService>();
            var commonService = LazyServiceProvider.LazyGetService<ICommonService>();
            var roleLevel = personCenterService.GetEFlowMyRoleLevel();

            var count = await query.CountAsync();
            var datas = await query.OrderByDescending(o => o.CreationTime).PagingIf(requestDto).ToListAsync();
            var result = ObjectMapper.Map<List<STicketApplication>, List<GetSTicketApplicationDraftResponseDto>>(datas);
            return new PagedResultDto<GetSTicketApplicationDraftResponseDto>(count, result);
        }
        /// <summary>
        /// 核销申请列表查询
        /// </summary>
        /// <param name="requestDto">The request dto.</param>
        /// <returns></returns>
        public async Task<MessageResult> DeleteSTicketAsync(Guid Id)
        {
            var sTicketResponse = LazyServiceProvider.LazyGetService<ISTicketApplicationRepository>();
            //var sTicketQuery = (await LazyServiceProvider.LazyGetService<ISTicketApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            await sTicketResponse.DeleteAsync(Id);
            return MessageResult.SuccessResult("Succsee");
        }

        /// <summary>
        /// 核销单推送字段验证
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        private string ValidatePushSTicketRequired(PushSTicketApplicationRequest request)
        {
            var result = string.Empty;
            if (string.IsNullOrEmpty(request.CSSNumber))
                result += "CSSNumber不能为空;";

            if (string.IsNullOrEmpty(request.applicantEmpId))
                result += "applicantEmpId不能为空;";

            if (string.IsNullOrEmpty(request.budgetId))
                result += "budgetId不能为空;";

            if (string.IsNullOrEmpty(request.expenseCategory_Id))
                result += "expenseCategory_Id不能为空;";

            if (string.IsNullOrEmpty(request.productCbo_Id))
                result += "productCbo_Id不能为空;";

            if (string.IsNullOrEmpty(request.company_Id))
                result += "company_Id不能为空;";

            if (string.IsNullOrEmpty(request.customtype_Id))
                result += "customtype_Id不能为空;";

            if (string.IsNullOrEmpty(request.customId))
                result += "customId不能为空;";

            if (!request.sumamount.HasValue)
                result += "sumamount不能为空;";

            if (string.IsNullOrEmpty(request.verificationContent))
                result += "verificationContent不能为空;";

            if (!request.settlementPeriodStart.HasValue)
                result += "settlementPeriodStart不能为空;";

            if (!request.settlementPeriodEnd.HasValue)
                result += "settlementPeriodEnd不能为空;";

            if (string.IsNullOrEmpty(request.cityId))
                result += "cityId不能为空;";

            if (string.IsNullOrEmpty(request.Url))
                result += "Url不能为空;";

            //核销明细信息
            if (request.writeOffDetails.Count > 0)
            {
                foreach (var item in request.writeOffDetails)
                {
                    if (!item.no.HasValue)
                        result += "no不能为空;";

                    if (string.IsNullOrEmpty(item.StoreId))
                        result += "StoreId不能为空;";

                    if (string.IsNullOrEmpty(item.ExpensePropertyId))
                        result += "ExpensePropertyId不能为空;";

                    if (!item.number.HasValue)
                        result += "number不能为空;";

                    if (!item.price.HasValue)
                        result += "price不能为空;";

                    if (!item.amount.HasValue)
                        result += "amount不能为空;";

                    if (!item.PredictDate.HasValue)
                        result += "PredictDate不能为空;";
                }
            }
            else
            {
                result += "核销明细不能为空;";
            }
            return result;
        }

        async Task<MessageResult> ValidateIfValueValidAsync(PushSTicketApplicationRequest request)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            //CSS单号
            var querySTicket = await LazyServiceProvider.LazyGetService<ISTicketApplicationRepository>().GetQueryableAsync();
            var existApplication = querySTicket.Where(a => a.CssCode == request.CSSNumber && a.Status != STicketStatus.Rejected).FirstOrDefault();
            if (existApplication != null)
                return MessageResult.FailureResult($"4-CSSNumber接收重复。");

            var application = new STicketApplication();
            application.CssCode = request.CSSNumber;
            //申请人
            var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var user = queryableUser.FirstOrDefault(a => a.UserName == request.applicantEmpId);
            Guid? userOrgId = null;
            if (user != null)
            {
                //获取用户主部门
                var userDeptId = user.GetProperty<Guid?>(EntityConsts.IdentityUser.MainDepartmentId);
                if (!userDeptId.HasValue)
                    return MessageResult.FailureResult($"2-applicantEmpId未设置主部门。");

                IEnumerable<DepartmentDto> orgs = await dataverseService.GetOrganizations(userDeptId.Value.ToString());
                if (!orgs.Any())
                    return MessageResult.FailureResult($"2-未找到applicantEmpId组织信息。");

                var org = orgs.First();
                userOrgId = org.Id;
                //获取当前机构的上级BU
                orgs = await LazyServiceProvider.LazyGetService<ICommonService>().GetParentOrgs(org);
                var bu = orgs.FirstOrDefault(a => a.OrganizationType == OrganizationType.Bu);
                if (bu == null)
                    return MessageResult.FailureResult($"2-未找到applicantEmpId上级BU信息。");

                //预算
                var querySubBudget = await LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>().GetQueryableAsync();
                var subBudget = querySubBudget.Include(a => a.MasterBudget).Include(s => s.MonthlyBudgets).FirstOrDefault(a => a.Code == request.budgetId);
                if (subBudget != null)
                {
                    if (!subBudget.Status)
                        return MessageResult.FailureResult($"5-budgetId已冻结。");

                    //子预算成本中心与申请人所在组织的成本中心是否一致
                    if (org.CostcenterId != subBudget.CostCenterId)
                        return MessageResult.FailureResult($"6-budgetId预算的大区或成本中心与申请人不匹配，请更换预算。");

                    //比较预算可用金额与核销金额
                    var subBudgetAvailableAmount = subBudget.GetAvailableAmount();
                    if (subBudgetAvailableAmount < request.sumamount)
                        return MessageResult.FailureResult($"3-预算可用金额不足。");
                }
                else
                {
                    return MessageResult.FailureResult($"2-budgetId不存在。");
                }

                //大区
                var staff = await dataverseService.GetStaffs(user.Id.ToString());
                if (staff.Any() && staff.First().DistrictId.HasValue && staff.First().DistrictName != "全国")
                {
                    var userRegionId = staff.First().DistrictId;
                    if (userRegionId != subBudget.RegionId)
                        return MessageResult.FailureResult($"6-budgetId预算对应大区与申请人所在大区不一致。");
                }

                //成本中心
                var costcenters = await dataverseService.GetCostcentersAsync(org.CostcenterId.ToString());
                var costcenter = costcenters.FirstOrDefault();
                if (costcenter == null)
                    return MessageResult.FailureResult($"未找到Id为{org.CostcenterId}的成本中心数据");

                application.CostCenterID = costcenter.Id;
                application.CostCenter = costcenter.Name;
                application.CostCenterCode = costcenter.Code;

                application.ApplyUserId = user.Id;
                application.ApplyUser = user.Name;
                application.ApplyUserBuId = bu.Id;

                application.ApplyUserBuName = bu.DepartmentName;
                application.ApplyUserDeptId = org.Id;
                application.ApplyUserDeptName = org.DepartmentName;

                //获取BU编码配置
                var buCodingCfgs = await dataverseService.GetBuCodingCfgAsync(bu.Id.ToString());
                var buCodingCfg = buCodingCfgs.First();
                if (buCodingCfg != null)
                    application.BUCode = buCodingCfg.BuCode;

                //主预算信息
                application.BudgetId = subBudget.MasterBudget.Id;
                application.BudgetCode = subBudget.MasterBudget.Code;
                //子预算信息
                application.SubBudgetId = subBudget.Id;
                application.SubBudgetCode = subBudget.Code;
                application.SubBudgetRegionId = subBudget.RegionId;
                application.BudgetUsedAmount = subBudget.UesdAmount;
                application.BudgetAmount = subBudget.BudgetAmount;
                application.BudgetAvailableAmount = subBudget.GetAvailableAmount();
                application.SubBudgetDesc = subBudget.Description;

                var regions = await dataverseService.GetDistrict(subBudget.RegionId.ToString());
                if (regions.Any())
                    application.SubBudgetRegion = regions.First().Name;

                var queryUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
                var ownerUser = await queryUser.Select(a => new { a.Id, a.Name }).FirstOrDefaultAsync(a => a.Id == subBudget.OwnerId);
                if (ownerUser != null)
                {
                    application.BudgetOwner = ownerUser.Name;
                }
                application.BudgetOwnerId = subBudget.OwnerId;
                application.BudgetFile = subBudget.AttachmentFile;
            }
            else
            {
                return MessageResult.FailureResult($"2-applicantEmpId不存在。");
            }

            //消费大类
            var consumeCategories = await dataverseService.GetConsumeCategoryAsync(request.expenseCategory_Id.ToString());
            var consumeCategory = consumeCategories.FirstOrDefault();
            if (consumeCategory == null)
                return MessageResult.FailureResult($"2-expenseCategory_Id不存在。");
            application.ExpenseTypeId = consumeCategory.Id;
            application.ExpenseTypeName = consumeCategory.Name;

            //产品
            var products = await dataverseService.GetProductsAsync(request.productCbo_Id.ToString());
            var product = products.FirstOrDefault();
            if (product == null)
                return MessageResult.FailureResult($"2-productCbo_Id不存在。");
            application.ProductId = product.Id;
            application.ProductCode = product.Code;
            application.ProductName = product.Name;

            //公司
            //var companies = await dataverseService.GetCompanyList(request.company_Id.ToString());
            var companies = await dataverseService.GetCompanyList();
            var company = companies.Where(a => a.CompanyCode == request.company_Id).FirstOrDefault();
            if (company == null)
                return MessageResult.FailureResult($"2-company_Id不存在。");
            application.CompanyId = company.Id;
            application.CompanyCode = company.CompanyCode;
            application.CompanyName = company.CompanyName;

            //客户类型
            var customerTypeList = await dataverseService.GetDictionariesAsync(DictionaryType.CustomerType);
            var customerType = customerTypeList.Where(a => a.Id.ToString() == request.customtype_Id.ToString()).FirstOrDefault();
            if (customerType == null)
                return MessageResult.FailureResult($"2-customtype_Id不存在。");
            application.ClientTypeId = customerType.Id;
            application.ClientType = customerType.Name;

            //客户编号
            //var clientQuery = await LazyServiceProvider.LazyGetService<IClientInfoRepository>().GetQueryableAsync();
            var clientQuery = await LazyServiceProvider.LazyGetService<IStoreInfoRepository>().GetQueryableAsync();
            var customerCode = request.customId;
            if (request.customId.Contains('-'))
            {
                var customer = request.customId.Split('-');
                customerCode = customer[1];
            }
            var client = await clientQuery.FirstOrDefaultAsync(a => a.StoreCode.ToString() == customerCode);
            if (client == null)
                return MessageResult.FailureResult($"2-customId不存在。");
            application.ClientId = client.Id;
            application.ClientCode = client.StoreCode;
            application.ClientName = client.StoreName;

            //折扣结算-开始日期
            try
            {
                var formattedStartDate = request.settlementPeriodStart.Value.ToString("yyyy-MM-dd");
                application.SettlementPeriodStart = request.settlementPeriodStart.Value;
            }
            catch (Exception)
            {
                return MessageResult.FailureResult($"4-settlementPeriodStart必须为年月日格式。");
            }

            //折扣结算-结束日期
            try
            {
                var formattedEndDate = request.settlementPeriodEnd.Value.ToString("yyyy-MM-dd");
                application.SettlementPeriodEnd = request.settlementPeriodEnd.Value;
            }
            catch (Exception)
            {
                return MessageResult.FailureResult($"4-settlementPeriodEnd必须为年月日格式。");
            }

            //城市
            //根据组织和公司获取城市
            //var result = await GetSpecialCitiesAsync(userOrgId.Value, company.Id);
            //if (!result.Success)
            //    return MessageResult.FailureResult($"获取城市列表失败");
            //var cities = result.Data as IEnumerable<KeyValueDto>;

            //获取公司与城市关系数据
            var cities = await dataverseService.GetSpecialCitiesAsync();

            var city = cities.FirstOrDefault(a => a.Id.ToString() == request.cityId);
            if (city == null)
                return MessageResult.FailureResult($"2-cityId不存在。");
            application.CityId = city.Id;
            application.CityCode = city?.CityCode;
            application.CityName = city?.Name;

            //附件
            var log = new SetOperationLogRequestDto();
            log = _commonService.InitOperationLog("CSS", "GetCSSFile", request.Url);
            var cssFileResult = await GetCSSFile(request.Url);

            _commonService.LogResponse(log, JsonConvert.SerializeObject(cssFileResult));
            if (cssFileResult.Success)
            {
                var uploadFile = cssFileResult.Data as List<UploadFileResponseDto>;
                application.Attachment = uploadFile.Count > 0 ? uploadFile.Select(a => a.AttachmentId).JoinAsString(",") : string.Empty;
            }

            application.ApplicationCode = request.CSSNumber;
            application.Content = request.verificationContent;
            application.Remark = request.remark;
            application.CurrencyCode = DictionaryType.CurrencyItems.RMB;
            application.TotalAmountRMB = request.sumamount.Value;
            application.ApplicationType = STicketDataSourceConst.CSS;
            application.DataSource = STicketDataSourceConst.CSS;
            application.Status = STicketStatus.Approving;
            application.ApplyTime = DateTime.Now;

            //核销明细
            var storeQuery = await LazyServiceProvider.LazyGetService<IStoreInfoRepository>().GetQueryableAsync();
            //获取该消费大类下的费用性质
            var costNatures = await dataverseService.GetCostNatureAsync(consumeCategory.Id.ToString());

            var applicationDetails = new List<CreateUpdateSTicketApplicationDetailRequest>();
            var writeOffDetails = request.writeOffDetails.OrderBy(a => a.no).ToList();
            foreach (var item in writeOffDetails)
            {
                //行号
                if (item.no <= 0)
                    return MessageResult.FailureResult($"4-no必须为正整数。");

                //门店
                var store = await storeQuery.FirstOrDefaultAsync(a => a.CustomerId.ToString() == item.StoreId.ToString());
                if (store == null)
                    return MessageResult.FailureResult($"2-StoreId不存在。");

                var parentCustomerName = string.Empty;
                if (!string.IsNullOrEmpty(store.ParentCustomerId))
                {
                    var parentCustomer = await storeQuery.FirstOrDefaultAsync(a => a.CustomerId.ToString() == store.ParentCustomerId);
                    if (parentCustomer != null)
                        parentCustomerName = parentCustomer.StoreName;
                }

                //费用性质
                var costNature = costNatures.FirstOrDefault(a => a.Id.ToString() == item.ExpensePropertyId);
                if (costNature == null)
                    return MessageResult.FailureResult($"2-ExpensePropertyId不存在。");

                if (item.number <= 0)
                    return MessageResult.FailureResult($"4-no必须为正整数。");

                if (item.price <= 0)
                    return MessageResult.FailureResult($"4-price必须为4位正小数。");

                if (item.amount <= 0)
                    return MessageResult.FailureResult($"4-amount必须为4位正小数。");

                //预计年月
                try
                {
                    var formattedPredictDate = item.PredictDate.Value.ToString("yyyy-MM");
                }
                catch (Exception)
                {
                    return MessageResult.FailureResult($"4-PredictDate必须为年月格式。");
                }

                var detailItem = new CreateUpdateSTicketApplicationDetailRequest();
                detailItem.RowId = item.no;
                detailItem.StoreId = store.Id;
                detailItem.StoreName = store.StoreName;
                detailItem.StoreChannel = store.ChannelCN;
                detailItem.StoreSubChannel = store.SubChannelCN;
                detailItem.SettlementEntityId = store.Id;
                detailItem.SettlementEntityCode = store.CustomerId;
                detailItem.SettlementEntityName = store.StoreName;
                detailItem.SettlementEntityType = store.DataSource;
                detailItem.SettlementEntityChannel = store.SubChannelCN;
                detailItem.SettlementEntityHQCode = store.ParentCustomerId;
                detailItem.SettlementEntityHQName = parentCustomerName;
                detailItem.ExpenseNatureId = costNature.Id;
                detailItem.ExpenseNatureCode = costNature.Code;
                detailItem.ExpenseNature = costNature.Name;
                detailItem.ExpenseNatureDesc = costNature.Description;
                detailItem.Quantity = item.number;
                detailItem.Price = item.price;
                detailItem.SubTotalAmountRMB = item.amount;
                //detailItem.SettlementRegion = item.settlement_region;
                //detailItem.SettlementPeriodStart = item.settlement_period_start;
                //detailItem.SettlementPeriodEnd = item.settlement_period_end;
                detailItem.CityId = city.Id;
                detailItem.CityCode = city.CityCode;
                detailItem.CityName = city.Name;
                detailItem.PredictDate = item.PredictDate;
                detailItem.Description = item.content;
                applicationDetails.Add(detailItem);
            }
            return MessageResult.SuccessResult(new Tuple<STicketApplication, List<CreateUpdateSTicketApplicationDetailRequest>>(application, applicationDetails));
        }

        /// <summary>
        /// 根据组织获取城市
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="companyId"></param>
        /// <returns></returns>
        public async Task<MessageResult> GetSpecialCitiesAsync(Guid orgId, Guid? companyId)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            //获取BU
            var orgs = await LazyServiceProvider.LazyGetService<ICommonService>().GetAllParentOrgs(orgId);
            var bu = orgs.FirstOrDefault(a => a.OrganizationType == DataverseEnums.Organization.OrganizationType.Bu);
            if (bu == null)
                return default;

            IEnumerable<Guid> cityIds = null;
            //获取机构与城市关系数据
            var allOrgCityRelations = await dataverseService.GetOrgSpecialCityRelationAsync();
            var orgCityRelations = allOrgCityRelations.Where(a => a.CityId.HasValue && a.OrgId.HasValue && a.OrgId == bu.Id);
            var restrictedCities = orgCityRelations.Where(a => a.IsRestricted == true);
            //单据所属BU有限定的城市，则返回限定的城市
            if (restrictedCities.Any())
                cityIds = restrictedCities.Select(a => a.CityId.Value).ToArray();
            else
            {
                if (companyId.HasValue)
                {
                    //获取公司与城市关系数据
                    var cityCompanyRelations = await dataverseService.GetCityCompanyRelationsAsync(companyId.ToString());
                    cityCompanyRelations = cityCompanyRelations.Where(x => x.CityId.HasValue && x.CompanyId.HasValue);
                    //如果有配置机构与城市关系，则取与公司城市关系交集的数据
                    if (orgCityRelations.Any())
                        cityIds = cityCompanyRelations.Join(orgCityRelations, a => a.CityId, a => a.CityId, (a, b) => b.CityId.Value);
                    else//如果没有配置机构与城市关系，则直接取与公司关联的城市数据
                        cityIds = cityCompanyRelations.Where(a => a.CityId.HasValue).Select(a => a.CityId.Value);
                }
                else
                {
                    //返回机构与城市关系数据
                    if (orgCityRelations.Any())
                        cityIds = orgCityRelations.Where(a => a.CityId.HasValue).Select(a => a.CityId.Value);
                }

                //单据所属BU没有限定的城市，则需要额外排除标记"是否限定BU城市"为"Yes"的城市
                var allRestrictedCities = allOrgCityRelations.Where(a => a.IsRestricted == true).Select(a => a.CityId.Value);
                cityIds = cityIds.Except(allRestrictedCities).ToArray();
            }

            IEnumerable<KeyValueDto> cities = [];
            if (cityIds.Any())
            {
                var specialCities = await dataverseService.GetSpecialCitiesAsync();
                cities = specialCities.Join(cityIds, a => a.Id, a => a, (a, b) => new KeyValueDto { Key = a.Id, Value = a.CityNameCode, Code = a.CityCode });
            }

            return MessageResult.SuccessResult(cities);
        }

        /// <summary>
        /// 获取折扣大类列表
        /// </summary>
        /// <returns></returns>
        public async Task<List<GetDiscountCategoryResponseDto>> GetDiscountCategoryListAsync()
        {
            var _dataverseService = _serviceProvider.GetService<IDataverseService>();
            var discountCategoryList = (await _dataverseService.GetDictionariesAsync(DictionaryType.DiscountCategory)).ToList();
            var result = ObjectMapper.Map<List<DictionaryDto>, List<GetDiscountCategoryResponseDto>>(discountCategoryList);
            return result;
        }

        /// <summary>
        /// SOI核销结果返回接口
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> GetSOIWriteOffResultAsync(List<WriteOffResultRequestDto> request)
        {
            SetOperationLogRequestDto log = _commonService.InitOperationLog("SOI", "STikect核销", Json.JsonSerializer.Serialize(request));
            try
            {
                request = request.Distinct().OrderBy(o => o.Actiontimestamp).ToList();
                var sTicketRepository = LazyServiceProvider.LazyGetService<ISTicketApplicationRepository>();
                var querySTicket = (await sTicketRepository.GetQueryableAsync());

                var PONos = request.Select(s => s.PoNo).ToList();
                var sticketsQuery = querySTicket.Where(s => PONos.Contains(s.ApplicationCode)).AsEnumerable();
                //记录所有造作时间，
                var operationTimes = request.Select(s => s.Actiontimestamp).ToList();

                var sticketIds = sticketsQuery.Select(s => s.Id).ToList();

                if (sticketIds.Count == 0) return MessageResult.FailureResult("申请编号错误，未查到相应的申请单");
                var sTicketDetailRepository = LazyServiceProvider.LazyGetService<ISTicketApplicationDetailRepository>();

                var ISOIWriteoffResultReponsitory = LazyServiceProvider.LazyGetService<ISOIWriteoffResultReponsitory>();

                var SOIWriteoffResultDatas = (await ISOIWriteoffResultReponsitory.GetQueryableAsync()).AsNoTracking().Where(m => operationTimes.Contains(m.ActionTimeStamp.Value)).AsEnumerable();
                var sTicketDetailQuery = await sTicketDetailRepository.GetQueryableAsync();

                var lineNumbers = request.Select(s => s.LineNumber).ToList();

                if (lineNumbers.Count == 0) return MessageResult.SuccessResult();

                var sTicketDetail = await sTicketDetailQuery.Where(s => sticketIds.Contains(s.ParentID) && lineNumbers.Contains(s.RowId)).ToArrayAsync();
                IList<SOIWriteoffResult> SOIWriteoffResults = [];
                //预算退回
                List<ReturnBudgetRequestDto> returnBudgets = [];
                //已核销金额
                var subVerifiedAmount = decimal.Zero;
                List<Guid> settlementIds = [];
                int[] returnStatus = [14, 12, 22];
                HashSet<STicketApplicationDetail> returnBudDtai = [];
                foreach (var item in request)
                {
                    var mes = item.VerifySoiFocWriteOff();
                    if (!string.IsNullOrEmpty(mes)) throw new Exception(mes);
                    var sticket = sticketsQuery.FirstOrDefault(f => f.ApplicationCode == item.PoNo);
                    if (sticket == null) throw new Exception($"申请编号错误，未查到相应的申请：,{item.PoNo}");
                    if (sticket.DataSource == "BPM Migration")
                    {
                        sticketIds.Remove(sticket.Id);
                        continue;
                    }
                    var detail = sTicketDetail.FirstOrDefault(f => f.RowId == item.LineNumber && f.ParentID == sticket.Id);
                    if (detail == null) throw new Exception($"未查到相应的申请单：{item.PoNo};行号：{item.LineNumber}");
                    var isPostOpreation = SOIWriteoffResultDatas.Any(f => f.DetailId == detail.Id && f.ActionTimeStamp == item.Actiontimestamp);

                    if (isPostOpreation) continue;
                    subVerifiedAmount = decimal.Zero;
                    if (item.Flag == SettlementStatus.WriteOff)//核销
                    {
                        subVerifiedAmount = detail.SubVerifiedAmountRMB.HasValue ? detail.SubVerifiedAmountRMB.Value + item.Qty.Value : item.Qty.Value;

                        if (subVerifiedAmount > detail.SubTotalAmountRMB)
                            throw new Exception($"核销金额超过详情总金额。单号:{item.PoNo};行号:{item.LineNumber};总金额:{detail.SubTotalAmountRMB}");
                        else if (subVerifiedAmount < 0)
                        {
                            throw new Exception($"核销金额小于0。单号:{item.PoNo};行号:{item.LineNumber};总金额:{subVerifiedAmount}");
                        }
                        else
                            detail.SubVerifiedAmountRMB = subVerifiedAmount;

                        detail.SubVerificationStatus = item.Status;
                        detail.CutOffStatus = item.LockStatus;
                        if (item.CancelStatus == true)
                        {
                            detail.SubVerificationStatus = WriteOffStatus.NotWrittenOff;
                            detail.CutOffStatus = FreezeStatus.Cancel;
                        }
                    }
                    else //结算
                    {
                        subVerifiedAmount = detail.SettlementAmount.HasValue ? detail.SettlementAmount.Value + item.SettlementQty.Value : item.SettlementQty.Value;
                        if (subVerifiedAmount > detail.SubTotalAmountRMB)
                            throw new Exception($"结算金额超过详情总金额。单号:{item.PoNo};行号:{item.LineNumber};总金额:{detail.SubTotalAmountRMB}");
                        else if (subVerifiedAmount < 0)
                        {
                            throw new Exception($"结算金额小于0。单号:{item.PoNo};行号:{item.LineNumber};总金额:{subVerifiedAmount}");
                        }
                        else
                        {
                            settlementIds.Add(sticket.Id);
                            detail.SettlementAmount = subVerifiedAmount;
                        }

                    }
                    SOIWriteoffResult sOIWriteoffResult = new()
                    {
                        DetailId = detail.Id,
                        IsPass = item.Pass,
                        CancelStatus = item.CancelStatus,
                        FailureReason = item.CancelReason,
                        WriteOffQuantity = item.Qty,
                        ActionTimeStamp = item.Actiontimestamp,
                        Status = item.Status,
                        LockStatus = item.LockStatus,
                        Flag = item.Flag,
                        Type = item.Type,
                        SettlementQuantity = item.SettlementQty,
                        OrderNumber = item.OrderNumber,
                        BatchNumber = item.BatchNumber
                    };
                    //预算退回
                    var budgetReturnD = (detail.SubVerificationStatus.HasValue ? (int)detail.SubVerificationStatus * 10 : 1) + (detail.CutOffStatus.HasValue ? (int)detail.CutOffStatus : 1);
                    if (returnStatus.Contains(budgetReturnD))
                        returnBudDtai.Add(detail);
                    SOIWriteoffResults.Add(sOIWriteoffResult);
                }
                await sTicketDetailRepository.UpdateManyAsync(sTicketDetail, true);
                await ISOIWriteoffResultReponsitory.InsertManyAsync(SOIWriteoffResults, true);
                if (sticketIds.Count == 0)
                {
                    _commonService.LogResponse(log, "success");
                    return MessageResult.SuccessResult("提交成功!");
                }
                //插叙详情状态更改申请单状态
                var sTicketDetailNew = sTicketDetailQuery.Where(s => sticketIds.Contains(s.ParentID));
                //去掉反冲行和被反冲行
                var hedgesTicketDetails = sTicketDetailNew.GroupJoin(sTicketDetailNew, a => a.Id, b => b.HedgeDetailId, (a, b) => new { detail = a, hedge = b }).SelectMany(a => a.hedge.DefaultIfEmpty(), (a, b) => new { a.detail, b }).Where(m => m.detail.HedgeDetailId == null && m.b == null).Select(s => s.detail);
                var mapQuery = await hedgesTicketDetails.Select(s => new
                {
                    s.RowId,
                    s.Id,
                    s.SettlementAmount,
                    //unWrittenAmount = s.SubTotalAmountRMB - (s.SubVerifiedAmountRMB.HasValue ? s.SubVerifiedAmountRMB.Value : 0),
                    pendingSettlement = (s.SubVerifiedAmountRMB.HasValue ? s.SubVerifiedAmountRMB.Value : 0) - (s.SettlementAmount.HasValue ? s.SettlementAmount.Value : 0),
                    s.ParentID,
                    Status = (s.SubVerificationStatus.HasValue ? (int)s.SubVerificationStatus * 10 : 1) + (s.CutOffStatus.HasValue ? (int)s.CutOffStatus : 1)
                })
                    .Select(s => new
                    {
                        s.Id,
                        s.RowId,
                        s.ParentID,
                        Status =
                        s.Status == 11 ? 1 ://SOI未核销
                        s.Status == 14 ? 2 ://SOI已取消
                        s.Status == 12 ? 3 ://SOI已截止
                        s.Status == 21 ? 1 ://SOI部分核销
                        s.Status == 22 ? 3 ://SOI已截止
                        s.Status == 31 ? 4 ://SOI已核销
                        s.Status == 32 ? 4 ://SOI已核销
                        1,
                        SettlementAmount = s.SettlementAmount,
                        pendingSettlement = s.pendingSettlement,
                        //s.unWrittenAmount,
                    }).ToArrayAsync();//最多100行
                var StatusQuery = mapQuery.GroupBy(g => g.ParentID, (key, val) =>
                {
                    var settlementAmountSum = val.Sum(s => s.SettlementAmount);//总结算金额
                    var pendingSettlementSum = val.Sum(s => s.pendingSettlement);//待结算金额
                    var IsEquals = val.All(a => a.pendingSettlement == 0); //已核销等于已结算
                    var NotIsEquals = val.Any(a => a.pendingSettlement != 0); //任意已核销不等于已结算
                    var hasStatus1 = val.Any(a => a.Status == 1);//为SOl未核销或SOI部分核销
                    var allStatus2Or3 = val.All(a => a.Status == 2 || a.Status == 3);
                    var allNotStatus1 = val.All(a => a.Status != 1);

                    STicketStatus? Status = null;
                    if (hasStatus1 && IsEquals)
                        Status = STicketStatus.SOIInProcess;
                    else if (NotIsEquals)
                        Status = STicketStatus.PendingSettlement;
                    else if (allStatus2Or3 && settlementAmountSum == 0 && pendingSettlementSum == 0)
                        Status = STicketStatus.SOICancelled;
                    else if (allNotStatus1 && pendingSettlementSum == 0 && settlementAmountSum > 0)
                        Status = STicketStatus.Settled;
                    return new
                    {
                        key,
                        Status = Status
                    };
                });
                //需要发送邮件的核销申请
                var sticketEmails = new List<STicketApplication>();
                foreach (var s in sticketsQuery)
                {
                    var stutas = StatusQuery.FirstOrDefault(f => f.key == s.Id)?.Status;
                    if (stutas != null && s.Status != stutas)
                    {
                        s.Status = stutas.Value;
                        //推送SOI状态给CSS
                        await STicketSOIResultPushAsync(s);
                    }
                    if (settlementIds.Any(a => a == s.Id))
                    {
                        sticketEmails.Add(s);
                    }
                }
                foreach (var item in returnBudDtai)
                {
                    var s = sticketsQuery.First(f => f.Id == item.ParentID);
                    var useBudgetRequest = new ReturnBudgetRequestDto
                    {
                        PrId = s.Id,
                        SubbudgetId = s.SubBudgetId.Value,
                        Items = [new ReturnInfo
                        {
                            PdRowNo = item.RowId,
                            ReturnAmount =  item.SubTotalAmountRMB - (item.SubVerifiedAmountRMB ?? 0),
                            ReturnSourceId = item.Id,
                            ReturnSourceCode = s.ApplicationCode
                        }]
                    };
                    returnBudgets.Add(useBudgetRequest);
                }
                var result = await LazyServiceProvider.LazyGetService<ISubbudgetService>().ReturnSubbudgetAsync(returnBudgets);
                if (!result.Success) throw new Exception(result.Message);
                await sTicketRepository.UpdateManyAsync(sticketsQuery);
                await SendForSeetlementEmail(sticketEmails);
                _commonService.LogResponse(log, "success");
                return MessageResult.SuccessResult("提交成功!");
            }
            catch (Exception e)
            {
                _commonService.LogResponse(log, e.Message, false);
                return MessageResult.FailureResult(e.Message);
            }
        }
        /// <summary>
        /// Pushes the s ticket by soi asynchronous.
        /// </summary>
        /// <param name="Id">The identifier.</param>
        /// <returns></returns>
        public async Task<string> PushSTicketBySOIAsync(Guid Id, bool isImmediatePush = false)
        {
            try
            {
                var querySTicket = (await LazyServiceProvider.LazyGetService<ISTicketApplicationRepository>().GetQueryableAsync()).AsNoTracking();
                var sTicketDetailRepository = LazyServiceProvider.LazyGetService<ISTicketApplicationDetailRepository>();
                var sTicketDetailQuery = (await sTicketDetailRepository.GetQueryableAsync()).AsNoTracking();
                var storeQuery = (await LazyServiceProvider.LazyGetService<IStoreInfoRepository>().GetQueryableAsync()).AsNoTracking();
                //var queryableUser = (await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync()).AsNoTracking();

                //去掉反冲行和被反冲行
                var hedgesTicketDetails = sTicketDetailQuery.GroupJoin(sTicketDetailQuery, a => a.Id, b => b.HedgeDetailId, (a, b) => new { detail = a, hedge = b }).SelectMany(a => a.hedge.DefaultIfEmpty(), (a, b) => new { a.detail, b }).Where(m => m.detail.HedgeDetailId == null && m.b == null).Select(s => s.detail);
                var querySubBudget = (await LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>().GetQueryableAsync()).AsNoTracking();

                var query = await querySTicket.GroupJoin(hedgesTicketDetails, a => a.Id, b => b.ParentID, (a, b) => new { STicket = a, STicketDetail = b }).FirstAsync(f => f.STicket.Id == Id);
                var sTicketData = query.STicket;
                var sTicketDetailData = query.STicketDetail;
                var subBudget = querySubBudget.FirstOrDefault(f => f.Id == sTicketData.SubBudgetId);
                //var user = await queryableUser.FirstAsync(f => f.Id == subBudget.OwnerId);
                //var userDeptId = user.GetProperty<Guid?>(EntityConsts.IdentityUser.MainDepartmentId);
                var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
                var orgs = await dataverseService.GetOrganizations();
                var expenseNature = await dataverseService.GetCostNatureAsync();
                var BuCodeCfg = (await dataverseService.GetBuCodingCfgAsync(sTicketData.ApplyUserBuId.ToString())).FirstOrDefault();
                DepartmentDto org = orgs.FirstOrDefault(f => f.Id == sTicketData.ApplyUserDeptId);
                //DepartmentDto bu = orgs.First(f => f.Id == subBudget.BuId);
                //var applicationBuCode = orgs.FirstOrDefault(f => f.Id == sTicketData.ApplyUserBuId).OrgCode;
                STicketPushSOIRequestDto sTicket = new()
                {
                    PoRequest = sTicketData.ApplyUser,
                    PoPostDate = sTicketData.ApplyTime?.ToString("yyyy-MM-dd HH:mm:ss"),
                    PoFormNo = sTicketData.ApplicationCode,
                    CategoryCode = sTicketData.SubBudgetCode,
                    CategoryDes = subBudget?.Description,
                    CategoryOwner = sTicketData.BudgetOwner,
                    CategoryBUName = sTicketData.ApplyUserBuName,
                    CategoryDept = org?.DepartmentName,
                    PoExpenseCategory = sTicketData.ExpenseTypeName,
                    PoCompany = sTicketData.CompanyName,
                    PoBUDesc = sTicketData.ApplyUserBuName,
                    PoProduct = sTicketData.ProductName,
                    ItmWSName = sTicketData.ClientName,
                    ItmWSCode = sTicketData.ClientCode,
                    CustCode = sTicketData.ClientCode[^6..],
                    ItmPayType = sTicketData.ClientType,
                    PoRemark = sTicketData.Remark,
                    PoAmount = sTicketData.TotalAmountRMB.ToString(),
                    PoStatus = "生效",
                };
                var productCode = (await dataverseService.GetProductsAsync(sTicketData.ProductId.ToString())).FirstOrDefault()?.Code;
                var StoreIds = sTicketDetailData.Select(s => s.StoreId).ToList();
                var stores = storeQuery.Where(s => StoreIds.Contains(s.Id)).AsEnumerable();
                foreach (var item in sTicketDetailData)
                {
                    //var store = stores.FirstOrDefault(f => f.Id == item.StoreId);

                    var expenseoption = expenseNature.FirstOrDefault(f => f.Id == item.ExpenseNatureId)?.Expenseoption ?? *********;
                    var expenseoptionDec = ((ExpenseOptions)expenseoption).ToString();
                    STicketPushSOIDetailRequestDto sTicketPush = new()
                    {
                        ItmNo = item.RowId.ToString(),
                        ItmCOA = $"{sTicketData.CompanyCode}.{BuCodeCfg?.BuCode}.{sTicketData.CostCenterCode}.{item.ExpenseNatureCode}.{productCode}.{item.CityCode}.00",
                        //ItmCOA = "20.62.8524.14510.0700.00ZZ.00",
                        SettlementObjectCode = item.SettlementEntityCode,
                        SettlementObjectName = item.SettlementEntityName,
                        SettlementObjectType = "",
                        ExpenseNatureid = expenseoptionDec,// item.ExpenseNatureCode,
                        ExpenseNatureName = item.ExpenseNature,
                        CostCenterName = sTicketData.CostCenter,
                        City = sTicketData.CityName,
                        ItmDesc = item.Description,
                        ItmRegion = item.SettlementRegion,
                        ItmPeriodStart = item.SettlementPeriodStart?.ToString("yyyy-MM-dd"),
                        ItmPeriodEnd = item.SettlementPeriodEnd?.ToString("yyyy-MM-dd"),
                        ItmAmount = item.SubTotalAmountRMB.ToString(),
                        YYMM = item.PredictDate?.ToString("yyyy-MM")
                    };
                    sTicket.Details.Add(sTicketPush);
                }
                var json = Json.JsonSerializer.Serialize(sTicket);
                if (isImmediatePush)
                {
                    var appkey = _configuration["Integrations:SOI:AppKey"];
                    var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                    string encryptText = $"appkey={appkey}method=ReceiverEPODatatimestamp={timestamp}";
                    var Sign = encryptText.EncryptSHA3();
                    var token = await _httpRequestService.GetSOITokenAsync();
                    var requestData = new
                    {
                        appkey = appkey,
                        accessToken = token,
                        timestamp = timestamp,
                        method = "ReceiverEPOData",
                        Sign = Sign,
                        data = sTicket
                    };
                    var result = await _httpRequestService.HttpPostAsync<MessageResult>(json, $"{_configuration["Integrations:SOI:BaseUrl"]}/SOIWebAPI/api/EPO/ReceiverEPOData");
                    return result.Message;
                }

                //if (!result.Success)
                //{
                //    _logger.LogError("调用SOI接口报错：" + result.Message);
                //}
                return json;
            }
            catch (Exception e)
            {
                return e.Message;
            }
        }

        /// <summary>
        /// 验证预算
        /// </summary>
        /// <param name="prApplication"></param>
        /// <param name="detailItems"></param>
        /// <returns></returns>
        async Task<MessageResult> ValidateBudgetAsync(STicketApplication sticketApplication, IEnumerable<STicketApplicationDetail> detailItems)
        {
            //检查子预算是否足够
            var useBudgetRequest = new UseBudgetRequestDto
            {
                PrId = sticketApplication.Id,
                SubbudgetId = sticketApplication.SubBudgetId.Value,
                Items = detailItems.Select(a => new UseInfo { PdRowNo = a.RowId, UseAmount = a.SubTotalAmountRMB })
            };
            var result = await LazyServiceProvider.LazyGetService<ISubbudgetService>().CheckSubbudgetAmountSufficientAsync(useBudgetRequest);
            if (result.Success)
                result.Data = useBudgetRequest;

            return result;
        }

        /// <summary>
        /// 核销申请单使用子预算
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        async Task<MessageResult> UseSubbudgetAsync(UseBudgetRequestDto request, bool autoSave = false)
        {
            var result = await LazyServiceProvider.LazyGetService<ISubbudgetService>().CheckSubbudgetAmountSufficientAsync(request);
            if (!result.Success)
                return result;

            var useSubbudgetRespository = LazyServiceProvider.LazyGetService<IBdBudgetUseRepository>();
            var subbudgetRespository = LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>();
            var querySubbudget = await LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>().GetQueryableAsync();

            //check
            var totalUseAmount = request.Items.Select(x => x.UseAmount).Sum();
            var subbudgetData = querySubbudget.First(x => x.Id == request.SubbudgetId);

            var datas = new List<BdBudgetUse>();

            foreach (var item in request.Items)
            {
                datas.Add(new BdBudgetUse
                {
                    PrId = request.PrId,
                    PdRowNo = item.PdRowNo,
                    SubbudgetId = request.SubbudgetId,
                    Amount = item.UseAmount,
                    OperateTime = DateTime.Now,
                    IsEnable = true,
                    ProcessType = BudgetProcessTypes.STicket
                });
            }

            //获取历史数据
            var usedHistories = await useSubbudgetRespository.GetListAsync(x => x.PrId == request.PrId && x.SubbudgetId == request.SubbudgetId);
            //将之前的使用金额返还
            var historyAmount = usedHistories.Sum(a => a.Amount);
            totalUseAmount -= historyAmount;

            //预算使用之后 对应子预算的已用金额、可用金额 相应的更新
            subbudgetData.UesdAmount += totalUseAmount;

            //删除之前提交的使用明细（一个PR单、一个子预算只保留最近一次的使用明细）
            await useSubbudgetRespository.DeleteManyAsync(usedHistories, autoSave);
            await useSubbudgetRespository.InsertManyAsync(datas, autoSave);
            //await subbudgetRespository.UpdateAsync(subbudgetData, autoSave);

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 处理核销申请明细数据
        /// </summary>
        /// <param name="sticketApplication"></param>
        /// <param name="applicationDetailRequests"></param>
        /// <returns></returns>
        async Task<MessageResult> HandleSTicketApplicationDetailAsync(STicketApplication sticketApplication, IEnumerable<CreateUpdateSTicketApplicationDetailRequest> applicationDetailRequests)
        {
            List<STicketApplicationDetail> originalSTicketDetails = null, newSTicketDetails = null;
            if (applicationDetailRequests?.Any() == true)
            {
                var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
                var querySTicketDetail = await LazyServiceProvider.LazyGetService<ISTicketApplicationDetailRepository>().GetQueryableAsync();
                var sticketDetailRepository = LazyServiceProvider.LazyGetService<ISTicketApplicationDetailRepository>();
                var settlementEntityQuery = await LazyServiceProvider.LazyGetService<IStoreInfoRepository>().GetQueryableAsync();
                originalSTicketDetails = querySTicketDetail.Where(a => a.ParentID == sticketApplication.Id).ToList();

                newSTicketDetails = new List<STicketApplicationDetail>(originalSTicketDetails);

                var index = newSTicketDetails.Any() ? newSTicketDetails.Max(a => a.RowId) + 1 : 1;
                foreach (var item in applicationDetailRequests)
                {
                    STicketApplicationDetail detail;
                    if (item.Id.HasValue)
                    {
                        //已提交后的数据，不允许编辑明细行，只能反冲
                        if (sticketApplication.ApplyTime.HasValue)
                            continue;

                        detail = newSTicketDetails.First(a => a.Id == item.Id);
                        detail = ObjectMapper.Map(item, detail);
                        await sticketDetailRepository.UpdateAsync(detail);
                    }
                    else
                    {
                        detail = ObjectMapper.Map<CreateUpdateSTicketApplicationDetailRequest, STicketApplicationDetail>(item);
                        detail.RowId = index++;
                        detail.ParentID = sticketApplication.Id;
                        //城市主数据
                        CityMasterDataDto city = null;
                        if (item.CityId.HasValue)
                        {
                            var cities = await dataverseService.GetSpecialCitiesAsync(item.CityId.ToString());
                            city = cities.FirstOrDefault();
                        }
                        detail.CityCode = city?.CityCode;
                        detail.CityName = city?.Name;

                        //费用性质
                        CostNatureDto costNature = null;
                        if (item.ExpenseNatureId.HasValue)
                        {
                            var costNatures = await dataverseService.GetCostNatureAsync(item.ExpenseNatureId.ToString());
                            costNature = costNatures.FirstOrDefault();
                        }
                        detail.ExpenseNatureCode = costNature?.Code;
                        detail.ExpenseNature = costNature?.Name;

                        //结算对象
                        if (item.SettlementEntityId.HasValue)
                        {
                            var settlementEntity = settlementEntityQuery.Where(a => a.Id == item.SettlementEntityId).FirstOrDefault();
                            if (settlementEntity != null)
                            {
                                detail.SettlementEntityCode = settlementEntity.CustomerId;
                                detail.SettlementEntityName = settlementEntity.StoreName;
                                detail.SettlementEntityType = settlementEntity.DataSource;
                                detail.SettlementEntityChannel = settlementEntity.SubChannelCN;
                                if (!string.IsNullOrEmpty(settlementEntity.ParentCustomerId))
                                {
                                    detail.SettlementEntityHQCode = settlementEntity.ParentCustomerId;
                                    var parentCustomer = settlementEntityQuery.Where(a => a.CustomerId == settlementEntity.ParentCustomerId).FirstOrDefault();
                                    if (parentCustomer != null)
                                        detail.SettlementEntityHQName = parentCustomer.StoreName;
                                }
                            }
                        }

                        if (item.HedgeDetailId.HasValue)
                        {
                            detail.IsHedge = true;//反冲行
                            var hedgeDetail = newSTicketDetails.First(a => a.Id == detail.HedgeDetailId.Value);
                            hedgeDetail.IsHedge = true;//被反冲行
                            await sticketDetailRepository.UpdateAsync(hedgeDetail);
                        }

                        newSTicketDetails.Add(detail);
                        await sticketDetailRepository.InsertAsync(detail);
                    }
                }
            }

            return MessageResult.SuccessResult(new Tuple<List<STicketApplicationDetail>, List<STicketApplicationDetail>>(originalSTicketDetails, newSTicketDetails));
        }

        private async Task<MessageResult> GetCSSFile(string fileName)
        {
            var url = _configuration["Integrations:CSS:BaseUrl"]!.TrimEnd('/') + $"/Bpm/GetSettlementFile";
            var appSecret = _configuration["Integrations:CSS:AppSecret"];
            long unixTimestamp = DateTimeOffset.Now.ToUnixTimeSeconds();
            var parameters = new Dictionary<string, string>
            {
                {"channel", "NexBPM" },
                {"timestamp" ,unixTimestamp.ToString()}
            };
            string content = $"\"{fileName}\"";//业务数据（json字符串）
            string signature = CreateSign(parameters, appSecret, "sha256", content);
            //发送POST请求
            var requestBody = new CSSRequestDto
            {
                channel = "NexBPM",
                timestamp = unixTimestamp.ToString(),
                sign = signature,
                data = fileName
            };
            var headers = new Dictionary<string, string>
            {
                { "Content-Type", "application/json" }
            };
            var response = await url.WithHeaders(headers).PostJsonAsync(requestBody);
            //var responseData = await response.GetStringAsync();
            var responseStream = await response.GetStreamAsync();
            var formFile = new FormFile(responseStream, 0, responseStream.Length, "file", fileName)
            {
                Headers = new HeaderDictionary(),
                ContentType = "application/octet-stream"
            };

            var formFileCollection = new FormFileCollection
            {
                formFile
            };
            IFormFileCollection files = new FormFileCollection { formFile };
            if (!files.Any())
                return MessageResult.FailureResult("没有接收到附件信息");

            var list = new List<UploadFileDto>();
            foreach (var item in files)
            {
                using (var memoryStream = new MemoryStream())
                {
                    item.CopyTo(memoryStream);
                    list.Add(new UploadFileDto
                    {
                        FileName = item.FileName,
                        Size = item.Length,
                        Buffer = memoryStream.ToArray()
                    });
                }
            }

            var attachmentService = LazyServiceProvider.LazyGetService<IAttachmentService>();
            var result = await attachmentService.UploadFiles(FunctionModule.STICKET_STicketApplication_SupportDocs, list);

            return MessageResult.SuccessResult(result);
        }

        /// <summary>
        /// 创建CSS签名
        /// </summary>
        /// <param name="parameters"></param>
        /// <param name="secret"></param>
        /// <param name="signMethod"></param>
        /// <param name="content"></param>
        /// <returns></returns>
        private static string CreateSign(IDictionary<string, string> parameters, string secret, string signMethod, string content)
        {
            signMethod = string.IsNullOrEmpty(signMethod.Trim()) ? "sha256" : signMethod.ToLower();
            // 第一步：把字典按key的字母顺序排序
            IDictionary<string, string> sortedParams = new SortedDictionary<string, string>(parameters, StringComparer.Ordinal);
            IEnumerator<KeyValuePair<string, string>> dem = sortedParams.GetEnumerator();
            // 第二步：把所有参数名和参数值串在一起
            StringBuilder query = new StringBuilder();
            if (signMethod.Equals("sha256"))
            {
                query.Append(secret);
            }
            while (dem.MoveNext())
            {
                string key = dem.Current.Key;
                string value = dem.Current.Value;
                if (!string.IsNullOrEmpty(key) && !string.IsNullOrEmpty(value))
                {
                    query.Append(key).Append(value);
                }
            }
            if (!string.IsNullOrEmpty(content))
            {
                query.Append(content);
            }
            //第三步:使用sha256/HMAC加密
            byte[] bytes;
            if (signMethod.Equals("sha256"))
            {
                query.Append(secret);
                SHA256 sha256 = SHA256.Create();
                bytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(query.ToString()));
            }
            else
            {
                HMACMD5 hmac = new HMACMD5(Encoding.UTF8.GetBytes(secret));
                bytes = hmac.ComputeHash(Encoding.UTF8.GetBytes(query.ToString()));
            }
            // 第四步：把二进制转化为大写的十六进制
            StringBuilder result = new StringBuilder();
            for (int i = 0; i < bytes.Length; i++)
            {
                result.Append(bytes[i].ToString("X2"));
            }
            return result.ToString();
        }
        /// <summary>
        /// Sends the choose product email.
        /// </summary>
        /// <param name="info">The information.</param>
        /// <returns></returns>
        async Task SendForSeetlementEmail(List<STicketApplication> infos)
        {
            if (infos.Count == 0) return;
            var webRoot = LazyServiceProvider.LazyGetService<IWebHostEnvironment>().WebRootFileProvider;
            var html = await webRoot.GetFileInfo("Templates/Email/FOCShipments.html").ReadAsStringAsync();
            var _identityUserRepository = _serviceProvider.GetService<IIdentityUserRepository>();

            var applyUserIds = infos.Select(s => s.ApplyUserId).ToList();
            var users = await _identityUserRepository.GetListByIdsAsync(applyUserIds);
            List<InsertSendEmaillRecordDto> sendEmaillRecordls = [];
            foreach (var info in infos)
            {
                var bodyHtml = html;

                bodyHtml = bodyHtml.Replace("{Name}", info.ApplyUser);
                bodyHtml = bodyHtml.Replace("{Applicate}", info.ApplyUser);
                bodyHtml = bodyHtml.Replace("{ApplicationCode}", info.ApplicationCode);
                bodyHtml = bodyHtml.Replace("{ApplicationType}", "核销申请");
                bodyHtml = bodyHtml.Replace("{Link}", $"{_configuration["SpeakerEmail:WebHost"]?.TrimEnd('/')}/verify/wholesalerDetail/{info.Id}?type=detail");
                var user = users.FirstOrDefault(f => f.Id == info.ApplyUserId);
                if (user == null) continue;
                var sendEmaillRecords = new InsertSendEmaillRecordDto
                {
                    EmailAddress = user.Email,
                    Subject = $"NexBPM消息中心]由{info.ApplyUser}发起的【核销申请】进行结算",
                    Content = bodyHtml,
                    SourceType = EmailSourceType.STicketShipments,
                    Status = SendStatus.Pending,
                    Attempts = 0,
                };
                sendEmaillRecordls.Add(sendEmaillRecords);
            }
            //记录邮件，并触发邮件发送功能
            var emailService = LazyServiceProvider.LazyGetService<IEmailService>();
            await emailService.InsertSendEmaillRecordAsync(sendEmaillRecordls);
            // 调度作业
            BackgroundJob.Enqueue<SendEmailWorker>(a => a.DoWorkAsync());
        }

        async Task<MdmTokenResponseDto> GetMdmTokenAsync()
        {
            SetOperationLogRequestDto log;
            try
            {
                var url = $"{_configuration["Integrations:MDM:BaseUrl"]}/dmp/eventdriven/getToken";
                var ts = DateTimeOffset.Now.ToUnixTimeSeconds();
                var sign = ShaHelper.Sha512($"{ts}{_configuration["Integrations:MDM:AppSecret"]}");
                var data = new
                {
                    appId = _configuration["Integrations:MDM:AppId"],
                    ts,
                    sign
                };
                var response = await url.PostJsonAsync(data);
                var result = await response.GetJsonAsync<MdmTokenResponseDto>();

                log = _commonService.InitOperationLog("MDM", "GetMdmTokenAsync", $"登录成功");
                _commonService.LogResponse(log, JsonConvert.SerializeObject(result));

                return result;
            }
            catch (Exception ex)
            {
                log = _commonService.InitOperationLog("MDM", "GetMdmTokenAsync", $"登录失败");
                _commonService.LogResponse(log, ex.Message, false);

                Logger.LogError(ex, ex.Message);

                return default;
            }
        }
    }
}
