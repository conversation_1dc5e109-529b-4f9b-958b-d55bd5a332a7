-- dbo.vw_FOCSubBudgetDetails source

CREATE VIEW vw_FOCSubBudgetDetails AS
   SELECT
    fsb.Code AS SubBudgetCode,
    fsb.Description AS SubBudgetDescription,
    fmb.Code AS MasterBudgetCode,
    fmb.Description AS MasterBudgetDescription,
    fmb.Year AS MasterBudgetYear,
    ownerUser.Name AS SubBudgetOwnerName,
    masterOwnerUser.Name AS MasterBudgetOwnerName,  -- 新增主预算负责人姓名0317
    fsb.CostCenterName,
    fsb.RegionName,
    fsb.ProductMCode,
    ISNULL(SUM(CASE WHEN fbm.Status = 1 THEN 1 ELSE 0 END), 0) AS ActiveMonthCount,
    CASE 
        WHEN ISNULL(SUM(CASE WHEN fbm.Status = 1 THEN 1 ELSE 0 END), 0) > 1 
        THEN SUM(fbm.BudgetQty) - fsb.UesdQty 
    END AS RemainingQty,
    ISNULL(SUM(fapd.WriteoffQty), 0) AS TotalWriteoffQty,
    ISNULL(SUM(fapd.ShippedQty), 0) AS TotalShippedQty,
    ISNULL(SUM(fapd.WriteoffQty), 0) - ISNULL(SUM(fapd.ShippedQty), 0) AS RemainingWriteoffQty
FROM FocSubBudgets AS fsb
-- 关联主预算表
LEFT JOIN FocMasterBudgets AS fmb ON fsb.MasterBudgetId = fmb.Id
-- 关联子预算负责人姓名
LEFT JOIN AbpUsers AS ownerUser ON fsb.OwnerId = ownerUser.Id
-- 关联主预算负责人姓名
LEFT JOIN AbpUsers AS masterOwnerUser ON fmb.OwnerId = masterOwnerUser.Id  -- 新增关联
-- 关联成本中心名称
LEFT JOIN PLATFORM_ABBOTT_Dev.dbo.spk_costcentermasterdata AS cc ON fsb.CostCenterId = cc.spk_NexBPMCode  
-- 关联区域名称
LEFT JOIN PLATFORM_ABBOTT_Dev.dbo.spk_districtmasterdata AS dm ON fsb.RegionId = dm.spk_NexBPMCode
-- 关联月度预算表，计算开启状态的月份数量
LEFT JOIN FocMonthlyBudgets AS fbm ON fsb.id = fbm.SubBudgetId
-- 关联 FOC 单据产品明细表，计算核销数量和发运数量
LEFT JOIN FOCApplicationProductDetails AS fapd ON fsb.id = fapd.FOCDetailId
WHERE fbm.status = '1'
GROUP BY
    fsb.Code,
    fsb.Description,
    fmb.Code,
    fmb.Description,
    fmb.Year,
    ownerUser.Name,
    masterOwnerUser.Name,  -- 新增分组字段
    cc.spk_name,
    dm.spk_name,
    fsb.ProductMCode,
    fsb.UesdQty;