﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Abbott.SpeakerPortal.Contracts.Budget.Subbudget;
using Abbott.SpeakerPortal.Contracts.Dataverse;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Contracts.STicket;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Volo.Abp.Application.Dtos;
using static Abbott.SpeakerPortal.Enums.DataverseEnums;

namespace Abbott.SpeakerPortal.Contracts.FOC
{
    public interface IFocService
    {
        /// <summary>
        /// 分页获取MDM产品列表
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        Task<PagedResultDto<GetProductListResponseDto>> GetMDMProductListAsync(GetProductListRequestDto request);

        /// <summary>
        /// 分页获取MDM产品MCode列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<PagedResultDto<GetProductMCodeResponseDto>> GetProductMCodeListAsync(GetProductListRequestDto request);

        /// <summary>
        /// 根据组织Id获取关联的FOC成本中心
        /// </summary>
        /// <param name="orgId"></param>
        /// <returns></returns>
        Task<IEnumerable<KeyValuePair<Guid, string>>> GetFocCostcenterByOrgAsync(Guid? orgId);

        /// <summary>
        /// 获取FOC申请单详情
        /// </summary>
        /// <param name="id">FOC申请单Id</param>
        /// <returns></returns>
        Task<GetFocApplicationResponseDto> GetFocApplicationDetailsAsync(Guid id);

        /// <summary>
        /// 获取FOC申请单列表
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        Task<PagedResultDto<GetFocApplicationListResponseDto>> GetFocApplicationListAsync(GetFocApplicationListRequestDto request);

        /// <summary>
        /// 创建FOC申请单
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        Task<MessageResult> CreateFocApplicationAsync(CreateFocApplicationRequest request);

        /// <summary>
        /// 修改FOC申请单
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<MessageResult> UpdateFocApplicationAsync(UpdateFocApplicationRequest request);

        /// <summary>
        /// 保存草稿FOC申请单
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<MessageResult> SaveDraftFocApplicationAsync(CreateFocApplicationRequest request);

        /// <summary>
        /// 修改草稿FOC申请单
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<MessageResult> UpdateDraftFocApplicationAsync(UpdateFocApplicationRequest request);

        /// <summary>
        /// 删除FOC申请单
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        Task<MessageResult> DeleteFocApplicationAsync(Guid Id);

        /// <summary>
        /// 提交FOC申请单
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        Task<MessageResult> SubmitFocApplicationAsync(UpdateFocApplicationRequest request);

        /// <summary>
        /// 检查FOC申请单金额是否大于限定值
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<MessageResult> CheckFocApplicationAmountAsync(UpdateFocApplicationRequest request);

        /// <summary>
        /// FOC申请添加产品
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<MessageResult> CreateFocApplicationProductDetailAsync(CreateFocApplicationProductDetailRequest request);

        /// <summary>
        /// 获取发货类型
        /// </summary>
        /// <returns></returns>
        Task<List<GetShippingTypeResponseDto>> GetShippingTypeListAsync();

        /// <summary>
        /// 获取客户收货代码列表
        /// </summary>
        /// <returns></returns>
        Task<PagedResultDto<GetConsigneeResponseDto>> GetConsigneeListAsync(GetConsigneeRequestDto request);

        /// <summary>
        /// 获取终端列表
        /// </summary>
        /// <returns></returns>
        Task<List<GetFOCTerminalListResponseDto>> GetFOCTerminalListAsync();

        /// <summary>
        /// FOC物流填写
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<MessageResult> CreateFocProductLogisticsAsync(List<CreateFocLogisticsRequestDto> request);

        /// <summary>
        /// 获取物流发货记录
        /// </summary>
        /// <param name="focDetailId"></param>
        /// <returns></returns>
        Task<List<GetFocProductLogisticsResponseDto>> GetFocProductLogisticsAsync(Guid focDetailId);

        /// <summary>
        /// 获取FOC物流信息列表
        /// </summary>
        /// <returns></returns>
        Task<PagedResultDto<GetFocLogisticsListResponseDto>> GetFocLogisticsListAsync(GetFocLogisticsListRequestDto request);

        /// <summary>
        /// 解析批量上传物流信息excel
        /// </summary>
        /// <param name="excelDtos"></param>
        /// <returns></returns>
        Task<MessageResult> CheckCreateFocProductLogisticsExcelAsync(IEnumerable<CreateFocProductLogisticsExcelDto> excelDtos);

        /// <summary>
        /// 批量上传物流信息
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<MessageResult> SubmitBatchCreateFocProductLogisticsAsync(ImportDataResponseDto<AnalyzeFocProductLogisticsExcelResponseDto> request);

        /// <summary>
        /// 获取FOC子预算列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<PagedResultDto<GetFocSubBudgetsResponseDto>> GetFocSubBudgetInfosAsync(GetFocSubBudgetsRequestDto request);

        /// <summary>
        /// 获取FOC预算信息
        /// </summary>
        /// <param name="focSubBudgetId"></param>
        /// <returns></returns>
        Task<GetFocBudgetInfoResponse> GetFocSubBudgetInfoAsync(Guid focSubBudgetId);

        /// <summary>
        /// 获取FOC消费大类
        /// </summary>
        /// <returns></returns>
        Task<IEnumerable<ConsumeCategoryDto>> GetFocConsumeCategoryAsync();

        /// <summary>
        /// 导出物流信息
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        Task<Stream> ExportLogisticsInfoExcelAsync(Guid[] ids);

        /// <summary>
        /// 同步PP Region主数据
        /// </summary>
        /// <returns></returns>
        Task<string> SyncPPRegionDataAsync();

        /// <summary>
        /// 同步PP CostCenter主数据
        /// </summary>
        /// <returns></returns>
        Task<string> SyncPPCostCenterDataAsync();

        /// <summary>
        /// Gets the approved by me asynchronous.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        Task<PagedResultDto<GettFOCApprovedByMeResponseDto>> GetApprovedByMeAsync(GetFOCApprovedByMeRequestDto request);
        /// <summary>
        /// Gets the applied by me asynchronous.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        Task<PagedResultDto<GetFOCAppliedByMeResponseDto>> GetAppliedByMeAsync(GetFOCAppliedByMeRequestDto request);
        /// <summary>
        /// Pushes the foc for soi asynchronous.
        /// </summary>
        /// <param name="Id">The identifier.</param>
        /// <returns></returns>
        Task<MessageResult> PushFOCForSOIAsync(Guid Id);
        /// <summary>
        /// 获取FOC申请单草稿列表
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        Task<PagedResultDto<GetFocApplicationListResponseDto>> GetFocApplicationDraftListAsync(GetFocApplicationDraftListRequestDto request);
        /// <summary>
        /// FOc回调接口
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        Task<MessageResult> GetSOIWriteOffResultAsync(List<WriteOffResultRequestDto> request);
    }
}
