SELECT
 TRY_CONVERT(UNIQUE<PERSON>ENTIFIER, [Id]) [Id]
,TRY_CONVERT(UNIQUEIDENTIFIER, [PRDetailId]) [PRDetailId]
,[InterceptTypeCode]
,[InterceptStatus]
,TRY_CONVERT(UNIQUEIDENTIFIER, [spk_NexBPMCode]) [InterceptByUserId]
,[InterceptByUserName]
,[InterceptTime]
,[InterceptRemark]
,[InterceptTypeCode] as [ResolvedInterceptType]
,'{}' AS [ExtraProperties]
,'NULL' AS [ConcurrencyStamp]
,GETDATE() AS [CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CreatorId]) [CreatorId]
,[LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [LastModifierId]) [LastModifierId]
,0 AS [IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, [DeleterId]) [DeleterId]
,[DeletionTime]
INTO #OECIntercepts
FROM PLATFORM_ABBOTT_STG.dbo.OECIntercepts_ns

--drop table #OECIntercepts


USE Speaker_Portal_STG;

UPDATE a
SET
 a.[Id] = b.[Id]
,a.[PRDetailId] = b.[PRDetailId]
,a.[InterceptTypeCode] = b.[InterceptTypeCode]
,a.[InterceptStatus] = b.[InterceptStatus]
,a.[InterceptByUserId] = b.[InterceptByUserId]
,a.[InterceptByUserName] = b.[InterceptByUserName]
,a.[InterceptTime] = b.[InterceptTime]
,a.[InterceptRemark] = b.[InterceptRemark]
,a.[ResolvedInterceptType] = b.[ResolvedInterceptType]
,a.[ExtraProperties] = b.[ExtraProperties]
,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = b.[CreatorId]
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = b.[LastModifierId]
,a.[IsDeleted] = b.[IsDeleted]
,a.[DeleterId] = b.[DeleterId]
,a.[DeletionTime] = b.[DeletionTime]
FROM dbo.OECIntercepts a
left join #OECIntercepts  b
ON a.id=b.id


INSERT INTO dbo.OECIntercepts
SELECT
 [Id]
,[PRDetailId]
,[InterceptTypeCode]
,[InterceptStatus]
,[InterceptByUserId]
,[InterceptByUserName]
,[InterceptTime]
,[InterceptRemark]
,[ResolvedInterceptType]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
FROM #OECIntercepts a
WHERE not exists (select * from dbo.OECIntercepts where id=a.id)


--truncate table dbo.OECIntercepts
