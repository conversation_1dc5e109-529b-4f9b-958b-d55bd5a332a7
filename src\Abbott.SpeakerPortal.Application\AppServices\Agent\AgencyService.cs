﻿using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Agent;
using Abbott.SpeakerPortal.Contracts.Agent.Transferee;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.PowerApp;
using Abbott.SpeakerPortal.Contracts.Dataverse;
using Abbott.SpeakerPortal.Contracts.Integration.Veeva;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Dataverse.Contracts;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.Agent;
using Abbott.SpeakerPortal.Entities.EFlow.FOC;
using Abbott.SpeakerPortal.Entities.EFlow.FOC.Readonly;
using Abbott.SpeakerPortal.Entities.EFlow.STicket;
using Abbott.SpeakerPortal.Entities.EFlow.STicket.Readonly;
using Abbott.SpeakerPortal.Entities.OECSpeakerAuthApplys;
using Abbott.SpeakerPortal.Entities.Purchase.PurBDApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurBWApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurGRApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPOApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Entities.User;
using Abbott.SpeakerPortal.Entities.VendorApplications;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Utils;

using Microsoft.EntityFrameworkCore;
using Microsoft.PowerPlatform.Dataverse.Client.Extensions;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Messages;
using Microsoft.Xrm.Sdk.Query;

using Senparc.CO2NET;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using static Abbott.SpeakerPortal.Enums.Purchase;
using static Abbott.SpeakerPortal.Enums.PurGRStatus;
using static Abbott.SpeakerPortal.Enums.PurPAStatus;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace Abbott.SpeakerPortal.AppServices.Agent
{
    public class AgencyService : SpeakerPortalAppService, IAgencyService
    {
        public async Task<MessageResult> CreateAgentConfigAsync(CreateAgentConfigRequestDto request)
        {
            var dataverseRepository = LazyServiceProvider.LazyGetService<IDataverseRepository>();
            var agentConfigRepository = LazyServiceProvider.LazyGetService<IAgentConfigRepository>();
            var queryAgentConfig = await agentConfigRepository.GetQueryableAsync();

            queryAgentConfig = queryAgentConfig.Where(x => x.StartDate <= request.EndDate && x.EndDate >= request.StartDate && x.Status)
                .Where(x => x.AgentOperator == request.OriginalApprover || x.OriginalOperator == request.Agent)
                .WhereIf(request.BusinessType.HasValue, x => x.BusinessTypeCategory == request.BusinessType.Value || !x.BusinessTypeCategory.HasValue);

            var count = await queryAgentConfig.CountAsync();
            if (count > 0)
                return MessageResult.FailureResult("新增代理配置失败，代理人已设置了被代理或者被代理人已作为他人的代理");

            //验证输入：原审批人、代理人是否存在（同一流程相同的代理）；
            var query = new QueryExpression("spk_staffmasterdata");
            query.ColumnSet.AddColumns("spk_name", "spk_staffstate", "spk_staffemail", "spk_staffaccount");
            query.Criteria.AddCondition(new ConditionExpression("statecode", ConditionOperator.Equal, 0));
            query.Criteria.AddCondition(new ConditionExpression("spk_staffmasterdataid", ConditionOperator.In, new List<Guid> { request.OriginalApprover, request.Agent }));
            var staffEtts = await dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);
            if (staffEtts == null || staffEtts.Count() < 2)
                return MessageResult.FailureResult("原审批人或代理人错误");

            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var businessTypes = await dataverseService.GetBusinessTypeAsync();

            var originalApprover = staffEtts.FirstOrDefault(x => x.Id == request.OriginalApprover);
            var agentApprover = staffEtts.FirstOrDefault(x => x.Id == request.Agent);
            BusinessTypeDto businessType = null;

            var ett = new Entity("spk_agentconfiguration");

            var businessTypeName = request.BusinessType.HasValue ? request.BusinessType.GetDescription() : "全部";
            ett["spk_name"] = $"{originalApprover.GetAttributeValue<string>("spk_name")} 的代理：{agentApprover.GetAttributeValue<string>("spk_name")}({businessTypeName})";
            //ett["spk_name"] = "test001";
            //ett["spk_workflowname"] = new OptionSetValue((int)request.WorkflowType);
            if (request.BusinessType.HasValue)
            {
                businessType = businessTypes.FirstOrDefault(x => x.BusinessCategory == request.BusinessType);
                if (businessType != null)
                    ett["spk_businessflowname"] = new EntityReference("spk_businessflowtype", businessType.Id);
            }
            ett["spk_originalapprover"] = new EntityReference("spk_staffmasterdata", request.OriginalApprover);
            ett["spk_agent"] = new EntityReference("spk_staffmasterdata", request.Agent);
            ett["spk_startdate"] = request.StartDate;
            ett["spk_enddate"] = request.EndDate;
            ett["spk_isnotifyoriginalapprover"] = request.IsNotifyOriginalApprover;
            ett["spk_remark"] = request.Remark;

            var entitieExt = await dataverseRepository.DataverseClient.CreateAndReturnAsync(ett);
            if (entitieExt != null)
            {
                var agentConfig = new AgentConfig()
                {
                    //WorkflowType = request.WorkflowType,
                    BusinessTypeId = businessType?.Id,
                    BusinessTypeCategory = request.BusinessType,
                    BusinessTypeName = !request.BusinessType.HasValue ? "全部" : request.BusinessType.GetDescription(),
                    OriginalOperator = request.OriginalApprover,
                    AgentOperator = request.Agent,
                    StartDate = request.StartDate.Date,
                    EndDate = request.EndDate.Date,
                    Creator = (Guid)CurrentUser.Id,
                    Remark = request.Remark,
                    Status = true,
                    IsNotifyOriginalOperator = request.IsNotifyOriginalApprover,
                    DataverseId = entitieExt.Id
                };

                var agentConfigDt = await agentConfigRepository.InsertAsync(agentConfig);

                return MessageResult.SuccessResult(entitieExt.Id);
            }

            return MessageResult.FailureResult("新增代理配置失败");
        }

        public async Task<MessageResult> GetAgencyHistoryListAsync(GetAgencyHistoryListRequestDto request)
        {
            /*
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseRepository>();

            var query = new QueryExpression("spk_agencyhistory");
            query.ColumnSet.AddColumns("spk_formno", "spk_workflowname", "spk_agencytime", "createdby", "modifiedon", "statecode");
            query.Criteria.AddCondition(new ConditionExpression("statecode", ConditionOperator.Equal, 0));
            if (request.WorkflowType.HasValue)
                query.Criteria.AddCondition(new ConditionExpression("spk_workflowname", ConditionOperator.Equal, (int)(WorkflowTypeName)request.WorkflowType));

            query.PageInfo = new PagingInfo() { PageNumber = request.PageIndex + 1, Count = request.PageSize, ReturnTotalRecordCount = true };

            var etts = await dataverseService.DataverseClient.RetrieveMultipleAsync(query);

            var datas = etts.Entities.Select(x =>
            {
                var workflowType = x.GetAttributeValue<OptionSetValue>("spk_workflowname").Value;
                var workflowTypeName = EnumUtil.GetDescription((WorkflowTypeName)workflowType);
                var status = x.GetAttributeValue<OptionSetValue>("statecode");
                return new GetAgencyHistoryListResponseDto
                {
                    WorkflowType = (WorkflowTypeName)workflowType,
                    WorkflowTypeName = workflowTypeName,
                    FormNo = x.GetAttributeValue<string>("spk_formno"),
                    AgencyTime = x.GetAttributeValue<DateTime>("spk_agencytime"),
                    CreateByName = x.GetAttributeValue<EntityReference>("createdby").Name,
                    Status = status.Value == 0
                };
            });

            return MessageResult.SuccessResult(new PagedResultDto<GetAgencyHistoryListResponseDto>(etts.TotalRecordCount, datas.ToArray()));
            */

            var queryAgentHistory = await LazyServiceProvider.LazyGetService<IAgentHistoryRepository>().GetQueryableAsync();

            var query = queryAgentHistory.WhereIf(request.BusinessType.HasValue, x => x.BusinessTypeCategory == request.BusinessType)
                .WhereIf(request.AgentConfigId.HasValue, x => x.AgentConfigId == request.AgentConfigId);

            var count = query.Count();

            var pageDatas = query.Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToArray().Select(x =>
                new GetAgencyHistoryListResponseDto
                {
                    //WorkflowType = x.WorkflowType,
                    WorkflowTypeName = x.BusinessTypeCategory?.GetDescription(),
                    BusinessTypeId = x.BusinessTypeId,
                    BusinessTypeName = x.BusinessTypeName,
                    FormNo = x.FormCode,
                    FormId = x.FormId,
                    AgencyTime = x.AgentTime.ToString("yyyy-MM-dd HH:mm:ss"),
                    CreateTime = x.CreationTime.ToString("yyyy-MM-dd HH:mm:ss")
                });

            return MessageResult.SuccessResult(new PagedResultDto<GetAgencyHistoryListResponseDto>(count, pageDatas.ToArray()));
        }

        public async Task<MessageResult> GetAgentConfigListAsync(GetAgentConfigListRequestDto request)
        {
            var queryAgentConfig = await LazyServiceProvider.LazyGetService<IAgentConfigReadonlyRepository>().GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var users = await dataverseService.GetStaffs(stateCode: null);

            var query = queryAgentConfig.WhereIf(request.BusinessType.HasValue, x => x.BusinessTypeCategory == request.BusinessType)
                .WhereIf(request.Status.HasValue, x => x.Status == request.Status)
                .WhereIf(!request.IsAdmin, x => x.OriginalOperator == CurrentUser.Id)
                .WhereIf(request.Agent != null, x => x.AgentOperator == request.Agent)
                .WhereIf(request.OriginalOperator != null, x => x.OriginalOperator == request.OriginalOperator)
                .OrderByDescending(x => x.CreationTime);

            var count = query.Count();

            var pageDatas = query.Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToArray().Select(x =>
                new GetAgentConfigListResponseDto
                {
                    Id = x.Id,
                    //WorkflowType = x.WorkflowType,
                    //WorkflowTypeName = EnumUtil.GetDescription(x.WorkflowType),
                    BusinessType = x.BusinessTypeCategory,
                    BusinessTypeId = x.BusinessTypeId,
                    BusinessTypeName = x.BusinessTypeName,
                    OriginalApprover = x.OriginalOperator,
                    OriginalApproverName = users.FirstOrDefault(a => a.Id == x.OriginalOperator)?.Name,
                    Agent = x.AgentOperator,
                    AgentName = users.FirstOrDefault(a => a.Id == x.AgentOperator)?.Name,
                    StartDate = x.StartDate.ToString("yyyy-MM-dd"),
                    EndDate = x.EndDate.ToString("yyyy-MM-dd"),
                    IsNotifyOriginalApprover = x.IsNotifyOriginalOperator,
                    CreateByName = users.FirstOrDefault(a => a.Id == x.Creator)?.Name,
                    ModifiedOn = x.CreationTime.ToString("yyyy-MM-dd"),
                    Remark = x.Remark,
                    Status = x.Status
                });

            return MessageResult.SuccessResult(new PagedResultDto<GetAgentConfigListResponseDto>(count, pageDatas.ToArray()));
        }

        public async Task<MessageResult> SetAgentStatusAsync(Guid agentId, bool status)
        {
            var agentConfigRespository = LazyServiceProvider.LazyGetService<IAgentConfigRepository>();
            var queryAgentConfig = await LazyServiceProvider.LazyGetService<IAgentConfigRepository>().GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseRepository>();

            var agentConfigData = queryAgentConfig.FirstOrDefault(x => x.Id == agentId);

            if (agentConfigData == null)
                return MessageResult.FailureResult("找不到代理配置数据");

            var ett = await dataverseService.DataverseClient.RetrieveAsync("spk_agentconfiguration", agentConfigData.DataverseId, new ColumnSet("statecode", "statuscode"));

            if (ett == null)
                return MessageResult.FailureResult("找不到代理配置数据");

            ett["statecode"] = status ? new OptionSetValue(0) : new OptionSetValue(1);
            ett["statuscode"] = status ? new OptionSetValue(1) : new OptionSetValue(2);

            await dataverseService.DataverseClient.UpdateAsync(ett);

            //var result = dataverseService.DataverseClient.UpdateStateAndStatusForEntity("spk_agentconfiguration", agentId, status ? 0 : 1, ett.GetAttributeValue<OptionSetValue>("statuscode").Value);

            agentConfigData.Status = status;

            var res = await agentConfigRespository.UpdateAsync(agentConfigData);

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 根据代理操作人获取原操作人(含代理人)
        /// 默认情况下，代理操作人即当前登录用户
        /// </summary>
        /// <param name="request">代理操作人Id</param>
        /// <returns>当前代理人的所有目前有效的原操作人(包含自己)</returns>
        public async Task<IEnumerable<KeyValuePair<Guid, string>>> GetOriginalOperators(GetAgentOperatorsRequestDto request)
        {
            var queryAgentConfig = await LazyServiceProvider.LazyGetService<IAgentConfigReadonlyRepository>().GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var staffs = await dataverseService.GetStaffs();

            if (!request.AgentOperatorId.HasValue)
                request.AgentOperatorId = CurrentUser.Id;

            if (!request.AgentOperatorId.HasValue)
                return [];

            var dateTimeNow = DateTime.Now;
            var query = queryAgentConfig.Where(x => x.AgentOperator == request.AgentOperatorId && x.Status)
                .Where(x => x.StartDate <= dateTimeNow && x.EndDate > dateTimeNow.AddDays(-1))
                .WhereIf(request.BusinessTypeCategory.HasValue, x => !x.BusinessTypeId.HasValue || request.BusinessTypeCategory == x.BusinessTypeCategory);
            //.WhereIf(!request.BusinessTypeCategory.HasValue, x => !x.BusinessTypeId.HasValue);

            var count = query.Count();

            var currentAgentOperator = new KeyValuePair<Guid, string>((Guid)request.AgentOperatorId, staffs.FirstOrDefault(x => x.Id == request.AgentOperatorId)?.Name);

            if (count < 1)
                return [currentAgentOperator];

            var datas = query.ToArray().Select(a => a.OriginalOperator).Distinct().Select(b => new KeyValuePair<Guid, string>(b, staffs.FirstOrDefault(c => c.Id == b)?.Name));

            return datas.Append(currentAgentOperator);
        }

        /// <summary>
        /// 根据代理操作人获取原操作人(含代理人)
        /// 默认情况下，代理操作人即当前登录用户
        /// </summary>
        /// <param name="request">代理操作人Id</param>
        /// <returns>当前代理人的所有目前有效的原操作人(包含自己)，任务中心我发起的所有流程数量统计</returns>
        public async Task<IEnumerable<(Guid userId, ResignationTransfer.TaskFormCategory? category)>> GetOriginalOperatorsForTaskCenterPendingCount(GetAgentOperatorsRequestDto request)
        {
            var queryAgentConfig = await LazyServiceProvider.LazyGetService<IAgentConfigReadonlyRepository>().GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var staffs = await dataverseService.GetStaffs();

            if (!request.AgentOperatorId.HasValue)
                request.AgentOperatorId = CurrentUser.Id;

            if (!request.AgentOperatorId.HasValue)
                return [];

            var dateTimeNow = DateTime.Now;
            var query = queryAgentConfig.Where(x => x.AgentOperator == request.AgentOperatorId && x.Status)
                .Where(x => x.StartDate <= dateTimeNow && x.EndDate > dateTimeNow.AddDays(-1));

            var count = query.Count();

            var currentAgentOperator = ((Guid)request.AgentOperatorId, request.BusinessTypeCategory);

            if (count < 1)
                return [currentAgentOperator];

            var datas = query.ToArray().Select(a => new { a.OriginalOperator, a.BusinessTypeCategory }).Distinct().Select(a => (a.OriginalOperator, a.BusinessTypeCategory));

            return datas.Append(currentAgentOperator);
        }

        /// <summary>
        /// 根据原操作人获取代理人
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<KeyValuePair<Guid, string>?> GetAgentByOriginalOperator(GetAgentByOriginalOperatorRequestDto request = null)
        {
            if (request == null) return default;
            var queryAgentConfig = await LazyServiceProvider.LazyGetService<IAgentConfigReadonlyRepository>().GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();

            var staffs = await dataverseService.GetStaffs();
            var dateTimeNow = DateTime.Now;

            var query = queryAgentConfig.Where(x => x.OriginalOperator == request.OriginalOperatorId && x.Status)
                .Where(x => x.StartDate <= dateTimeNow && x.EndDate > dateTimeNow.AddDays(-1))
                .WhereIf(request.BusinessTypeCategory.HasValue, x => !x.BusinessTypeId.HasValue || request.BusinessTypeCategory == x.BusinessTypeCategory);
            var count = query.Count();
            if (count == 0)
                return null;
            var queryData = query.OrderByDescending(x => x.CreationTime).First();
            return new KeyValuePair<Guid, string>(queryData.AgentOperator, staffs.FirstOrDefault(x => x.Id == queryData.AgentOperator)?.Name);
        }

        #region 离职转办
        public async Task<MessageResult> GetDepartingApplicant(string name)
        {
            var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var datas = queryableUser.Where(a => a.IsActive && 0 == EF.Property<int>(a, EntityConsts.IdentityUser.JobStatus) && !string.IsNullOrEmpty(EF.Property<string>(a, EntityConsts.IdentityUser.StaffCode)))
                .WhereIf(!string.IsNullOrEmpty(name), a => a.Name.Contains(name))
                .Select(a => new List<KeyValuePair<Guid, string>> { new(a.Id, a.Name) })
                .ToArray();

            return MessageResult.SuccessResult(datas);
        }

        public async Task<MessageResult> GetTransfereeUsers()
        {
            var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var datas = queryableUser.Where(a => 1 == EF.Property<int>(a, EntityConsts.IdentityUser.JobStatus) && !string.IsNullOrEmpty(EF.Property<string>(a, EntityConsts.IdentityUser.StaffCode)))
                .Select(a => new List<KeyValuePair<Guid, string>> { new(a.Id, a.Name) })
                .ToArray();

            return MessageResult.SuccessResult(datas);
        }


        public async Task<MessageResult> GetApplyForms4Transferee(GetApplyFormsRequestDto request)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var commonService = LazyServiceProvider.LazyGetService<ICommonService>();

            var queryableUser = await LazyServiceProvider.LazyGetService<IIdentityUserReadonlyRepository>().GetQueryableAsync();
            //供应商申请SP-V
            var queryableSP = await LazyServiceProvider.LazyGetService<IVendorApplicationReadonlyRepository>().GetQueryableAsync();
            //采购申请PR-P
            var queryablePR = await LazyServiceProvider.LazyGetService<IPurPRApplicationReadonlyRepository>().GetQueryableAsync();
            //采购订单PO-O
            var queryablePO = await LazyServiceProvider.LazyGetService<IPurPOApplicationReadonlyRepository>().GetQueryableAsync();
            //供应商比价BD-B
            var queryableBD = await LazyServiceProvider.LazyGetService<IPurBDApplicationReadonlyRepository>().GetQueryableAsync();
            //收货GR-G
            var queryableGR = await LazyServiceProvider.LazyGetService<IPurGRApplicationReadonlyRepository>().GetQueryableAsync();
            //付款PA-A
            var queryablePA = await LazyServiceProvider.LazyGetService<IPurPAApplicationReadonlyRepository>().GetQueryableAsync();
            //竞价豁免BW-W
            var queryableBW = await LazyServiceProvider.LazyGetService<IPurBWApplicationReadonlyRepository>().GetQueryableAsync();
            //讲者授权申请
            var queryableSA = await LazyServiceProvider.LazyGetService<IOECSpeakerAuthApplyReadonlyRepository>().GetQueryableAsync();
            //核销申请
            var queryableSTicket = await LazyServiceProvider.LazyGetService<ISTicketApplicationReadonlyRepository>().GetQueryableAsync();
            //FOC申请
            var queryableFOC = await LazyServiceProvider.LazyGetService<IFocApplicationReadonlyRepository>().GetQueryableAsync();
            //离职员工
            var queryUser = queryableUser.Where(a => 0 == EF.Property<int>(a, EntityConsts.IdentityUser.JobStatus) && !string.IsNullOrEmpty(EF.Property<string>(a, EntityConsts.IdentityUser.StaffCode)));

            var queryList = new List<IQueryable<GetApplyFormsResponseDto>>();

            if (!request.FormCategory.HasValue || request.FormCategory == ResignationTransfer.TaskFormCategory.VendorApplication)
            {
                //Vendors：草稿状态不需转办，其他状态均可转办
                var querySP = queryUser.Join(queryableSP, a => a.Id, b => b.ApplyUserId, (a, b) => new { User = a, Apply = b })
                    .Where(x => x.Apply.Status != Statuses.Saved && !x.Apply.IsDeleted && !x.Apply.TransfereeId.HasValue)
                    .WhereIf(request.ApplyUserId.HasValue, x => x.Apply.ApplyUserId == request.ApplyUserId.Value)
                    .WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), x => x.Apply.ApplicationCode.Contains(request.ApplicationCode))
                    .Select(x => new GetApplyFormsResponseDto
                    {
                        Id = x.Apply.Id,
                        ApplicationCode = x.Apply.ApplicationCode,
                        FormStatus = (int)x.Apply.Status,
                        FormCategory = ResignationTransfer.TaskFormCategory.VendorApplication.GetDescription(),
                        ApplyUserId = x.Apply.ApplyUserId,
                        ApplyUserName = x.User.Name,
                        ApplyOrgId = x.Apply.ApplyUserBu,
                        ApplyOrgName = string.Empty,
                        ApplyDate = x.Apply.ApplyTime,
                        VendorType = x.Apply.VendorType,
                        ActionType = x.Apply.ApplicationType,
                        WaiverType = null
                    });
                queryList.Add(querySP);
            }

            if (!request.FormCategory.HasValue || request.FormCategory == ResignationTransfer.TaskFormCategory.PurchaseRequestApplication)
            {
                //PR：草稿状态不需转办，其他状态均可转办
                var queryPR = queryUser.Join(queryablePR, a => a.Id, b => b.ApplyUserId, (a, b) => new { User = a, Apply = b })
                    .Where(x => x.Apply.Status != PurPRApplicationStatus.Draft && !x.Apply.IsDeleted && !x.Apply.TransfereeId.HasValue)
                    .WhereIf(request.ApplyUserId.HasValue, x => x.Apply.ApplyUserId == request.ApplyUserId.Value)
                    .WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), x => x.Apply.ApplicationCode.Contains(request.ApplicationCode))
                    .Select(x => new GetApplyFormsResponseDto
                    {
                        Id = x.Apply.Id,
                        ApplicationCode = x.Apply.ApplicationCode,
                        FormStatus = (int)x.Apply.Status,
                        FormCategory = ResignationTransfer.TaskFormCategory.PurchaseRequestApplication.GetDescription(),
                        ApplyUserId = x.Apply.ApplyUserId,
                        ApplyUserName = x.Apply.ApplyUserIdName,
                        ApplyOrgId = x.Apply.ApplyUserDept.ToString(),
                        ApplyOrgName = x.Apply.ApplyUserDeptName,
                        ApplyDate = x.Apply.ApplyTime,
                        VendorType = null,
                        ActionType = null,
                        WaiverType = null
                    });
                queryList.Add(queryPR);
            }

            if (!request.FormCategory.HasValue || request.FormCategory == ResignationTransfer.TaskFormCategory.PurhcaseOrderApplication)
            {
                //采购订单：草稿状态不需转办，其他状态均可转办
                var queryPO = queryUser.Join(queryablePO, a => a.Id, b => b.ApplyUserId, (a, b) => new { User = a, Apply = b })
                    .Where(x => x.Apply.Status != PurOrderStatus.Draft && !x.Apply.IsDeleted && !x.Apply.TransfereeId.HasValue)
                    .WhereIf(request.ApplyUserId.HasValue, x => x.Apply.ApplyUserId == request.ApplyUserId.Value)
                    .WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), x => x.Apply.ApplicationCode.Contains(request.ApplicationCode))
                    //.WhereIf(request.FormCategory.HasValue && request.FormCategory != ResignationTransfer.FormCategory.PurchaseOrder, x => false)
                    .Select(x => new GetApplyFormsResponseDto
                    {
                        Id = x.Apply.Id,
                        ApplicationCode = x.Apply.ApplicationCode,
                        FormStatus = (int)x.Apply.Status,
                        FormCategory = ResignationTransfer.TaskFormCategory.PurhcaseOrderApplication.GetDescription(),
                        ApplyUserId = x.Apply.ApplyUserId,
                        ApplyUserName = x.Apply.ApplyUserName,
                        ApplyOrgId = x.Apply.ApplyUserDept.ToString(),
                        ApplyOrgName = x.Apply.ApplyUserBuToDeptName,
                        ApplyDate = x.Apply.ApplyTime,
                        VendorType = null,
                        ActionType = null,
                        WaiverType = null
                    });
                queryList.Add(queryPO);
            }

            if (!request.FormCategory.HasValue || request.FormCategory == ResignationTransfer.TaskFormCategory.BiddingApplication)
            {
                //比价申请：草稿状态不需转办，其他状态均可转办
                var queryBD = queryUser.Join(queryableBD, a => a.Id, b => b.ApplyUserId, (a, b) => new { User = a, Apply = b })
                    .Where(x => x.Apply.Status != PurBDStatus.Draft && !x.Apply.IsDeleted && !x.Apply.TransfereeId.HasValue)
                    .WhereIf(request.ApplyUserId.HasValue, x => x.Apply.ApplyUserId == request.ApplyUserId.Value)
                    .WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), x => x.Apply.ApplicationCode.Contains(request.ApplicationCode))
                    .Select(x => new GetApplyFormsResponseDto
                    {
                        Id = x.Apply.Id,
                        ApplicationCode = x.Apply.ApplicationCode,
                        FormStatus = (int)x.Apply.Status,
                        FormCategory = ResignationTransfer.TaskFormCategory.BiddingApplication.GetDescription(),
                        ApplyUserId = x.Apply.ApplyUserId,
                        ApplyUserName = x.Apply.ApplyUserName,
                        ApplyOrgId = x.Apply.ApplyUserBu.ToString(),
                        ApplyOrgName = string.Empty,
                        ApplyDate = x.Apply.ApplyTime,
                        VendorType = null,
                        ActionType = null,
                        WaiverType = null
                    });
                queryList.Add(queryBD);
            }

            if (!request.FormCategory.HasValue || request.FormCategory == ResignationTransfer.TaskFormCategory.GoodsReceiptApplication)
            {
                //收货申请：没有草稿状态，都可以转办
                var queryGR = queryUser.Join(queryableGR, a => a.Id, b => b.ApplyUserId, (a, b) => new { User = a, Apply = b })
                    .Where(x => !x.Apply.IsDeleted && !x.Apply.TransfereeId.HasValue)
                    .WhereIf(request.ApplyUserId.HasValue, x => x.Apply.ApplyUserId == request.ApplyUserId.Value)
                    .WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), x => x.Apply.ApplicationCode.Contains(request.ApplicationCode))
                    //.WhereIf(request.FormCategory.HasValue && request.FormCategory != ResignationTransfer.FormCategory.GoodsReceipt, x => false)
                    .Select(x => new GetApplyFormsResponseDto
                    {
                        Id = x.Apply.Id,
                        ApplicationCode = x.Apply.ApplicationCode,
                        FormStatus = (int)x.Apply.Status,
                        FormCategory = ResignationTransfer.TaskFormCategory.GoodsReceiptApplication.GetDescription(),
                        ApplyUserId = x.Apply.ApplyUserId,
                        ApplyUserName = x.Apply.ApplyUserName,
                        ApplyOrgId = x.Apply.ApplyUserBu.ToString(),
                        ApplyOrgName = x.Apply.ApplyUserBuToDeptName,
                        ApplyDate = x.Apply.ApplyTime,
                        VendorType = null,
                        ActionType = null,
                        WaiverType = null
                    });
                queryList.Add(queryGR);
            }

            if (!request.FormCategory.HasValue || request.FormCategory == ResignationTransfer.TaskFormCategory.PaymentApplication)
            {
                //付款申请：没有草稿状态，都可以转办
                var queryPA = queryUser.Join(queryablePA, a => a.Id, b => b.ApplyUserId, (a, b) => new { User = a, Apply = b })
                    .Where(x => !x.Apply.IsDeleted && !x.Apply.TransfereeId.HasValue)
                    .WhereIf(request.ApplyUserId.HasValue, x => x.Apply.ApplyUserId == request.ApplyUserId.Value)
                    .WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), x => x.Apply.ApplicationCode.Contains(request.ApplicationCode))
                    //.WhereIf(request.FormCategory.HasValue && request.FormCategory != ResignationTransfer.FormCategory.PaymentApplication, x => false)
                    .Select(x => new GetApplyFormsResponseDto
                    {
                        Id = x.Apply.Id,
                        ApplicationCode = x.Apply.ApplicationCode,
                        FormStatus = (int)x.Apply.Status,
                        FormCategory = ResignationTransfer.TaskFormCategory.PaymentApplication.GetDescription(),
                        ApplyUserId = x.Apply.ApplyUserId,
                        ApplyUserName = x.Apply.ApplyUserName,
                        ApplyOrgId = x.Apply.ApplyUserBu.ToString(),
                        ApplyOrgName = x.Apply.ApplyUserBuToDeptName,
                        ApplyDate = x.Apply.ApplyTime,
                        VendorType = null,
                        ActionType = null,
                        WaiverType = null
                    });
                queryList.Add(queryPA);
            }

            var BWApplicationCategories = new List<ResignationTransfer.TaskFormCategory> {
                ResignationTransfer.TaskFormCategory.BiddingWaiverApplication,
                ResignationTransfer.TaskFormCategory.JustificationApplication };
            if (!request.FormCategory.HasValue || BWApplicationCategories.Contains((ResignationTransfer.TaskFormCategory)request.FormCategory))
            {
                //竞价豁免/Justification：草稿状态不需转办，其他均可以转办
                var queryBW = queryUser.Join(queryableBW, a => a.Id, b => b.ApplyUserId, (a, b) => new { User = a, Apply = b })
                    .Where(x => x.Apply.Status != PurExemptStatus.Draft && !x.Apply.IsDeleted && !x.Apply.TransfereeId.HasValue)
                    .WhereIf(request.ApplyUserId.HasValue, x => x.Apply.ApplyUserId == request.ApplyUserId.Value)
                    .WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), x => x.Apply.ApplicationCode.Contains(request.ApplicationCode))
                    .WhereIf(request.FormCategory.HasValue && request.FormCategory == ResignationTransfer.TaskFormCategory.BiddingWaiverApplication, x => x.Apply.ExemptType == Enums.Purchase.ExemptType.Waiver)
                    .WhereIf(request.FormCategory.HasValue && request.FormCategory == ResignationTransfer.TaskFormCategory.JustificationApplication, x => x.Apply.ExemptType == Enums.Purchase.ExemptType.Justification)
                    .Select(x => new GetApplyFormsResponseDto
                    {
                        Id = x.Apply.Id,
                        ApplicationCode = x.Apply.ApplicationCode,
                        FormStatus = (int)x.Apply.Status,
                        //FormCategory = ResignationTransfer.TaskFormCategory.BiddingWaiverApplication.GetDescription(),
                        FormCategory = string.Empty,
                        ApplyUserId = x.Apply.ApplyUserId,
                        ApplyUserName = x.User.Name,
                        ApplyOrgId = x.Apply.ApplyDeptId.ToString(),
                        ApplyOrgName = string.Empty,
                        ApplyDate = x.Apply.ApplyTime,
                        VendorType = null,
                        ActionType = null,
                        WaiverType = x.Apply.ExemptType
                    });
                queryList.Add(queryBW);
            }

            if (!request.FormCategory.HasValue || request.FormCategory == ResignationTransfer.TaskFormCategory.OECSpeakerAuthApplication)
            {
                //讲者授权申请：没有草稿状态，都可以转办
                var querySA = queryUser.Join(queryableSA, a => a.Id, b => b.ApplyUserId, (a, b) => new { User = a, Apply = b })
                    .Where(x => !x.Apply.IsDeleted && !x.Apply.TransfereeId.HasValue)
                    .WhereIf(request.ApplyUserId.HasValue, x => x.Apply.ApplyUserId == request.ApplyUserId.Value)
                    .WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), x => x.Apply.ApplicationCode.Contains(request.ApplicationCode))
                    .Select(x => new GetApplyFormsResponseDto
                    {
                        Id = x.Apply.Id,
                        ApplicationCode = x.Apply.ApplicationCode,
                        FormStatus = (int)x.Apply.Status,
                        FormCategory = ResignationTransfer.TaskFormCategory.OECSpeakerAuthApplication.GetDescription(),
                        ApplyUserId = x.Apply.ApplyUserId,
                        ApplyUserName = x.Apply.ApplyUserName,
                        ApplyOrgId = x.Apply.ApplyUserDept.ToString(),
                        ApplyOrgName = x.Apply.ApplyUserDeptName,
                        ApplyDate = x.Apply.SubmitTime,
                        VendorType = null,
                        ActionType = null,
                        WaiverType = null
                    });
                queryList.Add(querySA);
            }

            if (!request.FormCategory.HasValue || request.FormCategory == ResignationTransfer.TaskFormCategory.STicketRequestApplication)
            {
                //核销申请：草稿状态不需转办，其他状态均可转办
                var querySTicket = queryUser.Join(queryableSTicket, a => a.Id, b => b.ApplyUserId, (a, b) => new { User = a, Apply = b })
                    .Where(x => x.Apply.Status != STicketStatus.Draft && !x.Apply.IsDeleted && !x.Apply.TransfereeId.HasValue)
                    .WhereIf(request.ApplyUserId.HasValue, x => x.Apply.ApplyUserId == request.ApplyUserId.Value)
                    .WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), x => x.Apply.ApplicationCode.Contains(request.ApplicationCode))
                    .Select(x => new GetApplyFormsResponseDto
                    {
                        Id = x.Apply.Id,
                        ApplicationCode = x.Apply.ApplicationCode,
                        FormStatus = (int)x.Apply.Status,
                        FormCategory = ResignationTransfer.TaskFormCategory.STicketRequestApplication.GetDescription(),
                        ApplyUserId = x.Apply.ApplyUserId,
                        ApplyUserName = x.Apply.ApplyUser,
                        ApplyOrgId = x.Apply.ApplyUserDeptId.ToString(),
                        ApplyOrgName = x.Apply.ApplyUserDeptName,
                        ApplyDate = x.Apply.ApplyTime,
                        VendorType = null,
                        ActionType = null,
                        WaiverType = null
                    });
                queryList.Add(querySTicket);
            }

            if (!request.FormCategory.HasValue || request.FormCategory == ResignationTransfer.TaskFormCategory.FOCRequestApplication)
            {
                //FOC申请：草稿状态不需转办，其他状态均可转办
                var queryFOC = queryUser.Join(queryableFOC, a => a.Id, b => b.ApplyUserId, (a, b) => new { User = a, Apply = b })
                    .Where(x => x.Apply.Status != FOCStatus.Draft && !x.Apply.IsDeleted && !x.Apply.TransfereeId.HasValue)
                    .WhereIf(!string.IsNullOrEmpty(request.ApplyUserName), x => x.Apply.ApplyUser.Contains(request.ApplyUserName))
                    .WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), x => x.Apply.ApplicationCode.Contains(request.ApplicationCode))
                    .Select(x => new GetApplyFormsResponseDto
                    {
                        Id = x.Apply.Id,
                        ApplicationCode = x.Apply.ApplicationCode,
                        FormStatus = (int)x.Apply.Status,
                        FormCategory = ResignationTransfer.TaskFormCategory.FOCRequestApplication.GetDescription(),
                        ApplyUserId = x.Apply.ApplyUserId,
                        ApplyUserName = x.Apply.ApplyUser,
                        ApplyOrgId = x.Apply.ApplyUserDeptId.ToString(),
                        ApplyOrgName = x.Apply.ApplyUserDeptName,
                        ApplyDate = x.Apply.ApplyTime,
                        VendorType = null,
                        ActionType = null,
                        WaiverType = null
                    });
                queryList.Add(queryFOC);
            }

            //var query = querySP.Union(queryPR).Union(queryPO).Union(queryBD).Union(queryGR).Union(queryPA).Union(queryBW);
            //query = querySP.Concat(queryPR).Concat(queryPO).Concat(queryBD).Concat(queryGR).Concat(queryPA).Concat(queryBW);
            //queryList.AddRange([querySP, queryPR, queryPO, queryBD, queryGR, queryPA, queryBW]);
            //IQueryable<GetApplyFormsResponseDto> query = Expression<Func<GetApplyFormsResponseDto> ()=> true;

            var query = queryList.Count != 0 ? queryList[0] : null;

            if (query == null)
                return MessageResult.SuccessResult(new PagedResultDto<GetApplyFormsResponseDto>(0, []));

            for (int i = 1; i < queryList.Count; i++)
                query = query.Concat(queryList[i]);

            var count = query.Count();

            if (count < 1)
                return MessageResult.SuccessResult(new PagedResultDto<GetApplyFormsResponseDto>(0, []));

            var pageDatas = query.OrderBy(x => x.ApplyDate).Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToArray();

            var orgs = await dataverseService.GetOrganizations(stateCode: null);

            foreach (var item in pageDatas)
            {
                if (string.IsNullOrEmpty(item.ApplyOrgId) || !string.IsNullOrEmpty(item.ApplyOrgName))
                    continue;
                var orgDto = orgs.FirstOrDefault(x => x.Id == Guid.Parse(item.ApplyOrgId));
                if (orgDto == null)
                    continue;
                var orgTree2BU = await commonService.GetParentOrgs(orgDto, orgs);
                item.ApplyOrgName = orgTree2BU.Select(x => x.DepartmentName).Reverse().JoinAsString("-");
            }

            return MessageResult.SuccessResult(new PagedResultDto<GetApplyFormsResponseDto>(count, pageDatas));
        }

        public async Task<MessageResult> GetWorkflowTask4Transferee(GetWorkflowTaskRequestDto request)
        {
            var dataverseRepository = LazyServiceProvider.LazyGetService<IDataverseRepository>();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var commonService = LazyServiceProvider.LazyGetService<ICommonService>();

            var queryableUser = await LazyServiceProvider.LazyGetService<IIdentityUserReadonlyRepository>().GetQueryableAsync();
            //供应商申请SP-V
            var queryableSP = await LazyServiceProvider.LazyGetService<IVendorApplicationReadonlyRepository>().GetQueryableAsync();
            //采购申请PR-P
            var queryablePR = await LazyServiceProvider.LazyGetService<IPurPRApplicationReadonlyRepository>().GetQueryableAsync();
            //采购订单PO-O
            var queryablePO = await LazyServiceProvider.LazyGetService<IPurPOApplicationReadonlyRepository>().GetQueryableAsync();
            //供应商比价BD-B
            var queryableBD = await LazyServiceProvider.LazyGetService<IPurBDApplicationReadonlyRepository>().GetQueryableAsync();
            //收货GR-G
            var queryableGR = await LazyServiceProvider.LazyGetService<IPurGRApplicationReadonlyRepository>().GetQueryableAsync();
            //付款PA-A
            var queryablePA = await LazyServiceProvider.LazyGetService<IPurPAApplicationReadonlyRepository>().GetQueryableAsync();
            //竞价豁免BW-W
            var queryableBW = await LazyServiceProvider.LazyGetService<IPurBWApplicationReadonlyRepository>().GetQueryableAsync();
            //讲者授权申请SA-SA
            var queryableSA = await LazyServiceProvider.LazyGetService<IOECSpeakerAuthApplyReadonlyRepository>().GetQueryableAsync();
            //核销申请
            var queryableSTicket = await LazyServiceProvider.LazyGetService<ISTicketApplicationReadonlyRepository>().GetQueryableAsync();
            //FOC申请
            var queryableFOC = await LazyServiceProvider.LazyGetService<IFocApplicationReadonlyRepository>().GetQueryableAsync();
            //离职员工
            //var queryUser = queryableUser.Where(a => 0 == EF.Property<int>(a, EntityConsts.IdentityUser.JobStatus) && !string.IsNullOrEmpty(EF.Property<string>(a, EntityConsts.IdentityUser.StaffCode)));

            var query = new QueryExpression("spk_workflowtask");
            query.ColumnSet.AddColumns("spk_approver", "spk_businessformno", "spk_businessformid", "spk_approvalstatus");
            query.Criteria.AddCondition(new ConditionExpression("statecode", ConditionOperator.Equal, 0));
            query.Criteria.AddCondition(new ConditionExpression("spk_approvalstatus", ConditionOperator.Equal, 100000000));
            //query.Criteria.AddCondition(new ConditionExpression("spk_businessformno", ConditionOperator.NotNull));
            if (!string.IsNullOrEmpty(request.ApplicationCode))
                query.Criteria.AddCondition(new ConditionExpression("spk_businessformno", ConditionOperator.Like, $"%{request.ApplicationCode}%"));
            string prefix = "V";
            if (request.FormCategory.HasValue)
            {
                switch (request.FormCategory)
                {
                    case ResignationTransfer.TaskFormCategory.VendorApplication:
                        prefix = "V";
                        break;
                    case ResignationTransfer.TaskFormCategory.PurchaseRequestApplication:
                        prefix = "P";
                        break;
                    case ResignationTransfer.TaskFormCategory.PurhcaseOrderApplication:
                        prefix = "O";
                        break;
                    case ResignationTransfer.TaskFormCategory.BiddingApplication:
                        prefix = "B";
                        break;
                    case ResignationTransfer.TaskFormCategory.GoodsReceiptApplication:
                        prefix = "G";
                        break;
                    case ResignationTransfer.TaskFormCategory.PaymentApplication:
                        prefix = "A";
                        break;
                    case ResignationTransfer.TaskFormCategory.BiddingWaiverApplication:
                        prefix = "W";
                        break;
                    case ResignationTransfer.TaskFormCategory.JustificationApplication:
                        prefix = "J";
                        break;
                    case ResignationTransfer.TaskFormCategory.STicketRequestApplication:
                        prefix = "S";
                        break;
                    case ResignationTransfer.TaskFormCategory.FOCRequestApplication:
                        prefix = "F";
                        break;
                    case ResignationTransfer.TaskFormCategory.OECSpeakerAuthApplication:
                        prefix = "SA";
                        break;

                    default:
                        break;
                }
                query.Criteria.AddCondition(new ConditionExpression("spk_businessformno", ConditionOperator.BeginsWith, prefix));
            }

            var linkStaff = query.AddLink("spk_staffmasterdata", "spk_approver", "spk_staffmasterdataid");
            linkStaff.LinkCriteria.AddCondition(new ConditionExpression("spk_staffstate", ConditionOperator.Equal, 923180001));//离职
            //if (!string.IsNullOrEmpty(request.Approval))
            //    linkStaff.LinkCriteria.AddCondition(new ConditionExpression("spk_name", ConditionOperator.Like, $"%{request.Approval}%"));
            if (request.ApproverId.HasValue)
                linkStaff.LinkCriteria.AddCondition(new ConditionExpression("spk_staffmasterdataid", ConditionOperator.Equal, (Guid)request.ApproverId.Value));

            linkStaff.Columns.AddColumn("spk_name");
            linkStaff.EntityAlias = "staff";

            query.AddOrder("createdon", OrderType.Ascending);
            //query.PageInfo.PageNumber = request.PageIndex == 0 ? request.PageIndex + 1 : request.PageIndex;
            //query.PageInfo.Count = request.PageSize;
            query.PageInfo.ReturnTotalRecordCount = true;

            var entityCollection = await dataverseRepository.DataverseClient.RetrieveMultipleAsync(query);

            if (entityCollection.Entities.Count < 1)
                return MessageResult.SuccessResult(new PagedResultDto<GetWorkflowTaskResponseDto>(0, []));

            var tasks = entityCollection.Entities.Select(x => new
            {
                x.Id,
                FormId = x.GetAttributeValue<string>("spk_businessformid"),
                FormNo = x.GetAttributeValue<string>("spk_businessformno"),
                ApproverId = x.GetAttributeValue<EntityReference>("spk_approver")?.Id,
                ApproverName = x.GetAttributeValue<AliasedValue>("staff.spk_name").Value?.ToString()
            });

            //关联业务表(从SQLSERVER)
            var pageDatas = new List<GetWorkflowTaskResponseDto>();

            if (!request.FormCategory.HasValue || request.FormCategory == ResignationTransfer.TaskFormCategory.VendorApplication)
            {
                var bizTasks = tasks.Where(x => x.FormNo.StartsWith('V')).ToList();
                if (bizTasks.Count > 0)
                {
                    var applys = queryableSP.Where((x => x.Status == Statuses.Approving && !x.IsDeleted && bizTasks.Select(v => v.FormNo).Distinct().Contains(x.ApplicationCode)))
                        .Join(queryableUser, a => a.ApplyUserId, b => b.Id, (a, b) => new { Apply = new { a.ApplicationCode, a.Status, a.ApplyUserId, a.ApplyUserBu, a.ApplyTime }, User = new { b.Name } });
                    if (applys.Any())
                    {
                        var formNos = applys.ToArray().Select(x => x.Apply.ApplicationCode).Distinct();
                        var intersect = bizTasks.Select(x => x.FormNo).Distinct().Intersect(formNos);
                        if (intersect.Any())
                        {
                            pageDatas.AddRange(bizTasks.Where(x => intersect.Contains(x.FormNo)).Select(x =>
                            {
                                var apply = applys.FirstOrDefault(a => a.Apply.ApplicationCode == x.FormNo);
                                var resDto = new GetWorkflowTaskResponseDto { Approval = x.ApproverName, Id = x.Id, ApplicationCode = x.FormNo };
                                if (apply != null)
                                {
                                    resDto.FormStatus = (int)apply.Apply.Status;
                                    resDto.FormCategory = ResignationTransfer.TaskFormCategory.VendorApplication.GetDescription();
                                    resDto.ApplyUserId = apply.Apply.ApplyUserId;
                                    resDto.ApplyUserName = apply.User.Name;
                                    resDto.ApplyOrgId = apply.Apply.ApplyUserBu.ToString();
                                    resDto.ApplyOrgName = string.Empty;
                                    resDto.ApplyDate = apply.Apply.ApplyTime;
                                }
                                return resDto;
                            }));
                        }
                    }
                }
            }

            if (!request.FormCategory.HasValue || request.FormCategory == ResignationTransfer.TaskFormCategory.PurchaseRequestApplication)
            {
                var bizTasks = tasks.Where(x => x.FormNo.StartsWith('P')).ToList();
                if (bizTasks.Count > 0)
                {
                    var applys = queryablePR.Where((x => x.Status == PurPRApplicationStatus.Approving && !x.IsDeleted && bizTasks.Select(v => v.FormNo).Distinct().Contains(x.ApplicationCode)))
                        .Join(queryableUser, a => a.ApplyUserId, b => b.Id, (a, b) => new { Apply = new { a.ApplicationCode, a.Status, a.ApplyUserId, a.ApplyUserDept, a.ApplyUserDeptName, a.ApplyTime }, User = new { b.Name } });
                    if (applys.Any())
                    {
                        var formNos = applys.ToArray().Select(x => x.Apply.ApplicationCode).Distinct();
                        var intersect = bizTasks.Select(x => x.FormNo).Distinct().Intersect(formNos);
                        if (intersect.Any())
                        {
                            pageDatas.AddRange(bizTasks.Where(x => intersect.Contains(x.FormNo)).Select(x =>
                            {
                                var apply = applys.FirstOrDefault(a => a.Apply.ApplicationCode == x.FormNo);
                                var resDto = new GetWorkflowTaskResponseDto { Approval = x.ApproverName, Id = x.Id, ApplicationCode = x.FormNo };
                                if (apply != null)
                                {
                                    resDto.FormStatus = (int)apply.Apply.Status;
                                    resDto.FormCategory = ResignationTransfer.TaskFormCategory.PurchaseRequestApplication.GetDescription();
                                    resDto.ApplyUserId = apply.Apply.ApplyUserId;
                                    resDto.ApplyUserName = apply.User.Name;
                                    resDto.ApplyOrgId = apply.Apply.ApplyUserDept.ToString();
                                    resDto.ApplyOrgName = apply.Apply.ApplyUserDeptName;
                                    resDto.ApplyDate = apply.Apply.ApplyTime;
                                }
                                return resDto;
                            }));
                        }
                    }
                }
            }

            if (!request.FormCategory.HasValue || request.FormCategory == ResignationTransfer.TaskFormCategory.PurhcaseOrderApplication)
            {
                var bizTasks = tasks.Where(x => x.FormNo.StartsWith('O')).ToList();
                if (bizTasks.Count > 0)
                {
                    var applys = queryablePO.Where((x => x.Status == PurOrderStatus.Approving && !x.IsDeleted && bizTasks.Select(v => v.FormNo).Distinct().Contains(x.ApplicationCode)))
                        .Join(queryableUser, a => a.ApplyUserId, b => b.Id, (a, b) => new { Apply = new { a.ApplicationCode, a.Status, a.ApplyUserId, a.ApplyUserBu, a.ApplyUserBuToDeptName, a.ApplyTime }, User = new { b.Name } });
                    if (applys.Any())
                    {
                        var formNos = applys.ToArray().Select(x => x.Apply.ApplicationCode).Distinct();
                        var intersect = bizTasks.Select(x => x.FormNo).Distinct().Intersect(formNos);
                        if (intersect.Any())
                        {
                            pageDatas.AddRange(bizTasks.Where(x => intersect.Contains(x.FormNo)).Select(x =>
                            {
                                var apply = applys.FirstOrDefault(a => a.Apply.ApplicationCode == x.FormNo);
                                var resDto = new GetWorkflowTaskResponseDto { Approval = x.ApproverName, Id = x.Id, ApplicationCode = x.FormNo };
                                if (apply != null)
                                {
                                    resDto.FormStatus = (int)apply.Apply.Status;
                                    resDto.FormCategory = ResignationTransfer.TaskFormCategory.PurhcaseOrderApplication.GetDescription();
                                    resDto.ApplyUserId = apply.Apply.ApplyUserId;
                                    resDto.ApplyUserName = apply.User.Name;
                                    resDto.ApplyOrgId = apply.Apply.ApplyUserBu.ToString();
                                    resDto.ApplyOrgName = apply.Apply.ApplyUserBuToDeptName;
                                    resDto.ApplyDate = apply.Apply.ApplyTime;
                                }
                                return resDto;
                            }));
                        }
                    }
                }
            }

            if (!request.FormCategory.HasValue || request.FormCategory == ResignationTransfer.TaskFormCategory.BiddingApplication)
            {
                var bizTasks = tasks.Where(x => x.FormNo.StartsWith('B')).ToList();
                if (bizTasks.Count > 0)
                {
                    var applys = queryableBD.Where((x => x.Status == PurBDStatus.Approving && !x.IsDeleted && bizTasks.Select(v => v.FormNo).Distinct().Contains(x.ApplicationCode)))
                        .Join(queryableUser, a => a.ApplyUserId, b => b.Id, (a, b) => new { Apply = new { a.ApplicationCode, a.Status, a.ApplyUserId, a.ApplyUserBu, a.ApplyTime }, User = new { b.Name } });
                    if (applys.Any())
                    {
                        var formNos = applys.ToArray().Select(x => x.Apply.ApplicationCode).Distinct();
                        var intersect = bizTasks.Select(x => x.FormNo).Distinct().Intersect(formNos);
                        if (intersect.Any())
                        {
                            pageDatas.AddRange(bizTasks.Where(x => intersect.Contains(x.FormNo)).Select(x =>
                            {
                                var apply = applys.FirstOrDefault(a => a.Apply.ApplicationCode == x.FormNo);
                                var resDto = new GetWorkflowTaskResponseDto { Approval = x.ApproverName, Id = x.Id, ApplicationCode = x.FormNo };
                                if (apply != null)
                                {
                                    resDto.FormStatus = (int)apply.Apply.Status;
                                    resDto.FormCategory = ResignationTransfer.TaskFormCategory.BiddingApplication.GetDescription();
                                    resDto.ApplyUserId = apply.Apply.ApplyUserId;
                                    resDto.ApplyUserName = apply.User.Name;
                                    resDto.ApplyOrgId = apply.Apply.ApplyUserBu.ToString();
                                    resDto.ApplyDate = apply.Apply.ApplyTime;
                                }
                                return resDto;
                            }));
                        }
                    }
                }
            }

            if (!request.FormCategory.HasValue || request.FormCategory == ResignationTransfer.TaskFormCategory.GoodsReceiptApplication)
            {
                var bizTasks = tasks.Where(x => x.FormNo.StartsWith('G')).ToList();
                if (bizTasks.Count > 0)
                {
                    var transferableStatusGR = new PurGRApplicationStatus[] { PurGRApplicationStatus.SignedBy, PurGRApplicationStatus.Termination };
                    var applys = queryableGR.Where((x => transferableStatusGR.Contains(x.Status) && !x.IsDeleted && bizTasks.Select(v => v.FormNo).Distinct().Contains(x.ApplicationCode)))
                        .Join(queryableUser, a => a.ApplyUserId, b => b.Id, (a, b) => new { Apply = new { a.ApplicationCode, a.Status, a.ApplyUserId, a.ApplyUserBu, a.ApplyUserBuToDeptName, a.ApplyTime }, User = new { b.Name } });
                    if (applys.Any())
                    {
                        var formNos = applys.ToArray().Select(x => x.Apply.ApplicationCode).Distinct();
                        var intersect = bizTasks.Select(x => x.FormNo).Distinct().Intersect(formNos);
                        if (intersect.Any())
                        {
                            pageDatas.AddRange(bizTasks.Where(x => intersect.Contains(x.FormNo)).Select(x =>
                            {
                                var apply = applys.FirstOrDefault(a => a.Apply.ApplicationCode == x.FormNo);
                                var resDto = new GetWorkflowTaskResponseDto { Approval = x.ApproverName, Id = x.Id, ApplicationCode = x.FormNo };
                                if (apply != null)
                                {
                                    resDto.FormStatus = (int)apply.Apply.Status;
                                    resDto.FormCategory = ResignationTransfer.TaskFormCategory.GoodsReceiptApplication.GetDescription();
                                    resDto.ApplyUserId = apply.Apply.ApplyUserId;
                                    resDto.ApplyUserName = apply.User.Name;
                                    resDto.ApplyOrgId = apply.Apply.ApplyUserBu.ToString();
                                    resDto.ApplyOrgName = apply.Apply.ApplyUserBuToDeptName;
                                    resDto.ApplyDate = apply.Apply.ApplyTime;
                                }
                                return resDto;
                            }));
                        }
                    }
                }
            }

            if (!request.FormCategory.HasValue || request.FormCategory == ResignationTransfer.TaskFormCategory.PaymentApplication)
            {
                var bizTasks = tasks.Where(x => x.FormNo.StartsWith('A')).ToList();
                if (bizTasks.Count > 0)
                {
                    //付款申请：2-审批中、10-财务初审、11-财务复审
                    var transferableStatusPA = new PurPAApplicationStatus[] { PurPAApplicationStatus.Approvaling, PurPAApplicationStatus.FinancialPreliminaryReview, PurPAApplicationStatus.FinancialReview };
                    var applys = queryablePA.Where((x => transferableStatusPA.Contains(x.Status) && !x.IsDeleted && bizTasks.Select(v => v.FormNo).Distinct().Contains(x.ApplicationCode)))
                        .Join(queryableUser, a => a.ApplyUserId, b => b.Id, (a, b) => new { Apply = new { a.ApplicationCode, a.Status, a.ApplyUserId, a.ApplyUserBu, a.ApplyUserBuToDeptName, a.ApplyTime }, User = new { b.Name } });
                    if (applys.Any())
                    {
                        var formNos = applys.ToArray().Select(x => x.Apply.ApplicationCode).Distinct();
                        var intersect = bizTasks.Select(x => x.FormNo).Distinct().Intersect(formNos);
                        if (intersect.Any())
                        {
                            pageDatas.AddRange(bizTasks.Where(x => intersect.Contains(x.FormNo)).Select(x =>
                            {
                                var apply = applys.FirstOrDefault(a => a.Apply.ApplicationCode == x.FormNo);
                                var resDto = new GetWorkflowTaskResponseDto { Approval = x.ApproverName, Id = x.Id, ApplicationCode = x.FormNo };
                                if (apply != null)
                                {
                                    resDto.FormStatus = (int)apply.Apply.Status;
                                    resDto.FormCategory = ResignationTransfer.TaskFormCategory.PaymentApplication.GetDescription();
                                    resDto.ApplyUserId = apply.Apply.ApplyUserId;
                                    resDto.ApplyUserName = apply.User.Name;
                                    resDto.ApplyOrgId = apply.Apply.ApplyUserBu.ToString();
                                    resDto.ApplyOrgName = apply.Apply.ApplyUserBuToDeptName;
                                    resDto.ApplyDate = apply.Apply.ApplyTime;
                                }
                                return resDto;
                            }));
                        }
                    }
                }
            }

            var BWApplicationCategories = new List<ResignationTransfer.TaskFormCategory> {
                ResignationTransfer.TaskFormCategory.BiddingWaiverApplication,
                ResignationTransfer.TaskFormCategory.JustificationApplication };
            if (!request.FormCategory.HasValue || BWApplicationCategories.Contains((ResignationTransfer.TaskFormCategory)request.FormCategory))
            {
                var bizTasks = tasks.Where(x => x.FormNo.StartsWith('W') || x.FormNo.StartsWith('J')).ToList();
                if (bizTasks.Count > 0)
                {
                    var bwPrefix = request.FormCategory == ResignationTransfer.TaskFormCategory.BiddingWaiverApplication ? 'W' : 'J';
                    var bwFormNos = bizTasks.Where(x => x.FormNo.StartsWith(bwPrefix)).Select(v => v.FormNo).Distinct();
                    var applys = queryableBW.Where((x => x.Status == PurExemptStatus.Approving && !x.IsDeleted && bwFormNos.Contains(x.ApplicationCode)))
                        .WhereIf(request.FormCategory.HasValue && request.FormCategory == ResignationTransfer.TaskFormCategory.BiddingWaiverApplication, x => x.ExemptType == Enums.Purchase.ExemptType.Waiver)
                        .WhereIf(request.FormCategory.HasValue && request.FormCategory == ResignationTransfer.TaskFormCategory.JustificationApplication, x => x.ExemptType == Enums.Purchase.ExemptType.Justification)
                        .Join(queryableUser, a => a.ApplyUserId, b => b.Id, (a, b) => new { Apply = new { a.ApplicationCode, a.Status, a.ApplyUserId, a.ApplyDeptId, a.ApplyTime, a.ExemptType }, User = new { b.Name } });
                    if (applys.Any())
                    {
                        var formNos = applys.ToArray().Select(x => x.Apply.ApplicationCode).Distinct();
                        var intersect = bizTasks.Select(x => x.FormNo).Distinct().Intersect(formNos);
                        if (intersect.Any())
                        {
                            pageDatas.AddRange(bizTasks.Where(x => intersect.Contains(x.FormNo)).Select(x =>
                            {
                                var apply = applys.FirstOrDefault(a => a.Apply.ApplicationCode == x.FormNo);
                                var resDto = new GetWorkflowTaskResponseDto { Approval = x.ApproverName, Id = x.Id, ApplicationCode = x.FormNo };
                                if (apply != null)
                                {
                                    resDto.FormStatus = (int)apply.Apply.Status;
                                    //resDto.FormCategory = ResignationTransfer.TaskFormCategory.BiddingWaiverApplication.GetDescription();
                                    resDto.FormCategory = string.Empty;
                                    resDto.ApplyUserId = apply.Apply.ApplyUserId;
                                    resDto.ApplyUserName = apply.User.Name;
                                    resDto.ApplyOrgId = apply.Apply.ApplyDeptId.ToString();
                                    resDto.ApplyDate = apply.Apply.ApplyTime;
                                    resDto.WaiverType = apply.Apply.ExemptType;
                                }
                                return resDto;
                            }));
                        }
                    }
                }
            }

            if (!request.FormCategory.HasValue || request.FormCategory == ResignationTransfer.TaskFormCategory.OECSpeakerAuthApplication)
            {
                var bizTasks = tasks.Where(x => x.FormNo.StartsWith("SA")).ToList();
                if (bizTasks.Count > 0)
                {
                    var applys = queryableSA.Where((x => x.Status == SpeakerAuthStatus.Approvaling && !x.IsDeleted && bizTasks.Select(v => v.FormNo).Distinct().Contains(x.ApplicationCode)))
                        .Join(queryableUser, a => a.ApplyUserId, b => b.Id, (a, b) => new { Apply = new { a.ApplicationCode, a.Status, a.ApplyUserId, a.ApplyUserDept, a.SubmitTime }, User = new { b.Name } });
                    if (applys.Any())
                    {
                        var formNos = applys.ToArray().Select(x => x.Apply.ApplicationCode).Distinct();
                        var intersect = bizTasks.Select(x => x.FormNo).Distinct().Intersect(formNos);
                        if (intersect.Any())
                        {
                            pageDatas.AddRange(bizTasks.Where(x => intersect.Contains(x.FormNo)).Select(x =>
                            {
                                var apply = applys.FirstOrDefault(a => a.Apply.ApplicationCode == x.FormNo);
                                var resDto = new GetWorkflowTaskResponseDto { Approval = x.ApproverName, Id = x.Id, ApplicationCode = x.FormNo };
                                if (apply != null)
                                {
                                    resDto.FormStatus = (int)apply.Apply.Status;
                                    resDto.FormCategory = ResignationTransfer.TaskFormCategory.OECSpeakerAuthApplication.GetDescription();
                                    resDto.ApplyUserId = apply.Apply.ApplyUserId;
                                    resDto.ApplyUserName = apply.User.Name;
                                    resDto.ApplyOrgId = apply.Apply.ApplyUserDept.ToString();
                                    resDto.ApplyDate = apply.Apply.SubmitTime;
                                }
                                return resDto;
                            }));
                        }
                    }
                }
            }

            if (!request.FormCategory.HasValue || request.FormCategory == ResignationTransfer.TaskFormCategory.STicketRequestApplication)
            {
                var bizTasks = tasks.Where(x => x.FormNo.StartsWith('S') && !x.FormNo.StartsWith("SA")).ToList();
                if (bizTasks.Count > 0)
                {
                    var applys = queryableSTicket.Where((x => x.Status == STicketStatus.Approving && !x.IsDeleted && bizTasks.Select(v => v.FormNo).Distinct().Contains(x.ApplicationCode)))
                        .Join(queryableUser, a => a.ApplyUserId, b => b.Id, (a, b) => new { Apply = new { a.ApplicationCode, a.Status, a.ApplyUserId, a.ApplyUserDeptId, a.ApplyUserDeptName, a.ApplyTime }, User = new { b.Name } });
                    if (applys.Any())
                    {
                        var formNos = applys.ToArray().Select(x => x.Apply.ApplicationCode).Distinct();
                        var intersect = bizTasks.Select(x => x.FormNo).Distinct().Intersect(formNos);
                        if (intersect.Any())
                        {
                            pageDatas.AddRange(bizTasks.Where(x => intersect.Contains(x.FormNo)).Select(x =>
                            {
                                var apply = applys.FirstOrDefault(a => a.Apply.ApplicationCode == x.FormNo);
                                var resDto = new GetWorkflowTaskResponseDto { Approval = x.ApproverName, Id = x.Id, ApplicationCode = x.FormNo };
                                if (apply != null)
                                {
                                    resDto.FormStatus = (int)apply.Apply.Status;
                                    resDto.FormCategory = ResignationTransfer.TaskFormCategory.STicketRequestApplication.GetDescription();
                                    resDto.ApplyUserId = apply.Apply.ApplyUserId;
                                    resDto.ApplyUserName = apply.User.Name;
                                    resDto.ApplyOrgId = apply.Apply.ApplyUserDeptId.ToString();
                                    resDto.ApplyOrgName = apply.Apply.ApplyUserDeptName;
                                    resDto.ApplyDate = apply.Apply.ApplyTime;
                                }
                                return resDto;
                            }));
                        }
                    }
                }
            }

            if (!request.FormCategory.HasValue || request.FormCategory == ResignationTransfer.TaskFormCategory.FOCRequestApplication)
            {
                var bizTasks = tasks.Where(x => x.FormNo.StartsWith("F")).ToList();
                if (bizTasks.Count > 0)
                {
                    var applys = queryableFOC.Where((x => x.Status == FOCStatus.Approving && !x.IsDeleted && bizTasks.Select(v => v.FormNo).Distinct().Contains(x.ApplicationCode)))
                        .Join(queryableUser, a => a.ApplyUserId, b => b.Id, (a, b) => new { Apply = new { a.ApplicationCode, a.Status, a.ApplyUserId, a.ApplyUserDeptId, a.ApplyTime }, User = new { b.Name } });
                    if (applys.Any())
                    {
                        var formNos = applys.ToArray().Select(x => x.Apply.ApplicationCode).Distinct();
                        var intersect = bizTasks.Select(x => x.FormNo).Distinct().Intersect(formNos);
                        if (intersect.Any())
                        {
                            pageDatas.AddRange(bizTasks.Where(x => intersect.Contains(x.FormNo)).Select(x =>
                            {
                                var apply = applys.FirstOrDefault(a => a.Apply.ApplicationCode == x.FormNo);
                                var resDto = new GetWorkflowTaskResponseDto { Approval = x.ApproverName, Id = x.Id, ApplicationCode = x.FormNo };
                                if (apply != null)
                                {
                                    resDto.FormStatus = (int)apply.Apply.Status;
                                    resDto.FormCategory = ResignationTransfer.TaskFormCategory.FOCRequestApplication.GetDescription();
                                    resDto.ApplyUserId = apply.Apply.ApplyUserId;
                                    resDto.ApplyUserName = apply.User.Name;
                                    resDto.ApplyOrgId = apply.Apply.ApplyUserDeptId.ToString();
                                    resDto.ApplyDate = apply.Apply.ApplyTime;
                                }
                                return resDto;
                            }));
                        }
                    }
                }
            }

            var totalCount = pageDatas.Count;

            if (pageDatas.Count == 0)
                return MessageResult.SuccessResult(new PagedResultDto<GetWorkflowTaskResponseDto>(0, []));
            else if (pageDatas.Count > request.PageSize)
                pageDatas = pageDatas.Skip(request.PageSize * request.PageIndex).Take(request.PageSize).ToList();

            var orgs = await dataverseService.GetOrganizations(stateCode: null);

            foreach (var item in pageDatas)
            {
                if (string.IsNullOrEmpty(item.ApplyOrgId) || !string.IsNullOrEmpty(item.ApplyOrgName))
                    continue;
                var orgDto = orgs.FirstOrDefault(x => x.Id == Guid.Parse(item.ApplyOrgId));
                if (orgDto == null)
                    continue;
                var orgTree2BU = await commonService.GetParentOrgs(orgDto, orgs);
                item.ApplyOrgName = orgTree2BU.Select(x => x.DepartmentName).Reverse().JoinAsString("-");
            }

            return MessageResult.SuccessResult(new PagedResultDto<GetWorkflowTaskResponseDto>(totalCount, [.. pageDatas.OrderBy(x => x.ApplyDate)]));
        }


        public async Task<MessageResult> SetTransfereeUser4ApplyForms(SetTransfereeUserRequestDto request)
        {
            //供应商申请SP-V
            var repositorySP = LazyServiceProvider.LazyGetService<IVendorApplicationRepository>();
            var queryableSP = await repositorySP.GetQueryableAsync();
            //采购申请PR-P
            var repositoryPR = LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>();
            var queryablePR = await repositoryPR.GetQueryableAsync();
            //采购订单PO-O
            var repositoryPO = LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>();
            var queryablePO = await repositoryPO.GetQueryableAsync();
            //供应商比价BD-B
            var repositoryBD = LazyServiceProvider.LazyGetService<IPurBDApplicationRepository>();
            var queryableBD = await repositoryBD.GetQueryableAsync();
            //收货GR-G
            var repositoryGR = LazyServiceProvider.LazyGetService<IPurGRApplicationRepository>();
            var queryableGR = await repositoryGR.GetQueryableAsync();
            //付款PA-A
            var repositoryPA = LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>();
            var queryablePA = await repositoryPA.GetQueryableAsync();
            //竞价豁免BW-W
            var repositoryBW = LazyServiceProvider.LazyGetService<IPurBWApplicationRepository>();
            var queryableBW = await repositoryBW.GetQueryableAsync();
            //讲者授权申请SA-SA
            var repositorySA = LazyServiceProvider.LazyGetService<IOECSpeakerAuthApplyRepository>();
            var queryableSA = await repositorySA.GetQueryableAsync();
            //核销申请
            var repositorySTicket = LazyServiceProvider.LazyGetService<ISTicketApplicationRepository>();
            var queryableSTicket = await repositorySTicket.GetQueryableAsync();
            //FOC申请
            var repositoryFOC = LazyServiceProvider.LazyGetService<IFocApplicationRepository>();
            var queryableFOC = await repositoryFOC.GetQueryableAsync();

            var requestSP = request.ApplicationItems.Where(x => x.ApplicationCode.StartsWith('V')).Select(x => x.Id);
            if (requestSP.Any())
            {
                //var dataSP = queryableSP.Where((x => x.Status == Statuses.Approving && !x.IsDeleted && !x.TransfereeId.HasValue && requestSP.Contains(x.Id))).ToArray();
                var dataSP = queryableSP.Where((x => !x.IsDeleted && !x.TransfereeId.HasValue && requestSP.Contains(x.Id))).ToArray();
                if (dataSP.Length > 0)
                {
                    dataSP.ToList().ForEach(x =>
                    {
                        x.TransfereeId = request.TransfereeUserId;
                        x.TransfereeName = request.TransfereeUserName;
                    });
                    await repositorySP.UpdateManyAsync(dataSP);
                }
            }

            var requestPR = request.ApplicationItems.Where(x => x.ApplicationCode.StartsWith('P')).Select(x => x.Id);
            if (requestPR.Any())
            {
                //var dataPR = queryablePR.Where(x => x.Status == Enums.Purchase.PurPRApplicationStatus.Approving && !x.IsDeleted && !x.TransfereeId.HasValue && requestPR.Contains(x.Id)).ToArray();
                var dataPR = queryablePR.Where(x => !x.IsDeleted && !x.TransfereeId.HasValue && requestPR.Contains(x.Id)).ToArray();
                if (dataPR.Length > 0)
                {
                    dataPR.ToList().ForEach(x =>
                    {
                        x.TransfereeId = request.TransfereeUserId;
                        x.TransfereeName = request.TransfereeUserName;
                    });
                    await repositoryPR.UpdateManyAsync(dataPR);
                }
            }

            var requestPO = request.ApplicationItems.Where(x => x.ApplicationCode.StartsWith('O')).Select(x => x.Id);
            if (requestPO.Any())
            {
                //var dataPO = queryablePO.Where(x => x.Status == Enums.Purchase.PurOrderStatus.Approving && !x.IsDeleted && !x.TransfereeId.HasValue && requestPO.Contains(x.Id)).ToArray();
                var dataPO = queryablePO.Where(x => !x.IsDeleted && !x.TransfereeId.HasValue && requestPO.Contains(x.Id)).ToArray();
                if (dataPO.Length > 0)
                {
                    dataPO.ToList().ForEach(x =>
                    {
                        x.TransfereeId = request.TransfereeUserId;
                        x.TransfereeName = request.TransfereeUserName;
                    });
                    await repositoryPO.UpdateManyAsync(dataPO);
                }
            }

            var requestBD = request.ApplicationItems.Where(x => x.ApplicationCode.StartsWith('B')).Select(x => x.Id);
            if (requestBD.Any())
            {
                //var dataBD = queryableBD.Where(x => x.Status == PurBDStatus.Approving && !x.IsDeleted && !x.TransfereeId.HasValue && requestBD.Contains(x.Id)).ToArray();
                var dataBD = queryableBD.Where(x => !x.IsDeleted && !x.TransfereeId.HasValue && requestBD.Contains(x.Id)).ToArray();
                if (dataBD.Length > 0)
                {
                    dataBD.ToList().ForEach(x =>
                    {
                        x.TransfereeId = request.TransfereeUserId;
                        x.TransfereeName = request.TransfereeUserName;
                    });
                    await repositoryBD.UpdateManyAsync(dataBD);
                }
            }

            var requestGR = request.ApplicationItems.Where(x => x.ApplicationCode.StartsWith('G')).Select(x => x.Id);
            if (requestGR.Any())
            {
                //var dataGR = queryableGR.Where(x => x.Status == PurGRStatus.PurGRApplicationStatus.Returned && !x.IsDeleted && !x.TransfereeId.HasValue && requestGR.Contains(x.Id)).ToArray();
                var dataGR = queryableGR.Where(x => !x.IsDeleted && !x.TransfereeId.HasValue && requestGR.Contains(x.Id)).ToArray();
                if (dataGR.Length > 0)
                {
                    dataGR.ToList().ForEach(x =>
                    {
                        x.TransfereeId = request.TransfereeUserId;
                        x.TransfereeName = request.TransfereeUserName;
                    });
                    await repositoryGR.UpdateManyAsync(dataGR);
                }
            }

            var requestPA = request.ApplicationItems.Where(x => x.ApplicationCode.StartsWith('A')).Select(x => x.Id);
            if (requestPA.Any())
            {
                //var dataPA = queryablePA.Where(x => x.Status == PurPAStatus.PurPAApplicationStatus.Approvaling && !x.IsDeleted && !x.TransfereeId.HasValue && requestPA.Contains(x.Id)).ToArray();
                var dataPA = queryablePA.Where(x => !x.IsDeleted && !x.TransfereeId.HasValue && requestPA.Contains(x.Id)).ToArray();
                if (dataPA.Length > 0)
                {
                    dataPA.ToList().ForEach(x =>
                    {
                        x.TransfereeId = request.TransfereeUserId;
                        x.TransfereeName = request.TransfereeUserName;
                    });
                    await repositoryPA.UpdateManyAsync(dataPA);
                }
            }

            var requestBW = request.ApplicationItems.Where(x => x.ApplicationCode.StartsWith('W') || x.ApplicationCode.StartsWith('J')).Select(x => x.Id);
            if (requestBW.Any())
            {
                //var dataBW = queryableBW.Where(x => x.Status == Enums.Purchase.PurExemptStatus.Return && !x.IsDeleted && !x.TransfereeId.HasValue && requestBW.Contains(x.Id)).ToArray();
                var dataBW = queryableBW.Where(x => !x.IsDeleted && !x.TransfereeId.HasValue && requestBW.Contains(x.Id)).ToArray();
                if (dataBW.Length > 0)
                {
                    dataBW.ToList().ForEach(x =>
                    {
                        x.TransfereeId = request.TransfereeUserId;
                        x.TransfereeName = request.TransfereeUserName;
                    });
                    await repositoryBW.UpdateManyAsync(dataBW);
                }
            }

            var requestSA = request.ApplicationItems.Where(x => x.ApplicationCode.StartsWith("SA")).Select(x => x.Id);
            if (requestSA.Any())
            {
                //var dataSA = queryableSA.Where(x => x.Status == SpeakerAuthStatus.Approvaling && !x.IsDeleted && !x.TransfereeId.HasValue && requestBW.Contains(x.Id)).ToArray();
                var dataSA = queryableSA.Where(x => !x.IsDeleted && !x.TransfereeId.HasValue && requestSA.Contains(x.Id)).ToArray();
                if (dataSA.Length > 0)
                {
                    dataSA.ToList().ForEach(x =>
                    {
                        x.TransfereeId = request.TransfereeUserId;
                        x.TransfereeName = request.TransfereeUserName;
                    });
                    await repositorySA.UpdateManyAsync(dataSA);
                }
            }

            var requestSTicket = request.ApplicationItems.Where(x => x.ApplicationCode.StartsWith("S") && !x.ApplicationCode.StartsWith("SA")).Select(x => x.Id);
            if (requestSTicket.Any())
            {
                var dataSTicket = queryableSTicket.Where(x => !x.IsDeleted && !x.TransfereeId.HasValue && requestSTicket.Contains(x.Id)).ToArray();
                if (dataSTicket.Length > 0)
                {
                    dataSTicket.ToList().ForEach(x =>
                    {
                        x.TransfereeId = request.TransfereeUserId;
                        x.TransfereeName = request.TransfereeUserName;
                    });
                    await repositorySTicket.UpdateManyAsync(dataSTicket);
                }
            }

            var requestFOC = request.ApplicationItems.Where(x => x.ApplicationCode.StartsWith("F")).Select(x => x.Id);
            if (requestFOC.Any())
            {
                var dataFOC = queryableFOC.Where(x => !x.IsDeleted && !x.TransfereeId.HasValue && requestFOC.Contains(x.Id)).ToArray();
                if (dataFOC.Length > 0)
                {
                    dataFOC.ToList().ForEach(x =>
                    {
                        x.TransfereeId = request.TransfereeUserId;
                        x.TransfereeName = request.TransfereeUserName;
                    });
                    await repositoryFOC.UpdateManyAsync(dataFOC);
                }
            }

            return MessageResult.SuccessResult();
        }

        public async Task<MessageResult> SetTransferreUser4WorkflowTask(SetTransfereeUserRequestDto request)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseRepository>();

            OrganizationRequestCollection orgRequests = [];
            ExecuteTransactionRequest multipleRequest = new ExecuteTransactionRequest()
            {
                Requests = [],
                ReturnResponses = true
            };

            request.ApplicationItems.ForEach(x =>
            {
                var entityToUpdate = new Entity("spk_workflowtask") { Id = x.Id };
                entityToUpdate["spk_approver"] = new EntityReference("spk_staffmasterdata", request.TransfereeUserId);
                orgRequests.Add(new UpdateRequest() { Target = entityToUpdate });
            });

            if (orgRequests.Count > 0)
            {
                multipleRequest.Requests.Clear();
                multipleRequest.Requests.AddRange(orgRequests);
                var res = await dataverseService.DataverseClient.ExecuteAsync(multipleRequest);
                orgRequests.Clear();
            }

            return MessageResult.SuccessResult();
        }
        #endregion
    }
}
