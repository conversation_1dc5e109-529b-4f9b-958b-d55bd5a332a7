select  newid()  as spk_NexBPMCode
,spk_BPMCode,spk_Name
,spk_chinesevalue
,spk_englishvalue
,spk_fax
,spk_CompanyCode
,spk_invoiceaddress
,spk_invoicetitle
,spk_openbank
,spk_phone
,spk_abbrcode
,spk_bankcity
,spk_bankaccount
,flg 
into #spk_companymasterdata 
from spk_companymasterdata_Tmp 


IF OBJECT_ID(N'dbo.spk_companymasterdata', N'U') IS NOT NULL
BEGIN
	update a 
	set a.spk_BPMCode        = b.spk_BPMCode
        ,a.spk_Name          = b.spk_Name
        ,a.spk_chinesevalue  = b.spk_chinesevalue
        ,a.spk_englishvalue  = b.spk_englishvalue
        ,a.spk_fax           = b.spk_fax
        ,a.spk_CompanyCode   = b.spk_CompanyCode
        ,a.spk_invoiceaddress= b.spk_invoiceaddress
        ,a.spk_invoicetitle  = b.spk_invoicetitle
        ,a.spk_openbank      = b.spk_openbank
        ,a.spk_phone         = b.spk_phone
        ,a.spk_abbrcode      = b.spk_abbrcode
        ,a.spk_bankcity      = b.spk_bankcity
        ,a.spk_bankaccount   = b.spk_bankaccount
        ,a.flg               = b.flg 
    from dbo.spk_companymasterdata a
    join #spk_companymasterdata b on a.spk_BPMCode = b.spk_BPMCode
    
    insert into dbo.spk_companymasterdata
    select a.spk_NexBPMCode
          ,a.spk_BPMCode
          ,a.spk_Name
          ,a.spk_chinesevalue
          ,a.spk_englishvalue
          ,a.spk_fax
          ,a.spk_CompanyCode
          ,a.spk_invoiceaddress
          ,a.spk_invoicetitle
          ,a.spk_openbank
          ,a.spk_phone
          ,a.spk_abbrcode
          ,a.spk_bankcity
          ,a.spk_bankaccount
          ,a.flg 
	from #spk_companymasterdata a
	where NOT EXISTS (SELECT * FROM dbo.spk_companymasterdata where spk_BPMCode = a.spk_BPMCode)
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into dbo.spk_companymasterdata from #spk_companymasterdata
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END