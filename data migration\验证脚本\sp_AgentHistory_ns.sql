create proc sp_AgentHistory_ns
as
begin
	if object_id('AgentHistory','u') is null
		select
			 newid() [Id]
            ,[AgentConfigId]
            ,[WorkflowType]
            ,[AgentTime]
            ,iif(substring(a.serialNumber,1,1) in ('S','T'),null,a.FormId) [FormId]
            ,[FormCode]
            ,[ExtraProperties]
            ,[ConcurrencyStamp]
            ,[CreationTime]
            ,a.AgentId [CreatorId]
            ,[LastModificationTime]
            ,[LastModifierId]
            ,[IsDeleted]
            ,[DeleterId]
            ,[DeletionTime]
            ,a.BpmId _BpmId
		into AgentHistory
		from AgentHistory_tmp a
	else
	begin
		update t
		set t.[AgentConfigId]=tt.[AgentConfigId],
		t.[WorkflowType]=tt.[WorkflowType],
		t.[AgentTime]=tt.[AgentTime],
		t.[FormId]=iif(substring(tt.serialNumber,1,1) in ('S','T'),null,tt.FormId),
		t.[FormCode]=tt.[FormCode],
		t.[ExtraProperties]=tt.[ExtraProperties],
		t.[ConcurrencyStamp]=tt.[ConcurrencyStamp],
		t.[CreationTime]=tt.[CreationTime],
		t.[CreatorId]=tt.AgentId,
		t.[LastModificationTime]=tt.[LastModificationTime],
		t.[LastModifierId]=tt.[LastModifierId],
		t.[IsDeleted]=tt.[IsDeleted],
		t.[DeleterId]=tt.[DeleterId],
		t.[DeletionTime]=tt.[DeletionTime]
		from AgentHistory t join AgentHistory_tmp tt on t._BpmId=tt.BpmId

		insert AgentHistory
		select
			newid() [Id],[AgentConfigId],[WorkflowType],[AgentTime],[FormId],[FormCode],[ExtraProperties],[ConcurrencyStamp],[CreationTime],a.AgentId [CreatorId],[LastModificationTime],[LastModifierId],[IsDeleted],[DeleterId],[DeletionTime], a.BpmId _BpmId
		from AgentHistory_tmp a
		where not exists(select * from AgentHistory tt where tt._BpmId=a.BpmId);
	end
end;