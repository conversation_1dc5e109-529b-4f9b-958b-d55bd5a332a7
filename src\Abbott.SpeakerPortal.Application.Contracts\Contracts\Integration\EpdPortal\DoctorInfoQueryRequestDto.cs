﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Text.Json.Serialization;

namespace Abbott.SpeakerPortal.Contracts.Integration.Dspot
{

    /// <summary>
    /// 医生信息查询请求
    /// </summary>
    public class DoctorInfoQueryRequestDto
    {
        /// <summary>
        /// 查询开始日期
        /// </summary>
        [JsonPropertyName("queryStartDate")]
        public DateTime? QueryStartDate { get; set; }

        /// <summary>
        /// 查询医生编码
        /// </summary>
        [JsonPropertyName("buHcpCode")]
        public string BUHcpCode { get; set; }
    }
}
