SELECT
 TRY_CONVERT(UNIQUEIDENTIFIER, [Id]) [Id]
,case when ApplicationCode is null or ApplicationCode='' or ApplicationCode='NULL' then 'NULL' else ApplicationCode  end AS [ApplicationCode]
,[PRApplicationDetailId]
,case when
Status=N'申请人确认' then '1'
when Status=N'主采购循环审批中' then '2'
when Status=N'已通过' then '3'
when Status=N'已拒绝' then '4'
when Status=N'退回' then '5'
when Status=N'关闭' then '6'
when Status=N'作废' then '7'
when Status=N'草稿' then '8'
when Status=N'已撤回' then '9'
when Status=N'发起收货' then '10'
else '0'
end AS [Status]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([ApplyUserId],'********-0000-0000-0000-************')) [ApplyUserId]
,GETDATE() AS [ApplyTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([ApplyUserBu],'********-0000-0000-0000-************')) [ApplyUserBu]
,TRY_CONVERT(UNIQUEIDENTIFIER, [BWApplicationId]) [BWApplicationId]
,TRY_CONVERT(UNIQUEIDENTIFIER, [BApplicationId]) [BApplicationId]
,iif(POType is null,0,POType) AS [POType]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CompanyId]) [CompanyId]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([VendorId],'********-0000-0000-0000-************')) [VendorId]
,case when VendorPorperty='ASN' then '1' else '0' end AS [VendorPorperty]
,[RegCertificateAddress]
,[ContactName]
,[ContactPhone]
,[ContactEmail]
,[FaxNumber]
,[PaymentTerm]
,[Currency]
,iif(ExchangeRate is null,0,ExchangeRate) AS [ExchangeRate]
,[PRType]
,0 AS [PRCorrespond]
,[AttentionNote]
,[DeliveryType]
,[DeliveryAddress]
,[PaymentCondition]
,GETDATE() AS [DeliveryDate]
,[Qualitystandard]
,[Others]
,[Remark]
,[AttachmentFile]
,'{}' AS [ExtraProperties]
,'NULL' AS [ConcurrencyStamp]
,GETDATE() AS [CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CreatorId]) [CreatorId]
,[LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [LastModifierId]) [LastModifierId]
,0 AS [IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, [DeleterId]) [DeleterId]
,[DeletionTime]
,[Saving]
,[VendorCategory]
,[BankAccount]
,[InvoiceAddress]
,[InvoiceFax]
,[InvoiceTitle]
,[OpenBank]
,iif(TotalAmount is null,0,TotalAmount) AS [TotalAmount]
,iif(TotalAmountTax is null,0,TotalAmountTax) AS [TotalAmountTax]
,[ApplyUserBuName]
,[ApplyUserName]
,[VendorName]
,iif(IsLate is null,0,IsLate) AS [IsLate]
,[ApsPorperty]
,[CurrencySymbol]
,[PhoneNumber]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([PRId],'********-0000-0000-0000-************')) [PRId]
,GETDATE() AS [ApprovedDate]
,[VendorCode]
,[ApplyUserBuToDeptName]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([ApplyUserDept],'********-0000-0000-0000-************')) [ApplyUserDept]
,NULL AS [ASNTypeTex]
,0 AS [SupplierAtribute]
,0 AS [ExpectedFloatRate]
,0 AS [PlanRate]
,TRY_CONVERT(UNIQUEIDENTIFIER, '********-0000-0000-0000-************') AS [TransfereeId]
,NULL AS [TransfereeName]
INTO #PurPOApplications
FROM (SELECT DISTINCT *,ROW_NUMBER() OVER(PARTITION BY Id ORDER BY Id) RK FROM PLATFORM_ABBOTT_STG.dbo.PurPOApplications)a
WHERE RK = 1

--drop table #PurPOApplications
--select * from PLATFORM_ABBOTT_STG.dbo.PurPOApplications

USE Speaker_Portal_STG;

UPDATE a
SET 
 a.[Id] = b.[Id]
,a.[ApplicationCode] = b.[ApplicationCode]
,a.[PRApplicationDetailId] = b.[PRApplicationDetailId]
,a.[Status] = b.[Status]
,a.[ApplyUserId] = b.[ApplyUserId]
,a.[ApplyTime] = b.[ApplyTime]
,a.[ApplyUserBu] = b.[ApplyUserBu]
,a.[BWApplicationId] = b.[BWApplicationId]
,a.[BApplicationId] = b.[BApplicationId]
,a.[POType] = b.[POType]
,a.[CompanyId] = b.[CompanyId]
,a.[VendorId] = b.[VendorId]
,a.[VendorPorperty] = b.[VendorPorperty]
,a.[RegCertificateAddress] = b.[RegCertificateAddress]
,a.[ContactName] = b.[ContactName]
,a.[ContactPhone] = b.[ContactPhone]
,a.[ContactEmail] = b.[ContactEmail]
,a.[FaxNumber] = b.[FaxNumber]
,a.[PaymentTerm] = b.[PaymentTerm]
,a.[Currency] = b.[Currency]
,a.[ExchangeRate] = b.[ExchangeRate]
,a.[PRType] = b.[PRType]
,a.[PRCorrespond] = b.[PRCorrespond]
,a.[AttentionNote] = b.[AttentionNote]
,a.[DeliveryType] = b.[DeliveryType]
,a.[DeliveryAddress] = b.[DeliveryAddress]
,a.[PaymentCondition] = b.[PaymentCondition]
,a.[DeliveryDate] = b.[DeliveryDate]
,a.[Qualitystandard] = b.[Qualitystandard]
,a.[Others] = b.[Others]
,a.[Remark] = b.[Remark]
,a.[AttachmentFile] = b.[AttachmentFile]
,a.[ExtraProperties] = b.[ExtraProperties]
,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = b.[CreatorId]
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = b.[LastModifierId]
,a.[IsDeleted] = b.[IsDeleted]
,a.[DeleterId] = b.[DeleterId]
,a.[DeletionTime] = b.[DeletionTime]
,a.[Saving] = b.[Saving]
,a.[VendorCategory] = b.[VendorCategory]
,a.[BankAccount] = b.[BankAccount]
,a.[InvoiceAddress] = b.[InvoiceAddress]
,a.[InvoiceFax] = b.[InvoiceFax]
,a.[InvoiceTitle] = b.[InvoiceTitle]
,a.[OpenBank] = b.[OpenBank]
,a.[TotalAmount] = b.[TotalAmount]
,a.[TotalAmountTax] = b.[TotalAmountTax]
,a.[ApplyUserBuName] = b.[ApplyUserBuName]
,a.[ApplyUserName] = b.[ApplyUserName]
,a.[VendorName] = b.[VendorName]
,a.[IsLate] = b.[IsLate]
,a.[ApsPorperty] = b.[ApsPorperty]
,a.[CurrencySymbol] = b.[CurrencySymbol]
,a.[PhoneNumber] = b.[PhoneNumber]
,a.[PRId] = b.[PRId]
,a.[ApprovedDate] = b.[ApprovedDate]
,a.[VendorCode] = b.[VendorCode]
,a.[ApplyUserBuToDeptName] = b.[ApplyUserBuToDeptName]
,a.[ApplyUserDept] = b.[ApplyUserDept]
,a.[ASNTypeTex] = b.[ASNTypeTex]
,a.[SupplierAtribute] = b.[SupplierAtribute]
,a.[ExpectedFloatRate] = b.[ExpectedFloatRate]
,a.[PlanRate] = b.[PlanRate]
,a.[TransfereeId] = b.[TransfereeId]
,a.[TransfereeName] = b.[TransfereeName]
FROM dbo.PurPOApplications a
left join #PurPOApplications  b
ON a.id=b.id
WHERE b.ApplyUserBu IS NOT NULL

--select * from #PurPOApplications where ApplyUserBu is null


INSERT INTO dbo.PurPOApplications
(
 [Id]
,[ApplicationCode]
,[PRApplicationDetailId]
,[Status]
,[ApplyUserId]
,[ApplyTime]
,[ApplyUserBu]
,[BWApplicationId]
,[BApplicationId]
,[POType]
,[CompanyId]
,[VendorId]
,[VendorPorperty]
,[RegCertificateAddress]
,[ContactName]
,[ContactPhone]
,[ContactEmail]
,[FaxNumber]
,[PaymentTerm]
,[Currency]
,[ExchangeRate]
,[PRType]
,[PRCorrespond]
,[AttentionNote]
,[DeliveryType]
,[DeliveryAddress]
,[PaymentCondition]
,[DeliveryDate]
,[Qualitystandard]
,[Others]
,[Remark]
,[AttachmentFile]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[Saving]
,[VendorCategory]
,[BankAccount]
,[InvoiceAddress]
,[InvoiceFax]
,[InvoiceTitle]
,[OpenBank]
,[TotalAmount]
,[TotalAmountTax]
,[ApplyUserBuName]
,[ApplyUserName]
,[VendorName]
,[IsLate]
,[ApsPorperty]
,[CurrencySymbol]
,[PhoneNumber]
,[PRId]
,[ApprovedDate]
,[VendorCode]
,[ApplyUserBuToDeptName]
,[ApplyUserDept]
,[ASNTypeTex]
,[SupplierAtribute]
,[ExpectedFloatRate]
,[PlanRate]
,[TransfereeId]
,[TransfereeName]
)
SELECT
 [Id]
,[ApplicationCode]
,[PRApplicationDetailId]
,[Status]
,[ApplyUserId]
,[ApplyTime]
,[ApplyUserBu]
,[BWApplicationId]
,[BApplicationId]
,[POType]
,[CompanyId]
,[VendorId]
,[VendorPorperty]
,[RegCertificateAddress]
,[ContactName]
,[ContactPhone]
,[ContactEmail]
,[FaxNumber]
,[PaymentTerm]
,[Currency]
,[ExchangeRate]
,[PRType]
,[PRCorrespond]
,[AttentionNote]
,[DeliveryType]
,[DeliveryAddress]
,[PaymentCondition]
,[DeliveryDate]
,[Qualitystandard]
,[Others]
,[Remark]
,[AttachmentFile]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[Saving]
,[VendorCategory]
,[BankAccount]
,[InvoiceAddress]
,[InvoiceFax]
,[InvoiceTitle]
,[OpenBank]
,[TotalAmount]
,[TotalAmountTax]
,[ApplyUserBuName]
,[ApplyUserName]
,[VendorName]
,[IsLate]
,[ApsPorperty]
,[CurrencySymbol]
,[PhoneNumber]
,[PRId]
,[ApprovedDate]
,[VendorCode]
,[ApplyUserBuToDeptName]
,[ApplyUserDept]
,[ASNTypeTex]
,[SupplierAtribute]
,[ExpectedFloatRate]
,[PlanRate]
,[TransfereeId]
,[TransfereeName]
FROM #PurPOApplications a
WHERE not exists (select * from dbo.PurPOApplications where id=a.id)


--truncate table dbo.PurPOApplications

--alter table dbo.PurPOApplications alter column [PRApplicationDetailId] [nvarchar](max) NULL
--alter table dbo.PurPOApplications alter column [ContactName] [nvarchar](50) NULL
--alter table dbo.PurPOApplications alter column [ContactPhone] [nvarchar](50) NULL
--alter table dbo.PurPOApplications alter column [FaxNumber] [nvarchar](50) NULL
--alter table dbo.PurPOApplications alter column [Currency] [nvarchar](50) NULL
--alter table dbo.PurPOApplications alter column [AttachmentFile] [nvarchar](max) NULL
--alter table dbo.PurPOApplications alter column [BankAccount] [nvarchar](255) NULL
--alter table dbo.PurPOApplications alter column [InvoiceFax] [nvarchar](50) NULL
--alter table dbo.PurPOApplications alter column [OpenBank] [nvarchar](255) NULL
--alter table dbo.PurPOApplications alter column [ApplyUserBuName] [nvarchar](255) NULL
--alter table dbo.PurPOApplications alter column [ApplyUserName] [nvarchar](50) NULL
--alter table dbo.PurPOApplications alter column [CurrencySymbol] [nvarchar](50) NULL
--alter table dbo.PurPOApplications alter column [PhoneNumber] [nvarchar](50) NULL