{
	"App": {
		"SelfUrl": "https://speaker-portal-api-d.oneabbott.com",
		"CorsOrigins": "https://*.oneabbott.com,https://*.abbott.com.cn",
		"RedirectAllowedUrls": ""
	},
	"ConnectionStrings": {
		"BpmDb": "Server=WQ00261Q;Database=PLATFORM_ABBOTT_BUSINESSDATA;TrustServerCertificate=True;User ID=svc_ca191_nexbpm_s;Password=********************************"
	},
	"KeyVaultHost": "https://kv-corp-ca191-d.vault.azure.cn",
	"AzureAd": {
		"Instance": "https://login.microsoftonline.com/",
		"CallbackPath": "/login"
	},
	"AuthServer": {
		"Authority": "https://speaker-portal-api-d.oneabbott.com",
		"RequireHttpsMetadata": "false",
		"SwaggerClientId": "SpeakerPortal_Swagger"
	},
	"Consent": {
		"BaseUrl": "https://consent-portal-api-d.oneabbott.com/",
		"clientId": "06106afa-d79d-4fb0-9add-afd8f22f2fb9",
		"secret": "35f38902-621b-456d-b989-26e20a263228",
		"ConsentCode": "C00000001,C00000002,C00000003", //签署版本
		"ConsentName": "隐私政策,敏感信息保护政策,知情同意书"
	},
	//微信小程序
	"WxApp": {
		"WxToken": "https://api.weixin.qq.com",
		"WeChatUrl": "pages/informationConfirmation/index",
		"QRwidth": "300",
		"Enversion": "develop"
	},
	"WcApp": {
		"WcToken": "https://qyapi.weixin.qq.com"
	},
	"Blob": {
		"ContainerName": "speaker-portal-file"
	},
	"SpeakerEmail": {
		"Env": "D",
		"WebHost": "https://speaker-portal-d.oneabbott.com/",
		"SmtpServer": "mail.oneabbott.com",
		"FromEmail": "<EMAIL>",
		"FromName": "NexBPM Admin Test",
		"HelpdeskEmail": "<EMAIL>",
		"BPMHelpdeskEmail": "<EMAIL>",
		"VeevaTimeoutCCEmail": "<EMAIL>,<EMAIL>"
	},
	"Integrations": {
		"DSpot": {
			"Url_WholeProcessReport": "https://oec-drsp-api-d.oneabbott.com/drsp/api/sampling/whole",
			"Url_WholeNotify": "https://speaker-portal-api-d.oneabbott.com/api/integrationnotify/whole",
			"Push_Count": "3",

			"TwoElementsUrl": "https://iddc2.deloitte.com.cn/drsp/api/twoElements/getResult",
			"TwoElementsBatchUrl": "https://iddc2.deloitte.com.cn/drsp/api/twoElements/getBatchResult"
		},
		"BPCS": {
			/*
			正式环境:
			"SftpIP": "*************",
			"SftpUser": "FIN_SP",
			"SftpPwd": "9sjjes*nQy"
			"SftpFolderLvl1": "SP_ALL",
			*/

			//"SftpFolderLvl2Prefixes": "AD,JV,JX,RO,SF",

			"SftpFolderLvl3Archive": "ARCHIVE",
			"SftpFolderLvl3Error": "ERROR",
			"SftpFolderLvl3Upload": "WIP",
			"SftpFolderLvl4Error": "ERROR"
		},
		"OM": {
			"PrintUrl": "https://speaker-portal-d.oneabbott.com",
			//"BaseUrl": "http://epd-hcp-portal-dev.1kuaizhuan.cn",
			"BaseUrl": "https://epd-hcpportal-s.oneabbott.com",
			"AppId": "next-bpm",
			"AppSecret": "E53ssz3PnbQSXkLdFv",
			"AppVersion": "1.0"
		},
		"Veeva": {
			"ApiUrlHost": "https://service-cn.veevaopendata.com:9320/",
			"ApiUrlToken": "api/v1/security/login",
			"ApiUrlEntrance": "api/v1/opendataapi/entrance/",

			"Provider": "db", //[ db, ldap ]
			"Refresh": true,
			"SftpIP": "abtcnsftpstg.blob.core.chinacloudapi.cn",
			"SftpUser": "abtcnsftpstg.ca191-corp-nbpm.ca191nbpmappuse",
			"SftpPwd": "QGWA8Fi7l8+gn/6QhadF7I7zc4o7ZZUz",
			"SftpPort": "22"
		},
		"Graph": {
			"CertName": "[7643] - GIS - China Speaker Portal Hub - Dev.pfx",
			"Authority": "https://login.microsoftonline.com",
			"Resource": "https://graph.microsoft.com"
		},
		"SOI": {
			"AppKey": "b7860726bfd14fa293679bc1d11e24e4",
			"AppSecret": "8/a5dinzgpjx+ff5c9u01hlNE6gzHa9MShy8mCdGCyg=",
			"BaseUrl": "https://ani-dynastytest.oneabbott.com"
		},
		"MDM": {
			"AppId": "UXCAO8ID4K2E83EW",
			"ApiKey": "84o53b9jEcahmSVkSJ5i6G883oMFLbOA",
			"AppSecret": "A420F74C3B18EC28A4F7A2AD4167EAD6E6A00F3F6BC6BD231A0518A267C4CC90902551F1584D6D20FCA4C89BFF55EA1A5BBBFD2F60B8A2B8187AFFFE7DCB44EE",
			"SubscriptionKey": "571c5262d31a4e52b7cc21a10b9d5a99",
			"BaseUrl": "https://apicenter-s.abbott.com.cn/mnd"
		},
		"CSS": {
			"AppSecret": "b9cf967faca946ff8468769eadca0868",
			"BaseUrl": "http://andcss.garnier-smp.com:2009/api"
		}
	}
}
