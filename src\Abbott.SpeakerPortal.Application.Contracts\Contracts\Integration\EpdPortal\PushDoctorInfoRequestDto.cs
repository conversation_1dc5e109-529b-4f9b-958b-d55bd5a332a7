﻿using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Abbott.SpeakerPortal.Contracts.Integration.Dspot
{
    public class PushDoctorInfoRequestDto
    {
        /// <summary>
        /// 指名人511账号
        /// </summary>
        [Required]
        [JsonPropertyName("empID")]
        public string EmpID { get; set; }

        /// <summary>
        /// 指名人雅培邮箱
        /// </summary>
        [JsonPropertyName("empEmail")]
        public string EmpEmail { get; set; }

        /// <summary>
        /// EPD医生主键
        /// </summary>
        [Required]
        [JsonPropertyName("epdHcpCode")]
        public string EpdHcpCode { get; set; }

        ///// <summary>
        ///// 医生的veevaid
        ///// </summary>
        //[JsonPropertyName("veevaHcpId")]
        //public string VeevaHcpId { get; set; }

        /// <summary>
        /// 医生姓名
        /// </summary>
        [Required]
        [JsonPropertyName("name")]
        public string Name { get; set; }

        /// <summary>
        /// 医生所属医院
        /// </summary>
        [Required]
        [JsonPropertyName("hospitalName")]
        public string HospitalName { get; set; }

        /// <summary>
        /// EPD医院ID
        /// </summary>
        [Required]
        [JsonPropertyName("hospitalCode")]
        public string HospitalCode { get; set; }

        /// <summary>
        /// 所属标准科室
        /// </summary>
        [Required]
        [JsonPropertyName("departmentName")]
        public string DepartmentName { get; set; }

        /// <summary>
        /// 所属院内科室
        /// </summary>
        [JsonPropertyName("hosDepartmentName")]
        public string HosDepartmentName { get; set; }

        /// <summary>
        /// 职称
        /// </summary>
        [Required]
        [JsonPropertyName("professionalTitle")]
        public string ProfessionalTitle { get; set; }

        /// <summary>
        /// 医生手机号-密文
        /// </summary>
        [Required]
        [JsonPropertyName("mobile")]
        public string Mobile { get; set; }
    }
}