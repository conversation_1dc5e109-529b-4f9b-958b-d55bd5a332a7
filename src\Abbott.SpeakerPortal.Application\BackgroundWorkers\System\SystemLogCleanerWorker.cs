﻿using System.Threading;
using System.Threading.Tasks;

using Abbott.SpeakerPortal.Contracts.Common;

using Hangfire;

namespace Abbott.SpeakerPortal.BackgroundWorkers.System
{
    public class SystemLogCleanerWorker : SpeakerPortalBackgroundWorkerBase
    {
        public SystemLogCleanerWorker()
        {
            CronExpression = Cron.Daily();
        }

        public override async Task DoWorkAsync(CancellationToken cancellationToken = default)
        {
            await LazyServiceProvider.LazyGetService<ICommonService>().ClearSystemLogAsync();
        }
    }
}
