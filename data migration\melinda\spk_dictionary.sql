select newid() as spk_NexBPMCode,spk_BPMCode,spk_code,spk_Name,spk_type,spk_parentcode,flg 
into #spk_dictionary
from spk_dictionary_Tmp 
where spk_BPMCode is not null and spk_BPMCode <> ''

IF OBJECT_ID(N'dbo.spk_dictionary', N'U') IS NOT NULL
BEGIN
	update a 
	set a.spk_BPMCode    = b.spk_BPMCode
       ,a.spk_code       = b.spk_code
       ,a.spk_Name       = b.spk_Name
       ,a.spk_type       = b.spk_type
       ,a.spk_parentcode = b.spk_parentcode
       ,a.flg            = b.flg 
    from dbo.spk_dictionary a
    join #spk_dictionary b on a.spk_BPMCode = b.spk_BPMCode
    
    insert into dbo.spk_dictionary
    select a.spk_NexBPMCode
          ,a.spk_BPMCode
          ,a.spk_code
          ,a.spk_Name
          ,a.spk_type
          ,a.spk_parentcode
          ,a.flg 
	from #spk_dictionary a
	where NOT EXISTS (SELECT * FROM dbo.spk_dictionary where a.spk_BPMCode = spk_BPMCode)
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into dbo.spk_dictionary from #spk_dictionary
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END;

