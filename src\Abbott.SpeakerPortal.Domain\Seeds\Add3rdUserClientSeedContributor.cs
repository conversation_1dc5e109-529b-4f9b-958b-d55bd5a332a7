﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Models;
using Abbott.SpeakerPortal.Seeds.OpenIddict;

using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Guids;
using Volo.Abp.Identity;

namespace Abbott.SpeakerPortal.Seeds
{
    public class Add3rdUserClientSeedContributor : IDataSeedContributor, ITransientDependency
    {
        IdentityUserManager _userManager;
        IGuidGenerator _guidGenerator;
        OpenIddictDataSeedContributor _openIddictDataSeedContributor;
        const string _collection = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789~`!@#$%^&*_+<.>?";

        public Add3rdUserClientSeedContributor(IdentityUserManager userManager, IGuidGenerator guidGenerator, OpenIddictDataSeedContributor openIddictDataSeedContributor)
        {
            _userManager = userManager;
            _guidGenerator = guidGenerator;
            _openIddictDataSeedContributor = openIddictDataSeedContributor;
        }

        public async Task SeedAsync(DataSeedContext context)
        {
            #region PP

            var clientIdScope = ClientIdScopeConsts.PP;
            if (await _userManager.FindByNameAsync(clientIdScope) == null)
            {
                var user = BuildUser(clientIdScope);
                await _userManager.CreateAsync(user);

                await _openIddictDataSeedContributor.CreateScopeAsync(clientIdScope);
                var properties = BuildProperties();
                await _openIddictDataSeedContributor.CreateApplicationsAsync(clientIdScope, [ClientIdScopeConsts.SpeakerPortal], [GrantTypes.ThirdParty], properties);
            }

            #endregion

            #region Veeva

            clientIdScope = ClientIdScopeConsts.Veeva;
            if (await _userManager.FindByNameAsync(clientIdScope) == null)
            {
                var user = BuildUser(clientIdScope);
                await _userManager.CreateAsync(user);

                await _openIddictDataSeedContributor.CreateScopeAsync(clientIdScope);
                var properties = BuildProperties();
                await _openIddictDataSeedContributor.CreateApplicationsAsync(clientIdScope, [clientIdScope], [GrantTypes.ThirdParty], properties);
            }

            #endregion

            #region EPD HCP Portal

            clientIdScope = ClientIdScopeConsts.EpdHcpPortal;
            if (await _userManager.FindByNameAsync(clientIdScope) == null)
            {
                var user = BuildUser(clientIdScope);
                await _userManager.CreateAsync(user);

                await _openIddictDataSeedContributor.CreateScopeAsync(clientIdScope);
                var properties = BuildProperties();
                await _openIddictDataSeedContributor.CreateApplicationsAsync(clientIdScope, [clientIdScope], [GrantTypes.ThirdParty], properties);
            }

            #endregion

            #region OM
            clientIdScope = ClientIdScopeConsts.OnlineMeeting;
            if (await _userManager.FindByNameAsync(clientIdScope) == null)
            {
                var user = BuildUser(clientIdScope);
                await _userManager.CreateAsync(user);

                await _openIddictDataSeedContributor.CreateScopeAsync(clientIdScope);
                var properties = BuildProperties();
                await _openIddictDataSeedContributor.CreateApplicationsAsync(clientIdScope, [clientIdScope], [GrantTypes.ThirdParty], properties);
            }
            #endregion

            #region Dspot
            clientIdScope = ClientIdScopeConsts.Dspot;
            if (await _userManager.FindByNameAsync(clientIdScope) == null)
            {
                var user = BuildUser(clientIdScope);
                await _userManager.CreateAsync(user);

                await _openIddictDataSeedContributor.CreateScopeAsync(clientIdScope);
                var properties = BuildProperties();
                await _openIddictDataSeedContributor.CreateApplicationsAsync(clientIdScope, [clientIdScope], [GrantTypes.ThirdParty], properties);
            }
            #endregion
            #region SOI
            clientIdScope = ClientIdScopeConsts.SOI;
            if (await _userManager.FindByNameAsync(clientIdScope) == null)
            {
                var user = BuildUser(clientIdScope);
                await _userManager.CreateAsync(user);

                await _openIddictDataSeedContributor.CreateScopeAsync(clientIdScope);
                var properties = BuildProperties();
                await _openIddictDataSeedContributor.CreateApplicationsAsync(clientIdScope, [clientIdScope], [GrantTypes.ThirdParty], properties);
            }
            #endregion

            #region CSS
            clientIdScope = ClientIdScopeConsts.CSS;
            if (await _userManager.FindByNameAsync(clientIdScope) == null)
            {
                var user = BuildUser(clientIdScope);
                await _userManager.CreateAsync(user);

                await _openIddictDataSeedContributor.CreateScopeAsync(clientIdScope);
                var properties = BuildProperties();
                await _openIddictDataSeedContributor.CreateApplicationsAsync(clientIdScope, [clientIdScope], [GrantTypes.ThirdParty], properties);
            }
            #endregion
        }

        IdentityUser BuildUser(string userName)
        {
            var user = new IdentityUser(_guidGenerator.Create(), userName, $"{userName}@abbott.com");
            return user;
        }

        Dictionary<string, JsonElement> BuildProperties()
        {
            var property = new OpenIddictApplicationExtraProperty { Salt = GenerateSalt() };
            var jsonDoc = JsonDocument.Parse(JsonSerializer.Serialize(property));
            return new Dictionary<string, JsonElement>
            {
                { nameof(OpenIddictApplicationExtraProperty), jsonDoc.RootElement }
            };
        }

        string GenerateSalt(int num = 50)
        {
            var buffer = new char[num];
            var random = new Random();
            for (int i = 0; i < 50; i++)
            {
                var index = random.Next(_collection.Length);
                buffer[i] = _collection[index];
            }
            return new string(buffer);
        }
    }
}
