﻿using Abbott.SpeakerPortal.Contracts.Budget;
using Abbott.SpeakerPortal.Contracts.Budget.Subbudget;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.Budget;
using Abbott.SpeakerPortal.Enums;
using Microsoft.AspNetCore.SignalR.Protocol;
using Microsoft.EntityFrameworkCore;
using Senparc.CO2NET.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.ServiceModel;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Data;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using Volo.Abp.Validation.Localization;
using static Abbott.SpeakerPortal.Enums.DataverseEnums.Organization;
using Abbott.SpeakerPortal.Extension;
using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Utils;
using Volo.Abp.DistributedLocking;
using Abbott.SpeakerPortal.Entities.User;

namespace Abbott.SpeakerPortal.AppServices.Budget
{
    /// <summary>
    /// 主预算
    /// </summary>
    public class BdMasterBudgetService : SpeakerPortalAppService, IBdMasterBudgetService
    {
        /// <summary>
        /// 最大预算金额
        /// </summary>
        const decimal MaxAmount = 99999999999999.0000m;
        /// <summary>
        /// 创建主数据
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        public async Task CreateMasterBudget(CreateBudgetRequestDto requestDto)
        {
            var budgetRespository = LazyServiceProvider.LazyGetService<IBdMasterBudgetRepository>();
            var budget = ObjectMapper.Map<CreateBudgetRequestDto, BdMasterBudget>(requestDto);
            budget.Status = true;
            var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            string historyContent = string.Empty;
            historyContent += $"预算金额：{requestDto.BudgetAmount}；";
            var ownerName = queryableUser.First(m => m.Id == requestDto.OwnerId).Name;
            historyContent += $"预算负责人：{ownerName}；";
            historyContent += $"描述：{requestDto.Description}；";
            //historyContent += $"年份：{requestDto.Year}；";
            //string isCapital = requestDto.Capital ? "是" : "否";
            //historyContent += $"是否固定资产：{isCapital}；";
            BdHistory bdHistory = new();
            bdHistory.OperateType = OperateType.Create;
            bdHistory.OperateContent = historyContent;
            bdHistory.OperatorId = CurrentUser.Id.Value;
            bdHistory.OperatorName = CurrentUser.Name;
            bdHistory.OperateAmount = requestDto.BudgetAmount;
            bdHistory.OperatingTime = DateTime.Now;
            bdHistory.BudgetType = BudgetType.MasterBudget;

            bdHistory.BudgetId = await GenerateCode(budgetRespository, budget);
            //保存
            await SaveHistory([bdHistory]);
        }
        /// <summary>
        /// 获取主预算
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetBudgetListResponseDto>> GetMasterBudgetListAsync(GetBudgetListRequestDto request, bool IsPage = true)
        {
            var queryBudget = await LazyServiceProvider.LazyGetService<IBdMasterBudgetRepository>().GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var queryableUser = await LazyServiceProvider.LazyGetService<Entities.User.IIdentityUserRepository>().GetQueryableAsync();
            var queryAuthorizedBudgetBu = await LazyServiceProvider.LazyGetService<IBdAuthorizedBudgetBuRepository>().GetQueryableAsync();
            var BuIds = queryAuthorizedBudgetBu.Where(s => s.UserId == CurrentUser.Id).Select(s => s.BuId).ToList();
            var getBuTask = await dataverseService.GetDivisions(null);
            var query = queryBudget.Include(x => x.SubBudgets)
                .Where(m => BuIds.Contains(m.BuId))
                .WhereIf(!string.IsNullOrWhiteSpace(request.Code), m => m.Code.Contains(request.Code))
                .WhereIf(request.Year.HasValue, m => m.Year == request.Year)
                .WhereIf(request.BuId.HasValue, m => m.BuId == request.BuId)
                .WhereIf(!string.IsNullOrWhiteSpace(request.Description), m => m.Description.Contains(request.Description))
                .WhereIf(request.OwnerId.HasValue, m => m.OwnerId == request.OwnerId)
                .WhereIf(request.Status.HasValue, m => m.Status == request.Status)
                .GroupJoin(queryableUser, a => a.OwnerId, b => b.Id, (a, b) => new { budget = a, owner = b })
                 .SelectMany(a => a.owner.DefaultIfEmpty(), (a, b) => new
                 {
                     a.budget,
                     ownerName = b.Name,
                     OwnerEmail = b.Email,
                     //subBudgetAmount = a.budget.SubBudgets.Sum(s => s.AvailableAmount),
                     isDelete = a.budget.SubBudgets.Count() == 0,
                     subAmount = a.budget.SubBudgets.Sum(s => s.BudgetAmount)
                 });
            //总数
            var count = await query.CountAsync();
            //分页
            query = query.OrderByDescending(m => m.budget.CreationTime);
            query = query.PagingIf(request, IsPage);
            var datas = query.ToList().Select(a => new GetBudgetListResponseDto
            {
                Id = a.budget.Id,
                Code = a.budget.Code,
                BuId = a.budget.BuId,
                BuName = getBuTask.FirstOrDefault(a1 => a1.Id == a.budget.BuId)?.DepartmentName ?? "",
                OwnerName = a.ownerName,
                OwnerEmail = a.OwnerEmail,
                OwnerId = a.budget.OwnerId,
                Capital = a.budget.Capital,
                Description = a.budget.Description,
                BudgetAmount = a.budget.BudgetAmount,
                AdjustableAmount = a.budget.BudgetAmount - a.subAmount,
                Status = a.budget.Status,
                SubBudgetAmount = a.subAmount,
                Year = a.budget.Year,
                isDelete = a.isDelete
            }).ToList();
            var result = new PagedResultDto<GetBudgetListResponseDto>(count, datas);
            return result;
        }
        #region 生成自动主预算编号
        /// <summary>
        /// 生成自动编号
        /// </summary>
        /// <returns></returns>
        private async Task<Guid> GenerateCode(IBdMasterBudgetRepository masterBudgetRepository, BdMasterBudget masterBudget)
        {
            await using (var handle = await LazyServiceProvider.LazyGetService<IAbpDistributedLock>().TryAcquireAsync($"{DistributedLock.DistributedLock_GenerateSerialBudgetNo}", TimeSpan.FromSeconds(5)))
            {
                if (handle != null)
                {
                    int count = 0;
                    masterBudget.Year = masterBudget.Year.HasValue ? (masterBudget.Year.Value < 1000 ? DateTimeOffset.Now.Year : masterBudget.Year.Value) : DateTimeOffset.Now.Year;
                    using (DataFilter.Disable<ISoftDelete>())
                    {
                        var queryBudget = await masterBudgetRepository.GetQueryableAsync();
                        var budgetNo = queryBudget.Where(a => a.Year == masterBudget.Year)
                            //.Select(a => int.Parse(a.Code.Substring(a.Code.IndexOf('-') + 1)))
                            .OrderByDescending(a => a.Code)
                            .FirstOrDefault();
                        if (budgetNo != null)
                        {
                            count = int.Parse(budgetNo.Code.Substring(budgetNo.Code.IndexOf('-') + 1));
                        }
                    }
                    //todo:与BPM并行期间，采购申请单号规则调整5000开始（后续可能需要还原）
                    var countNo = count < 5000 ? (++count + 5000) : (++count);
                    var strYear = masterBudget.Year.ToString();
                    var no = $"MB{strYear.Substring(strYear.Length - 2)}-{countNo:D4}";
                    masterBudget.Code = no;
                    var mBudget = await masterBudgetRepository.InsertAsync(masterBudget, true);
                    return mBudget.Id;
                }
            }
            throw new TimeoutException("生成主预算编号超时，请重新操作");
        }
        /// <summary>
        /// 生成自动编号 批量
        /// </summary>
        /// <returns></returns>
        private async Task GenerateBatchCode(IBdMasterBudgetRepository masterBudgetRepository, List<BdMasterBudget> masterBudgets)
        {
            await using (var handle = await LazyServiceProvider.LazyGetService<IAbpDistributedLock>().TryAcquireAsync($"{DistributedLock.DistributedLock_GenerateSerialBudgetNo}", TimeSpan.FromSeconds(5)))
            {
                if (handle != null)
                {
                    foreach (var budget in masterBudgets)
                    {
                        int count = 0;
                        budget.Year = budget.Year.HasValue ? (budget.Year.Value < 1000 ? DateTimeOffset.Now.Year : budget.Year.Value) : DateTimeOffset.Now.Year;
                        using (DataFilter.Disable<ISoftDelete>())
                        {
                            var queryBudget = await masterBudgetRepository.GetQueryableAsync();
                            var budgetNo = queryBudget.Where(a => a.Year == budget.Year)
                                .OrderByDescending(a => a.Code)
                                .FirstOrDefault();
                            if (budgetNo != null)
                            {
                                count = int.Parse(budgetNo.Code.Substring(budgetNo.Code.IndexOf('-') + 1));
                            }
                        }
                        //todo:与BPM并行期间，采购申请单号规则调整5000开始（后续可能需要还原）
                        var countNo = count < 5000 ? (++count + 5000) : (++count);
                        var strYear = budget.Year.ToString();
                        var no = $"MB{strYear.Substring(strYear.Length - 2)}-{countNo:D4}";
                        budget.Code = no;
                        await masterBudgetRepository.InsertAsync(budget, true);
                    }
                    return;
                }
            }
            throw new TimeoutException("生成主预算编号超时，请重新操作");
        }
        #endregion
        /// <summary>
        /// 删除预算
        /// </summary>
        /// <param name="Ids"></param>
        /// <returns></returns>
        public async Task<MessageResult> DeleteBdMasterBudgetByIdsAsync(List<Guid> Ids)
        {
            var queryBudgetRepository = LazyServiceProvider.LazyGetService<IBdMasterBudgetRepository>();
            var queryBudget = await queryBudgetRepository.GetQueryableAsync();
            var query = queryBudget.Include(x => x.SubBudgets).Where(m => Ids.Contains(m.Id));
            var exsitSubBudget = query.Where(m => m.SubBudgets.Count > 0);
            var exsitSubCount = await exsitSubBudget.CountAsync();
            if (exsitSubCount > 0)
            {
                var mastrCodes = exsitSubBudget.Select(s => s.Code).JoinAsString(",");
                return MessageResult.FailureResult($"{mastrCodes}子预算不为空，不能删除主预算");
            }
            await queryBudgetRepository.DeleteManyAsync(query);
            return MessageResult.SuccessResult("删除成功");
        }
        /// <summary>
        /// 冻结预算
        /// </summary>
        /// <param name="Ids"></param>
        /// <param name="Status"></param>
        /// <returns></returns>
        public async Task<MessageResult> UpdateBudgetStatusAsync(UpdateBudgetStatusRequestDto requestDto)
        {
            var queryBudgetRepository = LazyServiceProvider.LazyGetService<IBdMasterBudgetRepository>();
            var queryBudget = await queryBudgetRepository.GetQueryableAsync();
            var querySubBudgetRepository = LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>();
            var querySubBudget = await querySubBudgetRepository.GetQueryableAsync();
            var querymas = queryBudget.Where(m => requestDto.Ids.Contains(m.Id));
            //判断是否存在与变更预算状态一致的状态
            var sameBudgetCount = await querymas.Where(m => m.Status == requestDto.Status).CountAsync();
            if (sameBudgetCount > 0)
                return MessageResult.FailureResult("请选择与操作保持一致的预算");
            //更改主预算状态
            foreach (var item in querymas)
            {
                item.Status = requestDto.Status;
            }
            //冻结操作更新所有子预算

            var subquery = querySubBudget.Where(m => requestDto.Ids.Contains(m.MasterBudgetId) && m.Status == !requestDto.Status);
            var bdHistorys = new List<BdHistory>();
            string hiscontent = requestDto.Status ? "是" : "否";
            foreach (var item in subquery)
            {
                item.Status = requestDto.Status;
                BdHistory bdHistory = new();
                bdHistory.OperateType = requestDto.Status ? OperateType.Enable : OperateType.Disable;
                bdHistory.OperateContent = $"是否启用：{hiscontent}";
                bdHistory.BudgetId = item.Id;
                bdHistory.OperatorId = CurrentUser.Id.Value;
                bdHistory.OperatorName = CurrentUser.Name;
                bdHistory.OperatingTime = DateTime.Now;
                bdHistory.BudgetType = BudgetType.SubBudget;
                bdHistorys.Add(bdHistory);
            }
            await querySubBudgetRepository.UpdateManyAsync(subquery);
            await queryBudgetRepository.UpdateManyAsync(querymas);
            foreach (var item in requestDto.Ids)
            {
                BdHistory bdHistory = new();
                bdHistory.OperateType = requestDto.Status ? OperateType.Enable : OperateType.Disable;
                bdHistory.OperateContent = $"是否启用：{hiscontent}";
                bdHistory.BudgetId = item;
                bdHistory.OperatorId = CurrentUser.Id.Value;
                bdHistory.OperatorName = CurrentUser.Name;
                bdHistory.OperatingTime = DateTime.Now;
                bdHistory.BudgetType = BudgetType.MasterBudget;
                bdHistorys.Add(bdHistory);
            }
            //保存
            await SaveHistory(bdHistorys);
            return MessageResult.SuccessResult("操作成功");
        }
        /// <summary>
        /// 调整预算
        /// </summary>
        /// <returns></returns>
        public async Task<MessageResult> AdjustmentBudgetAmountAsync(AdjustBudgetAmountRequestDto amountRequestDto)
        {
            var queryBudgetRepository = LazyServiceProvider.LazyGetService<IBdMasterBudgetRepository>();
            var queryBudget = await queryBudgetRepository.GetQueryableAsync();
            var masterBudget = await queryBudget.Include(x => x.SubBudgets).FirstOrDefaultAsync(m => m.Id == amountRequestDto.Id);
            var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            //子预算金额
            var subAmount = masterBudget.SubBudgets.Sum(a => a.BudgetAmount);
            var adjustAfterAmount = masterBudget.BudgetAmount + amountRequestDto.AdjustAmount;
            if (subAmount > adjustAfterAmount)
            {
                return MessageResult.FailureResult("主预算金额不得小于子预算金额");
            }
            if (adjustAfterAmount > MaxAmount) return MessageResult.FailureResult("调整金额超出数值范围");
            masterBudget.BudgetAmount = adjustAfterAmount;
            masterBudget.OwnerId = amountRequestDto.OwnerId;
            //masterBudget.Remark = amountRequestDto.Remark;
            masterBudget.Description = amountRequestDto.Description;
            var ownerName = queryableUser.First(m => m.Id == amountRequestDto.OwnerId).Name;
            //历史记录
            BdHistory bdHistory = new();
            bdHistory.OperateType = OperateType.Update;
            bdHistory.OperateContent = $"调整金额：{amountRequestDto.AdjustAmount};预算负责人：{ownerName};描述:{amountRequestDto.Description}";
            bdHistory.BudgetId = amountRequestDto.Id;
            bdHistory.OperatorId = CurrentUser.Id.Value;
            bdHistory.OperatorName = CurrentUser.Name;
            bdHistory.OperatingTime = DateTime.Now;
            bdHistory.OperateAmount = amountRequestDto.AdjustAmount;
            bdHistory.Remark = amountRequestDto.Remark;
            bdHistory.BudgetType = BudgetType.MasterBudget;
            await queryBudgetRepository.UpdateAsync(masterBudget);
            await SaveHistory([bdHistory]);
            return MessageResult.SuccessResult("操作成功");
        }
        /// <summary>
        /// 调拨预算
        /// </summary>
        /// <returns></returns>
        public async Task<MessageResult> TransferBudgetAmountAsync(TransferBudgetAmountRequestDto requestDto)
        {
            var queryBudgetRepository = LazyServiceProvider.LazyGetService<IBdMasterBudgetRepository>();
            var queryBudget = await queryBudgetRepository.GetQueryableAsync();
            var originalBudget = await queryBudget.Include(x => x.SubBudgets).FirstOrDefaultAsync(m => m.Id == requestDto.OriginalId);
            var transferBudget = await queryBudget.Include(x => x.SubBudgets).FirstOrDefaultAsync(m => m.Id == requestDto.TransferId);
            var adjustableAmount = originalBudget.BudgetAmount - originalBudget.SubBudgets.Sum(m => m.BudgetAmount);
            if (adjustableAmount < requestDto.TransferAmount)
                return MessageResult.FailureResult("调拨金额不能大于可调拨金额");
            if (requestDto.TransferAmount <= 0)
                return MessageResult.FailureResult("调拨金额不能为负数和0");
            originalBudget.BudgetAmount -= requestDto.TransferAmount;
            transferBudget.BudgetAmount += requestDto.TransferAmount;
            var updateBudget = new List<BdMasterBudget> { originalBudget, transferBudget };

            ///历史记录 Todo
            //原预算
            BdHistory orgbdHistory = new()
            {
                OperateType = OperateType.Transfer,
                OperateContent = $"调出预算：{originalBudget.Code};调入预算：{transferBudget.Code}",
                BudgetId = originalBudget.Id,
                OperatorId = CurrentUser.Id.Value,
                OperatorName = CurrentUser.Name,
                OperatingTime = DateTime.Now,
                Remark = requestDto.Remark,
                BudgetType = BudgetType.MasterBudget,
                OperateAmount = -requestDto.TransferAmount,
                TargetBudgetCode = transferBudget.Code,
            };
            //调拨到
            BdHistory tansferbdHistory = new()
            {
                OperateType = OperateType.Transfer,
                OperateContent = $"调出预算：{originalBudget.Code};调入预算：{transferBudget.Code}",
                BudgetId = transferBudget.Id,
                OperatorId = CurrentUser.Id.Value,
                OperatorName = CurrentUser.Name,
                OperatingTime = DateTime.Now,
                Remark = requestDto.Remark,
                BudgetType = BudgetType.MasterBudget,
                OperateAmount = requestDto.TransferAmount,
                TargetBudgetCode = originalBudget.Code,
            };
            await queryBudgetRepository.UpdateManyAsync(updateBudget);
            await SaveHistory([orgbdHistory, tansferbdHistory]);
            return MessageResult.SuccessResult("操作成功");
        }
        /// <summary>
        /// 获取同bu下的预算信息
        /// </summary>
        /// <param name="BuId"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<BudgetResponseDto>> GetBudgetListAsync(TransferRequestDto transfer)
        {
            var queryBudgetRepository = LazyServiceProvider.LazyGetService<IBdMasterBudgetRepository>();
            var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var queryBudget = await queryBudgetRepository.GetQueryableAsync();
            var datas = queryBudget.Where(b => b.BuId == transfer.BuId && b.Status && transfer.Id != b.Id).WhereIf(!string.IsNullOrWhiteSpace(transfer.Code), m => m.Code.Contains(transfer.Code))
                .GroupJoin(queryableUser, a => a.OwnerId, b => b.Id, (a, b) => new { a, b })
                .SelectMany(a => a.b.DefaultIfEmpty(), (a, b) => new { a = a.a, b })
                .Select(s => new BudgetResponseDto
                {
                    Id = s.a.Id,
                    Code = s.a.Code,
                    Description = s.a.Description,
                    OwnerId = s.a.OwnerId,
                    OwnerName = s.b.Name ?? "",
                    Capital = s.a.Capital,
                    BudgetAmount = s.a.BudgetAmount,

                })
                .WhereIf(!string.IsNullOrWhiteSpace(transfer.OwnerName), m => m.OwnerName.Contains(transfer.OwnerName))
                .WhereIf(!string.IsNullOrWhiteSpace(transfer.Description), m => m.Description.Contains(transfer.Description));
            //总数
            var count = await datas.CountAsync();
            //分页
            datas = datas.Skip(transfer.PageIndex * transfer.PageSize).Take(transfer.PageSize);
            var result = new PagedResultDto<BudgetResponseDto>(count, datas.ToArray());
            return result;
        }
        /// <summary>
        /// 根据Id获取预算信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public async Task<BudgetAmountResponseDto> GetBudgetAmountAsync(Guid Id)
        {
            var queryBudgetRepository = LazyServiceProvider.LazyGetService<IBdMasterBudgetRepository>();
            var queryBudget = await queryBudgetRepository.GetQueryableAsync();
            var data = await queryBudget.FirstAsync(b => b.Id == Id);
            BudgetAmountResponseDto budgetAmount = new() { Id = data.Id, Code = data.Code, BudgetAmount = data.BudgetAmount };
            return budgetAmount;
        }
        /// <summary>
        /// 新增历史记录
        /// </summary>
        /// <param name="bdHistorys"></param>
        /// <returns></returns>
        private async Task SaveHistory(List<BdHistory> bdHistorys)
        {
            var historyQueryRepository = LazyServiceProvider.LazyGetService<IBdHistoryRepository>();
            await historyQueryRepository.InsertManyAsync(bdHistorys);
        }
        /// <summary>
        /// 获取详情记录
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public async Task<GetBudgetListResponseDto> GetMasterBudgetRecordByIdAsync(Guid Id)
        {
            var queryBudget = await LazyServiceProvider.LazyGetService<IBdMasterBudgetRepository>().GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var query = await queryBudget.Include(x => x.SubBudgets).FirstAsync(m => m.Id == Id);
            var getBuTask = await dataverseService.GetDivisions();
            var SubBudgetAmount = query.SubBudgets.Sum(a => a.BudgetAmount);
            GetBudgetListResponseDto responseDto = new()
            {
                Id = query.Id,
                Code = query.Code,
                BuId = query.BuId,
                BuName = getBuTask.FirstOrDefault(a1 => a1.Id == query.BuId)?.DepartmentName ?? "",
                OwnerName = queryableUser.FirstOrDefault(m => m.Id == query.OwnerId)?.Name ?? "",
                OwnerId = query.OwnerId,
                Capital = query.Capital,
                Description = query.Description,
                BudgetAmount = query.BudgetAmount,
                AdjustableAmount = query.BudgetAmount - SubBudgetAmount,
                Status = query.Status,
                SubBudgetAmount = SubBudgetAmount,
                Year = query.Year,
            };
            return responseDto;
        }
        /// <summary>
        /// 获取所有主数据
        /// </summary>
        /// <returns></returns>
        public async Task<IList<AllBudgetResponseDto>> GetAllBudgetListAsync(string Code)
        {
            var queryBudgetRepository = LazyServiceProvider.LazyGetService<IBdMasterBudgetRepository>();
            var queryBudget = await queryBudgetRepository.GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var getBuTask = await dataverseService.GetDivisions();

            var datas = queryBudget.Where(b => b.Status)
                .WhereIf(!string.IsNullOrWhiteSpace(Code), m => m.Code.Contains(Code)).Take(20).ToList()
                .Select(s => new AllBudgetResponseDto
                {
                    Id = s.Id,
                    Code = s.Code,
                    BuId = s.BuId,
                    BuName = getBuTask.FirstOrDefault(a1 => a1.Id == s.BuId)?.DepartmentName ?? "",
                }).ToArray();
            return datas;
        }
        /// <summary>
        /// 获取用户授权预算Bu
        /// </summary>
        /// <returns></returns>
        public async Task<List<DepartmentDto>> GetAuthorizedBudgetBuAsync()
        {
            var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var queryAuthorizedBudgetBu = await LazyServiceProvider.LazyGetService<IBdAuthorizedBudgetBuRepository>().GetQueryableAsync();
            //var authorizedBudgetBuIds = (await queryableUser.FirstAsync(u => u.Id == CurrentUser.Id)).GetProperty<string>("AuthorizedBudgetBuId") ?? "";
            var BuIds = queryAuthorizedBudgetBu.Where(s => s.UserId == CurrentUser.Id).Select(s => s.BuId).ToList();

            if (BuIds.Count == 0)
                return default(List<DepartmentDto>);
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var getBuTask = await dataverseService.GetOrganizations();
            var authorizedBu = getBuTask.Where(m => BuIds.Contains(m.Id)).OrderBy(a => a.DepartmentName).ToList();
            return authorizedBu;
        }
        /// <summary>
        /// 解析批量上传excel
        /// </summary>
        /// <returns></returns>
        /// <param name="excelDtos"></param>
        public async Task<MessageResult> AnalyzeCreateMasterBudgetExcelAsync(IEnumerable<CreateMasterBudgetExcelDto> excelDtos)
        {
            var queryableUser = (await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync()).AsNoTracking();
            var userEmails = excelDtos.Select(s => s.OwnerEmail).Where(m => !string.IsNullOrWhiteSpace(m)).ToList();
            var users = await queryableUser.Where(m => userEmails.Contains(m.Email)).ToDictionaryAsync(d => d.Email, v => new { v.Name, v.Id });
            var queryAuthorizedBudgetBu = await LazyServiceProvider.LazyGetService<IBdAuthorizedBudgetBuRepository>().GetQueryableAsync();
            var BuIds = queryAuthorizedBudgetBu.Where(s => s.UserId == CurrentUser.Id).Select(s => s.BuId).ToList();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var orgs = (await dataverseService.GetOrganizations()).Where(x => x.OrganizationType == OrganizationType.Bu).ToDictionary(k => k.DepartmentName, v => new { v.Id, v.DepartmentName });
            List<AnalyzeExcelResponseDto> success = new();
            List<AnalyzeExcelResponseDto> error = new();
            string[] capotal = ["是", "否"];
            int i = 0;
            var rowNo = 5;
            foreach (var item in excelDtos)
            {
                AnalyzeExcelResponseDto analyze = new();
                //analyze.No = ++i;
                analyze.No = ++rowNo;
                var message = string.Empty;
                if (string.IsNullOrWhiteSpace(item.YearText)) message += "请填写年度;";
                else if (int.TryParse(item.YearText, out int year))
                {
                    if (item.YearText.Length != 4) message += "请填写正确的年度";
                    else analyze.Year = year;
                }
                else message += "请填写正确的年度";

                if (string.IsNullOrWhiteSpace(item.Bu)) message += "请填写BU;";
                else if (orgs.TryGetValue(item.Bu, out var Bu))
                {
                    analyze.BuId = Bu.Id;
                    if (BuIds.IndexOf(Bu.Id) == -1) message += "请填写正确的授权BU;";
                }
                else message += "BU不存在;";
                if (string.IsNullOrWhiteSpace(item.Description)) message += "请填写描述;";

                if (string.IsNullOrWhiteSpace(item.OwnerEmail)) message += "请填写负责人邮箱;";
                else if (users.TryGetValue(item.OwnerEmail, out var user)) { analyze.OwnerId = user.Id; analyze.OwnerName = user.Name; }
                else message += "主预算负责人不存在;";

                if (string.IsNullOrWhiteSpace(item.BudgetAmountText)) message += "请填写预算金额;";
                else if (decimal.TryParse(item.BudgetAmountText, out decimal amount))
                {
                    if (amount > MaxAmount) message += "超出预算填写范围;";
                    else if (amount < 0) message += "预算金额不能小于0;";
                    else analyze.BudgetAmount = amount;
                }
                else message += "预算金额请填写数字;";

                if (string.IsNullOrWhiteSpace(item.CapitalText)) message += "请填写是否固定资产";
                else if (!capotal.Contains(item.CapitalText)) message += "是否固定资产必须是是和否;";
                else analyze.Capital = (item.CapitalText == "是");


                analyze.YearText = item.YearText;
                analyze.Bu = item.Bu;
                analyze.Description = item.Description;
                analyze.OwnerEmail = item.OwnerEmail;
                analyze.BudgetAmountText = item.BudgetAmountText;
                analyze.CapitalText = item.CapitalText;
                analyze.Remark = item.Remark;


                if (string.IsNullOrEmpty(message)) success.Add(analyze);
                else
                {
                    analyze.Message = message;
                    error.Add(analyze);
                }

            }
            if (error.Count() > 0)
                return MessageResult.SuccessResult(new ImportDataResponseDto<AnalyzeExcelResponseDto>(error, false));
            return MessageResult.SuccessResult(new ImportDataResponseDto<AnalyzeExcelResponseDto>(success, true));
        }
        /// <summary>
        /// 批量新增主预算
        /// </summary>
        /// <returns></returns>
        /// <param name="request"></param>
        public async Task<MessageResult> CreatesMasterBudgetAsync(ImportDataResponseDto<AnalyzeExcelResponseDto> request)
        {
            var BudgetRepository = LazyServiceProvider.LazyGetService<IBdMasterBudgetRepository>();
            //int count;
            //var now = DateTimeOffset.Now;
            //using (DataFilter.Disable<ISoftDelete>())
            //{
            //    count = await BudgetRepository.CountAsync(a => a.CreationTime.Date.Year == now.Year);
            //}
            List<BdMasterBudget> budgets = new();
            List<BdHistory> historys = new();
            foreach (var item in request.Datas)
            {
                Guid Id = Guid.NewGuid();
                BdMasterBudget bdMaster = new()
                {
                    //Code = $"MB{now:yy}-{++count:D4}",
                    BuId = item.BuId.Value,
                    Description = item.Description,
                    OwnerId = item.OwnerId.Value,
                    Capital = item.Capital.Value,
                    BudgetAmount = item.BudgetAmount.Value,
                    Status = true,
                    Remark = item.Remark,
                    Year = item.Year,
                };
                bdMaster.SetId(Id);
                budgets.Add(bdMaster);
                //添加历史记录
                string historyContent = string.Empty;
                historyContent += $"预算金额：{item.BudgetAmount}；";
                historyContent += $"预算负责人：{item.OwnerName}；";
                historyContent += $"描述：{item.Description}；";
                BdHistory bdHistory = new();
                bdHistory.OperateType = OperateType.Create;
                bdHistory.OperateContent = historyContent;
                bdHistory.OperatorId = CurrentUser.Id.Value;
                bdHistory.OperatorName = CurrentUser.Name;
                bdHistory.OperateAmount = item.BudgetAmount;
                bdHistory.OperatingTime = DateTime.Now;
                bdHistory.BudgetType = BudgetType.MasterBudget;
                bdHistory.BudgetId = Id;
                historys.Add(bdHistory);
            }
            //await BudgetRepository.InsertManyAsync(budgets);
            await GenerateBatchCode(BudgetRepository, budgets);
            await SaveHistory(historys);
            return MessageResult.SuccessResult("新增成功");
        }
    }
}
