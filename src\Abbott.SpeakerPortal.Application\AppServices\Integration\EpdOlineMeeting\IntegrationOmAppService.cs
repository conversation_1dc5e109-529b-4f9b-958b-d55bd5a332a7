﻿using Abbott.SpeakerPortal.BackgroundWorkers.BPMEmail;
using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Budget.Subbudget;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.Approve;
using Abbott.SpeakerPortal.Contracts.Common.BPMEmail;
using Abbott.SpeakerPortal.Contracts.Common.Common;
using Abbott.SpeakerPortal.Contracts.Common.PowerApp;
using Abbott.SpeakerPortal.Contracts.Integration.EpdOlineMeeting;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.Budget;
using Abbott.SpeakerPortal.Entities.Common.Workflow.WorkflowTasks;
using Abbott.SpeakerPortal.Entities.Integration.Dspot;
using Abbott.SpeakerPortal.Entities.Integration.EpdOnlineMeeting;
using Abbott.SpeakerPortal.Entities.Purchase.PurGRApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Entities.SystemConfig.Slide;
using Abbott.SpeakerPortal.Entities.VendorFinancials;
using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Extension;
using Abbott.SpeakerPortal.Vendor.Speaker;

using DocumentFormat.OpenXml.ExtendedProperties;

using Flurl.Http;
using Hangfire;
using Hangfire.Logging;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

using Org.BouncyCastle.Asn1.Ocsp;
using Polly;
using Senparc.CO2NET.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Threading.Tasks;

using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;

using static Abbott.SpeakerPortal.Enums.Purchase;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace Abbott.SpeakerPortal.AppServices.Integration.EpdOlineMeeting
{
    public class IntegrationOmAppService : SpeakerPortalAppService, IIntegrationOmAppService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<IntegrationOmAppService> _logger;
        private readonly IConfiguration _configuration;
        private readonly IOmAuthorizationService _omAuthorizationService;
        private readonly ICommonService _commonService;

        public IntegrationOmAppService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _logger = serviceProvider.GetService<ILogger<IntegrationOmAppService>>();
            _configuration = serviceProvider.GetService<IConfiguration>();
            _omAuthorizationService = serviceProvider.GetService<IOmAuthorizationService>();
            _commonService = _serviceProvider.GetService<ICommonService>();
        }

        /// <summary>
        /// 调用OM api将会议推送到OM
        /// </summary>
        /// <param name="prNo"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        /// <exception cref="Exception"></exception>
        public async Task<MessageResult> OmAddMeeting(string prNo)
        {
            string postString = "";
            var log = new SetOperationLogRequestDto();
            PurPRApplication pr = null;
            try
            {
                _logger.LogWarning("Start to execute OmAddMeeting, pr number:" + prNo + "!");
                if (string.IsNullOrWhiteSpace(prNo))
                {
                    _logger.LogWarning("End to execute OmAddMeeting, empty pr number!");
                    return MessageResult.FailureResult("Empty pr number!");
                }

                var prRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>();
                pr = await prRepository.SingleOrDefaultAsync(x => x.ApplicationCode == prNo && x.IsEsignUsed == true);
                if (pr == null)
                {
                    _logger.LogWarning("End to execute OmAddMeeting, no found pr, pr number:" + prNo + "!");
                    return MessageResult.FailureResult("No found pr, pr number:" + prNo + "!");
                }

                // 会议已被OM激活
                // 会议状态是空或者1000才推送 clx 2024/09/13
                if (!(string.IsNullOrEmpty(pr.MeetingStatus) || pr.MeetingStatus == "1000"))
                {
                    _logger.LogWarning("End to execute OmAddMeeting, failed to push meeting because that the meeting has already been activated!");
                    return MessageResult.FailureResult("Failed to push meeting because that the meeting has already been activated!");
                }

                // 只推送有效明细行, 支付方式AR，供应商为讲者且有EPD主键，费用性质为需要推送
                var prDetailRepsitory = LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>();
                var prDetails = await prDetailRepsitory.GetListAsync(x => x.PRApplicationId == pr.Id && x.PayMethod == Enums.Purchase.PayMethods.AR && x.VendorId.HasValue);
                //zhx20240705: 筛选费用性质
                prDetails = FiltrateCostNature(prDetails);
                //过滤反冲行和被反冲行
                prDetails = FilterHedge(prDetails);
                if (prDetails?.Any() != true)
                {
                    _logger.LogWarning("End to execute OmAddMeeting, no valid prDetails need push to om, pr number:" + prNo + "!");
                    return MessageResult.FailureResult("No valid prDetails need push to om, pr number:" + prNo + "!");
                }

                _logger.LogWarning("Processing OmAddMeeting, start to build speakers , pr number:" + prNo + "!");
                var speakers = await BuindSpeakers(prDetails);
                _logger.LogWarning("Processing OmAddMeeting, end to build speakers, pr number:" + prNo + "!");

                if (speakers == null || speakers.Item1.Count == 0)
                {
                    _logger.LogWarning("End to execute OmAddMeeting, no valid row data need push to om, pr number:" + prNo + "!");
                    return MessageResult.FailureResult("No valid row data need push to om, pr number:" + prNo + "!");
                }

                // 构造推送结构
                OmMeetingAddRequestDto meetingAddRequest = await BuildOmMeetingAddRequest(pr);
                meetingAddRequest.MainSpeakerList = speakers.Item1;
                meetingAddRequest.StandbySpeakerList = speakers.Item2;

                // 发送请求前，校验数据
                MessageResult validateResult = OmMeetingAddRequestDto.Validate(meetingAddRequest);
                if (!validateResult.Success)
                {
                    _logger.LogWarning("End to execute OmAddMeeting, validate message: " + validateResult.Message + "pr number:" + prNo);
                    return validateResult;
                }

                //获取本次推送OM的所有PRDetail的行号，以便后续修改这些被推送PRDetail的MeetingStatus
                var prNoList = OmMeetingAddRequestDto.GetSpeakersNo(meetingAddRequest);

                // 和om接口通信
                var url = _configuration["Integrations:OM:BaseUrl"]!.TrimEnd('/') + "/api/nextBpm/meeting/add";
                var headers = _omAuthorizationService.BuildAuthorizationHeaders();
                if (headers == null)
                {
                    _logger.LogWarning("End to execute OmAddMeeting, failed to build authorization headers, pr number:" + prNo + "!");
                    return MessageResult.FailureResult("Failed to build authorization headers, pr number: " + prNo + "!");
                }
                UpdateAddMeetingStandbySpeaker(meetingAddRequest);
                postString = JsonSerializer.Serialize(meetingAddRequest);
                _logger.LogWarning("Call om api to add meeting start, pr number:" + prNo + ", request:" + postString);
                log = _commonService.InitOperationLog("Epd Online Meeting", "添加会议", url + "|" + postString);

                var response = await url.WithHeaderJson(headers).PostStringAsync(postString);
                string responseData = await response.GetStringAsync();
                //string responseData = "{\"requestId\":\"8b71b3e5-72ed-474e-9a92-2e5ed0766fba\",\"timestamp\":1718204604457,\"success\":true,\"code\":200,\"message\":\"Success\"}";
                _logger.LogWarning("End to execute OmAddMeeting, pr number: " + prNo + ", response:" + responseData);
                log.FormId = pr.Id;
                log.FormNo = pr.ApplicationCode;
                _commonService.LogResponse(log, responseData);
                var resObj = JsonSerializer.Deserialize<OmResponseDto>(responseData);
                if (resObj.success)
                {
                    var meetingStatus = "1000";
                    // 推送成功
                    pr.MeetingStatus = meetingStatus;

                    //zhx20240704:修改PrDetail.MeetingStatus
                    await UpdatePrDetailMeetingStatus(pr.Id, meetingStatus, prNoList);
                }
                else
                {
                    //失败邮件提醒  EPD在线会议推送（实时）
                    await CallOMApiFailAsync(pr, pr.ApplicationCode, responseData);

                    // 推送失败
                    //pr.MeetingStatus = "-1000";
                    return MessageResult.FailureResult(resObj.message);
                }
                await prRepository.UpdateAsync(pr);
                return resObj.success ? MessageResult.SuccessResult(resObj.data) : MessageResult.FailureResult(resObj.message);
            }
            catch (FlurlHttpException ex) when (ex.Call.HttpResponseMessage.StatusCode == HttpStatusCode.BadRequest)
            {
                // 获取响应内容
                var responseContent = await ex.GetResponseStringAsync();
                // 处理解析后的数据
                try {
                    _commonService.LogResponse(log, responseContent, false);
                }catch (Exception e) {
                    _logger.LogError(e, "Build Param for OmAddMeeting Error, pr number: " + prNo + ", request: " + postString + ", error message: " + e.Message + "!");
                }
                
                await CallOMApiFailAsync(pr, prNo, $"error:{ex.Message},\n error_data:{responseContent}");//失败邮件提醒

                return MessageResult.FailureResult("Failed to add meeting to OM, pr number:" + prNo + ", error message:" + responseContent + "!");
            }
            catch (Exception ex)
            {
                try
                {
                    _commonService.LogResponse(log, ex.ToString(), false);
                }
                catch (Exception e)
                {
                    _logger.LogError(e, "Build Param for OmAddMeeting Error, pr number: " + prNo + ", request: " + postString + ", error message: " + e.Message + "!");
                }
                
                _logger.LogError(ex, "End to execute OmAddMeeting, pr number: " + prNo + ", request: " + postString + ", error message: " + ex.Message + "!");

                await CallOMApiFailAsync(pr, prNo, ex.Message);//失败邮件提醒

                // TODO 记录并重试
                return MessageResult.FailureResult("Failed to add meeting to OM, pr number:" + prNo + ", error message:" + ex.Message + "!");
            }
        }


        /// <summary>
        /// 失败邮件提醒
        /// </summary>
        /// <param name="pr"></param>
        /// <param name="applicationCode"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        private async Task CallOMApiFailAsync(PurPRApplication pr, string applicationCode, string message)
        {

            Guid applyUserId = pr?.ApplyUserId ?? Guid.Empty;
            string helpdeskEmail = _configuration["SpeakerEmail:BPMHelpdeskEmail"];
            string l2CCEmail = _configuration["SpeakerEmail:VeevaTimeoutCCEmail"];//TODO
            var prUser = (await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync()).AsNoTracking().FirstOrDefault(a => a.Id == pr.ApplyUserId);
            List<string> ccEmails = new List<string>();
            ccEmails.AddIf(!string.IsNullOrWhiteSpace(l2CCEmail), l2CCEmail);
            ccEmails.AddIf(!string.IsNullOrWhiteSpace(prUser?.Email), prUser.Email);
            var sendEmaillRecords = new InsertSendEmaillRecordDto
            {
                EmailAddress = helpdeskEmail,
                CCAddresses = string.Join(",", ccEmails),//抄送L2和该PR申请的申请人邮箱
                Subject = "[NexBPM] EPD在线会议推送失败提醒。",
                Content = $"会议{applicationCode}推送OM失败，报错内容为：{message}，请知悉。",
                SourceType = EmailSourceType.EPDOmAddMeetingFail,
                Status = SendStatus.Pending,
                Attempts = 0,
            };
            //记录邮件，并触发邮件发送功能
            var emailService = LazyServiceProvider.LazyGetService<IEmailService>();
            await emailService.InsertSendEmaillRecordAsync([sendEmaillRecords]);
            // 调度作业
            BackgroundJob.Enqueue<SendEmailWorker>(a => a.DoWorkAsync());
        }

        private void UpdateAddMeetingStandbySpeaker(OmMeetingAddRequestDto meetingAddRequest)
        {

            //主讲者通过 no关联对应的备选讲者
            foreach (var item in meetingAddRequest.MainSpeakerList)
            {
                item.StandbySpeakerItems = meetingAddRequest.StandbySpeakerList.Where(s => s.No == item.No).ToList();
            }
            meetingAddRequest.StandbySpeakerList = null;

        }

        /// <summary>
        /// 调用OM api，作废会议
        /// </summary>
        /// <param name="prNo"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        /// <exception cref="Exception"></exception>
        public async Task<MessageResult> OmRevokeMeeting(string prNo)
        {
            string postString = "";
            var log = _commonService.InitOperationLog("Epd Online Meeting", "作废会议", null);
            try
            {
                _logger.LogWarning($"单号：{prNo}，OmRevokeMeeting push start!");
                if (string.IsNullOrWhiteSpace(prNo))
                    return MessageResult.FailureResult("Empty pr number!");

                var prRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>();
                var pr = await prRepository.SingleOrDefaultAsync(x => x.ApplicationCode == prNo && x.IsEsignUsed == true);
                if (pr == null)
                    return MessageResult.FailureResult("No found pr, pr number:" + prNo + "!");

                var meetingRevokeRequest = new OmMeetingRevokeRequestDto { SerialNumberPr = prNo };
                var url = _configuration["Integrations:OM:BaseUrl"]!.TrimEnd('/') + "/api/nextBpm/meeting/revoke";
                var headers = _omAuthorizationService.BuildAuthorizationHeaders();
                if (headers == null)
                    return MessageResult.FailureResult("Failed to build authorization headers, pr number: " + prNo + "!");

                postString = JsonSerializer.Serialize(meetingRevokeRequest);
                log.Content = url + "|" + postString;
                log.FormId = pr.Id;
                log.FormNo = pr.ApplicationCode;

                var response = await url.WithHeaderJson(headers).PostStringAsync(postString);
                string responseData = await response.GetStringAsync();
                _commonService.LogResponse(log, responseData);
                var resObj = JsonSerializer.Deserialize<OmResponseDto>(responseData);
                if (resObj.success)
                {
                    var meetingStatus = "1003";
                    // nextBpm作废会议成功
                    pr.MeetingStatus = meetingStatus;

                    //zhx20240704:修改PrDetail.MeetingStatus
                    await UpdatePrDetailMeetingStatus(pr.Id, meetingStatus, null, true);
                }
                else
                {
                    // nextBpm作废会议失败
                    //pr.MeetingStatus = "-1003";
                    return MessageResult.FailureResult(resObj.message);
                }

                await prRepository.UpdateAsync(pr);
                return resObj.success ? MessageResult.SuccessResult(resObj.data) : MessageResult.FailureResult(resObj.message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"单号：{prNo}，{ex.Message}");
                _commonService.LogResponse(log, ex.ToString(), false);
                _logger.LogError(ex, $"单号：{prNo}，{JsonSerializer.Serialize(log)}");
                // TODO 记录并重试
                return MessageResult.FailureResult("Failed to revoke meeting to OM, pr number:" + prNo + ", error message:" + ex.Message + "!");
            }
        }

        /// <summary>
        /// 调用OM api，付款作废或线下递交
        /// </summary>
        /// <param name="prNo"></param>
        /// <param name="paNo"></param>
        /// <param name="status"></param>
        /// <param name="remark"></param>
        /// <returns></returns>
        public async Task<MessageResult> OmUpdateMeetingSpeakerStatus(string paNo, string status, string remark)
        {
            string postString = "";
            var log = new SetOperationLogRequestDto();

            try
            {
                _logger.LogWarning("Start to execute OmUpdateMeetingSpeakerStatus, pa number:" + paNo + "!");
                StringBuilder sb = new StringBuilder();
                if (string.IsNullOrWhiteSpace(paNo))
                {
                    sb.AppendLine("Empty pa number!");
                }

                if (string.IsNullOrWhiteSpace(status))
                {
                    sb.AppendLine("Empty status!");
                }
                else if (status != "线下递交" && status != "作废")
                {
                    sb.AppendLine("Invalid status, only support:线下递交|作废!");
                }

                if (sb.Length > 0)
                {
                    _logger.LogWarning("End to execute OmUpdateMeetingSpeakerStatus, pa number:" + paNo + ", error message: " + sb.ToString() + "!");
                    return MessageResult.FailureResult(sb.ToString());
                }

                var paRepository = LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>();
                var pa = await paRepository.SingleOrDefaultAsync(x => x.ApplicationCode == paNo);
                if (pa == null)
                {
                    _logger.LogWarning("End to execute OmUpdateMeetingSpeakerStatus, no found pa, pa number:" + paNo + "!");
                    return MessageResult.FailureResult("No found pa, pa number:" + paNo + "!");
                }

                var prRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>();
                var pr = await prRepository.SingleOrDefaultAsync(x => x.Id == pa.PRId);
                if (pr == null)
                {
                    _logger.LogWarning("End to execute OmUpdateMeetingSpeakerStatus, no found pr, pa number:" + paNo + "!");
                    return MessageResult.FailureResult("No found pr, pa number:" + paNo + "!");
                }

                if (pr.MeetingStatus != "1002")
                {
                    _logger.LogWarning("End to execute OmUpdateMeetingSpeakerStatus, pr meeting status is not 1002-结算, pa number:" + paNo + "!");
                    return MessageResult.FailureResult("Pr meeting status is not 1002-结算, pa number:" + paNo + "!");
                }

                //zhx20240708:链接查询，同时查出gr+grDetail，以供后续使用
                var queryGr = LazyServiceProvider.LazyGetService<IPurGRApplicationRepository>().GetQueryableAsync().GetAwaiterResult();
                var queryGrDetail = LazyServiceProvider.LazyGetService<IPurGRApplicationDetailRepository>().GetQueryableAsync().GetAwaiterResult();
                var grGrDetail = queryGr.Where(a => a.Id == pa.GRId)
                    .Join(queryGrDetail, l => l.Id, r => r.GRApplicationId, (l, r) => new { gr = l, grDetail = r })
                    .FirstOrDefault();

                if (grGrDetail == null)
                {
                    _logger.LogWarning("End to execute OmUpdateMeetingSpeakerStatus, no found gr or grDetail, pa number:" + paNo + "!");
                    return MessageResult.FailureResult("No found gr or grDetail, pa number:" + paNo + "!");
                }

                OmMeetingOfflineSubmissionRequestDto meetingOfflineSubmissionRequest = new OmMeetingOfflineSubmissionRequestDto();
                meetingOfflineSubmissionRequest.SerialNumberPr = grGrDetail.gr.PrApplicationCode;
                meetingOfflineSubmissionRequest.SerialNumberPa = paNo;
                meetingOfflineSubmissionRequest.Status = status;
                meetingOfflineSubmissionRequest.Remark = remark;

                var url = _configuration["Integrations:OM:BaseUrl"]!.TrimEnd('/') + "/api/nextBpm/meetingSpeaker/bpmStatus";
                var headers = _omAuthorizationService.BuildAuthorizationHeaders();
                if (headers == null)
                {
                    _logger.LogWarning("End to execute OmUpdateMeetingSpeakerStatus, failed to build authorization headers, pa number:" + paNo + "!");
                    return MessageResult.FailureResult("Failed to build authorization headers, pa number: " + paNo + "!");
                }
                postString = JsonSerializer.Serialize(meetingOfflineSubmissionRequest);
                _logger.LogWarning("Call om api to update meeting speaker status start, pa number:" + paNo + ", request:" + postString + "!");
                log = _commonService.InitOperationLog("Epd Online Meeting", "付款作废或线下", url + "|" + postString);

                var response = await url.WithHeaderJson(headers).PostStringAsync(postString);

                string responseData = await response.GetStringAsync();
                _logger.LogWarning("End to execute OmUpdateMeetingSpeakerStatus, pa number: " + paNo + ", response:" + responseData);

                log.FormId = pr.Id;
                log.FormNo = pr.ApplicationCode;
                _commonService.LogResponse(log, responseData);

                var resObj = JsonSerializer.Deserialize<OmResponseDto>(responseData);
                if (resObj.success)
                {
                    var meetingStatus = "1005";
                    // 付款线下递交或者作废成功
                    //pr.MeetingStatus = meetingStatus;

                    //zhx20240704:修改PrDetail.MeetingStatus
                    UpdatePrDetailMeetingStatusById(grGrDetail.grDetail.PRDetailId, meetingStatus, true);
                }
                else
                {
                    // 付款线下递交或者作废失败
                    //pr.MeetingStatus = "-1005";
                    return MessageResult.FailureResult(resObj.message);
                }
                return resObj.success ? MessageResult.SuccessResult(resObj.data) : MessageResult.FailureResult(resObj.message);
            }
            catch (Exception ex)
            {
                _commonService.LogResponse(log, ex.ToString(), false);
                _logger.LogError(ex, "End to execute OmUpdateMeetingSpeakerStatus, failed to update meeting speaker status, pa number:" + paNo + ", request: " + postString + ", error message: " + ex.Message + "!");
                // TODO 记录并重试
                return MessageResult.FailureResult("Failed to update meeting speaker status, pa number:" + paNo + ", error message: " + ex.Message + "!");
            }
        }

        /// <summary>
        /// 调用OM api，打印页面推送
        /// </summary>
        /// <param name="paNo"></param>
        /// <returns></returns>
        public async Task<MessageResult> OmAddPrintPAPR(string paNo)
        {
            string postString = "";
            _logger.LogWarning("Start to execute OmAddPrintPAPR, pa number:" + paNo + "!");
            if (string.IsNullOrWhiteSpace(paNo))
            {
                _logger.LogWarning("End to execute OmAddPrintPAPR, empty pa number!");
                return MessageResult.FailureResult("Empty pa number!");
            }
            var log = new SetOperationLogRequestDto();
            try
            {
                var paRepository = LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>();
                var pa = await paRepository.SingleOrDefaultAsync(x => x.ApplicationCode == paNo);
                if (pa == null)
                {
                    _logger.LogWarning("End to execute OmAddPrintPAPR, no found pa, pa number:" + paNo + "!");
                    return MessageResult.FailureResult("No found pa, pa number:" + paNo + "!");
                }

                var grDetailRepository = LazyServiceProvider.LazyGetService<IPurGRApplicationDetailRepository>();
                var gr = await grDetailRepository.FirstOrDefaultAsync(x => x.GRApplicationId == pa.GRId);
                if (gr == null)
                {
                    _logger.LogWarning("End to execute OmAddPrintPAPR, no found gr detail, pa number:" + paNo + "!");
                    return MessageResult.FailureResult("No found gr detail, pa number:" + paNo + "!");
                }

                var prDetailRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>();
                var prDetail = await prDetailRepository.SingleOrDefaultAsync(x => x.Id == gr.PRDetailId);
                if (prDetail == null)
                {
                    _logger.LogWarning("End to execute OmAddPrintPAPR, no found pr detail, pa number:" + paNo + "!");
                    return MessageResult.FailureResult("No found pr detail, pa number:" + paNo + "!");
                }

                var prRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>();
                var pr = await prRepository.SingleAsync(x => x.Id == prDetail.PRApplicationId);
                if (pr.MeetingStatus != "1002")
                {
                    _logger.LogWarning("End to execute OmAddPrintPAPR, pr meeting status is not 1002-结算, pa number:" + paNo + "!");
                    return MessageResult.FailureResult("pr meeting status is not 1002-结算, pa number:" + paNo + "!");
                }

                //var omAuthService = LazyServiceProvider.LazyGetService<IOmAuthorizationService>();
                //var prParameter = await omAuthService.BuildSignParametersAsync(pr.Id);
                //var paParameter = await omAuthService.BuildSignParametersAsync(pa.Id);

                var headers = _omAuthorizationService.BuildAuthorizationHeaders();
                OmMeetingPrintPAPRRequestDto meetingPrintPAPRRequest = new OmMeetingPrintPAPRRequestDto();
                meetingPrintPAPRRequest.SerialNumberPR = pr.ApplicationCode;
                meetingPrintPAPRRequest.No = prDetail.RowNo;
                meetingPrintPAPRRequest.VendorName = prDetail.VendorName;
                meetingPrintPAPRRequest.VendorCode = prDetail.VendorCode;
                //meetingPrintPAPRRequest.PrintUrlPR = _configuration["Integrations:OM:PrintUrl"]!.TrimEnd('/') + "/print/procureRequestPrint/" + pr.Id;
                meetingPrintPAPRRequest.PrintUrlPR = $"{_configuration["App:SelfUrl"]}/api/EpdOmPrintPage/GetPRPrintPage?id={pr.Id}";
                meetingPrintPAPRRequest.SerialNumberPA = pa.ApplicationCode;
                //meetingPrintPAPRRequest.PrintUrlPA = _configuration["Integrations:OM:PrintUrl"]!.TrimEnd('/') + "/print/paymentRequestPrint/" + pa.Id;
                meetingPrintPAPRRequest.PrintUrlPA = $"{_configuration["App:SelfUrl"]}/api/EpdOmPrintPage/GetPAPrintPage?id={pa.Id}";

                var url = _configuration["Integrations:OM:BaseUrl"]!.TrimEnd('/') + "/api/nextBpm/meeting/addPrintPAPR";
                if (headers == null)
                {
                    _logger.LogWarning("End to execute OmAddPrintPAPR, failed to build authorization headers, pa number:" + paNo + "!");
                    return MessageResult.FailureResult("Failed to build authorization headers, pa number: " + paNo + "!");
                }
                postString = JsonSerializer.Serialize(meetingPrintPAPRRequest, new JsonSerializerOptions
                {
                    Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
                    WriteIndented = true
                });
                _logger.LogWarning("Call om api to add PAPR start, pa number:" + paNo + ", request:" + postString + "!");
                log = _commonService.InitOperationLog("Epd Online Meeting", "打印页面推送", url + "|" + postString);
                var response = await url.WithHeaderJson(headers).PostStringAsync(postString);
                string responseData = await response.GetStringAsync();
                _logger.LogWarning("End to execute OmAddPrintPAPR, pa number: " + paNo + ", response:" + responseData);

                log.FormId = pr.Id;
                log.FormNo = pr.ApplicationCode;
                _commonService.LogResponse(log, responseData);
                var resObj = JsonSerializer.Deserialize<OmResponseDto>(responseData);
                if (resObj.success)
                {
                    var meetingStatus = "1006";
                    // 打印页面推送成功
                    //pr.MeetingStatus = meetingStatus;

                    //zhx20240704:修改PrDetail.MeetingStatus
                    UpdatePrDetailMeetingStatusById(prDetail.Id, meetingStatus, true);
                }
                else
                {
                    // 打印页面推送失败
                    //pr.MeetingStatus = "-1006";
                    return MessageResult.FailureResult(resObj.message);
                }
                return resObj.success ? MessageResult.SuccessResult(resObj.data) : MessageResult.FailureResult(resObj.message);
            }
            catch (Exception ex)
            {
                _commonService.LogResponse(log, ex.ToString(), false);
                _logger.LogError(ex, "End to execute OmAddPrintPAPR, failed to add PAPR, pa number:" + paNo + ", request: " + postString + ", error message: " + ex.Message + "!");
                // TODO 记录并重试
                return MessageResult.FailureResult(ex.Message);
            }
        }

        /// <summary>
        /// 调用OM api，会议复审状态
        /// </summary>
        /// <param name="paNo"></param>
        /// <param name="approvalPerson"></param>
        /// <param name="approvalTime"></param>
        /// <returns></returns>
        public async Task<MessageResult> OmReviewMeeting(string paNo, string approvalPerson, string approvalTime)
        {
            string postString = "";
            _logger.LogWarning("Start to execute OmReviewMeeting, pa number:" + paNo + "!");
            StringBuilder sb = new StringBuilder();
            if (string.IsNullOrWhiteSpace(paNo))
            {
                sb.AppendLine("Empty pa number!");
            }

            if (string.IsNullOrWhiteSpace(approvalPerson))
            {
                sb.AppendLine("Empty approval person!");
            }

            if (string.IsNullOrWhiteSpace(approvalTime))
            {
                sb.AppendLine("Empty approval time!");
            }
            else if (!DateTime.TryParse(approvalTime, out var date))
            {
                sb.AppendLine("Invalid approval time:" + date + "!");
            }

            if (sb.Length > 0)
            {
                _logger.LogWarning("End to execute OmReviewMeeting, validate message: " + sb.ToString());
                return MessageResult.FailureResult(sb.ToString());
            }
            var log = new SetOperationLogRequestDto();

            try
            {
                var paRepository = LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>();
                var pa = await paRepository.SingleOrDefaultAsync(x => x.ApplicationCode == paNo);
                if (pa == null)
                {
                    _logger.LogWarning("End to execute OmReviewMeeting, no found pa, pa number:" + paNo + "!");
                    return MessageResult.FailureResult("No found pa, pa number:" + paNo + "!");
                }

                var grDetailRepository = LazyServiceProvider.LazyGetService<IPurGRApplicationDetailRepository>();
                var gr = await grDetailRepository.FirstOrDefaultAsync(x => x.GRApplicationId == pa.GRId);
                if (gr == null)
                {
                    _logger.LogWarning("End to execute OmReviewMeeting, no found gr detail, pa number:" + paNo + "!");
                    return MessageResult.FailureResult("No found gr detail, pa number:" + paNo + "!");
                }

                var prDetailRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>();
                var prDetail = await prDetailRepository.SingleOrDefaultAsync(x => x.Id == gr.PRDetailId);
                if (prDetail == null)
                {
                    _logger.LogWarning("End to execute OmReviewMeeting, no found pr detail, pa number:" + paNo + "!");
                    return MessageResult.FailureResult("No found pr detail, pa number:" + paNo + "!");
                }

                //var prRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>();
                //var pr = await prRepository.SingleAsync(x => x.Id == prDetail.PRApplicationId);
                //if (pr.MeetingStatus != "1006")
                //{
                //    _logger.LogWarning("End to execute OmReviewMeeting, pr meeting status is not 1006-打印页面, pa number:" + paNo + "!");
                //    return MessageResult.FailureResult("Pr meeting status is not 1006-打印页面, pa number:" + paNo + "!");
                //}

                OmMeetingReviewRequestDto meetingReviewRequest = new OmMeetingReviewRequestDto();
                meetingReviewRequest.PaNo = paNo;
                meetingReviewRequest.Status = "复审完成";
                meetingReviewRequest.ApprovalPerson = approvalPerson;
                meetingReviewRequest.ApprovalTime = approvalTime;

                var url = _configuration["Integrations:OM:BaseUrl"]!.TrimEnd('/') + "/api/nextBpm/meeting/review";
                var headers = _omAuthorizationService.BuildAuthorizationHeaders();
                if (headers == null)
                {
                    _logger.LogWarning("End to execute OmReviewMeeting, failed to build authorization headers, pa number:" + paNo + "!");
                    return MessageResult.FailureResult("Failed to build authorization headers, pa number: " + paNo + "!");
                }
                postString = JsonSerializer.Serialize(meetingReviewRequest);
                _logger.LogWarning("Call om api to review meeting start, pa number:" + paNo + ", request:" + postString + "!");
                log = _commonService.InitOperationLog("Epd Online Meeting", "会议复审状态", url + "|" + postString);

                var response = await url.WithHeaderJson(headers).PostStringAsync(postString);
                string responseData = await response.GetStringAsync();
                _logger.LogWarning("End to execute OmReviewMeeting, pa number: " + paNo + ", response:" + responseData);
                _commonService.LogResponse(log, responseData);
                var resObj = JsonSerializer.Deserialize<OmResponseDto>(responseData);
                if (resObj.success)
                {
                    var meetingStatus = "1007";
                    // 复审状态推送成功
                    //pr.MeetingStatus = meetingStatus;

                    //zhx20240704:修改PrDetail.MeetingStatus
                    UpdatePrDetailMeetingStatusById(prDetail.Id, meetingStatus, true);
                }
                else
                {
                    // 复审状态推送失败
                    //pr.MeetingStatus = "-1007";
                    return MessageResult.FailureResult(resObj.message);
                }
                return resObj.success ? MessageResult.SuccessResult(resObj.data) : MessageResult.FailureResult(resObj.message);
            }
            catch (Exception ex)
            {
                _commonService.LogResponse(log, ex.ToString(), false);
                _logger.LogError(ex, "End to execute OmReviewMeeting, failed to review meeting, pa number:" + paNo + ", request: " + postString + ", error message: " + ex.Message + "!");
                // TODO 记录并重试
                return MessageResult.FailureResult(ex.Message);
            }
        }

        /// <summary>
        /// OM调用，更新NexBpm会议的状态
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> NexBpmUpdateMeetingStatus(NexBpmMeetingStatusUpdateRequestDto request)
        {
            MessageResult validateResult = NexBpmMeetingStatusUpdateRequestDto.Validate(request);
            if (!validateResult.Success)
            {
                _logger.LogWarning("End to execute NexBpmUpdateMeetingStatus, validate message: " + validateResult.Message + "!");
                return validateResult;
            }

            try
            {
                _logger.LogWarning("Start to execute NexBpmUpdateMeetingStatus, request:" + JsonSerializer.Serialize(request) + "!");
                var prRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>();
                var pr = await prRepository.SingleOrDefaultAsync(x => x.ApplicationCode == request.SerialNumberPr && x.IsEsignUsed == true);
                if (pr == null)
                {
                    _logger.LogWarning("End to execute NexBpmUpdateMeetingStatus, no found pr, pr number:" + request.SerialNumberPr + "!");
                    return MessageResult.FailureResult("No found pr, pr number:" + request.SerialNumberPr + "!");
                }
                if (request.Status == "1000" && !string.IsNullOrEmpty(pr.MeetingStatus) && pr.MeetingStatus != "1000")
                {
                    _logger.LogWarning("End to execute NexBpmUpdateMeetingStatus, pr meeting status is not online or is not 1000-推送成功, pr number:" + request.SerialNumberPr + "!");
                    return MessageResult.FailureResult("Pr meeting status is not online or is not 1000-推送成功, pr number:" + request.SerialNumberPr + "!");
                }
                if (request.Status == "1001" && pr.MeetingStatus != "1000")
                {
                    _logger.LogWarning("End to execute NexBpmUpdateMeetingStatus, pr meeting status is not 1000-推送成功, pr number:" + request.SerialNumberPr + "!");
                    return MessageResult.FailureResult("Pr meeting status is not 1000-推送成功, pr number:" + request.SerialNumberPr + "!");
                }
                if (request.Status == "1002" && pr.MeetingStatus != "1000" && pr.MeetingStatus != "1001")
                {
                    _logger.LogWarning("End to execute NexBpmUpdateMeetingStatus, pr meeting status is not 1000-推送成功 or 1001-激活, pr number:" + request.SerialNumberPr + "!");
                    return MessageResult.FailureResult("Pr meeting status is not 1000-推送成功 or 1001-激活, pr number:" + request.SerialNumberPr + "!");
                }
                //接收到OM激活时判断该PR是否处于审批中，若为审批中则不允许激活（1001）
                if (request.Status == "1001" && pr.Status == PurPRApplicationStatus.Approving)
                {
                    _logger.LogWarning("End to execute NexBpmUpdateMeetingStatus, pr  status is approving , can not change pr meeting status to 1001 pr number:" + request.SerialNumberPr + "!");
                    return MessageResult.FailureResult("Pr  status is approving , can not change pr meeting status to 1001 pr number" + request.SerialNumberPr + "!");
                }

                // 更新会议状态，1001-激活，1002-结算
                pr.MeetingStatus = request.Status;

                //zhx20240704:修改PrDetail.MeetingStatus
                await UpdatePrDetailMeetingStatus(pr.Id, request.Status, null, true);

                await prRepository.UpdateAsync(pr, true);

                return MessageResult.SuccessResult("Success to update meeting status to:" + request.Status + ",pr number:" + request.SerialNumberPr + "!");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex,"End to execute NexBpmUpdateMeetingStatus, failed to update meeting status, pr number: " + request.SerialNumberPr + ", error message:" + ex.Message + "!");
                return MessageResult.FailureResult("Failed to update meeting status," + ex.Message + "!");
            }
        }

        /// <summary>
        /// OM调用，更新NexBpm会议内容
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> NexBpmUpdateMeeting(NexBpmMeetingUpdateRequestDto request)
        {
            MessageResult validateResult = NexBpmMeetingUpdateRequestDto.Validate(request);
            if (!validateResult.Success)
            {
                _logger.LogWarning("End to execute NexBpmUpdateMeeting, validate message: " + validateResult.Message + "!");
                return validateResult;
            }

            try
            {
                _logger.LogWarning("Start to execute NexBpmUpdateMeeting, request:" + JsonSerializer.Serialize(request) + "!");
                var prRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>();
                var pr = await prRepository.SingleOrDefaultAsync(x => x.ApplicationCode == request.SerialNumberPr && x.IsEsignUsed == true);
                if (pr == null)
                {
                    _logger.LogWarning("End to execute NexBpmUpdateMeeting, no found pr, pr number:" + request.SerialNumberPr + "!");
                    return MessageResult.FailureResult("No found pr, pr number:" + request.SerialNumberPr + "!");
                }

                //if (pr.MeetingStatus != "1001")
                //{
                //    _logger.LogWarning("End to execute NexBpmUpdateMeeting, pr status is not 1001-激活, pr number:" + request.SerialNumberPr + "!");
                //    return MessageResult.FailureResult("Failed to modify meeting, pr status is not 1001-激活:" + request.SerialNumberPr + "!");
                //}
                DateTime? estimateDate = null;
                if (request.SpeakerDetails.Any())
                {
                    var prDetailRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>();
                    var speakerService = LazyServiceProvider.LazyGetService<ISpeakerService>();
                    var prDetails = await prDetailRepository.GetListAsync(x => x.PRApplicationId == pr.Id);

                    //判断入参 request.SpeakerDetails里所有VendorCode是否在Original讲者 或 备选讲者里
                    var (vndCodeRight, vndInfo) = IsAllVendorCodeRight(request.SpeakerDetails, prDetails, pr.CompanyCode);
                    if (!vndCodeRight)
                    {
                        _logger.LogWarning($"End to execute NexBpmUpdateMeeting, there are some invalid vendor codes:{JsonSerializer.Serialize(request)}");
                        return MessageResult.FailureResult("Cannot be replaced without a main speaker or alternative speaker");
                    }

                    foreach (var speakerDetail in request.SpeakerDetails)
                    {
                        if (int.TryParse(speakerDetail.No, out int rowNo))
                        {
                            var prDetail = prDetails.SingleOrDefault(x => x.RowNo == rowNo);
                            if (prDetail != null)
                            {
                                var tmpVndInfo = vndInfo.GetValue(speakerDetail.VendorCode);
                                var getSpeakerResponse = await speakerService.GetSpeakerDetailByCodeAsync(tmpVndInfo?[4]);
                                if (getSpeakerResponse == null || !getSpeakerResponse.Success)
                                {
                                    _logger.LogWarning("End to execute NexBpmUpdateMeeting, no found speaker, invalid vendor code:" + speakerDetail.VendorCode + ", row number:" + speakerDetail.No + ", pr number:" + request.SerialNumberPr + "!");
                                    return MessageResult.FailureResult("Invalid vendor code:" + speakerDetail.VendorCode + ", row number:" + speakerDetail.No + ", pr number:" + request.SerialNumberPr + "!"); ;
                                }

                                var speaker = getSpeakerResponse.Data as SpeakerDetailResponseDto;
                                estimateDate = DateTime.Parse(request.MeetingDate);
                                prDetail.VendorCode = speakerDetail.VendorCode;
                                prDetail.ExecutorName = speakerDetail.Executive;
                                prDetail.ExecutorEmail = speakerDetail?.ExecutiveMail?.Trim();
                                prDetail.Hospital = tmpVndInfo?[5] == "1" ? tmpVndInfo?[6] : speaker.HospitalName;
                                prDetail.HosDepartment = speaker.HosDepartment;
                                prDetail.HcpLevelName = tmpVndInfo?[5] == "1" ? tmpVndInfo?[7] : speaker.SPLevelName;
                                //zhx20240703添加：
                                if (Guid.TryParse(tmpVndInfo?[0], out Guid tmpBpcsAvmId))
                                {
                                    prDetail.VendorId = tmpBpcsAvmId;
                                }
                                prDetail.VendorCode = tmpVndInfo?[1];
                                prDetail.VendorName = tmpVndInfo?[2];
                                prDetail.CardNo = tmpVndInfo?[3];
                                prDetail.CertificateCode = speaker.CertificateCode;
                                prDetail.StandardDepartmentId = speaker.StandardHosDepId;
                                prDetail.StandardDepartment = speaker.StandardHosDepName;

                                //pr总金额先减去老的金额
                                pr.TotalAmount -= prDetail.TotalAmount ?? 0;
                                pr.TotalAmountRMB -= prDetail.TotalAmountRMB ?? 0;
                                //变更为备选时
                                if (tmpVndInfo?[5] == "1")
                                {
                                    //1901【采购申请】OM：修改为备选讲者结算（备选金额<主讲金额）后，该PR单的单价和总金额未按照备选讲者的金额进行调整，未返还预算
                                    //有备选讲者的情况
                                    if (!string.IsNullOrEmpty(prDetail.BackUpVendors))
                                    {
                                        var backupVendors = Newtonsoft.Json.JsonConvert.DeserializeObject<IEnumerable<CreateUpdatePRApplicationBackupVendor>>(prDetail.BackUpVendors);
                                        var backupVendor = backupVendors.FirstOrDefault(a => a.VendorId == tmpBpcsAvmId);
                                        if (backupVendor != null)
                                        {
                                            prDetail.UnitPrice = backupVendor.Price;
                                            prDetail.TotalAmount = prDetail.Quantity * backupVendor.Price;
                                            prDetail.TotalAmountRMB = prDetail.Quantity * backupVendor.Price * (decimal)pr.Rate;
                                        }
                                    }
                                }
                                else
                                {
                                    prDetail.TotalAmount = prDetail.OriginalTotalAmount;
                                    prDetail.TotalAmountRMB = prDetail.OriginalTotalAmountRMB;
                                    prDetail.UnitPrice = prDetail.OriginalTotalAmount / prDetail.Quantity;
                                }

                                //再加上本次的金额
                                pr.TotalAmount += prDetail.TotalAmount ?? 0;
                                pr.TotalAmountRMB += prDetail.TotalAmountRMB ?? 0;

                            }
                        }
                    }
                    prDetails.ForEach(a => {
                        if (estimateDate.HasValue)
                            a.EstimateDate = estimateDate;
                    });
                    // 更新pr申请详情数据
                    await prDetailRepository.UpdateManyAsync(prDetails, true);
                }
                if (estimateDate.HasValue)
                    pr.MeetingDate = estimateDate;

                pr.AcitveHostAddress = request.MeetingAddress;
                pr.MeetingDate = DateTime.Parse(request.MeetingDate);
                if (request.MeetingType == "线上")
                    pr.MeetingType = "Online";
                else if (request.MeetingType == "线下")
                    pr.MeetingType = "Offline";
                else
                    pr.MeetingType = "OnlineAndOffline";//线上加线下

                // 更新pr申请主表数据
                await prRepository.UpdateAsync(pr, true);

                return MessageResult.SuccessResult("Success to update meeting, pr number:" + request.SerialNumberPr + "!");
            }
            catch (Exception ex)
            {
                _logger.LogError("End to execute NexBpmUpdateMeeting, failed to update meeting, pr number:" + request.SerialNumberPr + ", error message:" + ex.Message + "!");
                return MessageResult.FailureResult("Failed to update meeting," + ex.Message + "!");
            }
        }

        /// <summary>
        /// OM调用，作废NexBpm会议
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> NexBpmRevokeMeeting(NexBpmMeetingRevokeRequestDto request)
        {
            if (request == null)
                return MessageResult.FailureResult("Argument null!");

            if (string.IsNullOrWhiteSpace(request.SerialNumberPr))
                return MessageResult.FailureResult("Empty pr number!");

            try
            {
                _logger.LogWarning("Start to execute NexBpmRevokeMeeting, request:" + JsonSerializer.Serialize(request) + "!");
                var prRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>();
                var pr = await prRepository.SingleOrDefaultAsync(x => x.ApplicationCode == request.SerialNumberPr && x.IsEsignUsed == true);
                if (pr == null)
                {
                    _logger.LogError("End to execute NexBpmRevokeMeeting, no found pr, pr number:" + request.SerialNumberPr + "!");
                    return MessageResult.FailureResult("No found pr, pr number:" + request.SerialNumberPr + "!");
                }

                //var prDetailRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>();
                //var existingPushedPrDetail = await prDetailRepository.AnyAsync(x => x.PRApplicationId == pr.Id && x.PushFlag == PushFlagEnum.Pushed || x.PRApplicationId == pr.Id && x.IsVendorConfimed == true);
                //if (existingPushedPrDetail)
                //{
                //    _logger.LogWarning("End to execute NexBpmRevokeMeeting, there is pushed pr detail, pr number:" + request.SerialNumberPr + "!");
                //    return MessageResult.FailureResult("\"Failed to revoke meeting, there is pushed pr detail, pr number:" + request.SerialNumberPr + "!");
                //}

                //var prDetails = await prDetailRepository.GetListAsync(a => a.PRApplicationId == pr.Id);
                //var useBudgetRequest = new ReturnBudgetRequestDto
                //{
                //    PrId = pr.Id,
                //    SubbudgetId = pr.SubBudgetId.Value,
                //    Items = prDetails.Select(a => new ReturnInfo
                //    {
                //        PdRowNo = a.RowNo,
                //        ReturnAmount = a.TotalAmountRMB ?? 0,
                //        ReturnSourceId = pr.Id,
                //        ReturnSourceCode = pr.ApplicationCode
                //    })
                //};
                //var returnSubBudgetResult = await LazyServiceProvider.LazyGetService<ISubbudgetService>().ReturnSubbudgetAsync(useBudgetRequest);
                //if (!returnSubBudgetResult.Success)
                //{
                //    _logger.LogWarning("End to execute NexBpmRevokeMeeting, failed to revoke meeting, failed to return sub budget, " + returnSubBudgetResult.Message + ", pr number: " + request.SerialNumberPr + "!");
                //    return MessageResult.FailureResult("Failed to revoke meeting, failed to return sub budget, " + returnSubBudgetResult.Message + ", pr number: " + request.SerialNumberPr + "!");
                //}

                //// NexBpm会议作废逻辑，加回预算
                //pr.Status = PurPRApplicationStatus.ApplicantTerminate;

                //zhx20240704:修改PrDetail.MeetingStatus
                await UpdatePrDetailMeetingStatus(pr.Id, pr.MeetingStatus, null, true);

                // Om作废
                pr.MeetingStatus = OnlineMeetingStatus.OmDeprecated;
                await prRepository.UpdateAsync(pr, true);

                //退预算和psa
                var result = await LazyServiceProvider.LazyGetService<IApproveService>().UpdatePRApplicationStatus([pr.Id], ApprovalOperation.Delete, false);
                if (!result.Success)
                {
                    await CurrentUnitOfWork.RollbackAsync();
                    return result;
                }
                //zhx20240702:类似我方作废时，添加作废操作记录（审批人直接取CurrentUser，就是OM调该接口时对应的用户）
                AddApprovalRecord(pr.Id, ApprovalOperation.Delete, "EPD Online Meeting作废会议", "会议系统作废会议", "作废");
                return MessageResult.SuccessResult("Success to revoke meeting, pr number:" + request.SerialNumberPr + "!");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex,"End to execute NexBpmRevokeMeeting, failed to revoke meeting, pr number:" + request.SerialNumberPr + "error message: " + ex.Message + "!");
                return MessageResult.FailureResult("Failed to revoke meeting," + ex.Message + "!");
            }
        }


        /// <summary>
        /// OM调用，结算NexBpm会议
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> NexBpmSettleMeeting(NexBpmMeetingSettlementRequestDto request)
        {
            MessageResult validateResult = NexBpmMeetingSettlementRequestDto.Validate(request);
            if (!validateResult.Success)
            {
                _logger.LogWarning("End to execute NexBpmSettleMeeting, validate message: " + validateResult.Message + "!");
                return validateResult;
            }

            try
            {
                _logger.LogWarning("Start to execute NexBpmSettleMeeting, request:" + JsonSerializer.Serialize(request) + "!");
                var prRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>();
                //var pr = await prRepository.SingleOrDefaultAsync(x => x.ApplicationCode == request.SerialNumber && x.IsEsignUsed == true);
                var pr = await GetPRApplicationAsync(prRepository, request.SerialNumber);
                if (pr == null)
                {
                    _logger.LogWarning("End to execute NexBpmSettleMeeting, no found pr, pr number:" + request.SerialNumber + "!");
                    return MessageResult.FailureResult("No found pr, pr number:" + request.SerialNumber + "!");
                }
                //如果有多个PR明细行，会一行一行的计算
                //等所有的都结算完之后再推送更新状态为1002
                //所以这里的条件不能用1002，而是用1001和1002
                if (pr.MeetingStatus != "1001" && pr.MeetingStatus != "1002")
                {
                    _logger.LogWarning("End to execute NexBpmSettleMeeting, pr meeting status is not 1001-激活 or 1002-结算, pr number:" + request.SerialNumber + "!");
                    return MessageResult.FailureResult("Failed to settle meeting, pr meeting status is not 1001-激活 or 1002-结算:" + request.SerialNumber + "!");
                }

                var prDetailRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>();
                var prDetails = await prDetailRepository.GetListAsync(x => x.PRApplicationId == pr.Id);
                var prDetail = prDetails.SingleOrDefault(x => x.PRApplicationId == pr.Id && x.RowNo == int.Parse(request.No));
                if (prDetail == null)
                {
                    _logger.LogWarning("End to execute NexBpmSettleMeeting, no found row number:" + request.No + ", pr number:" + request.SerialNumber + "!");
                    return MessageResult.FailureResult("No found row number:" + request.No + ", pr number:" + request.SerialNumber + "!");
                }

                #region 参照OM变更会议时：判断VendorCode+字段赋值

                //判断入参 request.SpeakerDetails里所有VendorCode是否在Original讲者 或 备选讲者里
                var (vndCodeRight, vndInfo) = IsAllVendorCodeRight(
                    new List<NexBpmMeetingSpeakerItemDto> { new NexBpmMeetingSpeakerItemDto { No = request.No, VendorCode = request.VendorCode } }
                    , new List<PurPRApplicationDetail> { prDetail }, pr.CompanyCode);
                if (!vndCodeRight)
                {
                    _logger.LogWarning($"End to execute NexBpmSettleMeeting, there are some invalid vendor codes，request:{JsonSerializer.Serialize(request)}");
                    return MessageResult.FailureResult($"There are some invalid vendor codes,No:{JsonSerializer.Serialize(request.No)},VendorCode:{JsonSerializer.Serialize(request.VendorCode)}");
                }

                var tmpVndInfo = vndInfo.GetValue(request.VendorCode);
                var speakerService = LazyServiceProvider.LazyGetService<ISpeakerService>();
                var getSpeakerResponse = await speakerService.GetSpeakerDetailByCodeAsync(tmpVndInfo?[4]);
                if (getSpeakerResponse == null || !getSpeakerResponse.Success)
                {
                    _logger.LogWarning($"End to execute NexBpmSettleMeeting, no found speaker, request:{JsonSerializer.Serialize(request)}");
                    return MessageResult.FailureResult("Invalid vendor code:" + request.VendorCode + ", row number:" + request.No + ", pr number:" + request.SerialNumber + "!"); ;
                }
                #endregion 参照OM变更会议时：判断VendorCode+字段赋值

                // 更新总价格，单价
                if (decimal.TryParse(request.PayAmount, out decimal payAmount)
                    && prDetail.TotalAmount.HasValue
                    && prDetail.TotalAmountRMB.HasValue
                    && prDetail.UnitPrice.HasValue)
                {
                    if (tmpVndInfo == null || tmpVndInfo[5] == "0")
                    {
                        //实际金额>PR申请金额:报错，
                        //zhx20240709:以下几个判断，都用prDetail.TotalAmountRMB
                        if (payAmount > prDetail.OriginalTotalAmountRMB)
                        {
                            _logger.LogWarning("End to execute NexBpmSettleMeeting, payAmount greater than applied amount, row number:" + request.No + ", pr number:" + request.SerialNumber + "!");
                            return MessageResult.FailureResult("payAmount greater than applied amount, row number:" + request.No + ", pr number:" + request.SerialNumber + "!");
                        }
                    }
                    else
                    {
                        //实际金额>PR备选讲者金额：报错
                        if (payAmount > decimal.Parse(tmpVndInfo[8]))
                        {
                            _logger.LogWarning("End to execute NexBpmSettleMeeting, payAmount greater than applied amount, row number:" + request.No + ", pr number:" + request.SerialNumber + "!");
                            return MessageResult.FailureResult("payAmount greater than applied amount, row number:" + request.No + ", pr number:" + request.SerialNumber + "!");
                        }
                    }
                }

                DateTime estimateDate = DateTime.Parse(request.StartDate);
                var meetingSettlementRepository = LazyServiceProvider.LazyGetService<IInteOnlineMeetingRepository>();
                var meetingSettlement = new InteOnlineMeetingSettlement();
                meetingSettlement.PRApplicationId = prDetail.PRApplicationId;
                meetingSettlement.PRDetailId = prDetail.Id;
                meetingSettlement.SerialNumber = request.SerialNumber;
                meetingSettlement.No = int.Parse(request.No);
                meetingSettlement.VendorCode = request.VendorCode;
                meetingSettlement.PdfUrl = request.PdfUrl;
                meetingSettlement.PayAmount = decimal.Parse(request.PayAmount);
                meetingSettlement.ModifyRemark = request.ModifyRemark;
                meetingSettlement.ModifyAmountRemark = request.ModifyAmountRemark;
                meetingSettlement.Executive = request.Executive;
                meetingSettlement.ExecutiveMail = request.ExecutiveMail?.Trim();
                meetingSettlement.StartDate = DateTime.Parse(request.StartDate);
                meetingSettlement.ActualNumber = request.ActualNumber;

                await meetingSettlementRepository.InsertAsync(meetingSettlement);

                prDetails.ForEach(a =>
                {
                    if (a.Id == prDetail.Id)
                    {
                        a.ExecutorName = request.Executive;
                        a.ExecutorEmail = request.ExecutiveMail?.Trim();

                        //5066 会议结算时，也更新vendor及金额
                        var speaker = getSpeakerResponse.Data as SpeakerDetailResponseDto;
                        if (Guid.TryParse(tmpVndInfo?[0], out Guid tmpBpcsAvmId))
                            prDetail.VendorId = tmpBpcsAvmId;
                        prDetail.VendorCode = tmpVndInfo?[1];
                        prDetail.VendorName = tmpVndInfo?[2];

                        //pr总金额先减去老的金额
                        pr.TotalAmount -= prDetail.TotalAmount ?? 0;
                        pr.TotalAmountRMB -= prDetail.TotalAmountRMB ?? 0;
                        //变更为备选时
                        if (tmpVndInfo?[5] == "1")
                        {
                            //1901【采购申请】OM：修改为备选讲者结算（备选金额<主讲金额）后，该PR单的单价和总金额未按照备选讲者的金额进行调整，未返还预算
                            //有备选讲者的情况
                            if (!string.IsNullOrEmpty(prDetail.BackUpVendors))
                            {
                                var backupVendors = Newtonsoft.Json.JsonConvert.DeserializeObject<IEnumerable<CreateUpdatePRApplicationBackupVendor>>(prDetail.BackUpVendors);
                                var backupVendor = backupVendors.FirstOrDefault(a => a.VendorId == tmpBpcsAvmId);
                                if (backupVendor != null)
                                {
                                    prDetail.UnitPrice = backupVendor.Price;
                                    prDetail.TotalAmount = prDetail.Quantity * backupVendor.Price;
                                    prDetail.TotalAmountRMB = prDetail.Quantity * backupVendor.Price * (decimal)pr.Rate;
                                }
                            }
                        }
                        else
                        {
                            prDetail.TotalAmount = prDetail.OriginalTotalAmount;
                            prDetail.TotalAmountRMB = prDetail.OriginalTotalAmountRMB;
                            prDetail.UnitPrice = prDetail.OriginalTotalAmount / prDetail.Quantity;
                        }

                        //再加上本次的金额
                        pr.TotalAmount += prDetail.TotalAmount ?? 0;
                        pr.TotalAmountRMB += prDetail.TotalAmountRMB ?? 0;

                    }
                    a.EstimateDate = estimateDate;
                });
                pr.MeetingDate = estimateDate;
                pr.DoctorsNum = request.ActualNumber;
                await prRepository.UpdateAsync(pr);
                await prDetailRepository.UpdateManyAsync(prDetails);

                return MessageResult.SuccessResult("Succes to settle meeting, pr number:" + request.SerialNumber + ",row number:" + request.No + "!");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "End to execute NexBpmSettleMeeting, failed to settle meeting, pr number:" + request.SerialNumber + ", row number:" + request.No + ",error message:" + ex.Message + "!");
                return MessageResult.FailureResult("Failed to settle meeting," + ex.Message + "!");
            }
        }

        /// <summary>
        /// 重试获取PR
        /// </summary>
        /// <param name="prRepository"></param>
        /// <param name="serialNumber"></param>
        /// <returns></returns>
        private async Task<PurPRApplication> GetPRApplicationAsync(IPurPRApplicationRepository prRepository, string serialNumber)
        {
            var retryPolicy = Policy
                .Handle<SqlException>(ex => ex.Number == 1205) // 死锁错误号
                .Or<TimeoutException>()
                .Or<Exception>()
                .WaitAndRetryAsync(
                    retryCount: 3,
                    sleepDurationProvider: attempt => TimeSpan.FromSeconds(Math.Pow(2, attempt)),//这里设置了重试次数为 3 次，并且每次重试之间的等待时间是指数级增加  3次等待8秒
                    onRetry: (ex, timeSpan, retryCount, context) =>
                    {
                        // 记录重试尝试
                        _logger.LogError(ex,$"第 {retryCount} 次重试，等待 {timeSpan.TotalSeconds} 秒后重试，单号：{serialNumber} ，原因：{ex.Message}");
                    });

            return await retryPolicy.ExecuteAsync(() => prRepository.SingleOrDefaultAsync(x => x.ApplicationCode == serialNumber && x.IsEsignUsed == true));
        }
        /// <summary>
        /// OM调用，结算NexBpm会议  (原方法弃用，可做参考)
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> NexBpmSettleMeetingBack(NexBpmMeetingSettlementRequestDto request)
        {
            MessageResult validateResult = NexBpmMeetingSettlementRequestDto.Validate(request);
            if (!validateResult.Success)
            {
                _logger.LogWarning("End to execute NexBpmSettleMeeting, validate message: " + validateResult.Message + "!");
                return validateResult;
            }

            try
            {
                _logger.LogWarning("Start to execute NexBpmSettleMeeting, request:" + JsonSerializer.Serialize(request) + "!");
                var prRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>();
                var pr = await prRepository.SingleOrDefaultAsync(x => x.ApplicationCode == request.SerialNumber && x.IsEsignUsed == true);
                if (pr == null)
                {
                    _logger.LogWarning("End to execute NexBpmSettleMeeting, no found pr, pr number:" + request.SerialNumber + "!");
                    return MessageResult.FailureResult("No found pr, pr number:" + request.SerialNumber + "!");
                }
                //如果有多个PR明细行，会一行一行的计算
                //等所有的都结算完之后再推送更新状态为1002
                //所以这里的条件不能用1002，而是用1001和1002
                if (pr.MeetingStatus != "1001" && pr.MeetingStatus != "1002")
                {
                    _logger.LogWarning("End to execute NexBpmSettleMeeting, pr meeting status is not 1001-激活 or 1002-结算, pr number:" + request.SerialNumber + "!");
                    return MessageResult.FailureResult("Failed to settle meeting, pr meeting status is not 1001-激活 or 1002-结算:" + request.SerialNumber + "!");
                }

                var prDetailRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>();
                var prDetails = await prDetailRepository.GetListAsync(x => x.PRApplicationId == pr.Id);
                var prDetail = prDetails.SingleOrDefault(x => x.PRApplicationId == pr.Id && x.RowNo == int.Parse(request.No));
                if (prDetail == null)
                {
                    _logger.LogWarning("End to execute NexBpmSettleMeeting, no found row number:" + request.No + ", pr number:" + request.SerialNumber + "!");
                    return MessageResult.FailureResult("No found row number:" + request.No + ", pr number:" + request.SerialNumber + "!");
                }

                #region 参照OM变更会议时：判断VendorCode+字段赋值

                //判断入参 request.SpeakerDetails里所有VendorCode是否在Original讲者 或 备选讲者里
                var (vndCodeRight, vndInfo) = IsAllVendorCodeRight(
                    new List<NexBpmMeetingSpeakerItemDto> { new NexBpmMeetingSpeakerItemDto { No = request.No, VendorCode = request.VendorCode } }
                    , new List<PurPRApplicationDetail> { prDetail }, pr.CompanyCode);
                if (!vndCodeRight)
                {
                    _logger.LogWarning($"End to execute NexBpmSettleMeeting, there are some invalid vendor codes，request:{JsonSerializer.Serialize(request)}");
                    return MessageResult.FailureResult($"There are some invalid vendor codes,No:{JsonSerializer.Serialize(request.No)},VendorCode:{JsonSerializer.Serialize(request.VendorCode)}");
                }

                var tmpVndInfo = vndInfo.GetValue(request.VendorCode);
                var speakerService = LazyServiceProvider.LazyGetService<ISpeakerService>();
                var getSpeakerResponse = await speakerService.GetSpeakerDetailByCodeAsync(tmpVndInfo?[4]);
                if (getSpeakerResponse == null || !getSpeakerResponse.Success)
                {
                    _logger.LogWarning($"End to execute NexBpmSettleMeeting, no found speaker, request:{JsonSerializer.Serialize(request)}");
                    return MessageResult.FailureResult("Invalid vendor code:" + request.VendorCode + ", row number:" + request.No + ", pr number:" + request.SerialNumber + "!"); ;
                }

                var speaker = getSpeakerResponse.Data as SpeakerDetailResponseDto;
                prDetail.Hospital = tmpVndInfo?[5] == "1" ? tmpVndInfo?[6] : speaker.HospitalName;
                prDetail.HosDepartment = speaker.HosDepartment;
                prDetail.HcpLevelName = tmpVndInfo?[5] == "1" ? tmpVndInfo?[7] : speaker.SPLevelName;
                //zhx20240703添加：
                if (Guid.TryParse(tmpVndInfo?[0], out Guid tmpBpcsAvmId))
                {
                    prDetail.VendorId = tmpBpcsAvmId;
                }
                prDetail.VendorCode = tmpVndInfo?[1];
                prDetail.VendorName = tmpVndInfo?[2];
                prDetail.CardNo = tmpVndInfo?[3];
                prDetail.CertificateCode = speaker.CertificateCode;
                prDetail.StandardDepartmentId = speaker.StandardHosDepId;
                prDetail.StandardDepartment = speaker.StandardHosDepName;

                #endregion 参照OM变更会议时：判断VendorCode+字段赋值

                // NexBpm结算逻辑
                //prDetail.VendorCode = request.VendorCode;//已从tmpVndInfo里取
                prDetail.ExecutorName = request.Executive;
                prDetail.ExecutorEmail = request?.ExecutiveMail?.Trim();
                if (DateTime.TryParse(request.StartDate, out DateTime startDate))
                {
                    prDetail.EstimateDate = startDate;
                }
                // 更新总价格，单价
                if (decimal.TryParse(request.PayAmount, out decimal payAmount)
                    && prDetail.TotalAmount.HasValue
                    && prDetail.TotalAmountRMB.HasValue
                    && prDetail.UnitPrice.HasValue)
                {
                    //prDetail.TotalAmount = payAmount;
                    //if (prDetail.Quantity != null)
                    //{
                    //    prDetail.UnitPrice = payAmount / prDetail.Quantity;
                    //}

                    //实际金额>PR申请金额:报错，
                    //zhx20240709:以下几个判断，都用prDetail.TotalAmountRMB
                    if (payAmount > prDetail.TotalAmountRMB)
                    {
                        _logger.LogWarning("End to execute NexBpmSettleMeeting, payAmount greater than TotalAmount, row number:" + request.No + ", pr number:" + request.SerialNumber + "!");
                        return MessageResult.FailureResult("payAmount greater than TotalAmount, row number:" + request.No + ", pr number:" + request.SerialNumber + "!");
                    }
                    // 实际金额少于PR申请金额，但大于0
                    else if (payAmount > 0 && payAmount < prDetail.TotalAmountRMB)
                    {
                        //pr总金额先减去老的金额
                        pr.TotalAmount -= prDetail.TotalAmount ?? 0;
                        pr.TotalAmountRMB -= prDetail.TotalAmountRMB ?? 0;

                        //实际大于0且小于申请金额时，不用去做返还预算了，只要更新图中的金额和单价字段就行了。
                        //zhx20240709:按实际金额整理以下几个金额和单价字段
                        prDetail.TotalAmountRMB = payAmount;//覆盖PrDetail.TotalAmountRMB
                        prDetail.UnitPrice = prDetail.TotalAmountRMB / prDetail.Quantity;//再算单价
                        prDetail.TotalAmount = prDetail.TotalAmountRMB / (decimal)pr.Rate;//用汇率重新算TotalAmount

                        //再加上本次的金额
                        pr.TotalAmount += prDetail.TotalAmount ?? 0;
                        pr.TotalAmountRMB += prDetail.TotalAmountRMB ?? 0;

                        //var returnInfo = new ReturnInfo
                        //{
                        //    PdRowNo = prDetail.RowNo,
                        //    ReturnAmount = prDetail.TotalAmountRMB.Value - payAmount,
                        //    ReturnSourceId = pr.Id,
                        //    ReturnSourceCode = pr.ApplicationCode
                        //};

                        //var useBudgetRequest = new ReturnBudgetRequestDto
                        //{
                        //    PrId = pr.Id,
                        //    SubbudgetId = pr.SubBudgetId.Value,
                        //    Items = new List<ReturnInfo>()
                        //    {
                        //        returnInfo
                        //    }
                        //};
                        //MessageResult returnSubbugetResult = await LazyServiceProvider.LazyGetService<ISubbudgetService>().ReturnSubbudgetAsync(useBudgetRequest);
                        //if (!returnSubbugetResult.Success)
                        //{
                        //    _logger.LogWarning("End to execute NexBpmSettleMeeting, failed to return sub budget, pr number:" + request.SerialNumber + ", row number:" + request.No + ", reason:" + returnSubbugetResult.Message + "!");
                        //    return MessageResult.FailureResult("Failed to return sub budget, pr number:" + request.SerialNumber + ", row number:" + request.No + ", reason:" + returnSubbugetResult.Message + "!");
                        //}
                    }

                    // 实际金额为0，PR申请金额不为0
                    else if (payAmount == 0 && prDetail.TotalAmountRMB != 0)
                    {
                        // 反冲pr detail行，所有行重新提交预算使用
                        PurPRApplicationDetail hedgePrDetail = JsonSerializer.Deserialize<PurPRApplicationDetail>(JsonSerializer.Serialize(prDetail));
                        hedgePrDetail.SetId(Guid.Empty);
                        hedgePrDetail.HedgePrDetailId = prDetail.Id;
                        hedgePrDetail.RowNo = GetCurMaxRowNo(prDetail.PRApplicationId, prDetailRepository, 1);
                        hedgePrDetail.TotalAmount *= -1;
                        hedgePrDetail.TotalAmountRMB *= -1;
                        hedgePrDetail.UnitPrice *= -1;

                        ////获取子预算信息
                        //var subBudget = await LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>().FirstOrDefaultAsync(a => a.Id == pr.SubBudgetId);
                        //if (subBudget == null)
                        //{
                        //    _logger.LogWarning("End to execute NexBpmSettleMeeting, no found sub budget, sub budget id:" + pr.SubBudgetId + "!");
                        //    return MessageResult.FailureResult($"未找到Id为{pr.SubBudgetId}的子预算数据");
                        //}

                        //检查子预算是否足够
                        var useInfos = prDetails.Select(a => new UseInfo
                        {
                            PdRowNo = a.RowNo,
                            UseAmount = a.TotalAmountRMB ?? 0
                        }).ToList();
                        //添加反冲行的金额，一起Check和Use预算
                        useInfos.Add(new UseInfo
                        {
                            PdRowNo = hedgePrDetail.RowNo,
                            UseAmount = hedgePrDetail.TotalAmountRMB ?? 0
                        });

                        var useBudgetRequest = new UseBudgetRequestDto
                        {
                            PrId = pr.Id,
                            SubbudgetId = pr.SubBudgetId.Value,
                            Items = useInfos
                        };

                        var subBudgetService = LazyServiceProvider.LazyGetService<ISubbudgetService>();
                        //var validateSubBudgetEnoughResult = await subBudgetService.CheckSubbudgetAmountSufficientAsync(useBudgetRequest);
                        //if (!validateSubBudgetEnoughResult.Success)
                        //{
                        //    _logger.LogWarning("End to execute NexBpmSettleMeeting, sub budget is not enough, sub budget id:" + pr.SubBudgetId + "!");
                        //    return MessageResult.FailureResult("Sub budget is not enough, sub budget id:" + pr.SubBudgetId + "!");
                        //}

                        var validateSubBudgetUseResult = await subBudgetService.UseSubbudgetAsync(useBudgetRequest);
                        if (!validateSubBudgetUseResult.Success)
                        {
                            _logger.LogWarning("End to execute NexBpmSettleMeeting, sub budget is not enough, sub budget id:" + pr.SubBudgetId + "!");
                            return MessageResult.FailureResult("Sub budget is not enough, sub budget id:" + pr.SubBudgetId + "!");
                        }

                        //2366【采购申请】OM：结算金额<申请金额时，未更新列表的申请总金额(SettleMeeting接口)
                        //pr总净额加上对冲的金额（负数）
                        pr.TotalAmount += hedgePrDetail.TotalAmount ?? 0;
                        pr.TotalAmountRMB += hedgePrDetail.TotalAmountRMB ?? 0;

                        // 插入反冲pr detail行
                        await prDetailRepository.InsertAsync(hedgePrDetail);
                    }

                    // 实际金额为0，PR申请金额为0
                    else if (payAmount == 0 && prDetail.TotalAmountRMB == 0)
                    {
                        // nothing
                    }
                }

                //2366【采购申请】OM：结算金额<申请金额时，未更新列表的申请总金额(SettleMeeting接口)
                await prRepository.UpdateAsync(pr);
                await prDetailRepository.UpdateAsync(prDetail);

                var meetingSettlementRepository = LazyServiceProvider.LazyGetService<IInteOnlineMeetingRepository>();
                var meetingSettlement = new InteOnlineMeetingSettlement();
                meetingSettlement.PRApplicationId = prDetail.PRApplicationId;
                meetingSettlement.PRDetailId = prDetail.Id;
                meetingSettlement.SerialNumber = request.SerialNumber;
                meetingSettlement.No = int.Parse(request.No);
                meetingSettlement.VendorCode = request.VendorCode;
                meetingSettlement.PdfUrl = request.PdfUrl;
                meetingSettlement.PayAmount = decimal.Parse(request.PayAmount);
                meetingSettlement.ModifyRemark = request.ModifyRemark;
                meetingSettlement.ModifyAmountRemark = request.ModifyAmountRemark;
                meetingSettlement.Executive = request.Executive;
                meetingSettlement.ExecutiveMail = request.ExecutiveMail;
                meetingSettlement.StartDate = DateTime.Parse(request.StartDate);
                meetingSettlement.ActualNumber = request.ActualNumber;

                await meetingSettlementRepository.InsertAsync(meetingSettlement);

                return MessageResult.SuccessResult("Succes to settle meeting, pr number:" + request.SerialNumber + ",row number:" + request.No + "!");
            }
            catch (Exception ex)
            {
                _logger.LogError("End to execute NexBpmSettleMeeting, failed to settle meeting, pr number:" + request.SerialNumber + ", row number:" + request.No + ",error message:" + ex.Message + "!");
                return MessageResult.FailureResult("Failed to settle meeting," + ex.Message + "!");
            }
        }

        private List<PurPRApplicationDetail> FiltrateCostNature(List<PurPRApplicationDetail> prDetails)
        {
            if (prDetails?.Any() != true)
            {
                return prDetails;
            }

            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var costNatureList = dataverseService.GetCostNatureAsync().GetAwaiterResult().ToList();
            var costNaturePushIds = costNatureList.Where(a => a.PushOnlineMeeting == true).Select(a => a.Id);
            return prDetails.Where(a => a.CostNature.HasValue && costNaturePushIds.Contains(a.CostNature.Value)).ToList();
        }

        /// <summary>
        /// 过滤反冲行和被反冲行
        /// </summary>
        /// <param name="prDetails"></param>
        /// <returns></returns>
        private List<PurPRApplicationDetail> FilterHedge(List<PurPRApplicationDetail> prDetails)
        {
            //过滤被反冲行
            var prDetailIDs = prDetails.Select(a => a.Id).ToList();
            var prDetailRepsitory = LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>();
            var hedgeedList = prDetailRepsitory.GetListAsync(x => prDetailIDs.Contains(x.HedgePrDetailId.Value)).GetAwaiterResult();
            if (hedgeedList.Count > 0)
            {
                var hedgeedIDs = hedgeedList.Select(a => a.HedgePrDetailId).ToList();
                prDetails = prDetails.Where(a => !hedgeedIDs.Contains(a.Id)).ToList();
            }

            //过滤反冲行
            prDetails = prDetails.Where(a => a.HedgePrDetailId == null).ToList();
            return prDetails;
        }

        private async Task UpdatePrDetailMeetingStatus(Guid prId, string meetingStatus, List<int> prNoList = null, bool onlyMeetingStatusHasValue = false)
        {
            var repoPrDetail = LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>();
            var queryPrDetail = repoPrDetail.GetQueryableAsync().GetAwaiterResult();
            var updateEntities = queryPrDetail.Where(a => a.PRApplicationId == prId)
                .WhereIf(prNoList?.Any() == true, a => prNoList.Contains(a.RowNo))
                .WhereIf(onlyMeetingStatusHasValue, a => !string.IsNullOrWhiteSpace(a.MeetingStatus)).ToList();
            if (updateEntities.Count > 0)
            {
                updateEntities.ForEach(a =>
                {
                    a.MeetingStatus = meetingStatus;
                });
                //repoPrDetail.UpdateManyAsync(updateEntities, true).GetAwaiterResult();
                await repoPrDetail.UpdateManyAsync(updateEntities, true); // 使用 await 替代 GetAwaiterResult()
            }
        }

        /// <summary>
        /// 适用于1005-1007, 直接根据 PrDetail.Id 修改 PrDetail.MeetingStatus
        /// </summary>
        private void UpdatePrDetailMeetingStatusById(Guid prDetailId, string meetingStatus, bool onlyMeetingStatusHasValue = false)
        {
            var repoPrDetail = LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>();
            var queryPrDetail = repoPrDetail.GetQueryableAsync().GetAwaiterResult();
            var updateEntity = queryPrDetail.Where(a => a.Id == prDetailId)
                .WhereIf(onlyMeetingStatusHasValue, a => !string.IsNullOrWhiteSpace(a.MeetingStatus)).FirstOrDefault();
            if (updateEntity != null)
            {
                updateEntity.MeetingStatus = meetingStatus;
                repoPrDetail.UpdateAsync(updateEntity).GetAwaiterResult();
            }
        }

        private int GetCurMaxRowNo(Guid pRApplicationId, IPurPRApplicationDetailRepository prDetailRepository, int increment = 0)
        {
            return prDetailRepository.GetQueryableAsync().GetAwaiterResult()
                .Where(a => a.PRApplicationId == pRApplicationId).Max(a => a.RowNo)
                + increment;
        }

        private void AddApprovalRecord(Guid id, ApprovalOperation delete, string remark, string workStep, string name)
        {
            var approvalRecord = new AddApprovalRecordDto
            {
                FormId = id,
                ApprovalId = CurrentUser.Id.HasValue ? CurrentUser.Id.Value : Guid.Empty,
                Status = delete,
                Remark = remark,
                ApprovalTime = DateTime.Now,
                WorkStep = workStep,
                Name = name,
            };

            var taskRepository = _serviceProvider.GetService<IWorkflowTaskRepository>();
            var taskEntity = ObjectMapper.Map<AddApprovalRecordDto, WorkflowTask>(approvalRecord);
            taskRepository.InsertAsync(taskEntity).GetAwaiterResult();
        }

        /// <summary>
        /// Determines whether [is all vendor code right] [the specified speaker details].
        /// </summary>
        /// <param name="speakerDetails">The speaker details.</param>
        /// <param name="prDetails">The pr details.</param>
        /// <returns>
        /// bool: speakerDetails每一项里的VendorCode是否是对应PrDetail的OriginalVendorId 或 备选讲者，true:全是，false:不全是；
        /// Dictionary(string, string[]): Key: speakerDetails每一项的VendorCode（BpcsAvm.Vendor）,
        /// string[]按索引依次为如下4个值：
        /// 0：BpcsAvm.Id, 1：BpcsAvm.Vendor, 2：BpcsPMFVM.Vextnm, 3：BpcsAvm.Vmxcrt，4：Vendors.VendorCode，
        /// 5：0-变更为主讲者、1-变更后为备选讲者
        /// 6：备选讲者Json里的 Hospital
        /// 7：备选讲都Json里的 HcpLevelName
        /// </returns>
        private (bool, Dictionary<string, string[]>) IsAllVendorCodeRight(List<NexBpmMeetingSpeakerItemDto> speakerDetails, List<PurPRApplicationDetail> prDetails, string prCompanyCode)
        {
            if (speakerDetails?.Any() != true || prDetails?.Any() != true)
            {
                return (false, null);
            }

            //1，根据所有 speakerDetail.VendorCode + prCompanyCode，查出所有 BpcsAvm+BpcsPMFVM
            var queryBpcsAvm = LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync().GetAwaiterResult();
            var queryBpcsPmfvm = LazyServiceProvider.LazyGetService<IBpcsPmfvmRepository>().GetQueryableAsync().GetAwaiterResult();
            var bpcsAvmPmfvm = queryBpcsAvm.Where(a => a.Vcmpny == decimal.Parse(prCompanyCode)
                    && speakerDetails.Select(s => decimal.Parse(s.VendorCode)).Contains(a.Vendor))
                .Join(queryBpcsPmfvm, l => new { Vnd = l.Vendor, Cmp = l.Vcmpny }, r => new { Vnd = r.Vnderx, Cmp = r.Vmcmpy },
                    (pbcsAvm, pbcsPmfvm) => new { bpcsAvmId = pbcsAvm.Id, pbcsAvm, pbcsPmfvm })
                .ToList();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var specialvendors =  dataverseService.GetSpecialvendorAsync().Result;//特殊供应商

            var resultData = new Dictionary<string, string[]>();
            var finaIds = new List<Guid>();
            //2，循环判断每个 speakerDetail.VendorCode 的 BpcsAvm.Id 是否在对应的 PrDetail 的Original或Backup里
            //如有某一条不在，则直接返回false
            foreach (var speakerDetail in speakerDetails)
            {
                if (int.TryParse(speakerDetail.No, out int rowNo))
                {
                    var prDetail = prDetails.SingleOrDefault(x => x.RowNo == rowNo);
                    if (prDetail == null)
                    {
                        return (false, null);
                    }

                    var curBpcsAvm = bpcsAvmPmfvm.FirstOrDefault(a =>
                        a.pbcsAvm.Vendor == decimal.Parse(speakerDetail.VendorCode)
                        && a.pbcsAvm.Vcmpny == decimal.Parse(prCompanyCode));
                    var specialvendor = specialvendors.FirstOrDefault(a=>a.VendorCode == speakerDetail.VendorCode && a.CompanyCode == prCompanyCode);//特殊供应商
                    var curBpcsAvmId = curBpcsAvm?.bpcsAvmId;
                    if (!curBpcsAvmId.HasValue)
                    {
                        return (false, null);
                    }
                    var backupVendors = string.IsNullOrEmpty(prDetail.BackUpVendors) ? null : JsonSerializer.Deserialize<List<CreateUpdatePRApplicationBackupVendor>>(prDetail.BackUpVendors);
                    if (prDetail.OriginalVendorId != curBpcsAvmId
                        && (backupVendors == null || !backupVendors.Select(a => a.VendorId).Contains(curBpcsAvmId.Value)))
                    {
                        return (false, null);
                    }

                    //以下2个变量 为了区分变更后的是主讲者还是备选讲者
                    var isOriginal = prDetail.OriginalVendorId == curBpcsAvmId;
                    var matchedBackupVnd = isOriginal ? null : backupVendors?.FirstOrDefault(a => a.VendorId == curBpcsAvmId.Value);

                    resultData.Add(speakerDetail.VendorCode,
                        new string[] {
                                curBpcsAvm.bpcsAvmId.ToString(),
                                curBpcsAvm.pbcsAvm.Vendor.ToString(),
                                specialvendor != null ? specialvendor.Name : curBpcsAvm.pbcsPmfvm.Vextnm,
                                curBpcsAvm.pbcsAvm.Vmxcrt,
                                curBpcsAvm.pbcsAvm.FinaId?.ToString(),//先用FinaId占位，后面替换成对应的Vendor.VendorCode
                                isOriginal ? "0":"1",
                                matchedBackupVnd?.Hospital,
                                matchedBackupVnd?.HcpLevelName,
                                (((matchedBackupVnd?.Price)??0M)*(prDetail.Quantity??0L)).ToString(),
                        });

                    //3，在循环判断处理第2点时，同时记录所有 BpcsAvm.FinalId，
                    finaIds.AddIf(curBpcsAvm.pbcsAvm.FinaId.HasValue, curBpcsAvm.pbcsAvm.FinaId.Value);
                }
                else
                {
                    return (false, null);
                }
            }

            //4，在第2点完成后，用第3点记录的所有 BpcsAvm.FinalId，查出对应的 Vendor.VendorCode，并添加到Result里
            if (finaIds.Any())
            {
                var queryVendor = LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync().GetAwaiterResult();
                var queryVendorFinal = LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync().GetAwaiterResult();
                var dicVndCodes = queryVendor.Join(queryVendorFinal.Where(a => finaIds.Contains(a.Id)), l => l.Id, r => r.VendorId,
                    (l, r) => new { l.VendorCode, r.Id })
                    .ToDictionary(a => a.Id, a => a.VendorCode);
                foreach (var item in resultData)
                {
                    if (item.Value[4] == null)
                    {
                        continue;
                    }
                    item.Value[4] = dicVndCodes.GetValueOrDefault(Guid.Parse(item.Value[4]));
                }
            }
            return (true, resultData);
        }

        /// <summary>
        /// 判断单个VendorCode是否在主讲者和备选讲者里，
        /// 参照IsAllVendorCodeRight，但只包含VenderCode的判断，不返回其它信息
        /// </summary>
        /// <param name="vendorCode">The speaker VendorCode.</param>
        /// <param name="prDetail">The prDetails.</param>
        /// <returns>
        /// bool: 入参VendorCode是否是对应PrDetail的OriginalVendorId 或 备选讲者，true:是，false:否；
        /// string：0-变更为主讲者、1-变更后为备选讲者
        /// </returns>
        private (bool, string) IsVendorCodeRight(string vendorCode, PurPRApplicationDetail prDetail, string prCompanyCode)
        {
            if (string.IsNullOrWhiteSpace(vendorCode) || prDetail == null)
            {
                return (false, null);
            }

            //1，根据所有 vendorCode + prCompanyCode，查出一条 BpcsAvm+BpcsPMFVM
            var queryBpcsAvm = LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync().GetAwaiterResult();
            var queryBpcsPmfvm = LazyServiceProvider.LazyGetService<IBpcsPmfvmRepository>().GetQueryableAsync().GetAwaiterResult();
            var bpcsAvmPmfvm = queryBpcsAvm.Where(a => a.Vcmpny == decimal.Parse(prCompanyCode)
                    && decimal.Parse(vendorCode) == a.Vendor)
                .Join(queryBpcsPmfvm, l => new { Vnd = l.Vendor, Cmp = l.Vcmpny }, r => new { Vnd = r.Vnderx, Cmp = r.Vmcmpy },
                    (pbcsAvm, pbcsPmfvm) => new { bpcsAvmId = pbcsAvm.Id, pbcsAvm, pbcsPmfvm })
                .FirstOrDefault();
            if (bpcsAvmPmfvm == null)
            {
                return (false, null);
            }

            var resultData = new Dictionary<string, string[]>();
            var finaIds = new List<Guid>();
            //2，判断 vendorCode 的 BpcsAvm.Id 是否在 prDetail 的Original或Backup里
            var curBpcsAvmId = bpcsAvmPmfvm.bpcsAvmId;

            var backupVendors = JsonSerializer.Deserialize<List<CreateUpdatePRApplicationBackupVendor>>(prDetail.BackUpVendors);
            if (prDetail.OriginalVendorId != curBpcsAvmId
                && (backupVendors == null || !backupVendors.Select(a => a.VendorId).Contains(curBpcsAvmId)))
            {
                return (false, null);
            }

            //以下2个变量 为了区分变更后的是主讲者还是备选讲者
            var isOriginal = prDetail.OriginalVendorId == curBpcsAvmId;
            var matchedBackupVnd = isOriginal ? null : backupVendors?.FirstOrDefault(a => a.VendorId == curBpcsAvmId);

            return (true, isOriginal ? "0" : "1");
        }

        private async Task<OmMeetingAddRequestDto> BuildOmMeetingAddRequest(PurPRApplication pr)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var identityUserRepository = LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>();

            OmMeetingAddRequestDto meetingAddRequest = new OmMeetingAddRequestDto();
            meetingAddRequest.Name = pr.MeetingTitle?.Trim().ReplaceLineEndings();
            meetingAddRequest.SerialNumberPr = pr.ApplicationCode;
            if (pr.MeetingType?.ToLower() == "Online".ToLower())
            {
                meetingAddRequest.Type = "线上";
            }
            else if (pr.MeetingType?.ToLower() == "Offline".ToLower())
            {
                meetingAddRequest.Type = "线下";
            }
            else
            {
                meetingAddRequest.Type = "线上加线下";
            }

            //活动举行城市
            meetingAddRequest.City = pr.ActiveHostCity?.Trim().ReplaceLineEndings();

            meetingAddRequest.Address = pr.AcitveHostAddress?.Trim().ReplaceLineEndings();
            meetingAddRequest.Host = pr.HostVendorIdName;
            meetingAddRequest.HostEmail = pr.HostVendorEmail;
            meetingAddRequest.ApplicantName = pr.ApplyUserIdName;
            // 申请人
            var applyUser = await identityUserRepository.SingleOrDefaultAsync(x => x.Id == pr.ApplyUserId);
            meetingAddRequest.ApplicantEmail = applyUser?.Email;
            meetingAddRequest.ApplicationDate = pr.ApplyTime?.ToString("yyyy-MM-dd");
            meetingAddRequest.ApplicationCompany = pr.CompanyIdName;
            meetingAddRequest.Principal = pr.AgentIdName;
            // 被代理人
            if (pr.AgentId != null)
            {
                var agentUser = await identityUserRepository.SingleOrDefaultAsync(x => x.Id == pr.AgentId);
                meetingAddRequest.PrincipalEmail = agentUser?.Email;
            }

            meetingAddRequest.ProductName = pr.ProductIdsName;

            return meetingAddRequest;
        }

        private async Task<Tuple<List<OmMeetingMainSpeakerItemDto>, List<OmMeetingStandbySpeakerItemDto>>> BuindSpeakers(List<PurPRApplicationDetail> prDetails)
        {
            if (prDetails == null)
            {
                return null;
            }

            var mainSpeakers = new List<OmMeetingMainSpeakerItemDto>();
            var standbySpeakers = new List<OmMeetingStandbySpeakerItemDto>();
            var dataverseService = LazyServiceProvider.GetService<IDataverseService>();
            foreach (var prDetail in prDetails)
            {
                // 只推送付款方式为AR
                if (prDetail.PayMethod != PayMethods.AR)
                {
                    continue;
                }

                if (!prDetail.VendorId.HasValue)
                {
                    continue;
                }
                OmMeetingMainSpeakerItemDto mainSpeaker = await BuildOmMeetingMainSpeaker(prDetail);
                if (mainSpeaker == null)
                {
                    continue;
                }

                mainSpeakers.Add(mainSpeaker);

                List<OmMeetingStandbySpeakerItemDto> standbySpeakerList = await BuildOmMeetingStandbySpeakers(prDetail);
                if (standbySpeakerList == null)
                {
                    continue;
                }

                standbySpeakers.AddRange(standbySpeakerList);
            }

            return new Tuple<List<OmMeetingMainSpeakerItemDto>, List<OmMeetingStandbySpeakerItemDto>>(mainSpeakers, standbySpeakers);
        }

        private async Task<OmMeetingMainSpeakerItemDto> BuildOmMeetingMainSpeaker(PurPRApplicationDetail prDetail)
        {
            // 只推送付款方式为AR
            if (prDetail.PayMethod != PayMethods.AR)
            {
                return null;
            }

            if (!prDetail.VendorId.HasValue)
            {
                return null;
            }

            var speaker = await GetSpeaker(prDetail.VendorId.Value);
            //// 只推送有EPD医生主键的讲者
            //if (string.IsNullOrWhiteSpace(speaker?.EpdId))
            //{
            //    return null;
            //}

            //zhx20240708:第一次推OM新建会议时，还要判断主讲者的供应商类型为讲者(Vendor.VendorType=="HCP-个人")
            if (speaker.VendorType != VendorTypes.HCPPerson)
            {
                return null;
            }
            OmMeetingMainSpeakerItemDto mainSpeakerItem = new OmMeetingMainSpeakerItemDto();
            mainSpeakerItem.PlanDate = prDetail.EstimateDate?.ToString("yyyy-MM-dd");
            mainSpeakerItem.No = prDetail.RowNo;
            mainSpeakerItem.MeetingContent = prDetail.Content?.Trim().ReplaceLineEndings();
            mainSpeakerItem.Executor = prDetail.ExecutorName;
            mainSpeakerItem.ExecutorEmail = prDetail.ExecutorEmail;
            mainSpeakerItem.ServiceTime = prDetail.ServiceDuration?.ToString();
            mainSpeakerItem.SlideName = prDetail.SlideType == "Category-A" ? prDetail.SlideAName : prDetail.SlideName;
            mainSpeakerItem.SlideType = prDetail.SlideType;
            mainSpeakerItem.SpeakerLevel = prDetail.HcpLevelName;
            mainSpeakerItem.VendorName = prDetail.VendorName;
            mainSpeakerItem.VendorCode = prDetail.VendorCode;
            mainSpeakerItem.RmbAmount = prDetail.TotalAmountRMB?.ToString();

            mainSpeakerItem.TaxMobile = speaker.HandPhone;
            mainSpeakerItem.IdNumber = speaker.CardNo;
            mainSpeakerItem.CertificateNo = speaker.CertificateCode;
            mainSpeakerItem.BankNumber = speaker.BankCardNo;
            mainSpeakerItem.HospitalName = speaker.HospitalName;
            mainSpeakerItem.DepartmentName = speaker.HosDepartment;
            mainSpeakerItem.ProfessionalTitle = speaker.PTIName;
            mainSpeakerItem.AbbottHcpId = speaker.EpdId;
            mainSpeakerItem.NextBpmHcpId = speaker.VendorCode;

            return mainSpeakerItem;
        }

        private async Task<List<OmMeetingStandbySpeakerItemDto>> BuildOmMeetingStandbySpeakers(PurPRApplicationDetail prDetail)
        {
            // 只推送付款方式为AR
            if (prDetail.PayMethod != PayMethods.AR)
            {
                return null;
            }

            if (!prDetail.VendorId.HasValue)
            {
                return null;
            }

            //推送新建会议时，备选讲者也用Backup表的TotalAmountRMB推过去
            var prDetailBackups = LazyServiceProvider.LazyGetService<IPurPRApplicationDetailBackupVendorRepository>()
                .GetListAsync(a => a.PRApplicationDetailId == prDetail.Id).GetAwaiterResult();
            if (prDetailBackups?.Any() != true)
            {
                return null;
            }
            //从BpcsAvm+BpcsPmfvm表取Vendor，以便后续取相关值
            var queryBpcsAvm = LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync().GetAwaiterResult();
            var queryBpcsPmfvm = LazyServiceProvider.LazyGetService<IBpcsPmfvmRepository>().GetQueryableAsync().GetAwaiterResult();
            var dicBpcsAvmPmfvm = queryBpcsAvm.Where(a => prDetailBackups.Select(b => b.VendorId).Contains(a.Id))
                .Join(queryBpcsPmfvm, l => new { Vnd = l.Vendor, Cmp = l.Vcmpny }, r => new { Vnd = r.Vnderx, Cmp = r.Vmcmpy },
                    (pbcsAvm, pbcsPmfvm) => new { bpcsAvmId = pbcsAvm.Id, bpcsAvmVendor = pbcsAvm.Vendor, pbcsPmfvmVextnm = pbcsPmfvm.Vextnm, Vmcmpy = pbcsPmfvm.Vmcmpy })
                .ToDictionary(a => a.bpcsAvmId, a => a);

            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var specialvendors = await dataverseService.GetSpecialvendorAsync();//特殊供应商

            if (dicBpcsAvmPmfvm?.Any() != true)
            {
                _logger.LogWarning($"BuildOmMeetingStandbySpeakers() return null, dicBpcsAvmPmfvm is null,prDetailId:{prDetail.Id}");
                return null;
            }

            var standbySpeakers = new List<OmMeetingStandbySpeakerItemDto>();
            foreach (var backupVendor in prDetailBackups)
            {
                var speaker = await GetSpeaker(backupVendor.VendorId);
                //// 只推送有EPD医生主键的讲者
                //if (string.IsNullOrWhiteSpace(speaker?.EpdId))
                //{
                //    continue;
                //}
                var curBpcsAvmPmfvm = dicBpcsAvmPmfvm.GetValueOrDefault(backupVendor.VendorId);

                var vendorName = curBpcsAvmPmfvm?.pbcsPmfvmVextnm;
                if (curBpcsAvmPmfvm != null) 
                {
                    var specialvendor = specialvendors.FirstOrDefault(a => a.VendorCode == curBpcsAvmPmfvm.bpcsAvmVendor.ToString() && a.CompanyCode == curBpcsAvmPmfvm.Vmcmpy.ToString());//特殊供应商
                    if (!string.IsNullOrWhiteSpace(specialvendor?.Name))
                        vendorName = specialvendor.Name;
                }

                OmMeetingStandbySpeakerItemDto backupSpeakerItem = new OmMeetingStandbySpeakerItemDto();
                backupSpeakerItem.No = prDetail.RowNo;
                backupSpeakerItem.SpeakerLevel = backupVendor.HcpLevelName;
                backupSpeakerItem.VendorName = vendorName;
                backupSpeakerItem.VendorCode = curBpcsAvmPmfvm?.bpcsAvmVendor.ToString();
                backupSpeakerItem.TaxMobile = speaker.HandPhone;
                backupSpeakerItem.IdNumber = speaker.CardNo;
                backupSpeakerItem.CertificateNo = speaker.CertificateCode;
                backupSpeakerItem.BankNumber = speaker.BankCardNo;
                backupSpeakerItem.HospitalName = speaker.HospitalName;
                backupSpeakerItem.DepartmentName = speaker.HosDepartment;
                backupSpeakerItem.ProfessionalTitle = speaker.PTIName;
                backupSpeakerItem.AbbottHcpId = speaker.EpdId;
                backupSpeakerItem.NextBpmHcpId = speaker.VendorCode;
                backupSpeakerItem.RmbAmount = backupVendor.TotalAmountRMB.ToString();

                standbySpeakers.Add(backupSpeakerItem);
            }

            return standbySpeakers;
        }

        private async Task<SpeakerDetailResponseDto> GetSpeaker(Guid vendorId)
        {
            var bpcsAvmRepository = LazyServiceProvider.LazyGetService<IBpcsAvmRepository>();
            var bpcsAvm = await bpcsAvmRepository.SingleOrDefaultAsync(x => x.Id == vendorId);
            if (bpcsAvm == null || bpcsAvm.FinaId == null)
            {
                return null;
            }

            var vendorFinancialRepository = LazyServiceProvider.LazyGetService<IVendorFinancialRepository>();
            var vendorFinancial = await vendorFinancialRepository.SingleOrDefaultAsync(x => x.Id == bpcsAvm.FinaId.Value);
            if (vendorFinancial == null)
            {
                return null;
            }

            var speakerService = LazyServiceProvider.LazyGetService<ISpeakerService>();
            var getSpeakerResponse = await speakerService.GetSpeakerDetailAsync(vendorFinancial.VendorId);

            if (getSpeakerResponse == null || !getSpeakerResponse.Success)
            {
                return null;
            }
            var speaker = getSpeakerResponse.Data as SpeakerDetailResponseDto;
            //zhx20240703:speaker里的银行卡号、身份证号、手机号、要从bpcsAvm+BpcsPMFVM表里查出来替换
            if (speaker != null)
            {
                var bpcsPmfvmRepository = LazyServiceProvider.LazyGetService<IBpcsPmfvmRepository>();
                var bpcsPmfvm = await bpcsPmfvmRepository.FirstOrDefaultAsync(x => x.Vnderx == bpcsAvm.Vendor && x.Vmcmpy == bpcsAvm.Vcmpny);

                //银行卡号
                speaker.BankCardNo = bpcsPmfvm.Vldrm2;
                //身份证号
                speaker.CardNo = bpcsAvm.Vmxcrt;
                //手机号(zhx20240705:手机号，BPCS里面会有很多都是空的，所以还是按vendors来)
                //speaker.HandPhone = bpcsAvm.Vphone;
            }
            return speaker;
        }
    }
}