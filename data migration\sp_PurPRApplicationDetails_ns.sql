CREATE PROCEDURE dbo.sp_PurPRApplicationDetails_ns
AS 
BEGIN 
	/*
	1.a.TermCode AVT表还未迁移 
	2.a.TermCodeDays AVT表还未迁移
	3.BiddingId 如何匹配
 */
select 
a.Id,
UPPER(b.Id) PRApplicationId,
a.PayMethod,
a.EstimateDate,
UPPER(cc.Id) as VendorId,
UPPER(d.spk_NexBPMCode) as CostNature,
UPPER(e.spk_NexBPMCode)  as CityId,
a.Content,
a.Quantity,
a.Unit,
a.UnitPrice,
a.TotalAmount,
a.TaxRate,
a.TaxAmount,
UPPER(f.spk_NexBPMCode) as ProductId,
a.VendorType,
a.AcademyName,
a.AcademyJob,
UPPER(di1.spk_code) as SlideType,
UPPER(di2.spk_code) as SlideName,
a.ServiceDuration,
a.<PERSON>Up<PERSON>end<PERSON>,
a.Executor,
a<PERSON><PERSON><PERSON>,
a.<PERSON>,
a.ExtraProperties,
a.ConcurrencyStamp,
UPPER(ss.spk_NexBPMCode)  as CreatorId,
ppt.CreatorId as CreationTime,
a.LastModificationTime,
a.LastModifierId,
a.IsDeleted,
a.DeleterId,
a.DeletionTime,
a.PushFlag,
UPPER(ss.spk_NexBPMCode) as PurchaserId,
a.PushTime,
a.RollbackReason,
a.IsOffline,
a.OfflineNo,
a.VendorName,
a.RowNo,
a.IsPrLate,
a.HedgePrDetailId,
a.CardNo,
a.CertificateCode,
a.CityIdName,
UPPER(sc.spk_NexBPMCode) as CostCenter,
a.CostCenterCode,
a.CostCenterName,
UPPER(d1.spk_NexBPMCode) as CostNatureCode,
a.CostNatureName,
a.ExecutorEmail,
a.ExecutorName,
a.HcpLevelName,
a.HosDepartment,
a.Hospital,
a.IsVendorConfimed,
a.OriginalEstimateDate,
a.OriginalVendorCode,
UPPER(cc1.Id) as OriginalVendorId,
a.OriginalVendorName,
a.SlideTypeName,
a.StandardDepartment,
a.StandardDepartmentId,
a.TermCode,
a.TermCodeDays,
a.VendorCode,
a.VendorTypeName,
UPPER(di3.spk_code) as HcpLevelCode,
a.OrderStatusFlag,
a.BiddingId,
a.ExceptionNumber
into #PurPRApplicationDetails
from PLATFORM_ABBOTT_STg.dbo.PurPRApplicationDetails_tmp a --1362220
left join PLATFORM_ABBOTT_STg.dbo.PurPRApplications_tmp b 
on a.ProcInstId =b.ProcInstId 
left join PLATFORM_ABBOTT_STg.dbo.ods_BPCS_PMFVM c
on a.VendorId = concat(c.[VNDERX],c.[VEXTNM],c.[VMCMPY])
left join PLATFORM_ABBOTT_STg.dbo.ODS_BPCS_AVM  cc
on c.[VMCMPY]=cc.[VCMPNY] AND c.[VNDERX]=cc.[VENDOR]
left join PLATFORM_ABBOTT_STg.dbo.spk_costnature d
on a.CostNature = d.spk_BPMCode 
left join PLATFORM_ABBOTT_STg.dbo.spk_citymasterdata e
on SUBSTRING(a.CityId, PATINDEX('%[^0]%', a.CityId), LEN(a.CityId))  =concat(SUBSTRING(e.spk_citynumber, PATINDEX('%[^0]%', e.spk_citynumber), LEN(e.spk_citynumber)),e.spk_Name)
left join PLATFORM_ABBOTT_STg.dbo.spk_productmasterdata f
on cast(a.ProductId as nvarchar(50)) = cast(f.spk_BPMCode as nvarchar(50)) 
left join PLATFORM_ABBOTT_STg.dbo.spk_dictionary di1
on a.SlideType  COLLATE SQL_Latin1_General_CP1_CI_AS =di1.spk_BPMCode and di1.spk_type=N'幻灯片类型'
left join (select *,ROW_NUMBER () over(PARTITION by spk_name order by spk_code desc ) rn from PLATFORM_ABBOTT_STg.dbo.spk_dictionary) di2
on a.SlideName  COLLATE SQL_Latin1_General_CP1_CI_AS =di2.spk_Name and di2.spk_type=N'幻灯片名称' and di2.rn=1
left join PLATFORM_ABBOTT_STg.dbo.PurPRApplications_tmp ppt 
on a.ProcInstId =ppt.ProcInstId 
left join PLATFORM_ABBOTT_STg.dbo.spk_staffmasterdata ss 
on ppt.CreationTime =ss.bpm_id 
left join PLATFORM_ABBOTT_STg.dbo.spk_staffmasterdata ss1 
on a.PurchaserId =ss1.bpm_id 
left join PLATFORM_ABBOTT_STg.dbo.spk_costcentermasterdata sc
on a.CostCenter = sc.spk_BPMCode 
left join PLATFORM_ABBOTT_STg.dbo.spk_costnature d1
on a.CostNatureCode = d1.spk_BPMCode --923430640
left join PLATFORM_ABBOTT_STg.dbo.ods_BPCS_PMFVM c1
on a.OriginalVendorId = concat(c1.[VNDERX],c1.[VEXTNM],c1.[VMCMPY])
left join PLATFORM_ABBOTT_STg.dbo.ODS_BPCS_AVM  cc1
on c1.[VMCMPY]=cc1.[VCMPNY] AND c1.[VNDERX]=cc1.[VENDOR]
left join PLATFORM_ABBOTT_STg.dbo.spk_dictionary di3
on a.HcpLevelCode  COLLATE SQL_Latin1_General_CP1_CI_AS =di3.spk_Name and di3.spk_type=N'HCP级别'


IF OBJECT_ID(N'PLATFORM_ABBOTT_stg.dbo.PurPRApplicationDetails', N'U') IS NOT NULL
BEGIN
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
    drop table PLATFORM_ABBOTT_stg.dbo.PurPRApplicationDetails
    select *
    into PLATFORM_ABBOTT_stg.dbo.PurPRApplicationDetails from #PurPRApplicationDetails
	END
	ELSE
	BEGIN
    --落成实体表
    select *
    into PLATFORM_ABBOTT_stg.dbo.PurPRApplicationDetails from #PurPRApplicationDetails
    -- select * from #InteOnlineMeetingSettlement_tmp
	  PRINT(N' 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END

END