apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: speaker-ingress
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - speaker-portal-api-s.oneabbott.com
    secretName: tls-speaker-portal-api-s-secret
  rules:
  - http:
      paths:
      - path: /(.*)
        pathType: Prefix
        backend:
          service:
            name: corp-191-speakerapi-stg
            port:
              number: 80