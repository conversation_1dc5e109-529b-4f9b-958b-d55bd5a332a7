CREATE PROCEDURE dbo.sp_VendorApplicationPersonals_ns
AS 
BEGIN

with cityInfo as (
	SELECT 
		b.spk_name ,
		b.spk_provincecode ,
		a.spk_citycode ,
		a.spk_name as spk_name1,
		LEFT(a.spk_name,2) spk_name2,
		LEFT(a.spk_name,3) spk_name3,
		LEFT(a.spk_name,4) spk_name4,
		Len(a.spk_name) spk_nameSize,
		b.spk_provincialadministrativecode as province_code,
		a.spk_cityadministrativedivisioncode as city_code
	from   
		PLATFORM_ABBOTT.dbo.spk_city a,
		PLATFORM_ABBOTT.dbo.spk_province b
	where  a.spk_provincenamename = b.spk_name
)
select
vapt.[Id]
,vapt.[ApplicationId]
,vapt.[Sex]
,vapt.[CardType]
,vapt.[CardNo]
,vapt.[CardPic]
,COALESCE (cast (COALESCE(city2.province_code, city3.province_code, city4.province_code, city5.province_code) as nvarchar(255)),'000000') as [Province]
,COALESCE (cast (COALESCE(city2.city_code, city3.city_code, city4.city_code, city5.city_code) as nvarchar(255)),'000000') as [City]
,vapt.[Address]
,vapt.[PostCode]
,vapt.[ExtraProperties]
,vapt.[ConcurrencyStamp]
,vapt.[CreationTime]
,ss.spk_NexBPMCode  as [CreatorId]
,vapt.[LastModificationTime]
,vapt.[LastModifierId]
,vapt.[IsDeleted]
,vapt.[DeleterId]
,vapt.[DeletionTime]
,vapt.[SPName]
,vapt.[AffiliationOrgan]
,vapt.[Email]
into #VendorApplicationPersonals
from VendorApplicationPersonals_Tmp vapt 
left join spk_staffmasterdata ss 
on vapt.CreatorId COLLATE SQL_Latin1_General_CP1_CI_AS =ss.bpm_id 
left join cityInfo as city2
    on vapt.city = city2.spk_name1
left join cityInfo as city3
    on vapt.city = city3.spk_name2
left join cityInfo as city4
    on vapt.city = city4.spk_name3
left join cityInfo as city5
    on vapt.city = city5.spk_name4
;

--drop table  #VendorApplicationPersonals


-- 中文市 & 省 替换为 code ,代码 start
--with cityInfo as (
--	SELECT 
--		b.spk_name ,
--		b.spk_provincialadministrativecode ,
--		a.spk_cityadministrativedivisioncode ,
--		a.spk_name as spk_name1,
--		LEFT(a.spk_name,2) spk_name2,
--		LEFT(a.spk_name,3) spk_name3,
--		LEFT(a.spk_name,4) spk_name4,
--		Len(a.spk_name) spk_nameSize
--	from   
--		PLATFORM_ABBOTT.dbo.spk_city a,
--		PLATFORM_ABBOTT.dbo.spk_province b
--	where  a.spk_provincenamename = b.spk_name
--)
--update p 
--SET 
--	p.Province = nullif(city1.spk_provincialadministrativecode,city2.spk_provincialadministrativecode) ,
--	p.city =nullif(city1.spk_cityadministrativedivisioncode,city2.spk_cityadministrativedivisioncode) 
--
--from #VendorApplicationPersonals p
--left join
-- 	cityInfo as city1
-- 	on city1.spk_name1=p.city or city1.spk_name3=p.city  -- 优先三个字去匹配城市或原始字段去,避免2个字的城市名重复
--left join
-- 	cityInfo as city2
-- 	on city2.spk_name2=LEFT(p.city,2)  and city1.spk_name3 is null  -- 三个字城市名字无法匹配则用2个名字城市去匹配
--where p.city is not null 
--and  p.city not like '%张家港%' -- TODO  张家 导致数据异常 
--;
-- 中文市 & 省 替换为 code ,代码 end


-- ->>>>>>>>>>>>>>>>>>>>>>>>>>>
-- 中文证件号汉字替换为 code ,代码 start
update p 
SET 
	p.CardType = a.spk_code
from #VendorApplicationPersonals p
left join spk_dictionary a
  on TRIM(p.CardType) = TRIM(a.spk_Name) and  a.spk_type=N'证件类型'
where p.CardType is not null
;
-- 中文证件号汉字替换为 code  ,代码 end





-- ->>>>>>>>>>>>>>>>>>>>>>>>>>>
--  在导出时候统一做了的，此处不需要
-- 中文 汉字 男&女 替换为 code ,代码 start
--update #VendorApplicationPersonals
--SET 
--	 Sex = case when TRIM(sex) ='男' then '1' when TRIM(sex) ='女' then '2' else sex end 
--where TRIM(sex) in('男','女')
--;
-- 中文 汉字 男&女 替换为 code ,代码 end



WITH SplitAtta AS (                                                                     
    SELECT                                                                              
        A.id AS A_id, -- 假设A表有id字段                                                      
        TRIM(value) AS AttachmentInformation                                            
    FROM #VendorApplicationPersonals a                                                          
    CROSS APPLY STRING_SPLIT(A.CardPic, ',')                              
),                                                                                      
File_id as (                                                                            
	SELECT                                                                                 
	    A_id,                                                                              
	    B.id as B_id,                                                                      
	    B.BPMId                                                                            
	FROM                                                                                   
	    SplitAtta                                                                          
	JOIN dbo.Attachments_tmp B                                         
	ON SplitAtta.AttachmentInformation COLLATE SQL_Latin1_General_CP1_CI_As= B.BPMId       
)                                                                                       
select A_id,                                                                            
STRING_AGG(cast(B_id as nvarchar(1000)), ',') WITHIN GROUP (ORDER BY B_id) AS B_id      
into #AttachmentInformation        
from File_id                                                                            
group by A_id                                                                           
                                                                                        
update a set a.CardPic=b.B_id from #VendorApplicationPersonals a
left join #AttachmentInformation b                                                           
on a.id=b.A_id                                                                          
PRINT(N'update AttachmentInformation'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff')) 
                                                                                        
 IF OBJECT_ID(N'dbo.VendorApplicationPersonals ', N'U') IS NOT NULL         
	BEGIN                                                                                  
		drop table dbo.VendorApplicationPersonals                                 
		select *                                                                              
        into dbo.VendorApplicationPersonals from #VendorApplicationPersonals        
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))                     
	END                                                                                    
	ELSE                                                                                   
	BEGIN                                                                                  
    --落成实体表                                                                             
    select *                                                                            
        into dbo.VendorApplicationPersonals from #VendorApplicationPersonals        
    -- select * from #vendor_tbl                                                        
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))              
END                                                                                     
END
