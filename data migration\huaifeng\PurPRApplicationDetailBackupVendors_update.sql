select 
TRY_CONVERT(UNIQUEIDENTIFIER,Id) Id
,TRY_CONVERT(UNIQUEIDENTIFIER, IIF([PRApplicationId] IS NULL , '00000000-0000-0000-0000-000000000000',PRApplicationId)) PRApplicationId
,TRY_CONVERT(UNIQUEIDENTIFIER, IIF([PRApplicationDetailId] IS NULL , '00000000-0000-0000-0000-000000000000',PRApplicationDetailId)) PRApplicationDetailId
,TRY_CONVERT(UNIQUEIDENTIFIER, IIF([VendorId] IS NULL , '00000000-0000-0000-0000-000000000000',VendorId)) VendorId
,VendorName
,PaymentTerm
,HcpLevelName
,HcpLevelCode
,Hospital
,isnull(Price,0)  Price
,Text
,ExtraProperties
,ConcurrencyStamp
,isnull(CreationTime,'1900-01-01') CreationTime
,TRY_CONVERT(UNIQUEIDENTIFIER,CreatorId) CreatorId
,GETDATE()  LastModificationTime
,TRY_CONVERT(UNIQUEIDENTIFIER, IIF(CAST([LastModifierId] AS nvarchar) = '' OR CAST([LastModifierId] AS nvarchar) IS NULL , '00000000-0000-0000-0000-000000000000', CAST([LastModifierId] AS nvarchar))) LastModifierId
,IsDeleted
,TRY_CONVERT(UNIQUEIDENTIFIER, IIF(CAST([DeleterId] AS nvarchar) = '' OR CAST([DeleterId] AS nvarchar) IS NULL , '00000000-0000-0000-0000-000000000000', CAST([DeleterId] AS nvarchar))) DeleterId
,'1900-01-01' DeletionTime
,ExceptionNumber
into #PurPRApplicationDetailBackupVendors
from (SELECT DISTINCT *,ROW_NUMBER() OVER(PARTITION BY Id ORDER BY Id) RK FROM PLATFORM_ABBOTT_Stg.dbo.PurPRApplicationDetailBackupVendors) a 
where RK = 1

--drop table #PurPRApplicationDetailBackupVendors;
use Speaker_Portal_STG;

update a                   
set a.PRApplicationId       = b.PRApplicationId
   ,a.PRApplicationDetailId = b.PRApplicationDetailId  
   ,a.VendorId              = b.VendorId
   ,a.VendorName            = b.VendorName
   ,a.PaymentTerm           = b.PaymentTerm
   ,a.HcpLevelName          = b.HcpLevelName
   ,a.HcpLevelCode          = b.HcpLevelCode
   ,a.Hospital              = b.Hospital
   ,a.Price                 = b.Price
   ,a.Text                  = b.Text
   ,a.ExtraProperties       = b.ExtraProperties
   ,a.ConcurrencyStamp      = b.ConcurrencyStamp
   ,a.CreationTime          = b.CreationTime
   ,a.CreatorId             = b.CreatorId
   ,a.LastModificationTime  = b.LastModificationTime
   ,a.LastModifierId        = b.LastModifierId
   ,a.IsDeleted             = b.IsDeleted
   ,a.DeleterId             = b.DeleterId
   ,a.DeletionTime          = b.DeletionTime
   ,a.ExceptionNumber       = b.ExceptionNumber
from dbo.PurPRApplicationDetailBackupVendors a 
left join #PurPRApplicationDetailBackupVendors b
on a.id = b.id

insert into dbo.PurPRApplicationDetailBackupVendors
(    ID
	 ,PRApplicationId
     ,PRApplicationDetailId
     ,VendorId
     ,VendorName
     ,PaymentTerm
     ,HcpLevelName
     ,HcpLevelCode
     ,Hospital
     ,Price
     ,Text
     ,ExtraProperties
     ,ConcurrencyStamp
     ,CreationTime
     ,CreatorId
     ,LastModificationTime
     ,[LastModifierId]
     ,IsDeleted
     ,[DeleterId]
     ,DeletionTime
     ,ExceptionNumber
)
select Id
     ,PRApplicationId
     ,PRApplicationDetailId
     ,VendorId
     ,VendorName
     ,PaymentTerm
     ,HcpLevelName
     ,HcpLevelCode
     ,Hospital
     ,Price
     ,Text
     ,ExtraProperties
     ,ConcurrencyStamp
     ,CreationTime
     ,CreatorId
     ,LastModificationTime
     ,[LastModifierId]
     ,IsDeleted
     ,[DeleterId]
     ,DeletionTime
     ,ExceptionNumber
FROM #PurPRApplicationDetailBackupVendors a
where not exists (select * from dbo.PurPRApplicationDetailBackupVendors where id = a.id)
