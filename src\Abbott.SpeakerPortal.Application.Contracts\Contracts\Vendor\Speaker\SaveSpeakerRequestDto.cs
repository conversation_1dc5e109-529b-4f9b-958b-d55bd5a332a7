﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

using Abbott.SpeakerPortal.Enums;

using Volo.Abp.Application.Dtos;

namespace Abbott.SpeakerPortal.Vendor.Speaker
{
    public class SaveSpeakerRequestDto
    {
        /// <summary>
        /// 操作类型
        /// </summary>
        [Required]
        public ApplicationTypes? ApplicationType { get; set; }
        /// <summary>
        /// 讲者草稿ID
        /// </summary>
        [Required]
        public Guid? ID { get; set; }
        /// <summary>
        /// 是否授权
        /// </summary>
        public bool BAuth { get; set; }
        /// <summary>
        ///申请人ID
        /// </summary>
        [Required]
        public Guid? ApplyUserId { get; set; }
        /// <summary>
        /// 申请人
        /// </summary>
        public string ApplyUserName { get; set; }
        /// <summary>
        ///部门ID
        /// </summary>
        [Required]
        public Guid? ApplyUserBu { get; set; }
        /// <summary>
        /// 部门
        /// </summary>
        public string ApplyUserBuName { get; set; }
        /// <summary>
        /// 讲者编码
        /// </summary>
        public string VendorCode { get; set; }
        /// <summary>
        /// 医生主键
        /// </summary>
        public string EpdId { get; set; }
        /// <summary>
        /// EPD医生手机号（AES256加密）
        /// 字符串数组
        /// </summary>
        public string Mobile { get; set; }
        /// <summary>
        /// 讲者名称
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string SPName { get; set; }
        /// <summary>
        /// 职称Id
        /// </summary>
        [Required]
        public Guid? PTId { get; set; }
        /// <summary>
        /// 职称
        /// </summary>
        public string PTIName { get; set; }
        /// <summary>
        /// 所属医院Id
        /// </summary>
        [Required]
        public Guid? HospitalId { get; set; }
        /// <summary>
        /// 所属医院
        /// </summary>
        public string HospitalName { get; set; }

        /// <summary>
        /// EPD医院Code，提交BU是EPD时填写
        /// </summary>
        public string EpdHospitalCode { get; set; }

        /// <summary>
        /// 标准科室Id
        /// </summary>
        [Required]
        public Guid? StandardHosDepId { get; set; }
        /// <summary>
        /// 标准科室
        /// </summary>
        public string StandardHosDepName { get; set; }
        /// <summary>
        /// 院内科室
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string HosDepartment { get; set; }
        /// <summary>
        /// 执业证书编码
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string CertificateCode { get; set; }
        /// <summary>
        /// 学术级别
        /// </summary>
        [MaxLength(100)]
        public string AcademicLevel { get; set; }
        /// <summary>
        /// 学会任职
        /// </summary>
        public List<AcademicPositionDto> AcademicPositionJson { get; set; }
        /// <summary>
        /// 是否是院士
        /// </summary>
        public bool IsAcademician { get; set; }
        /// <summary>
        /// 原BPM学会任职
        /// </summary>
        public string FormerBPMAcademicPosition { get; set; }
        /// <summary>
        /// 手机号码
        /// </summary>
        [MaxLength(20)]
        public string HandPhone { get; set; }
        /// <summary>
        /// 证件照片
        /// </summary>
        public AttachmentInformation CardPic { get; set; }
        ///<summary>
        /// 证件类型
        ///</summary>
        public string CardType { get; set; }
        /// <summary>
        /// 性别
        /// </summary>
        public Gender? Sex { get; set; }
        /// <summary>
        /// 证件编码
        /// </summary>
        [MaxLength(50)]
        public string CardNo { get; set; }
        /// <summary>
        /// 身份证省份
        /// </summary>
        public string Province { get; set; }
        /// <summary>
        /// 身份证城市
        /// </summary>
        public string City { get; set; }
        /// <summary>
        /// 身份证省份城市
        /// </summary>
        public string[] ProvinceCity { get; set; }
        /// <summary>
        /// 身份证住址邮编
        /// </summary>
        [MaxLength(20)]
        public string PostCode { get; set; }
        /// <summary>
        /// 身份证住址
        /// </summary>
        [MaxLength(100)]
        public string Address { get; set; }

        /// <summary>
        /// 银行名称
        /// </summary>
        [MaxLength(50)]
        public string BankCode { get; set; }
        /// <summary>
        /// 银行账号
        /// </summary>
        [MaxLength(50)]
        public string BankCardNo { get; set; }
        /// <summary>
        /// 银行所在城市
        /// </summary>
        [MaxLength(50)]
        public string[] BankCity { get; set; }
        /// <summary>
        /// 收款银行联行号
        /// </summary>
        [MaxLength(50)]
        public string BankNo { get; set; }
        /// <summary>
        /// 银行卡正面照片
        /// </summary>
        public AttachmentInformation BankCardImg { get; set; }
        /// <summary>
        /// 银行SwiftCode
        /// </summary>
        public string BankSwiftCode { get; set; }
        /// <summary>
        /// 财务信息
        /// </summary>
        public List<FinancialInformation> FinancialInformation { get; set; }
        /// <summary>
        /// 附件信息
        /// </summary>
        public List<AttachmentInformation> AttachmentInformation { get; set; }

        /// <summary>
        /// 草稿版本号
        /// </summary>
        public int DraftVersion { get; set; }

        /// <summary>
        /// 讲者级别
        /// </summary>
        public string SPLevel { get; set; }
        #region 小程序
        /// <summary>
        /// 小程序用户ID
        /// </summary>
        public string OpenId { get; set; }
        /// <summary>
        /// 是否小程序用户
        /// </summary>
        public bool IsMiniProgram { get; set; }
        #endregion
    }
}
