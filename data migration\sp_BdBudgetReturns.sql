CREATE PROCEDURE dbo.sp_BdBudgetReturns
AS 
BEGIN
	with BdBudgetReturns_tmp as (
select 
newid() AS Id,--自动生成的uuid
a.id as bpm_id,
case when (b.Folio<>null or b.Folio<>'') and SUBSTRING(b.Folio,1,1)<>'S' then b.Folio 
else 
	case when SUBSTRING(b.Folio,1,1)='S' 
		then 'NULL' else   
			case when (b.Folio=null or b.Folio='') and BDNumber='YXHD006' then  e.SerialNUmber
				 when BDNumber<>'YXHD006' and a.ProcInstId=0 then POOrArNumber
				 else 'NULL'
				 end
	end
end AS PrId,--以该编码匹配至T_PROCESS_Historys.ProcInstId，以查询出的Folio去查询出PurPRApplications对应的ID；
--若查询到的Folio以""S""开头代表为批发商核销申请单，此处留空；
--若无法在T_PROCESS_Historys查询到记录，且预算为形如YXHD006的编码，则以该编码匹配至AUTO_BIZ_T_ProcurementApplication_Info.ProcInstId，以查询出的SerialNUmber去查询PurPRApplications对应的ID
--若查询到ProcInstId为0，且预算编码并非""YXHD006""，目前仅有一条数据有该记录，需要基于ReturnProcinstId查询AUTO_BIZ_T_GoodsReceiveApplication_Info.ProcInstId，以查询出的POOrArNumber作为PR单号来使用"
PRNumber AS PdRowNo,--
BDNumber AS SubbudgetId,--以该编码匹配至12-1迁移的子预算ID
--(对于YXHD006这个预算编码，建议在子预算表里加入一条编码为YXHD006的虚拟预算编码，除Code外其他信息均为空，创建人为admin，创建时间直接填写为迁移的时间)"
Budget AS Amount,--返还金额
OperateDate AS OperateTime,--返还时间
'1' AS IsEnable,--默认填写为1
case when b.Folio<>null or b.Folio<>'' 
then b.Folio 
else case when c.SerialNumber<>null or c.SerialNumber<>'' 
		then c.SerialNumber else d.SerialNumber 
		end  
end AS ReturnSourceId,--按下方Code，到各类申请对应的主表找到ID信息：
--若为""O""开头，则以单号查询出PurPOApplications.ID
--若为""G""开头，则以单号查询出PurGRApplications.ID
--若为""A""开头，则以单号查询出PurPAApplications.ID"
case when b.Folio<>null or b.Folio<>'' 
then b.Folio 
else case when c.SerialNumber<>null or c.SerialNumber<>'' 
		then c.SerialNumber else d.SerialNumber 
		end  
end   AS ReturnSourceCode,--以该编码匹配至T_PROCESS_Historys.ProcInstId，填入查询出的Folio
--若无法在T_PROCESS_Historys查询到记录，以ProcInstId查询AUTO_BIZ_PurchaseOrderApplication_Info.ProcInstId后得到匹配出的SerialNumber
--若如上两种方式均无法查询到记录，以ProcInstId查询AUTO_BIZ_PurchaseOrderApplication_Info.ProcInstId后得到匹配出的SerialNumber"
'{}' AS ExtraProperties,--默认填写为"{}"
'' AS ConcurrencyStamp,--?
OperateDate AS CreationTime,--填写为该操作的操作时间
case when b.Folio<>null or b.Folio<>'' 
then b.Folio 
else case when c.SerialNumber<>null or c.SerialNumber<>'' 
		then c.SerialNumber else d.SerialNumber 
		end  
end AS CreatorId,--基于不同的ReturnSourceId填入对应的创建人
--若为""O""开头，则以单号查询出PurPOApplications.CreatorId
--若为""G""开头，则以单号查询出PurGRApplications.CreatorId
--若为""A""开头，则以单号查询出PurPAApplications.CreatorId"
'' AS LastModificationTime,--默认为空
'' AS LastModifierId,--默认为空
'0' AS IsDeleted,--默认填写为0
'' AS DeleterId,--默认为空
'' AS DeletionTime,--默认为空
'1' as flag
--into #BdBudgetReturns_tmp
from PLATFORM_ABBOTT_Dev.dbo.ODS_T_Pur_ExpenseBudget_ReturnInfo a 
left join (select DISTINCT ProcInstId,Folio from PLATFORM_ABBOTT_Dev.dbo.ods_T_PROCESS_Historys) b
on a.ReturnProcInstId=b.ProcInstId
left join PLATFORM_ABBOTT_Dev.dbo.ods_AUTO_BIZ_PurchaseOrderApplication_Info c
on a.ReturnProcInstId =c.ProcInstId 
left join PLATFORM_ABBOTT_Dev.dbo.ods_AUTO_BIZ_T_GoodsReceiveApplication_Info d
on a.ReturnProcInstId =d.ProcInstId 
left join (select DISTINCT ProcInstId,serialNumber from PLATFORM_ABBOTT_Dev.dbo.ods_AUTO_BIZ_T_ProcurementApplication_Info) e
on a.ProcInstId =e.ProcInstId 
where IsEnable='1'
union all
SELECT 
newid() AS Id,--自动生成的uuid
a.id as bpm_id,
case when (b.Folio<>null or b.Folio<>'') and SUBSTRING(b.Folio,1,1)<>'S' 
then b.Folio 
else  
	case when SUBSTRING(b.Folio,1,1)='S'  then 'NULL' 
		 when (b.Folio=null or b.Folio='') and Number='YXHD006' then SerialNUmber
		 else 'NULL'
	end 
end AS PrId,--以该编码匹配至T_PROCESS_Historys.ProcInstId，以查询出的Folio去查询出PurPRApplications对应的ID；
--若查询到的Folio以""S""开头代表为批发商核销申请单，此处留空；
--若无法在T_PROCESS_Historys查询到记录，且预算为形如YXHD006的编码，则以该编码匹配至AUTO_BIZ_T_ProcurementApplication_Info.ProcInstId，以查询出的SerialNUmber去查询PurPRApplications对应的ID"
PRNumber AS PdRowNo,--
Number AS SubbudgetId,--以该编码匹配至12-1迁移的子预算ID
--(对于YXHD006这个预算编码，建议在子预算表里加入一条编码为YXHD006的虚拟预算编码，除Code外其他信息均为空，创建人为admin，创建时间直接填写为迁移的时间)"
Budget AS Amount,--对于IsEnable=0的记录，表示PR被作废了，也即PR申请的金额就是进行了预算返还的金额
OperateDate AS OperateTime,--
'' AS IsEnable,--默认填写为1
case when b.Folio<>null or b.Folio<>'' 
	 then b.Folio 
	 else  SerialNUmber
end AS ReturnSourceId,--即PrId
case when b.Folio<>null or b.Folio<>'' 
	 then b.Folio 
	 else  SerialNUmber
end AS ReturnSourceCode,--以该编码匹配至T_PROCESS_Historys.ProcInstId，填入查询出的Folio；
--若无法在T_PROCESS_Historys查询到记录，且预算为形如YXHD006的编码，则以该编码匹配至AUTO_BIZ_T_ProcurementApplication_Info.ProcInstId，填入查询出的SerialNUmber"
'{}' AS ExtraProperties,--默认填写为"{}"
'' AS ConcurrencyStamp,--?
OperateDate AS CreationTime,--填写为该操作的操作时间
case when b.Folio<>null or b.Folio<>'' 
	 then b.Folio 
	 else  SerialNUmber
end AS CreatorId,--对于P开头的单号或纯数字单号，填充为该PR的申请人，即PurPRApplications.ApplyUserId
--applicantEmpId AS ,--对于S开头的单号，按ProcInstId查询对应的xml文件后填入该批发商核销单的申请人
'' AS LastModificationTime,--默认为空
'' AS LastModifierId,--默认为空
'0' AS IsDeleted,--默认填写为0
'' AS DeleterId,--默认为空
'' AS DeletionTime,--默认为空
'2' as flag
from PLATFORM_ABBOTT_Dev.dbo.ODS_T_Pur_ExpenseBudget_UseInfo a
left join (select DISTINCT ProcInstId,Folio from PLATFORM_ABBOTT_Dev.dbo.ods_T_PROCESS_Historys) b
on a.ProcInstId=b.ProcInstId
left join (select DISTINCT ProcInstId,serialNumber,applicantEmpId from PLATFORM_ABBOTT_Dev.dbo.ods_AUTO_BIZ_T_ProcurementApplication_Info) e
on a.ProcInstId =e.ProcInstId 
where a.IsEnable='0'
)
select * into #BdBudgetReturns_tmp from BdBudgetReturns_tmp --Updated Rows	355115


 IF OBJECT_ID(N'PLATFORM_ABBOTT_Dev.dbo.BdBudgetReturns_tmp ', N'U') IS NOT NULL
	BEGIN
		update a 
		set a.bpm_id              = b.bpm_id
           ,a.PrId                = b.PrId
           ,a.PdRowNo             = b.PdRowNo
           ,a.SubbudgetId         = b.SubbudgetId
           ,a.Amount              = b.Amount
           ,a.OperateTime         = b.OperateTime
           ,a.IsEnable            = b.IsEnable
           ,a.ReturnSourceId      = b.ReturnSourceId
           ,a.ReturnSourceCode    = b.ReturnSourceCode
           ,a.ExtraProperties     = b.ExtraProperties
           ,a.ConcurrencyStamp    = b.ConcurrencyStamp
           ,a.CreationTime        = b.CreationTime
           ,a.CreatorId           = b.CreatorId
           ,a.LastModificationTime= b.LastModificationTime
           ,a.LastModifierId      = b.LastModifierId
           ,a.IsDeleted           = b.IsDeleted
           ,a.DeleterId           = b.DeleterId
           ,a.DeletionTime        = b.DeletionTime
           ,a.flag                = b.flag
		from PLATFORM_ABBOTT_Dev.dbo.BdBudgetReturns_tmp a
		left join #BdBudgetReturns_tmp b
		on a.Bpm_id = b.Bpm_id
		
		insert into PLATFORM_ABBOTT_Dev.dbo.BdBudgetReturns_tmp 
		select a.Id
              ,a.bpm_id
              ,a.PrId
              ,a.PdRowNo
              ,a.SubbudgetId
              ,a.Amount
              ,a.OperateTime
              ,a.IsEnable
              ,a.ReturnSourceId
              ,a.ReturnSourceCode
              ,a.ExtraProperties
              ,a.ConcurrencyStamp
              ,a.CreationTime
              ,a.CreatorId
              ,a.LastModificationTime
              ,a.LastModifierId
              ,a.IsDeleted
              ,a.DeleterId
              ,a.DeletionTime
              ,a.flag
		from #BdBudgetReturns_tmp a
		where not exists (select * from PLATFORM_ABBOTT_Dev.dbo.BdBudgetReturns_tmp where Bpm_id = a.Bpm_id)
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into PLATFORM_ABBOTT_Dev.dbo.BdBudgetReturns_tmp from #BdBudgetReturns_tmp
    -- select * from #vendor_tbl
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END

END;

