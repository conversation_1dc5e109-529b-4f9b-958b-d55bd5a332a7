drop table #PurPAApplicationInvoices
SELECT
 TRY_CONVERT(UNIQUEIDENTIFIER, [Id]) [Id]
,TRY_CONVERT(UNIQUEIDENTIFIER, [PurPAApplicationId]) [PurPAApplicationId]
,ISNULL([InvoiceCode],'NULL') [InvoiceCode]
,ISNULL([InvoiceDate],GETDATE()) [InvoiceDate]
--,case when InvoiceTotalAmount is null then '0'
--else CAST(InvoiceTotalAmount AS float) end as 
,'0'[InvoiceTotalAmount]
--,case when TaxAmount is null  then '0'
--else  CAST(TaxAmount AS float) end as
,'0'[TaxAmount]
--,case when ExcludingTaxAmount  is null then '0'
--else CAST(ExcludingTaxAmount AS float) end as [ExcludingTaxAmount]
,'0'[ExcludingTaxAmount]
,[ExtraProperties]
,'NULL' [ConcurrencyStamp]
,[CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CreatorId]) [CreatorId]
,[LastModificationTime]
,TRY_CONVERT(UNIQUE<PERSON>ENTIFIER, [LastModifierId]) [LastModifierId]
,[IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, [DeleterId]) [DeleterId]
,[DeletionTime]
,[InvoiceType]
,[TaxRate]
INTO #PurPAApplicationInvoices
FROM (SELECT *,ROW_NUMBER() OVER(PARTITION BY Id ORDER BY Id) RK FROM PLATFORM_ABBOTT_STG.dbo.PurPAApplicationInvoices)a
WHERE RK = 1
;

select * from #PurPAApplicationInvoices

select InvoiceTotalAmount,ExcludingTaxAmount,TaxAmount,* from PLATFORM_ABBOTT_STG.dbo.PurPAApplicationInvoices

USE Speaker_Portal_STG;

UPDATE a 
SET 
 a.[Id] = b.[Id]
,a.[PurPAApplicationId] = b.[PurPAApplicationId]
,a.[InvoiceCode] = b.[InvoiceCode]
,a.[InvoiceDate] = b.[InvoiceDate]
,a.[InvoiceTotalAmount] = b.[InvoiceTotalAmount]
,a.[TaxAmount] = b.[TaxAmount]
,a.[ExcludingTaxAmount] = b.[ExcludingTaxAmount]
,a.[ExtraProperties] = b.[ExtraProperties]
,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = b.[CreatorId]
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = b.[LastModifierId]
,a.[IsDeleted] = b.[IsDeleted]
,a.[DeleterId] = b.[DeleterId]
,a.[DeletionTime] = b.[DeletionTime]
,a.[InvoiceType] = b.[InvoiceType]
,a.[TaxRate] = b.[TaxRate]
FROM dbo.PurPAApplicationInvoices a
left join #PurPAApplicationInvoices  b
ON a.id=b.id;


INSERT INTO dbo.PurPAApplicationInvoices
(
[Id]
,[PurPAApplicationId]
,[InvoiceCode]
,[InvoiceDate]
,[InvoiceTotalAmount]
,[TaxAmount]
,[ExcludingTaxAmount]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[InvoiceType]
,[TaxRate]
)
SELECT
[Id]
,[PurPAApplicationId]
,[InvoiceCode]
,[InvoiceDate]
,[InvoiceTotalAmount]
,[TaxAmount]
,[ExcludingTaxAmount]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[InvoiceType]
,[TaxRate]
FROM #PurPAApplicationInvoices a
WHERE not exists (select * from dbo.PurPAApplicationInvoices where id=a.id);


--truncate table dbo.PurPAApplicationInvoices

--alter table dbo.PurPAApplicationInvoices alter column [InvoiceCode] [nvarchar](50) NOT NULL
--alter table dbo.PurPAApplicationInvoices alter column [InvoiceType] [nvarchar](100) NULL