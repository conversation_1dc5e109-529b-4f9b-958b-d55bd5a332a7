CREATE PROCEDURE dbo.sp_VendorApplicationFinancials
AS 
BEGIN
	--供应商申请单-财务板块信息

select * into #VendorApplicationFinancials_Tmp from (
select 
newid() AS Id,--自动生成的uuid
vt.id AS ApplicationId,--此处填入对应VendorApplications的Id，用于标记该行为供应商申请单对应的财务信息
tsi.company_Value AS Company,--
tsi.currency_Text AS Currency,--
tsi.vendorNumber AS VendorCode,--
tsi.bankCode AS AbbottBank,--
tsi.vendorType AS VendorType,--
tsi.divisionCode AS Division,--
tsi.paymentType_Text AS PayType,--
tsi.ctryCode_Text AS CountryCode,--
cast(a.XmlContent as XML).value('(/root/SupplierApplication_financeInfoBlock_MainStore/BankType_Value)[1]', 'nvarchar(255)')  AS BankType,--
DPOCategory AS DpoCategory,--根据填入的值与DPO Category字典匹配后填入字典编码
cast(XmlContent as XML).value('(/root/SupplierApplication_PaymentInfoBlock_MainStore/TermCode_Value)[1]', 'nvarchar(255)')  AS PaymentTerm,--
'' AS BankNo,--
'{}' AS ExtraProperties,--默认填写为"{}"
'' AS ConcurrencyStamp,--?
vt.CreationTime AS CreationTime,--与对应的VendorApplications记录保持一致即可
vt.CreatorId AS CreatorId,--与对应的VendorApplications记录保持一致即可
'' AS LastModificationTime,--默认为空
'' AS LastModifierId,--默认为空
'0'AS IsDeleted,--默认为0
'' AS DeleterId,--默认为空
'' AS DeletionTime,--默认为空
SpendingCategory AS SpendingCategory,--此处填写的值就是字典编码，不需要额外匹配(但如果识别到了不存在于字典的值可能需要加入字典或前端展示时直接展示为code)
vt.ApplicationType as FinancialVendorStatus,
0 flag
from PLATFORM_ABBOTT.dbo.ODS_AUTO_BIZ_T_SupplierApplication_Info tsi
join  PLATFORM_ABBOTT.dbo.ODS_T_FORMINSTANCE_GLOBAL a
on tsi.ProcInstId =a.ProcInstId
join PLATFORM_ABBOTT.dbo.VendorApplications_Tmp vt
on tsi.ProcInstId =vt.ProcInstId
union all
select
newid() AS Id,--自动生成的uuid
vt.id AS ApplicationId,--此处填入对应VendorApplications的Id，用于标记该行为供应商申请单对应的财务信息
vc.[VCMPNY] as Company,
vc.[VCURR] as Currency,
tsi.supplierCode as VendorCode,
vc.[VMBANK] as AbbottBank,
vc.[VTYPE] as VendorType,
vc.[VMREF1] as Division,
vc.[VPAYTY] as PayType,
vc.[VCOUN] as CountryCode,
vc.[VLDCD1] as BankType,
vc.[VPALS2] as DpoCategory,
vc.[VTERMS] as PaymentTerm,
'' as BankNo,
'{}' as ExtraProperties,
'' as ConcurrencyStamp,
vt.CreationTime AS CreationTime,--与对应的VendorApplications记录保持一致即可
vt.CreatorId AS CreatorId,--与对应的VendorApplications记录保持一致即可
'' AS LastModificationTime,--默认为空
'' AS LastModifierId,--默认为空
'0'AS IsDeleted,--默认为0
'' AS DeleterId,--默认为空
'' AS DeletionTime,--默认为空
'' AS SpendingCategory,--默认为空 此处填写的值就是字典编码，不需要额外匹配(但如果识别到了不存在于字典的值可能需要加入字典或前端展示时直接展示为code)
vt.ApplicationType as FinancialVendorStatus,
1 flag
from PLATFORM_ABBOTT.dbo.ODS_AUTO_BIZ_T_HcpLevelApplication_info tsi --15405
join  PLATFORM_ABBOTT.dbo.ODS_T_FORMINSTANCE_GLOBAL a
on tsi.ProcInstId =a.ProcInstId
join PLATFORM_ABBOTT.dbo.VendorApplications_Tmp vt
on tsi.ProcInstId =vt.ProcInstId
join (select VENDOR,[VCMPNY],VNDNAM,[VCURR],[VMBANK],[VTYPE],[VMREF1],[VPAYTY],[VCOUN],[VLDCD1],[VPALS2],[VTERMS],ROW_NUMBER () over(partition by VENDOR,VNDNAM  order by VENDOR desc ) rn from PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a 
	join PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM b on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
	where vtype='NHIV') as vc
	on tsi.supplierCode = vc.VENDOR
	and tsi.SupplierCNName = vc.VNDNAM and vc.rn=1
)A


--写入目标表
IF OBJECT_ID(N'PLATFORM_ABBOTT.dbo.VendorApplicationFinancials_Tmp', N'U') IS NOT NULL
BEGIN
	PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
		UPDATE a
		SET 
		a.ApplicationId = b.ApplicationId
		,a.Company = b.Company
		,a.Currency = b.Currency
		,a.AbbottBank = b.AbbottBank
		,a.VendorType = b.VendorType
		,a.Division = b.Division
		,a.PayType = b.PayType
		,a.CountryCode = b.CountryCode
		,a.BankType = b.BankType
		,a.DpoCategory = b.DpoCategory
		,a.PaymentTerm = b.PaymentTerm
		,a.BankNo = b.BankNo
		,a.ExtraProperties = b.ExtraProperties
		,a.ConcurrencyStamp = b.ConcurrencyStamp
		,a.CreatorId = b.CreatorId
		,a.LastModificationTime = b.LastModificationTime
		,a.LastModifierId = b.LastModifierId
		,a.IsDeleted = b.IsDeleted
		,a.DeleterId = b.DeleterId
		,a.DeletionTime = b.DeletionTime
		,a.SpendingCategory = b.SpendingCategory
        ,a.FinancialVendorStatus = b.FinancialVendorStatus
		FROM PLATFORM_ABBOTT.dbo.VendorApplicationFinancials_Tmp a
		LEFT JOIN #VendorApplicationFinancials_Tmp b
		ON a.ApplicationId = b.ApplicationId
		AND a.VendorCode = b.VendorCode
		AND a.CreationTime = b.CreationTime
		
		INSERT INTO PLATFORM_ABBOTT.dbo.VendorApplicationFinancials_Tmp
		SELECT
		 a.Id
		,a.ApplicationId
		,a.Company
		,a.Currency
		,a.VendorCode
		,a.AbbottBank
		,a.VendorType
		,a.Division
		,a.PayType
		,a.CountryCode
		,a.BankType
		,a.DpoCategory
		,a.PaymentTerm
		,a.BankNo
		,a.ExtraProperties
		,a.ConcurrencyStamp
		,a.CreationTime
		,a.CreatorId
		,a.LastModificationTime
		,a.LastModifierId
		,a.IsDeleted
		,a.DeleterId
		,a.DeletionTime
		,a.SpendingCategory
        ,a.FinancialVendorStatus
		FROM #VendorApplicationFinancials_Tmp a
		WHERE NOT EXISTS (SELECT * FROM PLATFORM_ABBOTT.dbo.VendorApplicationFinancials_Tmp WHERE ApplicationId=a.ApplicationId AND VendorCode=a.VendorCode AND CreationTime=a.CreationTime)
END
ELSE
BEGIN
	select * into PLATFORM_ABBOTT.dbo.VendorApplicationFinancials_Tmp from #VendorApplicationFinancials_Tmp
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END

END
