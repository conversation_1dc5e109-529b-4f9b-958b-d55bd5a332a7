﻿using System.Threading;
using System.Threading.Tasks;
using Abbott.SpeakerPortal.Contracts.FOC;
using Hangfire;

namespace Abbott.SpeakerPortal.BackgroundWorkers.STicket
{
    public class PPRegionSyncWorker : SpeakerPortalBackgroundWorkerBase
    {
        public PPRegionSyncWorker()
        {
            //触发周期,每天凌晨2点
            CronExpression = Cron.Daily(2);
        }

        public override async Task DoWorkAsync(CancellationToken cancellationToken = default)
        {
            await LazyServiceProvider.LazyGetService<IFocService>().SyncPPRegionDataAsync();
        }
    }
}
