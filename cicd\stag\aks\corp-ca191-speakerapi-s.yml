﻿---
apiVersion: apps/v1
kind: Deployment
metadata:
  # Kind 的名称
  name: corp-ca191-speakerapi-s
  namespace: default
spec:
  selector:
    matchLabels:
      # 容器标签的名字，发布 Service 时，selector 需要和这里对应
      app: corp-ca191-speakerapi-s
  # 部署的实例数量
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 2        # 一次可以添加多少个Pod
      maxUnavailable: 2  # 滚动更新期间最大多少个Pod不可用
  template:
    metadata:
      labels:
        app: corp-ca191-speakerapi-s
    spec:
      # 配置容器，数组类型，说明可以配置多个容器
      containers:
      # 容器名称
      - name: corp-ca191-speakerapi-s
        # 容器镜像
        image: abbottchina.azurecr.cn/corp-ca191-nexbpm-stag/speaker-api:__TAG
        # 只有镜像不存在时，才会进行镜像拉取IfNotPresent
        imagePullPolicy: Always
        ports:
        # Pod 端口
        - containerPort: 80
        env:
        - name: ASPNETCORE_ENVIRONMENT
          value: "Stag"
        - name: ASPNETCORE_URLS
          value: "http://+:80"
        resources:
         requests:
          memory: 1Gi
          cpu: 200m
         limits:
          memory: 3Gi
          cpu: 500m
---
apiVersion: v1
kind: Service
metadata:
  name: corp-ca191-speakerapi-s
spec:
  selector:
    app: corp-ca191-speakerapi-s
  type: ClusterIP
  ports:
  - protocol: TCP
    port: 443
    targetPort: 80

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
 name: corp-ca191-apihpa-s
 namespace: default
spec:
 maxReplicas: 6
 minReplicas: 2
 scaleTargetRef:
  apiVersion: apps/v1
  kind: Deployment
  name: corp-ca191-speakerapi-s
 metrics:
 - type: Resource
   resource:
    name: memory
    target:
     type: Utilization
     averageUtilization: 60
 - type: Resource
   resource:
    name: cpu
    target:
     type: Utilization
     averageUtilization: 60
