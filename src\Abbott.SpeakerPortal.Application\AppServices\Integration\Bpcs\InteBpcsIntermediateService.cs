﻿using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Integration.Dspot;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Entities.VendorApplicationFinancials;
using Abbott.SpeakerPortal.Entities.VendorApplicationOrgnizations;
using Abbott.SpeakerPortal.Entities.VendorApplicationPersonals;
using Abbott.SpeakerPortal.Entities.VendorApplications;
using Abbott.SpeakerPortal.IntermediateDB;
using Abbott.SpeakerPortal.Enums.Integration;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Renci.SshNet;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Abbott.SpeakerPortal.IntermediateDB.Repositories;
using Abbott.SpeakerPortal.IntermediateDB.Models;

namespace Abbott.SpeakerPortal.AppServices.Integration.Dspot
{
    public class InteBpcsIntermediateService : SpeakerPortalAppService, IInteBpcsIntermediateService
    {
        /// <summary>
        /// The service provider
        /// </summary>
        private readonly IServiceProvider _serviceProvider;

        /// <summary>
        /// The logger
        /// </summary>
        private readonly ILogger<InteBpcsIntermediateService> _logger;

        /// <summary>
        /// The configuration
        /// </summary>
        private readonly IConfiguration _configuration;

        //private readonly SpeakerPortalIntermediateDbContext _intermediateDbContext;

        public InteBpcsIntermediateService(IServiceProvider serviceProvider)
            //, SpeakerPortalIntermediateDbContext intermediateDbContext)
        {
            _serviceProvider = serviceProvider;
            _logger = serviceProvider.GetService<ILogger<InteBpcsIntermediateService>>();
            _configuration = serviceProvider.GetService<IConfiguration>();

            //_intermediateDbContext = intermediateDbContext;
        }

        public async Task<string> SyncTablesTest()
        {
            //var intermediateDbContext = LazyServiceProvider.GetService<SpeakerPortalIntermediateDbContext>();

            //SpeakerPortalIntermediateDbContext dbContext = new SpeakerPortalIntermediateDbContext();
            //var list1 = intermediateDbContext.Abks.AsQueryable().ToList();

            //查询
            var repoAbk = LazyServiceProvider.GetService<IIntermediateAbkRepository>();
            var query = await repoAbk.GetQueryableAsync();
            var list1 = query.Where(a => 1 == 1).ToList();

            //修改：
            var updateEntity = list1[0];
            updateEntity.Bkcmpy = 13;
            var updateResult = await repoAbk.UpdateAsync(updateEntity, true);

            //新增：
            var insertResult = await repoAbk.InsertAsync(new Abk { Bkid = "12", Bform1 = "b3" }, true);

            //删除：
            await repoAbk.DeleteAsync(a => a.Bkid == insertResult.Bkid && a.Bform1 == insertResult.Bform1, true);

            return null;
        }
    }
}