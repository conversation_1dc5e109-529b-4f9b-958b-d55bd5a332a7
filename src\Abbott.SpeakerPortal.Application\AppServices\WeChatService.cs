﻿using Abbott.SpeakerPortal.AppServices.Common;
using Abbott.SpeakerPortal.Consent;
using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.Common;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.Common.Log;
using Abbott.SpeakerPortal.Entities.Common.WeChat;
using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.WeChat;
using Azure.Core;
using DocumentFormat.OpenXml.EMMA;
using DocumentFormat.OpenXml.Spreadsheet;
using Flurl.Http;
using Hangfire.Logging;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Senparc.Weixin;
using Senparc.Weixin.WxOpen.AdvancedAPIs.Sns;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows.Input;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.SettingManagement;

namespace Abbott.SpeakerPortal.WeChatService
{
    /// <summary>
    /// WeChatService
    /// </summary>
    public class WeChatService : SpeakerPortalAppService, IWeChatService
    {
        /// <summary>
        /// The service provider
        /// </summary>
        private readonly IServiceProvider _serviceProvider;
        /// <summary>
        /// The configuration
        /// </summary>
        private readonly IConfiguration _configuration;
        /// <summary>
        /// The logger
        /// </summary>
        private readonly ILogger<WeChatService> _logger;

        private readonly ICommonService _commonService;

        /// <summary>
        /// Initializes a new instance of the <see cref="WeChatService"/> class.
        /// </summary>
        /// <param name="serviceProvider">The service provider.</param>
        public WeChatService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _configuration = _serviceProvider.GetService<IConfiguration>();
            _logger = _serviceProvider.GetService<ILogger<WeChatService>>();
            _commonService = _serviceProvider.GetService<ICommonService>();
        }

        /// <summary>
        /// 获取二维码
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> GetQRPictureAsync(string request, bool isNoneSpeaker, bool isChangePhone)
        {
            var picture = await GetQRCode(request, isNoneSpeaker, isChangePhone);
            return MessageResult.SuccessResult(picture);
        }

        /// <summary>
        /// Gets the qr code.
        /// </summary>
        /// <param name="ticket">The ticket.</param>
        /// <returns></returns>
        private async Task<string> GetQRCode(string ticket, bool isNoneSpeaker, bool isChangePhone)
        {
            string apiHost = _configuration["WxApp:WxToken"];
            string appId = _configuration["WxApp:AppId"];
            string appSecret = _configuration["WxApp:AppSecret"];
            string page = _configuration["WxApp:WeChatUrl"];
            string width = _configuration["WxApp:QRwidth"];
            string enversion = _configuration["WxApp:Enversion"];
            string sp = isNoneSpeaker ? "T" : "F";
            string cp = isChangePhone ? "T" : "F";
            string d = !ticket.ToUpper().Contains('S') ? "T" : "F";
            string sence = $"tk={ticket}&sp={sp}&cp={cp}&d={d}";
            //获取token
            string token = await GetAccessToken(apiHost, appId, appSecret);
            //string token = await GetAccessToken(apiHost, "wx429668899289706a", "15fe34eebbaacceb8be80cd57344a78e");
            if (string.IsNullOrEmpty(token))
                return string.Empty;
            //获取二维码
            var response = await CreateXcxQrCode(sence, page, width, apiHost, token, enversion);
            if (!string.IsNullOrWhiteSpace(response))
            {
                var xcxQrCode = $"data:image/jpeg;base64,{response}";
                //记录二维码，生效失效状态
                await RecordQRCodeStatus(ticket);
                //获取小程序scheme码 (小程序发布后可用)
                //var openlink = await GetSchemeUrl(sence, page, apiHost, token, enversion);
                //return new QRPictureResponseDTO { PictureBase = xcxQrCode, PictureUrl = openlink };
                return xcxQrCode;
            }
            return string.Empty;
        }

        /// <summary>
        /// 记录二维码生效失效状态
        /// </summary>
        /// <param name="ticket"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        private async Task<bool> RecordQRCodeStatus(string ticket)
        {
            var recordsRepository = _serviceProvider.GetService<IQRCodeRecordRepository>();
            //var recordsQuery = await recordsRepository.GetQueryableAsync();
            //var effectivedRecords = recordsQuery.Where(a => a.ApplicationCode == ticket && a.IsEffective && a.EffectiveEndTime >= DateTime.Now).ToArray();
            var recordQuery = await recordsRepository.FirstOrDefaultAsync(a => a.ApplicationCode == ticket);
            var expirationDays = await LazyServiceProvider.LazyGetService<ISettingRepository>().FindAsync(SettingsConst.QRCodeExpirationDays, "G", null);
            try
            {
                if (recordQuery == null)
                {
                    //如果是空则记录二维码状态
                    var entityRecord = new QRCodeRecord
                    {
                        ApplicationCode = ticket,
                        IsEffective = true,
                        EffectiveStartTime = DateTime.Now,
                        EffectiveEndTime = DateTime.Now.AddDays(double.Parse(expirationDays?.Value)),
                    };
                    await recordsRepository.InsertAsync(entityRecord);
                }
                else
                {
                    //不为空则更新二维码状态
                    recordQuery.IsEffective = true;
                    recordQuery.EffectiveStartTime = DateTime.Now;
                    recordQuery.EffectiveEndTime = DateTime.Now.AddDays(double.Parse(expirationDays?.Value));
                    await recordsRepository.UpdateAsync(recordQuery);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"WeChatService's RecordQRCodeStatus has an error : {ex.Message}");
                return false;
            }
            return true;
        }

        /// <summary>
        /// Gets the access token.
        /// </summary>
        /// <param name="apiHost">The API host.</param>
        /// <param name="appid">The appid.</param>
        /// <param name="secret">The secret.</param>
        /// <returns></returns>
        public async Task<string> GetAccessToken(string apiHost, string appid, string secret)
        {
            var log = new SetOperationLogRequestDto();
            try
            {
                var url = $"{apiHost}/cgi-bin/token?grant_type=client_credential&appid={appid}&secret={secret}";
                var headers = new Dictionary<string, string>
                {
                    { "Content-Type", "application/json" }
                };
                log = _commonService.InitOperationLog("Wechat", "获取token", url);
                var tokenData = await url.WithHeaders(headers).GetJsonAsync<QRTokenResponseDto>();
                _commonService.LogResponse(log, JsonConvert.SerializeObject(tokenData));
                return tokenData.access_token;
            }
            catch (Exception ex)
            {
                _commonService.LogResponse(log, ex.ToString(), false);
                _logger.LogError($"WeChatService's GetAccessToken has an error : {ex.Message}");
                return null;
            }

            
        }

        /// <summary>
        /// Creates the XCX qr code.
        /// </summary>
        /// <param name="scene">The scene.</param>
        /// <param name="page">The page.</param>
        /// <param name="width">The width.</param>
        /// <param name="apiHost">The API host.</param>
        /// <param name="appid">The appid.</param>
        /// <param name="secret">The secret.</param>
        /// <param name="enversion"></param>
        /// <returns></returns>
        public async Task<string> CreateXcxQrCode(string scene, string page, string width, string apiHost, string token, string enversion)
        {
            var log = new SetOperationLogRequestDto();
            try
            {
                string base64 = string.Empty;
                //string token =await GetAccessToken(apiHost, appid, secret);
                string _url = $"{apiHost}/wxa/getwxacodeunlimit?access_token={token}";
                string jsonString = "{\"scene\":\"" + scene + "\",\"env_version\":\"" + enversion + "\",\"page\":\"" + page + "\",\"width\":\"" + width + "\",\"check_path\":false}";
                var headers = new Dictionary<string, string>
                {
                    { "Content-Type", "application/json" },
                };
                log = _commonService.InitOperationLog("Wechat", "获取二维码", jsonString);
                var data = await _url.WithHeaders(headers).PostStringAsync(jsonString);
                _commonService.LogResponse(log, JsonConvert.SerializeObject(data));

                if (data.ResponseMessage.IsSuccessStatusCode)
                    base64 = Convert.ToBase64String(data.ResponseMessage.Content.ReadAsByteArrayAsync().Result);

                return base64;
            }
            catch (Exception ex)
            {
                _commonService.LogResponse(log, ex.ToString(), false);
                return string.Empty;
            }
        }

        /// <summary>
        /// 获取小程序scheme码
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="isNoneSpeaker">The isNoneSpeaker.</param>
        /// <returns></returns>
        public async Task<string> GetSchemeUrl(string sence, string page, string apiHost, string token, string enversion)
        {
            try
            {
                string openlink = string.Empty;

                string _url = $"{apiHost}/wxa/generatescheme?access_token={token}";
                string jsonString = "{\"jump_wxa\":{\"path\":\"" + page + "\",\"query\":\"" + sence + "\",\"env_version\":\"" + enversion + "\"},\"is_expire\":true,\"expire_type\":1,\"expire_interval\":1}";
                var headers = new Dictionary<string, string>
                {
                    { "Content-Type", "application/json" },
                };
                var data = await _url.WithHeaders(headers).PostStringAsync(jsonString);
                openlink = await data.ResponseMessage.Content.ReadAsStringAsync();
                return openlink;
            }
            catch
            {
                return string.Empty;
            }
        }

        public async Task<string> GetPhoneNumberAsync(string code)
        {
            string phoneNumber = string.Empty;
            var log = new SetOperationLogRequestDto();
            try
            {
                string apiHost = _configuration["WxApp:WxToken"];
                string appId = _configuration["WxApp:AppId"];
                string appSecret = _configuration["WxApp:AppSecret"];

                string accessToken = await GetAccessToken(apiHost, appId, appSecret);
                if (!string.IsNullOrEmpty(accessToken))
                {
                    var url = $"{apiHost}/wxa/business/getuserphonenumber?access_token={accessToken}";
                    var requestBody = new Dictionary<string, string>
                    {
                        { "code",code }
                    };
                    var headers = new Dictionary<string, string>
                    {
                        { "Content-Type", "application/json" }
                    };
                    log = _commonService.InitOperationLog("Wechat", "获取手机号码", JsonConvert.SerializeObject(requestBody));
                    var data = await url.WithHeaders(headers).PostJsonAsync(requestBody);
                    
                    var phoneNumberResponse = await data.GetStringAsync();
                    _commonService.LogResponse(log, JsonConvert.SerializeObject(phoneNumberResponse));
                    var phoneNumberResponseDto = System.Text.Json.JsonSerializer.Deserialize<PhoneNumberResponseDto>(phoneNumberResponse);
                    if (phoneNumberResponseDto != null && phoneNumberResponseDto.errcode==0)
                    {
                        if(phoneNumberResponseDto?.phone_info != null && !string.IsNullOrWhiteSpace(phoneNumberResponseDto?.phone_info.purePhoneNumber)){
                            //无区号手机号码
                            phoneNumber = phoneNumberResponseDto?.phone_info.purePhoneNumber;
                        }
                    } 
                }
            }
            catch (Exception ex)
            {
                _commonService.LogResponse(log, ex.ToString(), false);
            }
            return phoneNumber;
        }
    }
}
