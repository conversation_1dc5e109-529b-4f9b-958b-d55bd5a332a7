﻿using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Integration.Veeva;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Dataverse.Contracts;
using Abbott.SpeakerPortal.Entities.Common.ScheduleJob;
using Abbott.SpeakerPortal.Entities.VendorApplicationPersonals;
using Abbott.SpeakerPortal.Entities.VendorApplications;
using Abbott.SpeakerPortal.Entities.VendorPersonals;
using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Extension;
using Abbott.SpeakerPortal.Utils;
using DocumentFormat.OpenXml.Wordprocessing;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Crm.Sdk.Messages;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Messages;
using Renci.SshNet;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

using VndEntities = Abbott.SpeakerPortal.Entities.Vendors;

namespace Abbott.SpeakerPortal.AppServices.Integration.Veeva
{
    public class InteVeevaBatchService : SpeakerPortalAppService, IInteVeevaBatchService
    {
        /// <summary>
        /// The service provider
        /// </summary>
        private readonly IServiceProvider _serviceProvider;

        /// <summary>
        /// The logger
        /// </summary>
        private readonly ILogger<InteVeevaBatchService> _logger;

        /// <summary>
        /// The configuration
        /// </summary>
        private readonly IConfiguration _configuration;

        /// <summary>
        /// The dataverseService
        /// </summary>
        private readonly IDataverseService _dataverseService;

        private readonly IScheduleJobLogService _jobService;
        private readonly IInteVeevaService _inteVeevaService;

        private readonly string _sftpIp;
        private readonly string _sftpPort;
        private readonly string _sftpUser;
        private readonly string _sftpPwd;
        private readonly string _aes256Key;
        private readonly string _aes256IV;

        public InteVeevaBatchService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _logger = serviceProvider.GetService<ILogger<InteVeevaBatchService>>();
            _configuration = serviceProvider.GetService<IConfiguration>();
            _dataverseService = serviceProvider.GetService<IDataverseService>();
            _inteVeevaService = serviceProvider.GetService<IInteVeevaService>();
            //读取veeva sftp配置
            _sftpIp = _configuration["Integrations:Veeva:SftpIP"];
            _sftpUser = _configuration["Integrations:Veeva:SftpUser"];
            _sftpPwd = _configuration["Integrations:Veeva:SftpPwd"];
            _sftpPort = _configuration["Integrations:Veeva:SftpPort"];
            _aes256Key = _configuration["Integrations:Veeva:AES256Key"];
            _aes256IV = _configuration["Integrations:Veeva:AES256IV"];
        }
                
        public string PushSpeakers(DateTime dtBegin, DateTime dtEnd)
        {
            //根据2个时间参数，筛选API推送到Veeva时得到vndApp.PushVeevaResp的时间？

            //得到时间区间内，所有推送过的vndApp
            
            //用这些vndApp，来组装Excel文件


            return null;
        }

        public string PullSpeakersResult(string fileName)
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// 年度讲者信息更新,只要hcp dcr和hco dcr
        /// 弃用，Veeva不再需要speaker推送待更新数据，Veeva直接将年度更新结果放到SFTP
        /// </summary>
        /// <param name="dtBegin">开始日期</param>
        /// <param name="dtEnd">结束日期</param>
        public async Task PushAnnualSpeackerInfo(DateTime dtBegin,DateTime dtEnd)
        {
            //1.时间如何获取，待确认。测试阶段读取全部的推送记录
            //var repoVndApp = LazyServiceProvider.LazyGetService<IVendorApplicationRepository>();
            //var vndApps = repoVndApp.GetListAsync(a => !string.IsNullOrEmpty(a.PushVeevaResp)).GetAwaiterResult();
            //return vndApps;
            var _vendorAppPersonalRepository = _serviceProvider.GetService<IVendorApplicationPersonalRepository>();
            var _vendorAppRepository = _serviceProvider.GetService<IVendorApplicationRepository>();
            var vendorAppQuery = await _vendorAppRepository.GetQueryableAsync();
            var vendorAppPersonalQuery = await _vendorAppPersonalRepository.GetQueryableAsync();
            var query = vendorAppQuery.Join(vendorAppPersonalQuery, a => a.Id, b => b.ApplicationId, (a, b) => new { b.Sex, b.SPName, vndApp = a })
                .Where(x => !string.IsNullOrEmpty(x.vndApp.PushVeevaResp))
                .Select(r => new
                {
                    r.Sex,
                    r.SPName,
                    r.vndApp.VendorCode,
                    r.vndApp.VendorId,
                    r.vndApp.HosDepartment,
                    r.vndApp.StandardHosDepId,
                    r.vndApp.PTId,
                    r.vndApp.CertificateCode,
                    r.vndApp.AcademicLevel,
                    r.vndApp.HospitalId,
                    r.vndApp.PushVeevaResp
                });
            var pushHistory = query.ToList();

            //2.读取字典数据
            var standartDept = GetStandarDepartments();
            var dicHospitals = GetHospitals();
        
            var pts = GetPTs();
            var dicAcademicLevel = GetAcademicLevels();
          

            var excelSource = new
            {
                hcp = new List<dynamic>() { },
                hco = new List<dynamic>() { }
            };
            //3.组装excel数据源
            foreach (var item in pushHistory)
            {
                //查询推送成功的记录
                VeevaDcrRespDto pushResp =null;
                try
                {
                    
                    pushResp = JsonSerializer.Deserialize<VeevaDcrRespDto>(item.PushVeevaResp);
                }
                catch (Exception)
                {
                    //反序列化异常，继续下一条
                    continue;
                }
                if (pushResp == null) continue;
                if (pushResp.ResponseStatus != "0") continue;
                
                string hcp_code = item.VendorCode;// VendorApplications.VendorCode
                string hcp_vid = item.VendorId;//VendorApplications.VendorId 同时也是veeva验证后的verify_vid__v字段
                string hcp_name = item.SPName;//VendorApplicationPersonals.SPName
                string hcp_sex = item.Sex.GetDescription() ?? "未知";//VendorApplicationPersonals.Sex
                string department_name = item.HosDepartment;//挂牌科室 VendorApplications.HosDepartment
                string primary_department_class = standartDept.GetValue(item.StandardHosDepId)?.Name;//标准科室 VendorApplications.StandardHosDepId
                string specialty = standartDept.GetValue(item.StandardHosDepId)?.Specialty;//专长
                string professional_title = pts.GetValue(item.PTId)?.Name; //职称 VendorApplications.PTId
                string certificate_code = item.CertificateCode;//执业医师证号 VendorApplications.CertificateCode
                string hco_code = dicHospitals.GetValue(item.HospitalId)?.HospitalCode;//VendorApplications.HospitalId
                string hco_name = dicHospitals.GetValue(item.HospitalId)?.Name;
                string hco_vid = dicHospitals.GetValue(item.HospitalId)?.HcoVeevaID;//从医院主数据获取veeva对应ID
                //下面是非必填字段
                string speaker_code = "";
                string subscribe_exponent = "";//重合订阅指数，hcp dcr result中有返回，但是未保存到系统
                string primary_relation_type = "";//职务，目前没有此字段
                string academic_title = dicAcademicLevel.GetValue(item.AcademicLevel)?.Name;//学术头衔 VendorApplications.AcademicLevel
                excelSource.hcp.Add(new
                {
                    hcp_code = hco_code,
                    hcp_vid= hcp_vid,
                    hcp_name= hcp_name,
                    hcp_sex = hcp_sex,
                    department_name= department_name,
                    primary_department_class= primary_department_class,
                    specialty=specialty,
                    professional_title= professional_title,
                    certificate_code=certificate_code,
                    primary_relation_type= primary_relation_type,
                    academic_title= academic_title,
                    hco_code= hco_code,
                    hco_name= hco_name,
                    hco_vid= hco_vid,
                });
                //根据PushVeevaResp 判断是否有hcp dcr，如果有加入到hco sheet页
                if (pushResp.Data?.hco_dcr != null)
                {
                    excelSource.hco.Add(new
                    {
                        hco_code = hco_code,
                        hco_name = hco_name,
                        hco_vid = hco_vid,
                        province = dicHospitals.GetValue(item.HospitalId)?.ProvinceName,
                        city = dicHospitals.GetValue(item.HospitalId)?.CityName,
                        district = dicHospitals.GetValue(item.HospitalId)?.District,
                        level = dicHospitals.GetValue(item.HospitalId)?.Level,
                        type = dicHospitals.GetValue(item.HospitalId)?.Type,
                        detail_type = dicHospitals.GetValue(item.HospitalId)?.DetailType
                    });
                }
            }

            //4.excel模板设置数据
            var (t_path, s_path) = GetUploadFilePath();
            MiniExcelLibs.MiniExcel.SaveAsByTemplate(s_path, t_path, excelSource);

            //5.excel放到目标SFTP
            UploadFile2Sftp(s_path);

        }

        public async Task<int> PullAnnualSpeackerInfo(string sftpFilePath = null)
        {
            int excelRowCount = 0;
            //默认sftp上的文件路径
            string remoteFilePath = @"/Template for Abbott Speaker Active Update.xlsx";

#if DEBUG
            remoteFilePath = @"/SP_ALL_TEST/AD_AP/ERROR/veeva/加密后.xlsx";
#endif

            // 调用者指定了 sftp文件的路径
            if (!string.IsNullOrEmpty(sftpFilePath))
            {
                remoteFilePath = sftpFilePath;
            }
            //1.读取文件
            var byteArray = GetByteArrayFromSftp(remoteFilePath);
            using (Stream stream = new MemoryStream(byteArray))
            {
                byte[] key = Encoding.UTF8.GetBytes(_aes256Key); // 32字节密钥
                byte[] iv = Encoding.UTF8.GetBytes(_aes256IV); // 16字节IV
                //解密文件流
                Stream decrptStream = AesHelper.DecryptFileStream(stream, key, iv);
                //2.处理HCO
                excelRowCount += await ProcessHCOResult(decrptStream);
                //3.处理HCP
                excelRowCount += ProcessHCPResult(decrptStream);
            }                        
            return excelRowCount;
        }

        /// <summary>
        /// 更新HCP记录
        /// </summary>
        /// <param name="fileStream"></param>
        /// <returns></returns>
        private int ProcessHCPResult(Stream fileStream)
        {
            /*
             验证状态=有效
                讲者数据：根据讲者编码，更新对应讲者的职称、科室等信息，所有信息以返回的结果为准进行替换，
                仅当"职称"有变化时需要进行如下额外操作：
                将讲者的信息+更新后的职称以API形式发起Veeva的讲者及HCP的Change类型DCR，以便根据年度验证后更新的信息来更新讲者的级别(讲者级别无法通过年度更新接口直接刷新，因此必须通过API方式发起dcr才能实现验证)
                若职称未发生变化，则验证状态flag标记为"验证通过"即可；若职称发生变化，验证状态flag在获取到API形式DCR结果后再更新为"验证通过"
             验证状态=无效
                讲者数据：根据讲者编码，将讲者验证状态标记为"验证不通过"，
                但对于讲者本身的档案状态(有效/无效)不需要进行调整(此处应该以BPCS内的供应商状态为准，Veeva的验证结果不会提供至BPCS处，只用于告知用户该讲者未能通过年度验证)
                ——建议此时查看讲者详情时，可展示此次验证不通过的备注说明(一般情况下主要是改名/转院/离职/退休/死亡等情况会导致过去验证通过的讲者变成无效，此时用户可以基于备注信息更新讲者信息重新验证)

             */
            var hcoList = GetHospitalsList();

            // 读取 hcp veeva交付数据
            var rows = MiniExcelLibs.MiniExcel.Query(fileStream, sheetName: "HCP", startCell: "A2").ToList();
            List<VndEntities.Vendor> updateVendors = new List<VndEntities.Vendor>();
            List<VendorPersonal> vendorPersonals = new List<VendorPersonal>();
            List<Guid> dcrVndIds = new List<Guid>();
            foreach (var item in rows)
            {                
                #region 获取excel数据              
                string v_status = (item.Q);
                string v_reason = (item.S);
                string v_hcovid = (item.T);
                string v_hconame = (item.U);                 
                string v_hcpcode = (item.V);
                string v_hcpvid = (item.X);
                string v_hcpname= (item.Y);
                string v_hosdepartment = (item.Z);//挂牌科室
                string v_standard_department = (item.AA);//标准科室
                string v_specialty = (item.AB);//专长
                string v_professional_title = (item.AC);//职称
                string v_primary_relation_type = (item.AD);//职务
                string v_academic_title = (item.AE);//学术头衔
                string v_sex = (item.AF);
                string v_certificate_code = (item.AG);//执业医师证号
                #endregion

                if (string.IsNullOrEmpty(v_hcpcode)) continue;// 根据医生code更新
                var vendor = GetVendor(v_hcpcode);
                if (vendor == null) continue;//根据code找不到医生
                vendor.YearlyReviewResult = v_status;
                vendor.YearlyReviewReason = v_reason;
                var isPTChanged = false;
                //状态是有效，按照veeva提供的信息更新
                if (v_status == YearlyReviewResult.Active.GetDescription())
                {                    
                    if (vendor.PTName != v_professional_title)
                    {
                        //职称变化，需要发起dcr
                        isPTChanged = true;
                    }
                    if (isPTChanged)
                    {
                        vendor.YearlyReviewResult = "";//职称变化，暂时不更新结果，等dcr验证后再更新
                    }
                    var hcoObj = hcoList.Where(h => h.HcoVeevaID == v_hcovid).SingleOrDefault();//根据医院主数据veevaid查询               
                    vendor.HospitalId = hcoObj.Id;//更新医院ID
                    vendor.HospitalName = v_hconame;//更新医院名称
                    vendor.VendorId = v_hcpvid;//更新 hcp veevaid
                    vendor.HosDepartment = v_hosdepartment;//挂牌科室
                    var standardHosDepId = CalcStandardHosDepId(v_standard_department, v_specialty);
                    vendor.StandardHosDepId = standardHosDepId.HasValue ? standardHosDepId.Value : vendor.StandardHosDepId;//标准科室id
                    vendor.StandardHosDepName = standardHosDepId.HasValue ? v_standard_department : vendor.StandardHosDepName;//标准科室名称
                    var ptId = CalcPtId(v_professional_title);
                    vendor.PTId = ptId.HasValue ? ptId.Value : vendor.PTId;//职称ID
                    vendor.PTName = ptId.HasValue ? v_professional_title : vendor.PTName;//职称名称
                    var academicLvlCode = CalcAcademicLvl(v_academic_title);
                    vendor.AcademicLevel = academicLvlCode ?? vendor.AcademicLevel;
                    vendor.CertificateCode = v_certificate_code;
                    vendor.RelationType = v_primary_relation_type;
                    var vendorPer = GetVendorPersonal(vendor.Id);
                    vendorPer.SPName = v_hcpname;//更新医生姓名
                    vendorPer.Sex = v_sex == "男" ? Gender.Male : Gender.Woman;
                    vendorPersonals.Add(vendorPer);
                }                               
                updateVendors.Add(vendor);

                if (isPTChanged)
                {
                    dcrVndIds.Add(vendor.Id);                    
                }
            }
            if (updateVendors.Count > 0)
            {
                LazyServiceProvider.LazyGetService<IVendorRepository>().UpdateManyAsync(updateVendors);
            }
            if (vendorPersonals.Count > 0)
            {
                LazyServiceProvider.LazyGetService<IVendorPersonalRepository>().UpdateManyAsync(vendorPersonals);
            }
            if (dcrVndIds.Count > 0)
            {
                foreach (var item in dcrVndIds)
                {
                    _inteVeevaService.PushDcrByYearlyReview(item);
                }
            }
            return rows.Count;
        }

        private async Task<int> ProcessHCOResult(Stream fileStream)
        {            
            var hcoList = GetHospitalsList();
            var provinceList = GetAllProvince();
            var cityList = GetAllCity();
            var dics = GetDictionaries();
            //读取 hco veeva交付数据
            var rows = MiniExcelLibs.MiniExcel.Query(fileStream, sheetName: "HCO", startCell: "A2").ToList();
            OrganizationRequestCollection orgRequests = [];

            
            foreach (var item in rows)
            {            
                #region 按excel列标题读取数据             
                string v_status = (item.M);//状态列
                string v_reason = (item.N);//reason
                string v_hco_code = (item.O);
                string v_hco_vid = (item.P);
                string v_hco_name = (item.Q);
                string v_hco_alternatename = (item.R);//别名/曾用名
                string v_province = (item.S);
                string v_city = (item.T);
                string v_district = (item.U);
                string v_address = (item.W);
                string v_postalcode = (item.X);
                string v_level = (item.Y);
                string v_hcodetailtype = (item.Z);
                string v_hcotype = (item.AA);
                string v_uscicode = (item.AB);
                string v_hcophone = (item.AC);
                string v_longitude = (item.AD);
                string v_latitude = (item.AE);
                #endregion
                /*
                 验证状态=有效:根据医院代码，更新医院主数据信息，所有信息以返回的结果为准进行替换
                 验证状态=无效:将医院状态标记为"验证不通过"
                 */
                var hcoObj = hcoList.Where(h => h.HospitalCode == v_hco_code).SingleOrDefault();
                //按照hco_code更新 PP数据
                if (hcoObj == null)
                {
                    // todo:根据code找不到对应，如何处理？
                    continue;
                }
                #region 组装更新数据

                var entityToUpdate = new Entity("spk_hospitalmasterdata") { Id = hcoObj.Id };
                /// 待验证: 923180000
                /// 已验证: 923180001
                /// 无效:   923180002
                entityToUpdate["spk_hospitalstatus"] = new OptionSetValue(v_status == "Active" ? 923180001 : 923180002);
                if (v_status == "Active")
                {
                    entityToUpdate["spk_hcoveevaid"] = v_hco_vid;
                    entityToUpdate["spk_name"] = v_hco_name;
                    entityToUpdate["spk_chinesevalue"] = v_hco_name;
                    entityToUpdate["spk_englishvalue"] = v_hco_name;
                    entityToUpdate["spk_altername"] = v_hco_alternatename;
                    //根据名称反查ID，感觉怪怪的，有其他方案吗？
                    var pObj = provinceList.Where(p => p.Name == v_province).SingleOrDefault();
                    entityToUpdate["spk_hcoprovince"] = new EntityReference("spk_province", (Guid)(pObj?.Id));
                    var cObj = cityList.Where(c => c.ProvinceId == pObj?.Id && c.Name == v_city).SingleOrDefault();
                    entityToUpdate["spk_hcocity"] = new EntityReference("spk_city", (Guid)(cObj?.Id));
                    entityToUpdate["spk_district"] = v_district;
                    entityToUpdate["spk_address"] = v_address;
                    entityToUpdate["spk_postalcode"] = v_postalcode;
                    var levelObj = dics.Where(x => x.ParentCode == "HcoLevel" && x.Name == v_level).SingleOrDefault();
                    entityToUpdate["spk_hcolevel"] = new EntityReference("spk_dictionary", (Guid)(levelObj?.Id));
                    entityToUpdate["spk_hcodetailtype"] = v_hcodetailtype;
                    entityToUpdate["spk_hcotype"] = v_hcotype;
                    entityToUpdate["spk_uscicode"] = v_uscicode;
                    entityToUpdate["spk_hcophone"] = v_hcophone;
                    entityToUpdate["spk_longitude"] = Convert.ToDecimal(v_longitude);
                    entityToUpdate["spk_latitude"] = Convert.ToDecimal(v_latitude);
                }                                                   
                orgRequests.Add(new UpdateRequest() { Target = entityToUpdate });
                #endregion
            }

            //批量更新
            ExecuteTransactionRequest multipleRequest = new ExecuteTransactionRequest()
            {
                Requests = orgRequests,
                ReturnResponses = true
            };
            await LazyServiceProvider.LazyGetService<IDataverseRepository>().DataverseClient.ExecuteAsync(multipleRequest);
            return rows.Count;
        }

       
        
        private (string,string) GetUploadFilePath()
        {
            var environment = LazyServiceProvider.GetService<IWebHostEnvironment>();
            var t_path = (Path.Combine(environment.ContentRootPath, @"wwwroot\Templates\Veeva\Template for Abbott Speaker Active Update.xlsx"));
            var s_path = (Path.Combine(environment.ContentRootPath, @$"wwwroot\Templates\Veeva\Template for Abbott Speaker Active Update_pending.xlsx"));
            return (t_path, s_path);
                                              
        }

        private (string, string) GetDownFilePath()
        {
            var environment = LazyServiceProvider.GetService<IWebHostEnvironment>();
            var local = (Path.Combine(environment.ContentRootPath, @"wwwroot\Templates\Veeva\Template for Abbott Speaker Active Update_veevaresult.xlsx"));
            var remote = @"/SP_ALL_TEST/AD_AP/ERROR/test/Template for Abbott Speaker Active Update_pending.xlsx";
            return (local, remote);                                                   
        }

        private VndEntities.Vendor GetVendor(string code)
        {
            var repoVnd = LazyServiceProvider.LazyGetService<IVendorRepository>();
            var vnd = repoVnd.GetAsync(a => a.VendorCode == code).GetAwaiterResult();
            return vnd;
        }

        private VendorPersonal GetVendorPersonal(Guid vendorId) 
        {
            var repoVndPer = LazyServiceProvider.LazyGetService<IVendorPersonalRepository>();
            var vndPer = repoVndPer.GetAsync(a => a.VendorId == vendorId).GetAwaiterResult();
            return vndPer;
        }

        

        #region SFTP交互
        private bool UploadFile2Sftp(string filePath)
        {
            bool result = false;


            var sftpFolder = @"/SP_ALL_TEST/AD_AP/ERROR/test/";


            try
            {
                //创建连接对象
                using (var client = new SftpClient(_sftpIp, Convert.ToInt32(_sftpPort), _sftpUser, _sftpPwd))
                {
                    client.Connect(); //连接

                    using (var fs = File.OpenRead(filePath).CreateMemoryStream())
                    {
                        if (fs != null && fs.Length > 0)
                        {
                            client.BufferSize = 4 * 1024 * 1024;
                            fs.Seek(0, SeekOrigin.Begin);

                            client.UploadFile(fs, sftpFolder);
                        }
                    }
                    result = true;
                }
            }
            catch (Exception ex)
            {

                _logger.LogError($"Veeva Sync Annual Update Exception: {ex}");
                result = false;
            }
            return result;
        }


        private byte[] GetByteArrayFromSftp(string remotePath)
        {

            try
            {
                //创建连接对象
                using (var client = new SftpClient(_sftpIp, Convert.ToInt32(_sftpPort), _sftpUser, _sftpPwd))
                {
                    client.Connect(); //连接
                    return client.ReadAllBytes(remotePath);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Veeva Sync Annual Update Exception: {ex}");
                throw;
            }            
        }
        #endregion

        #region 获取主数据从PP

        /// <summary>
        /// 找PP，找到就存其Code，未找到先返回Null。未找到时是否要怎么处理，待确认。
        /// </summary>
        private string CalcAcademicLvl(string verify_academic_title__v)
        {
            if (string.IsNullOrWhiteSpace(verify_academic_title__v))
            {
                return null;
            }
            var dicAcademicLevel = GetAcademicLevels();
            var item = dicAcademicLevel.Where(a => a.Value.Name.Equals(verify_academic_title__v, StringComparison.OrdinalIgnoreCase))
                .Select(a => a.Value).FirstOrDefault();
            return item?.Code;
        }

        /// <summary>
        /// 看回来的标准科室名称，在PP有否，有则查出其Id，无则先新增再查出其Id，返回此Id
        /// </summary>
        private Guid? CalcStandardHosDepId(string verify_primary_department_class__v,string v_specialty)
        {
            if (string.IsNullOrWhiteSpace(verify_primary_department_class__v))
            {
                return null;
            }
            var standartDept = GetStandarDepartments();
            var existEtt = standartDept?.Values.FirstOrDefault(a => a.Name.Equals(verify_primary_department_class__v, StringComparison.OrdinalIgnoreCase));
            if (existEtt != null)
            {
                return existEtt.Id;
            }

            //新增后再返回Id
            existEtt = new OfficeDto { Name = verify_primary_department_class__v, Specialty = v_specialty };
            return _dataverseService.AddDeptAsync(existEtt).GetAwaiterResult();
        }

        /// <summary>
        /// 职称处理，逻辑类似：CalcStandardHosDepId()
        /// </summary>
        private Guid? CalcPtId(string verify_professional_title__v)
        {
            if (string.IsNullOrWhiteSpace(verify_professional_title__v))
            {
                return null;
            }
            var jobTitle = GetPTs();
            var existEtt = jobTitle?.Values.FirstOrDefault(a => a.Name.Equals(verify_professional_title__v, StringComparison.OrdinalIgnoreCase));
            if (existEtt != null)
            {
                return existEtt.Id;
            }

            //新增后再返回Id
            existEtt = new JobTitleDto { Name = verify_professional_title__v };
            return _dataverseService.AddJobTitleAsync(existEtt).GetAwaiterResult();
        }

        private Dictionary<Guid, OfficeDto> GetStandarDepartments()
        {
            return _dataverseService.GetAllDepartments().GetAwaiterResult()
                .ToDictionary(a => a.Id, a => a);
        }

        private Dictionary<Guid, JobTitleDto> GetPTs()
        {
            var jobTiles = _dataverseService.GetAllJobTiles().GetAwaiterResult();
            return jobTiles.ToDictionary(a => a.Id, a => a);
        }

        private Dictionary<string, DictionaryDto> GetAcademicLevels()
        {
            var dicMeetingTypes = _dataverseService.GetDictionariesAsync(DictionaryType.LearningLevel).GetAwaiterResult();
            return dicMeetingTypes.ToDictionary(a => a.Code, a => a);
        }

        private Dictionary<Guid, HospitalDto> GetHospitals()
        {
            return _dataverseService.GetAllHospitals().GetAwaiter().GetResult()
               .ToDictionary(a => a.Id, a => a);
        }

        private List<HospitalDto> GetHospitalsList()
        {
            return _dataverseService.GetAllHospitals().GetAwaiter().GetResult();            
        }

        private List<ProvinceDto> GetAllProvince()
        { 
            return _dataverseService.GetAllProvince().GetAwaiter().GetResult();
        }

        private List<CityDto> GetAllCity()
        { 
            return _dataverseService.GetAllCity().GetAwaiter().GetResult();
        }

        private IEnumerable<DictionaryDto> GetDictionaries()
        { 
            return _dataverseService.GetDictionariesAsync().GetAwaiter().GetResult();
        }
        #endregion


    }
}