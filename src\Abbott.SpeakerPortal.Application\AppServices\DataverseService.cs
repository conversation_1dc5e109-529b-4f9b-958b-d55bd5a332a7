﻿using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.CrossBu;
using Abbott.SpeakerPortal.Contracts.Dataverse;
using Abbott.SpeakerPortal.Contracts.OEC.PayStandard;
using Abbott.SpeakerPortal.Dataverse.Contracts;
using Abbott.SpeakerPortal.Entities.Integration.Dspot;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Redis;

using DocumentFormat.OpenXml.Wordprocessing;

using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Identity.Client;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Messages;
using Microsoft.Xrm.Sdk.Metadata;
using Microsoft.Xrm.Sdk.Query;

using Newtonsoft.Json;

using StackExchange.Redis;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using Volo.Abp.Application.Dtos;

using static Abbott.SpeakerPortal.Enums.DataverseEnums;
using RedisKey = Abbott.SpeakerPortal.Consts.RedisKey;

namespace Abbott.SpeakerPortal.Dataverse
{
    public class DataverseService : SpeakerPortalAppService, IDataverseService
    {
        DistributedCacheEntryOptions _defaultCacheEntryOptions;
        private IDataverseRepository _dataverseRepository;
        private readonly IServiceProvider _serviceProvider;
        readonly IRedisRepository _redisRepository;
        /// <summary>
        /// 批量事务的最大允许请求数，微软默认只支持最多同时处理1000个请求，超过则会报错
        /// </summary>
        public static readonly int ExecuteTransactionMaxmumBatchSize = 1000;
        public DataverseService(IServiceProvider serviceProvider, IRedisRepository redisRepository)
        {
            _serviceProvider = serviceProvider;
            _dataverseRepository = _serviceProvider.GetService<IDataverseRepository>();
            _redisRepository = redisRepository;

            _defaultCacheEntryOptions = new DistributedCacheEntryOptions { AbsoluteExpiration = null, AbsoluteExpirationRelativeToNow = null, SlidingExpiration = null };
        }

        #region 主数据

        #region Organization相关

        /// <summary>
        /// 获取机构信息
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="count"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<List<DepartmentDto>> GetOrganizations(string pattern = null, int count = int.MaxValue, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_Organization))
            {
                var query = new QueryExpression("spk_organizationalmasterdata");
                query.ColumnSet.AddColumns("spk_name", "spk_parentorganization", "spk_organizationtype", "spk_costcenter", "spk_epoleader", "spk_orgparentcode", "spk_orgcode", "spk_bpmcode", "statecode", "spk_foccostcenter");
                var datas = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);

                entries = datas.Select(a =>
                {
                    var orgType = a.GetAttributeValue<OptionSetValue>("spk_organizationtype")?.Value;
                    var data = new DepartmentDto
                    {
                        Id = a.Id,
                        DepartmentName = a.GetAttributeValue<string>("spk_name"),
                        ParentDepartment = a.GetAttributeValue<EntityReference>("spk_parentorganization")?.Id,
                        OrganizationType = orgType.HasValue ? (DataverseEnums.Organization.OrganizationType)orgType.Value : null,
                        CostcenterId = a.GetAttributeValue<EntityReference>("spk_costcenter")?.Id,
                        OrgParentCode = a.GetAttributeValue<string>("spk_orgparentcode"),
                        OrgCode = a.GetAttributeValue<string>("spk_orgcode"),
                        BpmCode = a.GetAttributeValue<string>("spk_bpmcode"),
                        StateCode = (StateCode)a.GetAttributeValue<OptionSetValue>("statecode").Value,
                        EpoLeaderId = a.GetAttributeValue<EntityReference>("spk_epoleader")?.Id,
                        FocCostcenterId = a.GetAttributeValue<EntityReference>("spk_foccostcenter")?.Id,
                    };

                    return new HashEntry($"{data.Id}\\{data.OrganizationType}\\{data.DepartmentName}", JsonConvert.SerializeObject(data));
                });

                _redisRepository.Database.HashSet(RedisKey.BaseData_Organization, entries.ToArray());
            }

            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_Organization, $"*{pattern}*").Take(count);

            return entries.Select(a => JsonConvert.DeserializeObject<DepartmentDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode).ToList();
        }

        /// <summary>
        /// 获取员工和机构关系
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<IEnumerable<StaffDepartmentRelationDto>> GetStaffDepartmentRelations(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_Org_Staff_Relation))
            {
                var query = new QueryExpression("spk_organizational_staff");
                query.ColumnSet.AddColumns("spk_organizationalmasterdataid", "spk_staffmasterdataid", "statecode");
                var departStaffRelations = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);

                entries = departStaffRelations.Select(a =>
                {
                    var data = new StaffDepartmentRelationDto
                    {
                        Id = a.Id,
                        UserId = a.GetAttributeValue<EntityReference>("spk_staffmasterdataid").Id,
                        DepartmentId = a.GetAttributeValue<EntityReference>("spk_organizationalmasterdataid").Id,
                        StateCode = (StateCode)a.GetAttributeValue<OptionSetValue>("statecode").Value
                    };

                    return new HashEntry($"{data.Id}\\{data.UserId}\\{data.DepartmentId}", JsonConvert.SerializeObject(data));
                });

                _redisRepository.Database.HashSet(RedisKey.BaseData_Org_Staff_Relation, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_Org_Staff_Relation, $"*{pattern}*");

            return entries.Select(a => JsonConvert.DeserializeObject<StaffDepartmentRelationDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode);
        }

        #endregion

        #region 用户相关

        /// <summary>
        /// 获取员工数据
        /// </summary>
        /// <param name="pattern">Id\StaffCode</param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<IEnumerable<StaffDto>> GetStaffs(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_User))
            {
                var query = new QueryExpression("spk_staffmasterdata");
                query.ColumnSet.AddColumns("spk_staffnumber", "spk_name", "spk_staffemail", "spk_staffstate", "statecode", "spk_staffaccount", "spk_districtmasterdata");
                var staffs = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);

                entries = staffs.Select(a =>
                {
                    var staffState = a.GetAttributeValue<OptionSetValue>("spk_staffstate")?.Value;
                    var data = new StaffDto
                    {
                        Id = a.Id,
                        StaffCode = a.GetAttributeValue<string>("spk_staffnumber"),
                        Name = a.GetAttributeValue<string>("spk_name"),
                        Email = a.GetAttributeValue<string>("spk_staffemail"),
                        JobStatus = staffState.HasValue ? (DataverseEnums.Staff.JobStatus)staffState.Value : null,
                        IsActive = a.GetAttributeValue<OptionSetValue>("statecode")?.Value,
                        StaffAccount = a.GetAttributeValue<string>("spk_staffaccount"),
                        DistrictId = a.GetAttributeValue<EntityReference>("spk_districtmasterdata")?.Id,
                        DistrictName = a.GetAttributeValue<EntityReference>("spk_districtmasterdata")?.Name
                    };

                    return new HashEntry($"{data.Id}\\{data.StaffCode}", JsonConvert.SerializeObject(data));
                });

                _redisRepository.Database.HashSet(RedisKey.BaseData_User, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_User, $"*{pattern}*");

            return entries.Select(a => JsonConvert.DeserializeObject<StaffDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.IsActive == (int)stateCode);
        }

        /// <summary>
        /// 添加员工主数据
        /// </summary>
        /// <returns></returns>
        public async Task<Guid> AddStaffAsync(AddStaffRequestDto request)
        {
            //添加医院主数据
            var staffInstances = new Entity("spk_staffmasterdata");
            staffInstances["spk_staffnumber"] = request.StaffNumber;
            staffInstances["spk_staffaccount"] = request.StaffAccount;
            staffInstances["spk_name"] = request.Name;
            staffInstances["spk_gender"] = new OptionSetValue(request.StaffGender);
            staffInstances["spk_staffemail"] = request.StaffEmail;
            staffInstances["statecode"] = request.StateCode;
            var createId = await _dataverseRepository.DataverseClient.CreateAsync(staffInstances);
            return createId;
        }

        #endregion

        #region 职称

        /// <summary>
        /// Gets all job tiles.
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<List<JobTitleDto>> GetAllJobTiles(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_JobTitle))
            {
                var query = new QueryExpression("spk_occupationaltitlemasterdata");
                query.ColumnSet.AddColumns("spk_name", "statecode");
                var jobTitles = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);

                entries = jobTitles.Select(a =>
                {
                    var data = new JobTitleDto
                    {
                        Id = a.Id,
                        Name = a.GetAttributeValue<string>("spk_name"),
                        StateCode = (StateCode)a.GetAttributeValue<OptionSetValue>("statecode").Value
                    };

                    return new HashEntry($"{data.Id}", JsonConvert.SerializeObject(data));
                });

                _redisRepository.Database.HashSet(RedisKey.BaseData_JobTitle, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_JobTitle, $"*{pattern}*");

            return entries.Select(a => JsonConvert.DeserializeObject<JobTitleDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode).ToList();
        }

        /// <summary>
        /// 添加职称主数据
        /// </summary>
        public async Task<Guid> AddJobTitleAsync(JobTitleDto request)
        {
            var ett = new Entity("spk_occupationaltitlemasterdata");
            ett["spk_name"] = request.Name;
            var createId = await _dataverseRepository.DataverseClient.CreateAsync(ett);
            return createId;
        }
        #endregion

        #region 所属医院

        /// <summary>
        /// Gets all hospitals.
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="count"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<List<HospitalDto>> GetAllHospitals(string pattern = null, int count = int.MaxValue, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_Hospital))
            {
                var query = new QueryExpression("spk_hospitalmasterdata");
                query.ColumnSet.AddColumns("spk_name", "statecode", "spk_hospitalcode", "spk_hospitalstatus", "spk_hcoprovince", "spk_hcocity", "spk_district", "spk_hcoveevaid", "spk_hcolevel", "spk_hcodetailtype", "spk_hcotype");
                LinkEntity linkStep = query.AddLink("spk_province", "spk_hcoprovince", "spk_provinceid", JoinOperator.LeftOuter);
                linkStep.Columns.AddColumns("spk_provincialadministrativecode", "spk_name");
                linkStep.EntityAlias = "lines1";
                LinkEntity linkStep2 = query.AddLink("spk_city", "spk_hcocity", "spk_cityid", JoinOperator.LeftOuter);
                linkStep2.Columns.AddColumns("spk_cityadministrativedivisioncode", "spk_name");
                linkStep2.EntityAlias = "lines2";

                LinkEntity linkStep3 = query.AddLink("spk_dictionary", "spk_hcolevel", "spk_dictionaryid", JoinOperator.LeftOuter);
                linkStep3.Columns.AddColumns("spk_name");
                linkStep3.EntityAlias = "lines3";

                var hospitals = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);

                entries = hospitals.Select(a =>
                {
                    var data = new HospitalDto
                    {
                        Id = a.Id,
                        Name = a.GetAttributeValue<string>("spk_name"),
                        HcoVeevaID = a.GetAttributeValue<string>("spk_hcoveevaid"),
                        Status = a.GetAttributeValue<OptionSetValue>("spk_hospitalstatus") != null ? a.GetAttributeValue<OptionSetValue>("spk_hospitalstatus").Value : 0,
                        HospitalCode = a.GetAttributeValue<string>("spk_hospitalcode"),
                        Province = a.GetAttributeValue<AliasedValue>("lines1.spk_provincialadministrativecode")?.Value.ToString(),
                        ProvinceName = a.GetAttributeValue<AliasedValue>("lines1.spk_name")?.Value.ToString(),
                        City = a.GetAttributeValue<AliasedValue>("lines2.spk_cityadministrativedivisioncode")?.Value.ToString(),
                        CityName = a.GetAttributeValue<AliasedValue>("lines2.spk_name")?.Value.ToString(),
                        District = a.GetAttributeValue<string>("spk_district"),
                        Level = a.GetAttributeValue<AliasedValue>("lines3.spk_name")?.Value.ToString(),//a.GetAttributeValue<string>("spk_hcolevelname"),
                        Type = a.GetAttributeValue<string>("spk_hcotype"),
                        DetailType = a.GetAttributeValue<string>("spk_hcodetailtype"),
                        StateCode = (StateCode)a.GetAttributeValue<OptionSetValue>("statecode").Value
                    };

                    return new HashEntry($"{data.Id}\\{data.Name}\\{data.HospitalCode}\\{data.HcoVeevaID}", JsonConvert.SerializeObject(data));
                });

                _redisRepository.Database.HashSet(RedisKey.BaseData_Hospital, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_Hospital, $"*{pattern}*").Take(count);

            return entries.Select(a => JsonConvert.DeserializeObject<HospitalDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode).ToList();
        }

        /// <summary>
        /// 添加医院
        /// </summary>
        /// <returns></returns>
        public async Task<HospitalDto> AddHospitalAsync(AddHospitalRequestDto request)
        {
            //查询医院是否存在,防止已经验证重复的医院再次添加
            var hospitals = await GetAllHospitals();
            var hospitalDto = hospitals.FirstOrDefault(f => f.Name == request.HospitalName.Trim() && f.Province == request.HospitalProvinceCity[0] && f.City == request.HospitalProvinceCity[1]);
            if (hospitalDto != null) return null;

            //获取基础数据
            List<ProvinceDto> provinceList = await GetAllProvince();
            List<CityDto> cityList = await GetAllCity();
            var getHcoLevel = await GetDictionariesAsync(DictionaryType.HcoLevel);
            var provinceId = provinceList.FirstOrDefault(f => f.Code == request.HospitalProvinceCity[0])?.Id;
            var cityId = cityList.FirstOrDefault(f => f.Code == request.HospitalProvinceCity[1])?.Id;
            var levelId = getHcoLevel.FirstOrDefault(f => f.Code == request.HcoLevel)?.Id;

            //添加医院主数据
            var hospitalInstances = new Entity("spk_hospitalmasterdata");
            hospitalInstances["spk_name"] = request.HospitalName.Trim();
            hospitalInstances["spk_hospitalstatus"] = new OptionSetValue(923180000);
            hospitalInstances["spk_chinesevalue"] = request.HospitalName;
            hospitalInstances["spk_hcoprovince"] = new EntityReference("spk_province", (Guid)provinceId);
            hospitalInstances["spk_hcocity"] = new EntityReference("spk_city", (Guid)cityId);
            hospitalInstances["spk_hcolevel"] = new EntityReference("spk_dictionary", (Guid)levelId);
            var createId = await _dataverseRepository.DataverseClient.CreateAsync(hospitalInstances);
            return new HospitalDto
            {
                Id = createId,
                Name = request.HospitalName.Trim(),
                Status = 923180000,
                Province = request.HospitalProvinceCity[0],
                City = request.HospitalProvinceCity[1]
            };
        }

        public async Task<bool> UpdateHospitalAsync(Guid id, Dictionary<string, object> dicFields)
        {
            return await UpdateAsync("spk_hospitalmasterdata", id, dicFields);
        }

        public async Task<bool> UpdateAsync(string ettName, Guid id, Dictionary<string, object> dicFields)
        {
            var ett = await _dataverseRepository.DataverseClient.RetrieveAsync(ettName, id, new ColumnSet(dicFields.Keys.ToArray()));

            if (ett == null)
                return false;

            foreach (var item in dicFields)
            {
                ett[item.Key] = item.Value;
            }

            await _dataverseRepository.DataverseClient.UpdateAsync(ett);

            return true;
        }

        /// <summary>
        /// 从Redis删除PP 医院主数据
        /// </summary>
        /// <returns></returns>
        public bool DeleteHospitalFromRedis()
        {
            if (_redisRepository.Database.KeyExists(RedisKey.BaseData_Hospital))
            {
                return _redisRepository.Database.KeyDelete(RedisKey.BaseData_Hospital);
            }
            return true;
        }

        /// <summary>
        /// 删除PP 医院主数据
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        public bool DeleteHospitalFromPP(List<Guid> ids)
        {
            EntityCollection entityCollectionForDelete = new EntityCollection();
            foreach (var item in ids)
            {
                entityCollectionForDelete.Entities.Add(new Entity("spk_hospitalmasterdata", item));
            }
            if (entityCollectionForDelete.Entities.Count > 0)
            {
                TransactionRequest(null, null, entityCollectionForDelete);
            }
            return true;
        }

        #endregion

        #region 所属科室

        /// <summary>
        /// 获取科室数据
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<List<OfficeDto>> GetAllDepartments(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_Department))
            {
                var query = new QueryExpression("spk_departmentmasterdata");
                query.ColumnSet.AddColumns("spk_name", "spk_specialty", "statecode");
                var offices = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);

                entries = offices.Select(a =>
                {
                    var data = new OfficeDto
                    {
                        Id = a.Id,
                        Name = a.GetAttributeValue<string>("spk_name"),
                        Specialty = a.GetAttributeValue<string>("spk_specialty"),
                        StateCode = (StateCode)a.GetAttributeValue<OptionSetValue>("statecode").Value
                    };

                    return new HashEntry($"{data.Id}", JsonConvert.SerializeObject(data));
                });

                _redisRepository.Database.HashSet(RedisKey.BaseData_Department, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_Department, $"*{pattern}*");

            return entries.Select(a => JsonConvert.DeserializeObject<OfficeDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode).ToList();
        }

        /// <summary>
        /// 添加科室主数据（标准科室）
        /// </summary>
        public async Task<Guid> AddDeptAsync(OfficeDto request)
        {
            var ett = new Entity("spk_departmentmasterdata");
            ett["spk_name"] = request.Name;
            ett["spk_specialty"] = request.Specialty;
            var createId = await _dataverseRepository.DataverseClient.CreateAsync(ett);
            return createId;
        }

        #endregion

        #region 公司（财务付款方）

        /// <summary>
        /// 获取公司数据
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<List<CompanyDto>> GetCompanyList(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            /*
            IEnumerable<HashEntry> entries;
            List<CompanyDto> companyList;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_Company))
            {
                var query = new QueryExpression("spk_companymasterdata");
                query.ColumnSet.AddColumns("spk_chinesevalue", "spk_companycode", "spk_name", "spk_invoicetitle", "spk_englishtitle", "spk_invoiceaddress", "spk_phone", "spk_fax", "spk_openbank", "spk_bankaccount", "spk_abbrcode", "spk_bankcodermb", "spk_bankcodenonrmb", "statecode");
                var companies = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);

                companyList = companies.Select(a => new CompanyDto
                {
                    Id = a.Id,
                    CompanyCode = a.GetAttributeValue<string>("spk_companycode"),
                    CompanyName = a.GetAttributeValue<string>("spk_name"),
                    InvoiceTitle = a.GetAttributeValue<string>("spk_invoicetitle"),
                    InvoiceEnglishTitle = a.GetAttributeValue<string>("spk_englishtitle"),
                    InvoiceAddress = a.GetAttributeValue<string>("spk_invoiceaddress"),
                    Bank = a.GetAttributeValue<string>("spk_openbank"),
                    BankCardNo = a.GetAttributeValue<string>("spk_bankaccount"),
                    Fax = a.GetAttributeValue<string>("spk_fax"),
                    Telephone = a.GetAttributeValue<string>("spk_phone"),
                    AbbrCode = a.GetAttributeValue<string>("spk_abbrcode"),
                    Bankcodermb = a.GetAttributeValue<string>("spk_bankcodermb"),
                    Bankcodenonrmb = a.GetAttributeValue<string>("spk_bankcodenonrmb"),
                    StateCode = (StateCode)a.GetAttributeValue<OptionSetValue>("statecode").Value
                }).ToList();

                entries = companyList.Select(a =>
                {
                    return new HashEntry($"{a.Id}", JsonConvert.SerializeObject(a));
                });
                _redisRepository.Database.HashSet(RedisKey.BaseData_Company, entries.ToArray());
            }

            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_Company, $"*{pattern}*");
            companyList = entries.Select(a => JsonConvert.DeserializeObject<CompanyDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode).ToList();

            //查询相关货币
            var companyCurrency = await GetCompanyCurrencyList();
            foreach (var item in companyList)
            {
                item.CompanyCurrency = companyCurrency
                .Where(w => w.CompanyId == item.Id).OrderByDescending(o => o.Code == "RMB").ToList();
            }
            return companyList;
            */

            var res = await GetCompanyCurrencyInfoAsync(pattern, stateCode);

            return res.ToList();
        }

        /// <summary>
        /// 获取公司货币信息
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public Task<List<CurrencyDto>> GetCompanyCurrencyList(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            /*
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_CompanyCurrency))
            {
                var queryCurrency = new QueryExpression("spk_companycurrency");
                queryCurrency.ColumnSet.AddColumns("spk_company", "spk_currency", "statecode");

                LinkEntity linkDictionary = queryCurrency.AddLink("spk_dictionary", "spk_currency", "spk_dictionaryid", JoinOperator.Inner);
                linkDictionary.Columns.AddColumns("spk_name", "spk_code");
                linkDictionary.EntityAlias = "lines1";

                LinkEntity linkCurrencyconfig = queryCurrency.AddLink("spk_currencyconfig", "spk_currency", "spk_currency", JoinOperator.LeftOuter);
                linkCurrencyconfig.Columns.AddColumns("spk_rmbplanrate", "spk_currencysymbol");
                linkCurrencyconfig.EntityAlias = "lines2";

                LinkEntity linkCompany = queryCurrency.AddLink("spk_companymasterdata", "spk_company", "spk_companymasterdataid");
                linkCompany.Columns.AddColumns("spk_companycode");
                linkCompany.EntityAlias = "lines3";

                var records = _dataverseRepository.DataverseClient.RetrieveMultiple(queryCurrency);

                var companyList = records.Entities
                    .Select(s => new CurrencyDto
                    {
                        Id = s.Id,
                        Code = s.GetAttributeValue<AliasedValue>("lines1.spk_code")?.Value.ToString(),
                        Name = s.GetAttributeValue<AliasedValue>("lines1.spk_name")?.Value.ToString(),
                        CompanyId = s.GetAttributeValue<EntityReference>("spk_company")?.Id,
                        CompanyCode = s.GetAttributeValue<AliasedValue>("lines3.spk_companycode")?.Value.ToString(),
                        CurrencyId = s.GetAttributeValue<EntityReference>("spk_currency")?.Id,
                        CurrencySymbol = s.GetAttributeValue<AliasedValue>("lines2.spk_currencysymbol")?.Value.ToString() ?? "",
                        PlanRate = float.TryParse(s.GetAttributeValue<AliasedValue>("lines2.spk_rmbplanrate")?.Value?.ToString(), out float planRate) ? planRate : 0,
                        StateCode = (StateCode)s.GetAttributeValue<OptionSetValue>("statecode").Value
                    }).ToList();

                entries = companyList.Select(a =>
                {
                    return new HashEntry($"{a.Id}", JsonConvert.SerializeObject(a));
                });
                _redisRepository.Database.HashSet(RedisKey.BaseData_CompanyCurrency, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_CompanyCurrency, $"*{pattern}*");

            return Task.FromResult(entries.Select(a => JsonConvert.DeserializeObject<CurrencyDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode).ToList());
            */

            var res = GetCompanyCurrencyExtAsync(pattern, stateCode).Result;

            return Task.FromResult(res.ToList());
        }

        #region 修改公司币种
        /// <summary>
        /// 获取公司主数据
        /// </summary>
        /// <returns></returns>
        public async Task<IEnumerable<CompanyMasterDataDto>> GetCompanyAsync(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_Company))
            {
                var query = new QueryExpression("spk_companymasterdata");
                query.ColumnSet.AddColumns("spk_chinesevalue", "spk_companycode", "spk_name", "spk_invoicetitle", "spk_englishtitle", "spk_invoiceaddress", "spk_phone", "spk_fax", "spk_openbank", "spk_bankaccount", "spk_abbrcode", "spk_bankcodermb", "spk_bankcodenonrmb", "statecode");
                var entities = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);

                entries = entities.Select(a =>
                {
                    var data = new CompanyMasterDataDto
                    {
                        Id = a.Id,
                        CompanyCode = a.GetAttributeValue<string>("spk_companycode"),
                        CompanyName = a.GetAttributeValue<string>("spk_name"),
                        InvoiceTitle = a.GetAttributeValue<string>("spk_invoicetitle"),
                        InvoiceEnglishTitle = a.GetAttributeValue<string>("spk_englishtitle"),
                        InvoiceAddress = a.GetAttributeValue<string>("spk_invoiceaddress"),
                        Bank = a.GetAttributeValue<string>("spk_openbank"),
                        BankCardNo = a.GetAttributeValue<string>("spk_bankaccount"),
                        Fax = a.GetAttributeValue<string>("spk_fax"),
                        Telephone = a.GetAttributeValue<string>("spk_phone"),
                        AbbrCode = a.GetAttributeValue<string>("spk_abbrcode"),
                        Bankcodermb = a.GetAttributeValue<string>("spk_bankcodermb"),
                        Bankcodenonrmb = a.GetAttributeValue<string>("spk_bankcodenonrmb"),
                        StateCode = (StateCode)a.GetAttributeValue<OptionSetValue>("statecode").Value
                    };

                    return new HashEntry($"{data.Id}", JsonConvert.SerializeObject(data));
                }).ToArray();
                _redisRepository.Database.HashSet(RedisKey.BaseData_Company, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_Company, $"*{pattern}*");

            return entries.Select(a => JsonConvert.DeserializeObject<CompanyMasterDataDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode);
        }

        /// <summary>
        /// 获取公司货币映射配置
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<IEnumerable<CompanyCurrencyDto>> GetCompanyCurrencyAsync(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_CompanyCurrency))
            {
                var query = new QueryExpression("spk_companycurrency");
                query.ColumnSet.AddColumns("spk_company", "spk_currency", "statecode");

                var entities = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);

                entries = entities.Select(a =>
                {
                    var data = new CompanyCurrencyDto
                    {
                        Id = a.Id,
                        CompanyId = a.GetAttributeValue<EntityReference>("spk_company")?.Id,
                        CompanyName = a.GetAttributeValue<EntityReference>("spk_company")?.Name,
                        CurrencyId = a.GetAttributeValue<EntityReference>("spk_currency")?.Id,
                        CurrencyName = a.GetAttributeValue<EntityReference>("spk_currency")?.Name,
                        StateCode = (StateCode)a.GetAttributeValue<OptionSetValue>("statecode").Value
                    };
                    return new HashEntry($"{data.Id}", JsonConvert.SerializeObject(data));
                }).ToArray();
                _redisRepository.Database.HashSet(RedisKey.BaseData_CompanyCurrency, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_CompanyCurrency, $"*{pattern}*");

            return entries.Select(a => JsonConvert.DeserializeObject<CompanyCurrencyDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode);
        }

        /// <summary>
        /// 获取公司和币种扩展信息
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<IEnumerable<CurrencyDto>> GetCompanyCurrencyExtAsync(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            var coms = await GetCompanyAsync(stateCode: null);
            coms = coms.ToList();
            var comCurc = await GetCompanyCurrencyAsync(stateCode: null);
            comCurc = comCurc.ToList();
            var currencyConfig = await GetCurrencyConfig(stateCode: null);
            currencyConfig = currencyConfig.ToList();

            var comCurrencyList = new List<CurrencyDto>();

            foreach (var comcur in comCurc)
            {
                var cfg = currencyConfig.FirstOrDefault(x => x.CurrencyId == comcur.CurrencyId);
                comCurrencyList.Add(new CurrencyDto
                {
                    Id = comcur.Id,
                    Code = comcur.CurrencyName,
                    Name = comcur.CurrencyName,
                    CompanyId = comcur.CompanyId,
                    CompanyCode = coms.FirstOrDefault(x => x.Id == comcur.CompanyId)?.CompanyCode,
                    CurrencyId = comcur.CurrencyId,
                    CurrencySymbol = cfg?.CurrencySymbol,
                    PlanRate = cfg == null ? 0 : (float)cfg.PlanRate,
                    StateCode = comcur.StateCode
                });
            }

            if (!string.IsNullOrEmpty(pattern))
                comCurrencyList = comCurrencyList.Where(x => x.Id.ToString().Contains(pattern)).ToList();

            return comCurrencyList.WhereIf(stateCode.HasValue, x => x.StateCode == stateCode);
        }

        /// <summary>
        /// 获取公司与货币的关联信息
        /// </summary>
        /// <returns></returns>
        public async Task<IEnumerable<CompanyDto>> GetCompanyCurrencyInfoAsync(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            var coms = await GetCompanyAsync(stateCode: null);
            coms = coms.ToList();
            var comCurc = await GetCompanyCurrencyAsync(stateCode: null);
            comCurc = comCurc.ToList();

            var companyCurrencyInfoList = new List<CompanyDto>();

            var comCurrencyList = await GetCompanyCurrencyExtAsync(stateCode: null);


            foreach (var com in coms)
            {
                var currenciesIds = comCurc.Where(a => a.CompanyId == com.Id && a.CurrencyId.HasValue).Select(b => b.Id).Distinct();

                companyCurrencyInfoList.Add(new CompanyDto
                {
                    Id = com.Id,
                    CompanyCode = com.CompanyCode,
                    CompanyName = com.CompanyName,
                    InvoiceTitle = com.InvoiceTitle,
                    InvoiceEnglishTitle = com.InvoiceEnglishTitle,
                    InvoiceAddress = com.InvoiceAddress,
                    Bank = com.Bank,
                    BankCardNo = com.BankCardNo,
                    Fax = com.Fax,
                    Telephone = com.Telephone,
                    AbbrCode = com.AbbrCode,
                    Bankcodermb = com.Bankcodermb,
                    Bankcodenonrmb = com.Bankcodenonrmb,
                    StateCode = com.StateCode,
                    CompanyCurrency = comCurrencyList.Where(x => currenciesIds.Contains(x.Id)).OrderByDescending(x => x.Code == "RMB").ToList()
                });
            }

            if (!string.IsNullOrEmpty(pattern))
                companyCurrencyInfoList = companyCurrencyInfoList.Where(x => x.Id.ToString().Contains(pattern)).ToList();

            return companyCurrencyInfoList.WhereIf(stateCode.HasValue, x => x.StateCode == stateCode).OrderByDescending(x => x.CompanyName == "TRADING");
        }


        /// <summary>
        /// 获取公司主数据组织映射
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<IEnumerable<CompanyAndOrgRelationDto>> GetCompanyAndOrgRelationAsync(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_CompanyAndOrgRelation))
            {
                var query = new QueryExpression("spk_companymasterdata_organizational");
                query.ColumnSet.AddColumns("spk_company", "spk_organizational", "statecode");
                var entities = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);

                entries = entities.Select(a =>
                {
                    var data = new CompanyAndOrgRelationDto
                    {
                        Id = a.Id,
                        CompanyId = a.GetAttributeValue<EntityReference>("spk_company")?.Id,
                        CompanyName = a.GetAttributeValue<EntityReference>("spk_company")?.Name,
                        OrgId = a.GetAttributeValue<EntityReference>("spk_organizational")?.Id,
                        OrgName = a.GetAttributeValue<EntityReference>("spk_organizational")?.Name,
                        StateCode = (StateCode)a.GetAttributeValue<OptionSetValue>("statecode").Value
                    };

                    return new HashEntry($"{data.Id}", JsonConvert.SerializeObject(data));
                }).ToArray();
                _redisRepository.Database.HashSet(RedisKey.BaseData_CompanyAndOrgRelation, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_CompanyAndOrgRelation, $"*{pattern}*");

            return entries.Select(a => JsonConvert.DeserializeObject<CompanyAndOrgRelationDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode);
        }
        #endregion


        /// <summary>
        /// PaymentTerms simple
        /// </summary>
        /// <returns></returns>
        public async Task<IEnumerable<DictionaryDto>> GetSimplePaymentTermsAsync()
        {
            var paymentTermRepository = await LazyServiceProvider.LazyGetService<IBpcsAvtRepository>().GetQueryableAsync();
            var paymentTerms = paymentTermRepository.GroupBy(a => new { a.Vcmpy, a.Vterm })
                .Select(g => new DictionaryDto
                {
                    Code = g.Key.Vcmpy + "_" + g.Key.Vterm,
                    Name = g.Key.Vterm,
                    Type = g.FirstOrDefault().Vtmdsc
                });

            return paymentTerms.ToList();
        }

        #endregion

        #region 大区主数据

        /// <summary>
        /// 获取大区
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<IEnumerable<DistrictDto>> GetDistrict(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_District))
            {
                var query = new QueryExpression("spk_districtmasterdata");
                query.ColumnSet.AddColumns("spk_name", "spk_districtcode", "statecode");
                var entities = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);

                entries = entities.Select(a =>
                {
                    var data = new DistrictDto
                    {
                        Id = a.Id,
                        Name = a.GetAttributeValue<string>("spk_name"),
                        DistrictCode = a.GetAttributeValue<string>("spk_districtcode"),
                        StateCode = (StateCode)a.GetAttributeValue<OptionSetValue>("statecode").Value
                    };
                    return new HashEntry($"{data.Id}\\{data.Name}", JsonConvert.SerializeObject(data));
                }).ToArray();

                _redisRepository.Database.HashSet(RedisKey.BaseData_District, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_District, $"*{pattern}*");

            return entries.Select(a => JsonConvert.DeserializeObject<DistrictDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode);
        }

        #endregion

        #region 省市

        /// <summary>
        /// 获取省份
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<List<ProvinceDto>> GetAllProvince(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            var key = RedisKey.BaseData_Province;
            if (!_redisRepository.Database.KeyExists(key))
            {
                var query = new QueryExpression("spk_province");
                query.ColumnSet.AddColumns("spk_name", "spk_provincialadministrativecode", "statecode");
                var entities = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);

                entries = entities.Select(a =>
                {
                    var data = new ProvinceDto
                    {
                        Id = a.Id,
                        Code = a.GetAttributeValue<string>("spk_provincialadministrativecode"),
                        Name = a.GetAttributeValue<string>("spk_name"),
                        StateCode = (StateCode)a.GetAttributeValue<OptionSetValue>("statecode").Value
                    };

                    return new HashEntry($"{data.Id}\\{data.Code}\\{data.Name}", JsonConvert.SerializeObject(data));
                });

                _redisRepository.Database.HashSet(key, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(key, $"*{pattern}*");

            return entries.Select(a => JsonConvert.DeserializeObject<ProvinceDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode).ToList();
        }

        /// <summary>
        /// 获取城市
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<List<CityDto>> GetAllCity(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            var key = RedisKey.BaseData_City;
            if (!_redisRepository.Database.KeyExists(key))
            {
                var query = new QueryExpression("spk_city");
                query.ColumnSet.AddColumns("spk_name", "spk_cityadministrativedivisioncode", "spk_provincename", "statecode");
                var entities = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);

                entries = entities.Select(a =>
                {
                    var data = new CityDto
                    {
                        Id = a.Id,
                        Code = a.GetAttributeValue<string>("spk_cityadministrativedivisioncode"),
                        Name = a.GetAttributeValue<string>("spk_name"),
                        ProvinceId = a.GetAttributeValue<EntityReference>("spk_provincename").Id,
                        StateCode = (StateCode)a.GetAttributeValue<OptionSetValue>("statecode").Value
                    };

                    return new HashEntry($"{data.Id}\\{data.Code}\\{data.Name}", JsonConvert.SerializeObject(data));
                });

                _redisRepository.Database.HashSet(key, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(key, $"*{pattern}*");

            return entries.Select(a => JsonConvert.DeserializeObject<CityDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode).ToList();
        }

        #endregion

        /// <summary>
        /// 获取产品数据
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<IEnumerable<ProductDto>> GetProductsAsync(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_Product))
            {
                var query = new QueryExpression("spk_productmasterdata");
                query.ColumnSet.AddColumns("spk_name", "spk_chinesevalue", "spk_englishvalue", "spk_limitexpensecode", "spk_productcode", "statecode");
                var entities = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);

                entries = entities.Select(a =>
                {
                    var data = new ProductDto
                    {
                        Id = a.Id,
                        Name = a.GetAttributeValue<string>("spk_name"),
                        ChineseName = a.GetAttributeValue<string>("spk_chinesevalue"),
                        EnglishName = a.GetAttributeValue<string>("spk_englishvalue"),
                        Code = a.GetAttributeValue<string>("spk_productcode"),
                        LimitCostNatureCode = a.GetAttributeValue<string>("spk_limitexpensecode"),
                        StateCode = (StateCode)a.GetAttributeValue<OptionSetValue>("statecode").Value
                    };
                    return new HashEntry($"{data.Id}\\{data.Code}", JsonConvert.SerializeObject(data));
                }).ToArray();

                _redisRepository.Database.HashSet(RedisKey.BaseData_Product, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_Product, $"*{pattern}*");

            return entries.Select(a => JsonConvert.DeserializeObject<ProductDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode);
        }

        /// <summary>
        /// 获取机构和产品关系
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<IEnumerable<OrgProducRelationtDto>> GetOrgProductRelationsAsync(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_OrgProductRelation))
            {
                var query = new QueryExpression("spk_organizational_product");
                query.ColumnSet.AddColumns("spk_organizational_productid", "spk_bu", "spk_product", "statecode");
                var entities = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);

                entries = entities.Select(a =>
                {
                    var data = new OrgProducRelationtDto
                    {
                        Id = a.Id,
                        OrgId = a.GetAttributeValue<EntityReference>("spk_bu").Id,
                        OrgName = a.GetAttributeValue<EntityReference>("spk_bu").Name,
                        ProductId = a.GetAttributeValue<EntityReference>("spk_product").Id,
                        ProductName = a.GetAttributeValue<EntityReference>("spk_product").Name,
                        StateCode = (StateCode)a.GetAttributeValue<OptionSetValue>("statecode").Value
                    };
                    return new HashEntry($"{data.Id}\\{data.OrgId}\\{data.ProductId}", JsonConvert.SerializeObject(data));
                }).ToArray();

                _redisRepository.Database.HashSet(RedisKey.BaseData_OrgProductRelation, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_OrgProductRelation, $"*{pattern}*");

            return entries.Select(a => JsonConvert.DeserializeObject<OrgProducRelationtDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode);
        }

        /// <summary>
        /// 获取成本中心数据
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<IEnumerable<CostcenterDto>> GetCostcentersAsync(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_Costcenter))
            {
                var query = new QueryExpression("spk_costcentermasterdata");
                query.ColumnSet.AddColumns("spk_costcentercode", "spk_name", "spk_englishvalue", "spk_ccentercode", "statecode");
                var entities = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);

                entries = entities.Select(a =>
                {
                    var data = new CostcenterDto
                    {
                        Id = a.Id,
                        Code = a.GetAttributeValue<string>("spk_costcentercode"),
                        Name = a.GetAttributeValue<string>("spk_name"),
                        NameEn = a.GetAttributeValue<string>("spk_englishvalue"),
                        CcenterCode = a.GetAttributeValue<string>("spk_ccentercode"),
                        StateCode = (StateCode)a.GetAttributeValue<OptionSetValue>("statecode").Value
                    };
                    return new HashEntry($"{data.Id}\\{data.Code}", JsonConvert.SerializeObject(data));
                }).ToArray();

                _redisRepository.Database.HashSet(RedisKey.BaseData_Costcenter, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_Costcenter, $"*{pattern}*");

            return entries.Select(a => JsonConvert.DeserializeObject<CostcenterDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode);
        }

        /// <summary>
        /// 获取消费大类数据
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<IEnumerable<ConsumeCategoryDto>> GetConsumeCategoryAsync(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_ConsumeCategory))
            {
                var query = new QueryExpression("spk_consume");
                query.ColumnSet.AddColumns("spk_name", "spk_organizational", "spk_englishname", "spk_number", "spk_isdspot", "spk_iscomplianceaudit", "spk_isassets", "statecode", "spk_isvendortype", "spk_flowtype");
                var entities = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);

                entries = entities.Select(a =>
                {
                    var data = new ConsumeCategoryDto
                    {
                        Id = a.Id,
                        OrgId = a.GetAttributeValue<EntityReference>("spk_organizational")?.Id,
                        Name = a.GetAttributeValue<string>("spk_name"),
                        NameEn = a.GetAttributeValue<string>("spk_englishname"),
                        Code = a.GetAttributeValue<string>("spk_number"),
                        IsDspot = a.GetAttributeValue<bool?>("spk_isdspot"),
                        IsComplianceAudit = a.GetAttributeValue<bool?>("spk_iscomplianceaudit"),
                        IsAssets = a.GetAttributeValue<bool?>("spk_isassets"),
                        IsVendorType = a.GetAttributeValue<bool?>("spk_isvendortype"),
                        FlowType = (ConsumeCategory.FlowTypes?)a.GetAttributeValue<OptionSetValue>("spk_flowtype")?.Value,
                        StateCode = (StateCode)a.GetAttributeValue<OptionSetValue>("statecode").Value
                    };
                    return new StackExchange.Redis.HashEntry($"{data.OrgId}\\{data.Id}", JsonConvert.SerializeObject(data));
                }).ToArray();

                _redisRepository.Database.HashSet(RedisKey.BaseData_ConsumeCategory, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_ConsumeCategory, $"*{pattern}*");

            return entries.Select(a => JsonConvert.DeserializeObject<ConsumeCategoryDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode);
        }

        /// <summary>
        /// 获取消费大类和赞助类型的关系
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<IEnumerable<ConsumeCategorySponsorshipTypeRelationDto>> GetConsumeCategorySponsorshipTypeRelationDtosAsync(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_ConsumeCategorySponsorshipTypeRelation))
            {
                var query = new QueryExpression("spk_consume_dictionary");
                query.ColumnSet.AddColumns("spk_consume", "spk_dictionarycode", "statecode");
                var entities = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);

                entries = entities.Select(a =>
                {
                    var data = new ConsumeCategorySponsorshipTypeRelationDto
                    {
                        Id = a.Id,
                        ConsumeCategoryId = a.GetAttributeValue<EntityReference>("spk_consume")?.Id,
                        SponsorshipTypeCode = a.GetAttributeValue<string>("spk_dictionarycode"),
                        StateCode = (StateCode)a.GetAttributeValue<OptionSetValue>("statecode").Value
                    };
                    return new StackExchange.Redis.HashEntry($"{data.Id}\\{data.ConsumeCategoryId}\\{data.SponsorshipTypeCode}", JsonConvert.SerializeObject(data));
                }).ToArray();

                _redisRepository.Database.HashSet(RedisKey.BaseData_ConsumeCategorySponsorshipTypeRelation, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_ConsumeCategorySponsorshipTypeRelation, $"*{pattern}*");

            return entries.Select(a => JsonConvert.DeserializeObject<ConsumeCategorySponsorshipTypeRelationDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode);
        }

        /// <summary>
        /// 获取费用性质数据
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<IEnumerable<CostNatureDto>> GetCostNatureAsync(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_CostNature))
            {
                var query = new QueryExpression("spk_costnature");
                query.ColumnSet.AddColumns("spk_consume", "spk_paymentmethod", "spk_costnumber", "spk_name", "spk_englishname", "spk_approvalnumber", "spk_pushonlinemeeting", "spk_ar", "statecode", "spk_description", "spk_soiverifytype");
                var entities = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);

                entries = entities.Select(a =>
                {
                    var data = new CostNatureDto
                    {
                        Id = a.Id,
                        ConsumeCategoryId = a.GetAttributeValue<EntityReference>("spk_consume")?.Id,
                        Code = a.GetAttributeValue<string>("spk_costnumber"),
                        Name = a.GetAttributeValue<string>("spk_name"),
                        NameEn = a.GetAttributeValue<string>("spk_englishname"),
                        PayMethods = a.GetAttributeValue<string>("spk_paymentmethod"),
                        ApprovalNumber = a.GetAttributeValue<string>("spk_approvalnumber"),
                        PushOnlineMeeting = a.GetAttributeValue<bool>("spk_pushonlinemeeting"),
                        IsAdvancePayment = a.GetAttributeValue<bool>("spk_ar"),
                        StateCode = (StateCode)a.GetAttributeValue<OptionSetValue>("statecode").Value,
                        Description = a.GetAttributeValue<string>("spk_description"),
                        Expenseoption = a.GetAttributeValue<OptionSetValue>("spk_expenseoption")?.Value,
                        SoiVerifyType = (SoiVerifyType?)a.GetAttributeValue<OptionSetValue>("spk_soiverifytype")?.Value
                    };

                    return new StackExchange.Redis.HashEntry($"{data.ConsumeCategoryId}\\{data.Id}", JsonConvert.SerializeObject(data));
                }).ToArray();

                _redisRepository.Database.HashSet(RedisKey.BaseData_CostNature, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_CostNature, $"*{pattern}*");

            return entries.Select(a => JsonConvert.DeserializeObject<CostNatureDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode);
        }

        /// <summary>
        /// 获取机构和费用性质的关系
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<IEnumerable<OrgCostNatureRelationDto>> GetOrgCostNatureRelationAsync(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_OrgCostNatureRelation))
            {
                var query = new QueryExpression("spk_organizational_costnature");
                query.ColumnSet.AddColumns("spk_costnatureid", "spk_organizationalmasterdataid", "statecode");
                var entities = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);

                entries = entities.Select(a =>
                {
                    var data = new OrgCostNatureRelationDto
                    {
                        Id = a.Id,
                        OrgId = a.GetAttributeValue<EntityReference>("spk_organizationalmasterdataid")?.Id,
                        CostNatureId = a.GetAttributeValue<EntityReference>("spk_costnatureid")?.Id,
                        StateCode = (StateCode)a.GetAttributeValue<OptionSetValue>("statecode").Value
                    };

                    return new StackExchange.Redis.HashEntry($"{data.Id}\\{data.OrgId}\\{data.CostNatureId}", JsonConvert.SerializeObject(data));
                }).ToArray();

                _redisRepository.Database.HashSet(RedisKey.BaseData_OrgCostNatureRelation, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_OrgCostNatureRelation, $"*{pattern}*");

            return entries.Select(a => JsonConvert.DeserializeObject<OrgCostNatureRelationDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode);
        }

        /// <summary>
        /// 获取BU、成本中心、费用性质映射关系
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<IEnumerable<CoaMappingRuleDto>> GetCoaMappingRuleAsync(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_CoaMappingRule))
            {
                //BU、成本中心、费用性质关系映射表
                var query = new QueryExpression("spk_coamappingrule");
                query.ColumnSet.AddColumns("spk_bucode", "spk_costcentercode", "spk_expensenaturecode", "statecode");
                var entities = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);

                entries = entities.Select(a =>
                {
                    var data = new CoaMappingRuleDto
                    {
                        Id = a.Id,
                        BuCode = a.GetAttributeValue<string>("spk_bucode"),
                        CostcenterCode = a.GetAttributeValue<string>("spk_costcentercode"),
                        CostNatureCode = a.GetAttributeValue<string>("spk_expensenaturecode"),
                        StateCode = (StateCode)a.GetAttributeValue<OptionSetValue>("statecode").Value
                    };

                    return new StackExchange.Redis.HashEntry($"{data.Id}\\{data.BuCode}\\{data.CostcenterCode}\\{data.CostNatureCode}", JsonConvert.SerializeObject(data));
                }).ToArray();

                _redisRepository.Database.HashSet(RedisKey.BaseData_CoaMappingRule, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_CoaMappingRule, $"*{pattern}*", 50000);

            return entries.Select(a => JsonConvert.DeserializeObject<CoaMappingRuleDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode);
        }

        /// <summary>
        /// 获取BPCS与PP讲者类型映射关系
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<IEnumerable<VendorTypeMappingDto>> GetVendorTypeCfgAsync(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_VendorTypeCfg))
            {
                //讲者类型关系映射表
                var query = new QueryExpression("spk_vendertype");
                query.ColumnSet.AddColumns("spk_name", "spk_code", "spk_bpcscodermb", "spk_bpcscodenonrmb", "spk_paymentterms", "spk_dpocategory", "statecode");


                LinkEntity linkDic = query.AddLink("spk_dictionary", "spk_dpocategory", "spk_dictionaryid", JoinOperator.LeftOuter);
                linkDic.Columns.AddColumns("spk_dictionaryid", "spk_code");
                linkDic.EntityAlias = "lines1";

                var entities = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);

                entries = entities.Select(a =>
                {
                    var data = new VendorTypeMappingDto
                    {
                        Id = a.Id,
                        VendorType = a.GetAttributeValue<int>("spk_code").ToString(),
                        BPCSTypeRMB = a.GetAttributeValue<string>("spk_bpcscodermb"),
                        BPCSTypeNoneRMB = a.GetAttributeValue<string>("spk_bpcscodenonrmb"),
                        PaymentTerms = a.GetAttributeValue<string>("spk_paymentterms"),
                        StateCode = (StateCode)a.GetAttributeValue<OptionSetValue>("statecode").Value,
                        DpoCategoryCode = a.GetAttributeValue<AliasedValue>("lines1.spk_code")?.Value?.ToString(),
                    };

                    return new HashEntry($"{data.Id}", JsonConvert.SerializeObject(data));
                }).ToArray();

                _redisRepository.Database.HashSet(RedisKey.BaseData_VendorTypeCfg, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_VendorTypeCfg, $"*{pattern}*");

            return entries.Select(a => JsonConvert.DeserializeObject<VendorTypeMappingDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode);
        }

        /// <summary>
        /// 获取BU编码配置
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<IEnumerable<BuCodingCfgDto>> GetBuCodingCfgAsync(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_BuCodingCfg))
            {
                //BU、成本中心、费用性质关系映射表
                var query = new QueryExpression("spk_bucodingconfiguration");
                query.ColumnSet.AddColumns("spk_code", "spk_buname", "spk_pushspecialcodes", "statecode");
                var entities = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);

                entries = entities.Select(a =>
                {
                    var data = new BuCodingCfgDto
                    {
                        Id = a.Id,
                        BuId = a.GetAttributeValue<EntityReference>("spk_buname").Id,
                        BuCode = a.GetAttributeValue<string>("spk_code"),
                        PushSpecialCode = a.GetAttributeValue<string>("spk_pushspecialcodes"),
                        StateCode = (StateCode)a.GetAttributeValue<OptionSetValue>("statecode").Value
                    };

                    return new StackExchange.Redis.HashEntry($"{data.Id}\\{data.BuId}", JsonConvert.SerializeObject(data));
                }).ToArray();

                _redisRepository.Database.HashSet(RedisKey.BaseData_BuCodingCfg, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_BuCodingCfg, $"*{pattern}*");

            return entries.Select(a => JsonConvert.DeserializeObject<BuCodingCfgDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode);
        }

        /// <summary>
        /// 获取计酬主数据
        /// </summary>
        /// <param name="pattern">The pattern.</param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<IEnumerable<CompensationDto>> GetCompensationAsync(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            var key = RedisKey.BaseData_Compensation;
            if (!_redisRepository.Database.KeyExists(key))
            {
                var query = new QueryExpression("spk_compensationdataconfig");
                query.ColumnSet.AddColumns("spk_name", "spk_consume", "spk_costnature", "spk_identity", "spk_unit", "spk_calculateunit", "statecode");
                var entities = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);

                var attributeRequest = new RetrieveAttributeRequest { EntityLogicalName = "spk_compensationdataconfig", LogicalName = "spk_calculateunit" };
                var attributeResponse = (RetrieveAttributeResponse)await _dataverseRepository.DataverseClient.ExecuteAsync(attributeRequest);
                var picklistMetadata = (PicklistAttributeMetadata)attributeResponse.AttributeMetadata;

                entries = entities.Select(a =>
                {
                    var data = new CompensationDto
                    {
                        Id = a.Id,
                        Name = a.GetAttributeValue<string>("spk_name"),
                        Consume = a.GetAttributeValue<EntityReference>("spk_consume").Id,
                        Costnature = a.GetAttributeValue<EntityReference>("spk_costnature").Id,
                        Identity = a.GetAttributeValue<EntityReference>("spk_identity").Id,
                        Unit = a.GetAttributeValue<OptionSetValue>("spk_calculateunit")?.Value,
                        StateCode = (StateCode)a.GetAttributeValue<OptionSetValue>("statecode").Value
                    };
                    data.UnitName = picklistMetadata.OptionSet.Options.FirstOrDefault(a => a.Value == data.Unit)?.Label.UserLocalizedLabel.Label;

                    return new HashEntry($"{data.Id}", JsonConvert.SerializeObject(data));
                });

                _redisRepository.Database.HashSet(key, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(key, $"*{pattern}*");

            return entries.Select(a => JsonConvert.DeserializeObject<CompensationDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode);
        }

        /// <summary>
        /// 获取城市主数据spk_citymasterdata
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<IEnumerable<CityMasterDataDto>> GetSpecialCitiesAsync(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_SpecialCity))
            {
                var query = new QueryExpression("spk_citymasterdata");
                query.ColumnSet.AddColumns("spk_name", "spk_citynumber", "statecode");
                var entities = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);

                entries = entities.Select(a =>
                {
                    var data = new CityMasterDataDto
                    {
                        Id = a.Id,
                        Name = a.GetAttributeValue<string>("spk_name"),
                        CityCode = a.GetAttributeValue<string>("spk_citynumber"),
                        CityNameCode = $"{a.GetAttributeValue<string>("spk_name")}-{a.GetAttributeValue<string>("spk_citynumber")}",
                        StateCode = (StateCode)a.GetAttributeValue<OptionSetValue>("statecode").Value
                    };
                    return new HashEntry($"{data.Id}", JsonConvert.SerializeObject(data));
                }).ToArray();

                _redisRepository.Database.HashSet(RedisKey.BaseData_SpecialCity, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_SpecialCity, $"*{pattern}*");

            return entries.Select(a => JsonConvert.DeserializeObject<CityMasterDataDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode);
        }

        /// <summary>
        /// 获取机构和城市主数据的关系
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<IEnumerable<OrgSpecialCityRelationDto>> GetOrgSpecialCityRelationAsync(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_OrgSpecialCityRelation))
            {
                var query = new QueryExpression("spk_organizationalmasterdata_citymasterdata");
                query.ColumnSet.AddColumns("spk_citymasterdataid", "spk_organizationalmasterdataid", "spk_bu", "statecode");
                var entities = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);

                entries = entities.Select(a =>
                {
                    var data = new OrgSpecialCityRelationDto
                    {
                        Id = a.Id,
                        OrgId = a.GetAttributeValue<EntityReference>("spk_organizationalmasterdataid")?.Id,
                        CityId = a.GetAttributeValue<EntityReference>("spk_citymasterdataid")?.Id,
                        IsRestricted = a.GetAttributeValue<bool?>("spk_bu"),
                        StateCode = (StateCode)a.GetAttributeValue<OptionSetValue>("statecode").Value
                    };
                    return new HashEntry($"{data.Id}\\{data.OrgId}\\{data.CityId}\\{data.IsRestricted == true}", JsonConvert.SerializeObject(data));
                }).ToArray();

                _redisRepository.Database.HashSet(RedisKey.BaseData_OrgSpecialCityRelation, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_OrgSpecialCityRelation, $"*{pattern}*");

            return entries.Select(a => JsonConvert.DeserializeObject<OrgSpecialCityRelationDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode);
        }

        /// <summary>
        /// 获取Division/BU
        /// </summary>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<IEnumerable<DepartmentDto>> GetDivisions(StateCode? stateCode = StateCode.Active)
        {
            var divisions = await GetOrganizations(stateCode: stateCode);
            return divisions.Where(a => a.OrganizationType == Organization.OrganizationType.Bu);
        }

        /// <summary>
        /// 获取货币配置信息
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<IEnumerable<CurrencyConfigDto>> GetCurrencyConfig(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_CurrencyConfig))
            {
                var query = new QueryExpression("spk_currencyconfig");
                query.ColumnSet.AddColumns("spk_currency", "spk_rmbplanrate", "spk_currencysymbol", "statecode");
                var entities = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);

                entries = entities.Select(a =>
                {
                    var data = new CurrencyConfigDto
                    {
                        Id = a.Id,
                        CurrencyId = a.GetAttributeValue<EntityReference>("spk_currency")?.Id,
                        CurrencyCode = a.GetAttributeValue<EntityReference>("spk_currency")?.Name,
                        PlanRate = a.GetAttributeValue<decimal>("spk_rmbplanrate"),
                        CurrencySymbol = a.GetAttributeValue<string>("spk_currencysymbol"),
                        StateCode = (StateCode)a.GetAttributeValue<OptionSetValue>("statecode").Value
                    };
                    return new HashEntry($"{data.Id}\\{data.CurrencyCode}", JsonConvert.SerializeObject(data));
                }).ToArray();

                _redisRepository.Database.HashSet(RedisKey.BaseData_CurrencyConfig, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_CurrencyConfig, $"*{pattern}*");

            return entries.Select(a => JsonConvert.DeserializeObject<CurrencyConfigDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode);
        }

        #region 业务系统医院主数据
        /// <summary>
        /// 获取EPD业务系统医院数据
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="pageSize"></param>
        /// <param name="count"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<List<BUHospitalDto>> GetEPDBUHospitals(string pattern = null, int pageSize = 10000, int count = int.MaxValue, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_BUHospital))
            {
                var ppResult = await GetAllBUHospitalsFromPP(stateCode);
                entries = ppResult.Select(a =>
                {
                    return new HashEntry($"{a.Id}\\{a.HospitalCode}\\{a.Name}\\{a.VeevaID}\\{a.SourceSystem}", JsonConvert.SerializeObject(a));
                });
                _redisRepository.Database.HashSet(RedisKey.BaseData_BUHospital, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_BUHospital, $"{pattern}\\\\{BUHospitalConst.EPDHospitalSourceSystem}", pageSize);

            return entries.Select(a => JsonConvert.DeserializeObject<BUHospitalDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode).Take(count).ToList();
        }

        /// <summary>
        /// 获取所有业务系统医院主数据
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<List<BUHospitalDto>> GetAllBUHospitals(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_BUHospital))
            {
                var ppResult = await GetAllBUHospitalsFromPP(stateCode);
                entries = ppResult.Select(a =>
                {
                    return new HashEntry($"{a.Id}\\{a.HospitalCode}\\{a.Name}\\{a.VeevaID}\\{a.SourceSystem}", JsonConvert.SerializeObject(a));
                });
                _redisRepository.Database.HashSet(RedisKey.BaseData_BUHospital, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_BUHospital, $"*{pattern}*");

            return entries.Select(a => JsonConvert.DeserializeObject<BUHospitalDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode).ToList();
        }

        /// <summary>
        /// 获取所有业务系统医院主数据，直接从PP获取，不读缓存
        /// </summary>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<List<BUHospitalDto>> GetAllBUHospitalsFromPP(StateCode? stateCode = StateCode.Active)
        {
            var query = new QueryExpression("spk_buhospitalmasterdata");
            query.ColumnSet.AddColumns("spk_name", "statecode", "spk_hospitalcode", "spk_veevaid", "spk_sourcesystem");

            var hospitals = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);
            var result = hospitals.Select(a =>
             {
                 return new BUHospitalDto
                 {
                     Id = a.Id,
                     Name = a.GetAttributeValue<string>("spk_name"),
                     HospitalCode = a.GetAttributeValue<string>("spk_hospitalcode"),
                     VeevaID = a.GetAttributeValue<string>("spk_veevaid"),
                     SourceSystem = a.GetAttributeValue<string>("spk_sourcesystem"),
                     StateCode = (StateCode)a.GetAttributeValue<OptionSetValue>("statecode").Value
                 };
             }).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode).ToList();
            return result;
        }

        #endregion

        #endregion

        #region 字典数据

        /// <summary>
        /// 获取字典数据
        /// </summary>
        /// <param name="type"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<IEnumerable<DictionaryDto>> GetDictionariesAsync(string type = null, StateCode? stateCode = StateCode.Active)
        {
            var redisRepository = LazyServiceProvider.LazyGetService<IRedisRepository>();
            var caches = redisRepository.Database.HashScan(RedisKey.DictData_All, $@"*\{type}\*");
            if (!caches.Any())
            {
                var query = new QueryExpression("spk_dictionary");
                query.ColumnSet.AddColumns("spk_name", "spk_code", "spk_type", "spk_parentcode", "statecode");
                if (!string.IsNullOrEmpty(type))
                    query.Criteria.AddCondition(new ConditionExpression("spk_parentcode", ConditionOperator.Equal, type));
                var dictionaries = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);
                caches = dictionaries
                    .Select(a =>
                    {
                        var dict = new DictionaryDto
                        {
                            Id = a.Id,
                            Type = a.GetAttributeValue<string>("spk_type"),
                            Code = a.GetAttributeValue<string>("spk_code"),
                            ParentCode = a.GetAttributeValue<string>("spk_parentcode"),
                            Name = a.GetAttributeValue<string>("spk_name"),
                            StateCode = (StateCode)a.GetAttributeValue<OptionSetValue>("statecode").Value
                        };
                        return new StackExchange.Redis.HashEntry(@$"{dict.Id}\{dict.ParentCode}\{dict.Type}\{dict.Code}\{dict.Name}", JsonConvert.SerializeObject(dict));
                    });

                redisRepository.Database.HashSet(RedisKey.DictData_All, caches.ToArray());
            }

            return caches.Select(a => JsonConvert.DeserializeObject<DictionaryDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode);
        }

        #endregion

        #region 审批流

        #region OEC审批

        public async Task<IEnumerable<WorkflowInstanceDto>> GetApplicationRecordAsync(string formId)
        {
            var queryInstance = new QueryExpression("spk_workflowinstance");
            queryInstance.ColumnSet.AddColumns("spk_businessformid", "spk_name", "spk_submitter", "createdon", "spk_workflowyype", "spk_workflowinstanceid");
            queryInstance.Criteria.AddCondition(new ConditionExpression("statecode", ConditionOperator.Equal, 0));
            queryInstance.Criteria.AddCondition(new ConditionExpression("spk_businessformid", ConditionOperator.Equal, formId));

            var entitieInstances = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(queryInstance);
            var instanceResults = entitieInstances.Select(p => new
            {
                Submitter = p.GetAttributeValue<EntityReference>("spk_submitter"),
                FormId = formId,
                WorkflowType = p.GetAttributeValue<EntityReference>("spk_workflowyype"),
                SubmitTime = p.GetAttributeValue<DateTime>("createdon"),
                Id = p.GetAttributeValue<Guid>("id"),
                WorkflowTasks = new List<WorkflowTaskDto>()
            });

            var instanceResultsRes = instanceResults.Select(p => new WorkflowInstanceDto
            {
                Id = p.Id,
                SubmitterId = p.Submitter.Id,
                FormId = Guid.Parse(p.FormId),
                WorkflowType = p.WorkflowType.Name,
                SubmitTime = p.SubmitTime
            });

            var queryTask = new QueryExpression("spk_workflowtask");
            queryTask.ColumnSet.AddColumns("spk_businessformid", "spk_name", "spk_approvalstatus", "spk_remark", "spk_businessformid", "spk_workflowstep", "createdon", "spk_approver", "spk_workflowinstance");
            queryTask.Criteria.AddCondition(new ConditionExpression("statecode", ConditionOperator.Equal, 0));
            queryTask.Criteria.AddCondition(new ConditionExpression("spk_businessformid", ConditionOperator.Equal, formId));

            var entitieTasks = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(queryTask);
            var taskResults = entitieTasks.Select(p => new
            {
                FormId = formId,
                Instance = p.GetAttributeValue<EntityReference>("spk_workflowinstance"),
                Name = p.GetAttributeValue<string>("spk_name"),
                Approvalstatus = p.GetAttributeValue<OptionSetValue>("spk_approvalstatus"),
                Remark = p.GetAttributeValue<string>("spk_remark"),
                WorkflowStep = p.GetAttributeValue<EntityReference>("spk_workflowstep"),
                ApprovalTime = p.GetAttributeValue<DateTime>("createdon"),
                Approvaler = p.GetAttributeValue<EntityReference>("spk_approver")
            });

            var taskRes = taskResults.Select(p => new WorkflowTaskDto
            {
                InstanceId = p.Instance?.Id,
                FormId = Guid.Parse(p.FormId),
                Name = p.Name,
                Remark = p.Remark,
                WorkStep = p.WorkflowStep?.Name,
                CreatedTime = p.ApprovalTime,
                ApprovalStatus = p.Approvalstatus.Value.ToString(),
                ApprovalId = p.Approvaler?.Id
            });

            foreach (var task in taskRes)
            {
                switch (task.ApprovalStatus)
                {
                    case "100000000":
                        task.Status = Enums.ApprovalStatus.PendingForApproval;
                        break;
                    case "100000001":
                        task.Status = Enums.ApprovalStatus.Approved;
                        break;
                    case "100000002":
                        task.Status = Enums.ApprovalStatus.Rejected;
                        break;
                    case "100000003":
                        task.Status = Enums.ApprovalStatus.Withdraw;
                        break;

                    default:
                        break;
                }
            }

            foreach (var res in instanceResultsRes)
            {
                res.WorkflowTasks = taskRes.Where(a => a.InstanceId == res.Id);
            }

            return instanceResultsRes;
        }
        #endregion

        /// <summary>
        /// 获取审批人节点
        /// </summary>
        /// <param name="formId">审批人ID</param>
        /// <param name="workflowType">传入所要查询的审批类型，减少查询数量</param>
        /// <returns></returns>
        public async Task<IEnumerable<WorkflowTaskDto>> GetApprovelTaskAsync(string userId, WorkflowTypeName[] workflowType, ProcessingStatus processingStatus = ProcessingStatus.Progressing)
        {
            bool? isIncludeOrSigned = null;
            int[] destTaskApprovalStatus = [];
            switch (processingStatus)
            {
                case ProcessingStatus.PendingProcessing:
                case ProcessingStatus.Progressing:
                    isIncludeOrSigned = null;
                    destTaskApprovalStatus = [100000000];
                    break;
                case ProcessingStatus.Completed:
                    isIncludeOrSigned = false;
                    destTaskApprovalStatus = [100000001, 100000002, 100000003];
                    break;
                default:
                    break;
            }

            try
            {
                //查询task
                var queryTask = new QueryExpression("spk_workflowtask");
                queryTask.ColumnSet.AddColumns("spk_businessformid", "spk_name", "spk_approvalstatus", "spk_remark", "spk_businessformid", "spk_workflowstep", "createdon", "spk_approver", "spk_workflowinstance");
                queryTask.Criteria.AddCondition(new ConditionExpression("statecode", ConditionOperator.Equal, 0));
                queryTask.Criteria.AddCondition(new ConditionExpression("spk_approver", ConditionOperator.Equal, userId));

                if (isIncludeOrSigned.HasValue)
                    queryTask.Criteria.AddCondition(new ConditionExpression("spk_isorsigned", ConditionOperator.Equal, isIncludeOrSigned.Value));

                if (destTaskApprovalStatus.Length > 0)
                    queryTask.Criteria.AddCondition(new ConditionExpression("spk_approvalstatus", ConditionOperator.In, destTaskApprovalStatus));

                LinkEntity linkStep = queryTask.AddLink("spk_workflowstep", "spk_workflowstep", "spk_workflowstepid");
                linkStep.Columns.AddColumns("spk_step");
                linkStep.EntityAlias = "step";

                LinkEntity linkType = linkStep.AddLink("spk_workflowtype", "spk_workflowtype", "spk_workflowtypeid");
                linkType.EntityAlias = "type";
                if (workflowType.Length > 1)
                    linkType.LinkCriteria.AddCondition(new ConditionExpression("spk_workflowtypename", ConditionOperator.In, workflowType.Select(s => (int)s).ToArray()));
                else if (workflowType.Length > 0)
                    linkType.LinkCriteria.AddCondition(new ConditionExpression("spk_workflowtypename", ConditionOperator.Equal, (int)workflowType.First()));

                var entitieTasks = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(queryTask);

                var taskRes = entitieTasks.Select(p =>
                {
                    var formId = p.GetAttributeValue<string>("spk_businessformid");
                    var approvalStatusCode = p.GetAttributeValue<OptionSetValue>("spk_approvalstatus").Value.ToString();
                    ApprovalStatus approvalStatus = Enums.ApprovalStatus.PendingForApproval;
                    switch (approvalStatusCode)
                    {
                        case "100000000":
                            approvalStatus = Enums.ApprovalStatus.PendingForApproval;
                            break;
                        case "100000001":
                            approvalStatus = Enums.ApprovalStatus.Approved;
                            break;
                        case "100000002":
                            approvalStatus = Enums.ApprovalStatus.Rejected;
                            break;
                        case "100000003":
                            approvalStatus = Enums.ApprovalStatus.Withdraw;
                            break;
                    }

                    return new WorkflowTaskDto
                    {
                        TaskId = p.Id,
                        Step = p.GetAttributeValue<AliasedValue>($"{linkStep.EntityAlias}.spk_step")?.Value.ToString(),
                        FormId = Guid.Parse(formId),
                        InstanceId = p.GetAttributeValue<EntityReference>("spk_workflowinstance")?.Id,
                        Name = p.GetAttributeValue<string>("spk_name"),
                        ApprovalStatus = p.GetAttributeValue<OptionSetValue>("spk_approvalstatus").Value.ToString(),
                        Status = approvalStatus,
                        Remark = p.GetAttributeValue<string>("spk_remark"),
                        WorkStep = p.GetAttributeValue<EntityReference>("spk_workflowstep")?.Name,
                        CreatedTime = p.GetAttributeValue<DateTime>("createdon").ToLocalTime(),
                        ApprovalId = p.GetAttributeValue<EntityReference>("spk_approver")?.Id
                    };
                });

                return taskRes;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        /// <summary>
        /// 获取审批人节点
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="workflowTypes"></param>
        /// <param name="approvalPowerAppStatuses"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<WorkflowTaskDto>> GetApprovalTaskAsync(string userId, IEnumerable<WorkflowTypeName> workflowTypes, IEnumerable<ApprovalPowerAppStatus> approvalPowerAppStatuses, int pageIndex = 1, int pageSize = 10)
        {
            //查询task
            var queryTask = new QueryExpression("spk_workflowtask");
            queryTask.ColumnSet.AddColumns("spk_businessformid", "spk_name", "spk_approvalstatus", "spk_remark", "spk_businessformid", "spk_workflowstep", "createdon", "spk_approver", "spk_workflowinstance");
            queryTask.Criteria.AddCondition(new ConditionExpression("statecode", ConditionOperator.Equal, 0));
            queryTask.Criteria.AddCondition(new ConditionExpression("spk_approver", ConditionOperator.Equal, userId));
            queryTask.Criteria.AddCondition(new ConditionExpression("spk_approvalstatus", ConditionOperator.In, approvalPowerAppStatuses.Select(a => (int)a).ToArray()));
            queryTask.AddOrder("createdon", OrderType.Descending);

            //查询flow instance
            var queryInstance = queryTask.AddLink("spk_workflowinstance", "spk_workflowinstance", "spk_workflowinstanceid");
            queryInstance.Columns.AddColumns("spk_workflowyype");
            queryInstance.EntityAlias = "flowInstance";

            //获取workflowType
            var queryType = queryInstance.AddLink("spk_workflowtype", "spk_workflowyype", "spk_workflowtypeid");
            queryType.Columns.AddColumns("spk_workflowtypeid", "spk_name");
            queryType.LinkCriteria.AddCondition(new ConditionExpression("statecode", ConditionOperator.Equal, 0));
            queryType.LinkCriteria.AddCondition(new ConditionExpression("spk_workflowtypename", ConditionOperator.In, workflowTypes.Select(c => (int)c).ToArray()));

            //获取workflowStep
            var queryStep = queryTask.AddLink("spk_workflowstep", "spk_workflowstep", "spk_workflowstepid");
            queryStep.EntityAlias = "step";
            queryStep.Columns.AddColumns("spk_workflowstepid", "spk_name", "spk_step");
            queryStep.LinkCriteria.AddCondition(new ConditionExpression("statecode", ConditionOperator.Equal, 0));

            queryTask.PageInfo.PageNumber = pageIndex;
            queryTask.PageInfo.Count = pageSize;
            queryTask.PageInfo.ReturnTotalRecordCount = true;

            var entityCollection = await _dataverseRepository.DataverseClient.RetrieveMultipleAsync(queryTask);

            var datas = entityCollection.Entities.Select(p => new WorkflowTaskDto
            {
                TaskId = p.Id,
                Step = p.GetAttributeValue<AliasedValue>("step.spk_name").Value?.ToString(),
                StepNumber = p.GetAttributeValue<AliasedValue>("step.spk_step").Value?.ToString(),
                FormId = Guid.Parse(p.GetAttributeValue<string>("spk_businessformid")),
                InstanceId = p.GetAttributeValue<EntityReference>("spk_workflowinstance")?.Id,
                Name = p.GetAttributeValue<string>("spk_name"),
                Remark = p.GetAttributeValue<string>("spk_remark"),
                WorkStep = p.GetAttributeValue<EntityReference>("spk_workflowstep")?.Name,
                CreatedTime = p.GetAttributeValue<DateTime>("createdon"),
                ApprovalId = p.GetAttributeValue<EntityReference>("spk_approver")?.Id,
                ApprovalPowerAppStatus = (ApprovalPowerAppStatus)p.GetAttributeValue<OptionSetValue>("spk_approvalstatus").Value
            }).ToArray();

            return new PagedResultDto<WorkflowTaskDto>(entityCollection.TotalRecordCount, datas);
        }

        /// <summary>
        /// 获取审批流Task
        /// </summary>
        /// <param name="businessId"></param>
        /// <param name="taskId"></param>
        /// <returns></returns>
        public async Task<IEnumerable<WorkflowTaskDto>> GetWorkflowTaskAsync(Guid businessId, Guid? taskId = null)
        {
            //查询task
            var queryTask = new QueryExpression("spk_workflowtask");
            queryTask.ColumnSet.AddColumns("spk_businessformid", "spk_name", "spk_approvalstatus", "spk_remark", "spk_businessformid", "spk_workflowstep", "createdon", "spk_approver", "spk_workflowinstance");
            queryTask.Criteria.AddCondition(new ConditionExpression("spk_businessformid", ConditionOperator.Equal, businessId.ToString()));
            if (taskId.HasValue)
                queryTask.Criteria.AddCondition(new ConditionExpression("spk_workflowtaskid", ConditionOperator.Equal, taskId));

            //查询flow instance
            var queryInstance = queryTask.AddLink("spk_workflowinstance", "spk_workflowinstance", "spk_workflowinstanceid");
            queryInstance.Columns.AddColumns("spk_workflowyype");
            queryInstance.EntityAlias = "flowInstance";

            //获取workflowType
            var queryType = queryInstance.AddLink("spk_workflowtype", "spk_workflowyype", "spk_workflowtypeid");
            queryType.Columns.AddColumns("spk_workflowtypeid", "spk_name");

            //获取workflowStep
            var queryStep = queryTask.AddLink("spk_workflowstep", "spk_workflowstep", "spk_workflowstepid");
            queryStep.EntityAlias = "step";
            queryStep.Columns.AddColumns("spk_workflowstepid", "spk_name", "spk_step");

            var entityCollection = await _dataverseRepository.DataverseClient.RetrieveMultipleAsync(queryTask);

            var data = entityCollection.Entities.Select(p => new WorkflowTaskDto
            {
                TaskId = p.Id,
                Step = p.GetAttributeValue<AliasedValue>("step.spk_name").Value?.ToString(),
                StepNumber = p.GetAttributeValue<AliasedValue>("step.spk_step").Value?.ToString(),
                FormId = Guid.Parse(p.GetAttributeValue<string>("spk_businessformid")),
                InstanceId = p.GetAttributeValue<EntityReference>("spk_workflowinstance")?.Id,
                Name = p.GetAttributeValue<string>("spk_name"),
                Remark = p.GetAttributeValue<string>("spk_remark"),
                WorkStep = p.GetAttributeValue<EntityReference>("spk_workflowstep")?.Name,
                CreatedTime = p.GetAttributeValue<DateTime>("createdon"),
                ApprovalId = p.GetAttributeValue<EntityReference>("spk_approver")?.Id,
                ApprovalPowerAppStatus = (ApprovalPowerAppStatus)p.GetAttributeValue<OptionSetValue>("spk_approvalstatus").Value
            });

            return data;
        }

        /// <summary>
        /// 获取审批矩阵配置
        /// </summary>
        /// <returns></returns>
        public async Task<IEnumerable<ExpenseApprovalMatrixCfgItemDto>> GetExpenseApprovalMatrixConfigurationItemAsync()
        {
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_ExpenseApprovalMatrixCfgItem))
            {
                var query = new QueryExpression("spk_expenseapprovalmatrixconfigurationitem");
                query.ColumnSet.AddColumns("spk_bu", "spk_approvalnumber", "spk_natureofexpenses", "spk_level0");
                query.Criteria.AddCondition(new ConditionExpression("statecode", ConditionOperator.Equal, 0));
                var entities = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);
                var datas = entities.Select(x => new ExpenseApprovalMatrixCfgItemDto
                {
                    BuId = x.GetAttributeValue<EntityReference>("spk_bu")?.Id,
                    ApprovalNumber = x.GetAttributeValue<string>("spk_approvalnumber"),
                    CostNatureId = x.GetAttributeValue<EntityReference>("spk_natureofexpenses")?.Id,
                    Level0Amount = x.GetAttributeValue<Money>("spk_level0").Value
                });
                //缓存1小时
                _redisRepository.Database.StringSet(RedisKey.BaseData_ExpenseApprovalMatrixCfgItem, JsonConvert.SerializeObject(datas), new TimeSpan(1, 0, 0));
            }
            var json = _redisRepository.Database.StringGet(RedisKey.BaseData_ExpenseApprovalMatrixCfgItem);
            return JsonConvert.DeserializeObject<IEnumerable<ExpenseApprovalMatrixCfgItemDto>>(json);
        }

        /// <summary>
        /// 获取财务审批金额矩阵
        /// </summary>
        /// <returns></returns>
        public async Task<IEnumerable<FinancialApprovalAmountMatrixDto>> GetFinancialApprovalAmountMatrixAsync()
        {
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_FinancialApprovalAmountMatrix))
            {
                var query = new QueryExpression("spk_financialapprovalamountmatrix");
                query.ColumnSet.AddColumns("spk_bu", "spk_name", "spk_procurementfinance");
                query.Criteria.AddCondition(new ConditionExpression("statecode", ConditionOperator.Equal, 0));
                var entities = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);
                var datas = entities.Select(x => new FinancialApprovalAmountMatrixDto
                {
                    BuId = x.GetAttributeValue<EntityReference>("spk_bu")?.Id,
                    Level = int.TryParse(x.GetAttributeValue<string>("spk_name"), out int level) ? level : null,
                    Amount = x.GetAttributeValue<Money>("spk_procurementfinance").Value
                });
                //缓存1小时
                _redisRepository.Database.StringSet(RedisKey.BaseData_FinancialApprovalAmountMatrix, JsonConvert.SerializeObject(datas), new TimeSpan(1, 0, 0));
            }
            var json = _redisRepository.Database.StringGet(RedisKey.BaseData_FinancialApprovalAmountMatrix);
            return JsonConvert.DeserializeObject<IEnumerable<FinancialApprovalAmountMatrixDto>>(json);
        }

        #endregion

        /// <summary>
        /// 根据指定的组织向上找组织，直到分公司结束
        /// </summary>
        /// <returns></returns>
        async Task<IEnumerable<KeyValuePair<Guid, string>>> GetOrganizationListByDept(Guid deptOrg)
        {
            var orgs = await GetOrganizations();
            var org = orgs.FirstOrDefault(x => x.Id == deptOrg);
            List<KeyValuePair<Guid, string>> orgTree = new List<KeyValuePair<Guid, string>>();
            var findNext = true;
            DataverseEnums.Organization.OrganizationType[] validType = [DataverseEnums.Organization.OrganizationType.Dept, DataverseEnums.Organization.OrganizationType.Bu, DataverseEnums.Organization.OrganizationType.Affiliates];
            if (org.OrganizationType.HasValue && validType.Contains((DataverseEnums.Organization.OrganizationType)org.OrganizationType))
            {
                orgTree.Add(new KeyValuePair<Guid, string>(org.Id, org.DepartmentName));
                do
                {
                    if (org.ParentDepartment.HasValue && validType.Contains((DataverseEnums.Organization.OrganizationType)org.OrganizationType))
                    {
                        var parentOrg = orgs.FirstOrDefault(x => x.Id == (Guid)org.ParentDepartment);
                        if (parentOrg.OrganizationType.HasValue && validType.Contains((DataverseEnums.Organization.OrganizationType)parentOrg.OrganizationType))
                        {
                            orgTree.Add(new KeyValuePair<Guid, string>(parentOrg.Id, parentOrg.DepartmentName));
                            org = parentOrg;
                        }
                        else
                            findNext = false;
                    }
                    else
                        findNext = false;
                } while (findNext);
            }
            return orgTree;
        }

        /// <summary>
        /// 根据组织及岗位获取 岗位配置
        /// </summary>
        /// <param name="deptId"></param>
        /// <param name="positionType"></param>
        /// <returns></returns>
        public async Task<List<OrgAndStaffDto>> GetOrgAndStaffByFlowAsync(Guid deptId, PositionType positionType)
        {
            var orgTree = await GetOrganizationListByDept(deptId);
            var staffs = await GetStaffAndPositionAsync();
            var orgAndStaffs = new List<OrgAndStaffDto>();

            foreach (var org in orgTree)
            {
                var orgStaff = new OrgAndStaffDto();
                var staff = staffs.Where(a => a.OrganizationId == org.Key && a.PositionType == (int)positionType).ToList();
                orgStaff.OrganizationId = org.Key;
                orgStaff.Staffs = staff;
                orgAndStaffs.Add(orgStaff);
            }
            return orgAndStaffs;
        }

        public async Task<IEnumerable<KeyValuePair<Guid, string>>> GetStaffListByPositionAndFlow(Guid PrDept, PositionType positionType)
        {
            var orgTree = await GetOrganizationListByDept(PrDept);

            //获取指定岗位下的对应组织是orgTree范围内的员工
            var queryExt = new QueryExpression("spk_extensioncode");
            queryExt.ColumnSet.AddColumns("spk_staffname", "spk_organization", "spk_extensioncode2");
            queryExt.Criteria.AddCondition(new ConditionExpression("statecode", ConditionOperator.Equal, 0));
            queryExt.Criteria.AddCondition(new ConditionExpression("spk_organization", ConditionOperator.In, orgTree.Select(x => x.Key).ToArray()));

            var linkPosition = queryExt.AddLink("spk_positionmasterdata", "spk_position", "spk_positionmasterdataid");
            linkPosition.EntityAlias = "position";
            linkPosition.LinkCriteria.AddCondition(new ConditionExpression("spk_positiontypename", ConditionOperator.Equal, (int)positionType));

            var entitieExt = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(queryExt);

            var staffKv = entitieExt.Select(x => new KeyValuePair<Guid, string>(
                x.GetAttributeValue<EntityReference>("spk_staffname").Id,
                x.GetAttributeValue<EntityReference>("spk_staffname").Name)).Distinct();

            return staffKv;
            //return await Task.FromResult(kv);
        }

        /// <summary>
        /// 根据岗位获取员工数据
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<IEnumerable<StaffAndPositionDto>> GetStaffAndPositionAsync(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_StaffPositionRelation))
            {
                var query = new QueryExpression(DataverseEntitiesConsts.Extensioncode);
                query.ColumnSet.AddColumns("spk_staffname", "spk_organization", "spk_extensioncode2", "statecode", "spk_organization");

                var linkPosition = query.AddLink("spk_positionmasterdata", "spk_position", "spk_positionmasterdataid");
                linkPosition.EntityAlias = "position";
                linkPosition.Columns.AddColumn("spk_positiontypename");

                var entitieExt = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);

                entries = entitieExt.Select(a =>
                {
                    var positionType = (OptionSetValue)a.GetAttributeValue<AliasedValue>("position.spk_positiontypename")?.Value;
                    var data = new StaffAndPositionDto
                    {
                        Id = a.Id,
                        StaffId = a.GetAttributeValue<EntityReference>("spk_staffname")?.Id,
                        StaffName = a.GetAttributeValue<EntityReference>("spk_staffname").Name,
                        PositionType = positionType?.Value,
                        StateCode = (StateCode)a.GetAttributeValue<OptionSetValue>("statecode").Value,
                        OrganizationId = a.GetAttributeValue<EntityReference>("spk_organization")?.Id,
                        Extensioncode = a.GetAttributeValue<int?>("spk_extensioncode2")
                    };
                    return new HashEntry(@$"{data.Id}\\{data.StaffId}\\{data.PositionType}\\{data.OrganizationId}", JsonConvert.SerializeObject(data));
                }).ToArray();

                _redisRepository.Database.HashSet(RedisKey.BaseData_StaffPositionRelation, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_StaffPositionRelation, $"*{pattern}*");

            return entries.Select(a => JsonConvert.DeserializeObject<StaffAndPositionDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode);
        }

        /// <summary>
        /// 获取发票税率映射关系
        /// </summary>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<IEnumerable<InvoiceTaxRateMappDto>> GetInvoiceTaxRateMappAsync(StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_InvoiceTypeTaxRate))
            {
                var query = new QueryExpression(DataverseEntitiesConsts.InvoiceTypeTaxRateMapp);
                query.ColumnSet.AddColumns("spk_invoicetype", "spk_taxrate", "statecode");
                var departStaffRelations = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);

                entries = departStaffRelations.Select(a =>
                {
                    var data = new InvoiceTaxRateMappDto
                    {
                        Id = a.Id,
                        InvoiceTypeId = a.GetAttributeValue<EntityReference>("spk_invoicetype")?.Id,
                        InvoiceTypeName = a.GetAttributeValue<EntityReference>("spk_invoicetype")?.Name,
                        TaxRateId = a.GetAttributeValue<EntityReference>("spk_taxrate")?.Id,
                        TaxRateName = a.GetAttributeValue<EntityReference>("spk_taxratename")?.Name,
                        StateCode = (StateCode)a.GetAttributeValue<OptionSetValue>("statecode").Value
                    };

                    return new HashEntry($"{data.Id}\\{data.InvoiceTypeId}\\{data.InvoiceTypeName}\\{data.TaxRateId}\\{data.TaxRateName}", JsonConvert.SerializeObject(data));
                });

                _redisRepository.Database.HashSet(RedisKey.BaseData_InvoiceTypeTaxRate, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_InvoiceTypeTaxRate);

            return entries.Select(a => JsonConvert.DeserializeObject<InvoiceTaxRateMappDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode);
        }

        /// <summary>
        /// 获取紧急付款类型配置
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<IEnumerable<PturtTypeConfigDto>> GetPturtTypeConfigDtoAsync(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_PturtTypeConfig))
            {
                var query = new QueryExpression(DataverseEntitiesConsts.PturtTypeConfig);
                query.ColumnSet.AddColumns("spk_name", "spk_pturttype", "spk_code", "spk_pturttypename", "statecode", "spk_isattachmentequired", "spk_sortno");
                var departStaffRelations = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);
                entries = departStaffRelations.Select(a =>
                {
                    var pturtType = a.GetAttributeValue<OptionSetValue>("spk_pturttypename").Value;
                    var data = new PturtTypeConfigDto
                    {
                        Id = a.Id,
                        Name = a.GetAttributeValue<string>("spk_name"),
                        PturtType = a.GetAttributeValue<string>("spk_pturttype"),
                        Code = a.GetAttributeValue<string>("spk_code"),
                        SortNo = a.GetAttributeValue<int>("spk_sortno"),
                        PturtTypeName = (Enums.DataverseEnums.PturtTypeConfig.UrgentType)pturtType,
                        StateCode = (StateCode)a.GetAttributeValue<OptionSetValue>("statecode").Value,
                        IsAttachmentRequired = a.GetAttributeValue<bool>("spk_isattachmentequired")
                    };

                    return new HashEntry($"{data.Id}\\{data.Name}\\{data.PturtType}\\{data.Code}\\{data.PturtTypeName}\\{data.IsAttachmentRequired}", JsonConvert.SerializeObject(data));
                });
                _redisRepository.Database.HashSet(RedisKey.BaseData_PturtTypeConfig, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_PturtTypeConfig, $"*{pattern}*");

            return entries.Select(a => JsonConvert.DeserializeObject<PturtTypeConfigDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode);
        }

        /// <summary>
        /// 发起PO金额规则
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<IEnumerable<POInitiateAmountDto>> GetPoInitiateAmountAsync(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_POInitiateAmount))
            {
                var query = new QueryExpression(DataverseEntitiesConsts.POInitiateAmount);
                query.ColumnSet.AddColumns("spk_name", "spk_amountlimitusd", "statecode");
                var departStaffRelations = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);
                entries = departStaffRelations.Select(a =>
                {
                    var data = new POInitiateAmountDto
                    {
                        Id = a.Id,
                        Name = a.GetAttributeValue<string>("spk_name"),
                        AmountLimitUSD = a.GetAttributeValue<decimal>("spk_amountlimitusd"),
                        StateCode = (StateCode)a.GetAttributeValue<OptionSetValue>("statecode").Value
                    };

                    return new HashEntry($"{data.Id}\\{data.Name}\\{data.AmountLimitUSD}", JsonConvert.SerializeObject(data));
                });
                _redisRepository.Database.HashSet(RedisKey.BaseData_POInitiateAmount, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_POInitiateAmount, $"*{pattern}*");

            return entries.Select(a => JsonConvert.DeserializeObject<POInitiateAmountDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode);
        }

        /// <summary>
        /// 获取全部退单子原因
        /// </summary>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<IEnumerable<ReturnSubReasonDto>> GetReturnSubReasonAsync(StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_ReturnSubReason))
            {
                var query = new QueryExpression(DataverseEntitiesConsts.ReturnSubReason);
                query.ColumnSet.AddColumns("spk_returnsubreason", "spk_belongmainreason", "statecode");
                var departStaffRelations = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);
                entries = departStaffRelations.Select(a =>
                {
                    var data = new ReturnSubReasonDto
                    {
                        Id = a.Id,
                        ReturnSubreason = a.GetAttributeValue<string>("spk_returnsubreason"),
                        BelongMainReasonId = a.GetAttributeValue<EntityReference>("spk_belongmainreason").Id,
                        StateCode = (StateCode)a.GetAttributeValue<OptionSetValue>("statecode").Value
                    };
                    return new HashEntry($"{data.Id}", JsonConvert.SerializeObject(data));
                });
                _redisRepository.Database.HashSet(RedisKey.BaseData_ReturnSubReason, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_ReturnSubReason);

            return entries.Select(a => JsonConvert.DeserializeObject<ReturnSubReasonDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode);
        }

        /// <summary>
        /// 获取退单原因模板
        /// </summary>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<IEnumerable<PaReturnReasonDto>> GetPaReturnReasonAsync(StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_PaReturnReason))
            {
                var query = new QueryExpression(DataverseEntitiesConsts.PaReturnReason);
                query.ColumnSet.AddColumns("spk_mainreason", "spk_returntype", "statecode");
                var departStaffRelations = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);
                entries = departStaffRelations.Select(a =>
                {
                    var returnType = a.GetAttributeValue<OptionSetValue>("spk_returntype").Value;
                    var data = new PaReturnReasonDto
                    {
                        Id = a.Id,
                        MainReason = a.GetAttributeValue<string>("spk_mainreason"),
                        ReturnType = (Enums.DataverseEnums.Returnreason.ReturnType)returnType,
                        StateCode = (StateCode)a.GetAttributeValue<OptionSetValue>("statecode").Value
                    };
                    return new HashEntry($"{data.Id}\\{data.MainReason}", JsonConvert.SerializeObject(data));
                });
                _redisRepository.Database.HashSet(RedisKey.BaseData_PaReturnReason, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_PaReturnReason);

            return entries.Select(a => JsonConvert.DeserializeObject<PaReturnReasonDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode);
        }

        /// <summary>
        /// 获取成本中心BU映射关系
        /// </summary>
        /// <param name="pattern">The bu identifier.</param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        /// <exception cref="System.NotImplementedException"></exception>
        public async Task<IEnumerable<OrgCostcenterDto>> GetCostCenterOrgRelationsAsync(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_OrganizationalCostcenter))
            {
                var query = new QueryExpression("spk_organizational_costcenter");
                query.ColumnSet.AddColumns("spk_costcenter", "spk_organizational", "statecode");
                var departStaffRelations = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);
                entries = departStaffRelations.Select(x =>
                {
                    var data = new OrgCostcenterDto
                    {
                        Id = x.Id,
                        CostcenterId = x.GetAttributeValue<EntityReference>("spk_costcenter")?.Id,
                        CostcenterName = x.GetAttributeValue<EntityReference>("spk_costcenter").Name,
                        OrgId = x.GetAttributeValue<EntityReference>("spk_organizational")?.Id,
                        OrgName = x.GetAttributeValue<EntityReference>("spk_organizational").Name,
                        StateCode = (StateCode)x.GetAttributeValue<OptionSetValue>("statecode").Value
                    };
                    return new HashEntry($"{data.Id}\\{data.OrgId}\\{data.CostcenterId}", JsonConvert.SerializeObject(data));
                });
                _redisRepository.Database.HashSet(RedisKey.BaseData_OrganizationalCostcenter, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_OrganizationalCostcenter, $"*{pattern}*");
            return entries.Select(a => JsonConvert.DeserializeObject<OrgCostcenterDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode);
        }

        /// <summary>
        /// 获取大区BU映射关系
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        /// <exception cref="System.NotImplementedException"></exception>
        public async Task<IEnumerable<OrgDistrictDto>> GetOrgDistrictRelationsAsync(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_OrganizationalDistrict))
            {
                var query = new QueryExpression("spk_organizational_district");
                query.ColumnSet.AddColumns("spk_district", "spk_organizational", "statecode");
                var entities = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);
                entries = entities.Select(x =>
                {
                    var data = new OrgDistrictDto
                    {
                        Id = x.Id,
                        DistrictId = x.GetAttributeValue<EntityReference>("spk_district")?.Id,
                        DistrictName = x.GetAttributeValue<EntityReference>("spk_district").Name,
                        OrgId = x.GetAttributeValue<EntityReference>("spk_organizational")?.Id,
                        OrgName = x.GetAttributeValue<EntityReference>("spk_organizational").Name,
                        StateCode = (StateCode)x.GetAttributeValue<OptionSetValue>("statecode").Value
                    };
                    return new HashEntry($"{data.Id}\\{data.OrgId}\\{data.DistrictId}", JsonConvert.SerializeObject(data));
                });
                _redisRepository.Database.HashSet(RedisKey.BaseData_OrganizationalDistrict, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_OrganizationalDistrict, $"*{pattern}*");
            return entries.Select(a => JsonConvert.DeserializeObject<OrgDistrictDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode);
        }

        /// <summary>
        /// 获取城市公司映射关系
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        /// <exception cref="System.NotImplementedException"></exception>
        public async Task<IEnumerable<CityCompanyDto>> GetCityCompanyRelationsAsync(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_CityCompany))
            {
                var query = new QueryExpression("spk_companymasterdata_citymasterdata");
                query.ColumnSet.AddColumns("spk_company", "spk_city", "statecode");
                var entities = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);
                entries = entities.Select(x =>
                {
                    var data = new CityCompanyDto
                    {
                        Id = x.Id,
                        CompanyId = x.GetAttributeValue<EntityReference>("spk_company")?.Id,
                        CompanyName = x.GetAttributeValue<EntityReference>("spk_company")?.Name,
                        CityId = x.GetAttributeValue<EntityReference>("spk_city")?.Id,
                        CityName = x.GetAttributeValue<EntityReference>("spk_city")?.Name,
                        StateCode = (StateCode)x.GetAttributeValue<OptionSetValue>("statecode").Value
                    };
                    return new HashEntry($"{data.Id}\\{data.CompanyId}\\{data.CityId}", JsonConvert.SerializeObject(data));
                });
                _redisRepository.Database.HashSet(RedisKey.BaseData_CityCompany, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_CityCompany, $"*{pattern}*");
            return entries.Select(a => JsonConvert.DeserializeObject<CityCompanyDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode);
        }

        /// <summary>
        /// 获取采购推送人员配置
        /// </summary>
        /// <param name="pattern"></param>
        /// <returns></returns>
        public async Task<IEnumerable<ProcurementPushConfigDto>> GetProcurementPushConfigAsync(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_ProcurementPushConfig))
            {
                var query = new QueryExpression("spk_procurementpushconfig");
                query.ColumnSet.AddColumns("spk_company", "spk_employee", "spk_isadmin", "statecode");
                var entities = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);
                entries = entities.Select(x =>
                {
                    var data = new ProcurementPushConfigDto
                    {
                        Id = x.Id,
                        CompanyId = x.GetAttributeValue<EntityReference>("spk_company")?.Id,
                        CompanyName = x.GetAttributeValue<EntityReference>("spk_company")?.Name,
                        EmployeeId = x.GetAttributeValue<EntityReference>("spk_employee")?.Id,
                        EmployeeName = x.GetAttributeValue<EntityReference>("spk_employee")?.Name,
                        IsAdmin = x.GetAttributeValue<bool>("spk_isadmin"),
                        StateCode = (StateCode)x.GetAttributeValue<OptionSetValue>("statecode").Value
                    };
                    return new HashEntry($"{data.Id}\\{data.CompanyId}\\{data.EmployeeId}", JsonConvert.SerializeObject(data));
                });
                _redisRepository.Database.HashSet(RedisKey.BaseData_ProcurementPushConfig, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_ProcurementPushConfig, $"*{pattern}*");
            return entries.Select(a => JsonConvert.DeserializeObject<ProcurementPushConfigDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode);
        }

        /// <summary>
        /// 获取业务类型
        /// </summary>
        /// <param name="pattern">The pattern.</param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<IEnumerable<BusinessTypeDto>> GetBusinessTypeAsync(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_BusinessType))
            {
                var query = new QueryExpression("spk_businessflowtype");
                query.ColumnSet.AddColumns("spk_name", "spk_businesstypedescription", "spk_businesscategory", "spk_belongedsystem", "statecode");
                query.Criteria.AddCondition(new ConditionExpression("spk_belongedsystem", ConditionOperator.Equal, "NexBPM"));
                var entities = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);
                entries = entities.Select(x =>
                {
                    var data = new BusinessTypeDto
                    {
                        Id = x.Id,
                        Name = x.GetAttributeValue<string>("spk_name"),
                        BusinessCategory = (ResignationTransfer.TaskFormCategory)x.GetAttributeValue<OptionSetValue>("spk_businesscategory").Value,
                        BelongedSystem = x.GetAttributeValue<string>("spk_belongedsystem"),
                        StateCode = (StateCode)x.GetAttributeValue<OptionSetValue>("statecode").Value
                    };
                    return new HashEntry($"{data.Id}\\{data.Name}\\{data.BelongedSystem}", JsonConvert.SerializeObject(data));
                });
                _redisRepository.Database.HashSet(RedisKey.BaseData_BusinessType, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_BusinessType, $"*{pattern}*");
            return entries.Select(a => JsonConvert.DeserializeObject<BusinessTypeDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode);
        }

        /// <summary>
        /// 获取业务类型与审批流类型的Mapping
        /// </summary>
        /// <param name="pattern">The pattern.</param>
        /// <returns></returns>
        public async Task<IEnumerable<BusinessTypeAndWorkflowTypeDto>> GetBusinessTypeAndWorkflowTypeAsync(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_BusinessTypeAndWorkflowType))
            {
                var query = new QueryExpression("spk_businessflowworkflow");
                query.ColumnSet.AddColumns("spk_name", "spk_businessflow", "spk_relatedworkflowtype", "spk_workflowtypename", "spk_belongedsystem", "statecode");

                var link = query.AddLink("spk_businessflowtype", "spk_businessflow", "spk_businessflowtypeid");
                link.LinkCriteria.AddCondition(new ConditionExpression("statecode", ConditionOperator.Equal, 0));
                link.LinkCriteria.AddCondition(new ConditionExpression("spk_belongedsystem", ConditionOperator.Equal, "NexBPM"));
                link.Columns.AddColumns("spk_businesstypedescription", "spk_businesscategory");
                link.EntityAlias = "businessType";

                var entities = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);
                entries = entities.Select(x =>
                {
                    var data = new BusinessTypeAndWorkflowTypeDto
                    {
                        Id = x.Id,
                        Name = x.GetAttributeValue<string>("spk_name"),
                        //BelongedSystem = x.GetAttributeValue<AliasedValue>("spk_belongedsystem")?.Value.ToString(),
                        BelongedSystem = x.GetAttributeValue<string>("spk_belongedsystem"),
                        BusinessTypeId = x.GetAttributeValue<EntityReference>("spk_businessflow").Id,
                        BusinessTypeName = x.GetAttributeValue<EntityReference>("spk_businessflow").Name,
                        //BusinessTypeCategory = (ResignationTransfer.TaskFormCategory)x.GetAttributeValue<OptionSetValue>("businessType.spk_businesscategory").Value,
                        BusinessTypeCategory = (ResignationTransfer.TaskFormCategory)((OptionSetValue)x.GetAttributeValue<AliasedValue>("businessType.spk_businesscategory").Value).Value,
                        WorkflowTypeId = x.GetAttributeValue<EntityReference>("spk_relatedworkflowtype").Id,
                        WorkflowTypeName = x.GetAttributeValue<EntityReference>("spk_relatedworkflowtype").Name,
                        WorkflowType = (WorkflowTypeName)x.GetAttributeValue<OptionSetValue>("spk_workflowtypename").Value,
                        StateCode = (StateCode)x.GetAttributeValue<OptionSetValue>("statecode").Value
                    };
                    return new HashEntry($"{data.Id}\\{data.Name}\\{data.BelongedSystem}\\{data.BusinessTypeId}\\{data.WorkflowTypeId}", JsonConvert.SerializeObject(data));
                });
                _redisRepository.Database.HashSet(RedisKey.BaseData_BusinessTypeAndWorkflowType, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_BusinessTypeAndWorkflowType, $"*{pattern}*");
            return entries.Select(a => JsonConvert.DeserializeObject<BusinessTypeAndWorkflowTypeDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode);
        }

        /// <summary>
        /// 获取活动类型与成本中心配置信息
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<IEnumerable<ActiveTypeCostcenterMappingDto>> GetActiveTypeCostcenterMappingsAsync(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_ActiveTypeCostcenterMapping))
            {
                var query = new QueryExpression("spk_activitytype");
                query.ColumnSet.AddColumns("spk_activitytypeid", "spk_name", "spk_costcentercode", "statecode");
                var entities = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);
                entries = entities.Select(a =>
                {
                    var data = new ActiveTypeCostcenterMappingDto
                    {
                        Id = a.Id,
                        ActiveTypeName = a.GetAttributeValue<string>("spk_name"),
                        Costcenters = a.GetAttributeValue<string>("spk_costcentercode"),
                        StateCode = (StateCode)a.GetAttributeValue<OptionSetValue>("statecode").Value
                    };
                    return new HashEntry($"{data.Id}\\{data.ActiveTypeName}", JsonConvert.SerializeObject(data));
                });
                _redisRepository.Database.HashSet(RedisKey.BaseData_ActiveTypeCostcenterMapping, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_ActiveTypeCostcenterMapping, $"*{pattern}*");
            return entries.Select(a => JsonConvert.DeserializeObject<ActiveTypeCostcenterMappingDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode);
        }

        /// <summary>
        /// 获取特殊采购审批条件配置
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        public async Task<IEnumerable<SpecApprovalConditionProcurementDto>> GetSpecApprovalConditionProcurementAsync(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_SpecApprovalConditionProcurement))
            {
                var query = new QueryExpression("spk_specapprovalconditionforprocurement");
                query.ColumnSet.AddColumns("spk_specapprovalconditionforprocurementid", "spk_type", "spk_company", "spk_consumercategories", "spk_natureofexpenses", "spk_costcenter", "statecode");
                var entities = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);
                entries = entities.Select(a =>
                {
                    var data = new SpecApprovalConditionProcurementDto
                    {
                        Id = a.Id,
                        Type = (DataverseEnums.SpecApprovalConditionProcurement.Type)a.GetAttributeValue<OptionSetValue>("spk_type").Value,
                        CompanyId = a.GetAttributeValue<EntityReference>("spk_company")?.Id,
                        ConsumeId = a.GetAttributeValue<EntityReference>("spk_consumercategories")?.Id,
                        CostNatureId = a.GetAttributeValue<EntityReference>("spk_natureofexpenses")?.Id,
                        ExceptionCostcenterId = a.GetAttributeValue<EntityReference>("spk_costcenter")?.Id,
                        StateCode = (StateCode)a.GetAttributeValue<OptionSetValue>("statecode").Value
                    };
                    return new HashEntry($"{data.Id}\\{data.CompanyId}\\{data.ConsumeId}\\{data.CostNatureId}\\{data.ExceptionCostcenterId}", JsonConvert.SerializeObject(data));
                });
                _redisRepository.Database.HashSet(RedisKey.BaseData_SpecApprovalConditionProcurement, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_SpecApprovalConditionProcurement, $"*{pattern}*");
            return entries.Select(a => JsonConvert.DeserializeObject<SpecApprovalConditionProcurementDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode);
        }

        /// <summary>
        /// 获取系统配置，不能加入redis,否则配置修改后，不能立即生效
        /// </summary>
        /// <param name="pattern"></param>
        /// <returns></returns>
        public async Task<List<SystemInterationConfigDto>> GetAllSystemInterationConfig(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            var query = new QueryExpression("spk_systemintegrationconfiguration");
            query.ColumnSet.AddColumns("spk_description", "spk_module", "spk_name", "spk_system", "spk_value", "statecode");
            var entities = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);
            var results = entities.Select(x =>
            {
                return new SystemInterationConfigDto
                {
                    Id = x.Id,
                    Name = x.GetAttributeValue<string>("spk_name"),
                    Value = x.GetAttributeValue<string>("spk_value"),
                    Description = x.GetAttributeValue<string>("spk_description"),
                    Module = x.GetAttributeValue<string>("spk_module"),
                    System = x.GetAttributeValue<string>("spk_system"),
                    StateCode = (StateCode)x.GetAttributeValue<OptionSetValue>("statecode").Value
                };
            }).WhereIf(stateCode.HasValue, a => a.StateCode == stateCode).ToList();
            return results;
        }


        public async Task<string> GetWorkflowInstanceFormData(Guid formId)
        {
            var query = new QueryExpression("spk_workflowinstance");
            query.ColumnSet.AddColumns("spk_businessformdata");
            query.Criteria.AddCondition(new ConditionExpression("statecode", ConditionOperator.Equal, 0));
            query.Criteria.AddCondition(new ConditionExpression("spk_businessformid", ConditionOperator.Equal, formId.ToString()));
            query.Criteria.AddCondition(new ConditionExpression("spk_status", ConditionOperator.Equal, 100000000));//Pending Approval
            query.AddOrder("createdon", OrderType.Descending);
            query.TopCount = 1;
            var collection = await _dataverseRepository.DataverseClient.RetrieveMultipleAsync(query);
            var entity = collection.Entities.FirstOrDefault();
            return entity?.GetAttributeValue<string>("spk_businessformdata");
        }

        /// <summary>
        /// 获取旅行社供应商列表
        /// </summary>
        /// <returns></returns>
        public async Task<IEnumerable<DictionaryDto>> GetTravelagencyAsync()
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_Travelagency))
            {
                var query = new QueryExpression("spk_travelagency");
                query.ColumnSet.AddColumns("spk_name");
                var entities = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);
                entries = entities.Select(a =>
                {
                    var data = new DictionaryDto
                    {
                        Id = a.Id,
                        Name = a.GetAttributeValue<string>("spk_name"),
                    };
                    return new HashEntry($"{data.Id}\\{data.Name}", JsonConvert.SerializeObject(data));
                });
                _redisRepository.Database.HashSet(RedisKey.BaseData_Travelagency, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_Travelagency);
            return entries.Select(a => JsonConvert.DeserializeObject<DictionaryDto>(a.Value));
        }
        /// <summary>
        /// 获取产品成本值列表
        /// </summary>
        /// <returns></returns>
        public async Task<IEnumerable<ProductCostDto>> GetProductCostAsync(string pattern = null, StateCode? stateCode = StateCode.Active)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_ProductCost))
            {
                var query = new QueryExpression("spk_productcost");
                query.ColumnSet.AddColumns("spk_productcostid", "spk_productmcode", "spk_costvalue", "statecode");
                var entities = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);
                entries = entities.Select(a =>
                {
                    var data = new ProductCostDto
                    {
                        Id = a.Id,
                        ProductMcode = a.GetAttributeValue<string>("spk_productmcode"),
                        CostValue = a.GetAttributeValue<decimal>("spk_costvalue"),
                        StateCode = a.GetAttributeValue<OptionSetValue>("statecode")?.Value
                    };
                    return new HashEntry($"{data.Id}", JsonConvert.SerializeObject(data));
                });
                _redisRepository.Database.HashSet(RedisKey.BaseData_ProductCost, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_ProductCost, $"*{pattern}*");
            return entries.Select(a => JsonConvert.DeserializeObject<ProductCostDto>(a.Value)).WhereIf(stateCode.HasValue, a => a.StateCode == (int)stateCode);
        }

        /// <summary>
        /// 获取特殊名称供应商
        /// </summary>
        /// <param name="pattern"></param>
        /// <returns></returns>
        public async Task<IEnumerable<SpecialVendorDto>> GetSpecialvendorAsync(string pattern = null)
        {
            IEnumerable<HashEntry> entries;
            if (!_redisRepository.Database.KeyExists(RedisKey.BaseData_SpecialVendor))
            {
                var query = new QueryExpression("spk_specialvendorname");
                query.ColumnSet.AddColumns("spk_name", "spk_vendorcode", "statecode");
                var link = query.AddLink("spk_companymasterdata", "spk_company", "spk_companymasterdataid");
                link.Columns.AddColumns("spk_companycode");
                link.EntityAlias = "link1";
                var entities = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);
                entries = entities.Select(a =>
                {
                    var data = new SpecialVendorDto
                    {
                        Id = a.Id,
                        Name = a.GetAttributeValue<string>("spk_name"),
                        CompanyCode = a.GetAttributeValue<AliasedValue>("link1.spk_companycode")?.Value.ToString(),
                        VendorCode = a.GetAttributeValue<string>("spk_vendorcode"),
                        StateCode = (StateCode)a.GetAttributeValue<OptionSetValue>("statecode").Value
                    };
                    return new HashEntry($"{data.Id}\\{data.Name}\\{data.CompanyCode}\\{data.VendorCode}", JsonConvert.SerializeObject(data));
                });
                _redisRepository.Database.HashSet(RedisKey.BaseData_SpecialVendor, entries.ToArray());
            }
            entries = _redisRepository.Database.HashScan(RedisKey.BaseData_SpecialVendor, $"*{pattern}*");
            return entries.Select(a => JsonConvert.DeserializeObject<SpecialVendorDto>(a.Value));
        }

        #region 批量操作PP
        /// <summary>
        /// 批量操作CRM的数据，可以同时支持新增、编辑和删除，属于事务操作，要么全部成功，要么全部失败
        /// </summary>
        /// <param name="entityCollectionForCreate">需要创建的CRM数据集合</param>
        /// <param name="entityCollectionForUpdate">需要编辑的CRM数据集合</param>
        /// <param name="entityCollectionForDelete">需要删除的CRM数据集合</param>
        /// <param name="otherRequestList">其他request的集合，比如说GrantAccessRequest等等，默认为null代表没有该请求</param>
        /// <returns>全部成功后返回true，只要有一个失败了则回滚所有操作并返回false</returns>
        private bool TransactionRequest(EntityCollection entityCollectionForCreate, EntityCollection entityCollectionForUpdate, EntityCollection entityCollectionForDelete, OrganizationRequestCollection otherRequestList = null)
        {
            ExecuteTransactionResponse response = TransactionRequestAndReturnResponse(entityCollectionForCreate, entityCollectionForUpdate, entityCollectionForDelete, otherRequestList);
            return response != null;
        }

        /// <summary>
        /// 批量操作CRM的数据，可以同时支持新增、编辑和删除，属于事务操作，要么全部成功，要么全部失败
        /// 这里有一个限制，同一个事务的请求数量不能超过ExecuteTransactionMaxmumBatchSize，超过了就会报错
        /// </summary>
        /// <param name="entityCollectionForCreate">需要创建的CRM数据集合</param>
        /// <param name="entityCollectionForUpdate">需要编辑的CRM数据集合</param>
        /// <param name="entityCollectionForDelete">需要删除的CRM数据集合</param>
        /// <param name="otherRequestList">其他request的集合，比如说GrantAccessRequest等等，默认为null代表没有该请求</param>
        /// <returns>全部成功后返回ExecuteTransactionResponse，只要有一个失败了则回滚所有操作并返回null</returns>
        private ExecuteTransactionResponse TransactionRequestAndReturnResponse(EntityCollection entityCollectionForCreate, EntityCollection entityCollectionForUpdate, EntityCollection entityCollectionForDelete, OrganizationRequestCollection otherRequestList = null)
        {
            try
            {
                OrganizationRequestCollection requests = new OrganizationRequestCollection();
                // 创建带事务的请求
                ExecuteTransactionRequest multipleRequest = new ExecuteTransactionRequest()
                {
                    // 创建执行集合
                    Requests = new OrganizationRequestCollection(),
                    ReturnResponses = true
                };
                if (entityCollectionForCreate != null && entityCollectionForCreate.Entities.Count > 0)
                {
                    foreach (Entity entity in entityCollectionForCreate.Entities)
                    {
                        requests.Add(new CreateRequest { Target = entity });
                    }
                }
                if (entityCollectionForUpdate != null && entityCollectionForUpdate.Entities.Count > 0)
                {
                    foreach (Entity entity in entityCollectionForUpdate.Entities)
                    {
                        requests.Add(new UpdateRequest { Target = entity });
                    }
                }
                if (entityCollectionForDelete != null && entityCollectionForDelete.Entities.Count > 0)
                {
                    foreach (Entity entity in entityCollectionForDelete.Entities)
                    {
                        requests.Add(new DeleteRequest { Target = new EntityReference(entity.LogicalName, entity.Id) });
                    }
                }
                if (otherRequestList != null && otherRequestList.Count > 0)
                {
                    requests.AddRange(otherRequestList.ToArray());
                }
                //总数超过ExecuteTransactionMaxmumBatchSize的情况需要分开处理，因为一次事务的提交里面最多只能包含ExecuteTransactionMaxmumBatchSize个请求
                if (requests.Count > ExecuteTransactionMaxmumBatchSize)
                {
                    ExecuteTransactionResponse allResponse = new ExecuteTransactionResponse();
                    Dictionary<Guid, string> allCreatedEntity = new Dictionary<Guid, string>();
                    IEnumerable<OrganizationRequest> allRequests = requests.Take(requests.Count);
                    IEnumerable<OrganizationRequest> tempRequests = requests.Take(requests.Count);
                    int requestCount = ExecuteTransactionMaxmumBatchSize;
                    string responseName;
                    while (allRequests.Count() > 0)
                    {
                        requestCount = allRequests.Count() > ExecuteTransactionMaxmumBatchSize ? ExecuteTransactionMaxmumBatchSize : allRequests.Count();
                        multipleRequest.Requests.Clear();
                        tempRequests = allRequests.Take(requestCount);
                        multipleRequest.Requests.AddRange(tempRequests);
                        allRequests = allRequests.Skip(requestCount);
                        ExecuteTransactionResponse currentResponse = TransactionRequestBase(multipleRequest);
                        if (currentResponse != null)
                        {
                            if (allResponse.Responses != null)
                            {
                                allResponse.Responses.AddRange(currentResponse.Responses);
                            }
                            else
                            {
                                allResponse = currentResponse;
                            }
                            for (int i = 0; i < currentResponse.Responses.Count; i++)
                            {
                                responseName = currentResponse.Responses[i].ResponseName.ToLower();
                                if (responseName == "create")
                                {
                                    allCreatedEntity.Add((Guid)currentResponse.Responses[i]["id"], ((CreateRequest)tempRequests.ElementAt(i)).Target.LogicalName);
                                }
                            }
                        }
                        else
                        {
                            RollBackTransactionRequest(allCreatedEntity);
                            return null;
                        }
                    }
                    return allResponse;
                }
                else
                {
                    multipleRequest.Requests = requests;
                    return (ExecuteTransactionResponse)_dataverseRepository.DataverseClient.Execute(multipleRequest);
                }
            }
            catch (Exception ex)
            {
                return null;
            }
        }
        /// <summary>
        /// 如果某次的事务请求超过ExecuteTransactionMaxmumBatchSize，则分批执行事务时，如果前面批次的成功，但是后面的批次失败，则需要将前面成功的进行回滚操作，暂时只支持新增的请求回滚
        /// </summary>
        /// <param name="allCreatedEntity">已经成功了的事务请求，字典的key是实体数据的guid，字典的value是实体的逻辑名</param>
        private void RollBackTransactionRequest(Dictionary<Guid, string> allCreatedEntity)
        {
            EntityCollection entityCollectionForDelete = new EntityCollection();
            foreach (KeyValuePair<Guid, string> createdEntity in allCreatedEntity)
            {
                entityCollectionForDelete.Entities.Add(new Entity(createdEntity.Value, createdEntity.Key));
            }
            if (entityCollectionForDelete.Entities.Count > 0)
            {
                TransactionRequest(null, null, entityCollectionForDelete);
            }
        }
        /// <summary>
        /// 事务请求的base方法，私有方法，解决事务请求超过ExecuteTransactionMaxmumBatchSize报超限错误的问题
        /// </summary>
        /// <param name="multipleRequest">事务请求的集合，数量不能超过ExecuteTransactionMaxmumBatchSize</param>
        /// <returns>全部成功后返回ExecuteTransactionResponse，只要有一个失败了则回滚所有操作并返回null</returns>
        private ExecuteTransactionResponse TransactionRequestBase(ExecuteTransactionRequest multipleRequest)
        {
            try
            {
                return (ExecuteTransactionResponse)_dataverseRepository.DataverseClient.Execute(multipleRequest);
            }
            catch (Exception ex)
            {
                return null;
            }
        }
        #endregion
    }
}
