﻿using Abbott.SpeakerPortal.Contracts.Report;

using Hangfire;
using System.Threading;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.BackgroundWorkers.Report
{
    /// <summary>
    /// 生成Accrual报表数据Job
    /// </summary>
    public class GenerateAccrualReportWorker : SpeakerPortalBackgroundWorkerBase
    {
        public GenerateAccrualReportWorker()
        {
            //CronExpression = Cron.Daily();
            //CronExpression = "0 8-23/3 * * *";
            CronExpression = Cron.Daily(3);
        }
        
        [AutomaticRetry(Attempts = 0)]
        public override async Task DoWorkAsync(CancellationToken cancellationToken = default)
        {
            await LazyServiceProvider.LazyGetService<IReportService>().GenerateReportAccrualAsync();
        }
    }
}
