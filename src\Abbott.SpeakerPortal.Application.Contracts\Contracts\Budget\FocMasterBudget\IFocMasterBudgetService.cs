﻿using System.Collections.Generic;
using System;
using System.Threading.Tasks;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Volo.Abp.Application.Dtos;
using Abbott.SpeakerPortal.Contracts.Budget.Subbudget;

namespace Abbott.SpeakerPortal.Contracts.Budget.FocMasterBudget
{
    public interface IFocMasterBudgetService
    {
        /// <summary>
        /// 创建FOC主预算
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task CreateFocMasterBudget(CreateFocBudgetRequestDto requestDto);

        /// <summary>
        /// 获取FOC主预算列表
        /// </summary>
        /// <param name="request"></param>
        /// <param name="IsPage"></param>
        /// <returns></returns>
        Task<PagedResultDto<GetFocBudgetListResponseDto>> GetFocMasterBudgetListAsync(GetFocBudgetListRequestDto request, bool IsPage = true);

        /// <summary>
        /// 删除FOC主预算
        /// </summary>
        /// <param name="Ids"></param>
        /// <returns></returns>
        Task<MessageResult> DeleteFocMasterBudgetByIdsAsync(List<Guid> Ids);

        /// <summary>
        /// 冻结预算
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<MessageResult> UpdateFocBudgetStatusAsync(UpdateBudgetStatusRequestDto requestDto);

        /// <summary>
        /// 调整预算
        /// </summary>
        /// <returns></returns>
        Task<MessageResult> AdjustmentBudgetQtyAsync(AdjustBudgetQtyRequestDto requestDto);

        /// <summary>
        /// 获取同bu下的Bu信息
        /// </summary>
        /// <param name="transfer"></param>
        /// <returns></returns>
        Task<PagedResultDto<FocBudgetResponseDto>> GetFocBudgetListAsync(TransferRequestDto transfer);

        /// <summary>
        /// 根据Id获取预算信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        Task<BudgetQtyResponseDto> GetBudgetQtyAsync(Guid Id);

        /// <summary>
        /// 获取详情记录
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        Task<GetFocBudgetListResponseDto> GetFocMasterBudgetRecordByIdAsync(Guid Id);

        /// <summary>
        /// 获取所有主数据
        /// </summary>
        /// <returns></returns>
        Task<IList<AllFocBudgetResponseDto>> GetAllFocBudgetListAsync(string Code);

        /// <summary>
        /// 解析批量上传excel
        /// </summary>
        /// <returns></returns>
        /// <param name="excelDtos"></param>
        Task<MessageResult> AnalyzeCreateFocMasterBudgetExcelAsync(IEnumerable<CreateFocMasterBudgetExcelDto> excelDtos);

        /// <summary>
        /// 批量新增主预算
        /// </summary>
        /// <returns></returns>
        /// <param name="request"></param>
        Task<MessageResult> CreatesFocMasterBudgetAsync(ImportDataResponseDto<AnalyzeFocBudgetExcelResponseDto> request);

    }
}
