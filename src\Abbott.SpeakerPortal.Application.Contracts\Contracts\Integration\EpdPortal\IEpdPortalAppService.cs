﻿using Abbott.SpeakerPortal.Domain.Shared.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.Contracts.Integration.Dspot
{
    public interface IEpdPortalAppService
    {
        Task<MessageResult> PushSpeakerInfoStatus(PushSpeakerInfoRequestDto request);

        Task<MessageResult> PushDoctorInfo(PushDoctorInfoRequestDto request);

        Task<MessageResult> ModifyEpdHcpCode(ModifyEpdHcpCodeRequestDto request);

        Task<MessageResult> ModifyEpdHcpCode(List<ModifyEpdHcpCodeRequestDto> request);

        /// <summary>
        /// 相比之前的逻辑添加了：
        ///1. Code变更时也要看申请表的草稿数据；
        ///2. Rita说的原Code有的就处理，没有的就不管；
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<MessageResult> ModifyOrNotEpdHcpCode(List<ModifyEpdHcpCodeRequestDto> request);


        Task<MessageResult> DoctorInfoResult(DoctorInfoQueryRequestDto request);
    }
}