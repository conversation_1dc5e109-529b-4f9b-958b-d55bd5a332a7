﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

using Abbott.SpeakerPortal.Consent;
using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Common.Common;
using Abbott.SpeakerPortal.Contracts.Common.InvoiceTax;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Models;

using Volo.Abp.Application.Dtos;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.Domain.Repositories;

namespace Abbott.SpeakerPortal.Contracts.Common
{
    public interface ICommonService : ITransientDependency
    {
        /// <summary>
        /// 获取省市树形数据
        /// </summary>
        /// <returns></returns>
        Task<List<ProvinceCityDto>> GetProvinceCity();

        /// <summary>
        /// 获取部门(组织中的部门)
        /// </summary>
        /// <returns></returns>
        Task<IEnumerable<KeyValuePair<Guid, string>>> GetOrganizationDept(Guid userId);

        /// <summary>
        /// 获取部门类型的组织数据(与指定用户有关系的组织)，包含每个部门所属BU
        /// </summary>
        /// <returns></returns>
        Task<IEnumerable<DepartmentWithBuDto>> GetOrganizationDeptWithBU(Guid userId);

        /// <summary>
        /// 根据传入的组织机构获取所有下级组织机构
        /// </summary>
        /// <param name="org"></param>
        /// <param name="orgs"></param>
        /// <returns></returns>
        Task<IEnumerable<DepartmentDto>> GetChildrenOrgs(DepartmentDto org, List<DepartmentDto> orgs = null);
        /// <summary>
        /// 根据传入的组织机构获取所有下级组织机构
        /// </summary>
        /// <param name="Ids">The ids.</param>
        /// <param name="orgs">The orgs.</param>
        /// <returns></returns>
        Task<IEnumerable<DepartmentDto>> GetChildrenOrgs(List<Guid> Ids, List<DepartmentDto> orgs = null);
        /// <summary>
        /// 根据传入的组织机构向上获取所有父级组织机构至BU
        /// </summary>
        /// <param name="org"></param>
        /// <param name="orgs"></param>
        /// <returns></returns>
        Task<IEnumerable<DepartmentDto>> GetParentOrgs(DepartmentDto org, List<DepartmentDto> orgs = null);

        /// <summary>
        /// 获取所有上级组织
        /// </summary>
        /// <param name="orgId"></param>
        /// <param name="orgs"></param>
        /// <returns></returns>
        Task<IEnumerable<DepartmentDto>> GetAllParentOrgs(Guid orgId, List<DepartmentDto> orgs = null);

        /// <summary>
        /// 根据传入的组织机构向上获取所有父级组织机构至分公司
        /// </summary>
        /// <param name="org"></param>
        /// <param name="orgs"></param>
        /// <returns></returns>
        Task<IEnumerable<DepartmentDto>> GetAffiliatesOrgs(DepartmentDto org, List<DepartmentDto> orgs = null);

        /// <summary>
        /// 获取组织，模糊查询
        /// </summary>
        /// <param name="keyword">The keyword.</param>
        /// <param name="count">The count.</param>
        /// <returns></returns>
        Task<IEnumerable<DepartmentDto>> GetOrgsAsync(string keyword, int count = 200);


        /// <summary>
		/// 获取内部可用用户列表
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>
		Task<PagedResultDto<GetActiveUserListResponseDto>> GetInternalActiveUsersAsync(GetActiveUserListRequestDto request);

        /// <summary>
        /// 获取所有内部可用用户列表
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        Task<PagedResultDto<GetActiveUserListResponseDto>> GetAllInternalActiveUsersAsync(GetActiveUserListRequestDto request);

        /// <summary>
        /// 导出所有内部可用用户列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<Stream> ExportAllInternalActiveUsersAsync(GetActiveUserListRequestDto request);

        /// <summary>
        /// 获取发票类型与税率
        /// </summary>
        /// <returns></returns>
        Task<IEnumerable<InvoiceTaxRateDto>> GetInvoiceTypeTaxRateAsync();

        /// <summary>
        /// 获取紧急付款类型配置
        /// </summary>
        /// <returns></returns>
        Task<IEnumerable<DictionaryDto>> GetPturtTypeDictionaryAsync();

        /// <summary>
        /// 获取员工数据
        /// </summary>
        /// <returns></returns>
        Task<IList<GetUserDropdownListResponseDto>> GetUserDropDownLsitAsync();

        /// <summary>
        /// 识别身份证信息
        /// </summary>
        /// <param name="blobName"></param>
        /// <returns></returns>
        Task<MessageResult> CognizeIdCard(string blobName);
        /// <summary>
        /// 生成Pdf
        /// </summary>
        /// <param name="values"></param>
        /// <param name="TemplateWordPath">word模版路径,只能小于3页</param>
        /// <param name="tableDics">表格数据</param>
        /// <param name="TemplatePDFPath">pdf模版路径</param>
        /// <returns></returns>
        Task<Stream> GeneratePDFAsync(Dictionary<string, object> values, string TemplateWordPath, Dictionary<string, List<Dictionary<string, object>>> tableDics = null,
            string TemplatePDFPath = null);
        /// <summary>
        /// 获取所有费用性质
        /// </summary>
        Task<MessageResult> GetAllCostNatureAsync();

        /// <summary>
        /// 获取Consent最新版本协议信息
        /// </summary>
        /// <returns></returns>
        Task<IEnumerable<ConsentUrlResponseDto>> GetLeatestConsentInfosAsync();

        /// <summary>
        /// 获取Consent的签署信息
        /// </summary>
        /// <param name="userIds"></param>
        /// <returns></returns>
        Task<IEnumerable<(Guid UserId, string ConsentCode, string ConsentVersion)>> GetConsentSignedInfosAsync(IEnumerable<Guid> userIds);

        /// <summary>
        /// 判断User是否签署了最新协议
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="consentResponses"></param>
        /// <param name="consentSigneds"></param>
        /// <returns></returns>
        bool IsSignLeatestVersion(Guid userId, IEnumerable<ConsentUrlResponseDto> consentResponses, IEnumerable<(Guid UserId, string ConsentCode, string ConsentVersion)> consentSigneds);

        /// <summary>
        /// 记录操作日志
        /// 内部关键操作日志，外部集成和交互的日志
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        Task<MessageResult> CreateOperationLog(List<SetOperationLogRequestDto> request);

        /// <summary>
        /// 初始化日志对象
        /// </summary>
        /// <param name="system"></param>
        /// <param name="api"></param>
        /// <param name="content"></param>
        /// <returns></returns>
        SetOperationLogRequestDto InitOperationLog(string system, string api, string content);


        /// <summary>
        /// 记录api调用响应
        /// </summary>
        /// <param name="log"></param>
        /// <param name="response"></param>
        /// <param name="success"></param>
        /// <returns></returns>        
        void LogResponse(SetOperationLogRequestDto log, string response, bool success = true);

        /// <summary>
        /// 获取PaymentTerm显示值
        /// </summary>
        /// <param name="company"></param>
        /// <param name="paymentTermCode"></param>
        /// <returns></returns>
        string GetPaymentTermName(string company, string paymentTermCode);

        /// <summary>
        /// 申请人相关的操作仅限申请人/代理人/转办人可以操作
        /// </summary>
        /// <param name="businessFormId">业务Id</param>
        /// <returns></returns>
        Task<bool> ValidateApplicantRelatedOperationAsync(Guid businessFormId);

        /// <summary>
        /// 获取指定组织相关的公司
        /// </summary>
        /// <param name="OrgId"></param>
        /// <returns></returns>
        Task<MessageResult> GetComByOrgAsync(Guid OrgId);

        /// <summary>
        /// 获取指定用户的关联组织和作为EpoLeader的组织的所有下属组织
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        Task<IEnumerable<Guid>> GetAllChildrenOrgs(Guid userId);

        /// <summary>
        /// 获取openiddict application的扩展信息
        /// </summary>
        /// <param name="clientId"></param>
        /// <returns></returns>
        Task<OpenIddictApplicationExtraProperty> GetOpenIddictApplicationExtraPropertyAsync(string clientId);

        /// <summary>
        /// 判断某个组织是否属于指定分公司下属的组织
        /// </summary>
        /// <param name="theOrg">某个组织</param>
        /// <param name="targetOrgType">目标组织类型：默认“分公司”</param>
        /// <param name="targetOrgName">目标组织名称：默认“嘉兴工厂”</param>
        /// <returns></returns>
        Task<bool> IsSubOrganization(Guid theOrg, DataverseEnums.Organization.OrganizationType targetOrgType = DataverseEnums.Organization.OrganizationType.Affiliates, string targetOrgName = AffiliateNameConst.JXFactory);

        /// <summary>
        /// 清理系统日志
        /// </summary>
        /// <returns></returns>
        Task ClearSystemLogAsync();
        /// <summary>
        /// 获取授权bu下所有子部门
        /// </summary>
        /// <returns></returns>
        Task<List<KeyValuePair<IEnumerable<string>, IEnumerable<Guid>>>> GetAuthorizedAllDepts(List<(Guid, string)> users);
    }
}
