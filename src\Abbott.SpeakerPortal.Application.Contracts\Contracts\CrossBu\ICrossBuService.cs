﻿using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.User;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;

namespace Abbott.SpeakerPortal.Contracts.CrossBu
{
    /// <summary>
    /// 市场活动服务
    /// </summary>
    public interface ICrossBuService
    {
        /// <summary>
        /// 查询一年中有月份是否有活动
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<MessageResult> QueryYearMarketActivitiesAsync(QueryYearMarketActivityRequestDto request);

        /// <summary>
        /// 根据传入月份获取市场活动数据
        /// </summary>
        /// <param name="request"></param>
        /// <param name="IsPage"></param>
        /// <returns></returns>
        Task<PagedResultDto<QueryMarketActivityListResponseDto>> QueryMarketActivitiesAsync(QueryMarketActivityRequestDto request, bool IsPage = true);

        /// <summary>
        /// 检查市场活动是否重复
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<MessageResult> CheckMarketActivityAsync(CreateMarketActivityRequestDto request);

        /// <summary>
        /// 创建市场活动数据
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<MessageResult> CreateMarketActivityAsync(CreateMarketActivityRequestDto request);

        /// <summary>
        /// 更新市场活动数据
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<MessageResult> UpdateMarketActivityAsync(CreateMarketActivityRequestDto request);

        /// <summary>
        /// 发送申请加入邮件邮件
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task SendApplyToJoinEmail(Guid id);

        /// <summary>
        /// 获取市场活动数据
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<QueryMarketActivityResponseDto> GetMarketActivityAsync(Guid? id);

        /// <summary>
        /// 验证上传的Excel
        /// </summary>
        /// <param name="Rows"></param>
        /// <returns></returns>
        Task<MessageResult> VarifyImportMarketActivityAsync(List<TransferMarketActivityDto> Rows);

        /// <summary>
        /// 提交市场活动数据
        /// </summary>
        /// <param name="Rows"></param>
        /// <returns></returns>
        Task<MessageResult> ImportMarketActivityAsync(List<TransferMarketActivityDto> Rows);

        /// <summary>
        /// 更新用户的收藏
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<MessageResult> UpdateMarketActivityUserInformationAsync(UpdateMarketActivityUserInformationDto request);

        /// <summary>
        /// 修改市场活动状态
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<MessageResult> ModifyMarketActivityStatusAsync(UpdateMarketActivityStatusRequestDto request);

        /// <summary>
        /// 删除市场活动
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<MessageResult> DeleteMarketActivityAsync(Guid? id);

        /// <summary>
        /// 获取用户的收藏
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<MessageResult> GetMarketActivityUserInformationAsync(Guid? id);

        /// <summary>
        /// H5加入活动
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<MessageResult> CreateH5ApplyJoinToMarketActivityAsync(CreateH5ApplyJoinToMarketActivityDto request);

        /// <summary>
        /// 创建意见反馈
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<MessageResult> CreateSuggestionFeedbackAsync(CreateSuggestionFeedbackRequestDto request);

        /// <summary>
        /// 检索加入市场活动数据
        /// </summary>
        /// <param name="request"></param>
        /// <param name="IsPage"></param>
        /// <returns></returns>
        Task<PagedResultDto<QueryJoinToMarketActivityListResponseDto>> QueryJoinToMarketActivitiesAsync(QueryJoinToMarketActivityRequestDto request, bool IsPage = true);

        /// <summary>
        /// 获取加入市场活动详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<QueryJoinToMarketActivityResponseDto> GetJoinToMarketActivitiesAsync(Guid? id);

        /// <summary>
        /// 修改加入市场活动审批状态
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task ModifyJoinToMarketActivityStatusAsync(UpdateJoinToMarketActivityStatusRequestDto request);

        /// <summary>
        /// 筛选市场关系数据
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<PagedResultDto<QueryCustomerRelationListResponseH5Dto>> QueryCustomerRelationsAsync(QueryCustomerRelationRequestDto request);

        /// <summary>
        /// 查询客户关系列表
        /// </summary>
        /// <param name="request"></param>
        /// <param name="IsPage"></param>
        /// <returns></returns>
        Task<PagedResultDto<QueryCustomerRelationListResponsePortalDto>> QueryPortalCustomerRelationsAsync(QueryCustomerRelationRequestDto request, bool IsPage = true);

        /// <summary>
        /// 验证客户关系上传的Excel
        /// </summary>
        /// <param name="Rows"></param>
        /// <param name="bussinessUnitId"></param>
        /// <returns></returns>
        Task<MessageResult> VarifyImportCustomerRelationAsync(IEnumerable<TransferCustomerRelationDto> Rows, Guid bussinessUnitId);

        /// <summary>
        /// 提交客户关系数据
        /// </summary>
        /// <param name="Rows"></param>
        /// <param name="bussinessUnitId"></param>
        /// <returns></returns>
        Task<MessageResult> ImportCustomerRelationAsync(List<TransferCustomerRelationDto> Rows, Guid bussinessUnitId);
        /// <summary>
        /// Creates the customer relation asynchronous.
        /// </summary>
        /// <param name="requestDto">The request dto.</param>
        /// <returns></returns>
        Task<MessageResult> CreateCustomerRelationAsync(CreateCustomerRelationRequestDto requestDto);
        /// <summary>
        /// Edits the customer relation asynchronous.
        /// </summary>
        /// <param name="requestDto">The request dto.</param>
        /// <returns></returns>
        Task<MessageResult> EditCustomerRelationAsync(UpdateCustomerRelationRequestDto requestDto);
        /// <summary>
        /// Gets the customer relation asynchronous.
        /// </summary>
        /// <param name="Id">The identifier.</param>
        /// <returns></returns>
        Task<GetCustomerRelationResponseDto> GetCustomerRelationAsync(Guid Id);
        /// <summary>
        /// Modifies the status asynchronous.
        /// </summary>
        /// <param name="requestDto">The request dto.</param>
        /// <returns></returns>
        Task ModifyStatusAsync(UpdateCustomerRelationStatusRequestDto requestDto);
        /// <summary>
        /// Gets the history records by identifier asynchronous.
        /// </summary>
        /// <param name="requestDto">The request dto.</param>
        /// <returns></returns>
        Task<PagedResultDto<CustomerRelationHistoryResponseDto>> GetHistoryRecordsByIdAsync(CustomerRelationHistoryRequestDto requestDto);
        /// <summary>
        /// Queries the customer relations asynchronous.
        /// </summary>
        /// <param name="hospitalName">The hospital identifier.</param>
        /// <returns></returns>
        Task<List<HospitalDepartment>> QueryCustomerRelationsAsync(string hospitalName, Guid? businessUnitId, string province, string city);

        /// <summary>
        /// 获取当前用户部门
        /// </summary>
        /// <returns></returns>
        Task<List<GerUserDepartmentResponseDto>> GetUserDepartment();
        /// <summary>
        /// 删除客户关系
        /// </summary>
        /// <param name="Id">The identifier.</param>
        /// <returns></returns>
        Task<MessageResult> DeleteCustomerRelationAsync(Guid Id);
        /// <summary>
        /// Contacts the share asynchronous.
        /// </summary>
        /// <param name="resquestDto">The resquest dto.</param>
        /// <returns></returns>
        Task<MessageResult> ContactShareAsync(ContactShareResquestDto resquestDto);
        Task<MessageResult> Cleaned(IList<KeyValuePair<string, string>> valuePairs);


        /// <summary>
        /// 获取当前用户部门
        /// </summary>
        /// <returns></returns>
        Task<List<GerUserDepartmentResponseDto>> GetDepartment(MenuType type);
    }
}
