﻿using Abbott.SpeakerPortal.Contracts.Report;

using Hangfire;
using System.Threading;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.BackgroundWorkers.Report
{
    /// <summary>
    /// 生成EPD报表数据Job
    /// </summary>
    public class GenerateEPDReportWorker : SpeakerPortalBackgroundWorkerBase
    {
        public GenerateEPDReportWorker()
        {
            CronExpression = Cron.Daily(2);
            //CronExpression = "0 6-23/3 * * *";
        }
        
        [AutomaticRetry(Attempts = 0)]
        public override async Task DoWorkAsync(CancellationToken cancellationToken = default)
        {
            //var cc = await LazyServiceProvider.LazyGetService<IReportService>().TestCallSP();

            var dd = await LazyServiceProvider.LazyGetService<IReportService>().GenerateReportEPDAsync();


            /*
            var request = new EPDReportRequestDto { PageIndex = 1, PageSize = 20 };
            var ee = await LazyServiceProvider.LazyGetService<IReportService>().GetEpdReportListAsync(request);
            */
        }
    }
}
