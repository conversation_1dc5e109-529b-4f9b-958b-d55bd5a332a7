﻿using System;
using System.Linq;

namespace Abbott.SpeakerPortal.Contracts.Budget.FocSubbudget
{
    public class FocMothlyQtyTextDto : MothlyQtyDto
    {
        /// <summary>
        /// 一月预算
        /// </summary>
        public string JanQtyText { get; set; }
        /// <summary>
        /// 一月状态
        /// </summary>
        public string JanStatusText { get; set; }
        /// <summary>
        /// 二月预算
        /// </summary>
        public string FebQtyText { get; set; }
        /// <summary>
        /// 二月状态
        /// </summary>
        public string FebStatusText { get; set; }
        /// <summary>
        /// 三月预算
        /// </summary>
        public string MarQtyText { get; set; }
        /// <summary>
        /// 三月状态
        /// </summary>
        public string MarStatusText { get; set; }
        /// <summary>
        /// 四月预算
        /// </summary>
        public string AprQtyText { get; set; }
        /// <summary>
        /// 四月状态
        /// </summary>
        public string AprStatusText { get; set; }
        /// <summary>
        /// 五月预算
        /// </summary>
        public string MayQtyText { get; set; }
        /// <summary>
        /// 五月状态
        /// </summary>
        public string MayStatusText { get; set; }
        /// <summary>
        /// 六月预算
        /// </summary>
        public string JunQtyText { get; set; }
        /// <summary>
        ///六月状态
        /// </summary>
        public string JunStatusText { get; set; }
        /// <summary>
        /// 七月预算
        /// </summary>
        public string JulQtyText { get; set; }
        /// <summary>
        /// 七月状态
        /// </summary>
        public string JulStatusText { get; set; }
        /// <summary>
        /// 八月预算
        /// </summary>
        public string AugQtyText { get; set; }
        /// <summary>
        /// 八月状态
        /// </summary>
        public string AugStatusText { get; set; }
        /// <summary>
        /// 九月预算
        /// </summary>
        public string SeptQtyText { get; set; }
        /// <summary>
        /// 九月状态
        /// </summary>
        public string SeptStatusText { get; set; }
        /// <summary>
        /// 十月预算
        /// </summary>
        public string OctQtyText { get; set; }
        /// <summary>
        /// 十月状态
        /// </summary>
        public string OctStatusText { get; set; }
        /// <summary>
        /// 十一月预算
        /// </summary>
        public string NovQtyText { get; set; }
        /// <summary>
        /// 十一月状态
        /// </summary>
        public string NovStatusText { get; set; }
        /// <summary>
        /// 十二月预算
        /// </summary>
        public string DecQtyText { get; set; }
        /// <summary>
        /// 十二月状态
        /// </summary>
        public string DecStatusText { get; set; }
        /// <summary>
        /// 总数量
        /// </summary>
        public int TotalQty { get; private set; }
        /// <summary>
        /// 验证
        /// </summary>
        public (bool, string) VerificationCreate()
        {
            Type type = this.GetType();
            string[] months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sept", "Oct", "Nov", "Dec"];
            string message = string.Empty;
            int i = 1;
            string[] status = ["是", "否"];
            foreach (var item in months)
            {
                var qtyProperty = type.GetProperty($"{item}QtyText");
                var qtyValue = qtyProperty.GetValue(this, null).ToString();
                if (string.IsNullOrEmpty(qtyValue))
                    type.GetProperty($"{item}Qty").SetValue(this, 0, null);
                else if (int.TryParse(qtyValue, out int qty))
                {
                    if (qty < 0) message += $"{i}月数量必须大于0;";
                    else
                    {
                        type.GetProperty($"{item}Qty").SetValue(this, qty, null);
                        type.GetProperty($"{item}QtyText").SetValue(this, qty.ToString(), null);
                        TotalQty += qty;
                    }
                }
                else message += $"{i}月数量不是数字;";

                var statusProperty = type.GetProperty($"{item}StatusText");
                var statusValue = statusProperty.GetValue(this, null).ToString();
                if (string.IsNullOrEmpty(statusValue))
                {
                    type.GetProperty($"{item}Status").SetValue(this, false, null);
                    statusProperty.SetValue(this, "否", null);
                }
                else if (status.Contains(statusValue))
                {
                    bool value = statusValue == "是";
                    type.GetProperty($"{item}Status").SetValue(this, value, null);
                }
                else message += $"{i}月状态只能填写是否;";
                i++;
            }
            return (!string.IsNullOrEmpty(message), message);
        }
        //批量调整
        public (bool, string) VerificationAdjustment()
        {
            Type type = this.GetType();
            string[] months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sept", "Oct", "Nov", "Dec"];
            string message = string.Empty;
            int i = 1;
            string[] status = ["是", "否"];
            foreach (var item in months)
            {
                var qtyProperty = type.GetProperty($"{item}QtyText");
                var qtyValue = qtyProperty.GetValue(this, null).ToString();
                if (string.IsNullOrEmpty(qtyValue)) { }
                else if (int.TryParse(qtyValue, out int qty))
                {
                    type.GetProperty($"{item}Qty").SetValue(this, qty, null);
                    type.GetProperty($"{item}QtyText").SetValue(this, qty, null);
                    TotalQty += qty;
                }
                else message += $"{i}月数量不是数字;";

                var statusProperty = type.GetProperty($"{item}StatusText");
                var statusValue = statusProperty.GetValue(this, null).ToString();
                if (string.IsNullOrEmpty(statusValue)) { }
                else if (status.Contains(statusValue))
                {
                    bool value = statusValue == "是";
                    type.GetProperty($"{item}Status").SetValue(this, value, null);
                }
                else message += $"{i}月状态只能填写是否;";
                i++;
            }
            return (!string.IsNullOrEmpty(message), message);
        }
    }

    public class MothlyQtyDto
    {
        /// <summary>
        /// 一月预算
        /// </summary>
        public int? JanQty { get; set; }
        /// <summary>
        /// 一月状态
        /// </summary>
        public bool? JanStatus { get; set; }
        /// <summary>
        /// 二月预算
        /// </summary>
        public int? FebQty { get; set; }
        /// <summary>
        /// 二月状态
        /// </summary>
        public bool? FebStatus { get; set; }
        /// <summary>
        /// 三月预算
        /// </summary>
        public int? MarQty { get; set; }
        /// <summary>
        /// 三月状态
        /// </summary>
        public bool? MarStatus { get; set; }
        /// <summary>
        /// 四月预算
        /// </summary>
        public int? AprQty { get; set; }
        /// <summary>
        /// 四月状态
        /// </summary>
        public bool? AprStatus { get; set; }
        /// <summary>
        /// 五月预算
        /// </summary>
        public int? MayQty { get; set; }
        /// <summary>
        /// 五月状态
        /// </summary>
        public bool? MayStatus { get; set; }
        /// <summary>
        /// 六月预算
        /// </summary>
        public int? JunQty { get; set; }
        /// <summary>
        ///六月状态
        /// </summary>
        public bool? JunStatus { get; set; }
        /// <summary>
        /// 七月预算
        /// </summary>
        public int? JulQty { get; set; }
        /// <summary>
        /// 七月状态
        /// </summary>
        public bool? JulStatus { get; set; }
        /// <summary>
        /// 八月预算
        /// </summary>
        public int? AugQty { get; set; }
        /// <summary>
        /// 八月状态
        /// </summary>
        public bool? AugStatus { get; set; }
        /// <summary>
        /// 九月预算
        /// </summary>
        public int? SeptQty { get; set; }
        /// <summary>
        /// 九月状态
        /// </summary>
        public bool? SeptStatus { get; set; }
        /// <summary>
        /// 十月预算
        /// </summary>
        public int? OctQty { get; set; }
        /// <summary>
        /// 十月状态
        /// </summary>
        public bool? OctStatus { get; set; }
        /// <summary>
        /// 十一月预算
        /// </summary>
        public int? NovQty { get; set; }
        /// <summary>
        /// 十一月状态
        /// </summary>
        public bool? NovStatus { get; set; }
        /// <summary>
        /// 十二月预算
        /// </summary>
        public int? DecQty { get; set; }
        /// <summary>
        /// 十二月状态
        /// </summary>
        public bool? DecStatus { get; set; }
    }
}
