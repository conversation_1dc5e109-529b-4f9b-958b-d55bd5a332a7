﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Abbott.SpeakerPortal.Contracts.Budget.Subbudget
{
    public class ImportDataResponseDto<T>
    {
        public ImportDataResponseDto(IList<T> Datas, bool IsSuccess,string Base64Datas=null)
        {
            this.Datas = Datas;
            this.IsSuccess = IsSuccess;
            this.Base64Datas = Base64Datas;
        }
        /// <summary>
        /// 是否验证通过
        /// </summary>
        public bool IsSuccess { get; set; }
        /// <summary>
        /// 验证信息
        /// </summary>
        public IList<T> Datas { get; set; }
        /// <summary>
        /// 验证信息base64编码
        /// </summary>
        public string Base64Datas { get; set; }
    }
}
