SELECT
 TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([ID],'00000000-0000-0000-0000-000000000000'))[Id]
,CAST('00000000-0000-0000-0000-000000000000' AS UNIQUEIDENTIFIER) AS [TransfereeId]
,NULL AS [TransfereeName]
,ApplicationCode
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([ApplyUserId],'00000000-0000-0000-0000-000000000000'))[ApplyDeptId]
,[ApplyDeptName]
,CAST([ApplyTime] AS datetime2) [ApplyTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([ApplyUserId],'00000000-0000-0000-0000-000000000000'))[ApplyUserId]
,[ApplyUserName]
,[AttachmentIds]
,[CompanyCode]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([CompanyId],'00000000-0000-0000-0000-000000000000'))[CompanyId]
,[CompanyName]
,[ConcurrencyStamp]
,CAST([CreationTime] AS datetime2)[CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([CreatorId],'00000000-0000-0000-0000-000000000000'))[CreatorId]
,TRY_CONVERT(UNIQUEIDENTIFIER, IIF([DeleterId] ='','00000000-0000-0000-0000-000000000000',[DeleterId]))[DeleterId]
,CASE WHEN [DeletionTime] = '' THEN '1900-01-01' ELSE [DeletionTime] END AS [DeletionTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([DivisionId],'00000000-0000-0000-0000-000000000000'))[DivisionId]
,[DivisionName]
,TRY_CONVERT(datetime2, [EstDeliveryDate], 120)[EstDeliveryDate]
,[ExemptType]
,[ExtraProperties]
,[GoodsServicesRequested]
,[IsDeleted]
,[JustificationTypeText]
,CASE WHEN [LastModificationTime] = '' THEN '1900-01-01' ELSE [LastModificationTime] END AS [LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, IIF([LastModifierId] = '','00000000-0000-0000-0000-000000000000',[LastModifierId]))[LastModifierId]
,[PRApplicationCode]
,TRY_CONVERT(UNIQUEIDENTIFIER, IIF([PRDetailId] = '','00000000-0000-0000-0000-000000000000',[PRDetailId]))[PRDetailId]
,TRY_CONVERT(UNIQUEIDENTIFIER, IIF([PRId] = '','00000000-0000-0000-0000-000000000000',[PRId]))[PRId]
,[ProjectDescription]
,[ProjectName]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([PurchaserId],'00000000-0000-0000-0000-000000000000'))[PurchaserId]
,[RceNo]
,[Remark]
,[RequisitionAmount]
,[Status]
,[VendorCode]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([VendorId],'00000000-0000-0000-0000-000000000000'))[VendorId]
,[VendorName]
,[WaiveReasonText]
,[WaiveRequest]
,NULL AS [PurchaserName]
,[JustificationTypeId] as [JustificationTypeCode]
,[WaiveReasonId] as [WaiveReasonCode]
,NULL AS [TogetherPRDetailIds]
INTO #PurBWApplications
FROM PLATFORM_ABBOTT_Dev.dbo.PurBWApplications;

--ALTER TABLE Speaker_Portal_Dev.dbo.PurBWApplications ALTER COLUMN AttachmentIds nvarchar(2000) COLLATE SQL_Latin1_General_CP1_CI_AS NULL;
--ALTER TABLE Speaker_Portal_Dev.dbo.PurBWApplications ALTER COLUMN CompanyCode nvarchar(4000) COLLATE SQL_Latin1_General_CP1_CI_AS NULL;
--ALTER TABLE Speaker_Portal_Dev.dbo.PurBWApplications ALTER COLUMN CompanyName nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL;
--ALTER TABLE Speaker_Portal_Dev.dbo.PurBWApplications ALTER COLUMN DivisionName nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL;
--ALTER TABLE Speaker_Portal_Dev.dbo.PurBWApplications ALTER COLUMN WaiveRequest nvarchar(3000) COLLATE SQL_Latin1_General_CP1_CI_AS NULL;

--DROP TABLE #PurBWApplications;

USE Speaker_Portal_Dev;

UPDATE a 
SET
 a.[TransfereeId] = b.TransfereeId
,a.[TransfereeName] = b.TransfereeName
,a.[ApplicationCode] = b.ApplicationCode
,a.[ApplyDeptId] = b.ApplyDeptId
,a.[ApplyDeptName] = b.ApplyDeptName
,a.[ApplyTime] = b.ApplyTime
,a.[ApplyUserId] = b.ApplyUserId
,a.[ApplyUserName] = b.ApplyUserName
,a.[AttachmentIds] = b.AttachmentIds
,a.[CompanyCode] = b.CompanyCode
,a.[CompanyId] = b.CompanyId
,a.[CompanyName] = b.CompanyName
,a.[ConcurrencyStamp] = b.ConcurrencyStamp
,a.[CreationTime] = b.CreationTime
,a.[CreatorId] = b.CreatorId
,a.[DeleterId] = b.DeleterId
,a.[DeletionTime] = b.DeletionTime
,a.[DivisionId] = b.DivisionId
,a.[DivisionName] = b.DivisionName
,a.[EstDeliveryDate] = b.EstDeliveryDate
,a.[ExemptType] = b.ExemptType
,a.[ExtraProperties] = b.ExtraProperties
,a.[GoodsServicesRequested] = b.GoodsServicesRequested
,a.[IsDeleted] = b.IsDeleted
,a.[JustificationTypeText] = b.JustificationTypeText
,a.[LastModificationTime] = b.LastModificationTime
,a.[LastModifierId] = b.LastModifierId
,a.[PRApplicationCode] = b.PRApplicationCode
,a.[PRDetailId] = b.PRDetailId
,a.[PRId] = b.PRId
,a.[ProjectDescription] = b.ProjectDescription
,a.[ProjectName] = b.ProjectName
,a.[PurchaserId] = b.PurchaserId
,a.[RceNo] = b.RceNo
,a.[Remark] = b.Remark
,a.[RequisitionAmount] = b.RequisitionAmount
,a.[Status] = b.Status
,a.[VendorCode] = b.VendorCode
,a.[VendorId] = b.VendorId
,a.[VendorName] = b.VendorName
,a.[WaiveReasonText] = b.WaiveReasonText
,a.[WaiveRequest] = b.WaiveRequest
,a.[PurchaserName] = b.PurchaserName
,a.[JustificationTypeCode] = b.JustificationTypeCode
,a.[WaiveReasonCode] = b.WaiveReasonCode
,a.[TogetherPRDetailIds] = b.TogetherPRDetailIds
FROM dbo.PurBWApplications a
left join #PurBWApplications  b
ON a.id=b.id;

INSERT INTO dbo.PurBWApplications
SELECT
 Id
,TransfereeId
,TransfereeName
,ApplicationCode
,ApplyDeptId
,ApplyDeptName
,ApplyTime
,ApplyUserId
,ApplyUserName
,AttachmentIds
,CompanyCode
,CompanyId
,CompanyName
,ConcurrencyStamp
,CreationTime
,CreatorId
,DeleterId
,DeletionTime
,DivisionId
,DivisionName
,EstDeliveryDate
,ExemptType
,ExtraProperties
,GoodsServicesRequested
,IsDeleted
,JustificationTypeText
,LastModificationTime
,LastModifierId
,PRApplicationCode
,PRDetailId
,PRId
,ProjectDescription
,ProjectName
,PurchaserId
,RceNo
,Remark
,RequisitionAmount
,Status
,VendorCode
,VendorId
,VendorName
,WaiveReasonText
,WaiveRequest
,PurchaserName
,JustificationTypeCode
,WaiveReasonCode
,TogetherPRDetailIds
FROM #PurBWApplications a
WHERE not exists (select * from dbo.PurBWApplications where id=a.id);

--TRUNCATE TABLE dbo.PurBWApplications;