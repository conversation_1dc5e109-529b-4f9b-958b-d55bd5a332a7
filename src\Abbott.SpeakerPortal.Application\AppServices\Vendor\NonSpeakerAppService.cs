﻿using Abbott.SpeakerPortal.Consent;
using Abbott.SpeakerPortal.ConsentServices;
using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Approval;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.Approve;
using Abbott.SpeakerPortal.Contracts.Common.Attachment;
using Abbott.SpeakerPortal.Contracts.Common.PowerApp;
using Abbott.SpeakerPortal.Contracts.Dataverse;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPOApplication;
using Abbott.SpeakerPortal.Contracts.Vendor;
using Abbott.SpeakerPortal.Contracts.Vendor.NonSpeaker;
using Abbott.SpeakerPortal.Contracts.Vendor.NonSpeaker.HCI;
using Abbott.SpeakerPortal.Contracts.Vendor.NonSpeaker.NonHCI;
using Abbott.SpeakerPortal.Contracts.Vendor.NonSpeaker.NonHcpPersonal;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Dataverse.Contracts;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.Common.Attachment;
using Abbott.SpeakerPortal.Entities.Common.Consent;
using Abbott.SpeakerPortal.Entities.Common.Workflow.WorkflowTasks;
using Abbott.SpeakerPortal.Entities.Integration.Dspot;
using Abbott.SpeakerPortal.Entities.VendorApplicationFinancials;
using Abbott.SpeakerPortal.Entities.VendorApplicationOrgnizations;
using Abbott.SpeakerPortal.Entities.VendorApplicationPersonals;
using Abbott.SpeakerPortal.Entities.VendorApplications;
using Abbott.SpeakerPortal.Entities.VendorBlackLists;
using Abbott.SpeakerPortal.Entities.VendorFinancials;
using Abbott.SpeakerPortal.Entities.VendorOrgnizations;
using Abbott.SpeakerPortal.Entities.VendorPersonals;
using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.OEC.BlackList;
using Abbott.SpeakerPortal.Utils;
using Abbott.SpeakerPortal.Vendor.Speaker;

using Microsoft.EntityFrameworkCore.Internal;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;

using MiniExcelLibs;

using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;

using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using Volo.Abp.ObjectMapping;

using IConfiguration = Microsoft.Extensions.Configuration.IConfiguration;

namespace Abbott.SpeakerPortal.AppServices.Vendor
{
    public partial class NonSpeakerAppService : SpeakerPortalAppService, INonSpeakerAppService
    {
        private readonly ILogger<NonSpeakerAppService> _logger;
        private readonly IWorkflowTaskRepository _taskRepository;
        readonly IConfiguration _configuration;
        private IDataverseService _dataverseService;
        private readonly ICommonService _commonService;

        public NonSpeakerAppService(IServiceProvider serviceProvider)
        {
            _logger = serviceProvider.GetService<ILogger<NonSpeakerAppService>>();
            _configuration = serviceProvider.GetService<IConfiguration>();
            _dataverseService = serviceProvider.GetService<IDataverseService>();
            _commonService = serviceProvider.GetService<ICommonService>();
        }

        /// <summary>
        /// 获取非Hcp-个人信息列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<NonHcpPersonalListResponseDto>> GetNonHcpPersonalListAsync(NonHcpPersonalListRequestDto request, bool isPaging = true)
        {
            var vendorQuery = await LazyServiceProvider.LazyGetService<IVendorReadonlyRepository>().GetQueryableAsync();
            var vendorPersonalQuery = await LazyServiceProvider.LazyGetService<IVendorPersonalReadonlyRepository>().GetQueryableAsync();
            var vendorApplication = await LazyServiceProvider.LazyGetService<IVendorApplicationReadonlyRepository>().GetQueryableAsync();

            //查询我相关的草稿
            var myList = new List<string>();
            if (request.OnlyMine)
            {
                myList = vendorApplication.Where(w => w.ApplyUserId == CurrentUser.Id && w.Status != Statuses.Saved && w.Status != Statuses.Delete && w.Status != Statuses.Rejected).Select(s => s.VendorCode).ToList();
                if (!myList.Any())
                    return new PagedResultDto<NonHcpPersonalListResponseDto>(0, []);
            }

            var query = vendorQuery.Where(a => a.VendorType == VendorTypes.NonHCPPerson).Join(vendorPersonalQuery, a => a.Id, a => a.VendorId, (a, b) => new { Vendor = a, Personal = b })
                .WhereIf(myList.Count > 0, p => myList.Contains(p.Vendor.VendorCode))
                .WhereIf(!string.IsNullOrEmpty(request.VendorCode), a => a.Vendor.VendorCode == request.VendorCode)
                .WhereIf(!string.IsNullOrEmpty(request.VendorName), a => a.Personal.SPName == request.VendorName)
                .WhereIf(request.Status.HasValue, a => a.Vendor.Status == request.Status)
                .WhereIf(!string.IsNullOrEmpty(request.AffiliationOrgan), a => a.Personal.AffiliationOrgan.Contains(request.AffiliationOrgan))
                //.WhereIf(request.OnlyMine, a => a.Vendor.CreatorId == CurrentUser.Id)
                .OrderByDescending(o => o.Vendor.CreationTime);

            //var status = EnumUtil.GetEnumIdValues<VendorStatus>().ToDictionary(a => (VendorStatus)a.Key, a => a.Value);

            var count = query.Count();

            if (!isPaging)
            {
                request.PageIndex = 0;
                request.PageSize = count;
            }

            var datas = query.Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToArray().Select(a => new NonHcpPersonalListResponseDto
            {
                Id = a.Vendor.Id,
                VendorCode = a.Vendor.VendorCode,
                VendorName = a.Personal.SPName,
                AffiliationOrgan = a.Personal.AffiliationOrgan,
                //Status = status[a.Vendor.Status]
                Status = (int)a.Vendor.Status,
                OpenId = a.Vendor.OpenId,
                UserId = a.Vendor.UserId.ToString()
            }).ToArray();

            // 获取签署内容
            var _configuration = LazyServiceProvider.GetService<IConfiguration>();
            var _consentsetvice = LazyServiceProvider.GetService<IConsentService>();
            var consentSignedQuery = await LazyServiceProvider.LazyGetService<IConsentSignedReadonlyRepository>().GetListAsync();
            //获取需要检查的最新版签署版本号
            string consentInfo = _configuration["Consent:ConsentCode"];
            var consentResponse = await _consentsetvice.GetConsentInfoAsync([.. consentInfo.Split(",")]);
            foreach (var item in datas)
            {
                //审批状态
                var approving = vendorApplication.FirstOrDefault(w => w.VendorCode == item.VendorCode && w.Status == Statuses.Approving);
                item.IsApprove = approving != null;
                item.IsApproveString = approving != null ? "审批中" : "";

                //获取讲者签署记录，判断是否已经完全签署
                var consentList = consentSignedQuery.Where(w => w.OneId == item.OpenId || w.AppUserId == item.UserId).ToList();
                var isConsent = HasBeenFullySigned(consentList, consentResponse);
                item.SignedStatus = isConsent;
                item.SignedStatusString = isConsent ? "已签署" : "待签署";
                item.OpenId = "";//安全起见不返回该内容
            }

            var result = new PagedResultDto<NonHcpPersonalListResponseDto>(count, datas);
            return result;
        }

        /// <summary>
        /// 获取HCI机构列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<HCIListResponseDto>> GetHCIListAsync(HCIListRequestDto request, bool isPaging = true)
        {
            var vendorQuery = await LazyServiceProvider.LazyGetService<IVendorReadonlyRepository>().GetQueryableAsync();
            var vendorOrgQuery = await LazyServiceProvider.LazyGetService<IVendorOrgnizationReadonlyRepository>().GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var vendorBlackQuery = await LazyServiceProvider.LazyGetService<IVendorBlackListReadonlyRepository>().GetNoFilterQueryable();
            var vendorApplication = await LazyServiceProvider.LazyGetService<IVendorApplicationReadonlyRepository>().GetQueryableAsync();

            //查询我相关的草稿
            var myList = new List<string>();
            if (request.OnlyMine)
                myList = vendorApplication.Where(w => w.ApplyUserId == CurrentUser.Id && w.Status != Statuses.Saved && w.Status != Statuses.Delete && w.Status != Statuses.Rejected).Select(s => s.VendorCode).ToList();

            var provinceCode = string.Empty;
            var cityCode = string.Empty;
            if (request.Region != null)
            {
                if (request.Region.Count > 0)
                    provinceCode = request.Region[0];
                if (request.Region.Count > 1)
                    cityCode = request.Region[1];
            }

            var query = vendorQuery.Where(a => a.VendorType == VendorTypes.HCIAndOtherInstitutionsAR).Join(vendorOrgQuery, a => a.Id, a => a.VendorId, (a, b) => new { Vendor = a, Org = b })
                .WhereIf(myList.Count > 0, p => myList.Contains(p.Vendor.VendorCode))
                .WhereIf(!string.IsNullOrEmpty(request.VendorCode), a => a.Vendor.VendorCode.Contains(request.VendorCode))
                .WhereIf(!string.IsNullOrEmpty(request.VendorName), a => a.Org.VendorName.Contains(request.VendorName))
                .WhereIf(request.Status.HasValue, a => a.Vendor.Status == request.Status)
                .WhereIf(!string.IsNullOrEmpty(request.OrgType), a => a.Org.OrgType == request.OrgType)
                .WhereIf(!string.IsNullOrEmpty(provinceCode), a => a.Org.Province == provinceCode)
                .WhereIf(!string.IsNullOrEmpty(cityCode), a => a.Org.City == cityCode)
                //.WhereIf(request.OnlyMine, a => a.Vendor.CreatorId == CurrentUser.Id)
                .OrderByDescending(o => o.Vendor.CreationTime);

            //var status = EnumUtil.GetEnumIdValues<VendorStatus>().ToDictionary(a => (VendorStatus)a.Key, a => a.Value);

            var count = query.Count();

            if (!isPaging)
            {
                request.PageIndex = 0;
                request.PageSize = count;
            }

            var provinces = await dataverseService.GetAllProvince(stateCode: null);
            var cities = await dataverseService.GetAllCity(stateCode: null);
            var orgTypes = await dataverseService.GetDictionariesAsync(DictionaryType.RegisteredCertificateAuthorityType, stateCode: null);

            var pageDatas = query.Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToArray();

            //查询黑名单
            var vendorBlackAndVendorQuery = vendorBlackQuery.Where(w => w.Status == BlackStatus.Effective || w.Status == BlackStatus.ToBeEffective)
                .Where(w => pageDatas.Select(s => s.Vendor.Id).Contains(w.VendorId)).ToList();

            var datas = pageDatas.Select(a => new HCIListResponseDto
            {
                Id = a.Vendor.Id,
                VendorCode = a.Vendor.VendorCode,
                VendorName = a.Org.VendorName,
                //Status = status[a.Vendor.Status],
                isInBlackList = vendorBlackAndVendorQuery.Select(s => s.VendorId).Contains(a.Vendor.Id),
                Status = (int)a.Vendor.Status,
                OrgType = orgTypes.FirstOrDefault(x => x.Code == a.Org.OrgType)?.Name,
                Province = provinces.FirstOrDefault(x => x.Code == a.Org.Province)?.Name,
                City = cities.FirstOrDefault(x => x.Code == a.Org.City)?.Name
            }).ToArray();

            foreach (var item in datas)
            {
                if (vendorBlackAndVendorQuery.Select(s => s.VendorId).Contains(item.Id))
                {
                    item.isInBlackList = true;
                    var entity = vendorBlackAndVendorQuery.First(f => f.VendorId == item.Id);
                    if (entity.StartDate < DateTime.Now && entity.EndDate > DateTime.Now)
                    {
                        item.Status = (int)VendorStatus.Exception;
                    }
                }
                //审批状态
                var approving = vendorApplication.FirstOrDefault(w => w.VendorCode == item.VendorCode && w.Status == Statuses.Approving);
                item.IsApprove = approving != null;
                item.IsApproveString = approving != null ? "审批中" : "";
            }

            var result = new PagedResultDto<HCIListResponseDto>(count, datas);
            return result;
        }

        /// <summary>
        /// 获取非HCI机构列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<NonHCIListResponseDto>> GetNonHCIListAsync(NonHCIListRequestDto request, bool isPaging = true)
        {
            var vendorQuery = await LazyServiceProvider.LazyGetService<IVendorReadonlyRepository>().GetQueryableAsync();
            var vendorOrgQuery = await LazyServiceProvider.LazyGetService<IVendorOrgnizationReadonlyRepository>().GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var vendorApplication = await LazyServiceProvider.LazyGetService<IVendorApplicationReadonlyRepository>().GetQueryableAsync();

            //查询我相关的草稿
            var myList = new List<string>();
            if (request.OnlyMine)
                myList = vendorApplication.Where(w => w.ApplyUserId == CurrentUser.Id && w.Status != Statuses.Saved && w.Status != Statuses.Delete && w.Status != Statuses.Rejected).Select(s => s.VendorCode).ToList();

            var provinceCode = string.Empty;
            var cityCode = string.Empty;
            if (request.Region != null)
            {
                if (request.Region.Count > 0)
                    provinceCode = request.Region[0];
                if (request.Region.Count > 1)
                    cityCode = request.Region[1];
            }

            var query = vendorQuery.Where(a => a.VendorType == VendorTypes.NonHCIInstitutionalAP).Join(vendorOrgQuery, a => a.Id, a => a.VendorId, (a, b) => new { Vendor = a, Org = b })
                .WhereIf(myList.Count > 0, p => myList.Contains(p.Vendor.VendorCode))
                .WhereIf(!string.IsNullOrEmpty(request.VendorCode), a => a.Vendor.VendorCode.Contains(request.VendorCode))
                .WhereIf(!string.IsNullOrEmpty(request.VendorName), a => a.Org.VendorName.Contains(request.VendorName))
                .WhereIf(request.Status.HasValue, a => a.Vendor.Status == request.Status)
                .WhereIf(!string.IsNullOrEmpty(provinceCode), a => a.Org.Province == provinceCode)
                .WhereIf(!string.IsNullOrEmpty(cityCode), a => a.Org.City == cityCode)
                .WhereIf(request.IsAPS == true, a => !string.IsNullOrEmpty(a.Vendor.ApsPorperty))
                .WhereIf(request.IsAPS == false, a => string.IsNullOrEmpty(a.Vendor.ApsPorperty))
                //.WhereIf(request.OnlyMine, a => a.Vendor.CreatorId == CurrentUser.Id)
                .WhereIf(!string.IsNullOrEmpty(request.ApsPorperty), a => a.Vendor.ApsPorperty.Contains(request.ApsPorperty))
                .OrderByDescending(o => o.Vendor.CreationTime);

            //var status = EnumUtil.GetEnumIdValues<VendorStatus>().ToDictionary(a => (VendorStatus)a.Key, a => a.Value);

            var count = query.Count();

            if (!isPaging)
            {
                request.PageIndex = 0;
                request.PageSize = count;
            }

            var provinces = await dataverseService.GetAllProvince(stateCode: null);
            var cities = await dataverseService.GetAllCity(stateCode: null);
            var apsProperty = await dataverseService.GetDictionariesAsync(DictionaryType.ApsProperty, stateCode: null);
            var datas = query.Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToArray().Select(a =>
            {
                List<string> apsPropertyNames = [];
                if (!string.IsNullOrEmpty(a.Vendor.ApsPorperty))
                {
                    var apsProperties = a.Vendor.ApsPorperty.Split(',');
                    apsProperties.ToList().ForEach(x =>
                    {
                        var apsName = apsProperty?.FirstOrDefault(p => p.Code == x)?.Name;
                        if (!string.IsNullOrEmpty(apsName))
                            apsPropertyNames.Add(apsName);
                    });
                }
                return new NonHCIListResponseDto
                {
                    Id = a.Vendor.Id,
                    VendorCode = a.Vendor.VendorCode,
                    VendorName = a.Org.VendorName,
                    //ApsPorperty = a.Vendor.ApsPorperty,
                    ApsPorperty = apsPropertyNames.JoinAsString(","),
                    //Status = status[a.Vendor.Status],
                    Status = (int)a.Vendor.Status,
                    Province = provinces.FirstOrDefault(x => x.Code == a.Org.Province)?.Name,
                    City = cities.FirstOrDefault(x => x.Code == a.Org.City)?.Name,
                    IsAPS = string.IsNullOrEmpty(a.Vendor.ApsPorperty) ? "否" : "是"
                };
            }).ToArray();

            foreach (var item in datas)
            {
                //审批状态
                var approving = vendorApplication.FirstOrDefault(w => w.VendorCode == item.VendorCode && w.Status == Statuses.Approving);
                item.IsApprove = approving != null;
                item.IsApproveString = approving != null ? "审批中" : "";
            }
            var result = new PagedResultDto<NonHCIListResponseDto>(count, datas);
            return result;
        }


        #region 非讲者供应商草稿列表/非讲者供应商列表导出
        /// <summary>
        /// 获取非HCP-个人信息草稿列表
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<PagedResultDto<NonHcpPersonalListResponseDto>> GetNonHcpPersonalDraftListAsync(NonHcpPersonalDraftListRequestDto request, bool isPaging = true)
        {
            var vendorQuery = await LazyServiceProvider.LazyGetService<IVendorApplicationReadonlyRepository>().GetQueryableAsync();
            var vendorPersonalQuery = await LazyServiceProvider.LazyGetService<IVendorApplicationPersonalReadonlyRepository>().GetQueryableAsync();

            var query = vendorQuery
                .Where(a => a.VendorType == VendorTypes.NonHCPPerson && a.Status == Statuses.Saved && a.ApplyUserId == CurrentUser.Id)
                .Join(vendorPersonalQuery, a => a.Id, a => a.ApplicationId, (a, b) => new { Vendor = a, Personal = b })
                .WhereIf(request.ApplicationType.HasValue, a => a.Vendor.ApplicationType == request.ApplicationType.Value)
                .WhereIf(!string.IsNullOrEmpty(request.VendorCode), a => a.Vendor.VendorCode.Contains(request.VendorCode))
                .WhereIf(!string.IsNullOrEmpty(request.VendorName), a => a.Personal.SPName.Contains(request.VendorName))
                .WhereIf(!string.IsNullOrEmpty(request.AffiliationOrgan), a => a.Personal.AffiliationOrgan.Contains(request.AffiliationOrgan))
                .OrderByDescending(o => o.Vendor.CreationTime);

            var status = EnumUtil.GetEnumIdValues<Statuses>().ToDictionary(a => (Statuses)a.Key, a => a.Value);

            var count = query.Count();

            if (!isPaging)
            {
                request.PageIndex = 0;
                request.PageSize = count;
            }

            var datas = query.Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToArray().Select(a => new NonHcpPersonalListResponseDto
            {
                Id = a.Vendor.Id,
                VendorCode = a.Vendor.VendorCode,
                VendorName = a.Personal.SPName,
                AffiliationOrgan = a.Personal.AffiliationOrgan,
                //Status = status[a.Vendor.Status]
                BImproved = a.Vendor.BImproved,
                BImprovedString = a.Vendor.BImproved ? "已完善" : "待完善",
                Status = (int)a.Vendor.Status,
                BAuth = a.Vendor.BAuth,
                OpenId = a.Vendor.OpenId,
                UserId = a.Vendor.UserId.ToString(),
                ApplicationType = a.Vendor.ApplicationType
            }).ToArray();

            // 获取签署内容
            var _configuration = LazyServiceProvider.GetService<IConfiguration>();
            var _consentsetvice = LazyServiceProvider.GetService<IConsentService>();
            var consentSignedQuery = await LazyServiceProvider.LazyGetService<IConsentSignedReadonlyRepository>().GetListAsync();
            //获取需要检查的最新版签署版本号
            string consentInfo = _configuration["Consent:ConsentCode"];
            var consentResponse = await _consentsetvice.GetConsentInfoAsync([.. consentInfo.Split(",")]);
            foreach (var item in datas)
            {
                //获取讲者签署记录，判断是否已经完全签署
                var consentList = consentSignedQuery.Where(w => w.OneId == item.OpenId || w.AppUserId == item.UserId).ToList();
                var isConsent = HasBeenFullySigned(consentList, consentResponse);
                item.SignedStatus = isConsent;
                item.SignedStatusString = isConsent ? "已签署" : "待签署";
                item.OpenId = "";//安全起见不返回该内容
            }

            var result = new PagedResultDto<NonHcpPersonalListResponseDto>(count, datas);
            return result;
        }

        /// <summary>
        /// 检查是否完全签署
        /// </summary>
        /// <returns></returns>
        private static bool HasBeenFullySigned(List<ConsentSigned> consentList, List<ConsentUrlResponseDto> consentResponse)
        {
            var isConsent = false;
            if (consentList.Count != 0)
            {
                isConsent = true;
                foreach (var consent in consentResponse)
                {
                    var entity = consentList.OrderByDescending(o => o.CreationTime).FirstOrDefault(w => w.ConsentCode == consent.Data.ConsentCode);
                    //如果不存在说明没有签署，直接返回
                    if (entity == null)
                    {
                        isConsent = false;
                        break;
                    }
                    //如果存在但是版本不一样，说明签署的是老版本，返回false
                    if (entity.ConsentVersion != consent.Data.Version.ToString())
                    {
                        isConsent = false;
                        break;
                    }
                }
            }
            return isConsent;
        }


        /// <summary>
        /// 获取HCI机构草稿列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<HCIListResponseDto>> GetHciDraftListAsync(HciDraftListRequestDto request, bool isPaging = true)
        {
            var vendorQuery = await LazyServiceProvider.LazyGetService<IVendorApplicationReadonlyRepository>().GetQueryableAsync();
            var vendorOrgQuery = await LazyServiceProvider.LazyGetService<IVendorApplicationOrganizationReadonlyRepository>().GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();

            var provinceCode = string.Empty;
            var cityCode = string.Empty;
            if (request.Region != null)
            {
                if (request.Region.Count > 0)
                    provinceCode = request.Region[0];
                if (request.Region.Count > 1)
                    cityCode = request.Region[1];
            }

            var query = vendorQuery
                .Where(a => a.VendorType == VendorTypes.HCIAndOtherInstitutionsAR && a.Status == Statuses.Saved && a.ApplyUserId == CurrentUser.Id)
                .Join(vendorOrgQuery, a => a.Id, a => a.ApplicationId, (a, b) => new { Vendor = a, Org = b })
                .WhereIf(request.ApplicationType.HasValue, a => a.Vendor.ApplicationType == request.ApplicationType.Value)
                .WhereIf(!string.IsNullOrEmpty(request.VendorCode), a => a.Vendor.VendorCode.Contains(request.VendorCode))
                .WhereIf(!string.IsNullOrEmpty(request.VendorName), a => a.Org.VendorName.Contains(request.VendorName))
                .WhereIf(!string.IsNullOrEmpty(request.OrgType), a => a.Org.OrgType == request.OrgType)
                .WhereIf(!string.IsNullOrEmpty(provinceCode), a => a.Org.Province == provinceCode)
                .WhereIf(!string.IsNullOrEmpty(cityCode), a => a.Org.City == cityCode)
                .OrderByDescending(o => o.Vendor.CreationTime);

            //var status = EnumUtil.GetEnumIdValues<Statuses>().ToDictionary(a => (Statuses)a.Key, a => a.Value);

            var count = query.Count();

            if (!isPaging)
            {
                request.PageIndex = 0;
                request.PageSize = count;
            }

            var provinces = await dataverseService.GetAllProvince(stateCode: null);
            var cities = await dataverseService.GetAllCity(stateCode: null);
            var orgTypes = await dataverseService.GetDictionariesAsync(DictionaryType.RegisteredCertificateAuthorityType, stateCode: null);

            var datas = query.Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToArray().Select(a => new HCIListResponseDto
            {
                Id = a.Vendor.Id,
                VendorCode = a.Vendor.VendorCode,
                VendorName = a.Org.VendorName,
                //Status = status[a.Vendor.Status],
                Status = (int)a.Vendor.Status,
                OrgType = orgTypes.FirstOrDefault(x => x.Code == a.Org.OrgType)?.Name,
                Province = provinces.FirstOrDefault(x => x.Code == a.Org.Province)?.Name,
                City = cities.FirstOrDefault(x => x.Code == a.Org.City)?.Name,
                ApplicationType = a.Vendor.ApplicationType
            }).ToArray();

            var result = new PagedResultDto<HCIListResponseDto>(count, datas);
            return result;
        }

        /// <summary>
        /// 获取非HCI机构草稿列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<NonHCIListResponseDto>> GetNonHciDraftListAsync(NonHciDraftListRequestDto request, bool isPaging = true)
        {
            var vendorQuery = await LazyServiceProvider.LazyGetService<IVendorApplicationReadonlyRepository>().GetQueryableAsync();
            var vendorOrgQuery = await LazyServiceProvider.LazyGetService<IVendorApplicationOrganizationReadonlyRepository>().GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();

            var provinceCode = string.Empty;
            var cityCode = string.Empty;
            if (request.Region != null)
            {
                if (request.Region.Count > 0)
                    provinceCode = request.Region[0];
                if (request.Region.Count > 1)
                    cityCode = request.Region[1];
            }

            var query = vendorQuery
                .Where(a => a.VendorType == VendorTypes.NonHCIInstitutionalAP && a.Status == Statuses.Saved && a.CreatorId == CurrentUser.Id)
                .Join(vendorOrgQuery, a => a.Id, a => a.ApplicationId, (a, b) => new { Vendor = a, Org = b })
                .WhereIf(request.ApplicationType.HasValue, a => a.Vendor.ApplicationType == request.ApplicationType.Value)
                .WhereIf(!string.IsNullOrEmpty(request.VendorCode), a => a.Vendor.VendorCode.Contains(request.VendorCode))
                .WhereIf(!string.IsNullOrEmpty(request.VendorName), a => a.Org.VendorName.Contains(request.VendorName))
                .WhereIf(!string.IsNullOrEmpty(provinceCode), a => a.Org.Province == provinceCode)
                .WhereIf(!string.IsNullOrEmpty(cityCode), a => a.Org.City == cityCode)
                .WhereIf(request.IsAPS == true, a => !string.IsNullOrEmpty(a.Vendor.ApsPorperty))
                .WhereIf(request.IsAPS == false, a => string.IsNullOrEmpty(a.Vendor.ApsPorperty))
                .OrderByDescending(o => o.Vendor.CreationTime);

            //var status = EnumUtil.GetEnumIdValues<Statuses>().ToDictionary(a => (Statuses)a.Key, a => a.Value);

            var count = query.Count();

            if (!isPaging)
            {
                request.PageIndex = 0;
                request.PageSize = count;
            }

            var provinces = await dataverseService.GetAllProvince(stateCode: null);
            var cities = await dataverseService.GetAllCity(stateCode: null);

            var datas = query.Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToArray().Select(a => new NonHCIListResponseDto
            {
                Id = a.Vendor.Id,
                VendorCode = a.Vendor.VendorCode,
                VendorName = a.Org.VendorName,
                Status = (int)a.Vendor.Status,
                Province = provinces.FirstOrDefault(x => x.Code == a.Org.Province)?.Name,
                City = cities.FirstOrDefault(x => x.Code == a.Org.City)?.Name,
                IsAPS = string.IsNullOrEmpty(a.Vendor.ApsPorperty) ? "否" : "是",
                ApplicationType = a.Vendor.ApplicationType
            }).ToArray();

            var result = new PagedResultDto<NonHCIListResponseDto>(count, datas);
            return result;
        }
        #endregion

        /// <summary>
        /// 非讲者供应商列表导出Excel
        /// 草稿列表不导出
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<Stream> ExportVendorNonSpeaker(ExportExcelRequestDto request)
        {
            MemoryStream stream = new();
            List<object> list = [];
            object objData = null;
            switch (request.ExportVendorType)
            {
                case VendorNonSpeaker.EnumExportVendorType.NonHcpList:
                    objData = JsonConvert.DeserializeObject<NonHcpPersonalListRequestDto>(request.Search.ToString());
                    list.AddRange((await GetNonHcpPersonalListAsync(objData as NonHcpPersonalListRequestDto, false)).Items);
                    break;
                case VendorNonSpeaker.EnumExportVendorType.HciList:
                    objData = JsonConvert.DeserializeObject<HCIListRequestDto>(request.Search.ToString());
                    list.AddRange((await GetHCIListAsync(objData as HCIListRequestDto, false)).Items);
                    break;
                case VendorNonSpeaker.EnumExportVendorType.NonHciList:
                    objData = JsonConvert.DeserializeObject<NonHCIListRequestDto>(request.Search.ToString());
                    list.AddRange((await GetNonHCIListAsync(objData as NonHCIListRequestDto, false)).Items);
                    break;
                case VendorNonSpeaker.EnumExportVendorType.NonHcpDraftList:
                    objData = JsonConvert.DeserializeObject<NonHcpPersonalDraftListRequestDto>(request.Search.ToString());
                    list.AddRange((await GetNonHcpPersonalDraftListAsync(objData as NonHcpPersonalDraftListRequestDto, false)).Items);
                    break;
                case VendorNonSpeaker.EnumExportVendorType.HciDraftList:
                    objData = JsonConvert.DeserializeObject<HciDraftListRequestDto>(request.Search.ToString());
                    list.AddRange((await GetHciDraftListAsync(objData as HciDraftListRequestDto, false)).Items);
                    break;
                case VendorNonSpeaker.EnumExportVendorType.NonHciDraftList:
                    objData = JsonConvert.DeserializeObject<NonHciDraftListRequestDto>(request.Search.ToString());
                    list.AddRange((await GetNonHciDraftListAsync(objData as NonHciDraftListRequestDto, false)).Items);
                    break;
                default:
                    break;
            }
            stream.SaveAs(list, true, "SheetName");
            stream.Seek(0, SeekOrigin.Begin);
            return stream;
        }

        #region 新建
        /// <summary>
        /// 新建非讲者供应商申请-非HCP个人
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> CreateApplicationNonHcp(NonHcpCreateApplicationRequestDto request)
        {
            try
            {
                var vendorRepository = LazyServiceProvider.LazyGetService<IVendorRepository>();
                var vendorApplicationQuery = LazyServiceProvider.LazyGetService<IVendorApplicationRepository>();
                var vendorApplicationPersonalQuery = LazyServiceProvider.LazyGetService<IVendorApplicationPersonalRepository>();
                var vendorApplicationFinanceQuery = LazyServiceProvider.LazyGetService<IVendorApplicationFinancialRepository>();
                var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
                var commonService = LazyServiceProvider.LazyGetService<ICommonService>();



                //新增 供应商申请
                var vendorApplicationData = ObjectMapper.Map<NonHcpCreateApplicationRequestDto, Entities.VendorApplications.VendorApplication>(request);

                vendorApplicationData.VendorType = VendorTypes.NonHCPPerson;

                #region 验证数据
                //1.变更或者激活时，VendorCode应当存在于Vendor表中
                //2.相同的提交人，相同的ApplicationType且状态为审批结束之前的状态的申请，不应存在多条
                #endregion

                //vendorApplicationData.ApplicationCode = await GetSerialNoAsync(vendorApplicationQuery, "V");
                //vendorApplicationData.CreatorId = CurrentUser.Id;
                vendorApplicationData.Status = Statuses.Saved;

                //if (request.ApplicationType == ApplicationTypes.Create)
                //    vendorApplicationData.VendorCode = await GetSerialNoAsync(vendorApplicationQuery, "SP");

                #region 根据单据上选择的申请组织ID获取对应的名称、BU和全链路组织名称
                var orgs = await dataverseService.GetOrganizations();
                var orgDto = orgs.FirstOrDefault(x => x.Id == Guid.Parse(vendorApplicationData.ApplyUserBu));
                if (orgDto != null)
                {
                    var orgTree2BU = await commonService.GetParentOrgs(orgDto, orgs);
                    vendorApplicationData.ApplyDeptName = orgTree2BU.First().DepartmentName;
                    vendorApplicationData.ApplyBuId = orgTree2BU.Last().Id;
                    vendorApplicationData.ApplyBuName = orgTree2BU.Last().DepartmentName;
                    vendorApplicationData.ApplyUserBuName = orgTree2BU.Select(x => x.DepartmentName).Reverse().JoinAsString("-");
                }
                #endregion

                //附件
                if (request.AttachmentInformation == null || request.AttachmentInformation.Count < 1)
                    vendorApplicationData.AttachmentInformation = null;
                else
                    vendorApplicationData.AttachmentInformation = request.AttachmentInformation?.Select(x => x.AttachmentId.ToString()).JoinAsString(",");

                //var vendorApplicationEntity = await vendorApplicationQuery.InsertAsync(vendorApplicationData);

                await InsertAndGenerateSerialNoAsync(vendorApplicationQuery, vendorApplicationData);

                //新增 供应商申请个人信息
                var vendorApplicationPersonalData = ObjectMapper.Map<NonHcpCreateApplicationRequestDto, VendorApplicationPersonal>(request);
                vendorApplicationPersonalData.ApplicationId = vendorApplicationData.Id;
                vendorApplicationPersonalData = await vendorApplicationPersonalQuery.InsertAsync(vendorApplicationPersonalData);

                //新增 供应商申请财务信息
                var vendorTypeCfgs = await dataverseService.GetVendorTypeCfgAsync();
                if (request.FinanceInfo?.Count > 0)
                {
                    var vendorApplicationFinanceDataList = ObjectMapper.Map<List<FinancialInformation>, List<VendorApplicationFinancial>>(request.FinanceInfo);
                    vendorApplicationFinanceDataList.ForEach(x =>
                    {
                        x.ApplicationId = vendorApplicationData.Id;
                        if (x.FinancialVendorStatus == 0)
                            x.FinancialVendorStatus = FinancialVendorStatus.ToBeEffective;
                        if (string.IsNullOrEmpty(x.PaymentTerm))
                            x.PaymentTerm = vendorTypeCfgs.FirstOrDefault(f => f.VendorType == ((int)VendorTypes.NonHCPPerson).ToString())?.PaymentTerms;
                    });
                    await vendorApplicationFinanceQuery.InsertManyAsync(vendorApplicationFinanceDataList);
                }

                return await Task.FromResult(MessageResult.SuccessResult(vendorApplicationData.Id));
            }
            catch (Exception ex)
            {
                _logger.LogError($"NonSpeakerAppService's CreateApplicationNonHcp has an error : {ex.Message}");
                return await Task.FromResult(MessageResult.FailureResult(new MessageModelBase(500, $"Create Application with NonHCP Failed! {ex.Message}")));
            }
        }

        /// <summary>
        /// 新建非讲者供应商申请-HCI机构
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> CreateApplicationHci(NonSpeakerApplicationOrganizationDto request)
        {
            try
            {
                var vendorRepository = LazyServiceProvider.LazyGetService<IVendorRepository>();
                var vendorApplicationQuery = LazyServiceProvider.LazyGetService<IVendorApplicationRepository>();
                var vendorApplicationOrgQuery = LazyServiceProvider.LazyGetService<IVendorApplicationOrganizationRepository>();
                var vendorApplicationFinanceQuery = LazyServiceProvider.LazyGetService<IVendorApplicationFinancialRepository>();
                var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");
                var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
                var commonService = LazyServiceProvider.LazyGetService<ICommonService>();

                //新增 供应商申请
                var vendorApplicationData = ObjectMapper.Map<NonSpeakerApplicationOrganizationDto, Entities.VendorApplications.VendorApplication>(request);
                vendorApplicationData.ApplyUserId = request.ApplyUserId;
                vendorApplicationData.ApplyUserBu = request.ApplyUserBu.ToString();
                vendorApplicationData.VendorType = VendorTypes.HCIAndOtherInstitutionsAR;
                vendorApplicationData.HandPhone = request.ContactPhone ?? string.Empty;

                //vendorApplicationData.ApplicationCode = await GetSerialNoAsync(vendorApplicationQuery, "V");
                //if (request.ApplicationType == ApplicationTypes.Create)
                //    vendorApplicationData.VendorCode = await GetSerialNoAsync(vendorApplicationQuery, "SP");

                #region 根据单据上选择的申请组织ID获取对应的名称、BU和全链路组织名称
                var orgs = await dataverseService.GetOrganizations();
                var orgDto = orgs.FirstOrDefault(x => x.Id == Guid.Parse(vendorApplicationData.ApplyUserBu));
                if (orgDto != null)
                {
                    var orgTree2BU = await commonService.GetParentOrgs(orgDto, orgs);
                    vendorApplicationData.ApplyDeptName = orgTree2BU.First().DepartmentName;
                    vendorApplicationData.ApplyBuId = orgTree2BU.Last().Id;
                    vendorApplicationData.ApplyBuName = orgTree2BU.Last().DepartmentName;
                    vendorApplicationData.ApplyUserBuName = orgTree2BU.Select(x => x.DepartmentName).Reverse().JoinAsString("-");
                }
                #endregion

                //如果是变更和激活版本加一
                if (request.ApplicationType != ApplicationTypes.Create)
                {
                    //如果是更新或激活草稿添加，判断VendorCode是否存在
                    var vendorOld = await vendorRepository.FirstOrDefaultAsync(f => f.VendorCode == request.VendorCode);
                    if (vendorOld == null) return MessageResult.FailureResult($"{request.VendorCode}数据不存在于正式库，修改时请填写VendorCode");
                    //版本增加
                    vendorApplicationData.DraftVersion = int.Parse(request.DraftVersion) + 1;
                    //记录上一版数据内容
                    var getSpeakerResponse = await GetOrganizationDetailHci(vendorOld.Id);
                    if (getSpeakerResponse.Success)
                    {
                        var speaker = getSpeakerResponse.Data as VendorOrganizationDetailResponseDto;
                        //加密
                        if (!string.IsNullOrEmpty(speaker.BankCardNo))
                            speaker.BankCardNo = AesHelper.Encryption(speaker.BankCardNo, insightKey);
                        vendorApplicationData.UpdatePreJson = JsonConvert.SerializeObject(speaker);
                    }
                }

                //vendorApplicationData.CreatorId = CurrentUser.Id;
                vendorApplicationData.Status = Statuses.Saved;
                vendorApplicationData.BankCardImg = request.BankCardImg?.AttachmentId.ToString();
                if (request.BankCity != null && request.BankCity.Length > 0)
                    vendorApplicationData.BankCity = request.BankCity?.JoinAsString(",");

                //附件
                if (request.AttachmentInformation == null || request.AttachmentInformation.Count < 1)
                    vendorApplicationData.AttachmentInformation = null;
                else
                    vendorApplicationData.AttachmentInformation = request.AttachmentInformation?.Select(x => x.AttachmentId.ToString()).JoinAsString(",");

                if (!string.IsNullOrEmpty(vendorApplicationData.BankCardNo))
                {
                    var key = _configuration.GetValue<string>("ApplicationInsightKey");
                    vendorApplicationData.BankCardNo = AesHelper.Encryption(vendorApplicationData.BankCardNo, key);
                }

                //var vendorApplicationEntity = await vendorApplicationQuery.InsertAsync(vendorApplicationData, true);
                await InsertAndGenerateSerialNoAsync(vendorApplicationQuery, vendorApplicationData);

                //新增 供应商申请机构信息
                if (request.ProvinceCity != null && request.ProvinceCity.Length > 0)
                    request.Province = request.ProvinceCity[0];
                if (request.ProvinceCity != null && request.ProvinceCity.Length > 1)
                    request.City = request.ProvinceCity[1];
                var vendorApplicationOrgData = ObjectMapper.Map<NonSpeakerApplicationOrganizationDto, VendorApplicationOrganization>(request);
                vendorApplicationOrgData.ApplicationId = vendorApplicationData.Id;
                vendorApplicationOrgData = await vendorApplicationOrgQuery.InsertAsync(vendorApplicationOrgData);

                //新增 供应商申请财务信息
                var vendorTypeCfgs = await dataverseService.GetVendorTypeCfgAsync();
                if (request.FinanceInfo?.Count > 0)
                {
                    var vendorApplicationFinanceDataList = ObjectMapper.Map<List<FinancialInformation>, List<VendorApplicationFinancial>>(request.FinanceInfo);
                    vendorApplicationFinanceDataList.ForEach(x =>
                    {
                        x.ApplicationId = vendorApplicationData.Id;
                        if (x.FinancialVendorStatus == 0)
                            x.FinancialVendorStatus = FinancialVendorStatus.ToBeEffective;
                        //HCI变更，PaymentTerm前端选择
                        //payment_term特殊处理下,前端传的是[79_45_45]格式
                        if (x.PaymentTerm.Contains("_"))
                        {
                            var paymentTerms = x.PaymentTerm.Split("_");
                            if (paymentTerms.Length > 2)
                            {
                                x.PaymentTerm = paymentTerms[1];
                            }
                        }
                        if (string.IsNullOrEmpty(x.PaymentTerm))
                            x.PaymentTerm = vendorTypeCfgs.FirstOrDefault(f => f.VendorType == ((int)VendorTypes.HCIAndOtherInstitutionsAR).ToString())?.PaymentTerms;

                        //2881 【供应商管理】【HCI激活】激活时：默认帐期调整为30天（如原本是45_45激活时默认写为30_30）
                        //3866  【供应商管理】【HCI激活】激活时：默认将帐期调整为“供应商类型配置下HCI机构的默认Payment Terms” 20250221 YTW
                        if (x.FinancialVendorStatus == FinancialVendorStatus.ToBeActivated)
                            x.PaymentTerm = vendorTypeCfgs.FirstOrDefault(f => f.VendorType == ((int)VendorTypes.HCIAndOtherInstitutionsAR).ToString())?.PaymentTerms;
                    });
                    await vendorApplicationFinanceQuery.InsertManyAsync(vendorApplicationFinanceDataList);
                }

                return await Task.FromResult(MessageResult.SuccessResult(vendorApplicationData.Id));
            }
            catch (Exception ex)
            {
                _logger.LogError($"NonSpeakerAppService's CreateApplicationHci has an error : {ex.Message}");
                return await Task.FromResult(MessageResult.FailureResult(new MessageModelBase(500, $"Create Application with HCI Failed! {ex.Message}")));
            }
        }

        /// <summary>
        /// 新建非讲者供应商申请-非HCI机构
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> CreateApplicationNonHci(NonSpeakerApplicationOrganizationDto request)
        {
            try
            {
                var vendorRepository = LazyServiceProvider.LazyGetService<IVendorRepository>();
                var vendorApplicationQuery = LazyServiceProvider.LazyGetService<IVendorApplicationRepository>();
                var vendorApplicationOrgQuery = LazyServiceProvider.LazyGetService<IVendorApplicationOrganizationRepository>();
                var vendorApplicationFinanceQuery = LazyServiceProvider.LazyGetService<IVendorApplicationFinancialRepository>();
                //var vendorApplicationEntity = await vendorApplicationQuery.FirstOrDefaultAsync(v => v.Id == request.ID);
                var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");
                var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
                var commonService = LazyServiceProvider.LazyGetService<ICommonService>();

                //新增 供应商申请
                var vendorApplicationData = ObjectMapper.Map<NonSpeakerApplicationOrganizationDto, Entities.VendorApplications.VendorApplication>(request);
                vendorApplicationData.ApplyUserId = request.ApplyUserId;
                vendorApplicationData.ApplyUserBu = request.ApplyUserBu.ToString();
                vendorApplicationData.VendorType = VendorTypes.NonHCIInstitutionalAP;
                vendorApplicationData.HandPhone = request.ContactPhone ?? string.Empty;

                //vendorApplicationData.ApplicationCode = await GetSerialNoAsync(vendorApplicationQuery, "V");
                //if (request.ApplicationType == ApplicationTypes.Create)
                //    vendorApplicationData.VendorCode = await GetSerialNoAsync(vendorApplicationQuery, "SP");

                #region 根据单据上选择的申请组织ID获取对应的名称、BU和全链路组织名称
                var orgs = await dataverseService.GetOrganizations();
                var orgDto = orgs.FirstOrDefault(x => x.Id == Guid.Parse(vendorApplicationData.ApplyUserBu));
                if (orgDto != null)
                {
                    var orgTree2BU = await commonService.GetParentOrgs(orgDto, orgs);
                    vendorApplicationData.ApplyDeptName = orgTree2BU.First().DepartmentName;
                    vendorApplicationData.ApplyBuId = orgTree2BU.Last().Id;
                    vendorApplicationData.ApplyBuName = orgTree2BU.Last().DepartmentName;
                    vendorApplicationData.ApplyUserBuName = orgTree2BU.Select(x => x.DepartmentName).Reverse().JoinAsString("-");
                }
                #endregion

                //如果是变更和激活版本加一
                if (request.ApplicationType != ApplicationTypes.Create)
                {
                    //如果是更新或激活草稿添加，判断VendorCode是否存在
                    var vendorOld = await vendorRepository.FirstOrDefaultAsync(f => f.VendorCode == request.VendorCode);
                    if (vendorOld == null) return MessageResult.FailureResult($"{request.VendorCode}数据不存在于正式库，修改时请填写VendorCode");
                    //版本增加
                    vendorApplicationData.DraftVersion = int.Parse(request.DraftVersion) + 1;
                    //记录上一版数据内容
                    var getSpeakerResponse = await GetOrganizationDetailNonHci(vendorOld.Id);
                    if (getSpeakerResponse.Success)
                    {
                        var speaker = getSpeakerResponse.Data as VendorOrganizationDetailResponseDto;
                        //加密
                        if (!string.IsNullOrEmpty(speaker.BankCardNo))
                            speaker.BankCardNo = AesHelper.Encryption(speaker.BankCardNo, insightKey);
                        vendorApplicationData.UpdatePreJson = JsonConvert.SerializeObject(speaker);
                    }
                }

                //vendorApplicationData.CreatorId = CurrentUser.Id;
                vendorApplicationData.Status = Statuses.Saved;
                vendorApplicationData.BankCardImg = request.BankCardImg?.AttachmentId.ToString();
                if (request.BankCity != null && request.BankCity.Length > 0)
                    vendorApplicationData.BankCity = request.BankCity?.JoinAsString(",");

                //附件
                if (request.AttachmentInformation == null || request.AttachmentInformation.Count < 1)
                    vendorApplicationData.AttachmentInformation = null;
                else
                    vendorApplicationData.AttachmentInformation = request.AttachmentInformation?.Select(x => x.AttachmentId.ToString()).JoinAsString(",");

                if (!string.IsNullOrEmpty(vendorApplicationData.BankCardNo))
                {
                    var key = _configuration.GetValue<string>("ApplicationInsightKey");
                    vendorApplicationData.BankCardNo = AesHelper.Encryption(vendorApplicationData.BankCardNo, key);
                }

                //vendorApplicationEntity = await vendorApplicationQuery.InsertAsync(vendorApplicationData, true);
                await InsertAndGenerateSerialNoAsync(vendorApplicationQuery, vendorApplicationData);

                //新增 供应商申请机构信息
                if (request.ProvinceCity != null && request.ProvinceCity.Length > 0)
                    request.Province = request.ProvinceCity[0];
                if (request.ProvinceCity != null && request.ProvinceCity.Length > 1)
                    request.City = request.ProvinceCity[1];
                var vendorApplicationOrgData = ObjectMapper.Map<NonSpeakerApplicationOrganizationDto, VendorApplicationOrganization>(request);
                vendorApplicationOrgData.ApplicationId = vendorApplicationData.Id;
                vendorApplicationOrgData.LastYearSales = vendorApplicationOrgData.LastYearSales;
                vendorApplicationOrgData = await vendorApplicationOrgQuery.InsertAsync(vendorApplicationOrgData);

                //新增 供应商申请财务信息
                if (request.FinanceInfo?.Count > 0)
                {
                    var vendorApplicationFinanceDataList = ObjectMapper.Map<List<FinancialInformation>, List<VendorApplicationFinancial>>(request.FinanceInfo);
                    vendorApplicationFinanceDataList.ForEach(x =>
                    {
                        x.ApplicationId = vendorApplicationData.Id;
                        if (x.FinancialVendorStatus == 0)
                            x.FinancialVendorStatus = FinancialVendorStatus.ToBeEffective;
                        //payment_term特殊处理下,前端传的是[79_45_45]格式
                        if (x.PaymentTerm.Contains("_"))
                        {
                            var paymentTerms = x.PaymentTerm.Split("_");
                            if (paymentTerms.Length > 2)
                            {
                                x.PaymentTerm = paymentTerms[1];
                            }
                        }
                    });
                    await vendorApplicationFinanceQuery.InsertManyAsync(vendorApplicationFinanceDataList);
                }

                return await Task.FromResult(MessageResult.SuccessResult(vendorApplicationData.Id));
            }
            catch (Exception ex)
            {
                _logger.LogError($"NonSpeakerAppService's CreateApplicationNonHci has an error : {ex.Message}");
                return await Task.FromResult(MessageResult.FailureResult(new MessageModelBase(500, $"Create Application with NonHCI Failed! {ex.Message}")));
            }
        }
        #endregion

        #region 变更、激活时复制信息
        /// <summary>
        /// 克隆非讲者供应商申请-非HCP个人
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MessageResult> CloneApplicationNonHcpAsync(NonSpeakerApplicationPersonalDto request)
        {
            var vendor = LazyServiceProvider.GetService<IVendorRepository>();
            var vendorPersonal = LazyServiceProvider.GetService<IVendorPersonalRepository>();
            var vendorApplication = LazyServiceProvider.GetService<IVendorApplicationRepository>();
            var vendorApplicationPersonal = LazyServiceProvider.GetService<IVendorApplicationPersonalRepository>();
            var vendorApplicationFinancial = LazyServiceProvider.GetService<IVendorApplicationFinancialRepository>();
            var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");
            var commonService = LazyServiceProvider.LazyGetService<ICommonService>();

            if (request.ApplicationType != ApplicationTypes.Create)
            {
                var applyUserId = await ValidationVendorApplicationDuplication(vendorApplication, request.VendorCode);
                if (applyUserId != null)
                {
                    var _identityUserRepository = LazyServiceProvider.GetService<IIdentityUserRepository>();
                    var user = await _identityUserRepository.FindAsync((Guid)applyUserId);
                    if (user != null)
                    {
                        return MessageResult.SuccessResult(messageModel: new MessageModelBase(603, $"供应商正在由{user.Name}({user.Email})申请中，无法重复申请"));
                    }
                    else
                    {
                        return MessageResult.FailureResult("供应商正在被申请，但没有找到申请人信息");
                    }
                }
            }
            //新产生数据ID清空
            request.ID = null;
            //城市处理
            if (request.ProvinceCity != null)
            {
                request.Province = request.ProvinceCity[0];
                request.City = request.ProvinceCity[1];
            }
            //如果是更新或激活草稿添加，判断VendorCode是否存在
            var vendorOld = await vendor.FirstOrDefaultAsync(f => f.VendorCode == request.VendorCode);
            if (vendorOld == null) return MessageResult.FailureResult($"{request.VendorCode}数据不存在于正式库，修改时请填写VendorCode");
            //查看是否存在供应商申请表
            var vendorDetailOld = await vendorApplication
                .FirstOrDefaultAsync(f => f.VendorCode == request.VendorCode &&
                f.ApplyUserId == request.ApplyUserId &&
                (f.Status == Statuses.Saved || f.Status == Statuses.Approving));
            if (vendorDetailOld != null)
                return MessageResult.FailureResult($"{vendorDetailOld.ApplicationCode}编号HCP个人数据已存在草稿或发起审批，请勿重复创建");
            //检查是否他人正在发起编辑审批
            var vendorDetailOldTwo = await vendorApplication
                .FirstOrDefaultAsync(f => f.VendorCode == request.VendorCode &&
                f.Status == Statuses.Approving);
            if (vendorDetailOldTwo != null)
                return MessageResult.FailureResult($"{vendorDetailOldTwo.ApplicationCode}编号HCP个人数据已由他人发起审批，请勿重复创建");

            var vendorDetail = ObjectMapper.Map<NonSpeakerApplicationPersonalDto, VendorApplication>(request);
            vendorDetail.ApplyUserId = CurrentUser.Id ?? Guid.Empty;
            vendorDetail.ApplyUserName = CurrentUser?.Name;
            vendorDetail.ApplyUserBu = request.ApplyUserBu.ToString();
            #region 根据单据上选择的申请组织ID获取对应的名称、BU和全链路组织名称
            var orgs = await _dataverseService.GetOrganizations();
            var orgDto = orgs.FirstOrDefault(x => x.Id == Guid.Parse(vendorDetail.ApplyUserBu));
            if (orgDto != null)
            {
                var orgTree2BU = await commonService.GetParentOrgs(orgDto, orgs);
                vendorDetail.ApplyDeptName = orgTree2BU.First().DepartmentName;
                vendorDetail.ApplyBuId = orgTree2BU.Last().Id;
                vendorDetail.ApplyBuName = orgTree2BU.Last().DepartmentName;
                vendorDetail.ApplyUserBuName = orgTree2BU.Select(x => x.DepartmentName).Reverse().JoinAsString("-");
            }
            #endregion
            //vendorDetail.ApplicationCode = await GetSerialNoAsync(vendorApplication, "V");
            vendorDetail.VendorType = VendorTypes.NonHCPPerson;
            vendorDetail.Status = Statuses.Saved;
            vendorDetail.VerificationStatus = VerificationStatuses.ToBeConfirmed;
            //vendorDetail.BImproved = request.ApplicationType != Enums.ApplicationTypes.Create;
            vendorDetail.BImproved = VendorCommon.CheckBImprovedNonHCP(request);
            vendorDetail.BAuth = false;
            vendorDetail.DraftVersion = int.Parse(request.DraftVersion) + 1;
            vendorDetail.BankCity = request.BankCity?.JoinAsString(",");
            vendorDetail.BankCardImg = request.BankCardImg?.AttachmentId.ToString();
            if (!string.IsNullOrEmpty(vendorDetail.BankCardNo))
                vendorDetail.BankCardNo = AesHelper.Encryption(vendorDetail.BankCardNo, insightKey);
            vendorDetail.OpenId = vendorOld.OpenId;
            vendorDetail.UserId = vendorOld.UserId;
            vendorDetail.AttachmentInformation = request.AttachmentInformation.Count > 0 ? string.Join(",", request.AttachmentInformation.Select(s => s.AttachmentId).ToList()) : "";
            //记录上一版数据内容
            var getSpeakerResponse = await GetPersonalDetailNonHcp(vendorOld.Id);
            if (getSpeakerResponse.Success)
            {
                var speaker = getSpeakerResponse.Data as VendorPersonalDetailResponseDto;
                //加密
                if (!string.IsNullOrEmpty(speaker.CardNo))
                    speaker.CardNo = AesHelper.Encryption(speaker.CardNo, insightKey);
                if (!string.IsNullOrEmpty(speaker.BankCardNo))
                    speaker.BankCardNo = AesHelper.Encryption(speaker.BankCardNo, insightKey);
                vendorDetail.UpdatePreJson = JsonConvert.SerializeObject(speaker);
            }
            //var newRecord = await vendorApplication.InsertAsync(vendorDetail, true);
            await InsertAndGenerateSerialNoAsync(vendorApplication, vendorDetail);

            //新增供应商个人申请表
            var vendorPersonalDetail = ObjectMapper.Map<NonSpeakerApplicationPersonalDto, VendorApplicationPersonal>(request);
            vendorPersonalDetail.ApplicationId = vendorDetail.Id;
            vendorPersonalDetail.CardPic = request.CardPic?.AttachmentId.ToString();
            if (!string.IsNullOrEmpty(vendorPersonalDetail.CardNo))
                vendorPersonalDetail.CardNo = AesHelper.Encryption(vendorPersonalDetail.CardNo, insightKey);
            var createPersonalEntity = await vendorApplicationPersonal.InsertAsync(vendorPersonalDetail, true);
            //新增供应商财务信息表
            var financeDetailList = ObjectMapper.Map<List<FinancialInformation>, List<VendorApplicationFinancial>>(request.FinanceInfo);
            var vendorTypeCfgs = await _dataverseService.GetVendorTypeCfgAsync();
            foreach (var item in financeDetailList)
            {
                item.ApplicationId = vendorDetail.Id;
                if (item.FinancialVendorStatus == 0)
                    item.FinancialVendorStatus = FinancialVendorStatus.ToBeEffective;
                if (string.IsNullOrEmpty(item.PaymentTerm))
                    item.PaymentTerm = vendorTypeCfgs.FirstOrDefault(f => f.VendorType == ((int)VendorTypes.NonHCPPerson).ToString())?.PaymentTerms;
            }
            await vendorApplicationFinancial.InsertManyAsync(financeDetailList, true);

            return MessageResult.SuccessResult(vendorDetail.Id);
        }
        /// <summary>
        /// 克隆非讲者供应商申请-HCI机构（暂时用不到，因为原本就可以直接创建，不需要分层保存）
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MessageResult> CloneApplicationHciAsync(NonSpeakerApplicationOrganizationDto request)
        {
            var vendor = LazyServiceProvider.GetService<IVendorRepository>();
            var vendorPersonal = LazyServiceProvider.GetService<IVendorPersonalRepository>();
            var vendorApplication = LazyServiceProvider.GetService<IVendorApplicationRepository>();
            var vendorApplicationOrg = LazyServiceProvider.GetService<IVendorApplicationOrganizationRepository>();
            var vendorApplicationFinancial = LazyServiceProvider.GetService<IVendorApplicationFinancialRepository>();
            var commonService = LazyServiceProvider.LazyGetService<ICommonService>();

            //新产生数据ID清空
            request.ID = null;
            //城市处理
            if (request.ProvinceCity != null)
            {
                request.Province = request.ProvinceCity[0];
                request.City = request.ProvinceCity[1];
            }
            //如果是更新或激活草稿添加，判断VendorCode是否存在
            var vendorOld = await vendor.FirstOrDefaultAsync(f => f.VendorCode == request.VendorCode);
            if (vendorOld == null) return MessageResult.FailureResult($"{request.VendorCode}数据不存在于正式库，修改时请填写VendorCode");
            //查看是否存在供应商申请表
            var vendorDetailOld = await vendorApplication
                .FirstOrDefaultAsync(f => f.VendorCode == request.VendorCode &&
                f.ApplyUserId == request.ApplyUserId &&
                (f.Status == Statuses.Saved || f.Status == Statuses.Approving));
            if (vendorDetailOld != null)
                return MessageResult.FailureResult($"{vendorDetailOld.ApplicationCode}编号HCI机构数据已存在草稿或发起审批，请勿重复创建");
            //检查是否他人正在发起编辑审批
            var vendorDetailOldTwo = await vendorApplication
                .FirstOrDefaultAsync(f => f.VendorCode == request.VendorCode &&
                f.Status == Statuses.Approving);
            if (vendorDetailOldTwo != null)
                return MessageResult.FailureResult($"{vendorDetailOld.ApplicationCode}编号HCI机构数据已由他人发起审批，请勿重复创建");


            var vendorDetail = ObjectMapper.Map<NonSpeakerApplicationOrganizationDto, VendorApplication>(request);
            vendorDetail.ApplyUserId = CurrentUser.Id ?? Guid.Empty;
            vendorDetail.ApplyUserName = CurrentUser?.Name;
            vendorDetail.ApplyUserBu = request.ApplyUserBu.ToString();
            #region 根据单据上选择的申请组织ID获取对应的名称、BU和全链路组织名称
            var orgs = await _dataverseService.GetOrganizations();
            var orgDto = orgs.FirstOrDefault(x => x.Id == Guid.Parse(vendorDetail.ApplyUserBu));
            if (orgDto != null)
            {
                var orgTree2BU = await commonService.GetParentOrgs(orgDto, orgs);
                vendorDetail.ApplyDeptName = orgTree2BU.First().DepartmentName;
                vendorDetail.ApplyBuId = orgTree2BU.Last().Id;
                vendorDetail.ApplyBuName = orgTree2BU.Last().DepartmentName;
                vendorDetail.ApplyUserBuName = orgTree2BU.Select(x => x.DepartmentName).Reverse().JoinAsString("-");
            }
            #endregion
            //vendorDetail.ApplicationCode = await GetSerialNoAsync(vendorApplication, "V");
            vendorDetail.VendorType = VendorTypes.HCIAndOtherInstitutionsAR;
            vendorDetail.Status = Statuses.Saved;
            vendorDetail.VerificationStatus = VerificationStatuses.ToBeConfirmed;
            vendorDetail.BImproved = request.ApplicationType == Enums.ApplicationTypes.Update;
            vendorDetail.BAuth = false;
            vendorDetail.DraftVersion = int.Parse(request.DraftVersion) + 1;
            vendorDetail.BankCity = request.BankCity?.JoinAsString(",");
            vendorDetail.BankCardImg = request.BankCardImg?.AttachmentId.ToString();
            vendorDetail.AttachmentInformation = request.AttachmentInformation.Count > 0 ? string.Join(",", request.AttachmentInformation.Select(s => s.AttachmentId).ToList()) : "";
            //记录上一版数据内容
            var getSpeakerResponse = await GetOrganizationDetailHci(vendorOld.Id);
            if (getSpeakerResponse.Success)
            {
                var speaker = getSpeakerResponse.Data as VendorOrganizationDetailResponseDto;
                vendorDetail.UpdatePreJson = JsonConvert.SerializeObject(speaker);
            }
            //var newRecord = await vendorApplication.InsertAsync(vendorDetail, true);
            await InsertAndGenerateSerialNoAsync(vendorApplication, vendorDetail);

            //新增供应商机构申请表
            var vendorApplicationOrgData = ObjectMapper.Map<NonSpeakerApplicationOrganizationDto, VendorApplicationOrganization>(request);
            vendorApplicationOrgData.ApplicationId = vendorDetail.Id;
            vendorApplicationOrgData = await vendorApplicationOrg.InsertAsync(vendorApplicationOrgData);
            //新增供应商财务信息表
            var financeDetailList = ObjectMapper.Map<List<FinancialInformation>, List<VendorApplicationFinancial>>(request.FinanceInfo);
            foreach (var item in financeDetailList)
            {
                item.ApplicationId = vendorDetail.Id;
            }
            await vendorApplicationFinancial.InsertManyAsync(financeDetailList, true);

            return MessageResult.SuccessResult(vendorDetail.Id);
        }
        /// <summary>
        /// 克隆非讲者供应商申请-非HCI机构（暂时用不到，因为原本就可以直接创建，不需要分层保存）
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MessageResult> CloneApplicationNonHciAsync(NonSpeakerApplicationOrganizationDto request)
        {
            var vendor = LazyServiceProvider.GetService<IVendorRepository>();
            var vendorPersonal = LazyServiceProvider.GetService<IVendorPersonalRepository>();
            var vendorApplication = LazyServiceProvider.GetService<IVendorApplicationRepository>();
            var vendorApplicationOrg = LazyServiceProvider.GetService<IVendorApplicationOrganizationRepository>();
            var vendorApplicationFinancial = LazyServiceProvider.GetService<IVendorApplicationFinancialRepository>();
            var commonService = LazyServiceProvider.LazyGetService<ICommonService>();

            //新产生数据ID清空
            request.ID = null;
            //城市处理
            if (request.ProvinceCity != null)
            {
                request.Province = request.ProvinceCity[0];
                request.City = request.ProvinceCity[1];
            }
            //如果是更新或激活草稿添加，判断VendorCode是否存在
            var vendorOld = await vendor.FirstOrDefaultAsync(f => f.VendorCode == request.VendorCode);
            if (vendorOld == null) return MessageResult.FailureResult($"{request.VendorCode}数据不存在于正式库，修改时请填写VendorCode");
            //查看是否存在供应商申请表
            var vendorDetailOld = await vendorApplication
                .FirstOrDefaultAsync(f => f.VendorCode == request.VendorCode &&
                f.ApplyUserId == request.ApplyUserId &&
                (f.Status == Statuses.Saved || f.Status == Statuses.Approving));
            if (vendorDetailOld != null)
                return MessageResult.FailureResult($"{vendorDetailOld.ApplicationCode}编号非HCI机构数据已存在草稿或发起审批，请勿重复创建");
            //检查是否他人正在发起编辑审批
            var vendorDetailOldTwo = await vendorApplication
                .FirstOrDefaultAsync(f => f.VendorCode == request.VendorCode &&
                f.Status == Statuses.Approving);
            if (vendorDetailOldTwo != null)
                return MessageResult.FailureResult($"{vendorDetailOld.ApplicationCode}编号非HCI机构数据已由他人发起审批，请勿重复创建");


            var vendorDetail = ObjectMapper.Map<NonSpeakerApplicationOrganizationDto, VendorApplication>(request);
            vendorDetail.ApplyUserId = CurrentUser.Id ?? Guid.Empty;
            vendorDetail.ApplyUserName = CurrentUser?.Name;
            vendorDetail.ApplyUserBu = request.ApplyUserBu.ToString();
            #region 根据单据上选择的申请组织ID获取对应的名称、BU和全链路组织名称
            var orgs = await _dataverseService.GetOrganizations();
            var orgDto = orgs.FirstOrDefault(x => x.Id == Guid.Parse(vendorDetail.ApplyUserBu));
            if (orgDto != null)
            {
                var orgTree2BU = await commonService.GetParentOrgs(orgDto, orgs);
                vendorDetail.ApplyDeptName = orgTree2BU.First().DepartmentName;
                vendorDetail.ApplyBuId = orgTree2BU.Last().Id;
                vendorDetail.ApplyBuName = orgTree2BU.Last().DepartmentName;
                vendorDetail.ApplyUserBuName = orgTree2BU.Select(x => x.DepartmentName).Reverse().JoinAsString("-");
            }
            #endregion
            //vendorDetail.ApplicationCode = await GetSerialNoAsync(vendorApplication, "V");
            vendorDetail.VendorType = VendorTypes.NonHCIInstitutionalAP;
            vendorDetail.Status = Statuses.Saved;
            vendorDetail.VerificationStatus = VerificationStatuses.ToBeConfirmed;
            vendorDetail.BImproved = request.ApplicationType == Enums.ApplicationTypes.Update;
            vendorDetail.BAuth = false;
            vendorDetail.DraftVersion = int.Parse(request.DraftVersion) + 1;
            vendorDetail.BankCity = request.BankCity?.JoinAsString(",");
            vendorDetail.BankCardImg = request.BankCardImg?.AttachmentId.ToString();
            vendorDetail.AttachmentInformation = request.AttachmentInformation.Count > 0 ? string.Join(",", request.AttachmentInformation.Select(s => s.AttachmentId).ToList()) : "";
            //记录上一版数据内容
            var getSpeakerResponse = await GetPersonalDetailNonHcp(vendorOld.Id);
            if (getSpeakerResponse.Success)
            {
                var speaker = getSpeakerResponse.Data as VendorPersonalDetailResponseDto;
                vendorDetail.UpdatePreJson = JsonConvert.SerializeObject(speaker);
            }
            //var newRecord = await vendorApplication.InsertAsync(vendorDetail, true);
            await InsertAndGenerateSerialNoAsync(vendorApplication, vendorDetail);

            //新增供应商机构申请表
            var vendorApplicationOrgData = ObjectMapper.Map<NonSpeakerApplicationOrganizationDto, VendorApplicationOrganization>(request);
            vendorApplicationOrgData.ApplicationId = vendorDetail.Id;
            vendorApplicationOrgData = await vendorApplicationOrg.InsertAsync(vendorApplicationOrgData);
            //新增供应商财务信息表
            var financeDetailList = ObjectMapper.Map<List<FinancialInformation>, List<VendorApplicationFinancial>>(request.FinanceInfo);
            foreach (var item in financeDetailList)
            {
                item.ApplicationId = vendorDetail.Id;
            }
            await vendorApplicationFinancial.InsertManyAsync(financeDetailList, true);

            return MessageResult.SuccessResult(vendorDetail.Id);
        }
        #endregion

        #region 编辑	
        /// <summary>
        /// 保存(编辑)非讲者供应商申请-非HCP个人
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> SaveVendorApplicationNonHcp(NonSpeakerApplicationPersonalDto request)
        {
            try
            {
                var vendorRepository = LazyServiceProvider.LazyGetService<IVendorRepository>();
                var vendorPersonalProvider = LazyServiceProvider.GetService<IVendorPersonalRepository>();
                var vendorApplicationQuery = LazyServiceProvider.LazyGetService<IVendorApplicationRepository>();
                var vendorApplicationPersonalQuery = LazyServiceProvider.LazyGetService<IVendorApplicationPersonalRepository>();
                var vendorApplicationFinanceQuery = LazyServiceProvider.LazyGetService<IVendorApplicationFinancialRepository>();
                var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");
                var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
                var commonService = LazyServiceProvider.LazyGetService<ICommonService>();

                //修改
                var vendorApplicationEntity = await vendorApplicationQuery.FirstOrDefaultAsync(v => v.Id == request.ID);
                if (vendorApplicationEntity == null)
                    return MessageResult.FailureResult("修改数据不存在");

                Statuses[] editableStatus = [Statuses.Saved, Statuses.Returned, Statuses.Withdraw];
                if (!editableStatus.Contains(vendorApplicationEntity.Status))
                    return MessageResult.FailureResult("数据状态不是草稿/退回/撤回，不能修改/提交");

                if (request.IsMiniProgram)//小程序请求才验证
                {
                    var ErrorMsg = await ValidationCardNo(request, vendorApplicationQuery, vendorApplicationPersonalQuery, vendorRepository, vendorPersonalProvider);
                    if (!string.IsNullOrWhiteSpace(ErrorMsg))
                        return MessageResult.FailureResult(ErrorMsg);
                }

                var vendorApplicationPersonalEntity = await vendorApplicationPersonalQuery.FirstOrDefaultAsync(x => x.ApplicationId == request.ID);

                //根据[是否授权、是否完善]区分是哪种阶段的编辑保存，不同阶段的编辑范围比一样，验证数据不一样：
                //if (!request.BAuth)
                //{
                //	//只更新 申请人信息&基本信息&财务信息&支持文档
                //	//个人&银行信息不可编辑：不包含这2块信息 更新
                //	var baseInfo = ObjectMapper.Map<NonSpeakerApplicationPersonalDto, NonHcpPersonalEditBaseInfoDto>(request);
                //	ObjectMapper.Map(baseInfo, vendorApplicationEntity);

                //	if (vendorApplicationPersonalEntity != null)
                //		ObjectMapper.Map(baseInfo, vendorApplicationPersonalEntity);
                //}
                //else
                //{
                //所有信息更新
                ObjectMapper.Map(request, vendorApplicationEntity);

                //申请人提交时，才修改发起人和发起部门
                if (vendorApplicationEntity.ApplyUserId == CurrentUser.Id)
                {
                    vendorApplicationEntity.ApplyUserBu = request.ApplyUserBu.ToString();
                    #region 根据单据上选择的申请组织ID获取对应的名称、BU和全链路组织名称
                    var orgs = await dataverseService.GetOrganizations();
                    var orgDto = orgs.FirstOrDefault(x => x.Id == Guid.Parse(vendorApplicationEntity.ApplyUserBu));
                    if (orgDto != null)
                    {
                        var orgTree2BU = await commonService.GetParentOrgs(orgDto, orgs);
                        vendorApplicationEntity.ApplyDeptName = orgTree2BU.First().DepartmentName;
                        vendorApplicationEntity.ApplyBuId = orgTree2BU.Last().Id;
                        vendorApplicationEntity.ApplyBuName = orgTree2BU.Last().DepartmentName;
                        vendorApplicationEntity.ApplyUserBuName = orgTree2BU.Select(x => x.DepartmentName).Reverse().JoinAsString("-");
                    }
                    #endregion
                }

                //vendorApplicationEntity.BImproved = true;
                if (request.BAuth || request.IsMiniProgram)
                    vendorApplicationEntity.BAuth = true;

                if (request.BankCity != null && request.BankCity.Length > 0)
                    vendorApplicationEntity.BankCity = request.BankCity?.JoinAsString(",");

                //从小程序过来或者授权了的
                //if (request.IsMiniProgram || request.BAuth)
                //{
                //    vendorApplicationEntity.BImproved = true;
                //}
                vendorApplicationEntity.BImproved = VendorCommon.CheckBImprovedNonHCP(request);

                if (vendorApplicationPersonalEntity != null)
                {
                    if (request.ProvinceCity != null && request.ProvinceCity.Length > 0)
                        request.Province = request.ProvinceCity[0];
                    if (request.ProvinceCity != null && request.ProvinceCity.Length > 1)
                        request.City = request.ProvinceCity[1];

                    ObjectMapper.Map(request, vendorApplicationPersonalEntity);
                    vendorApplicationPersonalEntity.CardPic = request.CardPic?.AttachmentId.ToString();
                    //加密
                    if (!string.IsNullOrEmpty(vendorApplicationPersonalEntity.CardNo))
                        vendorApplicationPersonalEntity.CardNo = AesHelper.Encryption(vendorApplicationPersonalEntity.CardNo, insightKey);
                }
                //}
                //修改VendorAPPlication
                vendorApplicationEntity.BankCardImg = request.BankCardImg?.AttachmentId.ToString();
                if (request.AttachmentInformation == null || request.AttachmentInformation.Count < 1)
                    vendorApplicationEntity.AttachmentInformation = null;
                else
                    vendorApplicationEntity.AttachmentInformation = request.AttachmentInformation?.Select(x => x.AttachmentId.ToString()).JoinAsString(",");

                //加密
                if (!string.IsNullOrEmpty(vendorApplicationEntity.BankCardNo))
                    vendorApplicationEntity.BankCardNo = AesHelper.Encryption(vendorApplicationEntity.BankCardNo, insightKey);

                await vendorApplicationQuery.UpdateAsync(vendorApplicationEntity, true);

                //修改VendorApplicationPersonalInfo
                if (vendorApplicationPersonalEntity != null)
                    await vendorApplicationPersonalQuery.UpdateAsync(vendorApplicationPersonalEntity);

                //修改VendorApplicationFinanceInfo:先删除相关的财务记录，再添加新的财务记录
                await vendorApplicationFinanceQuery.DeleteAsync(x => x.ApplicationId == vendorApplicationEntity.Id);
                var vendorApplicationFinanceDataList = ObjectMapper.Map<List<FinancialInformation>, List<VendorApplicationFinancial>>(request.FinanceInfo);
                var vendorTypeCfgs = await _dataverseService.GetVendorTypeCfgAsync();
                vendorApplicationFinanceDataList.ForEach(x =>
                {
                    x.ApplicationId = vendorApplicationEntity.Id;
                    if (x.FinancialVendorStatus == 0)
                        x.FinancialVendorStatus = FinancialVendorStatus.ToBeEffective;
                    if (string.IsNullOrEmpty(x.PaymentTerm))
                        x.PaymentTerm = vendorTypeCfgs.FirstOrDefault(f => f.VendorType == ((int)VendorTypes.NonHCPPerson).ToString())?.PaymentTerms;
                });
                await vendorApplicationFinanceQuery.InsertManyAsync(vendorApplicationFinanceDataList, true);

                //发送信息已完善的邮件
                if (request.IsMiniProgram)
                    await LazyServiceProvider.LazyGetService<ISpeakerService>().SendCompleteInfoEmail((vendorApplicationEntity.Id, vendorApplicationEntity.ApplyUserId, vendorApplicationEntity.VendorType, vendorApplicationEntity.ApplicationType, vendorApplicationEntity.Status, vendorApplicationEntity.ApplyUserName, vendorApplicationPersonalEntity.SPName));

                return await Task.FromResult(MessageResult.SuccessResult(vendorApplicationEntity.Id));
            }
            catch (Exception ex)
            {
                _logger.LogError($"NonSpeakerAppService's SaveVendorApplicationNonHcp has an error : {ex.Message}");
                return await Task.FromResult(MessageResult.FailureResult(new MessageModelBase(500, $"Update Application with NonHCP Failed! {ex.Message}")));
            }
        }

        /// <summary>
        /// 保存(编辑)非讲者供应商申请-HCI机构
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> SaveVendorApplicationHciOrg(NonSpeakerApplicationOrganizationDto request)
        {
            try
            {
                var vendorApplicationQuery = LazyServiceProvider.LazyGetService<IVendorApplicationRepository>();
                var vendorApplicationOrgQuery = LazyServiceProvider.LazyGetService<IVendorApplicationOrganizationRepository>();
                var vendorApplicationFinanceQuery = LazyServiceProvider.LazyGetService<IVendorApplicationFinancialRepository>();

                var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");

                var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
                var commonService = LazyServiceProvider.LazyGetService<ICommonService>();

                if (request.ID == null)
                    return MessageResult.FailureResult("ID不能空");
                //修改
                var vendorApplicationEntity = await vendorApplicationQuery.FirstOrDefaultAsync(v => v.Id == request.ID);
                if (vendorApplicationEntity == null)
                    return MessageResult.FailureResult("修改数据不存在");

                Statuses[] editableStatus = [Statuses.Saved, Statuses.Returned, Statuses.Withdraw];
                if (!editableStatus.Contains(vendorApplicationEntity.Status))
                    return MessageResult.FailureResult("数据状态不是草稿/退回/撤回，不能修改/提交");

                if (request.ProvinceCity != null && request.ProvinceCity.Length > 0)
                    request.Province = request.ProvinceCity[0];
                if (request.ProvinceCity != null && request.ProvinceCity.Length > 1)
                    request.City = request.ProvinceCity[1];

                //修改VendorAPPlication
                var vendorApplicationData = ObjectMapper.Map(request, vendorApplicationEntity);

                //申请人提交时，才修改发起人和发起部门
                if (vendorApplicationData.ApplyUserId == CurrentUser.Id)
                {
                    vendorApplicationData.ApplyUserBu = request.ApplyUserBu.ToString();
                    #region 根据单据上选择的申请组织ID获取对应的名称、BU和全链路组织名称
                    var orgs = await dataverseService.GetOrganizations();
                    var orgDto = orgs.FirstOrDefault(x => x.Id == Guid.Parse(vendorApplicationEntity.ApplyUserBu));
                    if (orgDto != null)
                    {
                        var orgTree2BU = await commonService.GetParentOrgs(orgDto, orgs);
                        vendorApplicationEntity.ApplyDeptName = orgTree2BU.First().DepartmentName;
                        vendorApplicationEntity.ApplyBuId = orgTree2BU.Last().Id;
                        vendorApplicationEntity.ApplyBuName = orgTree2BU.Last().DepartmentName;
                        vendorApplicationEntity.ApplyUserBuName = orgTree2BU.Select(x => x.DepartmentName).Reverse().JoinAsString("-");
                    }
                    #endregion
                }

                //vendorApplicationData.BImproved = true;//必填项填完后就认为是已完善
                vendorApplicationData.BImproved = VendorCommon.CheckBImprovedHCI(request);
                vendorApplicationData.BankCardImg = request.BankCardImg?.AttachmentId.ToString();
                vendorApplicationData.HandPhone = request.ContactPhone ?? string.Empty;

                if (request.BankCity != null && request.BankCity.Length > 0)
                    vendorApplicationData.BankCity = request.BankCity.JoinAsString(",");

                if (request.AttachmentInformation == null || request.AttachmentInformation.Count < 1)
                    vendorApplicationData.AttachmentInformation = null;
                else
                    vendorApplicationData.AttachmentInformation = request.AttachmentInformation?.Select(x => x.AttachmentId.ToString()).JoinAsString(",");

                if (!string.IsNullOrEmpty(vendorApplicationData.BankCardNo))
                    vendorApplicationData.BankCardNo = AesHelper.Encryption(vendorApplicationData.BankCardNo, insightKey);

                await vendorApplicationQuery.UpdateAsync(vendorApplicationData);
                //修改VendorApplicationOrganization
                var vendorApplicationOrgEntity = await vendorApplicationOrgQuery.FirstOrDefaultAsync(x => x.ApplicationId == request.ID);
                if (vendorApplicationOrgEntity != null)
                {
                    var vendorApplicationOrgData = ObjectMapper.Map(request, vendorApplicationOrgEntity);
                    await vendorApplicationOrgQuery.UpdateAsync(vendorApplicationOrgData);
                }
                //修改VendorApplicationFinanceInfo:先删除相关的财务记录，再添加新的财务记录
                await vendorApplicationFinanceQuery.DeleteAsync(x => x.ApplicationId == vendorApplicationData.Id);
                var vendorApplicationFinanceDataList = ObjectMapper.Map<List<FinancialInformation>, List<VendorApplicationFinancial>>(request.FinanceInfo);
                var vendorTypeCfgs = await _dataverseService.GetVendorTypeCfgAsync();
                vendorApplicationFinanceDataList.ForEach(x =>
                {
                    x.ApplicationId = vendorApplicationEntity.Id;
                    if (x.FinancialVendorStatus == 0)
                        x.FinancialVendorStatus = FinancialVendorStatus.ToBeEffective;

                    //HCI变更，PaymentTerm前端选择
                    //payment_term特殊处理下,前端传的是[79_45_45]格式
                    if (x.PaymentTerm.Contains("_"))
                    {
                        var paymentTerms = x.PaymentTerm.Split("_");
                        if (paymentTerms.Length > 2)
                        {
                            x.PaymentTerm = paymentTerms[1];
                        }
                    }
                    //HCI新建，取默认值
                    if (string.IsNullOrEmpty(x.PaymentTerm))
                        x.PaymentTerm = vendorTypeCfgs.FirstOrDefault(f => f.VendorType == ((int)VendorTypes.HCIAndOtherInstitutionsAR).ToString())?.PaymentTerms;

                    //2881 【供应商管理】【HCI激活】激活时：默认帐期调整为30天（如原本是45_45激活时默认写为30_30）
                    //3866  【供应商管理】【HCI激活】激活时：默认将帐期调整为“供应商类型配置下HCI机构的默认Payment Terms” 20250221 YTW
                    if (x.FinancialVendorStatus == FinancialVendorStatus.ToBeActivated)
                        x.PaymentTerm = vendorTypeCfgs.FirstOrDefault(f => f.VendorType == ((int)VendorTypes.HCIAndOtherInstitutionsAR).ToString())?.PaymentTerms;
                });
                await vendorApplicationFinanceQuery.InsertManyAsync(vendorApplicationFinanceDataList);

                return await Task.FromResult(MessageResult.SuccessResult(vendorApplicationData.Id));
            }
            catch (Exception ex)
            {
                _logger.LogError($"NonSpeakerAppService's SaveVendorApplicationHciOrg has an error : {ex.Message}");
                return await Task.FromResult(MessageResult.FailureResult(new MessageModelBase(500, $"Update Application with HCI Failed! {ex.Message}")));
            }
        }

        /// <summary>
        /// 保存(编辑)非讲者供应商申请-非HCI机构
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> SaveVendorApplicationNonHciOrg(NonSpeakerApplicationOrganizationDto request)
        {
            try
            {
                var vendorApplicationQuery = LazyServiceProvider.LazyGetService<IVendorApplicationRepository>();
                var vendorApplicationOrgQuery = LazyServiceProvider.LazyGetService<IVendorApplicationOrganizationRepository>();
                var vendorApplicationFinanceQuery = LazyServiceProvider.LazyGetService<IVendorApplicationFinancialRepository>();

                var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");

                var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
                var commonService = LazyServiceProvider.LazyGetService<ICommonService>();

                if (request.ID == null)
                    return MessageResult.FailureResult("ID不能空");
                //修改
                var vendorApplicationEntity = await vendorApplicationQuery.FirstOrDefaultAsync(v => v.Id == request.ID);
                if (vendorApplicationEntity == null)
                    return MessageResult.FailureResult("修改数据不存在");

                Statuses[] editableStatus = [Statuses.Saved, Statuses.Returned, Statuses.Withdraw];
                if (!editableStatus.Contains(vendorApplicationEntity.Status))
                    return MessageResult.FailureResult("数据状态不是草稿/退回/撤回，不能修改/提交");

                if (request.ProvinceCity != null && request.ProvinceCity.Length > 0)
                    request.Province = request.ProvinceCity[0];
                if (request.ProvinceCity != null && request.ProvinceCity.Length > 1)
                    request.City = request.ProvinceCity[1];

                //修改VendorAPPlication
                var vendorApplicationData = ObjectMapper.Map(request, vendorApplicationEntity);

                //申请人提交时，才修改发起人和发起部门
                if (vendorApplicationData.ApplyUserId == CurrentUser.Id)
                {
                    vendorApplicationData.ApplyUserBu = request.ApplyUserBu.ToString();
                    #region 根据单据上选择的申请组织ID获取对应的名称、BU和全链路组织名称
                    var orgs = await dataverseService.GetOrganizations();
                    var orgDto = orgs.FirstOrDefault(x => x.Id == Guid.Parse(vendorApplicationEntity.ApplyUserBu));
                    if (orgDto != null)
                    {
                        var orgTree2BU = await commonService.GetParentOrgs(orgDto, orgs);
                        vendorApplicationEntity.ApplyDeptName = orgTree2BU.First().DepartmentName;
                        vendorApplicationEntity.ApplyBuId = orgTree2BU.Last().Id;
                        vendorApplicationEntity.ApplyBuName = orgTree2BU.Last().DepartmentName;
                        vendorApplicationEntity.ApplyUserBuName = orgTree2BU.Select(x => x.DepartmentName).Reverse().JoinAsString("-");
                    }
                    #endregion
                }

                //vendorApplicationData.BImproved = true;//必填项填完后就认为是已完善
                vendorApplicationData.BImproved = VendorCommon.CheckBImprovedNonHCI(request);
                vendorApplicationData.BankCardImg = request.BankCardImg?.AttachmentId.ToString();
                vendorApplicationData.HandPhone = request.ContactPhone ?? string.Empty;

                if (request.BankCity != null && request.BankCity.Length > 0)
                    vendorApplicationData.BankCity = request.BankCity.JoinAsString(",");

                if (request.AttachmentInformation == null || request.AttachmentInformation.Count < 1)
                    vendorApplicationData.AttachmentInformation = null;
                else
                    vendorApplicationData.AttachmentInformation = request.AttachmentInformation?.Select(x => x.AttachmentId.ToString()).JoinAsString(",");

                if (!string.IsNullOrEmpty(vendorApplicationData.BankCardNo))
                    vendorApplicationData.BankCardNo = AesHelper.Encryption(vendorApplicationData.BankCardNo, insightKey);

                await vendorApplicationQuery.UpdateAsync(vendorApplicationData);

                //修改VendorApplicationOrganization
                var vendorApplicationOrgEntity = await vendorApplicationOrgQuery.FirstOrDefaultAsync(x => x.ApplicationId == request.ID);
                if (vendorApplicationOrgEntity != null)
                {
                    var vendorApplicationOrgData = ObjectMapper.Map(request, vendorApplicationOrgEntity);
                    vendorApplicationOrgData.LastYearSales = vendorApplicationOrgData.LastYearSales;
                    await vendorApplicationOrgQuery.UpdateAsync(vendorApplicationOrgData);
                }
                //修改VendorApplicationFinanceInfo:先删除相关的财务记录，再添加新的财务记录
                await vendorApplicationFinanceQuery.DeleteAsync(x => x.ApplicationId == vendorApplicationData.Id);
                var vendorApplicationFinanceDataList = ObjectMapper.Map<List<FinancialInformation>, List<VendorApplicationFinancial>>(request.FinanceInfo);
                vendorApplicationFinanceDataList.ForEach(x =>
                {
                    x.ApplicationId = vendorApplicationData.Id;
                    if (x.FinancialVendorStatus == 0)
                        x.FinancialVendorStatus = FinancialVendorStatus.ToBeEffective;
                    //payment_term特殊处理下,前端传的是[79_45_45]格式
                    if (x.PaymentTerm.Contains("_"))
                    {
                        var paymentTerms = x.PaymentTerm.Split("_");
                        if (paymentTerms.Length > 2)
                        {
                            x.PaymentTerm = paymentTerms[1];
                        }
                    }
                });
                await vendorApplicationFinanceQuery.InsertManyAsync(vendorApplicationFinanceDataList);

                return await Task.FromResult(MessageResult.SuccessResult(vendorApplicationData.Id));
            }
            catch (Exception ex)
            {
                //logger.LogError($"SpeakerService's SubmitSpeakerAsync has an error : {ex.Message}");
                return await Task.FromResult(MessageResult.FailureResult(new MessageModelBase(500, $"Update Application with NonHCI Failed! {ex.Message}")));
            }
        }
        #endregion


        #region 详情-草稿
        /// <summary>
        /// 供应商申请的详情-非HCP个人
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <returns></returns>
        public async Task<MessageResult> VendorApplicationPersonalDetail(Guid? applicationId)
        {
            var applicationQuery = await LazyServiceProvider.LazyGetService<IVendorApplicationRepository>().GetQueryableAsync();
            var applicationPersonalQuery = await LazyServiceProvider.LazyGetService<IVendorApplicationPersonalRepository>().GetQueryableAsync();
            var applicationFinanceQuery = await LazyServiceProvider.LazyGetService<IVendorApplicationFinancialRepository>().GetQueryableAsync();
            var attachmentQuery = await LazyServiceProvider.LazyGetService<IAttachmentRepository>().GetQueryableAsync();
            var userQuery = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var commonService = LazyServiceProvider.LazyGetService<ICommonService>();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var attachmentService = LazyServiceProvider.LazyGetService<IAttachmentService>();
            var insightKey = this._configuration.GetValue<string>("ApplicationInsightKey");

            var query = applicationQuery.Where(x => x.VendorType == VendorTypes.NonHCPPerson)
                .Join(userQuery, a => a.ApplyUserId, a => a.Id, (a, b) => new { vendor = a, User = b })
                .Join(applicationPersonalQuery, c => c.vendor.Id, c => c.ApplicationId, (a, c) => new { vendorUser = a, personal = c })
                .WhereIf(applicationId.HasValue, a => a.vendorUser.vendor.Id == applicationId);
            var applicationEntity = query.FirstOrDefault();

            if (applicationEntity == null)
                return MessageResult.FailureResult("该详情数据不存在");

            var responseDto = ObjectMapper.Map<Entities.VendorApplications.VendorApplication, VendorApplicationPersonalDetailReponseDto>(applicationEntity.vendorUser.vendor);
            Statuses[] showDraftStatus = [Statuses.Saved, Statuses.Returned, Statuses.Withdraw];
            responseDto.isDraft = showDraftStatus.Contains(responseDto.Status);
            ObjectMapper.Map(applicationEntity.personal, responseDto);
            responseDto.ApplyUserName = applicationEntity.vendorUser.User.Name;
            //responseDto.ApplyUserBuToDept = await commonService.GetOrganizationDept(applicationEntity.vendorUser.vendor.ApplyUserId);
            //responseDto.ApplyUserBuName = responseDto.ApplyUserBuToDept?.First(x => x.Key == responseDto.ApplyUserBu).Value?.Split("->").Last();

            //var orgs = await dataverseService.GetOrganizations(applicationEntity.vendorUser.vendor.ApplyUserBu);
            //if (orgs.Any())
            //    responseDto.ApplyUserBuName = orgs.First().DepartmentName;

            if (!string.IsNullOrEmpty(responseDto.CardType))
            {
                var certificateType = await dataverseService.GetDictionariesAsync(DictionaryType.CertificateType);
                responseDto.CardTypeText = certificateType.FirstOrDefault(x => x.Code == responseDto.CardType)?.Name;
            }

            if (responseDto.Sex.HasValue)
            {
                var gender = EnumUtil.GetEnumIdValues<Gender>().ToDictionary(x => x.Key, x => x.Value);
                responseDto.SexText = gender[(int)responseDto.Sex];
            }

            responseDto.StatusString = EnumUtil.GetDescription(responseDto.Status);

            var provinces = await dataverseService.GetAllProvince();
            responseDto.ProvinceText = provinces.FirstOrDefault(x => x.Code == responseDto.Province)?.Name;
            var cities = await dataverseService.GetAllCity();
            responseDto.CityText = cities.FirstOrDefault(x => x.Code == responseDto.City)?.Name;
            responseDto.ProvinceCity = [responseDto.Province, responseDto.City];
            responseDto.ProvinceCityName = [responseDto.ProvinceText, responseDto.CityText];

            if (!string.IsNullOrEmpty(applicationEntity.vendorUser.vendor.BankCity))
            {
                responseDto.BankCity = applicationEntity.vendorUser.vendor.BankCity.Split(",").ToArray();
                responseDto.BankCityName = $"{provinces.FirstOrDefault(x => x.Code == responseDto.BankCity[0])?.Name}/{cities.FirstOrDefault(x => x.Code == responseDto.BankCity[1])?.Name}";
            }

            //解密
            if (!string.IsNullOrEmpty(applicationEntity.personal.CardNo))
                responseDto.CardNo = AesHelper.Decryption(applicationEntity.personal.CardNo, insightKey);
            if (!string.IsNullOrEmpty(applicationEntity.vendorUser.vendor.BankCardNo))
                responseDto.BankCardNo = AesHelper.Decryption(applicationEntity.vendorUser.vendor.BankCardNo, insightKey);

            //证件照正面照
            if (!string.IsNullOrEmpty(applicationEntity.personal.CardPic))
            {
                var cardAttachmentInfos = await attachmentService.GetAttachmentsAsync(Guid.Parse(applicationEntity.personal.CardPic));
                responseDto.CardPic = cardAttachmentInfos.FirstOrDefault();
            }
            //银行卡图片
            if (!string.IsNullOrEmpty(applicationEntity.vendorUser.vendor.BankCardImg))
            {
                var bankCardAttachmentInfos = await attachmentService.GetAttachmentsAsync(Guid.Parse(applicationEntity.vendorUser.vendor.BankCardImg));
                responseDto.BankCardImg = bankCardAttachmentInfos.FirstOrDefault();
            }
            //财务信息
            var financesQuery = applicationFinanceQuery.Where(x => x.ApplicationId == applicationId);
            var financeEntities = financesQuery.ToList();
            var financeInfo = ObjectMapper.Map<List<VendorApplicationFinancial>, List<FinancialInformation>>(financeEntities);
            var companies = await dataverseService.GetCompanyList();
            var vendorOldData = string.IsNullOrEmpty(applicationEntity.vendorUser.vendor.UpdatePreJson) ? null : JsonConvert.DeserializeObject<VendorPersonalDetailResponseDto>(applicationEntity.vendorUser.vendor.UpdatePreJson);
            financeInfo.ForEach(x =>
            {
                x.Flag = vendorOldData != null && (vendorOldData.FinanceInfo.Select(s => s.Company).Contains(x.Company));
                x.PaymentTermName = _commonService.GetPaymentTermName(x.Company, x.PaymentTerm);
                var comCur = companies.FirstOrDefault(c => c.CompanyCode == x.Company);
                if (comCur != null)
                {
                    x.CompanyName = comCur.CompanyName;
                    x.CurrencyName = comCur.CompanyCurrency?.FirstOrDefault(c => c.Code == x.Currency)?.Name;
                }
            });
            responseDto.FinanceInfo = financeInfo;
            //附件
            if (!string.IsNullOrEmpty(applicationEntity.vendorUser.vendor.AttachmentInformation))
            {
                var attachmentIds = applicationEntity.vendorUser.vendor.AttachmentInformation?.Split(',').Select(x => Guid.Parse(x)).ToArray();
                if (attachmentIds.Count() > 0)
                    responseDto.AttachmentInformation = await attachmentService.GetAttachmentsAsync(attachmentIds);
            }
            //DPS信息
            if (!string.IsNullOrEmpty(applicationEntity.vendorUser.vendor.DPSCheck))
            {
                var dpsCheckIds = applicationEntity.vendorUser.vendor.DPSCheck.Split(',').Select(x => Guid.Parse(x)).ToArray();
                if (dpsCheckIds.Count() > 0)
                    responseDto.DPSCheck = await attachmentService.GetAttachmentsAsync(dpsCheckIds);
            }

            // 获取签署内容
            var _configuration = LazyServiceProvider.GetService<IConfiguration>();
            var _consentsetvice = LazyServiceProvider.GetService<IConsentService>();
            var consentSignedQuery = await LazyServiceProvider.LazyGetService<IConsentSignedRepository>().GetListAsync();
            //获取需要检查的最新版签署版本号
            string consentInfo = _configuration["Consent:ConsentCode"];
            var consentResponse = await _consentsetvice.GetConsentInfoAsync([.. consentInfo.Split(",")]);
            //获取讲者签署记录，判断是否已经完全签署
            var consentList = consentSignedQuery.Where(w => w.OneId == applicationEntity.vendorUser.vendor.OpenId || w.AppUserId == applicationEntity.vendorUser.vendor.UserId.ToString()).ToList();
            var isConsent = HasBeenFullySigned(consentList, consentResponse);
            responseDto.SignedStatus = isConsent;
            responseDto.SignedStatusString = isConsent ? "已签署" : "待签署";
            responseDto.BankName = responseDto.BankCode;

            return MessageResult.SuccessResult(responseDto);
        }

        /// <summary>
        /// 供应商申请的详情-非/HCI机构
        /// </summary>
        /// <param name="applicationId">The application identifier.</param>
        /// <returns></returns>
        public async Task<MessageResult> VendorApplicationOrgDetail(Guid applicationId, VendorTypes vendorType)
        {
            var applicationQuery = await LazyServiceProvider.LazyGetService<IVendorApplicationRepository>().GetQueryableAsync();
            var applicationOrgQuery = await LazyServiceProvider.LazyGetService<IVendorApplicationOrganizationRepository>().GetQueryableAsync();
            var applicationFinanceQuery = await LazyServiceProvider.LazyGetService<IVendorApplicationFinancialRepository>().GetQueryableAsync();

            var attachmentQuery = await LazyServiceProvider.LazyGetService<IAttachmentRepository>().GetQueryableAsync();

            var userQuery = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();

            var commonService = LazyServiceProvider.LazyGetService<ICommonService>();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var attachmentService = LazyServiceProvider.LazyGetService<IAttachmentService>();

            var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");

            var purPOApplicationService = LazyServiceProvider.LazyGetService<IPurPOApplicationService>();

            var query = applicationQuery.Where(x => x.Id == applicationId && x.VendorType == vendorType)
                .Join(userQuery, a => a.ApplyUserId, a => a.Id, (a, b) => new { vendor = a, User = b })
                .Join(applicationOrgQuery, c => c.vendor.Id, c => c.ApplicationId, (a, c) => new { vendorUser = a, org = c });
            var applicationEntity = query.FirstOrDefault();

            if (applicationEntity == null)
                return MessageResult.FailureResult("该详情数据不存在");

            var responseDto = ObjectMapper.Map<Entities.VendorApplications.VendorApplication, VendorApplicationOrganizationDetailResponseDto>(applicationEntity.vendorUser.vendor);
            Statuses[] showDraftStatus = [Statuses.Saved, Statuses.Returned, Statuses.Withdraw];
            responseDto.isDraft = showDraftStatus.Contains(responseDto.Status);
            ObjectMapper.Map(applicationEntity.org, responseDto);
            responseDto.ApplyUserName = applicationEntity.vendorUser.User.Name;
            responseDto.ApplyUserBuToDept = await commonService.GetOrganizationDept(applicationEntity.vendorUser.vendor.ApplyUserId);
            //responseDto.ApplyUserBuName = responseDto.ApplyUserBuToDept?.First(x => x.Key == responseDto.ApplyUserBu).Value?.Split("->").Last();

            //var orgs = await dataverseService.GetOrganizations(applicationEntity.vendorUser.vendor.ApplyUserBu);
            //if (orgs.Any())
            //    responseDto.ApplyUserBuName = orgs.First().DepartmentName;

            var provinces = await dataverseService.GetAllProvince();
            responseDto.ProvinceText = provinces.FirstOrDefault(x => x.Code == responseDto.Province)?.Name;
            var cities = await dataverseService.GetAllCity();
            responseDto.CityText = cities.FirstOrDefault(x => x.Code == responseDto.City)?.Name;
            responseDto.ProvinceCity = [responseDto.Province, responseDto.City];
            responseDto.ProvinceCityName = [responseDto.ProvinceText, responseDto.CityText];
            responseDto.LastYearSales = responseDto.LastYearSales;
            responseDto.StatusString = EnumUtil.GetDescription(responseDto.Status);
            if (!string.IsNullOrEmpty(applicationEntity.vendorUser.vendor.BankCity))
            {
                responseDto.BankCity = applicationEntity.vendorUser.vendor.BankCity.Split(",").ToArray();
                responseDto.BankCityName = $"{provinces.FirstOrDefault(x => x.Code == responseDto.BankCity[0])?.Name}/{cities.FirstOrDefault(x => x.Code == responseDto.BankCity[1])?.Name}";
            }

            if (!string.IsNullOrEmpty(applicationEntity.vendorUser.vendor.BankCardNo))
                responseDto.BankCardNo = AesHelper.Decryption(applicationEntity.vendorUser.vendor.BankCardNo, insightKey);

            //银行卡图片
            if (!string.IsNullOrEmpty(applicationEntity.vendorUser.vendor.BankCardImg))
            {
                var bankCardAttachmentInfos = await attachmentService.GetAttachmentsAsync(Guid.Parse(applicationEntity.vendorUser.vendor.BankCardImg));
                responseDto.BankCardImg = bankCardAttachmentInfos.FirstOrDefault();
            }
            //财务信息
            var financesQuery = applicationFinanceQuery.Where(x => x.ApplicationId == applicationId);
            var financeEntities = financesQuery.ToList();
            var financeInfo = ObjectMapper.Map<List<VendorApplicationFinancial>, List<FinancialInformation>>(financeEntities);
            var companies = await dataverseService.GetCompanyList();


            //var apsProperty = await dataverseService.GetDictionariesAsync(DictionaryType.ApsProperty);
            var dpoCategory = await dataverseService.GetDictionariesAsync(DictionaryType.DpoCategory);
            var spendingCategory = await dataverseService.GetDictionariesAsync(DictionaryType.SpendingCategory);
            var vendorOldData = string.IsNullOrEmpty(applicationEntity.vendorUser.vendor.UpdatePreJson) ? null : JsonConvert.DeserializeObject<VendorOrganizationDetailResponseDto>(applicationEntity.vendorUser.vendor.UpdatePreJson);
            financeInfo.ForEach(x =>
            {
                x.Flag = vendorOldData != null && (vendorOldData.FinanceInfo.Select(s => s.Company).Contains(x.Company));
                var comCur = companies.FirstOrDefault(c => c.CompanyCode == x.Company);
                if (comCur != null)
                {
                    x.CompanyName = comCur.CompanyName;
                    x.CurrencyName = comCur.CompanyCurrency?.FirstOrDefault(c => c.Code == x.Currency)?.Name;
                }
                if (!string.IsNullOrEmpty(x.PaymentTerm))
                {
                    x.PaymentTermName = _commonService.GetPaymentTermName(x.Company, x.PaymentTerm);
                }

                if (!string.IsNullOrEmpty(x.DpoCategory))
                    x.DpoCategoryName = dpoCategory?.FirstOrDefault(d => d.Code == x.DpoCategory)?.Name;
                if (!string.IsNullOrEmpty(x.SpendingCategory))
                    x.SpendingCategoryName = spendingCategory?.FirstOrDefault(s => s.Code == x.SpendingCategory)?.Name;
            });
            responseDto.FinanceInfo = financeInfo;
            //附件
            if (!string.IsNullOrEmpty(applicationEntity.vendorUser.vendor.AttachmentInformation))
            {
                var attachmentIds = applicationEntity.vendorUser.vendor.AttachmentInformation?.Split(',').Select(x => Guid.Parse(x)).ToArray();
                if (attachmentIds.Count() > 0)
                    responseDto.AttachmentInformation = await attachmentService.GetAttachmentsAsync(attachmentIds);
            }

            //DPS信息
            if (!string.IsNullOrEmpty(applicationEntity.vendorUser.vendor.DPSCheck))
            {
                var dpsCheckIds = applicationEntity.vendorUser.vendor.DPSCheck.Split(',').Select(Guid.Parse).ToArray();
                if (dpsCheckIds.Count() > 0)
                    responseDto.DPSCheck = await attachmentService.GetAttachmentsAsync(dpsCheckIds);
            }

            //电话验证审批岗附件
            if (!string.IsNullOrEmpty(applicationEntity.vendorUser.vendor.TelValidationAttachmentIds))
            {
                var attachmentIds = applicationEntity.vendorUser.vendor.TelValidationAttachmentIds.Split(",").Select(Guid.Parse).ToArray();
                responseDto.TelValidationAttachments = await LazyServiceProvider.LazyGetService<IAttachmentService>().GetAttachmentsAsync(attachmentIds);
            }

            if (!string.IsNullOrEmpty(applicationEntity.vendorUser.vendor.ApsPorperty))
            {
                responseDto.ApsPorperty = applicationEntity.vendorUser.vendor.ApsPorperty;
                responseDto.IsAPS = true;
            }
            else
            {
                responseDto.ApsPorperty = "";
                responseDto.IsAPS = false;
            }
            responseDto.BankName = responseDto.BankCode;

            return MessageResult.SuccessResult(responseDto);
        }
        #endregion


        #region 详情-正式供应商
        public async Task<MessageResult> GetPersonalDetailNonHcp(Guid vendorId)
        {
            try
            {
                var vendorQuery = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();
                var vendorPersonalQuery = await LazyServiceProvider.LazyGetService<IVendorPersonalRepository>().GetQueryableAsync();
                var vendorFinanceQuery = await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync();

                var attachmentQuery = await LazyServiceProvider.LazyGetService<IAttachmentRepository>().GetQueryableAsync();
                //var userQuery = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
                //var commonService = LazyServiceProvider.LazyGetService<ICommonService>();
                var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();

                var insightKey = this._configuration.GetValue<string>("ApplicationInsightKey");

                var query = vendorQuery.Where(x => x.Id == vendorId && x.VendorType == VendorTypes.NonHCPPerson)
                    .Join(vendorPersonalQuery, v => v.Id, p => p.VendorId, (a, b) => new { vendor = a, personal = b });
                var vendorEntity = query.FirstOrDefault();

                if (vendorEntity == null)
                    return MessageResult.FailureResult("该详情数据不存在");

                var responseDto = ObjectMapper.Map<Entities.Vendors.Vendor, VendorPersonalDetailResponseDto>(vendorEntity.vendor);
                ObjectMapper.Map(vendorEntity.personal, responseDto);

                //var certificateType = await dataverseService.GetDictionariesAsync(DictionaryType.CertificateType);
                //responseDto.CardType = certificateType.FirstOrDefault(x => x.Code == responseDto.CardType)?.Name;

                //responseDto.CityText = cities.FirstOrDefault(x => x.Code == responseDto.City)?.Name;
                //responseDto.ProvinceCity = [responseDto.Province, responseDto.City];
                //responseDto.ProvinceCityName = [responseDto.ProvinceText, responseDto.CityText];


                var provinces = await dataverseService.GetAllProvince(stateCode: null);
                var provinceText = provinces.FirstOrDefault(x => x.Code == responseDto.Province)?.Name;
                var cities = await dataverseService.GetAllCity(stateCode: null);
                var cityText = cities.FirstOrDefault(x => x.Code == responseDto.City)?.Name;
                responseDto.ProvinceCity = [responseDto.Province, responseDto.City];
                responseDto.ProvinceCityName = [provinceText, cityText];
                if (!string.IsNullOrEmpty(vendorEntity.vendor.BankCity))
                {
                    responseDto.BankCity = vendorEntity.vendor.BankCity.Split(",").ToArray();
                    responseDto.BankCityName = $"{provinces.FirstOrDefault(x => x.Code == responseDto.BankCity[0])?.Name}/{cities.FirstOrDefault(x => x.Code == responseDto.BankCity[1])?.Name}";
                }

                if (!string.IsNullOrEmpty(vendorEntity.vendor.BankCardNo))
                    responseDto.BankCardNo = AesHelper.Decryption(vendorEntity.vendor.BankCardNo, insightKey);

                if (!string.IsNullOrEmpty(vendorEntity.personal.CardNo))
                    responseDto.CardNo = AesHelper.Decryption(vendorEntity.personal.CardNo, insightKey);

                //证件照正面照
                if (!string.IsNullOrEmpty(vendorEntity.personal.CardPic))
                {
                    var cardPicAttachment = attachmentQuery.FirstOrDefault(x => x.Id == Guid.Parse(vendorEntity.personal.CardPic));
                    if (cardPicAttachment != null)
                    {
                        var cardPicAttachmentInfo = ObjectMapper.Map<Attachment, UploadFileResponseDto>(cardPicAttachment);
                        responseDto.CardPic = cardPicAttachmentInfo;
                    }
                }
                //银行卡图片
                if (!string.IsNullOrEmpty(vendorEntity.vendor.BankCardImg))
                {
                    var bankCardAttachment = attachmentQuery.FirstOrDefault(x => x.Id == Guid.Parse(vendorEntity.vendor.BankCardImg));
                    if (bankCardAttachment != null)
                    {
                        var bankCardAttachmentInfo = ObjectMapper.Map<Attachment, UploadFileResponseDto>(bankCardAttachment);
                        responseDto.BankCardImg = bankCardAttachmentInfo;
                    }
                }
                //财务信息
                var financesQuery = vendorFinanceQuery.Where(x => x.VendorId == vendorId);
                var financeEntities = financesQuery.ToList();
                var financeInfo = ObjectMapper.Map<List<VendorFinancial>, List<FinancialInformation>>(financeEntities);
                var companies = await dataverseService.GetCompanyList(stateCode: null);
                financeInfo.ForEach(x =>
                {
                    x.PaymentTermName = _commonService.GetPaymentTermName(x.Company, x.PaymentTerm);
                    x.Flag = true;
                    var comCur = companies.FirstOrDefault(c => c.CompanyCode == x.Company);
                    if (comCur != null)
                    {
                        x.CompanyName = comCur.CompanyName;
                        x.CurrencyName = comCur.CompanyCurrency?.FirstOrDefault(c => c.Code == x.Currency)?.Name;
                    }
                });
                responseDto.FinanceInfo = financeInfo;
                //附件
                if (!string.IsNullOrEmpty(vendorEntity.vendor.AttachmentInformation))
                {
                    var attachmentIds = vendorEntity.vendor.AttachmentInformation?.Split(',').Select(x => Guid.Parse(x)).ToList();
                    if (attachmentIds.Count() > 0)
                    {
                        var attachmentEntities = attachmentQuery.Where(x => attachmentIds.Contains(x.Id)).ToList();
                        var attachmentInfo = ObjectMapper.Map<List<Attachment>, List<UploadFileResponseDto>>(attachmentEntities);
                        responseDto.AttachmentInformation = attachmentInfo;
                    }
                }

                // 获取签署内容
                var _configuration = LazyServiceProvider.GetService<IConfiguration>();
                var _consentsetvice = LazyServiceProvider.GetService<IConsentService>();
                var consentSignedQuery = await LazyServiceProvider.LazyGetService<IConsentSignedRepository>().GetListAsync();
                //获取需要检查的最新版签署版本号
                string consentInfo = _configuration["Consent:ConsentCode"];
                var consentResponse = await _consentsetvice.GetConsentInfoAsync([.. consentInfo.Split(",")]);
                //获取讲者签署记录，判断是否已经完全签署
                var consentList = consentSignedQuery.Where(w => w.OneId == vendorEntity.vendor.OpenId || w.AppUserId == vendorEntity.vendor.UserId.ToString()).ToList();
                var isConsent = HasBeenFullySigned(consentList, consentResponse);
                responseDto.SignedStatus = isConsent;
                responseDto.SignedStatusString = isConsent ? "已签署" : "待签署";
                responseDto.BankName = responseDto.BankCode;
                return MessageResult.SuccessResult(responseDto);
            }
            catch (Exception ex)
            {
                var msgContent = $"NonSpeakerAppService's GetPersonalDetailNonHcp has an error : {ex.Message}";
                _logger.LogError(msgContent);
                return await Task.FromResult(MessageResult.FailureResult(new MessageModelBase(500, msgContent)));
            }
        }

        public async Task<MessageResult> GetPersonalDetailNonHcp(string vendorCode)
        {
            try
            {
                var vendorQuery = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();
                var vendorPersonalQuery = await LazyServiceProvider.LazyGetService<IVendorPersonalRepository>().GetQueryableAsync();
                var vendorFinanceQuery = await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync();

                var attachmentQuery = await LazyServiceProvider.LazyGetService<IAttachmentRepository>().GetQueryableAsync();
                //var userQuery = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
                //var commonService = LazyServiceProvider.LazyGetService<ICommonService>();
                var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();

                var insightKey = this._configuration.GetValue<string>("ApplicationInsightKey");

                var query = vendorQuery.Where(x => x.VendorCode == vendorCode && x.VendorType == VendorTypes.NonHCPPerson)
                    .Join(vendorPersonalQuery, v => v.Id, p => p.VendorId, (a, b) => new { vendor = a, personal = b });
                var vendorEntity = query.FirstOrDefault();

                if (vendorEntity == null)
                    return MessageResult.FailureResult("该详情数据不存在");

                var responseDto = ObjectMapper.Map<Entities.Vendors.Vendor, VendorPersonalDetailResponseDto>(vendorEntity.vendor);
                ObjectMapper.Map(vendorEntity.personal, responseDto);

                //var certificateType = await dataverseService.GetDictionariesAsync(DictionaryType.CertificateType);
                //responseDto.CardType = certificateType.FirstOrDefault(x => x.Code == responseDto.CardType)?.Name;

                //responseDto.CityText = cities.FirstOrDefault(x => x.Code == responseDto.City)?.Name;
                //responseDto.ProvinceCity = [responseDto.Province, responseDto.City];
                //responseDto.ProvinceCityName = [responseDto.ProvinceText, responseDto.CityText];


                var provinces = await dataverseService.GetAllProvince(stateCode: null);
                var provinceText = provinces.FirstOrDefault(x => x.Code == responseDto.Province)?.Name;
                var cities = await dataverseService.GetAllCity(stateCode: null);
                var cityText = cities.FirstOrDefault(x => x.Code == responseDto.City)?.Name;
                responseDto.ProvinceCity = [responseDto.Province, responseDto.City];
                responseDto.ProvinceCityName = [provinceText, cityText];
                if (!string.IsNullOrEmpty(vendorEntity.vendor.BankCity))
                {
                    responseDto.BankCity = vendorEntity.vendor.BankCity.Split(",").ToArray();
                    responseDto.BankCityName = $"{provinces.FirstOrDefault(x => x.Code == responseDto.BankCity[0])?.Name}/{cities.FirstOrDefault(x => x.Code == responseDto.BankCity[1])?.Name}";
                }

                if (!string.IsNullOrEmpty(vendorEntity.vendor.BankCardNo))
                    responseDto.BankCardNo = AesHelper.Decryption(vendorEntity.vendor.BankCardNo, insightKey);

                if (!string.IsNullOrEmpty(vendorEntity.personal.CardNo))
                    responseDto.CardNo = AesHelper.Decryption(vendorEntity.personal.CardNo, insightKey);

                //证件照正面照
                if (!string.IsNullOrEmpty(vendorEntity.personal.CardPic))
                {
                    var cardPicAttachment = attachmentQuery.FirstOrDefault(x => x.Id == Guid.Parse(vendorEntity.personal.CardPic));
                    if (cardPicAttachment != null)
                    {
                        var cardPicAttachmentInfo = ObjectMapper.Map<Attachment, UploadFileResponseDto>(cardPicAttachment);
                        responseDto.CardPic = cardPicAttachmentInfo;
                    }
                }
                //银行卡图片
                if (!string.IsNullOrEmpty(vendorEntity.vendor.BankCardImg))
                {
                    var bankCardAttachment = attachmentQuery.FirstOrDefault(x => x.Id == Guid.Parse(vendorEntity.vendor.BankCardImg));
                    if (bankCardAttachment != null)
                    {
                        var bankCardAttachmentInfo = ObjectMapper.Map<Attachment, UploadFileResponseDto>(bankCardAttachment);
                        responseDto.BankCardImg = bankCardAttachmentInfo;
                    }
                }
                //财务信息
                var financesQuery = vendorFinanceQuery.Where(x => x.VendorId == vendorEntity.vendor.Id);
                var financeEntities = financesQuery.ToList();
                var financeInfo = ObjectMapper.Map<List<VendorFinancial>, List<FinancialInformation>>(financeEntities);
                var companies = await dataverseService.GetCompanyList(stateCode: null);
                financeInfo.ForEach(x =>
                {
                    x.PaymentTermName = _commonService.GetPaymentTermName(x.Company, x.PaymentTerm);
                    x.Flag = true;
                    var comCur = companies.FirstOrDefault(c => c.CompanyCode == x.Company);
                    if (comCur != null)
                    {
                        x.CompanyName = comCur.CompanyName;
                        x.CurrencyName = comCur.CompanyCurrency?.FirstOrDefault(c => c.Code == x.Currency)?.Name;
                    }
                });
                responseDto.FinanceInfo = financeInfo;
                //附件
                if (!string.IsNullOrEmpty(vendorEntity.vendor.AttachmentInformation))
                {
                    var attachmentIds = vendorEntity.vendor.AttachmentInformation?.Split(',').Select(x => Guid.Parse(x)).ToList();
                    if (attachmentIds.Count() > 0)
                    {
                        var attachmentEntities = attachmentQuery.Where(x => attachmentIds.Contains(x.Id)).ToList();
                        var attachmentInfo = ObjectMapper.Map<List<Attachment>, List<UploadFileResponseDto>>(attachmentEntities);
                        responseDto.AttachmentInformation = attachmentInfo;
                    }
                }

                // 获取签署内容
                var _configuration = LazyServiceProvider.GetService<IConfiguration>();
                var _consentsetvice = LazyServiceProvider.GetService<IConsentService>();
                var consentSignedQuery = await LazyServiceProvider.LazyGetService<IConsentSignedRepository>().GetListAsync();
                //获取需要检查的最新版签署版本号
                string consentInfo = _configuration["Consent:ConsentCode"];
                var consentResponse = await _consentsetvice.GetConsentInfoAsync([.. consentInfo.Split(",")]);
                //获取讲者签署记录，判断是否已经完全签署
                var consentList = consentSignedQuery.Where(w => w.OneId == vendorEntity.vendor.OpenId || w.AppUserId == vendorEntity.vendor.UserId.ToString()).ToList();
                var isConsent = HasBeenFullySigned(consentList, consentResponse);
                responseDto.SignedStatus = isConsent;
                responseDto.SignedStatusString = isConsent ? "已签署" : "待签署";
                responseDto.BankName = responseDto.BankCode;
                return MessageResult.SuccessResult(responseDto);
            }
            catch (Exception ex)
            {
                var msgContent = $"NonSpeakerAppService's GetPersonalDetailNonHcp has an error : {ex.Message}";
                _logger.LogError(msgContent);
                return await Task.FromResult(MessageResult.FailureResult(new MessageModelBase(500, msgContent)));
            }
        }

        public async Task<MessageResult> GetOrganizationDetailHci(Guid vendorId)
        {
            try
            {
                var vendorQuery = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();
                var vendorOrgQuery = await LazyServiceProvider.LazyGetService<IVendorOrgnizationRepository>().GetQueryableAsync();
                var vendorFinanceQuery = await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync();

                var attachmentQuery = await LazyServiceProvider.LazyGetService<IAttachmentRepository>().GetQueryableAsync();
                //var userQuery = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
                //var commonService = LazyServiceProvider.LazyGetService<ICommonService>();
                var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();

                var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");

                var query = vendorQuery.Where(x => x.Id == vendorId && x.VendorType == VendorTypes.HCIAndOtherInstitutionsAR)
                    .Join(vendorOrgQuery, v => v.Id, o => o.VendorId, (a, b) => new { vendor = a, organization = b });
                var vendorEntity = query.FirstOrDefault();

                if (vendorEntity == null)
                    return MessageResult.FailureResult("该详情数据不存在");

                var responseDto = ObjectMapper.Map<Entities.Vendors.Vendor, VendorOrganizationDetailResponseDto>(vendorEntity.vendor);
                ObjectMapper.Map(vendorEntity.organization, responseDto);

                //var certificateType = await dataverseService.GetDictionariesAsync(DictionaryType.CertificateType);
                //responseDto.CardType = certificateType.FirstOrDefault(x => x.Code == responseDto.CardType)?.Name;

                //responseDto.CityText = cities.FirstOrDefault(x => x.Code == responseDto.City)?.Name;
                //responseDto.ProvinceCity = [responseDto.Province, responseDto.City];
                //responseDto.ProvinceCityName = [responseDto.ProvinceText, responseDto.CityText];


                var provinces = await dataverseService.GetAllProvince(stateCode: null);
                var provinceText = provinces.FirstOrDefault(x => x.Code == responseDto.Province)?.Name;
                var cities = await dataverseService.GetAllCity(stateCode: null);
                var cityText = cities.FirstOrDefault(x => x.Code == responseDto.City)?.Name;
                responseDto.ProvinceCity = [responseDto.Province, responseDto.City];
                responseDto.ProvinceCityName = [provinceText, cityText];
                if (!string.IsNullOrEmpty(vendorEntity.vendor.BankCity))
                {
                    responseDto.BankCity = vendorEntity.vendor.BankCity.Split(",").ToArray();
                    responseDto.BankCityName = $"{provinces.FirstOrDefault(x => x.Code == responseDto.BankCity[0])?.Name}/{cities.FirstOrDefault(x => x.Code == responseDto.BankCity[1])?.Name}";
                }

                if (!string.IsNullOrEmpty(vendorEntity.vendor.BankCardNo))
                    responseDto.BankCardNo = AesHelper.Decryption(vendorEntity.vendor.BankCardNo, insightKey);

                //银行卡图片
                if (!string.IsNullOrEmpty(vendorEntity.vendor.BankCardImg))
                {
                    var bankCardAttachment = attachmentQuery.FirstOrDefault(x => x.Id == Guid.Parse(vendorEntity.vendor.BankCardImg));
                    if (bankCardAttachment != null)
                    {
                        var bankCardAttachmentInfo = ObjectMapper.Map<Attachment, UploadFileResponseDto>(bankCardAttachment);
                        responseDto.BankCardImg = bankCardAttachmentInfo;
                    }
                }
                //财务信息
                var financesQuery = vendorFinanceQuery.Where(x => x.VendorId == vendorId);
                var financeEntities = financesQuery.ToList();
                var financeInfo = ObjectMapper.Map<List<VendorFinancial>, List<FinancialInformation>>(financeEntities);
                var companies = await dataverseService.GetCompanyList(stateCode: null);
                financeInfo.ForEach(x =>
                {
                    x.PaymentTermName = _commonService.GetPaymentTermName(x.Company, x.PaymentTerm);
                    x.Flag = true;
                    var comCur = companies.FirstOrDefault(c => c.CompanyCode == x.Company);
                    if (comCur != null)
                    {
                        x.CompanyName = comCur.CompanyName;
                        x.CurrencyName = comCur.CompanyCurrency?.FirstOrDefault(c => c.Code == x.Currency)?.Name;
                    }
                });
                responseDto.FinanceInfo = financeInfo;
                //附件
                if (!string.IsNullOrEmpty(vendorEntity.vendor.AttachmentInformation))
                {
                    var attachmentIds = vendorEntity.vendor.AttachmentInformation?.Split(',').Select(x => Guid.Parse(x)).ToList();
                    if (attachmentIds.Count() > 0)
                    {
                        var attachmentEntities = attachmentQuery.Where(x => attachmentIds.Contains(x.Id)).ToList();
                        var attachmentInfo = ObjectMapper.Map<List<Attachment>, List<UploadFileResponseDto>>(attachmentEntities);
                        responseDto.AttachmentInformation = attachmentInfo;
                    }
                }
                responseDto.BankName = responseDto.BankCode;
                return MessageResult.SuccessResult(responseDto);
            }
            catch (Exception ex)
            {
                var msgContent = $"NonSpeakerAppService's GetOrganizationDetailHci has an error : {ex.Message}";
                _logger.LogError(msgContent);
                return await Task.FromResult(MessageResult.FailureResult(new MessageModelBase(500, msgContent)));
            }
        }

        public async Task<MessageResult> GetOrganizationDetailNonHci(Guid vendorId)
        {
            try
            {
                var vendorQuery = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();
                var vendorOrgQuery = await LazyServiceProvider.LazyGetService<IVendorOrgnizationRepository>().GetQueryableAsync();
                var vendorFinanceQuery = await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync();

                var attachmentQuery = await LazyServiceProvider.LazyGetService<IAttachmentRepository>().GetQueryableAsync();
                //var userQuery = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
                //var commonService = LazyServiceProvider.LazyGetService<ICommonService>();
                var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();

                var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");

                var query = vendorQuery.Where(x => x.Id == vendorId && x.VendorType == VendorTypes.NonHCIInstitutionalAP)
                    .Join(vendorOrgQuery, v => v.Id, o => o.VendorId, (a, b) => new { vendor = a, organization = b });
                var vendorEntity = query.FirstOrDefault();

                if (vendorEntity == null)
                    return MessageResult.FailureResult("该详情数据不存在");

                var responseDto = ObjectMapper.Map<Entities.Vendors.Vendor, VendorOrganizationDetailResponseDto>(vendorEntity.vendor);
                ObjectMapper.Map(vendorEntity.organization, responseDto);
                responseDto.LastYearSales = responseDto.LastYearSales;
                //var certificateType = await dataverseService.GetDictionariesAsync(DictionaryType.CertificateType);
                //responseDto.CardType = certificateType.FirstOrDefault(x => x.Code == responseDto.CardType)?.Name;

                //responseDto.CityText = cities.FirstOrDefault(x => x.Code == responseDto.City)?.Name;
                //responseDto.ProvinceCity = [responseDto.Province, responseDto.City];
                //responseDto.ProvinceCityName = [responseDto.ProvinceText, responseDto.CityText];


                var provinces = await dataverseService.GetAllProvince(stateCode: null);
                var provinceText = provinces.FirstOrDefault(x => x.Code == responseDto.Province)?.Name;
                var cities = await dataverseService.GetAllCity(stateCode: null);
                var cityText = cities.FirstOrDefault(x => x.Code == responseDto.City)?.Name;
                responseDto.ProvinceCity = [responseDto.Province, responseDto.City];
                responseDto.ProvinceCityName = [provinceText, cityText];
                if (!string.IsNullOrEmpty(vendorEntity.vendor.BankCity))
                {
                    responseDto.BankCity = vendorEntity.vendor.BankCity.Split(",").ToArray();
                    responseDto.BankCityName = $"{provinces.FirstOrDefault(x => x.Code == responseDto.BankCity[0])?.Name}/{cities.FirstOrDefault(x => x.Code == responseDto.BankCity[1])?.Name}";
                }

                if (!string.IsNullOrEmpty(vendorEntity.vendor.BankCardNo))
                    responseDto.BankCardNo = AesHelper.Decryption(vendorEntity.vendor.BankCardNo, insightKey);

                //银行卡图片
                if (!string.IsNullOrEmpty(vendorEntity.vendor.BankCardImg))
                {
                    var bankCardAttachment = attachmentQuery.FirstOrDefault(x => x.Id == Guid.Parse(vendorEntity.vendor.BankCardImg));
                    if (bankCardAttachment != null)
                    {
                        var bankCardAttachmentInfo = ObjectMapper.Map<Attachment, UploadFileResponseDto>(bankCardAttachment);
                        responseDto.BankCardImg = bankCardAttachmentInfo;
                    }
                }
                //财务信息
                var financesQuery = vendorFinanceQuery.Where(x => x.VendorId == vendorId);
                var financeEntities = financesQuery.ToList();
                var financeInfo = ObjectMapper.Map<List<VendorFinancial>, List<FinancialInformation>>(financeEntities);
                var companies = await dataverseService.GetCompanyList(stateCode: null);
                var dpoCategory = await dataverseService.GetDictionariesAsync(DictionaryType.DpoCategory);
                var spendingCategory = await dataverseService.GetDictionariesAsync(DictionaryType.SpendingCategory);
                financeInfo.ForEach(x =>
                {
                    x.PaymentTermName = _commonService.GetPaymentTermName(x.Company, x.PaymentTerm);
                    x.Flag = true;
                    var comCur = companies.FirstOrDefault(c => c.CompanyCode == x.Company);
                    if (comCur != null)
                    {
                        x.CompanyName = comCur.CompanyName;
                        x.CurrencyName = comCur.CompanyCurrency?.FirstOrDefault(c => c.Code == x.Currency)?.Name;
                    }
                    if (!string.IsNullOrEmpty(x.DpoCategory))
                        x.DpoCategoryName = dpoCategory?.FirstOrDefault(d => d.Code == x.DpoCategory)?.Name;
                    if (!string.IsNullOrEmpty(x.SpendingCategory))
                        x.SpendingCategoryName = spendingCategory?.FirstOrDefault(s => s.Code == x.SpendingCategory)?.Name;
                });
                responseDto.FinanceInfo = financeInfo;
                //附件
                if (!string.IsNullOrEmpty(vendorEntity.vendor.AttachmentInformation))
                {
                    var attachmentIds = vendorEntity.vendor.AttachmentInformation?.Split(',').Select(x => Guid.Parse(x)).ToList();
                    if (attachmentIds.Count() > 0)
                    {
                        var attachmentEntities = attachmentQuery.Where(x => attachmentIds.Contains(x.Id)).ToList();
                        var attachmentInfo = ObjectMapper.Map<List<Attachment>, List<UploadFileResponseDto>>(attachmentEntities);
                        responseDto.AttachmentInformation = attachmentInfo;
                    }
                }
                responseDto.BankName = responseDto.BankCode;
                return MessageResult.SuccessResult(responseDto);
            }
            catch (Exception ex)
            {
                var msgContent = $"NonSpeakerAppService's GetOrganizationDetailNonHci has an error : {ex.Message}";
                _logger.LogError(msgContent);
                return await Task.FromResult(MessageResult.FailureResult(new MessageModelBase(500, msgContent)));
            }
        }
        #endregion


        #region 提交
        /// <summary>
        /// 非HCP个人提交
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> SubmitApplicationNonHcp(NonSpeakerApplicationPersonalDto request)
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(request.BankCardNo))
                    request.BankCardNo = request.BankCardNo.Trim().Replace("\n", "").Replace("\r", "").Replace("\t", "");
                if (!string.IsNullOrWhiteSpace(request.CardNo))
                    request.CardNo = request.CardNo.Trim().Replace("\n", "").Replace("\r", "").Replace("\t", "");

                var vendorQuery = LazyServiceProvider.LazyGetService<IVendorRepository>();
                var vendorPersonalQuery = LazyServiceProvider.LazyGetService<IVendorPersonalRepository>();
                var vendorFinancialQuery = LazyServiceProvider.LazyGetService<IVendorFinancialRepository>();
                var vendorApplicationQuery = LazyServiceProvider.LazyGetService<IVendorApplicationRepository>();
                var vendorApplicationPersonalQuery = LazyServiceProvider.LazyGetService<IVendorApplicationPersonalRepository>();
                var vendorApplicationFinanceQuery = LazyServiceProvider.LazyGetService<IVendorApplicationFinancialRepository>();
                var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");
                var consentSignedQuery = await LazyServiceProvider.LazyGetService<IConsentSignedRepository>().GetListAsync();
                var _consentsetvice = LazyServiceProvider.GetService<IConsentService>();
                var _identityUserRepository = LazyServiceProvider.GetService<IIdentityUserRepository>();
                Guid vendorApplicationId = Guid.Empty;
                var vendorApplicationCode = string.Empty;
                Guid originalApplyUserId;
                if (request.ApplicationType != ApplicationTypes.Create)
                {
                    var applyUserId = await ValidationVendorApplicationDuplication(vendorApplicationQuery, request.VendorCode, request.ID);
                    if (applyUserId != null)
                    {
                        var user = await _identityUserRepository.FindAsync((Guid)applyUserId);
                        if (user != null)
                        {
                            return MessageResult.SuccessResult(messageModel: new MessageModelBase(603, $"供应商正在由{user.Name}({user.Email})申请中，无法重复申请"));
                        }
                        else
                        {
                            return MessageResult.FailureResult("供应商正在被申请，但没有找到申请人信息");
                        }
                    }
                }
                if (request.ApplicationType == ApplicationTypes.Active && !VendorCommon.ValidateBeforeActivation(request.FinanceInfo))
                    return MessageResult.FailureResult("财务信息待生效或待激活才能提交");

                var cnt = await vendorApplicationQuery.FirstOrDefaultAsync(x => x.Id == request.ID);
                if (cnt != null)
                {
                    //新建时，NonHCP，需检查证件号是否唯一
                    var message = ValidationData(request, vendorApplicationQuery, vendorApplicationPersonalQuery, vendorQuery, vendorPersonalQuery);
                    if (!string.IsNullOrEmpty(message.Result))
                        return MessageResult.FailureResult(message.Result);

                    //update
                    var updRes = await SaveVendorApplicationNonHcp(request);
                    if (!updRes.Success)
                        return MessageResult.FailureResult(updRes.Message);
                    vendorApplicationId = (Guid)request.ID;
                    vendorApplicationCode = cnt.ApplicationCode;
                    originalApplyUserId = cnt.ApplyUserId;
                }
                else
                {
                    //若没查到,则ID设为空重新生成草稿
                    request.ID = null;
                    //检查是否有相同草稿提交
                    var message = ValidationData(request, vendorApplicationQuery, vendorApplicationPersonalQuery, vendorQuery, vendorPersonalQuery);
                    if (!string.IsNullOrEmpty(message.Result))
                        return MessageResult.FailureResult(message.Result);
                    //克隆草稿
                    var createVendor = await CloneApplicationNonHcpAsync(request);
                    if (!createVendor.Success) return MessageResult.FailureResult(createVendor.Message);
                    vendorApplicationId = Guid.Parse(createVendor.Data.ToString());
                    cnt = await vendorApplicationQuery.FirstOrDefaultAsync(x => x.Id == vendorApplicationId);
                    vendorApplicationCode = cnt.ApplicationCode;
                    originalApplyUserId = cnt.ApplyUserId;
                }

                //检查变更内容，是否变更了个人信息，是否变更了公司信息 默认都要触发 
                bool isChangedDoctorInfo = true, isDPSCheckInfo = true, isFinanceInfo = true;
                if (request.ApplicationType == ApplicationTypes.Update)
                {
                    var entity = await GetChangeContent(request, vendorQuery, vendorPersonalQuery, vendorFinancialQuery);
                    isChangedDoctorInfo = entity.Item1;
                    isDPSCheckInfo = entity.Item2;
                    isFinanceInfo = entity.Item3;
                }
                var newEntity = await vendorApplicationQuery.FirstOrDefaultAsync(g => g.Id == vendorApplicationId);

                //YTW 20241029 2517【讲者&非HCP个人】（新建&变更&激活）待签署状态不允许提交申请
                //获取需要检查的最新版签署版本号
                string consentInfo = _configuration["Consent:ConsentCode"];
                var consentResponse = await _consentsetvice.GetConsentInfoAsync([.. consentInfo.Split(",")]);
                //获取讲者签署记录，判断是否已经完全签署
                var consentList = consentSignedQuery.Where(w => w.OneId == newEntity.OpenId || w.AppUserId == newEntity.UserId.ToString()).ToList();
                var isConsent = HasBeenFullySigned(consentList, consentResponse);
                if (!isConsent && request.ApplicationType == ApplicationTypes.Create)
                    return MessageResult.FailureResult("待签署状态不能提交，请签署后重新提交");

                //创建审批任务
                var exemptType = EnumUtil.GetEnumIdValues<WorkflowTypeName>().ToDictionary(a => (WorkflowTypeName)a.Key, a => a.Value);
                var name = request.ApplicationType == Enums.ApplicationTypes.Create ? "供应商新建申请-非HCP个人" : request.ApplicationType == Enums.ApplicationTypes.Update ? "供应商变更申请-非HCP个人" : "供应商激活申请-非HCP个人";
                var createApproval = new CreateApprovalDto
                {
                    Name = name,
                    Department = cnt.ApplyUserBu,
                    BusinessFormId = vendorApplicationId.ToString(),
                    BusinessFormNo = vendorApplicationCode,
                    BusinessFormName = EntitiesNameConsts.NameConsts.VendorApplication,
                    Submitter = CurrentUser?.Id.ToString(),
                    OriginalApprovalId = originalApplyUserId,
                    FormData = "{\"isDPSCheckInfo\" : " + isDPSCheckInfo.ToString().ToLower() + ", \"isFinanceInfo\" : " + isFinanceInfo.ToString().ToLower() + ", \"isChangedCallBackInfo\" : " + isChangedDoctorInfo.ToString().ToLower() + "}",
                    WorkflowType = WorkflowTypeName.SupplierRequestNonHCPPerson,
                    InstanceName = $"{name}-{DateTime.Now.ToString("yyMMdd-hhmmss")}"
                };
                var approvalRecord = LazyServiceProvider.LazyGetService<IInitializeApprovalRecordService>();
                await approvalRecord.InsertInitApprovalRecordAsync(new InitializeApprovalRecordDto
                {
                    BusinessFormName = EntitiesNameConsts.NameConsts.VendorApplication,
                    BusinessId = vendorApplicationId,
                    InstanceId = Guid.NewGuid(),
                    Status = InitApprovalRecordStatus.Pending,
                    MaxRetries = 3,
                    RequestContent = JsonConvert.SerializeObject(createApproval)
                });

                if (vendorApplicationId != Guid.Empty)
                {
                    //更新申请的状态为“审批中”
                    var validStatus = new List<Statuses> { Statuses.Saved, Statuses.Returned, Statuses.Withdraw };
                    var vendorApp = await vendorApplicationQuery.FirstOrDefaultAsync(x => x.Id == vendorApplicationId && validStatus.Contains(x.Status));
                    if (vendorApp != null)
                    {
                        var applyUser = await _identityUserRepository.FindAsync(vendorApp.ApplyUserId);
                        vendorApp.Status = Statuses.Approving;
                        vendorApp.ApplyUserName = applyUser?.Name;
                        vendorApp.ApplyTime = DateTime.Now;
                        await vendorApplicationQuery.UpdateAsync(vendorApp);
                    }
                }

                return await Task.FromResult(MessageResult.SuccessResult(vendorApplicationId));
            }
            catch (Exception ex)
            {
                _logger.LogError($"NonSpeakerAppService's SubmitApplicationNonHcp has an error : {ex.Message}");
                return await Task.FromResult(MessageResult.FailureResult(new MessageModelBase(500, $"Update Application with NonHCP Failed! {ex.Message}")));
            }
        }

        /// <summary>
        /// 验证证件是否唯一 （个人）
        /// </summary>
        /// <param name="request"></param>
        /// <param name="vendorPersonal"></param>
        /// <param name="vendorApplicationPersonal"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        private async Task<string> ValidationData(NonSpeakerApplicationPersonalDto request, IVendorApplicationRepository vendorApplication, IVendorApplicationPersonalRepository vendorApplicationPersonal, IVendorRepository vendor, IVendorPersonalRepository vendorPersonal)
        {
            var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");
            var ErrorMsg = string.Empty;
            var EncryptionCardNo = string.Empty;
            if (!string.IsNullOrEmpty(request.CardNo))
                EncryptionCardNo = AesHelper.Encryption(request.CardNo, insightKey);
            //验证证件号
            try
            {
                //证件号验证
                var queryVendor = await vendor.GetQueryableAsync();
                var queryVendorPers = await vendorPersonal.GetQueryableAsync();
                var duplicateCardNoVendors = queryVendorPers
                    .Where(x => x.CardType == request.CardType && x.CardNo == EncryptionCardNo)
                    .Join(queryVendor, a => a.VendorId, b => b.Id, (a, b) => new { a.SPName, a.CardNo, b.VendorCode })
                    .Where(a => a.VendorCode != request.VendorCode)//排除当前行
                    .ToArray();
                if (duplicateCardNoVendors.Length > 0)
                    duplicateCardNoVendors.ToList().ForEach(x => { ErrorMsg += $"该证件号码在{x.VendorCode}号中与{x.SPName}讲者重复;"; });

                //审批流程验证
                var queryVendorApply = await vendorApplication.GetQueryableAsync();
                var duplicateApplies = queryVendorApply
                    .Where(x => x.VendorCode == request.VendorCode && x.Status == Statuses.Approving && x.Id != request.ID)
                    .Select(x => x.VendorCode).ToArray();
                if (duplicateApplies.Length > 0)
                    duplicateApplies.ToList().ForEach(x => ErrorMsg += $"{x}申请单号讲者正在执行审批流程，请勿重复提交;");

                //证件号验证
                var queryVendorApplyPers = await vendorApplicationPersonal.GetQueryableAsync();
                var duplicateCardNoVendorApplies = queryVendorApplyPers
                    .Where(x => x.CardType == request.CardType && x.CardNo == EncryptionCardNo)
                    .Join(queryVendorApply, a => a.ApplicationId, b => b.Id, (a, b) => new { a.SPName, b.VendorCode, b.Status })
                    .Where(x => x.Status == Statuses.Approving && x.VendorCode != request.VendorCode)//排除当前行
                    .ToArray();
                if (duplicateCardNoVendorApplies.Length > 0)
                    duplicateCardNoVendorApplies.ToList().ForEach(x => ErrorMsg += $"该证件号码在{x.VendorCode}号中与{x.SPName}讲者重复;");

                //电话号码验证
                VendorTypes[] vendorTypes = [VendorTypes.HCPPerson, VendorTypes.NonHCPPerson];//只验证个人、排除机构
                var duplicatePhoneVendors = queryVendor.Where(x => x.VendorCode != request.VendorCode && x.HandPhone == request.HandPhone && vendorTypes.Contains(x.VendorType)).Select(x => x.VendorCode).ToArray();
                if (duplicatePhoneVendors.Length > 0)
                    duplicatePhoneVendors.ToList().ForEach(x => ErrorMsg += $"该电话号码已被{x}讲者中注册，请查证;");

                //电话号码验证
                var duplicatePhoneApplies = queryVendorApply.Where(x => x.Id != request.ID && x.HandPhone == request.HandPhone && x.Status == Statuses.Approving && vendorTypes.Contains(x.VendorType)).ToArray();
                if (duplicatePhoneApplies.Length > 0)
                    duplicatePhoneApplies.ToList().ForEach(x => ErrorMsg += $"该电话号码已被{x.VendorCode}讲者中注册，请查证;");

                //验证版本号 若是更新操作或者激活操作直接提交时则不验证
                if (request.ID.HasValue)
                {
                    var vendorVersion = queryVendor.FirstOrDefault(x => x.VendorCode == request.VendorCode && (x.DraftVersion + 1).ToString() != request.DraftVersion);
                    if (vendorVersion != null)
                        ErrorMsg += $"{vendorVersion.VendorCode}中版本号为V{vendorVersion.DraftVersion}，当前草稿版本号为V{request.DraftVersion}，请重新生成草稿;";
                }
            }
            catch (Exception ex)
            {
                return ErrorMsg = ex.Message;
            }

            return ErrorMsg;
        }

        /// <summary>
        /// 验证机构号是否唯一 （机构）
        /// </summary>
        /// <param name="request"></param>
        /// <param name="vendorOrg"></param>
        /// <param name="vendorApplicationOrg"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        private async Task<string> ValidationHCIData(NonSpeakerApplicationOrganizationDto request, IVendorApplicationRepository vendorApplication, IVendorApplicationOrganizationRepository vendorApplicationOrg, IVendorRepository vendor, IVendorOrgnizationRepository vendorOrg, VendorTypes vendorType)
        {
            var avmQuery = await LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync();
            var fvmQuery = await LazyServiceProvider.LazyGetService<IBpcsPmfvmRepository>().GetQueryableAsync();
            var vendorBlackQuery = await LazyServiceProvider.LazyGetService<IVendorBlackListRepository>().GetNoFilterQueryable();
            var queryFinancial = await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync();
            var queryVendor = await vendor.GetQueryableAsync();
            var queryVendorOrg = await vendorOrg.GetQueryableAsync();

            var queryVendorApply = await vendorApplication.GetQueryableAsync();
            var queryVendorApplyOrgs = await vendorApplicationOrg.GetQueryableAsync();

            var ErrorMsg = string.Empty;
            //验证证件号
            try
            {
                bool checkDuplicateName = true;
                //变更或者激活时，如果名称没变化，不做名称的重复性校验，兼容迁移的旧数据
                if (request.ApplicationType == ApplicationTypes.Update || request.ApplicationType == ApplicationTypes.Active)
                {
                    var vendorNameCode = queryVendorOrg.Join(queryVendor, a => a.VendorId, b => b.Id, (a, b) => new { a.VendorName, b.VendorCode })
                    .Where(x => x.VendorCode == request.VendorCode).SingleOrDefault();
                    if (vendorNameCode?.VendorName == request.VendorName)
                    {
                        checkDuplicateName = false;
                    }
                }
                if (checkDuplicateName)
                {
                    var duplicateNameVendors = queryVendorOrg.Where(x => x.VendorName == request.VendorName)
                        .Join(queryVendor, a => a.VendorId, b => b.Id, (a, b) => new { a.VendorName, b.VendorCode })
                        .Where(x => x.VendorCode != request.VendorCode).ToArray();
                    if (duplicateNameVendors.Length > 0)
                        duplicateNameVendors.ToList().ForEach(x => ErrorMsg += $"{x.VendorName}该机构名称在{x.VendorCode}中重复;");
                }

                var duplicateVendorApplies = queryVendorApply
                    .Where(x => x.VendorCode == request.VendorCode && x.Status == Statuses.Approving && x.Id != request.ID)
                    .Select(x => x.VendorCode).ToArray();
                if (duplicateVendorApplies.Length > 0)
                    duplicateVendorApplies.ToList().ForEach(x => ErrorMsg += $"{x}申请单号正在执行审批流程，请勿重复提交;");

                if (checkDuplicateName)
                {
                    var duplicateNameApplies = queryVendorApplyOrgs.Where(x => x.VendorName == request.VendorName)
                        .Join(queryVendorApply, a => a.ApplicationId, b => b.Id, (a, b) => new { a.VendorName, b.VendorCode, b.Status })
                        .Where(x => x.Status == Statuses.Approving).ToArray();
                    if (duplicateNameApplies.Length > 0)
                        duplicateNameApplies.ToList().ForEach(x => ErrorMsg += $"{x.VendorName}该机构名称在{x.VendorCode}中重复;");
                }

                if (checkDuplicateName)
                {
                    //bpcs同步过来的供应商机构名称检查
                    var vendorFin = avmQuery.GroupJoin(queryFinancial, a => a.FinaId, a => a.Id, (a, b) => new { Avm = a, Fins = b })//遇到VendorFinancial 删除的情况会查询到null 所以后续做null 判断
                        .SelectMany(a => a.Fins.DefaultIfEmpty(), (a, b) => new { a.Avm, Fin = b })
                        .GroupJoin(queryVendor, a => a.Fin.VendorId, a => a.Id, (a, b) => new { a.Avm, Vendors = b })
                        .SelectMany(a => a.Vendors.DefaultIfEmpty(), (a, b) => new { a.Avm, Vendor = b })
                        .Where(a => a.Vendor.VendorCode == request.VendorCode)
                        .FirstOrDefault();
                    var vdType = new string[] { "NH", "NL" };//HCI机构、非HCI机构
                    var vd = avmQuery.Where(x => vdType.Contains(x.Vtype))
                        .Join(fvmQuery, a => new { vd = a.Vendor, nm = a.Vcmpny }, b => new { vd = b.Vnderx, nm = b.Vmcmpy }, (a, b) => new { a.Id, a.Vendor, a.Vndnam, b.Vextnm })
                        .Where(x => x.Vndnam == request.VendorName || x.Vextnm == request.VendorName)
                        .WhereIf(vendorFin != null, x => x.Id != vendorFin.Avm.Id)//编辑时排除本身
                        .ToList();
                    if (vendorFin != null && vd.Count > 0)
                    {
                        vd.ForEach(f => { ErrorMsg += $"{f.Vendor}中名为{f.Vndnam}/{f.Vextnm}的Bpcs机构名称重复;"; });
                    }
                    else if (vendorFin == null && vd.Count > 1)
                    {
                        vd.ForEach(f => { ErrorMsg += $"{f.Vendor}中名为{f.Vndnam}/{f.Vextnm}的Bpcs机构名称重复;"; });
                    }
                }


                if (request.ApplicationType == ApplicationTypes.Update || request.ApplicationType == ApplicationTypes.Active)
                {
                    if (request.FinanceInfo != null)
                    {
                        foreach (var financialInformation in request.FinanceInfo)
                        {
                            if (financialInformation.IsAddFlag)
                            {
                                var bools = queryVendor.Where(x => x.VendorCode != request.VendorCode)
                                    .Join(queryVendorOrg, a => a.Id, b => b.VendorId, (a, b) => new { a.Id, a.VendorCode, b.VendorName })
                                    .Join(queryFinancial, a => a.Id, b => b.VendorId,
                                        (a, b) => new { b.Id, b.VendorId, b.Company, a.VendorName, a.VendorCode })
                                    .Join(avmQuery, a => a.Id, b => b.FinaId, (a, b) => new { a.VendorName, a.VendorCode, a.Company })
                                    .Where(x => x.Company == financialInformation.Company && x.VendorName == request.VendorName).ToList();
                                if (bools.Count > 0)
                                {
                                    bools.ForEach(x => ErrorMsg += $"{x.VendorName}该机构名称在{x.VendorCode}中重复;");
                                }
                            }
                        }
                    }
                }

                if (vendorType == VendorTypes.HCIAndOtherInstitutionsAR)
                {
                    //HCI黑名单中已有的供应商名称 需要排除（仅生效的黑名单）
                    var exsistVendorBlack = vendorBlackQuery.Where(x => x.SPName == request.VendorName && (x.Status == BlackStatus.Effective)).ToList();
                    if (exsistVendorBlack.Count > 0)
                        exsistVendorBlack.ForEach(f => { ErrorMsg += $"\"{f.SPName}\"已存在黑名单列表，不允许提交;"; });
                }

                //验证版本号 若是更新操作或者激活操作直接提交时则不验证
                if (request.ID.HasValue)
                {
                    var vendorVersion = queryVendor.FirstOrDefault(x => x.VendorCode == request.VendorCode && (x.DraftVersion + 1).ToString() != request.DraftVersion);
                    if (vendorVersion != null)
                        ErrorMsg += $"{vendorVersion.VendorCode}中版本号为V{vendorVersion.DraftVersion}，当前草稿版本号为V{request.DraftVersion}，请重新生成草稿;";
                }
            }
            catch (Exception ex)
            {
                return ErrorMsg = ex.Message;
            }
            return ErrorMsg;
        }

        /// <summary>
        /// 验证修改的内容
        /// </summary>
        /// <param name="request"></param>
        /// <param name="vendor"></param>
        /// <param name="vendorPersonal"></param>
        /// <param name="vendorFinancial"></param>
        /// <exception cref="NotImplementedException"></exception>
        private async Task<(bool, bool, bool)> GetChangeContent(NonSpeakerApplicationPersonalDto request, IVendorRepository vendor, IVendorPersonalRepository vendorPersonal, IVendorFinancialRepository vendorFinancial)
        {
            var isChangedDoctor = true;
            var isDPSCheck = false;
            var isFinance = false;
            var vendorQuery = await vendor.GetQueryableAsync();
            var vendorPersonalQuery = await vendorPersonal.GetQueryableAsync();
            var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");
            //供应商信息
            var vendorData = vendorQuery.Join(vendorPersonalQuery, a => a.Id, b => b.VendorId, (a, b) => new { vendorData = a, vendorPersonalData = b })
                .FirstOrDefault(f => f.vendorData.VendorCode == request.VendorCode);
            //财务信息
            var vendorFinancialData = await vendorFinancial.GetListAsync(g => g.VendorId == vendorData.vendorData.Id);

            //匹配个人其他信息及银行信息
            if (request.Sex != (int)vendorData.vendorPersonalData.Sex || request.BankCode != vendorData.vendorData.BankCode ||
                request.CardNo != AesHelper.Decryption(vendorData.vendorPersonalData.CardNo, insightKey) || //2771[供应商管理][讲者&非HCP个人]目前允许了修改身份证号，需要身份证号修改时设置isFinanceInfo为true
                request.Email != vendorData.vendorPersonalData.Email ||
                request.BankCardNo != AesHelper.Decryption(vendorData.vendorData.BankCardNo, insightKey) ||
                request.BankCity.JoinAsString(",") != vendorData.vendorData.BankCity ||
                ((!string.IsNullOrEmpty(request.BankNo) || !string.IsNullOrEmpty(vendorData.vendorData.BankNo)) && request.BankNo != vendorData.vendorData.BankNo) ||
                ((!string.IsNullOrEmpty(request.BankSwiftCode) || !string.IsNullOrEmpty(vendorData.vendorData.BankSwiftCode)) && request.BankSwiftCode != vendorData.vendorData.BankSwiftCode) ||
                request.HandPhone != vendorData.vendorData.HandPhone)
            {
                isFinance = true;
            }
            //匹配个人信息
            if (request.SPName != vendorData.vendorPersonalData.SPName || request.Province != vendorData.vendorPersonalData.Province ||
                request.City != vendorData.vendorPersonalData.City || request.Address != vendorData.vendorPersonalData.Address ||
                request.PostCode != vendorData.vendorPersonalData.PostCode)
            {
                isDPSCheck = true;
                isFinance = true;
            }

            //有新增或者激活行
            var isCompanyChanged = new VendorCommon().CheckFinancialChanged(request.FinanceInfo, vendorFinancialData);
            if (isCompanyChanged)
            {
                isDPSCheck = true;
                isFinance = true;
            }

            #region old logic

            ////匹配公司信息
            //if (request.FinanceInfo.Count != vendorFinancialData.Count)
            //{
            //    isDPSCheck = true;
            //    isFinance = true;
            //}
            ////操作财务表中的激活按钮
            //if (request.FinanceInfo.FirstOrDefault(f => f.FinancialVendorStatus == FinancialVendorStatus.ToBeActivated) != null)
            //{
            //    isDPSCheck = true;
            //    isFinance = true;
            //}

            #endregion

            return (isChangedDoctor, isDPSCheck, isFinance);
        }

        /// <summary>
        /// HCI机构提交
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> SubmitApplicationHci(NonSpeakerApplicationOrganizationDto request)
        {
            try
            {
                var vendorQuery = LazyServiceProvider.LazyGetService<IVendorRepository>();
                var vendorOrgQuery = LazyServiceProvider.LazyGetService<IVendorOrgnizationRepository>();
                var vendorFinancialQuery = LazyServiceProvider.LazyGetService<IVendorFinancialRepository>();
                var vendorApplicationQuery = LazyServiceProvider.LazyGetService<IVendorApplicationRepository>();
                var vendorApplicationOrgQuery = LazyServiceProvider.LazyGetService<IVendorApplicationOrganizationRepository>();
                var vendorApplicationFinanceQuery = LazyServiceProvider.LazyGetService<IVendorApplicationFinancialRepository>();
                Guid vendorApplicationId = Guid.Empty;
                string vendorApplicationCode = string.Empty;
                Guid originalApplyUserId;

                if (request.ApplicationType == ApplicationTypes.Active && !VendorCommon.ValidateBeforeActivation(request.FinanceInfo))
                    return MessageResult.FailureResult("财务信息待生效或待激活才能提交");

                var cnt = await vendorApplicationQuery.FirstOrDefaultAsync(x => x.Id == request.ID);
                if (cnt != null)
                {
                    //新建时，HCI/NonHCI，需检查机构号是否唯一
                    var message = ValidationHCIData(request, vendorApplicationQuery, vendorApplicationOrgQuery, vendorQuery, vendorOrgQuery, VendorTypes.HCIAndOtherInstitutionsAR);
                    
                    if (!string.IsNullOrEmpty(message.Result))
                        return MessageResult.FailureResult(message.Result);

                    vendorApplicationId = (Guid)request.ID;
                    vendorApplicationCode = cnt.ApplicationCode;
                    originalApplyUserId = cnt.ApplyUserId;
                    //update
                    var updRes = await SaveVendorApplicationHciOrg(request);
                    if (!updRes.Success)
                        return MessageResult.FailureResult(updRes.Message);
                }
                else
                {
                    //若没查到,则ID设为空重新生成草稿
                    request.ID = null;
                    //检查是否有相同草稿提交
                    var message = ValidationHCIData(request, vendorApplicationQuery, vendorApplicationOrgQuery, vendorQuery, vendorOrgQuery, VendorTypes.HCIAndOtherInstitutionsAR);
                    if (!string.IsNullOrEmpty(message.Result))
                        return MessageResult.FailureResult(message.Result);
                    //created
                    var result = await CreateApplicationHci(request);
                    if (!result.Success) return result;
                    cnt = await vendorApplicationQuery.FirstOrDefaultAsync(x => x.Id == Guid.Parse(result.Data.ToString()));
                    vendorApplicationCode = cnt.ApplicationCode;
                    vendorApplicationId = Guid.Parse(result.Data.ToString());
                    originalApplyUserId = cnt.ApplyUserId;
                }


                //检查变更内容，是否变更了个人信息，是否变更了公司信息 默认都要触发
                bool isChangedDoctorInfo = true, isDPSCheckInfo = true, isFinanceInfo = true;
                if (request.ApplicationType == ApplicationTypes.Update)
                {
                    var entity = await GetChangeContentHCI(request, vendorQuery, vendorOrgQuery, vendorFinancialQuery);
                    isChangedDoctorInfo = entity.Item1;
                    isDPSCheckInfo = entity.Item2;
                    isFinanceInfo = entity.Item3;
                }

                //创建审批任务
                var exemptType = EnumUtil.GetEnumIdValues<WorkflowTypeName>().ToDictionary(a => (WorkflowTypeName)a.Key, a => a.Value);

                var name = request.ApplicationType == Enums.ApplicationTypes.Create ? "供应商新建申请-HCI机构" : request.ApplicationType == Enums.ApplicationTypes.Update ? "供应商变更申请-HCI机构" : "供应商激活申请-HCI机构";
                var createApproval = new CreateApprovalDto
                {
                    Name = name,
                    Department = cnt.ApplyUserBu,
                    BusinessFormId = vendorApplicationId.ToString(),
                    BusinessFormNo = vendorApplicationCode,
                    BusinessFormName = EntitiesNameConsts.NameConsts.VendorApplication,
                    Submitter = CurrentUser?.Id.ToString(),
                    OriginalApprovalId = originalApplyUserId,
                    FormData = "{\"isDPSCheckInfo\" : " + isDPSCheckInfo.ToString().ToLower() + ", \"isFinanceInfo\" : " + isFinanceInfo.ToString().ToLower() + ", \"isChangedCallBackInfo\" : " + isChangedDoctorInfo.ToString().ToLower() + "}",
                    WorkflowType = WorkflowTypeName.SupplierRequestHCIInstitution,
                    InstanceName = $"{name}-{DateTime.Now.ToString("yyMMdd-hhmmss")}"
                };
                var approvalRecord = LazyServiceProvider.LazyGetService<IInitializeApprovalRecordService>();
                await approvalRecord.InsertInitApprovalRecordAsync(new InitializeApprovalRecordDto
                {
                    BusinessFormName = EntitiesNameConsts.NameConsts.VendorApplication,
                    BusinessId = vendorApplicationId,
                    InstanceId = Guid.NewGuid(),
                    Status = InitApprovalRecordStatus.Pending,
                    MaxRetries = 3,
                    RequestContent = JsonConvert.SerializeObject(createApproval)
                });

                if (vendorApplicationId != Guid.Empty)
                {
                    //更新申请的状态为“审批中”
                    var validStatus = new List<Statuses> { Statuses.Saved, Statuses.Returned, Statuses.Withdraw };
                    var vendorApp = await vendorApplicationQuery.FirstOrDefaultAsync(x => x.Id == vendorApplicationId && validStatus.Contains(x.Status));
                    if (vendorApp != null)
                    {
                        var applyUser = await LazyServiceProvider.GetService<IIdentityUserRepository>().FindAsync(vendorApp.ApplyUserId);
                        vendorApp.ApplyUserName = applyUser?.Name;
                        vendorApp.Status = Statuses.Approving;
                        vendorApp.ApplyTime = DateTime.Now;
                        await vendorApplicationQuery.UpdateAsync(vendorApp);
                    }
                }

                return await Task.FromResult(MessageResult.SuccessResult(vendorApplicationId));
            }
            catch (Exception ex)
            {
                _logger.LogError($"NonSpeakerAppService's SubmitApplicationHci has an error : {ex.Message}");
                return await Task.FromResult(MessageResult.FailureResult(new MessageModelBase(500, $"Update Application with HCI Failed! {ex.Message}")));
            }
        }

        /// <summary>
        /// 验证修改的内容-HCI机构
        /// </summary>
        /// <param name="request"></param>
        /// <param name="vendor"></param>
        /// <param name="vendorPersonal"></param>
        /// <param name="vendorFinancial"></param>
        /// <exception cref="NotImplementedException"></exception>
        private async Task<(bool, bool, bool)> GetChangeContentHCI(NonSpeakerApplicationOrganizationDto request, IVendorRepository vendor, IVendorOrgnizationRepository vendorPersonal, IVendorFinancialRepository vendorFinancial)
        {
            var isChangedDoctor = true;
            var isDPSCheck = false;
            var isFinance = false;
            var vendorQuery = await vendor.GetQueryableAsync();
            var vendorPersonalQuery = await vendorPersonal.GetQueryableAsync();
            var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");
            //供应商信息
            var vendorData = vendorQuery.Join(vendorPersonalQuery, a => a.Id, b => b.VendorId, (a, b) => new { vendorData = a, vendorPersonalData = b })
                .FirstOrDefault(f => f.vendorData.VendorCode == request.VendorCode);
            //财务信息
            var vendorFinancialData = await vendorFinancial.GetListAsync(g => g.VendorId == vendorData.vendorData.Id);

            //匹配个人其他信息及银行信息
            if (request.BankCode != vendorData.vendorData.BankCode || request.BankCardNo != AesHelper.Decryption(vendorData.vendorData.BankCardNo, insightKey) ||
                request.BankCity.JoinAsString(",") != vendorData.vendorData.BankCity ||
                request.BankNo != vendorData.vendorData.BankNo || request.BankSwiftCode != vendorData.vendorData.BankSwiftCode ||
                request.ContactName != vendorData.vendorPersonalData.ContactName || request.ContactPhone != vendorData.vendorPersonalData.ContactPhone ||
                request.ContactEmail != vendorData.vendorPersonalData.ContactEmail)
            {
                isFinance = true;
            }
            //paymentTerm信息
            var oldPayment = vendorFinancialData.Select(s => s.PaymentTerm).OrderBy(o => o).ToList();
            var newPayment = vendorFinancialData.Select(s => s.PaymentTerm).OrderBy(o => o).ToList();
            if (oldPayment.SequenceEqual(newPayment))
            {
                isFinance = true;
            }
            //匹配个人信息
            if (request.VendorName != vendorData.vendorPersonalData.VendorName ||
                request.ProvinceCity[0].ToString() != vendorData.vendorPersonalData.Province || request.ProvinceCity[1].ToString() != vendorData.vendorPersonalData.City ||
                request.RegCertificateAddress != vendorData.vendorPersonalData.RegCertificateAddress || request.PostCode != vendorData.vendorPersonalData.PostCode)
            {
                isDPSCheck = true;
                isFinance = true;
            }

            //有新增或者激活行
            var isCompanyChanged = new VendorCommon().CheckFinancialChanged(request.FinanceInfo, vendorFinancialData);
            if (isCompanyChanged)
            {
                isDPSCheck = true;
                isFinance = true;
            }

            #region old logic

            ////匹配公司信息
            //if (request.FinanceInfo.Count != vendorFinancialData.Count)
            //{
            //    isDPSCheck = true;
            //    isFinance = true;
            //}
            ////操作财务表中的激活按钮
            //if (request.FinanceInfo.FirstOrDefault(f => f.FinancialVendorStatus == FinancialVendorStatus.ToBeActivated) != null)
            //{
            //    isDPSCheck = true;
            //    isFinance = true;
            //}

            #endregion

            return (isChangedDoctor, isDPSCheck, isFinance);
        }

        /// <summary>
        /// 非HCI机构提交
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> SubmitApplicationNonHci(NonSpeakerApplicationOrganizationDto request)
        {
            try
            {
                var vendorQuery = LazyServiceProvider.LazyGetService<IVendorRepository>();
                var vendorOrgQuery = LazyServiceProvider.LazyGetService<IVendorOrgnizationRepository>();
                var vendorFinancialQuery = LazyServiceProvider.LazyGetService<IVendorFinancialRepository>();
                var vendorApplicationQuery = LazyServiceProvider.LazyGetService<IVendorApplicationRepository>();
                var vendorApplicationOrgQuery = LazyServiceProvider.LazyGetService<IVendorApplicationOrganizationRepository>();
                var vendorApplicationFinanceQuery = LazyServiceProvider.LazyGetService<IVendorApplicationFinancialRepository>();
                Guid vendorApplicationId = Guid.Empty;
                string vendorApplicationCode = string.Empty;
                Guid originalApplyUserId;

                if (request.ApplicationType == ApplicationTypes.Active && !VendorCommon.ValidateBeforeActivation(request.FinanceInfo))
                    return MessageResult.FailureResult("财务信息待生效或待激活才能提交");

                var cnt = await vendorApplicationQuery.FirstOrDefaultAsync(x => x.Id == request.ID);
                if (cnt != null)
                {
                    //新建时，HCI/NonHCI，需检查机构号是否唯一
                    var message = ValidationHCIData(request, vendorApplicationQuery, vendorApplicationOrgQuery, vendorQuery, vendorOrgQuery, VendorTypes.NonHCIInstitutionalAP);
                    if (!string.IsNullOrEmpty(message.Result))
                        return MessageResult.FailureResult(message.Result);

                    vendorApplicationId = (Guid)request.ID;
                    vendorApplicationCode = cnt.ApplicationCode;
                    originalApplyUserId = cnt.ApplyUserId;
                    //update
                    var updRes = await SaveVendorApplicationNonHciOrg(request);
                    if (!updRes.Success)
                        return MessageResult.FailureResult(updRes.Message);
                }
                else
                {
                    //若没查到,则ID设为空重新生成草稿
                    request.ID = null;
                    //新建时，HCI/NonHCI，需检查机构号是否唯一
                    var message = ValidationHCIData(request, vendorApplicationQuery, vendorApplicationOrgQuery, vendorQuery, vendorOrgQuery, VendorTypes.NonHCIInstitutionalAP);
                    if (!string.IsNullOrEmpty(message.Result))
                        return MessageResult.FailureResult(message.Result);
                    //create
                    var result = await CreateApplicationNonHci(request);
                    if (!result.Success) return result;
                    cnt = await vendorApplicationQuery.FirstOrDefaultAsync(x => x.Id == Guid.Parse(result.Data.ToString()));

                    vendorApplicationCode = cnt.ApplicationCode;
                    vendorApplicationId = Guid.Parse(result.Data.ToString());
                    originalApplyUserId = cnt.ApplyUserId;
                }


                //检查变更内容，是否变更了个人信息，是否变更了公司信息 默认都要触发
                bool isChangedDoctorInfo = false, isDPSCheckInfo = true, isFinanceInfo = true;
                if (request.ApplicationType == ApplicationTypes.Update)//2252【供应商激活】【非HCI机构】激活流程不需要走”电话验证岗“
                {
                    var entity = await GetChangeContentNoneHCI(request, vendorQuery, vendorOrgQuery, vendorFinancialQuery);
                    isChangedDoctorInfo = entity.Item1;
                    isDPSCheckInfo = entity.Item2;
                    //isFinanceInfo = entity.Item3;
                    //3841 ytw 20250303 【非HCI机构】采购供应商审核时，若历史数据spending category和dpo category为空时，需要提供入口补此空项   (isFinanceInfo,BA确认：审批时无法变更该字段值，提交时就设置为true.当spending category和dpo category 变更时可以触发”财务供应商维护岗"审批及后续处理)
                    isFinanceInfo = true;
                }

                //创建审批任务
                var exemptType = EnumUtil.GetEnumIdValues<WorkflowTypeName>().ToDictionary(a => (WorkflowTypeName)a.Key, a => a.Value);
                var name = request.ApplicationType == Enums.ApplicationTypes.Create ? "供应商新建申请-非HCI机构" : request.ApplicationType == Enums.ApplicationTypes.Update ? "供应商变更申请-非HCI机构" : "供应商激活申请-非HCI机构";
                var createApproval = new CreateApprovalDto
                {
                    Name = name,
                    Department = cnt.ApplyUserBu,
                    BusinessFormId = vendorApplicationId.ToString(),
                    BusinessFormNo = vendorApplicationCode,
                    BusinessFormName = EntitiesNameConsts.NameConsts.VendorApplication,
                    Submitter = CurrentUser?.Id.ToString(),
                    OriginalApprovalId = originalApplyUserId,
                    FormData = "{\"isDPSCheckInfo\" : " + isDPSCheckInfo.ToString().ToLower() + ", \"isFinanceInfo\" : " + isFinanceInfo.ToString().ToLower() + ", \"isChangedCallBackInfo\" : " + isChangedDoctorInfo.ToString().ToLower() + "}",
                    WorkflowType = WorkflowTypeName.SupplierRequestNonHCIInstitution,
                    InstanceName = $"{name}-{DateTime.Now.ToString("yyMMdd-hhmmss")}"
                };
                var approvalRecord = LazyServiceProvider.LazyGetService<IInitializeApprovalRecordService>();
                await approvalRecord.InsertInitApprovalRecordAsync(new InitializeApprovalRecordDto
                {
                    BusinessFormName = EntitiesNameConsts.NameConsts.VendorApplication,
                    BusinessId = vendorApplicationId,
                    InstanceId = Guid.NewGuid(),
                    Status = InitApprovalRecordStatus.Pending,
                    MaxRetries = 3,
                    RequestContent = JsonConvert.SerializeObject(createApproval)
                });

                if (vendorApplicationId != Guid.Empty)
                {
                    //更新申请的状态为“审批中”
                    var validStatus = new List<Statuses> { Statuses.Saved, Statuses.Returned, Statuses.Withdraw };
                    var vendorApp = await vendorApplicationQuery.FirstOrDefaultAsync(x => x.Id == vendorApplicationId && validStatus.Contains(x.Status));
                    if (vendorApp != null)
                    {
                        var applyUser = await LazyServiceProvider.GetService<IIdentityUserRepository>().FindAsync(vendorApp.ApplyUserId);
                        vendorApp.ApplyUserName = applyUser?.Name;
                        vendorApp.Status = Statuses.Approving;
                        vendorApp.ApplyTime = DateTime.Now;
                        await vendorApplicationQuery.UpdateAsync(vendorApp);
                    }
                }

                return await Task.FromResult(MessageResult.SuccessResult(vendorApplicationId));
            }
            catch (Exception ex)
            {
                _logger.LogError($"NonSpeakerAppService's SubmitApplicationNonHci has an error : {ex.Message}");
                return await Task.FromResult(MessageResult.FailureResult(new MessageModelBase(500, $"Update Application with NonHCI Failed! {ex.Message}")));
            }
        }

        /// <summary>
        /// 验证修改的内容-非HCI机构
        /// </summary>
        /// <param name="request"></param>
        /// <param name="vendor"></param>
        /// <param name="vendorPersonal"></param>
        /// <param name="vendorFinancial"></param>
        /// <exception cref="NotImplementedException"></exception>
        private async Task<(bool, bool, bool)> GetChangeContentNoneHCI(NonSpeakerApplicationOrganizationDto request, IVendorRepository vendor, IVendorOrgnizationRepository vendorPersonal, IVendorFinancialRepository vendorFinancial)
        {
            var isChangedDoctor = false;
            var isDPSCheck = false;
            var isFinance = false;
            var vendorQuery = await vendor.GetQueryableAsync();
            var vendorPersonalQuery = await vendorPersonal.GetQueryableAsync();
            var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");
            //供应商信息
            var vendorData = vendorQuery.Join(vendorPersonalQuery, a => a.Id, b => b.VendorId, (a, b) => new { vendorData = a, vendorPersonalData = b })
                .FirstOrDefault(f => f.vendorData.VendorCode == request.VendorCode);
            //财务信息
            var vendorFinancialData = await vendorFinancial.GetListAsync(g => g.VendorId == vendorData.vendorData.Id);

            //匹配信息
            if (//request.VendorName != vendorData.vendorPersonalData.VendorName ||
                request.ProvinceCity[0].ToString() != vendorData.vendorPersonalData.Province || request.ProvinceCity[1].ToString() != vendorData.vendorPersonalData.City ||
                request.RegCertificateAddress != vendorData.vendorPersonalData.RegCertificateAddress ||
                request.ContactName != vendorData.vendorPersonalData.ContactName ||
                request.ContactPhone != vendorData.vendorPersonalData.ContactPhone || request.ContactEmail != vendorData.vendorPersonalData.ContactEmail ||
                request.BankCode != vendorData.vendorData.BankCode ||
                request.BankCardNo != AesHelper.Decryption(vendorData.vendorData.BankCardNo, insightKey) ||
                request.BankCity.JoinAsString(",") != vendorData.vendorData.BankCity ||
                request.BankNo != vendorData.vendorData.BankNo || request.BankSwiftCode != vendorData.vendorData.BankSwiftCode ||
                request.VendorEngName != vendorData.vendorPersonalData.VendorEngName || request.PostCode != vendorData.vendorPersonalData.PostCode //2480【供应商管理】【非HCI机构】非HCI机构的邮编/英文名字也属于关键信息，如有变更，需要走电话验证岗位---BE
                )
            {
                isChangedDoctor = true;
                isFinance = true;
            }
            //匹配邮编和payment
            var oldPayment = vendorFinancialData.Select(s => s.PaymentTerm).OrderBy(o => o).ToList();
            var newPayment = vendorFinancialData.Select(s => s.PaymentTerm).OrderBy(o => o).ToList();
            var oldCategory = vendorFinancialData.Select(s => s.SpendingCategory).OrderBy(o => o).ToList();
            var newCategory = vendorFinancialData.Select(s => s.SpendingCategory).OrderBy(o => o).ToList();
            if (request.PostCode != vendorData.vendorPersonalData.PostCode ||
                oldPayment.SequenceEqual(newPayment) || oldCategory.SequenceEqual(newCategory))
            {
                isFinance = true;
            }

            //有新增或者激活行
            var isCompanyChanged = new VendorCommon().CheckFinancialChanged(request.FinanceInfo, vendorFinancialData);
            if (isCompanyChanged)
                isFinance = true;

            #region old logic

            ////匹配公司信息
            //if (request.FinanceInfo.Count != vendorFinancialData.Count)
            //{
            //    isFinance = true;
            //}

            #endregion

            return (isChangedDoctor, isDPSCheck, isFinance);
        }

        #endregion
        #region 小程序 验证身份证号码重复

        /// <summary>
        /// 验证证件是否唯一
        /// </summary>
        /// <param name="request"></param>
        /// <param name="vendorPersonal"></param>
        /// <param name="vendorApplicationPersonal"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        private async Task<string> ValidationCardNo(NonSpeakerApplicationPersonalDto request, IVendorApplicationRepository vendorApplication, IVendorApplicationPersonalRepository vendorApplicationPersonal, IVendorRepository vendor, IVendorPersonalRepository vendorPersonal)
        {

            var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");
            var ErrorMsg = string.Empty;
            var EncryptionCardNo = string.Empty;
            if (!string.IsNullOrEmpty(request.CardNo))
                EncryptionCardNo = AesHelper.Encryption(request.CardNo, insightKey);
            //验证证件号
            try
            {
                //正式表数据验证
                var queryVendor = await vendor.GetQueryableAsync();
                var queryVendorPers = await vendorPersonal.GetQueryableAsync();
                var duplicateCardNoVendors = queryVendorPers
                    .Where(x => x.CardType == request.CardType && x.CardNo == EncryptionCardNo)
                    .Join(queryVendor, a => a.VendorId, b => b.Id, (a, b) => new { a.SPName, a.CardNo, b.VendorCode })
                    .Where(a => a.VendorCode != request.VendorCode)//排除当前行
                    .ToArray();
                if (duplicateCardNoVendors.Length > 0)
                    ErrorMsg = $"身份证号码在{duplicateCardNoVendors[0].VendorCode}中重复";
                //duplicateCardNoVendors.ToList().ForEach(x => { ErrorMsg += $"该证件号码在{x.VendorCode}号中与{x.SPName}讲者重复;"; });

                //申请草稿数据验证
                var queryVendorApply = await vendorApplication.GetQueryableAsync();
                var queryVendorApplyPers = await vendorApplicationPersonal.GetQueryableAsync();
                var duplicateCardNoVendorApplies = queryVendorApplyPers
                    .Where(x => x.CardType == request.CardType && x.CardNo == EncryptionCardNo)
                    .Join(queryVendorApply, a => a.ApplicationId, b => b.Id, (a, b) => new { a.SPName, b.VendorCode, b.Status })
                    .Where(x => (x.Status == Statuses.Approving || x.Status == Statuses.Returned || x.Status == Statuses.Withdraw) && x.VendorCode != request.VendorCode)//排除当前行
                    .ToArray();
                if (duplicateCardNoVendorApplies.Length > 0)
                    ErrorMsg = $"身份证号码在{duplicateCardNoVendorApplies[0].VendorCode}中重复";
                //duplicateCardNoVendorApplies.ToList().ForEach(x => ErrorMsg += $"该证件号码在{x.VendorCode}号中与{x.SPName}讲者重复;");

            }
            catch (Exception ex)
            {
                return ErrorMsg = ex.Message;
            }

            return ErrorMsg;
        }
        #endregion
        /// <summary>
        /// 判断是否有重复申请
        /// </summary>
        /// <param name="vendorApplication"></param>
        /// <param name="vendorCode"></param>
        /// <param name="vendorApplicationId"></param>
        /// <returns></returns>
        private async Task<Guid?> ValidationVendorApplicationDuplication(IVendorApplicationRepository vendorApplication, string vendorCode, Guid? vendorApplicationId = null)
        {

            IEnumerable<Statuses> statuses = new List<Statuses> { Statuses.Saved, Statuses.Approving, Statuses.Returned, Statuses.Withdraw };
            statuses = statuses.ToHashSet();
            VendorApplication vendorapplication = null;
            if (vendorApplicationId != null)
            {
                vendorapplication = await vendorApplication.FirstOrDefaultAsync(f => f.Id != vendorApplicationId && f.VendorCode == vendorCode && statuses.Contains(f.Status));
            }
            else
            {
                vendorapplication = await vendorApplication.FirstOrDefaultAsync(f => f.VendorCode == vendorCode && statuses.Contains(f.Status));
            }

            if (vendorapplication == null)
            {
                return null;
            }
            else
            {
                return vendorapplication.ApplyUserId;
            }
        }
    }
}
