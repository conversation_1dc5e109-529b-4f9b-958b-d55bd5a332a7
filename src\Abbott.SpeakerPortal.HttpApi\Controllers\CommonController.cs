﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

using Abbott.SpeakerPortal.BackgroundWorkers.Approval;
using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.Approve;
using Abbott.SpeakerPortal.Contracts.Common.Attachment;
using Abbott.SpeakerPortal.Contracts.Common.Blob;
using Abbott.SpeakerPortal.Contracts.Common.Cognitive;
using Abbott.SpeakerPortal.Contracts.Common.Common;
using Abbott.SpeakerPortal.Contracts.Common.PowerApp;
using Abbott.SpeakerPortal.Contracts.Common.SMS;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPOApplication;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Extension;
using Abbott.SpeakerPortal.Permissions;
using Abbott.SpeakerPortal.Redis;
using Abbott.SpeakerPortal.User;
using Abbott.SpeakerPortal.Utils;

using Hangfire;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;

using Volo.Abp.Application.Dtos;

using static Abbott.SpeakerPortal.Enums.OECInterceptTypes;
using static Abbott.SpeakerPortal.Enums.Purchase;
using static Abbott.SpeakerPortal.Enums.PurGRStatus;
using static Abbott.SpeakerPortal.Enums.PurPAStatus;
using static Abbott.SpeakerPortal.Enums.SlideConfig;
using Abbott.SpeakerPortal.Extension;
using Senparc.Weixin.WxOpen.Entities;
using Microsoft.Extensions.FileSystemGlobbing.Internal;
using DocumentFormat.OpenXml.Wordprocessing;
using Microsoft.AspNetCore.Http.HttpResults;
using Abbott.SpeakerPortal.Enums.MSA;

namespace Abbott.SpeakerPortal.Controllers
{
    /// <summary>
    /// 通用
    /// </summary>
    [ApiExplorerSettings(GroupName = SwaggerGrouping.COMMON)]
    public class CommonController : SpeakerPortalController
    {
        ICommonService _commonService;
        private IDataverseService _dataverseService;
        private ISMSService _smsService;
        private IPowerappService _powerappService;
        private IApproveService _approveService;
        private readonly IUserService _userService;
        public CommonController(IServiceProvider serviceProvider, IUserService userService)
        {
            _commonService = serviceProvider.GetService<ICommonService>();
            _dataverseService = serviceProvider.GetService<IDataverseService>();
            _smsService = serviceProvider.GetService<ISMSService>();
            _powerappService = serviceProvider.GetService<IPowerappService>();
            _approveService = serviceProvider.GetService<IApproveService>();
            _userService = userService;
        }

        /// <summary>
        /// 获取所有基础数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<AllBasicDataDto>))]
        public IActionResult GetAllBasicDatas()
        {
            var purPOApplicationService = LazyServiceProvider.LazyGetService<IPurPOApplicationService>();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();

            var getAllDictionaries = Task.Run(async () => await dataverseService.GetDictionariesAsync());
            var getApsProperty = Task.Run(async () => await dataverseService.GetDictionariesAsync(DictionaryType.ApsProperty, null));//禁用的也需要取出
            var apsList = new List<DictionaryDto>();
            var getDivisionTask = Task.Run(async () => await dataverseService.GetDivisions());
            var getAllJobTilesTask = Task.Run(async () => await dataverseService.GetAllJobTiles());
            var getAllHospitalDepartmentsTask = Task.Run(async () => await dataverseService.GetAllDepartments());
            var getPturtType = Task.Run(_commonService.GetPturtTypeDictionaryAsync);
            var getCompanyList = Task.Run(async () => await _dataverseService.GetCompanyList());

            Task.WaitAll(getAllDictionaries, getDivisionTask, getAllJobTilesTask, getAllHospitalDepartmentsTask, getPturtType, getApsProperty, getCompanyList);

            //构建最终对象
            var result = new AllBasicDataDto();

            var crossBus = new List<string>()
            {
                "ADC",
                "AV",
                "CRDx",
                "EPD",
                "MND",
                "ARDx",
                "CRM",
                "AMD",
                "APOC",
                "EP",
                "SH",
                "ADD",
                "AND"
            };
            //从缓存中获取的数据
            var setValueFromRedisTask = Task.Run(async () =>
            {
                result.Divisions = getDivisionTask.Result.OrderBy(a => a.DepartmentName).Select(a => new KeyValuePair<Guid, string>(a.Id, a.DepartmentName));
                result.CrossBuDivisions = result.Divisions.Where(a => crossBus.Contains(a.Value, StringComparer.OrdinalIgnoreCase)).Select(s =>
                {
                    return new KeyValuePair<Guid, string>(s.Key, s.Value.ClearBu());

                }).OrderBy(s => s.Value);
                result.JobTiles = getAllJobTilesTask.Result.Select(a => new KeyValuePair<Guid, string>(a.Id, a.Name));
                result.HospitalDepartments = getAllHospitalDepartmentsTask.Result.Select(a => new KeyValuePair<Guid, string>(a.Id, a.Name));
                result.PturtTypeConfig = getPturtType.Result;
                result.InvoiceTypeTaxRates = await _commonService.GetInvoiceTypeTaxRateAsync();
            });

            //从数据库中获取的数据
            var setValueFromDbTask = Task.Run(async () =>
            {
                result.SimplePaymentTerms = await dataverseService.GetSimplePaymentTermsAsync();
                result.PaymentTerms = await purPOApplicationService.GetPaymentTermsAsync();
                result.ThePaymentTerms = await purPOApplicationService.GetThePaymentTermsAsync();
            });

            //从字典获取的数据
            var setValueFromDictTask = Task.Run(() =>
            {
                var dict = getAllDictionaries.Result.GroupBy(a => a.ParentCode).ToDictionary(a => a.Key, a => a.Select(a1 => a1));
                var constants = typeof(DictionaryType).GetFields(BindingFlags.Public | BindingFlags.Static | BindingFlags.FlattenHierarchy).Where(a => a.IsLiteral && !a.IsInitOnly).Select(a => a.GetRawConstantValue()?.ToString()).ToArray();
                var except = constants.Except(dict.Where(a => a.Value.Any()).Select(a => a.Key));

                //检测是否存在没有数据的键，如果存在则需要单独去拉取
                if (except.Any())
                {
                    var listTasks = new List<Task>();
                    foreach (var item in except)
                    {
                        var task = Task.Run(async () =>
                        {
                            var datas = await dataverseService.GetDictionariesAsync(item);
                            dict[item] = datas;
                        });
                        listTasks.Add(task);
                    }
                    Task.WaitAll(listTasks.ToArray());
                }

                IEnumerable<DictionaryDto> value = [];
                result.SpeakerLevel = dict.TryGetValue(DictionaryType.HCPLevel, out value) ? value : [];
                result.CertificateType = dict.TryGetValue(DictionaryType.CertificateType, out value) ? value : [];
                result.BankType = dict.TryGetValue(DictionaryType.BankType, out value) ? value : [];
                result.LearningLevel = dict.TryGetValue(DictionaryType.LearningLevel, out value) ? value : [];
                result.IdentityType = dict.TryGetValue(DictionaryType.IdentityType, out value) ? value : [];
                result.SpeakerRate = dict.TryGetValue(DictionaryType.SpeakerLevel, out value) ? value : [];
                result.PayType = dict.TryGetValue(DictionaryType.PayType, out value) ? value : [];
                result.Currency = dict.TryGetValue(DictionaryType.Currency, out value) ? value : [];
                result.RegisteredCertificateAuthorityType = dict.TryGetValue(DictionaryType.RegisteredCertificateAuthorityType, out value) ? value : [];
                result.ActiveTypes = dict.TryGetValue(DictionaryType.ActiveType, out value) ? value : [];
                result.ProjectTypes = dict.TryGetValue(DictionaryType.ProjectType, out value) ? value.OrderBy(x => x.Name) : [];
                result.MeetingTypes = dict.TryGetValue(DictionaryType.MeetingType, out value) ? value : [];
                result.MarketResearchLeaders = dict.TryGetValue(DictionaryType.MarketResearchLeader, out value) ? value : [];
                result.SponsorshipTypes = dict.TryGetValue(DictionaryType.SponsorshipType, out value) ? value : [];
                result.OrganizerNatures = dict.TryGetValue(DictionaryType.OrganizerNature, out value) ? value : [];
                result.TaxRates = dict.TryGetValue(DictionaryType.TaxRate, out value) ? value : [];
                result.SlideTypes = dict.TryGetValue(DictionaryType.SlideType, out value) ? value : [];
                result.SlideNames = dict.TryGetValue(DictionaryType.SlideName, out value) ? value : [];
                result.ConferenceFeeItems = dict.TryGetValue(DictionaryType.ConferenceFeeItem, out value) ? value : [];
                //result.ApsProperty = dict.TryGetValue(DictionaryType.ApsProperty, out value) ? value : [];
                result.DpoCategory = dict.TryGetValue(DictionaryType.DpoCategory, out value) ? value.OrderBy(o => o.Name) : [];
                result.SpendingCategory = dict.TryGetValue(DictionaryType.SpendingCategory, out value) ? value.OrderBy(o => o.Name) : [];
                result.DeliveryTypes = dict.TryGetValue(DictionaryType.DeliveryTypes, out value) ? value : [];
                result.DeliveryAddress = dict.TryGetValue(DictionaryType.DeliveryAddress, out value) ? value : [];
                result.PaymentConditions = dict.TryGetValue(DictionaryType.PaymentConditions, out value) ? value : [];
                result.HcpTravelAgencyConferenceFeeTypes = dict.TryGetValue(DictionaryType.HcpTravelAgencyConferenceFeeType, out value) ? value : [];
                result.InvoiceTypes = dict.TryGetValue(DictionaryType.InvoiceType, out value) ? value.Where(a => a.Code != DictionaryType.InvoiceTypes.NonInvoice) : [];//AP 排除无发票选项
                result.ARInvoiceTypes = dict.TryGetValue(DictionaryType.InvoiceType, out value) ? value : [];
                result.SerialMeetingType = dict.TryGetValue(DictionaryType.SerialMeetingType, out value) ? value : [];
                result.RatingStandard = dict.TryGetValue(DictionaryType.RatingStandard, out value) ? value.OrderBy(x => x.Name) : [];//供应商打分信息按照分数排序
                result.AssociationGrade = dict.TryGetValue(DictionaryType.AssociationGrade, out value) ? value : [];
                result.AssociationType = dict.TryGetValue(DictionaryType.AssociationType, out value) ? value : [];
                result.AssociationJobAType001 = dict.TryGetValue(DictionaryType.AssociationJob1, out value) ? value : [];
                result.AssociationJobAType002 = dict.TryGetValue(DictionaryType.AssociationJob2, out value) ? value : [];
                result.AssociationJobStatus = dict.TryGetValue(DictionaryType.AssociationJobStatus, out value) ? value : [];
                result.HcoLevel = dict.TryGetValue(DictionaryType.HcoLevel, out value) ? value : [];
                result.WaiverReason = dict.TryGetValue(DictionaryType.WaiverReason, out value) ? value : [];
                result.JustificationType = dict.TryGetValue(DictionaryType.JustificationType, out value) ? value : [];
                result.InterceptTypes = dict.TryGetValue(DictionaryType.InterceptTypes, out value) ? value : [];
                result.ConcurPaymentStatus = dict.TryGetValue(DictionaryType.ConcurPaymentStatus, out value) ? value : [];
                result.ConcurApprovalStatus = dict.TryGetValue(DictionaryType.ConcurApprovalStatus, out value) ? value : [];
            });

            //回显时需要（未启用的）
            result.ApsProperty = getApsProperty.Result;

            //获取公司
            result.CompanyCodes = getCompanyList.Result.Select(a => new KeyValuePair<string, string>(a.CompanyCode, a.CompanyName));


            //从枚举获取的数据
            var setValueFromEnumTask = Task.Run(() =>
            {
                result.EffectStatus = EnumUtil.GetEnumIdValues<EffectStatus>();
                result.VendorTypes = EnumUtil.GetEnumIdValues<VendorTypes>();
                result.PayStandardOperators = EnumUtil.GetEnumIdValues<PayStandardConfigOperator>();
                result.PurExemptStatus = EnumUtil.GetEnumIdValues(PurExemptStatus.Draft);
                result.VendorStatus = EnumUtil.GetEnumIdValues<VendorStatus>();
                result.BlackStatus = EnumUtil.GetEnumIdValues<BlackStatus>();
                result.FunctionModules = EnumUtil.GetEnumIdValues<FunctionModule>();
                result.PurExemptTypes = EnumUtil.GetEnumIdValues<PurExemptType>();
                result.ApprovalStatus = EnumUtil.GetEnumIdValues<ApprovalStatus>();
                result.ApprovalPowerAppStatus = EnumUtil.GetEnumIdValues<ApprovalPowerAppStatus>();
                result.ApprovalOperation = EnumUtil.GetEnumIdValues<ApprovalOperation>();
                result.PositionTypes = EnumUtil.GetEnumIdValues<PositionType>();
                result.WorkflowTypes = EnumUtil.GetEnumIdValues<WorkflowTypeName>();
                result.PurPRApplicationStatus = EnumUtil.GetEnumIdValues<PurPRApplicationStatus>().Where(a => a.Key != (int)PurPRApplicationStatus.Draft);
                result.PurGRApplicationStatus = EnumUtil.GetEnumIdValues<PurGRApplicationStatus>();
                result.PurPAApplicationStatus = EnumUtil.GetEnumIdValues<PurPAApplicationStatus>();
                result.PurBDApplicationStatus = EnumUtil.GetEnumIdValues<PurBDStatus>();
                result.VendorApplicationTypes = EnumUtil.GetEnumIdValues<ApplicationTypes>();
                result.VendorApplicationStatuses = EnumUtil.GetEnumIdValues<Statuses>();
                result.VendorApplicationVerificationStatuses = EnumUtil.GetEnumIdValues<VerificationStatuses>();
                result.VendorBankTypes = EnumUtil.GetEnumIdValues<VendorBankTypes>();
                result.VendorBPCSStatus = EnumUtil.GetEnumIdValues<VendorBPCSStatus>();
                result.FinancialVendorStatus = EnumUtil.GetEnumIdValues<FinancialVendorStatus>();
                result.Genders = EnumUtil.GetEnumIdValues<Gender>();
                result.DeliveryMethods = EnumUtil.GetEnumIdValues<DeliveryMethod>();
                result.PaymentTypes = EnumUtil.GetEnumIdValues<PaymentTypes>().Select(a => { a.Type = a.Key != 1 ? "OLD" : ""; return a; });
                result.DeliveryModes = EnumUtil.GetEnumIdValues<DeliveryModes>();
                result.PayMethods = EnumUtil.GetEnumIdValues<PayMethods>().Where(a => a.Key != (int)PayMethods.ICBIn && a.Key != (int)PayMethods.FG);
                result.POType = EnumUtil.GetEnumIdValues<PurOrderType>();
                result.POStatus = EnumUtil.GetEnumIdValues<PurOrderStatus>();
                result.BudgetHistoryTypes = EnumUtil.GetEnumIdValues<OperateType>();
                result.TemplateTyps = EnumUtil.GetEnumIdValues<TemplateTypes>();
                result.ExpenseType = EnumUtil.GetEnumIdValues<PAExpenseType>();
                result.InterceptType = EnumUtil.GetEnumIdValues<InterceptType>();
                result.OrderStatusFlag = EnumUtil.GetEnumIdValues<OrderStatusFlag>();
                result.HcpLevelApplicableDepts = EnumUtil.GetEnumIdValues<ApplicableDepartments>();
                result.InterceptStatus = EnumUtil.GetEnumIdValues<InterceptStatus>();
                result.InterceptOperateType = EnumUtil.GetEnumIdValues<InterceptOperateType>();
                result.AdaptationResults = EnumUtil.GetEnumIdValues<AdaptationResults>();
                result.SlideOperationTypes = EnumUtil.GetEnumIdValues<SlideOperationType>();
                result.FormCategory = EnumUtil.GetEnumIdValues<ResignationTransfer.FormCategory>();
                result.TaskFormCategory = EnumUtil.GetEnumIdValues<ResignationTransfer.TaskFormCategory>();
                result.SpeakerAuthStatus = EnumUtil.GetEnumIdValues<SpeakerAuthStatus>();
                result.VendorPorperty = new List<DropdownListDto<bool, string>> { new DropdownListDto<bool, string>(true, "APS"), new DropdownListDto<bool, string>(false, "不适用") };
                result.BudgetStatus = new List<DropdownListDto<bool, string>> { new DropdownListDto<bool, string>(true, "启用"), new DropdownListDto<bool, string>(false, "禁用") };
                result.ActivityReleaseStatus = EnumUtil.GetEnumIdValues<ActivityReleaseStatus>();
                result.ActivityType = EnumUtil.GetEnumIdValues<ActivityType>();
                result.ActivityMode = EnumUtil.GetEnumIdValues<ActivityMode>();
                result.CustomerStatus = EnumUtil.GetEnumIdValues<CustomerStatus>();
                result.ApplicationDocumentType = EnumUtil.GetEnumIdValues<ApplicationDocumentType>();
                result.STicketStatus = EnumUtil.GetEnumIdValues<STicketStatus>();
                result.FOCStatus = EnumUtil.GetEnumIdValues<FOCStatus>();
                result.ReturnApplicationStatus = EnumUtil.GetEnumIdValues<ReturnApplicationStatus>();
                result.PaymentMethod = EnumUtil.GetEnumIdValues<PaymentMethod>();
                result.ReturnReason = EnumUtil.GetEnumIdValues<ReturnReason>();
                result.MsaOperateTypes = EnumUtil.GetEnumIdValues<MsaOperateTypes>();
            });

            #region 从其他类型转换

            result.CostNatureCodes = new List<KeyValuePair<string, string>>
            {
                new KeyValuePair<string, string>(CostNatureCode.C_14492, CostNatureCode.C_14492),
                new KeyValuePair<string, string>(CostNatureCode.C_14496, CostNatureCode.C_14496),
                new KeyValuePair<string, string>(CostNatureCode.C_14498, CostNatureCode.C_14498),
                new KeyValuePair<string, string>(CostNatureCode.C_15471, CostNatureCode.C_15471)
            };
            result.ProfessionalReportVendorType = new List<KeyValuePair<string, string>>
            {
                //NL、NLIV、NH、NHIV、NT
                new KeyValuePair<string, string>("NL", "NL"),
                new KeyValuePair<string, string>("NLIV", "NLIV"),
                new KeyValuePair<string, string>("NH", "NH"),
                new KeyValuePair<string, string>("NHIV", "NHIV"),
                new KeyValuePair<string, string>("NT", "NT"),
            };

            result.OnlineMeetingStatus = new List<KeyValuePair<string, string>>
            {
                new KeyValuePair<string, string>(OnlineMeetingStatus.NexBpmPushed,"已推送"),
                new KeyValuePair<string, string>(OnlineMeetingStatus.OmActivated,"已激活"),
                new KeyValuePair<string, string>(OnlineMeetingStatus.OmSettledPushed,"已结算"),
                new KeyValuePair<string, string>(OnlineMeetingStatus.NexBpmDeprecated,"NexBPM作废"),
                new KeyValuePair<string, string>(OnlineMeetingStatus.OmDeprecated,"会议系统作废")
            };

            #endregion

            //绿色：success，红色：danger，灰色：info，蓝色：primary，橙色：warning
            Task.WaitAll(setValueFromRedisTask, setValueFromDbTask, setValueFromDictTask, setValueFromEnumTask);

            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 获取所有医院信息
        /// </summary>
        /// <param name="keyword"></param>
        /// <param name="count"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<DropdownListDto<Guid, string>>))]
        public async Task<IActionResult> GetHospital(string keyword, int count = 200)
        {
            var result = await LazyServiceProvider.LazyGetService<IDataverseService>().GetAllHospitals(keyword, count);
            return Ok(MessageResult.SuccessResult(result.Select(a => new DropdownListDto<Guid, string>(a.Id, a.Name, a.Status.ToString()))));
        }

        /// <summary>
        /// 获取EPD业务系统医院主数据
        /// </summary>
        /// <param name="keyword"></param>
        /// <param name="count"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<DropdownListDto<string, string>>))]
        public async Task<IActionResult> GetEpdBuHospital(string keyword, int count = 200)
        {
            var pattern = string.IsNullOrEmpty(keyword) ? "*" : $"*\\\\*\\\\*{keyword}*\\\\*";
            var pageSize = string.IsNullOrEmpty(keyword) ? count : 50000;
            var result = await LazyServiceProvider.LazyGetService<IDataverseService>().GetEPDBUHospitals(pattern, pageSize, count);
            return Ok(MessageResult.SuccessResult(result.Select(a => new DropdownListDto<string, string>(a.HospitalCode, a.Name, a.VeevaID))));
        }

        /// <summary>
        /// 根据Epd业务系统医院获取对应的医院主数据
        /// </summary>
        /// <param name="name">EPD医院完整名称</param>
        /// <param name="vid">EPD医院VeevaId</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<DropdownListDto<Guid, string>>))]
        public async Task<IActionResult> GetHospitalByEpd(string name, string vid)
        {
            if (string.IsNullOrEmpty(name))
                return Ok(MessageResult.FailureResult(message: "参数错误"));

            var service = LazyServiceProvider.LazyGetService<IDataverseService>();

            var pattern = $"\\\\{name}\\\\*\\\\";//根据医院完整名称或veevaId去匹配医院主数据
            var datas = await service.GetAllHospitals(pattern, 100, stateCode: null);

            if (datas.Count == 0 && !string.IsNullOrEmpty(vid))
            {
                pattern = $"\\\\*\\\\*\\\\{vid}";
                datas = await service.GetAllHospitals(pattern, 1, stateCode: null);
            }

            var dto = datas.FirstOrDefault(x => x.Name == name);

            if (dto == null)
                return Ok(MessageResult.FailureResult(message: "Not found hospital"));

            return Ok(MessageResult.SuccessResult(new DropdownListDto<Guid, string>(dto.Id, dto.Name, dto.Status.ToString())));
        }


        /// <summary>
        /// 上传文件
        /// </summary>
        /// <param name="functionModule"><see cref="FunctionModule"/></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(IEnumerable<UploadFileResponseDto>))]
        public async Task<IActionResult> UploadFiles(FunctionModule functionModule)
        {
            if (!Enum.IsDefined(functionModule))
            {
                var functionModules = EnumUtil.GetEnumIdValues<FunctionModule>();
                return Ok(MessageResult.FailureResult($"模块类型不能为空，对应模块请参考data字段", functionModules.Select(a => $"{a.Value}：{a.Key}")));
            }

            var files = HttpContext.Request.Form.Files;
            if (!files.Any())
                return Ok(MessageResult.FailureResult("没有接收到附件信息"));

            var list = new List<UploadFileDto>();
            foreach (var item in files)
            {
                list.Add(new UploadFileDto { FileName = item.FileName, Size = item.Length, Buffer = item.GetAllBytes() });
            }

            var attachmentService = LazyServiceProvider.LazyGetService<IAttachmentService>();
            var result = await attachmentService.UploadFiles(functionModule, list);

            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 下载文件
        /// </summary>
        /// <param name="blobName"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> Download(string blobName, string fileName)
        {
            if (string.IsNullOrWhiteSpace(blobName))
                return Ok(MessageResult.FailureResult());

            var attachmentService = LazyServiceProvider.LazyGetService<IAttachmentService>();
            var result = await attachmentService.Download(blobName);

            return File(result.Value.Content, result.Value.Details.ContentType, fileName ?? Path.GetFileName(blobName));
        }

        /// <summary>
        /// 通用下载Excel模板
        /// </summary>
        /// <param name="TemplateType"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> DownloadTemplates([FromQuery] TemplateTypes TemplateType)
        {
            var fieldInfo = TemplateType.GetType().GetField(TemplateType.ToString());
            var templateName = fieldInfo.GetCustomAttribute<CategoryAttribute>().Category;
            var attachmentService = LazyServiceProvider.LazyGetService<IAttachmentService>();
            var result = await attachmentService.GetTemplatesFileAsync(TemplateType, templateName);
            if (result == null)
            {
                return Ok(MessageResult.FailureResult("未找到文件模版,请联系管理员"));
            }
            string contentType = "application/octet-stream";
            string fileName = templateName;
            return File(result, contentType, fileName);
        }

        /// <summary>
        /// 发送短信，可一次发多个手机号
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        [AllowAnonymous]
        public async Task<IActionResult> SendSMS([FromBody] SendSMSRequestDto request)
        {
            if (string.IsNullOrWhiteSpace(request.Mobiles))
            {
                return Ok(MessageResult.FailureResult("手机号必填"));
            }
            string[] Mobiles = request.Mobiles.Split(',');
            foreach (var item in Mobiles)
            {
                if (!IsValidPhoneNumber(item))
                {
                    return Ok(MessageResult.FailureResult($"手机号{item}格式不正确，请查证"));
                }
            }
            if (string.IsNullOrWhiteSpace(request.Content) && request.ContentType == null)
            {
                return Ok(MessageResult.FailureResult("内容或模板编号必须填"));
            }

            //#region 手机号选择登录的情况 or 修改手机号的情况

            //if (request.VendorId.HasValue)
            //{
            //    var vendorDatas = await LazyServiceProvider.LazyGetService<ISpeakerService>().GetVendorApplicationOrOfficialInfoAsync(request.VendorId.Value);
            //    //旧手机号不为空则验证旧手机号与vendor的关系是否存在
            //    if (!string.IsNullOrEmpty(request.OldPhone))
            //    {
            //        if (!vendorDatas.Any(a => a.Phone == request.OldPhone))
            //            return Ok(MessageResult.FailureResult("旧手机号验证失败"));
            //    }
            //    else//验证手机号在档案中是否存在
            //    {
            //        if (!vendorDatas.Any(a => a.ReservedMobilePhone.Contains(request.Mobiles)))
            //            return Ok(MessageResult.FailureResult("手机号验证失败"));
            //    }
            //}
            //else//去验证当前用户中是否有该手机号的用户存在
            //{
            //    var userData = await LazyServiceProvider.LazyGetService<IUserService>().GetUserByPhoneAsync(request.Mobiles);
            //    if (userData == null)
            //        return Ok(MessageResult.FailureResult("手机号码不存在"));
            //}

            //#endregion

            var redis = LazyServiceProvider.LazyGetService<IRedisRepository>();
            var redisKey = $"{RedisKey.RecentFlag}{request.Mobiles}";
            if (redis.Database.KeyExists(redisKey))
                return Ok(MessageResult.FailureResult("重新发送请1分钟之后再试"));

            var res = await _smsService.SendSMS(request);

            //成功发送短信后，记录flag，防止重复发送
            if (res.Success)
                redis.Database.StringSet(redisKey, string.Empty, TimeSpan.FromMinutes(1));

            return Ok(res);
        }

        /// <summary>
        /// 验证码验证
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        public async Task<IActionResult> VerifySMS([FromBody] VerifySMSRequestDto request)
        {
            if (string.IsNullOrWhiteSpace(request.Mobile))
            {
                return Ok(MessageResult.FailureResult("手机号必填"));
            }
            if (!IsValidPhoneNumber(request.Mobile))
            {
                return Ok(MessageResult.FailureResult("手机号格式不正确"));
            }
            if (request.VerifyCode.ToString().Length != 6)
            {
                return Ok(MessageResult.FailureResult("请填写6位验证码"));
            }
            var result = await _smsService.VerifySMS(request);
            return Ok(result);
        }

        /// <summary>
        /// 旧手机号码验证
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [AllowAnonymous]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        public async Task<IActionResult> VerifyOldPhone([FromQuery] string vendorCode, string oldPhone)
        {
            if (string.IsNullOrWhiteSpace(oldPhone) || string.IsNullOrWhiteSpace(vendorCode))
            {
                return Ok(MessageResult.FailureResult("参数必填"));
            }
            var result = await _smsService.VerifyOldPhone(vendorCode, oldPhone);
            return Ok(result);
        }

        private static bool IsValidPhoneNumber(string mobile)
        {
            string pattern = @"^1[0-9]\d{9}$";
            Regex regex = new Regex(pattern);
            return regex.IsMatch(mobile);
        }


        /// <summary>
        /// 获取省市
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<List<ProvinceCityDto>>))]
        public async Task<IActionResult> GetProvinceBaseInfo()
        {
            var result = await _commonService.GetProvinceCity();
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 接收PowerApp同步的数据到Redis缓存
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        public async Task<IActionResult> ReceivePowerData([FromBody] ReceivePowerDataDto request)
        {
            var result = await _powerappService.ReceivePowerData(request);
            return Ok(result);
        }

        /// <summary>
        /// 接收PowerApp同步的当前审批任务
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        public async Task<IActionResult> ReceiveApprovalTask([FromBody] CurrentApprovalTaskDto request)
        {
            var result = await _powerappService.ReceiveApprovalTask(request);
            return Ok(result);
        }

        /// <summary>
        /// 发送邮件
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        public async Task<IActionResult> SendEmailAsync([FromBody] SendEmailRequestDto request)
        {
            var result = await _powerappService.SendEmailAsync(request);
            return Ok(result);
        }

        /// <summary>
        /// 获取审批记录
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<List<ApprovalRecordDto>>))]
        public async Task<IActionResult> GeApprovalRecord([FromQuery] Guid request)
        {
            var result = await _approveService.GetApprovalRecordAsync(request);
            return Ok(result);
        }

        /// <summary>
        /// 获取审批所需人员列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<List<ApprovalPersonListResponseDto>>))]
        public async Task<IActionResult> GetApprovalPersonList([FromQuery] ApprovalPersonListRequestDto request)
        {
            var result = await _approveService.GetApprovalPersonListAsync(request);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 获取审批所需人员列表(根据部门)
        /// </summary>
        /// <param name="bu"></param>
        /// <param name="positionType"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<IEnumerable<KeyValuePair<Guid, string>>>))]
        public async Task<IActionResult> GetStaffListByPositionAndFlow([FromQuery] Guid bu, PositionType positionType)
        {
            var result = await _dataverseService.GetStaffListByPositionAndFlow(bu, positionType);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 审批结果接收
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult<MessageResult>))]
        public async Task<IActionResult> ApprovalResults([FromBody] ApprovalResultsRequestDto request)
        {
            BackgroundJob.Enqueue<ApprovalSyncWorker>(a => a.ExecuteAsync(request));
            return Ok(MessageResult.SuccessResult());
        }

        /// <summary>
        /// 我发起的数量
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<MessageResult>))]
        public async Task<IActionResult> GetInitiatedApprovalCount()
        {
            var result = await _approveService.InitiatedApprovalCountAsync();
            return Ok(result);
        }

        /// <summary>
        /// 我发起的数量(ABP)
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<MessageResult>))]
        public async Task<IActionResult> InitiatedApprovalCountABP()
        {
            var result = await _approveService.InitiatedApprovalCountABPAsync();
            return Ok(result);
        }

        /// <summary>
        /// 我审批的数量
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<MessageResult>))]
        public async Task<IActionResult> GetApprovalCount()
        {
            var result = await _approveService.GetApprovalCountAsync();
            return Ok(result);
        }

        /// <summary>
        /// 我审批的数量(ABP)
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<MessageResult>))]
        public async Task<IActionResult> GetApprovalCountABP()
        {
            var result = await _approveService.GetApprovalCountABPAsync();
            return Ok(result);
        }

        /// <summary>
        /// 当前用户待处理的任务(我发起的和我审批的)数量
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<MessageResult>))]
        public async Task<IActionResult> GetPendingCount()
        {
            var result = await _approveService.GetPendingCountAsync();
            return Ok(result);
        }

        /// <summary>
        /// 审批操作（同意、拒绝、退回）
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [Authorize(SpeakerPortalPermissions.TaskCenter.ApproveAction)]
        [ProducesDefaultResponseType(typeof(MessageResult<List<UpdateApprovalResponseDto>>))]
        public async Task<IActionResult> ApprovalOperation([FromBody] List<UpdateApprovalDto> request)
        {
            var result = await _approveService.ApprovalOperationAsync(request);
            return Ok(result);
        }

        /// <summary>
        /// 提交人操作（撤回，作废）
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [Authorize(SpeakerPortalPermissions.TaskCenter.LaunchAction)]
        [ProducesDefaultResponseType(typeof(MessageResult<List<UpdateApprovalResponseDto>>))]
        public async Task<IActionResult> SubmitterOperation([FromBody] List<UpdateApprovalDto> request)
        {
            //验证是否有权利执行该操作
            foreach (var item in request)
            {
                if (!Guid.TryParse(item.BusinessFormId, out Guid businessFormId))
                    return Ok(MessageResult.FailureResult("你无权执行该操作"));

                var valid = await LazyServiceProvider.LazyGetService<ICommonService>().ValidateApplicantRelatedOperationAsync(businessFormId);
                if (!valid)
                    return Ok(MessageResult.FailureResult("你无权执行该操作"));
            }

            var result = await _approveService.SubmitterOperationAsync(request);
            return Ok(result);
        }

        /// <summary>
        /// 添加医院信息
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult<HospitalDto>))]
        public async Task<IActionResult> AddHospital([FromBody] AddHospitalRequestDto request)
        {
            //验证数据
            if (!ModelState.IsValid)
            {
                var errorFields = ModelState.Where(x => x.Value.Errors.Any()).Select(x => new { Field = x.Key, Errors = x.Value.Errors.Select(e => e.ErrorMessage).ToList() }).ToList();
                return Ok(MessageResult.FailureResult($"请填写必填项:{string.Join(",", errorFields.Select(s => s.Field))}"));
            }
            if (request.HospitalProvinceCity.Length != 2)
                return Ok(MessageResult.FailureResult("添加正确省市"));

            var result = await _dataverseService.AddHospitalAsync(request);
            if (result == null) return Ok(MessageResult.FailureResult("医院已存在"));
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 获取组织中的部门(BU-Dept)
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<IEnumerable<KeyValuePair<Guid, string>>>))]
        public async Task<IActionResult> GetOrganizationDept(Guid? userId)
        {
            if (!userId.HasValue)
                userId = CurrentUser.Id;
            if (!userId.HasValue)
                return Ok(MessageResult.FailureResult("当前用户ID为空"));
            var commonService = LazyServiceProvider.LazyGetService<ICommonService>();
            var result = await commonService.GetOrganizationDept((Guid)userId);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 获取组织信息，模糊查询
        /// </summary>
        /// <param name="keyword">The keyword.</param>
        /// <param name="count">The count.</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<IEnumerable<DepartmentDto>>))]
        public async Task<IActionResult> GetOrgsAsync(string keyword, int count = 200)
        {
            var commonService = LazyServiceProvider.LazyGetService<ICommonService>();
            var result = await commonService.GetOrgsAsync(keyword, count);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 获取组织中的部门(BU-Dept)，包含Bu
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<IEnumerable<DepartmentWithBuDto>>))]
        public async Task<IActionResult> GetOrganizationDeptWithBU(Guid? userId)
        {
            if (!userId.HasValue)
                userId = CurrentUser.Id;
            if (!userId.HasValue)
                return Ok(MessageResult.FailureResult("当前用户ID为空"));
            var commonService = LazyServiceProvider.LazyGetService<ICommonService>();
            var result = await commonService.GetOrganizationDeptWithBU((Guid)userId);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 获取城市主数据spk_citymasterdata
        /// </summary>
        /// <param name="orgId">组织Id</param>
        /// <param name="companyId">公司Id</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<IEnumerable<KeyValueDto>>))]
        public async Task<IActionResult> GetSpecialCitiesAsync(Guid orgId, Guid companyId)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            //获取BU
            var orgs = await LazyServiceProvider.LazyGetService<ICommonService>().GetAllParentOrgs(orgId);
            var bu = orgs.FirstOrDefault(a => a.OrganizationType == DataverseEnums.Organization.OrganizationType.Bu);
            if (bu == null)
                return default;

            IEnumerable<Guid> cityIds;
            //获取机构与城市关系数据
            var allOrgCityRelations = await dataverseService.GetOrgSpecialCityRelationAsync();
            var orgCityRelations = allOrgCityRelations.Where(a => a.CityId.HasValue && a.OrgId.HasValue && a.OrgId == bu.Id);
            var restrictedCities = orgCityRelations.Where(a => a.IsRestricted == true);
            //单据所属BU有限定的城市，则返回限定的城市(无论该城市是否关联至了PR申请的公司)
            if (restrictedCities.Any())
                cityIds = restrictedCities.Select(a => a.CityId.Value).ToArray();
            else
            {
                //获取公司与城市关系数据
                var cityCompanyRelations = await dataverseService.GetCityCompanyRelationsAsync(companyId.ToString());
                cityCompanyRelations = cityCompanyRelations.Where(x => x.CityId.HasValue && x.CompanyId.HasValue);
                //如果有配置机构与城市关系，则取与公司城市关系交集的数据
                if (orgCityRelations.Any())
                    cityIds = cityCompanyRelations.Join(orgCityRelations, a => a.CityId, a => a.CityId, (a, b) => b.CityId.Value);
                else//如果没有配置机构与城市关系，则直接取与公司关联的城市数据
                    cityIds = cityCompanyRelations.Where(a => a.CityId.HasValue).Select(a => a.CityId.Value);

                //单据所属BU没有限定的城市，则需要额外排除标记"是否限定BU城市"为"Yes"的城市
                var allRestrictedCities = allOrgCityRelations.Where(a => a.IsRestricted == true).Select(a => a.CityId.Value);
                cityIds = cityIds.Except(allRestrictedCities).ToArray();
            }

            IEnumerable<KeyValueDto> cities = [];
            if (cityIds.Any())
            {
                var specialCities = await dataverseService.GetSpecialCitiesAsync();
                cities = specialCities.Join(cityIds, a => a.Id, a => a, (a, b) => new KeyValueDto { Key = a.Id, Value = a.CityNameCode, Code = a.CityCode });
            }

            return Ok(MessageResult.SuccessResult(cities));
        }

        /// <summary>
        /// 获取所有公司及相关数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<List<CompanyDto>>))]
        public async Task<IActionResult> GetCompanyList([FromQuery] string companyId)
        {
            var result = await _dataverseService.GetCompanyList(companyId);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 获取内部可用用户列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<PagedResultDto<GetActiveUserListResponseDto>>))]
        public async Task<IActionResult> GetInternalActiveUsersAsync([FromQuery] GetActiveUserListRequestDto request)
        {
            var data = await _commonService.GetInternalActiveUsersAsync(request);
            return Ok(MessageResult.SuccessResult(data));
        }

        /// <summary>
        /// 获取所有内部可用用户列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<PagedResultDto<GetActiveUserListResponseDto>>))]
        public async Task<IActionResult> GetAllInternalActiveUsersAsync([FromQuery] GetActiveUserListRequestDto request)
        {
            var data = await _commonService.GetAllInternalActiveUsersAsync(request);
            return Ok(MessageResult.SuccessResult(data));
        }

        /// <summary>
        /// 导出所有内部可用用户列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> ExportAllInternalActiveUsersAsync([FromQuery] GetActiveUserListRequestDto request)
        {
            var data = await _commonService.ExportAllInternalActiveUsersAsync(request);
            return File(data, "application/octet-stream", $"用户列表-{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}.xlsx");
        }

        /// <summary>
        /// 获取员工数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<GetUserDropdownListResponseDto>))]
        public async Task<IActionResult> GetUserDropDownLsit()
        {
            var data = await _commonService.GetUserDropDownLsitAsync();
            return Ok(MessageResult.SuccessResult(data));
        }

        /// <summary>
        /// 识别身份证信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(CognitiveIdCardResponseDto))]
        public async Task<IActionResult> CognizeIdCard(string blobName)
        {
            if (string.IsNullOrWhiteSpace(blobName))
                return Ok(MessageResult.FailureResult("未找到要识别的图片"));

            var result = await _commonService.CognizeIdCard(blobName);
            return Ok(result);
        }

        /// <summary>
        /// 识别银行卡图片获取卡号
        /// </summary>
        /// <param name="blobName"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(string))]
        public async Task<IActionResult> CognizeBankCard(string blobName)
        {
            if (string.IsNullOrWhiteSpace(blobName))
                return Ok(MessageResult.FailureResult("未找到要识别的图片"));

            var stream = await LazyServiceProvider.LazyGetService<IBlobService>().DownloadStream(blobName);
            if (stream == null)
                return Ok(MessageResult.FailureResult("未找到要识别的图片"));

            var cognitiveService = LazyServiceProvider.LazyGetService<ICognitiveService>();
            var cognizeResult = await cognitiveService.GetCognizeResultAsync(stream.GetBuffer());

            var line = cognizeResult.Select(a => a.Replace(" ", "")).JoinAsString("");
            var match = Regex.Match(line, "\\d{16,19}");

            return Ok(MessageResult.SuccessResult(match?.Value));
        }

        /// <summary>
        /// 获取消费大类
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<IEnumerable<KeyValuePair<Guid, string>>>))]
        public async Task<IActionResult> GetConsumeCategoryAsync()
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var consumeCategories = await dataverseService.GetConsumeCategoryAsync();
            var datas = consumeCategories
                .Where(a => !a.FlowType.HasValue || a.FlowType == DataverseEnums.ConsumeCategory.FlowTypes.PR)
                .Select(a => new KeyValuePair<Guid, string>(a.Id, a.Name)).ToArray();

            return Ok(MessageResult.SuccessResult(datas));
        }

        /// <summary>
        /// 获取消费大类
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<IEnumerable<KeyValuePair<Guid, string>>>))]
        public async Task<IActionResult> GetConsumeCategoriesAsync(string keyword)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var consumeCategories = await dataverseService.GetConsumeCategoryAsync(null, null);
            var datas = consumeCategories
                .Where(a => !a.FlowType.HasValue || a.FlowType == DataverseEnums.ConsumeCategory.FlowTypes.PR)
                .WhereIf(!string.IsNullOrEmpty(keyword), x => x.Name.Contains(keyword))
                .Select(x => new KeyValuePair<Guid, string>(x.Id, x.Name)).ToArray();

            return Ok(MessageResult.SuccessResult(datas));
        }

        /// <summary>
        /// 根据消费大类Id获取费用性质
        /// </summary>
        /// <param name="consumeCategoryId"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<IEnumerable<KeyValuePair<Guid, string>>>))]
        public async Task<IActionResult> GetCostNatureAsync(Guid consumeCategoryId)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var costNatures = await dataverseService.GetCostNatureAsync(consumeCategoryId.ToString());
            var datas = costNatures.Select(a => new KeyValuePair<Guid, string>(a.Id, a.Name)).ToArray();

            return Ok(MessageResult.SuccessResult(datas));
        }

        /// <summary>
        /// 获取所有部门
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(List<DepartmentDto>))]
        public async Task<IActionResult> GetDepartmentList(string departmentId = "")
        {
            var data = await _userService.GetDepartmentList(departmentId);
            return Ok(MessageResult.SuccessResult(data));
        }
        /// <summary>
        /// 获取所有费用性质
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<IEnumerable<DropDownListDto>>))]
        public async Task<IActionResult> GetAllCostNatures()
        {
            var data = await _commonService.GetAllCostNatureAsync();
            return Ok(data);
        }
        /// <summary>
        /// 获取城市主数据spk_citymasterdata
        /// </summary>
        /// <param name="companyId">公司Id</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<IEnumerable<KeyValueDto>>))]
        public async Task<IActionResult> GetSpecialCitiesByCompanyId(Guid companyId)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            //获取公司与城市关系数据
            var cityCompanyRelations = await dataverseService.GetCityCompanyRelationsAsync(companyId.ToString());
            IEnumerable<Guid> cityIds;
            cityIds = cityCompanyRelations.Where(a => a.CityId.HasValue).Select(a => a.CityId.Value).ToArray();

            var list = new List<KeyValueDto>();
            foreach (var item in cityIds)
            {
                var specialCities = await _dataverseService.GetSpecialCitiesAsync(item.ToString());
                foreach (var city in specialCities)
                {
                    list.Add(new KeyValueDto { Key = city.Id, Value = city.CityNameCode, Code = city.CityCode });
                }
            }

            return Ok(MessageResult.SuccessResult(list));
        }


        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<IEnumerable<CompanyDto>>))]
        public async Task<IActionResult> GetComByOrgAsync(Guid orgId)
        {
            var data = await _commonService.GetComByOrgAsync(orgId);
            return Ok(data);
        }

        [HttpGet]
        public async Task<IActionResult> IsSubOrgOfJXFactory(Guid orgId)
        {
            var flag = await _commonService.IsSubOrganization(theOrg: orgId, targetOrgName: AffiliateNameConst.JXFactory);
            return Ok(MessageResult.SuccessResult(data: new { isJXFactory = flag }));
        }
    }
}
