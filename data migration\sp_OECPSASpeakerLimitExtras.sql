CREATE proc sp_OECPSASpeakerLimitExtras
as
begin
	IF OBJECT_ID(N'PLATFORM_ABBOTT_Stg.dbo.OECPSASpeakerLimitExtras_tmp', N'U') IS NOT NULL
BEGIN
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
    drop table OECPSASpeakerLimitExtras_tmp;
    select
	a.HCPIDNumber OriginalHCPIDNumber,
	TRIM(REPLACE(REPLACE(a.HCPIDNumber,'F',''),'M','')) HCPIDNumber,
	vendor.VendorId,
	'' [ComPSALimitId],
	1 [ModifyType],
	a.BUCode [BpmDivisionId],
	org.spk_NexBPMCode [DivisionId],
	a.Amount [ExtralAmountRest],
	a.Count [ExtralTimesRest],
	'BPMHistoryRecord' [ExtralAuditApplicationNo],
	a.Year [Year],
	N'BPM历史例外次数/金额迁移' Remark,
	'' Doc,
	Cast(YEAR(getdate()) as varchar)+'-01-01' [CreationTime],
	'' [CreatorId],
	'' [LastModificationTime],
	'' [LastModifierId],
	0 [IsDeleted],
	'' [DeleterId],
	'' [DeletionTime]
	into OECPSASpeakerLimitExtras_tmp
	from ODS_T_HCPCountExceedConfig a
	left join VendorPersonals vendor on TRIM(REPLACE(REPLACE(a.HCPIDNumber,'F',''),'M',''))=vendor.CardNo
	left join spk_organizationalmasterData org on a.BUCode=org.spk_BPMCode;
	END
	ELSE
	BEGIN
    --落成实体表
    select
	a.HCPIDNumber OriginalHCPIDNumber,
	TRIM(REPLACE(REPLACE(a.HCPIDNumber,'F',''),'M','')) HCPIDNumber,
	vendor.VendorId,
	'' [ComPSALimitId],
	1 [ModifyType],
	a.BUCode [BpmDivisionId],
	org.spk_NexBPMCode [DivisionId],
	a.Amount [ExtralAmountRest],
	a.Count [ExtralTimesRest],
	'BPMHistoryRecord' [ExtralAuditApplicationNo],
	a.Year [Year],
	N'BPM历史例外次数/金额迁移' Remark,
	'' Doc,
	Cast(YEAR(getdate()) as varchar)+'-01-01' [CreationTime],
	'' [CreatorId],
	'' [LastModificationTime],
	'' [LastModifierId],
	0 [IsDeleted],
	'' [DeleterId],
	'' [DeletionTime]
	into OECPSASpeakerLimitExtras_tmp
	from ODS_T_HCPCountExceedConfig a
	left join VendorPersonals vendor on TRIM(REPLACE(REPLACE(a.HCPIDNumber,'F',''),'M',''))=vendor.CardNo
	left join spk_organizationalmasterData org on a.BUCode=org.spk_BPMCode;
	  PRINT(N' 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
END
