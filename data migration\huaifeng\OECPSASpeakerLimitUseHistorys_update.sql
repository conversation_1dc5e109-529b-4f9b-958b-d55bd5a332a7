select 
TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL(a.[ID],'00000000-0000-0000-0000-000000000000'))[Id]
,TRY_CONVERT(UNIQUEIDENTIFIER, iif([VendorId] is null or [VendorId] = '','00000000-0000-0000-0000-000000000000',a.[VendorId])) as[VendorId]
,TRY_CONVERT(UNIQUEIDENTIFIER, iif([ComPSALimitId] is null or [ComPSALimitId] = '','00000000-0000-0000-0000-000000000000',a.[ComPSALimitId]))[ComPSALimitId]
,a.[OperateType]
,a.[OperDetailType]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL(a.[PrApplicationId],'00000000-0000-0000-0000-000000000000'))[PrApplicationId]
,TRY_CONVERT(UNIQUEIDENTIFIER, iif([BuId] is null or [BuId] = '','00000000-0000-0000-0000-000000000000',a.[BuId]))[BuId]
,coalesce(a.[Times],0) as [Times]
,coalesce(a.[Amount],0) as [Amount]
,a.[Doc]
,a.[ExtraProperties]
,a.[ConcurrencyStamp]
,a.[CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL(a.[CreatorId],'00000000-0000-0000-0000-000000000000'))[CreatorId]
,a.[LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, iif([LastModifierId] is null or [LastModifierId] = '','00000000-0000-0000-0000-000000000000',a.[LastModifierId]))[LastModifierId]
,a.[IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL(a.[DeleterId],'00000000-0000-0000-0000-000000000000'))[DeleterId]
,a.[DeletionTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL(a.[PsaExternalId],'00000000-0000-0000-0000-000000000000'))[PsaExternalId]
,a.[EffectiveDate]
into #OECPSASpeakerLimitUseHistorys
from PLATFORM_ABBOTT_STG.dbo.OECPSASpeakerLimitUseHistorys a;

--drop table #OECPSASpeakerLimitUseHistorys;

USE Speaker_Portal_STG;

UPDATE a
SET
a.[VendorId]            = b.[VendorId]
,a.[ComPSALimitId]       = b.[ComPSALimitId]
,a.[OperateType]         = b.[OperateType]
,a.[OperDetailType]      = b.[OperDetailType]
,a.[PrApplicationId]     = b.[PrApplicationId]
,a.[BuId]                = b.[BuId]
,a.[Times]               = b.[Times]
,a.[Amount]              = b.[Amount]
,a.[Doc]                 = b.[Doc]
,a.[ExtraProperties]     = b.[ExtraProperties]
,a.[ConcurrencyStamp]    = b.[ConcurrencyStamp]
,a.[CreationTime]        = b.[CreationTime]
,a.[CreatorId]           = b.[CreatorId]
,a.[LastModificationTime]= b.[LastModificationTime]
,a.[LastModifierId]      = b.[LastModifierId]
,a.[IsDeleted]           = b.[IsDeleted]
,a.[DeleterId]           = b.[DeleterId]
,a.[DeletionTime]        = b.[DeletionTime]
,a.[PsaExternalId]       = b.[PsaExternalId]
,a.[EffectiveDate]       = b.[EffectiveDate]
FROM dbo.OECPSASpeakerLimitUseHistorys as  a
left join #OECPSASpeakerLimitUseHistorys  b
ON a.id=b.id;


INSERT INTO dbo.OECPSASpeakerLimitUseHistorys
SELECT
[Id]                  
,[VendorId]            
,[ComPSALimitId]       
,[OperateType]         
,[OperDetailType]      
,[PrApplicationId]     
,[BuId]                
,[Times]               
,[Amount]              
,[Doc]                 
,[ExtraProperties]     
,[ConcurrencyStamp]    
,[CreationTime]        
,[CreatorId]           
,[LastModificationTime]
,[LastModifierId]      
,[IsDeleted]           
,[DeleterId]           
,[DeletionTime]        
,[PsaExternalId]       
,[EffectiveDate]     
FROM #OECPSASpeakerLimitUseHistorys a                                           
WHERE not exists (select * from dbo.OECPSASpeakerLimitUseHistorys where id= a.id);

        
--truncate table dbo.OECPSASpeakerLimitUseHistorys
