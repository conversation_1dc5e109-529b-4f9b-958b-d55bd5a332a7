select 
newid() as spk_NexBPMCode
,a.spk_BPMCode
,a.spk_Name
,spk_chineseName
,a.spk_englishName
,a.spk_organizationType
,a.spk_epolevel
,b.spk_NexBPMCode  as spk_epoleader
,a.spk_parentorganization
,c.spk_NexBPMCode as spk_costcenter
,[spk_orgCode]
,[spk_orgParentCode]
,a.flg
into #a1
 from spk_organizationalmasterData_Tmp a
 left join spk_staffmasterdata b on b.bpm_id=a.spk_epoleader
 left join spk_costcentermasterdata c on c.spk_BPMCode=a.spk_costcenter
 select a.spk_NexBPMCode,a.spk_BPMCode,a.spk_Name,a.spk_chineseName,a.spk_englishName,a.spk_organizationType
,a.spk_epolevel,a.spk_epoleader,a3.spk_NexBPMCode as spk_parentorganization,a.spk_costcenter,a.[spk_orgCode]
, a.[spk_orgParentCode] ,a.flg
into #spk_organizationalmasterData
from #a1  a
left join  #a1  as a3
on a.spk_parentorganization=a3.spk_BPMCode 


IF OBJECT_ID(N'dbo.spk_organizationalmasterData', N'U') IS NOT NULL
BEGIN
	update a 
	set a.spk_NexBPMCode         = b.spk_NexBPMCode
       ,a.spk_BPMCode            = b.spk_BPMCode
       ,a.spk_Name               = b.spk_Name
       ,a.spk_chineseName        = b.spk_chineseName
       ,a.spk_englishName        = b.spk_englishName
       ,a.spk_organizationType   = b.spk_organizationType
       ,a.spk_epolevel           = b.spk_epolevel
       ,a.spk_epoleader          = b.spk_epoleader
       ,a.spk_parentorganization = b.spk_parentorganization
       ,a.spk_costcenter         = b.spk_costcenter
       ,a.spk_orgCode            = b.spk_orgCode
       ,a.spk_orgParentCode      = b.spk_orgParentCode
       ,a.flg                    = b.flg
    from dbo.spk_organizationalmasterData a
    left join #spk_organizationalmasterData b on a.spk_BPMCode = b.spk_BPMCode
    
    insert into dbo.spk_organizationalmasterData
    select a.spk_NexBPMCode
          ,a.spk_BPMCode
          ,a.spk_Name
          ,a.spk_chineseName
          ,a.spk_englishName
          ,a.spk_organizationType
          ,a.spk_epolevel
          ,a.spk_epoleader
          ,a.spk_parentorganization
          ,a.spk_costcenter
          ,a.spk_orgCode
          ,a.spk_orgParentCode
          ,a.flg
	from #spk_organizationalmasterData a
	where NOT EXISTS (SELECT * FROM dbo.spk_organizationalmasterData where spk_BPMCode = a.spk_BPMCode)
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into dbo.spk_organizationalmasterData from #spk_organizationalmasterData
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END