﻿using Abbott.SpeakerPortal.Enums;

using System;
using System.Collections.Generic;
using System.Text;

namespace Abbott.SpeakerPortal.Contracts.Agent
{
    public class GetAgentConfigListResponseDto
    {
        public Guid Id { get; set; }
        //public WorkflowTypeName? WorkflowType { get; set; }
        public ResignationTransfer.TaskFormCategory? BusinessType { get; set; }
        /// <summary>
        /// 业务类型Id，可空，空则表示“全部”
        /// </summary>
        public Guid? BusinessTypeId { get; set; }
        /// <summary>
        /// 业务类型名称
        /// </summary>
        public string BusinessTypeName { get; set; }
        public string WorkflowTypeName { get; set; }
        public Guid OriginalApprover { get; set; }
        public string OriginalApproverName { get; set; }
        public Guid Agent { get; set; }
        public string AgentName { get; set; }
        public bool IsNotifyOriginalApprover { get; set; }
        public string StartDate { get; set; }
        public string EndDate { get; set; }
        public string CreateByName { get; set; }
        public string ModifiedOn { get; set; }
        public string Remark { get; set; }
        public bool Status { get; set; }
    }
}
