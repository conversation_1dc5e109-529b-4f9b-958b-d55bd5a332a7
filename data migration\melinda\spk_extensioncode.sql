--岗位mapping
select 
a.[spk_Name],
b.spk_NexBPMCode  as [spk_staffname],
[spk_staffBPMCode],
c.spk_NexBPMCode as [spk_organization],
[spk_orgBPMCode],
[spk_position],
[spk_extentioncode2],
[spk_extensioncodeId]
into #spk_extensioncode
from (
	select 
		a.[spk_Name],
		a.[spk_staffname],
		a.[spk_staffBPMCode],
		case when spk_position=N'MKT director审批岗' or spk_position=N'Sales director审批岗' then '917fa20d3bac4e47bb47790db4aa9a30'
		when  spk_position in (N'STP Header审批岗',N'STP Manager审批岗',N'特殊采购审批岗') then d.spk_BPMCode
		else spk_organization
		end as [spk_organization],
		case when spk_position=N'MKT director审批岗' or spk_position=N'Sales director审批岗' then '917fa20d3bac4e47bb47790db4aa9a30'
		when  spk_position in (N'STP Header审批岗',N'STP Manager审批岗',N'特殊采购审批岗') then d.spk_BPMCode
		else spk_orgBPMCode
		end as [spk_orgBPMCode],
		a.[spk_position],
		a.[spk_extentioncode2],
		a.[spk_extensioncodeId]
	from spk_extensioncode_tmp a
	left join spk_organizationalmasterdata d
	on 'BU'=d.spk_organizationType and spk_position in (N'STP Header审批岗',N'STP Manager审批岗',N'特殊采购审批岗') or a.spk_organization=d.spk_BPMCode
) a
left join spk_staffmasterdata b
on a.spk_staffname=b.bpm_id
left join spk_organizationalmasterdata c
on a.spk_organization=c.spk_BPMCode
where spk_position is not null  and spk_organization is not null 








IF OBJECT_ID(N'dbo.spk_extensioncode', N'U') IS NOT NULL
BEGIN
	update a 
	set a.spk_Name           = b.spk_Name
       ,a.spk_staffname      = b.spk_staffname
       ,a.spk_staffBPMCode   = b.spk_staffBPMCode
       ,a.spk_organization   = b.spk_organization
       ,a.spk_orgBPMCode     = b.spk_orgBPMCode
       ,a.spk_position       = b.spk_position
       ,a.spk_extentioncode2 = b.spk_extentioncode2
       ,a.spk_extensioncodeId= b.spk_extensioncodeId
    from dbo.spk_extensioncode a
    left join #spk_extensioncode b on a.spk_Name = b.spk_Name
    
    insert into dbo.spk_extensioncode
    select a.spk_Name
          ,a.spk_staffname
          ,a.spk_staffBPMCode
          ,a.spk_organization
          ,a.spk_orgBPMCode
          ,a.spk_position
          ,a.spk_extentioncode2
          ,a.spk_extensioncodeId
	from #spk_extensioncode a
	where NOT EXISTS (SELECT * FROM dbo.spk_extensioncode where spk_Name = a.spk_Name)
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into dbo.spk_extensioncode from #spk_extensioncode
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END

