---------------员工主数据---------------------------
select newid() as spk_NexBPMCode,* into spk_staffmasterdata from(
select * from spk_staffmasterdata_Tmp)A

----------------配置数据-产品主数据--------------------------
select newid()  as spk_NexBPMCode,spk_BPMCode,spk_name,spk_chinesevalue,spk_englishvalue,spk_productcode,spk_limitexpensecode
into spk_productmasterdata from(
select * from spk_productmasterdata_Tmp)A

----------------配置数据-角色关系----------------------------
select newid() as id,*
into AbpUserRoles
from AbpUserRoles_tmp

------------配置数据-BU成本中心费用性质Mapping------------------------------
select  newid()  as spk_NexBPMCode,spk_bucode,spk_costcentercode,spk_expensenaturecode
into spk_coamappingrule
 from  spk_coamappingrule_Tmp
 
 -----------配置数据-字典-------------------------------
select newid()  as spk_NexBPMCode,spk_BPMCode,spk_code,spk_Name,spk_type,spk_parentcode
into spk_dictionary
from spk_dictionary_Tmp 

------------配置数据-大区主数据------------------------------
select  newid()  as spk_NexBPMCode,spk_BPMCode,spk_name,spk_chinesevalue,spk_englishvalue,spk_districtcode,spk_paymentmethod
into spk_districtmasterdata
from spk_districtmasterdata_Tmp

-----------配置数据-BU编码配置(及特殊推送编码)-------------------------------
--spk_organizationalMasterData_Tmp
select newid() as spk_NexBPMCode,a.spk_buname as spk_BPMCode, b.spk_NexBPMCode as spk_buname,spk_code,spk_pushspecialcodes 
into spk_bucodingconfiguration 
from spk_bucodingconfiguration_Tmp a
left join spk_organizationalMasterData b on a.spk_buname=b.spk_BPMCode

------------配置数据-城市主数据------------------------------
select newid()  as spk_NexBPMCode,spk_BPMCode,spk_Name,spk_citynumber 
into spk_citymasterdata 
from spk_citymasterdata_Tmp

------------配置数据-公司主数据------------------------------
select  newid()  as spk_NexBPMCode,spk_BPMCode,spk_Name,spk_chinesevalue,spk_englishvalue,spk_fax,spk_CompanyCode,spk_invoiceaddress,spk_invoicetitle,spk_openbank,spk_phone,spk_abbrcode,spk_bankcity,spk_bankaccount
into spk_companymasterdata 
from spk_companymasterdata_Tmp 

------------配置数据-成本中心主数据------------------------------
select  newid() as spk_NexBPMCode,spk_BPMCode,spk_name,spk_chinesevalue,spk_englishvalue,spk_costcentercode,spk_ccentercoder_num,spk_subsidiary
into spk_costcentermasterdata
from spk_costcentermasterdata_Tmp 

-------------配置数据-组织主数据-----------------------------
--spk_staffmasterdata_tmp spk_costcentermasterdata_tmp
select 
newid() as spk_NexBPMCode,a.spk_BPMCode,a.spk_Name,spk_chineseName,a.spk_englishName,a.spk_organizationType
,a.spk_epolevel,b.spk_NexBPMCode  as spk_epoleader,a.spk_parentorganization,c.spk_NexBPMCode as spk_costcenter,[spk_orgCode]
,[spk_orgParentCode]
into #a1
 from spk_organizationalmasterData_Tmp a
 left join spk_staffmasterdata b on b.bpm_id=a.spk_epoleader
 left join spk_costcentermasterdata c on c.spk_BPMCode=a.spk_costcenter
 select a.spk_NexBPMCode,a.spk_BPMCode,a.spk_Name,a.spk_chineseName,a.spk_englishName,a.spk_organizationType
,a.spk_epolevel,a.spk_epoleader,a3.spk_NexBPMCode as spk_parentorganization,a.spk_costcenter,a.[spk_orgCode]
, a.[spk_orgParentCode] 
into spk_organizationalmasterData
from #a1  a
left join  #a1  as a3
on a.spk_parentorganization=a3.spk_BPMCode 
 
 --------------配置数据-消费大类----------------------------
--spk_organizationalmasterdata_Tmp
select newid() as spk_NexBPMCode,a.spk_BPMCode,a.spk_name,a.spk_englishname,a.spk_number,iif(a.spk_isspecial=0,N'是',N'否') as spk_isspecial,iif(a.spk_isvendortype=0,N'是',N'否') as spk_isvendortype
,iif(a.spk_isassets=0,N'是',N'否') as spk_isassets
,iif(a.spk_isconferencefees=0,N'是',N'否') as  spk_isconferencefees,iif(a.spk_isdspot=0,'Yes','No') as spk_isdspot,a.spk_precode,b.spk_NexBPMCode as spk_organizational
into spk_consume
from spk_consume_Tmp a
LEFT JOIN spk_organizationalmasterdata b on a.spk_organizational=b.spk_bpmcode

------------配置数据-Expense审批矩阵------------------------------
--spk_organizationalmasterdata_Tmp
select newid() as spk_NexBPMCode,a.spk_bu as spk_BPMCode,b.spk_NexBPMCode as spk_bu,a.spk_approvalnumber,a.spk_level0,a.spk_level1,a.spk_level2,a.spk_level3,a.spk_level4,a.spk_level5,a.spk_level6
into spk_expenseapprovalmatrixconfigurationitem
from spk_expenseapprovalmatrixconfigurationitem_Tmp a
left join spk_organizationalmasterdata b on a.spk_bu=b.spk_BPMCode


-------------配置数据-费用性质-----------------------------
--spk_consume_Tmp
select  newid() as spk_NexBPMCode, a.spk_BPMCode,a.spk_name,a.spk_englishname,a.spk_costnumber,b.spk_NexBPMCode as spk_consume,spk_paymentmethod
,spk_approvalnumber,iif(ISNULL(spk_pushonlinemeeting,'')='','No','Yes') as spk_pushonlinemeeting,spk_unit,iif(ISNULL(spk_ar,'')='','No','Yes') as spk_ar,iif(ISNULL(spk_isBPCS,'')='','No','Yes') as spk_isBPCS
into spk_costnature
from spk_costnature_Tmp a
left join spk_consume b on a.spk_consume=b.spk_BPMCode

--------------配置数据-特殊采购审批条件-部分费用性质AR需采购额外审批/部分费用性质仅可申请AP----------------------------
--spk_costcentermasterdata_Tmp,spk_consume_Tmp,spk_costnature_Tmp
select newid()  as spk_NexBPMCode,a.spk_costcenter as spk_BPMCode,b.spk_name as spk_costcenter,sc.spk_NexBPMCode as spk_company,a.spk_type,a.spk_Name,c.spk_name as spk_consumercategories,d.spk_NexBPMCode as spk_natureofexpenses
into spk_specapprovalconditionforprocurement
from spk_specapprovalconditionforprocurement_Tmp a
left join spk_costcentermasterdata_Tmp b on a.spk_costcenter=b.spk_BPMCode
left join spk_consume_tmp c on a.spk_consumercategories=c.spk_BPMCode
left join spk_costnature d on a.spk_natureofexpenses=d.spk_BPMCode
left join ODS_T_RESOURCE r1
on r1.Res_Data =a.spk_company and r1.res_parent_code = '61a3f911b5ae4bc98cddd441833d861e'
left join spk_companymasterdata sc 
on r1.Res_Code =sc.spk_BPMCode 

------------------------------------------

--  spk_financialapprovalamountmatrix_Tmp;
--配置数据-财务审批矩阵
--依赖:spk_organizationalmasterdata_tmp
select newid() as spk_NexBPMCode,* into spk_financialapprovalamountmatrix from (
select ft.spk_bu as spk_BPMCode,ot.spk_NexBPMCode as spk_bu,ft.spk_procurementfinance,ft.spk_name from spk_financialapprovalamountmatrix_Tmp ft
join spk_organizationalmasterdata ot
on ot.spk_BPMCode=ft.spk_bu) A

--spk_financialapprovalruleconfig_Tmp
--配置数据-特殊财务审批条件-小于5K美金必须财务审批/财务总监与GM绑定/财务与财务总监同一人时需跳过财务总监审批
select newid() as spk_NexBPMCode,* into spk_financialapprovalruleconfig from (
select * from spk_financialapprovalruleconfig_Tmp)A

--  spk_financialapprovalruleandbu_Tmp;
--配置数据-特殊财务审批条件-条件适用的BU
--依赖:spk_organizationalmasterdata_tmp,spk_financialapprovalruleconfig_Tmp
select newid() as spk_NexBPMCode,* into spk_financialapprovalruleandbu from (
select ft.spk_bu as spk_BPMCode,ot.spk_NexBPMCode as spk_bu,ft1.spk_name+ot.spk_Name  as spk_name,ft1.spk_name as spk_rulename from spk_financialapprovalruleandbu_Tmp ft
join spk_organizationalmasterdata ot
on ft.spk_bu=ot.spk_BPMCode
join spk_financialapprovalruleconfig_Tmp ft1
on ft.spk_rulename=ft1.spk_BPMCode)A


--  spk_organizational_costcenter_Tmp;
--配置数据-成本中心主数据与BU映射关系
--依赖:spk_organizationalmasterdata_tmp,spk_costcentermasterdata_Tmp
select newid() as spk_NexBPMCode,* into spk_organizational_costcenter from (
select 
oct.spk_organizational as spk_BPMCode,
ot.spk_Name as spk_organizational,
pt.spk_name as spk_costcenter from spk_organizational_costcenter_Tmp oct
join spk_organizationalmasterdata_tmp ot
on oct.spk_organizational=ot.spk_BPMCode
join spk_costcentermasterdata_Tmp pt
on oct.spk_costcenter=pt.spk_BPMCode)A

--spk_organizational_product_Tmp;
--配置数据-产品主数据与BU映射关系
--依赖:spk_productmasterdata_tmp
select newid()  as spk_NexBPMCode,* into spk_organizational_product from (
select pt.spk_BPMCode,pt.spk_name as spk_product,opt.spk_BU,'' as spk_name from spk_organizational_product_Tmp opt
join spk_productmasterdata_tmp pt
on opt.spk_product=pt.spk_BPMCode)A

--spk_procurementapprovalamountmatrix_Tmp;
--配置数据-采购/比价审批矩阵
select newid() as spk_NexBPMCode
,a.[spk_mainprocurement]
,a.[spk_agencyprocurement]
,a.[spk_agentprocurementpricecomparisonapprover]
,a.[spk_pricecomparisonapprover]
,a.[spk_name]
,ot.spk_NexBPMCode as [spk_organizational]
into spk_procurementapprovalamountmatrix 
from spk_procurementapprovalamountmatrix_Tmp a
left join spk_organizationalmasterdata ot
on a.spk_organizational=ot.spk_BPMCode

--spk_organizationalmasterdata_citymasterdata_tmp
--配置数据-城市主数据与BU关系
--依赖:spk_citymasterdata_tmp,spk_organizationalmasterdata_tmp
select newid() as spk_NexBPMCode,* into spk_organizationalmasterdata_citymasterdata from (
select ot.spk_BPMCode,ot.spk_NexBPMCode as spk_organizationalmasterdataid,ct.spk_NexBPMCode as spk_citymasterdataid ,oct.spk_bu
from spk_organizationalmasterdata_citymasterdata_tmp oct
join spk_organizationalmasterdata ot
on oct.spk_organizationalmasterdataid=ot.spk_BPMCode
join spk_citymasterdata ct
on ct.spk_citynumber =oct.spk_citymasterdataid
)A

--岗位mapping
select 
a.[spk_Name],
b.spk_NexBPMCode  as [spk_staffname],
[spk_staffBPMCode],
c.spk_NexBPMCode as [spk_organization],
[spk_orgBPMCode],
[spk_position],
[spk_extentioncode2],
[spk_extensioncodeId]
into spk_extensioncode
from (
	select 
		a.[spk_Name],
		a.[spk_staffname],
		a.[spk_staffBPMCode],
		case when spk_position=N'MKT director审批岗' or spk_position=N'Sales director审批岗' then '917fa20d3bac4e47bb47790db4aa9a30'
		when  spk_position in (N'STP Header审批岗',N'STP Manager审批岗',N'指定采购岗') then d.spk_BPMCode
		else spk_organization
		end as [spk_organization],
		case when spk_position=N'MKT director审批岗' or spk_position=N'Sales director审批岗' then '917fa20d3bac4e47bb47790db4aa9a30'
		when  spk_position in (N'STP Header审批岗',N'STP Manager审批岗',N'指定采购岗') then d.spk_BPMCode
		else spk_orgBPMCode
		end as [spk_orgBPMCode],
		a.[spk_position],
		a.[spk_extentioncode2],
		a.[spk_extensioncodeId]
	from spk_extensioncode_tmp a
	left join spk_organizationalmasterdata d
	on 'BU'=d.spk_organizationType and spk_position in (N'STP Header审批岗',N'STP Manager审批岗',N'指定采购岗')
) a
left join spk_staffmasterdata b
on a.spk_staffname=b.bpm_id
left join spk_organizationalmasterdata c
on a.spk_organization=c.spk_BPMCode
where spk_position is not null  and spk_organization is not null 


--spk_organizational_district_Tmp
--配置数据-大区主数据与BU映射关系
--依赖：spk_organizationalmasterdata_tmp
select newid() as spk_NexBPMCode,* 
into spk_organizational_district 
from (
select ot.spk_BPMCode,d.spk_NexBPMCode as spk_district,ot.spk_NexBPMCode as spk_organizational from spk_organizational_district_Tmp sdt
join spk_organizationalmasterdata ot
on sdt.spk_organizational=ot.spk_BPMCode
left join (select *,ROW_NUMBER () over(PARTITION by spk_name order by spk_districtcode)rn  from spk_districtmasterdata) d
on sdt.spk_district =d.spk_name and d.rn=1)A

--人员组织关系
select newid() as spk_NexBPMCode,
[spk_code],
ss.spk_NexBPMCode as [spk_staffmasterdataid],
ot.spk_NexBPMCode as [spk_organizationalmasterdataid]
into spk_organizational_staff
from spk_organizational_staff_tmp oct
join spk_organizationalmasterdata ot
on oct.spk_organizationalmasterdataid=ot.spk_BPMCode
join spk_staffmasterdata ss 
on oct.spk_staffmasterdataid =ss.bpm_id 

-- --员工与组织关系
-- select * into PLATFORM_ABBOTT_Dev.dbo.spk_organizational_staff_Tmp  from (
-- select te.Emp_Id as spk_BPMCode,te.Emp_Name ,c.Res_Name
-- from PLATFORM_ABBOTT_Dev.dbo.ODS_T_ORGANIZATION_STRUCTURE b
-- left join   PLATFORM_ABBOTT_Dev.dbo.ODS_T_ORGANIZATION_EXTEND a on a.Org_Code=b.Child_Org_Code
-- left join PLATFORM_ABBOTT_Dev.dbo.ODS_T_RESOURCE c on c.Res_Code=b.Child_Org_Code
-- join PLATFORM_ABBOTT_Dev.dbo.ODS_T_EMPLOYEE te on te.Emp_Id =a.Data04 )A

--配置数据-城市主数据与公司映射关系
select newid() as spk_NexBPMCode,
c.spk_NexBPMCode as [spk_city],
ot.spk_NexBPMCode as [spk_company]
into spk_companymasterdata_citymasterdata
from spk_companymasterdata_citymasterdata_tmp a
join spk_companymasterdata ot
on a.spk_company=ot.spk_BPMCode
join spk_citymasterdata c
on concat(c.spk_Name,c.spk_citynumber)=a.spk_city

--配置数据-岗位mapping 1
DELETE FROM spk_extensioncode
where spk_name in (
select a.spk_name from spk_extensioncode  a
LEFT JOIN spk_staffmasterdata b ON a.spk_staffname = b.spk_NexBPMCode
LEFT JOIN spk_organizationalmasterdata c ON a.spk_organization = c.spk_NexBPMCode
WHERE b.spk_name IN (N'孙瑜  Sun Yu', N'张梦文 Mengwen Zhang')
AND c.spk_Name = 'ADD'
AND a.spk_position = N'IFO DPS Check审批岗(HCI机构)'
);

---------------员工主数据---------------------------
select cast('' as nvarchar(50)) as spk_NexBPMCode,* into PLATFORM_ABBOTT_Dev.dbo.spk_organizational_staff from(
select * from PLATFORM_ABBOTT_Dev.dbo.spk_organizational_staff_tmp)A

--Business flow type
if object_id(N'spk_agentconfiguration',N'u') is null
	select
		newid() [spk_agentconfigurationid],
		a.spk_businessflowtypeid [spk_businessflowname],
		a.StaffId [spk_originalapprover],
		a.AgentId [spk_agent],
		a.StartTime [spk_startDate],
		a.EndTime [spk_endDate],
		a.Description [spk_remark],
		a.Title [spk_name],
		a.IsRemind [spk_isnotifyoriginaloperator],
		'' [spk_numberofproxies],
		a.BpmId _BpmId,
		a.ProcessName _WorkflowType,
		a.CreatorId _Creator,
		a.IsEnable _Status,
		a.CreateTime _CreationTime
	into spk_agentconfiguration
	from [spk_agentconfiguration_tmp] a;
else
	insert spk_agentconfiguration
	select
		newid() [spk_agentconfigurationid],
		a.spk_businessflowtypeid [spk_businessflowname],
		a.StaffId [spk_originalapprover],
		a.AgentId [spk_agent],
		a.StartTime [spk_startDate],
		a.EndTime [spk_endDate],
		a.Description [spk_remark],
		a.Title [spk_name],
		a.IsRemind [spk_isnotifyoriginaloperator],
		'' [spk_numberofproxies],
		a.BpmId _BpmId,
		a.ProcessName _WorkflowType,
		a.CreatorId _Creator,
		a.IsEnable _Status,
		a.CreateTime _CreationTime
	from [spk_agentconfiguration_tmp] a
	where not exists(select * from spk_agentconfiguration where _BpmId=a.BpmId);
select [spk_agentconfigurationid],
	a.[spk_businessflowname],
	a.[spk_originalapprover],
	a.[spk_agent],
	a.[spk_startDate],
	a.[spk_endDate],
	a.[spk_remark],
	a.[spk_name],
	a.[spk_isnotifyoriginaloperator],
	'' [spk_numberofproxies]
from spk_agentconfiguration a;
--紧急
select newid() as spk_NexBPMCode,*
into spk_pturttypeconfig
from spk_pturttypeconfig_tmp

