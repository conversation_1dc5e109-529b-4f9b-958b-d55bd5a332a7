﻿using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Dataverse;
using Abbott.SpeakerPortal.Contracts.Integration.Dspot;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Entities.Integration.Dspot;
using Abbott.SpeakerPortal.Entities.Purchase.PurGRApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Enums.Integration;
using Abbott.SpeakerPortal.Extension;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;

namespace Abbott.SpeakerPortal.AppServices.Integration.Dspot
{
    public class InteBpcsPAAppService : SpeakerPortalAppService, IInteBpcsPAAppService
    {
        /// <summary>
        /// The service provider
        /// </summary>
        private readonly IServiceProvider _serviceProvider;

        /// <summary>
        /// The logger
        /// </summary>
        private readonly ILogger<InteBpcsPAAppService> _logger;

        /// <summary>
        /// The configuration
        /// </summary>
        private readonly IConfiguration _configuration;

        /// <summary>
        /// The dataverseService
        /// </summary>
        private readonly IDataverseService _dataverseService;

        public InteBpcsPAAppService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _logger = serviceProvider.GetService<ILogger<InteBpcsPAAppService>>();
            _configuration = serviceProvider.GetService<IConfiguration>();
            _dataverseService = serviceProvider.GetService<IDataverseService>();
        }

        /// <summary>
        /// 根据PA Id推送PA的EDI文件到BPCS的SFTP目录
        /// </summary>
        /// <param name="paId">PurPAApplication表ID</param>
        /// <returns></returns>
        public async Task<string> SyncPAById(Guid paId)
        {
            if (paId == Guid.Empty)
            {
                _logger.LogError($"SyncPAById() paId is Guid.Empty");
                return "paId is Guid.Empty";
            }

            try
            {
                var queryPA = (await LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>().GetQueryableAsync()).AsNoTracking();
                var queryPAFinaVoucher = (await LazyServiceProvider.LazyGetService<IPurPAFinancialVoucherInfoRepository>().GetQueryableAsync()).AsNoTracking();
                var paAndVoucher = queryPA.Where(a => a.Id == paId)
                    .GroupJoin(queryPAFinaVoucher, left => left.Id, right => right.PAId, (left, right) => new { pa = left, paFinaVoucher = right })
                    //.SelectMany(a => a.paFinaVoucher.DefaultIfEmpty(), (a, paFinaVoucher) => new { a.pa, paFinaVoucher })
                    .FirstOrDefault();

                if (paAndVoucher == null || paAndVoucher.pa == null || paAndVoucher.paFinaVoucher?.Any() != true)
                {
                    _logger.LogError($"SyncPAById() paAndVoucher == null || paAndVoucher.pa == null || paAndVoucher.paFinaVoucher?.Any() != true");
                    return "paAndVoucher is null";
                }

                //判断：表1CompanyCode+VenderCode去BPCSAVM表查，失效状态(VMID='VZ')则不允许提交
                var bpcsAvm = GetBpcsAvm(paAndVoucher.pa);
                if (!IsValidByVMID(paAndVoucher.pa, bpcsAvm))
                {
                    _logger.LogError($"SyncPAById() VMID is VZ");
                    return "VMID is VZ";
                }

                var dicBuCodingCfg = GetBuCodingCfg(paAndVoucher.pa);
                //签收日期 for Invoice Date	LPINVD	收货日期
                var invoiceDate = GetInvoiceDate(paAndVoucher.pa.Id);
                //复审人
                var reviewer = GetReviewer(paAndVoucher.pa.ApprovedUserId);

                //PurGRApplicationDetailHistory
                //组装EDI文件的内容，N条子行( 不要1条主行)
                Dictionary<string, string[]> dicUploadEdiInputs = new Dictionary<string, string[]>();
                List<string> ediLines = new List<string>();
                List<EdiLogApInvioce> ediLogs = new List<EdiLogApInvioce>();
                foreach (var item in paAndVoucher.paFinaVoucher)
                {
                    var (ediLine, ediLogApInv) = GetEdiLinesPaFinaVouchers(paAndVoucher.pa, item, bpcsAvm, dicBuCodingCfg, invoiceDate, reviewer);
                    ediLines.Add(ediLine);
                    ediLogs.Add(ediLogApInv);
                }
                if (!ediLines.Any())
                {
                    _logger.LogError($"SyncPAById() !ediLines.Any()");
                    return "ediLines is Empty";
                }

                dicUploadEdiInputs.Add(paAndVoucher.pa.Id.ToString(), new string[2] { CalcSftpVndPath(paAndVoucher.pa), ediLines.JoinAsString("\r\n") });
                if (dicUploadEdiInputs.Count < 1)
                {
                    _logger.LogError($"SyncPAById() dicUploadEdiInputs.Count < 1");
                    return "Upload Edi Inputs is null";
                }

                var repoApInv = LazyServiceProvider.LazyGetService<IEdiLogApInvioceRepository>();
                //保存表 EdiLogApInvioce（因为一个EDI一般有多行，就有多条ediLog，所以到外面去用.InsertManyAsync()一次性插入）
                if (ediLogs?.Any() == true)
                {
                    //本来要用 InsertManyAsync()一次性插入多条，但该方法返回void，不能获取Tracking的Entity，不便于后面的修改Stauts
                    for (int i = 0; i < ediLogs.Count; i++)
                    {
                        var item = repoApInv.InsertAsync(ediLogs[i], true).GetAwaiter().GetResult();
                        ediLogs[i] = item;
                    }
                }

                //推送到BPCS的SFTP
                var _sftpService = _serviceProvider.GetService<IInteBpcsSFTPService>();
                var uploadResult = _sftpService.UploadToBatch(dicUploadEdiInputs);
                //根据推送结果修改EdiLogVM.Status
                if (ediLogs?.Any() == true)
                {
                    var ediLogStatus = uploadResult ? EdiSyncStatus.Pushed : EdiSyncStatus.Init;
                    ediLogs.ForEach(a =>
                    {
                        a.Status = ediLogStatus;
                    });
                    //保存表 EdiLogApInvioce
                    repoApInv.UpdateManyAsync(ediLogs).GetAwaiter().GetResult();
                }
                if (!uploadResult)
                {
                    _logger.LogError($"SyncPAById() uploadResult is false");
                    return "Upload to SFTP is failed";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"SyncPAById() Exception: {ex}");
                return ex.Message;
            }

            return null;
        }

        private BpcsAvm GetBpcsAvm(PurPAApplication pa)
        {
            var repoBpcsAvm = LazyServiceProvider.LazyGetService<IBpcsAvmRepository>();
            var queryBpcsAvm = repoBpcsAvm.GetQueryableAsync().GetAwaiter().GetResult().AsNoTracking();
            return queryBpcsAvm.Where(a => a.Vendor.ToString() == pa.VendorCode && a.Vcmpny.ToString() == pa.CompanyCode)
                .OrderByDescending(a => a.UpdateTime).FirstOrDefault();
        }

        private bool IsValidByVMID(PurPAApplication pa, BpcsAvm bpcsAvm)
        {
            if (pa == null || bpcsAvm == null)
            {
                return false;
            }

            return !bpcsAvm.Vmid.Equals("VZ", StringComparison.OrdinalIgnoreCase);
        }

        private (string, EdiLogApInvioce) GetEdiLinesPaFinaVouchers(PurPAApplication pa, PurPAFinancialVoucherInfo paFinaVoucher, BpcsAvm bpcsAvm
            , Dictionary<Guid, BuCodingCfgDto> buCodingCfgs
            , DateTime? invoiceDate
            , IdentityUser lpUser)
        {
            //要根据PA+paFinaVoucher表来组装主行
            if (pa == null || paFinaVoucher == null)
            {
                _logger.LogError($"GetEdiLinesPaFinaVouchers() pa == null || paFinaVoucher == null");
                return (null, null);
            }

            //Console.WriteLine($"==== PA 现在的BpcsRate={pa.BpcsRate},ApprovedDate={pa.ApprovedDate}");

            //组装实体 EdiLogApInvioce，保存EDI日志表
            EdiLogApInvioce ediLogApInv = new EdiLogApInvioce()
            {
                TimeSendToBpcs = DateTime.Now,
                PAApplicationId = pa.Id,
                PAFinancialVoucherInfoId = paFinaVoucher.Id,
            };

            var result =
            $"{ediLogApInv.Lpvndr = pa.VendorCode.ToDecimal()}|"
            + $"{ediLogApInv.Lpinv = CalcPACodeCutA(pa)}|"
            + $"{ediLogApInv.Lpcina = paFinaVoucher.InvoiceLineAmount:F2}|"
            + $"{ediLogApInv.Lpinvd = paFinaVoucher.InvoiceDate?.ToDecimal()}|"
            + $"{ediLogApInv.Lpdesc = pa.InvoiceDescription}|"

            + $"{ediLogApInv.Lpinvr = CalcReceiptRef(pa)}|"
            + $"{ediLogApInv.Lpgldt = pa.ApprovedDate?.ToString("yyyyMMdd").ToDecimal()}|"
            //LPRESN,LPPAYT 这2个字段传空
            + $"{ediLogApInv.Lpresn = ""}|"
            + $"{ediLogApInv.Lppayt = ""}|"
            //LPBANC:判断如果表1.ComapnyCode==20，且表1.CurrencyCode==RMB，则传“CSB"，否则就传AVM表的VMBANK字段
            + $"{ediLogApInv.Lpbanc = (pa.CompanyCode == "20" && string.Equals(pa.Currency, "RMB", StringComparison.OrdinalIgnoreCase) ? "CSB" : bpcsAvm?.Vmbank)}|"

            + $"{ediLogApInv.Lpivrd = pa.AcceptedTime?.ToString("yyyyMMdd").ToDecimal()}|"
            + $"{ediLogApInv.Lpcurr = pa.Currency}|"
            + $"{ediLogApInv.Lpexch = (pa.BpcsRate.HasValue? (decimal)pa.BpcsRate.Value:(decimal)pa.ExchangeRate):F7}|"
            + $"{ediLogApInv.Lseg01 = CalcPriority(paFinaVoucher.CompanyCode, pa.CompanyCode, 2)}|"
            //+ $"{ediLogApInv.Lseg02 = CalcPriority(buCodingCfgs?.GetValue(pa.ApplyUserBu)?.BuCode, null, 2)}|"
            + $"{ediLogApInv.Lseg02 = CalcPriority(paFinaVoucher.DivisionCode, null, 2)}|"

            + $"{ediLogApInv.Lseg03 = CalcPriority(paFinaVoucher.CostCenter, null, 4)}|"
            + $"{ediLogApInv.Lseg04 = CalcPriority(paFinaVoucher.NatureAccount, null, 5)}|"
            + $"{ediLogApInv.Lseg05 = CalcPriority(paFinaVoucher.SubAccount, null, 4)}|"
            + $"{ediLogApInv.Lseg06 = CalcPriority(paFinaVoucher.Location, null, 4)}|"
            + $"{ediLogApInv.Lseg07 = ""}|"

            + $"{ediLogApInv.Lvatc = ""}|"
            + $"{ediLogApInv.Lpanb1 = pa.PaymentTerms}|"
            + $"{ediLogApInv.Lpanb2 = ""}|"
            + $"{ediLogApInv.Lpanb3 = ""}|"
            + $"{ediLogApInv.Lpanb4 = ""}|"

            + $"{ediLogApInv.Lpanb5 = ""}|"
            + $"{ediLogApInv.Lpanb6 = ""}|"
            + $"{ediLogApInv.Lpann1 = null}|"
            + $"{ediLogApInv.Lpann2 = null}|"
            + $"{ediLogApInv.Lpand1 = null}|"

            + $"{ediLogApInv.Lpand2 = pa.ApprovedDate?.ToString("yyyyMMdd").ToDecimal()}|"
            + $"{ediLogApInv.Lpuser = lpUser?.UserName}|"
            + $"{ediLogApInv.Lpcrdt = pa.ApplyTime.ToString("yyyyMMdd").ToDecimal()}|"
            + $"{ediLogApInv.Lptime = pa.ApplyTime.ToString("HHmmss").ToDecimal()}|"
            + $""//ediLogApInv.Errmsg;//共35个字段，即34根竖线，上一个字段后面已有竖线，所以最后不再要竖线
            ;
            return (result, ediLogApInv);
        }

        private string CalcPACodeCutA(PurPAApplication pa)
        {
            return pa?.ApplicationCode?.Substring(pa.ApplicationCode.IndexOf('A') + 1);
        }

        private string CalcReceiptRef(PurPAApplication pa)
        {
            if (pa == null)
            {
                return null;
            }

            //如果表1.AkritivCaseID不为空且格式是 G+9位数字，则取表1.AkritivCaseID
            //否则，取去掉首字母的表1.ApplicationCode
            //if (!string.IsNullOrWhiteSpace(pa.AkritivCaseID) && Regex.IsMatch(pa.AkritivCaseID, @"^G\d{9}$"))
            //{
            //    return pa.AkritivCaseID;
            //}
            //return CalcPACodeCutA(pa);

            //4485 ytw 20250313 只要AkritivID为空或者等于"G"才换成PA单号移除首个A，否则就按填入的akritivID来做，不要判断是不是跟着九位数字
            if (string.IsNullOrWhiteSpace(pa.AkritivCaseID) || pa.AkritivCaseID.Trim() == "G")
            {
                return CalcPACodeCutA(pa); 
            }
            return pa.AkritivCaseID;
        }

        private string CalcPriority(string priorityStr, string spareStr, int minLen, char padLeftChar = '0')
        {
            var resultStr = !string.IsNullOrWhiteSpace(priorityStr) ? priorityStr : spareStr;
            return !string.IsNullOrWhiteSpace(resultStr) ? resultStr.PadLeft(minLen, padLeftChar) : null;
        }

        private Dictionary<Guid, BuCodingCfgDto> GetBuCodingCfg(List<Guid> listBuId)
        {
            if (listBuId?.Any() != true)
            {
                return new Dictionary<Guid, BuCodingCfgDto>();
            }

            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var dicBuCodingCfg = dataverseService.GetBuCodingCfgAsync().GetAwaiter().GetResult();
            return dicBuCodingCfg.Where(a => listBuId.Contains(a.BuId)).ToDictionary(a => a.BuId, a => a);
        }

        private Dictionary<Guid, BuCodingCfgDto> GetBuCodingCfg(PurPAApplication pa)
        {
            List<Guid> listBuId = new List<Guid>();
            if (pa?.ApplyUserBu != null)
            {
                listBuId.Add(pa.ApplyUserBu);
            }

            return GetBuCodingCfg(listBuId);
        }

        private string CalcSftpVndPath(PurPAApplication appPa)
        {
            string folderLvl1 = _configuration["Integrations:BPCS:SftpFolderLvl1"];

            //获取公司的缩写名称
            var allCompany = _dataverseService.GetCompanyList().GetAwaiter().GetResult();
            string folderLvl2 = $"{(allCompany?.Any() != true ? "" : allCompany.FirstOrDefault(a => a.CompanyCode == appPa.CompanyCode)?.AbbrCode)}_{InteBpcsDataType.AP}";

            string folderLvl3 = _configuration["Integrations:BPCS:SftpFolderLvl3Upload"];

            //计算文件名，Vendor：A+7位序列号（从0000001开始）
            var seqNumService = _serviceProvider.GetService<ISequenceNumService>();
            var fileName = seqNumService.GetNextFileNameAndIncrease(Enums.SequenceNumCategory.A);

            //正反斜杠都行；最前面要不要"/"也都行
            //return $"/{folderLvl1}/{folderLvl2}/{folderLvl3}/V0000001_{DateTime.Now.ToString("yyyy-MM-dd_HHmmss")}.TXT";
            return Path.Combine("/", folderLvl1, folderLvl2, folderLvl3, fileName);
        }

        private DateTime? GetInvoiceDate(Guid id)
        {
            var repoGrDetailHis = LazyServiceProvider.LazyGetService<IPurGRApplicationDetailHistoryRepository>();
            var maxSigningDt = repoGrDetailHis.GetQueryableAsync().GetAwaiter().GetResult().AsNoTracking()
                .Where(x => x.PurPAApplicationId == id)
                .OrderByDescending(a => a.SigningDate)
                .Select(x => x.SigningDate)
                .FirstOrDefault();
            return maxSigningDt;
        }

        private IdentityUser GetReviewer(Guid? userId)
        {
            if (userId == null)
            {
                return null;
            }

            var queryableUser = LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>()
                .GetQueryableAsync().GetAwaiter().GetResult().AsNoTracking();
            return queryableUser.Where(a => a.Id == userId.Value).FirstOrDefault();
        }
    }
}