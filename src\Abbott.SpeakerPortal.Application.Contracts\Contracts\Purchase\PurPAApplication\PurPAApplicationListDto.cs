﻿using MiniExcelLibs.Attributes;
using System;
using System.Collections.Generic;
using System.Text;
using static Abbott.SpeakerPortal.Enums.PurPAStatus;

namespace Abbott.SpeakerPortal.Contracts.Purchase.PurPAApplication
{
    //public class PurPAApplicationListDto : PurPAApplicationDto
    public class PurPAApplicationListDto
    {
        /// <summary>
        /// PA Id
        /// </summary>
        public Guid Id { get; set; }
        /// <summary>
        /// PA申请单编号
        /// </summary>
        public string ApplicationCode { get; set; }

        /// <summary>
        /// PR Id
        /// </summary>
        public Guid PRId { get; set; }
        /// <summary>
        /// PR申请单号
        /// </summary>
        public string PrApplicationCode { get; set; }

        public PurPAApplicationStatus Status { get; set; } = PurPAApplicationStatus.FillIn;
        public DateTime ApplyTime { get; set; }
        public Guid ApplyUserId { get; set; }
        /// <summary>
        /// 申请人名字
        /// </summary>
        public string ApplyUserName { get; set; }
        /// <summary>
        /// 申请人BU
        /// </summary>
        public Guid ApplyUserBu { get; set; }

        /// <summary>
        /// 申请人部门Id
        /// </summary>
        public Guid ApplyUserBuToDept { get; set; }
        /// <summary>
        /// 申请人部门名称
        /// </summary>
        public string ApplyUserBuToDeptName { get; set; }
        /// <summary>
        ///付款形式
        /// </summary>
        public PaymentTypes PaymentType { get; set; } = PaymentTypes.WireTransfer;
        /// <summary>
        /// 供应商名字
        /// </summary>
        public string VendorName { get; set; }

        /// <summary>
        /// GR Id
        /// </summary>
        public Guid GRId { get; set; }
        /// <summary>
        /// GR申请单号
        /// </summary>
        public string GRApplicationCode { get; set; }

        /// <summary>
        /// PO Id
        /// </summary>
        public Guid? POId { get; set; }
        /// <summary>
        ///PO申请单号
        /// </summary>
        public string POApplicationCode { get; set; }

        public DateTime CreationTime { get; set; }
        public Guid? TransfereeId { get; set; }

        /// <summary>
        /// 支付金额
        /// </summary>
        public decimal PayTotalAmount { get; set; }
        /// <summary>
        ///是否候补发票
        /// </summary>
        public bool IsBackupInvoice { get; set; }
        public Guid? PrSubBudgetId { get; set; }

        /// <summary>
        /// 预计打款时间
        /// </summary>
        public string EstimatedPaymentTime { get; set; }

        /// <summary>
        /// 打款时间
        /// </summary>
        public string PaymentTime { get; set; }

        /// <summary>
        /// 网银支付时间
        /// </summary>
        public string OnlinePaymentTime { get; set; }
        /// <summary>
        /// RefNo
        /// </summary>
        public string RefNo { get; set; }
    }
}
