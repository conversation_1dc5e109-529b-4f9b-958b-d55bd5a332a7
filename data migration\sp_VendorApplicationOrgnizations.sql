CREATE PROCEDURE dbo.sp_VendorApplicationOrgnizations
AS 
BEGIN
select * into #VendorApplicationOrgnizations_Tmp from (
select 
newid() AS Id,--自动生成的uuid
tsi.ProcInstId,
vt.ID AS ApplicationId,--对于VendorType=3/4的机构类型供应商，此处填入对应VendorApplications的Id，用于标记该行为机构供应商申请单对应的扩展信息
supplierCNName AS VendorName,--
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/supplierOldName)[1]', 'nvarchar(50)') AS VendorOldName,--
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/supplierENName)[1]', 'nvarchar(50)') AS VendorEngName,--
supplierAddress AS RegCertificateAddress,--
postCode AS PostCode,--
contacts AS ContactName,--
supplierPhone AS ContactPhone,--
supplierEmail AS ContactEmail,--
case when vt.VendorType='3' then
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/AgencyWebsite)[1]', 'nvarchar(50)') 
else '' end  AS WebSite,--仅适用于HCI机构(NH)
case when vt.VendorType='3' then
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/FoundDate)[1]', 'nvarchar(50)') 
else '' end AS RegisterDate,--仅适用于HCI机构(NH)
case when vt.VendorType='3' then
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/AgencyType_Value)[1]', 'nvarchar(50)') else '' end AS OrgType,--基于该Code定位对应字典值
case when vt.VendorType='3' then
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/IssuingAuthority)[1]', 'nvarchar(50)') else '' end AS IssuingAuthority,--仅适用于HCI机构(NH)
case when vt.VendorType='3' then
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/SocialCreditCode)[1]', 'nvarchar(50)') else '' end AS RegisterCode,--仅适用于HCI机构(NH)
case when vt.VendorType='3' then
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/ValidityPeriod)[1]', 'nvarchar(50)') else '' end AS RegValidityStart,--仅适用于HCI机构(NH)
case when vt.VendorType='3' then
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/ValidityPeriod1)[1]', 'nvarchar(50)') else '' end  AS RegValidityEnd,--仅适用于HCI机构(NH)
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/SupplierProvince)[1]', 'nvarchar(50)')  AS Province,--需要匹配至省份主数据后找回Code(如果为空或匹配失败可基于城市带回)
case when vt.VendorType='3' then
'' else cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/supplierCity)[1]', 'nvarchar(50)')  end AS City,--该条件仅适用于非HCI机构(NL)，需要匹配至城市主数据后找回Code
--cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/Residence)[1]', 'nvarchar(50)') AS ,--该条件仅适用于HCI机构(NH)，需要匹配至城市主数据后找回Code
case when vt.VendorType='3' then
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/LegalRepresentative)[1]', 'nvarchar(50)') else '' end AS Legal,--仅适用于HCI机构(NH)
case when vt.VendorType='3' then
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/RegisteredCapital)[1]', 'nvarchar(50)') else '' end AS RegisterAmount,--仅适用于HCI机构(NH)
case when vt.VendorType='3' then
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/BusinessUnit)[1]', 'nvarchar(50)') else '' end AS BusinessAuthority,--仅适用于HCI机构(NH)
case when vt.VendorType='3' then
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/BusinessScope)[1]', 'nvarchar(50)') else '' end AS BusinessScope,--该条件仅适用于HCI机构(NH)
--cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/serviceArea)[1]', 'nvarchar(50)') AS ,--该条件仅适用于非HCI机构(NL)
case when vt.VendorType!='3' then
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/saleAmount)[1]', 'nvarchar(50)') else '' end AS LastYearSales,--仅适用于非HCI机构(NL)
case when vt.VendorType!='3' then
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/mainIndustry)[1]', 'nvarchar(50)') else '' end AS KeyIndustry,--仅适用于非HCI机构(NL)
case when vt.VendorType!='3' then
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/mainCustomer)[1]', 'nvarchar(50)') else '' end AS KeyClient,--仅适用于非HCI机构(NL)
case when vt.VendorType!='3' then
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/empNumber)[1]', 'nvarchar(50)') else '' end AS Staffs,--仅适用于非HCI机构(NL)
case when vt.VendorType!='3' then
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/certification)[1]', 'nvarchar(50)') else '' end AS Aptitudes,--仅适用于非HCI机构(NL)，目前ABP处该数据填写在了Vendors.CertificateCode内，不确定是否会进行调整
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/comEvaluation)[1]', 'nvarchar(50)')  AS ApplyReason,--两种机构均适用
'{}' AS ExtraProperties,--默认填写为"{}"
'' AS ConcurrencyStamp,--?
vt.CreationTime AS CreationTime,--与对应的VendorApplications记录保持一致即可
vt.CreatorId AS CreatorId,--与对应的VendorApplications记录保持一致即可
'' AS LastModificationTime,--默认为空
'' AS LastModifierId,--默认为空
'0' AS IsDeleted,--默认为0
'' AS DeleterId,--默认为空
'' AS DeletionTime,--默认为空
case when vt.VendorType='3' then
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/shareholder)[1]', 'nvarchar(50)') else '' end AS Shareholder--仅适用于非HCI机构(NL)
from PLATFORM_ABBOTT_Stg.dbo.ODS_AUTO_BIZ_T_SupplierApplication_Info tsi
join  PLATFORM_ABBOTT_Stg.dbo.ODS_T_FORMINSTANCE_GLOBAL a
on tsi.ProcInstId =a.ProcInstId
join PLATFORM_ABBOTT_Stg.dbo.VendorApplications_Tmp vt
on tsi.ProcInstId =vt.ProcInstId
where vt.VendorType in ('3','4')
union ALL 
select 
newid() AS Id,--自动生成的uuid
tsi.ProcInstId,
vt.ID AS ApplicationId,--对于VendorType=3/4的机构类型供应商，此处填入对应VendorApplications的Id，用于标记该行为机构供应商申请单对应的扩展信息
supplierCNName AS VendorName,--
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/supplierOldName)[1]', 'nvarchar(50)') AS VendorOldName,--
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/supplierENName)[1]', 'nvarchar(50)') AS VendorEngName,--
'' AS RegCertificateAddress,--
'' AS PostCode,--
'' AS ContactName,--
'' AS ContactPhone,--
'' AS ContactEmail,--
case when vt.VendorType='3' then
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/AgencyWebsite)[1]', 'nvarchar(50)') 
else '' end  AS WebSite,--仅适用于HCI机构(NH)
case when vt.VendorType='3' then
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/FoundDate)[1]', 'nvarchar(50)') 
else '' end AS RegisterDate,--仅适用于HCI机构(NH)
case when vt.VendorType='3' then
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/AgencyType_Value)[1]', 'nvarchar(50)') else '' end AS OrgType,--基于该Code定位对应字典值
case when vt.VendorType='3' then
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/IssuingAuthority)[1]', 'nvarchar(50)') else '' end AS IssuingAuthority,--仅适用于HCI机构(NH)
case when vt.VendorType='3' then
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/SocialCreditCode)[1]', 'nvarchar(50)') else '' end AS RegisterCode,--仅适用于HCI机构(NH)
case when vt.VendorType='3' then
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/ValidityPeriod)[1]', 'nvarchar(50)') else '' end AS RegValidityStart,--仅适用于HCI机构(NH)
case when vt.VendorType='3' then
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/ValidityPeriod1)[1]', 'nvarchar(50)') else '' end  AS RegValidityEnd,--仅适用于HCI机构(NH)
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/SupplierProvince)[1]', 'nvarchar(50)')  AS Province,--需要匹配至省份主数据后找回Code(如果为空或匹配失败可基于城市带回)
case when vt.VendorType!='3' then
'' else cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/Residence)[1]', 'nvarchar(50)')  end AS City,--该条件仅适用于非HCI机构(NL)，需要匹配至城市主数据后找回Code
--cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/Residence)[1]', 'nvarchar(50)') AS ,--该条件仅适用于HCI机构(NH)，需要匹配至城市主数据后找回Code
case when vt.VendorType='3' then
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/LegalRepresentative)[1]', 'nvarchar(50)') else '' end AS Legal,--仅适用于HCI机构(NH)
case when vt.VendorType='3' then
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/RegisteredCapital)[1]', 'nvarchar(50)') else '' end AS RegisterAmount,--仅适用于HCI机构(NH)
case when vt.VendorType='3' then
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/BusinessUnit)[1]', 'nvarchar(50)') else '' end AS BusinessAuthority,--仅适用于HCI机构(NH)
case when vt.VendorType!='3' then
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/serviceArea)[1]', 'nvarchar(50)') else '' end AS BusinessScope,--该条件仅适用于HCI机构(NH)
--cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/serviceArea)[1]', 'nvarchar(50)') AS ,--该条件仅适用于非HCI机构(NL)
case when vt.VendorType!='3' then
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/saleAmount)[1]', 'nvarchar(50)') else '' end AS LastYearSales,--仅适用于非HCI机构(NL)
case when vt.VendorType!='3' then
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/mainIndustry)[1]', 'nvarchar(50)') else '' end AS KeyIndustry,--仅适用于非HCI机构(NL)
case when vt.VendorType!='3' then
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/mainCustomer)[1]', 'nvarchar(50)') else '' end AS KeyClient,--仅适用于非HCI机构(NL)
case when vt.VendorType!='3' then
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/empNumber)[1]', 'nvarchar(50)') else '' end AS Staffs,--仅适用于非HCI机构(NL)
case when vt.VendorType!='3' then
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/certification)[1]', 'nvarchar(50)') else '' end AS Aptitudes,--仅适用于非HCI机构(NL)，目前ABP处该数据填写在了Vendors.CertificateCode内，不确定是否会进行调整
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/comEvaluation)[1]', 'nvarchar(50)')  AS ApplyReason,--两种机构均适用
'{}' AS ExtraProperties,--默认填写为"{}"
'' AS ConcurrencyStamp,--?
vt.CreationTime AS CreationTime,--与对应的VendorApplications记录保持一致即可
vt.CreatorId AS CreatorId,--与对应的VendorApplications记录保持一致即可
'' AS LastModificationTime,--默认为空
'' AS LastModifierId,--默认为空
'0' AS IsDeleted,--默认为0
'' AS DeleterId,--默认为空
'' AS DeletionTime,--默认为空
case when vt.VendorType='3' then
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/shareholder)[1]', 'nvarchar(50)') else '' end AS Shareholder--仅适用于非HCI机构(NL)
from PLATFORM_ABBOTT_Stg.dbo.ODS_AUTO_BIZ_T_HcpLevelApplication_info tsi
join  PLATFORM_ABBOTT_Stg.dbo.ODS_T_FORMINSTANCE_GLOBAL a
on tsi.ProcInstId =a.ProcInstId
join PLATFORM_ABBOTT_Stg.dbo.VendorApplications_Tmp vt
on tsi.ProcInstId =vt.ProcInstId
where vt.VendorType in ('3','4')
)A

--写入目标表
IF OBJECT_ID(N'PLATFORM_ABBOTT_Stg.dbo.VendorApplicationOrgnizations_Tmp', N'U') IS NOT NULL
BEGIN
	update a 
	set a.ProcInstId             = b.ProcInstId
        ,a.ApplicationId         = b.ApplicationId
        ,a.VendorName            = b.VendorName
        ,a.VendorOldName         = b.VendorOldName
        ,a.VendorEngName         = b.VendorEngName
        ,a.RegCertificateAddress = b.RegCertificateAddress
        ,a.PostCode              = b.PostCode
        ,a.ContactName           = b.ContactName
        ,a.ContactPhone          = b.ContactPhone
        ,a.ContactEmail          = b.ContactEmail
        ,a.WebSite               = b.WebSite
        ,a.RegisterDate          = b.RegisterDate
        ,a.OrgType               = b.OrgType
        ,a.IssuingAuthority      = b.IssuingAuthority
        ,a.RegisterCode          = b.RegisterCode
        ,a.RegValidityStart      = b.RegValidityStart
        ,a.RegValidityEnd        = b.RegValidityEnd
        ,a.Province              = b.Province
        ,a.City                  = b.City
        ,a.Legal                 = b.Legal
        ,a.RegisterAmount        = b.RegisterAmount
        ,a.BusinessAuthority     = b.BusinessAuthority
        ,a.BusinessScope         = b.BusinessScope
        ,a.LastYearSales         = b.LastYearSales
        ,a.KeyIndustry           = b.KeyIndustry
        ,a.KeyClient             = b.KeyClient
        ,a.Staffs                = b.Staffs
        ,a.Aptitudes             = b.Aptitudes
        ,a.ApplyReason           = b.ApplyReason
        ,a.ExtraProperties       = b.ExtraProperties
        ,a.ConcurrencyStamp      = b.ConcurrencyStamp
        ,a.CreationTime          = b.CreationTime
        ,a.CreatorId             = b.CreatorId
        ,a.LastModificationTime  = b.LastModificationTime
        ,a.LastModifierId        = b.LastModifierId
        ,a.IsDeleted             = b.IsDeleted
        ,a.DeleterId             = b.DeleterId
        ,a.DeletionTime          = b.DeletionTime
        ,a.Shareholder           = b.Shareholder
    from PLATFORM_ABBOTT_Stg.dbo.VendorApplicationOrgnizations_Tmp a 
    left join #VendorApplicationOrgnizations_Tmp b on a.ProcInstId = b.ProcInstId
	
    insert into PLATFORM_ABBOTT_Stg.dbo.VendorApplicationOrgnizations_Tmp
    select a.Id
          ,a.ProcInstId
          ,a.ApplicationId
          ,a.VendorName
          ,a.VendorOldName
          ,a.VendorEngName
          ,a.RegCertificateAddress
          ,a.PostCode
          ,a.ContactName
          ,a.ContactPhone
          ,a.ContactEmail
          ,a.WebSite
          ,a.RegisterDate
          ,a.OrgType
          ,a.IssuingAuthority
          ,a.RegisterCode
          ,a.RegValidityStart
          ,a.RegValidityEnd
          ,a.Province
          ,a.City
          ,a.Legal
          ,a.RegisterAmount
          ,a.BusinessAuthority
          ,a.BusinessScope
          ,a.LastYearSales
          ,a.KeyIndustry
          ,a.KeyClient
          ,a.Staffs
          ,a.Aptitudes
          ,a.ApplyReason
          ,a.ExtraProperties
          ,a.ConcurrencyStamp
          ,a.CreationTime
          ,a.CreatorId
          ,a.LastModificationTime
          ,a.LastModifierId
          ,a.IsDeleted
          ,a.DeleterId
          ,a.DeletionTime
          ,a.Shareholder
	from #VendorApplicationOrgnizations_Tmp a
	where not exists (select * from PLATFORM_ABBOTT_Stg.dbo.VendorApplicationOrgnizations_Tmp where a.ProcInstId = ProcInstId)
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select * into PLATFORM_ABBOTT_Stg.dbo.VendorApplicationOrgnizations_Tmp from #VendorApplicationOrgnizations_Tmp
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END

END;
