﻿using System;
using Abbott.SpeakerPortal.Enums;

namespace Abbott.SpeakerPortal.Contracts.Budget.FocHistory
{
    public class FocHistoryRecordsResponseDto
    {
        public Guid Id { get; set; }
        ///// <summary>
        ///// 主预算,子预算ID
        ///// </summary>
        ////public Guid BudgetId { get; set; }
        ///// <summary>
        ///// 预算类型
        ///// </summary>
        ////public BudgetType BudgetType { get; set; }
        /// <summary>
        /// 操作人ID
        /// </summary>
        public Guid OperatorId { get; set; }
        /// <summary>
        /// 操作人
        /// </summary>
        public string OperatorName { get; set; }
        /// <summary>
        /// 操作时间
        /// </summary>
        public DateTime OperatingTime { get; set; }
        /// <summary>
        /// 操作状态
        /// </summary>
        public OperateType OperateType { get; set; }
        /// <summary>
        /// 操作数量
        /// </summary>
        public int? OperateQty { get; set; }
        /// <summary>
        /// 操作内容
        /// </summary>
        public string OperateContent { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
    }
}
