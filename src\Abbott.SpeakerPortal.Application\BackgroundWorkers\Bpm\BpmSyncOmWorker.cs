﻿using Abbott.SpeakerPortal.Contracts.Bpm;
using Hangfire;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.BackgroundWorkers.Bpm
{
    public class BpmSyncOmWorker : SpeakerPortalBackgroundWorkerBase
    {
        public BpmSyncOmWorker()
        {
            //触发周期,每2小时
            CronExpression = Cron.Never();
        }

        public override async Task DoWorkAsync(CancellationToken cancellationToken = default)
        {
            await LazyServiceProvider.LazyGetService<IBpmOmService>().PullBpmOmLogAsync();
        }
    }
}
