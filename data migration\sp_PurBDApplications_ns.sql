CREATE PROCEDURE dbo.sp_PurBDApplications_ns
AS 
BEGIN
	/*
 * 1.VendorId未处理  --已处理
 * 2.AttachmentFile没有值
 */
	
with 
PRApplicationDetailId_info as (
select 
a.ProcInstId ,
STRING_AGG(cast(b.id as nvarchar(50)), ', ') WITHIN GROUP (ORDER BY b.id) AS ID
from  PLATFORM_ABBOTT_Stg.dbo.PurBDApplications_tmp a
join ods_AUTO_BIZ_T_BiddingApplication_Info_PR b1
on a.ProcInstId=b1.ProcInstId
left join PLATFORM_ABBOTT_Stg.dbo.PurPRApplications_tmp c
on B1.PRFormCode  =c.ApplicationCode
left join PLATFORM_ABBOTT_Stg.dbo.PurPRApplicationDetails_tmp   b
on b1.PRNumber=b.RowNo and c.ProcInstId =b.ProcInstId  --[PRFormCode]+[PRNumber]
GROUP BY  a.ProcInstId
),
PurPRApplications_info as (
select 
a.ProcInstId ,c.CompanyId
from  PLATFORM_ABBOTT_Stg.dbo.PurBDApplications_tmp a
join ods_AUTO_BIZ_T_BiddingApplication_Info_PR b1
on a.ProcInstId=b1.ProcInstId
left join PLATFORM_ABBOTT_Stg.dbo.PurPRApplications_tmp c
on B1.PRFormCode  =c.ApplicationCode
left join PLATFORM_ABBOTT_Stg.dbo.PurPRApplicationDetails_tmp   b
on b1.PRNumber=b.RowNo and c.ProcInstId =b.ProcInstId  --[PRFormCode]+[PRNumber]
group by a.ProcInstId ,c.CompanyId
)
select 
	a.Id,
	a.ApplicationCode,
	b.ID as PRApplicationDetailId,
	a.Status,
	UPPER(ss.spk_NexBPMCode)  as ApplyUserId,
	a.ApplyUserName,
	a.ApplyTime,
	UPPER(soc.spk_NexBPMCode)  as ApplyUserBu,
	a.ApplyUserBuName,
	UPPER(ss1.spk_NexBPMCode) as BudgetManagerUserId,
	a.BudgetManagerUserName,
	UPPER(cc.Id) as VendorId,
	a.VendorName,
	a.Currency,
	a.SingleChoice,
	a.BDApplicationId,
	a.BDApplicationCode,
	a.PRCorrespond,
	a.Explain,
	a.OtherSupplierExplain,
	a.AttachmentFile,
	a.ExtraProperties,
	a.ConcurrencyStamp,
	a.CreationTime,
	UPPER(ss2.spk_NexBPMCode) as CreatorId,
	a.LastModificationTime,
	a.LastModifierId,
	a.IsDeleted,
	a.DeleterId,
	a.DeletionTime,
	a.TotalAmount,
	a.VendorCode
into #PurBDApplications
from PLATFORM_ABBOTT_Stg.dbo.PurBDApplications_tmp a --11786
left join PRApplicationDetailId_info b
on a.ProcInstId=b.ProcInstId 
left join PLATFORM_ABBOTT_Stg.dbo.spk_staffmasterdata ss 
on a.ApplyUserId =ss.bpm_id 
left join PLATFORM_ABBOTT_Stg.dbo.spk_organizationalmasterdata soc 
on a.ApplyUserBu =soc.spk_BPMCode 
left join PLATFORM_ABBOTT_Stg.dbo.spk_staffmasterdata ss1 
on a.BudgetManagerUserId COLLATE SQL_Latin1_General_CP1_CI_AS =ss1.bpm_id 
left join PLATFORM_ABBOTT_Stg.dbo.spk_staffmasterdata ss2 
on a.CreatorId =ss2.bpm_id 
left join PurPRApplications_info e
on a.ProcInstId=e.ProcInstId 
left join PLATFORM_ABBOTT_Stg.dbo.ods_BPCS_PMFVM f
on a.VendorId = f.[VNDERX] and a.VendorName=f.[VEXTNM] and e.CompanyId=f.[VMCMPY]
left join PLATFORM_ABBOTT_Stg.dbo.ODS_BPCS_AVM  cc
on f.[VMCMPY]=cc.[VCMPNY] AND f.[VNDERX]=cc.[VENDOR]



IF OBJECT_ID(N'PLATFORM_ABBOTT_Stg.dbo.PurBDApplications', N'U') IS NOT NULL
BEGIN
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
    drop table PLATFORM_ABBOTT_Stg.dbo.PurBDApplications
    select *
    into PLATFORM_ABBOTT_Stg.dbo.PurBDApplications from #PurBDApplications
	END
	ELSE
	BEGIN
    --落成实体表
    select *
    into PLATFORM_ABBOTT_Stg.dbo.PurBDApplications from #PurBDApplications
	  PRINT(N' 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END


END;