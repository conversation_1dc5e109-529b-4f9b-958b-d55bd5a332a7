CREATE PROCEDURE dbo.sp_PurPOApplicationDetails_ns
AS 
BEGIN
	select 
Id,
POApplicationId,
Content,
Quantity,
Unit,
UnitPrice,
di2.spk_code  as InvoiceType,
TotalAmount,
TaxRate,
TaxAmount,
ApplicationCode,
ExtraProperties,
ConcurrencyStamp,
a.CreatorId as CreationTime,
UPPER(ss1.spk_NexBPMCode)  as CreatorId,
LastModificationTime,
LastModifierId,
IsDeleted,
DeleterId,
DeletionTime,
TotalAmountNoTax,
PRDetailId,
Price,
RowNo,
PORowNo
into #PurPOApplicationDetails
from  PLATFORM_ABBOTT_Dev.dbo.PurPOApplicationDetails_tmp a
left join PLATFORM_ABBOTT_Dev.dbo.spk_dictionary di2
on a.InvoiceType  =di2.spk_Name  and di2.spk_type=N'发票类型'
left join PLATFORM_ABBOTT_Dev.dbo.spk_staffmasterdata ss1 
on a.CreationTime =ss1.bpm_id 

IF OBJECT_ID(N'PLATFORM_ABBOTT_Dev.dbo.PurPOApplicationDetails', N'U') IS NOT NULL
BEGIN
	PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	drop table PLATFORM_ABBOTT_Dev.dbo.PurPOApplicationDetails
	select  *  into PLATFORM_ABBOTT_Dev.dbo.PurPOApplicationDetails from #PurPOApplicationDetails
END
ELSE
BEGIN
	select  *  into PLATFORM_ABBOTT_Dev.dbo.PurPOApplicationDetails from #PurPOApplicationDetails
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
END
