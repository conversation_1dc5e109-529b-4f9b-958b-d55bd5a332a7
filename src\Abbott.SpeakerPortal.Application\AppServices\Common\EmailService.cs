﻿using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.BPMEmail;
using Abbott.SpeakerPortal.Contracts.Common.Common;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.BPMEmail;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Mail;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.AppServices.Common
{
    public class EmailService : SpeakerPortalAppService, IEmailService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<EmailService> _logger;
        private IServiceProvider _serviceProvider;

        private readonly string _smtpServer;
        private readonly string _fromEmail;

        public EmailService(IServiceProvider serviceProvider)
        {
            _configuration = serviceProvider.GetService<IConfiguration>();
            _logger = serviceProvider.GetService<ILogger<EmailService>>();
            _serviceProvider = serviceProvider;

            _smtpServer = _configuration["SpeakerEmail:SmtpServer"];
            _fromEmail = _configuration["SpeakerEmail:FromEmail"];
        }

        public async Task<bool> SendEmailAsync(SendEmailDto emailDto)
        {
            try
            {
                SmtpClient client = new SmtpClient(_smtpServer)
                {
                    Timeout = 60 * 1000,
                    UseDefaultCredentials = true
                };
                MailMessage mailMsg = new MailMessage();
                MailAddress from = new MailAddress(emailDto.FromEmail, emailDto.FromName, Encoding.UTF8);
                mailMsg.From = from;
                mailMsg.Subject = emailDto.Subject;
                mailMsg.SubjectEncoding = Encoding.UTF8;
                mailMsg.Body = emailDto.Body;
                mailMsg.IsBodyHtml = emailDto.IsBodyHtml;
                mailMsg.BodyEncoding = Encoding.UTF8;
                
                emailDto.Tos?.ForEach(x =>
                {
                    if (!string.IsNullOrEmpty(x))
                        mailMsg.To.Add(new MailAddress(x));
                });

                emailDto.Ccs?.ForEach(x =>
                {
                    if (!string.IsNullOrEmpty(x))
                        mailMsg.CC.Add(new MailAddress(x));
                });
                emailDto.Attachments?.ForEach(v =>
                {
                    MemoryStream stream = new MemoryStream(v.AttachmentStream);
                    mailMsg.Attachments.Add(new Attachment(stream, v.AttachmentName));
                });
                //emailDto.Files?.ForEach(async a => {
                //    var attachmentService = LazyServiceProvider.LazyGetService<IAttachmentService>();
                //    var attachment = await attachmentService.Download(a.Key);
                //    mailMsg.Attachments.Add(new Attachment(attachment.Value.Content, a.Value));
                //});
                await client.SendMailAsync(mailMsg);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"SendEmail() Exception : {ex.Message}");
                return false;
            }
        }
        /// <summary>
        /// 发送邮件附件
        /// </summary>
        /// <param name="emailDto"></param>
        /// <returns></returns>
        public async Task<MessageResult> SendEmailWithAttachment(SendEmailDto emailDto)
        {
            try
            {
                SmtpClient client = new(_smtpServer)
                {
                    Timeout = 60 * 1000,
                    UseDefaultCredentials = true
                };
                string pattern = @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$";
                Regex regex = new(pattern);
                MailMessage mailMsg = new();
                if (!regex.IsMatch(emailDto.FromEmail)) throw new Exception($"请输入有效的电子邮件地址");
                MailAddress from = new(emailDto.FromEmail, emailDto.FromName, Encoding.UTF8);
                mailMsg.From = from;
                mailMsg.Subject = emailDto.Subject;
                mailMsg.SubjectEncoding = Encoding.UTF8;
                mailMsg.Body = emailDto.Body;
                mailMsg.IsBodyHtml = emailDto.IsBodyHtml;
                mailMsg.BodyEncoding = Encoding.UTF8;

                emailDto.Tos.ForEach(x =>
                {
                    if (regex.IsMatch(x))
                        mailMsg.To.Add(new MailAddress(x));
                    else
                        throw new Exception($"请输入有效的电子邮件地址，多个请用英文分号\";\"分隔");
                });

                emailDto.Ccs.ForEach(x =>
                {
                    if (regex.IsMatch(x))
                        mailMsg.CC.Add(new MailAddress(x));
                    else throw new Exception($"请输入有效的电子邮件地址，多个请用英文分号\";\"分隔");
                });
                emailDto.Attachments.ForEach(v =>
                {
                    MemoryStream stream = new MemoryStream(v.AttachmentStream);
                    mailMsg.Attachments.Add(new Attachment(stream, v.AttachmentName));
                    
                });
                await client.SendMailAsync(mailMsg);
                return MessageResult.SuccessResult("发送成功");
            }
            catch (Exception ex)
            {
                return MessageResult.FailureResult(ex.Message);
            }
        }

        /// <summary>
        /// 新增邮件发送记录
        /// </summary>
        /// <param name="sendEmaillRecords"></param>
        /// <returns></returns>
        public async Task InsertSendEmaillRecordAsync(List<InsertSendEmaillRecordDto> sendEmaillRecords) 
        {
            var sendEmailRepository = LazyServiceProvider.LazyGetService<ISendEmailRecordRepository>();
            if (!sendEmaillRecords.Any())
                return;
            var records = ObjectMapper.Map<List<InsertSendEmaillRecordDto>, List<SendEmailRecord>>(sendEmaillRecords);
            await sendEmailRepository.InsertManyAsync(records,true);
        }

        /// <summary>
        /// Worker 执行邮件发送
        /// </summary>
        /// <returns></returns>
        public async Task SendBPMEmailAsync() 
        {
            try
            {
                var sendEmailRepository = LazyServiceProvider.LazyGetService<ISendEmailRecordRepository>();
                var querySendEmail = await sendEmailRepository.GetQueryableAsync();
                //2、查询未发送的邮件，并执行发送
                bool isDo = true;
                do
                {
                    var emails = querySendEmail.Where(a => a.Status == Enums.SendStatus.Pending).ToList();
                    foreach (var item in emails)
                    {
                        var fromName = item.SourceType == Enums.EmailSourceType.OECIntercept ? "NEXBPM 单据拦截通知" : "NEXBPM";
                        var tos = item.EmailAddress.Split(',').ToList();
                        var htmlBody = item.Content;
                        var subject = item.Subject;
                        switch (item.SourceType)
                        {
                            case Enums.EmailSourceType.ApprovalNotifyApplicant:
                                //审批通知申请人 
                                var htmlContent = await GetAndFellApplicantEmailTemplate(item.Content, subject);
                                if (!htmlContent.Item1)
                                {
                                    item.ErrorMessage = htmlContent.Item3;
                                    item.Status = Enums.SendStatus.Failed;
                                    continue;//出错了 记录并继续下一个邮件
                                }
                                subject = htmlContent.Item2;
                                htmlBody = htmlContent.Item3;
                                break;
                            case Enums.EmailSourceType.ReturnNoticeToApplicant:
                                //PA退回通知申请人
                                var htmlPAContent = await GetReturnNoticeToApplicantEmailTemplate(item.Content, subject);
                                if (!htmlPAContent.Item1)
                                {
                                    item.ErrorMessage = htmlPAContent.Item3;
                                    item.Status = Enums.SendStatus.Failed;
                                    continue;//出错了 记录并继续下一个邮件
                                }
                                subject = htmlPAContent.Item2;
                                htmlBody = htmlPAContent.Item3;
                                break;
                            case Enums.EmailSourceType.VendorExpireNoticeToApplican:
                                //PA供应商失效通知申请人
                                var htmlVendorExpire = await GetVendorExpireNoticeToApplicanEmailTemplate(item.Content, subject);
                                if (!htmlVendorExpire.Item1)
                                {
                                    item.ErrorMessage = htmlVendorExpire.Item3;
                                    item.Status = Enums.SendStatus.Failed;
                                    continue;//出错了 记录并继续下一个邮件
                                }
                                subject = htmlVendorExpire.Item2;
                                htmlBody = htmlVendorExpire.Item3;
                                break;
                            case Enums.EmailSourceType.ApplicationApproved:
                                //申请通过通知申请人
                                var htmlApproved = await GetApprovedNoticeToApplicanEmailTemplate(item.Content, subject);
                                if (!htmlApproved.Item1)
                                {
                                    item.ErrorMessage = htmlApproved.Item3;
                                    item.Status = Enums.SendStatus.Failed;
                                    continue;//出错了 记录并继续下一个邮件
                                }
                                subject = htmlApproved.Item2;
                                htmlBody = htmlApproved.Item3;
                                break;
                            case Enums.EmailSourceType.VeevaPushEmail:
                                //申请通过通知申请人
                                var htmlVeeva = await GetVeevaTimeoutEmailTemplate(item.Content, subject);
                                if (!htmlVeeva.Item1)
                                {
                                    item.ErrorMessage = htmlVeeva.Item3;
                                    item.Status = Enums.SendStatus.Failed;
                                    continue;//出错了 记录并继续下一个邮件
                                }
                                subject = htmlVeeva.Item2;
                                htmlBody = htmlVeeva.Item3;
                                break;
                            case Enums.EmailSourceType.OECIntercept:
                            case Enums.EmailSourceType.HcpCompleteInfo:
                            case Enums.EmailSourceType.NonHcpCompleteInfo:
                            case Enums.EmailSourceType.ApplicationReturned:
                            case Enums.EmailSourceType.ApplyToJoinActivity:
                            case Enums.EmailSourceType.CustomerInfo:
                            case Enums.EmailSourceType.EPDOmAddMeetingFail:
                            case Enums.EmailSourceType.MonitorSyncLog:
                                //暂时不想要特殊处理
                                break;
                        }

                        for (int i = 1; i < 4; i++)//重试最多重试3次
                        {
                            try
                            {
                                await WorkerSendEmailAsync(new SendEmailDto
                                {
                                    Subject = subject,
                                    Body = htmlBody,
                                    FromEmail = _fromEmail,
                                    FromName = fromName,
                                    IsBodyHtml = item.IsBodyHtml,
                                    Tos = item.EmailAddress.Split(',').ToList(),
                                    Ccs = string.IsNullOrWhiteSpace(item.CCAddresses) ? new List<string>() : item.CCAddresses.Split(',').ToList(),
                                });
                            }
                            catch (Exception e)
                            {
                                item.Attempts = item.Attempts + i;
                                item.ErrorMessage = e.Message;
                                item.Status = Enums.SendStatus.Failed;
                                continue;//出错了 接着重试
                            }
                            item.Status = Enums.SendStatus.Success;
                            break; //成功了 跳过重试
                        }
                    }
                    await sendEmailRepository.UpdateManyAsync(emails, true);
                    //3、执行完成后查询还有没有未执行、没有则结束循环
                    var isDoSendEmails = querySendEmail.Where(a => a.Status == Enums.SendStatus.Pending).ToList();
                    if (!isDoSendEmails.Any())
                    {
                        isDo = false;
                    }
                } while (isDo);
            }
            catch (Exception ex)
            {
                _logger.LogError($"SendBPMEmailAsync() Worker Send Email:{ex.Message}");
            }
        }

        #region 私有方法
        /// <summary>
        /// 邮件发送
        /// </summary>
        /// <param name="emailDto"></param>
        /// <returns></returns>
        private async Task WorkerSendEmailAsync(SendEmailDto emailDto) 
        {
            SmtpClient client = new SmtpClient(_smtpServer)
            {
                Timeout = 60 * 1000,
                UseDefaultCredentials = true
            };

            MailMessage mailMsg = new MailMessage();
            MailAddress from = new MailAddress(emailDto.FromEmail, emailDto.FromName, Encoding.UTF8);
            mailMsg.From = from;
            mailMsg.Subject = emailDto.Subject;
            mailMsg.SubjectEncoding = Encoding.UTF8;
            mailMsg.Body = emailDto.Body;
            mailMsg.IsBodyHtml = emailDto.IsBodyHtml;
            mailMsg.BodyEncoding = Encoding.UTF8;

            emailDto.Tos.ForEach(x =>
            {
                if (!string.IsNullOrEmpty(x))
                    mailMsg.To.Add(new MailAddress(x));
            });

            emailDto.Ccs.ForEach(x =>
            {
                if (!string.IsNullOrEmpty(x))
                    mailMsg.CC.Add(new MailAddress(x));
            });
            await client.SendMailAsync(mailMsg);
        }

        /// <summary>
        /// 获取且填充通知申请人邮件模板
        /// </summary>
        /// <param name="notificationContent"></param>
        /// <returns></returns>
        private async Task<(bool,string,string)> GetAndFellApplicantEmailTemplate(string notificationContent,string subject) 
        {
            string bodyHtml = "";
            try
            {
                var notification = JsonSerializer.Deserialize<NotificationApplicantEmailDto>(notificationContent);
                var webRoot = LazyServiceProvider.LazyGetService<IWebHostEnvironment>().WebRootFileProvider;
                bodyHtml = await webRoot.GetFileInfo("Templates/Email/ApprovalNotificationApplicant.html").ReadAsStringAsync();
                bodyHtml = bodyHtml.Replace("{WorkflowTypeName}", notification.WorkflowTypeName);
                bodyHtml = bodyHtml.Replace("{ProcessType}", notification.ProcessType);
                bodyHtml = bodyHtml.Replace("{UserName}", notification.UserName);
                bodyHtml = bodyHtml.Replace("{ApplicationCode}", notification.ApplicationCode);
                bodyHtml = bodyHtml.Replace("{ApplicationLink}", notification.ApplicationLink);
                subject = subject.Replace("{WorkflowTypeName}", notification.WorkflowTypeName);
                subject = subject.Replace("{ProcessType}", notification.ProcessType);
            }
            catch (Exception ex)
            {
                return (false, "",ex.Message);
            }
            return (true, subject, bodyHtml);
        }
        
        /// <summary>
        /// 获取且填充PA退回通知申请人邮件模板
        /// </summary>
        /// <param name="notificationContent"></param>
        /// <returns></returns>
        private async Task<(bool, string, string)> GetReturnNoticeToApplicantEmailTemplate(string notificationContent, string subject = null)
        {
            string bodyHtml = "";
            try
            {
                var notification = JsonSerializer.Deserialize<ReturnNoticeToApplicantEmailDto>(notificationContent);
                var webRoot = LazyServiceProvider.LazyGetService<IWebHostEnvironment>().WebRootFileProvider;
                bodyHtml = await webRoot.GetFileInfo("Templates/Email/ReturnNoticeToApplicant.html").ReadAsStringAsync();
                bodyHtml = bodyHtml.Replace("{ApplicationCode}", notification.ApplicationCode);
                bodyHtml = bodyHtml.Replace("{OperationType}", notification.OperationType);
                bodyHtml = bodyHtml.Replace("{UserName}", notification.UserName);
                bodyHtml = bodyHtml.Replace("{ReturnTime}", notification.ReturnTime);
                bodyHtml = bodyHtml.Replace("{ApplicationLink}", notification.ApplicationLink);
                if (!string.IsNullOrWhiteSpace(subject))
                {
                    subject = subject.Replace("{ApplicationCode}", notification.ApplicationCode);
                    subject = subject.Replace("{OperationType}", notification.OperationType);
                }
            }
            catch (Exception ex)
            {
                return (false, "", ex.Message);
            }
            return (true, subject, bodyHtml);
        }

        /// <summary>
        /// 获取且填充PA供应商失效通知申请人邮件模板
        /// </summary>
        /// <param name="notificationContent"></param>
        /// <returns></returns>
        private async Task<(bool, string, string)> GetVendorExpireNoticeToApplicanEmailTemplate(string notificationContent, string subject = null)
        {
            string bodyHtml = "";
            try
            {
                var notification = JsonSerializer.Deserialize<VendorExpireNoticeToApplicanDto>(notificationContent);
                var webRoot = LazyServiceProvider.LazyGetService<IWebHostEnvironment>().WebRootFileProvider;
                bodyHtml = await webRoot.GetFileInfo("Templates/Email/VendorExpireNoticeToApplican.html").ReadAsStringAsync();
                bodyHtml = bodyHtml.Replace("{ApplicationCode}", notification.ApplicationCode);
                bodyHtml = bodyHtml.Replace("{VendorName}", notification.VendorName);
                bodyHtml = bodyHtml.Replace("{UserName}", notification.UserName);
                bodyHtml = bodyHtml.Replace("{Company}", notification.Company);
                bodyHtml = bodyHtml.Replace("{VendorCode}", notification.VendorCode);
                bodyHtml = bodyHtml.Replace("{ApplicationLink}", notification.ApplicationLink);
                if (!string.IsNullOrWhiteSpace(subject))
                {
                    subject = subject.Replace("{ApplicationCode}", notification.ApplicationCode);
                }
            }
            catch (Exception ex)
            {
                return (false, "", ex.Message);
            }
            return (true, subject, bodyHtml);
        }
        /// <summary>
        ///  获取且填充PA供应商审批完成申请人邮件模板
        /// </summary>
        /// <param name="notificationContent"></param>
        /// <param name="subject"></param>
        /// <returns></returns>
        private async Task<(bool, string, string)> GetApprovedNoticeToApplicanEmailTemplate(string notificationContent, string subject = null) 
        {
            string bodyHtml = "";
            try
            {
                var notification = JsonSerializer.Deserialize<ApprovedNoticeToApplicanDto>(notificationContent);
                var webRoot = LazyServiceProvider.LazyGetService<IWebHostEnvironment>().WebRootFileProvider;
                bodyHtml = await webRoot.GetFileInfo("Templates/Email/WorkflowApprovedNotify.html").ReadAsStringAsync();
                bodyHtml = bodyHtml.Replace("{UserName}", notification.UserName);
                bodyHtml = bodyHtml.Replace("{WorkflowTypeName}", notification.WorkflowTypeName);
                bodyHtml = bodyHtml.Replace("{ApplicationCode}", notification.ApplicationCode);
                bodyHtml = bodyHtml.Replace("{CompletedTime}", notification.CompletedTime);
                bodyHtml = bodyHtml.Replace("{ApplicationLink}", notification.ApplicationLink);
                if (!string.IsNullOrWhiteSpace(subject))
                {
                    subject = subject.Replace("{WorkflowTypeName}", notification.WorkflowTypeName);
                    subject = subject.Replace("{ApplicationCode}", notification.ApplicationCode);
                }
            }
            catch (Exception ex)
            {
                return (false, "", ex.Message);
            }
            return (true, subject, bodyHtml);
        }

        /// <summary>
        /// Veeva 推送超时邮件模板
        /// </summary>
        /// <param name="notificationContent"></param>
        /// <param name="subject"></param>
        /// <returns></returns>
        private async Task<(bool, string, string)> GetVeevaTimeoutEmailTemplate(string notificationContent, string subject = null)
        {
            string bodyHtml = "";
            try
            {
                var notification = JsonSerializer.Deserialize<List<string>>(notificationContent);
                var webRoot = LazyServiceProvider.LazyGetService<IWebHostEnvironment>().WebRootFileProvider;
                bodyHtml = await webRoot.GetFileInfo("Templates/Email/VeevaTimeoutEmail.html").ReadAsStringAsync();
                string applicationCodes = "";
                foreach (var item in notification) 
                {
                    applicationCodes += $"<div>{item}</div>";
                }
                bodyHtml = bodyHtml.Replace("{ApplicationCodes}", applicationCodes);

                if (!string.IsNullOrWhiteSpace(subject))
                {

                }
            }
            catch (Exception ex)
            {
                return (false, "", ex.Message);
            }
            return (true, subject, bodyHtml);
        }
        #endregion
    }
}