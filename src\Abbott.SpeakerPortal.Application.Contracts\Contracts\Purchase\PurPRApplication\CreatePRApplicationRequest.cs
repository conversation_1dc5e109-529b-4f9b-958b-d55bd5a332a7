﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;
using System.Text.Json.Serialization;

using Abbott.SpeakerPortal.Contracts.Common.Attachment;
using Abbott.SpeakerPortal.Dataverse;

using static Abbott.SpeakerPortal.Enums.Purchase;

namespace Abbott.SpeakerPortal.Contracts.Purchase.PurPRApplication
{
    public class CreatePRApplicationRequest
    {
        /// <summary>
        /// 申请人部门Id
        /// </summary>
        public Guid ApplyUserDept { get; set; }

        /// <summary>
        /// 申请人部门名称
        /// </summary>
        [MaxLength(500)]
        public string ApplyUserDeptName { get; set; }

        /// <summary>
        /// 成本中心
        /// </summary>
        public Guid? CostCenter { get; set; }

        /// <summary>
        /// 预算Id
        /// </summary>
        public Guid? BudgetId { get; set; }

        /// <summary>
        /// 会议主题-EPD
        /// </summary>
        [MaxLength(100)]
        public string MeetingTitle { get; set; }

        /// <summary>
        /// 会议编号-MND
        /// </summary>
        [MaxLength(50)]
        public string MeetingNo { get; set; }

        /// <summary>
        /// 是否使用电子签章
        /// </summary>
        public bool? IsEsignUsed { get; set; }

        /// <summary>
        /// 推送对象，如使用电子签章系统，此字段保存为“雅培会议（online meeting）”
        /// </summary>
        [MaxLength(50)]
        public string PushSystem { get; set; }

        /// <summary>
        /// 活动编号
        /// </summary>
        [MaxLength(50)]
        public string ActiveNo { get; set; }

        /// <summary>
        /// 被代理人Id
        /// </summary>
        public Guid? AgentId { get; set; }

        /// <summary>
        /// 主持人Id
        /// </summary>
        public Guid? HostVendorId { get; set; }

        /// <summary>
        /// 消费大类
        /// </summary>
        public Guid? ExpenseType { get; set; }

        /// <summary>
        /// 大区
        /// </summary>
        public Guid? BudgetRegion { get; set; }

        /// <summary>
        /// 产品Ids
        /// </summary>
        public IEnumerable<string> ProductIds { get; set; }

        /// <summary>
        /// 公司
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// 币种
        /// </summary>
        [MaxLength(50)]
        public string Currency { get; set; }

        /// <summary>
        /// 货币符号
        /// </summary>
        [MaxLength(50)]
        public string CurrencySymbol { get; set; }

        /// <summary>
        /// 计划汇率
        /// </summary>
        public float PlanRate { get; set; }

        /// <summary>
        /// 预计浮动汇率
        /// </summary>
        public float ExpectedFloatRate { get; set; }

        /// <summary>
        /// 汇率
        /// </summary>
        public float Rate { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [MaxLength(500)]
        public string Remark { get; set; }

        /// <summary>
        /// 说明
        /// </summary>
        [MaxLength(500)]
        public string PrLateDescription { get; set; }

        /// <summary>
        /// 活动发起城市
        /// </summary>
        public string ActiveLaunchCity { get; set; }

        /// <summary>
        /// 活动举行城市
        /// </summary>
        public string ActiveHostCity { get; set; }

        /// <summary>
        /// 活动举行地点
        /// </summary>
        [MaxLength(100)]
        public string AcitveHostAddress { get; set; }

        /// <summary>
        /// 活动日期
        /// </summary>
        public DateTime? AcitveDate { get; set; }

        /// <summary>
        /// 活动类型
        /// </summary>
        public string ActiveType { get; set; }

        /// <summary>
        /// 项目类型
        /// </summary>
        public string ProjectType { get; set; }

        /// <summary>
        /// 活动覆盖医院
        /// </summary>
        public IEnumerable<Guid> Hospitals { get; set; }

        /// <summary>
        /// 活动覆盖医生人数
        /// </summary>
        public string DoctorsNum { get; set; }

        /// <summary>
        /// 覆盖科室
        /// </summary>
        public IEnumerable<Guid> HospitalDepartments { get; set; }

        /// <summary>
        /// 会议类型
        /// </summary>
        [MaxLength(50)]
        public string MeetingType { get; set; }

        /// <summary>
        /// 系列会类型
        /// </summary>
        [MaxLength(50)]
        public string SerialMeetingType { get; set; }

        /// <summary>
        /// 主会场PR（历史单据Id）
        /// </summary>
        public Guid? MainMeetingPR { get; set; }

        /// <summary>
        /// 会议主办方性质
        /// </summary>
        [MaxLength(50)]
        public string OrganizerNature { get; set; }

        /// <summary>
        /// OEC例外审批编号
        /// </summary>
        [MaxLength(50)]
        public string OecExceptionNumber { get; set; }

        /// <summary>
        /// 赞助类型
        /// </summary>
        [MaxLength(50)]
        public string SponsorshipType { get; set; }

        /// <summary>
        /// 活动负责人
        /// </summary>
        [MaxLength(50)]
        public string ActiveLeader { get; set; }

        /// <summary>
        /// 会议名称
        /// </summary>
        [MaxLength(100)]
        public string MeetingName { get; set; }

        /// <summary>
        /// 会议主办方名称
        /// </summary>
        [MaxLength(100)]
        public string OrganizerName { get; set; }

        /// <summary>
        /// 会议时间
        /// </summary>
        public DateTime? MeetingDate { get; set; }

        /// <summary>
        /// 会议具体召开地点
        /// </summary>
        public string MeetingLocation { get; set; }

        /// <summary>
        /// 专业人士人数
        /// </summary>
        public string NumberOfProfessionals { get; set; }

        /// <summary>
        /// 选择理由
        /// </summary>
        public bool ChoiceReason { get; set; }

        /// <summary>
        /// 是否包含HCP差旅、住宿费用
        /// </summary>
        public bool? IsIncludeHcpTravelLodgingFee { get; set; }

        /// <summary>
        /// 支持此活动的目的
        /// </summary>
        [MaxLength(500)]
        public string SupportReason { get; set; }

        /// <summary>
        /// 对文章视频需求的原因
        /// </summary>
        [MaxLength(500)]
        public string RequireMediaReason { get; set; }

        /// <summary>
        /// 参会人士专业领域
        /// </summary>
        [MaxLength(500)]
        public string AttendeeExpertise { get; set; }

        /// <summary>
        /// 计划的市场调研主导方
        /// </summary>
        [MaxLength(50)]
        public string MarketResearchLeader { get; set; }

        /// <summary>
        /// 第三方市场调研公司名称
        /// </summary>
        [MaxLength(100)]
        public string MarketResearchCompany { get; set; }

        /// <summary>
        /// 开展该项目的利益和目标/目的
        /// </summary>
        [MaxLength(500)]
        public string ProjectBenefitAndObjective { get; set; }

        /// <summary>
        /// 计划的项目细节和交付成果
        /// </summary>
        [MaxLength(500)]
        public string PlanProjectDetailAndResult { get; set; }

        /// <summary>
        /// 项目信息成果的用途描述
        /// </summary>
        [MaxLength(500)]
        public string ProjectDetailAndResultUsedFor { get; set; }

        /// <summary>
        /// 是否支持HCP讲课费
        /// </summary>
        public bool? IsSupportHcpTeachFee { get; set; }

        /// <summary>
        /// 开展市场调研的理由和目的
        /// </summary>
        [MaxLength(500)]
        public string MarketResearchReason { get; set; }

        /// <summary>
        /// 计划的调研细节和交付成果描述
        /// </summary>
        [MaxLength(500)]
        public string MarketResearchResult { get; set; }

        /// <summary>
        /// 本次调研的成果的用途描述
        /// </summary>
        [MaxLength(500)]
        public string MarketResearchResultUsedFor { get; set; }

        /// <summary>
        /// 获得患者信息描述
        /// </summary>
        [MaxLength(500)]
        public string PatientInfo { get; set; }

        /// <summary>
        /// 服务提供者的选择标准及计酬标准描述
        /// </summary>
        [MaxLength(500)]
        public string SPChoiceAndPayStandard { get; set; }

        /// <summary>
        /// 是否系列会
        /// </summary>
        public bool? IsSerialMeeting { get; set; }

        /// <summary>
        /// 忽略会议编号重复
        /// </summary>
        public bool IgnoreActiveNumberDuplication { get; set; }

        /// <summary>
        /// 产品分摊明细
        /// </summary>
        public IEnumerable<CreateUpdatePRApplicationProductApportionmentRequest> ProductApportionments { get; set; }

        /// <summary>
        /// 支持文件Ids，多个附件用逗号分隔
        /// </summary>
        public IEnumerable<UploadFileResponseDto> SupportFiles { get; set; }

        /// <summary>
        /// 审批邮件附件
        /// </summary>
        public IEnumerable<UploadFileResponseDto> ApprovalEmails { get; set; }

        /// <summary>
        /// 采购明细
        /// </summary>
        public IEnumerable<CreateUpdatePRApplicationDetailRequest> DetailItems { get; set; }

        /// <summary>
        /// 会议支持的预计费用项
        /// </summary>
        public IEnumerable<CreateUpdatePRApplicationCostItemRequest> CostItems { get; set; }

        /// <summary>
        /// Hcp旅行社会务费
        /// </summary>
        public IEnumerable<CreateUpdateHcpTravelAgencyConferenceFeeRequest> HcpTravelAgencyConferenceFees { get; set; }
    }

    public class CreateUpdatePRApplicationDetailRequest
    {
        public Guid? Id { get; set; }

        /// <summary>
        /// 付款方式
        /// </summary>
        public PayMethods? PayMethod { get; set; }

        /// <summary>
        /// 预估日期
        /// </summary>
        public DateTime? EstimateDate { get; set; }

        /// <summary>
        /// 供应商Id
        /// </summary>
        public Guid? VendorId { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string VendorIdName { get; set; }

        /// <summary>
        /// 供应商名称
        /// </summary>
        public string VendorName { get => VendorIdName; }

        /// <summary>
        /// 费用性质
        /// </summary>
        public Guid? CostNature { get; set; }

        /// <summary>
        /// 城市主数据Id
        /// </summary>
        public Guid? CityId { get; set; }

        /// <summary>
        /// 内容
        /// </summary>
        [MaxLength(200)]
        public string Content { get; set; }

        /// <summary>
        /// 数量
        /// </summary>
        public int? Quantity { get; set; }

        /// <summary>
        /// 单位
        /// </summary>
        [MaxLength(50)]
        public string Unit { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        public decimal? UnitPrice { get; set; }

        /// <summary>
        /// 总金额
        /// </summary>
        public decimal? TotalAmount { get; set; }

        /// <summary>
        /// 总金额（RMB）
        /// </summary>
        public decimal? TotalAmountRMB { get; set; }

        /// <summary>
        /// 税率
        /// </summary>
        [MaxLength(50)]
        public string TaxRate { get; set; }

        /// <summary>
        /// 税额
        /// </summary>
        public decimal? TaxAmount { get; set; }

        /// <summary>
        /// 产品
        /// </summary>
        public Guid? ProductId { get; set; }

        /// <summary>
        /// 产品分摊明细
        /// </summary>
        public IEnumerable<CreateUpdatePRApplicationProductApportionmentRequest> ProductApportionments { get; set; }

        /// <summary>
        /// 讲者类型
        /// </summary>
        [MaxLength(50)]
        public string VendorType { get; set; }

        /// <summary>
        /// 学会名称
        /// </summary>
        [MaxLength(50)]
        public string AcademyName { get; set; }

        /// <summary>
        /// 学会任职
        /// </summary>
        [MaxLength(50)]
        public string AcademyJob { get; set; }

        /// <summary>
        /// 幻灯片类型
        /// </summary>
        [MaxLength(50)]
        public string SlideType { get; set; }

        /// <summary>
        /// 幻灯片名称
        /// </summary>
        [MaxLength(100)]
        public string SlideName { get; set; }

        /// <summary>
        /// 幻灯片A类名称
        /// </summary>
        [MaxLength(100)]
        public string SlideAName { get; set; }

        /// <summary>
        /// 服务市场（分钟）/次数
        /// </summary>
        public int? ServiceDuration { get; set; }

        /// <summary>
        /// 备选讲者
        /// </summary>
        public IEnumerable<CreateUpdatePRApplicationBackupVendor> BackUpVendors { get; set; }

        /// <summary>
        /// 原始备选讲者
        /// </summary>
        [JsonIgnore]
        public IEnumerable<CreateUpdatePRApplicationBackupVendor> OriginalBackUpVendors { get; set; }

        /// <summary>
        /// 执行人
        /// </summary>
        public Guid? Executor { get; set; }

        /// <summary>
        /// REC编号
        /// </summary>
        [MaxLength(50)]
        public string RceNo { get; set; }

        /// <summary>
        /// ICB金额
        /// </summary>
        public decimal? IcbAmount { get; set; }

        /// <summary>
        /// 对冲行的Id
        /// </summary>
        public Guid? HedgePrDetailId { get; set; }

        /// <summary>
        /// 例外审批编号
        /// </summary>
        public string ExceptionNumber { get; set; }
    }

    public class CreateUpdatePRApplicationProductApportionmentRequest
    {
        /// <summary>
        /// 产品Id
        /// </summary>
        public Guid ProductId { get; set; }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 比例
        /// </summary>
        public float Ratio { get; set; }
    }

    public class CreateUpdatePRApplicationBackupVendor
    {
        /// <summary>
        /// 讲者Id
        /// </summary>
        public Guid VendorId { get; set; }

        /// <summary>
        /// 讲者名称
        /// </summary>
        public string VendorIdName { get; set; }

        /// <summary>
        /// Payment term
        /// </summary>
        public string PaymentTerm { get; set; }

        /// <summary>
        /// HCP级别
        /// </summary>
        public string HcpLevelName { get; set; }

        /// <summary>
        /// HCP级别
        /// </summary>
        public string HcpLevelCode { get; set; }

        /// <summary>
        /// 所属医院
        /// </summary>
        public string Hospital { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// XXX(单价:1000,00)
        /// </summary>
        public string Text { get; set; }

        /// <summary>
        /// 例外审批编号
        /// </summary>
        public string ExceptionNumber { get; set; }
    }

    public class CreateUpdatePRApplicationCostItemRequest
    {
        /// <summary>
        /// 支持项目
        /// </summary>
        [MaxLength(50)]
        public string SupportItem { get; set; }

        /// <summary>
        /// 项目类别
        /// </summary>
        [MaxLength(50)]
        public string ItemCategory { get; set; }

        /// <summary>
        /// 预计费用
        /// </summary>
        public decimal EstimateCost { get; set; }
    }

    public class CreateUpdateHcpTravelAgencyConferenceFeeRequest
    {
        /// <summary>
        /// 费用类型
        /// </summary>
        [MaxLength(50)]
        public string HcpTravelAgencyConferenceFeeType { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        public decimal Amount { get; set; }
    }
}
