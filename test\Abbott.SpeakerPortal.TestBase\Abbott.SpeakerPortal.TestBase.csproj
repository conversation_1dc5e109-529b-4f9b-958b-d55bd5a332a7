﻿<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <RootNamespace>Abbott.SpeakerPortal</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.TestBase" Version="8.0.0" />
    <PackageReference Include="Volo.Abp.Autofac" Version="8.0.0" />
    <PackageReference Include="Volo.Abp.Authorization" Version="8.0.0" />
    <ProjectReference Include="..\..\src\Abbott.SpeakerPortal.Domain\Abbott.SpeakerPortal.Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.2.0" />
    <PackageReference Include="NSubstitute" Version="4.3.0" />
    <PackageReference Include="NSubstitute.Analyzers.CSharp" Version="1.0.15">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Shouldly" Version="4.0.3" />
    <PackageReference Include="xunit" Version="2.4.1" />
    <PackageReference Include="xunit.extensibility.execution" Version="2.4.1" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.4.5" />
  </ItemGroup>

</Project>
