﻿using Abbott.SpeakerPortal.Contracts.Report;
using Abbott.SpeakerPortal.Contracts.Report.ProfessionalServiceTax;
using Hangfire;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.BackgroundWorkers.Report
{
    public class PAJoinBpcsGlhWorker : SpeakerPortalBackgroundWorkerBase
    {
        public PAJoinBpcsGlhWorker()
        {
            CronExpression = Cron.Never();
        }

        public override async Task DoWorkAsync(CancellationToken cancellationToken = default)
        {
            //逻辑处理
            var reportService = LazyServiceProvider.LazyGetService<IReportService>();
            await reportService.FillPAJoinBpcsGlhsAsync();
        }
    }
}
