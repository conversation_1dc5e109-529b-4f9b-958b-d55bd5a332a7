#Build and push a given service for all parameters

parameters:
  #Path to Docker Compose file.
  - name: dockerComposeFile
    type: string

jobs:
  - job: build
    displayName: Build and Push to ACR
    steps:
      - task: DockerCompose@0
        displayName: 'Docker build'
        inputs:
          containerregistrytype: 'Azure Container Registry'
          azureSubscription: cd-serviceconnection-s
          azureContainerRegistry: '{"loginServer":"$(acrName).azurecr.cn", "id" : "/subscriptions/$(subscriptionGuid)/resourceGroups/$(acrResourceGroup)/providers/Microsoft.ContainerRegistry/registries/$(acrName)"}'
          dockerComposeFile: ${{ parameters.dockerComposeFile }}
          dockerComposeFileArgs: |
            ENV=$(namespace)
            ACR=$(acrName).azurecr.cn
          action: 'Build services'
          additionalImageTags: 'v$(Build.BuildNumber).$(Build.BuildId)'
          includeLatestTag: true
      - task: DockerCompose@0
        displayName: 'Docker push'
        inputs:
          containerregistrytype: 'Azure Container Registry'
          azureSubscription: cd-serviceconnection-s
          azureContainerRegistry: '{"loginServer":"$(acrName).azurecr.cn", "id" : "/subscriptions/$(subscriptionGuid)/resourceGroups/$(acrResourceGroup)/providers/Microsoft.ContainerRegistry/registries/$(acrName)"}'
          dockerComposeFile: ${{ parameters.dockerComposeFile }}
          dockerComposeFileArgs: |
            ENV=$(namespace)
            ACR=$(acrName).azurecr.cn
          action: 'Push services'
          additionalImageTags: 'v$(Build.BuildNumber).$(Build.BuildId)'
          includeLatestTag: true
      - task: Bash@3
        displayName: 'Clean local docker image'
        inputs:
          targetType: 'inline'
          script: |
            docker system prune -a --force
