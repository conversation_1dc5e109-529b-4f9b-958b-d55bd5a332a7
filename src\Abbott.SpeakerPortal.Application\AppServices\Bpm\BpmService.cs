﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Abbott.SpeakerPortal.BackgroundWorkers.Bpm;
using Abbott.SpeakerPortal.Bpm;
using Abbott.SpeakerPortal.Bpm.Repositories;
using Abbott.SpeakerPortal.Contracts.Bpm;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Entities.VendorApplicationFinancials;
using Abbott.SpeakerPortal.Entities.VendorApplicationPersonals;
using Abbott.SpeakerPortal.Entities.VendorApplications;
using Abbott.SpeakerPortal.Utils;

using EFCore.BulkExtensions;

using Hangfire;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

using Volo.Abp.Domain.Repositories;

namespace Abbott.SpeakerPortal.AppServices.Bpm
{
    public class BpmService : SpeakerPortalAppService, IBpmService
    {
        /// <summary>
        /// 同步讲者数据到Bpm
        /// </summary>
        /// <param name="vendorApplicationId"></param>
        /// <returns></returns>
        public async Task SyncVendorToBpm(Guid vendorApplicationId)
        {
            var queryVendorApplication = await LazyServiceProvider.LazyGetService<IVendorApplicationRepository>().GetQueryableAsync();
            var queryVendorApplicationPersonal = await LazyServiceProvider.LazyGetService<IVendorApplicationPersonalRepository>().GetQueryableAsync();
            var queryVendorApplicationFinancial = await LazyServiceProvider.LazyGetService<IVendorApplicationFinancialRepository>().GetQueryableAsync();

            //获取该申请的讲者及财务信息
            var vendorDatas = queryVendorApplication.Where(a => a.Id == vendorApplicationId)
                .Join(queryVendorApplicationPersonal, a => a.Id, a => a.ApplicationId, (a, b) => new { Application = a, ApplicationPersonal = b })
                .Join(queryVendorApplicationFinancial, a => a.Application.Id, a => a.ApplicationId, (a, b) => new { a.Application, a.ApplicationPersonal, ApplicationFinancial = b })
                .ToArray();

            var vendorApplication = vendorDatas.Select(a => a.Application).FirstOrDefault();
            var vendorApplicationPersonal = vendorDatas.Select(a => a.ApplicationPersonal).FirstOrDefault();
            var vendorAppliationFinancials = vendorDatas.Select(a => a.ApplicationFinancial).ToArray();

            await SyncToVendorTier(vendorApplication, vendorApplicationPersonal, vendorAppliationFinancials);
            await SyncToSupplierAriseCode(vendorApplication, vendorApplicationPersonal, vendorAppliationFinancials);
        }

        /// <summary>
        /// 同步数据到T_Vendor_Tier
        /// </summary>
        /// <param name="vendorApplication"></param>
        /// <param name="vendorApplicationPersonal"></param>
        /// <param name="vendorApplicationFinancials"></param>
        /// <returns></returns>
        async Task SyncToVendorTier(VendorApplication vendorApplication, VendorApplicationPersonal vendorApplicationPersonal, IEnumerable<VendorApplicationFinancial> vendorApplicationFinancials)
        {
            var vendorCodes = vendorApplicationFinancials.Where(a => !string.IsNullOrEmpty(a.VendorCode)).Select(a => a.VendorCode).ToArray();
            if (!vendorCodes.Any())
                return;

            var vendorTierRepository = LazyServiceProvider.LazyGetService<ITVendorTierRepository>();
            var vendorTiers = await vendorTierRepository.GetListAsync(a => vendorCodes.Contains(a.Vendor));
            var key = LazyServiceProvider.LazyGetService<IConfiguration>().GetValue<string>("ApplicationInsightKey");
            var listForAdd = new List<TVendorTier>();
            var listForUpdate = new List<TVendorTier>();
            var cities = await LazyServiceProvider.LazyGetService<IDataverseService>().GetAllCity(vendorApplicationPersonal.City);

            //构建VendorTier对象
            var buildTier = new Func<TVendorTier, VendorApplication, VendorApplicationPersonal, string, TVendorTier>((tier, vendorApplication, vendorApplicationPersonal, key) =>
            {
                tier.Gender = EnumUtil.GetDescription(vendorApplicationPersonal.Sex);
                tier.City = cities?.FirstOrDefault()?.Name;
                tier.Hospital = vendorApplication.HospitalName;
                tier.HosDept = vendorApplication.HosDepartment;
                tier.Title = vendorApplication.PTName;
                tier.LicenseNo = vendorApplication.CertificateCode;
                tier.Vndmemo02 = "无";

                var cardNo = AesHelper.Decryption(vendorApplicationPersonal.CardNo, key);
                if (vendorApplicationPersonal.Sex.HasValue)
                    tier.Vndmemo07 = $"{cardNo} {(vendorApplicationPersonal.Sex == Enums.Gender.Male ? "M" : "F")}";
                else
                    tier.Vndmemo07 = cardNo;
                tier.Vndmemo08 = vendorApplication.ApplicationCode;
                tier.Tier = vendorApplication.SPLevel;

                return tier;
            });

            foreach (var item in vendorCodes)
            {
                var existDatas = vendorTiers.Where(a => a.Vendor == item && a.Vndnam == vendorApplicationPersonal.SPName);
                //存在数据则修改
                if (existDatas.Any())
                {
                    foreach (var data in existDatas)
                    {
                        var tier = buildTier(data, vendorApplication, vendorApplicationPersonal, key);
                        tier.UpdDatetime = DateTime.Now;
                        listForUpdate.Add(tier);
                    }
                }
                else//新增
                {
                    var tier = new TVendorTier { Vendor = item, Vndnam = vendorApplicationPersonal.SPName };
                    tier = buildTier(tier, vendorApplication, vendorApplicationPersonal, key);
                    tier.InsDatetime = DateTime.Now;
                    listForAdd.Add(tier);
                }
            }

            var context = await vendorTierRepository.GetDbContextAsync();
            if (listForAdd.Count > 0)
                await context.BulkInsertAsync(listForAdd);
            if (listForUpdate.Count > 0)
                await context.BulkUpdateAsync(listForUpdate, options =>
                {
                    options.UpdateByProperties = [nameof(TVendorTier.Vendor), nameof(TVendorTier.Vndnam)];
                });
        }

        /// <summary>
        /// 同步数据到T_SupplierAriseCode
        /// </summary>
        /// <param name="vendorApplication"></param>
        /// <param name="vendorApplicationPersonal"></param>
        /// <param name="vendorApplicationFinancials"></param>
        /// <returns></returns>
        async Task SyncToSupplierAriseCode(VendorApplication vendorApplication, VendorApplicationPersonal vendorApplicationPersonal, IEnumerable<VendorApplicationFinancial> vendorApplicationFinancials)
        {
            var vendorCodes = vendorApplicationFinancials.Where(a => !string.IsNullOrEmpty(a.VendorCode)).Select(a => a.VendorCode).ToArray();
            if (!vendorCodes.Any())
                return;

            var supplierAriseCodeRepository = LazyServiceProvider.LazyGetService<ITSupplierAriseCodeRepository>();
            var supplierAriseCodes = await supplierAriseCodeRepository.GetListAsync(a => vendorCodes.Contains(a.SupplierNo));

            var listForAdd = new List<TSupplierAriseCode>();
            var listForUpdate = new List<TSupplierAriseCode>();

            foreach (var item in vendorCodes)
            {
                var financial = vendorApplicationFinancials.FirstOrDefault(a => a.VendorCode == item);
                var existDatas = supplierAriseCodes.Where(a => a.CompanyCode == financial?.Company && a.SupplierNo == item && a.SupplierName == vendorApplicationPersonal.SPName);
                //存在数据则修改
                if (existDatas.Any())
                {
                    foreach (var data in existDatas)
                    {
                        data.AriseCode = vendorApplication.EpdId;
                        listForUpdate.Add(data);
                    }
                }
                else//新增
                {
                    var data = new TSupplierAriseCode();
                    data.SetId(Guid.NewGuid().ToString("N"));
                    data.CompanyCode = financial.Company;
                    data.SupplierNo = item;
                    data.SupplierName = vendorApplicationPersonal.SPName;
                    data.AriseCode = vendorApplication.EpdId;
                    listForAdd.Add(data);
                }
            }

            var context = await supplierAriseCodeRepository.GetDbContextAsync();
            //新增
            if (listForAdd.Count > 0)
            {
                var querySupplierAriseCode = await supplierAriseCodeRepository.GetQueryableAsync();
                foreach (var item in listForAdd)
                {
                    var minProcInstId = await context.Database.SqlQueryRaw<int>("SELECT MIN(CAST(ProcInstId AS INT)) Value FROM T_SupplierAriseCode").FirstAsync();
                    item.ProcInstId = (--minProcInstId).ToString();
                    await supplierAriseCodeRepository.InsertAsync(item, true);
                }
            }

            //修改
            if (listForUpdate.Count > 0)
            {
                await context.BulkUpdateAsync(listForUpdate, options =>
                {
                    options.UpdateByProperties = [nameof(TSupplierAriseCode.Id)];
                });
            }
        }
    }
}
