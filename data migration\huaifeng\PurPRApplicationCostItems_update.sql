SELECT
 TRY_CONVERT(UNIQUEIDENTIFIER, [Id]) [Id]
,TRY_CONVERT(UNIQUEIDENTIFIER, [PRApplicationId]) [PRApplicationId]
,[SupportItem]
,0 AS [EstimateCost]
,'{}' AS [ExtraProperties]
,'NULL' AS [ConcurrencyStamp]
,GETDATE() AS [CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CreatorId]) [CreatorId]
,[LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [LastModifierId]) [LastModifierId]
,0 AS [IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, [DeleterId]) [DeleterId]
,[DeletionTime]
,[ItemCategory]
INTO #PurPRApplicationCostItems
FROM PLATFORM_ABBOTT_STG.dbo.PurPRApplicationCostItems

USE Speaker_Portal_STG;

UPDATE a
SET 
 a.[Id] = b.[Id]
,a.[PRApplicationId] = b.[PRApplicationId]
,a.[SupportItem] = b.[SupportItem]
,a.[EstimateCost] = b.[EstimateCost]
,a.[ExtraProperties] = b.[ExtraProperties]
,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = b.[CreatorId]
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = b.[LastModifierId]
,a.[IsDeleted] = b.[IsDeleted]
,a.[DeleterId] = b.[DeleterId]
,a.[DeletionTime] = b.[DeletionTime]
,a.[ItemCategory] = b.[ItemCategory]
FROM dbo.PurPRApplicationCostItems a
left join #PurPRApplicationCostItems  b
ON a.id=b.id


--select * from #PurPRApplicationCostItems where EstimateCost is null




INSERT INTO dbo.PurPRApplicationCostItems
(
 [Id]
,[PRApplicationId]
,[SupportItem]
,[EstimateCost]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[ItemCategory]
)
SELECT
 [Id]
,[PRApplicationId]
,[SupportItem]
,[EstimateCost]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[ItemCategory]
FROM #PurPRApplicationCostItems a
WHERE not exists (select * from dbo.PurPRApplicationCostItems where id=a.id)

--truncate table dbo.PurPRApplicationCostItems

--alter table dbo.PurPRApplicationCostItems
--alter column [SupportItem] [nvarchar](100) NULL
--
--alter table dbo.PurPRApplicationCostItems
--alter column [ItemCategory] [nvarchar](100) NULL