SELECT
 [PAApplicationCode]
,[RefNo]
,[Status]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([Id],'00000000-0000-0000-0000-000000000000')) [Id]
INTO #PurPAApplicationRefs
FROM (SELECT *,ROW_NUMBER() OVER(PARTITION BY Id ORDER BY Id) RK FROM PLATFORM_ABBOTT_STG.dbo.PurPAApplicationRefs)a
WHERE RK = 1

;
--drop table #PurPAApplicationRefs

USE Speaker_Portal_STG;

UPDATE a 
SET 
 a.[PAApplicationCode]  = b.[PAApplicationCode] 
,a.[RefNo] = b.[RefNo]
,a.[Status] = b.[Status]
,a.[Id] = b.[Id]
FROM dbo.PurPAApplicationRefs a
left join #PurPAApplicationRefs  b
ON a.id=b.id;


INSERT INTO dbo.PurPAApplicationRefs
(
 [PAApplicationCode] 
,[RefNo]
,[Status]
,[Id]
)
SELECT
 [PAApplicationCode] 
,[RefNo]
,[Status]
,[Id]
FROM #PurPAApplicationRefs a
WHERE not exists (select * from dbo.PurPAApplicationRefs where id=a.id);


--truncate table dbo.PurPAApplicationRefs