CREATE PROCEDURE dbo.sp_VendorPersonals_ns
AS 
begin
	
select 
a.Id,
a<PERSON>,
a.<PERSON>,
sd.spk_code CardType,
a<PERSON>,
b.<PERSON>d as <PERSON><PERSON><PERSON>,
COALESCE(cast(d2.spk_provincialadministrativecode as varchar(10)),cast(d3.spk_provincialadministrativecode as varchar(10)),cast(d.spk_provincialadministrativecode as varchar(10)),cast(d1.spk_provincialadministrativecode as varchar(10)),REPLICATE('0', 6)) as Province,
COALESCE(cast(c.spk_cityadministrativedivisioncode as varchar(10)),cast(c1.spk_cityadministrativedivisioncode as varchar(10)),REPLICATE('0', 6)) as City,
a.Address,
a.PostCode,
a.ExtraProperties,
a.ConcurrencyStamp,
CONVERT(DATETIME, LEFT(a.CreationTime, 8) + ' ' + SUBSTRING(a.CreationTime, 9, 2) + ':' + SUBSTRING(a.CreationTime, 11, 2) + ':' + SUBSTRING(a.CreationTime, 13, 2), 120) CreationTime,
g.spk_NexBPMCode as CreatorId,
case when len(a.LastModificationTime)=14 then CONVERT(DATETIME, LEFT(a.LastModificationTime, 8) + ' ' + SUBSTRING(a.LastModificationTime, 9, 2) + ':' + SUBSTRING(a.LastModificationTime, 11, 2) + ':' + SUBSTRING(a.LastModificationTime, 13, 2), 120) 
else a.LastModificationTime end as LastModificationTime,
e1.spk_NexBPMCode as LastModifierId,
a.IsDeleted,
a.DeleterId,
a.DeletionTime,
a.SPName,
a.AffiliationOrgan,
a.Email 
into #VendorPersonals
from PLATFORM_ABBOTT.dbo.VendorPersonals_tmp a 
left join PLATFORM_ABBOTT.dbo.Attachments_tmp b
on a.CardPic COLLATE SQL_Latin1_General_CP1_CI_AS = b.BPMId COLLATE SQL_Latin1_General_CP1_CI_AS
left join PLATFORM_ABBOTT.dbo.spk_city c
on SUBSTRING(a.City,1,3) =SUBSTRING(c.spk_name,1,3) or a.City=c.spk_name
left join PLATFORM_ABBOTT.dbo.spk_city c1
on SUBSTRING(a.City,1,2) =SUBSTRING(c1.spk_name,1,2) and c.spk_provincenamename is null  and SUBSTRING(c.spk_name,1,3)<>N'张家港'
left join PLATFORM_ABBOTT.dbo.spk_province d
on c.spk_provincenamename = d.spk_name 
left join PLATFORM_ABBOTT.dbo.spk_province d1
on c1.spk_provincenamename = d1.spk_name 
left join PLATFORM_ABBOTT.dbo.spk_province d2
on SUBSTRING(a.Province,1,3) =SUBSTRING(d2.spk_name,1,3) or a.Province=d2.spk_name
left join PLATFORM_ABBOTT.dbo.spk_province d3
on SUBSTRING(a.Province,1,2) =SUBSTRING(d3.spk_name,1,2) and d3.spk_provincecode is null
left join PLATFORM_ABBOTT.dbo.spk_staffmasterdata g
on a.CreatorId COLLATE SQL_Latin1_General_CP1_CI_AS = g.bpm_id
left join spk_dictionary sd 
on a.CardType=sd.spk_Name and spk_type=N'证件类型'
left join PLATFORM_ABBOTT.dbo.spk_staffmasterdata e1
on a.LastModifierId = e1.bpm_id;

--删除表
 IF OBJECT_ID(N'PLATFORM_ABBOTT.dbo.VendorPersonals ', N'U') IS NOT NULL
 BEGIN
		drop table PLATFORM_ABBOTT.dbo.VendorPersonals
		select *
        into PLATFORM_ABBOTT.dbo.VendorPersonals from #VendorPersonals
 PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
 END
 ELSE
 BEGIN
--落成实体表
 select *
    into PLATFORM_ABBOTT.dbo.VendorPersonals from #VendorPersonals
 -- select * from #vendor_tbl
 PRINT(N'落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
 END
end;