﻿using Abbott.SpeakerPortal.Contracts.Integration.Veeva;
using Abbott.SpeakerPortal.Dataverse;
using Hangfire;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.BackgroundWorkers.Hangfire;
using Abbott.SpeakerPortal.Extension;
using Abbott.SpeakerPortal.Contracts.Common;

namespace Abbott.SpeakerPortal.BackgroundWorkers.Integration.Veeva
{
    /// <summary>
    /// 年度讲者信息更新推送
    /// </summary>
    public class AnnualPushSpeakerWorker: SpeakerPortalBackgroundWorkerBase
    {
        private IInteVeevaBatchService _inteVeevaBatchService;        
        private IServiceProvider _serviceProvider;
        private IScheduleJobLogService _jobLogService;

        public AnnualPushSpeakerWorker(IServiceProvider serviceProvider)
        {
            _serviceProvider= serviceProvider;
            _inteVeevaBatchService = serviceProvider.GetService<IInteVeevaBatchService>();
            _jobLogService = serviceProvider.GetService<IScheduleJobLogService>();
            //触发周期
            CronExpression = Cron.Daily(2);
        }

        public override async Task DoWorkAsync(CancellationToken cancellationToken = default)
        {
            var log = _jobLogService.InitSyncLog("Veeva_AnnualPushSpeaker");
            try
            {
                var (isExecute, StartDate, endDate) = GetExecuteConfig();
                log.Remark = $"job  execute flag:{isExecute},this flag from pp";
                if (isExecute)
                {
                    //逻辑处理
                    await _inteVeevaBatchService.PushAnnualSpeackerInfo(StartDate, endDate);
                }
            }
            catch (Exception ex)
            {
                log.IsSuccess = false;
                log.Remark = ex.ToString();
            }
            finally
            {
                _jobLogService.SyncLog(log);
            }

        }

        /// <summary>
        /// 根据PP里的flag标记，是否执行
        /// </summary>
        /// <returns></returns>
        private (bool,DateTime,DateTime) GetExecuteConfig()
        {
            var dataverseService = _serviceProvider.GetService<IDataverseService>();
            var configList = dataverseService.GetAllSystemInterationConfig().GetAwaiterResult();
            var flag = configList.Where(c => c.Name == "Job_Veeva_AnnualSpeakerPush_Flag").FirstOrDefault();
            var isExecute = false;
            if (flag == null) isExecute= false;
            isExecute = flag.Value == "1" ? true : false;
            
            if (!isExecute)
            {
                //不执行
                return (isExecute, DateTime.Now, DateTime.Now);
            }
            var startDate = configList.Where(c => c.Name == "Job_Veeva_AnnualSpeakerPush_StartDate").FirstOrDefault();
            var endDate = configList.Where(c => c.Name == "Job_Veeva_AnnualSpeakerPush_EndDate").FirstOrDefault();
            if (startDate == null || endDate == null)
            { 
                //时间未配置，不执行
                isExecute = false;
                return (isExecute, DateTime.Now, DateTime.Now);
            }
            return (isExecute, Convert.ToDateTime(startDate), Convert.ToDateTime(endDate));
        }
    }
}
