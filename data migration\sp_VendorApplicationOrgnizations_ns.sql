CREATE PROCEDURE dbo.sp_VendorApplicationOrgnizations_ns
AS 
BEGIN
	select 
a.[Id]
,a.[ApplicationId]
,a.[VendorName]
,a.[VendorOldName]
,a.[VendorEngName]
,a.[RegCertificateAddress]
,a.[PostCode]
,a.[ContactName]
,a.[ContactPhone]
,a.[ContactEmail]
,a.[WebSite]
,a.[RegisterDate]
,sd.spk_type  as [OrgType]
,a.[IssuingAuthority]
,a.[RegisterCode]
,a.[RegValidityStart]
,a.[RegValidityEnd]
,a.[Province]
,a.[City]
,a.[Legal]
,a.[RegisterAmount]
,a.[BusinessAuthority]
,a.[BusinessScope]
,a.[LastYearSales]
,a.[KeyIndustry]
,a.[KeyClient]
,a.[Staffs]
,a.[Aptitudes]
,a.[ApplyReason]
,a.[ExtraProperties]
,a.[ConcurrencyStamp]
,a.[CreationTime]
,ss.spk_NexBPMCode as [CreatorId]
,a.[LastModificationTime]
,a.[LastModifierId]
,a.[IsDeleted]
,a.[DeleterId]
,a.[DeletionTime]
,a.[Shareholder]
into #VendorApplicationOrgnizations
from VendorApplicationOrgnizations_Tmp a 
left join spk_dictionary sd 
on a.OrgType COLLATE SQL_Latin1_General_CP1_CI_AS=sd.spk_BPMCode 
left join spk_staffmasterdata ss 
on a.CreatorId COLLATE SQL_Latin1_General_CP1_CI_AS=ss.bpm_id 

 IF OBJECT_ID(N'PLATFORM_ABBOTT_Dev.dbo.VendorApplicationOrgnizations ', N'U') IS NOT NULL
	BEGIN
		drop table PLATFORM_ABBOTT_Dev.dbo.VendorApplicationOrgnizations
		select *
        into PLATFORM_ABBOTT_Dev.dbo.VendorApplicationOrgnizations from #VendorApplicationOrgnizations
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into PLATFORM_ABBOTT_Dev.dbo.VendorApplicationOrgnizations from #VendorApplicationOrgnizations
    -- select * from #vendor_tbl
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END

END
