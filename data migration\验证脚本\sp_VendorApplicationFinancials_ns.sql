CREATE PROCEDURE dbo.sp_VendorApplicationFinancials_ns
AS 
BEGIN
	select 
vaft.[Id]
,vaft.[ApplicationId]
,vaft.[Company]
,vaft.[Currency]
,vaft.[VendorCode]
,vaft.[AbbottBank]
,vaft.[VendorType]
,vaft.[Division]
,vaft.[PayType]
,vaft.[CountryCode]
,vaft.[BankType]
,vaft.[DpoCategory]
,vaft.[PaymentTerm]
,vaft.[BankNo]
,vaft.[ExtraProperties]
,vaft.[ConcurrencyStamp]
,vaft.[CreationTime]
,ss.spk_NexBPMCode  as [CreatorId]
,vaft.[LastModificationTime]
,vaft.[LastModifierId]
,vaft.[IsDeleted]
,vaft.[DeleterId]
,vaft.[DeletionTime]
,vaft.[SpendingCategory]
,vaft.[FinancialVendorStatus]
into #VendorApplicationFinancials
from VendorApplicationFinancials_Tmp vaft 
left join spk_staffmasterdata ss 
on vaft.CreatorId COLLATE SQL_Latin1_General_CP1_CI_AS=ss.bpm_id 

 IF OBJECT_ID(N'PLATFORM_ABBOTT.dbo.VendorApplicationFinancials ', N'U') IS NOT NULL
	BEGIN
		drop table PLATFORM_ABBOTT.dbo.VendorApplicationFinancials
		select *
        into PLATFORM_ABBOTT.dbo.VendorApplicationFinancials from #VendorApplicationFinancials
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into PLATFORM_ABBOTT.dbo.VendorApplicationFinancials from #VendorApplicationFinancials
    -- select * from #vendor_tbl
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END

END;