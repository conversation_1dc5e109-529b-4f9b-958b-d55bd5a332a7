﻿using Abbott.SpeakerPortal.BackgroundWorkers;
using Abbott.SpeakerPortal.BackgroundWorkers.BPMEmail;
using Abbott.SpeakerPortal.BackgroundWorkers.Integration.Veeva;
using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Common.Common;
using Abbott.SpeakerPortal.Contracts.Common.PowerApp;
using Abbott.SpeakerPortal.Contracts.Dataverse;
using Abbott.SpeakerPortal.Contracts.OEC.PayStandard;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPOApplication;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.Agent;
using Abbott.SpeakerPortal.Entities.Common.Workflow.CurrentApprovalTask;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Redis;
using Abbott.SpeakerPortal.User;
using Abbott.SpeakerPortal.Utils;

using Hangfire;

using Microsoft.AspNetCore.Hosting;
using Microsoft.Crm.Sdk.Messages;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Logging;

using Newtonsoft.Json;

using StackExchange.Redis;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Mail;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

using Volo.Abp.Domain.Repositories;

namespace Abbott.SpeakerPortal.AppServices.Common
{
    public class PowerappService : SpeakerPortalAppService, IPowerappService
    {
        private readonly ILogger<PowerappService> _logger;
        IRedisRepository _redisRepository;
        IConfiguration _configuration;
        private readonly IWebHostEnvironment _env;
        private readonly IUserService _userService;
        public PowerappService(IServiceProvider serviceProvider, IWebHostEnvironment env, IUserService userService)
        {
            _redisRepository = serviceProvider.GetService<IRedisRepository>(); ;
            _logger = serviceProvider.GetService<ILogger<PowerappService>>();
            _configuration = serviceProvider.GetService<IConfiguration>();
            _env = env;
            _userService = userService;
        }

        public async Task<MessageResult> ReceiveApprovalTask(CurrentApprovalTaskDto request)
        {
            var result = MessageResult.SuccessResult();

            try
            {
                var wfApprovalTask = ObjectMapper.Map<CurrentApprovalTaskDto, WfApprovalTask>(request);

                char prefix = request.FormNo.ToUpper()[0];
                switch (prefix)
                {
                    case 'V':
                        wfApprovalTask.BusinessType = Enums.ResignationTransfer.TaskFormCategory.VendorApplication;
                        break;
                    case 'P':
                        wfApprovalTask.BusinessType = Enums.ResignationTransfer.TaskFormCategory.PurchaseRequestApplication;
                        break;
                    case 'O':
                        wfApprovalTask.BusinessType = Enums.ResignationTransfer.TaskFormCategory.PurhcaseOrderApplication;
                        break;
                    case 'B':
                        wfApprovalTask.BusinessType = Enums.ResignationTransfer.TaskFormCategory.BiddingApplication;
                        break;
                    case 'G':
                        wfApprovalTask.BusinessType = Enums.ResignationTransfer.TaskFormCategory.GoodsReceiptApplication;
                        break;
                    case 'A':
                        wfApprovalTask.BusinessType = Enums.ResignationTransfer.TaskFormCategory.PaymentApplication;
                        break;
                    case 'W':
                        wfApprovalTask.BusinessType = Enums.ResignationTransfer.TaskFormCategory.BiddingWaiverApplication;
                        break;
                    case 'S':
                        wfApprovalTask.BusinessType = Enums.ResignationTransfer.TaskFormCategory.OECSpeakerAuthApplication;
                        break;
                    default:
                        break;
                }

                wfApprovalTask.BusinessTypeName = wfApprovalTask.BusinessType.GetDescription();

                if (request.Approver?.Count > 0)
                    wfApprovalTask.Approver = JsonConvert.SerializeObject(request.Approver);

                var wfApprovalTaskRepository = LazyServiceProvider.LazyGetService<IWfApprovalTaskRepository>();

                await wfApprovalTaskRepository.InsertAsync(wfApprovalTask);

                #region 调度作业
                switch (wfApprovalTask.BusinessType)
                {
                    case Enums.ResignationTransfer.TaskFormCategory.VendorApplication:
                        //讲者新建\讲者变更流程中，Marketing Review(StepNo=200)步骤审批通过之后推送医师信息验证
                        if (request.StepNo == "400" && (request.WorkflowTypeOption == WorkflowTypeName.SpeakerRequest || request.WorkflowTypeOption == WorkflowTypeName.SpeakerChange))
                            BackgroundJob.Enqueue<PushDcrWorker>(a => a.DoWorkAsync(CancellationToken.None));
                        break;
                    case Enums.ResignationTransfer.TaskFormCategory.PurchaseRequestApplication:
                        //记录Pr下一步待处理人
                        result = await LazyServiceProvider.LazyGetService<IPurPRApplicationService>().RecordPrCurrentProcessor(request);
                        break;
                    case Enums.ResignationTransfer.TaskFormCategory.PurhcaseOrderApplication:
                        //记录Po下一步待处理人
                        result = await LazyServiceProvider.LazyGetService<IPurPOApplicationService>().RecordPoCurrentProcessor(request);
                        break;
                    case Enums.ResignationTransfer.TaskFormCategory.BiddingApplication:
                        break;
                    case Enums.ResignationTransfer.TaskFormCategory.GoodsReceiptApplication:
                        break;
                    case Enums.ResignationTransfer.TaskFormCategory.PaymentApplication:
                        //记录Pa下一步待处理人
                        result = await LazyServiceProvider.LazyGetService<IPurPAApplicationService>().RecordPaCurrentProcessor(request);
                        break;
                    case Enums.ResignationTransfer.TaskFormCategory.BiddingWaiverApplication:
                        break;
                    case Enums.ResignationTransfer.TaskFormCategory.JustificationApplication:
                        break;
                    case Enums.ResignationTransfer.TaskFormCategory.OECSpeakerAuthApplication:
                        break;
                    default:
                        break;
                }

                #endregion

                return await Task.FromResult(result);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Receive Powerapp ApprovalTask Exception:{ex.Message} {ex.StackTrace}");
                return await Task.FromResult(MessageResult.FailureResult(new MessageModelBase(500, "Receive Powerapp ApprovalTask Failed!")));
            }
        }

        public async Task<MessageResult> ReceivePowerData(ReceivePowerDataDto request)
        {
            List<HashEntry> hashList = new List<HashEntry>();
            object objData = null;
            var mainKey = string.Empty;
            var subKey = request.Id.ToString();
            try
            {
                #region 解析请求过来的EntityData并组织相应的redis key
                switch (request.EntityName)
                {
                    case DataverseEntitiesConsts.Staff:
                        mainKey = Consts.RedisKey.BaseData_User;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            objData = JsonConvert.DeserializeObject<StaffDto>(request.EntityData.ToString());
                            var staffData = objData as StaffDto;
                            if (request.OperationType == EnumOperationType.Update) await _userService.SynchronizeChangesUserAsync(staffData);
                            subKey = $"{staffData.Id}\\{staffData.StaffCode}";
                        }
                        break;
                    case DataverseEntitiesConsts.Org:
                        mainKey = Consts.RedisKey.BaseData_Organization;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<DepartmentDto>(request.EntityData.ToString());
                            subKey = $"{data.Id}\\{data.OrganizationType}\\{data.DepartmentName}";
                            objData = data;
                        }
                        break;
                    case DataverseEntitiesConsts.Position:
                        mainKey = Consts.RedisKey.BaseData_Position;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<PositionDto>(request.EntityData.ToString());
                            subKey = $"{data.Id}\\{data.Name}";
                            objData = data;
                        }
                        break;
                    case DataverseEntitiesConsts.Consume:
                        mainKey = Consts.RedisKey.BaseData_ConsumeCategory;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<ConsumeCategoryDto>(request.EntityData.ToString());
                            subKey = $"{data.OrgId}\\{data.Id}";
                            objData = data;
                        }
                        break;
                    case DataverseEntitiesConsts.Costnature://5
                        mainKey = Consts.RedisKey.BaseData_CostNature;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<CostNatureDto>(request.EntityData.ToString());
                            subKey = $"{data.ConsumeCategoryId}\\{data.Id}";
                            objData = data;
                        }
                        break;
                    case DataverseEntitiesConsts.Company:
                        mainKey = Consts.RedisKey.BaseData_Company;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<CompanyMasterDataDto>(request.EntityData.ToString());
                            subKey = $"{data.Id}";
                            objData = data;
                        }
                        break;
                    case DataverseEntitiesConsts.District:
                        mainKey = Consts.RedisKey.BaseData_District;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<DistrictDto>(request.EntityData.ToString());
                            subKey = $"{data.Id}\\{data.Name}";
                            objData = data;
                        }
                        break;
                    case DataverseEntitiesConsts.Product:
                        mainKey = Consts.RedisKey.BaseData_Product;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<ProductDto>(request.EntityData.ToString());
                            subKey = $"{data.Id}\\{data.Code}";
                            objData = data;
                        }
                        break;
                    case DataverseEntitiesConsts.CostCenter:
                        mainKey = Consts.RedisKey.BaseData_Costcenter;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<CostcenterDto>(request.EntityData.ToString());
                            subKey = $"{data.Id}\\{data.Code}";
                            objData = data;
                        }
                        break;
                    case DataverseEntitiesConsts.Hospital://10
                        mainKey = Consts.RedisKey.BaseData_Hospital;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<HospitalDto>(request.EntityData.ToString());
                            subKey = $"{data.Id}\\{data.Name}\\{data.HospitalCode}\\{data.HcoVeevaID}";
                            objData = data;
                        }
                        break;
                    case DataverseEntitiesConsts.Department:
                        mainKey = Consts.RedisKey.BaseData_Department;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<OfficeDto>(request.EntityData.ToString());
                            subKey = $"{data.Id}";
                            objData = data;
                        }
                        break;
                    case DataverseEntitiesConsts.OccupationalTitle:
                        mainKey = Consts.RedisKey.BaseData_JobTitle;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<JobTitleDto>(request.EntityData.ToString());
                            subKey = $"{data.Id}";
                            objData = data;
                        }
                        break;
                    case DataverseEntitiesConsts.Province:
                        mainKey = Consts.RedisKey.BaseData_Province;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<ProvinceDto>(request.EntityData.ToString());
                            subKey = $"{data.Id}\\{data.Code}\\{data.Name}";
                            objData = data;
                        }
                        break;
                    case DataverseEntitiesConsts.City:
                        mainKey = Consts.RedisKey.BaseData_City;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<CityDto>(request.EntityData.ToString());
                            subKey = $"{data.Id}\\{data.Code}\\{data.Name}";
                            objData = data;
                        }
                        break;
                    case DataverseEntitiesConsts.Dictionary://15
                        mainKey = Consts.RedisKey.DictData_All;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            objData = JsonConvert.DeserializeObject<DictionaryDto>(request.EntityData.ToString());
                            var dictData = objData as DictionaryDto;
                            subKey = $"{dictData.Id}\\{dictData.ParentCode}\\{dictData.Type}\\{dictData.Code}\\{dictData.Name}";
                        }
                        break;
                    case DataverseEntitiesConsts.Compensation:
                        mainKey = Consts.RedisKey.BaseData_Compensation;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<CompensationDto>(request.EntityData.ToString());
                            subKey = $"{data.Id}";
                            objData = data;
                        }
                        break;
                    //case DataverseEntitiesConsts.WorkflowType:
                    //    mainKey = Consts.RedisKey.BaseData_WorkflowType;
                    //    if (request.OperationType != EnumOperationType.Delete)
                    //        objData = JsonConvert.DeserializeObject<WorkflowTypeDto>(request.EntityData.ToString());
                    //    break;
                    //case DataverseEntitiesConsts.WorkflowStep:
                    //    mainKey = Consts.RedisKey.BaseData_WorkflowStep;
                    //    if (request.OperationType != EnumOperationType.Delete)
                    //        objData = JsonConvert.DeserializeObject<WorkflowStepDto>(request.EntityData.ToString());
                    //    break;
                    case DataverseEntitiesConsts.CoamappingRule:
                        mainKey = Consts.RedisKey.BaseData_CoaMappingRule;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            objData = JsonConvert.DeserializeObject<CoaMappingRuleDto>(request.EntityData.ToString());
                            var data = objData as CoaMappingRuleDto;
                            subKey = $"{data.Id}\\{data.BuCode}\\{data.CostcenterCode}\\{data.CostNatureCode}";
                        }
                        break;
                    case DataverseEntitiesConsts.BuCodingConfiguration:
                        mainKey = Consts.RedisKey.BaseData_BuCodingCfg;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            objData = JsonConvert.DeserializeObject<BuCodingCfgDto>(request.EntityData.ToString());
                            var data = objData as BuCodingCfgDto;
                            subKey = $"{data.Id}\\{data.BuId}";
                        }
                        break;
                    //case "spk_organizational_costcent":
                    //    mainKey = Consts.RedisKey.BaseData_OrganizationCostcenterRelation;
                    //    if (request.OperationType != EnumOperationType.Delete)
                    //    {
                    //        objData = JsonConvert.DeserializeObject<OrgCosterCenterRelationDto>(request.EntityData.ToString());
                    //    }
                    //    break;
                    case "spk_organizationalmasterdata_citymasterdata":
                        mainKey = Consts.RedisKey.BaseData_OrgSpecialCityRelation;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            objData = JsonConvert.DeserializeObject<OrgSpecialCityRelationDto>(request.EntityData.ToString());
                            var data = (OrgSpecialCityRelationDto)objData;
                            subKey = $"{data.Id}\\{data.OrgId}\\{data.CityId}\\{data.IsRestricted == true}";
                        }
                        break;
                    case "spk_organizational_costnature"://20
                        mainKey = Consts.RedisKey.BaseData_OrgCostNatureRelation;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            objData = JsonConvert.DeserializeObject<OrgCostNatureRelationDto>(request.EntityData.ToString());
                            var data = (OrgCostNatureRelationDto)objData;
                            subKey = $"{data.Id}\\{data.OrgId}\\{data.CostNatureId}";
                        }
                        break;
                    case "spk_consume_dictionary":
                        mainKey = Consts.RedisKey.BaseData_ConsumeCategorySponsorshipTypeRelation;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            objData = JsonConvert.DeserializeObject<ConsumeCategorySponsorshipTypeRelationDto>(request.EntityData.ToString());
                            var data = (ConsumeCategorySponsorshipTypeRelationDto)objData;
                            subKey = $"{data.Id}\\{data.ConsumeCategoryId}\\{data.SponsorshipTypeCode}";
                        }
                        break;
                    case "spk_citymasterdata":
                        mainKey = Consts.RedisKey.BaseData_SpecialCity;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<CityMasterDataDto>(request.EntityData.ToString());
                            subKey = $"{data.Id}";
                            objData = data;
                        }
                        break;
                    case "spk_companycurrency":
                        mainKey = Consts.RedisKey.BaseData_CompanyCurrency;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<CompanyCurrencyDto>(request.EntityData.ToString());
                            subKey = $"{data.Id}";
                            objData = data;
                        }
                        break;
                    case "spk_extensioncode":
                        mainKey = Consts.RedisKey.BaseData_StaffPositionRelation;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<StaffAndPositionDto>(request.EntityData.ToString());
                            subKey = $"{data.Id}\\{data.StaffId}\\{data.PositionType}\\{data.OrganizationId}";
                            objData = data;
                        }
                        break;
                    case "spk_organizational_staff"://25
                        mainKey = Consts.RedisKey.BaseData_Org_Staff_Relation;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<StaffDepartmentRelationDto>(request.EntityData.ToString());
                            subKey = $"{data.Id}\\{data.UserId}\\{data.DepartmentId}";
                            objData = data;
                        }
                        break;
                    case "spk_vendertype":
                        mainKey = Consts.RedisKey.BaseData_VendorTypeCfg;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<VendorTypeMappingDto>(request.EntityData.ToString());
                            subKey = $"{data.Id}";
                            objData = data;
                        }
                        break;
                    case "spk_invoicetypetaxratemapp":
                        mainKey = Consts.RedisKey.BaseData_InvoiceTypeTaxRate;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<InvoiceTaxRateMappDto>(request.EntityData.ToString());
                            subKey = $"{data.Id}\\{data.InvoiceTypeId}\\{data.InvoiceTypeName}\\{data.TaxRateId}\\{data.TaxRateName}";
                            objData = data;
                        }
                        break;
                    case "spk_poinitiateamount":
                        mainKey = Consts.RedisKey.BaseData_POInitiateAmount;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<POInitiateAmountDto>(request.EntityData.ToString());
                            subKey = $"{data.Id}\\{data.Name}\\{data.AmountLimitUSD}";
                            objData = data;
                        }
                        break;
                    case "spk_pareturnreason":
                        mainKey = Consts.RedisKey.BaseData_PaReturnReason;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<PaReturnReasonDto>(request.EntityData.ToString());
                            subKey = $"{data.Id}\\{data.MainReason}";
                            objData = data;
                        }
                        break;
                    case "spk_returnsubreason"://30
                        mainKey = Consts.RedisKey.BaseData_ReturnSubReason;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<ReturnSubReasonDto>(request.EntityData.ToString());
                            subKey = $"{data.Id}";
                            objData = data;
                        }
                        break;
                    case "spk_organizational_costcenter":
                        mainKey = Consts.RedisKey.BaseData_OrganizationalCostcenter;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<OrgCostcenterDto>(request.EntityData.ToString());
                            subKey = $"{data.Id}\\{data.OrgId}\\{data.CostcenterId}";
                            objData = data;
                        }
                        break;
                    case "spk_organizational_district":
                        mainKey = Consts.RedisKey.BaseData_OrganizationalDistrict;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<OrgDistrictDto>(request.EntityData.ToString());
                            subKey = $"{data.Id}\\{data.OrgId}\\{data.DistrictId}";
                            objData = data;
                        }
                        break;
                    case "spk_companymasterdata_citymasterdata":
                        mainKey = Consts.RedisKey.BaseData_CityCompany;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<CityCompanyDto>(request.EntityData.ToString());
                            subKey = $"{data.Id}\\{data.CompanyId}\\{data.CityId}";
                            objData = data;
                        }
                        break;
                    case "spk_procurementpushconfig":
                        mainKey = Consts.RedisKey.BaseData_ProcurementPushConfig;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<ProcurementPushConfigDto>(request.EntityData.ToString());
                            subKey = $"{data.Id}\\{data.CompanyId}\\{data.EmployeeId}";
                            objData = data;
                        }
                        break;
                    case "spk_organizational_product"://35
                        mainKey = Consts.RedisKey.BaseData_OrgProductRelation;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<OrgProducRelationtDto>(request.EntityData.ToString());
                            subKey = $"{data.Id}\\{data.OrgId}\\{data.ProductId}";
                            objData = data;
                        }
                        break;
                    case "spk_specapprovalconditionforprocurement":
                        mainKey = Consts.RedisKey.BaseData_SpecApprovalConditionProcurement;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<SpecApprovalConditionProcurementDto>(request.EntityData.ToString());
                            subKey = $"{data.Id}\\{data.CompanyId}\\{data.ConsumeId}\\{data.CostNatureId}\\{data.ExceptionCostcenterId}";
                            objData = data;
                        }
                        break;
                    case "spk_activitytype":
                        mainKey = Consts.RedisKey.BaseData_ActiveTypeCostcenterMapping;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<ActiveTypeCostcenterMappingDto>(request.EntityData.ToString());
                            subKey = $"{data.Id}\\{data.ActiveTypeName}";
                            objData = data;
                        }
                        break;
                    case "spk_businessflowtype":
                        mainKey = Consts.RedisKey.BaseData_BusinessType;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<BusinessTypeDto>(request.EntityData.ToString());
                            subKey = $"{data.Id}\\{data.Name}\\{data.BelongedSystem}";
                            objData = data;
                        }
                        break;
                    case "spk_businessflowworkflow":
                        mainKey = Consts.RedisKey.BaseData_BusinessTypeAndWorkflowType;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<BusinessTypeAndWorkflowTypeDto>(request.EntityData.ToString());
                            subKey = $"{data.Id}\\{data.Name}\\{data.BelongedSystem}\\{data.BusinessTypeId}\\{data.WorkflowTypeId}";
                            objData = data;
                        }
                        break;
                    case "spk_currencyconfig"://40
                        mainKey = Consts.RedisKey.BaseData_CurrencyConfig;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<CurrencyConfigDto>(request.EntityData.ToString());
                            subKey = $"{data.Id}\\{data.CurrencyCode}";
                            objData = data;
                        }
                        break;
                    case "spk_buhospitalmasterdata":
                        mainKey = Consts.RedisKey.BaseData_BUHospital;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<BUHospitalDto>(request.EntityData.ToString());
                            subKey = $"{data.Id}\\{data.HospitalCode}\\{data.Name}\\{data.VeevaID}\\{data.SourceSystem}";
                            objData = data;
                        }
                        break;
                    case "spk_pturttypeconfig":
                        mainKey = Consts.RedisKey.BaseData_PturtTypeConfig;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<PturtTypeConfigDto>(request.EntityData.ToString());
                            subKey = $"{data.Id}\\{data.Name}\\{data.PturtType}\\{data.Code}\\{data.PturtTypeName}\\{data.IsAttachmentRequired}";
                            objData = data;
                        }
                        break;
                    case "spk_companymasterdata_organizational":
                        mainKey = Consts.RedisKey.BaseData_CompanyAndOrgRelation;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<CompanyAndOrgRelationDto>(request.EntityData.ToString());
                            subKey = $"{data.Id}";
                            objData = data;
                        }
                        break;
                    case "spk_travelagency":
                        mainKey = Consts.RedisKey.BaseData_Travelagency;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<BaseDataDto>(request.EntityData.ToString());
                            subKey = $"{data.Id}\\{data.Name}";
                            objData = data;
                        }
                        break;
                    case "spk_productcost":
                        mainKey = Consts.RedisKey.BaseData_ProductCost;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<ProductCostDto>(request.EntityData.ToString());
                            subKey = $"{data.Id}";
                            objData = data;
                        }
                        break;
                    case "spk_specialvendorname":
                        mainKey = Consts.RedisKey.BaseData_SpecialVendor;
                        if (request.OperationType != EnumOperationType.Delete)
                        {
                            var data = JsonConvert.DeserializeObject<SpecialVendorDto>(request.EntityData.ToString());
                            subKey = $"{data.Id}\\{data.Name}\\{data.CompanyCode}\\{data.VendorCode}";
                            objData = data;
                        }
                        break;
                    default:
                        break;
                }
                #endregion

                #region 操作对应的redi缓存
                if (request.OperationType == EnumOperationType.Delete)
                {
                    var subId = "*" + request.Id.ToString() + "*";
                    var hashEntries = _redisRepository.Database.HashScan(mainKey, subId);//根据Id去检索
                    var hashFields = hashEntries.Select(x => x.Name).ToArray();
                    _redisRepository.Database.HashDelete(mainKey, hashFields);
                }
                else if (request.OperationType == EnumOperationType.Create)
                {
                    hashList.Add(new HashEntry(subKey, JsonConvert.SerializeObject(objData)));
                    _redisRepository.Database.HashSet(mainKey, hashList.ToArray());
                }
                else//update
                {
                    //先删除后新增：subkey为多级且其中某个属性发生变更
                    var subId = "*" + request.Id.ToString() + "*";
                    var hashEntries = _redisRepository.Database.HashScan(mainKey, subId);//根据Id去检索
                    if (hashEntries != null && hashEntries.Any())
                    {
                        var entry = hashEntries.First();
                        hashList.Add(new HashEntry(subKey, JsonConvert.SerializeObject(objData)));
                        var trans = _redisRepository.Database.CreateTransaction();
                        trans.AddCondition(Condition.HashExists(mainKey, entry.Name));
                        trans.HashDeleteAsync(mainKey, entry.Name);//await 会卡在这
                        trans.HashSetAsync(mainKey, hashList.ToArray());
                        var committed = trans.Execute();
                        if (!committed)
                        {
                            return await Task.FromResult(MessageResult.FailureResult(new MessageModelBase(500, "Update Cache Data Failed!")));
                        }
                    }
                }
                //else
                //{
                //    hashList.Add(new HashEntry(subKey, JsonConvert.SerializeObject(objData)));
                //    _redisRepository.Database.HashSet(mainKey, hashList.ToArray());
                //}

                #endregion

                return await Task.FromResult(MessageResult.SuccessResult());
            }
            catch (Exception ex)
            {
                _logger.LogError($"Receive Powerapp Data Exception:{ex}");
                //return MessageResult.FailureResult("Receive Powerapp Data Failed!");
                return await Task.FromResult(MessageResult.FailureResult(new MessageModelBase(500, "Receive Powerapp Data Failed!")));
            }
        }

        public async Task<MessageResult> SendEmailAsync(SendEmailRequestDto request)
        {
            try
            {
                #region 查询单据的信息
                var msgFormatted = string.Empty;
                SendMessageContentDto msgDto = null;
                var formCode = string.Empty;
                if (!string.IsNullOrEmpty(request.Content))
                {
                    msgDto = JsonConvert.DeserializeObject<SendMessageContentDto>(request.Content);
                    formCode = msgDto.FormNo;
                    if (string.IsNullOrEmpty(msgDto.FormNo) && !string.IsNullOrEmpty(msgDto.FormId))
                        formCode = msgDto.FormId;
                }
                #endregion

                #region 组织信息
                //string path = AppDomain.CurrentDomain.BaseDirectory;
                var provider = _env.WebRootFileProvider;
                string webhost = _configuration["SpeakerEmail:WebHost"];

                //html模板
                string html = string.Empty;
                //string html = File.ReadAllText($"{path}/Templates/Email/ApprovalNotification.html");
                switch (request.EmailNotifyType)
                {
                    case EnumEmailNotifyType.ApprovalTaskNotify:
                        html = await provider.GetFileInfo("Templates/Email/ApprovalNotification.html").ReadAsStringAsync();
                        webhost += "task/approve";
                        break;
                    case EnumEmailNotifyType.WorkflowApprovedNotify:
                        break;
                    case EnumEmailNotifyType.WorkflowRejectedNotify:
                        break;
                    case EnumEmailNotifyType.WorkflowDeniedNotify:
                        break;
                    default:
                        break;
                }
                if (string.IsNullOrEmpty(html))
                    return MessageResult.FailureResult("发送邮件失败:邮件通知类型有误或模板文件未找到");

                string env = _configuration["SpeakerEmail:Env"];

                html = html.Replace("{ApproverName}", msgDto?.Approver);
                html = html.Replace("{ApprovalTaskLink}", webhost);
                //html = html.Replace("{Environment}", env?.ToUpper());//标识环境是dev\sit\prod
                html = html.Replace("{Applicat}", msgDto?.Submitter);
                //html = html.Replace("{ApplyDate}", taskEntity?.ApprovalTime.ToString("yyyyMMdd"));
                html = html.Replace("{ApplyDate}", msgDto?.SubmitTime);
                html = html.Replace("{ApplyCode}", formCode);
                html = html.Replace("{WorkflowStepName}", msgDto?.StepName);
                html = html.Replace("{WorkflowTypeName}", msgDto?.WorkflowTypeName);
                //html = html.Replace("{ContactEmail}", request.From);
                //html = html.Replace("{ContactTelephone}", "13023456789");
                #endregion

                #region 发送邮件通知
                SmtpClient client = new SmtpClient(request.Server)
                {
                    Timeout = 60 * 1000,
                    UseDefaultCredentials = true
                };

                MailMessage mailMsg = new MailMessage();
                MailAddress from = new MailAddress(request.From, request.FromDisplayName, Encoding.UTF8);
                mailMsg.From = from;
                var tempTag = env.ToUpper().Equals("P") ? "" : "[测试]";
                mailMsg.Subject = $"{tempTag}审批通知|{formCode}";
                mailMsg.SubjectEncoding = Encoding.UTF8;
                mailMsg.Body = html;
                mailMsg.IsBodyHtml = true;
                mailMsg.BodyEncoding = Encoding.UTF8;

                request.Tos.Split(",").Distinct().ToList().ForEach(x =>
                {
                    if (!string.IsNullOrEmpty(x))
                        mailMsg.To.Add(new MailAddress(x));
                });

                request.Ccs.Split(",").Distinct().ToList().ForEach(x =>
                {
                    if (!string.IsNullOrEmpty(x))
                        mailMsg.CC.Add(new MailAddress(x));
                });

                await client.SendMailAsync(mailMsg);
                #endregion

                return MessageResult.SuccessResult();
            }
            catch (Exception ex)
            {
                return MessageResult.FailureResult("发送邮件失败" + ex.Message);
            }
        }
    }
}
