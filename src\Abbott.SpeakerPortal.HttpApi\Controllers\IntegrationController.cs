﻿using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Integration.EpdPortal.EpdHcp;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.Controllers
{
    /// <summary>
    /// 通用 -调用外部接口
    /// </summary>
    [ApiExplorerSettings(GroupName = SwaggerGrouping.COMMON)]
    public class IntegrationController : SpeakerPortalController
    {
        private readonly IEPDHcpService _epdHcpService;
        public IntegrationController(IEPDHcpService epdHcpService)
        {
            _epdHcpService = epdHcpService;
        }

        /// <summary>
        /// 医生查询接口-EPD TEST
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult<List<DoctorData>>))]
        public async Task<IActionResult> QueryEpdDoctorsAsync([FromBody] DoctorQueryRequest doctorRequest) { 
            var result = await _epdHcpService.QueryEpdDoctorsAsync(doctorRequest);
            return Ok(MessageResult.SuccessResult(result));
        }
    }
}
