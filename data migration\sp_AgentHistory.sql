create PROCEDURE sp_AgentHistory
as
begin
	    --删除表
    IF OBJECT_ID(N'PLATFORM_ABBOTT_STG.dbo.AgentHistory_tmp ', N'U') IS NOT NULL
	BEGIN
		drop table AgentHistory_tmp;
	--165758
	select
		a.Id BpmId,
		agent.spk_agentconfigurationid [AgentConfigId],
		agent._WorkflowType [WorkflowType],
		a.WarrantyOccurTime [AgentTime],
		a.ProcInstId,
		case
			when fvr.serialNumber is not null then fvr.serialNumber
			when fpr.serialNumber is not null then fpr.serialNumber
			when fbd.serialNumber is not null then fbd.serialNumber
			when fbw.serialNumber is not null then fbw.serialNumber
			when fpo.serialNumber is not null then fpo.serialNumber
			when fgr.serialNumber is not null then fgr.serialNumber
			when fpa.serialNumber is not null then fpa.serialNumber
			else
			null
		end serialNumber,
		case
			when vr.ApplicationCode is not null then vr.Id
			when pr.ApplicationCode is not null then pr.ID
			when bd.ApplicationCode is not null then bd.Id
			when bw.ApplicationCode is not null then bw.Id
			when po.ApplicationCode is not null then po.Id
			when gr.ApplicationCode is not null then gr.Id
			when pa.ApplicationCode is not null then pa.Id
			else
			null
		end [FormId],
		case
			when vr.ApplicationCode is not null then vr.ApplicationCode
			when pr.ApplicationCode is not null then pr.ApplicationCode
			when bd.ApplicationCode is not null then bd.ApplicationCode
			when bw.ApplicationCode is not null then bw.ApplicationCode
			when po.ApplicationCode is not null then po.ApplicationCode
			when gr.ApplicationCode is not null then gr.ApplicationCode
			when pa.ApplicationCode is not null then pa.ApplicationCode
			else
			null
		end [FormCode],
		'{}' [ExtraProperties],
		''[ConcurrencyStamp],
		'ApplyTime' [CreationTime],
		agent.spk_agent AgentId,
		'' [LastModificationTime],
		'' [LastModifierId],
		0 [IsDeleted],
		'' [DeleterId],
		'' [DeletionTime]
	into AgentHistory_tmp
	from [ODS_T_WARRANTY_LOG] a
	join spk_agentconfiguration agent on a.WarrantyId=agent._BpmId
	left join (
		select ProcInstId,serialNumber from ODS_Form_7a708c9568fb444a884eb5eca658975f
		union
		select ProcInstId,serialNumber from ODS_Form_663dd63299be45d69dd8f853d0a4b445
	) fvr on a.ProcInstId=fvr.ProcInstId
	left join VendorApplications_Tmp vr on fvr.serialNumber=vr.ApplicationCode
	left join ODS_Form_6e32f7dd7c4e49aea79949009c3bf7ae fpr on a.ProcInstId=fpr.ProcInstId
	left join PurPRApplications_tmp pr on fpr.serialNumber=pr.ApplicationCode
	left join ODS_Form_fefa2338743b4ebea533c8f6c5c2bacd fbd on a.ProcInstId=fbd.ProcInstId
	left join PurBDApplications_tmp bd on fbd.serialNumber=bd.ApplicationCode
	left join ODS_Form_c2bc32cd01e74718b19ff60c52cf98f6 fbw on a.ProcInstId=fbw.ProcInstId
	left join PurBWApplications bw on fbw.serialNumber=bw.ApplicationCode
	left join ODS_Form_92ccaf9b95de4d7d9d8b411b2a030edc fpo on a.ProcInstId=fpo.ProcInstId
	left join PurPOApplications_tmp po on fpo.serialNumber=po.ApplicationCode
	left join ODS_Form_2ce08373394a4d4cace676978bfa2984 fgr on a.ProcInstId=fgr.ProcInstId
	left join PurGRApplications_tmp gr on fgr.serialNumber=gr.ApplicationCode
	left join ODS_Form_e37632eb82f04fbda355cffdac744166 fpa on a.ProcInstId=fpa.ProcInstId
	left join PurPAApplications_tmp pa on fpa.serialNumber=pa.ApplicationCode
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
   	--165758
	select
		a.Id BpmId,
		agent.spk_agentconfigurationid [AgentConfigId],
		agent._WorkflowType [WorkflowType],
		a.WarrantyOccurTime [AgentTime],
		a.ProcInstId,
		case
			when fvr.serialNumber is not null then fvr.serialNumber
			when fpr.serialNumber is not null then fpr.serialNumber
			when fbd.serialNumber is not null then fbd.serialNumber
			when fbw.serialNumber is not null then fbw.serialNumber
			when fpo.serialNumber is not null then fpo.serialNumber
			when fgr.serialNumber is not null then fgr.serialNumber
			when fpa.serialNumber is not null then fpa.serialNumber
			else
			null
		end serialNumber,
		case
			when vr.ApplicationCode is not null then vr.Id
			when pr.ApplicationCode is not null then pr.ID
			when bd.ApplicationCode is not null then bd.Id
			when bw.ApplicationCode is not null then bw.Id
			when po.ApplicationCode is not null then po.Id
			when gr.ApplicationCode is not null then gr.Id
			when pa.ApplicationCode is not null then pa.Id
			else
			null
		end [FormId],
		case
			when vr.ApplicationCode is not null then vr.ApplicationCode
			when pr.ApplicationCode is not null then pr.ApplicationCode
			when bd.ApplicationCode is not null then bd.ApplicationCode
			when bw.ApplicationCode is not null then bw.ApplicationCode
			when po.ApplicationCode is not null then po.ApplicationCode
			when gr.ApplicationCode is not null then gr.ApplicationCode
			when pa.ApplicationCode is not null then pa.ApplicationCode
			else
			null
		end [FormCode],
		'{}' [ExtraProperties],
		''[ConcurrencyStamp],
		'ApplyTime' [CreationTime],
		agent.spk_agent AgentId,
		'' [LastModificationTime],
		'' [LastModifierId],
		0 [IsDeleted],
		'' [DeleterId],
		'' [DeletionTime]
	into AgentHistory_tmp
	from [ODS_T_WARRANTY_LOG] a
	join spk_agentconfiguration agent on a.WarrantyId=agent._BpmId
	left join (
		select ProcInstId,serialNumber from ODS_Form_7a708c9568fb444a884eb5eca658975f
		union
		select ProcInstId,serialNumber from ODS_Form_663dd63299be45d69dd8f853d0a4b445
	) fvr on a.ProcInstId=fvr.ProcInstId
	left join VendorApplications_Tmp vr on fvr.serialNumber=vr.ApplicationCode
	left join ODS_Form_6e32f7dd7c4e49aea79949009c3bf7ae fpr on a.ProcInstId=fpr.ProcInstId
	left join PurPRApplications_tmp pr on fpr.serialNumber=pr.ApplicationCode
	left join ODS_Form_fefa2338743b4ebea533c8f6c5c2bacd fbd on a.ProcInstId=fbd.ProcInstId
	left join PurBDApplications_tmp bd on fbd.serialNumber=bd.ApplicationCode
	left join ODS_Form_c2bc32cd01e74718b19ff60c52cf98f6 fbw on a.ProcInstId=fbw.ProcInstId
	left join PurBWApplications bw on fbw.serialNumber=bw.ApplicationCode
	left join ODS_Form_92ccaf9b95de4d7d9d8b411b2a030edc fpo on a.ProcInstId=fpo.ProcInstId
	left join PurPOApplications_tmp po on fpo.serialNumber=po.ApplicationCode
	left join ODS_Form_2ce08373394a4d4cace676978bfa2984 fgr on a.ProcInstId=fgr.ProcInstId
	left join PurGRApplications_tmp gr on fgr.serialNumber=gr.ApplicationCode
	left join ODS_Form_e37632eb82f04fbda355cffdac744166 fpa on a.ProcInstId=fpa.ProcInstId
	left join PurPAApplications_tmp pa on fpa.serialNumber=pa.ApplicationCode
    -- select * from #vendor_tbl
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END

end;