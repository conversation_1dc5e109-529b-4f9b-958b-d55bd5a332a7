﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.User;

using Hangfire;

namespace Abbott.SpeakerPortal.BackgroundWorkers.System
{
    public class SyncBpcsEdiSequenceNumWorker : SpeakerPortalBackgroundWorkerBase
    {
        public SyncBpcsEdiSequenceNumWorker()
        {
            CronExpression = Cron.Minutely();
        }

        public override async Task DoWorkAsync(CancellationToken cancellationToken = default)
        {
            await LazyServiceProvider.LazyGetService<ISequenceNumService>().UpdateToDB();
        }
    }
}
