﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Abbott.SpeakerPortal.Contracts.Common.Cognitive;

using Microsoft.Azure.CognitiveServices.Vision.ComputerVision;
using Microsoft.Azure.CognitiveServices.Vision.ComputerVision.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Abbott.SpeakerPortal.AppServices.Common
{
    public class CognitiveService : SpeakerPortalAppService, ICognitiveService
    {
        CognitiveSettings _cognitiveSettings;
        public CognitiveService(IOptions<CognitiveSettings> options)
        {
            _cognitiveSettings = options.Value;
        }

        ComputerVisionClient CreateClient()
        {
            var client = new ComputerVisionClient(new ApiKeyServiceClientCredentials(_cognitiveSettings.Key));
            client.Endpoint = _cognitiveSettings.Endpoint;
            return client;
        }

        async Task<IEnumerable<string>> Cognize(Stream stream)
        {
            using (var client = CreateClient())
            {
                stream.Position = 0;
                Logger.LogInformation($"Cognize：{stream.Length}");
                var result = await client.ReadInStreamAsync(stream);

                Logger.LogInformation($"Cognize：{result.OperationLocation}");
                var operationId = result.OperationLocation[^36..];
                ReadOperationResult operationResult;
                do
                {
                    operationResult = await client.GetReadResultAsync(Guid.Parse(operationId));
                } while (operationResult.Status == OperationStatusCodes.NotStarted || operationResult.Status == OperationStatusCodes.Running);
                return operationResult.AnalyzeResult.ReadResults.SelectMany(a => a.Lines, (a, b) => b.Text);
            }
        }

        public async Task<IEnumerable<string>> GetCognizeResultAsync(Stream stream)
        {
            var result = await Cognize(stream);
            return result;
        }

        public async Task<IEnumerable<string>> GetCognizeResultAsync(byte[] buffer)
        {
            using (var stream = new MemoryStream(buffer))
            {
                var result = await Cognize(stream);
                return result;
            }
        }
    }
}
