apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: speaker-ingress
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "20m"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - speaker-portal-api-d.oneabbott.com
    secretName: tls-speaker-portal-api-d-secret
  - hosts:
    - speaker-portal-d.oneabbott.com
    secretName: tls-speaker-portal-d-secret
  - hosts:
    - speaker-portal-h5-d.abbott.com.cn
    secretName: tls-speaker-portal-h5-d-secret
  rules:
  - host: speaker-portal-api-d.oneabbott.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: corp-ca191-speakerapi-d
            port:
              number: 80
  - host: speaker-portal-d.oneabbott.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: corp-ca191-speakerh5-d
            port:
              number: 80
  - host: speaker-portal-h5-d.abbott.com.cn
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: corp-ca191-speakerh5-pc-d
            port:
              number: 80