CREATE PROCEDURE dbo.sp_PurPOApplications
AS 
BEGIN

with processStatus_info as (
select a.ProcInstId,actname,FinishDate,ROW_NUMBER () over (PARTITION by a.ProcInstId order by FinishDate desc) rn 
from PLATFORM_ABBOTT_dev.dbo.ODS_AUTO_BIZ_PurchaseOrderApplication_Info a
left join PLATFORM_ABBOTT_dev.dbo.ODS_Form_92ccaf9b95de4d7d9d8b411b2a030edc b 
on a.ProcInstId =b.ProcInstId 
left join PLATFORM_ABBOTT_Dev.dbo.ODS_T_PROCESS_Historys c 
on a.ProcInstId =c.ProcInstID 
where processStatus=N'发起人终止'
)
SELECT 
NEWID() AS Id,--
a.ProcInstId,
a.serialNumber AS ApplicationCode,--
CAST('' as nvarchar(max)) AS PRApplicationDetailId,--以PO主表的ProcInstId查询该表的记录，以PRFormCode+PRNUmber查询到对应的PR明细行ID并填入，如有多个ID则逗号分隔
case when processStatus=N'PO审批结束' then 
	case when XmlContent.value('(/root/PurchaseOrderApplication_hiddenBlock_MainStore/PDFDownloadStatus)[1]', 'nvarchar(255)') ='1' then N'发起收货'
	else N'申请人确认' end 
when processStatus=N'N/A' then N'主采购循环审批中'
when processStatus=N'N/A(不存在该状态)' then N'已通过'
when processStatus=N'发起人终止' then 
	case when pi.actname=N'重发起' then  N'作废' 
	else N'已拒绝' end
when processStatus=N'重发起' or processStatus=N'主采购循环审批中' then N'退回'
when processStatus=N'关闭' then N'关闭'
--when processStatus=N'发起人终止' then N'作废'
when processStatus=N'N/A(不迁移草稿)' then N'草稿' 
when processStatus=N'N/A(不存在该状态)' then N'已撤回'
--when processStatus=N'PO审批结束' then N'发起收货'
end AS Status,--参考下方附录状态mapping表
a.applicantEmpId AS ApplyUserId,--以该ID匹配至员工主数据
a.applicationDate AS ApplyTime,--
PRFormCode AS ApplyUserBu,--基于该PO对应的PR申请单，查询PurPRApplications表内对应记录的ApplyUserBu表示该单据对应费用由哪个Division申请
'' AS BWApplicationId,--?
'' AS BApplicationId,--?
case when XmlContent.value('(/root/PurchaseOrderApplication_orderTypeBlock_MainStore/OrderType)[1]', 'nvarchar(255)')=N'采购订单' then '1' 
when XmlContent.value('(/root/PurchaseOrderApplication_orderTypeBlock_MainStore/OrderType)[1]', 'nvarchar(255)')=N'形式订单' then '2' end AS POType,--采购订单-1；形式订单-2
res_code AS CompanyId,--以该值作为Res_Data查询T_Resource中Res_Parent_Code="61a3f911b5ae4bc98cddd441833d861e"的记录，基于匹配回的Res_Code匹配至公司主数据得到公司ID
a.SupplierCode,a.SupplierName,
CAST('' as nvarchar(255)) AS VendorId,--基于此处的SupplyCode及SupplyName，结合该单对应的公司编码CompanyId，去查询BPCSPMFVM对应的记录(VNDERX,VEXTNM,VMCMPY)，对于匹配成功的记录，再找到其对应的BPCSAVM.ID并填入(基于VMCMPY=VCMPNY AND VNDERX=VENDOR)
case when a.SupplierAttribute=N'不适用' then '0'
 when a.SupplierAttribute=N'APS' then '1' else a.SupplierAttribute  end AS VendorPorperty,--若为"不适用"则填写为0，若为"APS"则填写为1，其他情况等待与开发确认逻辑
Address AS RegCertificateAddress,--
Contacts AS ContactName,--
TelPhone AS ContactPhone,--
Email AS ContactEmail,--
S_Fax AS FaxNumber,--
CASE WHEN CHARINDEX('_', TermCode) > 0 THEN LEFT(TermCode, CHARINDEX('_', TermCode) - 1) ELSE NULL END AS  PaymentTerm,--取此处"_"前的字符串作为Term编码填入
Currency_Value AS Currency,--
ExchangeRate AS ExchangeRate,--
'' AS PRType,--
'' AS PRCorrespond,--
MattersNeedAttention AS AttentionNote,--
DeliveryMethod AS DeliveryType,--
DeliveryLocation_Text AS DeliveryAddress,--
PaymentCondition_Text AS PaymentCondition,--
DeliveryData AS DeliveryDate,--
ShelfLife AS Qualitystandard,--
Other AS Others,--
Remark AS Remark,--
d.up_id  AS AttachmentFile,--支持文档，以ProcInstId查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到AccessoryGrid下所有的up_Id，填入匹配出的NexBPM支持文件ID，多个支持文件以英文逗号分隔
'{}' AS ExtraProperties,--默认填写为"{}"
'' AS ConcurrencyStamp,--?
a.applicantEmpId AS CreationTime,--填充为ApplyTime即可
a.applicationDate AS CreatorId,--填充为ApplyUserId即可
'' AS LastModificationTime,--默认为空
'' AS LastModifierId,--默认为空
'0' AS IsDeleted,--默认为0
'' AS DeleterId,--默认为空
'' AS DeletionTime,--默认为空
Saving AS Saving,--
'' AS VendorCategory,--
XmlContent.value('(/root/PurchaseOrderApplication_InvoiceInfoBlock_MainStore/BankAccount)[1]', 'nvarchar(255)') AS BankAccount,--
InvoiceAddress AS InvoiceAddress,--
Fax AS InvoiceFax,--
InvoicePleaseOpen AS InvoiceTitle,--
XmlContent.value('(/root/PurchaseOrderApplication_InvoiceInfoBlock_MainStore/OpeningBank)[1]', 'nvarchar(255)') AS OpenBank,--
A.NotTaxTotalAmount AS TotalAmount,--
A.totalAmount AS TotalAmountTax,--
PRFormCode AS ApplyUserBuName,--基于该PO对应的PR申请单，查询PurPRApplications表内对应记录的ApplyUserBuName表示该单据对应费用由哪个Division申请
A.applicantEmpName AS ApplyUserName,--
A.supplierName AS VendorName,--
CASE WHEN IsPOLate='false' THEN '0' 
WHEN IsPOLate='true' THEN '1'  END AS IsLate,--false-0, true-1
ASNType_Value AS ApsPorperty,--基于该值匹配至APS类别后找回对应ID
Currency_Value AS CurrencySymbol,--基于币种值填入对应的货币符号(数据源等待与BPM确认)
Phone AS PhoneNumber,--
cast(PRFormCode as nvarchar(max)) AS PRId,--基于该PO对应的PR申请单，填入PR申请ID
A.POApprovedDate AS ApprovedDate,--
A.SupplierCode AS VendorCode,--
A.applicantDept_Text AS ApplyUserBuToDeptName,--
A.applicantDeptId AS ApplyUserDept--以该ID匹配至对应的组织主数据
INTO #PurPOApplications_tmp
FROM PLATFORM_ABBOTT_dev.dbo.ODS_AUTO_BIZ_PurchaseOrderApplication_Info a 
left join PLATFORM_ABBOTT_dev.dbo.ODS_Form_92ccaf9b95de4d7d9d8b411b2a030edc b 
on a.ProcInstId =b.ProcInstId 
left join PLATFORM_ABBOTT_dev.dbo.ODS_T_FORMINSTANCE_GLOBAL c
on a.ProcInstId =c.ProcInstId 
left join PLATFORM_ABBOTT_dev.dbo.ODS_T_Resource t
on a.company_Value=t.Res_Data and Res_Parent_Code='61a3f911b5ae4bc98cddd441833d861e'
left join PLATFORM_ABBOTT_Dev.dbo.XML_4  d
on a.ProcInstId=d.ProcInstId
left join processStatus_info pi
on a.ProcInstId=pi.ProcInstId and rn='1'


--更新PRApplicationDetailId
update a set a.PRApplicationDetailId=b.id from #PurPOApplications_tmp a
join
(
SELECT   a.ProcInstId,STRING_AGG(cast(ppdt.id as nvarchar(50)), ', ') WITHIN GROUP (ORDER BY ppdt.id) AS id  from PLATFORM_ABBOTT_Dev.dbo.ods_AUTO_BIZ_PurchaseOrderApplication_Info a
left join PLATFORM_ABBOTT_Dev.dbo.ods_AUTO_BIZ_PurchaseOrderApplication_Info_PR b
on a.ProcInstId=b.ProcInstId 
left join (select *,ROW_NUMBER () over(PARTITION by ApplicationCode order by ProcInstId desc) rn  from PLATFORM_ABBOTT_Dev.dbo.PurPRApplications_tmp ) ppt 
on b.PRNo=ppt.ApplicationCode and rn='1'
left join PLATFORM_ABBOTT_Dev.dbo.PurPRApplicationDetails_tmp ppdt 
on ppt.ProcInstId=ppdt.ProcInstId  and ppdt.RowNo=b.PR_Item_No 
GROUP BY  a.ProcInstId
)b
on a.ProcInstId=b.ProcInstId
PRINT(N'完成'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'));

--更新ApplyUserBu
update a set a.ApplyUserBu=b.ApplyUserBu,a.ApplyUserBuName=b.ApplyUserBuName  from #PurPOApplications_tmp a
join(
select b.ProcInstId,a.ApplyUserBu,a.ApplyUserBuName from #PurPOApplications_tmp b  left join PLATFORM_ABBOTT_Dev.dbo.PurPRApplications_tmp  a  
on b.ApplyUserBu =a.ApplicationCode 
)b
on a.ProcInstId=b.ProcInstId
PRINT(N'完成'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'));

--更新PRId
update a set a.PRId=b.id from #PurPOApplications_tmp a
join
(
select ppt.id,b.PRNO
from PLATFORM_ABBOTT_Dev.dbo.ods_AUTO_BIZ_PurchaseOrderApplication_Info a
left join PLATFORM_ABBOTT_Dev.dbo.ods_AUTO_BIZ_PurchaseOrderApplication_Info_PR b
on a.ProcInstId=b.ProcInstId 
left join (select *,ROW_NUMBER () over(PARTITION by ApplicationCode order by ProcInstId desc) rn  from PLATFORM_ABBOTT_Dev.dbo.PurPRApplications_tmp ) ppt 
on b.PRNo=ppt.ApplicationCode and rn='1'  
)b
on PRId=PRNO
PRINT(N'完成'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'));

IF OBJECT_ID(N'PLATFORM_ABBOTT_Dev.dbo.PurPOApplications_tmp', N'U') IS NOT NULL
BEGIN
	update a
	set a.[PRApplicationDetailId]  = b.[PRApplicationDetailId] 
        ,a.[Status]                 = b.[Status]                
        ,a.[ApplyUserId]            = b.[ApplyUserId]           
        ,a.[ApplyTime]              = b.[ApplyTime]             
        ,a.[ApplyUserBu]            = b.[ApplyUserBu]           
        ,a.[BWApplicationId]        = b.[BWApplicationId]       
        ,a.[BApplicationId]         = b.[BApplicationId]        
        ,a.[POType]                 = b.[POType]                
        ,a.[CompanyId]              = b.[CompanyId] 
        ,a.[SupplierCode]           = b.[SupplierCode]
        ,a.[SupplierName]           = b.[SupplierName]             
        ,a.[VendorId]               = b.[VendorId]              
        ,a.[VendorPorperty]         = b.[VendorPorperty]        
        ,a.[RegCertificateAddress]  = b.[RegCertificateAddress] 
        ,a.[ContactName]            = b.[ContactName]           
        ,a.[ContactPhone]           = b.[ContactPhone]          
        ,a.[ContactEmail]           = b.[ContactEmail]          
        ,a.[FaxNumber]              = b.[FaxNumber]             
        ,a.[PaymentTerm]            = b.[PaymentTerm]           
        ,a.[Currency]               = b.[Currency]              
        ,a.[ExchangeRate]           = b.[ExchangeRate]          
        ,a.[PRType]                 = b.[PRType]                
        ,a.[PRCorrespond]           = b.[PRCorrespond]          
        ,a.[AttentionNote]          = b.[AttentionNote]         
        ,a.[DeliveryType]           = b.[DeliveryType]          
        ,a.[DeliveryAddress]        = b.[DeliveryAddress]       
        ,a.[PaymentCondition]       = b.[PaymentCondition]      
        ,a.[DeliveryDate]           = b.[DeliveryDate]          
        ,a.[Qualitystandard]        = b.[Qualitystandard]       
        ,a.[Others]                 = b.[Others]                
        ,a.[Remark]                 = b.[Remark]                
        ,a.[AttachmentFile]         = b.[AttachmentFile]        
        ,a.[ExtraProperties]        = b.[ExtraProperties]       
        ,a.[ConcurrencyStamp]       = b.[ConcurrencyStamp]      
        ,a.[CreationTime]           = b.[CreationTime]          
        ,a.[CreatorId]              = b.[CreatorId]             
        ,a.[LastModificationTime]   = b.[LastModificationTime]  
        ,a.[LastModifierId]         = b.[LastModifierId]        
        ,a.[IsDeleted]              = b.[IsDeleted]             
        ,a.[DeleterId]              = b.[DeleterId]             
        ,a.[DeletionTime]           = b.[DeletionTime]          
        ,a.[Saving]                 = b.[Saving]                
        ,a.[VendorCategory]         = b.[VendorCategory]        
        ,a.[BankAccount]            = b.[BankAccount]           
        ,a.[InvoiceAddress]         = b.[InvoiceAddress]        
        ,a.[InvoiceFax]             = b.[InvoiceFax]            
        ,a.[InvoiceTitle]           = b.[InvoiceTitle]          
        ,a.[OpenBank]               = b.[OpenBank]              
        ,a.[TotalAmount]            = b.[TotalAmount]           
        ,a.[TotalAmountTax]         = b.[TotalAmountTax]        
        ,a.[ApplyUserBuName]        = b.[ApplyUserBuName]       
        ,a.[ApplyUserName]          = b.[ApplyUserName]         
        ,a.[VendorName]             = b.[VendorName]            
        ,a.[IsLate]                 = b.[IsLate]                
        ,a.[ApsPorperty]            = b.[ApsPorperty]           
        ,a.[CurrencySymbol]         = b.[CurrencySymbol]        
        ,a.[PhoneNumber]            = b.[PhoneNumber]           
        ,a.[PRId]                   = b.[PRId]                  
        ,a.[ApprovedDate]           = b.[ApprovedDate]          
        ,a.[VendorCode]             = b.[VendorCode]            
        ,a.[ApplyUserBuToDeptName]  = b.[ApplyUserBuToDeptName] 
        ,a.[ApplyUserDept]          = b.[ApplyUserDept]      
     from PLATFORM_ABBOTT_Dev.dbo.PurPOApplications_tmp a 
     left join #PurPOApplications_tmp b on a.ProcInstId = b.ProcInstId and a.ApplicationCode = b.ApplicationCode
      
     insert into PLATFORM_ABBOTT_Dev.dbo.PurPOApplications_tmp
     select a.[Id]
     		,a.[ProcInstId]
            ,a.[ApplicationCode]
            ,a.[PRApplicationDetailId]
            ,a.[Status]
            ,a.[ApplyUserId]
            ,a.[ApplyTime]
            ,a.[ApplyUserBu]
            ,a.[BWApplicationId]
            ,a.[BApplicationId]
            ,a.[POType]
            ,a.[CompanyId]
            ,a.[SupplierCode]
            ,a.[SupplierName]  
            ,a.[VendorId]
            ,a.[VendorPorperty]
            ,a.[RegCertificateAddress]
            ,a.[ContactName]
            ,a.[ContactPhone]
            ,a.[ContactEmail]
            ,a.[FaxNumber]
            ,a.[PaymentTerm]
            ,a.[Currency]
            ,a.[ExchangeRate]
            ,a.[PRType]
            ,a.[PRCorrespond]
            ,a.[AttentionNote]
            ,a.[DeliveryType]
            ,a.[DeliveryAddress]
            ,a.[PaymentCondition]
            ,a.[DeliveryDate]
            ,a.[Qualitystandard]
            ,a.[Others]
            ,a.[Remark]
            ,a.[AttachmentFile]
            ,a.[ExtraProperties]
            ,a.[ConcurrencyStamp]
            ,a.[CreationTime]
            ,a.[CreatorId]
            ,a.[LastModificationTime]
            ,a.[LastModifierId]
            ,a.[IsDeleted]
            ,a.[DeleterId]
            ,a.[DeletionTime]
            ,a.[Saving]
            ,a.[VendorCategory]
            ,a.[BankAccount]
            ,a.[InvoiceAddress]
            ,a.[InvoiceFax]
            ,a.[InvoiceTitle]
            ,a.[OpenBank]
            ,a.[TotalAmount]
            ,a.[TotalAmountTax]
            ,a.[ApplyUserBuName]
            ,a.[ApplyUserName]
            ,a.[VendorName]
            ,a.[IsLate]
            ,a.[ApsPorperty]
            ,a.[CurrencySymbol]
            ,a.[PhoneNumber]
            ,a.[PRId]
            ,a.[ApprovedDate]
            ,a.[VendorCode]
            ,a.[ApplyUserBuToDeptName]
            ,a.[ApplyUserDept]
      from #PurPOApplications_tmp a
      WHERE NOT EXISTS (SELECT * FROM PLATFORM_ABBOTT_Dev.dbo.PurPOApplications_tmp WHERE ProcInstId = a.ProcInstId and ApplicationCode = a.ApplicationCode)
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into PLATFORM_ABBOTT_Dev.dbo.PurPOApplications_tmp from #PurPOApplications_tmp
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END 

END

