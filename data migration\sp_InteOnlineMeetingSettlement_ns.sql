CREATE PROCEDURE dbo.sp_InteOnlineMeetingSettlement_ns
AS 
begin
	select 
a.[Id],
UPPER(b.id)  as [PRApplicationId],
a.[SerialNumber],
a.[No],
a.[VendorCode],
a.[PdfUrl],
a.[PayAmount],
a.[ModifyRemark],
a.[ModifyAmountRemark],
a.[Executive],
a.[ExecutiveMail],
a.[StartDate],
a.[ActualNumber],
a.[ConcurrencyStamp],
a.[CreationTime],
a.[CreatorId],
a.[DeleterId],
a.[DeletionTime],
a.[ExtraProperties],
a.[IsDeleted],
a.[LastModificationTime],
a.[LastModifierId],
UPPER(c.id) as [PRDetailId]
into #InteOnlineMeetingSettlement
from PLATFORM_ABBOTT_Dev.dbo.InteOnlineMeetingSettlement_tmp a
left join PLATFORM_ABBOTT_Dev.dbo.PurPRApplications_tmp  b
on a.PRApplicationId =b.ApplicationCode 
left join PLATFORM_ABBOTT_Dev.dbo.PurPRApplicationDetails_tmp c
on b.ProcInstId =c.ProcInstId and c.RowNo =SUBSTRING(a.PRDetailId,12,len(a.PRDetailId)-11) 
IF OBJECT_ID(N'PLATFORM_ABBOTT_Dev.dbo.InteOnlineMeetingSettlement', N'U') IS NOT NULL
BEGIN
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
    drop table PLATFORM_ABBOTT_Dev.dbo.InteOnlineMeetingSettlement
    select *
    into PLATFORM_ABBOTT_Dev.dbo.InteOnlineMeetingSettlement from #InteOnlineMeetingSettlement
	END
	ELSE
	BEGIN
    --落成实体表
    select *
    into PLATFORM_ABBOTT_Dev.dbo.InteOnlineMeetingSettlement from #InteOnlineMeetingSettlement
	  PRINT(N' 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END


end
