create proc sp_OECPSASpeakerLimitUseHistorys
as
begin
	--临时表存储【讲者】解析后的数据
	select 
		a.ProcInstId,
		TRIM(REPLACE(REPLACE(a.IDNumber,'F',''),'M','')) IDNumber,
		a.No,
		a.rmb<PERSON>mount,
		a.expectedDate
	into [#ODS_AUTO_BIZ_T_ProcurementApplication_Info_PR]
	from [ODS_AUTO_BIZ_T_ProcurementApplication_Info_PR] a
	where a.payMent='AR' and ISNULL(a.re_id,'')='' and isnull(TRIM(REPLACE(REPLACE(a.IDNumber,'F',''),'M','')),'')<>''
	
	--临时表存储【备选讲者】解析后的数据
	select
		a.ProcInstId,
		TRIM(REPLACE(REPLACE(a.IDNumber,'F',''),'M','')) IDNumber,
		a.No,
		a.Amount
	into [#ODS_AUTO_BIZ_T_ProcurementApplication_Info_Alternative]
	from [ODS_AUTO_BIZ_T_ProcurementApplication_Info_Alternative] a
	where isnull(TRIM(REPLACE(REPLACE(a.IDNumber,'F',''),'M','')),'')<>''

	--收货信息，由于有重复数据，这里需要按单号取最新一条
	select 
		t.*
	into #GoodsRecipt
	from
	(
		select
			a.serialNumber,
			a.processStatus,
		ROW_NUMBER() over(PARTITION BY a.serialNumber,a.processStatus ORDER BY a.applicationDate desc) RowNo
		from ODS_form_2ce08373394a4d4cace676978bfa2984 a
	)t
	where t.RowNo=1

	--PR消耗
	select
		t.IDNumber,
		t.VendorId,
		'' [ComPSALimitId],
		4 [OperateType],
		2 [OperDetailType],
		t.PRApplicationCode,
		t.PrApplicationId,
		t.BuId,
		sum(t.Times) Times,
		sum(t.rmbAmount) [Amount],
		'' [Doc],
		'{}' [ExtraProperties],
		'' [ConcurrencyStamp],
		max(t.CreationTime) CreationTime,
		t.CreatorId,
		'' [LastModificationTime],
		'' [LastModifierId],
		0 [IsDeleted],
		'' [DeleterId],
		'' [DeletionTime],
		'' [PsaExternalId],
		isnull(max(t.EffectiveDate),max(t.CreationTime)) EffectiveDate,
		t.[Identity]
	into #OECPSASpeakerLimitUseHistorys_tmp
	from
	(
		--查询主讲者
		select
			a.IDNumber,
			vendor.VendorId,
			pr.ApplicationCode PRApplicationCode,
			pr.ID [PrApplicationId],
			pr.ApplyUserBu [BuId],
			1 Times,
			a.rmbAmount,
			prInfo.applicationDate [CreationTime],
			prInfo.applicantEmpId [CreatorId],
			a.expectedDate [EffectiveDate],
			form1.processStatus prStatus,
			gr.processStatus grStatus,
			N'主讲' [Identity]
		from [#ODS_AUTO_BIZ_T_ProcurementApplication_Info_PR] a
		join VendorPersonals vendor on a.IDNumber=vendor.CardNo
		join [ODS_AUTO_BIZ_T_ProcurementApplication_Info] prInfo on a.ProcInstId=prInfo.ProcInstId
		join PurPRApplications pr on prInfo.serialNumber=pr.ApplicationCode
		join ODS_Form_6e32f7dd7c4e49aea79949009c3bf7ae form1 on a.ProcInstId=form1.ProcInstId and form1.processStatus not in (N'发起人终止',N'终止')
		join ODS_T_Pur_PRItems_Info prItem on a.ProcInstId=prItem.ProcInstId and a.No=prItem.RowNumber
		left join #GoodsRecipt gr on gr.serialNumber=prItem.GRFormCode
		union
		--查询备选讲者
		select
			alternative.IDNumber,
			vendor.VendorId,
			pr.ApplicationCode PRApplicationCode,
			pr.ID [PrApplicationId],
			pr.ApplyUserBu [BuId],
			1 Times,
			alternative.Amount rmbAmount,
			prInfo.applicationDate [CreationTime],
			prInfo.applicantEmpId [CreatorId],
			a.expectedDate [EffectiveDate],
			form1.processStatus prStatus,
			gr.processStatus grStatus,
			N'备讲' [Identity]
		from [#ODS_AUTO_BIZ_T_ProcurementApplication_Info_PR] a
		join [#ODS_AUTO_BIZ_T_ProcurementApplication_Info_Alternative] alternative on a.ProcInstId=alternative.ProcInstId and a.No=alternative.No
		join VendorPersonals vendor on alternative.IDNumber=vendor.CardNo
		join [ODS_AUTO_BIZ_T_ProcurementApplication_Info] prInfo on a.ProcInstId=prInfo.ProcInstId
		join PurPRApplications pr on prInfo.serialNumber=pr.ApplicationCode
		join ODS_Form_6e32f7dd7c4e49aea79949009c3bf7ae form1 on a.ProcInstId=form1.ProcInstId and form1.processStatus not in (N'审批完毕，等待关闭',N'财务关闭',N'完成',N'发起人终止',N'终止')
		join ODS_T_Pur_PRItems_Info prItem on a.ProcInstId=prItem.ProcInstId and alternative.No=prItem.RowNumber
		left join #GoodsRecipt gr on gr.serialNumber=prItem.GRFormCode
		union
		--查询第三方讲者
		select
			vp.CardNo IDNumber,
			vp.VendorId,
			pr.ApplicationCode PRApplicationCode,
			pr.ID PrApplicationId,
			pr.ApplyUserBu BuId,
			1 Times,
			a.Amount,
			prInfo.applicationDate [CreationTime],
			prInfo.applicantEmpId [CreatorId],
			prInfo.applicationDate [EffectiveDate],
			'' prStatus,
			'' grStatus,
			N'第三方讲者' [Identity]
		from ODS_T_YearSupplierCount a
		join ODS_BPCS_AVM avm on a.SupplierCode=avm.VENDOR and a.SupplierName=avm.VNDNAM
		join VendorPersonals vp on TRIM(REPLACE(REPLACE(avm.VMXCRT,'F',''),'M',''))=vp.CardNo
		join PurPRApplications pr on a.PRNO=pr.ApplicationCode
		join [ODS_AUTO_BIZ_T_ProcurementApplication_Info] prInfo on a.PRNO=prInfo.serialNumber
	)t
	where (t.[Identity]=N'主讲' and (t.grStatus is null or t.grStatus not in(N'发起人终止',N'终止')))
	or (t.[Identity]=N'备讲' and t.grStatus is null)
	or t.[Identity]=N'第三方讲者'
	group by t.IDNumber,t.VendorId,t.PRApplicationCode,t.PrApplicationId,t.BuId,t.CreatorId,t.[Identity]

	--将临时表的数据insert到tmp表
	 IF OBJECT_ID(N'OECPSASpeakerLimitUseHistorys_tmp', N'U') IS NOT NULL
		drop table OECPSASpeakerLimitUseHistorys_tmp;
	select * into OECPSASpeakerLimitUseHistorys_tmp from #OECPSASpeakerLimitUseHistorys_tmp;
	PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'));
end