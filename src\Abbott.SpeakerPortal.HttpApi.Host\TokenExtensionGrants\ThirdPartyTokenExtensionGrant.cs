﻿using System.Collections.Generic;
using System.Text.Json;
using System.Threading.Tasks;

using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Models;
using Abbott.SpeakerPortal.Utils;

using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

using OpenIddict.Abstractions;
using OpenIddict.Server.AspNetCore;

using Volo.Abp.Domain.Repositories;
using Volo.Abp.OpenIddict;
using Volo.Abp.OpenIddict.Applications;
using Volo.Abp.OpenIddict.ExtensionGrantTypes;

using IdentityUser = Volo.Abp.Identity.IdentityUser;

namespace Abbott.SpeakerPortal.TokenExtensionGrants
{
    public class ThirdPartyTokenExtensionGrant : BaseTokenExtensionGrant
    {
        public const string ExtensionGrantName = GrantTypes.ThirdParty;

        public override string Name => ExtensionGrantName;

        public override async Task<IActionResult> HandleAsync(ExtensionGrantContext context)
        {
            try
            {
                var timestamp = context.HttpContext.Request.Form["timestamp"];
                var signature = context.HttpContext.Request.Form["signature"];

                //check timestamp
                if (!long.TryParse(timestamp, out long ts))
                {
                    var properties = new AuthenticationProperties(new Dictionary<string, string>
                    {
                        [OpenIddictServerAspNetCoreConstants.Properties.Error] = "invalid parameters",
                        [OpenIddictServerAspNetCoreConstants.Properties.ErrorDescription] = "The 'timestamp' is invalid."
                    });
                    return new ForbidResult(OpenIddictServerAspNetCoreDefaults.AuthenticationScheme, properties);
                }

                //get salt
                var openiddictApplicationExtra = await context.HttpContext.RequestServices.GetService<ICommonService>().GetOpenIddictApplicationExtraPropertyAsync(context.Request.ClientId);

                //check signature
                var validSignature = Md5Helper.Encrypt($"{context.Request.ClientId}{openiddictApplicationExtra.Salt}{timestamp}");
                // 与CSS系统和SOI系统对接时signature使用SHA256加密
                if (context.HttpContext.Request.Form["client_id"] == ClientIdScopeConsts.CSS || context.HttpContext.Request.Form["client_id"] == ClientIdScopeConsts.SOI)
                {
                    validSignature = Sha256Helper.Hash($"{context.Request.ClientId}{openiddictApplicationExtra.Salt}{timestamp}");
                }
                if (!string.Equals(signature, validSignature))
                {
                    var properties = new AuthenticationProperties(new Dictionary<string, string>
                    {
                        [OpenIddictServerAspNetCoreConstants.Properties.Error] = "invalid parameters",
                        [OpenIddictServerAspNetCoreConstants.Properties.ErrorDescription] = "The 'signature' is invalid."
                    });
                    return new ForbidResult(OpenIddictServerAspNetCoreDefaults.AuthenticationScheme, properties);
                }

                //check user
                var userRepository = context.HttpContext.RequestServices.GetService<IRepository<IdentityUser>>();
                var user = await userRepository.FirstOrDefaultAsync(a => a.UserName == context.Request.ClientId);

                if (user == null || user.IsDeleted || !user.IsActive)
                {
                    await SecurityLog(context, context.Request.ClientId, context.Request.ClientId, OpenIddictSecurityLogActionConsts.LoginInvalidUserName);
                    var properties = new AuthenticationProperties(new Dictionary<string, string>
                    {
                        [OpenIddictServerAspNetCoreConstants.Properties.Error] = OpenIddictConstants.Errors.AccessDenied,
                        [OpenIddictServerAspNetCoreConstants.Properties.ErrorDescription] = "User does not exist or inactive !"
                    });
                    return new ForbidResult(OpenIddictServerAspNetCoreDefaults.AuthenticationScheme, properties);
                }

                return await Login(context, user);
            }
            catch
            {
                await SecurityLog(context, context.Request.ClientId, context.Request.ClientId, OpenIddictSecurityLogActionConsts.LoginFailed);
                throw;
            }
        }
    }
}

