create proc sp_OECPSASpeakerLimitUseHistorys_ns
as
begin
	--首次全部insert
	IF OBJECT_ID(N'OECPSASpeakerLimitUseHistorys', N'U') IS NULL
	begin
		select 
			NEWID() [Id],[VendorId],[ComPSALimitId],[OperateType],[OperDetailType],[PrApplicationId],[BuId],[Times],[Amount],[Doc],[ExtraProperties],[ConcurrencyStamp],[CreationTime],[CreatorId],[LastModificationTime],[LastModifierId],[IsDeleted],[DeleterId],[DeletionTime],[PsaExternalId],[EffectiveDate]
		into OECPSASpeakerLimitUseHistorys
		from OECPSASpeakerLimitUseHistorys_tmp;
		PRINT(N'首次新增完成');
	end
	else--否则upsert
	begin
		--update存量
		update t set
			t.[VendorId]=tmp.[VendorId],
			t.[ComPSALimitId]=tmp.[ComPSALimitId],
			t.[OperateType]=tmp.[OperateType],
			t.[OperDetailType]=tmp.[OperDetailType],
			t.[PrApplicationId]=tmp.[PrApplicationId],
			t.[BuId]=tmp.[BuId],
			t.[Times]=tmp.[Times],
			t.[Amount]=tmp.[Amount],
			t.[Doc]=tmp.[Doc],
			t.[PsaExternalId]=tmp.[PsaExternalId],
			t.[EffectiveDate]=tmp.[EffectiveDate],
			t.[ExtraProperties]=tmp.[ExtraProperties],
			t.[ConcurrencyStamp]=tmp.[ConcurrencyStamp],
			t.[CreationTime]=tmp.[CreationTime],
			t.[CreatorId]=tmp.[CreatorId],
			t.[LastModificationTime]=tmp.[LastModificationTime],
			t.[LastModifierId]=tmp.[LastModifierId],
			t.[IsDeleted]=tmp.[IsDeleted],
			t.[DeleterId]=tmp.[DeleterId],
			t.[DeletionTime]=tmp.[DeletionTime]
		from OECPSASpeakerLimitUseHistorys t join OECPSASpeakerLimitUseHistorys_tmp tmp on t.VendorId=tmp.VendorId and t.[PrApplicationId]=tmp.[PrApplicationId];
		PRINT(N'修改存量数据完成');

		--insert增量
		insert OECPSASpeakerLimitUseHistorys select NEWID() [Id],[VendorId],[ComPSALimitId],[OperateType],[OperDetailType],[PrApplicationId],[BuId],[Times],[Amount],[Doc],[ExtraProperties],[ConcurrencyStamp],[CreationTime],[CreatorId],[LastModificationTime],[LastModifierId],[IsDeleted],[DeleterId],[DeletionTime],[PsaExternalId],[EffectiveDate]
		from OECPSASpeakerLimitUseHistorys_tmp tmp where not exists(select * from OECPSASpeakerLimitUseHistorys where VendorId=tmp.VendorId and [PrApplicationId]=tmp.[PrApplicationId]);
		PRINT(N'新增增量数据完成');
	end
end