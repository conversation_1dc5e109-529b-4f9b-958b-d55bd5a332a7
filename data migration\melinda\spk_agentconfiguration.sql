select
		newid() [spk_agentconfigurationid],
		a.spk_businessflowtypeid [spk_businessflowname],
		a.StaffId [spk_originalapprover],
		a.AgentId [spk_agent],
		a.StartTime [spk_startDate],
		a.EndTime [spk_endDate],
		a.Description [spk_remark],
		a.Title [spk_name],
		a.IsRemind [spk_isnotifyoriginaloperator],
		'' [spk_numberofproxies],
		a.BpmId _BpmId,
		a.ProcessName _WorkflowType,
		a.CreatorId _Creator,
		a.IsEnable _Status,
		a.CreateTime _CreationTime,
        a.flg
	into #spk_agentconfiguration
	from [spk_agentconfiguration_tmp] a

IF OBJECT_ID(N'dbo.spk_agentconfiguration', N'U') IS NOT NULL
BEGIN
	update a 
	set a.spk_businessflowname           = b.spk_businessflowname
        ,a.spk_originalapprover          = b.spk_originalapprover
        ,a.spk_agent                     = b.spk_agent
        ,a.spk_startDate                 = b.spk_startDate
        ,a.spk_endDate                   = b.spk_endDate
        ,a.spk_remark                    = b.spk_remark
        ,a.spk_name                      = b.spk_name
        ,a.spk_isnotifyoriginaloperator  = b.spk_isnotifyoriginaloperator
        ,a.spk_numberofproxies           = b.spk_numberofproxies
        ,a._BpmId                        = b._BpmId
        ,a._WorkflowType                 = b._WorkflowType
        ,a._Creator                      = b._Creator
        ,a._Status                       = b._Status
        ,a._CreationTime                 = b._CreationTime
        ,a.flg                           = b.flg
    from spk_agentconfiguration a
    join #spk_agentconfiguration b on a._BpmId = b._BpmId
    
    insert into spk_agentconfiguration
    select a.spk_agentconfigurationid
          ,a.spk_businessflowname
          ,a.spk_originalapprover
          ,a.spk_agent
          ,a.spk_startDate
          ,a.spk_endDate
          ,a.spk_remark
          ,a.spk_name
          ,a.spk_isnotifyoriginaloperator
          ,a.spk_numberofproxies
          ,a._BpmId
          ,a._WorkflowType
          ,a._Creator
          ,a._Status
          ,a._CreationTime
          ,a.flg
    from #spk_agentconfiguration a
    where not exists (select * from spk_agentconfiguration where a._BpmId = _BpmId)
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into dbo.spk_agentconfiguration from #spk_agentconfiguration
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END


