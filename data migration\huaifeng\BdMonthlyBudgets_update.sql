SELECT
 TRY_CONVERT(UNIQUEIDENTIFIER, a.[Id]) [Id]
,TRY_CONVERT(UNIQUEIDENTIFIER, b.[id] )[SubBudgetId]
,a.[BudgetAmount]
,a.[Status]
,isnull(a.[Month],1)Month
,a.[ExtraProperties]
,a.[ConcurrencyStamp]
,GETDATE() [CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, a.[CreatorId]) [CreatorId]
,GETDATE() [LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, a.[LastModifierId]) [LastModifierId]
,a.[IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, CAST(a.[DeleterId] AS nvarchar)) [DeleterId]
,GETDATE() [DeletionTime]
INTO #BdMonthlyBudgets
FROM PLATFORM_ABBOTT_STG.dbo.BdMonthlyBudgets AS a
left JOIN PLATFORM_ABBOTT_Stg.dbo.BdSubBudgets AS b
ON a.SubBudgetId = b.id
where SubBudgetId is not null
--drop table #BdMonthlyBudgets


USE Speaker_Portal_STG;

UPDATE a
SET
 a.[Id] = b.[Id]
,a.[SubBudgetId] = b.[SubBudgetId]
,a.[BudgetAmount] = b.[BudgetAmount]
,a.[Status] = b.[Status]
,a.[Month] = b.[Month]
,a.[ExtraProperties] = b.[ExtraProperties]
,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = b.[CreatorId]
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = b.[LastModifierId]
,a.[IsDeleted] = b.[IsDeleted]
,a.[DeleterId] = b.[DeleterId]
,a.[DeletionTime] = b.[DeletionTime]
FROM dbo.BdMonthlyBudgets a
left join #BdMonthlyBudgets  b
ON a.id=b.id


INSERT INTO dbo.BdMonthlyBudgets
SELECT
 [Id]
,[SubBudgetId]
,[BudgetAmount]
,[Status]
,[Month]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
FROM #BdMonthlyBudgets a
WHERE not exists (select * from dbo.BdMonthlyBudgets where id=a.id)





