CREATE PROCEDURE dbo.sp_PurGRApplicationDetails
AS 
BEGIN
	

	
--	drop table #PurGRApplicationDetails_tmp
select * into #PurGRApplicationDetails_tmp from (
SELECT
NEWID() AS Id,
A.ProcInstId AS GRApplicationId,--基于06-1迁移的申请单主信息，以ProcInstId定位对应的PurGRApplications.ID
case when PRItemType ='AR' then c.ProductId else '' end AS ProductId,--若AUTO_BIZ_T_GoodsReceiveApplication_Info中PRItemType="AR"则取该code匹配至对应的产品主数据后得到ID；如果为AP则留空(AP的产品来自于PO内人工录入的品名)
a.InvoiceType AS InvoiceType,--基于发票类型名称找回对应的字典Code
OrderCount AS OrderQuantity,--
Been_ReceivedNum_Per AS TotalReceivedQuantity,--已收货金额
case when ReceivingWay=N'按数量' then '1' 
when ReceivingWay=N'按金额' then '2' 
when ReceivingWay=N'按百分比' or ReceivingWay=N'按比例' then '3'  end AS DeliveryMethod,--按数量-1；按金额-2；按比例-3
ReceiverNum_Per AS CurrentReceivingQuantity,--
SignNum_Per AS CurrentSignedQuantity,--
b.Price AS UnitPrice,--
SignAmount AS ReceivedAmount,--
SignDate AS SigningDate,--
case when IsOnTime='false' then '0' 
when IsOnTime='true' then '1' end AS IsArrive,--false-0；true-1
'{}' AS ExtraProperties,--默认填写为"{}"
'' AS ConcurrencyStamp,--?
a.applicantEmpId AS CreationTime,--与对应的PurGRApplications记录保持一致即可
a.applicationDate AS CreatorId,--与对应的PurGRApplications记录保持一致即可
'' AS LastModificationTime,--默认为空
'' AS LastModifierId,--默认为空
'0' AS IsDeleted,--默认为0
'' AS DeleterId,--默认为空
'' AS DeletionTime,--默认为空
b.SignAmount/(1+a.TaxRate) AS AllocationAmount,--基于该金额除以(1+税率)得到不含税分摊金额，税率：AUTO_BIZ_T_GoodsReceiveApplication_Info.TaxRate
--预付款逻辑确认中"
p.id  AS PRDetailId,--基于该组合查询出对应的PurPRApplicationDetails的ID
SignAmount/(1+a.TaxRate)*a.ExchangeRate AS AllocationRMBAmount,--基于该金额除以(1+税率)再乘以汇率得到不含税分摊人民币金额，税率：AUTO_BIZ_T_GoodsReceiveApplication_Info.TaxRate，汇率：AUTO_BIZ_T_GoodsReceiveApplication_Info.ExchangeRate
--预付款逻辑确认中"
q.id AS PODetailId,--基于该组合查询出对应的PurPOApplicationDetails的ID
NoTaxTotalAmount AS PurchaseRMBAmount,--采购人民币不含税金额，根据POSerialNumber+GR_Item_No查询到对应行的金额
0 AS IsAdvancePayment,--预付款逻辑在确认中
case when SUBSTRING(a.POOrArNumber ,1,1)='O' then q.Content  COLLATE SQL_Latin1_General_CP1_CI_AS else GoodsName end  AS ProductName,
b.GR_ITEM_NO as PO_Item_No,
b.id as BPM_id,
0 as flag
FROM PLATFORM_ABBOTT.dbo.ODS_AUTO_BIZ_T_GoodsReceiveApplication_Info A --617207
JOIN (select *,ROW_NUMBER () over(partition by ProcInstId,PR_Item_No order by pasn desc) rn from PLATFORM_ABBOTT.dbo.ods_T_Pur_ProcessForm_GR_GRDetail_Info oabtgraip 
) B --648325
ON A.ProcInstId =B.ProcInstId and b.rn=1
left JOIN (select *,ROW_NUMBER () over(PARTITION by PR_Item_No,PRNo order by ProcInstId desc) rn from PLATFORM_ABBOTT.dbo.ODS_AUTO_BIZ_T_GoodsReceiveApplication_Info_PR) C
ON b.PR_Item_No =c.PR_Item_No and b.PRFormCode = c.PRNo and c.rn=1 --648373
left join (select *,ROW_NUMBER () over(partition by ApplicationCode order by ApplicationCode) rn from PLATFORM_ABBOTT.dbo.PurPRApplications_tmp) ppt 
on b.PRFormCode =ppt.ApplicationCode  and ppt.rn=1
join PLATFORM_ABBOTT.dbo.PurPRApplicationDetails_tmp p--有两笔PR单需要在PR明细排查
on  b.PR_Item_No=p.RowNo  and ppt.ProcInstId=p.ProcInstId 
left join (select *,ROW_NUMBER () over(partition by ApplicationCode order by ApplicationCode) rn from PLATFORM_ABBOTT.dbo.PurPOApplications_tmp) ppt1 
on b.POSerialNumber =ppt1.ApplicationCode  and ppt1.rn=1
left join PLATFORM_ABBOTT.dbo.PurPOApplicationDetails_tmp q
on ppt1.ProcInstId  =q.POApplicationId  and GR_Item_No=q.PORowNo 
left join (select a.ProcInstId ,a.serialNumber,b.PO_Item_No,b.NoTaxTotalAmount,b.id from ods_AUTO_BIZ_PurchaseOrderApplication_Info a 
join ods_AUTO_BIZ_PurchaseOrderApplication_Info_PO b
on a.ProcInstId =b.ProcInstId ) po
on  po.PO_Item_No =b.GR_Item_No  and b.POSerialNumber =po.serialNumber 
where a.AdvancePayment='false'   
UNION 
select 
newid() AS Id,--
a.ProcInstId AS GRApplicationId,--基于06-1迁移的申请单主信息，以ProcInstId定位对应的PurGRApplications.ID
case when SUBSTRING(a.POOrArNumber,1,1)='O' then '' else d.ProductIds end  AS ProductId,--若POOrArNumber为O开头，则留空；
--若为P开头，则取对应PurPRApplications.ProductIds"
a.InvoiceType AS InvoiceType,--基于发票类型名称找回对应的字典Code
case when SUBSTRING(a.POOrArNumber,1,1)='O' then c.Num else h.num end AS OrderQuantity,
--基于AUTO_BIZ_T_GoodsReceiveApplication_Info.POOrArNumber，查询AUTO_BIZ_PurchaseOrderApplication_Info.SerialNumber后，以查询出的ProcInstId查询AUTO_BIZ_PurchaseOrderApplication_Info_PO得到PO的明细记录
--特殊情况：如果POOrArNumber得到的是并非""O""开头的单号而是P开头的单号，说明是历史记录里AR类型行进行了预付款，此时需要基于该收货单的AUTO_BIZ_T_GoodsReceiveApplication_Info_PR的唯一一条明细行，查询PRNo+PR_Item_No后得到对应的唯一一条AUTO_BIZ_T_ProcurementApplication_Info_PR，按该行内的num填入"
'' AS TotalReceivedQuantity,--默认为空
'' AS DeliveryMethod,--默认为空
'' AS CurrentReceivingQuantity,--默认为空
'' AS CurrentSignedQuantity,--默认为空
case when SUBSTRING(a.POOrArNumber,1,1)='O' then c.Price else h.Price end  AS UnitPrice,--基于AUTO_BIZ_T_GoodsReceiveApplication_Info.POOrArNumber，查询AUTO_BIZ_PurchaseOrderApplication_Info.SerialNumber后，以查询出的ProcInstId查询AUTO_BIZ_PurchaseOrderApplication_Info_PO得到PO的明细记录
--特殊情况：如果POOrArNumber得到的是并非""O""开头的单号而是P开头的单号，说明是历史记录里AR类型行进行了预付款，此时需要基于该收货单的AUTO_BIZ_T_GoodsReceiveApplication_Info_PR的唯一一条明细行，查询PRNo+PR_Item_No后得到对应的唯一一条AUTO_BIZ_T_ProcurementApplication_Info_PR，按该行内的Price填入"
case when SUBSTRING(a.POOrArNumber,1,1)='O' then ROUND(a.ApportionTotalAmount/(isnull(sm,0)/NoTaxTotalAmount), 4) else a.ApportionTotalAmount end AS ReceivedAmount,--该金额是这次收货的总金额，需要以该金额乘以每行的分摊比例得到本次收货该特定品类的收货金额(如果是AP付款方式且对应仅有一条PO明细，或为AR付款方式，则直接填入收货金额即可)
--分摊比例计算方式：基于GR对应的AUTO_BIZ_PurchaseOrderApplication_Info_PO中各行的NoTaxTotalAmount加总后，再以每一行的NoTaxTotalAmount除以加总结果得到每一行的分摊比例"
'' AS SigningDate,--默认为空
'' AS IsArrive,--默认为空
'{}' AS ExtraProperties,--默认填写为"{}"
'' AS ConcurrencyStamp,--?
a.applicantEmpId AS CreationTime,--与对应的PurGRApplications记录保持一致即可
a.applicationDate AS CreatorId,--与对应的PurGRApplications记录保持一致即可
'' AS LastModificationTime,--默认为空
'' AS LastModifierId,--默认为空
'0' AS IsDeleted,--默认为0
'' AS DeleterId,--默认为空
'' AS DeletionTime,--默认为空
case when SUBSTRING(a.POOrArNumber,1,1)='O' then ROUND(a.ApportionTotalAmount/(isnull(sm,0)/NoTaxTotalAmount), 4) else a.ApportionTotalAmount end  AS AllocationAmount,--按上方ReceivedAmount计算结果填入即可
p.ID AS PRDetailId,--基于该组合查询出对应的PurPRApplicationDetails的ID
case when SUBSTRING(a.POOrArNumber,1,1)='O' then ROUND(a.ApportionTotalAmount/(isnull(sm,0)/NoTaxTotalAmount)*a.ExchangeRate,4) else a.ApportionTotalAmount*a.ExchangeRate end 
 AS AllocationRMBAmount,--按上方ReceivedAmount计算结果乘以汇率得到不含税分摊人民币金额，汇率：AUTO_BIZ_T_GoodsReceiveApplication_Info.ExchangeRate
q.id  AS PODetailId,--基于该组合查询出对应的PurPOApplicationDetails的ID(如果查询到的单据为AR类型则此处留空)
case when a.pRItemType='AR' then 0 else NoTaxTotalAmount end  AS PurchaseRMBAmount,--采购人民币不含税金额，根据POSerialNumber+GR_Item_No查询到对应行的金额(如果查询到的单据为AR类型则此处留空)
'1' AS IsAdvancePayment,--预付款默认填写为1
case when SUBSTRING(a.POOrArNumber ,1,1)='O' then q.Content  COLLATE SQL_Latin1_General_CP1_CI_AS 
else d.ProductIdsName 
end AS ProductName,--基于该行对应的PODetailId填入PO处已根据xml内容替换的完整名称Content
--若POOrArNumber为P开头，则取对应PurPRApplications.ProductIdsName"
c.Po_Item_No,
c.id as BPM_id,
1 as flag
 FROM PLATFORM_ABBOTT.dbo.ODS_AUTO_BIZ_T_GoodsReceiveApplication_Info A --626855
left join (select *,ROW_NUMBER () over(PARTITION by serialNumber order by serialNumber) rn from PLATFORM_ABBOTT.dbo.ODS_AUTO_BIZ_PurchaseOrderApplication_Info) b
on a.POOrArNumber =b.serialNumber and b.rn=1 --626855
join (select *,sum(NoTaxTotalAmount) over(PARTITION by ProcInstId) sm from PLATFORM_ABBOTT.dbo.ods_AUTO_BIZ_PurchaseOrderApplication_Info_PO) c
on c.ProcInstId =b.ProcInstId --648547
left join (select *,ROW_NUMBER () over(partition by ApplicationCode order by ApplicationCode) rn from PLATFORM_ABBOTT.dbo.PurPRApplications_tmp) d
on a.POOrArNumber =d.ApplicationCode  and d.rn=1 --648547
left join (select * ,ROW_NUMBER () over(PARTITION by ProcInstId order by PR_Item_No ) rn from PLATFORM_ABBOTT.dbo.ODS_AUTO_BIZ_T_GoodsReceiveApplication_Info_PR) g
on a.ProcInstId =g.ProcInstId  and g.rn=1--648547
left join (
select e.serialNumber,f.[No],f.num,f.Price,ROW_NUMBER () over(PARTITION by e.serialNumber,f.[No],f.num order by e.serialNumber,f.[No],f.num) rn from PLATFORM_ABBOTT.dbo.ods_AUTO_BIZ_T_ProcurementApplication_Info e
left join PLATFORM_ABBOTT.dbo.ods_AUTO_BIZ_T_ProcurementApplication_Info_PR f
on e.ProcInstId =f.ProcInstId
) h
on g.PRNo =h.serialNumber and g.PR_Item_No =h.[No] and h.rn=1--648547
left join (select *,ROW_NUMBER () over(partition by ApplicationCode order by ApplicationCode) rn from PLATFORM_ABBOTT.dbo.PurPRApplications_tmp) ppt 
on c.PRFormCode=ppt.ApplicationCode  and ppt.rn=1--648547
join PLATFORM_ABBOTT.dbo.PurPRApplicationDetails_tmp p
on ppt.ProcInstId  = p.ProcInstId  and p.RowNo = c.PR_Item_No --有两笔PR单需要在PR明细排查 --or (d.ProcInstId  = p.ProcInstId  and p.RowNo = g.PR_Item_No);
left join PLATFORM_ABBOTT.dbo.PurPOApplicationDetails_tmp q
on q.POApplicationId = c.ProcInstId  and q.PORowNo = c.Po_Item_No--648547
where a.AdvancePayment='true'
)B

--落成实体表
 IF OBJECT_ID(N'PLATFORM_ABBOTT.dbo.PurGRApplicationDetails_tmp', N'U') IS NOT NULL
BEGIN
	update a 
	set a.GRApplicationId          = b.GRApplicationId
        ,a.ProductId               = b.ProductId
        ,a.InvoiceType             = b.InvoiceType
        ,a.OrderQuantity           = b.OrderQuantity
        ,a.TotalReceivedQuantity   = b.TotalReceivedQuantity
        ,a.DeliveryMethod          = b.DeliveryMethod
        ,a.CurrentReceivingQuantity= b.CurrentReceivingQuantity
        ,a.CurrentSignedQuantity   = b.CurrentSignedQuantity
        ,a.UnitPrice               = b.UnitPrice
        ,a.ReceivedAmount          = b.ReceivedAmount
        ,a.SigningDate             = b.SigningDate
        ,a.IsArrive                = b.IsArrive
--        ,a.ExtraProperties         = b.ExtraProperties
--        ,a.ConcurrencyStamp        = b.ConcurrencyStamp
        ,a.CreationTime            = b.CreationTime
        ,a.CreatorId               = b.CreatorId
--        ,a.LastModificationTime    = b.LastModificationTime
--        ,a.LastModifierId          = b.LastModifierId
--        ,a.IsDeleted               = b.IsDeleted
--        ,a.DeleterId               = b.DeleterId
--        ,a.DeletionTime            = b.DeletionTime
        ,a.AllocationAmount        = b.AllocationAmount
        ,a.PRDetailId              = b.PRDetailId
        ,a.AllocationRMBAmount     = b.AllocationRMBAmount
        ,a.PODetailId              = b.PODetailId
        ,a.PurchaseRMBAmount       = b.PurchaseRMBAmount
--        ,a.IsAdvancePayment        = b.IsAdvancePayment
        ,a.ProductName             = b.ProductName
        ,a.PO_Item_No              = b.PO_Item_No
        ,a.BPM_id                  = b.BPM_id
--        ,a.flag                    = b.flag
       from PLATFORM_ABBOTT.dbo.PurGRApplicationDetails_tmp a 
       left join #PurGRApplicationDetails_tmp b 
       on isnull(b.GRApplicationId,1) = isnull(a.GRApplicationId,1) and isnull(b.Po_Item_No,1) = isnull(a.Po_Item_No,1) and isnull(b.bpm_id,'null') = isnull(a.bpm_id,'null')
       
       insert into PLATFORM_ABBOTT.dbo.PurGRApplicationDetails_tmp 
       select  a.Id
              ,a.GRApplicationId
              ,a.ProductId
              ,a.InvoiceType
              ,a.OrderQuantity
              ,a.TotalReceivedQuantity
              ,a.DeliveryMethod
              ,a.CurrentReceivingQuantity
              ,a.CurrentSignedQuantity
              ,a.UnitPrice
              ,a.ReceivedAmount
              ,a.SigningDate
              ,a.IsArrive
              ,a.ExtraProperties
              ,a.ConcurrencyStamp
              ,a.CreationTime
              ,a.CreatorId
              ,a.LastModificationTime
              ,a.LastModifierId
              ,a.IsDeleted
              ,a.DeleterId
              ,a.DeletionTime
              ,a.AllocationAmount
              ,a.PRDetailId 
              ,a.AllocationRMBAmount
              ,a.PODetailId
              ,a.PurchaseRMBAmount
              ,a.IsAdvancePayment
              ,a.ProductName
  ,a.PO_Item_No
     ,a.BPM_id
              ,a.flag
		from #PurGRApplicationDetails_tmp a 
		where not exists (select * from PLATFORM_ABBOTT.dbo.PurGRApplicationDetails_tmp 
		where isnull(GRApplicationId,1) = isnull(a.GRApplicationId,1) and isnull(Po_Item_No,1) = isnull(a.Po_Item_No,1) and isnull(bpm_id,'null') = isnull(a.bpm_id,'null'))
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into PLATFORM_ABBOTT.dbo.PurGRApplicationDetails_tmp from #PurGRApplicationDetails_tmp
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END

END;