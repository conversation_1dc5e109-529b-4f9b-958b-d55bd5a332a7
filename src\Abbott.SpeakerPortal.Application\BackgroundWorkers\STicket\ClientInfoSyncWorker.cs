﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Abbott.SpeakerPortal.Contracts.STicket;
using Hangfire;

namespace Abbott.SpeakerPortal.BackgroundWorkers.STicket
{
    public class ClientInfoSyncWorker : SpeakerPortalBackgroundWorkerBase
    {
        public ClientInfoSyncWorker()
        {
            //触发周期,每天凌晨1点
            CronExpression = Cron.Daily(1);
        }

        public override async Task DoWorkAsync(CancellationToken cancellationToken = default)
        {
            await LazyServiceProvider.LazyGetService<ISTicketService>().SyncClientInfos();
        }
    }
}
