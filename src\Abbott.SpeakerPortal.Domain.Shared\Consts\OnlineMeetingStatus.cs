﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Abbott.SpeakerPortal.Consts
{
    /// <summary>
    /// OM会议状态
    /// --1000-nextBpm会议推送成功
    ///--1001-om会议激活成功
    ///--1002-om会议结算状态推送成功
    ///--1003-nextBpm作废成功
    ///--1004-om作废成功
    ///--1005-nextBpm付款线下递交或者作废推送成功
    ///--1006-nextBpm打印页面推送成功
    ///--1007-nextBpm复审状态推送成功
    /// </summary>
    public class OnlineMeetingStatus
    {
        /// <summary>
        /// nextBpm会议推送成功
        /// </summary>
        public const string NexBpmPushed = "1000";

        /// <summary>
        /// om会议激活成功
        /// </summary>
        public const string OmActivated = "1001";

        /// <summary>
        /// om会议结算状态推送成功
        /// </summary>
        public const string OmSettledPushed = "1002";

        /// <summary>
        /// nextBpm作废成功
        /// </summary>
        public const string NexBpmDeprecated = "1003";

        /// <summary>
        /// om作废成功
        /// </summary>
        public const string OmDeprecated = "1004";

        /// <summary>
        /// nextBpm付款线下递交或者作废推送成功
        /// </summary>
        public const string NexBpmOfflineDeliveryAndDeprecatePushed = "1005";

        /// <summary>
        /// nextBpm打印页面推送成功
        /// </summary>
        public const string NexBpmPrintPagePushed = "1006";

        /// <summary>
        /// nextBpm复审状态推送成功
        /// </summary>
        public const string NexBpmReviewStatusPushed = "1007";
    }
}
