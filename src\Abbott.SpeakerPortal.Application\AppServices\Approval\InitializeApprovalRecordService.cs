﻿using Abbott.SpeakerPortal.Contracts.Approval;
using Abbott.SpeakerPortal.Contracts.Common.Approve;
using Abbott.SpeakerPortal.Contracts.Common.PowerApp;
using Abbott.SpeakerPortal.Entities.Approval;
using Abbott.SpeakerPortal.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static OpenIddict.Abstractions.OpenIddictConstants;
using System.Text.Json;
using Abbott.SpeakerPortal.AppServices.System;
using Microsoft.Extensions.Logging;

namespace Abbott.SpeakerPortal.AppServices.Approval
{
    public class InitializeApprovalRecordService : SpeakerPortalAppService, IInitializeApprovalRecordService
    {
        private readonly ILogger<InitializeApprovalRecordService> _logger;
        public InitializeApprovalRecordService(ILogger<InitializeApprovalRecordService> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// 插入记录
        /// </summary>
        /// <returns></returns>
        public async Task InsertInitApprovalRecordAsync(InitializeApprovalRecordDto initApprovalRecord)
        {
            var approvalRecordRepository = LazyServiceProvider.LazyGetService<IInitializeApprovalRecordRepository>();
            var approvalRecord = ObjectMapper.Map<InitializeApprovalRecordDto, InitializeApprovalRecord>(initApprovalRecord);
            await approvalRecordRepository.InsertAsync(approvalRecord);
        }

        /// <summary>
        /// 推送审批记录
        /// </summary>
        /// <returns></returns>
        public async Task PushApprovalRecordAsync()
        {
            try
            {
                var approvalRecordRepository = LazyServiceProvider.LazyGetService<IInitializeApprovalRecordRepository>();
                var queryApprovalRecord = await approvalRecordRepository.GetQueryableAsync();
                InitApprovalRecordStatus[] status = [InitApprovalRecordStatus.Pending, InitApprovalRecordStatus.Failed];
                var pushData = queryApprovalRecord.Where(a => status.Contains(a.Status) && a.Count <= a.MaxRetries).ToList();
                var approveService = LazyServiceProvider.LazyGetService<IApproveService>();
                foreach (var push in pushData)
                {
                    var createApproval = JsonSerializer.Deserialize<CreateApprovalDto>(push.RequestContent);
                    var (success, message) = await approveService.InitiateApprovalAsync(push.InstanceId, createApproval);
                    if (success)
                    {
                        push.Status = InitApprovalRecordStatus.Succeeded;
                        var isAddRecord = WorkflowTypeName.PaymentFinanceApprove != createApproval.WorkflowType;//付款财务审批记录不在这里记录（按原逻辑处理）
                        await approveService.AddApprovalRecordAndAgentAsync(push.InstanceId, createApproval, isAddRecord);//审批记录成功后处理
                    }
                    else
                    {
                        push.Status = InitApprovalRecordStatus.Failed;
                    }
                    push.Count++;
                    push.ResponseContent = message;
                }
                await approvalRecordRepository.UpdateManyAsync(pushData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex,$"PushApprovalRecordAsync Errorr :{ex.Message}");
            }
        }
    }
}
