﻿using System;
using System.Collections.Generic;
using System.Text;

using Abbott.SpeakerPortal.Dtos;

namespace Abbott.SpeakerPortal.Contracts.Concur.OrgMapping
{
    public class QueryOrgMappingRequestDto : PagedDto
    {
        /// <summary>
        /// 原名
        /// </summary>
        public string OriginalName { get; set; }

        /// <summary>
        /// 转换后名称
        /// </summary>
        public string NewName { get; set; }
    }
}
