CREATE PROCEDURE dbo.sp_PurPAApplicationDetails_ns
AS 
BEGIN
	
select 
a.[Id]
,ppt.id as [PurPAApplicationId]
,ppt1.id as [PRId]
,ppt2.id as  [POId]
,ppt3.b_id as [PRDetailId]
,ppdt.id [PODetailId]
,pgdht.Id  as [GRHistoryId]
,sp.spk_NexBPMCode  as [ProductId]
,a.[ProductName]
,a.[InvoiceType]
,a.[TaxRate]
,a.[PaymentAmount]
,a.[ExtraProperties]
,a.[ConcurrencyStamp]
,a.[CreationTime]
,ss.spk_NexBPMCode  as [CreatorId]
,a.[LastModificationTime]
,a.[LastModifierId]
,a.[IsDeleted]
,a.[DeleterId]
,a.[DeletionTime]
,GRH.GRApplicationDetailId  as [GRApplicationDetailId]
,ROW_NUMBER () over(PARTITION by a.[PurPAApplicationId]
,a.[PRId]
,a.[POId]
,a.[PRDetailId]
,a.[PODetailId]
,a.[GRHistoryId]
,a.[ProductId]
,a.[ProductName]
,a.[InvoiceType]
,a.[TaxRate]
,a.[PaymentAmount]
,a.[ExtraProperties]
,a.[ConcurrencyStamp]
,a.[CreationTime]
,a.[CreatorId]
,a.[LastModificationTime]
,a.[LastModifierId]
,a.[IsDeleted]
,a.[DeleterId]
,a.[DeletionTime]
,a.[GRApplicationDetailId] order by a.id desc) rn1
into #PurPAApplicationDetails
from PurPAApplicationDetails_tmp a
left join (select *,ROW_NUMBER () over(PARTITION by ApplicationCode order by ProcInstId desc ) rn  from PurPAApplications_tmp) ppt 
on a.PurPAApplicationId  COLLATE SQL_Latin1_General_CP1_CI_AS=ppt.ApplicationCode  and ppt.rn=1
left join (select *,ROW_NUMBER () over(PARTITION by ApplicationCode order by ProcInstId desc ) rn  from PurPRApplications_tmp ) ppt1 
on a.PRId  COLLATE SQL_Latin1_General_CP1_CI_AS=ppt1.ApplicationCode  and ppt1.rn=1
left join (select *,ROW_NUMBER () over(PARTITION by ApplicationCode order by ProcInstId desc ) rn  from PurPOApplications_tmp ) ppt2 
on a.POId  COLLATE SQL_Latin1_General_CP1_CI_AS=ppt2.ApplicationCode  and ppt2.rn=1
left join (select a.*,b.id AS b_id,B.rowno  from PurPRApplications_tmp  A
			LEFT JOIN PurPRApplicationDetails_tmp  B 
			ON A.ProcInstId  =B.ProcInstId) ppt3
on a.PRDetailId=concat(ppt3.ApplicationCode,ppt3.RowNO)
left join (select a.*,b.id AS b_id,B.rowno  from PurPOApplications_tmp  A
			LEFT JOIN PurPRApplicationDetails_tmp  B 
			ON A.ProcInstId  =B.ProcInstId) ppt4
on a.PRDetailId=concat(ppt4.ApplicationCode,ppt4.RowNO)
left join PurGRApplicationDetailHistorys_tmp pgdht 
on a.GRHistoryId =pgdht.GRApplicationId
left join ODS_AUTO_BIZ_T_GoodsReceiveApplication_Info oabtgrai 
on a.ProductId = oabtgrai.ProcInstId 
left join (select *,ROW_NUMBER () over(PARTITION by ProcInstId	order by ProcInstId) rn from ODS_AUTO_BIZ_T_GoodsReceiveApplication_Info_PR) oabtgraip 
on oabtgraip.ProcInstId = oabtgrai.ProcInstId  and oabtgraip.rn=1 and oabtgrai.pRItemType ='AR'
left join spk_productmasterdata sp 
on a.ProductId =sp.spk_BPMCode and oabtgrai.pRItemType ='AR'
left join spk_staffmasterdata ss 
on a.CreatorId=ss.bpm_id 
left join (select ppdt.id,concat(ppt.ApplicationCode,'_',ppdt.PORowNo) PODetailId,ROW_NUMBER () over(PARTITION by ppt.ApplicationCode order by ppt.ProcInstId desc ) rn from PurPOApplications_tmp  ppt 
					join PurPOApplicationDetails_tmp ppdt  
					on ppdt.POApplicationId=ppt.ProcInstId) ppdt
on a.PODetailId =ppdt.PODetailId COLLATE SQL_Latin1_General_CP1_CI_AS  and ppdt.rn=1
left join PurGRApplicationDetailHistorys GRH
on GRH.Id =pgdht.id


IF OBJECT_ID(N'PLATFORM_ABBOTT_STG.dbo.PurPAApplicationDetails ', N'U') IS NOT NULL
	BEGIN
		drop table PLATFORM_ABBOTT_STG.dbo.PurPAApplicationDetails
		select *
        into PLATFORM_ABBOTT_STG.dbo.PurPAApplicationDetails from #PurPAApplicationDetails
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into PLATFORM_ABBOTT_STG.dbo.PurPAApplicationDetails from #PurPAApplicationDetails
    -- select * from #vendor_tbl
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END

END
