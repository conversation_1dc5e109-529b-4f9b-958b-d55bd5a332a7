﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace Abbott.SpeakerPortal.Contracts.Integration.Dspot
{
    /// <summary>
    /// 全流程报表，一条满足条件的数据的每个阶段的Id（PR、PO、GR、PA、PAInvoice、出纳）
    /// </summary>
    public class DspotReportContentIdListDto
    {
        public List<Guid> PRIds { get; set; }

        public List<Guid?> POIds { get; set; }

        public List<Guid?> GRIds { get; set; }

        public List<Guid?> PAIds { get; set; }

        public List<Guid?> PAInvoiceIds { get; set; }

        public static DspotReportContentIdListDto GetFromReportIds(List<DspotReportContentIdDto> reportIds)
        {
            if (reportIds?.Any() != true)
            {
                return null;
            }

            return new DspotReportContentIdListDto
            {
                PRIds = reportIds.Select(a => a.PRId).ToList(),
                POIds = reportIds.Where(a => a.POId != null).Select(a => a.POId).ToList(),
                GRIds = reportIds.Where(a => a.GRId != null).Select(a => a.GRId).ToList(),
                PAIds = reportIds.Where(a => a.PAId != null).Select(a => a.PAId).ToList(),
                PAInvoiceIds = reportIds.Where(a => a.PAInvoiceId != null).Select(a => a.PAInvoiceId).ToList(),
            };
        }
    }
}