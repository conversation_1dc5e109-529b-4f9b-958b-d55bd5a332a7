﻿using Abbott.SpeakerPortal.Contracts.Common.Attachment;
using Abbott.SpeakerPortal.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Net.NetworkInformation;
using System.Text;
using System.Text.Json.Serialization;
using static Abbott.SpeakerPortal.Enums.DataverseEnums.Staff;

namespace Abbott.SpeakerPortal.Contracts.Budget.Subbudget
{
    public class GetSubbudgetDetailResponseDto
    {
        /// <summary>
        /// 子预算Id
        /// </summary>
        public Guid Id { get; set; }
        /// <summary>
        /// 子预算编号
        /// </summary>
        public string Code { get; set; }
        /// <summary>
        /// BuId
        /// </summary>
        public Guid BuId { get; set; }
        /// <summary>
        /// 成本中心
        /// </summary>
        public Guid CostCenterId { get; set; }
        /// <summary>
        /// 成本中心名称
        /// </summary>
        public string CostCenterName { get; set; }
        /// <summary>
        /// 大区
        /// </summary>
        public Guid RegionId { get; set; }
        /// <summary>
        /// 大区名称
        /// </summary>
        public string RegionName { get; set; }
        /// <summary>
        /// 负责人
        /// </summary>
        public Guid OwnerId { get; set; }
        /// <summary>
        /// 负责人名称
        /// </summary>
        public string OwnerName { get; set; }
        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 是否合规审计审批
        /// </summary>
        public bool IsComplicanceAudits { get; set; }

        /// <summary>
        /// 预算金额
        /// </summary>
        public decimal BudgetAmount { get; set; }
        /// <summary>
        /// 开启金额
        /// </summary>
        public decimal EnableAmount { get { return this.MonthlyBudgets.Where(m => m.Status).Sum(s => s.Amount); } }
        /// <summary>
        /// 已使用金额
        /// </summary>
        public decimal UsedAmount { get; set; }
        /// <summary>
        /// 可用金额
        /// </summary>
        public decimal AvailableAmount { get { return EnableAmount - UsedAmount; } }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 附件
        /// </summary>
        public List<UploadFileResponseDto> AttachmentInformation { get; set; }
        /// <summary>
        /// 月份预算
        /// </summary>
        public ICollection<MonthlyBudgetResponseDto> MonthlyBudgets { get; set; }
    }
}
