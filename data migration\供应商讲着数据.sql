CREATE TABLE PLATFORM_ABBOTT_Dev.dbo.Hospital_Cleaning_Data (
	Hospital_Data nvarchar(200) COLLATE Chinese_PRC_CI_AS NULL,
	city nvarchar(200) COLLATE Chinese_PRC_CI_AS NULL,
	province nvarchar(200) COLLATE Chinese_PRC_CI_AS NULL,
	NID nvarchar(200) COLLATE Chinese_PRC_CI_AS NULL,
	Repeating_Group nvarchar(200) COLLATE Chinese_PRC_CI_AS NULL,
	status nvarchar(200) COLLATE Chinese_PRC_CI_AS NULL,
	Reason nvarchar(200) COLLATE Chinese_PRC_CI_AS NULL,
	HCO_VID nvarchar(200) COLLATE Chinese_PRC_CI_AS NULL,
	corporate_name__v nvarchar(200) COLLATE Chinese_PRC_CI_AS NULL,
	alternate_name_1__v nvarchar(200) COLLATE Chinese_PRC_CI_AS NULL,
	administrative_area__v nvarchar(200) COLLATE Chinese_PRC_CI_AS NULL,
	locality__v nvarchar(200) COLLATE Chinese_PRC_CI_AS NULL,
	sub_administrative_area__v nvarchar(200) COLLATE Chinese_PRC_CI_AS NULL,
	address_line_1__v nvarchar(200) COLLATE Chinese_PRC_CI_AS NULL,
	District_Type nvarchar(200) COLLATE Chinese_PRC_CI_AS NULL,
	postal_code__v int NULL,
	hospital_grade__v nvarchar(200) COLLATE Chinese_PRC_CI_AS NULL,
	hco_type__v nvarchar(200) COLLATE Chinese_PRC_CI_AS NULL,
	hco_property__c nvarchar(200) COLLATE Chinese_PRC_CI_AS NULL,
	phone_1__v nvarchar(200) COLLATE Chinese_PRC_CI_AS NULL,
	organization_id__v nvarchar(200) COLLATE Chinese_PRC_CI_AS NULL,
	latitude__v nvarchar(200) COLLATE Chinese_PRC_CI_AS NULL,
	longitude__v nvarchar(200) COLLATE Chinese_PRC_CI_AS NULL,
	subscribeExponent nvarchar(200) COLLATE Chinese_PRC_CI_AS NULL
);
drop table PLATFORM_ABBOTT_Dev.dbo.Vendors_ns
select 
Id,
ApplicationId,
VendorCode,
OpenId,
UnionId,
HandPhone,
VendorType,
BuCode,
a.Status,
EpdId,
MndId,
VendorId,
ApsPorperty,
CertificateCode,
SPLevel,
AcademicLevel,
AcademicPosition,
BankCode,
BankCardNo,
BankCity,
BankNo,
ExtraProperties,
ConcurrencyStamp,
CreationTime,
CreatorId,
LastModificationTime,
LastModifierId,
IsDeleted,
DeleterId,
DeletionTime,
UserId,
PTId,
StandardHosDepId,
HospitalId,
HosDepartment,
AttachmentInformation,
Description,
DraftVersion,
PaymentTerm,
BankCardImg,
DPSCheck,
SignedStatus,
SignedVersion,
isnull(corporate_name__v,'') as HospitalName,
PTName,
StandardHosDepName
into PLATFORM_ABBOTT_Dev.dbo.Vendors_ns
from  PLATFORM_ABBOTT_Dev.dbo.Vendors a 
full join PLATFORM_ABBOTT_Dev.dbo.Hospital_Cleaning_Data b
on a.HospitalName=b.Hospital_Data
where VendorType in(1,2)



