﻿using Abbott.SpeakerPortal.Enums;
using MiniExcelLibs.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Reflection;
using System.Text;

using static Abbott.SpeakerPortal.Enums.Purchase;

namespace Abbott.SpeakerPortal.Contracts.Budget.Subbudget
{
    public class GetSubbudgetUseInfoResponseDto
    {
        /// <summary>
        /// 申请人姓名
        /// </summary>
        [ExcelColumnName("申请人")]
        public string ApplyUserName { get; set; }
        /// <summary>
        /// 申请日期
        /// </summary>
        [ExcelColumnName("申请日期")]
        public string ApplyTime { get; set; }
        /// <summary>
        /// 流程类型名称
        /// </summary>
        [ExcelColumnName("流程名称")]
        public string WorkflowTypeName { get; set; }
        /// <summary>
        /// 流程编号
        /// </summary>
        [ExcelColumnName("流程单号")]
        public string FlowNo { get; set; }
        /// <summary>
        /// 使用金额
        /// </summary>
        [ExcelColumnName("使用金额")]
        public decimal UseAmount { get; set; }
        /// <summary>
        /// 流程状态
        /// </summary>
        [ExcelIgnore]
        public PurPRApplicationStatus? FlowStatus { get; set; }
        /// <summary>
        /// 核销流程状态
        /// </summary>
        [ExcelIgnore]
        public STicketStatus? STicketStatus { get; set; }
        [ExcelColumnName("流程状态")]
        public string FlowStatusText { get; set; }
        //public string FlowStatusText => FlowStatus.GetType().GetField(FlowStatus.ToString()).GetCustomAttribute<DescriptionAttribute>().Description;
    }
}
