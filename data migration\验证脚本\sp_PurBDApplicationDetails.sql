CREATE PROCEDURE dbo.sp_PurBDApplicationDetails
AS 
BEGIN

select 
newid() AS Id,--自动生成的uuid
a.ProcInstId,
c.Id AS BDApplicationId,--基于04-1迁移的申请单主信息，以ProcInstId定位对应的PurPRApplications.ID
b.Id AS PRDetailId,--以该组合查询到对应的PR明细行ID并填入，若填写为空代表此处选择为“采购自定义”则无需关联PR明细行
a.Content AS Content,--
a.num AS Quantity,--
a.calculateunit AS Unit,--
a.price AS TotalAmount,--
a.PRFormCode AS ApplicationCode,--若填写为空代表此处选择为“采购自定义”则无需关联PR明细行
a.PRNumber AS RowNo,--若填写为空代表此处选择为“采购自定义”则无需关联PR明细行
'{}' AS ExtraProperties,--默认填写为"{}"
'' AS ConcurrencyStamp,--?
c.CreationTime AS CreationTime,--与对应的PurBDApplications记录保持一致即可
c.CreatorId AS CreatorId,--与对应的PurBDApplications记录保持一致即可
'' AS LastModificationTime,--默认为空
'' AS LastModifierId,--默认为空
'0' AS IsDeleted,--默认为0
'' AS DeleterId,--默认为空
'' AS DeletionTime,--默认为空
a.cost AS UnitPrice--
into #PurBDApplicationDetails_tmp
from PLATFORM_ABBOTT.dbo.ods_AUTO_BIZ_T_BiddingApplication_Info_BidPR a
left join PLATFORM_ABBOTT.dbo.PurPRApplicationDetails_tmp b
on a.PRNumber = b.RowNo
and a.PRFormCode = b.serialNumber
left join PLATFORM_ABBOTT.dbo.PurBDApplications_tmp c
on a.ProcInstId = c.ProcInstId



IF OBJECT_ID(N'PLATFORM_ABBOTT.dbo.PurBDApplicationDetails_tmp', N'U') IS NOT NULL
BEGIN
	update a 
	set 
		a.ProcInstId          = b.ProcInstId
	   ,a.BDApplicationId     = b.BDApplicationId
	   ,a.PRNumber            = b.PRNumber
       ,a.PRDetailId          = b.PRDetailId
       ,a.Content             = b.Content
       ,a.Quantity            = b.Quantity
       ,a.Unit                = b.Unit
       ,a.TotalAmount         = b.TotalAmount
       ,a.ApplicationCode     = b.ApplicationCode
       ,a.RowNo               = b.RowNo
       ,a.ExtraProperties     = b.ExtraProperties
       ,a.ConcurrencyStamp    = b.ConcurrencyStamp
       ,a.CreationTime        = b.CreationTime
       ,a.CreatorId           = b.CreatorId
       ,a.LastModificationTime= b.LastModificationTime
       ,a.LastModifierId      = b.LastModifierId
       ,a.IsDeleted           = b.IsDeleted
       ,a.DeleterId           = b.DeleterId
       ,a.DeletionTime        = b.DeletionTime
       ,a.UnitPrice           = b.UnitPrice
    from PLATFORM_ABBOTT.dbo.PurBDApplicationDetails_tmp a
    left join #PurBDApplicationDetails_tmp b
    on a.ProcInstId = b.ProcInstId and a.PRNumber = b.PRNumber 
    and a.PRDetailId = b.PRDetailId and a.Content = b.Content
    
    insert into PLATFORM_ABBOTT.dbo.PurBDApplicationDetails_tmp
    select Id
    	  ,a.ProcInstId
          ,a.BDApplicationId
          ,a.PRNumber
          ,a.PRDetailId
          ,a.Content
          ,a.Quantity
          ,a.Unit
          ,a.TotalAmount
          ,a.ApplicationCode
          ,a.RowNo
          ,a.ExtraProperties
          ,a.ConcurrencyStamp
          ,a.CreationTime
          ,a.CreatorId
          ,a.LastModificationTime
          ,a.LastModifierId
          ,a.IsDeleted
          ,a.DeleterId
          ,a.DeletionTime
          ,a.UnitPrice
	from #PurBDApplicationDetails_tmp a
	where NOT EXISTS (SELECT * FROM PLATFORM_ABBOTT.dbo.PurBDApplicationDetails_tmp WHERE ProcInstId = a.ProcInstId 
	and PRNumber = a.PRNumber and PRDetailId = a.PRDetailId and Content = a.Content)
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into PLATFORM_ABBOTT.dbo.PurBDApplicationDetails_tmp from #PurBDApplicationDetails_tmp
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END

END