﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.Approve;
using Abbott.SpeakerPortal.Contracts.Common.PowerApp;
using Abbott.SpeakerPortal.Contracts.Dataverse;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Contracts.Vendor.NonSpeaker;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.OECPSASpeakerLimits;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Entities.VendorFinancials;
using Abbott.SpeakerPortal.Entities.VendorOrgnizations;
using Abbott.SpeakerPortal.Entities.VendorPersonals;
using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Utils;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Internal;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using static Abbott.SpeakerPortal.Enums.Purchase;
using static Abbott.SpeakerPortal.Consts.EntitiesNameConsts;
using Abbott.SpeakerPortal.Entities.OECPSAComPSALimits;
using Newtonsoft.Json;
using Abbott.SpeakerPortal.Contracts.Purchase.PurGRApplication;
using Abbott.SpeakerPortal.Entities.Integration.Dspot;
using Abbott.SpeakerPortal.Contracts.OEC.SpeakerLimit;
using Abbott.SpeakerPortal.OEC.SpeakerLimit;
using Abbott.SpeakerPortal.Contracts.Budget.Subbudget;
using Abbott.SpeakerPortal.Contracts.Agent;
using Abbott.SpeakerPortal.Entities.Common.Workflow.WorkflowTasks;
using DocumentFormat.OpenXml.Drawing.Charts;
using Abbott.SpeakerPortal.Entities.Integration.EpdOnlineMeeting;
using Abbott.SpeakerPortal.Contracts.Integration.EpdOlineMeeting;
using Abbott.SpeakerPortal.Extension;
using Abbott.SpeakerPortal.Vendor.Speaker;
using Senparc.Weixin.WxOpen.Entities;
using Abbott.SpeakerPortal.Entities;
using System.Linq.Dynamic.Core;
using Microsoft.Extensions.Logging;

namespace Abbott.SpeakerPortal.AppServices.Purchase
{
    public partial class PurPRApplicationService : SpeakerPortalAppService, IPurPRApplicationService
    {
        /// <summary>
        /// 获取我发起的采购申请任务
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetAppliedByMeResponseDto>> GetAppliedByMe(GetAppliedByMeRequestDto request)
        {
            var queryPr = await LazyServiceProvider.LazyGetService<IPurPRApplicationReadonlyRepository>().GetQueryableAsync();
            var queryPrDetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailReadonlyRepository>().GetQueryableAsync();

            PurPRApplicationStatus[] pendingProcessings = [PurPRApplicationStatus.RejectedBack];
            PurPRApplicationStatus[] progressings = [PurPRApplicationStatus.Approving, PurPRApplicationStatus.WaitForClose];
            PurPRApplicationStatus[] completeds = [PurPRApplicationStatus.Rejected, PurPRApplicationStatus.ApplicantTerminate, PurPRApplicationStatus.Closed];

            //获取代理信息
            var agents = await LazyServiceProvider.LazyGetService<IAgencyService>().GetOriginalOperators(new GetAgentOperatorsRequestDto { BusinessTypeCategory = ResignationTransfer.TaskFormCategory.PurchaseRequestApplication });
            var userIds = agents.Select(a => a.Key).Distinct().ToArray();
            var principalIds = userIds.Where(x => x != CurrentUser.Id.Value).ToArray();
            var queryApply = queryPr
                .WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), a => a.ApplicationCode.Contains(request.ApplicationCode))
                .WhereIf(request.StartDate.HasValue, a => a.ApplyTime >= request.StartDate)
                .WhereIf(request.EndDate.HasValue, a => a.ApplyTime < request.EndDate.Value.Date.AddDays(1))
                .WhereIf(!string.IsNullOrEmpty(request.ApplyDept), a => a.ApplyUserDeptName.Contains(request.ApplyDept))
                .WhereIf(!string.IsNullOrEmpty(request.BudgetCode), a => a.SubBudgetCode.Contains(request.BudgetCode))
                .WhereIf(request.Company.HasValue, a => a.CompanyId == request.Company)
                .WhereIf(!string.IsNullOrEmpty(request.OnlineMeetingStatus), a => a.MeetingStatus == request.OnlineMeetingStatus)
                .Select(a => new { a.Id, a.ApplicationCode, a.ApplyUserId, a.TransfereeId, a.ApplyTime, a.ApplyUserDeptName, a.CostCenter, a.SubBudgetCode, a.CompanyId, a.TotalAmount, a.Status, a.IsEsignUsed, a.MeetingStatus, a.ApplyUserIdName, HasNonConfirmed = false, HasNonPushed = false });

            var query = queryApply.Where(a => (a.ApplyUserId == CurrentUser.Id.Value && !a.TransfereeId.HasValue) || CurrentUser.Id.Value == a.TransfereeId.Value || principalIds.ToHashSet().Contains(a.ApplyUserId));

            //待处理
            if (request.ProcessingStatus == ProcessingStatus.PendingProcessing)
            {
                query = query.Where(a => pendingProcessings.Contains(a.Status)).WhereIf(request.Status.HasValue, a => a.Status == request.Status)
                .Concat
                (
                    query.Where(a => a.Status == PurPRApplicationStatus.Approved)
                    .Join(queryPrDetail.GroupBy(a => a.PRApplicationId).Select(a => new
                    {
                        PRApplicationId = a.Key,
                        HasNonConfirmed = a.Any(a1 => a1.PayMethod == PayMethods.AR && a1.IsVendorConfimed != true),
                        HasNonPushed = a.Any(a1 => a1.PayMethod == PayMethods.AP && a1.PushFlag != PushFlagEnum.Pushed),
                    }), a => a.Id, a => a.PRApplicationId, (a, b) => new
                    {
                        a.Id,
                        a.ApplicationCode,
                        a.ApplyUserId,
                        a.TransfereeId,
                        a.ApplyTime,
                        a.ApplyUserDeptName,
                        a.CostCenter,
                        a.SubBudgetCode,
                        a.CompanyId,
                        a.TotalAmount,
                        a.Status,
                        a.IsEsignUsed,
                        a.MeetingStatus,
                        a.ApplyUserIdName,
                        b.HasNonConfirmed,
                        b.HasNonPushed
                    })
                    .WhereIf(request.Status.HasValue && request.Status != PurPRApplicationStatus.VendorConfirmed && request.Status != PurPRApplicationStatus.PushToPurchase, a => a.Status == request.Status)
                    .WhereIf(request.Status == PurPRApplicationStatus.VendorConfirmed, a => a.HasNonConfirmed)
                    .WhereIf(request.Status == PurPRApplicationStatus.PushToPurchase, a => a.HasNonPushed)
                );
            }
            //进行中
            else if (request.ProcessingStatus == ProcessingStatus.Progressing)
                query = query.Where(a => progressings.Contains(a.Status))
                .WhereIf(request.Status.HasValue, a => a.Status == request.Status);
            //已完成
            else if (request.ProcessingStatus == ProcessingStatus.Completed)
                query = query.Where(a => completeds.Contains(a.Status))
                .WhereIf(request.Status.HasValue, a => a.Status == request.Status);

            var count = query.Count();
            var finalQuery = query.Select(a => new
            {
                a.Id,
                a.ApplicationCode,
                a.ApplyUserIdName,
                a.ApplyTime,
                a.ApplyUserDeptName,
                a.CostCenter,
                a.SubBudgetCode,
                a.CompanyId,
                a.TotalAmount,
                a.Status,
                a.IsEsignUsed,
                a.MeetingStatus,
                a.HasNonConfirmed,
                a.HasNonPushed
            }).OrderByDescending(a => a.ApplyTime).Skip(request.PageIndex * request.PageSize).Take(request.PageSize);

            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var getCostcenterTask = dataverseService.GetCostcentersAsync(stateCode: null);
            var getCompanyTask = dataverseService.GetCompanyList(stateCode: null);
            var getDataTask = finalQuery.ToArrayAsync();
            Task.WaitAll(getCostcenterTask, getCompanyTask, getDataTask);

            var datas = getDataTask.Result.ToArray();

            #region 【采购申请】（OM）已执行至供应商确认状态的PR（尚未激活，当前meetingStatus-1000），撤回后，点击作废提示“提交人操作失败，请联系管理员”(结合问题976的comments)---现在通用流程作废有问题

            //筛选出撤回/退回的数据
            var rejectBackIds = datas.Where(a => a.Status == PurPRApplicationStatus.RejectedBack).Select(a => a.Id).ToArray();
            //查询出特殊撤回的数据
            var specialRecalls = await GetLatestWorkflowTasksAsync(rejectBackIds);

            #endregion

            var result = getDataTask.Result.ToArray()
                .Select(a =>
                {
                    var data = new GetAppliedByMeResponseDto
                    {
                        Id = a.Id,
                        ApplicationCode = a.ApplicationCode,
                        Applicant = a.ApplyUserIdName,
                        AppliedAt = a.ApplyTime?.ToString("yyyy-MM-dd HH:mm:ss"),
                        ApplyDept = a.ApplyUserDeptName,
                        Costcenter = getCostcenterTask.Result.FirstOrDefault(a1 => a1.Id == a.CostCenter)?.Name,
                        BudgetCode = a.SubBudgetCode,
                        Company = getCompanyTask.Result.FirstOrDefault(a1 => a1.Id == a.CompanyId)?.CompanyName,
                        TotalAmount = a.TotalAmount,
                        MeetingStatusName = GetStatusDescription(a.MeetingStatus),
                    };

                    if (a.Status == PurPRApplicationStatus.Approved)
                    {
                        if (a.HasNonConfirmed)
                            data.Status.Add(new KeyValuePair<PurPRApplicationStatus, string>(PurPRApplicationStatus.VendorConfirmed, EnumUtil.GetDescription(PurPRApplicationStatus.VendorConfirmed)));
                        if (a.HasNonPushed)
                            data.Status.Add(new KeyValuePair<PurPRApplicationStatus, string>(PurPRApplicationStatus.PushToPurchase, EnumUtil.GetDescription(PurPRApplicationStatus.PushToPurchase)));

                        //是否可以确认
                        data.CanConfirm = (a.IsEsignUsed == true ? a.MeetingStatus == OnlineMeetingStatus.OmSettledPushed : true) && data.Status.Any(a1 => a1.Key == PurPRApplicationStatus.VendorConfirmed);
                    }
                    else
                        data.Status.Add(new KeyValuePair<PurPRApplicationStatus, string>(a.Status, EnumUtil.GetDescription(a.Status)));

                    data.CanRecall = data.Status.Any(a1 => a1.Key != PurPRApplicationStatus.RejectedBack && a1.Key != PurPRApplicationStatus.ApplicantTerminate && a1.Key != PurPRApplicationStatus.Approved && a1.Key != PurPRApplicationStatus.Closed);
                    data.CanDeprecate = a.IsEsignUsed == true ? string.IsNullOrEmpty(a.MeetingStatus) || a.MeetingStatus == OnlineMeetingStatus.NexBpmPushed : a.Status == PurPRApplicationStatus.RejectedBack;
                    //审批通过或等待关闭状态时，说明审批流已结束，但是流程仍然可以使用特殊撤回进行撤回
                    data.SpecialRecall = a.Status == PurPRApplicationStatus.Approved || a.Status == PurPRApplicationStatus.WaitForClose;
                    //如果是使用特殊撤回的数据，就可以使用特殊作废
                    var specialRecall = specialRecalls.FirstOrDefault(a1 => a1.FormId == a.Id);

                    data.SpecialDeprecate = specialRecall == null ? false : specialRecall.Status == ApprovalOperation.Recall && specialRecall.InstanceId == Guid.Empty;

                    return data;
                }).ToArray();

            return new PagedResultDto<GetAppliedByMeResponseDto>(count, result);
        }

        /// <summary>
        /// 获取我审批的采购申请任务
        /// </summary>
        /// <param name="request"></param>
        /// <param name="processingStatus"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetApprovedByMeResponseDto>> GetApprovedByMe(GetApprovedByMeRequestDto request)
        {
            var queryPr = await LazyServiceProvider.LazyGetService<IPurPRApplicationReadonlyRepository>().GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var getCostcenterTask = dataverseService.GetCostcentersAsync(stateCode: null);
            var getCompanyTask = dataverseService.GetCompanyList(stateCode: null);
            Task.WaitAll(getCostcenterTask, getCompanyTask);
            var userId = CurrentUser.Id;
            //待处理
            if (request.ProcessingStatus == ProcessingStatus.PendingProcessing)
            {
                var taskRecords = await dataverseService.GetApprovelTaskAsync(userId.ToString(), [WorkflowTypeName.PurchaseRequest], request.ProcessingStatus);
                var formIds = taskRecords.WhereIf(request.Status.HasValue, a1 => a1.ApprovalStatus == ((int)request.Status.Value).ToString()).Select(a1 => a1.FormId).ToArray();
                var datas = queryPr
                    .Where(a => formIds.Contains(a.Id))
                    .WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), a => a.ApplicationCode.Contains(request.ApplicationCode))
                    .WhereIf(request.StartDate.HasValue, a => a.ApplyTime >= request.StartDate)
                    .WhereIf(request.EndDate.HasValue, a => a.ApplyTime < request.EndDate.Value.Date.AddDays(1))
                    .WhereIf(!string.IsNullOrEmpty(request.ApplyDept), a => a.ApplyUserDeptName.Contains(request.ApplyDept))
                    .WhereIf(!string.IsNullOrEmpty(request.BudgetCode), a => a.SubBudgetCode.Contains(request.BudgetCode))
                    .WhereIf(request.Company.HasValue, a => a.CompanyId == request.Company)
                    .Select(a => new
                    {
                        a.Id,
                        a.ApplicationCode,
                        Applicant = a.ApplyUserIdName,
                        a.ApplyTime,
                        a.ApplyUserDeptName,
                        a.CostCenter,
                        a.SubBudgetCode,
                        a.CompanyId,
                        a.TotalAmount,
                        a.Status,
                        a.MeetingStatus
                    })
                    .ToArray();

                var count = datas.Count();

                var result = new GetApprovedByMeResponseDto[] { };

                var dataQuery = datas.Join(taskRecords, a => a.Id, a => a.FormId, (a, b) => new { Pr = a, Task = b })
                    .Select(a => new
                    {
                        a.Pr.Id,
                        a.Task.TaskId,
                        a.Pr.ApplicationCode,
                        a.Pr.Status,
                        a.Pr.Applicant,
                        a.Pr.ApplyTime,
                        a.Pr.ApplyUserDeptName,
                        a.Pr.CostCenter,
                        a.Pr.CompanyId,
                        a.Pr.TotalAmount,
                        a.Pr.SubBudgetCode,
                        a.Task.StepNumber,
                        a.Pr.MeetingStatus,
                        a.Task.CreatedTime,
                    }).AsQueryable();
                // 动态排序
                var dataOrderByQuery = (string.IsNullOrEmpty(request.SortField) ? dataQuery.OrderBy("CreatedTime" + (request.IsAsc ? "" : " desc")) : dataQuery.OrderBy(request.SortField + (request.IsAsc ? "" : " desc")))
                    .Skip(request.PageIndex * request.PageSize).Take(request.PageSize);
                var data = dataOrderByQuery.ToList();
                result = data.Select(a => new GetApprovedByMeResponseDto
                {
                    Id = a.Id,
                    TaskId = a.TaskId,
                    ApplicationCode = a.ApplicationCode,
                    Applicant = a.Applicant,
                    AppliedAt = a.ApplyTime?.ToString("yyyy-MM-dd HH:mm:ss"),
                    ApplyDept = a.ApplyUserDeptName,
                    Costcenter = getCostcenterTask.Result.FirstOrDefault(a1 => a1.Id == a.CostCenter)?.Name,
                    BudgetCode = a.SubBudgetCode,
                    Company = getCompanyTask.Result.FirstOrDefault(a1 => a1.Id == a.CompanyId)?.CompanyName,
                    TotalAmount = a.TotalAmount,
                    CanAmend = a.StepNumber == WorkflowConsts.PRWorkflowConsts.PrFinApprovalStepCode,
                    MeetingStatusName = GetStatusDescription(a.MeetingStatus),
                    Status = new List<KeyValuePair<PurPRApplicationStatus, string>> { new KeyValuePair<PurPRApplicationStatus, string>(a.Status, EnumUtil.GetDescription(a.Status)) },
                    CreatedTime = a.CreatedTime,
                }).ToArray();

                return new PagedResultDto<GetApprovedByMeResponseDto>(count, result);
            }
            else//已完成
            {
                var queryWfTask = await LazyServiceProvider.LazyGetService<IWorkflowTaskReadonlyRepository>().GetQueryableAsync();
                var query = queryPr
                    .WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), a => a.ApplicationCode.Contains(request.ApplicationCode))
                    .WhereIf(request.StartDate.HasValue, a => a.ApplyTime >= request.StartDate)
                    .WhereIf(request.EndDate.HasValue, a => a.ApplyTime < request.EndDate.Value.Date.AddDays(1))
                    .WhereIf(!string.IsNullOrEmpty(request.ApplyDept), a => a.ApplyUserDeptName.Contains(request.ApplyDept))
                    .WhereIf(!string.IsNullOrEmpty(request.BudgetCode), a => a.SubBudgetCode.Contains(request.BudgetCode))
                    .WhereIf(request.Company.HasValue, a => a.CompanyId == request.Company)
                    .Join(queryWfTask.Where(a => a.ApprovalId == userId).Select(x => new
                    {
                        x.Id,
                        x.FormId,
                        x.StepNo,
                        x.ApprovalTime,
                        x.CreationTime,
                    }),
                          a => a.Id,
                          a => a.FormId,
                          (a, b) => new { Pr = a, Task = b })
                    .Select(a => new
                    {
                        a.Pr.Id,
                        TaskId = a.Task.Id,
                        a.Pr.ApplicationCode,
                        Applicant = a.Pr.ApplyUserIdName,
                        AppliedAt = a.Pr.ApplyTime.HasValue ? a.Pr.ApplyTime.Value.ToString("yyyy-MM-dd HH:mm:ss") : null,
                        ApplyDept = a.Pr.ApplyUserDeptName,
                        Costcenter = a.Pr.CostCenter.HasValue ? a.Pr.CostCenter.ToString() : null,
                        BudgetCode = a.Pr.SubBudgetCode,
                        Company = a.Pr.CompanyId.HasValue ? a.Pr.CompanyId.ToString() : null,
                        a.Pr.TotalAmount,
                        CanAmend = a.Task.StepNo == WorkflowConsts.PRWorkflowConsts.PrFinApprovalStepCode,
                        a.Pr.Status,
                        MeetingStatusName = a.Pr.MeetingStatus,
                        a.Task.ApprovalTime,
                        a.Task.CreationTime
                    });

                var count = query.Count();
                if (request.IsAsc)
                    query = query.OrderBy(a => a.ApprovalTime).Skip(request.PageIndex * request.PageSize).Take(request.PageSize);
                else
                    query = query.OrderByDescending(a => a.ApprovalTime).Skip(request.PageIndex * request.PageSize).Take(request.PageSize);

                var result = query.ToArray().Select(a =>
                {
                    var data = new GetApprovedByMeResponseDto
                    {
                        Id = a.Id,
                        TaskId = a.TaskId,
                        ApplicationCode = a.ApplicationCode,
                        Applicant = a.Applicant,
                        AppliedAt = a.AppliedAt,
                        ApplyDept = a.ApplyDept,
                        BudgetCode = a.BudgetCode,
                        TotalAmount = a.TotalAmount,
                        CanAmend = a.CanAmend,
                        ApprovalTime = a.ApprovalTime,
                        CreatedTime = a.CreationTime,
                    };

                    if (!string.IsNullOrEmpty(a.Costcenter))
                        data.Costcenter = getCostcenterTask.Result.FirstOrDefault(a1 => a1.Id == Guid.Parse(a.Costcenter))?.Name;
                    if (!string.IsNullOrEmpty(a.Company))
                        data.Company = getCompanyTask.Result.FirstOrDefault(a1 => a1.Id == Guid.Parse(a.Company))?.CompanyName;

                    data.MeetingStatusName = GetStatusDescription(a.MeetingStatusName);
                    data.Status.Add(new KeyValuePair<PurPRApplicationStatus, string>(a.Status, EnumUtil.GetDescription(a.Status)));

                    return data;
                }).ToArray();

                return new PagedResultDto<GetApprovedByMeResponseDto>(count, result);
            }
        }

        /// <summary>
        /// 供应商确认
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> VendorConfirmed(SaveVendorConfirmedRequestDto request)
        {
            var prApplicationRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>();
            var prApplication = await prApplicationRepository.FindAsync(request.PRApplicationId);
            if (prApplication == null)
                return MessageResult.FailureResult($"未找到Id为{request.PRApplicationId}的采购申请数据");

            var prApplicationDetailRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>();
            var omHedges = new List<PurPRApplicationDetail>();
            if (prApplication.IsEsignUsed == true)
            {
                if (prApplication.MeetingStatus != OnlineMeetingStatus.OmSettledPushed)
                {
                    return MessageResult.FailureResult("电子签章会议需要会议状态为结算时，才能进行确认");
                }
                else
                {
                    omHedges = await OMSettleMeetingAsync(prApplication);//20250106 ytw 会议结算（结算接口对PR相关处理移到此处）
                    request.VendorConfirmedDetails = request.VendorConfirmedDetails.Union(omHedges.Select(a => new SaveVendorConfirmedDetailRequestDto { PRApplicationDetailId = a.Id, VendorId = a.VendorId.Value }).ToList());
                }
            }

            var messageResult = MessageResult.SuccessResult();
            var detailIds = request.VendorConfirmedDetails.Select(a => a.PRApplicationDetailId).ToArray();

            //获取所有明细行
            var prApplicationDetails = await prApplicationDetailRepository.GetListAsync(a => a.PRApplicationId == request.PRApplicationId);

            //ar行
            var arDetails = prApplicationDetails.Where(a => a.PayMethod == PayMethods.AR && detailIds.Contains(a.Id)).ToArray();

            //获取对冲的行
            var hedgeDetails = arDetails.Where(a => a.HedgePrDetailId.HasValue).ToArray();

            var listNonHedgeRows = new List<PurPRApplicationDetail>();
            foreach (var item in arDetails)
            {
                //如果行已经被确认了则直接跳过
                if (item.IsVendorConfimed == true)
                    continue;

                //非对冲行，后面会生成GR
                if (!hedgeDetails.Any(a => a.HedgePrDetailId == item.Id || a.Id == item.Id))
                    listNonHedgeRows.Add(item);

                if (!string.IsNullOrEmpty(item.BackUpVendors))
                {
                    var confirmDetail = request.VendorConfirmedDetails.First(a => a.PRApplicationDetailId == item.Id);
                    var backupVendors = JsonConvert.DeserializeObject<IEnumerable<CreateUpdatePRApplicationBackupVendor>>(item.BackUpVendors);
                    var backupVendor = backupVendors.FirstOrDefault(a => a.VendorId == confirmDetail.VendorId);
                    if (backupVendor != null && !omHedges.Any(a => a.Id == item.Id))//OM 会议不能切换讲者， 只能通过接口处理
                    {
                        item.VendorId = backupVendor.VendorId;
                        item.VendorName = backupVendor.VendorIdName;
                        item.TermCodeDays = backupVendor.PaymentTerm;
                        item.HcpLevelCode = backupVendor.HcpLevelCode;
                        item.HcpLevelName = backupVendor.HcpLevelName;
                        item.Hospital = backupVendor.Hospital;
                        item.UnitPrice = backupVendor.Price;

                        //pr总金额先减去老的金额
                        prApplication.TotalAmount -= item.TotalAmount ?? 0;
                        prApplication.TotalAmountRMB -= item.TotalAmountRMB ?? 0;

                        item.TotalAmount = item.Quantity * backupVendor.Price;
                        item.TotalAmountRMB = item.Quantity * backupVendor.Price * (decimal)prApplication.Rate;

                        //再加上本次的金额
                        prApplication.TotalAmount += item.TotalAmount ?? 0;
                        prApplication.TotalAmountRMB += item.TotalAmountRMB ?? 0;
                    }
                }
                item.IsVendorConfimed = true;
            }

            #region 退回预算
            #region OM 反冲相关
            var omHedgeIds = omHedges.Select(x => x.Id).ToList();
            var oriIds = omHedges.Select(x => x.HedgePrDetailId.Value).ToList();
            omHedgeIds.AddRange(oriIds);
            #endregion
            //获取有金额变更的行
            var changedDetails = arDetails.WhereIf(omHedgeIds.Any(), a => omHedgeIds.Contains(a.Id)).Where(a => a.TotalAmount != a.OriginalTotalAmount || a.TotalAmountRMB != a.OriginalTotalAmountRMB).ToArray();
            if (changedDetails.Any())
            {
                var useBudgetRequest = new ReturnBudgetRequestDto
                {
                    PrId = prApplication.Id,
                    SubbudgetId = prApplication.SubBudgetId.Value,
                    Items = changedDetails.Select(a => new ReturnInfo
                    {
                        PdRowNo = a.RowNo,
                        ReturnAmount = (a.OriginalTotalAmountRMB ?? 0) - (a.TotalAmountRMB ?? 0),
                        ReturnSourceId = a.Id,
                        ReturnSourceCode = prApplication.ApplicationCode
                    })
                };
                var result = await LazyServiceProvider.LazyGetService<ISubbudgetService>().ReturnSubbudgetAsync(useBudgetRequest);
                if (!result.Success)
                    return result;
            }

            #endregion

            await prApplicationDetailRepository.UpdateManyAsync(arDetails, true);

            //如果所有的AP行都已经推送完毕，则修改状态为待关闭
            var judgement = await IsAllPrDetailPushedAndConfirmed(prApplication.Id);
            if (judgement)
                prApplication.Status = PurPRApplicationStatus.WaitForClose;

            await prApplicationRepository.UpdateAsync(prApplication);

            #region 退回备选的psa用量

            //有备选讲者的情况，需退回备选和原始讲者的psa用量（若变更）
            var needReturnPsaSpeakers = listNonHedgeRows.Where(a => !string.IsNullOrEmpty(a.BackUpVendors)).SelectMany(a =>
            {
                var vendors = JsonConvert.DeserializeObject<List<CreateUpdatePRApplicationBackupVendor>>(a.BackUpVendors);
                //有变更过主讲的情况，则把原始讲者加入到要退回psa用量的列表
                if (a.OriginalVendorId.HasValue && a.OriginalVendorId != a.VendorId)
                {
                    vendors.Add(new CreateUpdatePRApplicationBackupVendor { VendorId = a.OriginalVendorId.Value, Price = (a.OriginalTotalAmount ?? 0) / ((a.Quantity == null || a.Quantity == 0) ? 1 : a.Quantity.Value), ExceptionNumber = a.ExceptionNumber });//将主讲者变为备选
                    vendors = vendors.Where(x => x.VendorId != a.VendorId).ToList();//将被选为主讲者的备选讲者排除
                }
                return vendors;
            }, (a, b) => new { PrDetail = a, BackupVendor = b });

            //会议结算0元讲者产生反冲，被反冲行讲者和备选讲者都需要退psa用量
            var omNeedReturnPsaSpeakers = arDetails.Where(a => omHedges.Select(x => x.HedgePrDetailId.Value).Contains(a.Id)).SelectMany(a =>
            {
                var vendors = new List<CreateUpdatePRApplicationBackupVendor>();
                if (!string.IsNullOrWhiteSpace(a.BackUpVendors))
                    vendors.AddRange(JsonConvert.DeserializeObject<List<CreateUpdatePRApplicationBackupVendor>>(a.BackUpVendors));//全部备选讲者需要退
                vendors.Add(new CreateUpdatePRApplicationBackupVendor { VendorId = a.OriginalVendorId.Value, Price = (a.OriginalTotalAmount ?? 0) / ((a.Quantity == null || a.Quantity == 0) ? 1 : a.Quantity.Value), ExceptionNumber = a.ExceptionNumber });//主讲者需要退
                return vendors;
            }, (a, b) => new { PrDetail = a, BackupVendor = b });
            needReturnPsaSpeakers = needReturnPsaSpeakers.Union(omNeedReturnPsaSpeakers);//合起来退

            //获取NexBpm中的vendorId
            var bpcsVendorIds = needReturnPsaSpeakers.Select(a => a.BackupVendor.VendorId).Distinct();
            var types = new string[] { "NHIV", "NT" };
            var queryBpcsAvm = await LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync();
            var queryVendor = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();
            var queryFinancial = await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync();
            //获取bpcs和nexBpm映射关系
            var vendorIdMappings = queryBpcsAvm.Where(a => types.Contains(a.Vtype) && bpcsVendorIds.Contains(a.Id))
                .Join(queryFinancial, a => a.FinaId, a => a.Id, (a, b) => new { BpcsVendorId = a.Id, NexBpmVendorId = b.VendorId }).ToArray();

            //要退回psa用量的数据
            var psaDatas = needReturnPsaSpeakers.Join(vendorIdMappings, a => a.BackupVendor.VendorId, a => a.BpcsVendorId, (a, b) => new
            {
                b.NexBpmVendorId,
                a.PrDetail.EstimateDate,
                a.BackupVendor.ExceptionNumber,
                Times = 1,
                Amount = a.BackupVendor.Price * a.PrDetail.Quantity * (decimal)prApplication.Rate
            })
            .GroupBy(a => new { a.NexBpmVendorId, a.EstimateDate, a.ExceptionNumber })
            .Select(a => new PrUseSpeakerLimitDetailRequest
            {
                VendorId = a.Key.NexBpmVendorId,
                EffectiveDate = a.Key.EstimateDate ?? DateTime.Today,
                ExceptionNumber = a.Key.ExceptionNumber,
                Times = a.Sum(a1 => a1.Times),
                Amount = a.Sum(a1 => a1.Amount ?? 0),
                Type1 = OperDetailType.Added,
                Type2 = ModifyTypes.Return
            }).ToArray();

            if (psaDatas.Any())
            {
                var result = await LazyServiceProvider.LazyGetService<ISpeakerLimitService>().SavePrSpeakerLimitAsync(new PrUseSpeakerLimitRequest { PrApplicationId = prApplication.Id, BuId = prApplication.ApplyUserBu, Details = psaDatas });
                if (!result.Success)
                    return result;
            }

            #endregion

            await CurrentUnitOfWork.SaveChangesAsync();

            if (listNonHedgeRows.Any())
                messageResult = await LazyServiceProvider.LazyGetService<IPurGRApplicationService>().CreateGRApplication(listNonHedgeRows.Select(a => a.Id).ToList());

            //操作记录
            if (listNonHedgeRows.Any())
                await LazyServiceProvider.LazyGetService<IApproveService>().AddApprovalRecordAsync(new AddApprovalRecordDto
                {
                    FormId = prApplication.Id,
                    ApprovalId = CurrentUser.Id ?? prApplication.ApplyUserId,
                    OriginalApprovalId = prApplication.ApplyUserId,
                    Status = ApprovalOperation.VendorConfirmed,
                    ApprovalTime = DateTime.Now,
                    WorkStep = "供应商确认",
                    Name = "供应商确认"
                });

            return messageResult;
        }

        /// <summary>
        /// OM 结算NexBpm会议
        /// </summary>
        /// <returns></returns>
        private async Task<List<PurPRApplicationDetail>> OMSettleMeetingAsync(PurPRApplication pRApplication)
        {
            var meetingSettlementQuery = await LazyServiceProvider.LazyGetService<IInteOnlineMeetingRepository>().GetQueryableAsync();
            var prDetailRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>();
            var meetingSettlements = meetingSettlementQuery.Where(a => a.PRApplicationId == pRApplication.Id).ToList();
            var prDetails = await prDetailRepository.GetListAsync(x => x.PRApplicationId == pRApplication.Id);

            meetingSettlements = meetingSettlements.GroupBy(a => a.PRDetailId).Select(a => a.OrderByDescending(x => x.CreationTime).FirstOrDefault()).ToList();//每一个PR 明细只取最新一条数据

            var meetingSpeakerItems = meetingSettlements.Select(a => new NexBpmMeetingSpeakerItemDto { No = a.No.ToString(), VendorCode = a.VendorCode }).ToList();
            var vndInfos = await IsAllVendorCodeRight(meetingSpeakerItems, prDetails, pRApplication.CompanyCode);
            var updatePRDetails = new List<PurPRApplicationDetail>();
            var insertPRDetails = new List<PurPRApplicationDetail>();
            int rowNo = prDetails.Max(a => a.RowNo);//当前最大明细行号
            DateTime? estimateDate = null;
            foreach (var meeting in meetingSettlements)
            {
                var prDetail = prDetails.SingleOrDefault(x => x.Id == meeting.PRDetailId && x.RowNo == meeting.No);
                var tmpVndInfo = vndInfos.GetValue($"{meeting.VendorCode}-{prDetail.Id.ToString()}");
                if (tmpVndInfo == null)
                    continue;
                if (tmpVndInfo[8] != prDetail.Id.ToString())
                    continue;
                var speakerService = LazyServiceProvider.LazyGetService<ISpeakerService>();
                var getSpeakerResponse = await speakerService.GetSpeakerDetailByCodeAsync(tmpVndInfo?[4]);
                if (getSpeakerResponse == null || !getSpeakerResponse.Success)
                    continue;
                var speaker = getSpeakerResponse.Data as SpeakerDetailResponseDto;
                prDetail.Hospital = tmpVndInfo?[5] == "1" ? tmpVndInfo?[6] : speaker.HospitalName;
                prDetail.HosDepartment = speaker.HosDepartment;
                prDetail.HcpLevelName = tmpVndInfo?[5] == "1" ? tmpVndInfo?[7] : speaker.SPLevelName;
                //zhx20240703添加：
                if (Guid.TryParse(tmpVndInfo?[0], out Guid tmpBpcsAvmId))
                {
                    prDetail.VendorId = tmpBpcsAvmId;
                }
                prDetail.VendorCode = tmpVndInfo?[1];
                prDetail.VendorName = tmpVndInfo?[2];
                prDetail.CardNo = tmpVndInfo?[3];
                prDetail.CertificateCode = speaker.CertificateCode;
                prDetail.StandardDepartmentId = speaker.StandardHosDepId;
                prDetail.StandardDepartment = speaker.StandardHosDepName;
                // NexBpm结算逻辑
                //prDetail.VendorCode = request.VendorCode;//已从tmpVndInfo里取
                prDetail.ExecutorName = meeting.Executive;
                prDetail.ExecutorEmail = meeting?.ExecutiveMail?.Trim();
                estimateDate = meeting.StartDate;

                //3832 供应商确认的时候不应该更新PR行的金额以及PR单上的申请总金额 20250218 ytw  代码注释掉
                //// 实际金额少于PR申请金额，但大于0
                //if (meeting.PayAmount > 0 && meeting.PayAmount < prDetail.OriginalTotalAmountRMB)
                //{
                //    //pr总金额先减去老的金额
                //    pRApplication.TotalAmount -= prDetail.TotalAmount ?? 0;
                //    pRApplication.TotalAmountRMB -= prDetail.TotalAmountRMB ?? 0;

                //    //实际大于0且小于申请金额时，不用去做返还预算了，只要更新图中的金额和单价字段就行了。
                //    //zhx20240709:按实际金额整理以下几个金额和单价字段
                //    prDetail.TotalAmountRMB = meeting.PayAmount;//覆盖PrDetail.TotalAmountRMB
                //    prDetail.UnitPrice = prDetail.TotalAmountRMB / prDetail.Quantity;//再算单价
                //    prDetail.TotalAmount = prDetail.TotalAmountRMB / (decimal)pRApplication.Rate;//用汇率重新算TotalAmount

                //    //再加上本次的金额
                //    pRApplication.TotalAmount += prDetail.TotalAmount ?? 0;
                //    pRApplication.TotalAmountRMB += prDetail.TotalAmountRMB ?? 0;

                //}
                // 实际金额为0，PR申请金额不为0
                if (meeting.PayAmount == 0 && prDetail.OriginalTotalAmountRMB != 0)
                {
                    // 反冲pr detail行，所有行重新提交预算使用
                    PurPRApplicationDetail hedgePrDetail = JsonConvert.DeserializeObject<PurPRApplicationDetail>(JsonConvert.SerializeObject(prDetail));
                    hedgePrDetail.SetId(Guid.NewGuid());
                    hedgePrDetail.HedgePrDetailId = prDetail.Id;
                    hedgePrDetail.RowNo = rowNo + 1;
                    hedgePrDetail.TotalAmount *= -1;
                    hedgePrDetail.TotalAmountRMB *= -1;
                    hedgePrDetail.UnitPrice *= -1;
                    hedgePrDetail.IsHedge = true;//反冲行
                    prDetail.IsHedge = true;//被反冲
                    hedgePrDetail.OriginalTotalAmount *= -1;
                    hedgePrDetail.OriginalTotalAmountRMB *= -1;
                    //检查子预算是否足够
                    var useInfos = prDetails.Select(a => new UseInfo
                    {
                        PdRowNo = a.RowNo,
                        UseAmount = a.TotalAmountRMB ?? 0
                    }).ToList();
                    //添加反冲行的金额，一起Check和Use预算
                    useInfos.Add(new UseInfo
                    {
                        PdRowNo = hedgePrDetail.RowNo,
                        UseAmount = hedgePrDetail.TotalAmountRMB ?? 0
                    });

                    var useBudgetRequest = new UseBudgetRequestDto
                    {
                        PrId = pRApplication.Id,
                        SubbudgetId = pRApplication.SubBudgetId.Value,
                        Items = useInfos
                    };

                    var subBudgetService = LazyServiceProvider.LazyGetService<ISubbudgetService>();

                    await subBudgetService.UseSubbudgetAsync(useBudgetRequest, true);//退回子预算

                    //2366【采购申请】OM：结算金额<申请金额时，未更新列表的申请总金额(SettleMeeting接口)
                    //pr总净额加上对冲的金额（负数）
                    pRApplication.TotalAmount += hedgePrDetail.TotalAmount ?? 0;
                    pRApplication.TotalAmountRMB += hedgePrDetail.TotalAmountRMB ?? 0;

                    // 插入反冲pr detail行
                    insertPRDetails.Add(hedgePrDetail);
                    rowNo++;
                }
                updatePRDetails.Add(prDetail);
            }
            if (estimateDate.HasValue)
                pRApplication.MeetingDate = estimateDate;
            if (insertPRDetails.Any())
            {

                insertPRDetails.ForEach(a =>
                {
                    if (estimateDate.HasValue)
                        a.EstimateDate = estimateDate;
                });
                await prDetailRepository.InsertManyAsync(insertPRDetails, true);
            }

            var otherDetails = prDetails.Where(a => !updatePRDetails.Select(x => x.Id).Contains(a.Id)).ToList();//其他PR明细
            if (otherDetails.Any())
            {
                updatePRDetails.AddRange(otherDetails);
                updatePRDetails.ForEach(a =>
                {
                    if (estimateDate.HasValue)
                        a.EstimateDate = estimateDate;
                });
            }
            await prDetailRepository.UpdateManyAsync(updatePRDetails, true);

            return insertPRDetails;
        }

        /// <summary>
        /// 讲者是否是主讲者或备选讲者、讲者明细数据
        /// </summary>
        /// <param name="speakerDetails">The speaker details.</param>
        /// <param name="prDetails">The pr details.</param>
        /// <returns>
        /// bool: speakerDetails每一项里的VendorCode是否是对应PrDetail的OriginalVendorId 或 备选讲者，true:全是，false:不全是；
        /// Dictionary(string, string[]): Key: speakerDetails每一项的VendorCode（BpcsAvm.Vendor）,
        /// string[]按索引依次为如下4个值：
        /// 0：BpcsAvm.Id, 1：BpcsAvm.Vendor, 2：BpcsPMFVM.Vextnm, 3：BpcsAvm.Vmxcrt，4：Vendors.VendorCode，
        /// 5：0-变更为主讲者、1-变更后为备选讲者
        /// 6：备选讲者Json里的 Hospital
        /// 7：备选讲都Json里的 HcpLevelName
        /// </returns>
        private async Task<Dictionary<string, string[]>> IsAllVendorCodeRight(List<NexBpmMeetingSpeakerItemDto> speakerDetails, List<PurPRApplicationDetail> prDetails, string prCompanyCode)
        {
            //1，根据所有 speakerDetail.VendorCode + prCompanyCode，查出所有 BpcsAvm+BpcsPMFVM
            var queryBpcsAvm = await LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync();
            var queryBpcsPmfvm = await LazyServiceProvider.LazyGetService<IBpcsPmfvmRepository>().GetQueryableAsync();
            var bpcsAvmPmfvm = queryBpcsAvm.Where(a => a.Vcmpny == decimal.Parse(prCompanyCode)
                    && speakerDetails.Select(s => decimal.Parse(s.VendorCode)).Contains(a.Vendor))
                .Join(queryBpcsPmfvm, l => new { Vnd = l.Vendor, Cmp = l.Vcmpny }, r => new { Vnd = r.Vnderx, Cmp = r.Vmcmpy },
                    (pbcsAvm, pbcsPmfvm) => new { bpcsAvmId = pbcsAvm.Id, pbcsAvm, pbcsPmfvm })
                .ToList();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var specialvendors = await dataverseService.GetSpecialvendorAsync();//特殊供应商
            var resultData = new Dictionary<string, string[]>();
            var finaIds = new List<Guid>();
            //2，循环判断每个 speakerDetail.VendorCode 的 BpcsAvm.Id 是否在对应的 PrDetail 的Original或Backup里
            //如有某一条不在，则直接返回false
            foreach (var speakerDetail in speakerDetails)
            {
                if (int.TryParse(speakerDetail.No, out int rowNo))
                {
                    var prDetail = prDetails.SingleOrDefault(x => x.RowNo == rowNo);
                    if (prDetail == null)
                    {
                        continue;
                    }

                    var curBpcsAvm = bpcsAvmPmfvm.FirstOrDefault(a =>
                        a.pbcsAvm.Vendor == decimal.Parse(speakerDetail.VendorCode)
                        && a.pbcsAvm.Vcmpny == decimal.Parse(prCompanyCode));

                    var specialvendor = specialvendors.FirstOrDefault(a => a.VendorCode == speakerDetail.VendorCode && a.CompanyCode == prCompanyCode);//特殊供应商
                    var curBpcsAvmId = curBpcsAvm?.bpcsAvmId;

                    if (!curBpcsAvmId.HasValue)
                    {
                        continue;
                    }
                    var backupVendors = string.IsNullOrEmpty(prDetail.BackUpVendors) ? null : JsonConvert.DeserializeObject<List<CreateUpdatePRApplicationBackupVendor>>(prDetail.BackUpVendors);
                    if (prDetail.OriginalVendorId != curBpcsAvmId
                        && (backupVendors == null || !backupVendors.Select(a => a.VendorId).Contains(curBpcsAvmId.Value)))
                    {
                        continue;
                    }

                    //以下2个变量 为了区分变更后的是主讲者还是备选讲者
                    var isOriginal = prDetail.OriginalVendorId == curBpcsAvmId;
                    var matchedBackupVnd = isOriginal ? null : backupVendors?.FirstOrDefault(a => a.VendorId == curBpcsAvmId.Value);

                    resultData.Add($"{speakerDetail.VendorCode}-{prDetail.Id.ToString()}",
                        new string[] {
                                curBpcsAvm.bpcsAvmId.ToString(),
                                curBpcsAvm.pbcsAvm.Vendor.ToString(),
                                specialvendor != null ? specialvendor.Name : curBpcsAvm.pbcsPmfvm.Vextnm,
                                curBpcsAvm.pbcsAvm.Vmxcrt,
                                curBpcsAvm.pbcsAvm.FinaId?.ToString(),//先用FinaId占位，后面替换成对应的Vendor.VendorCode
                                isOriginal ? "0":"1",
                                matchedBackupVnd?.Hospital,
                                matchedBackupVnd?.HcpLevelName,
                                prDetail.Id.ToString(),
                        });

                    //3，在循环判断处理第2点时，同时记录所有 BpcsAvm.FinalId，
                    finaIds.AddIf(curBpcsAvm.pbcsAvm.FinaId.HasValue, curBpcsAvm.pbcsAvm.FinaId.Value);
                }
                else
                {
                    continue;
                }
            }

            //4，在第2点完成后，用第3点记录的所有 BpcsAvm.FinalId，查出对应的 Vendor.VendorCode，并添加到Result里
            if (finaIds.Any())
            {
                var queryVendor = LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync().GetAwaiterResult();
                var queryVendorFinal = LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync().GetAwaiterResult();
                var dicVndCodes = queryVendor.Join(queryVendorFinal.Where(a => finaIds.Contains(a.Id)), l => l.Id, r => r.VendorId,
                    (l, r) => new { l.VendorCode, r.Id })
                    .ToDictionary(a => a.Id, a => a.VendorCode);
                foreach (var item in resultData)
                {
                    if (item.Value[4] == null)
                    {
                        continue;
                    }
                    item.Value[4] = dicVndCodes.GetValueOrDefault(Guid.Parse(item.Value[4]));
                }
            }
            return resultData;
        }

        /// <summary>
        /// 修订
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> Amend(AmendRequestDto request)
        {
            var prApplicationRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>();
            var prApplication = await prApplicationRepository.FirstOrDefaultAsync(a => a.Id == request.BusinessFormId);
            if (prApplication == null)
                return MessageResult.FailureResult($"未找到Id为{request.BusinessFormId}的PR申请信息");

            if (prApplication.Status != PurPRApplicationStatus.Approving)
                return MessageResult.FailureResult($"单据状态不正确，无法回退");

            var result = await LazyServiceProvider.LazyGetService<IApproveService>().ApprovalOperationAsync(new List<UpdateApprovalDto> { new UpdateApprovalDto { BusinessFormId = request.BusinessFormId.ToString(), Submitter = CurrentUser.Id.Value, OperationStatus = ApprovalOperation.Amend, Remark = request.Remark } });
            if (!result.Success)
                return result;

            prApplication.IsShowExpenseStep = false;
            await prApplicationRepository.UpdateAsync(prApplication);

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 特殊撤回，用于审批完成/等待关闭状态的PR申请单撤回
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> SpecialRecallAsync(SpecialWithdrawRequestDto request)
        {
            var prApplicationRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>();
            var prApplication = await prApplicationRepository.FirstOrDefaultAsync(a => a.Id == request.BusinessFormId);
            if (prApplication == null)
                return MessageResult.FailureResult($"未找到Id为{request.BusinessFormId}的PR申请信息");

            if (prApplication.Status == PurPRApplicationStatus.Closed)
                return MessageResult.FailureResult("已完结的申请无法再撤回");

            if (prApplication.Status != PurPRApplicationStatus.Approved && prApplication.Status != PurPRApplicationStatus.WaitForClose)
                return MessageResult.FailureResult($"只有特定状态下，才能使用该接口进行撤回");

            prApplication.Status = PurPRApplicationStatus.RejectedBack;
            await prApplicationRepository.UpdateAsync(prApplication);

            //操作记录
            await LazyServiceProvider.LazyGetService<IApproveService>().AddApprovalRecordAsync(new AddApprovalRecordDto
            {
                FormId = prApplication.Id,
                ApprovalId = CurrentUser.Id ?? prApplication.ApplyUserId,
                OriginalApprovalId = prApplication.ApplyUserId,
                Status = ApprovalOperation.Recall,
                Remark = request.Remark,
                ApprovalTime = DateTime.Now,
                WorkStep = "提交人操作",
                Name = "撤回"
            });

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 特殊作废接口
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> SpecialDeprecateAsync(SpecialWithdrawRequestDto request)
        {
            var prApplicationRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>();
            var prApplication = await prApplicationRepository.FirstOrDefaultAsync(a => a.Id == request.BusinessFormId);
            if (prApplication == null)
                return MessageResult.FailureResult($"未找到Id为{request.BusinessFormId}的PR申请信息");

            if (prApplication.Status == PurPRApplicationStatus.Closed)
                return MessageResult.FailureResult("已完结的申请无法再作废");

            if (prApplication.Status != PurPRApplicationStatus.RejectedBack)
                return MessageResult.FailureResult($"只有特定状态下，才能使用该接口进行作废");

            var result = await LazyServiceProvider.LazyGetService<IApproveService>().UpdatePRApplicationStatus([prApplication.Id], ApprovalOperation.Delete);
            if (!result.Success)
                return result;

            //prApplication.Status = PurPRApplicationStatus.ApplicantTerminate;
            //await prApplicationRepository.UpdateAsync(prApplication);

            //操作记录
            await LazyServiceProvider.LazyGetService<IApproveService>().AddApprovalRecordAsync(new AddApprovalRecordDto
            {
                FormId = prApplication.Id,
                ApprovalId = CurrentUser.Id ?? prApplication.ApplyUserId,
                OriginalApprovalId = prApplication.ApplyUserId,
                Status = ApprovalOperation.Delete,
                Remark = request.Remark,
                ApprovalTime = DateTime.Now,
                WorkStep = "提交人操作",
                Name = "作废"
            });

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 为全流程报表增加数据埋点
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> RecordPrCurrentProcessor(CurrentApprovalTaskDto request)
        {
            var prApplicationRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>();
            try
            {
                var pr = await prApplicationRepository.FirstOrDefaultAsync(a => a.Id == request.FormId);

                pr.CurrentProcessorIds = string.Join(",", request.Approver.Select(a => a.Id));
                pr.CurrentProcessorName = string.Join(",", request.Approver.Select(a => a.Name));
                await prApplicationRepository.UpdateAsync(pr);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"RecordPrCurrentProcessor：记录PR下一步处理人失败。{JsonConvert.SerializeObject(request)}");
                return MessageResult.FailureResult();
            }

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 采购流程审批时，记录expense审批人或者财务审批人
        /// </summary>
        /// <param name="updateApprovalDto"></param>
        /// <returns></returns>
        public async Task<MessageResult> RecordExpenseOrFinanceProcessor(UpdateApprovalDto updateApprovalDto)
        {
            //如果不是expense审批步骤或者财务审批步骤，直接跳过
            if (!WorkflowConsts.PRWorkflowConsts.ExpenseRange.Contains(updateApprovalDto.StepNo) && !WorkflowConsts.PRWorkflowConsts.FinRange.Contains(updateApprovalDto.StepNo))
                return MessageResult.SuccessResult();

            var prApplicationRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>();
            try
            {
                var pr = await prApplicationRepository.FirstOrDefaultAsync(a => a.Id == Guid.Parse(updateApprovalDto.BusinessFormId));

                //expense审批
                if (WorkflowConsts.PRWorkflowConsts.ExpenseRange.Contains(updateApprovalDto.StepNo))
                {
                    pr.FinalExpenseApproverId = updateApprovalDto.Submitter;
                    pr.FinalExpenseApproverName = CurrentUser.Name;
                    pr.FinalExpenseApprovedDatetime = DateTime.Now;
                }
                else//财务审批
                {
                    pr.FinalFinApproverId = updateApprovalDto.Submitter;
                    pr.FinalFinApproverName = CurrentUser.Name;
                    pr.FinalFinApprovedDatetime = DateTime.Now;
                }

                await prApplicationRepository.UpdateAsync(pr);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"RecordExpenseOrFinanceProcessor：记录expense审批人或者财务审批人。{JsonConvert.SerializeObject(updateApprovalDto)}");
                return MessageResult.FailureResult();
            }

            return MessageResult.SuccessResult();
        }
    }
}
