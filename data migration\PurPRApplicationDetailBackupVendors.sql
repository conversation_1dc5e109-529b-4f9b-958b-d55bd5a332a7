select 
TRY_CONVERT(UNIQUEIDENTIFIER, a.[Id]) id
,TRY_CONVERT(UNIQUEIDENTIFIER, IIF(a.[PRApplicationId] is null,'00000000-0000-0000-0000-000000000000',a.[PRApplicationId]))PRApplicationId
,TRY_CONVERT(UNIQUEIDENTIFIER, IIF(a.[PRApplicationDetailId] is null,'00000000-0000-0000-0000-000000000000',a.[PRApplicationDetailId]))PRApplicationDetailId
,TRY_CONVERT(UNIQUEIDENTIFIER, IIF(a.[VendorId] is null,'00000000-0000-0000-0000-000000000000',a.[VendorId]))VendorId
,a.[VendorName]
,a.[PaymentTerm]
,a.[HcpLevelName]
,a.[HcpLevelCode]
,a.[Hospital]
,isnull(a.[Price],'0')Price
,a.[Text]
,a.[ExtraProperties]
,a.[ConcurrencyStamp]
,a.[CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER,IIF(a.[CreatorId] is null,'00000000-0000-0000-0000-000000000000',a.[CreatorId]))CreatorId
,'1900-01-01'[LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER,'00000000-0000-0000-0000-000000000000')LastModifierId
,a.[IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER,'00000000-0000-0000-0000-000000000000')DeleterId
,'1900-01-01'[DeletionTime]
,a.[ExceptionNumber]
into #PurPRApplicationDetailBackupVendors
from PLATFORM_ABBOTT_Stg.dbo.PurPRApplicationDetailBackupVendors a 

--select * from  #PurPRApplicationDetailBackupVendors



USE Speaker_Portal_STG;

UPDATE a 
SET 
a.[PRApplicationId]                  =b.[PRApplicationId]
,a.[PRApplicationDetailId]            =b.[PRApplicationDetailId]
,a.[VendorId]                         =b.[VendorId]
,a.[VendorName]                       =b.[VendorName]
,a.[PaymentTerm]                      =b.[PaymentTerm]
,a.[HcpLevelName]                     =b.[HcpLevelName]
,a.[HcpLevelCode]                     =b.[HcpLevelCode]
,a.[Hospital]                         =b.[Hospital]
,a.[Price]                            =b.[Price]
,a.[Text]                             =b.[Text]
,a.[ExtraProperties]                  =b.[ExtraProperties]
,a.[ConcurrencyStamp]                 =b.[ConcurrencyStamp]
,a.[CreationTime]                     =b.[CreationTime]
,a.[CreatorId]                        =b.[CreatorId]
,a.[LastModificationTime]             =b.[LastModificationTime]
,a.[LastModifierId]                   =b.[LastModifierId]
,a.[IsDeleted]                        =b.[IsDeleted]
,a.[DeleterId]                        =b.[DeleterId]
,a.[DeletionTime]                     =b.[DeletionTime]
,a.[ExceptionNumber]                  =b.[ExceptionNumber]
FROM dbo.PurPRApplicationDetailBackupVendors a
left join #PurPRApplicationDetailBackupVendors  b
ON a.id=b.id;


INSERT INTO dbo.PurPRApplicationDetailBackupVendors
(
id,
[PRApplicationId]             
,[PRApplicationDetailId]      
,[VendorId]                   
,[VendorName]                 
,[PaymentTerm]                
,[HcpLevelName]               
,[HcpLevelCode]               
,[Hospital]                   
,[Price]                      
,[Text]                       
,[ExtraProperties]            
,[ConcurrencyStamp]           
,[CreationTime]               
,[CreatorId]                  
,[LastModificationTime]       
,[LastModifierId]             
,[IsDeleted]                  
,[DeleterId]                  
,[DeletionTime]               
,[ExceptionNumber]            
)
SELECT
id,
a.[PRApplicationId]             
,a.[PRApplicationDetailId]      
,a.[VendorId]                   
,a.[VendorName]                 
,a.[PaymentTerm]                
,a.[HcpLevelName]               
,a.[HcpLevelCode]               
,a.[Hospital]                   
,a.[Price]                      
,a.[Text]                       
,a.[ExtraProperties]            
,a.[ConcurrencyStamp]           
,a.[CreationTime]               
,a.[CreatorId]                  
,a.[LastModificationTime]       
,a.[LastModifierId]             
,a.[IsDeleted]                  
,a.[DeleterId]                  
,a.[DeletionTime]               
,a.[ExceptionNumber]            
FROM #PurPRApplicationDetailBackupVendors a
WHERE not exists (select * from dbo.PurPRApplicationDetailBackupVendors where id=a.id);