-- 如果数据库主密钥不存在，则创建  
IF NOT EXISTS (SELECT * FROM sys.symmetric_keys WHERE name = '##MS_DatabaseMasterKey##')  
BEGIN  
    CREATE MASTER KEY ENCRYPTION BY PASSWORD = 'QzvjraSkir0Ai7KlqK2Qk7gxOjP7MSSP';  
END  

-- 打开数据库主密钥  
OPEN MASTER KEY DECRYPTION BY PASSWORD = 'QzvjraSkir0Ai7KlqK2Qk7gxOjP7MSSP';

-- 创建对称密钥  
IF NOT EXISTS (SELECT * FROM sys.symmetric_keys WHERE name = 'AES_Key')  
BEGIN  
    CREATE SYMMETRIC KEY AES_Key  
    WITH ALGORITHM = AES_256  
    ENCRYPTION BY PASSWORD = 'AnotherStrongPassword123!';  
END

-- 打开对称密钥以进行加密  
OPEN SYMMETRIC KEY AES_Key  
DECRYPTION BY PASSWORD = 'AnotherStrongPassword123!';  

-- 加密 `name` 列的值并更新表  
UPDATE spk_staffmasterdata_BAK  
SET spk_name = ENCRYPTBYKEY(KEY_GUID('AES_Key'), spk_name);

-- 打开对称密钥以进行解密  
OPEN SYMMETRIC KEY AES_Key  
DECRYPTION BY PASSWORD = 'AnotherStrongPassword123!';  

-- 查询并转换为 Base64 编码
SELECT 
    spk_name,
    CONVERT(VARCHAR(MAX), ENCRYPTBYKEY(KEY_GUID('AES_Key'), spk_name), 2) AS Base64Encoded
FROM spk_staffmasterdata_BAK;

-- 查询并解密 `name` 列的值  
SELECT CONVERT(NVARCHAR(100), DECRYPTBYKEY(spk_name)) AS DecryptedName  
FROM spk_staffmasterdata_BAK;

-- 关闭对称密钥  
CLOSE SYMMETRIC KEY AES_Key

