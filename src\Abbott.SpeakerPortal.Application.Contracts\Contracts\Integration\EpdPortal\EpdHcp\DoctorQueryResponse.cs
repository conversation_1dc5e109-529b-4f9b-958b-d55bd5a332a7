﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Text.Json.Serialization;

namespace Abbott.SpeakerPortal.Contracts.Integration.EpdPortal.EpdHcp
{
    // 医生数据模型
    public class DoctorData
    {
        /// <summary>
        /// 医生姓名
        /// </summary>
        [JsonPropertyName("hcpName")]
        public string Name { get; set; }

        /// <summary>
        /// 医生所属医院
        /// </summary>
        [JsonPropertyName("hospitalName")]
        public string HospitalName { get; set; }

        /// <summary>
        /// EPD医院ID
        /// </summary>
        [JsonPropertyName("hospitalCode")]
        public string HospitalCode { get; set; }

        /// <summary>
        /// 职称
        /// </summary>
        [JsonPropertyName("professionalTitle")]
        public string ProfessionalTitle { get; set; }

        /// <summary>
        /// 所属标准科室
        /// </summary>
        [JsonPropertyName("departmentName")]
        public string DepartmentName { get; set; }

        /// <summary>
        /// 所属院内科室
        /// </summary>
        [JsonPropertyName("outpatientDepartmentName")]
        public string HosDepartmentName { get; set; }

        /// <summary>
        /// EPD医生主键
        /// </summary>
        [JsonPropertyName("abbottHcpId")]
        public string EpdHcpCode { get; set; }

        /// <summary>
        /// 医生手机号（AES256加密）
        /// </summary>
        [JsonPropertyName("mobile")]
        public List<string> Mobile { get; set; }

        /// <summary>
        /// 基本信息验证结果
        /// </summary>
        [JsonPropertyName("veevaHcpStatus")]
        public string HcpDcrStatus { get; set; }
    }
}
