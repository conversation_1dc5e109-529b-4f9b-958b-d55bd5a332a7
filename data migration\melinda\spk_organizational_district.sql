select newid() as spk_NexBPMCode,* 
into #spk_organizational_district 
from (
select ot.spk_BPMCode,d.spk_NexBPMCode as spk_district,ot.spk_NexBPMCode as spk_organizational,sdt.flg  from spk_organizational_district_Tmp sdt
join spk_organizationalmasterdata ot
on sdt.spk_organizational=ot.spk_BPMCode
left join (select *,ROW_NUMBER () over(PARTITION by spk_name order by spk_districtcode)rn  from spk_districtmasterdata) d
on sdt.spk_district =d.spk_name and d.rn=1)A

IF OBJECT_ID(N'dbo.spk_organizational_district', N'U') IS NOT NULL
BEGIN
	update a 
	set a.spk_BPMCode         = b.spk_BPMCode
        ,a.spk_district       = b.spk_district
        ,a.spk_organizational = b.spk_organizational
        ,a.flg                = b.flg 
    from dbo.spk_organizational_district a
    join #spk_organizational_district b on a.spk_BPMCode = b.spk_BPMCode
    
    insert into dbo.spk_organizational_district
    select a.spk_NexBPMCode
          ,a.spk_BPMCode
          ,a.spk_district
          ,a.spk_organizational
          ,a.flg 
	from #spk_organizational_district a
	where NOT EXISTS (SELECT * FROM dbo.spk_organizational_district where spk_BPMCode = a.spk_BPMCode)
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into dbo.spk_organizational_district from #spk_organizational_district
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END