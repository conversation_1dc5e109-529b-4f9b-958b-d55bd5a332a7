﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Abbott.SpeakerPortal.Consts
{
	public class EntityConsts
	{
		public class IdentityUser
		{
			public const string OpenId = "OpenId";
			public const string UnionId = "UnionId";
			public const string StaffCode = "StaffCode";
			public const string DepartmentId = "DepartmentId";
			public const string JobStatus = "JobStatus";
			public const string MainDepartmentId = "MainDepartmentId";
			public const string LastLoginTime = "LastLoginTime";
            public const string SignedVersion = "SignedVersion";
            //public const string AuthorizedBudgetBuId = "AuthorizedBudgetBuId";
        }

		public class IdentityRole
		{
			public const string IsDeleted = "IsDeleted";
            public const string DisplayName = "DisplayName";
        }
	}
}
