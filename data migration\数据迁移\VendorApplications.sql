SELECT
 TRY_CONVERT(UNIQUEIDENTIFIER, [Id]) [Id]
,isnull([ApplicationType],'0')ApplicationType
,[ApplicationCode]
,IIF([VendorCode] IS NULL or [VendorCode] = '' ,[ApplicationCode],[VendorCode]) COLLATE SQL_Latin1_General_CP1_CI_AS [VendorCode]
,[OpenId]
,[UnionId]
,[HandPhone]
,ISNULL([VendorType],0) [VendorType]
,[Status]
,TRY_CONVERT(UNIQUEIDENTIFIER, IIF([ApplyUserId] IS NULL ,'********-0000-0000-0000-************',[ApplyUserId])) [ApplyUserId]
,[ApplyUserBu]
,COALESCE(TRY_CONVERT(datetime2, ApplyTime), '1999-01-01 00:00:01') [ApplyTime]
,[VerificationStatus]
,[BpcsId]
,[EpdId]
,[MndId]
,[VendorId]
,[ApsPorperty]
,TRY_CONVERT(datetime2, LastVerifyStartTime)  [LastVerifyStartTime]
,TRY_CONVERT(datetime2, LastVerifyEndTime)  [LastVerifyEndTime]
,[CertificateCode]
,[SPLevel]
,[AcademicLevel]
,[AcademicPosition]
,[BankCode]
,[BankCardNo]
,[BankCity]
,[BankNo]
,[ExtraProperties]
,'' [ConcurrencyStamp]
,COALESCE(TRY_CONVERT(datetime2, CreationTime), '1999-01-01 00:00:01') [CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CreatorId]) [CreatorId]
,TRY_CONVERT(datetime2, LastModificationTime) [LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [LastModifierId]) [LastModifierId]
,[IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, [DeleterId]) [DeleterId]
,TRY_CONVERT(datetime2, DeletionTime) [DeletionTime]
,[BAuth]
,TRY_CONVERT(UNIQUEIDENTIFIER, [UserId]) [UserId]
,TRY_CONVERT(UNIQUEIDENTIFIER, [PTId]) [PTId]
,TRY_CONVERT(UNIQUEIDENTIFIER, [StandardHosDepId]) [StandardHosDepId]
,TRY_CONVERT(UNIQUEIDENTIFIER, [HospitalId]) [HospitalId]
,[HosDepartment]
,[BankCardImg]
,[AttachmentInformation]
,[BImproved]
,[DraftVersion]
,[SignedStatus]
,[SignedVersion]
,[DPSCheck]
,[HospitalName]
,[PTName]
,[StandardHosDepName]
,[EpdHospitalId]
,[MobileEncrypt]
,[UpdatePreJson]
,[PushVeevaResp]
,[BankSwiftCode]
,[IsAcademician]
,TRY_CONVERT(UNIQUEIDENTIFIER,TransfereeId) [TransfereeId]
,[TransfereeName]
,[FormerBPMAcademicPosition]
,[HCPType]
,[RelationType]
,TRY_CONVERT(UNIQUEIDENTIFIER,ApplyBuId) [ApplyBuId]
,[ApplyBuName]
,[ApplyDeptName]
,[ApplyUserBuName]
,[ApplyUserName]
--,[VendorApplications]
INTO #VendorApplications
FROM PLATFORM_ABBOTT.dbo.VendorApplications
;
--drop table #VendorApplications

--select * from #VendorApplications
--
--select ApplyTime,LastVerifyStartTime,LastVerifyEndTime,CreationTime,LastModificationTime,DeletionTime,* from #VendorApplications
--
--select ApplicationType,VendorType,Status,Status,Status from #VendorApplications

USE Speaker_Portal;

UPDATE a 
SET 
 a.[Id] = b.[Id]
--,a.[ApplicationType] = b.[ApplicationType]
--,a.[ApplicationCode] = b.[ApplicationCode]
--,a.[VendorCode] = b.[VendorCode]
--,a.[OpenId] = b.[OpenId]
--,a.[UnionId] = b.[UnionId]
--,a.[HandPhone] = b.[HandPhone]
--,a.[VendorType] = b.[VendorType]
--,a.[Status] = b.[Status]
--,a.[ApplyUserId] = b.[ApplyUserId]
--,a.[ApplyUserBu] = b.[ApplyUserBu]
--,a.[ApplyTime] = b.[ApplyTime]
--,a.[VerificationStatus] = b.[VerificationStatus]
--,a.[BpcsId] = b.[BpcsId]
--,a.[EpdId] = b.[EpdId]
--,a.[MndId] = b.[MndId]
--,a.[VendorId] = b.[VendorId]
--,a.[ApsPorperty] = b.[ApsPorperty]
--,a.[LastVerifyStartTime] = b.[LastVerifyStartTime]
--,a.[LastVerifyEndTime] = b.[LastVerifyEndTime]
--,a.[CertificateCode] = b.[CertificateCode]
--,a.[SPLevel] = b.[SPLevel]
--,a.[AcademicLevel] = b.[AcademicLevel]
--,a.[AcademicPosition] = b.[AcademicPosition]
--,a.[BankCode] = b.[BankCode]
--,a.[BankCardNo] = b.[BankCardNo]
--,a.[BankCity] = b.[BankCity]
--,a.[BankNo] = b.[BankNo]
--,a.[ExtraProperties] = b.[ExtraProperties]
--,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
--,a.[CreationTime] = b.[CreationTime]
--,a.[CreatorId] = b.[CreatorId]
--,a.[LastModificationTime] = b.[LastModificationTime]
--,a.[LastModifierId] = b.[LastModifierId]
--,a.[IsDeleted] = b.[IsDeleted]
--,a.[DeleterId] = b.[DeleterId]
--,a.[DeletionTime] = b.[DeletionTime]
--,a.[BAuth] = b.[BAuth]
--,a.[UserId] = b.[UserId]
--,a.[PTId] = b.[PTId]
--,a.[StandardHosDepId] = b.[StandardHosDepId]
--,a.[HospitalId] = b.[HospitalId]
--,a.[HosDepartment] = b.[HosDepartment]
--,a.[BankCardImg] = b.[BankCardImg]
--,a.[AttachmentInformation] = b.[AttachmentInformation]
--,a.[BImproved] = b.[BImproved]
--,a.[DraftVersion] = b.[DraftVersion]
--,a.[SignedStatus] = b.[SignedStatus]
--,a.[SignedVersion] = b.[SignedVersion]
--,a.[DPSCheck] = b.[DPSCheck]
--,a.[HospitalName] = b.[HospitalName]
--,a.[PTName] = b.[PTName]
--,a.[StandardHosDepName] = b.[StandardHosDepName]
--,a.[EpdHospitalId] = b.[EpdHospitalId]
--,a.[MobileEncrypt] = b.[MobileEncrypt]
,a.[UpdatePreJson] = b.[UpdatePreJson]
--,a.[PushVeevaResp] = b.[PushVeevaResp]
--,a.[BankSwiftCode] = b.[BankSwiftCode]
--,a.[IsAcademician] = b.[IsAcademician]
--,a.[TransfereeId] = b.[TransfereeId]
--,a.[TransfereeName] = b.[TransfereeName]
--,a.[FormerBPMAcademicPosition] = b.[FormerBPMAcademicPosition]
--,a.[HCPType]=b.[HCPType]
--,a.[RelationType]=b.RelationType
--,a.[ApplyBuId]=b.ApplyBuId
--,a.[ApplyBuName]=b.ApplyBuName
--,a.[ApplyDeptName]=b.ApplyDeptName
--,a.[ApplyUserBuName]=b.ApplyUserBuName
--,a.[ApplyUserName]=b.ApplyUserName
FROM dbo.VendorApplications a
left join #VendorApplications  b
ON a.id=b.id;

--drop table #VendorApplications

INSERT INTO dbo.VendorApplications
(
 [Id]
,[ApplicationType]
,[ApplicationCode]
,[VendorCode]
,[OpenId]
,[UnionId]
,[HandPhone]
,[VendorType]
,[Status]
,[ApplyUserId]
,[ApplyUserBu]
,[ApplyTime]
,[VerificationStatus]
,[BpcsId]
,[EpdId]
,[MndId]
,[VendorId]
,[ApsPorperty]
,[LastVerifyStartTime]
,[LastVerifyEndTime]
,[CertificateCode]
,[SPLevel]
,[AcademicLevel]
,[AcademicPosition]
,[BankCode]
,[BankCardNo]
,[BankCity]
,[BankNo]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[BAuth]
,[UserId]
,[PTId]
,[StandardHosDepId]
,[HospitalId]
,[HosDepartment]
,[BankCardImg]
,[AttachmentInformation]
,[BImproved]
,[DraftVersion]
,[SignedStatus]
,[SignedVersion]
,[DPSCheck]
,[HospitalName]
,[PTName]
,[StandardHosDepName]
,[EpdHospitalId]
,[MobileEncrypt]
,[UpdatePreJson]
,[PushVeevaResp]
,[BankSwiftCode]
,[IsAcademician]
,[TransfereeId]
,[TransfereeName]
,[FormerBPMAcademicPosition]
,[HCPType]
,[RelationType]
, [ApplyBuId]
,[ApplyBuName]
,[ApplyDeptName]
,[ApplyUserBuName]
,[ApplyUserName]
)
SELECT
 [Id]
,[ApplicationType]
,[ApplicationCode]
,[VendorCode]
,[OpenId]
,[UnionId]
,[HandPhone]
,[VendorType]
,[Status]
,[ApplyUserId]
,[ApplyUserBu]
,[ApplyTime]
,[VerificationStatus]
,[BpcsId]
,[EpdId]
,[MndId]
,[VendorId]
,[ApsPorperty]
,[LastVerifyStartTime]
,[LastVerifyEndTime]
,[CertificateCode]
,[SPLevel]
,[AcademicLevel]
,[AcademicPosition]
,[BankCode]
,[BankCardNo]
,[BankCity]
,[BankNo]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[BAuth]
,[UserId]
,[PTId]
,[StandardHosDepId]
,[HospitalId]
,[HosDepartment]
,[BankCardImg]
,[AttachmentInformation]
,[BImproved]
,[DraftVersion]
,[SignedStatus]
,[SignedVersion]
,[DPSCheck]
,[HospitalName]
,[PTName]
,[StandardHosDepName]
,[EpdHospitalId]
,[MobileEncrypt]
,[UpdatePreJson]
,[PushVeevaResp]
,[BankSwiftCode]
,[IsAcademician]
,TRY_CONVERT(UNIQUEIDENTIFIER,TransfereeId) [TransfereeId]
,[TransfereeName]
,[FormerBPMAcademicPosition]
,[HCPType]
,[RelationType]
,TRY_CONVERT(UNIQUEIDENTIFIER,ApplyBuId) [ApplyBuId]
,[ApplyBuName]
,[ApplyDeptName]
,[ApplyUserBuName]
,[ApplyUserName]
FROM #VendorApplications a
WHERE not exists (select * from dbo.VendorApplications where id=a.id);

--truncate table dbo.VendorApplications

select * from VendorApplications


--alter table Speaker_Portal.dbo.VendorApplications alter column [ApplicationCode] [nvarchar](50) NULL
--alter table Speaker_Portal.dbo.VendorApplications alter column [VendorCode] [nvarchar](50) NULL
--alter table Speaker_Portal.dbo.VendorApplications alter column [EpdId] [nvarchar](500) NULL
--alter table Speaker_Portal.dbo.VendorApplications alter column [MndId] [nvarchar](100) NULL
--alter table Speaker_Portal.dbo.VendorApplications alter column [ApsPorperty] [nvarchar](255) NULL
--alter table Speaker_Portal.dbo.VendorApplications alter column [SPLevel] [nvarchar](255) NULL
--alter table Speaker_Portal.dbo.VendorApplications alter column [HosDepartment] [nvarchar](50) NULL
--alter table Speaker_Portal.dbo.VendorApplications alter column [DPSCheck] [nvarchar](max) NULL


