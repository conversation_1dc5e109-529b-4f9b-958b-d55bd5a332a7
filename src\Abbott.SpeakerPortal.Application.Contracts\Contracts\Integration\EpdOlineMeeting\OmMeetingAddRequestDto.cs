﻿using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Extension;
using DocumentFormat.OpenXml.Wordprocessing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;

namespace Abbott.SpeakerPortal.Contracts.Integration.EpdOlineMeeting
{
    public class OmMeetingAddRequestDto
    {
        /// <summary>
        /// 会议主题 e.g.患者教育
        /// </summary>
        [JsonPropertyName("name")]
        public string Name { get; set; }

        /// <summary>
        /// PR单号 e.g.P2401010001
        /// </summary>
        [JsonPropertyName("serialNumberPr")]
        public string SerialNumberPr { get; set; }

        /// <summary>
        /// 会议类型 e.g.线上加线下
        /// </summary>
        [JsonPropertyName("type")]
        public string Type { get; set; }

        /// <summary>
        /// 会议城市 e.g.长沙
        /// </summary>
        [JsonPropertyName("city")]
        public string City { get; set; }

        /// <summary>
        /// 会议地点 e.g.湖南省人民医院
        /// </summary>
        [JsonPropertyName("address")]
        public string Address { get; set; }

        /// <summary>
        /// 主持人 e.g.李主持
        /// </summary>
        [JsonPropertyName("host")]
        public string Host { get; set; }

        /// <summary>
        /// 主持人邮箱 <EMAIL>
        /// </summary>
        [JsonPropertyName("hostEmail")]
        public string HostEmail { get; set; }

        /// <summary>
        /// 申请人发起日期 e.g.2023-12-05
        /// </summary>
        [JsonPropertyName("applicantName")]
        public string ApplicantName { get; set; }

        /// <summary>
        /// 申请人邮箱 <EMAIL>
        /// </summary>
        [JsonPropertyName("applicantEmail")]
        public string ApplicantEmail { get; set; }

        /// <summary>
        /// 申请发起日期 e.g.2023-12-05
        /// </summary>
        [JsonPropertyName("applicationDate")]
        public string ApplicationDate { get; set; }

        /// <summary>
        /// 申请公司 e.g.JV
        /// </summary>
        [JsonPropertyName("applicationCompany")]
        public string ApplicationCompany { get; set; }

        /// <summary>
        /// 被代理人 e.g.张代理
        /// </summary>
        [JsonPropertyName("principal")]
        public string Principal { get; set; }

        /// <summary>
        /// 被代理人邮箱 <EMAIL>
        /// </summary>
        [JsonPropertyName("principalEmail")]
        public string PrincipalEmail { get; set; }

        /// <summary>
        /// 相关产品 e.g.DUPHASTON达芙通
        /// </summary>
        [JsonPropertyName("productName")]
        public string ProductName { get; set; }

        /// <summary>
        /// 主讲者列表
        /// </summary>
        [JsonPropertyName("mainSpeakerList")]
        public List<OmMeetingMainSpeakerItemDto> MainSpeakerList { get; set; }

        /// <summary>
        /// 备用讲者列表
        /// </summary>
        [JsonPropertyName("standbySpeakerList")]
        public List<OmMeetingStandbySpeakerItemDto> StandbySpeakerList { get; set; }

        public static MessageResult Validate(OmMeetingAddRequestDto request)
        {
            if (request == null)
            {
                return MessageResult.FailureResult("Argument null!");
            }

            StringBuilder sb = new StringBuilder();

            if (string.IsNullOrWhiteSpace(request.Name))
            {
                sb.AppendLine("Empty meeting name!");
            }

            if (string.IsNullOrWhiteSpace(request.SerialNumberPr))
            {
                sb.AppendLine("Empty pr number!");
            }

            if (string.IsNullOrWhiteSpace(request.Type))
            {
                sb.AppendLine("Empty meeting type!");
            }
            else if (request.Type != "线上" && request.Type != "线下" && request.Type != "线上加线下")
            {
                sb.AppendLine("Invalid meeting type, only support:线上，线下，线上加线下!");
            }

            //if (string.IsNullOrWhiteSpace(request.Host))
            //{
            //    sb.AppendLine("Empty host!");
            //}

            //if (string.IsNullOrWhiteSpace(request.HostEmail))
            //{
            //    sb.AppendLine("Empty hostEmail!");
            //}

            if (string.IsNullOrWhiteSpace(request.ApplicantName))
            {
                sb.AppendLine("Empty application name!");
            }

            if (string.IsNullOrWhiteSpace(request.ApplicantEmail))
            {
                sb.AppendLine("Empty application email!");
            }

            if (string.IsNullOrWhiteSpace(request.ApplicationDate))
            {
                sb.AppendLine("Empty application date!");
            }
            else if (!DateTime.TryParse(request.ApplicationDate, out var date))
            {
                sb.AppendLine("Invalid application date!");
            }

            if (string.IsNullOrWhiteSpace(request.ApplicationCompany))
            {
                sb.AppendLine("Empty application company!");
            }

            if (string.IsNullOrWhiteSpace(request.ProductName))
            {
                sb.AppendLine("Empty product name!");
            }

            if (request.MainSpeakerList != null)
            {
                for (int i = 0; i < request.MainSpeakerList.Count; i++)
                {
                    if (string.IsNullOrWhiteSpace(request.MainSpeakerList[i].PlanDate))
                    {
                        sb.AppendLine("Empty plan date, main item index:" + i + 1);
                    }
                    else if (!DateTime.TryParse(request.MainSpeakerList[i].PlanDate, out DateTime planDate))
                    {
                        sb.AppendLine("Invalid plan date, main item index:" + i + 1);
                    }

                    if (string.IsNullOrWhiteSpace(request.MainSpeakerList[i].Executor))
                    {
                        sb.AppendLine("Empty executor, main item index:" + i + 1);
                    }

                    if (string.IsNullOrWhiteSpace(request.MainSpeakerList[i].ExecutorEmail))
                    {
                        sb.AppendLine("Empty executor email, main item index:" + i + 1);
                    }

                    if (string.IsNullOrWhiteSpace(request.MainSpeakerList[i].SlideName))
                    {
                        sb.AppendLine("Empty slide name, main item index:" + i + 1);
                    }

                    if (string.IsNullOrWhiteSpace(request.MainSpeakerList[i].SlideType))
                    {
                        sb.AppendLine("Empty slide type, main item index:" + i + 1);
                    }

                    if (string.IsNullOrWhiteSpace(request.MainSpeakerList[i].SpeakerLevel))
                    {
                        sb.AppendLine("Empty speaker level, main item index:" + i + 1);
                    }

                    if (string.IsNullOrWhiteSpace(request.MainSpeakerList[i].VendorName))
                    {
                        sb.AppendLine("Empty vendor name, main item index:" + i + 1);
                    }

                    if (string.IsNullOrWhiteSpace(request.MainSpeakerList[i].VendorCode))
                    {
                        sb.AppendLine("Empty vendor code, main item index:" + i + 1);
                    }

                    if (string.IsNullOrWhiteSpace(request.MainSpeakerList[i].TaxMobile))
                    {
                        //sb.AppendLine("Empty tax mobile, main item index:" + i + 1);
                        request.MainSpeakerList[i].TaxMobile = "99999999999";
                    }

                    if (string.IsNullOrWhiteSpace(request.MainSpeakerList[i].IdNumber))
                    {
                        sb.AppendLine("Empty id number, main item index:" + i + 1);
                    }

                    if (string.IsNullOrWhiteSpace(request.MainSpeakerList[i].CertificateNo))
                    {
                        sb.AppendLine("Empty certificate number, main item index:" + i + 1);
                    }

                    if (string.IsNullOrWhiteSpace(request.MainSpeakerList[i].BankNumber))
                    {
                        sb.AppendLine("Empty bank number, main item index:" + i + 1);
                    }

                    if (string.IsNullOrWhiteSpace(request.MainSpeakerList[i].HospitalName))
                    {
                        sb.AppendLine("Empty hospital name, main item index:" + i + 1);
                    }

                    if (string.IsNullOrWhiteSpace(request.MainSpeakerList[i].DepartmentName))
                    {
                        sb.AppendLine("Empty department name, main item index:" + i + 1);
                    }

                    if (string.IsNullOrWhiteSpace(request.MainSpeakerList[i].ProfessionalTitle))
                    {
                        sb.AppendLine("Empty profession title, main item index:" + i + 1);
                    }

                    if (string.IsNullOrWhiteSpace(request.MainSpeakerList[i].RmbAmount))
                    {
                        sb.AppendLine("Empty rmb amount, main item index:" + i + 1);
                    }
                    else if (!decimal.TryParse(request.MainSpeakerList[i].RmbAmount, out decimal rmbAmount))
                    {
                        sb.AppendLine("Invalid rmb amount, main item index:" + i + 1);
                    }

                    if (string.IsNullOrWhiteSpace(request.MainSpeakerList[i].AbbottHcpId))
                    {
                        sb.AppendLine("Empty abbott hcp id, main item index:" + i + 1);
                    }

                    if (string.IsNullOrWhiteSpace(request.MainSpeakerList[i].NextBpmHcpId))
                    {
                        sb.AppendLine("Empty next bpm hcp id, main item index:" + i + 1);
                    }
                }
            }

            if (request.StandbySpeakerList != null)
            {
                for (int i = 0; i < request.StandbySpeakerList.Count; i++)
                {
                    if (string.IsNullOrWhiteSpace(request.StandbySpeakerList[i].SpeakerLevel))
                    {
                        sb.AppendLine("Empty speaker level, standby item index:" + i + 1);
                    }

                    if (string.IsNullOrWhiteSpace(request.StandbySpeakerList[i].VendorName))
                    {
                        sb.AppendLine("Empty vendor name, standby item index:" + i + 1);
                    }

                    if (string.IsNullOrWhiteSpace(request.StandbySpeakerList[i].VendorCode))
                    {
                        sb.AppendLine("Empty vendor code, standby item index:" + i + 1);
                    }

                    if (string.IsNullOrWhiteSpace(request.StandbySpeakerList[i].TaxMobile))
                    {
                        //sb.AppendLine("Empty tax mobile, standby item index:" + i + 1);
                        request.StandbySpeakerList[i].TaxMobile = "99999999999";
                    }

                    if (string.IsNullOrWhiteSpace(request.StandbySpeakerList[i].IdNumber))
                    {
                        sb.AppendLine("Empty id number, standby item index:" + i + 1);
                    }

                    if (string.IsNullOrWhiteSpace(request.StandbySpeakerList[i].CertificateNo))
                    {
                        sb.AppendLine("Empty certificate number, standby item index:" + i + 1);
                    }

                    if (string.IsNullOrWhiteSpace(request.StandbySpeakerList[i].BankNumber))
                    {
                        sb.AppendLine("Empty bank number, standby item index:" + i + 1);
                    }

                    if (string.IsNullOrWhiteSpace(request.StandbySpeakerList[i].HospitalName))
                    {
                        sb.AppendLine("Empty hospital name, standby item index:" + i + 1);
                    }

                    if (string.IsNullOrWhiteSpace(request.StandbySpeakerList[i].DepartmentName))
                    {
                        sb.AppendLine("Empty department name, standby item index:" + i + 1);
                    }

                    if (string.IsNullOrWhiteSpace(request.StandbySpeakerList[i].ProfessionalTitle))
                    {
                        sb.AppendLine("Empty profession title, standby item index:" + i + 1);
                    }

                    if (string.IsNullOrWhiteSpace(request.StandbySpeakerList[i].RmbAmount))
                    {
                        sb.AppendLine("Empty rmb amount, standby item index:" + i + 1);
                    }
                    else if (!decimal.TryParse(request.StandbySpeakerList[i].RmbAmount, out decimal rmbAmount))
                    {
                        sb.AppendLine("Invalid rmb amount, standby item index:" + i + 1);
                    }

                    if (string.IsNullOrWhiteSpace(request.StandbySpeakerList[i].AbbottHcpId))
                    {
                        sb.AppendLine("Empty abbott hcp id, standby item index:" + i + 1);
                    }

                    if (string.IsNullOrWhiteSpace(request.StandbySpeakerList[i].NextBpmHcpId))
                    {
                        sb.AppendLine("Empty next bpm hcp id, standby item index:" + i + 1);
                    }
                }
            }

            if (sb.Length > 0)
            {
                return MessageResult.FailureResult(sb.ToString());
            }

            return MessageResult.SuccessResult();
        }

        public static List<int> GetSpeakersNo(OmMeetingAddRequestDto instance)
        {
            if (instance == null)
            {
                return default;
            }

            var result = new List<int>();
            result.AddRangeIf(instance.MainSpeakerList?.Any() == true, instance.MainSpeakerList.Select(a => a.No).Distinct());
            result.AddRangeIf(instance.StandbySpeakerList?.Any() == true, instance.StandbySpeakerList.Select(a => a.No).Distinct());
            return result;
        }
    }
}
