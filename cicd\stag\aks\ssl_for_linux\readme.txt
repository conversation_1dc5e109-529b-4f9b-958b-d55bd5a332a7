pfx convert to cer:
openssl pkcs12 -nodes -nokeys -in speaker-portal-api-d.oneabbott.com.pfx  -out speaker-portal-api-d.oneabbott.com.cer
openssl pkcs12 -nodes -nocerts -in speaker-portal-api-d.oneabbott.com.pfx  -out speaker-portal-api-d.oneabbott.com.key

openssl pkcs12 -nodes -nokeys -in speaker-int-api-s.oneabbott.com.pfx  -out speaker-int-api-s.oneabbott.com.cer
openssl pkcs12 -nodes -nocerts -in speaker-int-api-s.oneabbott.com.pfx  -out speaker-int-api-s.oneabbott.com.key


cmd:
kubectl create secret tls tls-speaker-portal-api-d-secret -n default --key=speaker-portal-api-d.oneabbott.com.key --cert=speaker-portal-api-d.oneabbott.com.cer
kubectl create secret tls tls-speaker-int-api-s-secret -n default --key=speaker-int-api-s.oneabbott.com.key --cert=speaker-int-api-s.oneabbott.com.cer
