﻿using Abbott.SpeakerPortal.Contracts.STicket;
using Abbott.SpeakerPortal.Entities.EFlow.STicket;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Extension;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Contracts.Agent;
using DocumentFormat.OpenXml.Spreadsheet;
using Abbott.SpeakerPortal.Entities.Common.Workflow.WorkflowTasks;
using DocumentFormat.OpenXml.Office2010.Excel;
using Microsoft.AspNetCore.Mvc.ApplicationModels;
using Senparc.Weixin.WxOpen.AdvancedAPIs.WxApp.WxAppJson;
using System.ServiceModel;
using Senparc.Weixin.WxOpen.Entities;

namespace Abbott.SpeakerPortal.AppServices.STicket
{
    public partial class STicketService : SpeakerPortalAppService, ISTicketService
    {
        /// <summary>
        /// 获取我发起的任务
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetSTicketAppliedByMeResponseDto>> GetAppliedByMeAsync(GetSTicketAppliedByMeRequestDto request)
        {
            STicketStatus[] pendingProcessings = [STicketStatus.RejectedBack, STicketStatus.ReCall];
            STicketStatus[] progressings = [STicketStatus.Approving, STicketStatus.Approved, STicketStatus.SOIInProcess];
            STicketStatus[] completeds = [STicketStatus.Rejected, STicketStatus.ApplicantTerminate, STicketStatus.Settled, STicketStatus.PendingSettlement, STicketStatus.SOICancelled];
            var agents = await LazyServiceProvider.LazyGetService<IAgencyService>().GetOriginalOperators(new GetAgentOperatorsRequestDto { BusinessTypeCategory = ResignationTransfer.TaskFormCategory.STicketRequestApplication });
            var userIds = agents.Select(a => a.Key).Distinct().ToArray();
            var sTicketQuery = (await LazyServiceProvider.LazyGetService<ISTicketApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            var sTicketDetailQuery = (await LazyServiceProvider.LazyGetService<ISTicketApplicationDetailRepository>().GetQueryableAsync()).AsNoTracking();
            //var stutasQuery = sTicketDetailQuery.Select(s => new { s.ParentID, Status = (s.SubVerificationStatus.HasValue ? (int)s.SubVerificationStatus * 10 : 1) + (s.CutOffStatus.HasValue ? (int)s.CutOffStatus : 1) })
            //    .Select(s => new
            //    {
            //        s.ParentID,
            //        Status =
            //        s.Status == 11 ? 1 ://SOI未核销
            //        s.Status == 14 ? 2 ://SOI已取消
            //        s.Status == 12 ? 2 ://SOI已截止
            //        s.Status == 21 ? 1 ://SOI部分核销
            //        s.Status == 22 ? 2 ://SOI已截止
            //        s.Status == 31 ? 3 ://SOI已核销
            //        s.Status == 32 ? 3 ://SOI已核销
            //        1
            //    })
            //    .GroupBy(g => g.ParentID, (key, val) => new { key, avg = val.Average(a => a.Status) < 2 ? "SOl核销中" : val.Average(a => a.Status) == 4 ? "SOl已完成" : "SOI已取消" });
            var query = sTicketQuery.Where(a => userIds.Contains(a.ApplyUserId) || userIds.Contains(a.TransfereeId.Value))
                        .WhereIf(request.ApplicantId.HasValue, m => m.ApplyUserId == request.ApplicantId)
                        .WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), a => a.ApplicationCode.Contains(request.ApplicationCode))
                        .WhereIf(request.StartDate.HasValue, a => a.ApplyTime >= request.StartDate)
                        .WhereIf(request.EndDate.HasValue, a => a.ApplyTime <= request.EndDate.Value.Date.AddDays(1))
                        .WhereIf(!string.IsNullOrEmpty(request.ApplyDeptName), a => a.ApplyUserDeptName.Contains(request.ApplyDeptName))
                        .WhereIf(!string.IsNullOrEmpty(request.BudgetCode), a => a.SubBudgetCode == request.BudgetCode)
                        .WhereIf(request.Status.HasValue, a => a.Status == request.Status)
                        .WhereIf(request.CompanyId.HasValue, a => a.CompanyId == request.CompanyId)
                        //查询结算周期有交集的数据
                        .WhereIf(request.SettlementPeriodStart.HasValue && request.SettlementPeriodEnd.HasValue, m => m.SettlementPeriodStart <= request.SettlementPeriodEnd && m.SettlementPeriodEnd >= request.SettlementPeriodStart);
            switch (request.ProcessingStatus)
            {
                case ProcessingStatus.PendingProcessing:
                    query = query.Where(m => pendingProcessings.Contains(m.Status));
                    break;
                case ProcessingStatus.Progressing:
                    query = query.Where(m => progressings.Contains(m.Status));
                    break;
                case ProcessingStatus.Completed:
                    query = query.Where(m => completeds.Contains(m.Status));
                    break;
                default:
                    break;
            }
            var count = await query.CountAsync();
            var datas = await query.OrderByDescending(a => a.ApplyTime).PagingIf(request).ToListAsync();
            var result = ObjectMapper.Map<List<STicketApplication>, List<GetSTicketAppliedByMeResponseDto>>(datas);
            return new PagedResultDto<GetSTicketAppliedByMeResponseDto>(count, result);
        }

        /// <summary>
        /// 待我审批
        /// </summary>
        /// <param name="request"></param>
        /// <param name="processingStatus"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetSTicketApprovedByMeResponseDto>> GetApprovedByMeAsync(GetSTicketApprovedByMeRequestDto request)
        {
            var sTicketQuery = (await LazyServiceProvider.LazyGetService<ISTicketApplicationRepository>().GetQueryableAsync()).AsNoTracking();

            //获取代理信息
            //var agents = await LazyServiceProvider.LazyGetService<IAgencyService>().GetOriginalOperators(new GetAgentOperatorsRequestDto { BusinessTypeCategory = ResignationTransfer.TaskFormCategory.STicketRequestApplication });
            //var userIds = agents.Select(a => a.Key).Distinct().ToArray();
            var query = sTicketQuery
                        .WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), a => a.ApplicationCode.Contains(request.ApplicationCode))
                        .WhereIf(request.ApplicantId.HasValue, m => m.ApplyUserId == request.ApplicantId)
                        .WhereIf(request.StartDate.HasValue, a => a.ApplyTime >= request.StartDate)
                        .WhereIf(request.EndDate.HasValue, a => a.ApplyTime <= request.EndDate.Value.Date.AddDays(1))
                        .WhereIf(!string.IsNullOrEmpty(request.ApplyDeptName), a => a.ApplyUserDeptName.Contains(request.ApplyDeptName))
                        .WhereIf(!string.IsNullOrEmpty(request.BudgetCode), a => a.SubBudgetCode == request.BudgetCode)
                        .WhereIf(request.Status.HasValue, a => a.Status == request.Status)
                        .WhereIf(request.CompanyId.HasValue, a => a.CompanyId == request.CompanyId)
                        //查询结算周期有交集的数据
                        .WhereIf(request.SettlementPeriodStart.HasValue && request.SettlementPeriodEnd.HasValue, m => m.SettlementPeriodStart <= request.SettlementPeriodEnd && m.SettlementPeriodEnd >= request.SettlementPeriodStart);
            if (request.ProcessingStatus == ProcessingStatus.PendingProcessing)
            {
                var taskRecords = await LazyServiceProvider.LazyGetService<IDataverseService>().GetApprovelTaskAsync(CurrentUser.Id.ToString(), [WorkflowTypeName.STicketRequest], request.ProcessingStatus);
                var formIds = taskRecords.Select(a1 => a1.FormId).ToArray();
                var datas = await query.Where(m => formIds.Contains(m.Id)).Select(s => new
                {
                    s.Id,
                    s.ApplicationCode,
                    s.ApplyUser,
                    s.ApplyUserDeptName,
                    //s.CostCenter,
                    s.ExpenseTypeName,
                    s.SubBudgetCode,
                    s.CompanyName,
                    s.ClientName,
                    s.Status,
                    s.TotalAmountRMB,
                    s.ApplyTime,
                    s.SettlementPeriodStart,
                    s.SettlementPeriodEnd,
                    s.ApprovedDate,
                    s.SubBudgetDesc
                }).ToListAsync();
                var count = datas.Count;
                var result = datas.Join(taskRecords, a => a.Id, a => a.FormId, (a, b) => new GetSTicketApprovedByMeResponseDto
                {
                    Id = a.Id,
                    ApplicationCode = a.ApplicationCode,
                    ApplyUser = a.ApplyUser,
                    ApplyUserDeptName = a.ApplyUserDeptName,
                    //CostCenter = a.CostCenter,
                    ExpenseTypeName = a.ExpenseTypeName,
                    SubBudgetCode = a.SubBudgetCode,
                    CompanyName = a.CompanyName,
                    ClientName = a.ClientName,
                    Status = a.Status,
                    Amount = a.TotalAmountRMB,
                    ApplyTime = a.ApplyTime,
                    SettlementPeriodStart = a.SettlementPeriodStart,
                    SettlementPeriodEnd = a.SettlementPeriodEnd,
                    TaskId = b.TaskId,
                    ApprovalTime = a.ApprovedDate,
                    SubBudgetDesc = a.SubBudgetDesc,
                }).OrderByDescending(o => o.ApplyTime).PagingIf(request).ToArray();
                return new PagedResultDto<GetSTicketApprovedByMeResponseDto>(count, result);
            }
            else
            {
                var queryWfTask = (await LazyServiceProvider.LazyGetService<IWorkflowTaskRepository>().GetQueryableAsync()).AsNoTracking().Where(a => a.ApprovalId == CurrentUser.Id && a.Status != ApprovalOperation.Start);
                var result = query.Join(queryWfTask, a => a.Id, a => a.FormId, (a, b) => new GetSTicketApprovedByMeResponseDto
                {
                    Id = a.Id,
                    ApplicationCode = a.ApplicationCode,
                    ApplyUser = a.ApplyUser,
                    ApplyUserDeptName = a.ApplyUserDeptName,
                    //CostCenter = a.CostCenter,
                    ExpenseTypeName = a.ExpenseTypeName,
                    SubBudgetCode = a.SubBudgetCode,
                    CompanyName = a.CompanyName,
                    ClientName = a.ClientName,
                    Status = a.Status,
                    Amount = a.TotalAmountRMB,
                    ApplyTime = a.ApplyTime,
                    SettlementPeriodStart = a.SettlementPeriodStart,
                    SettlementPeriodEnd = a.SettlementPeriodEnd,
                    TaskId = b.Id,
                    ApprovalTime = b.ApprovalTime,
                    SubBudgetDesc = a.SubBudgetDesc,
                }).OrderByDescending(o => o.ApplyTime);
                var count = await result.CountAsync();
                var datas = await result.PagingIf(request).ToListAsync();
                return new PagedResultDto<GetSTicketApprovedByMeResponseDto>(count, datas);
            }
        }
    }
}
