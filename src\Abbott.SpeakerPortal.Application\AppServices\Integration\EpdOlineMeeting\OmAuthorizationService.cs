﻿using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Integration.EpdOlineMeeting;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Models;

using DocumentFormat.OpenXml.InkML;

using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

using Volo.Abp.OpenIddict.Applications;

namespace Abbott.SpeakerPortal.AppServices.Integration.EpdOlineMeeting
{
    public class OmAuthorizationService : SpeakerPortalAppService, IOmAuthorizationService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<OmAuthorizationService> _logger;
        private readonly IConfiguration _configuration;

        public OmAuthorizationService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _logger = serviceProvider.GetService<ILogger<OmAuthorizationService>>();
            _configuration = serviceProvider.GetService<IConfiguration>();
        }

        public Dictionary<string, string> BuildAuthorizationHeaders()
        {
            try
            {
                var result = new Dictionary<string, string>();
                var appId = _configuration["Integrations:OM:AppId"];
                var appSecret = _configuration["Integrations:OM:AppSecret"];
                var appVersion = _configuration["Integrations:OM:AppVersion"];

                DateTime dateTime = DateTime.UtcNow;
                TimeSpan timeSpan = dateTime - new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
                long timestamp = (long)timeSpan.TotalSeconds;
                var data = $"{appId}-{appSecret}-{timestamp}-{appVersion}";

                HMACSHA256 hmac = new HMACSHA256(Encoding.UTF8.GetBytes(appSecret));
                byte[] hashValue = hmac.ComputeHash(Encoding.UTF8.GetBytes(data));
                var sign = BitConverter.ToString(hashValue).Replace("-", "").ToLowerInvariant();

                result.Add("appId", appId);
                result.Add("timestamp", timestamp.ToString());
                result.Add("appVersion", appVersion);
                result.Add("sign", sign);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to builder authorization headers, error message:" + ex.Message);
                return null;
            }

            //try
            //{
            //    var result = new Dictionary<string, string>();
            //    var appId = ClientIdScopeConsts.OnlineMeeting;
            //    var openiddictApplicationExtra = LazyServiceProvider.LazyGetRequiredService<ICommonService>().GetOpenIddictApplicationExtraPropertyAsync(appId).GetAwaiter().GetResult();
            //    var appSecret = openiddictApplicationExtra.Salt;
            //    var appVersion = _configuration["Integrations:OM:AppVersion"];

            //    var timestamp = DateTimeOffset.Now.ToUnixTimeSeconds();
            //    var data = $"{appId}-{appSecret}-{timestamp}-{appVersion}";

            //    HMACSHA256 hmac = new HMACSHA256(Encoding.UTF8.GetBytes(appSecret));
            //    byte[] hashValue = hmac.ComputeHash(Encoding.UTF8.GetBytes(data));
            //    var sign = BitConverter.ToString(hashValue).Replace("-", "").ToLowerInvariant();

            //    result.Add("appId", appId);
            //    result.Add("timestamp", timestamp.ToString());
            //    result.Add("appVersion", appVersion);
            //    result.Add("sign", sign);

            //    return result;
            //}
            //catch (Exception ex)
            //{
            //    _logger.LogError("Failed to builder authorization headers, error message:" + ex.Message);
            //    return null;
            //}
        }

        public MessageResult ValidateAuthorization(OmAuthorizationDto request)
        {
            try
            {
                MessageResult validateResult = OmAuthorizationDto.Validate(request, _configuration);
                if (!validateResult.Success)
                    return validateResult;

                var sign = ValidateSign(request).GetAwaiter().GetResult();
                if (sign != request.Sign)
                    return MessageResult.FailureResult(new object(), new MessageModelBase { Code = 403, Message = "Failed to validate sign, invalid sign!" });

                return MessageResult.SuccessResult();
            }
            catch (Exception ex)
            {
                return MessageResult.FailureResult("Failed to validate sign, error message:" + ex.Message);
            }
        }

        async Task<string> ValidateSign(OmAuthorizationDto request)
        {
            var openiddictApplicationExtra = await LazyServiceProvider.LazyGetRequiredService<ICommonService>().GetOpenIddictApplicationExtraPropertyAsync(request.AppId);
            //var data = $"{request.Id}-{request.AppId}-{openiddictApplicationExtra.Salt}-{request.Timestamp}-{request.AppVersion}";
            var data = $"{request.AppId}-{openiddictApplicationExtra.Salt}-{request.Timestamp}-{request.AppVersion}";
            HMACSHA256 hmac = new HMACSHA256(Encoding.UTF8.GetBytes(openiddictApplicationExtra.Salt));
            byte[] hashValue = hmac.ComputeHash(Encoding.UTF8.GetBytes(data));
            var sign = BitConverter.ToString(hashValue).Replace("-", "").ToLowerInvariant();

            return sign;
        }

        /// <summary>
        /// 构建打印页参数
        /// </summary>
        /// <returns></returns>
        public async Task<(string Parameter, string Sign)> BuildSignParametersAsync(Guid id)
        {
            var appId = ClientIdScopeConsts.OnlineMeeting;
            var openiddictApplicationExtra = await LazyServiceProvider.LazyGetRequiredService<ICommonService>().GetOpenIddictApplicationExtraPropertyAsync(appId);
            var appVersion = _configuration["Integrations:OM:AppVersion"];
            var timestamp = DateTimeOffset.Now.ToUnixTimeSeconds();
            var data = $"{appId}-{openiddictApplicationExtra.Salt}-{timestamp}-{appVersion}";

            HMACSHA256 hmac = new HMACSHA256(Encoding.UTF8.GetBytes(openiddictApplicationExtra.Salt));
            byte[] hashValue = hmac.ComputeHash(Encoding.UTF8.GetBytes(data));
            var sign = BitConverter.ToString(hashValue).Replace("-", "").ToLowerInvariant();
            var parameter = $"?id={id}&appId={appId}&timestamp={timestamp}&appVersion={appVersion}&sign={sign}";

            return (parameter, sign);
        }
    }
}
