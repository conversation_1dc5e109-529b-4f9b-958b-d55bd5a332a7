SELECT
 TRY_CONVERT(UNIQUEIDENTIFIER, a.[Id]) [Id]
,TRY_CONVERT(UNIQUEIDENTIFIER, IIF(a.[PrId] is null,'00000000-0000-0000-0000-000000000000',a.[PrId])) [PrId]
,a.[PdRowNo]
,TRY_CONVERT(UNIQUEIDENTIFIER, IIF(b.[id] is null,'00000000-0000-0000-0000-000000000000',b.[id])) [SubbudgetId]
,[Amount]
,GETDATE() AS [OperateTime]
,1 AS [IsEnable]
,TRY_CONVERT(UNIQUEIDENTIFIER, NEWID()) [ReturnSourceId]
,a.[ReturnSourceCode]
,a.[ExtraProperties]
,'NULL' AS [ConcurrencyStamp]
,GETDATE() AS [CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, a.[CreatorId]) [CreatorId]
,a.[LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, a.[LastModifierId]) [LastModifierId]
,0 As [IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, a.[DeleterId]) [DeleterId]
,a.[DeletionTime]
INTO #BdBudgetReturns
FROM (SELECT DISTINCT *,ROW_NUMBER() OVER(PARTITION BY Id ORDER BY Id) RK FROM PLATFORM_ABBOTT_STG.dbo.BdBudgetReturns) AS a
LEFT JOIN PLATFORM_ABBOTT_Stg.dbo.BdSubBudgets AS b
ON a.SubBudgetId = b.id
WHERE RK = 1

--drop table #BdBudgetReturns


USE Speaker_Portal_STG;

UPDATE a
SET
-- a.[Id] = b.[Id]
 a.[PrId] = b.[PrId]
,a.[PdRowNo] = b.[PdRowNo]
,a.[SubbudgetId] = b.[SubbudgetId]
,a.[Amount] = b.[Amount]
,a.[OperateTime] = b.[OperateTime]
,a.[IsEnable] = b.[IsEnable]
,a.[ReturnSourceId] = b.[ReturnSourceId]
,a.[ReturnSourceCode] = b.[ReturnSourceCode]
,a.[ExtraProperties] = b.[ExtraProperties]
,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = b.[CreatorId]
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = b.[LastModifierId]
,a.[IsDeleted] = b.[IsDeleted]
,a.[DeleterId] = b.[DeleterId]
,a.[DeletionTime] = b.[DeletionTime]
FROM dbo.BdBudgetReturns a
left join #BdBudgetReturns  b
ON a.id=b.id
where a.SubbudgetId <>'00000000-0000-0000-0000-000000000000'
and  a.PrId  <>'00000000-0000-0000-0000-000000000000'

INSERT INTO dbo.BdBudgetReturns
SELECT
 [Id]
,[PrId]
,[PdRowNo]
,[SubbudgetId]
,[Amount]
,[OperateTime]
,[IsEnable]
,[ReturnSourceId]
,[ReturnSourceCode]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
FROM #BdBudgetReturns a
WHERE not exists (select * from dbo.BdBudgetReturns where id=a.id)
and  SubbudgetId <>'00000000-0000-0000-0000-000000000000'
and  PrId  <>'00000000-0000-0000-0000-000000000000'

--select * into dbo.BdBudgetReturns_bak20240826 from dbo.BdBudgetReturns

