﻿using Abbott.SpeakerPortal.Enums;
using MiniExcelLibs.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Reflection;
using System.Text;

namespace Abbott.SpeakerPortal.Contracts.Budget
{
    public class HistoryRecordsExcelResponseDto
    {
        [ExcelIgnore]
        public Guid Id { get; set; }
        /// <summary>
        /// 操作人ID
        /// </summary>
        [ExcelIgnore]
        public Guid OperatorId { get; set; }
        /// <summary>
        /// 操作人
        /// </summary>
        [ExcelColumnName("操作人")]
        [ExcelColumnWidth(20)]

        public string OperatorName { get; set; }
        /// <summary>
        /// 操作时间
        /// </summary>
        //[ExcelIgnore]
        [ExcelColumnName("操作时间")]
        [ExcelColumnWidth(30)]
        [ExcelFormat("yyyy-MM-dd HH:mm:ss")]
        public DateTime OperatingTime { get; set; }

        //public string OperatingTimesStr
        //{
        //    get
        //    {
        //        return this.OperatingTime.ToString("yyyy-MM-dd HH:mm:ss");
        //    }
        //}
        /// <summary>
        /// 操作状态
        /// </summary>
        [ExcelIgnore]
        public OperateType OperateType { get; set; }

        [ExcelColumnName("操作类型")]
        [ExcelColumnWidth(20)]
        public string OperateTypeStr
        {
            get
            {
                if (!Enum.IsDefined(typeof(OperateType), OperateType))
                {
                    return "";
                }
                return this.OperateType.GetType().GetField(this.OperateType.ToString()).
                GetCustomAttribute<DescriptionAttribute>()?.Description;
            }
        }

        /// <summary>
        /// 操作金额
        /// </summary>
        [ExcelColumnName("操作金额")]
        public decimal? OperateAmount { get; set; }
        /// <summary>
        /// 操作内容
        /// </summary>
        [ExcelColumnWidth(80)]
        [ExcelColumnName("操作内容")]
        public string OperateContent { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        [ExcelColumnWidth(70)]
        [ExcelColumnName("备注")]
        public string Remark { get; set; }
    }
}
