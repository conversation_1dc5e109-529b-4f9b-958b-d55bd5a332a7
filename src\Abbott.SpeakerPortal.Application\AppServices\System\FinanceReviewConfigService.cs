﻿using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.System.FinanceReview;
using Abbott.SpeakerPortal.Contracts.System.Slide;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.SystemConfig.FinanceReview;
using Abbott.SpeakerPortal.Enums;
using Microsoft.Extensions.Logging;
using Microsoft.Xrm.Sdk;
using MiniExcelLibs;
using Senparc.CO2NET.Extensions;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection.Emit;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.SettingManagement;
using Volo.Abp.Settings;

namespace Abbott.SpeakerPortal.AppServices.System
{
    public class FinanceReviewConfigService : SpeakerPortalAppService, IFinanceReviewConfigService
    {
        private readonly ILogger<FinanceReviewConfigService> _logger;
        public FinanceReviewConfigService(ILogger<FinanceReviewConfigService> logger)
        {
            _logger = logger;
        }
        public async Task<MessageResult> AddSecondReviewUserAsync(SecondReviewEditRequestDto editRequest)
        {
            var reviewConfigRepository = LazyServiceProvider.LazyGetService<IFinanceReviewConfigRepository>();
            var reviewConfigHistoryRepository = LazyServiceProvider.LazyGetService<IFinanceReviewConfigHistoryRepository>();
            var queryReviewConfig = await reviewConfigRepository.GetQueryableAsync();
            var queryReviewConfigHistory = await reviewConfigHistoryRepository.GetQueryableAsync();
            if (string.IsNullOrWhiteSpace(editRequest.FirstReviewUserName))
            {
                return MessageResult.FailureResult("初审人不能为空");
            }
            OperType? operateType = null;
            var config = queryReviewConfig.Where(a => a.FirstReviewUserId == editRequest.FirstReviewUserId).FirstOrDefault();
            if (config == null)
            {
                var configs = new FinanceReviewConfig();
                configs.FirstReviewUserId = editRequest.FirstReviewUserId;
                configs.FirstReviewUserName = editRequest.FirstReviewUserName;
                configs.SecondReviewUserId = editRequest.SecondReviewUserId;
                configs.SecondReviewUserName = editRequest.SecondReviewUserName;
                configs.UpdateTime = DateTime.Now;
                config = await reviewConfigRepository.InsertAsync(configs);
                operateType = OperType.Create;
            }
            else
            {
                config.SecondReviewUserId = editRequest.SecondReviewUserId;
                config.SecondReviewUserName = editRequest.SecondReviewUserName;
                config.UpdateTime = DateTime.Now;
                await reviewConfigRepository.UpdateAsync(config);
                operateType = OperType.Modify;
            }
            var historyConfig = new FinanceReviewConfigHistory();
            historyConfig.ParentId = config.Id;
            historyConfig.UserId = CurrentUser.Id.Value;
            historyConfig.UserName = CurrentUser.UserName;
            historyConfig.OperateTime = DateTime.Now;
            historyConfig.SecondReviewUserId = config.SecondReviewUserId;
            historyConfig.SecondReviewUserName = config.SecondReviewUserName;
            historyConfig.OperateType = operateType;
            await reviewConfigHistoryRepository.InsertAsync(historyConfig);
            return MessageResult.SuccessResult();
        }

        public async Task<MessageResult> CirculationAmountConfigAsync(decimal amount)
        {
            try
            {
                var setting = LazyServiceProvider.LazyGetService<ISettingManager>();
                await setting.SetGlobalAsync(SettingsConst.SecondReviewCirculationAmount, amount.ToString());
            }
            catch (Exception ex)
            {
                _logger.LogError($"CirculationAmountConfigAsync error:{ex.Message}");
                return MessageResult.FailureResult(ex.Message);
            }
            return MessageResult.SuccessResult(amount.ToString());
        }

        public async Task<MessageResult> GetCirculationAmountConfigAsync()
        {
            decimal amount = 0;
            try
            {
                var settingAmount = await LazyServiceProvider.LazyGetService<ISettingRepository>().FindAsync(SettingsConst.SecondReviewCirculationAmount, "G", null);
                decimal.TryParse(settingAmount?.Value, out amount);
            }
            catch (Exception ex)
            {
                _logger.LogError($"GetCirculationAmountConfigAsync error:{ex.Message}");
                return MessageResult.FailureResult(ex.Message);
            }
            return MessageResult.SuccessResult(amount.ToString());
        }

        public async Task<Stream> ExportFinanceReviewConfigListAsync(FinanceReviewConfigListRequstDto financeReviewConfigListRequst)
        {
            try
            {
                var financeReviewConfigList = await GetFinanceReviewConfigListAsync(financeReviewConfigListRequst, true);
                var data = financeReviewConfigList.Items.Select(a => new ExportFinanceReviewConfigDto
                {
                    FirstReviewUserName = a.FirstReviewUserName,
                    SecondReviewUserName = a.SecondReviewUserName,
                    UpdateTime = a.UpdateTime.HasValue ? a.UpdateTime.Value.ToString("yyyy-MM-dd HH:mm:ss") : ""
                });
                MemoryStream stream = new();
                stream.SaveAs(data, true, "SheetName");
                stream.Seek(0, SeekOrigin.Begin);
                return stream;
            }
            catch (Exception ex)
            {
                _logger.LogError($"ExportFinanceReviewConfigListAsync error:{ex.Message}");
                return null;
            }
        }

        public async Task<PagedResultDto<FinanceReviewConfigDto>> GetFinanceReviewConfigListAsync(FinanceReviewConfigListRequstDto financeReviewConfigListRequst, bool isExcept)
        {
            var result = new PagedResultDto<FinanceReviewConfigDto>();
            var reviewConfigRepository = LazyServiceProvider.LazyGetService<IFinanceReviewConfigRepository>();
            var queryReviewConfig = await reviewConfigRepository.GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var initialApprovals = await dataverseService.GetStaffAndPositionAsync(((int)PositionType.FinancialInitialApproval).ToString(), null);
            initialApprovals = initialApprovals.Where(x => x.StaffId.HasValue && x.OrganizationId.HasValue);
            if (initialApprovals == null)
                return result;
            initialApprovals = initialApprovals.GroupBy(a => a.StaffId).Select(a => a.FirstOrDefault()).ToList();
            var query = initialApprovals.GroupJoin(queryReviewConfig, a => a.StaffId, b => b.FirstReviewUserId, (a, b) => new { initial = a, FirstReviews = b })
                .SelectMany(a => a.FirstReviews.DefaultIfEmpty(), (a, b) => new { a.initial, FirstReview = b })
                .WhereIf(!string.IsNullOrWhiteSpace(financeReviewConfigListRequst.FirstReviewUserName), a => a.initial.StaffName.Contains(financeReviewConfigListRequst.FirstReviewUserName))
                .WhereIf(!string.IsNullOrWhiteSpace(financeReviewConfigListRequst.SecondReviewUserName), a => a.FirstReview != null && a.FirstReview.SecondReviewUserName.Contains(financeReviewConfigListRequst.SecondReviewUserName))
                .OrderBy(a => a.initial.StaffId)
                .Select(a => new FinanceReviewConfigDto
                {
                    Id = a.FirstReview?.Id,
                    FirstReviewUserId = a.initial.StaffId.Value,
                    FirstReviewUserName = a.initial.StaffName,
                    SecondReviewUserId = a.FirstReview?.SecondReviewUserId,
                    SecondReviewUserName = a.FirstReview?.SecondReviewUserName,
                    UpdateTime = a.FirstReview == null ? null : a.FirstReview.UpdateTime
                });
            var queryData = new List<FinanceReviewConfigDto>();
            if (!isExcept)
            {
                queryData = query.Skip(financeReviewConfigListRequst.PageIndex * financeReviewConfigListRequst.PageSize)
                .Take(financeReviewConfigListRequst.PageSize)
                .ToList();
            }
            else
            {
                queryData = query.ToList();
            }
            result.TotalCount = query.Count();
            result.Items = queryData;
            return result;
        }

        public async Task<List<FinanceReviewConfigHistorysDto>> GetOperateHistorysAsync(Guid? id)
        {
            if (!id.HasValue)
            {
                return new List<FinanceReviewConfigHistorysDto>();
            }
            var queryReviewConfigHistory = await LazyServiceProvider.LazyGetService<IFinanceReviewConfigHistoryRepository>().GetQueryableAsync();
            var queryReviewConfig = await LazyServiceProvider.LazyGetService<IFinanceReviewConfigRepository>().GetQueryableAsync();
            var histors = queryReviewConfigHistory.GroupJoin(queryReviewConfig, a => a.ParentId, b => b.Id, (a, b) => new { ch = a, configs = b })
                .SelectMany(a => a.configs.DefaultIfEmpty(), (a, b) => new { a.ch, config = b })
                .Where(a => a.ch.ParentId == id)
                .OrderByDescending(a=>a.ch.OperateTime)
                .Select(a => new FinanceReviewConfigHistorysDto()
                {
                    FirstReviewUserName = a.config.FirstReviewUserName,
                    SecondReviewUserName = a.ch.SecondReviewUserName,
                    UserName = a.ch.UserName,
                    OperateTime = a.ch.OperateTime
                })
                .ToList();
            return histors;
        }

        /// <summary>
        /// 根据初审人获取复审人
        /// </summary>
        /// <param name="firstReviewUserId"></param>
        /// <returns></returns>
        public async Task<FinanceReviewConfigDto> GetSecondByFirstReviewAsync(Guid firstReviewUserId)
        {
            var queryReviewConfig = await LazyServiceProvider.LazyGetService<IFinanceReviewConfigRepository>().GetQueryableAsync();
            var secondReviewUser = queryReviewConfig.Where(a => a.FirstReviewUserId == firstReviewUserId).FirstOrDefault();
            var result = ObjectMapper.Map<FinanceReviewConfig, FinanceReviewConfigDto>(secondReviewUser);
            return result;
        }
    }
}
