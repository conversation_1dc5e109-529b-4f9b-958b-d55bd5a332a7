

SELECT
 TRY_CONVERT(UNIQUEIDENTIFIER, [Id]) [Id]
,TRY_CONVERT(UNIQUEIDENTIFIER, [ApplicationId]) [ApplicationId]
,[Sex]
,[CardType]
,[CardNo]
,[CardPic]
,[Province]
,[City]
,[Address]
,[PostCode]
,[ExtraProperties]
,'' [ConcurrencyStamp]
,COALESCE(TRY_CONVERT(datetime2, CreationTime), '1999-01-01 00:00:01') [CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CreatorId]) [CreatorId]
,[LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [LastModifierId]) [LastModifierId]
,[IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, [DeleterId]) [DeleterId]
,[DeletionTime]
,[SPName]
,[AffiliationOrgan]
,[Email]
INTO #VendorApplicationPersonals
FROM PLATFORM_ABBOTT.dbo.VendorApplicationPersonals
;
--drop table #VendorApplicationPersonals

select *FROM PLATFORM_ABBOTT.dbo.VendorApplicationPersonals vapt 

USE Speaker_Portal;

UPDATE a 
SET 
 a.[Id] = b.[Id]
,a.[ApplicationId] = b.[ApplicationId]
,a.[Sex] = b.[Sex]
,a.[CardType] = b.[CardType]
,a.[CardNo] = b.[CardNo]
,a.[CardPic] = b.[CardPic]
,a.[Province] = b.[Province]
,a.[City] = b.[City]
,a.[Address] = b.[Address]
,a.[PostCode] = b.[PostCode]
,a.[ExtraProperties] = b.[ExtraProperties]
,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = b.[CreatorId]
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = b.[LastModifierId]
,a.[IsDeleted] = b.[IsDeleted]
,a.[DeleterId] = b.[DeleterId]
,a.[DeletionTime] = b.[DeletionTime]
,a.[SPName] = b.[SPName]
,a.[AffiliationOrgan] = b.[AffiliationOrgan]
,a.[Email] = b.[Email]
FROM dbo.VendorApplicationPersonals a
left join #VendorApplicationPersonals  b
ON a.id=b.id;


INSERT INTO dbo.VendorApplicationPersonals
SELECT
 [Id]
,[ApplicationId]
,[Sex]
,[CardType]
,[CardNo]
,[CardPic]
,[Province]
,[City]
,[Address]
,[PostCode]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[SPName]
,[AffiliationOrgan]
,[Email]
FROM #VendorApplicationPersonals a
WHERE not exists (select * from dbo.VendorApplicationPersonals where id=a.id);

--truncate table dbo.VendorApplicationPersonals

--alter table Speaker_Portal.dbo.VendorApplicationPersonals alter column [CardPic] [nvarchar](max) NULL
--alter table Speaker_Portal.dbo.VendorApplicationPersonals alter column [Province] [nvarchar](50) NULL
--alter table Speaker_Portal.dbo.VendorApplicationPersonals alter column [City] [nvarchar](50) NULL
--alter table Speaker_Portal.dbo.VendorApplicationPersonals alter column [PostCode] [nvarchar](50) NULL
--alter table Speaker_Portal.dbo.VendorApplicationPersonals alter column [SPName] [nvarchar](50) NOT NULL
