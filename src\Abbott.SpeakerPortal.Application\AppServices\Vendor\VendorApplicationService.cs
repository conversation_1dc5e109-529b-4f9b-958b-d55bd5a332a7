﻿using Abbott.SpeakerPortal.Contracts.Vendor.VendorApplication;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.VendorApplicationFinancials;
using Abbott.SpeakerPortal.Entities.VendorApplicationOrgnizations;
using Abbott.SpeakerPortal.Entities.VendorApplicationPersonals;
using Abbott.SpeakerPortal.Entities.VendorApplications;
using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Vendor.Speaker;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.AspNetCore.Mvc.ApplicationModels;
using Microsoft.EntityFrameworkCore;
using Senparc.CO2NET.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;

namespace Abbott.SpeakerPortal.AppServices.Vendor
{
    public class VendorApplicationService : SpeakerPortalAppService, IVendorApplicationService
    {
        /// <summary>
        /// 供应商申请列表
        /// </summary>
        /// <returns></returns>
        public async Task<PagedResultDto<VendorApplicationDto>> GetVendorApplicationListAsync(VendorApplicationRequestDto requestDto)
        {
            var vendorApplicationQuery = (await LazyServiceProvider.LazyGetService<IVendorApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            var vendorApplicationPersonalQuery = (await LazyServiceProvider.LazyGetService<IVendorApplicationPersonalRepository>().GetQueryableAsync()).AsNoTracking();
            var vendorApplicationFinanceQuery = (await LazyServiceProvider.LazyGetService<IVendorApplicationFinancialRepository>().GetQueryableAsync()).AsNoTracking();
            var vendorApplicationOrgQuery = (await LazyServiceProvider.LazyGetService<IVendorApplicationOrganizationRepository>().GetQueryableAsync()).AsNoTracking();

            var vendorQuery = (await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync()).AsNoTracking();

            var query = vendorApplicationQuery
                .GroupJoin(vendorApplicationPersonalQuery, a => a.Id, b => b.ApplicationId, (a, b) => new { va = a, vaps = b })
                .SelectMany(a => a.vaps.DefaultIfEmpty(), (a, b) => new { a.va, vap = b })
                .GroupJoin(vendorApplicationOrgQuery, a => a.va.Id, b => b.ApplicationId, (a, b) => new { a.va,a.vap,vaos=b })
                .SelectMany(a=>a.vaos.DefaultIfEmpty(),(a,b)=>new { a.va, a.vap ,vao = b})
                .Select(a => new 
                { 
                    a.va.Id,
                    a.va.ApplicationCode,
                    a.va.ApplyTime,
                    a.va.ApplicationType,
                    a.va.VendorCode,
                    a.va.VendorType,
                    a.va.ApplyUserName,
                    a.va.ApplyDeptName,
                    a.va.ApplyUserBuName,
                    a.va.Status,
                    a.vap.SPName,
                    a.vao.VendorName,
                });

            query = query.WhereIf(!string.IsNullOrWhiteSpace(requestDto.ApplicationCode), a => a.ApplicationCode.Contains(requestDto.ApplicationCode))
                .WhereIf(!string.IsNullOrWhiteSpace(requestDto.VendorName),a => a.VendorName.Contains(requestDto.VendorName)||a.SPName.Contains(requestDto.VendorName))
                .WhereIf(requestDto.Status.HasValue,a=>a.Status == requestDto.Status)
                .WhereIf(requestDto.ApplyStartTime.HasValue,a => a.ApplyTime >= requestDto.ApplyStartTime.Value.Date)
                .WhereIf(requestDto.ApplyEndTime.HasValue,a=>a.ApplyTime < requestDto.ApplyEndTime.Value.Date.AddDays(1))
                .WhereIf(!string.IsNullOrWhiteSpace(requestDto.VendorCode),a => a.VendorCode.Contains("SP") && a.VendorCode.Contains(requestDto.VendorCode))
                .WhereIf(!string.IsNullOrWhiteSpace(requestDto.BpcsCompanyCode), a => vendorApplicationFinanceQuery.Where(x=>x.ApplicationId == a.Id ).Any(x=>x.Company == requestDto.BpcsCompanyCode))
                .WhereIf(!string.IsNullOrWhiteSpace(requestDto.BpcsVendorCode), a => vendorApplicationFinanceQuery.Where(x => x.ApplicationId == a.Id).Any(x=>x.VendorCode.Contains(requestDto.BpcsVendorCode)));

            int vaCount = query.Count();

            var dataQuery = query.OrderByDescending(a=>a.ApplyTime).Skip(requestDto.PageIndex * requestDto.PageSize).Take(requestDto.PageSize).ToList();

            var data = dataQuery.Select(a => new VendorApplicationDto
            {
                Id = a.Id,
                ApplicationCode = a.ApplicationCode,
                ApplicationType = a.ApplicationType,
                VendorType = a.VendorType,
                ApplyTime = a.ApplyTime == DateTime.MinValue ? null : a.ApplyTime,
                VendorCode = a.VendorCode,
                VendorName = a.VendorName??a.SPName,
                ApplyUserName = a.ApplyUserName,
                //ApplyDeptName = a.ApplyDeptName,
                ApplyDeptName = a.ApplyUserBuName,
                Status = a.Status,
            })
            .ToList();

            var vendorCodes = data.Select(a => a.VendorCode).ToList();
            var vendors = vendorQuery.Where(a=> vendorCodes.Contains(a.VendorCode)).ToList();
            data.ForEach(a => 
            {
                var vendor = vendors.FirstOrDefault(x=>x.VendorCode == a.VendorCode);
                a.VendorId = vendor?.Id;
                a.VendorCode = vendor?.VendorCode;
                a.SPVendorType = vendor?.VendorType;
            });

            var result = new PagedResultDto<VendorApplicationDto>()
            {
                Items = data,
                TotalCount = vaCount,
            };
            return result;
        }

        /// <summary>
        /// 供应商申请删除草稿
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<MessageResult> DeleteDraftAsync(Guid id) 
        {
            var vendorApplicationRepository = LazyServiceProvider.LazyGetService<IVendorApplicationRepository>();
            var vendorApplicationPersonalRepository = LazyServiceProvider.LazyGetService<IVendorApplicationPersonalRepository>();
            var vendorApplicationFinanceRepository = LazyServiceProvider.LazyGetService<IVendorApplicationFinancialRepository>();
            var vendorApplicationOrgRepository = LazyServiceProvider.LazyGetService<IVendorApplicationOrganizationRepository>();

            var vendorApplication = await vendorApplicationRepository.GetAsync(id);
            if (vendorApplication == null || vendorApplication.Status != Statuses.Saved)
            {
                return MessageResult.FailureResult("删除失败,当前状态不允许删除");
            }
            await vendorApplicationRepository.DeleteAsync(id);
            await vendorApplicationPersonalRepository.DeleteAsync(a=>a.ApplicationId==id);
            await vendorApplicationFinanceRepository.DeleteAsync(a => a.ApplicationId == id);
            await vendorApplicationOrgRepository.DeleteAsync(a => a.ApplicationId == id);
            return MessageResult.SuccessResult();
        }
    }
}
