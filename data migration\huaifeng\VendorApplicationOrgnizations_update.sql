SELECT
 TRY_CONVERT(UNIQUEIDENTIFIER, [Id]) [Id]
,TRY_CONVERT(UNIQUEIDENTIFIER, [ApplicationId]) [ApplicationId]
,[VendorName]
,[VendorOldName]
,[VendorEngName]
,CASE WHEN [RegCertificateAddress] IS NULL OR [RegCertificateAddress] = '' THEN 'NULL' ELSE [RegCertificateAddress] END [RegCertificateAddress]
,CASE WHEN [PostCode] IS NULL OR [PostCode] = '' THEN 'NULL' ELSE [PostCode] END [PostCode]
,CASE WHEN [ContactName] IS NULL OR [ContactName] = '' THEN 'NULL' ELSE [ContactName] END [ContactName]
,[ContactPhone]
,[ContactEmail]
,[WebSite]
,GETDATE() [RegisterDate]
,[OrgType]
,[IssuingAuthority]
,[RegisterCode]
,GETDATE() [RegValidityStart]
,GETDATE() [RegValidityEnd]
,CASE WHEN [Province] IS NULL OR [Province] = '' THEN 'NULL' ELSE [Province] END [Province]
,CASE WHEN [City] IS NULL OR [City] = '' THEN 'NULL' ELSE [City] END [City]
,[Legal]
,0 [RegisterAmount]
,[BusinessAuthority]
,[BusinessScope]
,0 [LastYearSales]
,[KeyIndustry]
,[KeyClient]
,0 [Staffs]
,[Aptitudes]
,ISNULL([ApplyReason],0) [ApplyReason]
,[ExtraProperties]
,'NULL' [ConcurrencyStamp]
,[CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CreatorId]) [CreatorId]
,[LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [LastModifierId]) [LastModifierId]
,[IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, [DeleterId]) [DeleterId]
,[DeletionTime]
,[Shareholder]
INTO #VendorApplicationOrgnizations
FROM (SELECT DISTINCT *,ROW_NUMBER() OVER(PARTITION BY Id ORDER BY Id) RK FROM PLATFORM_ABBOTT_STG.dbo.VendorApplicationOrgnizations)a
WHERE RK = 1
;
--drop table #VendorApplicationOrgnizations

USE Speaker_Portal_STG;

UPDATE a 
SET 
 a.[Id] = b.[Id]
,a.[ApplicationId] = b.[ApplicationId]
,a.[VendorName] = b.[VendorName]
,a.[VendorOldName] = b.[VendorOldName]
,a.[VendorEngName] = b.[VendorEngName]
,a.[RegCertificateAddress] = b.[RegCertificateAddress]
,a.[PostCode] = b.[PostCode]
,a.[ContactName] = b.[ContactName]
,a.[ContactPhone] = b.[ContactPhone]
,a.[ContactEmail] = b.[ContactEmail]
,a.[WebSite] = b.[WebSite]
,a.[RegisterDate] = b.[RegisterDate]
,a.[OrgType] = b.[OrgType]
,a.[IssuingAuthority] = b.[IssuingAuthority]
,a.[RegisterCode] = b.[RegisterCode]
,a.[RegValidityStart] = b.[RegValidityStart]
,a.[RegValidityEnd] = b.[RegValidityEnd]
,a.[Province] = b.[Province]
,a.[City] = b.[City]
,a.[Legal] = b.[Legal]
,a.[RegisterAmount] = b.[RegisterAmount]
,a.[BusinessAuthority] = b.[BusinessAuthority]
,a.[BusinessScope] = b.[BusinessScope]
,a.[LastYearSales] = b.[LastYearSales]
,a.[KeyIndustry] = b.[KeyIndustry]
,a.[KeyClient] = b.[KeyClient]
,a.[Staffs] = b.[Staffs]
,a.[Aptitudes] = b.[Aptitudes]
,a.[ApplyReason] = b.[ApplyReason]
,a.[ExtraProperties] = b.[ExtraProperties]
,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = b.[CreatorId]
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = b.[LastModifierId]
,a.[IsDeleted] = b.[IsDeleted]
,a.[DeleterId] = b.[DeleterId]
,a.[DeletionTime] = b.[DeletionTime]
,a.[Shareholder] = b.[Shareholder]
FROM dbo.VendorApplicationOrgnizations a
left join #VendorApplicationOrgnizations  b
ON a.id=b.id;


INSERT INTO dbo.VendorApplicationOrgnizations
SELECT
 [Id]
,[ApplicationId]
,[VendorName]
,[VendorOldName]
,[VendorEngName]
,[RegCertificateAddress]
,[PostCode]
,[ContactName]
,[ContactPhone]
,[ContactEmail]
,[WebSite]
,[RegisterDate]
,[OrgType]
,[IssuingAuthority]
,[RegisterCode]
,[RegValidityStart]
,[RegValidityEnd]
,[Province]
,[City]
,[Legal]
,[RegisterAmount]
,[BusinessAuthority]
,[BusinessScope]
,[LastYearSales]
,[KeyIndustry]
,[KeyClient]
,[Staffs]
,[Aptitudes]
,[ApplyReason]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[Shareholder]
FROM #VendorApplicationOrgnizations a
WHERE not exists (select * from dbo.VendorApplicationOrgnizations where id=a.id);

--truncate table dbo.VendorApplicationOrgnizations

-- alter table Speaker_Portal_STG.dbo.VendorApplicationOrgnizations alter column [RegCertificateAddress] [nvarchar](300) NOT NULL
-- alter table Speaker_Portal_STG.dbo.VendorApplicationOrgnizations alter column [PostCode] [nvarchar](50) NOT NULL
-- alter table Speaker_Portal_STG.dbo.VendorApplicationOrgnizations alter column [ContactName] [nvarchar](50) NOT NULL
-- alter table Speaker_Portal_STG.dbo.VendorApplicationOrgnizations alter column [ContactPhone] [nvarchar](50) NULL
-- alter table Speaker_Portal_STG.dbo.VendorApplicationOrgnizations alter column [OrgType] [nvarchar](100) NULL