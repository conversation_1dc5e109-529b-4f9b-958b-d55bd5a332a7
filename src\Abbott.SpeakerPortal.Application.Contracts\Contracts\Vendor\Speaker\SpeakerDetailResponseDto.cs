﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

using Abbott.SpeakerPortal.Enums;

using Volo.Abp.Application.Dtos;

namespace Abbott.SpeakerPortal.Vendor.Speaker
{
    public class SpeakerDetailResponseDto
    {
        /// <summary>
        /// 讲者ID
        /// </summary>
        public Guid ID { get; set; }
        
        /// <summary>
        /// 是否是 草稿
        /// </summary>
        public bool IsDraft { get; set; }
        /// <summary>
        /// 供应商申请ID
        /// </summary>
        public Guid ApplicationId { get; set; }
        /// <summary>
        /// 草稿版本
        /// </summary>
        public int DraftVersion { get; set; }

        /// <summary>
        /// 是否签署
        /// </summary>
        public bool SignedStatus { get; set; }
        /// <summary>
        /// 是否签署文本
        /// </summary>
        public string SignedStatusString { get; set; }
        
        /// <summary>
        /// 签署版本
        /// </summary>
        public string SignedVersion { get; set; }

        /// <summary>
        /// 状态值
        /// </summary>
        public VendorStatus Status { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public string StatusString { get; set; }
        /// <summary>
        /// 讲者编码
        /// </summary>
        public string VendorCode { get; set; }
        /// <summary>
        /// 供应商类型，来自Vendor.VendorType
        /// </summary>
        public VendorTypes VendorType { get; set; }
        /// <summary>
        /// 讲者名称
        /// </summary>
        public string SPName { get; set; }
        /// <summary>
        /// 职称Id
        /// </summary>
        public Guid? PTId { get; set; }
        /// <summary>
        /// 职称
        /// </summary>
        public string PTIName { get; set; }
        /// <summary>
        /// 所属医院Id
        /// </summary>
        public Guid? HospitalId { get; set; }
        /// <summary>
        /// 所属医院
        /// </summary>
        public string HospitalName { get; set; }
        /// <summary>
        /// EPD医院Code，提交BU是EPD时填写
        /// </summary>
        public string EpdHospitalCode { get; set; }
        public string EpdHospitalName { get; set; }
        /// <summary>
        /// 标准科室Id
        /// </summary>
        public Guid? StandardHosDepId { get; set; }
        /// <summary>
        /// 标准科室
        /// </summary>
        public string StandardHosDepName { get; set; }
        /// <summary>
        /// 院内科室
        /// </summary>
        public string HosDepartment { get; set; }
        /// <summary>
        /// 医生主键
        /// </summary>
        public string EpdId { get; set; }
        /// <summary>
        /// 执业证书编码
        /// </summary>
        public string CertificateCode { get; set; }
        /// <summary>
        /// 讲者级别
        /// </summary>
        public string SPLevel { get; set; }
        /// <summary>
        /// 讲者级别名称
        /// </summary>
        public string SPLevelName { get; set; }

        /// <summary>
        /// 学术级别
        /// </summary>
        public string AcademicLevel { get; set; }
        /// <summary>
        /// 学会任职
        /// </summary>
        public List<AcademicPositionDto> AcademicPositionJson { get; set; }
        /// <summary>
        /// 是否是院士
        /// </summary>
        public bool IsAcademician { get; set; }
        /// <summary>
        /// 原BPM学会任职
        /// </summary>
        public string FormerBPMAcademicPosition { get; set; }
        /// <summary>
        /// 手机号码
        /// </summary>
        public string HandPhone { get; set; }
        /// <summary>
        /// 证件照片
        /// </summary>
        public AttachmentInformation CardPic { get; set; }
        ///<summary>
        /// 证件类型
        ///</summary>
        public string CardType { get; set; }
        /// <summary>
        /// 性别
        /// </summary>
        public Gender? Sex { get; set; }
        /// <summary>
        /// 证件编码
        /// </summary>
        public string CardNo { get; set; }
        /// <summary>
        /// 身份证省份
        /// </summary>
        public string Province { get; set; }
        /// <summary>
        /// 身份证省城市
        /// </summary>
        public string City { get; set; }
        /// <summary>
        /// 身份证省份城市
        /// </summary>
        public string[] ProvinceCity { get; set; }
        /// <summary>
        /// 身份证省份城市
        /// </summary>
        public string ProvinceCityName { get; set; }
        /// <summary>
        /// 身份证住址邮编
        /// </summary>
        public string PostCode { get; set; }
        /// <summary>
        /// 身份证住址
        /// </summary>
        public string Address { get; set; }
        /// <summary>
        /// 银行名称
        /// </summary>
        public string BankCode { get; set; }
        /// <summary>
        /// 银行名称
        /// </summary>
        public string BankName { get; set; }
        /// <summary>
        /// 银行账号
        /// </summary>
        public string BankCardNo { get; set; }
        /// <summary>
        /// 银行所在城市
        /// </summary>
        public string[] BankCity { get; set; }
        /// <summary>
        /// 银行所在城市
        /// </summary>
        public string BankCityName { get; set; }
        /// <summary>
        /// 收款银行联行号
        /// </summary>
        public string BankNo { get; set; }
        /// <summary>
        /// 银行卡正面照片
        /// </summary>
        public AttachmentInformation BankCardImg { get; set; }
        /// <summary>
        /// 银行SwiftCode
        /// </summary>
        public string BankSwiftCode { get; set; }

        /// <summary>
        /// 财务信息
        /// </summary>
        public List<FinancialInformation> FinancialInformation { get; set; }
        /// <summary>
        /// 附件信息
        /// </summary>
        public List<AttachmentInformation> AttachmentInformation { get; set; }
        /// <summary>
        /// DPS信息
        /// </summary>
        public List<AttachmentInformation> DPSCheck { get; set; }
    }

    /// <summary>
    /// 附件信息
    /// </summary>
    public class AttachmentInformation
    {
        /// <summary>
        /// 附件ID
        /// </summary>
        public Guid AttachmentId { get; set; }
        /// <summary>
        /// 文件名称
        /// </summary>
        public string FileName { get; set; }
        /// <summary>
        /// 文件大小
        /// </summary>
        public string FileSize { get; set; }
        /// <summary>
        /// 文件扩展名
        /// </summary>
        public string Suffix { get; set; }
        /// <summary>
        /// 文件路径
        /// </summary>
        public string FilePath { get; set; }
    }

    /// <summary>
    /// 财务信息
    /// </summary>
    public class FinancialInformation
    {
        /// <summary>
        /// ID
        /// </summary>
        public Guid? ID { get; set; }
        /// <summary>
        /// 公司名称
        /// </summary>
        [Required]
        public string Company { get; set; }
        /// <summary>
        /// 公司名称值
        /// </summary>
        public string CompanyName { get; set; }
        /// <summary>
        /// 币种
        /// </summary>
        [Required]
        public string Currency { get; set; }
        /// <summary>
        /// 币种值
        /// </summary>
        public string CurrencyName { get; set; }
        /// <summary>
        /// 供应商编码
        /// </summary>
        public string VendorCode { get; set; }
        /// <summary>
        /// 雅培银行
        /// </summary>
        public string AbbottBank { get; set; }
        /// <summary>
        /// 供应商类别
        /// </summary>
        public string VendorType { get; set; }
        /// <summary>
        /// Division
        /// </summary>
        public string Division { get; set; }
        /// <summary>
        /// 付款类别
        /// </summary>
        public string PayType { get; set; }
        public string PaymentTerm { get; set; }
        public string PaymentTermName { get; set; }
        /// <summary>
        /// DpoCategory编号
        /// </summary>
        public string DpoCategory { get; set; }
        /// <summary>
        /// DpoCategory名称
        /// </summary>
        public string DpoCategoryName { get; set; }
        /// <summary>
        /// SpendingCategory编号
        /// </summary>
        public string SpendingCategory { get; set; }
        /// <summary>
        /// SpendingCategory名称
        /// </summary>
        public string SpendingCategoryName { get; set; }
        /// <summary>
        /// Country Code
        /// </summary>
        public string CountryCode { get; set; }
        /// <summary>
        /// 银行类别
        /// </summary>
        public string BankType { get; set; }
        /// <summary>
        /// 收款银行联行号
        /// </summary>
        public string BankNo { get; set; }
        /// <summary>
        /// 是否可修改(false-可修改/true-不可修改)
        /// </summary>
        public bool Flag { get; set; }

        /// <summary>
        /// 是否可新增
        /// </summary>
        public bool IsAddFlag { get; set; }

        /// <summary>
        /// 当前供应商状态
        /// </summary>
        public FinancialVendorStatus FinancialVendorStatus { get; set; }= FinancialVendorStatus.ToBeEffective;
    }
}
