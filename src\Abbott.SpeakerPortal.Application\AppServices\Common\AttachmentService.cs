﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Abbott.SpeakerPortal.Contracts.Common.Attachment;
using Abbott.SpeakerPortal.Contracts.Common.Blob;
using Abbott.SpeakerPortal.Contracts.Common.PowerApp;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Dataverse.Contracts;
using Abbott.SpeakerPortal.Entities.Budget;
using Abbott.SpeakerPortal.Entities.Common.Attachment;
using Abbott.SpeakerPortal.Enums;

using Azure;
using Azure.Storage.Blobs.Models;

using ClosedXML.Excel;

using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Logging;
using Microsoft.Xrm.Sdk.PluginTelemetry;

namespace Abbott.SpeakerPortal.AppServices.Common
{
    public class AttachmentService : SpeakerPortalAppService, IAttachmentService
    {
        IBlobService _blobService;
        private readonly IWebHostEnvironment _env;
        public AttachmentService(IBlobService blobService, IWebHostEnvironment env)
        {
            _blobService = blobService;
            _env = env;
        }

        /// <summary>
        /// 上传文件
        /// </summary>
        /// <param name="functionModule"></param>
        /// <param name="fileDtos"></param>
        /// <returns></returns>
        public async Task<IEnumerable<UploadFileResponseDto>> UploadFiles(FunctionModule functionModule, IEnumerable<UploadFileDto> fileDtos)
        {
            var list = new List<UploadFileResponseDto>();
            var attachmentRepository = LazyServiceProvider.LazyGetService<IAttachmentRepository>();
            foreach (var item in fileDtos)
            {
                var suffix = Path.GetExtension(item.FileName);
                var fileName = Path.GetFileName(item.FileName);
                //组装存储路径
                var blobName = $"{functionModule}/{DateTimeOffset.Now.Year}/{DateTimeOffset.Now.Month}/{Path.GetFileNameWithoutExtension(item.FileName)}-{DateTimeOffset.Now.Ticks}{suffix}";
                var uploadResult = await _blobService.UploadAsync(blobName, new BinaryData(item.Buffer), true);
                var response = new UploadFileResponseDto { FileName = fileName, FilePath = blobName, Success = !uploadResult.GetRawResponse().IsError };
                var attachment = new Attachment { FunctionModule = functionModule, FileName = fileName, Size = item.Size, Suffix = suffix, FilePath = blobName };
                await attachmentRepository.InsertAsync(attachment);
                response.AttachmentId = attachment.Id;
                response.FileSize = item.Size < 1048576 ? (item.Size / 1024).ToString() + "KB" : (item.Size / 1048576).ToString() + "MB";

                list.Add(response);
            }

            return list;
        }

        /// <summary>
        /// 获取附件列表
        /// </summary>
        /// <param name="attachmentIds"></param>
        /// <returns></returns>
        public async Task<IEnumerable<UploadFileResponseDto>> GetAttachmentsAsync(params Guid[] attachmentIds)
        {
            var queryAttachment = await LazyServiceProvider.LazyGetService<IAttachmentRepository>().GetQueryableAsync();
            var attachments = queryAttachment.Where(a => attachmentIds.Contains(a.Id)).Select(a => new UploadFileResponseDto
            {
                AttachmentId = a.Id,
                FilePath = a.FilePath,
                FileName = a.FileName,
                FileSize = a.Size < 1048576 ? (a.Size / 1024).ToString() + "KB" : (a.Size / 1048576).ToString() + "MB",
            }).ToArray();

            return attachments;
        }

        /// <summary>
        /// 下载文件流
        /// </summary>
        /// <param name="blobName"></param>
        /// <returns></returns>
        public Task<Response<BlobDownloadStreamingResult>> Download(string blobName)
        {
            return _blobService.Download(blobName);
        }

        /// <summary>
        /// 下载文件流
        /// </summary>
        /// <param name="blobName"></param>
        /// <returns></returns>
        public Task<MemoryStream> DownloadStream(string blobName)
        {
            return _blobService.DownloadStream(blobName);
        }

        /// <summary>
        /// 下载拦截记录查询模板
        /// </summary>
        /// <param name="TemplateName"></param>
        /// <returns></returns>
        public async Task<Stream> GetTemplatesFileAsync(TemplateTypes TemplateType, string TemplateName, List<PAFinancialVoucherInfoDto> financialVoucherInfos = null)
        {
            var _logger = LazyServiceProvider.GetService<ILogger<AttachmentService>>();
            try
            {
                var path = Path.Combine(_env.WebRootPath, "Templates");
                DirectoryInfo di = new DirectoryInfo(path);
                var fileInfos = di.GetFiles(TemplateName, SearchOption.AllDirectories);
                if (fileInfos.Length == 0)
                {
                    throw new Exception("未找到文件模版,请联系管理员");
                }
                var fileInfo = fileInfos[0];
                FileStream fs = await Task.Run(() => fileInfo.OpenRead());
                fs.Position = 0;
                switch (TemplateType)
                {
                    case TemplateTypes.SubBudget_Create:
                        return await SetTemplateData(fs);
                    case TemplateTypes.MasterBudget_Create:
                        return await SetMasterBudgetTemplateData(fs);
                    case TemplateTypes.Financial_Voucher:
                        return SetFinancialVoucherTemplate(fs, financialVoucherInfos);
                }
                return fs;
            }
            catch (Exception ex)
            {
                _logger.LogError($"InterceptionService's PreventFileSAsync has an error : {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 填充财务凭证模板
        /// </summary>
        /// <param name="stream"></param>
        /// <param name="financialVoucherInfos"></param>
        /// <returns></returns>
        private Stream SetFinancialVoucherTemplate(Stream stream, List<PAFinancialVoucherInfoDto> financialVoucherInfos)
        {
            if (financialVoucherInfos == null || !financialVoucherInfos.Any())
                return stream;
            //Invoice Line Amount，Company Code ，Division Code，Cost Center，Nature Account，Sub Account，Location
            // 定义起始行号
            int startRow = 7;
            XLWorkbook wbFinancial = new XLWorkbook(stream);
            var sheet1 = wbFinancial.Worksheet(1);
            for (int i = 0; i < financialVoucherInfos.Count; i++)
            {
                sheet1.Cell(startRow + i, 1).Value = financialVoucherInfos[i].InvoiceLineAmount;
                sheet1.Cell(startRow + i, 2).Value = financialVoucherInfos[i].CompanyCode;
                sheet1.Cell(startRow + i, 3).Value = financialVoucherInfos[i].DivisionCode;
                sheet1.Cell(startRow + i, 4).Value = financialVoucherInfos[i].CostCenter;
                sheet1.Cell(startRow + i, 5).Value = financialVoucherInfos[i].NatureAccount;
                sheet1.Cell(startRow + i, 6).Value = financialVoucherInfos[i].SubAccount;
                sheet1.Cell(startRow + i, 7).Value = financialVoucherInfos[i].Location;
            }
            MemoryStream memoryStream = new MemoryStream();
            wbFinancial.SaveAs(memoryStream);
            memoryStream.Position = 0;
            return memoryStream;
        }

        /// <summary>
        /// 子预算新增填充模板
        /// </summary>
        /// <param name="stream"></param>
        /// <returns></returns>
        private async Task<Stream> SetTemplateData(Stream stream)
        {
            var userId = CurrentUser.Id;
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            //var staffDepartment = await dataverseService.GetStaffDepartmentRelations();
            //var userBuId = staffDepartment.Where(m => m.UserId == userId).Select(s => s.DepartmentId).ToList();
            //获取用户授权bu
            var queryAuthorizedBudgetBu = await LazyServiceProvider.LazyGetService<IBdAuthorizedBudgetBuRepository>().GetQueryableAsync();
            var BuIds = queryAuthorizedBudgetBu.Where(s => s.UserId == userId).Select(m => m.BuId).ToList();
            //大区bu
            var orgDistricts = await dataverseService.GetOrgDistrictRelationsAsync();
            //成本中心bu
            var costCenterOrg = await dataverseService.GetCostCenterOrgRelationsAsync();
            //大区
            var district = await dataverseService.GetDistrict();
            var userDistricts = orgDistricts.Where(m => m.OrgId.HasValue && BuIds.Contains(m.OrgId.Value))
                .Join(district, a => a.DistrictId, b => b.Id, (a, b) => new { a.OrgName, b.Name, b.DistrictCode }).ToList();
            //成本中心
            var Costcent = await dataverseService.GetCostcentersAsync();
            var userCostcents = costCenterOrg.Where(m => m.OrgId.HasValue && BuIds.Contains(m.OrgId.Value))
                .Join(Costcent, a => a.CostcenterId, b => b.Id, (a, b) => new { a.OrgName, b.Name, b.CcenterCode, b.Code }).ToList();
            XLWorkbook wb = new XLWorkbook(stream);
            var costcenterSheet = wb.Worksheet(2);
            //大区
            var region = wb.Worksheet(3);
            for (int i = 0; i < userCostcents.Count; i++)
            {
                costcenterSheet.Cell(i + 2, 1).Value = userCostcents[i].OrgName;
                costcenterSheet.Cell(i + 2, 2).Value = userCostcents[i].CcenterCode;
                costcenterSheet.Cell(i + 2, 3).Value = userCostcents[i].Name;
                costcenterSheet.Cell(i + 2, 4).Value = userCostcents[i].Code;
            }
            for (int i = 0; i < userDistricts.Count; i++)
            {
                region.Cell(i + 2, 1).Value = userDistricts[i].OrgName;
                region.Cell(i + 2, 2).Value = userDistricts[i].DistrictCode;
                region.Cell(i + 2, 3).Value = userDistricts[i].Name;
            }
            MemoryStream memoryStream = new MemoryStream();
            wb.SaveAs(memoryStream);
            memoryStream.Position = 0;
            return memoryStream;
        }
        /// <summary>
        /// 主预算预算新增填充模板
        /// </summary>
        /// <param name="stream"></param>
        /// <returns></returns>
        private async Task<Stream> SetMasterBudgetTemplateData(Stream stream)
        {
            var userId = CurrentUser.Id;
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            //获取用户授权bu
            var queryAuthorizedBudgetBu = await LazyServiceProvider.LazyGetService<IBdAuthorizedBudgetBuRepository>().GetQueryableAsync();
            var BuIds = queryAuthorizedBudgetBu.Where(s => s.UserId == userId).Select(m => m.BuId).ToList();
            //bu
            var orgs = (await dataverseService.GetOrganizations()).Where(x => BuIds.Contains(x.Id)).ToList();
            XLWorkbook wb = new XLWorkbook(stream);
            var Sheet2 = wb.Worksheet(2);
            var year = DateTime.Now.Year;
            Sheet2.Cell(1, 1).Value = year;
            Sheet2.Cell(2, 1).Value = year + 1;

            for (int i = 0; i < orgs.Count; i++)
            {
                Sheet2.Cell(i + 1, 2).Value = orgs[i].DepartmentName;
            }
            MemoryStream memoryStream = new MemoryStream();
            wb.SaveAs(memoryStream);
            memoryStream.Position = 0;
            return memoryStream;
        }
    }
}
