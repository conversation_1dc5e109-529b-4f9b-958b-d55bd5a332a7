﻿using Abbott.SpeakerPortal.Contracts.Integration.Veeva;
using Abbott.SpeakerPortal.Dataverse;
using Hangfire;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.BackgroundWorkers.Hangfire;
using Abbott.SpeakerPortal.Extension;
using Abbott.SpeakerPortal.Contracts.Common;

namespace Abbott.SpeakerPortal.BackgroundWorkers.Integration.Veeva
{
    /// <summary>
    /// 年度讲者信息更放，拉取结果
    /// </summary>
    public class AnnualPullSpeakerWorker : SpeakerPortalBackgroundWorkerBase
    {
        private IInteVeevaBatchService _inteVeevaBatchService;        
        private IServiceProvider _serviceProvider;
        private IScheduleJobLogService _jobLogService;
        public AnnualPullSpeakerWorker(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _inteVeevaBatchService = serviceProvider.GetService<IInteVeevaBatchService>();
            _jobLogService = serviceProvider.GetService<IScheduleJobLogService>();
            //触发周期,每天凌晨2点
            CronExpression = Cron.Daily(2);
        }

        public override async Task DoWorkAsync(CancellationToken cancellationToken = default)
        {
            var log = _jobLogService.InitSyncLog("Veeva_AnnualPullSpeaker");
            try
            {
                var (isExe, sftpFilePath) = GetExecuteObj();
                log.Remark = $"job  execute flag:{isExe},SftpFilePath:{sftpFilePath}.from pp.";
                if (isExe)
                {
                    //逻辑处理
                    log.RecordCount = await _inteVeevaBatchService.PullAnnualSpeackerInfo(sftpFilePath);
                }
            }
            catch (Exception ex)
            {
                log.IsSuccess = false;
                log.Remark += ex.ToString();
            }
            finally
            {
                _jobLogService.SyncLog(log);
            }

        }

        /// <summary>
        /// 获取执行信息
        /// </summary>
        /// <returns></returns>
        private (bool,string) GetExecuteObj()
        {
            var dataverseService = _serviceProvider.GetService<IDataverseService>();
            var configList = dataverseService.GetAllSystemInterationConfig().GetAwaiterResult();
            var flag = configList.Where(c => c.Name == "Job_Veeva_AnnualSpeakerPull_Flag").FirstOrDefault();
            var sftpFilePath = configList.Where(c => c.Name == "Job_Veeva_AnnualSpeakerPull_SftpFilePath").FirstOrDefault();
            if (flag == null || flag.Value != "1")
            {
                return (false, sftpFilePath?.Value);
            }
            else
            {
                return (true, sftpFilePath?.Value);
            }            
        }
    }
}
