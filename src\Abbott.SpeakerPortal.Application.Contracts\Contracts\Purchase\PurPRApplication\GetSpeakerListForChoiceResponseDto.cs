﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Text.Json.Serialization;

using Abbott.SpeakerPortal.Enums;

namespace Abbott.SpeakerPortal.Contracts.Purchase.PurPRApplication
{
    public class GetSpeakerListForChoiceResponseDto
    {
        public Guid Id { get; set; }

        public Guid VendorId { get; set; }

        /// <summary>
        /// 讲者姓名
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 讲者编号
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// Payment Terms
        /// </summary>
        public string PaymentTerms { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// 讲者评级
        /// </summary>
        public string Level { get; set; }

        /// <summary>
        /// 讲者评级Code
        /// </summary>
        public string LevelCode { get; set; }

        /// <summary>
        /// 所属医院
        /// </summary>
        public string Hospital { get; set; }

        /// <summary>
        /// 标准科室
        /// </summary>
        public string Department { get; set; }

        /// <summary>
        /// EPD医生主键
        /// </summary>
        public string EpdDoctorKey { get; set; }

        /// <summary>
        /// 签署状态
        /// </summary>
        public bool ConsentStatus { get; set; }

        /// <summary>
        /// 本年度剩余可讲课次数
        /// </summary>
        public int YearlyCount { get; set; }

        /// <summary>
        /// 本年度剩余金额
        /// </summary>
        public decimal YearlyAmount { get; set; }

        /// <summary>
        /// 是否需要校验讲者次数和金额
        /// </summary>
        public bool NeedCheckLimit { get; set; }

        /// <summary>
        /// 手机号
        /// </summary>
        public string Phone { get; set; }

        /// <summary>
        /// 执业证书编号
        /// </summary>
        public string CertificateNo { get; set; }

        /// <summary>
        /// 院内科室
        /// </summary>
        public string HosDepartment { get; set; }

        /// <summary>
        /// 职称
        /// </summary>
        public string JobTitle { get; set; }

        [JsonIgnore]
        public Guid? UserId { get; set; }

        /// <summary>
        /// 银行卡名称
        /// </summary>
        public string BankName { get; set; }

        /// <summary>
        /// 银行卡账号
        /// </summary>
        public string BankAccount { get; set; }

        /// <summary>
        /// 公司编码
        /// </summary>
        public string CompanyCode { get; set; }
    }
}
