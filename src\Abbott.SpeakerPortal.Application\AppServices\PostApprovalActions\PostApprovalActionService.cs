﻿using Abbott.SpeakerPortal.Contracts.PostApprovalActions;
using Abbott.SpeakerPortal.Contracts.STicket;
using Abbott.SpeakerPortal.Entities;
using Volo.Abp.ObjectMapping;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Abbott.SpeakerPortal.Entities.PostApprovalActions;
using Abbott.SpeakerPortal.Enums;
using Microsoft.Extensions.Configuration;
using Abbott.SpeakerPortal.Contracts.Common;
using Microsoft.Extensions.DependencyInjection;
using Abbott.SpeakerPortal.Extension;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using static Abbott.SpeakerPortal.Permissions.SpeakerPortalPermissions;
using System.Text.Json;
using Abbott.SpeakerPortal.Entities.EFlow.STicket;
using Microsoft.EntityFrameworkCore;
using Abbott.SpeakerPortal.AppServices.Common;
using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.ReturnAndExchange;
using Abbott.SpeakerPortal.Contracts.Common.BPMEmail;
using Hangfire;
using Abbott.SpeakerPortal.BackgroundWorkers.BPMEmail;

namespace Abbott.SpeakerPortal.AppServices.PostApprovalActions
{
    public class PostApprovalActionService : SpeakerPortalAppService, IPostApprovalActionService
    {
        private readonly IConfiguration _configuration;
        private readonly ISTicketService _sticketService;
        private readonly IHttpRequestService _httpRequestService;
        public PostApprovalActionService(IConfiguration configuration, ISTicketService sticketService, IHttpRequestService httpRequest)
        {
            _configuration = configuration;
            _sticketService = sticketService;
            _httpRequestService = httpRequest;
        }
        private string appkey { get; set; }
        private long timestamp { get; set; }
        private string Sign { get; set; }

        private string SignReturn { get; set; }
        private string token { get; set; }

        const string epo = "ReceiverEPOData";

        const string gr = "ReciveGRData";
        /// <summary>
        /// Creates the action asynchronous.
        /// </summary>
        /// <param name="create">The create.</param>
        public async Task CreateActionAsync(CreatePostApprovalActionDto create)
        {
            var PostApprovalActionRepository = LazyServiceProvider.LazyGetService<IPostApprovalActionRepository>();
            var result = ObjectMapper.Map<CreatePostApprovalActionDto, PostApprovalAction>(create);
            await PostApprovalActionRepository.InsertAsync(result);
        }
        /// <summary>
        /// Integrations the asynchronous.
        /// </summary>
        public async Task IntegrationAsync()
        {
            var postApprovalActionRepository = LazyServiceProvider.LazyGetService<IPostApprovalActionRepository>();
            var postApprovalActionquey = await postApprovalActionRepository.GetQueryableAsync();

            var queryEnumerable = postApprovalActionquey.Where(a => a.Count < a.MaxRetries && a.Status != InitApprovalRecordStatus.Succeeded && a.ProcessingType == ProcessingTypes.ThirdIntegration).AsEnumerable();
            if (!queryEnumerable.Any()) return;
            appkey = _configuration["Integrations:SOI:AppKey"];
            timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            token = await _httpRequestService.GetSOITokenAsync();

            await SOIPushAsync(queryEnumerable.Where(m => m.WfTypeOption == WorkflowTypeName.STicketRequest));//核销
            await SOIPushReturnAsync(queryEnumerable.Where(m => m.WfTypeOption == WorkflowTypeName.ReturnAndExchangeRequest));//退货

            await postApprovalActionRepository.UpdateManyAsync(queryEnumerable);

        }

        private async Task SOIPushAsync(IEnumerable<PostApprovalAction> queryEnumerable)
        {
            if (!queryEnumerable.Any()) return;
            var Ids = queryEnumerable.Select(a => a.FormId).ToArray();
            var sTiketRepository = LazyServiceProvider.GetService<ISTicketApplicationRepository>();
            var sTiketQuery = await sTiketRepository.GetQueryableAsync();

            var sTikets = await sTiketQuery.Where(a => Ids.Contains(a.Id)).ToListAsync();

            var encryptText = $"appkey={appkey}method={epo}timestamp={timestamp}";
            Sign = encryptText.EncryptSHA3();
            List<Task> tasks = [];
            foreach (var item in queryEnumerable)
            {
                var sTiket = sTikets.First(s => s.Id == item.FormId);
                tasks.Add(CallSOIApiAsync(item, sTiket));
            }
            await Task.WhenAll(tasks);
            await sTiketRepository.UpdateManyAsync(sTikets);
        }
        private async Task SOIPushReturnAsync(IEnumerable<PostApprovalAction> queryEnumerable)
        {
            if (!queryEnumerable.Any()) return;
            var encryptText = $"appkey={appkey}method={gr}timestamp={timestamp}";
            SignReturn = encryptText.EncryptSHA3();
            List<Task> tasks = [];
            foreach (var item in queryEnumerable)
            {
                tasks.Add(CallSOIReturnApiAsync(item));
            }
            await Task.WhenAll(tasks);
        }
        /// <summary>
        /// Calls the API asynchronous.
        /// </summary>
        /// <param name="postApproval">The post approval.</param>
        private async Task CallSOIApiAsync(PostApprovalAction postApproval, STicketApplication sTicket)
        {
            try
            {
                postApproval.Count += 1;
                var requestData = new
                {
                    appkey = appkey,
                    accessToken = token,
                    timestamp = timestamp,
                    method = epo,
                    Sign = Sign,
                    data = JsonSerializer.Deserialize<STicketPushSOIRequestDto>(postApproval.RequestContent),
                };
                var json = JsonSerializer.Serialize(requestData);
                var result = await _httpRequestService.HttpPostAsync<MessageResult>(json, $"{_configuration["Integrations:SOI:BaseUrl"]}/SOIWebAPI/api/EPO/ReceiverEPOData", formNo: requestData?.data?.PoFormNo);
                if (!result.Success)
                {
                    postApproval.Status = InitApprovalRecordStatus.Failed;
                }
                else
                {
                    postApproval.Status = InitApprovalRecordStatus.Succeeded;
                    sTicket.Status = STicketStatus.SOIInProcess;

                    //推送状态到CSS
                    if (sTicket.ApplicationType == STicketDataSourceConst.CSS)
                    {
                        PushSTicketApprovalResultDto approvalDto = new PushSTicketApprovalResultDto
                        {
                            CSSNumber = sTicket.CssCode,
                            SerialNumber = sTicket.ApplicationCode,
                            ApprovalResult = "2",
                            ApprovedName = string.Empty,
                            ApprovedTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                            ApprovedRemark = string.Empty,
                        };
                        await _sticketService.STicketApprovalResultPushAsync(approvalDto);
                    }
                }
                postApproval.ResponseContent = result.Message;
            }
            catch (Exception e)
            {
                postApproval.Status = InitApprovalRecordStatus.Failed;
                postApproval.ResponseContent = e.Message;
            }
            finally
            {
                //发生错误时，发送邮件给公邮
                if (postApproval.Status == InitApprovalRecordStatus.Failed)
                    await SendNotifyMailWhenError(postApproval.FormNo, postApproval.ResponseContent);
            }
        }
        /// <summary>
        /// Calls the API asynchronous.
        /// </summary>
        /// <param name="postApproval">The post approval.</param>
        private async Task CallSOIReturnApiAsync(PostApprovalAction postApproval)
        {
            try
            {
                postApproval.Count += 1;
                var requestData = new
                {
                    appkey = appkey,
                    accessToken = token,
                    timestamp = timestamp,
                    method = gr,
                    Sign = SignReturn,
                    data = (JsonSerializer.Deserialize<PushSOIReturnResponseDto>(postApproval.RequestContent)),
                };
                var json = JsonSerializer.Serialize(requestData);
                var result = await _httpRequestService.HttpPostAsync<MessageResult>(json, $"{_configuration["Integrations:SOI:BaseUrl"]}/SOIWebAPI/api/GR/ReciveGRData", formNo: requestData?.data?.GrFormNo);
                if (!result.Success)
                {
                    postApproval.Status = InitApprovalRecordStatus.Failed;
                }
                else
                {
                    postApproval.Status = InitApprovalRecordStatus.Succeeded;

                }
                postApproval.ResponseContent = result.Message;
            }
            catch (Exception e)
            {
                postApproval.Status = InitApprovalRecordStatus.Failed;
                postApproval.ResponseContent = e.Message;
            }
            finally
            {
                //发生错误时，发送邮件给公邮
                if (postApproval.Status == InitApprovalRecordStatus.Failed)
                    await SendNotifyMailWhenError(postApproval.FormNo, postApproval.ResponseContent);
            }
        }

        /// <summary>
        /// 发生错误时，发送邮件给公邮
        /// </summary>
        /// <param name="formNo"></param>
        /// <param name="errorMessage"></param>
        /// <returns></returns>
        async Task SendNotifyMailWhenError(string formNo, string errorMessage)
        {
            string helpdeskEmail = _configuration["SpeakerEmail:BPMHelpdeskEmail"];
            var sendEmaillRecord = new InsertSendEmaillRecordDto
            {
                EmailAddress = helpdeskEmail,
                Subject = "[NexBPM消息中心]推送SOI 执行失败提醒。",
                Content = $"{formNo}单据在推送SOI时失败，错误信息如下：{errorMessage}。请联系系统管理员排查问题。",
                SourceType = EmailSourceType.MonitorSyncLog,
                Status = SendStatus.Pending,
                Attempts = 0
            };
            //记录邮件，并触发邮件发送功能
            var emailService = LazyServiceProvider.LazyGetService<IEmailService>();
            await emailService.InsertSendEmaillRecordAsync([sendEmaillRecord]);
            // 调度作业
            BackgroundJob.Enqueue<SendEmailWorker>(a => a.DoWorkAsync());
        }
    }
}
