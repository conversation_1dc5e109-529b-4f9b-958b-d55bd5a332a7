CREATE PROCEDURE dbo.sp_STicketApplicationDetail
AS 
BEGIN

select 
newid() AS Id,--【明细行id】自动生成的uuid
a.ProcInstId ,
No AS RowId,--【明细行行号】
case when b.RE_ID='NULL' or b.RE_ID='' then null else b.RE_ID end  AS HedgeDetailId,--【反冲行号】正向行的金额为正，反冲行金额为负；正向行的id为反冲行的RE_ID，反冲行的id为正向行的RE_ID
a.serialNumber AS ParentID,--【核销主单编号】
null  AS SubVerificationStatus,--【核销状态】
null  AS CutOffStatus,
null  AS SubStatus,
null as SettlementEntityId,
tr.Res_Data  as SettlementEntityCode,
Store as SettlementEntityName,
tr.Res_Data1 as SettlementEntityType,
null as SettlementEntityChannel,
null as SettlementEntityHQCode,
null as SettlementEntityHQName,
b.ExpensePropertyCode AS ExpenseNatureId,--费用性质ID
--1.需要以核销单ExpenseProperty，作为[spk_BPMCode]查询[dbo].[spk_costnature]表，得到对应的NexBPM的ID[spk_NexBPMCode]"
b.ExpensePropertyCode AS ExpenseNatureCode,--费用性质Code，
--需要以核销单ExpenseProperty，作为[spk_BPMCode]查询[dbo].[spk_costnature]表，得到对应的NexBPM的Code[spk_costnumber]"
b.ExpenseProperty AS ExpenseNature,--费用性质
b.Number AS Quantity,--数量
b.Price AS Price,--单价（含税）
b.RMB AS SubTotalAmountRMB,--总金额
d.QTY AS SubVerifiedAmountRMB,--已核销金额
--SettlementRegion,--结算区域，kangyang,CSS结算周期：2023-10-01~2023-11-30,2023-12-01~2023-12-31,2024-01-01~2024-01-31。取,前的“kangyang”
--SettlementPeriodStart,--折扣结算开始日期，取最早的结算开始
--SettlementPeriodEnd,--折扣结算结束日期，取最晚的结算结束
--b.CityCode AS CityId,--城市id，需要以CityCode作为[spk_citynumber]查询[spk_citymasterdata]，得到[spk_citymasterdataId]
--b.CityCode AS CityCode,--城市代码
--b.City AS CityName,--城市
b.PredictDate AS PredictDate,--预计日期
b.Content AS Remark,--备注
NULl AS CreationTime,--默认为空
NULl AS CreatorId,--默认为空
NULl AS LastModificationTime,--默认为空
NULl AS LastModifierId,--默认为空
0 AS IsDeleted,--默认为0(BPM的删除功能是hard delete，已删除的不会有记录)
NULl AS DeleterId,--默认为空
NULl AS DeletionTime--默认为空
into #STicketApplicationDetail_tmp
from ods_AUTO_BIZ_T_WholesalerVerificationApplication_Info a 
left join ods_AUTO_BIZ_T_WholesalerVerificationApplication_Info_childInfo b
on a.ProcInstId=b.ProcInstId
left join ODS_T_RESOURCE tr
on b.StoreId=tr.Res_Code 
left join V_FeeApply_EPORlt_ForBPM d
on a.serialNumber=d.pono and b.No=d.LineNumber
--left join ods_T_Pur_MDM_StoreInfo tps
--on tr.Res_Data =tps.V_code
--left join ods_AUTO_BIZ_T_WholesalerVerificationApplication_Info_childInfo b1
--on a.ProcInstId=b1.ProcInstId and b.no=b1.RE_ID
--left join View_WholesalerVerificationApplication_Info c
--on a.ProcInstId=c.ProcInstId and b.no=c.no

--drop table STicketApplicationDetail_tmp

 IF OBJECT_ID(N'dbo.STicketApplicationDetail_tmp', N'U') IS NOT NULL
	BEGIN
		select 1
--		update a 
--		set 
--		a.RowId                         =b.RowId
--		,a.HedgeDetailId                 =b.HedgeDetailId
--		,a.ParentID                      =b.ParentID
--		,a.SubVerificationStatus         =b.SubVerificationStatus
--		,a.StoreId                       =b.StoreId
--		,a.StoreName                     =b.StoreName
--		,a.StoreChannel                  =b.StoreChannel
--		,a.StoreSubChannel   =b.StoreSubChannel
--		,a.ExpenseNatureId               =b.ExpenseNatureId
--		,a.ExpenseNatureCode             =b.ExpenseNatureCode
--		,a.ExpenseNature                 =b.ExpenseNature
--		,a.Quantity                      =b.Quantity
--		,a.Price                         =b.Price
--		,a.SubTotalAmountRMB             =b.SubTotalAmountRMB
--		,a.SubVerifiedAmountRMB          =b.SubVerifiedAmountRMB
--		,a.SettlementRegion              =b.SettlementRegion
--		,a.SettlementPeriodStart         =b.SettlementPeriodStart
--		,a.SettlementPeriodEnd           =b.SettlementPeriodEnd
--		,a.CityId                        =b.CityId
--		,a.CityCode                      =b.CityCode
--		,a.CityName                      =b.CityName
--		,a.PredictDate                   =b.PredictDate
--		,a.Remark                        =b.Remark
--		,a.CreationTime                  =b.CreationTime
--		,a.CreatorId                     =b.CreatorId
--		,a.LastModificationTime          =b.LastModificationTime
--		,a.LastModifierId                =b.LastModifierId
--		,a.IsDeleted                     =b.IsDeleted
--		,a.DeleterId                     =b.DeleterId
--		,a.DeletionTime                  =b.DeletionTime
--       from dbo.STicketApplicationDetail_tmp a 
--       left join #STicketApplicationDetail_tmp b on a.ProcInstId = b.ProcInstId and a.RowId=b.RowId
--       
--       insert into dbo.STicketApplicationDetail_tmp 
--       select 
--	    Id
--		,ProcInstId
--		,RowId
--		,HedgeDetailId
--		,ParentID
--		,SubVerificationStatus
--		,StoreId
--		,StoreName
--		,StoreChannel
--		,StoreSubChannel
--		,ExpenseNatureId
--		,ExpenseNatureCode
--		,ExpenseNature
--		,Quantity
--		,Price
--		,SubTotalAmountRMB
--		,SubVerifiedAmountRMB
--		,SettlementRegion
--		,SettlementPeriodStart
--		,SettlementPeriodEnd
--		,CityId
--		,CityCode
--		,CityName
--		,PredictDate
--		,Remark
--		,CreationTime
--		,CreatorId
--		,LastModificationTime
--		,LastModifierId
--		,IsDeleted
--		,DeleterId
--		,DeletionTime
--    from #STicketApplicationDetail_tmp a
--    where not exists (select * from dbo.STicketApplicationDetail_tmp b where a.ProcInstId = b.ProcInstId and a.RowId=b.RowId)
--    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into dbo.STicketApplicationDetail_tmp from #STicketApplicationDetail_tmp
    -- select * from #vendor_tbl
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END


END
