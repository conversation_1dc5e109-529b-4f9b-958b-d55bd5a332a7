select newid() as spk_NexBPMCode,* into #spk_organizational_costcenter from (
select 
oct.spk_organizational as spk_BPMCode,
ot.spk_Name as spk_organizational,
pt.spk_name as spk_costcenter,
oct.flg 
from spk_organizational_costcenter_Tmp oct
join spk_organizationalmasterdata_tmp ot
on oct.spk_organizational=ot.spk_BPMCode     --这里数据量减少
join spk_costcentermasterdata_Tmp pt
on oct.spk_costcenter=pt.spk_BPMCode)A

IF OBJECT_ID(N'dbo.spk_organizational_costcenter', N'U') IS NOT NULL
BEGIN
	update a 
	set a.spk_BPMCode         = b.spk_BPMCode
       ,a.spk_organizational  = b.spk_organizational
       ,a.spk_costcenter      = b.spk_costcenter
       ,a.flg                 = b.flg
    from dbo.spk_organizational_costcenter a
    join #spk_organizational_costcenter b on a.spk_BPMCode = b.spk_BPMCode
    
    insert into dbo.spk_organizational_costcenter
    select a.spk_NexBPMCode
          ,a.spk_BPMCode
          ,a.spk_organizational
          ,a.spk_costcenter
          ,a.flg
	from #spk_organizational_costcenter a
	where NOT EXISTS (SELECT * FROM dbo.spk_organizational_costcenter where spk_BPMCode = a.spk_BPMCode)
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into dbo.spk_organizational_costcenter from #spk_organizational_costcenter
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
