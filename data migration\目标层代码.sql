--AbpUsers
SELECT 
ID
,TenantId
,UserName
,UPPER(NormalizedUserName) NormalizedUserName
,Name
,Surname
,Email
,NormalizedEmail
,EmailConfirmed
,PasswordHash
,SecurityStamp
,IsExternal
,PhoneNumber
,PhoneNumberConfirmed
,IsActive
,TwoFactorEnabled
,LockoutEnd
,LockoutEnabled
,AccessFailedCount
,ExtraProperties
,ConcurrencyStamp
,CreationTime
,CreatorId
,LastModificationTime
,LastModifierId
,IsDeleted
,DeleterId
,DeletionTime
,OpenId
,StaffCode
,DepartmentId
,case when JobStatus=N'离职' then 0 else 1 end JobStatus
,MainDepartmentId
,LastLoginTime
,UnionId
,EntityVersion
,LastPasswordChangeTime
,ShouldChangePasswordOnNextLogin
,SignedVersion
into #AbpUsers
FROM PLATFORM_ABBOTT.dbo.AbpUsers
use Speaker_Portal;
--update更新
update a 
set 
a.TenantId= TRY_CONVERT(UNIQUEIDENTIFIER,b.TenantId)
,a.UserName= b.UserName
,a.NormalizedUserName= b.NormalizedUserName
,a.Name= b.Name
,a.Surname= b.Surname
,a.Email= b.Email
,a.NormalizedEmail= b.NormalizedEmail
,a.EmailConfirmed= b.EmailConfirmed
,a.PasswordHash= b.PasswordHash
,a.SecurityStamp= b.SecurityStamp
,a.IsExternal= b.IsExternal
,a.PhoneNumber= b.PhoneNumber
,a.PhoneNumberConfirmed= b.PhoneNumberConfirmed
,a.IsActive= b.IsActive
,a.TwoFactorEnabled= b.TwoFactorEnabled
,a.LockoutEnd= b.LockoutEnd
,a.LockoutEnabled= b.LockoutEnabled
,a.AccessFailedCount= b.AccessFailedCount
,a.ExtraProperties= b.ExtraProperties
,a.ConcurrencyStamp= b.ConcurrencyStamp
,a.CreationTime= b.CreationTime
,a.CreatorId= TRY_CONVERT(UNIQUEIDENTIFIER,b.CreatorId)
,a.LastModificationTime= b.LastModificationTime
,a.LastModifierId= TRY_CONVERT(UNIQUEIDENTIFIER,b.LastModifierId)
,a.IsDeleted= b.IsDeleted
,a.DeleterId= TRY_CONVERT(UNIQUEIDENTIFIER,b.DeleterId)
,a.DeletionTime= b.DeletionTime
,a.OpenId= b.OpenId
,a.StaffCode= b.StaffCode
,a.DepartmentId= b.DepartmentId
,a.JobStatus= b.JobStatus
,a.MainDepartmentId= TRY_CONVERT(UNIQUEIDENTIFIER,b.MainDepartmentId)
,a.LastLoginTime= b.LastLoginTime
,a.UnionId= b.UnionId
,a.EntityVersion= b.EntityVersion
,a.LastPasswordChangeTime= b.LastPasswordChangeTime
,a.ShouldChangePasswordOnNextLogin= b.ShouldChangePasswordOnNextLogin
,a.SignedVersion= b.SignedVersion
from dbo.AbpUsers a
left join #AbpUsers  b
on a.id=b.id;
--insert增量
insert dbo.AbpUsers
select 
TRY_CONVERT(UNIQUEIDENTIFIER, ID)ID
,TRY_CONVERT(UNIQUEIDENTIFIER, TenantId)TenantId
,UserName
,NormalizedUserName
,Name
,Surname
,Email
,NormalizedEmail
,EmailConfirmed
,PasswordHash
,SecurityStamp
,IsExternal
,PhoneNumber
,PhoneNumberConfirmed
,IsActive
,TwoFactorEnabled
,LockoutEnd
,LockoutEnabled
,AccessFailedCount
,ExtraProperties
,ConcurrencyStamp
,CreationTime
,TRY_CONVERT(UNIQUEIDENTIFIER, CreatorId)CreatorId
,LastModificationTime
,TRY_CONVERT(UNIQUEIDENTIFIER, LastModifierId)LastModifierId
,IsDeleted
,TRY_CONVERT(UNIQUEIDENTIFIER, DeleterId)DeleterId
,DeletionTime
,OpenId
,StaffCode
,DepartmentId
,JobStatus
,TRY_CONVERT(UNIQUEIDENTIFIER,MainDepartmentId )MainDepartmentId
,LastLoginTime
,UnionId
,EntityVersion
,LastPasswordChangeTime
,ShouldChangePasswordOnNextLogin
,SignedVersion
from #AbpUsers a
where not exists (select * from dbo.AbpUsers where id=a.id);


--[AbpUserRoles]
use PLATFORM_ABBOTT;
select 
DISTINCT 
[UserId],
[RoleId],
[TenantId]
into #AbpUserRoles
from dbo.AbpUserRoles
where RoleId <> null or RoleId <> '';
use Speaker_Portal;
--update更新
update a 
set 
a.RoleId= TRY_CONVERT(UNIQUEIDENTIFIER,b.RoleId)
,a.TenantId= TRY_CONVERT(UNIQUEIDENTIFIER,b.TenantId)
from AbpUserRoles a
left join dbo.AbpRoles c
on a.RoleId=c.Name COLLATE SQL_Latin1_General_CP1_CI_AS
left join #AbpUserRoles  b
on a.UserId=b.UserId
;
--insert增量
insert dbo.AbpUserRoles
select 
TRY_CONVERT(UNIQUEIDENTIFIER, a.UserId)UserId,
b.id as RoleId,
TRY_CONVERT(UNIQUEIDENTIFIER,a.TenantId)TenantId
from #AbpUserRoles a
left join dbo.AbpRoles b
on a.RoleId=b.Name COLLATE SQL_Latin1_General_CP1_CI_AS
where not exists (select * from dbo.AbpUsers c where UserId=a.UserId)
and a.UserId is not null ;
;



