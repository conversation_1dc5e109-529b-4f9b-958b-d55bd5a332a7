﻿using Abbott.SpeakerPortal.Contracts.Purchase.PurPOApplication;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPRApplication;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static Abbott.SpeakerPortal.Enums.Purchase;
using Volo.Abp.Application.Dtos;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using Abbott.SpeakerPortal.Entities.VendorOrgnizations;
using System.IO;
using MiniExcelLibs;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.Purchase.PurPOApplication;
using Abbott.SpeakerPortal.Contracts.Common;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp;
using Abbott.SpeakerPortal.AppServices.Common;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Internal;
using Volo.Abp.ObjectMapping;
using Abbott.SpeakerPortal.Entities.Common.Attachment;
using Abbott.SpeakerPortal.Contracts.Common.Attachment;
using System.ComponentModel;
using DocumentFormat.OpenXml.Wordprocessing;
using DocumentFormat.OpenXml.Office2010.Excel;
using Microsoft.AspNetCore.Mvc.ApplicationModels;
using Senparc.Weixin.WxOpen.AdvancedAPIs.WxApp.WxAppJson;
using Abbott.SpeakerPortal.Vendor;
using Abbott.SpeakerPortal.Entities.Vendors;
using System.Linq.Dynamic.Core;
using AutoMapper.Internal.Mappers;
using Microsoft.Xrm.Sdk.Messages;
using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Common.Approve;
using Abbott.SpeakerPortal.Contracts.Common.PowerApp;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Utils;
using Abbott.SpeakerPortal.Vendor.Speaker;
using Volo.Abp.Authorization;
using Volo.Abp.Validation;
using Abbott.SpeakerPortal.Entities.VendorPersonals;
using Microsoft.IdentityModel.Tokens;
using System.ComponentModel.DataAnnotations;
using DocumentFormat.OpenXml.Vml.Spreadsheet;
using Abbott.SpeakerPortal.Purchase.PurExemptApplication;
using Senparc.CO2NET.Extensions;
using Microsoft.Extensions.DependencyInjection;
using Org.BouncyCastle.Crypto;
using Abbott.SpeakerPortal.Contracts.Common.Common;
using Abbott.SpeakerPortal.Entities.VendorApplicationFinancials;
using Abbott.SpeakerPortal.Entities.VendorApplications;
using Abbott.SpeakerPortal.Entities.Purchase.PurGRApplication;
using Abbott.SpeakerPortal.Contracts.Purchase.PurGRApplication;
using Abbott.SpeakerPortal.Contracts.Budget.Subbudget;
using Abbott.SpeakerPortal.Entities.Purchase.PurPAApplication;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Abbott.SpeakerPortal.AppServices.Purchase
{
    public partial class PurPOApplicationService : SpeakerPortalAppService, IPurPOApplicationService
    {
        /// <summary>
        /// 发起收货操作
        /// </summary>
        /// <param name="pOCreateRequest"></param>
        /// <returns></returns>
        public async Task<MessageResult> InitiateReceiptAsync(Guid request, string remark = "")
        {
            var GRApplication = LazyServiceProvider.LazyGetService<IPurGRApplicationService>();
            var approveService = LazyServiceProvider.LazyGetService<IApproveService>();
            //查询PO
            var poRepository = LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>();
            var queryPO = poRepository.GetQueryableAsync().Result.FirstOrDefault(f=>f.Id == request && f.Status == PurOrderStatus.InitiateReceipt);
            if (queryPO == null)
                return MessageResult.FailureResult("提交失败，未找到相关PO数据");
            //其中某一条PR的ID
            if (string.IsNullOrEmpty(queryPO.PRApplicationDetailId))
                return MessageResult.FailureResult("提交失败，未找到相关PR明细数据");
            var prDetailIds = queryPO.PRApplicationDetailId.Split(",").Select(Guid.Parse).ToList(); 

            //创建收货流程
            var result = await GRApplication.CreateGRApplication(prDetailIds, new PurPOApplicationDto { Id = queryPO.Id,ApplicationCode = queryPO.ApplicationCode,VendorId = queryPO.VendorId});
            if (!result.Success) return result;
            await approveService.AddApprovalRecordAsync(new AddApprovalRecordDto()
            {
                FormId = queryPO.Id,
                ApprovalId = queryPO.ApplyUserId,
                Status = ApprovalOperation.InitiateReceipt,
                Remark = remark,
                ApprovalTime = DateTime.Now,
                WorkStep = "发起收货",
                Name = "发起收货"
            });//创建发起收货审批历史记录

            //发起收货成功后修改PO状态
            queryPO.Status = PurOrderStatus.Closed;
            var success = await poRepository.UpdateAsync(queryPO);
            await UpdatePRDetailAsync(queryPO);//更新PR明细
            await POBudgetRefundAsync(queryPO);//预算返还
            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// PO发起收货后预算返还
        /// PO(只有返还): 审批通过后发起收货时，退回每行[PR申请-PO不含税金额](这里最好记录一下税金退回多少、不含税金退回多少)
        /// </summary>
        /// <param name="paId"></param>
        /// <returns></returns>
        private async Task POBudgetRefundAsync(PurPOApplication po)
        {
            var poDetailQuery = await LazyServiceProvider.GetService<IPurPOApplicationDetailsRepository>().GetQueryableAsync();
            var prQuery = await LazyServiceProvider.GetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var prDetailQuery = await LazyServiceProvider.GetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var pr = prQuery.Where(a=>a.Id == po.PRId).FirstOrDefault();
            var prDetailIds = po.PRApplicationDetailId.Split(',').Select(Guid.Parse).ToList();
            var poDetail = poDetailQuery.Where(a=>a.POApplicationId == po.Id).ToList();
            var prDetails = prDetailQuery.Where(a=> prDetailIds.Contains(a.Id)).ToList();
            var useBudgetRequest = new ReturnBudgetRequestDto
            {
                PrId = po.PRId,
                SubbudgetId = pr.SubBudgetId.Value,
                Items = prDetails.Select(a => new ReturnInfo
                {
                    PdRowNo = a.RowNo,
                    ReturnAmount = ((a.TotalAmount ?? 0) - poDetail.Where(x => x.PRDetailId == a.Id).Sum(x => x.TotalAmountNoTax)) * (decimal)po.ExchangeRate,
                    ReturnSourceId = po.Id,
                    ReturnSourceCode = po.ApplicationCode
                }).Where(a=>a.ReturnAmount!=0)
            };
            if(useBudgetRequest.Items.Any())
                await LazyServiceProvider.LazyGetService<ISubbudgetService>().ReturnSubbudgetAsync(useBudgetRequest);
        }

        /// <summary>
        /// 更新PR明细
        /// </summary>
        /// <param name="po"></param>
        /// <returns></returns>
        private async Task UpdatePRDetailAsync(PurPOApplication po) 
        {
            var prDetailRepository = LazyServiceProvider.GetService<IPurPRApplicationDetailRepository>();
            var prDetailQuery = await prDetailRepository.GetQueryableAsync();
            var prDetailIds = po.PRApplicationDetailId.Split(',').Select(a=>Guid.Parse(a)).ToList();
            var prDetails = prDetailQuery.Where(a => prDetailIds.Contains(a.Id)).ToList();
            prDetails.ForEach(a => {
                a.OrderStatusFlag = OrderStatusFlag.Orderclosed;
            });
            await prDetailRepository.UpdateManyAsync(prDetails);
        }

        /// <summary>
        /// 为全流程报表增加数据埋点
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> RecordPoCurrentProcessor(CurrentApprovalTaskDto request)
        {
            var poApplicationRepository = LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>();
            try
            {
                var po = await poApplicationRepository.FirstOrDefaultAsync(a => a.Id == request.FormId);

                po.CurrentProcessorIds = string.Join(",", request.Approver.Select(a => a.Id));
                po.CurrentProcessorName = string.Join(",", request.Approver.Select(a => a.Name));
                await poApplicationRepository.UpdateAsync(po);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, $"RecordPoCurrentProcessor：记录PO下一步处理人失败。{JsonConvert.SerializeObject(request)}");
                return MessageResult.FailureResult();
            }

            return MessageResult.SuccessResult();
        }
    }
}
