﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.Contracts.Approval
{
    /// <summary>
    /// 初始化审批任务 
    /// </summary>
    public interface IInitializeApprovalRecordService
    {
        /// <summary>
        /// 插入记录
        /// </summary>
        /// <returns></returns>
        Task InsertInitApprovalRecordAsync(InitializeApprovalRecordDto initApprovalRecord);

        /// <summary>
        /// 推送审批记录
        /// </summary>
        /// <returns></returns>
        Task PushApprovalRecordAsync();
    }
}
