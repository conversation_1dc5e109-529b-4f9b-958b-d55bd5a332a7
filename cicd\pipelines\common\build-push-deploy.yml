
variables:
  - name: buildConfiguration
    value: 'Release'
  - name: env
    ${{ if eq(variables['Build.SourceBranchName'],'develop') }}: #dev
      value: dev
    ${{ if eq(variables['Build.SourceBranchName'],'stage') }}: #stage
      value: stg
    ${{ if eq(variables['Build.SourceBranchName'],'master') }}: #master
      value: prd
  
parameters:
  #Path to YAML file / folder to pass to kubectl configuration parameter
  - name: configurationYML
    type: string
  #Path to Docker Compose file.
  - name: dockerComposeFile
    type: string
  - name: environmentOverride
    type: string

stages:
  - stage: buildAndPushDEV
    displayName: 'Build and Push to ACR Dev'
    condition: eq(variables['env'], 'dev')
    variables: # Stage variables
    - template: variables/dev.yml
    jobs:
      - template: build-push-ACR.yml
        parameters:
          dockerComposeFile: ${{ parameters.dockerComposeFile }}
  
  - stage: buildAndPushStage
    displayName: 'Build and Push to ACR Stage'
    condition: eq(variables['env'], 'stg')
    variables: # Stage variables
    - template: variables/stg.yml
    jobs:
      - template: build-push-ACR.yml
        parameters:
          dockerComposeFile: ${{ parameters.dockerComposeFile }}
          
  - stage: buildAndPushMaster
    displayName: 'Build and Push to ACR Prod'
    condition: eq(variables['env'], 'prd')
    variables: # Stage variables
    - template: variables/prd.yml
    jobs:
      - template: build-push-ACR.yml
        parameters:
          dockerComposeFile: ${{ parameters.dockerComposeFile }}
          
  # - stage: deployDEV
  #   displayName: 'Deploy to AKS DEV'
  #   condition: and(succeeded(), eq(variables['env'], 'dev'))
  #   variables: # Stage variables
  #   - template: variables/dev.yml
  #   jobs:
  #     - template: deploy-to-AKS.yml
  #       parameters:
  #         configurationYML: ${{ parameters.configurationYML }}
  #         branchName: variables['env']
