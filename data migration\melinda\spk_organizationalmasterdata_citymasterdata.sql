select newid() as spk_NexBPMCode,* into #spk_organizationalmasterdata_citymasterdata from (
select ot.spk_BPMCode,ot.spk_NexBPMCode as spk_organizationalmasterdataid,ct.spk_NexBPMCode as spk_citymasterdataid ,oct.spk_bu
from spk_organizationalmasterdata_citymasterdata_tmp oct
join spk_organizationalmasterdata ot
on oct.spk_organizationalmasterdataid=ot.spk_BPMCode
join spk_citymasterdata ct
on ct.spk_citynumber =oct.spk_citymasterdataid
)A


IF OBJECT_ID(N'dbo.spk_organizationalmasterdata_citymasterdata', N'U') IS NOT NULL
BEGIN
	update a 
	set a.spk_BPMCode                    = b.spk_BPMCode
       ,a.spk_organizationalmasterdataid = b.spk_organizationalmasterdataid
       ,a.spk_citymasterdataid           = b.spk_citymasterdataid
       ,a.spk_bu                         = b.spk_bu
    from dbo.spk_organizationalmasterdata_citymasterdata a
    join #spk_organizationalmasterdata_citymasterdata b on a.spk_BPMCode = b.spk_BPMCode
    
    insert into dbo.spk_organizationalmasterdata_citymasterdata
    select a.spk_NexBPMCode
          ,a.spk_BPMCode
          ,a.spk_organizationalmasterdataid
          ,a.spk_citymasterdataid
          ,a.spk_bu
	from #spk_organizationalmasterdata_citymasterdata a
	where NOT EXISTS (SELECT * FROM dbo.spk_organizationalmasterdata_citymasterdata where spk_BPMCode = a.spk_BPMCode)
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into dbo.spk_organizationalmasterdata_citymasterdata from #spk_organizationalmasterdata_citymasterdata
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END

