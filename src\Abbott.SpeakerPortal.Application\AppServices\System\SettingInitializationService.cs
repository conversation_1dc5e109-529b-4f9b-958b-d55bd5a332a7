﻿using Abbott.SpeakerPortal.Consts;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;
using Volo.Abp.SettingManagement;
using Volo.Abp.Settings;

namespace Abbott.SpeakerPortal.AppServices.System
{
    /// <summary>
    /// 设置的初始化
    /// </summary>
    public class SettingInitializationService : ITransientDependency
    {
        private readonly ISettingManager _settingManager;

        public SettingInitializationService(ISettingManager settingManager)
        {
            _settingManager = settingManager;
        }
        public async Task InitializeAsync()
        {
            var settingsToInitialize = new[]
            {
                SettingsConst.QRCodeExpirationDays,
                SettingsConst.ADCNeedSalesMktApprovalRMBAmount,
                SettingsConst.OmBpmInterfaceOnAndOff,
            };

            foreach (var settingName in settingsToInitialize)
            {
                var currentValue = await _settingManager.GetOrNullAsync(settingName,"G",null,false);
                if (currentValue == null)
                {
                    switch (settingName)
                    {
                        case SettingsConst.QRCodeExpirationDays:
                            await _settingManager.SetAsync(settingName, "14", "G", null,true);
                            break;
                        case SettingsConst.ADCNeedSalesMktApprovalRMBAmount:
                            await _settingManager.SetAsync(settingName, "20000", "G",null,true);
                            break;
                        case SettingsConst.OmBpmInterfaceOnAndOff:
                            await _settingManager.SetAsync(settingName, "OFF", "G", null, true);
                            break;
                    }
                }
            }
        }
    }
}
