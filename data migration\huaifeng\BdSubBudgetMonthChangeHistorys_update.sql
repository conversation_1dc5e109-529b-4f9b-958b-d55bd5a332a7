SELECT
 TRY_CONVERT(UNIQUEIDENTIFIER, [Id]) [Id]
,TRY_CONVERT(UNIQUEIDENTIFIER, [HistoryId]) [HistoryId]
,[OperateAmount]
,[Status]
,[Month]
,[ExtraProperties]
,[ConcurrencyStamp]
,GETDATE() [CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CreatorId]) [CreatorId]
,GETDATE() [LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [LastModifierId]) [LastModifierId]
,[IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, CAST([DeleterId] AS nvarchar)) [DeleterId]
,GETDATE() [DeletionTime]
INTO #BdSubBudgetMonthChangeHistorys
FROM PLATFORM_ABBOTT_STG.dbo.BdSubBudgetMonthChangeHistorys

--drop table #BdMonthlyBudgets


USE Speaker_Portal_STG;

UPDATE a
SET
 a.[Id] = b.[Id]
,a.[HistoryId] = b.[HistoryId]
,a.[OperateAmount] = b.[OperateAmount]
,a.[Status] = b.[Status]
,a.[Month] = b.[Month]
,a.[ExtraProperties] = b.[ExtraProperties]
,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = b.[CreatorId]
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = b.[LastModifierId]
,a.[IsDeleted] = b.[IsDeleted]
,a.[DeleterId] = b.[DeleterId]
,a.[DeletionTime] = b.[DeletionTime]
FROM dbo.BdSubBudgetMonthChangeHistorys a
left join #BdSubBudgetMonthChangeHistorys  b
ON a.id=b.id


INSERT INTO dbo.BdSubBudgetMonthChangeHistorys
SELECT
 [Id]
,[HistoryId]
,[Month]
,[OperateAmount]
,[Status]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
FROM #BdSubBudgetMonthChangeHistorys a
WHERE not exists (select * from dbo.BdSubBudgetMonthChangeHistorys where id=a.id)

--truncate table dbo.BdSubBudgetMonthChangeHistorys



--alter table dbo.BdSubBudgetMonthChangeHistorys alter column [OperateAmount] decimal(32,8)