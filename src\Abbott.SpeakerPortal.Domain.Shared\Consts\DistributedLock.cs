﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Abbott.SpeakerPortal.Consts
{
    /// <summary>
    /// 分布式锁名称
    /// </summary>
    public class DistributedLock
    {
        public const string DistributedLock_Migration = "DistributedLock_Migration";

        /// <summary>
        /// 邮件工作分布式锁
        /// </summary>
        public const string DistributedLock_BPMEmail = "DistributedLock_BPMEmail";

        /// <summary>
        /// 推送医师信息验证job
        /// </summary>
        public const string DistributedLock_PushDcrWorkder = "DistributedLock_PushDcrWorkder";

        /// <summary>
        /// 生成序列号
        /// </summary>
        public const string DistributedLock_GenerateSerialNo = "DistributedLock_GenerateSerialNo";

        /// <summary>
        /// 生成预算序列号
        /// </summary>
        public const string DistributedLock_GenerateSerialBudgetNo = "DistributedLock_GenerateSerialBudgetNo";

        /// <summary>
        /// 生成子预算序列号
        /// </summary>
        public const string DistributedLock_GenerateSerialSubBudgetNo = "DistributedLock_GenerateSerialSubBudgetNo";

        /// <summary>
        /// 推送医师信息验证job
        /// </summary>
        public const string DistributedLock_PushDcrCroosBuWorkder = "DistributedLock_PushDcrCroosBuWorkder";

        /// <summary>
        /// 创建审批任务
        /// </summary>
        public const string DistributedLock_InitApprovalWorkder = "DistributedLock_InitApprovalWorkder";

        /// <summary>
        /// 为Bpcs生成文件序号
        /// </summary>
        public const string DistributedLock_GenerateSequenceNumForFileName = "DistributedLock_GenerateSequenceNumForFileName";

        /// <summary>
        /// Veeva推送后超时验证邮件
        /// </summary>
        public const string DistributedLock_VeevaPushTimeoutSendEmail = "DistributedLock_VeevaPushTimeoutSendEmail";

        /// <summary>
        /// Veeva处理hco_dcr 
        /// </summary>
        public const string DistributedLock_VeevaHcoDcrLogUpdate = "DistributedLock_VeevaHcoDcrLogUpdate";

        /// <summary>
        /// 生成FOC预算序列号
        /// </summary>
        public const string DistributedLock_GenerateSerialFocBudgetNo = "DistributedLock_GenerateSerialFocBudgetNo";

        /// <summary>
        /// 生成FOC子预算序列号
        /// </summary>
        public const string DistributedLock_GenerateSerialFocSubBudgetNo = "DistributedLock_GenerateSerialFocSubBudgetNo";
    }
}
