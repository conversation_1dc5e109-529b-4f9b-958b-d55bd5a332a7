﻿using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Integration.EpdOlineMeeting.BPM;
using Abbott.SpeakerPortal.Enums.Integration;
using Hangfire;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.DistributedLocking;

namespace Abbott.SpeakerPortal.BackgroundWorkers.Integration.BpmOm
{
    public class BpmOmWorker : SpeakerPortalBackgroundWorkerBase
    {
        public BpmOmWorker()
        {
            CronExpression = Cron.Never();
        }
        private BpmRequestDto RequestDto;
        public override async Task DoWorkAsync(CancellationToken cancellationToken = default)
        {
            var bpmOmAppService = LazyServiceProvider.LazyGetService<IBpmOmAppService>();

            switch (RequestDto.InterfaceType)
            {
                case OmBpmType.Add:
                    await bpmOmAppService.OmAddMeetingAsync(RequestDto.PrNo);
                    break;
                case OmBpmType.Revoke:
                    await bpmOmAppService.OmRevokeMeetingAsync(RequestDto.PrNo);
                    break;
                case OmBpmType.Print:
                    await bpmOmAppService.OmAddPrintPAPR(RequestDto.PaNo);
                    break;
                case OmBpmType.Review:
                    await bpmOmAppService.OmReviewMeeting(RequestDto.PaNo, RequestDto.ApprovalPerson, RequestDto.ApprovalTime);
                    break;
                case OmBpmType.UpdateStatus:
                    await bpmOmAppService.OmUpdateMeetingSpeakerStatus(RequestDto.PaNo, RequestDto.Status, RequestDto.Remark);
                    break;
            }
        }

        public async Task DoWorkAsync(BpmRequestDto requestDto)
        {
            RequestDto = requestDto;
            // 使用默认的 CancellationToken
            await DoWorkAsync(CancellationToken.None);
        }
    }
}
