﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Budget;
using Abbott.SpeakerPortal.Contracts.Concur;
using Abbott.SpeakerPortal.Contracts.Concur.MealReport;
using Abbott.SpeakerPortal.Contracts.Concur.OrgMapping;
using Abbott.SpeakerPortal.Contracts.Report;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.Concur.MealEmployee;
using Abbott.SpeakerPortal.Entities.Concur.MealOrgMapping;
using Abbott.SpeakerPortal.Entities.Concur.MealReport;
using Abbott.SpeakerPortal.Entities.User;

using ClosedXML.Excel;

using EFCore.BulkExtensions;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

using MiniExcelLibs;
using MiniExcelLibs.OpenXml;

using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace Abbott.SpeakerPortal.AppServices.Concur
{
    public class ConcurService : SpeakerPortalAppService, IConcurService
    {
        #region 员工分组配置

        /// <summary>
        /// 查询员工分组配置信息
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<EmployeeQueryResponseDto>> QueryEmployeeListAsync(EmployeeQueryRequestDto request)
        {
            IEnumerable<Guid> buIds = [];
            var isAdmin = CurrentUser.IsInRole(RoleNames.BizAdmin);
            //非管理员只能查看授权BU相关的数据
            if (!isAdmin)
            {
                var bus = await LazyServiceProvider.LazyGetService<IBdMasterBudgetService>().GetAuthorizedBudgetBuAsync();
                buIds = bus.Select(a => a.Id).ToArray();
            }

            var queryEmp = await LazyServiceProvider.LazyGetService<IMealEmployeeReadonlyRepository>().GetQueryableAsync();
            var query = queryEmp
                .WhereIf(!isAdmin, a => buIds.Contains(a.BuId))
                .WhereIf(DateTime.TryParseExact(request.Date, "yyyy-MM", null, DateTimeStyles.None, out DateTime date), a => a.Date == date)
                .WhereIf(!string.IsNullOrEmpty(request.Name), a => a.Name.Contains(request.Name))
                .WhereIf(!string.IsNullOrEmpty(request.City), a => a.City.Contains(request.City))
                .WhereIf(!string.IsNullOrEmpty(request.Hierarchy1), a => a.Hierarchy1.Contains(request.Hierarchy1))
                .WhereIf(!string.IsNullOrEmpty(request.Hierarchy2), a => a.Hierarchy2.Contains(request.Hierarchy2))
                .WhereIf(!string.IsNullOrEmpty(request.Department), a => a.Department.Contains(request.Department))
                .WhereIf(!string.IsNullOrEmpty(request.Region), a => a.Region.Contains(request.Region));

            var count = await query.CountAsync();
            var employees = await query
            .OrderByDescending(a => a.Date)
            .ThenBy(a => a.UPI)
            .Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToArrayAsync();
            var datas = ObjectMapper.Map<IEnumerable<MealEmployee>, IReadOnlyList<EmployeeQueryResponseDto>>(employees);

            return new PagedResultDto<EmployeeQueryResponseDto>(count, datas);
        }

        /// <summary>
        /// 验证导入的员工数据
        /// </summary>
        /// <param name="importRowDtos"></param>
        /// <returns></returns>
        async Task<IEnumerable<EmployeeImportValidRowDto>> ValidateImportEmployeeDatasAsync(IEnumerable<EmployeeImportRowDto> importRowDtos)
        {
            if (!importRowDtos.Any())
                return [];

            //判断员工号是否有重复
            var dictDuplicationEmpNos = importRowDtos.GroupBy(a => a.UPI).Where(a => a.Count() > 1).Select(a => a.Key).ToDictionary(a => a, a => a);
            //获取BU
            var bus = await LazyServiceProvider.LazyGetService<IDataverseService>().GetDivisions();
            var dictBus = bus.ToDictionary(a => a.DepartmentName, a => a);

            var responses = ObjectMapper.Map<IEnumerable<EmployeeImportRowDto>, IEnumerable<EmployeeImportValidRowDto>>(importRowDtos);
            var i = 2;
            foreach (var item in responses)
            {
                //有列头，所以这里从2开始
                item.RowNo = i;
                //校验UPI
                if (string.IsNullOrEmpty(item.UPI))
                    item.ErrorMessage = "UPI必填；";
                else if (dictDuplicationEmpNos.ContainsKey(item.UPI))
                    item.ErrorMessage = "该员工号重复出现；";

                //校验Category
                if (dictBus.TryGetValue(item.BuName, out DepartmentDto bu))
                    item.BuId = bu.Id;
                else
                    item.ErrorMessage += "Category必填或匹配失败；";

                i++;
            }

            return responses;
        }

        /// <summary>
        /// 导入员工数据做校验，并返回验证后的信息
        /// </summary>
        /// <param name="importRowDtos"></param>
        /// <returns></returns>
        public async Task<MessageResult> UploadEmployeeAsync(byte[] buffer)
        {
            using var stream = new MemoryStream(buffer);
            XLWorkbook xlWorkbook = null;
            IXLWorksheet ws = null;
            try
            {
                xlWorkbook = new XLWorkbook(stream);
                ws = xlWorkbook.Worksheets.FirstOrDefault();
                if (ws == null)
                    return MessageResult.FailureResult("请上传Excel文件模板");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, ex.Message);
                return MessageResult.FailureResult("请上传Excel文件模板");
            }

            //验证模板正确性
            if (ws.FirstColumn().Cell(1).GetString() != "UPI")
                return MessageResult.FailureResult("上传文件不符合模板要求，请确保列名在第一行");

            //读取excel数据
            var importRowDtos = ws.RowsUsed().Skip(1).Select((r, index) => new EmployeeImportRowDto
            {
                UPI = r.Cell(1).GetString(),
                Name = r.Cell(2).GetString(),
                BuName = r.Cell(3).GetString(),
                JobLevel = r.Cell(4).GetString(),
                Sex = r.Cell(5).GetString(),
                EmployStatus = r.Cell(6).GetString(),
                OnboardDate = r.Cell(7).TryGetValue(out DateTime onboardDate) ? onboardDate : null,
                Department = r.Cell(8).GetString(),
                Region = r.Cell(9).GetString(),
                City = r.Cell(10).GetString(),
                JobNameEn = r.Cell(11).GetString(),
                JobName = r.Cell(12).GetString(),
                Email = r.Cell(13).GetString(),
                LegalEntity = r.Cell(14).GetString(),
                ReportToUPI = r.Cell(15).GetString(),
                ReportTo = r.Cell(16).GetString(),
                DepartureDate = r.Cell(17).TryGetValue(out DateTime departureDate) ? departureDate : null,
                EmpCostcenterCode = r.Cell(18).GetString(),
                Hierarchy1 = r.Cell(19).GetString(),
                Hierarchy2 = r.Cell(20).GetString()
            });

            //验证导入的员工数据
            var validRows = await ValidateImportEmployeeDatasAsync(importRowDtos);
            var errorRows = validRows.Where(a => !string.IsNullOrEmpty(a.ErrorMessage));
            var response = new EmployeeImportValidResponseDto { IsSuccess = true };
            if (errorRows.Any())
            {
                response.IsSuccess = false;
                response.Datas = errorRows;
                response.ErrorDataFile = Convert.ToBase64String(JsonSerializer.SerializeToUtf8Bytes(response.Datas));
            }
            else
                response.Datas = validRows;

            return MessageResult.SuccessResult(response);
        }

        /// <summary>
        /// 执行上传员工数据
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> ImportEmployeeAsync(EmployeeImportRequestDto request)
        {
            if (!request.Rows.Any())
                return MessageResult.SuccessResult();

            //验证导入的员工数据
            var validRows = await ValidateImportEmployeeDatasAsync(request.Rows);
            if (validRows.Any(a => !string.IsNullOrEmpty(a.ErrorMessage)))
                return MessageResult.FailureResult("数据验证失败");

            request.Date = new DateTime(request.Date.Value.Year, request.Date.Value.Month, 1);
            var upis = request.Rows.Select(a => a.UPI).ToArray();

            //先删除现有数据再新增
            var empRepository = LazyServiceProvider.LazyGetService<IMealEmployeeRepository>();
            await empRepository.DeleteDirectAsync(a => a.Date == request.Date && upis.Contains(a.UPI));

            var now = DateTime.Now;
            var listEmployees = new List<MealEmployee>();
            foreach (var item in validRows)
            {
                var employee = ObjectMapper.Map<EmployeeImportValidRowDto, MealEmployee>(item);
                employee.SetId(GuidGenerator.Create());
                employee.SetCreatorIdAndCreationTime(CurrentUser.Id, now);
                employee.Date = request.Date.Value;
                listEmployees.Add(employee);
            }

            var context = await empRepository.GetDbContextAsync();
            await context.BulkInsertAsync(listEmployees);

            return MessageResult.SuccessResult();
        }

        #endregion

        #region 机构转换配置

        /// <summary>
        /// 获取机构转换配置列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<QueryOrgMappingResponseDto>> QueryOrgMappingListAsync(QueryOrgMappingRequestDto request)
        {
            var queryOrgMapping = await LazyServiceProvider.LazyGetService<IMealOrgMappingReadonlyRepository>().GetQueryableAsync();
            var query = queryOrgMapping
            .WhereIf(!string.IsNullOrEmpty(request.OriginalName), a => a.OriginalName.Contains(request.OriginalName))
            .WhereIf(!string.IsNullOrEmpty(request.NewName), a => a.NewName.Contains(request.NewName))
            .Select(a => new QueryOrgMappingResponseDto
            {
                Id = a.Id,
                OriginalName = a.OriginalName,
                NewName = a.NewName,
                CreatedAt = a.CreationTime.ToString("yyyy-MM-dd"),
                LastModifiedAt = a.LastModificationTime.HasValue ? a.LastModificationTime.Value.ToString("yyyy-MM-dd") : null
            });

            var count = await query.CountAsync();
            var datas = await query.Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToArrayAsync();

            return new PagedResultDto<QueryOrgMappingResponseDto>(count, datas);
        }

        /// <summary>
        /// 获取机构名称列表
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        public async Task<IEnumerable<string>> GetOrgNewNamesAsync(string keyword)
        {
            var queryOrgMapping = await LazyServiceProvider.LazyGetService<IMealOrgMappingReadonlyRepository>().GetQueryableAsync();
            var datas = await queryOrgMapping
                .Where(a => !string.IsNullOrEmpty(a.NewName))
                .WhereIf(!string.IsNullOrEmpty(keyword), a => a.NewName.Contains(keyword))
                .Select(a => a.NewName.Trim())
                .Distinct()
                .Take(10)
                .ToArrayAsync();

            return datas;
        }

        /// <summary>
        /// 创建机构转换配置
        /// </summary>
        /// <param name="orgNames"></param>
        /// <returns></returns>
        public async Task<MessageResult> CreateOrgMappingAsync(IEnumerable<string> orgNames)
        {
            var orgMappingRepository = LazyServiceProvider.LazyGetService<IMealOrgMappingRepository>();
            //查询出已存在的机构
            var existsOrgs = await orgMappingRepository.GetListAsync(a => orgNames.Contains(a.OriginalName));
            //获取新增机构
            var exceptOrgs = orgNames.Except(existsOrgs.Select(a => a.OriginalName)).Select(a => new MealOrgMapping { OriginalName = a }).ToArray();
            //新机构执行新增操作
            if (exceptOrgs.Any())
                await orgMappingRepository.InsertManyAsync(exceptOrgs);

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 更新机构转换配置
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> UpdateOrgMappingAsync(UpdateOrgMappingRequestDto request)
        {
            var orgMappingRepository = LazyServiceProvider.LazyGetService<IMealOrgMappingRepository>();
            var orgMapping = await orgMappingRepository.FindAsync(request.Id);
            if (orgMapping == null)
                return MessageResult.FailureResult("未找到指定的数据");

            orgMapping.MealOrgMappingHistories.Add(new MealOrgMappingHistory { Content = $"原名：{orgMapping.OriginalName}，转换后名称：{request.NewName}" });
            orgMapping.NewName = request.NewName;

            await orgMappingRepository.UpdateAsync(orgMapping);

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 获取机构配置变更历史
        /// </summary>
        /// <param name="orgMappingId"></param>
        /// <returns></returns>
        public async Task<IEnumerable<QueryOrgMappingHistoryResponseDto>> GetOrgMappingHistoryListAsync(Guid orgMappingId)
        {
            var queryOrgMappingHistory = await LazyServiceProvider.LazyGetService<IMealOrgMappingHistoryReadonlyRepository>().GetQueryableAsync();
            var queryUser = await LazyServiceProvider.LazyGetService<IIdentityUserReadonlyRepository>().GetQueryableAsync();
            var datas = await queryOrgMappingHistory.Where(a => a.MealOrgMappingId == orgMappingId)
            .GroupJoin(queryUser, a => a.CreatorId, a => a.Id, (a, b) => new { History = a, Users = b })
            .SelectMany(a => a.Users.DefaultIfEmpty(), (a, b) => new { a.History, User = b })
            .Select(a => new QueryOrgMappingHistoryResponseDto
            {
                Operator = a.User.Name,
                OperateDate = a.History.CreationTime.ToString("yyyy-MM-dd"),
                Content = a.History.Content
            })
            .ToArrayAsync();

            return datas;
        }

        #endregion

        #region 用餐报告

        /// <summary>
        /// 查询用餐报告
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<MealReportQueryResponseDto>> QueryMealReportListAsync(MealReportQueryRequestDto request)
        {
            IEnumerable<Guid> buIds = [];
            var isAdmin = CurrentUser.IsInRole(RoleNames.BizAdmin);
            //非管理员只能查看授权BU相关的数据
            if (!isAdmin)
            {
                var bus = await LazyServiceProvider.LazyGetService<IBdMasterBudgetService>().GetAuthorizedBudgetBuAsync();
                buIds = bus.Select(a => a.Id).ToArray();
            }

            var queryMealReport = await LazyServiceProvider.LazyGetService<IMealReportReadonlyRepository>().GetQueryableAsync();
            var queryEmployee = await LazyServiceProvider.LazyGetService<IMealEmployeeReadonlyRepository>().GetQueryableAsync();
            var queryOrgMapping = await LazyServiceProvider.LazyGetService<IMealOrgMappingReadonlyRepository>().GetQueryableAsync();

            //审批状态或支付状态有值时，转换其code为name进行查询
            if (!string.IsNullOrEmpty(request.ApprovalStatus) || !string.IsNullOrEmpty(request.PaymentStatus))
            {
                var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
                var getConcurApprovalStatusTask = dataverseService.GetDictionariesAsync(DictionaryType.ConcurApprovalStatus);
                var getConcurPaymentStatusTask = dataverseService.GetDictionariesAsync(DictionaryType.ConcurPaymentStatus);
                Task.WaitAll(getConcurApprovalStatusTask, getConcurPaymentStatusTask);

                if (!string.IsNullOrEmpty(request.ApprovalStatus))
                    request.ApprovalStatus = getConcurApprovalStatusTask.Result.FirstOrDefault(a => a.Code == request.ApprovalStatus)?.Name;

                if (!string.IsNullOrEmpty(request.PaymentStatus))
                    request.PaymentStatus = getConcurPaymentStatusTask.Result.FirstOrDefault(a => a.Code == request.PaymentStatus)?.Name;
            }

            var query = queryMealReport
                .WhereIf(!isAdmin, a => buIds.Contains(a.DivisionId))
                .WhereIf(request.FirstSubmittedDate1.HasValue, a => a.FirstSubmittedDate >= request.FirstSubmittedDate1)
                .WhereIf(request.FirstSubmittedDate2.HasValue, a => a.FirstSubmittedDate < request.FirstSubmittedDate2.Value.AddDays(1))
                .WhereIf(request.TransactionDate1.HasValue, a => a.TransactionDate >= request.TransactionDate1)
                .WhereIf(request.TransactionDate2.HasValue, a => a.TransactionDate < request.TransactionDate2.Value.AddDays(1))
                .WhereIf(!string.IsNullOrEmpty(request.ApprovalStatus), a => a.ApprovalStatus == request.ApprovalStatus)
                .WhereIf(request.PaidDate1.HasValue, a => a.PaidDate >= request.PaidDate1)
                .WhereIf(request.PaidDate2.HasValue, a => a.PaidDate < request.PaidDate2.Value.AddDays(1))
                .WhereIf(!string.IsNullOrEmpty(request.PaymentStatus), a => a.PaymentStatus == request.PaymentStatus)
                .GroupJoin(queryEmployee, a => new { Date = a.TransactionDateMask, UPI = a.EmployeeID }, a => new { a.Date, a.UPI }, (a, b) => new { MealReport = a, MealEmployees = b })
                .SelectMany(a => a.MealEmployees.DefaultIfEmpty(), (a, b) => new { a.MealReport, MealEmployee = b })
                .WhereIf(!string.IsNullOrEmpty(request.EmployeeHierarchy1), a => a.MealEmployee.Hierarchy1.Contains(request.EmployeeHierarchy1))
                .WhereIf(!string.IsNullOrEmpty(request.EmployeeHierarchy2), a => a.MealEmployee.Hierarchy2.Contains(request.EmployeeHierarchy2))
                .WhereIf(!string.IsNullOrEmpty(request.EmployeeDepartment), a => a.MealEmployee.Department.Contains(request.EmployeeDepartment))
                .WhereIf(!string.IsNullOrEmpty(request.EmployeeRegion), a => a.MealEmployee.Region.Contains(request.EmployeeRegion))
                .WhereIf(!string.IsNullOrEmpty(request.EmployeeCity), a => a.MealEmployee.City.Contains(request.EmployeeCity))
                .GroupJoin(queryOrgMapping, a => a.MealReport.Company, a => a.OriginalName, (a, b) => new { a.MealReport, a.MealEmployee, OrgMappings = b })
                .SelectMany(a => a.OrgMappings.DefaultIfEmpty(), (a, b) => new
                {
                    a.MealReport,
                    MealEmployee = new
                    {
                        a.MealEmployee.Name,
                        a.MealEmployee.Department,
                        a.MealEmployee.Region,
                        a.MealEmployee.City,
                        a.MealEmployee.Email,
                        a.MealEmployee.Hierarchy1,
                        a.MealEmployee.Hierarchy2
                    },
                    CompanyMappingName = b.NewName
                });

            var count = await query.CountAsync();
            var datas = await query
            .OrderByDescending(a => a.MealReport.FirstSubmittedDate)
            .ThenByDescending(a => a.MealReport.ReportId)
            .Skip(request.PageIndex * request.PageSize).Take(request.PageSize)
            .Select(a => new
            {
                a.MealReport,
                a.MealEmployee,
                a.CompanyMappingName
            })
            .ToArrayAsync();

            var responseDatas = datas.Select(a =>
            {
                var data = ObjectMapper.Map<MealReport, MealReportQueryResponseDto>(a.MealReport);
                data.EmployeeName = a.MealEmployee.Name;
                data.EmployeeDepartment = a.MealEmployee.Department;
                data.EmployeeRegion = a.MealEmployee.Region;
                data.EmployeeCity = a.MealEmployee.City;
                data.EmployeeEmail = a.MealEmployee.Email;
                data.EmployeeHierarchy1 = a.MealEmployee.Hierarchy1;
                data.EmployeeHierarchy2 = a.MealEmployee.Hierarchy2;
                data.AdjustedCompany = a.CompanyMappingName;

                return data;
            }).ToArray();

            return new PagedResultDto<MealReportQueryResponseDto>(count, responseDatas);
        }

        /// <summary>
        /// 用餐报告数据正确性验证
        /// </summary>
        /// <param name="importRowDtos"></param>
        /// <returns></returns>
        async Task<MealReportImportValidResultDto> ValidateMealReportDatasAsync(IEnumerable<MealReportImportRowDto> importRowDtos)
        {
            if (!importRowDtos.Any())
                return new();

            decimal amount;
            DateTime dateTime;
            //获取BU
            var bus = await LazyServiceProvider.LazyGetService<IDataverseService>().GetDivisions();
            var dictBus = bus.ToDictionary(a => a.DepartmentName, a => a);
            var listErrorRows = new List<MealReportImportErrorRowDto>();

            #region 验证必填/格式等

            foreach (var item in importRowDtos)
            {
                DepartmentDto bu = null;
                var errorRow = new MealReportImportErrorRowDto { ReportId = item.ReportId };

                var context = new ValidationContext(item);
                var results = new List<ValidationResult>();
                if (!Validator.TryValidateObject(item, context, results, true))
                    foreach (var result in results)
                        errorRow.ErrorMessages.Add(result.ErrorMessage);

                //验证Sent For Payment Date
                if (!string.IsNullOrEmpty(item.SentForPaymentDate))
                    if (!DateTime.TryParse(item.SentForPaymentDate, out dateTime))
                        errorRow.ErrorMessages.Add("Sent for Payment Date格式错误");
                    else
                        item.SentForPaymentDate = dateTime.ToString("yyyy-MM-dd");

                //验证Transaction Date
                if (string.IsNullOrEmpty(item.TransactionDate))
                    errorRow.ErrorMessages.Add("Transaction Date必填");
                else if (!DateTime.TryParse(item.TransactionDate, out dateTime))
                    errorRow.ErrorMessages.Add("Transaction Date格式错误");
                else
                    item.TransactionDate = dateTime.ToString("yyyy-MM-dd");

                //验证First Submitted Date
                if (!string.IsNullOrEmpty(item.FirstSubmittedDate))
                    if (!DateTime.TryParse(item.FirstSubmittedDate, out dateTime))
                        errorRow.ErrorMessages.Add("First Submitted Date格式错误");
                    else
                        item.FirstSubmittedDate = dateTime.ToString("yyyy-MM-dd");

                //验证Report Date
                if (!string.IsNullOrEmpty(item.ReportDate))
                    if (!DateTime.TryParse(item.ReportDate, out dateTime))
                        errorRow.ErrorMessages.Add("Report Date格式错误");
                    else
                        item.ReportDate = dateTime.ToString("yyyy-MM-dd");

                //验证Paid Date
                if (!string.IsNullOrEmpty(item.PaidDate))
                    if (!DateTime.TryParse(item.PaidDate, out dateTime))
                        errorRow.ErrorMessages.Add("Paid Date格式错误");
                    else
                        item.PaidDate = dateTime.ToString("yyyy-MM-dd");

                //验证Division
                if (string.IsNullOrEmpty(item.Division) || !dictBus.TryGetValue(item.Division, out bu))
                    errorRow.ErrorMessages.Add("Division必填或匹配失败");
                else
                    item.DivisionId = bu?.Id;

                //验证Expense Amount (transaction currency) / Transaction Amount
                if (string.IsNullOrEmpty(item.ExpenseAmount) || !decimal.TryParse(item.ExpenseAmount, out amount) || amount < 0)
                    errorRow.ErrorMessages.Add("Expense Amount (transaction currency)或Transaction Amount必填且不得为负数");

                //验证Exchange Rate
                if (!string.IsNullOrEmpty(item.ExchangeRate) && !decimal.TryParse(item.ExchangeRate, out amount))
                    errorRow.ErrorMessages.Add("Exchange Rate格式错误");

                //验证Expense Amount (Rmb)
                if (!string.IsNullOrEmpty(item.ExpenseAmountRmb) && !decimal.TryParse(item.ExpenseAmountRmb, out amount))
                    errorRow.ErrorMessages.Add("Expense Amount (Rmb)格式错误");

                //验证Approved Amount
                if (!string.IsNullOrEmpty(item.ApprovedAmount) && !decimal.TryParse(item.ApprovedAmount, out amount))
                    errorRow.ErrorMessages.Add("Approved Amount格式错误");

                //验证Amt Per Attendee
                if (!string.IsNullOrEmpty(item.AmtPerAttendee) && !decimal.TryParse(item.AmtPerAttendee, out amount))
                    errorRow.ErrorMessages.Add("Approved Amount (Transaction Currency)或Amt Per Attendee格式错误");

                //验证Number of Attendees
                if (!string.IsNullOrEmpty(item.NumberOfAttendees) && !int.TryParse(item.NumberOfAttendees, out int number))
                    errorRow.ErrorMessages.Add("Number of Attendees格式错误");

                //加入验证错误的数据
                if (errorRow.ErrorMessages.Count > 0)
                    listErrorRows.Add(errorRow);
            }

            #endregion

            //导入的数据中排除验证不通过的数据
            var errorReportIds = listErrorRows.GroupBy(a => a.ReportId).Select(a => a.Key).ToArray();
            var validRows = importRowDtos.Where(a => !errorReportIds.Contains(a.ReportId)).ToArray();
            var validResult = new MealReportImportValidResultDto
            {
                ValidRows = ObjectMapper.Map<IEnumerable<MealReportImportRowDto>, IEnumerable<MealReportImportValidRowDto>>(validRows),
                ErrorDatas = listErrorRows
            };

            return validResult;
        }

        /// <summary>
        /// 读取Excel的值
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="dict"></param>
        /// <param name="key1"></param>
        /// <param name="key2"></param>
        /// <returns></returns>
        string GetValueAsync(IDictionary<string, object> dict, string key1, string key2 = null)
        {
            object value;
            //第一个key获取不成功，才获取第二个key
            if (!dict.TryGetValue(key1, out value))
                if (!string.IsNullOrEmpty(key2))
                    dict.TryGetValue(key2, out value);

            return value?.ToString();
        }

        /// <summary>
        /// 上传用餐报告并验证
        /// </summary>
        /// <param name="buffer"></param>
        /// <returns></returns>
        public async Task<MessageResult> UploadMealReportAsync(byte[] buffer)
        {
            using var stream = new MemoryStream(buffer);
            var datas = await MiniExcel.QueryAsync(stream, true);
            //读取excel数据
            var importRowDtos = datas.Select(dict => new MealReportImportRowDto
            {
                EmployeeFirstName = GetValueAsync(dict, "Employee First Name"),
                MiddleInitial = GetValueAsync(dict, "Middle Initial"),
                EmployeeLastName = GetValueAsync(dict, "Employee Last Name"),
                EmployeeID = GetValueAsync(dict, "Employee ID"),
                EmployeeEmailAddress = GetValueAsync(dict, "Employee E-mail Address"),
                Division = GetValueAsync(dict, "Org Unit 5 - Name", "Division"),
                ReportName = GetValueAsync(dict, "Report Name"),
                ReportId = GetValueAsync(dict, "Report ID"),
                SentForPaymentDate = GetValueAsync(dict, "Sent for Payment Date"),
                FirstSubmittedDate = GetValueAsync(dict, "First Submitted Date"),
                ReportDate = GetValueAsync(dict, "Report Date"),
                ApprovalStatus = GetValueAsync(dict, "Approval Status"),
                PaymentStatus = GetValueAsync(dict, "Payment Status"),
                PaidDate = GetValueAsync(dict, "Paid Date"),
                ReportLegacyKey = GetValueAsync(dict, "Report Legacy Key"),
                EntryLegacyKey = GetValueAsync(dict, "Entry Legacy Key"),
                AttendeeKey = GetValueAsync(dict, "Attendee Key"),
                PaymentType = GetValueAsync(dict, "Payment Type"),
                LedgerOrDivision = GetValueAsync(dict, "Org Unit 3 - Name", "Ledger/Division"),
                DepartmentCostCenter = GetValueAsync(dict, "Org Unit 4 - Code", "Department/Cost Center"),
                ExpenseType = GetValueAsync(dict, "Expense Type"),
                TransactionDate = GetValueAsync(dict, "Transaction Date"),
                Venue = GetValueAsync(dict, "Custom 26 - Name", "Venue"),
                Vendor = GetValueAsync(dict, "Vendor"),
                Purpose = GetValueAsync(dict, "Custom 25 - Name", "Purpose"),
                Product = GetValueAsync(dict, "Custom 29 - Name", "Product"),
                CityLocation = GetValueAsync(dict, "City/Location"),
                Country = GetValueAsync(dict, "Country"),
                Personal = GetValueAsync(dict, "Personal"),
                HasAffidavit = GetValueAsync(dict, "Has Affidavit?", "Has Affidavit"),
                ExpenseAmount = GetValueAsync(dict, "Transaction Amount", "Expense Amount (transaction currency)"),
                TransactionCurrency = GetValueAsync(dict, "Transaction Currency"),
                ExchangeRate = GetValueAsync(dict, "Exchange Rate"),
                ExpenseAmountRmb = GetValueAsync(dict, "Transaction Amount (Reimbursement Currency", "Expense Amount (Rmb)"),
                ApprovedAmount = GetValueAsync(dict, "Approved Amount"),
                NumberOfAttendees = GetValueAsync(dict, "Number of Attendees"),
                AttendeeType = GetValueAsync(dict, "Attendee Type"),
                IsTraveling = GetValueAsync(dict, "Is Traveling"),
                AmtPerAttendee = GetValueAsync(dict, "Approved Amount (Transaction Currency)", "Amt Per Attendee"),
                AttendeeName = GetValueAsync(dict, "Attendee Name"),
                AttendeeFirstName = GetValueAsync(dict, "Attendee First Name"),
                AttendeeMiddleInitial = GetValueAsync(dict, "Attendee Middle Initial"),
                AttendeeLastName = GetValueAsync(dict, "Attendee Last Name"),
                ExternalID = GetValueAsync(dict, "External ID"),
                Company = GetValueAsync(dict, "Company"),
                AttendeeWorkAddress = GetValueAsync(dict, "Address", "Attendee Work Address"),
                AttendeeEmailAddress = GetValueAsync(dict, "Custom 16 - Name", "Attendee Email Address"),
                WorkAddressCity = GetValueAsync(dict, "City", "Work Address City"),
                Title = GetValueAsync(dict, "Attendee - HCP Title or Business Entity/HCO Type", "Title"),
                Comment = GetValueAsync(dict, "Comment")
            }).ToArray();

            //验证导入的员工数据
            var result = await ValidateMealReportDatasAsync(importRowDtos);
            var response = new MealReportImportValidResponseDto { IsSuccess = true, TotalCount = importRowDtos.Length };

            if (result?.ErrorDatas?.Any() == true)
            {
                var listErrorRows = new List<MealReportImportRowDto>();
                //根据Report Id找到该报告所有的数据，一并生成错误数据文件
                var errorDatas = result.ErrorDatas.GroupBy(a => a.ReportId).Select(a => new MealReportImportErrorRowDto { ReportId = a.Key, ErrorMessage = a.SelectMany(a1 => a1.ErrorMessages).ToHashSet().JoinAsString("；") });
                foreach (var item in errorDatas)
                {
                    listErrorRows.AddRange(importRowDtos.Where(a => a.ReportId == item.ReportId));
                }

                response.IsSuccess = false;
                response.ErrorCount = listErrorRows.Count();
                response.ErrorDatas = errorDatas;

                //错误数据转换为base64字符串
                using (var memoryStream = new MemoryStream())
                {
                    var config = new OpenXmlConfiguration()
                    {
                        TableStyles = TableStyles.None,
                        AutoFilter = false
                    };
                    memoryStream.SaveAs(listErrorRows, true, "Sheet1", configuration: config);
                    response.ErrorDataFile = Convert.ToBase64String(memoryStream.GetAllBytes());
                }
            }

            //执行导入
            await ImportMealReportAsync(result.ValidRows);
            response.SuccessCount = result.ValidRows.Count();

            return MessageResult.SuccessResult(response);
        }

        /// <summary>
        /// 导入验证后的用餐报告
        /// </summary>
        /// <param name="validRows"></param>
        /// <returns></returns>
        async Task<MessageResult> ImportMealReportAsync(IEnumerable<MealReportImportValidRowDto> validRows)
        {
            if (!validRows.Any())
                return MessageResult.SuccessResult();

            var reportIds = validRows.Select(a => a.ReportId).ToHashSet(StringComparer.CurrentCultureIgnoreCase);
            var queryMealReport = await LazyServiceProvider.LazyGetService<IMealReportReadonlyRepository>().GetQueryableAsync();
            //获取已存在的报告
            var existMealReport = await queryMealReport.Where(a => reportIds.Contains(a.ReportId)).Select(a => new { a.Id, a.ReportId, a.FirstSubmittedDate }).ToArrayAsync();
            var existDatas = existMealReport.GroupBy(a => a.ReportId)
                .Select(a => new { ReportId = a.Key, MinFirstSubmitDate = a.Where(a1 => a1.FirstSubmittedDate.HasValue).Min(a1 => a1.FirstSubmittedDate) })
                .ToDictionary(a => a.ReportId, a => a.MinFirstSubmitDate);

            var mealReportRepository = LazyServiceProvider.LazyGetService<IMealReportRepository>();
            var context = await mealReportRepository.GetDbContextAsync();

            //删除已存在的数据
            if (existMealReport.Length > 0)
            {
                var mealDatas = existMealReport.Select(a =>
                {
                    var mealReport = new MealReport();
                    mealReport.SetId(a.Id);

                    return mealReport;
                });
                await context.BulkDeleteAsync(mealDatas);
            }

            var listMealReports = new List<MealReport>();
            var listCompanies = new HashSet<string>();
            //设置默认值
            var now = DateTime.Now;
            DateTime? minFirstSubmitDate;
            var mealReports = ObjectMapper.Map<IEnumerable<MealReportImportValidRowDto>, IEnumerable<MealReport>>(validRows);
            foreach (var mealReport in mealReports)
            {
                //修改场景
                if (existDatas.TryGetValue(mealReport.ReportId, out minFirstSubmitDate))
                {
                    if (!mealReport.FirstSubmittedDate.HasValue)
                        mealReport.FirstSubmittedDate = minFirstSubmitDate;
                }
                else
                    mealReport.FirstSubmittedDate = now;

                if (string.IsNullOrEmpty(mealReport.ApprovalStatus))
                    mealReport.ApprovalStatus = "Approved";
                if (string.IsNullOrEmpty(mealReport.PaymentStatus))
                    mealReport.PaymentStatus = "Paid";

                mealReport.TransactionDateMask = new DateTime(mealReport.TransactionDate.Value.Year, mealReport.TransactionDate.Value.Month, 1);
                mealReport.SetId(GuidGenerator.Create());
                mealReport.SetCreatorIdAndCreationTime(CurrentUser.Id, now);

                listMealReports.Add(mealReport);

                //收集非空的company，后面要做机构转换配置
                if (!string.IsNullOrEmpty(mealReport.Company))
                    listCompanies.Add(mealReport.Company);
            }

            await context.BulkInsertAsync(listMealReports);

            if (listCompanies.Count > 0)
                await CreateOrgMappingAsync(listCompanies);

            return MessageResult.SuccessResult();
        }

        #endregion
    }
}
