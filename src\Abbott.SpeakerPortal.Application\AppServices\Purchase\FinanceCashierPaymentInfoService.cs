﻿using Abbott.SpeakerPortal.Contracts.Budget.Subbudget;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Utils;
using ClosedXML.Excel;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Abbott.SpeakerPortal.Enums.Purchase;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;
using Abbott.SpeakerPortal.Entities.Purchase.PurPAApplication;
using System.Linq.Dynamic.Core;
using Abbott.SpeakerPortal.Entities.Budget;
using Abbott.SpeakerPortal.Contracts.Budget;
using MiniExcelLibs.OpenXml;
using MiniExcelLibs;
using Microsoft.EntityFrameworkCore;
using Org.BouncyCastle.Crypto.Agreement.JPake;
using Abbott.SpeakerPortal.Enums;
using Volo.Abp.Identity;
using Abbott.SpeakerPortal.Entities.VendorFinancials;
using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.Entities.Integration.Dspot;
using static Abbott.SpeakerPortal.Permissions.SpeakerPortalPermissions;
using Abbott.SpeakerPortal.IntermediateDB.Repositories;
using Microsoft.Extensions.DependencyInjection;
using Abbott.SpeakerPortal.IntermediateDB.Models;
using Microsoft.EntityFrameworkCore.Internal;
using System.Globalization;
using System.Text.RegularExpressions;
using Abbott.SpeakerPortal.Extension;


namespace Abbott.SpeakerPortal.AppServices.Purchase
{
    public class FinanceCashierPaymentInfoService : SpeakerPortalAppService, IFinanceCashierPaymentInfoService
    {
        public async Task<PagedResultDto<GetFinanceCashierPaymentInfoResponse>> GetFinanceCashierPaymentInfosAsync(GetFinanceCashierPaymentInfoRequest request)
        {
            var queryableFinanceCashierPaymentInfo = await LazyServiceProvider.LazyGetService<IFinanceCashierPaymentInfoReadonlyRepository>().GetQueryableAsync();
            var queryPa = await LazyServiceProvider.LazyGetService<IPurPAApplicationReadonlyRepository>().GetQueryableAsync();

            var query = queryableFinanceCashierPaymentInfo.AsNoTracking()
                .Join(queryPa, a => a.PAApplicationCode, a => a.ApplicationCode, (a, b) => new { Fin = a, Pa = b })
                .WhereIf(!string.IsNullOrWhiteSpace(request.PAApplicationCode), a => a.Fin.PAApplicationCode.Contains(request.PAApplicationCode))
                .WhereIf(request.Bu.HasValue, a => a.Fin.ApplyUserBu == request.Bu)
                .WhereIf(request.Company.HasValue, a => a.Fin.CompanyId == request.Company)
                .WhereIf(!string.IsNullOrWhiteSpace(request.VendorName), a => a.Fin.VendorName == request.VendorName)
                .WhereIf(request.ApplyStarDate.HasValue, a => a.Fin.ApplyTime >= request.ApplyStarDate)
                .WhereIf(request.ApplyEndDate.HasValue, a => a.Fin.ApplyTime <= request.ApplyEndDate)
                .WhereIf(request.MPStartDate.HasValue, a => a.Fin.MPDate >= request.MPStartDate)
                .WhereIf(request.MPEndDate.HasValue, a => a.Fin.MPDate <= request.MPEndDate)
                .WhereIf(request.ReturnStartDate.HasValue, a => a.Fin.RetureDate >= request.ReturnStartDate)
                .WhereIf(request.ReturnEndDate.HasValue, a => a.Fin.RetureDate <= request.ReturnEndDate)
                .WhereIf(request.PaymentStartDate.HasValue, a => a.Fin.PaymentDate >= request.PaymentStartDate)
                .WhereIf(request.PaymentEndDate.HasValue, a => a.Fin.PaymentDate <= request.PaymentEndDate)
                .Select(a => new
                {
                    PaApplicationId = a.Pa.Id,
                    a.Fin.PAApplicationCode,
                    a.Fin.ApplyUserName,
                    a.Fin.ApplyUserEmail,
                    a.Fin.ApplyTime,
                    a.Fin.CompanyName,
                    a.Fin.ApplyUserBuName,
                    a.Fin.CostCenterName,
                    a.Fin.VendorName,
                    a.Fin.BankName,
                    a.Fin.BankCardNo,
                    a.Fin.RemoteOrCity,
                    a.Fin.PaymentAmount,
                    a.Fin.RefNo,
                    a.Fin.MPDate,
                    a.Fin.MPStatus,
                    a.Fin.PaymentDate,
                    a.Fin.RetureDate,
                    a.Fin.Remark,
                    a.Fin.CreationTime,
                });

            var count = query.Count();
            var datas = query.OrderByDescending(a => a.CreationTime).PagingIf(request)
                .Select(a => new GetFinanceCashierPaymentInfoResponse
                {
                    PaApplicationId = a.PaApplicationId,
                    PAApplicationCode = a.PAApplicationCode,
                    ApplyUserName = a.ApplyUserName,
                    ApplyUserEmail = a.ApplyUserEmail,
                    ApplyDate = a.ApplyTime.ToString("yyyy-MM-dd"),
                    Company = a.CompanyName,
                    ApplyUserBuName = a.ApplyUserBuName,
                    CostCenterName = a.CostCenterName,
                    VendorName = a.VendorName,
                    BankName = a.BankName,
                    BankCode = a.BankCardNo,
                    RemoteOrCity = a.RemoteOrCity,
                    PaymentAmount = a.PaymentAmount,
                    RefNo = a.RefNo,
                    MPDate = a.MPDate != null ? a.MPDate.Value.ToString("yyyy-MM-dd") : "",
                    MPStatus = a.MPStatus,
                    PaymentDate = a.PaymentDate != null ? a.PaymentDate.Value.ToString("yyyy-MM-dd") : "",
                    RetureDate = a.RetureDate.HasValue ? a.RetureDate.Value.ToString("yyyy-MM-dd") : "",
                    Remark = a.Remark,
                    CreationTime = a.CreationTime,
                }).ToArray();

            var result = new PagedResultDto<GetFinanceCashierPaymentInfoResponse>(count, datas);
            return result;
        }

        public async Task<Stream> ExportFinanceCashierPaymentAsync(GetFinanceCashierPaymentInfoRequest request)
        {
            var queryableFinanceCashierPaymentInfo = await LazyServiceProvider.LazyGetService<IFinanceCashierPaymentInfoReadonlyRepository>().GetQueryableAsync();
            var query = queryableFinanceCashierPaymentInfo.AsNoTracking()
                .WhereIf(!string.IsNullOrWhiteSpace(request.PAApplicationCode), a => a.PAApplicationCode.Contains(request.PAApplicationCode))
                .WhereIf(request.Bu.HasValue, a => a.ApplyUserBu == request.Bu)
                .WhereIf(request.Company.HasValue, a => a.CompanyId == request.Company)
                .WhereIf(!string.IsNullOrWhiteSpace(request.VendorName), a => a.VendorName == request.VendorName)
                .WhereIf(request.ApplyStarDate.HasValue, a => a.ApplyTime >= request.ApplyStarDate)
                .WhereIf(request.ApplyEndDate.HasValue, a => a.ApplyTime <= request.ApplyEndDate)
                .WhereIf(request.MPStartDate.HasValue, a => a.MPDate >= request.MPStartDate)
                .WhereIf(request.MPEndDate.HasValue, a => a.MPDate <= request.MPEndDate)
                .WhereIf(request.ReturnStartDate.HasValue, a => a.RetureDate >= request.ReturnStartDate)
                .WhereIf(request.ReturnEndDate.HasValue, a => a.RetureDate <= request.ReturnEndDate)
                .WhereIf(request.PaymentStartDate.HasValue, a => a.PaymentDate >= request.PaymentStartDate)
                .WhereIf(request.PaymentEndDate.HasValue, a => a.PaymentDate <= request.PaymentEndDate)
                .Select(a => new
                {
                    a.PAApplicationCode,
                    a.ApplyUserName,
                    a.ApplyUserEmail,
                    a.ApplyTime,
                    a.CompanyName,
                    a.ApplyUserBuName,
                    a.CostCenterName,
                    a.VendorName,
                    a.BankName,
                    a.BankCardNo,
                    a.RemoteOrCity,
                    a.PaymentAmount,
                    a.RefNo,
                    a.MPDate,
                    a.MPStatus,
                    a.PaymentDate,
                    a.RetureDate,
                    a.Remark,
                    a.CreationTime,
                });

            var datas = query.OrderByDescending(a => a.CreationTime)
                .Select(a => new GetFinanceCashierPaymentInfoResponse
                {
                    PAApplicationCode = a.PAApplicationCode,
                    ApplyUserName = a.ApplyUserName,
                    ApplyUserEmail = a.ApplyUserEmail,
                    ApplyDate = a.ApplyTime.ToString("yyyy-MM-dd"),
                    Company = a.CompanyName,
                    ApplyUserBuName = a.ApplyUserBuName,
                    CostCenterName = a.CostCenterName,
                    VendorName = a.VendorName,
                    BankName = a.BankName,
                    BankCode = a.BankCardNo,
                    RemoteOrCity = a.RemoteOrCity,
                    PaymentAmount = a.PaymentAmount,
                    RefNo = a.RefNo,
                    MPDate = a.MPDate != null ? a.MPDate.Value.ToString("yyyy-MM-dd") : string.Empty,
                    MPStatus =  a.MPStatus,
                    PaymentDate = a.PaymentDate != null ? a.PaymentDate.Value.ToString("yyyy-MM-dd") : string.Empty,
                    RetureDate = a.RetureDate.HasValue ? a.RetureDate.Value.ToString("yyyy-MM-dd") : string.Empty,
                    Remark = a.Remark,
                    //CreationTime = a.CreationTime,
                }).AsEnumerable();

            MemoryStream stream = new();
            var config = new OpenXmlConfiguration()
            {
                TableStyles = TableStyles.None,
                AutoFilter = false

            };
            stream.SaveAs(datas, true, "Sheet1", configuration: config);
            stream.Seek(0, SeekOrigin.Begin);
            return stream;
        }

        public async Task<MessageResult> ImportFinanceCashierPaymentInfoAsync(List<FinanceCashierPaymentImportMessageDto> request)
        {
            try
            {
                var adjustments = request.Where(x => x.Success
                    && !string.IsNullOrWhiteSpace(x.RefNo)
                    && !string.IsNullOrWhiteSpace(x.PAApplicationCode)
                    && !string.IsNullOrWhiteSpace(x.PaymentDate))
                    .ToList();

                var paApplicationCodes = adjustments.Select(x => x.PAApplicationCode);
                var paRepository = LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>();
                var queryPa = await paRepository.GetQueryableAsync();
                var prRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>();
                var queryPr = await prRepository.GetQueryableAsync();
                var paAndPrList = queryPa.Where(m => paApplicationCodes.Contains(m.ApplicationCode))
                    .GroupJoin(queryPr, pa => pa.PRId, pr => pr.Id, (pa, pr) => new { pa, pr = pr.FirstOrDefault() })
                    .ToList();

                var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
                var applyUserIds = paAndPrList.Select(m => m.pa.ApplyUserId).ToList();
                var applyUsers = queryableUser.Where(m => applyUserIds.Contains(m.Id));
                var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
                var costCenters = await dataverseService.GetCostcentersAsync();

                var vendorIds = paAndPrList.Select(m => m.pa.VendorId).ToList();
                var queryBpcsAvm = await LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync();
                var queryBpcsPmfvm = await LazyServiceProvider.LazyGetService<IBpcsPmfvmRepository>().GetQueryableAsync();
                var amvAndPmfvmList = queryBpcsAvm.Where(a => vendorIds.Contains(a.Id))
                                                  .Join(queryBpcsPmfvm,
                                                      l => new { Vnd = l.Vendor, Cmp = l.Vcmpny },
                                                      r => new { Vnd = r.Vnderx, Cmp = r.Vmcmpy },
                                                      (avm, pmfvm) => new { avm, pmfvm })
                    .ToList();
                var financeCashierPaymentInfos = new List<FinanceCashierPaymentInfo>();
                foreach (var item in adjustments)
                {
                    DateTime? parsePaymentDate = ParseDate(item.PaymentDate);
                    if (parsePaymentDate == null)
                    {
                        return MessageResult.FailureResult($"财务出纳支付导入失败, payment date: {item.PaymentDate} is invalid format!");
                    }

                    DateTime? parseReturnDate = null;
                    if (!string.IsNullOrWhiteSpace(item.ReturnDate))
                    {
                        parseReturnDate = ParseDate(item.ReturnDate);
                        if (parseReturnDate == null)
                        {
                            return MessageResult.FailureResult($"财务出纳支付导入失败, return date: {item.ReturnDate} is invalid format!");
                        }
                    }

                    var paAndPr = paAndPrList.FirstOrDefault(m => m.pa.ApplicationCode == item.PAApplicationCode);
                    if (paAndPr == null || paAndPr.pa == null)
                    {
                        return MessageResult.FailureResult("财务出纳支付导入失败, No found pa, pa number:" + item.PAApplicationCode, ", row number:" + item.No);
                    }

                    if (paAndPr.pr == null)
                    {
                        return MessageResult.FailureResult("财务出纳支付导入失败, No found pr, pa number:" + item.PAApplicationCode, ", row number:" + item.No);
                    }

                    var applyUser = applyUsers.FirstOrDefault(m => m.Id == paAndPr.pa.ApplyUserId);
                    if (applyUser == null)
                    {
                        return MessageResult.FailureResult("财务出纳支付导入失败, No found applyUser, pa number:" + item.PAApplicationCode, ", row number:" + item.No);
                    }

                    var costCenter = costCenters.FirstOrDefault(m => m.Id == paAndPr.pr.CostCenter);
                    if (costCenter == null)
                    {
                        return MessageResult.FailureResult("财务出纳支付导入失败, No found cost center, pa number:" + item.PAApplicationCode, ", row number:" + item.No);
                    }

                    var vendor = amvAndPmfvmList.FirstOrDefault(m => m.avm.Id == paAndPr.pa.VendorId)?.pmfvm;
                    if (vendor == null)
                    {
                        return MessageResult.FailureResult("财务出纳支付导入失败, No found vendor, pa number:" + item.PAApplicationCode, ", row number:" + item.No);
                    }

                    var financeCashierPaymentInfo = new FinanceCashierPaymentInfo
                    {
                        PAApplicationCode = paAndPr.pa.ApplicationCode,
                        ApplyUserId = paAndPr.pa.ApplyUserId,
                        ApplyUserName = paAndPr.pa.ApplyUserName,
                        ApplyUserEmail = applyUser.Email,
                        ApplyTime = paAndPr.pa.ApplyTime,
                        CompanyId = paAndPr.pa.CompanyId,
                        CompanyName = paAndPr.pa.CompanyName,
                        ApplyUserBu = paAndPr.pa.ApplyUserBu,
                        ApplyUserBuName = paAndPr.pa.ApplyUserBuName,
                        CostCenterName = costCenter?.Name,
                        VendorName = paAndPr.pa.VendorName,
                        BankName = vendor?.Vldrm1,
                        BankCardNo = vendor?.Vldrm2,
                        RemoteOrCity = vendor?.Vldcd1 == "51" ? "同城" : (vendor?.Vldcd1 == "52" ? "异地" : ""),
                        PaymentAmount = paAndPr.pa.PayTotalAmount,
                        RefNo = item.RefNo,
                        // TODO
                        MPDate = null,
                        MPStatus = "",
                        PaymentDate = parsePaymentDate,
                        RetureDate = parseReturnDate,
                        Remark = item.Remark
                    };

                    financeCashierPaymentInfos.Add(financeCashierPaymentInfo);
                }
                var paQueryable = await paRepository.GetQueryableAsync();

                List<string> paymentSuccessList = financeCashierPaymentInfos.Where(x => x.PaymentDate.HasValue && !x.RetureDate.HasValue).Select(x => x.PAApplicationCode).ToList();
                List<string> paymentFailedList = financeCashierPaymentInfos.Where(x => x.RetureDate.HasValue).Select(x => x.PAApplicationCode).ToList();
                var updatePaList = new List<PurPAApplication>();
                if (paymentSuccessList.Any())
                {
                    var pendingPaymentSuccessPaList = paQueryable.Where(x => paymentSuccessList.Contains(x.ApplicationCode)).ToList();
                    pendingPaymentSuccessPaList.ForEach(x => x.Status = PurPAStatus.PurPAApplicationStatus.Paid);
                    updatePaList.AddRange(pendingPaymentSuccessPaList);
                }

                if (paymentFailedList.Any())
                {
                    var pendingPaymentFailedPaList = paQueryable.Where(x => paymentFailedList.Contains(x.ApplicationCode)).ToList();
                    pendingPaymentFailedPaList.ForEach(x => x.Status = PurPAStatus.PurPAApplicationStatus.PaymenFailed);
                    updatePaList.AddRange(pendingPaymentFailedPaList);
                }

                if (updatePaList.Any())
                {
                    await paRepository.UpdateManyAsync(updatePaList);
                }

                var financeRepository = LazyServiceProvider.LazyGetService<IFinanceCashierPaymentInfoRepository>();
                await financeRepository.InsertManyAsync(financeCashierPaymentInfos);
                return MessageResult.SuccessResult("财务出纳支付导入成功, 共导入" + financeCashierPaymentInfos.Count + "条记录!");
            }
            catch (Exception ex)
            {
                return MessageResult.FailureResult("财务出纳支付导入失败, 错误信息:" + ex.Message);
            }
        }

        public async Task<MessageResult> ValidateFinanceCashierPaymentInfoAsync(List<FinanceCashierPaymentImportDto> Rows)
        {
            var refNos = Rows.Select(a => a.RefNo);
            var queryPaRefRepository = LazyServiceProvider.LazyGetService<IPurPAApplicationRefRepository>();
            var queryPaRef = (await queryPaRefRepository.GetQueryableAsync()).AsNoTracking();
            var paRefs = queryPaRef.Where(m => refNos.Contains(m.RefNo));
            var paRefsDic = paRefs.ToDictionary(m => m.RefNo, m => m);

            var paApplicationCodes = paRefs.Select(m => m.PAApplicationCode).ToList();
            var paRepository = LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>();
            var queryPa = await paRepository.GetQueryableAsync();
            var paList = queryPa.Where(m => paApplicationCodes.Contains(m.ApplicationCode)).ToList();

            var vendorIds = paList.Select(m => m.VendorId).ToList();
            var queryBpcsAvm = await LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync();
            var queryBpcsPmfvm = await LazyServiceProvider.LazyGetService<IBpcsPmfvmRepository>().GetQueryableAsync();
            var amvAndPmfvmList = queryBpcsAvm.Where(a => vendorIds.Contains(a.Id))
                .Join(queryBpcsPmfvm,
                    l => new { Vnd = l.Vendor, Cmp = l.Vcmpny },
                    r => new { Vnd = r.Vnderx, Cmp = r.Vmcmpy },
                    (avm, pmfvm) => new { avm, pmfvm })
                .ToList();

            var allRows = new List<FinanceCashierPaymentImportMessageDto>();
            IList<FinanceCashierPaymentImportMessageDto> errormsg = [];
            IList<FinanceCashierPaymentImportMessageDto> successmsg = [];
            int i = 6;
            
            foreach (var item in Rows)
            {
                ++i;
                var message = "";
                bool success = true;
                PurPAApplication pa = null;
                BpcsPmfvm vendor = null;
                if (string.IsNullOrWhiteSpace(item.RefNo))
                {
                    message += $"Reference number不得为空;";
                    success = false;
                }
                else if (allRows.Exists(x => x.RefNo == item.RefNo))
                {
                    message += $"Reference number重复上传;";
                    success = false;
                }
                else if (!paRefsDic.ContainsKey(item.RefNo))
                {
                    message += $"Reference number匹配失败;";
                    success = false;
                }
                else
                {
                    pa = paList.SingleOrDefault(x => x.ApplicationCode == paRefsDic[item.RefNo].PAApplicationCode);
                    vendor = amvAndPmfvmList.SingleOrDefault(x => pa.VendorId.HasValue && x.avm.Id == pa.VendorId)?.pmfvm;
                    if (pa == null)
                    {
                        message += $"PA number匹配失败;";
                        success = false;
                    }

                    if (vendor == null)
                    {
                        message += $"供应商匹配失败;";
                        success = false;
                    }
                }

                string paymentDate = "";
                if (string.IsNullOrWhiteSpace(item.PaymentDate))
                {
                    message += $"支付时间不得为空;";
                    success = false;
                }
                else
                {
                    var parsePaymentDate = ParseDate(item.PaymentDate);
                    if (parsePaymentDate == null)
                    {
                        message += $"支付时间需要为日期格式;";
                        success = false;
                    }
                    else
                    {
                        paymentDate = parsePaymentDate?.ToString("yyyy/MM/dd");
                    }
                }

                string returnDate = "";
                if (!string.IsNullOrWhiteSpace(item.ReturnDate))
                {
                    var parseReturnDate = ParseDate(item.ReturnDate);
                    if (parseReturnDate == null)
                    {
                        message += $"退单时间需要为日期格式;";
                        success = false;
                    }
                    else
                    {
                        returnDate = parseReturnDate?.ToString("yyyy/MM/dd");
                    }
                }

                if (item.Remark.Length > 500)
                {
                    message += $"备注不得超过500字;";
                    success = false;
                }

                FinanceCashierPaymentImportMessageDto cashier = new()
                {
                    No = i,
                    RefNo = item.RefNo,
                    PaymentDate = paymentDate,
                    ReturnDate = returnDate,
                    Remark = item.Remark,
                    PAApplicationCode = pa?.ApplicationCode ?? "",
                    VendorName = pa?.VendorName ?? "",
                    PaymentAmount = pa?.PayTotalAmount ?? 0,
                    BankName = vendor?.Vldrm1 ?? "",
                    BankCardNo = vendor?.Vldrm2 ?? "",
                    RemoteOrCity = vendor?.Vldcd1 == "51" ? "同城" : (vendor?.Vldcd1 == "52" ? "异地" : ""),
                    Success = success,
                    Message = message
                };

                allRows.Add(cashier);

                if (!string.IsNullOrWhiteSpace(message))
                {
                    message = $"第{i}行：{message}";
                    errormsg.Add(cashier);
                    
                }
                else
                    successmsg.Add(cashier);
            }

            if (errormsg.Count > 0 || successmsg.Count == 0)
            {
                var excelHeaders = new  { RefNo = "Reference Number" , PaymentDate= "支付时间", ReturnDate="退单时间", Remark="备注", PAApplicationCode="PA单号", VendorName= "供应商名称", PaymentAmount= "支付金额", BankName="支付银行", BankCardNo= "支付银行账号", RemoteOrCity= "同城/异地", Success= "导入结果", Message="错误信息" };
                var errorData = errormsg.Select(
                    e => new 
                    {
                        e.RefNo,
                        e.PaymentDate,
                        e.ReturnDate,
                        e.Remark,
                        e.PAApplicationCode,
                        e.VendorName,
                        PaymentAmount=e.PaymentAmount.ToString(),
                        e.BankName,
                        e.BankCardNo,
                        e.RemoteOrCity,
                        Success=e.Success==true?"成功":"失败",
                        e.Message
                    }).ToList();
                errorData.Insert(0, excelHeaders);
                string base64Excel = string.Empty;
                using(var memoryStream = new MemoryStream())
                {
                    var config = new OpenXmlConfiguration()
                    {
                        TableStyles = TableStyles.None,
                        AutoFilter = false

                    };
                    memoryStream.SaveAs(errorData, false, "Sheet1", configuration: config);
                    base64Excel = Convert.ToBase64String(memoryStream.ToArray());
                }
                return MessageResult.SuccessResult(new ImportDataResponseDto<FinanceCashierPaymentImportMessageDto>(errormsg, false, base64Excel));
            }
            return MessageResult.SuccessResult(new ImportDataResponseDto<FinanceCashierPaymentImportMessageDto>(successmsg, true));
        }

        private DateTime? ParseDate(string dateString)
        {
            string[] dateFormates = {
                "yyyy/MM/dd",
                "yyyy/MM/d",
                "yyyy/M/dd",
                "yyyy/M/d",
                //"dd/MM/yyyy", 
                //"dd/M/yyyy", 
                //"d/MM/yyyy", 
                //"d/M/yyyy", 
                //"MM/dd/yyyy",
                //"MM/d/yyyy",
                //"M/dd/yyyy",
                //"M/d/yyyy",
                //"yyyy-MM-dd",
                //"yyyy/MM/dd h:mm:ss tt",
                //"yyyy/MM/d h:mm:ss tt",
                //"yyyy/M/dd h:mm:ss tt",
                //"yyyy/M/d h:mm:ss tt",
                //"MM/dd/yyyy h:mm:ss tt", 
                //"MM/d/yyyy h:mm:ss tt", 
                //"M/d/yyyy h:mm:ss tt", 
                //"M/dd/yyyy h:mm:ss tt", 
                //"dd/MM/yyyy h:mm:ss tt", 
                //"dd/M/yyyy h:mm:ss tt", 
                //"d/MM/yyyy h:mm:ss tt", 
                //"d/M/yyyy h:mm:ss tt" 
            };
            DateTime dt;

            if (DateTime.TryParseExact(dateString, dateFormates, CultureInfo.InvariantCulture, DateTimeStyles.None, out dt))
            {
                return dt;
            }

            return null; // 无法解析的日期
        }
    }
}
