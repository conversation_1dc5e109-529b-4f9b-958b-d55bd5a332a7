SELECT
 TRY_CONVERT(UNIQUE<PERSON>ENTIFIER, ISNULL([ID],'********-0000-0000-0000-************'))[ID]
,[PAApplicationCode]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([ApplyUserId],'********-0000-0000-0000-************'))[ApplyUserId]
,[ApplyUserName]
,[ApplyUserEmail]
,ISNULL(GETDATE(),[ApplyTime])[ApplyTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([CompanyId],'********-0000-0000-0000-************'))[CompanyId]
,[CompanyName]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([ApplyUserBu],'********-0000-0000-0000-************'))[ApplyUserBu]
,[ApplyUserBuName]
,[CostCenterName]
,[VendorName]
,[BankName]
,[BankCardNo]
,[RemoteOrCity]
,[PaymentAmount]
,[RefNo]
,CASE WHEN [MPDate] = 'NULL' THEN NULL ELSE [MPDate] END AS [MPDate]
,[MPStatus]
,CAST ([PaymentDate] AS datetime2)[PaymentDate]
,IIF([RetureDate] = '',NULL,[RetureDate])[RetureDate]
,[Remark]
,[ExtraProperties]
,[ConcurrencyStamp]
,CAST ([CreationTime] AS datetime2)[CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([ApplyUserBu],'********-0000-0000-0000-************'))[CreatorId]
,CASE WHEN [LastModificationTime] = 'NULL' THEN NULL ELSE [LastModificationTime] END AS [LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([ApplyUserBu],'********-0000-0000-0000-************'))[LastModifierId]
,[IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([ApplyUserBu],'********-0000-0000-0000-************'))[DeleterId]
,CASE WHEN [DeletionTime] = 'NULL' THEN NULL ELSE [DeletionTime] END AS [DeletionTime]
INTO #FinanceCashierPaymentInfos
FROM PLATFORM_ABBOTT_STG.dbo.FinanceCashierPaymentInfos

--DROP TABLE #FinanceCashierPaymentInfos
USE Speaker_Portal_STG;

UPDATE a
SET
 a.[PAApplicationCode] = b.[PAApplicationCode]
,[ApplyUserId] = b.[ApplyUserId]
,[ApplyUserName] = b.[ApplyUserName]
,[ApplyUserEmail] = b.[ApplyUserEmail]
,[ApplyTime] = b.[ApplyTime]
,[CompanyId] = b.[CompanyId]
,[CompanyName] = b.[CompanyName]
,[ApplyUserBu] = b.[ApplyUserBu]
,[ApplyUserBuName] = b.[ApplyUserBuName]
,[CostCenterName] = b.[CostCenterName]
,[VendorName] = b.[VendorName]
,[BankName] = b.[BankName]
,[BankCardNo] = b.[BankCardNo]
,[RemoteOrCity] = b.[RemoteOrCity]
,[PaymentAmount] = b.[PaymentAmount]
,[RefNo] = b.[RefNo]
,[MPDate] = b.[MPDate]
,[MPStatus] = b.[MPStatus]
,[PaymentDate] = b.[PaymentDate]
,[RetureDate] = b.[RetureDate]
,[Remark] = b.[Remark]
,[ExtraProperties] = b.[ExtraProperties]
,[ConcurrencyStamp] = b.[ConcurrencyStamp]
,[CreationTime] = b.[CreationTime]
,[CreatorId] = b.[CreatorId]
,[LastModificationTime] = b.[LastModificationTime]
,[LastModifierId] = b.[LastModifierId]
,[IsDeleted] = b.[IsDeleted]
,[DeleterId] = b.[DeleterId]
,[DeletionTime] = b.[DeletionTime]
FROM dbo.FinanceCashierPaymentInfos a
left join #FinanceCashierPaymentInfos  b
ON a.id=b.id

INSERT INTO dbo.FinanceCashierPaymentInfos
SELECT
 [ID]
,[PAApplicationCode]
,[ApplyUserId]
,[ApplyUserName]
,[ApplyUserEmail]
,[ApplyTime]
,[CompanyId]
,[CompanyName]
,[ApplyUserBu]
,[ApplyUserBuName]
,[CostCenterName]
,[VendorName]
,[BankName]
,[BankCardNo]
,[RemoteOrCity]
,[PaymentAmount]
,[RefNo]
,[MPDate]
,[MPStatus]
,[PaymentDate]
,[RetureDate]
,[Remark]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
FROM (SELECT *,ROW_NUMBER() OVER(PARTITION BY Id ORDER BY Id) RK FROM #FinanceCashierPaymentInfos)a
WHERE RK = 1 and not exists (select * from dbo.FinanceCashierPaymentInfos where id= a.id)

--TRUNCATE TABLE dbo.FinanceCashierPaymentInfos;