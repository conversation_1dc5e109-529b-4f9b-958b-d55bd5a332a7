﻿using Abbott.SpeakerPortal.Domain.Shared.Models;
using System;
using System.Collections.Generic;
using System.Text;
using System.Text.Json.Serialization;

namespace Abbott.SpeakerPortal.Contracts.Integration.EpdOlineMeeting
{
    public class NexBpmMeetingStatusUpdateRequestDto
    {
        /// <summary>
        /// PR单号 e.g.P2008270001
        /// </summary>
        [JsonPropertyName("serialNumberPr")]
        public string SerialNumberPr { get; set; }

        /// <summary>
        /// 状态 e.g.1001-激活/1002-结算
        /// </summary>
        [JsonPropertyName("status")]
        public string Status { get; set; }

        public static MessageResult Validate(NexBpmMeetingStatusUpdateRequestDto request)
        {
            StringBuilder sb = new StringBuilder();

            if (request == null)
            {
                return MessageResult.FailureResult("Argument null!");
            }

            if (string.IsNullOrWhiteSpace(request.SerialNumberPr))
            {
                sb.AppendLine("Empty pr number!");
            }

            if (string.IsNullOrWhiteSpace(request.Status))
            {
                sb.AppendLine("Empty status!");
            }
            else if (request.Status != "1001" && request.Status != "1002" && request.Status != "1000")
            {
                sb.AppendLine("Invalid status, only support:1000-推送,1001-激活,1002-结算!");
            }

            if (sb.Length > 0)
            {
                return MessageResult.FailureResult(sb.ToString());
            }

            return MessageResult.SuccessResult();
        }
    }
}
