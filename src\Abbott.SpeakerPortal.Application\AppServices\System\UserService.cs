﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;

using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.Budget;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Entities.UserRoles;
using Abbott.SpeakerPortal.Role;
using Abbott.SpeakerPortal.RoleDto;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

using Volo.Abp.Application.Dtos;
using Volo.Abp.Data;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using static Volo.Abp.Identity.IdentityPermissions;
using Abbott.SpeakerPortal.Contracts.Common;
using System.Text.Json;
using Abbott.SpeakerPortal.Contracts.Dataverse;
using DocumentFormat.OpenXml.Wordprocessing;
using DocumentFormat.OpenXml.Office2010.Excel;
using Microsoft.AspNetCore.Mvc;
using Abbott.SpeakerPortal.Redis;
using Abbott.SpeakerPortal.Contracts.System.User;
using Abbott.SpeakerPortal.Entities.UserRoles.UserExtension;
using EFCore.BulkExtensions;

namespace Abbott.SpeakerPortal.User
{
    public class UserService : SpeakerPortalAppService, IUserService
    {
        private IIdentityUserRepository _identityUserRepository;
        private IIdentityRoleRepository _identityRoleRepository;
        private readonly IUserRoleRepository _userRoleRepository;
        private readonly IDataverseService _dataverseService;
        private readonly IRoleService _roleService;
        private readonly IdentityUserManager _identityUserManager;
        private static List<DepartmentDto> ParentDepartList = new List<DepartmentDto>();
        private static List<string> ParentIdList = new List<string>();
        private IServiceProvider _serviceProvider;
        private readonly IdentityRoleManager _identityRoleManager;

        public UserService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _identityUserRepository = serviceProvider.GetService<IIdentityUserRepository>();
            _identityRoleRepository = serviceProvider.GetService<IIdentityRoleRepository>();
            _userRoleRepository = serviceProvider.GetService<IUserRoleRepository>();
            _dataverseService = serviceProvider.GetService<IDataverseService>();
            _roleService = serviceProvider.GetService<IRoleService>();
            _identityUserManager = serviceProvider.GetService<IdentityUserManager>();
            _identityRoleManager = serviceProvider.GetService<IdentityRoleManager>();
        }

        public async Task<bool> ActiveUser(Guid id, bool isActive)
        {
            var user = await _identityUserRepository.FindAsync(id);
            if (user == null)
                return false;
            user.SetIsActive(isActive);
            await _identityUserRepository.UpdateAsync(user);
            //日志
            var dict = new Dictionary<string, string>
            {
                { "UserName", CurrentUser?.UserName },
                { "Date", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fffffff") },
                { "isActive",isActive.ToString()},
            };
            LazyServiceProvider.LazyGetService<IApplicationInsightService>().TrackEncryptEvent(ApplicationInsightEventNames.UserChange, dict);
            return true;
        }

        public async Task<MessageResult> CreateUser(CreateUserRequestDto createUserRequestDto)
        {
            var user = await GetUserByStaffCode(createUserRequestDto.StaffCode);
            if (user == null)
                return MessageResult.FailureResult($"该用户不存在员工主数据配置中, 请检查后重试。staff Code : {createUserRequestDto.StaffCode}");
            if (string.IsNullOrEmpty(user.Email))
            {
                return MessageResult.FailureResult("用户邮箱不能为空, 请检查员工主数据中邮箱配置");
            }
            var _userRepository = _serviceProvider.GetService<IIdentityUserRepository>();
            var existDbUser = await _userRepository.FindAsync(user.Id);
            if (existDbUser != null)
            {
                return MessageResult.FailureResult($"该用户已存在, 请添加新用户。staff code : {createUserRequestDto.StaffCode}");
            }
            var authorizedBudgetBu = new List<BdAuthorizedBudgetBu>();
            var authorizedBudgetBuRepository = LazyServiceProvider.LazyGetService<IBdAuthorizedBudgetBuRepository>();
            if (createUserRequestDto.AuthorizedBudgetBus?.Count > 0)
            {
                authorizedBudgetBu = createUserRequestDto.AuthorizedBudgetBus.Select(s => new BdAuthorizedBudgetBu { BuId = s.BuId, BuName = s.BuName, UserId = user.Id }).ToList();
            }
            var entity = new IdentityUser(user.Id, user.StaffAccount, user.Email);
            entity.Name = user.Name;
            entity.SetProperty(EntityConsts.IdentityUser.StaffCode, user.StaffCode);
            entity.SetProperty(EntityConsts.IdentityUser.MainDepartmentId, createUserRequestDto.MainDepartmentId);
            entity.SetProperty(EntityConsts.IdentityUser.JobStatus, user.JobStatus);
            //entity.SetProperty(EntityConsts.IdentityUser.AuthorizedBudgetBuId, authorizedBudgetBuId);
            await _identityUserRepository.InsertAsync(entity);
            List<UserRoleDto> userRoleDtos = new List<UserRoleDto>();
            if (createUserRequestDto?.RoleIds?.Any() == true)
            {
                foreach (var rol in createUserRequestDto.RoleIds)
                {
                    var userRoleDto = new UserRoleDto { userId = entity.Id, roleId = rol };
                    userRoleDtos.Add(userRoleDto);
                }
            }

            if (userRoleDtos?.Any() == true)
            {
                var userRoleEntity = ObjectMapper.Map<List<UserRoleDto>, List<IdentityUserRole>>(userRoleDtos);
                await _userRoleRepository.InsertManyAsync(userRoleEntity, true);
            }
            if (authorizedBudgetBu.Count != 0)
            {
                await authorizedBudgetBuRepository.InsertManyAsync(authorizedBudgetBu);
            }

            var dict = new Dictionary<string, string>
            {
                { "UserName", CurrentUser?.UserName },
                { "Date", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fffffff") },
                { "ChangeContent", JsonSerializer.Serialize(createUserRequestDto) },
            };
            LazyServiceProvider.LazyGetService<IApplicationInsightService>().TrackEncryptEvent(ApplicationInsightEventNames.UserChange, dict);
            return MessageResult.SuccessResult(true);
        }

        public async Task<bool> EditUser(EditUserRequestDto editUserRequestDto)
        {
            var userRepository = _serviceProvider.GetService<IRepository<IdentityUser>>();
            var roleRepository = _serviceProvider.GetService<IRepository<IdentityRole>>();
            var user = await userRepository.FirstOrDefaultAsync(a => EF.Property<string>(a, EntityConsts.IdentityUser.StaffCode) == editUserRequestDto.StaffCode);
            if (user == null)
                return false;

            user.SetIsActive(editUserRequestDto.IsActive);
            user.SetProperty(EntityConsts.IdentityUser.MainDepartmentId, editUserRequestDto.MainDepartmentId);
            var authorizedBudgetBuRepository = LazyServiceProvider.LazyGetService<IBdAuthorizedBudgetBuRepository>();

            //物理删除数据
            await authorizedBudgetBuRepository.HardDeleteAsync(x => x.UserId == user.Id, true);
            //await _userRoleRepository.DeleteDirectAsync(a => a.UserId == user.Id);
            await userRepository.UpdateAsync(user);
            //_roleService.GetRoleUserList(re)

            //var existRoleList = new List<string>();
            // 设置用户的角色

            //role update 
            if (editUserRequestDto.RoleIds != null && editUserRequestDto.RoleIds.Any())
            {
                var roleQuery = await roleRepository.GetQueryableAsync();
                var newRoles = await roleQuery.Where(m => editUserRequestDto.RoleIds.Contains(m.Id)).Select(s => s.Name).ToListAsync();
                //await _identityUserManager.SetRolesAsync(user, newRoles);
                //var difference = existRoleList.Except(newRoles).ToList();
                //if (difference.Count > 0)
                //{
                //    await _identityUserManager.RemoveFromRolesAsync(user, difference);
                //}
                //var difference2 = newRoles.Except(existRoleList).ToList();
                //if (difference2.Count > 0)
                //{
                //    await _identityUserManager.SetRolesAsync(user, difference2);
                //}
                //await _identityUserManager.SetRolesAsync(user, newRoles);

                var oldRoles = await _identityUserManager.GetRolesAsync(user);
                await _identityUserManager.RemoveFromRolesAsync(user, oldRoles);

                await _identityUserManager.AddToRolesAsync(user, newRoles);

                //var userRoleDtos = editUserRequestDto.RoleIds.Select(a => new UserRoleDto { userId = user.Id, roleId = a });
                //var userRoleEntity = ObjectMapper.Map<IEnumerable<UserRoleDto>, IEnumerable<IdentityUserRole>>(userRoleDtos);

                //await _userRoleRepository.InsertManyAsync(userRoleEntity, true);
            }
            else
            {
                // 移除用户的所有角色
                var existRoleList = await _identityUserManager.GetRolesAsync(user);
                await _identityUserManager.RemoveFromRolesAsync(user, existRoleList);
            }

            if (editUserRequestDto.AuthorizedBudgetBus?.Count > 0)
            {
                var authorizedBudgetBu = editUserRequestDto.AuthorizedBudgetBus?.Select(s => new BdAuthorizedBudgetBu { BuId = s.BuId, BuName = s.BuName, UserId = user.Id }).ToList();
                await authorizedBudgetBuRepository.InsertManyAsync(authorizedBudgetBu, true);
            }
            //日志
            var dict = new Dictionary<string, string>
            {
                { "UserName", CurrentUser?.UserName },
                { "Date", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fffffff") },
                { "ChangeContent", JsonSerializer.Serialize(editUserRequestDto)},
            };
            LazyServiceProvider.LazyGetService<IApplicationInsightService>().TrackEncryptEvent(ApplicationInsightEventNames.UserChange, dict);
            return true;
        }

        public async Task<GerUserResponseDto> GetUserByStaffCode(string staffCode)
        {
            GerUserResponseDto user = default;
            var authorizedBudgetBuRepository = LazyServiceProvider.LazyGetService<IBdAuthorizedBudgetBuRepository>();
            var queryAuthorizedBudgetBu = await authorizedBudgetBuRepository.GetQueryableAsync();
            var firstdata = await _serviceProvider.GetService<IRepository<IdentityUser>>().FirstOrDefaultAsync(a => EF.Property<string>(a, EntityConsts.IdentityUser.StaffCode) == staffCode);
            if (firstdata != null)
            {
                var _userRoleRepository = _serviceProvider.GetService<IRepository<IdentityUserRole>>();
                var userRoleQueryable = await _userRoleRepository.GetQueryableAsync();
                var queryAuthorizedBudgetBuDatas = queryAuthorizedBudgetBu.Where(m => m.UserId == firstdata.Id).Select(s => s.BuId).ToList();
                var roleIds = userRoleQueryable.Where(p => p.UserId == firstdata.Id).Select(a => a.RoleId).AsEnumerable();
                var mainDeptId = firstdata.GetProperty<Guid?>(EntityConsts.IdentityUser.MainDepartmentId);
                user = new GerUserResponseDto()
                {
                    Id = firstdata.Id,
                    Name = firstdata.Name,
                    Email = firstdata.Email,
                    IsActive = firstdata.IsActive,
                    MainDepartment = GetDepartmentById(mainDeptId?.ToString()).Result?.DepartmentName,
                    RoleIds = roleIds,
                    MainDepartmentId = mainDeptId,
                    DepartmentList = await GetDepartmentListByStaffId(firstdata.Id),
                    Department = firstdata.GetProperty<string>(EntityConsts.IdentityUser.DepartmentId),
                    AuthorizedBudgetBus = queryAuthorizedBudgetBuDatas,
                    JobStatus = firstdata.GetProperty<int>("JobStatus"),
                };
            }
            else
            {
                var staffs = await _dataverseService.GetStaffs($"*\\{staffCode}");
                if (staffs.Count() == 0) return user;
                var data = staffs.First();
                var departList = await GetDepartmentListByStaffId(data.Id);
                //var queryAuthorizedBudgetBuDatas = queryAuthorizedBudgetBu.Where(m => m.UserId == data.Id).Select(s => new Bu { BuId = s.BuId, BuName = s.BuName }).ToList();
                user = new GerUserResponseDto
                {
                    Id = data.Id,
                    StaffCode = data.StaffCode,
                    Name = data.Name,
                    Email = data.Email,
                    DepartmentList = departList,
                    JobStatus = data.JobStatus == Enums.DataverseEnums.Staff.JobStatus.Incumbency ? 1 : 0,
                    IsActive = data.IsActive != 0,
                    StaffAccount = data.StaffAccount,
                    //AuthorizedBudgetBus = queryAuthorizedBudgetBuDatas,
                };
            }

            return user;
        }

        public async Task<List<DepartmentDto>> GetUserDepartmentList(Guid id)
        {
            var user = await _identityUserRepository.FindAsync(id);
            var departmentId = user.GetProperty<string>("DepartmentId");
            var departList = new List<DepartmentDto>();
            if (user != null)
            {
                if (string.IsNullOrEmpty(departmentId))
                {
                    departList = await GetDepartmentListByStaffId(user.Id);
                }
                else
                {
                    var departmentIds = departmentId.Split(',');
                    foreach (var item in departmentIds)
                    {
                        var department = await GetDepartmentById(item);
                        departList.Add(department);
                    }
                }

            }

            return departList;
        }

        public async Task<PagedResultDto<UserListResponseDto>> GetUserList(UserListRequestDto userListRequestDto)
        {
            return await GetUserListNew(userListRequestDto);
        }
        private async Task<PagedResultDto<UserListResponseDto>> GetUserListNew(UserListRequestDto userListRequestDto)
        {
            var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var queryableRole = await LazyServiceProvider.LazyGetService<IRepository<IdentityRole>>().GetQueryableAsync();
            var queryableUserRole = await LazyServiceProvider.LazyGetService<IRepository<IdentityUserRole>>().GetQueryableAsync();
            var query = queryableUser
                .Where(a => !string.IsNullOrEmpty(EF.Property<string>(a, "StaffCode")))
                .Include(m => m.Roles)
                .WhereIf(!string.IsNullOrWhiteSpace(userListRequestDto.StaffCode), m => EF.Property<string>(m, "StaffCode").Contains(userListRequestDto.StaffCode))
                .WhereIf(!string.IsNullOrWhiteSpace(userListRequestDto.Name), m => m.Name.Contains(userListRequestDto.Name))
                .WhereIf(userListRequestDto.Department.HasValue, m => EF.Property<Guid>(m, "MainDepartmentId") == userListRequestDto.Department)
                .WhereIf(!string.IsNullOrWhiteSpace(userListRequestDto.Email),m=>m.Email.Contains(userListRequestDto.Email))
                .SelectMany(a => a.Roles.DefaultIfEmpty(), (a, b) => new { user = a, RoleId = b.RoleId })
                .GroupJoin(queryableRole, a => a.RoleId, b => b.Id, (a, b) => new { user = a.user, roles = string.Join(",", b.Select(s => EF.Property<string>(s, "DisplayName"))) })
                .GroupBy(a => a.user.Id, (key, val) => new
                {
                    Id = key,
                    StaffCode = EF.Property<string>(val.First().user, "StaffCode"),
                    UserName = val.First().user.UserName,
                    Name = val.First().user.Name,
                    Email = val.First().user.Email,
                    JobStatus = EF.Property<int?>(val.First().user, "JobStatus"),
                    Roles = val.Select(s => s.roles).JoinAsString(","),
                    MainDeptId = EF.Property<Guid?>(val.First().user, "MainDepartmentId"),
                    IsActive = val.First().user.IsActive,
                    LastModificationTime = val.First().user.LastModificationTime,
                });
            var count = await query.CountAsync();
            query = query.OrderByDescending(a => a.StaffCode).Skip(userListRequestDto.PageIndex * userListRequestDto.PageSize).Take(userListRequestDto.PageSize);

            //get所有部门
            var departmentList = await _dataverseService.GetOrganizations(stateCode: null);
            var datas = new List<UserListResponseDto>();


            await query.ForEachAsync(async s =>
             {
                 var User = new UserListResponseDto
                 {
                     Id = s.Id,
                     UserName = s.UserName,
                     LastModificationTime = s.LastModificationTime,
                     IsActive = s.IsActive,
                     StaffCode = s.StaffCode,
                     Name = s.Name,
                     Email = s.Email,
                     MainDepartment = string.Join('-', await GetDeptTree(departmentList, s.MainDeptId)),
                     Role = s.Roles,
                     JobStatus = s.JobStatus,
                 };
                 datas.Add(User);
             });
            return new PagedResultDto<UserListResponseDto>()
            {
                Items = datas,
                TotalCount = count,
            };
        }

        /// <summary>
        /// get所有部门，用树形结构返回
        /// </summary>
        /// <param name="departmentId"></param>
        /// <returns></returns>
        public async Task<List<DepartmenTreeDto>> GetDepartmentList(string departmentId = "")
        {
            var departmentList = await _dataverseService.GetOrganizations();
            Guid? parentId = null;
            if (!string.IsNullOrWhiteSpace(departmentId))
            {
                Guid id = Guid.NewGuid();
                if (Guid.TryParse(departmentId, out id))
                    parentId = id;
            }
            var data = BulidTreeByRecursive(departmentList, new List<DepartmenTreeDto>(), parentId);
            if (parentId.HasValue)
            {
                List<DepartmenTreeDto> rsult = new List<DepartmenTreeDto>();
                var departmentFirst = departmentList.FirstOrDefault(a => a.Id == parentId);
                rsult.Add(new DepartmenTreeDto
                {
                    Id = departmentFirst.Id,
                    ParentDepartmentId = !departmentFirst.ParentDepartment.HasValue ? null : departmentFirst.ParentDepartment,
                    Name = departmentFirst.DepartmentName,
                    Children = data
                });
                return rsult;
            }
            return data;
        }

        #region 私有方法

        /// <summary>
        /// 根据员工Id获取关联部门
        /// </summary>
        /// <param name="staffId"></param>
        /// <returns></returns>
        async Task<List<DepartmentDto>> GetDepartmentListByStaffId(Guid staffId)
        {
            var relations = await _dataverseService.GetStaffDepartmentRelations(staffId.ToString());
            var department = await _dataverseService.GetOrganizations();
            var result = relations.Join(department, a => a.DepartmentId, b => b.Id, (a, b) => b).ToList();
            //var result = new List<DepartmentDto>();
            //foreach (var item in relations)
            //{
            //    // var department = await _dataverseService.GetOrganizations(item.DepartmentId.ToString());
            //    result.AddRange(department);
            //}
            return result;
        }

        /// <summary>
        /// 递归返回树形结构的部门
        /// </summary>
        private List<DepartmenTreeDto> BulidTreeByRecursive(IEnumerable<DepartmentDto> departList, List<DepartmenTreeDto> departTrees, Guid? parentId = null)
        {
            departTrees = new List<DepartmenTreeDto>();
            try
            {
                List<DepartmentDto> tempList = departList.Where(c => (!parentId.HasValue && !c.ParentDepartment.HasValue) || (parentId.HasValue && c.ParentDepartment == parentId)).ToList();

                for (int i = 0; i < tempList.Count; i++)
                {
                    var dto = new DepartmenTreeDto();
                    dto.Id = tempList[i].Id;
                    dto.ParentDepartmentId = !tempList[i].ParentDepartment.HasValue ? null : tempList[i].ParentDepartment;
                    dto.Name = tempList[i].DepartmentName;
                    dto.Children = BulidTreeByRecursive(departList, departTrees, dto.Id);
                    departTrees.Add(dto);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
            }
            return departTrees;
        }

        /// <summary>
        /// 组合成EPD-FX-Finance这样的格式
        /// </summary>
        /// <param name="department">The department.</param>
        /// <returns></returns>
        private string GetDepartmentString(List<DepartmentDto> ParentDepartList)
        {
            string str = "";
            foreach (var item in ParentDepartList)
            {
                str += $"{item.DepartmentName}-";
            }
            str = str.TrimEnd('-');
            return str;
        }

        /// <summary>
        /// Gets departmentId所在Department和父节点Department
        /// </summary>
        /// <param name="departmentId">The department identifier.</param>
        private async Task GetParentDepartments(string departmentId)
        {
            if (departmentId == null || Guid.Parse(departmentId) == Guid.Empty || string.IsNullOrWhiteSpace(departmentId))
            {
                return;
            }
            else
            {
                var parentDept = await GetDepartmentById(departmentId);

                if (parentDept != null && !ParentIdList.Contains(parentDept.Id.ToString()))
                {
                    ParentIdList.Add(parentDept.Id.ToString());
                    ParentDepartList.Add(parentDept);
                    await GetParentDepartments(parentDept.ParentDepartment?.ToString()); // 递归调用
                }
            }
        }
        /// <summary>
        /// 根据部门获取到分公司层
        /// </summary>
        /// <param name="departmentDtos"></param>
        /// <param name="deptId"></param>
        /// <returns></returns>
        private async Task<List<string>> GetDeptTree(List<DepartmentDto> departmentDtos, Guid? deptId)
        {
            List<string> deptTree = [];
            if (!deptId.HasValue)
                return deptTree;
            var department = departmentDtos.FirstOrDefault(m => m.Id == deptId);
            if (department == null || department.OrganizationType == Enums.DataverseEnums.Organization.OrganizationType.Headquarters)
                return deptTree;
            else if (department.ParentDepartment.HasValue)
            {
                deptTree.AddRange(await GetDeptTree(departmentDtos, department.ParentDepartment));
            }
            deptTree.Add(department.DepartmentName);
            return deptTree;
        }
        private async Task<DepartmentDto> GetDepartmentById(string departmentId)
        {
            //var departmentList = _dataverseService.GetDepartmentList().Result;
            var depts = await _dataverseService.GetOrganizations(departmentId);
            return depts.FirstOrDefault();
        }

        private async Task<List<UserListResponseDto>> GetUserListRole(List<UserListResponseDto> result)
        {
            //Role查询,将用户List的多个roleId和roleName查询出来，添加到每个用户的RoleList
            var roleList = await _identityRoleRepository.GetListAsync();
            var userRoleList = await _userRoleRepository.GetListAsync();
            foreach (var item in result)
            {
                item.RoleList = new List<RoleListDto>();
                var ur = userRoleList.FirstOrDefault(x => x.UserId == item.Id);
                if (ur != null)
                    item.RoleList.Add(new RoleListDto { Id = ur.RoleId });
            }
            foreach (var item in result)
            {
                foreach (var role in item.RoleList)
                {
                    role.Name = roleList.FirstOrDefault(x => x.Id == role.Id)?.Name;
                }
            }
            return result;
        }

        /// <summary>
        /// 组合roleList的roleName
        /// </summary>
        /// <param name="result">The result.</param>
        /// <returns></returns>
        private List<UserListResponseDto> GetUserListRoleName(List<UserListResponseDto> result)
        {
            foreach (var item in result)
            {
                string roleName = "";
                foreach (var role in item.RoleList)
                {
                    roleName += $"{role.Name},";
                }
                roleName = roleName.TrimEnd(',');
                item.Role = roleName;
            }
            return result;
        }

        //根据角色Id获取角色
        //private async  Task<List<RoleListDto>> GetRoles(IEnumerable<Guid> roleIds)
        //{
        //    var roleList = await _identityRoleRepository.GetListAsync();
        //    var roles = roleList.Where(p => roleIds.Contains(p.Id)).ToList();
        //    var res = ObjectMapper.Map<List<IdentityRole>, List<RoleListDto>>(roles);

        //    return res;

        //}

        #endregion
        /// <summary>
        /// 同步pp用户数据到abp
        /// </summary>
        /// <param name="staff"></param>
        /// <returns></returns>
        public async Task SynchronizeChangesUserAsync(StaffDto staff)
        {
            var userRepository = LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>();
            var queryableUser = await userRepository.GetQueryableAsync();
            var entity = queryableUser.FirstOrDefault(f => f.Id == staff.Id);

            if (entity == null) return;
            await _identityUserManager.SetEmailAsync(entity, staff.Email);
            await _identityUserManager.SetUserNameAsync(entity, staff.StaffAccount);
            entity.Name = staff.Name;
            entity.SetProperty(EntityConsts.IdentityUser.StaffCode, staff.StaffCode);
            entity.SetProperty(EntityConsts.IdentityUser.JobStatus, staff.JobStatus == Enums.DataverseEnums.Staff.JobStatus.Incumbency ? 1 : 0);
            await userRepository.UpdateAsync(entity);
        }

        /// <summary>
        /// 根据手机获取用户信息
        /// </summary>
        /// <param name="phone"></param>
        /// <returns></returns>
        public async Task<GerUserResponseDto> GetUserByPhoneAsync(string phone)
        {
            var userRepository = LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>();
            var user = await userRepository.FirstOrDefaultAsync(a => a.PhoneNumber == phone);
            if (user == null)
                return default;

            return new GerUserResponseDto
            {
                Id = user.Id,
                Name = user.Name,
                Email = user.Email
            };
        }

        /// <summary>
        /// 获取用户最后登录时间
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<UserLastLoginDto> GetUserLastLogin(Guid userId)
        {
            var redisValue = await LazyServiceProvider.LazyGetService<IRedisRepository>().Database.HashGetAsync(RedisKey.UserLastLoginTime, userId.ToString());
            if (string.IsNullOrEmpty(redisValue))
                return default;

            var userLastLogin = Newtonsoft.Json.JsonConvert.DeserializeObject<UserLastLoginDto>(redisValue);

            return userLastLogin;
        }

        /// <summary>
        /// 更新用户最后登录时间
        /// </summary>
        /// <returns></returns>
        public async Task<MessageResult> UpsertUserLastLoginTime()
        {
            var redisRepository = LazyServiceProvider.LazyGetService<IRedisRepository>();
            var redisEntries = redisRepository.Database.HashGetAll(RedisKey.UserLastLoginTime);
            if (redisEntries?.Any() == false)
                return MessageResult.SuccessResult();

            var userLastLogins = redisEntries.Select(a =>
            {
                var userLastLogin = Newtonsoft.Json.JsonConvert.DeserializeObject<UserLastLoginDto>(a.Value);
                var userExtension = new UserExtension { LastLoginTime = userLastLogin.LastLoginTime };
                userExtension.SetId(userLastLogin.Id);

                return userExtension;
            });

            var userExtensionRepository = LazyServiceProvider.LazyGetService<IUserExtensionRepository>();
            var dbContext = await userExtensionRepository.GetDbContextAsync();
            await dbContext.BulkInsertOrUpdateAsync(userLastLogins);

            return MessageResult.SuccessResult();
        }
        /// <summary>
        /// 获取用户Id列表
        /// </summary>
        /// <param name="ApplicatEmail"></param>
        /// <returns></returns>
        public async Task<HashSet<Guid>?> GetUserIdsByApplicatEmailAsnyc(string email)
        {
            if (!string.IsNullOrWhiteSpace(email))
            {
                var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
                var userIds = queryableUser.Where(u => u.Email.Contains(email)).Select(u => u.Id)?.ToHashSet();
                return userIds;
            }
            else
            {
                return null;
            }
        }
    }
}
