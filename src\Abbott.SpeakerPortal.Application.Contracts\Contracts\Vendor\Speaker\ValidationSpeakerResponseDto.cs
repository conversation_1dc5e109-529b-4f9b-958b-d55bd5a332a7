﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;
using Abbott.SpeakerPortal.Dtos;
using Abbott.SpeakerPortal.Enums;

using Volo.Abp.Application.Dtos;

namespace Abbott.SpeakerPortal.Vendor.Speaker
{
    public class ValidationSpeakerResponseDto
    {
        /// <summary>
        /// 讲者Id(Vendor 表Id)
        /// </summary>
        public Guid? VendorId { get; set; }
        /// <summary>
        /// 讲者申请Id(VendorApplication 表Id)
        /// </summary>
        public Guid? VendorApplicationId { get; set; }
        /// <summary>
        /// 讲者编号
        /// </summary>
        public string VendorCode { get; set; }
        /// <summary>
        /// 讲者名称
        /// </summary>
        public string SPName { get; set; }
        /// <summary>
        /// 职称Id
        /// </summary>
        public Guid? PTId { get; set; }
        /// <summary>
        /// 职称
        /// </summary>
        public string PTIName { get; set; }
        /// <summary>
        /// 所属医院Id
        /// </summary>
        public Guid? HospitalId { get; set; }
        /// <summary>
        /// 所属医院
        /// </summary>
        public string HospitalName { get; set; }
        /// <summary>
        /// 执业证书编码
        /// </summary>
        public string CertificateCode { get; set; }
        /// <summary>
        /// 手机号码
        /// </summary>
        public string HandPhone { get; set; }
        ///<summary>
        /// 证件类型
        ///</summary>
        public string CardType { get; set; }
        /// <summary>
        /// 证件编码
        /// </summary>
        public string CardNo { get; set; }

        /// <summary>
        /// EPDId
        /// </summary>
        public string EpdId { get; set; }

        /// <summary>
        /// 是否全匹配
        /// </summary>
        public bool IsFullMatch { get; set; }
    }
}
