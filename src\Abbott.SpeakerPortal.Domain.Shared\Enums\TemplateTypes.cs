﻿using System.ComponentModel;

namespace Abbott.SpeakerPortal.Enums
{
    /// <summary>
    /// 模板类型
    /// </summary>
    public enum TemplateTypes
    {
        #region 合规管理
        [Description("合规管理-讲者授权")]
        [Category("Speaker Authorization Template.xlsx")]
        OEC_SpeakerAuth = 10100,

        [Category("Add Intercept Template.xlsm")]
        [Description("合规管理-单据拦截-新增")]
        OEC_Interception_Add = 10201,

        [Category("Batch Tackle Intercept Template.xlsm")]
        [Description("合规管理-单据拦截-批量处理")]
        OEC_Interception_Update = 10202,
        #endregion

        #region 预算管理
        // - 子预算批量新增--20100 子预算批量调整--20101 子预算批量调拨--20102 子预算批量Mapping--20103
        [Description("子预算批量新增")]
        [Category("SubBudget Creation Template.xlsx")]
        SubBudget_Create = 20100,

        [Description("子预算批量调整")]
        [Category("Sub Budget Adjustment Template.xlsx")]
        SubBudget_Adjustment = 20101,

        [Description("子预算批量调拨")]
        [Category("Sub Budget Transfer Template.xlsx")]
        SubBudget_Transfer = 20102,

        [Description("子预算批量Mapping")]
        [Category("Budget Mapping Template.xlsx")]
        SubBudget_Mapping = 20103,

        [Description("主预算批量新增")]
        [Category("MasterBudget Creation Template.xlsx")]
        MasterBudget_Create = 20104,

        [Description("FOC主预算批量新增")]
        [Category("FOC MasterBudget Creation Template.xlsx")]
        FocMasterBudget_Create = 20105,

        [Description("FOC子预算批量新增")]
        [Category("FOC SubBudget Creation Template.xlsx")]
        FocSubBudget_Create = 20106,
        #endregion

        #region 采购管理

        [Description("财务出纳支付")]
        [Category("Finance Cashier Payment Import Template.xlsx")]
        Finance_Cashier_Payment = 30100,
        
        [Description("第三方讲者")]
        [Category("Batch Upsert Third Party Speaker Template.xlsx")]
        Pur_BatchUpsert_ThirdPartySpeaker = 30200,

        #endregion

        #region 付款申请
        [Category("Vendor Score Template.xlsx")]
        [Description("任务中心-付款申请-供应商评分")]
        Vendor_Score = 40101,

        [Description("任务中心-付款申请-财务凭证")]
        [Category("Financial Voucher Template.xlsx")]
        Financial_Voucher = 40102,
        #endregion

        #region 供应商查询-删除供应商
        [Category("Vendor Delete Template.xlsx")]
        [Description("供应商查询-删除供应商")]
        Vendor_Delete = 50101,
        #endregion

        #region 市场活动
        [Description("市场活动批量新增")]
        [Category("MarketActivity Update Template.xlsx")]
        MarketActivty_Create = 60101,

        #endregion

        #region 客户关系
        [Description("客户关系批量新增")]
        [Category("CustomerRelation Update Template.xlsx")]
        CustomerRelation_Create = 70101,
        [Category("CustomerRelation Export Template.xlsx")]
        CustomerRelation_Export = 70102,
        #endregion

        #region FOC
        [Description("FOC物流批量新增")]
        [Category("FOC Logistics Export Template.xlsx")]
        FocLogistics_Export = 80101,
        #endregion

        #region Concur

        [Description("用餐报告员工导入")]
        [Category("Employee Template.xlsx")]
        Concur_Employee_Import = 80201,

        #endregion
    }
}
