﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using Abbott.SpeakerPortal.Enums;

namespace Abbott.SpeakerPortal.Contracts.Budget.FocSubbudget
{
    public class CreateSubBudgetImportMessageDto : MothlyQtyDto
    {
        /// <summary>
        /// 行号
        /// </summary>
        public int No { get; set; }
        /// <summary>
        /// 主预算编码
        /// </summary>
        public string MasterBudgetCode { get; set; }
        /// <summary>
        /// 主预算Id
        /// </summary>
        public Guid? MasterBudgetId { get; set; }
        /// <summary>
        /// 成本中心
        /// </summary>
        public string CostCenter { get; set; }
        /// <summary>
        /// 成本中心Id
        /// </summary>
        public Guid? CostCenterId { get; set; }
        /// <summary>
        /// 大区
        /// </summary>
        public string Region { get; set; }
        /// <summary>
        /// 大区Id
        /// </summary>
        public Guid? RegionId { get; set; }
        /// <summary>
        /// 负责人姓名
        /// </summary>
        public string OwnerName { get; set; }
        /// <summary>
        /// 负责人邮箱
        /// </summary>
        public string OwnerEmail { get; set; }
        /// <summary>
        /// 负责人Id
        /// </summary>
        public Guid? OwnerId { get; set; }
        /// <summary>
        /// 产品ID
        /// </summary>
        public Guid ProductId { get; set; }

        /// <summary>
        /// 产品简称
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 产品品牌信息编码
        /// </summary>
        public string ProductMCode { get; set; }

        /// <summary>
        /// 产品规格
        /// </summary>
        public string ProductUnit { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 金额
        /// </summary>
        public string Description { get; set; }
        ///// <summary>
        ///// 是否开启
        ///// </summary>
        //public string StatusText { get; set; }
        ///// <summary>
        ///// 子预算状态
        ///// </summary>
        //public bool? Status { get; set; }
        /// <summary>
        /// 错误信息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 一月状态文本
        /// </summary>
        public string JanStatusText { get; set; }

        /// <summary>
        /// 二月状态文本
        /// </summary>
        public string FebStatusText { get; set; }
        /// <summary>
        /// 三月状态文本
        /// </summary>
        public string MarStatusText { get; set; }
        /// <summary>
        /// 四月状态文本
        /// </summary>
        public string AprStatusText { get; set; }
        /// <summary>
        /// 五月状态文本
        /// </summary>
        public string MayStatusText { get; set; }

        /// <summary>
        ///六月状态文本
        /// </summary>
        public string JunStatusText { get; set; }

        /// <summary>
        /// 七月状态文本
        /// </summary>
        public string JulStatusText { get; set; }

        /// <summary>
        /// 八月状态文本
        /// </summary>
        public string AugStatusText { get; set; }
        /// <summary>
        /// 九月状态文本
        /// </summary>
        public string SeptStatusText { get; set; }
        /// <summary>
        /// 十月状态文本
        /// </summary>
        public string OctStatusText { get; set; }
        /// <summary>
        /// 十一月状态文本
        /// </summary>
        public string NovStatusText { get; set; }
        /// <summary>
        /// 十二月状态文本
        /// </summary>
        public string DecStatusText { get; set; }
        [JsonIgnore]
        public List<FocMonthlyBudgetDto> MonthlyBudgets
        {
            get
            {

                return new List<FocMonthlyBudgetDto>()
                {
                    new FocMonthlyBudgetDto{ BudgetQty=JanQty.Value,Status=JanStatus.Value,Month=Month.Jan},
                    new FocMonthlyBudgetDto{ BudgetQty=FebQty.Value,Status=FebStatus.Value,Month=Month.Feb},
                    new FocMonthlyBudgetDto{ BudgetQty=MarQty.Value,Status=MarStatus.Value,Month=Month.Mar},
                    new FocMonthlyBudgetDto{ BudgetQty=AprQty.Value,Status=AprStatus.Value,Month=Month.Apr},
                    new FocMonthlyBudgetDto{ BudgetQty=MayQty.Value,Status=MayStatus.Value,Month=Month.May},
                    new FocMonthlyBudgetDto{ BudgetQty=JunQty.Value,Status=JunStatus.Value,Month=Month.Jun},
                    new FocMonthlyBudgetDto{ BudgetQty=JulQty.Value,Status=JulStatus.Value,Month=Month.Jul},
                    new FocMonthlyBudgetDto{ BudgetQty=AugQty.Value,Status=AugStatus.Value,Month=Month.Aug},
                    new FocMonthlyBudgetDto{ BudgetQty=SeptQty.Value,Status=SeptStatus.Value,Month=Month.Sept},
                    new FocMonthlyBudgetDto{ BudgetQty=OctQty.Value,Status=OctStatus.Value,Month=Month.Oct},
                    new FocMonthlyBudgetDto{ BudgetQty=NovQty.Value,Status=NovStatus.Value,Month=Month.Nov},
                    new FocMonthlyBudgetDto{ BudgetQty=DecQty.Value,Status=DecStatus.Value,Month=Month.Dec},
                };

            }
        }
    }
}
