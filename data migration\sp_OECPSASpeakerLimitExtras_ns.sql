create proc sp_OECPSASpeakerLimitExtras_ns
as
begin
	--首次全部insert
	IF OBJECT_ID(N'OECPSASpeakerLimitExtras', N'U') IS NULL
	begin
		select 
			NEWID() [Id],[VendorId],[ComPSALimitId],[ModifyType],[DivisionId],[ExtralAmountRest],[ExtralTimesRest],[ExtralAuditApplicationNo],[Year],[Remark],[Doc],[CreationTime],[CreatorId],[LastModificationTime],[LastModifierId],[IsDeleted],[DeleterId],[DeletionTime]
		into OECPSASpeakerLimitExtras
		from OECPSASpeakerLimitExtras_tmp;
		PRINT(N'首次新增完成');
	end
	else--否则upsert
	begin
		--update存量
		update t set
			t.[ComPSALimitId]=tmp.[ComPSALimitId],
			t.[ModifyType]=tmp.[ModifyType],
			t.[DivisionId]=tmp.[DivisionId],
			t.[ExtralAmountRest]=tmp.[ExtralAmountRest],
			t.[ExtralTimesRest]=tmp.[ExtralTimesRest],
			t.[ExtralAuditApplicationNo]=tmp.[ExtralAuditApplicationNo],
			t.[Year]=tmp.[Year],
			t.[Remark]=tmp.[Remark],
			t.[Doc]=tmp.[Doc],
			t.[CreationTime]=tmp.[CreationTime],
			t.[CreatorId]=tmp.[CreatorId],
			t.[LastModificationTime]=tmp.[LastModificationTime],
			t.[LastModifierId]=tmp.[LastModifierId],
			t.[IsDeleted]=tmp.[IsDeleted],
			t.[DeleterId]=tmp.[DeleterId],
			t.[DeletionTime]=tmp.[DeletionTime]
		from OECPSASpeakerLimitExtras t join OECPSASpeakerLimitExtras_tmp tmp on t.VendorId=tmp.VendorId and t.Year=tmp.Year;
		PRINT(N'修改存量数据完成');

		--insert增量
		insert OECPSASpeakerLimitExtras select NEWID() [Id],[VendorId],[ComPSALimitId],[ModifyType],[DivisionId],[ExtralAmountRest],[ExtralTimesRest],[ExtralAuditApplicationNo],[Year],[Remark],[Doc],[CreationTime],[CreatorId],[LastModificationTime],[LastModifierId],[IsDeleted],[DeleterId],[DeletionTime]
		from OECPSASpeakerLimitExtras_tmp tmp where not exists(select * from OECPSASpeakerLimitExtras where VendorId=tmp.VendorId and Year=tmp.Year);
		PRINT(N'新增增量数据完成');
	end
end