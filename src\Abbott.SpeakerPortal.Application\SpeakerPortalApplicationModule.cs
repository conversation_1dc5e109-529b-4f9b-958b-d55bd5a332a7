﻿using Abbott.SpeakerPortal.AppServices.System;
using Abbott.SpeakerPortal.BackgroundWorkers;
using Abbott.SpeakerPortal.BackgroundWorkers.Approval;
using Abbott.SpeakerPortal.BackgroundWorkers.Bpcs;
using Abbott.SpeakerPortal.BackgroundWorkers.Bpm;
using Abbott.SpeakerPortal.BackgroundWorkers.Graph;
using Abbott.SpeakerPortal.BackgroundWorkers.Integration.Dspot;
using Abbott.SpeakerPortal.BackgroundWorkers.Integration.Veeva;
using Abbott.SpeakerPortal.BackgroundWorkers.OEC;
using Abbott.SpeakerPortal.BackgroundWorkers.PostApprovalActions;
using Abbott.SpeakerPortal.BackgroundWorkers.Purchase;
using Abbott.SpeakerPortal.BackgroundWorkers.Report;
using Abbott.SpeakerPortal.BackgroundWorkers.Report.ProfessionalServiceTax;
using Abbott.SpeakerPortal.BackgroundWorkers.STicket;
using Abbott.SpeakerPortal.BackgroundWorkers.System;
using Abbott.SpeakerPortal.Contracts.Integration.Dspot;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Dataverse.Contracts;

using Hangfire;

using Microsoft.Extensions.DependencyInjection;

using System;
using System.Threading.Tasks;

using Volo.Abp;
using Volo.Abp.Account;
using Volo.Abp.AutoMapper;
using Volo.Abp.BackgroundWorkers;
using Volo.Abp.BackgroundWorkers.Hangfire;
using Volo.Abp.FeatureManagement;
using Volo.Abp.Identity;
using Volo.Abp.Modularity;
using Volo.Abp.PermissionManagement;
using Volo.Abp.SettingManagement;
using Volo.Abp.TenantManagement;

namespace Abbott.SpeakerPortal;

[DependsOn(
    typeof(SpeakerPortalDomainModule),
    typeof(AbpAccountApplicationModule),
    typeof(SpeakerPortalApplicationContractsModule),
    typeof(SpeakerPortalDataverseModule),
    typeof(SpeakerPortaDataverseContractsModule),
    typeof(AbpIdentityApplicationModule),
    typeof(AbpPermissionManagementApplicationModule),
    typeof(AbpTenantManagementApplicationModule),
    typeof(AbpFeatureManagementApplicationModule),
    typeof(AbpSettingManagementApplicationModule),
    typeof(AbpBackgroundWorkersHangfireModule)
    )]
public class SpeakerPortalApplicationModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        Configure<AbpAutoMapperOptions>(options =>
        {
            options.AddMaps<SpeakerPortalApplicationModule>();
        });
    }

    public override async Task OnApplicationInitializationAsync(ApplicationInitializationContext context)
    {
        await context.AddBackgroundWorkerAsync<SyncUserStatusWorker>();
        //BPCS
        await context.AddBackgroundWorkerAsync<SyncTableConfigsWorker>();
        await context.AddBackgroundWorkerAsync<SyncTableVendorsWorker>();
        await context.AddBackgroundWorkerAsync<SyncTableAphWorker>();
        await context.AddBackgroundWorkerAsync<SyncTableAmlWorker>();
        await context.AddBackgroundWorkerAsync<SyncTableGlhWorker>();
        await context.AddBackgroundWorkerAsync<SyncUncompletedPRVendorWorker>();
        //Dspot
        await context.AddBackgroundWorkerAsync<WholeProcessReportPushWorker>();
        //每天8: 00     10:30     12:30     14:30      16:30     18：30
        //周一到周六 20：30    22：00
        var jobOptions = new RecurringJobOptions() { TimeZone = TimeZoneInfo.Local };
        RecurringJob.AddOrUpdate<IInteBpcsVendorAppService>("Dspot.ProcessAndMoveErrors800", t => t.ProcessAndMoveErrors(), Cron.Daily(8, 0), jobOptions);
        RecurringJob.AddOrUpdate<IInteBpcsVendorAppService>("Dspot.ProcessAndMoveErrors1030", t => t.ProcessAndMoveErrors(), Cron.Daily(10, 30), jobOptions);
        RecurringJob.AddOrUpdate<IInteBpcsVendorAppService>("Dspot.ProcessAndMoveErrors1230", t => t.ProcessAndMoveErrors(), Cron.Daily(12, 30), jobOptions);
        RecurringJob.AddOrUpdate<IInteBpcsVendorAppService>("Dspot.ProcessAndMoveErrors1430", t => t.ProcessAndMoveErrors(), Cron.Daily(14, 30), jobOptions);
        RecurringJob.AddOrUpdate<IInteBpcsVendorAppService>("Dspot.ProcessAndMoveErrors1630", t => t.ProcessAndMoveErrors(), Cron.Daily(16, 30), jobOptions);
        RecurringJob.AddOrUpdate<IInteBpcsVendorAppService>("Dspot.ProcessAndMoveErrors1830", t => t.ProcessAndMoveErrors(), Cron.Daily(18, 30), jobOptions);
        RecurringJob.AddOrUpdate<IInteBpcsVendorAppService>("Dspot.ProcessAndMoveErrors2030", t => t.ProcessAndMoveErrors(), "0 30 20 ? * 6-5", jobOptions);
        RecurringJob.AddOrUpdate<IInteBpcsVendorAppService>("Dspot.ProcessAndMoveErrors2200", t => t.ProcessAndMoveErrors(), "0 0 22 ? * 6-5", jobOptions);

        //Veeva
        await context.AddBackgroundWorkerAsync<AnnualEPDMappingUpdateWorker>();
        //await context.AddBackgroundWorkerAsync<AnnualPushSpeakerWorker>();
        await context.AddBackgroundWorkerAsync<AnnualPullSpeakerWorker>();
        await context.AddBackgroundWorkerAsync<PullDcrResultWorker>();
        await context.AddBackgroundWorkerAsync<VeevaTimeoutSendingEmailWorker>();
        await context.AddBackgroundWorkerAsync<VeevaHcoDcrLogUpdateWorker>();

        //OEC
        await context.AddBackgroundWorkerAsync<BlackListAppService>();

        //Purchase
        await context.AddBackgroundWorkerAsync<ClosePurPRApplicationWorker>();

        #region Reports
        await context.AddBackgroundWorkerAsync<GenerateWholeProcessReportWorker>();
        await context.AddBackgroundWorkerAsync<GenerateAccrualReportWorker>();
        await context.AddBackgroundWorkerAsync<GenerateEPDReportWorker>();
        await context.AddBackgroundWorkerAsync<ProfessionalServiceTaxReportWorker>();
        await context.AddBackgroundWorkerAsync<ApprovalRecordReportWorker>();
        await context.AddBackgroundWorkerAsync<ApprovalRecordWorkflowTaskWorker>();
        await context.AddBackgroundWorkerAsync<GeneratePRApplicationReportWorker>();
        await context.AddBackgroundWorkerAsync<PAJoinBpcsGlhWorker>();
        #endregion

        await context.AddBackgroundWorkerAsync<BpmSyncOmWorker>();

        await context.AddBackgroundWorkerAsync<InitApprovalWorker>();
        await context.AddBackgroundWorkerAsync<SyncUserLastLoginTimeWorker>();
        await context.AddBackgroundWorkerAsync<STicketSyncSOIWorker>();
        await context.AddBackgroundWorkerAsync<SyncLogMonitorWorker>();

        //MDM
        await context.AddBackgroundWorkerAsync<ProductDetailSyncWorker>();
        await context.AddBackgroundWorkerAsync<StoreInfoSyncWorker>();

        //PP
        await context.AddBackgroundWorkerAsync<PPRegionSyncWorker>();
        await context.AddBackgroundWorkerAsync<PPCostCenterSyncWorker>();
        await context.AddBackgroundWorkerAsync<SystemLogCleanerWorker>();

        //每日保存SequenceNum从Redis到DB
        await context.AddBackgroundWorkerAsync<SyncBpcsEdiSequenceNumWorker>();

        var settingInitializationService = context.ServiceProvider.GetRequiredService<SettingInitializationService>();
        await settingInitializationService.InitializeAsync();
    }
}
