create view [dbo].[vw_ReturnExchangeApplicationsWithApproval] as 
select 
case 
	when RA.IsPhysical is not null  then N'退货'
	else N'换货'
end ApplicationType
,RA.ApplicationCode
,RA.ApplyTime
,RA.ApplyUser
,RA.ClientCode
,RA.ClientName
,RA.ConvertedCustType
,RA.Reason
,RA.OrderNumber
,RA.IsPhysical
,RA.PaymentMethod
,Status
,approval.EApprover as ExpenseApproverId
,u.UserName AS ExpenseApproverName
,approval.ETime as ExpenseApprovalTime
,approval.FApprover as FinanceApproverId
,u2.UserName AS FinanceApproverName
,approval.FTime as FinanceApprovalTime
,RAD.RowId
,RAD.ProductSCode
,RAD.ProductName
,RAD.UnitPriceTax
,RAD.ProductQuantity
,RAD.AmountTax
,RAD.PhysicalBoxes
,RAD.StandardBoxFactor
,RAD.ProductQualityDes
,RAD.BatchNumber
,RAD.ShelfLife
from dbo.ReturnApplications as RA
left join dbo.ReturnApplicationDetails RAD
on RA.Id=RAD.ReturnApplicationId
left join (
   select ex.FormId,ex.[ApprovalTime] as ETime,fn.[ApprovalTime] as FTime,ex.[ApprovalId] as EApprover,fn.[ApprovalId] as FApprover  from (SELECT 
        [FormId],
        [ApprovalTime],
		[ApprovalId],
        ROW_NUMBER() OVER (PARTITION BY [FormId] ORDER BY [ApprovalTime] DESC) AS wf
    FROM [dbo].[WorkflowTasks] where WorkStep like N'%循环审批-Expense%') as ex 
	left join (
	select * from (SELECT 
        [FormId],
        [ApprovalTime],
		[ApprovalId],
        ROW_NUMBER() OVER (PARTITION BY [FormId] ORDER BY [ApprovalTime] DESC) AS wf
    FROM [dbo].[WorkflowTasks] where WorkStep like N'%财务循环审批-Finance%') as fn where fn.wf=1) as fn on ex.FormId=fn.FormId
	where ex.wf=1) as approval on RA.Id=approval.FormId
	left join [dbo].[AbpUsers] as u on  approval.EApprover=u.Id
	left join [dbo].[AbpUsers] as u2 on  approval.FApprover=u2.Id
;