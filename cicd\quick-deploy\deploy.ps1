param($env, $acr, $domain)
$TAG="v$(Get-Date -Format 'yyyyMMddHHmmss')"
az acr login -n $acr
docker build -t $acr$domain/$env/abbott-services-speaker-portal-service -f .\Abbott.SpeakerPortal.HttpApi.Host\Dockerfile . -t $acr$domain/$env/abbott-services-speaker-portal-service:$TAG
docker push $acr$domain/$env/abbott-services-speaker-portal-service:$TAG
if(Test-Path .\tmp) {
}
else
{
New-Item -ItemType Directory -Path '.\tmp'
}
Get-Content ".\aks\microservices\speaker-portal-service.yml" | Foreach-Object {$_ -replace ":__TAG_VERSION",":$TAG"} | Foreach-Object {$_ -replace "__ENV","$env"} | Foreach-Object {$_ -replace "__ACR-ENV","$env"} | Foreach-Object {$_ -replace "__ACR-REG","$acr"} | Foreach-Object {$_ -replace "__ACR-DOMAIN","$domain"} | Out-File .\tmp\quick-apply.yml
kubectl apply -f .\tmp\quick-apply.yml
Remove-Item .\tmp -recurse