﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Abbott.SpeakerPortal.Consts
{
    public class KeyVaultKeys
    {
        /// <summary>
        /// sqlServer连接字符串
        /// </summary>
        public const string SQLSERVER_CONNECTION_STRING = "SqlServerConnectionString";
        /// <summary>
        /// IntermediateSqlServer
        /// </summary>
        public const string INTERMEDIATE_SQLSERVER_CONNECTION_STRING = "IntermediateSqlServerConnectionString";
        /// <summary>
        /// Redis连接字符串
        /// </summary>
        public const string REDIS_CONFIGURATION = "RedisConfiguration";
        /// <summary>
        /// dataverse 连接串
        /// </summary>
        public const string DATAVERSE_CONFIGURATION = "DataVerseConfiguration";
        /// <summary>
        /// ApplicationInsights 
        /// </summary>
        public const string APPLICATIONINSIGHTS_CONFIGURATION = "ApplicationInsightsConfiguration";
        /// <summary>
        /// azure ocr
        /// </summary>
        public const string OCR_CONFIGURATION = "OCRHost";
        /// <summary>
        /// Ocr key
        /// </summary>
        public const string OCR_KEY_CONFIGURATION = "OCRKey";
        /// <summary>
        /// Auth Server 生成证书的key
        /// </summary>
        public const string PASS_PHRASE = "PassPhrase";
        /// <summary>
        /// AAD TenantId
        /// </summary>
        public const string AZUREAD_TENANTID = "AADTenantId";
        /// <summary>
        /// ClientId
        /// </summary>
        public const string AZUREAD_CLIENTID = "AADClientId";
        /// <summary>
        /// Scope
        /// </summary>
        public const string AZUREAD_SCOPE = "AADScope";
        /// <summary>
        /// JwksUrl
        /// </summary>
        public const string AZUREAD_JWKSURL = "AADJwksUrl";
        /// <summary>
        /// ClientId
        /// </summary>
        public const string CONSENT_CLIENTID = "ConsentClientId";
        /// <summary>
        /// secret
        /// </summary>
        public const string CONSENT_SECRET = "ConsentSecret";
        /// <summary>
        /// ConsentCode
        /// </summary>
        public const string CONSENT_CONSENTCODE = "ConsentConsentCode";
        /// <summary>
        /// 小程序AppID
        /// </summary>
        public const string WXAPP_APP_ID = "WxAppAppID";
        /// <summary>
        /// 小程序AppSecret
        /// </summary>
        public const string WXAPP_APP_SECRET = "WxAppAppSecret";
        /// <summary>
        /// 企业微信AppID
        /// </summary>
        public const string WCAPP_APP_ID = "WcAppAppID";
        /// <summary>
        /// 企业微信编号
        /// </summary>
        public const string WCAPP_APP_AGENTID = "WcAppAgentID";
        /// <summary>
        /// 企业微信AppSecret
        /// </summary>
        public const string WCAPP_APP_SECRET = "WcAppAppSecret";
        /// <summary>
        /// Address
        /// </summary>
        public const string STORAGE_ADDRESS = "StorageAddress";
        /// <summary>
        /// sms url
        /// </summary>
        public const string SMS_URL = "SMSUrl";
        /// <summary>
        /// sms appid
        /// </summary>
        public const string SMS_APPID = "SMSAppId";
        /// <summary>
        /// sms SecretKey
        /// </summary>
        public const string SMS_SECRETKEY = "SMSSecretKey";
        /// <summary>
        /// Integrations DSpot WholeProcessReport_PublicKey
        /// </summary>
        public const string DSPOT_WHOLEPROCESSREPORT_PUBLICKEY = "DSpotWholeProcessReportPublicKey";
        /// <summary>
        /// DSPOT_TwoElementsAppKey
        /// </summary>
        public const string DSPOT_TWOELEMENTSAPPKEY = "DSPOTTwoElementsAppKey";
        /// <summary>
        /// TwoElementsAppSecret
        /// </summary>
        public const string DSPOT_TWOELEMENTSAPPSECRET = "DSPOTTwoElementsAppSecret";
        /// <summary>
        /// BPCS_SftpIP
        /// </summary>
        public const string BPCS_SFTPIP = "BPCSSftpIP";
        /// <summary>
        /// SftpUser
        /// </summary>
        public const string BPCS_SFTPUSER = "SftpUser";
        /// <summary>
        /// SftpPwd
        /// </summary>
        public const string BPCS_SFTPPWD = "BPCSSftpPwd";
        /// <summary>
        /// SftpFolderLvl1
        /// </summary>
        public const string BPCS_SFTPFOLDERLVL1 = "BPCSSftpFolderLvl1";
        /// <summary>
        /// EPD_HCP_Portal_AesKey256
        /// </summary>
        public const string EPD_HCP_PORTAL_AESKEY256 = "EPDHCPPortalAesKey256";
        /// <summary>
        /// OM_AppId
        /// </summary>
        public const string OM_APPID = "OMAppId";
        /// <summary>
        /// AppSecret
        /// </summary>
        public const string OM_APPSECRET = "OMAppSecret";
        /// <summary>
        /// AppSecret
        /// </summary>
        public const string VEEVA_USERNAME = "InteVeevaUserName";
        /// <summary>
        /// AppSecret
        /// </summary>
        public const string VEEVA_PASSWORD = "InteVeevaPassword";
        /// <summary>
        /// Graph ClientId
        /// </summary>
        public const string GRAPH_CLIENTID = "GraphClientId";
        /// <summary>
        /// Graph TenantId
        /// </summary>
        public const string GRAPH_TENANTID = "GraphTenantId";
        /// <summary>
        /// Graph Password
        /// </summary>
        public const string GRAPH_CERTPASSWORD = "GraphCertPassword";

        /// <summary>
        /// ApplicationInsightKey
        /// </summary>
        public const string ApplicationInsightKey = "ApplicationInsightKey";

        public const string HangfireUserName = "HangfireUserName";
        public const string HangfirePassword = "HangfirePassword";
        public const string HangfireDigestRealm = "HangfireDigestRealm";
        public const string HangfireNonce = "HangfireNonce";
        public const string HangfireOpaque = "HangfireOpaque";


        public const string VeevaSftpIP = "InteVeevaSftpIP";
        public const string VeevaSftpUser = "InteVeevaSftpUser";
        public const string VeevaSftpPwd = "InteVeevaSftpPwd";
        public const string VeevaAES256Key = "InteVeevaAES256Key";
        public const string VeevaAES256IV = "InteVeevaAES256IV";

        public const string SOI_AppKey = "SOIAppKey";
        public const string SOI_AppSecret = "SOIAppSecret";
        public const string SOI_BaseUrl = "SOIBaseUrl";

        public const string CSS_AppSecret = "CSSAppSecret";
        public const string CSS_BaseUrl = "CSSBaseUrl";

        //MDM
        public const string MdmAppId = "MdmAppId";
        public const string MdmApiKey = "MdmApiKey";
        public const string MdmAppSecret = "MdmAppSecret";
    }
}
