select newid()  as spk_NexBPMCode,* into #spk_organizational_product from (
select pt.spk_BPMCode,TRY_CONVERT(UNIQUEIDENTIFIER,pt.spk_name) as spk_product,
TRY_CONVERT(UNIQUEIDENTIFIER,opt.spk_BU) as spk_BU,'' as spk_name,opt.flg
from spk_organizational_product_Tmp opt
join spk_productmasterdata_tmp pt
on opt.spk_product=pt.spk_BPMCode)A


IF OBJECT_ID(N'dbo.spk_organizational_product', N'U') IS NOT NULL
BEGIN
	update a 
	set a.spk_BPMCode  = b.spk_BPMCode
        ,a.spk_product = b.spk_product
        ,a.spk_BU      = b.spk_BU     
        ,a.spk_name    = b.spk_name   
        ,a.flg         = b.flg
    from dbo.spk_organizational_product a
    join #spk_organizational_product b on a.spk_BPMCode = b.spk_BPMCode
    
    insert into dbo.spk_organizational_product
    select a.spk_NexBPMCode
          ,a.spk_BPMCode
          ,a.spk_product
          ,a.spk_BU
          ,a.spk_name
          ,a.flg
	from #spk_organizational_product a
	where NOT EXISTS (SELECT * FROM dbo.spk_organizational_product where spk_BPMCode = a.spk_BPMCode)
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into dbo.spk_organizational_product from #spk_organizational_product
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
