﻿---
apiVersion: apps/v1
kind: Deployment
metadata:
  # Kind 的名称
  name: corp-191-speakerapi-stg
  namespace: default
spec:
  selector:
    matchLabels:
      # 容器标签的名字，发布 Service 时，selector 需要和这里对应
      app: corp-191-speakerapi-stg
  # 部署的实例数量
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 3        # 一次可以添加多少个Pod
      maxUnavailable: 3  # 滚动更新期间最大多少个Pod不可用
  template:
    metadata:
      labels:
        app: corp-191-speakerapi-stg
    spec:
      # 配置容器，数组类型，说明可以配置多个容器
      containers:
      # 容器名称
      - name: corp-191-speakerapi-stg
        # 容器镜像
        image: abbottchina.azurecr.cn/corp-ca191-speakerapi-s:latest
        # 只有镜像不存在时，才会进行镜像拉取IfNotPresent
        imagePullPolicy: Always
        ports:
        # Pod 端口
        - containerPort: 80
        env:
        - name: ASPNETCORE_ENVIRONMENT
          value: "Stage"
        - name: ASPNETCORE_URLS
          value: "http://+:80"
---
apiVersion: v1
kind: Service
metadata:
  name: corp-191-speakerapi-stg
spec:
  selector:
    app: corp-191-speakerapi-stg
  type: ClusterIP
  ports:
  - protocol: TCP
    port: 80
    targetPort: 80
