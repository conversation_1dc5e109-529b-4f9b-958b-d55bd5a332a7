﻿using Abbott.SpeakerPortal.Contracts.Integration.Dspot;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Renci.SshNet;
using Renci.SshNet.Sftp;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Text;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.AppServices.Integration.Dspot
{
    public class InteBpcsSFTPService : SpeakerPortalAppService, IInteBpcsSFTPService
    {
        /// <summary>
        /// The service provider
        /// </summary>
        private readonly IServiceProvider _serviceProvider;

        /// <summary>
        /// The logger
        /// </summary>
        private readonly ILogger<InteBpcsSFTPService> _logger;

        /// <summary>
        /// The configuration
        /// </summary>
        private readonly IConfiguration _configuration;

        private readonly string _sftpIp;
        private readonly int _sftpPort = 22;
        private readonly string _sftpUser;
        private readonly string _sftpPwd;

        private readonly Encoding _encoding;

        public InteBpcsSFTPService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _logger = serviceProvider.GetService<ILogger<InteBpcsSFTPService>>();
            _configuration = serviceProvider.GetService<IConfiguration>();

            //加载初始化需要的数据
            _sftpIp = _configuration["Integrations:BPCS:SftpIP"];
            _sftpUser = _configuration["Integrations:BPCS:SftpUser"];
            _sftpPwd = _configuration["Integrations:BPCS:SftpPwd"];

            //上传文本文件，默认编码 中文(ANSI)
            _encoding = Encoding.GetEncoding("GB2312");
        }

        public bool UploadTo111(Stream stream, string sftpPath)
        {
            try
            {
                //创建连接对象
                using (var client = new SftpClient(_sftpIp, _sftpPort, _sftpUser, _sftpPwd))
                {
                    client.Connect(); //连接

                    if (stream != null && stream.Length > 0)
                    {
                        client.BufferSize = 4194304;//4 * 1024 * 1024;//4MB
                        stream.Seek(0, SeekOrigin.Begin);

                        client.UploadFile(stream, sftpPath);
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"UploadTo() Exception: {ex}");
            }
            return false;
        }

        public bool UploadTo222(List<string> inputs, string sftpPath)
        {
            try
            {
                //创建连接对象
                using (var client = new SftpClient(_sftpIp, _sftpPort, _sftpUser, _sftpPwd))
                {
                    client.Connect(); //连接
                    using (MemoryStream ms = new MemoryStream())
                    {
                        foreach (var item in inputs)
                        {
                            byte[] bytes = Encoding.UTF8.GetBytes(item);
                            ms.Write(bytes, 0, bytes.Length);
                        }

                        if (ms != null && ms.Length > 0)
                        {
                            client.BufferSize = 4194304;//4 * 1024 * 1024;//4MB
                            ms.Seek(0, SeekOrigin.Begin);

                            client.UploadFile(ms, sftpPath);
                            return true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"UploadTo() Exception: {ex}");
            }
            return false;
        }

        public bool UploadTo(List<string> inputs, string sftpPath, Encoding encoding = null)
        {
            if (inputs?.Any() != true)
            {
                return false;
            }
            try
            {
                //创建连接对象
                using (var client = new SftpClient(_sftpIp, _sftpPort, _sftpUser, _sftpPwd))
                {
                    client.Connect(); //连接
                    using (var ms = new MemoryStream())
                    {
                        using (var writer = new StreamWriter(ms, encoding ?? _encoding))
                        {
                            for (int i = 0; i < inputs.Count; i++)
                            {
                                writer.Write(inputs[i] + $"{(i >= inputs.Count - 1 ? "" : "\r\n")}");
                            }
                            writer.Flush();

                            client.BufferSize = 4194304;//4 * 1024 * 1024;//4MB
                            ms.Seek(0, SeekOrigin.Begin);
                            client.UploadFile(ms, sftpPath);
                            return true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"UploadTo() Exception: {ex}");
            }
            return false;
        }

        public bool UploadToBatch(Dictionary<string, string[]> ediLines, Encoding encoding = null)
        {
            if (ediLines?.Any() != true)
            {
                _logger.LogError($"UploadToBatch() ediLines?.Any() != true");
                return false;
            }
            try
            {
                _logger.LogInformation($"UploadToBatch() SFTP Config: _sftpIp: {_sftpIp}; _sftpPort: {_sftpPort}; _sftpUser: {_sftpUser}; _sftpPwd: {_sftpPwd};");
                //创建连接对象
                using (var client = new SftpClient(_sftpIp, _sftpPort, _sftpUser, _sftpPwd))
                {
                    client.Connect(); //连接
                    foreach (var aLine in ediLines)
                    {
                        using (var ms = new MemoryStream())
                        {
                            using (var writer = new StreamWriter(ms, encoding ?? _encoding))//Encoding.GetEncoding("GB2312") Encoding.Default
                            {
                                writer.Write(aLine.Value[1]);
                                writer.Flush();

                                client.BufferSize = 4194304;//4 * 1024 * 1024;//4MB
                                ms.Seek(0, SeekOrigin.Begin);
                                client.UploadFile(ms, aLine.Value[0]);
                                //return true;
                            }
                        }
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"UploadToBatch() Exception: {ex}");
            }
            return false;
        }
                
        public List<string> GetSub(string parentFolder, bool? IsDirectory = null, string prefix = null, string suffix = null)
        {
            var result = new List<string>();
            try
            {
                IEnumerable<ISftpFile> sftpFiles = null;
                //创建连接对象
                using (var client = new SftpClient(_sftpIp, _sftpPort, _sftpUser, _sftpPwd))
                {
                    client.Connect(); //连接

                    //var command = $"find . -type d -name '{prefix}*{suffix}'"; // 使用find命令查找所有具有特定前缀后缀的子目录

                    sftpFiles = client.ListDirectory(parentFolder);
                }
                if (sftpFiles?.Any() != true)
                {
                    return result;
                }

                foreach (SftpFile current in sftpFiles.WhereIf(IsDirectory != null, a => a.IsDirectory == IsDirectory.Value))
                {
                    string name = current.Name;
                    if (!string.IsNullOrWhiteSpace(prefix)
                        && !name.StartsWith(prefix, StringComparison.OrdinalIgnoreCase))
                    {
                        continue;
                    }

                    if (!string.IsNullOrWhiteSpace(suffix)
                        && !name.EndsWith(suffix, StringComparison.OrdinalIgnoreCase))
                    {
                        continue;
                    }

                    result.Add(current.FullName);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"GetSub() Exception: {ex}");
            }
            return result;
        }

        public string[] ReadTxtLines(string filePath)
        {
            if (string.IsNullOrWhiteSpace(filePath))
            {
                return null;
            }
            try
            {
                //创建连接对象
                using (var client = new SftpClient(_sftpIp, _sftpPort, _sftpUser, _sftpPwd))
                {
                    client.Connect(); //连接

                    return client.ReadAllLines(filePath);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"ReadTxtLines() Exception: {ex}");
            }
            return null;
        }

        public Dictionary<string, string[]> ReadTxtsLines(IEnumerable<string> filePaths)
        {
            var result = new Dictionary<string, string[]>();
            if (filePaths?.Any() != true)
            {
                return result;
            }
            try
            {
                //创建连接对象
                using (var client = new SftpClient(_sftpIp, _sftpPort, _sftpUser, _sftpPwd))
                {
                    client.Connect(); //连接

                    foreach (var file in filePaths)
                    {
                        if (string.IsNullOrWhiteSpace(file))
                        {
                            continue;
                        }
                        result.Add(file, client.ReadAllLines(file));
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"ReadTxtsLines() Exception: {ex}");
            }
            return result;
        }

        public void MoveEDIFilesByFolder(string sourcePath, string destinationPath)
        {
            try
            {
                using (var sftp = new SftpClient(_sftpIp, _sftpPort, _sftpUser, _sftpPwd))
                {
                    sftp.Connect();

                    // 获取源目录中的所有文件信息
                    var files = GetSub(sourcePath, false);
                    if (files?.Any() != true)
                    {
                        return;
                    }

                    // 确认目标路径存在
                    if (!sftp.Exists(destinationPath))
                    {
                        sftp.CreateDirectory(destinationPath);
                    }
                    foreach (var item in files)
                    {
                        // 确认源文件（Path）存在
                        if (sftp.Exists(item))
                        {
                            // 目标文件路径
                            var destinationFilePath = Path.Combine(destinationPath, Path.GetFileName(item));

                            // 移动文件
                            sftp.RenameFile(item, destinationFilePath);
                        }
                    }
                    sftp.Disconnect();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"MoveEDIFilesByFolder() Exception: {ex}");
            }
        }
    }
}