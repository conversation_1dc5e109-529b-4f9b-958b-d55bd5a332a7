﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Entities.VendorOrgnizations;
using System.IO;
using MiniExcelLibs;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Entities.Common.Attachment;
using Abbott.SpeakerPortal.Contracts.Common.Attachment;
using Abbott.SpeakerPortal.Entities.Vendors;
using System.Linq.Dynamic.Core;
using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Utils;
using Abbott.SpeakerPortal.Vendor.Speaker;
using Abbott.SpeakerPortal.Entities.VendorPersonals;
using Abbott.SpeakerPortal.Entities.VendorApplications;
using Abbott.SpeakerPortal.Contracts.Vendor.NonSpeaker;
using Abbott.SpeakerPortal.Contracts.Dataverse;
using Abbott.SpeakerPortal.Contracts.Vendor.NonSpeaker.HCI;
using Abbott.SpeakerPortal.Contracts.Vendor.NonSpeaker.NonHCI;
using Abbott.SpeakerPortal.Contracts.Vendor.NonSpeaker.NonHcpPersonal;
using Abbott.SpeakerPortal.Contracts.Vendor;
using Abbott.SpeakerPortal.Entities.VendorApplicationOrgnizations;
using Abbott.SpeakerPortal.Entities.VendorApplicationPersonals;
using Abbott.SpeakerPortal.Entities.VendorFinancials;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Abbott.SpeakerPortal.Contracts.Vendor.Speaker.TaskCenter;
using Newtonsoft.Json;
using Volo.Abp.ObjectMapping;
using Microsoft.Extensions.DependencyInjection;
using Abbott.SpeakerPortal.Entities.VendorApplicationFinancials;
using Volo.Abp.Domain.Repositories;
using Newtonsoft.Json.Linq;

namespace Abbott.SpeakerPortal.AppServices.Vendor
{
    public partial class NonSpeakerAppService : SpeakerPortalAppService, INonSpeakerAppService
    {
        #region 任务中心-变更详情
        /// <summary>
        /// 非HCP个人的变更详情
        /// </summary>
        /// <param name="applicationId"></param>
        /// <returns></returns>
        public async Task<MessageResult> GetVendorChangedDetailNonHcp(Guid applicationId)
        {
            var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");
            try
            {
                var applicationDetail = await VendorApplicationPersonalDetail(applicationId);
                if (!applicationDetail.Success)
                    return MessageResult.FailureResult(applicationDetail.Message);
                var applicationData = applicationDetail.Data;
                var applicationPersonal = applicationData as VendorApplicationPersonalDetailReponseDto;
                var vendorCode = applicationPersonal.VendorCode;

                //基本信息映射
                var applyBaseData = ObjectMapper.Map<VendorApplicationPersonalDetailReponseDto, NonHcpPersonalChangedDetailResponseDto>(applicationPersonal);
                applyBaseData.IsDraft = true;
                applyBaseData.ID = (Guid)applicationPersonal.ID;
                if (!string.IsNullOrEmpty(applicationPersonal.BankCityName))
                    applyBaseData.BankCityName = applicationPersonal.BankCityName;

                //修改信息映射
                var applyData = ObjectMapper.Map<VendorApplicationPersonalDetailReponseDto, NonHcpPersonalChangedDetail>(applicationPersonal);
                applyData.IsDraft = true;
                if (!string.IsNullOrEmpty(applicationPersonal.BankCityName))
                    applyData.BankCity = applicationPersonal.BankCityName;
                applyData.Sex = applicationPersonal.Sex;
                applyData.ProvinceCity = applicationPersonal.ProvinceCityName?.JoinAsString("/");

                applyData.BankName = applicationPersonal.BankCode;
                if (!string.IsNullOrEmpty(applyData.BankCardNo))
                    applyData.BankCardNo = AesHelper.Decryption(applyData.BankCardNo, insightKey);

                if (!string.IsNullOrEmpty(applyData.CardNo))
                    applyData.CardNo = AesHelper.Decryption(applyData.CardNo, insightKey);

                var vendorQuery = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();
                var vendorPersonalQuery = await LazyServiceProvider.LazyGetService<IVendorPersonalRepository>().GetQueryableAsync();
                var vendorFinanceQuery = await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync();

                var attachmentQuery = await LazyServiceProvider.LazyGetService<IAttachmentRepository>().GetQueryableAsync();
                var commonService = LazyServiceProvider.LazyGetService<ICommonService>();
                var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();

                //查询正式库数据
                var query = vendorQuery.Where(x => x.VendorCode == vendorCode)//&& x.VendorType == VendorTypes.NonHCPPerson
                    .Join(vendorPersonalQuery, v => v.Id, p => p.VendorId, (a, b) => new { vendor = a, personal = b });
                var vendorPersonEntity = query.FirstOrDefault();
                if (vendorPersonEntity == null)
                    return MessageResult.FailureResult("获取供应商信息失败");

                //获取正式信息
                var companyList = await dataverseService.GetCompanyList();
                var companyCurrency = await dataverseService.GetCompanyCurrencyList();
                var vendorAppQuery = await LazyServiceProvider.LazyGetService<IVendorApplicationRepository>().GetQueryableAsync();
                var vendorOldData = JsonConvert.DeserializeObject<VendorPersonalDetailResponseDto>(vendorAppQuery.First(f => f.Id == applicationId).UpdatePreJson);
                vendorOldData.IsDraft = false;
                vendorOldData.FinanceInfo.ForEach(f =>
                {
                    f.CompanyName = companyList.FirstOrDefault(w => w.CompanyCode == f.Company)?.CompanyName;
                    f.CurrencyName = companyCurrency.FirstOrDefault(w => w.Code == f.Currency)?.Name;
                });
                var vendorData = ObjectMapper.Map<VendorPersonalDetailResponseDto, NonHcpPersonalChangedDetail>(vendorOldData);
                if (!string.IsNullOrEmpty(vendorOldData.BankCityName))
                    vendorData.BankCity = vendorOldData.BankCityName;

                vendorData.Sex = vendorOldData.Sex;
                vendorData.ProvinceCity = vendorOldData.ProvinceCityName?.JoinAsString("/");
                //解密
                if (!string.IsNullOrEmpty(vendorData.CardNo))
                    vendorData.CardNo = AesHelper.Decryption(vendorData.CardNo, insightKey);

                vendorData.BankName = vendorOldData.BankCode;
                if (!string.IsNullOrEmpty(vendorData.BankCardNo))
                    vendorData.BankCardNo = AesHelper.Decryption(vendorData.BankCardNo, insightKey);

                //排序前端验证需要使用
                vendorData.FinanceInfo = [.. vendorData.FinanceInfo.OrderBy(o => o.Company)];
                applyData.FinanceInfo = [.. applyData.FinanceInfo.OrderBy(o => o.Company)];
                //添加财务是否可修改的flag
                vendorData.FinanceInfo.ForEach(f => f.Flag = true);
                applyData.FinanceInfo.ForEach(f => f.Flag = true);
                applyBaseData.FinanceInfo.ForEach(f =>
                {
                    f.Flag = vendorData.FinanceInfo.Select(s => s.Company).Contains(f.Company);
                });

                //检查变更内容，是否触发DPScheck
                await GetChangeContentDPSCheck(applyBaseData);

                applyBaseData.PersonalDetailChangedCompare = [vendorData, applyData];
                applyBaseData.BankName = applyBaseData.BankCode;
                return MessageResult.SuccessResult(applyBaseData);
            }
            catch (Exception ex)
            {
                var msgContent = $"NonSpeakerAppService's GetVendorChangedDetailNonHcp has an error : {ex.Message}";
                _logger.LogError(msgContent);
                return await Task.FromResult(MessageResult.FailureResult(new MessageModelBase(500, msgContent)));
            }
        }

        /// <summary>
        /// HCI机构的变更详情
        /// </summary>
        /// <param name="applicationId"></param>
        /// <returns></returns>
        public async Task<MessageResult> GetVendorChangedDetailHci(Guid applicationId)
        {
            var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");
            try
            {
                var applicationDetail = await VendorApplicationOrgDetail(applicationId, VendorTypes.HCIAndOtherInstitutionsAR);
                if (!applicationDetail.Success)
                    return MessageResult.FailureResult(applicationDetail.Message);
                var applicationData = applicationDetail.Data;
                var applicationPersonal = applicationData as VendorApplicationOrganizationDetailResponseDto;
                var vendorCode = applicationPersonal.VendorCode;

                //基本信息映射
                var applyBaseData = ObjectMapper.Map<VendorApplicationOrganizationDetailResponseDto, HciOrgChangedDetailResponseDto>(applicationPersonal);
                applyBaseData.IsDraft = true;
                applyBaseData.ID = (Guid)applicationPersonal.ID;

                //修改信息映射
                var applyData = ObjectMapper.Map<VendorApplicationOrganizationDetailResponseDto, HciOrgChangedDetail>(applicationPersonal);
                applyData.IsDraft = true;
                applyData.BankName = applicationPersonal.BankCode;
                if (!string.IsNullOrEmpty(applyData.BankCardNo))
                    applyData.BankCardNo = AesHelper.Decryption(applyData.BankCardNo, insightKey);
                applyData.BankCityName = applicationPersonal.BankCityName;
                applyBaseData.BankCityName = applicationPersonal.BankCityName;
                if (applicationPersonal.ProvinceCityName != null || applicationPersonal.ProvinceCityName.Any())
                {
                    applyData.ProvinceCityName = $"{applicationPersonal.ProvinceCityName[0] ?? ""}/{applicationPersonal.ProvinceCityName[1] ?? ""}";
                    applyBaseData.ProvinceCityName = $"{applicationPersonal.ProvinceCityName[0] ?? ""}/{applicationPersonal.ProvinceCityName[1] ?? ""}";
                }

                var vendorQuery = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();
                var vendorOrgQuery = await LazyServiceProvider.LazyGetService<IVendorOrgnizationRepository>().GetQueryableAsync();
                var vendorFinanceQuery = await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync();

                var attachmentQuery = await LazyServiceProvider.LazyGetService<IAttachmentRepository>().GetQueryableAsync();
                var commonService = LazyServiceProvider.LazyGetService<ICommonService>();
                var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();

                var query = vendorQuery.Where(x => x.VendorCode == vendorCode)//&& x.VendorType == VendorTypes.HCIAndOtherInstitutionsAR
                    .Join(vendorOrgQuery, v => v.Id, p => p.VendorId, (a, b) => new { vendor = a, org = b });
                var vendorPersonEntity = query.FirstOrDefault();
                if (vendorPersonEntity == null)
                    return MessageResult.FailureResult("获取供应商信息失败");

                //获取正式信息
                var companyList = await dataverseService.GetCompanyList();
                var companyCurrency = await dataverseService.GetCompanyCurrencyList();
                var vendorAppQuery = await LazyServiceProvider.LazyGetService<IVendorApplicationRepository>().GetQueryableAsync();
                var vendorOldData = JsonConvert.DeserializeObject<VendorOrganizationDetailResponseDto>(vendorAppQuery.First(f => f.Id == applicationId).UpdatePreJson);
                vendorOldData.IsDraft = false;
                vendorOldData.FinanceInfo.ForEach(f =>
                {
                    f.CompanyName = companyList.FirstOrDefault(w => w.CompanyCode == f.Company)?.CompanyName;
                    f.CurrencyName = companyCurrency.FirstOrDefault(w => w.Code == f.Currency)?.Name;
                });
                var vendorData = ObjectMapper.Map<VendorOrganizationDetailResponseDto, HciOrgChangedDetail>(vendorOldData);

                vendorData.BankName = vendorOldData.BankCode;
                //解密
                if (!string.IsNullOrEmpty(vendorData.BankCardNo))
                    vendorData.BankCardNo = AesHelper.Decryption(vendorData.BankCardNo, insightKey);
                vendorData.BankCityName = vendorOldData.BankCityName;

                if (vendorOldData.ProvinceCityName != null || vendorOldData.ProvinceCityName.Any())
                    vendorData.ProvinceCityName = $"{vendorOldData.ProvinceCityName[0] ?? ""}/{vendorOldData.ProvinceCityName[1] ?? ""}";

                //排序前端验证需要使用
                vendorData.FinanceInfo = [.. vendorData.FinanceInfo.OrderBy(o => o.Company)];
                applyData.FinanceInfo = [.. applyData.FinanceInfo.OrderBy(o => o.Company)];
                //添加财务是否可修改的flag
                vendorData.FinanceInfo.ForEach(f => f.Flag = true);
                applyData.FinanceInfo.ForEach(f => f.Flag = true);
                applyBaseData.FinanceInfo.ForEach(f =>
                {
                    f.Flag = vendorData.FinanceInfo.Select(s => s.Company).Contains(f.Company);
                    f.PaymentTermName = _commonService.GetPaymentTermName(f.Company, f.PaymentTerm);
                });


                //var vendorData = ObjectMapper.Map<Entities.Vendors.Vendor, HciOrgChangedDetail>(vendorPersonEntity.vendor);
                //ObjectMapper.Map(vendorPersonEntity.org, vendorData);

                //var provinces = await dataverseService.GetAllProvince();
                //var cities = await dataverseService.GetAllCity();

                //var bc = vendorPersonEntity.vendor.BankCity?.Split(",").ToArray();
                //vendorData.BankCity = $"{provinces.FirstOrDefault(x => x.Code == bc[0])?.Name}/{cities.FirstOrDefault(x => x.Code == bc[1])?.Name}";

                //if (!string.IsNullOrEmpty(vendorData.BankCardNo))
                //    vendorData.BankCardNo = AesHelper.Decryption(vendorData.BankCardNo, insightKey);

                //var province = provinces.FirstOrDefault(x => x.Code == vendorPersonEntity.org.Province)?.Name;
                //var city = cities.FirstOrDefault(x => x.Code == vendorPersonEntity.org.City)?.Name;
                //List<string> pc = [province, city];
                //vendorData.ProvinceCity = pc.JoinAsString("/");


                //var financeEntities = vendorFinanceQuery.Where(x => x.VendorId == vendorPersonEntity.vendor.Id).ToList();
                //var financeInfo = ObjectMapper.Map<List<VendorFinancial>, List<FinancialInformation>>(financeEntities);
                //var companies = await dataverseService.GetCompanyList();
                //financeInfo.ForEach(x =>
                //{
                //    var comCur = companies.FirstOrDefault(c => c.CompanyCode == x.Company);
                //    if (comCur != null)
                //    {
                //        x.CompanyName = comCur.CompanyName;
                //        x.CurrencyName = comCur.CompanyCurrency?.FirstOrDefault(c => c.Code == x.Currency)?.Name;
                //    }
                //});
                //vendorData.FinanceInfo = financeInfo;

                //if (!string.IsNullOrEmpty(vendorPersonEntity.vendor.AttachmentInformation))
                //{
                //    var attachmentIds = vendorPersonEntity.vendor.AttachmentInformation?.Split(',').Select(x => Guid.Parse(x)).ToList();
                //    if (attachmentIds.Count() > 0)
                //    {
                //        var attachmentEntities = attachmentQuery.Where(x => attachmentIds.Contains(x.Id)).ToList();
                //        var attachmentInfo = ObjectMapper.Map<List<Attachment>, List<UploadFileResponseDto>>(attachmentEntities);
                //        vendorData.AttachmentInformation = attachmentInfo;
                //    }
                //}
                //var changedDetail = new HciOrgChangedDetailResponseDto()
                //{
                //    VendorCode = vendorCode,
                //    SPName = applyData.VendorName,
                //    VendorType = applicationPersonal.VendorType,
                //    ApplyUserName = applicationPersonal.ApplyUserName,
                //    ApplyUserBuName = applicationPersonal.ApplyUserBuName,
                //    ApplyTime = applicationPersonal.ApplyTime,
                //    HciOrgChangedCompare = [vendorData, applyData]
                //};

                //检查变更内容，是否触发DPScheck
                await GetChangeContentDPSCheckHCI(applyBaseData);
                applyBaseData.HciOrgChangedCompare = [vendorData, applyData];
                applyBaseData.BankName = applyBaseData.BankCode;
                return MessageResult.SuccessResult(applyBaseData);
            }
            catch (Exception ex)
            {
                var msgContent = $"NonSpeakerAppService's GetVendorChangedDetailNonHcp has an error : {ex.Message}";
                _logger.LogError(msgContent);
                return await Task.FromResult(MessageResult.FailureResult(new MessageModelBase(500, msgContent)));
            }
        }

        /// <summary>
        /// 非HCI机构的变更详情
        /// </summary>
        /// <param name="applicationId"></param>
        /// <returns></returns>
        public async Task<MessageResult> GetVendorChangedDetailNonHci(Guid applicationId)
        {
            var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");
            try
            {
                var applicationDetail = await VendorApplicationOrgDetail(applicationId, VendorTypes.NonHCIInstitutionalAP);
                if (!applicationDetail.Success)
                    return MessageResult.FailureResult(applicationDetail.Message);
                var applicationData = applicationDetail.Data;
                var applicationPersonal = applicationData as VendorApplicationOrganizationDetailResponseDto;
                var vendorCode = applicationPersonal.VendorCode;

                //基本信息映射
                var applyBaseData = ObjectMapper.Map<VendorApplicationOrganizationDetailResponseDto, NonHciOrgChangedDetailResponseDto>(applicationPersonal);
                applyBaseData.IsDraft = true;
                applyBaseData.ApsPorperty = string.IsNullOrWhiteSpace(applicationPersonal.ApsPorperty) ? [] : applicationPersonal.ApsPorperty.Split(",");
                applyBaseData.ID = (Guid)applicationPersonal.ID;
                applyBaseData.LastYearSales = applyBaseData.LastYearSales;
                var applyData = ObjectMapper.Map<VendorApplicationOrganizationDetailResponseDto, NonHciOrgChangedDetail>(applicationPersonal);
                applyData.IsDraft = true;
                applyData.BankCityName = applicationPersonal.BankCityName;
                applyBaseData.BankCityName = applicationPersonal.BankCityName;
                if (applicationPersonal.ProvinceCityName != null || applicationPersonal.ProvinceCityName.Any())
                {
                    applyData.ProvinceCityName = $"{applicationPersonal.ProvinceCityName[0] ?? ""}/{applicationPersonal.ProvinceCityName[1] ?? ""}";
                    applyBaseData.ProvinceCityName = $"{applicationPersonal.ProvinceCityName[0] ?? ""}/{applicationPersonal.ProvinceCityName[1] ?? ""}";
                }

                applyData.BankName = applicationPersonal.BankCode;
                if (!string.IsNullOrEmpty(applyData.BankCardNo))
                    applyData.BankCardNo = AesHelper.Decryption(applyData.BankCardNo, insightKey);

                var vendorQuery = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();
                var vendorOrgQuery = await LazyServiceProvider.LazyGetService<IVendorOrgnizationRepository>().GetQueryableAsync();
                var vendorFinanceQuery = await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync();

                var attachmentQuery = await LazyServiceProvider.LazyGetService<IAttachmentRepository>().GetQueryableAsync();
                var commonService = LazyServiceProvider.LazyGetService<ICommonService>();
                var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();

                var query = vendorQuery.Where(x => x.VendorCode == vendorCode)//&& x.VendorType == VendorTypes.NonHCIInstitutionalAP
                    .Join(vendorOrgQuery, v => v.Id, p => p.VendorId, (a, b) => new { vendor = a, org = b });
                var vendorPersonEntity = query.FirstOrDefault();
                if (vendorPersonEntity == null)
                    return MessageResult.FailureResult("获取供应商信息失败");

                //获取正式信息
                var companyList = await dataverseService.GetCompanyList();
                var companyCurrency = await dataverseService.GetCompanyCurrencyList();
                var vendorAppQuery = await LazyServiceProvider.LazyGetService<IVendorApplicationRepository>().GetQueryableAsync();
                var vendorOldData = JsonConvert.DeserializeObject<VendorOrganizationDetailResponseDto>(vendorAppQuery.First(f => f.Id == applicationId).UpdatePreJson);
                vendorOldData.IsDraft = false;
                vendorOldData.FinanceInfo.ForEach(f =>
                {
                    f.CompanyName = companyList.FirstOrDefault(w => w.CompanyCode == f.Company)?.CompanyName;
                    f.CurrencyName = companyCurrency.FirstOrDefault(w => w.Code == f.Currency)?.Name;
                });
                var vendorData = ObjectMapper.Map<VendorOrganizationDetailResponseDto, NonHciOrgChangedDetail>(vendorOldData);
                vendorData.LastYearSales = vendorData.LastYearSales;

                vendorData.BankName = vendorOldData.BankCode;
                //解密
                if (!string.IsNullOrEmpty(vendorData.BankCardNo))
                    vendorData.BankCardNo = AesHelper.Decryption(vendorData.BankCardNo, insightKey);
                vendorData.BankCityName = vendorOldData.BankCityName;
                if (vendorOldData.ProvinceCityName != null || vendorOldData.ProvinceCityName.Any())
                    vendorData.ProvinceCityName = $"{vendorOldData.ProvinceCityName[0] ?? ""}/{vendorOldData.ProvinceCityName[1] ?? ""}";
                //排序前端验证需要使用
                vendorData.FinanceInfo = [.. vendorData.FinanceInfo.OrderBy(o => o.Company)];
                applyData.FinanceInfo = [.. applyData.FinanceInfo.OrderBy(o => o.Company)];
                //添加财务是否可修改的flag
                vendorData.FinanceInfo.ForEach(f => f.Flag = true);
                applyData.FinanceInfo.ForEach(f => f.Flag = true);
                applyBaseData.FinanceInfo.ForEach(f =>
                {
                    f.Flag = vendorData.FinanceInfo.Select(s => s.Company).Contains(f.Company);
                });

                #region 2557【非HCI机构】”变更“操作时：在财务供应商审核岗审批界面增加<退回电话验证岗> 的按钮，点击退回后则将单据退回至电话验证岗
                //非HCI的变更时的审批任务的详情
                if (applicationPersonal.VendorType == VendorTypes.NonHCIInstitutionalAP && applicationPersonal.ApplicationType == ApplicationTypes.Update && applicationPersonal.Status == Statuses.Approving)
                {
                    //去PP查询提交实例的FormData中的关于是否变更关键信息的标记isChangedCallBackInfo
                    var formData = await dataverseService.GetWorkflowInstanceFormData(applicationId);
                    if (!string.IsNullOrEmpty(formData))
                    {
                        var jObj = JsonConvert.DeserializeObject<JObject>(formData);
                        var isChangedKeyInfo = jObj["isChangedCallBackInfo"]?.ToObject<bool>() ?? false;
                        applyBaseData.IsChangedKeyInfo = isChangedKeyInfo;
                    }
                }
                #endregion

                //var vendorData = ObjectMapper.Map<Entities.Vendors.Vendor, NonHciOrgChangedDetail>(vendorPersonEntity.vendor);
                //ObjectMapper.Map(vendorPersonEntity.org, vendorData);

                //var provinces = await dataverseService.GetAllProvince();
                //var cities = await dataverseService.GetAllCity();

                //var bc = vendorPersonEntity.vendor.BankCity?.Split(",").ToArray();
                //vendorData.BankCity = $"{provinces.FirstOrDefault(x => x.Code == bc[0])?.Name}/{cities.FirstOrDefault(x => x.Code == bc[1])?.Name}";

                //if (!string.IsNullOrEmpty(vendorData.BankCardNo))
                //    vendorData.BankCardNo = AesHelper.Decryption(vendorData.BankCardNo, insightKey);

                //var province = provinces.FirstOrDefault(x => x.Code == vendorPersonEntity.org.Province)?.Name;
                //var city = cities.FirstOrDefault(x => x.Code == vendorPersonEntity.org.City)?.Name;
                //List<string> pc = [province, city];
                //vendorData.ProvinceCity = pc.JoinAsString("/");


                //var financeEntities = vendorFinanceQuery.Where(x => x.VendorId == vendorPersonEntity.vendor.Id).ToList();
                //var financeInfo = ObjectMapper.Map<List<VendorFinancial>, List<FinancialInformation>>(financeEntities);
                //var companies = await dataverseService.GetCompanyList();
                //financeInfo.ForEach(x =>
                //{
                //    var comCur = companies.FirstOrDefault(c => c.CompanyCode == x.Company);
                //    if (comCur != null)
                //    {
                //        x.CompanyName = comCur.CompanyName;
                //        x.CurrencyName = comCur.CompanyCurrency?.FirstOrDefault(c => c.Code == x.Currency)?.Name;
                //    }
                //});
                //vendorData.FinanceInfo = financeInfo;

                //if (!string.IsNullOrEmpty(vendorPersonEntity.vendor.AttachmentInformation))
                //{
                //    var attachmentIds = vendorPersonEntity.vendor.AttachmentInformation?.Split(',').Select(x => Guid.Parse(x)).ToList();
                //    if (attachmentIds.Count() > 0)
                //    {
                //        var attachmentEntities = attachmentQuery.Where(x => attachmentIds.Contains(x.Id)).ToList();
                //        var attachmentInfo = ObjectMapper.Map<List<Attachment>, List<UploadFileResponseDto>>(attachmentEntities);
                //        vendorData.AttachmentInformation = attachmentInfo;
                //    }
                //}
                //var changedDetail = new NonHciOrgChangedDetailResponseDto()
                //{
                //    VendorCode = vendorCode,
                //    SPName = applyData.VendorName,
                //    VendorType = applicationPersonal.VendorType,
                //    ApplyUserName = applicationPersonal.ApplyUserName,
                //    ApplyUserBuName = applicationPersonal.ApplyUserBuName,
                //    ApplyTime = applicationPersonal.ApplyTime,
                //    NonHciOrgChangedCompare = [vendorData, applyData]
                //};


                //检查变更内容，是否触发DPScheck
                await GetChangeContentDPSCheckNoneHCI(applyBaseData);
                applyBaseData.NonHciOrgChangedCompare = [vendorData, applyData];
                applyBaseData.BankName = applyBaseData.BankCode;
                return MessageResult.SuccessResult(applyBaseData);
            }
            catch (Exception ex)
            {
                var msgContent = $"NonSpeakerAppService's GetVendorChangedDetailNonHcp has an error : {ex.Message}";
                _logger.LogError(msgContent);
                return await Task.FromResult(MessageResult.FailureResult(new MessageModelBase(500, msgContent)));
            }
        }

        /// <summary>
        /// 获取DPSCheck是否需要审批
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        private async Task GetChangeContentDPSCheck(NonHcpPersonalChangedDetailResponseDto request)
        {
            var vendor = LazyServiceProvider.GetService<IVendorRepository>();
            var vendorPersonal = LazyServiceProvider.GetService<IVendorPersonalRepository>();
            var vendorFinancial = LazyServiceProvider.GetService<IVendorFinancialRepository>();
            var isDPSCheck = false;
            var vendorQuery = await vendor.GetQueryableAsync();
            var vendorPersonalQuery = await vendorPersonal.GetQueryableAsync();
            //供应商信息
            var vendorData = vendorQuery.Join(vendorPersonalQuery, a => a.Id, b => b.VendorId, (a, b) => new { vendorData = a, vendorPersonalData = b })
                .FirstOrDefault(f => f.vendorData.VendorCode == request.VendorCode);
            //财务信息
            var vendorFinancialData = await vendorFinancial.GetListAsync(g => g.VendorId == vendorData.vendorData.Id);

            //匹配地址信息
            if (request.ProvinceCity[0] != vendorData.vendorPersonalData.Province || request.ProvinceCity[1] != vendorData.vendorPersonalData.City ||
                request.Address != vendorData.vendorPersonalData.Address || request.PostCode != vendorData.vendorPersonalData.PostCode)
            {
                isDPSCheck = true;
            }
            //匹配公司信息
            if (request.FinanceInfo.Count != vendorFinancialData.Count)
            {
                isDPSCheck = true;
            }
            if (request.SPName != vendorData.vendorPersonalData.SPName)
            {
                isDPSCheck = true;
            }
            request.IncludeDPSCheck = isDPSCheck;
        }

        /// <summary>
        /// 获取DPSCheck是否需要审批-HCI
        /// </summary>
        /// <param name="applyBaseData"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        private async Task GetChangeContentDPSCheckHCI(HciOrgChangedDetailResponseDto request)
        {
            var vendor = LazyServiceProvider.GetService<IVendorRepository>();
            var vendorPersonal = LazyServiceProvider.GetService<IVendorOrgnizationRepository>();
            var vendorFinancial = LazyServiceProvider.GetService<IVendorFinancialRepository>();
            var isDPSCheck = false;
            var vendorQuery = await vendor.GetQueryableAsync();
            var vendorPersonalQuery = await vendorPersonal.GetQueryableAsync();
            //供应商信息
            var vendorData = vendorQuery.Join(vendorPersonalQuery, a => a.Id, b => b.VendorId, (a, b) => new { vendorData = a, vendorPersonalData = b })
                .FirstOrDefault(f => f.vendorData.VendorCode == request.VendorCode);
            //财务信息
            var vendorFinancialData = await vendorFinancial.GetListAsync(g => g.VendorId == vendorData.vendorData.Id);

            //匹配地址信息
            if (request.SPName != vendorData.vendorPersonalData.VendorName ||
                request.ProvinceCity[0].ToString() != vendorData.vendorPersonalData.Province || request.ProvinceCity[1].ToString() != vendorData.vendorPersonalData.City ||
                request.RegCertificateAddress != vendorData.vendorPersonalData.RegCertificateAddress || request.PostCode != vendorData.vendorPersonalData.PostCode)
            {
                isDPSCheck = true;
            }
            //匹配公司信息
            if (request.FinanceInfo.Count != vendorFinancialData.Count)
            {
                isDPSCheck = true;
            }
            request.IncludeDPSCheck = isDPSCheck;
        }

        /// <summary>
        /// 获取DPSCheck是否需要审批-NoneHCI
        /// </summary>
        /// <param name="applyBaseData"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        private async Task GetChangeContentDPSCheckNoneHCI(NonHciOrgChangedDetailResponseDto request)
        {
            var vendor = LazyServiceProvider.GetService<IVendorRepository>();
            var vendorPersonal = LazyServiceProvider.GetService<IVendorOrgnizationRepository>();
            var vendorFinancial = LazyServiceProvider.GetService<IVendorFinancialRepository>();
            var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");

            var isDPSCheck = false;
            var vendorQuery = await vendor.GetQueryableAsync();
            var vendorPersonalQuery = await vendorPersonal.GetQueryableAsync();
            //供应商信息
            var vendorData = vendorQuery.Join(vendorPersonalQuery, a => a.Id, b => b.VendorId, (a, b) => new { vendorData = a, vendorPersonalData = b })
                .FirstOrDefault(f => f.vendorData.VendorCode == request.VendorCode);
            //财务信息
            var vendorFinancialData = await vendorFinancial.GetListAsync(g => g.VendorId == vendorData.vendorData.Id);
            //有新增或者激活行
            var isFinancialChanged = new VendorCommon().CheckFinancialChanged(request.FinanceInfo, vendorFinancialData);

            //匹配地址信息
            if (request.ProvinceCity[0].ToString() != vendorData.vendorPersonalData.Province ||
                request.ProvinceCity[1].ToString() != vendorData.vendorPersonalData.City ||
                request.RegCertificateAddress != vendorData.vendorPersonalData.RegCertificateAddress ||
                request.PostCode != vendorData.vendorPersonalData.PostCode ||
                request.FinanceInfo.Any(a => !a.ID.HasValue) || isFinancialChanged)//2902（优先）【供应商管理】【非HCI机构】增加或激活财务信息，需要走DPS Check
            {
                isDPSCheck = true;
            }
            request.IncludeDPSCheck = isDPSCheck;
        }

        #endregion

        /// <summary>
        /// 保存(编辑)填充供应商信息-非HCI机构
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> FillVendorInfoNonHciOrg(FillVendorInfoRequestDto request)
        {
            try
            {
                var vendorApplicationQuery = LazyServiceProvider.LazyGetService<IVendorApplicationRepository>();
                var vendorApplicationOrgQuery = LazyServiceProvider.LazyGetService<IVendorApplicationOrganizationRepository>();
                var vendorApplicationFinanceQuery = LazyServiceProvider.LazyGetService<IVendorApplicationFinancialRepository>();

                //修改
                var vendorApplicationEntity = await vendorApplicationQuery.FirstOrDefaultAsync(v => v.Id == request.ID);
                if (vendorApplicationEntity == null)
                    return MessageResult.FailureResult("修改数据不存在");

                //APS属性
                if (request.ApsPorperty != null && request.ApsPorperty.Count() > 0)
                {
                    vendorApplicationEntity.ApsPorperty = request.ApsPorperty.JoinAsString(",");
                }

                //修改VendorAPPlication
                if (request.AttachmentInformation == null || request.AttachmentInformation.Count < 1)
                    vendorApplicationEntity.AttachmentInformation = null;
                else
                    vendorApplicationEntity.AttachmentInformation = request.AttachmentInformation?.Select(x => x.AttachmentId.ToString()).JoinAsString(",");

                await vendorApplicationQuery.UpdateAsync(vendorApplicationEntity);

                //修改VendorApplicationOrganization
                if (!string.IsNullOrEmpty(request.ApplyReason))
                {
                    var vendorApplicationOrgEntity = await vendorApplicationOrgQuery.FirstOrDefaultAsync(x => x.ApplicationId == request.ID);
                    if (vendorApplicationOrgEntity != null)
                    {
                        vendorApplicationOrgEntity.ApplyReason = request.ApplyReason;
                        await vendorApplicationOrgQuery.UpdateAsync(vendorApplicationOrgEntity);
                    }
                }

                //修改VendorApplicationFinanceInfo:条目要与之前的一致，不能删除不能添加；然后更新变更的字段
                var queryFinance = await vendorApplicationFinanceQuery.GetQueryableAsync();
                var vendorApplicationFinanceDataList = queryFinance.Where(x => x.ApplicationId == request.ID).ToList();
                if (vendorApplicationFinanceDataList.Count != request.FinanceInfo.Count)
                    return MessageResult.FailureResult("财务信息条目错误，财务信息条目不能增减");

                if (vendorApplicationFinanceDataList.Count > 0)
                {
                    //var newFinanceIds = request.FinanceInfo.Select(x => x.ID);
                    var financeIds = vendorApplicationFinanceDataList.Select(x => x.Id).ToList();
                    var diff = request.FinanceInfo.Where(a => !vendorApplicationFinanceDataList.Any(b => b.Id == a.ID));
                    if (diff.Any())
                        return MessageResult.FailureResult("财务条目ID不一致");

                    foreach (var item in vendorApplicationFinanceDataList)
                    {
                        var newFinance = request.FinanceInfo.FirstOrDefault(x => x.ID == item.Id);
                        //item.PaymentTerm = newFinance.PaymentTerm.Split("_").Length == 3 ? newFinance.PaymentTerm.Split("_")[1] : newFinance.PaymentTerm;
                        item.PaymentTerm = newFinance.PaymentTerm;//paymentterm code值,有3部分。讲者只存储了中间一部分，非HCI存储3部分，互不影响
                        item.DpoCategory = newFinance.DpoCategory;
                        item.SpendingCategory = newFinance.SpendingCategory;
                    }

                    await vendorApplicationFinanceQuery.UpdateManyAsync(vendorApplicationFinanceDataList);
                }

                return await Task.FromResult(MessageResult.SuccessResult());
            }
            catch (Exception ex)
            {
                var msgContent = $"NonSpeakerAppService's FillVendorInfoNonHciOrg has an error : {ex.Message}";
                _logger.LogError(msgContent);
                return await Task.FromResult(MessageResult.FailureResult(new MessageModelBase(500, msgContent)));
            }
        }

        #region 正式供应商-编辑APS属性
        public async Task<MessageResult> GetVendorApsNonHci(Guid id)
        {
            var vendorQuery = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();

            var data = vendorQuery.FirstOrDefault(x => x.Id == id);

            if (data == null)
                return MessageResult.FailureResult("id错误");

            var response = new GetVendorApsNonHciResponseDto { Id = data.Id };

            if (data.ApsPorperty == null || string.IsNullOrEmpty(data.ApsPorperty))
                response.ApsProperties = [];
            else
                response.ApsProperties = data.ApsPorperty.Split(',');

            return MessageResult.SuccessResult(response);
        }

        public async Task<MessageResult> SetVendorApsNonHci(SetVendorApsNonHciRequestDto request)
        {
            var vendorRepository = LazyServiceProvider.LazyGetService<IVendorRepository>();
            var vendorQuery = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();

            var data = vendorQuery.FirstOrDefault(x => x.Id == request.Id);

            if (data == null)
                return MessageResult.FailureResult("id错误");

            if (request.ApsProperties == null)
                return MessageResult.FailureResult("参数错误");

            data.ApsPorperty = request.ApsProperties.Length < 1 ? null : request.ApsProperties.JoinAsString(",");

            await vendorRepository.UpdateAsync(data);

            return MessageResult.SuccessResult();
        }
        #endregion

        /// <summary>
        /// 导出我发起的供应商新建
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<Stream> ExportInitialNspCreateListAsync(NspCreateRequestDto request)
        {
            try
            {
                var nspCreate = await GetInitialNspCreateList(request, true);
                MemoryStream stream = new();
                stream.SaveAs(nspCreate, true, "SheetName");
                stream.Seek(0, SeekOrigin.Begin);
                return stream;
            }
            catch (Exception ex)
            {
                _logger.LogError($"NonSpeakerAppService's ExportInitialNspCreateListAsync has an error : {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 查询我发起的供应商新建
        /// </summary>
        /// <param name="request"></param>
        /// <param name="isExcept"></param>
        /// <returns></returns>
        private async Task<List<InitialNspCreateDto>> GetInitialNspCreateList(NspCreateRequestDto request, bool isExcept = false)
        {
            var vendorApplication = await LazyServiceProvider.LazyGetService<IVendorApplicationRepository>().GetQueryableAsync();
            var NspCreateList = vendorApplication.Where(w => w.VendorType != VendorTypes.HCPPerson &&
            w.ApplyUserId == CurrentUser.Id && w.ApplicationType == ApplicationTypes.Create).AsQueryable();

            var result = new List<InitialNspCreateDto>();
            if (string.IsNullOrEmpty(request.status.ToString()))
            {
                _logger.LogError("导出供应商新建数据status参数未填写!");
                return null;
            }
            else if (request.status == ProcessingStatus.PendingProcessing)
            {
                result = NspCreateList.Where(w => w.Status == Statuses.Returned || w.Status == Statuses.Withdraw)
                .Select(s => new InitialNspCreateDto
                {
                    VendorCode = s.VendorCode,
                    Status = s.Status,
                })
                .ToList();
            }
            else if (request.status == ProcessingStatus.Progressing)
            {
                result = NspCreateList.Where(w => w.Status == Statuses.Approving)
                .Select(s => new InitialNspCreateDto
                {
                    VendorCode = s.VendorCode,
                    Status = s.Status,
                })
                .ToList();
            }
            else if (request.status == ProcessingStatus.Completed)
            {
                result = NspCreateList.Where(w => w.Status == Statuses.Rejected || w.Status == Statuses.Delete || w.Status == Statuses.Passed)
                .Select(s => new InitialNspCreateDto
                {
                    VendorCode = s.VendorCode,
                    Status = s.Status,
                })
                .ToList();
            }
            else
            {
                _logger.LogError("导出供应商新建数据status参数出错!");
                return null;
            }
            var resultList = new List<InitialNspCreateDto>();
            if (!isExcept)
            {
                //这里分页减少数据量查询
                resultList = result.Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToList();
            }
            else
            {
                resultList = result.ToList();
            }
            return resultList;
        }

        /// <summary>
        /// 导出我审批的供应商新建
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<Stream> ExportApprovalNspCreateListAsync(NspCreateRequestDto request)
        {
            try
            {
                var nspCreate = await GetApprovalNspCreateList(request, true);
                MemoryStream stream = new();
                stream.SaveAs(nspCreate, true, "SheetName");
                stream.Seek(0, SeekOrigin.Begin);
                return stream;
            }
            catch (Exception ex)
            {
                _logger.LogError($"NonSpeakerAppService's ExportApprovalNspCreateListAsync has an error : {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 查询我审批的供应商新建
        /// </summary>
        /// <param name="request"></param>
        /// <param name="isExcept"></param>
        /// <returns></returns>
        private async Task<List<ApprovalNspCreateDto>> GetApprovalNspCreateList(NspCreateRequestDto request, bool isExcept = false)
        {
            var vendorQuery = await LazyServiceProvider.LazyGetService<IVendorApplicationRepository>().GetQueryableAsync();
            var vendorPersonalQuery = await LazyServiceProvider.LazyGetService<IVendorApplicationPersonalRepository>().GetQueryableAsync();
            var vendorApplicationOrgQuery = await LazyServiceProvider.LazyGetService<IVendorApplicationOrganizationRepository>().GetQueryableAsync();

            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var taskRecords = await dataverseService.GetApprovelTaskAsync(CurrentUser.Id.ToString(),
                [WorkflowTypeName.SupplierRequestNonHCPPerson, WorkflowTypeName.SupplierRequestNonHCIInstitution, WorkflowTypeName.SupplierRequestHCIInstitution]);

            var taskRecordList = new List<WorkflowTaskDto>();

            if (string.IsNullOrEmpty(request.status.ToString()))
            {
                _logger.LogError("导出供应商新建数据status参数未填写!");
                return null;
            }
            else if (request.status == ProcessingStatus.PendingProcessing)
            {
                taskRecordList = taskRecords.Where(p => p.Status == ApprovalStatus.PendingForApproval).ToList();
            }
            else if (request.status == ProcessingStatus.Completed)
            {
                taskRecordList = taskRecords.Where(p => (p.Status != ApprovalStatus.PendingForApproval && p.Status != ApprovalStatus.Approving)).ToList();
            }
            else
            {
                _logger.LogError("导出供应商新建数据status参数出错!");
                return null;
            }

            //供应商机构链表供应商
            var nspCreateList = vendorQuery.Join(vendorPersonalQuery, a => a.Id, b => b.ApplicationId, (a, b) => new { vendorQuery = a, vendorPersonalQuery = b })
            .Where(w => w.vendorQuery.ApplicationType == ApplicationTypes.Create)
            .Where(w => taskRecordList.Select(s => s.FormId).Contains(w.vendorQuery.Id))//筛选需要展示的业务数据
            .Select(g => new
            {
                Id = g.vendorQuery.Id,
                VendorType = g.vendorQuery.VendorType,
                ApplicationCode = g.vendorQuery.ApplicationCode,
                SPName = g.vendorPersonalQuery.SPName,
                ApplyUserId = g.vendorQuery.ApplyUserId,
                ApplyUserBu = g.vendorQuery.ApplyUserBu,
                ApprovalStatus = g.vendorQuery.Status,
                ApplyTime = g.vendorQuery.ApplyTime,
            })
            .Union(
                vendorQuery.Join(vendorApplicationOrgQuery, a => a.Id, b => b.ApplicationId, (a, b) => new { vendorQuery = a, vendorOrgQuery = b })
                .Where(w => w.vendorQuery.ApplicationType == ApplicationTypes.Create)
                .Where(w => taskRecordList.Select(s => s.FormId).Contains(w.vendorQuery.Id))//筛选需要展示的业务数据
                .Select(g => new
                {
                    Id = g.vendorQuery.Id,
                    VendorType = g.vendorQuery.VendorType,
                    ApplicationCode = g.vendorQuery.ApplicationCode,
                    SPName = g.vendorOrgQuery.VendorName,
                    ApplyUserId = g.vendorQuery.ApplyUserId,
                    ApplyUserBu = g.vendorQuery.ApplyUserBu,
                    ApprovalStatus = g.vendorQuery.Status,
                    ApplyTime = g.vendorQuery.ApplyTime,
                })
            )
            .Select(g => new ApprovalNspCreateDto
            {
                VendorCode = g.SPName,
                Status = g.ApprovalStatus,
            })
            .ToList();

            var resultList = new List<ApprovalNspCreateDto>();
            if (!isExcept)
            {
                //这里分页减少数据量查询
                resultList = nspCreateList.Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToList();
            }
            else
            {
                resultList = nspCreateList.ToList();
            }
            return resultList;
        }

        /// <summary>
        /// 电话验证审批岗上传附件
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> SaveNonHciTelValidationAsync(SaveSpeakerDPSRequestDto request)
        {
            var vendorApplicationRepository = LazyServiceProvider.LazyGetService<IVendorApplicationRepository>();
            var vendorApplication = await vendorApplicationRepository.FirstOrDefaultAsync(f => f.Id == request.ID);
            if (vendorApplication == null)
                return MessageResult.FailureResult("未找到相关数据");

            //修改相应供APS信息表
            vendorApplication.TelValidationAttachmentIds = request.AttachmentInformation.Count > 0 ? string.Join(",", request.AttachmentInformation.Select(s => s.AttachmentId)) : "";

            await vendorApplicationRepository.UpdateAsync(vendorApplication);
            return MessageResult.SuccessResult(request.ID);
        }
    }
}
