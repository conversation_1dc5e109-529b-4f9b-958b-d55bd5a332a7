﻿using System;
using System.ComponentModel.DataAnnotations;
using MiniExcelLibs.Attributes;

namespace Abbott.SpeakerPortal.Contracts.Budget.FocSubbudget
{
    public class CreatesFocSubBudgetDto : FocMothlyQtyTextDto
    {
        /// <summary>
        /// 主预算编码
        /// </summary>
        public string MasterBudgetCode { get; set; }
        /// <summary>
        /// 成本中心
        /// </summary>
        public string CostCenter { get; set; }
        /// <summary>
        /// 大区
        /// </summary>
        public string Region { get; set; }
        /// <summary>
        /// 产品ID
        /// </summary>
        public Guid ProductId { get; set; }
        /// <summary>
        /// 产品简称
        /// </summary>
        public string ProductName { get; set; }
        /// <summary>
        /// 品牌信息编码
        /// </summary>
        public string ProductMCode { get; set; }

        /// <summary>
        /// 负责人邮箱
        /// </summary>
        public string OwnerEmail { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }
        ///// <summary>
        ///// 是否开启
        ///// </summary>
        //public string StatusText { get; set; }
    }
}
