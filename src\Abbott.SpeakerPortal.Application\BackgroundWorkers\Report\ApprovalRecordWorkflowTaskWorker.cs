﻿using Abbott.SpeakerPortal.Contracts.Report;
using Hangfire;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.BackgroundWorkers.Report
{
    public class ApprovalRecordWorkflowTaskWorker : SpeakerPortalBackgroundWorkerBase
    {
        public ApprovalRecordWorkflowTaskWorker()
        {
            CronExpression = Cron.Hourly(1);//每个小时，整点执行  先将必要数据写入WorkflowTask表，后续审批记录报表需要
        }

        public override async Task DoWorkAsync(CancellationToken cancellationToken = default)
        {
            //逻辑处理
            var reportService = LazyServiceProvider.LazyGetService<IReportService>();
            await reportService.FillWorkflowTasksFormNameAsync();
        }
    }
}
