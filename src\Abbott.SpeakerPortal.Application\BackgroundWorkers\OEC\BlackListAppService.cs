﻿using Abbott.SpeakerPortal.Contracts.Integration.Dspot;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.VendorApplications;
using Abbott.SpeakerPortal.Enums.Integration;
using Abbott.SpeakerPortal.Extension;
using Flurl.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;

using Abbott.SpeakerPortal.Entities.VendorApplications;

using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.User;
using Abbott.SpeakerPortal.Utils;
using Abbott.SpeakerPortal.Entities.VendorApplicationPersonals;
using Abbott.SpeakerPortal.Contracts.Vendor.NonSpeaker;
using Volo.Abp.BackgroundWorkers.Hangfire;
using System.Threading;
using Abbott.SpeakerPortal.Entities.VendorBlackLists;
using Abbott.SpeakerPortal.Entities.VendorOrgnizations;
using Abbott.SpeakerPortal.Entities.VendorPersonals;
using Volo.Abp.Identity;
using Abbott.SpeakerPortal.Entities.VendorBlackListOperHistorys;
using Volo.Abp.Domain.Repositories;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.OEC.BlackList;
using Hangfire.Storage;
using System.Data;
using Hangfire;
using Abbott.SpeakerPortal.Entities.Integration.Dspot;
using Abbott.SpeakerPortal.Entities.VendorFinancials;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPRApplication;

namespace Abbott.SpeakerPortal.BackgroundWorkers.OEC
{
    public class BlackListAppService : SpeakerPortalBackgroundWorkerBase // SpeakerPortalAppService, IBlackListAppService//
    {                
        
        public BlackListAppService(IServiceProvider serviceProvider)
        {            
            CronExpression = Cron.Daily(1);
        }
        
        public override async Task DoWorkAsync(CancellationToken cancellationToken = default)
        {
            //逻辑处理            
            await LazyServiceProvider.LazyGetService<IBlackListService>().UpdateStatus();
        }      
    }
}