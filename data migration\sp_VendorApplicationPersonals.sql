CREATE PROCEDURE dbo.sp_VendorApplicationPersonals
AS 
begin	
--供应商申请单-个人供应商相关信息
select * into #VendorApplicationPersonals_Tmp from (
select 
newid() AS Id,--自动生成的uuid
tsi.ProcInstId,
vt.Id AS ApplicationId,--对于VendorType=1/2的个人类型供应商，此处填入对应VendorApplications的Id，用于标记该行为个人供应商申请单对应的扩展信息
sex AS Sex,--1代表男，2代表女，3或NULL代表为空
--sex AS ,--
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/idType_Text)[1]', 'nvarchar(50)') AS CardType,--对于更新申请，需要查询xml中的证件类型文字，得到的结果对应字典值：ID Card/身份证-身份证；护照-护照；其他-其他；军（警）官证-军(警)官证，根据字典值填入对应的字典code，若为空或匹配出的结果并非如上的值则填写为空
--idType_Text AS ,--
idNumber AS CardNo,--
--CertificatesNum AS ,--
cast('' as nvarchar(2000)) AS CardPic,--基于ProcInstId查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到AttachmentInfoGrid下所有的up_Id，并按照up_FileName包含文字"身份证"的条件筛选出所有身份证相关支持文件ID，如果查询到多个则按"正面"再次匹配出唯一的证件正面照片，填入匹配出的NexBPM支持文件ID，若无法匹配出该文件名则留空
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/SupplierProvince)[1]', 'nvarchar(50)')  AS Province,--基于ProcInstId查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到该字段(若成功匹配至省份主数据里的值，下方城市需要基于该省份的城市进行查询)，若无该值则以下方城市的清洗结果补充填回
supplierCity AS City,--对应供应商新建/激活申请
----City AS ,--对应供应商变更申请
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/IdentityCardResidence)[1]', 'nvarchar(50)') AS Address,--对应供应商新建/激活申请，变更申请时该值为空
postCode AS PostCode,--对应供应商新建/激活申请，变更申请时该值为空
'{}' AS ExtraProperties,--默认填写为"{}"
'' AS ConcurrencyStamp,--?
'' AS CreationTime,--与对应的VendorApplications记录保持一致即可
'' AS CreatorId,--与对应的VendorApplications记录保持一致即可
'' AS LastModificationTime,--默认为空
'' AS LastModifierId,--默认为空
'0' AS IsDeleted,--默认为0
'' AS DeleterId,--默认为空
'' AS DeletionTime,--默认为空
supplierCNName AS SPName,--
--supplierCNName AS ,--
belongInstitution AS AffiliationOrgan,--对应供应商新建/激活申请，变更申请时不会填写该值
supplierEmail AS Email--
--Mail AS --
from PLATFORM_ABBOTT_Stg.dbo.ODS_AUTO_BIZ_T_SupplierApplication_Info tsi
join PLATFORM_ABBOTT_Stg.dbo.VendorApplications_Tmp vt
on tsi.ProcInstId =vt.ProcInstId
join  PLATFORM_ABBOTT_Stg.dbo.ODS_T_FORMINSTANCE_GLOBAL a
on tsi.ProcInstId =a.ProcInstId
union ALL 
select 
newid() AS Id,--自动生成的uuid
tsi.ProcInstId,
vt.Id AS ApplicationId,--对于VendorType=1/2的个人类型供应商，此处填入对应VendorApplications的Id，用于标记该行为个人供应商申请单对应的扩展信息
sex AS Sex,--1代表男，2代表女，3或NULL代表为空
--sex AS ,--
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/idType_Text)[1]', 'nvarchar(50)') AS CardType,--对于更新申请，需要查询xml中的证件类型文字，得到的结果对应字典值：ID Card/身份证-身份证；护照-护照；其他-其他；军（警）官证-军(警)官证，根据字典值填入对应的字典code，若为空或匹配出的结果并非如上的值则填写为空
CertificatesNum AS CardNo,--
cast('' as nvarchar(2000)) AS CardPic,--基于ProcInstId查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到AttachmentInfoGrid下所有的up_Id，并按照up_FileName包含文字"身份证"的条件筛选出所有身份证相关支持文件ID，如果查询到多个则按"正面"再次匹配出唯一的证件正面照片，填入匹配出的NexBPM支持文件ID，若无法匹配出该文件名则留空
cast(XmlContent as XML).value('(/root/SupplierApplication_supplierInfoBlock_MainStore/SupplierProvince)[1]', 'nvarchar(50)')  AS Province,--基于ProcInstId查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到该字段(若成功匹配至省份主数据里的值，下方城市需要基于该省份的城市进行查询)，若无该值则以下方城市的清洗结果补充填回
City AS City,--对应供应商新建/激活申请
'' AS Address,--对应供应商新建/激活申请，变更申请时该值为空
'' AS PostCode,--对应供应商新建/激活申请，变更申请时该值为空
'{}' AS ExtraProperties,--默认填写为"{}"
'' AS ConcurrencyStamp,--?
vt.CreationTime AS CreationTime,--与对应的VendorApplications记录保持一致即可
vt.CreatorId AS CreatorId,--与对应的VendorApplications记录保持一致即可
'' AS LastModificationTime,--默认为空
'' AS LastModifierId,--默认为空
'0' AS IsDeleted,--默认为0
'' AS DeleterId,--默认为空
'' AS DeletionTime,--默认为空
supplierCNName AS SPName,--
'' AS AffiliationOrgan,--对应供应商新建/激活申请，变更申请时不会填写该值
Mail AS Email--
from PLATFORM_ABBOTT_Stg.dbo.ODS_AUTO_BIZ_T_HcpLevelApplication_info tsi
join PLATFORM_ABBOTT_Stg.dbo.VendorApplications_Tmp vt
on tsi.ProcInstId =vt.ProcInstId
join  PLATFORM_ABBOTT_Stg.dbo.ODS_T_FORMINSTANCE_GLOBAL a
on tsi.ProcInstId =a.ProcInstId)A
--更新CardPic
update a set a.CardPic=b.up_Id from #VendorApplicationPersonals_Tmp a
join (SELECT 
    ProcInstId,
    STRING_AGG(up_Id, ', ') WITHIN GROUP (ORDER BY up_Id) AS up_Id
FROM 
    (select ProcInstId,
		up_Id,
		File_Name,
		Emp_ID 
	from (
	select XmlContent,ProcInstId,
	up_Id.value('.', 'nvarchar(100)') as up_Id 
	from (
		SELECT
		cast (XmlContent as XML) as  XmlContent,
		ProcInstId
		FROM PLATFORM_ABBOTT_Stg.dbo.ODS_T_FORMINSTANCE_GLOBAL)A
		CROSS APPLY XmlContent.nodes('/root/AttachmentInfoGrid/row/up_Id') AS XMLTable(up_Id)) B
		join PLATFORM_ABBOTT_Stg.dbo.ODS_T_METAFILE tm
		on B.up_Id=tm.file_id)YourTable
		where File_Name like N'%身份证%' and File_Name not like N'%反面%'
		GROUP BY  ProcInstId
		 )b
on a.ProcInstId=b.ProcInstId

--写入目标表
IF OBJECT_ID(N'PLATFORM_ABBOTT_Stg.dbo.VendorApplicationPersonals_Tmp', N'U') IS NOT NULL
BEGIN
	update a 
	set a.ApplicationId        = b.ApplicationId
       ,a.Sex                  = b.Sex
       ,a.CardType             = b.CardType
       ,a.CardNo               = b.CardNo
       ,a.CardPic              = b.CardPic
       ,a.Province             = b.Province
       ,a.City                 = b.City
       ,a.Address              = b.Address
       ,a.PostCode             = b.PostCode
       ,a.ExtraProperties      = b.ExtraProperties
       ,a.ConcurrencyStamp     = b.ConcurrencyStamp
       ,a.CreationTime         = b.CreationTime
       ,a.CreatorId            = b.CreatorId
       ,a.LastModificationTime = b.LastModificationTime
       ,a.LastModifierId       = b.LastModifierId
       ,a.IsDeleted            = b.IsDeleted
       ,a.DeleterId            = b.DeleterId
       ,a.DeletionTime         = b.DeletionTime
       ,a.SPName               = b.SPName
       ,a.AffiliationOrgan     = b.AffiliationOrgan
       ,a.Email                = b.Email
    from PLATFORM_ABBOTT_Stg.dbo.VendorApplicationPersonals_Tmp a
    left join #VendorApplicationPersonals_Tmp b on a.ProcInstId = b.ProcInstId
    
    insert into PLATFORM_ABBOTT_Stg.dbo.VendorApplicationPersonals_Tmp 
    select a.Id        
          ,a.ProcInstId
          ,a.ApplicationId 
          ,a.Sex                
          ,a.CardType           
          ,a.CardNo             
          ,a.CardPic            
          ,a.Province           
          ,a.City               
          ,a.Address            
          ,a.PostCode           
          ,a.ExtraProperties    
          ,a.ConcurrencyStamp   
          ,a.CreationTime       
          ,a.CreatorId          
          ,a.LastModificationTime
          ,a.LastModifierId     
          ,a.IsDeleted          
          ,a.DeleterId          
          ,a.DeletionTime       
          ,a.SPName             
          ,a.AffiliationOrgan   
          ,a.Email              
     from #VendorApplicationPersonals_Tmp a 
     where not exists (select * from PLATFORM_ABBOTT_Stg.dbo.VendorApplicationPersonals_Tmp where a.ProcInstId = ProcInstId)
	PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select * into PLATFORM_ABBOTT_Stg.dbo.VendorApplicationPersonals_Tmp from #VendorApplicationPersonals_Tmp
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END

end;
