CREATE PROCEDURE dbo.sp_VendorApplicationFinancials_ns
AS 
BEGIN
	select 
vaft.[Id]
,vaft.[ApplicationId]
,vaft.[Company]
,vaft.[Currency]
,vaft.[VendorCode]
,vaft.[AbbottBank]
,vaft.[VendorType]
,vaft.[Division]
,vaft.[PayType]
,vaft.[CountryCode]
,vaft.[BankType]
,sd.spk_code as [DpoCategory]
,vaft.[PaymentTerm]
,vaft.[BankNo]
,vaft.[ExtraProperties]
,vaft.[ConcurrencyStamp]
,vaft.[CreationTime]
,ss.spk_NexBPMCode  as [CreatorId]
,vaft.[LastModificationTime]
,vaft.[LastModifierId]
,vaft.[IsDeleted]
,vaft.[DeleterId]
,vaft.[DeletionTime]
,vaft.[SpendingCategory]
into #VendorApplicationFinancials
from VendorApplicationFinancials_Tmp vaft 
left join spk_staffmasterdata ss 
on vaft.CreatorId COLLATE SQL_Latin1_General_CP1_CI_AS=ss.bpm_id 
left join spk_dictionary sd 
on vaft.DpoCategory =sd.spk_Name 

 IF OBJECT_ID(N'PLATFORM_ABBOTT_Dev.dbo.VendorApplicationFinancials ', N'U') IS NOT NULL
	BEGIN
		drop table PLATFORM_ABBOTT_Dev.dbo.VendorApplicationFinancials
		select *
        into PLATFORM_ABBOTT_Dev.dbo.VendorApplicationFinancials from #VendorApplicationFinancials
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into PLATFORM_ABBOTT_Dev.dbo.VendorApplicationFinancials from #VendorApplicationFinancials
    -- select * from #vendor_tbl
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END

END
