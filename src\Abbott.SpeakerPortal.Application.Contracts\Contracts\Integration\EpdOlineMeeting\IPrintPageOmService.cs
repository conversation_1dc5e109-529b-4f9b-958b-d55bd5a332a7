﻿using Abbott.SpeakerPortal.Domain.Shared.Models;

using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.Contracts.Integration.EpdOlineMeeting
{
    public interface IPrintPageOmService
    {
        Task<MessageResult> GetPRPrintPageContent(OmPrintPageRequestDto request);

        Task<MessageResult> GetPAPrintPageContent(OmPrintPageRequestDto request);

        /// <summary>
        /// 获取PR打印页Html
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<MessageResult> GetPRPrintPageAsync(OmAuthorizationDto request);

        /// <summary>
        /// 获取PA打印页Html
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<MessageResult> GetPAPrintPageAsync(OmAuthorizationDto request);
    }
}
