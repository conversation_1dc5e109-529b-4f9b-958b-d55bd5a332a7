-- drop table #Attachments
select 
TRY_CONVERT(UNIQUEIDENTIFIER,Id) Id,--生成的唯一GUID
isnull(FunctionModule,'null') FunctionModule,--根据附件ID，在单据xml文件中查询到对应附件后，再根据单据类型设置对应数字，规则见底部
FileName,--文件名
    CASE 
        WHEN SUBSTRING(Size, PATINDEX('%[KkMmBb]%', Size), 2) = 'KB' OR SUBSTRING(Size, PATINDEX('%[KkMmBb]%', Size), 2) = 'kb' THEN CAST(SUBSTRING(Size, 1, PATINDEX('%[KkMmBb]%', Size) - 1) AS float) * 1024
        WHEN SUBSTRING(Size, PATINDEX('%[KkMmBb]%', Size), 2) = 'MB' OR SUBSTRING(Size, PATINDEX('%[KkMmBb]%', Size), 2) = 'mb' THEN CAST(SUBSTRING(Size, 1, PATINDEX('%[KkMmBb]%', Size) - 1) AS float) * 1024 * 1024
        ELSE CAST(SUBSTRING(Size, 1, PATINDEX('%[KkMmBb]%', Size) - 1) AS float) -- Assuming the default is Bytes
    END AS   Size,--文件大小，转换为B后填入
Suffix,--文件后缀
FilePath,--根据BPM原有地址找到文件后转移至NexBPM目录下，根据新的目录地址填入
ExtraProperties,--默认填写为"{}"
ConcurrencyStamp,--?
isnull(CreationTime,'1900-01-01') CreationTime,--创建时间
TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([CreatorId],'00000000-0000-0000-0000-000000000000'))  CreatorId,--导入人(以此处ID作为员工主数据中的BPM编码，匹配出员工主数据ID后填入)
GETDATE() LastModificationTime,--留空
'00000000-0000-0000-0000-000000000000' as LastModifierId,--留空
IsDeleted,--默认填写为0
'00000000-0000-0000-0000-000000000000' as DeleterId,--留空
'1900-01-01' as DeletionTime--留空
into #Attachments
from (SELECT DISTINCT *,ROW_NUMBER() OVER(PARTITION BY Id ORDER BY Id) RK FROM PLATFORM_ABBOTT_Stg.dbo.Attachments) a 
where RK = 1 


use Speaker_Portal_STG;

update a 
set  a.FunctionModule       = b.FunctionModule
    ,a.FileName             = b.FileName
    ,a.Size                 = b.Size
    ,a.Suffix               = b.Suffix
    ,a.FilePath             = b.FilePath
    ,a.ExtraProperties      = b.ExtraProperties
    ,a.ConcurrencyStamp     = b.ConcurrencyStamp
    ,a.CreationTime         = b.CreationTime
    ,a.CreatorId            = b.CreatorId
    ,a.LastModificationTime = b.LastModificationTime
    ,a.LastModifierId       = b.LastModifierId
    ,a.IsDeleted            = b.IsDeleted
    ,a.DeleterId            = b.DeleterId
    ,a.DeletionTime         = b.DeletionTime
from dbo.Attachments a
left join #Attachments b on a.id = b.id

insert into dbo.Attachments
select Id
       ,FunctionModule
       ,FileName
       ,Size
       ,Suffix
       ,FilePath
       ,ExtraProperties
       ,ConcurrencyStamp
       ,CreationTime
       ,CreatorId
       ,LastModificationTime
       ,LastModifierId
       ,IsDeleted
       ,DeleterId
       ,DeletionTime
from #Attachments a 
where not exists (select * from dbo.Attachments where id = a.id)

