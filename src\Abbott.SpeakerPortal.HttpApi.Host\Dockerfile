#See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
ENV TZ Asia/Shanghai
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build

ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["NuGet.Config", "."]
COPY ["src/Abbott.SpeakerPortal.HttpApi.Host/Abbott.SpeakerPortal.HttpApi.Host.csproj", "src/Abbott.SpeakerPortal.HttpApi.Host/"]
COPY ["src/Abbott.SpeakerPortal.Application/Abbott.SpeakerPortal.Application.csproj", "src/Abbott.SpeakerPortal.Application/"]
COPY ["src/Abbott.SpeakerPortal.Dataverse.Contracts/Abbott.SpeakerPortal.Dataverse.Contracts.csproj", "src/Abbott.SpeakerPortal.Dataverse.Contracts/"]
COPY ["src/Abbott.SpeakerPortal.Domain/Abbott.SpeakerPortal.Domain.csproj", "src/Abbott.SpeakerPortal.Domain/"]
COPY ["src/Abbott.SpeakerPortal.Domain.Shared/Abbott.SpeakerPortal.Domain.Shared.csproj", "src/Abbott.SpeakerPortal.Domain.Shared/"]
COPY ["src/Abbott.SpeakerPortal.Application.Contracts/Abbott.SpeakerPortal.Application.Contracts.csproj", "src/Abbott.SpeakerPortal.Application.Contracts/"]
COPY ["src/Abbott.SpeakerPortal.Dataverse/Abbott.SpeakerPortal.Dataverse.csproj", "src/Abbott.SpeakerPortal.Dataverse/"]
COPY ["src/Abbott.SpeakerPortal.EntityFrameworkCore/Abbott.SpeakerPortal.EntityFrameworkCore.csproj", "src/Abbott.SpeakerPortal.EntityFrameworkCore/"]
COPY ["src/Abbott.SpeakerPortal.HttpApi/Abbott.SpeakerPortal.HttpApi.csproj", "src/Abbott.SpeakerPortal.HttpApi/"]
#RUN dotnet nuget add source https://nuget.cdn.azure.cn/v3/index.json
RUN dotnet restore "./src/Abbott.SpeakerPortal.HttpApi.Host/./Abbott.SpeakerPortal.HttpApi.Host.csproj"
COPY . .
WORKDIR "/src/src/Abbott.SpeakerPortal.HttpApi.Host"
RUN dotnet build "./Abbott.SpeakerPortal.HttpApi.Host.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./Abbott.SpeakerPortal.HttpApi.Host.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Abbott.SpeakerPortal.HttpApi.Host.dll"]