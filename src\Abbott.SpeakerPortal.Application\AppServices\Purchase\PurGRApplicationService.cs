﻿using Abbott.SpeakerPortal.Contracts.Agent;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.Attachment;
using Abbott.SpeakerPortal.Contracts.Purchase.PurGRApplication;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.Common.Attachment;
using Abbott.SpeakerPortal.Entities.Integration.EpdOnlineMeeting;
using Abbott.SpeakerPortal.Entities.Purchase.PurGRApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPOApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Entities.VendorOrgnizations;
using Abbott.SpeakerPortal.Entities.VendorPersonals;
using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Extension;

using DocumentFormat.OpenXml.VariantTypes;
using Abbott.SpeakerPortal.Utils;
using Microsoft.AspNetCore.Mvc.ApplicationModels;

using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using static Abbott.SpeakerPortal.Enums.Purchase;
using static Abbott.SpeakerPortal.Enums.PurGRStatus;
using static OpenIddict.Abstractions.OpenIddictConstants;
using Abbott.SpeakerPortal.Person;
using Abbott.SpeakerPortal.Entities.Common.Workflow.WorkflowTasks;
using Abbott.SpeakerPortal.Consts;
using static Abbott.SpeakerPortal.Enums.PurPAStatus;
using Abbott.SpeakerPortal.Entities.Integration.Dspot;
using Abbott.SpeakerPortal.Entities.VendorFinancials;
using Abbott.SpeakerPortal.BackgroundWorkers.BPMEmail;
using Abbott.SpeakerPortal.Contracts.Common.BPMEmail;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;
using Microsoft.Extensions.Configuration;
using Hangfire;

namespace Abbott.SpeakerPortal.AppServices.Purchase
{
    public partial class PurGRApplicationService : SpeakerPortalAppService, IPurGRApplicationService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IConfiguration _configuration;
        public PurGRApplicationService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _configuration = _serviceProvider.GetService<IConfiguration>();
        }

        #region 采购管理—收货列表
        /// <summary>
        /// 采购管理—收货列表
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<PurGRApplicationResponseDto>> GetPurGRApplicationListAsync(GRListSearchRequestDto requestDto)
        {
            var result = new PagedResultDto<PurGRApplicationResponseDto>();
            var querygr = await LazyServiceProvider.LazyGetService<IPurGRApplicationReadonlyRepository>().GetQueryableAsync();
            var querypr = await LazyServiceProvider.LazyGetService<IPurPRApplicationReadonlyRepository>().GetQueryableAsync();
            var querypo = await LazyServiceProvider.LazyGetService<IPurPOApplicationReadonlyRepository>().GetQueryableAsync();
            var queryGrLinq = querygr
                            .WhereIf(requestDto.Status.HasValue, a => a.Status.Equals(requestDto.Status))
                            .WhereIf(requestDto.ApplyUserId != null, a => a.ApplyUserId == requestDto.ApplyUserId)
                            .WhereIf(!string.IsNullOrWhiteSpace(requestDto.ApplyUserBuName), a => a.ApplyUserBuName.Contains(requestDto.ApplyUserBuName))
                            .WhereIf(!string.IsNullOrWhiteSpace(requestDto.ApplicationCode), a => a.ApplicationCode.Contains(requestDto.ApplicationCode))
                            .WhereIf(!string.IsNullOrWhiteSpace(requestDto.BudgetCode), a => a.BudgetCode.Contains(requestDto.BudgetCode))
                            .WhereIf(!string.IsNullOrWhiteSpace(requestDto.VendorName), a => a.VendorName.Contains(requestDto.VendorName))
                            .WhereIf(requestDto.StartApplyTime.HasValue, a => a.ApplyTime > requestDto.StartApplyTime.Value)
                            .WhereIf(requestDto.EndApplyTime.HasValue, a => a.ApplyTime < requestDto.EndApplyTime.Value)
                            .WhereIf(requestDto.BuId.HasValue, a => a.ApplyUserBu == requestDto.BuId.Value)
                            .WhereIf(requestDto.CompanyId.HasValue, a => a.CompanyId == requestDto.CompanyId.Value)
                            .WhereIf(!string.IsNullOrWhiteSpace(requestDto.PRApplicationCode), a => a.PrApplicationCode.Contains(requestDto.PRApplicationCode))
                            .WhereIf(!string.IsNullOrWhiteSpace(requestDto.POApplicationCode), a => a.PoApplicationCode.Contains(requestDto.POApplicationCode));

            var personCenterService = LazyServiceProvider.LazyGetService<IPersonCenterService>();
            var rolesIds = await personCenterService.GetIdsByRoleLevels(bizTypeCategory: ResignationTransfer.TaskFormCategory.GoodsReceiptApplication);
            queryGrLinq = queryGrLinq.ApplyRoleFilters(rolesIds, CurrentUser.Id.Value, x => x.ApplyUserBu, x => x.ApplyUserBuToDeptId, x => x.SubBudgetId, x => x.ApplyUserId, x => x.TransfereeId);

            queryGrLinq = queryGrLinq.OrderByDescending(a => a.CreationTime);

            result.TotalCount = queryGrLinq.Count();
            var queryData = queryGrLinq.Skip(requestDto.PageIndex * requestDto.PageSize).Take(requestDto.PageSize).ToList();
            result.Items = ObjectMapper.Map<List<PurGRApplication>, List<PurGRApplicationResponseDto>>(queryData);
            return result;
        }

        /// <summary>
        /// 采购管理—收货列表-导出
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        public async Task<List<ExportGRApplicationDto>> ExportGRApplicationListAsync(GRListSearchRequestDto requestDto)
        {
            var querygr = (await LazyServiceProvider.LazyGetService<IPurGRApplicationReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
            //var queryGrDetail = await LazyServiceProvider.LazyGetService<IPurGRApplicationDetailRepository>().GetQueryableAsync();
            var queryGrHistory = (await LazyServiceProvider.LazyGetService<IPurGRApplicationDetailHistoryReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
            var querypr = (await LazyServiceProvider.LazyGetService<IPurPRApplicationReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
            var querypo = (await LazyServiceProvider.LazyGetService<IPurPOApplicationReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
            var querypa = (await LazyServiceProvider.LazyGetService<IPurPAApplicationReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
            //var querypaDetail = (await LazyServiceProvider.LazyGetService<IPurPAApplicationDetailRepository>().GetQueryableAsync()).AsNoTracking();
            var queryGrLinq = querygr.Select(a => new
            {
                a.Id,
                a.PoId,
                a.PrId,
                a.ApplicationCode,
                a.ApplyTime,
                a.Currency,
                a.ApplyUserBuName,
                a.PoApplicationCode,
                a.CompanyName,
                a.ApplyUserName,
                a.ExchangeRate,
                a.Status,
                a.BudgetCode,
                a.VendorName,
                a.ApplyUserBu,
                a.CompanyId,
                a.PrApplicationCode,
                a.CreationTime,
                a.ApplyUserId,
                a.ApplyUserBuToDeptId,
                a.SubBudgetId,
                a.TransfereeId
            })
                .GroupJoin(querypr.Select(a => new GRExportPRDto { Id = a.Id, ApplicationCode = a.ApplicationCode, CostCenterName = a.CostCenterName, ApplyTime = a.ApplyTime }), a => a.PrId, b => b.Id, (a, b) => new { gr = a, prs = b })
                .SelectMany(a => a.prs.DefaultIfEmpty(), (a, b) => new { a.gr, pr = b })
                .GroupJoin(querypo.Select(a => new GRExportPODto { Id = a.Id, ApplicationCode = a.ApplicationCode, ApprovedDate = a.ApprovedDate }), a => a.gr.PoId, b => b.Id, (a, b) => new { a.gr, a.pr, pos = b })
                .SelectMany(a => a.pos.DefaultIfEmpty(), (a, b) => new { a.gr, a.pr, po = b })
                .GroupJoin(querypa.Where(a => a.Status != PurPAApplicationStatus.Void).Select(a => new GRExportPADto { Id = a.Id, GrId = a.GRId, ApplicationCode = a.ApplicationCode, Status = a.Status, ApplyTime = a.ApplyTime }), a => a.gr.Id, b => b.GrId, (a, b) => new { a.gr, a.pr, a.po, pas = b })
                .SelectMany(a => a.pas.DefaultIfEmpty(), (a, b) => new { a.gr, a.pr, a.po, pa = b })
                .GroupJoin(queryGrHistory.Select(a => new GRExportGRHistoryDto { GRApplicationId = a.GRApplicationId, PurPAApplicationId = a.PurPAApplicationId, TotalAmount = a.ReceivedAmount }), a => a.pa.Id, b => b.PurPAApplicationId, (a, b) => new { a.gr, a.pr, a.po, a.pa, grdhs = b })
                .SelectMany(a => a.grdhs.DefaultIfEmpty(), (a, b) => new { a.gr, a.pr, a.po, a.pa, grdh = b })
                .WhereIf(requestDto.Status.HasValue, a => a.gr.Status.Equals(requestDto.Status))
                .WhereIf(!string.IsNullOrWhiteSpace(requestDto.ApplyUserBuName), a => a.gr.ApplyUserBuName.Contains(requestDto.ApplyUserBuName))
                .WhereIf(!string.IsNullOrWhiteSpace(requestDto.ApplicationCode), a => a.gr.ApplicationCode.Contains(requestDto.ApplicationCode))
                .WhereIf(!string.IsNullOrWhiteSpace(requestDto.BudgetCode), a => a.gr.BudgetCode.Contains(requestDto.BudgetCode))
                .WhereIf(!string.IsNullOrWhiteSpace(requestDto.VendorName), a => a.gr.VendorName.Contains(requestDto.VendorName))
                .WhereIf(requestDto.StartApplyTime.HasValue, a => a.gr.ApplyTime > requestDto.StartApplyTime.Value.Date)
                .WhereIf(requestDto.EndApplyTime.HasValue, a => a.gr.ApplyTime <= requestDto.EndApplyTime.Value.Date.AddDays(1))
                .WhereIf(requestDto.BuId.HasValue, a => a.gr.ApplyUserBu == requestDto.BuId.Value)
                .WhereIf(requestDto.CompanyId.HasValue, a => a.gr.CompanyId == requestDto.CompanyId.Value)
                .WhereIf(!string.IsNullOrWhiteSpace(requestDto.PRApplicationCode), a => a.gr.PrApplicationCode.Contains(requestDto.PRApplicationCode))
                .WhereIf(!string.IsNullOrWhiteSpace(requestDto.POApplicationCode), a => a.gr.PoApplicationCode.Contains(requestDto.POApplicationCode))
                .Select(a => new
                {
                    a.gr.Id,
                    a.gr.PoId,
                    a.gr.PrId,
                    a.gr.ApplicationCode,
                    a.gr.ApplyTime,
                    a.gr.Currency,
                    a.gr.ApplyUserBuName,
                    a.gr.PoApplicationCode,
                    a.gr.CompanyName,
                    a.gr.ApplyUserName,
                    a.gr.ExchangeRate,
                    a.gr.Status,
                    a.gr.BudgetCode,
                    a.gr.VendorName,
                    a.gr.ApplyUserBu,
                    a.gr.CompanyId,
                    a.gr.PrApplicationCode,
                    a.gr.CreationTime,
                    a.gr.ApplyUserId,
                    a.gr.ApplyUserBuToDeptId,
                    a.gr.SubBudgetId,
                    a.gr.TransfereeId,
                    a.grdh.TotalAmount,
                    PrApplyTime = a.pr.ApplyTime,
                    a.pr.CostCenterName,
                    PaCode = a.pa.ApplicationCode,
                    PaApplyTime = a.pa.ApplyTime,
                    PaStatus = a.pa.Status,
                    a.po.ApprovedDate
                });

            var personCenterService = LazyServiceProvider.LazyGetService<IPersonCenterService>();
            var rolesIds = await personCenterService.GetIdsByRoleLevels(bizTypeCategory: ResignationTransfer.TaskFormCategory.GoodsReceiptApplication);
            queryGrLinq = queryGrLinq.ApplyRoleFilters(rolesIds, CurrentUser.Id.Value, x => x.ApplyUserBu, x => x.ApplyUserBuToDeptId, x => x.SubBudgetId, x => x.ApplyUserId, x => x.TransfereeId);

            queryGrLinq = queryGrLinq.OrderByDescending(a => a.CreationTime);

            var result = queryGrLinq.Select(a => new ExportGRApplicationDto
            {
                ApplicationCode = a.ApplicationCode,
                ApplyUserName = a.ApplyUserName,
                ApplyDate = a.ApplyTime.ToString("yyyy-MM-dd"),
                Currency = a.Currency,
                TotalAmount = a.TotalAmount,
                TotalAmountRMB = !a.TotalAmount.HasValue ? null : (a.TotalAmount * (string.IsNullOrWhiteSpace(a.Currency) ? 1M : (decimal)a.ExchangeRate)),
                POApplicationCode = a.PoApplicationCode,
                VendorName = a.VendorName,
                POApprovalTime = !a.ApprovedDate.HasValue ? "" : a.ApprovedDate.Value.ToString("yyyy-MM-dd HH:mm:ss"),
                PRApplicationCode = a.PrApplicationCode,
                PRApplyDate = a.PrApplyTime.HasValue ? a.PrApplyTime.Value.ToString("yyyy-MM-dd") : "",
                CompanyName = a.CompanyName,
                ApplyUserBuName = a.ApplyUserBuName,
                CostCenterName = a.CostCenterName,
                PAApplicationCode = a.PaCode ?? "",
                PAApplyDate = a.PaApplyTime.HasValue ? a.PaApplyTime.Value.ToString("yyyy-MM-dd") : "",
                PAStatusName = a.PaStatus.HasValue ? a.PaStatus.GetDescription() : ""
            }).ToList();
            return result;
        }
        #endregion 
        /// <summary>
        /// 查询GR详情
        /// </summary>
        /// <param name="grId"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<GRApplicationDetailsResponseDto> GetPurGRApplicationDetailsAsync(Guid grId)
        {
            var result = new GRApplicationDetailsResponseDto();
            var _identityUserRepository = LazyServiceProvider.GetService<IRepository<IdentityUser, Guid>>();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var queryAttachment = await LazyServiceProvider.LazyGetService<IAttachmentRepository>().GetQueryableAsync();
            var querygr = await LazyServiceProvider.LazyGetService<IPurGRApplicationRepository>().GetQueryableAsync();
            var queryGrDetail = await LazyServiceProvider.LazyGetService<IPurGRApplicationDetailRepository>().GetQueryableAsync();
            var queryPO = await LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>().GetQueryableAsync();
            var queryPODetail = await LazyServiceProvider.LazyGetService<IPurPOApplicationDetailsRepository>().GetQueryableAsync();
            var queryPr = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var queryPrDetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var queryPrDetailHistory = await LazyServiceProvider.LazyGetService<IPurGRApplicationDetailHistoryRepository>().GetQueryableAsync();
            var queryPrProductApportionment = await LazyServiceProvider.LazyGetService<IPurPRApplicationProductApportionmentRepository>().GetQueryableAsync();
            var queryOnlineMeeting = await LazyServiceProvider.LazyGetService<IInteOnlineMeetingRepository>().GetQueryableAsync();

            var grApplication = querygr.Where(a => a.Id == grId).FirstOrDefault();

            if (grApplication == null)
            {
                return null;
            }
            result = ObjectMapper.Map<PurGRApplication, GRApplicationDetailsResponseDto>(grApplication);
            //产品拆分
            var prProductApportionments = queryPrProductApportionment.Where(a => a.PRApplicationId == grApplication.PrId).ToArray();

            var procurementPersonnel = await _identityUserRepository.FindAsync(grApplication.ProcurementPersonnelId);
            if (procurementPersonnel != null)
            {
                result.ProcurementPersonnelName = procurementPersonnel.Name;
            }
            if (!string.IsNullOrWhiteSpace(grApplication.AdditionalSignerId))
            {
                var queryUser = await _identityUserRepository.GetQueryableAsync();
                var signerList = grApplication.AdditionalSignerId.Split(",").Select(Guid.Parse).ToList();
                var additionalSigner = queryUser.Where(a => signerList.Contains(a.Id)).ToList();
                var additionalSignerNames = additionalSigner.Select(a => a.Name).ToList();
                result.AdditionalSignerName = string.Join(",", additionalSignerNames);
                result.AdditionalSignerUsers = ObjectMapper.Map<List<IdentityUser>, List<IdentityUserDto>>(additionalSigner);
            }
            #region 电子签章附件
            if (!string.IsNullOrWhiteSpace(grApplication.EsignPdf))
            {
                Guid pdfFileId = Guid.Parse(grApplication.EsignPdf);
                var attachment = queryAttachment.FirstOrDefault(a => a.Id == pdfFileId);
                result.EsignPdfFile = new UploadFileResponseDto()
                {
                    AttachmentId = attachment.Id,
                    FileName = attachment.FileName,
                    FilePath = attachment.FilePath,
                };
            }
            #endregion
            var grDetails = queryGrDetail.Where(a => a.GRApplicationId == grApplication.Id).ToList();
            #region 附件
            var prDetailIds = grDetails.Select(b => b.PRDetailId).Distinct().ToList();
            var prDetail = queryPrDetail.Where(a => prDetailIds.Contains(a.Id))
                .GroupJoin(queryPr, pd => pd.PRApplicationId, pr => pr.Id, (pd, pr) => new { pd, p = pr.FirstOrDefault() })
                .ToList();
            //AR都是每一行生成一张收货单,有一个AR则表示为AR方式 并且 是0元讲者
            if (prDetail.Any(a => a.pd.PayMethod == PayMethods.AR) && prDetail.Any(a => (a.pd.TotalAmount ?? 0) == 0) && (prDetail.FirstOrDefault().p.IsEsignUsed.HasValue == false || prDetail.FirstOrDefault().p.IsEsignUsed == false))
            {
                result.IsZeroSpeaker = true;//前端弹出选择PSA附件框
            }
            if (!string.IsNullOrWhiteSpace(grApplication.PSAIds))
            {
                var attachmentIds = grApplication.PSAIds.Split(',').Select(Guid.Parse).ToList();
                var attachments = queryAttachment.Where(a => attachmentIds.Contains(a.Id)).ToList();
                var files = new List<UploadFileResponseDto>();
                foreach (var attachment in attachments)
                {
                    files.Add(new UploadFileResponseDto()
                    {
                        AttachmentId = attachment.Id,
                        FileName = attachment.FileName,
                        FilePath = attachment.FilePath,
                        FileSize = attachment.Size < 1048576 ? (attachment.Size / 1024).ToString() + "KB" : (attachment.Size / 1048576).ToString() + "MB",
                    });
                }
                result.Files = files;
            }
            #endregion

            #region 收货明细

            var grDetailsList = ObjectMapper.Map<List<PurGRApplicationDetail>, List<PurGRApplicationDetailResponseDto>>(grDetails);
            grDetailsList.ForEach(a =>
            {
                var prd = prDetail.FirstOrDefault(x => x.pd.Id == a.PRDetailId);
                a.EstimateDate = prd?.pd?.EstimateDate?.ToString("yyyy-MM-dd");
            });
            result.purGRApplicationDetails = grDetailsList;
            #endregion

            var history = queryPrDetailHistory.Where(a => grDetails.Select(b => b.Id).ToList().Contains(a.GRApplicationDetailId)).ToList();
            result.purGRApplicationDetails.ForEach(a =>
            {
                //总收获货量
                a.TotalReceivedQuantity = history.Where(x => x.GRApplicationDetailId == a.DetailId && x.IsAdvancePayment == false).Sum(a => (a.CurrentReceivingQuantity ?? 0M));//排除预付款
            });

            #region PR分摊
            //付款方式为AR的，币种固定为人民币
            if (PayMethods.AR.Equals(grApplication.PayMethod))
            {
                var pr = prDetail.FirstOrDefault()?.p;
                result.Currency = pr.Currency;
                result.ExchangeRate = pr.Rate;
                var prd = prDetail.FirstOrDefault()?.pd;
                var costNature = (await dataverseService.GetCostNatureAsync(prd.CostNature.Value.ToString(), null)).FirstOrDefault();
                result.IsAllowAdvancePayment = costNature.IsAdvancePayment.HasValue ? costNature.IsAdvancePayment.Value : false;//AR 是否允许预付款
                var om = queryOnlineMeeting.Where(a => prDetailIds.Contains(a.PRDetailId)).OrderByDescending(a => a.CreationTime).FirstOrDefault();
                if (om != null)
                {
                    result.IsOnlineMeeting = true;
                    result.OMPayAmount = prd.TotalAmount;
                    result.purGRApplicationDetails.ForEach(a =>
                    {
                        a.DeliveryMethod = DeliveryMethod.ByProportion;
                        a.CurrentSignedQuantity = 100M;//100%
                        a.CurrentReceivingQuantity = 100M;//100%
                        a.SigningDate = om.StartDate.ToString("yyyy-MM-dd");
                        a.IsArrive = true;
                    });
                }
            }
            if (PayMethods.AP.Equals(grApplication.PayMethod) && grApplication.PoId.HasValue)
            {
                var po = queryPO.Where(a => a.Id == grApplication.PoId.Value).FirstOrDefault();
                result.Currency = po.Currency;
                result.ExchangeRate = po.ExchangeRate;
            }
            List<PRApportionDetailsDto> pRApportionDetails = new List<PRApportionDetailsDto>();

            result.IsPrePayments = history != null && history.Any(a => a.IsAdvancePayment == true);//收货历史中存在预付款则说明本单为预付款
            if (result.IsPrePayments)
                result.IsPrePaid = history.Where(a => a.PurPAApplicationId.HasValue).Select(a => a.PurPAApplicationId).Distinct().Count() > 1;//存在多个PA单ID 说明有多次收获
            result.IsPaid = history != null && history.Any();//是否付过款
            List<PurPOApplicationDetails> poDetails = null;
            if (grApplication.PoId.HasValue)
                poDetails = queryPODetail.Where(a => a.POApplicationId == grApplication.PoId.Value).ToList();
            foreach (var item in prDetail)
            {
                var prApportionDetail = new PRApportionDetailsDto();
                if (PayMethods.AR.Equals(grApplication.PayMethod))
                    prApportionDetail.TaxRate = 0M;//付款方式为AR时税率为0
                if (PayMethods.AP.Equals(grApplication.PayMethod) && poDetails != null)
                {
                    var poDetail = poDetails.Where(a => a.PRDetailId == item.pd.Id).FirstOrDefault();
                    decimal taxRate = 0;
                    decimal.TryParse(poDetail?.TaxRate ?? "", out taxRate);
                    prApportionDetail.TaxRate = taxRate;
                }
                prApportionDetail.PRId = item.p.Id;
                prApportionDetail.PRApplicationCode = item.p.ApplicationCode;
                prApportionDetail.PRDetailId = item.pd.Id;
                prApportionDetail.TotalAmount = item.pd.TotalAmount ?? 0;
                prApportionDetail.PurchaseRMBAmount = (poDetails?.Where(a => a.PRDetailId == item.pd.Id).Sum(a => a.TotalAmountNoTax) ?? 0M) * (decimal)result.ExchangeRate;
                var thisHistory = history.Where(a => a.PRDetailId == item.pd.Id).ToList();
                if (PurGRApplicationStatus.SignedBy.Equals(grApplication.Status))
                {
                    //加签人加签时，收货明细还未写入历史
                    var thisGRDetails = ObjectMapper.Map<List<PurGRApplicationDetail>, List<PurGRApplicationDetailHistory>>(grDetails);
                    thisHistory.AddRange(thisGRDetails);
                }
                prApportionDetail.ApportionAmount = grDetails.Where(a => a.PRDetailId == item.pd.Id).Sum(a => { return a.AllocationAmount.HasValue ? a.AllocationAmount.Value : 0M; });
                prApportionDetail.ApportionRMBAmount = grDetails.Where(a => a.PRDetailId == item.pd.Id).Sum(a => { return a.AllocationRMBAmount.HasValue ? a.AllocationRMBAmount.Value : 0M; });
                prApportionDetail.ReceivedAmount = thisHistory.Sum(a => a.ReceivedAmount ?? 0);
                prApportionDetail.ReceivedRMBAmount = thisHistory.Sum(a => a.ReceivedAmount ?? 0) * (decimal)result.ExchangeRate;
                prApportionDetail.ApplyUserBu = item.p.ApplyUserBu;
                prApportionDetail.ApplyUserBuName = item.p.ApplyUserBuName;
                //产品拆分
                if (prProductApportionments.Any())
                    prApportionDetail.ProductApportionments = prProductApportionments.Where(a => a.PRApplicationDetailId == item.pd.Id).Select(a => new CreateUpdatePRApplicationProductApportionmentRequest { ProductId = a.ProductId, ProductName = a.ProductName, Ratio = a.Ratio });

                prApportionDetail.CostNature = item.pd.CostNature;
                prApportionDetail.CostNatureName = item.pd.CostNatureName;
                prApportionDetail.CostCenter = item.p.CostCenter;
                prApportionDetail.CostCenterName = item.pd.CostCenterName;
                prApportionDetail.EstimateDate = item.pd.EstimateDate?.ToString("yyyy-MM-dd");
                pRApportionDetails.Add(prApportionDetail);
            }
            result.PRApportionDetails = pRApportionDetails;
            #endregion
            return result;
        }

        /// <summary>
        /// 创建GR申请单
        /// </summary>
        /// <param name="prDetailId"></param>
        public async Task<MessageResult> CreateGRApplication(List<Guid> prDetailIds, PurPOApplicationDto queryPO = null)
        {
            var _identityUserRepository = LazyServiceProvider.GetService<IRepository<IdentityUser, Guid>>();
            var _dataverseService = LazyServiceProvider.GetService<IDataverseService>();
            var vendor = await LazyServiceProvider.GetService<IVendorRepository>().GetQueryableAsync();
            var vendorPersonal = await LazyServiceProvider.GetService<IVendorPersonalRepository>().GetQueryableAsync();
            var vendorOrg = await LazyServiceProvider.LazyGetService<IVendorOrgnizationRepository>().GetQueryableAsync();
            var commonService = LazyServiceProvider.LazyGetService<ICommonService>();
            var grApplicationRepository = LazyServiceProvider.GetService<IPurGRApplicationRepository>();
            var grApplicationDetailRepository = LazyServiceProvider.GetService<IPurGRApplicationDetailRepository>();
            var grQuery = await grApplicationRepository.GetQueryableAsync();
            //查询PR
            var prRepository = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            //查询PRDetails
            var prDetailsRepository = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var queryPR = prRepository.GroupJoin(prDetailsRepository, a => a.Id, b => b.PRApplicationId, (a, b) => new { prRepository = a, prDetailsRepository = b })
                .SelectMany(s => s.prDetailsRepository.DefaultIfEmpty(), (a, b) => new { a.prRepository, prDetailsRepository = b })
                .Where(w => prDetailIds.Contains(w.prDetailsRepository.Id)).ToList();
            if (queryPR.Count == 0)
                return MessageResult.FailureResult("提交失败，未找到相关PR详细数据");
            var companys = await _dataverseService.GetCompanyList();
            //PR直接生成收货单
            if (queryPO == null)
            {
                var entityGRList = new List<PurGRApplicationDetail>();
                //一条PR生成一个收货单
                foreach (var item in queryPR)
                {
                    var entityGR = new PurGRApplication
                    {
                        ApplyUserId = item.prRepository.ApplyUserId,
                        ApplyUserName = _identityUserRepository.FirstOrDefaultAsync(f => f.Id == item.prRepository.ApplyUserId).Result?.Name,
                        ApplyTime = DateTime.Now,
                        ApplyUserBu = item.prRepository.ApplyUserBu,
                        ApplyUserBuName = item.prRepository.ApplyUserBuName,
                        ApplyUserBuToDeptId = item.prRepository.ApplyUserDept,
                        ApplyUserBuToDeptName = item.prRepository.ApplyUserDeptName,
                        PrId = item.prRepository.Id,
                        PrApplicationCode = item.prRepository.ApplicationCode,
                        ProcurementPersonnelId = CurrentUser.Id.Value,
                        //预算
                        BudgetId = item.prRepository.BudgetId,
                        BudgetCode = item.prRepository.BudgetCode,
                        SubBudgetId = item.prRepository.SubBudgetId,
                        SubBudgetCode = item.prRepository.SubBudgetCode,
                        Remarks = "来自PR单确认发货",
                        PayMethod = PayMethods.AR,
                        CompanyId = item.prRepository.CompanyId,
                        CompanyName = item.prRepository.CompanyId.HasValue ? companys.Where(f => f.Id == item.prRepository.CompanyId).FirstOrDefault()?.CompanyName ?? "" : "",
                        CompanyCode = item.prRepository.CompanyId.HasValue ? companys.Where(f => f.Id == item.prRepository.CompanyId).FirstOrDefault()?.CompanyCode ?? "" : "",
                        //供应商名称
                        VendorId = item.prDetailsRepository.VendorId,
                        VendorName = item.prDetailsRepository.VendorName,
                        VendorCode = item.prDetailsRepository.VendorCode,
                        Currency = string.IsNullOrWhiteSpace(item.prRepository.Currency) ? "RMB" : item.prRepository.Currency,//PR 无币种则默认人民币（AR付款方式）
                        ExchangeRate = string.IsNullOrWhiteSpace(item.prRepository.Currency) ? 1 : item.prRepository.Rate,
                        PlanRate = item.prRepository.PlanRate,
                        ExpectedFloatRate = item.prRepository.ExpectedFloatRate,
                        CurrencySymbol = string.IsNullOrWhiteSpace(item.prRepository.Currency) ? "￥" : item.prRepository.CurrencySymbol,
                        TransfereeId = item.prRepository.TransfereeId,
                        TransfereeName = item.prRepository.TransfereeName,
                    };
                    await InsertAndGenerateSerialNoAsync(grApplicationRepository, entityGR, "G");
                    //var productsAsync = await _dataverseService.GetProductsAsync();
                    //添加收货明细模板
                    //PR直接转GR的只生成一条详情
                    //PO中有多少详情，收货就生成多少详情

                    var entityGRDetail = new PurGRApplicationDetail
                    {
                        GRApplicationId = entityGR.Id,
                        //产品
                        ProductId = item.prDetailsRepository.ProductId,
                        ProductName = item.prDetailsRepository.Content,
                        InvoiceType = InvoiceType.OrdinaryInvoice, //发票类型
                        OrderQuantity = (decimal)item.prDetailsRepository.Quantity,
                        UnitPrice = (decimal)item.prDetailsRepository.UnitPrice,
                        PRDetailId = item.prDetailsRepository.Id
                    };
                    entityGRList.Add(entityGRDetail);
                    //await grApplicationDetailRepository.InsertAsync(entityGRDetail, true);
                }
                await grApplicationDetailRepository.InsertManyAsync(entityGRList, true);
            }
            else //PO生成收货单
            {
                //查询PO
                var poRepository = await LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>().GetQueryableAsync();
                //查询PODetails
                var poDetailsRepository = await LazyServiceProvider.LazyGetService<IPurPOApplicationDetailsRepository>().GetQueryableAsync();
                var queryPOList = poRepository.GroupJoin(poDetailsRepository, a => a.Id, b => b.POApplicationId, (a, b) => new { poRepository = a, poDetailsRepository = b })
                    .SelectMany(s => s.poDetailsRepository.DefaultIfEmpty(), (a, b) => new { a.poRepository, poDetailsRepository = b })
                    .Where(w => w.poRepository.Id == queryPO.Id).ToList();
                if (queryPOList.Count == 0)
                    return MessageResult.FailureResult("提交失败，未找到相关PO详细数据");
                var gr = grQuery.Where(a => a.PoId == queryPO.Id).FirstOrDefault();
                if (gr != null)
                    return MessageResult.FailureResult("提交失败，请勿重复提交收货申请");
                var applyUserName = _identityUserRepository.FirstOrDefaultAsync(f => f.Id == queryPR.First().prRepository.ApplyUserId).Result?.Name;
                var po = queryPOList.FirstOrDefault()?.poRepository;
                var pr = queryPR.First();
                //生成收货
                var entityGR = new PurGRApplication
                {
                    ApplyUserId = pr.prRepository.ApplyUserId,
                    ApplyUserName = applyUserName,
                    ApplyTime = DateTime.Now,
                    ApplyUserBu = pr.prRepository.ApplyUserBu,
                    ApplyUserBuName = pr.prRepository.ApplyUserBuName,
                    ApplyUserBuToDeptId = pr.prRepository.ApplyUserDept,
                    ApplyUserBuToDeptName = pr.prRepository.ApplyUserDeptName,
                    PoId = queryPO?.Id,
                    PoApplicationCode = queryPO?.ApplicationCode,
                    PrId = pr.prRepository.Id,
                    PrApplicationCode = pr.prRepository.ApplicationCode,
                    ProcurementPersonnelId = CurrentUser.Id.Value,
                    VendorId = po?.VendorId,
                    VendorName = po?.VendorName,
                    VendorCode = po?.VendorCode,
                    //预算
                    BudgetId = pr.prRepository.BudgetId,
                    BudgetCode = pr.prRepository.BudgetCode,
                    SubBudgetId = pr.prRepository.SubBudgetId,
                    SubBudgetCode = pr.prRepository.SubBudgetCode,
                    Remarks = "来自PO单确认发货",
                    PayMethod = PayMethods.AP,
                    CompanyName = pr.prRepository.CompanyId.HasValue ? companys.Where(f => f.Id == pr.prRepository.CompanyId).FirstOrDefault()?.CompanyName ?? "" : "",
                    CompanyCode = pr.prRepository.CompanyId.HasValue ? companys.Where(f => f.Id == pr.prRepository.CompanyId).FirstOrDefault()?.CompanyCode ?? "" : "",
                    CompanyId = pr.prRepository.CompanyId,
                    Currency = po.Currency,
                    ExchangeRate = po.ExchangeRate,
                    PlanRate = po.PlanRate,
                    ExpectedFloatRate = po.ExpectedFloatRate,
                    CurrencySymbol = po.CurrencySymbol,
                    TransfereeId = pr.prRepository.TransfereeId,
                    TransfereeName = pr.prRepository.TransfereeName,
                };
                if (string.IsNullOrWhiteSpace(entityGR.CurrencySymbol)) //历史迁移数据PO 有汇率和币种、无符号 这里处理符号
                {
                    var getCompanyTask = (await _dataverseService.GetCompanyList(po.CompanyId.ToString())).FirstOrDefault();
                    entityGR.CurrencySymbol = getCompanyTask?.CompanyCurrency.Where(a => a.Code == entityGR.Currency).FirstOrDefault()?.CurrencySymbol;//获取币种符号
                }
                var grCode = await InsertAndGenerateSerialNoAsync(grApplicationRepository, entityGR, "G");

                //PO中有多少详情，收货就生成多少详情
                var entityGRList = new List<PurGRApplicationDetail>();
                foreach (var item in queryPOList)
                {
                    //添加收货明细模板
                    var entityGRDetail = new PurGRApplicationDetail
                    {
                        GRApplicationId = entityGR.Id,
                        PODetailId = item.poDetailsRepository.Id,
                        ProductId = queryPR?.FirstOrDefault(a => a.prDetailsRepository.Id == item.poDetailsRepository.Id)?.prDetailsRepository?.ProductId,
                        ProductName = item.poDetailsRepository.Content,
                        InvoiceType = item.poDetailsRepository.InvoiceType,
                        OrderQuantity = (decimal)item.poDetailsRepository.Quantity,
                        UnitPrice = (decimal)item.poDetailsRepository.UnitPrice,
                        PRDetailId = item.poDetailsRepository.PRDetailId.Value,
                        PurchaseRMBAmount = item.poDetailsRepository.TotalAmountNoTax * (decimal)item.poRepository.ExchangeRate
                    };
                    entityGRList.Add(entityGRDetail);
                }
                await grApplicationDetailRepository.InsertManyAsync(entityGRList, true);
                await SendEmailApplicantAsync(queryPR.First().prRepository, grCode, entityGR.Id);

            }
            return MessageResult.SuccessResult();
        }
        /// <summary>
        /// 审批通知申请人
        /// </summary>
        /// <param name="po"></param>
        /// <returns></returns>
        private async Task SendEmailApplicantAsync(PurPRApplication pr, string grCode, Guid grId)
        {
            //主采购订单 采购订单申请  申请审批通过 确认订单   变更成收货申请
            var userQuery = (await LazyServiceProvider.LazyGetService<IRepository<IdentityUser, Guid>>().GetQueryableAsync()).AsNoTracking();

            var userIdsToNotify = new List<Guid>();
            var agentService = LazyServiceProvider.LazyGetService<IAgencyService>();
            var agentRequest = new GetAgentByOriginalOperatorRequestDto { OriginalOperatorId = pr.ApplyUserId, BusinessTypeCategory = ResignationTransfer.TaskFormCategory.PurchaseRequestApplication };
            userIdsToNotify.Add(pr.TransfereeId.HasValue ? pr.TransfereeId.Value : pr.ApplyUserId);
            var agent = await agentService.GetAgentByOriginalOperator(agentRequest);
            if (agent.HasValue)
                userIdsToNotify.Add(agent.Value.Key);

            var users = userQuery.Where(a => userIdsToNotify.Distinct().ToHashSet().Contains(a.Id)).ToArray();
            var sendEmaillRecords = users.Select(user => new InsertSendEmaillRecordDto
            {
                EmailAddress = user.Email,
                Subject = "[NexBPM消息中心]您有一个【{WorkflowTypeName}】正在等待您{ProcessType}。",
                Content = JsonSerializer.Serialize(new NotificationApplicantEmailDto
                {
                    WorkflowTypeName = "收货申请",
                    ProcessType = NotifyApplicantProcessType.TJSH,
                    UserName = user.Name,
                    ApplicationCode = grCode,
                    ApplicationLink = $"{_configuration["SpeakerEmail:WebHost"]?.TrimEnd('/')}/task/launch",
                }),
                SourceType = EmailSourceType.ApprovalNotifyApplicant,
                Status = SendStatus.Pending,
                Attempts = 0,
            });

            //记录邮件，并触发邮件发送功能
            var emailService = LazyServiceProvider.LazyGetService<IEmailService>();
            await emailService.InsertSendEmaillRecordAsync(sendEmaillRecords.ToList());
            // 调度作业
            BackgroundJob.Enqueue<SendEmailWorker>(a => a.DoWorkAsync());
        }

        /// <summary>
        /// 讲课费 发票类型判断 (该方法作废不用——后续备用)
        /// </summary>
        /// <param name="avmId"></param>
        /// <returns></returns>
        private async Task<string> GetInvoeceTypeAsync(Guid? avmId)
        {
            if (!avmId.HasValue)
                return InvoiceType.OrdinaryInvoice;
            var queryBpcsAvm = await LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync();
            var queryFinancial = await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync();
            var queryVendor = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();
            //常规讲者
            var vendor = queryBpcsAvm.Where(s => s.Id == avmId)
                .Join(queryFinancial, a => a.FinaId, a => a.Id, (a, b) => new { Avm = a, Fin = b })
                .Join(queryVendor.Where(a => a.Status == VendorStatus.Valid && a.VendorType == VendorTypes.HCPPerson), a => a.Fin.VendorId, a => a.Id, (a, b) => new { a.Avm, Vendor = b })
                .FirstOrDefault();
            //讲者
            //1、非外币：bpcsAVM里NHIV类型
            //2、外币：bpcsAVM里是NT，Vendors表对应的VendorType = 1
            if (vendor?.Avm?.Vtype == "NHIV" || (vendor?.Avm?.Vtype == "NT" && vendor?.Vendor?.VendorType == VendorTypes.HCPPerson))//讲课费
                return InvoiceType.NonInvoice;
            return InvoiceType.OrdinaryInvoice;
        }

        /// <summary>
        /// 获取收货历史记录
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<PurGRApplicationDetailHistoryResponseDto>> GetGrDetailHistoryListAsync(GRHistoryRequestDto request)
        {
            var result = new PagedResultDto<PurGRApplicationDetailHistoryResponseDto>();
            var queryPrDetailHistory = await LazyServiceProvider.LazyGetService<IPurGRApplicationDetailHistoryRepository>().GetQueryableAsync();
            var historys = queryPrDetailHistory.Where(a => a.GRApplicationDetailId == request.Id && a.IsAdvancePayment == false)
                .OrderByDescending(a => a.CreationTime)
                .Select(a => new PurGRApplicationDetailHistoryResponseDto
                {
                    GRApplicationId = a.GRApplicationId,
                    GRApplicationDetailId = a.GRApplicationDetailId,
                    ProductId = a.ProductId,
                    ProductName = a.ProductName,
                    OrderQuantity = a.OrderQuantity,
                    TotalReceivedQuantity = a.TotalReceivedQuantity,
                    DeliveryMethod = a.DeliveryMethod,
                    CurrentReceivingQuantity = a.CurrentReceivingQuantity,
                    CurrentSignedQuantity = a.CurrentSignedQuantity,
                    UnitPrice = a.UnitPrice,
                    ReceivedAmount = a.ReceivedAmount,
                    SigningDate = a.SigningDate.ToString("yyyy-MM-dd"),
                    IsArrive = a.IsArrive,
                });
            result.TotalCount = historys.Count();
            result.Items = historys.Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToList();
            return result;
        }
        #region GR 收货申请  审批-我审批的视角相关操作

        /// <summary>
        /// 审批-我审批的收货申请列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<PurGRApplicationResponseDto>> GetGRApprovalListAsync(GRListSearchRequestDto request)
        {
            var result = new PagedResultDto<PurGRApplicationResponseDto>();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var queryGR = await LazyServiceProvider.LazyGetService<IPurGRApplicationReadonlyRepository>().GetQueryableAsync();
            var vendor = await LazyServiceProvider.LazyGetService<IVendorReadonlyRepository>().GetQueryableAsync();
            var getCompanyTask = await dataverseService.GetCompanyList(null);
            //待处理
            if (request.ProcessingStatus == ProcessingStatus.PendingProcessing)
            {
                var taskRecords = await dataverseService.GetApprovelTaskAsync(CurrentUser.Id.ToString(), [WorkflowTypeName.ReceiptRequest, WorkflowTypeName.TerminationOfReceipt], (ProcessingStatus)request.ProcessingStatus);
                var grs = queryGR.Where(a => taskRecords.Select(b => b.FormId).ToList().Contains(a.Id))
                    .WhereIf(!string.IsNullOrWhiteSpace(request.ApplyUserBuName), a => a.ApplyUserBuName.Contains(request.ApplyUserBuName))
                    .WhereIf(!string.IsNullOrWhiteSpace(request.ApplicationCode), a => a.ApplicationCode.Contains(request.ApplicationCode))
                    .WhereIf(!string.IsNullOrWhiteSpace(request.BudgetCode), a => a.BudgetCode.Contains(request.BudgetCode))
                    .WhereIf(!string.IsNullOrWhiteSpace(request.VendorName), a => a.VendorName.Contains(request.VendorName))
                    .WhereIf(request.StartApplyTime.HasValue, a => a.ApplyTime >= request.StartApplyTime.Value.Date)
                    .WhereIf(request.EndApplyTime.HasValue, a => a.ApplyTime <= request.EndApplyTime.Value.Date.AddDays(1))
                    .WhereIf(request.BuId.HasValue, a => a.ApplyUserBu == request.BuId.Value)
                    .WhereIf(!string.IsNullOrWhiteSpace(request.PRApplicationCode), a => a.PrApplicationCode.Contains(request.PRApplicationCode))
                    .WhereIf(!string.IsNullOrWhiteSpace(request.POApplicationCode), a => a.PoApplicationCode.Contains(request.POApplicationCode))
                    .ToArray();

                result.TotalCount = grs.Length;
                if (request.IsAsc)
                {
                    var datas = grs.Join(taskRecords, a => a.Id, a => a.FormId, (a, b) => new { Gr = a, Task = b })
                    .OrderBy(a => a.Task.CreatedTime)
                    .Select(a => a.Gr)
                    .ToArray();
                    result.Items = ObjectMapper.Map<IEnumerable<PurGRApplication>, List<PurGRApplicationResponseDto>>(datas);
                }
                else
                {
                    var datas = grs.Join(taskRecords, a => a.Id, a => a.FormId, (a, b) => new { Gr = a, Task = b })
                    .OrderByDescending(a => a.Task.CreatedTime)
                    .Select(a => a.Gr)
                    .ToArray();
                    result.Items = ObjectMapper.Map<IEnumerable<PurGRApplication>, List<PurGRApplicationResponseDto>>(datas);
                }
            }
            else//已处理
            {
                var queryWfTask = await LazyServiceProvider.LazyGetService<IWorkflowTaskReadonlyRepository>().GetQueryableAsync();
                var queryLinq = queryGR
                    .WhereIf(!string.IsNullOrWhiteSpace(request.ApplyUserBuName), a => a.ApplyUserBuName.Contains(request.ApplyUserBuName))
                    .WhereIf(!string.IsNullOrWhiteSpace(request.ApplicationCode), a => a.ApplicationCode.Contains(request.ApplicationCode))
                    .WhereIf(!string.IsNullOrWhiteSpace(request.BudgetCode), a => a.BudgetCode.Contains(request.BudgetCode))
                    .WhereIf(!string.IsNullOrWhiteSpace(request.VendorName), a => a.VendorName.Contains(request.VendorName))
                    .WhereIf(request.StartApplyTime.HasValue, a => a.ApplyTime >= request.StartApplyTime.Value.Date)
                    .WhereIf(request.EndApplyTime.HasValue, a => a.ApplyTime <= request.EndApplyTime.Value.Date.AddDays(1))
                    .WhereIf(request.BuId.HasValue, a => a.ApplyUserBu == request.BuId.Value)
                    .WhereIf(!string.IsNullOrWhiteSpace(request.PRApplicationCode), a => a.PrApplicationCode.Contains(request.PRApplicationCode))
                    .WhereIf(!string.IsNullOrWhiteSpace(request.POApplicationCode), a => a.PoApplicationCode.Contains(request.POApplicationCode))
                    .Join(queryWfTask.Where(a => a.ApprovalId == CurrentUser.Id), a => a.Id, a => a.FormId, (a, b) => new { Gr = a, Task = b });

                result.TotalCount = queryLinq.Count();

                if (request.IsAsc)
                    result.Items = queryLinq.OrderBy(a => a.Task.ApprovalTime).Select(a => new { a.Gr, a.Task.ApprovalTime }).Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToArray()
                    .Select(a =>
                    {
                        var data = ObjectMapper.Map<PurGRApplication, PurGRApplicationResponseDto>(a.Gr);
                        data.ApprovalTime = a.ApprovalTime;
                        return data;
                    })
                    .ToArray();
                else
                    result.Items = queryLinq.OrderByDescending(a => a.Task.ApprovalTime).Select(a => new { a.Gr, a.Task.ApprovalTime }).Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToArray()
                    .Select(a =>
                    {
                        var data = ObjectMapper.Map<PurGRApplication, PurGRApplicationResponseDto>(a.Gr);
                        data.ApprovalTime = a.ApprovalTime;
                        return data;
                    })
                    .ToArray();
            }

            return result;
        }

        /// <summary>
        /// 审批-我审批的收货申请列表-导出
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        public async Task<List<ExportGRApplicationDto>> ExportGRApprovalListAsync(GRListSearchRequestDto request)
        {
            var querygr = await LazyServiceProvider.LazyGetService<IPurGRApplicationRepository>().GetQueryableAsync();
            var queryGrDetail = await LazyServiceProvider.LazyGetService<IPurGRApplicationDetailRepository>().GetQueryableAsync();
            var queryGrHistory = await LazyServiceProvider.LazyGetService<IPurGRApplicationDetailHistoryRepository>().GetQueryableAsync();
            var querypr = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var querypo = await LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>().GetQueryableAsync();
            var querypa = await LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>().GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var getCompanyTask = await dataverseService.GetCompanyList();
            //获取审批人节点
            var taskRecords = await dataverseService.GetApprovelTaskAsync(CurrentUser.Id.ToString(), [WorkflowTypeName.ReceiptRequest, WorkflowTypeName.TerminationOfReceipt], (ProcessingStatus)request.ProcessingStatus);
            //任务中心 状态 
            ApprovalStatus[] pending = [ApprovalStatus.PendingForApproval];//待处理
            ApprovalStatus[] completed = [ApprovalStatus.Approved, ApprovalStatus.Rejected, ApprovalStatus.Withdraw];//已完成

            //查询流程中 待处理或者已处理的GR
            var grTaskRecords = taskRecords.WhereIf(ProcessingStatus.PendingProcessing.Equals(request.ProcessingStatus), a => pending.Contains(a.Status))
                                 .WhereIf(ProcessingStatus.Completed.Equals(request.ProcessingStatus), a => completed.Contains(a.Status))
                                 .ToList();
            if (!grTaskRecords.Any())
            {
                //未找到数据
                return null;
            }
            var queryGrLinq = querygr.Select(a => new
            {
                a.Id,
                a.PoId,
                a.PrId,
                a.ApplicationCode,
                a.ApplyTime,
                a.Currency,
                a.ApplyUserBuName,
                a.PoApplicationCode,
                a.CompanyName,
                a.ApplyUserName,
                a.ExchangeRate,
                a.Status,
                a.BudgetCode,
                a.VendorName,
                a.ApplyUserBu,
                a.CompanyId,
                a.PrApplicationCode,
                a.CreationTime,
                a.ApplyUserId
            }).GroupJoin(querypr.Select(a => new GRExportPRDto { Id = a.Id, ApplicationCode = a.ApplicationCode, CostCenterName = a.CostCenterName, ApplyTime = a.ApplyTime }), a => a.PrId, b => b.Id, (a, b) => new { gr = a, prs = b })
                .SelectMany(a => a.prs.DefaultIfEmpty(), (a, b) => new { a.gr, pr = b })
                .GroupJoin(queryGrHistory.Select(a => new GRExportGRHistoryDto { GRApplicationId = a.GRApplicationId, PurPAApplicationId = a.PurPAApplicationId, TotalAmount = a.ReceivedAmount }), a => a.gr.Id, b => b.GRApplicationId, (a, b) => new { a.gr, a.pr, grdhs = b })
                .SelectMany(a => a.grdhs.DefaultIfEmpty(), (a, b) => new { a.gr, a.pr, grdh = b })
                .GroupJoin(querypo.Select(a => new GRExportPODto { Id = a.Id, ApplicationCode = a.ApplicationCode, ApprovedDate = a.ApprovedDate }), a => a.gr.PoId, b => b.Id, (a, b) => new { a.gr, a.pr, a.grdh, pos = b })
                .SelectMany(a => a.pos.DefaultIfEmpty(), (a, b) => new { a.gr, a.pr, a.grdh, po = b })
                .GroupJoin(querypa.Select(a => new GRExportPADto { Id = a.Id, ApplicationCode = a.ApplicationCode, Status = a.Status, ApplyTime = a.ApplyTime }), a => a.grdh.PurPAApplicationId, b => b.Id, (a, b) => new { a.gr, a.pr, a.grdh, a.po, pas = b })
                .SelectMany(a => a.pas.DefaultIfEmpty(), (a, b) => new { a.gr, a.pr, a.grdh, a.po, pa = b })
                .Where(a => grTaskRecords.Select(b => b.FormId).ToList().Contains(a.gr.Id))
                .WhereIf(!string.IsNullOrWhiteSpace(request.ApplyUserBuName), a => a.gr.ApplyUserBuName.Contains(request.ApplyUserBuName))
                .WhereIf(!string.IsNullOrWhiteSpace(request.ApplicationCode), a => a.gr.ApplicationCode.Contains(request.ApplicationCode))
                .WhereIf(!string.IsNullOrWhiteSpace(request.BudgetCode), a => a.gr.BudgetCode.Contains(request.BudgetCode))
                .WhereIf(!string.IsNullOrWhiteSpace(request.VendorName), a => a.gr.VendorName.Contains(request.VendorName))
                .WhereIf(request.StartApplyTime.HasValue, a => a.gr.ApplyTime >= request.StartApplyTime.Value.Date)
                .WhereIf(request.EndApplyTime.HasValue, a => a.gr.ApplyTime <= request.EndApplyTime.Value.Date.AddDays(1))
                .WhereIf(request.BuId.HasValue, a => a.gr.ApplyUserBu == request.BuId.Value)
                .WhereIf(!string.IsNullOrWhiteSpace(request.PRApplicationCode), a => a.gr.PrApplicationCode.Contains(request.PRApplicationCode))
                .WhereIf(!string.IsNullOrWhiteSpace(request.POApplicationCode), a => a.gr.PoApplicationCode.Contains(request.POApplicationCode))
                .OrderByDescending(a => a.gr.CreationTime);
            var result = queryGrLinq.Select(a => new ExportGRApplicationDto
            {
                ApplicationCode = a.gr.ApplicationCode,
                ApplyUserName = a.gr.ApplyUserName,
                ApplyDate = a.gr.ApplyTime.ToString("yyyy-MM-dd"),
                Currency = a.gr.Currency,
                TotalAmount = a.grdh == null ? null : a.grdh.TotalAmount,
                TotalAmountRMB = a.grdh == null ? null : (a.grdh.TotalAmount * (string.IsNullOrWhiteSpace(a.gr.Currency) ? 1M : (decimal)a.gr.ExchangeRate)),
                POApplicationCode = a.gr.PoApplicationCode,
                VendorName = a.gr.VendorName,
                POApprovalTime = a.po == null ? "" : (a.po.ApprovedDate == null ? "" : a.po.ApprovedDate.Value.ToString("yyyy-MM-dd HH:mm:ss")),
                PRApplicationCode = a.gr.PrApplicationCode,
                CompanyName = a.gr.CompanyName,
                ApplyUserBuName = a.gr.ApplyUserBuName,
                CostCenterName = a.pr.CostCenterName,
                PAApplicationCode = a.pa == null ? "" : a.pa.ApplicationCode ?? "",
                PAApplyDate = a.pa == null ? "" : a.pa.ApplyTime.Value.ToString("yyyy-MM-dd"),
                PAStatusName = a.pa == null ? "" : a.pa.Status.GetDescription()
            }).ToList();
            return result;
        }
        #endregion

        #region 任务中心 收货申请我发起的视角
        /// <summary>
        ///  收货申请我发起的视角 列表
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<PurGRApplicationResponseDto>> GetGRInitiateListAsync(GRListSearchRequestDto requestDto)
        {
            var result = new PagedResultDto<PurGRApplicationResponseDto>();
            //任务中心 状态 
            PurGRApplicationStatus[] pending = [PurGRApplicationStatus.ToBeReceived, PurGRApplicationStatus.Returned];//待处理
            PurGRApplicationStatus[] progressings = [PurGRApplicationStatus.SignedBy, PurGRApplicationStatus.Termination, PurGRApplicationStatus.PaymentProgress];//进行中
            PurGRApplicationStatus[] completed = [PurGRApplicationStatus.ReceivedGoods, PurGRApplicationStatus.Terminationed];//已完成
            var querygr = await LazyServiceProvider.LazyGetService<IPurGRApplicationReadonlyRepository>().GetQueryableAsync();
            var querypr = await LazyServiceProvider.LazyGetService<IPurPRApplicationReadonlyRepository>().GetQueryableAsync();
            var querypo = await LazyServiceProvider.LazyGetService<IPurPOApplicationReadonlyRepository>().GetQueryableAsync();
            var agents = await LazyServiceProvider.LazyGetService<IAgencyService>().GetOriginalOperators(new GetAgentOperatorsRequestDto { BusinessTypeCategory = ResignationTransfer.TaskFormCategory.GoodsReceiptApplication });
            var userIds = agents.Select(a => a.Key).Distinct().ToArray();
            var principalIds = userIds.Where(a => a != CurrentUser.Id.Value);
            var queryGrLinq = querygr.Where(a => (a.ApplyUserId == CurrentUser.Id.Value && !a.TransfereeId.HasValue) || CurrentUser.Id.Value == a.TransfereeId.Value || principalIds.ToHashSet().Contains(a.ApplyUserId))
                            .WhereIf(ProcessingStatus.PendingProcessing.Equals(requestDto.ProcessingStatus), a => pending.Contains(a.Status))
                            .WhereIf(ProcessingStatus.Progressing.Equals(requestDto.ProcessingStatus), a => progressings.Contains(a.Status))
                            .WhereIf(ProcessingStatus.Completed.Equals(requestDto.ProcessingStatus), a => completed.Contains(a.Status))
                            .WhereIf(requestDto.Status.HasValue, a => a.Status.Equals(requestDto.Status))
                            .WhereIf(!string.IsNullOrWhiteSpace(requestDto.ApplyUserBuName), a => a.ApplyUserBuName.Contains(requestDto.ApplyUserBuName))
                            .WhereIf(!string.IsNullOrWhiteSpace(requestDto.ApplicationCode), a => a.ApplicationCode.Contains(requestDto.ApplicationCode))
                            .WhereIf(!string.IsNullOrWhiteSpace(requestDto.BudgetCode), a => a.BudgetCode.Contains(requestDto.BudgetCode))
                            .WhereIf(!string.IsNullOrWhiteSpace(requestDto.VendorName), a => a.VendorName.Contains(requestDto.VendorName))
                            .WhereIf(requestDto.StartApplyTime.HasValue, a => a.ApplyTime >= requestDto.StartApplyTime.Value.Date)
                            .WhereIf(requestDto.EndApplyTime.HasValue, a => a.ApplyTime <= requestDto.EndApplyTime.Value.Date.AddDays(1))
                            .WhereIf(requestDto.BuId.HasValue, a => a.ApplyUserBu == requestDto.BuId.Value)
                            .WhereIf(!string.IsNullOrWhiteSpace(requestDto.PRApplicationCode), a => a.PrApplicationCode.Contains(requestDto.PRApplicationCode))
                            .WhereIf(!string.IsNullOrWhiteSpace(requestDto.POApplicationCode), a => a.PoApplicationCode.Contains(requestDto.POApplicationCode));
            result.TotalCount = queryGrLinq.Count();
            var queryData = queryGrLinq.OrderByDescending(a => a.CreationTime).Skip(requestDto.PageIndex * requestDto.PageSize).Take(requestDto.PageSize).ToList();
            result.Items = ObjectMapper.Map<List<PurGRApplication>, List<PurGRApplicationResponseDto>>(queryData);
            return result;
        }

        /// <summary>
        /// 审批-我审批的收货申请列表-导出
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        public async Task<List<ExportGRApplicationDto>> ExportGRInitiateListAsync(GRListSearchRequestDto requestDto)
        {
            var querygr = await LazyServiceProvider.LazyGetService<IPurGRApplicationRepository>().GetQueryableAsync();
            var queryGrDetail = await LazyServiceProvider.LazyGetService<IPurGRApplicationDetailRepository>().GetQueryableAsync();
            var queryGrHistory = await LazyServiceProvider.LazyGetService<IPurGRApplicationDetailHistoryRepository>().GetQueryableAsync();
            var querypr = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var querypo = await LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>().GetQueryableAsync();
            var querypa = await LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>().GetQueryableAsync();

            var agents = await LazyServiceProvider.LazyGetService<IAgencyService>().GetOriginalOperators(new GetAgentOperatorsRequestDto { BusinessTypeCategory = ResignationTransfer.TaskFormCategory.GoodsReceiptApplication });
            var userIds = agents.Select(a => a.Key).Distinct().ToArray();
            //任务中心 状态 
            PurGRApplicationStatus[] pending = [PurGRApplicationStatus.ToBeReceived, PurGRApplicationStatus.Returned];//待处理
            PurGRApplicationStatus[] progressings = [PurGRApplicationStatus.SignedBy, PurGRApplicationStatus.Termination, PurGRApplicationStatus.PaymentProgress];//进行中
            PurGRApplicationStatus[] completed = [PurGRApplicationStatus.ReceivedGoods, PurGRApplicationStatus.Terminationed];//已完成

            var queryGrLinq = querygr.Select(a => new
            {
                a.Id,
                a.PoId,
                a.PrId,
                a.ApplicationCode,
                a.ApplyTime,
                a.Currency,
                a.ApplyUserBuName,
                a.PoApplicationCode,
                a.CompanyName,
                a.ApplyUserName,
                TotalAmount = queryGrDetail.Where(b => b.GRApplicationId == a.Id).Any() ? queryGrDetail.Where(b => b.GRApplicationId == a.Id).Sum(b => b.OrderQuantity * b.UnitPrice) : 0M,
                a.ExchangeRate,
                a.Status,
                a.BudgetCode,
                a.VendorName,
                a.ApplyUserBu,
                a.CompanyId,
                a.PrApplicationCode,
                a.CreationTime,
                a.ApplyUserId,
                a.TransfereeId
            }).GroupJoin(querypr.Select(a => new GRExportPRDto { Id = a.Id, ApplicationCode = a.ApplicationCode, CostCenterName = a.CostCenterName, ApplyTime = a.ApplyTime }), a => a.PrId, b => b.Id, (a, b) => new { gr = a, prs = b })
                .SelectMany(a => a.prs.DefaultIfEmpty(), (a, b) => new { a.gr, pr = b })
                .GroupJoin(queryGrHistory.Select(a => new GRExportGRHistoryDto { GRApplicationId = a.GRApplicationId, PurPAApplicationId = a.PurPAApplicationId, TotalAmount = a.ReceivedAmount }), a => a.gr.Id, b => b.GRApplicationId, (a, b) => new { a.gr, a.pr, grdhs = b })
                .SelectMany(a => a.grdhs.DefaultIfEmpty(), (a, b) => new { a.gr, a.pr, grdh = b })
                .GroupJoin(querypo.Select(a => new GRExportPODto { Id = a.Id, ApplicationCode = a.ApplicationCode, ApprovedDate = a.ApprovedDate }), a => a.gr.PoId, b => b.Id, (a, b) => new { a.gr, a.pr, a.grdh, pos = b })
                .SelectMany(a => a.pos.DefaultIfEmpty(), (a, b) => new { a.gr, a.pr, a.grdh, po = b })
                .GroupJoin(querypa.Select(a => new GRExportPADto { Id = a.Id, ApplicationCode = a.ApplicationCode, Status = a.Status, ApplyTime = a.ApplyTime }), a => a.grdh.PurPAApplicationId, b => b.Id, (a, b) => new { a.gr, a.pr, a.grdh, a.po, pas = b })
                .SelectMany(a => a.pas.DefaultIfEmpty(), (a, b) => new { a.gr, a.pr, a.grdh, a.po, pa = b })
                .Where(a => userIds.Contains(a.gr.ApplyUserId) || (a.gr.TransfereeId.HasValue && userIds.Contains(a.gr.TransfereeId.Value)))
                .WhereIf(ProcessingStatus.PendingProcessing.Equals(requestDto.ProcessingStatus), a => pending.Contains(a.gr.Status))
                .WhereIf(ProcessingStatus.Progressing.Equals(requestDto.ProcessingStatus), a => progressings.Contains(a.gr.Status))
                .WhereIf(ProcessingStatus.Completed.Equals(requestDto.ProcessingStatus), a => completed.Contains(a.gr.Status))
                .WhereIf(requestDto.Status.HasValue, a => a.gr.Status.Equals(requestDto.Status))
                .WhereIf(!string.IsNullOrWhiteSpace(requestDto.ApplyUserBuName), a => a.gr.ApplyUserBuName.Contains(requestDto.ApplyUserBuName))
                .WhereIf(!string.IsNullOrWhiteSpace(requestDto.ApplicationCode), a => a.gr.ApplicationCode.Contains(requestDto.ApplicationCode))
                .WhereIf(!string.IsNullOrWhiteSpace(requestDto.BudgetCode), a => a.gr.BudgetCode.Contains(requestDto.BudgetCode))
                .WhereIf(!string.IsNullOrWhiteSpace(requestDto.VendorName), a => a.gr.VendorName.Contains(requestDto.VendorName))
                .WhereIf(requestDto.StartApplyTime.HasValue, a => a.gr.ApplyTime >= requestDto.StartApplyTime.Value.Date)
                .WhereIf(requestDto.EndApplyTime.HasValue, a => a.gr.ApplyTime <= requestDto.EndApplyTime.Value.Date.AddDays(1))
                .WhereIf(requestDto.BuId.HasValue, a => a.gr.ApplyUserBu == requestDto.BuId.Value)
                .WhereIf(!string.IsNullOrWhiteSpace(requestDto.PRApplicationCode), a => a.gr.PrApplicationCode.Contains(requestDto.PRApplicationCode))
                .WhereIf(!string.IsNullOrWhiteSpace(requestDto.POApplicationCode), a => a.gr.PoApplicationCode.Contains(requestDto.POApplicationCode))
                .OrderByDescending(a => a.gr.CreationTime);
            var result = queryGrLinq.Select(a => new ExportGRApplicationDto
            {
                ApplicationCode = a.gr.ApplicationCode,
                ApplyUserName = a.gr.ApplyUserName,
                ApplyDate = a.gr.ApplyTime.ToString("yyyy-MM-dd"),
                Currency = a.gr.Currency,
                TotalAmount = a.grdh == null ? null : a.grdh.TotalAmount,
                TotalAmountRMB = a.grdh == null ? null : (a.grdh.TotalAmount * (string.IsNullOrWhiteSpace(a.gr.Currency) ? 1M : (decimal)a.gr.ExchangeRate)),
                POApplicationCode = a.gr.PoApplicationCode,
                VendorName = a.gr.VendorName,
                POApprovalTime = a.po == null ? "" : (a.po.ApprovedDate == null ? "" : a.po.ApprovedDate.Value.ToString("yyyy-MM-dd HH:mm:ss")),
                PRApplicationCode = a.gr.PrApplicationCode,
                CompanyName = a.gr.CompanyName,
                ApplyUserBuName = a.gr.ApplyUserBuName,
                CostCenterName = a.pr.CostCenterName,
                PAApplicationCode = a.pa == null ? "" : a.pa.ApplicationCode ?? "",
                PAApplyDate = a.pa == null ? "" : a.pa.ApplyTime.Value.ToString("yyyy-MM-dd"),
                PAStatusName = a.pa == null ? "" : a.pa.Status.GetDescription()
            }).ToList();
            return result;
        }
        #endregion
    }
}
