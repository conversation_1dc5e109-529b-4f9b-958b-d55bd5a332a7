﻿using DocumentFormat.OpenXml.Packaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.AppServices.UriRelationship
{
    /// <summary>
    /// Excel 读去错误处理
    /// </summary>
    public class UriRelationshipErrorRewriter : RelationshipErrorHandler
    {
        /// <summary>
        /// 处理导入模板文件时，邮箱等格式出现超链接错误问题
        /// </summary>
        /// <param name="partUri"></param>
        /// <param name="id"></param>
        /// <param name="uri"></param>
        /// <returns></returns>
        public override string Rewrite(Uri partUri, string id, string uri)
        {
            return "https://abbott-link";//不存在的地址，用于填充Excel中超链接报错地址
        }
    }
}
