﻿using Abbott.SpeakerPortal.Entities.Purchase.PurGRApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPOApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using System;
using System.Linq.Expressions;

namespace Abbott.SpeakerPortal.Contracts.Integration.Dspot
{
    /// <summary>
    /// 全流程报表，查询每个阶段的查询条件（PR、PO、GR、PA、PAInvoice、出纳）
    /// </summary>
    public class DspotReportConditionDto
    {
        public Expression<Func<PurPRApplication, bool>> expPR { get; set; }

        public Expression<Func<PurPOApplication, bool>> expPO { get; set; }

        public Expression<Func<PurGRApplication, bool>> expGR { get; set; }

        public Expression<Func<PurPAApplication, bool>> expPA { get; set; }

        public Expression<Func<PurPAApplicationInvoice, bool>> expPAInvoice { get; set; }
    }
}