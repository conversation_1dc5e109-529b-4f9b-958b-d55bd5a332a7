﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Localization;
using Abbott.SpeakerPortal.Redis;
using Abbott.SpeakerPortal.Utils;

using Microsoft.AspNetCore.Authorization;

using Volo.Abp;
using Volo.Abp.Application.Services;
using Volo.Abp.DistributedLocking;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.Domain.Repositories;

namespace Abbott.SpeakerPortal;

/* Inherit your application services from this class.
 */
[RemoteService(IsEnabled = false)]
public abstract class SpeakerPortalAppService : ApplicationService
{
    protected SpeakerPortalAppService()
    {
        LocalizationResource = typeof(SpeakerPortalResource);
    }

    public async Task<string> InsertAndGenerateSerialNoAsync<T>(IRepository<T, Guid> repository, T entity, string prefix = null) where T : FullAuditedEntity<Guid>
    {
        var distributedLockKey = $"{DistributedLock.DistributedLock_GenerateSerialNo}_{typeof(T).Name}";
        await using (var handle = await LazyServiceProvider.LazyGetService<IAbpDistributedLock>().TryAcquireAsync(distributedLockKey, TimeSpan.FromSeconds(5)))
        {
            if (handle != null)
            {
                int count;
                var today = DateTimeOffset.Now.Date;
                var redisRepository = LazyServiceProvider.LazyGetService<IRedisRepository>();
                //检索缓存中是否有数值
                var redisKey = $"{distributedLockKey}_{today:yyyyMMdd}";
                if (!redisRepository.Database.KeyExists(redisKey))
                {
                    using (DataFilter.Disable<ISoftDelete>())
                    {
                        count = await repository.CountAsync(a => a.CreationTime.Date == today);
                    }
                }
                else
                    count = (int)redisRepository.Database.StringGet(redisKey);

                //todo:与BPM并行期间，采购申请单号规则调整5000开始（后续可能需要还原）
                var no = $"{prefix}{today:yyMMdd}{++count + 5000:D4}";
                if (entity is IGenerateSerialNo)
                {
                    ((IGenerateSerialNo)entity).SetSerialNo(no);
                    await repository.InsertAsync(entity, true);
                }

                //设置到凌晨过期
                var expiry = today.AddDays(1) - DateTimeOffset.Now.DateTime;
                redisRepository.Database.StringSet(redisKey, count, expiry);

                return no;
            }
        }

        throw new TimeoutException("生成编号超时，请重新操作");
    }
}
