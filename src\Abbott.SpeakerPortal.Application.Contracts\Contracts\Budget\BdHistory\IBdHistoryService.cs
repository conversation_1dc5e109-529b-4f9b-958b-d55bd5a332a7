﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.Contracts.Budget
{
    public interface IBdHistoryService
    {
        /// <summary>
        /// 根据Id获取历史记录数据
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        Task<List<HistoryRecordsResponseDto>> GetHistoryRecordsByIdAsync(Guid Id);
        /// <summary>
        /// 导出预算操作历史记录
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        Task<Stream> ExportHistoryExcelAsync(Guid Id);
    }
}
