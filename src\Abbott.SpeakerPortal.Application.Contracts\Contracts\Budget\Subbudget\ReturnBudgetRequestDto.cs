﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace Abbott.SpeakerPortal.Contracts.Budget.Subbudget
{
    public class ReturnBudgetRequestDto
    {
        /// <summary>
        /// 采购申请单Id
        /// </summary>
        [Required]
        public Guid PrId { get; set; }
        /// <summary>
        /// 子预算Id
        /// </summary>
        [Required]
        public Guid SubbudgetId { get; set; }
        /// <summary>
        /// 使用详情列表
        /// </summary>
        [Required]
        public IEnumerable<ReturnInfo> Items { get; set; }
    }

    public class ReturnInfo
    {
        /// <summary>
        /// 采购明细的行号
        /// </summary>
        [Required]
        public int PdRowNo { get; set; }
        /// <summary>
        /// 退回金额
        /// </summary>
        [Required]
        public decimal ReturnAmount { get; set; }
        /// <summary>
        /// 退回的来源单据Id
        /// </summary>
        [Required]
        public Guid ReturnSourceId { get; set; }
        /// <summary>
        /// 退回的来源单据Code
        /// </summary>
        public string ReturnSourceCode { get; set; }
    }
}
