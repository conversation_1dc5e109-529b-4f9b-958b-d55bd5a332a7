﻿using Abbott.SpeakerPortal.Contracts.Dataverse;
using Abbott.SpeakerPortal.Contracts.OEC.PayStandard;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.User;

using System;
using System.Collections.Generic;
using System.Threading.Tasks;

using Volo.Abp.Application.Dtos;
using Volo.Abp.DependencyInjection;

using static Abbott.SpeakerPortal.Enums.DataverseEnums;

namespace Abbott.SpeakerPortal.Dataverse
{
    /// <summary>
    /// IDataverseService
    /// </summary>
    public interface IDataverseService : ITransientDependency
    {
        /// <summary>
        /// 获取机构信息
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="count"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<List<DepartmentDto>> GetOrganizations(string pattern = null, int count = int.MaxValue, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 获取员工数据
        /// </summary>
        /// <param name="pattern">Id\StaffCode</param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<IEnumerable<StaffDto>> GetStaffs(string pattern = null, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 添加一条员工数据
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<Guid> AddStaffAsync(AddStaffRequestDto request);

        /// <summary>
        /// 获取员工和机构关系
        /// </summary>
        /// <param name="pattern">UserId\OrganId</param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<IEnumerable<StaffDepartmentRelationDto>> GetStaffDepartmentRelations(string pattern = null, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// Gets all job tiles.
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<List<JobTitleDto>> GetAllJobTiles(string pattern = null, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// Gets all hospitals.
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="count"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<List<HospitalDto>> GetAllHospitals(string pattern = null, int count = int.MaxValue, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 添加医院信息
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<HospitalDto> AddHospitalAsync(AddHospitalRequestDto request);

        /// <summary>
        /// 修改医院信息
        /// </summary>
        /// <param name="id">PP里的Id</param>
        /// <param name="dicFields">Key:字段名，Value：字段值</param>
        /// <returns></returns>
        Task<bool> UpdateHospitalAsync(Guid id, Dictionary<string, object> dicFields);

        /// <summary>
        /// 根据Id，修改该条数据
        /// </summary>
        /// <param name="ettName">Entity Name</param>
        /// <param name="id">PP里的Id</param>
        /// <param name="dicFields">Key:字段名，Value：字段值</param>
        /// <returns></returns>
        Task<bool> UpdateAsync(string ettName, Guid id, Dictionary<string, object> dicFields);

        /// <summary>
        /// 获取科室数据
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<List<OfficeDto>> GetAllDepartments(string pattern = null, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 添加科室主数据（标准科室）
        /// </summary>
        Task<Guid> AddDeptAsync(OfficeDto request);

        /// <summary>
        /// 添加职称主数据
        /// </summary>
        Task<Guid> AddJobTitleAsync(JobTitleDto request);

        /// <summary>
        /// 根据类型获取字典数据
        /// </summary>
        /// <param name="type"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<IEnumerable<DictionaryDto>> GetDictionariesAsync(string type = null, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 获取产品数据
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<IEnumerable<ProductDto>> GetProductsAsync(string pattern = null, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 获取机构和产品关系
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<IEnumerable<OrgProducRelationtDto>> GetOrgProductRelationsAsync(string pattern = null, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 获取成本中心数据
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<IEnumerable<CostcenterDto>> GetCostcentersAsync(string pattern = null, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 获取消费大类数据
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<IEnumerable<ConsumeCategoryDto>> GetConsumeCategoryAsync(string pattern = null, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 获取消费大类和赞助类型的关系
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<IEnumerable<ConsumeCategorySponsorshipTypeRelationDto>> GetConsumeCategorySponsorshipTypeRelationDtosAsync(string pattern = null, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 获取费用性质数据
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<IEnumerable<CostNatureDto>> GetCostNatureAsync(string pattern = null, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 获取机构和费用性质的关系
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<IEnumerable<OrgCostNatureRelationDto>> GetOrgCostNatureRelationAsync(string pattern = null, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 获取讲者类型映射关系
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<IEnumerable<VendorTypeMappingDto>> GetVendorTypeCfgAsync(string pattern = null, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 获取BU、成本中心、费用性质映射关系
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<IEnumerable<CoaMappingRuleDto>> GetCoaMappingRuleAsync(string pattern = null, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 获取BU编码配置
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<IEnumerable<BuCodingCfgDto>> GetBuCodingCfgAsync(string pattern = null, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 获取公司数据
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<List<CompanyDto>> GetCompanyList(string pattern = null, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 获取公司货币信息
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<List<CurrencyDto>> GetCompanyCurrencyList(string pattern = null, StateCode? stateCode = StateCode.Active);

        #region 修改公司币种
        /// <summary>
        /// 获取公司主数据
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<IEnumerable<CompanyMasterDataDto>> GetCompanyAsync(string pattern = null, StateCode? stateCode = StateCode.Active);
        /// <summary>
        /// 获取公司主数据与货币字典的映射
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<IEnumerable<CompanyCurrencyDto>> GetCompanyCurrencyAsync(string pattern = null, StateCode? stateCode = StateCode.Active);
        /// <summary>
        /// 获取公司和币种扩展信息
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<IEnumerable<CurrencyDto>> GetCompanyCurrencyExtAsync(string pattern = null, StateCode? stateCode = StateCode.Active);
        /// <summary>
        /// 获取公司币种关联信息
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<IEnumerable<CompanyDto>> GetCompanyCurrencyInfoAsync(string pattern = null, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 获取公司主数据组织映射
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<IEnumerable<CompanyAndOrgRelationDto>> GetCompanyAndOrgRelationAsync(string pattern = null, StateCode? stateCode = StateCode.Active);
        #endregion


        /// <summary>
        /// PaymentTerms
        /// </summary>
        /// <returns></returns>
        Task<IEnumerable<DictionaryDto>> GetSimplePaymentTermsAsync();

        /// <summary>
        /// 获取城市主数据spk_citymasterdata
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<IEnumerable<CityMasterDataDto>> GetSpecialCitiesAsync(string pattern = null, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 获取机构和城市主数据的关系
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<IEnumerable<OrgSpecialCityRelationDto>> GetOrgSpecialCityRelationAsync(string pattern = null, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 获取Division/BU
        /// </summary>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<IEnumerable<DepartmentDto>> GetDivisions(StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 获取审批记录
        /// </summary>
        /// <param name="formId"></param>
        /// <returns></returns>
        Task<IEnumerable<WorkflowInstanceDto>> GetApplicationRecordAsync(string formId);

        /// <summary>
        /// 获取审批人节点
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="workflowType"></param>
        /// <param name="processingStatus"></param>
        /// <returns></returns>
        Task<IEnumerable<WorkflowTaskDto>> GetApprovelTaskAsync(string userId, WorkflowTypeName[] workflowType, ProcessingStatus processingStatus = ProcessingStatus.Progressing);

        /// <summary>
        /// 获取审批人节点
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="workflowTypes"></param>
        /// <param name="approvalPowerAppStatuses"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        Task<PagedResultDto<WorkflowTaskDto>> GetApprovalTaskAsync(string userId, IEnumerable<WorkflowTypeName> workflowTypes, IEnumerable<ApprovalPowerAppStatus> approvalPowerAppStatuses, int pageIndex = 1, int pageSize = 10);

        /// <summary>
        /// 获取审批流Task
        /// </summary>
        /// <param name="businessId"></param>
        /// <param name="taskId"></param>
        /// <returns></returns>
        Task<IEnumerable<WorkflowTaskDto>> GetWorkflowTaskAsync(Guid businessId, Guid? taskId = null);

        /// <summary>
        /// 获取大区
        /// </summary>
        /// <param name="pattern">The pattern.</param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<IEnumerable<DistrictDto>> GetDistrict(string pattern = null, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 获取省份
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<List<ProvinceDto>> GetAllProvince(string pattern = null, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 获取城市
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<List<CityDto>> GetAllCity(string pattern = null, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 获取计酬主数据
        /// </summary>
        /// <param name="pattern">The pattern.</param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<IEnumerable<CompensationDto>> GetCompensationAsync(string pattern = null, StateCode? stateCode = StateCode.Active);

        Task<IEnumerable<KeyValuePair<Guid, string>>> GetStaffListByPositionAndFlow(Guid bu, PositionType positionType);

        /// <summary>
        /// 获取发票税率映射关系
        /// </summary>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<IEnumerable<InvoiceTaxRateMappDto>> GetInvoiceTaxRateMappAsync(StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 获取紧急付款类型配置
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<IEnumerable<PturtTypeConfigDto>> GetPturtTypeConfigDtoAsync(string pattern = null, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 发起PO金额规则
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<IEnumerable<POInitiateAmountDto>> GetPoInitiateAmountAsync(string pattern = null, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 获取全部退单子原因
        /// </summary>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<IEnumerable<ReturnSubReasonDto>> GetReturnSubReasonAsync(StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 获取退单原因模板
        /// </summary>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<IEnumerable<PaReturnReasonDto>> GetPaReturnReasonAsync(StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 获取成本中心BU映射关系
        /// </summary>
        /// <param name="pattern">The bu identifier.</param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        /// <exception cref="System.NotImplementedException"></exception>
        Task<IEnumerable<OrgCostcenterDto>> GetCostCenterOrgRelationsAsync(string pattern = null, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 获取大区BU映射关系
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        /// <exception cref="System.NotImplementedException"></exception>
        Task<IEnumerable<OrgDistrictDto>> GetOrgDistrictRelationsAsync(string pattern = null, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 获取城市公司映射关系
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        /// <exception cref="System.NotImplementedException"></exception>
        Task<IEnumerable<CityCompanyDto>> GetCityCompanyRelationsAsync(string pattern = null, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 获取审批矩阵配置
        /// </summary>
        /// <returns></returns>
        Task<IEnumerable<ExpenseApprovalMatrixCfgItemDto>> GetExpenseApprovalMatrixConfigurationItemAsync();

        /// <summary>
        /// 获取财务审批金额矩阵
        /// </summary>
        /// <returns></returns>
        Task<IEnumerable<FinancialApprovalAmountMatrixDto>> GetFinancialApprovalAmountMatrixAsync();

        /// <summary>
        /// 获取货币配置信息
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<IEnumerable<CurrencyConfigDto>> GetCurrencyConfig(string pattern = null, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 获取采购推送人员配置
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<IEnumerable<ProcurementPushConfigDto>> GetProcurementPushConfigAsync(string pattern = null, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 根据岗位获取员工数据
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<IEnumerable<StaffAndPositionDto>> GetStaffAndPositionAsync(string pattern = null, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 获取业务类型
        /// </summary>
        /// <param name="pattern">The pattern.</param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<IEnumerable<BusinessTypeDto>> GetBusinessTypeAsync(string pattern = null, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 获取业务类型与审批流类型的Mapping
        /// </summary>
        /// <param name="pattern">The pattern.</param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<IEnumerable<BusinessTypeAndWorkflowTypeDto>> GetBusinessTypeAndWorkflowTypeAsync(string pattern = null, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 获取活动类型与成本中心配置信息
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<IEnumerable<ActiveTypeCostcenterMappingDto>> GetActiveTypeCostcenterMappingsAsync(string pattern = null, StateCode? stateCode = StateCode.Active);


        /// <summary>
        /// 获取EPD业务系统医院数据
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="pageSize"></param>
        /// <param name="count"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<List<BUHospitalDto>> GetEPDBUHospitals(string pattern = null, int pageSize = 10000, int count = int.MaxValue, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 获取所有业务系统医院主数据
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<List<BUHospitalDto>> GetAllBUHospitals(string pattern = null, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 获取所有业务系统医院主数据，直接从PP获取，不读缓存
        /// </summary>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<List<BUHospitalDto>> GetAllBUHospitalsFromPP(StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 从Redis删除PP 医院主数据
        /// </summary>
        /// <returns></returns>
        bool DeleteHospitalFromRedis();

        /// <summary>
        /// 删除PP 医院主数据
        /// </summary>
        /// <returns></returns>
        bool DeleteHospitalFromPP(List<Guid> ids);

        /// <summary>
        /// 获取系统配置
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<List<SystemInterationConfigDto>> GetAllSystemInterationConfig(string pattern = null, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 获取特殊采购审批条件配置
        /// </summary>
        /// <param name="pattern"></param>
        /// <param name="stateCode"></param>
        /// <returns></returns>
        Task<IEnumerable<SpecApprovalConditionProcurementDto>> GetSpecApprovalConditionProcurementAsync(string pattern = null, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 获取指定单据的最新的审批中的审批流实例的FormData
        /// </summary>
        /// <param name="formId"></param>
        /// <returns></returns>
        Task<string> GetWorkflowInstanceFormData(Guid formId);

        /// <summary>
        /// 根据组织及岗位获取 岗位配置
        /// </summary>
        /// <param name="deptId"></param>
        /// <param name="positionType"></param>
        /// <returns></returns>
        Task<List<OrgAndStaffDto>> GetOrgAndStaffByFlowAsync(Guid deptId, PositionType positionType);

        /// <summary>
        /// 获取旅行社供应商列表
        /// </summary>
        /// <returns></returns>
        Task<IEnumerable<DictionaryDto>> GetTravelagencyAsync();
        Task<IEnumerable<ProductCostDto>> GetProductCostAsync(string pattern = null, StateCode? stateCode = StateCode.Active);

        /// <summary>
        /// 获取特殊名称供应商
        /// </summary>
        /// <param name="pattern"></param>
        /// <returns></returns>
        Task<IEnumerable<SpecialVendorDto>> GetSpecialvendorAsync(string pattern = null);
    }
}
