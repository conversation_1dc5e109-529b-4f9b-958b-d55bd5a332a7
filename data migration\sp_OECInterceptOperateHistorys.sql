CREATE PROCEDURE [dbo].[sp_OECInterceptOperateHistorys]
AS 
BEGIN	
	--飞检拦截状态信息-详细历史信息
with OECInterceptOperateHistorys_tmp as (
select 
newid() AS Id,--自动生成的uuid
sd.id as bpm_id,
Concat(sd.ProcInstId,'+',sd.PRRowNumber) AS PRDetailId,
case when OperationType ='Sampling' then '1'
when OperationType ='Hold' then '1'
when OperationType ='Pass' then '2'
end AS OperateType,
'' AS OperateContent,
'' AS Remark,
sd.OperationDate AS OperateTime,
'{}' AS ExtraProperties,
'' AS ConcurrencyStamp,
sd.OperationDate AS CreationTime,
sd.SamplingEmpId AS CreatorId,
'' AS LastModificationTime,
'' AS LastModifierId,
'0' AS IsDeleted,
'' AS DeleterId,
'' AS DeletionTime,
Concat(sd.ProcInstId,'+',sd.PRRowNumber) AS InterceptId,
sd.SamplingEmpId AS OperateUserId,
t2.Emp_Name AS OperateUserName,
'' AS SendBackLimitAmount,
case when OperationType ='Sampling' then ''
when OperationType ='Hold' then ''
when OperationType ='Pass' then 'OEC'
end AS SolveInterceptType
FROM [PLATFORM_ABBOTT_Dev].[dbo].[ODS_T_OEC_SamplingDataLog] sd
left join
[PLATFORM_ABBOTT_Dev].[dbo].[ODS_T_EMPLOYEE] t2
on sd.SamplingEmpId = t2.Emp_Id)
select * into #OECInterceptOperateHistorys_tmp from OECInterceptOperateHistorys_tmp

 IF OBJECT_ID(N'PLATFORM_ABBOTT_Dev.dbo.OECInterceptOperateHistorys_tmp ', N'U') IS NOT NULL
	BEGIN
		update a 
		set a.bpm_id               = b.bpm_id
          ,a.PRDetailId            = b.PRDetailId
          ,a.OperateType           = b.OperateType
          ,a.OperateContent        = b.OperateContent
          ,a.Remark                = b.Remark
          ,a.OperateTime           = b.OperateTime
          ,a.ExtraProperties       = b.ExtraProperties
          ,a.ConcurrencyStamp      = b.ConcurrencyStamp
          ,a.CreationTime          = b.CreationTime
          ,a.CreatorId             = b.CreatorId
          ,a.LastModificationTime  = b.LastModificationTime
          ,a.LastModifierId        = b.LastModifierId
          ,a.IsDeleted             = b.IsDeleted
          ,a.DeleterId             = b.DeleterId
          ,a.DeletionTime          = b.DeletionTime
          ,a.InterceptId           = b.InterceptId
          ,a.OperateUserId         = b.OperateUserId
          ,a.OperateUserName       = b.OperateUserName
          ,a.SendBackLimitAmount   = b.SendBackLimitAmount
          ,a.SolveInterceptType    = b.SolveInterceptType
      from PLATFORM_ABBOTT_Dev.dbo.OECInterceptOperateHistorys_tmp a
      left join #OECInterceptOperateHistorys_tmp b on a.Bpm_id = b.Bpm_id 
      
      insert into PLATFORM_ABBOTT_Dev.dbo.OECInterceptOperateHistorys_tmp
      select a.Id
            ,a.bpm_id
            ,a.PRDetailId
            ,a.OperateType
            ,a.OperateContent
            ,a.Remark
            ,a.OperateTime
            ,a.ExtraProperties
            ,a.ConcurrencyStamp
            ,a.CreationTime
            ,a.CreatorId
            ,a.LastModificationTime
            ,a.LastModifierId
            ,a.IsDeleted
            ,a.DeleterId
            ,a.DeletionTime
            ,a.InterceptId
            ,a.OperateUserId
            ,a.OperateUserName
            ,a.SendBackLimitAmount
            ,a.SolveInterceptType
    from #OECInterceptOperateHistorys_tmp a
    where not exists (select * from PLATFORM_ABBOTT_Dev.dbo.OECInterceptOperateHistorys_tmp where Bpm_id = a.Bpm_id)
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into PLATFORM_ABBOTT_Dev.dbo.OECInterceptOperateHistorys_tmp from #OECInterceptOperateHistorys_tmp
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END

END;

