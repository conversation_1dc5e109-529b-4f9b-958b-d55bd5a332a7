﻿using Abbott.SpeakerPortal.Enums;
using System;
using System.Collections.Generic;
using System.Text;

namespace Abbott.SpeakerPortal.Contracts.Agent
{
    public class GetAgentByOriginalOperatorRequestDto
    {
        /// <summary>
        /// 原操作人Id
        /// </summary>
        public Guid OriginalOperatorId { get; set; }
        /// <summary>
        /// 业务表单类型，可空，空即所有类型
        /// </summary>
        public ResignationTransfer.TaskFormCategory? BusinessTypeCategory { get; set; }
    }
}
