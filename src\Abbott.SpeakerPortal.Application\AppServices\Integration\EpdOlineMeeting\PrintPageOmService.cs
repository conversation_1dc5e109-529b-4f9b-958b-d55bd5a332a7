﻿using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Common.Approve;
using Abbott.SpeakerPortal.Contracts.Common.PowerApp;
using Abbott.SpeakerPortal.Contracts.Integration.EpdOlineMeeting;
using Abbott.SpeakerPortal.Contracts.OEC.SpeakerAuth;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Utils;

using DocumentFormat.OpenXml.Office.CustomUI;

using Microsoft.AspNetCore.Hosting;
using Microsoft.Crm.Sdk.Messages;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Logging;

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

using static Abbott.SpeakerPortal.Enums.PurPAStatus;

namespace Abbott.SpeakerPortal.AppServices.Integration.EpdOlineMeeting
{
    public class PrintPageOmService : SpeakerPortalAppService, IPrintPageOmService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<PrintPageOmService> _logger;
        private readonly IConfiguration _configuration;
        private readonly IOmAuthorizationService _omAuthorizationService;
        private readonly IPurPRApplicationService _purPRApplicationService;
        private readonly IPurPAApplicationService _purPAApplicationService;
        private readonly IApproveService _approveService;

        public PrintPageOmService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _logger = serviceProvider.GetService<ILogger<PrintPageOmService>>();
            _configuration = serviceProvider.GetService<IConfiguration>();
            _omAuthorizationService = serviceProvider.GetService<IOmAuthorizationService>();
            _purPRApplicationService = serviceProvider.GetService<IPurPRApplicationService>();
            _purPAApplicationService = serviceProvider.GetService<IPurPAApplicationService>();
            _approveService = serviceProvider.GetService<IApproveService>();
        }

        public async Task<MessageResult> GetPRPrintPageContent(OmPrintPageRequestDto request)
        {
            MessageResult validateResult = OmPrintPageRequestDto.Validate(request, _configuration);
            if (!validateResult.Success)
            {
                return validateResult;
            }

            request.Authorization.Id = request.Id;
            MessageResult checkSignResult = _omAuthorizationService.ValidateAuthorization(request.Authorization);
            if (!checkSignResult.Success)
            {
                return checkSignResult;
            }

            try
            {
                var pr = await _purPRApplicationService.GetPRApplicationAsync(request.Id.Value);
                var approvalRecordResult = await _approveService.GetApprovalRecordAsync(request.Id.Value);
                if (!approvalRecordResult.Success)
                {
                    return approvalRecordResult;
                }

                OmPrPrintPageResponseDto response = new OmPrPrintPageResponseDto()
                {
                    PRDetail = pr,
                    ApprovalRecords = approvalRecordResult.Data as List<ApprovalRecordDto> ?? new List<ApprovalRecordDto>(),
                };

                return MessageResult.SuccessResult(response);
            }
            catch (Exception ex)
            {
                return MessageResult.FailureResult("Failed to get pr print page content, error message:" + ex.Message);
            }
        }

        public async Task<MessageResult> GetPAPrintPageContent(OmPrintPageRequestDto request)
        {
            MessageResult validateResult = OmPrintPageRequestDto.Validate(request, _configuration);
            if (!validateResult.Success)
            {
                return validateResult;
            }

            request.Authorization.Id = request.Id;
            MessageResult checkSignResult = _omAuthorizationService.ValidateAuthorization(request.Authorization);
            if (!checkSignResult.Success)
            {
                return checkSignResult;
            }

            try
            {
                var getPaResult = await _purPAApplicationService.GetPAApplicationDetailsAsync(request.Id.Value);
                if (!getPaResult.Success)
                {
                    return getPaResult;
                }

                var approvalRecordResult = await _approveService.GetApprovalRecordAsync(request.Id.Value);
                if (!approvalRecordResult.Success)
                {
                    return approvalRecordResult;
                }

                var paDetail = getPaResult.Data as PADetailsResponseDto;
                if (paDetail != null && paDetail.PAApplication != null)
                {
                    DescriptionAttribute deliveryModeDescriptionAttribute = paDetail.PAApplication.DeliveryMode.GetType().GetField(paDetail.PAApplication.DeliveryMode.ToString()).GetCustomAttribute<DescriptionAttribute>();
                    paDetail.PAApplication.DeliveryModeString = deliveryModeDescriptionAttribute == null ? paDetail.PAApplication.DeliveryMode.ToString() : deliveryModeDescriptionAttribute.Description;

                    DescriptionAttribute paymentTypeDescriptionAttribute = paDetail.PAApplication.PaymentType.GetType().GetField(paDetail.PAApplication.PaymentType.ToString())?.GetCustomAttribute<DescriptionAttribute>();
                    paDetail.PAApplication.PaymentTypeString = paymentTypeDescriptionAttribute == null ? paDetail.PAApplication.PaymentType.ToString() : paymentTypeDescriptionAttribute.Description;
                }

                OmPaPrintPageResponseDto response = new OmPaPrintPageResponseDto()
                {
                    PADetail = paDetail,
                    ApprovalRecords = approvalRecordResult.Data as List<ApprovalRecordDto> ?? new List<ApprovalRecordDto>(),
                };

                return MessageResult.SuccessResult(response);
            }
            catch (Exception ex)
            {
                return MessageResult.FailureResult("Failed to get pr print page content, error message:" + ex.Message);
            }
        }

        /// <summary>
        /// 获取PR打印页Html
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> GetPRPrintPageAsync(OmAuthorizationDto request)
        {
            var checkSignResult = _omAuthorizationService.ValidateAuthorization(request);
            if (!checkSignResult.Success)
                return checkSignResult;

            var pr = await _purPRApplicationService.GetPRApplicationAsync(request.Id.Value);
            var approvalRecordResult = await _approveService.GetApprovalRecordAsync(request.Id.Value);
            if (!approvalRecordResult.Success)
                return approvalRecordResult;

            var webRoot = LazyServiceProvider.LazyGetService<IWebHostEnvironment>().WebRootFileProvider;
            var html = await webRoot.GetFileInfo("Templates/PrintPage/pr-print.html").ReadAsStringAsync();

            #region 生成Html

            //申请信息
            html = html.Replace("{ApplicationCode}", pr.ApplicationCode);
            html = html.Replace("{ApplyTime}", pr.ApplyTime);
            html = html.Replace("{Applicant}", pr.Applicant);
            html = html.Replace("{ApplyUserDeptName}", pr.ApplyUserDeptName);
            html = html.Replace("{CostCenterName}", pr.CostCenterName);

            //预算信息
            html = html.Replace("{BudgetCode}", pr.Budget?.BudgetCode);
            html = html.Replace("{BudgetFileNames}", pr.Budget?.Files?.Select(a => a.FileName).JoinAsString(","));
            html = html.Replace("{BudgetDescription}", pr.Budget?.Description);
            html = html.Replace("{OrgName}", pr.Budget?.OrgName);
            html = html.Replace("{BudgetCostcenterName}", pr.Budget?.CostcenterName);
            html = html.Replace("{BudgetLead}", pr.Budget?.Lead);
            html = html.Replace("{BudgetTotalAmount}", pr.Budget?.TotalAmount.ToString("N2"));
            html = html.Replace("{BudgetUsedAmount}", pr.Budget?.UsedAmount.ToString("N2"));
            html = html.Replace("{BudgetAvailableAmount}", pr.Budget?.AvailableAmount.ToString("N2"));

            //消费大类
            html = html.Replace("{ExpenseTypeName}", pr.ExpenseTypeName);
            html = html.Replace("{BudgetRegionName}", pr.BudgetRegionName);
            html = html.Replace("{ProductNames}", pr.ProductNames);
            html = html.Replace("{CompanyIdName}", pr.CompanyIdName);

            //费用明细
            if (pr.DetailItems?.Any() == true)
            {
                decimal totalAmount = 0;
                var sb = new StringBuilder();
                var row = "<tr><td rowspan=\"2\">{0}</td><td>{1}</td><td>{2}</td><td>{3}</td><td>{4}</td><td>{5}</td><td>{6}</td><td>{7}</td><td>{8}</td><td>{9}</td><td>{10}</td><td>{11}</td></tr><tr><td>内容</td><td colspan=\"10\">{12}</td></tr>";
                foreach (var item in pr.DetailItems)
                {
                    sb.AppendFormat(row, item.RowNo, item.PayMethodName, item.VendorIdName ?? item.VendorName, item.CostNatureName, item.Costcenter, item.CityIdName, item.Quantity, item.UnitPrice?.ToString("N2"), item.TotalAmount?.ToString("N2"), item.IcbAmount?.ToString("N2"), item.EstimateDate, item.RceNo, item.Content);

                    totalAmount += item.TotalAmount ?? 0;
                }

                sb.Append($"<tr><td colspan=\"10\" style=\"text-align: right\">合计</td><td colspan=\"2\">{totalAmount.ToString("N2")}</td></tr>");

                html = html.Replace("{CostDetails}", sb.ToString());
            }
            else
                html = html.Replace("{CostDetails}", string.Empty);

            //活动信息
            html = html.Replace("{Remark}", pr.Remark);
            html = html.Replace("{PrLateDescription}", pr.PrLateDescription);
            html = html.Replace("{ActiveLaunchCity}", pr.ActiveLaunchCity);
            html = html.Replace("{ActiveHostCity}", pr.ActiveHostCity);
            html = html.Replace("{AcitveHostAddress}", pr.AcitveHostAddress);
            html = html.Replace("{AcitveDate}", pr.AcitveDate);
            html = html.Replace("{ActiveTypeName}", pr.ActiveTypeName);

            //补充信息

            IEnumerable<string> expenseTypes = [ExpenseTypeCodeConst.COSTSUPPORT, ExpenseTypeCodeConst.ABBOTTORGANIZEDMEETING, ExpenseTypeCodeConst.PATIENTANDCONSUMER, ExpenseTypeCodeConst.DIRECTSPONSORSHIP, ExpenseTypeCodeConst.MEETINGSPONSORSHIP, ExpenseTypeCodeConst.INDIRECTSPONSORSHIP, ExpenseTypeCodeConst.INTERNALMEETING, ExpenseTypeCodeConst.CONSULTINGPROJECTHCP];

            if (expenseTypes.Contains(pr.ExpenseTypeCode))
            {
                var additionalInfo = @$"
		<div class=""table-title"">补充信息</div>
		<table class=""info-table"">
			<colgroup>
				<col style=""width: 25%"" />
				<col style=""width: 25%"" />
				<col style=""width: 25%"" />
				<col style=""width: 25%"" />
			</colgroup>
			<tr>
				<td>赞助类型</td>
				<td>{pr.SponsorshipTypeName}</td>
				<td>活动负责人</td>
				<td>{pr.ActiveLeader}</td>
			</tr>
			<tr>
				<td>会议名称</td>
				<td>{pr.MeetingName}</td>
				<td>是否系列会</td>
				<td>{(pr.IsSerialMeeting == true ? "是" : "否")}</td>
			</tr>
			<tr>
				<td>会议时间</td>
				<td>{(DateTime.TryParse(pr.MeetingDate, out DateTime meetingDate) ? meetingDate.ToString("yyyy-MM-dd") : pr.MeetingDate)}</td>
				<td>计划直接赞助/邀请参会/提供专业服务的医疗保健专业人士人数</td>
				<td>{pr.NumberOfProfessionals}</td>
			</tr>
			<tr>
				<td>计划直接赞助/邀请参会/提供专业服务的医疗保健专业人士专业领域</td>
				<td colspan=""3"">{pr.AttendeeExpertise}</td>
			</tr>
			<tr>
				<td>选择理由</td>
				<td colspan=""3"">{(pr.ChoiceReason ? "确认选择对象的专业领域与会议主题相符合，且有必要参加。" : string.Empty)}</td>
			</tr>
		</table>";

                html = html.Replace("{AdditionalInfo}", additionalInfo);
            }
            else
                html = html.Replace("{AdditionalInfo}", string.Empty);

            //附件
            if (pr.SupportFiles?.Any() == true)
            {
                var sb = new StringBuilder();
                var row = "<tr><td>{0}</td><td>{1}</td></tr>";
                foreach (var item in pr.SupportFiles)
                    sb.AppendFormat(row, item.FileName, item.FileSize);

                html = html.Replace("{Attachments}", sb.ToString());
            }
            else
                html = html.Replace("{Attachments}", string.Empty);

            //审批历史记录
            var approvalHistories = approvalRecordResult.Data as List<ApprovalRecordDto>;
            if (approvalHistories?.Any() == true)
            {
                var sb = new StringBuilder();
                var row = "<tr><td>{0}</td><td>{1}</td><td>{2}</td><td>{3}</td><td>{4}</td><td>{5}</td></tr>";
                foreach (var item in approvalHistories)
                    sb.AppendFormat(row, item.WorkFlowName, item.UserName, string.Empty, item.StatusName, item.Remark, item.SubmitOrApprovalTime);

                html = html.Replace("{ApprovalHistories}", sb.ToString());
            }
            else
                html = html.Replace("{ApprovalHistories}", string.Empty);

            #endregion

            return MessageResult.SuccessResult(html);
        }

        /// <summary>
        /// 获取PA打印页Html
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> GetPAPrintPageAsync(OmAuthorizationDto request)
        {
            var checkSignResult = _omAuthorizationService.ValidateAuthorization(request);
            if (!checkSignResult.Success)
                return checkSignResult;

            var getPaResult = await _purPAApplicationService.GetPAApplicationDetailsAsync(request.Id.Value);
            if (!getPaResult.Success)
                return getPaResult;

            var getFinVouchers = await _purPAApplicationService.GetPAApplicicationFinancialVoucher(request.Id.Value);
            if (!getFinVouchers.Success)
                return getFinVouchers;


            var approvalRecordResult = await _approveService.GetApprovalRecordAsync(request.Id.Value);
            if (!approvalRecordResult.Success)
                return approvalRecordResult;

            var paDetail = getPaResult.Data as PADetailsResponseDto;
            paDetail.PAFinancialVoucherInfo = getFinVouchers.Data as PAApplicicationFinancialVoucherDto;

            if (paDetail != null && paDetail.PAApplication != null)
            {
                DescriptionAttribute deliveryModeDescriptionAttribute = paDetail.PAApplication.DeliveryMode.GetType().GetField(paDetail.PAApplication.DeliveryMode.ToString()).GetCustomAttribute<DescriptionAttribute>();
                paDetail.PAApplication.DeliveryModeString = deliveryModeDescriptionAttribute == null ? paDetail.PAApplication.DeliveryMode.ToString() : deliveryModeDescriptionAttribute.Description;

                DescriptionAttribute paymentTypeDescriptionAttribute = paDetail.PAApplication.PaymentType.GetType().GetField(paDetail.PAApplication.PaymentType.ToString())?.GetCustomAttribute<DescriptionAttribute>();
                paDetail.PAApplication.PaymentTypeString = paymentTypeDescriptionAttribute == null ? paDetail.PAApplication.PaymentType.ToString() : paymentTypeDescriptionAttribute.Description;
            }

            var webRoot = LazyServiceProvider.LazyGetService<IWebHostEnvironment>().WebRootFileProvider;
            var html = await webRoot.GetFileInfo("Templates/PrintPage/pa-print.html").ReadAsStringAsync();

            #region 生成Html

            //申请信息和申请人信息
            var pa = paDetail.PAApplication;
            html = html.Replace("{ApplicationCode}", pa.ApplicationCode);
            html = html.Replace("{ApplyTime}", pa.ApplyTime.ToString("yyyy-MM-dd HH:mm:ss"));
            html = html.Replace("{ApplyUserName}", pa.ApplyUserName);
            html = html.Replace("{ApplyUserBuToDeptName}", pa.ApplyUserBuToDeptName);

            //收货信息
            if (paDetail.GRApplicationDetails?.Any() == true)
            {
                var sb = new StringBuilder();
                var row = "<tr><td>{0}</td><td>{1}</td><td>{2}</td><td>{3}</td><td>{4}</td><td>{5}</td><td>{6}</td><td>{7}</td></tr>";
                foreach (var item in paDetail.GRApplicationDetails)
                    sb.AppendFormat(row, item.ProductName, (int)item.OrderQuantity, EnumUtil.GetDescription(item.DeliveryMethod), item.UnitPrice.ToString("N2"), item.CurrentReceivingQuantity?.ToString("N2"), item.CurrentSignedQuantity?.ToString("N2"), item.SigningDate, item.IsArrive == true ? "是" : "否");

                html = html.Replace("{GoodsReceipts}", sb.ToString());
            }
            else
                html = html.Replace("{GoodsReceipts}", string.Empty);

            var invoiceTypes = await LazyServiceProvider.LazyGetService<IDataverseService>().GetDictionariesAsync(DictionaryType.InvoiceType);

            //支付信息
            html = html.Replace("{GRApplicationCode}", pa.GRApplicationCode);
            html = html.Replace("{CompanyName}", pa.CompanyName);
            html = html.Replace("{Currency}", pa.Currency);
            html = html.Replace("{ExchangeRate}", pa.ExchangeRate.ToString());
            html = html.Replace("{PayTotalAmount}", pa.PayTotalAmount.ToString("N2"));
            html = html.Replace("{CityName}", pa.CityName);
            html = html.Replace("{AdvancePayment}", pa.AdvancePayment == true ? "是" : "否");
            html = html.Replace("{IsLastPayment}", pa.IsLastPayment == true ? "是" : "否");
            html = html.Replace("{PaymentType}", pa.PaymentTypeString);
            html = html.Replace("{ReceivingHeader}", pa.ReceivingHeader);
            html = html.Replace("{IsBackupInvoice}", pa.IsBackupInvoice == true ? "是" : "否");
            html = html.Replace("{UrgentPayment}", pa.UrgentPayment == true ? "是" : "否");
            html = html.Replace("{UrgentType}", pa.UrgentType);
            html = html.Replace("{PaymentTerm}", pa.PaymentTerms);

            if (paDetail.PAApplicationDetails?.Any() == true)
                html = html.Replace("{InvoiceTypes}", paDetail.PAApplicationDetails.Select(a => invoiceTypes.FirstOrDefault(a1 => a1.Code == a.InvoiceType)?.Name).Distinct()?.JoinAsString(","));
            else
                html = html.Replace("{InvoiceTypes}", string.Empty);

            //发票信息
            if (paDetail.PAApplicationInvoices?.Any() == true)
            {
                var sb = new StringBuilder();
                decimal totalInvoiceAmount = 0, totalTaxAmount = 0, totalExcludingTaxAmount = 0;
                var row = "<tr><td>{0}</td><td>{1}</td><td>{2}</td><td>{3}</td><td>{4}</td><td>{5}</td><td>{6}</td></tr>";

                foreach (var item in paDetail.PAApplicationInvoices)
                {
                    decimal.TryParse(item.TaxRate, out decimal taxRate);
                    sb.AppendFormat(row, item.InvoiceCode, item.InvoiceDate, $"{taxRate * 100}%", invoiceTypes.FirstOrDefault(a => a.Code == item.InvoiceType)?.Name, item.InvoiceTotalAmount.ToString("N2"), item.TaxAmount.ToString("N2"), item.ExcludingTaxAmount.ToString("N2"));

                    totalInvoiceAmount += item.InvoiceTotalAmount;
                    totalTaxAmount += item.TaxAmount;
                    totalExcludingTaxAmount += item.ExcludingTaxAmount;
                }

                html = html.Replace("{Invoices}", sb.ToString());
                html = html.Replace("{TotalInvoiceAmount}", totalInvoiceAmount.ToString("N2"));
                html = html.Replace("{TotalTaxAmount}", totalTaxAmount.ToString("N2"));
                html = html.Replace("{TotalExcludingTaxAmount}", totalExcludingTaxAmount.ToString("N2"));
            }
            else
            {
                html = html.Replace("{Invoices}", string.Empty);
                html = html.Replace("{TotalInvoiceAmount}", string.Empty);
                html = html.Replace("{TotalTaxAmount}", string.Empty);
                html = html.Replace("{TotalExcludingTaxAmount}", string.Empty);
            }

            html = html.Replace("{Remarks}", pa.Remarks);

            //COA
            if (paDetail.PAFinancialVoucherInfo?.PAFinancialVoucherInfos?.Any() == true)
            {
                var sb = new StringBuilder();
                var row = "<tr><td>{0}</td><td>{1}</td><td>{2}</td></tr>";
                foreach (var item in paDetail.PAFinancialVoucherInfo.PAFinancialVoucherInfos)
                    sb.AppendFormat(row, paDetail.PAFinancialVoucherInfo.PAFinancialVoucherInfos.IndexOf(item) + 1, $"{item.CompanyCode}.{item.DivisionCode}.{item.CostCenter}.{item.NatureAccount}.{item.SubAccount}.{item.Location}", item.InvoiceLineAmount?.ToString("N2"));

                html = html.Replace("{COAs}", sb.ToString());
            }
            else
                html = html.Replace("{COAs}", string.Empty);

            //附件
            if (paDetail.Files?.Any() == true)
            {
                var sb = new StringBuilder();
                var row = "<tr><td>{0}</td><td>{1}</td></tr>";
                foreach (var item in paDetail.Files)
                    sb.AppendFormat(row, item.FileName, item.FileSize);

                html = html.Replace("{Attachments}", sb.ToString());
            }
            else
                html = html.Replace("{Attachments}", string.Empty);

            //审批历史记录
            var approvalHistories = approvalRecordResult.Data as List<ApprovalRecordDto>;
            if (approvalHistories?.Any() == true)
            {
                var sb = new StringBuilder();
                var row = "<tr><td>{0}</td><td>{1}</td><td>{2}</td><td>{3}</td><td>{4}</td><td>{5}</td></tr>";
                foreach (var item in approvalHistories)
                    sb.AppendFormat(row, item.WorkFlowName, item.UserName, string.Empty, item.StatusName, item.Remark, item.SubmitOrApprovalTime);

                html = html.Replace("{ApprovalHistories}", sb.ToString());
            }
            else
                html = html.Replace("{ApprovalHistories}", string.Empty);

            #endregion

            return MessageResult.SuccessResult(html);
        }
    }
}
