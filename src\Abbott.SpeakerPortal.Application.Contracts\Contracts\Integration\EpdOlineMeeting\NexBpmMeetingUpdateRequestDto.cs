﻿using Abbott.SpeakerPortal.Domain.Shared.Models;
using DocumentFormat.OpenXml.Office.CustomUI;
using System;
using System.Collections.Generic;
using System.Text;
using System.Text.Json.Serialization;

namespace Abbott.SpeakerPortal.Contracts.Integration.EpdOlineMeeting
{
    public class NexBpmMeetingUpdateRequestDto
    {
        /// <summary>
        /// PR单号 e.g.P2008270001
        /// </summary>
        [JsonPropertyName("serialNumberPr")]
        public string SerialNumberPr { get; set; }

        /// <summary>
        /// 会议地点 e.g.湖北省妇幼保健院光谷院区中医科医生办公室
        /// </summary>
        [JsonPropertyName("meetingAddress")]
        public string MeetingAddress { get; set; }

        /// <summary>
        /// 会议日期 e.g.2022-11-29
        /// </summary>
        [JsonPropertyName("meetingDate")]
        public string MeetingDate { get; set; }

        /// <summary>
        /// 会议形式 e.g.线上加线下
        /// </summary>
        [JsonPropertyName("meetingType")]
        public string MeetingType { get; set; }

        /// <summary>
        /// 讲者列表
        /// </summary>
        [JsonPropertyName("speakerDetails")]
        public List<NexBpmMeetingSpeakerItemDto> SpeakerDetails { get; set; }

        public static MessageResult Validate(NexBpmMeetingUpdateRequestDto request)
        {
            StringBuilder sb = new StringBuilder();
            if (request == null)
            {
                return MessageResult.FailureResult("Argument null!");
            }

            if (string.IsNullOrWhiteSpace(request.SerialNumberPr))
            {
                sb.AppendLine("Empty pr number!");
            }

            if (string.IsNullOrWhiteSpace(request.MeetingAddress))
            {
                sb.AppendLine("Empty meeting address!");
            }

            if (string.IsNullOrWhiteSpace(request.MeetingDate))
            {
                sb.AppendLine("Empty meeting date!");
            }

            if (!DateTime.TryParse(request.MeetingDate, out DateTime date))
            {
                sb.AppendLine("Invalid meeting date:" + request.MeetingDate + "!");
            }

            if (string.IsNullOrWhiteSpace(request.MeetingType))
            {
                sb.AppendLine("Empty meeting type!");
            }
            else if (request.MeetingType != "线上" && request.MeetingType != "线下" && request.MeetingType != "线上加线下")
            {
                sb.AppendLine("Invalid meeting type, only support:线上，线下，线上加线下!");
            }

            if (request.SpeakerDetails != null)
            {
                for (int i = 0; i < request.SpeakerDetails.Count; i++)
                {
                    if (string.IsNullOrEmpty(request.SpeakerDetails[i].No))
                    {
                        sb.AppendLine("Empty row number, item index:" + i + 1);
                    }
                    else if (!int.TryParse(request.SpeakerDetails[i].No, out int no))
                    {
                        sb.AppendLine("Invalid row number:" + request.SpeakerDetails[i].No + ", item index:" + i + 1);
                    }

                    if (string.IsNullOrWhiteSpace(request.SpeakerDetails[i].VendorCode))
                    {
                        sb.AppendLine("Empty vendor code, item index:" + i + 1);
                    }

                    if (string.IsNullOrWhiteSpace(request.SpeakerDetails[i].Executive))
                    {
                        sb.AppendLine("Empty executive, item index:" + i + 1);
                    }

                    if (string.IsNullOrWhiteSpace(request.SpeakerDetails[i].ExecutiveMail))
                    {
                        sb.AppendLine("Empty executiveMail, item index:" + i + 1);
                    }
                }
            }

            if (sb.Length > 0)
            {
                return MessageResult.FailureResult(sb.ToString());
            }

            return MessageResult.SuccessResult();
        }
    }
}
