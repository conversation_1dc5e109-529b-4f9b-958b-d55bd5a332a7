CREATE PROCEDURE dbo.sp_PurPAApplications_ns
AS 
BEGIN
	
	select 
ppt.[Id]
,ppt.[ApplicationCode]
,case 
	when ppt.Status =N'填写申请表' then '1'
	when ppt.Status =N'审批中' then '2'
	when ppt.Status =N'待打款' then '3'
	when ppt.Status =N'打款中' then '4'
	when ppt.Status =N'已打款' then '5'
	when ppt.Status =N'打款失败' then '6'
	when ppt.Status =N'重新发起' then '7'
	when ppt.Status =N'作废' then '8'
	when ppt.Status =N'单据接收' then '9'
	when ppt.Status =N'财务初审' then '10'
	when ppt.Status =N'财务复审' then '11'
	when ppt.Status =N'打款作废' then '12'
end
[Status]
,ss.spk_NexBPMCode as [ApplyUserId]
,ppt.[ApplyUserName]
,ppt.[ApplyTime]
,sod.spk_NexBPMCode as [ApplyUserBu]
,sod.spk_Name  as [ApplyUserBuName]
,ppt.[ApplyUserBuToDeptName]
,ppt.[EsignPdf]
,ppt.[DeliveryMode]
,ppt.[MeetingModifyRemark]
,ppt.[AmountModifyRemark]
,pgt.Id as [GRId]
,ppt.[GRApplicationCode]
,ppt2.Id as [POId]
,ppt.[POApplicationCode]
,ppt.[CompanyCode]
,ppt.[ExchangeRate]
,ppt.[UrgentPayment]
,ppt.[UrgentType]
,ppt.[Region]
,ppt.[City]
,ppt.[AdvancePayment]
,ppt.[IsLastPayment]
,ppt.[PaymentType]
,ppt.[ReceivingHeader]
,ppt.[SupplierWarehousingTime]
,ppt.[IsBackupInvoice]
,ppt.[Remarks]
,ppt.[Attachments]
,ppt.[Invoice]
,ppt.[SponsorshipRewardPoints]
,ppt.[AkritivCaseID]
,ppt.[PaymentTerms]
,ppt.[InvoiceDescription]
,ppt.[ReceivedDocumentsDate]
,ppt.[ExtraProperties]
,ppt.[ConcurrencyStamp]
,ppt.[CreationTime]
,ss2.spk_NexBPMCode as [CreatorId]
,ppt.[LastModificationTime]
,'00000000-0000-0000-0000-000000000000' as [LastModifierId]
,ppt.[IsDeleted]
,'00000000-0000-0000-0000-000000000000' as [DeleterId]
,ppt.[DeletionTime]
,obp.ID as [VendorId]
,ppt.[VendorName]
,UPPER(co.spk_NexBPMCode)as [CompanyId]
,ppt.[CompanyName]
,ppt.[PayTotalAmount]
,ppt.[AcceptedTime]
,ppt.[AccepterId]
,ppt.[AccepterName]
,cast(ppt.[TaskType] as nvarchar(10)) as TaskType
,ppt.[EstimatedPaymentDate]
,ppt.[SeperateRemark]
,ppt.[TaxStampLegal]
,ppt.[ExpenseType]
,ppt.[InterceptType]
,ppt3.id as [PRId]
,ppt.[ApprovedDate]
,ppt.[VendorCode]
,ss1.spk_NexBPMCode as[ApprovedUserId]
,UPPER( ci.spk_NexBPMCode) as [CityId]
,ppt.[Currency]
,ppt.[CurrencySymbol]
,ppt.[ExpectedFloatRate]
,ppt.[PlanRate]
,ppt.[EmailAttachment]
,ppt.[PayMethod]
,ppt.[ReasonModification]
,ppt.[VendorRating]
,ppt.[VendorRatingRemark]
into #PurPAApplications
from PLATFORM_ABBOTT_STG.dbo.PurPAApplications_tmp ppt 
left join spk_staffmasterdata ss 
on ppt.ApplyUserId =ss.bpm_id 
left join spk_organizationalmasterData sod 
on ppt.ApplyUserBu =sod.spk_BPMCode 
left join PurGRApplications_tmp pgt 
on ppt.GRId =pgt.ProcInstId     --618626
left join (select *,ROW_NUMBER () over(PARTITION by ApplicationCode order by ProcInstId) rn from PLATFORM_ABBOTT_STG.dbo.PurPOApplications_tmp ) ppt2
on ppt.POId =ppt2.ApplicationCode  COLLATE SQL_Latin1_General_CP1_CI_AS and ppt2.rn = 1   --618627
left join spk_staffmasterdata ss2 
on ppt.CreatorId = ss2.bpm_id 
left join ODS_T_RESOURCE otr 
on ppt.CompanyId =otr.Res_Data and Res_Parent_Code ='61a3f911b5ae4bc98cddd441833d861e'
left join ODS_BPCS_PMFVM obp 
on ppt.VendorId COLLATE SQL_Latin1_General_CP1_CI_AS=concat(obp.VNDERX,obp.VEXTNM) and otr.Res_Data1 COLLATE SQL_Latin1_General_CP1_CI_AS=obp.VMCMPY 
left join spk_companymasterdata co
on otr.Res_Code =co.spk_BPMCode
left join (select *,ROW_NUMBER () over(PARTITION by ApplicationCode order by ProcInstId) rn from PLATFORM_ABBOTT_STG.dbo.PurPRApplications_tmp ) ppt3
on ppt.PRId =ppt3.ApplicationCode and ppt3.rn = 1 --and  ppt.ProcInstId =ppt3.ProcInstId    --618630
left join spk_staffmasterdata ss1 
on ppt.ApprovedUserId =ss1.bpm_id 
left join spk_citymasterdata ci
on ppt.CityId COLLATE SQL_Latin1_General_CP1_CI_AS=concat(ci.spk_citynumber,ci.spk_Name);

update a set a.TaskType=b.TaskType  from #PurPAApplications a
join (
select ppt.id,case when ppt.status in (N'待打款',N'打款中',N'已打款',N'打款失败',N'打款作废') then 2
		when c.id is not null  then 3
		else 1 end  as TaskType
from PLATFORM_ABBOTT_STG.dbo.PurPAApplications_tmp ppt
left join (select DISTINCT  ppdt.Id  from PurPAApplications_tmp ppdt 
join PurPAApplicationDetails on2 
on ppdt.Id  =on2.PurPAApplicationId 
join OECIntercepts_ns on1
on on2.PRDetailId =on1.PRDetailId ) c
on cast(ppt.id as nvarchar(50))=c.id
)b
on a.id=b.id


update a set a.VendorCode=b.VendorCode ,a.PayMethod=b.PayMethod  from #PurPAApplications a
join (
select 
id,
SupplierCode as VendorCode,
case when GI.PRItemType ='AR' then 1  
when GI.PRItemType='AP' then 2 end as PayMethod
from PLATFORM_ABBOTT_STG.dbo.PurPAApplications_tmp ppt
join PLATFORM_ABBOTT_Stg.dbo.ods_AUTO_BIZ_T_GoodsReceiveApplication_Info GI
on ppt.PayMethod=GI.ProcInstId
)b
on a.id=b.id




IF OBJECT_ID(N'PLATFORM_ABBOTT_STG.dbo.PurPAApplications ', N'U') IS NOT NULL
	BEGIN
		drop table PLATFORM_ABBOTT_STG.dbo.PurPAApplications
		select *
        into PLATFORM_ABBOTT_STG.dbo.PurPAApplications from #PurPAApplications
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into PLATFORM_ABBOTT_STG.dbo.PurPAApplications from #PurPAApplications
    -- select * from #vendor_tbl
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END






END;
