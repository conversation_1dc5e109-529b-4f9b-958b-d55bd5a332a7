﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

using Abbott.SpeakerPortal.OEC.PayStandard;
using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.OECPayStandardConfigDetails;
using Abbott.SpeakerPortal.Entities.OECPayStandardConfigs;
using Abbott.SpeakerPortal.Entities.OECPayStandards;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Utils;

using Microsoft.Extensions.DependencyInjection;

using Newtonsoft.Json;

using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Data;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Guids;
using Volo.Abp.Identity;
using Abbott.SpeakerPortal.Contracts.OEC.PayStandard;
using Abbott.SpeakerPortal.Entities.OECPayStandardOperHistorys;
using Abbott.SpeakerPortal.Entities.Message.OECPayStandardOperHistorys;
using Abbott.SpeakerPortal.Contracts.Common;

namespace Abbott.SpeakerPortal.OEC
{
    /// <summary>
    /// 计酬标准配置
    /// </summary>
    /// <seealso cref="Abbott.SpeakerPortal.SpeakerPortalAppService" />
    /// <seealso cref="Abbott.SpeakerPortal.OEC.IPayStandardAppService" />
    public class PayStandardAppService : SpeakerPortalAppService, IPayStandardAppService
    {
        IServiceProvider _serviceProvider;

        public PayStandardAppService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        /// <summary>
        /// Gets the pay standard list asynchronous.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<PagedResultDto<PayStandardListResponseDto>> GetPayStandardListAsync(PayStandardListRequestDto request)
        {
            var queryPayStandard = await _serviceProvider.GetService<IOECPayStandardRepository>().GetQueryableAsync();
            var queryUser = await _serviceProvider.GetService<IRepository<IdentityUser>>().GetQueryableAsync();

            if (!request.EffectStart.HasValue)
                request.EffectStart = DateTime.MinValue;
            if (!request.EffectEnd.HasValue)
                request.EffectEnd = DateTime.MaxValue;

            var now = DateTimeOffset.Now.Date;
            var query = queryPayStandard.WhereIf(!string.IsNullOrEmpty(request.SerialNo), a => a.SerialNo.Contains(request.SerialNo))
                //时间上有交集的数据都符合条件
                .Where
                (
                    a => a.EffectStart >= request.EffectStart && a.EffectStart <= request.EffectEnd
                    || (a.EffectEnd ?? DateTime.MaxValue) >= request.EffectStart && (a.EffectEnd ?? DateTime.MaxValue) <= request.EffectEnd
                    || request.EffectStart >= a.EffectStart && request.EffectStart <= (a.EffectEnd ?? DateTime.MaxValue)
                    || request.EffectEnd >= a.EffectStart && request.EffectEnd <= (a.EffectEnd ?? DateTime.MaxValue)
                )
                //生效中
                .WhereIf(request.PreventStatus == EffectStatus.Effective, a => now >= a.EffectStart && (!a.EffectEnd.HasValue || a.EffectEnd >= now))
                //待生效
                .WhereIf(request.PreventStatus == EffectStatus.ToBeEffective, a => a.EffectStart > now)
                //已过期
                .WhereIf(request.PreventStatus == EffectStatus.Expired, a => a.EffectEnd < now)
                .Select(a => new
                {
                    a.Id,
                    a.SerialNo,
                    a.EffectStart,
                    a.EffectEnd,
                    ModifiedBy = a.LastModifierId.HasValue ? a.LastModifierId.Value : a.CreatorId,
                    ModifiedAt = a.LastModificationTime.HasValue ? a.LastModificationTime.Value : a.CreationTime,
                })
                .GroupJoin(queryUser, a => a.ModifiedBy, a => a.Id, (a, b) => new { PayStandard = a, User = b })
                .SelectMany(a => a.User.DefaultIfEmpty(), (a, b) => new
                {
                    a.PayStandard.Id,
                    a.PayStandard.SerialNo,
                    a.PayStandard.EffectStart,
                    a.PayStandard.EffectEnd,
                    ModifiedBy = b.Name,
                    a.PayStandard.ModifiedAt,
                    PreventStatus = a.PayStandard.EffectEnd < now ? EffectStatus.Expired : a.PayStandard.EffectStart > now ? EffectStatus.ToBeEffective : EffectStatus.Effective
                })
                .OrderBy(a => a.PreventStatus == EffectStatus.Effective ? 0 : 1)
                .ThenByDescending(o => o.EffectStart);

            var preventStatus = EnumUtil.GetEnumIdValues<EffectStatus>().ToDictionary(a => (EffectStatus)a.Key, a => a.Value);

            var count = query.Count();
            var datas = query.Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToArray().Select(a => new PayStandardListResponseDto
            {
                Id = a.Id,
                SerialNo = a.SerialNo,
                EffectStart = a.EffectStart.ToString("yyyy-MM-dd"),
                EffectEnd = a.EffectEnd.HasValue ? a.EffectEnd.Value.ToString("yyyy-MM-dd") : string.Empty,
                ModifiedBy = a.ModifiedBy,
                ModifiedAt = a.ModifiedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                PreventStatus = a.PreventStatus,
                PreventStatusName = preventStatus[a.PreventStatus]
            }).ToArray();

            var result = new PagedResultDto<PayStandardListResponseDto>(count, datas);
            return result;
        }

        /// <summary>
        /// Saves the pay standard.
        /// </summary>
        /// <param name="savePayStandardDto">The save pay standard dto.</param>
        /// <returns></returns>
        public async Task<MessageResult> SavePayStandardAsync(SavePayStandardDto savePayStandardDto)
        {
            OECPayStandard payStandard, oldPayStandard = null;
            var payStandardRepository = _serviceProvider.GetService<IOECPayStandardRepository>();
            var payStandardConfigRepository = _serviceProvider.GetService<IOECPayStandardConfigRepository>();
            var payStandardConfigDetailRepository = _serviceProvider.GetService<IOECPayStandardConfigDetailRepository>();

            //validate dto
            var messageResult = Validate(savePayStandardDto);
            if (!messageResult.Success)
                return messageResult;

            if (await payStandardRepository.AnyAsync(a => a.EffectStart.Date == savePayStandardDto.EffectStart.Date && a.Id != savePayStandardDto.Id))
                return MessageResult.FailureResult("已存在相同日期的配置");

            //modify
            if (savePayStandardDto.Id.HasValue)
            {
                oldPayStandard = await payStandardRepository.FindAsync(savePayStandardDto.Id.Value);
                if (oldPayStandard == null)
                    return MessageResult.FailureResult($"未找到Id为{savePayStandardDto.Id}的计酬标准配置信息");

                //保存历史记录
                await SaveHistory(oldPayStandard);

                payStandard = ObjectMapper.Map(savePayStandardDto, oldPayStandard);
                await payStandardRepository.UpdateAsync(payStandard);
                await payStandardConfigRepository.DeleteAsync(a => a.PayStandardId == payStandard.Id);
                await payStandardConfigDetailRepository.DeleteAsync(a => a.PayStandardId == payStandard.Id);
            }
            else
                payStandard = ObjectMapper.Map<SavePayStandardDto, OECPayStandard>(savePayStandardDto);

            //设置前一条的失效日期
            var nextStartDate = await SetPrevRecordEffectEnd(payStandardRepository, payStandard.EffectStart);
            payStandard.EffectEnd = nextStartDate;

            // add new
            if (!savePayStandardDto.Id.HasValue)
                await InsertAndGenerateSerialNoAsync(payStandardRepository, payStandard);

            //configs
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var compensationList = await dataverseService.GetCompensationAsync();
            foreach (var config in savePayStandardDto.Configs)
            {
                var payStandardConfig = ObjectMapper.Map<PayStandardConfigDto, OECPayStandardConfig>(config);
                payStandardConfig.PayStandardId = payStandard.Id;
                payStandardConfig.CountUnitName = compensationList.FirstOrDefault(a => a.Unit == config.CountUnit)?.UnitName;
                payStandardConfig = await payStandardConfigRepository.InsertAsync(payStandardConfig);

                //config details
                foreach (var configDetail in config.ConfigDetails)
                {
                    var payStandardConfigDetail = ObjectMapper.Map<PayStandardConfigDetailDto, OECPayStandardConfigDetail>(configDetail);
                    payStandardConfigDetail.PayStandardId = payStandard.Id;
                    payStandardConfigDetail.PayStandardConfigId = payStandardConfig.Id;
                    payStandardConfigDetail.VLExpenseLimit = JsonConvert.SerializeObject(configDetail.ConfigDetailItems);
                    payStandardConfigDetail = await payStandardConfigDetailRepository.InsertAsync(payStandardConfigDetail);
                }
            }

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// Validates the specified pay standard repository.
        /// </summary>
        /// <param name="payStandardRepository">The pay standard repository.</param>
        /// <param name="savePayStandardDto">The save pay standard dto.</param>
        /// <returns></returns>
        MessageResult Validate(SavePayStandardDto savePayStandardDto)
        {
            if (savePayStandardDto.EffectStart.Date < DateTimeOffset.Now.Date)
                return MessageResult.FailureResult("生效日期不能早于当前日期");

            if (savePayStandardDto.Configs.Count() == 0)
                return MessageResult.FailureResult("配置信息不能为空");

            foreach (var config in savePayStandardDto.Configs)
            {
                var count = config.ConfigDetails.Count();
                if (count == 0)
                    return MessageResult.FailureResult("配置信息明细不能为空");

                config.ConfigDetails = config.ConfigDetails.OrderBy(a => a.MinServiceDuration).ThenBy(a => a.MaxServiceDuration);

                //第一条
                var firstConfigDetail = config.ConfigDetails.FirstOrDefault();
                if (!firstConfigDetail.MaxServiceDuration.HasValue)
                    return MessageResult.FailureResult("数值区间必须有值");
                if (!firstConfigDetail.MaxRelation.HasValue)
                    return MessageResult.FailureResult("数值比较符必须有值");

                //最后一条
                var lastConfigDetail = config.ConfigDetails.LastOrDefault();
                if (!lastConfigDetail.MinServiceDuration.HasValue)
                    return MessageResult.FailureResult("数值区间必须有值");
                if (!lastConfigDetail.MinRelation.HasValue)
                    return MessageResult.FailureResult("数值比较符必须有值");

                for (int i = 0; i < count; i++)
                {
                    var configDetail = config.ConfigDetails.ElementAt(i);
                    //中间配置
                    if (i > 0 && i < count - 1)
                    {
                        if (!configDetail.MinServiceDuration.HasValue || !configDetail.MaxServiceDuration.HasValue)
                            return MessageResult.FailureResult("数值区间必须有值");
                        if (!configDetail.MinRelation.HasValue || !configDetail.MaxRelation.HasValue)
                            return MessageResult.FailureResult("数值比较符必须有值");
                    }

                    if (configDetail.MinRelation.HasValue && configDetail.MinRelation != PayStandardConfigOperator.Lt && configDetail.MinRelation != PayStandardConfigOperator.Lte)
                        return MessageResult.FailureResult("数值比较符错误");
                    if (configDetail.MaxRelation.HasValue && configDetail.MaxRelation != PayStandardConfigOperator.Lt && configDetail.MaxRelation != PayStandardConfigOperator.Lte)
                        return MessageResult.FailureResult("数值比较符错误");

                    if (configDetail.MinServiceDuration > configDetail.MaxServiceDuration)
                        return MessageResult.FailureResult("数值区间设置有错误");

                    //转换比较符对应的数值
                    if (count > 1 && i < count - 1)
                    {
                        var nextConfigDetail = config.ConfigDetails.ElementAt(i + 1);
                        var value1 = configDetail.MaxRelation == PayStandardConfigOperator.Lt ? configDetail.MaxServiceDuration - 1 : configDetail.MaxServiceDuration;
                        var value2 = nextConfigDetail.MinRelation == PayStandardConfigOperator.Lt ? nextConfigDetail.MinServiceDuration + 1 : nextConfigDetail.MinServiceDuration;
                        var diff = (value2 - value1) ?? 0;
                        //相邻两个整数差超过1，说明未覆盖所有数值
                        if (Math.Abs(diff) != 1)
                            return MessageResult.FailureResult("数值区间未覆盖到每一个数值，请检查后再提交");
                    }
                }
            }

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// Sets the previous pay standard effect end.
        /// </summary>
        /// <param name="payStandardRepository">The pay standard repository.</param>
        /// <param name="currentEffectStart">The current effect start.</param>
        async Task<DateTime?> SetPrevRecordEffectEnd(IOECPayStandardRepository payStandardRepository, DateTime currentEffectStart)
        {
            var query = await payStandardRepository.GetQueryableAsync();
            var datas = query.OrderByDescending(a => a.EffectStart).Where(a => a.EffectStart < currentEffectStart).Take(1)
                .Union(query.OrderByDescending(a => a.EffectStart).Where(a => a.EffectStart > currentEffectStart).Take(1))
                .ToArray();

            var prevPayStandard = datas.FirstOrDefault(a => a.EffectStart < currentEffectStart);
            //找到当前保存配置的前一条，为其设置失效日期
            if (prevPayStandard != null)
            {
                prevPayStandard.EffectEnd = currentEffectStart.AddDays(-1);
                await payStandardRepository.UpdateAsync(prevPayStandard);
            }
            //后一条
            var nextPayStandard = datas.FirstOrDefault(a => a.EffectStart > currentEffectStart);
            return nextPayStandard?.EffectStart;
        }

        /// <summary>
        /// Save operate history
        /// </summary>
        /// <param name="payStandardId"></param>
        async Task SaveHistory(OECPayStandard payStandard)
        {
            var detail = await GetPayStandardDetailAsync(payStandard.Id);
            var history = new OECPayStandardOperHistory
            {
                PayStandardId = payStandard.Id,
                OperContent = JsonConvert.SerializeObject(detail),
                Remark = payStandard.Remark
            };
            await LazyServiceProvider.LazyGetService<IOECPayStandardOperHistoryRepository>().InsertAsync(history);
        }

        /// <summary>
        /// 获取计酬配置数据
        /// </summary>
        /// <returns></returns>
        public async Task<IEnumerable<RemunerationConfigDto>> GetRemunerationConfigAsync()
        {
            var remunerationConfigList = new List<RemunerationConfigDto>();

            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var compensationList = await dataverseService.GetCompensationAsync();
            var consumeList = await dataverseService.GetConsumeCategoryAsync();
            var costnatureList = await dataverseService.GetCostNatureAsync();
            var dictionaryList = await dataverseService.GetDictionariesAsync(DictionaryType.IdentityType);

            foreach (var item in compensationList)
            {
                var consume = consumeList.FirstOrDefault(c => c.Id == item.Consume);
                var costnature = costnatureList.FirstOrDefault(c => c.Id == item.Costnature);
                var identity = dictionaryList.FirstOrDefault(d => d.Id == item.Identity);
                remunerationConfigList.Add(new RemunerationConfigDto()
                {
                    ConsumeId = consume?.Id,
                    ConsumeName = consume?.Name,
                    CostnatureId = costnature?.Id,
                    CostnatureName = costnature?.Name,
                    IdentityId = identity?.Id,
                    IdentityName = identity?.Name,
                    CountUnit = item.Unit,
                    CountUnitName = item.UnitName
                });
            }

            return remunerationConfigList;
        }

        /// <summary>
        /// Gets the pay standard detail.
        /// </summary>
        /// <param name="payStandardId">The pay standard identifier.</param>
        /// <returns></returns>
        public async Task<PayStandardDetailResponseDto> GetPayStandardDetailAsync(Guid payStandardId)
        {
            var queryUser = await _serviceProvider.GetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var queryPayStandard = await LazyServiceProvider.GetService<IOECPayStandardRepository>().GetQueryableAsync();
            var queryPayStandardConfig = await LazyServiceProvider.GetService<IOECPayStandardConfigRepository>().GetQueryableAsync();
            var queryPayStandardConfigDetail = await LazyServiceProvider.GetService<IOECPayStandardConfigDetailRepository>().GetQueryableAsync();

            var datas = queryPayStandard.Where(a => a.Id == payStandardId).Select(a => new
            {
                a.Id,
                a.SerialNo,
                a.EffectStart,
                a.EffectEnd,
                a.Remark,
                ModifiedBy = a.LastModifierId.HasValue ? a.LastModifierId.Value : a.CreatorId,
                ModifiedAt = a.LastModificationTime.HasValue ? a.LastModificationTime.Value : a.CreationTime,
            })
            .GroupJoin(queryUser, a => a.ModifiedBy, a => a.Id, (a, b) => new { PayStandard = a, User = b })
            .SelectMany(a => a.User.DefaultIfEmpty(), (a, b) => new { a.PayStandard, User = b })
            .Join(queryPayStandardConfig, a => a.PayStandard.Id, a => a.PayStandardId, (a, b) => new { a.PayStandard, a.User, PayStandardConfig = b })
            .Join(queryPayStandardConfigDetail, a => a.PayStandardConfig.Id, a => a.PayStandardConfigId, (a, b) => new { a.PayStandard, a.User, a.PayStandardConfig, PayStandardConfigDetail = b })
            .Select(a => new
            {
                //basic info
                a.PayStandard.Id,
                a.PayStandard.SerialNo,
                ModifiedBy = a.User.Name,
                a.PayStandard.ModifiedAt,
                a.PayStandard.EffectStart,
                a.PayStandard.EffectEnd,
                a.PayStandard.Remark,

                //config info
                ConfigId = a.PayStandardConfig.Id,
                a.PayStandardConfig.ActiveNature,
                a.PayStandardConfig.ExpenseNature,
                a.PayStandardConfig.BuId,
                a.PayStandardConfig.IdentityType,
                a.PayStandardConfig.CountUnit,
                a.PayStandardConfig.CountUnitName,

                //config detail
                ConfigDetailId = a.PayStandardConfigDetail.Id,
                ConfigDetailConfigId = a.PayStandardConfigDetail.PayStandardConfigId,
                a.PayStandardConfigDetail.Condition,
                a.PayStandardConfigDetail.MinServiceDuration,
                a.PayStandardConfigDetail.MinRelation,
                a.PayStandardConfigDetail.MaxServiceDuration,
                a.PayStandardConfigDetail.MaxRelation,
                a.PayStandardConfigDetail.VLExpenseLimit
            })
            .ToArray();

            if (datas.Length == 0)
                return default;

            //pay standard
            var now = DateTimeOffset.Now.Date;
            var payStandard = datas.Select(a => new PayStandardDetailResponseDto
            {
                Id = a.Id,
                SerialNo = a.SerialNo,
                ModifiedBy = a.ModifiedBy,
                ModifiedAt = a.ModifiedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                EffectStart = a.EffectStart.ToString("yyyy-MM-dd"),
                EffectEnd = a.EffectEnd?.ToString("yyyy-MM-dd"),
                Remark = a.Remark,
                EffectStatus = a.EffectEnd < now ? EffectStatus.Expired : a.EffectStart > now ? EffectStatus.ToBeEffective : EffectStatus.Effective
            }).First();

            var configs = datas.GroupBy(a => new
            {
                a.ConfigId,
                a.ActiveNature,
                a.ExpenseNature,
                a.BuId,
                a.IdentityType,
                a.CountUnit,
                a.CountUnitName
            });

            if (configs.Any())
            {
                //pay standard configs
                payStandard.Configs = new List<PayStandardDetailConfigResponseDto>();
                foreach (var c in configs)
                {
                    var config = new PayStandardDetailConfigResponseDto
                    {
                        Id = c.Key.ConfigId,
                        ActiveNature = c.Key.ActiveNature,
                        ExpenseNature = c.Key.ExpenseNature,
                        BuId = c.Key.BuId,
                        IdentityType = c.Key.IdentityType,
                        CountUnit = c.Key.CountUnit,
                        CountUnitName = c.Key.CountUnitName
                    };
                    payStandard.Configs.Add(config);

                    //pay standard config details
                    config.ConfigDetails = new List<PayStandardDetailConfigDetailResponseDto>();
                    foreach (var d in c)
                    {
                        var configDetail = new PayStandardDetailConfigDetailResponseDto
                        {
                            Id = d.ConfigDetailId,
                            Condition = d.Condition,
                            MinServiceDuration = d.MinServiceDuration,
                            MinRelation = d.MinRelation,
                            MaxServiceDuration = d.MaxServiceDuration,
                            MaxRelation = d.MaxRelation
                        };

                        var vle = JsonConvert.DeserializeObject<IEnumerable<PayStandardConfigDetailItemDto>>(d.VLExpenseLimit);
                        configDetail.ConfigDetailItems = vle;

                        config.ConfigDetails.Add(configDetail);
                    }
                }
            }

            return payStandard;
        }

        /// <summary>
        /// Gets the copy pay standard detail asynchronous.
        /// </summary>
        /// <param name="payStandardId">The pay standard identifier.</param>
        /// <returns></returns>
        public async Task<PayStandardDetailResponseDto> GetCopyPayStandardDetailAsync(Guid payStandardId)
        {
            var payStandard = await GetPayStandardDetailAsync(payStandardId);
            payStandard.Id = null;
            foreach (var config in payStandard.Configs)
            {
                config.Id = null;
                foreach (var configDetail in config.ConfigDetails)
                {
                    configDetail.Id = null;
                }
            }

            return payStandard;
        }

        /// <summary>
        /// Delete pay standard asynchronous.
        /// </summary>
        /// <param name="payStandardId">The pay standard identifier.</param>
        /// <returns></returns>
        public Task DeletePayStandardAsync(Guid payStandardId)
        {
            return LazyServiceProvider.GetService<IOECPayStandardRepository>().DeleteAsync(a => a.Id == payStandardId);
        }

        /// <summary>
        /// Gets the pay standard operated hsitories asynchronous.
        /// </summary>
        /// <param name="payStandardOperHistoryListRequestDto">The request.</param>
        /// <returns></returns>
        public async Task<PagedResultDto<PayStandardOperHistoryListResponseDto>> GetPayStandardOperatedHistoryListAsync(PayStandardOperHistoryListRequestDto payStandardOperHistoryListRequestDto)
        {
            var queryUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var queryOperHistory = await LazyServiceProvider.LazyGetService<IOECPayStandardOperHistoryRepository>().GetQueryableAsync();

            var query = queryOperHistory.Where(a => a.PayStandardId == payStandardOperHistoryListRequestDto.PayStandardId)
                .GroupJoin(queryUser, a => a.CreatorId, a => a.Id, (a, b) => new { PayStandardOperHistory = a, User = b })
                .SelectMany(a => a.User.DefaultIfEmpty(), (a, b) => new
                {
                    a.PayStandardOperHistory.Id,
                    Operator = b.Name,
                    OperatedAt = a.PayStandardOperHistory.CreationTime,
                    a.PayStandardOperHistory.Remark,
                });

            var count = query.Count();
            var datas = query
                .OrderBy(a => a.OperatedAt)
                .Skip(payStandardOperHistoryListRequestDto.PageIndex * payStandardOperHistoryListRequestDto.PageSize)
                .Take(payStandardOperHistoryListRequestDto.PageSize)
                .Select(a => new PayStandardOperHistoryListResponseDto
                {
                    Id = a.Id,
                    Operator = a.Operator,
                    OperatedAt = a.OperatedAt.ToString("yyyy-MM-dd HH:mm:ss"),
                    Remark = a.Remark
                })
                .ToArray();

            var result = new PagedResultDto<PayStandardOperHistoryListResponseDto>(count, datas);
            return result;
        }

        /// <summary>
        /// Gets the pay standard operated history detail asynchronous.
        /// </summary>
        /// <param name="payStandardOperHistoryId">The pay standard operated history identifier.</param>
        /// <returns></returns>
        public async Task<PayStandardDetailResponseDto> GetPayStandardOperatedHistoryDetailAsync(Guid payStandardOperHistoryId)
        {
            if (payStandardOperHistoryId == Guid.Empty)
                return default;

            var operatedHistory = await LazyServiceProvider.LazyGetService<IOECPayStandardOperHistoryRepository>().FindAsync(payStandardOperHistoryId);
            if (operatedHistory == null || string.IsNullOrWhiteSpace(operatedHistory.OperContent))
                return default;

            var result = JsonConvert.DeserializeObject<PayStandardDetailResponseDto>(operatedHistory.OperContent);
            return result;
        }
    }
}
