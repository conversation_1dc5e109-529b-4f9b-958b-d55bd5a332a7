﻿using Abbott.SpeakerPortal.Contracts.Common.Attachment;
using Abbott.SpeakerPortal.Contracts.OEC.SpeakerLimit;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.OECPSAComPSALimits;
using Abbott.SpeakerPortal.Entities.OECPSASpeakerLimitExtras;
using Abbott.SpeakerPortal.Entities.OECPSASpeakerLimits;
using Abbott.SpeakerPortal.Entities.OECPSASpeakerLimitUseHistorys;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Entities.VendorPersonals;
using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.OEC.SpeakerLimit;
using Abbott.SpeakerPortal.Utils;

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection.Emit;
using System.Threading.Tasks;

using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;

namespace Abbott.SpeakerPortal.OEC
{
    /// <summary>
    /// SpeakerLimitService
    /// </summary>
    public class SpeakerLimitService : SpeakerPortalAppService, ISpeakerLimitService
    {
        /// <summary>
        /// The service provider
        /// </summary>
        private readonly IServiceProvider _serviceProvider;

        /// <summary>
        /// The logger
        /// </summary>
        private readonly ILogger<SpeakerLimitService> _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="SpeakerLimitService"/> class.
        /// </summary>
        /// <param name="serviceProvider">The service provider.</param>
        public SpeakerLimitService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _logger = serviceProvider.GetService<ILogger<SpeakerLimitService>>();
        }

        /// <summary>
        /// Gets the speaker limit list asynchronous.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<PagedResultDto<SpeakerLimitListResponseDto>> GetSpeakerLimitListAsync(SpeakerLimitListRequestDto request)
        {
            var queryVendor = await LazyServiceProvider.LazyGetService<IVendorReadonlyRepository>().GetQueryableAsync();
            var queryVendorPersonal = await LazyServiceProvider.LazyGetService<IVendorPersonalReadonlyRepository>().GetQueryableAsync();
            var queryPsaUseHistory = await LazyServiceProvider.LazyGetService<IOECPSASpeakerLimitUseHistoryReadonlyRepository>().GetQueryableAsync();
            var queryPsaExtra = await LazyServiceProvider.LazyGetService<IOECPSASpeakerLimitExtraReadonlyRepository>().GetQueryableAsync();
            var queryComPsa = await LazyServiceProvider.LazyGetService<IOECPSAComPSALimitReadonlyRepository>().GetQueryableAsync();

            var year = request.Year ?? DateTime.Now.Year;
            queryComPsa = queryComPsa.Where(a => year >= a.EffectStart.Year && year <= (a.EffectEnd.HasValue ? a.EffectEnd.Value.Year : DateTime.MaxValue.Year)).OrderByDescending(a => a.EffectStart).Take(1);

            queryPsaUseHistory = queryPsaUseHistory.Where(a => a.EffectiveDate.Year == year);
            var queryPsaExtraGroup = queryPsaExtra.Where(a => a.Year == year).GroupBy(a => a.VendorId).Select(a => new
            {
                VendorId = a.Key,
                //这里只计算新增的
                ExtraTimes = a.Sum(a1 => a1.ModifyType == ModifyTypes.ManuallyAdded ? a1.ExtralTimesRest : 0),
                ExtraAmount = a.Sum(a1 => a1.ModifyType == ModifyTypes.ManuallyAdded ? a1.ExtralAmountRest : 0)
            });

            var query = queryVendorPersonal.Join(queryVendor.Where(a => a.VendorType == VendorTypes.HCPPerson), a => a.VendorId, a => a.Id, (a, b) => new { VendorPersonal = a, Vendor = b })
                .GroupJoin(queryPsaExtraGroup, a => a.Vendor.Id, a => a.VendorId, (a, b) => new { a.Vendor, a.VendorPersonal, PsaExtras = b })
                .SelectMany(a => a.PsaExtras.DefaultIfEmpty(), (a, b) => new { a.Vendor, a.VendorPersonal, PsaExtra = b })
                .GroupJoin(queryComPsa, a => 1, a => 1, (a, b) => new { a.Vendor, a.VendorPersonal, a.PsaExtra, ComPsas = b })
                .SelectMany(a => a.ComPsas.DefaultIfEmpty(), (a, b) => new { a.Vendor, a.VendorPersonal, a.PsaExtra, ComPsa = b })
                .WhereIf(!string.IsNullOrEmpty(request.SpeakerCode), a => a.Vendor.VendorCode.Contains(request.SpeakerCode))
                .WhereIf(!string.IsNullOrEmpty(request.SpeakerName), a => a.VendorPersonal.SPName.Contains(request.SpeakerName))
                .Select(a => new
                {
                    a.Vendor.Id,
                    a.Vendor.VendorCode,
                    a.VendorPersonal.SPName,
                    a.Vendor.Status,
                    ComPsaTimesLimit = a.ComPsa == null ? 0 : a.ComPsa.TimesLimit,
                    ComPsaAmountLimit = a.ComPsa == null ? 0 : a.ComPsa.AmountLimit,
                    ExtraPsaTimesLimit = string.IsNullOrEmpty(a.PsaExtra.ExtraTimes.ToString()) ? 0 : a.PsaExtra.ExtraTimes,
                    ExtraPsaAmountLimit = string.IsNullOrEmpty(a.PsaExtra.ExtraAmount.ToString()) ? 0 : a.PsaExtra.ExtraAmount,
                    ComPsaUsedTimes = queryPsaUseHistory.Where(a1 => a1.VendorId == a.Vendor.Id && !a1.PsaExternalId.HasValue).Sum(a1 => a1.Times),
                    ComPsaUsedAmount = queryPsaUseHistory.Where(a1 => a1.VendorId == a.Vendor.Id && !a1.PsaExternalId.HasValue).Sum(a1 => a1.Amount),
                    ExtraPsaUsedTimes = queryPsaUseHistory.Where(a1 => a1.VendorId == a.Vendor.Id && a1.PsaExternalId.HasValue).Sum(a1 => a1.Times),
                    ExtraPsaUsedAmount = queryPsaUseHistory.Where(a1 => a1.VendorId == a.Vendor.Id && a1.PsaExternalId.HasValue).Sum(a1 => a1.Amount)
                });

            var count = query.Count();
            var datas = query.Skip(request.PageIndex * request.PageSize).Take(request.PageSize)
            .Select(a => new SpeakerLimitListResponseDto
            {
                Id = a.Id,
                SpeakerId = a.Id,
                SpeakerCode = a.VendorCode,
                SpeakerName = a.SPName,
                ComTimesRest = a.ComPsaTimesLimit - Math.Abs(a.ComPsaUsedTimes),
                ComAmountRest = a.ComPsaAmountLimit - Math.Abs(a.ComPsaUsedAmount),
                ExtralTimesRest = a.ExtraPsaUsedTimes,
                ExtralAmountRest = a.ExtraPsaUsedAmount,
                SpeakerStatus = a.Status
            })
            .ToArray();

            return new PagedResultDto<SpeakerLimitListResponseDto>(count, datas);
        }

        /// <summary>
        /// Gets the speaker information asynchronous.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns></returns>
        public async Task<MessageResult> GetSpeakerInfoAsync(Guid id)
        {
            try
            {
                var _vendorRepository = _serviceProvider.GetService<IVendorRepository>();
                var _vendorPersonalRepository = _serviceProvider.GetService<IRepository<VendorPersonal, Guid>>();
                var vendorPersonalQueryable = await _vendorPersonalRepository.GetQueryableAsync();

                var vender = await _vendorRepository.FirstOrDefaultAsync(p => p.Id == id);
                if (vender == null)
                {
                    _logger.LogError($"SpeakerLimitService's GetSpeakerInfoAsync has an error : can't found this speaker,the id is {id}");
                    return MessageResult.FailureResult($"Not found this speaker, the id is {id}");
                }
                var personal = await _vendorPersonalRepository.FirstOrDefaultAsync(p => p.VendorId == vender.Id);
                var response = new SpeakerInfoDetailDto()
                {
                    SpeakerName = personal.SPName,
                    HosDepartment = vender.HosDepartment
                };

                var _dataverseService = _serviceProvider.GetService<IDataverseService>();
                if (vender.PTId.HasValue)
                {
                    var jobs = await _dataverseService.GetAllJobTiles(stateCode: null);
                    response.SpeakerRole = jobs.FirstOrDefault(p => p.Id == vender.PTId)?.Name;
                }
                if (vender.HospitalId.HasValue)
                {
                    var hospitals = await _dataverseService.GetAllHospitals(stateCode: null);
                    response.BelongHospital = hospitals.FirstOrDefault(p => p.Id == vender.HospitalId)?.Name;
                }
                if (vender.StandardHosDepId.HasValue)
                {
                    var standardHosDeps = await _dataverseService.GetAllDepartments(stateCode: null);
                    response.StandardHosDep = standardHosDeps.FirstOrDefault(p => p.Id == vender.StandardHosDepId)?.Name;
                }

                return MessageResult.SuccessResult(response);
            }
            catch (Exception ex)
            {
                _logger.LogError($"SpeakerLimitService's GetSpeakerInfoAsync has an error : {ex.Message}");
                return MessageResult.FailureResult($"SpeakerLimitService's GetSpeakerInfoAsync has an error : {ex.Message}");
            }

        }

        /// <summary>
        /// Saves the adjustment limit.
        /// </summary>
        /// <param name="files">The files.</param>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> SaveAdjustmentLimit(SavePSALimitDto request)
        {
            //手动增加时，例外审批编号不能为空
            if (request.ModifyType == ModifyTypes.ManuallyAdded && string.IsNullOrEmpty(request.ExtralAuditApplicationNo))
                return MessageResult.FailureResult("例外审批编号不能为空");

            var speakerLimitExtraRepository = _serviceProvider.GetService<IOECPSASpeakerLimitExtraRepository>();
            //增加的时候需要验证编号是否重复
            var extra = await speakerLimitExtraRepository.FirstOrDefaultAsync(a => a.VendorId == request.VendorId && a.DivisionId == request.DivisionId && a.Year == request.Year && a.ExtralAuditApplicationNo == request.ExtralAuditApplicationNo && a.ModifyType == ModifyTypes.ManuallyAdded);
            if (request.ModifyType == ModifyTypes.ManuallyAdded && extra != null)
                return MessageResult.FailureResult("例外审批编号已存在，请勿重复操作");

            //人工扣减时，验证余量是否足够
            if (request.ModifyType == ModifyTypes.ManualDeduction)
            {
                var queryPsaUseHistory = await LazyServiceProvider.LazyGetService<IOECPSASpeakerLimitUseHistoryRepository>().GetQueryableAsync();
                //扣减时，如果扣减到具体的编号上，则编号必须存在
                if (!string.IsNullOrEmpty(request.ExtralAuditApplicationNo))
                {
                    if (extra == null)
                        return MessageResult.FailureResult("指定的例外审批编号不存在");

                    //判断余量是否充足
                    var extraUsedHistories = queryPsaUseHistory.Where(a => a.VendorId == request.VendorId && a.EffectiveDate.Year == request.Year && a.PsaExternalId == extra.Id).Select(a => new { a.Times, a.Amount }).ToArray();
                    var surplusTimes = extraUsedHistories.Sum(a => a.Times);
                    var surplusAmount = extraUsedHistories.Sum(a => a.Amount);
                    if (surplusTimes < Math.Abs(request.ExtralTimesRest) || surplusAmount < Math.Abs(request.ExtralAmountRest))
                        return MessageResult.FailureResult("该编号的剩余金额或次数不足");

                    var speakerLimitExtra = ObjectMapper.Map<SavePSALimitDto, OECPSASpeakerLimitExtra>(request);
                    speakerLimitExtra.ExtralTimesRest = request.ModifyType == ModifyTypes.ManuallyAdded ? Math.Abs(request.ExtralTimesRest) : -Math.Abs(request.ExtralTimesRest);
                    speakerLimitExtra.ExtralAmountRest = request.ModifyType == ModifyTypes.ManuallyAdded ? Math.Abs(request.ExtralAmountRest) : -Math.Abs(request.ExtralAmountRest);

                    if (request.Urls.Any())
                        speakerLimitExtra.Doc = string.Join(",", request.Urls.Select(p => p.AttachmentId));

                    await speakerLimitExtraRepository.InsertAsync(speakerLimitExtra);
                    if (request.ModifyType == ModifyTypes.ManuallyAdded)
                        extra = speakerLimitExtra;
                }
                else//扣减通用次数
                {
                    var queryComPsa = await LazyServiceProvider.LazyGetService<IOECPSAComPSALimitRepository>().GetQueryableAsync();
                    var comPsa = queryComPsa.Where(a => request.Year >= a.EffectStart.Year && request.Year <= (a.EffectEnd.HasValue ? a.EffectEnd.Value.Year : DateTime.MaxValue.Year)).OrderByDescending(a => a.EffectStart).FirstOrDefault();
                    if (comPsa == null)
                        return MessageResult.FailureResult("未匹配到该年份的通用PSA设置");

                    var extraUsedHistories = queryPsaUseHistory.Where(a => a.VendorId == request.VendorId && a.EffectiveDate.Year == request.Year && a.PsaExternalId == null).Select(a => new { a.Times, a.Amount }).ToArray();
                    //2049【合规管理】【PSA上限管理】（UAT）人工扣减没有校验余额是否充足，当前存在调整为负数的情况
                    var usedTimes = Math.Abs(extraUsedHistories.Sum(a => a.Times));
                    var usedAmount = Math.Abs(extraUsedHistories.Sum(a => a.Amount));

                    if (comPsa.TimesLimit - usedTimes < Math.Abs(request.ExtralTimesRest) || comPsa.AmountLimit - usedAmount < Math.Abs(request.ExtralAmountRest))
                        return MessageResult.FailureResult("通用剩余金额或次数不足");
                }
            }
            else
            {
                //人工增加，需要记录例外编号
                var speakerLimitExtra = ObjectMapper.Map<SavePSALimitDto, OECPSASpeakerLimitExtra>(request);
                speakerLimitExtra.ExtralTimesRest = request.ModifyType == ModifyTypes.ManuallyAdded ? Math.Abs(request.ExtralTimesRest) : -Math.Abs(request.ExtralTimesRest);
                speakerLimitExtra.ExtralAmountRest = request.ModifyType == ModifyTypes.ManuallyAdded ? Math.Abs(request.ExtralAmountRest) : -Math.Abs(request.ExtralAmountRest);

                if (request.Urls.Any())
                    speakerLimitExtra.Doc = string.Join(",", request.Urls.Select(p => p.AttachmentId));

                await speakerLimitExtraRepository.InsertAsync(speakerLimitExtra);
                if (request.ModifyType == ModifyTypes.ManuallyAdded)
                    extra = speakerLimitExtra;
            }

            var usedHistory = new AddSpeakerLimitUseHistoryRequestDto
            {
                VendorId = request.VendorId,
                PsaExternalId = extra?.Id,
                BuId = request.DivisionId,
                Times = request.ModifyType == ModifyTypes.ManuallyAdded ? Math.Abs(request.ExtralTimesRest) : -Math.Abs(request.ExtralTimesRest),
                Amount = request.ModifyType == ModifyTypes.ManuallyAdded ? Math.Abs(request.ExtralAmountRest) : -Math.Abs(request.ExtralAmountRest),
                OperateType = request.ModifyType,
                OperDetailType = request.ModifyType == ModifyTypes.ManuallyAdded ? OperDetailType.Added : OperDetailType.Deduction,
                EffectiveDate = new DateTime(request.Year, 1, 1)
            };
            await AddSpeakerLimitUseHistoryAsync([usedHistory]);

            return MessageResult.SuccessResult(true);
        }

        /// <summary>
        /// Gets the extral amount list asynchronous.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<PagedResultDto<ExtralAmountResponseDto>> GetExtralAmountListAsync(ExtralRequestDto request)
        {
            var divisions = await LazyServiceProvider.LazyGetService<IDataverseService>().GetDivisions();
            var querySpeakerLimitExtra = await LazyServiceProvider.LazyGetService<IOECPSASpeakerLimitExtraRepository>().GetQueryableAsync();
            var queryLimitUseHistory = await LazyServiceProvider.LazyGetService<IOECPSASpeakerLimitUseHistoryRepository>().GetQueryableAsync();

            var result = querySpeakerLimitExtra
            .Where(p => p.VendorId == request.VendorId && p.ModifyType == ModifyTypes.ManuallyAdded)
            .WhereIf(!request.Year.HasValue, a => a.Year == DateTime.Now.Year)
            .WhereIf(request.Year.HasValue, a => a.Year == request.Year)
            .Select(g => new
            {
                ExtralApproveNo = g.ExtralAuditApplicationNo,
                g.DivisionId,
                ApplicationAmount = g.ExtralAmountRest,
                UsedAmount = queryLimitUseHistory.Where(a => a.PsaExternalId == g.Id).Sum(a => a.Amount) - g.ExtralAmountRest
            })
            .Select(r => new ExtralAmountResponseDto
            {
                ExtralApproveNo = r.ExtralApproveNo,
                DivisionId = r.DivisionId,
                ApplicationAmount = r.ApplicationAmount,
                UsedAmount = r.UsedAmount,
                Balance = r.ApplicationAmount + r.UsedAmount
            });

            // 处理查询结果
            var pageResult = result.Skip(request.PageIndex * request.PageSize).Take(request.PageSize)
                .Select(p => new ExtralAmountResponseDto()
                {
                    ExtralApproveNo = p.ExtralApproveNo,
                    DivisionId = p.DivisionId,
                    ApplicationAmount = p.ApplicationAmount,
                    UsedAmount = Math.Abs(p.UsedAmount),
                    Balance = p.Balance
                }).ToList();

            foreach (var item in pageResult)
            {
                item.Division = divisions.FirstOrDefault(p => p.Id == item.DivisionId)?.DepartmentName;
            }

            var res = new PagedResultDto<ExtralAmountResponseDto>()
            {
                Items = pageResult,
                TotalCount = result.Count(),
            };

            return res;
        }

        /// <summary>
        /// Gets the extral time list asynchronous.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<PagedResultDto<ExtralTimesResponseDto>> GetExtralTimeListAsync(ExtralRequestDto request)
        {
            var divisions = await LazyServiceProvider.LazyGetService<IDataverseService>().GetDivisions();
            var querySpeakerLimitExtra = await LazyServiceProvider.LazyGetService<IOECPSASpeakerLimitExtraRepository>().GetQueryableAsync();
            var queryLimitUseHistory = await LazyServiceProvider.LazyGetService<IOECPSASpeakerLimitUseHistoryRepository>().GetQueryableAsync();

            var result = querySpeakerLimitExtra
            .Where(p => p.VendorId == request.VendorId && p.ModifyType == ModifyTypes.ManuallyAdded)
            .WhereIf(!request.Year.HasValue, a => a.Year == DateTime.Now.Year)
            .WhereIf(request.Year.HasValue, a => a.Year == request.Year)
            .Select(g => new
            {
                ExtralApproveNo = g.ExtralAuditApplicationNo,
                g.DivisionId,
                ApplicationTimes = g.ExtralTimesRest,
                UsedTimes = queryLimitUseHistory.Where(a => a.PsaExternalId == g.Id).Sum(a => a.Times) - g.ExtralTimesRest
            })
            .Select(r => new ExtralTimesResponseDto
            {
                ExtralApproveNo = r.ExtralApproveNo,
                DivisionId = r.DivisionId,
                ApplicationTimes = r.ApplicationTimes,
                UsedTimes = r.UsedTimes,
                Balance = r.ApplicationTimes + r.UsedTimes
            });

            // 处理查询结果
            var pageResult = result.Skip(request.PageIndex * request.PageSize).Take(request.PageSize)
                .Select(p => new ExtralTimesResponseDto()
                {
                    ExtralApproveNo = p.ExtralApproveNo,
                    DivisionId = p.DivisionId,
                    ApplicationTimes = p.ApplicationTimes,
                    UsedTimes = Math.Abs(p.UsedTimes),
                    Balance = p.Balance
                }).ToList();

            foreach (var item in pageResult)
            {
                item.Division = divisions.FirstOrDefault(p => p.Id == item.DivisionId)?.DepartmentName;
            }

            var res = new PagedResultDto<ExtralTimesResponseDto>()
            {
                Items = pageResult,
                TotalCount = result.Count(),
            };

            return res;
        }

        /// <summary>
        /// Gets the division asynchronous.
        /// </summary>
        /// <returns></returns>
        public async Task<List<DropdownListDto>> GetDivisionAsync()
        {
            var _dataverseService = _serviceProvider.GetService<IDataverseService>();
            var divisions = await _dataverseService.GetDivisions();
            var divisionList = divisions.ToList();
            var result = ObjectMapper.Map<List<DepartmentDto>, List<DropdownListDto>>(divisionList);
            return result;
        }

        /// <summary>
        /// Speakers the dosage detail list asynchronous.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<PagedResultDto<SpeakerDosageDetailResponseDto>> SpeakerDosageDetailListAsync(SpeakerDosageDetailRequestDto request)
        {
            var divisions = await _serviceProvider.GetService<IDataverseService>().GetDivisions(null);
            var querySpeakerLimitUseHistory = await LazyServiceProvider.LazyGetService<IOECPSASpeakerLimitUseHistoryRepository>().GetQueryableAsync();
            var queryPrApplication = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var queryUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser, Guid>>().GetQueryableAsync();

            if (!string.IsNullOrEmpty(request.Division))
                request.DivisionId = divisions.FirstOrDefault(p => p.DepartmentName == request.Division)?.Id;

            var query = querySpeakerLimitUseHistory
                .Where(p => p.VendorId == request.VendorId)
                .WhereIf(!string.IsNullOrEmpty(request.Division), p => p.BuId == request.DivisionId)
                .WhereIf(request.OperateTimeStart.HasValue, p => p.CreationTime >= request.OperateTimeStart.Value.Date)
                .WhereIf(request.OperateTimeEnd.HasValue, p => p.CreationTime < request.OperateTimeEnd.Value.Date.AddDays(1))
                .WhereIf(request.OperDetailType.Any(), p => request.OperDetailType.Contains(p.OperDetailType))
                .GroupJoin(queryPrApplication, a => a.PrApplicationId, a => a.Id, (a, b) => new { History = a, PrApplications = b })
                .SelectMany(a => a.PrApplications.DefaultIfEmpty(), (a, b) => new { a.History, PrApplication = b })
                .GroupJoin(queryUser, a => a.History.CreatorId, a => a.Id, (a, b) => new { a.History, a.PrApplication, Users = b })
                .SelectMany(a => a.Users.DefaultIfEmpty(), (a, b) => new { a.History, a.PrApplication, User = b })
                .OrderByDescending(a=>a.History.CreationTime)
                .Select(p => new SpeakerDosageDetailResponseDto
                {
                    Id = p.History.Id,
                    OperateTime = p.History.CreationTime.ToString("yyyy-MM-dd HH:mm:ss"),
                    CreatorId = p.History.CreatorId,
                    OperateName = p.User.Name,
                    ModifyType = p.History.OperateType,
                    DivisionId = p.History.BuId,
                    OperDetailType = p.History.OperDetailType,
                    Frequency = p.History.Times,
                    Amount = p.History.Amount,
                    PrApplicationId = p.History.PrApplicationId,
                    PRNo = p.PrApplication.ApplicationCode,
                    EffectiveDate = p.History.EffectiveDate.ToShortDateString()
                });

            var datas = query.Skip(request.PageIndex * request.PageSize).Take(request.PageSize).ToArray();

            foreach (var item in datas)
            {
                item.Division = divisions.FirstOrDefault(p => p.Id == item.DivisionId)?.DepartmentName;
                item.Type = EnumUtil.GetDescription(item.OperDetailType);
                item.DetailedType = EnumUtil.GetDescription(item.ModifyType);
            }

            var res = new PagedResultDto<SpeakerDosageDetailResponseDto>()
            {
                Items = datas,
                TotalCount = query.Count(),
            };

            return res;
        }

        /// <summary>
        /// Manuals the operation detail asynchronous.
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns></returns>
        public async Task<MessageResult> ManualOperationDetailAsync(Guid id)
        {
            try
            {
                var divisions = await GetDivisionAsync();
                var _userRepository = _serviceProvider.GetService<IIdentityUserRepository>();
                var _speakerLimitExtraRepository = _serviceProvider.GetService<IOECPSASpeakerLimitExtraRepository>();
                var speakerLimitExtraQueryable = await _speakerLimitExtraRepository.GetQueryableAsync();
                var _attachmentRepository = _serviceProvider.GetService<IRepository<Entities.Common.Attachment.Attachment, Guid>>();
                var attachmentQueryable = await _attachmentRepository.GetQueryableAsync();
                var _speakerLimitUseHistoryRepository = _serviceProvider.GetService<IRepository<OECPSASpeakerLimitUseHistory, Guid>>();
                var speakerLimitUseHistoryQueryable = await _speakerLimitUseHistoryRepository.GetQueryableAsync();

                var operationData = speakerLimitUseHistoryQueryable.FirstOrDefault(p => p.Id == id);
                if (operationData == null)
                {
                    return MessageResult.FailureResult($"Not found speakerLimitUseHistory data, this request id is {id}");
                }
                var user = await _userRepository.FindAsync((Guid)operationData.CreatorId);

                var limit = speakerLimitExtraQueryable.FirstOrDefault(p => p.Id == operationData.PsaExternalId);
                //if (limit == null)
                //{
                //    return MessageResult.FailureResult($"Not found speakerLimitUseHistory data, this Psa External id is {operationData.PsaExternalId}");
                //}
                List<UploadFileResponseDto> uploadFileResponseDto = new();
                if (!string.IsNullOrEmpty(limit?.Doc))
                {
                    var urls = limit.Doc.Split(',');

                    var attachments = attachmentQueryable.Where(p => urls.Contains(p.Id.ToString())).ToArray();

                    foreach (var url in urls)
                    {
                        var attachment = attachments.FirstOrDefault(a => a.Id == Guid.Parse(url));
                        if (attachment != null)
                        {
                            var file = new UploadFileResponseDto()
                            {
                                FileName = attachment.FileName,
                                FilePath = attachment.FilePath
                            };
                            uploadFileResponseDto.Add(file);
                        }
                    }
                }
                var result = new ManualOperationDetailDto()
                {
                    OperateTime = operationData.CreationTime.ToString("yyyy-MM-dd hh:mm:ss"),
                    OperateName = user?.Name,
                    OperateType = EnumUtil.GetDescription(operationData.OperateType),
                    Division = divisions.FirstOrDefault(p => p.Id == operationData.BuId)?.Name,
                    ExtralTimesRest = operationData.Times,
                    ExtralAmountRest = operationData.Amount,
                    ExtralAuditApplicationNo = limit?.ExtralAuditApplicationNo,
                    Year = limit?.Year,
                    Remark = limit?.Remark,
                    Urls = uploadFileResponseDto
                };

                return MessageResult.SuccessResult(result);
            }
            catch (Exception ex)
            {
                _logger.LogError($"SpeakerLimitService's ManualOperationDetailAsync has an error : {ex.Message}");
                return MessageResult.FailureResult($"SpeakerLimitService's ManualOperationDetailAsync has an error : {ex.Message}");
            }

        }

        /// <summary>
        /// 记录PSA上限使用情况
        /// </summary>
        /// <param name="requests"></param>
        /// <returns></returns>
        public async Task<MessageResult> AddSpeakerLimitUseHistoryAsync(IEnumerable<AddSpeakerLimitUseHistoryRequestDto> requests)
        {
            var psaUseHistoryRepository = LazyServiceProvider.LazyGetService<IOECPSASpeakerLimitUseHistoryRepository>();
            var usedHistories = ObjectMapper.Map<IEnumerable<AddSpeakerLimitUseHistoryRequestDto>, IEnumerable<OECPSASpeakerLimitUseHistory>>(requests);
            foreach (var item in usedHistories)
            {
                item.Times = item.OperDetailType == OperDetailType.Added ? Math.Abs(item.Times) : -Math.Abs(item.Times);
                item.Amount = item.OperDetailType == OperDetailType.Added ? Math.Abs(item.Amount) : -Math.Abs(item.Amount);
            }

            await psaUseHistoryRepository.InsertManyAsync(usedHistories);

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// PR使用次数和金额
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> SavePrSpeakerLimitAsync(PrUseSpeakerLimitRequest request)
        {
            var psaUseHistoryRepository = LazyServiceProvider.LazyGetService<IOECPSASpeakerLimitUseHistoryRepository>();

            List<OECPSASpeakerLimitExtra> speakerLimits = null;
            var exceptionNumbers = request.Details.Where(a => !string.IsNullOrEmpty(a.ExceptionNumber)).Select(a => a.ExceptionNumber).Distinct().ToArray();
            if (exceptionNumbers.Any())
                speakerLimits = await LazyServiceProvider.LazyGetService<IOECPSASpeakerLimitExtraRepository>().GetListAsync(a => exceptionNumbers.Contains(a.ExtralAuditApplicationNo) && a.ModifyType == ModifyTypes.ManuallyAdded);

            var tobeUseDatas = request.Details.GroupBy(a => new { a.VendorId, a.ExceptionNumber, a.Type1, a.Type2 }).Select(a => new AddSpeakerLimitUseHistoryRequestDto
            {
                PrApplicationId = request.PrApplicationId,
                BuId = request.BuId,
                VendorId = a.Key.VendorId,
                PsaExternalId = speakerLimits?.FirstOrDefault(a1 => a1.VendorId == a.Key.VendorId && string.Equals(a1.ExtralAuditApplicationNo, a.Key.ExceptionNumber, StringComparison.CurrentCultureIgnoreCase))?.Id,
                EffectiveDate = a.Max(a1 => a1.EffectiveDate),
                Times = a.Sum(a1 => a1.Times),
                Amount = a.Sum(a1 => a1.Amount),
                OperDetailType = a.Key.Type1,
                OperateType = a.Key.Type2
            })
            .ToArray();

            var result = await AddSpeakerLimitUseHistoryAsync(tobeUseDatas);
            return result;
        }

    }
}
