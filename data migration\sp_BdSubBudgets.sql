CREATE PROCEDURE dbo.sp_BdSubBudgets
AS 
BEGIN
	
-- drop table #BdSubBudgets_tmp;
with UseInfo as(
select Number,sum(Budget) Budget
from PLATFORM_ABBOTT_Stg.dbo.ods_T_Pur_ExpenseBudget_UseInfo
group by Number
)
,ReturnInfo as(
select BDNumber,sum(Budget) Budget
from PLATFORM_ABBOTT_Stg.dbo.ods_T_Pur_ExpenseBudget_ReturnInfo
group by BDNumber
),
LogInfo as (
select * 
from (select *,row_number() over(partition by Number order by  operateDate)  
rn from PLATFORM_ABBOTT_Stg.dbo.ODS_T_Pur_ExpenseBudget_LogInfo )a
where rn=1
),
LogInfo1 as (
select * 
from (select *,row_number() over(partition by Number order by  operateDate desc)  
rn from PLATFORM_ABBOTT_Stg.dbo.ODS_T_Pur_ExpenseBudget_LogInfo )a
where rn=1
),
BudgetMapping as (
select * from (select *,row_number() over(partition by ccNumber order by  [Date] desc)  
rn from PLATFORM_ABBOTT_Stg.dbo.ods_T_BudgetMapping)a
where rn=1
)
select 
newid() AS Id,--自动生成的uuid
a.Number AS Code,--
a.ParentNumber AS MasterBudgetId,--基于该信息匹配至11-1迁移的主预算ID
a.CostCenterId AS CostCenterId,--基于该编码匹配至成本中心ID
a.RegionId AS RegionId,--基于该编码匹配至大区ID
a.BUId AS BuId,--基于该编码匹配至组织中"BU"层级部门的ID
a.OwnerId AS OwnerId,--以该ID匹配至员工主数据
case when a.isEnable=1 then b.Budget-c.Budget else 0 end  AS UesdAmount,--基于T_Pur_ExpenseBudget_UseInfo.Number为子预算编码，isEnable=1查询到所有的使用记录，加总后得到使用总金额U1
--基于T_Pur_ExpenseBudget_ReturnInfo.BDNumber为子预算编码，isEnable=1查询到所有的退回记录，加总后得到使用总金额R1
--以U1-R1得到该预算编码实际使用的总金额"
cast(null as nvarchar(100)) AS AvailableAmount,--以BudgetAmount-UesdAmount得到剩余金额
FileId AS AttachmentFile,--以该ID查询匹配出的NexBPM支持文件ID，多个支持文件以英文逗号分隔
'{}' AS ExtraProperties,--默认填写为"{}"
'' AS ConcurrencyStamp,--?
d.OperateDate AS CreationTime,--以Number查询该表Number，将operateDate最早记录的对应OperateDate填入
d.OperateEmpId AS CreatorId,--以Number查询该表Number，将operateDate最早记录的对应OperateEmpId找出后以该ID匹配至员工主数据，若该值填写为1则填写为默认用户(admin)
o.OperateDate AS LastModificationTime,--以Number查询该表Number，将operateDate最新记录的对应OperateDate填入
o.OperateEmpId AS LastModifierId,--以Number查询该表Number，将operateDate最新记录的对应OperateEmpId找出后以该ID匹配至员工主数据，若该值填写为1则填写为默认用户(admin)
'' AS IsDeleted,--默认为0(BPM的删除功能是hard delete，已删除的不会有记录)
'' AS DeleterId,--默认为空
'' AS DeletionTime,--默认为空
Description AS Description,--
case when (d.Remark <> null or d.Remark <> '') and  d.Remark not like '********%' then d.Remark else '' end  AS Remark,--以Number查询该表Number，将operateDate最早记录的对应Remark填入，若为空或填为"********"则留空
a.IsEnable AS Status,--
case when Data01 is Null or Data01='' then 0 else Data01 end AS IsComplicanceAudits,--基于该表Number匹配至预算编码，该字段若为0或空均填写为0
BU2 AS Bu2,--以Number查询该表CCNumber，将对应信息填入
LMM AS LMMs,--以Number查询该表CCNumber，将对应信息以""/""切分后，将切分得到的八位数字查询T_Employee.Emp_Code，将匹配回的Emp_Name填入，如果有多个编码匹配到了多个名字，则以""/""分隔
--(多个值的填写逻辑及是否需要迁移为ID还在与开发确认)"
Principal AS Owner2,--
PM AS ProductManagers,--
DM AS RegionManagers,--
RA AS RegionalAssistants,--
a.Budget AS BudgetAmount--
into #BdSubBudgets_tmp
from PLATFORM_ABBOTT_Stg.dbo.ODS_T_Pur_ExpenseBudget_SubInfo a
left join UseInfo b
on a.Number =b.Number
left join ReturnInfo c
on a.Number =c.BDNumber
left join LogInfo d
on a.Number =d.Number
left join LogInfo1 o
on a.Number =o.Number
left join PLATFORM_ABBOTT_Stg.dbo.ods_T_Pur_ExpenseBudget_SubInfo_EXTEND e
on a.Number =e.Number
left join BudgetMapping g
on a.Number =g.CCNumber

update a set a.AvailableAmount=cast(b.AvailableAmount as nvarchar(255)) from #BdSubBudgets_tmp a
left join (select id,BudgetAmount-UesdAmount as AvailableAmount from #BdSubBudgets_tmp) b
on a.id=b.id

update a set a.LMMs=b.Emp_Name from #BdSubBudgets_tmp a
left join ( 
select id,STRING_AGG(cast(Emp_name as nvarchar(1000)), '/') WITHIN GROUP (ORDER BY id) Emp_Name from (
	SELECT id,
        TRIM(value) AS LMM
    FROM  #BdSubBudgets_tmp
	CROSS APPLY STRING_SPLIT(LMMs, '/') ) a
	left join PLATFORM_ABBOTT_Stg.dbo.ODS_T_EMPLOYEE  c
	on a.LMM=c.EMP_code
	group by id
    ) b
on a.id=b.id



IF OBJECT_ID(N'PLATFORM_ABBOTT_Stg.dbo.BdSubBudgets_tmp ', N'U') IS NOT NULL
	BEGIN
		
		ALTER TABLE PLATFORM_ABBOTT_Stg.dbo.BdSubBudgets_tmp ALTER COLUMN AvailableAmount varchar(100) COLLATE Chinese_PRC_CI_AS  NULL;
		update a 
		set a.Code                  = b.Code
           ,a.MasterBudgetId        = b.MasterBudgetId
           ,a.CostCenterId          = b.CostCenterId
           ,a.RegionId              = b.RegionId
           ,a.BuId                  = b.BuId
           ,a.OwnerId               = b.OwnerId
           ,a.UesdAmount            = b.UesdAmount
           ,a.AvailableAmount       = b.AvailableAmount
           ,a.AttachmentFile        = b.AttachmentFile
           ,a.ExtraProperties       = b.ExtraProperties
           ,a.ConcurrencyStamp      = b.ConcurrencyStamp
           ,a.CreationTime          = b.CreationTime
           ,a.CreatorId             = b.CreatorId
           ,a.LastModificationTime  = b.LastModificationTime
           ,a.LastModifierId        = b.LastModifierId
           ,a.IsDeleted             = b.IsDeleted
           ,a.DeleterId             = b.DeleterId
           ,a.DeletionTime          = b.DeletionTime
           ,a.Description           = b.Description
           ,a.Remark                = b.Remark
           ,a.Status                = b.Status
           ,a.IsComplicanceAudits   = b.IsComplicanceAudits
           ,a.Bu2                   = b.Bu2
           ,a.LMMs                  = b.LMMs
           ,a.Owner2                = b.Owner2
           ,a.ProductManagers       = b.ProductManagers
           ,a.RegionManagers        = b.RegionManagers
           ,a.RegionalAssistants    = b.RegionalAssistants
           ,a.BudgetAmount          = b.BudgetAmount
        from PLATFORM_ABBOTT_Stg.dbo.BdSubBudgets_tmp a
        left join #BdSubBudgets_tmp b on a.code = b.code
        
        insert into PLATFORM_ABBOTT_Stg.dbo.BdSubBudgets_tmp
        select a.Id
              ,a.Code
              ,a.MasterBudgetId
              ,a.CostCenterId
              ,a.RegionId
              ,a.BuId
              ,a.OwnerId
              ,a.UesdAmount
              ,a.AvailableAmount
              ,a.AttachmentFile
              ,a.ExtraProperties
              ,a.ConcurrencyStamp
              ,a.CreationTime
              ,a.CreatorId
              ,a.LastModificationTime
              ,a.LastModifierId
              ,a.IsDeleted
              ,a.DeleterId
              ,a.DeletionTime
              ,a.Description
              ,a.Remark
              ,a.Status
              ,a.IsComplicanceAudits
              ,a.Bu2
              ,a.LMMs
              ,a.Owner2
              ,a.ProductManagers
              ,a.RegionManagers
              ,a.RegionalAssistants
              ,a.BudgetAmount
        from #BdSubBudgets_tmp a
        where not EXISTS (select * from PLATFORM_ABBOTT_Stg.dbo.BdSubBudgets_tmp where code = a.code)
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into PLATFORM_ABBOTT_Stg.dbo.BdSubBudgets_tmp from #BdSubBudgets_tmp
    -- select * from #vendor_tbl
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
END




