create proc sp_PurBWApplications
as
begin
	--定义一个表，用于存储从T_FORMINSTANCE_GLOBAL表的XmlContent中解析出来的数据
	declare @BpmGlobal table
	(
		ProcInstId int,
		DeliveryDate varchar(50),
		ProjectName nvarchar(200),
		Purchaser<PERSON><PERSON><PERSON> varchar(50),
		<PERSON>ENo varchar(50),
		CheckBoxGoup varchar(50),
		CheckBoxGoup1 varchar(50),
		Reason1 nvarchar(3000),
		UpId varchar(2000),
		AttachmentId varchar(2000)
	);

	--解析Xml中的数据，存储到该表中
	insert @BpmGlobal
	select
		ProcInstId,
		DeliveryDate,
		ProjectName,
		PurchaserValue,
		RCENo,
		CheckBoxGoup,
		CheckBoxGoup1,
		Reason1,
		STRING_AGG(UpId,',') UpId,
		STRING_AGG(AttachmentId,',') AttachmentId
	from 
	(
		select 
		t.ProcInstId,
		up_Id.value('.','varchar(32)') UpId,
		cast(attach.Id as varchar(36)) AttachmentId,
		XmlContent.value('(/root/WaiverApplication_WaiverBlock1_MainStore/DeliveryDate)[1]','varchar(50)') DeliveryDate,
		XmlContent.value('(/root/WaiverApplication_WaiverBlock1_MainStore/ProjectName)[1]','nvarchar(200)') ProjectName,
		XmlContent.value('(/root/WaiverApplication_WaiverBlock1_MainStore/Purchaser_Value)[1]','varchar(50)') PurchaserValue,
		XmlContent.value('(/root/WaiverApplication_WaiverBlock1_MainStore/RCENo)[1]','varchar(50)') RCENo,
		XmlContent.value('(/root/WaiverApplication_WaiverBlock_MainStore/checkBoxGoup)[1]','varchar(50)') CheckBoxGoup,
		XmlContent.value('(/root/WaiverApplication_WaiverBlock1_MainStore/checkBoxGoup1)[1]','varchar(50)') CheckBoxGoup1,
		XmlContent.value('(/root/WaiverApplication_WaiverBlock1_MainStore/reason1)[1]','nvarchar(3000)') Reason1
		from 
		(
			select 
				b.ProcInstId,
				b.XmlContent
			from ODS_AUTO_BIZ_T_WaiverApplication_info a left join ODS_T_FORMINSTANCE_GLOBAL b
			on a.ProcInstId = b.ProcInstId
		)t
		CROSS APPLY XmlContent.nodes('/root/AttachmentBlock/row/up_Id') AS XMLTable(up_Id)
		join Attachments_tmp attach on up_Id.value('.','varchar(32)')=attach.BPMId
	)tt
	group by ProcInstId,DeliveryDate,ProjectName,PurchaserValue,RCENo,CheckBoxGoup,CheckBoxGoup1,Reason1

	--组装数据insert到临时表
	select
		a.serialNumber [ApplicationCode],
		a.applicantDeptId [BpmApplyDeptId],
		org.spk_NexBPMCode [ApplyDeptId],
		a.applicantDept_Text [ApplyDeptName],
		a.applicationDate [ApplyTime],
		a.applicantEmpId [BpmApplyUserId],
		applicant.spk_NexBPMCode [ApplyUserId],
		a.applicantEmpName [ApplyUserName],
		b.UpId [BpmAttachmentIds],
		b.AttachmentId [AttachmentIds],
		company.spk_CompanyCode [CompanyCode],
		company.spk_BPMCode [BpmCompanyId],
		company.spk_NexBPMCode [CompanyId],
		company.spk_Name [CompanyName],
		'' [ConcurrencyStamp],
		a.applicationDate [CreationTime],
		a.applicantEmpId [BpmCreatorId],
		applicant.spk_NexBPMCode [CreatorId],
		'' [DeleterId],
		'' [DeletionTime],
		org.spk_BPMCode [BpmDivisionId],
		org.spk_NexBPMCode [DivisionId],
		org.spk_Name [DivisionName],
		b.DeliveryDate [EstDeliveryDate],
		1 [ExemptType],--默认填写为1(1-waiver, 2-justification)
		'{}' [ExtraProperties],
		a.EventCity,
		b. ProjectName BpmProjectName,
		case when ISNULL(a.EventCity,'')<>'' then a.EventCity else b.ProjectName end [GoodsServicesRequested],
		0 [IsDeleted],
		'' [JustificationTypeId],
		'' [JustificationTypeText],
		'' [LastModificationTime],
		'' [LastModifierId],
		a.PRNo,
		--prItem.PRFormCode,
		'' [PRApplicationCode],--case when ISNULL(a.PRNo,'')<>'' then a.PRNo else prItem.PRFormCode end [PRApplicationCode],--按照Waiver单号查询该表中的[WaiverFormCode]后查询到对应的PR单号?
		'' [PRDetailId],--依赖PRApplicationCode，暂缓?
		'' [PRId],--依赖PRApplicationCode，暂缓?
		'' [ProjectDescription],
		'' [ProjectName],
		purchaser.bpm_id [BpmPurchaserId],
		purchaser.spk_NexBPMCode [PurchaserId],
		b.RCENo [RceNo],
		'' [Remark],
		a.EstimatedAmount [RequisitionAmount],
		case 
			when form.processStatus=N'完成' then 3
			when form.processStatus=N'发起人终止' then
				case when (select top 1 ActName from ODS_T_PROCESS_Historys where ProcInstID=a.ProcInstId order by FinishDate desc)=N'重发起' then 6 else 4 end
			when form.processStatus=N'重发起' or form.processStatus=N'审批中' or form.processStatus=N'预算负责人审批' then 5
		end [Status],
		case
			when form.processStatus=N'完成' then N'完成'
			when form.processStatus=N'发起人终止' then
				case when (select top 1 ActName from ODS_T_PROCESS_Historys where ProcInstID=a.ProcInstId order by FinishDate desc)=N'重发起' then N'作废' else N'已拒绝' end
			when form.processStatus=N'重发起' or form.processStatus=N'审批中' or form.processStatus=N'预算负责人审批' then N'重新发起'
		end StatusName,
		a.SupplyCode [VendorCode],
		avm.Id [VendorId],
		a.SupplyName [VendorName],
		b.CheckBoxGoup,
		b.CheckBoxGoup1,
		waiveReason.spk_NexBPMCode [WaiveReasonId],
		case when isnull(res.Res_Name,'')<>'' then res.Res_Name else res1.Res_Name end [WaiveReasonText],
		case when isnull(a.reason,'')<>'' then a.reason else b.Reason1 end [WaiveRequest]
		into #PurBWApplications_tmp
	from ODS_AUTO_BIZ_T_WaiverApplication_Info a
	left join @BpmGlobal b on a.ProcInstId=b.ProcInstId
	left join spk_staffmasterdata applicant on a.applicantEmpId=applicant.bpm_id
	left join (select * from spk_companymasterdata a1 join ODS_T_RESOURCE b1 on a1.spk_BPMCode=b1.Res_Code) company on a.Company_Code=company.Res_Data --查询公司
	left join (select * from spk_organizationalmasterData where spk_organizationType='BU') org on a.applicantDept_Text like org.spk_Name+(case when CHARINDEX('-',a.applicantDept_Text)=0 then '' else '-' end)+'%' --查询组织
	--left join (select PRFormCode,WaiverFormCode from ODS_T_Pur_PRItems_Info where ISNULL(WaiverFormCode,'')<>'' group by PRFormCode,WaiverFormCode) prItem on a.serialNumber=prItem.WaiverFormCode --查询PrItem，这里还有问题，涉及多个pr?
	left join spk_staffmasterdata purchaser on b.PurchaserValue=purchaser.bpm_id
	join ODS_Form_c2bc32cd01e74718b19ff60c52cf98f6 form on a.ProcInstId=form.ProcInstId and isnull(form.processStatus,'')<>'' --查询状态，草稿状态不迁移
	left join (select avm.Id,cast(avm.VENDOR as varchar) VENDOR,cast(avm.VCMPNY as varchar) VCMPNY,pmfvm.VEXTNM from ODS_BPCS_AVM avm join ODS_BPCS_PMFVM pmfvm on avm.VENDOR=pmfvm.VNDERX and avm.VCMPNY=pmfvm.VMCMPY) avm on avm.VENDOR=a.SupplyCode and avm.VEXTNM=a.SupplyName and avm.VCMPNY=company.spk_CompanyCode --查询供应商信息
	left join spk_dictionary waiveReason on ((case when isnull(b.CheckBoxGoup,'')<>'' then b.CheckBoxGoup else b.CheckBoxGoup1 end)=waiveReason.spk_BPMCode)
	left join ODS_T_RESOURCE res on b.CheckBoxGoup=res.Res_Code
	left join ODS_T_RESOURCE res1 on b.CheckBoxGoup1=res1.Res_Code

	--将临时表的数据insert到tmp表
	 IF OBJECT_ID(N'PurBWApplications_tmp', N'U') IS NOT NULL
		drop table PurBWApplications_tmp;
	select * into PurBWApplications_tmp from #PurBWApplications_tmp;
	PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'));
end