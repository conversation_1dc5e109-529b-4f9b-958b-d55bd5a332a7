﻿using Abbott.SpeakerPortal.Contracts.Agent;
using Abbott.SpeakerPortal.Contracts.ReturnAndExchange;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Entities.Common.Workflow.WorkflowTasks;
using Abbott.SpeakerPortal.Entities.EFlow.Return;
using Abbott.SpeakerPortal.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Abbott.SpeakerPortal.Extension;

namespace Abbott.SpeakerPortal.AppServices.ReturnAndExchange
{
    public partial class ReturnService : SpeakerPortalAppService, IReturnService
    {
        /// <summary>
        /// 获取我发起的任务
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetReturnAppliedByMeResponseDto>> GetAppliedByMeAsync(GetReturnAppliedByMeRequestDto request, ReturnTypeEnum Type)
        {
            ReturnApplicationStatus[] pendingProcessings = [ReturnApplicationStatus.RejectedBack];
            ReturnApplicationStatus[] progressings = [ReturnApplicationStatus.Approving];
            ReturnApplicationStatus[] completeds = [ReturnApplicationStatus.Rejected, ReturnApplicationStatus.ApplicantTerminate, ReturnApplicationStatus.Approved];
            var agents = await LazyServiceProvider.LazyGetService<IAgencyService>().GetOriginalOperators(new GetAgentOperatorsRequestDto { BusinessTypeCategory = ResignationTransfer.TaskFormCategory.ReturnAndExchangeRequest });
            var userIds = agents.Select(a => a.Key).Distinct().ToArray();
            var returnQuery = (await LazyServiceProvider.LazyGetService<IReturnApplicationReponsitory>().GetQueryableAsync()).AsNoTracking();
            //var returnDetailQuery = (await LazyServiceProvider.LazyGetService<ISTicketApplicationDetailRepository>().GetQueryableAsync()).AsNoTracking();
            var query = returnQuery.Where(a => userIds.Contains(a.ApplyUserId) && a.ApplicationType == Type) //|| userIds.Contains(a.TransfereeId.Value)
                        .WhereIf(request.ApplicantId.HasValue, m => m.ApplyUserId == request.ApplicantId)
                        .WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), a => a.ApplicationCode.Contains(request.ApplicationCode))
                        .WhereIf(request.StartDate.HasValue, a => a.ApplyTime >= request.StartDate)
                        .WhereIf(request.EndDate.HasValue, a => a.ApplyTime <= request.EndDate.Value.Date.AddDays(1))
                        .WhereIf(!string.IsNullOrEmpty(request.ApplyDeptName), a => a.ApplyUserDeptName.Contains(request.ApplyDeptName))
                        .WhereIf(request.Status.HasValue, a => a.Status == request.Status)
                        .WhereIf(!string.IsNullOrEmpty(request.ClientCode), m => m.ClientCode.Contains(request.ClientCode))
                        .WhereIf(!string.IsNullOrEmpty(request.ClientName), m => m.ClientName.Contains(request.ClientName));

            switch (request.ProcessingStatus)
            {
                case ProcessingStatus.PendingProcessing:
                    query = query.Where(m => pendingProcessings.Contains(m.Status));
                    break;
                case ProcessingStatus.Progressing:
                    query = query.Where(m => progressings.Contains(m.Status));
                    break;
                case ProcessingStatus.Completed:
                    query = query.Where(m => completeds.Contains(m.Status));
                    break;
                default:
                    break;
            }
            var count = await query.CountAsync();
            var datas = await query.OrderByDescending(a => a.ApplyTime).PagingIf(request).ToListAsync();
            var result = ObjectMapper.Map<List<ReturnApplication>, List<GetReturnAppliedByMeResponseDto>>(datas);
            return new PagedResultDto<GetReturnAppliedByMeResponseDto>(count, result);
        }

        /// <summary>
        /// 待我审批
        /// </summary>
        /// <param name="request"></param>
        /// <param name="processingStatus"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetReturnApprovedByMeResponseDto>> GetApprovedByMeAsync(GetReturnApprovedByMeRequestDto request, ReturnTypeEnum Type)
        {
            var returnQuery = (await LazyServiceProvider.LazyGetService<IReturnApplicationReponsitory>().GetQueryableAsync()).AsNoTracking();

            //获取代理信息
            //var agents = await LazyServiceProvider.LazyGetService<IAgencyService>().GetOriginalOperators(new GetAgentOperatorsRequestDto { BusinessTypeCategory = ResignationTransfer.TaskFormCategory.STicketRequestApplication });
            //var userIds = agents.Select(a => a.Key).Distinct().ToArray();
            var query = returnQuery.Where(a => a.ApplicationType == Type)
                        .WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), a => a.ApplicationCode.Contains(request.ApplicationCode))
                        .WhereIf(request.ApplicantId.HasValue, m => m.ApplyUserId == request.ApplicantId)
                        .WhereIf(request.StartDate.HasValue, a => a.ApplyTime >= request.StartDate)
                        .WhereIf(request.EndDate.HasValue, a => a.ApplyTime <= request.EndDate.Value.Date.AddDays(1))
                        .WhereIf(!string.IsNullOrEmpty(request.ApplyDeptName), a => a.ApplyUserDeptName.Contains(request.ApplyDeptName))
                        .WhereIf(request.Status.HasValue, a => a.Status == request.Status)
                        .WhereIf(!string.IsNullOrEmpty(request.ClientCode), m => m.ClientCode.Contains(request.ClientCode))
                        .WhereIf(!string.IsNullOrEmpty(request.ClientName), m => m.ClientName.Contains(request.ClientName));

            if (request.ProcessingStatus == ProcessingStatus.PendingProcessing)
            {
                var taskRecords = await LazyServiceProvider.LazyGetService<IDataverseService>().GetApprovelTaskAsync(CurrentUser.Id.ToString(), [WorkflowTypeName.ReturnAndExchangeRequest], request.ProcessingStatus);
                var formIds = taskRecords.Select(a1 => a1.FormId).ToArray();
                var datas = await query.Where(m => formIds.Contains(m.Id)).Select(s => new
                {
                    Id = s.Id,
                    ApplyUser = s.ApplyUser,
                    ApplicationCode = s.ApplicationCode,
                    ApplyUserDeptId = s.ApplyUserDeptId,
                    ApplyUserDeptName = s.ApplyUserDeptName,
                    ClientCode = s.ClientCode,
                    ClientName = s.ClientName,
                    Status = s.Status,
                    ApplyUserId = s.ApplyUserId,
                    ApplyTime = s.ApplyTime,
                    ProductQuantity = s.TotalProductQuantity,
                }).ToListAsync();
                var count = datas.Count;
                var result = datas.Join(taskRecords, a => a.Id, a => a.FormId, (a, b) => new GetReturnApprovedByMeResponseDto
                {
                    Id = a.Id,
                    ApplicationCode = a.ApplicationCode,
                    ApplyUser = a.ApplyUser,
                    ApplyUserDeptName = a.ApplyUserDeptName,
                    ClientCode = a.ClientCode,
                    ClientName = a.ClientName,
                    Status = a.Status,
                    ApplyUserId = a.ApplyUserId,
                    ApplyTime = a.ApplyTime,
                    ProductQuantity =a.ProductQuantity
                }).OrderByDescending(o => o.ApplyTime).PagingIf(request).ToArray();
                return new PagedResultDto<GetReturnApprovedByMeResponseDto>(count, result);
            }
            else
            {
                var queryWfTask = (await LazyServiceProvider.LazyGetService<IWorkflowTaskRepository>().GetQueryableAsync()).AsNoTracking().Where(a => a.ApprovalId == CurrentUser.Id && a.Status != ApprovalOperation.Start);
                var result = query.Join(queryWfTask, a => a.Id, a => a.FormId, (a, b) => new GetReturnApprovedByMeResponseDto
                {
                    Id = a.Id,
                    ApplicationCode = a.ApplicationCode,
                    ApplyUser = a.ApplyUser,
                    ApplyUserDeptName = a.ApplyUserDeptName,
                    //CostCenter = a.CostCenter,
                    ClientCode = a.ClientCode,
                    ClientName = a.ClientName,
                    Status = a.Status,
                    ApplyUserId = a.ApplyUserId,
                    TaskId = b.Id,
                    ApplyTime = a.ApplyTime,
                    ApprovalTime = b.ApprovalTime,
                    ProductQuantity = a.TotalProductQuantity
                });
                var count = await result.CountAsync();
                var datas = await result.OrderByDescending(o => o.ApplyTime).PagingIf(request).ToListAsync();
                return new PagedResultDto<GetReturnApprovedByMeResponseDto>(count, datas);
            }
        }
    }
}
