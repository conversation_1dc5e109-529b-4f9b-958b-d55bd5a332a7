﻿using Abbott.SpeakerPortal.Consts;
using System;
using System.Collections.Generic;
using System.Text;

namespace Abbott.SpeakerPortal.Contracts.PersonCenter
{
    public class CurrentUserResponseDto
    {
        public Guid Id { get; set; }

        /// <summary>
        /// 名字
        /// </summary>
        public string Name { get; set; }

        ///// <summary>
        ///// 账号
        ///// </summary>
        //public string UserName { get; set; }

        ///// <summary>
        ///// 邮箱
        ///// </summary>
        //public string Email { get; set; }

        ///// <summary>
        ///// 电话号码
        ///// </summary>
        //public string PhoneNumber { get; set; }

        ///// <summary>
        ///// 员工号
        ///// </summary>
        //public string StaffCode { get; set; }

        /// <summary>
        /// 最后登录时间
        /// </summary>
        public string LoginTime { get; set; }

        /// <summary>
        /// 主部门Id
        /// </summary>
        public Guid? MainDepartmentId { get; set; }

        /// <summary>
        /// 角色
        /// </summary>
        public IEnumerable<string> Roles { get; set; }

        /// <summary>
        /// 启用权限
        /// </summary>
        public IEnumerable<string> EnablePermission { get; set; }

        /// <summary>
        /// 新企微用户标识(由企微登录新建的PPStaff/AbpUser)，true则需要立即为该用户设置所属组织和主部门
        /// </summary>
        public bool IsNewWcUser { get; set; } = false;

        /// <summary>
        /// 特殊权限（建议之后都这里面）
        /// </summary>
        public SpecialPermission SpecialPermission { get; set; } = new SpecialPermission();
    }

    /// <summary>
    /// 根据不同情况设置的特殊权限
    /// </summary>
    public class SpecialPermission
    {

        /// <summary>
        /// 是否显示申请
        /// </summary>
        public bool IsApplicationRecordDisplay { get; set; } = false;
        /// <summary>
        /// 是否是业务管理员
        /// </summary>
        public bool IsBizManager { get; set; } = false;

        /// <summary>
        /// 是否是EPD用户
        /// </summary>
        public bool IsEPDUser { get; set; } = false;

        /// <summary>
        /// 是否查看所有供应商草稿
        /// </summary>
        public bool IsViewAllVendorDraft { get; set; } = false;

        /// <summary>
        /// 集团财务
        /// </summary>
        public bool IsGroupFinance { get; set; } = false;

        /// <summary>
        /// 是否OEC管理员
        /// </summary>
        public bool IsOECAdmin { get; set; } = false;

        /// <summary>
        /// 是否OEC调查员
        /// </summary>
        public bool IsOECInvestigator { get; set; } = false;
    }
}
