﻿using MiniExcelLibs.Attributes;
using System;
using System.Collections.Generic;
using System.Text;

namespace Abbott.SpeakerPortal.Contracts.Budget.Subbudget
{
    public class ImportTransferSubBudgetDto
    {
        [ExcelColumnName("预算编号")]
        public string OriginalCode { get; set; }
        [ExcelColumnName("目标预算编号")]
        public string TransferCode { get; set; }
        [ExcelColumnName("调拨金额")]
        public decimal TransferAmount { get; set; }
        [ExcelColumnName("备注")]
        public string Remark { get; set; }
    }
}
