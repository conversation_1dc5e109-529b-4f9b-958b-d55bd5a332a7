CREATE PROCEDURE [dbo].[sp_OECIntercepts]
AS 
BEGIN
	--飞检拦截状态信息-基础信息
with OECIntercepts_tmp as (
select 
newid() AS Id,--自动生成的uuid
Concat(sd.ProcInstId,'+',sd.PRRowNumber) AS PRDetailId,
case when SamplingStatus ='飞检中-OEC' then 'OEC'
when SamplingStatus ='飞检中-Investigation' then 'Investigate'
when SamplingStatus ='飞检中-二要素不通过' then 'TwoElementsFailed'
when SamplingStatus ='飞检完成' then 'OEC'
when SamplingStatus ='飞检中' then 'OEC'
end AS InterceptTypeCode,
case when SamplingStatus ='飞检完成' then '2'
when SamplingStatus !='飞检完成' then '1'
end AS InterceptStatus,
sdlog.SamplingEmpId AS InterceptByUserId,
sdlog.Emp_Name AS InterceptByUserName,
sdlog.OperationDate AS InterceptTime,
'' AS InterceptRemark,
case when SamplingStatus ='飞检完成' then 'OEC'
end as ResolvedInterceptType,
'{}' AS ExtraProperties,
'' AS ConcurrencyStamp,
sdlog.OperationDate AS CreationTime,
sdlog.SamplingEmpId AS CreatorId,
'' AS LastModificationTime,
'' AS LastModifierId,
'0' AS IsDeleted,
'' AS DeleterId,
'' AS DeletionTime
FROM [PLATFORM_ABBOTT].[dbo].[ODS_T_OEC_SamplingData] sd
left join (
   SELECT t1.*,
   ROW_NUMBER() OVER(PARTITION BY t1.ProcInstID,t1.PRRowNumber ORDER BY t1.OperationDate) AS rn,
   employee.Emp_Name
  FROM [PLATFORM_ABBOTT].[dbo].[ODS_T_OEC_SamplingDataLog] t1
  left join [PLATFORM_ABBOTT].[dbo].[ODS_T_EMPLOYEE] employee
  on t1.SamplingEmpId = employee.Emp_Id) sdlog
on sd.ProcInstId =sdlog.ProcInstId and sd.PRRowNumber = sdlog.PRRowNumber and sdlog.rn = 1)

select * into #OECIntercepts_tmp from OECIntercepts_tmp 

 IF OBJECT_ID(N'PLATFORM_ABBOTT.dbo.OECIntercepts_tmp ', N'U') IS NOT NULL
	BEGIN
		update a                  
		set a.PRDetailId          = b.PRDetailId
           ,a.InterceptTypeCode   = b.InterceptTypeCode
           ,a.InterceptStatus     = b.InterceptStatus
           ,a.InterceptByUserId   = b.InterceptByUserId
           ,a.InterceptByUserName = b.InterceptByUserName
           ,a.InterceptTime       = b.InterceptTime
           ,a.InterceptRemark     = b.InterceptRemark
           ,a.ResolvedInterceptType = b.ResolvedInterceptType
           ,a.ExtraProperties     = b.ExtraProperties
           ,a.ConcurrencyStamp    = b.ConcurrencyStamp
           ,a.CreationTime        = b.CreationTime
           ,a.CreatorId           = b.CreatorId
           ,a.LastModificationTime= b.LastModificationTime
           ,a.LastModifierId      = b.LastModifierId
           ,a.IsDeleted           = b.IsDeleted
           ,a.DeleterId           = b.DeleterId
           ,a.DeletionTime        = b.DeletionTime
        from PLATFORM_ABBOTT.dbo.OECIntercepts_tmp a
        left join #OECIntercepts_tmp b on a.PRDetailId = b.PRDetailId
        
        insert into PLATFORM_ABBOTT.dbo.OECIntercepts_tmp
        select a.Id
              ,a.PRDetailId
              ,a.InterceptTypeCode
              ,a.InterceptStatus
              ,a.InterceptByUserId
              ,a.InterceptByUserName
              ,a.InterceptTime
              ,a.InterceptRemark
              ,a.ResolvedInterceptType
              ,a.ExtraProperties
              ,a.ConcurrencyStamp
              ,a.CreationTime
              ,a.CreatorId
              ,a.LastModificationTime
              ,a.LastModifierId
              ,a.IsDeleted
              ,a.DeleterId
              ,a.DeletionTime
         from #OECIntercepts_tmp a
         where not EXISTS (select * from PLATFORM_ABBOTT.dbo.OECIntercepts_tmp where PRDetailId = a.PRDetailId)
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into PLATFORM_ABBOTT.dbo.OECIntercepts_tmp from #OECIntercepts_tmp
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END

END;
