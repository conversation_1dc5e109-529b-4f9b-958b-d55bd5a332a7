﻿using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.Common;
using DocumentFormat.OpenXml.Spreadsheet;
using DocumentFormat.OpenXml.Wordprocessing;
using Flurl.Http;
using Hangfire.Logging;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Senparc.CO2NET.Exceptions;
using Senparc.Weixin.WxOpen.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.AppServices.Common
{
    public class HttpRequestService(ILogger<HttpRequestService> logger, IConfiguration configuration,

        ICommonService commonService) : SpeakerPortalAppService, IHttpRequestService
    {
        /// <summary>
        /// The logger
        /// </summary>
        private readonly ILogger<HttpRequestService> _logger = logger;

        /// <summary>
        /// The configuration
        /// </summary>
        private readonly IConfiguration _configuration = configuration;

        private readonly ICommonService _commonService = commonService;

        /// <summary>
        /// Gets the soi token.
        /// </summary>
        /// <returns></returns>
        public async Task<string> GetSOITokenAsync()
        {
            var url = $"{_configuration["Integrations:SOI:BaseUrl"]}/SOIWebAPI/api/Token/AccessToken";
            var header = new
            {
                AppKey = _configuration["Integrations:SOI:AppKey"],
                AppSecret = string.Empty,
                //AppSecret = _configuration["Integrations:SOI:AppSecret"]
            };
            var response = await url.PostUrlEncodedAsync(header);
            SetOperationLogRequestDto log = null;
            if (response.StatusCode == 200 && response.ResponseMessage.IsSuccessStatusCode)
            {
                var resultStr = await response.ResponseMessage.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<SOIGetTokenResponseDto>(resultStr);
                log = _commonService.InitOperationLog("SOI", "获取Token", url + "|" + JsonSerializer.Serialize(header));
                _commonService.LogResponse(log, resultStr);
                if (result.Code == 200)
                {
                    return result.Data.AccessToken;
                }
                else
                {
                    throw new HttpException(result.Message);
                }
            }
            else
            {
                throw new HttpException("获取token报错了");
            }
        }
        /// <summary>
        /// HTTPs the post asynchronous.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="jsont">The jsont.</param>
        /// <param name="url">The URL.</param>
        /// <param name="authentication">The authentication.</param>
        /// <param name="headers">The headers.</param>
        /// <returns></returns>
        /// <exception cref="HttpException"></exception>
        public async Task<T> HttpPostAsync<T>(string json, string url, AuthenticationHeaderValue authentication = null, Dictionary<string, string> headers = null, string formNo = null)
        {
            using var client = new HttpClient();
            client.BaseAddress = new Uri(url);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            SetOperationLogRequestDto log = null;
            log = _commonService.InitOperationLog("SOI", "SOI核销接口", url + "|" + JsonSerializer.Serialize(json));
            log.FormNo = formNo;

            if (authentication != null)
            {
                client.DefaultRequestHeaders.Authorization = authentication;
            }
            if (headers != null)
            {
                foreach (var header in headers)
                {
                    client.DefaultRequestHeaders.Add(header.Key, header.Value);
                }
            }
            HttpResponseMessage response = await client.PostAsync(url, content);
            if (response.IsSuccessStatusCode)
            {
                string responseBody = await response.Content.ReadAsStringAsync();
                _commonService.LogResponse(log, responseBody);
                return JsonSerializer.Deserialize<T>(responseBody);
            }
            else
            {
                string errorResponse = await response.Content.ReadAsStringAsync();
                _commonService.LogResponse(log, errorResponse, false);
                throw new HttpException(errorResponse);
            }
        }
    }
}
