CREATE PROCEDURE dbo.sp_VendorApplications
AS 
BEGIN
	
--	drop table #VendorApplications_tem1
	
	--供应商申请单-基础信息

IF OBJECT_ID(N'dbo.xml_20', N'U') IS NOT NULL
BEGIN
PRINT(N'已经初始化'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
select 
	ProcInstId
	,each_row.row.value('(./action/text())[1]', 'nvarchar(100)') as ac_status
    ,each_row.row.value('(./approvalTime/text())[1]', 'nvarchar(100)') as approvalTime  
    ,each_row.row.value('(./approvalPersonEmpId/text())[1]', 'nvarchar(100)') as approvalPersonEmpId
    ,each_row.row.value('(./approvalLevel/text())[1]', 'nvarchar(100)') as approvalLevel
into xml_20
from (
	select
		XmlContent as XmlContent,
		ProcInstId
	from PLATFORM_ABBOTT.dbo.ODS_T_FORMINSTANCE_GLOBAL
	where FORM_Id in ('663dd63299be45d69dd8f853d0a4b445','7a708c9568fb444a884eb5eca658975f')
	)A
CROSS APPLY XmlContent.nodes('/root/approvalHistoryGrid/row') as each_row(row);
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END;
IF OBJECT_ID(N'dbo.xml_23', N'U') IS NOT NULL
BEGIN
PRINT(N'已经初始化'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
select 
ProcInstId,
XmlContent.value('(/root/SupplierApplication_supplierInfoBlock_MainStore/ModifyInfo)[1]', 'nvarchar(500)') as ModifyInfo,
XmlContent.value('(/root/HcpLevelApplication_applicantBaseInfoBlock_MainStore/OldData)[1]', 'nvarchar(500)') as OldData ,
XmlContent.value('(/root/HcpLevelApplication_hiddenBlock_MainStore/OldLevel1)[1]', 'nvarchar(500)') as OldLevel1
into xml_23
from ODS_T_FORMINSTANCE_GLOBAL a  
where  FORM_Id in ('663dd63299be45d69dd8f853d0a4b445','7a708c9568fb444a884eb5eca658975f')
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END;


	--准备工作
select   c.ProcInstId,a.VCMPNY,a.VENDOR into #upid  from PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a 
join PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM b 
on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
join (select *,ROW_NUMBER () over(partition by vendorNumber,company_Value,supplierCNName order by ProcInstId desc) rn from PLATFORM_ABBOTT.dbo.ods_AUTO_BIZ_T_SupplierApplication_Info) c
on cast(VENDOR as nvarchar(255))=cast(vendorNumber as nvarchar(255)) and cast(VCMPNY as nvarchar(255))=cast(company_Value as nvarchar(255)) and cast(VEXTNM as nvarchar(255))=cast(supplierCNName as nvarchar(255)) and c.rn=1
join PLATFORM_ABBOTT.dbo.ods_Form_7a708c9568fb444a884eb5eca658975f d
on c.ProcInstId =d.ProcInstId 
where processstatus=N'终止(系统)' or processstatus=N'已完成';
select   c.ProcInstId,a.VCMPNY,a.VENDOR into #upid1 
from PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a 
join PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM b 
on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
join (select *,ROW_NUMBER () over(partition by supplierCode,supplierCNName order by ProcInstId desc) rn from PLATFORM_ABBOTT.dbo.ods_AUTO_BIZ_T_HcpLevelApplication_info) c
on cast(VENDOR as nvarchar(255))=cast(supplierCode as nvarchar(255)) and cast(VEXTNM as nvarchar(255))=cast(supplierCNName as nvarchar(255)) and c.rn=1 --and cast(VEXTNM as nvarchar(255))=cast(supplierCNName as nvarchar(255))
join PLATFORM_ABBOTT.dbo.ods_Form_663dd63299be45d69dd8f853d0a4b445 d
on c.ProcInstId =d.ProcInstId 
where processstatus=N'终止(系统)' or processstatus=N'已完成';

with 
File_UP as (
select * from xml_20 a
join ODS_T_METAFILE b 
on b.Emp_ID=a.approvalPersonEmpId
where approvalLevel='IFODPSCheck'
),
cityInfo as (
	SELECT 
		b.spk_name ,
		b.spk_provincecode ,
		a.spk_citycode ,
		a.spk_name as spk_name1,
		LEFT(a.spk_name,2) spk_name2,
		LEFT(a.spk_name,3) spk_name3,
		LEFT(a.spk_name,4) spk_name4,
		Len(a.spk_name) spk_nameSize,
		concat(b.spk_provincialadministrativecode,',',a.spk_cityadministrativedivisioncode) as bankcity_Code
	from   
		PLATFORM_ABBOTT.dbo.spk_city a,
		PLATFORM_ABBOTT.dbo.spk_province b
	where  a.spk_provincenamename = b.spk_name
),
bankcity_Cd as (
select 
 xv.Procinstid
,xv.bankCity
,COALESCE (city1.bankcity_Code,city2.bankcity_Code,city3.bankcity_Code,city4.bankcity_Code,'000000,000000') as bankcity_Code
from PLATFORM_ABBOTT.dbo.xml_Vendors as xv
    left join cityInfo as city1
        on xv.bankCity = city1.spk_name1
    left join cityInfo as city2
        on xv.bankCity = city2.spk_name2
    left join cityInfo as city3
        on xv.bankCity = city3.spk_name3
    left join cityInfo as city4
        on xv.bankCity = city4.spk_name4
),
otfg2_process as (
	select
		 ProcInstId
		,ac_status
		,approvalTime
		,rn
	from (
		select 
			 ProcInstId
			,ac_status
			,approvalTime
			,row_number () over (partition by Procinstid order by approvalTime desc) as rn
		from xml_20
		)A
	where rn = 1
),
ApplyDeptNameProcess as (
select 
	 tsi.ProcInstId
	,b.applicantDeptId
	,c.ac_status
	,otr.res_name
from PLATFORM_ABBOTT.dbo.ODS_AUTO_BIZ_T_SupplierApplication_Info tsi
left join xml_Vendors b
on tsi.ProcInstId = b.ProcInstId
left join otfg2_process c
on tsi.ProcInstId = c.ProcInstId
left join PLATFORM_ABBOTT.dbo.ODS_T_RESOURCE otr
on b.applicantDeptId = otr.res_code
COLLATE SQL_Latin1_General_CP1_CI_AS
),
vendor_code_process as (
select 
	 a.VENDOR as vendorNumber
	,a.VCMPNY as company_Value
	,a.VNDNAM as supplierCNName
	,vt.VendorCode as VendorCode
from PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM as a
left join PLATFORM_ABBOTT.dbo.Vendor_Tmp vt
on a.VENDOR = vt.VENDOR 
and a.VCMPNY = vt.VCMPNY
union all
select 
	 a.VNDERX as vendorNumber
	,a.VMCMPY as company_Value
	,a.VEXTNM as supplierCNName
	,vt.VendorCode as VendorCode
from PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM as a
left join PLATFORM_ABBOTT.dbo.Vendor_Tmp vt
on a.VNDERX = vt.VENDOR 
and a.VMCMPY = vt.VCMPNY 
),
vendor_code as (
	select 
		 distinct 
		*
	from vendor_code_process
)
select * into #VendorApplications_tem1 
from (
	select 
	newid() AS Id,--自动生成的uuid
	tsi.ProcInstId,
	case when coalesce(Reactive,'null') <>'true' then '1'
	when Reactive ='true' then '3' end AS ApplicationType,
	of7a.SerialNumber AS ApplicationCode,
	COALESCE (vc.VendorCode,of7a.SerialNumber) as VendorCode,--基于M01中的清洗结果可得知供应商主数据中的某一条记录，其对应的可能多个BPCS_AVM内供应商
	--1.对于新建/激活申请单，根据AVM/PMFVM及AUTO_BIZ_T_SupplierApplication_Info表中VENDOR=vendorNumber+VCMPNY=company_Value+VEXTNM=supplierCNName(或AVM.VNDNAM=SupplierCNName)完全匹配成功作为条件，定位出单据对应的BPCS供应商，并将这些供应商匹配出来的Vendors的VendorCode填入
	--2.对于变更申请单，根据AVM/PMFVM及AUTO_BIZ_T_HcpLevelApplication_info表中VENDOR=supplierCode且{VEXTNM=supplierCNName或AVM.VNDNAM=SupplierCNName或AVM.VMXCRT=CertificatesNum}匹配成功作为条件，定位出单据对应的BPCS供应商，并将这些供应商匹配出来的Vendors的VendorCode填入
	--对于无法匹配至已有供应商主数据的单据，该值不能为空，建议以ApplicationCode直接填入"
	'' AS OpenId,--留空
	'' AS UnionId,--留空
	tsi.supplierPhone AS HandPhone,--
	case when tsi.vendorType='NHIV' then '1'
	when tsi.vendorType='NLIV' then '2'
	when tsi.vendorType='NH' then '3'
	when tsi.vendorType='NL' then '4'
	when tsi.vendorType='NT' then 
		case when XmlContent .value('(/root/SupplierApplication_applicantBaseInfoBlock_MainStore/supplierHCPType)[1]', 'nvarchar(1)')=1 then '1'
		when XmlContent .value('(/root/SupplierApplication_applicantBaseInfoBlock_MainStore/supplierHCPType)[1]', 'nvarchar(1)')=2 then '3'
		when XmlContent .value('(/root/SupplierApplication_applicantBaseInfoBlock_MainStore/supplierHCPType)[1]', 'nvarchar(1)')=3 then '4'
		when XmlContent .value('(/root/SupplierApplication_applicantBaseInfoBlock_MainStore/supplierHCPType)[1]', 'nvarchar(1)')=4 then '2'
		end
		end
	AS VendorType,--对于来自AUTO_BIZ_T_SupplierApplication_Info的数据：
	--NHIV->1
	--NLIV->2
	--NH->3
	--NL->4
	--NT->需要根据实际情况判断：
	--以申请单ProcInstId查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到标记为supplierHCPType的值，1则填写为1；2则填写为3；3则填写为4；4则填写为2
	--对于来自AUTO_BIZ_T_HcpLevelApplication_info的数据：
	--默认填写为1(即讲者)"
	case when processStatus=N'N/A(不迁移草稿)'then N'草稿' 
	 	 when processStatus=N'N/A(原BPM若未审批完，则退回到重新发起状态，让用户重新提交申请)'then N'审批中'
	 	 when processStatus in (N'已完成', N'终止', N'终止(系统)')then N'已通过'
	 	 when processStatus=N'审批中' then N'已退回'
	 	 when processStatus in (N'发起人终止', N'重发起') and adnp.ac_status in(N'退回', N'撤回') then N'已退回'
	 	 when processStatus in (N'发起人终止', N'重发起') and adnp.ac_status =N'拒绝' then N'已拒绝'
	 	 when processStatus in (N'发起人终止', N'重发起') and adnp.ac_status =N'作废' then N'已作废'
	 	 end AS Status,--参考下方附录状态mapping表
	XmlContent .value('(/root/SupplierApplication_applicantInfoBlock_MainStore/applicantEmpId)[1]', 'nvarchar(50)') 
	AS ApplyUserId,--基于申请单的ProcInstId查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到对应值，并以该ID匹配员工主数据
	--applicantEmpId AS ,--变更申请的申请人单独存在了表内，也可直接用xml读取出的申请人，则不需要单独处理本行逻辑
	XmlContent .value('(/root/SupplierApplication_applicantInfoBlock_MainStore/applicantDeptId)[1]', 'nvarchar(50)')
	AS ApplyUserBu,--基于申请单的ProcInstId查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到对应值，并以该ID匹配组织主数据
	of7a.applicationDate AS ApplyTime,--
	'0' AS VerificationStatus,--默认为0
	'' AS BpcsId,--默认为空
	c.AriseCode AS EpdId,--基于单据ProcInstId匹配T_SupplierAriseCode.ProcInstid
	DoctorsId AS MndId,--
	'' AS VendorId,--默认为空
	sd.spk_code AS ApsPorperty,--基于填入的res_Code定位对应的资源编码，从而匹配至NexBPM内的字典编码，填写为形如"APS-001"的Code
	'' AS LastVerifyStartTime,--默认为空
	'' AS LastVerifyEndTime,--默认为空
	CertificateNo AS CertificateCode,--
	sd1.spk_code  AS SPLevel,--基于级别名称找回对应的级别编码，若编码无法匹配至现有级别，需要在PP字典增加对应值但需要标记为"停用"(或者与开发确认此处能否填写为value)
	'' AS AcademicLevel,--默认为空
	cast('' as nvarchar(50)) AS AcademicPosition,--
	bankName AS BankCode,--
	LTRIM(RTRIM(
		REPLACE( -- remove /
			REPLACE(
		 		REPLACE(
		 			REPLACE(
						REPLACE(LEFT(bankNumber, 
									CASE 
										WHEN CHARINDEX('SwiftCode', bankNumber) > 0 THEN CHARINDEX('SwiftCode', bankNumber) - 1
										WHEN CHARINDEX('Swift Code', bankNumber) > 0 THEN CHARINDEX('Swift Code', bankNumber) - 1
										WHEN CHARINDEX('Swift', bankNumber) > 0 THEN CHARINDEX('Swift', bankNumber) - 1
										ELSE LEN(bankNumber)
										END),
											'(', ''), -- Remove left parenthesis
												')', '')  -- Remove right parenthesis
													,' ','')
														,'ACCOUNT:','')
																	,'/',''))
			) as BankCardNo,
	bc.bankcity_Code AS BankCity,--基于申请单的ProcInstId查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到对应值，根据这个值匹配PP中的市级主数据得到银行所在省市信息(匹配时可以尝试以原值或原值加上"市"后匹配)，如果匹配失败则留空
	bankLinkNumber AS BankNo,--
	--ReciveBankNumber AS ,--
	'{}' AS ExtraProperties,--默认填写为"{}"
	'' AS ConcurrencyStamp,--?
	of7a.applicationDate AS CreationTime,--填充为ApplyTime即可
	XmlContent .value('(/root/SupplierApplication_applicantInfoBlock_MainStore/applicantEmpId)[1]', 'nvarchar(50)')
	 AS CreatorId,--填充为ApplyUserId即可
	'' AS LastModificationTime,--默认为空
	'' AS LastModifierId,--默认为空
	'0' AS IsDeleted,--默认为0
	'' AS DeleterId,--默认为空
	'' AS DeletionTime,--默认为空
	'0' AS BAuth,--默认为0
	'********-0000-0000-0000-************' AS UserId,--默认为"********-0000-0000-0000-************"
	'' AS PTId,--默认为空(历史申请单内数据均为手动填写，没有必要匹配回主数据ID)
	'' AS StandardHosDepId,--默认为空(历史申请单内数据均为手动填写，没有必要匹配回主数据ID)
	'' AS HospitalId,--默认为空(历史申请单内数据均为手动填写，没有必要匹配回主数据ID)
	belongDivision AS HosDepartment,--
	'' AS BankCardImg,--默认为空
	x17_A.up_Id AS AttachmentInformation,--以ProcInstId查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到AttachmentInfoGrid下所有的up_Id，并按照up_FileName不包含文字"身份证"且不包含文字"DPS"的条件筛选出所有非身份证或DPS相关支持文件ID，填入匹配出的NexBPM支持文件ID
	'1' AS BImproved,--默认为1
	'0' AS DraftVersion,--默认为0
	'0' AS SignedStatus,--默认为0
	'' AS SignedVersion,--默认为空
	x17.up_id  AS DPSCheck,--基于单据ProcInstId查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到AttachmentInfoGrid下所有的up_Id，并按照up_FileName包含文字""DPS""的条件筛选出DPS相关支持文件ID，填入匹配出的NexBPM支持文件ID；
	--根据观察目前有一些DPS文件名未包含""DPS""，因此还需加入额外条件：基于xml审批记录(approvalHistoryGrid)中""IFODPSCheck""节点的""approvalPersonEmpId""，查询附件中由该人上传的文件(Emp_ID与前一个id相同)，也将该文件视为DPS Check附件"
	belongHospital AS HospitalName,--
	positionaltitles AS PTName,--
	belongDivision AS StandardHosDepName,--
	'' AS EpdHospitalId,--默认为空
	'' AS MobileEncrypt,--默认为空
	--ModifyInfo/OldData 
	cast('' as nvarchar(50)) AS UpdatePreJson,--仅对于变更及激活申请需要找回该值，且仅有讲者供应商会有这两种申请。需要基于单据ProcInstId查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到ModifyInfo，基于分号切分后识别出什么信息发生过修改，及修改前的值是什么。这一板块的信息先基于单据内信息填入Json文件内，对于发生过修改的值，将原值替换入Json内的对应值，以便界面显示时可以识别出修改的内容。该版块格式及对应的字段参考下方示例
	cast('' as nvarchar(50)) AS PushVeevaResp,--Veeva验回数据才有该信息，因此与现在一致即可，即留空
	REPLACE(
		REPLACE
			(REPLACE
				(REPLACE
					(REPLACE
						(REPLACE
							( RIGHT(bankNumber, 
								CASE 
									WHEN CHARINDEX('Swift', bankNumber) > 0 THEN LEN(bankNumber) - CHARINDEX('Swift', bankNumber)
									WHEN CHARINDEX('SwiftCode', bankNumber) > 0 THEN LEN(bankNumber) - CHARINDEX('SwiftCode', bankNumber)
									WHEN CHARINDEX('Swift Code', bankNumber) > 0 THEN LEN(bankNumber) - CHARINDEX('Swift Code', bankNumber)
--		                            	ELSE LEN(a.VLDRM2)
		            				END)
		        	    		,' ','')
		    	    		,':','')
			 			,')','')
					,'wiftcode','')
				,'wift','')
			,'-','')as BankSwiftCode,--参考M01-01的Swift Code抽取逻辑，更新该字段及[BankCardNo]
	'0' AS IsAcademician,--与现在一致即可，即默认为0
	cast('' as nvarchar(50)) AS TransfereeId,--单据转办人，历史数据无法查询到转办记录，与现在一致留空
	cast('' as nvarchar(50)) AS TransfereeName,--单据转办人，历史数据无法查询到转办记录，与现在一致留空
	SpeakInfo AS FormerBPMAcademicPosition,--原[AcademicPosition]逻辑修改为当前字段，由于[AcademicPosition]需要以Json格式存储，历史数据无法实现切分因此请直接留空
	cast('' as nvarchar(50)) AS HCPType,--Veeva验回数据才有该信息，因此与现在一致即可，即留空
	cast('' as nvarchar(50)) AS RelationType,--Veeva验回数据才有该信息，因此与现在一致即可，即留空
	coalesce(spk_1.spk_NexBPMCode,spk_2.spk_NexBPMCode) as ApplyBuId,--以该ID匹配至组织主数据后得到对应的BU层级组织ID
	iif(otr.Res_Name is not null, otr.Res_Name 
		,iif(tsi.applicantDept_Text =N'S_E2_曹颖' or tsi.applicantDept_Text ='S_West', 'EPD'
			,iif(tsi.applicantDept_Text like '%-%', substring(tsi.applicantDept_Text, 1, charindex('-', tsi.applicantDept_Text) - 1 ), tsi.applicantDept_Text))) as ApplyBuName,
	adnp.res_name AS ApplyDeptName,
	tsi.applicantDept_Text as ApplyUserBuName,
	of7a.applicantEmpName as ApplyUserName
	from PLATFORM_ABBOTT.dbo.ODS_AUTO_BIZ_T_SupplierApplication_Info tsi
	left join vendor_code as vc
	on cast(tsi.vendorNumber as nvarchar(50)) = cast(vc.vendorNumber as nvarchar(50))
	and tsi.company_Value = vc.company_Value
	and tsi.supplierCNName = vc.supplierCNName
	left join PLATFORM_ABBOTT.dbo.ODS_Form_7a708c9568fb444a884eb5eca658975f of7a
	on tsi.ProcInstId =of7a.ProcInstId 
	LEFT JOIN PLATFORM_ABBOTT.dbo.ODS_T_SupplierAriseCode C
	ON tsi.ProcInstId=c.ProcInstId
	join PLATFORM_ABBOTT.dbo.ODS_T_FORMINSTANCE_GLOBAL b
	on tsi.ProcInstId =b.ProcInstId 
	left join (select * from PLATFORM_ABBOTT.dbo.spk_dictionary where spk_BPMCode <> null or spk_BPMCode<>'')  sd
	on tsi.ASN_Value=sd.spk_BPMCode
	left join (select * from PLATFORM_ABBOTT.dbo.spk_dictionary where spk_BPMCode <> null or spk_BPMCode<>'') sd1
	on tsi.TIER=sd1.spk_Name
	left join PLATFORM_ABBOTT.dbo.spk_organizationalmasterData spk_1
	on tsi.BUId = spk_1.spk_BPMCode
	left join PLATFORM_ABBOTT.dbo.spk_organizationalmasterData spk_2
	on iif(tsi.applicantDept_Text =N'S_E2_曹颖' or tsi.applicantDept_Text ='S_West', 'EPD',iif(tsi.applicantDept_Text like '%-%', substring(tsi.applicantDept_Text, 1, charindex('-', tsi.applicantDept_Text) - 1 ), tsi.applicantDept_Text))  = spk_2.spk_Name
	and tsi.BUID is null
	and spk_2.spk_organizationType = 'BU'
	left join PLATFORM_ABBOTT.dbo.ODS_T_RESOURCE otr
	on tsi.BUId = otr.Res_Code
	left join ApplyDeptNameProcess adnp
	on tsi.ProcInstId = adnp.ProcInstId
	left join bankcity_Cd bc
	on tsi.ProcInstId = bc.ProcInstId
	left join (SELECT 
		    az.ProcInstId,
		    d.VCMPNY,d.VENDOR,
		    STRING_AGG(up_Id, ', ') WITHIN GROUP (ORDER BY up_Id) AS up_Id
		FROM 
		    (
		    select ProcInstId,
				up_Id,
				File_Name,
				Emp_ID 
			from xml_17 B
		join PLATFORM_ABBOTT.dbo.ODS_T_METAFILE tm
		on B.up_Id=tm.file_id)az
		join #upid d
		on az.ProcInstId=d.ProcInstId
		where File_Name not like N'%身份证%' and  File_Name not like N'%DPS%'
		GROUP BY  az.ProcInstId,d.VCMPNY,d.VENDOR)x17_A
		on tsi.ProcInstId =x17_A.ProcInstId
	left join (SELECT 
		    az.ProcInstId,
		    d.VCMPNY,d.VENDOR,
		    STRING_AGG(up_Id, ', ') WITHIN GROUP (ORDER BY up_Id) AS up_Id
		FROM 
		    (
		    select b.ProcInstId,
				up_Id,
				File_Name,
				Emp_ID,
				case when Emp_ID=approvalPersonEmpId then 1 else 0 end approvalPersonEmpId
			from xml_17 B
		join PLATFORM_ABBOTT.dbo.ODS_T_METAFILE tm
		on B.up_Id=tm.file_id
		join (select *,ROW_NUMBER () over(PARTITION by ProcInstId order by approvalTime desc) rn  from xml_20 where approvalLevel='IFODPSCheck' ) c
		on c.ProcInstId=b.ProcInstId and  c.rn=1
		)az
		join #upid d
		on az.ProcInstId=d.ProcInstId
		where File_Name like N'%DPS%' or approvalPersonEmpId=1
		GROUP BY  az.ProcInstId,d.VCMPNY,d.VENDOR
		)x17
		on tsi.ProcInstId =x17.ProcInstId
	union ALL 
	select 
	newid() AS Id,--自动生成的uuid
	tsi.ProcInstId,
	'2' AS ApplicationType,
	of7a.SerialNumber AS ApplicationCode,
	COALESCE (vc.VendorCode,of7a.SerialNumber) as VendorCode,--基于M01中的清洗结果可得知供应商主数据中的某一条记录，其对应的可能多个BPCS_AVM内供应商
	--1.对于新建/激活申请单，根据AVM/PMFVM及AUTO_BIZ_T_SupplierApplication_Info表中VENDOR=vendorNumber+VCMPNY=company_Value+VEXTNM=supplierCNName(或AVM.VNDNAM=SupplierCNName)完全匹配成功作为条件，定位出单据对应的BPCS供应商，并将这些供应商匹配出来的Vendors的VendorCode填入
	--2.对于变更申请单，根据AVM/PMFVM及AUTO_BIZ_T_HcpLevelApplication_info表中VENDOR=supplierCode且{VEXTNM=supplierCNName或AVM.VNDNAM=SupplierCNName或AVM.VMXCRT=CertificatesNum}匹配成功作为条件，定位出单据对应的BPCS供应商，并将这些供应商匹配出来的Vendors的VendorCode填入
	--对于无法匹配至已有供应商主数据的单据，该值不能为空，建议以ApplicationCode直接填入"
	'' AS OpenId,--留空
	'' AS UnionId,--留空
	tsi.Tel AS HandPhone,
	'1'AS VendorType,
	case when processStatus=N'N/A(不迁移草稿)'then N'草稿' 
	 when processStatus=N'N/A(原BPM若未审批完，则退回到重新发起状态，让用户重新提交申请)'then N'审批中'
	 when processStatus = N'已完成' then N'已通过'
	 when processStatus in ('ManagerReview', 'MKTReview', N'财务供应商预审') then N'已退回'
	 when processStatus in (N'发起人终止', N'重发起') and adnp.ac_status in(N'退回', N'撤回') then N'已退回'
	 when processStatus in (N'发起人终止', N'重发起') and adnp.ac_status =N'拒绝' then N'已拒绝'
	 when processStatus in (N'发起人终止', N'重发起') and adnp.ac_status =N'作废' then N'已作废'
	 end AS Status,--参考下方附录状态mapping表
	XmlContent.value('(/root/SupplierApplication_applicantInfoBlock_MainStore/applicantEmpId)[1]', 'nvarchar(50)') 
	 AS ApplyUserId,--基于申请单的ProcInstId查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到对应值，并以该ID匹配员工主数据
	--applicantEmpId AS ,--变更申请的申请人单独存在了表内，也可直接用xml读取出的申请人，则不需要单独处理本行逻辑
	XmlContent.value('(/root/SupplierApplication_applicantInfoBlock_MainStore/applicantDeptId)[1]', 'nvarchar(50)')
	AS ApplyUserBu,--基于申请单的ProcInstId查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到对应值，并以该ID匹配组织主数据
	of7a.applicationDate AS ApplyTime,--
	'0' AS VerificationStatus,--默认为0
	'' AS BpcsId,--默认为空
	c.AriseCode AS EpdId,--基于单据ProcInstId匹配T_SupplierAriseCode.ProcInstid
	'' AS MndId,
	'' AS VendorId,--默认为空
	'' AS ApsPorperty,--基于填入的res_Code定位对应的资源编码，从而匹配至NexBPM内的字典编码，填写为形如"APS-001"的Code
	'' AS LastVerifyStartTime,--默认为空
	'' AS LastVerifyEndTime,--默认为空
	CerCode AS CertificateCode,--
	sd1.spk_code  AS SPLevel,--基于级别名称找回对应的级别编码，若编码无法匹配至现有级别，需要在PP字典增加对应值但需要标记为"停用"(或者与开发确认此处能否填写为value)
	'' AS AcademicLevel,--默认为空
	cast('' as nvarchar(50)) AS AcademicPosition,
	bankName AS BankCode,
	LTRIM(RTRIM(
		REPLACE( -- remove /
			REPLACE(
				REPLACE(
		 			REPLACE(
						REPLACE(LEFT(bankNumber, 
									CASE 
										WHEN CHARINDEX('SwiftCode', bankNumber) > 0 THEN CHARINDEX('SwiftCode', bankNumber) - 1
										WHEN CHARINDEX('Swift Code', bankNumber) > 0 THEN CHARINDEX('Swift Code', bankNumber) - 1
										WHEN CHARINDEX('Swift', bankNumber) > 0 THEN CHARINDEX('Swift', bankNumber) - 1
										ELSE LEN(bankNumber)
										END),
											'(', ''), -- Remove left parenthesis
													  ')', '')  -- Remove right parenthesis
															,' ','')
																,'ACCOUNT:','')
																			,'/',''))
			) as BankCardNo,
	bc.bankcity_Code AS BankCity,--基于申请单的ProcInstId查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到对应值，根据这个值匹配PP中的市级主数据得到银行所在省市信息(匹配时可以尝试以原值或原值加上"市"后匹配)，如果匹配失败则留空
	ReciveBankNumber AS BankNo,
	'{}' AS ExtraProperties,--默认填写为"{}"
	'' AS ConcurrencyStamp,--?
	of7a.applicationDate AS CreationTime,--填充为ApplyTime即可
	XmlContent.value('(/root/SupplierApplication_applicantInfoBlock_MainStore/applicantEmpId)[1]', 'nvarchar(50)')
	AS CreatorId,--填充为ApplyUserId即可
	'' AS LastModificationTime,--默认为空
	'' AS LastModifierId,--默认为空
	'0' AS IsDeleted,--默认为0
	'' AS DeleterId,--默认为空
	'' AS DeletionTime,--默认为空
	'0' AS BAuth,--默认为0
	'********-0000-0000-0000-************' AS UserId,--默认为"********-0000-0000-0000-************"
	'' AS PTId,--默认为空(历史申请单内数据均为手动填写，没有必要匹配回主数据ID)
	'' AS StandardHosDepId,--默认为空(历史申请单内数据均为手动填写，没有必要匹配回主数据ID)
	'' AS HospitalId,--默认为空(历史申请单内数据均为手动填写，没有必要匹配回主数据ID)
	Department AS HosDepartment,--
	'' AS BankCardImg,--默认为空
	x17_A1.up_id AS AttachmentInformation,--以ProcInstId查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到AttachmentInfoGrid下所有的up_Id，并按照up_FileName不包含文字"身份证"且不包含文字"DPS"的条件筛选出所有非身份证或DPS相关支持文件ID，填入匹配出的NexBPM支持文件ID
	'1' AS BImproved,--默认为1
	'0' AS DraftVersion,--默认为0
	'0' AS SignedStatus,--默认为0
	'' AS SignedVersion,--默认为空
	x17_1.up_id AS DPSCheck,--基于单据ProcInstId查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到AttachmentInfoGrid下所有的up_Id，并按照up_FileName包含文字""DPS""的条件筛选出DPS相关支持文件ID，填入匹配出的NexBPM支持文件ID；
	--根据观察目前有一些DPS文件名未包含""DPS""，因此还需加入额外条件：基于xml审批记录(approvalHistoryGrid)中""IFODPSCheck""节点的""approvalPersonEmpId""，查询附件中由该人上传的文件(Emp_ID与前一个id相同)，也将该文件视为DPS Check附件"
	Hospital AS HospitalName,--
	Titles AS PTName,--
	Department AS StandardHosDepName,--
	'' AS EpdHospitalId,--默认为空
	'' AS MobileEncrypt,--默认为空
	--ModifyInfo/OldData 
	cast('' as nvarchar(50)) AS UpdatePreJson,--仅对于变更及激活申请需要找回该值，且仅有讲者供应商会有这两种申请。需要基于单据ProcInstId查询T_FORMINSTANCE_GLOBAL.ProcInstid，在查询出的XmlContent中找到ModifyInfo，基于分号切分后识别出什么信息发生过修改，及修改前的值是什么。这一板块的信息先基于单据内信息填入Json文件内，对于发生过修改的值，将原值替换入Json内的对应值，以便界面显示时可以识别出修改的内容。该版块格式及对应的字段参考下方示例
	cast('' as nvarchar(50)) AS PushVeevaResp,--Veeva验回数据才有该信息，因此与现在一致即可，即留空
	REPLACE(
		REPLACE
			(REPLACE
				(REPLACE
					(REPLACE
						(REPLACE
							( RIGHT(bankNumber, 
								CASE 
									WHEN CHARINDEX('Swift', bankNumber) > 0 THEN LEN(bankNumber) - CHARINDEX('Swift', bankNumber)
									WHEN CHARINDEX('SwiftCode', bankNumber) > 0 THEN LEN(bankNumber) - CHARINDEX('SwiftCode', bankNumber)
									WHEN CHARINDEX('Swift Code', bankNumber) > 0 THEN LEN(bankNumber) - CHARINDEX('Swift Code', bankNumber)
--		                            	ELSE LEN(a.VLDRM2)
		            				END)
		        	    		,' ','')
		    	    		,':','')
			 			,')','')
					,'wiftcode','')
				,'wift','')
			,'-','')as BankSwiftCode,--参考M01-01的Swift Code抽取逻辑，更新该字段及[BankCardNo]
	'0' AS IsAcademician,--与现在一致即可，即默认为0
	cast('' as nvarchar(50)) AS TransfereeId,--单据转办人，历史数据无法查询到转办记录，与现在一致留空
	cast('' as nvarchar(50)) AS TransfereeName,--单据转办人，历史数据无法查询到转办记录，与现在一致留空
	JobMsg AS FormerBPMAcademicPosition,--原[AcademicPosition]逻辑修改为当前字段，由于[AcademicPosition]需要以Json格式存储，历史数据无法实现切分因此请直接留空
	cast('' as nvarchar(50)) AS HCPType,--Veeva验回数据才有该信息，因此与现在一致即可，即留空
	cast('' as nvarchar(50)) AS RelationType,--Veeva验回数据才有该信息，因此与现在一致即可，即留空
	spk.spk_NexBPMCode as ApplyBuId,--以该ID匹配至组织主数据后得到对应的BU层级组织ID
	otr.Res_Name as ApplyBuName,--以该ID匹配至组织主数据后得到对应的BU层级组织ID
	adnp.res_name AS ApplyDeptName,
	of7a.applicantDept as ApplyUserBuName,
	of7a.applicantEmpName as ApplyUserName
	from PLATFORM_ABBOTT.dbo.ODS_AUTO_BIZ_T_HcpLevelApplication_info tsi
	left join vendor_code as vc
	on cast(tsi.supplierCode as nvarchar(50)) = cast(vc.vendorNumber as nvarchar(50))
	and tsi.supplierCNName = vc.supplierCNName
	left join PLATFORM_ABBOTT.dbo.ODS_Form_663dd63299be45d69dd8f853d0a4b445 of7a
	on tsi.ProcInstId =of7a.ProcInstId 
	LEFT JOIN PLATFORM_ABBOTT.dbo.ODS_T_SupplierAriseCode C
	ON tsi.ProcInstId=c.ProcInstId
	join PLATFORM_ABBOTT.dbo.ODS_T_FORMINSTANCE_GLOBAL b
	on tsi.ProcInstId =b.ProcInstId 
	left join PLATFORM_ABBOTT.dbo.spk_dictionary sd1
	on tsi.HCPLevel=sd1.spk_Name
	left join PLATFORM_ABBOTT.dbo.spk_organizationalmasterData spk
	on tsi.applicantbuid = spk.spk_bpmcode
	left join PLATFORM_ABBOTT.dbo.ODS_T_RESOURCE otr
	on tsi.applicantbuid = otr.Res_Code
	left join ApplyDeptNameProcess adnp
	on tsi.ProcInstId = adnp.ProcInstId
	left join bankcity_Cd bc
	on tsi.ProcInstId = bc.ProcInstId
		left join (SELECT 
		    az.ProcInstId,
		    d.VCMPNY,d.VENDOR,
		    STRING_AGG(up_Id, ', ') WITHIN GROUP (ORDER BY up_Id) AS up_Id
		FROM 
		    (
		    select ProcInstId,
				up_Id,
				File_Name,
				Emp_ID 
			from xml_17 B
		join PLATFORM_ABBOTT.dbo.ODS_T_METAFILE tm
		on B.up_Id=tm.file_id)az
		join #upid d
		on az.ProcInstId=d.ProcInstId
		where File_Name not like N'%身份证%' and  File_Name not like N'%DPS%'
		GROUP BY  az.ProcInstId,d.VCMPNY,d.VENDOR)x17_A1
		on tsi.ProcInstId =x17_A1.ProcInstId
	left join (SELECT 
		    az.ProcInstId,
		    d.VCMPNY,d.VENDOR,
		    STRING_AGG(up_Id, ', ') WITHIN GROUP (ORDER BY up_Id) AS up_Id
		FROM 
		    (
		    select b.ProcInstId,
				up_Id,
				File_Name,
				Emp_ID,
				case when Emp_ID=approvalPersonEmpId then 1 else 0 end approvalPersonEmpId
			from xml_17 B
		join PLATFORM_ABBOTT.dbo.ODS_T_METAFILE tm
		on B.up_Id=tm.file_id
		join (select *,ROW_NUMBER () over(PARTITION by ProcInstId order by approvalTime desc) rn  from xml_20 where approvalLevel='IFODPSCheck' ) c
		on c.ProcInstId=b.ProcInstId and  c.rn=1
		)az
		join #upid d
		on az.ProcInstId=d.ProcInstId
		where File_Name like N'%DPS%' or approvalPersonEmpId=1
		GROUP BY  az.ProcInstId,d.VCMPNY,d.VENDOR
		) x17_1
		on tsi.ProcInstId=x17_1.ProcInstId
)A
--更新up_id  AttachmentInformation DPSCheck
--update a set a.AttachmentInformation=b.up_Id,a.DPSCheck=b.up_Id from #VendorApplications_tem1 a
--join (SELECT 
--    ProcInstId,
--    STRING_AGG(up_Id, ', ') WITHIN GROUP (ORDER BY up_Id) AS up_Id
--FROM 
--    (select ProcInstId,
--		up_Id,
--		File_Name,
--		Emp_ID 
--	from xml_17 B
--		join PLATFORM_ABBOTT.dbo.ODS_T_METAFILE tm
--		on B.up_Id=tm.file_id)YourTable
--		where File_Name  like N'%DPS%'
--		GROUP BY  ProcInstId
--		 )b
--on a.ProcInstId=b.ProcInstId

--写入目标表
IF OBJECT_ID(N'PLATFORM_ABBOTT.dbo.VendorApplications_Tmp', N'U') IS NOT NULL
BEGIN
	update a 
	set a.ProcInstId            = b.ProcInstId
        ,a.ApplicationType    = b.ApplicationType
        ,a.ApplicationCode      = b.ApplicationCode
        ,a.VendorCode           = b.VendorCode
        ,a.OpenId               = b.OpenId
        ,a.UnionId              = b.UnionId
        ,a.HandPhone            = b.HandPhone
        ,a.VendorType           = b.VendorType
        ,a.Status               = b.Status
        ,a.ApplyUserId          = b.ApplyUserId
        ,a.ApplyUserBu          = b.ApplyUserBu
        ,a.ApplyTime            = b.ApplyTime
        ,a.VerificationStatus   = b.VerificationStatus
        ,a.BpcsId               = b.BpcsId
        ,a.EpdId          = b.EpdId
        ,a.MndId                = b.MndId
        ,a.VendorId  = b.VendorId
        ,a.ApsPorperty          = b.ApsPorperty
        ,a.LastVerifyStartTime  = b.LastVerifyStartTime
        ,a.LastVerifyEndTime    = b.LastVerifyEndTime
        ,a.CertificateCode      = b.CertificateCode
        ,a.SPLevel              = b.SPLevel
        ,a.AcademicLevel        = b.AcademicLevel
   ,a.AcademicPosition     = b.AcademicPosition
        ,a.BankCode             = b.BankCode
        ,a.BankCardNo           = b.BankCardNo
        ,a.BankCity             = b.BankCity
        ,a.BankNo               = b.BankNo
        ,a.ExtraProperties      = b.ExtraProperties
        ,a.ConcurrencyStamp     = b.ConcurrencyStamp
        ,a.CreationTime         = b.CreationTime
        ,a.CreatorId            = b.CreatorId
        ,a.LastModificationTime = b.LastModificationTime
        ,a.LastModifierId       = b.LastModifierId
        ,a.IsDeleted            = b.IsDeleted
        ,a.DeleterId            = b.DeleterId
        ,a.DeletionTime         = b.DeletionTime
        ,a.BAuth                = b.BAuth
        ,a.UserId               = b.UserId
        ,a.PTId                 = b.PTId
        ,a.StandardHosDepId     = b.StandardHosDepId
        ,a.HospitalId           = b.HospitalId
        ,a.HosDepartment        = b.HosDepartment
        ,a.BankCardImg          = b.BankCardImg
        ,a.AttachmentInformation= b.AttachmentInformation
        ,a.BImproved            = b.BImproved
        ,a.DraftVersion         = b.DraftVersion
        ,a.SignedStatus         = b.SignedStatus
        ,a.SignedVersion        = b.SignedVersion
        ,a.DPSCheck             = b.DPSCheck
        ,a.HospitalName         = b.HospitalName
        ,a.PTName               = b.PTName
        ,a.StandardHosDepName   = b.StandardHosDepName
        ,a.EpdHospitalId        = b.EpdHospitalId
        ,a.MobileEncrypt        = b.MobileEncrypt
        ,a.UpdatePreJson        = b.UpdatePreJson
		  ,a.PushVeevaResp        =b.PushVeevaResp
        ,a.BankSwiftCode        = b.BankSwiftCode
          ,a.IsAcademician        = b.IsAcademician
          ,a.TransfereeId         = b.TransfereeId
        	,a.TransfereeName       = b.TransfereeName
        	,a.FormerBPMAcademicPosition= b.FormerBPMAcademicPosition
        	,a.HCPType              = b.HCPType
        	,a.RelationType         = b.RelationType
        ,a.ApplyBuId            = b.ApplyBuId
        ,a.ApplyBuName          = b.ApplyBuName
        ,a.ApplyDeptName        = b.ApplyDeptName
        ,a.ApplyUserBuName      = b.ApplyUserBuName
        ,a.ApplyUserName        = b.ApplyUserName
    from PLATFORM_ABBOTT.dbo.VendorApplications_Tmp a 
    left join #VendorApplications_tem1 b on a.ProcInstId = b.ProcInstId 
    and a.ApplicationCode = b.ApplicationCode 
    
	insert into PLATFORM_ABBOTT.dbo.VendorApplications_Tmp
	select a.Id
           ,a.ProcInstId
           ,a.ApplicationType
           ,a.ApplicationCode
           ,a.VendorCode
           ,a.OpenId
           ,a.UnionId
           ,a.HandPhone
           ,a.VendorType
           ,a.Status
           ,a.ApplyUserId
           ,a.ApplyUserBu
           ,a.ApplyTime
           ,a.VerificationStatus
           ,a.BpcsId
           ,a.EpdId
           ,a.MndId
           ,a.VendorId
           ,a.ApsPorperty
           ,a.LastVerifyStartTime
           ,a.LastVerifyEndTime
           ,a.CertificateCode
           ,a.SPLevel
           ,a.AcademicLevel
           ,a.AcademicPosition
           ,a.BankCode
           ,a.BankCardNo
           ,a.BankCity
           ,a.BankNo
           ,a.ExtraProperties
           ,a.ConcurrencyStamp
           ,a.CreationTime
           ,a.CreatorId
           ,a.LastModificationTime
           ,a.LastModifierId
           ,a.IsDeleted
           ,a.DeleterId
           ,a.DeletionTime
           ,a.BAuth
   		   ,a.UserId
           ,a.PTId
           ,a.StandardHosDepId
           ,a.HospitalId
           ,a.HosDepartment
           ,a.BankCardImg
           ,a.AttachmentInformation
           ,a.BImproved
           ,a.DraftVersion
           ,a.SignedStatus
           ,a.SignedVersion
           ,a.DPSCheck
           ,a.HospitalName
           ,a.PTName
           ,a.StandardHosDepName
           ,a.EpdHospitalId
           ,a.MobileEncrypt
           ,a.UpdatePreJson
           ,a.PushVeevaResp
           ,a.BankSwiftCode
           ,a.IsAcademician
           ,a.TransfereeId
           ,a.TransfereeName
           ,a.FormerBPMAcademicPosition
           ,a.HCPType
           ,a.RelationType
           ,a.ApplyBuId
           ,a.ApplyBuName
           ,a.ApplyDeptName
           ,a.ApplyUserBuName
           ,a.ApplyUserName
    from #VendorApplications_tem1 a
    where not exists (select * from PLATFORM_ABBOTT.dbo.VendorApplications_Tmp where a.ProcInstId = ProcInstId 
    and a.ApplicationCode = ApplicationCode )

PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select * into PLATFORM_ABBOTT.dbo.VendorApplications_Tmp from #VendorApplications_tem1
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END


END;



