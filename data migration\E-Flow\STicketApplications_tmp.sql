CREATE PROCEDURE dbo.sp_STicketApplications
AS 
BEGIN
--
--CREATE TABLE dbo.ods_AUTO_BIZ_T_WholesalerVerificationApplication_Info (
--	ProcInstId int NULL,
--	applicantDeptId nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	productCbo_Value nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	expenseCategory_Value nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	BudgetNo nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	applicantDept_Text nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	productCbo_Text nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	totalmoney nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	Remark nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	serialNumber nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	Status nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	[money] nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	applicantLocationId nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	applicantEmpName nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	company_Text nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	applicationDate datetime NULL,
--	configmoney1 nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	configmoney2 nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	company_Value nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	GMApprovalStatus nvarchar(10) COLLATE Chinese_PRC_CI_AS NULL,
--	applicantBUId nvarchar(32) COLLATE Chinese_PRC_CI_AS NULL,
--	F_Code nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	CustomType_Text nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	CustomType_Value nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	CustomName nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	CustomNo nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	applicantCostConterId nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL
--);
--
--select * from ods_Form_4ad03a67d7e54490930fb50b4f5eaafb
--
--CREATE TABLE dbo.ods_Form_4ad03a67d7e54490930fb50b4f5eaafb (
--	ID nvarchar(50) COLLATE Chinese_PRC_CI_AS NOT NULL,
--	ProcInstId int NOT NULL,
--	applicantDept_Text nvarchar(100) COLLATE Chinese_PRC_CI_AS NULL,
--	applicantEmpName nvarchar(20) COLLATE Chinese_PRC_CI_AS NULL,
--	applicationDate nvarchar(20) COLLATE Chinese_PRC_CI_AS NULL,
--	BudgetNo nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	company_Text nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	CustomName nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	expenseCategory_Text nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	processStatus nvarchar(100) COLLATE Chinese_PRC_CI_AS NULL,
--	productCbo_Text nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	serialNumber nvarchar(20) COLLATE Chinese_PRC_CI_AS NULL,
--	totalmoney nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL
--);
--
---- PLATFORM_ABBOTT_PRD.dbo.AUTO_BIZ_T_WholesalerVerificationApplication_Info_childInfo definition
--
---- Drop table
--
---- DROP TABLE PLATFORM_ABBOTT_PRD.dbo.AUTO_BIZ_T_WholesalerVerificationApplication_Info_childInfo;
--
--CREATE TABLE dbo.ods_AUTO_BIZ_T_WholesalerVerificationApplication_Info_childInfo (
--	ProcInstId int NULL,
--	CostCenter nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	Store nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	Price nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	RMB nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	ExpensePropertyCode nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	Number nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	Content nvarchar(100) COLLATE Chinese_PRC_CI_AS NULL,
--	[No] nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	PredictDate datetime NULL,
--	City nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	ExpenseProperty nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	NoType nvarchar(10) COLLATE Chinese_PRC_CI_AS NULL,
--	RE_ID nvarchar(10) COLLATE Chinese_PRC_CI_AS NULL,
--	StoreId nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	cityCode nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	costCenterCode nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	Channel nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL
--);
--
--
--CREATE TABLE dbo.ods_T_ERS_CSSServiceHistory (
--	Id nvarchar(32) COLLATE Chinese_PRC_CI_AS NULL,
--	CSSNumber nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	[Method] nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	Context nvarchar(MAX) COLLATE Chinese_PRC_CI_AS NULL,
--	InsertDate datetime NULL,
--	Status int NULL,
--	ProcessingDate datetime NULL,
--	serialNumber nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	ErrorMsg nvarchar(MAX) COLLATE Chinese_PRC_CI_AS NULL
--);
--附件
IF OBJECT_ID(N'dbo.XML_upid', N'U') IS NOT NULL
BEGIN
	PRINT(N'已经初始化过'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select * into dbo.XML_upid 
	from (
		SELECT 
    ProcInstId,
    STRING_AGG(up_Id, ', ') WITHIN GROUP (ORDER BY up_Id) AS up_Id
FROM 
    (select ProcInstId,
		up_Id
	from (
	select ProcInstId,
	up_Id.value('.', 'nvarchar(100)') as up_Id 
	FROM dbo.ODS_T_FORMINSTANCE_GLOBAL
		CROSS APPLY XmlContent.nodes('/root/OthersAttachmentGrid/row/up_Id') AS XMLTable(up_Id)) B
) C
		GROUP BY  ProcInstId
) fg 
PRINT(N'初始化完成'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END;

SELECT 
newid() id,a.ProcInstId,a.serialNumber as ApplicationCode,
b.CSSNumber AS CssCode,--【CSS申请单号】根据serialNumber在[T_ERS_CSSServiceHistory]表查询得到，若查询不到，则保持为空
case when b.CSSNumber is not null or b.CSSNumber <> 'NULL' or b.CSSNumber <> '' 
then 'CSS' else 'NexBPM' end AS ApplicationType,--【申请单类型】若上一字段，CSSNumber不为空则填“CSS”，否则为“NexBPM”
case when processStatus=N'Expense循环审批中' or processStatus is null or processStatus=N'财务审核'  then 2
when processStatus=N'财务关闭' or  processStatus=N'完成' then 3
when processStatus=N'发起人终止'  then 6
when processStatus=N'终止' then 4
when processStatus=N'重发起' then 5 end 
AS Status,--【单据状态】（括号中为老BPM状态）
--"单据状态：
--1-草稿
--2-审批中
--3-审批通过：单据已经拿到PP更新状态，但未收到SOI成功接收
--4-审批拒绝
--5-审批退回
--6-发起人终止
--7-发起人撤回（暂时用不到该状态）
--"
--可以按照如下SQL查询到老BPM单据状态：
--SELECT processStatus FROM [dbo].[ods_AUTO_BIZ_T_WholesalerVerificationApplication_Info] a 
----LEFT JOIN [dbo].[ods_AUTO_BIZ_T_WholesalerVerificationApplication_Info_childInfo] d
----ON a.ProcInstId = d.ProcInstId
--left join [dbo].ods_Form_4ad03a67d7e54490930fb50b4f5eaafb c
--ON a.ProcInstId = c.ProcInstId
--WHERE MAIN.ProcInstId = '1774624'（示例）"
null AS VerificationStatus,--【核销状态】查询视图得到该Serialnumber下的所有明细行的已核销金额，若：
--1.主单核销金额>所有明细行的已核销金额>0，则为“部分核销”
--2.主单核销金额=所有明细行的已核销金额，则为“已核销”
--3.所有明细行的已核销金额=0，则为“未核销”"
a.applicationDate AS ApplyTime,--【申请时间】
XmlContent .value('(/root/WholesalerVerificationApplication_applicantInfoBlock_MainStore/applicantEmpId)[1]', 'nvarchar(100)') AS ApplyUserId,--【申请人ID】需要以bpm_id在PP查询得到nexBPM_id
a.applicantEmpName AS ApplyUser,--【申请人姓名】
a.applicantBUId as ApplyUserBUCode,--【申请人BUCode】按照BPMid在PP查询BU Code
a.applicantDeptId AS ApplyUserDeptId,--【申请部门ID】需要以bpm_id在PP查询得到nexBPM_id
a.applicantDept_Text AS ApplyUserDeptName,--【申请部门名称】
a.applicantCostConterId AS CostCenterId,--【成本中心ID】需要查询PP，将拿到的BPMid转换为nexBPMid
a.applicantCostConterId AS CostCenterCode,--【成本中心Code】需要以BPM编码查询PP，得到成本中心编码
a.applicantCostConterId AS CostCenter,--【成本中心名称】需要以BPM编码查询PP，得到成本中心资源名称
a.applicantBUId AS ApplyUserBuId,--【申请人BUid】需要以BPM编码查询PP，得到nexBPM的Buid
a.applicantBUId AS ApplyUserBuName,--【申请人BUid】需要以BPM编码查询PP，得到BU名称
a.BudgetNo AS BudgetId,--【主预算ID】将历史核销单的子预算编号BudgetNo在[dbo].[BdSubBudgets]中查询得到[MasterBudgetId]
a.BudgetNo AS BudgetCode,--【主预算Code】将历史核销单的子预算编号BudgetNo在[dbo].[BdSubBudgets]中查询得到[MasterBudgetId]，再根据该值，在[BdMasterBudgets]表得到主预算编号[Code]
a.BudgetNo AS SubBudgetId,--【预算ID】将历史核销单的子预算编号BudgetNo在[dbo].[BdSubBudgets]中查询得到子预算编号
a.BudgetNo AS SubBudgetCode,--【预算Code】
XmlContent .value('(/root/bugdetGridPanel/row/Desc)[1]', 'nvarchar(255)') as SubBudgetDesc,
XmlContent .value('(/root/WholesalerVerificationApplication_BudgetInfoBlock_MainStore/regionalId)[1]', 'nvarchar(255)') AS SubBudgetRegionId,--【预算大区id】根据Region查询到NexBPM的id
XmlContent .value('(/root/WholesalerVerificationApplication_BudgetInfoBlock_MainStore/regional)[1]', 'nvarchar(255)') AS SubBudgetRegion,--【预算大区】
a.BudgetNo AS BudgetOwnerId,--【预算负责人ID】根据历史核销单的子预算编号BudgetNo在[dbo].[BdSubBudgets]中查询得到子预算负责人ID
XmlContent .value('(/root/bugdetGridPanel/row/BudgetHolders)[1]', 'nvarchar(255)') AS BudgetOwner,--【预算负责人】根据procinstid查询到xml文件，取<BudgetHolders>
a.BudgetNo AS BudgetFile,--【预算文件】根据历史核销单的子预算编号BudgetNo在[dbo].[BdSubBudgets]中查询得到附件[AttachmentFile]
XmlContent .value('(/root/bugdetGridPanel/row/UsedMoney)[1]', 'nvarchar(255)') AS BudgetUsedAmount,--【预算已用金额】根据procinstid查询到xml文件，取<UsedMoney>
XmlContent .value('(/root/bugdetGridPanel/row/AvailableMoney)[1]', 'nvarchar(255)') AS BudgetAvailableAmount,--【预算可用金额】根据procinstid查询到xml文件，取<AvailableMoney>
XmlContent .value('(/root/bugdetGridPanel/row/BudgetMoney)[1]', 'nvarchar(255)') AS BudgetAmount,--【预算金额】根据procinstid查询到xml文件，取<BudgetMoney>
a.totalmoney AS TotalAmountRMB,--【费用总金额】
a.expenseCategory_Value AS ExpenseTypeId,--【消费大类ID】需要查询PP，将拿到的BPMid转换为nexBPMid
a.expenseCategory_Value AS ExpenseTypeName,--【消费大类名称】需要查询PP，将拿到的BPMid转换为名称
XmlContent .value('(/root/WholesalerVerificationApplication_BudgetInfoBlock_MainStore/productCbo_Value)[1]', 'nvarchar(255)') AS ProductId,--【产品ID】需要查询PP，将拿到的BPMid转换为nexBPMid
XmlContent .value('(/root/WholesalerVerificationApplication_BudgetInfoBlock_MainStore/productCbo_Text)[1]', 'nvarchar(255)') AS ProductName,--【产品】需要查询PP，将拿到的BPMid转换为产品名称
XmlContent .value('(/root/WholesalerVerificationApplication_BudgetInfoBlock_MainStore/productCbo_Value)[1]', 'nvarchar(255)') as ProductCode,
a.company_Text AS CompanyId,--【公司ID】需要查询PP，将拿到的公司名称得到nexBPM的id
a.company_Text AS CompanyCode,--【公司编码】需要查询PP，将拿到的公司名称得到公司编码
a.company_Text AS CompanyName,--【公司】
a.CustomNo AS ClientId,--【客户ID】按照原BPM的客户代码查询[ClientInfo]得到ID
a.CustomNo AS ClientCode,--【客户编号】
a.CustomName AS ClientName,--【客户名称】
a.CustomType_Text AS ClientTypeId,--【客户类型ID】按客户类型名称查询[ClientType]表得到id
a.CustomType_Text AS ClientType,--【客户类型】
upid.up_id AS Attachment,--附件
a.Remark AS Remark,--【备注】
null AS TransfereeId,--【转办人id】默认为空
null AS TransfereeName,--【转办人姓名】默认为空
'BPM Migration' AS DataSource,--【数据来源】固定为BPM Migration
null AS CreationTime,--【创建时间】默认为空
null AS CreatorId,--【创建人】默认为空
null AS LastModificationTime,--【最后修改时间】默认为空
null AS LastModifierId,--【最后修改人】默认为空
0 AS IsDeleted,--【是否删除】默认为0(BPM的删除功能是hard delete，已删除的不会有记录)
--0：未删除，1：已删除"
null AS DeleterId,--【删除时间】默认为空
null AS DeletionTime,--【删除人】默认为空
XmlContent .value('(/root/PRGridPanel/row/City)[1]', 'nvarchar(255)') AS CityId,--XmlContent（取该S单下第一行的城市）	【城市id】需要按照城市名+城市代码，在PP中查询得到id
XmlContent .value('(/root/PRGridPanel/row/cityCode)[1]', 'nvarchar(255)') AS CityCode,--XmlContent（取该S单下第一行的城市）	【城市代码】
XmlContent .value('(/root/PRGridPanel/row/City)[1]', 'nvarchar(255)') AS CityName,--XmlContent（取该S单下第一行的城市）	【城市名】
null AS Content,--NA	【核销内容】默认为空
cast(null as nvarchar(50))AS SettlementPeriodStart,--Content	【折扣结算开始日期】取最早的结算开始
cast(null as nvarchar(50))AS SettlementPeriodEnd--Content	【折扣结算结束日期】取最晚的结算结束
into #STicketApplications_tmp
from ods_AUTO_BIZ_T_WholesalerVerificationApplication_Info a
left join ods_T_ERS_CSSServiceHistory b 
on a.serialNumber=b.serialNumber
--LEFT JOIN [dbo].[ods_AUTO_BIZ_T_WholesalerVerificationApplication_Info_childInfo] d
--ON a.ProcInstId = d.ProcInstId
left join [dbo].ods_Form_4ad03a67d7e54490930fb50b4f5eaafb c
ON a.ProcInstId = c.ProcInstId
join  ODS_T_FORMINSTANCE_GLOBAL e
on a.ProcInstId=e.ProcInstId
left join xml_upid upid
on upid.ProcInstId=a.ProcInstId;

update a set a.SettlementPeriodStart=b.SettlementPeriodStart,a.SettlementPeriodEnd=b.SettlementPeriodEnd
from #STicketApplications_tmp a
join (
	select a.ProcInstId, LEFT(Content, CHARINDEX(',', Content) - 1)SettlementRegion ,
SUBSTRING(
    Content,
    CHARINDEX('：', Content) + 1,
    CHARINDEX('~', Content) - CHARINDEX('：', Content) - 1
)SettlementPeriodStart
,RIGHT(Content, LEN(Content) - CHARINDEX('~', Content)) SettlementPeriodEnd,a.serialNumber
from ods_AUTO_BIZ_T_WholesalerVerificationApplication_Info a 
join ods_AUTO_BIZ_T_WholesalerVerificationApplication_Info_childInfo b
on a.ProcInstId=b.ProcInstId
join dbo.ods_T_ERS_CSSServiceHistory c 
on a.serialNumber=c.serialNumber) b 
on a.ProcInstId=b.ProcInstId

select * from #STicketApplications_tmp

--drop table  STicketApplications_tmp

 IF OBJECT_ID(N'dbo.STicketApplications_tmp', N'U') IS NOT NULL
	BEGIN
		update a 
		set 
--	a.id					=		b.id
--	a.ProcInstId			=		b.ProcInstId
	a.ApplicationCode = b.ApplicationCode
	,a.CssCode				=	b.CssCode
	,a.ApplicationType		=	b.ApplicationType
	,a.Status				=	b.Status
	,a.ApplyTime				=	b.ApplyTime
	,a.ApplyUserId			=		b.ApplyUserId
	,a.ApplyUser				=	b.ApplyUser
	,a.ApplyUserDeptId		=			b.ApplyUserDeptId
	,a.ApplyUserDeptName		=			b.ApplyUserDeptName
	,a.CostCenterId			=		b.CostCenterId
	,a.CostCenterCode		=			b.CostCenterCode
	,a.CostCenter			=		b.CostCenter
	,a.ApplyUserBuId			=		b.ApplyUserBuId
	,a.ApplyUserBuName		=			b.ApplyUserBuName
	,a.BudgetId				=	b.BudgetId
	,a.BudgetCode			=		b.BudgetCode
	,a.SubBudgetId			=		b.SubBudgetId
	,a.SubBudgetCode			=		b.SubBudgetCode
	,a.SubBudgetRegionId		=			b.SubBudgetRegionId
	,a.SubBudgetRegion		=			b.SubBudgetRegion
	,a.BudgetOwnerId			=		b.BudgetOwnerId
	,a.BudgetOwner			=		b.BudgetOwner
	,a.BudgetFile			=		b.BudgetFile
	,a.BudgetUsedAmount		=			b.BudgetUsedAmount
	,a.BudgetAvailableAmount	=				b.BudgetAvailableAmount
	,a.BudgetAmount			=		b.BudgetAmount
	,a.TotalAmountRMB		=			b.TotalAmountRMB
	,a.ExpenseTypeId			=		b.ExpenseTypeId
	,a.ExpenseTypeName		=			b.ExpenseTypeName
	,a.ProductId				=	b.ProductId
	,a.ProductName			=		b.ProductName
	,a.CompanyId				=	b.CompanyId
	,a.CompanyCode			=		b.CompanyCode
	,a.CompanyName			=		b.CompanyName
	,a.ClientId				=	b.ClientId
	,a.ClientCode			=		b.ClientCode
	,a.ClientName			=		b.ClientName
	,a.ClientTypeId			=		b.ClientTypeId
	,a.ClientType			=		b.ClientType
	,a.Attachment			=		b.Attachment
	,a.Remark				=	b.Remark
	,a.TransfereeId			=		b.TransfereeId
	,a.TransfereeName		=			b.TransfereeName
	,a.DataSource			=		b.DataSource
	,a.CreationTime			=		b.CreationTime
	,a.CreatorId				=	b.CreatorId
	,a.LastModificationTime	=				b.LastModificationTime
	,a.LastModifierId		=			b.LastModifierId
	,a.IsDeleted				=	b.IsDeleted
	,a.DeleterId				=	b.DeleterId
	,a.DeletionTime			=		b.DeletionTime
       from dbo.STicketApplications_tmp a 
       left join #STicketApplications_tmp b on a.ProcInstId = b.ProcInstId
       
       insert into dbo.STicketApplications_tmp 
       select 
       id
		,ProcInstId
		,ApplicationCode
		,CssCode
		,ApplicationType
		,Status
		,ApplyTime
		,ApplyUserId
		,ApplyUser
		,ApplyUserDeptId
		,ApplyUserDeptName
		,CostCenterId
		,CostCenterCode
		,CostCenter
		,ApplyUserBuId
		,ApplyUserBuName
		,BudgetId
		,BudgetCode
		,SubBudgetId
		,SubBudgetCode
		,SubBudgetRegionId
		,SubBudgetRegion
		,BudgetOwnerId
		,BudgetOwner
		,BudgetFile
		,BudgetUsedAmount
		,BudgetAvailableAmount
		,BudgetAmount
		,TotalAmountRMB
		,ExpenseTypeId
		,ExpenseTypeName
		,ProductId
		,ProductName
		,CompanyId
		,CompanyCode
		,CompanyName
		,ClientId
		,ClientCode
		,ClientName
		,ClientTypeId
		,ClientType
		,Attachment
		,Remark
		,TransfereeId
		,TransfereeName
		,DataSource
		,CreationTime
		,CreatorId
		,LastModificationTime
		,LastModifierId
		,IsDeleted
		,DeleterId
		,DeletionTime
    from #STicketApplications_tmp a
    where not exists (select * from dbo.STicketApplications_tmp where ProcInstId = a.ProcInstId)
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into dbo.STicketApplications_tmp from #STicketApplications_tmp
    -- select * from #vendor_tbl
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END





end