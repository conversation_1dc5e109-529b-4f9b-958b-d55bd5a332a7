﻿using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Integration.Dspot;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Entities.Integration.Dspot;
using Abbott.SpeakerPortal.Entities.Purchase.PurGRApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPOApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Entities.VendorFinancials;
using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.Enums.Integration;
using Abbott.SpeakerPortal.Extension;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using EFCore.BulkExtensions;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.EntityFrameworkCore;
using static Abbott.SpeakerPortal.Enums.PurGRStatus;
using static Abbott.SpeakerPortal.Enums.Purchase;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Entities.VendorApplications;
using Abbott.SpeakerPortal.Entities.VendorApplicationFinancials;
using Abbott.SpeakerPortal.Enums;

namespace Abbott.SpeakerPortal.AppServices.Integration.Dspot
{
    public class InteBpcsUncompletedPRVendorService : SpeakerPortalAppService, IInteBpcsUncompletedPRVendorService
    {
        private const int VALID_WITHIN_MONTH = -18;//负18（-18）表示过去的18个月内的

        /// <summary>
        /// The service provider
        /// </summary>
        private readonly IServiceProvider _serviceProvider;

        /// <summary>
        /// The logger
        /// </summary>
        private readonly ILogger<InteBpcsUncompletedPRVendorService> _logger;

        /// <summary>
        /// The configuration
        /// </summary>
        private readonly IConfiguration _configuration;

        /// <summary>
        /// The dataverseService
        /// </summary>
        private readonly IDataverseService _dataverseService;

        private readonly IRepository<EdiLogUncompletedPRVendor> _ediLogRepo;


        public InteBpcsUncompletedPRVendorService(IServiceProvider serviceProvider, IRepository<EdiLogUncompletedPRVendor> ediLogRepo)
        {
            _serviceProvider = serviceProvider;
            _logger = serviceProvider.GetService<ILogger<InteBpcsUncompletedPRVendorService>>();
            _configuration = serviceProvider.GetService<IConfiguration>();
            _dataverseService = serviceProvider.GetService<IDataverseService>();
            _ediLogRepo = ediLogRepo;
        }

        public async Task<string> SyncUncompletedPRVendor()
        {
            DateTime invalidDt = DateTime.Now.Date.AddMonths(VALID_WITHIN_MONTH);

            try
            {
                //获取将要推送的Dto
                var dicUnDtos = await GetUnDtos(invalidDt);

                //组装EDI文件的内容，同一个CompanyCode的N条Vendor在一个EDI文件
                Dictionary<string, string[]> dicUploadEdiInputs = new Dictionary<string, string[]>();
                List<string> ediLines = null;// new List<string>();
                List<EdiLogUncompletedPRVendor> ediLogs = new List<EdiLogUncompletedPRVendor>();
                var now = DateTime.Now;
                foreach (var item in dicUnDtos)
                {
                    if (item.Value?.Any() != true)
                    {
                        continue;
                    }

                    //每次循环（每个CompanyCode）重新装填 ediLines，但 ediLogs、dicUploadEdiInputs 一直累加
                    ediLines = new List<string>();
                    foreach (var itemUnDto in item.Value)
                    {
                        var (ediLine, ediLog) = GetEdiLines(itemUnDto, now);
                        ediLines.Add(ediLine);
                        ediLogs.Add(ediLog);
                    }
                    if (ediLines.Any())
                    {
                        dicUploadEdiInputs.Add(item.Key, new string[2] { CalcSftpVndPath(item.Key), ediLines.JoinAsString("\r\n") });
                    }
                    else
                    {
                        _logger.LogError($"SyncUncompletedPRVendor() !ediLines.Any(), CompanyCode:{item.Key}");
                    }
                }

                if (dicUploadEdiInputs.Count < 1)
                {
                    _logger.LogError($"SyncUncompletedPRVendor() dicUploadEdiInputs.Count < 1");
                    return "Upload Edi Inputs is null";
                }
                /*数据量大，效率有问题*/
                /*
                var repoEdiLog = LazyServiceProvider.LazyGetService<IEdiLogUncompletedPRVendorRepository>();
                if (ediLogs?.Any() == true)
                {
                    //本来要用 InsertManyAsync()一次性插入多条，但该方法返回void，不能获取Tracking的Entity，不便于后面的修改Stauts
                    for (int i = 0; i < ediLogs.Count; i++)
                    {
                        var item = repoEdiLog.InsertAsync(ediLogs[i], true).GetAwaiter().GetResult();
                        ediLogs[i] = item;
                    }
                }

                //推送到BPCS的SFTP
                var _sftpService = _serviceProvider.GetService<IInteBpcsSFTPService>();
                var uploadResult = _sftpService.UploadToBatch(dicUploadEdiInputs);
                //根据推送结果修改EdiLogVM.Status
                if (ediLogs?.Any() == true)
                {
                    var ediLogStatus = uploadResult ? EdiSyncStatus.Pushed : EdiSyncStatus.Init;
                    ediLogs.ForEach(a =>
                    {
                        a.Status = ediLogStatus;
                    });
                    //保存表 EdiLogApInvioce
                    repoEdiLog.UpdateManyAsync(ediLogs).GetAwaiter().GetResult();
                }
                */

                
                //推送到BPCS的SFTP
                var _sftpService = _serviceProvider.GetService<IInteBpcsSFTPService>();
                var uploadResult = _sftpService.UploadToBatch(dicUploadEdiInputs);
                //根据推送结果修改EdiLogVM.Status
                if (ediLogs?.Any() == true)
                {
                    var ediLogStatus = uploadResult ? EdiSyncStatus.Pushed : EdiSyncStatus.Init;
                    ediLogs.ForEach(a =>
                    {                                           
                        a.Status = ediLogStatus;//EdiLogUncompletedPRVendors
                    });
                    var repoEdiLog = LazyServiceProvider.LazyGetService<IEdiLogUncompletedPRVendorRepository>();
                    repoEdiLog.GetDbContextAsync().GetAwaiterResult().BulkInsert(ediLogs);                    
                    //repoEdiLog.InsertManyAsync(ediLogs).GetAwaiter().GetResult();
                }
                if (!uploadResult)
                {
                    _logger.LogError($"SyncUncompletedPRVendor() uploadResult is false");
                    return "Upload to SFTP is failed";
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"SyncUncompletedPRVendor() Exception: {ex}");
                throw;
            }

            return null;
        }

        private async Task<Dictionary<string, List<UncompletedDto>>> GetUnDtos(DateTime invalidDt)
        {
            //第1点：
            var unBpcsAvmIdsAR = await GetBpcsAvmIdForAR(invalidDt);

            //第2点：
            var unBpcsAvmIdsAP = await GetBpcsAvmIdForAP(invalidDt);

            //第3点：
            var unBpcsAvmIdsAP0GR = await GetBpcsAvmIdForAR0GR(invalidDt);

            //将前3点获取到的BpcsAvm.Id，一次性从BpcsAvm表里取出 UncompletedDto
            var bpcsAvmIds = new List<Guid>();
            if (unBpcsAvmIdsAR?.Any() == true)
            {
                bpcsAvmIds.AddRange(unBpcsAvmIdsAR);
            }
            if (unBpcsAvmIdsAP?.Any() == true)
            {
                bpcsAvmIds.AddRange(unBpcsAvmIdsAP);
            }
            if (unBpcsAvmIdsAP0GR?.Any() == true)
            {
                bpcsAvmIds.AddRange(unBpcsAvmIdsAP0GR);
            }

            //BpcsAvm，最终从这里取数
            var queryBpcsAvm = (await LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync()).AsNoTracking();
            var unDtosPRD = queryBpcsAvm.Where(a => bpcsAvmIds.Contains(a.Id))
                .Select(a => new UncompletedDto { Vcmpny = a.Vcmpny, Vendor = a.Vendor })
                .ToList();

            List<UncompletedDto> allUnDtosVnd = new List<UncompletedDto>();
            //第4点：
            var unDtosVnd = await GetForVendors(invalidDt);
            if (unDtosVnd?.Any() == true)
                allUnDtosVnd.AddRange(unDtosVnd);

            //第5点
            var unDtosVndApplication = await GetForVendorApplications(invalidDt);
            if (unDtosVndApplication?.Any()==true)
                allUnDtosVnd.AddRange(unDtosVndApplication);

            var dicUnDtos = TidyDicUnDtos(unDtosPRD, allUnDtosVnd);

            return dicUnDtos;
        }

        private async Task<List<Guid>> GetBpcsAvmIdForAR(DateTime invalidDt)
        {
            //第1点：
            //• For AR-type line items in PR:
            //Last payment of the item has not been completed yet,
            //and the PR approval date is within 18 months.
            //– Both vendor & alternative vendor included in the line item would be added to the file
            var queryPRDetail = (await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync()).AsNoTracking();
            var queryPR = (await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync()).AsNoTracking();

            // PurPRApplicationStatus.RejectedBack SCTASK2359209,不排除退回状态
            PurPRApplicationStatus[] prStatus = [PurPRApplicationStatus.ApplicantTerminate, PurPRApplicationStatus.Rejected];
            var queryPRFilter = queryPR.Where(a =>!prStatus.Contains(a.Status) && a.ApprovedDate != null && a.ApprovedDate > invalidDt);

            var queryBackupVendor = (await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailBackupVendorRepository>().GetQueryableAsync()).AsNoTracking();

            var queryGRD = (await LazyServiceProvider.LazyGetService<IPurGRApplicationDetailRepository>().GetQueryableAsync()).AsNoTracking();
            var queryGR = (await LazyServiceProvider.LazyGetService<IPurGRApplicationRepository>().GetQueryableAsync()).AsNoTracking();

            PurGRApplicationStatus[] grStatus = [PurGRApplicationStatus.ReceivedGoods, PurGRApplicationStatus.Terminationed];
            //var queryGRFilter = queryGR.Where(a => !grStatus.Contains(a.Status));

            var queryPRDetailIds =
                //AR-type
                queryPRDetail.Where(a => a.PayMethod == Enums.Purchase.PayMethods.AR && !a.IsHedge).Select(a=>new { a.Id,a.PRApplicationId,a.VendorId })
                //PR审批在18月内
                .Join(queryPRFilter, left => left.PRApplicationId, right => right.Id, (left, right) => new { prD = left, pr = right })
                //PR行未完成（PR对应到GR行的GR主表，a.Status != PurGRApplicationStatus.ReceivedGoods）
                .GroupJoin(queryGRD.Select(a => new { a.Id, a.PRDetailId, a.GRApplicationId }), a => a.prD.Id, b => b.PRDetailId, (a, b) => new { a.prD, a.pr, grds = b })
                .SelectMany(a => a.grds.DefaultIfEmpty(), (a, b) => new { a.prD, a.pr, grd = b })
                .GroupJoin(queryGR.Select(a => new { a.Id, a.PrId ,a.VendorId,a.Status}), a => a.grd.GRApplicationId, b => b.Id, (a, b) => new { a.prD, a.pr, a.grd, grs = b })
                .SelectMany(a => a.grs.DefaultIfEmpty(), (a, b) => new { a.prD, a.pr, a.grd, gr = b })
                .Where(a=> !grStatus.Contains(a.gr.Status))// ytw 20241017 需要leftJoin 后再筛选，如果把这个条件放在queryGR PRD匹配不到会被当做未走到GR流程
                .Select(a=> new { a.prD.Id, a.prD.VendorId, GrVendorId = a.gr.VendorId });

            var bpcsAvmIds = queryBackupVendor.Join(queryPRDetailIds, left => left.PRApplicationDetailId, right => right.Id, (left, right) => new { backupVnd = left, right })
                .Select(a => new { a.backupVnd.VendorId,a.right.Id })
                .Union(queryPRDetailIds.Where(a => a.VendorId != null).Select(a => new { VendorId = a.GrVendorId.HasValue ? a.GrVendorId.Value : a.VendorId.Value,a.Id }))
                .ToList();

            var result = bpcsAvmIds.Select(a=>a.VendorId).Distinct().ToList();

            return result;
        }

        private async Task<List<Guid>> GetBpcsAvmIdForAP(DateTime invalidDt)
        {
            //第2点：
            //• For AP-type line items in PR:
            //Last payment of the item has not been completed yet,
            //and the related PO approval date is within 18 months.
            //– Both vendor & alternative vendor included in the line item would be added to the file
            var queryPRDetail = (await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync()).AsNoTracking();
            var queryPODetail = (await LazyServiceProvider.LazyGetService<IPurPOApplicationDetailsRepository>().GetQueryableAsync()).AsNoTracking();
            var queryPO = (await LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>().GetQueryableAsync()).AsNoTracking();

            PurOrderStatus[] poStatus = [PurOrderStatus.ApplierConfirmed, PurOrderStatus.Approving, PurOrderStatus.Approved, PurOrderStatus.Closed, PurOrderStatus.InitiateReceipt];
            var queryPOFilter = queryPO.Where(a => poStatus.Contains(a.Status) && a.ApprovedDate != null && a.ApprovedDate > invalidDt)
                .Join(queryPODetail, left => left.Id, right => right.POApplicationId, (left, right) => new { po = left, poD = right });

            var queryBackupVendor = (await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailBackupVendorRepository>().GetQueryableAsync()).AsNoTracking();

            var queryGRD = (await LazyServiceProvider.LazyGetService<IPurGRApplicationDetailRepository>().GetQueryableAsync()).AsNoTracking();
            var queryGR = (await LazyServiceProvider.LazyGetService<IPurGRApplicationRepository>().GetQueryableAsync()).AsNoTracking();

            PurGRApplicationStatus[] grStatus = [PurGRApplicationStatus.ReceivedGoods, PurGRApplicationStatus.Terminationed];
            //var queryGRFilter = queryGR.Where(a => !grStatus.Contains(a.Status));

            var queryPRDetailIds =
                //AP-type
                queryPOFilter
                //PO审批在18月内
                .Join(queryPRDetail.Where(a => a.PayMethod == Enums.Purchase.PayMethods.AP && !a.IsHedge).Select(a => new { a.Id, a.PRApplicationId, a.VendorId }), left => left.poD.PRDetailId, right => right.Id, (left, right) => new { prD = right, right })
                //PR行未完成（PR对应到GR行的GR主表，a.Status != PurGRApplicationStatus.ReceivedGoods）
                .GroupJoin(queryGRD.Select(a => new { a.Id, a.PRDetailId, a.GRApplicationId }), a => a.prD.Id, b => b.PRDetailId, (a, b) => new { a.prD, grds = b })
                .SelectMany(a => a.grds.DefaultIfEmpty(), (a, b) => new { a.prD, grd = b })
                .GroupJoin(queryGR.Select(a => new { a.Id, a.PrId, a.VendorId, a.Status }), a => a.grd.GRApplicationId, b => b.Id, (a, b) => new { a.prD, a.grd, grs = b })
                .SelectMany(a => a.grs.DefaultIfEmpty(), (a, b) => new { a.prD, a.grd, gr = b })
                .Where(a => !grStatus.Contains(a.gr.Status))// ytw 20241017 需要leftJoin 后再筛选，如果把这个条件放在queryGR PRD匹配不到会被当做未走到GR流程
                .Select(a => new { a.prD.Id, a.prD.VendorId, GrVendorId = a.gr.VendorId });

            var bpcsAvmIds = queryBackupVendor.Join(queryPRDetailIds, left => left.PRApplicationDetailId, right => right.Id, (left, right) => new { backupVnd = left, right })
                .Select(a => new { a.backupVnd.VendorId, a.right.Id })
                .Union(queryPRDetailIds.Where(a => a.VendorId != null).Select(a => new { VendorId = a.GrVendorId.HasValue ? a.GrVendorId.Value : a.VendorId.Value, a.Id }))
                .ToList();

            var result = bpcsAvmIds.Select(a => a.VendorId).Distinct().ToList();

            return result;
        }

        private async Task<List<Guid>> GetBpcsAvmIdForAR0GR(DateTime invalidDt)
        {
            //第3点
            //• For AR-type speaker line items with price of 0:
            //The receipt date of the related GR with price of 0 is within 18 months.
            //– Vendor included in the receipt request would be added to the file
            var queryPRDetail = (await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync()).AsNoTracking();

            var queryBackupVendor = (await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailBackupVendorRepository>().GetQueryableAsync()).AsNoTracking();

            var queryGRD = (await LazyServiceProvider.LazyGetService<IPurGRApplicationDetailRepository>().GetQueryableAsync()).AsNoTracking();
            var queryGR = (await LazyServiceProvider.LazyGetService<IPurGRApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            var queryGRFilter = queryGR.Where(a => a.Status == PurGRApplicationStatus.ReceivedGoods && a.ReceivedTime > invalidDt);

            var queryPRDetailIds = queryGRFilter
                //GR状态是完成收货+GR完成收货日期18个月内
                .Join(queryGRD, left => left.Id, right => right.GRApplicationId, (left, right) => new { gr = left, grD = right })
                //AR-type, 且关联的PR行.金额==0
                .Join(queryPRDetail.Where(a => a.PayMethod == Enums.Purchase.PayMethods.AR)
                    .Where(a => a.TotalAmount == null || a.TotalAmount == 0)
                    , left => left.grD.PRDetailId, right => right.Id, (left, right) => new { left, prD = right })
                .Select(a => new { a.prD.Id, a.prD.VendorId });

            var bpcsAvmIds = queryBackupVendor.Join(queryPRDetailIds, left => left.PRApplicationDetailId, right => right.Id, (left, right) => new { backupVnd = left, right })
                .Select(a => a.backupVnd.VendorId)
                .Union(queryPRDetailIds.Where(a => a.VendorId != null).Select(a => a.VendorId.Value))
                .Distinct().ToList();

            return bpcsAvmIds;
        }

        private async Task<List<UncompletedDto>> GetForVendors(DateTime invalidDt)
        {
            //第4点：
            //For vendors whose last update date (both information & status update) is within 18 months,
            //the vendor should be added to the file
            var queryVendor = (await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync()).AsNoTracking();
            var queryVendorFina = (await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync()).AsNoTracking();
            var pushVndVndFinas = queryVendor.Where(a => a.LastModificationTime > invalidDt || (a.LastModificationTime == null && a.CreationTime > invalidDt))
                .Join(queryVendorFina, left => left.Id, right => right.VendorId, (left, right) => new { left, right })
                .ToList();
            if (pushVndVndFinas?.Any() != true)
            {
                //_logger.LogError($"SyncUncompletedPRVendor() pushVndVndFinas?.Any() != true");
                //return "pushVndVndFinas is null";
                return default;
            }
            return pushVndVndFinas.Select(a => new UncompletedDto { Vcmpny = a.right.Company?.ToDecimal(), Vendor = a.right?.VendorCode.ToDecimal() }).ToList();
        }

        /// <summary>
        /// 供应商申请表
        /// </summary>
        /// <param name="invalidDt"></param>
        /// <returns></returns>
        private async Task<List<UncompletedDto>> GetForVendorApplications(DateTime invalidDt)
        {
            //第5点：
            //For vendorApplications whose last update date (both information & status update) is within 18 months,
            //the vendorApplication should be added to the file
            Statuses[] statuses = [Statuses.Approving, Statuses.Returned, Statuses.Withdraw];
            var queryVendor = (await LazyServiceProvider.LazyGetService<IVendorApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            var queryVendorFina = (await LazyServiceProvider.LazyGetService<IVendorApplicationFinancialRepository>().GetQueryableAsync()).AsNoTracking();
            var pushVndVndFinas = queryVendor.Where(a => a.LastModificationTime > invalidDt || (a.LastModificationTime == null && a.CreationTime > invalidDt))
                .Join(queryVendorFina, left => left.Id, right => right.ApplicationId, (left, right) => new { left, right })
                .Where(a => statuses.Contains(a.left.Status) && a.right.FinancialVendorStatus == FinancialVendorStatus.Valid)
                .ToList();
            if (pushVndVndFinas?.Any() != true)
            {
                return default;
            }
            return pushVndVndFinas.Select(a => new UncompletedDto { Vcmpny = a.right.Company?.ToDecimal(), Vendor = a.right?.VendorCode.ToDecimal() }).ToList();
        }

        private (string, EdiLogUncompletedPRVendor) GetEdiLines(VendorFinancial vendorFina
            )
        {
            if (vendorFina == null)
            {
                _logger.LogError($"GetEdiLines() vendorFina == null");
                return (null, null);
            }

            var now = DateTime.Now;
            //组装实体 EdiLogUncompletedPRVendor，保存EDI日志表
            EdiLogUncompletedPRVendor ediLog = new EdiLogUncompletedPRVendor()
            {
                TimeSendToBpcs = now,
                VendorId = vendorFina?.Id,
            };

            var result =
                $"{ediLog.Vendor = vendorFina.VendorCode.ToDecimal()}|"
                + $"{ediLog.Vpdate = now.ToString("yyyyMMdd").ToDecimal()}|"
                + $"{ediLog.Vptime = now.ToString("HHmmss").ToDecimal()}|";
            return (result, ediLog);
        }

        private (string, EdiLogUncompletedPRVendor) GetEdiLines(UncompletedDto unDto
            , DateTime now)
        {
            if (unDto == null)
            {
                _logger.LogError($"GetEdiLines() unDto == null");
                return (null, null);
            }

            //组装实体 EdiLogUncompletedPRVendor，保存EDI日志表
            EdiLogUncompletedPRVendor ediLog = new EdiLogUncompletedPRVendor()
            {
                TimeSendToBpcs = now,
                Vcmpny = unDto.Vcmpny
            };

            var result =
                $"{ediLog.Vendor = unDto.Vendor}|"
                + $"{ediLog.Vpdate = now.ToString("yyyyMMdd").ToDecimal()}|"
                + $"{ediLog.Vptime = now.ToString("HHmmss").ToDecimal()}";
            //未完成PR的Vendor推送，是没有最后的ErrMsg字段，所以最后没有竖线！

            return (result, ediLog);
        }

        private string CalcSftpVndPath(string companyCode)
        {
            string folderLvl1 = _configuration["Integrations:BPCS:SftpFolderLvl1"];

            //获取公司的缩写名称
            var allCompany = _dataverseService.GetCompanyList().GetAwaiter().GetResult();
            string folderLvl2 = $"{(allCompany?.Any() != true ? "" : allCompany.FirstOrDefault(a => a.CompanyCode == companyCode)?.AbbrCode)}_{InteBpcsDataType.VND_SPVSTS}";

            string folderLvl3 = _configuration["Integrations:BPCS:SftpFolderLvl3Upload"];

            //计算文件名，Vendor：SPV+5位序列号（从00001开始）
            var seqNumService = _serviceProvider.GetService<ISequenceNumService>();
            var fileName = seqNumService.GetNextFileNameAndIncrease(Enums.SequenceNumCategory.SPV);

            //正反斜杠都行；最前面要不要"/"也都行
            //return $"/{folderLvl1}/{folderLvl2}/{folderLvl3}/V0000001_{DateTime.Now.ToString("yyyy-MM-dd_HHmmss")}.TXT";
            return Path.Combine("/", folderLvl1, folderLvl2, folderLvl3, fileName);
        }

        private List<UncompletedAvmDto> GetListBpcsAvm(List<Guid> avmIds)
        {
            if (avmIds?.Any() != true)
            {
                return default;
            }
            var queryBpcsAvm = LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync()
                .GetAwaiter().GetResult().AsNoTracking();
            return queryBpcsAvm.Select(a => new UncompletedAvmDto
            {
                Id = a.Id,
                VendorFinaId = a.FinaId,
                Vcmpny = a.Vcmpny,
                Vendor = a.Vendor,
            })
                .Where(a => avmIds.Contains(a.Id))
                .ToList();
        }

        private Dictionary<string, List<UncompletedDto>> TidyDicUnDtos(List<UncompletedDto> unDtosPRD
            , List<UncompletedDto> unDtosVnd)
        {
            //先整合所有的 UncompletedDto
            var unDtos = new List<UncompletedDto>();
            if (unDtosPRD?.Any() == true)
            {
                unDtos.AddRange(unDtosPRD);
            }
            if (unDtosVnd?.Any() == true)
            {
                unDtos.AddRange(unDtosVnd);
            }

            var result = new Dictionary<string, List<UncompletedDto>>();
            if (unDtos?.Any() != true)
            {
                return result;
            }

            var companyCodes = unDtos.Select(a => a.Vcmpny?.ToString())
                .Where(a => !string.IsNullOrWhiteSpace(a)).Distinct();
            foreach (var companyCode in companyCodes)
            {
                var curRange = unDtos.Where(a => string.Equals(a.Vcmpny.ToString(), companyCode.ToString(), StringComparison.OrdinalIgnoreCase))
                    .Where(a => !string.IsNullOrWhiteSpace(a.Vendor.ToString()))
                    .Distinct().ToList();
                if (curRange?.Any() != true)
                {
                    continue;
                }

                if (result.ContainsKey(companyCode))
                {
                    result[companyCode].AddRange(curRange);
                }
                else
                {
                    result.Add(companyCode, curRange);
                }
            }
            return result;
        }
    }
}