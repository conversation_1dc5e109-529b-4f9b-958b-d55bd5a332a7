﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Abbott.SpeakerPortal.Contracts.MSA;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Entities.Common.Attachment;
using Abbott.SpeakerPortal.Entities.MSA;
using Abbott.SpeakerPortal.Vendor.Speaker;
using Abbott.SpeakerPortal.Utils;

using Microsoft.EntityFrameworkCore;

using Volo.Abp.Application.Dtos;
using Abbott.SpeakerPortal.Entities.Purchase.PurPOApplication;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Person;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Consts;

namespace Abbott.SpeakerPortal.AppServices.MSA
{
    public class SowService : SpeakerPortalAppService, ISowService
    {
        /// <summary>
        /// 获取Sow列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<SowResponseDto>> GetSowListAsync(SowRequestDto request)
        {
            IEnumerable<Guid?> companyIds = [];
            //非管理员级别只能查看自己配置公司下的数据
            if (!CurrentUser.IsInRole(RoleNames.BizAdmin))
                companyIds = (await LazyServiceProvider.LazyGetService<IDataverseService>().GetProcurementPushConfigAsync(CurrentUser.Id?.ToString(), null)).Select(s => s.CompanyId).ToArray();

            var querySow = await LazyServiceProvider.LazyGetService<IMsaStatementOfWorkReadonlyRepository>().GetQueryableAsync();
            var query = querySow.Include(a => a.Msa)
                .WhereIf(!CurrentUser.IsInRole(RoleNames.BizAdmin), a => companyIds.Contains(a.CompanyId))
                .WhereIf(request.MsaId.HasValue, a => a.MsaId == request.MsaId)
                .WhereIf(request.BuId.HasValue, a => a.BuId == request.BuId)
                .WhereIf(request.CompanyId.HasValue, a => a.CompanyId == request.CompanyId)
                .WhereIf(!string.IsNullOrEmpty(request.VendorNameAccuracy), a => a.VendorName == request.VendorNameAccuracy)
                .WhereIf(!string.IsNullOrEmpty(request.Code), a => a.Code.Contains(request.Code))
                .WhereIf(!string.IsNullOrEmpty(request.VendorName), a => a.VendorName.Contains(request.VendorName))
                .WhereIf(request.CategoryId.HasValue, a => a.CategoryId == request.CategoryId)
                .Select(a => new SowResponseDto
                {
                    Id = a.Id,
                    Code = a.Code,
                    MsaCode = a.Msa.Code,
                    VendorName = a.VendorName,
                    BuName = a.BuName,
                    CompanyName = a.CompanyName,
                    CategoryName = a.CategoryName,
                    AttachmentFileIds = a.AttachmentFileIds
                });

            var count = await query.CountAsync();
            var datas = await query
                .OrderByDescending(a => a.Code)
                .Skip(request.PageIndex * request.PageSize)
                .Take(request.PageSize)
                .ToArrayAsync();

            //有数据时，获取附件信息
            if (datas.Length > 0)
            {
                var allAttachmentIds = datas.Where(a => !string.IsNullOrEmpty(a.AttachmentFileIds)).SelectMany(a => a.AttachmentFileIds.Split(","), (a, b) => Guid.Parse(b)).ToArray();
                var attachments = await LazyServiceProvider.LazyGetService<IAttachmentReadonlyRepository>().GetListAsync(a => allAttachmentIds.Contains(a.Id));
                foreach (var item in datas)
                {
                    if (string.IsNullOrEmpty(item.AttachmentFileIds))
                        continue;

                    var attachmentIds = item.AttachmentFileIds.Split(",").Select(Guid.Parse).ToArray();
                    item.Attachments = attachments.Where(a => attachmentIds.Contains(a.Id)).Select(a => new AttachmentInformation
                    {
                        AttachmentId = a.Id,
                        FileName = a.FileName,
                        FileSize = a.Size.ToFileSizeString(),
                        Suffix = a.Suffix,
                        FilePath = a.FilePath
                    });
                }
            }

            return new PagedResultDto<SowResponseDto>(count, datas);
        }

        /// <summary>
        /// 通过Sow Id查询相关联的Po
        /// </summary>
        /// <param name="sowId"></param>
        /// <returns></returns>
        public async Task<IEnumerable<SowRelatedPoResponseDto>> GetRelatedPoListAsync(Guid sowId)
        {
            var queryPo = await LazyServiceProvider.LazyGetService<IPurPOApplicationReadonlyRepository>().GetQueryableAsync();
            var datas = await queryPo.Where(a => a.SowId == sowId)
            .Select(a => new SowRelatedPoResponseDto
            {
                PoId = a.Id,
                PoCode = a.ApplicationCode,
                AppliedAt = a.Status == Enums.Purchase.PurOrderStatus.Draft ? null : a.ApplyTime.ToString("yyyy-MM-dd"),
                ApplicantName = a.ApplyUserName,
                Amount = Math.Round(a.TotalAmount, 2),
                ApsProperty = a.ApsPorperty,
                PurchaseCategory = a.PRTypeName,
                Status = a.Status
            })
            .ToArrayAsync();

            if (datas.Length > 0)
            {
                var apsProperties = await LazyServiceProvider.LazyGetService<IDataverseService>().GetDictionariesAsync(DictionaryType.ApsProperty, null);
                foreach (var item in datas)
                    item.ApsProperty = apsProperties.FirstOrDefault(a => a.Code == item.ApsProperty)?.Name;
            }

            return datas;
        }

        /// <summary>
        /// 获取Sow详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<SowResponseDto> GetSowDetailAsync(Guid id)
        {
            var querySow = await LazyServiceProvider.LazyGetService<IMsaStatementOfWorkReadonlyRepository>().GetQueryableAsync();
            var sow = await querySow.Where(a => a.Id == id).Include(a => a.Msa).FirstOrDefaultAsync();
            if (sow == null)
                return default;

            var response = ObjectMapper.Map<MsaStatementOfWork, SowResponseDto>(sow);
            response.MsaCode = sow.Msa?.Code;

            if (!string.IsNullOrEmpty(sow.AttachmentFileIds))
            {
                var attachmentIds = sow.AttachmentFileIds.Split(",").Select(Guid.Parse).ToArray();
                var attachments = await LazyServiceProvider.LazyGetService<IAttachmentReadonlyRepository>().GetListAsync(a => attachmentIds.Contains(a.Id));
                response.Attachments = attachments.Where(a => attachmentIds.Contains(a.Id)).Select(a => new AttachmentInformation
                {
                    AttachmentId = a.Id,
                    FileName = a.FileName,
                    FileSize = a.Size.ToFileSizeString(),
                    Suffix = a.Suffix,
                    FilePath = a.FilePath
                });
            }

            return response;
        }

        /// <summary>
        /// 导出Sow列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<IEnumerable<SowExportResponseDto>> ExportSowListAsync(SowRequestDto request)
        {
            IEnumerable<Guid?> companyIds = [];
            //非管理员级别只能查看自己配置公司下的数据
            if (!CurrentUser.IsInRole(RoleNames.BizAdmin))
                companyIds = (await LazyServiceProvider.LazyGetService<IDataverseService>().GetProcurementPushConfigAsync(CurrentUser.Id?.ToString(), null)).Select(s => s.CompanyId).ToArray();

            var querySow = await LazyServiceProvider.LazyGetService<IMsaStatementOfWorkReadonlyRepository>().GetQueryableAsync();
            var queryPo = await LazyServiceProvider.LazyGetService<IPurPOApplicationReadonlyRepository>().GetQueryableAsync();
            var queryPr = await LazyServiceProvider.LazyGetService<IPurPRApplicationReadonlyRepository>().GetQueryableAsync();

            var datas = await queryPo
            .Join(queryPr, a => a.PRId, a => a.Id, (a, b) => new { Po = a, PrCode = b.ApplicationCode })
            .Join(querySow.Include(a => a.Msa).ThenInclude(a => a.Companies).Include(a => a.Msa).ThenInclude(a => a.BuMappings).Include(a => a.Msa).ThenInclude(a => a.ServiceTypeMappings), a => a.Po.SowId, a => a.Id, (a, b) => new { Po = new { a.Po.ApplicationCode, a.Po.ApplyTime, a.Po.ApplyUserName, a.Po.Status, a.Po.TotalAmountTax, a.Po.ExchangeRate, a.PrCode }, Sow = b })
            //.Include(a => a.Sow.Msa)
            //.ThenInclude(a => a.Companies)
            //.Include(a => a.Sow.Msa)
            //.ThenInclude(a => a.BuMappings)
            //.Include(a => a.Sow.Msa)
            //.ThenInclude(a => a.ServiceTypeMappings)
            .WhereIf(!CurrentUser.IsInRole(RoleNames.BizAdmin), a => companyIds.Contains(a.Sow.CompanyId))
            .WhereIf(request.MsaId.HasValue, a => a.Sow.MsaId == request.MsaId)
            .WhereIf(request.BuId.HasValue, a => a.Sow.BuId == request.BuId)
            .WhereIf(request.CompanyId.HasValue, a => a.Sow.CompanyId == request.CompanyId)
            .WhereIf(!string.IsNullOrEmpty(request.VendorNameAccuracy), a => a.Sow.VendorName == request.VendorNameAccuracy)
            .WhereIf(!string.IsNullOrEmpty(request.Code), a => a.Sow.Code.Contains(request.Code))
            .WhereIf(!string.IsNullOrEmpty(request.VendorName), a => a.Sow.VendorName.Contains(request.VendorName))
            .WhereIf(request.CategoryId.HasValue, a => a.Sow.CategoryId == request.CategoryId)
            .Select(a => new SowExportResponseDto
            {
                //Msa
                MsaCode = a.Sow.Msa.Code,
                VendorName = a.Sow.VendorName,
                TmpMsaCompanyName = a.Sow.Msa.Companies.Select(a => a.CompanyName),
                TmpMsaBuName = a.Sow.Msa.BuMappings.Select(a => a.BuName),
                TmpMsaCategoryName = a.Sow.Msa.ServiceTypeMappings.Select(a => a.ServiceTypeName),
                //MsaCompanyName = a.Sow.Msa.Companies.JoinAsString(","),
                //MsaBuName = a.Sow.Msa.BuMappings.JoinAsString(","),
                //MsaCategoryName = a.Sow.Msa.ServiceTypeMappings.JoinAsString(","),
                MsaAttachments = a.Sow.Msa.AttachmentFileIds,
                MsaRemark = a.Sow.Msa.Remark,
                MsaStartedAt = a.Sow.Msa.EffectiveDate.HasValue ? a.Sow.Msa.EffectiveDate.Value.ToString("yyyy-MM-dd") : null,
                MsaEndedAt = a.Sow.Msa.ExpiryDate.HasValue ? a.Sow.Msa.ExpiryDate.Value.ToString("yyyy-MM-dd") : null,

                //Sow
                SowCode = a.Sow.Code,
                SowAttachments = a.Sow.AttachmentFileIds,
                SowCompanyName = a.Sow.CompanyName,
                SowBuName = a.Sow.BuName,
                SowCategoryName = a.Sow.CategoryName,

                //Po
                PoNo = a.Po.ApplicationCode,
                PoAppliedAt = a.Po.Status == Enums.Purchase.PurOrderStatus.Draft ? null : a.Po.ApplyTime.ToString("yyyy-MM-dd"),
                PoApplicant = a.Po.ApplyUserName,
                TmpPoStatus = a.Po.Status,
                PoAmount = Math.Round(a.Po.TotalAmountTax * (decimal)a.Po.ExchangeRate, 2),

                //Pr
                PrNo = a.Po.PrCode
            })
            .OrderByDescending(a => a.SowCode)
            .ToArrayAsync();

            //有数据时，获取附件信息
            if (datas.Length > 0)
            {
                var attachmentIds = datas.Select(a => a.MsaAttachments)
                .Union(datas.Select(a => a.SowAttachments))
                .Where(a => !string.IsNullOrEmpty(a))
                .SelectMany(a => a.Split(","), (a, b) => Guid.Parse(b)).ToArray();

                var attachments = await LazyServiceProvider.LazyGetService<IAttachmentReadonlyRepository>().GetListAsync(a => attachmentIds.Contains(a.Id));
                foreach (var item in datas)
                {
                    item.MsaCompanyName = item.TmpMsaCompanyName?.JoinAsString(",");
                    item.MsaBuName = item.TmpMsaBuName?.JoinAsString(",");
                    item.MsaCategoryName = item.TmpMsaCategoryName?.JoinAsString(",");
                    item.PoStatus = item.TmpPoStatus.GetDescription();

                    //MSA附件
                    if (!string.IsNullOrEmpty(item.MsaAttachments))
                    {
                        attachmentIds = item.MsaAttachments.Split(",").Select(Guid.Parse).ToArray();
                        item.MsaAttachments = attachments.Where(a => attachmentIds.Contains(a.Id)).Select(a => a.FileName).JoinAsString(",");
                    }

                    //SOW附件
                    if (!string.IsNullOrEmpty(item.SowAttachments))
                    {
                        attachmentIds = item.SowAttachments.Split(",").Select(Guid.Parse).ToArray();
                        item.SowAttachments = attachments.Where(a => attachmentIds.Contains(a.Id)).Select(a => a.FileName).JoinAsString(",");
                    }
                }
            }

            return datas;
        }

        /// <summary>
        /// 创建Sow
        /// </summary>
        /// <param name="sowPostDto"></param>
        /// <returns></returns>
        public async Task<MessageResult> CreateSowAsync(SowPostDto sowPostDto)
        {
            var sow = ObjectMapper.Map<SowPostDto, MsaStatementOfWork>(sowPostDto);
            var repositorySow = LazyServiceProvider.LazyGetService<IMsaStatementOfWorkRepository>();

            await InsertAndGenerateSerialNoAsync(repositorySow, sow, "SOW");

            return MessageResult.SuccessResult(sow.Id);
        }

        /// <summary>
        /// 更新Sow
        /// </summary>
        /// <param name="sowPostDto"></param>
        /// <returns></returns>
        public async Task<MessageResult> UpdateSowAsync(SowPostDto sowPostDto)
        {
            if (!sowPostDto.Id.HasValue)
                return MessageResult.FailureResult("Sow标识不能为空");

            var repositorySow = LazyServiceProvider.LazyGetService<IMsaStatementOfWorkRepository>();
            var sow = await repositorySow.FindAsync(sowPostDto.Id.Value);
            //更新时，前端没有传入附件信息，这里先将以前的附件设置到dto上，再mapping
            sowPostDto.AttachmentFileIds = sow.AttachmentFileIds;
            sow = ObjectMapper.Map(sowPostDto, sow);

            await repositorySow.UpdateAsync(sow);
            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 删除Sow
        /// </summary>
        /// <param name="sowDeleteDto"></param>
        /// <returns></returns>
        public async Task<MessageResult> DeleteSowAsync(SowDeleteDto sowDeleteDto)
        {
            var queryPo = await LazyServiceProvider.LazyGetService<IPurPOApplicationReadonlyRepository>().GetQueryableAsync();
            var hsaRelatedPo = await queryPo.AnyAsync(a => a.Id != sowDeleteDto.PoId && a.SowId == sowDeleteDto.SowId && a.Status != Enums.Purchase.PurOrderStatus.Invalid && a.Status != Enums.Purchase.PurOrderStatus.Rejected);
            //如果有其他关联的Po，则不删除该sow
            if (hsaRelatedPo)
                return MessageResult.FailureResult("该Sow还有其他关联PO，不能删除");

            await LazyServiceProvider.LazyGetService<IMsaStatementOfWorkRepository>().DeleteAsync(a => a.Id == sowDeleteDto.SowId);
            return MessageResult.SuccessResult();
        }
    }
}
