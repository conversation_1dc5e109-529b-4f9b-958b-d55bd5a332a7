select newid() as spk_NexBPMCode,* into #spk_financialapprovalruleandbu from (
select ft.spk_bu as spk_BPMCode,ot.spk_NexBPMCode as spk_bu,ft1.spk_name+ot.spk_Name  as spk_name,ft1.spk_name as spk_rulename,ft.flg  from spk_financialapprovalruleandbu_Tmp ft
join spk_organizationalmasterdata ot
on ft.spk_bu=ot.spk_BPMCode            --这里数据量减少
join spk_financialapprovalruleconfig_Tmp ft1
on ft.spk_rulename=ft1.spk_BPMCode)A


IF OBJECT_ID(N'dbo.spk_financialapprovalruleandbu', N'U') IS NOT NULL
BEGIN
	update a 
	set a.spk_BPMCode   = b.spk_BPMCode
        ,a.spk_bu       = b.spk_bu
        ,a.spk_name     = b.spk_name
        ,a.spk_rulename = b.spk_rulename
        ,a.flg          = b.flg 
    from dbo.spk_financialapprovalruleandbu a
    join #spk_financialapprovalruleandbu b on a.spk_BPMCode = b.spk_BPMCode
    
    insert into dbo.spk_financialapprovalruleandbu
    select a.spk_NexBPMCode
           ,a.spk_BPMCode
           ,a.spk_bu
           ,a.spk_name
           ,a.spk_rulename
           ,a.flg 
	from #spk_financialapprovalruleandbu a
	where NOT EXISTS (SELECT * FROM dbo.spk_financialapprovalruleandbu where spk_BPMCode = a.spk_BPMCode)
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into dbo.spk_financialapprovalruleandbu from #spk_financialapprovalruleandbu
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
