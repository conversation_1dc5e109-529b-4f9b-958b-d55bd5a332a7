﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Abbott.SpeakerPortal.Contracts.Budget.FocSubbudget
{
    public class UseFocBudgetRequestDto
    {
        /// <summary>
        /// FOC申请单Id
        /// </summary>
        [Required]
        public Guid FocApplicationId { get; set; }
        /// <summary>
        /// 子预算Id
        /// </summary>
        [Required]
        public Guid SubbudgetId { get; set; }
        /// <summary>
        /// 使用详情列表
        /// </summary>
        [Required]
        public IEnumerable<FocUseInfo> Items { get; set; }
    }

    public class FocUseInfo
    {
        /// <summary>
        /// FOC明细的行号
        /// </summary>
        [Required]
        public int RowNo { get; set; }
        /// <summary>
        /// 使用数量
        /// </summary>
        [Required]
        public int UseQty { get; set; }
    }
}
