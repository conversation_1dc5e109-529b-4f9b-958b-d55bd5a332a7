SELECT
 TRY_CONVERT(UNIQUEIDENTIFIER, [Id]) [Id]
,TRY_CONVERT(UNIQUEIDENTIFIER, [AgentConfigId]) [AgentConfigId]
,CASE [WorkflowType]
	WHEN 'SupplierApplication' THEN 1
	WHEN 'ProcurementApplication' THEN 2
	WHEN 'PurchaseOrder' THEN 3
	WHEN 'WaiverApplication' THEN 4
	WHEN 'JustificationApplication' THEN 5
	WHEN 'BiddingApplication' THEN 6
	WHEN 'GoodsReceiving' THEN 7
	WHEN 'PaymentApplication' THEN 8
	WHEN 'SpeakerAuth' THEN 9
	WHEN '' THEN 10
	END [WorkflowType]
,[AgentTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([FormId],'00000000-0000-0000-0000-000000000000')) [FormId]
,ISNULL([FormCode],'NULL') [FormCode]
,[ExtraProperties]
,'NULL' [ConcurrencyStamp]
,GETDATE() [CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CreatorId]) [CreatorId]
,IIF([LastModificationTime] = '',GETDATE(),[LastModificationTime]) [LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [LastModifierId]) [LastModifierId]
,[IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, [DeleterId]) [DeleterId]
,IIF([DeletionTime] = '',GETDATE(),[DeletionTime]) [DeletionTime]
INTO #AgentHistory
FROM (SELECT *,ROW_NUMBER() OVER(PARTITION BY Id ORDER BY Id) RK FROM PLATFORM_ABBOTT_Dev.dbo.AgentHistory)a
WHERE RK = 1
;
--drop table #AgentHistory

USE Speaker_Portal_Dev;

UPDATE a 
SET 
 a.[Id] = b.[Id]
,a.[AgentConfigId] = b.[AgentConfigId]
,a.[WorkflowType] = b.[WorkflowType]
,a.[AgentTime] = b.[AgentTime]
,a.[FormId] = b.[FormId]
,a.[FormCode] = b.[FormCode]
,a.[ExtraProperties] = b.[ExtraProperties]
,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = b.[CreatorId]
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = b.[LastModifierId]
,a.[IsDeleted] = b.[IsDeleted]
,a.[DeleterId] = b.[DeleterId]
,a.[DeletionTime] = b.[DeletionTime]
FROM dbo.AgentHistory a
left join #AgentHistory  b
ON a.id=b.id;


INSERT INTO dbo.AgentHistory
(
 [Id]
,[AgentConfigId]
,[WorkflowType]
,[AgentTime]
,[FormId]
,[FormCode]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
)
SELECT
 [Id]
,[AgentConfigId]
,[WorkflowType]
,[AgentTime]
,[FormId]
,[FormCode]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
FROM #AgentHistory a
WHERE not exists (select * from dbo.AgentHistory where id=a.id);

--truncate table dbo.AgentHistory

--alter table dbo.AgentHistory alter column [FormCode] [nvarchar](50) NOT NULL;