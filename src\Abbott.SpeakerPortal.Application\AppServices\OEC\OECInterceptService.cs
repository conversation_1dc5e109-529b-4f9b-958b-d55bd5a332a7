﻿using Abbott.SpeakerPortal.AppServices.Common;
using Abbott.SpeakerPortal.BackgroundWorkers.BPMEmail;
using Abbott.SpeakerPortal.BackgroundWorkers.Graph;
using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Agent;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.Approve;
using Abbott.SpeakerPortal.Contracts.Common.BPMEmail;
using Abbott.SpeakerPortal.Contracts.Common.Common;
using Abbott.SpeakerPortal.Contracts.Common.PowerApp;
using Abbott.SpeakerPortal.Contracts.OEC.Intercept;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Contracts.Report;
using Abbott.SpeakerPortal.Contracts.System.Slide;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.BPMEmail;
using Abbott.SpeakerPortal.Entities.Common.Workflow.CurrentApprovalTask;
using Abbott.SpeakerPortal.Entities.OEC.OECIntercepts;
using Abbott.SpeakerPortal.Entities.Purchase.PurBDApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurBWApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurCategoryConfig;
using Abbott.SpeakerPortal.Entities.Purchase.PurGRApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPOApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Utils;
using DocumentFormat.OpenXml.Drawing.Charts;
using DocumentFormat.OpenXml.Wordprocessing;
using Hangfire;
using Hangfire.Common;
using Medallion.Threading;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.ApplicationModels;
using Microsoft.Crm.Sdk.Messages;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Logging;
using MiniExcelLibs;
using Senparc.Weixin.WxOpen.AdvancedAPIs.WxApp.WxAppJson;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.BackgroundJobs;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Guids;
using Volo.Abp.Identity;
using static Abbott.SpeakerPortal.Enums.DataverseEnums;
using static Abbott.SpeakerPortal.Enums.DataverseEnums.Returnreason;
using static Abbott.SpeakerPortal.Enums.OECInterceptTypes;
using static Abbott.SpeakerPortal.Enums.Purchase;
using static Abbott.SpeakerPortal.Enums.PurGRStatus;
using static Abbott.SpeakerPortal.Enums.PurPAStatus;
using IConfiguration = Microsoft.Extensions.Configuration.IConfiguration;

namespace Abbott.SpeakerPortal.AppServices.OEC
{
    public class OECInterceptService : SpeakerPortalAppService, IOECInterceptService
    {
        private readonly ILogger<OECInterceptService> _logger;
        private readonly IConfiguration _configuration;
        private readonly IWebHostEnvironment _env;
        private readonly IBackgroundJobManager _backgroundJobManager;
        private readonly IDistributedLockProvider _distributedLockProvider;
        private readonly IServiceProvider _serviceProvider;
        public OECInterceptService(ILogger<OECInterceptService> logger, IConfiguration configuration, IWebHostEnvironment env,
            IBackgroundJobManager backgroundJobManager,
            IDistributedLockProvider distributedLockProvider,
            IServiceProvider serviceProvider)
        {
            _logger = logger;
            _configuration = configuration;
            _env = env;
            _backgroundJobManager = backgroundJobManager;
            _distributedLockProvider = distributedLockProvider;
            _serviceProvider = serviceProvider;
        }
        /// <summary>
        /// 获取拦截列表
        /// </summary>
        /// <param name="InterceptListRequest"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetOECInterceptListDto>> GetOECInterceptListAsync(GetOECInterceptListRequestDto interceptListRequest)
        {
            var result = new PagedResultDto<GetOECInterceptListDto>();
            var queryablePR = (await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            var queryablePRDetail = (await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync()).AsNoTracking();
            var queryablePO = (await LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            var queryablePODetail = (await LazyServiceProvider.LazyGetService<IPurPOApplicationDetailsRepository>().GetQueryableAsync()).AsNoTracking();
            var queryableGR = (await LazyServiceProvider.LazyGetService<IPurGRApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            var queryableGRDetail = (await LazyServiceProvider.LazyGetService<IPurGRApplicationDetailRepository>().GetQueryableAsync()).AsNoTracking();
            var queryablePA = (await LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            var queryablePADetail = (await LazyServiceProvider.LazyGetService<IPurPAApplicationDetailRepository>().GetQueryableAsync()).AsNoTracking();
            var queryableIntercept = (await LazyServiceProvider.LazyGetService<IOECInterceptRepository>().GetQueryableAsync()).AsNoTracking();

            var verify = await GetVerifyAuthorAsync();
            if (!verify.Any())
                return result;

            var paPrdIds = new List<Guid>();
            if (!string.IsNullOrWhiteSpace(interceptListRequest.PAApplicationCode))
            {
                var pas = queryablePA.Where(a => a.Status != PurPAApplicationStatus.Void)
                    .GroupJoin(queryablePADetail, a => a.Id, b => b.PurPAApplicationId, (a, b) => new { pa = a, pads = b })
                    .SelectMany(a => a.pads.DefaultIfEmpty(), (a, b) => new { a.pa, pad = b })
                    .Where(a => a.pa.ApplicationCode == interceptListRequest.PAApplicationCode.Trim())
                    .ToList();
                paPrdIds = pas.Select(a => a.pad.PRDetailId).ToList();
            }

            var query = queryableIntercept.GroupJoin(queryablePRDetail, a => a.PRDetailId, b => b.Id, (a, b) => new { intercept = a, prds = b })
                .SelectMany(a => a.prds.DefaultIfEmpty(), (a, b) => new { a.intercept, prd = b })
                .GroupJoin(queryablePR, a => a.prd.PRApplicationId, b => b.Id, (a, b) => new { a.intercept, a.prd, prs = b })
                .SelectMany(a => a.prs.DefaultIfEmpty(), (a, b) => new { a.intercept, a.prd, pr = b });


            var linqWhere = query.WhereIf(!string.IsNullOrWhiteSpace(interceptListRequest.ApplicationCode), a => a.pr.ApplicationCode.Contains(interceptListRequest.ApplicationCode))
                 .WhereIf(verify.Contains(RoleNames.OEC_Investigator) && verify.Count == 1, a => a.intercept.InterceptTypeCode.Contains("Investigate"))//只有调查角色的仅查询调查相关的
                 .WhereIf(!string.IsNullOrWhiteSpace(interceptListRequest.VendorName), a => a.prd.VendorName.Contains(interceptListRequest.VendorName))
                 .WhereIf(!string.IsNullOrWhiteSpace(interceptListRequest.VendorCode), a => a.prd.VendorCode.Contains(interceptListRequest.VendorCode))
                 .WhereIf(!string.IsNullOrWhiteSpace(interceptListRequest.InterceptTypeCode), a => a.intercept.InterceptTypeCode.Contains(interceptListRequest.InterceptTypeCode))
                 .WhereIf(interceptListRequest.InterceptStatus.HasValue, a => a.intercept.InterceptStatus == interceptListRequest.InterceptStatus)
                 .WhereIf(!string.IsNullOrWhiteSpace(interceptListRequest.InterceptByUserName), a => a.intercept.InterceptByUserName.Contains(interceptListRequest.InterceptByUserName))
                 .WhereIf(interceptListRequest.InterceptStartTime.HasValue, a => a.intercept.InterceptTime.Date >= interceptListRequest.InterceptStartTime.Value.Date)
                 .WhereIf(interceptListRequest.InterceptEndTime.HasValue, a => a.intercept.InterceptTime.Date <= interceptListRequest.InterceptEndTime.Value.Date)
                 .WhereIf(!string.IsNullOrWhiteSpace(interceptListRequest.PAApplicationCode), a => paPrdIds.Contains(a.intercept.PRDetailId))
                 .GroupBy(a => a.intercept.Id)
                 .Select(g => g.OrderByDescending(a => a.intercept.InterceptTime).FirstOrDefault());

            var linqSkip = linqWhere.AsEnumerable().OrderByDescending(a => a.intercept.InterceptTime)
                .Skip(interceptListRequest.PageIndex * interceptListRequest.PageSize).Take(interceptListRequest.PageSize);

            var resultData = linqSkip.ToList()
                .Select(a => new GetOECInterceptListDto
                {
                    InterceptId = a.intercept.Id,
                    PRId = a.pr.Id,
                    ApplicationCode = a.pr.ApplicationCode,
                    PRStatus = a.pr.Status,
                    ApplyTime = a.pr.ApplyTime,
                    ApplyUserName = a.pr.ApplyUserIdName,
                    RowNo = a.prd.RowNo,
                    PayMethod = a.prd.PayMethod,
                    TotalAmount = a.prd.TotalAmount,
                    TotalAmountRMB = a.prd.TotalAmountRMB,
                    VendorName = a.prd.VendorName,
                    VendorCode = a.prd.VendorCode,
                    //POId = a.po?.Id,
                    //POApplicationCode = a.po?.ApplicationCode,
                    //GRId = a.gr?.Id,
                    //GRApplicationCode = a.gr?.ApplicationCode,
                    PRDetailId = a.intercept.PRDetailId,
                    InterceptTypeCode = a.intercept.InterceptTypeCode,
                    InterceptStatus = a.intercept.InterceptStatus,
                    InterceptByUserId = a.intercept.InterceptByUserId,
                    InterceptByUserName = a.intercept.InterceptByUserName,
                    InterceptTime = a.intercept.InterceptTime,
                    InterceptRemark = a.intercept.InterceptRemark,
                    ResolvedInterceptType = a.intercept.ResolvedInterceptType,
                }).ToList();
            if (resultData.Any())
            {
                var prDetailIds = resultData.Select(x => x.PRDetailId).ToList();
                var pas = QueryPAInfo(queryablePA, queryablePADetail).Where(a => prDetailIds.Contains(a.PRDetailId.Value) && a.PAStatus != PurPAApplicationStatus.Void)
                    .WhereIf(!string.IsNullOrWhiteSpace(interceptListRequest.PAApplicationCode), a => a.ApplicationCode == interceptListRequest.PAApplicationCode)
                    .ToList();//排除作废的
                PurOrderStatus[] poStatus = [PurOrderStatus.Draft, PurOrderStatus.Invalid];
                var pos = QueryPOInfo(queryablePO, queryablePODetail).Where(a => prDetailIds.Contains(a.PRDetailId.Value) && !poStatus.Contains(a.POStatus.Value)).ToList();
                var grInfo = QueryGRInfo(queryableGR, queryableGRDetail).Where(a => prDetailIds.Contains(a.PRDetailId.Value)).ToList();
                foreach (var pr in resultData)
                {
                    var paInfos = new List<PAApplicationInfoDto>();
                    var thisPA = pas.Where(a => a.PRDetailId == pr.PRDetailId).GroupBy(a => a.Id).Select(a => a.FirstOrDefault()).ToList();
                    foreach (var pa in thisPA)
                    {
                        paInfos.Add(new PAApplicationInfoDto()
                        {
                            PAId = pa.Id.Value,
                            ApplicationCode = pa.ApplicationCode,
                            Status = pa.PAStatus.Value,
                            PayTotalAmount = pa.TotalAmount * (decimal)pa.ExchangeRate,
                            DeliveryMode = pa.DeliveryMode,
                            PayPrDetailTotalAmount = pas.Where(a => a.Id == pa.Id && a.PRDetailId == pa.PRDetailId).Sum(a => a.PADTotalAmount * (decimal)a.ExchangeRate)
                        });
                    }
                    pr.PayTotalAmount = thisPA.Sum(a => a.TotalAmount * (decimal)a.ExchangeRate);
                    pr.PayPrDetailTotalAmount = paInfos.Sum(a => a.PayPrDetailTotalAmount);
                    pr.PAInfo = paInfos;
                    var po = pos.Where(a => a.PRDetailId == pr.PRDetailId).FirstOrDefault();
                    pr.POId = po?.Id;
                    pr.POApplicationCode = po?.ApplicationCode;
                    var gr = grInfo.Where(a => a.PRDetailId == pr.PRDetailId).FirstOrDefault();
                    pr.GRId = gr?.Id;
                    pr.GRApplicationCode = gr?.ApplicationCode;
                }
            }

            result.TotalCount = linqWhere.Count();
            result.Items = resultData;
            return result;
        }

        public async Task<PagedResultDto<GetOECInterceptListDto>> GetOECInterceptListAsync_back(GetOECInterceptListRequestDto interceptListRequest)
        {
            var result = new PagedResultDto<GetOECInterceptListDto>();
            var queryablePR = (await LazyServiceProvider.LazyGetService<IPurPRApplicationReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
            var queryablePRDetail = (await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
            var queryablePO = (await LazyServiceProvider.LazyGetService<IPurPOApplicationReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
            var queryablePODetail = (await LazyServiceProvider.LazyGetService<IPurPOApplicationDetailsReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
            var queryableGR = (await LazyServiceProvider.LazyGetService<IPurGRApplicationReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
            var queryableGRDetail = (await LazyServiceProvider.LazyGetService<IPurGRApplicationDetailReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
            var queryablePA = (await LazyServiceProvider.LazyGetService<IPurPAApplicationReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
            var queryablePADetail = (await LazyServiceProvider.LazyGetService<IPurPAApplicationDetailReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
            var queryableIntercept = (await LazyServiceProvider.LazyGetService<IOECInterceptReadonlyRepository>().GetQueryableAsync()).AsNoTracking();

            var verify = await GetVerifyAuthorAsync();
            if (!verify.Any())
                return result;
            var prInfo = QueryPRInfo(queryablePR, queryablePRDetail);
            var poInfo = QueryPOInfo(queryablePO, queryablePODetail);
            var grInfo = QueryGRInfo(queryableGR, queryableGRDetail);

            var paPrdIds = new List<Guid>();
            if (!string.IsNullOrWhiteSpace(interceptListRequest.PAApplicationCode))
            {
                var pas = queryablePA.Where(a => a.Status != PurPAApplicationStatus.Void)
                    .GroupJoin(queryablePADetail, a => a.Id, b => b.PurPAApplicationId, (a, b) => new { pa = a, pads = b })
                    .SelectMany(a => a.pads.DefaultIfEmpty(), (a, b) => new { a.pa, pad = b })
                    .Where(a => a.pa.ApplicationCode == interceptListRequest.PAApplicationCode.Trim())
                    .ToList();
                paPrdIds = pas.Select(a => a.pad.PRDetailId).ToList();
            }

            var query = queryableIntercept.GroupJoin(prInfo, a => a.PRDetailId, b => b.PRDetailId, (a, b) => new { intercept = a, prs = b })
                .SelectMany(a => a.prs.DefaultIfEmpty(), (a, b) => new { a.intercept, pr = b })
                .GroupJoin(poInfo, a => a.intercept.PRDetailId, b => b.PRDetailId, (a, b) => new { a.intercept, a.pr, pos = b })
                .SelectMany(a => a.pos.DefaultIfEmpty(), (a, b) => new { a.intercept, a.pr, po = b })
                .GroupJoin(grInfo, a => a.intercept.PRDetailId, b => b.PRDetailId, (a, b) => new { a.intercept, a.pr, a.po, grs = b })
                .SelectMany(a => a.grs.DefaultIfEmpty(), (a, b) => new { a.intercept, a.pr, a.po, gr = b });


            var linqWhere = query.WhereIf(!string.IsNullOrWhiteSpace(interceptListRequest.ApplicationCode), a => a.pr.ApplicationCode.Contains(interceptListRequest.ApplicationCode))
                 .WhereIf(verify.Contains(RoleNames.OEC_Investigator) && verify.Count == 1, a => a.intercept.InterceptTypeCode.Contains("Investigate"))//只有调查角色的仅查询调查相关的
                 .WhereIf(!string.IsNullOrWhiteSpace(interceptListRequest.VendorName), a => a.pr.VendorName.Contains(interceptListRequest.VendorName))
                 .WhereIf(!string.IsNullOrWhiteSpace(interceptListRequest.VendorCode), a => a.pr.VendorCode.Contains(interceptListRequest.VendorCode))
                 .WhereIf(!string.IsNullOrWhiteSpace(interceptListRequest.InterceptTypeCode), a => a.intercept.InterceptTypeCode.Contains(interceptListRequest.InterceptTypeCode))
                 .WhereIf(interceptListRequest.InterceptStatus.HasValue, a => a.intercept.InterceptStatus == interceptListRequest.InterceptStatus)
                 .WhereIf(!string.IsNullOrWhiteSpace(interceptListRequest.InterceptByUserName), a => a.intercept.InterceptByUserName.Contains(interceptListRequest.InterceptByUserName))
                 .WhereIf(interceptListRequest.InterceptStartTime.HasValue, a => a.intercept.InterceptTime.Date >= interceptListRequest.InterceptStartTime.Value.Date)
                 .WhereIf(interceptListRequest.InterceptEndTime.HasValue, a => a.intercept.InterceptTime.Date <= interceptListRequest.InterceptEndTime.Value.Date)
                 .WhereIf(!string.IsNullOrWhiteSpace(interceptListRequest.PAApplicationCode), a => paPrdIds.Contains(a.intercept.PRDetailId))
                 .GroupBy(a => a.intercept.Id)
                 .Select(g => g.OrderByDescending(a => a.intercept.InterceptTime).FirstOrDefault());

            var linqSkip = linqWhere.AsEnumerable().OrderByDescending(a => a.intercept.InterceptTime)
                .Skip(interceptListRequest.PageIndex * interceptListRequest.PageSize).Take(interceptListRequest.PageSize);

            var resultData = linqSkip.ToList()
                .Select(a => new GetOECInterceptListDto
                {
                    InterceptId = a.intercept.Id,
                    PRId = a.pr.Id.Value,
                    ApplicationCode = a.pr.ApplicationCode,
                    PRStatus = a.pr.PRStatus,
                    ApplyTime = a.pr.ApplyTime,
                    ApplyUserName = a.pr.ApplyUserName,
                    RowNo = a.pr.RowNo,
                    PayMethod = a.pr.PayMethod,
                    TotalAmount = a.pr.TotalAmount,
                    TotalAmountRMB = a.pr.TotalAmountRMB,
                    VendorName = a.pr.VendorName,
                    VendorCode = a.pr.VendorCode,
                    POId = a.po?.Id,
                    POApplicationCode = a.po?.ApplicationCode,
                    GRId = a.gr?.Id,
                    GRApplicationCode = a.gr?.ApplicationCode,
                    PRDetailId = a.intercept.PRDetailId,
                    InterceptTypeCode = a.intercept.InterceptTypeCode,
                    InterceptStatus = a.intercept.InterceptStatus,
                    InterceptByUserId = a.intercept.InterceptByUserId,
                    InterceptByUserName = a.intercept.InterceptByUserName,
                    InterceptTime = a.intercept.InterceptTime,
                    InterceptRemark = a.intercept.InterceptRemark,
                    ResolvedInterceptType = a.intercept.ResolvedInterceptType,
                }).ToList();
            if (resultData.Any())
            {
                var prDetailIds = resultData.Select(x => x.PRDetailId).ToList();
                var pas = QueryPAInfo(queryablePA, queryablePADetail).Where(a => prDetailIds.Contains(a.PRDetailId.Value) && a.PAStatus != PurPAApplicationStatus.Void)
                    .WhereIf(!string.IsNullOrWhiteSpace(interceptListRequest.PAApplicationCode), a => a.ApplicationCode == interceptListRequest.PAApplicationCode)
                    .ToList();//排除作废的
                foreach (var pr in resultData)
                {
                    var paInfos = new List<PAApplicationInfoDto>();
                    var thisPA = pas.Where(a => a.PRDetailId == pr.PRDetailId).GroupBy(a => a.Id).Select(a => a.FirstOrDefault()).ToList();
                    foreach (var pa in thisPA)
                    {
                        paInfos.Add(new PAApplicationInfoDto()
                        {
                            PAId = pa.Id.Value,
                            ApplicationCode = pa.ApplicationCode,
                            Status = pa.PAStatus.Value,
                            PayTotalAmount = pa.TotalAmount * (decimal)pa.ExchangeRate,
                            DeliveryMode = pa.DeliveryMode,
                            PayPrDetailTotalAmount = pas.Where(a => a.Id == pa.Id && a.PRDetailId == pa.PRDetailId).Sum(a => a.PADTotalAmount * (decimal)a.ExchangeRate)
                        });
                    }
                    pr.PayTotalAmount = thisPA.Sum(a => a.TotalAmount * (decimal)a.ExchangeRate);
                    pr.PayPrDetailTotalAmount = paInfos.Sum(a => a.PayPrDetailTotalAmount);
                    pr.PAInfo = paInfos;
                }
            }

            result.TotalCount = linqWhere.Count();
            result.Items = resultData;
            return result;
        }

        /// <summary>
        /// 获取拦截列表(导出)
        /// </summary>
        /// <param name="InterceptListRequest"></param>
        /// <returns></returns>
        public async Task<Stream> ExportOECInterceptListAsync(GetOECInterceptListRequestDto interceptListRequest)
        {
            try
            {
                //var interceptList = await QeuryeExportOECInterceptListAsync(interceptListRequest);
                var interceptList = await ExportOECInterceptAsync(interceptListRequest);
                MemoryStream stream = new();
                stream.SaveAs(interceptList, true, "SheetName");
                stream.Seek(0, SeekOrigin.Begin);
                return stream;
            }
            catch (Exception ex)
            {
                _logger.LogError($"ExportSlideConfigListAsync error:{ex.Message}");
                return null;
            }
        }
        private async Task<List<ExportOECInterceptDto>> ExportOECInterceptAsync(GetOECInterceptListRequestDto request)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();

            if (!request.InterceptStartTime.HasValue || !request.InterceptEndTime.HasValue)
            {
                request.InterceptStartTime = DateTime.Now.Date.AddYears(-1);
                request.InterceptEndTime = DateTime.Now.Date;
            }
            var dateTimes = DatetimeHelper.GetQueryTimeList(request.InterceptStartTime.Value.Date, request.InterceptEndTime.Value.Date, 20);

            var interceptTypes = await dataverseService.GetDictionariesAsync(DictionaryType.InterceptTypes);

            var allResults = new ConcurrentBag<ExportOECInterceptDto>(); // 使用 ConcurrentBag 避免锁竞争
            var errorTimes = new ConcurrentBag<KeyValuePair<DateTime, DateTime>>();
            var tasks = new List<Task>();

            using (var semaphoreSlim = new SemaphoreSlim(20))//可同时执行20个线程
            {
                foreach (var item in dateTimes)
                {
                    tasks.Add(QueryPoExportDataAsync(request, interceptTypes.ToList(), allResults, errorTimes, semaphoreSlim, item));
                }
                await Task.WhenAll(tasks);
                //后续可优化，错误的再次查询  errorTimes
            }

            var result = allResults.ToList();
            return result;
        }

        private async Task QueryPoExportDataAsync(GetOECInterceptListRequestDto request, List<DictionaryDto> interceptTypes,
            ConcurrentBag<ExportOECInterceptDto> allResults, ConcurrentBag<KeyValuePair<DateTime, DateTime>> errorTimes,
            SemaphoreSlim semaphore, KeyValuePair<DateTime, DateTime> dateTime)
        {
            await semaphore.WaitAsync();
            try
            {
                using (var scope = _serviceProvider.CreateScope())
                {
                    var queryableIntercept = (await scope.ServiceProvider.GetRequiredService<IOECInterceptReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
                    var queryablePR = (await scope.ServiceProvider.GetRequiredService<IPurPRApplicationReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
                    var queryablePRDetail = (await scope.ServiceProvider.GetRequiredService<IPurPRApplicationDetailReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
                    var queryablePO = (await scope.ServiceProvider.GetRequiredService<IPurPOApplicationReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
                    var queryablePODetail = (await scope.ServiceProvider.GetRequiredService<IPurPOApplicationDetailsReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
                    var queryableGR = (await scope.ServiceProvider.GetRequiredService<IPurGRApplicationReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
                    var queryableGRDetail = (await scope.ServiceProvider.GetRequiredService<IPurGRApplicationDetailReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
                    var queryablePA = (await scope.ServiceProvider.GetRequiredService<IPurPAApplicationReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
                    var queryablePADetail = (await scope.ServiceProvider.GetRequiredService<IPurPAApplicationDetailReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
                    var prInfo = QueryPRInfo(queryablePR, queryablePRDetail);
                    var poInfo = QueryPOInfo(queryablePO, queryablePODetail);
                    var grInfo = QueryGRInfo(queryableGR, queryableGRDetail);
                    var query = queryableIntercept.Where(a => a.InterceptTime >= dateTime.Key && a.InterceptTime <= dateTime.Value)
                        .GroupJoin(prInfo, a => a.PRDetailId, b => b.PRDetailId, (a, b) => new { intercept = a, prs = b })
                        .SelectMany(a => a.prs.DefaultIfEmpty(), (a, b) => new { a.intercept, pr = b })
                        .GroupJoin(poInfo, a => a.intercept.PRDetailId, b => b.PRDetailId, (a, b) => new { a.intercept, a.pr, pos = b })
                        .SelectMany(a => a.pos.DefaultIfEmpty(), (a, b) => new { a.intercept, a.pr, po = b })
                        .GroupJoin(grInfo, a => a.intercept.PRDetailId, b => b.PRDetailId, (a, b) => new { a.intercept, a.pr, a.po, grs = b })
                        .SelectMany(a => a.grs.DefaultIfEmpty(), (a, b) => new { a.intercept, a.pr, a.po, gr = b })
                        .GroupJoin(queryablePA.Where(a => a.Status != PurPAApplicationStatus.Void), a => a.gr.Id, b => b.GRId, (a, b) => new { a.intercept, a.pr, a.po, a.gr, pas = b })
                        .SelectMany(a => a.pas.DefaultIfEmpty(), (a, b) => new { a.intercept, a.pr, a.po, a.gr, pa = b });

                    var linqWhere = query.WhereIf(!string.IsNullOrWhiteSpace(request.ApplicationCode), a => a.pr.ApplicationCode.Contains(request.ApplicationCode))
                         .WhereIf(!string.IsNullOrWhiteSpace(request.VendorName), a => a.pr.VendorName.Contains(request.VendorName))
                         .WhereIf(!string.IsNullOrWhiteSpace(request.VendorCode), a => a.pr.VendorCode.Contains(request.VendorCode))
                         .WhereIf(!string.IsNullOrWhiteSpace(request.InterceptTypeCode), a => a.intercept.InterceptTypeCode.Contains(request.InterceptTypeCode))
                         .WhereIf(request.InterceptStatus.HasValue, a => a.intercept.InterceptStatus == request.InterceptStatus)
                         .WhereIf(!string.IsNullOrWhiteSpace(request.InterceptByUserName), a => a.intercept.InterceptByUserName.Contains(request.InterceptByUserName))
                         .WhereIf(!string.IsNullOrWhiteSpace(request.PAApplicationCode), a => a.pa.ApplicationCode.Contains(request.PAApplicationCode))
                         .GroupBy(a => a.intercept.Id)
                         .Select(g => g.OrderByDescending(a => a.intercept.InterceptTime).FirstOrDefault());

                    var linqSkip = linqWhere.AsEnumerable().ToList();

                    var paIds = linqSkip.Where(a => a.pa != null).Select(a => a.pa.Id).ToList();
                    var pads = queryablePADetail.Where(a => paIds.Contains(a.PurPAApplicationId)).ToList();

                    var datas = linqSkip.Select(a =>
                    {
                        decimal paAmountRMB = 0M;
                        if (a.pa != null)
                        {
                            var pad = pads.Where(x => x.PurPAApplicationId == a.pa.Id && a.intercept.PRDetailId == x.PRDetailId).ToList();
                            decimal rmb = pad.Sum(a => a.PaymentAmount) * (decimal)(a.pa?.ExchangeRate ?? 1L);
                            paAmountRMB = Math.Round(rmb, 2, MidpointRounding.AwayFromZero);
                        }

                        return new ExportOECInterceptDto
                        {
                            ApplicationCode = a.pr.ApplicationCode,
                            RowNo = a.pr.RowNo,
                            ApplyTime = a.pr.ApplyTime?.ToString("yyyy-MM-dd HH:mm:ss"),
                            ApplyUserName = a.pr.ApplyUserName,
                            TotalAmountRMB = Math.Round(a.pr.TotalAmountRMB ?? 0M, 2, MidpointRounding.AwayFromZero),
                            CompanyName = a.pr.CompanyName,
                            VendorCode = a.pr.VendorCode,
                            VendorName = a.pr.VendorName,
                            StatusText = a.pr.PRStatus.GetDescription(),
                            POApplicationCode = a.po?.ApplicationCode,
                            GRApplicationCode = a.gr?.ApplicationCode,
                            PAApplicationCode = a.pa?.ApplicationCode,
                            PAStatusText = a.pa == null ? "" : a.pa?.Status.GetDescription(),
                            PATotalAmountRMB = Math.Round((a.pa?.PayTotalAmount ?? 0M) * (decimal)(a.pa?.ExchangeRate ?? 0L), 2, MidpointRounding.AwayFromZero),
                            PAAmountRMB = paAmountRMB,
                            DeliveryModeText = a.pa?.DeliveryMode.GetDescription(),
                            InterceptTypeCode = GetInterceptTypeName(interceptTypes, a.intercept.InterceptTypeCode),
                            InterceptByUserName = a.intercept.InterceptByUserName,
                            InterceptTime = a.intercept.InterceptTime.ToString("yyyy-MM-dd"),
                            ReleasedSendBackedDate = a.intercept.InterceptStatus == InterceptStatus.Intercepting ? "" : a.intercept.LastModificationTime?.ToString("yyyy-MM-dd"),
                            InterceptStatusText = a.intercept.InterceptStatus.GetDescription(),
                        };
                    }).ToList();

                    foreach (var item in datas)
                    {
                        allResults.Add(item);
                    }
                }
            }
            catch (Exception ex)
            {
                errorTimes.Add(dateTime);
            }
            finally
            {
                semaphore.Release();
            }
        }
        /// <summary>
        /// 查询可拦截PR单
        /// </summary>
        /// <param name="interceptDetailsContent"></param>
        /// <returns></returns>
        public async Task<MessageResult> QueryWaitInterceptListAsync(QueryInterceptRequestDto interceptDetailsContent)
        {
            byte[] bytes = Convert.FromBase64String(interceptDetailsContent.InterceptDetailsContent);
            string decodedString = Encoding.UTF8.GetString(bytes);
            interceptDetailsContent.InterceptDetailsContent = decodedString;
            var queryablePR = (await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            var queryablePRdetail = (await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync()).AsNoTracking();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var queryablePA = (await LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            var queryablePADetail = (await LazyServiceProvider.LazyGetService<IPurPAApplicationDetailRepository>().GetQueryableAsync()).AsNoTracking();
            if (string.IsNullOrWhiteSpace(interceptDetailsContent.InterceptDetailsContent))
                return MessageResult.FailureResult("拦截明细查询内容未填写");
            var verifyContent = GetInterceptQueryDetails(interceptDetailsContent.InterceptDetailsContent);
            var detailContent = new List<InterceptQueryDetailDto>();
            if (verifyContent.Item1)
            {
                detailContent = verifyContent.Item2;
            }
            else
            {
                return MessageResult.FailureResult("请检查数据格式是否正确");
            }
            if (detailContent.Any(a => string.IsNullOrWhiteSpace(a.ApplicationCode)))
                return MessageResult.FailureResult("请检查数据，PR单号为必填");
            var prList = queryablePR.Where(a => detailContent.Select(x => x.ApplicationCode).Contains(a.ApplicationCode)).ToList();//根据PR单号查询所有PR数据

            //排除反冲和非反冲行  只要AR 或AP
            var hedgePrDetails = queryablePRdetail.Where(m => m.IsHedge == false && (m.PayMethod == PayMethods.AP || m.PayMethod == PayMethods.AR));

            var querPRDetails = queryablePR.Select(a => new { a.Id, a.ApplicationCode, a.Status, a.ApplyTime, a.IsDeleted }).AsQueryable()
                .GroupJoin(hedgePrDetails.Select(a => new { a.Id, a.PRApplicationId, a.HedgePrDetailId, a.PayMethod, a.VendorName, a.RowNo, a.TotalAmountRMB, a.VendorCode, a.IsDeleted }),
                           a => a.Id, b => b.PRApplicationId, (a, b) => new { pr = a, prds = b })
                .SelectMany(a => a.prds.DefaultIfEmpty(), (a, b) => new { a.pr, prd = b })
                .Where(a => detailContent.Select(x => x.ApplicationCode).Contains(a.pr.ApplicationCode)).AsQueryable();

            var prs = querPRDetails.ToList();
            var prDetailIds = prs.Select(x => x.prd.Id).ToList();

            var pas = queryablePA.Select(a => new { a.Id, a.ApplicationCode, a.Status, a.PayTotalAmount, a.DeliveryMode, a.IsDeleted })
                .GroupJoin(queryablePADetail.Select(a => new { a.Id, a.PRDetailId, a.PurPAApplicationId, a.PaymentAmount, a.IsDeleted }),
                           a => a.Id, b => b.PurPAApplicationId, (a, b) => new { pa = a, pads = b })
                .SelectMany(a => a.pads.DefaultIfEmpty(), (a, b) => new { a.pa, pad = b })
                .Where(a => prDetailIds.Contains(a.pad.PRDetailId) && a.pa.Status != PurPAApplicationStatus.Void)// 排除PA作废
                .ToList();
            //当PR单号及供应商名称均填写时，如果该组合不存在于系统内任何审批完毕(状态为"供应商确认" / "等待关闭" / "已完成")的采购申请明细行则显示为匹配失败，否则为成功；
            List<GetWaitInterceptListDto> result = new List<GetWaitInterceptListDto>();
            var interceptTypes = await dataverseService.GetDictionariesAsync(DictionaryType.InterceptTypes);
            foreach (var item in detailContent)
            {
                List<string> typeCodes = new List<string>();
                if (!string.IsNullOrWhiteSpace(item.InterceptTypeCode))
                {
                    var typeNames = item.InterceptTypeCode.Split(',');
                    foreach (var typeName in typeNames)
                    {
                        var thisType = interceptTypes.Where(a => a.Name == typeName).FirstOrDefault();
                        if (thisType != null)
                        {
                            typeCodes.Add(thisType.Code);
                        }
                    }
                }
                PurPRApplicationStatus[] completed = [PurPRApplicationStatus.VendorConfirmed, PurPRApplicationStatus.WaitForClose, PurPRApplicationStatus.Approved, PurPRApplicationStatus.Closed, PurPRApplicationStatus.PushToPurchase];
                if (!string.IsNullOrWhiteSpace(item.ApplicationCode) && !string.IsNullOrWhiteSpace(item.VendorName))
                {
                    var thisPr = prs.Where(a => a.pr.ApplicationCode == item.ApplicationCode && a.prd.VendorName == item.VendorName && completed.Contains(a.pr.Status)).ToList();
                    if (thisPr.Any())
                    {
                        foreach (var i in thisPr)
                        {
                            var paInfos = new List<PAApplicationInfoDto>();
                            var paInfo = pas.Where(a => a.pad.PRDetailId == i.prd.Id).GroupBy(a => a.pa.Id).Select(a => a.FirstOrDefault()).ToList();
                            foreach (var pa in paInfo)
                            {
                                paInfos.Add(new PAApplicationInfoDto()
                                {
                                    PAId = pa.pa.Id,
                                    ApplicationCode = pa.pa.ApplicationCode,
                                    Status = pa.pa.Status,
                                    PayTotalAmount = pa.pa.PayTotalAmount,
                                    DeliveryMode = pa.pa.DeliveryMode,
                                    PayPrDetailTotalAmount = pas.Where(a => a.pa.Id == pa.pa.Id && a.pad.PRDetailId == pa.pad.PRDetailId).Sum(a => a.pad.PaymentAmount),
                                });
                            }
                            result.Add(new GetWaitInterceptListDto()
                            {
                                PRDetailId = i.prd.Id,
                                PRId = i.pr.Id,
                                ApplicationCode = i.pr.ApplicationCode,
                                ApplyTime = i.pr.ApplyTime,
                                RowNo = i.prd.RowNo,
                                TotalAmountRMB = i.prd.TotalAmountRMB,
                                PRStatus = i.pr.Status,
                                VendorName = i.prd.VendorName,
                                VendorCode = i.prd.VendorCode,
                                InterceptTypeCodes = typeCodes.ToArray(),
                                InterceptRemark = item.InterceptRemark,
                                AdaptationResults = AdaptationResults.Success,
                                PAInfo = paInfos,
                            });
                        }
                    }
                    else
                    {
                        result.Add(new GetWaitInterceptListDto()
                        {
                            ApplicationCode = item.ApplicationCode,
                            VendorName = item.VendorName,
                            AdaptationResults = AdaptationResults.Failure,
                            InterceptRemark = item.InterceptRemark,
                        });
                    }
                }
                else
                {
                    var thisPr = prs.Where(a => a.pr.ApplicationCode == item.ApplicationCode && completed.Contains(a.pr.Status)).ToList();
                    if (thisPr.Any())
                    {
                        foreach (var i in thisPr)
                        {
                            var paInfos = new List<PAApplicationInfoDto>();
                            var paInfo = pas.Where(a => a.pad.PRDetailId == i.prd.Id).GroupBy(a => a.pa.Id).Select(a => a.FirstOrDefault()).ToList();
                            foreach (var pa in paInfo)
                            {
                                paInfos.Add(new PAApplicationInfoDto()
                                {
                                    PAId = pa.pa.Id,
                                    ApplicationCode = pa.pa.ApplicationCode,
                                    Status = pa.pa.Status,
                                    PayTotalAmount = pa.pa.PayTotalAmount,
                                    DeliveryMode = pa.pa.DeliveryMode,
                                    PayPrDetailTotalAmount = pas.Where(a => a.pa.Id == pa.pa.Id && a.pad.PRDetailId == pa.pad.PRDetailId).Sum(a => a.pad.PaymentAmount),
                                });
                            }
                            result.Add(new GetWaitInterceptListDto()
                            {
                                PRDetailId = i.prd.Id,
                                PRId = i.pr.Id,
                                ApplicationCode = i.pr.ApplicationCode,
                                ApplyTime = i.pr.ApplyTime,
                                RowNo = i.prd.RowNo,
                                TotalAmountRMB = i.prd.TotalAmountRMB,
                                PRStatus = i.pr.Status,
                                VendorName = i.prd.VendorName,
                                VendorCode = i.prd.VendorCode,
                                InterceptTypeCodes = typeCodes.ToArray(),
                                InterceptRemark = item.InterceptRemark,
                                AdaptationResults = AdaptationResults.Success,
                                PAInfo = paInfos,
                            });
                        }
                    }
                    else
                    {
                        result.Add(new GetWaitInterceptListDto()
                        {
                            ApplicationCode = item.ApplicationCode,
                            VendorName = item.VendorName,
                            AdaptationResults = AdaptationResults.Failure,
                            InterceptRemark = item.InterceptRemark,
                        });
                    }
                }
            }
            return MessageResult.SuccessResult(result);
        }

        /// <summary>
        /// 提交拦截
        /// </summary>
        /// <param name="submitInterceptList"></param>
        /// <returns></returns>
        public async Task<MessageResult> SubmitInterceptListAsync(List<GetWaitInterceptListDto> submitInterceptList)
        {
            var paRepository = LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>();
            var queryablePA = await paRepository.GetQueryableAsync();
            var queryablePADetail = await LazyServiceProvider.LazyGetService<IPurPAApplicationDetailRepository>().GetQueryableAsync();
            var interceptRepository = LazyServiceProvider.LazyGetService<IOECInterceptRepository>();
            var queryIntercept = await interceptRepository.GetQueryableAsync();
            var perateHistorys = new List<InsertOperateHistoryDto>();

            Guid userId = CurrentUser.Id.Value;
            string userName = CurrentUser.Name;

            foreach (var interceptor in submitInterceptList)
            {
                _logger.LogError($"SubmitInterceptListAsync:PRDetailId {interceptor.PRDetailId}");
                if (!interceptor.InterceptTypeCodes.Any())
                    continue;
                if (queryIntercept.Any(a => a.PRDetailId == interceptor.PRDetailId.Value))
                {
                    _logger.LogError($"SubmitInterceptListAsync:Update {interceptor.PRDetailId}");
                    var tistUpdate = queryIntercept.FirstOrDefault(a => a.PRDetailId == interceptor.PRDetailId.Value);
                    string oldCodes = tistUpdate.InterceptTypeCode;
                    tistUpdate.InterceptTypeCode = string.Join(",", interceptor.InterceptTypeCodes);
                    tistUpdate.InterceptStatus = InterceptStatus.Intercepting;
                    tistUpdate.InterceptByUserId = userId;
                    tistUpdate.InterceptByUserName = userName;
                    tistUpdate.InterceptTime = DateTime.Now;
                    tistUpdate.InterceptRemark = interceptor.InterceptRemark;
                    tistUpdate.InterceptTime = DateTime.Now;
                    tistUpdate.InterceptStatus = InterceptStatus.Intercepting;
                    tistUpdate.ResolvedInterceptType = "";
                    await interceptRepository.UpdateAsync(tistUpdate);
                    perateHistorys.Add(new InsertOperateHistoryDto()
                    {
                        InterceptId = tistUpdate.Id,
                        PRDetailId = tistUpdate.PRDetailId,
                        InterceptTypeCodes = interceptor.InterceptTypeCodes,
                        OriginalInterceptTypeCodes = oldCodes.Split(','),
                        Remark = tistUpdate.InterceptRemark,
                        OperateType = InterceptOperateType.Intercept,
                        OperateUserId = userId,
                        OperateUserName = userName,
                    });
                }
                else
                {
                    _logger.LogError($"SubmitInterceptListAsync:Insert {interceptor.PRDetailId}");
                    var newOECIntercept = new OECIntercept();
                    newOECIntercept.PRDetailId = interceptor.PRDetailId.Value;
                    newOECIntercept.InterceptTypeCode = string.Join(",", interceptor.InterceptTypeCodes);
                    newOECIntercept.InterceptStatus = InterceptStatus.Intercepting;
                    newOECIntercept.InterceptByUserId = userId;
                    newOECIntercept.InterceptByUserName = userName;
                    newOECIntercept.InterceptTime = DateTime.Now;
                    newOECIntercept.InterceptRemark = interceptor.InterceptRemark;
                    var insert = await interceptRepository.InsertAsync(newOECIntercept);
                    perateHistorys.Add(new InsertOperateHistoryDto()
                    {
                        InterceptId = insert.Id,
                        PRDetailId = newOECIntercept.PRDetailId,
                        InterceptTypeCodes = interceptor.InterceptTypeCodes,
                        Remark = newOECIntercept.InterceptRemark,
                        OperateType = InterceptOperateType.Intercept,
                        OperateUserId = userId,
                        OperateUserName = userName,
                    });
                }
            }
            await InsertOperateHistoryAsync(perateHistorys);
            _logger.LogError($"SubmitInterceptListAsync:perateHistorys{perateHistorys.Count()}");
            var pas = queryablePA.GroupJoin(queryablePADetail, a => a.Id, b => b.PurPAApplicationId, (a, b) => new { pa = a, pads = b })
                .SelectMany(a => a.pads.DefaultIfEmpty(), (a, b) => new { a.pa, pad = b })
                .Where(a => submitInterceptList.Select(x => x.PRDetailId.Value).Contains(a.pad.PRDetailId) && a.pa.Status != PurPAApplicationStatus.Void)//排除作废
                .ToList();
            _logger.LogError($"SubmitInterceptListAsync:pas {string.Join(',', pas.Select(a => a.pa.ApplicationCode).ToArray())}");
            if (!submitInterceptList.Any())
                return MessageResult.SuccessResult();
            pas = pas.GroupBy(a => a.pa.Id).Select(a => a.FirstOrDefault()).ToList();
            var sendEmaillRecords = new List<InsertSendEmaillRecordDto>();
            foreach (var pa in pas)
            {
                if (pa.pa.Status == PurPAApplicationStatus.WaitingForPayment)
                {
                    var record = await SendEmailToFinancialInitialApprovalAsync(pa.pa, InterceptStatus.Intercepting, userName);
                    if (record != null)
                        sendEmaillRecords.Add(record);
                }
                else if (pa.pa.Status == PurPAApplicationStatus.FinancialPreliminaryReview || pa.pa.Status == PurPAApplicationStatus.FinancialReview)
                {
                    //当被拦截的单据对应PA状态为"财务初审/复审"时，代表单据已推送至Hub团队审核，此时该单据会被自动打回至"单据接收"状态，且系统自动生成处理记录，备注为"审批中"
                    var approveService = LazyServiceProvider.LazyGetService<IApproveService>();
                    var approvers = approveService.GetApproverByFormId(pa.pa.Id);
                    if (!approvers.Any())
                        continue;//没有审批人
                    var approver = approvers.FirstOrDefault(a => a.Id == CurrentUser.Id.Value);
                    if (approver == null)
                        approver = approvers.FirstOrDefault();
                    var updateApproval = new UpdateApprovalDto();
                    updateApproval.BusinessFormId = pa.pa.Id.ToString();
                    updateApproval.Submitter = approver.Id;//提交人
                    updateApproval.Remark = "审批中";
                    updateApproval.OperationStatus = ApprovalOperation.Withdraw;
                    updateApproval.OECApproverId = CurrentUser.Id;
                    await approveService.ApprovalOperationAsync(updateApproval);//PA退回
                    var updatePa = pa.pa;
                    updatePa.SendBackType = WithdrawNodes.InterceptSendBack;
                    await paRepository.UpdateAsync(updatePa);
                }
                else if (pa.pa.Status == PurPAApplicationStatus.DocumentReceipt)
                {
                    _logger.LogError($"SubmitInterceptListAsync:DocumentReceipt PACode {pa.pa.ApplicationCode}");
                    //当被拦截的单据对应PA状态为"单据接收"时，代表单据已通过直接经理审批但尚未由Hub团队接收跟进，此时该单据无法再被Hub团队接收或分单，Hub团队可以在【付款审批任务】-【拦截中】tab下查看该单据的详细信息；
                    var updatePa = pa.pa;
                    updatePa.TaskType = PAApprovalTaskStatus.Intercepted;
                    await paRepository.UpdateAsync(updatePa);
                }
            }
            if (sendEmaillRecords.Any())
            {
                //记录邮件，并触发邮件发送功能
                var emailService = LazyServiceProvider.LazyGetService<IEmailService>();
                await emailService.InsertSendEmaillRecordAsync(sendEmaillRecords);
                // 调度作业
                BackgroundJob.Enqueue<SendEmailWorker>(a => a.DoWorkAsync());
            }
            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 测试邮件发送
        /// </summary>
        /// <returns></returns>
        public async Task SendEmailTest()
        {
            var sendEmaillRecords = new List<InsertSendEmaillRecordDto>();
            sendEmaillRecords.Add(new InsertSendEmaillRecordDto
            {
                EmailAddress = "<EMAIL>",
                Subject = "发送测试邮件",
                Content = "测试收到邮件内容",
                SourceType = EmailSourceType.OECIntercept,
                Status = SendStatus.Pending,
                Attempts = 0,
            });
            sendEmaillRecords.Add(new InsertSendEmaillRecordDto
            {
                EmailAddress = "<EMAIL>",
                Subject = "发送测试邮件",
                Content = "测试收到邮件内容",
                SourceType = EmailSourceType.OECIntercept,
                Status = SendStatus.Pending,
                Attempts = 0,
            });
            //记录邮件，并触发邮件发送功能
            var emailService = LazyServiceProvider.LazyGetService<IEmailService>();
            await emailService.InsertSendEmaillRecordAsync(sendEmaillRecords);

            // 调度作业
            BackgroundJob.Enqueue<SendEmailWorker>(a => a.DoWorkAsync());
        }

        /// <summary>
        /// 确认释放（支持批量）
        /// </summary>
        /// <param name="batchConfirmRelease"></param>
        /// <returns></returns>
        public async Task<MessageResult> BatchConfirmReleaseAsync(List<ConfirmReleaseRequestDto> batchConfirmRelease)
        {
            var interceptRepository = LazyServiceProvider.LazyGetService<IOECInterceptRepository>();
            var queryIntercept = await interceptRepository.GetQueryableAsync();
            var queryInterceptOperateHistory = await LazyServiceProvider.LazyGetService<IOECInterceptOperateHistoryRepository>().GetQueryableAsync();

            Guid userId = CurrentUser.Id.Value;
            string userName = CurrentUser.Name;

            var interceptHistorys = queryIntercept.GroupJoin(queryInterceptOperateHistory, a => a.Id, b => b.InterceptId, (a, b) => new { intercept = a, operates = b })
                .SelectMany(a => a.operates.DefaultIfEmpty(), (a, b) => new { a.intercept, operate = b })
                .Where(a => a.operate != null && a.intercept.InterceptTime <= a.operate.CreationTime)
                .Where(a => batchConfirmRelease.Select(a => a.InterceptId).Contains(a.intercept.Id))
                .ToList();//排除操作历史在拦截时间之前的数据
            var queryIntercepts = interceptHistorys.Select(a => a.intercept).GroupBy(a => a.Id).Select(a => a.FirstOrDefault()).ToList();//本批次拦截信息
            var interceptOperateHistorys = interceptHistorys.Select(a => a.operate).ToList();
            foreach (var confirmReleaseRequest in batchConfirmRelease)
            {
                var intercept = queryIntercepts.Where(a => a.Id == confirmReleaseRequest.InterceptId).FirstOrDefault();
                if (intercept == null)
                    continue;
                var thisInterceptOperateHistorys = interceptOperateHistorys.Where(a => a.InterceptId == intercept.Id).ToList();
                List<string> resolveds = new List<string>();
                if (!string.IsNullOrWhiteSpace(intercept.ResolvedInterceptType))
                {
                    resolveds.AddRange(intercept.ResolvedInterceptType.Split(',').ToList());
                }
                resolveds.AddRange(confirmReleaseRequest.InterceptTypeCodes.ToList());
                intercept.ResolvedInterceptType = string.Join(",", resolveds);

                var codes = intercept.InterceptTypeCode.Split(",").ToList();
                var resolvedCodes = intercept.ResolvedInterceptType.Split(",").ToList();
                bool isProcess = false;
                if (!codes.Except(resolvedCodes).Any())
                {
                    if (thisInterceptOperateHistorys.Any(a => a.OperateType == InterceptOperateType.SendBack))//记录中有一条退回，则该拦截单据为退回
                    {
                        intercept.InterceptStatus = InterceptStatus.SendBacked;
                    }
                    else
                    {
                        intercept.InterceptStatus = InterceptStatus.Released;
                    }
                    isProcess = true;
                }
                var update = await interceptRepository.UpdateAsync(intercept, true);
                var operateHistorys = new List<InsertOperateHistoryDto>();
                operateHistorys.Add(new InsertOperateHistoryDto()
                {
                    InterceptId = update.Id,
                    PRDetailId = update.PRDetailId,
                    InterceptTypeCodes = confirmReleaseRequest.InterceptTypeCodes,
                    Remark = confirmReleaseRequest.Remark,
                    OperateType = InterceptOperateType.Release,
                    SolveInterceptType = string.Join(",", confirmReleaseRequest.InterceptTypeCodes),
                    OperateUserId = userId,
                    OperateUserName = userName
                });
                await InsertOperateHistoryAsync(operateHistorys);
                if (isProcess)
                {
                    await InterceptionStatusToProcessPAAsync(intercept.PRDetailId);
                }
            }
            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 拦截退回（支持批量）
        /// </summary>
        /// <param name="sendBackRequest"></param>
        /// <returns></returns>
        public async Task<MessageResult> SendBackInterceptAsync(List<SendBackRequestDto> sendBackRequest)
        {
            var interceptRepository = LazyServiceProvider.LazyGetService<IOECInterceptRepository>();
            var queryIntercept = await interceptRepository.GetQueryableAsync();
            var queryInterceptOperateHistory = await LazyServiceProvider.LazyGetService<IOECInterceptOperateHistoryRepository>().GetQueryableAsync();

            Guid userId = CurrentUser.Id.Value;
            string userName = CurrentUser.Name;

            var interceptHistorys = queryIntercept.GroupJoin(queryInterceptOperateHistory, a => a.Id, b => b.InterceptId, (a, b) => new { intercept = a, operates = b })
                .SelectMany(a => a.operates.DefaultIfEmpty(), (a, b) => new { a.intercept, operate = b })
                .Where(a => a.operate != null && a.intercept.InterceptTime <= a.operate.CreationTime)
                .Where(a => sendBackRequest.Select(a => a.InterceptId).Contains(a.intercept.Id))
                .ToList();//排除操作历史在拦截时间之前的数据
            var queryIntercepts = interceptHistorys.Select(a => a.intercept).GroupBy(a => a.Id).Select(a => a.FirstOrDefault()).ToList();//本批次拦截信息
            var interceptOperateHistorys = interceptHistorys.Select(a => a.operate).ToList();

            foreach (var sendBack in sendBackRequest)
            {
                var intercept = queryIntercepts.Where(a => a.Id == sendBack.InterceptId).FirstOrDefault();
                if (intercept == null)
                    continue;
                List<string> resolveds = new List<string>();
                if (!string.IsNullOrWhiteSpace(intercept.ResolvedInterceptType))
                {
                    resolveds.AddRange(intercept.ResolvedInterceptType.Split(',').ToList());
                }
                resolveds.AddRange(sendBack.InterceptTypeCodes.ToList());
                intercept.ResolvedInterceptType = string.Join(",", resolveds);
                var codes = intercept.InterceptTypeCode.Split(",").ToList();
                var resolvedCodes = intercept.ResolvedInterceptType.Split(",").ToList();
                bool isProcess = false;
                if (!codes.Except(resolvedCodes).Any())
                {
                    intercept.InterceptStatus = InterceptStatus.SendBacked;
                    isProcess = true;
                }
                var operateHistorys = new List<InsertOperateHistoryDto>();
                operateHistorys.Add(new InsertOperateHistoryDto()
                {
                    InterceptId = intercept.Id,
                    PRDetailId = intercept.PRDetailId,
                    InterceptTypeCodes = intercept.InterceptTypeCode.Split(','),
                    Remark = sendBack.Remark,
                    OperateType = InterceptOperateType.SendBack,
                    SendBackLimitAmount = sendBack.SendBackLimitAmount,
                    SolveInterceptType = string.Join(",", sendBack.InterceptTypeCodes),
                    OperateUserId = userId,
                    OperateUserName = userName,
                });
                var update = await interceptRepository.UpdateAsync(intercept, true);
                await InsertOperateHistoryAsync(operateHistorys);
                if (isProcess)
                {
                    await InterceptionStatusToProcessPAAsync(intercept.PRDetailId);
                }
            }
            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 获取可批量处理拦截
        /// </summary>
        /// <param name="interceptContent"></param>
        /// <returns></returns>
        public async Task<MessageResult> GetBatchTackleInterceptAsync(QueryInterceptRequestDto interceptContent)
        {
            byte[] bytes = Convert.FromBase64String(interceptContent.InterceptDetailsContent);
            string decodedString = Encoding.UTF8.GetString(bytes);
            interceptContent.InterceptDetailsContent = decodedString;
            var result = new List<BatchTackleResponseDto>();
            var queryablePR = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var queryablePRdetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var paRepository = LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>();
            var queryablePA = await paRepository.GetQueryableAsync();
            var queryablePADetail = await LazyServiceProvider.LazyGetService<IPurPAApplicationDetailRepository>().GetQueryableAsync();
            var interceptRepository = LazyServiceProvider.LazyGetService<IOECInterceptRepository>();
            var queryIntercept = await interceptRepository.GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var verifyContent = GetBatchTackleInterceptQueryDetails(interceptContent.InterceptDetailsContent);
            var detailContent = new List<InterceptQueryDetailDto>();
            if (verifyContent.Item1)
            {
                detailContent = verifyContent.Item2;
            }
            else
            {
                return MessageResult.FailureResult("请检查数据格式是否正确");
            }
            if (detailContent.Any(a => string.IsNullOrWhiteSpace(a.ApplicationCode)))
                return MessageResult.FailureResult("请检查数据，PR单号为必填");
            var verify = await VerifyAuthorInterceptAsync(detailContent);
            if (verify.IsOECRole == false && verify.IsInvestigateRole == false)
                return MessageResult.FailureResult(verify.Remark);
            var prApplicationCode = detailContent.Select(x => x.ApplicationCode).ToList();
            var prInfo = QueryPRInfo(queryablePR, queryablePRdetail);
            var prs = queryIntercept.GroupJoin(prInfo, a => a.PRDetailId, b => b.PRDetailId, (a, b) => new { inter = a, prds = b })
                .SelectMany(a => a.prds.DefaultIfEmpty(), (a, b) => new { a.inter, prd = b })
                .Where(a => prApplicationCode.Contains(a.prd.ApplicationCode))
                .Where(a => a.inter.InterceptStatus == InterceptStatus.Intercepting)//单据已为"已释放"/"已退回"则标记为匹配失败(只查询拦截中的)
                .ToList();
            var prDetailIds = prs.Select(a => a.inter.PRDetailId).Distinct().ToList();
            var pas = queryablePA.GroupJoin(queryablePADetail, a => a.Id, b => b.PurPAApplicationId, (a, b) => new { pa = a, pads = b })
               .SelectMany(a => a.pads.DefaultIfEmpty(), (a, b) => new { a.pa, pad = b })
               .Where(a => prDetailIds.Contains(a.pad.PRDetailId) && a.pa.Status != PurPAApplicationStatus.Void)//作废的排除
               .ToList();
            detailContent = detailContent.OrderByDescending(a => a.VendorName).ToList();
            var interceptTypes = await dataverseService.GetDictionariesAsync(DictionaryType.InterceptTypes);
            foreach (var item in detailContent)
            {
                List<string> solveInterceptTypes = new List<string>();
                if (!string.IsNullOrWhiteSpace(item.InterceptTypeCode))
                {
                    foreach (var name in item.InterceptTypeCode.Split(','))
                    {
                        var type = interceptTypes.Where(a => a.Name == name).FirstOrDefault();
                        if (type != null)
                            solveInterceptTypes.Add(type.Code);
                    }
                }
                var thisPRIntercept = prs.Where(a => a.prd.ApplicationCode == item.ApplicationCode).ToList();
                if (!string.IsNullOrWhiteSpace(item.VendorName))
                {
                    var vendorNames = thisPRIntercept.Where(a => a.prd.VendorName == item.VendorName).ToList();
                    if (vendorNames.Any())
                    {
                        foreach (var vendorName in vendorNames)
                        {
                            if (result.Any(a => a.PRDetailId == vendorName.prd.PRDetailId))
                                continue;
                            var painfos = pas.Where(a => a.pad.PRDetailId == vendorName.prd.PRDetailId).GroupBy(a => a.pa.Id).Select(a => a.FirstOrDefault()).ToList();
                            var painfoResult = new List<PAApplicationInfoDto>();
                            foreach (var pa in painfos)
                            {
                                painfoResult.Add(new PAApplicationInfoDto()
                                {
                                    PAId = pa.pa.Id,
                                    ApplicationCode = pa.pa.ApplicationCode,
                                    Status = pa.pa.Status,
                                    PayTotalAmount = pa.pa.PayTotalAmount,
                                    DeliveryMode = pa.pa.DeliveryMode,
                                    PayPrDetailTotalAmount = pas.Where(a => a.pa.Id == pa.pa.Id && a.pad.PRDetailId == pa.pad.PRDetailId).Sum(a => a.pad.PaymentAmount),
                                });
                            }
                            List<string> resolvedInterceptTypes = new List<string>();
                            if (!string.IsNullOrWhiteSpace(vendorName.inter.ResolvedInterceptType))
                            {
                                foreach (var name in vendorName.inter.ResolvedInterceptType.Split(','))
                                {
                                    var type = interceptTypes.Where(a => a.Name == name).FirstOrDefault();
                                    if (type != null)
                                        resolvedInterceptTypes.Add(type.Code);
                                }
                            }

                            var interceptTypeCodes = vendorName.inter.InterceptTypeCode.Split(',').ToList();
                            #region 根据权限及已处理获取 本次可解决
                            var newSolveInterceptTypes = new List<string>();
                            if (verify.IsOECRole && verify.IsInvestigateRole)
                            {
                                var typcodes = solveInterceptTypes.Where(a => interceptTypeCodes.Contains(a)).ToList();//获取上传中包含于原处理类型  的 类型
                                newSolveInterceptTypes = typcodes;
                            }
                            else if (verify.IsOECRole)
                            {
                                var typcodes = solveInterceptTypes.Where(a => interceptTypeCodes.Contains(a)).ToList();//获取上传中包含于原处理类型  的 类型
                                newSolveInterceptTypes = typcodes.Where(a => a != "Investigate" && !resolvedInterceptTypes.Contains(a)).ToList();//排除已处理 和 调查类型
                            }
                            else if (verify.IsInvestigateRole)
                            {
                                var typcodes = solveInterceptTypes.Where(a => interceptTypeCodes.Contains(a)).ToList();//获取上传中包含于原处理类型  的 类型
                                newSolveInterceptTypes = typcodes.Where(a => a == "Investigate" && !resolvedInterceptTypes.Contains(a)).ToList();//只处理调查类型
                            }
                            #endregion
                            result.Add(new BatchTackleResponseDto()
                            {
                                InterceptId = vendorName.inter.Id,
                                PRDetailId = vendorName.prd.PRDetailId,
                                PRId = vendorName.prd.Id,
                                ApplicationCode = vendorName.prd.ApplicationCode,
                                ApplyTime = vendorName.prd.ApplyTime,
                                RowNo = vendorName.prd.RowNo,
                                TotalAmountRMB = vendorName.prd.TotalAmountRMB,
                                PRStatus = vendorName.prd.PRStatus,
                                VendorName = vendorName.prd.VendorName,
                                VendorCode = vendorName.prd.VendorCode,
                                InterceptTypeCodes = interceptTypeCodes.ToArray(),
                                OperateType = GetInterceptOperateType(item.OperateType),
                                SendBackLimitAmount = item.SendBackLimitAmount,
                                SolveInterceptTypes = newSolveInterceptTypes.ToArray(),
                                ResolvedInterceptType = resolvedInterceptTypes.ToArray(),
                                InterceptRemark = item.InterceptRemark,
                                AdaptationResults = AdaptationResults.Success,
                                PAInfo = painfoResult
                            });
                        }
                    }
                    else
                    {//没有查到，匹配失败
                        result.Add(new BatchTackleResponseDto()
                        {
                            PRDetailId = Guid.NewGuid(),//判断的时候要用，填充个值
                            ApplicationCode = item.ApplicationCode,
                            VendorName = item.VendorName,
                            AdaptationResults = AdaptationResults.Failure,
                        });
                    }
                }
                else
                {
                    if (thisPRIntercept.Any())
                    {
                        foreach (var prIntercept in thisPRIntercept)
                        {
                            if (result.Any(a => a.PRDetailId == prIntercept.prd.PRDetailId))
                                continue;
                            var painfos = pas.Where(a => a.pad.PRDetailId == prIntercept.prd.PRDetailId).GroupBy(a => a.pa.Id).Select(a => a.FirstOrDefault()).ToList();
                            var painfoResult = new List<PAApplicationInfoDto>();
                            foreach (var pa in painfos)
                            {
                                painfoResult.Add(new PAApplicationInfoDto()
                                {
                                    PAId = pa.pa.Id,
                                    ApplicationCode = pa.pa.ApplicationCode,
                                    Status = pa.pa.Status,
                                    PayTotalAmount = pa.pa.PayTotalAmount,
                                    DeliveryMode = pa.pa.DeliveryMode,
                                    PayPrDetailTotalAmount = pas.Where(a => a.pa.Id == pa.pa.Id && a.pad.PRDetailId == pa.pad.PRDetailId).Sum(a => a.pad.PaymentAmount),
                                });
                            }

                            List<string> resolvedInterceptTypes = new List<string>();
                            if (!string.IsNullOrWhiteSpace(prIntercept.inter.ResolvedInterceptType))
                            {
                                foreach (var name in prIntercept.inter.ResolvedInterceptType.Split(','))
                                {
                                    var type = interceptTypes.Where(a => a.Name == name).FirstOrDefault();
                                    if (type != null)
                                        resolvedInterceptTypes.Add(type.Code);
                                }
                            }

                            var interceptTypeCodes = prIntercept.inter.InterceptTypeCode.Split(',').ToList();
                            #region 根据权限及已处理获取 本次可解决
                            var newSolveInterceptTypes = new List<string>();
                            if (verify.IsOECRole && verify.IsInvestigateRole)
                            {
                                var typcodes = solveInterceptTypes.Where(a => interceptTypeCodes.Contains(a)).ToList();//获取上传中包含于原处理类型  的 类型
                                newSolveInterceptTypes = typcodes;
                            }
                            else if (verify.IsOECRole)
                            {
                                var typcodes = solveInterceptTypes.Where(a => interceptTypeCodes.Contains(a)).ToList();//获取上传中包含于原处理类型  的 类型
                                newSolveInterceptTypes = typcodes.Where(a => a != "Investigate" && !resolvedInterceptTypes.Contains(a)).ToList();//排除已处理 和 调查类型
                            }
                            else if (verify.IsInvestigateRole)
                            {
                                var typcodes = solveInterceptTypes.Where(a => interceptTypeCodes.Contains(a)).ToList();//获取上传中包含于原处理类型  的 类型
                                newSolveInterceptTypes = typcodes.Where(a => a == "Investigate" && !resolvedInterceptTypes.Contains(a)).ToList();//只处理调查类型
                            }
                            #endregion

                            result.Add(new BatchTackleResponseDto()
                            {
                                InterceptId = prIntercept.inter.Id,
                                PRDetailId = prIntercept.prd.PRDetailId,
                                PRId = prIntercept.prd.Id,
                                ApplicationCode = prIntercept.prd.ApplicationCode,
                                PRStatus = prIntercept.prd.PRStatus,
                                ApplyTime = prIntercept.prd.ApplyTime,
                                RowNo = prIntercept.prd.RowNo,
                                TotalAmountRMB = prIntercept.prd.TotalAmountRMB,
                                VendorName = prIntercept.prd.VendorName,
                                VendorCode = prIntercept.prd.VendorCode,
                                InterceptTypeCodes = interceptTypeCodes.ToArray(),
                                OperateType = GetInterceptOperateType(item.OperateType),
                                SendBackLimitAmount = item.SendBackLimitAmount,
                                SolveInterceptTypes = newSolveInterceptTypes.ToArray(),
                                ResolvedInterceptType = resolvedInterceptTypes.ToArray(),
                                InterceptRemark = item.InterceptRemark,
                                AdaptationResults = AdaptationResults.Success,
                                PAInfo = painfoResult
                            });
                        }
                    }
                    else
                    {//没有查到，匹配失败
                        result.Add(new BatchTackleResponseDto()
                        {
                            PRDetailId = Guid.NewGuid(),//判断的时候要用，填充个值
                            ApplicationCode = item.ApplicationCode,
                            VendorName = item.VendorName,
                            AdaptationResults = AdaptationResults.Failure,
                        });
                    }
                }
            }
            return MessageResult.SuccessResult(result);
        }

        /// <summary>
        /// 批量操作
        /// </summary>
        /// <param name="batchTackleRequest"></param>
        /// <returns></returns>
        public async Task<MessageResult> BatchTackleInterceptAsync(List<BatchTackleRequestDto> batchTackleRequest)
        {
            List<ConfirmReleaseRequestDto> batchConfirmRelease = batchTackleRequest.Where(a => a.OperateType == InterceptOperateType.Release)
                .Select(a => new ConfirmReleaseRequestDto()
                {
                    InterceptId = a.InterceptId.Value,
                    InterceptTypeCodes = a.SolveInterceptTypes,
                    Remark = a.Remark,
                })
                .ToList();
            List<SendBackRequestDto> sendBackRequest = batchTackleRequest.Where(a => a.OperateType == InterceptOperateType.SendBack)
                .Select(a => new SendBackRequestDto()
                {
                    InterceptId = a.InterceptId.Value,
                    InterceptTypeCodes = a.SolveInterceptTypes,
                    Remark = a.Remark,
                    SendBackLimitAmount = a.SendBackLimitAmount,
                }).ToList();
            if (batchConfirmRelease.Any())
            {
                await BatchConfirmReleaseAsync(batchConfirmRelease);
            }
            if (sendBackRequest.Any())
            {
                await SendBackInterceptAsync(sendBackRequest);
            }
            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 编辑拦截信息
        /// </summary>
        /// <param name="editIntercept"></param>
        /// <returns></returns>
        public async Task<MessageResult> EditInterceptAsync(EditInterceptDto editIntercept)
        {
            var paRepository = LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>();
            var queryablePA = await paRepository.GetQueryableAsync();
            var queryablePADetail = await LazyServiceProvider.LazyGetService<IPurPAApplicationDetailRepository>().GetQueryableAsync();
            var queryablePR = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var queryablePRDetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var interceptRepository = LazyServiceProvider.LazyGetService<IOECInterceptRepository>();
            var queryableIntercept = await interceptRepository.GetQueryableAsync();
            var interceptOperateHistoryRepository = LazyServiceProvider.LazyGetService<IOECInterceptOperateHistoryRepository>();
            var queryInterceptOperateHistory = await interceptOperateHistoryRepository.GetQueryableAsync();
            var intercept = queryableIntercept.Where(a => a.Id == editIntercept.InterceptId).FirstOrDefault();
            if (intercept == null)
                return MessageResult.FailureResult("拦截信息不存在");
            if (intercept.InterceptStatus != InterceptStatus.Intercepting)
                return MessageResult.FailureResult("该拦截信息当前状态不能编辑");
            Guid userId = CurrentUser.Id.Value;
            string userName = CurrentUser.Name;
            var history = new List<InsertOperateHistoryDto>();
            history.Add(new InsertOperateHistoryDto()
            {
                InterceptId = intercept.Id,
                PRDetailId = intercept.PRDetailId,
                OriginalInterceptTypeCodes = intercept.InterceptTypeCode.Split(','),
                InterceptTypeCodes = editIntercept.InterceptTypeCodes,
                Remark = editIntercept.Remark,
                OperateType = InterceptOperateType.Update,
                OperateUserId = userId,
                OperateUserName = userName
            });
            intercept.InterceptTypeCode = string.Join(",", editIntercept.InterceptTypeCodes);
            intercept.InterceptRemark = editIntercept.Remark;
            var historys = queryInterceptOperateHistory.Where(a => a.InterceptId == intercept.Id && a.CreationTime >= intercept.InterceptTime).ToList();
            bool isProcess = false;
            if (!string.IsNullOrWhiteSpace(intercept.ResolvedInterceptType))//编辑时已经解决的和更新拦截类型相同， 则视为该拦截已释放或退回
            {
                var resolved = intercept.ResolvedInterceptType.Split(',');
                var interceptType = intercept.InterceptTypeCode.Split(',');
                if (interceptType.All(item => resolved.Contains(item))) //编辑的拦截类型是否都解决
                {
                    if (historys.Any(a => a.OperateType == InterceptOperateType.SendBack))
                    {
                        intercept.InterceptStatus = InterceptStatus.SendBacked;
                    }
                    else
                    {
                        intercept.InterceptStatus = InterceptStatus.Released;
                    }
                    isProcess = true;
                }
            }
            await interceptRepository.UpdateAsync(intercept, true);
            await InsertOperateHistoryAsync(history);
            if (isProcess)
            {
                await InterceptionStatusToProcessPAAsync(intercept.PRDetailId);
            }
            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 获取操作记录
        /// </summary>
        /// <param name="interceptId"></param>
        /// <returns></returns>
        public async Task<MessageResult> GetInterceptOperateHistoryAsync(Guid interceptId)
        {
            var interceptOperateHistoryRepository = await LazyServiceProvider.LazyGetService<IOECInterceptOperateHistoryRepository>().GetQueryableAsync();
            var operateHistorys = interceptOperateHistoryRepository.Where(a => a.InterceptId == interceptId).OrderByDescending(a => a.OperateTime).ToList();
            var result = ObjectMapper.Map<List<OECInterceptOperateHistory>, List<OECInterceptOperateHistoryDto>>(operateHistorys);
            var operateTypes = EnumUtil.GetEnumIdValues<InterceptOperateType>();
            result.ForEach(a =>
            {
                a.OperateTypeName = operateTypes.Where(x => x.Key == (int)a.OperateType).FirstOrDefault()?.Value ?? "";
            });
            return MessageResult.SuccessResult(result);
        }
        #region 私有方法

        /// <summary>
        /// 根据拦截状态处理PA 
        /// </summary>
        /// <returns></returns>
        private async Task InterceptionStatusToProcessPAAsync(Guid prDetailId)
        {
            var paRepository = LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>();
            var queryablePA = await paRepository.GetQueryableAsync();
            var queryablePADetail = await LazyServiceProvider.LazyGetService<IPurPAApplicationDetailRepository>().GetQueryableAsync();
            var interceptRepository = LazyServiceProvider.LazyGetService<IOECInterceptRepository>();
            var queryIntercept = await interceptRepository.GetQueryableAsync();
            var queryInterceptOperateHistory = await LazyServiceProvider.LazyGetService<IOECInterceptOperateHistoryRepository>().GetQueryableAsync();
            var queryablePR = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var queryablePRDetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var pas = queryablePA.GroupJoin(queryablePADetail, a => a.Id, b => b.PurPAApplicationId, (a, b) => new { pa = a, pads = b })
                .SelectMany(a => a.pads.DefaultIfEmpty(), (a, b) => new { a.pa, pad = b })
                .Where(a => a.pad.PRDetailId == prDetailId && a.pa.Status != PurPAApplicationStatus.Void)//排除作废的PA
                .GroupBy(a => a.pa.Id)
                .Select(a => a.FirstOrDefault())
                .ToList();
            if (!pas.Any())
                return;
            Guid userId = CurrentUser.Id.Value;
            string userName = CurrentUser.Name;
            var paIds = pas.Select(a => a.pa.Id).ToList();//所有的PAID
            var paDetails = queryablePADetail.Where(a => paIds.Contains(a.PurPAApplicationId)).ToList();
            var prDetails = queryablePR.GroupJoin(queryablePRDetail, a => a.Id, b => b.PRApplicationId, (a, b) => new { pr = a, prds = b })
                .SelectMany(a => a.prds.DefaultIfEmpty(), (a, b) => new { a.pr, prd = b })
                .Where(a => paDetails.Select(a => a.PRDetailId).Contains(a.prd.Id))
                .ToList();
            var interceptHistorys = queryIntercept.GroupJoin(queryInterceptOperateHistory, a => a.Id, b => b.InterceptId, (a, b) => new { intercept = a, operates = b })
                .SelectMany(a => a.operates.DefaultIfEmpty(), (a, b) => new { a.intercept, operate = b })
                .Where(a => a.operate != null && a.intercept.InterceptTime <= a.operate.CreationTime)
                .Where(a => paDetails.Select(a => a.PRDetailId).Contains(a.intercept.PRDetailId))
                .ToList();//排除操作历史在拦截时间之前的数据
            var intercepts = interceptHistorys.Select(a => a.intercept).GroupBy(a => a.Id).Select(a => a.FirstOrDefault()).ToList();//所有PA对应的拦截信息
            var interceptOperateHistorys = interceptHistorys.Select(a => a.operate).ToList();
            var sendEmaillRecords = new List<InsertSendEmaillRecordDto>();
            foreach (var pa in pas)
            {
                var thisPaDetails = paDetails.Where(a => a.PurPAApplicationId == pa.pa.Id).ToList();
                var thisInterceptPrdId = thisPaDetails.Select(x => x.PRDetailId).Distinct().ToList();
                var thisintercept = intercepts.Where(a => thisInterceptPrdId.Contains(a.PRDetailId)).ToList();//PA 对应的PR明细拦截中数据
                if (thisintercept.Any(a => a.InterceptStatus == InterceptStatus.Intercepting))
                    continue;
                if (thisintercept.Any(a => a.InterceptStatus == InterceptStatus.SendBacked)) //有退回走退回处理
                {
                    if (pa.pa.Status == PurPAApplicationStatus.WaitingForPayment)
                    {
                        //当被拦截的单据对应PA状态为"已推送"时，系统发送消息及邮件提醒至Hub团队告知在BPCS内Void该PA对应付款，申请人后续可线下向财务申请以新的金额重新付款；
                        List<SendBackedEmailDto> backedEmails = new List<SendBackedEmailDto>();
                        var sendBacks = thisintercept.Where(a => a.InterceptStatus == InterceptStatus.SendBacked).ToList();//PA 明细对应的PR明细拦截被退回处理的
                        if (sendBacks.Any())
                        {
                            var paInPRs = prDetails.Where(a => sendBacks.Select(x => x.PRDetailId).Contains(a.prd.Id)).ToList();
                            foreach (var prInfo in paInPRs)
                            {
                                var sendBack = sendBacks.FirstOrDefault(a => a.PRDetailId == prInfo.prd.Id);
                                var sendBackHistorys = interceptOperateHistorys.Where(a => a.InterceptId == sendBack.Id).ToList();//只需要操作历史创建时间大约拦截时间的数据
                                decimal LimitAmount = 0M;
                                var sendBackHistory = sendBackHistorys.Where(a => (a.SolveInterceptType ?? string.Empty).Contains("Investigate") && a.OperateType == InterceptOperateType.SendBack).OrderBy(a => a.SendBackLimitAmount).FirstOrDefault();
                                if (sendBackHistory == null)
                                {
                                    LimitAmount = sendBackHistorys.Where(a => a.OperateType == InterceptOperateType.SendBack).OrderBy(a => a.SendBackLimitAmount).FirstOrDefault()?.SendBackLimitAmount ?? 0M;
                                }
                                else
                                {
                                    LimitAmount = sendBackHistory.SendBackLimitAmount ?? 0M;
                                }
                                PurPAApplicationStatus[] historyStatus = [PurPAApplicationStatus.WaitingForPayment, PurPAApplicationStatus.PaymentProgress, PurPAApplicationStatus.Paid, PurPAApplicationStatus.PaymenFailed];//财务复审通过后的状态
                                var historyPAs = pas.Where(a => a.pad.PRDetailId == pa.pad.PRDetailId && a.pa.CreationTime < pa.pa.CreationTime && historyStatus.Contains(a.pa.Status)).ToList();//财务复审通过的历史PA  创建时间小于当前时间则为历史
                                backedEmails.Add(new SendBackedEmailDto()
                                {
                                    PRApplicationCode = prInfo.pr.ApplicationCode,
                                    RowNo = prInfo.prd.RowNo,
                                    LimitAmount = Math.Round(LimitAmount, 2, MidpointRounding.AwayFromZero),
                                    TotalAmount = Math.Round(thisPaDetails.Where(a => a.PRDetailId == prInfo.prd.Id).Sum(a => a.PaymentAmount), 2, MidpointRounding.AwayFromZero),
                                    HistoryTotalAmount = Math.Round(historyPAs.Sum(a => a.pad.PaymentAmount), 2, MidpointRounding.AwayFromZero),
                                });
                            }
                        }

                        var backIds = thisintercept.Where(a => a.InterceptStatus == InterceptStatus.SendBacked).Select(a => a.Id).ToList();//所有退回拦截单据ID
                        var backHistory = interceptOperateHistorys.Where(a => backIds.Contains(a.InterceptId)).OrderByDescending(a => a.OperateTime).FirstOrDefault();//取一条操作退回最新记录
                        var record = await SendEmailToFinancialInitialApprovalAsync(pa.pa, InterceptStatus.SendBacked, backHistory?.OperateUserName, backedEmails);
                        if (record != null)
                            sendEmaillRecords.Add(record);
                    }
                    else if (pa.pa.Status == PurPAApplicationStatus.Approvaling)
                    {
                        var thisInterceptOperateType = interceptOperateHistorys.Where(a => thisintercept.Select(x => x.Id).Contains(a.InterceptId) && a.OperateType == InterceptOperateType.SendBack).ToList();//PA 对应的PR 拦截处理记录（退回）
                        var paPRDetails = prDetails.Where(a => thisInterceptPrdId.Contains(a.prd.Id)).Select(a => a.prd).ToList();
                        var prRemarks = ReleaseInterceptSendBackRemark(paPRDetails, thisInterceptOperateType);
                        //当被拦截的单据对应PA状态为"单据接收"或"直接经理审批"时，该PA自动退回至申请人，申请人需要重新填写并提交，且填写的付款金额不得高于OEC团队填写的退回金额上限，填写并提交后该PA可继续进行后续流程；
                        var approveService = LazyServiceProvider.LazyGetService<IApproveService>();
                        var approvers = approveService.GetApproverByFormId(pa.pa.Id);
                        if (!approvers.Any())
                            continue;//没有审批人
                        var approver = approvers.FirstOrDefault(a => a.Id == CurrentUser.Id.Value);
                        if (approver == null)
                            approver = approvers.FirstOrDefault();
                        var updateApproval = new UpdateApprovalDto();
                        updateApproval.BusinessFormId = pa.pa.Id.ToString();
                        updateApproval.Submitter = approver.Id;//提交人
                        updateApproval.Remark = string.Join(";", prRemarks);
                        updateApproval.OperationStatus = ApprovalOperation.Withdraw;
                        updateApproval.OECApproverId = CurrentUser.Id;
                        await approveService.ApprovalOperationAsync(updateApproval);//PA退回  状态变更由回调处理
                        //await SendEmailReturnNoticeToApplicantAsync(pa.pa, NotifyApplicantOperationType.LJTH, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                    }
                    else if (pa.pa.Status == PurPAApplicationStatus.DocumentReceipt)
                    {
                        var thisInterceptOperateType = interceptOperateHistorys.Where(a => thisintercept.Select(x => x.Id).Contains(a.InterceptId) && a.OperateType == InterceptOperateType.SendBack).ToList();//PA 对应的PR 拦截处理记录（退回）
                        var paPRDetails = prDetails.Where(a => thisInterceptPrdId.Contains(a.prd.Id)).Select(a => a.prd).ToList();
                        //var prRemarks = ReleaseInterceptSendBackRemark(paPRDetails, thisInterceptOperateType);

                        var approveService = LazyServiceProvider.LazyGetService<IApproveService>();
                        DateTime approvalTime = DateTime.Now;
                        await approveService.AddApprovalRecordAsync(new AddApprovalRecordDto()
                        {
                            FormId = pa.pa.Id,
                            ApprovalId = userId,//提交人,
                            //Status = ApprovalOperation.ReturnToPreliminary,//变更为 退回申请人
                            Status = ApprovalOperation.ReturnToApplicant,
                            //Remark = string.Join(";", prRemarks),
                            ApprovalTime = approvalTime,
                            WorkStep = "拦截退回",
                            Name = "拦截退回"
                        });
                        pa.pa.Status = PurPAApplicationStatus.Reissue;
                        pa.pa.TaskType = null;//付款审批任务中心移出
                        await paRepository.UpdateAsync(pa.pa, true);
                        await SendEmailReturnNoticeToApplicantAsync(pa.pa, NotifyApplicantOperationType.LJTH, approvalTime.ToString("yyyy-MM-dd HH:mm:ss"));
                    }
                }
                else//没有退回的情况下为释放
                {
                    if (pa.pa.Status == PurPAApplicationStatus.WaitingForPayment)
                    {
                        var releasedIds = thisintercept.Where(a => a.InterceptStatus == InterceptStatus.Released).Select(a => a.Id).ToList();//所有释放拦截单据ID
                        var releasedHistory = interceptOperateHistorys.Where(a => releasedIds.Contains(a.InterceptId)).OrderByDescending(a => a.OperateTime).FirstOrDefault();//取一条操作退回最新记录
                        //当被拦截的单据对应PA状态为"已推送"时，系统发送消息及邮件提醒至Hub团队告知在BPCS内Unhold付款;
                        var record = await SendEmailToFinancialInitialApprovalAsync(pa.pa, InterceptStatus.Released, releasedHistory?.OperateUserName);
                        if (record != null)
                            sendEmaillRecords.Add(record);
                    }
                    else if (pa.pa.Status == PurPAApplicationStatus.DocumentReceipt)
                    {
                        //当被拦截的单据对应PA状态为"单据接收"时，该单据不再出现在Hub团队的【付款审批任务】-【拦截中】页面中，如果为线上单据则进入【待分配】页面内，如果为线下单据则可重新扫入系统中分配;
                        var updatePa = pa.pa;
                        if (updatePa.TaskType == PAApprovalTaskStatus.Intercepted)
                        {
                            //理解这里跟递交方式无关，都改为待分配
                            updatePa.TaskType = PAApprovalTaskStatus.ToBeDistributed;
                            await paRepository.UpdateAsync(updatePa, true);
                        }
                    }
                }
            }
            if (sendEmaillRecords.Any())
            {
                //记录邮件，并触发邮件发送功能
                var emailService = LazyServiceProvider.LazyGetService<IEmailService>();
                await emailService.InsertSendEmaillRecordAsync(sendEmaillRecords);
                // 调度作业
                BackgroundJob.Enqueue<SendEmailWorker>(a => a.DoWorkAsync());
            }
        }

        /// <summary>
        /// PA 退回通知申请人
        /// </summary>
        /// <param name="pa"></param>
        /// <param name="operationType"></param>
        /// <param name="returnTime"></param>
        /// <returns></returns>
        private async Task SendEmailReturnNoticeToApplicantAsync(PurPAApplication pa, string operationType, string returnTime)
        {
            var userQuery = (await LazyServiceProvider.LazyGetService<IRepository<IdentityUser, Guid>>().GetQueryableAsync()).AsNoTracking();

            var userIdsToNotify = new List<Guid>();
            var agentService = LazyServiceProvider.LazyGetService<IAgencyService>();
            var agentRequest = new GetAgentByOriginalOperatorRequestDto { OriginalOperatorId = pa.ApplyUserId, BusinessTypeCategory = ResignationTransfer.TaskFormCategory.PaymentApplication };
            userIdsToNotify.Add(pa.TransfereeId.HasValue ? pa.TransfereeId.Value : pa.ApplyUserId);
            var agent = await agentService.GetAgentByOriginalOperator(agentRequest);
            if (agent.HasValue)
                userIdsToNotify.Add(agent.Value.Key);

            var users = userQuery.Where(a => userIdsToNotify.Distinct().ToHashSet().Contains(a.Id)).ToArray();
            var sendEmaillRecords = users.Select(user => new InsertSendEmaillRecordDto
            {
                EmailAddress = user.Email,
                Subject = "[NexBPM消息中心]您申请的付款申请：{ApplicationCode}已被【{OperationType}】。",
                Content = JsonSerializer.Serialize(new ReturnNoticeToApplicantEmailDto
                {
                    ApplicationCode = pa.ApplicationCode,
                    OperationType = operationType,
                    ReturnTime = returnTime,
                    UserName = user.Name,
                    ApplicationLink = $"{_configuration["SpeakerEmail:WebHost"]?.TrimEnd('/')}/task/launch",
                }),
                SourceType = EmailSourceType.ReturnNoticeToApplicant,
                Status = SendStatus.Pending,
                Attempts = 0,
            });
            //记录邮件，并触发邮件发送功能
            var emailService = LazyServiceProvider.LazyGetService<IEmailService>();
            await emailService.InsertSendEmaillRecordAsync(sendEmaillRecords.ToList());
            // 调度作业
            BackgroundJob.Enqueue<SendEmailWorker>(a => a.DoWorkAsync());
        }

        /// <summary>
        /// 获取拦截列表(导出用)
        /// </summary>
        /// <param name="InterceptListRequest"></param>
        /// <returns></returns>
        private async Task<List<ExportOECInterceptDto>> QeuryeExportOECInterceptListAsync(GetOECInterceptListRequestDto interceptListRequest)
        {
            var queryablePR = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var queryablePRDetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var queryablePO = await LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>().GetQueryableAsync();
            var queryablePODetail = await LazyServiceProvider.LazyGetService<IPurPOApplicationDetailsRepository>().GetQueryableAsync();
            var queryableGR = await LazyServiceProvider.LazyGetService<IPurGRApplicationRepository>().GetQueryableAsync();
            var queryableGRDetail = await LazyServiceProvider.LazyGetService<IPurGRApplicationDetailRepository>().GetQueryableAsync();
            var queryablePA = await LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>().GetQueryableAsync();
            var queryablePADetail = await LazyServiceProvider.LazyGetService<IPurPAApplicationDetailRepository>().GetQueryableAsync();
            var queryableIntercept = await LazyServiceProvider.LazyGetService<IOECInterceptRepository>().GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();

            var verify = await GetVerifyAuthorAsync();
            if (!verify.Any())
                return new List<ExportOECInterceptDto>();
            var poInfo = QueryPOInfo(queryablePO, queryablePODetail);
            var grInfo = QueryGRInfo(queryableGR, queryableGRDetail);

            var prInfo = queryablePR.GroupJoin(queryablePRDetail, a => a.Id, b => b.PRApplicationId, (a, b) => new { pr = a, prds = b })
                .SelectMany(a => a.prds.DefaultIfEmpty(), (a, b) => new { a.pr, prd = b });

            var query = queryableIntercept.GroupJoin(prInfo, a => a.PRDetailId, b => b.prd.Id, (a, b) => new { intercept = a, prs = b })
                .SelectMany(a => a.prs.DefaultIfEmpty(), (a, b) => new { a.intercept, b.pr, b.prd })
                .GroupJoin(poInfo, a => a.intercept.PRDetailId, b => b.PRDetailId, (a, b) => new { a.intercept, a.pr, a.prd, pos = b })
                .SelectMany(a => a.pos.DefaultIfEmpty(), (a, b) => new { a.intercept, a.pr, a.prd, po = b })
                .GroupJoin(grInfo, a => a.intercept.PRDetailId, b => b.PRDetailId, (a, b) => new { a.intercept, a.pr, a.prd, a.po, grs = b })
                .SelectMany(a => a.grs.DefaultIfEmpty(), (a, b) => new { a.intercept, a.pr, a.prd, a.po, gr = b })
                .GroupJoin(queryablePA.Where(a => a.Status != PurPAApplicationStatus.Void), a => a.gr.Id, b => b.GRId, (a, b) => new { a.intercept, a.pr, a.prd, a.po, a.gr, pas = b })
                .SelectMany(a => a.pas.DefaultIfEmpty(), (a, b) => new { a.intercept, a.pr, a.prd, a.po, a.gr, pa = b });


            var linqWhere = query.WhereIf(!string.IsNullOrWhiteSpace(interceptListRequest.ApplicationCode), a => a.pr.ApplicationCode.Contains(interceptListRequest.ApplicationCode))
                 .WhereIf(verify.Contains(RoleNames.OEC_Investigator) && verify.Count == 1, a => a.intercept.InterceptTypeCode.Contains("Investigate"))//只有调查角色的仅查询调查相关的
                 .WhereIf(!string.IsNullOrWhiteSpace(interceptListRequest.VendorName), a => a.prd.VendorName.Contains(interceptListRequest.VendorName))
                 .WhereIf(!string.IsNullOrWhiteSpace(interceptListRequest.VendorCode), a => a.prd.VendorCode.Contains(interceptListRequest.VendorCode))
                 .WhereIf(!string.IsNullOrWhiteSpace(interceptListRequest.PAApplicationCode), a => a.pa.ApplicationCode.Contains(interceptListRequest.PAApplicationCode))
                 .WhereIf(interceptListRequest.DeliveryMode.HasValue, a => a.pa.DeliveryMode == interceptListRequest.DeliveryMode)
                 .WhereIf(!string.IsNullOrWhiteSpace(interceptListRequest.InterceptTypeCode), a => a.intercept.InterceptTypeCode.Contains(interceptListRequest.InterceptTypeCode))
                 .WhereIf(interceptListRequest.InterceptStatus.HasValue, a => a.intercept.InterceptStatus == interceptListRequest.InterceptStatus)
                 .WhereIf(!string.IsNullOrWhiteSpace(interceptListRequest.InterceptByUserName), a => a.intercept.InterceptByUserName.Contains(interceptListRequest.InterceptByUserName))
                 .WhereIf(interceptListRequest.InterceptStartTime.HasValue, a => a.intercept.InterceptTime.Date >= interceptListRequest.InterceptStartTime.Value.Date)
                 .WhereIf(interceptListRequest.InterceptEndTime.HasValue, a => a.intercept.InterceptTime.Date <= interceptListRequest.InterceptEndTime.Value.Date)
                 .OrderByDescending(a => a.intercept.InterceptTime);
            //.GroupBy(a => a.pa.Id)
            //.Select(g => g.FirstOrDefault());

            var resultData = linqWhere.ToList();
            var paIds = resultData.Where(a => a.pa != null).Select(a => a.pa.Id).ToList();
            var pads = queryablePADetail.Where(a => paIds.Contains(a.PurPAApplicationId)).ToList();
            var interceptTypes = await dataverseService.GetDictionariesAsync(DictionaryType.InterceptTypes);
            var result = resultData.Select(a => new ExportOECInterceptDto
            {
                ApplicationCode = a.pr.ApplicationCode,
                RowNo = a.prd.RowNo,
                ApplyTime = a.pr.ApplyTime?.ToString("yyyy-MM-dd HH:mm:ss"),
                ApplyUserName = a.pr.ApplyUserIdName,
                TotalAmountRMB = Math.Round(a.prd.TotalAmountRMB ?? 0M, 2, MidpointRounding.AwayFromZero),
                CompanyName = a.pr.CompanyIdName,
                VendorCode = a.prd.VendorCode,
                VendorName = a.prd.VendorName,
                StatusText = a.pr.Status.GetDescription(),
                POApplicationCode = a.po?.ApplicationCode,
                GRApplicationCode = a.gr?.ApplicationCode,
                PAApplicationCode = a.pa?.ApplicationCode,
                PAStatusText = a.pa == null ? "" : a.pa?.Status.GetDescription(),
                PATotalAmountRMB = Math.Round((a.pa?.PayTotalAmount ?? 0M) * (decimal)(a.pa?.ExchangeRate ?? 0L), 2, MidpointRounding.AwayFromZero),
                PAAmountRMB = a.pa == null ? 0M : Math.Round(pads.Where(x => x.PurPAApplicationId == a.pa.Id && a.intercept.PRDetailId == x.PRDetailId).Sum(a => a.PaymentAmount) * (decimal)(a.pa?.ExchangeRate ?? 0L), 2, MidpointRounding.AwayFromZero),
                DeliveryModeText = a.pa == null ? "" : a.pa.DeliveryMode.GetDescription(),
                InterceptTypeCode = GetInterceptTypeName(interceptTypes.ToList(), a.intercept.InterceptTypeCode),
                InterceptByUserName = a.intercept.InterceptByUserName,
                InterceptTime = a.intercept.InterceptTime.ToString("yyyy-MM-dd"),
                ReleasedSendBackedDate = a.intercept.InterceptStatus == InterceptStatus.Intercepting ? "" : a.intercept.LastModificationTime?.ToString("yyyy-MM-dd"),
                InterceptStatusText = a.intercept.InterceptStatus.GetDescription(),
            }).ToList();
            return result;
        }

        /// <summary>
        /// 查询当前用户的OEC 管理员和调查 角色
        /// </summary>
        /// <returns></returns>
        private async Task<List<string>> GetVerifyAuthorAsync()
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var interceptTypes = await dataverseService.GetDictionariesAsync(DictionaryType.InterceptTypes);
            var userId = CurrentUser?.Id;
            var rolesRepository = await LazyServiceProvider.LazyGetService<IRepository<IdentityRole, Guid>>().GetQueryableAsync();
            var userManager = LazyServiceProvider.LazyGetService<IdentityUserManager>();
            var identityUser = await userManager.FindByIdAsync(userId.ToString());
            if (identityUser == null)
            {
                return new List<string>();
            }
            if (!identityUser.Roles.Any())
            {
                return new List<string>();
            }
            var userRoles = rolesRepository.Where(a => identityUser.Roles.Select(x => x.RoleId).Contains(a.Id)).ToList();
            return userRoles.Where(a => a.Name == RoleNames.OEC_Admin || a.Name == RoleNames.OEC_Investigator || a.Name == RoleNames.GroupFinance).Select(a => a.Name).ToList();
        }

        /// <summary>
        /// 拦截权限验证
        /// item1:有无权限、item2:说明 、item3:OEC权限和调查
        /// </summary>
        /// <param name="interceptInfo"></param>
        /// <returns></returns>
        private async Task<VerifyAuthDto> VerifyAuthorInterceptAsync(List<InterceptQueryDetailDto> interceptInfo)
        {
            var result = new VerifyAuthDto();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var interceptTypes = await dataverseService.GetDictionariesAsync(DictionaryType.InterceptTypes);
            var userId = CurrentUser?.Id;
            var rolesRepository = await LazyServiceProvider.LazyGetService<IRepository<IdentityRole, Guid>>().GetQueryableAsync();
            var userManager = LazyServiceProvider.LazyGetService<IdentityUserManager>();
            var identityUser = await userManager.FindByIdAsync(userId.ToString());
            if (identityUser == null)
            {
                return new VerifyAuthDto() { Remark = "未查询到用户信息" };
            }
            if (!identityUser.Roles.Any())
            {
                return new VerifyAuthDto() { Remark = "该用户无权处理单据拦截" };
            }
            var userRoles = rolesRepository.Where(a => identityUser.Roles.Select(x => x.RoleId).Contains(a.Id)).ToList();
            if (userRoles.Any(a => a.Name == RoleNames.OEC_Admin) && userRoles.Any(a => a.Name == RoleNames.OEC_Investigator))//两个角色都有
                return new VerifyAuthDto() { IsOECRole = true, IsInvestigateRole = true, Remark = "有权限" };
            if (userRoles.Any(a => a.Name == RoleNames.OEC_Admin))
            {
                var investigates = interceptInfo.Where(a => a.InterceptTypeCode.Contains(OECInterceptType.Investigate)).ToList();
                if (investigates.Any())
                {
                    return new VerifyAuthDto() { Remark = "合规管理员无权处理调查类型，请检查模板信息" };
                }
                return new VerifyAuthDto() { IsOECRole = true, Remark = "有权限" };
            }
            else if (userRoles.Any(a => a.Name == RoleNames.OEC_Investigator))
            {
                var oec = interceptInfo.Where(a => a.InterceptTypeCode.Contains(OECInterceptType.OEC) || a.InterceptTypeCode.Contains(OECInterceptType.TwoElementsFailed)).ToList();
                if (oec.Any())
                {
                    return new VerifyAuthDto() { Remark = "调查员仅能处理调查类型，请检查模板信息" };
                }
                return new VerifyAuthDto() { IsInvestigateRole = true, Remark = "有权限" };
            }
            else
            {
                return new VerifyAuthDto() { Remark = "该用户无权处理单据拦截" };
            }
        }

        public InterceptOperateType? GetInterceptOperateType(string operateTypeName)
        {
            InterceptOperateType? rsult = null;
            switch (operateTypeName)
            {
                case "拦截":
                    rsult = InterceptOperateType.Intercept;
                    break;
                case "退回":
                    rsult = InterceptOperateType.SendBack;
                    break;
                case "释放":
                    rsult = InterceptOperateType.Release;
                    break;
            }
            return rsult;
        }

        /// <summary>
        /// 组装PR退回备注
        /// </summary>
        /// <param name="prds"></param>
        /// <param name="interceptOperateHistory"></param>
        /// <returns></returns>
        private List<string> ReleaseInterceptSendBackRemark(List<PurPRApplicationDetail> prds, List<OECInterceptOperateHistory> interceptOperateHistory)
        {

            List<string> prRemarks = new List<string>();
            foreach (var item in prds)
            {
                var thisPrInterceptOperateType = interceptOperateHistory.Where(a => a.PRDetailId == item.Id).ToList();//PR明细对应的拦截处理记录（退回相关）
                if (!thisPrInterceptOperateType.Any())
                {
                    continue;
                }
                var investigates = thisPrInterceptOperateType.Where(a => (a.SolveInterceptType ?? string.Empty).Contains("Investigate")).OrderBy(a => a.SendBackLimitAmount).FirstOrDefault();//调查（Investigate）
                if (investigates != null)
                {
                    prRemarks.Add($"PR明细行号:{item.RowNo},支付人民币金额上限:{investigates.SendBackLimitAmount}");
                }
                else
                {
                    var oecLimitAmount = thisPrInterceptOperateType.OrderBy(a => a.SendBackLimitAmount).FirstOrDefault();
                    prRemarks.Add($"PR明细行号:{item.RowNo},支付人民币金额上限:{oecLimitAmount.SendBackLimitAmount}");
                }
            }
            return prRemarks;
        }

        /// <summary>
        /// 插入操作记录
        /// </summary>
        /// <param name="insertOperateHistory"></param>
        /// <returns></returns>
        private async Task InsertOperateHistoryAsync(List<InsertOperateHistoryDto> insertOperateHistory)
        {

            var interceptOperateHistoryRepository = LazyServiceProvider.LazyGetService<IOECInterceptOperateHistoryRepository>();
            var queryInterceptOperateHistory = await interceptOperateHistoryRepository.GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var interceptTypes = await dataverseService.GetDictionariesAsync(DictionaryType.InterceptTypes);
            var oecInterceptOperateHistorys = new List<OECInterceptOperateHistory>();
            foreach (var item in insertOperateHistory)
            {
                var interceptTypeName = interceptTypes.Where(a => item.InterceptTypeCodes.Contains(a.Code)).Select(a => a.Name).ToList();
                var oecInterceptOperateHistory = new OECInterceptOperateHistory();
                switch (item.OperateType)
                {
                    case InterceptOperateType.Intercept:
                        string names0 = interceptTypeName != null ? string.Join(",", interceptTypeName) : "";
                        oecInterceptOperateHistory.OperateContent = $"拦截类型：{names0}";
                        break;
                    case InterceptOperateType.Release:
                        var solveInterceptTypeName = interceptTypes.Where(a => item.SolveInterceptType.Contains(a.Code)).Select(a => a.Name).ToList();
                        string names = solveInterceptTypeName != null ? string.Join(",", solveInterceptTypeName) : "";
                        oecInterceptOperateHistory.OperateContent = $"拦截类型：{names}";
                        break;
                    case InterceptOperateType.SendBack:
                        var solveSendBackName = interceptTypes.Where(a => item.SolveInterceptType.Contains(a.Code)).Select(a => a.Name).ToList();
                        string name1 = solveSendBackName != null ? string.Join(",", solveSendBackName) : "";
                        oecInterceptOperateHistory.OperateContent = $"拦截类型：{name1};退回金额：{item.SendBackLimitAmount}";
                        break;
                    case InterceptOperateType.Update:
                        var originalInterceptTypeName = interceptTypes.Where(a => item.OriginalInterceptTypeCodes.Contains(a.Code)).Select(a => a.Name).ToList();
                        string name3 = originalInterceptTypeName != null ? string.Join(",", originalInterceptTypeName) : "";
                        string name2 = interceptTypeName != null ? string.Join(",", interceptTypeName) : "";
                        oecInterceptOperateHistory.OperateContent = $"{name3} 修改为 {name2}";
                        break;
                }
                oecInterceptOperateHistory.OperateType = item.OperateType;
                oecInterceptOperateHistory.InterceptId = item.InterceptId;
                oecInterceptOperateHistory.PRDetailId = item.PRDetailId;
                oecInterceptOperateHistory.Remark = item.Remark;
                oecInterceptOperateHistory.OperateTime = DateTime.Now;
                oecInterceptOperateHistory.SendBackLimitAmount = item.SendBackLimitAmount;
                oecInterceptOperateHistory.OperateUserId = item.OperateUserId;
                oecInterceptOperateHistory.OperateUserName = item.OperateUserName;
                oecInterceptOperateHistory.SolveInterceptType = item.SolveInterceptType;
                oecInterceptOperateHistorys.Add(oecInterceptOperateHistory);
            }
            await interceptOperateHistoryRepository.InsertManyAsync(oecInterceptOperateHistorys, true);
        }

        /// <summary>
        /// 发邮件通知至Hub团队
        /// </summary>
        /// <param name="pa"></param>
        /// <param name="interceptStatus"></param>
        /// <param name="backedEmails"></param>
        /// <param name="interceptByUserName">拦截时必须传参</param>
        /// <returns></returns>
        private async Task<InsertSendEmaillRecordDto> SendEmailToFinancialInitialApprovalAsync(PurPAApplication pa, InterceptStatus interceptStatus, string interceptByUserName, List<SendBackedEmailDto> backedEmails = null)
        {
            if (!pa.ApprovedUserId.HasValue)
                return null;
            var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var user = queryableUser.Where(a => a.Id == pa.ApprovedUserId.Value).FirstOrDefault();
            if (user == null)
                return null;
            var provider = _env.WebRootFileProvider;
            string bodyHtml = "";
            string subject = "";
            switch (interceptStatus)
            {
                case InterceptStatus.Intercepting://拦截
                    subject = $"[NexBPM消息中心]付款申请{pa.ApplicationCode}被拦截请及时前往BPCS处理";
                    bodyHtml = await provider.GetFileInfo("Templates/Email/InterceptEmail.html").ReadAsStringAsync();
                    string webhost = _configuration["SpeakerEmail:WebHost"] + $"/procure/payment/detail/{pa.Id}";//PA详情
                    bodyHtml = bodyHtml.Replace("{ApplicationCode}", pa.ApplicationCode);
                    bodyHtml = bodyHtml.Replace("{ApplicationLink}", webhost);
                    bodyHtml = bodyHtml.Replace("{InterceptByUserName}", interceptByUserName ?? "");
                    break;
                case InterceptStatus.Released://释放
                    subject = $"[NexBPM消息中心]付款申请{pa.ApplicationCode}被释放请及时前往BPCS处理";
                    bodyHtml = await provider.GetFileInfo("Templates/Email/InterceptReleasedEmail.html").ReadAsStringAsync();
                    string webhostReleased = _configuration["SpeakerEmail:WebHost"] + $"/procure/payment/detail/{pa.Id}";//PA详情
                    bodyHtml = bodyHtml.Replace("{ApplicationCode}", pa.ApplicationCode);
                    bodyHtml = bodyHtml.Replace("{ApplicationLink}", webhostReleased);
                    bodyHtml = bodyHtml.Replace("{InterceptByUserName}", interceptByUserName ?? "");
                    break;
                case InterceptStatus.SendBacked://退回
                    subject = $"[NexBPM消息中心]付款申请{pa.ApplicationCode}被退回请及时前往BPCS处理";
                    bodyHtml = await provider.GetFileInfo("Templates/Email/InterceptSendBackedEmail.html").ReadAsStringAsync();
                    string webhostSendBacked = _configuration["SpeakerEmail:WebHost"] + $"/procure/payment/detail/{pa.Id}";//PA详情
                    bodyHtml = bodyHtml.Replace("{ApplicationCode}", pa.ApplicationCode);
                    bodyHtml = bodyHtml.Replace("{ApplicationLink}", webhostSendBacked);
                    bodyHtml = bodyHtml.Replace("{InterceptByUserName}", interceptByUserName ?? "");
                    string prContent = "";
                    if (backedEmails != null)
                    {
                        foreach (var item in backedEmails)
                        {
                            prContent += $"<tr><td>{item.PRApplicationCode}</td><td>{item.RowNo}</td><td>{item.LimitAmount}</td><td>{item.TotalAmount}</td><td>{item.HistoryTotalAmount}</td></tr>";
                        }
                        bodyHtml = bodyHtml.Replace("{PRContent}", prContent);
                    }
                    break;
                default:
                    break;
            }

            var htmluser = bodyHtml.Replace("{UserName}", user.Name);
            return new InsertSendEmaillRecordDto
            {
                EmailAddress = string.Join(',', (new List<string>() { user.Email })),
                Subject = subject,
                Content = htmluser,
                SourceType = EmailSourceType.OECIntercept,
                Status = SendStatus.Pending,
                Attempts = 0,
            };
        }

        private (bool, List<InterceptQueryDetailDto>) GetInterceptQueryDetails(string detailsContent)
        {
            List<InterceptQueryDetailDto> result = new List<InterceptQueryDetailDto>();
            try
            {
                var contents = detailsContent.Split(';');
                foreach (var item in contents)
                {
                    if (string.IsNullOrWhiteSpace(item))
                        continue;
                    var details = item.Split('|');
                    result.Add(new InterceptQueryDetailDto
                    {
                        ApplicationCode = details[0],
                        VendorName = details[1],
                        InterceptTypeCode = details[2],
                        InterceptRemark = details[3]
                    });
                }
            }
            catch (Exception ex)
            {
                return (false, null);
            }
            return (true, result);
        }
        private (bool, List<InterceptQueryDetailDto>) GetBatchTackleInterceptQueryDetails(string detailsContent)
        {
            List<InterceptQueryDetailDto> result = new List<InterceptQueryDetailDto>();
            try
            {
                var contents = detailsContent.Split(';');
                foreach (var item in contents)
                {
                    if (string.IsNullOrWhiteSpace(item))
                        continue;
                    var details = item.Split('|');
                    result.Add(new InterceptQueryDetailDto
                    {
                        ApplicationCode = details[0],
                        VendorName = details[1],
                        InterceptTypeCode = details[2],
                        OperateType = details[3],
                        SendBackLimitAmount = string.IsNullOrWhiteSpace(details[4]) ? null : decimal.Parse(details[4]),
                        InterceptRemark = details[5]
                    });
                }
            }
            catch (Exception ex)
            {
                return (false, null);
            }
            return (true, result);
        }

        private string GetInterceptTypeName(List<DictionaryDto> interceptTypes, string codes)
        {
            if (string.IsNullOrWhiteSpace(codes))
                return "";
            var codelist = codes.Split(',').ToList();
            var nameList = interceptTypes.Where(a => codelist.Contains(a.Code)).Select(a => a.Name).ToList();
            return string.Join(',', nameList);
        }
        #endregion

        #region 私有查询组合方法
        /// <summary>
        /// 查询PR信息
        /// </summary>
        /// <param name="prApplications"></param>
        /// <param name="prApplicationDetails"></param>
        /// <returns></returns>
        private IQueryable<QueryInfoDto> QueryPRInfo(IQueryable<PurPRApplication> prApplications, IQueryable<PurPRApplicationDetail> prApplicationDetails) =>
                        prApplications.GroupJoin(prApplicationDetails, a => a.Id, b => b.PRApplicationId, (a, b) => new { pr = a, prds = b })
            .SelectMany(a => a.prds.DefaultIfEmpty(), (a, b) => new QueryInfoDto
            {
                Id = a.pr.Id,
                ApplicationCode = a.pr.ApplicationCode,
                PRDetailId = b.Id,
                PRStatus = a.pr.Status,
                ApplyTime = a.pr.ApplyTime,
                ApplyUserName = a.pr.ApplyUserIdName,
                RowNo = b.RowNo,
                PayMethod = b.PayMethod,
                TotalAmount = b.TotalAmount,
                VendorName = b.VendorName,
                VendorCode = b.VendorCode,
                TotalAmountRMB = b.TotalAmountRMB,
                CompanyName = a.pr.CompanyIdName,
            });

        /// <summary>
        /// 查询Po信息
        /// </summary>
        /// <param name="pOApplications"></param>
        /// <param name="pOApplicationDetails"></param>
        /// <returns></returns>
        private IQueryable<QueryInfoDto> QueryPOInfo(IQueryable<PurPOApplication> pOApplications, IQueryable<PurPOApplicationDetails> pOApplicationDetails) =>
                        pOApplications.GroupJoin(pOApplicationDetails, a => a.Id, b => b.POApplicationId, (a, b) => new { po = a, pods = b })
            .SelectMany(a => a.pods.DefaultIfEmpty(), (a, b) => new QueryInfoDto
            {
                Id = a.po.Id,
                ApplicationCode = a.po.ApplicationCode,
                PRDetailId = b.PRDetailId,
                POStatus = a.po.Status,
            });

        /// <summary>
        /// 查询GR信息
        /// </summary>
        /// <param name="pOApplications"></param>
        /// <param name="pOApplicationDetails"></param>
        /// <returns></returns>
        private IQueryable<QueryInfoDto> QueryGRInfo(IQueryable<PurGRApplication> grApplications, IQueryable<PurGRApplicationDetail> grApplicationDetails) =>
                        grApplications.GroupJoin(grApplicationDetails, a => a.Id, b => b.GRApplicationId, (a, b) => new { gr = a, grds = b })
            .SelectMany(a => a.grds.DefaultIfEmpty(), (a, b) => new QueryInfoDto
            {
                Id = a.gr.Id,
                ApplicationCode = a.gr.ApplicationCode,
                PRDetailId = b.PRDetailId,
                GRStatus = a.gr.Status,
            });

        /// <summary>
        /// 查询PA信息
        /// </summary>
        /// <param name="pOApplications"></param>
        /// <param name="pOApplicationDetails"></param>
        /// <returns></returns>
        private IQueryable<QueryInfoDto> QueryPAInfo(IQueryable<PurPAApplication> paApplications, IQueryable<PurPAApplicationDetail> paApplicationDetails) =>
                        paApplications.GroupJoin(paApplicationDetails, a => a.Id, b => b.PurPAApplicationId, (a, b) => new { pa = a, pads = b })
            .SelectMany(a => a.pads.DefaultIfEmpty(), (a, b) => new QueryInfoDto
            {
                Id = a.pa.Id,
                ApplicationCode = a.pa.ApplicationCode,
                PRDetailId = b.PRDetailId,
                TotalAmount = a.pa.PayTotalAmount,
                DeliveryMode = a.pa.DeliveryMode,
                PAStatus = a.pa.Status,
                PADTotalAmount = b.PaymentAmount,
                ExchangeRate = a.pa.ExchangeRate,
            });
        #endregion
    }
}
