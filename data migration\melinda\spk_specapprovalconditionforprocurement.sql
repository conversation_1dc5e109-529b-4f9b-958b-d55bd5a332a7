select newid()  as spk_NexBPMCode
,a.spk_costcenter as spk_BPMCode
,b.spk_name as spk_costcenter
,sc.spk_NexBPMCode as spk_company
,a.spk_type,a.spk_Name
,c.spk_name as spk_consumercategories
,d.spk_NexBPMCode as spk_natureofexpenses
,a.flg
into #spk_specapprovalconditionforprocurement
from spk_specapprovalconditionforprocurement_Tmp a
left join spk_costcentermasterdata_Tmp b on a.spk_costcenter=b.spk_BPMCode
left join spk_consume_tmp c on a.spk_consumercategories=c.spk_BPMCode
left join spk_costnature d on a.spk_natureofexpenses=d.spk_BPMCode
left join ODS_T_RESOURCE r1
on r1.Res_Data =a.spk_company and r1.res_parent_code = '61a3f911b5ae4bc98cddd441833d861e'
left join spk_companymasterdata sc 
on r1.Res_Code =sc.spk_BPMCode 


IF OBJECT_ID(N'dbo.spk_specapprovalconditionforprocurement', N'U') IS NOT NULL
BEGIN
	update a 
	set a.spk_BPMCode              = b.spk_BPMCode
        ,a.spk_costcenter          = b.spk_costcenter
        ,a.spk_company             = b.spk_company
        ,a.spk_type                = b.spk_type
        ,a.spk_Name                = b.spk_Name
        ,a.spk_consumercategories  = b.spk_consumercategories
        ,a.spk_natureofexpenses    = b.spk_natureofexpenses
        ,a.flg                     = b.flg
    from dbo.spk_specapprovalconditionforprocurement a
    join #spk_specapprovalconditionforprocurement b on isnull(a.spk_BPMCode,'null') = isnull(b.spk_BPMCode,'null')
    
    insert into dbo.spk_specapprovalconditionforprocurement
    select a.spk_NexBPMCode
          ,a.spk_BPMCode
          ,a.spk_costcenter
          ,a.spk_company
          ,a.spk_type
          ,a.spk_Name
          ,a.spk_consumercategories
          ,a.spk_natureofexpenses
          ,a.flg
	from #spk_specapprovalconditionforprocurement a
	where NOT EXISTS (SELECT * FROM dbo.spk_specapprovalconditionforprocurement where isnull(spk_BPMCode,'null') = isnull(a.spk_BPMCode,'null'))
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into dbo.spk_specapprovalconditionforprocurement from #spk_specapprovalconditionforprocurement
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
