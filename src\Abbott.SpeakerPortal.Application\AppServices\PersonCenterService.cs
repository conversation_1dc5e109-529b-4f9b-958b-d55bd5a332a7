﻿using Abbott.SpeakerPortal.Consent;
using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Agent;
using Abbott.SpeakerPortal.Contracts.Budget.Subbudget;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.PersonCenter;
using Abbott.SpeakerPortal.Contracts.System.Login;
using Abbott.SpeakerPortal.Contracts.System.User;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Dataverse.Contracts;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities;
using Abbott.SpeakerPortal.Entities.Budget;
using Abbott.SpeakerPortal.Entities.Common.Consent;
using Abbott.SpeakerPortal.Entities.User;
using Abbott.SpeakerPortal.Entities.UserRoles;
using Abbott.SpeakerPortal.Entities.VendorApplicationFinancials;
using Abbott.SpeakerPortal.Entities.VendorApplicationPersonals;
using Abbott.SpeakerPortal.Entities.VendorApplications;
using Abbott.SpeakerPortal.Entities.VendorPersonals;
using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Extension;
using Abbott.SpeakerPortal.Person;
using Abbott.SpeakerPortal.PersonCenter;
using Abbott.SpeakerPortal.Redis;
using Abbott.SpeakerPortal.Role;
using Abbott.SpeakerPortal.User;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;

using Newtonsoft.Json;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Data;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using Volo.Abp.PermissionManagement;

using static Abbott.SpeakerPortal.Enums.DataverseEnums;
using static Abbott.SpeakerPortal.Enums.ResignationTransfer;
using static Abbott.SpeakerPortal.Permissions.SpeakerPortalPermissions;

namespace Abbott.SpeakerPortal.PersonServices
{
    /// <summary>
    /// PersonService
    /// </summary>
    public class PersonCenterService : SpeakerPortalAppService, IPersonCenterService
    {
        /// <summary>
        /// The service provider
        /// </summary>
        private readonly IServiceProvider _serviceProvider;

        /// <summary>
        /// The logger
        /// </summary>
        private readonly ILogger<PersonCenterService> _logger;

        /// <summary>
        /// The consentsetvice
        /// </summary>
        private readonly IConsentService _consentsetvice;

        /// <summary>
        /// Initializes a new instance of the <see cref="PersonCenterService"/> class.
        /// </summary>
        /// <param name="serviceProvider">The service provider.</param>
        public PersonCenterService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _logger = serviceProvider.GetService<ILogger<PersonCenterService>>();
            _consentsetvice = _serviceProvider.GetService<IConsentService>();
        }

        /// <summary>
        /// Gets the person asynchronous.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> GetPersonAsync()
        {
            try
            {
                if (!CurrentUser.IsAuthenticated)
                    return default;

                //如果是讲者，获取讲者对应的编号用于获取信息
                var queryVendor = await _serviceProvider.GetService<IVendorRepository>().GetQueryableAsync();
                var queryVendorPersonal = await _serviceProvider.GetService<IVendorPersonalRepository>().GetQueryableAsync();
                var queryVendorApp = await _serviceProvider.GetService<IVendorApplicationRepository>().GetQueryableAsync();
                var queryVendorAppPersonal = await _serviceProvider.GetService<IVendorApplicationPersonalRepository>().GetQueryableAsync();
                var queryUser = await _serviceProvider.GetService<IRepository<IdentityUser>>().GetQueryableAsync();

                var speakers = queryVendor.Where(a => a.UserId == CurrentUser.Id)
                    .Join(queryVendorPersonal, a => a.Id, a => a.VendorId, (a, b) => new { newList = 1, IsDraft = false, a.Id, a.VendorType, a.OpenId, a.UserId, Dt = a.LastModificationTime ?? a.CreationTime, b.SPName }).OrderByDescending(a => a.Dt).Take(1)
                    .Union
                    (
                        queryVendorApp.Where(a => a.UserId == CurrentUser.Id)
                        .Join(queryVendorAppPersonal, a => a.Id, a => a.ApplicationId, (a, b) => new { newList = 2, IsDraft = true, a.Id, a.VendorType, a.OpenId, a.UserId, Dt = a.LastModificationTime ?? a.CreationTime, b.SPName })
                        .OrderByDescending(a => a.Dt).Take(1)
                    )
                    .Join(queryUser, a => a.UserId, a => a.Id, (a, b) => new { Speaker = a, User = new { b.Id, b.Name, b.PhoneNumber, OpenId = b.GetProperty<string>("OpenId", null) } })
                    .ToArray();

                if (!speakers.Any())
                    return MessageResult.FailureResult("");

                var data = speakers.FirstOrDefault(a => a.Speaker.newList == 1);
                if (data == null)
                    data = speakers.FirstOrDefault(a => a.Speaker.newList == 2);

                var speaker = data.Speaker;
                var user = data.User;

                var userLastLogin = await LazyServiceProvider.LazyGetService<IUserService>().GetUserLastLogin(user.Id);

                var result = new PersonInfoDto
                {
                    Name = string.IsNullOrEmpty(speaker?.SPName) ? user.Name : speaker?.SPName,
                    PhoneNumber = user.PhoneNumber,
                    OneId = user.OpenId,
                    AppUserId = user.Id,
                    LoginTime = userLastLogin?.LastLoginTime.ToString("yyyy-MM-dd HH:mm:ss"),
                    VonderId = speaker != null ? speaker?.Id.ToString() : "",
                    IsHCP = speaker?.VendorType == VendorTypes.HCPPerson,
                    IsDraft = speaker != null ? speaker.IsDraft : false
                };

                return MessageResult.SuccessResult(result);
            }
            catch (Exception ex)
            {
                return MessageResult.FailureResult($"PersonCenterService's GetPersonAsync has an error : {ex.Message}, user id: {CurrentUser.Id}");
            }
        }

        /// <summary>
        /// Verifies the cosent list.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<List<ConsentVerifyData>> VerifyCosentListAsync(ConsentVerifyRequestDto request)
        {
            try
            {
                //获取需要检查的最新版签署版本号
                var _configuration = LazyServiceProvider.GetService<IConfiguration>();
                string consentInfo = _configuration["Consent:ConsentCode"];
                string consentName = _configuration["Consent:ConsentName"];
                request.ConsentCode = consentInfo;

                var verifyConsent = await _consentsetvice.VerifyWhetherUserHasAcceptConsentAsync(request);
                var result = verifyConsent.Data;

                if (request.IsAccepted == Enums.VerifyConsent.Accepted)
                {
                    result = result.Where(p => p.Consented).ToList();
                }

                if (request.IsAccepted == Enums.VerifyConsent.Rejected)
                {
                    result = result.Where(p => !p.Consented).ToList();
                }

                if (!string.IsNullOrEmpty(consentName) && result.Count > 0)
                {
                    var ccCodes = consentInfo.Split(',');
                    var ccNames = consentName.Split(',');
                    var queryConsentSigned = await LazyServiceProvider.LazyGetService<IConsentSignedRepository>().GetQueryableAsync();
                    var ccSigned = queryConsentSigned.Where(x => x.OneId == request.OneId && x.AppUserId == request.AppUserId && ccCodes.Contains(x.ConsentCode)).ToArray();
                    foreach (var cc in result)
                    {
                        var idx = Array.IndexOf(ccCodes, cc.ConsentCode);
                        if (idx >= 0)
                            cc.ConsentName = ccNames[idx];
                        if (!cc.Consented)//已签署的有签署记录可查，未签署则没有签署记录即没有签署时间
                            continue;
                        if (ccSigned.Length == 0)
                            continue;
                        var signeds = ccSigned.Where(x => x.ConsentCode == cc.ConsentCode && x.ConsentVersion == cc.Version.ToString());
                        if (!signeds.Any())
                            continue;
                        var last = signeds.OrderByDescending(x => x.CreationTime).First();
                        cc.AcceptedTime = last.CreationTime.ToString("yyyy-MM-dd HH:mm:ss");
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError($"PersonCenterService's VerifyCosentListAsync has an error : {ex.Message}");
                return null;
            }

        }

        /// <summary>
        /// Gets the unread message count asynchronous.
        /// </summary>
        /// <returns></returns>
        public async Task<int> GetUnreadMessageCountAsync(Guid ownerId)
        {
            var msgRepository = _serviceProvider.GetService<IMessageInfoRepository>();
            var count = await msgRepository.CountAsync(a => a.OwnerId == ownerId && !a.IsRead);

            return count;
        }

        /// <summary>
        /// Cleans the unread messages asynchronous.
        /// </summary>
        /// <param name="ownerId"></param>
        /// <returns></returns>
        public async Task<int> CleanUnreadMessagesAsync(Guid ownerId)
        {
            var msgRepository = _serviceProvider.GetService<IMessageInfoRepository>();
            var count = await msgRepository.CleanUnreadMessagesAsync(ownerId);

            return count;
        }

        /// <summary>
        /// 已读单条消息
        /// </summary>
        /// <param name="id">The identifier.</param>
        /// <returns></returns>
        public async Task<MessageResult> UpdateMessageStatusAsync(Guid id)
        {
            var _messageRepository = _serviceProvider.GetService<IMessageInfoRepository>();
            var messagesQueryable = await _messageRepository.GetQueryableAsync();
            var message = messagesQueryable.FirstOrDefault(m => m.Id == id && !m.IsRead);
            if (message == null)
            {
                return MessageResult.FailureResult("未找到该消息或消息已读");
            }
            message.IsRead = true;

            await _messageRepository.UpdateAsync(message, true);

            return MessageResult.SuccessResult(true);
        }

        /// <summary>
        /// Gets the message list asynchronous.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<List<MessageDto>> GetMessageListAsync(MessageListRequestDto request)
        {
            try
            {
                var _messageRepository = _serviceProvider.GetService<IMessageInfoRepository>();
                var messagesQueryable = await _messageRepository.GetQueryableAsync();
                var messages = messagesQueryable.Where(m => m.OwnerId == CurrentUser.Id && m.IsRead == request.IsRead)
                    .ToList();
                var messageMap = ObjectMapper.Map<List<MessageInfo>, List<MessageDto>>(messages);
                foreach (var message in messageMap)
                {
                    message.ReadAt = message.ReadTime.HasValue ? message.ReadTime.Value.ToString("yyyy-MM-dd HH:mm") : string.Empty;
                }

                return messageMap;
            }
            catch (Exception ex)
            {
                _logger.LogError($"PersonCenterService's GetMessageListAsync has an error : {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Gets the consent URL.
        /// </summary>
        /// <param name="code">The code.</param>
        /// <returns></returns>
        public async Task<string> GetConsentUrl(string code)
        {
            try
            {
                var requestCodes = new List<string>()
                {
                    code
                };
                var consentResponse = await _consentsetvice.GetConsentInfoAsync(requestCodes);
                var consent = consentResponse.FirstOrDefault();
                if (consent.Data == null)
                {
                    return string.Empty;
                }
                var url = consent.Data.Url;
                return url;
            }
            catch (Exception ex)
            {
                _logger.LogError($"PersonCenterService's GetConsentUrl has an error : {ex.Message}");
                return string.Empty;
            }
        }


        /// <summary>
        /// Accepts the or reject consent asynchronous.
        /// </summary>
        /// <param name="requests">The requests.</param>
        /// <returns></returns>
        public async Task<bool> AcceptOrRejectConsentAsync(List<SetAcceptDto> requests)
        {
            try
            {
                var consentResponse = await _consentsetvice.AcceptOrRejectConsentAsync(requests);
                var _userRepository = _serviceProvider.GetService<Volo.Abp.Identity.IIdentityUserRepository>();
                var userList = await _userRepository.GetListAsync();
                //记录讲者签署版本
                var groupedResults = requests
                    .GroupBy(x => x.AppUserId)
                    .Select(g => new
                    {
                        AppUserId = g.Key,
                        result = $"[{string.Join(",", g.Select(x => $"{{\"ConsentCode\":\"{x.ConsentCode}\",\"Version\":\"{x.Version}\"}}"))}]"
                    });

                foreach (var group in groupedResults)
                {
                    var user = userList.FirstOrDefault(f => f.Id == Guid.Parse(group.AppUserId));
                    if (user != null)
                    {
                        user.SetProperty("SignedVersion", group.result);
                        await _userRepository.UpdateAsync(user, true);
                    }
                }
                return consentResponse.Data;
            }
            catch (Exception ex)
            {
                _logger.LogError($"PersonCenterService's AcceptOrRejectConsentAsync has an error : {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取当前用户信息
        /// </summary>
        /// <returns></returns>
        public async Task<CurrentUserResponseDto> GetCurrentUserInfoAsync()
        {
            if (!CurrentUser.IsAuthenticated)
                return default;

            var user = await LazyServiceProvider.LazyGetService<IIdentityUserReadonlyRepository>().FindAsync(CurrentUser.Id.Value);
            if (user == null)
                return default;

            //获取权限
            var userPermissions = await LazyServiceProvider.LazyGetService<IPermissionManager>().GetAllForUserAsync(CurrentUser.Id.Value);
            var userP = userPermissions.Where(a => a.IsGranted).Select(a => a.Name).ToList();
            List<string> EnablePermissionAll = [];
            foreach (var item in userP)
            {
                if (!EnablePermissionAll.Contains(item.Split(".")[0]))
                {
                    EnablePermissionAll.Add(item.Split(".")[0]);
                }
            }
            EnablePermissionAll.AddRange(userP);

            //获取角色
            var identityRoleRepository = LazyServiceProvider.LazyGetService<IIdentityRoleRepository>();
            var roleList = await identityRoleRepository.GetListAsync();
            var queryUserRole = await LazyServiceProvider.LazyGetService<IUserRoleRepository>().GetQueryableAsync();
            var userRoles = queryUserRole.Where(a => a.UserId == CurrentUser.Id.Value).Select(a => a.RoleId).ToList();
            var roles = new List<string>();

            if (userRoles.Any())
            {
                roles = roleList.Where(a => userRoles.Contains(a.Id)).Select(a => a.Name).ToList();

            }
            //cross bu登录用
            var isMarketingEventUser = roles.Count == 1 && roles[0].Equals(LoginConstant.MarketingEventUser);
            var userLastLogin = await LazyServiceProvider.LazyGetService<IUserService>().GetUserLastLogin(user.Id);
            var currentUserDto = new CurrentUserResponseDto
            {
                Id = CurrentUser.Id.Value,
                Name = CurrentUser.Name,
                MainDepartmentId = user.GetProperty<Guid?>("MainDepartmentId"),
                //Roles = CurrentUser.Roles,
                Roles = roles,
                EnablePermission = EnablePermissionAll.ToArray(),
                LoginTime = userLastLogin?.LastLoginTime.ToString("yyyy-MM-dd HH:mm:ss")
            };

            if (!currentUserDto.MainDepartmentId.HasValue && !string.IsNullOrWhiteSpace(CurrentUser.Name) && !string.IsNullOrWhiteSpace(user.GetProperty<string>(EntityConsts.IdentityUser.StaffCode)) && !isMarketingEventUser)
            {
                currentUserDto.IsNewWcUser = true;
            }
            #region 特殊角色处理
            var specialPermission = new SpecialPermission()
            {
                //业务管理员  集团财务
                IsApplicationRecordDisplay = RoleExtension.BG_ROLES.Intersect(currentUserDto.Roles, StringComparer.CurrentCultureIgnoreCase).Any(),
                //业务管理员
                IsBizManager = RoleExtension.Biz_ROLES.Intersect(currentUserDto.Roles, StringComparer.CurrentCultureIgnoreCase).Any(),
                //是否是EPD用户
                IsEPDUser = RoleExtension.EPD_ROLES.Intersect(currentUserDto.Roles, StringComparer.CurrentCultureIgnoreCase).Any(),
                //集团财务/合规管理员/报税管理员/系统管理员/业务管理员
                IsViewAllVendorDraft = RoleExtension.AGOTB_ROLES.Intersect(currentUserDto.Roles, StringComparer.CurrentCultureIgnoreCase).Any(),
                //集团财务
                IsGroupFinance = currentUserDto.Roles.Any(a => a == RoleNames.GroupFinance),
                //OEC管理员
                IsOECAdmin = currentUserDto.Roles.Any(a => a == RoleNames.OEC_Admin),
                //合规调查员
                IsOECInvestigator = currentUserDto.Roles.Any(a => a == RoleNames.OEC_Investigator),
            };
            currentUserDto.SpecialPermission = specialPermission;
            #endregion
            return currentUserDto;
        }

        /// <summary>
        /// 设置新用户的主部门和部门关系
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> SetDepartmentForNewUserAsync(SetDepartmentForNewUserRequestDto request)
        {
            //1.添加此Staff与Organizationmasterdata的关系数据
            //2.设置此AbpUser的主部门并启用该用户
            var userRepository = LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>();
            var userQueryable = await userRepository.GetQueryableAsync();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();

            var user = userQueryable.FirstOrDefault(x => x.Id == request.UserId);
            if (user == null)
                return MessageResult.FailureResult("用户参数错误");

            var staff = await dataverseService.GetStaffs(request.UserId.ToString());
            if (staff == null)
                return MessageResult.FailureResult("用户参数错误");

            var org = await dataverseService.GetOrganizations(request.DepartmentId.ToString());
            if (org == null)
                return MessageResult.FailureResult("部门参数错误");

            var dataverseRepository = _serviceProvider.GetService<IDataverseRepository>();

            var query = new QueryExpression("spk_organizational_staff");
            query.ColumnSet.AddColumns("spk_organizationalmasterdataid", "spk_staffmasterdataid");
            query.Criteria.AddCondition(new ConditionExpression("spk_staffmasterdataid", ConditionOperator.Equal, request.UserId));
            query.Criteria.AddCondition(new ConditionExpression("spk_organizationalmasterdataid", ConditionOperator.Equal, request.DepartmentId));
            query.Criteria.AddCondition(new ConditionExpression("statecode", ConditionOperator.Equal, 0));
            var ettCollection = await dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);
            if (ettCollection == null || !ettCollection.Any())
            {
                var ett = new Entity("spk_organizational_staff");
                ett["spk_staffmasterdataid"] = new EntityReference("spk_staffmasterdata", request.UserId);
                ett["spk_organizationalmasterdataid"] = new EntityReference("spk_organizationalmasterdata", request.DepartmentId);
                var res = await dataverseRepository.DataverseClient.CreateAsync(ett);
                if (!res.Equals(Guid.Empty))
                {
                    user.SetIsActive(true);
                    user.SetProperty(EntityConsts.IdentityUser.MainDepartmentId, request.DepartmentId);
                    var resUser = await userRepository.UpdateAsync(user);
                    return MessageResult.SuccessResult();
                }
                else
                    return MessageResult.FailureResult("设置失败");
            }
            else
                return MessageResult.FailureResult("设置失败");

        }


        /// <summary>
        /// 获取的当前用户的拥有的角色对应的级别(优先取高级别,0级最高)
        /// </summary>
        /// <returns></returns>
        public RoleLevel GetMyRoleLevel()
        {
            var level = RoleLevel.Unkonw;

            if (!CurrentUser.IsAuthenticated || CurrentUser.Roles.Length == 0)
                return level;

            var map = RoleLevelMapping.RoleLevelMap;

            foreach (var kvp in map)
            {
                if (kvp.Value.Length == 0)
                    continue;
                if (kvp.Value.Intersect(CurrentUser.Roles, StringComparer.CurrentCultureIgnoreCase).Any())
                {
                    level = kvp.Key;
                    break;
                }
            }

            return level;
        }
        /// <summary>
        /// 获取的当前用户的拥有的角色对应的级别(优先取高级别,0级最高)--eflow
        /// </summary>
        /// <returns></returns>
        public RoleLevel GetEFlowMyRoleLevel(bool IsFoc = false)
        {
            var level = RoleLevel.Unkonw;

            if (!CurrentUser.IsAuthenticated || CurrentUser.Roles.Length == 0)
                return level;

            Dictionary<RoleLevel, string[]> map = [];

            if (IsFoc)
                map = RoleLevelMapping.RoleE_FlowFOCLevelMap;
            else map = RoleLevelMapping.RoleE_FlowLevelMap;

            foreach (var kvp in map)
            {
                if (kvp.Value.Length == 0)
                    continue;
                if (kvp.Value.Intersect(CurrentUser.Roles, StringComparer.CurrentCultureIgnoreCase).Any())
                {
                    level = kvp.Key;
                    break;
                }
            }

            return level;
        }
        /// <summary>
        /// 获取当前用户有用的所有的角色分别对应的级别
        /// </summary>
        /// <returns></returns>
        private List<RoleLevel> GetMyRoleLevels()
        {
            var myRoleLevels = new List<RoleLevel>();
            if (!CurrentUser.IsAuthenticated || CurrentUser.Roles.Length == 0)
                return myRoleLevels;

            var map = RoleLevelMapping.RoleLevelMap;

            foreach (var kvp in map)
            {
                if (kvp.Value.Length == 0)
                    continue;
                if (kvp.Value.Intersect(CurrentUser.Roles, StringComparer.CurrentCultureIgnoreCase).Any())
                    myRoleLevels.Add(kvp.Key);
            }
            return myRoleLevels;
        }


        /// <summary>
        /// 获取可用的BU(根据当前用户的角色获取可用的BU)
        /// </summary>
        /// <returns></returns>
        public async Task<IEnumerable<Guid>> GetUsableBuAsync()
        {
            var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var queryAuthorizedBudgetBu = await LazyServiceProvider.LazyGetService<IBdAuthorizedBudgetBuRepository>().GetQueryableAsync();

            var commonService = LazyServiceProvider.LazyGetService<ICommonService>();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();

            var roleLevel = GetMyRoleLevel();

            List<DepartmentDto> buDtos = [];

            switch (roleLevel)
            {
                case RoleLevel.Admin://所有数据
                    //bu.AddRange(dataverseService.GetDivisions().Result.Select(x => x.Id));
                    var divisions = await dataverseService.GetDivisions();
                    buDtos.AddRange(divisions);
                    break;
                case RoleLevel.Manager:
                    var userInfo = queryableUser.FirstOrDefault(x => x.Id == CurrentUser.Id);
                    //所属主部门所在BU的数据
                    if (CurrentUser.Roles.Contains(RoleNames.DeptAdmin))
                    {
                        var mainDeptId = userInfo.GetProperty<Guid?>(EntityConsts.IdentityUser.MainDepartmentId);
                        if (mainDeptId.HasValue)
                        {
                            var mainDeptDtos = await dataverseService.GetOrganizations(mainDeptId.ToString(), 1, stateCode: null);
                            if (mainDeptDtos != null)
                            {
                                var mainDeptDto = mainDeptDtos.First();
                                if (mainDeptDto.OrganizationType == Organization.OrganizationType.Bu)
                                {
                                    //bu.Add(mainDeptDto.Id);
                                    buDtos.Add(mainDeptDto);
                                }
                                else if (mainDeptDto.OrganizationType == Organization.OrganizationType.Dept)
                                {
                                    var parentOrgs = await commonService.GetParentOrgs(mainDeptDto);
                                    if (parentOrgs.Any())
                                    {
                                        var parentBu = parentOrgs.FirstOrDefault(x => x.OrganizationType == Organization.OrganizationType.Bu);
                                        if (parentBu != null)
                                            //bu.Add(parentBu.Id);
                                            buDtos.Add(parentBu);
                                    }
                                }
                            }
                        }
                    }
                    //管辖BU的数据
                    if (CurrentUser.Roles.Contains(RoleNames.DeptFinance))
                    {
                        var queryAuthorizedBudgetBuDatas = queryAuthorizedBudgetBu.Where(x => x.UserId == userInfo.Id).Select(s => s.BuId).ToList();
                        //bu.AddRange(queryAuthorizedBudgetBuDatas);

                        var allOrgs = await dataverseService.GetOrganizations(pattern: null, stateCode: null);
                        buDtos.AddRange(allOrgs.Where(x => queryAuthorizedBudgetBuDatas.Contains(x.Id)));
                    }
                    break;
                case RoleLevel.Leader://审批人:Approver~所属部门的所有数据，以及自己作为EPO Leader的部门，作为单据申请部门的数据
                case RoleLevel.Owner://预算负责人:BudgetOwner~自己作为预算负责人的预算对应数据
                case RoleLevel.Applicant://自己作为申请人的数据:BU不可选，留空
                case RoleLevel.Unkonw:
                default:
                    break;
            }

            //return bu.Distinct();
            var datas = buDtos.Distinct().OrderBy(x => x.DepartmentName);
            //var dis = datas.Select(x => new { x.Id, x.DepartmentName });
            return datas.Select(x => x.Id);
        }


        /// <summary>
        /// 根据当前用户的角色获取可用的BU/部门/子预算范围的GUID
        /// </summary>
        /// <returns></returns>
        public async Task<IEnumerable<Guid>> GetIdsByRoles(RoleLevel? roleLevel = null, ResignationTransfer.TaskFormCategory? bizTypeCategory = null, Guid? agentOperatorId = null)
        {
            var queryableUser = await LazyServiceProvider.LazyGetService<IIdentityUserReadonlyRepository>().GetQueryableAsync();
            var queryAuthorizedBudgetBu = await LazyServiceProvider.LazyGetService<IBdAuthorizedBudgetBuReadonlyRepository>().GetQueryableAsync();

            var commonService = LazyServiceProvider.LazyGetService<ICommonService>();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();

            if (!roleLevel.HasValue)
                roleLevel = GetMyRoleLevel();

            List<DepartmentDto> bus = [];

            List<Guid> guids = [];

            switch (roleLevel)
            {
                case RoleLevel.Admin://所有数据
                    var divisions = await dataverseService.GetDivisions();
                    bus.AddRange(divisions);
                    guids.AddRange(bus.Select(x => x.Id).Distinct());
                    break;
                case RoleLevel.Manager:
                    var userInfo = queryableUser.FirstOrDefault(x => x.Id == CurrentUser.Id);
                    //所属主部门所在BU的数据
                    /*202502-28 4002:【Enhancement】部门管理员的数据权限需要替换成和部门财务一样的，允许部门管理员看所属BU的数据权限
                    if (CurrentUser.Roles.Contains(RoleNames.DeptAdmin))
                    {
                        var mainDeptId = userInfo.GetProperty<Guid?>(EntityConsts.IdentityUser.MainDepartmentId);
                        if (mainDeptId.HasValue)
                        {
                            var mainDeptDtos = await dataverseService.GetOrganizations(mainDeptId.ToString(), 1, stateCode: null);
                            if (mainDeptDtos != null)
                            {
                                var mainDeptDto = mainDeptDtos.First();
                                if (mainDeptDto.OrganizationType == Organization.OrganizationType.Bu)
                                {
                                    //bu.Add(mainDeptDto.Id);
                                    bus.Add(mainDeptDto);
                                }
                                else if (mainDeptDto.OrganizationType == Organization.OrganizationType.Dept)
                                {
                                    var parentOrgs = await commonService.GetParentOrgs(mainDeptDto);
                                    if (parentOrgs.Any())
                                    {
                                        var parentBu = parentOrgs.FirstOrDefault(x => x.OrganizationType == Organization.OrganizationType.Bu);
                                        if (parentBu != null)
                                            //bu.Add(parentBu.Id);
                                            bus.Add(parentBu);
                                    }
                                }
                            }
                        }
                    }
                    */
                    //管辖BU的数据
                    var queryAuthorizedBudgetBuDatas = queryAuthorizedBudgetBu.Where(x => x.UserId == userInfo.Id).Select(s => s.BuId).ToList();
                    var allOrgs = await dataverseService.GetOrganizations(pattern: null, stateCode: null);
                    bus.AddRange(allOrgs.Where(x => queryAuthorizedBudgetBuDatas.Contains(x.Id)));
                    guids.AddRange(bus.Select(x => x.Id).Distinct());
                    break;
                case RoleLevel.Leader://审批人:Approver~所属部门的所有数据，以及自己作为EPO Leader的部门，作为单据申请部门的数据
                    var orgIds = await commonService.GetAllChildrenOrgs(CurrentUser.Id.Value);
                    guids.AddRange(orgIds);
                    break;
                case RoleLevel.Owner://预算负责人:BudgetOwner~自己作为预算负责人的预算对应数据
                    var subbudgetService = LazyServiceProvider.LazyGetService<ISubbudgetService>();
                    var subbudgetIds = await subbudgetService.GetSubbudgetsByOwner(CurrentUser.Id.Value);
                    guids.AddRange(subbudgetIds.Distinct());
                    break;
                case RoleLevel.Applicant://自己作为申请人的数据:BU不可选，留空
                case RoleLevel.Unkonw:
                default:
                    if (CurrentUser.Id.HasValue)
                    {
                        var originalOperators = await LazyServiceProvider.LazyGetService<IAgencyService>().GetOriginalOperators(new GetAgentOperatorsRequestDto
                        {
                            AgentOperatorId = agentOperatorId,
                            BusinessTypeCategory = bizTypeCategory
                        });
                        guids.AddRange(originalOperators.Select(x => x.Key));
                    }
                    break;
            }

            return guids;
        }


        /// <summary>
        /// 根据当前用户的所有角色级别获取对应的可用的BU/部门/子预算范围的GUID
        /// </summary>
        /// <param name="roleLevels"></param>
        /// <param name="bizTypeCategory"></param>
        /// <param name="agentOperatorId"></param>
        /// <returns></returns>
        public async Task<Dictionary<RoleLevel, IEnumerable<Guid>>> GetIdsByRoleLevels(List<RoleLevel> roleLevels = null, ResignationTransfer.TaskFormCategory? bizTypeCategory = null, Guid? agentOperatorId = null)
        {
            var queryableUser = await LazyServiceProvider.LazyGetService<IIdentityUserReadonlyRepository>().GetQueryableAsync();
            var queryAuthorizedBudgetBu = await LazyServiceProvider.LazyGetService<IBdAuthorizedBudgetBuReadonlyRepository>().GetQueryableAsync();

            var commonService = LazyServiceProvider.LazyGetService<ICommonService>();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();

            if (roleLevels == null || roleLevels.Count == 0)
                roleLevels = GetMyRoleLevels();

            Dictionary<RoleLevel, IEnumerable<Guid>> dict = [];

            if (roleLevels.Contains(RoleLevel.Admin))//管理员可以看所有数据，则无需关注其他Role
                return dict;

            foreach (var roleLevel in roleLevels)
            {
                switch (roleLevel)
                {
                    case RoleLevel.Admin://所有数据
                        //dict.Add(roleLevel, null);
                        break;
                    case RoleLevel.Manager://管辖BU的数据
                        List<DepartmentDto> bus = [];
                        var userInfo = queryableUser.FirstOrDefault(x => x.Id == CurrentUser.Id);
                        var queryAuthorizedBudgetBuDatas = queryAuthorizedBudgetBu.Where(x => x.UserId == userInfo.Id).Select(s => s.BuId).ToList();
                        var allOrgs = await dataverseService.GetOrganizations(pattern: null, stateCode: null);
                        bus.AddRange(allOrgs.Where(x => queryAuthorizedBudgetBuDatas.Contains(x.Id)));
                        if (bus.Count > 0)
                            dict.Add(roleLevel, bus.Select(x => x.Id).Distinct());
                        break;
                    case RoleLevel.Leader://审批人:Approver~所属部门的所有数据，以及自己作为EPO Leader的部门，作为单据申请部门的数据
                        var orgIds = await commonService.GetAllChildrenOrgs(CurrentUser.Id.Value);
                        if (orgIds.Any())
                            dict.Add(roleLevel, orgIds);
                        break;
                    case RoleLevel.Owner://预算负责人:BudgetOwner~自己作为预算负责人的预算对应数据
                        var subbudgetService = LazyServiceProvider.LazyGetService<ISubbudgetService>();
                        var subbudgetIds = await subbudgetService.GetSubbudgetsByOwner(CurrentUser.Id.Value);
                        if (subbudgetIds.Any())
                            dict.Add(roleLevel, subbudgetIds.Distinct());
                        break;
                    case RoleLevel.Applicant://自己作为申请人的数据:BU不可选，留空
                    case RoleLevel.Unkonw:
                    default:
                        if (CurrentUser.Id.HasValue)
                        {
                            var operators = await GetOriginalOperators(agentOperatorId, bizTypeCategory);
                            if (operators.Any())
                                dict.Add(roleLevel, operators.Select(x => x.Key));
                        }
                        break;
                }
            }

            if (dict.Count != 0 && !dict.ContainsKey(RoleLevel.Applicant) && CurrentUser.Id.HasValue)
            {
                var operators = await GetOriginalOperators(agentOperatorId, bizTypeCategory);
                if (operators.Any())
                    dict.Add(RoleLevel.Applicant, operators.Select(x => x.Key));
            }

            return dict;
        }

        private async Task<IEnumerable<KeyValuePair<Guid, string>>> GetOriginalOperators(Guid? agentOperatorId = null, ResignationTransfer.TaskFormCategory? bizTypeCategory = null)
        {
            var agentService = LazyServiceProvider.LazyGetService<IAgencyService>();
            var originalOperators = await agentService.GetOriginalOperators(new GetAgentOperatorsRequestDto
            {
                AgentOperatorId = agentOperatorId,
                BusinessTypeCategory = bizTypeCategory
            });
            return originalOperators;
        }
        /// <summary>
        /// 根据角色获取部门Id
        /// </summary>
        /// <returns></returns>
        public async Task<KeyValuePair<RoleLevel, IEnumerable<Guid>>> GetDeptByRoleLevelde()
        {
            IEnumerable<Guid> deptIds = [];
            var roleLevel = GetEFlowMyRoleLevel();
            var commonService = LazyServiceProvider.LazyGetService<ICommonService>();
            var queryAuthorizedBudgetBu = await LazyServiceProvider.LazyGetService<IBdAuthorizedBudgetBuReadonlyRepository>().GetQueryableAsync();
            switch (roleLevel)
            {
                case RoleLevel.Admin:
                    break;
                case RoleLevel.Manager:
                    {
                        var buIds = queryAuthorizedBudgetBu.Where(x => x.UserId == CurrentUser.Id).Select(s => s.BuId).ToList();
                        var depts = await commonService.GetChildrenOrgs(buIds);
                        deptIds = depts.Select(s => s.Id).Distinct();

                    }
                    break;
                case RoleLevel.Owner:
                    {
                        var buIds = await commonService.GetOrganizationDeptWithBU(CurrentUser.Id.Value);
                        var depts = await commonService.GetChildrenOrgs(buIds.Select(s => s.Id).ToList());
                        deptIds = depts.Select(s => s.Id).Distinct();

                    }
                    break;
                case RoleLevel.Leader:
                    {
                        var orgIds = (await commonService.GetAllChildrenOrgs(CurrentUser.Id.Value)).ToHashSet();
                        var depts = await commonService.GetChildrenOrgs(orgIds.ToList());
                        deptIds = depts.Select(s => s.Id).Distinct();
                    }
                    break;
                default:
                    break;
            }
            return new KeyValuePair<RoleLevel, IEnumerable<Guid>>(roleLevel, deptIds);
        }
    }
}
