﻿using Abbott.SpeakerPortal;
using Abbott.SpeakerPortal.Contracts.Common.Blob;

using Azure;
using Azure.Identity;
using Azure.Storage;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;

using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.AppServices.Common
{
    /// <summary>
    /// Azure Blob 扩展服务方法
    /// </summary>
    public class BlobService : SpeakerPortalAppService, IBlobService
    {
        readonly BlobOptions _blobOptions;

        public BlobService(IOptions<BlobOptions> options)
        {
            _blobOptions = options.Value;
        }

        BlobContainerClient Build()
        {
            var _blobServiceClient = new BlobServiceClient(new Uri(_blobOptions.Uri), new DefaultAzureCredential());
            //获取容器BlobContainerClient
            var containerClient = _blobServiceClient.GetBlobContainerClient(_blobOptions.ContainerName);
            containerClient.CreateIfNotExists();
            return containerClient;
        }

        BlobContainerClient BuildEncryption()
        {
            var options = new BlobClientOptions
            {
                CustomerProvidedKey = new CustomerProvidedKey(Convert.ToBase64String(Encoding.UTF8.GetBytes(_blobOptions.ProvidedKey)))//aes原始密钥转base64编码
            };

            var _blobServiceClient = new BlobServiceClient(new Uri(_blobOptions.Uri), new DefaultAzureCredential(), options);
            //获取容器BlobContainerClient
            var containerClient = _blobServiceClient.GetBlobContainerClient(_blobOptions.ContainerName);
            containerClient.CreateIfNotExists();
            return containerClient;
        }

        /// <summary>
        /// 下载文件流
        /// </summary>
        /// <param name="path"></param>
        /// <returns></returns>
        public async Task<Response<BlobDownloadStreamingResult>> Download(string path)
        {
            var client = BuildEncryption();
            try
            {
                var blobClient = client.GetBlobClient(path);
                return await blobClient.DownloadStreamingAsync();
            }
            catch (RequestFailedException ex)
            {
                if (ex.Status == (int)HttpStatusCode.Conflict)
                {
                    client = Build();

                    var blobClient = client.GetBlobClient(path);
                    return await blobClient.DownloadStreamingAsync();
                }

                throw;
            }
        }

        /// <summary>
        /// 下载到文件流
        /// </summary>
        /// <param name="path"></param>
        /// <returns></returns>
        public async Task<MemoryStream> DownloadStream(string path)
        {
            var stream = new MemoryStream();
            var client = BuildEncryption();
            try
            {
                var blobClient = client.GetBlobClient(path);
                await blobClient.DownloadToAsync(stream);
                return stream;
            }
            catch (RequestFailedException ex)
            {
                if (ex.Status == (int)HttpStatusCode.Conflict)
                {
                    client = Build();

                    var blobClient = client.GetBlobClient(path);
                    await blobClient.DownloadToAsync(stream);
                    return stream;
                }

                throw;
            }
        }

        ///// <summary>
        ///// 下载到本地
        ///// </summary>
        ///// <param name="path"></param>
        ///// <param name="localPath"></param>
        ///// <returns></returns>
        //public async Task<Response> DownloadToFile(string path, string localPath)
        //{
        //    var client = Build();

        //    var blobClient = client.GetBlobClient(path);
        //    var response = await blobClient.DownloadToAsync(localPath);
        //    return response;
        //}

        /// <summary>
        /// 上传
        /// </summary>
        /// <param name="blobName"></param>
        /// <param name="content"></param>
        /// <param name="overwrite"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<Response<BlobContentInfo>> UploadAsync(string blobName, BinaryData content, bool overwrite = false, CancellationToken cancellationToken = default)
        {
            var client = BuildEncryption();
            var blobClient = client.GetBlobClient(blobName);
            return await blobClient.UploadAsync(content, overwrite, cancellationToken);
        }

        /// <summary>
        /// 上传
        /// </summary>
        /// <param name="blobName"></param>
        /// <param name="content"></param>
        /// <param name="overwrite"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<Response<BlobContentInfo>> UploadAsync(string blobName, Stream content, bool overwrite = false, CancellationToken cancellationToken = default)
        {
            var client = BuildEncryption();
            var blobClient = client.GetBlobClient(blobName);
            return await blobClient.UploadAsync(content, overwrite, cancellationToken);
        }
    }
}