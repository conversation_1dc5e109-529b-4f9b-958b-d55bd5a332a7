﻿using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Integration.Dspot;
using Abbott.SpeakerPortal.Contracts.Vendor.NonSpeaker;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.VendorApplicationFinancials;
using Abbott.SpeakerPortal.Entities.VendorApplicationPersonals;
using Abbott.SpeakerPortal.Entities.VendorApplications;
using Abbott.SpeakerPortal.Entities.VendorPersonals;
using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.Enums.Integration;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Utils;
using Abbott.SpeakerPortal.Extension;
using Abbott.SpeakerPortal.User;
using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Wordprocessing;
using Flurl.Http;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using PdfSharp.Pdf.Content.Objects;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Abbott.SpeakerPortal.Vendor.Speaker;
using DocumentFormat.OpenXml.Drawing.Diagrams;
using Volo.Abp.Domain.Repositories;
using EFCore.BulkExtensions;
using Microsoft.EntityFrameworkCore;

namespace Abbott.SpeakerPortal.AppServices.Integration.Dspot
{
    public class EpdPortalAppService : SpeakerPortalAppService, IEpdPortalAppService
    {
        /// <summary>
        /// The service provider
        /// </summary>
        private readonly IServiceProvider _serviceProvider;

        /// <summary>
        /// The logger
        /// </summary>
        private readonly ILogger<EpdPortalAppService> _logger;

        /// <summary>
        /// The configuration
        /// </summary>
        private readonly IConfiguration _configuration;

        private readonly object _lockObject = new object();

        private List<InteTaskPushStatus> _taskPushStatusList = new List<InteTaskPushStatus>();

        public EpdPortalAppService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _logger = serviceProvider.GetService<ILogger<EpdPortalAppService>>();
            _configuration = serviceProvider.GetService<IConfiguration>();
        }

        public async Task<MessageResult> PushSpeakerInfoStatus(PushSpeakerInfoRequestDto request)
        {
            if (request == null)
            {
                return MessageResult.FailureResult("request is null");
            }

            try
            {
                var url = "";// _configuration["Integrations:DSpot:TwoElementsBatchUrl"];
                var response = await url.WithHeaderJson().PostJsonAsync(request);
                string responseData = await response.GetStringAsync();
                var resObj = JsonSerializer.Deserialize<PushSpeakerInfoResponseDto>(responseData);
                return resObj.Code == 200 ? MessageResult.SuccessResult(resObj.Data) : MessageResult.FailureResult(resObj.Data);
            }
            catch (Exception ex)
            {
                _logger.LogError($"PushSpeakerInfoStatus() Exception: {ex}");
                return MessageResult.FailureResult(ex.Message);
            }
        }

        public async Task<MessageResult> PushDoctorInfo(PushDoctorInfoRequestDto request)
        {
            if (request == null)
            {
                return MessageResult.FailureResult(messageModel: new MessageModelBase(503, "参数错误"));
            }

            try
            {
                var dataverseService = _serviceProvider.GetService<IDataverseService>();
                var vendorApp = new Abbott.SpeakerPortal.Entities.VendorApplications.VendorApplication();
                var vendorAppPer = new Abbott.SpeakerPortal.Entities.VendorApplicationPersonals.VendorApplicationPersonal();
                vendorApp.Status = Enums.Statuses.Saved;
                vendorApp.ApplicationType = Enums.ApplicationTypes.Create;
                vendorApp.VendorType = Enums.VendorTypes.HCPPerson;
                vendorApp.ApplyUserId = await GetStaffIdOrSave(request, dataverseService);
                vendorApp.EpdId = request.EpdHcpCode;
                vendorAppPer.SPName = request.Name;
                await SetHospitalIdOrName(vendorApp, request, dataverseService);
                vendorApp.EpdHospitalId = request.HospitalCode;
                await SetDempartmentName(vendorApp, request, dataverseService);
                vendorApp.HosDepartment = request.HosDepartmentName;
                await SetProfessionalTitleName(vendorApp, request, dataverseService);
                //手机号是密文
                vendorApp.MobileEncrypt = request.Mobile;

                var repoVndApp = LazyServiceProvider.LazyGetService<IVendorApplicationRepository>();
                //插入并且生成编号
                await InsertAndGenerateSerialNoAsync(repoVndApp, vendorApp);

                //供应商申请个人表，关联外键
                vendorAppPer.ApplicationId = vendorApp.Id;
                var repoVndAppPer = LazyServiceProvider.LazyGetService<IVendorApplicationPersonalRepository>();
                var savedVndAppPer = await repoVndAppPer.InsertAsync(vendorAppPer);

                //财务信息里需要初始化加上Trading/jv/yykj三条，币种是RMB
                //走PP去拿公司主数据
                var ppCompanies = await dataverseService.GetCompanyList();
                if (ppCompanies?.Any() == true)
                {
                    var insertVndAppFina = ppCompanies.Where(a =>
                            (
                                new string[3]
                                {
                                    PPCompanyNames.TRADING,
                                    PPCompanyNames.JV,
                                    PPCompanyNames.YYKJ
                                }
                            ).Contains(a.CompanyName)
                        )
                        .Select(a => new VendorApplicationFinancial
                        {
                            ApplicationId = vendorApp.Id,
                            Company = a.CompanyCode,
                            Currency = "RMB",
                        });
                    if (insertVndAppFina?.Any() == true)
                    {
                        var repoVndAppFina = LazyServiceProvider.LazyGetService<IVendorApplicationFinancialRepository>();
                        await repoVndAppFina.InsertManyAsync(insertVndAppFina);
                    }
                }

                return MessageResult.SuccessResult();
            }
            catch (Exception ex)
            {
                _logger.LogError($"PushDoctorInfo() Exception: {ex}");
                //500：服务器错误
                return MessageResult.FailureResult(messageModel: new MessageModelBase(500, "服务器错误: " + ex.Message));
            }
        }

        public async Task<MessageResult> ModifyEpdHcpCode(ModifyEpdHcpCodeRequestDto request)
        {
            if (request == null || string.IsNullOrWhiteSpace(request.EpdHcpCodeOriginal)
                 || string.IsNullOrWhiteSpace(request.EpdHcpCodeNew))
            {
                return MessageResult.FailureResult(messageModel: new MessageModelBase(503, "参数错误"));
            }

            try
            {
                var repoVnd = LazyServiceProvider.LazyGetService<IVendorRepository>();
                var epdIdInArray = new string[] { request.EpdHcpCodeOriginal, request.EpdHcpCodeNew };
                var exsitEntity = await repoVnd.GetListAsync(a => epdIdInArray.Contains(a.EpdId));

                //501：原Code不存在
                var original = exsitEntity.FirstOrDefault(a => request.EpdHcpCodeOriginal == a.EpdId);
                if (original == null)
                {
                    return MessageResult.FailureResult(messageModel: new MessageModelBase(501, "原Code不存在"));
                }

                //502：新Code已存在
                var updateEntity = exsitEntity.FirstOrDefault(a => request.EpdHcpCodeNew == a.EpdId);
                if (updateEntity != null)
                {
                    return MessageResult.FailureResult(messageModel: new MessageModelBase(502, "新Code已存在"));
                }

                original.EpdId = request.EpdHcpCodeNew;
                await repoVnd.UpdateAsync(original);
                return MessageResult.SuccessResult();
            }
            catch (Exception ex)
            {
                _logger.LogError($"ModifyEpdHcpCode() Exception: {ex}");
                //500：服务器错误
                return MessageResult.FailureResult(messageModel: new MessageModelBase(500, "服务器错误"));
            }
        }

        public async Task<MessageResult> ModifyEpdHcpCode(List<ModifyEpdHcpCodeRequestDto> request)
        {
            if (request?.Any() != true)
            {
                return MessageResult.FailureResult(messageModel: new MessageModelBase(503, "参数错误"));
            }

            try
            {
                var repoVnd = LazyServiceProvider.LazyGetService<IVendorRepository>();
                var epdIdOriginal = request.Select(a => a.EpdHcpCodeOriginal);
                var epdIdNew = request.Select(a => a.EpdHcpCodeNew);
                var epdIdInArray = epdIdOriginal.Union(epdIdNew);
                var exsitEntities = await repoVnd.GetListAsync(a => epdIdInArray.Contains(a.EpdId));
                //查出来的Entity的EpdId
                var exsitEpdId = exsitEntities.Select(a => a.EpdId).ToList();

                //501：原Code不存在
                var notExistOriginal = epdIdOriginal.Where(a => !exsitEpdId.Contains(a)).ToList();
                if (notExistOriginal?.Any() == true)
                {
                    return MessageResult.FailureResult(messageModel: new MessageModelBase(501, "原Code不存在"),
                        data: request.Where(a => notExistOriginal.Contains(a.EpdHcpCodeOriginal)));
                }

                //502：新Code已存在
                var existOriginal = epdIdNew.Where(a => exsitEpdId.Contains(a)).ToList();
                if (existOriginal?.Any() == true)
                {
                    return MessageResult.FailureResult(messageModel: new MessageModelBase(502, "新Code已存在"),
                        data: request.Where(a => existOriginal.Contains(a.EpdHcpCodeNew)));
                }

                exsitEntities.ForEach(a =>
                {
                    a.EpdId = request.FirstOrDefault(b => b.EpdHcpCodeOriginal == a.EpdId)?.EpdHcpCodeNew ?? a.EpdId;
                });
                await repoVnd.UpdateManyAsync(exsitEntities);
                return MessageResult.SuccessResult();
            }
            catch (Exception ex)
            {
                _logger.LogError($"ModifyEpdHcpCode() Exception: {ex}");
                //500：服务器错误
                return MessageResult.FailureResult(messageModel: new MessageModelBase(500, "服务器错误"));
            }
        }

        public async Task<MessageResult> ModifyOrNotEpdHcpCode(List<ModifyEpdHcpCodeRequestDto> request)
        {
            if (request?.Any() != true)
            {
                return MessageResult.FailureResult(messageModel: new MessageModelBase(503, "参数错误"));
            }
            _logger.LogInformation($"ModifyOrNotEpdHcpCode() request: {JsonSerializer.Serialize(request)}");
            var listProblemDatas = new List<ModifyEpdHcpCodeRequestDto>();

            try
            {
                var epdIdOriginal = request.Select(a => a.EpdHcpCodeOriginal);
                var epdIdNew = request.Select(a => a.EpdHcpCodeNew);
                var queryVendor = (await LazyServiceProvider.LazyGetService<IVendorReadonlyRepository>().GetQueryableAsync()).AsNoTracking();
                var queryVendorApplication = (await LazyServiceProvider.LazyGetService<IVendorApplicationReadonlyRepository>().GetQueryableAsync()).AsNoTracking();

                //正式档案
                var existVendorForOrginals = queryVendor.Where(a => epdIdOriginal.Contains(a.EpdId)).ToList();
                if (existVendorForOrginals.Any())
                {
                    var existVendorForNews = queryVendor.Where(a => epdIdNew.Contains(a.EpdId)).ToArray();
                    for (int i = 0; i < request.Count; i++)
                    {
                        var r = request[i];
                        //老code对应的数据
                        var original = existVendorForOrginals.FirstOrDefault(a => string.Equals(a.EpdId, r.EpdHcpCodeOriginal, StringComparison.CurrentCultureIgnoreCase));
                        //新code对应的数据
                        var exists = existVendorForNews.FirstOrDefault(a => string.Equals(a.EpdId, r.EpdHcpCodeNew, StringComparison.CurrentCultureIgnoreCase));
                        //判断老code对应的数据不存在或者新code对应的数据存在，则为问题数据
                        if (original == null || exists != null)
                        {
                            listProblemDatas.Add(r);
                            //移除不需要更新的数据
                            if (original != null)
                                existVendorForOrginals.Remove(original);

                            continue;
                        }
                        original.EpdId = r.EpdHcpCodeNew;
                    }

                    if (existVendorForOrginals.Any())
                    {
                        //BulkUpdateAsync不会更新最后更新时间以及审计日志，所以这里换回UpdateManyAsync
                        await LazyServiceProvider.LazyGetService<IVendorRepository>().UpdateManyAsync(existVendorForOrginals);
                        //var dbcontext = await vendorRepository.GetDbContextAsync();
                        //await dbcontext.BulkUpdateAsync(existVendorForOrginals);
                    }
                }

                //申请档案草稿
                var existVendorApplicationForOrginals = queryVendorApplication.Where(a => a.Status== Statuses.Saved && epdIdOriginal.Contains(a.EpdId)).ToList();
                if (existVendorApplicationForOrginals.Any())
                {
                    var existVendorApplicationForNews = queryVendorApplication.Where(a => a.Status == Statuses.Saved && epdIdNew.Contains(a.EpdId)).ToArray();
                    for (int i = 0; i < request.Count; i++)
                    {
                        var r = request[i];
                        //老code对应的数据
                        var original = existVendorApplicationForOrginals.FirstOrDefault(a => string.Equals(a.EpdId, r.EpdHcpCodeOriginal, StringComparison.CurrentCultureIgnoreCase));
                        //新code对应的数据
                        var exists = existVendorApplicationForNews.FirstOrDefault(a => string.Equals(a.EpdId, r.EpdHcpCodeNew, StringComparison.CurrentCultureIgnoreCase));
                        //判断老code对应的数据不存在或者新code对应的数据存在，则为问题数据
                        if (original == null || exists != null)
                        {
                            listProblemDatas.Add(r);
                            //移除不需要更新的数据
                            if (original != null)
                                existVendorApplicationForOrginals.Remove(original);

                            continue;
                        }
                        original.EpdId = r.EpdHcpCodeNew;
                    }

                    if (existVendorApplicationForOrginals.Any())
                    {
                        //BulkUpdateAsync不会更新最后更新时间以及审计日志，所以这里换回UpdateManyAsync
                        await LazyServiceProvider.LazyGetService<IVendorApplicationRepository>().UpdateManyAsync(existVendorApplicationForOrginals);
                        //var dbcontext = await vendorApplicationRepository.GetDbContextAsync();
                        //await dbcontext.BulkUpdateAsync(existVendorApplicationForOrginals);
                    }
                }

                return MessageResult.SuccessResult(listProblemDatas);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"ModifyOrNotEpdHcpCode() Exception: {ex}");
                //500：服务器错误
                return MessageResult.FailureResult(messageModel: new MessageModelBase(500, "服务器错误：" + ex.Message));
            }
        }

        public async Task<MessageResult> DoctorInfoResult(DoctorInfoQueryRequestDto request)
        {
            if (request == null)
            {
                return MessageResult.FailureResult("request is null");
            }
            if (request.QueryStartDate == null && string.IsNullOrEmpty(request.BUHcpCode))
            {
                return MessageResult.FailureResult("params error");
            }
            if (request.QueryStartDate != null)
            {
                if (IsMoreThanOneWeekApart((DateTime)request.QueryStartDate))
                {
                    return MessageResult.FailureResult("查询日期不得早于当前日期的一周");
                }
            }
            try
            {
                //1.查询数据
                //当QueryStartDate条件非空时，Vendor表记录的创建时间/更新时间在查询开始日期之后的，并且EpdID非空，都查询出来。
                //当QueryStartDate条件为空时，根据BUHcpCode条件查询，匹配EpdID字段
                var _vendorPersonalRepository = _serviceProvider.GetService<IVendorPersonalRepository>();
                var _vendorRepository = _serviceProvider.GetService<IVendorRepository>();
                var vendorQuery = await _vendorRepository.GetQueryableAsync();
                var vendorPersonalQuery = await _vendorPersonalRepository.GetQueryableAsync();
                var query = vendorQuery.Join(vendorPersonalQuery, a => a.Id, b => b.VendorId, (a, b) => new { vndPer = b, vndApp = a })
                    .Where(a => !string.IsNullOrEmpty(a.vndApp.EpdId))
                    .WhereIf(request.QueryStartDate == null, a => a.vndApp.EpdId == request.BUHcpCode)
                    .WhereIf(request.QueryStartDate != null, a => a.vndApp.CreationTime > request.QueryStartDate
                                                          || a.vndApp.LastModificationTime > request.QueryStartDate)
                    .Select(r => new
                    {
                        r.vndApp.VendorCode,
                        r.vndApp.EpdId,
                        r.vndPer.Sex,
                        r.vndPer.SPName,
                        r.vndApp.HospitalId,
                        r.vndApp.VendorId,
                        r.vndApp.HosDepartment,
                        r.vndApp.StandardHosDepId,
                        r.vndApp.PTId,
                        r.vndApp.CertificateCode,
                        r.vndApp.AcademicLevel,
                        r.vndApp.IsAcademician,
                        r.vndApp.SPLevel,
                        r.vndApp.AcademicPosition,
                        r.vndApp.Status,
                        r.vndApp.EpdHospitalId,
                        r.vndApp.LastModificationTime
                    });
                var speakers = query.ToList();
                //获取PP 主数据
                var dataverseService = _serviceProvider.GetService<IDataverseService>();

                var ppBUHospitals = await dataverseService.GetAllBUHospitals();
                var ppDepartments = await dataverseService.GetAllDepartments();
                var ppJobTiles = await dataverseService.GetAllJobTiles();
                var ppHospitals = await dataverseService.GetAllHospitals();
                var ppAcademicLevel = await dataverseService.GetDictionariesAsync(DictionaryType.LearningLevel);

                var results = new List<dynamic>();
                foreach (var speaker in speakers)
                {
                    var ppHosObj = ppHospitals.Where(h => h.Id == speaker.HospitalId).FirstOrDefault();
                    var ppBUHosObj = ppBUHospitals.Where(h => h.VeevaID == ppHosObj?.HcoVeevaID).FirstOrDefault();
                    var ppDep = ppDepartments.Where(d => d.Id == speaker.StandardHosDepId).FirstOrDefault();
                    var ppJob = ppJobTiles.Where(j => j.Id == speaker.PTId).FirstOrDefault();
                    var ppAcademic = ppAcademicLevel.Where(a => a.Code == speaker.AcademicLevel).FirstOrDefault();

                    string buHcoId = null;
                    string buHcoName = null;
                    //buHcoId需取vendors.EPDHospitalID，若此ID为空:
                    //根据vendors.hospitalid，查询出PP中[医院主数据]中的医院名称
                    //根据医院名称查询PP中[业务系统医院主数据]中，来源系统=[EPD HCP Portal]的医院名称，完全匹配时带出该表的[医院代码]
                    //若上述查询无法匹配到任何医院，则根据[医院主数据]中的医院VeevaID，查询[业务系统医院主数据]中，来源系统=[EPD HCP Portal]的医院VeevaID，完全匹配时带出该表的[医院代码]
                    //若上述两种查询均无法匹配到任何医院，则留空

                    if (string.IsNullOrEmpty(speaker.EpdHospitalId))
                    {
                        if (ppHosObj != null)
                            buHcoId = ppBUHospitals.FirstOrDefault(a => a.Name == ppHosObj.Name && a.SourceSystem == "EPD HCP Portal")?.HospitalCode;
                        if (string.IsNullOrEmpty(buHcoId))
                            buHcoId = ppBUHospitals.FirstOrDefault(h => h.VeevaID == ppHosObj?.HcoVeevaID && h.SourceSystem == "EPD HCP Portal")?.HospitalCode;
                    }
                    else
                        buHcoId = speaker.EpdHospitalId;

                    //HCOName：原逻辑为查询hospitalid对应的医院名称，需要调整为：
                    //先根据1中查询出的buHCOid，查询PP中[业务系统医院主数据]中，来源系统=[EPD HCP Portal]的[医院代码]，填入匹配出的医院名称
                    //若1中查询出的buHCOid为空，或id在该表中无法匹配到任何数据，才查询hospitalid对应的医院名称
                    if (string.IsNullOrEmpty(buHcoId))
                        buHcoName = ppHosObj?.Name;
                    else
                    {
                        buHcoName = ppBUHospitals.FirstOrDefault(h => h.HospitalCode == buHcoId && h.SourceSystem == "EPD HCP Portal")?.Name;
                        if (string.IsNullOrEmpty(buHcoName))
                            buHcoName = ppHosObj?.Name;
                    }

                    var r = new
                    {
                        nextBpmHcpId = speaker.VendorCode,
                        buHcpCode = speaker.EpdId,
                        name = speaker.SPName,
                        nextBpmHcoId = speaker.HospitalId,
                        nextBpmHcoCode = ppHosObj?.HospitalCode,
                        //epdhospitalid 应该按hospitalid，去PP查veeva编码，然后去查业务系统医院主数据，拿veeva编码找回EPD的id
                        buHcoId = buHcoId, //ppBUHosObj?.HospitalCode,
                        hcoInfo = new
                        {
                            hcoName = buHcoName, //ppHosObj?.Name,
                            hcoGrade = ppHosObj?.Level,
                            province = ppHosObj?.ProvinceName,
                            city = ppHosObj?.CityName,
                        },
                        departmentName = ppDep?.Name,//标准科室名称
                        speciality = ppDep?.Specialty,//专长
                        hosDepartmentName = speaker.HosDepartment,//院内科室
                        professionalTitle = ppJob?.Name,//职称
                        academicTitle = ppAcademic?.Name,//学术职称
                        isAcademician = speaker.IsAcademician,//是否教授
                        speakerLevel = speaker.SPLevel,//讲者评级
                        certificateCode = speaker.CertificateCode,//执业证书编号
                        gender = speaker.Sex.GetDescription() ?? "未知",
                        status = speaker.Status,
                        academicPosition = new List<dynamic>(),
                        updatetime = speaker.LastModificationTime?.ToString("yyyy-MM-dd HH:mm:ss")
                    };
                    // //学协会/期刊社任职,同一讲者可拥有多个学会任职信息，也可能无任何学会任职信息，如为空则推送为空列表
                    if (!string.IsNullOrEmpty(speaker.AcademicPosition))
                    {
                        try
                        {
                            var apList = Newtonsoft.Json.JsonConvert.DeserializeObject<List<AcademicPositionDto>>(speaker.AcademicPosition);
                            foreach (var apObj in apList)
                            {
                                r.academicPosition.Add(new
                                {
                                    orgType = apObj?.JournalTypeName,
                                    orgName = apObj?.JournalName,
                                    orgLevel = apObj?.JournalCategoryName,
                                    orgPosition = apObj?.AppointmentName,
                                    orgPositionStatus = apObj?.AppointmentStatusName
                                });
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError($"DoctorInfoResult()_AcademicPosition_Deserialize,{speaker.AcademicPosition}, Exception: {ex}");

                        }

                    }
                    results.Add(r);
                }


                return MessageResult.SuccessResult(results);
            }
            catch (Exception ex)
            {
                _logger.LogError($"DoctorInfoResult() Exception: {ex}");
                //500：服务器错误
                return MessageResult.FailureResult(messageModel: new MessageModelBase(500, "服务器错误: " + ex.Message));
            }


        }

        private bool IsMoreThanOneWeekApart(DateTime specified)
        {
            TimeSpan diff = specified - DateTime.Now;
            return Math.Abs(diff.Days) > 7;
        }

        private async Task<Guid> GetStaffIdOrSave(PushDoctorInfoRequestDto request, IDataverseService dataverseService)
        {
            //empId:去PP的员工主数据表查员工ID，取ID字段（UserInfo.Id),若没有，PP表和UserInfo表都添加
            //empEmail：不必填
            var ppStaff = await dataverseService.GetStaffs(request.EmpID);

            var staff = ppStaff?.FirstOrDefault(a => a.StaffCode == request.EmpID);
            if (staff != null)
            {
                return staff.Id;
            }

            //插入到PP表
            var staffId = await dataverseService.AddStaffAsync(new AddStaffRequestDto
            {
                StaffNumber = request.EmpID,
                StaffEmail = request.EmpEmail,
            });

            //插入到 AbpUsers 表
            IUserService userService = LazyServiceProvider.LazyGetService<IUserService>();
            await userService.CreateUser(new CreateUserRequestDto { StaffCode = request.EmpID });

            return staffId;
        }

        private async Task SetHospitalIdOrName(Entities.VendorApplications.VendorApplication vendorApp
            , PushDoctorInfoRequestDto request, IDataverseService dataverseService)
        {
            //先将 request.HospitalName 存到主表.HospitalName
            vendorApp.HospitalName = request.HospitalName;
            //走PP找医院主数据表，找ID，保存到主表.HospitalId，如果没找到，就留空
            var ppHospitals = await dataverseService.GetAllHospitals();
            var ppExsit = ppHospitals.FirstOrDefault(a => a.Name.Equals(request.HospitalName, StringComparison.OrdinalIgnoreCase));
            vendorApp.HospitalId = ppExsit != null ? ppExsit.Id : vendorApp.HospitalId;
        }

        private async Task SetDempartmentName(Entities.VendorApplications.VendorApplication vendorApp
            , PushDoctorInfoRequestDto request, IDataverseService dataverseService)
        {
            //先将 request.departmentName 存到主表.StandardHosDepName
            vendorApp.StandardHosDepName = request.DepartmentName;
            //走PP找科室主数据表，找ID，存到主表.StandardHosDepId，如果没找到，就留空
            var ppDept = await dataverseService.GetAllDepartments();
            var ppExsit = ppDept.FirstOrDefault(a => a.Name.Equals(request.DepartmentName, StringComparison.OrdinalIgnoreCase));
            vendorApp.StandardHosDepId = ppExsit != null ? ppExsit.Id : vendorApp.StandardHosDepId;
        }

        private async Task SetProfessionalTitleName(Entities.VendorApplications.VendorApplication vendorApp
            , PushDoctorInfoRequestDto request, IDataverseService dataverseService)
        {
            //先将 request.professionalTitle 存到主表.PTName
            vendorApp.PTName = request.ProfessionalTitle;
            //走PP找职称主数据表，找ID，存到主表.PTId，如果没找到，就留空
            var ppJobTiles = await dataverseService.GetAllJobTiles();
            var ppExsit = ppJobTiles.FirstOrDefault(a => a.Name.Equals(request.ProfessionalTitle, StringComparison.OrdinalIgnoreCase));
            vendorApp.PTId = ppExsit != null ? ppExsit.Id : vendorApp.PTId;
        }
    }
}