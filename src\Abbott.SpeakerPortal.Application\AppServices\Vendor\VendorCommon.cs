﻿using Abbott.SpeakerPortal.Contracts.Vendor.NonSpeaker;
using Abbott.SpeakerPortal.Entities.VendorApplicationFinancials;
using Abbott.SpeakerPortal.Entities.VendorApplications;
using Abbott.SpeakerPortal.Entities.VendorFinancials;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Vendor.Speaker;
using Org.BouncyCastle.Asn1.Cmp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.AppServices.Vendor
{
    public class VendorCommon
    {
        #region 提交前验证
        /// <summary>
        /// 激活类型的申请提交前验证财务信息
        /// </summary>
        /// <param name="vendorApply"></param>
        /// <returns></returns>
        public static bool ValidateBeforeActivation(List<FinancialInformation> vdFinancialQuery)
        {
            FinancialVendorStatus[] targetStatus = [FinancialVendorStatus.ToBeEffective, FinancialVendorStatus.ToBeActivated];
            return vdFinancialQuery.Any(x => targetStatus.Contains(x.FinancialVendorStatus));
        }
        #endregion

        /// <summary>
        /// 检测财务信息是否有变化
        /// </summary>
        /// <param name="financeInfos"></param>
        /// <param name="vendorFinancials"></param>
        /// <returns></returns>
        public bool CheckFinancialChanged(IEnumerable<FinancialInformation> financeInfos, IEnumerable<VendorFinancial> vendorFinancials)
        {
            //财务（公司）信息是否有变动或激活
            //2713【供应商管理】【四个供应商类型】变更时：若存在一行讲者供应商状态为待激活，则判断审批流时需要与extend的场景相同
            var intersectVendorFinancials = financeInfos
                .GroupJoin(vendorFinancials, a => new { a.Company, a.VendorCode }, a => new { a.Company, a.VendorCode }, (a, b) => new { RequestVendorFinancial = a, VendorFinancials = b })
                .SelectMany(a => a.VendorFinancials.DefaultIfEmpty(), (a, b) => new { RequestVendorFinancial = a.RequestVendorFinancial, ExistedVendorFinancial = b })
                .ToArray();
            //有新增或者激活行
            var isCompanyChanged = intersectVendorFinancials.Any(a => a.ExistedVendorFinancial == null) || intersectVendorFinancials.Any(a => a.RequestVendorFinancial.FinancialVendorStatus != a.ExistedVendorFinancial?.FinancialVendorStatus);

            return isCompanyChanged;
        }

        /// <summary>
        /// 检查NonHCP草稿是否已完善
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public static bool CheckBImprovedNonHCP(NonSpeakerApplicationPersonalDto request)
        {
            if (string.IsNullOrEmpty(request.CardType) || string.IsNullOrEmpty(request.CardNo) || request.CardPic == null ||
                !request.Sex.HasValue || string.IsNullOrEmpty(request.HandPhone) || request.ProvinceCity == null ||
                request.ProvinceCity.Length != 2 || string.IsNullOrEmpty(request.PostCode) ||
                string.IsNullOrEmpty(request.Address) || string.IsNullOrEmpty(request.BankCode) || string.IsNullOrEmpty(request.BankCardNo) ||
                request.BankCity == null || request.BankCity.Length != 2)
                return false;
            return true;
        }
        /// <summary>
        /// 检查HCP草稿是否已完善
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public static bool CheckBImprovedHCP(SaveSpeakerRequestDto request)
        {
            if (string.IsNullOrEmpty(request.HandPhone) || request.CardPic == null || string.IsNullOrEmpty(request.CardType) ||
                    !request.Sex.HasValue || string.IsNullOrEmpty(request.CardNo) || request.ProvinceCity == null ||
                    request.ProvinceCity.Length != 2 || string.IsNullOrEmpty(request.PostCode) ||
                    string.IsNullOrEmpty(request.Address) || string.IsNullOrEmpty(request.BankCode) || string.IsNullOrEmpty(request.BankCardNo) ||
                    request.BankCity == null || request.BankCity.Length != 2)
                return false;
            return true;
        }
        /// <summary>
        /// 检查HCI草稿是否已完善
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public static bool CheckBImprovedHCI(NonSpeakerApplicationOrganizationDto request)
        {
            if (string.IsNullOrEmpty(request.VendorName) || request.ProvinceCity == null || request.ProvinceCity.Length != 2 ||
                string.IsNullOrEmpty(request.PostCode) || string.IsNullOrEmpty(request.RegCertificateAddress) || string.IsNullOrEmpty(request.ContactName) || string.IsNullOrEmpty(request.ContactPhone) ||
                string.IsNullOrEmpty(request.ContactEmail) || string.IsNullOrEmpty(request.Website) || !request.RegisterDate.HasValue || string.IsNullOrEmpty(request.OrgType) ||
                !request.RegValidityStart.HasValue || !request.RegValidityEnd.HasValue || string.IsNullOrEmpty(request.ApplyReason) ||
                string.IsNullOrEmpty(request.BankCode) || string.IsNullOrEmpty(request.BankCardNo) || request.BankCity == null || request.BankCity.Length != 2)
                return false;
            return true;
        }
        /// <summary>
        /// 检查NonHCI草稿是否已完善
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public static bool CheckBImprovedNonHCI(NonSpeakerApplicationOrganizationDto request)
        {
            if (string.IsNullOrEmpty(request.VendorName) || string.IsNullOrEmpty(request.VendorEngName) || request.ProvinceCity == null || request.ProvinceCity.Length != 2 ||
                string.IsNullOrEmpty(request.PostCode) || string.IsNullOrEmpty(request.RegCertificateAddress) || string.IsNullOrEmpty(request.ContactName) ||
                string.IsNullOrEmpty(request.ContactPhone) || string.IsNullOrEmpty(request.ContactEmail) || string.IsNullOrEmpty(request.BusinessScope) ||
                string.IsNullOrEmpty(request.LastYearSales) || string.IsNullOrEmpty(request.KeyClient) || string.IsNullOrEmpty(request.Staffs) || string.IsNullOrEmpty(request.ApplyReason) ||
                string.IsNullOrEmpty(request.BankCode) || string.IsNullOrEmpty(request.BankCardNo) || request.BankCity == null || request.BankCity.Length != 2)
                return false;
            return true;
        }
    }
}
