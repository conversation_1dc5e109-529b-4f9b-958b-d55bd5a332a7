﻿using Abbott.SpeakerPortal.Contracts.Budget.Subbudget;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;

namespace Abbott.SpeakerPortal.Contracts.Budget
{
    public interface IBdMasterBudgetService
    {
        /// <summary>
        /// 创建主数据
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task CreateMasterBudget(CreateBudgetRequestDto requestDto);
        /// <summary>
        /// 获取主预算
        /// </summary>
        /// <param name="request"></param>
        /// <param name="IsPage"></param>
        /// <returns></returns>
        Task<PagedResultDto<GetBudgetListResponseDto>> GetMasterBudgetListAsync(GetBudgetListRequestDto request, bool IsPage = true);
        /// <summary>
        /// 删除预算
        /// </summary>
        /// <param name="Ids"></param>
        /// <returns></returns>
        Task<MessageResult> DeleteBdMasterBudgetByIdsAsync(List<Guid> Ids);
        /// <summary>
        /// 冻结预算
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<MessageResult> UpdateBudgetStatusAsync(UpdateBudgetStatusRequestDto requestDto);
        /// <summary>
        /// 调整预算
        /// </summary>
        /// <returns></returns>
        Task<MessageResult> AdjustmentBudgetAmountAsync(AdjustBudgetAmountRequestDto amountRequestDto);
        /// <summary>
        /// 调拨预算
        /// </summary>
        /// <returns></returns>
        Task<MessageResult> TransferBudgetAmountAsync(TransferBudgetAmountRequestDto requestDto);
        /// <summary>
        /// 获取同bu下的Bu信息
        /// </summary>
        /// <param name="BuId"></param>
        /// <returns></returns>
        Task<PagedResultDto<BudgetResponseDto>> GetBudgetListAsync(TransferRequestDto transfer);
        /// <summary>
        /// 根据Id获取预算信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        Task<BudgetAmountResponseDto> GetBudgetAmountAsync(Guid Id);
        /// <summary>
        /// 获取详情记录
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        Task<GetBudgetListResponseDto> GetMasterBudgetRecordByIdAsync(Guid Id);
        /// <summary>
        /// 获取所有主数据
        /// </summary>
        /// <returns></returns>
        Task<IList<AllBudgetResponseDto>> GetAllBudgetListAsync(string Code);
        /// <summary>
        /// 获取用户授权预算Bu
        /// </summary>
        /// <returns></returns>
        Task<List<DepartmentDto>> GetAuthorizedBudgetBuAsync();
        /// <summary>
        /// 解析批量上传excel
        /// </summary>
        /// <returns></returns>
        /// <param name="excelDtos"></param>
        Task<MessageResult> AnalyzeCreateMasterBudgetExcelAsync(IEnumerable<CreateMasterBudgetExcelDto> excelDtos);
        /// <summary>
        /// 批量新增主预算
        /// </summary>
        /// <returns></returns>
        /// <param name="request"></param>
        Task<MessageResult> CreatesMasterBudgetAsync(ImportDataResponseDto<AnalyzeExcelResponseDto> request);
    }
}
