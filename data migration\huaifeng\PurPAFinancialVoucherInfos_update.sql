SELECT
 TRY_CONVERT(UNIQUEIDENTIFIER, [Id]) [Id]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([PAId],'********-0000-0000-0000-************')) [PAId]
,[VendorName]
,[InvoiceReference]
,[InvoiceLineAmount]
,[InvoiceDate]
,[InvoiceDescription]
,[InvoiceReceiptReference]
,[GeneralLedgerDate]
,[ReasonCode]
,[PayType]
,[Bank]
,[InvoiceReceiptDate]
,[CurrencyCode]
,[RecognitionRate]
,[CompanyCode]
,[DivisionCode]
,[CostCenter]
,[NatureAccount]
,[SubAccount]
,[Location]
,[Currency]
,[VendorTaxCode]
,[User]
INTO #PurPAFinancialVoucherInfos
FROM (SELECT *,ROW_NUMBER() OVER(PARTITION BY Id ORDER BY Id) RK FROM PLATFORM_ABBOTT_STG.dbo.PurPAFinancialVoucherInfos)a
WHERE RK = 1
;
--drop table #PurPAFinancialVoucherInfos

USE Speaker_Portal_STG;

UPDATE a 
SET 
 a.[Id] = b.[Id]
,a.[PAId] = b.[PAId]
,a.[VendorName] = b.[VendorName]
,a.[InvoiceReference] = b.[InvoiceReference]
,a.[InvoiceLineAmount] = b.[InvoiceLineAmount]
,a.[InvoiceDate] = b.[InvoiceDate]
,a.[InvoiceDescription] = b.[InvoiceDescription]
,a.[InvoiceReceiptReference] = b.[InvoiceReceiptReference]
,a.[GeneralLedgerDate] = b.[GeneralLedgerDate]
,a.[ReasonCode] = b.[ReasonCode]
,a.[PayType] = b.[PayType]
,a.[Bank] = b.[Bank]
,a.[InvoiceReceiptDate] = b.[InvoiceReceiptDate]
,a.[CurrencyCode] = b.[CurrencyCode]
,a.[RecognitionRate] = b.[RecognitionRate]
,a.[CompanyCode] = b.[CompanyCode]
,a.[DivisionCode] = b.[DivisionCode]
,a.[CostCenter] = b.[CostCenter]
,a.[NatureAccount] = b.[NatureAccount]
,a.[SubAccount] = b.[SubAccount]
,a.[Location] = b.[Location]
,a.[Currency] = b.[Currency]
,a.[VendorTaxCode] = b.[VendorTaxCode]
,a.[User] = b.[User]
FROM dbo.PurPAFinancialVoucherInfos a
left join #PurPAFinancialVoucherInfos  b
ON a.id=b.id;


INSERT INTO dbo.PurPAFinancialVoucherInfos
(
 [Id]
,[PAId]
,[VendorName]
,[InvoiceReference]
,[InvoiceLineAmount]
,[InvoiceDate]
,[InvoiceDescription]
,[InvoiceReceiptReference]
,[GeneralLedgerDate]
,[ReasonCode]
,[PayType]
,[Bank]
,[InvoiceReceiptDate]
,[CurrencyCode]
,[RecognitionRate]
,[CompanyCode]
,[DivisionCode]
,[CostCenter]
,[NatureAccount]
,[SubAccount]
,[Location]
,[Currency]
,[VendorTaxCode]
,[User]
)
SELECT
 [Id]
,[PAId]
,[VendorName]
,[InvoiceReference]
,[InvoiceLineAmount]
,[InvoiceDate]
,[InvoiceDescription]
,[InvoiceReceiptReference]
,[GeneralLedgerDate]
,[ReasonCode]
,[PayType]
,[Bank]
,[InvoiceReceiptDate]
,[CurrencyCode]
,[RecognitionRate]
,[CompanyCode]
,[DivisionCode]
,[CostCenter]
,[NatureAccount]
,[SubAccount]
,[Location]
,[Currency]
,[VendorTaxCode]
,[User]
FROM #PurPAFinancialVoucherInfos a
WHERE not exists (select * from dbo.PurPAFinancialVoucherInfos where id=a.id);


--truncate table dbo.PurPAFinancialVoucherInfos