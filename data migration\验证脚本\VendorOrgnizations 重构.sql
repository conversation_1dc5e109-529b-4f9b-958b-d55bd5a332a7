CREATE PROCEDURE dbo.sp_VendorOrgnizations
AS 
BEGIN
				
--  需要 M1-01 主表过滤 3和4的 供应商
with  vendInfo as
(
	SELECT 
		tt.*
	from 
	(
		SELECT  
			a1.*,
			case when VTYPE in ('NH','NT') THEN VNDAD1+ VNDAD2+ VNDAD3 END AS C_VNDAD
		from
		(
			SELECT a.*,b.VEMLAD,b.VLDRM1,b.VLDRM2,b.VLDATE,b.VLTIME,b.VEXTNM,b.VCRDTE,b.VCTIME ,VCUSER, VMBNKA as BankNo
					from PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a 
					join PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM b 
						on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
					join VENDOR_TMP vt 
					on a.VCMPNY=VT.VCMPNY and a.VENDOR=VT.VENDOR and VendorType in (3,4)   -- ,'NH','NL' 人供应商去掉，加速查询 TODO 检查点
			--		 and TRIM(REPLACE( REPLACE(VMXCRT,' F','') ,' M',''))  in ('140402198012160020','110105196201020439')
		) a1 
	) tt --where tt.rn=1 
)
select 
--	count(1) over(),
	 a2.VMXCRT 	as VMXCRT --辅助字段，用于关联
	,a2.VCMPNY 		as VCMPNY  --辅助字段，用于关联
	,a2.VENDOR 		as VENDOR  --辅助字段，用于关联
	,a2.VTYPE 		as VTYPE     -- 辅助字段,用于后续验证
	,newid() 		as Id
	,vt.id as [VendorId] -- TODO 逻辑检查点
	,a2.VEXTNM as [VendorName]
	,c.supplierOldName as [VendorOldName]
	,c.supplierENName  as [VendorEngName]
	,case when a2.C_VNDAD is not null  then a2.C_VNDAD else c.supplierAddress  end  as [RegCertificateAddress]
	,a2.VPOST as [PostCode]
	,a2.VCON as [ContactName]
	,a2.VPHONE as [ContactPhone]  -- TODO 电话号码是否需要清洗
	,a2.VEMLAD as [ContactEmail],
	case when vt.VendorType is not null then  e.VNDMEMO01 	end  AS WebSite,--机构网站，仅type=3有该值，type=4时填写为空
	case when vt.VendorType='3' then  e.VNDMEMO02  	when vt.VendorType='4'  then '0001-01-01 00:00:00' end  AS RegisterDate,--成立日期，仅type=3有该值，type=4时填写为""0001-01-01 00:00:00""
	case when vt.VendorType='3' then  e.VNDMEMO03 			end  AS OrgType,-- TODO，以字典code填入
	case when vt.VendorType='3' then  e.VNDMEMO04			end  AS IssuingAuthority,--发证机关，仅type=3有该值，type=4时填写为空
	case when vt.VendorType='3' then  e.VNDMEMO05			end  AS RegisterCode,--统一社会信用代码，仅type=3有该值，type=4时填写为空
	case when vt.VendorType='3' then  e.VNDMEMO06  	when vt.VendorType='4' then '0001-01-01 00:00:00'  end  AS RegValidityStart,--登记证书有效期的开始日期，仅type=3有该值，type=4时填写为""0001-01-01 00:00:00""
	case when vt.VendorType='3' then  e.VNDMEMO07  	when vt.VendorType='4' then '0001-01-01 00:00:00'  end  AS RegValidityEnd,--登记证书有效期的结束日期，仅type=3有该值，type=4时填写为""0001-01-01 00:00:00""
	COALESCE( h.supplierprovince ,c.supplierprovince ) 		 	 AS Province, --- TODO  c.supplierprovince 省 为空需要 继续处理	
	COALESCE( h.suppliercity,c.SupplierCity ) 					 AS [City] 
	,case when vt.VendorType='3' then  VNDMEMO09				end  AS Legal,--法定代表人，仅type=3有该值，type=4时填写为空
	case when vt.VendorType='3' then  VNDMEMO10  	when vt.VendorType='4' then '0' end  AS RegisterAmount,--注册资金，仅type=3有该值，type=4时填写为0，需要根据填入的文字转换为数字
	case when vt.VendorType='3' then  VNDMEMO11  			end  AS BusinessAuthority,--业务主管单位，仅type=3有该值，type=4时填写为空
	case 
			when vt.VendorType='3' then  VNDMEMO12
			when vt.VendorType='4' then  c.serviceArea  -- TODO 重点检查 字段
	end  AS BusinessScope,--业务范围(第一行对应type=3，第二行对应type=4)
	case 
			when vt.VendorType='3' then  '0'
			when vt.VendorType='4' then  c.saleAmount   -- TODO 重点检查 字段
	end   				 										as [LastYearSales]
	,case 
			when vt.VendorType='4' then  c.mainIndustry   
	end  													    as [KeyIndustry]
	,case 
			when vt.VendorType='4' then  c.mainCustomer   
	end  													    as [KeyClient]
	,case 
			when vt.VendorType='3' then  '0'
			when vt.VendorType='4' then  c.empNumber  
	end  													    as [Staffs]
	,
case 
	when vt.VendorType='4' then  c.certification   
	end  													    as [Aptitudes],
	c.comEvaluation    										as [ApplyReason]
	,'{}' as [ExtraProperties]
	,cast(null as nvarchar(100)) as [ConcurrencyStamp]
	,vt.CreationTime as [CreationTime]  --- 
	,vt.CreatorId  as [CreatorId]
	,vt.LastModificationTime  as [LastModificationTime]
	,vt.LastModifierId  as [LastModifierId]
	,'0' as [IsDeleted]
	,cast(null as nvarchar(100)) as [DeleterId]
	,cast(null as nvarchar(100)) as [DeletionTime]
	,case 
			when vt.VendorType='3' then  '0'
			when vt.VendorType='4' then  c.shareholder   
	end  													    as [Shareholder]
into #VendorOrgnizations_tmp
from 
	vendInfo a2
left join
(
	SELECT 
		NULL AS supplierprovince,  -- TODO 需要根据 c.SupplierCity  查询省份
  		c.SupplierCity,   
		cast(c.vendorNumber as int) vendorNumber,c.company_Value,c.supplierCNName,c.supplierAddress,
		j.XmlContent.value('(/root/SupplierApplication_supplierInfoBlock_MainStore/supplierOldName)[1]', 'NVARCHAR(50)') supplierOldName,
		j.XmlContent.value('(/root/SupplierApplication_supplierInfoBlock_MainStore/supplierENName)[1]', 'NVARCHAR(50)') supplierENName,
		j.XmlContent.value('(/root/SupplierApplication_supplierInfoBlock_MainStore/serviceArea)[1]', 'NVARCHAR(50)')  serviceArea,
		j.XmlContent.value('(/root/SupplierApplication_supplierInfoBlock_MainStore/saleAmount)[1]', 'NVARCHAR(50)') saleAmount,
		j.XmlContent.value('(/root/SupplierApplication_supplierInfoBlock_MainStore/mainIndustry)[1]', 'NVARCHAR(50)') mainIndustry,
		j.XmlContent.value('(/root/SupplierApplication_supplierInfoBlock_MainStore/mainCustomer)[1]', 'NVARCHAR(50)') mainCustomer,
		j.XmlContent.value('(/root/SupplierApplication_supplierInfoBlock_MainStore/empNumber)[1]', 'NVARCHAR(50)') empNumber,
		j.XmlContent.value('(/root/SupplierApplication_supplierInfoBlock_MainStore/certification)[1]', 'NVARCHAR(50)') certification,
		j.XmlContent.value('(/root/SupplierApplication_supplierInfoBlock_MainStore/comEvaluation)[1]', 'NVARCHAR(50)') comEvaluation,
		j.XmlContent.value('(/root/SupplierApplication_supplierInfoBlock_MainStore/shareholder)[1]', 'NVARCHAR(50)') shareholder,
		row_number() over(partition by c.vendorNumber,c.company_Value order by  c.ProcInstid desc) as rns
	from  PLATFORM_ABBOTT.dbo.ods_AUTO_BIZ_T_SupplierApplication_Info c
	left join
		PLATFORM_ABBOTT.dbo.ods_Form_7a708c9568fb444a884eb5eca658975f r 
		on  c.ProcInstid=r.ProcInstid and c.vendorNumber is not null  and c.vendorNumber<>'' and  (r.processstatus =N'终止(系统)' or r.processstatus ='已完成')
	LEFT JOIN 
	(
		select ProcInstid,XmlContent from PLATFORM_ABBOTT.dbo.ods_T_FORMINSTANCE_GLOBAL 
		where FORM_Id ='663dd63299be45d69dd8f853d0a4b445' or FORM_Id = '7a708c9568fb444a884eb5eca658975f'
	) j
		on  c.ProcInstid=j.ProcInstid and r.processstatus is not null  -- 终止(系统) /完成 下所有的[up_Id]，并按照[up_FileName]包含文字"
	where  c.vendorNumber is not null  and c.vendorNumber<>''
	
) c 	
	on a2.VENDOR=c.vendorNumber and a2.VCMPNY=c.company_Value  and ( a2.VEXTNM =c.supplierCNName or a2.VNDNAM =c.supplierCNName) and c.rns=1

left join
	PLATFORM_ABBOTT.dbo.ods_T_VENDORINFO_NH e
	on a2.VCMPNY =e.CompanyCode and TRIM( a2.VEXTNM) =TRIM( e.VendorName )
	
left join 
(
	select SupplierCode as VENDOR, supplierName as VEXTNM,
		supplierprovince,suppliercity,IdentityCardResidence
	from PLATFORM_ABBOTT.dbo.ods_T_SupplierExtendedInfo
) h 
	on a2.VENDOR =h.VENDOR and (a2.VEXTNM =h.VEXTNM or a2.VNDNAM =h.VEXTNM)
	
left join 
	PLATFORM_ABBOTT.dbo.Vendor_Tmp vt    -- 根据 vendor 表查询 vendor type =3 和 4
	on vt.VENDOR =a2.VENDOR and vt.VCMPNY = a2.VCMPNY  --and  (vt.VendorType='3'  or vt.VendorType='4')
--where  vt.VENDOR is not null  -- 根据要求只需要3 和4 的供应商
;

--落成实体表
 IF OBJECT_ID(N'PLATFORM_ABBOTT.dbo.VendorOrgnizations_tmp', N'U') IS NOT NULL
BEGIN

	update a 
	set  a.VMXCRT                  = b.VMXCRT
        ,a.VCMPNY                  = b.VCMPNY
        ,a.VENDOR                  = b.VENDOR
        ,a.VendorId         = b.VendorId
        ,a.VendorName              = b.VendorName
        ,a.VendorOldName           = b.VendorOldName
        ,a.VendorEngName           = b.VendorEngName
        ,a.RegCertificateAddress   = b.RegCertificateAddress
        ,a.PostCode                = b.PostCode
        ,a.ContactName             = b.ContactName
        ,a.ContactPhone            = b.ContactPhone
        ,a.ContactEmail            = b.ContactEmail
        ,a.WebSite                 = b.WebSite
        ,a.RegisterDate            = b.RegisterDate
        ,a.OrgType                 = b.OrgType
        ,a.IssuingAuthority        = b.IssuingAuthority
        ,a.RegisterCode            = b.RegisterCode
        ,a.RegValidityStart        = b.RegValidityStart
        ,a.RegValidityEnd          = b.RegValidityEnd
        ,a.Province                = b.Province
        ,a.City                    = b.City
        ,a.Legal                   = b.Legal
        ,a.RegisterAmount          = b.RegisterAmount
        ,a.BusinessAuthority       = b.BusinessAuthority
        ,a.BusinessScope           = b.BusinessScope
        ,a.LastYearSales           = b.LastYearSales
        ,a.KeyIndustry             = b.KeyIndustry
        ,a.KeyClient               = b.KeyClient
        ,a.Staffs                  = b.Staffs
        ,a.Aptitudes               = b.Aptitudes
        ,a.ApplyReason             = b.ApplyReason
        ,a.ExtraProperties         = b.ExtraProperties
        ,a.ConcurrencyStamp        = b.ConcurrencyStamp
        ,a.CreationTime            = b.CreationTime
        ,a.CreatorId               = b.CreatorId
        ,a.LastModificationTime    = b.LastModificationTime
        ,a.LastModifierId          = b.LastModifierId
        ,a.IsDeleted               = b.IsDeleted
        ,a.DeleterId               = b.DeleterId
        ,a.DeletionTime            = b.DeletionTime
        ,a.Shareholder             = b.Shareholder
    from PLATFORM_ABBOTT.dbo.VendorOrgnizations_tmp a
    left join #VendorOrgnizations_tmp b on a.VCMPNY = b.VCMPNY and a.VENDOR = b.VENDOR
    
    insert into PLATFORM_ABBOTT.dbo.VendorOrgnizations_tmp
    select a.VMXCRT
           ,a.VCMPNY
           ,a.VENDOR
           ,a.VTYPE
           ,a.Id
           ,a.VendorId
           ,a.VendorName
           ,a.VendorOldName
           ,a.VendorEngName
           ,a.RegCertificateAddress
           ,a.PostCode
           ,a.ContactName
           ,a.ContactPhone
           ,a.ContactEmail
           ,a.WebSite
           ,a.RegisterDate
           ,a.OrgType
           ,a.IssuingAuthority
           ,a.RegisterCode
           ,a.RegValidityStart
           ,a.RegValidityEnd
           ,a.Province
           ,a.City
           ,a.Legal
           ,a.RegisterAmount
           ,a.BusinessAuthority
           ,a.BusinessScope
           ,a.LastYearSales
           ,a.KeyIndustry
           ,a.KeyClient
           ,a.Staffs
           ,a.Aptitudes
           ,a.ApplyReason
           ,a.ExtraProperties
           ,a.ConcurrencyStamp
           ,a.CreationTime
           ,a.CreatorId
           ,a.LastModificationTime
           ,a.LastModifierId
           ,a.IsDeleted
           ,a.DeleterId
           ,a.DeletionTime
           ,a.Shareholder
	from #VendorOrgnizations_tmp a 
	where not exists (select * from PLATFORM_ABBOTT.dbo.VendorOrgnizations_tmp where a.VCMPNY = VCMPNY and a.VENDOR = VENDOR)
	
	PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into PLATFORM_ABBOTT.dbo.VendorOrgnizations_tmp from #VendorOrgnizations_tmp
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END


END
