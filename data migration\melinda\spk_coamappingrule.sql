select  newid()  as spk_NexBPMCode,spk_bucode,spk_costcentercode,spk_expensenaturecode 
into #spk_coamappingrule
from  spk_coamappingrule_Tmp

IF OBJECT_ID(N'dbo.spk_coamappingrule', N'U') IS NOT NULL
BEGIN
	drop table spk_coamappingrule;
	select  *  into dbo.spk_coamappingrule from #spk_coamappingrule
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into dbo.spk_coamappingrule from #spk_coamappingrule
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
