﻿using System;
using System.Collections.Generic;
using System.Text;

using Abbott.SpeakerPortal.Enums;

namespace Abbott.SpeakerPortal.Contracts.Purchase.PurPRApplication
{
    /// <summary>
    /// bpcs和nexbpm的vendor复合对象
    /// </summary>
    public class CompositeVendorDto
    {
        public Guid AvmVendorId { get; set; }

        public Guid NexBpmVendorId { get; set; }

        public string VendorName { get; set; }

        public VendorTypes NexBpmVendorType { get; set; }

        public string EpdId { get; set; }

        public VendorStatus NexBpmStatus { get; set; }
    }
}
