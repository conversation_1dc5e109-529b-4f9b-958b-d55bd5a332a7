﻿using Abbott.SpeakerPortal.Enums;
using System;
using System.Collections.Generic;
using System.Text;
using System.Text.Json.Serialization;

namespace Abbott.SpeakerPortal.Contracts.Budget.Subbudget
{
    public class TransferSubBudgetDto
    {
        /// <summary>
        /// 被调拨预算
        /// </summary>
        public string OriginalCode { get; set; }
        /// <summary>
        /// 调拨预算
        /// </summary>
        public string TransferCode { get; set; }
        /// <summary>
        /// 调拨金额
        /// </summary>
        public string TransferAmountText { get; set; }
        public decimal? TransferAmount { get; set; }
        /// <summary>
        /// 来源月份
        /// </summary>
        public string OriginalMonthText { get; set; }
        public Month? OriginalMonth { get; set; }
        /// <summary>
        /// 调拨月份
        /// </summary>
        public string TransferMonthText { get; set; }
        public Month? TransferMonth { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        [JsonPropertyName("D")]
        public string Remark { get; set; }
        /// <summary>
        /// 验证信息
        /// </summary>
        /// <returns></returns>
        public (string, bool) VarifyInfom()
        {
            string mes = string.Empty;
            if (string.IsNullOrWhiteSpace(OriginalCode)) mes += $"来源子预算编号不能为空;";
            if (string.IsNullOrWhiteSpace(TransferCode)) mes += $"目标子预算编号不能为空;";
            if (!decimal.TryParse(TransferAmountText, out decimal amount)) mes += $"调用金额不是数字;";
            else if (amount < 0) mes += $"调拨金额不能为负数;";
            else
            {

                TransferAmount = Math.Round(amount, 2);
                TransferAmountText = amount.ToString("F2");
            }

            if (string.IsNullOrWhiteSpace(OriginalMonthText)) mes += $"预算月份不能为空;";
            else if (Enum.TryParse<Month>(OriginalMonthText, out var omonth))
                OriginalMonth = omonth;
            else mes += "请输入正确的预算月份";

            if (string.IsNullOrWhiteSpace(TransferMonthText)) mes += $"调整预算月份不能为空;";
            else if (Enum.TryParse<Month>(TransferMonthText, out var tmonth))
                TransferMonth = tmonth;
            else mes += "请输入正确的调整预算月份";
            return (mes, !string.IsNullOrEmpty(mes));
        }
    }
}
