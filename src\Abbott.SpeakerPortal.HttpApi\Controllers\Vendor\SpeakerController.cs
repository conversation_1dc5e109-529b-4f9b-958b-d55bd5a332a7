﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Budget.Subbudget;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.PowerApp;
using Abbott.SpeakerPortal.Contracts.Vendor;
using Abbott.SpeakerPortal.Contracts.Vendor.Speaker;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Extension;
using Abbott.SpeakerPortal.Filters;
using Abbott.SpeakerPortal.OEC.SpeakerLevel;
using Abbott.SpeakerPortal.OEC.SpeakerLimit;
using Abbott.SpeakerPortal.Permissions;
using Abbott.SpeakerPortal.Purchase.PurExemptApplication;
using Abbott.SpeakerPortal.Vendor;
using Abbott.SpeakerPortal.Vendor.Speaker;
using Abbott.SpeakerPortal.Vendor.Speaker.TaskCenter;
using Abbott.SpeakerPortal.WeChat;

using ClosedXML.Excel;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ApplicationModels;
using Microsoft.Extensions.DependencyInjection;

using Volo.Abp.Application.Dtos;

namespace Abbott.SpeakerPortal.Controllers
{
    [ApiExplorerSettings(GroupName = SwaggerGrouping.VENDOR_MANAGE)]
    public class SpeakerController : SpeakerPortalController
    {
        readonly ISpeakerService _speakerService;
        readonly IWeChatService _weChatService;

        public SpeakerController(IServiceProvider serviceProvider)
        {
            _speakerService = serviceProvider.GetService<ISpeakerService>();
            _weChatService = serviceProvider.GetService<IWeChatService>();
        }

        /// <summary>
        /// 修改更换手机号码
        /// 小程序需要访问，无角色
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
		[HttpPost]
        public async Task<IActionResult> UpdateMobile([FromBody] UpdateMobileRequestDto request)
        {
            if (!IsValidPhoneNumber(request.OldMobile) || !IsValidPhoneNumber(request.NewMobile))
            {
                return Ok(MessageResult.FailureResult("手机号格式不正确"));
            }
            if (request.VerifyCode.ToString().Length != 6)
            {
                return Ok(MessageResult.FailureResult("请填写6位验证码"));
            }
            await _speakerService.UpdateMobileAsync(request);
            return Ok(MessageResult.SuccessResult());
        }

        private static bool IsValidPhoneNumber(string mobile)
        {
            string pattern = @"^(13[0-9]|14[5|7]|15[0|1|2|3|4|5|6|7|8|9]|18[0|1|2|3|5|6|7|8|9])\d{8}$";
            Regex regex = new Regex(pattern);
            return regex.IsMatch(mobile);
        }

        /// <summary>
        /// 获取讲者列表(查全部时必须传入讲者名称)
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [Authorize(SpeakerPortalPermissions.Vendor.Speaker)]
        [ProducesDefaultResponseType(typeof(PagedResultDto<SpeakerListResponseDto>))]
        public async Task<IActionResult> GetSpeakerList([FromBody] SpeakerListRequestDto request)
        {
            //集团财务/合规管理员/报税管理员/系统管理员 /业务管理员
            var isSpecialRole = RoleExtension.AGOTB_ROLES.Intersect(CurrentUser.Roles, StringComparer.CurrentCultureIgnoreCase).Any();

            //非特殊角色，查询全部时，至少需要输入一种查询条件
            if (!isSpecialRole)
                if (string.IsNullOrEmpty(request.VendorCode) && string.IsNullOrEmpty(request.SPName) && request.Status == null && request.PTId == null && request.HospitalId == null && request.StandardHosDepId == null && !request.OnlyMine)
                    return Ok(MessageResult.FailureResult("请使用至少一种筛选条件！"));

            var result = await _speakerService.GetSpeakerListAsync(request);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 导出讲者列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [Authorize(SpeakerPortalPermissions.Vendor.SpeakerExport)]
        public async Task<IActionResult> ExportSpeaker([FromBody] SpeakerListRequestDto request)
        {
            var result = await _speakerService.ExportSpeakerListAsync(request);
            return File(result, "application/octet-stream", "Speaker List.xlsx");
        }

        /// <summary>
        /// 获取讲者历史申请记录列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(PagedResultDto<SpeakerListApplicationResponseDto>))]
        [Authorize(SpeakerPortalPermissions.Vendor.SpeakerRead)]
        [Authorize(SpeakerPortalPermissions.Vendor.NoneSpeakerRead)]
        [Authorize(SpeakerPortalPermissions.Vendor.HCIRead)]
        [Authorize(SpeakerPortalPermissions.Vendor.NoneHCIRead)]
        public async Task<IActionResult> GetSpeakerApplicationList([FromBody] SpeakerListApplicationRequestDto request)
        {
            var result = await _speakerService.GetSpeakerApplicationListAsync(request);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 获取讲者详情
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<SpeakerDetailResponseDto>))]
        public async Task<IActionResult> GetSpeakerDetail([FromQuery] Guid? request)
        {
            if (request == Guid.Empty)
                return Ok(MessageResult.FailureResult($"至少需要一个查询条件"));

            var canAccess = await _speakerService.JudgeIfCurrentUserIsVendorAsync(request.Value);
            if (!canAccess)
                return Ok(MessageResult.FailureResult(messageModel: MessageModelBase.HaveNoPermissions));

            var result = await _speakerService.GetSpeakerDetailAsync((Guid)request);
            return Ok(result);
        }

        /// <summary>
        /// 获取讲者详情 - for H5
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<SpeakerDetailResponseDto>))]
        public async Task<IActionResult> GetSpeakerDetailForH5([FromQuery] Guid? request)
        {
            if (request == Guid.Empty)
                return Ok(MessageResult.FailureResult($"至少需要一个查询条件"));

            var canAccess = await _speakerService.JudgeIfCurrentUserIsVendorAsync(request.Value);
            if (!canAccess)
                return Ok(MessageResult.FailureResult(messageModel: MessageModelBase.HaveNoPermissions));

            var result = await _speakerService.GetSpeakerDetailAsync((Guid)request, true);
            return Ok(result);
        }

        /// <summary>
        /// 获取讲者草稿列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [Authorize(SpeakerPortalPermissions.Vendor.Speaker)]
        [ProducesDefaultResponseType(typeof(MessageResult<PagedResultDto<SpeakerDraftListResponseDto>>))]
        public async Task<IActionResult> GetSpeakerDraftList([FromBody] SpeakerDraftListRequestDto request)
        {
            if (!ModelState.IsValid)
            {
                var errorFields = ModelState.Where(x => x.Value.Errors.Any()).Select(x => new { Field = x.Key, Errors = x.Value.Errors.Select(e => e.ErrorMessage).ToList() }).ToList();
                return Ok(MessageResult.FailureResult($"请填写必填项:{string.Join(",", errorFields.Select(s => s.Field))}"));
            }
            var result = await _speakerService.GetSpeakerDraftListAsync(request);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 讲者草稿删除
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        [Authorize(SpeakerPortalPermissions.Vendor.SpeakerCRUD)]
        [Authorize(SpeakerPortalPermissions.Vendor.NoneSpeakerCRUD)]
        [Authorize(SpeakerPortalPermissions.Vendor.HCICRUD)]
        [Authorize(SpeakerPortalPermissions.Vendor.NoneHCICRUD)]
        public async Task<IActionResult> DeleteSpeakerDraft([FromBody] DeleteSpeakerDraftRequestDto request)
        {
            //验证数据
            if (!ModelState.IsValid)
                return Ok(MessageResult.FailureResult("请填写必填项"));
            var result = await _speakerService.DeleteSpeakerDraftAsync(request);
            return Ok(result);
        }


        /// <summary>
        /// 讲者草稿新增
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [Authorize(SpeakerPortalPermissions.Vendor.SpeakerCRUD)]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        public async Task<IActionResult> CreateSpeaker([FromBody] CreateSpeakerRequestDto request)
        {
            //验证数据
            if (!ModelState.IsValid)
            {
                var errorFields = ModelState.Where(x => x.Value.Errors.Any()).Select(x => new { Field = x.Key, Errors = x.Value.Errors.Select(e => e.ErrorMessage).ToList() }).ToList();
                return Ok(MessageResult.FailureResult($"请填写必填项:{string.Join(",", errorFields.Select(s => s.Field))}"));
            }
            if (request.ID.HasValue)
                return Ok(MessageResult.FailureResult($"请调用修改接口"));
            if (request.FinancialInformation.Count > 0)
            {
                if (request.FinancialInformation.Select(s => s.Currency).Distinct().Count() != 1)
                    return Ok(MessageResult.FailureResult($"财务表中货币类型需一致"));
            }
            var result = await _speakerService.CreateSpeakerAsync(request);
            return Ok(result);
        }

        /// <summary>
        /// 讲者草稿修改
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        public async Task<IActionResult> UpdateSpeaker([FromBody] SaveSpeakerRequestDto request)
        {
            if (!request.ID.HasValue)
                return Ok(MessageResult.FailureResult($"修改时Id不能为空"));

            var canAccess = await _speakerService.JudgeIfCurrentUserIsVendorAsync(request.ID.Value);
            if (!canAccess)
                return Ok(MessageResult.FailureResult(messageModel: MessageModelBase.HaveNoPermissions));

            //验证数据
            if (!ModelState.IsValid)
            {
                var errorFields = ModelState.Where(x => x.Value.Errors.Any()).Select(x => new { Field = x.Key, Errors = x.Value.Errors.Select(e => e.ErrorMessage).ToList() }).ToList();
                return Ok(MessageResult.FailureResult($"请填写基本信息必填项:{string.Join(",", errorFields.Select(s => s.Field))}"));
            }
            //未授权讲者自填信息需要验证，或授权后销售保存需要验证
            if (request.BAuth || request.IsMiniProgram)
            {
                if (string.IsNullOrEmpty(request.HandPhone) || request.CardPic == null || string.IsNullOrEmpty(request.CardType) ||
                    !request.Sex.HasValue || string.IsNullOrEmpty(request.CardNo) || request.ProvinceCity == null ||
                    request.ProvinceCity.Length != 2 || string.IsNullOrEmpty(request.PostCode) ||
                    string.IsNullOrEmpty(request.Address) || string.IsNullOrEmpty(request.BankCode) || string.IsNullOrEmpty(request.BankCardNo) ||
                    request.BankCity == null || request.BankCity.Length != 2)
                {
                    return Ok(MessageResult.FailureResult($"请完善所有信息"));
                }
            }

            if (string.IsNullOrEmpty(request.VendorCode))
                return Ok(MessageResult.FailureResult($"修改时VendorCode不能为空"));
            if (request.FinancialInformation == null || request.FinancialInformation.Count == 0)
                return Ok(MessageResult.FailureResult($"财务表必须添加数据"));
            if (request.FinancialInformation.Count > 0)
            {
                if (request.FinancialInformation.Select(s => s.Currency).Distinct().Count() != 1)
                    return Ok(MessageResult.FailureResult($"财务表中货币类型需一致"));
            }
            var result = await _speakerService.SaveSpeakerAsync(request);
            return Ok(result);
        }

        /// <summary>
        /// DPS Check审批节点允许审批人识别证件后直接替换填写的身份证信息，不需要退回申请人处重新填写
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        public async Task<IActionResult> DPSCheckApprove([FromBody] DPSCheckApproveRequestDto request)
        {
            if (request.VendorType != VendorTypes.HCPPerson && request.VendorType != VendorTypes.NonHCPPerson)
                return Ok(MessageResult.FailureResult("供应商类型错误"));

            if (request.ProvinceCity.Length != 2) return Ok(MessageResult.FailureResult("请输入完整省份和城市"));

            var response = await _speakerService.DPSCheckApproveAsync(request);
            return Ok(response);
        }

        /// <summary>
        /// 讲者授权给销售
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        public async Task<IActionResult> AuthorizedSellers([FromBody] AuthorizedSellerRequestDto request)
        {
            //验证数据
            if (!ModelState.IsValid)
            {
                var errorFields = ModelState.Where(x => x.Value.Errors.Any()).Select(x => new { Field = x.Key, Errors = x.Value.Errors.Select(e => e.ErrorMessage).ToList() }).ToList();
                return Ok(MessageResult.FailureResult($"请填写必填项:{string.Join(",", errorFields.Select(s => s.Field))}"));
            }
            var result = await _speakerService.AuthorizedSellersAsync(request);
            return Ok(result);
        }

        /// <summary>
        /// 讲者草稿克隆
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [Authorize(SpeakerPortalPermissions.Vendor.SpeakerCRUD)]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        public async Task<IActionResult> CloneSpeaker([FromBody] SaveSpeakerRequestDto request)
        {
            //验证数据
            if (!ModelState.IsValid)
            {
                var errorFields = ModelState.Where(x => x.Value.Errors.Any()).Select(x => new { Field = x.Key, Errors = x.Value.Errors.Select(e => e.ErrorMessage).ToList() }).ToList();
                return Ok(MessageResult.FailureResult($"请填写必填项:{string.Join(",", errorFields.Select(s => s.Field))}"));
            }
            //if (request.ID.HasValue)
            //    return Ok(MessageResult.FailureResult($"克隆时Id必须为空"));
            if (string.IsNullOrEmpty(request.VendorCode))
                return Ok(MessageResult.FailureResult($"克隆时VendorCode不能为空"));
            if (request.FinancialInformation == null || request.FinancialInformation.Count == 0)
                return Ok(MessageResult.FailureResult($"财务表必须添加数据"));
            if (request.FinancialInformation.Count > 0)
            {
                if (request.FinancialInformation.Select(s => s.Currency).Distinct().Count() != 1)
                    return Ok(MessageResult.FailureResult($"财务表中货币类型需一致"));
            }
            var result = await _speakerService.CloneSpeakerAsync(request);
            return Ok(result);
        }

        /// <summary>
        /// 验证讲者
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet]
        [Authorize(SpeakerPortalPermissions.Vendor.SpeakerCRUD)]
        [ProducesDefaultResponseType(typeof(MessageResult<List<ValidationSpeakerResponseDto>>))]
        public async Task<IActionResult> ValidationSpeakerData([FromQuery] ValidationSpeakerRequestDto request)
        {
            if (!ModelState.IsValid)
            {
                var errorFields = ModelState.Where(x => x.Value.Errors.Any()).Select(x => new { Field = x.Key, Errors = x.Value.Errors.Select(e => e.ErrorMessage).ToList() }).ToList();
                return Ok(MessageResult.FailureResult($"请填写必填项:{string.Join(",", errorFields.Select(s => s.Field))}"));
            }
            if (string.IsNullOrWhiteSpace(request.SPName) && string.IsNullOrWhiteSpace(request.CertificateCode))
            {
                return Ok(MessageResult.FailureResult($"请填写讲者名称或者证书编号"));
            }
            var result = await _speakerService.ValidationSpeakerDataAsync(request);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 获取讲者草稿详情
        /// </summary>
        /// <param name="request">PC端查询使用</param>
        /// <param name="ApplicationCode">小程序端查询使用</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<SpeakerDraftDetailResponseDto>))]
        public async Task<IActionResult> GetSpeakerDraftDetail([FromQuery] Guid? request)
        {
            if (!request.HasValue) return Ok(MessageResult.FailureResult("Guid或Code必须传一个"));

            var canAccess = await _speakerService.JudgeIfCurrentUserIsVendorAsync(request.Value);
            if (!canAccess)
                return Ok(MessageResult.FailureResult(messageModel: MessageModelBase.HaveNoPermissions));

            var result = await _speakerService.GetSpeakerDraftDetailAsync(request);
            return Ok(result);
        }

        /// <summary>
        /// 获取[None]Hcp详情（小程序扫码专用）
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        [ProducesDefaultResponseType(typeof(MessageResult<GetSpeakerDetailByScanQRResponseDto>))]
        public async Task<IActionResult> GetSpeakerDetailByScanQR([FromBody] GetSpeakerDetailByScanQRRequestDto request)
        {
            var result = await _speakerService.GetSpeakerDetailByScanQRAsync(request);
            return Ok(result);
        }

        /// <summary>
        /// 讲者草稿提交
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [Authorize(SpeakerPortalPermissions.Vendor.SpeakerCRUD)]
        [Authorize(SpeakerPortalPermissions.TaskCenter.LaunchAction)]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        public async Task<IActionResult> SubmitSpeaker([FromBody] SaveSpeakerRequestDto request)
        {
            //验证数据
            if (!ModelState.IsValid)
            {
                var errorFields = ModelState.Where(x => x.Value.Errors.Any()).Select(x => new { Field = x.Key, Errors = x.Value.Errors.Select(e => e.ErrorMessage).ToList() }).ToList();
                return Ok(MessageResult.FailureResult($"请填写必填项:{string.Join(",", errorFields.Select(s => s.Field))}"));
            }

            ////未授权讲者自填信息需要验证，或授权后销售保存需要验证
            //if (request.BAuth || request.IsMiniProgram)
            //{
            if (string.IsNullOrEmpty(request.HandPhone) || request.CardPic == null || string.IsNullOrEmpty(request.CardType) ||
                !request.Sex.HasValue || string.IsNullOrEmpty(request.CardNo) || request.ProvinceCity == null ||
                request.ProvinceCity.Length != 2 || string.IsNullOrEmpty(request.PostCode) ||
                string.IsNullOrEmpty(request.Address) || string.IsNullOrEmpty(request.BankCode) || string.IsNullOrEmpty(request.BankCardNo) ||
                request.BankCity == null || request.BankCity.Length != 2 || Array.Exists(request.ProvinceCity, string.IsNullOrEmpty) ||
                Array.Exists(request.BankCity, string.IsNullOrEmpty))
            {
                return Ok(MessageResult.FailureResult($"请完善所有信息"));
            }
            //}
            if (string.IsNullOrEmpty(request.VendorCode))
                return Ok(MessageResult.FailureResult($"提交时VendorCode不能为空"));
            if (request.FinancialInformation.Count > 0)
            {
                if (request.FinancialInformation.Select(s => s.Currency).Distinct().Count() != 1)
                    return Ok(MessageResult.FailureResult($"财务表中货币类型需一致"));
            }
            var result = await _speakerService.SubmitSpeakerAsync(request);
            return Ok(result);
        }

        /// <summary>
        /// 生成二维码
        /// </summary>
        /// <param name="request"></param>
        /// <param name="isSpeaker"></param>
        /// <param name="isChangePhone"></param>
        /// <returns></returns>
        [HttpGet]
        [Authorize(SpeakerPortalPermissions.Vendor.SpeakerCRUD)]
        public async Task<IActionResult> GetQRPicture([FromQuery] string request, bool isSpeaker, bool isChangePhone)
        {
            var result = await _weChatService.GetQRPictureAsync(request, isSpeaker, isChangePhone);
            return Ok(result);
        }

        /// <summary>
        /// 获取任务中心我发起的供应商列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [Authorize(SpeakerPortalPermissions.TaskCenter.LaunchSpeakerCreate)]
        [Authorize(SpeakerPortalPermissions.TaskCenter.LaunchSpeakerChange)]
        [Authorize(SpeakerPortalPermissions.TaskCenter.LaunchSpeakerActivate)]
        [Authorize(SpeakerPortalPermissions.TaskCenter.LaunchVendorCreate)]
        [Authorize(SpeakerPortalPermissions.TaskCenter.LaunchVendorChange)]
        [Authorize(SpeakerPortalPermissions.TaskCenter.LaunchVendorActivate)]
        [ProducesDefaultResponseType(typeof(PagedResultDto<SpeakerTaskCenterListResponseDto>))]
        public async Task<IActionResult> GetTaskCenterInitiatedSpeakerList([FromBody] SpeakerTaskCenterListRequestDto request)
        {
            var result = await _speakerService.GetInitiatedSpeakerListAsync(request);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 获取任务中心我审批的供应商列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [Authorize(SpeakerPortalPermissions.TaskCenter.ApproveSpeakerCreate)]
        [Authorize(SpeakerPortalPermissions.TaskCenter.ApproveSpeakerChange)]
        [Authorize(SpeakerPortalPermissions.TaskCenter.ApproveSpeakerActivate)]
        [Authorize(SpeakerPortalPermissions.TaskCenter.ApproveVendorCreate)]
        [Authorize(SpeakerPortalPermissions.TaskCenter.ApproveVendorChange)]
        [Authorize(SpeakerPortalPermissions.TaskCenter.ApproveVendorActivate)]
        [ProducesDefaultResponseType(typeof(PagedResultDto<SpeakerTaskCenterListResponseDto>))]
        public async Task<IActionResult> GetTaskCenterApprovalSpeakerList([FromBody] SpeakerTaskCenterListRequestDto request)
        {
            if (request.ProcessingStatus == ProcessingStatus.Progressing)
                return Ok(MessageResult.FailureResult("我审批的无法查询进行中类型"));
            var result = await _speakerService.GetApprovalSpeakerListAsync(request);
            return Ok(MessageResult.SuccessResult(result));
        }

        /// <summary>
        /// 审批时财务信息编辑
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [Authorize(SpeakerPortalPermissions.TaskCenter.ApproveAction)]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        public async Task<IActionResult> SaveSpeakerFinancial([FromBody] SaveSpeakerFinancialRequestDto request)
        {
            var result = await LazyServiceProvider.LazyGetService<IApproveService>().JudgeIfHasPendingTask(request.ID, CurrentUser.Id ?? Guid.Empty);
            if (!result.Success)
                return Ok(MessageResult.FailureResult(messageModel: MessageModelBase.HaveNoPermissions));

            result = await _speakerService.SaveSpeakerFinancialAsync(request);
            return Ok(result);
        }

        // <summary>
        /// 审批时DPS信息编辑
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [Authorize(SpeakerPortalPermissions.TaskCenter.ApproveAction)]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        public async Task<IActionResult> SaveSpeakerDPS([FromBody] SaveSpeakerDPSRequestDto request)
        {
            var result = await LazyServiceProvider.LazyGetService<IApproveService>().JudgeIfHasPendingTask(request.ID, CurrentUser.Id ?? Guid.Empty);
            if (!result.Success)
                return Ok(MessageResult.FailureResult(messageModel: MessageModelBase.HaveNoPermissions));

            result = await _speakerService.SaveSpeakerDPSAsync(request);
            return Ok(result);
        }

        /// <summary>
        /// 审批时变更和激活的详情
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        [Authorize(SpeakerPortalPermissions.TaskCenter.LaunchSpeakerChange)]
        [Authorize(SpeakerPortalPermissions.TaskCenter.LaunchSpeakerActivate)]
        [Authorize(SpeakerPortalPermissions.TaskCenter.ApproveSpeakerChange)]
        [Authorize(SpeakerPortalPermissions.TaskCenter.ApproveSpeakerActivate)]
        public async Task<IActionResult> GetSpeakerChangedDetail([FromQuery] Guid request)
        {
            var result = await _speakerService.GetSpeakerChangedDetailAsync(request);
            return Ok(result);
        }

        /// <summary>
        /// 审批时验证财务信息中的供应商编码
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [Authorize(SpeakerPortalPermissions.TaskCenter.ApproveSpeakerCreate)]
        [Authorize(SpeakerPortalPermissions.TaskCenter.ApproveSpeakerChange)]
        [Authorize(SpeakerPortalPermissions.TaskCenter.ApproveSpeakerActivate)]
        [Authorize(SpeakerPortalPermissions.TaskCenter.ApproveVendorCreate)]
        [Authorize(SpeakerPortalPermissions.TaskCenter.ApproveVendorChange)]
        [Authorize(SpeakerPortalPermissions.TaskCenter.ApproveVendorActivate)]
        [ProducesDefaultResponseType(typeof(MessageResult<List<VonderCodeValidateListResponseDto>>))]
        public async Task<IActionResult> GetVonderCodeValidate([FromBody] FinancialInformation request)
        {
            var result = await _speakerService.GetVonderCodeValidateListAsync(request);
            return Ok(result);
        }

        /// <summary>
        /// 财务信息数据获取
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [Authorize(SpeakerPortalPermissions.TaskCenter.ApproveSpeakerCreate)]
        [Authorize(SpeakerPortalPermissions.TaskCenter.ApproveSpeakerChange)]
        [Authorize(SpeakerPortalPermissions.TaskCenter.ApproveSpeakerActivate)]
        [Authorize(SpeakerPortalPermissions.TaskCenter.ApproveVendorCreate)]
        [Authorize(SpeakerPortalPermissions.TaskCenter.ApproveVendorChange)]
        [Authorize(SpeakerPortalPermissions.TaskCenter.ApproveVendorActivate)]
        [ProducesDefaultResponseType(typeof(MessageResult<SpeakerVonderCodeValidateResponseDto>))]
        public async Task<IActionResult> GetFinancialInformation([FromBody] SpeakerVonderCodeValidateRequestDto request)
        {
            var result = await _speakerService.GetFinancialInformationListAsync(request);
            return Ok(result);
        }

        /// <summary>
        /// 供应商查询
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        [Authorize(SpeakerPortalPermissions.Vendor.Query)]
        [ProducesDefaultResponseType(typeof(PagedResultDto<SupplierListResponseDto>))]
        public async Task<IActionResult> GetSupplierList([FromBody] SupplierListRequestDto request)
        {
            var result = await _speakerService.GetSupplierListAsync(request);
            return Ok(MessageResult.SuccessResult(result));
        }


        /// <summary>
        /// 供应商删除文档数据验证
        /// </summary>
        /// <param name="formFile"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult<ImportDataResponseDto<VendorDeleteImportResponseDto>>))]
        [Authorize(SpeakerPortalPermissions.Vendor.QueryDelete)]
        public async Task<IActionResult> VarifyVendorDeleteAsync(IFormFile formFile)
        {
            try
            {
                var str = Path.GetExtension(formFile.FileName);
                string[] strings = [".xlsx", ".xls"];
                if (!strings.Contains(str))
                    return Ok(MessageResult.FailureResult("请上传excel文件"));

                using MemoryStream memoryStream = new();
                formFile.CopyTo(memoryStream);
                memoryStream.Position = 0;
                using XLWorkbook xLWorkbook = new XLWorkbook(memoryStream);
                IXLWorksheet ws = xLWorkbook.Worksheets.First();
                var rows = ws.Rows().Skip(6).ToArray();
                var rowCount = rows.Length;
                List<VendorDeleteTempleteDto> fileDatas = [];

                for (int i = 0; i < rowCount; i++)
                {
                    VendorDeleteTempleteDto dto = new()
                    {
                        ComCode = rows[i].Cell(1).GetString(),
                        VendorCode = rows[i].Cell(2).GetString()
                    };
                    if (HasNonNullProprerties(dto))
                        fileDatas.Add(dto);
                };

                //var datas = creates.Where(m => HasNonNullProprerties(m));
                if (fileDatas.Count == 0)
                    return Ok(MessageResult.FailureResult("导入内容不能为空!"));

                var response = await _speakerService.VarifyVendorDelete(fileDatas);

                return Ok(response);
            }
            catch (Exception e)
            {
                return Ok(MessageResult.FailureResult(e.Message));
            }
        }

        private bool HasNonNullProprerties<T>(T obj)
        {
            var propertis = obj.GetType().GetProperties();
            foreach (var property in propertis)
            {
                var value = property.GetValue(obj) as string;
                if (!string.IsNullOrWhiteSpace(value)) return true;
            }
            return false;
        }

        /// <summary>
        /// 提交供应商删除数据
        /// </summary>
        /// <param name="messageDtos"></param>
        /// <returns></returns>
        [HttpPost]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        [Authorize(SpeakerPortalPermissions.Vendor.QueryDelete)]
        public async Task<IActionResult> SubmitImportVendorDelete([FromBody] ImportDataResponseDto<VendorDeleteImportResponseDto> messageDtos)
        {
            if (!messageDtos.IsSuccess) return Ok(MessageResult.FailureResult("验证失败!请修改后重新提交"));
            var response = await _speakerService.SubmitImportVendorDelete([.. messageDtos.Datas]);
            return Ok(response);
        }

        /// <summary>
        /// 导出DPS Check信息
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Authorize(SpeakerPortalPermissions.Vendor.QueryExport)]
        public async Task<IActionResult> ExportDPSCheckInfos()
        {
            var response = await _speakerService.ExportDPSCheckInfos();
            return File(response, "application/octet-stream", $"Export DPS Check Info {DateTime.Now:yyyyMMdd-HHmmss}.xlsx");
        }

        /// <summary>
        /// 查询EPD医生数据
        /// </summary>
        /// <param name="epdDoctorQuery"></param>
        /// <returns></returns>
        [HttpPost]
        [Authorize(SpeakerPortalPermissions.Vendor.SpeakerCRUD)]
        [ProducesDefaultResponseType(typeof(MessageResult<PagedResultDto<EPDDoctorResponseDto>>))]
        public async Task<IActionResult> QueryEpdDoctorsAsync([FromBody] EPDDoctorQueryDto epdDoctorQuery)
        {
            var response = await _speakerService.QueryEpdDoctorsAsync(epdDoctorQuery);
            return Ok(response);
        }

        /// <summary>
        /// EPD关联讲者
        /// </summary>
        /// <param name="epdRelationSpeaker"></param>
        /// <returns></returns>
        [HttpPost]
        [Authorize(SpeakerPortalPermissions.Vendor.SpeakerCRUD)]
        [ProducesDefaultResponseType(typeof(MessageResult))]
        public async Task<IActionResult> EpdRelationSpeakerAsync([FromBody] EpdRelationSpeakerDto epdRelationSpeaker)
        {
            var response = await _speakerService.EpdRelationSpeakerAsync(epdRelationSpeaker);
            return Ok(response);
        }


        /// <summary>
        /// 根据EPD医院名和Code获取医院主数据
        /// </summary>
        /// <param name="name"></param>
        /// <param name="code"></param>
        /// <returns></returns>
        [HttpGet]
        [ProducesDefaultResponseType(typeof(MessageResult<DropdownListDto<Guid, string>>))]
        public async Task<IActionResult> GetHospitalByEpdHospitalCode(string name, string code)
        {
            if (string.IsNullOrEmpty(name) || string.IsNullOrEmpty(code))
                return Ok(MessageResult.FailureResult(message: "参数错误"));
            var dto = await _speakerService.GetHospitalByEpdHospitalCode(name, code);
            if (dto == null)
                return Ok(MessageResult.FailureResult(message: "Not found hospital"));

            return Ok(MessageResult.SuccessResult(new DropdownListDto<Guid, string>(dto.Id, dto.Name, dto.Status.ToString())));
        }
    }
}
