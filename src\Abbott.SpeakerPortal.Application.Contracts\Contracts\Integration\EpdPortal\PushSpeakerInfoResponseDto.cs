﻿using System.Text.Json.Serialization;

namespace Abbott.SpeakerPortal.Contracts.Integration.Dspot
{
    public class PushSpeakerInfoResponseDto
    {
        [JsonPropertyName("requestId")]
        public string RequestId { get; set; }

        [JsonPropertyName("timestamp")]
        public int? Timestamp { get; set; }

        [JsonPropertyName("success")]
        public bool? Success { get; set; }

        [JsonPropertyName("code")]
        public int? Code { get; set; }

        [JsonPropertyName("message")]
        public string Message { get; set; }

        [JsonPropertyName("data")]
        public object Data { get; set; }

        [JsonPropertyName("errorList")]
        public object[] ErrorList { get; set; }
    }
}