using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Common.Blob;
using Abbott.SpeakerPortal.Contracts.Common.Cognitive;
using Abbott.SpeakerPortal.Contracts.System.Login;
using Abbott.SpeakerPortal.Converters;
using Abbott.SpeakerPortal.Data;
using Abbott.SpeakerPortal.EntityFrameworkCore;
using Abbott.SpeakerPortal.Filters;
using Abbott.SpeakerPortal.Login;
using Abbott.SpeakerPortal.MultiTenancy;
using Abbott.SpeakerPortal.Redis;
using Abbott.SpeakerPortal.TokenExtensionGrants;
using Abbott.SpeakerPortal.Toolkit.Swagger;
using Abbott.SpeakerPortal.Utils;

using Azure.Identity;
using Azure.Security.KeyVault.Secrets;

using DocumentFormat.OpenXml.Bibliography;

using Hangfire;
using Hangfire.Dashboard;
using Hangfire.MemoryStorage;

using Medallion.Threading;
using Medallion.Threading.Redis;

using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Logging;
using Microsoft.OpenApi.Models;
using Microsoft.PowerPlatform.Dataverse.Client;
using Microsoft.Rest.Serialization;

using Newtonsoft.Json.Converters;

using OpenIddict.Validation.AspNetCore;

using Senparc.CO2NET;
using Senparc.CO2NET.RegisterServices;
using Senparc.Weixin;
using Senparc.Weixin.Entities;
using Senparc.Weixin.RegisterServices;

using Serilog;
using Serilog.Sinks.MSSqlServer;

using Volo.Abp;
using Volo.Abp.Account;
using Volo.Abp.Account.Web;
using Volo.Abp.AspNetCore.MultiTenancy;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Serilog;
using Volo.Abp.Auditing;
using Volo.Abp.Autofac;
using Volo.Abp.DistributedLocking;
using Volo.Abp.Json;
using Volo.Abp.Json.SystemTextJson.JsonConverters;
using Volo.Abp.Localization;
using Volo.Abp.Modularity;
using Volo.Abp.OpenIddict;
using Volo.Abp.OpenIddict.ExtensionGrantTypes;
using Volo.Abp.Swashbuckle;
using Volo.Abp.UI.Navigation.Urls;

namespace Abbott.SpeakerPortal;

[DependsOn(
    typeof(AbpDistributedLockingModule),
    typeof(SpeakerPortalHttpApiModule),
    typeof(AbpAutofacModule),
    typeof(AbpAspNetCoreMultiTenancyModule),
    typeof(SpeakerPortalApplicationModule),
    typeof(SpeakerPortalEntityFrameworkCoreModule),
    typeof(AbpAccountWebOpenIddictModule),
    typeof(AbpAspNetCoreSerilogModule),
    typeof(AbpSwashbuckleModule)
)]
public class SpeakerPortalHttpApiHostModule : AbpModule
{
    public override void PreConfigureServices(ServiceConfigurationContext context)
    {
        PreConfigure<OpenIddictBuilder>(builder =>
        {
            builder.AddValidation(options =>
            {
                options.AddAudiences(ClientIdScopeConsts.SpeakerPortal, ClientIdScopeConsts.PP, ClientIdScopeConsts.Veeva, ClientIdScopeConsts.EpdHcpPortal, ClientIdScopeConsts.OnlineMeeting, ClientIdScopeConsts.Dspot, ClientIdScopeConsts.SOI, ClientIdScopeConsts.CSS);
                options.UseLocalServer();
                options.UseAspNetCore();
            });
        });

        //var hostingEnvironment = context.Services.GetHostingEnvironment();
        //if (!hostingEnvironment.IsDevelopment())
        //{
        PreConfigure<AbpOpenIddictAspNetCoreOptions>(options =>
        {
            options.AddDevelopmentEncryptionAndSigningCertificate = false;
        });

        PreConfigure<OpenIddictServerBuilder>(serverBuilder =>
        {
            serverBuilder.UseAspNetCore().DisableTransportSecurityRequirement();

            //var certificate = new X509Certificate2("openiddict.pfx", "dace7343-3e1c-4874-8eaa-aea77e947808");
            var certificate = new X509Certificate2("Certificate.pfx", "J61MKplmwdwdegre");
            serverBuilder.AddSigningCertificate(certificate);
            serverBuilder.AddEncryptionCertificate(certificate);
            //serverBuilder.AddEventHandler(Controllers.XXX.Descriptor);
            serverBuilder.Configure(options =>
            {
                options.AccessTokenLifetime = TimeSpan.FromHours(1);
                options.RefreshTokenLifetime = TimeSpan.FromHours(2);
                options.GrantTypes.Add(SsoTokenExtensionGrant.ExtensionGrantName);
                options.GrantTypes.Add(WechatTokenExtensionGrant.ExtensionGrantName);
                options.GrantTypes.Add(PhoneTokenExtensionGrant.ExtensionGrantName);
                options.GrantTypes.Add(ThirdPartyTokenExtensionGrant.ExtensionGrantName);
                options.GrantTypes.Add(WeComTokenExtensionGrant.ExtensionGrantName);
                //options.TokenValidationParameters.ValidateIssuer = false;
            });
        });
        //}
    }

    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        var configuration = context.Services.GetConfiguration();
        ConfigureKeyVault(context, configuration);
        ConfigureCustomSettings(context, configuration);
        ConfigureAuthentication(context, configuration);
        ConfigureBundles();
        ConfigureUrls(configuration);
        ConfigureConventionalControllers();
        ConfigureLocalization();
        ConfigureVirtualFileSystem(context);
        ConfigureCors(context, configuration);
        ConfigureSwaggerServices(context, configuration);
        ConfigureJsonSerialization(context);
        ConfigSerilog(configuration, context);
    }

    void ConfigureCustomSettings(ServiceConfigurationContext context, IConfiguration configuration)
    {
        System.Threading.ThreadPool.SetMinThreads(1000, 1000);
        // 设置dataverse链接超时时间，批量更新时
        ServiceClient.MaxConnectionTimeout = new TimeSpan(0, 30, 0);
        var hostingEnvironment = context.Services.GetHostingEnvironment();
        Configure<AbpAuditingOptions>(options =>
        {
            if (!hostingEnvironment.IsEnvironment(EnvironmentConst.Prod))
                options.IsEnabledForGetRequests = true;

            //不需要为所有实体启用审计日志
            //options.EntityHistorySelectors.AddAllEntities();
        });
#if RELEASE
        //
        //if (!hostingEnvironment.EnvironmentName.Equals("HardCode", StringComparison.CurrentCultureIgnoreCase))
        //{
        //	var uri = configuration.GetSection("KeyVaultHost").Value;
        //	IReadOnlyDictionary<string, string> secrets = null;
        //	if (!string.IsNullOrEmpty(uri))
        //		secrets = ConfigKeyVaultHelper.GetAllSecretsFromKeyVault(uri);

        //	foreach (var item in secrets)
        //		configuration[item.Key] = item.Value;
        //}
        context.Services.AddStackExchangeRedisCache(options =>
        {
            options.Configuration = configuration["ConnectionStrings:Redis"];
        });
#endif
        context.Services.AddApplicationInsightsTelemetry(options =>
        {
            options.EnableQuickPulseMetricStream = false;
            options.EnablePerformanceCounterCollectionModule = false;
            options.EnableRequestTrackingTelemetryModule = false;
            options.EnableEventCounterCollectionModule = false;
            options.EnableDependencyTrackingTelemetryModule = false;
            options.EnableAppServicesHeartbeatTelemetryModule = false;
            options.EnableAzureInstanceMetadataTelemetryModule = false;
            options.AddAutoCollectedMetricExtractor = false;
            options.EnableHeartbeat = false;
            options.RequestCollectionOptions.TrackExceptions = false;
            options.EnableDiagnosticsTelemetryModule = false;
            options.ConnectionString = configuration["ConnectionStrings:ApplicationInsights"];
        });
        context.Services.Configure<RedisOptions>(options =>
        {
            options.ConnectionString = configuration["ConnectionStrings:Redis"];
        });
        context.Services.Configure<CognitiveSettings>(configuration.GetSection("AzureOcr"));
        context.Services.Configure<AzureAdSettings>(configuration.GetSection("AzureAd"));
        context.Services.Configure<WechatAppSettings>(configuration.GetSection("WxApp"));
        context.Services.Configure<WecomAppSettings>(configuration.GetSection("WcApp"));
        context.Services.Configure<GraphAppSettings>(configuration.GetSection("Integrations:Graph"));
        context.Services.Configure<BlobOptions>(options =>
        {
            options.Uri = configuration["Blob:Address"];
            options.ContainerName = configuration["Blob:ContainerName"];
            options.ProvidedKey = configuration["ApplicationInsightKey"];
        });
        context.Services.Configure<MvcOptions>(options =>
        {
            options.Filters.Add<ExceptionFilter>();
        });

        //context.Services.Configure<AbpDistributedCacheOptions>(options =>
        //{
        //	//options.KeyPrefix = "SpeakerPortal";
        //});
        //sso settings
        Configure<AbpOpenIddictExtensionGrantsOptions>(options =>
        {
            options.Grants.Add(SsoTokenExtensionGrant.ExtensionGrantName, new SsoTokenExtensionGrant());
            options.Grants.Add(WechatTokenExtensionGrant.ExtensionGrantName, new WechatTokenExtensionGrant());
            options.Grants.Add(WeComTokenExtensionGrant.ExtensionGrantName, new WeComTokenExtensionGrant());
            options.Grants.Add(PhoneTokenExtensionGrant.ExtensionGrantName, new PhoneTokenExtensionGrant());
            options.Grants.Add(ThirdPartyTokenExtensionGrant.ExtensionGrantName, new ThirdPartyTokenExtensionGrant());
        });
        //hangfire
        context.Services.AddHangfire(config =>
        {
            if (hostingEnvironment.IsEnvironment("HardCode") || hostingEnvironment.IsEnvironment("Sit"))
                config.UseMemoryStorage();
            else
                config.UseSqlServerStorage(configuration["ConnectionStrings:Default"]);
        });

        // 注册编码提供者
        Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);

        //distributed locking
        context.Services.AddSingleton<IDistributedLockProvider>(sp =>
        {
            var connection = StackExchange.Redis.ConnectionMultiplexer.Connect(configuration["ConnectionStrings:Redis"]);
            return new RedisDistributedSynchronizationProvider(connection.GetDatabase());
        });
    }

    private void ConfigureAuthentication(ServiceConfigurationContext context, IConfiguration configuration)
    {
        context.Services.ForwardIdentityAuthenticationForBearer(OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme);
        context.Services.ConfigureApplicationCookie(options =>
        {
            options.ForwardDefaultSelector = ctx =>
            {
                //return ctx.Request.Path.StartsWithSegments("/api") ? OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme : null;
                return OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme;
            };
        });
    }

    private void ConfigureBundles()
    {
        //Configure<AbpBundlingOptions>(options =>
        //{
        //	options.StyleBundles.Configure(
        //		LeptonXLiteThemeBundles.Styles.Global,
        //		bundle =>
        //		{
        //			bundle.AddFiles("/global-styles.css");
        //		}
        //	);
        //});
    }

    private void ConfigureUrls(IConfiguration configuration)
    {
        Configure<AppUrlOptions>(options =>
        {
            options.Applications["MVC"].RootUrl = configuration["App:SelfUrl"];
            options.RedirectAllowedUrls.AddRange(configuration["App:RedirectAllowedUrls"].Split(','));

            options.Applications["Angular"].RootUrl = configuration["App:ClientUrl"];
            options.Applications["Angular"].Urls[AccountUrlNames.PasswordReset] = "account/reset-password";
        });
    }

    private void ConfigureVirtualFileSystem(ServiceConfigurationContext context)
    {
        //var hostingEnvironment = context.Services.GetHostingEnvironment();

        //if (hostingEnvironment.IsDevelopment())
        //{
        //	Configure<AbpVirtualFileSystemOptions>(options =>
        //	{
        //		options.FileSets.ReplaceEmbeddedByPhysical<SpeakerPortalDomainSharedModule>(
        //			Path.Combine(hostingEnvironment.ContentRootPath,
        //				$"..{Path.DirectorySeparatorChar}Abbott.SpeakerPortal.Domain.Shared"));
        //		options.FileSets.ReplaceEmbeddedByPhysical<SpeakerPortalDomainModule>(
        //			Path.Combine(hostingEnvironment.ContentRootPath,
        //				$"..{Path.DirectorySeparatorChar}Abbott.SpeakerPortal.Domain"));
        //		options.FileSets.ReplaceEmbeddedByPhysical<SpeakerPortalApplicationContractsModule>(
        //			Path.Combine(hostingEnvironment.ContentRootPath,
        //				$"..{Path.DirectorySeparatorChar}Abbott.SpeakerPortal.Application.Contracts"));
        //		options.FileSets.ReplaceEmbeddedByPhysical<SpeakerPortalApplicationModule>(
        //			Path.Combine(hostingEnvironment.ContentRootPath,
        //				$"..{Path.DirectorySeparatorChar}Abbott.SpeakerPortal.Application"));
        //	});
        //}
    }

    private void ConfigureConventionalControllers()
    {
        Configure<AbpAspNetCoreMvcOptions>(options =>
        {
            options.ConventionalControllers.Create(typeof(SpeakerPortalApplicationModule).Assembly);
        });
    }

    private static void ConfigureSwaggerServices(ServiceConfigurationContext context, IConfiguration configuration)
    {
        if (IsDevelopment(context.Services.GetHostingEnvironment()))
        {
            context.Services.AddAbpSwaggerGenWithOAuth(
            configuration["AuthServer:Authority"],
            new Dictionary<string, string>
            {
                    {"SpeakerPortal", "SpeakerPortal API"}
            },
            options =>
            {
                options.SwaggerDoc("v1", new OpenApiInfo { Title = "SpeakerPortal API", Version = "v1" });
                SwaggerInitializer.ApiInfos.ForEach(f =>
                {
                    options.SwaggerDoc(f.UrlPrefix, f.OpenApiInfo);
                });
                var xmlpaths = Directory.GetFiles(AppContext.BaseDirectory, "*.xml");
                foreach (var xmlpath in xmlpaths)
                {
                    options.IncludeXmlComments(xmlpath, true);
                }
                options.DocInclusionPredicate((docName, description) =>
                {
                    var actionlist = description.ActionDescriptor.EndpointMetadata.Where(x => x is ApiExplorerSettingsAttribute);
                    if (actionlist.Count() > 0)
                    {
                        //判断是否包含这个分组
                        var actionfilter = actionlist.FirstOrDefault() as ApiExplorerSettingsAttribute;
                        return actionfilter.GroupName == docName;
                    }
                    return docName == "v1";
                });
                options.CustomSchemaIds(type => type.FullName);
                options.SchemaFilter<EnumSchemaFilter>();
            });
        }
    }

    private void ConfigureLocalization()
    {
        Configure<AbpLocalizationOptions>(options =>
        {
            options.Languages.Add(new LanguageInfo("ar", "ar", "العربية"));
            options.Languages.Add(new LanguageInfo("cs", "cs", "Čeština"));
            options.Languages.Add(new LanguageInfo("en", "en", "English"));
            options.Languages.Add(new LanguageInfo("en-GB", "en-GB", "English (UK)"));
            options.Languages.Add(new LanguageInfo("fi", "fi", "Finnish"));
            options.Languages.Add(new LanguageInfo("fr", "fr", "Français"));
            options.Languages.Add(new LanguageInfo("hi", "hi", "Hindi", "in"));
            options.Languages.Add(new LanguageInfo("is", "is", "Icelandic", "is"));
            options.Languages.Add(new LanguageInfo("it", "it", "Italiano", "it"));
            options.Languages.Add(new LanguageInfo("hu", "hu", "Magyar"));
            options.Languages.Add(new LanguageInfo("pt-BR", "pt-BR", "Português"));
            options.Languages.Add(new LanguageInfo("ro-RO", "ro-RO", "Română"));
            options.Languages.Add(new LanguageInfo("ru", "ru", "Русский"));
            options.Languages.Add(new LanguageInfo("sk", "sk", "Slovak"));
            options.Languages.Add(new LanguageInfo("tr", "tr", "Türkçe"));
            options.Languages.Add(new LanguageInfo("zh-Hans", "zh-Hans", "简体中文"));
            options.Languages.Add(new LanguageInfo("zh-Hant", "zh-Hant", "繁體中文"));
            options.Languages.Add(new LanguageInfo("de-DE", "de-DE", "Deutsch", "de"));
            options.Languages.Add(new LanguageInfo("es", "es", "Español", "es"));
            options.Languages.Add(new LanguageInfo("el", "el", "Ελληνικά"));
        });
    }
    /// <summary>
    /// 添加AzureKeyVault(本地开发)
    /// </summary>
    /// <param name="context"></param>
    /// <param name="configuration"></param>
    private void ConfigureKeyVault(ServiceConfigurationContext context, IConfiguration configuration)
    {
        // Azure Key Vault 的 URL
        string keyVaultUrl = configuration["KeyVaultHost"].Trim('/');
        Console.WriteLine($"keyVaultUrl:{keyVaultUrl}");
        SecretClient client;

        #region keyvalut
        if (context.Services.GetHostingEnvironment().EnvironmentName != "Sit")
        {
            if (context.Services.GetHostingEnvironment().EnvironmentName == "HardCode")
            {
                string authority = configuration["KeyVault:LocalKeyVault:AuthorityUrl"];
                // Azure AD 应用程序的 Client Id 和 Client Secret
                string clientId = configuration["KeyVault:LocalKeyVault:ClientId"];
                string tenantId = configuration["KeyVault:LocalKeyVault:TenantId"];
                string clientSecret = configuration["KeyVault:LocalKeyVault:ClientSecret"];
                //指定中国区域（默认美国）
                ClientSecretCredentialOptions clientSecretCredentialOptions = new ClientSecretCredentialOptions();
                clientSecretCredentialOptions.AuthorityHost = new Uri(authority);
                var clientCredential = new ClientSecretCredential(tenantId, clientId, clientSecret, clientSecretCredentialOptions);
                client = new SecretClient(new Uri(keyVaultUrl), clientCredential);
            }
            else
            {
                //免密码登录
                client = new SecretClient(new Uri(keyVaultUrl), new DefaultAzureCredential());
            }
            context.Services.AddTransient<IKeyVaultUtil, AzureKeyVaultUtil>(provider =>
            {
                return new AzureKeyVaultUtil(client);
            });
            InitFromKeyValut(context, configuration);
        }


        #endregion


    }

    /// <summary>
    /// 从KeyValut 初始化config
    /// </summary>
    private void InitFromKeyValut(ServiceConfigurationContext context, IConfiguration configuration)
    {
        var keyVaultService = context.Services.BuildServiceProvider().GetService<IKeyVaultUtil>();

        var dict = new Dictionary<string, string>
        {
            {KeyVaultKeys.SQLSERVER_CONNECTION_STRING,"ConnectionStrings:Default" },//数据库连接字符串
            {KeyVaultKeys.INTERMEDIATE_SQLSERVER_CONNECTION_STRING,"ConnectionStrings:DefaultIntermediate" },//数据库连接字符串
            {KeyVaultKeys.REDIS_CONFIGURATION,"ConnectionStrings:Redis"},//redis连接字符串
            {KeyVaultKeys.DATAVERSE_CONFIGURATION,"ConnectionStrings:Dataverse"},//dataverse连接字符串
            {KeyVaultKeys.APPLICATIONINSIGHTS_CONFIGURATION,"ConnectionStrings:ApplicationInsights"},
            {KeyVaultKeys.OCR_CONFIGURATION,"AzureOcr:Endpoint"},//azure ocr address
            {KeyVaultKeys.OCR_KEY_CONFIGURATION,"AzureOcr:Key"},//azure ocr key
            {KeyVaultKeys.PASS_PHRASE,"StringEncryption:DefaultPassPhrase"},
            {KeyVaultKeys.AZUREAD_TENANTID,"AzureAd:TenantId"},
            {KeyVaultKeys.AZUREAD_CLIENTID,"AzureAd:ClientId"},
            {KeyVaultKeys.AZUREAD_SCOPE,"AzureAd:Scope"},
            {KeyVaultKeys.AZUREAD_JWKSURL,"AzureAd:JwksUrl"},
            {KeyVaultKeys.CONSENT_CLIENTID,"Consent:clientId"},
            {KeyVaultKeys.CONSENT_SECRET,"Consent:secret"},
            {KeyVaultKeys.CONSENT_CONSENTCODE,"Consent:ConsentCode"},
            {KeyVaultKeys.WXAPP_APP_ID,"WxApp:AppId"},
            {KeyVaultKeys.WXAPP_APP_SECRET,"WxApp:AppSecret"},
            {KeyVaultKeys.WCAPP_APP_ID,"WcApp:AppId"},
            {KeyVaultKeys.WCAPP_APP_AGENTID,"WcApp:AgentId"},
            {KeyVaultKeys.WCAPP_APP_SECRET,"WcApp:AppSecret"},
            {KeyVaultKeys.STORAGE_ADDRESS,"Blob:Address"},
            {KeyVaultKeys.SMS_URL,"SMS:Url"},
            {KeyVaultKeys.SMS_APPID,"SMS:AppId"},
            {KeyVaultKeys.SMS_SECRETKEY,"SMS:SecretKey"},
            {KeyVaultKeys.DSPOT_WHOLEPROCESSREPORT_PUBLICKEY,"Integrations:DSpot:WholeProcessReport_PublicKey"},
            {KeyVaultKeys.DSPOT_TWOELEMENTSAPPKEY,"Integrations:DSpot:TwoElementsAppKey"},
            {KeyVaultKeys.DSPOT_TWOELEMENTSAPPSECRET,"Integrations:DSpot:TwoElementsAppSecret"},

            {KeyVaultKeys.BPCS_SFTPIP,"Integrations:BPCS:SftpIP"},
            {KeyVaultKeys.BPCS_SFTPUSER,"Integrations:BPCS:SftpUser"},
            {KeyVaultKeys.BPCS_SFTPPWD,"Integrations:BPCS:SftpPwd"},
            {KeyVaultKeys.BPCS_SFTPFOLDERLVL1,"Integrations:BPCS:SftpFolderLvl1"},

            {KeyVaultKeys.EPD_HCP_PORTAL_AESKEY256,"Integrations:EPD_HCP_Portal:AesKey256"},
            {KeyVaultKeys.OM_APPID,"Integrations:OM:AppId"},
            {KeyVaultKeys.OM_APPSECRET,"Integrations:OM:AppSecret"},

            {KeyVaultKeys.VEEVA_USERNAME,"Integrations:Veeva:UserName"},
            {KeyVaultKeys.VEEVA_PASSWORD,"Integrations:Veeva:Password"},

            {KeyVaultKeys.VeevaSftpIP,"Integrations:Veeva:SftpIP"},
            {KeyVaultKeys.VeevaSftpUser,"Integrations:Veeva:SftpUser"},
            {KeyVaultKeys.VeevaSftpPwd,"Integrations:Veeva:SftpPwd"},
            {KeyVaultKeys.VeevaAES256Key,"Integrations:Veeva:AES256Key"},
            {KeyVaultKeys.VeevaAES256IV,"Integrations:Veeva:AES256IV"},

            {KeyVaultKeys.GRAPH_CLIENTID,"Integrations:Graph:ClientId"},
            {KeyVaultKeys.GRAPH_TENANTID,"Integrations:Graph:TenantId"},
            {KeyVaultKeys.GRAPH_CERTPASSWORD,"Integrations:Graph:CertPassword"},
            {KeyVaultKeys.ApplicationInsightKey,"ApplicationInsightKey"},

            {KeyVaultKeys.HangfireUserName,"Hangfire:UserName"},
            {KeyVaultKeys.HangfirePassword,"Hangfire:Password"},
            {KeyVaultKeys.HangfireDigestRealm,"Hangfire:DigestRealm"},
            {KeyVaultKeys.HangfireNonce,"Hangfire:Nonce"},
            {KeyVaultKeys.HangfireOpaque,"Hangfire:Opaque"},

            {KeyVaultKeys.SOI_AppKey,"Integrations:SOI:AppKey"},
            {KeyVaultKeys.SOI_AppSecret,"Integrations:SOI:AppSecret"},
            {KeyVaultKeys.SOI_BaseUrl,"Integrations:SOI:BaseUrl"},

            {KeyVaultKeys.CSS_AppSecret,"Integrations:CSS:AppSecret"},
            {KeyVaultKeys.CSS_BaseUrl,"Integrations:CSS:BaseUrl"},

            //Mdm
            {KeyVaultKeys.MdmAppId,"Integrations:MDM:AppId"},
            {KeyVaultKeys.MdmApiKey,"Integrations:MDM:ApiKey"},
            {KeyVaultKeys.MdmAppSecret,"Integrations:MDM:AppSecret"},
        };

        var all = keyVaultService.GetAllSecretsFromKeyVault();

        foreach (var item in dict)
        {
            string value = all.GetOrDefault(item.Key);

            if (!string.IsNullOrEmpty(value))
            {
                configuration[item.Value] = value;

                //如果是数据库连接时，设置一个只读库连接
                if (item.Key == KeyVaultKeys.SQLSERVER_CONNECTION_STRING)
                    configuration["ConnectionStrings:DefaultReadonly"] = string.Join(";", value.TrimEnd(';'), "ApplicationIntent=ReadOnly");
            }
            Console.WriteLine($"{item.Value}:{configuration[item.Value]}");
        }
    }

    /// <summary>
    /// serilog 写入到数据库
    /// </summary>
    /// <param name="configuration"></param>
    /// <param name="context"></param>
    public void ConfigSerilog(IConfiguration configuration, ServiceConfigurationContext context)
    {
        var logger = new LoggerConfiguration();
        var env = context.Services.GetHostingEnvironment();

        //if (env.EnvironmentName == "HardCode" || env.EnvironmentName == "Sit")
        //{
        //    logger = logger.MinimumLevel.Information().WriteTo.Async(c => c.Console());
        //}
        //else
        //{
        //    logger = logger.MinimumLevel.Warning()
        //    .WriteTo.Async(c => c.Console())
        //    .WriteTo.MSSqlServer(
        //        connectionString: configuration["ConnectionStrings:Default"],
        //        sinkOptions: new MSSqlServerSinkOptions
        //        {
        //            TableName = "DbLogs",
        //            BatchPostingLimit = 100,//批量插入数据库条数
        //            BatchPeriod = TimeSpan.FromSeconds(5),//执行时间间隔
        //            AutoCreateSqlTable = true
        //        }
        //    );
        //}

        logger = logger
            .MinimumLevel.Override("Microsoft", Serilog.Events.LogEventLevel.Warning)
            .MinimumLevel.Override("Microsoft.EntityFrameworkCore.Model.Validation", Serilog.Events.LogEventLevel.Error)
            .MinimumLevel.Override("Volo.Abp", Serilog.Events.LogEventLevel.Warning)
            .MinimumLevel.Override("OpenIddict.Server", Serilog.Events.LogEventLevel.Warning)
            .MinimumLevel.Override("Hangfire", Serilog.Events.LogEventLevel.Warning)
            .WriteTo.Async(c => c.Console())
            .WriteTo.MSSqlServer(
                connectionString: configuration["ConnectionStrings:Default"],
                sinkOptions: new MSSqlServerSinkOptions
                {
                    TableName = "DbLogs",
                    BatchPostingLimit = 100,//批量插入数据库条数
                    BatchPeriod = TimeSpan.FromSeconds(5),//执行时间间隔
                    AutoCreateSqlTable = true
                }
            );

        Log.Logger = logger
         .Enrich.FromLogContext()
         .CreateLogger();
    }

    private void ConfigureCors(ServiceConfigurationContext context, IConfiguration configuration)
    {
        context.Services.AddCors(options =>
        {
            options.AddDefaultPolicy(builder =>
            {
                builder
                    .WithOrigins(
                        configuration["App:CorsOrigins"]
                            .Split(",", StringSplitOptions.RemoveEmptyEntries)
                            .Select(o => o.RemovePostFix("/"))
                            .ToArray()
                    )
                    .WithAbpExposedHeaders()
                    .SetIsOriginAllowedToAllowWildcardSubdomains()
                    .AllowAnyHeader()
                    .AllowAnyMethod()
                    .AllowCredentials();
            });
        });
    }

    private void ConfigureJsonSerialization(ServiceConfigurationContext context)
    {
        context.Services.AddControllers()
            .AddJsonOptions(options =>
            {
                options.JsonSerializerOptions.ReferenceHandler = System.Text.Json.Serialization.ReferenceHandler.IgnoreCycles;
            });
        //设置日期时间格式
        context.Services.Configure<AbpJsonOptions>(options =>
        {
            options.InputDateTimeFormats = new List<string>
            {
                "yyyy-MM-dd",
                "yyyy-MM-dd HH:mm:ss",
                "yyyy-MM-dd HH:mm:ss.fff",
                "yyyy-MM-dd HH:mm:ss.ffffff"
            };
            options.OutputDateTimeFormat = "yyyy-MM-dd HH:mm:ss";
        });
    }

    /// <summary>
    /// 是否开启调试模式
    /// </summary>
    /// <param name="env"></param>
    /// <returns></returns>
    private static bool IsDevelopment(IHostEnvironment env)
    {
        var isDev = !env.IsEnvironment(EnvironmentConst.Prod);
        return isDev;
    }

    public async override void OnPostApplicationInitialization(ApplicationInitializationContext context)
    {
        var app = context.GetApplicationBuilder();
        //分布式锁，防止种子数据重复初始化
        await using (var handle = await app.ApplicationServices.GetService<IAbpDistributedLock>().TryAcquireAsync(DistributedLock.DistributedLock_Migration))
        {
            if (handle != null)
            {
                app.ApplicationServices
                    .GetService<SpeakerPortalDbMigrationService>()
                    .MigrateAsync()
                    .Wait();
            }
        }
    }

    public override void OnApplicationInitialization(ApplicationInitializationContext context)
    {
        var app = context.GetApplicationBuilder();
        var env = context.GetEnvironment();

        //if (!env.IsEnvironment(EnvironmentConst.Prod))
        //    IdentityModelEventSource.ShowPII = true;

        app.UseAbpRequestLocalization();

        app.UseCorrelationId();
        app.UseStaticFiles();
        app.UseRouting();
        app.UseCors();
        app.UseAuthentication();
        app.UseAbpOpenIddictValidation();

        if (MultiTenancyConsts.IsEnabled)
        {
            app.UseMultiTenancy();
        }

        app.UseUnitOfWork();
        app.UseAuthorization();

        if (IsDevelopment(env))
        {
            app.UseSwagger();
            app.UseAbpSwaggerUI(c =>
            {
                c.SwaggerEndpoint("/swagger/v1/swagger.json", "SpeakerPortal API");

                SwaggerInitializer.ApiInfos.ForEach(f =>
                {
                    c.SwaggerEndpoint($"/swagger/{f.UrlPrefix}/swagger.json", f.Name);
                });
                var configuration = context.ServiceProvider.GetRequiredService<IConfiguration>();
                c.OAuthClientId(configuration["AuthServer:SwaggerClientId"]);
                c.OAuthScopes("SpeakerPortal");
            });
        }

        app.UseAuditing();
        app.UseAbpSerilogEnrichers();
        app.UseHangfireDashboard("/HFJobs", new DashboardOptions
        {
            DisplayStorageConnectionString = false,
            IgnoreAntiforgeryToken = true,
            Authorization = new[] { new DigestAuthorizationFilter() }
        });
        app.UseConfiguredEndpoints();
    }
}
