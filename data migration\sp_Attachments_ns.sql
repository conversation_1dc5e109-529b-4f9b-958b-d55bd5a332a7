CREATE PROCEDURE dbo.sp_Attachments_ns
AS 
BEGIN
	select 
at2.[BPMId]
,at2.[Id]
,at2.[FunctionModule]
,at2.[FileName]
,at2.[Size]
,at2.[Suffix]
,at2.[FilePath]
,'{}'[ExtraProperties]
,'NULL'[ConcurrencyStamp]
,at2.[CreationTime]
,ss.spk_NexBPMCode as [CreatorId]
,at2.[LastModificationTime]
,at2.[LastModifierId]
,0 [IsDeleted]
,at2.[DeleterId]
,at2.[DeletionTime] 
into #Attachments
from Attachments_tmp at2 
left join spk_staffmasterdata ss 
on at2.CreatorId =ss.bpm_id 



--drop table #Attachments

--删除表
 IF OBJECT_ID(N'PLATFORM_ABBOTT_STG.dbo.Attachments ', N'U') IS NOT NULL
 BEGIN
		drop table PLATFORM_ABBOTT_STG.dbo.Attachments
		select *
        into PLATFORM_ABBOTT_STG.dbo.Attachments from #Attachments
 PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
 END
 ELSE
 BEGIN
--落成实体表
 select *
    into PLATFORM_ABBOTT_STG.dbo.Attachments from #Attachments
 -- select * from #vendor_tbl
 PRINT(N'落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
 END
END
