CREATE PROCEDURE dbo.sp_PurPRApplicationDetails
AS 
BEGIN
PRINT(N'开始执行时间'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
--初始化xml
IF OBJECT_ID(N'PLATFORM_ABBOTT_Stg.dbo.XML ', N'U') IS NOT NULL
BEGIN
	PRINT(N'已经初始化过'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select * into PLATFORM_ABBOTT_Stg.dbo.XML 
	from (
		select   fg.ProcInstId,
		unit.value('.', 'nvarchar(100)')  unit,
		NO.value('.', 'nvarchar(100)')  NO
		from (SELECT
				cast (XmlContent as XML) as  XmlContent,
				ProcInstId
				FROM PLATFORM_ABBOTT_Stg.dbo.ODS_T_FORMINSTANCE_GLOBAL
			) fg 
CROSS APPLY XmlContent.nodes('/root/PRGridPanel/row/unit') AS XMLTable(unit)
CROSS APPLY XmlContent.nodes('/root/PRGridPanel/row/No') AS XMLTable1(NO)
) b
PRINT(N'初始化完成'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
--删除临时表ABTPI

IF OBJECT_ID('tempdb..#ABTPI1') IS NOT NULL
BEGIN
    DROP TABLE #ABTPI1;
END
ELSE
BEGIN
    PRINT 'Table [dbo].[YourTableName] does not exist.';
END;

--删除临时表PurPRApplicationDetails_tmp
IF OBJECT_ID('tempdb..#PurPRApplicationDetails_tmp') IS NOT NULL
BEGIN
    DROP TABLE #PurPRApplicationDetails_tmp;
ENd
ELSE
BEGIN
    PRINT 'Table [dbo].[YourTableName] does not exist.';
END;

--创建临时表#ABTPI1
PRINT N'创建临时表#ABTPI1'
select * into #ABTPI1 from (
	select  ROW_NUMBER() over( PARTITION by ProcInstId  order by expenseCategory_Text desc) as a,* 
	from PLATFORM_ABBOTT_Stg.dbo.ODS_AUTO_BIZ_T_ProcurementApplication_Info )B 
	where a=1
PRINT N'创建成功'

--创建PurPRApplicationDetails_tmp层表
select *  
into #PurPRApplicationDetails_tmp 
from (
select 
newid() AS Id,--自动生成的uuid
PI.ProcInstId,
PI.ProcInstId AS PRApplicationId,--基于03-1迁移的申请单主信息，以ProcInstId定位对应的PurPRApplications.ID
case when PIR.payMent='AR' then '1'
when PIR.payMent='AP' then '2' 
when PIR.payMent='ER' then '3' 
when PIR.payMent='ICB In' then '4' 
when PIR.payMent='FG' then '5'
else '' end AS PayMethod,--AR-1, AP-2, ER-3, ICB In-4, FG-5，如为空则留空
PIR.expectedDate AS EstimateDate,--
concat(vendorCode,VendorName,f_code) AS VendorId,--基于此处的VendorCode及VendorName，结合PurPRApplications该单的公司编码f_code，去查询BPCSPMFVM对应的记录(VNDERX,VEXTNM,VMCMPY)，对于匹配成功的记录，再找到其对应的BPCSAVM.ID并填入(基于VMCMPY=VCMPNY AND VNDERX=VENDOR)
expenseNatureCode AS CostNature,--以该ID匹配至费用性质ID
concat(PIR.cityCode,PIR.city) AS CityId,--以该组合查询城市主数据，得到城市主数据ID
PIR.content AS Content,--
PIR.num AS Quantity,--
cast('todo' as nvarchar(500)) AS Unit,--以ProcInstId找到对应的单据xml后，在PRGridPanel内查询，每一个row中以row=AUTO_BIZ_T_ProcurementApplication_Info_PR.No定位至对应行的信息，再找到该字段填充
PIR.price AS UnitPrice,--
PIR.rmbAmount AS TotalAmount,--
'' AS TaxRate,--留空
'' AS TaxAmount,--留空
PI.productCbo_Value AS ProductId,--以该ID匹配至产品ID
'' AS VendorType,--留空(原BPM有一类似字段"讲者类型"，但是无法匹配至现有的身份，将该字段的值填入下方的VendorTypeName作为记录用)
'' AS AcademyName,--留空(原BPM无此字段)
'' AS AcademyJob,--留空(原BPM无此字段)serialNumber
PIR.SlideTypeId AS SlideType,--以该ID匹配至字典-幻灯片类型，填入字典Code
PIR.SlideName AS SlideName,--以名称匹配至字典-幻灯片名称，填入字典Code
PIR.ServiceTime AS ServiceDuration,--
'' AS BackUpVendors,--基于迁移后的PurPRApplicationDetailBackupVendors内容拼接而成
PIR.ExecutiveEmail AS Executor,--以该信息匹配至BPCSPMFVM.venlad，基于匹配后的结果以VMCMPY+VNDERX组合查询BPCSAVM对应VCMPNY+VENDOR的组合，取匹配出的BPCSAVM.ID，若匹配失败则留空
'' AS RceNo,--注意此处若填写为"NULL"或""都存为空
PIR.icbAmount AS IcbAmount,--
'{}' AS ExtraProperties,--默认填写为"{}"
'' AS ConcurrencyStamp,--?
applicantEmpId AS CreationTime,--与对应的PurPRApplications记录保持一致即可
PI.applicationDate AS CreatorId,--与对应的PurPRApplications记录保持一致即可
'' AS LastModificationTime,--默认为空
'' AS LastModifierId,--默认为空
'0' AS IsDeleted,--默认为0
'' AS DeleterId,--默认为空
'' AS DeletionTime,--默认为空
case when PurchaseEmpId is not null then '2' 
when PurchaseEmpId is null  and ReturnReason is not null then '3' 
else '1' end AS PushFlag,--对于PaymentType为AP的记录，以ProcInstId+RowNumber=ProcInstId+No匹配出对应的记录，若此处推送采购人员有值则为已推送(2)，若采购人员无值且ReturnReason非空则为被退回(3)，否则为未推送(1)
PurchaseEmpId AS PurchaserId,--以该ID匹配至员工主数据
PushDate AS PushTime,--
ReturnReason AS RollbackReason,--
case when IsWaiver=N'是' then '1' else '0' end AS IsOffline,--对于PushFlag为已推送/被退回的记录，以ProcInstId+RowNumber=ProcInstId+No匹配出对应的T_Pur_PRItems_Info记录，再以ID匹配出对应的T_Pur_PRItems_Info_Child记录，若为"是"则填为1，否则填为0
'' AS OfflineNo,--默认为空(BPM无该信息)
vendorName AS VendorName,--
No AS RowNo,--
case when PIR.expectedDate<PI.applicationDate then '1' else '0' end AS IsPrLate,--若该行预计日期小于PR的申请日期，则标记为1，否则标记为0
case when NoType = 'R' then PIR.re_id else 'NULL' end  AS HedgePrDetailId,--对于"NoType"为R的反冲行，此处记录了该行用于反冲哪一行，需要基于填入的行号查询该PR内No为该编号的行，并找回该行的ID填入
NoType,
IDNumber AS CardNo,--
certificateNo AS CertificateCode,--
CONCAT(PIR.city,'_',cityCode)  AS CityIdName,--需要以这两个值以"-"分隔后拼接
PIR.costCenterCode AS CostCenter,--以该值匹配至成本中心主数据后找回ID
Res_Data1 AS CostCenterCode,--以AUTO_BIZ_T_ProcurementApplication_Info_PR.costCenterCode查询该表中的Res_Code后得到对应的值
PIR.costCenter AS CostCenterName,--
expenseNatureCode AS CostNatureCode,--以AUTO_BIZ_T_ProcurementApplication_Info_PR.expenseNatureCode查询该表中的Res_Data1后得到对应的值
PIR.expenseNature AS CostNatureName,--
PIR.executiveEmail AS ExecutorEmail,--
PIR.executive AS ExecutorName,--
PIR.HCPLevel AS HcpLevelName,--
PIR.Department AS HosDepartment,--
PIR.SpeakInfo AS Hospital,--
case when PIR.payMent ='AR' and GIP.PRNo is not null and GIP.PR_Item_No is not null then PIR.ProcInstID 
else '' end AS IsVendorConfimed,
--对于付款方式为"AR"的行，以单号=PRNo，行号=PR_Item_No查询，若能查询到对应的记录代表该行已经进行了供应商确认并生成了收货申请，否则填为空
PIR.OriginalExpectedDate AS OriginalEstimateDate,--
PIR.OriginalVendorCode AS OriginalVendorCode,--
concat(OriginalVendorCode,VendorName,f_code) AS OriginalVendorId,--基于此处的OriginalVendorCode及VendorName，结合PurPRApplications该单的公司编码f_code，去查询BPCSPMFVM对应的记录(VNDERX,VEXTNM,VMCMPY)，对于匹配成功的记录，再找到其对应的BPCSAVM.ID并填入(基于VMCMPY=VCMPNY AND VNDERX=VENDOR)
OriginalVendor AS OriginalVendorName,--
SlideTypeName AS SlideTypeName,--
Department AS StandardDepartment,--
'' AS StandardDepartmentId,--留空(历史科室无法匹配至科室主数据)
TermCode  AS TermCode,--以该Code查询BPCSAVT的VTERM，查询到VTMDDY后以"{VTERM}_{VTMDDY}天"的格式拼接后填入
TermCode AS TermCodeDays,--以该Code查询BPCSAVT的VTERM，查询到的VTMDDY
vendorCode AS VendorCode,--
'' AS VendorTypeName,--留空(原BPM有一类似字段"讲者类型"，但是无法匹配至现有的身份，将该字段的值填入下方的VendorTypeName作为记录用)
HCPLevel AS HcpLevelCode,
case when (c.processStatus =N'重发起' or c.processStatus =N'主采购循环审批中') and PIR.payMent ='AP' then '4' 
when (c.processStatus =N'关闭' or c.processStatus =N'PO审批结束') and PIR.payMent ='AP' then '5'
when c.processStatus =N'发起人终止'and PIR.payMent ='AP' then '6' 
when (e.processStatus =N'重发起' or e.processStatus =N'审批中') and PIR.payMent ='AP' then '1'
when e.processStatus =N'完成' and PIR.payMent ='AP' then '2'
when e.processStatus =N'发起人终止'and PIR.payMent ='AP' then '3' 
end AS OrderStatusFlag,--对于付款方式为""AP""的行，以单号=PRNo，行号=PR_Item_No查询，若能查询到对应的记录代表该行已经发起了PO，
--再基于该表中的ProcInstID查询Form_92ccaf9b95de4d7d9d8b411b2a030edc.ProcInstId找到对应PO的审批状态：
--a.重发起/主采购循环审批中->4(发起PO中)
--b.关闭/PO审批结束->5(PO完成)
--c.发起人终止->6(PO审批拒绝)"
--processStatus AS ,--
--"ProcInstID AS ,--对于付款方式为""AP""的行，以单号=PRFormCode，行号=PRNumber查询，若能查询到对应的记录代表该行已经发起了Bidding，再基于该表中的ProcInstID查询Form_fefa2338743b4ebea533c8f6c5c2bacd.ProcInstId找到对应比价申请的审批状态：(若该单据已经发起过PO，则不需要继续查询是否有Bidding记录)
--a.重发起/审批中->1(发起Bidding中)
--b.完成->2(Bidding完成)
--c.发起人终止->3(Bidding拒绝)"
--processStatus AS ,--
BiddingFormCode AS BiddingId,--以该编码匹配至Bidding表的申请单号以找到Bidding的ID
'' AS ExceptionNumber--留空(历史单据无该数据)
from  #ABTPI1 PI --543891
join  PLATFORM_ABBOTT_Stg.dbo.ODS_AUTO_BIZ_T_ProcurementApplication_Info_PR PIR 
on Pi.ProcInstId =PIR.ProcInstId --1338084
left join PLATFORM_ABBOTT_Stg.dbo.ODS_T_Pur_PRItems_Info PRI
on PIR.ProcInstId =PRI.ProcInstId and PRI.RowNumber=PIR.no--1338084
left join PLATFORM_ABBOTT_Stg.dbo.ODS_T_RESOURCE otr 
on otr.Res_Code =pir.costCenterCode
left join (select *,ROW_NUMBER () over(partition by PR_Item_No,PRNo order by  ProcInstId desc) rn from PLATFORM_ABBOTT_Stg.dbo.ODS_AUTO_BIZ_T_GoodsReceiveApplication_Info_PR) GIP
on PI.serialNumber =GIP.PRNo  and PIR.no=GIP.PR_Item_No  and GIP.rn=1
left join PLATFORM_ABBOTT_Stg.dbo.ODS_T_Pur_PRItems_Info_Child a
on PRI.id=a.id
left join (select *,ROW_NUMBER () over(partition by PRNo,PR_Item_No order by  ProcInstId desc) rn from PLATFORM_ABBOTT_Stg.dbo.ods_AUTO_BIZ_PurchaseOrderApplication_Info_PR) b
on PI.serialNumber =b.PRNo  and PIR.no=b.PR_Item_No and b.rn=1
left join PLATFORM_ABBOTT_Stg.dbo.ODS_Form_92ccaf9b95de4d7d9d8b411b2a030edc c
on b.ProcInstId=c.ProcInstId
left  join (select *,ROW_NUMBER () over(partition by PRNumber,PRFormCode order by  ProcInstId desc) rn from PLATFORM_ABBOTT_Stg.dbo.ODS_AUTO_BIZ_T_BiddingApplication_Info_PR) d
on PI.serialNumber=d.PRFormCode and PIR.NO=d.PRNumber and d.rn=1
left join PLATFORM_ABBOTT_Stg.dbo.ods_Form_fefa2338743b4ebea533c8f6c5c2bacd e
on d.ProcInstId=e.ProcInstId
)A
PRINT(N'临时表搭建成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))



UPDATE a
SET a.Unit = b.unit
FROM #PurPRApplicationDetails_tmp a
left JOIN (
SELECT DISTINCT ProcInstId, unit, NO COLLATE SQL_Latin1_General_CP1_CI_AS AS NO
FROM PLATFORM_ABBOTT_Stg.dbo.XML
) b ON b.NO = a.RowNo AND a.ProcInstId = b.ProcInstId
PRINT(N'更新Unit'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))

IF OBJECT_ID(N'PLATFORM_ABBOTT_Stg.dbo.PurPRApplicationDetails_tmp', N'U') IS NOT NULL
BEGIN
	update a
	set a.ProcInstId             = b.ProcInstId
       ,a.PRApplicationId        = b.PRApplicationId
       ,a.PayMethod              = b.PayMethod
       ,a.EstimateDate           = b.EstimateDate
       ,a.VendorId               = b.VendorId
       ,a.CostNature             = b.CostNature
       ,a.CityId                 = b.CityId
       ,a.Content                = b.Content
       ,a.Quantity               = b.Quantity
       ,a.Unit                   = b.Unit
       ,a.UnitPrice              = b.UnitPrice
       ,a.TotalAmount            = b.TotalAmount
       ,a.TaxRate                = b.TaxRate
       ,a.TaxAmount              = b.TaxAmount
       ,a.ProductId              = b.ProductId
       ,a.VendorType             = b.VendorType
       ,a.AcademyName            = b.AcademyName
       ,a.AcademyJob             = b.AcademyJob
       ,a.SlideType              = b.SlideType
       ,a.SlideName              = b.SlideName
       ,a.ServiceDuration        = b.ServiceDuration
       ,a.BackUpVendors          = b.BackUpVendors
       ,a.Executor               = b.Executor
       ,a.RceNo                  = b.RceNo
       ,a.IcbAmount              = b.IcbAmount
       ,a.ExtraProperties        = b.ExtraProperties
       ,a.ConcurrencyStamp       = b.ConcurrencyStamp
       ,a.CreationTime           = b.CreationTime
       ,a.CreatorId              = b.CreatorId
       ,a.LastModificationTime   = b.LastModificationTime
       ,a.LastModifierId         = b.LastModifierId
       ,a.IsDeleted              = b.IsDeleted
       ,a.DeleterId              = b.DeleterId
       ,a.DeletionTime           = b.DeletionTime
       ,a.PushFlag               = b.PushFlag
       ,a.PurchaserId            = b.PurchaserId
       ,a.PushTime               = b.PushTime
       ,a.RollbackReason         = b.RollbackReason
       ,a.IsOffline              = b.IsOffline
       ,a.OfflineNo              = b.OfflineNo
       ,a.VendorName             = b.VendorName
       ,a.RowNo                  = b.RowNo
       ,a.IsPrLate               = b.IsPrLate
       ,a.HedgePrDetailId        = b.HedgePrDetailId
       ,a.NoType                 = b.NoType
       ,a.CardNo                 = b.CardNo
       ,a.CertificateCode        = b.CertificateCode
       ,a.CityIdName             = b.CityIdName
       ,a.CostCenter             = b.CostCenter
       ,a.CostCenterCode         = b.CostCenterCode
       ,a.CostCenterName         = b.CostCenterName
       ,a.CostNatureCode         = b.CostNatureCode
       ,a.CostNatureName         = b.CostNatureName
       ,a.ExecutorEmail          = b.ExecutorEmail
       ,a.ExecutorName           = b.ExecutorName
       ,a.HcpLevelName           = b.HcpLevelName
       ,a.HosDepartment          = b.HosDepartment
       ,a.Hospital               = b.Hospital
       ,a.IsVendorConfimed       = b.IsVendorConfimed
       ,a.OriginalEstimateDate   = b.OriginalEstimateDate
       ,a.OriginalVendorCode     = b.OriginalVendorCode
       ,a.OriginalVendorId       = b.OriginalVendorId
       ,a.OriginalVendorName     = b.OriginalVendorName
       ,a.SlideTypeName          = b.SlideTypeName
       ,a.StandardDepartment     = b.StandardDepartment
       ,a.StandardDepartmentId   = b.StandardDepartmentId
       ,a.TermCode               = b.TermCode
       ,a.TermCodeDays           = b.TermCodeDays
       ,a.VendorCode             = b.VendorCode
       ,a.VendorTypeName         = b.VendorTypeName
       ,a.HcpLevelCode           = b.HcpLevelCode
       ,a.OrderStatusFlag        = b.OrderStatusFlag
       ,a.BiddingId              = b.BiddingId
       ,a.ExceptionNumber        = b.ExceptionNumber
    from PLATFORM_ABBOTT_Stg.dbo.PurPRApplicationDetails_tmp a
    left join #PurPRApplicationDetails_tmp b on a.ProcInstId = b.ProcInstId and a.RowNo = b.RowNo
    
    insert into PLATFORM_ABBOTT_Stg.dbo.PurPRApplicationDetails_tmp
    select Id
          ,a.ProcInstId
          ,a.PRApplicationId
          ,a.PayMethod
          ,a.EstimateDate
          ,a.VendorId
          ,a.CostNature
          ,a.CityId
          ,a.Content
          ,a.Quantity
          ,a.Unit
          ,a.UnitPrice
          ,a.TotalAmount
          ,a.TaxRate
          ,a.TaxAmount
          ,a.ProductId
          ,a.VendorType
          ,a.AcademyName
          ,a.AcademyJob
          ,a.SlideType
          ,a.SlideName
          ,a.ServiceDuration
          ,a.BackUpVendors
          ,a.Executor
          ,a.RceNo
          ,a.IcbAmount
          ,a.ExtraProperties
          ,a.ConcurrencyStamp
          ,a.CreationTime
          ,a.CreatorId
          ,a.LastModificationTime
          ,a.LastModifierId
          ,a.IsDeleted
          ,a.DeleterId
          ,a.DeletionTime
          ,a.PushFlag
          ,a.PurchaserId
          ,a.PushTime
          ,a.RollbackReason
          ,a.IsOffline
          ,a.OfflineNo
          ,a.VendorName
          ,a.RowNo
          ,a.IsPrLate
          ,a.HedgePrDetailId
          ,a.NoType
          ,a.CardNo
          ,a.CertificateCode
          ,a.CityIdName
          ,a.CostCenter
          ,a.CostCenterCode
          ,a.CostCenterName
          ,a.CostNatureCode
          ,a.CostNatureName
          ,a.ExecutorEmail
          ,a.ExecutorName
          ,a.HcpLevelName
          ,a.HosDepartment
          ,a.Hospital
          ,a.IsVendorConfimed
          ,a.OriginalEstimateDate
          ,a.OriginalVendorCode
          ,a.OriginalVendorId
          ,a.OriginalVendorName
          ,a.SlideTypeName
          ,a.StandardDepartment
          ,a.StandardDepartmentId
          ,a.TermCode
          ,a.TermCodeDays
          ,a.VendorCode
          ,a.VendorTypeName
          ,a.HcpLevelCode
          ,a.OrderStatusFlag
          ,a.BiddingId
          ,a.ExceptionNumber
   from #PurPRApplicationDetails_tmp a
   where NOT EXISTS (SELECT * FROM PLATFORM_ABBOTT_Stg.dbo.PurPRApplicationDetails_tmp WHERE ProcInstId = a.ProcInstId 
    and RowNo = a.RowNo)
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into PLATFORM_ABBOTT_Stg.dbo.PurPRApplicationDetails_tmp from #PurPRApplicationDetails_tmp
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
end;
