﻿apiVersion: apps/v1
kind: Deployment
metadata:
  # Kind 的名称
  name: corp-ca191-speakerapi-p
  namespace: default
spec:
  selector:
    matchLabels:
      # 容器标签的名字，发布 Service 时，selector 需要和这里对应
      app: corp-ca191-speakerapi-p
  # 部署的实例数量
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 2        # 一次可以添加多少个Pod
      maxUnavailable: 2  # 滚动更新期间最大多少个Pod不可用
  template:
    metadata:
      labels:
        app: corp-ca191-speakerapi-p
    spec:
      # 配置容器，数组类型，说明可以配置多个容器
      containers:
      # 容器名称
      - name: corp-ca191-speakerapi-p
        # 容器镜像
        image: abbottchina.azurecr.cn/corp-ca191-nexbpm-prod/speaker-api:__TAG
        env:
        - name: ASPNETCORE_ENVIRONMENT
          value: "Prod"
        - name: ASPNETCORE_URLS
          value: "http://+:80"
        # 只有镜像不存在时，才会进行镜像拉取IfNotPresent
        imagePullPolicy: Always
        ports:
        # Pod 端口
        - containerPort: 80

---
apiVersion: v1
kind: Service
metadata:
  name: corp-ca191-speakerapi-p
spec:
  selector:
    app: corp-ca191-speakerapi-p
  type: ClusterIP
  ports:
  - protocol: TCP
    port: 443
    targetPort: 80

---
##另一个服务for移动端
apiVersion: apps/v1
kind: Deployment
metadata:
  # Kind 的名称
  name: corp-ca191-speakerapi-m-p
  namespace: default
spec:
  selector:
    matchLabels:
      # 容器标签的名字，发布 Service 时，selector 需要和这里对应
      app: corp-ca191-speakerapi-m-p
  # 部署的实例数量
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 2        # 一次可以添加多少个Pod
      maxUnavailable: 2  # 滚动更新期间最大多少个Pod不可用
  template:
    metadata:
      labels:
        app: corp-ca191-speakerapi-m-p
    spec:
      # 配置容器，数组类型，说明可以配置多个容器
      containers:
      # 容器名称
      - name: corp-ca191-speakerapi-m-p
        # 容器镜像
        image: abbottchina.azurecr.cn/corp-ca191-nexbpm-prod/speaker-api:__TAG
        env:
        - name: ASPNETCORE_ENVIRONMENT
          value: "Prod"
        - name: ASPNETCORE_URLS
          value: "http://+:80"
        # 只有镜像不存在时，才会进行镜像拉取IfNotPresent
        imagePullPolicy: Always
        ports:
        # Pod 端口
        - containerPort: 80

---
apiVersion: v1
kind: Service
metadata:
  name: corp-ca191-speakerapi-m-p
spec:
  selector:
    app: corp-ca191-speakerapi-m-p
  type: ClusterIP
  ports:
  - protocol: TCP
    port: 443
    targetPort: 80
