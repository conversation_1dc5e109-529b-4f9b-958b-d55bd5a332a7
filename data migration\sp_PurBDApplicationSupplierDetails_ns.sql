CREATE PROCEDURE dbo.sp_PurBDApplicationSupplierDetails_ns
AS 
BEGIN
	select 
a.Id,
a.BDApplicationId,
UPPER(cc.Id) as VendorId,
a.VendorName,
a.Unit,
a.Quantity,
a.Total<PERSON>mount,
a.ExtraProperties,
a.ConcurrencyStamp,
a.CreationTime,
UPPER(ss.spk_NexBPMCode) as CreatorId,
a.LastModificationTime,
a.LastModifierId,
a.<PERSON>,
a.DeleterId,
a.DeletionTime,
a.UnitPrice
into #PurBDApplicationSupplierDetails
from PLATFORM_ABBOTT_Dev.dbo.PurBDApplicationSupplierDetails_tmp a 
left join PLATFORM_ABBOTT_Dev.dbo.ods_BPCS_PMFVM f
on a.VendorId = f.[VNDERX] and a.VendorName=f.[VEXTNM] and a.CompanyId=f.[VMCMPY]
left join PLATFORM_ABBOTT_Dev.dbo.ODS_BPCS_AVM  cc
on f.[VMCMPY]=cc.[VCMPNY] AND f.[VNDERX]=cc.[VENDOR]
left join PLATFORM_ABBOTT_Dev.dbo.spk_staffmasterdata ss 
on a.CreatorId =ss.bpm_id 

IF OBJECT_ID(N'PLATFORM_ABBOTT_Dev.dbo.PurBDApplicationSupplierDetails', N'U') IS NOT NULL
BEGIN
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
    drop table PLATFORM_ABBOTT_Dev.dbo.PurBDApplicationSupplierDetails
    select *
    into PLATFORM_ABBOTT_Dev.dbo.PurBDApplicationSupplierDetails from #PurBDApplicationSupplierDetails
	END
	ELSE
	BEGIN
    --落成实体表
    select *
    into PLATFORM_ABBOTT_Dev.dbo.PurBDApplicationSupplierDetails from #PurBDApplicationSupplierDetails
	  PRINT(N' 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END

END
