apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: speaker-int-ingress
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"

spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - speaker-int-api-d.oneabbott.com
    secretName: tls-speaker-int-api-d-secret
  rules:
  - host: speaker-int-api-d.oneabbott.com
    http:
      paths:
      - path: /api/Misc/Health
        pathType: Prefix
        backend:
          service:
            name: corp-ca191-speakerapi-d
            port:
              number: 80
      - path: /api/EpdOnlineMeeting/(.*)
        pathType: Prefix
        backend:
          service:
            name: corp-ca191-speakerapi-d
            port:
              number: 80
      - path: /api/IntegrationDspot/(.*)
        pathType: Prefix
        backend:
          service:
            name: corp-ca191-speakerapi-d
            port:
              number: 80
      - path: /api/EpdPortal/(.*)
        pathType: Prefix
        backend:
          service:
            name: corp-ca191-speakerapi-d
            port:
              number: 80              
      - path: /swagger/(.*)
        pathType: Prefix
        backend:
          service:
            name: corp-ca191-speakerapi-d
            port:
              number: 80