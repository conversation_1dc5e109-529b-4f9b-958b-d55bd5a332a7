create proc sp_AgentConfig_ns
as
begin
	if object_id('AgentConfig','u') is null
		select
			a.[spk_agentconfigurationid] Id,
			a._WorkflowType [WorkflowType],
			a.[spk_originalapprover] [OriginalOperator],
			a.[spk_agent] [AgentOperator],
			a.[spk_startDate] [StartDate],
			a.[spk_endDate] [EndDate],
			a._Creator [Creator],
			a.[spk_remark] [Remark],
			a._Status [Status],
			a.[spk_isnotifyoriginaloperator] [IsNotifyOriginalOperator],
			'{}' [ExtraProperties],
			'' [ConcurrencyStamp],
			a._CreationTime [CreationTime],
			a._Creator CreatorId,
			'' [LastModificationTime],
			'' [LastModifierId],
			0 [IsDeleted],
			'' [DeleterId],
			'' [DeletionTime],
			a.[spk_agentconfigurationid] [DataverseId],
			a._BpmId
		into [AgentConfig]
		from spk_agentconfiguration a
	else
	begin
		update t
		set t.[WorkflowType]=tt._WorkflowType,
		t.[OriginalOperator]=tt.[spk_originalapprover],
		t.[AgentOperator]=tt.[spk_agent],
		t.[StartDate]=tt.[spk_startDate],
		t.[EndDate]=tt.[spk_endDate],
		t.[Creator]=tt._Creator,
		t.[Remark]=tt.[spk_remark],
		t.[Status]=tt._Status,
		t.[IsNotifyOriginalOperator]=tt.[spk_isnotifyoriginaloperator],
		t.[CreationTime]=tt._CreationTime,
		t.CreatorId=tt._Creator
		from AgentConfig t join spk_agentconfiguration tt on t._BpmId=tt._BpmId

		insert AgentConfig
		select
			a.[spk_agentconfigurationid] Id,
			a._WorkflowType [WorkflowType],
			a.[spk_originalapprover] [OriginalOperator],
			a.[spk_agent] [AgentOperator],
			a.[spk_startDate] [StartDate],
			a.[spk_endDate] [EndDate],
			a._Creator [Creator],
			a.[spk_remark] [Remark],
			a._Status [Status],
			a.[spk_isnotifyoriginaloperator] [IsNotifyOriginalOperator],
			'{}' [ExtraProperties],
			'' [ConcurrencyStamp],
			a._CreationTime [CreationTime],
			a._Creator CreatorId,
			'' [LastModificationTime],
			'' [LastModifierId],
			0 [IsDeleted],
			'' [DeleterId],
			'' [DeletionTime],
			a.[spk_agentconfigurationid] [DataverseId],
			a._BpmId
		from spk_agentconfiguration a
		where not exists(select * from AgentConfig tt where tt._BpmId=a._BpmId);
	end
end
