apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: speaker-int-ingress
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"

spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - speaker-int-api-s.oneabbott.com
    secretName: tls-speaker-int-api-s-secret
  rules:
  - host: speaker-int-api-s.oneabbott.com
    http:
      paths:
      - path: /api/Misc/Health
        pathType: Prefix
        backend:
          service:
            name: corp-ca191-speakerapi-s
            port:
              number: 80
      - path: /api/EpdOnlineMeeting/(.*)
        pathType: Prefix
        backend:
          service:
            name: corp-ca191-speakerapi-s
            port:
              number: 80
      - path: /api/EpdPortal/(.*)
        pathType: Prefix
        backend:
          service:
            name: corp-ca191-speakerapi-s
            port:
              number: 80
      - path: /api/IntegrationDspot/(.*)
        pathType: Prefix
        backend:
          service:
            name: corp-ca191-speakerapi-s
            port:
              number: 80