﻿using Abbott.SpeakerPortal.Contracts.Report;
using System.Threading;
using Hangfire;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.BackgroundWorkers.Report
{
    /// <summary>
    /// 生成采购申请导出数据Job
    /// </summary>
    public class GeneratePRApplicationReportWorker : SpeakerPortalBackgroundWorkerBase
    {
        public GeneratePRApplicationReportWorker()
        {
            CronExpression = Cron.Daily(6);
        }

        [AutomaticRetry(Attempts = 0)]
        public override async Task DoWorkAsync(CancellationToken cancellationToken = default)
        {
            var result = await LazyServiceProvider.LazyGetService<IReportService>().GeneratePRApplicationReportAsync();
        }
    }
}
