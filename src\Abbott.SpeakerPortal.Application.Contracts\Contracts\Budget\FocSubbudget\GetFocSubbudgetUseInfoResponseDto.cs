﻿using Abbott.SpeakerPortal.Enums;
using MiniExcelLibs.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Reflection;
using System.Text;

namespace Abbott.SpeakerPortal.Contracts.Budget.FocSubbudget
{
    public class GetFocSubbudgetUseInfoResponseDto
    {
        /// <summary>
        /// 申请人姓名
        /// </summary>
        [ExcelColumnName("申请人")]
        public string ApplyUserName { get; set; }
        /// <summary>
        /// 申请日期
        /// </summary>
        [ExcelColumnName("申请日期")]
        public string ApplyTime { get; set; }
        /// <summary>
        /// 流程类型名称
        /// </summary>
        [ExcelColumnName("流程名称")]
        public string WorkflowTypeName { get; set; }
        /// <summary>
        /// 流程编号
        /// </summary>
        [ExcelColumnName("流程单号")]
        public string FlowNo { get; set; }
        /// <summary>
        /// 使用数量
        /// </summary>
        [ExcelColumnName("使用数量")]
        public int UseQty { get; set; }
        /// <summary>
        /// 流程状态
        /// </summary>
        [ExcelIgnore]
        public FOCStatus FlowStatus { get; set; }
        [ExcelColumnName("流程状态")]
        public string FlowStatusText => FlowStatus.GetType().GetField(FlowStatus.ToString()).GetCustomAttribute<DescriptionAttribute>().Description;
    }
}
