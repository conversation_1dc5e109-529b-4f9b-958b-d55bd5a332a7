﻿using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Integration.Veeva;
using Hangfire;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.DistributedLocking;

namespace Abbott.SpeakerPortal.BackgroundWorkers.Integration.Veeva
{
    /// <summary>
    /// 若不同讲者同时推了新建的医院（hco_dcr），Veeva只会返回一次处理结果
    /// </summary>
    public class VeevaHcoDcrLogUpdateWorker : SpeakerPortalBackgroundWorkerBase
    {
        private IScheduleJobLogService _jobLogService;
        private IInteVeevaService _inteVeevaService;
        public VeevaHcoDcrLogUpdateWorker(IServiceProvider serviceProvider)
        {
            _jobLogService = serviceProvider.GetService<IScheduleJobLogService>();
            _inteVeevaService = serviceProvider.GetService<IInteVeevaService>();
            //触发周期,每周六
            CronExpression = Cron.Weekly(DayOfWeek.Saturday);
        }

        public override async Task DoWorkAsync(CancellationToken cancellationToken = default)
        {
            //1、加锁执行 执行完自动释放锁
            await using (var handle = await LazyServiceProvider.LazyGetService<IAbpDistributedLock>().TryAcquireAsync(DistributedLock.DistributedLock_VeevaHcoDcrLogUpdate))
            {
                // 锁已被其他任务持有，跳过
                if (handle == null)
                    return;

                var log = _jobLogService.InitSyncLog("VeevaHcoDcrLogUpdateWorker");
                try
                {
                    //若不同讲者同时推了新建的医院（hco_dcr），Veeva只会返回一次处理结果
                    await _inteVeevaService.UpdateHcoDcrLogTask();
                }
                catch (Exception ex)
                {
                    log.IsSuccess = false;
                    log.Remark = ex.ToString();
                }
                finally
                {
                    _jobLogService.SyncLog(log);
                }
            }
        }
    }
}
