﻿using Abbott.SpeakerPortal.Contracts.Common.Approve;
using Abbott.SpeakerPortal.Contracts.Common.PowerApp;
using Abbott.SpeakerPortal.Contracts.ReturnAndExchange;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.EFlow.Return;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.IntermediateDB.Repositories;
using Abbott.SpeakerPortal.Utils;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using static Abbott.SpeakerPortal.Consts.EntitiesNameConsts;
using Abbott.SpeakerPortal.Extension;
using Volo.Abp.ObjectMapping;
using Abbott.SpeakerPortal.Contracts.Common.Attachment;
using Abbott.SpeakerPortal.Entities.EFlow.STicket;
using DocumentFormat.OpenXml.Office2010.Excel;
using Microsoft.AspNetCore.Mvc.ApplicationModels;
using System.ServiceModel;
using Senparc.Weixin.WxOpen.AdvancedAPIs.WxApp.WxAppJson;
using Abbott.SpeakerPortal.Contracts.Dataverse;
using Abbott.SpeakerPortal.Entities.EFlow.FOC;
using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.IntermediateDB.Models;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Abbott.SpeakerPortal.Contracts.Common;
using DocumentFormat.OpenXml.Bibliography;
using PdfSharp.Pdf.Content.Objects;
using System.Security.AccessControl;
using Abbott.SpeakerPortal.Contracts.FOC;
using Abbott.SpeakerPortal.Person;
using Volo.Abp.Validation;

namespace Abbott.SpeakerPortal.AppServices.ReturnAndExchange
{
    public partial class ReturnService : SpeakerPortalAppService, IReturnService
    {
        private readonly ILogger<ReturnService> _logger;
        private readonly IConfiguration _configuration;
        private readonly IServiceProvider _serviceProvider;
        private readonly string[] _clocs = { "60", "62" };
        public ReturnService(ILogger<ReturnService> logger, IServiceProvider serviceProvider)
        {
            this._serviceProvider = serviceProvider;
            this._logger = logger;
            _configuration = serviceProvider.GetService<IConfiguration>();
        }
        /// <summary>
        /// 创建申请单
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        [DisableValidation]
        public async Task<MessageResult> CreateReturnApplicationAsync<T, K>(T request, ReturnTypeEnum Type, bool isSubmit = false) where T : CreateReturnApplicationRequestDto<K> where K : CreateReturnApplicationDetailRequestDto
        {
            var returnReponsitory = LazyServiceProvider.LazyGetService<IReturnApplicationReponsitory>();
            var returnDetaiReponsitory = LazyServiceProvider.LazyGetService<IReturnApplicationDetailReponsitory>();
            var returnApplication = new ReturnApplication();
            returnApplication = ObjectMapper.Map<T, ReturnApplication>(request);
            var returnApplicationDetail = ObjectMapper.Map<List<K>, List<ReturnApplicationDetail>>(request.DetailItems);
            if (CurrentUser.Id.HasValue)
            {
                returnApplication.ApplyUserId = CurrentUser.Id.Value;
                returnApplication.ApplyUser = CurrentUser.Name;
            }
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            //成本中心

            if (isSubmit)
            {
                returnApplication.ApplyTime = DateTime.Now;
                returnApplication.Status = ReturnApplicationStatus.Approving;

            }
            else
            {
                returnApplication.Status = ReturnApplicationStatus.Draft;
            }
            await ConvertType(returnApplication);
            if (request.CostCenterId.HasValue)
            {
                var costcenters = await dataverseService.GetCostcentersAsync(request.CostCenterId.ToString());
                var costcenter = costcenters.FirstOrDefault();
                if (costcenter == null)
                    return MessageResult.FailureResult($"未找到Id为{request.CostCenterId}的成本中心数据");
                returnApplication.CostCenterID = costcenter.Id;
                returnApplication.CostCenter = costcenter.Name;
                returnApplication.CostCenterCode = costcenter.Code;
            }
            returnApplication.Attachment = request.Attachment?.Select(a => a.AttachmentId).JoinAsString(",");
            returnApplication.ApplicationType = Type;
            var insertReturn = await InsertAndGenerateSerialNoAsync(returnReponsitory, returnApplication, "T");
            //var insertReturn = await returnReponsitory.InsertAsync(returnApplication);
            returnApplicationDetail.ForEach(v => v.ReturnApplicationId = returnApplication.Id);
            await returnDetaiReponsitory.InsertManyAsync(returnApplicationDetail);
            return MessageResult.SuccessResult(new Tuple<ReturnApplication, List<ReturnApplicationDetail>>(returnApplication, returnApplicationDetail));
        }
        /// <summary>
        /// 修改申请单
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        [DisableValidation]
        public async Task<MessageResult> UpdateReturnApplicationAsync<T>(UpdateReturnApplicationRequestDto<T> request, ReturnTypeEnum Type, bool isSubmit = false) where T : CreateReturnApplicationDetailRequestDto
        {
            var ReturnReponsitory = LazyServiceProvider.LazyGetService<IReturnApplicationReponsitory>();
            if (!request.Id.HasValue) return MessageResult.FailureResult("单据Id不能为空");
            var returnApplication = await ReturnReponsitory.GetAsync(request.Id.Value);
            var returnDetaiReponsitory = LazyServiceProvider.LazyGetService<IReturnApplicationDetailReponsitory>();
            //详情
            var ReturnDetails = await returnDetaiReponsitory.GetListAsync(a => a.ReturnApplicationId == request.Id);

            ObjectMapper.Map(request, returnApplication);
            //过滤插入的数据
            var insertItems = request.DetailItems.Where(m => !m.Id.HasValue).ToList();
            //编辑的数据
            var updateItems = request.DetailItems.Where(m => m.Id.HasValue).ToList();
            var updateItemIds = updateItems.Select(s => s.Id).ToList();

            var InsertDetail = ObjectMapper.Map<List<T>, List<ReturnApplicationDetail>>(insertItems);
            InsertDetail.ForEach(v => v.ReturnApplicationId = request.Id.Value);
            if (CurrentUser.Id.HasValue)
            {
                returnApplication.ApplyUserId = CurrentUser.Id.Value;
                returnApplication.ApplyUser = CurrentUser.Name;
            }
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();

            if (isSubmit)
            {
                returnApplication.Status = ReturnApplicationStatus.Approving;
                returnApplication.ApplyTime = DateTime.Now;
            }
            else
            {
                returnApplication.Status = ReturnApplicationStatus.Draft;
            }
            await ConvertType(returnApplication);
            //成本中心
            if (request.CostCenterId.HasValue)
            {
                var costcenters = await dataverseService.GetCostcentersAsync(request.CostCenterId.ToString());
                var costcenter = costcenters.FirstOrDefault();
                if (costcenter == null)
                    return MessageResult.FailureResult($"未找到Id为{request.CostCenterId}的成本中心数据");
                returnApplication.CostCenterID = costcenter.Id;
                returnApplication.CostCenter = costcenter.Name;
                returnApplication.CostCenterCode = costcenter.Code;
            }
            returnApplication.Attachment = request.Attachment?.Select(a => a.AttachmentId).JoinAsString(",");
            returnApplication.ApplicationType = Type;
            //删除数据
            var deleteReturnDetail = ReturnDetails.Where(m => !updateItemIds.Contains(m.Id));
            await returnDetaiReponsitory.DeleteManyAsync(deleteReturnDetail);
            List<ReturnApplicationDetail> updateDetailItems = [];
            foreach (var item in updateItems)
            {
                var ReturnDetail = ReturnDetails.First(f => f.Id == item.Id);
                ObjectMapper.Map<T, ReturnApplicationDetail>(item, ReturnDetail);
                updateDetailItems.Add(ReturnDetail);

            }
            await returnDetaiReponsitory.InsertManyAsync(InsertDetail);
            await returnDetaiReponsitory.UpdateManyAsync(updateDetailItems);
            var insertReturn = await ReturnReponsitory.UpdateAsync(returnApplication, true);
            return MessageResult.SuccessResult(new Tuple<ReturnApplication, List<ReturnApplicationDetail>>(returnApplication, updateDetailItems));
        }
        /// <summary>
        /// 获取申请
        /// </summary>
        /// <param name="Id">The identifier.</param>
        /// <returns></returns>
        public async Task<MessageResult> GetReturnApplicationAsync(Guid Id)
        {
            var returnQuery = (await LazyServiceProvider.LazyGetService<IReturnApplicationReponsitory>().GetQueryableAsync()).AsNoTracking();
            var returnDetaiReponsitory = (await LazyServiceProvider.LazyGetService<IReturnApplicationDetailReponsitory>().GetQueryableAsync()).AsNoTracking();
            var returnApplication = await returnQuery.Where(a => a.Id == Id).GroupJoin(returnDetaiReponsitory, a => a.Id, b => b.ReturnApplicationId, (a, b) => new { header = a, details = b }).FirstOrDefaultAsync();
            if (returnApplication == null) return MessageResult.FailureResult("未查找到数据!");
            var response = ObjectMapper.Map<ReturnApplication, GetReturnApplicationResponseDto>(returnApplication.header);
            if (returnApplication.details.Any())
            {
                var details = ObjectMapper.Map<List<ReturnApplicationDetail>, List<GetReturnApplicationDetailResponseDto>>(returnApplication.details.ToList());
                response.DetailItems = details;
            }
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            //获取美刀货币配置
            var usdCurrencies = await dataverseService.GetCurrencyConfig(DictionaryType.CurrencyItems.USD);
            var usdCurrency = usdCurrencies.FirstOrDefault();
            if (usdCurrency == null)
            {
                return MessageResult.FailureResult("未找到美刀货币配置");
            }

            var subTotalAmount = response.DetailItems.Sum(a => a.AmountTax) ?? 0;
            decimal sumAmount = decimal.Round(subTotalAmount / (decimal)usdCurrency.PlanRate, 4);

            var focRemindAmt = (await dataverseService.GetDictionariesAsync(DictionaryType.FOCRemindAmount)).FirstOrDefault();
            if (focRemindAmt != null)
            {
                var remindAmt = Convert.ToDecimal(focRemindAmt.Name);
                response.IsExceedAmount = sumAmount > remindAmt;
            }


            //附件
            if (!string.IsNullOrEmpty(returnApplication.header.Attachment))
            {
                var attachmentIds = returnApplication.header.Attachment.Split(",", StringSplitOptions.RemoveEmptyEntries).Select(Guid.Parse).ToArray();
                response.Attachment = await LazyServiceProvider.LazyGetService<IAttachmentService>().GetAttachmentsAsync(attachmentIds);
            }
            return MessageResult.SuccessResult(response);
        }

        /// <summary>
        /// 提交审批
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> SubmitReturnApplicationAsync<T>(UpdateReturnApplicationRequestDto<T> request, ReturnTypeEnum Type) where T : CreateReturnApplicationDetailRequestDto
        {
            MessageResult result = MessageResult.FailureResult();
            if (request.Id.HasValue)
                result = await UpdateReturnApplicationAsync(request, Type, true);
            else
                result = await CreateReturnApplicationAsync<UpdateReturnApplicationRequestDto<T>, T>(request, Type, true);
            if (!result.Success)
            {
                result.Data = null;
                return result;
            }

            var data = result.Data as Tuple<ReturnApplication, List<ReturnApplicationDetail>>;
            var returnApplication = data.Item1;
            var details = await TransferNatureApprovalNumber(data.Item2, Type);
            var exemptType = EnumUtil.GetEnumIdValues<WorkflowTypeName>().ToDictionary(a => (WorkflowTypeName)a.Key, a => a.Value);
            var isOk = await LazyServiceProvider.LazyGetService<IApproveService>().InitiateApprovalAsync(new CreateApprovalDto
            {
                Name = exemptType[WorkflowTypeName.ReturnAndExchangeRequest],
                Department = returnApplication.ApplyUserDeptId.ToString(),
                BusinessFormId = returnApplication.Id.ToString(),
                BusinessFormNo = returnApplication.ApplicationCode,
                BusinessFormName = NameConsts.ReturnAndExchangeRequest,
                Submitter = CurrentUser.Id?.ToString(),
                OriginalApprovalId = returnApplication.ApplyUserId,
                WorkflowType = WorkflowTypeName.ReturnAndExchangeRequest,
                FormData = JsonSerializer.Serialize(new
                {
                    returnApplication.ApplicationCode,
                    StickteDtails = details.Select(a => new { a.TotalAmount, a.ApprovalNumber }),
                    IsCss = true
                })
            });
            if (!isOk)
                throw new Exception("提交审批失败");
            return MessageResult.SuccessResult();
        }
        /// <summary>
        /// 获取退换货列表
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="Type">The type.</param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetReturnApplicationListResponseDto>> GetReturnApplicationListAsync(GetReturnApplicationListRequestDto request, ReturnTypeEnum Type)
        {
            var returnReponsitory = LazyServiceProvider.LazyGetService<IReturnApplicationReponsitory>();
            var returnDetailReponsitory = LazyServiceProvider.LazyGetService<IReturnApplicationDetailReponsitory>();

            var returnQuery = (await returnReponsitory.GetQueryableAsync()).AsNoTracking();
            var detailQuery = (await returnDetailReponsitory.GetQueryableAsync()).AsNoTracking();
            //var productQuantityQuery = detailQuery.GroupBy(g => g.ReturnApplicationId, (key, group) => new { key, totalQuantity = group.Sum(s => s.ProductQuantity) });
            var query = returnQuery.Where(m => m.ApplicationType == Type && m.Status != ReturnApplicationStatus.Draft)
            .WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), m => m.ApplicationCode.Contains(request.ApplicationCode))
            .WhereIf(!string.IsNullOrEmpty(request.ApplyDeptName), m => m.ApplyUserDeptName.Contains(request.ApplyDeptName))
            .WhereIf(request.ApplicantId.HasValue, m => m.ApplyUserId == request.ApplicantId)
            .WhereIf(request.StartDate.HasValue, a => a.ApplyTime >= request.StartDate)
            .WhereIf(request.EndDate.HasValue, a => a.ApplyTime <= request.EndDate.Value.Date.AddDays(1))
            .WhereIf(request.Status.HasValue, a => a.Status == request.Status)
            .WhereIf(!string.IsNullOrEmpty(request.ClientCode), m => m.ClientCode.Contains(request.ClientCode))
            .WhereIf(!string.IsNullOrEmpty(request.ClientName), m => m.ClientName.Contains(request.ClientName))
            ;
            var personCenterService = LazyServiceProvider.LazyGetService<IPersonCenterService>();
            var commonService = LazyServiceProvider.LazyGetService<ICommonService>();
            var roleLevel = personCenterService.GetEFlowMyRoleLevel();

            //var roleLevel = rolesIds.MinBy(m => m.Key);
            switch (roleLevel)
            {
                case RoleLevel.Admin:
                    break;
                case RoleLevel.Manager:
                    {
                        var rolekyes = await personCenterService.GetIdsByRoleLevels([roleLevel]);
                        var buIds = rolekyes.GetValueOrDefault(RoleLevel.Manager)?.ToList() ?? [];
                        var depts = await commonService.GetChildrenOrgs(buIds.ToList());
                        var deptIds = depts.Select(s => s.Id).ToList();
                        deptIds.AddRange(buIds);
                        deptIds.Add(Guid.NewGuid());
                        query = query.Where(m => deptIds.Contains(m.ApplyUserDeptId) || m.ApplyUserId == CurrentUser.Id || m.TransfereeId == CurrentUser.Id);
                    }
                    break;
                case RoleLevel.Leader:
                    {
                        var orgIds = (await commonService.GetAllChildrenOrgs(CurrentUser.Id.Value)).ToHashSet();
                        var depts = await commonService.GetChildrenOrgs(orgIds.ToList());
                        var deptIds = depts.Select(s => s.Id).ToList();
                        deptIds.AddRange(orgIds);
                        deptIds.Add(Guid.NewGuid());
                        query = query.Where(m => deptIds.Contains(m.ApplyUserDeptId));
                    }
                    break;
                default:
                    query = query.Where(m => m.ApplyUserId == CurrentUser.Id || m.TransfereeId == CurrentUser.Id);
                    break;
            }
            var dataQuery = query.Select(s => new GetReturnApplicationListResponseDto
            {
                Id = s.Id,
                ApplyUser = s.ApplyUser,
                ApplicationCode = s.ApplicationCode,
                ApplyUserDeptId = s.ApplyUserDeptId,
                ApplyUserDeptName = s.ApplyUserDeptName,
                ClientCode = s.ClientCode,
                ClientName = s.ClientName,
                Status = s.Status,
                ApplyUserId = s.ApplyUserId,
                ApplyTime = s.ApplyTime,
                ProductQuantity = s.TotalProductQuantity,
            });
            var count = await dataQuery.CountAsync();
            var datas = await dataQuery.OrderByDescending(o => o.ApplyTime).PagingIf(request).ToListAsync();
            return new PagedResultDto<GetReturnApplicationListResponseDto>(count, datas);
        }
        /// <summary>
        /// Deletes the Return application asynchronous.
        /// </summary>
        /// <param name="Id">The identifier.</param>
        /// <returns></returns>
        public async Task<MessageResult> DeleteReturnApplicationAsync(Guid Id)
        {
            var ReturnReponsitory = LazyServiceProvider.LazyGetService<IReturnApplicationReponsitory>();
            var ReturnDetaiReponsitory = LazyServiceProvider.LazyGetService<IReturnApplicationDetailReponsitory>();
            await ReturnReponsitory.DeleteAsync(Id);
            await ReturnDetaiReponsitory.DeleteAsync(s => s.ReturnApplicationId == Id);
            return MessageResult.SuccessResult("操作成功");
        }
        /// <summary>
        ///获取各户信息
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<PagedResultDto<ClientResponseDto>> GetClientInfoAsync(ClientRequestDto request)
        {
            var rcmQuery = (await LazyServiceProvider.LazyGetService<IIntermediateRcmRepository>().GetQueryableAsync()).AsNoTracking();

            var query = rcmQuery.Select(s => new { s.Cccus, s.Cnme, s.Ccust, s.Cmid, s.Ccmpy, s.Cloc })
                .Where(m => m.Cmid == "CM" && m.Ccmpy == 20 && _clocs.Contains(m.Cloc)).WhereIf(!string.IsNullOrEmpty(request.ClientName), m => m.Cnme.Contains(request.ClientName))
                 .WhereIf(!string.IsNullOrEmpty(request.ClientCode), m => m.Ccust.ToString().Contains(request.ClientCode))
                 .WhereIf(!string.IsNullOrEmpty(request.CompanyCode), m => m.Cccus.ToString().Contains(request.CompanyCode));
            var count = await query.CountAsync();
            var datas = await query.PagingIf(request).Select(s => new ClientResponseDto { ClientCode = s.Ccust.ToString(), ClientName = s.Cnme, CompanyCode = s.Cccus.ToString() }).ToListAsync();
            return new PagedResultDto<ClientResponseDto>(count, datas);
        }
        /// <summary>
        /// Gets the product information.
        /// </summary>
        /// <param name="clientCode">The client code.</param>
        /// <param name="companyCode">The company code.</param>
        /// <returns></returns>
        public async Task<PagedResultDto<ProductInfoResponseDto>> GetProductInfoAsync(ProductInfoRequestDto request)
        {
            var espQuery = (await LazyServiceProvider.LazyGetService<IIntermediateEspRepository>().GetQueryableAsync()).AsNoTracking();
            var eixQuery = (await LazyServiceProvider.LazyGetService<IIntermediateEixRepository>().GetQueryableAsync()).AsNoTracking().Where(m => m.Ixid == "IX" && m.Ixcmpy == 20);
            //var pmfimQuery = (await LazyServiceProvider.LazyGetService<IIntermediatePmfimRepository>().GetQueryableAsync()).AsNoTracking().Where(m => m.Irid == "IM");

            var prodd = (await LazyServiceProvider.LazyGetService<IProductDetailsRepository>().GetQueryableAsync()).AsNoTracking();


            // 获取当前时间
            DateTime currentDate = DateTime.Now;

            // 转化为Decimal格式20250410
            var dateDecimal = decimal.Parse(currentDate.ToString("yyyyMMdd"));

            var query1 = espQuery.Where(m => m.Spid == "SP" && m.Pmeth == "A" && m.Prkey != null && m.Psdte <= dateDecimal && m.Psedt >= dateDecimal).Select(s => new
            {
                cus = s.Prkey.Substring(s.Prkey.Length - 6),
                prod = s.Prkey.Substring(0, 15).Replace(" ", ""),
                amount = s.Pfct1,
                Psdte = s.Psdte,
                Psedt = s.Psedt
            });
            var query = eixQuery.Where(w => w.Ixcust.ToString() == request.ClientCode || w.Ixcust.ToString() == request.CompanyCode).Select(s => new { cus = s.Ixcust.ToString(), prod = s.Ixprod, prodes = s.Ixdesc, s.Ixitem });
            var newquery = query1.Join(query, a => new { a.cus, a.prod }, b => new { b.cus, b.prod }, (a, b) => new ProductInfoResponseDto
            {
                ClientProductCode = b.Ixitem,
                ProductSCode = a.prod,
                ProductName = b.prodes,
                Amount = a.amount,
                Psdte = a.Psdte,
                Psedt = a.Psedt,
                MinDate = a.Psedt - a.Psdte,
            })
            //.Where(m => m.Psdte >= dateDecimal && m.Psedt <= dateDecimal)
            .WhereIf(!string.IsNullOrEmpty(request.ProductSCode), m => m.ProductSCode.Contains(request.ProductSCode))
            .WhereIf(!string.IsNullOrEmpty(request.ProductName), m => m.ProductName.Contains(request.ProductName))
            .WhereIf(!string.IsNullOrEmpty(request.ClientProductCode), m => m.ClientProductCode.Contains(request.ClientProductCode))
            ;

            var q = newquery.AsEnumerable().GroupBy(g => new { g.ClientProductCode, g.ProductSCode }).Select(s => s.MinBy(s => s.MinDate));
            var count = q.Count();
            var datas = q.PagingIf(request).ToList();
            var prodsCodes = datas.Select(s => s.ProductSCode).ToHashSet();
            var p = prodd.Where(m => prodsCodes.Contains(m.ProductSCode)).AsEnumerable();
            datas.ForEach(v =>
            {
                var salesPackageSize = p.FirstOrDefault(f => f.ProductSCode == v.ProductSCode)?.SalesPackageSize;
                v.StandardBoxFactor = string.IsNullOrEmpty(salesPackageSize) ? null : Convert.ToInt32(salesPackageSize);
            });

            return new PagedResultDto<ProductInfoResponseDto>(count, datas);
        }
        /// <summary>
        /// 获取退换货草稿列表
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="Type">The type.</param>
        /// <returns></returns>
        public async Task<PagedResultDto<GetReturnApplicationDraftListResponseDto>> GetReturnApplicationDraftListAsync(GetReturnApplicationDraftListRequestDto request, ReturnTypeEnum Type)
        {
            var returnReponsitory = LazyServiceProvider.LazyGetService<IReturnApplicationReponsitory>();
            var returnDetailReponsitory = LazyServiceProvider.LazyGetService<IReturnApplicationDetailReponsitory>();

            var returnQuery = (await returnReponsitory.GetQueryableAsync()).AsNoTracking();
            var detailQuery = (await returnDetailReponsitory.GetQueryableAsync()).AsNoTracking();
            //var productQuantityQuery = detailQuery.GroupBy(g => g.ReturnApplicationId, (key, group) => new { key, totalQuantity = group.Sum(s => s.ProductQuantity) });
            var query = returnQuery.Where(m => m.ApplicationType == Type && m.Status == ReturnApplicationStatus.Draft && (m.ApplyUserId == CurrentUser.Id || m.TransfereeId == CurrentUser.Id))
            .WhereIf(!string.IsNullOrEmpty(request.ApplicationCode), m => m.ApplicationCode.Contains(request.ApplicationCode))
            .WhereIf(!string.IsNullOrEmpty(request.ApplyDeptName), m => m.ApplyUserDeptName.Contains(request.ApplyDeptName))
            .WhereIf(request.ApplicantId.HasValue, m => m.ApplyUserId == request.ApplicantId)
            .WhereIf(request.StartDate.HasValue, a => a.CreationTime >= request.StartDate)
            .WhereIf(request.EndDate.HasValue, a => a.CreationTime <= request.EndDate.Value.Date.AddDays(1))
            .WhereIf(!string.IsNullOrEmpty(request.ClientCode), m => m.ClientCode.Contains(request.ClientCode))
            .WhereIf(!string.IsNullOrEmpty(request.ClientName), m => m.ClientName.Contains(request.ClientName))
            ;
            var personCenterService = LazyServiceProvider.LazyGetService<IPersonCenterService>();
            var commonService = LazyServiceProvider.LazyGetService<ICommonService>();
            var roleLevel = personCenterService.GetEFlowMyRoleLevel();

            var dataQuery = query.Select(s => new GetReturnApplicationDraftListResponseDto
            {
                Id = s.Id,
                ApplyUser = s.ApplyUser,
                ApplicationCode = s.ApplicationCode,
                ApplyUserDeptId = s.ApplyUserDeptId,
                ApplyUserDeptName = s.ApplyUserDeptName,
                ClientCode = s.ClientCode,
                ClientName = s.ClientName,
                Status = s.Status,
                ApplyUserId = s.ApplyUserId,
                SaveTime = s.CreationTime,
                ProductQuantity = s.TotalProductQuantity,
            });
            var count = await dataQuery.CountAsync();
            var datas = await dataQuery.OrderByDescending(o => o.SaveTime).PagingIf(request).ToListAsync();
            return new PagedResultDto<GetReturnApplicationDraftListResponseDto>(count, datas);
        }
        /// <summary>
        /// 转换费用性质的Approval Number
        /// </summary>
        /// <param name="newPrDetails"></param>
        /// <returns></returns>
        async Task<IEnumerable<(decimal TotalAmount, string ApprovalNumber)>> TransferNatureApprovalNumber(List<ReturnApplicationDetail> returnDetails, ReturnTypeEnum type)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            //获取美刀货币配置
            var usdCurrencies = await dataverseService.GetCurrencyConfig(DictionaryType.CurrencyItems.USD);
            var usdCurrency = usdCurrencies.First();
            //var costNatures = await dataverseService.GetCostNatureAsync(natureId.ToString());
            var focApplicationDetails = returnDetails.Select(a =>
            {
                //var costNature = costNatures.FirstOrDefault();

                return
                (
                    decimal.Round(a.AmountTax / (decimal)usdCurrency.PlanRate, 4),
                    type == ReturnTypeEnum.Return ? "50" : "60"
                );

            }).ToArray();

            return focApplicationDetails;
        }
        /// <summary>
        /// 转化类型
        /// </summary>
        /// <param name="returnApplication">The return application.</param>
        /// <returns></returns>
        public async Task ConvertType(ReturnApplication returnApplication)
        {
            if (returnApplication.Status == ReturnApplicationStatus.Draft) return;
            var rcmQuery = (await LazyServiceProvider.LazyGetService<IIntermediateRcmRepository>().GetQueryableAsync()).AsNoTracking();
            var rem = rcmQuery.Where(m => m.Cmid == "CM" && m.Ccmpy == 20 && _clocs.Contains(m.Cloc)).Select(s => new
            {
                ClientName = s.Cnme,
                CompanyCode = s.Cccus,
                Type = s.Ctype,
                CWHSE = s.Cwhse,
                ClientCode = s.Ccust,
            }).FirstOrDefault(f => f.ClientCode.ToString() == returnApplication.ClientCode && f.CompanyCode.ToString() == returnApplication.CompanyCode);
            if (rem != null)
            {
                var types = new string[] { "GR-P", "GRBP", "RTEP" };
                var sort = new string[] { "BJ", "CD", "ZS", "AH" };
                if (rem.ClientCode < 900000 && rem.Type == "DS-P" && sort.Contains(rem.CWHSE))
                {
                    returnApplication.ConvertedCustType = ConvertedCustType.ADWSL;
                }
                else if (types.Contains(rem.Type))
                {
                    returnApplication.ConvertedCustType = ConvertedCustType.ADKA;
                }
                else returnApplication.ConvertedCustType = ConvertedCustType.Distributor;
            }
        }
        /// <summary>
        /// Pushes the soi return asnyc.
        /// </summary>
        /// <param name="Id">The identifier.</param>
        /// <returns></returns>
        public async Task<string> PushSoiReturnAsnyc(Guid Id)
        {
            var returnReponsitory = LazyServiceProvider.LazyGetService<IReturnApplicationReponsitory>();
            var returnDetailReponsitory = LazyServiceProvider.LazyGetService<IReturnApplicationDetailReponsitory>();

            var returnQuery = (await returnReponsitory.GetQueryableAsync()).AsNoTracking();
            var returnDetailQuery = (await returnDetailReponsitory.GetQueryableAsync()).AsNoTracking();

            var data = await returnQuery.GroupJoin(returnDetailQuery, a => a.Id, b => b.ReturnApplicationId, (a, b) => new { main = a, details = b }).FirstAsync(f => f.main.Id == Id);
            var returnApplication = data.main;
            var details = data.details;

            PushSOIReturnResponseDto response = new()
            {
                //GrPostUserId = returnApplication.ApplicationCode,
                GrPostUserName = returnApplication.ApplyUser,
                GrPostDate = returnApplication.ApplyTime?.ToString("yyyy-MM-dd"),
                GrFormNo = returnApplication.ApplicationCode,
                CustomerType = returnApplication.ConvertedCustType.GetDescription(),
                CorporateNum = returnApplication.CompanyCode,
                CustomerCode = returnApplication.ClientCode,
                CustomerName = returnApplication.ClientName,
                CustomerRTNo = returnApplication.OrderNumber,
                CeasonFlg = returnApplication.Reason.GetDescription(),
                CeasonDetails = returnApplication.Explanation,
                IsPhysical = returnApplication.IsPhysical == true ? "1" : "2",
                TtlCartons = returnApplication.TotalProductQuantity.ToString(),
                TtlAmt = returnApplication.TotalReturnAmount.ToString(),
                ReturnOrChange = returnApplication.PaymentMethod.GetDescription(),
            };
            foreach (var item in details)
            {
                var d = new Details
                {
                    ItemRow = item.RowId.ToString(),
                    ProductCode = item.ProductSCode,
                    ProductDesc = item.ProductName,
                    BatchNum = item.BatchNumber,
                    //ExpDate = item.ShelfLife.ToString(),
                    Qty = item.ProductQuantity.ToString(),
                    ChgRate = item.StandardBoxFactor.ToString(),
                    Amount = item.AmountTax.ToString(),
                    Remarks = item.ProductQualityDes
                };
                response.Details.Add(d);
            }
            //var _httpRequestService = _serviceProvider.GetService<IHttpRequestService>();
            //var insightKey = _configuration.GetValue<string>("ApplicationInsightKey");
            //var appkey = _configuration["Integrations:SOI:AppKey"];
            //var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            //string encryptText = $"appkey={appkey}method=ReciveGRDatatimestamp={timestamp}";
            //var Sign = encryptText.EncryptSHA3();
            //var token = await _httpRequestService.GetSOITokenAsync();
            //var requestData = new
            //{
            //    appkey = appkey,
            //    accessToken = token,
            //    timestamp = timestamp,
            //    method = "ReciveGRData",
            //    Sign = Sign,
            //    data = response
            //};
            var json = JsonSerializer.Serialize(response);
            return json;
            //var result = await _httpRequestService.HttpPostAsync<MessageResult>(json, $"{_configuration["Integrations:SOI:BaseUrl"]}/SOIWebAPI/api/GR/ReciveGRData");
            //if (!result.Success)
            //{
            //    _logger.LogError("调用SOI接口报错：" + result.Message);
            //    throw new Exception("调用SOI接口报错:" + result.Message);
            //}
            //return MessageResult.SuccessResult();
        }
        /// <summary>
        /// 检查单金额是否大于限定值
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> CheckApplicationAmountAsync<T>(UpdateReturnApplicationRequestDto<T> request) where T : CreateReturnApplicationDetailRequestDto
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            //获取美刀货币配置
            var usdCurrencies = await dataverseService.GetCurrencyConfig(DictionaryType.CurrencyItems.USD);
            var usdCurrency = usdCurrencies.FirstOrDefault();
            if (usdCurrency == null)
            {
                return MessageResult.FailureResult("未找到美刀货币配置");
            }

            var subTotalAmount = request.DetailItems.Sum(a => a.AmountTax);
            decimal sumAmount = decimal.Round(subTotalAmount.Value / (decimal)usdCurrency.PlanRate, 4);

            var focRemindAmt = (await dataverseService.GetDictionariesAsync(DictionaryType.FOCRemindAmount)).FirstOrDefault();
            if (focRemindAmt != null)
            {
                var remindAmt = Convert.ToDecimal(focRemindAmt.Name);
                bool isExceedAmount = false;
                if (sumAmount >= remindAmt)
                {
                    isExceedAmount = true;
                }
                var checkResult = new FocApplicationAmountCheckResponseDto
                {
                    IsExceedAmount = isExceedAmount,
                    SumAmount = sumAmount,
                };
                return MessageResult.SuccessResult(checkResult);
            }
            else
            {
                return MessageResult.FailureResult($"未设置提醒金额");
            }
        }
    }
}
