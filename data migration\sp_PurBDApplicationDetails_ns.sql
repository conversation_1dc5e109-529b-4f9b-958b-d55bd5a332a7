CREATE PROCEDURE dbo.sp_PurBDApplicationDetails_ns
AS 
BEGIN
	/*
 * PRDetailId无法关联
 */
select  
a.Id,
UPPER(c.Id) BDApplicationId,
a.PRDetailId,
a.Content,
a.Quantity,
a.Unit,
a.TotalAmount,
a.ApplicationCode,
a.Row<PERSON>o,
a.ExtraProperties,
a.ConcurrencyStamp,
c.CreationTime,
UPPER(ss.spk_NexBPMCode) as CreatorId,
a.LastModificationTime,
a.LastModifierId,
a.IsDeleted,
a.DeleterId,
a.DeletionTime,
a.UnitPrice
into #PurBDApplicationDetails
from PLATFORM_ABBOTT_Stg.dbo.PurBDApplicationDetails_tmp a 
left join PLATFORM_ABBOTT_Stg.dbo.PurBDApplications_tmp c
on a.BDApplicationId=c.ProcInstId
left join PLATFORM_ABBOTT_Stg.dbo.spk_staffmasterdata ss 
on c.CreatorId =ss.bpm_id 
left join PLATFORM_ABBOTT_Stg.dbo.PurPRApplications_tmp ppt 
on a.BDApplicationId =ppt.ProcInstId 


IF OBJECT_ID(N'PLATFORM_ABBOTT_Stg.dbo.PurBDApplicationDetails', N'U') IS NOT NULL
BEGIN
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
    drop table PLATFORM_ABBOTT_Stg.dbo.PurBDApplicationDetails
    select *
    into PLATFORM_ABBOTT_Stg.dbo.PurBDApplicationDetails from #PurBDApplicationDetails
	END
	ELSE
	BEGIN
    --落成实体表
    select *
    into PLATFORM_ABBOTT_Stg.dbo.PurBDApplicationDetails from #PurBDApplicationDetails
	  PRINT(N' 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END


END;
