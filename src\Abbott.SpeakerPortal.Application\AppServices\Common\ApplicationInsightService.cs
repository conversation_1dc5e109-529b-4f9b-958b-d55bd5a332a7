﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Abbott.SpeakerPortal.Contracts.Common;

using Microsoft.ApplicationInsights;
using Abbott.SpeakerPortal.Extension;
using Microsoft.Extensions.Configuration;
using Abbott.SpeakerPortal.Utils;

namespace Abbott.SpeakerPortal.AppServices.Common
{
    /// <summary>
    /// Application Insight日志记录服务
    /// </summary>
    public class ApplicationInsightService : SpeakerPortalAppService, IApplicationInsightService
    {
        TelemetryClient _telemetryClient;
        IConfiguration _configuration;

        public ApplicationInsightService(TelemetryClient telemetryClient, IConfiguration configuration)
        {
            _telemetryClient = telemetryClient;
            _configuration = configuration;
        }

        /// <summary>
        /// 记录加密Event
        /// </summary>
        /// <param name="category"></param>
        /// <param name="dict"></param>
        public void TrackEncryptEvent(string category, IDictionary<string, string> dict)
        {
            var key = _configuration.GetValue<string>("ApplicationInsightKey");
            foreach (var item in dict)
            {
                dict[item.Key] = AesHelper.Encryption(item.Value, key);
            }

            _telemetryClient.TrackEvent(category, dict);
            _telemetryClient.Flush();
        }

        /// <summary>
        /// 记录Event
        /// </summary>
        /// <param name="category"></param>
        /// <param name="dict"></param>
        public void TrackEvent(string category, IDictionary<string, string> dict)
        {
            _telemetryClient.TrackEvent(category, dict);
            _telemetryClient.Flush();
        }

        /// <summary>
        /// 记录加密Trace
        /// </summary>
        /// <param name="message"></param>
        /// <param name="dict"></param>
        public void TrackEncryptTrace(string message, IDictionary<string, string> dict)
        {
            var key = _configuration.GetValue<string>("ApplicationInsightKey");
            foreach (var item in dict)
            {
                dict[item.Key] = AesHelper.Encryption(item.Value, key);
            }

            _telemetryClient.TrackTrace(message, dict);
            _telemetryClient.Flush();
        }

        /// <summary>
        /// 记录Trace
        /// </summary>
        /// <param name="message"></param>
        /// <param name="dict"></param>
        public void TrackTrace(string message, IDictionary<string, string> dict)
        {
            _telemetryClient.TrackTrace(message, dict);
            _telemetryClient.Flush();
        }
    }
}
