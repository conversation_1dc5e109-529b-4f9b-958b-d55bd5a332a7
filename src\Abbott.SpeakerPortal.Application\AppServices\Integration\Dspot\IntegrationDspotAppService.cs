﻿using Abbott.SpeakerPortal.AppServices.Common;
using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.Common;
using Abbott.SpeakerPortal.Contracts.Dataverse;
using Abbott.SpeakerPortal.Contracts.Integration.Dspot;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Dataverse.Contracts;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.Budget;
using Abbott.SpeakerPortal.Entities.Integration.Dspot;
using Abbott.SpeakerPortal.Entities.Purchase.PurGRApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPOApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Enums.Integration;
using Abbott.SpeakerPortal.Extension;
using Abbott.SpeakerPortal.Utils;
using Flurl.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Query;
using Senparc.CO2NET.Extensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Linq.Expressions;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using static Abbott.SpeakerPortal.Enums.Purchase;

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.OEC.SpeakerLimit;
using Abbott.SpeakerPortal.Contracts.Purchase.PurThirdPartySpeaker;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.OECPSAComPSALimits;
using Abbott.SpeakerPortal.Entities.OECPSASpeakerLimitExtras;
using Abbott.SpeakerPortal.Entities.OECPSASpeakerLimitUseHistorys;
using Abbott.SpeakerPortal.Entities.Purchase.PurGRApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurThirdPartySpeaker;
using Abbott.SpeakerPortal.Entities.VendorPersonals;
using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.OEC.SpeakerLimit;
using Abbott.SpeakerPortal.Utils;

using ClosedXML.Excel;

using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

using static Abbott.SpeakerPortal.Enums.Purchase;
using MigraDoc.DocumentObjectModel.Tables;
using Abbott.SpeakerPortal.Entities.OEC.OECIntercepts;
using Senparc.Weixin.WxOpen.Entities;
using static Abbott.SpeakerPortal.Enums.PurPAStatus;
using Abbott.SpeakerPortal.User;
using DocumentFormat.OpenXml.ExtendedProperties;
using System.Globalization;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Volo.Abp.SettingManagement;
using Volo.Abp.EntityFrameworkCore;
using Volo.Abp.Uow;
using DocumentFormat.OpenXml.Spreadsheet;
using Org.BouncyCastle.Crypto;

namespace Abbott.SpeakerPortal.AppServices.Integration.Dspot
{
    public class IntegrationDspotAppService : SpeakerPortalAppService, IIntegrationDspotAppService
    {
        /// <summary>
        /// 在查询全流程报表时，要排除这个供应商名
        /// </summary>
        private const string EXCLUSIVE_GR_VENDORNAME = "上海市浦东新区税务局外高桥保";

        private readonly IServiceScopeFactory _scopeFactory;

        /// <summary>
        /// The service provider
        /// </summary>
        private readonly IServiceProvider _serviceProvider;

        /// <summary>
        /// The logger
        /// </summary>
        private readonly ILogger<IntegrationDspotAppService> _logger;

        /// <summary>
        /// The Host service provider
        /// </summary>
        private readonly IServiceProvider _hostServiceProvider;

        /// <summary>
        /// The configuration
        /// </summary>
        private readonly IConfiguration _configuration;

        private readonly object _lockObject = new object();

        private List<InteTaskPushStatus> _taskPushStatusList = new List<InteTaskPushStatus>();

        private readonly ICommonService _commonService;

        public IntegrationDspotAppService(IServiceScopeFactory scopeFactory, IServiceProvider serviceProvider)
        {
            _scopeFactory = scopeFactory;
            _serviceProvider = serviceProvider;
            _logger = serviceProvider.GetService<ILogger<IntegrationDspotAppService>>();
            _configuration = serviceProvider.GetService<IConfiguration>();
            _commonService = serviceProvider.GetService<ICommonService>();
        }

        #region 全流程报表回调

        public async Task<MessageResult> ProcessNotify(DspotWholeNotifyDto notify)
        {
            //		○ 当收到一个通知，即某一条PR的处理回调，根据ID定位是哪个子任务
            if (string.IsNullOrWhiteSpace(notify?.Id) || !Guid.TryParse(notify.Id, out Guid subId))
            {
                return MessageResult.FailureResult();
            }

            try
            {
                //当Id找不到WholeTaskSub时，直接返回
                var wholeTaskSubRepository = LazyServiceProvider.LazyGetService<IInteDspotWholeTaskSubRepository>();
                var wholeTaskSubQuerable = await wholeTaskSubRepository.GetQueryableAsync();
                var wholeTaskSub = wholeTaskSubQuerable.FirstOrDefault(a => a.Id == subId);
                if (wholeTaskSub == null)
                {
                    return MessageResult.FailureResult();
                }

                //		○ 保存接收的报文，并根据报文修改字段【处理状态】
                wholeTaskSub.LastNotifyTime = DateTime.Now;
                wholeTaskSub.LastNotifyJson = JsonSerializer.Serialize(notify);
                wholeTaskSub.ProcessStatus = notify.Code == 200 ? InteTaskProcessStatus.Processed : InteTaskProcessStatus.ProcessFaild;
                await wholeTaskSubRepository.UpdateAsync(wholeTaskSub, true);
                /*
                //			§ Tag2：统计所属主任务的处理状态，根据所有子任务的处理状态（任何一条处理失败即处理失败；否则，全已处理即已处理；否则，状态不变即Init）
                var subProcessStatus = wholeTaskSubQuerable.Where(a => a.WholeTaskId == wholeTaskSub.WholeTaskId)
                    .Select(a => a.ProcessStatus).Distinct().ToList();
                if (subProcessStatus?.Any() != true)
                {
                    _logger.LogInformation($"ProcessNotify() Return by: subProcessStatus?.Any() != true");
                    return MessageResult.FailureResult();
                }

                //主任务不存在，返回
                var wholeTaskRepos = LazyServiceProvider.LazyGetService<IInteDspotWholeTaskRepository>();
                var wholeTaskQuerable = await wholeTaskRepos.GetQueryableAsync();
                var wholeTask = wholeTaskQuerable.FirstOrDefault(a => a.Id == wholeTaskSub.WholeTaskId);
                if (wholeTask == null)
                {
                    _logger.LogInformation($"ProcessNotify() Return by: wholeTask == null");
                    return MessageResult.FailureResult();
                }

                var newProcessStatus = InteTaskProcessStatus.Init;
                //修改主任务ProcessStatus
                if (subProcessStatus.Contains(InteTaskProcessStatus.ProcessFaild))
                {
                    newProcessStatus = InteTaskProcessStatus.ProcessFaild;
                }
                else if (subProcessStatus.Count == 1 && subProcessStatus[0] == InteTaskProcessStatus.Processed)
                {
                    newProcessStatus = InteTaskProcessStatus.Processed;
                }

                //状态不变时不修改数据
                if (newProcessStatus != InteTaskProcessStatus.Init)
                {
                    wholeTask.ProcessStatus = newProcessStatus;
                    await wholeTaskRepos.UpdateAsync(wholeTask, true);
                }
                */
                return MessageResult.SuccessResult();
            }
            catch (Exception ex)
            {
                _logger.LogError($"ProcessNotify() Exception: {ex}");
                return MessageResult.FailureResult(ex.Message);
            }
        }

        #endregion 全流程报表回调

        #region 二要素验证

        public async Task<MessageResult> TwoElementsValidate(TwoElementsRequestDataDto request)
        {
            //todo：第一次上线暂时不对接Dspot，这里直接返回，防止报错
            return MessageResult.SuccessResult();

            if (string.IsNullOrWhiteSpace(request?.Name) || string.IsNullOrWhiteSpace(request?.Phone))
            {
                return MessageResult.FailureResult("param is null");
            }
            var log = new SetOperationLogRequestDto();

            try
            {
                var headers = GetTwoElementsHeaders();
                //Call DSPOT的二要素验证接口
                var url = _configuration["Integrations:DSpot:TwoElementsUrl"];
                var requestParams = new TwoElementsRequestDto { Data = request };
                log = _commonService.InitOperationLog("DSPOT", "二要素验证", url + "|" + JsonSerializer.Serialize(requestParams));

                var response = await url.WithHeaderJson(headers).PostJsonAsync(requestParams);
                string responseData = await response.GetStringAsync();
                _commonService.LogResponse(log, responseData);

                var resObj = JsonSerializer.Deserialize<TwoElementsResponseDto>(responseData);
                return resObj.Code == 200 ? MessageResult.SuccessResult(resObj.Data) : MessageResult.FailureResult(resObj.Msg);
            }
            catch (Exception ex)
            {
                _commonService.LogResponse(log, ex.ToString(), false);
                _logger.LogError($"TwoElementsValidate() Exception: {ex}");
                return MessageResult.FailureResult(ex.Message);
            }
        }

        public async Task<MessageResult> TwoElementsValidateBatch(List<TwoElementsRequestDataDto> request)
        {
            if (request?.Any() != true)
            {
                return MessageResult.FailureResult("param is null");
            }

            if (request.Any(a => string.IsNullOrWhiteSpace(a?.Name) || string.IsNullOrWhiteSpace(a?.Phone)))
            {
                return MessageResult.FailureResult("some fields are empty");
            }

            try
            {
                var headers = GetTwoElementsHeaders();
                //Call DSPOT的二要素验证接口-批量
                var url = _configuration["Integrations:DSpot:TwoElementsBatchUrl"];
                var response = await url.WithHeaderJson(headers).PostJsonAsync(new TwoElementsRequestBatchDto { Data = request });
                string responseData = await response.GetStringAsync();
                var resObj = JsonSerializer.Deserialize<TwoElementsResponseBatchDto>(responseData);
                return resObj.Code == 200 ? MessageResult.SuccessResult(resObj.Data) : MessageResult.FailureResult(resObj.Msg);
            }
            catch (Exception ex)
            {
                _logger.LogError($"TwoElementsValidateBatch() Exception: {ex}");
                return MessageResult.FailureResult(ex.Message);
            }
        }

        private Dictionary<string, string> GetTwoElementsHeaders()
        {
            var appKey = _configuration["Integrations:DSpot:TwoElementsAppKey"];
            var appSecret = _configuration["Integrations:DSpot:TwoElementsAppSecret"];
            var random = RandomHelper.GetEncryptKeyNum(10);
            var timeStamp = DatetimeHelper.GetTimeStamp13();
            var signature = Sha256Helper.Hash(random + timeStamp + appSecret);
            //组装Map，转化为Token使用
            JsonObject jObj = new JsonObject();
            /*jObj.Add("AppKey", appKey);
            jObj.Add("Random", random);
            jObj.Add("TimeStamp", timeStamp);
            jObj.Add("Signature", signature);*/
            //必须按照以下顺序添加，因为对方是这个顺序，对方Java代码也是之前的添加排序，不知为何对方jObj.ToJsonString()时会变成以下顺序
            jObj.Add("Random", random);
            jObj.Add("AppKey", appKey);
            jObj.Add("Signature", signature);
            jObj.Add("TimeStamp", timeStamp);
            var token = $"Bearer {jObj.ToJsonString().ToBase64()}";

            ////Mock文档中的示例：
            //random = "3953108592";
            //timeStamp = "1540884793000";
            //token = "Bearer eyJBcHBLZXkiOiJGNDlBMzhGNTBDMjk0RUMzQjJFMkJBNDlDRUIzQTIwMyIsIlJhbmRvbSI6IjE1Mjg5MjEzMTYiLCJUaW1lU3RhbXAiOjE2Mzk1NDYwNDkyMzcsIlNpZ25hdHVyZSI6IjI0Q0YyMjEyQjJGNjVFQUQ1QzVGOUVCMTE4MDgxMjcyMDc0RjdGODUyN0I3MDQzRkQ0RjJBNjU0MDgwMzJCNUYifQ==";

            var headers = new Dictionary<string, string>
                {
                    { "AppKey", appKey },
                    { "Random", random },
                    { "TimeStamp", timeStamp },
                    { "Token", token },
                };

            return headers;
        }

        #endregion 二要素验证

        #region 全流程报表推送
        //[UnitOfWork(IsDisabled = true)]
        public async Task<string> SyncWholeProcessReport()
        {
            var now = DateTime.Now;
            var startDt = now.AddDays(-1).Date;
            var endDt = now.Date;

#if DEBUG
            //startDt = startDt.AddDays(-10).Date;
            //endDt = now;
#endif

            try
            {
                //先获取接下来都要用到的Repository 和 Query
                var repoPR = LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>();
                var repoPO = LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>();
                var repoGR = LazyServiceProvider.LazyGetService<IPurGRApplicationRepository>();
                var repoPA = LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>();
                var repoPAInvoice = LazyServiceProvider.LazyGetService<IPurPAApplicationDetailRepository>();
                //TODO:出纳的表结构确定后再查询
                //var wholeTaskRepository = LazyServiceProvider.LazyGetService<IInteDspotWholeTaskRepository>();
                var queryPR = (await repoPR.GetQueryableAsync()).AsNoTracking();
                var queryPO = (await repoPO.GetQueryableAsync()).AsNoTracking();
                var queryGR = (await repoGR.GetQueryableAsync()).AsNoTracking();
                var queryPA = (await repoPA.GetQueryableAsync()).AsNoTracking();
                var queryPAInvoice = (await repoPAInvoice.GetQueryableAsync()).AsNoTracking();

                //查PR、PO、GR、PA、出纳（打款历史）表（按付款主表分组），它们的最后更新时间（创建/修改）满足条件（前一天）【考虑性能：先筛选修改时间，再筛选修改时间为空的创建时间】
                List<DspotReportContentIdDto> listReportIds = GetQueryingReportIds(startDt, endDt, queryPR, queryPO, queryGR, queryPA, queryPAInvoice);
                if (listReportIds?.Any() != true)
                {
                    _logger.LogError($"SyncWholeProcessReport() listReportIds?.Any() != true");
                    return "No PR that meets the conditions";
                }
                var listReportObj = await GetReportObjectList_new(listReportIds, queryPR, queryPO, queryGR, queryPA, queryPAInvoice);


                //然后再查询几张主从表里匹配ReportIds的数据（除了最后更新时间），得到报表对象List
                //var listReportObj = await GetReportObjectList(listReportIds, queryPR, queryPO, queryGR, queryPA, queryPAInvoice);

                if (listReportObj?.Any() != true)
                {
                    _logger.LogError($"SyncWholeProcessReport() listReportObj?.Any() != true");
                    return "There are no Report Data.";
                }

                var contentList = await TransObjToContent(listReportObj);

                //一起发送
                //var resultTask = await PushToDspot(_serviceProvider, wholeTaskEntity.Id, listReportObj);

                #region 按PR分组推送
                //var gp = listReportObj.GroupBy(x => x.PR).Select(g => g.ToList()).ToList();
                //var subTasks = new List<InteDspotWholeTaskSub>();
                //foreach (var g in gp)
                //{
                //    subTasks.Add(new InteDspotWholeTaskSub() { WholeTaskId = wholeTaskEntity.Id });
                //}

                //await wholeTaskSubRepository.InsertManyAsync(subTasks, true);

                //for (int i = 0; i < gp.Count; i++)
                //{
                //    await PushSubTaskDataToDspot(_serviceProvider, subTasks[i], gp[i]);
                //}
                var pushDataRes = await PushData(startDt, endDt, contentList);
                #endregion

                //执行“Tag1”
                //var resultStatus = await TidyTaskPushStatus(_serviceProvider, wholeTaskEntity.Id);

                //遍历查到的N条PR，每个子任务开一个新线程进行处理
                //var tasksSub = new List<Task>();
                //foreach (var item in listReportObj)
                //{
                //    var scope = _serviceProvider.CreateScope();
                //    var scopeServiceProvider = scope.ServiceProvider;

                //    //var scope = _scopeFactory.CreateScope();
                //    //_ = Task.Run(async () =>
                //    tasksSub.Add(Task.Run(async () =>
                //    {
                //        using (var scope = _scopeFactory.CreateScope())
                //        {
                //            await PushToDspot(_serviceProvider, wholeTaskEntity.Id, item);
                //        }
                //    }));
                //}

                //等待所有子任务完成
                //await Task.WhenAll(tasksSub);

                //执行“Tag1”
                //await TidyTaskPushStatus(_serviceProvider, wholeTaskEntity.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError($"SyncWholeProcessReport() Exception: {ex.ToString()}");
                throw;
            }

            return null;
            return await TestPushToDspot(new List<DspotReportContentDto>()
            {
                new DspotReportContentDto()
                {
                    Prcode ="P2104250001",
                    PostDate="2021-04-25 10:44:04",
                    Prinitiator="陈媛 Yuan Chen",
                    Actualinitiator="王涛 Tao Wang",
                    Company="TRADING",
                    Bu="EPD",
                },
                new DspotReportContentDto()
                {
                    Prcode ="P2104250001",
                    PostDate="2021-04-25 10:44:05",
                    Prinitiator="陈媛1 Yuan Chen",
                    Actualinitiator="王涛1 Tao Wang",
                    Company="TRADING",
                    Bu="EPD",
                }
            });
        }

        //[UnitOfWork(IsDisabled = true)]
        [UnitOfWork(isTransactional: false)]
        private async Task<string> PushData(DateTime start, DateTime end, List<DspotReportContentDto> data)
        {
            var wholeTaskRepository = _serviceProvider.GetService<IInteDspotWholeTaskRepository>();
            var wholeTaskSubRepository = _serviceProvider.GetService<IInteDspotWholeTaskSubRepository>();

            //保存WholeTaskEntity
            var wholeTaskEntity = new InteDspotWholeTask { IntegrationDate = start, FirstRunTime = end };

            wholeTaskEntity = await wholeTaskRepository.InsertAsync(wholeTaskEntity);

            var gp = data.GroupBy(x => x.Prcode).Select(g => g.ToList()).ToList();
            var subTasks = new List<InteDspotWholeTaskSub>();
            foreach (var g in gp)
            {
                subTasks.Add(new InteDspotWholeTaskSub() { WholeTaskId = wholeTaskEntity.Id });
            }

            await wholeTaskSubRepository.InsertManyAsync(subTasks, true);

            for (int i = 0; i < gp.Count; i++)
            {
                //await PushSubTaskDataToDspot(_serviceProvider, subTasks[i], gp[i]);
                await PushByTaskSubAndRetry(_serviceProvider, gp[i], subTasks[i]);
                //await Task.Delay(1000);
            }

            //var resultStatus = await TidyTaskPushStatus(_serviceProvider, wholeTaskEntity.Id);

            return null;
        }

        private async Task<InteDspotWholeTask> SaveTask(DateTime startDt, DateTime now)
        {
            //新建一条当天的主任务数据，状态为Init=1，首次执行时间=Now
            var wholeTaskEntity = new InteDspotWholeTask
            {
                IntegrationDate = startDt,
                FirstRunTime = now,
            };
            var wholeTaskRepository = LazyServiceProvider.LazyGetService<IInteDspotWholeTaskRepository>();
            wholeTaskEntity = await wholeTaskRepository.InsertAsync(wholeTaskEntity, true);
            //wholeTaskEntity = await wholeTaskRepository.InsertAsync(wholeTaskEntity);
            return wholeTaskEntity;
        }

        private List<DspotReportContentIdDto> GetQueryingReportIds(DateTime startDt, DateTime endDt
            , IQueryable<PurPRApplication> queryPR
            , IQueryable<PurPOApplication> queryPO
            , IQueryable<PurGRApplication> queryGR
            , IQueryable<PurPAApplication> queryPA
            , IQueryable<PurPAApplicationDetail> queryPAInvoice)
        {
            //查询满足条件的数据
            /*
            1. 推送时间：单据的更新时间为当天
            */
            //var queringPrIds = queryPR.Where(a => a.LastModificationTime.BetweenLeftClose(startDt, endDt)
            //        || (a.LastModificationTime == null && a.CreationTime.BetweenLeftClose(startDt, endDt))).Select(a => a.Id)
            //    .Union(queryPO.Where(a => a.LastModificationTime.BetweenLeftClose(startDt, endDt)
            //        || (a.LastModificationTime == null && a.CreationTime.BetweenLeftClose(startDt, endDt))).Select(a => a.PRId))
            //    .Union(queryGR.Where(a => a.LastModificationTime.BetweenLeftClose(startDt, endDt)
            //        || (a.LastModificationTime == null && a.CreationTime.BetweenLeftClose(startDt, endDt))).Select(a => a.PrId))
            //    .Union(queryPA.Where(a => a.LastModificationTime.BetweenLeftClose(startDt, endDt)
            //        || (a.LastModificationTime == null && a.CreationTime.BetweenLeftClose(startDt, endDt))).Select(a => a.PRId))
            //    .Union(queryPAInvoice.Where(a => a.LastModificationTime.BetweenLeftClose(startDt, endDt)
            //        || (a.LastModificationTime == null && a.CreationTime.BetweenLeftClose(startDt, endDt))).Select(a => a.PRId))
            //    .ToList();
            Expression<Func<PurPRApplication, bool>> exp1 = a =>
                a.LastModificationTime >= startDt && a.LastModificationTime < endDt
                || (a.LastModificationTime == null && a.CreationTime >= startDt && a.CreationTime < endDt);
            Expression<Func<PurPOApplication, bool>> exp2 = a =>
                a.LastModificationTime >= startDt && a.LastModificationTime < endDt
                || (a.LastModificationTime == null && a.CreationTime >= startDt && a.CreationTime < endDt);
            Expression<Func<PurGRApplication, bool>> exp3 = a =>
                a.LastModificationTime >= startDt && a.LastModificationTime < endDt
                || (a.LastModificationTime == null && a.CreationTime >= startDt && a.CreationTime < endDt);
            Expression<Func<PurPAApplication, bool>> exp4 = a =>
                a.LastModificationTime >= startDt && a.LastModificationTime < endDt
                || (a.LastModificationTime == null && a.CreationTime >= startDt && a.CreationTime < endDt);
            Expression<Func<PurPAApplicationInvoice, bool>> exp5 = a =>
                a.LastModificationTime >= startDt && a.LastModificationTime < endDt
                || (a.LastModificationTime == null && a.CreationTime >= startDt && a.CreationTime < endDt);
            var conditions = new DspotReportConditionDto()
            {
                expPR = exp1,
                expPO = exp2,
                expGR = exp3,
                expPA = exp4,
                expPAInvoice = exp5,
            };

            var listReportId = queryPR.Where(conditions.expPR).Select(a => new DspotReportContentIdDto { PRId = a.Id, POId = null, GRId = null, PAId = null })
                .Union(queryPO.Where(conditions.expPO).Select(a => new DspotReportContentIdDto { PRId = a.PRId, POId = a.Id, GRId = null, PAId = null }))
                .Union(queryGR.Where(conditions.expGR).Select(a => new DspotReportContentIdDto { PRId = a.PrId, POId = a.PoId, GRId = a.Id, PAId = null }))
                .Union(queryPA.Where(conditions.expPA).Select(a => new DspotReportContentIdDto { PRId = a.PRId, POId = a.POId, GRId = a.GRId, PAId = a.Id }))
                //.Union(queryPAInvoice.Where(exp5).Select(a => new DspotReportContentIdDto { PRId = a.PRId, POId = a.POId, GRId = a.gr }))
                .Distinct().ToList();
            return listReportId;
        }

        private async Task<List<DspotReportContentObjectDto>> GetReportObjectList(
            List<DspotReportContentIdDto> reportIds
            , IQueryable<PurPRApplication> queryPR
            , IQueryable<PurPOApplication> queryPO
            , IQueryable<PurGRApplication> queryGR
            , IQueryable<PurPAApplication> queryPA
            , IQueryable<PurPAApplicationDetail> queryPAInvoice)
        {
            //提取各个主表的Ids
            var idsList = DspotReportContentIdListDto.GetFromReportIds(reportIds);

            if (reportIds?.Any() != true || idsList == null)
            {
                return new List<DspotReportContentObjectDto>();
            }

            //查询满足条件的数据
            /*
            2. 单据状态：供应商确认/财务关闭/完成(PurPRApplication.Status 申请单状态)
            3. 单据类型：OEC相关的会议类型申请，消费大类=患者和消费者活动/第三方会议支持/雅培自办会议等 (具体详细类型等待OEC最终确认)。(PurPRApplication.ExpenseType 消费大类)
            4. "Vendor name for GR"<>"上海市浦东新区税务局外高桥保"(每个PR行项目都能对应一条GR，找每个GR的VendorName)
            */
            //获取消费大类
            var dataverseService = _serviceProvider.GetService<IDataverseService>();
            var consumeCategory = (await dataverseService.GetConsumeCategoryAsync()).Where(a => a.IsDspot.HasValue && a.IsDspot.Value);
            var consumeIds = consumeCategory.Select(a => a.Id).ToList();

            var prLamda = queryPR
                 //单据状态
                 .Where(a => (new PurPRApplicationStatus[]
                     {
                            PurPRApplicationStatus.VendorConfirmed,//供应商确认
                            PurPRApplicationStatus.WaitForClose,//财务关闭
                            PurPRApplicationStatus.Closed//完成
                     }).Contains(a.Status));
            //单据类型,debug临时注释，因为没有足够的测试数据
            //.Where(a => a.ExpenseType != null && consumeIds.Contains(a.ExpenseType.Value));

            //关联查PRDetail（3个单据状态下，PR一定有PRDetail，所以用Join）
            var queryPrDetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var prPrDLamda = prLamda.WhereIf(idsList.PRIds.Any(), a => idsList.PRIds.Contains(a.Id))
                .Join(queryPrDetail, pr => pr.Id, prD => prD.PRApplicationId, (pr, prD) => new DspotReportContentObjPrPrDDto { PR = pr, PRDetail = prD });

            //查PO，暂不查PODetail
            var poLamda = queryPO.WhereIf(idsList.POIds.Any(), a => idsList.POIds.Contains(a.Id));

            //查GR，暂不查GRDetail
            var grLamda = queryGR.WhereIf(idsList.GRIds.Any(), a => idsList.GRIds.Contains(a.Id))
                .Where(g => !g.VendorName.Equals(EXCLUSIVE_GR_VENDORNAME));

            //查PA Join Invoice
            var paPAInvLamda = queryPA.WhereIf(idsList.PAIds.Any(), a => idsList.PAIds.Contains(a.Id))
                .GroupJoin(queryPAInvoice, left => left.Id, invoice => invoice.PurPAApplicationId, (pa, invoice) => new { pa, invoice })
                .SelectMany(a => a.invoice.DefaultIfEmpty(), (a, invoice) => new DspotReportContentObjPaPaDDto { PA = a.pa, PADetail = invoice })
                //按PRDetailId+PAId分组排序，便于后续将同一PA下的相同PRDetail的发票行组合成一条
                .OrderBy(a => a.PA.Id).ThenBy(a => a.PADetail.PRDetailId);
            //.GroupBy(a => new { a.pa.Id, a.invoice.PRDetailId })
            //.Select(lg => lg.OrderBy(a => new { a.pa.Id, a.invoice.PRDetailId }))
            //.ToList();

            var paPAInvList = paPAInvLamda.ToList();
            //按PRDetailId+PAId分组排序，便于后续将同一PA下的相同PRDetail的发票行组合成一条
            //.OrderBy(a => a.PA?.Id).ThenBy(a => a.PAInvoice?.PRDetailId).ToList();
            var prPrDList = prPrDLamda.ToList();
            var poList = poLamda.ToList();
            var grList = grLamda.ToList();

            //从右到左依次组装PA（PA+PAInvoice）、GR、PO、PR
            var result = new List<DspotReportContentObjectDto>();

            //组装PA，PAInvoice 及前面的层次
            result.AddRange(TidyPaPAInv(paPAInvList, grList, poList, prPrDList));

            //组装GR 及前面的层次
            result.AddRange(TidyGR(result, grList, poList, prPrDList));

            //组装PO 及前面的层次
            result.AddRange(TidyPO(result, poList, prPrDList));

            //组装PR、Detail
            result.AddRange(TidyPRPRDetail(result, prPrDList));

            return result.OrderByDescending(a => a.LastModifyDt).ToList();
        }

        private List<DspotReportContentObjectDto> TidyPRPRDetail(List<DspotReportContentObjectDto> existPRDetail
            , List<DspotReportContentObjPrPrDDto> prPrDList)
        {
            var result = new List<DspotReportContentObjectDto>();

            //排除已存在的Result（existPRDetail）里包含的PRDetail
            var curPrPrDList = existPRDetail.Any()
                ? prPrDList.Where(a => !existPRDetail.Select(e => e?.PRDetail?.Id).Contains(a.PRDetail.Id)).ToList()
                : prPrDList;
            //找PR、PRDetail
            curPrPrDList.ForEach(a =>
            {
                var curReportObj = new DspotReportContentObjectDto();

                //PR, PRDetail
                curReportObj.PR = a.PR;
                curReportObj.PRDetail = a.PRDetail;
                curReportObj.LastModifyDt = a.PRDetail?.LastModificationTime ?? a.PRDetail?.CreationTime;

                result.Add(curReportObj);
            });

            return result;
        }

        private List<DspotReportContentObjectDto> TidyPO(List<DspotReportContentObjectDto> existPO
            , List<PurPOApplication> poList
            , List<DspotReportContentObjPrPrDDto> prPrDList)
        {
            var result = new List<DspotReportContentObjectDto>();

            //然后再从PO开始，从右到左一层一层组装完PR，（最细维度是PO）
            if (poList?.Any() != true)
            {
                return result;
            }
            //排除已存在的Result（existPO）里包含的PO
            var curPoList = existPO.Any()
                ? poList.Where(a => !existPO.Select(e => e?.PO?.Id).Contains(a.Id)).ToList()
                : poList;

            curPoList.ForEach(a =>
            {
                var curReportObj = new DspotReportContentObjectDto();

                //PO
                curReportObj.PO = a;
                curReportObj.LastModifyDt = a.LastModificationTime ?? a.CreationTime;

                //PR, PRDetail
                var curPrPrD = prPrDList.Where(prPrD => prPrD.PR.Id == a.PRId).FirstOrDefault();
                if (curPrPrD != null)
                {
                    curReportObj.PR = curPrPrD.PR;
                    curReportObj.PRDetail = curPrPrD.PRDetail;
                }

                result.Add(curReportObj);
            });

            return result;
        }

        private List<DspotReportContentObjectDto> TidyGR(List<DspotReportContentObjectDto> existGR
            , List<PurGRApplication> grList
            , List<PurPOApplication> poList
            , List<DspotReportContentObjPrPrDDto> prPrDList)
        {
            var result = new List<DspotReportContentObjectDto>();

            //然后再从GR开始，从右到左一层一层组装完PO、PR，（最细维度是GR）
            if (grList?.Any() != true)
            {
                return result;
            }
            //排除已存在的Result（existGR）里包含的GR
            var curGrList = existGR.Any()
                ? grList.Where(a => !existGR.Select(e => e?.GR?.Id).Contains(a.Id)).ToList()
                : grList;

            curGrList.ForEach(a =>
            {
                var curReportObj = new DspotReportContentObjectDto();

                //GR
                curReportObj.GR = a;
                curReportObj.LastModifyDt = a.LastModificationTime ?? a.CreationTime;

                //PO
                curReportObj.PO = poList.Where(po => po.Id == a.PoId).FirstOrDefault();

                //PR, PRDetail
                var curPrPrD = prPrDList.Where(prPrD => prPrD.PR.Id == a.PrId).FirstOrDefault();
                if (curPrPrD != null)
                {
                    curReportObj.PR = curPrPrD.PR;
                    curReportObj.PRDetail = curPrPrD.PRDetail;
                }

                result.Add(curReportObj);
            });

            return result;
        }

        private List<DspotReportContentObjectDto> TidyPaPAInv(List<DspotReportContentObjPaPaDDto> paPAInvList
            , List<PurGRApplication> grList
            , List<PurPOApplication> poList
            , List<DspotReportContentObjPrPrDDto> prPrDList)
        {
            var result = new List<DspotReportContentObjectDto>();

            //然后再从PAInvoice开始，从右到左一层一层组装完GR、PO、PR，（最细维度是同一PRDetail的PA明细（PurPAApplicationInvoice））
            if (paPAInvList?.Any() != true)
            {
                return result;
            }

            //查出付款之前的每一层
            Guid? lastPAId = null, lastPAInvPRDId = null;//lastPAId：PA的Id，lastPAInvPRDId：PAInvoice的PRDetail的ID
            foreach (var item in paPAInvList)
            {
                //通过对比lastPAId、lastPAInvPRDId，看当前这条是否跟上一条是否需要合并
                bool sameAsLast = (item.PA?.Id != null || item.PADetail?.PRDetailId != null)
                    && (lastPAId == item.PA.Id && lastPAInvPRDId == item.PADetail.PRDetailId);

                //如果循环的是同一个PA+PRDetail的Invoice，直接添加到Last的PA
                if (sameAsLast)
                {
                    if (item.PADetail != null)
                    {
                        result.Last()?.PADetails.Add(item.PADetail);
                    }
                    continue;
                }

                lastPAId = item.PA?.Id;
                lastPAInvPRDId = item.PADetail?.PRDetailId;

                //每次循环到新的PA时，就New一条DspotReportContentObjectDto，且加到Result里
                var curReportObj = new DspotReportContentObjectDto();
                //PA, PAInvoice
                curReportObj.PA = item.PA;
                curReportObj.LastModifyDt = item.PA?.LastModificationTime ?? item.PA?.CreationTime;
                if (item.PADetail != null)
                {
                    curReportObj.PADetails.Add(item.PADetail);
                }

                //GR
                curReportObj.GR = grList.Where(gr => gr.Id == item.PA.GRId).FirstOrDefault();

                //PO
                curReportObj.PO = poList.Where(po => po.Id == item.PA.POId).FirstOrDefault();

                //PR, PRDetail
                var curPrPrD = prPrDList.Where(prPrD => prPrD.PR?.Id == item.PA?.PRId)
                        .Where(prPrD => prPrD.PRDetail?.Id == item.PADetail?.PRDetailId)
                        .FirstOrDefault();
                if (curPrPrD != null)
                {
                    curReportObj.PR = curPrPrD.PR;
                    curReportObj.PRDetail = curPrPrD.PRDetail;
                }

                result.Add(curReportObj);
            }

            return result;
        }

        private async Task<string> PushSubTaskDataToDspot(IServiceProvider serviceProvider, InteDspotWholeTaskSub subTask, List<DspotReportContentObjectDto> listReportObj)
        {
            try
            {
                var contentList = await TransObjToContent(listReportObj);
                return await PushByTaskSubAndRetry(serviceProvider, contentList, subTask);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Task.Run PushToDspot() Exception: {ex.ToString()}");
            }
            return null;
        }

        private async Task<string> PushToDspot(IServiceProvider serviceProvider, Guid wholeTaskId, List<DspotReportContentObjectDto> listReportObj)
        {
            try
            {
                var contentList = await TransObjToContent(listReportObj);
                //return "";//等待九个字段加上再测试推送
                //在多线程里，各自生成子任务，存到子表，状态为Init=1
                var wholeTaskSubEntity = new InteDspotWholeTaskSub
                {
                    WholeTaskId = wholeTaskId,
                };
                var wholeTaskSubRepository = serviceProvider.GetService<IInteDspotWholeTaskSubRepository>();
                wholeTaskSubEntity = await wholeTaskSubRepository.InsertAsync(wholeTaskSubEntity);

                return await PushByTaskSubAndRetry(serviceProvider, contentList, wholeTaskSubEntity);
                //return await PushByTaskSubAndRetry(contentList, wholeTaskSubEntity);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Task.Run PushToDspot() Exception: {ex.ToString()}");
            }
            finally
            {
            }

            return null;
        }

        private async Task<List<DspotReportContentDto>> TransObjToContent(List<DspotReportContentObjectDto> listReportObj)
        {
            var result = new List<DspotReportContentDto>();
            if (listReportObj?.Any() != true)
            {
                return result;
            }

            var dicBudgetOwners = GetBudgetOwners(listReportObj);
            var dicMeetingTypes = GetMeetingTypes(listReportObj);
            var dicHospitals = GetHospitals(listReportObj);
            var dicPOFirstApprover = GetPOFirstApprover(listReportObj);
            var dicPendingApprover = GetPendingApprover(listReportObj);
            var dicBpcsPmfvmDate = GetBpcsPmfvmDate(listReportObj);
            var dicBuCodingCfg = GetBuCodingCfg(listReportObj);
            var dicProducts = await GetProducts(listReportObj);
            var dicPRDetailCities = GetPRDetailCities(listReportObj);
            var dicGramount = await GetGramount(listReportObj);
            var dicPaamount = await GetPaamount(listReportObj);
            var dicOECIntercepts = await GetOECIntercepts(listReportObj);
            var dicEbankingPayDate = await GetLastEbankingDate(listReportObj);
            var dicBPCSMakePay = await GetMakePayment(listReportObj);

            foreach (var a in listReportObj)
            {
                if (a == null)
                    continue;
                var dto = new DspotReportContentDto();
                dto.Prcode = a.PR?.ApplicationCode;
                dto.PostDate = a.PR?.ApplyTime?.ToStringFormat();
                dto.Prinitiator = a.PR?.ApplyUserIdName;//发起人（提交人）
                dto.Actualinitiator = a.PR.AgentIdName;//执行者
                dto.Company = a.PR?.CompanyIdName;
                dto.Bu = a.PR?.ApplyUserBuName;
                dto.CategoryCode = a.PR?.SubBudgetCode;//取：子预算编码
                dto.BudgetOwner = dicBudgetOwners.GetValue(a.PR?.SubBudgetId)?.Name;//取：子预算编码.OwnerId.Name//预算所有者;用该ID去表BdSubBudget查
                dto.MeetingType = dicMeetingTypes.GetValue(a.PR?.MeetingType)?.Name;//这是Code，参照取名称：var taxRateDictionarys = await dataverseService.GetDictionariesAsync(DictionaryType.TaxRate);
                dto.ActivityLeader = a.PR?.ActiveLeader;
                dto.ExpenseCategory = a.PR?.ExpenseTypeName;
                if (!string.IsNullOrEmpty(dto.ExpenseCategory))
                {
                    dto.ExpenseCategory = dto.ExpenseCategory.ToLower() switch
                    {
                        "abbott-organized meetings" or "雅培自办会议" => "雅培自办会",
                        "患者和消费者活动" => "患者和消费者活动",
                        "third party meeting support" or "第三方会议支持" => "参会赞助",
                        _ => dto.ExpenseCategory
                    };
                }
                dto.ExpenseNature = a.PRDetail?.CostNatureName;//费用性质
                dto.ActiveCity = a.PR?.ActiveHostCity;
                dto.ActivePlacer = a.PR?.AcitveHostAddress;
                dto.CoverHospital = GetHospitalsNames(dicHospitals, a.PR?.Hospitals);//覆盖医院;是多个逗号分隔的ID;要查：dataverseService.GetAllHospitals
                dto.DoctorNum = string.IsNullOrWhiteSpace(a.PR?.DoctorsNum) ? "0" : a.PR?.DoctorsNum;
                dto.Pramount = a.PR?.TotalAmountRMB.ToString();//PR金额，含税总金额
                //dto.Prstatus = a.PR?.Status.GetDescription();
                if (a.PR?.Status != null)
                {
                    switch (a.PR.Status)
                    {
                        case PurPRApplicationStatus.Closed:
                            dto.Prstatus = "完成";
                            break;
                        case PurPRApplicationStatus.WaitForClose:
                            dto.Prstatus = "财务关闭";
                            break;
                        case PurPRApplicationStatus.Approved:
                            var prDetails = listReportObj.Where(x => x.PR.Id == a.PR.Id).Select(x => x.PRDetail).ToList();
                            if (prDetails.Count == 0)
                                dto.Prstatus = a.PR.Status.GetDescription();
                            else
                            {
                                var isVendorConfirmed = prDetails.Any(x => x.PayMethod.HasValue && x.PayMethod.Value == PayMethods.AR && x.IsVendorConfimed.HasValue && !x.IsVendorConfimed.Value);
                                dto.Prstatus = isVendorConfirmed ? "供应商确认" : "财务关闭";
                            }
                            break;
                        default:
                            dto.Prstatus = a.PR.Status.GetDescription();
                            break;
                    }
                }
                dto.PrcurrentProcessor = dicPendingApprover.GetValue(a.PR?.Id);//当前审批人？https://speaker-portal-api-d.oneabbott.com/api/Common/GeApprovalRecord?request=77a673da-0b9a-ed27-9cb4-3a128e65e466
                dto.TotalProcessDays = a.PR?.ApprovedDate.GapDaysString(a.PR?.ApplyTime);
                dto.PritemNo = a.PRDetail?.RowNo.ToString();
                dto.PritemPaymentType = a.PRDetail?.PayMethod?.GetDescription();
                dto.HcpDepartment = a.PRDetail?.HosDepartment;
                dto.HcpTier = a.PRDetail?.HcpLevelName;//讲者级别 名称：T2
                dto.HcpHospital = a.PRDetail?.Hospital;
                dto.OriginalVendorName = a.PRDetail?.OriginalVendorName;
                dto.VendorName = a.PRDetail?.VendorName;
                dto.VendorNameForgr = string.IsNullOrEmpty(a.GR?.VendorName) ? a.PRDetail.VendorName : a.GR?.VendorName;
                dto.VendorCodeForgr = string.IsNullOrEmpty(a.GR?.VendorCode) ? a.PRDetail.VendorCode : a.GR?.VendorCode;//GR里的VendorId;关联BpcsAvm表查Code; 类似：public async Task<PagedResultDto<GetNonSpeakerListForChoiceResponseDto>> GetNonSpeakerForChoiceAsync
                var bpcsDtm = dicBpcsPmfvmDate.GetValue(a.GR == null || !a.GR.VendorId.HasValue ? a.PRDetail?.VendorId : a.GR.VendorId);
                if (!string.IsNullOrEmpty(bpcsDtm) && bpcsDtm.Split(",").Length == 2)
                {
                    dto.VendorWarehousingDateforgr = bpcsDtm.Split(",")[1];//GR里的VendorId;关联BpcsAvm+BpcsPmfvm，参照晓峰的代码，去BpcsPmfvm表查2个decimal的创建时间
                    var vtype = bpcsDtm.Split(",")[0];
                    //讲者类型，没取VendorTypeName
                    dto.VendorType = vtype switch
                    {
                        "NHIV" => "HCP",
                        "NLIV" => "Non-HCP",
                        "NH" => "HCI",
                        "NL" => "Non-HCI",
                        _ => vtype,
                    };
                }

                dto.Pramountvat = a.PRDetail?.TotalAmountRMB.ToStringExt();
                dto.Coa = $"{a.PR?.CompanyCode}.{dicBuCodingCfg.GetValue(a.PR?.ApplyUserBu)?.BuCode}.{a.PR?.CostCenterCode}.{a.PRDetail?.CostNatureCode}.{dicProducts.GetValue(a.PRDetail?.Id)?.Code}.{dicPRDetailCities.GetValue(a.PRDetail?.CityId)?.CityCode}";//??? //DSPOT:公司+BU+成本中心+费用性质+产品+城市;还有第2点的逻辑
                dto.OrignalActivityDate = a.PRDetail?.EstimateDate.ToStringFormat(DateTimeExtension.DT_FORMAT);
                dto.Prcontent = a.PRDetail?.Content;
                dto.Pushed = a.PRDetail?.PushFlag == PushFlagEnum.NotPushed ? "N" : "Y";//是否已推送，已退回时也传Y?
                dto.PrapproveDate = a.PR?.ApprovedDate.ToStringFormat();
                dto.PrpushDate = a.PRDetail?.PushTime.ToStringFormat();
                dto.PritemProcessDays = a.PRDetail?.PushTime.GapDaysString(a.PR?.ApprovedDate);

                //PO
                dto.Pocode = a.PO?.ApplicationCode;
                dto.PopostDate = a.PO?.ApplyTime.ToStringFormat();
                dto.Poinitiator = a.PO?.ApplyUserName;
                dto.Poamount = a.PO?.TotalAmount.ToString();
                dto.Postatus = a.PO?.Status.GetDescription();
                dto.PocurrentProcessor = dicPendingApprover.GetValue(a.PO?.Id);//PO当前审批人
                dto.Firstlevelapproval = dicPOFirstApprover.GetValue(a.PO?.Id);//第一级审批人
                dto.PrpoDayshoursPr = a.PR?.ApprovedDate.GapDaysString(a.PRDetail?.PushTime);//PR审批通过到推送PO的天数
                dto.PoprocessDays = a.PO?.ApprovedDate.GapDaysString(a.PO?.ApplyTime);//从PO提交到PO审批通过经过的天数

                //GR
                dto.Grcode = a.GR?.ApplicationCode;

                dto.Gramount = dicGramount.GetValue(a.PRDetail?.Id).ToString();// a.GR?.PaymentExcludingTaxAmount;// //DSPOT
                dto.Grdate = a.GR?.ApplyTime.ToStringFormat();
                dto.Grstatus = a.GR?.Status.GetDescription();

                //PA
                dto.Pacode = a.PA?.ApplicationCode;
                dto.PapostDate = a.PA?.ApplyTime.ToStringFormat();
                dto.Paamount = dicPaamount.GetValue(a.PRDetail?.Id).ToString();//(a.PAInvoices?.Sum(a => a.PaymentAmount)).ToStringExt();//按PR行分组求PAInvoice的金额之和//a.PA?.PayTotalAmount.ToString();
                dto.Vendorscore = a.PA?.VendorRating;//供应商得分? //DSPOT3
                dto.PacurrStatus = a.PA?.Status.GetDescription();
                dto.SamplingStatus = dicOECIntercepts.GetValue(a.PRDetail?.Id);// a.PA?.s;//飞检中？ //DSPOT
                dto.PacurrentProcessor = dicPendingApprover.GetValue(a.PA?.Id);//PA当前审批人
                dto.PaprocessDays = a.PA?.ApprovedDate.GapDaysString(a.PA?.ApplyTime);//PA审批了多久                
                dto.Mpdate = dicBPCSMakePay.GetValue(a.PA?.Id);//a.PA?.EstimatedPaymentDate111.ToStringFormat(DateTimeExtension.DT_FORMAT);//? //DSPOT
                dto.EbankingDate = dicEbankingPayDate.GetValue(a.PA?.Id).ToStringFormat(DateTimeExtension.DT_FORMAT);//a.PA?.date;//? //DSPOT
                dto.Finished = a.PR?.Status == PurPRApplicationStatus.Closed ? "Y" : "N"; //如何判定状态是已完成

                dto.PoapproveDate = a.PO?.ApprovedDate.ToStringFormat();//PO审批完成的时间？
                dto.PapostpoApproveProcessDays = a.PO?.ApprovedDate.GapDaysString(a.PA?.ApplyTime);
                dto.IfoReceivedPadate = a.PA?.AcceptedTime.ToStringFormat();
                dto.IfoReceivedPapostprocessDays = a.PA?.AcceptedTime.GapDaysString(a.PA?.ApplyTime);
                dto.Pardreviewdate = a.PA?.ApprovedDate.ToStringFormat();//PA复审日期 == PA最终审批通过的时间
                //dto.MppaPostingDays = a.PA?.ApprovedDate.GapDaysString(Convert.ToDateTime(dicBPCSMakePay.GetValue(a.PA?.Id)));  //a.PA?.make ? +"Days",//? PA申请(ApprovedDate)直至Make Payment所用天数
                if (!string.IsNullOrEmpty(dicBPCSMakePay.GetValue(a.PA?.Id)))
                    dto.MppaPostingDays = a.PA?.ApprovedDate.GapDaysString(DateTime.ParseExact(dicBPCSMakePay.GetValue(a.PA?.Id), "yyyyMMdd", CultureInfo.InvariantCulture));
                dto.ebankingMpDays = dicEbankingPayDate.GetValue(a.PA?.Id)?.GapDays(Convert.ToDateTime(dicBPCSMakePay.GetValue(a.PA?.Id))) + "Days";   //a.PA ? +"Days",//? Make Payment直至最近一次网银成功付款日期
                // DeleteFlag
                dto.DeleteFlag = (a.PR?.Status == PurPRApplicationStatus.Rejected || a.PR?.Status == PurPRApplicationStatus.ApplicantTerminate
                               || a.PA?.Status == PurPAApplicationStatus.Void || a.PO?.Status == PurOrderStatus.Invalid) ? 1 : 0;
                result.Add(dto);
            }
            return result;
        }

        //////private async Task<List<DspotReportContentDto>> GetContent(IServiceProvider scopeServiceProvider, PurPRApplication purPR)
        //////{
        //////    var result = new List<DspotReportContentDto>();
        //////    //查PR明细
        //////    var prDetailQuery = await scopeServiceProvider.GetS//////}ervice<IPurPRApplicationDetailRepository>().GetQueryableAsync();
        //////    var prDetailList = prDetailQuery.Where(a => a.PRApplicationId == purPR.Id)
        //////        .ToList();
        //////    prDetailQuery.GroupJoin

        //////    if (prDetailList?.Any() != true)
        //////    {
        //////        return result;
        //////    }

        //////    ////////查PO及PODetail
        //////    //////var prDetailQuery = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
        //////    //////var prDetailList = prDetailQuery.Where(a => prIds.Contains(a.PRApplicationId))
        //////    //////    .ToList();

        //////    var aaa = prDetailList.Select(a => new DspotReportContentDto()
        //////    {
        //////        Prcode = purPR.ApplicationCode,
        //////        PostDate = a.post
        //////    });

        private async Task<string> PushByTaskSubAndRetry(IServiceProvider scopeServiceProvider, List<DspotReportContentDto> contentList, InteDspotWholeTaskSub wholeTaskSub)
        //private async Task<string> PushByTaskSubAndRetry(List<DspotReportContentDto> contentList, InteDspotWholeTaskSub wholeTaskSub)
        {
            if (contentList?.Any() != true)
            {
                _logger.LogError($"PushByTaskSubAndRetry Faild: contentList is null or Count==0.");
                return null;
            }

            var prCodes = contentList?.Select(a => a.Prcode).JoinAsString(",");
            //从配置文件获取DSPOT的公钥
            var dspotPublicKey = _configuration["Integrations:DSpot:WholeProcessReport_PublicKey"];
            if (string.IsNullOrWhiteSpace(dspotPublicKey))
            {
                _logger.LogError($"PushByTaskSubAndRetry Faild: Configuration(Integrations:DSpot:WholeProcessReport_PublicKey) is null. PrCodes: {prCodes}");
                //throw new Exception("Configuration(Integrations:DSpot:WholeProcessReport_PublicKey) is null");
                return null;
            }

            //新线程里，每条的Id即其行项目组装Body推送到DSPOT，并记录子任务的发送次数/时间，和发送接收的报文
            var pushCount = GetConfigRetryCount();
            InteTaskPushStatus pushStatus = InteTaskPushStatus.Pushing;
            for (int i = 1; i <= pushCount; i++)
            {
                var log = new SetOperationLogRequestDto();
                try
                {
                    //每次都临时生成一个AES密钥串（GUID）
                    var aesKey = Guid.NewGuid().ToString("N");
                    byte[] bytesAesKey = Encoding.UTF8.GetBytes(aesKey);
                    string base64AesKey = Convert.ToBase64String(bytesAesKey);

                    //组装Content
                    var contentListJson = JsonSerializer.Serialize(contentList);
                    var request = new DspotReportRequestDto()
                    {
                        Id = wholeTaskSub.Id,
                        NotifyUrl = _configuration["Integrations:DSpot:Url_WholeNotify"],
                        PublicKey = RsaHelper.EncryptByPublicKey(base64AesKey, dspotPublicKey),
                        Content = AesHelper.EncryptionDspot(contentListJson, aesKey),
                    };

                    //设置请求前子任务相关信息
                    wholeTaskSub.PushStatus = pushStatus;
                    wholeTaskSub.RunTimes = i;
                    wholeTaskSub.LastPushTime = DateTime.Now;
                    wholeTaskSub.LastPushContent = contentListJson;
                    wholeTaskSub.LastPushBody = JsonSerializer.Serialize(request);

                    var url = $"{_configuration["Integrations:DSpot:Url_WholeProcessReport"]!.TrimEnd('/')}";
                    var headers = new Dictionary<string, string>
                    {
                        { "Content-Type", "application/json" },
                    };
                    log = _commonService.InitOperationLog(ClientIdScopeConsts.Dspot, "whole process report同步", url);

                    var response = await url.WithHeaders(headers).PostJsonAsync(request);
                    string responseData = await response.GetStringAsync();
                    _commonService.LogResponse(log, responseData);

                    var resObj = JsonSerializer.Deserialize<DspotWholeResponseDto>(responseData);
                    pushStatus = resObj.Code == 200 ? InteTaskPushStatus.Pushed : pushStatus;

                    //设置请求后子任务相关信息
                    wholeTaskSub.PushStatus = pushStatus;
                    wholeTaskSub.LastResponseTime = DateTime.Now;
                    wholeTaskSub.LastResponseJson = responseData;
                }
                catch (Exception ex)
                {
                    _commonService.LogResponse(log, ex.ToString(), false);
                    //			§ 新线程等待DSPOT的实时Response，若超时或异常，则循环上一步重试，最多发送3次
                    wholeTaskSub.LstPushException = ex.ToString();
                }
                finally
                {
                    //			§ 若3次都非200，则标记该推送失败。执行Tag1（在下方一起执行）
                    if (i == pushCount && wholeTaskSub.PushStatus != InteTaskPushStatus.Pushed)
                    {
                        wholeTaskSub.PushStatus = InteTaskPushStatus.PushFaild;
                    }

                    lock (_lockObject)
                    {
                        //更新子任务到DB
                        var wholeTaskSubRepository = scopeServiceProvider.GetService<IInteDspotWholeTaskSubRepository>();// LazyServiceProvider.LazyGetService<IInteDspotWholeTaskSubRepository>();
                                                                                                                         //await wholeTaskSubRepository.UpdateAsync(wholeTaskSub, true);
                                                                                                                         //wholeTaskSubRepository.UpdateAsync(wholeTaskSub, true).GetAwaiter().GetResult();
                                                                                                                         //wholeTaskSubRepository.UpdateAsync(wholeTaskSub).GetAwaiter().GetResult();
                        wholeTaskSubRepository.UpdateAsync(wholeTaskSub, true);
                    }
                }

                //等到200（成功）的Response则推送成功，执行Tag1（在下方一起执行）。等异步通知。
                //如果推送成功或次数满了，退出循环.且将上面注释中有2处“执行Tag1”的在下方一起执行
                if (wholeTaskSub.PushStatus == InteTaskPushStatus.Pushed || i == pushCount)
                {
                    //记录本次子任务的PushStatus，以便“执行Tag1”，维护主任务的PushStatus（直接执行方法的话DBContext多线程报错）
                    _taskPushStatusList.Add(wholeTaskSub.PushStatus);
                    break;
                }
            }
            return null;
        }

        //Tag1：统计所属主任务的推送状态，根据所有子任务的推送状态（任何一条推送中即推送中；否则，任何一条推送失败即推送失败；否则，全推送成功即推送成功；否则，状态不变即Init）
        private async Task<string> TidyTaskPushStatus(IServiceProvider scopeServiceProvider, Guid taskId)
        {
            if (_taskPushStatusList?.Any() != true)
            {
                return null;
            }

            _taskPushStatusList = _taskPushStatusList.Distinct().ToList();

            var taskPushStatus = InteTaskPushStatus.Init;
            if (_taskPushStatusList.Contains(InteTaskPushStatus.Pushing))
            {
                taskPushStatus = InteTaskPushStatus.Pushing;
            }
            else if (_taskPushStatusList.Contains(InteTaskPushStatus.PushFaild))
            {
                taskPushStatus = InteTaskPushStatus.PushFaild;
            }
            else if (_taskPushStatusList.Count == 1 && _taskPushStatusList[0] == InteTaskPushStatus.Pushed)
            {
                taskPushStatus = InteTaskPushStatus.Pushed;
            }

            //状态不变时不修改数据
            if (taskPushStatus == InteTaskPushStatus.Init)
            {
                return null;
            }

            lock (_lockObject)
            {
                //Task.Delay(5000).GetAwaiter().GetResult();
                var wholeTaskRepository = scopeServiceProvider.GetService<IInteDspotWholeTaskRepository>();
                var wholeTask = wholeTaskRepository.FirstOrDefaultAsync(a => a.Id == taskId).GetAwaiter().GetResult();
                if (wholeTask == null)
                {
                    return null;
                }
                wholeTask.PushStatus = taskPushStatus;
                wholeTaskRepository.UpdateAsync(wholeTask, true);
            }
            return null;
        }

        /// <summary>
        /// 默认3次
        /// </summary>
        /// <returns></returns>
        private int GetConfigRetryCount()
        {
            var pushCount = _configuration["Integrations:DSpot:Push_Count"];
            if (string.IsNullOrWhiteSpace(pushCount) || !int.TryParse(pushCount, out int result))
            {
                return 3;
            }
            return result;
        }

        #endregion 全流程报表推送

        #region 全流程报表推送 - 查询组装字段时需要的数据

        private Dictionary<Guid, IdentityUser> GetBudgetOwners(List<DspotReportContentObjectDto> listReportObj)
        {
            if (listReportObj?.Any() != true)
            {
                return new Dictionary<Guid, IdentityUser>();
            }

            var subBudgetIds = listReportObj.Where(a => a.PR?.SubBudgetId != null)
                .Select(a => a.PR.SubBudgetId.Value).Distinct();

            var querySubBudget = LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>()
                .GetQueryableAsync().GetAwaiter().GetResult().AsNoTracking();
            var queryableUser = LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>()
                .GetQueryableAsync().GetAwaiter().GetResult().AsNoTracking();

            return querySubBudget.Where(a => subBudgetIds.Contains(a.Id))
                .Join(queryableUser, left => left.OwnerId, right => right.Id, (left, right) => new { budget = left, user = right })
                .ToDictionary(a => a.budget.Id, a => a.user);
        }

        private Dictionary<string, DictionaryDto> GetMeetingTypes(List<DspotReportContentObjectDto> listReportObj)
        {
            if (listReportObj?.Any() != true)
            {
                return new Dictionary<string, DictionaryDto>();
            }

            var codes = listReportObj.Where(a => !string.IsNullOrWhiteSpace(a.PR?.MeetingType))
                .Select(a => a.PR?.MeetingType).Distinct();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var dicMeetingTypes = dataverseService.GetDictionariesAsync(DictionaryType.MeetingType).GetAwaiter().GetResult();
            return dicMeetingTypes.Where(a => codes.Contains(a.Code)).ToDictionary(a => a.Code, a => a);
        }

        private string GetCityName(Dictionary<string, CityDto> cities, string activeHostCityId)
        {
            if (cities?.Any() != true || string.IsNullOrWhiteSpace(activeHostCityId))
            {
                return null;
            }

            var code = activeHostCityId.Split(',');
            return code.Length <= 1 ? null : cities.GetValue(code[1])?.Name;
        }

        private Dictionary<Guid, HospitalDto> GetHospitals(List<DspotReportContentObjectDto> listReportObj)
        {
            //dataverseService.GetAllHospitals
            if (listReportObj?.Any() != true)
            {
                return new Dictionary<Guid, HospitalDto>();
            }
            var idsStr = listReportObj.Where(a => !string.IsNullOrWhiteSpace(a.PR?.Hospitals))
                .Select(a => a.PR.Hospitals).Distinct().ToList();

            List<Guid> ids = [];
            idsStr.ForEach(x =>
            {
                var array = x.Split(',');
                foreach (var ar in array)
                {
                    ids.Add(Guid.Parse(ar));
                }
            });
            var idList = ids.Distinct();

            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            return dataverseService.GetAllHospitals().GetAwaiter().GetResult()
                .Where(a => idList.Contains(a.Id)).ToDictionary(a => a.Id, a => a);
        }

        private string GetHospitalsNames(Dictionary<Guid, HospitalDto> dicHospitals, string hospitalsIds)
        {
            if (dicHospitals?.Any() != true || string.IsNullOrWhiteSpace(hospitalsIds))
            {
                return null;
            }

            //医院ID有多个
            var keysString = hospitalsIds.Split(',');
            var keys = keysString.Select(a =>
            {
                if (Guid.TryParse(a, out Guid tempKey))
                {
                    return tempKey;
                }
                return Guid.Empty;
            }).Where(a => a != Guid.Empty).Distinct().ToList();

            return dicHospitals.Where(a => keys.Contains(a.Key))
                .Select(a => a.Value?.Name)
                .Where(a => !string.IsNullOrWhiteSpace(a))
                .JoinAsString(";");
        }

        private Dictionary<Guid, string> GetPendingApprover(List<DspotReportContentObjectDto> listReportObj)
        {
            var result = new Dictionary<Guid, string>();
            if (listReportObj?.Any() != true)
            {
                return result;
            }

            var businessFormIds = listReportObj.Where(a => a.PR?.Id != null).Select(a => a.PR.Id.ToString())
                .Union(listReportObj.Where(a => a.PO?.Id != null).Select(a => a.PO.Id.ToString()))
                .Union(listReportObj.Where(a => a.PA?.Id != null).Select(a => a.PA.Id.ToString()))
                .Select(a => a).Distinct().ToList();//.ToArray();

            //查询PP-task,拼接待审批的节点信息
            var queryTask = new QueryExpression("spk_workflowtask");
            queryTask.ColumnSet.AddColumns("spk_businessformid", "spk_name", "spk_approvalstatus", "spk_remark", "spk_businessformid", "spk_workflowstep", "createdon", "spk_approver", "spk_workflowinstance");
            queryTask.Criteria.AddCondition(new ConditionExpression("statecode", ConditionOperator.Equal, 0));
            queryTask.Criteria.AddCondition(new ConditionExpression("spk_businessformid", ConditionOperator.In, businessFormIds));
            queryTask.Criteria.AddCondition(new ConditionExpression("spk_approvalstatus", ConditionOperator.Equal, (int)ApprovalPowerAppStatus.PendingForApproval));

            var dataverseRepository = _serviceProvider.GetService<IDataverseRepository>();
            var entitieTasks = dataverseRepository.DataverseClient.GetAllEntitiesAsync(queryTask).Result.ToList();
            if (entitieTasks?.Any() != true)
            {
                return result;
            }

            var allBusiIds = entitieTasks.Select(a => a.GetAttributeValue<string>("spk_businessformid"))
                .Where(a => !string.IsNullOrEmpty(a)).Distinct().ToList();
            foreach (var item in allBusiIds)
            {
                var pendingTasks = entitieTasks.Where(a => item == a.GetAttributeValue<string>("spk_businessformid"));
                var pendingApprovers = pendingTasks?.Any() != true ? null
                    : pendingTasks.Select(p => p.GetAttributeValue<EntityReference>("spk_approver")?.Name).JoinAsString(";");
                result.Add(Guid.Parse(item), pendingApprovers);
            }
            return result;
        }

        private Dictionary<Guid, string> GetPOFirstApprover(List<DspotReportContentObjectDto> listReportObj)
        {
            var result = new Dictionary<Guid, string>();
            if (listReportObj?.Any() != true)
            {
                return result;
            }

            var businessFormIds = listReportObj.Where(a => a.PO?.Id != null).Select(a => a.PO.Id.ToString()).Distinct().ToList();//.ToArray();
            businessFormIds = new List<string> { "7C763191-620E-862D-7CCE-3A12CF5DD983" };
            //查询PP-task,获取createdon最早的记录
            var queryTask = new QueryExpression("spk_workflowtask");
            queryTask.ColumnSet.AddColumns("spk_businessformid", "spk_name", "spk_approvalstatus", "spk_remark", "spk_businessformid", "spk_workflowstep", "createdon", "spk_approver", "spk_workflowinstance");
            queryTask.Criteria.AddCondition(new ConditionExpression("statecode", ConditionOperator.Equal, 0));
            queryTask.Criteria.AddCondition(new ConditionExpression("spk_businessformid", ConditionOperator.In, businessFormIds));

            var dataverseRepository = _serviceProvider.GetService<IDataverseRepository>();
            var entitieTasks = dataverseRepository.DataverseClient.GetAllEntitiesAsync(queryTask).Result.ToList();
            if (entitieTasks?.Any() != true)
            {
                return result;
            }

            var allBusiIds = entitieTasks.Select(a => a.GetAttributeValue<string>("spk_businessformid"))
                .Where(a => !string.IsNullOrEmpty(a)).Distinct().ToList();
            foreach (var item in allBusiIds)
            {
                var tasks = entitieTasks.Where(a => item == a.GetAttributeValue<string>("spk_businessformid"));
                var pendingApprovers = tasks?.Any() != true ? ""
                    : tasks.OrderBy(t => t.GetAttributeValue<DateTime>("createdon")).Select(p => p.GetAttributeValue<EntityReference>("spk_approver")?.Name).First().ToString();
                result.Add(Guid.Parse(item), pendingApprovers);
            }
            return result;

        }

        private Dictionary<Guid, string> GetBpcsPmfvmDate(List<DspotReportContentObjectDto> listReportObj)
        {
            var result = new Dictionary<Guid, string>();
            if (listReportObj?.Any() != true)
            {
                return result;
            }

            //a.GR?.VendorId
            var vendorIds = listReportObj.Select(a => a.GR?.VendorId).Where(a => a != null && a.Value != Guid.Empty)
                .Select(a => a.Value).Distinct().ToList();

            if (vendorIds?.Any() != true)
            {
                return result;
            }

            //查询表bpcsAvm+bpcsPmfvm
            var bpcsAvmQuery = LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync()
                .GetAwaiter().GetResult().AsNoTracking();
            var bpcsPmfvmQuery = LazyServiceProvider.LazyGetService<IBpcsPmfvmRepository>().GetQueryableAsync()
                .GetAwaiter().GetResult().AsNoTracking();

            var listData = bpcsAvmQuery.Where(a => vendorIds.Contains(a.Id))
                .Join(bpcsPmfvmQuery, a => new { VendorNum = a.Vendor, Vmcmpy = a.Vcmpny }, b => new { VendorNum = b.Vnderx, Vmcmpy = b.Vmcmpy },
                    (a, b) => new { bpcsAvmId = a.Id, bpcsAvmVtype = a.Vtype, bpcsPmfvmVcrdte = b.Vcrdte, bpcsPmfvmVctime = b.Vctime })
                .ToList();
            if (listData?.Any() != true)
            {
                return result;
            }

            return listData.ToDictionary(a => a.bpcsAvmId, a =>
            {
                var vtype = a.bpcsAvmVtype;
                var dtm = string.Empty;
                if (a.bpcsPmfvmVcrdte == null)
                {
                    return $"{vtype},{dtm}";
                }
                var dtString = a.bpcsPmfvmVcrdte.ToString() + a.bpcsPmfvmVctime?.ToString().PadLeft(6, '0');
                if (!string.IsNullOrWhiteSpace(dtString) && dtString.Length == 14)
                {
                    dtm = DateTime.ParseExact(dtString, "yyyyMMddHHmmss", null).ToString(DateTimeExtension.DT_FORMAT);
                }
                return $"{vtype},{dtm}";
            });
        }

        private Dictionary<Guid, BuCodingCfgDto> GetBuCodingCfg(List<DspotReportContentObjectDto> listReportObj)
        {
            if (listReportObj?.Any() != true)
            {
                return new Dictionary<Guid, BuCodingCfgDto>();
            }

            var buIds = listReportObj.Where(a => a.PR?.ApplyUserBu != null)
                .Select(a => a.PR?.ApplyUserBu).Where(a => a != null).Distinct().ToList();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var dicBuCodingCfg = dataverseService.GetBuCodingCfgAsync().GetAwaiter().GetResult();
            return dicBuCodingCfg.Where(a => buIds.Contains(a.BuId)).ToDictionary(a => a.BuId, a => a);
        }

        private async Task<Dictionary<Guid, ProductDto>> GetProducts(List<DspotReportContentObjectDto> listReportObj)
        {
            var result = new Dictionary<Guid, ProductDto>();
            if (listReportObj?.Any() != true)
            {
                return result;
            }
            var prDetailIds = listReportObj.Select(a => a.PRDetail?.Id).Distinct();
            if (!prDetailIds.Any())
            {
                return result;
            }
            //var productIds = listReportObj.Where(a => a.PRDetail?.ProductId != null)
            //    .Select(a => a.PRDetail?.ProductId).Where(a => a != null).Select(a => a.Value).Distinct().ToList();
            //var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            //var dicProducts = dataverseService.GetProductsAsync().GetAwaiter().GetResult();
            //return dicProducts.Where(a => productIds.Contains(a.Id)).ToDictionary(a => a.Id, a => a);

            //PR拆分，PurPRApplicationDetails表中ProductId字段弃用，新增PurPRApplicationProductApportionments 关联表
            //多个产品拆分的时候取第一个产品就行
            var prIds = listReportObj.Select(a => a.PR.Id).Distinct();
            var queryMapping = await LazyServiceProvider.LazyGetService<IPurPRApplicationProductApportionmentRepository>().GetQueryableAsync();
            var queryResult = queryMapping.Where(a => prIds.Contains(a.PRApplicationId) && prDetailIds.Contains(a.PRApplicationDetailId)).ToList();
            foreach (var item in prDetailIds)
            {
                var first = queryResult.Where(a => a.PRApplicationDetailId == item).OrderBy(a => a.ProductCode).FirstOrDefault();
                if (first != null)
                {
                    result.Add((Guid)item, new ProductDto() { Code = first.ProductCode, Name = first.ProductName });
                }
            }
            return result;
        }

        private Dictionary<Guid, CityMasterDataDto> GetPRDetailCities(List<DspotReportContentObjectDto> listReportObj)
        {
            if (listReportObj?.Any() != true)
            {
                return new Dictionary<Guid, CityMasterDataDto>();
            }

            var cityIds = listReportObj.Select(a => a.PRDetail?.CityId)
                .Where(a => a != null).Select(a => a.Value).Distinct().ToList();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var dicCities = dataverseService.GetSpecialCitiesAsync().GetAwaiter().GetResult();
            return dicCities.Where(a => cityIds.Contains(a.Id)).ToDictionary(a => a.Id, a => a);
        }

        #endregion 全流程报表推送 - 查询组装字段时需要的数据


        #region 重写GetReportObjectList方法逻辑

        /// <summary>
        /// 获取符合条件的所有PR记录，当PO GR PA有变动时，也找到对应的PR。再根据PR往下游查询对应记录
        /// 重写原因：
        /// 1.当只有PR状态改动时，之前的逻辑不查找对应的PO GR PA数据
        /// 2.当PA GR PO改动时，对应PR需要满足过滤条件才推送
        /// 3.PR查询单据状态有变更
        /// </summary>
        /// <param name="reportIds"></param>
        /// <param name="queryPR"></param>
        /// <param name="queryPO"></param>
        /// <param name="queryGR"></param>
        /// <param name="queryPA"></param>
        /// <param name="queryPAInvoice"></param>
        /// <returns></returns>
        private async Task<List<DspotReportContentObjectDto>> GetReportObjectList_new(
             List<DspotReportContentIdDto> reportIds
             , IQueryable<PurPRApplication> queryPR
             , IQueryable<PurPOApplication> queryPO
             , IQueryable<PurGRApplication> queryGR
             , IQueryable<PurPAApplication> queryPA
             , IQueryable<PurPAApplicationDetail> queryPAInvoice)
        {
            //提取各个主表的Ids
            var idsList = DspotReportContentIdListDto.GetFromReportIds(reportIds);

#if DEBUG
            //idsList.PRIds = new List<Guid>() { Guid.Parse("7C763191-620E-862D-7CCE-3A12CF5DD983") };
#endif

            if (reportIds?.Any() != true || idsList == null)
            {
                return new List<DspotReportContentObjectDto>();
            }


            //查询满足条件的数据
            /*
            2. 单据状态：审批通过/财务关闭/完成(PurPRApplication.Status 申请单状态)
                            - 7换成3，然后状态名称的逻辑：
                            - 如果状态为3，且该PR对应[PurPRApplicationDetails].[PayMethod] = 'AR'的明细行，[PurPRApplicationDetails].[IsVendorConfimed]存在为0的数据，就推送状态名称为"供应商确认"，否则(AR全部confirmed = 1，或者无AR等)推送状态名称为"审批完毕，等待关闭"   
                         审批拒绝/发起人终止 
                            - DeleteFlag赋值为1
            3. 单据类型：OEC相关的会议类型申请，消费大类=患者和消费者活动/第三方会议支持/雅培自办会议等 (具体详细类型等待OEC最终确认)。(PurPRApplication.ExpenseType 消费大类)
            4. "Vendor name for GR"<>"上海市浦东新区税务局外高桥保"(每个PR行项目都能对应一条GR，找每个GR的VendorName)
            */
            //获取消费大类
            var dataverseService = _serviceProvider.GetService<IDataverseService>();
            var consumeCategory = (await dataverseService.GetConsumeCategoryAsync()).Where(a => a.IsDspot.HasValue && a.IsDspot.Value);
            var consumeIds = consumeCategory.Select(a => a.Id).ToList();

            /// 获取符合条件的所有PR记录，当PO GR PA有变动时，也找到对应的PR。再根据PR往下游查询对应记录
            var PRList = queryPR
                 //单据状态
                 .Where(a => (new PurPRApplicationStatus[]
                     {
                            PurPRApplicationStatus.Approved,//审批通过
                            PurPRApplicationStatus.WaitForClose,//财务关闭
                            PurPRApplicationStatus.Closed,//完成
                            PurPRApplicationStatus.Rejected,//审批拒绝
                            PurPRApplicationStatus.ApplicantTerminate//发起人终止

                     }).Contains(a.Status))
                //单据类型,debug临时注释，因为没有足够的测试数据
                .Where(a => a.ExpenseType != null && consumeIds.Contains(a.ExpenseType.Value))
                //单据修改日期是前一天的PR
                .Where(a => idsList.PRIds.Contains(a.Id)).Select(x => x.Id).ToList();

            if (!PRList.Any())
            {
                return new List<DspotReportContentObjectDto>();
            }

            //查询PRDetails,防止子表没有记录，全部用left join
            var queryPrDetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var PRDQuery = queryPR
                .GroupJoin(queryPrDetail, pr => pr.Id, prD => prD.PRApplicationId, (pr, prD) => new { pr, prD })
                .SelectMany(a => a.prD.DefaultIfEmpty(), (a, prd) => new DspotReportContentObjPrPrDDto { PR = a.pr, PRDetail = prd })
                .Where(a => PRList.Contains(a.PR.Id));

            //查询PODetails,防止子表没有记录，全部用left join
            var queryPoDetail = await LazyServiceProvider.LazyGetService<IPurPOApplicationDetailsRepository>().GetQueryableAsync();
            var PODQuery = queryPO
                .GroupJoin(queryPoDetail, po => po.Id, poD => poD.POApplicationId, (po, poD) => new { po, poD })
                .SelectMany(a => a.poD.DefaultIfEmpty(), (a, pod) => new DspotReportContentObjPoPoDDto { PO = a.po, PODetail = pod })
                .Where(a => PRList.Contains(a.PO.PRId));

            //查询GRDetails,防止子表没有记录，全部用left join
            var queryGrDetail = await LazyServiceProvider.LazyGetService<IPurGRApplicationDetailRepository>().GetQueryableAsync();
            var GRDQuery = queryGR
                .GroupJoin(queryGrDetail, gr => gr.Id, grD => grD.GRApplicationId, (gr, grD) => new { gr, grD })
                .SelectMany(a => a.grD.DefaultIfEmpty(), (a, grd) => new DspotReportContentObjGrGrDDto { GR = a.gr, GRDetail = grd })
                .Where(a => PRList.Contains(a.GR.PrId) && !a.GR.VendorName.Equals(EXCLUSIVE_GR_VENDORNAME));

            //查询PADetails,防止子表没有记录，全部用left join
            var queryPaDetail = await LazyServiceProvider.LazyGetService<IPurPAApplicationDetailRepository>().GetQueryableAsync();
            var PADQuery = queryPA
                .GroupJoin(queryPaDetail, pa => pa.Id, paD => paD.PurPAApplicationId, (pa, paD) => new { pa, paD })
                .SelectMany(a => a.paD.DefaultIfEmpty(), (a, pad) => new DspotReportContentObjPaPaDDto { PA = a.pa, PADetail = pad })
                .Where(a => PRList.Contains(a.PA.PRId));




            var query = PRDQuery
                //left join po
                .GroupJoin(PODQuery, left => left.PRDetail.Id, right => right.PODetail.PRDetailId, (left, right) => new { left, right })
                .SelectMany(a => a.right.DefaultIfEmpty(), (a, right) => new { PR = a.left.PR, PRDetail = a.left.PRDetail, PO = right.PO, PODetail = right.PODetail })
                //left join gr
                .GroupJoin(GRDQuery, l => new { PrDID = l.PRDetail.Id, PoDID = l.PODetail.Id.ToString() },
                                     r => new { PrDID = r.GRDetail.PRDetailId, PoDID = r.GRDetail.PODetailId.ToString() }, (left, right) => new { left, right })
                .SelectMany(a => a.right.DefaultIfEmpty(), (a, right) => new
                {
                    PR = a.left.PR,
                    PRDetail = a.left.PRDetail,
                    PO = a.left.PO,
                    PODetail = a.left.PODetail,
                    GR = right.GR,
                    GRDetail = right.GRDetail
                })
                //left join pa
                .GroupJoin(PADQuery, l => new { PrDID = l.PRDetail.Id, PoDID = l.PODetail.Id.ToString(), GrDID = l.GRDetail.Id.ToString() },
                                     r => new { PrDID = r.PADetail.PRDetailId, PoDID = r.PADetail.PODetailId.ToString(), GrDID = r.PADetail.GRApplicationDetailId.ToString() },
                                     (left, right) => new { left, right })
                .SelectMany(a => a.right.DefaultIfEmpty(),
                        (a, right) => new DspotReportContentObjectDto()
                        {
                            PR = a.left.PR,
                            PRDetail = a.left.PRDetail,
                            PO = a.left.PO,
                            //PODetail = a.left.PODetail,
                            GR = a.left.GR,
                            //GRDetail = a.left.GRDetail,
                            PA = right.PA,
                            //PADetail = right.PADetail
                        });
            return query.ToList();
        }

        private async Task<Dictionary<Guid, decimal?>> GetGramount(List<DspotReportContentObjectDto> listReportObj)
        {
            var result = new Dictionary<Guid, decimal?>();
            if (listReportObj?.Any() != true)
            {
                return result;
            }

            var prDetailIds = listReportObj.Where(a => a.PRDetail?.Id != null).Select(a => a.PRDetail.Id);
            if (!prDetailIds.Any())
            {
                return result;
            }
            var queryGRDetailHis = await LazyServiceProvider.LazyGetService<IPurGRApplicationDetailHistoryRepository>().GetQueryableAsync();
            //按pr detailid去[PurGRApplicationDetailHistorys]查询，然后按allocationRMBamount汇总
            var dic = queryGRDetailHis.Where(a => prDetailIds.Contains(a.PRDetailId)).GroupBy(a => a.PRDetailId).Select(g => new { PRDetailId = g.Key, Gramount = g.Sum(x => x.AllocationRMBAmount) })
                .ToDictionary(g => g.PRDetailId, g => g.Gramount);
            return dic;
        }

        /// <summary>
        /// 查询PA金额
        /// 按Prdetailid查到一行对应的PurPAApplicationDetails金额(可能查出来多行)，然后每一行按照paymentamount/(1+taxrate)得到不含税金额，再加总
        /// </summary>
        /// <param name="listReportObj"></param>
        /// <returns></returns>
        private async Task<Dictionary<Guid, decimal>> GetPaamount(List<DspotReportContentObjectDto> listReportObj)
        {
            var result = new Dictionary<Guid, decimal>();
            if (listReportObj?.Any() != true)
            {
                return result;
            }

            var prDetailIds = listReportObj.Where(a => a.PRDetail?.Id != null).Select(a => a.PRDetail.Id);
            if (!prDetailIds.Any())
            {
                return result;
            }
            var queryPADetail = await LazyServiceProvider.LazyGetService<IPurPAApplicationDetailRepository>().GetQueryableAsync();
            //按pr detailid去[PurGRApplicationDetailHistorys]查询，然后按allocationRMBamount汇总
            var dic = queryPADetail.Where(a => prDetailIds.Contains(a.PRDetailId)).GroupBy(a => a.PRDetailId).Select(g => new { PRDetailId = g.Key, Paamount = g.Sum(x => (x.PaymentAmount / (string.IsNullOrEmpty(x.TaxRate) ? 1m : (Convert.ToDecimal(x.TaxRate) + 1m)))) })
                .ToDictionary(g => g.PRDetailId, g => Math.Round(g.Paamount, 4));
            return dic;
        }

        /// <summary>
        /// 查询拦截状态OECIntercepts
        /// 按Prdetailid查询
        /// </summary>
        /// <param name="listReportObj"></param>
        /// <returns></returns>
        private async Task<Dictionary<Guid, string>> GetOECIntercepts(List<DspotReportContentObjectDto> listReportObj)
        {
            var result = new Dictionary<Guid, string>();
            if (listReportObj?.Any() != true)
            {
                return result;
            }

            var prDetailIds = listReportObj.Where(a => a.PRDetail?.Id != null).Select(a => a.PRDetail.Id);
            if (!prDetailIds.Any())
            {
                return result;
            }
            var queryOECIntercepts = await LazyServiceProvider.LazyGetService<IOECInterceptRepository>().GetQueryableAsync();
            //按pr detailid去[PurGRApplicationDetailHistorys]查询，然后按allocationRMBamount汇总
            var dic = queryOECIntercepts.Where(a => prDetailIds.Contains(a.PRDetailId)).Select(x => new { x.PRDetailId, x.InterceptStatus })
                .ToDictionary(x => x.PRDetailId, x => x.InterceptStatus.GetDescription());
            return dic;
        }

        private async Task<Dictionary<Guid, string>> GetMakePayment(List<DspotReportContentObjectDto> listReportObj)
        {
            /*
             逻辑是，按PA申请单，移除第一位A之后的单号，去查[BPCSGLH].[LHDREF]
             1.如果查询出的结果的[LHREAS]中包含状态为[APMPL]的行，就标记Make Payment = Y，且取该行的[LHDATE]+[LHTIME]作为make payment时间(如果查到有多行，优先取[LHJNLN]最小的记录)
             2.如果查询出的结果的[LHREAS]中不包含状态为[APMPL]的行，则查询是否有包含状态为[APV2L]的行，如果有代表进行过void，此时将单号转置一位(最左侧一位移动到最右侧，例如12345转置为23451)之后用来查询[BPCSGLH].[LHDREF]，继续按1的逻辑查询
             3.如果按1/2均没有查询到[APMPL]记录，则标记Make Payment = N，且Make Payment时间为空
             */
            var result = new Dictionary<Guid, string>();
            if (listReportObj?.Any() != true)
            {
                return result;
            }

            var paIds = listReportObj.Where(a => a.PA?.Id != null).Select(a => a.PA.Id);
            if (!paIds.Any())
            {
                return result;
            }
            var queryPAApplication = await LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>().GetQueryableAsync();
            var queryBPCSGlh = await LazyServiceProvider.LazyGetService<IBpcsGlhRepository>().GetQueryableAsync();
            //步骤1
            var first = queryPAApplication.Where(a => paIds.Contains(a.Id)).GroupJoin(queryBPCSGlh.Where(a => a.Lhreas == "APMPL"), a => a.ApplicationCode, a => "A" + a.Lhdref, (l, r) => new { l, r })
                .SelectMany(a => a.r.DefaultIfEmpty(), (a, right) => new { PA = a.l, BPCS = right }).ToList();

            foreach (var item in paIds)
            {
                var r = first.Where(a => a.PA.Id == item && a.BPCS != null).OrderBy(a => a.BPCS.Lhjnln).FirstOrDefault();
                if (r != null)
                {
                    result.Add(item, r.BPCS.Lhdate.ToString());
                }
            }
            //步骤2
            var notinFirst = paIds.Where(a => !result.Keys.Contains(a));
            var second = queryPAApplication.Where(a => notinFirst.Contains(a.Id)).GroupJoin(queryBPCSGlh, a => a.ApplicationCode, a => "A" + a.Lhdref, (l, r) => new { l, r })
                .SelectMany(a => a.r.DefaultIfEmpty(), (a, right) => new { PA = a.l, BPCS = right }).Where(a => a.BPCS.Lhreas == "APV2L").ToList();
            var secondGoOnIds = second.Select(a => a.PA.Id).Distinct();

            //继续步骤1的逻辑查询
            var secondGoOnResult = queryPAApplication.Where(a => secondGoOnIds.Contains(a.Id)).GroupJoin(queryBPCSGlh.Where(a => a.Lhreas == "APMPL"), a => a.ApplicationCode.Substring(2) + a.ApplicationCode.Substring(1).Substring(0, 1), a => a.Lhdref, (l, r) => new { l, r })
               .SelectMany(a => a.r.DefaultIfEmpty(), (a, right) => new { PA = a.l, BPCS = right }).ToList();
            foreach (var item in secondGoOnIds)
            {
                var r = secondGoOnResult.Where(a => a.PA.Id == item && a.BPCS != null).OrderBy(a => a.BPCS.Lhjnln).FirstOrDefault();
                if (r != null)
                {
                    result.Add(item, r.BPCS.Lhdate.ToString());
                }
            }

            return result;
        }

        private async Task<Dictionary<Guid, DateTime?>> GetLastEbankingDate(List<DspotReportContentObjectDto> listReportObj)
        {
            /*查询最近的一次网银付款记录
             */
            var result = new Dictionary<Guid, DateTime?>();
            if (listReportObj?.Any() != true)
            {
                return result;
            }

            var paIds = listReportObj.Where(a => a.PA?.Id != null).Select(a => a.PA.Id);
            if (!paIds.Any())
            {
                return result;
            }
            var queryPAApplication = await LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>().GetQueryableAsync();
            var queryFinance = await LazyServiceProvider.LazyGetService<IFinanceCashierPaymentInfoRepository>().GetQueryableAsync();
            result = queryPAApplication.Where(pa => paIds.Contains(pa.Id)).GroupJoin(queryFinance, a => a.ApplicationCode, b => b.PAApplicationCode, (a, b) => new { a, b })
                .SelectMany(a => a.b.DefaultIfEmpty(), (a, fc) => new { a.a.Id, fc.PaymentDate }).GroupBy(x => x.Id).ToDictionary(g => g.Key, g => g.First().PaymentDate);
            return result;
        }
        #endregion
        #region 未使用的方法

        private async Task<string> TestPushToDspot(List<DspotReportContentDto> contentList)
        {
            //从配置文件获取DSPOT的公钥
            var dspotPublicKey = _configuration["Integrations:DSpot:WholeProcessReport_PublicKey"];
            if (string.IsNullOrWhiteSpace(dspotPublicKey))
            {
                var prCodes = contentList?.Select(a => a.Prcode).JoinAsString(",");
                _logger.LogError($"TestPushToDspot Faild: Configuration(Integrations:DSpot:WholeProcessReport_PublicKey) is null. PrCodes: {prCodes}");
                //throw new Exception("Configuration(Integrations:DSpot:WholeProcessReport_PublicKey) is null");
                return null;
            }

            //每次都临时生成一个AES密钥串（GUID）
            var aesKey = Guid.NewGuid().ToString("N");

            //组装Content
            var request = new DspotReportRequestDto()
            {
                Id = Guid.NewGuid(),
                NotifyUrl = _configuration["Integrations:DSpot:Url_WholeNotify"],
                PublicKey = RsaHelper.EncryptByPublicKey(aesKey, dspotPublicKey),
                //PublicKey = "E9a4RhWqA33Hi+UVz5t66SmfgAFeJ47bItNgOTR/gcu/++g3tSjmDuqqNeIaGNjBirC1P1v6oZRAdjU0BWcjW1EXeAunAgxA7utw9nO9omCpek3v8h82e/VzZWmsPUOn1OIvxN3jxeaLlYkGMwpknjlWifpAlw5wfWU6LW+L3Kw=",
                Content = AesHelper.Encryption(JsonSerializer.Serialize(contentList), aesKey),
                //Content = "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"
            };

            var url = $"{_configuration["Integrations:DSpot:Url_WholeProcessReport"]!.TrimEnd('/')}";
            var headers = new Dictionary<string, string>
                {
                    { "Content-Type", "application/json" },
                };
            var response = await url.WithHeaders(headers).PostJsonAsync(request);
            string responseData = await response.GetStringAsync();
            return responseData;
        }

        private async Task<string> PushToDspotByPR(IServiceScope scope, Guid wholeTaskId, PurPRApplication purPR)
        {
            try
            {
                //var scope = _scopeFactory.CreateScope();
                //using (var scope = _serviceProvider.CreateScope())
                //using (var scope = _scopeFactory.CreateScope())
                {
                    var scopeServiceProvider = scope.ServiceProvider;
                    //在多线程里，各自生成子任务，存到子表，状态为Init=1
                    var wholeTaskSubEntity = new InteDspotWholeTaskSub
                    {
                        WholeTaskId = wholeTaskId,
                    };
                    lock (_lockObject)
                    {
                        var wholeTaskSubRepository = scopeServiceProvider.GetService<IInteDspotWholeTaskSubRepository>();// LazyServiceProvider.LazyGetService<IInteDspotWholeTaskSubRepository>();
                                                                                                                         //wholeTaskSubEntity = await wholeTaskSubRepository.InsertAsync(wholeTaskSubEntity, true);
                                                                                                                         //wholeTaskSubEntity = wholeTaskSubRepository.InsertAsync(wholeTaskSubEntity, true).GetAwaiter().GetResult();
                        wholeTaskSubEntity = wholeTaskSubRepository.InsertAsync(wholeTaskSubEntity).GetAwaiter().GetResult();
                    }

                    //TODO:查找该PR的行项目和需要推送的所有字段（PR,PRDetail，GR,PA），拼装成Content,并校验数据格式或必填项

                    //////var contentList111 = new List<DspotReportContentDto>();
                    //////contentList111.Add(GetContent(scopeServiceProvider, purPR));

                    var contentList = new List<DspotReportContentDto>()
                {
                    new DspotReportContentDto()
                    {
                        Prcode =purPR.ApplicationCode,
                        PostDate="2021-04-25 10:44:04",
                        Prinitiator="陈媛 Yuan Chen",
                        Actualinitiator="王涛 Tao Wang",
                        Company="TRADING",
                        Bu="EPD",
                    },
                    new DspotReportContentDto()
                    {
                        Prcode =purPR.ApplicationCode,
                        PostDate="2021-04-25 10:44:05",
                        Prinitiator="陈媛1 Yuan Chen",
                        Actualinitiator="王涛1 Tao Wang",
                        Company="TRADING",
                        Bu="EPD",
                    }
                };

                    return await PushByTaskSubAndRetry(scopeServiceProvider, contentList, wholeTaskSubEntity);
                    //return await PushByTaskSubAndRetry(contentList, wholeTaskSubEntity);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Task.Run PushToDspotByPR() Exception: {ex.ToString()}");
            }
            finally
            {
            }

            return null;
        }

        #endregion 未使用的方法
    }
}