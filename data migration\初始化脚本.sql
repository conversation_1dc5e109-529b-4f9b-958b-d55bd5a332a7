--导数前
ALTER TABLE PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM DROP CONSTRAINT DF__ODS_BPCS_AVM__ID__2AA05119;
ALTER TABLE PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM DROP COLUMN ID;
ALTER TABLE PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM DROP CONSTRAINT DF__ODS_BPCS_PMF__ID__2B947552;
ALTER TABLE PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM DROP COLUMN ID;
--导数后
ALTER TABLE PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM 
ADD ID UNIQUEIDENTIFIER NOT NULL DEFAULT (NEWID());
ALTER TABLE PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM  
ADD ID UNIQUEIDENTIFIER NOT NULL DEFAULT (NEWID());

--更改路劲
UPDATE Attachments
SET FilePath = REPLACE(FilePath, 'F:/ExportFolder/', 'bpm-his-file/')
WHERE FilePath LIKE 'F:/ExportFolder/%';

--初始化xml_11
SELECT 
	    ProcInstId,
	    STRING_AGG(up_Id, ', ') WITHIN GROUP (ORDER BY up_Id) AS up_Id
	    into PLATFORM_ABBOTT.dbo.XML_11
	FROM 
	    (select ProcInstId,
			up_Id
		from (
		select XmlContent,ProcInstId,
		up_Id.value('.', 'nvarchar(100)') as up_Id 
		FROM PLATFORM_ABBOTT.dbo.ODS_T_FORMINSTANCE_GLOBAL
		CROSS APPLY XmlContent.nodes('/root/QuoteAttachmentGrid/row/up_Id') AS XMLTable(up_Id)) B	
		)q
		GROUP BY  ProcInstId ;

--初始化xml2  sp_PurBDApplications
IF OBJECT_ID(N'PLATFORM_ABBOTT_Dev.dbo.XML_2', N'U') IS NOT NULL
BEGIN
	PRINT(N'已经初始化过'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select * into PLATFORM_ABBOTT_Dev.dbo.XML_2 
	from (
		SELECT 
    ProcInstId,
    STRING_AGG(up_Id, ', ') WITHIN GROUP (ORDER BY up_Id) AS up_Id
FROM 
    (select ProcInstId,
		up_Id
	from (
	select ProcInstId,
	up_Id.value('.', 'nvarchar(100)') as up_Id 
	FROM PLATFORM_ABBOTT_Dev.dbo.ODS_T_FORMINSTANCE_GLOBAL
		CROSS APPLY XmlContent.nodes('/root/QuoteAttachmentGrid/row/up_Id') AS XMLTable(up_Id)) B
) C
		GROUP BY  ProcInstId
) fg 
PRINT(N'初始化完成'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END

--初始化xml3 PurBDApplicationSupplierDetails_tmp
IF OBJECT_ID(N'PLATFORM_ABBOTT_Dev.dbo.XML_3', N'U') IS NOT NULL
BEGIN
	PRINT(N'已经初始化过'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select * into PLATFORM_ABBOTT_Dev.dbo.XML_3 
	from (
	select ProcInstId,
		code,Number,UnitPrice,content
	from (
	select ProcInstId,
	 RowData.value('(code/text())[1]', 'nvarchar(50)') AS Code,
    RowData.value('(Number/text())[1]', 'nvarchar(50)') AS Number,
    RowData.value('(UnitPrice/text())[1]', 'nvarchar(50)') AS UnitPrice,
    RowData.value('(content/text())[1]', 'nvarchar(255)') AS Content 
	FROM PLATFORM_ABBOTT_Dev.dbo.ODS_T_FORMINSTANCE_GLOBAL
		CROSS APPLY XmlContent.nodes('/root/OtherGridPanel/row') AS XMLTable(ROWDATA)
		) B		
) fg 
PRINT(N'初始化完成'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END

--初始化xml4 sp_PurPOApplications
IF OBJECT_ID(N'PLATFORM_ABBOTT_Dev.dbo.XML_4', N'U') IS NOT NULL
BEGIN
	PRINT(N'已经初始化过'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select * into PLATFORM_ABBOTT_Dev.dbo.XML_4 
	from (
		SELECT 
    ProcInstId,
    STRING_AGG(up_Id, ', ') WITHIN GROUP (ORDER BY up_Id) AS up_Id
FROM 
    (select ProcInstId,
		up_Id
	from (
	select XmlContent,ProcInstId,
	up_Id.value('.', 'nvarchar(100)') as up_Id 
	from (
		SELECT
		cast (XmlContent as XML) as  XmlContent,
		ProcInstId
		FROM PLATFORM_ABBOTT_Dev.dbo.ODS_T_FORMINSTANCE_GLOBAL)A
		CROSS APPLY XmlContent.nodes('/root/AccessoryGrid/row/up_Id') AS XMLTable(up_Id)) B
) C
		GROUP BY  ProcInstId
) fg 
PRINT(N'初始化完成'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END	

--初始化xml13 sp_PurPOApplicationDetails
IF OBJECT_ID(N'PLATFORM_ABBOTT_Stg.dbo.XML_13', N'U') IS NOT NULL
BEGIN
	PRINT(N'已经初始化过'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select * into PLATFORM_ABBOTT_Stg.dbo.XML_13 
	from (
	select ProcInstId,
		PR_Item_No,PO_Item_No,PRFormCode,Content
	from (
	select ProcInstId,
   RowData.value('(PR_Item_No/text())[1]', 'nvarchar(255)') AS PR_Item_No,
   RowData.value('(PO_Item_No/text())[1]', 'nvarchar(255)') AS PO_Item_No,
   RowData.value('(PRFormCode/text())[1]', 'nvarchar(255)') AS PRFormCode,
   RowData.value('(Content/text())[1]', 'nvarchar(50)') AS Content
	FROM PLATFORM_ABBOTT_Stg.dbo.ODS_T_FORMINSTANCE_GLOBAL
	CROSS APPLY XmlContent.nodes('/root/OrderGridPanel/row') AS XMLTable(ROWDATA)
		) B		
) fg 
PRINT(N'初始化完成'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END

--初始化xml5 sp_PurGRApplications
IF OBJECT_ID(N'PLATFORM_ABBOTT_Dev.dbo.XML_5', N'U') IS NOT NULL
BEGIN
	PRINT(N'已经初始化过'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
select  ProcInstId,
STRING_AGG(CountersignEmpId, ', ') WITHIN GROUP (ORDER BY CountersignEmpId) AS CountersignEmpId 
into PLATFORM_ABBOTT_Dev.dbo.XML_5 
from (
select ProcInstId,CountersignEmpId.value('.', 'nvarchar(100)') as CountersignEmpId  from  PLATFORM_ABBOTT_Dev.dbo.ODS_T_FORMINSTANCE_GLOBAL otfg 
CROSS APPLY XmlContent.nodes('/root/GoodsReceiveApplication_CountersignInfoBlock_MainStore/CountersignEmpId') AS XMLTable(CountersignEmpId))A
group by ProcInstId
PRINT(N'初始化完成'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END;
--初始化xml pa
IF OBJECT_ID(N'PLATFORM_ABBOTT_Dev.dbo.XML_6', N'U') IS NOT NULL
BEGIN
	PRINT(N'已经初始化过'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select * into PLATFORM_ABBOTT_Dev.dbo.XML_6 
	from (
		SELECT 
    ProcInstId,
    STRING_AGG(up_Id, ', ') WITHIN GROUP (ORDER BY up_Id) AS up_Id
FROM 
    (select ProcInstId,
		up_Id
	from (
	select XmlContent,ProcInstId,
	up_Id.value('.', 'nvarchar(100)') as up_Id 
	from PLATFORM_ABBOTT_Dev.dbo.ODS_T_FORMINSTANCE_GLOBAL
		CROSS APPLY XmlContent.nodes('/root/AttachmentBlock/row/up_Id') AS XMLTable(up_Id)) B
) C
		GROUP BY  ProcInstId
) fg 
PRINT(N'初始化完成'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
--初始化xml
IF OBJECT_ID(N'PLATFORM_ABBOTT_Dev.dbo.XML_15', N'U') IS NOT NULL
BEGIN
	PRINT(N'已经初始化过'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select * into PLATFORM_ABBOTT_Dev.dbo.XML_15 
	from (
		SELECT 
    ProcInstId,
    STRING_AGG(up_Id, ', ') WITHIN GROUP (ORDER BY up_Id) AS up_Id
FROM 
    (select ProcInstId,
		up_Id
	from (
	select XmlContent,ProcInstId,
	up_Id.value('.', 'nvarchar(100)') as up_Id 
	from PLATFORM_ABBOTT_Dev.dbo.ODS_T_FORMINSTANCE_GLOBAL
		CROSS APPLY XmlContent.nodes('/root/AttachmentBlock1/row/up_Id') AS XMLTable(up_Id)) B
) C
		GROUP BY  ProcInstId
) fg 
PRINT(N'初始化完成'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END;	

	--初始化xml
IF OBJECT_ID(N'PLATFORM_ABBOTT_Dev.dbo.XML_9', N'U') IS NOT NULL
BEGIN
	PRINT(N'已经初始化过'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select ProcInstId,
	 RowData.value('(goodsName/text())[1]', 'nvarchar(50)') AS goodsName,
	 RowData.value('(orderCount/text())[1]', 'nvarchar(50)') AS orderCount,
	 RowData.value('(receivingWay/text())[1]', 'nvarchar(50)') AS receivingWay,
	 RowData.value('(price/text())[1]', 'nvarchar(50)') AS price,
	 RowData.value('(receivedNum_Per/text())[1]', 'nvarchar(50)') AS receivedNum_Per,
	 RowData.value('(signNumPer/text())[1]', 'nvarchar(50)') AS signNumPer,
	 RowData.value('(signDate/text())[1]', 'nvarchar(50)') AS signDate,
	 RowData.value('(isOnTime/text())[1]', 'nvarchar(50)') AS isOnTime
into PLATFORM_ABBOTT_Dev.dbo.XML_9
FROM 
(select a.ProcInstId,XmlContent from PLATFORM_ABBOTT_Dev.dbo.ods_AUTO_BIZ_T_PaymentApplication_Info a 
join PLATFORM_ABBOTT_Dev.dbo.ODS_T_FORMINSTANCE_GLOBAL b
on a.ProcInstId=b.ProcInstId) A
CROSS APPLY XmlContent.nodes('/root/ReveiceGoodsGridPanel/row') AS XMLTable(RowData)
PRINT(N'初始化完成'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END;

	select 
	 a.ProcInstId,
	 RowData.value('(invoiceNo/text())[1]', 'nvarchar(50)') AS InvoiceNo,
	 RowData.value('(invoiceDate/text())[1]', 'nvarchar(50)') AS InvoiceDate,
	 RowData.value('(singleInvoice_Amount/text())[1]', 'nvarchar(50)') AS SingleInvoice_Amount,
	 RowData.value('(tax_Amount/text())[1]', 'nvarchar(50)') AS tax_Amount,
	 RowData.value('(noTax_Amount/text())[1]', 'nvarchar(50)') AS noTax_Amount
	 into PLATFORM_ABBOTT_Stg.dbo.XML_10
	from PLATFORM_ABBOTT_Stg.dbo.ods_AUTO_BIZ_T_PaymentApplication_Info a 
	join PLATFORM_ABBOTT_Stg.dbo.ODS_T_FORMINSTANCE_GLOBAL b
	on a.ProcInstId =b.ProcInstId 
	CROSS APPLY XmlContent.nodes('/root/InvoiceGridPanel/row') AS XMLTable(RowData)

--初始化xml22  sp_PurBDApplications
IF OBJECT_ID(N'PLATFORM_ABBOTT_Dev.dbo.XML_2', N'U') IS NOT NULL
BEGIN
	PRINT(N'已经初始化过'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select * into PLATFORM_ABBOTT.dbo.XML_22
	from (
		SELECT 
    ProcInstId,
    STRING_AGG(up_Id, ', ') WITHIN GROUP (ORDER BY up_Id) AS up_Id
FROM 
    (select ProcInstId,
		up_Id
	from (
	select ProcInstId,
	up_Id.value('.', 'nvarchar(100)') as up_Id 
	FROM PLATFORM_ABBOTT.dbo.ODS_T_FORMINSTANCE_GLOBAL
		CROSS APPLY XmlContent.nodes('/root/AttachmentBlock/row/up_Id') AS XMLTable(up_Id)) B
) C
		GROUP BY  ProcInstId
) fg 
PRINT(N'初始化完成'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
