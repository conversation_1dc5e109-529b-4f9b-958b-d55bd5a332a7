select  newid() as spk_NexBPMCode,spk_BPMCode,spk_name,spk_chinesevalue,spk_englishvalue,spk_costcentercode,spk_ccentercoder_num,spk_subsidiary,flg 
into #spk_costcentermasterdata
from spk_costcentermasterdata_Tmp 


IF OBJECT_ID(N'dbo.spk_costcentermasterdata', N'U') IS NOT NULL
BEGIN
	update a 
	set a.spk_BPMCode           = b.spk_BPMCode
       ,a.spk_name              = b.spk_name
       ,a.spk_chinesevalue      = b.spk_chinesevalue
       ,a.spk_englishvalue      = b.spk_englishvalue
       ,a.spk_costcentercode    = b.spk_costcentercode
       ,a.spk_ccentercoder_num  = b.spk_ccentercoder_num
       ,a.spk_subsidiary        = b.spk_subsidiary
       ,a.flg                   = b.flg 
    from dbo.spk_costcentermasterdata a
    join #spk_costcentermasterdata b on a.spk_BPMCode = b.spk_BPMCode
    
    insert into dbo.spk_costcentermasterdata
    select a.spk_NexBPMCode
          ,a.spk_BPMCode
          ,a.spk_name
          ,a.spk_chinesevalue
          ,a.spk_englishvalue
          ,a.spk_costcentercode
          ,a.spk_ccentercoder_num
          ,a.spk_subsidiary
          ,a.flg 
	from #spk_costcentermasterdata a
	where NOT EXISTS (SELECT * FROM dbo.spk_costcentermasterdata where spk_BPMCode = a.spk_BPMCode)
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into dbo.spk_costcentermasterdata from #spk_costcentermasterdata
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END