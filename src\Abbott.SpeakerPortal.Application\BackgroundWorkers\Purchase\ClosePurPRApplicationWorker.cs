﻿using System.Threading;
using System.Threading.Tasks;

using Abbott.SpeakerPortal.Contracts.Purchase.PurPRApplication;

using Hangfire;

namespace Abbott.SpeakerPortal.BackgroundWorkers.Purchase
{
    /// <summary>
    /// 采购申请关闭Job
    /// </summary>
    public class ClosePurPRApplicationWorker : SpeakerPortalBackgroundWorkerBase
    {
        public ClosePurPRApplicationWorker()
        {
            CronExpression = Cron.Daily();
        }

        public override async Task DoWorkAsync(CancellationToken cancellationToken = default)
        {
            await LazyServiceProvider.LazyGetService<IPurPRApplicationService>().ClosePRApplication();
        }
    }
}
