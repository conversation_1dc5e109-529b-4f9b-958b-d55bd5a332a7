﻿using Abbott.SpeakerPortal.Enums;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Abbott.SpeakerPortal.Contracts.Approval
{
    public class InitializeApprovalRecordDto
    {
        /// <summary>
        /// 业务表名
        /// </summary>
        /// <value>
        /// The name.
        /// </value>
        public string BusinessFormName { get; set; }

        /// <summary>
        /// 业务单据ID
        /// </summary>
        public Guid BusinessId { get; set; }

        /// <summary>
        /// PP端审批记录ID
        /// </summary>
        public Guid InstanceId { get; set; }

        /// <summary>
        /// 状态
        /// </summary>
        public InitApprovalRecordStatus Status { get; set; }

        /// <summary>
        /// 处理次数
        /// </summary>
        public int Count { get; set; }

        /// <summary>
        /// 最大重试次数
        /// </summary>
        public int MaxRetries { get; set; }

        /// <summary>
        /// 请求内容
        /// </summary>
        public string RequestContent { get; set; }

        /// <summary>
        /// 响应内容
        /// </summary>
        public string ResponseContent { get; set; }
    }
}
