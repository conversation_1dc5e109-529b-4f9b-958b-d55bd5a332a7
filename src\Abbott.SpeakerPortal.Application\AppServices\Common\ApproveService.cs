﻿using Abbott.SpeakerPortal.AppServices.Agent;
using Abbott.SpeakerPortal.AppServices.STicket;
using Abbott.SpeakerPortal.BackgroundWorkers.Bpm;
using Abbott.SpeakerPortal.BackgroundWorkers.BPMEmail;
using Abbott.SpeakerPortal.BackgroundWorkers.Integration.BpmOm;
using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Agent;
using Abbott.SpeakerPortal.Contracts.Budget.FocSubbudget;
using Abbott.SpeakerPortal.Contracts.Budget.Subbudget;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.Approve;
using Abbott.SpeakerPortal.Contracts.Common.BPMEmail;
using Abbott.SpeakerPortal.Contracts.Common.PowerApp;
using Abbott.SpeakerPortal.Contracts.Dataverse;
using Abbott.SpeakerPortal.Contracts.Integration.Dspot;
using Abbott.SpeakerPortal.Contracts.Integration.EpdOlineMeeting;
using Abbott.SpeakerPortal.Contracts.Integration.EpdOlineMeeting.BPM;
using Abbott.SpeakerPortal.Contracts.Integration.Veeva;
using Abbott.SpeakerPortal.Contracts.MSA;
using Abbott.SpeakerPortal.Contracts.OEC.SpeakerAuth;
using Abbott.SpeakerPortal.Contracts.OEC.SpeakerLimit;
using Abbott.SpeakerPortal.Contracts.PostApprovalActions;
using Abbott.SpeakerPortal.Contracts.Purchase.PurGRApplication;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Contracts.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Contracts.ReturnAndExchange;
using Abbott.SpeakerPortal.Contracts.STicket;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Dataverse.Contracts;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.Agent;
using Abbott.SpeakerPortal.Entities.Budget.BdExpense;
using Abbott.SpeakerPortal.Entities.Common.Workflow.WorkflowInstances;
using Abbott.SpeakerPortal.Entities.Common.Workflow.WorkflowTasks;
using Abbott.SpeakerPortal.Entities.EFlow.FOC;
using Abbott.SpeakerPortal.Entities.EFlow.Return;
using Abbott.SpeakerPortal.Entities.EFlow.STicket;
using Abbott.SpeakerPortal.Entities.Integration.Dspot;
using Abbott.SpeakerPortal.Entities.OEC.OECIntercepts;
using Abbott.SpeakerPortal.Entities.OECSpeakerAuthApplys;
using Abbott.SpeakerPortal.Entities.Purchase.PurBDApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurBWApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurGRApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPOApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Entities.User;
using Abbott.SpeakerPortal.Entities.VendorApplicationFinancials;
using Abbott.SpeakerPortal.Entities.VendorApplicationOrgnizations;
using Abbott.SpeakerPortal.Entities.VendorApplicationPersonals;
using Abbott.SpeakerPortal.Entities.VendorApplications;
using Abbott.SpeakerPortal.Entities.VendorBlackLists;
using Abbott.SpeakerPortal.Entities.VendorFinancials;
using Abbott.SpeakerPortal.Entities.VendorOrgnizations;
using Abbott.SpeakerPortal.Entities.VendorPersonals;
using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.OEC.SpeakerLimit;
using Abbott.SpeakerPortal.Redis;
using Abbott.SpeakerPortal.RoleDto;
using Abbott.SpeakerPortal.Utils;
using Abbott.SpeakerPortal.Vendor.Speaker;

using DocumentFormat.OpenXml.Drawing.Diagrams;
using DocumentFormat.OpenXml.Office2010.Excel;
using DocumentFormat.OpenXml.Spreadsheet;
using DocumentFormat.OpenXml.Vml.Spreadsheet;

using Hangfire;

using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ApplicationModels;
using Microsoft.Azure.CognitiveServices.Vision.ComputerVision.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Messages;
using Microsoft.Xrm.Sdk.Query;

using Newtonsoft.Json;

using Senparc.Weixin.WxOpen.AdvancedAPIs.WxApp.WxAppJson;

using StackExchange.Redis;

using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Threading;
using System.Threading.Tasks;

using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using Volo.Abp.ObjectMapping;
using Volo.Abp.SettingManagement;

using static Abbott.SpeakerPortal.Consts.EntitiesNameConsts;
using static Abbott.SpeakerPortal.Consts.WorkflowConsts;
using static Abbott.SpeakerPortal.Enums.OECInterceptTypes;
using static Abbott.SpeakerPortal.Enums.Purchase;
using static Abbott.SpeakerPortal.Enums.PurGRStatus;
using static Abbott.SpeakerPortal.Enums.PurPAStatus;
using static Abbott.SpeakerPortal.Permissions.SpeakerPortalPermissions;
using static System.Runtime.InteropServices.JavaScript.JSType;

using JsonSerializer = System.Text.Json.JsonSerializer;

namespace Abbott.SpeakerPortal.AppServices.Common
{
    public class ApproveService : SpeakerPortalAppService, IApproveService
    {
        private readonly ILogger<ApproveService> _logger;
        private readonly IServiceProvider _serviceProvider;
        private readonly IWorkflowTaskRepository _taskRepository;
        private readonly IRepository<IdentityUser, Guid> _userRepository;
        private IDataverseRepository _dataverseRepository;
        private readonly IConfiguration _configuration;
        /// <summary>
        /// 批量事务的最大允许请求数，微软默认只支持最多同时处理1000个请求，超过则会报错
        /// </summary>
        public static readonly int ExecuteTransactionMaxmumBatchSize = 1000;

        public ApproveService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _logger = serviceProvider.GetService<ILogger<ApproveService>>();
            _taskRepository = serviceProvider.GetService<IWorkflowTaskRepository>();
            _userRepository = serviceProvider.GetService<IRepository<IdentityUser, Guid>>();
            _dataverseRepository = _serviceProvider.GetService<IDataverseRepository>();
            _configuration = _serviceProvider.GetService<IConfiguration>();
        }

        /// <summary>
        /// 查询审批记录
        /// </summary>
        /// <returns></returns>
        public async Task<MessageResult> GetApprovalRecordAsync(Guid requestId)
        {
            var taskQueryable = await LazyServiceProvider.LazyGetService<IWorkflowTaskReadonlyRepository>().GetQueryableAsync();
            var userQueryable = await LazyServiceProvider.LazyGetService<IIdentityUserReadonlyRepository>().GetQueryableAsync();
            var approveStatus = EnumUtil.GetEnumIdValues<ApprovalOperation>().ToDictionary(a => (ApprovalOperation)a.Key, a => a.Value);

            //查询本地-task
            var taskList = taskQueryable.Where(w => w.FormId == requestId)
                .GroupJoin(userQueryable, a => a.ApprovalId, a => a.Id, (a, b) => new { Task = a, Users = b })
                .SelectMany(a => a.Users.DefaultIfEmpty(), (a, b) => new { a.Task, ApprovalUser = b })
                .GroupJoin(userQueryable, a => a.Task.OriginalApprovalId, a => a.Id, (a, b) => new { a.Task, a.ApprovalUser, Users = b })
                .SelectMany(a => a.Users.DefaultIfEmpty(), (a, b) => new { a.Task, a.ApprovalUser, OriginalApprovalUser = b })
                .OrderBy(p => p.Task.ApprovalTime).ToList();

            var paReturns = new List<PAReturnReasonInfoReponseDto>();
            if (taskList.Any(a => NameConsts.PAApplication.Equals(a.Task.FormName)))//PA 单才查询
            {
                var paApplicationService = LazyServiceProvider.LazyGetService<IPurPAApplicationService>();
                paReturns = await paApplicationService.GetPAReturnReasonByPAIdAsync(requestId);
            }
            var res = taskList.Select(s =>
            {
                var sectionName = $"{s.Task.WorkStep}";
                //if (s.Task.OriginalApprovalId != Guid.Empty && s.Task.OriginalApprovalId != s.Task.ApprovalId && !string.IsNullOrEmpty(s.OriginalApprovalUser?.Name))
                //    sectionName += $"--由【{s.ApprovalUser?.Name}】代理【{s.OriginalApprovalUser?.Name}】操作";
                return new ApprovalRecordDto
                {
                    InstanceId = s.Task.InstanceId,
                    IsApproval = s.Task.Status == ApprovalOperation.Start ? 0 : 1,
                    WorkFlowName = sectionName,
                    CreatorId = s.Task.ApprovalId,
                    UserName = s.ApprovalUser?.Name,
                    UserEmail = s.ApprovalUser?.Email,
                    SubmitOrApprovalTime = s.Task.ApprovalTime.ToString("yyyy-MM-dd HH:mm"),
                    ApprovalTime = s.Task.ApprovalTime,
                    Remark = s.Task.Remark,
                    Status = s.Task.Status,
                    StatusName = approveStatus[s.Task.Status],
                    WorkStep = s.Task.WorkStep,
                    StepNo = s.Task.StepNo,
                    OriginalApprovalId = s.Task.OriginalApprovalId,
                    OriginalApprovalName = s.OriginalApprovalUser?.Name,
                    OriginalApprovalEmail = s.OriginalApprovalUser?.Email,
                    PAReturnReasonInfos = paReturns.Where(a => a.WorkFlowTaskId == s.Task.Id).ToList()
                };
            }).ToList();

            //查询PP-task,拼接待审批的节点信息
            var queryTask = new QueryExpression("spk_workflowtask");
            queryTask.ColumnSet.AddColumns("spk_businessformid", "spk_name", "spk_approvalstatus", "spk_remark", "spk_businessformid", "spk_workflowstep", "createdon", "spk_approver", "spk_workflowinstance");
            queryTask.Criteria.AddCondition(new ConditionExpression("statecode", ConditionOperator.Equal, 0));
            queryTask.Criteria.AddCondition(new ConditionExpression("spk_businessformid", ConditionOperator.Equal, requestId.ToString()));
            queryTask.Criteria.AddCondition(new ConditionExpression("spk_approvalstatus", ConditionOperator.Equal, (int)ApprovalPowerAppStatus.PendingForApproval));
            LinkEntity linkStep = queryTask.AddLink("spk_workflowstep", "spk_workflowstep", "spk_workflowstepid", JoinOperator.LeftOuter);
            linkStep.Columns.AddColumns("spk_name", "spk_stepdescriptionname");
            linkStep.EntityAlias = "lines1";
            var entitieTasks = _dataverseRepository.DataverseClient.GetAllEntitiesAsync(queryTask).Result.ToList();
            var operationResult = new UpdateApprovalResponseDto();
            if (entitieTasks.Count != 0)
            {
                string userNames = "";
                if (entitieTasks[0].GetAttributeValue<AliasedValue>("lines1.spk_name")?.Value?.ToString() != "Step1-财务初审")
                {
                    userNames = entitieTasks.Select(p => p.GetAttributeValue<EntityReference>("spk_approver")?.Name).JoinAsString(";");
                }
                res.Add(new ApprovalRecordDto
                {
                    IsApproval = 1,
                    WorkFlowName = entitieTasks[0].GetAttributeValue<AliasedValue>("lines1.spk_stepdescriptionname")?.Value?.ToString(),
                    UserName = userNames,
                    SubmitOrApprovalTime = "-",
                    Remark = "-",
                    Status = ApprovalOperation.Wait,
                    StatusName = approveStatus[ApprovalOperation.Wait],
                });
            }

            return MessageResult.SuccessResult(res);
        }

        /// <summary>
        /// 根据单据ID 获取当前审批人
        /// </summary>
        /// <param name="formId"></param>
        /// <returns></returns>
        public List<ApproverDto> GetApproverByFormId(Guid formId)
        {
            var result = new List<ApproverDto>();
            //查询PP-task,拼接待审批的节点信息
            var queryTask = new QueryExpression("spk_workflowtask");
            queryTask.ColumnSet.AddColumns("spk_businessformid", "spk_name", "spk_approvalstatus", "spk_remark", "spk_businessformid", "spk_workflowstep", "createdon", "spk_approver", "spk_workflowinstance");
            queryTask.Criteria.AddCondition(new ConditionExpression("statecode", ConditionOperator.Equal, 0));
            queryTask.Criteria.AddCondition(new ConditionExpression("spk_businessformid", ConditionOperator.Equal, formId.ToString()));
            queryTask.Criteria.AddCondition(new ConditionExpression("spk_approvalstatus", ConditionOperator.Equal, (int)ApprovalPowerAppStatus.PendingForApproval));
            LinkEntity linkStep = queryTask.AddLink("spk_workflowstep", "spk_workflowstep", "spk_workflowstepid", JoinOperator.LeftOuter);
            linkStep.Columns.AddColumns("spk_name", "spk_stepdescriptionname");
            linkStep.EntityAlias = "lines1";
            var entitieTasks = _dataverseRepository.DataverseClient.GetAllEntitiesAsync(queryTask).Result.ToList();
            if (entitieTasks.Count != 0)
            {
                foreach (var item in entitieTasks)
                {
                    var userId = item.GetAttributeValue<EntityReference>("spk_approver").Id;
                    var userName = item.GetAttributeValue<EntityReference>("spk_approver")?.Name;
                    result.Add(new ApproverDto
                    {
                        Id = userId,
                        Name = userName,
                    });
                }
            }
            return result;
        }

        /// <summary>
        /// 添加审批记录
        /// </summary>
        /// <returns></returns>
        public async Task<(bool, Guid?)> AddApprovalRecordAsync(AddApprovalRecordDto request)
        {
            int maxRetries = 3;
            int retryCount = 0;
            TimeSpan delay = TimeSpan.FromSeconds(2);

            while (true)
            {
                try
                {
                    var taskEntity = ObjectMapper.Map<AddApprovalRecordDto, WorkflowTask>(request);
                    // 如果是待审批，则判断是第一次发起审批还是多次重新发起审批
                    if (request.Status == ApprovalOperation.Start)
                    {
                        var task = await _taskRepository.FirstOrDefaultAsync(f => f.FormId == request.FormId);
                        taskEntity.WorkStep = task == null ? "发起申请" : "重新发起申请";
                        if (EntitiesNameConsts.NameConsts.GRApplication.Equals(request.FormName))//收货单独处理
                            taskEntity.WorkStep = "收货";
                    }

                    var taskInsert = await _taskRepository.InsertAsync(taskEntity);
                    return (true, taskInsert.Id);
                }
                catch (OperationCanceledException ex)
                {
                    string requestJson = "";
                    try
                    {
                        requestJson = JsonConvert.SerializeObject(request, Formatting.Indented);
                    }
                    catch
                    {
                        requestJson = "Failed to serialize request.";
                    }
                    _logger.LogError($"ApproveService's AddApprovalRecordAsync has an error : {ex.Message}, StackTrace: {ex.StackTrace}, Request Data: {requestJson}");
                    if (ex.InnerException != null)
                    {
                        _logger.LogError($"ApproveService's AddApprovalRecordAsync has an error : {ex.InnerException.Message} , StackTrace: {ex.InnerException.StackTrace}, Request Data: {requestJson}");
                    }
                }
                catch (Exception ex)
                {
                    string requestJson = "";
                    try
                    {
                        requestJson = JsonConvert.SerializeObject(request, Formatting.Indented);
                    }
                    catch
                    {
                        requestJson = "Failed to serialize request.";
                    }
                    _logger.LogError($"ApproveService's AddApprovalRecordAsync has an error : {ex.Message},StackTrace: {ex.StackTrace}, Request Data: {requestJson}");
                    if (ex.InnerException != null)
                    {
                        _logger.LogError($"ApproveService's AddApprovalRecordAsync has an error :{ex.InnerException.Message},StackTrace: {ex.StackTrace}, Request Data: {requestJson}");
                    }
                }

                retryCount++;
                if (retryCount >= maxRetries)
                {
                    break;
                }

                await Task.Delay(delay); // 等待后重试
            }

            return (false, null); // 返回失败标识
        }
        /// <summary>
        /// 添加审批记录
        /// </summary>
        /// <returns></returns>
        public async Task<bool> AddApprovalRecordAsync(List<AddApprovalRecordDto> requests)
        {
            int maxRetries = 3;
            int retryCount = 0;
            TimeSpan delay = TimeSpan.FromSeconds(2);

            while (true)
            {
                try
                {
                    var taskEntitys = ObjectMapper.Map<List<AddApprovalRecordDto>, List<WorkflowTask>>(requests);
                    foreach (var request in taskEntitys)
                    {
                        if (request.Status == ApprovalOperation.Start)
                        {
                            var task = await _taskRepository.FirstOrDefaultAsync(f => f.FormId == request.FormId);
                            request.WorkStep = task == null ? "发起申请" : "重新发起申请";
                        }
                    }

                    await _taskRepository.InsertManyAsync(taskEntitys);
                    return true;
                }
                catch (OperationCanceledException ex)
                {
                    string requestJson = "[]";
                    try
                    {
                        requestJson = JsonConvert.SerializeObject(requests, Formatting.Indented);
                    }
                    catch
                    {
                        requestJson = "Failed to serialize requests.";
                    }
                    _logger.LogError($"ApproveService's AddApprovalRecordAsync has an error : {ex.Message},StackTrace:  {ex.StackTrace}  Request Data: {requestJson}");
                    if (ex.InnerException != null)
                    {
                        _logger.LogError($"ApproveService's AddApprovalRecordAsync has an error :{ex.InnerException.Message},StackTrace: {ex.StackTrace}, Request Data: {requestJson}");
                    }
                }
                catch (Exception ex)
                {
                    string requestJson = "[]";
                    try
                    {
                        requestJson = JsonConvert.SerializeObject(requests, Formatting.Indented);
                    }
                    catch
                    {
                        requestJson = "Failed to serialize requests.";
                    }
                    _logger.LogError($"ApproveService's AddApprovalRecordAsync has an error : {ex.Message},StackTrace:  {ex.StackTrace}  Request Data: {requestJson}");
                    if (ex.InnerException != null)
                    {
                        _logger.LogError($"ApproveService's AddApprovalRecordAsync has an error :{ex.InnerException.Message},StackTrace: {ex.StackTrace}, Request Data: {requestJson}");
                    }
                }

                retryCount++;
                if (retryCount >= maxRetries)
                {
                    break;
                }

                await Task.Delay(delay); // 等待后重试
            }

            return false; // 返回失败标识
        }
        #region 发起审批
        /// <summary>
        /// 发起审批 (后台任务执行)
        /// </summary>
        /// <param name="instanceId"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<(bool, string)> InitiateApprovalAsync(Guid instanceId, CreateApprovalDto request)
        {
            try
            {
                //查询审批类型ID
                var query = new QueryExpression("spk_workflowtype");
                query.ColumnSet.AddColumns("spk_name");
                query.Criteria.AddCondition(new ConditionExpression("spk_workflowtypename", ConditionOperator.Equal, (int)request.WorkflowType));
                var datas = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);
                if (!datas.Any()) return (false, "Approval type does not exist");

                //查询审批是否有重复
                var queryInstances = new QueryExpression("spk_workflowinstance");
                queryInstances.ColumnSet.AddColumns("spk_name");
                queryInstances.Criteria.AddCondition(new ConditionExpression("spk_businessformid", ConditionOperator.Equal, request.BusinessFormId));
                queryInstances.Criteria.AddCondition(new ConditionExpression("spk_status", ConditionOperator.Equal, (int)ApprovalPowerAppStatus.PendingForApproval));
                var datasInstances = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(queryInstances);
                if (datasInstances.Any()) return (false, "Create duplicate approvals");

                var submitterId = request.Submitter;
                #region 代理人提交流程实例时，spk_submitter设置为原操作人，转办人提交时就用转办人作为提交人
                if (!string.IsNullOrEmpty(request.Submitter) && request.Submitter != request.OriginalApprovalId.ToString())
                {
                    var formCategory = WorkflowTypeToFormCategory(request.WorkflowType);
                    if (formCategory.HasValue)
                    {
                        var originalOperators = await LazyServiceProvider.LazyGetService<IAgencyService>().GetOriginalOperators(new GetAgentOperatorsRequestDto
                        {
                            AgentOperatorId = Guid.Parse(request.Submitter),
                            BusinessTypeCategory = formCategory.Value
                        });
                        if (originalOperators != null && originalOperators.Select(x => x.Key).Contains(request.OriginalApprovalId))//有代理则认为是代理人
                            submitterId = request.OriginalApprovalId.ToString();
                    }
                }
                #endregion

                //添加审批任务
                var time = DateTime.Now.ToString("yyMMdd-hhmmss");
                var approveInstances = new Entity("spk_workflowinstance");
                approveInstances["spk_workflowinstanceid"] = instanceId;
                approveInstances["spk_name"] = request.InstanceName;
                approveInstances["spk_bu"] = new EntityReference("spk_organizationalmasterdata", Guid.Parse(request.Department));
                approveInstances["spk_businessformid"] = request.BusinessFormId;
                approveInstances["spk_businessformno"] = request.BusinessFormNo;
                approveInstances["spk_firstapprover"] = request.FirstApprover;
                approveInstances["spk_status"] = new OptionSetValue((int)request.Status);
                approveInstances["spk_submitter"] = new EntityReference("spk_staffmasterdata", Guid.Parse(submitterId));
                approveInstances["spk_workflowyype"] = new EntityReference("spk_workflowtype", datas.ToList().First().Id);
                approveInstances["spk_businessformdata"] = request.FormData;

                //创建审批
                var createId = await _dataverseRepository.DataverseClient.CreateAsync(approveInstances);

                return (true, "Success");
            }
            catch (Exception ex)
            {
                _logger.LogError($"ApproveService's InitiateApprovalAsync has an error : {ex.Message}");
                return (false, ex.StackTrace);
            }
        }

        /// <summary>
        /// 流程类型转表单类型
        /// </summary>
        /// <param name="workflowType"></param>
        /// <returns></returns>
        private ResignationTransfer.TaskFormCategory? WorkflowTypeToFormCategory(WorkflowTypeName workflowType)
        {
            var map = FormCategoryWorkflowMapping.FormCategoryWorkflowMap;
            ResignationTransfer.TaskFormCategory? formCategory = null;
            foreach (var kvp in map)
            {
                if (kvp.Value.Length == 0)
                    continue;
                if (kvp.Value.Any(x => x == workflowType))
                {
                    formCategory = kvp.Key;
                    break;
                }
            }
            return formCategory;
        }
        /// <summary>
        /// 添加审批记录 和 记录代理历史
        /// </summary>
        /// <param name="instanceId"></param>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task AddApprovalRecordAndAgentAsync(Guid instanceId, CreateApprovalDto request, bool isAddRecord = true)
        {
            if (isAddRecord)
            {
                //添加审批记录
                await AddApprovalRecordAsync(new AddApprovalRecordDto
                {
                    InstanceId = instanceId,
                    Name = request.InstanceName,
                    Status = ApprovalOperation.Start,
                    Remark = request.Remark,
                    FormId = Guid.Parse(request.BusinessFormId),
                    FormName = request.BusinessFormName,
                    ApprovalTime = DateTime.UtcNow.AddHours(8),
                    ApprovalId = Guid.Parse(request.Submitter),
                    OriginalApprovalId = request.OriginalApprovalId
                });
            }
            #region 记录代理历史-提交
            var requestDto = new CreateAgentHistoryRequestDto
            {
                SubmitterId = Guid.Parse(request.Submitter),
                WorkflowType = request.WorkflowType,
                BusinessTypeCategory = WorkflowTypeToFormCategory(request.WorkflowType),
                FormId = Guid.Parse(request.BusinessFormId),
                FormNo = request.BusinessFormNo
            };
            await CreateAgentHistory([requestDto]);
            #endregion
        }

        /// <summary>
        /// 发起审批
        /// </summary>
        /// <returns></returns>
        public async Task<bool> InitiateApprovalAsync(CreateApprovalDto request)
        {
            try
            {
                //查询审批类型ID
                var query = new QueryExpression("spk_workflowtype");
                query.ColumnSet.AddColumns("spk_name");
                query.Criteria.AddCondition(new ConditionExpression("spk_workflowtypename", ConditionOperator.Equal, (int)request.WorkflowType));
                var datas = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);
                if (!datas.Any()) return false;

                //查询审批是否有重复
                var queryInstances = new QueryExpression("spk_workflowinstance");
                queryInstances.ColumnSet.AddColumns("spk_name");
                queryInstances.Criteria.AddCondition(new ConditionExpression("spk_businessformid", ConditionOperator.Equal, request.BusinessFormId));
                queryInstances.Criteria.AddCondition(new ConditionExpression("spk_status", ConditionOperator.Equal, (int)ApprovalPowerAppStatus.PendingForApproval));
                var datasInstances = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(queryInstances);
                if (datasInstances.Any()) return false;

                //添加审批任务
                var time = DateTime.Now.ToString("yyMMdd-hhmmss");
                var approveInstances = new Entity("spk_workflowinstance");
                approveInstances["spk_name"] = $"{request.Name}-{time}";
                approveInstances["spk_bu"] = new EntityReference("spk_organizationalmasterdata", Guid.Parse(request.Department));
                approveInstances["spk_businessformid"] = request.BusinessFormId;
                approveInstances["spk_businessformno"] = request.BusinessFormNo;
                approveInstances["spk_firstapprover"] = request.FirstApprover;
                approveInstances["spk_status"] = new OptionSetValue((int)request.Status);
                approveInstances["spk_submitter"] = new EntityReference("spk_staffmasterdata", Guid.Parse(request.Submitter));
                approveInstances["spk_workflowyype"] = new EntityReference("spk_workflowtype", datas.ToList().First().Id);
                approveInstances["spk_businessformdata"] = request.FormData;

                //创建审批
                var createId = await _dataverseRepository.DataverseClient.CreateAsync(approveInstances);
                //添加审批记录
                await AddApprovalRecordAsync(new AddApprovalRecordDto
                {
                    InstanceId = createId,
                    Name = $"{request.Name}-{time}",
                    Status = ApprovalOperation.Start,
                    Remark = request.Remark,
                    FormId = Guid.Parse(request.BusinessFormId),
                    FormName = request.BusinessFormName,
                    ApprovalTime = DateTime.UtcNow.AddHours(8),
                    ApprovalId = Guid.Parse(request.Submitter),
                    OriginalApprovalId = request.OriginalApprovalId
                });

                #region 记录代理历史-提交
                var requestDto = new CreateAgentHistoryRequestDto
                {
                    SubmitterId = Guid.Parse(request.Submitter),
                    WorkflowType = request.WorkflowType,
                    BusinessTypeCategory = WorkflowTypeToFormCategory(request.WorkflowType),
                    FormId = Guid.Parse(request.BusinessFormId),
                    FormNo = request.BusinessFormNo
                };
                await CreateAgentHistory([requestDto]);
                #endregion

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"ApproveService's InitiateApprovalAsync has an error : {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 发起审批
        /// </summary>
        /// <returns></returns>
        public async Task<bool> InitiateApprovalAsync(List<CreateApprovalDto> requests)
        {
            try
            {
                //查询审批类型ID
                var query = new QueryExpression("spk_workflowtype");
                query.ColumnSet.AddColumns("spk_name");
                query.Criteria.AddCondition(new ConditionExpression("spk_workflowtypename", ConditionOperator.Equal, (int)requests[0].WorkflowType));
                var datas = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(query);
                if (datas == null) return false;
                //添加审批任务
                var time = DateTime.Now.ToString("yyMMdd-hhmmss");
                //List<AddApprovalRecordDto> records = new List<AddApprovalRecordDto>();
                EntityCollection entityCollection = new EntityCollection();
                var TypeId = datas.ToList().First().Id;
                List<CreateAgentHistoryRequestDto> agentHistory = new List<CreateAgentHistoryRequestDto>();
                foreach (var data in requests)
                {
                    var Id = Guid.NewGuid();
                    var approveInstances = new Entity("spk_workflowinstance");
                    approveInstances.Id = Id;
                    approveInstances["spk_name"] = $"{data.Name}-{time}";
                    approveInstances["spk_bu"] = new EntityReference("spk_organizationalmasterdata", Guid.Parse(data.Department));
                    approveInstances["spk_businessformid"] = data.BusinessFormId;
                    approveInstances["spk_businessformno"] = data.BusinessFormNo;
                    approveInstances["spk_firstapprover"] = data.FirstApprover;
                    approveInstances["spk_status"] = new OptionSetValue((int)data.Status);
                    approveInstances["spk_submitter"] = new EntityReference("spk_staffmasterdata", Guid.Parse(data.Submitter));
                    approveInstances["spk_workflowyype"] = new EntityReference("spk_workflowtype", TypeId);
                    approveInstances["spk_businessformdata"] = data.FormData;
                    entityCollection.Entities.Add(approveInstances);
                    //records.Add(new AddApprovalRecordDto
                    //{
                    //    InstanceId = Id,
                    //    Name = $"{data.Name}-{time}",
                    //    Status = ApprovalOperation.Start,
                    //    Remark = data.Remark,
                    //    FormId = Guid.Parse(data.BusinessFormId),
                    //    FormName = data.BusinessFormName,
                    //    ApprovalTime = DateTime.UtcNow.AddHours(8),
                    //    ApprovalId = Guid.Parse(data.Submitter),
                    //    OriginalApprovalId = data.OriginalApprovalId
                    //});

                    agentHistory.Add(new CreateAgentHistoryRequestDto
                    {
                        SubmitterId = Guid.Parse(data.Submitter),
                        WorkflowType = data.WorkflowType,
                        BusinessTypeCategory = WorkflowTypeToFormCategory(data.WorkflowType),
                        FormId = Guid.Parse(data.BusinessFormId),
                        FormNo = data.BusinessFormNo
                    });
                }
                //创建审批
                var isSave = TransactionRequest(entityCollection, null, null);
                //添加审批记录
                //await AddApprovalRecordAsync(records);
                #region 记录代理历史-提交
                await CreateAgentHistory(agentHistory);
                #endregion
                return isSave;
            }
            catch (Exception ex)
            {
                _logger.LogError($"ApproveService's InitiateApprovalAsync has an error : {ex.Message}");
                return false;
            }
        }

        #endregion
        /// <summary>
        /// 根据业务Id和人员Id检测是否有带审批任务
        /// </summary>
        /// <param name="businessFormId"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        public async Task<MessageResult> JudgeIfHasPendingTask(Guid businessFormId, Guid userId)
        {
            //查询审批task
            var queryTask = new QueryExpression("spk_workflowtask");
            queryTask.ColumnSet.AddColumns("spk_businessformid", "spk_name", "spk_approvalstatus", "spk_workflowstep", "spk_approver");
            queryTask.Criteria.AddCondition(new ConditionExpression("statecode", ConditionOperator.Equal, 0));
            queryTask.Criteria.AddCondition(new ConditionExpression("spk_businessformid", ConditionOperator.Equal, businessFormId.ToString()));
            queryTask.Criteria.AddCondition(new ConditionExpression("spk_approver", ConditionOperator.Equal, userId));
            queryTask.Criteria.AddCondition(new ConditionExpression("spk_approvalstatus", ConditionOperator.Equal, (int)ApprovalPowerAppStatus.PendingForApproval));

            var entitieTasks = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(queryTask);
            if (entitieTasks.Count() != 1)
                return MessageResult.FailureResult("未查询到相关数据或数据有误");

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 审批操作(同意，拒绝，退回-退回包含退回初始和退回某个节点)
        /// </summary>
        /// <returns></returns>
        public async Task<MessageResult> ApprovalOperationAsync(List<UpdateApprovalDto> request, bool isRobot = false, bool isInterrupt = true)
        {
            EntityCollection updateEntities = new EntityCollection();
            var responses = new List<UpdateApprovalResponseDto>();
            var agentHistory = new List<CreateAgentHistoryRequestDto>();
            var taskName = string.Empty;
            var spkStep = string.Empty;
            Guid? originalApproverId = null;

            try
            {
                foreach (var item in request)
                {
                    //查询审批task
                    var queryTask = new QueryExpression("spk_workflowtask");
                    queryTask.ColumnSet.AddColumns("spk_businessformid", "spk_name", "spk_approvalstatus", "spk_remark", "spk_workflowstep", "createdon", "spk_approver", "spk_originalapprover", "spk_workflowinstance", "spk_nextdatas");
                    queryTask.Criteria.AddCondition(new ConditionExpression("statecode", ConditionOperator.Equal, 0));
                    queryTask.Criteria.AddCondition(new ConditionExpression("spk_businessformid", ConditionOperator.Equal, item.BusinessFormId));
                    if (isRobot)
                    {
                        queryTask.Criteria.AddCondition(new ConditionExpression("spk_approver", ConditionOperator.Null));
                    }
                    else
                    {
                        queryTask.Criteria.AddCondition(new ConditionExpression("spk_approver", ConditionOperator.Equal, item.Submitter));
                    }
                    queryTask.Criteria.AddCondition(new ConditionExpression("spk_approvalstatus", ConditionOperator.Equal, (int)ApprovalPowerAppStatus.PendingForApproval));
                    LinkEntity linkStep = queryTask.AddLink("spk_workflowstep", "spk_workflowstep", "spk_workflowstepid", JoinOperator.LeftOuter);
                    linkStep.Columns.AddColumns("spk_name", "spk_stepdescriptionname", "spk_step", "spk_workflowtype");
                    linkStep.EntityAlias = "lines1";
                    LinkEntity linkType = linkStep.AddLink("spk_workflowtype", "spk_workflowtype", "spk_workflowtypeid", JoinOperator.LeftOuter);
                    linkType.Columns.AddColumns("spk_workflowtypename");
                    linkType.EntityAlias = "lines2";
                    var entitieTasks = _dataverseRepository.DataverseClient.GetAllEntitiesAsync(queryTask).Result.ToList();

                    if (entitieTasks == null || entitieTasks.Count != 1)
                    {
                        _logger.LogError($"VeevaDcrCall,Batch,未查询到相关数据或数据有误,BusinessFormId:{item.BusinessFormId}.");//测试日志，veeva dcr有时不自动发起，找到问题后再删除
                        if (isInterrupt)
                            return MessageResult.FailureResult("未查询到相关数据或数据有误");
                        else
                        {
                            responses.Add(new UpdateApprovalResponseDto { BusinessFormId = item.BusinessFormId, OperationMessage = "未查询到相关数据或数据有误" });
                            continue;
                        }
                    }

                    var workflowTypeOpt = (OptionSetValue)entitieTasks[0].GetAttributeValue<AliasedValue>("lines2.spk_workflowtypename")?.Value;
                    var workflowTypeName = (WorkflowTypeName)workflowTypeOpt.Value;
                    spkStep = entitieTasks[0].GetAttributeValue<AliasedValue>("lines1.spk_step")?.Value.ToString();
                    item.StepNo = spkStep;

                    //点击批准的时候才执行SOD验证
                    if (item.OperationStatus == ApprovalOperation.Approved && !isRobot)
                    {
                        //PR SOD 校验
                        var sod = SODApproveVerification(workflowTypeName, item.BusinessFormId, spkStep);
                        if (!sod.Item1)
                            return MessageResult.FailureResult(sod.Item2);

                        //采购流程审批时，记录expense审批人或者财务审批人
                        if (workflowTypeName == WorkflowTypeName.PurchaseRequest)
                        {
                            var messageResult = await LazyServiceProvider.LazyGetService<IPurPRApplicationService>().RecordExpenseOrFinanceProcessor(item);
                            if (!messageResult.Success)
                                return MessageResult.FailureResult();
                        }
                    }

                    //处理审批任务
                    switch (item.OperationStatus)
                    {
                        case ApprovalOperation.Approved:
                            entitieTasks[0]["spk_approvalstatus"] = new OptionSetValue((int)ApprovalPowerAppStatus.Approved);
                            break;
                        case ApprovalOperation.Rejected:
                            entitieTasks[0]["spk_approvalstatus"] = new OptionSetValue((int)ApprovalPowerAppStatus.Rejected);
                            break;
                        case ApprovalOperation.Withdraw:
                        case ApprovalOperation.Amend:
                        case ApprovalOperation.ReturnToDPSCheck:
                            entitieTasks[0]["spk_approvalstatus"] = new OptionSetValue((int)ApprovalPowerAppStatus.Denied);
                            break;
                        //case ApprovalOperation.Recall:
                        //	entitieTasks[0]["spk_approvalstatus"] = new OptionSetValue((int)ApprovalPowerAppStatus.Revoked);
                        //	break;
                        default:
                            break;
                    }
                    if (item.NextApprover != null)
                        entitieTasks[0]["spk_nextapprover"] = new EntityReference("spk_staffmasterdata", (Guid)item.NextApprover);
                    entitieTasks[0]["spk_remark"] = item.Remark;
                    entitieTasks[0]["spk_navigationtostep"] = item.Step;//退回时需要的参数  //操作审批
                    if (!string.IsNullOrWhiteSpace(item.BusinessFormNextData))
                    {
                        entitieTasks[0]["spk_nextdatas"] = item.BusinessFormNextData;
                    }
                    updateEntities.Entities.Add(entitieTasks[0]);

                    responses.Add(new UpdateApprovalResponseDto { BusinessFormId = item.BusinessFormId, IsSuccess = true });

                    item.Name = entitieTasks[0].GetAttributeValue<EntityReference>("spk_workflowinstance").Name;
                    item.WorkflowInstanceId = entitieTasks[0].GetAttributeValue<EntityReference>("spk_workflowinstance").Id;
                    //task节点名称
                    taskName = entitieTasks[0].GetAttributeValue<AliasedValue>("lines1.spk_stepdescriptionname")?.Value.ToString();
                    originalApproverId = entitieTasks[0].GetAttributeValue<EntityReference>("spk_originalapprover")?.Id;

                    #region 记录代理历史-审批操作
                    if (!isRobot && originalApproverId.HasValue && CurrentUser.Id.HasValue && originalApproverId.Value != CurrentUser.Id.Value)
                    {
                        var workflowType = item.WorkflowType.HasValue ? item.WorkflowType.Value : workflowTypeName;
                        agentHistory.Add(new CreateAgentHistoryRequestDto
                        {
                            SubmitterId = originalApproverId.Value,
                            WorkflowType = workflowType,
                            BusinessTypeCategory = WorkflowTypeToFormCategory(workflowType),
                            FormId = Guid.Parse(item.BusinessFormId),
                            FormNo = item.BusinessFormNo
                        });
                    }
                    #endregion
                }

                //批量执行
                var result = TransactionRequest(null, updateEntities, null);
                if (!result)
                {
                    _logger.LogError($"VeevaDcrCall,Batch,批量操作失败，请联系管理员");//测试日志，veeva dcr有时不自动发起，找到问题后再删除
                    return MessageResult.FailureResult("批量操作失败，请联系管理员");
                }
                else if (agentHistory.Count > 0)
                    await CreateAgentHistoryApprovalTask(agentHistory);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"ApproveService's ApprovalOperationAsync has an error : {ex.Message}");
                return MessageResult.FailureResult("审批操作失败，请联系管理员");
            }

            ////如果讲者信息200步审批通过执行逻辑
            //if (request.First().OperationStatus == ApprovalOperation.Approved && spkStep == "200")
            //{
            //    //调用医师信息验证
            //    PhysicianInformationVerification(request, spkStep);
            //}

            try
            {
                //执行成功后写入业务数据库的审批记录表
                foreach (var item in request)
                {
                    //没有成功的，说明并没有approve则不增加审批记录
                    if (!responses.Any(a => a.IsSuccess && string.Equals(a.BusinessFormId, item.BusinessFormId, StringComparison.CurrentCultureIgnoreCase)))
                        continue;

                    //添加审批记录
                    var time = DateTime.Now.ToString("yyMMdd-hhmmss");
                    var approvalRecord = new AddApprovalRecordDto
                    {
                        InstanceId = (Guid)item.WorkflowInstanceId,
                        Name = $"{item.Name}",
                        Remark = item.Remark,
                        FormId = Guid.Parse(item.BusinessFormId),
                        ApprovalTime = DateTime.UtcNow.AddHours(8),
                        ApprovalId = item.Submitter,
                        OriginalApprovalId = originalApproverId.HasValue ? originalApproverId.Value : item.Submitter,
                        Status = item.OperationStatus,
                        WorkStep = taskName,
                        StepNo = item.StepNo,
                    };
                    await AddApprovalRecordAsync(approvalRecord);
                }
                return MessageResult.SuccessResult(responses);
            }
            catch (Exception ex)
            {
                _logger.LogError($"ApproveService's ApprovalOperationBussinessAsync has an error : {ex.Message}");
                return MessageResult.FailureResult("审批操作记录失败，请联系管理员");
                //考虑失败后放入redis中定时处理 TODO
            }

        }

        /// <summary>
        /// 审批操作(同意，拒绝，退回-退回包含退回初始和退回某个节点);单个，返回task的id,付款申请退回需要关联
        /// </summary>
        /// <param name="request"></param>
        /// <param name="isRobot"></param>
        /// <returns></returns>
        public async Task<MessageResult> ApprovalOperationAsync(UpdateApprovalDto request, bool isRobot = false)
        {
            EntityCollection updateEntities = new EntityCollection();
            var responses = new List<UpdateApprovalResponseDto>();
            var taskName = string.Empty;
            var spkStep = string.Empty;
            Guid? originalApproverId = null;
            try
            {
                //查询审批task
                var queryTask = new QueryExpression("spk_workflowtask");
                queryTask.ColumnSet.AddColumns("spk_businessformid", "spk_name", "spk_approvalstatus", "spk_remark", "spk_workflowstep", "createdon", "spk_approver", "spk_originalapprover", "spk_workflowinstance", "spk_nextdatas");
                queryTask.Criteria.AddCondition(new ConditionExpression("statecode", ConditionOperator.Equal, 0));
                queryTask.Criteria.AddCondition(new ConditionExpression("spk_businessformid", ConditionOperator.Equal, request.BusinessFormId));
                if (isRobot)
                {
                    queryTask.Criteria.AddCondition(new ConditionExpression("spk_approver", ConditionOperator.Null));
                }
                else
                {
                    queryTask.Criteria.AddCondition(new ConditionExpression("spk_approver", ConditionOperator.Equal, request.Submitter));
                }
                queryTask.Criteria.AddCondition(new ConditionExpression("spk_approvalstatus", ConditionOperator.Equal, (int)ApprovalPowerAppStatus.PendingForApproval));
                LinkEntity linkStep = queryTask.AddLink("spk_workflowstep", "spk_workflowstep", "spk_workflowstepid", JoinOperator.LeftOuter);
                linkStep.Columns.AddColumns("spk_name", "spk_stepdescriptionname", "spk_step");
                linkStep.EntityAlias = "lines1";
                LinkEntity linkType = linkStep.AddLink("spk_workflowtype", "spk_workflowtype", "spk_workflowtypeid", JoinOperator.LeftOuter);
                linkType.Columns.AddColumns("spk_workflowtypename");
                linkType.EntityAlias = "lines2";
                var entitieTasks = _dataverseRepository.DataverseClient.GetAllEntitiesAsync(queryTask).Result.ToList();

                if (entitieTasks == null || entitieTasks.Count != 1)
                {
                    _logger.LogWarning($"VeevaDcrCall,Single,未查询到相关数据或数据有误");//测试日志，veeva dcr有时不自动发起，找到问题后再删除
                    return MessageResult.FailureResult("未查询到相关数据或数据有误");
                }

                var workflowTypeOpt = (OptionSetValue)entitieTasks[0].GetAttributeValue<AliasedValue>("lines2.spk_workflowtypename")?.Value;
                var workflowTypeName = (WorkflowTypeName)workflowTypeOpt.Value;

                //点击批准的时候才执行SOD验证
                if (request.OperationStatus == ApprovalOperation.Approved)
                {
                    //SOD 校验
                    var sod = SODApproveVerification(workflowTypeName, request.BusinessFormId, entitieTasks[0].GetAttributeValue<AliasedValue>("lines1.spk_step")?.Value.ToString());
                    if (!sod.Item1)
                        return MessageResult.FailureResult(sod.Item2);
                }

                //处理审批任务
                switch (request.OperationStatus)
                {
                    case ApprovalOperation.Approved:
                        entitieTasks[0]["spk_approvalstatus"] = new OptionSetValue((int)ApprovalPowerAppStatus.Approved);
                        break;
                    case ApprovalOperation.Rejected:
                        entitieTasks[0]["spk_approvalstatus"] = new OptionSetValue((int)ApprovalPowerAppStatus.Rejected);
                        break;
                    case ApprovalOperation.Withdraw:
                        entitieTasks[0]["spk_approvalstatus"] = new OptionSetValue((int)ApprovalPowerAppStatus.Denied);
                        break;
                    case ApprovalOperation.ReturnToPatch:
                        entitieTasks[0]["spk_approvalstatus"] = new OptionSetValue((int)ApprovalPowerAppStatus.Denied);
                        break;
                    case ApprovalOperation.ReturnToPreliminary:
                        entitieTasks[0]["spk_approvalstatus"] = new OptionSetValue((int)ApprovalPowerAppStatus.Denied);
                        break;
                    case ApprovalOperation.ReturnToApplicant:
                        entitieTasks[0]["spk_approvalstatus"] = new OptionSetValue((int)ApprovalPowerAppStatus.Denied);
                        break;
                    case ApprovalOperation.ReturnOriginal:
                        entitieTasks[0]["spk_approvalstatus"] = new OptionSetValue((int)ApprovalPowerAppStatus.Denied);
                        break;
                    //case ApprovalOperation.Recall:
                    //	entitieTasks[0]["spk_approvalstatus"] = new OptionSetValue((int)ApprovalPowerAppStatus.Revoked);
                    //	break;
                    default:
                        break;
                }
                if (request.NextApprover != null)
                    entitieTasks[0]["spk_nextapprover"] = new EntityReference("spk_staffmasterdata", (Guid)request.NextApprover);
                entitieTasks[0]["spk_remark"] = request.OECApproverId.HasValue ? $"OEC({CurrentUser.Name})单据拦截，" + request.Remark : request.Remark;
                entitieTasks[0]["spk_navigationtostep"] = request.Step;//退回时需要的参数  //操作审批
                if (!string.IsNullOrWhiteSpace(request.BusinessFormNextData))
                {
                    entitieTasks[0]["spk_nextdatas"] = request.BusinessFormNextData;
                }
                updateEntities.Entities.Add(entitieTasks[0]);
                var operationResult = new UpdateApprovalResponseDto();
                operationResult.BusinessFormId = request.BusinessFormId;
                operationResult.IsSuccess = true;
                responses.Add(operationResult);
                request.Name = entitieTasks[0].GetAttributeValue<EntityReference>("spk_workflowinstance").Name;
                request.WorkflowInstanceId = entitieTasks[0].GetAttributeValue<EntityReference>("spk_workflowinstance").Id;
                //task节点名称
                taskName = entitieTasks[0].GetAttributeValue<AliasedValue>("lines1.spk_stepdescriptionname")?.Value.ToString();
                spkStep = entitieTasks[0].GetAttributeValue<AliasedValue>("lines1.spk_step")?.Value.ToString();
                request.StepNo = spkStep;
                originalApproverId = entitieTasks[0].GetAttributeValue<EntityReference>("spk_originalapprover")?.Id;
                //批量执行
                var result = TransactionRequest(null, updateEntities, null);
                if (!result)
                {
                    _logger.LogWarning($"VeevaDcrCall,Single,批量操作失败，请联系管理员");//测试日志，veeva dcr有时不自动发起，找到问题后再删除
                    return MessageResult.FailureResult("批量操作失败，请联系管理员");
                }
                else if (!isRobot && originalApproverId.HasValue && CurrentUser.Id.HasValue && originalApproverId.Value != CurrentUser.Id.Value)
                {
                    #region 记录代理历史-审批操作
                    var workflowType = request.WorkflowType.HasValue ? request.WorkflowType.Value : workflowTypeName;
                    var requestDto = new CreateAgentHistoryRequestDto
                    {
                        SubmitterId = originalApproverId.Value,
                        WorkflowType = workflowType,
                        BusinessTypeCategory = WorkflowTypeToFormCategory(workflowType),
                        FormId = Guid.Parse(request.BusinessFormId),
                        FormNo = request.BusinessFormNo
                    };
                    await CreateAgentHistoryApprovalTask([requestDto]);
                    #endregion
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"ApproveService's ApprovalOperationAsync has an error : {ex.Message}", ex.StackTrace);
                _logger.LogException(ex);
                return MessageResult.FailureResult("审批操作失败，请联系管理员");
            }

            try
            {
                //添加审批记录
                var time = DateTime.Now.ToString("yyMMdd-hhmmss");
                var approvalId = request.OECApproverId.HasValue ? request.OECApproverId.Value : request.Submitter;
                var approvalRecord = new AddApprovalRecordDto
                {
                    InstanceId = (Guid)request.WorkflowInstanceId,
                    Name = $"{request.Name}",
                    Remark = request.Remark,
                    FormId = Guid.Parse(request.BusinessFormId),
                    ApprovalTime = DateTime.UtcNow.AddHours(8),
                    ApprovalId = approvalId,
                    OriginalApprovalId = request.OECApproverId.HasValue ? approvalId : (originalApproverId.HasValue ? originalApproverId.Value : request.Submitter),
                    Status = request.OperationStatus,
                    WorkStep = taskName,
                    StepNo = request.StepNo,
                };

                var task = await AddApprovalRecordAsync(approvalRecord);
                return MessageResult.SuccessResult(task.Item2);
            }
            catch (Exception ex)
            {
                _logger.LogError($"ApproveService's ApprovalOperationBussinessAsync has an error : {ex.Message}");
                return MessageResult.FailureResult("审批操作记录失败，请联系管理员");
                //考虑失败后放入redis中定时处理 TODO
            }
        }
        #region  SOD 校验
        /// <summary>
        ///  SOD 校验
        /// </summary>
        /// <param name="flowType"></param>
        /// <param name="businessFormId"></param>
        /// <param name="stepNo"></param>
        /// <param name="instanceId"></param>
        /// <returns></returns>
        private (bool, string) SODApproveVerification(WorkflowTypeName? flowType, string businessFormId, string stepNo)
        {
            switch (flowType)
            {
                case WorkflowTypeName.PurchaseRequest:
                    return PRSODApproveVerification(Guid.Parse(businessFormId), stepNo);
                case WorkflowTypeName.MasterPurchaseOrder:
                    return POSODApproveVerification(Guid.Parse(businessFormId), stepNo);
                default:
                    return (true, "");
            }
        }

        /// <summary>
        /// PO SOD 校验
        /// </summary>
        /// <param name="prId"></param>
        /// <param name="stepNo"></param>
        /// <param name="instanceId"></param>
        /// <returns></returns>
        private (bool, string) POSODApproveVerification(Guid poId, string stepNo)
        {
            string[] poStepNos = [POWorkflowConsts.CycleApproval6, POWorkflowConsts.CycleApproval5, POWorkflowConsts.CycleApproval4, POWorkflowConsts.CycleApproval3, POWorkflowConsts.CycleApproval2, POWorkflowConsts.CycleApproval1]; //主采购循环审批节点
            if (!poStepNos.Contains(stepNo))
                return (true, "");
            Guid thisApproverId = CurrentUser.Id.Value;
            try
            {
                var poRepository = LazyServiceProvider.GetService<IPurPOApplicationRepository>();
                var po = poRepository.GetAsync(poId).Result;
                //查询审批task
                var queryTask = new QueryExpression("spk_workflowtask");
                queryTask.ColumnSet.AddColumns("spk_businessformid", "spk_name", "spk_approvalstatus", "spk_remark", "spk_workflowstep", "createdon", "spk_approver", "spk_originalapprover", "spk_workflowinstance", "modifiedon", "spk_isorsigned");
                queryTask.Criteria.AddCondition(new ConditionExpression("statecode", ConditionOperator.Equal, 0));
                queryTask.Criteria.AddCondition(new ConditionExpression("spk_isorsigned", ConditionOperator.NotEqual, true));//排除或签
                queryTask.Criteria.AddCondition(new ConditionExpression("spk_businessformid", ConditionOperator.Equal, po.PRId.ToString()));
                queryTask.Criteria.AddCondition(new ConditionExpression("spk_approvalstatus", ConditionOperator.Equal, (int)ApprovalPowerAppStatus.Approved));
                LinkEntity linkStep = queryTask.AddLink("spk_workflowstep", "spk_workflowstep", "spk_workflowstepid", JoinOperator.LeftOuter);
                linkStep.Columns.AddColumns("spk_name", "spk_stepdescriptionname", "spk_step");
                linkStep.EntityAlias = "lines1";
                var entitieTasks = _dataverseRepository.DataverseClient.GetAllEntitiesAsync(queryTask).Result.ToList();

                var taskResults = entitieTasks.Select(p => new
                {
                    Instance = p.GetAttributeValue<EntityReference>("spk_workflowinstance"),
                    Name = p.GetAttributeValue<string>("spk_name"),
                    Approvalstatus = p.GetAttributeValue<OptionSetValue>("spk_approvalstatus"),
                    Remark = p.GetAttributeValue<string>("spk_remark"),
                    WorkflowStep = p.GetAttributeValue<AliasedValue>("lines1.spk_step")?.Value.ToString(),
                    ApprovalTime = p.GetAttributeValue<DateTime>("createdon"),
                    Approvaler = p.GetAttributeValue<EntityReference>("spk_approver"),
                    OriginalApprover = p.GetAttributeValue<EntityReference>("spk_originalapprover")
                });
                var taskRes = taskResults.Select(p => new SODResponseDto
                {
                    InstanceId = p.Instance?.Id,
                    StepNo = p.WorkflowStep,
                    ApprovalStatus = (ApprovalPowerAppStatus)p.Approvalstatus.Value,
                    ApproverId = p.Approvaler?.Id,
                    ApprovalTime = p.ApprovalTime,
                    OriginalApproverId = p.OriginalApprover?.Id
                }).Where(a => a.ApproverId.HasValue);
                string[] stepNos = [PRWorkflowConsts.Expense6, PRWorkflowConsts.Expense5, PRWorkflowConsts.Expense4, PRWorkflowConsts.Expense3, PRWorkflowConsts.Expense2, PRWorkflowConsts.Expense1, PRWorkflowConsts.Expense0]; //Expense循环审批
                ////都需要从PP端拉取数据，因为存在跳过审批情况，NEXBPM 无法取到PP端跳过的审批人
                //var prApprovalRecord = GetApprovalRecordAsync(po.PRId).Result;
                //var approvalData = (List<ApprovalRecordDto>)prApprovalRecord.Data;
                var expenseApprovedInstanceId = taskRes.Where(a => stepNos.Contains(a.StepNo)).MaxBy(a => a.ApprovalTime)?.InstanceId;//PR最近循环审批的InstanceId(修正后提交不走循环审批) 特别注意：业务上除了修正，不存在跳过循环审批情况，否则此处取InstanceId 不严谨 
                var prApproveds = taskRes.Where(a => a.InstanceId == expenseApprovedInstanceId && a.ApprovalStatus == ApprovalPowerAppStatus.Approved && stepNos.Contains(a.StepNo)).ToList();//本地审批记录
                if (!prApproveds.Any())
                    return (true, "");

                string[] stepNoF = [PRWorkflowConsts.PrFinApprovalStepCode, PRWorkflowConsts.PrFinApprovalDirectorStepCode];
                var expenseApprovedInstanceIdF = taskRes.Where(a => stepNoF.Contains(a.StepNo)).MaxBy(a => a.ApprovalTime)?.InstanceId;
                var prApprovedFs = taskRes.Where(a => a.InstanceId == expenseApprovedInstanceIdF && a.ApprovalStatus == ApprovalPowerAppStatus.Approved && stepNoF.Contains(a.StepNo)).ToList();//本地审批记录 PR 财务审批人 审批人
                if (prApprovedFs.Any())
                    prApproveds.AddRange(prApprovedFs);

                if (prApproveds.Select(a => a.OriginalApproverId.HasValue ? a.OriginalApproverId.Value : a.ApproverId).Contains(thisApproverId))//当前审批人参与了
                {
                    return (false, $"因SOD问题，采购审批人与费用审批人或财务审批人不可为同一人，请将此单退回申请人，并请提醒申请人联系IT调整审批配置后重新发起");
                }
                else
                {
                    //当前审批人未参与Expense循环审批 可通过
                    return (true, "");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"POSODApproveVerification error: {ex.Message}");
                return (false, ex.Message);
            }
        }

        /// <summary>
        /// PR SOD 校验
        /// </summary>
        /// <param name="prId"></param>
        /// <param name="stepNo"></param>
        /// <param name="instanceId"></param>
        /// <returns></returns>
        private (bool, string) PRSODApproveVerification(Guid prId, string stepNo)
        {
            if (!(stepNo.Equals(PRWorkflowConsts.PrFinApprovalStepCode) || stepNo.Equals(PRWorkflowConsts.PrFinApprovalDirectorStepCode)))
                return (true, "");
            Guid thisApproverId = CurrentUser.Id.Value;
            try
            {
                //查询审批task
                var queryTask = new QueryExpression("spk_workflowtask");
                queryTask.ColumnSet.AddColumns("spk_businessformid", "spk_name", "spk_approvalstatus", "spk_remark", "spk_workflowstep", "createdon", "spk_approver", "spk_originalapprover", "spk_workflowinstance", "modifiedon", "spk_isorsigned");
                queryTask.Criteria.AddCondition(new ConditionExpression("statecode", ConditionOperator.Equal, 0));
                queryTask.Criteria.AddCondition(new ConditionExpression("spk_isorsigned", ConditionOperator.NotEqual, true));//排除或签
                queryTask.Criteria.AddCondition(new ConditionExpression("spk_businessformid", ConditionOperator.Equal, prId.ToString()));
                queryTask.Criteria.AddCondition(new ConditionExpression("spk_approvalstatus", ConditionOperator.In, [(int)ApprovalPowerAppStatus.Approved, (int)ApprovalPowerAppStatus.PendingForApproval]));
                LinkEntity linkStep = queryTask.AddLink("spk_workflowstep", "spk_workflowstep", "spk_workflowstepid", JoinOperator.LeftOuter);
                linkStep.Columns.AddColumns("spk_name", "spk_stepdescriptionname", "spk_step");
                linkStep.EntityAlias = "lines1";
                var entitieTasks = _dataverseRepository.DataverseClient.GetAllEntitiesAsync(queryTask).Result.ToList();

                var taskResults = entitieTasks.Select(p => new
                {
                    Instance = p.GetAttributeValue<EntityReference>("spk_workflowinstance"),
                    Name = p.GetAttributeValue<string>("spk_name"),
                    Approvalstatus = p.GetAttributeValue<OptionSetValue>("spk_approvalstatus"),
                    Remark = p.GetAttributeValue<string>("spk_remark"),
                    WorkflowStep = p.GetAttributeValue<AliasedValue>("lines1.spk_step")?.Value.ToString(),
                    ApprovalTime = p.GetAttributeValue<DateTime>("createdon"),
                    Approvaler = p.GetAttributeValue<EntityReference>("spk_approver"),
                    OriginalApprover = p.GetAttributeValue<EntityReference>("spk_originalapprover")
                });
                var taskRes = taskResults.Select(p => new SODResponseDto
                {
                    InstanceId = p.Instance?.Id,
                    StepNo = p.WorkflowStep,
                    ApprovalStatus = (ApprovalPowerAppStatus)p.Approvalstatus.Value,
                    ApproverId = p.Approvaler?.Id,
                    ApprovalTime = p.ApprovalTime,
                    OriginalApproverId = p.OriginalApprover?.Id
                }).Where(a => a.ApproverId.HasValue);

                string[] stepNos = [PRWorkflowConsts.Expense6, PRWorkflowConsts.Expense5, PRWorkflowConsts.Expense4, PRWorkflowConsts.Expense3, PRWorkflowConsts.Expense2, PRWorkflowConsts.Expense1, PRWorkflowConsts.Expense0]; //Expense循环审批
                var expenseApprovedInstanceId = taskRes.Where(a => stepNos.Contains(a.StepNo)).MaxBy(a => a.ApprovalTime)?.InstanceId;//最近循环审批的InstanceId
                if (!expenseApprovedInstanceId.HasValue)
                    return (true, "");

                //都需要从PP端拉取数据，因为存在跳过审批情况，NEXBPM 无法取到PP端跳过的审批人
                //var prApprovalRecord = GetApprovalRecordAsync(prId).Result;
                //var approvalData = (List<ApprovalRecordDto>)prApprovalRecord.Data;
                var approveds = taskRes.Where(a => a.InstanceId == expenseApprovedInstanceId && a.ApprovalStatus == ApprovalPowerAppStatus.Approved && stepNos.Contains(a.StepNo)).ToList();
                if (!approveds.Any())
                    return (true, "");

                var thisApproveds = taskRes.Where(a => a.ApprovalStatus == ApprovalPowerAppStatus.PendingForApproval).ToList();//当前节点审批人 待审批(Pending中)

                // 如当前节点所有审批人都曾参与Expense循环审批(检查集合 approveds 是否包含了集合 thisApproveds 中的所有元素)
                bool containsAll = thisApproveds.Select(a => a.ApproverId).All(b => approveds.Select(x => x.OriginalApproverId.HasValue ? x.OriginalApproverId.Value : x.ApproverId).Contains(b));
                if (containsAll)
                    return (false, $"因SOD问题，费用审批人与财务审批人不可为同一人，请将此单退回申请人，并请提醒申请人联系IT调整审批配置后重新发起");

                if (approveds.Select(a => a.OriginalApproverId.HasValue ? a.OriginalApproverId.Value : a.ApproverId).Contains(thisApproverId))//当前审批人参与了，判断还有没有未参与的
                {
                    //如当前节点存在审批人未曾参与Expense循环审批(当前审批人参与了说明还有其他审批人未参与)
                    return (false, $"因SOD问题，费用审批人与财务审批人不可为同一人，请提醒其他财务审批人进行审批");
                }
                else
                {
                    //当前审批人未参与Expense循环审批 可通过
                    return (true, "");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"PRSODApproveVerification error: {ex.Message}");
                return (false, ex.Message);
            }
        }

        #endregion
        /// <summary>
        /// 医师信息验证
        /// </summary>
        /// <param name="request"></param>
        private void PhysicianInformationVerification(List<UpdateApprovalDto> request, string spkStep)
        {
            try
            {
                var integrationOmAppService = LazyServiceProvider.GetService<IInteVeevaService>();
                foreach (var item in request)
                {
                    var queryInstance = new QueryExpression("spk_workflowinstance");
                    queryInstance.ColumnSet.AddColumns("spk_workflowinstanceid", "spk_name", "spk_status", "spk_businessformdata");
                    queryInstance.Criteria.AddCondition(new ConditionExpression("statecode", ConditionOperator.Equal, 0));
                    queryInstance.Criteria.AddCondition(new ConditionExpression("spk_businessformid", ConditionOperator.Equal, item.BusinessFormId));
                    queryInstance.Criteria.AddCondition(new ConditionExpression("spk_status", ConditionOperator.Equal, (int)ApprovalPowerAppStatus.PendingForApproval));
                    LinkEntity linkStep = queryInstance.AddLink("spk_workflowtype", "spk_workflowyype", "spk_workflowtypeid", JoinOperator.LeftOuter);
                    linkStep.Columns.AddColumns("spk_name", "spk_workflowtypename");
                    linkStep.EntityAlias = "lines1";
                    var entitieInstance = _dataverseRepository.DataverseClient.GetAllEntitiesAsync(queryInstance).Result.ToList();
                    var entity = entitieInstance.FirstOrDefault();
                    _logger.LogWarning($"VeevaDcrCall,entity is null:{entity != null},BusinessFormId:{item.BusinessFormId}.");//测试日志，veeva dcr有时不自动发起，找到问题后再删除
                    if (entity != null)
                    {
                        //判断审批类型
                        var workType = ((OptionSetValue)entity.GetAttributeValue<AliasedValue>("lines1.spk_workflowtypename")?.Value)?.Value;
                        _logger.LogWarning($"VeevaDcrCall,workType:{workType},BusinessFormId:{item.BusinessFormId}.");//测试日志，veeva dcr有时不自动发起，找到问题后再删除
                        if (workType != (int)WorkflowTypeName.SpeakerRequest && workType != (int)WorkflowTypeName.SpeakerChange) continue;
                        //判断是否发起医师信息验证
                        var formData = entity.Contains("spk_businessformdata") ? entity.GetAttributeValue<string>("spk_businessformdata") : "";
                        _logger.LogWarning($"VeevaDcrCall,formData:{formData},BusinessFormId:{item.BusinessFormId}.");//测试日志，veeva dcr有时不自动发起，找到问题后再删除
                        if (string.IsNullOrEmpty(formData))
                        {
                            //调用同步接口
                            integrationOmAppService.PushDcr(Guid.Parse(item.BusinessFormId));
                            continue;
                        }
                        if (JsonConvert.DeserializeObject<ApprovalFomrDataDto>(formData).isChangedDoctorInfo)
                        {
                            //调用同步接口
                            integrationOmAppService.PushDcr(Guid.Parse(item.BusinessFormId));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"ApproveService's ApprovalOperationBussinessAsync has an error 医师信息验证审批操作记录失败: {ex.Message}");
            }

        }

        /// <summary>
        /// 提交人操作（撤回，作废）,按表单类型批量处理
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<MessageResult> SubmitterOperationAsync(List<UpdateApprovalDto> request)
        {
            EntityCollection updateEntities = new EntityCollection();
            var responses = new List<UpdateApprovalResponseDto>();

            try
            {
                if (request == null || request.Count < 1)
                    return MessageResult.FailureResult("未找到相关记录");
                //根据业务ID查询相应表及数据
                var taskQuery = await _taskRepository.GetQueryableAsync();
                var task = taskQuery.OrderBy(o => o.CreationTime)
                    .FirstOrDefault(f => f.FormId == Guid.Parse(request.First().BusinessFormId));
                if (task == null)
                    return MessageResult.FailureResult("未找到相关记录");

                var ids = request.Select(x => Guid.Parse(x.BusinessFormId)).ToList();
                IEnumerable<(Guid BusinessId, Guid ApplicantId, string Applicant, WorkflowTypeName workflowType, string stepNo)> applyDatas;

                //验证是否可以操作
                #region 验证
                if (request.First().OperationStatus == ApprovalOperation.Recall)
                {
                    //查询审批task
                    var queryValidateTask = new QueryExpression("spk_workflowtask");
                    queryValidateTask.ColumnSet.AddColumns("spk_businessformid", "spk_name", "spk_approvalstatus", "spk_remark", "spk_businessformid", "spk_workflowstep", "createdon", "spk_approver", "spk_workflowinstance");
                    queryValidateTask.Criteria.AddCondition(new ConditionExpression("spk_businessformid", ConditionOperator.In, ids.Select(s => s.ToString()).ToList()));
                    queryValidateTask.Criteria.AddCondition(new ConditionExpression("spk_approvalstatus", ConditionOperator.Equal, (int)ApprovalPowerAppStatus.PendingForApproval));
                    LinkEntity linkInstance = queryValidateTask.AddLink("spk_workflowinstance", "spk_workflowinstance", "spk_workflowinstanceid", JoinOperator.LeftOuter);
                    linkInstance.Columns.AddColumns("spk_name", "spk_status", "spk_submitter", "spk_workflowyype");
                    linkInstance.EntityAlias = "ins";
                    LinkEntity linkType = linkInstance.AddLink("spk_workflowtype", "spk_workflowyype", "spk_workflowtypeid");
                    linkType.Columns.AddColumns("spk_workflowtypename");
                    linkType.EntityAlias = "tp";
                    var linkStep = queryValidateTask.AddLink("spk_workflowstep", "spk_workflowstep", "spk_workflowstepid");
                    linkStep.Columns.AddColumns("spk_step");
                    linkStep.EntityAlias = "step";

                    var entitieValidateTasks = _dataverseRepository.DataverseClient.GetAllEntitiesAsync(queryValidateTask).Result.ToList();

                    //检查所有task中的状态是否可以撤回
                    if (entitieValidateTasks.Select(s => s.GetAttributeValue<string>("spk_businessformid")).Distinct().Count() != ids.Count)
                    {
                        return MessageResult.FailureResult("未查询到相关数据或数据有误");
                    }
                    //检查所有instance中的状态是否可以撤回
                    if (entitieValidateTasks.Any(w => ((OptionSetValue)w.GetAttributeValue<AliasedValue>("ins.spk_status").Value).Value != (int)ApprovalPowerAppStatus.PendingForApproval))
                    {
                        return MessageResult.FailureResult("未查询到相关数据或数据有误");
                    }

                    applyDatas = entitieValidateTasks.Select(a =>
                    {
                        var businessId = a.GetAttributeValue<string>("spk_businessformid");
                        var submitter = (EntityReference)a.GetAttributeValue<AliasedValue>("ins.spk_submitter").Value;
                        var workflowType = (OptionSetValue)a.GetAttributeValue<AliasedValue>("tp.spk_workflowtypename").Value;
                        var stepNo = a.GetAttributeValue<AliasedValue>("step.spk_step")?.Value?.ToString();
                        return (string.IsNullOrEmpty(businessId) ? Guid.Empty : Guid.Parse(businessId), submitter == null ? Guid.Empty : submitter.Id, submitter == null ? null : submitter.Name, (WorkflowTypeName)workflowType.Value, stepNo);
                    });
                }
                else
                {
                    //查询撤回或退回的Instance,撤回或退回的Instance方可作废
                    var queryValidateTask = new QueryExpression("spk_workflowinstance");
                    queryValidateTask.ColumnSet.AddColumns("spk_name", "spk_status", "spk_submitter", "spk_businessformid", "spk_workflowyype");
                    queryValidateTask.Criteria.AddCondition(new ConditionExpression("spk_businessformid", ConditionOperator.In, ids.Select(s => s.ToString()).ToList()));
                    queryValidateTask.Criteria.AddCondition(new ConditionExpression("spk_status", ConditionOperator.In, [(int)ApprovalPowerAppStatus.Revoked, (int)ApprovalPowerAppStatus.Denied]));
                    LinkEntity linkType = queryValidateTask.AddLink("spk_workflowtype", "spk_workflowyype", "spk_workflowtypeid");
                    linkType.Columns.AddColumns("spk_workflowtypename");
                    linkType.EntityAlias = "tp";
                    var defeasibleInstances = _dataverseRepository.DataverseClient.GetAllEntitiesAsync(queryValidateTask).Result.ToList();

                    // 添加调试日志
                    _logger.LogInformation($"作废操作检查 - BusinessFormId: {ids.FirstOrDefault()}, 查询到的工作流实例数量: {defeasibleInstances.Count()}");

                    if (defeasibleInstances.Count() > 0)
                    {
                        foreach (var instance in defeasibleInstances)
                        {
                            var businessId = instance.GetAttributeValue<string>("spk_businessformid");
                            var status = instance.GetAttributeValue<OptionSetValue>("spk_status")?.Value;
                            _logger.LogInformation($"工作流实例 - BusinessFormId: {businessId}, Status: {status}");
                        }
                    }

                    //检查所有instance中的状态是否可以作废
                    if (defeasibleInstances.Count() == 0)
                    {
                        _logger.LogWarning($"作废操作失败 - 未找到状态为撤回(4)或退回(5)的工作流实例，BusinessFormId: {ids.FirstOrDefault()}");
                        return MessageResult.FailureResult("未查询到相关数据或数据有误");
                    }

                    applyDatas = defeasibleInstances.Select(a =>
                    {
                        var businessId = a.GetAttributeValue<string>("spk_businessformid");
                        var submitter = a.GetAttributeValue<EntityReference>("spk_submitter");
                        var workflowType = (OptionSetValue)a.GetAttributeValue<AliasedValue>("tp.spk_workflowtypename").Value;
                        return (string.IsNullOrEmpty(businessId) ? Guid.Empty : Guid.Parse(businessId), submitter == null ? Guid.Empty : submitter.Id, submitter == null ? null : submitter.Name, (WorkflowTypeName)workflowType.Value, string.Empty);
                    });
                }
                #endregion

                switch (task.FormName)
                {
                    case NameConsts.VendorApplication:
                        //4724【Enhancement】【讲者申请】【新建/激活/变更】当申请单处于“医师信息验证”节点时，不允许申请人撤回申请--BE
                        if (request.First().OperationStatus == ApprovalOperation.Recall && applyDatas.Any(a => a.stepNo == WorkflowConsts.VendorWorkflowConsts.DoctorVerification))
                            return MessageResult.FailureResult("医师验证中，无法撤回");

                        await UpdateVendorApplicationFormStatus(ids, request.First().OperationStatus);//已撤回
                        break;
                    case NameConsts.PRApplication:
                        var r = await UpdatePRApplicationStatus(ids, request.First().OperationStatus);
                        if (!r.Success)
                        {
                            await CurrentUnitOfWork.RollbackAsync();
                            return r;
                        }
                        ////根据会议状态判断，是否能撤回&作废会议
                        //var validateCanWithdrawResult = await ValidateCanWithdraw(ids, request.First().OperationStatus);
                        //if (!validateCanWithdrawResult.Success)
                        //{
                        //    return validateCanWithdrawResult;
                        //}
                        //await WithdrawOmMeeting(ids, request.First().OperationStatus);
                        break;
                    case NameConsts.BDApplication:
                        await UpdateBDApplication(ids, request.First().OperationStatus);
                        break;
                    case NameConsts.POApplication:
                        await UpdatePOApplicationFormStatus(ids, request.First().OperationStatus);
                        break;
                    case NameConsts.PAApplication:
                        await UpdatePAApplication(ids, request.First().OperationStatus);
                        break;
                    case NameConsts.PurExemptWaiver:
                        await UpdateBWApplication(ids, request.First().OperationStatus);
                        break;
                    case NameConsts.PurExemptJustification:
                        await UpdateBWApplication(ids, request.First().OperationStatus);
                        break;
                    case NameConsts.OECSpeakerAuthApply:
                        await UpdateSpeakerAuthApplyAsync(ids, request.First().OperationStatus);
                        break;
                    case NameConsts.STicketApplication:
                        await UpdateSTicktApplyAsync(ids, request.First().OperationStatus);
                        break;
                    case NameConsts.FOCApplication:
                        await UpdateFOCApplyAsync(ids, request.First().OperationStatus);
                        break;
                    case NameConsts.ReturnAndExchangeRequest:
                        await UpdateReturnApplyAsync(ids, request.First().OperationStatus);
                        break;
                    default:
                        break;
                }

                //更新对应的Instance的状态以及对应的当前Task的状态
                #region 更新对应的Instance的状态以及对应的当前Task的状态
                foreach (var item in request)
                {
                    //查询审批Instance
                    var queryInstance = new QueryExpression("spk_workflowinstance");
                    queryInstance.ColumnSet.AddColumns("spk_workflowinstanceid", "spk_name", "spk_status");
                    queryInstance.Criteria.AddCondition(new ConditionExpression("statecode", ConditionOperator.Equal, 0));
                    queryInstance.Criteria.AddCondition(new ConditionExpression("spk_businessformid", ConditionOperator.In, ids.Select(s => s.ToString()).ToList()));
                    if (request.First().OperationStatus == ApprovalOperation.Recall)
                    {
                        queryInstance.Criteria.AddCondition(new ConditionExpression("spk_status", ConditionOperator.Equal, (int)ApprovalPowerAppStatus.PendingForApproval));
                    }
                    if (request.First().OperationStatus == ApprovalOperation.Delete)
                    {
                        queryInstance.Criteria.AddCondition(new ConditionExpression("spk_status", ConditionOperator.In, [(int)ApprovalPowerAppStatus.Revoked, (int)ApprovalPowerAppStatus.Denied]));
                    }

                    var entitieInstance = _dataverseRepository.DataverseClient.GetAllEntitiesAsync(queryInstance).Result.ToList();
                    //重复调用，注释后看结果
                    //if (entitieInstance == null || entitieInstance.Count != 1)
                    //{
                    //    return MessageResult.FailureResult("未查询到相关Instance数据或数据有误");
                    //}
                    //处理审批任务
                    if (request.First().OperationStatus == ApprovalOperation.Recall)
                    {
                        foreach (var itemInstance in entitieInstance)
                        {
                            itemInstance["spk_status"] = new OptionSetValue((int)ApprovalPowerAppStatus.Revoked);
                            updateEntities.Entities.Add(itemInstance);
                        }
                    }
                    if (request.First().OperationStatus == ApprovalOperation.Delete)
                    {
                        foreach (var itemInstance in entitieInstance)
                        {
                            itemInstance["spk_status"] = new OptionSetValue((int)ApprovalPowerAppStatus.Delete);
                            updateEntities.Entities.Add(itemInstance);
                        }
                    }

                    //撤回时标记task，作废无需标记（作废的前提是已退回或已撤回）
                    if (request.First().OperationStatus == ApprovalOperation.Recall)
                    {
                        //查询审批task
                        var queryTask = new QueryExpression("spk_workflowtask");
                        queryTask.ColumnSet.AddColumns("spk_businessformid", "spk_name", "spk_approvalstatus", "spk_remark", "spk_businessformid", "spk_workflowstep", "createdon", "spk_approver", "spk_workflowinstance");
                        queryTask.Criteria.AddCondition(new ConditionExpression("statecode", ConditionOperator.Equal, 0));
                        queryTask.Criteria.AddCondition(new ConditionExpression("spk_businessformid", ConditionOperator.In, ids.Select(s => s.ToString()).ToList()));
                        queryTask.Criteria.AddCondition(new ConditionExpression("spk_approvalstatus", ConditionOperator.Equal, (int)ApprovalPowerAppStatus.PendingForApproval));

                        var entitieTasks = _dataverseRepository.DataverseClient.GetAllEntitiesAsync(queryTask).Result.ToList();

                        if (entitieTasks != null)
                        {
                            //处理审批任务
                            foreach (var itemTask in entitieTasks)
                            {
                                itemTask["spk_approvalstatus"] = new OptionSetValue((int)ApprovalPowerAppStatus.Revoked);
                                itemTask["spk_remark"] = $"提交人主动撤回。{item.Remark}";
                                updateEntities.Entities.Add(itemTask);
                            }
                            //return MessageResult.FailureResult("未查询到相关Task数据或数据有误");
                        }
                    }
                }
                //批量执行
                var result = TransactionRequest(null, updateEntities, null);
                if (!result) return MessageResult.FailureResult("批量操作失败，请联系管理员");
                #endregion

                //记录操作task
                var operationDtp = new List<AddApprovalRecordDto>();
                ids.ForEach(x =>
                {
                    var originalApproval = applyDatas.FirstOrDefault(a => a.BusinessId == x);
                    operationDtp.Add(new AddApprovalRecordDto
                    {
                        FormId = x,
                        ApprovalId = request.First().Submitter,
                        OriginalApprovalId = originalApproval.ApplicantId,//申请人Id
                        Status = request.First().OperationStatus,
                        Remark = request.First().Remark,
                        ApprovalTime = DateTime.Now,
                        WorkStep = "提交人操作",
                        Name = request.First().OperationStatus == ApprovalOperation.Recall ? "撤回" : "作废"
                    });
                });

                operationDtp.ForEach(async x => await AddApprovalRecordAsync(x));

                #region 记录代理历史-提交批量
                var requestDto = new List<CreateAgentHistoryRequestDto>();
                foreach (var item in request)
                {
                    var bizFormId = Guid.Parse(item.BusinessFormId);
                    var apply = applyDatas.FirstOrDefault(x => x.BusinessId == bizFormId);
                    WorkflowTypeName? workflowType = apply != default ? apply.workflowType : (item.WorkflowType.HasValue ? item.WorkflowType.Value : null);
                    requestDto.Add(new CreateAgentHistoryRequestDto
                    {
                        SubmitterId = item.Submitter,
                        WorkflowType = workflowType,
                        BusinessTypeCategory = workflowType.HasValue ? WorkflowTypeToFormCategory(workflowType.Value) : null,
                        FormId = bizFormId,
                        FormNo = item.BusinessFormNo
                    });
                }
                await CreateAgentHistory(requestDto);
                #endregion

                return MessageResult.SuccessResult();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"ApproveService's SubmitterOperationAsync has an error : {ex.Message}");
                return MessageResult.FailureResult("提交人操作失败，请联系管理员");
            }
        }

        /// <summary>
        /// 更新比价申请单状态
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="operationStatus"></param>
        /// <exception cref="NotImplementedException"></exception>
        private async Task UpdateBDApplication(List<Guid> ids, ApprovalOperation operationStatus)
        {
            var bdApplication = _serviceProvider.GetService<IPurBDApplicationRepository>();
            var query = await bdApplication.GetQueryableAsync();
            var entities = query.Where(x => ids.Contains(x.Id)).ToList();
            if (operationStatus == ApprovalOperation.Recall)
            {
                if (entities.Any())
                    entities.ForEach(x => x.Status = PurBDStatus.Recall);

                //1792【任务中心】【我发起的】供应商比价撤回后，无法重新发起，提交提示：您所选的采购信息中包含已发起的比价或采购订单，不能重复发起（是否未排除重发起状态的比价单据）
                foreach (var item in entities)
                {
                    var prDetailRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>();
                    var prDetailQuery = await prDetailRepository.GetQueryableAsync();
                    var prDetailIds = item.PRApplicationDetailId.Split(',').Select(Guid.Parse).OrderBy(a => a).ToList();
                    var prDetail = prDetailQuery.Where(w => prDetailIds.Contains(w.Id)).ToList();
                    prDetail.ForEach(f => { f.OrderStatusFlag = null; });
                    await prDetailRepository.UpdateManyAsync(prDetail);
                }
            }
            if (operationStatus == ApprovalOperation.Delete)
            {
                if (entities.Any())
                    entities.ForEach(x => x.Status = PurBDStatus.Invalid);
            }
            await bdApplication.UpdateManyAsync(entities, true);

            //作废后，状态为拒绝
            if (operationStatus == ApprovalOperation.Delete)
            {
                foreach (var item in entities)
                {
                    var prDetailRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>();
                    var prDetailQuery = await prDetailRepository.GetQueryableAsync();
                    var prDetailIds = item.PRApplicationDetailId.Split(',').Select(Guid.Parse).OrderBy(a => a).ToList();
                    var prDetail = prDetailQuery.Where(w => prDetailIds.Contains(w.Id)).ToList();
                    //#2124 【任务中心】【我的采购推送】针对超过waiver限制的明细行，若不存在有效的Bidding/豁免单据，则不允许发起PO（当前推测未排除处于拒绝、作废的单据）
                    //作废比价后，PrDetail对应的BiddingId设置为空
                    prDetail.ForEach(f =>
                    {
                        f.BiddingId = null;
                        OrderStatusFlag[] orderStatusFlags = [OrderStatusFlag.Bidding, OrderStatusFlag.BiddingCompleted];//当前不在Bidding了就不更新状态了
                        if (f.OrderStatusFlag != null && orderStatusFlags.Contains(f.OrderStatusFlag.Value))
                            f.OrderStatusFlag = null;
                    });
                    await prDetailRepository.UpdateManyAsync(prDetail);
                }
            }
        }
        /// <summary>
        /// 更新付款申请单状态-撤回
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="operationStatus"></param>
        /// <exception cref="NotImplementedException"></exception>
        private async Task UpdatePAApplication(List<Guid> ids, ApprovalOperation operationStatus)
        {
            var paApplication = _serviceProvider.GetService<IPurPAApplicationRepository>();
            var query = await paApplication.GetQueryableAsync();
            var entities = query.Where(x => ids.Contains(x.Id)).ToList();
            if (operationStatus == ApprovalOperation.Recall)
            {
                foreach (var item in entities)
                {
                    item.Status = PurPAApplicationStatus.FillIn;
                    if (item.UseBudgetTime.HasValue)
                        await LazyServiceProvider.LazyGetService<IPurGRApplicationService>().ReturnSubbudgetAsync(item.PRId, item.Id, item.UseBudgetTime.Value);//预算返还
                    item.UseBudgetTime = null;//清除PA提交时使用预算时间，待后续提交使用
                }
                await paApplication.UpdateManyAsync(entities, true);
            }
        }
        /// <summary>
        /// 更新供应商状态
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="status"></param>
        private async Task UpdateVendorApplicationFormStatus(List<Guid> ids, ApprovalOperation status)
        {
            var vendorApplication = _serviceProvider.GetService<IVendorApplicationRepository>();
            var query = await vendorApplication.GetQueryableAsync();
            var entities = query.Where(x => ids.Contains(x.Id)).ToList();
            if (status == ApprovalOperation.Recall)
            {
                if (entities.Any())
                    entities.ForEach(x => x.Status = Statuses.Withdraw);
            }
            if (status == ApprovalOperation.Delete)
            {
                if (entities.Any())
                    entities.ForEach(x => x.Status = Statuses.Delete);
            }
            var res = vendorApplication.UpdateManyAsync(entities, true);

        }

        /// <summary>
        /// 更新采购申请状态
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="status"></param>
        /// <param name="isCallOm">是否调用OM接口</param>
        /// <returns></returns>
        public async Task<MessageResult> UpdatePRApplicationStatus(IEnumerable<Guid> ids, ApprovalOperation status, bool isCallOm = true)
        {
            var result = MessageResult.SuccessResult();

            //撤回/退回/作废
            if (status == ApprovalOperation.Recall || status == ApprovalOperation.Withdraw || status == ApprovalOperation.Delete)
            {
                var prApplicationRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>();
                var queryPrApplication = await prApplicationRepository.GetQueryableAsync();
                var prApplications = queryPrApplication.Where(a => ids.Contains(a.Id)).ToArray();

                foreach (var item in prApplications)
                {
                    if (status == ApprovalOperation.Recall || status == ApprovalOperation.Withdraw)
                        item.Status = PurPRApplicationStatus.RejectedBack;
                    else if (status == ApprovalOperation.Delete)
                    {
                        var prApplicationService = LazyServiceProvider.LazyGetService<IPurPRApplicationService>();
                        var canDeprecte = await prApplicationService.CanDeprecteOnlineMeetingAsync(item.Id);
                        if (!canDeprecte)
                            return MessageResult.FailureResult("该采购申请已有后续流程，不允许作废");

                        //作废电子签章会议时，同步更新OM侧
                        if (item.IsEsignUsed == true && isCallOm)
                        {
                            if (item.MeetingStatus == OnlineMeetingStatus.OmActivated || item.MeetingStatus == OnlineMeetingStatus.OmSettledPushed)
                                return MessageResult.FailureResult("该采购申请在OM侧已激活，不允许作废");
                            var onOff = await LazyServiceProvider.LazyGetService<ISettingRepository>().FindAsync(SettingsConst.OmBpmInterfaceOnAndOff, "G", null);
                            //OM状态为1000时才调用OM / BPM作废接口
                            if (item.MeetingStatus == OnlineMeetingStatus.NexBpmPushed)
                            {
                                if (onOff?.Value == "OFF")
                                {
                                    //20241114 系统并行期间，使用BPM OM相关接口。后续需要回复使用OmAddMeeting
                                    result = await LazyServiceProvider.LazyGetService<IIntegrationOmAppService>().OmRevokeMeeting(item.ApplicationCode);
                                    if (!result.Success)
                                        return result;
                                }
                                else
                                {
                                    // 调度作业 老BPM
                                    BackgroundJob.Enqueue<BpmOmWorker>(a => a.DoWorkAsync(new BpmRequestDto() { PrNo = item.ApplicationCode, InterfaceType = Enums.Integration.OmBpmType.Revoke }));
                                }
                            }
                        }

                        //退回预算
                        result = await ReturnBudgetAsync(item);
                        if (!result.Success)
                            return result;

                        //退回Psa
                        result = await ReturnPsaAsync(item);
                        if (!result.Success)
                            return result;

                        item.Status = PurPRApplicationStatus.ApplicantTerminate;
                    }
                }

                await prApplicationRepository.UpdateManyAsync(prApplications, true);
            }
            return result;
        }

        /// <summary>
        /// 返还预算
        /// </summary>
        /// <param name="prApplication"></param>
        /// <returns></returns>
        async Task<MessageResult> ReturnBudgetAsync(PurPRApplication prApplication)
        {
            var queryPrDetails = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var queryPrApplicationDetail = queryPrDetails.Where(a => a.PRApplicationId == prApplication.Id);
            var prDetailIds = queryPrApplicationDetail.Select(s => s.Id).ToArray();

            var queryGr = await LazyServiceProvider.LazyGetService<IPurGRApplicationDetailRepository>().GetQueryableAsync();
            var notReturnIds = queryGr.Where(m => prDetailIds.Contains(m.PRDetailId)).Select(s => s.PRDetailId).ToArray();
            //去掉存在在收货的pr行
            queryPrApplicationDetail = queryPrApplicationDetail.WhereIf(notReturnIds.Length > 0, m => !notReturnIds.Contains(m.Id));
            var prDetails = queryPrApplicationDetail.Select(a => new { a.RowNo, a.TotalAmountRMB, a.IcbAmount }).ToArray();
            var useBudgetRequest = new ReturnBudgetRequestDto
            {
                PrId = prApplication.Id,
                SubbudgetId = prApplication.SubBudgetId.Value,
                Items = prDetails.Select(a => new ReturnInfo
                {
                    PdRowNo = a.RowNo,
                    ReturnAmount = (a.TotalAmountRMB ?? 0) - (a.IcbAmount ?? 0),
                    ReturnSourceId = prApplication.Id,
                    ReturnSourceCode = prApplication.ApplicationCode
                })
            };

            var result = await LazyServiceProvider.LazyGetService<ISubbudgetService>().ReturnSubbudgetAsync(useBudgetRequest);
            return result;
        }

        /// <summary>
        /// 退回Psa
        /// </summary>
        /// <param name="prApplication"></param>
        /// <returns></returns>
        async Task<MessageResult> ReturnPsaAsync(PurPRApplication prApplication)
        {
            var queryBpcsAvm = await LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync();
            var queryVendor = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();
            var queryFinancial = await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync();
            var queryPrApplicationDetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var queryPrApplicationDetailBackupVendor = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailBackupVendorRepository>().GetQueryableAsync();

            queryPrApplicationDetail = queryPrApplicationDetail.Where(a => a.PRApplicationId == prApplication.Id);
            queryPrApplicationDetailBackupVendor = queryPrApplicationDetailBackupVendor.Where(a => a.PRApplicationId == prApplication.Id);

            //讲者类型
            var types = new string[] { "NHIV", "NT" };
            var speakerLimits = queryPrApplicationDetail
            .Join(queryBpcsAvm.Where(a => types.Contains(a.Vtype)), a => a.VendorId, a => a.Id, (a, b) => new { PrDetail = a, Avm = b })
            .Join(queryFinancial, a => a.Avm.FinaId, a => a.Id, (a, b) => new { a.Avm, a.PrDetail, Fin = b })
            .Select(a => new
            {
                a.PrDetail.Id,
                a.Fin.VendorId,
                a.PrDetail.EstimateDate,
                a.PrDetail.ExceptionNumber,
                Times = a.PrDetail.HedgePrDetailId.HasValue ? -1 : 1,
                Amount = a.PrDetail.TotalAmountRMB
            })
            .Union
            (
                queryPrApplicationDetail
                .Join(queryPrApplicationDetailBackupVendor, a => a.Id, a => a.PRApplicationDetailId, (a, b) => new { PrDetail = a, BackupVendor = b })
                .Join(queryBpcsAvm.Where(a => types.Contains(a.Vtype)), a => a.BackupVendor.VendorId, a => a.Id, (a, b) => new { a.PrDetail, a.BackupVendor, Avm = b })
                .Join(queryFinancial, a => a.Avm.FinaId, a => a.Id, (a, b) => new
                {
                    a.PrDetail.Id,
                    b.VendorId,
                    a.PrDetail.EstimateDate,
                    a.BackupVendor.ExceptionNumber,
                    Times = a.PrDetail.HedgePrDetailId.HasValue ? -1 : 1,
                    Amount = a.PrDetail.HedgePrDetailId.HasValue ? -a.BackupVendor.TotalAmountRMB : a.BackupVendor.TotalAmountRMB
                })
            ).ToArray();

            //需要退回psa用量的数据
            var psaDatas = speakerLimits.GroupBy(a => new { a.VendorId, a.EstimateDate, a.ExceptionNumber })
            .Select(a => new PrUseSpeakerLimitDetailRequest
            {
                VendorId = a.Key.VendorId,
                EffectiveDate = a.Key.EstimateDate ?? DateTime.Today,
                ExceptionNumber = a.Key.ExceptionNumber,
                Times = a.Sum(a1 => a1.Times),
                Amount = a.Sum(a1 => a1.Amount ?? 0),
                Type1 = OperDetailType.Added,
                Type2 = ModifyTypes.Return
            })
            .Where(a => a.Times > 0 || a.Amount > 0)
            .ToArray();

            var result = await LazyServiceProvider.LazyGetService<ISpeakerLimitService>().SavePrSpeakerLimitAsync(new PrUseSpeakerLimitRequest { PrApplicationId = prApplication.Id, BuId = prApplication.ApplyUserBu, Details = psaDatas });
            return result;
        }

        //async Task<MessageResult> ValidateCanWithdraw(IEnumerable<Guid> ids, ApprovalOperation status)
        //{
        //    // 申请人撤回, 作废
        //    if (status == ApprovalOperation.Recall || status == ApprovalOperation.Delete)
        //    {
        //        var pRApplicationRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>();
        //        var queryPRApplication = await pRApplicationRepository.GetQueryableAsync();
        //        var prApplications = queryPRApplication.Where(a => ids.Contains(a.Id)).ToArray();
        //        foreach (var pr in prApplications)
        //        {
        //            // 会议已激活
        //            if (pr.MeetingStatus == "1001")
        //            {
        //                return MessageResult.FailureResult(pr.ApplicationCode, "OM已激活该会议, NextBpm无法撤回&作废, pr number:" + pr.ApplicationCode + "!"); ;
        //            }
        //        }
        //    }

        //    return MessageResult.SuccessResult();
        //}

        //async Task<bool> WithdrawOmMeeting(IEnumerable<Guid> ids, ApprovalOperation status)
        //{
        //    bool result = true;
        //    // 申请人作废
        //    if (status == ApprovalOperation.Delete)
        //    {
        //        var pRApplicationRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>();
        //        var queryPRApplication = await pRApplicationRepository.GetQueryableAsync();
        //        var prApplications = queryPRApplication.Where(a => ids.Contains(a.Id)).ToArray();
        //        var omIntegrationService = LazyServiceProvider.LazyGetService<IIntegrationOmAppService>();
        //        foreach (var item in prApplications)
        //        {
        //            await omIntegrationService.OmRevokeMeeting(item.ApplicationCode);
        //        }
        //    }

        //    return result;
        //}

        /// <summary>
        /// 如果某次的事务请求超过ExecuteTransactionMaxmumBatchSize，则分批执行事务时，如果前面批次的成功，但是后面的批次失败，则需要将前面成功的进行回滚操作，暂时只支持新增的请求回滚
        /// </summary>
        /// <param name="allCreatedEntity">已经成功了的事务请求，字典的key是实体数据的guid，字典的value是实体的逻辑名</param>
        private void RollBackTransactionRequest(Dictionary<Guid, string> allCreatedEntity)
        {
            EntityCollection entityCollectionForDelete = new EntityCollection();
            foreach (KeyValuePair<Guid, string> createdEntity in allCreatedEntity)
            {
                entityCollectionForDelete.Entities.Add(new Entity(createdEntity.Value, createdEntity.Key));
            }
            if (entityCollectionForDelete.Entities.Count > 0)
            {
                TransactionRequest(null, null, entityCollectionForDelete);
            }
        }

        /// <summary>
        /// 批量操作CRM的数据，可以同时支持新增、编辑和删除，属于事务操作，要么全部成功，要么全部失败
        /// </summary>
        /// <param name="entityCollectionForCreate">需要创建的CRM数据集合</param>
        /// <param name="entityCollectionForUpdate">需要编辑的CRM数据集合</param>
        /// <param name="entityCollectionForDelete">需要删除的CRM数据集合</param>
        /// <param name="otherRequestList">其他request的集合，比如说GrantAccessRequest等等，默认为null代表没有该请求</param>
        /// <returns>全部成功后返回true，只要有一个失败了则回滚所有操作并返回false</returns>
        public bool TransactionRequest(EntityCollection entityCollectionForCreate, EntityCollection entityCollectionForUpdate, EntityCollection entityCollectionForDelete, OrganizationRequestCollection otherRequestList = null)
        {
            ExecuteTransactionResponse response = TransactionRequestAndReturnResponse(entityCollectionForCreate, entityCollectionForUpdate, entityCollectionForDelete, otherRequestList);
            return response != null;
        }

        /// <summary>
        /// 批量操作CRM的数据，可以同时支持新增、编辑和删除，属于事务操作，要么全部成功，要么全部失败
        /// 这里有一个限制，同一个事务的请求数量不能超过ExecuteTransactionMaxmumBatchSize，超过了就会报错
        /// </summary>
        /// <param name="entityCollectionForCreate">需要创建的CRM数据集合</param>
        /// <param name="entityCollectionForUpdate">需要编辑的CRM数据集合</param>
        /// <param name="entityCollectionForDelete">需要删除的CRM数据集合</param>
        /// <param name="otherRequestList">其他request的集合，比如说GrantAccessRequest等等，默认为null代表没有该请求</param>
        /// <returns>全部成功后返回ExecuteTransactionResponse，只要有一个失败了则回滚所有操作并返回null</returns>
        public ExecuteTransactionResponse TransactionRequestAndReturnResponse(EntityCollection entityCollectionForCreate, EntityCollection entityCollectionForUpdate, EntityCollection entityCollectionForDelete, OrganizationRequestCollection otherRequestList = null)
        {
            try
            {
                OrganizationRequestCollection requests = new OrganizationRequestCollection();
                // 创建带事务的请求
                ExecuteTransactionRequest multipleRequest = new ExecuteTransactionRequest()
                {
                    // 创建执行集合
                    Requests = new OrganizationRequestCollection(),
                    ReturnResponses = true
                };
                if (entityCollectionForCreate != null && entityCollectionForCreate.Entities.Count > 0)
                {
                    foreach (Entity entity in entityCollectionForCreate.Entities)
                    {
                        requests.Add(new CreateRequest { Target = entity });
                    }
                }
                if (entityCollectionForUpdate != null && entityCollectionForUpdate.Entities.Count > 0)
                {
                    foreach (Entity entity in entityCollectionForUpdate.Entities)
                    {
                        requests.Add(new UpdateRequest { Target = entity });
                    }
                }
                if (entityCollectionForDelete != null && entityCollectionForDelete.Entities.Count > 0)
                {
                    foreach (Entity entity in entityCollectionForDelete.Entities)
                    {
                        requests.Add(new DeleteRequest { Target = new EntityReference(entity.LogicalName, entity.Id) });
                    }
                }
                if (otherRequestList != null && otherRequestList.Count > 0)
                {
                    requests.AddRange(otherRequestList.ToArray());
                }
                //总数超过ExecuteTransactionMaxmumBatchSize的情况需要分开处理，因为一次事务的提交里面最多只能包含ExecuteTransactionMaxmumBatchSize个请求
                if (requests.Count > ExecuteTransactionMaxmumBatchSize)
                {
                    ExecuteTransactionResponse allResponse = new ExecuteTransactionResponse();
                    Dictionary<Guid, string> allCreatedEntity = new Dictionary<Guid, string>();
                    IEnumerable<OrganizationRequest> allRequests = requests.Take(requests.Count);
                    IEnumerable<OrganizationRequest> tempRequests = requests.Take(requests.Count);
                    int requestCount = ExecuteTransactionMaxmumBatchSize;
                    string responseName;
                    while (allRequests.Count() > 0)
                    {
                        requestCount = allRequests.Count() > ExecuteTransactionMaxmumBatchSize ? ExecuteTransactionMaxmumBatchSize : allRequests.Count();
                        multipleRequest.Requests.Clear();
                        tempRequests = allRequests.Take(requestCount);
                        multipleRequest.Requests.AddRange(tempRequests);
                        allRequests = allRequests.Skip(requestCount);
                        ExecuteTransactionResponse currentResponse = TransactionRequestBase(multipleRequest);
                        if (currentResponse != null)
                        {
                            if (allResponse.Responses != null)
                            {
                                allResponse.Responses.AddRange(currentResponse.Responses);
                            }
                            else
                            {
                                allResponse = currentResponse;
                            }
                            for (int i = 0; i < currentResponse.Responses.Count; i++)
                            {
                                responseName = currentResponse.Responses[i].ResponseName.ToLower();
                                if (responseName == "create")
                                {
                                    allCreatedEntity.Add((Guid)currentResponse.Responses[i]["id"], ((CreateRequest)tempRequests.ElementAt(i)).Target.LogicalName);
                                }
                            }
                        }
                        else
                        {
                            RollBackTransactionRequest(allCreatedEntity);
                            return null;
                        }
                    }
                    return allResponse;
                }
                else
                {
                    multipleRequest.Requests = requests;
                    return (ExecuteTransactionResponse)_dataverseRepository.DataverseClient.Execute(multipleRequest);
                }
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        /// <summary>
        /// 事务请求的base方法，私有方法，解决事务请求超过ExecuteTransactionMaxmumBatchSize报超限错误的问题
        /// </summary>
        /// <param name="multipleRequest">事务请求的集合，数量不能超过ExecuteTransactionMaxmumBatchSize</param>
        /// <returns>全部成功后返回ExecuteTransactionResponse，只要有一个失败了则回滚所有操作并返回null</returns>
        private ExecuteTransactionResponse TransactionRequestBase(ExecuteTransactionRequest multipleRequest)
        {
            try
            {
                return (ExecuteTransactionResponse)_dataverseRepository.DataverseClient.Execute(multipleRequest);
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        /// <summary>
        /// 获取审批所用的岗位员工列表
        /// </summary>
        /// <returns></returns>
        public async Task<List<ApprovalPersonListResponseDto>> GetApprovalPersonListAsync(ApprovalPersonListRequestDto request)
        {
            try
            {
                //查询审批岗位人员配置信息
                var queryPosition = new QueryExpression("spk_extensioncode");
                queryPosition.ColumnSet.AddColumns("spk_position", "spk_staffname");
                if (request.BuIds.Count > 0)
                    queryPosition.Criteria.AddCondition(new ConditionExpression("spk_organization", ConditionOperator.In, request.BuIds));
                LinkEntity linkPosition = queryPosition.AddLink("spk_positionmasterdata", "spk_position", "spk_positionmasterdataid", JoinOperator.Inner);
                linkPosition.Columns.AddColumn("spk_positiontypename");
                linkPosition.EntityAlias = "lines1";
                linkPosition.LinkCriteria.AddCondition("spk_positiontypename", ConditionOperator.Equal, (int)request.PositionType);
                LinkEntity linkStaff = queryPosition.AddLink("spk_staffmasterdata", "spk_staffname", "spk_staffmasterdataid", JoinOperator.Inner);
                linkStaff.Columns.AddColumns("spk_name", "spk_staffnumber", "spk_staffstate");
                linkStaff.EntityAlias = "lines2";

                var records = _dataverseRepository.DataverseClient.RetrieveMultiple(queryPosition);

                var query = records.Entities;
                //.Where(w => ((OptionSetValue)w.GetAttributeValue<AliasedValue>("lines2.spk_positiontypename").Value).Value == (int)request.WorkflowType);

                if (query == null) return null;
                var datas = query.Select(a =>
                                            new ApprovalPersonListResponseDto
                                            {
                                                UserId = a.GetAttributeValue<EntityReference>("spk_staffname").Id,
                                                UserName = a.GetAttributeValue<EntityReference>("spk_staffname").Name,
                                                UserNumber = a.GetAttributeValue<AliasedValue>("lines2.spk_staffnumber").Value.ToString(),
                                                State = ((OptionSetValue)a.GetAttributeValue<AliasedValue>("lines2.spk_staffstate")?.Value)?.Value.ToString(),
                                            }).DistinctBy(d => d.UserId);
                //if (request.UserIds?.Count > 0)
                //{
                //    datas = datas.Where(a => request.UserIds.Contains(a.UserId));
                //}

                return await Task.FromResult(datas.ToList());
            }
            catch (Exception ex)
            {
                _logger.LogError($"ApproveService's InitiateApprovalAsync has an error : {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 审批结果接收（用于PP审批流结束后调用）
        /// </summary>
        /// <returns></returns>
        public async Task<MessageResult> ApprovalResultsAsync(ApprovalResultsRequestDto request)
        {
            try
            {
                //根据业务ID查询相应表及数据
                var task = await (await _taskRepository.GetQueryableAsync()).AsNoTracking().FirstOrDefaultAsync(f => f.FormId == request.FormId && f.FormName != null);
                if (task == null)
                    return MessageResult.FailureResult("未找到相关记录");

                var messageResult = MessageResult.SuccessResult();
                switch (task.FormName)
                {
                    case NameConsts.VendorApplication:
                        messageResult = await UpdateVendorApplication(request, task);
                        break;
                    case NameConsts.PRApplication:
                        messageResult = await UpdatePrApplication(request);
                        break;
                    case NameConsts.BDApplication:
                        messageResult = await UpdatePurBDApplication(request);
                        break;
                    case NameConsts.POApplication:
                        messageResult = await UpdatePOApplicationStatus(request);
                        break;
                    case NameConsts.GRApplication:
                        messageResult = await UpdateGRApplicationStatus(request);
                        break;
                    case NameConsts.PAApplication:
                        messageResult = await UpdatePAApplicationStatus(request);
                        break;
                    case NameConsts.PurExemptWaiver:
                        messageResult = await UpdateBWApplication(request);
                        break;
                    case NameConsts.PurExemptJustification:
                        messageResult = await UpdateBWApplication(request);
                        break;
                    case NameConsts.OECSpeakerAuthApply:
                        messageResult = await UpdateSpeakerAuthApplyAsync(request);
                        break;
                    case NameConsts.STicketApplication:
                        messageResult = await UpdateSTicktApplyAsync(request, task);
                        break;
                    case NameConsts.FOCApplication:
                        messageResult = await UpdateFOCApplyAsync(request);
                        break;
                    case NameConsts.ReturnAndExchangeRequest:
                        messageResult = await UpdateReturnApplyAsync(request, task);
                        break;
                    default:
                        break;
                }

                ApprovalPowerAppStatus[] statusNeedToNotify = [ApprovalPowerAppStatus.Approved, ApprovalPowerAppStatus.Rejected, ApprovalPowerAppStatus.Denied];
                if (statusNeedToNotify.Contains(request.ApproveStatus) && messageResult.Success)
                    await PreSendEmailAsync(request.FormId, task.FormName, task.ApprovalTime, request.ApproveStatus);

                _logger.LogInformation($"ApprovalResultsAsync success:{request.FormId} {request.ApproveStatus}");
                return messageResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"ApprovalResultsAsync error: {request.FormId} {ex.Message} ");
                return MessageResult.FailureResult("服务器繁忙");
            }
        }

        /// <summary>
        ///  流程结束回调时，更新比价所有逻辑
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        private async Task<MessageResult> UpdatePurBDApplication(ApprovalResultsRequestDto request)
        {
            var bdApplication = _serviceProvider.GetService<IPurBDApplicationRepository>();
            var query = await bdApplication.GetQueryableAsync();
            var entities = await query.FirstAsync(x => request.FormId == x.Id);
            if (entities == null) return MessageResult.FailureResult("未查询到相关数据");
            if (entities.Status != PurBDStatus.Approving) return MessageResult.FailureResult($"该{request.FormId}单据已经完结，无需重复调用");
            switch (request.ApproveStatus)
            {
                case ApprovalPowerAppStatus.Approved:
                    entities.Status = PurBDStatus.Approved;
                    entities.ApprovedTime = DateTime.Now;
                    break;
                case ApprovalPowerAppStatus.Rejected:
                    entities.Status = PurBDStatus.Rejected;
                    break;
                case ApprovalPowerAppStatus.Denied:
                    entities.Status = PurBDStatus.Return;
                    break;
                default:
                    break;
            }
            await bdApplication.UpdateAsync(entities, true);

            //通过后，更新采购列表中采购明细对应的状态 
            if (request.ApproveStatus == ApprovalPowerAppStatus.Approved)
            {
                var prDetailRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>();
                var prDetailQuery = await prDetailRepository.GetQueryableAsync();
                var prDetailIds = entities.PRApplicationDetailId.Split(',').Select(Guid.Parse).OrderBy(a => a).ToList();
                var prDetail = prDetailQuery.Where(w => prDetailIds.Contains(w.Id)).ToList();
                prDetail.ForEach(f => { f.OrderStatusFlag = OrderStatusFlag.BiddingCompleted; });
                await prDetailRepository.UpdateManyAsync(prDetail);
            }
            //拒绝后，状态为拒绝
            if (request.ApproveStatus == ApprovalPowerAppStatus.Rejected)
            {
                var prDetailRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>();
                var prDetailQuery = await prDetailRepository.GetQueryableAsync();
                var prDetailIds = entities.PRApplicationDetailId.Split(',').Select(Guid.Parse).OrderBy(a => a).ToList();
                var prDetail = prDetailQuery.Where(w => prDetailIds.Contains(w.Id)).ToList();
                prDetail.ForEach(f =>
                {
                    f.BiddingId = null;
                    OrderStatusFlag[] orderStatusFlags = [OrderStatusFlag.Bidding, OrderStatusFlag.BiddingCompleted];//当前不在Bidding了就不更新状态了
                    if (f.OrderStatusFlag != null && orderStatusFlags.Contains(f.OrderStatusFlag.Value))
                        f.OrderStatusFlag = null;
                });
                await prDetailRepository.UpdateManyAsync(prDetail);
            }
            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 流程结束回调时，供应商数据更新
        /// </summary>
        /// <param name="businessFormId"></param>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<MessageResult> UpdateVendorApplication(ApprovalResultsRequestDto request, WorkflowTask task)
        {
            var vendor = _serviceProvider.GetService<IVendorRepository>();
            var vendorPersonal = _serviceProvider.GetService<IVendorPersonalRepository>();
            var vendorOrganization = _serviceProvider.GetService<IVendorOrgnizationRepository>();
            var vendorFinancial = _serviceProvider.GetService<IVendorFinancialRepository>();
            var vendorApplication = _serviceProvider.GetService<IVendorApplicationRepository>();
            var vendorApplicationPersonal = _serviceProvider.GetService<IVendorApplicationPersonalRepository>();
            var vendorApplicationOrganization = _serviceProvider.GetService<IVendorApplicationOrganizationRepository>();
            var vendorApplicationFinancial = _serviceProvider.GetService<IVendorApplicationFinancialRepository>();
            var vendorBlackListRepository = _serviceProvider.GetService<IVendorBlackListRepository>();
            try
            {
                //查询草稿数据
                var vendorApplicationData = await vendorApplication.FirstOrDefaultAsync(g => g.Id == request.FormId);
                if (vendorApplicationData == null) return MessageResult.FailureResult("未查询到相关数据");
                if (vendorApplicationData.Status != Statuses.Approving) return MessageResult.FailureResult($"该{request.FormId}单据已经完结，无需重复调用");

                switch (request.ApproveStatus)
                {
                    case ApprovalPowerAppStatus.Approved:
                        vendorApplicationData.Status = Statuses.Passed;
                        break;
                    case ApprovalPowerAppStatus.Rejected:
                        vendorApplicationData.Status = Statuses.Rejected;
                        break;
                    case ApprovalPowerAppStatus.Denied:
                        vendorApplicationData.Status = Statuses.Returned;
                        break;
                    default:
                        break;
                }
                //更新草稿状态
                await vendorApplication.UpdateAsync(vendorApplicationData, true);

                if (request.ApproveStatus != ApprovalPowerAppStatus.Approved)
                    return MessageResult.SuccessResult();

                //判断草稿类型，如果是创建则直接添加正式库，如果是变更或激活更新正式库
                if (vendorApplicationData.ApplicationType == ApplicationTypes.Create)
                {
                    //复制讲者草稿数据新增到正式数据
                    var vendorEntity = ObjectMapper.Map<Entities.VendorApplications.VendorApplication, Entities.Vendors.Vendor>(vendorApplicationData);
                    vendorEntity.BuCode = "100"; //TODO
                    vendorEntity.Status = VendorStatus.ToBeEffective;
                    vendorEntity.DraftVersion = 1;
                    //******** ytw 2220 除了身份证外 银行卡和DPS Check上传的附件都不带入正式列表
                    vendorEntity.BankCardImg = string.Empty;
                    vendorEntity.DPSCheck = string.Empty;
                    vendorEntity.AttachmentInformation = string.Empty;

                    //如果是供应商,查询黑名单列表中是否以前根据供应商名称添加过黑名单，如果是则状态直接变更为失效
                    var vendorApplicationOrganizationData = await vendorApplicationOrganization.FirstOrDefaultAsync(g => g.ApplicationId == vendorApplicationData.Id);
                    if (vendorEntity.VendorType == VendorTypes.HCIAndOtherInstitutionsAR || vendorEntity.VendorType == VendorTypes.NonHCIInstitutionalAP)
                    {
                        var vendorBlackQuery = await vendorBlackListRepository.GetQueryableAsync();
                        if (vendorBlackQuery.Any(w => w.SPName.Trim() == vendorApplicationOrganizationData.VendorName.Trim() && w.Status == BlackStatus.Effective))
                        {
                            vendorEntity.Status = VendorStatus.Exception;
                        }
                    }

                    var newEntity = await vendor.InsertAsync(vendorEntity, true);
                    //查询人员信息草稿
                    if (vendorEntity.VendorType == VendorTypes.HCPPerson || vendorEntity.VendorType == VendorTypes.NonHCPPerson)
                    {
                        var vendorApplicationPersonalData = await vendorApplicationPersonal.FirstOrDefaultAsync(g => g.ApplicationId == vendorApplicationData.Id);
                        var vendorPersonalEntity = ObjectMapper.Map<VendorApplicationPersonal, VendorPersonal>(vendorApplicationPersonalData);
                        vendorPersonalEntity.VendorId = newEntity.Id;
                        await vendorPersonal.InsertAsync(vendorPersonalEntity, true);
                    }
                    else
                    {
                        //查询组织信息草稿
                        var vendorOrganizationEntity = ObjectMapper.Map<VendorApplicationOrganization, VendorOrgnization>(vendorApplicationOrganizationData);
                        vendorOrganizationEntity.VendorId = newEntity.Id;
                        await vendorOrganization.InsertAsync(vendorOrganizationEntity, true);
                    }
                    //查询财务信息草稿
                    var vendorApplicationFinancialData = await vendorApplicationFinancial.GetListAsync(g => g.ApplicationId == vendorApplicationData.Id);
                    var vendorFinancialEntity = ObjectMapper.Map<List<VendorApplicationFinancial>, List<VendorFinancial>>(vendorApplicationFinancialData);
                    vendorFinancialEntity.ForEach(f => f.VendorId = newEntity.Id);
                    await vendorFinancial.InsertManyAsync(vendorFinancialEntity, true);

                    //调用BPCS接口同步供应商数据
                    var syncVendorResult = await LazyServiceProvider.LazyGetService<IInteBpcsVendorAppService>().SyncVendorByAppId(vendorApplicationData.Id);
                }
                else
                {
                    //复制讲者草稿数据更新到正式数据
                    var vendorData = await vendor.FirstOrDefaultAsync(g => g.VendorCode == vendorApplicationData.VendorCode);
                    //如果有敏感信息变更,则记录日志
                    RecordLog(vendorData, vendorApplicationData);

                    var vendorEntity = ObjectMapper.Map(vendorApplicationData, vendorData);
                    //******** ytw 2220 除了身份证外 银行卡和DPS Check上传的附件都不带入正式列表
                    vendorEntity.BankCardImg = string.Empty;
                    vendorEntity.DPSCheck = string.Empty;
                    vendorEntity.AttachmentInformation = string.Empty;

                    #region 讲者的变更或者激活时，对正式供应商表的EpdHospitalId的更新逻辑
                    if (request.ApproveStatus == ApprovalPowerAppStatus.Approved && vendorApplicationData.VendorType == VendorTypes.HCPPerson)
                    {
                        if (vendorApplicationData.ApplyBuName == BUNameConst.EPD)
                        {
                            //正常更新HospitalId和EpdHospitalId（对象映射）
                        }
                        else
                        {
                            //非EPD:通过所属医院的HospitalName或者VeevaId去查对应的Epd医院拿到Code写入到EpdHospitalId，找不到则EpdHospitalId置空
                            var dataverseService = _serviceProvider.GetService<IDataverseService>();
                            var epdHospitals = new List<BUHospitalDto>();
                            if (!string.IsNullOrEmpty(vendorApplicationData.HospitalName))
                                epdHospitals = await dataverseService.GetEPDBUHospitals(pattern: $"*\\\\*\\\\{vendorApplicationData.HospitalName}\\\\*", pageSize: 50000, count: 1, stateCode: null);

                            if (epdHospitals.Count == 0 && !string.IsNullOrEmpty(vendorApplicationData.VendorId))//VeevaId
                                epdHospitals = await dataverseService.GetEPDBUHospitals(pattern: $"*\\\\*\\\\*\\\\{vendorApplicationData.VendorId}", pageSize: 50000, count: 1, stateCode: null);

                            vendorEntity.EpdHospitalId = epdHospitals.Count > 0 ? epdHospitals.First().HospitalCode : null;
                        }
                    }
                    #endregion
                    //如果是激活操作，状态改为待激活，等待BPCS系统回应
                    if (vendorApplicationData.ApplicationType == ApplicationTypes.Active)
                    {
                        vendorEntity.Status = VendorStatus.ToBeActivated;
                    }
                    await vendor.UpdateAsync(vendorEntity, true);

                    if (vendorEntity.VendorType == VendorTypes.HCPPerson || vendorEntity.VendorType == VendorTypes.NonHCPPerson)
                    {
                        //查询人员信息
                        var vendorPersonalData = await vendorPersonal.FirstOrDefaultAsync(g => g.VendorId == vendorData.Id);
                        var vendorApplicationPersonalData = await vendorApplicationPersonal.FirstOrDefaultAsync(g => g.ApplicationId == vendorApplicationData.Id);
                        //如果有敏感信息变更,则记录日志
                        RecordPersonalLog(vendorPersonalData, vendorApplicationPersonalData, vendorApplicationData.ApplyUserId);
                        ObjectMapper.Map(vendorApplicationPersonalData, vendorPersonalData);
                        await vendorPersonal.UpdateAsync(vendorPersonalData, true);
                    }
                    else
                    {
                        //查询组织信息
                        var vendorOrganizationData = await vendorOrganization.FirstOrDefaultAsync(g => g.VendorId == vendorData.Id);
                        var vendorApplicationOrganizationData = await vendorApplicationOrganization.FirstOrDefaultAsync(g => g.ApplicationId == vendorApplicationData.Id);
                        ObjectMapper.Map(vendorApplicationOrganizationData, vendorOrganizationData);
                        await vendorOrganization.UpdateAsync(vendorOrganizationData, true);
                    }
                    //查询财务信息
                    var vendorFinancialData = await vendorFinancial.GetListAsync(g => g.VendorId == vendorData.Id);
                    var vendorApplicationFinancialData = await vendorApplicationFinancial.GetListAsync(g => g.ApplicationId == vendorApplicationData.Id);
                    //var vendorFinancialEntity = ObjectMapper.Map<List<VendorApplicationFinancial>, List<VendorFinancial>>(vendorApplicationFinancialData);
                    //vendorFinancialEntity.ForEach(f => f.VendorId = vendorData.Id);

                    //替换财务信息
                    //await vendorFinancial.DeleteManyAsync(vendorFinancialData, true);
                    //await vendorFinancial.InsertManyAsync(vendorFinancialEntity, true);

                    List<VendorFinancial> updatefinancials = new List<VendorFinancial>();
                    List<VendorFinancial> insertfinancials = new List<VendorFinancial>();
                    foreach (var item in vendorApplicationFinancialData)
                    {
                        //update或insert
                        //2770 [供应商管理]审批通过回调时，信息写回VendorFinancials不能以每个公司只保留一条记录的方式操作，历史数据中有同个供应商在同一主体有多账号的情况
                        var financial = vendorFinancialData.FirstOrDefault(a => a.Company == item.Company && a.VendorCode == item.VendorCode);
                        if (financial != null)
                        {
                            ObjectMapper.Map(item, financial);//保留Bpcs 相关字段及VendorId
                            updatefinancials.Add(financial);
                        }
                        else
                        {
                            var insertfinancial = ObjectMapper.Map<VendorApplicationFinancial, VendorFinancial>(item);
                            insertfinancial.VendorId = vendorData.Id;
                            insertfinancials.Add(insertfinancial);
                        }
                    }
                    if (updatefinancials.Count > 0)
                        await vendorFinancial.UpdateManyAsync(updatefinancials, true);
                    if (insertfinancials.Count > 0)
                        await vendorFinancial.InsertManyAsync(insertfinancials, true);

                    ////记录财务信息变化，如果更新数量有变化，则调用BPCS同步接口
                    //IEnumerable<VendorFinancial> financialList = null;
                    //if (vendorFinancialData.Count != vendorApplicationFinancialData.Count)
                    //{
                    //    vendorFinancialData.AddRange(vendorFinancialEntity);
                    //    financialList = vendorFinancialData.GroupBy(x => new { x.Company, x.Currency })
                    //        .Where(g => g.Count() == 1)
                    //        .SelectMany(g => g);
                    //}

                    ////调用BPCS接口同步供应商数据
                    //if (financialList != null)
                    //{
                    //    //变更财务时才同步
                    //    var getList = new List<Guid>();
                    //    foreach (var item in financialList)
                    //    {
                    //        getList.Add(vendorApplicationFinancial.FirstAsync(w => w.ApplicationId == vendorApplicationData.Id && w.Company == item.Company && w.Currency == item.Currency).Result.Id);
                    //    }
                    //    var syncVendorResult = await LazyServiceProvider.LazyGetService<IInteBpcsVendorAppService>().SyncVendorByAppId(vendorApplicationData.Id, getList);
                    //    if (!string.IsNullOrEmpty(syncVendorResult))
                    //    {
                    //        //只记录同步错误信息
                    //        _logger.LogError($"ApproveService's UpdateVendorApplication has an error : {syncVendorResult}"); Logger.LogError($"");
                    //    }
                    //}

                    //记录财务信息变化，如果更新数量有变化，则调用BPCS同步接口
                    IEnumerable<VendorFinancial> financialList = null;
                    if (insertfinancials.Count > 0)
                    {
                        vendorFinancialData.AddRange(insertfinancials);
                        financialList = vendorFinancialData.GroupBy(x => new { x.Company, x.Currency })
                            .Where(g => g.Count() == 1)
                            .SelectMany(g => g);
                    }

                    //调用BPCS接口同步供应商数据
                    if (financialList != null)
                    {
                        //变更财务时才同步
                        var getList = new List<Guid>();
                        foreach (var item in financialList)
                        {
                            getList.Add(vendorApplicationFinancial.FirstAsync(w => w.ApplicationId == vendorApplicationData.Id && w.Company == item.Company && w.Currency == item.Currency).Result.Id);
                        }
                        var syncVendorResult = await LazyServiceProvider.LazyGetService<IInteBpcsVendorAppService>().SyncVendorByAppId(vendorApplicationData.Id, getList);
                        if (!string.IsNullOrEmpty(syncVendorResult))
                        {
                            //只记录同步错误信息
                            _logger.LogError($"ApproveService's UpdateVendorApplication has an error : {syncVendorResult}"); Logger.LogError($"");
                        }
                    }
                }

                ////如果是讲者，同步数据到Bpm数据库
                //if (vendorApplicationData.VendorType == VendorTypes.HCPPerson)
                //    BackgroundJob.Enqueue<BpmSyncVendorWorker>(a => a.DoWorkAsync(vendorApplicationData.Id));

                return MessageResult.SuccessResult();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"ApproveService's UpdateVendorApplication has an error : {ex.Message}"); Logger.LogError($"");
                return MessageResult.FailureResult();
            }
        }

        /// <summary>
        /// 敏感基本信息变更后记录日志
        /// </summary>
        /// <param name="vendorData"></param>
        /// <param name="vendorApplicationData"></param>
        /// <exception cref="NotImplementedException"></exception>
        private void RecordLog(Entities.Vendors.Vendor vendorData, VendorApplication vendorApplicationData)
        {
            var _identityUserRepository = _serviceProvider.GetService<IRepository<IdentityUser, Guid>>();
            var operatorName = _identityUserRepository.FirstOrDefaultAsync(f => f.Id == vendorApplicationData.ApplyUserId).Result?.Name;
            if (vendorData.HandPhone != vendorApplicationData.HandPhone)
            {
                var dict = new Dictionary<string, string>
                {
                     { "OperatorName", operatorName },
                     { "Date", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fffffff") },
                     { "BeforeChange", vendorData.HandPhone },
                     { "AfterChange", vendorApplicationData.HandPhone }
                 };
                LazyServiceProvider.LazyGetService<IApplicationInsightService>().TrackEvent(ApplicationInsightEventNames.PhoneChange, dict);
            }
            if (vendorData.BankCardNo != vendorApplicationData.BankCardNo)
            {
                var dict = new Dictionary<string, string>
                {
                     { "OperatorName", operatorName },
                     { "Date", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fffffff") },
                     { "BeforeChange", vendorData.BankCardNo },
                     { "AfterChange", vendorApplicationData.BankCardNo }
                 };
                LazyServiceProvider.LazyGetService<IApplicationInsightService>().TrackEncryptEvent(ApplicationInsightEventNames.BankNoChange, dict);
            }
        }

        /// <summary>
        /// 敏感信息变更后记录日志
        /// </summary>
        /// <param name="vendorPersonalData"></param>
        /// <param name="vendorApplicationPersonalData"></param>
        /// <exception cref="NotImplementedException"></exception>
        private void RecordPersonalLog(VendorPersonal vendorPersonalData, VendorApplicationPersonal vendorApplicationPersonalData, Guid OperatorId)
        {
            var _identityUserRepository = _serviceProvider.GetService<IRepository<IdentityUser, Guid>>();
            var operatorName = _identityUserRepository.FirstOrDefaultAsync(f => f.Id == OperatorId).Result?.Name;
            if (vendorPersonalData.CardNo != vendorApplicationPersonalData.CardNo)
            {
                var dict = new Dictionary<string, string>
                {
                     { "OperatorName", operatorName },
                     { "Date", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fffffff") },
                     { "BeforeChange", vendorPersonalData.CardNo },
                     { "AfterChange", vendorApplicationPersonalData.CardNo }
                 };
                LazyServiceProvider.LazyGetService<IApplicationInsightService>().TrackEncryptEvent(ApplicationInsightEventNames.IdNoChange, dict);
            }
            if (vendorPersonalData.Email != vendorApplicationPersonalData.Email)
            {
                var dict = new Dictionary<string, string>
                {
                     { "OperatorName", operatorName },
                     { "Date", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fffffff") },
                     { "BeforeChange", vendorPersonalData.Email },
                     { "AfterChange", vendorApplicationPersonalData.Email }
                 };
                LazyServiceProvider.LazyGetService<IApplicationInsightService>().TrackEvent(ApplicationInsightEventNames.EmailChange, dict);
            }
        }

        /// <summary>
        /// 流程结束回调时，根据不同状态更新PR信息
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        async Task<MessageResult> UpdatePrApplication(ApprovalResultsRequestDto request)
        {
            PurPRApplicationStatus? status;
            switch (request.ApproveStatus)
            {
                case ApprovalPowerAppStatus.Approved:
                    status = PurPRApplicationStatus.Approved;
                    break;
                case ApprovalPowerAppStatus.Rejected:
                    status = PurPRApplicationStatus.Rejected;
                    break;
                case ApprovalPowerAppStatus.Denied:
                    //case ApprovalPowerAppStatus.Revoked:
                    status = PurPRApplicationStatus.RejectedBack;
                    break;
                case ApprovalPowerAppStatus.Delete:
                    status = PurPRApplicationStatus.ApplicantTerminate;
                    break;
                default:
                    status = null;
                    break;
            }
            if (!status.HasValue)
                return MessageResult.SuccessResult();

            var result = MessageResult.SuccessResult();
            var prRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>();
            var prDetailQueryable = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var prApplication = await prRepository.FindAsync(request.FormId);
            try
            {
                if (prApplication == null)
                    return MessageResult.FailureResult($"未找到Id为{request.FormId}的PR申请");
                if (prApplication.Status != PurPRApplicationStatus.Approving)
                    return MessageResult.FailureResult($"Id为{request.FormId}的PR申请已完结，无需重复调用");

                prApplication.Status = status.Value;

                //审批通过会根据采购明细付款方式生成相应任务
                if (prApplication.Status == PurPRApplicationStatus.Approved)
                {
                    var judgement = await LazyServiceProvider.LazyGetService<IPurPRApplicationService>().IsAllPrDetailPushedAndConfirmed(prApplication.Id);
                    if (judgement)
                        prApplication.Status = PurPRApplicationStatus.WaitForClose;
                    prApplication.ApprovedDate = DateTime.Now;
                    prApplication.IsOnceApproved = true;

                    // 推送会议给OM
                    if (prApplication.IsEsignUsed.HasValue && prApplication.IsEsignUsed == true)
                    {
                        var onOff = await LazyServiceProvider.LazyGetService<ISettingRepository>().FindAsync(SettingsConst.OmBpmInterfaceOnAndOff, "G", null);
                        if (onOff?.Value == "OFF")
                        {
                            //20241114 系统并行期间，使用BPM OM相关接口。后续需要回复使用OmAddMeeting
                            var omIntegrationService = LazyServiceProvider.LazyGetService<IIntegrationOmAppService>();
                            var addMeetingResult = await omIntegrationService.OmAddMeeting(prApplication.ApplicationCode);
                        }
                        else
                        {
                            //调度作业  老BPM
                            BackgroundJob.Enqueue<BpmOmWorker>(a => a.DoWorkAsync(new BpmRequestDto() { PrNo = prApplication.ApplicationCode, InterfaceType = Enums.Integration.OmBpmType.Add }));
                        }
                    }
                    var prDetails = prDetailQueryable.Where(a => a.IsHedge == false && a.PRApplicationId == prApplication.Id).ToList();
                    var isSandEmail = IsPushAndVendorConfimed(prDetails);
                    if (isSandEmail.Item1)
                    {
                        var userQuery = (await LazyServiceProvider.LazyGetService<IRepository<IdentityUser, Guid>>().GetQueryableAsync()).AsNoTracking();

                        var userIdsToNotify = await GetUsersToNotify(prApplication.ApplyUserId, prApplication.TransfereeId, ResignationTransfer.TaskFormCategory.PurchaseRequestApplication);

                        var users = userQuery.Where(a => userIdsToNotify.ToHashSet().Contains(a.Id)).ToArray();
                        var sendEmaillRecords = users.Select(user => new InsertSendEmaillRecordDto
                        {
                            EmailAddress = user.Email,
                            Subject = "[NexBPM消息中心]您有一个【{WorkflowTypeName}】正在等待您{ProcessType}。",
                            Content = JsonConvert.SerializeObject(new NotificationApplicantEmailDto
                            {
                                WorkflowTypeName = "采购申请",
                                ProcessType = isSandEmail.Item2,
                                UserName = user.Name,
                                ApplicationCode = prApplication.ApplicationCode,
                                ApplicationLink = $"{_configuration["SpeakerEmail:WebHost"]?.TrimEnd('/')}/task/launch",
                            }),
                            SourceType = EmailSourceType.ApprovalNotifyApplicant,
                            Status = SendStatus.Pending,
                            Attempts = 0
                        });

                        //记录邮件，并触发邮件发送功能
                        var emailService = LazyServiceProvider.LazyGetService<IEmailService>();
                        await emailService.InsertSendEmaillRecordAsync(sendEmaillRecords.ToList());
                        // 调度作业
                        BackgroundJob.Enqueue<SendEmailWorker>(a => a.DoWorkAsync());

                    }
                }
                //拒绝时，退回预算和PSA使用数据
                else if (prApplication.Status == PurPRApplicationStatus.Rejected)
                {
                    //退回预算
                    result = await ReturnBudgetAsync(prApplication);
                    if (!result.Success)
                        return result;

                    //退回Psa
                    result = await ReturnPsaAsync(prApplication);
                    if (!result.Success)
                        return result;
                }
                await prRepository.UpdateAsync(prApplication, true);

                _logger.LogWarning($"单号：{prApplication?.ApplicationCode} MeetingStatus：{prApplication.MeetingStatus}");
                //拒绝后需要 OM作废
                if (prApplication.Status == PurPRApplicationStatus.Rejected && prApplication.MeetingStatus == "1000")
                {
                    await LazyServiceProvider.GetService<IIntegrationOmAppService>().OmRevokeMeeting(prApplication.ApplicationCode);//后续需要优化，不应该再这里等着第三方接口返回，需要加重试机制
                }
            }
            catch (Exception ex)
            {
                await CurrentUnitOfWork.RollbackAsync();
                Logger.LogError(ex, $"{prApplication?.ApplicationCode}更新Pr状态失败");
                return MessageResult.FailureResult(ex.Message, ex.StackTrace);
            }

            return result;
        }

        private (bool, string) IsPushAndVendorConfimed(List<PurPRApplicationDetail> prDetails)
        {
            //采购申请 采购申请    申请审批通过后，且该申请单包含未确认AR行 + 未推送AP行     确认供应商及推送采购
            //采购申请 采购申请    申请审批通过后，且该申请单仅包含未确认AR行               确认供应商
            //采购申请 采购申请    申请审批通过后，且该申请单仅包含未推送AP行               推送采购

            var arPRD = prDetails.Where(a => a.PayMethod == PayMethods.AR);//AR
            var apPRD = prDetails.Where(a => a.PayMethod == PayMethods.AP);//AP
            if (arPRD.Any() == false && apPRD.Any() == false)//没有AR和AP
                return (false, "");

            //仅包含未确认AR行
            if (arPRD.Any() == true && apPRD.Any() == false)
            {
                if (arPRD.Any(a => a.IsVendorConfimed.HasValue == false || a.IsVendorConfimed == false))//未确认AR行
                {
                    return (true, NotifyApplicantProcessType.QRGYS);
                }
            }
            //仅包含未确认AP行
            if (arPRD.Any() == false && apPRD.Any() == true)
            {
                if (apPRD.Any(a => a.PushFlag == PushFlagEnum.NotPushed || a.PushFlag == PushFlagEnum.Rollback))
                {
                    return (true, NotifyApplicantProcessType.TSCG);
                }
            }
            //AR AP行都存在
            if (arPRD.Any() == true && apPRD.Any() == true)
            {
                if (arPRD.Any(a => a.IsVendorConfimed.HasValue == false || a.IsVendorConfimed == false) && apPRD.Any(a => a.PushFlag == PushFlagEnum.NotPushed || a.PushFlag == PushFlagEnum.Rollback))
                {
                    return (true, NotifyApplicantProcessType.QRGYSJTSCG);
                }
            }

            return (false, ""); //不发送邮件
        }

        /// <summary>
        /// 我发起的审批任务数量统计（废弃）
        /// </summary>
        /// <returns></returns>
        public async Task<MessageResult> InitiatedApprovalCountAsync()
        {
            List<ApprovalCountDto> countList = [];
            //查询我发起的待处理
            string xrmQuery = @"<fetch aggregate='true'>
                          <entity name='spk_workflowinstance'>
                            <attribute name='spk_workflowinstanceid' alias='Expr1' aggregate='count' />
                                <filter>
								  <condition attribute='statecode' operator='eq' value='0' />
                                  <condition attribute='spk_submitter' operator='eq' value='{0}' />
                                      <filter type='or'>
                                        <condition attribute='spk_status' operator='eq' value='100000003' />
                                        <condition attribute='spk_status' operator='eq' value='100000004' />
                                      </filter>
                                </filter>
                          </entity>
                      </fetch>";
            string fetchXrm = string.Format(xrmQuery, CurrentUser.Id);
            var responseCount = _dataverseRepository.DataverseClient.RetrieveMultiple(new FetchExpression(fetchXrm));
            var jsonObject = responseCount.Entities[0].Attributes.Values.First();
            var taskCount = (int)jsonObject.GetType().GetProperty("Value")?.GetValue(jsonObject);
            countList.Add(new ApprovalCountDto() { ShowType = "Waiting", Count = taskCount });

            //查询我发起的进行中
            string xrmIngQuery = @"<fetch aggregate='true'>
                          <entity name='spk_workflowinstance'>
                            <attribute name='spk_workflowinstanceid' alias='Expr1' aggregate='count' />
                                <filter>
							      <condition attribute='statecode' operator='eq' value='0' />
                                  <condition attribute='spk_submitter' operator='eq' value='{0}' />
                                     <filter type='or'>
                                        <condition attribute='spk_status' operator='eq' value='100000000' />
                                      </filter>
                                </filter>
                          </entity>
                      </fetch>";
            string fetchXrmIng = string.Format(xrmIngQuery, CurrentUser.Id);
            var responseIngCount = _dataverseRepository.DataverseClient.RetrieveMultiple(new FetchExpression(fetchXrmIng));
            var jsonIngObject = responseIngCount.Entities[0].Attributes.Values.First();
            var taskIngCount = (int)jsonObject.GetType().GetProperty("Value")?.GetValue(jsonIngObject);
            countList.Add(new ApprovalCountDto() { ShowType = "Pending", Count = taskIngCount });

            //查询我发起的已完成
            string xrmEndQuery = @"<fetch aggregate='true'>
                          <entity name='spk_workflowinstance'>
                            <attribute name='spk_workflowinstanceid' alias='Expr1' aggregate='count' />
                                <filter>
                                  <condition attribute='statecode' operator='eq' value='0' />
                                  <condition attribute='spk_submitter' operator='eq' value='{0}' />
                                     <filter type='or'>
                                        <condition attribute='spk_status' operator='eq' value='100000001' />
                                        <condition attribute='spk_status' operator='eq' value='100000002' />
                                        <condition attribute='spk_status' operator='eq' value='100000005' />
                                      </filter>
                                </filter>
                          </entity>
                      </fetch>";
            string fetchXrmEnd = string.Format(xrmEndQuery, CurrentUser.Id);
            var responseEndCount = _dataverseRepository.DataverseClient.RetrieveMultiple(new FetchExpression(fetchXrmEnd));
            var jsonEndObject = responseEndCount.Entities[0].Attributes.Values.First();
            var taskEndCount = (int)jsonObject.GetType().GetProperty("Value")?.GetValue(jsonEndObject);
            countList.Add(new ApprovalCountDto() { ShowType = "Completed", Count = taskEndCount });
            return await Task.FromResult(MessageResult.SuccessResult(countList));
        }

        /// <summary>
        /// 我审批的审批任务数量统计（废弃）
        /// </summary>
        /// <returns></returns>
        public async Task<MessageResult> GetApprovalCountAsync()
        {
            List<ApprovalCountDto> countList = [];
            //查询审批task待处理
            string xrmQuery = @"<fetch aggregate='true'>
                          <entity name='spk_workflowtask'>
                            <attribute name='spk_workflowtaskid' alias='Expr1' aggregate='count' />
                                <filter>
                                  <condition attribute='spk_approver' operator='eq' value='{0}' />
                                        <filter type='or'>
                                        <condition attribute='spk_approvalstatus' operator='eq' value='100000000' />
                                      </filter>
                                </filter>
                          </entity>
                      </fetch>";
            string fetchXrm = string.Format(xrmQuery, CurrentUser.Id);
            var responseCount = _dataverseRepository.DataverseClient.RetrieveMultiple(new FetchExpression(fetchXrm));
            var jsonObject = responseCount.Entities[0].Attributes.Values.First();
            var taskCount = (int)jsonObject.GetType().GetProperty("Value")?.GetValue(jsonObject);
            countList.Add(new ApprovalCountDto() { ShowType = "Waiting", Count = taskCount });
            //查询审批task已操作
            string xrmEndQuery = @"<fetch aggregate='true'>
                          <entity name='spk_workflowtask'>
                            <attribute name='spk_workflowtaskid' alias='Expr1' aggregate='count' />
                                <filter>
                                  <condition attribute='spk_approver' operator='eq' value='{0}' />
                                      <filter type='or'>
                                        <condition attribute='spk_approvalstatus' operator='eq' value='100000001' />
                                        <condition attribute='spk_approvalstatus' operator='eq' value='100000002' />
                                        <condition attribute='spk_approvalstatus' operator='eq' value='100000003' />
                                      </filter>
                                </filter>
                          </entity>
                      </fetch>";
            string fetchXrmEnd = string.Format(xrmEndQuery, CurrentUser.Id);
            var responseEndCount = _dataverseRepository.DataverseClient.RetrieveMultiple(new FetchExpression(fetchXrmEnd));
            var jsonEndObject = responseEndCount.Entities[0].Attributes.Values.First();
            var taskEndCount = (int)jsonObject.GetType().GetProperty("Value")?.GetValue(jsonEndObject);
            countList.Add(new ApprovalCountDto() { ShowType = "Completed", Count = taskEndCount });
            return await Task.FromResult(MessageResult.SuccessResult(countList));
        }

        /// <summary>
        /// 我发起的审批任务数量统计(ABP端)
        /// </summary>
        /// <returns></returns>
        public async Task<MessageResult> InitiatedApprovalCountABPAsync()
        {
            //暂时加一个开关，用于跳过任务中心-我发起的-数量统计
            if (await LazyServiceProvider.LazyGetService<IRedisRepository>().Database.KeyExistsAsync(Consts.RedisKey.SkipCount))
                return MessageResult.SuccessResult(Array.Empty<ApprovalCountDto>());

            var queryableVA = await LazyServiceProvider.LazyGetService<IVendorApplicationRepository>().GetQueryableAsync();
            var queryablePR = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var queryablePRdetail = await LazyServiceProvider.GetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var queryableBD = await LazyServiceProvider.LazyGetService<IPurBDApplicationRepository>().GetQueryableAsync();
            var queryablePO = await LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>().GetQueryableAsync();
            var queryableGR = await LazyServiceProvider.LazyGetService<IPurGRApplicationRepository>().GetQueryableAsync();
            var queryablePA = await LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>().GetQueryableAsync();
            var queryableSA = await LazyServiceProvider.LazyGetService<IOECSpeakerAuthApplyRepository>().GetQueryableAsync();
            var queryableBW = await LazyServiceProvider.LazyGetService<IPurBWApplicationRepository>().GetQueryableAsync();
            var queryableST = await LazyServiceProvider.LazyGetService<ISTicketApplicationRepository>().GetQueryableAsync();
            var queryableFoc = await LazyServiceProvider.LazyGetService<IFocApplicationRepository>().GetQueryableAsync();
            var queryableRe = await LazyServiceProvider.LazyGetService<IReturnApplicationReponsitory>().GetQueryableAsync();
            //获取代理信息
            var agents = await LazyServiceProvider.LazyGetService<IAgencyService>().GetOriginalOperatorsForTaskCenterPendingCount(new GetAgentOperatorsRequestDto());
            agents = agents.Where(a => a.userId != CurrentUser.Id);
            var vUserIds = agents.Where(a => !a.category.HasValue || a.category == ResignationTransfer.TaskFormCategory.VendorApplication).Select(a => a.userId).ToArray();
            var pUserIds = agents.Where(a => !a.category.HasValue || a.category == ResignationTransfer.TaskFormCategory.PurchaseRequestApplication).Select(a => a.userId).ToArray();
            var bUserIds = agents.Where(a => !a.category.HasValue || a.category == ResignationTransfer.TaskFormCategory.BiddingApplication).Select(a => a.userId).ToArray();
            var oUserIds = agents.Where(a => !a.category.HasValue || a.category == ResignationTransfer.TaskFormCategory.PurhcaseOrderApplication).Select(a => a.userId).ToArray();
            var gUserIds = agents.Where(a => !a.category.HasValue || a.category == ResignationTransfer.TaskFormCategory.GoodsReceiptApplication).Select(a => a.userId).ToArray();
            var aUserIds = agents.Where(a => !a.category.HasValue || a.category == ResignationTransfer.TaskFormCategory.PaymentApplication).Select(a => a.userId).ToArray();
            var saUserIds = agents.Where(a => !a.category.HasValue || a.category == ResignationTransfer.TaskFormCategory.OECSpeakerAuthApplication).Select(a => a.userId).ToArray();
            var bwUserIds = agents.Where(a => !a.category.HasValue || a.category == ResignationTransfer.TaskFormCategory.BiddingWaiverApplication).Select(a => a.userId).ToArray();
            var juUserIds = agents.Where(a => !a.category.HasValue || a.category == ResignationTransfer.TaskFormCategory.JustificationApplication).Select(a => a.userId).ToArray();
            var stUserIds = agents.Where(a => !a.category.HasValue || a.category == ResignationTransfer.TaskFormCategory.STicketRequestApplication).Select(a => a.userId).ToArray();
            var focUserIds = agents.Where(a => !a.category.HasValue || a.category == ResignationTransfer.TaskFormCategory.FOCRequestApplication).Select(a => a.userId).ToArray();
            var returnUserIds = agents.Where(a => !a.category.HasValue || a.category == ResignationTransfer.TaskFormCategory.ReturnAndExchangeRequest).Select(a => a.userId).ToArray();

            var countList = queryableVA.Where(a => (a.Status == Statuses.Returned || a.Status == Statuses.Withdraw) && ((a.ApplyUserId == CurrentUser.Id.Value && !a.TransfereeId.HasValue) || CurrentUser.Id.Value == a.TransfereeId.Value || vUserIds.ToHashSet().Contains(a.ApplyUserId)))
                .GroupBy(g => new { VendorType = g.VendorType == VendorTypes.HCPPerson ? "SP" : "NSP" })
                .Select(s => new ApprovalCountDto { ShowType = "Waiting", BusinessType = s.Key.VendorType == "SP" ? "speaker" : "nonSpeaker", Count = s.Count() })
                .Concat
                (
                    queryablePR.Where(w => w.Status == PurPRApplicationStatus.RejectedBack && ((w.ApplyUserId == CurrentUser.Id.Value && !w.TransfereeId.HasValue) || CurrentUser.Id.Value == w.TransfereeId.Value || pUserIds.ToHashSet().Contains(w.ApplyUserId))).Select(a => new { Type = "PR-Create" })
                    .Concat
                    (
                        queryablePR.Where(w => w.Status == PurPRApplicationStatus.Approved && ((w.ApplyUserId == CurrentUser.Id.Value && !w.TransfereeId.HasValue) || CurrentUser.Id.Value == w.TransfereeId.Value || pUserIds.ToHashSet().Contains(w.ApplyUserId)) && queryablePRdetail.Where(a => a.PRApplicationId == w.Id && !a.IsHedge).Any(a => a.PushFlag != PushFlagEnum.Pushed || a.IsVendorConfimed == null || a.IsVendorConfimed == false)).Select(a => new { Type = "PR-Create" })
                    )
                    .GroupBy(a => a.Type).Select(s => new ApprovalCountDto { ShowType = "Waiting", BusinessType = "prCreate", Count = s.Count() })
                )
                .Concat
                (
                    queryableBD.Where(w => (w.Status == PurBDStatus.Return || w.Status == PurBDStatus.Recall) && ((w.ApplyUserId == CurrentUser.Id.Value && !w.TransfereeId.HasValue) || CurrentUser.Id.Value == w.TransfereeId.Value || bUserIds.ToHashSet().Contains(w.ApplyUserId))).Select(a => "BD-Create")
                    .GroupBy(g => g).Select(s => new ApprovalCountDto { ShowType = "Waiting", BusinessType = "bdCreate", Count = s.Count() })
                )
                .Concat
                (
                     queryablePO.Where(w => (w.Status == PurOrderStatus.Return || w.Status == PurOrderStatus.Withdraw || w.Status == PurOrderStatus.ApplierConfirmed || w.Status == PurOrderStatus.InitiateReceipt) && ((w.ApplyUserId == CurrentUser.Id.Value && !w.TransfereeId.HasValue) || CurrentUser.Id.Value == w.TransfereeId.Value || oUserIds.ToHashSet().Contains(w.ApplyUserId))).Select(a => "PO-Create")
                    .GroupBy(g => g).Select(s => new ApprovalCountDto { ShowType = "Waiting", BusinessType = "poCreate", Count = s.Count() })
                )
                .Concat
                (
                    queryableGR.Where(w => (w.Status == PurGRApplicationStatus.Returned || w.Status == PurGRApplicationStatus.ToBeReceived) && ((w.ApplyUserId == CurrentUser.Id.Value && !w.TransfereeId.HasValue) || CurrentUser.Id.Value == w.TransfereeId.Value || gUserIds.ToHashSet().Contains(w.ApplyUserId))).Select(a => "GR-Create")
                    .GroupBy(g => g).Select(s => new ApprovalCountDto { ShowType = "Waiting", BusinessType = "grCreate", Count = s.Count() })
                )
                .Concat
                (
                    queryablePA.Where(w => (w.Status == PurPAApplicationStatus.Reissue || w.Status == PurPAApplicationStatus.FillIn) && ((w.ApplyUserId == CurrentUser.Id.Value && !w.TransfereeId.HasValue) || CurrentUser.Id.Value == w.TransfereeId.Value || aUserIds.ToHashSet().Contains(w.ApplyUserId))).Select(a => "PA-Create")
                    .GroupBy(g => g).Select(s => new ApprovalCountDto { ShowType = "Waiting", BusinessType = "paCreate", Count = s.Count() })
                )
                .Concat
                (
                    queryableSA.Where(w => w.Status == SpeakerAuthStatus.Return && ((w.ApplyUserId == CurrentUser.Id.Value && !w.TransfereeId.HasValue) || CurrentUser.Id.Value == w.TransfereeId.Value || saUserIds.ToHashSet().Contains(w.ApplyUserId))).Select(a => "SA-Create")
                    .GroupBy(g => g).Select(s => new ApprovalCountDto { ShowType = "Waiting", BusinessType = "saCreate", Count = s.Count() })
                )
                .Concat
                (
                    queryableBW.Where(w => w.ExemptType == ExemptType.Waiver && w.Status == PurExemptStatus.Return && ((w.ApplyUserId == CurrentUser.Id.Value && !w.TransfereeId.HasValue) || CurrentUser.Id.Value == w.TransfereeId.Value || bwUserIds.ToHashSet().Contains(w.ApplyUserId))).Select(a => "BW-Create")
                    .GroupBy(g => g).Select(s => new ApprovalCountDto { ShowType = "Waiting", BusinessType = "bwCreate", Count = s.Count() })
                )
                .Concat
                (
                    queryableBW.Where(w => w.ExemptType == ExemptType.Justification && w.Status == PurExemptStatus.Return && ((w.ApplyUserId == CurrentUser.Id.Value && !w.TransfereeId.HasValue) || CurrentUser.Id.Value == w.TransfereeId.Value || juUserIds.ToHashSet().Contains(w.ApplyUserId))).Select(a => "JU-Create")
                    .GroupBy(g => g).Select(s => new ApprovalCountDto { ShowType = "Waiting", BusinessType = "juCreate", Count = s.Count() })
                )
                .Concat
                (
                    queryableST.Where(w => (w.Status == STicketStatus.RejectedBack || w.Status == STicketStatus.ReCall) && ((w.ApplyUserId == CurrentUser.Id.Value && !w.TransfereeId.HasValue) || CurrentUser.Id.Value == w.TransfereeId.Value || stUserIds.ToHashSet().Contains(w.ApplyUserId))).Select(a => "ST-Create")
                    .GroupBy(g => g).Select(s => new ApprovalCountDto { ShowType = "Waiting", BusinessType = "stCreate", Count = s.Count() })
                )
                .Concat
                (
                    queryableFoc.Where(w => w.Status == FOCStatus.RejectedBack && ((w.ApplyUserId == CurrentUser.Id.Value && !w.TransfereeId.HasValue) || CurrentUser.Id.Value == w.TransfereeId.Value || focUserIds.ToHashSet().Contains(w.ApplyUserId))).Select(a => "FOC-Create")
                    .GroupBy(g => g).Select(s => new ApprovalCountDto { ShowType = "Waiting", BusinessType = "focCreate", Count = s.Count() })
                )
                .Concat
                (
                    queryableRe.Where(w => w.ApplicationType == ReturnTypeEnum.Return && w.Status == ReturnApplicationStatus.RejectedBack && ((w.ApplyUserId == CurrentUser.Id.Value && !w.TransfereeId.HasValue) || CurrentUser.Id.Value == w.TransfereeId.Value || returnUserIds.ToHashSet().Contains(w.ApplyUserId))).Select(a => "RE-Create")
                    .GroupBy(g => g).Select(s => new ApprovalCountDto { ShowType = "Waiting", BusinessType = "reCreate", Count = s.Count() })
                )
                .Concat
                (
                    queryableRe.Where(w => w.ApplicationType == ReturnTypeEnum.Exchange && w.Status == ReturnApplicationStatus.RejectedBack && ((w.ApplyUserId == CurrentUser.Id.Value && !w.TransfereeId.HasValue) || CurrentUser.Id.Value == w.TransfereeId.Value || returnUserIds.ToHashSet().Contains(w.ApplyUserId))).Select(a => "EX-Create")
                    .GroupBy(g => g).Select(s => new ApprovalCountDto { ShowType = "Waiting", BusinessType = "exCreate", Count = s.Count() })
                )
                .ToList();
            return MessageResult.SuccessResult(countList);
        }

        /// <summary>
        /// 批量查询 userIds 发起的审批任务数量统计(ABP端)
        /// </summary>
        /// <returns></returns>
        public async Task<MessageResult<List<ApprovalCountByUserDto>>> InitiatedApprovalCountABPAsync(List<Guid> userIds)
        {
            List<ApprovalCountByUserDto> countList = [];
            if (userIds?.Any() != true)
            {
                return MessageResult<List<ApprovalCountByUserDto>>.SuccessResult(countList);
            }

            var queryableVA = await LazyServiceProvider.LazyGetService<IVendorApplicationRepository>().GetQueryableAsync();
            var queryablePR = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            //var queryableJJ = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            //var queryableJF = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var queryableBD = await LazyServiceProvider.LazyGetService<IPurBDApplicationRepository>().GetQueryableAsync();
            var queryablePO = await LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>().GetQueryableAsync();
            var queryableGR = await LazyServiceProvider.LazyGetService<IPurGRApplicationRepository>().GetQueryableAsync();
            var queryablePA = await LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>().GetQueryableAsync();
            var queryableST = await LazyServiceProvider.LazyGetService<ISTicketApplicationRepository>().GetQueryableAsync();
            var queryableFoc = await LazyServiceProvider.LazyGetService<IFocApplicationRepository>().GetQueryableAsync();
            var countAllList = queryableVA
                .Where(w => w.VendorType == VendorTypes.HCPPerson && w.ApplicationType == ApplicationTypes.Create && userIds.Contains(w.ApplyUserId)).GroupBy(g => new { g.Status, g.ApplyUserId }).Select(s => new { userId = s.Key.ApplyUserId, type = "SP-Create", status = (int)s.Key.Status, count = s.Count() })
                .Union(queryableVA.Where(w => w.VendorType == VendorTypes.HCPPerson && w.ApplicationType == ApplicationTypes.Update && userIds.Contains(w.ApplyUserId)).GroupBy(g => new { g.Status, g.ApplyUserId }).Select(s => new { userId = s.Key.ApplyUserId, type = "SP-Update", status = (int)s.Key.Status, count = s.Count() }))
                .Union(queryableVA.Where(w => w.VendorType == VendorTypes.HCPPerson && w.ApplicationType == ApplicationTypes.Active && userIds.Contains(w.ApplyUserId)).GroupBy(g => new { g.Status, g.ApplyUserId }).Select(s => new { userId = s.Key.ApplyUserId, type = "SP-Active", status = (int)s.Key.Status, count = s.Count() }))
                .Union(queryableVA.Where(w => w.VendorType != VendorTypes.HCPPerson && userIds.Contains(w.ApplyUserId) && w.ApplicationType == ApplicationTypes.Create).GroupBy(g => new { g.Status, g.ApplyUserId }).Select(s => new { userId = s.Key.ApplyUserId, type = "NSP-Create", status = (int)s.Key.Status, count = s.Count() }))
                .Union(queryableVA.Where(w => w.VendorType != VendorTypes.HCPPerson && userIds.Contains(w.ApplyUserId) && w.ApplicationType == ApplicationTypes.Update).GroupBy(g => new { g.Status, g.ApplyUserId }).Select(s => new { userId = s.Key.ApplyUserId, type = "NSP-Update", status = (int)s.Key.Status, count = s.Count() }))
                .Union(queryableVA.Where(w => w.VendorType != VendorTypes.HCPPerson && userIds.Contains(w.ApplyUserId) && w.ApplicationType == ApplicationTypes.Active).GroupBy(g => new { g.Status, g.ApplyUserId }).Select(s => new { userId = s.Key.ApplyUserId, type = "NSP-Active", status = (int)s.Key.Status, count = s.Count() }))
                .Union(queryablePR.Where(w => userIds.Contains(w.ApplyUserId)).GroupBy(g => new { g.Status, g.ApplyUserId }).Select(s => new { userId = s.Key.ApplyUserId, type = "PR-Create", status = (int)s.Key.Status, count = s.Count() }))
                .Union(queryableBD.Where(w => w.CreatorId != null && userIds.Contains(w.CreatorId.Value)).GroupBy(g => new { g.Status, g.ApplyUserId }).Select(s => new { userId = s.Key.ApplyUserId, type = "BD-Create", status = (int)s.Key.Status, count = s.Count() }))
                .Union(queryablePO.Where(w => w.CreatorId != null && userIds.Contains(w.CreatorId.Value)).GroupBy(g => new { g.Status, g.ApplyUserId }).Select(s => new { userId = s.Key.ApplyUserId, type = "PO-Create", status = (int)s.Key.Status, count = s.Count() }))
                .Union(queryableGR.Where(w => w.CreatorId != null && userIds.Contains(w.CreatorId.Value)).GroupBy(g => new { g.Status, g.ApplyUserId }).Select(s => new { userId = s.Key.ApplyUserId, type = "GR-Create", status = (int)s.Key.Status, count = s.Count() }))
                .Union(queryablePA.Where(w => userIds.Contains(w.ApplyUserId)).GroupBy(g => new { g.Status, g.ApplyUserId }).Select(s => new { userId = s.Key.ApplyUserId, type = "PA-Create", status = (int)s.Key.Status, count = s.Count() }))
                .ToList();

            //讲者新建
            var spCreateWaiting = countAllList.Where(w => w.type == "SP-Create" && (w.status == (int)Statuses.Returned || w.status == (int)Statuses.Withdraw))
                .GroupBy(a => a.userId).Select(g => new { g_userId = g.Key, g_count = g.Sum(a => a.count) });
            countList.AddRange(spCreateWaiting.Select(a => new ApprovalCountByUserDto() { RelatedUserId = a.g_userId, ShowType = "Waiting", BusinessType = "speakerCreate", Count = a.g_count }));
            var spCreatePending = countAllList.Where(w => w.type == "SP-Create" && w.status == (int)Statuses.Approving)
                .GroupBy(a => a.userId).Select(g => new { g_userId = g.Key, g_count = g.Sum(a => a.count) });
            countList.AddRange(spCreatePending.Select(a => new ApprovalCountByUserDto() { RelatedUserId = a.g_userId, ShowType = "Pending", BusinessType = "speakerCreate", Count = a.g_count }));
            var spCreateCompleted = countAllList.Where(w => w.type == "SP-Create" && (w.status == (int)Statuses.Rejected || w.status == (int)Statuses.Delete || w.status == (int)Statuses.Passed))
                .GroupBy(a => a.userId).Select(g => new { g_userId = g.Key, g_count = g.Sum(a => a.count) });
            countList.AddRange(spCreateCompleted.Select(a => new ApprovalCountByUserDto() { RelatedUserId = a.g_userId, ShowType = "Completed", BusinessType = "speakerCreate", Count = a.g_count }));
            //讲者变更
            var spUpdateWaiting = countAllList.Where(w => w.type == "SP-Update" && (w.status == (int)Statuses.Returned || w.status == (int)Statuses.Withdraw))
                .GroupBy(a => a.userId).Select(g => new { g_userId = g.Key, g_count = g.Sum(a => a.count) });
            countList.AddRange(spUpdateWaiting.Select(a => new ApprovalCountByUserDto() { RelatedUserId = a.g_userId, ShowType = "Waiting", BusinessType = "speakerUpdate", Count = a.g_count }));
            var spUpdatePending = countAllList.Where(w => w.type == "SP-Update" && w.status == (int)Statuses.Approving)
                .GroupBy(a => a.userId).Select(g => new { g_userId = g.Key, g_count = g.Sum(a => a.count) });
            countList.AddRange(spUpdatePending.Select(a => new ApprovalCountByUserDto() { RelatedUserId = a.g_userId, ShowType = "Pending", BusinessType = "speakerUpdate", Count = a.g_count }));
            var spUpdateCompleted = countAllList.Where(w => w.type == "SP-Update" && (w.status == (int)Statuses.Rejected || w.status == (int)Statuses.Delete || w.status == (int)Statuses.Passed))
                .GroupBy(a => a.userId).Select(g => new { g_userId = g.Key, g_count = g.Sum(a => a.count) });
            countList.AddRange(spUpdateCompleted.Select(a => new ApprovalCountByUserDto() { RelatedUserId = a.g_userId, ShowType = "Completed", BusinessType = "speakerUpdate", Count = a.g_count }));
            //讲者激活
            var spActiveWaiting = countAllList.Where(w => w.type == "SP-Active" && (w.status == (int)Statuses.Returned || w.status == (int)Statuses.Withdraw))
                .GroupBy(a => a.userId).Select(g => new { g_userId = g.Key, g_count = g.Sum(a => a.count) });
            countList.AddRange(spActiveWaiting.Select(a => new ApprovalCountByUserDto() { RelatedUserId = a.g_userId, ShowType = "Waiting", BusinessType = "speakerActive", Count = a.g_count }));
            var spActivePending = countAllList.Where(w => w.type == "SP-Active" && w.status == (int)Statuses.Approving)
                .GroupBy(a => a.userId).Select(g => new { g_userId = g.Key, g_count = g.Sum(a => a.count) });
            countList.AddRange(spActivePending.Select(a => new ApprovalCountByUserDto() { RelatedUserId = a.g_userId, ShowType = "Pending", BusinessType = "speakerActive", Count = a.g_count }));
            var spActiveCompleted = countAllList.Where(w => w.type == "SP-Active" && (w.status == (int)Statuses.Rejected || w.status == (int)Statuses.Delete || w.status == (int)Statuses.Passed))
                .GroupBy(a => a.userId).Select(g => new { g_userId = g.Key, g_count = g.Sum(a => a.count) });
            countList.AddRange(spActiveCompleted.Select(a => new ApprovalCountByUserDto() { RelatedUserId = a.g_userId, ShowType = "Completed", BusinessType = "speakerActive", Count = a.g_count }));
            //供应商新建
            var nspCreateWaiting = countAllList.Where(w => w.type == "NSP-Create" && (w.status == (int)Statuses.Returned || w.status == (int)Statuses.Withdraw))
                .GroupBy(a => a.userId).Select(g => new { g_userId = g.Key, g_count = g.Sum(a => a.count) });
            countList.AddRange(nspCreateWaiting.Select(a => new ApprovalCountByUserDto() { RelatedUserId = a.g_userId, ShowType = "Waiting", BusinessType = "hciCreate", Count = a.g_count }));
            var nspCreatePending = countAllList.Where(w => w.type == "NSP-Create" && w.status == (int)Statuses.Approving)
                .GroupBy(a => a.userId).Select(g => new { g_userId = g.Key, g_count = g.Sum(a => a.count) });
            countList.AddRange(nspCreatePending.Select(a => new ApprovalCountByUserDto() { RelatedUserId = a.g_userId, ShowType = "Pending", BusinessType = "hciCreate", Count = a.g_count }));
            var nspCreateCompleted = countAllList.Where(w => w.type == "NSP-Create" && (w.status == (int)Statuses.Rejected || w.status == (int)Statuses.Delete || w.status == (int)Statuses.Passed))
                .GroupBy(a => a.userId).Select(g => new { g_userId = g.Key, g_count = g.Sum(a => a.count) });
            countList.AddRange(nspCreateCompleted.Select(a => new ApprovalCountByUserDto() { RelatedUserId = a.g_userId, ShowType = "Completed", BusinessType = "hciCreate", Count = a.g_count }));
            //供应商变更
            var nspUpdateWaiting = countAllList.Where(w => w.type == "NSP-Update" && (w.status == (int)Statuses.Returned || w.status == (int)Statuses.Withdraw))
                .GroupBy(a => a.userId).Select(g => new { g_userId = g.Key, g_count = g.Sum(a => a.count) });
            countList.AddRange(nspCreateWaiting.Select(a => new ApprovalCountByUserDto() { RelatedUserId = a.g_userId, ShowType = "Waiting", BusinessType = "hciUpdate", Count = a.g_count }));
            var nspUpdatePending = countAllList.Where(w => w.type == "NSP-Update" && w.status == (int)Statuses.Approving)
                .GroupBy(a => a.userId).Select(g => new { g_userId = g.Key, g_count = g.Sum(a => a.count) });
            countList.AddRange(nspCreateWaiting.Select(a => new ApprovalCountByUserDto() { RelatedUserId = a.g_userId, ShowType = "Pending", BusinessType = "hciUpdate", Count = a.g_count }));
            var nspUpdateCompleted = countAllList.Where(w => w.type == "NSP-Update" && (w.status == (int)Statuses.Rejected || w.status == (int)Statuses.Delete || w.status == (int)Statuses.Passed))
                .GroupBy(a => a.userId).Select(g => new { g_userId = g.Key, g_count = g.Sum(a => a.count) });
            countList.AddRange(nspCreateWaiting.Select(a => new ApprovalCountByUserDto() { RelatedUserId = a.g_userId, ShowType = "Completed", BusinessType = "hciUpdate", Count = a.g_count }));
            //供应商激活
            var nspActiveWaiting = countAllList.Where(w => w.type == "NSP-Active" && (w.status == (int)Statuses.Returned || w.status == (int)Statuses.Withdraw))
                .GroupBy(a => a.userId).Select(g => new { g_userId = g.Key, g_count = g.Sum(a => a.count) });
            countList.AddRange(nspCreateWaiting.Select(a => new ApprovalCountByUserDto() { RelatedUserId = a.g_userId, ShowType = "Waiting", BusinessType = "hciActive", Count = a.g_count }));
            var nspActivePending = countAllList.Where(w => w.type == "NSP-Active" && w.status == (int)Statuses.Approving)
                .GroupBy(a => a.userId).Select(g => new { g_userId = g.Key, g_count = g.Sum(a => a.count) });
            countList.AddRange(nspCreateWaiting.Select(a => new ApprovalCountByUserDto() { RelatedUserId = a.g_userId, ShowType = "Pending", BusinessType = "hciActive", Count = a.g_count }));
            var nspActiveCompleted = countAllList.Where(w => w.type == "NSP-Active" && (w.status == (int)Statuses.Rejected || w.status == (int)Statuses.Delete || w.status == (int)Statuses.Passed))
                .GroupBy(a => a.userId).Select(g => new { g_userId = g.Key, g_count = g.Sum(a => a.count) });
            countList.AddRange(nspCreateWaiting.Select(a => new ApprovalCountByUserDto() { RelatedUserId = a.g_userId, ShowType = "Completed", BusinessType = "hciActive", Count = a.g_count }));
            //采购申请
            var prWaiting = countAllList.Where(w => w.type == "PR-Create" && (w.status == (int)PurPRApplicationStatus.Rejected ||
                w.status == (int)PurPRApplicationStatus.RejectedBack || w.status == (int)PurPRApplicationStatus.VendorConfirmed))
                .GroupBy(a => a.userId).Select(g => new { g_userId = g.Key, g_count = g.Sum(a => a.count) });
            var idAndUserIdList = queryablePR.Where(w => userIds.Contains(w.ApplyUserId) && w.Status == PurPRApplicationStatus.Approved)
                .Select(s => new { s.ApplyUserId, s.Id }).ToList();//同一ApplyUserId可能有多行，因为可能有多条PR
            var queryablePRdetail = await LazyServiceProvider.GetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var dicUserIdPrWaiting = prWaiting.ToDictionary(a => a.g_userId, a => a.g_count);
            if (idAndUserIdList?.Any() == true)
            {
                var prAppIds = idAndUserIdList.Select(a => a.Id);
                var prDetails = queryablePRdetail.Where(a => prAppIds.Contains(a.PRApplicationId)).ToArray();
                //获取对冲的行
                var hedgeDetails = prDetails.Where(a => a.HedgePrDetailId.HasValue).ToArray();
                //除对冲行外的明细行
                var expectHedgeDetails = prDetails.Where(a => !hedgeDetails.Any(a1 => a1.HedgePrDetailId == a.Id)).ToArray();
                //判断是否非已推送
                var prCount = expectHedgeDetails.Where(a => (a.PayMethod == PayMethods.AP && a.PushFlag != PushFlagEnum.Pushed) || (a.PayMethod == PayMethods.AR && a.IsVendorConfimed != true))
                    .GroupBy(a => a.PRApplicationId).Select(g => new { g_prId = g.Key, g_count = g.Count() });
                //根据UserId将Count加到prWaiting
                if (prCount?.Any() == true)
                {
                    foreach (var item in prCount)
                    {
                        var curUserId = idAndUserIdList.FirstOrDefault(b => b.Id == item.g_prId)?.ApplyUserId;
                        if (curUserId.HasValue)
                        {
                            if (dicUserIdPrWaiting.ContainsKey(curUserId.Value))
                            {
                                dicUserIdPrWaiting[curUserId.Value] += item.g_count;
                            }
                            else
                            {
                                dicUserIdPrWaiting.Add(curUserId.Value, item.g_count);
                            }
                        }
                    }
                }
            }


            countList.AddRange(dicUserIdPrWaiting.Select(a => new ApprovalCountByUserDto() { RelatedUserId = a.Key, ShowType = "Waiting", BusinessType = "prCreate", Count = a.Value }));
            var prPending = countAllList.Where(w => w.type == "PR-Create" &&
            (w.status == (int)PurPRApplicationStatus.Approving || w.status == (int)PurPRApplicationStatus.WaitForClose))
                .GroupBy(a => a.userId).Select(g => new { g_userId = g.Key, g_count = g.Sum(a => a.count) });
            countList.AddRange(prPending.Select(a => new ApprovalCountByUserDto() { RelatedUserId = a.g_userId, ShowType = "Pending", BusinessType = "prCreate", Count = a.g_count }));
            var prCompleted = countAllList.Where(w => w.type == "PR-Create" &&
            (w.status == (int)PurPRApplicationStatus.ApplicantTerminate || w.status == (int)PurPRApplicationStatus.Closed))
                .GroupBy(a => a.userId).Select(g => new { g_userId = g.Key, g_count = g.Sum(a => a.count) });
            countList.AddRange(prCompleted.Select(a => new ApprovalCountByUserDto() { RelatedUserId = a.g_userId, ShowType = "Completed", BusinessType = "prCreate", Count = a.g_count }));
            //供应商比价
            var bdWaiting = countAllList.Where(w => w.type == "BD-Create" &&
            (w.status == (int)PurBDStatus.Return || w.status == (int)PurBDStatus.Recall))
                .GroupBy(a => a.userId).Select(g => new { g_userId = g.Key, g_count = g.Sum(a => a.count) });
            countList.AddRange(bdWaiting.Select(a => new ApprovalCountByUserDto() { RelatedUserId = a.g_userId, ShowType = "Waiting", BusinessType = "bdCreate", Count = a.g_count }));
            var bdPending = countAllList.Where(w => w.type == "BD-Create" && w.status == (int)PurBDStatus.Approving)
                .GroupBy(a => a.userId).Select(g => new { g_userId = g.Key, g_count = g.Sum(a => a.count) });
            countList.AddRange(bdPending.Select(a => new ApprovalCountByUserDto() { RelatedUserId = a.g_userId, ShowType = "Pending", BusinessType = "bdCreate", Count = a.g_count }));
            var bdCompleted = countAllList.Where(w => w.type == "BD-Create" && (w.status == (int)PurBDStatus.Rejected || w.status == (int)PurBDStatus.Approved || w.status == (int)PurBDStatus.Invalid))
                .GroupBy(a => a.userId).Select(g => new { g_userId = g.Key, g_count = g.Sum(a => a.count) });
            countList.AddRange(bdCompleted.Select(a => new ApprovalCountByUserDto() { RelatedUserId = a.g_userId, ShowType = "Completed", BusinessType = "bdCreate", Count = a.g_count }));
            //采购订单
            var poWaiting = countAllList.Where(w => w.type == "PO-Create" &&
            (w.status == (int)PurOrderStatus.Withdraw || w.status == (int)PurOrderStatus.Return ||
            w.status == (int)PurOrderStatus.InitiateReceipt || w.status == (int)PurOrderStatus.ApplierConfirmed))
                .GroupBy(a => a.userId).Select(g => new { g_userId = g.Key, g_count = g.Sum(a => a.count) });
            countList.AddRange(poWaiting.Select(a => new ApprovalCountByUserDto() { RelatedUserId = a.g_userId, ShowType = "Waiting", BusinessType = "poCreate", Count = a.g_count }));
            var poPending = countAllList.Where(w => w.type == "PO-Create" && w.status == (int)PurOrderStatus.Approving)
                .GroupBy(a => a.userId).Select(g => new { g_userId = g.Key, g_count = g.Sum(a => a.count) });
            countList.AddRange(poPending.Select(a => new ApprovalCountByUserDto() { RelatedUserId = a.g_userId, ShowType = "Pending", BusinessType = "poCreate", Count = a.g_count }));
            var poCompleted = countAllList.Where(w => w.type == "PO-Create" && (w.status == (int)PurOrderStatus.Rejected || w.status == (int)PurOrderStatus.Approved || w.status == (int)PurOrderStatus.Closed))
                .GroupBy(a => a.userId).Select(g => new { g_userId = g.Key, g_count = g.Sum(a => a.count) });
            countList.AddRange(poCompleted.Select(a => new ApprovalCountByUserDto() { RelatedUserId = a.g_userId, ShowType = "Completed", BusinessType = "poCreate", Count = a.g_count }));
            //收货申请
            var grWaiting = countAllList.Where(w => w.type == "GR-Create" &&
            (w.status == (int)PurGRApplicationStatus.Returned || w.status == (int)PurGRApplicationStatus.ToBeReceived))
                .GroupBy(a => a.userId).Select(g => new { g_userId = g.Key, g_count = g.Sum(a => a.count) });
            countList.AddRange(grWaiting.Select(a => new ApprovalCountByUserDto() { RelatedUserId = a.g_userId, ShowType = "Waiting", BusinessType = "grCreate", Count = a.g_count }));
            var grPending = countAllList.Where(w => w.type == "GR-Create" && (w.status == (int)PurGRApplicationStatus.PaymentProgress ||
            w.status == (int)PurGRApplicationStatus.SignedBy || w.status == (int)PurGRApplicationStatus.Termination))
                .GroupBy(a => a.userId).Select(g => new { g_userId = g.Key, g_count = g.Sum(a => a.count) });
            countList.AddRange(grPending.Select(a => new ApprovalCountByUserDto() { RelatedUserId = a.g_userId, ShowType = "Pending", BusinessType = "grCreate", Count = a.g_count }));
            var grCompleted = countAllList.Where(w => w.type == "GR-Create" && (w.status == (int)PurGRApplicationStatus.ReceivedGoods
            || w.status == (int)PurGRApplicationStatus.Terminationed))
                .GroupBy(a => a.userId).Select(g => new { g_userId = g.Key, g_count = g.Sum(a => a.count) });
            countList.AddRange(grCompleted.Select(a => new ApprovalCountByUserDto() { RelatedUserId = a.g_userId, ShowType = "Completed", BusinessType = "grCreate", Count = a.g_count }));
            //付款申请
            var paWaiting = countAllList.Where(w => w.type == "PA-Create" &&
            (w.status == (int)PurPAApplicationStatus.FillIn || w.status == (int)PurPAApplicationStatus.Reissue))
                .GroupBy(a => a.userId).Select(g => new { g_userId = g.Key, g_count = g.Sum(a => a.count) });
            countList.AddRange(paWaiting.Select(a => new ApprovalCountByUserDto() { RelatedUserId = a.g_userId, ShowType = "Waiting", BusinessType = "paCreate", Count = a.g_count }));
            var paPending = countAllList.Where(w => w.type == "PA-Create" &&
            (w.status == (int)PurPAApplicationStatus.Approvaling || w.status == (int)PurPAApplicationStatus.FinancialPreliminaryReview
            || w.status == (int)PurPAApplicationStatus.DocumentReceipt || w.status == (int)PurPAApplicationStatus.WaitingForPayment
            || w.status == (int)PurPAApplicationStatus.FinancialReview))
                .GroupBy(a => a.userId).Select(g => new { g_userId = g.Key, g_count = g.Sum(a => a.count) });
            countList.AddRange(paPending.Select(a => new ApprovalCountByUserDto() { RelatedUserId = a.g_userId, ShowType = "Pending", BusinessType = "paCreate", Count = a.g_count }));
            var paCompleted = countAllList.Where(w => w.type == "PA-Create" &&
            (w.status == (int)PurPAApplicationStatus.Paid || w.status == (int)PurPAApplicationStatus.PaymenFailed
            || w.status == (int)PurPAApplicationStatus.Void))
                .GroupBy(a => a.userId).Select(g => new { g_userId = g.Key, g_count = g.Sum(a => a.count) });
            countList.AddRange(paCompleted.Select(a => new ApprovalCountByUserDto() { RelatedUserId = a.g_userId, ShowType = "Completed", BusinessType = "paCreate", Count = a.g_count }));

            return MessageResult<List<ApprovalCountByUserDto>>.SuccessResult(countList);
        }

        /// <summary>
        /// 我审批的审批任务数量统计(ABP端)
        /// </summary>
        /// <returns></returns>
        public async Task<MessageResult> GetApprovalCountABPAsync()
        {
            var countList = await GetApproverCountNew();
            //var countList = await GetApproverCountOld();
            return MessageResult.SuccessResult(countList);
        }

        async Task<List<ApprovalCountDto>> GetApproverCountOld()
        {
            List<ApprovalCountDto> countList = [];
            var queryableVA = await LazyServiceProvider.LazyGetService<IVendorApplicationRepository>().GetQueryableAsync();

            //查询task
            var queryTask = new QueryExpression("spk_workflowtask");
            queryTask.ColumnSet.AddColumns("spk_businessformid", "spk_name", "spk_approvalstatus", "spk_remark", "spk_workflowstep", "createdon", "spk_approver", "spk_workflowinstance");
            queryTask.Criteria.AddCondition(new ConditionExpression("spk_approver", ConditionOperator.Equal, CurrentUser.Id.ToString()));
            queryTask.Criteria.AddCondition(new ConditionExpression("spk_approvalstatus", ConditionOperator.In, (int)ApprovalPowerAppStatus.PendingForApproval));
            queryTask.AddOrder("createdon", OrderType.Descending);
            //查询flow instance
            var queryInstance = queryTask.AddLink("spk_workflowinstance", "spk_workflowinstance", "spk_workflowinstanceid");
            queryInstance.Columns.AddColumns("spk_workflowyype");
            queryInstance.EntityAlias = "flowInstance";
            //获取workflowType
            var queryType = queryInstance.AddLink("spk_workflowtype", "spk_workflowyype", "spk_workflowtypeid");
            queryType.Columns.AddColumns("spk_workflowtypeid", "spk_name");
            //获取workflowStep
            var queryStep = queryTask.AddLink("spk_workflowstep", "spk_workflowstep", "spk_workflowstepid");
            queryStep.EntityAlias = "step";
            queryStep.Columns.AddColumns("spk_workflowstepid", "spk_name", "spk_step");
            queryTask.PageInfo.PageNumber = 1;
            queryTask.PageInfo.Count = 5000;
            queryTask.PageInfo.ReturnTotalRecordCount = true;
            var entityCollection = await _dataverseRepository.DataverseClient.RetrieveMultipleAsync(queryTask);

            var datas = entityCollection.Entities.Select(p =>
            Guid.Parse(p.GetAttributeValue<string>("spk_businessformid"))
            ).ToList();

            //查询记录表中对应的业务数据及类型
            var taskEntities = await _taskRepository.GetQueryableAsync();
            var taskList = taskEntities.Where(w => datas.Contains(w.FormId) && w.FormName != null)
                .Select(s => new { s.FormName, s.FormId }).Distinct().GroupBy(g => g.FormName)
                .Select(s => new { FormName = s.Key, count = s.Count() }).ToList();

            //供应商单独处理
            var vendor = taskEntities.Where(w => datas.Contains(w.FormId) && w.FormName == NameConsts.VendorApplication)
                .Join(queryableVA, a => a.FormId, b => b.Id, (a, b) => new { vendorQuery = b }).Distinct().ToList();
            if (vendor.Count != 0)
            {
                countList.Add(new ApprovalCountDto() { ShowType = "Waiting", BusinessType = "speakerCreate", Count = vendor.Where(w => w.vendorQuery.VendorType == VendorTypes.HCPPerson && w.vendorQuery.ApplicationType == ApplicationTypes.Create).Count() });
                countList.Add(new ApprovalCountDto() { ShowType = "Waiting", BusinessType = "speakerUpdate", Count = vendor.Where(w => w.vendorQuery.VendorType == VendorTypes.HCPPerson && w.vendorQuery.ApplicationType == ApplicationTypes.Update).Count() });
                countList.Add(new ApprovalCountDto() { ShowType = "Waiting", BusinessType = "speakerActive", Count = vendor.Where(w => w.vendorQuery.VendorType == VendorTypes.HCPPerson && w.vendorQuery.ApplicationType == ApplicationTypes.Active).Count() });
                countList.Add(new ApprovalCountDto() { ShowType = "Waiting", BusinessType = "hciCreate", Count = vendor.Where(w => w.vendorQuery.VendorType != VendorTypes.HCPPerson && w.vendorQuery.ApplicationType == ApplicationTypes.Create).Count() });
                countList.Add(new ApprovalCountDto() { ShowType = "Waiting", BusinessType = "hciUpdate", Count = vendor.Where(w => w.vendorQuery.VendorType != VendorTypes.HCPPerson && w.vendorQuery.ApplicationType == ApplicationTypes.Update).Count() });
                countList.Add(new ApprovalCountDto() { ShowType = "Waiting", BusinessType = "hciActive", Count = vendor.Where(w => w.vendorQuery.VendorType != VendorTypes.HCPPerson && w.vendorQuery.ApplicationType == ApplicationTypes.Active).Count() });
            }

            //其他类型数据
            foreach (var item in taskList)
            {
                if (item.FormName == NameConsts.PRApplication)
                {
                    countList.Add(new ApprovalCountDto() { ShowType = "Waiting", BusinessType = "prCreate", Count = item.count });
                }
                else if (item.FormName == NameConsts.BDApplication)
                {
                    countList.Add(new ApprovalCountDto() { ShowType = "Waiting", BusinessType = "bdCreate", Count = item.count });
                }
                else if (item.FormName == NameConsts.POApplication)
                {
                    countList.Add(new ApprovalCountDto() { ShowType = "Waiting", BusinessType = "poCreate", Count = item.count });
                }
                else if (item.FormName == NameConsts.GRApplication)
                {
                    countList.Add(new ApprovalCountDto() { ShowType = "Waiting", BusinessType = "grCreate", Count = item.count });
                }
                else if (item.FormName == NameConsts.PAApplication)
                {
                    countList.Add(new ApprovalCountDto() { ShowType = "Waiting", BusinessType = "paCreate", Count = item.count });
                }
                else if (item.FormName == NameConsts.OECSpeakerAuthApply)
                {
                    countList.Add(new ApprovalCountDto() { ShowType = "Waiting", BusinessType = "saCreate", Count = item.count });
                }
                else if (item.FormName == NameConsts.PurExemptWaiver)
                {
                    countList.Add(new ApprovalCountDto() { ShowType = "Waiting", BusinessType = "bwCreate", Count = item.count });
                }
                else if (item.FormName == NameConsts.PurExemptJustification)
                {
                    countList.Add(new ApprovalCountDto() { ShowType = "Waiting", BusinessType = "juCreate", Count = item.count });
                }
            }

            return countList;
        }

        async Task<List<ApprovalCountDto>> GetApproverCountNew()
        {
            List<ApprovalCountDto> counts = [];
            var queryVdAp = await LazyServiceProvider.LazyGetService<IVendorApplicationRepository>().GetQueryableAsync();
            var queryableRe = await LazyServiceProvider.LazyGetService<IReturnApplicationReponsitory>().GetQueryableAsync();

            //查询task
            var queryTask = new QueryExpression("spk_workflowtask");
            queryTask.ColumnSet.AddColumns("spk_businessformid", "spk_businessformno");
            queryTask.Criteria.AddCondition(new ConditionExpression("spk_approver", ConditionOperator.Equal, CurrentUser.Id.ToString()));
            queryTask.Criteria.AddCondition(new ConditionExpression("spk_approvalstatus", ConditionOperator.Equal, (int)ApprovalPowerAppStatus.PendingForApproval));
            /*注释调
             * var instans = queryTask.AddLink("spk_workflowinstance", "spk_workflowinstance", "spk_workflowinstanceid");
            instans.Columns.AddColumn("spk_workflowyype");
            instans.EntityAlias = "alias";
            var type = instans.AddLink("spk_workflowtype", "spk_workflowyype", "spk_workflowtypeid");
            type.Columns.AddColumn("spk_workflowtypename");
            type.EntityAlias = "alias2";*/
            queryTask.TopCount = 1000;
            var entityCollection = await _dataverseRepository.DataverseClient.RetrieveMultipleAsync(queryTask);
            //当前用户 所有待审批的任务 相关的所有表单Id
            var myPendings = entityCollection.Entities.Select(p => new
            {
                formId = Guid.Parse(p.GetAttributeValue<string>("spk_businessformid")),
                formNo = p.GetAttributeValue<string>("spk_businessformno"),
            }).Distinct().ToList();

            var myVdIds = myPendings.Where(x => x.formNo.StartsWith('V')).Select(x => x.formId);
            //退换货
            var myTIds = myPendings.Where(x => x.formNo.StartsWith('T')).Select(x => x.formId);

            var st = "Waiting";
            //供应商单独处理
            if (myVdIds.Any())
            {
                var vdAps = queryVdAp.Where(x => myVdIds.Contains(x.Id)).Select(x => new { x.VendorType, x.ApplicationType });
                if (vdAps.Any())
                {
                    counts.Add(new ApprovalCountDto() { ShowType = st, BusinessType = "speaker", Count = vdAps.Where(w => w.VendorType == VendorTypes.HCPPerson).Count() });
                    counts.Add(new ApprovalCountDto() { ShowType = st, BusinessType = "nonSpeaker", Count = vdAps.Where(w => w.VendorType != VendorTypes.HCPPerson).Count() });
                }
            }
            if (myTIds.Any())
            {
                var t = await queryableRe.Where(m => myTIds.Contains(m.Id)).Select(m => m.ApplicationType).GroupBy(g => g, (key, v) => new { key, Count = v.Count() }).ToListAsync();
                counts.Add(new ApprovalCountDto() { ShowType = st, BusinessType = "reCreate", Count = t.FirstOrDefault(w => w.key == ReturnTypeEnum.Return)?.Count ?? 0 });
                counts.Add(new ApprovalCountDto() { ShowType = st, BusinessType = "exCreate", Count = t.FirstOrDefault(w => w.key == ReturnTypeEnum.Exchange)?.Count ?? 0 });
            }
            //其他类型数据
            var formTypes = new Dictionary<string, string> {
                    { "P", "prCreate" },
                    { "O", "poCreate" },
                    { "B", "bdCreate" },
                    { "G", "grCreate" },
                    { "A", "paCreate" },
                    { "W", "bwCreate" },
                    { "SA", "saCreate" },
                    { "J", "juCreate" },
                    { "F", "focCreate" },
                    { "S", "stCreate" },
                };

            formTypes.ToList().ForEach(a =>
            {
                if (a.Key == "S")
                {
                    var formsPerType = myPendings.Where(x => x.formNo.StartsWith(a.Key) && !x.formNo.StartsWith("SA"));
                    counts.Add(new ApprovalCountDto() { ShowType = st, BusinessType = a.Value, Count = formsPerType.Count() });
                }
                {
                    var formsPerType = myPendings.Where(x => x.formNo.StartsWith(a.Key));
                    counts.Add(new ApprovalCountDto() { ShowType = st, BusinessType = a.Value, Count = formsPerType.Count() });
                }
            });
            return counts;
        }

        /// <summary>
        /// 批量查询 userIds 审批的审批任务数量统计(ABP端)
        /// </summary>
        /// <returns></returns>
        public async Task<MessageResult<List<ApprovalCountByUserDto>>> GetApprovalCountABPAsync(List<Guid> userIds)
        {
            List<ApprovalCountByUserDto> countList = [];
            if (userIds?.Any() != true)
            {
                return MessageResult<List<ApprovalCountByUserDto>>.SuccessResult(countList);
            }

            var queryableVA = await LazyServiceProvider.LazyGetService<IVendorApplicationRepository>().GetQueryableAsync();

            //查询task
            var queryTask = new QueryExpression("spk_workflowtask");
            queryTask.ColumnSet.AddColumns("spk_businessformid", "spk_name", "spk_approvalstatus", "spk_remark", "spk_workflowstep", "createdon", "spk_approver", "spk_workflowinstance");
            queryTask.Criteria.AddCondition(new ConditionExpression("spk_approver", ConditionOperator.In, userIds.Select(s => s.ToString()).ToList()));
            queryTask.Criteria.AddCondition(new ConditionExpression("spk_approvalstatus", ConditionOperator.In, (int)ApprovalPowerAppStatus.PendingForApproval));
            queryTask.AddOrder("createdon", OrderType.Descending);
            //查询flow instance
            var queryInstance = queryTask.AddLink("spk_workflowinstance", "spk_workflowinstance", "spk_workflowinstanceid");
            queryInstance.Columns.AddColumns("spk_workflowyype");
            queryInstance.EntityAlias = "flowInstance";
            //获取workflowType
            var queryType = queryInstance.AddLink("spk_workflowtype", "spk_workflowyype", "spk_workflowtypeid");
            queryType.Columns.AddColumns("spk_workflowtypeid", "spk_name");
            //获取workflowStep
            var queryStep = queryTask.AddLink("spk_workflowstep", "spk_workflowstep", "spk_workflowstepid");
            queryStep.EntityAlias = "step";
            queryStep.Columns.AddColumns("spk_workflowstepid", "spk_name", "spk_step");
            queryTask.PageInfo.PageNumber = 1;
            queryTask.PageInfo.Count = 5000;
            queryTask.PageInfo.ReturnTotalRecordCount = true;
            var entityCollection = await _dataverseRepository.DataverseClient.RetrieveMultipleAsync(queryTask);

            var datas = entityCollection.Entities.Select(p => new
            {
                formId = Guid.Parse(p.GetAttributeValue<string>("spk_businessformid")),
                approverId = p.GetAttributeValue<EntityReference>("spk_approver").Id,
            }
            ).ToList();
            if (datas?.Any() != true)
            {
                return MessageResult<List<ApprovalCountByUserDto>>.SuccessResult(countList);
            }

            //查询记录表中对应的业务数据及类型
            var taskEntities = await _taskRepository.GetQueryableAsync();
            //先按formId查出来，然后再安放UserId
            var taskQueryList = taskEntities.Where(w => datas.Select(a => a.formId).Contains(w.FormId) && w.FormName != null)
                .Select(s => new { s.FormName, s.FormId }).Distinct().ToList();
            //安放UserId
            var taskList = taskQueryList.Join(datas, left => left.FormId, right => right.formId, (left, right) => new { right.approverId, left.FormName, left.FormId })
                .GroupBy(g => new { g.approverId, g.FormName })
                .Select(s => new { s.Key.approverId, s.Key.FormName, count = s.Count() })
                .ToList();

            //供应商单独处理
            var vendorQuery = taskEntities.Where(w => datas.Select(a => a.formId).Contains(w.FormId) && w.FormName == NameConsts.VendorApplication)
                .Join(queryableVA, a => a.FormId, b => b.Id, (a, b) => new { b.Id, b.VendorType, b.ApplicationType }).Distinct().ToList();
            var vendor = vendorQuery.Join(datas, left => left.Id, right => right.formId, (left, right) => new { right.approverId, vndAppId = left.Id, left.VendorType, left.ApplicationType })
                .Distinct().ToList();
            if (vendor?.Any() == true)
            {
                var Dtos = vendor.GroupBy(g => new { g.approverId, g.VendorType, g.ApplicationType })
                    .Select(a => new { a.Key, g_count = a.Count() })
                    .Select(a => new ApprovalCountByUserDto
                    {
                        RelatedUserId = a.Key.approverId,
                        ShowType = "Waiting",
                        BusinessType =
                            a.Key.VendorType == VendorTypes.HCPPerson ? "speaker" :
                            a.Key.VendorType != VendorTypes.HCPPerson ? "nonSpeaker" :
                            null,
                        Count = a.g_count,
                    })
                    .Where(a => !string.IsNullOrEmpty(a.BusinessType));
                countList.AddRange(Dtos);
            }

            //其他类型数据
            foreach (var item in taskList)
            {
                if (item.FormName == NameConsts.PRApplication)
                {
                    countList.Add(new ApprovalCountByUserDto() { RelatedUserId = item.approverId, ShowType = "Waiting", BusinessType = "prCreate", Count = item.count });
                }
                else if (item.FormName == NameConsts.BDApplication)
                {
                    countList.Add(new ApprovalCountByUserDto() { RelatedUserId = item.approverId, ShowType = "Waiting", BusinessType = "bdCreate", Count = item.count });
                }
                else if (item.FormName == NameConsts.POApplication)
                {
                    countList.Add(new ApprovalCountByUserDto() { RelatedUserId = item.approverId, ShowType = "Waiting", BusinessType = "poCreate", Count = item.count });
                }
                else if (item.FormName == NameConsts.GRApplication)
                {
                    countList.Add(new ApprovalCountByUserDto() { RelatedUserId = item.approverId, ShowType = "Waiting", BusinessType = "grCreate", Count = item.count });
                }
                else if (item.FormName == NameConsts.PAApplication)
                {
                    countList.Add(new ApprovalCountByUserDto() { RelatedUserId = item.approverId, ShowType = "Waiting", BusinessType = "paCreate", Count = item.count });
                }
            }
            return MessageResult<List<ApprovalCountByUserDto>>.SuccessResult(countList);
        }

        /// <summary>
        /// 当前用户待处理的任务(我发起的和我审批的)数量
        /// </summary>
        /// <returns></returns>
        public async Task<MessageResult> GetPendingCountAsync()
        {
            var tempCount = new PendingCountDto()
            {
                InitiatialCount = 0,
                ApprovalCount = 0
            };
            return MessageResult.SuccessResult(tempCount);

            var queryableVA = await LazyServiceProvider.LazyGetService<IVendorApplicationRepository>().GetQueryableAsync();
            var queryablePR = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var queryableBD = await LazyServiceProvider.LazyGetService<IPurBDApplicationRepository>().GetQueryableAsync();
            var queryablePO = await LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>().GetQueryableAsync();
            var queryableGR = await LazyServiceProvider.LazyGetService<IPurGRApplicationRepository>().GetQueryableAsync();
            var queryablePA = await LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>().GetQueryableAsync();

            //获取代理信息
            var agents = await LazyServiceProvider.LazyGetService<IAgencyService>().GetOriginalOperators(new GetAgentOperatorsRequestDto());
            var userIds = agents.Select(a => a.Key).Distinct().ToArray();

            var countAllList = queryableVA.Where(w => userIds.Contains(w.ApplyUserId) || (w.TransfereeId.HasValue && userIds.Contains(w.TransfereeId.Value)))
                .GroupBy(g => g.Status).Select(s => new { type = "SP-NSP", status = (int)s.Key, count = s.Count() })
                .Union(queryablePR.Where(w => userIds.Contains(w.ApplyUserId) || (w.TransfereeId.HasValue && userIds.Contains(w.TransfereeId.Value)))
                .GroupBy(g => g.Status).Select(s => new { type = "PR-Create", status = (int)s.Key, count = s.Count() }))
                .Union(queryableBD.Where(w => userIds.Contains(w.ApplyUserId) || (w.TransfereeId.HasValue && userIds.Contains(w.TransfereeId.Value)))
                .GroupBy(g => g.Status).Select(s => new { type = "BD-Create", status = (int)s.Key, count = s.Count() }))
                .Union(queryablePO.Where(w => userIds.Contains(w.ApplyUserId) || (w.TransfereeId.HasValue && userIds.Contains(w.TransfereeId.Value)))
                .GroupBy(g => g.Status).Select(s => new { type = "PO-Create", status = (int)s.Key, count = s.Count() }))
                .Union(queryableGR.Where(w => userIds.Contains(w.ApplyUserId) || (w.TransfereeId.HasValue && userIds.Contains(w.TransfereeId.Value)))
                .GroupBy(g => g.Status).Select(s => new { type = "GR-Create", status = (int)s.Key, count = s.Count() }))
                .Union(queryablePA.Where(w => userIds.Contains(w.ApplyUserId) || (w.TransfereeId.HasValue && userIds.Contains(w.TransfereeId.Value)))
                .GroupBy(g => g.Status).Select(s => new { type = "PA-Create", status = (int)s.Key, count = s.Count() }))
                .ToList();

            //讲者,供应商
            var spAndNspWaiting = countAllList.Where(w => w.type == "SP-NSP" &&
            (w.status == (int)Statuses.Returned || w.status == (int)Statuses.Withdraw)).Sum(s => s.count);
            //采购申请
            var prWaiting = countAllList.Where(w => w.type == "PR-Create" && (w.status == (int)PurPRApplicationStatus.Rejected ||
            w.status == (int)PurPRApplicationStatus.RejectedBack || w.status == (int)PurPRApplicationStatus.VendorConfirmed)).Sum(s => s.count);
            var idList = queryablePR.Where(w => w.ApplyUserId == CurrentUser.Id && w.Status == PurPRApplicationStatus.Approved).Select(s => s.Id);
            var queryablePRdetail = await LazyServiceProvider.GetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            foreach (var id in idList)
            {
                var prDetails = queryablePRdetail.Where(a => a.PRApplicationId == id).ToArray();
                //获取对冲的行
                var hedgeDetails = prDetails.Where(a => a.HedgePrDetailId.HasValue).ToArray();
                //除对冲行外的明细行
                var expectHedgeDetails = prDetails.Where(a => !hedgeDetails.Any(a1 => a1.HedgePrDetailId == a.Id)).ToArray();
                //判断是否非已推送
                var prCount = expectHedgeDetails.Where(a => (a.PayMethod == PayMethods.AP && a.PushFlag != PushFlagEnum.Pushed) || (a.PayMethod == PayMethods.AR && a.IsVendorConfimed != true));
                if (prCount != null && prCount.Count() > 0)
                    prWaiting++;
            }
            //供应商比价
            var bdWaiting = countAllList.Where(w => w.type == "BD-Create" &&
            (w.status == (int)PurBDStatus.Return || w.status == (int)PurBDStatus.Recall)).Sum(s => s.count);
            //采购订单
            var poWaiting = countAllList.Where(w => w.type == "PO-Create" &&
            (w.status == (int)PurOrderStatus.Withdraw || w.status == (int)PurOrderStatus.Return ||
            w.status == (int)PurOrderStatus.InitiateReceipt || w.status == (int)PurOrderStatus.ApplierConfirmed)).Sum(s => s.count);
            //收货申请
            var grWaiting = countAllList.Where(w => w.type == "GR-Create" &&
            (w.status == (int)PurGRApplicationStatus.Returned || w.status == (int)PurGRApplicationStatus.ToBeReceived)).Sum(s => s.count);
            //付款申请
            var paWaiting = countAllList.Where(w => w.type == "PA-Create" &&
            (w.status == (int)PurPAApplicationStatus.FillIn || w.status == (int)PurPAApplicationStatus.Reissue)).Sum(s => s.count);

            //当前用户发起的任务总数
            var initiatialCount = spAndNspWaiting + prWaiting + bdWaiting + poWaiting + grWaiting + paWaiting;

            //查询task
            var queryTask = new QueryExpression("spk_workflowtask");
            queryTask.ColumnSet.AddColumns("spk_businessformid", "spk_name", "spk_approvalstatus", "spk_remark", "spk_workflowstep", "createdon", "spk_approver", "spk_workflowinstance");
            queryTask.Criteria.AddCondition(new ConditionExpression("spk_approver", ConditionOperator.Equal, CurrentUser.Id.ToString()));
            queryTask.Criteria.AddCondition(new ConditionExpression("spk_approvalstatus", ConditionOperator.In, (int)ApprovalPowerAppStatus.PendingForApproval));
            queryTask.AddOrder("createdon", OrderType.Descending);
            var entityCollection = await _dataverseRepository.DataverseClient.RetrieveMultipleAsync(queryTask);

            //需要当前用户审批的任务总数
            var approvalCount = entityCollection.Entities.Count();

            var counts = new PendingCountDto()
            {
                InitiatialCount = initiatialCount,
                ApprovalCount = approvalCount
            };
            return MessageResult.SuccessResult(counts);
        }
        /// <summary>
        /// 审批记录(根据instance逻辑查询)
        /// </summary>
        /// <returns></returns>
        public async Task<MessageResult> ApprovalRecordAsync(Guid id)
        {
            var _instanceRepository = _serviceProvider.GetService<IRepository<WorkflowInstance, Guid>>();
            var _taskRepository = _serviceProvider.GetService<IRepository<WorkflowTask, Guid>>();
            var _userRepository = _serviceProvider.GetService<IRepository<IdentityUser, Guid>>();

            var instanceQueryable = await _instanceRepository.GetQueryableAsync();
            var taskQueryable = await _taskRepository.GetQueryableAsync();
            var userQueryable = await _userRepository.GetQueryableAsync();

            List<ApprovalRecordDto> response = new();
            List<WorkflowTask> workflowTasks = new();

            var instances = instanceQueryable.Where(p => p.FormId == id).OrderBy(p => p.CreationTime).ToList();

            if (!instances.Any() && instances.Count == 0)
            {
                var _dataverseService = _serviceProvider.GetService<IDataverseService>();
                var instanceDatas = await _dataverseService.GetApplicationRecordAsync(id.ToString());
                var submitData = instanceDatas.ToList();
                if (submitData.Any())
                {
                    var instanceData = ObjectMapper.Map<List<WorkflowInstanceDto>, List<WorkflowInstance>>(submitData);

                    await _instanceRepository.InsertManyAsync(instanceData, true);
                    instances.AddRange(instanceData);

                    foreach (var data in submitData)
                    {
                        var taskData = data.WorkflowTasks.ToList();
                        var tasks = ObjectMapper.Map<List<WorkflowTaskDto>, List<WorkflowTask>>(taskData);
                        workflowTasks.AddRange(tasks);
                    }
                    await _taskRepository.InsertManyAsync(workflowTasks, true);
                }
            }

            for (int i = 0; i < instances.Count; i++)
            {
                if (i == 0)
                {
                    instances[0].Title = "发起申请";
                }
                else
                {
                    instances[i].Title = "重新发起申请";
                }
            }

            var instance = instances.Select(p => new ApprovalRecordDto
            {
                IsApproval = 0,
                WorkFlowName = p.Title,
                CreatorId = p.SubmitId,
                SubmitOrApprovalTime = p.SubmitTime.ToString("yyyy-MM-dd hh:mm:ss")
            });

            response.AddRange(instance);
            IEnumerable<WorkflowTask> taskEnumerable;
            if (workflowTasks.Any())
            {
                taskEnumerable = instances.GroupJoin(workflowTasks, a => a.Id, b => b.InstanceId, (instance, task) => new
                {
                    instance,
                    task
                }).SelectMany(x => x.task.DefaultIfEmpty(), (x, y) => new
                {
                    x,
                    y
                }).Select(p => p.y).AsEnumerable();
            }
            else
            {
                taskEnumerable = instances.GroupJoin(taskQueryable, a => a.Id, b => b.InstanceId, (instance, task) => new
                {
                    instance,
                    task
                }).SelectMany(x => x.task.DefaultIfEmpty(), (x, y) => new
                {
                    x,
                    y
                }).Select(p => p.y).AsEnumerable();
            }

            if (taskEnumerable.Any())
            {
                var tasks = taskEnumerable.Select(p => new ApprovalRecordDto()
                {
                    IsApproval = 1,
                    WorkFlowName = p.WorkStep,
                    CreatorId = p.ApprovalId,
                    Status = p.Status,
                    SubmitOrApprovalTime = p.ApprovalTime.ToString("yyyy-MM-dd hh:mm:ss"),
                    Remark = p.Remark,
                });
                response.AddRange(tasks);
            }

            foreach (var item in response)
            {
                item.UserName = userQueryable.FirstOrDefault(p => p.Id == item.CreatorId)?.Name;
                item.StatusName = EnumUtil.GetDescription(item.Status);
            }

            var res = response.OrderBy(p => p.SubmitOrApprovalTime);

            return MessageResult.SuccessResult(res);
        }
        /// <summary>
        /// 更改po业务状态--撤回，作废
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        private async Task UpdatePOApplicationFormStatus(List<Guid> ids, ApprovalOperation status)
        {
            var POApplication = _serviceProvider.GetService<IPurPOApplicationRepository>();
            var query = await POApplication.GetQueryableAsync();
            var entities = query.Where(x => ids.Contains(x.Id)).ToList();
            if (status == ApprovalOperation.Recall)
            {
                if (entities.Any())
                    entities.ForEach(x => x.Status = PurOrderStatus.Withdraw);
            }
            if (status == ApprovalOperation.Delete)
            {
                if (entities.Any())
                {
                    var msaService = LazyServiceProvider.LazyGetService<ISowService>();
                    foreach (var item in entities)
                    {
                        item.Status = PurOrderStatus.Invalid;
                        //作废时，如果有关联的sow，需要check是否可以删除
                        if (item.SowId.HasValue)
                            await msaService.DeleteSowAsync(new SowDeleteDto { PoId = item.Id, SowId = item.SowId.Value });
                    }
                }
            }
            var res = POApplication.UpdateManyAsync(entities, true);
        }
        /// <summary>
        /// 更改po业务状态--退回，
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="status"></param>
        /// <returns></returns>
        private async Task<MessageResult> UpdatePOApplicationStatus(ApprovalResultsRequestDto request)
        {
            var POApplication = _serviceProvider.GetService<IPurPOApplicationRepository>();
            var query = await POApplication.GetQueryableAsync();
            var PRDApplication = _serviceProvider.GetService<IPurPRApplicationDetailRepository>();
            var queryPRD = await PRDApplication.GetQueryableAsync();
            var entities = await query.FirstAsync(x => request.FormId == x.Id);
            if (entities == null) return MessageResult.FailureResult("未查询到相关数据");
            if (entities.Status != PurOrderStatus.Approving) return MessageResult.FailureResult($"该{request.FormId}单据已经完结，无需重复调用");
            switch (request.ApproveStatus)
            {
                case ApprovalPowerAppStatus.Approved:
                    entities.Status = PurOrderStatus.ApplierConfirmed;
                    entities.ApprovedDate = DateTime.Now;
                    break;
                case ApprovalPowerAppStatus.Rejected:
                    entities.Status = PurOrderStatus.Rejected;

                    break;
                case ApprovalPowerAppStatus.Denied:
                    entities.Status = PurOrderStatus.Return;
                    break;
                default:
                    break;
            }
            //更改po详情状态
            if (ApprovalPowerAppStatus.Rejected == request.ApproveStatus || request.ApproveStatus == ApprovalPowerAppStatus.Approved)
            {
                if (!string.IsNullOrWhiteSpace(entities.PRApplicationDetailId))
                {
                    var PrDetailIds = entities.PRApplicationDetailId.Split(",").Select(x => Guid.Parse(x)).ToList();
                    var prdEntities = queryPRD.Where(m => PrDetailIds.Contains(m.Id)).ToList();
                    prdEntities.ForEach((x) =>
                    {
                        if (ApprovalPowerAppStatus.Rejected == request.ApproveStatus)
                            x.OrderStatusFlag = OrderStatusFlag.OrderReject;
                        else
                            x.OrderStatusFlag = OrderStatusFlag.OrderCompleted;
                    });
                    await PRDApplication.UpdateManyAsync(prdEntities);
                }

                //拒绝时，如果有关联的sow，需要check是否可以删除
                if (ApprovalPowerAppStatus.Rejected == request.ApproveStatus && entities.SowId.HasValue)
                    await LazyServiceProvider.LazyGetService<ISowService>().DeleteSowAsync(new SowDeleteDto { PoId = entities.Id, SowId = entities.SowId.Value });

                if (request.ApproveStatus == ApprovalPowerAppStatus.Approved)
                {
                    #region 审批通知申请人
                    //主采购订单 采购订单申请  申请审批通过 确认订单
                    var userQuery = (await LazyServiceProvider.LazyGetService<IRepository<IdentityUser, Guid>>().GetQueryableAsync()).AsNoTracking();
                    var userIdsToNotify = await GetUsersToNotify(entities.ApplyUserId, entities.TransfereeId, ResignationTransfer.TaskFormCategory.PurhcaseOrderApplication);
                    var users = userQuery.Where(a => userIdsToNotify.ToHashSet().Contains(a.Id)).ToArray();
                    var sendEmaillRecords = users.Select(user => new InsertSendEmaillRecordDto
                    {
                        EmailAddress = user.Email,
                        Subject = "[NexBPM消息中心]您有一个【{WorkflowTypeName}】正在等待您{ProcessType}。",
                        Content = JsonConvert.SerializeObject(new NotificationApplicantEmailDto
                        {
                            WorkflowTypeName = "采购订单申请",
                            ProcessType = NotifyApplicantProcessType.QRDD,
                            UserName = user.Name,
                            ApplicationCode = entities.ApplicationCode,
                            ApplicationLink = $"{_configuration["SpeakerEmail:WebHost"]?.TrimEnd('/')}/task/launch",
                        }),
                        SourceType = EmailSourceType.ApprovalNotifyApplicant,
                        Status = SendStatus.Pending,
                        Attempts = 0
                    });

                    //记录邮件，并触发邮件发送功能
                    var emailService = LazyServiceProvider.LazyGetService<IEmailService>();
                    await emailService.InsertSendEmaillRecordAsync(sendEmaillRecords.ToList());
                    // 调度作业
                    BackgroundJob.Enqueue<SendEmailWorker>(a => a.DoWorkAsync());
                    #endregion
                }
            }
            await POApplication.UpdateAsync(entities, true);

            return MessageResult.SuccessResult();
        }
        #region GR相关私有方法
        private async Task<MessageResult> UpdateGRApplicationStatus(ApprovalResultsRequestDto request)
        {
            var _paApplicationService = LazyServiceProvider.GetService<IPurPAApplicationService>();
            var grApplication = _serviceProvider.GetService<IPurGRApplicationRepository>();
            var grQuery = await grApplication.GetQueryableAsync();
            var paQuery = await LazyServiceProvider.GetService<IPurPAApplicationRepository>().GetQueryableAsync();
            var prQuery = await LazyServiceProvider.GetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var grDetailQuery = await LazyServiceProvider.GetService<IPurGRApplicationDetailRepository>().GetQueryableAsync();
            var prDetailQuery = await LazyServiceProvider.GetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var paInvoiceQuery = await LazyServiceProvider.GetService<IPurPAApplicationDetailRepository>().GetQueryableAsync();
            var poQuery = await LazyServiceProvider.GetService<IPurPOApplicationRepository>().GetQueryableAsync();
            var poDetailsQuery = await LazyServiceProvider.GetService<IPurPOApplicationDetailsRepository>().GetQueryableAsync();
            var gr = grQuery.Where(a => a.Id == request.FormId).FirstOrDefault();
            if (gr == null)
                return MessageResult.FailureResult("未找到对应的GR申请");

            switch (request.ApproveStatus)
            {
                case ApprovalPowerAppStatus.Approved:
                    //1、当前状态是终止审批中，说明本次审批回调为终止终止收货
                    if (PurGRApplicationStatus.Termination.Equals(gr.Status))
                    { //同意则终止收货
                        gr.Status = PurGRApplicationStatus.Terminationed;
                        #region 预算返还
                        //GR(返还): 终止收货时，退回每行[PO不含税金额-已付款不含税金额]
                        var pr = prQuery.Where(a => a.Id == gr.PrId).FirstOrDefault();
                        var grDetails = grDetailQuery.Where(a => a.GRApplicationId == gr.Id).ToList();
                        PurOrderStatus[] poStatus = [PurOrderStatus.Invalid, PurOrderStatus.Rejected];
                        var poDetails = poDetailsQuery.Join(poQuery.Select(a => new { a.Id, a.Status }), a => a.POApplicationId, b => b.Id, (a, b) => new { pod = a, po = b })
                            .Where(a => !poStatus.Contains(a.po.Status))
                            .Where(a => grDetails.Select(x => x.PRDetailId).Contains(a.pod.PRDetailId.Value))
                            .Select(a => a.pod).ToList();
                        var prDetails = prDetailQuery.Where(a => grDetails.Select(b => b.PRDetailId).ToList().Contains(a.Id)).ToList();
                        var pas = paQuery.GroupJoin(paInvoiceQuery, a => a.Id, b => b.PurPAApplicationId, (a, b) => new { pa = a, pads = b })
                            .SelectMany(a => a.pads.DefaultIfEmpty(), (a, b) => new { a.pa, pad = b })
                            .Where(a => a.pa.Status != PurPAApplicationStatus.Void && grDetails.Select(x => x.PRDetailId).ToList().Contains(a.pad.PRDetailId))
                            .ToList();
                        var paInvoices = pas.Select(a => a.pad).ToList();//GR对应的PR 查找的PA明细
                        var useBudgetRequest = new ReturnBudgetRequestDto
                        {
                            PrId = gr.PrId,
                            SubbudgetId = pr.SubBudgetId.Value,
                            Items = prDetails.Select(a =>
                            {
                                //PO不含税金额
                                var poNoTaxAmount = poDetails.Where(o => o.PRDetailId == a.Id).Sum(o => o.TotalAmountNoTax) * (decimal)gr.ExchangeRate;
                                //已付款不含税金额
                                var paNoTaxAmount = paInvoices.Where(x => x.PRDetailId == a.Id).Sum(x =>
                                {
                                    decimal taxRate = 0M;
                                    if (!InvoiceType.GiftIncrease.Equals(x.InvoiceType))
                                    {
                                        decimal.TryParse(x.TaxRate, out taxRate);
                                    }
                                    return x.PaymentAmount / (1 + taxRate); //已付款不含税金额
                                }) * (decimal)gr.ExchangeRate;
                                return new ReturnInfo
                                {
                                    PdRowNo = a.RowNo,
                                    ReturnSourceId = gr.Id,
                                    ReturnSourceCode = gr.ApplicationCode,
                                    ReturnAmount = poNoTaxAmount - paNoTaxAmount
                                };
                            })
                        };
                        if (useBudgetRequest.Items.Any(a => a.ReturnAmount > 0))
                        {
                            useBudgetRequest.Items = useBudgetRequest.Items.Where(a => a.ReturnAmount > 0);//返还未使用完的预算
                            await LazyServiceProvider.LazyGetService<ISubbudgetService>().ReturnSubbudgetAsync(useBudgetRequest);
                        }
                        #endregion
                    }
                    //2、当前状态是加签人加签，说明本次审批回调为收货审批
                    if (PurGRApplicationStatus.SignedBy.Equals(gr.Status))
                    {
                        if (gr.IsZeroSpeaker) //0元讲者审批结束状态直接改为已收货
                        {
                            gr.Status = PurGRApplicationStatus.ReceivedGoods;
                            gr.ReceivedTime = DateTime.Now;
                        }
                        else
                        {
                            //从GR详情模板生成历史记录
                            var grHistoryIds = await CreateGRHistoryAsync(gr.Id);
                            //生成付款申请
                            await _paApplicationService.CreatePurPAApplicationAsync(new CreatePurPAApplicationDto()
                            {
                                GRId = gr.Id,
                                GrDetailHistoryIds = grHistoryIds,
                            });
                            gr.Status = PurGRApplicationStatus.PaymentProgress;
                        }
                    }
                    break;
                case ApprovalPowerAppStatus.Denied:
                    //1、当前状态是终止审批中，说明本次审批回调为终止终止收货
                    if (PurGRApplicationStatus.Termination.Equals(gr.Status))
                    {//退回则继续收货
                        gr.Status = PurGRApplicationStatus.ToBeReceived;
                    }
                    //2、当前状态是加签人加签，说明本次审批回调为收货审批
                    if (PurGRApplicationStatus.SignedBy.Equals(gr.Status))
                    {//退回则继续收货
                        gr.Status = PurGRApplicationStatus.ToBeReceived;
                        //加签人退回 则需要返还多使用的预算
                        if (gr.UseBudgetTime.HasValue)
                        {
                            await LazyServiceProvider.LazyGetService<IPurGRApplicationService>().ReturnSubbudgetAsync(gr.PrId, gr.Id, gr.UseBudgetTime.Value);
                            gr.UseBudgetTime = null; //清除GR提交时使用预算时间，待后续收货使用
                        }
                    }
                    break;
            }
            await grApplication.UpdateAsync(gr, true);
            return MessageResult.SuccessResult();
        }
        #endregion

        /// <summary>
        /// 流程结束回调时，根据不同状态更新PA信息
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        private async Task<MessageResult> UpdatePAApplicationStatus(ApprovalResultsRequestDto request)
        {
            var grApplicationRepository = LazyServiceProvider.GetService<IPurGRApplicationRepository>();
            var paApplicationRepository = LazyServiceProvider.GetService<IPurPAApplicationRepository>();
            var prApplicationRepository = LazyServiceProvider.GetService<IPurPRApplicationRepository>();
            var interceptQuery = await LazyServiceProvider.LazyGetService<IOECInterceptRepository>().GetQueryableAsync();
            var paQuery = await paApplicationRepository.GetQueryableAsync();
            var paDetailQuery = await LazyServiceProvider.GetService<IPurPAApplicationDetailRepository>().GetQueryableAsync();
            var grQuery = await grApplicationRepository.GetQueryableAsync();
            var prQuery = await prApplicationRepository.GetQueryableAsync();
            var pa = paQuery.Where(a => a.Id == request.FormId).FirstOrDefault();
            if (pa == null)
                return MessageResult.FailureResult("未找到对应的PA申请");
            switch (request.ApproveStatus)
            {
                case ApprovalPowerAppStatus.Approved:
                    if (PurPAApplicationStatus.Approvaling.Equals(pa.Status))
                    {
                        //付款申请提交后，状态改为了Approvaling、单前状态为Approvaling 则说明本次审批同意的是提交
                        pa.Status = PurPAApplicationStatus.DocumentReceipt;
                        pa.ManagerApprovalTime = DateTime.Now;
                        #region 打印页面推送OM

                        // 付款申请，经理审批通过
                        var pr = prQuery.FirstOrDefault(x => x.Id.Equals(pa.PRId));
                        if (pr != null && pr.IsEsignUsed == true)
                        {
                            var onOff = await LazyServiceProvider.LazyGetService<ISettingRepository>().FindAsync(SettingsConst.OmBpmInterfaceOnAndOff, "G", null);
                            if (onOff?.Value == "OFF")
                            {
                                var integrationOmAppService = LazyServiceProvider.GetService<IIntegrationOmAppService>();
                                // 打印页面推送
                                await integrationOmAppService.OmAddPrintPAPR(pa.ApplicationCode);
                            }
                            else
                            {
                                //调度作业  老BPM
                                BackgroundJob.Enqueue<BpmOmWorker>(a => a.DoWorkAsync(new BpmRequestDto() { PaNo = pa.ApplicationCode, InterfaceType = Enums.Integration.OmBpmType.Print }));
                            }
                        }
                        #endregion

                        #region 单据拦截
                        //(Abbott NexBPM_Development Plan_0423.xlsx)=>当被拦截的单据尚未生成PA，或对应PA状态为"直接经理审批"，代表单据尚未流转至Hub团队处，一旦直接经理审批完成后即与c的处理方式相同　（拦截中）
                        //PR明细拦截释放时=》当被拦截的单据对应PA未生成或状态为"直接经理审批"时，该单据一经审批完成后即走正常的后续审批及付款流程（只要还存在拦截中的PR明细，PA都还是拦截中）;
                        var paDetails = paDetailQuery.Where(a => a.PurPAApplicationId == pa.Id).ToList();
                        var thisPrDetailIds = paDetails.Select(x => x.PRDetailId).Distinct().ToList();
                        var intercepts = interceptQuery.Where(a => thisPrDetailIds.Contains(a.PRDetailId) && a.InterceptStatus == InterceptStatus.Intercepting).ToList();
                        if (intercepts.Any())
                        {
                            pa.TaskType = PAApprovalTaskStatus.Intercepted;
                        }
                        else
                        {
                            pa.TaskType = PAApprovalTaskStatus.ToBeDistributed;
                        }
                        #endregion
                    }
                    else if (PurPAApplicationStatus.FinancialPreliminaryReview.Equals(pa.Status))
                    {
                        pa.Status = PurPAApplicationStatus.FinancialReview;
                    }
                    else if (PurPAApplicationStatus.FinancialReview.Equals(pa.Status) || PurPAApplicationStatus.BudgetManagerApproval.Equals(pa.Status))
                    {//当前财务复审包含等待财务复审和等待预算负责人审批 同样的操作逻辑
                        var queryBpcsGcc = await LazyServiceProvider.LazyGetService<IBpcsGccRepository>().GetQueryableAsync();
                        pa.Status = PurPAApplicationStatus.WaitingForPayment;
                        pa.ApprovedDate = DateTime.Now;
                        var gr = grQuery.Where(a => a.Id == pa.GRId).FirstOrDefault();
                        var usdGcc = queryBpcsGcc.Where(a => a.Ccfrcr == gr.Currency && a.Cctocr == "RMB").OrderByDescending(a => a.Ccnvdt).FirstOrDefault();
                        var rate = 1M;
                        if (usdGcc != null)
                        {
                            rate = usdGcc.Ccnvfc.Value;
                        }
                        pa.BpcsRate = (float)rate;
                        //复审完成后继续收货
                        if (pa.IsLastPayment)
                        {
                            gr.Status = PurGRApplicationStatus.ReceivedGoods;
                            gr.ReceivedTime = DateTime.Now;
                            await LazyServiceProvider.GetService<IPurPAApplicationService>().PABudgetRefundAsync(pa.Id, rate);//预算返还
                        }
                        else
                        {
                            gr.Status = PurGRApplicationStatus.ToBeReceived;
                        }
                        gr.UseBudgetTime = null;//清除GR提交时使用预算时间，待后续收货使用
                        await grApplicationRepository.UpdateAsync(gr);
                        await UpdateFinancialVoucherInfoAsync(pa.Id, rate);
                        await paApplicationRepository.UpdateAsync(pa, true);
                        await PAPushOtherSystemAsync(pa);
                        pa.UseBudgetTime = null;//清除提交时使用预算时间，还原
                    }
                    break;
                case ApprovalPowerAppStatus.Denied:
                    if (PurPAApplicationStatus.Reissue.Equals(pa.Status))
                    {
                        pa.TaskType = null;
                        pa.Status = PurPAApplicationStatus.Reissue;
                    }
                    else if (PurPAApplicationStatus.DocumentReceipt.Equals(pa.Status))
                    {
                        pa.TaskType = PAApprovalTaskStatus.ToBeDistributed;
                        pa.Status = PurPAApplicationStatus.Reissue;
                    }
                    else if (PurPAApplicationStatus.FinancialPreliminaryReview.Equals(pa.Status) || PurPAApplicationStatus.FinancialReview.Equals(pa.Status) || PurPAApplicationStatus.BudgetManagerApproval.Equals(pa.Status))
                    {   //财务初审或复审时
                        if (WithdrawNodes.InterceptSendBack.Equals(pa.SendBackType))//OEC 拦截触发的退回回调
                        {
                            pa.Status = PurPAApplicationStatus.DocumentReceipt;
                            pa.TaskType = PAApprovalTaskStatus.Intercepted;
                        }
                        else if (WithdrawNodes.Applicant.Equals(pa.SendBackType))
                        {
                            pa.Status = PurPAApplicationStatus.Reissue;
                            pa.TaskType = null;
                        }
                        else if (WithdrawNodes.OriginalFile.Equals(pa.SendBackType))
                        {
                            pa.Status = PurPAApplicationStatus.DocumentReceipt;
                            pa.TaskType = PAApprovalTaskStatus.ToBeDistributed;
                        }
                    }
                    else if (PurPAApplicationStatus.Approvaling.Equals(pa.Status))
                    {
                        pa.Status = PurPAApplicationStatus.Reissue;
                    }
                    pa.SendBackType = null;//重置退回类型，待下次使用

                    if (PurPAApplicationStatus.Reissue.Equals(pa.Status))
                    { //最终当前PA状态为重新发起时 并且有多使用的预算则需要返还预算
                        if (pa.UseBudgetTime.HasValue)
                            await LazyServiceProvider.LazyGetService<IPurGRApplicationService>().ReturnSubbudgetAsync(pa.PRId, pa.Id, pa.UseBudgetTime.Value);//预算返还
                        pa.UseBudgetTime = null;//清除提交时使用预算时间，待后续提交使用
                    }
                    break;
            }
            await paApplicationRepository.UpdateAsync(pa, true);
            return MessageResult.SuccessResult();
        }
        /// <summary>
        /// 更新财务凭证信息
        /// </summary>
        /// <param name="paId"></param>
        /// <param name="bpcsRate"></param>
        /// <returns></returns>
        private async Task UpdateFinancialVoucherInfoAsync(Guid paId, decimal bpcsRate)
        {
            var paVoucherRepository = LazyServiceProvider.LazyGetService<IPurPAFinancialVoucherInfoRepository>();
            var paFinancialVoucherInfos = await paVoucherRepository.GetListAsync(a => a.PAId == paId);
            paFinancialVoucherInfos.ForEach(a =>
            {
                a.GeneralLedgerDate = DateTime.Now.ToString("yyyyMMdd");
                a.RecognitionRate = bpcsRate;
            });
            await paVoucherRepository.UpdateManyAsync(paFinancialVoucherInfos);
        }
        /// <summary>
        /// PA 推送其他系统
        /// </summary>
        /// <param name="paId"></param>
        /// <param name="prId"></param>
        /// <param name="approvedDate"></param>
        /// <returns></returns>
        private async Task PAPushOtherSystemAsync(PurPAApplication pa)
        {
            var prApplicationRepository = LazyServiceProvider.GetService<IPurPRApplicationRepository>();
            var prQuery = await prApplicationRepository.GetQueryableAsync();
            try
            {
                //复审通过后推送Bpcs
                //BackgroundJob.Enqueue(() => LazyServiceProvider.GetService<IInteBpcsPAAppService>().SyncPAById(paId));
                await LazyServiceProvider.GetService<IInteBpcsPAAppService>().SyncPAById(pa.Id);
                var pr = prQuery.Where(x => x.Id == pa.PRId).FirstOrDefault();
                //复审通过后推送Om
                if (pr != null && pr.IsEsignUsed == true)
                {
                    var identityUserRepository = LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>();
                    var approvalUser = await identityUserRepository.SingleOrDefaultAsync(x => x.Id == pa.ApprovedUserId);

                    var onOff = await LazyServiceProvider.LazyGetService<ISettingRepository>().FindAsync(SettingsConst.OmBpmInterfaceOnAndOff, "G", null);
                    if (onOff?.Value == "OFF")
                    {
                        //2389【集成】EPD复审状态接口 / api / nextBpm / meeting / review推送失败：1、approvalPerson应该传财务复审人员；2、approvalTime传年月日，格式为“YYYY - MM - DD”
                        await LazyServiceProvider.GetService<IIntegrationOmAppService>().OmReviewMeeting(pa.ApplicationCode, approvalUser.Name, pa.ApprovedDate.Value.ToString("yyyy-MM-dd"));
                    }
                    else
                    {
                        //调度作业  老BPM
                        BackgroundJob.Enqueue<BpmOmWorker>(a => a.DoWorkAsync(new BpmRequestDto() { PaNo = pa.ApplicationCode, ApprovalPerson = approvalUser.Name, ApprovalTime = pa.ApprovedDate.Value.ToString("yyyy-MM-dd"), InterfaceType = Enums.Integration.OmBpmType.Review }));
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogException(ex, LogLevel.Error);
                _logger.LogError($"PAPushOtherSystem Error:{ex.Message}");
            }
        }
        /// <summary>
        /// 创建收货历史记录
        /// </summary>
        /// <returns></returns>
        private async Task<List<Guid>> CreateGRHistoryAsync(Guid grId)
        {
            var grDetailQuery = await _serviceProvider.GetService<IPurGRApplicationDetailRepository>().GetQueryableAsync();
            var grDetailQueryHistory = _serviceProvider.GetService<IPurGRApplicationDetailHistoryRepository>();
            var grDetail = grDetailQuery.Where(a => a.GRApplicationId == grId).ToList();
            var historys = ObjectMapper.Map<List<PurGRApplicationDetail>, List<PurGRApplicationDetailHistory>>(grDetail);
            await grDetailQueryHistory.InsertManyAsync(historys, true);
            return historys.Select(a => a.Id).ToList();
        }

        private async Task<MessageResult> CreateAgentHistoryApprovalTask(IEnumerable<CreateAgentHistoryRequestDto> request)
        {
            var queryAgentConfig = await _serviceProvider.GetService<IAgentConfigRepository>().GetQueryableAsync();
            var agentHistoryRepository = _serviceProvider.GetService<IAgentHistoryRepository>();

            var dataverseService = _serviceProvider.GetService<IDataverseService>();
            var businessTypeAndWorkflowTypeMapping = await dataverseService.GetBusinessTypeAndWorkflowTypeAsync();

            var originalOperators = request.Select(x => x.SubmitterId).Distinct();
            var workflowTypes = request.Where(x => x.WorkflowType.HasValue).Select(x => x.WorkflowType).Distinct();
            var businessTypes = businessTypeAndWorkflowTypeMapping.Where(x => workflowTypes.Contains(x.WorkflowType));

            var dateTimeNow = DateTime.Now;
            var queryDatas = queryAgentConfig.Where(x => x.Status && x.AgentOperator == CurrentUser.Id && x.StartDate <= dateTimeNow && x.EndDate >= dateTimeNow.AddDays(-1))
                .WhereIf(originalOperators.Count() > 0, x => originalOperators.Contains(x.OriginalOperator))
                .WhereIf(businessTypes.Count() > 0, x => !x.BusinessTypeId.HasValue || businessTypes.Select(b => b.BusinessTypeId).Contains((Guid)x.BusinessTypeId))
                .WhereIf(businessTypes.Count() < 1, x => !x.BusinessTypeId.HasValue).ToArray();

            var histories = new List<AgentHistory>();
            foreach (var item in request)
            {
                var agentcofig = queryDatas.Where(x => x.OriginalOperator == item.SubmitterId).OrderByDescending(x => x.CreationTime).FirstOrDefault();
                if (agentcofig == null)
                    continue;
                histories.Add(new AgentHistory
                {
                    AgentConfigId = agentcofig.Id,
                    //WorkflowType = agentcofig.WorkflowType,
                    BusinessTypeId = agentcofig.BusinessTypeId,
                    BusinessTypeName = agentcofig.BusinessTypeName,
                    BusinessTypeCategory = agentcofig.BusinessTypeCategory.HasValue ? agentcofig.BusinessTypeCategory.Value : item.BusinessTypeCategory,
                    AgentTime = DateTime.Now,
                    FormId = item.FormId,
                    FormCode = item.FormNo
                });
            }

            await agentHistoryRepository.InsertManyAsync(histories);

            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 创建代理历史记录
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        private async Task<MessageResult> CreateAgentHistory(List<CreateAgentHistoryRequestDto> request)
        {
            //操作人不是申请人也不是转办人的时候 就是代理
            //check 单据的提交/申请人与当前登录用户是否一致，一致则为本人操作，不一致则为代理人操作。代理人操作(重新提交/作废)需记录代理历史
            if (CurrentUser?.Id == null)
                return MessageResult.FailureResult("请登录");

            //var targets = request.Where(x => x.SubmitterId != CurrentUser.Id);

            //if (!targets.Any())
            //    return MessageResult.SuccessResult();

            //由于各个单据在提交和做提交人操作时，传递的DTO的Submitter都是CurrentUser.Id，所以只能再统一查询一下原单据：操作人不是申请人也不是转办人的时候 就是代理
            var agentForms = await GetAgentForms(request);

            if (agentForms.Count() == 0)
                return MessageResult.SuccessResult();

            return await CreateAgentHistoryApprovalTask(agentForms);
        }

        private async Task<IEnumerable<CreateAgentHistoryRequestDto>> GetAgentForms(IEnumerable<CreateAgentHistoryRequestDto> request)
        {
            //供应商申请SP-V
            var queryableSP = (await LazyServiceProvider.LazyGetService<IVendorApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            //采购申请PR-P
            var queryablePR = (await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            //采购订单PO-O
            var queryablePO = (await LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            //供应商比价BD-B
            var queryableBD = (await LazyServiceProvider.LazyGetService<IPurBDApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            //收货GR-G
            var queryableGR = (await LazyServiceProvider.LazyGetService<IPurGRApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            //付款PA-A
            var queryablePA = (await LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            //竞价豁免BW-W
            var queryableBW = (await LazyServiceProvider.LazyGetService<IPurBWApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            //讲者授权申请SA-SA
            var queryableSA = (await LazyServiceProvider.LazyGetService<IOECSpeakerAuthApplyRepository>().GetQueryableAsync()).AsNoTracking();

            var results = new List<CreateAgentHistoryRequestDto>();
            var requestCategory = request.Where(x => x.FormNo.StartsWith('V'));
            if (requestCategory.Any())
            {
                var transfereeIds = queryableSP.Where(x => requestCategory.Select(x => x.FormId).Contains(x.Id)).Select(x => new { x.Id, x.ApplyUserId, x.TransfereeId }).ToArray();
                //操作人不是申请人也不是转办人的时候 就是代理
                var rightFormIds = transfereeIds.Where(x => x.ApplyUserId != CurrentUser.Id.Value && (!x.TransfereeId.HasValue || x.TransfereeId != CurrentUser.Id.Value)).Select(x => x.Id);
                foreach (var item in requestCategory)
                {
                    if (!rightFormIds.Contains(item.FormId))
                        continue;
                    var agentItem = transfereeIds.FirstOrDefault(x => x.Id == item.FormId);
                    if (agentItem == null)
                        continue;
                    item.SubmitterId = agentItem.ApplyUserId;
                    results.Add(item);
                }
            }

            requestCategory = request.Where(x => x.FormNo.StartsWith('P'));
            if (requestCategory.Any())
            {
                var transfereeIds = queryablePR.Where(x => requestCategory.Select(x => x.FormId).Contains(x.Id)).Select(x => new { x.Id, x.ApplyUserId, x.TransfereeId }).ToArray();
                var rightFormIds = transfereeIds.Where(x => x.ApplyUserId != CurrentUser.Id.Value && (!x.TransfereeId.HasValue || x.TransfereeId != CurrentUser.Id.Value)).Select(x => x.Id);
                foreach (var item in requestCategory)
                {
                    if (!rightFormIds.Contains(item.FormId))
                        continue;
                    var agentItem = transfereeIds.FirstOrDefault(x => x.Id == item.FormId);
                    if (agentItem == null)
                        continue;
                    item.SubmitterId = agentItem.ApplyUserId;
                    results.Add(item);
                }
            }

            requestCategory = request.Where(x => x.FormNo.StartsWith('O'));
            if (requestCategory.Any())
            {
                var transfereeIds = queryablePO.Where(x => requestCategory.Select(x => x.FormId).Contains(x.Id)).Select(x => new { x.Id, x.ApplyUserId, x.TransfereeId }).ToArray();
                var rightFormIds = transfereeIds.Where(x => x.ApplyUserId != CurrentUser.Id.Value && (!x.TransfereeId.HasValue || x.TransfereeId != CurrentUser.Id.Value)).Select(x => x.Id);
                foreach (var item in requestCategory)
                {
                    if (!rightFormIds.Contains(item.FormId))
                        continue;
                    var agentItem = transfereeIds.FirstOrDefault(x => x.Id == item.FormId);
                    if (agentItem == null)
                        continue;
                    item.SubmitterId = agentItem.ApplyUserId;
                    results.Add(item);
                }
            }

            requestCategory = request.Where(x => x.FormNo.StartsWith('B'));
            if (requestCategory.Any())
            {
                var transfereeIds = queryableBD.Where(x => requestCategory.Select(x => x.FormId).Contains(x.Id)).Select(x => new { x.Id, x.ApplyUserId, x.TransfereeId }).ToArray();
                var rightFormIds = transfereeIds.Where(x => x.ApplyUserId != CurrentUser.Id.Value && (!x.TransfereeId.HasValue || x.TransfereeId != CurrentUser.Id.Value)).Select(x => x.Id);
                foreach (var item in requestCategory)
                {
                    if (!rightFormIds.Contains(item.FormId))
                        continue;
                    var agentItem = transfereeIds.FirstOrDefault(x => x.Id == item.FormId);
                    if (agentItem == null)
                        continue;
                    item.SubmitterId = agentItem.ApplyUserId;
                    results.Add(item);
                }
            }

            requestCategory = request.Where(x => x.FormNo.StartsWith('G'));
            if (requestCategory.Any())
            {
                var transfereeIds = queryableGR.Where(x => requestCategory.Select(x => x.FormId).Contains(x.Id)).Select(x => new { x.Id, x.ApplyUserId, x.TransfereeId }).ToArray();
                var rightFormIds = transfereeIds.Where(x => x.ApplyUserId != CurrentUser.Id.Value && (!x.TransfereeId.HasValue || x.TransfereeId != CurrentUser.Id.Value)).Select(x => x.Id);
                foreach (var item in requestCategory)
                {
                    if (!rightFormIds.Contains(item.FormId))
                        continue;
                    var agentItem = transfereeIds.FirstOrDefault(x => x.Id == item.FormId);
                    if (agentItem == null)
                        continue;
                    item.SubmitterId = agentItem.ApplyUserId;
                    results.Add(item);
                }
            }

            requestCategory = request.Where(x => x.FormNo.StartsWith('A'));
            if (requestCategory.Any())
            {
                var transfereeIds = queryablePA.Where(x => requestCategory.Select(x => x.FormId).Contains(x.Id)).Select(x => new { x.Id, x.ApplyUserId, x.TransfereeId }).ToArray();
                var rightFormIds = transfereeIds.Where(x => x.ApplyUserId != CurrentUser.Id.Value && (!x.TransfereeId.HasValue || x.TransfereeId != CurrentUser.Id.Value)).Select(x => x.Id);
                foreach (var item in requestCategory)
                {
                    if (!rightFormIds.Contains(item.FormId))
                        continue;
                    var agentItem = transfereeIds.FirstOrDefault(x => x.Id == item.FormId);
                    if (agentItem == null)
                        continue;
                    item.SubmitterId = agentItem.ApplyUserId;
                    results.Add(item);
                }
            }

            requestCategory = request.Where(x => x.FormNo.StartsWith('W'));
            if (requestCategory.Any())
            {
                var transfereeIds = queryableBW.Where(x => requestCategory.Select(x => x.FormId).Contains(x.Id)).Select(x => new { x.Id, x.ApplyUserId, x.TransfereeId }).ToArray();
                var rightFormIds = transfereeIds.Where(x => x.ApplyUserId != CurrentUser.Id.Value && (!x.TransfereeId.HasValue || x.TransfereeId != CurrentUser.Id.Value)).Select(x => x.Id);
                foreach (var item in requestCategory)
                {
                    if (!rightFormIds.Contains(item.FormId))
                        continue;
                    var agentItem = transfereeIds.FirstOrDefault(x => x.Id == item.FormId);
                    if (agentItem == null)
                        continue;
                    item.SubmitterId = agentItem.ApplyUserId;
                    results.Add(item);
                }
            }

            requestCategory = request.Where(x => x.FormNo.StartsWith("SA"));
            if (requestCategory.Any())
            {
                var transfereeIds = queryableSA.Where(x => requestCategory.Select(x => x.FormId).Contains(x.Id)).Select(x => new { x.Id, x.ApplyUserId, x.TransfereeId }).ToArray();
                var rightFormIds = transfereeIds.Where(x => x.ApplyUserId != CurrentUser.Id.Value && (!x.TransfereeId.HasValue || x.TransfereeId != CurrentUser.Id.Value)).Select(x => x.Id);
                foreach (var item in requestCategory)
                {
                    if (!rightFormIds.Contains(item.FormId))
                        continue;
                    var agentItem = transfereeIds.FirstOrDefault(x => x.Id == item.FormId);
                    if (agentItem == null)
                        continue;
                    item.SubmitterId = agentItem.ApplyUserId;
                    results.Add(item);
                }
            }

            return results;
        }

        /// <summary>
        /// 豁免单，通过、拒绝、退回
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        private async Task<MessageResult> UpdateBWApplication(ApprovalResultsRequestDto request)
        {
            var repo = LazyServiceProvider.GetService<IPurBWApplicationRepository>();
            var bw = await repo.GetAsync(request.FormId);
            if (bw == null)
                return MessageResult.FailureResult("未找到对应的豁免申请");
            switch (request.ApproveStatus)
            {
                //通过
                case ApprovalPowerAppStatus.Approved:
                    bw.Status = PurExemptStatus.Approved;
                    bw.ApprovedTime = DateTime.Now;
                    break;
                //拒绝
                case ApprovalPowerAppStatus.Rejected:
                    bw.Status = PurExemptStatus.Rejected;
                    break;
                //退回
                case ApprovalPowerAppStatus.Denied:
                    bw.Status = PurExemptStatus.Return;
                    break;
                default:
                    return MessageResult.FailureResult("豁免单审批状态不是：通过、拒绝、退回");
            }
            await repo.UpdateAsync(bw);
            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 豁免单，撤回、作废
        /// </summary>
        /// <param name="ids">The ids.</param>
        /// <param name="status">The status.</param>
        /// <returns></returns>
        private async Task<MessageResult> UpdateBWApplication(List<Guid> ids, ApprovalOperation status)
        {
            if (ids.Count < 1)
            {
                return MessageResult.SuccessResult();
            }

            var repo = LazyServiceProvider.GetService<IPurBWApplicationRepository>();
            var bw = await repo.GetListAsync(a => ids.Contains(a.Id));
            if (bw.Count < 1)
                return MessageResult.FailureResult("未找到对应的豁免申请");
            PurExemptStatus? approvalStatus = null;
            switch (status)
            {
                //撤回
                case ApprovalOperation.Recall:
                    approvalStatus = PurExemptStatus.Return;
                    break;
                //作废
                case ApprovalOperation.Delete:
                    approvalStatus = PurExemptStatus.Invalid;
                    break;
                default:
                    return MessageResult.FailureResult("豁免单审批状态不是：撤回、作废");
            }
            if (approvalStatus.HasValue)
            {
                bw.ForEach(a => a.Status = approvalStatus.Value);
            }
            await repo.UpdateManyAsync(bw);
            await UpdatePRDetailByBWAsync(bw);//撤回和作废都需要清空PR明细中BWID
            return MessageResult.SuccessResult();
        }
        /// <summary>
        /// 清空PR明细中BW关联关系
        /// </summary>
        /// <param name="bws"></param>
        /// <returns></returns>
        private async Task UpdatePRDetailByBWAsync(List<PurBWApplication> bws)
        {
            var prDetailRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>();
            var queryPrDetail = await prDetailRepository.GetQueryableAsync();

            var prIds = bws.Select(a => a.PRId).ToArray();
            var prdIds = bws.SelectMany(a => a.PRDetailIds.Split(",").Select(Guid.Parse), (a, b) => b);

            var prds = queryPrDetail.Where(a => prIds.Contains(a.PRApplicationId) && a.BWApplicationId.HasValue && prdIds.Contains(a.Id)).ToList();
            prds.ForEach(a => { a.BWApplicationId = null; });
            await prDetailRepository.UpdateManyAsync(prds);
        }

        /// <summary>
        /// T5讲者授权申请，通过、拒绝、退回
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        private async Task<MessageResult> UpdateSpeakerAuthApplyAsync(ApprovalResultsRequestDto request)
        {
            var speakerAuthRepository = LazyServiceProvider.GetService<IOECSpeakerAuthApplyRepository>();
            var speakerAuth = await speakerAuthRepository.GetAsync(request.FormId);
            if (speakerAuth == null)
                return MessageResult.FailureResult("未找到对应的豁免申请");
            switch (request.ApproveStatus)
            {
                //通过
                case ApprovalPowerAppStatus.Approved:
                    speakerAuth.Status = SpeakerAuthStatus.ToBeActivated;
                    break;
                //拒绝
                case ApprovalPowerAppStatus.Rejected:
                    speakerAuth.Status = SpeakerAuthStatus.Rejected;
                    break;
                //退回
                case ApprovalPowerAppStatus.Denied:
                    speakerAuth.Status = SpeakerAuthStatus.Return;
                    break;
                default:
                    return MessageResult.FailureResult("讲者授权审批状态不是：通过、拒绝、退回");
            }
            await speakerAuthRepository.UpdateAsync(speakerAuth);
            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// 讲者授权审批，撤回、作废
        /// </summary>
        /// <param name="ids">The ids.</param>
        /// <param name="status">The status.</param>
        /// <returns></returns>
        private async Task<MessageResult> UpdateSpeakerAuthApplyAsync(List<Guid> ids, ApprovalOperation status)
        {
            if (ids.Count < 1)
            {
                return MessageResult.SuccessResult();
            }

            var speakerAuthRepository = LazyServiceProvider.GetService<IOECSpeakerAuthApplyRepository>();
            var speakerAuths = await speakerAuthRepository.GetListAsync(a => ids.Contains(a.Id));
            if (speakerAuths.Count < 1)
                return MessageResult.FailureResult("未找到对应的讲者授权审批");
            SpeakerAuthStatus? approvalStatus = null;
            switch (status)
            {
                //撤回
                case ApprovalOperation.Recall:
                    approvalStatus = SpeakerAuthStatus.Return;
                    break;
                //作废
                case ApprovalOperation.Delete:
                    approvalStatus = SpeakerAuthStatus.Invalid;
                    break;
                default:
                    return MessageResult.FailureResult("豁免单审批状态不是：撤回、作废");
            }
            if (approvalStatus.HasValue)
            {
                speakerAuths.ForEach(a => a.Status = approvalStatus.Value);
            }
            await speakerAuthRepository.UpdateManyAsync(speakerAuths);
            return MessageResult.SuccessResult();
        }

        #region 发送退回邮件
        /// <summary>
        /// 发送退回邮件
        /// </summary>
        /// <param name="info"></param>
        /// <returns></returns>
        async Task SendReturnOrRejectedEmail((IEnumerable<Guid> ApplyUserId, string WorkflowTypeName, string ApplicationCode, DateTime CompletedTime, ApprovalPowerAppStatus ApprovalPowerAppStatus) info)
        {
            var webRoot = LazyServiceProvider.LazyGetService<IWebHostEnvironment>().WebRootFileProvider;
            var emailBodyHtmlTemplate = await webRoot.GetFileInfo("Templates/Email/WorkflowDeniedNotify.html").ReadAsStringAsync();
            var vendorTypes = EnumUtil.GetEnumIdValues<VendorTypes>();

            var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var users = queryableUser.Where(a => info.ApplyUserId.ToHashSet().Contains(a.Id)).ToArray();

            var statusName = info.ApprovalPowerAppStatus == ApprovalPowerAppStatus.Denied ? "退回" : "拒绝";
            var configuration = LazyServiceProvider.LazyGetService<IConfiguration>();

            var sendEmaillRecords = users.Select(user =>
            {
                var bodyHtml = emailBodyHtmlTemplate;
                bodyHtml = bodyHtml.Replace("{UserName}", user.Name);
                bodyHtml = bodyHtml.Replace("{WorkflowTypeName}", info.WorkflowTypeName);
                bodyHtml = bodyHtml.Replace("{ApplicationCode}", info.ApplicationCode);
                bodyHtml = bodyHtml.Replace("{Operation}", statusName);
                bodyHtml = bodyHtml.Replace("{CompletedTime}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                bodyHtml = bodyHtml.Replace("{ApplicationLink}", $"{configuration["SpeakerEmail:WebHost"]?.TrimEnd('/')}/task/launch");
                bodyHtml = bodyHtml.Replace("{Tab}", info.ApprovalPowerAppStatus == ApprovalPowerAppStatus.Denied ? "待处理" : "已完成");

                return new InsertSendEmaillRecordDto
                {
                    EmailAddress = user.Email,
                    Subject = $"[NexBPM消息中心]您申请的【{info.WorkflowTypeName}】：{info.ApplicationCode}已被{statusName}。",
                    Content = bodyHtml,
                    SourceType = info.ApprovalPowerAppStatus == ApprovalPowerAppStatus.Denied ? EmailSourceType.ApplicationReturned : EmailSourceType.ApplicationRejected,
                    Status = SendStatus.Pending,
                    Attempts = 0,
                };
            });

            //记录邮件，并触发邮件发送功能
            var emailService = LazyServiceProvider.LazyGetService<IEmailService>();
            await emailService.InsertSendEmaillRecordAsync(sendEmaillRecords.ToList());
            // 调度作业
            BackgroundJob.Enqueue<SendEmailWorker>(a => a.DoWorkAsync());
        }



        /// <summary>
        /// 采购推送退单发送退回邮件
        /// </summary>
        /// <param name="info"></param>
        /// <returns></returns>
        public async Task SendReturnOrRollbackEmail(Guid UserId, string Operator, string RollbackReason, string ApplicationCode, DateTime CompletedTime)
        {
            var webRoot = LazyServiceProvider.LazyGetService<IWebHostEnvironment>().WebRootFileProvider;
            var emailBodyHtmlTemplate = await webRoot.GetFileInfo("Templates/Email/WorkflowRollbackNotify.html").ReadAsStringAsync();
            var vendorTypes = EnumUtil.GetEnumIdValues<VendorTypes>();

            var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var users = queryableUser.Where(a => UserId == a.Id).ToArray();

            var sendEmaillRecords = users.Select(user =>
            {
                var bodyHtml = emailBodyHtmlTemplate;
                bodyHtml = bodyHtml.Replace("{ApplicationCode}", ApplicationCode);
                bodyHtml = bodyHtml.Replace("{Operator}", Operator);
                bodyHtml = bodyHtml.Replace("{RollbackReason}", RollbackReason);
                bodyHtml = bodyHtml.Replace("{CompletedTime}", CompletedTime.ToString("yyyy-MM-dd HH:mm:ss"));
                return new InsertSendEmaillRecordDto
                {
                    EmailAddress = user.Email,
                    Subject = $"[NexBPM消息中心]您申请的PR单{ApplicationCode}被采购{Operator}退回。",
                    Content = bodyHtml,
                    SourceType = EmailSourceType.ApplicationReturned,
                    Status = SendStatus.Pending,
                    Attempts = 0,
                };
            });
            //记录邮件，并触发邮件发送功能
            var emailService = LazyServiceProvider.LazyGetService<IEmailService>();
            await emailService.InsertSendEmaillRecordAsync(sendEmaillRecords.ToList());
            // 调度作业
            BackgroundJob.Enqueue<SendEmailWorker>(a => a.DoWorkAsync());
        }
        

        /// <summary>
        /// 发送退回邮件
        /// </summary>
        /// <param name="id"></param>
        /// <param name="formName"></param>
        /// <param name="completedTime"></param>
        /// <returns></returns>
        private async Task PreSendEmailAsync(Guid id, string formName, DateTime completedTime, ApprovalPowerAppStatus approvalPowerAppStatus)
        {
            Guid applyUserId = Guid.Empty;
            string applicationName = "";
            string applicationCode = "";
            string applicateName = string.Empty;
            Guid deptId = Guid.Empty;
            Volo.Abp.Domain.Entities.Entity entity = null;

            ResignationTransfer.TaskFormCategory? bizCategory = null;
            Guid? transfereeId = null;

            switch (formName)
            {
                case NameConsts.VendorApplication:
                    var vendorApplication = _serviceProvider.GetService<IVendorApplicationRepository>();
                    var vendor = await vendorApplication.GetAsync(a => a.Id == id);
                    //供应商类型
                    var vendorTypes = EnumUtil.GetEnumIdValues<VendorTypes>();
                    var vendorType = vendorTypes.FirstOrDefault(a => a.Key == (int)vendor.VendorType)?.Value;
                    //申请类型
                    var applicationTypes = EnumUtil.GetEnumIdValues<ApplicationTypes>();
                    var applicationType = applicationTypes.FirstOrDefault(a => a.Key == (int)vendor.ApplicationType)?.Value;

                    bizCategory = ResignationTransfer.TaskFormCategory.VendorApplication;
                    applyUserId = vendor.ApplyUserId;
                    transfereeId = vendor.TransfereeId;
                    applicationName = $"{vendorType}-{applicationType}";
                    applicationCode = vendor.ApplicationCode;
                    break;
                case NameConsts.PRApplication:
                    var prRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>();
                    var pr = await prRepository.GetAsync(a => a.Id == id);
                    bizCategory = ResignationTransfer.TaskFormCategory.PurchaseRequestApplication;
                    applyUserId = pr.ApplyUserId;
                    transfereeId = pr.TransfereeId;
                    applicationName = "采购申请";
                    applicationCode = pr.ApplicationCode;
                    break;
                case NameConsts.BDApplication:
                    var bdRepository = LazyServiceProvider.LazyGetService<IPurBDApplicationRepository>();
                    var bd = await bdRepository.GetAsync(a => a.Id == id);
                    bizCategory = ResignationTransfer.TaskFormCategory.BiddingApplication;
                    applyUserId = bd.ApplyUserId;
                    transfereeId = bd.TransfereeId;
                    applicationName = "供应商比价";
                    applicationCode = bd.ApplicationCode;
                    break;
                case NameConsts.POApplication:
                    var poRepository = LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>();
                    var po = await poRepository.GetAsync(a => a.Id == id);
                    bizCategory = ResignationTransfer.TaskFormCategory.PurhcaseOrderApplication;
                    applyUserId = po.ApplyUserId;
                    transfereeId = po.TransfereeId;
                    applicationName = "采购订单";
                    applicationCode = po.ApplicationCode;
                    break;
                case NameConsts.GRApplication:
                    var grRepository = LazyServiceProvider.LazyGetService<IPurGRApplicationRepository>();
                    var gr = await grRepository.GetAsync(a => a.Id == id);
                    bizCategory = ResignationTransfer.TaskFormCategory.GoodsReceiptApplication;
                    applyUserId = gr.ApplyUserId;
                    transfereeId = gr.TransfereeId;
                    applicationName = "收货申请";
                    applicationCode = gr.ApplicationCode;
                    break;
                case NameConsts.PAApplication:
                    var paRepository = LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>();
                    var pa = await paRepository.GetAsync(a => a.Id == id);
                    bizCategory = ResignationTransfer.TaskFormCategory.PaymentApplication;
                    applyUserId = pa.ApplyUserId;
                    transfereeId = pa.TransfereeId;
                    applicationName = "付款申请";
                    applicationCode = pa.ApplicationCode;
                    entity = pa;
                    break;
                case NameConsts.PurExemptWaiver:
                    var jwRepository = LazyServiceProvider.LazyGetService<IPurBWApplicationRepository>();
                    var jw = await jwRepository.GetAsync(a => a.Id == id);
                    bizCategory = ResignationTransfer.TaskFormCategory.BiddingWaiverApplication;
                    applyUserId = jw.ApplyUserId;
                    transfereeId = jw.TransfereeId;
                    applicationName = "竞价豁免";
                    applicationCode = jw.ApplicationCode;
                    break;
                case NameConsts.PurExemptJustification:
                    var jjRepository = LazyServiceProvider.LazyGetService<IPurBWApplicationRepository>();
                    var jj = await jjRepository.GetAsync(a => a.Id == id);
                    bizCategory = ResignationTransfer.TaskFormCategory.BiddingWaiverApplication;
                    applyUserId = jj.ApplyUserId;
                    transfereeId = jj.TransfereeId;
                    applicationName = "Justification";
                    applicationCode = jj.ApplicationCode;
                    break;
                case NameConsts.OECSpeakerAuthApply:
                    var speakerAuthRepository = LazyServiceProvider.GetService<IOECSpeakerAuthApplyRepository>();
                    var sa = await speakerAuthRepository.GetAsync(a => a.Id == id);
                    bizCategory = ResignationTransfer.TaskFormCategory.OECSpeakerAuthApplication;
                    applyUserId = sa.ApplyUserId;
                    transfereeId = sa.TransfereeId;
                    applicationName = "讲者授权申请";
                    applicationCode = sa.ApplicationCode;
                    break;
                case NameConsts.STicketApplication:
                    var sTicketApplicationRepository = LazyServiceProvider.GetService<ISTicketApplicationRepository>();
                    var sta = await sTicketApplicationRepository.GetAsync(a => a.Id == id);
                    bizCategory = ResignationTransfer.TaskFormCategory.STicketRequestApplication;
                    applyUserId = sta.ApplyUserId;
                    transfereeId = sta.TransfereeId;
                    applicationName = "核销单申请";
                    applicationCode = sta.ApplicationCode;
                    break;
                case NameConsts.FOCApplication:
                    var FocApplicationRepository = LazyServiceProvider.GetService<IFocApplicationRepository>();
                    var Foc = await FocApplicationRepository.GetAsync(a => a.Id == id);
                    bizCategory = ResignationTransfer.TaskFormCategory.FOCRequestApplication;
                    applyUserId = Foc.ApplyUserId;
                    transfereeId = Foc.TransfereeId;
                    applicationName = "FOC申请";
                    applicationCode = Foc.ApplicationCode;
                    deptId = Foc.ApplyUserDeptId;
                    applicateName = Foc.ApplyUser;
                    break;
                case NameConsts.ReturnAndExchangeRequest:
                    var returnRepository = LazyServiceProvider.GetService<IReturnApplicationReponsitory>();
                    var r = await returnRepository.GetAsync(a => a.Id == id);
                    bizCategory = ResignationTransfer.TaskFormCategory.ReturnAndExchangeRequest;
                    applyUserId = r.ApplyUserId;
                    applicationName = "退换货申请";
                    applicationCode = r.ApplicationCode;
                    break;
                default:
                    break;
            }

            var userIdsToNotify = await GetUsersToNotify(applyUserId, transfereeId, bizCategory);

            //PA比较特殊，单独提出来判断
            if (formName == NameConsts.PAApplication)
            {
                var pa = entity as PurPAApplication;
                //退回到重新发起才向申请人发邮件
                if (approvalPowerAppStatus == ApprovalPowerAppStatus.Denied && pa.Status == PurPAApplicationStatus.Reissue)
                    await SendReturnOrRejectedEmail((userIdsToNotify, applicationName, applicationCode, completedTime, approvalPowerAppStatus));
                else if (approvalPowerAppStatus == ApprovalPowerAppStatus.Rejected)
                    await SendReturnOrRejectedEmail((userIdsToNotify, applicationName, applicationCode, completedTime, approvalPowerAppStatus));
                //仅财务复审完成后才发送审批通过邮件
                else if (approvalPowerAppStatus == ApprovalPowerAppStatus.Approved && pa.Status == PurPAApplicationStatus.WaitingForPayment)
                    await SendApprovalEmailAsync((userIdsToNotify, applicationName, applicationCode, completedTime, approvalPowerAppStatus));
            }
            else if (formName == NameConsts.FOCApplication)
            {
                if (approvalPowerAppStatus == ApprovalPowerAppStatus.Approved)
                    await SendChooseProductEmail((id, applicateName, applicationName, applicationCode, completedTime, deptId));
                else if (approvalPowerAppStatus == ApprovalPowerAppStatus.Denied || approvalPowerAppStatus == ApprovalPowerAppStatus.Rejected)
                    await SendReturnOrRejectedEmail((userIdsToNotify, applicationName, applicationCode, completedTime, approvalPowerAppStatus));
            }
            else
            {
                if (approvalPowerAppStatus == ApprovalPowerAppStatus.Denied || approvalPowerAppStatus == ApprovalPowerAppStatus.Rejected)
                    await SendReturnOrRejectedEmail((userIdsToNotify, applicationName, applicationCode, completedTime, approvalPowerAppStatus));
                else if (approvalPowerAppStatus == ApprovalPowerAppStatus.Approved && formName != NameConsts.PRApplication && formName != NameConsts.POApplication)//Pr和Po已经有了另外的审批完成邮件，这里排除这两种类型
                    await SendApprovalEmailAsync((userIdsToNotify, applicationName, applicationCode, completedTime, approvalPowerAppStatus));
            }
        }

        /// <summary>
        /// 获取要通知的用户
        /// </summary>
        /// <param name="applyUserId"></param>
        /// <param name="transfereeId"></param>
        /// <param name="bizCategory"></param>
        /// <returns></returns>
        private async Task<IEnumerable<Guid>> GetUsersToNotify(Guid applyUserId, Guid? transfereeId, ResignationTransfer.TaskFormCategory? bizCategory)
        {
            var agentService = LazyServiceProvider.LazyGetService<IAgencyService>();
            var userIdListToNotify = new List<Guid>();
            var agentRequest = new GetAgentByOriginalOperatorRequestDto { OriginalOperatorId = applyUserId, BusinessTypeCategory = bizCategory };
            userIdListToNotify.Add(transfereeId.HasValue ? transfereeId.Value : applyUserId);//有转办人则不通知申请人
            var agent = await agentService.GetAgentByOriginalOperator(agentRequest);
            if (agent.HasValue)
                userIdListToNotify.Add(agent.Value.Key);
            return userIdListToNotify.Distinct();
        }
        #endregion

        #region 发送审批通过邮件

        async Task SendApprovalEmailAsync((IEnumerable<Guid> ApplyUserId, string WorkflowTypeName, string ApplicationCode, DateTime CompletedTime, ApprovalPowerAppStatus ApprovalPowerAppStatus) info)
        {
            var vendorTypes = EnumUtil.GetEnumIdValues<VendorTypes>();
            var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var users = queryableUser.Where(a => info.ApplyUserId.ToHashSet().Contains(a.Id)).ToArray();

            var configuration = LazyServiceProvider.LazyGetService<IConfiguration>();

            var sendEmaillRecords = users.Select(user => new InsertSendEmaillRecordDto
            {
                EmailAddress = user.Email,
                Subject = "[NexBPM消息中心]您申请的【{WorkflowTypeName}】：{ApplicationCode}已处理完成。",
                Content = JsonSerializer.Serialize(new ApprovedNoticeToApplicanDto
                {
                    UserName = user.Name,
                    WorkflowTypeName = info.WorkflowTypeName,
                    ApplicationCode = info.ApplicationCode,
                    CompletedTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    ApplicationLink = $"{configuration["SpeakerEmail:WebHost"]?.TrimEnd('/')}/task/launch"
                }),
                SourceType = EmailSourceType.ApplicationApproved,
                Status = SendStatus.Pending,
                Attempts = 0,
            });

            //记录邮件，并触发邮件发送功能
            var emailService = LazyServiceProvider.LazyGetService<IEmailService>();
            await emailService.InsertSendEmaillRecordAsync(sendEmaillRecords.ToList());
            // 调度作业
            BackgroundJob.Enqueue<SendEmailWorker>(a => a.DoWorkAsync());
        }

        #endregion

        /// <summary>
        /// 核销单申请，通过、拒绝、退回
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        private async Task<MessageResult> UpdateSTicktApplyAsync(ApprovalResultsRequestDto request, WorkflowTask task)
        {
            var sTiketRepository = LazyServiceProvider.GetService<ISTicketApplicationRepository>();
            var sTiket = await sTiketRepository.GetAsync(request.FormId);
            var ApprovalAction = LazyServiceProvider.GetService<IPostApprovalActionService>();
            if (sTiket == null)
                return MessageResult.FailureResult("未找到对应的核销单申请");
            switch (request.ApproveStatus)
            {
                //通过
                case ApprovalPowerAppStatus.Approved:
                    sTiket.Status = STicketStatus.Approved;
                    var sTicketService = LazyServiceProvider.GetService<ISTicketService>();
                    var data = await sTicketService.PushSTicketBySOIAsync(request.FormId);
                    CreatePostApprovalActionDto actionDto = new CreatePostApprovalActionDto
                    {
                        FormId = request.FormId,
                        FormNo = sTiket.ApplicationCode,
                        ApprovalStatus = request.ApproveStatus,
                        WfInstanceId = task.InstanceId,
                        WfInstanceName = task.Name,
                        WfTypeOption = WorkflowTypeName.STicketRequest,
                        ProcessingType = ProcessingTypes.ThirdIntegration,
                        RequestContent = data,
                    };
                    await ApprovalAction.CreateActionAsync(actionDto);

                    //同步批发商核销流程审批结果到CSS
                    if (sTiket.ApplicationType == STicketDataSourceConst.CSS && !string.IsNullOrEmpty(sTiket.CssCode))
                    {
                        var queryWfTask = await LazyServiceProvider.LazyGetService<IWorkflowTaskRepository>().GetQueryableAsync();
                        var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
                        var workFlowTask = queryWfTask.Where(a => a.FormId == request.FormId)
                            .GroupJoin(queryableUser, a => a.ApprovalId, a => a.Id, (a, b) => new { Task = a, Users = b })
                            .SelectMany(a => a.Users.DefaultIfEmpty(), (a, b) => new { a.Task, ApprovalUser = b })
                            .OrderByDescending(a => a.Task.ApprovalTime).FirstOrDefault();
                        PushSTicketApprovalResultDto approvalDto = new PushSTicketApprovalResultDto
                        {
                            CSSNumber = sTiket.CssCode,
                            SerialNumber = sTiket.ApplicationCode,
                            ApprovalResult = "1",
                            ApprovedName = workFlowTask.ApprovalUser != null ? workFlowTask.ApprovalUser.Name : string.Empty,
                            ApprovedTime = workFlowTask.Task.ApprovalTime.ToString("yyyy-MM-dd HH:mm:ss"),
                            ApprovedRemark = workFlowTask.Task != null ? workFlowTask.Task.Remark : string.Empty,
                        };
                        await sTicketService.STicketApprovalResultPushAsync(approvalDto);
                    }
                    break;
                //拒绝
                case ApprovalPowerAppStatus.Rejected:
                    sTiket.Status = STicketStatus.Rejected;

                    var sTicketReject = LazyServiceProvider.GetService<ISTicketService>();
                    //同步批发商核销流程审批结果到CSS
                    if (sTiket.ApplicationType == STicketDataSourceConst.CSS && !string.IsNullOrEmpty(sTiket.CssCode))
                    {
                        var queryWfTask = await LazyServiceProvider.LazyGetService<IWorkflowTaskRepository>().GetQueryableAsync();
                        var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
                        var workFlowTask = queryWfTask.Where(a => a.FormId == request.FormId)
                            .GroupJoin(queryableUser, a => a.ApprovalId, a => a.Id, (a, b) => new { Task = a, Users = b })
                            .SelectMany(a => a.Users.DefaultIfEmpty(), (a, b) => new { a.Task, ApprovalUser = b })
                            .OrderByDescending(a => a.Task.ApprovalTime).FirstOrDefault();
                        PushSTicketApprovalResultDto approvalDto = new PushSTicketApprovalResultDto
                        {
                            CSSNumber = sTiket.CssCode,
                            SerialNumber = sTiket.ApplicationCode,
                            ApprovalResult = "0",
                            ApprovedName = workFlowTask.ApprovalUser != null ? workFlowTask.ApprovalUser.Name : string.Empty,
                            ApprovedTime = workFlowTask.Task.ApprovalTime.ToString("yyyy-MM-dd HH:mm:ss"),
                            ApprovedRemark = workFlowTask.Task != null ? workFlowTask.Task.Remark : string.Empty,
                        };
                        await sTicketReject.STicketApprovalResultPushAsync(approvalDto);
                    }

                    //预算返还
                    var querySTicket = await LazyServiceProvider.LazyGetService<ISTicketApplicationRepository>().GetQueryableAsync();
                    var sticketApplication = querySTicket.Where(a => a.Id == request.FormId).FirstOrDefault();
                    var querySTicketDetails = await LazyServiceProvider.LazyGetService<ISTicketApplicationDetailRepository>().GetQueryableAsync();
                    var querySTicketApplicationDetail = querySTicketDetails.Where(a => a.ParentID == request.FormId && a.IsHedge == false);
                    var sticketDetails = querySTicketApplicationDetail.Select(a => new { a.RowId, a.SubTotalAmountRMB }).ToArray();

                    var useBudgetRequest = new ReturnBudgetRequestDto
                    {
                        PrId = sticketApplication.Id,
                        SubbudgetId = sticketApplication.SubBudgetId.Value,
                        Items = sticketDetails.Select(a => new ReturnInfo
                        {
                            PdRowNo = a.RowId,
                            ReturnAmount = a.SubTotalAmountRMB,
                            ReturnSourceId = sticketApplication.Id,
                            ReturnSourceCode = sticketApplication.ApplicationCode
                        })
                    };
                    await LazyServiceProvider.LazyGetService<ISubbudgetService>().ReturnSubbudgetAsync(useBudgetRequest);
                    break;
                //退回
                case ApprovalPowerAppStatus.Denied:
                    sTiket.Status = STicketStatus.RejectedBack;
                    break;
                default:
                    return MessageResult.FailureResult("核销单申请：通过、拒绝、退回");
            }
            sTiket.ApprovedDate = DateTime.Now;
            await sTiketRepository.UpdateAsync(sTiket);
            return MessageResult.SuccessResult();
        }
        /// <summary>
        /// 讲者授权审批，撤回、作废
        /// </summary>
        /// <param name="ids">The ids.</param>
        /// <param name="status">The status.</param>
        /// <returns></returns>
        private async Task<MessageResult> UpdateSTicktApplyAsync(List<Guid> ids, ApprovalOperation status)
        {
            if (ids.Count < 1)
            {
                return MessageResult.SuccessResult();
            }

            var sTiketRepository = LazyServiceProvider.GetService<ISTicketApplicationRepository>();
            var sTikets = await sTiketRepository.GetListAsync(a => ids.Contains(a.Id));
            if (sTikets.Count < 1)
                return MessageResult.FailureResult("未找到对应的核销单申请");
            STicketStatus? approvalStatus = null;
            switch (status)
            {
                //撤回
                case ApprovalOperation.Recall:
                    approvalStatus = STicketStatus.RejectedBack;
                    break;
                //作废
                case ApprovalOperation.Delete:
                    approvalStatus = STicketStatus.ApplicantTerminate;

                    //预算返还
                    foreach (var formId in ids)
                    {
                        var querySTicket = await LazyServiceProvider.LazyGetService<ISTicketApplicationRepository>().GetQueryableAsync();
                        var sticketApplication = querySTicket.Where(a => a.Id == formId).FirstOrDefault();
                        var querySTicketDetails = await LazyServiceProvider.LazyGetService<ISTicketApplicationDetailRepository>().GetQueryableAsync();
                        var querySTicketApplicationDetail = querySTicketDetails.Where(a => a.ParentID == formId && a.IsHedge == false);
                        var sticketDetails = querySTicketApplicationDetail.Select(a => new { a.RowId, a.SubTotalAmountRMB }).ToArray();

                        var useBudgetRequest = new ReturnBudgetRequestDto
                        {
                            PrId = sticketApplication.Id,
                            SubbudgetId = sticketApplication.SubBudgetId.Value,
                            Items = sticketDetails.Select(a => new ReturnInfo
                            {
                                PdRowNo = a.RowId,
                                ReturnAmount = a.SubTotalAmountRMB,
                                ReturnSourceId = sticketApplication.Id,
                                ReturnSourceCode = sticketApplication.ApplicationCode
                            })
                        };
                        await LazyServiceProvider.LazyGetService<ISubbudgetService>().ReturnSubbudgetAsync(useBudgetRequest);
                    }
                    break;
                default:
                    return MessageResult.FailureResult("核销单批状态不是：撤回、作废");
            }
            if (approvalStatus.HasValue)
            {
                sTikets.ForEach(a => a.Status = approvalStatus.Value);
            }
            await sTiketRepository.UpdateManyAsync(sTikets);
            return MessageResult.SuccessResult();
        }
        /// <summary>
        /// FOC申请，通过、拒绝、退回
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        private async Task<MessageResult> UpdateFOCApplyAsync(ApprovalResultsRequestDto request)
        {
            var focRepository = LazyServiceProvider.GetService<IFocApplicationRepository>();
            var foc = await focRepository.GetAsync(request.FormId);
            if (foc == null)
                return MessageResult.FailureResult("未找到对应的FOC申请");
            switch (request.ApproveStatus)
            {
                //通过
                case ApprovalPowerAppStatus.Approved:
                    foc.Status = FOCStatus.Approved;
                    break;
                //拒绝
                case ApprovalPowerAppStatus.Rejected:
                    foc.Status = FOCStatus.Rejected;

                    var queryFoc = await LazyServiceProvider.LazyGetService<IFocApplicationRepository>().GetQueryableAsync();
                    var focApplication = queryFoc.Where(a => a.Id == request.FormId).FirstOrDefault();
                    var queryFocDetails = await LazyServiceProvider.LazyGetService<IFocApplicationDetailRepository>().GetQueryableAsync();
                    var queryFocApplicationDetail = queryFocDetails.Where(a => a.ParentID == request.FormId);
                    var focDetailIds = queryFocApplicationDetail.Select(a => a.Id).ToArray();

                    var focDetails = queryFocApplicationDetail.Select(a => new { a.RowId, a.ProductQuantity }).ToArray();
                    var focReturnList = new List<ReturnFocBudgetRequestDto>();
                    var useBudgetRequest = new ReturnFocBudgetRequestDto
                    {
                        FocId = request.FormId,
                        SubbudgetId = focApplication.SubBudgetId.Value,
                        Items = focDetails.Select(a => new FocReturnInfo
                        {
                            PdRowNo = a.RowId,
                            ReturnQty = a.ProductQuantity,
                            ReturnSourceId = focApplication.Id,
                            ReturnSourceCode = focApplication.ApplicationCode
                        })
                    };
                    focReturnList.Add(useBudgetRequest);

                    //退回预算
                    var result = await LazyServiceProvider.LazyGetService<IFocSubbudgetService>().ReturnFocSubbudgetAsync(focReturnList);
                    break;
                //退回
                case ApprovalPowerAppStatus.Denied:
                    foc.Status = FOCStatus.RejectedBack;
                    break;
                default:
                    return MessageResult.FailureResult("FOC申请：通过、拒绝、退回");
            }
            foc.ApprovedDate = DateTime.Now;
            await focRepository.UpdateAsync(foc);
            return MessageResult.SuccessResult();
        }
        /// <summary>
        ///FOC撤回、作废
        /// </summary>
        /// <param name="ids">The ids.</param>
        /// <param name="status">The status.</param>
        /// <returns></returns>
        private async Task<MessageResult> UpdateFOCApplyAsync(List<Guid> ids, ApprovalOperation status)
        {
            if (ids.Count < 1)
            {
                return MessageResult.SuccessResult();
            }

            var FocRepository = LazyServiceProvider.GetService<IFocApplicationRepository>();
            var Focs = await FocRepository.GetListAsync(a => ids.Contains(a.Id));
            if (Focs.Count < 1)
                return MessageResult.FailureResult("未找到对应的FOC申请");
            FOCStatus? approvalStatus = null;
            switch (status)
            {
                //撤回
                case ApprovalOperation.Recall:
                    approvalStatus = FOCStatus.RejectedBack;
                    break;
                //作废
                case ApprovalOperation.Delete:
                    approvalStatus = FOCStatus.ApplicantTerminate;

                    foreach (var formId in ids)
                    {
                        var queryFoc = await LazyServiceProvider.LazyGetService<IFocApplicationRepository>().GetQueryableAsync();
                        var focApplication = queryFoc.Where(a => a.Id == formId).FirstOrDefault();
                        var queryFocDetails = await LazyServiceProvider.LazyGetService<IFocApplicationDetailRepository>().GetQueryableAsync();
                        var queryFocApplicationDetail = queryFocDetails.Where(a => a.ParentID == formId);
                        var focDetailIds = queryFocApplicationDetail.Select(a => a.Id).ToArray();

                        var focDetails = queryFocApplicationDetail.Select(a => new { a.RowId, a.ProductQuantity }).ToArray();
                        var focReturnList = new List<ReturnFocBudgetRequestDto>();
                        var useBudgetRequest = new ReturnFocBudgetRequestDto
                        {
                            FocId = formId,
                            SubbudgetId = focApplication.SubBudgetId.Value,
                            Items = focDetails.Select(a => new FocReturnInfo
                            {
                                PdRowNo = a.RowId,
                                ReturnQty = a.ProductQuantity,
                                ReturnSourceId = focApplication.Id,
                                ReturnSourceCode = focApplication.ApplicationCode
                            })
                        };
                        focReturnList.Add(useBudgetRequest);

                        //退回预算
                        var result = await LazyServiceProvider.LazyGetService<IFocSubbudgetService>().ReturnFocSubbudgetAsync(focReturnList);
                    }
                    break;
                default:
                    return MessageResult.FailureResult("FOC审批状态不是：撤回、退回");
            }
            if (approvalStatus.HasValue)
            {
                Focs.ForEach(a => a.Status = approvalStatus.Value);
            }
            await FocRepository.UpdateManyAsync(Focs);
            return MessageResult.SuccessResult();
        }
        /// <summary>
        /// Foc选择产品发送邮件
        /// </summary>
        /// <param name="info"></param>
        /// <returns></returns>
        async Task SendChooseProductEmail((Guid Id, string ApplyUserName, string WorkflowTypeName, string ApplicationCode, DateTime CompletedTime, Guid deptId) info)
        {
            var webRoot = LazyServiceProvider.LazyGetService<IWebHostEnvironment>().WebRootFileProvider;
            var bodyHtml = await webRoot.GetFileInfo("Templates/Email/FOCChooseProduct.html").ReadAsStringAsync();
            var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();

            var _identityUserRepository = _serviceProvider.GetService<Volo.Abp.Identity.IIdentityUserRepository>();
            var _identityRoleRepository = _serviceProvider.GetService<IIdentityRoleRepository>();
            var role = await _identityRoleRepository.FindByNormalizedNameAsync(RoleNames.FOCSupplyChain, false);

            var userList = await _identityUserRepository.GetListByNormalizedRoleNameAsync(role.NormalizedName, false);

            if (userList.Count == 0) return;
            var commonService = LazyServiceProvider.LazyGetService<ICommonService>();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var emailUsers = userList.Select(s => (s.Id, s.Email)).ToList();
            var sendemailBus = await commonService.GetAuthorizedAllDepts(emailUsers);
            var greeting = string.Empty;
            var configuration = LazyServiceProvider.LazyGetService<IConfiguration>();
            bodyHtml = bodyHtml.Replace("{Applicate}", info.ApplyUserName);
            bodyHtml = bodyHtml.Replace("{ApplicationType}", info.WorkflowTypeName);
            bodyHtml = bodyHtml.Replace("{ApplicationCode}", info.ApplicationCode);
            bodyHtml = bodyHtml.Replace("{TaskPushTime}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
            bodyHtml = bodyHtml.Replace("{Link}", $"{configuration["SpeakerEmail:WebHost"]?.TrimEnd('/')}/foc/productSelect/{info.Id}");
            bodyHtml = bodyHtml.Replace("{Greeting}", "Dear all：");

            var sendemails = sendemailBus.Where(m => m.Value.Contains(info.deptId)).SelectMany(s => s.Key).Distinct().JoinAsString(",");
            if (string.IsNullOrEmpty(sendemails)) return;
            var sendEmaillRecords = new InsertSendEmaillRecordDto
            {
                EmailAddress = sendemails,
                Subject = $"[NexBPM消息中心]由{info.ApplyUserName}发起的【{info.WorkflowTypeName}】需要您处理、进行产品选择",
                Content = bodyHtml,
                SourceType = EmailSourceType.ProductChoose,
                Status = SendStatus.Pending,
                Attempts = 0,
            };

            //记录邮件，并触发邮件发送功能
            var emailService = LazyServiceProvider.LazyGetService<IEmailService>();
            await emailService.InsertSendEmaillRecordAsync([sendEmaillRecords]);
            // 调度作业
            BackgroundJob.Enqueue<SendEmailWorker>(a => a.DoWorkAsync());
        }
        /// <summary>
        /// 退换货，通过、拒绝、退回
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        private async Task<MessageResult> UpdateReturnApplyAsync(ApprovalResultsRequestDto request, WorkflowTask task)
        {
            var reRepository = LazyServiceProvider.GetService<IReturnApplicationReponsitory>();
            var re = await reRepository.GetAsync(request.FormId);
            if (re == null)
                return MessageResult.FailureResult("未找到对应的退换货申请");
            switch (request.ApproveStatus)
            {
                //通过
                case ApprovalPowerAppStatus.Approved:
                    re.Status = ReturnApplicationStatus.Approved;
                    if (re.ApplicationType == ReturnTypeEnum.Return)
                    {
                        var ApprovalAction = LazyServiceProvider.GetService<IPostApprovalActionService>();
                        var returnService = LazyServiceProvider.GetService<IReturnService>();
                        var data = await returnService.PushSoiReturnAsnyc(request.FormId);
                        CreatePostApprovalActionDto actionDto = new CreatePostApprovalActionDto
                        {
                            FormId = request.FormId,
                            FormNo = re.ApplicationCode,
                            ApprovalStatus = request.ApproveStatus,
                            WfInstanceId = task.InstanceId,
                            WfInstanceName = task.Name,
                            WfTypeOption = WorkflowTypeName.ReturnAndExchangeRequest,
                            ProcessingType = ProcessingTypes.ThirdIntegration,
                            RequestContent = data,
                        };
                        await ApprovalAction.CreateActionAsync(actionDto);
                    }

                    break;
                //拒绝
                case ApprovalPowerAppStatus.Rejected:
                    re.Status = ReturnApplicationStatus.Rejected;
                    break;
                //退回
                case ApprovalPowerAppStatus.Denied:
                    re.Status = ReturnApplicationStatus.RejectedBack;
                    break;
                default:
                    return MessageResult.FailureResult("退换货申请：通过、拒绝、退回");
            }
            //foc.ApprovedDate = DateTime.Now;
            await reRepository.UpdateAsync(re);
            return MessageResult.SuccessResult();
        }
        /// <summary>
        ///退换货撤回、作废
        /// </summary>
        /// <param name="ids">The ids.</param>
        /// <param name="status">The status.</param>
        /// <returns></returns>
        private async Task<MessageResult> UpdateReturnApplyAsync(List<Guid> ids, ApprovalOperation status)
        {
            if (ids.Count < 1)
            {
                return MessageResult.SuccessResult();
            }

            var reRepository = LazyServiceProvider.GetService<IReturnApplicationReponsitory>();
            var re = await reRepository.GetListAsync(a => ids.Contains(a.Id));
            if (re.Count < 1)
                return MessageResult.FailureResult("未找到对应的退换货申请");
            ReturnApplicationStatus? approvalStatus = null;
            switch (status)
            {
                //撤回
                case ApprovalOperation.Recall:
                    approvalStatus = ReturnApplicationStatus.RejectedBack;
                    break;
                //作废
                case ApprovalOperation.Delete:
                    approvalStatus = ReturnApplicationStatus.ApplicantTerminate;
                    break;
                default:
                    return MessageResult.FailureResult("退换货审批状态不是：撤回、退回");
            }
            if (approvalStatus.HasValue)
            {
                re.ForEach(a => a.Status = approvalStatus.Value);
            }
            await reRepository.UpdateManyAsync(re);
            return MessageResult.SuccessResult();
        }
    }
}
