﻿using System.Collections.Generic;
using System.Text.Json.Serialization;
using Abbott.SpeakerPortal.Enums;

namespace Abbott.SpeakerPortal.Contracts.Budget.FocSubbudget
{
    public class AdjustmentFocSubBudgetMessageDto : MothlyQtyDto
    {
        /// <summary>
        /// 序号
        /// </summary>
        public int No { get; set; }
        /// <summary>
        ///子预算编号
        /// </summary>
        public string Code { get; set; }
        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
        /// <summary>
        /// 错误信息
        /// </summary>
        public string Message { get; set; }
        [JsonIgnore]
        public List<FocMonthlyBudgetNullAbleDto> MonthlyBudgets
        {
            get
            {
                return new List<FocMonthlyBudgetNullAbleDto>()
                {
                    new FocMonthlyBudgetNullAbleDto{ BudgetQty=JanQty,Status=JanStatus,Month=Month.Jan},
                    new FocMonthlyBudgetNullAbleDto{ BudgetQty=FebQty,Status=FebStatus,Month=Month.Feb},
                    new FocMonthlyBudgetNullAbleDto{ BudgetQty=MarQty,Status=MarStatus,Month=Month.Mar},
                    new FocMonthlyBudgetNullAbleDto{ BudgetQty=AprQty,Status=AprStatus,Month=Month.Apr},
                    new FocMonthlyBudgetNullAbleDto{ BudgetQty=MayQty,Status=MayStatus,Month=Month.May},
                    new FocMonthlyBudgetNullAbleDto{ BudgetQty=JunQty,Status=JunStatus,Month=Month.Jun},
                    new FocMonthlyBudgetNullAbleDto{ BudgetQty=JulQty,Status=JulStatus,Month=Month.Jul},
                    new FocMonthlyBudgetNullAbleDto{ BudgetQty=AugQty,Status=AugStatus,Month=Month.Aug},
                    new FocMonthlyBudgetNullAbleDto{ BudgetQty=SeptQty,Status=SeptStatus,Month=Month.Sept},
                    new FocMonthlyBudgetNullAbleDto{ BudgetQty=OctQty,Status=OctStatus,Month=Month.Oct},
                    new FocMonthlyBudgetNullAbleDto{ BudgetQty=NovQty,Status=NovStatus,Month=Month.Nov},
                    new FocMonthlyBudgetNullAbleDto{ BudgetQty=DecQty,Status=DecStatus,Month=Month.Dec},
                };

            }
        }
    }
}
