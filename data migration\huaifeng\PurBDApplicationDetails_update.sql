--drop table  #PurBDApplicationDetails
SELECT
 TRY_CONVERT(UNIQUEIDENTIFIER, [Id]) [Id]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([BDApplicationId],'00000000-0000-0000-0000-000000000000')) [BDApplicationId]
,TRY_CONVERT(UNIQUEIDENTIFIER, [PRDetailId]) [PRDetailId]
,[Content]
,iif(Quantity =null,0,Quantity) AS [Quantity]
,[Unit]
,iif(TotalAmount =null,0,TotalAmount) AS [TotalAmount]
,[ApplicationCode]
,case when RowNo is null or RowNo ='' or RowNo='NULL' then 0 
else RowNo end AS [RowNo]
,'{}' AS [ExtraProperties]
,'NULL' AS [ConcurrencyStamp]
,case when CreationTime is null or CreationTime ='' or CreationTime='NULL' then GETDATE() 
else CreationTime end  AS [CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CreatorId]) [CreatorId]
,[LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [LastModifierId])[LastModifierId]
,0 AS [IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, [DeleterId]) [DeleterId]
,[DeletionTime]
,iif(UnitPrice =null,0,UnitPrice)  AS [UnitPrice]
INTO #PurBDApplicationDetails
FROM (SELECT DISTINCT *,ROW_NUMBER() OVER(PARTITION BY Id ORDER BY Id) RK FROM PLATFORM_ABBOTT_STG.dbo.PurBDApplicationDetails)a
WHERE RK = 1
--where LastModificationTime = ''



select * from PLATFORM_ABBOTT_STG.dbo.PurBDApplicationDetails

USE Speaker_Portal_STG;

UPDATE a
SET 
 a.[Id] = b.[Id]
,a.[BDApplicationId] = b.[BDApplicationId]
,a.[PRDetailId] = b.[PRDetailId]
,a.[Content] = b.[Content]
,a.[Quantity] = b.[Quantity]
,a.[Unit] = b.[Unit]
,a.[TotalAmount] = b.[TotalAmount]
,a.[ApplicationCode] = b.[ApplicationCode]
,a.[RowNo] = b.[RowNo]
,a.[ExtraProperties] = b.[ExtraProperties]
,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = b.[CreatorId]
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = b.[LastModifierId]
,a.[IsDeleted] = b.[IsDeleted]
,a.[DeleterId] = b.[DeleterId]
,a.[DeletionTime] = b.[DeletionTime]
,a.[UnitPrice] = b.[UnitPrice]
FROM dbo.PurBDApplicationDetails a
left join #PurBDApplicationDetails  b
ON a.id=b.id

--select * from #PurBDApplicationDetails where BDApplicationId is null


INSERT INTO dbo.PurBDApplicationDetails
SELECT
 [Id]
,[BDApplicationId]
,[PRDetailId]
,[Content]
,[Quantity]
,[Unit]
,[TotalAmount]
,[ApplicationCode]
,[RowNo]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[UnitPrice]
FROM #PurBDApplicationDetails a
WHERE not exists (select * from dbo.PurBDApplicationDetails where id=a.id)


--重复值
--select * from #PurBDApplicationDetails where Id = '42944500-05c1-42d0-85ee-b158c943efa8'

--truncate table dbo.PurBDApplicationDetails