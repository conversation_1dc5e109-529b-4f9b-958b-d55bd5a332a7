
select 
TRY_CONVERT(UNIQUE<PERSON>ENTIFIER, [Id]) [Id]
,ApplicationCode
,CssCode
,ApplicationType
,Status
,ApplyTime
,TRY_CONVERT(UNIQUEIDENTIFIER, ApplyUserId) [ApplyUserId]
,Apply<PERSON><PERSON>
,TRY_CONVERT(UNIQUEIDENTIFIER, ApplyUserDeptId) [ApplyUserDeptId]
,ApplyUserDeptName
,TRY_CONVERT(UNIQUEIDENTIFIER, CostCenterId) [CostCenterId]
,CostCenterCode
,CostCenter
,TRY_CONVERT(UNIQUEIDENTIFIER, ApplyUserBuId) [ApplyUserBuId]
,ApplyUserBuName
,TRY_CONVERT(UNIQUEIDENTIFIER, BudgetId) [BudgetId]
,BudgetCode
,TRY_CONVERT(UNIQUEIDENTIFIER, SubBudgetId) [SubBudgetId]
,SubBudgetCode
,TRY_CONVERT(UNIQUE<PERSON>ENTIFIER, SubBudgetRegionId) [SubBudgetRegionId]
,SubBudgetRegion
,TRY_CONVERT(UNIQUEIDENTIFIER, BudgetOwnerId) [BudgetOwnerId]
,BudgetOwner
,BudgetFile
,BudgetUsedAmount
,BudgetAvailableAmount
,BudgetAmount
,TotalAmountRMB
,TRY_CONVERT(UNIQUEIDENTIFIER, ExpenseTypeId) [ExpenseTypeId]
,ExpenseTypeName
,TRY_CONVERT(UNIQUEIDENTIFIER, ProductId) [ProductId]
,ProductName
,TRY_CONVERT(UNIQUEIDENTIFIER, CompanyId) [CompanyId]
,CompanyCode
,CompanyName
,TRY_CONVERT(UNIQUEIDENTIFIER,ISNULL(cast([ClientId] as nvarchar(50)),'00000000-0000-0000-0000-000000000000')) [ClientId]
,ClientCode
,ClientName
,TRY_CONVERT(UNIQUEIDENTIFIER,ISNULL(cast([ClientTypeId] as nvarchar(50)),'00000000-0000-0000-0000-000000000000')) [ClientTypeId]
,ClientType
,Attachment
,Remark
, TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL(cast([TransfereeId] as nvarchar(50)),'00000000-0000-0000-0000-000000000000')) [TransfereeId]
,TransfereeName
,DataSource
,CreationTime
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL(cast([CreatorId] as nvarchar(50)),'00000000-0000-0000-0000-000000000000')) [CreatorId]
,LastModificationTime
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL(cast([LastModifierId] as nvarchar(50)),'00000000-0000-0000-0000-000000000000')) [LastModifierId]
,IsDeleted
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL(cast([DeleterId] as nvarchar(50)),'00000000-0000-0000-0000-000000000000')) DeleterId 
,DeletionTime
,CityId
,CityCode
,CityName
,Content
,SettlementPeriodStart
,SettlementPeriodEnd
,ProductCode
,SubBudgetDesc
,ApplyUserBUCode
--into #STicketApplications
from   PLATFORM_ABBOTT_Dev.dbo.STicketApplications

--drop table #STicketApplications

select Attachment ,* from  PLATFORM_ABBOTT_Dev.dbo.STicketApplications


USE Speaker_Portal_Dev;
ALTER TABLE  dbo.STicketApplications ALTER COLUMN Attachment nvarchar(1000) COLLATE SQL_Latin1_General_CP1_CI_AS NULL;

--delete from dbo.STicketApplications where Attachment='todo'

INSERT INTO dbo.STicketApplications
(id
,ApplicationCode
,CssCode
,ApplicationType
,Status
,ApplyTime
,ApplyUserId
,ApplyUser
,ApplyUserDeptId
,ApplyUserDeptName
,CostCenterId
,CostCenterCode
,CostCenter
,ApplyUserBuId
,ApplyUserBuName
,BudgetId
,BudgetCode
,SubBudgetId
,SubBudgetCode
,SubBudgetRegionId
,SubBudgetRegion
,BudgetOwnerId
,BudgetOwner
,BudgetFile
,BudgetUsedAmount
,BudgetAvailableAmount
,BudgetAmount
,TotalAmountRMB
,ExpenseTypeId
,ExpenseTypeName
,ProductId
,ProductName
,CompanyId
,CompanyCode
,CompanyName
,ClientId
,ClientCode
,ClientName
,ClientTypeId
,ClientType
,Attachment
,Remark
,TransfereeId
,TransfereeName
,DataSource
,CreationTime
,CreatorId
,LastModificationTime
,LastModifierId
,IsDeleted
,DeleterId
,DeletionTime
,CityId
,CityCode
,CityName
,Content
,SettlementPeriodStart
,SettlementPeriodEnd
,ProductCode
,SubBudgetDesc
,ApplyUserBUCode
)
SELECT
 id
,ApplicationCode
,CssCode
,ApplicationType
,Status
,ApplyTime
,ApplyUserId
,ApplyUser
,ApplyUserDeptId
,ApplyUserDeptName
,CostCenterId
,CostCenterCode
,CostCenter
,ApplyUserBuId
,ApplyUserBuName
,BudgetId
,BudgetCode
,SubBudgetId
,SubBudgetCode
,SubBudgetRegionId
,SubBudgetRegion
,BudgetOwnerId
,BudgetOwner
,BudgetFile
,BudgetUsedAmount
,BudgetAvailableAmount
,BudgetAmount
,TotalAmountRMB
,ISNULL([ExpenseTypeId],'00000000-0000-0000-0000-000000000000') ExpenseTypeId
,ExpenseTypeName
, ISNULL([ProductId],'00000000-0000-0000-0000-000000000000') ProductId
,ProductName
,CompanyId
,CompanyCode
,CompanyName
,ISNULL([ClientId],'00000000-0000-0000-0000-000000000000') ClientId
,ClientCode
,ClientName
,ISNULL([ClientTypeId],'00000000-0000-0000-0000-000000000000') ClientTypeId
,ClientType
,Attachment
,Remark
,TransfereeId
,TransfereeName
,DataSource
,'1900-01-01' as CreationTime
,CreatorId
,null LastModificationTime
,LastModifierId
,IsDeleted
,DeleterId
,null DeletionTime
,CityId
,CityCode
,CityName
,Content
,SettlementPeriodStart
,SettlementPeriodEnd
,ProductCode
,SubBudgetDesc
,ApplyUserBUCode
FROM #STicketApplications a
WHERE not exists (select * from dbo.STicketApplications where id=a.id)


--drop table #STicketApplications

