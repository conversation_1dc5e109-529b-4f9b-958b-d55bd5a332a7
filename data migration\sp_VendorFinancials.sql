CREATE PROCEDURE dbo.sp_VendorFinancials
AS 
BEGIN
	
with 
FVM_INFO as (
select *,LTRIM(RTRIM(
					 REPLACE(REPLACE( REPLACE(
									REPLACE(
										      LEFT(a.VLDRM2,
										   CASE
											                    WHEN CHARINDEX('SwiftCode', a.VLDRM2) > 0 THEN CHARINDEX('SwiftCode', a.VLDRM2) - 1
										                        WHEN CHARINDEX('Swift Code', a.VLDRM2) > 0 THEN CHARINDEX('Swift Code', a.VLDRM2) - 1
										                        WHEN CHARINDEX('Swift', a.VLDRM2) > 0 THEN CHARINDEX('Swift', a.VLDRM2) - 1
										                        ELSE LEN(a.VLDRM2)
										                    END),
													      '(', ''), -- Remove left parenthesis
													  ')', '')  -- Remove right parenthesis
												,' ',''),'ACCOUNT:',''))
							) BankCardNo from PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM a
),
Time_info as (
				SELECT  
					a1.*,
					ROW_NUMBER () over(PARTITION by Trim(NAME),Trim(VMXCRT_NO1) order by vnstat1 desc,
					COALESCE(nullif(vldate1,0),vcrdte1) desc,vcrdte1 desc, COALESCE(nullif(VLTIME1,0),VCTIME)  desc,VENDOR desc ) as rn,
					FIRST_value (VMXCRT_NO1) over(PARTITION by Trim(NAME),Trim(VMXCRT_NO1) order by vnstat1 desc,
					COALESCE(nullif(vldate1,0),vcrdte1) desc,vcrdte1 desc, COALESCE(nullif(VLTIME1,0),VCTIME)  desc,VENDOR desc ) as identity_id, 
					min(CONCAT(vcrdte1,VCTIME1))over(PARTITION by VMXCRT_NO1 ) as CreationTime,
					min(vcrdte1)over(PARTITION by VMXCRT_NO1) as vcrdte1_Min,
					max(CONCAT(vldate1,VLTIME1))over(PARTITION by VMXCRT_NO1 ) as vcrdte1_MAX,
--					min(VCTIME)over(PARTITION by VMXCRT_NO1) as VCTIME_Min,
					FIRST_value(VCUSER)over(PARTITION by VMXCRT_NO1 order by vcrdte1,VCTIME  ) as VCUSER1,  -- 最早用户id
					FIRST_value(VCUSER)over(PARTITION by VMXCRT_NO1 order by vcrdte1 desc,VCTIME desc  ) as VCUSER2
				from
				(
					SELECT a.*,b.VLDRM1,b.VLDRM2,b.BankCardNo,b.VLDATE,b.VLTIME,b.VEXTNM,b.VCRDTE,b.VCTIME ,VCUSER, VMBNKA as BankNo,b.VADD1,b.VADD2,b.VADD3,
						CASE WHEN len(cast(vldate as VARCHAR(255)))<=6 and vldate <> 0 
							 then cast(CONCAT('20',vldate) as int) 
							 WHEN vldate=******** AND VLTIME=173000 AND VLUSER='CNSECOFR1' --[LastModificationTime] 在********系统故障，时间错误，为空
							 THEN 0
							 else vldate end vldate1,
						CASE WHEN len(cast(VCTIME as VARCHAR(255)))<=6 and VCTIME <> 0 
							 then cast(CONCAT('0',VCTIME) as nvarchar(10)) 
							 else VCTIME end VCTIME1,
							 case when vldate=******** AND VLTIME=173000 AND VLUSER='CNSECOFR1' --[LastModificationTime] 在********系统故障，时间错误，为空
							 THEN 0 else VLTIME end VLTIME1,
						CASE WHEN len(cast(VCRDTE as VARCHAR(255)))<=6 and VCRDTE <> 0 
						then cast(CONCAT('20',VCRDTE) as int) 
						else VCRDTE end vcrdte1,
					 	case 
							when upper(trim(VTYPE)) in ('NHIV','NLIV') and VMXCRT  <>''   then  TRIM(REPLACE( REPLACE(VMXCRT,' F','') ,' M',''))   --判断个人供应商
							when upper(trim(VTYPE)) in ('NT') and (VMXCRT is null or VMXCRT  ='' ) AND  BankCardNo  IS NOT NULL THEN BankCardNo
							when upper(trim(VTYPE)) in ('NT') and (BankCardNo is null or BankCardNo  ='')  AND  VMXCRT  IS NOT NULL THEN VMXCRT
							when upper(trim(VTYPE)) in ('NT') and BankCardNo  IS NOT NULL  AND  VMXCRT  IS NOT NULL THEN VMXCRT
						    when VMXCRT is null or VMXCRT  =''  or BankCardNo='' or BankCardNo is null then cast(newid() as nvarchar(255))  
						else  TRIM(BankCardNo)
						end AS VMXCRT_NO1,
						case when upper(vnstat)='A' THEN 1 else 0 end vnstat1,
						COALESCE(a.VNDNAM,b.VEXTNM) as NAME
					from PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a 
					join FVM_INFO b 
						on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
					where [VCMPNY] in (91,79,18,20)  AND trim(VTYPE) in ('NHIV','NLIV','NH','NL','NT')
--							 and TRIM(REPLACE( REPLACE(VMXCRT,' F','') ,' M',''))  in ('130702197011050348'
--								,'652301196912100825',
--								'370283197912077527',
--								'340264197404083221',
--								'650104196202209005',
--								'110108196102113000'
--								)
				) a1 
)
select
NEWID() AS Id,--自动生成的uuid
max(vt.[ID])                    over(partition by Trim(COALESCE(a.VNDNAM,b.VEXTNM)),ti.identity_id) as  VendorId,
A.VCMPNY AS Company,--
A.VCURR AS Currency,--
A.VENDOR AS VendorCode,--
A.VMBANK AS AbbottBank,--
A.VTYPE AS VendorType,--
A.VMREF1 AS Division,--
A.VPAYTY AS PayType,--
A.VCOUN AS CountryCode,--
B.VLDCD1 AS BankType,--
B.VPALS2 AS DpoCategory,--根据填入的值与DPO Category字典匹配后填入字典编码
a.VTERMS AS PaymentTerm,--
'' AS BankNo,--留空
'{}' AS ExtraProperties,--默认填写为"{}"
'' AS ConcurrencyStamp,--?
max(vt.[CreationTime])  		over(partition by Trim(COALESCE(a.VNDNAM,b.VEXTNM)),ti.identity_id) as CreationTime,
max(vt.[CreatorId]) 			over(partition by Trim(COALESCE(a.VNDNAM,b.VEXTNM)),ti.identity_id) as CreatorId,
max(vt.[LastModificationTime])  over(partition by Trim(COALESCE(a.VNDNAM,b.VEXTNM)),ti.identity_id) as LastModificationTime,
max(vt.[LastModifierId])        over(partition by Trim(COALESCE(a.VNDNAM,b.VEXTNM)),ti.identity_id) as LastModifierId,
'0' AS IsDeleted,--默认为0
'' AS DeleterId,--默认为空
'' AS DeletionTime,--默认为空
b.VPALS3 AS SpendingCategory,--此处填写的值就是字典编码，不需要额外匹配(但如果识别到了不存在于字典的值可能需要加入字典或前端展示时直接展示为code)
CASE WHEN len(cast(b.VCTIME as VARCHAR(255)))<=5 and b.VCTIME <> 0
							 then cast(CONCAT(ti.vcrdte1,'0',b.VCTIME) as nvarchar(16))
							 else CONCAT(ti.vcrdte1,b.VCTIME) end BpcsCreationTime,
a.VMID AS BpcsVmid,--
a.VNSTAT AS BpcsVnstat,
case when a.VNSTAT='A' then 3 when a.VNSTAT='D' then 4 end  as FinancialVendorStatus,a.VMXCRT
into #VendorFinancials_tmp
from PLATFORM_ABBOTT.dbo.ODS_BPCS_AVM a
left join PLATFORM_ABBOTT.dbo.ODS_BPCS_PMFVM b
on a.VCMPNY=b.VMCMPY and a.VENDOR=b.VNDERX
join Time_info ti
on a.VCMPNY=ti.VCMPNY and a.VENDOR=ti.VENDOR
join FVM_INFO fi
on  a.VCMPNY=fi.VMCMPY and a.VENDOR=fi.VNDERX
--join PLATFORM_ABBOTT.dbo.Vendor_Tmp vt
full join
	Vendor_Tmp vt
	on a.vendor= vt.VENDOR  and vt.VCMPNY =a.VCMPNY
where a.VCMPNY in ('91','79','18','20') and trim(a.VTYPE) in ('NHIV','NLIV','NH','NL','NT') ;


--drop table #VendorFinancials_tmp

--落成实体表
 IF OBJECT_ID(N'PLATFORM_ABBOTT.dbo.VendorFinancials_tmp', N'U') IS NOT NULL
BEGIN
	
	update a 
	set a.VendorId             = b.VendorId
       ,a.Company              = b.Company
       ,a.Currency             = b.Currency
       ,a.VendorCode           = b.VendorCode
       ,a.AbbottBank           = b.AbbottBank
       ,a.VendorType           = b.VendorType
       ,a.Division             = b.Division
       ,a.PayType              = b.PayType
       ,a.CountryCode          = b.CountryCode
       ,a.BankType             = b.BankType
       ,a.DpoCategory          = b.DpoCategory
       ,a.PaymentTerm          = b.PaymentTerm
       ,a.BankNo               = b.BankNo
       ,a.ExtraProperties      = b.ExtraProperties
       ,a.ConcurrencyStamp     = b.ConcurrencyStamp
       ,a.CreationTime         = b.CreationTime
       ,a.CreatorId            = b.CreatorId
       ,a.LastModificationTime = b.LastModificationTime
       ,a.LastModifierId       = b.LastModifierId
       ,a.IsDeleted            = b.IsDeleted
       ,a.DeleterId            = b.DeleterId
       ,a.DeletionTime         = b.DeletionTime
       ,a.SpendingCategory     = b.SpendingCategory
       ,a.BpcsCreationTime     = b.BpcsCreationTime
       ,a.BpcsVmid             = b.BpcsVmid
       ,a.BpcsVnstat           = b.BpcsVnstat
       ,a.FinancialVendorStatus=b.FinancialVendorStatus
    from PLATFORM_ABBOTT.dbo.VendorFinancials_tmp a
    left join #VendorFinancials_tmp b on a.VendorCode = b.VendorCode and a.Company = b.Company
    
    insert into PLATFORM_ABBOTT.dbo.VendorFinancials_tmp 
    select a.Id
          ,a.VendorId
          ,a.Company
          ,a.Currency
          ,a.VendorCode
          ,a.AbbottBank
          ,a.VendorType
          ,a.Division
          ,a.PayType
          ,a.CountryCode
          ,a.BankType
          ,a.DpoCategory
          ,a.PaymentTerm
          ,a.BankNo
          ,a.ExtraProperties
          ,a.ConcurrencyStamp
          ,a.CreationTime
          ,a.CreatorId
          ,a.LastModificationTime
          ,a.LastModifierId
          ,a.IsDeleted
          ,a.DeleterId
          ,a.DeletionTime
          ,a.SpendingCategory
          ,a.BpcsCreationTime
          ,a.BpcsVmid
          ,a.BpcsVnstat
          ,FinancialVendorStatus
    from #VendorFinancials_tmp a
    where not exists (select * from PLATFORM_ABBOTT.dbo.VendorFinancials_tmp where a.VendorCode = VendorCode and a.Company = Company)
	PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into PLATFORM_ABBOTT.dbo.VendorFinancials_tmp from #VendorFinancials_tmp
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END

end