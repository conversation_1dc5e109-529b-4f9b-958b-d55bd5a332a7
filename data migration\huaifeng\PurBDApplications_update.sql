SELECT
 TRY_CONVERT(UNIQUEIDENTIFIER, [Id]) [Id]
,ISNULL([ApplicationCode],'00000000-0000-0000-0000-000000000000') AS [ApplicationCode]
,[PRApplicationDetailId]
,0 AS [Status]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([ApplyUserId],'00000000-0000-0000-0000-000000000000')) [ApplyUserId]
,[ApplyUserName]
,[ApplyTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([ApplyUserBu],'00000000-0000-0000-0000-000000000000')) [ApplyUserBu]
,[ApplyUserBuName]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([BudgetManagerUserId],'00000000-0000-0000-0000-000000000000')) [BudgetManagerUserId]
,[BudgetManagerUserName]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([VendorId],'00000000-0000-0000-0000-000000000000')) [VendorId]
,[VendorName]
,[Currency]
,0 AS [SingleChoice]
,TRY_CONVERT(UNIQUEIDENTIFIER, [BDApplicationId]) [BDApplicationId]
,[BDApplicationCode]
,0 AS [PRCorrespond]
,[Explain]
,[OtherSupplierExplain]
,[AttachmentFile]
,'{}' AS [ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CreatorId]) [CreatorId]
,[LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [LastModifierId]) [LastModifierId]
,0 AS [IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, [DeleterId]) [DeleterId]
,[DeletionTime]
,0 AS [TotalAmount]
,[VendorCode]
,NULL AS [CurrencySymbol]
,0 AS [Rate]
,TRY_CONVERT(UNIQUEIDENTIFIER,'00000000-0000-0000-0000-000000000000') AS [PRApplicationId]
,TRY_CONVERT(UNIQUEIDENTIFIER,'00000000-0000-0000-0000-000000000000') AS [TransfereeId]
,NULL AS [TransfereeName]
INTO #PurBDApplications
FROM (SELECT DISTINCT *,ROW_NUMBER() OVER(PARTITION BY Id ORDER BY Id) RK FROM PLATFORM_ABBOTT_Stg.dbo.PurBDApplications)a
WHERE RK = 1



USE Speaker_Portal_Stg;

UPDATE a
SET 
 a.[Id] = b.[Id]
,a.[ApplicationCode] = b.[ApplicationCode]
,a.[PRApplicationDetailId] = b.[PRApplicationDetailId]
,a.[Status] = b.[Status]
,a.[ApplyUserId] = b.[ApplyUserId]
,a.[ApplyUserName] = b.[ApplyUserName]
,a.[ApplyTime] = b.[ApplyTime]
,a.[ApplyUserBu] = b.[ApplyUserBu]
,a.[ApplyUserBuName] = b.[ApplyUserBuName]
,a.[BudgetManagerUserId] = b.[BudgetManagerUserId]
,a.[BudgetManagerUserName] = b.[BudgetManagerUserName]
,a.[VendorId] = b.[VendorId]
,a.[VendorName] = b.[VendorName]
,a.[Currency] = b.[Currency]
,a.[SingleChoice] = b.[SingleChoice]
,a.[BDApplicationId] = b.[BDApplicationId]
,a.[BDApplicationCode] = b.[BDApplicationCode]
,a.[PRCorrespond] = b.[PRCorrespond]
,a.[Explain] = b.[Explain]
,a.[OtherSupplierExplain] = b.[OtherSupplierExplain]
,a.[AttachmentFile] = b.[AttachmentFile]
,a.[ExtraProperties] = b.[ExtraProperties]
,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = b.[CreatorId]
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = b.[LastModifierId]
,a.[IsDeleted] = b.[IsDeleted]
,a.[DeleterId] = b.[DeleterId]
,a.[DeletionTime] = b.[DeletionTime]
,a.[TotalAmount] = b.[TotalAmount]
,a.[VendorCode] = b.[VendorCode]
,a.[CurrencySymbol] = b.[CurrencySymbol]
,a.[Rate] = b.[Rate]
,a.[PRApplicationId] = b.[PRApplicationId]
,a.[TransfereeId] = b.[TransfereeId]
,a.[TransfereeName] = b.[TransfereeName]
FROM dbo.PurBDApplications a
left join #PurBDApplications  b
ON a.id=b.id


--select * from #PurBDApplications where ApplyUserBu is null


INSERT INTO dbo.PurBDApplications
(
 [Id]
,[ApplicationCode]
,[PRApplicationDetailId]
,[Status]
,[ApplyUserId]
,[ApplyUserName]
,[ApplyTime]
,[ApplyUserBu]
,[ApplyUserBuName]
,[BudgetManagerUserId]
,[BudgetManagerUserName]
,[VendorId]
,[VendorName]
,[Currency]
,[SingleChoice]
,[BDApplicationId]
,[BDApplicationCode]
,[PRCorrespond]
,[Explain]
,[OtherSupplierExplain]
,[AttachmentFile]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[TotalAmount]
,[VendorCode]
,[CurrencySymbol]
,[Rate]
,[PRApplicationId]
,[TransfereeId]
,[TransfereeName]
)
SELECT
 [Id]
,[ApplicationCode]
,[PRApplicationDetailId]
,[Status]
,[ApplyUserId]
,[ApplyUserName]
,[ApplyTime]
,[ApplyUserBu]
,[ApplyUserBuName]
,[BudgetManagerUserId]
,[BudgetManagerUserName]
,[VendorId]
,[VendorName]
,[Currency]
,[SingleChoice]
,[BDApplicationId]
,[BDApplicationCode]
,[PRCorrespond]
,[Explain]
,[OtherSupplierExplain]
,[AttachmentFile]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[TotalAmount]
,[VendorCode]
,[CurrencySymbol]
,[Rate]
,[PRApplicationId]
,[TransfereeId]
,[TransfereeName]
FROM #PurBDApplications a
WHERE not exists (select * from dbo.PurBDApplications where id=a.id)


--truncate table dbo.PurBDApplications
