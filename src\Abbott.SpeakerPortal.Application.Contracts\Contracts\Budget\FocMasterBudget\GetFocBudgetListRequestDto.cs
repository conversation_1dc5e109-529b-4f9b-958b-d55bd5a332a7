﻿using System;
using Abbott.SpeakerPortal.Dtos;

namespace Abbott.SpeakerPortal.Contracts.Budget.FocMasterBudget
{
    public class GetFocBudgetListRequestDto : PagedDto
    {
        /// <summary>
        /// 年度
        /// </summary>
        public ushort? Year { get; set; }
        /// <summary>
        /// buId
        /// </summary>
        public Guid? BuId { get; set; }
        /// <summary>
        /// 预算编号
        /// </summary>
        public string Code { get; set; }
        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }
        /// <summary>
        /// 责任人Id
        /// </summary>
        public Guid? OwnerId { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public bool? Status { get; set; }
    }
}
