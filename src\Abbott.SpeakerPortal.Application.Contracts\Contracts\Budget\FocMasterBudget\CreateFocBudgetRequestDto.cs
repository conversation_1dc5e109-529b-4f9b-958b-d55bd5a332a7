﻿using System;
using System.ComponentModel.DataAnnotations;

namespace Abbott.SpeakerPortal.Contracts.Budget.FocMasterBudget
{
    public class CreateFocBudgetRequestDto
    {
        /// <summary>
        /// 年度
        /// </summary>
        [Required(ErrorMessage = "年度必填")]
        public int Year { get; set; }

        /// <summary>
        /// bu
        /// </summary>
        [Required(ErrorMessage = "请填写Bu")]
        public Guid BuId { get; set; }

        /// <summary>
        /// 主预算数量
        /// </summary>
        [Required(ErrorMessage = "请填写主预算数量")]
        [Range(0, 999999999, ErrorMessage = "预算数量不能小于0")]
        public int BudgetQty { get; set; }

        /// <summary>
        /// 负责人Id
        /// </summary>
        [Required(ErrorMessage = "请填写负责人")]
        public Guid OwnerId { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        [Required(ErrorMessage = "请填写描述")]
        public string Description { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; }
    }
}
