﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;

namespace Abbott.SpeakerPortal.Enums
{
    /// <summary>
    /// 邮件来源类型
    /// </summary>
    public enum EmailSourceType
    {
        [Description("单据拦截")]
        OECIntercept = 1,

        /// <summary>
        /// Hcp完善信息
        /// </summary>
        [Description("Hcp完善信息")]
        HcpCompleteInfo = 2,

        /// <summary>
        /// Non Hcp完善信息
        /// </summary>
        [Description("Non Hcp完善信息")]
        NonHcpCompleteInfo = 3,

        /// <summary>
        /// 审批通知申请人
        /// </summary>
        [Description("审批通知申请人")]
        ApprovalNotifyApplicant = 4,

        /// <summary>
        /// PA退回通知申请人
        /// </summary>
        [Description("退回通知申请人")]
        ReturnNoticeToApplicant = 5,

        /// <summary>
        /// PA供应商失效通知申请人
        /// </summary>
        [Description("供应商失效通知申请人")]
        VendorExpireNoticeToApplican = 6,

        /// <summary>
        /// 申请退回
        /// </summary>
        [Description("申请退回")]
        ApplicationReturned = 10,

        /// <summary>
        /// 申请拒绝
        /// </summary>
        [Description("申请拒绝")]
        ApplicationRejected = 11,

        /// <summary>
        /// 申请通过
        /// </summary>
        [Description("申请通过")]
        ApplicationApproved = 12,

        /// <summary>
        /// 监控ScheduleJobLog
        /// </summary>
        [Description("监控ScheduleJobLog")]
        MonitorSyncLog = 400,

        /// <summary>
        /// 申请加入活动
        /// </summary>
        [Description("申请加入活动")]
        ApplyToJoinActivity = 20,
        /// <summary>
        /// 申请加入活动
        /// </summary>
        [Description("客户信息")]
        CustomerInfo = 30,
        /// <summary>
        /// Veeva 推送超时邮件
        /// </summary>
        [Description("Veeva推送超时")]
        VeevaPushEmail = 40,

        /// <summary>
        /// EPD在线会议推送失败
        /// </summary>
        [Description("EPD在线会议推送失败")]
        EPDOmAddMeetingFail = 41,
        /// <summary>
        /// 产品选择
        /// </summary>
        [Description("FOC")]
        ProductChoose = 50,

        /// <summary>
        /// FOC发货
        /// </summary>
        [Description("FOC")]
        Shipments = 51,
        /// <summary>
        /// 核销单
        /// </summary>
        [Description("核销单")]
        STicketShipments = 52,
        /// <summary>
        /// foc结算
        /// </summary>
        [Description("FOC")]
        FOCSettlement = 53
    }

    /// <summary>
    /// 发送状态
    /// </summary>
    public enum SendStatus
    {
        /// <summary>
        /// 待发送
        /// </summary>
        [Description("待发送")]
        Pending = 1,

        /// <summary>
        /// 发送成功
        /// </summary>
        [Description("成功")]
        Success = 2,

        /// <summary>
        /// 发送失败
        /// </summary>
        [Description("失败")]
        Failed = 3
    }
}
