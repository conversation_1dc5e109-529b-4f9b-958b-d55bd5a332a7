CREATE PROCEDURE dbo.sp_PurPAFinancialVoucherInfos_ns
AS 
BEGIN
	select 
a.[Id]
,ppt.Id as [PAId]
,a.[VendorName]
,a.[InvoiceReference]
,a.[InvoiceLineAmount]
,a.[InvoiceDate]
,a.[InvoiceDescription]
,a.[InvoiceReceiptReference]
,a.[GeneralLedgerDate]
,a.[ReasonCode]
,a.[PayType]
,a.[Bank]
,a.[InvoiceReceiptDate]
,a.[CurrencyCode]
,a.[RecognitionRate]
,a.[CompanyCode]
,a.[DivisionCode]
,a.[CostCenter]
,a.[NatureAccount]
,a.[SubAccount]
,a.[Location]
,a.[Currency]
,a.[VendorTaxCode]
,a.[User]
into #PurPAFinancialVoucherInfos
from PurPAFinancialVoucherInfos_tmp a   --694826
left join (select *,ROW_NUMBER () over(PARTITION by ApplicationCode order by ApplicationCode) rn from PLATFORM_ABBOTT_STG.dbo.PurPAApplications_tmp ) ppt 
on a.PAId =ppt.ApplicationCode  and  ppt.rn = 1     --694834, ApplicationCode重复


IF OBJECT_ID(N'PLATFORM_ABBOTT_STG.dbo.PurPAFinancialVoucherInfos', N'U') IS NOT NULL
BEGIN
	drop table PLATFORM_ABBOTT_STG.dbo.PurPAFinancialVoucherInfos
	select  *  into PLATFORM_ABBOTT_STG.dbo.PurPAFinancialVoucherInfos from #PurPAFinancialVoucherInfos
	PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into PLATFORM_ABBOTT_STG.dbo.PurPAFinancialVoucherInfos from #PurPAFinancialVoucherInfos
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END 

END
