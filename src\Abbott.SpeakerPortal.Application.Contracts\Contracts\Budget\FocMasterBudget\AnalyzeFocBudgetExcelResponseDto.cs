﻿using System;

namespace Abbott.SpeakerPortal.Contracts.Budget.FocMasterBudget
{
    public class AnalyzeFocBudgetExcelResponseDto : CreateFocMasterBudgetExcelDto
    {
        /// <summary>
        /// 行号
        /// </summary>
        public int No { get; set; }

        /// <summary>
        /// 整型年度,数字类型，前端不需要展示
        /// </summary>
        public int? Year { get; set; }

        /// <summary>
        /// BuId
        /// </summary>
        public Guid? BuId { get; set; }

        /// <summary>
        /// 负责人姓名
        /// </summary>
        public string OwnerName { get; set; }

        /// <summary>
        /// 负责人Id
        /// </summary>
        public Guid? OwnerId { get; set; }

        /// <summary>
        /// 预算数量,数字类型，前端不需要展示
        /// </summary>
        public int? BudgetQty { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string Message { get; set; }
    }
}
