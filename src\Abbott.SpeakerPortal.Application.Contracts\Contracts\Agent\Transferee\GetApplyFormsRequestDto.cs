﻿using Abbott.SpeakerPortal.Dtos;
using Abbott.SpeakerPortal.Enums;

using System;

namespace Abbott.SpeakerPortal.Contracts.Agent
{
    /// <summary>
    /// 获取申请单据
    /// </summary>
    public class GetApplyFormsRequestDto : PagedDto
    {
        /// <summary>
        /// 申请人ID
        /// </summary>
        public Guid? ApplyUserId { get; set; }
        /// <summary>
        /// 申请人
        /// </summary>
        public string ApplyUserName { get; set; }
        /// <summary>
        /// 单据编号
        /// </summary>
        public string ApplicationCode { get; set; }
        /// <summary>
        /// 单据/流程类型
        /// </summary>
        public ResignationTransfer.TaskFormCategory? FormCategory { get; set; }
    }
}
