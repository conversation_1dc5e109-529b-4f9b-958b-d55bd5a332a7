﻿using Abbott.SpeakerPortal.Contracts.Budget;
using Abbott.SpeakerPortal.Entities.Budget;
using MiniExcelLibs;
using MiniExcelLibs.OpenXml;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.AppServices.Budget
{
    public class BdHistoryService : SpeakerPortalAppService, IBdHistoryService
    {
        /// <summary>
        /// 根据Id获取历史记录数据
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public async Task<List<HistoryRecordsResponseDto>> GetHistoryRecordsByIdAsync(Guid Id)
        {
            var historyQuery = await LazyServiceProvider.LazyGetService<IBdHistoryRepository>().GetQueryableAsync();
            var query = historyQuery.Where(r => r.BudgetId == Id).OrderByDescending(o => o.OperatingTime).ToList();
            var datas = ObjectMapper.Map<List<BdHistory>, List<HistoryRecordsResponseDto>>(query);
            return datas;
        }
        /// <summary>
        /// 导出预算操作历史记录
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public async Task<Stream> ExportHistoryExcelAsync(Guid Id)
        {
            var historyQuery = await LazyServiceProvider.LazyGetService<IBdHistoryRepository>().GetQueryableAsync();
            var query = historyQuery.Where(r => r.BudgetId == Id).OrderByDescending(o => o.OperatingTime).ToList();
            var datas = ObjectMapper.Map<List<BdHistory>, List<HistoryRecordsExcelResponseDto>>(query);
            MemoryStream stream = new();
            var config = new OpenXmlConfiguration()
            {
                TableStyles = TableStyles.None,
                AutoFilter = false

            };
            stream.SaveAs(datas, true, "Sheet1", configuration: config);
            stream.Seek(0, SeekOrigin.Begin);
            return stream;
        }
    }
}
