﻿using Abbott.SpeakerPortal.BackgroundWorkers.Report;
using Abbott.SpeakerPortal.Contracts.Integration.Dspot;

using Hangfire;

using Microsoft.Extensions.DependencyInjection;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

using Volo.Abp.BackgroundWorkers.Hangfire;

namespace Abbott.SpeakerPortal.BackgroundWorkers.Bpcs
{
    public class SyncTableGlhWorker : SpeakerPortalBackgroundWorkerBase
    {
        public SyncTableGlhWorker()
        {
            CronExpression = Cron.Daily(0);//每天凌晨1点
        }

        public override async Task DoWorkAsync(CancellationToken cancellationToken = default)
        {
            await LazyServiceProvider.LazyGetService<IIntermediateToSpeakerService>().SyncTablesGlh();
            // 调度作业
            BackgroundJob.Enqueue<PAJoinBpcsGlhWorker>(a => a.<PERSON>(CancellationToken.None));
        }
    }
}
