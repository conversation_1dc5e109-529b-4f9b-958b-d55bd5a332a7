﻿using System;
using System.Collections.Generic;
using System.Text;
using static Org.BouncyCastle.Crypto.Engines.SM2Engine;

namespace Abbott.SpeakerPortal.Contracts.Budget.Subbudget
{
    public class MappingSubgetMessageDto
    {
        /// <summary>
        /// 序号
        /// </summary>
        public int No { get; set; }
        /// <summary>
        /// 预算code
        /// </summary>
        public string Code { get; set; }
        /// <summary>
        /// 主预算
        /// </summary>
        public string Bu2 { get; set; }
        /// <summary>
        /// 责任人2
        /// </summary>
        public string Owner2Names { get; set; }
        /// <summary>
        /// 大区经理
        /// </summary>
        public string RegionManagerNames { get; set; }
        /// <summary>
        /// LMM
        /// </summary>
        public string LMMNames { get; set; }
        /// <summary>
        /// 大区助理
        /// </summary>
        public string RegionalAssistantNames { get; set; }
        /// <summary>
        /// 产品经理
        /// </summary>
        public string ProductManagerNames { get; set; }
        /// <summary>
        /// 信息
        /// </summary>
        public string Message {  get; set; }


        public string Owner2Ids { get; set; }
        public string RegionManagerIds { get; set; }
        public string LMMIds { get; set; }
        public string RegionalAssistantIds { get; set; }
        public string ProductManagerIds { get; set; }
    }
}
