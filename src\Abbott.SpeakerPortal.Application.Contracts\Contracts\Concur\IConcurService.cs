﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

using Abbott.SpeakerPortal.Contracts.Concur.MealReport;
using Abbott.SpeakerPortal.Contracts.Concur.OrgMapping;
using Abbott.SpeakerPortal.Domain.Shared.Models;

using Volo.Abp.Application.Dtos;

namespace Abbott.SpeakerPortal.Contracts.Concur
{
    public interface IConcurService
    {
        /// <summary>
        /// 查询员工分组配置信息
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<PagedResultDto<EmployeeQueryResponseDto>> QueryEmployeeListAsync(EmployeeQueryRequestDto request);

        /// <summary>
        /// 上传员工信息，执行验证
        /// </summary>
        /// <param name="buffer"></param>
        /// <returns></returns>
        Task<MessageResult> UploadEmployeeAsync(byte[] buffer);

        /// <summary>
        /// 提交验证后的信息，执行导入
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<MessageResult> ImportEmployeeAsync(EmployeeImportRequestDto request);

        /// <summary>
        /// 获取机构转换配置列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<PagedResultDto<QueryOrgMappingResponseDto>> QueryOrgMappingListAsync(QueryOrgMappingRequestDto request);

        /// <summary>
        /// 获取机构名称列表
        /// </summary>
        /// <param name="keyword"></param>
        /// <returns></returns>
        Task<IEnumerable<string>> GetOrgNewNamesAsync(string keyword);

        /// <summary>
        /// 创建机构转换配置
        /// </summary>
        /// <param name="orgNames"></param>
        /// <returns></returns>
        Task<MessageResult> CreateOrgMappingAsync(IEnumerable<string> orgNames);

        /// <summary>
        /// 更新机构转换配置
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<MessageResult> UpdateOrgMappingAsync(UpdateOrgMappingRequestDto request);

        /// <summary>
        /// 获取机构配置变更历史
        /// </summary>
        /// <param name="orgMappingId"></param>
        /// <returns></returns>
        Task<IEnumerable<QueryOrgMappingHistoryResponseDto>> GetOrgMappingHistoryListAsync(Guid orgMappingId);

        /// <summary>
        /// 查询用餐报告
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<PagedResultDto<MealReportQueryResponseDto>> QueryMealReportListAsync(MealReportQueryRequestDto request);

        /// <summary>
        /// 上传用餐报告
        /// </summary>
        /// <param name="buffer"></param>
        /// <returns></returns>
        Task<MessageResult> UploadMealReportAsync(byte[] buffer);
    }
}
