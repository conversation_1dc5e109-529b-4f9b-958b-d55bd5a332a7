﻿using Abbott.SpeakerPortal.Contracts.Integration.Bpcs;
using Abbott.SpeakerPortal.Entities.Integration.Dspot;
using Abbott.SpeakerPortal.IntermediateDB.Models;
using Abbott.SpeakerPortal.IntermediateDB.Repositories;
using Abbott.SpeakerPortal.Utils;

using EFCore.BulkExtensions;

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using Volo.Abp.Domain.Repositories;
using Volo.Abp.Guids;

namespace Abbott.SpeakerPortal.AppServices.Integration.Bpcs
{
    public class IntermediateToSpeakerAmlService : SpeakerPortalAppService, IIntermediateToSpeakerAmlService
    {
        /// <summary>
        /// The logger
        /// </summary>
        private readonly ILogger<IntermediateToSpeakerAmlService> _logger;

        public IntermediateToSpeakerAmlService(IServiceProvider serviceProvider)
        {
            _logger = serviceProvider.GetService<ILogger<IntermediateToSpeakerAmlService>>();
        }

        BpcsAml GetTarByOri(Dictionary<string, BpcsAml> tarDict, Aml ori)
        {
            if (tarDict?.Any() != true)
                return default;

            tarDict.TryGetValue($"{ori.Amlcmp}{ori.Amlvnd}{ori.Amlref}{ori.Amlsub}{ori.Amllin}{ori.Amlinv}", out BpcsAml bpcsAml);

            return bpcsAml;
        }

        public async Task<int> SyncTableIncrement()
        {
            int recordCount = 0;
            try
            {
                //查询TEntityOri
                var repoOri = LazyServiceProvider.GetService<IIntermediateAmlRepository>();
                if (repoOri == null)
                {
                    _logger.LogError($"SyncTableIncrement() repoOri is null");
                    return recordCount;
                }
                var query = await repoOri.GetQueryableAsync();
                //拉取7天的数据进行对比
                var listOri = query.Where(a => a.UpdateTime >= DateTime.Today.AddDays(-7)).ToList();
                recordCount = listOri.Count;
                if (!listOri.Any())
                {
                    return recordCount;
                }

                var amlcmps = listOri.Select(a => a.Amlcmp).Distinct();
                var amlvnds = listOri.Select(a => a.Amlvnd).Distinct();
                var amlrefs = listOri.Select(a => a.Amlref).Distinct();
                var amlsubs = listOri.Select(a => a.Amlsub).Distinct();
                var amllins = listOri.Select(a => a.Amllin).Distinct();
                var amlinvs = listOri.Select(a => a.Amlinv).Distinct();

                //1,查询有哪些Ori已存在表BpcsTar
                var repoTar = LazyServiceProvider.GetService<IBpcsAmlRepository>();
                var queryTar = await repoTar.GetQueryableAsync();
                var exitsTar = queryTar.Where(a => amlcmps.Contains(a.Amlcmp) && amlvnds.Contains(a.Amlvnd) && amlrefs.Contains(a.Amlref) && amlsubs.Contains(a.Amlsub) && amllins.Contains(a.Amllin) && amlinvs.Contains(a.Amlinv)).ToList();

                var guidGenerator = LazyServiceProvider.LazyGetService<IGuidGenerator>();
                var updateEntities = new List<BpcsAml>();
                var addEntities = new List<BpcsAml>();

                var exitsTarDict = exitsTar.ToDictionary(a => $"{a.Amlcmp}{a.Amlvnd}{a.Amlref}{a.Amlsub}{a.Amllin}{a.Amlinv}", a => a);

                //2,已存在表Tar的，比较Tar是否有更新，有则Update，无则不保存
                foreach (var item in listOri)
                {
                    var exitsEntity = GetTarByOri(exitsTarDict, item);
                    if (exitsEntity != null)
                    {
                        //比较Tar是否有更新，有则Update，无则不保存
                        if (!item.PropertyEqualAll(exitsEntity))
                        {
                            var updateEntity = item.AssignPropertiesTo(exitsEntity);
                            if (updateEntity != null)
                            {
                                updateEntities.Add(updateEntity);
                            }
                        }
                    }
                    else
                    {
                        //3,未存在表BpcsAml的，Insert BpcsAml
                        var entity = ObjectMapper.Map<Aml, BpcsAml>(item);

                        //手动设置Id
                        var manualSetId = entity as IManualSetId<Guid>;
                        if (manualSetId != null)
                            manualSetId.SetId(guidGenerator.Create());

                        addEntities.Add(entity);
                    }
                }

                //更新
                if (updateEntities.Any())
                {
                    //await repoTar.UpdateManyAsync(updateEntities, repoAutoSave);
                    var context = await repoTar.GetDbContextAsync();
                    await context.BulkUpdateAsync(updateEntities);
                }
                //插入
                if (addEntities.Any())
                {
                    //await repoTar.InsertManyAsync(addEntities, repoAutoSave);
                    var context = await repoTar.GetDbContextAsync();
                    await context.BulkInsertAsync(addEntities);
                }

                AfterSyncIncrement(updateEntities, addEntities);
            }
            catch (Exception ex)
            {
                _logger.LogError($"SyncTableIncrement() Exception: {ex}");
                throw;
            }
            return recordCount;
        }

        protected string AfterSyncIncrement(List<BpcsAml> updateEntities, List<BpcsAml> addEntities)
        {
            if (updateEntities?.Any() != true && addEntities.Any() != true)
            {
                return null;
            }

            //AML的数据，拿着updateEntities+addEntities 去找 PurPAApplication，BpcsAml.AMLINV，找到即为“打款中”

            return null;
        }
    }
}