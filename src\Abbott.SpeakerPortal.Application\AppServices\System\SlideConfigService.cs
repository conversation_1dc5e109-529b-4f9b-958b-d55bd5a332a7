﻿using Abbott.SpeakerPortal.Contracts.System.FinanceReview;
using Abbott.SpeakerPortal.Contracts.System.Slide;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Entities.SystemConfig.Slide;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Redis;
using Abbott.SpeakerPortal.Utils;
using DocumentFormat.OpenXml.Presentation;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Xrm.Sdk;
using MiniExcelLibs;
using Senparc.CO2NET.Extensions;
using Senparc.Weixin.WxOpen.Entities;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.ObjectMapping;
using static Abbott.SpeakerPortal.Enums.SlideConfig;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace Abbott.SpeakerPortal.AppServices.System
{
    public class SlideConfigService : SpeakerPortalAppService, ISlideConfigService
    {
        private readonly ILogger<SlideConfigService> _logger;
        public SlideConfigService(ILogger<SlideConfigService> logger) 
        {
            _logger = logger;
        }
        public async Task<MessageResult> AddSlideConfigAsync(CreateSlideConfigDto createSlide)
        {
            var slideConfigRepository = LazyServiceProvider.LazyGetService<ISlideConfigRepository>();
            var querySlide = await slideConfigRepository.GetQueryableAsync();
            var slideHistoryRepository = LazyServiceProvider.LazyGetService<ISlideOperateHistoryRepository>();
            if (querySlide.Any(a => a.SlideCode == createSlide.SlideCode))
                return MessageResult.FailureResult("SPECTRA ID已存在，保存失败");
            if (querySlide.Any(a => a.SlideName == createSlide.SlideName))
                return MessageResult.FailureResult("幻灯片名称已存在，保存失败");
            var addSlideConfig = ObjectMapper.Map<CreateSlideConfigDto, Entities.SystemConfig.Slide.SlideConfig>(createSlide);
            addSlideConfig.UpdateTime = DateTime.Now;
            var result = await slideConfigRepository.InsertAsync(addSlideConfig);
            await slideHistoryRepository.InsertAsync(new SlideOperateHistory() {
                SlideId = result.Id,
                Type = Enums.SlideConfig.SlideOperationType.Add,
                UserId = CurrentUser.Id.Value,
                UserName = CurrentUser.Name,
                OperateDate = DateTime.Now,
            });
            return MessageResult.SuccessResult(ObjectMapper.Map<Entities.SystemConfig.Slide.SlideConfig,SlideConfigDto>( result));
        }

        public async Task<MessageResult> EditSlideConfigAsync(UpdateSlideConfigDto updateSlide)
        {
            var slideConfigRepository = LazyServiceProvider.LazyGetService<ISlideConfigRepository>();
            var querySlide = await slideConfigRepository.GetQueryableAsync();
            var slideHistoryRepository = LazyServiceProvider.LazyGetService<ISlideOperateHistoryRepository>();
            var slide = querySlide.Where(a=>a.Id== updateSlide.Id).FirstOrDefault();
            if (slide.SlideCode != updateSlide.SlideCode && querySlide.Any(a => a.SlideCode == updateSlide.SlideCode)) 
            {
                return MessageResult.FailureResult("SPECTRA ID已存在，保存失败");
            }
            if (slide.SlideName != updateSlide.SlideName && querySlide.Any(a => a.SlideName == updateSlide.SlideName))
            {
                return MessageResult.FailureResult("幻灯片名称已存在，保存失败");
            }
            ObjectMapper.Map(updateSlide, slide);
            slide.UpdateTime = DateTime.Now;
            var result = await slideConfigRepository.UpdateAsync(slide);
            await slideHistoryRepository.InsertAsync(new SlideOperateHistory()
            {
                SlideId = result.Id,
                Type = Enums.SlideConfig.SlideOperationType.Edit,
                UserId = CurrentUser.Id.Value,
                UserName = CurrentUser.Name,
                OperateDate = DateTime.Now,
            });
            return MessageResult.SuccessResult(ObjectMapper.Map<Entities.SystemConfig.Slide.SlideConfig, SlideConfigDto>(result));
        }

        public async Task<MessageResult> EnableOrDisableSlideConfigAsync(Guid slideId)
        {
            var slideConfigRepository = LazyServiceProvider.LazyGetService<ISlideConfigRepository>();
            var querySlide = await slideConfigRepository.GetQueryableAsync();
            var slideHistoryRepository = LazyServiceProvider.LazyGetService<ISlideOperateHistoryRepository>();
            var slide = querySlide.Where(a => a.Id == slideId).FirstOrDefault();
            var type = Enums.SlideConfig.SlideOperationType.Enable;
            if (slide.Status == SlideConfigStatus.Enable)
            {
                slide.Status = SlideConfigStatus.Disable;
                type = Enums.SlideConfig.SlideOperationType.Disable;
            }
            else 
            {
                slide.Status = SlideConfigStatus.Enable;
                type = Enums.SlideConfig.SlideOperationType.Enable;
            }
            await slideHistoryRepository.InsertAsync(new SlideOperateHistory()
            {
                SlideId = slide.Id,
                Type = type,
                UserId = CurrentUser.Id.Value,
                UserName = CurrentUser.Name,
                OperateDate = DateTime.Now,
            });
            slide.UpdateTime = DateTime.Now;
            await slideConfigRepository.UpdateAsync(slide);
            return MessageResult.SuccessResult(slide.Status);
        }

        public async Task<List<SlideOperateHistoryDto>> GetOperateHistoryAsync(Guid slideId)
        {
            var querySlide = await LazyServiceProvider.LazyGetService<ISlideConfigRepository>().GetQueryableAsync();
            var querySlideHistory = await LazyServiceProvider.LazyGetService<ISlideOperateHistoryRepository>().GetQueryableAsync();
            var historys = querySlideHistory.GroupJoin(querySlide, a=>a.SlideId,b=>b.Id,(a,b)=>new { history = a,slides = b})
                .SelectMany(a => a.slides.DefaultIfEmpty(),(a,b)=>new { a.history, slide = b})
                .Where(a=>a.history.SlideId== slideId)
                .Select(a => new SlideOperateHistoryDto() {
                    SlideId = a.slide.Id,
                    SlideCode = a.slide.SlideCode,
                    SlideName = a.slide.SlideName,
                    EffectiveDate = a.slide.EffectiveDate.ToString("yyyy-MM-dd"),
                    ExpirationDate = a.slide.ExpirationDate.ToString("yyyy-MM-dd"),
                    Type = a.history.Type,
                    UserName = a.history.UserName,
                    OperateDate = a.history.OperateDate.ToString("yyyy-MM-dd"),
                })
                .ToList();
            var types = EnumUtil.GetEnumIdValues<SlideOperationType>();
            historys.ForEach(a => {
                a.TypeName = types.Where(x => x.Key == (int)a.Type).FirstOrDefault()?.Value ?? "";
            });
            return historys;
        }

        public async Task<PagedResultDto<SlideConfigDto>> GetSlideConfigListAsync(SlideListRequestDto slideListRequest, bool isExcept=false)
        {
            var querySlide = await LazyServiceProvider.LazyGetService<ISlideConfigRepository>().GetQueryableAsync();
            var query = querySlide.WhereIf(!string.IsNullOrWhiteSpace(slideListRequest.SlideCode), a => a.SlideCode.Contains(slideListRequest.SlideCode))
                .WhereIf(!string.IsNullOrWhiteSpace(slideListRequest.SlideName), a => a.SlideName.Contains(slideListRequest.SlideName))
                .OrderByDescending(a=>a.UpdateTime).ThenBy(a=>a.Status);
            var queryData = new List<Entities.SystemConfig.Slide.SlideConfig>();
            if (!isExcept)
            {
                queryData = query.Skip(slideListRequest.PageIndex * slideListRequest.PageSize)
                    .Take(slideListRequest.PageSize)
                    .ToList();
            }
            else
            {
                queryData = query.ToList();
            }
            var resultSlides = ObjectMapper.Map<List<Entities.SystemConfig.Slide.SlideConfig>, List<SlideConfigDto>>(queryData);
            resultSlides.ForEach(slide => { slide.StatusName = slide.Status.GetDescription(); });
            var result = new PagedResultDto<SlideConfigDto>();
            result.Items = resultSlides;
            result.TotalCount = query.Count();
            return result;
        }

        public async Task<Stream> ExportSlideConfigListAsync(SlideListRequestDto slideListRequest)
        {
            try
            {
                var slideConfigList = await GetSlideConfigListAsync(slideListRequest, true);
                var exportData = slideConfigList.Items.Select(a => new ExportSlideConfigDto
                {
                    SlideCode = a.SlideCode,
                    SlideName = a.SlideName,
                    EffectiveDate = a.EffectiveDate,
                    ExpirationDate = a.ExpirationDate,
                    UpdateTime = a.UpdateTime.ToString("yyyy-MM-dd HH:mm:ss"),
                    StatusName = a.Status.GetDescription(),
                });
                MemoryStream stream = new();
                stream.SaveAs(exportData, true, "SheetName");
                stream.Seek(0, SeekOrigin.Begin);
                return stream;
            }
            catch (Exception ex)
            {
                _logger.LogError($"ExportSlideConfigListAsync error:{ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 根据预计日期查询幻灯片
        /// </summary>
        /// <param name="expectedDate"></param>
        /// <returns></returns>
        public async Task<List<DictionaryDto>> GetSlideNames(DateTime expectedDate) 
        {
            var querySlide = await LazyServiceProvider.LazyGetService<ISlideConfigRepository>().GetQueryableAsync();
            var slides = querySlide.Where(a=>a.EffectiveDate.Date <= expectedDate.Date && a.ExpirationDate.Date >= expectedDate.Date && a.Status == SlideConfigStatus.Enable)
                .Select(a=>new DictionaryDto { 
                    Type= "幻灯片名称",
                    Code = a.SlideCode,
                    ParentCode = "SlideName",
                    Id= a.Id,
                    Name = a.SlideName,
                })
                .ToList();
            return slides;
        }
    }
}
