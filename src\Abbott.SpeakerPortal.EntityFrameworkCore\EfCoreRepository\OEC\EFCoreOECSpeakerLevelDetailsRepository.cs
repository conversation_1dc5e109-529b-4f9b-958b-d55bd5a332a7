﻿using Abbott.SpeakerPortal.Entities.OECSpeakerLevelDetails;
using Abbott.SpeakerPortal.EntityFrameworkCore;
using System;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace Abbott.SpeakerPortal.EfCoreRepository
{
    /// <summary>
    /// EFCoreOECSpeakerLevelDetailsRepository
    /// </summary>
    /// <seealso cref="Volo.Abp.Domain.Repositories.EntityFrameworkCore.EfCoreRepository&lt;Abbott.SpeakerPortal.EntityFrameworkCore.SpeakerPortalDbContext, Abbott.SpeakerPortal.Entities.OECSpeakerLevelDetails.OECSpeakerLevelDetail, System.Guid&gt;" />
    /// <seealso cref="Abbott.SpeakerPortal.Entities.OECSpeakerLevelDetails.IOECSpeakerLevelDetailRepository" />
    public class EFCoreOECSpeakerLevelDetailRepository : EfCoreRepository<SpeakerPortalDbContext, OECSpeakerLevelDetail, Guid>, IOECSpeakerLevelDetailRepository
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="EFCoreOECSpeakerLevelDetailsRepository"/> class.
        /// </summary>
        /// <param name="dbContextProvider">The database context provider.</param>
        public EFCoreOECSpeakerLevelDetailRepository(IDbContextProvider<SpeakerPortalDbContext> dbContextProvider) : base(dbContextProvider)
        {
        }
    }
}
