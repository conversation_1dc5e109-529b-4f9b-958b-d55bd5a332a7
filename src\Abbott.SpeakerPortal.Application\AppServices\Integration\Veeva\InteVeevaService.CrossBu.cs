﻿using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.Common;
using Abbott.SpeakerPortal.Contracts.Integration.Veeva;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Entities.CrossBu;
using DocumentFormat.OpenXml.Drawing;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Abbott.SpeakerPortal.Extension;

using Flurl.Http;
using Abbott.SpeakerPortal.Vendor.Speaker;
using Abbott.SpeakerPortal.Entities.Integration.Veeva;
using Abbott.SpeakerPortal.Entities.VendorApplications;
using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.Enums.Integration;
using Abbott.SpeakerPortal.Utils;
using Abbott.SpeakerPortal.Contracts.Common.Approve;
using Abbott.SpeakerPortal.Entities.Common.Workflow.CurrentApprovalTask;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Entities.VendorApplicationPersonals;
using Microsoft.Xrm.Sdk;

namespace Abbott.SpeakerPortal.AppServices.Integration.Veeva
{
    public partial class InteVeevaService : SpeakerPortalAppService, IInteVeevaService
    {
        /// <summary>
        /// 推送客户关系到veeva
        /// </summary>
        /// <param name="customer">The customer.</param>
        /// <returns></returns>
        public string PushDcrCrossBU(CustomerRelation customer)
        {
            //Entrance api Url
            var url = $"{_apiHost}{_apiEntrance}";
            if (string.IsNullOrWhiteSpace(url))
            {
                //return MessageResult.FailureResult("url is null");
                return $"url is null, customrelate: {JsonSerializer.Serialize(customer)}";
            }
            var log = new SetOperationLogRequestDto();

            try
            {
                //先获取Token
                var reqToken = GetToken();
                if (reqToken == null || string.IsNullOrWhiteSpace(reqToken.AccessToken))
                {
                    return $"reqToken is null, customrelate: {JsonSerializer.Serialize(customer)}";
                }
                var headers = new Dictionary<string, string>
                {
                    {"Authorization" ,$"Bearer {reqToken.AccessToken}"}
                };

                var request = TidyDcrReqCrossBuDto(customer);
                log = _commonService.InitOperationLog("Veeva", "向Veeva推送医生进行验证", url + "|" + JsonSerializer.Serialize(request));
                //TODO:待记录API日志，记录每个调出API的信息
                var response = url.WithHeaderJson(headers).PostJsonAsync(request).GetAwaiterResult();
                string responseResult = response.GetStringAsync().GetAwaiterResult();
                _commonService.LogResponse(log, (responseResult));
                //保存Response到表 VendorApplication
                var respObj = response.GetJsonAsync<VeevaDcrRespDto>().GetAwaiterResult();
                //记录dcr log
                DCRCrossBuLog(request, respObj, customer);
                customer.PushVeevaResp = responseResult;
                return respObj.ResponseStatus == "0" ? null : respObj.ResponseMessage;
            }
            catch (Exception ex)
            {
                _commonService.LogResponse(log, ex.ToString(), false);
                _logger.LogError($"PushDcr() , customerId: {JsonSerializer.Serialize(customer)}, Exception: {ex}");
                return ex.Message;
            }
        }

        private VeevaDcrReqDto TidyDcrReqCrossBuDto(CustomerRelation customer)
        {
            //var vndAppPer = GetVndAppPer(vndApp.Id);

            //是否新增，逻辑修改为判断是否有VeevaID，Veevaid就走change，没veevaid就走add
            //var isAdd = vndApp.ApplicationType == Enums.ApplicationTypes.Create;
            //var isAdd = string.IsNullOrEmpty(vndApp.VendorId);

            //var vndAppOld = isAdd ? null : GetLastVndApp(vendorOldData);
            //var vnd = isAdd ? null : GetVendorByVendorCode(vndApp.VendorCode);//先不查，就用vendorOldData
            var dicHcpLevels = GetHcpLevels();
            var dicAcademicLevel = GetAcademicLevels();
            var pts = GetPTs();
            var standartDept = GetStandarDepartments();
            var dicHospitals = GetHospitals(new List<Guid?> { customer.HospitalPPId });
            //var (hcpPreDcrId, speakerPreDcrId, hcoPreDcrId) = GetPreviousDcrID(vndApp.Id);
            //基础信息 data.hcp_dcr_entity
            var hcpDcrEntity = new
            {
                dcr_type = "ADD_REQUEST",
                note = (string)null,
                customer_dcr_id = Guid.NewGuid(),
                file_url = new List<string> { },//支持件链接 TODO:多个文件用|隔开
                hcp_code = customer.Id,
                //待定

                previous_dcr_id = (string)null,//hcpPreDcrId,
                vid__v = customer.HPCCode,

                original_full_name__v = "",
                request_full_name__v = customer.DoctorName,
                original_gender__v = "未知",
                request_gender__v = "未知",
                original_department_name = "",
                request_department_name = customer.HospitalDepartment,
                original_primary_department_class__v = "",
                request_primary_department_class__v = "",
                original_specialty_1__v = "",//讲者专长 对应标准科室的专长
                request_specialty_1__v = "",//讲者专长 对应标准科室的专长
                original_primary_relation_type__v = customer.StandardizationPosition,//讲者职务 不涉及修改，只是从veeva拿过来保存。
                request_primary_relation_type__v = customer.StandardizationPosition,//讲者职务
                original_professional_title__v = customer.DoctorTitle,
                request_professional_title__v = customer.DoctorTitle,
                original_hcp_status__v = "",
                request_hcp_status__v = "",//与org保持一致
                original_academic_title__v = "",
                request_academic_title__v = "",
                original_hcp_type__v = "",//HCP 类型 参考职务
                request_hcp_type__v = "",//HCP 类型 
                license__v = new List<dynamic>
                {
                    new
                    {
                        original_license_number__v = "",//HCP 执业医师编码原始值
                        request_license_number__v = "",//HCP 执业医师编码请求值
                    }
                },

                //parent_hcos__v：用供应商申请表的所属医院字段去查PP（hco_dcr也一样），PP的医院要加好多东西。。。

                parent_hcos__v = new List<dynamic>
                {
                    new
                    {
                        original_hco_code = "",
                        request_hco_code = dicHospitals.GetValue(customer.HospitalPPId)?.HospitalCode,
                        original_hco_vid = "",
                        request_hco_vid = dicHospitals.GetValue(customer.HospitalPPId)?.HcoVeevaID,
                        original_hco_name ="",
                        request_hco_name =  dicHospitals.GetValue(customer.HospitalPPId)?.Name,
                        original_hco_province="",
                        request_hco_province= dicHospitals.GetValue(customer.HospitalPPId)?.ProvinceName,
                        original_hco_city="",
                        request_hco_city= dicHospitals.GetValue(customer.HospitalPPId)?.CityName,
                    }
                }
            };
            VeevaSpeakerDcrEntityReqDto speakerDcr = null;
            var addHco = customer.HospitalStatus == CustomerRelationHospitalStatus.New;
            //医院信息，文档里必填字段已补充完整
            var hcoDcrEntity = new
            {
                dcr_type = "ADD_REQUEST",
                note = (string)null,
                customer_dcr_id = Guid.NewGuid(),
                file_url = new List<string> { },//支持件链接 TODO:多个文件用|隔开
                previous_dcr_id = (string)null,//hcoPreDcrId,
                hco_code = dicHospitals.GetValue(customer.HospitalPPId)?.HospitalCode,
                vid__v = customer.HCOCode,//
                request_corporate_name = customer.Hospital,
                request_administrative_area = customer.HospitalProvince,  //机构所在省份请求值
                request_locality = customer.HospitalCity,  //机构所在城市请求值

            };

            var requestId = Guid.NewGuid();
            var requestType = new List<string> { "hcp_dcr" };
            requestType.AddIf(addHco, "hco_dcr");

            return new VeevaDcrReqDto()
            {
                RequestId = requestId.ToString(),
                RequestType = requestType,
                Data = new
                {
                    speaker_dcr_entity = speakerDcr,
                    hcp_dcr_entity = hcpDcrEntity,
                    hco_dcr_entity = hcoDcrEntity,
                }
            };
        }
        /// <summary>
        /// 根据推送记录，拉取结果
        /// </summary>
        /// <returns></returns>
        public string PullDcrCrossBuResult()
        {

            var log = new SetOperationLogRequestDto();

            try
            {
                //Entrance api Url
                var url = $"{_apiHost}{_apiEntrance}";
                if (string.IsNullOrWhiteSpace(url))
                {
                    //return MessageResult.FailureResult("url is null");
                    return $"url is null";
                }


                var (request, veevaDcrLog) = TidyDcrResultReqDto(FlagType.CrossBu);
                if (veevaDcrLog.Count == 0) return "has no dcrid need to pull";

                //获取Token
                var reqToken = GetToken();
                if (reqToken == null || string.IsNullOrWhiteSpace(reqToken.AccessToken))
                {
                    return $"reqToken is null";
                }
                var headers = new Dictionary<string, string>
                {
                    {"Authorization" ,$"Bearer {reqToken.AccessToken}"}
                };
                log = _commonService.InitOperationLog("Veeva", "向Veeva批量获取医生验证结果", url + "|" + JsonSerializer.Serialize(request));
                var respObj = new VeevaDcrResultRespDto();
                try
                {
                    var response = url.WithHeaderJson(headers).PostJsonAsync(request).GetAwaiterResult();
                    respObj = response.GetJsonAsync<VeevaDcrResultRespDto>().GetAwaiterResult();
                }
                catch (Exception ex)
                {
                    _commonService.LogResponse(log, ex.ToString(), false);
                    throw;
                }

                _commonService.LogResponse(log, JsonSerializer.Serialize(respObj));
                //处理Veeva验证结果
                ProcessDcrCrossBuResult(request, respObj, veevaDcrLog);

                return respObj.ResponseStatus == "0" ? null : respObj.ResponseMessage;
            }
            catch (Exception ex)
            {
                _logger.LogError($"PullDcrResult()  Exception: {ex}");
                return ex.Message;
            }
        }
        private string ProcessDcrCrossBuResult(VeevaDcrReqDto request, VeevaDcrResultRespDto respObj, List<VeevaDCRLog> veevaDcrLogs)
        {

            if (request == null || respObj == null || respObj.ResponseStatus != "0")
            {
                return "request is null or respObj is null or respObj.ResponseStatus != 0";
            }

            //记录结果日志
            DCRResultLog(respObj, veevaDcrLogs);


            var formIdS = veevaDcrLogs.DistinctBy(a => a.BusinessFormId).Select(a => a.BusinessFormId);

            //更新申请表，循环更新
            //var repoVnd = LazyServiceProvider.LazyGetService<IVendorRepository>();
            var customerRelationRepository = LazyServiceProvider.LazyGetService<ICustomerRelationRepository>();
            var crDatas = customerRelationRepository.GetListAsync(a => formIdS.Contains(a.Id)).GetAwaiterResult();
            var deletePPHcos = new List<KeyValuePair<Guid, Guid?>>();//需要删除的PP端医院ID
            var hospitals = _dataverseService.GetAllHospitals().GetAwaiter().GetResult();
            List<VeevaApprovalOperationDto> veevaApprovals = [];
            List<Guid> errorFormId = [];
            List<Guid> delhospitalIds = [];
            foreach (var cr in crDatas)
            {
                try
                {
                    var hcp_dcrid = veevaDcrLogs.FirstOrDefault(a => a.BusinessFormId == cr.Id && a.RequestType == DcrRequestType.hcp_dcr.GetDescription())?.DCRID;

                    var hco_dcrid = veevaDcrLogs.FirstOrDefault(a => a.BusinessFormId == cr.Id && a.RequestType == DcrRequestType.hco_dcr.GetDescription())?.DCRID;

                    var dcrResultHcp = respObj.data?.hcp_dcr_result?.Any() != true ? null
                    : respObj.data?.hcp_dcr_result.FirstOrDefault(a => a.status.Equals(DcrResultStatus.Completed.GetDescription(), StringComparison.OrdinalIgnoreCase)
                        && a.dcr_id == hcp_dcrid);
                    var dcrResultHcpEntity = dcrResultHcp?.entity_data;

                    var dcrResultHco = respObj.data?.hco_dcr_result?.Any() != true ? null
                        : respObj.data?.hco_dcr_result.FirstOrDefault(a => a.status.Equals(DcrResultStatus.Completed.GetDescription(), StringComparison.OrdinalIgnoreCase)
                            && a.dcr_id == hco_dcrid);
                    var dcrResultHcoEntity = dcrResultHco?.entity_data;
                    cr.Status = CustomerStatus.Valid;
                    //Speaker信息：（基础信息+讲者信息）
                    var resultHcpSpeaker = ProcessDcrResultHcpCrossBu(cr, dcrResultHcpEntity);
                    if (dcrResultHcoEntity != null)
                    {
                        var hospital = hospitals.First(f => f.HospitalCode == dcrResultHcoEntity.verify_hco_code);
                        if (hospital.Id != cr.HospitalPPId)
                        {
                            delhospitalIds.Add(hospital.Id);
                            cr.HospitalPPId = hospital.Id;
                        }
                        else
                        {
                            //医院信息：PP医院主数据，且医院会被标记为已验证
                            var resultHco = ProcessDcrResultCrossBuHco(cr.HospitalPPId, dcrResultHcoEntity);
                            if (!string.Equals(dcrResultHcoEntity.dcr_result, DcrResultApproveStatus.Approved.GetDescription(), StringComparison.OrdinalIgnoreCase)) cr.Status = CustomerStatus.Rejected;
                        }
                        cr.HCOCode = dcrResultHcoEntity.verify_hco_code;
                        cr.CleanedHospitalName = dcrResultHcoEntity.verified_corporate_name__v;
                        cr.HospitalType = dcrResultHcoEntity.verified_hco_type__v;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"ProcessDcrResult Error VendorApplicationId({cr.Id}):{ex.Message}");
                    continue;
                }
                //var result = resultHcpSpeaker ? null : "Process Speaker Failed. ";
                //result += resultHco ? null : "Process Hco Failed. ";
                //result += resultApproval ? null : "Process Approval Operation Failed. ";
            }
            customerRelationRepository.UpdateManyAsync(crDatas).GetAwaiter().GetResult();
            return "";
        }

        public async Task PushDcrByCrossBuSubmitTask(ScheduleJobLogDto log)
        {
            var customerRelationRepository = _serviceProvider.GetService<ICustomerRelationRepository>();

            var query = await customerRelationRepository.GetQueryableAsync();

            //var targetWorkflowType = new List<WorkflowTypeName> { WorkflowTypeName.SpeakerRequest, WorkflowTypeName.SpeakerChange };
            var tasks = query.Where(x => x.Status == CustomerStatus.Valid && x.IsPush == false).Take(100).ToArray();
            if (tasks.Length == 0)
                return;

            var response = string.Empty;

            foreach (var task in tasks)
            {
                response = string.Empty;
                response = PushDcrCrossBU(task);

                if (string.IsNullOrEmpty(response))//push成功
                    task.IsPush = true;
            }

            var updatedTasks = tasks.Where(x => x.IsPush == true);
            await customerRelationRepository.UpdateManyAsync(updatedTasks);
            return;
        }
        /// <summary>
        ///修改
        /// </summary>
        /// <param name="vndApp">The VND application.</param>
        /// <param name="dcrResultHcpEntity">The DCR result HCP entity.</param>
        /// <param name="dcrResultSpeakerEntity">The DCR result speaker entity.</param>
        /// <returns></returns>
        private bool ProcessDcrResultHcpCrossBu(CustomerRelation cr, VeevaDcrResultRespEntityDataHcpDto dcrResultHcpEntity)
        {
            if (cr == null || dcrResultHcpEntity == null)
            {
                return false;
            }

            var repoVndApp = LazyServiceProvider.LazyGetService<IVendorApplicationRepository>();
            //验证失败时，只写vndApp.VerificationStatus
            if (string.Equals(dcrResultHcpEntity.dcr_result, DcrResultApproveStatus.Rejected.GetDescription(), StringComparison.OrdinalIgnoreCase))
            {
                cr.Status = CustomerStatus.Rejected;
                return true;
            }

            ////先根据返回的SPLevel，如果SPLevel在我方没有，只记API日志，其它任何字段不修改，待下次取验证结果
            //var spLvlCode = CalcSpLvl(dcrResultSpeakerEntity.verify_speaker_level);
            //if (spLvlCode == null)
            //{
            //    //TODO:为SPLevel不对的场景，待记录API日志
            //    return true;
            //}

            //var vndAppPer = GetVndAppPer(vndApp.Id);
            cr.HPCCode = dcrResultHcpEntity.verify_vid__v;
            cr.HPCName = dcrResultHcpEntity.verify_full_name__v;
            cr.StandardizedTitle = dcrResultHcpEntity.verify_hcp_type__v;
            cr.StandardizationPosition = dcrResultHcpEntity.verify_primary_relation_type__v;
            cr.StandardDepartmentName = dcrResultHcpEntity.verify_primary_department_class__v;
            return true;
        }
        private (bool, Guid?) ProcessDcrResultCrossBuHco(Guid? id, VeevaDcrResultRespEntityDataHcoDto dcrResultHcoEntity)
        {
            if (!id.HasValue || !string.Equals(dcrResultHcoEntity.dcr_result, DcrResultApproveStatus.Approved.GetDescription(), StringComparison.OrdinalIgnoreCase))
            {
                return (false, null);
            }

            var dataverse = LazyServiceProvider.LazyGetService<IDataverseService>();
            var getProvincesTask = dataverse.GetAllProvince();
            var getCitiesTask = dataverse.GetAllCity();
            var getHcoLevelTask = dataverse.GetDictionariesAsync(DictionaryType.HcoLevel);

            var enums = EnumUtil.GetEnumIdValues<DataverseEnums.SpkHospitalMasterData.HospitalStatus>();

            Task.WaitAll(getProvincesTask, getCitiesTask, getHcoLevelTask);

            var dicFields = new Dictionary<string, object>();
            dicFields.Add("spk_name", dcrResultHcoEntity.verified_corporate_name__v);
            dicFields.Add("spk_hospitalcode", dcrResultHcoEntity.verify_hco_code);

            //状态
            var status = enums.FirstOrDefault(a => a.Value == dcrResultHcoEntity.verified_hco_status__v);
            if (status != null)
                dicFields.Add("spk_hospitalstatus", new OptionSetValue(status.Key));//已验证
            else
                dicFields.Add("spk_hospitalstatus", new OptionSetValue((int)DataverseEnums.SpkHospitalMasterData.HospitalStatus.Undetermined));//已验证

            dicFields.Add("spk_chinesevalue", dcrResultHcoEntity.verified_corporate_name__v);
            dicFields.Add("spk_englishvalue", dcrResultHcoEntity.verified_corporate_name__v);

            #region 2024-10-21新增字段，2353【Veeva对接】从EPD获取的hco_dcr_result中数据丢失---优先处理

            dicFields.Add("spk_hcoveevaid", dcrResultHcoEntity.verify_vid__v);

            //省份
            var hospital = getProvincesTask.Result.FirstOrDefault(a => a.Name == dcrResultHcoEntity.verified_administrative_area__v);
            if (hospital != null)
                dicFields.Add("spk_hcoprovince", new EntityReference(DataverseEntitiesConsts.Province, hospital.Id));
            else
                dicFields.Add("spk_hcoprovince", null);

            //城市
            var city = getCitiesTask.Result.FirstOrDefault(a => a.Name == dcrResultHcoEntity.verified_locality__v);
            if (city != null)
                dicFields.Add("spk_hcocity", new EntityReference(DataverseEntitiesConsts.City, city.Id));
            else
                dicFields.Add("spk_hcocity", null);

            //等级
            var hcoLevel = getHcoLevelTask.Result.FirstOrDefault(a => a.Name == dcrResultHcoEntity.verified_hco_grade__v);
            if (hcoLevel != null)
                dicFields.Add("spk_hcolevel", new EntityReference(DataverseEntitiesConsts.Dictionary, hcoLevel.Id));
            else
                dicFields.Add("spk_hcolevel", null);

            dicFields.Add("spk_hcotype", dcrResultHcoEntity.verified_hco_property__v);
            dicFields.Add("spk_hcodetailtype", dcrResultHcoEntity.verified_hco_type__v);
            dicFields.Add("spk_hcophone", dcrResultHcoEntity.verified_phone_1__v);
            dicFields.Add("spk_uscicode", dcrResultHcoEntity.verified_organization_code__v);

            //纬度
            if (decimal.TryParse(dcrResultHcoEntity.verified_cn_latitude__v, out decimal latitude))
                dicFields.Add("spk_latitude", latitude);
            else
                dicFields.Add("spk_latitude", null);

            //经度
            if (decimal.TryParse(dcrResultHcoEntity.verified_cn_longitude__v, out decimal longitude))
                dicFields.Add("spk_longitude", longitude);
            else
                dicFields.Add("spk_longitude", null);

            //dicFields.Add("spk_districtlevel", dcrResultHcoEntity.);//区县类别
            dicFields.Add("spk_district", dcrResultHcoEntity.verified_sub_administrative_area__v);
            dicFields.Add("spk_postalcode", dcrResultHcoEntity.verified_post_code__v);
            dicFields.Add("spk_subscribeexponent", dcrResultHcoEntity.verified_subscribe_exponent__v);
            dicFields.Add("spk_address", dcrResultHcoEntity.verified_address_line__v);
            dicFields.Add("spk_alternatename", dcrResultHcoEntity.verified_alternate_name_1__v);

            #endregion

            return (_dataverseService.UpdateHospitalAsync(id.Value, dicFields).GetAwaiterResult(), null);
        }
        private void DCRCrossBuLog(VeevaDcrReqDto requestDto, VeevaDcrRespDto respDto, CustomerRelation cr)
        {

            //IdentityUser firstUser = null;
            //Guid? deptId=Guid.Empty;
            //Dictionary<Guid, DepartmentDto> dicOrg=new Dictionary<Guid, DepartmentDto>();
            //if (vndApp != null)
            //{
            //    firstUser =_serviceProvider.GetService<IRepository<IdentityUser>>().FirstOrDefaultAsync(a => a.Id == vndApp.ApplyUserId).GetAwaiterResult();
            //    deptId = firstUser.GetProperty<Guid?>(EntityConsts.IdentityUser.MainDepartmentId);
            //    dicOrg = GetOrganizations();
            //}  
            List<VeevaDCRLog> logs = new List<VeevaDCRLog>();
            //hcp speaker hco 分开记录
            foreach (var item in requestDto.RequestType)
            {
                var log = new VeevaDCRLog();
                log.BusinessFormId = cr.Id;//年度更新调用dcr，记录vendor表ID
                log.RequestType = item;
                log.RequestID = Guid.Parse(requestDto.RequestId);

                log.ApplyUserDept = cr.BussinessUnitId.Value;
                log.ApplyUserDeptName = cr.BussinessUnit;
                log.ApplyUserBu = cr.BussinessUnitId.Value;
                log.ApplyUserBuName = cr.BussinessUnit;
                log.FlagType = FlagType.CrossBu;

                if (item == DcrRequestType.hcp_dcr.GetDescription())
                {
                    log.CustomerDCRID = requestDto.Data.hcp_dcr_entity?.customer_dcr_id;
                    log.DCRType = requestDto.Data.hcp_dcr_entity?.dcr_type;
                    log.RequestContent = JsonSerializer.Serialize(requestDto.Data.hcp_dcr_entity);
                    log.DCRResponseStatus = respDto.Data?.hcp_dcr?.hcp_dcr_response_status;
                    log.DCRResponseMessage = respDto.Data?.hcp_dcr?.message;
                    // 没有状态码，从父节点获取
                    if (string.IsNullOrEmpty(log.DCRResponseStatus))
                    {
                        log.DCRResponseStatus = respDto.ResponseStatus;
                        log.DCRResponseMessage = respDto.ResponseMessage;
                    }
                    log.DCRID = respDto.Data?.hcp_dcr?.dcr_id;
                }
                else if (item == DcrRequestType.speaker_dcr.GetDescription())
                {
                    log.CustomerDCRID = Guid.Parse(requestDto.Data.speaker_dcr_entity?.OriginalEntity?.customer_dcr_id);
                    log.DCRType = requestDto.Data.speaker_dcr_entity?.OriginalEntity?.dcr_type;
                    log.RequestContent = JsonSerializer.Serialize(requestDto.Data.speaker_dcr_entity);

                    log.DCRResponseStatus = respDto.Data?.speaker_dcr?.speaker_dcr_response_status;
                    log.DCRResponseMessage = respDto.Data?.speaker_dcr?.message;
                    // 没有状态码，从父节点获取
                    if (string.IsNullOrEmpty(log.DCRResponseStatus))
                    {
                        log.DCRResponseStatus = respDto.ResponseStatus;
                        log.DCRResponseMessage = respDto.ResponseMessage;
                    }
                    log.DCRID = respDto.Data?.speaker_dcr?.dcr_id;

                }
                else
                {
                    log.CustomerDCRID = requestDto.Data.hco_dcr_entity?.customer_dcr_id;
                    log.DCRType = requestDto.Data.hco_dcr_entity?.dcr_type;
                    log.RequestContent = JsonSerializer.Serialize(requestDto.Data.hco_dcr_entity);
                    log.DCRResponseStatus = respDto.Data?.hco_dcr?.hco_dcr_response_status;
                    log.DCRResponseMessage = respDto.Data?.hco_dcr?.message;
                    // 没有状态码，从父节点获取
                    if (string.IsNullOrEmpty(log.DCRResponseStatus))
                    {
                        log.DCRResponseStatus = respDto.ResponseStatus;
                        log.DCRResponseMessage = respDto.ResponseMessage;
                    }
                    log.DCRID = respDto.Data?.hco_dcr?.dcr_id;
                }
                logs.Add(log);
            }
            var repoVeevaDCRLog = LazyServiceProvider.LazyGetService<IVeevaDCRLogRepository>();
            repoVeevaDCRLog.InsertManyAsync(logs, true).GetAwaiterResult();
        }
    }
}
