CREATE PROCEDURE dbo.sp_BdMasterBudgets_ns
AS 
BEGIN
	select 
a.Id,
a.Code,
UPPER(soc.spk_NexBPMCode) as BuId,
a.Description,
UPPER(ss.spk_NexBPMCode) as OwnerId,
a.Capital,
a.BudgetAmount,
a.Status,
a.Remark,
a.ExtraProperties,
a.ConcurrencyStamp,
a.CreationTime,
UPPER(ss1.spk_NexBPMCode) as CreatorId,
a.LastModificationTime,
UPPER(ss2.spk_NexBPMCode) as LastModifierId,
a.IsDeleted,
a.DeleterId,
a.DeletionTime,
a.[Year]
into #BdMasterBudgets
from PLATFORM_ABBOTT_Dev.dbo.BdMasterBudgets_tmp a 
left join PLATFORM_ABBOTT_Dev.dbo.spk_organizationalmasterdata soc 
on a.BuId =soc.spk_BPMCode 
left join PLATFORM_ABBOTT_Dev.dbo.spk_staffmasterdata ss 
on a.OwnerId =ss.bpm_id 
left join PLATFORM_ABBOTT_Dev.dbo.spk_staffmasterdata ss1 
on a.CreatorId =ss1.bpm_id 
left join PLATFORM_ABBOTT_Dev.dbo.spk_staffmasterdata ss2 
on a.CreatorId =ss2.bpm_id 

    --删除表
    IF OBJECT_ID(N'PLATFORM_ABBOTT_Dev.dbo.BdMasterBudgets ', N'U') IS NOT NULL
	BEGIN
		drop table PLATFORM_ABBOTT_Dev.dbo.BdMasterBudgets
		select *
        into PLATFORM_ABBOTT_Dev.dbo.BdMasterBudgets from #BdMasterBudgets
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into PLATFORM_ABBOTT_Dev.dbo.BdMasterBudgets from #BdMasterBudgets
    -- select * from #vendor_tbl
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
END
