﻿using Abbott.SpeakerPortal.Bpm;
using Abbott.SpeakerPortal.Contracts.Bpm;
using Abbott.SpeakerPortal.Contracts.Common.PowerApp;
using Abbott.SpeakerPortal.Contracts.Integration.EpdOlineMeeting;
using Abbott.SpeakerPortal.Entities.Integration.Bpm;
using Abbott.SpeakerPortal.Entities.Purchase.PurBDApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Enums.Integration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Text.Json;
using System.Text.Json.Serialization;
using Abbott.SpeakerPortal.Contracts.Common;
using Microsoft.Extensions.DependencyInjection;

namespace Abbott.SpeakerPortal.AppServices.Bpm
{
    public class BpmOmService : SpeakerPortalAppService, IBpmOmService
    {
        /// <summary>
        /// The service provider
        /// </summary>
        private readonly IServiceProvider _serviceProvider;
        private readonly IScheduleJobLogService _jobLogService;
        public BpmOmService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _jobLogService = serviceProvider.GetService<IScheduleJobLogService>();
        }

        public async Task PullBpmOmLogAsync()
        {
            var log = _jobLogService.InitSyncLog("BpmSyncOmWorker", "BPM_OM");
            //获取数据时间  每两个小时执行一次 查询一天内的数据
            DateTime stateTime = DateTime.Now.AddDays(-1);
            string[] mothods = ["PostMeetingModifyInfo", "MeetingCancelVerification", "PostSettlementInfo", "PostStatusInfo"];
            var bpmLogs = await GetBpmOmLogsAsync(stateTime, mothods);
            if (bpmLogs == null || !bpmLogs.Any()) 
            {
                log.Remark = "没有拉取到数据";
                _jobLogService.SyncLog(log);
                return;
            }
            log.Remark = $"查询时间的24h(一天)内数据量：{bpmLogs.Count}";
            try
            {
                var nexBpmLogRepository = LazyServiceProvider.LazyGetService<IBpmTpmInterfaceLogRepository>();
                var prQuery = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
                var nexBpmLogQuery = await nexBpmLogRepository.GetQueryableAsync();
                var maxCreateTime = nexBpmLogQuery.Any()? nexBpmLogQuery.Max(a => a.CreationTime).AddMinutes(-30): DateTime.Now.AddHours(-2);//防止批量插入时间不同 少30分钟（理论上不会存在30分钟了还没用Insert完）
                await InsertNexBpmLogsAsync(nexBpmLogRepository, bpmLogs);

                BpmInterfaceLogState[] states = [BpmInterfaceLogState.Unprocessed, BpmInterfaceLogState.Failed];
                var nexBpmLogs = nexBpmLogQuery.Where(a => states.Contains(a.State) && a.CreationTime >= maxCreateTime).ToList();//查询处理失败（每两小时执行一次，上次失败的数据）和未处理的数据

                log.Remark += $",本次增量数：{nexBpmLogs.Where(a=>a.State == BpmInterfaceLogState.Unprocessed).Count()},上次处理失败数：{nexBpmLogs.Where(a => a.State == BpmInterfaceLogState.Failed).Count()}";

                var integrationOmAppService = LazyServiceProvider.LazyGetService<IIntegrationOmAppService>();
                List<BpmTpmInterfaceLog> updateBpmTpmInterfaceLogs = new List<BpmTpmInterfaceLog>();

                var outsideNexbpm = nexBpmLogs.Where(a => string.IsNullOrWhiteSpace(a.Scenes) || !a.Scenes.StartsWith("P")).ToList();//明显不存在在NexBpm 的数据 
                if (outsideNexbpm.Count > 0)
                {
                    outsideNexbpm.ForEach(a => a.State = BpmInterfaceLogState.NotNeeded);
                    updateBpmTpmInterfaceLogs.AddRange(outsideNexbpm);
                }

                var inNexBpmLogs = nexBpmLogs.Where(a => !string.IsNullOrWhiteSpace(a.Scenes) && a.Scenes.StartsWith("P")).ToList();//大概率存在NexBpm 的数据

                var prCodes = inNexBpmLogs.Select(a =>
                {
                    var applicationCode = "";
                    if (a.Scenes.Length >= 11)
                    {
                        applicationCode = a.Scenes.Substring(0, 11);
                    }
                    else
                    {
                        applicationCode = a.Scenes;
                    }
                    return new { logs = a, applicationCode = applicationCode };
                });//获取所有PR单号

                var prs = prQuery.Where(a => prCodes.Select(x => x.applicationCode).Contains(a.ApplicationCode)).Select(a => a.ApplicationCode).ToList();
                var notInNexBpm = prCodes.Where(a => !prs.Contains(a.applicationCode)).Select(a => a.logs).ToList();//确实不存在NexBpm 的数据
                if (notInNexBpm.Any())
                {
                    notInNexBpm.ForEach(a => a.State = BpmInterfaceLogState.NotNeeded);
                    updateBpmTpmInterfaceLogs.AddRange(outsideNexbpm);
                }

                var inNexBpm = prCodes.Where(a => prs.Contains(a.applicationCode)).Select(a => a.logs).ToList();//确实存在的数据

                foreach (var mothod in mothods)
                {
                    var nexBpms = inNexBpm.Where(a => a.Method == mothod).OrderBy(a => a.Date).ToList();//处理确实存在的数据
                    if (!nexBpms.Any())
                        continue;
                    switch (mothod)
                    {
                        case "PostMeetingModifyInfo":
                            updateBpmTpmInterfaceLogs.AddRange(await RunPostMeetingModifyInfoAsync(integrationOmAppService, nexBpms));
                            break;
                        case "MeetingCancelVerification":
                            //原MeetingCancelVerification无法调通, 经查会有Method="PostSettlementInfo"的记录,且记录中isSuccess='0',且ErrorMsg中包含方法名"MeetingCancelVerification"
                            //updateBpmTpmInterfaceLogs.AddRange(await RunMeetingCancelVerificationAsync(integrationOmAppService, nexBpms));
                            break;
                        case "PostSettlementInfo":
                            updateBpmTpmInterfaceLogs.AddRange(await RunPostSettlementInfoAsync(integrationOmAppService, nexBpms));
                            break;
                        case "PostStatusInfo":
                            updateBpmTpmInterfaceLogs.AddRange(await RunPostStatusInfoAsync(integrationOmAppService, nexBpms));
                            break;
                    }
                }
                await nexBpmLogRepository.UpdateManyAsync(updateBpmTpmInterfaceLogs);
            }
            catch (Exception ex)
            {
                log.IsSuccess = true;
                log.Remark = ex.Message;
            }
            finally
            {
                _jobLogService.SyncLog(log);
            }
        }

        #region Method 处理
        /// <summary>
        /// PostStatusInfo
        /// </summary>
        /// <param name="integrationOmAppService"></param>
        /// <param name="bpmTpmInterfaceLogs"></param>
        /// <returns></returns>
        private async Task<List<BpmTpmInterfaceLog>> RunPostStatusInfoAsync(IIntegrationOmAppService integrationOmAppService, List<BpmTpmInterfaceLog> bpmTpmInterfaceLogs)
        {
            foreach (var item in bpmTpmInterfaceLogs)
            {
                if (item.IsSuccess == "0")
                {
                    item.State = BpmInterfaceLogState.NotNeeded;
                    continue;
                }
                var result = await integrationOmAppService.NexBpmUpdateMeetingStatus(new NexBpmMeetingStatusUpdateRequestDto { SerialNumberPr = item.Scenes.Substring(0, 11), Status = "1002" });
                if (result.Success)
                    item.State = BpmInterfaceLogState.Success;
                else
                    item.State = BpmInterfaceLogState.Failed;
            }
            return bpmTpmInterfaceLogs;
        }

        /// <summary>
        /// PostSettlementInfo
        /// </summary>
        /// <param name="integrationOmAppService"></param>
        /// <param name="bpmTpmInterfaceLogs"></param>
        /// <returns></returns>
        private async Task<List<BpmTpmInterfaceLog>> RunPostSettlementInfoAsync(IIntegrationOmAppService integrationOmAppService, List<BpmTpmInterfaceLog> bpmTpmInterfaceLogs)
        {
            foreach (var item in bpmTpmInterfaceLogs)
            {
                //原MeetingCancelVerification无法调通, 经查会有Method="PostSettlementInfo"的记录,且记录中isSuccess='0',且ErrorMsg中包含方法名"MeetingCancelVerification"
                if (item.IsSuccess == "0" && item.ErrorMsg.Contains("MeetingCancelVerification"))
                {
                    var revokeResult = await integrationOmAppService.NexBpmRevokeMeeting(new NexBpmMeetingRevokeRequestDto { SerialNumberPr = item.Scenes });
                    if (revokeResult.Success)
                        item.State = BpmInterfaceLogState.Success;
                    else
                        item.State = BpmInterfaceLogState.Failed;
                    continue;
                }
                else if(item.IsSuccess == "0") {
                    item.State = BpmInterfaceLogState.NotNeeded;
                    continue;
                }

                var settleMeetingRequest = JsonSerializer.Deserialize<BpmSettlementMeetingDto>(item.Parameter);
                var request = ObjectMapper.Map<BpmSettlementMeetingDto, NexBpmMeetingSettlementRequestDto>(settleMeetingRequest);
                var result = await integrationOmAppService.NexBpmSettleMeeting(request);
                if (result.Success)
                    item.State = BpmInterfaceLogState.Success;
                else
                    item.State = BpmInterfaceLogState.Failed;
            }
            return bpmTpmInterfaceLogs;
        }

        /// <summary>
        /// MeetingCancelVerification
        /// </summary>
        /// <param name="integrationOmAppService"></param>
        /// <param name="bpmTpmInterfaceLogs"></param>
        /// <returns></returns>
        private async Task<List<BpmTpmInterfaceLog>> RunMeetingCancelVerificationAsync(IIntegrationOmAppService integrationOmAppService, List<BpmTpmInterfaceLog> bpmTpmInterfaceLogs)
        {
            foreach (var item in bpmTpmInterfaceLogs)
            {
                if (item.IsSuccess == "0")
                {
                    item.State = BpmInterfaceLogState.NotNeeded;
                    continue;
                }
                var result = await integrationOmAppService.NexBpmRevokeMeeting(new NexBpmMeetingRevokeRequestDto { SerialNumberPr= item.Scenes });
                
                if (result.Success)
                    item.State = BpmInterfaceLogState.Success;
                else
                    item.State = BpmInterfaceLogState.Failed;
            }
            return bpmTpmInterfaceLogs;
        }

        /// <summary>
        /// PostMeetingModifyInfo
        /// </summary>
        /// <param name="integrationOmAppService"></param>
        /// <param name="bpmTpmInterfaceLogs"></param>
        /// <returns></returns>
        private async Task<List<BpmTpmInterfaceLog>> RunPostMeetingModifyInfoAsync(IIntegrationOmAppService integrationOmAppService, List<BpmTpmInterfaceLog> bpmTpmInterfaceLogs) 
        {
            foreach (var item in bpmTpmInterfaceLogs)
            {
                if (item.IsSuccess == "0") 
                {
                    item.State = BpmInterfaceLogState.NotNeeded;
                    continue;
                }
                var result = await integrationOmAppService.NexBpmUpdateMeetingStatus(new NexBpmMeetingStatusUpdateRequestDto() {SerialNumberPr = item.Scenes.Substring(0, 11),Status="1001" });
                var bpmUpdateMeetingRequest = JsonSerializer.Deserialize<List<BpmMeetingUpdateRequestDto>>(item.Parameter);
                //解析 item.Parameter 并 mapp 到NexBpmMeetingUpdateRequestDto
                var nexBpmUpdateMeetingRequest = bpmUpdateMeetingRequest.Select(a=>new NexBpmMeetingUpdateRequestDto {
                    SerialNumberPr = a.SerialNumber,
                    MeetingAddress = a.MeetingAddress,
                    MeetingDate = a.MeetingDate,
                    MeetingType = a.MeetingType,
                    SpeakerDetails =  a.Speakers.Select(x=>new NexBpmMeetingSpeakerItemDto {
                        No = x.No,
                        VendorCode = x.VendorCode,
                        Executive = x.Executive,
                        ExecutiveMail = x.ExecutiveMail,
                    }).ToList(),
                }).ToList();
                var updateResult = await integrationOmAppService.NexBpmUpdateMeeting(nexBpmUpdateMeetingRequest.FirstOrDefault());
                if (result.Success && updateResult.Success)
                    item.State = BpmInterfaceLogState.Success;
                else
                    item.State = BpmInterfaceLogState.Failed;
            }
            return bpmTpmInterfaceLogs;
        }
        #endregion

        /// <summary>
        /// 增量 查询Bpm 表数据
        /// </summary>
        /// <param name="stateTime"></param>
        /// <returns></returns>
        private async Task<List<TTpmInterfaceLog>> GetBpmOmLogsAsync(DateTime stateTime, string[] mothods) 
        {
            var bpmLogQuery = await LazyServiceProvider.LazyGetService<ITpmInterfaceLogRepository>().GetQueryableAsync();
            var bpmLogs = bpmLogQuery.Where(a=>a.Date >= stateTime && mothods.Contains(a.Method)).ToList();
            return bpmLogs;
        }

        /// <summary>
        /// 存入NexBpm Log 表
        /// </summary>
        /// <param name="nexBpmLogRepository"></param>
        /// <param name="bpmLogs"></param>
        /// <returns></returns>
        private async Task InsertNexBpmLogsAsync(IBpmTpmInterfaceLogRepository nexBpmLogRepository, List<TTpmInterfaceLog> bpmLogs) 
        {
            var nexBpmLogs = ObjectMapper.Map<List<TTpmInterfaceLog>, List<BpmTpmInterfaceLog>>(bpmLogs);
            var nexBpmLogQuery = await nexBpmLogRepository.GetQueryableAsync();
            var bpmIds = bpmLogs.Select(a => a.Id).Distinct();
            //存在nexBpmLogQuery 中的数据
            var existingBpmIds = nexBpmLogQuery.Where(a => bpmIds.Contains(a.BpmId)).Select(a => a.BpmId).ToList();

            //不存在nexBpmLogQuery 中的数据
            var noNexBpmIds = bpmIds.Except(existingBpmIds).ToList();
            nexBpmLogs = nexBpmLogs.Where(a => noNexBpmIds.Contains(a.BpmId)).ToList();
            if (nexBpmLogs.Any())
                await nexBpmLogRepository.InsertManyAsync(nexBpmLogs,true);
        }
    }
}
