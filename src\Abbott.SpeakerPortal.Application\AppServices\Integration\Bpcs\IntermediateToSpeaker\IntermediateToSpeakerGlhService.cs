﻿using Abbott.SpeakerPortal.Contracts.Integration.Bpcs;
using Abbott.SpeakerPortal.Entities.Integration.Dspot;
using Abbott.SpeakerPortal.IntermediateDB.Models;
using Abbott.SpeakerPortal.IntermediateDB.Repositories;
using Abbott.SpeakerPortal.Utils;

using EFCore.BulkExtensions;

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

using Volo.Abp.Domain.Repositories;
using Volo.Abp.Guids;

namespace Abbott.SpeakerPortal.AppServices.Integration.Bpcs
{
    public class IntermediateToSpeakerGlhService : SpeakerPortalAppService, IIntermediateToSpeakerGlhService
    {
        /// <summary>
        /// The logger
        /// </summary>
        private readonly ILogger<IntermediateToSpeakerGlhService> _logger;

        public IntermediateToSpeakerGlhService(IServiceProvider serviceProvider)
        {
            _logger = serviceProvider.GetService<ILogger<IntermediateToSpeakerGlhService>>();
        }

        protected BpcsGlh GetTarByOri(Dictionary<string, BpcsGlh> tarDict, Glh ori)
        {
            if (tarDict?.Any() != true)
            {
                return default;
            }

            tarDict.TryGetValue($"{ori.Lhldgr}{ori.Lhbook}{ori.Lhyear}{ori.Lhperd}{ori.Lhjnen}{ori.Lhjnln}", out BpcsGlh bpcsGlh);

            return bpcsGlh;
        }

        public async Task<int> SyncTableIncrement()
        {
            int recordCount = 0;
            try
            {
                //查询TEntityOri
                var repoOri = LazyServiceProvider.GetService<IIntermediateGlhRepository>();
                if (repoOri == null)
                {
                    _logger.LogError($"SyncTableIncrement() repoOri is null");
                    return recordCount;
                }
                var query = await repoOri.GetQueryableAsync();
                //拉取7天的数据进行对比
                var listOri = query.Where(a => a.UpdateTime >= DateTime.Today.AddDays(-7)).ToList();
                recordCount = listOri.Count;
                if (!listOri.Any())
                {
                    return recordCount;
                }

                var lhldgrs = listOri.Select(a => a.Lhldgr).Distinct();
                var lhbooks = listOri.Select(a => a.Lhbook).Distinct();
                var lhyears = listOri.Select(a => a.Lhyear).Distinct();
                var lhperds = listOri.Select(a => a.Lhperd).Distinct();
                var lhjnens = listOri.Select(a => a.Lhjnen).Distinct();
                var lhjnlns = listOri.Select(a => a.Lhjnln).Distinct();

                //1,查询有哪些Ori已存在表BpcsTar
                var repoTar = LazyServiceProvider.GetService<IBpcsGlhRepository>();
                var queryTar = await repoTar.GetQueryableAsync();
                var exitsTar = queryTar.Where(a => a.UpdateTime >= DateTime.Today.AddDays(-7)).ToList();
                //var exitsTar = queryTar.Where(a => lhldgrs.Contains(a.Lhldgr) && lhbooks.Contains(a.Lhbook) && lhyears.Contains(a.Lhyear) && lhperds.Contains(a.Lhperd) && lhjnens.Contains(a.Lhjnen) && lhjnlns.Contains(a.Lhjnln)).ToList();

                var guidGenerator = LazyServiceProvider.LazyGetService<IGuidGenerator>();
                var updateEntities = new List<BpcsGlh>();
                var addEntities = new List<BpcsGlh>();

                var exitsTarDict = exitsTar.ToDictionary(a => $"{a.Lhldgr}{a.Lhbook}{a.Lhyear}{a.Lhperd}{a.Lhjnen}{a.Lhjnln}", a => a);

                //2,已存在表Tar的，比较Tar是否有更新，有则Update，无则不保存
                foreach (var item in listOri)
                {
                    var exitsEntity = GetTarByOri(exitsTarDict, item);
                    if (exitsEntity != null)
                    {
                        //比较Tar是否有更新，有则Update，无则不保存
                        if (!item.PropertyEqualAll(exitsEntity))
                        {
                            var updateEntity = item.AssignPropertiesTo(exitsEntity);
                            if (updateEntity != null)
                            {
                                updateEntities.Add(updateEntity);
                            }
                        }
                    }
                    else
                    {
                        //3,未存在表BpcsGlh的，Insert BpcsGlh
                        var entity = ObjectMapper.Map<Glh, BpcsGlh>(item);

                        //手动设置Id
                        var manualSetId = entity as IManualSetId<Guid>;
                        if (manualSetId != null)
                            manualSetId.SetId(guidGenerator.Create());

                        addEntities.Add(entity);
                    }
                }

                //更新
                if (updateEntities.Any())
                {
                    //await repoTar.UpdateManyAsync(updateEntities, repoAutoSave);
                    var context = await repoTar.GetDbContextAsync();
                    await context.BulkUpdateAsync(updateEntities);
                }
                //插入
                if (addEntities.Any())
                {
                    //await repoTar.InsertManyAsync(addEntities, repoAutoSave);
                    var context = await repoTar.GetDbContextAsync();
                    await context.BulkInsertAsync(addEntities);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"SyncTableIncrement() Exception: {ex}");
                throw;
            }
            return recordCount;
        }


    }
}