--M3-01 
--基本规则：查询[AUTO_BIZ_T_ProcurementApplication_Info]得到历史的采购申请单据，
--以单据号Join[Form_6e32f7dd7c4e49aea79949009c3bf7ae]得到采购申请状态，
--以ProcInstId join [AUTO_BIZ_T_ProcurementApplication_Info_PR]得到对应的采购申请明细信息
--*如下基于ID匹配出NexBPMID均需要考虑：
--	若原ID对应数据已不存在是否会影响显示，
--	若会影响则是否需要迁移部分无效的字典值并仅在查询时使用，在提交新PR时禁用
use PLATFORM_ABBOTT;



-- drop table GLOBALXmlContent_temp
SELECT 
	 a.ProcInstId,
	 a.expenseCategory_Value,
	 XmlContent.query('/root/QuoteAttachmentGrid/row') AS row_quote_up_id,
	 XmlContent.query('/root/OthersAttachmentGrid/row') AS row_other_up_id,
	 XmlContent.query('/root/approvalHistoryGrid/row') AS row_history_up_id,
	 XmlContent.query('/root/PRGridPanel/row')       as PRGridPanel,
	 XmlContent.value('(/root/ProcurementApplication_BudgetInfoBlock_MainStore/Projecttype_Value)[1]', 'nvarchar(255)') ProjectType,
	 XmlContent.value('(/root/ProcurementApplication_SelfHostedBlock_MainStore/checkbox2)[1]', 'nvarchar(255)') checkbox2,
	 XmlContent.value('(/root/ProcurementApplication_SelfHostedBlock_MainStore/checkbox3)[1]', 'nvarchar(255)') checkbox3,
	 XmlContent.value('(/root/ProcurementApplication_SelfHostedBlock_MainStore/checkbox4)[1]', 'nvarchar(255)') checkbox4,
	 XmlContent.value('(/root/ProcurementApplication_SelfHostedBlock_MainStore/checkbox5)[1]', 'nvarchar(255)') checkbox5,
	 XmlContent.value('(/root/ProcurementApplication_SelfHostedBlock_MainStore/checkbox7)[1]', 'nvarchar(255)') checkbox7,
	 XmlContent.value('(/root/ProcurementApplication_SelfHostedBlock_MainStore/SelfActivityLeader)[1]', 'nvarchar(255)') SelfActivityLeader,
	 XmlContent.value('(/root/ProcurementApplication_ThirdPartyBlock_MainStore/ThirdActivityLeader)[1]', 'nvarchar(255)') ThirdActivityLeader,
	 XmlContent.value('(/root/ProcurementApplication_ThirdPartyBlock_MainStore/ThirdConferenceOrganizer)[1]', 'nvarchar(255)') ThirdConferenceOrganizer,
	 XmlContent.value('(/root/ProcurementApplication_SelfHostedBlock_MainStore/SelfMeetingTime)[1]', 'nvarchar(255)') SelfMeetingTime,
	 XmlContent.value('(/root/ProcurementApplication_ThirdPartyBlock_MainStore/ThirdMeetingTime)[1]', 'nvarchar(255)') ThirdMeetingTime,
	 XmlContent.value('(/root/ProcurementApplication_SelfHostedBlock_MainStore/SelfMeetingName)[1]', 'nvarchar(255)') SelfMeetingName,
	 XmlContent.value('(/root/ProcurementApplication_ThirdPartyBlock_MainStore/ThirdMeetingName)[1]', 'nvarchar(255)') ThirdMeetingName,
	 XmlContent.value('(/root/ProcurementApplication_SelfHostedBlock_MainStore/SelfParticipateNumber)[1]', 'nvarchar(255)') SelfParticipateNumber,
	 XmlContent.value('(/root/ProcurementApplication_SelfHostedBlock_MainStore/checkbox6)[1]', 'nvarchar(255)') checkbox6,
	 XmlContent.value('(/root/ProcurementApplication_ThirdPartyBlock_MainStore/ThirdObjectives)[1]', 'nvarchar(255)') ThirdObjectives,
	 XmlContent.value('(/root/ProcurementApplication_SelfHostedBlock_MainStore/SelfProfessionalField)[1]', 'nvarchar(255)') SelfProfessionalField,
	 XmlContent.value('(/root/ProcurementApplication_hiddenBlock_MainStore/IsSpecialOperate)[1]', 'nvarchar(255)') IsSpecialOperate,
	 XmlContent.value('(/root/ProcurementApplication_ThirdPartyBlock_MainStore/Other)[1]', 'nvarchar(255)') Other,
	 XmlContent.value('(/root/ProcurementApplication_ThirdPartyBlock_MainStore/checkbox1)[1]', 'nvarchar(255)') checkbox1,
	 XmlContent.value('(/root/ProcurementApplication_ThirdPartyBlock_MainStore/checkbox22)[1]', 'nvarchar(255)') checkbox22
into GLOBALXmlContent_temp
from  ODS_T_FORMINSTANCE_GLOBAL_bak j,
      (select DISTINCT  ProcInstId ,expenseCategory_Value from ODS_AUTO_BIZ_T_ProcurementApplication_Info) a
where   a.ProcInstId = j.ProcInstId  
;


CREATE  index procinstid  ON GLOBALXmlContent_temp (procinstid);
-- CREATE  index procinstid  ON ODS_T_FORMINSTANCE_GLOBAL_bak (procinstid);

 
 
-- SELECT count(*) from  GLOBALXmlContent_temp ; 
-- CREATE  index index_procinstid  ON ODS_AUTO_BIZ_T_ProcurementApplication_Info (procinstid);
-- CREATE  index index_procinstid  ON ODS_T_FORMINSTANCE_GLOBAL (procinstid);

---------------------approvalHistoryGrid 准备
select   
	a.ProcInstId,
	RowData.value('(approvalLevel)[1]', 'nvarchar(50)') AS approvalLevel,
    RowData.value('(approvalPersonEmpId)[1]', 'nvarchar(50)') AS approvalPersonEmpId,
    RowData.value('(action)[1]', 'nvarchar(50)') AS action,
    RowData.value('(approvalTime/text())[1]', 'nvarchar(50)') AS approvalTime
	into #XmlContent_approvalHistoryGrid
from  
 GLOBALXmlContent_temp  a
CROSS APPLY row_history_up_id.nodes('/row') AS XMLTable(ROWDATA);	
CREATE  index #ProcInstId  ON #XmlContent_approvalHistoryGrid (ProcInstId);


--CREATE  index index_ProcInstId  ON ODS_Form_6e32f7dd7c4e49aea79949009c3bf7ae (ProcInstId);
--CREATE  index index_ProcInstId  ON ODS_AUTO_BIZ_T_ProcurementApplication_Info (ProcInstId);

-----------------------
-- DROP  table  #ApprovedDate ;

SELECT 
	t2.*,
	case 
	  when [action] =N'作废'  AND processStatus=N'发起人终止' AND second_action =N'撤回' THEN approvalTime2
	  when [action] =N'撤回'  AND processStatus=N'重发起'                              THEN approvalTime2
	  when processStatus     in (N'供应商确认',N'审批完毕，等待关闭',N'等待关闭',N'完成')              then FinishDate
	  when [action] =N'通过'  and  Scenes is not null                                 then approvalTime
	end approvalTime3
into #ApprovedDate 
from (
	SELECT 
		t1.*,
		a2.Scenes,
		a1.FinishDate,
		max(case when desc_time =2 then [action] end) over(partition by  t1.ProcInstId order by  t1.ProcInstId ) as second_action,
		first_value (approvalTime1) over(partition by  t1.ProcInstId order by  t1.approvalTime1 desc ) as approvalTime2 
	from (
		SELECT  
			a.*,c.serialNumber,
			b.processStatus,
			ROW_NUMBER () over(partition by a.ProcInstId order by a.approvalTime desc ) as desc_time,
			
			CASE 
				WHEN  
					 ApprovalLevel in (N'Expense循环审批',N'财务审核',N'财务总监审批',N'GM审批') and
					 b.processStatus in (N'发起人终止',N'重发起') 
				THEN approvalTime END
			as approvalTime1
		from 
			ODS_AUTO_BIZ_T_ProcurementApplication_Info c
		join 
			#XmlContent_approvalHistoryGrid a
			on c.ProcInstId =   a.ProcInstId
		left join
			ODS_Form_6e32f7dd7c4e49aea79949009c3bf7ae b  -- 561664
			on a.ProcInstId =b.ProcInstId 
	) t1 --WHERE t1.desc_time <= 2
	left join (
		SELECT  max(FinishDate) FinishDate,ProcInstID from ods_T_PROCESS_Historys group by ProcInstID
	) a1
	on t1.ProcInstId = a1.ProcInstId
	left join (
		SELECT     
			DISTINCT  Scenes   
		from PLATFORM_ABBOTT_Stg.dbo.ODS_T_TPM_InterfaceLog
		WHERE [Method] ='MeetingCancelVerification' AND IsSuccess =1 
	) a2
	on t1.serialNumber = a2.Scenes
) t2 
WHERE t2.desc_time  = 1;
CREATE  index #index_ProcInstId  ON #ApprovedDate (ProcInstId);

--- ApprovedDate 验证
--SELECT  
-- 	gg.serialNumber,
-- 	gg.ProcInstId,
--	gg.approvalTime3,
--
--	pp.ApprovedDate 
--
--from  #ApprovedDate gg 
--left join 
--PurPRApplications pp 
--on gg.serialNumber =pp.ApplicationCode
--where pp.ApprovedDate  is not null and pp.ApprovedDate <>'NULL'

--SELECT  DISTINCT ApprovedDate from PurPRApplications
 

----------------------------------------------------------------------------
-----------------SupportFiles 准备
SELECT  
	t.ProcInstId,
	x.row.value('(up_Id)[1]','nvarchar(100)') as up_Id  
into #SupportUp_Id
from  GLOBALXmlContent_temp t
CROSS APPLY t.row_quote_up_id.nodes('/row') AS x(row)
;
CREATE  index #up_Id  ON #SupportUp_Id (up_Id);
--CREATE  index index_BPMId  ON Attachments (BPMId);

----- SupportFiles 
-- drop table #SupportFiles
SELECT 
	t.ProcInstId,
	STRING_AGG(t.up_Id, ', ')  AS up_Id_SupportFiles,  
	STRING_AGG(cast(a.id as nvarchar(100)), ', ') WITHIN GROUP (ORDER BY a.id  ) AS SupportFiles 
into #SupportFiles
from
	#SupportUp_Id t
left join 
 	Attachments  a
on a.BPMId =t.up_Id and a.BPMId <>''
group by ProcInstId
;
CREATE  index #ProcInstId  ON #SupportFiles (ProcInstId);

--SELECT * from #SupportFiles

--select 
--	a.ProcInstId,
--	b.up_Id_SupportFiles,
--	b.SupportFiles,
--	c.SupportFiles 
--
--from PurPRApplications_tmp a
--,PurPRApplications c
--,#SupportFiles b 
--
--where 
--a.ProcInstId=b.ProcInstId 
--and a.id = c.id
-- and b.up_Id_SupportFiles <> c.SupportFiles


---------------------------------------------------
-----------------AdditionalFiles 准备
SELECT  
	t.ProcInstId,
	x.row.value('(up_Id)[1]','nvarchar(100)') as up_Id  
into #OtherUp_Id
from  GLOBALXmlContent_temp t
CROSS APPLY t.row_other_up_id.nodes('/row') AS x(row)
;
CREATE  index #up_Id  ON #OtherUp_Id (up_Id);

--SELECT * from #OtherUp_Id
----- AdditionalFiles 

SELECT 
	t.ProcInstId,
	STRING_AGG(t.up_Id, ', ') AS Other_up_id_AdditionalFiles ,
	STRING_AGG(cast(a.id as nvarchar(100)), ', ') WITHIN GROUP (ORDER BY a.id) AS AdditionalFiles 
into #AdditionalFiles
from
	#OtherUp_Id t
left join 
 	Attachments  a
on a.BPMId =t.up_Id and a.BPMId <>''
group by ProcInstId
;
CREATE  index #ProcInstId  ON #AdditionalFiles (ProcInstId);


--SELECT * from #AdditionalFiles

--------------------------------附件完成

--
--SELECT ProcInstId,count(*) from GLOBALXmlContent_temp group by ProcInstId HAVING count(*)>1
--
--SELECT * from GLOBALXmlContent_temp where ProcInstId =223606
-- drop table  #GLOBALXmlContent_Value
---------
SELECT 
	j.ProcInstId,
	tt.spk_code as ProjectType,
	jj.spk_code as OrganizerNature,
	oo.spk_code as SponsorshipType,
	case 
		when pp.spk_NexBPMCode is not null then j.SelfActivityLeader 
		when aa.spk_NexBPMCode is not null then j.ThirdActivityLeader 
	end as ActiveLeader,
	case 
		when j.SelfActivityLeader is not null and j.SelfActivityLeader <>'' THEN SelfActivityLeader
		when j.ThirdActivityLeader is not null and j.ThirdActivityLeader <>'' THEN ThirdActivityLeader
	end  as ActiveLeader1,
	case 
		when pp.spk_NexBPMCode is not null then j.SelfMeetingName 
		when aa.spk_NexBPMCode is not null then j.ThirdMeetingName 
	end as MeetingName,
	case 
		when  j.SelfMeetingName is not null and j.SelfMeetingName  <>'' then  j.SelfMeetingName
		when  j.ThirdMeetingName is not null and j.ThirdMeetingName <>'' then j.ThirdMeetingName
	end  MeetingName1,
	case 
		when pp.spk_NexBPMCode is not null then j.SelfMeetingTime 
		when aa.spk_NexBPMCode is not null then j.ThirdMeetingTime 
	end as MeetingDate,
		case 
		when  j.SelfMeetingTime is not null and  j.SelfMeetingTime<>'' then  j.SelfMeetingTime
		when  j.ThirdMeetingTime  is not null and j.ThirdMeetingTime  <>'' then j.ThirdMeetingTime
	end  MeetingDate1,
	j.ThirdConferenceOrganizer as OrganizerName,
	j.SelfParticipateNumber	   as NumberOfProfessionals,
	case when j.checkbox6 ='true' then 1 else 0 end ChoiceReason,
	j.ThirdObjectives 		   as SupportReason,
	j.SelfProfessionalField    as AttendeeExpertise,
	j.IsSpecialOperate ,
	j.Other
into #GLOBALXmlContent_Value
from 
	GLOBALXmlContent_temp  j
left join
 	spk_dictionary tt
on j.ProjectType = tt.spk_BPMCode and tt.spk_type =N'项目类型'
left join
 	spk_dictionary jj
on case when checkbox1='true' then N'非营利性的非医疗保健机构（如医学会、基金会）' when checkbox22='true' then N'其他' end  = TRIM(jj.spk_Name) 
	and jj.spk_type =N'主办方性质'
left join
 	spk_dictionary oo
on 
	case when checkbox2='true' then N'赞助个人参加雅培自办会议' 
		 when checkbox3='true' then N'赞助个人参加第三方会议中的雅培卫星会/专场' 
		 when checkbox4='true' then N'赞助个人参加第三方会议（仅限医疗器械、诊断事业部以外的事业部或部门）' 
		 when checkbox5='true' then N'赞助个人参加第三方组织的医疗程序培训（仅限医疗器械、诊断事业部的事业部）' 
		 when checkbox7='true' then N'不适用' 
		 end  = TRIM(oo.spk_Name) 
	and oo.spk_type =N'赞助类型'	
left join  spk_consume pp 
	on j.expenseCategory_Value=pp.spk_BPMCode and pp.spk_name in (N'赞助个人参加专业教育/活动 - 国内',N'雅培自办会议' )
left join  spk_consume aa 
	on j.expenseCategory_Value=aa.spk_BPMCode and aa.spk_name in (N'第三方会议支持')
;
CREATE  index #procinstid  ON #GLOBALXmlContent_Value (procinstid);

-- SELECT * from  #GLOBALXmlContent_Value
	
--- costCenterCode
-- drop table #ProcurementCostCenter

select  
	DISTINCT 
	procinstid,
	costCenterCode ,
	costCenter 
	into #ProcurementCostCenter
from  ODS_AUTO_BIZ_T_ProcurementApplication_Info_PR;

CREATE  index #procinstid  ON #ProcurementCostCenter (procinstid);

SELECT  * from  #ProcurementCostCenter ;

--------------vendorID 准备-----------

--  drop table #vendorID	;

SELECT 
 * 
into #vendorID	
from (
	SELECT 
		a.id,
		TRIM(b.VEMLAD) VEMLAD,
		a.VENDOR,
		a.VCMPNY,
		row_number() over(partition by TRIM(b.VEMLAD) order by case when VCMPNY =20 then 0 else VCMPNY   end)  as rn
	from 
	     ODS_BPCS_AVM a ,
		 ODS_BPCS_PMFVM b  
	where 
		a.VCMPNY=b.VMCMPY 
		and a.VENDOR=b.VNDERX 
		AND b.VEMLAD is not null 
		and b.VEMLAD <> ''
	 	and VNSTAT='A'
		AND VTYPE LIKE 'NE%'
) t1 where rn =1
;
SELECT count(*) from #vendorID
;


-------验证数据开始
 
 
基本规则：查询[AUTO_BIZ_T_ProcurementApplication_Info]得到历史的采购申请单据，
以单据号Join[Form_6e32f7dd7c4e49aea79949009c3bf7ae]得到采购申请状态，
以ProcInstId join [AUTO_BIZ_T_ProcurementApplication_Info_PR]得到对应的采购申请明细信息
--- PurPRApplications

--  drop table  #purprapplications ;

SELECT   
 	a.serialNumber as ApplicationCode
 	,a.ProcInstId
 	,case 
 		when trim(b.processStatus) in ('完成') 													then 10
 		when trim(b.processStatus) in ('供应商确认') 												then 3
  		when trim(b.processStatus) in ('重发起','审批中') 											then 5
 		when trim(b.processStatus) in ('审批完毕','等待关闭','财务关闭') and g.ProcInstId is not null 	then g.is_sent_status
		when trim(b.processStatus) in ('发起人终止') and trim(f.operate) ='作废' 					then 6
		when trim(b.processStatus) in ('发起人终止') and trim(f.operate) ='拒绝' 					then 4
		when trim(b.processStatus) in ('发起人终止') and f.SerialNumber is not null  			 	then 6
 	end as Status
	,o.spk_NexBPMCode 				as ApplyUserId
	,a.applicationDate 				as ApplyTime
	,p.spk_NexBPMCode 				as ApplyUserBu
	,q.spk_NexBPMCode 				as CostCenter
	,kk.id 							as BudgetId
	,a.MeetingTheme 				as MeetingTitle
	,case when UPPER(trim(a.Pilot)) ='TRUE' THEN 1 ELSE 0 END 					AS IsEsignUsed
	,case when ProPushObject=N'Esign' 	   then N'E-Sign' when ProPushObject='Online' then N'雅会议（online meeting）'end  AS PushSystem
	,oo.spk_NexBPMCode				as [AgentId] 
	,h.id							as HostVendorId	 
	,u.spk_NexBPMCode				as [ExpenseType]  	--TODO type 取值 是id  再次检查
	,qq.spk_NexBPMCode				as [BudgetRegion]   
	,ww.spk_NexBPMCode				as [ProductIds]  	--TODO 以该ID匹配 
	,n.spk_NexBPMCode    			as [CompanyId]  	--以该编码匹配至公司主数据的"公司编码"得到ID
	,a.[PRTotalAmount]				as [TotalAmount]  
	,a.[remark]						as [Remark] 
	,a.[lateRemark]					as [PrLateDescription]   
	,a.[launchactiveCity]			as [ActiveLaunchCityId]  --TODO
	,a.[activeCity]					as [ActiveHostCityId]  --TODO
	,a.[activePlace]				as [AcitveHostAddress] 
	,a.[activeDate]					as [AcitveDate] 
	,ee.spk_code					as [ActiveType]   -- TODO  随便找了一个没有匹配上
	,j.ProjectType					as ProjectType	   
	,a.DoctorNum					as DoctorsNum
	,rr.spk_code					as MeetingType     -- 会议类型
	,yy.spk_code                    as SerialMeetingType                  -- TODO
	,a.MainPRNo                                       -- TODO  serialNumber  基于单号匹配至本表的ApplicationCode以找回对应的主会场采购申请ID
	,j.OrganizerNature
	,j.SponsorshipType
	,j.ActiveLeader
	,j.MeetingName
	,j.OrganizerName
	,j.MeetingDate
	,j.NumberOfProfessionals
	,j.ChoiceReason
	,j.SupportReason
	,j.AttendeeExpertise
	,a.applicantEmpId
	,dd.SupportFiles
--	,a.applicationDate 				as CreationTime  -- 验证  即可 ApplyTime
--	,a.applicantEmpId				as CreatorId     -- 验证  即可 ApplyUserId
	,ss.AdditionalFiles
	,ff.approvalTime3				as ApprovedDate
	,a.CoverDepartment				as HospitalDepartments						 
	,a.CoverHospital				as Hospitals				 
	,k.res_name 					as ApplyUserBuName
	,a.MeetingControl				as ActiveNo
	,UPPER(l.spk_NexBPMCode) 		as ApplyUserDept
	,a.applicantDept_Text			as ApplyUserDeptName
	,z.Emp_Name						as AgentIdName
	,a.applicantEmpName				as ApplyUserIdName
	,e.ParentNumber					as BudgetCode
	,a.regional_Text				as BudgetRegionName
	,COALESCE(x.date,v.FinishDate)  as ClosedDate 
	,a.f_code						as CompanyCode
	,a.company_Text					as CompanyIdName
	,n.spk_Name 			     	as CompanyShortName
	,m.Res_Data1					as CostCenterCode
	,c.costCenter					as CostCenterName
	,u.spk_NexBPMCode				as ExpenseTypeCode  -- TODO  以该值匹配消费大类后，填入对应的"消费大类编号"  逻辑可能有问题,
	,a.expenseCategory_Text 		as ExpenseTypeName  -- TODO  以该值匹配消费大类后，填入对应的"消费大类编号"  逻辑可能有问题,
	,a.MeetingSelectPersonName		as HostVendorIdName
	,a.productCbo_Text				as ProductIdsName
	,a.budgetNumber					as SubBudgetCode
	,i.id							as SubBudgetId
	,z.Emp_AD_Mail_Address			as AgentEmail
	,a.MeetingSelectPersonId		as HostVendorEmail
	,case when Pilot='true' and ff.approvalTime3 is not null then 
		case 
			when gg.meet_code is not null then gg.meet_code
			when hh.meet_code is not null then hh.meet_code
			when b.processStatus in (N'供应商确认',N'审批完毕，等待关闭',N'等待关闭',N'完成')  then 1002 
			when b.processStatus =N'发起人终止'   then 1003 
			else 1000
		end
	end MeetingStatus
	,a.PRTotalAmount 				as TotalAmountRMB
	,case when j.IsSpecialOperate =1 then 0 else 1 end as IsShowExpenseStep
	,j.Other as 				    OecExceptionNumber
into #purprapplications
from   ODS_AUTO_BIZ_T_ProcurementApplication_Info  a -- 561664   555228
left join spk_dictionary ee
	on a.activetype_Value =ee.spk_BPMCode and  ee.spk_BPMCode<>'' and ee.spk_type=N'活动类型'
left join  spk_dictionary rr
	on a.ConferenceTypeCode = rr.spk_BPMCode and rr.spk_type=N'会议类型' 
left join  spk_dictionary yy
	on a.IsSeriesMeeting = yy.spk_BPMCode and yy.spk_type=N'系列会类型' 
left join spk_productmasterdata ww
	on a.productCbo_Value =ww.spk_BPMCode
left join spk_districtmasterdata qq
	on a.regional_Value = qq.spk_BPMCode
left join 
	spk_staffmasterdata o 
	on a.applicantEmpId=o.bpm_id
left join 
	spk_staffmasterdata oo
	on a.SelectPersonId = oo.bpm_id
left join spk_organizationalmasterdata p
	on a.BUId=p.spk_BPMCode
left join spk_consume u 
	on a.expenseCategory_Value=u.spk_BPMCode
left join BdSubBudgets i
	on i.code =a.budgetNumber
left join spk_companymasterdata n
	on trim(a.f_code) =n.spk_CompanyCode
left join (
	SELECT 
		PRFormCode,
		max([Date]) as [Date] 
	from 
		ods_T_ClosedPRRecord
	group by 
		PRFormCode 
) x
	on a.SerialNumber=x.PRFormCode
left join 
  (
  	SELECT  
		ProcInstID ,max(FinishDate) as FinishDate  
	from ods_T_PROCESS_Historys 
	where TRIM(ActName)=N'财务关闭' 
	group by ProcInstID
  
  ) v
    on a.ProcInstID=v.ProcInstID and x.PRFormCode is null 
 left join 
 (
	SELECT  Emp_Id ,Emp_Name ,Emp_Code,Emp_AD_Mail_Address  from ODS_T_EMPLOYEE  
 ) z 
   on a.SelectPersonId= z.Emp_Id
  left join 
 (
	select spk_BPMCode,spk_NexBPMCode from spk_organizationalmasterdata
 ) l
on a.applicantDept_Value = l.spk_BPMCode
left join 
(
	SELECT  Res_Code ,Res_Name  from ODS_T_RESOURCE 
) k
	on a.BUId=k.Res_Code
left join
	ODS_Form_6e32f7dd7c4e49aea79949009c3bf7ae b  -- 561664
	on a.ProcInstId =b.ProcInstId 
LEFT  join 
 	#ProcurementCostCenter c  -- 561666  -- ODS_AUTO_BIZ_T_ProcurementApplication_Info_PR
	on b.ProcInstId =c.ProcInstId 
left join spk_costcentermasterdata q
	on c.costCenterCode = q.spk_BPMCode 
 left join  
 (
	 SELECT   
		Res_Data1,Res_Code
	from ods_T_RESOURCE
 ) m
 on c.costCenterCode = m.Res_Code
left join 
	ODS_T_Pur_ExpenseBudget_SubInfo e  -- 561666
	on a.budgetNumber =e.Number 
left join BdSubBudgets kk
	on e.ParentNumber =kk.Code
left join 
	(
		select 
			*,ROW_NUMBER () over(partition by serialNumber order by OperateTime desc ) f_rn 
		from ods_T_Return_Reject_Info
	) f  -- 561666
	on a.SerialNumber =f.SerialNumber and f.f_rn=1
LEFT JOIN 
	(
		SELECT 
			 ProcInstId,
		--	 count(ProcInstId) ProcInstId_count,
		--	 count(PurchaseEmpId) PurchaseEmpId_count,
			 case when count(ProcInstId) = count(PurchaseEmpId) then 9 else 3 end is_sent_status
		FROM ods_T_Pur_PRItems_Info
		where  paymentType='AP'
			--ProcInstId =1104034
		group by ProcInstId
	) g 
	on a.ProcInstId =g.ProcInstId
left JOIN 
	 #vendorID h
	on trim(a.MeetingSelectPersonId) = trim(h.VEMLAD)
left JOIN 
	#GLOBALXmlContent_Value j
	on a.ProcInstId = j.ProcInstId
left join 
	#AdditionalFiles ss
	on a.ProcInstId = ss.ProcInstId
left join 
	#SupportFiles dd
	on a.ProcInstId = dd.ProcInstId
left join 
	#ApprovedDate ff
	on a.ProcInstId = ff.ProcInstId
left join 
(
	SELECT * from (
	 	SELECT 
	 		*,
	 		row_number() over(partition by Scenes order by meet_code ) as rn 
	 	from (
			SELECT 
				Scenes ,[Method],
				case when [Method] ='UpdateCampaignInActive' then 1003 else 1004 end meet_code  
			from ods_T_TPM_InterfaceLog 
			where  [Method] ='UpdateCampaignInActive' or [Method] ='MeetingCancelVerification' 
	 	) t1
	) t2 where t2.rn =1
) gg
	on a.SerialNumber = gg.Scenes
 left join 
 (
 	 SELECT 
 		* 
	 from (
		 SELECT 
		 	* ,
		 	row_number() over(partition by serialNumber order by meet_code desc) as rn
		 from (
			SELECT    
				serialNumber 
				,status
				,CASE WHEN status=N'已结算' THEN 1002 ELSE 1001 end meet_code
			from ods_T_ERS_MeetingStatusInfo 
			where status  in (N'已结算' ,N'已开始',N'已结束' ) 
		 )  t1
	 ) t2 where rn =1	
 
 ) hh
   on a.SerialNumber = hh.serialNumber
 ;
  CREATE  index index_ApplicationCode on #purprapplications(ApplicationCode);

 ----------------------------
 
   
   ---- 验证数据开始 #purprapplications   总数
   
   
   SELECT count(*) from   #purprapplications ; -- 555230
   
   SELECT  count(*) from   purprapplications  ;  -- 555197
   
   ------  CreationTime   CreatorId 验证
      SELECT  ApplyTime ,ApplyUserId,CreationTime, CreatorId  from   purprapplications 
      where ApplyTime<> CreationTime  and  ApplyUserId<> CreatorId
      
      
      ------------
      
SELECT  
	TOP 100
	t1.ApplicationCode,t1.ProcInstId,t2.id,
	t1.[ClosedDate],t2.[ClosedDate]
from
  	(select  ApplicationCode, ProcInstId,FORMAT(ClosedDate, 'yyyy-MM-dd HH:mm:00')   ClosedDate 
  	from #purprapplications where  [ClosedDate] is not null ) t1
full join
(
	select  a.id,a.ApplicationCode,ppt.ProcInstId,
		FORMAT(cast(a.[ClosedDate] as datetime ), 'yyyy-MM-dd HH:mm:ss') ClosedDate
	from
		purprapplications a
	join 
		PurPRApplications_tmp ppt 
		on a.id =ppt.id
    where a.[ClosedDate] is not null
) t2
     on t1.ApplicationCode = t2.ApplicationCode and t1.ProcInstId= t2.ProcInstId
WHERE  

  t1.[ClosedDate]<> t2.[ClosedDate]
  OR (t1.[ClosedDate] IS NULL AND t2.[ClosedDate] IS NOT NULL)
  OR (t1.[ClosedDate] IS NOT NULL AND t2.[ClosedDate] IS  NULL)
 
      
      ;  -- 555197
------------ 字段验证
   CREATE  index index_id on purprapplications(id)
   CREATE  index index_ApplicationCode on purprapplications(ApplicationCode)
   CREATE  index index_id on purprapplications_tmp(id)

    
  -------------- 
 
   -- rejected columns : status CostCenter HostVendorId TotalAmount ActiveLaunchCityId ActiveHostCityId ActiveLeader MeetingName MeetingDate
--    					 AdditionalFiles  ApprovedDate  ClosedDate CompanyShortName CostCenterCode CostCenterName ExpenseTypeName MeetingStatus IsShowExpenseStep
    
   -- pass columns     : ApplyUserId ApplyTime ApplyUserBu MeetingTitle IsEsignUsed PushSystem AgentId ExpenseType  BudgetRegion   SupportFiles
--                       ProductIds CompanyId  Remark PrLateDescription AcitveHostAddress ProjectType  DoctorsNum MeetingType OrganizerNature
--   					 OrganizerName  NumberOfProfessionals ChoiceReason SupportReason AttendeeExpertise HospitalDepartments Hospitals ApplyUserDept
--   					 ApplyUserDeptName AgentIdName BudgetCode BudgetRegionName CompanyCode CompanyIdName ExpenseTypeCode HostVendorIdName ProductIdsName
--   					 SubBudgetCode SubBudgetId AgentEmail HostVendorEmail OecExceptionNumber
   
   -- 逻辑不清楚          : BudgetId  ActiveType  SerialMeetingType MainMeetingPR
  
 
declare @columnName NVARCHAR(128)  ='OecExceptionNumber';
declare @SQL NVARCHAR(MAX);
SET @SQL='
SELECT  
	TOP 100
	t1.ApplicationCode,t1.ProcInstId,t2.id,
	t1.'+quotename(@columnName)+',t2.'+quotename(@columnName)+'
from
  	(select * from #purprapplications where  '+quotename(@columnName)+' is not null ) t1
full join
(
	select  a.id,a.ApplicationCode,ppt.ProcInstId,
		a.'+quotename(@columnName)+'
	from
		purprapplications a
	join 
		PurPRApplications_tmp ppt 
		on a.id =ppt.id
    where a.'+quotename(@columnName)+' is not null
) t2
     on t1.ApplicationCode = t2.ApplicationCode and t1.ProcInstId= t2.ProcInstId
WHERE  

  t1.'+quotename(@columnName)+'<> t2.'+quotename(@columnName)+'
  OR (t1.'+quotename(@columnName)+' IS NULL AND t2.'+quotename(@columnName)+' IS NOT NULL)
  OR (t1.'+quotename(@columnName)+' IS NOT NULL AND t2.'+quotename(@columnName)+' IS  NULL)
 
';
EXEC sp_executesql @SQL;


SELECT  @SQL;
     
     
     
----------------------------
 
----------------------------
     
SELECT  IsShowExpenseStep,OecExceptionNumber from purprapplications
 


 
