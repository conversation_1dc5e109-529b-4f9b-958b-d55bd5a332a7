CREATE PROCEDURE dbo.sp_PurPAApplicationDetails
AS 
BEGIN
	--初始化xml
--IF OBJECT_ID(N'PLATFORM_ABBOTT_Stg.dbo.XML_9', N'U') IS NOT NULL
--BEGIN
--	PRINT(N'已经初始化过'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
--END
--ELSE
--BEGIN
--	select ProcInstId,
--	 RowData.value('(goodsName/text())[1]', 'nvarchar(50)') AS goodsName,
--	 RowData.value('(orderCount/text())[1]', 'nvarchar(50)') AS orderCount,
--	 RowData.value('(receivingWay/text())[1]', 'nvarchar(50)') AS receivingWay,
--	 RowData.value('(price/text())[1]', 'nvarchar(50)') AS price,
--	 RowData.value('(receivedNum_Per/text())[1]', 'nvarchar(50)') AS receivedNum_Per,
--	 RowData.value('(signNumPer/text())[1]', 'nvarchar(50)') AS signNumPer,
--	 RowData.value('(signDate/text())[1]', 'nvarchar(50)') AS signDate,
--	 RowData.value('(isOnTime/text())[1]', 'nvarchar(50)') AS isOnTime
--into PLATFORM_ABBOTT_Stg.dbo.XML_9
--FROM 
--(select a.ProcInstId,XmlContent from PLATFORM_ABBOTT_Stg.dbo.ods_AUTO_BIZ_T_PaymentApplication_Info a 
--join PLATFORM_ABBOTT_Stg.dbo.ODS_T_FORMINSTANCE_GLOBAL b
--on a.ProcInstId=b.ProcInstId) A
--CROSS APPLY XmlContent.nodes('/root/ReveiceGoodsGridPanel/row') AS XMLTable(RowData)
--) fg 
--PRINT(N'初始化完成'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
--END;



with processStatus_info as (
select c.ProcInstId,processStatus,a.serialNumber from (
select DISTINCT  serialNumber
from PLATFORM_ABBOTT_STG.dbo.ods_AUTO_BIZ_T_PaymentApplication_Info a
left join PLATFORM_ABBOTT_STG.dbo.ods_T_Pur_ProcessForm_GR_GRDetail_Info b
on a.serialNumber=b.PASN
where PASN is null or PASN = ''
)A 
join PLATFORM_ABBOTT_STG.dbo.ods_Form_e37632eb82f04fbda355cffdac744166 c
on a.serialNumber=c.serialNumber
)
,processStatus_info1 as (
select c.ProcInstId,processStatus,a.serialNumber from (
select  serialNumber
from PLATFORM_ABBOTT_STG.dbo.ods_AUTO_BIZ_T_PaymentApplication_Info a
left join PLATFORM_ABBOTT_STG.dbo.ods_T_Pur_ProcessForm_GR_GRDetail_Info b
on a.serialNumber=b.PASN
where PASN is not null or PASN <> ''
)A 
left join PLATFORM_ABBOTT_STG.dbo.ods_Form_e37632eb82f04fbda355cffdac744166 c
on a.serialNumber=c.serialNumber
)
,GR_info as (
select 
	a.ProcInstId ,
	a.serialNumber  COLLATE SQL_Latin1_General_CP1_CI_AS as PASN,
	cast('' as nvarchar(100)) as PRFormCode,
	XmlContent.value('(/root/PaymentApplication_hiddenBlock_MainStore/POSerialNumber)[1]', 'nvarchar(50)') as POSerialNumber,
	PO_Item_No as GR_Item_No,
	cast('' as nvarchar(100)) as PR_Item_No,
	processStatus COLLATE SQL_Latin1_General_CP1_CI_AS as processStatus,
	goodsName COLLATE SQL_Latin1_General_CP1_CI_AS as goodsName,
	orderCount COLLATE SQL_Latin1_General_CP1_CI_AS as orderCount,
	receivingWay COLLATE SQL_Latin1_General_CP1_CI_AS as receivingWay,
	x.price COLLATE SQL_Latin1_General_CP1_CI_AS as price,
	receivedNum_Per COLLATE SQL_Latin1_General_CP1_CI_AS as ReceiverNum_Per,
	signNumPer COLLATE SQL_Latin1_General_CP1_CI_AS  as SignNum_Per,
	signDate COLLATE SQL_Latin1_General_CP1_CI_AS as  signDate,
	isOnTime COLLATE SQL_Latin1_General_CP1_CI_AS   as isOnTime,
	1 rn
--	'1' as flag
from processStatus_info a 
join (select * from (select *,ROW_NUMBER () over(PARTITION by  ProcInstId,price,goodsName,orderCount  order by signDate desc) rn from  PLATFORM_ABBOTT_STG.dbo.XML_9) a where rn=1) x
on a.ProcInstId =x.ProcInstId
join PLATFORM_ABBOTT_STG.dbo.ODS_T_FORMINSTANCE_GLOBAL b
on a.ProcInstId =b.ProcInstId
left join PLATFORM_ABBOTT_STG.dbo.ods_AUTO_BIZ_PurchaseOrderApplication_Info_PO g
on x.goodsName  COLLATE SQL_Latin1_General_CP1_CI_AS =g.content and x.orderCount  =g.num and x.price  =g.price
where 
processStatus not in (N'终止',N'发起人终止') 
and  XmlContent.value('(/root/PaymentApplication_hiddenBlock_MainStore/AdvancePayment)[1]', 'nvarchar(50)')='true'
union
select 
	a.ProcInstId ,
	a.serialNumber  COLLATE SQL_Latin1_General_CP1_CI_AS as PASN,
	cast('' as nvarchar(100)) as PRFormCode,
	XmlContent.value('(/root/PaymentApplication_hiddenBlock_MainStore/POSerialNumber)[1]', 'nvarchar(50)') as POSerialNumber,
	PO_Item_No as GR_Item_No,
	cast('' as nvarchar(100)) as PR_Item_No,
	processStatus COLLATE SQL_Latin1_General_CP1_CI_AS as processStatus,
	goodsName COLLATE SQL_Latin1_General_CP1_CI_AS as goodsName,
	orderCount COLLATE SQL_Latin1_General_CP1_CI_AS as orderCount,
	receivingWay COLLATE SQL_Latin1_General_CP1_CI_AS as receivingWay,
	x.price COLLATE SQL_Latin1_General_CP1_CI_AS as price,
	receivedNum_Per COLLATE SQL_Latin1_General_CP1_CI_AS as ReceiverNum_Per,
	signNumPer COLLATE SQL_Latin1_General_CP1_CI_AS  as SignNum_Per,
	signDate COLLATE SQL_Latin1_General_CP1_CI_AS as  signDate,
	isOnTime COLLATE SQL_Latin1_General_CP1_CI_AS   as isOnTime,
	1 rn
--	'2' as flag
from processStatus_info a 
join (select * from (select *,ROW_NUMBER () over(PARTITION by   ProcInstId,price,goodsName,orderCount   order by signDate desc) rn from  PLATFORM_ABBOTT_STG.dbo.XML_9) a where rn=1) x
on a.ProcInstId =x.ProcInstId
join PLATFORM_ABBOTT_STG.dbo.ODS_T_FORMINSTANCE_GLOBAL b
on a.ProcInstId =b.ProcInstId
left join PLATFORM_ABBOTT_STG.dbo.ods_AUTO_BIZ_PurchaseOrderApplication_Info_PO g
on x.goodsName  COLLATE SQL_Latin1_General_CP1_CI_AS =g.content and x.orderCount  =g.num and x.price  =g.price 
where 
processStatus in (N'终止',N'发起人终止') 
or  XmlContent.value('(/root/PaymentApplication_hiddenBlock_MainStore/AdvancePayment)[1]', 'nvarchar(50)')='true'
union
select 
a.ProcInstId,
a.PASN COLLATE SQL_Latin1_General_CP1_CI_AS as PASN,
a.PRFormCode COLLATE SQL_Latin1_General_CP1_CI_AS as PRFormCode,
a.POSerialNumber COLLATE SQL_Latin1_General_CP1_CI_AS as POSerialNumber,
a.GR_Item_No,
a.PR_Item_No,
processStatus COLLATE SQL_Latin1_General_CP1_CI_AS as processStatus,
goodsName COLLATE SQL_Latin1_General_CP1_CI_AS as  goodsName,
orderCount   as orderCount,
receivingWay COLLATE SQL_Latin1_General_CP1_CI_AS as  receivingWay,
price  as price,
ReceiverNum_Per COLLATE SQL_Latin1_General_CP1_CI_AS as ReceiverNum_Per,
SignNum_Per COLLATE SQL_Latin1_General_CP1_CI_AS as SignNum_Per,
signDate  as signDate,
isOnTime COLLATE SQL_Latin1_General_CP1_CI_AS as isOnTime,
ROW_NUMBER() over(PARTITION by a.ProcInstId,PASN,goodsName,orderCount,price order by processStatus desc) rn
--'3' as flag
from PLATFORM_ABBOTT_STG.dbo.ods_T_Pur_ProcessForm_GR_GRDetail_Info a
left join PLATFORM_ABBOTT_STG.dbo.ods_Form_e37632eb82f04fbda355cffdac744166 c
on a.PASN=c.serialNumber
)
select * 
into #GR_info 
from GR_info
where rn=1 

 
--drop table #GR_info

select  newid() AS Id,* 
into #PurPAApplicationDetails_tmp
from (
select 
DISTINCT--
case when PASN COLLATE SQL_Latin1_General_CP1_CI_AS <>null or PASN COLLATE SQL_Latin1_General_CP1_CI_AS <>'' 
then PASN COLLATE SQL_Latin1_General_CP1_CI_AS else a.SerialNumber  
end AS PurPAApplicationId,--基于07-1迁移的申请单主信息，以该单号定位对应的PurPAApplications.ID
--在T_Pur_ProcessForm_GR_GRDetail_Info中查询到的行数即为PurPAApplicationDetails内该PA单对应的行数"
--SerialNumber AS ,--(预付款/部分终止或异常的单据)
--若PA在T_Pur_ProcessForm_GR_GRDetail_Info没有查询到记录，此时需要基于单据xml找回当时的收货记录，且预付款逻辑需要单独记录；此时对应的行数需要基于xml内的数量/PO行数决定"
case when b.PRFormCode COLLATE SQL_Latin1_General_CP1_CI_AS <>null or b.PRFormCode COLLATE SQL_Latin1_General_CP1_CI_AS <>'' 
then b.PRFormCode COLLATE SQL_Latin1_General_CP1_CI_AS else  c.PR_No end  AS PRId,--基于03-1迁移的申请单主信息，以该单号定位对应的PurPRApplications.ID
case when POSerialNumber COLLATE SQL_Latin1_General_CP1_CI_AS <> null or POSerialNumber COLLATE SQL_Latin1_General_CP1_CI_AS <> '' 
	then 
		case when SUBSTRING(POSerialNumber COLLATE SQL_Latin1_General_CP1_CI_AS,1,1)='P' 
		then '' else POSerialNumber COLLATE SQL_Latin1_General_CP1_CI_AS end 
	else case when SUBSTRING(POorArNumber,1,1)='P' then '' else POorArNumber end 
end 
AS POId,--基于05-1迁移的申请单主信息，以该单号定位对应的PurPRApplications.ID
--(若查询到的单号以""P""开头，则此处填为空)"
--GR_ProcInstId ,--(预付款/部分终止或异常的单据)
--基于该信息查询AUTO_BIZ_T_GoodsReceiveApplication_Info.ProcInstID，根据得到的POorArNumber查询05-1迁移的申请单主信息，以该单号定位对应的PurPOApplications.ID(若查询到的单号以""P""开头，则此处填为空)"
concat(c.PR_No,PR_Item_No) AS PRDetailId,--对于AR付款方式的PA，或付款方式为AP且PO单号对应AUTO_BIZ_PurchaseOrderApplication_Info.ProcurementDetail标记为"与PR行一一对应"的PA，此处的每一行即作为PurPAApplicationDetails内该PA单对应的行数
--'' AS ,--对于付款方式为AP且PO单号对应AUTO_BIZ_PurchaseOrderApplication_Info.ProcurementDetail标记为"采购自定义"的PA，会出现多行对应同一个PRDetail的情况，此时先找回PODetailId后再找回对应的PRDetailID
case when (POSerialNumber COLLATE SQL_Latin1_General_CP1_CI_AS <> null or POSerialNumber COLLATE SQL_Latin1_General_CP1_CI_AS <> '' ) 
	then 
		case when SUBSTRING(POSerialNumber COLLATE SQL_Latin1_General_CP1_CI_AS,1,1)='P' 
		then '' else concat(POSerialNumber COLLATE SQL_Latin1_General_CP1_CI_AS,'_',GR_Item_No) end 
	else 
		case when SUBSTRING(POorArNumber,1,1)='P' 
		then '' else concat(POSerialNumber COLLATE SQL_Latin1_General_CP1_CI_AS,'_',GR_Item_No) end 
	end  
AS PODetailId,--(若POId为空则留空),--基于05-1及05-2迁移的PO申请单号+明细行号，定位至对应的PurPOApplicationDetails.ID
--ReveiceGoodsGridPanel AS ,--非预付款：基于ProcInstId查询T_FORMINSTANCE_GLOBAL内xml中ReveiceGoodsGridPanel板块的内容，基于其中填写的goodsName+orderCount+price查询AUTO_BIZ_PurchaseOrderApplication_Info_PO中的content+num+price后匹配出对应的PO_Item_No，再基于POSerialNumber+GR_Item_No匹配回对应的PurPOApplicationDetails.ID
--GR_ProcInstId AS ,--预付款：基于该值找到收货单号及PO单号，PO的所有明细行都将生成对应的一行付款明细，也即将PurPOApplicationDetails的所有ID都带回，每个ID填入一行即可
concat(a.ProcInstId,b.GR_Item_No) AS GRHistoryId,--对于非预付款，该组合会定位至PurGRApplicationDetailHistorys的唯一一行(如果有多次收货的情况，可以通过SignDate来识别，或者可以考虑迁移PurGRApplicationDetailHistorys时保存该表内的ID以作为此处关联用)
--ReveiceGoodsGridPanel AS ,--非预付款：基于ProcInstId查询T_FORMINSTANCE_GLOBAL内xml中ReveiceGoodsGridPanel板块的内容后，需要补回对应的PurGRApplicationDetailHistorys，将补回后的ID填回即可
--GR_ProcInstId AS ,--预付款：对应收货History处单独加入的预付款记录，将补回后的ID填回即可
GR_ProcInstId AS ProductId,--基于该信息查询AUTO_BIZ_T_GoodsReceiveApplication_Info.ProcInstId，若查询出的PRItemType="AR"，则取对应的唯一一行AUTO_BIZ_T_GoodsReceiveApplication_Info_PR.ProductId匹配至对应的产品主数据后得到ID；如果为AP则留空(AP的产品来自于PO内人工录入的品名)
case when PRItemType='AR' then Product end  AS ProductName,--基于该信息查询AUTO_BIZ_T_GoodsReceiveApplication_Info.ProcInstId，若查询出的PRItemType="AR"，则取对应的唯一一行AUTO_BIZ_T_GoodsReceiveApplication_Info_PR.Product
--'' AS ,--对于AP付款方式，基于上方已迁移的PODetailId找回PurPOApplicationDetails表中对应的Content
h.invoiceType AS InvoiceType,--(同一张PA在BPM内为相同发票类型)基于发票类型名称找回对应的字典Code
case when a.TaxRate = 'NULL' or a.TaxRate ='' then '0'
else CAST(REPLACE(a.TaxRate, '%', '') AS INT) / 100.0  end AS TaxRate,
ProcurementDetail,--(同一张PA在BPM内为相同税率)移除百分号后除以100(例如1%转换为0.01)
case when PRItemType='AR' or (PRItemType='AP' and ProcurementDetail=N'与PR行一一对应')
then  
 Payment_ApportionAmount_RMB / e.ExchangeRate * 
    (1 + CAST(
            REPLACE(
                CASE 
                    WHEN a.TaxRate IS NULL OR a.TaxRate = '' OR a.TaxRate = 'NULL' THEN '0'
                    ELSE a.TaxRate 
                END, 
                '%', 
                ''
            ) AS FLOAT
         ) / 100.0)
when PRItemType='PA' or (PRItemType='AP' and ProcurementDetail=N'采购自定义') 
then  
 Payment_ApportionAmount_RMB / e.ExchangeRate * 
    (1 + CAST(
            REPLACE(
                CASE 
                    WHEN a.TaxRate IS NULL OR a.TaxRate = '' OR a.TaxRate = 'NULL' THEN '0'
                    ELSE a.TaxRate 
                END, 
                '%', 
                ''
            ) AS FLOAT
         ) / 100.0)
else 0 end 
AS PaymentAmount,
'{}' AS ExtraProperties,--默认填写为"{}"
'' AS ConcurrencyStamp,--?
a.applicationDate AS CreationTime,--与对应的PurPAApplications记录保持一致即可
a.applicantEmpId AS CreatorId,--与对应的PurPAApplications记录保持一致即可
'' AS LastModificationTime,--默认为空
'' AS LastModifierId,--默认为空
'0' AS IsDeleted,--默认为0
'' AS DeleterId,--默认为空
'' AS DeletionTime,--默认为空
'' AS GRApplicationDetailId--基于GRHistoryId查询 PurGRApplicationDetailHistorys.GRApplicationDetailId找回对应记录
from PLATFORM_ABBOTT_Stg.dbo.ods_AUTO_BIZ_T_PaymentApplication_Info a--618626
left join #GR_info b
on a.serialNumber COLLATE SQL_Latin1_General_CP1_CI_AS =b.PASN COLLATE SQL_Latin1_General_CP1_CI_AS  and a.GR_ProcInstId=b.ProcInstId--637508
left join PLATFORM_ABBOTT_Stg.dbo.ODS_AUTO_BIZ_T_PaymentApplication_Info_PR c
on a.ProcInstId =c.ProcInstId and b.PR_item_no=c.[row] and b.PRFormCode COLLATE SQL_Latin1_General_CP1_CI_AS=c.PR_No--and b.goodsName COLLATE SQL_Latin1_General_CP1_CI_AS =c.product_Content and b.PRFormCode=c.PR_No--637515
left join PLATFORM_ABBOTT_Stg.dbo.ODS_T_FORMINSTANCE_GLOBAL d 
on a.ProcInstId =d.ProcInstId 
left join PLATFORM_ABBOTT_Stg.dbo.ODS_AUTO_BIZ_T_GoodsReceiveApplication_Info e
on a.GR_ProcInstId=e.ProcInstId 
left join PLATFORM_ABBOTT_Stg.dbo.ods_Form_e37632eb82f04fbda355cffdac744166 h
on a.serialNumber=h.serialNumber
left join PLATFORM_ABBOTT_Stg.dbo.ods_AUTO_BIZ_PurchaseOrderApplication_Info k
on b.POSerialNumber COLLATE SQL_Latin1_General_CP1_CI_AS =k.serialNumber
)A
ALTER TABLE PLATFORM_ABBOTT_Stg.dbo.PurPAApplicationDetails_tmp ALTER COLUMN PRDetailId nvarchar(62) COLLATE Chinese_PRC_CI_AS NULL;
ALTER TABLE PLATFORM_ABBOTT_Stg.dbo.PurPAApplicationDetails_tmp ALTER COLUMN PODetailId nvarchar(62) COLLATE SQL_Latin1_General_CP1_CI_AS NULL;
ALTER TABLE PLATFORM_ABBOTT_Stg.dbo.PurPAApplicationDetails_tmp ALTER COLUMN GRHistoryId varchar(24) COLLATE Chinese_PRC_CI_AS NULL;
ALTER TABLE PLATFORM_ABBOTT_Stg.dbo.PurPAApplicationDetails_tmp ALTER COLUMN ExtraProperties varchar(2) COLLATE Chinese_PRC_CI_AS NULL;
ALTER TABLE PLATFORM_ABBOTT_Stg.dbo.PurPAApplicationDetails_tmp ALTER COLUMN ConcurrencyStamp varchar(1) COLLATE Chinese_PRC_CI_AS NULL;
ALTER TABLE PLATFORM_ABBOTT_Stg.dbo.PurPAApplicationDetails_tmp ALTER COLUMN LastModificationTime varchar(1) COLLATE Chinese_PRC_CI_AS NULL;
ALTER TABLE PLATFORM_ABBOTT_Stg.dbo.PurPAApplicationDetails_tmp ALTER COLUMN LastModifierId varchar(1) COLLATE Chinese_PRC_CI_AS NULL;
ALTER TABLE PLATFORM_ABBOTT_Stg.dbo.PurPAApplicationDetails_tmp ALTER COLUMN IsDeleted varchar(1) COLLATE Chinese_PRC_CI_AS NULL;
ALTER TABLE PLATFORM_ABBOTT_Stg.dbo.PurPAApplicationDetails_tmp ALTER COLUMN DeleterId varchar(1) COLLATE Chinese_PRC_CI_AS NULL;
ALTER TABLE PLATFORM_ABBOTT_Stg.dbo.PurPAApplicationDetails_tmp ALTER COLUMN DeletionTime varchar(1) COLLATE Chinese_PRC_CI_AS NULL;
ALTER TABLE PLATFORM_ABBOTT_Stg.dbo.PurPAApplicationDetails_tmp ALTER COLUMN GRApplicationDetailId varchar(1) COLLATE Chinese_PRC_CI_AS NULL;

IF OBJECT_ID(N'PLATFORM_ABBOTT_Stg.dbo.PurPAApplicationDetails_tmp', N'U') IS NOT NULL
BEGIN
	update A
	set    a.PurPAApplicationId    = b.PurPAApplicationId   
           ,a.PRId                  = b.PRId                 
           ,a.POId                  = b.POId                 
           ,a.PRDetailId            = b.PRDetailId           
           ,a.PODetailId            = b.PODetailId           
           ,a.GRHistoryId           = b.GRHistoryId          
           ,a.ProductId             = b.ProductId            
           ,a.ProductName           = b.ProductName          
           ,a.InvoiceType           = b.InvoiceType          
           ,a.TaxRate               = b.TaxRate              
           ,a.ProcurementDetail     = b.ProcurementDetail    
           ,a.PaymentAmount         = b.PaymentAmount        
           ,a.ExtraProperties       = b.ExtraProperties      
           ,a.ConcurrencyStamp      = b.ConcurrencyStamp     
           ,a.CreationTime          = b.CreationTime         
           ,a.CreatorId             = b.CreatorId            
           ,a.LastModificationTime  = b.LastModificationTime 
           ,a.LastModifierId        = b.LastModifierId       
           ,a.IsDeleted             = b.IsDeleted            
           ,a.DeleterId             = b.DeleterId            
           ,a.DeletionTime          = b.DeletionTime         
           ,a.GRApplicationDetailId = b.GRApplicationDetailId
from PLATFORM_ABBOTT_Stg.dbo.PurPAApplicationDetails_tmp a
left join #PurPAApplicationDetails_tmp b on a.PurPAApplicationId = b.PurPAApplicationId 
and a.GRHistoryId = b.GRHistoryId and a.InvoiceType = b.InvoiceType and isnull(a.PaymentAmount,0) = isnull(b.PaymentAmount,0)

insert into PLATFORM_ABBOTT_Stg.dbo.PurPAApplicationDetails_tmp 
select      a.Id
           ,a.PurPAApplicationId
           ,a.PRId
           ,a.POId
           ,a.PRDetailId
           ,a.PODetailId
           ,a.GRHistoryId
           ,a.ProductId
           ,a.ProductName
           ,a.InvoiceType
           ,a.TaxRate
           ,a.ProcurementDetail
           ,a.PaymentAmount
           ,a.ExtraProperties
           ,a.ConcurrencyStamp
           ,a.CreationTime
           ,a.CreatorId
           ,a.LastModificationTime
           ,a.LastModifierId
           ,a.IsDeleted
           ,a.DeleterId
           ,a.DeletionTime
           ,a.GRApplicationDetailId
from #PurPAApplicationDetails_tmp a 
where not exists (select * from PLATFORM_ABBOTT_Stg.dbo.PurPAApplicationDetails_tmp where a.PurPAApplicationId = PurPAApplicationId 
and a.GRHistoryId = GRHistoryId and a.InvoiceType = InvoiceType and isnull(a.PaymentAmount,0) = isnull(PaymentAmount,0))
	PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into PLATFORM_ABBOTT_Stg.dbo.PurPAApplicationDetails_tmp from #PurPAApplicationDetails_tmp
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END 
END;

