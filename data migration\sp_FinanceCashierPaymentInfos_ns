CREATE PROCEDURE dbo.sp_FinanceCashierPaymentInfos_ns
AS 
BEGIN
	
select 
a.[Id]
,c.<PERSON><PERSON>orm<PERSON><PERSON> as [PAApplicationCode]
,pb.ApplyUserId as [ApplyUserId]                                        
,pb.ApplyUserName as [ApplyUserName]                                    
,ss.spk_staffemail as [ApplyUserEmail]                                     
,pb.ApplyTime as [ApplyTime]                                            
,pb.CompanyId as [CompanyId]                                            
,pb.CompanyName as [CompanyName]                                        
,pb.ApplyUserBu as [ApplyUserBu]                                        
,pb.ApplyUserBuName as [ApplyUserBuName]                    
,pp.CostCenterName  as [CostCenterName]                      
,a.[VendorName]
,a.[BankName]
,a.[BankCardNo]
,a.[RemoteOrCity]
,a.[PaymentAmount]
,a.[RefNo]
,a.[MPDate]
,a.[MPStatus]
,a.[PaymentDate]
,a.[RetureDate]
,a.[Remark]
,a.[ExtraProperties]
,a.[ConcurrencyStamp]
,a.[CreationTime]
,a.[CreatorId]
,a.[LastModificationTime]
,a.[LastModifierId]
,a.[IsDeleted]
,a.[DeleterId]
,a.[DeletionTime] 
into #FinanceCashierPaymentInfos
from PLATFORM_ABBOTT_Dev.dbo.FinanceCashierPaymentInfos_tmp a--567710
left join  PLATFORM_ABBOTT_Dev.dbo.ODS_T_AP_REF c  
on a.PAApplicationCode=c.RefNo 
left join (select *,ROW_NUMBER() over(PARTITION by ApplicationCode order by  ApplicationCode) rn from  PurPAApplications)  pb
on c.PAFormCode =pb.ApplicationCode  and rn=1
left join spk_staffmasterdata ss 
on ss.spk_NexBPMCode =pb.ApplyUserId
left join PurPRApplications pp 
on pp.ID =pb.PRId  --660261



 IF OBJECT_ID(N'PLATFORM_ABBOTT_Dev.dbo.FinanceCashierPaymentInfos ', N'U') IS NOT NULL
	BEGIN
		drop table PLATFORM_ABBOTT_Dev.dbo.FinanceCashierPaymentInfos
		select *
        into PLATFORM_ABBOTT_Dev.dbo.FinanceCashierPaymentInfos from #FinanceCashierPaymentInfos
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into PLATFORM_ABBOTT_Dev.dbo.FinanceCashierPaymentInfos from #FinanceCashierPaymentInfos
    -- select * from #vendor_tbl
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
END
