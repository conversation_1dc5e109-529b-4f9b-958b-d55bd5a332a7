CREATE PROCEDURE dbo.sp_PurPOApplicationDetails
AS 
BEGIN

--初始化xml13 sp_PurPOApplicationDetails
--IF OBJECT_ID(N'PLATFORM_ABBOTT_Dev.dbo.XML_13', N'U') IS NOT NULL
--BEGIN
--	PRINT(N'已经初始化过'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
--END
--ELSE
--BEGIN
--	select * into PLATFORM_ABBOTT_Dev.dbo.XML_13 
--	from (
--	select ProcInstId,
--		PR_Item_No,PO_Item_No,PRFormCode,Content
--	from (
--	select ProcInstId,
--    RowData.value('(PR_Item_No/text())[1]', 'nvarchar(255)') AS PR_Item_No,
--    RowData.value('(PO_Item_No/text())[1]', 'nvarchar(255)') AS PO_Item_No,
--    RowData.value('(PRFormCode/text())[1]', 'nvarchar(255)') AS PRFormCode,
--    RowData.value('(Content/text())[1]', 'nvarchar(50)') AS Content
--	FROM PLATFORM_ABBOTT_Dev.dbo.ODS_T_FORMINSTANCE_GLOBAL
--	CROSS APPLY XmlContent.nodes('/root/OrderGridPanel/row') AS XMLTable(ROWDATA)
--		) B		
--) fg 
--PRINT(N'初始化完成'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
--END

SELECT 
NEWID() AS Id,--自动生成的uuid
A.ProcInstId AS POApplicationId,--基于05-1迁移的申请单主信息，以ProcInstId定位对应的PurPRApplications.ID
-- XmlContent.value('(/root/PurchaseOrderApplication_orderTypeBlock_MainStore/OrderType)[1]', 'nvarchar(255)') 
d.Content AS Content,--
B.Num AS Quantity,--
B.Unit AS Unit,--
B.Price AS UnitPrice,--
A.InvoiceType AS InvoiceType,--基于发票类型名称找回对应的字典Code
B.HasTaxTotalAmount AS TotalAmount,--
cast(A.TaxRate as FLOAT)/100 AS TaxRate,--
B.HasTaxTotalAmount-NoTaxTotalAmount AS TaxAmount,--
b.PRFormCode AS ApplicationCode,--
'{}' AS ExtraProperties,--默认填写为"{}"
'' AS ConcurrencyStamp,--?
applicantEmpId AS CreationTime,--与对应的PurPOApplications记录保持一致即可
applicationDate AS CreatorId,--与对应的PurPOApplications记录保持一致即可
'' AS LastModificationTime,--默认为空
'' AS LastModifierId,--默认为空
'0' AS IsDeleted,--默认为0
'' AS DeleterId,--默认为空
'' AS DeletionTime,--默认为空
NoTaxTotalAmount AS TotalAmountNoTax,--
ppdt.ID AS PRDetailId,--以该组合查询到对应的PR明细行ID并填入
'' AS Price,--默认为0
B.PR_Item_No AS RowNo,--
B.PO_Item_No AS PORowNo--(目前无该行，建议在迁移时加上，但可无需迁移到正式库)
INTO #PurPOApplicationDetails_tmp
FROM PLATFORM_ABBOTT_Dev.dbo.ODS_AUTO_BIZ_PurchaseOrderApplication_Info A 
LEFT JOIN PLATFORM_ABBOTT_Dev.dbo.ODS_AUTO_BIZ_PurchaseOrderApplication_Info_PO B 
ON A.ProcInstId =B.ProcInstId 
--LEFT JOIN PLATFORM_ABBOTT_Dev.dbo.PurPRApplications_tmp   C 
--ON C.=B.PRFormCode AND  C.PR_Item_No=B.PO_Item_No 
left join PLATFORM_ABBOTT_Dev.dbo.xml_13 d
on A.ProcInstId =d.ProcInstId and d.PO_Item_No=b.PO_Item_No
left join (select *,ROW_NUMBER () over(PARTITION by ApplicationCode order by ProcInstId desc) rn  from PLATFORM_ABBOTT_Dev.dbo.PurPRApplications_tmp ) ppt 
on B.PRFormCode=ppt.ApplicationCode and rn='1'
left join PLATFORM_ABBOTT_Dev.dbo.PurPRApplicationDetails_tmp ppdt 
on ppt.ProcInstId=ppdt.ProcInstId  and ppdt.RowNo=b.PR_Item_No 

select * from #PurPOApplicationDetails_tmp;

IF OBJECT_ID(N'PLATFORM_ABBOTT_Dev.dbo.PurPOApplicationDetails_tmp', N'U') IS NOT NULL
BEGIN
	update a
	set  a.Content         = b.Content
		,a.Quantity        = b.Quantity
		,a.UnitPrice       = b.UnitPrice
		,a.InvoiceType     = b.InvoiceType
		,a.TotalAmount     = b.TotalAmount
		,a.TaxRate          = b.TaxRate
		,a.TaxAmount        = b.TaxAmount
--		,a.ExtraProperties  = b.ExtraProperties
--		,a.ConcurrencyStamp = b.ConcurrencyStamp
		,a.CreationTime     = b.CreationTime
		,a.CreatorId        = b.CreatorId
--		,a.LastModificationTime = b.LastModificationTime
--		,a.LastModifierId   = b.LastModifierId
--		,a.IsDeleted        = b.IsDeleted
--		,a.DeleterId        = b.DeleterId
--		,a.DeletionTime     = b.DeletionTime
		,a.TotalAmountNoTax = b.TotalAmountNoTax
		,a.PRDetailId       = b.PRDetailId
--		,a.Price            = b.Price
	FROM PLATFORM_ABBOTT_Dev.dbo.PurPOApplicationDetails_tmp a
	LEFT JOIN #PurPOApplicationDetails_tmp b
		ON a.POApplicationId = b.POApplicationId AND a.ApplicationCode = b.ApplicationCode
		and a.RowNo = b.RowNo  and a.PORowNo = b.PORowNo
		
	insert into PLATFORM_ABBOTT_Dev.dbo.PurPOApplicationDetails_tmp
	select   a.Id                       
	        ,a.POApplicationId          
			,a.Content                  
			,a.Quantity  
			,a.Unit
			,a.UnitPrice                
			,a.InvoiceType              
			,a.TotalAmount              
			,a.TaxRate                  
			,a.TaxAmount                
			,a.ApplicationCode          
			,a.ExtraProperties          
			,a.ConcurrencyStamp         
			,a.CreationTime             
			,a.CreatorId                
			,a.LastModificationTime     
			,a.LastModifierId           
			,a.IsDeleted                
			,a.DeleterId                
			,a.DeletionTime             
			,a.TotalAmountNoTax         
			,a.PRDetailId               
			,a.Price                    
			,a.RowNo                    
			,a.PORowNo                  
	from #PurPOApplicationDetails_tmp a 
	WHERE NOT EXISTS (SELECT * FROM PLATFORM_ABBOTT_Dev.dbo.PurPOApplicationDetails_tmp WHERE POApplicationId = a.POApplicationId AND ApplicationCode = a.ApplicationCode
		and RowNo = a.RowNo  and PORowNo = a.PORowNo)
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
			
END
ELSE
BEGIN
	select  *  into PLATFORM_ABBOTT_Dev.dbo.PurPOApplicationDetails_tmp from #PurPOApplicationDetails_tmp
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END

END
