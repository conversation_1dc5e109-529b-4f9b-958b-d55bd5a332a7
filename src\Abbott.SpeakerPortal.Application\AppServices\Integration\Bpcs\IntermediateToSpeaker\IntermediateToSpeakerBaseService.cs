﻿using Abbott.SpeakerPortal.Contracts.Integration.Bpcs;
using Abbott.SpeakerPortal.Entities.Integration.Dspot;
using Abbott.SpeakerPortal.Utils;

using EFCore.BulkExtensions;

using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;

using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Guids;

namespace Abbott.SpeakerPortal.AppServices.Integration.Bpcs
{
    public abstract class IntermediateToSpeakerBaseService<TEntityOri, TEntityTar, TRepoOri, TRepoTar>
        : SpeakerPortalAppService, IIntermediateToSpeakerBaseService
        where TEntityOri : Entity
        where TEntityTar : Entity
        where TRepoOri : IRepository<TEntityOri>
        where TRepoTar : IRepository<TEntityTar>
    {
        /// <summary>
        /// The service provider
        /// </summary>
        protected readonly IServiceProvider _serviceProvider;

        /// <summary>
        /// The logger
        /// </summary>
        private readonly ILogger<IntermediateToSpeakerBaseService<TEntityOri, TEntityTar, TRepoOri, TRepoTar>> _logger;

        /// <summary>
        /// The configuration
        /// </summary>
        protected readonly IConfiguration _configuration;

        public IntermediateToSpeakerBaseService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _logger = serviceProvider.GetService<ILogger<IntermediateToSpeakerBaseService<TEntityOri, TEntityTar, TRepoOri, TRepoTar>>>();
            _configuration = serviceProvider.GetService<IConfiguration>();
        }

        public async Task<int> SyncTableFull(bool repoAutoSave = false)
        {
            int recordCount = 0;
            try
            {
                //查询
                var repoOri = LazyServiceProvider.GetService<TRepoOri>();
                var query = await repoOri.GetQueryableAsync();
                var listOri = query.Where(a => 1 == 1).ToList();
                recordCount = listOri.Count;
                if (listOri?.Any() != true)
                {
                    return recordCount;
                }

                var repoTar = LazyServiceProvider.GetService<TRepoTar>();
                //先清空
                await repoTar.DeleteDirectAsync(a => 1 == 1);

                var datas = ObjectMapper.Map<List<TEntityOri>, List<TEntityTar>>(listOri);

                if (typeof(IManualSetId<Guid>).IsAssignableFrom(typeof(TEntityTar)))
                {
                    var guidGenerator = LazyServiceProvider.LazyGetService<IGuidGenerator>();
                    datas.ForEach(a => (a as IManualSetId<Guid>).SetId(guidGenerator.Create()));
                    var context = await repoTar.GetDbContextAsync();
                    await context.BulkInsertAsync(datas);
                }
                else
                {
                    //插入
                    await repoTar.InsertManyAsync(datas, true);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"SyncTableFull() Exception: {ex}");
                throw;
            }

            return recordCount;
        }

        public virtual async Task<int> SyncTableIncrement(bool repoAutoSave = false)
        {
            int recordCount = 0;
            try
            {
                //查询TEntityOri
                var repoOri = LazyServiceProvider.GetService<TRepoOri>();
                if (repoOri == null)
                {
                    _logger.LogError($"SyncTableIncrement() repoOri is null");
                    return recordCount;
                }
                var query = await repoOri.GetQueryableAsync();
                var listOri = query.ToList();
                recordCount = listOri.Count;
                if (!listOri.Any())
                {
                    return recordCount;
                }

                //1,查询有哪些Ori已存在表BpcsTar
                var repoTar = LazyServiceProvider.GetService<TRepoTar>();
                var queryTar = await repoTar.GetQueryableAsync();
                var exitsTar = queryTar.ToList();

                var guidGenerator = LazyServiceProvider.LazyGetService<IGuidGenerator>();
                List<TEntityTar> updateEntities = new List<TEntityTar>();
                List<TEntityTar> addEntities = new List<TEntityTar>();
                //2,已存在表Tar的，比较Tar是否有更新，有则Update，无则不保存
                foreach (var item in listOri)
                {
                    var exitsEntity = GetTarByOri(exitsTar, item);
                    if (exitsEntity != null)
                    {
                        //比较Tar是否有更新，有则Update，无则不保存
                        if (!item.PropertyEqualAll(exitsEntity))
                        {
                            var updateEntity = item.AssignPropertiesTo(exitsEntity);
                            if (updateEntity != null)
                            {
                                updateEntities.Add(updateEntity);
                            }
                        }
                    }
                    else
                    {
                        //3,未存在表BpcsGlh的，Insert BpcsGlh
                        var entity = ObjectMapper.Map<TEntityOri, TEntityTar>(item);

                        //手动设置Id
                        var manualSetId = entity as IManualSetId<Guid>;
                        if (manualSetId != null)
                            manualSetId.SetId(guidGenerator.Create());

                        addEntities.Add(entity);
                    }
                }

                //更新
                if (updateEntities.Any())
                {
                    //await repoTar.UpdateManyAsync(updateEntities, repoAutoSave);
                    var context = await repoTar.GetDbContextAsync();
                    await context.BulkUpdateAsync(updateEntities);
                }
                //插入
                if (addEntities.Any())
                {
                    //await repoTar.InsertManyAsync(addEntities, repoAutoSave);
                    var context = await repoTar.GetDbContextAsync();
                    await context.BulkInsertAsync(addEntities);
                }

                AfterSyncIncrement(updateEntities, addEntities);
            }
            catch (Exception ex)
            {
                _logger.LogError($"SyncTableIncrement() Exception: {ex}");
                throw;
            }
            return recordCount;
        }

        protected abstract TEntityTar GetTarByOri(List<TEntityTar> tarList, TEntityOri ori);

        protected virtual string AfterSyncIncrement(List<TEntityTar> updateEntities, List<TEntityTar> addEntities)
        {
            return null;
        }
    }
}