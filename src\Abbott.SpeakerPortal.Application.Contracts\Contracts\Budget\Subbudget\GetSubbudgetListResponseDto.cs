﻿using Abbott.SpeakerPortal.Contracts.Common.Attachment;
using Abbott.SpeakerPortal.Enums;

using MiniExcelLibs.Attributes;

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;

namespace Abbott.SpeakerPortal.Contracts.Budget.Subbudget
{
    public class GetSubbudgetListResponseDto
    {
        /// <summary>
        /// 子预算Id
        /// </summary>
        [ExcelIgnore]
        public Guid Id { get; set; }

        /// <summary>
        /// 主预算编号
        /// </summary>
        [ExcelColumnName("主预算编号")]
        public string MasterBudgetCode { get; set; }

        #region new column for pr master report

        /// <summary>
        /// 主预算描述
        /// </summary>
        [ExcelColumnName("主预算描述")]
        [JsonIgnore]
        public string MasterBudgetDescription { get; set; }

        #endregion

        /// <summary>
        /// 子预算BU名称
        /// </summary>
        [ExcelColumnName("BU")]
        public string BuName { get; set; }

        #region new column for pr master report

        /// <summary>
        /// 主预算负责人
        /// </summary>
        [ExcelColumnName("主预算负责人")]
        [JsonIgnore]
        public string MasterBudgetOwnerName { get; set; }

        /// <summary>
        /// 主预算金额
        /// </summary>
        [ExcelColumnName("主预算金额")]
        [JsonIgnore]
        public decimal MasterBudgetAmount { get; set; }

        #endregion

        /// <summary>
        /// 子预算编号
        /// </summary>
        [ExcelColumnName("子预算编号")]
        public string SubbudgetCode { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        [ExcelColumnName("描述")]
        public string Description { get; set; }

        /// <summary>
        /// 子预算负责人姓名
        /// </summary>
        [ExcelColumnName("负责人")]
        public string OwnerName { get; set; }

        /// <summary>
        /// 子预算成本中心名称
        /// </summary>
        [ExcelColumnName("成本中心")]
        public string CostCenterName { get; set; }
        /// <summary>
        /// 子预算大区名称
        /// </summary>
        [ExcelColumnName("大区")]
        public string RegionName { get; set; }

        [ExcelColumnName("预算金额")]
        [JsonIgnore]
        public string BudgetAmountText => BudgetAmount.ToString("N2");

        #region new column for pr master report

        /// <summary>
        /// PR Committed
        /// </summary>
        [ExcelColumnName("PR Committed")]
        [JsonIgnore]
        public decimal PRCommitted { get; set; }

        /// <summary>
        /// PR Saving
        /// </summary>
        [ExcelColumnName("PR Saving")]
        [JsonIgnore]
        public decimal PRSaving { get; set; }

        /// <summary>
        /// PR Pending Amount
        /// </summary>
        [ExcelColumnName("PR Pending Amount")]
        [JsonIgnore]
        public decimal PRPendingAmount { get; set; }

        /// <summary>
        /// PR Approve Amount
        /// </summary>
        [ExcelColumnName("PR Approve Amount")]
        [JsonIgnore]
        public decimal PRApproveAmount { get; set; }

        /// <summary>
        /// PO Pending Amount
        /// </summary>
        [ExcelColumnName("PO Pending Amount")]
        [JsonIgnore]
        public decimal POPendingAmount { get; set; }

        /// <summary>
        /// PO Approve Amount
        /// </summary>
        [ExcelColumnName("PO Approve Amount")]
        [JsonIgnore]
        public decimal POApproveAmount { get; set; }

        /// <summary>
        /// PO Close Amount
        /// </summary>
        [ExcelColumnName("PO Close Amount")]
        [JsonIgnore]
        public decimal POCloseAmount { get; set; }

        /// <summary>
        /// PA Pending Amount
        /// </summary>
        [ExcelColumnName("PA Pending Amount")]
        [JsonIgnore]
        public decimal PAPendingAmount { get; set; }

        /// <summary>
        /// PA Processed Amount
        /// </summary>
        [ExcelColumnName("PA Processed Amount")]
        [JsonIgnore]
        public decimal PAProcessedAmount { get; set; }

        /// <summary>
        /// Debit Note
        /// </summary>
        [ExcelColumnName("Debit Note")]
        [JsonIgnore]
        public string DebitNote { get; set; }

        /// <summary>
        /// Category Balance
        /// </summary>
        [ExcelColumnName("Category Balance")]
        [JsonIgnore]
        public decimal CategoryBalance { get; set; }

        #endregion

        /// <summary>
        /// 子预算负责人邮箱
        /// </summary>
        [ExcelColumnName("负责人邮箱")]
        public string OwnerEmail { get; set; }

        /// <summary>
        /// 子预算状态文本
        /// </summary>
        [ExcelColumnName("子预算状态")]
        [JsonIgnore]
        public string StatusText => Status ? "开启" : "冻结";

        [ExcelColumnName("已启用金额")]
        [JsonIgnore]
        public string EnableAmountText => EnableAmount.ToString("N2");

        [ExcelColumnName("已用金额")]
        [JsonIgnore]
        public string UsedAmountText => UsedAmount.ToString("N2");

        [ExcelColumnName("可用金额")]
        [JsonIgnore]
        public string AvailableAmountText => AvailableAmount.ToString("N2");

        /// <summary>
        /// 年度
        /// </summary>
        [ExcelColumnName("年度")]
        public int? Year { get; set; }

        /// <summary>
        /// 子预算金额
        /// </summary>
        [ExcelColumnName("预算金额")]
        [ExcelIgnore]
        public decimal BudgetAmount { get; set; }
        /// <summary>
        /// 已用金额
        /// </summary>
        [ExcelColumnName("已用金额")]
        [ExcelIgnore]
        public decimal UsedAmount { get; set; }
        /// <summary>
        /// 可用金额
        /// </summary>
        [ExcelIgnore]
        public decimal AvailableAmount { get { return this.EnableAmount - this.UsedAmount; } }
        /// <summary>
        /// 启用金额
        /// </summary>
        [ExcelIgnore]
        public decimal EnableAmount { get { return this.Monthlies.Where(m => m.Value.Status).Sum(s => s.Value.BudgetAmount); } }

        /// <summary>
        /// 主预算Id
        /// </summary>
        [ExcelIgnore]
        public Guid MasterBudgetId { get; set; }

        /// <summary>
        /// 子预算BuId
        /// </summary>
        [ExcelIgnore]
        public Guid BuId { get; set; }
        /// <summary>
        /// 子预算成本中心Id
        /// </summary>
        [ExcelIgnore]
        public Guid CostCenterId { get; set; }
        /// <summary>
        /// 子预算大区Id
        /// </summary>
        [ExcelIgnore]
        public Guid RegionId { get; set; }
        /// <summary>
        /// 子预算负责人Id
        /// </summary>
        [ExcelIgnore]
        public Guid OwnerId { get; set; }

        #region 月份导出文本
        /// <summary>
        /// 一月预算
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("一月预算")]
        public string JanAmountText => JanAmount?.ToString("N2");
        /// <summary>
        /// 一月状态
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("一月状态")]
        public string JanStatusText => JanStatus == true ? "是" : "否";
        /// <summary>
        /// 二月预算
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("二月预算")]
        public string FebAmountText => FebAmount?.ToString("N2");
        /// <summary>
        /// 二月状态
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("二月状态")]
        public string FebStatusText => FebStatus == true ? "是" : "否";
        /// <summary>
        /// 三月预算
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("三月预算")]
        public string MarAmountText => MarAmount?.ToString("N2");
        /// <summary>
        /// 三月状态
        /// </summary>
        [ExcelColumnName("三月状态")]
        [JsonIgnore]
        public string MarStatusText => MarStatus == true ? "是" : "否";
        /// <summary>
        /// 四月预算
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("四月预算")]
        public string AprAmountText => AprAmount?.ToString("N2");
        /// <summary>
        /// 四月状态
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("四月状态")]
        public string AprStatusText => AprStatus == true ? "是" : "否";
        /// <summary>
        /// 五月预算
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("五月预算")]
        public string MayAmountText => MayAmount?.ToString("N2");
        /// <summary>
        /// 五月状态
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("五月状态")]
        public string MayStatusText => MayStatus == true ? "是" : "否";
        /// <summary>
        /// 六月预算
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("六月预算")]
        public string JunAmountText => JunAmount?.ToString("N2");
        /// <summary>
        ///六月状态
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("六月状态")]
        public string JunStatusText => JunStatus == true ? "是" : "否";
        /// <summary>
        /// 七月预算
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("七月预算")]
        public string JulAmountText => JulAmount?.ToString("N2");
        /// <summary>
        /// 七月状态
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("七月状态")]
        public string JulStatusText => JulStatus == true ? "是" : "否";
        /// <summary>
        /// 八月预算
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("八月预算")]
        public string AugAmountText => AugAmount?.ToString("N2");
        /// <summary>
        /// 八月状态
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("八月状态")]
        public string AugStatusText => AugStatus == true ? "是" : "否";
        /// <summary>
        /// 九月预算
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("九月预算")]
        public string SeptAmountText => SeptAmount?.ToString("N2");
        /// <summary>
        /// 九月状态
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("九月状态")]
        public string SeptStatusText => SeptStatus == true ? "是" : "否";
        /// <summary>
        /// 十月预算
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("十月预算")]
        public string OctAmountText => OctAmount?.ToString("N2");
        /// <summary>
        /// 十月状态
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("十月状态")]
        public string OctStatusText => OctStatus == true ? "是" : "否";
        /// <summary>
        /// 十一月预算
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("十一月预算")]
        public string NovAmountText => NovAmount?.ToString("N2");
        /// <summary>
        /// 十一月状态
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("十一月状态")]
        public string NovStatusText => NovStatus == true ? "是" : "否";
        /// <summary>
        /// 十二月预算
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("十二月预算")]
        public string DecAmountText => DecAmount?.ToString("N2");
        /// <summary>
        /// 十二月状态
        /// </summary>
        [JsonIgnore]
        [ExcelColumnName("十二月状态")]
        public string DecStatusText => DecStatus == true ? "是" : "否";
        #endregion
        #region EPD特有字段
        /// <summary>
        /// EPD特有BU2
        /// </summary>
        [Category("EPD")]
        [ExcelColumnName("BU2")]
        public string BU2 { get; set; }
        ///// <summary>
        ///// EPD特有负责人Id
        ///// </summary>
        //public Guid? OwnerId2 { get; set; }
        /// <summary>
        /// EPD特有负责人姓名
        /// </summary>
        [Category("EPD")]
        [ExcelColumnName("负责人")]
        public string OwnerName2 { get; set; }
        ///// <summary>
        ///// EPD特有大区经理Id
        ///// </summary>
        //public Guid? RegionManagerId { get; set; }
        /// <summary>
        /// EPD特有大区经理姓名
        /// </summary>
        [Category("EPD")]
        [ExcelColumnName("大区经理")]
        public string RegionManagerName { get; set; }
        ///// <summary>
        ///// EPD特有LMM Id
        ///// </summary>
        //public Guid? LMMId { get; set; }
        /// <summary>
        /// EPD特有LMM姓名
        /// </summary>
        [Category("EPD")]
        [ExcelColumnName("LMM")]
        public string LMMName { get; set; }
        ///// <summary>
        ///// 产品经理Id
        ///// </summary>
        //public Guid? ProductManagerId { get; set; }
        /// <summary>
        /// 产品经理姓名
        /// </summary>
        [Category("EPD")]
        [ExcelColumnName("产品经理")]
        public string ProductManagerName { get; set; }
        #endregion

        #region ADC特有字段
        /// <summary>
        /// ADC特有是否合规审计
        /// </summary>
        [Category("ADC")]
        [ExcelColumnName("合规审计")]
        public bool? IsComplicanceAudits { get; set; }
        #endregion

        /// <summary>
        /// 状态
        /// </summary>
        [ExcelIgnore]
        public bool Status { get; set; }

        /// <summary>
        /// 是否可删除
        /// </summary>
        [ExcelIgnore]
        public bool IsDeletable { get; set; }
        /// <summary>
        /// 附件
        /// </summary>
        [ExcelIgnore]
        public List<UploadFileResponseDto> AttachmentInformation { get; set; }
        /// <summary>
        /// 一月预算
        /// </summary>
        [ExcelIgnore]
        public decimal? JanAmount { get { return Monthlies.GetOrDefault(Month.Jan)?.BudgetAmount; } }
        /// <summary>
        /// 一月状态
        /// </summary>
        [ExcelIgnore]
        public bool? JanStatus { get { return Monthlies.GetOrDefault(Month.Jan)?.Status; } }
        /// <summary>
        /// 二月预算
        /// </summary>
        [ExcelIgnore]
        public decimal? FebAmount { get { return Monthlies.GetOrDefault(Month.Feb)?.BudgetAmount; } }
        /// <summary>
        /// 二月状态
        /// </summary>
        [ExcelIgnore]
        public bool? FebStatus { get { return Monthlies.GetOrDefault(Month.Feb)?.Status; } }
        /// <summary>
        /// 三月预算
        /// </summary>
        [ExcelIgnore]
        public decimal? MarAmount { get { return Monthlies.GetOrDefault(Month.Mar)?.BudgetAmount; } }
        /// <summary>
        /// 三月状态
        /// </summary>
        [ExcelIgnore]
        public bool? MarStatus { get { return Monthlies.GetOrDefault(Month.Mar)?.Status; } }
        /// <summary>
        /// 四月预算
        /// </summary>
        [ExcelIgnore]
        public decimal? AprAmount { get { return Monthlies.GetOrDefault(Month.Apr)?.BudgetAmount; } }
        /// <summary>
        /// 四月状态
        /// </summary>
        [ExcelIgnore]
        public bool? AprStatus { get { return Monthlies.GetOrDefault(Month.Apr)?.Status; } }
        /// <summary>
        /// 五月预算
        /// </summary>
        [ExcelIgnore]
        public decimal? MayAmount { get { return Monthlies.GetOrDefault(Month.May)?.BudgetAmount; } }
        /// <summary>
        /// 五月状态
        /// </summary>
        [ExcelIgnore]
        public bool? MayStatus { get { return Monthlies.GetOrDefault(Month.May)?.Status; } }
        /// <summary>
        /// 六月预算
        /// </summary>
        [ExcelIgnore]
        public decimal? JunAmount { get { return Monthlies.GetOrDefault(Month.Jun)?.BudgetAmount; } }
        /// <summary>
        ///六月状态
        /// </summary>
        [ExcelIgnore]
        public bool? JunStatus { get { return Monthlies.GetOrDefault(Month.Jun)?.Status; } }
        /// <summary>
        /// 七月预算
        /// </summary>
        [ExcelIgnore]
        public decimal? JulAmount { get { return Monthlies.GetOrDefault(Month.Jul)?.BudgetAmount; } }
        /// <summary>
        /// 七月状态
        /// </summary>
        [ExcelIgnore]
        public bool? JulStatus { get { return Monthlies.GetOrDefault(Month.Jul)?.Status; } }
        /// <summary>
        /// 八月预算
        /// </summary>
        [ExcelIgnore]
        public decimal? AugAmount { get { return Monthlies.GetOrDefault(Month.Aug)?.BudgetAmount; } }
        /// <summary>
        /// 八月状态
        /// </summary>
        [ExcelIgnore]
        public bool? AugStatus { get { return Monthlies.GetOrDefault(Month.Aug)?.Status; } }
        /// <summary>
        /// 九月预算
        /// </summary>
        [ExcelIgnore]
        public decimal? SeptAmount { get { return Monthlies.GetOrDefault(Month.Sept)?.BudgetAmount; } }
        /// <summary>
        /// 九月状态
        /// </summary>
        [ExcelIgnore]
        public bool? SeptStatus { get { return Monthlies.GetOrDefault(Month.Sept)?.Status; } }
        /// <summary>
        /// 十月预算
        /// </summary>
        [ExcelIgnore]
        public decimal? OctAmount { get { return Monthlies.GetOrDefault(Month.Oct)?.BudgetAmount; } }
        /// <summary>
        /// 十月状态
        /// </summary>
        [ExcelIgnore]
        public bool? OctStatus { get { return Monthlies.GetOrDefault(Month.Oct)?.Status; } }
        /// <summary>
        /// 十一月预算
        /// </summary>
        [ExcelIgnore]
        public decimal? NovAmount { get { return Monthlies.GetOrDefault(Month.Nov)?.BudgetAmount; } }
        /// <summary>
        /// 十一月状态
        /// </summary>
        [ExcelIgnore]
        public bool? NovStatus { get => Monthlies.GetOrDefault(Month.Nov)?.Status; }
        /// <summary>
        /// 十二月预算
        /// </summary>
        [ExcelIgnore]
        public decimal? DecAmount { get { return Monthlies.GetOrDefault(Month.Dec)?.BudgetAmount; } }
        /// <summary>
        /// 十二月状态
        /// </summary>
        [ExcelIgnore]
        public bool? DecStatus { get { return Monthlies.GetOrDefault(Month.Dec)?.Status; } }
        [JsonIgnore]
        [ExcelIgnore]
        public Dictionary<Month, MonthlyBudgetDto> Monthlies { get; set; }
    }
}
