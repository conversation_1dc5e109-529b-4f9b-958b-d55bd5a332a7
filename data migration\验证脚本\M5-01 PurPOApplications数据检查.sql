基本规则：查询[AUTO_BIZ_PurchaseOrderApplication_Info]得到历史的采购订单申请单据，
以单据号Join [Form_92ccaf9b95de4d7d9d8b411b2a030edc]得到采购订单申请状态，
以ProcInstId join [AUTO_BIZ_PurchaseOrderApplication_Info_PR]得到对应的可能多行比价申请明细信息


-- 
use  PLATFORM_ABBOTT;
--CREATE  index procinstid  ON ODS_AUTO_BIZ_PurchaseOrderApplication_Info (procinstid);
--CREATE  index procinstid  ON ODS_T_FORMINSTANCE_GLOBAL_bak (procinstid);
--CREATE  index procinstid  ON ODS_Form_92ccaf9b95de4d7d9d8b411b2a030edc (procinstid);
--CREATE  index procinstid ON PurPOApplications_tmp(procinstid);
--CREATE  index id ON PurPOApplications_tmp(id);
--CREATE  index id ON PurPOApplications(id);

------------- 数据准备
 
DROP TABLE #temp_global;

SELECT 
	q.ProcInstId,
	XmlContent.value('(/root/PurchaseOrderApplication_InvoiceInfoBlock_MainStore/OpeningBank)[1]', 'nvarchar(255)') AS OpenBank,
	XmlContent.value('(/root/PurchaseOrderApplication_InvoiceInfoBlock_MainStore/BankAccount)[1]', 'nvarchar(255)') AS BankAccount,
	XmlContent.value('(/root/PurchaseOrderApplication_orderTypeBlock_MainStore/OrderType)[1]', 'nvarchar(255)') as OrderType,
	XmlContent.value('(/root/PurchaseOrderApplication_hiddenBlock_MainStore/PDFDownloadStatus)[1]', 'nvarchar(255)') as PDFDownloadStatus,
	XmlContent.query('/root/AccessoryGrid/row') AS AccessoryGrid
into temp_global_m5_01
from 
--ODS_T_FORMINSTANCE_GLOBAL w
ODS_T_FORMINSTANCE_GLOBAL_bak w,
ODS_AUTO_BIZ_PurchaseOrderApplication_Info q
where q.ProcInstId =w.ProcInstId 
;
CREATE  index procinstid  ON temp_global_m5_01 (procinstid);

----------------------
 
DROP TABLE #up_id_value;
 
SELECT  
	t.ProcInstId,
	x.row.value('(up_Id)[1]','nvarchar(120)') as up_Id  
into #up_id_value
from  temp_global_m5_01 t
CROSS APPLY t.AccessoryGrid.nodes('/row') AS x(row)
;
CREATE  index #up_Id  ON #up_id_value (ProcInstId);
CREATE  index #up_Id_file  ON #up_id_value (up_Id);

---

DROP TABLE #up_id_value_id;

SELECT 
	a.ProcInstId,
	STRING_AGG( cast(b.id as nvarchar(120)), ',') WITHIN GROUP (ORDER BY b.id)  as id
into #up_id_value_id
from 
	#up_id_value a,
	Attachments b
where 
	b.BPMId=a.up_Id
GROUP BY 
	a.ProcInstId
;
CREATE  index #up_id  ON #up_id_value_id (ProcInstId);

-- SELECT * from #up_id_value_id
-----------------

DROP TABLE #PRApplicationDetails;


SELECT 
	ApplicationCode
	,STRING_AGG(cast(b.id as nvarchar(max)), ',') WITHIN GROUP (ORDER BY b.id) AS id 
into #PRApplicationDetails
from 
	PurPRApplications a,
	PurPRApplicationDetails b
where a.id =b.PRApplicationId
group by a.ApplicationCode
;
CREATE  index ApplicationCode  ON #PRApplicationDetails (ApplicationCode);


-- SELECT  * from #PRApplicationDetails 
---------
IF OBJECT_ID('#vendor_id', 'U') IS NOT NULL DROP TABLE  #vendor_id;
 
SELECT
	a.id
	,TRIM(b.VEMLAD) VEMLAD,
	cast(VNDERX as NVARCHAR(120)) as VNDERX
	,cast(VMCMPY as NVARCHAR(120)) as VMCMPY
	,trim(b.[VEXTNM]) as VEXTNM
into #vendor_id
from
     ODS_BPCS_AVM a ,
	 ODS_BPCS_PMFVM b  
where
	a.VCMPNY=b.VMCMPY
	and a.VENDOR=b.VNDERX
;
-----------

SELECT * 
into #pric_Historys
from (
	select * ,ROW_NUMBER () over (PARTITION by  ProcInstId order by FinishDate desc) rn from  ODS_T_PROCESS_Historys)  t 
where rn=1;

CREATE  index procinstid  ON #pric_Historys (procinstid);

--------------


 DROP TABLE #PurPOApplications;

SELECT 
	q.ProcInstId
	,w.processStatus
	,q.serialNumber 	                            			as ApplicationCode 	
	,e.id 														as PRApplicationDetailId
	,case 
		when trim(processStatus)=N'PO审批结束' and trim(PDFDownloadStatus)='1'  	then '发起收货' --10
		when trim(processStatus)=N'PO审批结束' and trim(PDFDownloadStatus)<>'1'  	then null
		when trim(processStatus)=N'发起人终止' and trim(actname)=N'重发起'  			then '作废'
		when trim(processStatus)=N'发起人终止' and trim(actname)<>N'重发起'  	    then '已拒绝'
		when trim(processStatus)=N'关闭'       								    then '关闭'
		when trim(processStatus) in (N'主采购循环审批中',N'重发起' )  					then '退回'
	 end														AS Status 
	,UPPER(ss.spk_NexBPMCode) 									as ApplyUserId
	,q.applicationDate											as ApplyTime
	,r.ApplyUserBu
	,q.company_Value,y.spk_CompanyCode,u.VMCMPY,q.SupplierCode,q.SupplierName,t.res_code,w.PRFormCode
	,case 
		when trim(o.OrderType)=N'采购订单' THEN 1
	    when trim(o.OrderType)=N'形式订单' THEN 2
		end   as 												POType  
	,UPPER(y.spk_NexBPMCode) 									as CompanyId
	,u.id 														as VendorId
	,q.SupplierAttribute
	,case 
		when q.SupplierAttribute=N'不适用' then '0'
 		when q.SupplierAttribute=N'APS'  then '1' 
 		  end				 	  		  AS                    VendorPorperty
	,[Address] as                               				[RegCertificateAddress]
	,[Contacts] as                              				[ContactName]
	,[TelPhone] as                               				[ContactPhone]
	,[Email] as                              					[ContactEmail]
	,[S_Fax] as                               					[FaxNumber]
--	,[TermCode] as                              				[PaymentTerm]
	,CASE 
		WHEN CHARINDEX('_', TermCode) > 0 THEN 
			LEFT(TermCode, CHARINDEX('_', TermCode) - 1) 
			ELSE NULL END 								AS  	PaymentTerm
	,[Currency_Value] as                              			[Currency]
	,[ExchangeRate] as                               			[ExchangeRate]
	,'' as                               						[PRType]
	,'' as                               						[PRCorrespond]
	,[MattersNeedAttention] as                               	[AttentionNote]
	,[DeliveryMethod] as                               			[DeliveryType]
	,[DeliveryLocation_Text] as                              	[DeliveryAddress]
	,[PaymentCondition_Text] as                              	[PaymentCondition]
	,[DeliveryData] as                              			[DeliveryDate]
	,[ShelfLife] as                               				[Qualitystandard]
	,[Other] as                              					[Others]
	,q.[Remark] as                               				[Remark]
	,a.id		as 												AttachmentFile   
	,'{}'		as 												ExtraProperties
	,q.applicationDate			as								[CreationTime]
	,UPPER(ss.spk_NexBPMCode)			as 						CreatorId
	,0 				as											IsDeleted
	,q.[Saving] as                              				[Saving]
	,o.BankAccount as                               			[BankAccount]   
	,q.[InvoiceAddress] as                             			[InvoiceAddress]
	,[Fax] as                              						[InvoiceFax]
	,[InvoicePleaseOpen] as                               		[InvoiceTitle]
	,o.OpenBank    as                              				[OpenBank]              
	,q.[NotTaxTotalAmount] as                               	[TotalAmount]
	,q.[totalAmount] as                              			[TotalAmountTax]
--	,[PRFormCode] as                              				[ApplyUserBuName]         
	,r.ApplyUserBuName
	,q.[applicantEmpName] as                               		[ApplyUserName]
	,q.[supplierName] as                                     	[VendorName]
	 ,case WHEN IsPOLate='true'  THEN 1 when IsPOLate='false' then 0  END AS 			IsLate
--	,[ASNType_Value] as                                         [ApsPorperty]              
	,UPPER(di2.spk_NexBPMCode) as								ApsPorperty
	,[Currency_Value] as                                        [CurrencySymbol]           --BA 留空
	,trim([Phone]) as                                         	[PhoneNumber]
	,r.id as                               						[PRId]                 
	,q.[POApprovedDate] as                              		[ApprovedDate]
	,q.[SupplierCode] as                               			[VendorCode]
	,q.[applicantDept_Text] as                               	[ApplyUserBuToDeptName]
	,UPPER(soc3.spk_NexBPMCode) 				as				ApplyUserDept
into #PurPOApplications
FROM  
	ODS_AUTO_BIZ_PurchaseOrderApplication_Info q
left join 
	ODS_Form_92ccaf9b95de4d7d9d8b411b2a030edc w
	on q.ProcInstId =w.ProcInstId 
LEFT JOIN 
	#PRApplicationDetails e
	on w.PRFormCode= e.ApplicationCode
left join 
	 spk_staffmasterdata ss 
	on q.applicantEmpId =ss.bpm_id
left join 
	PurPRApplications r
	on w.PRFormCode= r.ApplicationCode 
left join 
	 ODS_T_Resource t
	on q.company_Value=t.Res_Data and t.Res_Parent_Code='61a3f911b5ae4bc98cddd441833d861e'
left join 
	spk_companymasterdata y
	on t.res_code = y.spk_BPMCode
left JOIN 
 	#vendor_id u
 	on 
	 	q.SupplierCode= u.[VNDERX] 
	 	and (trim(q.SupplierName)  =u.[VEXTNM] 
	 	or y.spk_CompanyCode =u.[VMCMPY]
	 	or q.company_Value = u.[VMCMPY])
left join 
	spk_organizationalmasterdata soc3 
	on q.applicantDeptId =soc3.spk_BPMCode 
--left join
--    PurPRApplications_tmp i
--    on q.ProcInstId =i.ProcInstId 
left join 
	spk_dictionary di2
	on q.ASNType_Value  =di2.spk_BPMCode  and di2.spk_type=N'APS属性'
left join 
	temp_global_m5_01 o
	on q.ProcInstId =o.ProcInstId 
left join
	#pric_Historys p 
	on q.ProcInstId =p.ProcInstID 
left join 
	#up_id_value_id a
	on q.ProcInstId =a.ProcInstID 

;
CREATE  index procinstid  ON #PurPOApplications (procinstid);


 ------------------

SELECT  [Id]
from PurPOApplications
where id  is null ;

 SELECT count(*) from #PurPOApplications  -- 99596
 SELECT count(*) from PurPOApplications  -- 99588
 ------------------------
 



declare @columnName NVARCHAR(128)  ='PRApplicationDetailId';
declare @SQL NVARCHAR(MAX);
SET @SQL='
SELECT  
	TOP 100
	t1.ProcInstId,t2.id,
	t1.'+quotename(@columnName)+',t2.'+quotename(@columnName)+'
from
  	(select * from  #PurPOApplications where  '+quotename(@columnName)+' is not null  AND cast('+ quotename(@columnName) +' as nvarchar(120)) <> ''NULL'') t1
full join
 	PurPOApplications_tmp  t3
     on t1.ProcInstId = t3.ProcInstId  
left join 
	PurPOApplications t2
	on t3.id = t2.id
WHERE  
  t1.'+quotename(@columnName)+'<> t2.'+quotename(@columnName)+'
  OR (t1.'+quotename(@columnName)+' IS NULL AND t2.'+quotename(@columnName)+' IS NOT NULL)
  OR (t1.'+quotename(@columnName)+' IS NOT NULL AND t2.'+quotename(@columnName)+' IS  NULL)
';
SELECT  @SQL;
--EXEC sp_executesql @SQL;





-----------


 

DECLARE @TableName NVARCHAR(MAX) = '#PurPOApplications'; -- 检查临时表 字段全null，可能就有问题
DECLARE @TagetTableName NVARCHAR(MAX) = 'PurPOApplications'; -- 检查临时表 字段全null，可能就有问题
DECLARE @T1_BaseCLoumns NVARCHAR(MAX) = 't1.ProcInstId,t2.ProcInstId,t2.id '; -- 检查临时表 字段全null，可能就有问题
DECLARE @T2_BaseCLoumns NVARCHAR(MAX) = 'a.id ,ppt.ProcInstId '; -- 检查临时表 字段全null，可能就有问题
DECLARE @T1_T2_ON NVARCHAR(MAX) = ' t1.ProcInstId = t2.ProcInstId  ';  
DECLARE @SQL NVARCHAR(MAX) = '';
DECLARE @ColumnName NVARCHAR(MAX);
DECLARE @DataType NVARCHAR(50);
DECLARE db_cursor CURSOR FOR
SELECT COLUMN_NAME, DATA_TYPE
FROM tempdb.INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME LIKE @TableName + '%' -- 临时表的名称
AND TABLE_SCHEMA = 'dbo' -- 如果使用的是其他模式，请修改为对应的模式
and COLUMN_NAME in 
('VendorPorperty')
--('ApprovedDate','PRId' ,'openbank', 'BankAccount')
--('AttachmentFile', 'AttentionNote', 'Others' ,'Remark', 'saving')
--('VendorPorperty' , 'wendorld'  ,'POType', 'Status' )

-- (PRApplicationDetailId)
OPEN db_cursor
FETCH NEXT FROM db_cursor INTO @ColumnName, @DataType
-- 遍历每个列
WHILE @@FETCH_STATUS = 0
BEGIN
    -- 针对字符串类型，检查是否为空字符串；对于其他类型，检查是否为 NULL
    IF @DataType IN ('nvarchar', 'varchar', 'char', 'text')  -- 字符串类型
    BEGIN
		SET @SQL='
			SELECT TOP 100 '+@T1_BaseCLoumns+',t1.'+quotename(@columnName)+',t2.'+quotename(@columnName)+'
			from
			(
				select '+quotename(@columnName)+', ProcInstId  from '+quotename(@TableName)+' 
				where  '+quotename(@columnName)+' is not null 	AND '+QUOTENAME(@ColumnName) + ' <> ''''
			 ) t1
			full join
			(
				select  '+@T2_BaseCLoumns+',a.'+quotename(@columnName)+'
				from '+quotename(@TagetTableName)+' a
				join 
					'+@TagetTableName+'_tmp ppt 
					on a.id =ppt.id
			    where a.'+quotename(@columnName)+' is not null 	AND a.'+QUOTENAME(@ColumnName) + ' <> ''''
			) t2
			     on  '+@T1_T2_ON+'
			WHERE  
			  t1.'+quotename(@columnName)+'<> t2.'+quotename(@columnName)+'
			  OR (t1.'+quotename(@columnName)+' IS NULL AND t2.'+quotename(@columnName)+' IS NOT NULL)
			  OR (t1.'+quotename(@columnName)+' IS NOT NULL AND t2.'+quotename(@columnName)+' IS  NULL)
			';
    END
    ELSE  -- 非字符串类型，仅检查 NULL
    BEGIN
		SET @SQL='
			SELECT TOP 100 '+@T1_BaseCLoumns+',t1.'+quotename(@columnName)+',t2.'+quotename(@columnName)+'
			from
			  	(select '+quotename(@columnName)+',ProcInstId from '+quotename(@TableName)+' where  '+quotename(@columnName)+' is not null ) t1
			full join
			(
				select  '+@T2_BaseCLoumns+',a.'+quotename(@columnName)+'
				from '+quotename(@TagetTableName)+' a
				join 
					'+@TagetTableName+'_tmp ppt 
					on a.id =ppt.id
			    where a.'+quotename(@columnName)+' is not null  
			) t2
			     on  '+@T1_T2_ON+'
			WHERE  
			  t1.'+quotename(@columnName)+'<> t2.'+quotename(@columnName)+'
			  OR (t1.'+quotename(@columnName)+' IS NULL AND t2.'+quotename(@columnName)+' IS NOT NULL)
			  OR (t1.'+quotename(@columnName)+' IS NOT NULL AND t2.'+quotename(@columnName)+' IS  NULL)
			';
    END
--	SELECT @SQL;
    -- 继续获取下一个列名
 	-- 执行单条SQL查询
    EXEC sp_executesql @SQL;
    FETCH NEXT FROM db_cursor INTO @ColumnName, @DataType
END
CLOSE db_cursor
DEALLOCATE db_cursor

---------------

 

