﻿using System;
using System.Collections.Generic;

namespace Abbott.SpeakerPortal.Contracts.Budget.FocSubbudget
{
    public class FocSubBudgetResponseDto
    {
        /// <summary>
        /// 预算Id
        /// </summary>
        public Guid Id { get; set; }
        /// <summary>
        /// 预算编号
        /// </summary>
        public string Code { get; set; }
        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; }
        /// <summary>
        /// 负责人Id
        /// </summary>
        public Guid OwnerId { get; set; }
        /// <summary>
        /// 负责人姓名
        /// </summary>
        public string OwnerName { get; set; }
        /// <summary>
        /// 成本中心
        /// </summary>
        public Guid CostCenterId { get; set; }
        /// <summary>
        /// 成本中心
        /// </summary>
        public string CostCenterName { get; set; }
        /// <summary>
        /// 大区
        /// </summary>
        public Guid RegionId { get; set; }
        /// <summary>
        /// 大区名称
        /// </summary>
        public string RegionName { get; set; }
        /// <summary>
        /// 预算数量
        /// </summary>
        public int SubbudgetQty { get; set; }
        /// <summary>
        /// 月份数量
        /// </summary>
        public List<FocMonthBudgetReponseDto> MonthBudgets { get; set; }
    }
}
