CREATE PROCEDURE dbo.sp_OECInterceptOperateHistorys_ns
AS 
BEGIN
	--飞检拦截状态信息-历史详细信息
with OECInterceptOperateHistorys_ns as (
select 
oech.Id,
t1.Id AS PRDetailId,
oech.OperateType,
oech.OperateContent,
oech.Remark,
oech.OperateTime,
oech.ExtraProperties,
oech.ConcurrencyStamp,
oech.CreationTime,
t3.spk_NexBPMCode AS CreatorId,
oech.LastModificationTime,
oech.LastModifierId,
oech.IsDeleted,
oech.DeleterId,
oech.DeletionTime,
t2.Id AS InterceptId,
t3.spk_NexBPMCode AS  OperateUserId,
oech.OperateUserName,
oech.SendBackLimitAmount,
oech.SolveInterceptType
FROM [PLATFORM_ABBOTT_Dev].[dbo].[OECInterceptOperateHistorys_tmp] oech
left join
[PLATFORM_ABBOTT_Dev].[dbo].[PurPRApplicationDetails_tmp] t1
on SUBSTRING(oech.PRDetailId, 1, CHARINDEX('+', oech.PRDetailId) - 1) = t1.ProcInstId 
and SUBSTRING(oech.PRDetailId, CHARINDEX('+', oech.PRDetailId) + 1,LEN(oech.PRDetailId) - CHARINDEX('+', oech.PRDetailId)) = t1.RowNo
left join 
[PLATFORM_ABBOTT_Dev].[dbo].[OECIntercepts_tmp] t2
on oech.InterceptId = t2.PRdetailId
left join 
[PLATFORM_ABBOTT_Dev].[dbo].[spk_staffmasterdata] t3
on oech.creatorId = t3.bpm_id)
select * into #OECInterceptOperateHistorys_ns from OECInterceptOperateHistorys_ns 

 IF OBJECT_ID(N'PLATFORM_ABBOTT_Dev.dbo.OECInterceptOperateHistorys_ns ', N'U') IS NOT NULL
	BEGIN
		drop table PLATFORM_ABBOTT_Dev.dbo.OECInterceptOperateHistorys_ns
		select *
        into PLATFORM_ABBOTT_Dev.dbo.OECInterceptOperateHistorys_ns from #OECInterceptOperateHistorys_ns
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into PLATFORM_ABBOTT_Dev.dbo.OECInterceptOperateHistorys_ns from #OECInterceptOperateHistorys_ns
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END

END
