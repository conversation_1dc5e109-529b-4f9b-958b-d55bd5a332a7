CREATE PROCEDURE dbo.sp_PurPRApplicationDetailBackupVendors_ns
AS 
BEGIN

--	drop table #PurPRApplicationDetailBackupVendors
select 
a.[Id]
,a.[PRApplicationId]
,a.[PRApplicationDetailId]
,a.[VendorId]
,a.[VendorName]
,a.[PaymentTerm]
,a.[HcpLevelName]
,a.[HcpLevelCode]
,a.[Hospital]
,a.[Price]
,a.[Text]
,a.[ExtraProperties]
,a.[ConcurrencyStamp]
,a.[CreationTime]
,ss.spk_NexBPMCode  as [CreatorId]
,a.[LastModificationTime]
,a.[LastModifierId]
,a.[IsDeleted]
,a.[DeleterId]
,a.[DeletionTime]
,a.[ExceptionNumber]
into #PurPRApplicationDetailBackupVendors
from PurPRApplicationDetailBackupVendors_tmp a 
left join spk_staffmasterdata ss 
on a.CreatorId=ss.bpm_id 

--删除表
IF OBJECT_ID(N'PLATFORM_ABBOTT_STG.dbo.PurPRApplicationDetailBackupVendors ', N'U') IS NOT NULL
BEGIN
	drop table PLATFORM_ABBOTT_STG.dbo.PurPRApplicationDetailBackupVendors
	select *
    into PLATFORM_ABBOTT_STG.dbo.PurPRApplicationDetailBackupVendors from #PurPRApplicationDetailBackupVendors
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
--落成实体表
select *
    into PLATFORM_ABBOTT_STG.dbo.PurPRApplicationDetailBackupVendors from #PurPRApplicationDetailBackupVendors
-- select * from #vendor_tbl
  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END

END
