SELECT
 TRY_CONVERT(UNIQUEIDENTIFIER, [Id]) [Id]
,TRY_CONVERT(UNIQUEIDENTIFIER, [ApplicationId]) [ApplicationId]
,[Company]
,[Currency]
,[VendorCode]
,[AbbottBank]
,[VendorType]
,[Division]
,[PayType]
,[CountryCode]
,[BankType]
,[DpoCategory]
,[PaymentTerm]
,[BankNo]
,[ExtraProperties]
,'NULL' [ConcurrencyStamp]
,TRY_CONVERT(datetime2, [CreationTime], 120)[CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CreatorId]) [CreatorId]
,CASE WHEN [LastModificationTime] ='' THEN NULL ELSE [LastModificationTime] END AS [LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [LastModifierId]) [LastModifierId]
,[IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, [DeleterId]) [DeleterId]
,CASE WHEN [DeletionTime] = '' THEN NULL ELSE [DeletionTime] END AS [DeletionTime]
,[SpendingCategory]
,0 AS FinancialVendorStatus
INTO #VendorApplicationFinancials
FROM (SELECT DISTINCT *,ROW_NUMBER() OVER(PARTITION BY Id ORDER BY Id) RK FROM PLATFORM_ABBOTT_Dev.dbo.VendorApplicationFinancials)a
WHERE RK = 1;

UPDATE #VendorApplicationFinancials SET [CreationTime] = FORMAT(GETDATE(), 'yyyy-MM-dd HH:mm:ss') WHERE [CreationTime] IS NULL;

--drop table #VendorApplicationFinancials

USE Speaker_Portal_Dev;

UPDATE a 
SET 
 a.[ApplicationId] = b.[ApplicationId]
,a.[Company] = b.[Company]
,a.[Currency] = b.[Currency]
,a.[VendorCode] = b.[VendorCode]
,a.[AbbottBank] = b.[AbbottBank]
,a.[VendorType] = b.[VendorType]
,a.[Division] = b.[Division]
,a.[PayType] = b.[PayType]
,a.[CountryCode] = b.[CountryCode]
,a.[BankType] = b.[BankType]
,a.[DpoCategory] = b.[DpoCategory]
,a.[PaymentTerm] = b.[PaymentTerm]
,a.[BankNo] = b.[BankNo]
,a.[ExtraProperties] = b.[ExtraProperties]
,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = b.[CreatorId]
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = b.[LastModifierId]
,a.[IsDeleted] = b.[IsDeleted]
,a.[DeleterId] = b.[DeleterId]
,a.[DeletionTime] = b.[DeletionTime]
,a.[SpendingCategory] = b.[SpendingCategory]
,a.[FinancialVendorStatus] = b.[FinancialVendorStatus]
FROM dbo.VendorApplicationFinancials a
left join #VendorApplicationFinancials  b
ON a.id=b.id;

INSERT INTO dbo.VendorApplicationFinancials
SELECT
 [Id]
,[ApplicationId]
,[Company]
,[Currency]
,[VendorCode]
,[AbbottBank]
,[VendorType]
,[Division]
,[PayType]
,[CountryCode]
,[BankType]
,[DpoCategory]
,[PaymentTerm]
,[BankNo]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[SpendingCategory]
,[FinancialVendorStatus]
FROM #VendorApplicationFinancials a
WHERE not exists (select * from dbo.VendorApplicationFinancials where id=a.id);

--truncate table dbo.VendorApplicationFinancials