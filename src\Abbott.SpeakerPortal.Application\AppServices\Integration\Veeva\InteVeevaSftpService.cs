﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Abbott.SpeakerPortal.Contracts.Integration.Veeva;

using Microsoft.Extensions.Configuration;

using Renci.SshNet;

namespace Abbott.SpeakerPortal.AppServices.Integration.Veeva
{
    public class InteVeevaSftpService : SpeakerPortalAppService, IInteVeevaSftpService
    {
        public InteVeevaSftpService(IConfiguration configuration)
        {
            if (!int.TryParse(configuration["Integrations:Veeva:SftpPort"], out int port))
                port = 22;

            var sftpIP = configuration["Integrations:Veeva:SftpIP"];
            var sftpUser = configuration["Integrations:Veeva:SftpUser"];
            var sftpPwd = configuration["Integrations:Veeva:SftpPwd"];

            if (string.IsNullOrEmpty(sftpIP) || string.IsNullOrEmpty(sftpUser) || string.IsNullOrEmpty(sftpPwd))
            {
                throw new ArgumentException("SFTP configuration is missing or invalid.");
            }

            SftpClient = new SftpClient(sftpIP, port, sftpUser, sftpPwd);
        }

        public ISftpClient SftpClient { get; }
    }
}
