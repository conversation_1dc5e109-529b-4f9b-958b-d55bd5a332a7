﻿using Abbott.SpeakerPortal.Enums;

using System;
using System.Collections.Generic;
using System.Text;

namespace Abbott.SpeakerPortal.Contracts.Agent
{
    public class GetAgentOperatorsRequestDto
    {
        /// <summary>
        /// 代理操作人Id
        /// 可空，空即默认当前登录用户即代理操作人
        /// </summary>
        public Guid? AgentOperatorId { get; set; }
        /// <summary>
        /// 流程类型
        /// 可空，空即所有流程
        /// 每种业务单据可能包含一种或多种流程
        /// </summary>
        //public ICollection<WorkflowTypeName> WorkflowTypes { get; set; }
        //public ICollection<Guid> BusinessTypeIds { get; set; }
        public ResignationTransfer.TaskFormCategory? BusinessTypeCategory { get; set; }
    }
}
