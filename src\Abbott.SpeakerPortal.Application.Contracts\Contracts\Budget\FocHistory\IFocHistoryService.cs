﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.Contracts.Budget.FocHistory
{
    public interface IFocHistoryService
    {
        /// <summary>
        /// 根据Id获取FOC历史记录数据
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        Task<List<FocHistoryRecordsResponseDto>> GetFocHistoryRecordsByIdAsync(Guid Id);
        /// <summary>
        /// 导出FOC预算操作历史记录
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        Task<Stream> ExportFocHistoryExcelAsync(Guid Id);
    }
}
