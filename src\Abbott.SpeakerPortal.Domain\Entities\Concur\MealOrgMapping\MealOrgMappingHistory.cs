﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Volo.Abp.Domain.Entities.Auditing;

namespace Abbott.SpeakerPortal.Entities.Concur.MealOrgMapping
{
    /// <summary>
    /// 医院映射变更历史
    /// </summary>
    public class MealOrgMappingHistory : FullAuditedEntity<Guid>
    {
        /// <summary>
        /// 医院映射表Id
        /// </summary>
        public Guid MealOrgMappingId { get; set; }

        /// <summary>
        /// 医院映射
        /// </summary>
        public MealOrgMapping MealOrgMapping { get; set; }

        /// <summary>
        /// 原名
        /// </summary>
        [MaxLength(200)]
        public string Content { get; set; }
    }
}
