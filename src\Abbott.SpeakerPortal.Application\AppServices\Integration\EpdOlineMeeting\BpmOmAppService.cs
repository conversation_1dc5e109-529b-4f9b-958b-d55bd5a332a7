﻿using Abbott.SpeakerPortal.BackgroundWorkers.BPMEmail;
using Abbott.SpeakerPortal.BackgroundWorkers.Integration.BpmOm;
using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Common.Common;
using Abbott.SpeakerPortal.Contracts.Integration.EpdOlineMeeting;
using Abbott.SpeakerPortal.Contracts.Integration.EpdOlineMeeting.BPM;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.Integration.Dspot;
using Abbott.SpeakerPortal.Entities.Purchase.PurGRApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Entities.SystemConfig.Slide;
using Abbott.SpeakerPortal.Entities.VendorFinancials;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Enums.Integration;
using Abbott.SpeakerPortal.OEC.SpeakerLevel;
using Abbott.SpeakerPortal.Vendor.Speaker;

using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Spreadsheet;

using Flurl;
using Flurl.Http;

using Hangfire;

using IdentityModel;

using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

using Senparc.Weixin.WxOpen.Entities;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Text.Encodings.Web;
using System.Text.Json;
using System.Threading.Tasks;

using Volo.Abp.BackgroundJobs;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;

using static Abbott.SpeakerPortal.Enums.Purchase;
using static Volo.Abp.UI.Navigation.DefaultMenuNames.Application;

namespace Abbott.SpeakerPortal.AppServices.Integration.EpdOlineMeeting
{
    /// <summary>
    /// BPM推送OM会议（系统并行期间使用、后续直接对接新接口）
    /// </summary>
    public class BpmOmAppService : SpeakerPortalAppService, IBpmOmAppService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<BpmOmAppService> _logger;
        private readonly IConfiguration _configuration;
        private readonly ICommonService _commonService;
        public BpmOmAppService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _logger = serviceProvider.GetService<ILogger<BpmOmAppService>>();
            _configuration = serviceProvider.GetService<IConfiguration>();
            _commonService = _serviceProvider.GetService<ICommonService>();
        }

        /// <summary>
        /// 会议推送（PR审批通过）
        /// </summary>
        /// <param name="prNo"></param>
        /// <returns></returns>
        public async Task<MessageResult> OmAddMeetingAsync(string prNo)
        {
            var log = new SetOperationLogRequestDto();
            try
            {
                _logger.LogWarning("Start to execute OmAddMeeting, pr number:" + prNo + "!");
                if (string.IsNullOrWhiteSpace(prNo))
                {
                    _logger.LogWarning("End to execute OmAddMeeting, empty pr number!");
                    return MessageResult.FailureResult("Empty pr number!");
                }
                var prRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>();
                var pr = await prRepository.SingleOrDefaultAsync(x => x.ApplicationCode == prNo && x.IsEsignUsed == true);
                if (pr == null)
                {
                    _logger.LogWarning("End to execute OmAddMeeting, no found pr, pr number:" + prNo + "!");
                    return MessageResult.FailureResult("No found pr, pr number:" + prNo + "!");
                }
                // 会议已被OM激活
                // 会议状态是空或者1000才推送 clx 2024/09/13
                if (!(string.IsNullOrEmpty(pr.MeetingStatus) || pr.MeetingStatus == "1000"))
                {
                    _logger.LogWarning("End to execute OmAddMeeting, failed to push meeting because that the meeting has already been activated!");
                    return MessageResult.FailureResult("Failed to push meeting because that the meeting has already been activated!");
                }
                // 只推送有效明细行, 支付方式AR，供应商为讲者且有EPD主键，费用性质为需要推送
                var prDetailRepsitory = LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>();
                var prDetails = await prDetailRepsitory.GetListAsync(x => x.PRApplicationId == pr.Id && x.PayMethod == Enums.Purchase.PayMethods.AR && x.VendorId.HasValue);
                prDetails = await FiltrateCostNatureAsync(prDetails);
                //过滤反冲行和被反冲行
                prDetails = await FilterHedgeAsync(prDetails);
                if (prDetails?.Any() != true)
                {
                    _logger.LogWarning("End to execute OmAddMeeting, no valid prDetails need push to om, pr number:" + prNo + "!");
                    return MessageResult.FailureResult("No valid prDetails need push to om, pr number:" + prNo + "!");
                }
                //activeType【参会赞助】这个消费大类的情况下，就是1，其他为0
                var speakers = await BuindSpeakersAsync(prDetails, pr.ExpenseTypeCode == "MEETINGSPONSORSHIP" ? "1" : "0");
                if (speakers == null || speakers.Count == 0)
                {
                    _logger.LogWarning("End to execute OmAddMeeting, no valid row data need push to om, pr number:" + prNo + "!");
                    return MessageResult.FailureResult("No valid row data need push to om, pr number:" + prNo + "!");
                }
                List<BpmOmAddRequestDto> bpmOmAddRequestDtos = new List<BpmOmAddRequestDto>();
                var bpmOmRequest = await BuildBpmOmAddRequestAsync(pr);
                bpmOmRequest.CampaignSpeakers = speakers;
                bpmOmAddRequestDtos.Add(bpmOmRequest);

                var authorizationHeader = AuthorizationHeader();
                if (authorizationHeader.Count < 1)
                    return MessageResult.FailureResult("获取Token失败");
                // 和EPD接口通信
                var url = _configuration["Integrations:BPMOM:BaseUrl"]!.TrimEnd('/') + "/api/CampaignContract/AddCampaignContract";
                string postString = JsonSerializer.Serialize(bpmOmAddRequestDtos);
                log = _commonService.InitOperationLog("OmAddMeetingAsync", "BPMOm会议推送", url + "|" + postString);
                // 发送POST请求
                var response = await url.WithHeaders(authorizationHeader).PostStringAsync(postString);
                string responseData = await response.GetStringAsync();
                _commonService.LogResponse(log, responseData);
                var resObj = JsonSerializer.Deserialize<BpmOmResponseDto>(responseData);
                if (resObj.Status == 1)
                {
                    var meetingStatus = "1000";
                    // 推送成功
                    pr.MeetingStatus = meetingStatus;
                    List<int> prNoList = speakers.Select(a => int.Parse(a.No)).ToList();
                    //修改PrDetail.MeetingStatus
                    await UpdatePrDetailMeetingStatus(pr.Id, meetingStatus, prNoList);
                    await prRepository.UpdateAsync(pr);
                    return MessageResult.SuccessResult(resObj.Message);
                }
                else
                {
                    // 推送失败
                    //pr.MeetingStatus = "-1000";
                    return MessageResult.FailureResult(resObj.Message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"BPMOmAddMeetingAsync:{ex.Message}");
                _commonService.LogResponse(log, ex.ToString(), false);
                return MessageResult.FailureResult(ex.Message);
            }
        }

        /// <summary>
        /// 会议作废（PR作废）
        /// </summary>
        /// <param name="prNo"></param>
        /// <returns></returns>
        public async Task<MessageResult> OmRevokeMeetingAsync(string prNo)
        {
            var log = new SetOperationLogRequestDto();
            log = _commonService.InitOperationLog("Epd BPM Online Meeting", "作废会议", null);
            try
            {
                if (string.IsNullOrWhiteSpace(prNo))
                    return MessageResult.FailureResult("Empty pr number!");

                var prRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>();
                var pr = await prRepository.SingleOrDefaultAsync(x => x.ApplicationCode == prNo && x.IsEsignUsed == true);
                if (pr == null)
                    return MessageResult.FailureResult("No found pr, pr number:" + prNo + "!");
                var meetingRevokeRequest = new OmMeetingRevokeRequestDto { SerialNumberPr = prNo };
                var url = _configuration["Integrations:BPMOM:BaseUrl"]!.TrimEnd('/') + "/api/CampaignContract/UpdateCampaignInActive";

                var authorizationHeader = AuthorizationHeader();
                if (authorizationHeader.Count < 1)
                    return MessageResult.FailureResult("获取Token失败");
                var postString = JsonSerializer.Serialize(meetingRevokeRequest);
                log = _commonService.InitOperationLog("OmRevokeMeetingAsync", "BPMOm会议推送", url + "|" + postString);
                var response = await url.AppendPathSegment(pr.ApplicationCode).WithHeaders(authorizationHeader).PostAsync();
                string responseData = await response.GetStringAsync();
                _commonService.LogResponse(log, responseData);
                var resObj = JsonSerializer.Deserialize<BpmOmResponseDto>(responseData);
                if (resObj.Status == 1)
                {
                    var meetingStatus = "1003";
                    // nextBpm作废会议成功
                    pr.MeetingStatus = meetingStatus;

                    //修改PrDetail.MeetingStatus
                    await UpdatePrDetailMeetingStatus(pr.Id, meetingStatus, null, true);
                    await prRepository.UpdateAsync(pr);
                    return MessageResult.SuccessResult(resObj.Data);
                }
                else
                {
                    // nextBpm作废会议失败
                    //pr.MeetingStatus = "-1003";
                    return MessageResult.FailureResult(resObj.Message);
                }
            }
            catch (Exception ex)
            {
                _commonService.LogResponse(log, ex.ToString(), false);
                // TODO 记录并重试
                return MessageResult.FailureResult("Failed to revoke meeting to OM, pr number:" + prNo + ", error message:" + ex.Message + "!");
            }
        }

        /// <summary>
        /// 调用OM api，打印页面推送
        /// </summary>
        /// <param name="paNo"></param>
        /// <returns></returns>
        public async Task<MessageResult> OmAddPrintPAPR(string paNo)
        {
            string postString = "";
            _logger.LogWarning("Start to execute OmAddPrintPAPR, pa number:" + paNo + "!");
            if (string.IsNullOrWhiteSpace(paNo))
            {
                _logger.LogWarning("End to execute OmAddPrintPAPR, empty pa number!");
                return MessageResult.FailureResult("Empty pa number!");
            }
            var log = new SetOperationLogRequestDto();
            try
            {
                var paRepository = LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>();
                var pa = await paRepository.SingleOrDefaultAsync(x => x.ApplicationCode == paNo);
                if (pa == null)
                {
                    _logger.LogWarning("End to execute OmAddPrintPAPR, no found pa, pa number:" + paNo + "!");
                    return MessageResult.FailureResult("No found pa, pa number:" + paNo + "!");
                }

                var grDetailRepository = LazyServiceProvider.LazyGetService<IPurGRApplicationDetailRepository>();
                var gr = await grDetailRepository.FirstOrDefaultAsync(x => x.GRApplicationId == pa.GRId);
                if (gr == null)
                {
                    _logger.LogWarning("End to execute OmAddPrintPAPR, no found gr detail, pa number:" + paNo + "!");
                    return MessageResult.FailureResult("No found gr detail, pa number:" + paNo + "!");
                }

                var prDetailRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>();
                var prDetail = await prDetailRepository.SingleOrDefaultAsync(x => x.Id == gr.PRDetailId);
                if (prDetail == null)
                {
                    _logger.LogWarning("End to execute OmAddPrintPAPR, no found pr detail, pa number:" + paNo + "!");
                    return MessageResult.FailureResult("No found pr detail, pa number:" + paNo + "!");
                }

                var prRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>();
                var pr = await prRepository.SingleAsync(x => x.Id == prDetail.PRApplicationId);
                if (pr.MeetingStatus != "1002")
                {
                    _logger.LogWarning("End to execute OmAddPrintPAPR, pr meeting status is not 1002-结算, pa number:" + paNo + "!");
                    return MessageResult.FailureResult("pr meeting status is not 1002-结算, pa number:" + paNo + "!");
                }

                var omAuthService = LazyServiceProvider.LazyGetService<IOmAuthorizationService>();
                var prParameter = await omAuthService.BuildSignParametersAsync(pr.Id);
                var paParameter = await omAuthService.BuildSignParametersAsync(pa.Id);

                BpmOmMeetingPrintPAPRRequestDto meetingPrintPAPRRequest = new BpmOmMeetingPrintPAPRRequestDto();
                meetingPrintPAPRRequest.SerialNumberPR = pr.ApplicationCode;
                meetingPrintPAPRRequest.SerialPRNO = prDetail.RowNo;
                //meetingPrintPAPRRequest.PrintUrlPR = _configuration["Integrations:OM:PrintUrl"]!.TrimEnd('/') + "/print/procureRequestPrint/" + pr.Id + parameter;
                meetingPrintPAPRRequest.PrintUrlPR = $"{_configuration["App:SelfUrl"]}/api/EpdOmPrintPage/GetPRPrintPage{prParameter.Parameter}";
                meetingPrintPAPRRequest.SerialNumberPA = pa.ApplicationCode;
                //meetingPrintPAPRRequest.PrintUrlPA = _configuration["Integrations:OM:PrintUrl"]!.TrimEnd('/') + "/print/paymentRequestPrint/" + pa.Id + parameter;
                meetingPrintPAPRRequest.PrintUrlPA = $"{_configuration["App:SelfUrl"]}/api/EpdOmPrintPage/GetPAPrintPage{paParameter.Parameter}";

                var url = _configuration["Integrations:BPMOM:BaseUrl"]!.TrimEnd('/') + "/api/CampaignContract/AddCampaignPrintPRPA";
                var authorizationHeader = AuthorizationHeader();
                if (authorizationHeader.Count < 1)
                    return MessageResult.FailureResult("获取Token失败");
                postString = JsonSerializer.Serialize(new[] { meetingPrintPAPRRequest }, new JsonSerializerOptions
                {
                    Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
                    WriteIndented = true
                });
                _logger.LogWarning("Call om api to add PAPR start, pa number:" + paNo + ", request:" + postString + "!");
                log = _commonService.InitOperationLog("Epd Online Meeting", "打印页面推送", url + "|" + postString);
                var response = await url.WithHeaders(authorizationHeader).PostStringAsync(postString);
                string responseData = await response.GetStringAsync();
                _logger.LogWarning("End to execute OmAddPrintPAPR, pa number: " + paNo + ", response:" + responseData);
                _commonService.LogResponse(log, responseData);
                var resObj = JsonSerializer.Deserialize<OmResponseDto>(responseData);
                if (resObj.success)
                {
                    var meetingStatus = "1006";
                    // 打印页面推送成功
                    //pr.MeetingStatus = meetingStatus;

                    //zhx20240704:修改PrDetail.MeetingStatus
                    UpdatePrDetailMeetingStatusById(prDetail.Id, meetingStatus, true);
                }
                else
                {
                    // 打印页面推送失败
                    //pr.MeetingStatus = "-1006";
                    return MessageResult.FailureResult(resObj.message);
                }
                return resObj.success ? MessageResult.SuccessResult(resObj.data) : MessageResult.FailureResult(resObj.message);
            }
            catch (Exception ex)
            {
                _commonService.LogResponse(log, ex.ToString(), false);
                _logger.LogError(ex, "End to execute OmAddPrintPAPR, failed to add PAPR, pa number:" + paNo + ", request: " + postString + ", error message: " + ex.Message + "!");
                // TODO 记录并重试
                return MessageResult.FailureResult(ex.Message);
            }
        }

        /// <summary>
        /// 调用OM api，会议复审状态
        /// </summary>
        /// <param name="paNo"></param>
        /// <param name="approvalPerson"></param>
        /// <param name="approvalTime"></param>
        /// <returns></returns>
        public async Task<MessageResult> OmReviewMeeting(string paNo, string approvalPerson, string approvalTime)
        {
            string postString = "";
            _logger.LogWarning("Start to execute OmReviewMeeting, pa number:" + paNo + "!");
            StringBuilder sb = new StringBuilder();
            if (string.IsNullOrWhiteSpace(paNo))
            {
                sb.AppendLine("Empty pa number!");
            }

            if (string.IsNullOrWhiteSpace(approvalPerson))
            {
                sb.AppendLine("Empty approval person!");
            }

            if (string.IsNullOrWhiteSpace(approvalTime))
            {
                sb.AppendLine("Empty approval time!");
            }
            else if (!DateTime.TryParse(approvalTime, out var date))
            {
                sb.AppendLine("Invalid approval time:" + date + "!");
            }

            if (sb.Length > 0)
            {
                _logger.LogWarning("End to execute OmReviewMeeting, validate message: " + sb.ToString());
                return MessageResult.FailureResult(sb.ToString());
            }
            var log = new SetOperationLogRequestDto();

            try
            {
                var paRepository = LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>();
                var pa = await paRepository.SingleOrDefaultAsync(x => x.ApplicationCode == paNo);
                if (pa == null)
                {
                    _logger.LogWarning("End to execute OmReviewMeeting, no found pa, pa number:" + paNo + "!");
                    return MessageResult.FailureResult("No found pa, pa number:" + paNo + "!");
                }

                var grDetailRepository = LazyServiceProvider.LazyGetService<IPurGRApplicationDetailRepository>();
                var gr = await grDetailRepository.FirstOrDefaultAsync(x => x.GRApplicationId == pa.GRId);
                if (gr == null)
                {
                    _logger.LogWarning("End to execute OmReviewMeeting, no found gr detail, pa number:" + paNo + "!");
                    return MessageResult.FailureResult("No found gr detail, pa number:" + paNo + "!");
                }

                var prDetailRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>();
                var prDetail = await prDetailRepository.SingleOrDefaultAsync(x => x.Id == gr.PRDetailId);
                if (prDetail == null)
                {
                    _logger.LogWarning("End to execute OmReviewMeeting, no found pr detail, pa number:" + paNo + "!");
                    return MessageResult.FailureResult("No found pr detail, pa number:" + paNo + "!");
                }


                BpmOmMeetingReviewRequestDto meetingReviewRequest = new BpmOmMeetingReviewRequestDto();
                meetingReviewRequest.PaNo = paNo;
                meetingReviewRequest.Status = "复审完成";
                meetingReviewRequest.ApprovalPerson = approvalPerson;
                meetingReviewRequest.ApprovalTime = approvalTime;

                var url = _configuration["Integrations:BPMOM:BaseUrl"]!.TrimEnd('/') + "/api/CampaignContract/CampaignReview";
                var authorizationHeader = AuthorizationHeader();
                if (authorizationHeader.Count < 1)
                    return MessageResult.FailureResult("获取Token失败");
                postString = JsonSerializer.Serialize(new[] { meetingReviewRequest });
                _logger.LogWarning("Call om api to review meeting start, pa number:" + paNo + ", request:" + postString + "!");
                log = _commonService.InitOperationLog("Epd Online Meeting", "会议复审状态", url + "|" + postString);

                var response = await url.WithHeaders(authorizationHeader).PostStringAsync(postString);
                string responseData = await response.GetStringAsync();
                _logger.LogWarning("End to execute OmReviewMeeting, pa number: " + paNo + ", response:" + responseData);
                _commonService.LogResponse(log, responseData);
                var resObj = JsonSerializer.Deserialize<OmResponseDto>(responseData);
                if (resObj.success)
                {
                    var meetingStatus = "1007";
                    // 复审状态推送成功
                    //pr.MeetingStatus = meetingStatus;

                    //zhx20240704:修改PrDetail.MeetingStatus
                    UpdatePrDetailMeetingStatusById(prDetail.Id, meetingStatus, true);
                }
                else
                {
                    // 复审状态推送失败
                    //pr.MeetingStatus = "-1007";
                    return MessageResult.FailureResult(resObj.message);
                }
                return resObj.success ? MessageResult.SuccessResult(resObj.data) : MessageResult.FailureResult(resObj.message);
            }
            catch (Exception ex)
            {
                _commonService.LogResponse(log, ex.ToString(), false);
                _logger.LogError(ex, "End to execute OmReviewMeeting, failed to review meeting, pa number:" + paNo + ", request: " + postString + ", error message: " + ex.Message + "!");
                // TODO 记录并重试
                return MessageResult.FailureResult(ex.Message);
            }
        }

        /// <summary>
        /// 调用OM api，付款作废或线下递交
        /// </summary>
        /// <param name="prNo"></param>
        /// <param name="paNo"></param>
        /// <param name="status"></param>
        /// <param name="remark"></param>
        /// <returns></returns>
        public async Task<MessageResult> OmUpdateMeetingSpeakerStatus(string paNo, string status, string remark)
        {
            string postString = "";
            var log = new SetOperationLogRequestDto();

            try
            {
                _logger.LogWarning("Start to execute OmUpdateMeetingSpeakerStatus, pa number:" + paNo + "!");
                StringBuilder sb = new StringBuilder();
                if (string.IsNullOrWhiteSpace(paNo))
                {
                    sb.AppendLine("Empty pa number!");
                }

                if (string.IsNullOrWhiteSpace(status))
                {
                    sb.AppendLine("Empty status!");
                }
                else if (status != "线下递交" && status != "作废")
                {
                    sb.AppendLine("Invalid status, only support:线下递交|作废!");
                }

                if (sb.Length > 0)
                {
                    _logger.LogWarning("End to execute OmUpdateMeetingSpeakerStatus, pa number:" + paNo + ", error message: " + sb.ToString() + "!");
                    return MessageResult.FailureResult(sb.ToString());
                }

                var paRepository = LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>();
                var pa = await paRepository.SingleOrDefaultAsync(x => x.ApplicationCode == paNo);
                if (pa == null)
                {
                    _logger.LogWarning("End to execute OmUpdateMeetingSpeakerStatus, no found pa, pa number:" + paNo + "!");
                    return MessageResult.FailureResult("No found pa, pa number:" + paNo + "!");
                }

                var prRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>();
                var pr = await prRepository.SingleOrDefaultAsync(x => x.Id == pa.PRId);
                if (pr == null)
                {
                    _logger.LogWarning("End to execute OmUpdateMeetingSpeakerStatus, no found pr, pa number:" + paNo + "!");
                    return MessageResult.FailureResult("No found pr, pa number:" + paNo + "!");
                }

                if (pr.MeetingStatus != "1002")
                {
                    _logger.LogWarning("End to execute OmUpdateMeetingSpeakerStatus, pr meeting status is not 1002-结算, pa number:" + paNo + "!");
                    return MessageResult.FailureResult("Pr meeting status is not 1002-结算, pa number:" + paNo + "!");
                }

                //zhx20240708:链接查询，同时查出gr+grDetail，以供后续使用
                var queryGr = LazyServiceProvider.LazyGetService<IPurGRApplicationRepository>().GetQueryableAsync().GetAwaiter().GetResult();
                var queryGrDetail = LazyServiceProvider.LazyGetService<IPurGRApplicationDetailRepository>().GetQueryableAsync().GetAwaiter().GetResult();
                var grGrDetail = queryGr.Where(a => a.Id == pa.GRId)
                    .Join(queryGrDetail, l => l.Id, r => r.GRApplicationId, (l, r) => new { gr = l, grDetail = r })
                    .FirstOrDefault();

                if (grGrDetail == null)
                {
                    _logger.LogWarning("End to execute OmUpdateMeetingSpeakerStatus, no found gr or grDetail, pa number:" + paNo + "!");
                    return MessageResult.FailureResult("No found gr or grDetail, pa number:" + paNo + "!");
                }

                var padQuer = LazyServiceProvider.LazyGetService<IPurPAApplicationDetailRepository>().GetQueryableAsync().GetAwaiter().GetResult();
                var prdQuery = LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync().GetAwaiter().GetResult();
                var prd = padQuer.Select(a => new { a.PurPAApplicationId, a.PRDetailId }).Where(a => a.PurPAApplicationId == pa.Id)
                    .Join(prdQuery.Select(a => new { a.Id, a.RowNo }), a => a.PRDetailId, b => b.Id, (a, b) => new { b.RowNo })
                    .FirstOrDefault();//讲者默认只有一个PA明细

                BpmOmMeetingOfflineSubmissionRequestDto meetingOfflineSubmissionRequest = new BpmOmMeetingOfflineSubmissionRequestDto();
                meetingOfflineSubmissionRequest.PrNo = grGrDetail.gr.PrApplicationCode;
                meetingOfflineSubmissionRequest.PaNo = paNo;
                meetingOfflineSubmissionRequest.Status = status;
                meetingOfflineSubmissionRequest.No = prd.RowNo;

                var url = _configuration["Integrations:BPMOM:BaseUrl"]!.TrimEnd('/') + "/api/CampaignContract/UpdateSpeakerPaymentOrder";
                var authorizationHeader = AuthorizationHeader();
                if (authorizationHeader.Count < 1)
                    return MessageResult.FailureResult("获取Token失败");
                postString = JsonSerializer.Serialize(new[] { meetingOfflineSubmissionRequest });
                _logger.LogWarning("Call om api to update meeting speaker status start, pa number:" + paNo + ", request:" + postString + "!");
                log = _commonService.InitOperationLog("Epd Online Meeting", "付款作废或线下", url + "|" + postString);

                var response = await url.WithHeaders(authorizationHeader).PostStringAsync(postString);

                string responseData = await response.GetStringAsync();
                _logger.LogWarning("End to execute OmUpdateMeetingSpeakerStatus, pa number: " + paNo + ", response:" + responseData);
                _commonService.LogResponse(log, responseData);

                var resObj = JsonSerializer.Deserialize<OmResponseDto>(responseData);
                if (resObj.success)
                {
                    var meetingStatus = "1005";
                    // 付款线下递交或者作废成功
                    //pr.MeetingStatus = meetingStatus;

                    //zhx20240704:修改PrDetail.MeetingStatus
                    UpdatePrDetailMeetingStatusById(grGrDetail.grDetail.PRDetailId, meetingStatus, true);
                }
                else
                {
                    // 付款线下递交或者作废失败
                    //pr.MeetingStatus = "-1005";
                    return MessageResult.FailureResult(resObj.message);
                }
                return resObj.success ? MessageResult.SuccessResult(resObj.data) : MessageResult.FailureResult(resObj.message);
            }
            catch (Exception ex)
            {
                _commonService.LogResponse(log, ex.ToString(), false);
                _logger.LogError(ex, "End to execute OmUpdateMeetingSpeakerStatus, failed to update meeting speaker status, pa number:" + paNo + ", request: " + postString + ", error message: " + ex.Message + "!");
                // TODO 记录并重试
                return MessageResult.FailureResult("Failed to update meeting speaker status, pa number:" + paNo + ", error message: " + ex.Message + "!");
            }
        }

        /// <summary>
        /// 测试
        /// </summary>
        /// <param name="bpmRequest"></param>
        /// <returns></returns>
        public async Task<MessageResult> TestBpmOmPushAsync(BpmRequestDto bpmRequest)
        {
            // 调度作业
            BackgroundJob.Enqueue<BpmOmWorker>(a => a.DoWorkAsync(bpmRequest));
            return MessageResult.SuccessResult();
        }

        #region 会议推送（PR审批通过） 私有方法
        /// <summary>
        /// 适用于1005-1007, 直接根据 PrDetail.Id 修改 PrDetail.MeetingStatus
        /// </summary>
        private void UpdatePrDetailMeetingStatusById(Guid prDetailId, string meetingStatus, bool onlyMeetingStatusHasValue = false)
        {
            var repoPrDetail = LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>();
            var queryPrDetail = repoPrDetail.GetQueryableAsync().GetAwaiter().GetResult();
            var updateEntity = queryPrDetail.Where(a => a.Id == prDetailId)
                .WhereIf(onlyMeetingStatusHasValue, a => !string.IsNullOrWhiteSpace(a.MeetingStatus)).FirstOrDefault();
            if (updateEntity != null)
            {
                updateEntity.MeetingStatus = meetingStatus;
                repoPrDetail.UpdateAsync(updateEntity).GetAwaiter().GetResult();
            }
        }

        /// <summary>
        /// PR 相关主数据
        /// </summary>
        /// <param name="pr"></param>
        /// <returns></returns>
        private async Task<BpmOmAddRequestDto> BuildBpmOmAddRequestAsync(PurPRApplication pr)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var userRepository = LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>();

            BpmOmAddRequestDto meetingAddRequest = new BpmOmAddRequestDto();
            meetingAddRequest.ApplicantEmpName = pr.ApplyUserIdName;
            meetingAddRequest.SerialNumber = pr.ApplicationCode;
            meetingAddRequest.Host = pr.HostVendorIdName;
            meetingAddRequest.HostEmail = pr.HostVendorEmail;
            meetingAddRequest.ActiveCity = pr.ActiveHostCity;
            meetingAddRequest.ActivePlace = pr.AcitveHostAddress;

            if (pr.MeetingType?.ToLower() == "Online".ToLower())
            {
                meetingAddRequest.ConferenceType = "线上";
            }
            else if (pr.MeetingType?.ToLower() == "Offline".ToLower())
            {
                meetingAddRequest.ConferenceType = "线下";
            }
            else
            {
                meetingAddRequest.ConferenceType = "线上加线下";
            }
            meetingAddRequest.Principal = pr.AgentIdName;
            meetingAddRequest.CampaignName = pr.MeetingTitle;
            meetingAddRequest.ProductName = pr.ProductIdsName;
            meetingAddRequest.Field1 = pr.ApplyTime?.ToString("yyyy-MM-dd");
            meetingAddRequest.Field2 = pr.CompanyIdName;
            // 申请人
            var applyUser = await userRepository.SingleOrDefaultAsync(x => x.Id == pr.ApplyUserId);
            meetingAddRequest.Field3 = applyUser?.Email;
            // 被代理人
            if (pr.AgentId != null)
            {
                var agentUser = await userRepository.SingleOrDefaultAsync(x => x.Id == pr.AgentId);
                meetingAddRequest.Field4 = agentUser?.Email;
            }
            return meetingAddRequest;
        }

        /// <summary>
        /// 筛选费用性质
        /// </summary>
        /// <param name="prDetails"></param>
        /// <returns></returns>
        private async Task<List<PurPRApplicationDetail>> FiltrateCostNatureAsync(List<PurPRApplicationDetail> prDetails)
        {
            if (prDetails?.Any() != true)
            {
                return prDetails;
            }
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var costNatureList = await dataverseService.GetCostNatureAsync();
            var costNaturePushIds = costNatureList.Where(a => a.PushOnlineMeeting == true).Select(a => a.Id);
            return prDetails.Where(a => a.CostNature.HasValue && costNaturePushIds.Contains(a.CostNature.Value)).ToList();
        }

        /// <summary>
        /// 过滤反冲行和被反冲行
        /// </summary>
        /// <param name="prDetails"></param>
        /// <returns></returns>
        private async Task<List<PurPRApplicationDetail>> FilterHedgeAsync(List<PurPRApplicationDetail> prDetails)
        {
            //过滤被反冲行
            var prDetailIDs = prDetails.Select(a => a.Id).ToList();
            var prDetailRepsitory = LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>();
            var hedgeedList = await prDetailRepsitory.GetListAsync(x => prDetailIDs.Contains(x.HedgePrDetailId.Value));
            if (hedgeedList.Count > 0)
            {
                var hedgeedIDs = hedgeedList.Select(a => a.HedgePrDetailId).ToList();
                prDetails = prDetails.Where(a => !hedgeedIDs.Contains(a.Id)).ToList();
            }

            //过滤反冲行
            prDetails = prDetails.Where(a => a.HedgePrDetailId == null).ToList();
            return prDetails;
        }

        /// <summary>
        /// 讲者信息
        /// </summary>
        /// <param name="prDetails"></param>
        /// <returns></returns>
        private async Task<List<CampaignSpeakerDto>> BuindSpeakersAsync(List<PurPRApplicationDetail> prDetails, string activeType)
        {
            if (prDetails == null)
            {
                return null;
            }
            var campaignSpeaker = new List<CampaignSpeakerDto>();
            var dataverseService = LazyServiceProvider.GetService<IDataverseService>();
            foreach (var prDetail in prDetails)
            {
                // 只推送付款方式为AR
                if (prDetail.PayMethod != PayMethods.AR || !prDetail.VendorId.HasValue)
                {
                    continue;
                }
                CampaignSpeakerDto mainSpeaker = new CampaignSpeakerDto();
                var speaker = await GetSpeakerAsync(prDetail.VendorId.Value);
                //第一次推OM新建会议时，还要判断主讲者的供应商类型为讲者(Vendor.VendorType=="HCP-个人")
                if (speaker == null || speaker.VendorType != VendorTypes.HCPPerson)
                {
                    continue;
                }
                mainSpeaker.Content = prDetail.Content;
                mainSpeaker.ExpectedDate = prDetail.EstimateDate?.ToString("yyyy-MM-dd");
                mainSpeaker.Executive = prDetail.ExecutorName;
                mainSpeaker.ExecutiveCode = prDetail.ExecutorEmail;
                mainSpeaker.ServiceTime = prDetail.ServiceDuration?.ToString();
                mainSpeaker.IsMain = "1";
                mainSpeaker.No = prDetail.RowNo.ToString();
                mainSpeaker.SlideType = prDetail.SlideType;
                mainSpeaker.SlideName = prDetail.SlideType == "Category-A" ? prDetail.SlideAName : prDetail.SlideName;
                mainSpeaker.VendorName = prDetail.VendorName;
                mainSpeaker.VendorCode = prDetail.VendorCode;
                mainSpeaker.CertificateNo = speaker.CertificateCode;
                mainSpeaker.Hospital = speaker.HospitalName;
                mainSpeaker.Division = speaker.HosDepartment;
                mainSpeaker.IdNumber = speaker.CardNo;
                mainSpeaker.BankNumber = speaker.BankCardNo;
                mainSpeaker.JobTitle = speaker.PTIName;
                mainSpeaker.SpeakerLevel = prDetail.HcpLevelName;
                mainSpeaker.TelPhone = string.IsNullOrEmpty(speaker.HandPhone) ? "***********" : speaker.HandPhone;
                mainSpeaker.RmbAmount = prDetail.TotalAmountRMB.HasValue ? prDetail.TotalAmountRMB.Value : 0M;
                mainSpeaker.SpeakerCampaignType = activeType;
                mainSpeaker.AriseCode = speaker.EpdId;

                //推送新建会议时，备选讲者也用Backup表的TotalAmountRMB推过去
                var prDetailBackups = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailBackupVendorRepository>()
                    .GetListAsync(a => a.PRApplicationDetailId == prDetail.Id);
                if (prDetailBackups?.Any() != true)
                {
                    campaignSpeaker.Add(mainSpeaker);
                    continue;
                }
                //从BpcsAvm+BpcsPmfvm表取Vendor，以便后续取相关值
                var queryBpcsAvm = await LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync();
                var queryBpcsPmfvm = await LazyServiceProvider.LazyGetService<IBpcsPmfvmRepository>().GetQueryableAsync();
                var dicBpcsAvmPmfvm = queryBpcsAvm.Where(a => prDetailBackups.Select(b => b.VendorId).Contains(a.Id))
                    .Join(queryBpcsPmfvm, l => new { Vnd = l.Vendor, Cmp = l.Vcmpny }, r => new { Vnd = r.Vnderx, Cmp = r.Vmcmpy },
                        (pbcsAvm, pbcsPmfvm) => new { bpcsAvmId = pbcsAvm.Id, bpcsAvmVendor = pbcsAvm.Vendor, pbcsPmfvmVextnm = pbcsPmfvm.Vextnm })
                    .ToDictionary(a => a.bpcsAvmId, a => a);
                if (dicBpcsAvmPmfvm?.Any() != true)
                {
                    _logger.LogWarning($"BuildOmMeetingStandbySpeakers() return null, dicBpcsAvmPmfvm is null,prDetailId:{prDetail.Id}");
                    campaignSpeaker.Add(mainSpeaker);
                    continue;
                }
                var standbySpeakers = new List<SpeakerDto>();
                foreach (var backupVendor in prDetailBackups)
                {
                    var standbySpeaker = await GetSpeakerAsync(backupVendor.VendorId);
                    var curBpcsAvmPmfvm = dicBpcsAvmPmfvm.GetValueOrDefault(backupVendor.VendorId);
                    SpeakerDto backupSpeakerItem = new SpeakerDto();
                    backupSpeakerItem.IsMain = "0";
                    backupSpeakerItem.No = prDetail.RowNo.ToString(); ;
                    backupSpeakerItem.VendorName = curBpcsAvmPmfvm?.pbcsPmfvmVextnm;
                    backupSpeakerItem.VendorCode = curBpcsAvmPmfvm?.bpcsAvmVendor.ToString();
                    backupSpeakerItem.CertificateNo = standbySpeaker.CertificateCode;
                    backupSpeakerItem.Hospital = standbySpeaker.HospitalName;
                    backupSpeakerItem.Division = standbySpeaker.HosDepartment;
                    backupSpeakerItem.IdNumber = standbySpeaker.CardNo;
                    backupSpeakerItem.BankNumber = standbySpeaker.BankCardNo;
                    backupSpeakerItem.JobTitle = standbySpeaker.PTIName;
                    backupSpeakerItem.TelPhone = string.IsNullOrEmpty(standbySpeaker.HandPhone) ? "***********" : standbySpeaker.HandPhone;
                    backupSpeakerItem.RmbAmount = backupVendor.TotalAmountRMB.HasValue ? backupVendor.TotalAmountRMB.Value : 0M;
                    backupSpeakerItem.AriseCode = standbySpeaker.EpdId;
                    standbySpeakers.Add(backupSpeakerItem);
                }
                mainSpeaker.Speakers = standbySpeakers;
                campaignSpeaker.Add(mainSpeaker);
            }
            return campaignSpeaker;
        }

        private async Task<SpeakerDetailResponseDto> GetSpeakerAsync(Guid vendorId)
        {
            var bpcsAvmRepository = LazyServiceProvider.LazyGetService<IBpcsAvmRepository>();
            var bpcsAvm = await bpcsAvmRepository.FirstOrDefaultAsync(x => x.Id == vendorId);
            if (bpcsAvm == null)
            {
                return null;
            }
            var vendorFinancialRepository = LazyServiceProvider.LazyGetService<IVendorFinancialRepository>();
            var vendorFinancial = await vendorFinancialRepository.SingleOrDefaultAsync(x => x.Id == bpcsAvm.FinaId.Value);
            if (vendorFinancial == null)
            {
                return null;
            }

            var speakerService = LazyServiceProvider.LazyGetService<ISpeakerService>();
            var getSpeakerResponse = await speakerService.GetSpeakerDetailAsync(vendorFinancial.VendorId);

            if (getSpeakerResponse == null || !getSpeakerResponse.Success)
            {
                return null;
            }
            var speaker = getSpeakerResponse.Data as SpeakerDetailResponseDto;
            //zhx20240703:speaker里的银行卡号、身份证号、手机号、要从bpcsAvm+BpcsPMFVM表里查出来替换
            if (speaker != null)
            {
                var bpcsPmfvmRepository = LazyServiceProvider.LazyGetService<IBpcsPmfvmRepository>();
                var bpcsPmfvm = await bpcsPmfvmRepository.FirstOrDefaultAsync(x => x.Vnderx == bpcsAvm.Vendor && x.Vmcmpy == bpcsAvm.Vcmpny);

                //银行卡号
                speaker.BankCardNo = bpcsPmfvm.Vldrm2;
                //身份证号
                speaker.CardNo = bpcsAvm.Vmxcrt;
                //手机号(zhx20240705:手机号，BPCS里面会有很多都是空的，所以还是按vendors来)
                //speaker.HandPhone = bpcsAvm.Vphone;
            }
            return speaker;
        }

        private async Task UpdatePrDetailMeetingStatus(Guid prId, string meetingStatus, List<int> prNoList = null, bool onlyMeetingStatusHasValue = false)
        {
            var repoPrDetail = LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>();
            var queryPrDetail = await repoPrDetail.GetQueryableAsync();
            var updateEntities = queryPrDetail.Where(a => a.PRApplicationId == prId)
                .WhereIf(prNoList?.Any() == true, a => prNoList.Contains(a.RowNo))
                .WhereIf(onlyMeetingStatusHasValue, a => !string.IsNullOrWhiteSpace(a.MeetingStatus)).ToList();
            if (updateEntities.Count > 0)
            {
                updateEntities.ForEach(a =>
                {
                    a.MeetingStatus = meetingStatus;
                });
                await repoPrDetail.UpdateManyAsync(updateEntities);
            }
        }
        #endregion

        #region 签名和认证 
        private Dictionary<string, string> AuthorizationHeader()
        {
            var result = new Dictionary<string, string>();
            try
            {
                var appSecret = _configuration["Integrations:BPMOM:AppSecret"];
                string random = Guid.NewGuid().ToString("N").Substring(0, 16); // 生成16位随机字符串
                long timeStamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                // 3. 计算签名
                string signature = ComputeSha256Hash(random + timeStamp + appSecret);
                // 4. 构建JSON字符串
                var tokenData = new
                {
                    Random = random,
                    TimeStamp = timeStamp.ToString(),
                    Signature = signature
                };
                string json = JsonSerializer.Serialize(tokenData);
                // 5. 将JSON字符串转换为Base64
                string base64Token = Convert.ToBase64String(Encoding.UTF8.GetBytes(json));
                // 返回最终的授权头值
                result.Add("Authorization", $"Bearer {base64Token}");
                result.Add("Content-Type", "application/json");
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to headers Token, error message:" + ex.Message);
                return result;
            }
        }

        /// <summary>
        /// 256加密
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        private string ComputeSha256Hash(string encryptionSource)
        {
            try
            {
                using (SHA256 sha256 = SHA256.Create())
                {
                    byte[] hashValue = sha256.ComputeHash(Encoding.UTF8.GetBytes(encryptionSource));
                    var sign = BitConverter.ToString(hashValue).Replace("-", "").ToLowerInvariant();
                    return sign;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("ComputeSha256Hash, error message:" + ex.Message);
                return "";
            }
        }
        #endregion
    }
}
