﻿using Abbott.SpeakerPortal.AppServices.Integration.Dspot;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Integration.Dspot;
using Abbott.SpeakerPortal.Contracts.Integration.Veeva;
using Hangfire;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.BackgroundWorkers.Hangfire;
using Volo.Abp.Uow;

namespace Abbott.SpeakerPortal.BackgroundWorkers.Integration.Dspot
{
    /// <summary>
    /// 全流程报表推送
    /// </summary>
    public class WholeProcessReportPushWorker: SpeakerPortalBackgroundWorkerBase
    {
        private IIntegrationDspotAppService _inteDspot;
        private IScheduleJobLogService _jobLogService;
        
        public WholeProcessReportPushWorker(IServiceProvider serviceProvider)
        {            
            _inteDspot = serviceProvider.GetService<IIntegrationDspotAppService>();
            _jobLogService = serviceProvider.GetService<IScheduleJobLogService>();
            //触发周期,每天凌晨4点
            CronExpression = Cron.Daily(6);
        }

        //[UnitOfWork(IsDisabled = true)]
        public override async Task DoWorkAsync(CancellationToken cancellationToken = default)
        {
            var log = _jobLogService.InitSyncLog("WholeProcessReportPush");
            try
            {
                //逻辑处理
                log.Remark = await _inteDspot.SyncWholeProcessReport();
            }
            catch (Exception ex)
            {
                log.IsSuccess = false;
                log.Remark = ex.ToString();
            }
            finally
            {
                _jobLogService.SyncLog(log);
            }
            
        }   
    }
}
