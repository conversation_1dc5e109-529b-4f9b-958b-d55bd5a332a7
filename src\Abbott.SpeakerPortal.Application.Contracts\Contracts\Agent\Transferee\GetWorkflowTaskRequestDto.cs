﻿using Abbott.SpeakerPortal.Dtos;
using Abbott.SpeakerPortal.Enums;

using System;
using System.Collections.Generic;
using System.Text;

namespace Abbott.SpeakerPortal.Contracts.Agent.Transferee
{
    public class GetWorkflowTaskRequestDto : PagedDto
    {
        /// <summary>
        /// 审批人
        /// </summary>
        public string Approval { get; set; }
        /// <summary>
        /// 审批人ID
        /// </summary>
        public Guid? ApproverId { get; set; }
        /// <summary>
        /// 单据编号
        /// </summary>
        public string ApplicationCode { get; set; }
        /// <summary>
        /// 单据/流程类型
        /// </summary>
        public ResignationTransfer.TaskFormCategory? FormCategory { get; set; }
    }
}
