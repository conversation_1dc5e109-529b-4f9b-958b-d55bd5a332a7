﻿using Abbott.SpeakerPortal.Domain.Shared.Models;
using ClosedXML.Excel;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading.Tasks;

using Volo.Abp.Application.Dtos;
using Volo.Abp.Modularity;

namespace Abbott.SpeakerPortal.Contracts.Budget.Subbudget
{
    public interface ISubbudgetService
    {
        #region 新增子预算-单个
        /// <summary>
        /// 获取成本中心根据BU
        /// </summary>
        /// <param name="BuId">The bu identifier.</param>
        /// <returns></returns>
        Task<MessageResult> GetCostCenterByBuAsync(Guid BuId);
        /// <summary>
        /// 获取大区根据BU
        /// </summary>
        /// <param name="BuId">The bu identifier.</param>
        /// <returns></returns>
        Task<MessageResult> GetRegionByBuAsync(Guid BuId);
        /// <summary>
        /// 单个新增子预算
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        Task<MessageResult> CreateSubbudgetAsync(CreateSubbudgetRequestDto request);
        #endregion

        /// <summary>
        /// 获取子预算详情
        /// </summary>
        /// <param name="subbudgetId">The subbudget identifier.</param>
        /// <returns></returns>
        Task<MessageResult> GetSubbudgetDetailAsync(Guid subbudgetId);


        /// <summary>
        /// 调整子预算
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        Task<MessageResult> AdjustingSubbudgetAsync(AdjustSubbudgetRequestDto request);
        /// <summary>
        /// 调拨子预算
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<MessageResult> TransferSubBudgetAsync(TransferSubBudgetRequestDto request);
        /// <summary>
        /// 获取同主预算下的子预算信息
        /// </summary>
        /// <param name="transfer"></param>
        /// <returns></returns>
        Task<PagedResultDto<SubBudgetResponseDto>> GetSubBudgetListAsync(TransferRequestDto transfer);
        /// <summary>
        /// 验证上传的Excel
        /// </summary>
        /// <param name="Rows"></param>
        /// <returns></returns>
        Task<MessageResult> VarifyImportTransferSubBudgetAsync(IEnumerable<TransferSubBudgetDto> Rows);
        /// <summary>
        /// 批量调拨
        /// </summary>
        /// <param name="messageDtos"></param>
        /// <returns></returns>
        Task<MessageResult> ImportTransferSubBudgetAsync(List<TransferImportMessageDto> messageDtos);
        /// <summary>
        /// 批量调整子预算
        /// </summary>
        /// <param name="Rows"></param>
        /// <returns></returns>
        Task<MessageResult> VarifyImportAdjustmentSubBudgetAsync(IEnumerable<AdjustmentBudgetDto> Rows);
        /// <summary>
        /// 批量提交调整子预算
        /// </summary>
        /// <param name="adjustments"></param>
        /// <returns></returns>
        Task<MessageResult> SubmitImportAdjustmentSubBudgetAsync(List<AdjustmentBudgetDto> adjustments);


        /// <summary>
        /// 获取子预算管理列表
        /// </summary>
        /// <param name="request">筛选条件</param>
        /// <param name="isPaging">是否分页，默认是</param>
        /// <returns></returns>
        Task<PagedResultDto<GetSubbudgetListResponseDto>> GetSubbudgetManageListAsync(GetSubbudgetListRequestDto request, bool isPaging = true);
        /// <summary>
        /// 批量新增
        /// </summary>
        /// <param name="Rows"></param>
        /// <returns></returns>
        Task<MessageResult> VarifyBatchCreateSubBudgetAsync(IEnumerable<CreatesSubBudgetDto> Rows);
        /// <summary>
        /// 批量新增
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<MessageResult> SubmitBatchCreateSubBudgetAsync(List<CreateImportMessageDto> request);

        /// <summary>
        /// 设置子预算状态（冻结false、启用true）
        /// </summary>
        /// <param name="ids">子预算Id列表</param>
        /// <param name="status">状态：false冻结、true启用</param>
        /// <returns></returns>
        Task<MessageResult> SetSubbudgetStatusAsync(List<Guid> ids, bool status);

        /// <summary>
        /// 删除子预算
        /// </summary>
        /// <param name="ids">子预算Id列表</param>
        /// <returns></returns>
        Task<MessageResult> DeleteSubbudget(List<Guid> ids);

        /// <summary>
        /// 导出子预算列表
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        Task<Stream> ExportSubbudgetListAsync(GetSubbudgetListRequestDto request);
        /// <summary>
        /// 子预算批量mapping
        /// </summary>
        /// <param name="Rows"></param>
        /// <returns></returns>
        Task<MessageResult> VarifyBatchMappingSubBudgetAsync(IEnumerable<MappingSubBudgetDto> Rows);
        /// <summary>
        /// 提交批量Mapping
        /// </summary>
        /// <param name="mappings"></param>
        /// <returns></returns>
        Task<MessageResult> SubmitMappingSubBudgetAsync(List<MappingSubgetMessageDto> mappings);

        /// <summary>
        /// 判断子预算是否足够
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        Task<MessageResult> CheckSubbudgetAmountSufficientAsync(UseBudgetRequestDto request);

        /// <summary>
        /// 使用子预算（含反冲）
        /// </summary>
        /// <param name="request"></param>
        /// <param name="autoSave"></param>
        /// <returns></returns>
        Task<MessageResult> UseSubbudgetAsync(UseBudgetRequestDto request, bool autoSave = false);
        /// <summary>
        /// 退回子预算
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        Task<MessageResult> ReturnSubbudgetAsync(ReturnBudgetRequestDto request);
        /// <summary>
        /// 获取子预算使用明细
        /// </summary>
        /// <param name="subbudgetId">The subbudget identifier.</param>
        /// <returns></returns>
        Task<IEnumerable<GetSubbudgetUseInfoResponseDto>> GetSubbudgetUseInfo(Guid subbudgetId);
        /// <summary>
        /// 通过子预算和月份查询该月的预算金额
        /// </summary>
        /// <param name="requestDto"></param>
        /// <returns></returns>
        Task<MessageResult> GetMothlyAmountAsync(GetMothlyBudgetRequestDto requestDto);
        /// <summary>
        /// 将预算分配到每个月上去
        /// </summary>
        /// <returns></returns>
        Task ExcuteJobAsync();


        /// <summary>
        /// 获取某人负责的所有子预算Id
        /// </summary>
        /// <returns></returns>
        Task<IEnumerable<Guid>> GetSubbudgetsByOwner(Guid ownerId);
        /// <summary>
        ///  批量退回子预算
        /// </summary>
        /// <param name="requests">The request.</param>
        /// <returns></returns>
        Task<MessageResult> ReturnSubbudgetAsync(List<ReturnBudgetRequestDto> requests);
    }
}
