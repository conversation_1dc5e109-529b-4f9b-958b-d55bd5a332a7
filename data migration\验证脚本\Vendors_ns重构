CREATE PROCEDURE dbo.sp_vendors_ns
AS 
BEGIN
	--CREATE TABLE PLATFORM_ABBOTT.dbo.spk_occupationaltitlemasterdata (
--	spk_NexBPMCode nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	spk_name nvarchar(255) COLLATE Chinese_PRC_CI_AS NULL,
--	spk_ordernumber nvarchar(255) COLLATE Chinese_PRC_CI_AS NULL,
--	spk_chinesevalue varchar(255) COLLATE Chinese_PRC_CI_AS NOT NULL,
--	spk_chineseremark nvarchar(255) COLLATE Chinese_PRC_CI_AS NULL,
--	spk_englishvalue nvarchar(255) COLLATE Chinese_PRC_CI_AS NULL,
--	spk_englishremark nvarchar(1000) COLLATE Chinese_PRC_CI_AS NULL
--);
--CREATE TABLE PLATFORM_ABBOTT.dbo.spk_departmentmasterdata (
--	spk_NexBPMCode nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	spk_name nvarchar(255) COLLATE Chinese_PRC_CI_AS NULL,
--	ownerid nvarchar(255) COLLATE Chinese_PRC_CI_AS NULL
--);
--
--CREATE TABLE PLATFORM_ABBOTT.dbo.spk_hospitalmasterdata (
--	spk_NexBPMCode nvarchar(50) COLLATE Chinese_PRC_CI_AS NULL,
--	spk_name nvarchar(255) COLLATE Chinese_PRC_CI_AS NULL,
--	spk_hospitalcode nvarchar(255) COLLATE Chinese_PRC_CI_AS NULL,
--	spk_hospitalstatus varchar(255) COLLATE Chinese_PRC_CI_AS NOT NULL,
--	spk_ordernumber nvarchar(255) COLLATE Chinese_PRC_CI_AS NULL,
--	spk_chinesevalue nvarchar(255) COLLATE Chinese_PRC_CI_AS NULL,
--	spk_chineseremark nvarchar(1000) COLLATE Chinese_PRC_CI_AS NULL,
--	spk_englishvalue nvarchar(255) COLLATE Chinese_PRC_CI_AS NULL,
--	spk_englishremark nvarchar(1000) COLLATE Chinese_PRC_CI_AS NULL
--);

select  
a.Id,
ApplicationId,
VendorCode,
OpenId,
UnionId,
HandPhone,
VendorType,
BuCode,
Status,
EpdId,
MndId,
VendorId,
ApsPorperty,
CertificateCode,
isnull(spk_code,'') as SPLevel,
AcademicLevel,
AcademicPosition,
BankCode,
BankCardNo,
case when BankCity <> null or BankCity <> '' then concat(COALESCE(d.spk_provincialadministrativecode,d1.spk_provincialadministrativecode),',',COALESCE(c.spk_cityadministrativedivisioncode,c1.spk_cityadministrativedivisioncode)) else '' end as BankCity,
BankNo,
ExtraProperties,
ConcurrencyStamp,
CONVERT(DATETIME, LEFT(CreationTime, 8) + ' ' + SUBSTRING(CreationTime, 9, 2) + ':' + SUBSTRING(CreationTime, 11, 2) + ':' + SUBSTRING(CreationTime, 13, 2), 120) CreationTime,
e.spk_NexBPMCode as CreatorId,
case when len(LastModificationTime)=14 then CONVERT(DATETIME, LEFT(LastModificationTime, 8) + ' ' + SUBSTRING(LastModificationTime, 9, 2) + ':' + SUBSTRING(LastModificationTime, 11, 2) + ':' + SUBSTRING(LastModificationTime, 13, 2), 120) 
else LastModificationTime end as LastModificationTime,
--CONVERT(DATETIME, STUFF(STUFF(LastModificationTime, 9, 0, ':'), 7, 0, ' '), 120) LastModificationTime,
e1.spk_NexBPMCode as LastModifierId,
IsDeleted,
DeleterId,
DeletionTime,
UserId,
UPPER( f.spk_NexBPMCode) as PTId,
UPPER(g.spk_NexBPMCode) as StandardHosDepId,
UPPER(h.spk_NexBPMCode) as HospitalId,
HosDepartment,
AttachmentInformation,
Description,
DraftVersion,
PaymentTerm,
BankCardImg,
DPSCheck,
SignedStatus,
SignedVersion,
HospitalName,
PTName,
StandardHosDepName,
[BankSwiftCode],
[IsAcademician],
[FormerBPMAcademicPosition],
[HCPType],
[RelationType]
into #Vendors
from  PLATFORM_ABBOTT.dbo.Vendor_Tmp a 
left join PLATFORM_ABBOTT.dbo.spk_dictionary sd 
on a.SPLevel =sd.spk_Name --87872
left join PLATFORM_ABBOTT.dbo.spk_city c
on SUBSTRING(a.BankCity,1,3) =SUBSTRING(c.spk_name,1,3)
left join PLATFORM_ABBOTT.dbo.spk_city c1
on SUBSTRING(a.BankCity,1,2) =SUBSTRING(c1.spk_name,1,2) and c.spk_provincenamename is null  and SUBSTRING(c1.spk_name,1,3)<>N'张家港'
left join PLATFORM_ABBOTT.dbo.spk_province d
on c.spk_provincenamename = d.spk_name 
left join PLATFORM_ABBOTT.dbo.spk_province d1
on c1.spk_provincenamename = d1.spk_name 
left join PLATFORM_ABBOTT.dbo.spk_staffmasterdata e
on a.CreatorId = e.bpm_id
left join PLATFORM_ABBOTT.dbo.spk_occupationaltitlemasterdata f
on a.PTId=f.spk_name
left join PLATFORM_ABBOTT.dbo.spk_departmentmasterdata g
on a.StandardHosDepId=g.spk_name
left join (select DISTINCT  * from Hospital_Cleaning_Data) cd
on cd.Hospital_Data=a.HospitalId
left join PLATFORM_ABBOTT.dbo.spk_hospitalmasterdata h
on cd.corporate_name__v=h.spk_name
left join PLATFORM_ABBOTT.dbo.spk_staffmasterdata e1
on a.LastModifierId = e1.bpm_id

;


WITH SplitAtta AS (
    SELECT 
        A.id AS A_id, -- 假设A表有id字段
        TRIM(value) AS AttachmentInformation
    FROM #Vendors a
    CROSS APPLY STRING_SPLIT(A.AttachmentInformation, ',')
),
File_id as (
	SELECT 
	    A_id,
	    B.id as B_id,
	    B.BPMId
	FROM 
	    SplitAtta
	JOIN PLATFORM_ABBOTT.dbo.Attachments_tmp B ON SplitAtta.AttachmentInformation = B.BPMId
)
select A_id,
STRING_AGG(cast(B_id as nvarchar(1000)), ',') WITHIN GROUP (ORDER BY B_id) AS B_id
into #AttachmentInformation
from File_id
group by A_id;

update a set a.AttachmentInformation=b.B_id from #vendors a
join #AttachmentInformation b
on a.id=b.A_id
PRINT(N'update AttachmentInformation'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'));

    --删除表
    IF OBJECT_ID(N'PLATFORM_ABBOTT.dbo.vendors ', N'U') IS NOT NULL
	BEGIN
		drop table PLATFORM_ABBOTT.dbo.vendors
		select *
        into PLATFORM_ABBOTT.dbo.vendors from #vendors
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into PLATFORM_ABBOTT.dbo.vendors from #vendors
    -- select * from #vendor_tbl
	  PRINT(N'落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END

END;

