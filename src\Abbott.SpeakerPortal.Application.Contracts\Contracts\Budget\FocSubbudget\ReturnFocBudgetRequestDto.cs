﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Abbott.SpeakerPortal.Contracts.Budget.FocSubbudget
{
    public class ReturnFocBudgetRequestDto
    {
        /// <summary>
        /// FOC申请单Id
        /// </summary>
        [Required]
        public Guid FocId { get; set; }
        /// <summary>
        /// 子预算Id
        /// </summary>
        [Required]
        public Guid SubbudgetId { get; set; }
        /// <summary>
        /// 使用详情列表
        /// </summary>
        [Required]
        public IEnumerable<FocReturnInfo> Items { get; set; } = [];
    }

    public class FocReturnInfo
    {
        /// <summary>
        /// FOC申请明细的行号
        /// </summary>
        [Required]
        public int PdRowNo { get; set; }
        /// <summary>
        /// 退回数量
        /// </summary>
        [Required]
        public int ReturnQty { get; set; }
        /// <summary>
        /// 退回的来源单据Id
        /// </summary>
        [Required]
        public Guid ReturnSourceId { get; set; }
        /// <summary>
        /// 退回的来源单据Code
        /// </summary>
        public string ReturnSourceCode { get; set; }
    }
}
