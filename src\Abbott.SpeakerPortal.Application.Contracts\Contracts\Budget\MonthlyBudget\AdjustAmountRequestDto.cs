﻿using Abbott.SpeakerPortal.Enums;
using System;
using System.Collections.Generic;
using System.Text;

namespace Abbott.SpeakerPortal.Contracts.Budget
{
    public class AdjustAmountRequestDto
    {
        /// <summary>
        /// 调整金额
        /// </summary>
        public decimal? AdjustAmount { get; set; }
        /// <summary>
        /// 是否可用
        /// </summary>
        public bool Status { get; set; }
        /// <summary>
        /// 月份
        /// </summary>
        public Month Month { get; set; }
    }
}
