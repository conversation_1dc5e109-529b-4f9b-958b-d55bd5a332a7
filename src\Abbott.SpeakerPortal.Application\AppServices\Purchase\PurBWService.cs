﻿using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Approval;
using Abbott.SpeakerPortal.Contracts.Common.Approve;
using Abbott.SpeakerPortal.Contracts.Common.Attachment;
using Abbott.SpeakerPortal.Contracts.Common.PowerApp;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.Budget;
using Abbott.SpeakerPortal.Entities.Common.Attachment;
using Abbott.SpeakerPortal.Entities.OECSpeakerAuthApplys;
using Abbott.SpeakerPortal.Entities.Purchase.PurBDApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurBWApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPOApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using Abbott.SpeakerPortal.Entities.VendorFinancials;
using Abbott.SpeakerPortal.Entities.VendorOrgnizations;
using Abbott.SpeakerPortal.Entities.VendorPersonals;
using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Extension;
using Abbott.SpeakerPortal.Person;
using Abbott.SpeakerPortal.Purchase.PurExemptApplication;
using Abbott.SpeakerPortal.Utils;

using DocumentFormat.OpenXml.VariantTypes;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

using Org.BouncyCastle.Crypto;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Linq.Expressions;
using System.Text.Json;
using System.Threading.Tasks;

using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;

using static Abbott.SpeakerPortal.Consts.EntitiesNameConsts;
using static Abbott.SpeakerPortal.Enums.Purchase;

namespace Abbott.SpeakerPortal.AppServices.Purchase
{
    public partial class PurBWService : SpeakerPortalAppService, IPurBWService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IPurBWApplicationRepository _purExemptRepository;
        private readonly IRepository<IdentityUser, Guid> _identityUserRepository;
        private readonly ILogger<PurBWService> _logger;
        private readonly IApproveService approveService;

        public PurBWService(IServiceProvider serviceProvider, IApproveService _approveService)
        {
            _serviceProvider = serviceProvider;
            _purExemptRepository = serviceProvider.GetService<IPurBWApplicationRepository>();
            _identityUserRepository = serviceProvider.GetService<IRepository<IdentityUser, Guid>>();
            _logger = serviceProvider.GetService<ILogger<PurBWService>>();
            approveService = _approveService;
        }

        /// <summary>
        /// 获取竞价豁免列表和Justification列表
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public PagedResultDto<PurBWPageResponseDto> GetPage(PurBWPageRequestDto request, bool isPage = true)
        {
            try
            {
                var purExemptQuery = LazyServiceProvider.LazyGetService<IPurBWApplicationReadonlyRepository>().GetQueryableAsync().GetAwaiterResult();

                //获取豁免数据
                var query = purExemptQuery
                    .Where(w => w.ExemptType == request.ExemptType)
                    .WhereIf(!string.IsNullOrWhiteSpace(request.ApplicationCode), p => p.ApplicationCode.Contains(request.ApplicationCode))
                    .WhereIf(!string.IsNullOrWhiteSpace(request.ApplyDeptName), p => p.ApplyDeptName.Contains(request.ApplyDeptName))
                    .WhereIf(!string.IsNullOrWhiteSpace(request.ApplyUserName), p => p.ApplyUserName.Contains(request.ApplyUserName))
                    .WhereIf(request.ApplyUserId != null, p => p.ApplyUserId == request.ApplyUserId)
                    .WhereIf(!string.IsNullOrWhiteSpace(request.VendorName), p => p.VendorName.Contains(request.VendorName))
                    .WhereIf(request.Status.HasValue, p => p.Status == request.Status)
                    //分页列表，去掉状态为“草稿”的数据，“草稿”是一个单独的页
                    .WhereIf(!request.Status.HasValue, p => p.Status != PurExemptStatus.Draft)
                    .WhereIf(request.ApplyStartTime.HasValue, p => p.ApplyTime >= request.ApplyStartTime)
                    .WhereIf(request.ApplyEndTime.HasValue, p => p.ApplyTime <= request.ApplyEndTime)
                    .WhereIf(request.CreationStartTime.HasValue, p => p.CreationTime >= request.CreationStartTime)
                    .WhereIf(request.CreationEndTime.HasValue, p => p.CreationTime <= request.CreationEndTime);

                var personCenterService = LazyServiceProvider.LazyGetService<IPersonCenterService>();
                var rolesIds = personCenterService.GetIdsByRoleLevels(bizTypeCategory: request.ExemptType == ExemptType.Waiver ? ResignationTransfer.TaskFormCategory.BiddingWaiverApplication : ResignationTransfer.TaskFormCategory.JustificationApplication).Result;
                var topLevel = rolesIds.Count == 0 ? RoleLevel.Admin : rolesIds.Min(x => x.Key);

                switch (topLevel)
                {
                    case RoleLevel.Admin://看全部
                        break;
                    case RoleLevel.Manager:
                    case RoleLevel.Leader:
                    case RoleLevel.Owner:
                    case RoleLevel.Applicant:
                    case RoleLevel.Unkonw:
                        var userIds = rolesIds.GetValue(RoleLevel.Applicant);
                        query = query.WhereIf(userIds.Count() > 1, a => userIds.ToHashSet().Contains(a.ApplyUserId) || (a.TransfereeId.HasValue && userIds.ToHashSet().Contains(a.TransfereeId.Value)))
                            .WhereIf(userIds.Count() == 1, a => a.ApplyUserId == CurrentUser.Id || (a.TransfereeId.HasValue && a.TransfereeId.Value == CurrentUser.Id));
                        break;
                }

                var count = query.Count();
                var data = query.OrderByDescending(a => a.CreationTime)
                    .PagingIf(isPage, request.PageIndex, request.PageSize)
                    //先不ToList()，这里只查出需要的列（以下Select只会查出需要的列）
                    .Select(g => new PurBWPageResponseDto
                    {
                        Id = g.Id,
                        ApplicationCode = g.ApplicationCode,
                        ApplyDeptId = g.ApplyDeptId,
                        ApplyDeptName = g.ApplyDeptName,
                        ApplyUserId = g.ApplyUserId,
                        ApplyUserName = g.ApplyUserName,
                        RequisitionAmount = g.RequisitionAmount,
                        VendorId = g.VendorId,
                        VendorName = g.VendorName,
                        ApplyTime = g.ApplyTime,
                        Status = g.Status,
                        CreationTime = g.CreationTime,
                    }).ToList();

                return new PagedResultDto<PurBWPageResponseDto>(count, data);
            }
            catch (Exception ex)
            {
                _logger.LogError($"PurBWService's GetPage() has an error : {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取可用采购申请单列表
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public PagedResultDto<PurBWPRDetailResponseDto> GetPRDetailList(PurBWPRDetailRequestDto request)
        {
            var queryPr = _serviceProvider.GetService<IPurPRApplicationRepository>().GetQueryableAsync().GetAwaiterResult().AsNoTracking();
            var queryPrDetail = _serviceProvider.GetService<IPurPRApplicationDetailRepository>().GetQueryableAsync().GetAwaiterResult().AsNoTracking();

            //排除反冲行
            //var queryHedgeRowIds = queryPrDetail.Where(a => a.HedgePrDetailId.HasValue).Select(a => a.HedgePrDetailId.Value);
            var query = queryPrDetail.Where(a => a.IsHedge == false && a.PushFlag != PushFlagEnum.Pushed && a.PayMethod == PayMethods.AP)
                 .Select(a => new
                 {
                     a.Id,
                     a.VendorName,
                     a.PRApplicationId,
                     a.OriginalEstimateDate,
                     a.PayMethod,
                     a.CostNatureName,
                     a.CostCenterName,
                     a.CityIdName,
                     a.Content,
                     a.Quantity,
                     a.UnitPrice,
                     a.TotalAmountRMB,
                     a.BWApplicationId,
                     a.RowNo
                 })
                 .Join(queryPr.Where(a => a.ApplyUserId == CurrentUser.Id)//当前用户提交的PR单 
                       .Where(a => a.Status != PurPRApplicationStatus.ApplicantTerminate && a.Status != PurPRApplicationStatus.Draft)//排除作废的PR（排除PR.Status=="ApplicantTerminate"）
                       .WhereIf(!string.IsNullOrWhiteSpace(request.PRApplicationCode), a => a.ApplicationCode.Contains(request.PRApplicationCode))
                       .Select(a => new { a.Id, a.ApplyUserId, a.ApplyUserIdName, a.ApplicationCode, a.CreationTime }),
                       a => a.PRApplicationId, a => a.Id, (a, b) => new { PrDetail = a, Pr = b }
                 );

            var count = query.Count();
            var datas = query.OrderByDescending(a => a.Pr.CreationTime).ThenBy(a => a.PrDetail.RowNo)
                .Skip(request.PageIndex * request.PageSize).Take(request.PageSize)
                //先不ToList()，这里只查出需要的列（以下Select只会查出需要的列）
                .Select(a => new PurBWPRDetailResponseDto
                {
                    PrDetailId = a.PrDetail.Id,
                    PrId = a.Pr.Id,
                    ApplyUserId = a.Pr.ApplyUserId,
                    ApplyUserName = a.Pr.ApplyUserIdName,
                    PRApplicationCode = a.Pr.ApplicationCode,
                    VendorName = a.PrDetail.VendorName,
                    EstimateDate = a.PrDetail.OriginalEstimateDate,
                    PayMethod = a.PrDetail.PayMethod.ToString(),
                    CostNatureName = a.PrDetail.CostNatureName,
                    CostCenterName = a.PrDetail.CostCenterName,
                    Region = a.PrDetail.CityIdName,
                    Content = a.PrDetail.Content,
                    Quantity = a.PrDetail.Quantity,
                    UnitPrice = a.PrDetail.UnitPrice,
                    TotalAmountRMB = a.PrDetail.TotalAmountRMB,
                    BWApplicationId = a.PrDetail.BWApplicationId,
                }).ToList();

            //整理最后的3个Code字段
            if (datas.Count > 0)
            {
                //查已有的豁免单(BWApplicationId 为空时已经排除了作废和拒绝)
                var bwIds = datas.Where(a => a.BWApplicationId.HasValue).Select(a => a.BWApplicationId).ToList();
                //排除有豁免单且豁免单状态不是拒绝+不是作废的
                var queryExempt = _serviceProvider.GetService<IPurBWApplicationRepository>().GetQueryableAsync().GetAwaiterResult().AsNoTracking();
                var listExempt = queryExempt.Where(a => bwIds.Contains(a.Id)).ToList();

                //查已有的Bidding单
                var prIds = datas.Select(a => a.PrId);
                var bdInvalidStatus = new PurBDStatus[] { PurBDStatus.Draft, PurBDStatus.Rejected, PurBDStatus.Invalid };
                var queryBd = _serviceProvider.GetService<IPurBDApplicationRepository>().GetQueryableAsync().GetAwaiterResult().AsNoTracking();
                var listBd = queryBd.Where(a => a.PRApplicationId.HasValue && prIds.Contains(a.PRApplicationId.Value))
                    .Where(a => !bdInvalidStatus.Contains(a.Status)).ToList();

                datas.ForEach(a =>
                {
                    //豁免单
                    var curExempts = listExempt.Where(e => e.Id == a.BWApplicationId);
                    a.WaiverFormCode = curExempts.FirstOrDefault(e => e.ExemptType == ExemptType.Waiver)?.ApplicationCode;
                    a.WaiverFormId = curExempts.FirstOrDefault(e => e.ExemptType == ExemptType.Waiver)?.Id;
                    a.JustificationFormCode = curExempts.FirstOrDefault(e => e.ExemptType == ExemptType.Justification)?.ApplicationCode;
                    a.JustificationFormId = curExempts.FirstOrDefault(e => e.ExemptType == ExemptType.Justification)?.Id;

                    //Bidding单(BD单的PrDetailIds存在一个字段，用逗号分隔)
                    var curBds = listBd.Where(b => b.PRApplicationId == a.PrId).ToList();
                    a.BiddingFormCode = curBds.FirstOrDefault(b => b.PRApplicationDetailId.Contains(a.PrDetailId.ToString()))?.ApplicationCode;
                    a.BiddingFormId = curBds.FirstOrDefault(b => b.PRApplicationDetailId.Contains(a.PrDetailId.ToString()))?.Id;
                });
            }

            return new PagedResultDto<PurBWPRDetailResponseDto>()
            {
                TotalCount = count,
                Items = datas,
            };
        }

        /// <summary>
        /// 获取选中PRDetail的相关信息，带出给页面
        /// </summary>
        /// <param name="prDetailId">The pr detail identifier.</param>
        /// <returns></returns>
        public PurBWPRDetailDataResponseDto GetPRDetailData(Guid prDetailId)
        {
            try
            {
                var queryPr = _serviceProvider.GetService<IPurPRApplicationRepository>().GetQueryableAsync().GetAwaiterResult();
                var queryPrDetail = _serviceProvider.GetService<IPurPRApplicationDetailRepository>().GetQueryableAsync().GetAwaiterResult();

                var query = queryPrDetail.Where(a => a.Id == prDetailId)
                    .Where(a => a.PushFlag != PushFlagEnum.Pushed)//排除PRDetail.PushFlag==“已推送”的
                    .Join(queryPr.Where(a => a.ApplyUserId == CurrentUser.Id)//当前用户提交的PR单
                        .Where(a => a.Status != PurPRApplicationStatus.ApplicantTerminate)//排除作废的PR（排除PR.Status=="ApplicantTerminate"）
                        , l => l.PRApplicationId, r => r.Id, (l, r) => new { PrDetail = l, Pr = r });

                var data = query
                    .Select(a => new PurBWPRDetailDataResponseDto
                    {
                        PrDetailId = a.PrDetail.Id,
                        PrId = a.Pr.Id,
                        PRApplicationCode = a.Pr.ApplicationCode,
                        CompanyId = a.Pr.CompanyId,
                        CompanyCode = a.Pr.CompanyCode,
                        CompanyName = a.Pr.CompanyIdName,
                        VendorId = a.PrDetail.VendorId,
                        VendorCode = a.PrDetail.VendorCode,
                        VendorName = a.PrDetail.VendorName,
                        PurchaserId = a.PrDetail.PurchaserId,
                    }).FirstOrDefault();

                //整理 RequisitionAmount
                if (data != null)
                {
                    ////排除反冲和被反冲行
                    //var hedgePrDetails = queryPrDetail.GroupJoin(queryPrDetail, a => a.Id, b => b.HedgePrDetailId, (a, b) => new { Prd = a, hedge = b })
                    //    .SelectMany(a => a.hedge.DefaultIfEmpty(), (a, b) => new { a.Prd, b })
                    //    .Where(m => m.Prd.HedgePrDetailId == null && m.b == null)
                    //    .Select(s => s.Prd);
                    //供应商ID不为空时只看ID，不看名称了。
                    //供应商ID为空时，只能看ID为空且名称相等的。
                    var queryReqAmountRmb = queryPrDetail
                        .Where(a => a.PRApplicationId == data.PrId && a.IsHedge == false && a.PayMethod == PayMethods.AP)//排除非AP
                        .Where(a => a.BWApplicationId == null)//排除已经发起过竞价豁免的数据
                        .WhereIf(data.VendorId.HasValue, a => a.VendorId == data.VendorId)
                        .WhereIf(!data.VendorId.HasValue, a => !a.VendorId.HasValue && a.VendorName == data.VendorName)
                        .Where(a => a.PushFlag != PushFlagEnum.Pushed)//排除PRDetail.PushFlag==“已推送”的
                        .Sum(a => a.TotalAmountRMB);
                    data.RequisitionAmount = queryReqAmountRmb;
                }

                return data;
            }
            catch (Exception ex)
            {
                _logger.LogError($"PurBWService's GetPRDetailData has an error : {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 竞价豁免保存草稿(新增、编辑 的保存)
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> SaveAsync(PurBWRequestDto request)
        {
            try
            {
                var repoExempt = _serviceProvider.GetService<IPurBWApplicationRepository>();
                var queryPr = _serviceProvider.GetService<IPurPRApplicationRepository>().GetQueryableAsync().GetAwaiterResult();
                var queryPrDetail = _serviceProvider.GetService<IPurPRApplicationDetailRepository>().GetQueryableAsync().GetAwaiterResult();

                PurBWApplication resultData = null;
                if (request.Id != null)
                {
                    //修改
                    var exempt = await repoExempt.FirstOrDefaultAsync(f => f.Id == request.Id);
                    if (exempt == null)
                    {
                        return MessageResult.FailureResult("修改数据不存在");
                    }

                    PurExemptStatus[] editableStatus = [PurExemptStatus.Draft, PurExemptStatus.Return];
                    if (!editableStatus.Contains(exempt.Status))
                        return MessageResult.FailureResult("数据状态不是草稿/重新发起，不能修改/提交");

                    exempt = ObjectMapper.Map(request, exempt);
                    //申请人提交时，才修改发起人和发起部门
                    if (exempt.ApplyUserId == CurrentUser.Id)
                    {
                        exempt.ApplyDeptId = request.ApplyDeptId ?? Guid.Empty;
                        exempt.ApplyDeptName = request.ApplyDeptName;
                    }
                    UpdateEntity(request, ref exempt);
                    await repoExempt.UpdateAsync(exempt, true);
                    resultData = exempt;
                }
                else
                {
                    //新增
                    var detail = await repoExempt.FirstOrDefaultAsync(f => f.PRDetailId == request.PRDetailId
                        && f.Status != PurExemptStatus.Rejected
                        && f.Status != PurExemptStatus.Invalid);
                    if (detail != null)
                    {
                        return MessageResult.FailureResult("该采购申请已有豁免记录");
                    }

                    var exempt = ObjectMapper.Map<PurBWRequestDto, PurBWApplication>(request);
                    exempt.ApplyUserId = CurrentUser.Id.HasValue ? CurrentUser.Id.Value : Guid.Empty;
                    exempt.ApplyUserName = CurrentUser.Name;
                    //zhx20240730:在MapperProfile里忽略掉部门后，在此处新增时，手动赋值部门
                    exempt.ApplyDeptId = request.ApplyDeptId ?? Guid.Empty;
                    exempt.ApplyDeptName = request.ApplyDeptName;
                    UpdateEntity(request, ref exempt);
                    await InsertAndGenerateSerialNoAsync(repoExempt, exempt, exempt.ExemptType == ExemptType.Waiver ? "W" : "J");
                    resultData = exempt;
                }
                return MessageResult.SuccessResult(resultData);
            }
            catch (Exception ex)
            {
                _logger.LogError($"PurBWService's SaveAsync has an error : {ex.Message}");
                return MessageResult.FailureResult("保存失败");
            }
        }

        /// <summary>
        /// 竞价豁免提交，验证、保存、提交PP创建Workflow；
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> SubmitAsync(PurBWRequestDto request)
        {
            try
            {
                var prDetailRepository = _serviceProvider.GetService<IPurPRApplicationDetailRepository>();
                var queryPrDetail = await prDetailRepository.GetQueryableAsync();
                var prdsByVendor = queryPrDetail
                       .Where(a => a.PRApplicationId == request.PRId && a.PayMethod == PayMethods.AP)
                       .Where(a => a.BWApplicationId == null || a.BWApplicationId == request.Id)//a.BWApplicationId == request.Id 表示编辑
                       .WhereIf(request.VendorId.HasValue, a => a.VendorId == request.VendorId)
                       .WhereIf(!request.VendorId.HasValue, a => !a.VendorId.HasValue && a.VendorName == request.VendorName)
                       .Where(a => a.PushFlag != PushFlagEnum.Pushed)//排除PRDetail.PushFlag==“已推送”的
                       .ToList();//查询用户选择的PR下相同供应商的PR明细
                //排除PR明细反冲行
                var hedgeRowIds = prdsByVendor.Where(a => a.HedgePrDetailId.HasValue).Select(a => a.HedgePrDetailId.Value).ToArray();
                var prDetailsByVendor = prdsByVendor.Where(a => !a.HedgePrDetailId.HasValue && !hedgeRowIds.Contains(a.Id)).ToList();

                if (!prDetailsByVendor.Any())
                    return MessageResult.FailureResult("提交时选择的PR当前状态不能发起申请");

                //保存
                var saveResult = await SaveAsync(request);
                if (!saveResult.Success)
                {
                    saveResult.Data = null;
                    return saveResult;
                }

                var savedData = saveResult.Data as PurBWApplication;
                if (savedData == null)
                {
                    return MessageResult.FailureResult("提交时保存的数据错误");
                }

                var isOk = await CreateWorkflowAsync(savedData);
                if (!isOk)
                    return MessageResult.FailureResult("提交审批失败");
                else
                {
                    //更新申请的状态为“审批中”、申请人（保存时已更新）、申请时间
                    savedData.Status = PurExemptStatus.Approving;
                    savedData.ApplyTime = DateTime.Now;
                    savedData.PRDetailIds = string.Join(",", prDetailsByVendor.Select(a => a.Id));//存入相同供应商PR明细ID
                    savedData.RequisitionAmount = prDetailsByVendor.Sum(a => a.TotalAmountRMB);
                    await LazyServiceProvider.LazyGetService<IPurBWApplicationRepository>().UpdateAsync(savedData);
                }
                var ids = savedData.PRDetailIds.Split(',').Select(Guid.Parse).ToList();
                var prDetails = queryPrDetail.Where(a => ids.Contains(a.Id)).ToList();
                prDetails.ForEach(a => { a.BWApplicationId = savedData.Id; });
                await prDetailRepository.UpdateManyAsync(prDetails);
                return MessageResult.SuccessResult("提交成功");
            }
            catch (Exception ex)
            {
                _logger.LogError($"PurBWService's SubmitAsync has an error : {ex.Message}");
                return MessageResult.FailureResult("提交失败");
            }
        }

        #region 私有方法
        /// <summary>
        ///  添加审批任务
        /// </summary>
        /// <param name="request"></param>
        /// <param name="submitter"></param>
        /// <param name="firstApprover"></param>
        /// <returns></returns>
        private async Task<bool> CreateWorkflowAsync(PurBWApplication savedData)
        {
            var approvalRecord = LazyServiceProvider.LazyGetService<IInitializeApprovalRecordService>();
            //走审批，PP创建Workflow
            var workflowTypes = EnumUtil.GetEnumIdValues<WorkflowTypeName>().ToDictionary(a => (WorkflowTypeName)a.Key, a => a.Value);
            var name = savedData.ExemptType == ExemptType.Waiver ?
                    workflowTypes[WorkflowTypeName.ExemptWaiver] : workflowTypes[WorkflowTypeName.ExemptJustification];
            var createApproval = new CreateApprovalDto
            {
                Name = name,
                Department = savedData.ApplyDeptId.ToString(),
                BusinessFormId = savedData.Id.ToString(),
                BusinessFormNo = savedData.ApplicationCode,
                BusinessFormName = savedData.ExemptType == ExemptType.Waiver ?
                    NameConsts.PurExemptWaiver : NameConsts.PurExemptJustification,
                Submitter = CurrentUser.Id?.ToString(),
                OriginalApprovalId = savedData.ApplyUserId,
                WorkflowType = savedData.ExemptType == ExemptType.Waiver ?
                    WorkflowTypeName.ExemptWaiver : WorkflowTypeName.ExemptJustification,
                FirstApprover = savedData.PurchaserId.ToString(),
                InstanceName = $"{name}-{DateTime.Now.ToString("yyMMdd-hhmmss")}",
                Remark = savedData.Remark
            };
            await approvalRecord.InsertInitApprovalRecordAsync(new InitializeApprovalRecordDto
            {
                BusinessFormName = savedData.ExemptType == ExemptType.Waiver ?
                    NameConsts.PurExemptWaiver : NameConsts.PurExemptJustification,
                BusinessId = savedData.Id,
                InstanceId = Guid.NewGuid(),
                Status = InitApprovalRecordStatus.Pending,
                MaxRetries = 3,
                RequestContent = JsonSerializer.Serialize(createApproval)
            });
            return true;
        }
        #endregion

        /// <summary>
        /// 获取详情
        /// </summary>
        /// <param name="id">The id.</param>
        /// <returns></returns>
        public MessageResult Get(Guid id, string stepNo = null)
        {
            try
            {
                //豁免查询
                var exempt = _purExemptRepository.GetAsync(id).GetAwaiterResult();
                if (exempt == null) return MessageResult.FailureResult($"未找到豁免数据, 请核实Id：{id}");

                //stepNo=="100"时，要获取第2步的审批人ID
                var result = stepNo == "100" ? GetApprovalDetail(exempt)
                    : ObjectMapper.Map<PurBWApplication, PurBWResponseDto>(exempt);

                //附件
                if (!string.IsNullOrWhiteSpace(exempt.AttachmentIds))
                {
                    var attachmentIds = exempt.AttachmentIds.Split(",", StringSplitOptions.RemoveEmptyEntries).Select(Guid.Parse).ToArray();
                    var queryAttachment = LazyServiceProvider.LazyGetService<IAttachmentRepository>().GetQueryableAsync().GetAwaiterResult();
                    result.Attachments = queryAttachment.Where(a => attachmentIds.Contains(a.Id))
                        .Select(a => new UploadFileResponseDto
                        {
                            AttachmentId = a.Id,
                            FilePath = a.FilePath,
                            FileName = a.FileName,
                            FileSize = a.Size.ToFileSizeString(),
                        }).ToArray();
                }

                if (result.EstDeliveryDate.HasValue)
                    result.EstDeliveryDateStr = result.EstDeliveryDate.Value.ToString("yyyy-MM-dd");

                return MessageResult.SuccessResult(result);
            }
            catch (Exception ex)
            {
                _logger.LogError($"PurBWService's Get() has an error : {ex.Message}");
                return MessageResult.FailureResult("查询出错");
            }
        }

        /// <summary>
        /// 竞价豁免和Justification删除草稿
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> DeleteDraft(DeletePurBWRequestDto request)
        {
            try
            {
                if (request.Ids?.Count < 1)
                {
                    return MessageResult.FailureResult($"没有要删除的数据Id");
                }

                var repo = _serviceProvider.GetService<IPurBWApplicationRepository>();
                var datas = await repo.GetListAsync(a => request.Ids.Contains(a.Id));
                if (datas?.Count < 1)
                {
                    return MessageResult.FailureResult($"未找到要删除的豁免单");
                }

                //这些Id没有相关记录
                var incorrectIds = request.Ids.Where(a => !datas.Select(d => d.Id).Contains(a));
                if (incorrectIds?.Count() > 0)
                {
                    return MessageResult.FailureResult($"未找到以下Id的豁免单：{incorrectIds.JoinAsString(",")}");
                }

                //以下这些数据状态不属于草稿
                var unDraftIds = datas.Where(a => a.Status != PurExemptStatus.Draft).Select(a => a.Id);
                if (unDraftIds?.Count() > 0)
                {
                    return MessageResult.FailureResult($"以下Id的豁免单不是草稿，不能删除：{unDraftIds.JoinAsString(",")}");
                }

                await repo.DeleteManyAsync(datas);
                return MessageResult.SuccessResult();
            }
            catch (Exception ex)
            {
                _logger.LogError($"PurBWService's SavePurExemptAsync has an error : {ex.Message}");
                return MessageResult.FailureResult("删除失败");
            }
        }

        /// <summary>
        /// BW 作废
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<MessageResult> CancellationAsync(Guid id)
        {
            var approveService = LazyServiceProvider.LazyGetService<IApproveService>();
            var bwApplication = LazyServiceProvider.LazyGetService<IPurBWApplicationRepository>();
            var query = await bwApplication.GetQueryableAsync();
            var queryPo = await LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>().GetQueryableAsync();
            var queryPoDetail = await LazyServiceProvider.LazyGetService<IPurPOApplicationDetailsRepository>().GetQueryableAsync();
            var bw = query.Where(a => a.Id == id).FirstOrDefault();
            if (bw == null)
                return MessageResult.FailureResult("未查询到豁免单");
            var pos = queryPo.GroupJoin(queryPoDetail, a => a.Id, b => b.POApplicationId, (a, b) => new { po = a, pods = b })
                .SelectMany(a => a.pods.DefaultIfEmpty(), (a, b) => new { a.po, pod = b })
                .Where(a => a.pod.PRDetailId == bw.PRDetailId)
                .ToList();
            if (pos.Any(a => a.po.Status != PurOrderStatus.Invalid))
            {
                return MessageResult.FailureResult("已发起PO且PO不是作废状态，该豁免不能作废");
            }
            //状态 
            PurExemptStatus[] pending = [PurExemptStatus.Return];//待处理
            PurExemptStatus[] completed = [PurExemptStatus.Approved, PurExemptStatus.Rejected, PurExemptStatus.Invalid];//已完成

            if (pending.Contains(bw.Status))
            {
                var updateApproval = new List<UpdateApprovalDto>() {
                    new UpdateApprovalDto() {
                        BusinessFormId = bw.Id.ToString(),
                        OperationStatus = ApprovalOperation.Delete,
                        Submitter = CurrentUser.Id.Value,
                        Remark = "作废（解绑）",
                    }
                };
                return await approveService.SubmitterOperationAsync(updateApproval);
            }
            else if (completed.Contains(bw.Status))
            {
                bw.Status = PurExemptStatus.Invalid;
                await bwApplication.UpdateAsync(bw);
                if (!string.IsNullOrWhiteSpace(bw.PRDetailIds))
                {
                    var prDetailRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>();
                    var queryPrDetail = await prDetailRepository.GetQueryableAsync();
                    var prdIds = bw.PRDetailIds.Split(',').Select(Guid.Parse);
                    var prds = queryPrDetail.Where(a => prdIds.Contains(a.Id)).ToList();
                    prds.ForEach(a =>
                    {
                        a.BWApplicationId = null;//解绑BW
                    });
                    await prDetailRepository.UpdateManyAsync(prds);
                }
                var addApproval = new AddApprovalRecordDto
                {
                    FormId = bw.Id,
                    ApprovalId = CurrentUser.Id.Value,
                    OriginalApprovalId = bw.ApplyUserId,//申请人Id
                    Status = ApprovalOperation.Delete,
                    Remark = "作废（解绑）",
                    ApprovalTime = DateTime.Now,
                    WorkStep = "提交人操作",
                    Name = "作废"
                };
                await approveService.AddApprovalRecordAsync(addApproval);
            }
            else
            {
                return MessageResult.FailureResult("当前状态不能作废");
            }
            return MessageResult.SuccessResult();
        }

        #region 私方法

        /// <summary>
        /// 修改数据
        /// </summary>
        private void UpdateEntity(PurBWRequestDto request, ref PurBWApplication exempt)
        {
            if (request.Attachments?.Count() > 0)
            {
                exempt.AttachmentIds = string.Join(",", request.Attachments.Select(s => s.AttachmentId.ToString()).ToList());
            }
            exempt.Status = PurExemptStatus.Draft;
            //exempt.ApplyTime = DateTime.Now;//保存草稿时是否可不赋值

            //页面上下拉的字段，根据Id取其名称或文本(Dept+Purchaser+WaiverReason、Just..Type)
            //Dept
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            //clx 20241001，完整部门名称已从前端获取，如果这里要更新，也应该取全名称。
            //exempt.ApplyDeptName = dataverseService.GetOrganizations().GetAwaiterResult()
            //    .FirstOrDefault(a => a.Id == request.ApplyDeptId)?.DepartmentName;
            //Purchaser
            exempt.PurchaserName = dataverseService.GetProcurementPushConfigAsync().GetAwaiterResult()
                .FirstOrDefault(a => a.EmployeeId == request.PurchaserId)?.EmployeeName;
            //WaiverReason
            exempt.WaiveReasonText = dataverseService.GetDictionariesAsync(DictionaryType.WaiverReason).GetAwaiterResult()
                .FirstOrDefault(a => a.Code == request.WaiveReasonCode)?.Name;
            //Just..Type
            exempt.JustificationTypeText = dataverseService.GetDictionariesAsync(DictionaryType.JustificationType).GetAwaiterResult()
                .FirstOrDefault(a => a.Code == request.JustificationTypeCode)?.Name;
        }

        private PurBWResponseApprovalDto GetApprovalDetail(PurBWApplication exempt)
        {
            var result = ObjectMapper.Map<PurBWApplication, PurBWResponseApprovalDto>(exempt);

            if (result == null || !result.PRId.HasValue)
            {
                return result;
            }

            //获取：豁免申请单所选采购单据上的所选预算的预算负责人ID
            var pr = _serviceProvider.GetService<IPurPRApplicationRepository>().GetAsync(result.PRId.Value).GetAwaiterResult();
            if (pr?.SubBudgetId.HasValue != true)
            {
                return result;
            }

            result.SecondApproverId = _serviceProvider.GetService<IBdSubBudgetRepository>().GetAsync(pr.SubBudgetId.Value).GetAwaiterResult()?.OwnerId;
            return result;
        }

        #endregion 私方法
    }
}