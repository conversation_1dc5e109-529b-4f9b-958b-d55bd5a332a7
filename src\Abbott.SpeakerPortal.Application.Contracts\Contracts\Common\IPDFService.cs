﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.DependencyInjection;

namespace Abbott.SpeakerPortal.Contracts.Common
{
    public interface IPDFService : ITransientDependency
    {
        /// <summary>
        /// 生成po的pdf
        /// </summary>
        /// <param name="dict"></param>
        /// <param name="lsitDict"></param>
        /// <returns></returns>
        Task<MemoryStream> GeneratePoPdfAsync(Dictionary<string, string> dict, List<Dictionary<string, string>> lsitDict);
    }
}
