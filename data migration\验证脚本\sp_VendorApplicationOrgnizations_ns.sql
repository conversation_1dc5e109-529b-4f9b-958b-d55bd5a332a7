CREATE PROCEDURE dbo.sp_VendorApplicationOrgnizations_ns
AS 
BEGIN
with cityInfo as (
	SELECT 
		b.spk_name ,
		b.spk_provincecode ,
		a.spk_citycode ,
		a.spk_name as spk_name1,
		LEFT(a.spk_name,2) spk_name2,
		LEFT(a.spk_name,3) spk_name3,
		LEFT(a.spk_name,4) spk_name4,
		Len(a.spk_name) spk_nameSize,
		b.spk_provincialadministrativecode as province_code,
		a.spk_cityadministrativedivisioncode as city_code
	from   
		PLATFORM_ABBOTT.dbo.spk_city a,
		PLATFORM_ABBOTT.dbo.spk_province b
	where  a.spk_provincenamename = b.spk_name
)
select 
     a.[Id]
    ,a.[ApplicationId]
    ,a.[VendorName]
    ,a.[VendorOldName]
    ,a.[VendorEngName]
    ,a.[RegCertificateAddress]
    ,a.[PostCode]
    ,a.[ContactName]
    ,a.[ContactPhone]
    ,a.[ContactEmail]
    ,a.[WebSite]
    ,a.[RegisterDate]
    ,sd.spk_type  as [OrgType]
    ,a.[IssuingAuthority]
    ,a.[RegisterCode]
    ,a.[RegValidityStart]
    ,a.[RegValidityEnd]
	,coalesce(cast(coalesce(city2.province_code, city3.province_code, city4.province_code, city5.province_code) as nvarchar(255)),'000000') as [Province]
	,coalesce(cast(coalesce(city2.city_code, city3.city_code, city4.city_code, city5.city_code) as nvarchar(255)),'000000') as [City]
    ,a.[Legal]
    ,a.[RegisterAmount]
    ,a.[BusinessAuthority]
    ,a.[BusinessScope]
    ,a.[LastYearSales]
    ,a.[KeyIndustry]
    ,a.[KeyClient]
    ,a.[Staffs]
    ,a.[Aptitudes]
    ,a.[ApplyReason]
    ,a.[ExtraProperties]
    ,a.[ConcurrencyStamp]
    ,a.[CreationTime]
    ,ss.spk_NexBPMCode as [CreatorId]
    ,a.[LastModificationTime]
    ,a.[LastModifierId]
    ,a.[IsDeleted]
    ,a.[DeleterId]
    ,a.[DeletionTime]
    ,a.[Shareholder]
    into #VendorApplicationOrgnizations
    from VendorApplicationOrgnizations_Tmp a 
    left join spk_dictionary sd 
        on a.OrgType COLLATE SQL_Latin1_General_CP1_CI_AS=sd.spk_BPMCode and sd.spk_BPMCode is not null and sd.spk_BPMCode <> ''
    left join spk_staffmasterdata ss 
        on a.CreatorId COLLATE SQL_Latin1_General_CP1_CI_AS=ss.bpm_id
    left join cityInfo as city2
        on a.city = city2.spk_name1
    left join cityInfo as city3
        on a.city = city3.spk_name2
    left join cityInfo as city4
        on a.city = city4.spk_name3
    left join cityInfo as city5
        on a.city = city5.spk_name4

 IF OBJECT_ID(N'PLATFORM_ABBOTT.dbo.VendorApplicationOrgnizations ', N'U') IS NOT NULL
	BEGIN
		drop table PLATFORM_ABBOTT.dbo.VendorApplicationOrgnizations
		select *
        into PLATFORM_ABBOTT.dbo.VendorApplicationOrgnizations from #VendorApplicationOrgnizations
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into PLATFORM_ABBOTT.dbo.VendorApplicationOrgnizations from #VendorApplicationOrgnizations
    -- select * from #vendor_tbl
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END

END;