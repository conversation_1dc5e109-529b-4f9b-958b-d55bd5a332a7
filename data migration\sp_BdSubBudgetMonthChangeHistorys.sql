CREATE PROCEDURE dbo.sp_BdSubBudgetMonthChangeHistorys
AS 
BEGIN

WITH Months AS (
    SELECT 1 AS Month
    UNION ALL
    SELECT Month + 1
    FROM Months
    WHERE Month < 12
),

Data AS (
    SELECT 
         NEWID() AS Id
		,[Id] AS [HistoryId]
		,1 AS [Month]
		,cast([OperateAmount] AS decimal(32,8)) AS [OperateAmount]
		,1 AS [Status]
		,[ExtraProperties]
		,[ConcurrencyStamp]
		,[CreationTime]
		,[CreatorId]
		,[LastModificationTime]
		,[LastModifierId]
		,0 AS [IsDeleted]
		,NULL AS [DeleterId]
		,NULL AS [DeletionTime]

    FROM 
        PLATFORM_ABBOTT_Stg.dbo.BdHistory
		--where Id = '6D34F9AA-2348-402A-9882-D86DA9957E6D'
)

SELECT 
    d.Id,
    d.HistoryId,
    CASE WHEN m.Month = 1 THEN d.OperateAmount ELSE 0 END AS OperateAmount,
    d.<PERSON>,
    m.Month AS Month,
    d.ExtraProperties,
    d.ConcurrencyStamp,
    d.CreationT<PERSON>,
    d.CreatorId,
    d.LastModificationTime,
    d.LastModifierId,
    d.<PERSON>ted,
    d.DeleterId,
    d.DeletionTime
INTO #BdSubBudgetMonthChangeHistorys
FROM 
    Months m
CROSS JOIN
    Data d
ORDER BY 
    m.Month
	

 IF OBJECT_ID(N'PLATFORM_ABBOTT_Stg.dbo.BdSubBudgetMonthChangeHistorys ', N'U') IS NOT NULL
	BEGIN
		update a 
		set a.HistoryId           = b.HistoryId
           ,a.OperateAmount       = b.OperateAmount
--           ,a.Status              = b.Status
           ,a.Month               = b.Month
--           ,a.ExtraProperties     = b.ExtraProperties
--           ,a.ConcurrencyStamp    = b.ConcurrencyStamp
--           ,a.CreationTime        = b.CreationTime
--           ,a.CreatorId           = b.CreatorId
--           ,a.LastModificationTime= b.LastModificationTime
--           ,a.LastModifierId      = b.LastModifierId
--           ,a.IsDeleted           = b.IsDeleted
--           ,a.DeleterId           = b.DeleterId
--           ,a.DeletionTime        = b.DeletionTime
		from PLATFORM_ABBOTT_Stg.dbo.BdSubBudgetMonthChangeHistorys a
		left join #BdSubBudgetMonthChangeHistorys b on a.HistoryId = b.HistoryId and a.Month = b.Month
		
		insert into PLATFORM_ABBOTT_Stg.dbo.BdSubBudgetMonthChangeHistorys
		select a.Id                  
              ,a.HistoryId           
              ,a.OperateAmount       
              ,a.Status              
              ,a.Month               
              ,a.ExtraProperties     
              ,a.ConcurrencyStamp    
              ,a.CreationTime        
              ,a.CreatorId           
              ,a.LastModificationTime
              ,a.LastModifierId      
              ,a.IsDeleted           
              ,a.DeleterId           
              ,a.DeletionTime   
       from #BdSubBudgetMonthChangeHistorys a 
       where not exists (select * from PLATFORM_ABBOTT_Stg.dbo.BdSubBudgetMonthChangeHistorys where HistoryId = a.HistoryId and Month = a.Month)
    PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
	ELSE
	BEGIN
    --落成实体表
    select *
        into PLATFORM_ABBOTT_Stg.dbo.BdSubBudgetMonthChangeHistorys from #BdSubBudgetMonthChangeHistorys
    -- select * from #vendor_tbl
	  PRINT(N'update 落成实体表'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
	END
END	
;