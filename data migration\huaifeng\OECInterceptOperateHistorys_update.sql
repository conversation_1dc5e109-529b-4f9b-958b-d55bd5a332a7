SELECT
 TRY_CONVERT(UNIQUEIDENTIFIER, [Id]) [Id]
,TRY_CONVERT(UNIQUEIDENTIFIER, [PRDetailId])[PRDetailId]
,[OperateType] 
,[OperateContent]
,[Remark]
,[OperateTime]
,'{}' AS [ExtraProperties]
,'NULL' AS [ConcurrencyStamp]
,[CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CreatorId]) [CreatorId]
,[LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [LastModifierId]) [LastModifierId]
,0 AS [IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, [DeleterId]) [DeleterId]
,[DeletionTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [InterceptId])[InterceptId]
,TRY_CONVERT(UNIQUEIDENTIFIER, [OperateUserId])[OperateUserId]
,[OperateUserName]
,0 AS [SendBackLimitAmount]
,[SolveInterceptType]
INTO #OECInterceptOperateHistorys
FROM PLATFORM_ABBOTT_STG.dbo.OECInterceptOperateHistorys_ns;

--drop table #OECInterceptOperateHistorys


USE Speaker_Portal_STG;

UPDATE a
SET
 a.[Id] = b.[Id]
,a.[PRDetailId] = b.[PRDetailId]
,a.[OperateType] = b.[OperateType]
,a.[OperateContent] = b.[OperateContent]
,a.[Remark] = b.[Remark]
,a.[OperateTime] = b.[OperateTime]
,a.[ExtraProperties] = b.[ExtraProperties]
,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = b.[CreatorId]
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = b.[LastModifierId]
,a.[IsDeleted] = b.[IsDeleted]
,a.[DeleterId] = b.[DeleterId]
,a.[DeletionTime] = b.[DeletionTime]
,a.[InterceptId] = b.[InterceptId]
,a.[OperateUserId] = b.[OperateUserId]
,a.[OperateUserName] = b.[OperateUserName]
,a.[SendBackLimitAmount] = b.[SendBackLimitAmount]
,a.[SolveInterceptType] = b.[SolveInterceptType]
FROM dbo.OECInterceptOperateHistorys a
left join #OECInterceptOperateHistorys  b
ON a.id=b.id


INSERT INTO dbo.OECInterceptOperateHistorys
SELECT
 [Id]
,[PRDetailId]
,[OperateType]
,[OperateContent]
,[Remark]
,[OperateTime]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[InterceptId]
,[OperateUserId]
,[OperateUserName]
,[SendBackLimitAmount]
,[SolveInterceptType]
FROM #OECInterceptOperateHistorys a
WHERE not exists (select * from dbo.OECInterceptOperateHistorys where id=a.id)


--truncate table dbo.OECInterceptOperateHistorys
