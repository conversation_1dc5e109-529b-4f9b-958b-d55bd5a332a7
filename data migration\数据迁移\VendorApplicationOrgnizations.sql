SELECT
 TRY_CONVERT(UNIQUEIDENTIFIER, [Id]) [Id]
,TRY_CONVERT(UNIQUEIDENTIFIER, [ApplicationId]) [ApplicationId]
,[VendorName]
,[VendorOldName]
,[VendorEngName]
,CASE WHEN [RegCertificateAddress] IS NULL OR [RegCertificateAddress] = '' THEN '-' ELSE [RegCertificateAddress] END [RegCertificateAddress]
,CASE WHEN [PostCode] IS NULL OR [PostCode] = '' THEN '-' ELSE [PostCode] END [PostCode]
,CASE WHEN [ContactName] IS NULL OR [ContactName] = '' THEN '-' ELSE [ContactName] END [ContactName]
,[ContactPhone]
,[ContactEmail]
,[WebSite]
,case when RegisterDate is null or RegisterDate =''  then '1900-01-01 00:00:00' else STUFF(STUFF(RegisterDate, 5, 1, '-'), 8, 1, '-') end  [RegisterDate]
,[OrgType]
,[IssuingAuthority]
,[RegisterCode]
,case when RegValidityStart is null or RegValidityStart =''  then '1900-01-01 00:00:00' else STUFF(STUFF(RegValidityStart, 5, 1, '-'), 8, 1, '-') end [RegValidityStart]
,case when RegValidityEnd is null or RegValidityEnd =''  then '1900-01-01 00:00:00' else STUFF(STUFF(RegValidityEnd, 5, 1, '-'), 8, 1, '-') end [RegValidityEnd]
,CASE WHEN [Province] IS NULL OR [Province] = '' THEN '-' ELSE [Province] END [Province]
,CASE WHEN [City] IS NULL OR [City] = '' THEN '-' ELSE [City] END [City]
,[Legal]
,case when RegisterAmount is null  or RegisterAmount ='' then '0' 
	  when RegisterAmount in ('不详', '无','N/A') then '0'
else RegisterAmount end AS [RegisterAmount] --字段值部分为空
,[BusinessAuthority]
,[BusinessScope]
,case when LastYearSales is null  or LastYearSales ='' then '0'
	  when LastYearSales in ('不详', '无','N/A') then '0'
else LastYearSales end AS [LastYearSales] --字段值部分为空
,[KeyIndustry]
,[KeyClient]
,case when Staffs is null  or Staffs ='' then '0' 
	  when Staffs in ('不详', '无','N/A') then '0'
else Staffs end AS [Staffs]
,[Aptitudes]
,case when ApplyReason is null  or ApplyReason ='' then '-'
else ApplyReason end [ApplyReason]
,[ExtraProperties]
,'' [ConcurrencyStamp]
,[CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CreatorId]) [CreatorId]
,[LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [LastModifierId]) [LastModifierId]
,[IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, [DeleterId]) [DeleterId]
,[DeletionTime]
,[Shareholder]
INTO #VendorApplicationOrgnizations
FROM (SELECT DISTINCT *,ROW_NUMBER() OVER(PARTITION BY Id ORDER BY Id) RK FROM PLATFORM_ABBOTT.dbo.VendorApplicationOrgnizations)a
WHERE RK = 1
;
--drop table #VendorApplicationOrgnizations

select * from #VendorApplicationOrgnizations

USE Speaker_Portal;
ALTER TABLE Speaker_Portal.dbo.VendorApplicationOrgnizations ALTER COLUMN RegisterAmount nvarchar(255) NULL;
ALTER TABLE Speaker_Portal.dbo.VendorApplicationOrgnizations ALTER COLUMN LastYearSales nvarchar(255) NULL;
ALTER TABLE Speaker_Portal.dbo.VendorApplicationOrgnizations ALTER COLUMN Staffs nvarchar(255) NULL;

UPDATE a 
SET 
 a.[Id] = b.[Id]
,a.[ApplicationId] = b.[ApplicationId]
,a.[VendorName] = b.[VendorName]
,a.[VendorOldName] = b.[VendorOldName]
,a.[VendorEngName] = b.[VendorEngName]
,a.[RegCertificateAddress] = b.[RegCertificateAddress]
,a.[PostCode] = b.[PostCode]
,a.[ContactName] = b.[ContactName]
,a.[ContactPhone] = b.[ContactPhone]
,a.[ContactEmail] = b.[ContactEmail]
,a.[WebSite] = b.[WebSite]
,a.[RegisterDate] = b.[RegisterDate]
,a.[OrgType] = b.[OrgType]
,a.[IssuingAuthority] = b.[IssuingAuthority]
,a.[RegisterCode] = b.[RegisterCode]
,a.[RegValidityStart] = b.[RegValidityStart]
,a.[RegValidityEnd] = b.[RegValidityEnd]
,a.[Province] = b.[Province]
,a.[City] = b.[City]
,a.[Legal] = b.[Legal]
,a.[RegisterAmount] = b.[RegisterAmount]
,a.[BusinessAuthority] = b.[BusinessAuthority]
,a.[BusinessScope] = b.[BusinessScope]
,a.[LastYearSales] = b.[LastYearSales]
,a.[KeyIndustry] = b.[KeyIndustry]
,a.[KeyClient] = b.[KeyClient]
,a.[Staffs] = b.[Staffs]
,a.[Aptitudes] = b.[Aptitudes]
,a.[ApplyReason] = b.[ApplyReason]
,a.[ExtraProperties] = b.[ExtraProperties]
,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = b.[CreatorId]
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = b.[LastModifierId]
,a.[IsDeleted] = b.[IsDeleted]
,a.[DeleterId] = b.[DeleterId]
,a.[DeletionTime] = b.[DeletionTime]
,a.[Shareholder] = b.[Shareholder]
FROM dbo.VendorApplicationOrgnizations a
left join #VendorApplicationOrgnizations  b
ON a.id=b.id;


INSERT INTO dbo.VendorApplicationOrgnizations
SELECT
 [Id]
,[ApplicationId]
,[VendorName]
,[VendorOldName]
,[VendorEngName]
,[RegCertificateAddress]
,[PostCode]
,[ContactName]
,[ContactPhone]
,[ContactEmail]
,[WebSite]
,[RegisterDate]
,[OrgType]
,[IssuingAuthority]
,[RegisterCode]
,[RegValidityStart]
,[RegValidityEnd]
,[Province]
,[City]
,[Legal]
,[RegisterAmount]
,[BusinessAuthority]
,[BusinessScope]
,[LastYearSales]
,[KeyIndustry]
,[KeyClient]
,[Staffs]
,[Aptitudes]
,[ApplyReason]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[Shareholder]
FROM #VendorApplicationOrgnizations a
WHERE not exists (select * from dbo.VendorApplicationOrgnizations where id=a.id);

--truncate table dbo.VendorApplicationOrgnizations

-- select * from VendorApplicationOrgnizations  vaot where ApplicationId  =UPPER('c9f0b0d6-95f6-472b-b90a-6b78504dba1d')

-- alter table Speaker_Portal.dbo.VendorApplicationOrgnizations alter column [RegCertificateAddress] [nvarchar](300) NOT NULL
-- alter table Speaker_Portal.dbo.VendorApplicationOrgnizations alter column [PostCode] [nvarchar](50) NOT NULL
-- alter table Speaker_Portal.dbo.VendorApplicationOrgnizations alter column [ContactName] [nvarchar](50) NOT NULL
-- alter table Speaker_Portal.dbo.VendorApplicationOrgnizations alter column [ContactPhone] [nvarchar](50) NULL
-- alter table Speaker_Portal.dbo.VendorApplicationOrgnizations alter column [OrgType] [nvarchar](100) NULL