﻿using Abbott.SpeakerPortal.Contracts.Purchase.PurPRApplication;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using Abbott.SpeakerPortal.Entities.VendorOrgnizations;
using System.IO;
using MiniExcelLibs;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Entities.Purchase.PurPOApplication;
using Abbott.SpeakerPortal.AppServices.Common;
using Microsoft.EntityFrameworkCore;
using Abbott.SpeakerPortal.Entities.Common.Attachment;
using Abbott.SpeakerPortal.Contracts.Common.Attachment;
using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Common.Approve;
using Abbott.SpeakerPortal.Contracts.Common.PowerApp;
using Abbott.SpeakerPortal.Enums;
using Abbott.SpeakerPortal.Utils;
using Microsoft.Extensions.DependencyInjection;
using Abbott.SpeakerPortal.Entities.Integration.Dspot;
using Abbott.SpeakerPortal.Contracts.Purchase.PurBDApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurBDApplication;
using Abbott.SpeakerPortal.Entities.Budget;
using Microsoft.Xrm.Sdk.Query;
using Abbott.SpeakerPortal.Dataverse.Contracts;
using Microsoft.Xrm.Sdk;
using Abbott.SpeakerPortal.Entities.VendorFinancials;
using Microsoft.Extensions.Logging;
using static Abbott.SpeakerPortal.Enums.Purchase;
using Microsoft.Xrm.Sdk.Messages;
using static Abbott.SpeakerPortal.Enums.PurPAStatus;
using NetTopologySuite.Mathematics;
using System.Text.Json;
using System.Linq.Dynamic.Core;
using Abbott.SpeakerPortal.Contracts.Approval;
using DocumentFormat.OpenXml.Wordprocessing;
using Abbott.SpeakerPortal.Entities.User;

namespace Abbott.SpeakerPortal.AppServices.Purchase
{
    public partial class PurBDApplicationService : SpeakerPortalAppService, IPurBDApplicationService
    {
        private readonly ILogger<PurBDApplicationService> _logger;

        public PurBDApplicationService(IServiceProvider serviceProvider)
        {
            _logger = serviceProvider.GetService<ILogger<PurBDApplicationService>>();
        }
        /// <summary>
        /// BD申请保存
        /// </summary>
        /// <param name="BDCreateRequest"></param>
        /// <returns></returns>
        public async Task<MessageResult> SavePurBDApplicationAsync(BDCreateOrUpdateRequestDto BDCreateRequest)
        {
            var queryBD = await LazyServiceProvider.LazyGetService<IPurBDApplicationRepository>().GetQueryableAsync();

            PurBDApplication bDApplication;
            //总额
            decimal amount = BDCreateRequest.BDApplicationDetails.Sum(s => s.TotalAmount);
            BDCreateRequest.TotalAmount = amount;
            BDCreateRequest.ApplyUserBu = BDCreateRequest.ApplyUserDept;
            BDCreateRequest.ApplyUserBuName = BDCreateRequest.ApplyUserBuToDeptName;

            if (BDCreateRequest.BDId.HasValue)
            {
                var bd =await queryBD.FirstOrDefaultAsync(a => a.Id == BDCreateRequest.BDId.Value);
                if (bd == null)
                    return MessageResult.FailureResult("修改数据不存在");

                PurBDStatus[] editableStatus = [PurBDStatus.Draft, PurBDStatus.Return, PurBDStatus.Recall];
                if (!editableStatus.Contains(bd.Status))
                    return MessageResult.FailureResult("数据状态不是草稿/退回/撤回，不能修改/提交");

                bDApplication = await UpdatePurBDApplication(BDCreateRequest);
            }
            else
            {
                bDApplication = await CreatePurBDApplication(BDCreateRequest);
            }
            if (bDApplication == null)
                return MessageResult.FailureResult("保存失败，稍后再试");
            return MessageResult.SuccessResult(bDApplication.Id);
        }

        /// <summary>
        /// BD提交
        /// </summary>
        /// <param name="bdCreateRequest"></param>
        /// <returns></returns>
        public async Task<MessageResult> SubmitPurBDApplicationAsync(BDCreateOrUpdateRequestDto bdCreateRequest)
        {
            //提交时做校验
            var checkFields = await CheckCreateRequiredFieldsAsync(bdCreateRequest);
            var PRID = checkFields.Item2;
            if (!checkFields.Item1)
            {
                return MessageResult.FailureResult(checkFields.Item2);
            }
            bdCreateRequest.ApplyTime = DateTime.Now;
            bdCreateRequest.ApplyUserBu = bdCreateRequest.ApplyUserDept;
            bdCreateRequest.ApplyUserBuName = bdCreateRequest.ApplyUserBuToDeptName;
            //总额
            decimal amount = bdCreateRequest.BDApplicationDetails.Sum(s => s.TotalAmount);
            bdCreateRequest.TotalAmount = amount;
            //特殊校验 6个月
            if (bdCreateRequest.SingleChoice == SingleChoice.ComparisonResult)
            {
                var checkPRAndPOMatch = await CheckPRAndBDMatchAsync(bdCreateRequest);
                if (!checkPRAndPOMatch.Item1)
                {
                    return MessageResult.FailureResult(checkPRAndPOMatch.Item2);
                }
            }

            var bdRepository = LazyServiceProvider.LazyGetService<IPurBDApplicationRepository>();
            var queryBD = await bdRepository.GetQueryableAsync();
            if (!string.IsNullOrWhiteSpace(bdCreateRequest.BDApplicationCode))
            {
                var bd = queryBD.FirstOrDefault(a => a.ApplicationCode == bdCreateRequest.BDApplicationCode);
                bdCreateRequest.BDApplicationId = bd?.Id;
            }
            PurBDApplication bdApplication;
            if (bdCreateRequest.BDId.HasValue)
            {
                var bd = await queryBD.FirstOrDefaultAsync(a => a.Id == bdCreateRequest.BDId.Value);
                if (bd == null)
                    return MessageResult.FailureResult("数据不存在");

                PurBDStatus[] editableStatus = [PurBDStatus.Draft, PurBDStatus.Return, PurBDStatus.Recall];
                if (!editableStatus.Contains(bd.Status))
                    return MessageResult.FailureResult("数据状态不是草稿/退回/撤回，不能修改/提交");

                bdApplication = await UpdatePurBDApplication(bdCreateRequest);
            }
            else
            {
                bdApplication = await CreatePurBDApplication(bdCreateRequest);
            }
            if (bdApplication == null)
                return MessageResult.FailureResult("保存失败，稍后再试");

            if (bdApplication.BudgetManagerUserId == Guid.Empty)
                return MessageResult.FailureResult("预算负责人为空，请查证");

            decimal amountRMB = amount * ((decimal)bdApplication.Rate < 1 ? 1M : (decimal)bdApplication.Rate);//小于1默认人民币
            //发起审批流
            var createOK = await CreateBDWorkflowAsync(bdApplication.ApplyUserBu, bdApplication.ApplyUserId, bdApplication.Id, bdApplication.ApplicationCode, bdCreateRequest.SingleChoice == SingleChoice.Null, amountRMB, bdApplication.OtherSupplierExplain);
            if (!createOK)
            {
                //await bdRepository.UpdateAsync(bdApplication);
                return MessageResult.FailureResult("审批任务创建失败");
            }
            bdApplication.Status = PurBDStatus.Approving;//创建成功后更新状态
            await bdRepository.UpdateAsync(bdApplication);

            //PR状态也同步变更发起bidding中
            var prDetailRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>();
            var prDetailQuery = await prDetailRepository.GetQueryableAsync();
            var prDetailIds = bdCreateRequest.PRApplicationDetailId.Split(',').Select(Guid.Parse).OrderBy(a => a).ToList();
            var prDetail = prDetailQuery.Where(w => prDetailIds.Contains(w.Id)).ToList();
            prDetail.ForEach(f =>
            {
                f.OrderStatusFlag = OrderStatusFlag.Bidding;
                f.BiddingId = bdApplication.Id;
            });
            await prDetailRepository.UpdateManyAsync(prDetail);

            return MessageResult.SuccessResult(bdApplication.Id);
        }

        /// <summary>
        /// 获取所有已完成的比价单
        /// </summary>
        /// <param name="request"></param>
        /// <param name="isPaging"></param>
        /// <returns></returns>
        public async Task<PagedResultDto<BDApplicationResponseDto>> GetBDApplicationListAsync(BDApplicationRequestDto request)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var queryableBD = await LazyServiceProvider.LazyGetService<IPurBDApplicationRepository>().GetQueryableAsync();
            var queryBdDetail = await LazyServiceProvider.LazyGetService<IPurBDApplicationDetailsRepository>().GetQueryableAsync();
            var vendorOrg = await LazyServiceProvider.LazyGetService<IVendorOrgnizationRepository>().GetQueryableAsync();
            var vendor = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();
            var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var queryable = queryBdDetail.Join(queryableBD, a => a.BDApplicationId, b => b.Id, (a, b) => new { bdd = a, bd = b })
                .Where(m => m.bd.Status == PurBDStatus.Approved)
                .GroupJoin(queryableUser, a => a.bd.ApplyUserId, b => b.Id, (a, b) => new { BD = a.bd, a.bdd, user = b })
                .SelectMany(a => a.user.DefaultIfEmpty(), (a, b) => new { a.BD, a.bdd, user = b })
                .GroupJoin(vendorOrg, a => a.BD.VendorId, b => b.VendorId, (a, b) => new { a.bdd, a.BD, a.user, Vendor = b })
                .SelectMany(a => a.Vendor.DefaultIfEmpty(), (a, b) => new { a.bdd, a.BD, a.user, VendorName = b.VendorName ?? "" })
                .GroupJoin(vendor, a => a.BD.VendorId, b => b.Id, (a, b) => new { a.bdd, a.BD, a.user, a.VendorName, Vendor = b })
                .SelectMany(a => a.Vendor.DefaultIfEmpty(), (a, b) => new { a.bdd, a.BD, a.user, a.VendorName, Vendor = b })
                .Where(m => m.BD.VendorId == request.VendorId)
                .WhereIf(request.ApprovedDateStart.HasValue, m => m.BD.ApprovedTime >= request.ApprovedDateStart.Value.Date)
                .WhereIf(request.ApprovedDateEnd.HasValue, m => m.BD.ApprovedTime < request.ApprovedDateEnd.Value.Date.AddDays(1));

            var count = await queryable.CountAsync();
            queryable = queryable.Skip(request.PageIndex * request.PageSize).Take(request.PageSize);

            var getBuTask = await dataverseService.GetDivisions();
            var datas = queryable.ToList().Select(s => new BDApplicationResponseDto
            {
                Id = s.BD.Id,
                ApplicationCode = s.BD.ApplicationCode,
                ApplyUserName = s.BD.ApplyUserName,
                ApplyUserBuName = s.BD.ApplyUserBuName,
                VendorName = s.BD.VendorName,
                ApplyTime = s.BD.ApplyTime.Value.ToString("yyyy-MM-dd HH:mm:ss"),
                Status = s.BD.Status,
                TotalAmount = s.BD.TotalAmount,
                ApprovedDate = s.BD.ApprovedTime?.ToString("yyyy-MM-dd"),
                Content = s.bdd.Content,
                Quantity = s.bdd.Quantity,
                RMBUnitPrice = s.bdd.UnitPrice * (decimal)s.BD.Rate,
                RMBTotalAmount = s.bdd.TotalAmount * (decimal)s.BD.Rate,
                Explain = s.BD.Explain,
            }).ToList();
            var result = new PagedResultDto<BDApplicationResponseDto>(count, datas);
            return result;
        }


        /// <summary>
        /// 根据ID获取单据详细信息
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        public async Task<BDInfoReponseDto> GetPurBDApplicationAsync(Guid Id)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var queryableBD = await LazyServiceProvider.LazyGetService<IPurBDApplicationRepository>().GetQueryableAsync();
            var queryableBDdetail = await LazyServiceProvider.LazyGetService<IPurBDApplicationDetailsRepository>().GetQueryableAsync();
            var vendorOrg = await LazyServiceProvider.LazyGetService<IVendorOrgnizationRepository>().GetQueryableAsync();
            var vendor = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();
            var attachmentQuery = await LazyServiceProvider.LazyGetService<IAttachmentRepository>().GetQueryableAsync();
            var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var bpcsGcc = await LazyServiceProvider.LazyGetService<IBpcsGccRepository>().GetQueryableAsync();
            var paymentTermRepository = await LazyServiceProvider.LazyGetService<IBpcsAvtRepository>().GetQueryableAsync();

            var bdData = queryableBD.GroupJoin(vendorOrg.DefaultIfEmpty(), a => a.VendorId, b => b.Id, (a, b) => new { BD = a, vendor = b })
                .SelectMany(m => m.vendor.DefaultIfEmpty(), (a, b) => new { a.BD, VendorName = b.VendorName ?? "" })
                .GroupJoin(queryableUser, (a) => a.BD.ApplyUserId, b => b.Id, (a, b) => new { BD = a.BD, a.VendorName, ApplyUser = b })
                .SelectMany(a => a.ApplyUser.DefaultIfEmpty(), (a, b) => new { BD = a.BD, a.VendorName, ApplyUserName = b.Name })
                .GroupJoin(queryableBDdetail, a => a.BD.Id, b => b.BDApplicationId, (a, b) => new { a.BD, a.VendorName, a.ApplyUserName, BDD = b })
                .FirstOrDefault(m => m.BD.Id == Id);
            var bd = ObjectMapper.Map<PurBDApplication, BDInfoReponseDto>(bdData.BD);
            if (bdData.BDD.Any())
            {
                var bddetais = ObjectMapper.Map<List<PurBDApplicationDetail>, List<BDDetailInfoReponseDto>>(bdData.BDD.ToList());
                bd.BDDetailInfos = bddetais;
            }
            //附件信息
            var AttachmentFileIds = bdData.BD?.AttachmentFile?.Split(',') ?? [];
            if (AttachmentFileIds.Any())
            {
                var attachmentList = attachmentQuery.Where(m => AttachmentFileIds.Contains(m.Id.ToString())).ToList();
                bd.Files = ObjectMapper.Map<List<Attachment>, List<UploadFileResponseDto>>(attachmentList);
                foreach (var file in bd.Files)
                {
                    var size = attachmentList.Where(w => w.Id == file.AttachmentId).Select(s => s.Size).First();
                    file.FileSize = size < 1048576 ? (size / 1024).ToString() + "KB" : (size / 1048576).ToString() + "MB";
                }
            }
            //获取PR明细行信息
            var prdetails = bdData.BD?.PRApplicationDetailId?.Split(',') ?? [];
            if (prdetails.Any())
            {
                var prdids = prdetails.Select(s => Guid.Parse(s)).ToList();
                bd.PRDetails = await GetPRInfo(prdids);
                if (bd.PRDetails.Any(a => !string.IsNullOrWhiteSpace(a.Currency)))//PR币种不为空，说明有币种信息
                    bd.IsHaveCurrency = true;
            }

            //获取BD供应商行信息
            var bdApplicationSupplierDetailsRepository = await LazyServiceProvider.LazyGetService<IPurBDApplicationSupplierDetailsRepository>().GetQueryableAsync();
            var bdSupplierdetails = bdApplicationSupplierDetailsRepository.Where(w => w.BDApplicationId == bd.Id).ToList();
            if (bdSupplierdetails.Any())
            {
                bd.BDSupplierDetailInfos = ObjectMapper.Map<List<PurBDApplicationSupplierDetail>, List<BDSupplierDetailInfoReponseDto>>(bdSupplierdetails);
            }

            //获取PR申请人，申请人为二级审批人
            bd.PRApplyUserId = bd.PRDetails.First().ApplicantId;
            bd.PRApplyName = bd.PRDetails.First().Applicant;
            bd.ApplyUserDept = bd.ApplyUserBu;
            bd.ApplyUserBuToDeptName = bd.ApplyUserBuName;
            bd.ApplyName = bdData.ApplyUserName;
            return bd;
        }

        /// <summary>
        /// 根据比价申请详情获取相应PR信息
        /// </summary>
        /// <param name="guids"></param>
        /// <returns></returns>
        public async Task<List<GetPRDetailListDto>> GetPRInfo(List<Guid> guids)
        {
            //var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var queryablePRdetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var queryablePR = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var vendorOrg = await LazyServiceProvider.LazyGetService<IVendorOrgnizationRepository>().GetQueryableAsync();
            // var vendor = await LazyServiceProvider.LazyGetService<>().GetQueryableAsync();
            var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            //var getBuTask = await dataverseService.GetDivisions();
            var query = queryablePR
           .GroupJoin(queryableUser, (a) => a.ApplyUserId, b => b.Id, (a, b) => new { PR = a, ApplyUser = b })
           .SelectMany(a => a.ApplyUser.DefaultIfEmpty(), (a, b) => new { a.PR, ApplyUser = b })
           .GroupJoin(queryablePRdetail, a => a.PR.Id, b => b.PRApplicationId, (a, b) => new { PR = a.PR, ApplyUser = a.ApplyUser, PRDetail = b })
           .SelectMany(s => s.PRDetail.DefaultIfEmpty(), (a, b) => new { PR = a.PR, ApplyUser = a.ApplyUser, PRDetail = b })
           .Where(m => guids.Contains(m.PRDetail.Id))
           .ToList()
           .Select(s => new GetPRDetailListDto
           {
               Id = s.PRDetail.Id,
               ApplicationCode = s.PR.ApplicationCode,
               ApplicantId = s.PR.ApplyUserId,
               Applicant = s.ApplyUser.Name,
               ApplicationBu = s.PR.ApplyUserBuName,
               ApplyUserId = s.PR.ApplyUserId,
               ApplyUserBu = s.PR.ApplyUserBu,
               ApplyUserName = s.ApplyUser.Name,
               ApplyUserDept = s.PR.ApplyUserDept,
               ApplyUserBuToDeptName = s.PR.ApplyUserDeptName,
               Currency = s.PR.Currency,
               CurrencySymbol = s.PR.CurrencySymbol,
               Rate = s.PR.Rate,
               EstimateDate = s.PRDetail.EstimateDate?.ToString("yyyy-MM-dd"),
               Quantity = s.PRDetail.Quantity,
               Unit = s.PRDetail.Unit,
               UnitPrice = s.PRDetail.UnitPrice,
               TotalAmount = s.PRDetail.TotalAmount,
               TaxRate = s.PRDetail.TaxRate,
               TaxAmounts = s.PRDetail.TaxAmount,
               Content = s.PRDetail.Content,
               VendorId = s.PRDetail.VendorId,
               VendorName = s.PRDetail.VendorName,
               CompanyCode = s.PR.CompanyCode,
               CompanyShortName = s.PR.CompanyShortName,
               PrLateDescription = s.PR.PrLateDescription,
               RowNo = s.PRDetail.RowNo,
               RceNo = s.PRDetail.RceNo,
               CompanyId = s.PR.CompanyId,
               PayMethod = s.PRDetail.PayMethod,
               PRId = s.PR.Id,
           }).ToList();
            return query;
        }

        /// <summary>
        /// 根据比价申请详情获取相应PR信息及预算负责人
        /// </summary>
        /// <param name="guids"></param>
        /// <returns></returns>
        public async Task<MessageResult> GetPRListInfo(List<Guid> guids)
        {
            var queryablePRdetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var queryablePR = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var queryableUser = await LazyServiceProvider.LazyGetService<IRepository<IdentityUser>>().GetQueryableAsync();
            var querySubBudget = await LazyServiceProvider.LazyGetService<IBdSubBudgetRepository>().GetQueryableAsync();
            var queryAttachment = (await LazyServiceProvider.LazyGetService<IAttachmentRepository>().GetQueryableAsync()).AsNoTracking();

            //检查是否是同一个PR单
            var prQuery = queryablePR.Join(queryablePRdetail, a => a.Id, b => b.PRApplicationId, (a, b) => new { PR = a, PRD = b })
                .Where(w => guids.Contains(w.PRD.Id)).ToList();
            if (prQuery.Select(s => s.PR.Id).Distinct().ToList().Count != 1)
                return MessageResult.FailureResult("您所选的采购信息来自不同的采购订单，无法发起比价");
            var userId = querySubBudget.FirstOrDefault(f => f.Id == prQuery.First().PR.SubBudgetId)?.OwnerId;

            var PRDetails = await GetPRInfo(guids);
            PRDetails.ForEach(f => { f.CompanyId = prQuery.First().PR.CompanyId.Value; });
            var result = new PRDetailListResponseDto
            {
                BudgetManagerUserId = userId ?? Guid.Empty,
                BudgetManagerUserName = userId != null ? queryableUser.FirstOrDefault(f => f.Id == userId).Name : "",
                PRDetails = PRDetails,
            };
            #region PR相关附件
            var pr = prQuery.First().PR;
            var attachmentIds = new List<Guid>();
            if (!string.IsNullOrWhiteSpace(pr.SupportFiles))
                attachmentIds.AddRange(pr.SupportFiles.Split(',').ToList().Select(Guid.Parse));
            if (!string.IsNullOrWhiteSpace(pr.AdditionalFiles))
                attachmentIds.AddRange(pr.AdditionalFiles.Split(',').ToList().Select(Guid.Parse));
            if (attachmentIds.Any())
            {
                List<UploadFileResponseDto> prFiles = new List<UploadFileResponseDto>();
                var attachments = queryAttachment.Where(a => attachmentIds.Contains(a.Id)).ToList();
                foreach (var item in attachments)
                {
                    prFiles.Add(new UploadFileResponseDto()
                    {
                        AttachmentId = item.Id,
                        FileName = item.FileName,
                        FilePath = item.FilePath,
                        Success = true
                    });
                }
                result.Files = prFiles;
            }
            #endregion
            if (PRDetails.Any(a => !string.IsNullOrWhiteSpace(a.Currency)))//PR币种不为空，说明有币种信息
                result.IsHaveCurrency = true;
            return MessageResult.SuccessResult(result);
        }

        /// <summary>
        /// 根据公司与供应商获取相应币种及汇率
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<BDCurrencyResponseDto> GetCurrencyAndExchangeRateAsync(string CompanyCode, string VendorCodeBPCS)
        {
            var queryFinance = await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync();
            var avmRepository = await LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync();
            var gccRepository = await LazyServiceProvider.LazyGetService<IBpcsGccRepository>().GetQueryableAsync();
            //根据vendorId查询BPCS的venodrCode
            //var vendorCode = queryFinance.FirstOrDefault(f => f.VendorId == VendorId && f.Company == CompanyCode)?.VendorCode;

            var currencyCode = avmRepository.Where(a => a.Vcmpny.ToString() == CompanyCode && a.Vendor.ToString() == VendorCodeBPCS).FirstOrDefault()?.Vcurr;
            var exchangeRate = gccRepository.Where(a => a.Cctocr == "RMB" && a.Ccfrcr == currencyCode)
                .OrderByDescending(o => o.Ccnvdt).FirstOrDefault()?.Ccnvfc;

            var result = new BDCurrencyResponseDto
            {
                CurrencyCode = currencyCode,
                ExchangeRate = exchangeRate ?? 1
            };
            return result;
        }

        /// <summary>
        /// 比价申请草稿列表
        /// </summary>
        /// <returns></returns>
        public async Task<PagedResultDto<BDListResponseDto>> GetPurBDApplicationDraftListAsync(BDApplicationListRequest request, bool isPaging = true)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var queryableBD = await LazyServiceProvider.LazyGetService<IPurBDApplicationReadonlyRepository>().GetQueryableAsync();
            var vendorOrg = await LazyServiceProvider.LazyGetService<IVendorOrgnizationReadonlyRepository>().GetQueryableAsync();
            var vendor = await LazyServiceProvider.LazyGetService<IVendorReadonlyRepository>().GetQueryableAsync();
            var queryableUser = await LazyServiceProvider.LazyGetService<IIdentityUserReadonlyRepository>().GetQueryableAsync();
            var queryable = queryableBD
                .Where(m => m.Status == PurBDStatus.Draft)
                .Where(m => m.CreatorId == CurrentUser.Id)
                .GroupJoin(queryableUser, a => a.ApplyUserId, b => b.Id, (a, b) => new { BD = a, user = b })
                .SelectMany(a => a.user.DefaultIfEmpty(), (a, b) => new { BD = a.BD, user = b })
                .GroupJoin(vendorOrg, a => a.BD.VendorId, b => b.VendorId, (a, b) => new { a.BD, a.user, Vendor = b })
                .SelectMany(a => a.Vendor.DefaultIfEmpty(), (a, b) => new { a.BD, a.user, VendorName = b.VendorName ?? "" })
                .GroupJoin(vendor, a => a.BD.VendorId, b => b.Id, (a, b) => new { a.BD, a.user, a.VendorName, Vendor = b })
                .SelectMany(a => a.Vendor.DefaultIfEmpty(), (a, b) => new { a.BD, a.user, a.VendorName, Vendor = b })
                .WhereIf(!string.IsNullOrWhiteSpace(request.ApplicationCode), m => m.BD.ApplicationCode.Contains(request.ApplicationCode))
                .WhereIf(!string.IsNullOrWhiteSpace(request.ApplyUserName), m => m.user.Name.Contains(request.ApplyUserName))
                .WhereIf(!string.IsNullOrWhiteSpace(request.VendorName), m => m.BD.VendorName.Contains(request.VendorName))
                .WhereIf(request.ApplyUserBuId.HasValue, m => m.BD.ApplyUserBu == request.ApplyUserBuId)
                .OrderByDescending(o => o.BD.CreationTime).AsQueryable();

            var count = await queryable.CountAsync();
            //判断是否需要分页
            if (isPaging)
            {
                queryable = queryable.Skip(request.PageIndex * request.PageSize).Take(request.PageSize);
            }
            //var getBuTask = await dataverseService.GetDivisions();
            var datas = queryable.ToList().Select(s => new BDListResponseDto
            {
                Id = s.BD.Id,
                ApplicationCode = s.BD.ApplicationCode,
                Status = s.BD.Status,
                ApplyUserName = s.BD.ApplyUserName,
                ApplyUserBuName = s.BD.ApplyUserBuName,
                VendorName = s.BD.VendorName,
                TotalAmount = s.BD.TotalAmount,
            }).ToList();
            var result = new PagedResultDto<BDListResponseDto>(count, datas);
            return result;
        }

        /// <summary>
        /// 比价草稿删除
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> DeleteBDApplicationDraftAsync(List<Guid> request)
        {
            try
            {
                var queryableBD = LazyServiceProvider.LazyGetService<IPurBDApplicationRepository>();
                var entity = await queryableBD.GetQueryableAsync();

                foreach (var item in request)
                {
                    var bdApplication = await entity.FirstOrDefaultAsync(f => f.Id == item);
                    if (bdApplication == null)
                        return MessageResult.FailureResult($"该ID{item}没有相关记录");
                    if (bdApplication.Status != PurBDStatus.Draft)
                        return MessageResult.FailureResult($"该{bdApplication.ApplicationCode}编号已经不属于草稿，不能删除");
                }
                await queryableBD.DeleteManyAsync(request);
                return MessageResult.SuccessResult();
            }
            catch (Exception ex)
            {
                _logger.LogError($"PurBDApplicationService's DeleteSpeakerDraftAsync has an error : {ex.Message}");
                return MessageResult.FailureResult("删除失败");
            }
        }


        /// <summary>
        /// 比价申请列表
        /// </summary>
        /// <returns></returns>
        public async Task<PagedResultDto<BDListResponseDto>> GetPurBDApplicationListAsync(BDApplicationListRequest request, bool isPaging = true)
        {
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var queryableBD = await LazyServiceProvider.LazyGetService<IPurBDApplicationReadonlyRepository>().GetQueryableAsync();
            var vendorOrg = await LazyServiceProvider.LazyGetService<IVendorOrgnizationReadonlyRepository>().GetQueryableAsync();
            var vendor = await LazyServiceProvider.LazyGetService<IVendorReadonlyRepository>().GetQueryableAsync();
            var queryableUser = await LazyServiceProvider.LazyGetService<IIdentityUserReadonlyRepository>().GetQueryableAsync();
            var queryable = queryableBD
                .Where(m => m.Status != PurBDStatus.Draft && m.Status != PurBDStatus.Return && m.Status != PurBDStatus.Recall)
                .GroupJoin(queryableUser, a => a.ApplyUserId, b => b.Id, (a, b) => new { BD = a, user = b })
                .SelectMany(a => a.user.DefaultIfEmpty(), (a, b) => new { BD = a.BD, user = b })
                .GroupJoin(vendorOrg, a => a.BD.VendorId, b => b.VendorId, (a, b) => new { a.BD, a.user, Vendor = b })
                .SelectMany(a => a.Vendor.DefaultIfEmpty(), (a, b) => new { a.BD, a.user, VendorName = b.VendorName ?? "" })
                .GroupJoin(vendor, a => a.BD.VendorId, b => b.Id, (a, b) => new { a.BD, a.user, a.VendorName, Vendor = b })
                .SelectMany(a => a.Vendor.DefaultIfEmpty(), (a, b) => new { a.BD, a.user, a.VendorName, Vendor = b })
                .WhereIf(!string.IsNullOrWhiteSpace(request.ApplicationCode), m => m.BD.ApplicationCode.Contains(request.ApplicationCode))
                .WhereIf(!string.IsNullOrWhiteSpace(request.ApplyUserName), m => m.user.Name.Contains(request.ApplyUserName))
                .WhereIf(request.ApplyUserId != null, m => m.user.Id == request.ApplyUserId)
                .WhereIf(!string.IsNullOrWhiteSpace(request.VendorName), m => m.BD.VendorName.Contains(request.VendorName))
                .WhereIf(request.ApplyUserBuId.HasValue, m => m.BD.ApplyUserBu == request.ApplyUserBuId)
                .WhereIf(request.StartDate.HasValue, m => m.BD.ApplyTime >= request.StartDate)
                .WhereIf(request.EndDate.HasValue, m => m.BD.ApplyTime <= request.EndDate)
                .WhereIf(request.Status.HasValue, m => m.BD.Status == request.Status)
                .WhereIf(!string.IsNullOrWhiteSpace(request.ApplicationDepartment), m => m.BD.ApplyUserBuName.Contains(request.ApplicationDepartment))
                .OrderByDescending(o => o.BD.ApplyTime).AsQueryable();

            var count = await queryable.CountAsync();
            //判断是否需要分页
            if (isPaging)
            {
                queryable = queryable.Skip(request.PageIndex * request.PageSize).Take(request.PageSize);
            }
            //var getBuTask = await dataverseService.GetDivisions();
            var datas = queryable.ToList().Select(s => new BDListResponseDto
            {
                Id = s.BD.Id,
                ApplicationCode = s.BD.ApplicationCode,
                Status = s.BD.Status,
                ApplyUserName = s.BD.ApplyUserName,
                ApplyTime = s.BD.ApplyTime.Value.ToString("yyyy-MM-dd HH:mm:ss"),
                ApplyUserBuName = s.BD.ApplyUserBuName,
                VendorName = s.BD.VendorName,
                TotalAmount = s.BD.TotalAmount,
            }).ToList();
            var result = new PagedResultDto<BDListResponseDto>(count, datas);
            return result;
        }

        /// <summary>
        /// 导出比价申请列表
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        public async Task<Stream> ExportBDApplication(BDApplicationListRequest request)
        {
            using MemoryStream stream = new();
            List<object> list = [];
            list.AddRange((await GetPurBDApplicationListAsync(request, false)).Items);
            stream.SaveAs(list, true, "Sheet1");
            stream.Seek(0, SeekOrigin.Begin);
            return stream;
        }

        /// <summary>
        /// BD 作废
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<MessageResult> CancellationAsync(Guid id)
        {
            var approveService = LazyServiceProvider.LazyGetService<IApproveService>();
            var bdApplication = LazyServiceProvider.LazyGetService<IPurBDApplicationRepository>();
            var query = await bdApplication.GetQueryableAsync();
            var queryBdDetail = await LazyServiceProvider.LazyGetService<IPurBDApplicationDetailsRepository>().GetQueryableAsync();
            var queryPo = await LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>().GetQueryableAsync();
            var queryPoDetail = await LazyServiceProvider.LazyGetService<IPurPOApplicationDetailsRepository>().GetQueryableAsync();
            var bd = query.Where(a => a.Id == id).FirstOrDefault();
            var bdDetails = queryBdDetail.Where(a => a.BDApplicationId == id).ToList();
            if (bd == null)
                return MessageResult.FailureResult("未查询到Bidding");
            var pos = queryPo.GroupJoin(queryPoDetail, a => a.Id, b => b.POApplicationId, (a, b) => new { po = a, pods = b })
                .SelectMany(a => a.pods.DefaultIfEmpty(), (a, b) => new { a.po, pod = b })
                .Where(a => bdDetails.Select(x => x.PRDetailId).Contains(a.pod.PRDetailId))
                .ToList();
            PurOrderStatus[] poStatus = [PurOrderStatus.Invalid, PurOrderStatus.Rejected];
            foreach (var po in pos)
            {
                if (!poStatus.Contains(po.po.Status))
                    return MessageResult.FailureResult("已发起PO且PO不是作废状态，该BD不能作废");
            }
            //状态 
            PurBDStatus[] pending = [PurBDStatus.Return, PurBDStatus.Recall];//待处理
            PurBDStatus[] completed = [PurBDStatus.Rejected, PurBDStatus.Closed, PurBDStatus.Invalid, PurBDStatus.Approved];//已完成
            if (pending.Contains(bd.Status))
            {
                var updateApproval = new List<UpdateApprovalDto>() {
                    new UpdateApprovalDto() {
                        BusinessFormId = bd.Id.ToString(),
                        OperationStatus = ApprovalOperation.Delete,
                        Submitter = CurrentUser.Id.Value,
                        Remark = "作废（解绑）",
                    }
                };
                return await approveService.SubmitterOperationAsync(updateApproval);
            }
            else if (completed.Contains(bd.Status))
            {
                bd.Status = PurBDStatus.Invalid;
                await bdApplication.UpdateAsync(bd);
                var prDetailRepository = LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>();
                var prDetailQuery = await prDetailRepository.GetQueryableAsync();
                var prDetailIds = bd.PRApplicationDetailId.Split(',').Select(Guid.Parse).OrderBy(a => a).ToList();
                var prDetail = prDetailQuery.Where(w => prDetailIds.Contains(w.Id)).ToList();
                prDetail.ForEach(f =>
                {
                    f.BiddingId = null;//PR解绑Bidding
                    OrderStatusFlag[] orderStatusFlags = [OrderStatusFlag.Bidding, OrderStatusFlag.BiddingCompleted];//当前不在Bidding了就不更新状态了
                    if (f.OrderStatusFlag != null && orderStatusFlags.Contains(f.OrderStatusFlag.Value))
                        f.OrderStatusFlag = null;
                });
                await prDetailRepository.UpdateManyAsync(prDetail);
                var addApproval = new AddApprovalRecordDto
                {
                    FormId = bd.Id,
                    ApprovalId = CurrentUser.Id.Value,
                    OriginalApprovalId = bd.ApplyUserId,//申请人Id
                    Status = ApprovalOperation.Delete,
                    Remark = "作废（解绑）",
                    ApprovalTime = DateTime.Now,
                    WorkStep = "提交人操作",
                    Name = "作废"
                };
                await approveService.AddApprovalRecordAsync(addApproval);
            }
            else
            {
                return MessageResult.FailureResult("当前状态不能作废");
            }
            return MessageResult.SuccessResult();
        }

        #region 私有方法
        /// <summary>
        /// 创建BD数据
        /// </summary>
        /// <param name="poCreate"></param>
        /// <returns></returns>
        private async Task<PurBDApplication> CreatePurBDApplication(BDCreateOrUpdateRequestDto bdCreate)
        {
            var bdApplicationRepository = LazyServiceProvider.LazyGetService<IPurBDApplicationRepository>();
            var bdApplicationDetailsRepository = LazyServiceProvider.LazyGetService<IPurBDApplicationDetailsRepository>();
            var bdApplicationSupplierDetailsRepository = LazyServiceProvider.LazyGetService<IPurBDApplicationSupplierDetailsRepository>();
            var prDetailApplication = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();

            try
            {
                var bdApplication = ObjectMapper.Map<BDCreateOrUpdateRequestDto, PurBDApplication>(bdCreate);
                bdApplication.Status = PurBDStatus.Draft;//保存都是草稿
                bdApplication.ApplyUserId = bdCreate.ApplyUserId;
                bdApplication.ApplyUserName = bdCreate.ApplyUserName;
                bdApplication.ApplyUserBu = bdCreate.ApplyUserBu;
                bdApplication.ApplyUserBuName = bdCreate.ApplyUserBuName;

                //获取
                var firstDetail = Guid.Parse(bdApplication.PRApplicationDetailId.Split(',')[0]);
                bdApplication.PRApplicationId = prDetailApplication.FirstOrDefault(f => f.Id == firstDetail)?.PRApplicationId;
                if (bdCreate.Files != null)
                {
                    bdApplication.AttachmentFile = bdCreate.Files.Any() ? string.Join(",", bdCreate.Files.Select(a => a.AttachmentId.ToString()).ToList()) : "";
                }

                await InsertAndGenerateSerialNoAsync(bdApplicationRepository, bdApplication, "B");

                //添加比价详情
                var bdApplicationDetails = ObjectMapper.Map<List<BDCreateDetailRequestDto>, List<PurBDApplicationDetail>>(bdCreate.BDApplicationDetails);
                bdApplicationDetails.ForEach(a =>
                {
                    a.BDApplicationId = bdApplication.Id;
                });
                await bdApplicationDetailsRepository.InsertManyAsync(bdApplicationDetails, true);
                //添加供应商详情
                var bdApplicationSupplierDetails = ObjectMapper.Map<List<BDCreateSupplierDetailRequestDto>, List<PurBDApplicationSupplierDetail>>(bdCreate.BDApplicationSupplierDetails);
                bdApplicationSupplierDetails.ForEach(a =>
                {
                    a.BDApplicationId = bdApplication.Id;
                });
                await bdApplicationSupplierDetailsRepository.InsertManyAsync(bdApplicationSupplierDetails, true);
                return bdApplication;
            }
            catch (Exception ex)
            {
                _logger.LogError($"PurBDApplicationService's CreatePurBDApplication has an error : {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 更新BD数据
        /// </summary>
        /// <param name="poCreate"></param>
        /// <returns></returns>
        private async Task<PurBDApplication> UpdatePurBDApplication(BDCreateOrUpdateRequestDto bdUpdate)
        {
            var bdApplicationRepository = LazyServiceProvider.LazyGetService<IPurBDApplicationRepository>();
            var bdApplicationDetailsRepository = LazyServiceProvider.LazyGetService<IPurBDApplicationDetailsRepository>();
            var bdApplicationSupplierDetailsRepository = LazyServiceProvider.LazyGetService<IPurBDApplicationSupplierDetailsRepository>();
            var queryBD = await bdApplicationRepository.GetQueryableAsync();

            try
            {
                //更新
                var bd = queryBD.Where(a => a.Id == bdUpdate.BDId.Value).FirstOrDefault();
                ObjectMapper.Map(bdUpdate, bd);
                //申请人提交时，才修改发起人和发起部门
                if (bd.ApplyUserId == CurrentUser.Id)
                {
                    bd.ApplyUserBu = bdUpdate.ApplyUserBu;
                    bd.ApplyUserBuName = bdUpdate.ApplyUserBuName;
                }

                if (bdUpdate.Files != null)
                {
                    bd.AttachmentFile = bdUpdate.Files.Any() ? string.Join(",", bdUpdate.Files.Select(a => a.AttachmentId.ToString()).ToList()) : "";
                }
                var updateResult = await bdApplicationRepository.UpdateAsync(bd, true);
                if (updateResult != null)
                {
                    //添加比价详情
                    await bdApplicationDetailsRepository.DeleteDirectAsync(a => a.BDApplicationId == updateResult.Id);
                    var bdApplicationDetails = ObjectMapper.Map<List<BDCreateDetailRequestDto>, List<PurBDApplicationDetail>>(bdUpdate.BDApplicationDetails);
                    bdApplicationDetails.ForEach(a =>
                    {
                        a.BDApplicationId = updateResult.Id;
                    });
                    await bdApplicationDetailsRepository.InsertManyAsync(bdApplicationDetails, true);
                    //添加供应商详情
                    await bdApplicationSupplierDetailsRepository.DeleteDirectAsync(a => a.BDApplicationId == updateResult.Id);
                    var bdApplicationSupplierDetails = ObjectMapper.Map<List<BDCreateSupplierDetailRequestDto>, List<PurBDApplicationSupplierDetail>>(bdUpdate.BDApplicationSupplierDetails);
                    bdApplicationSupplierDetails.ForEach(a =>
                    {
                        a.BDApplicationId = updateResult.Id;
                    });
                    await bdApplicationSupplierDetailsRepository.InsertManyAsync(bdApplicationSupplierDetails, true);
                }
                return updateResult;
            }
            catch (Exception ex)
            {
                _logger.LogError($"PurBDApplicationService's UpdatePurBDApplication has an error : {ex.Message}");
                return null;
            }
        }


        /// <summary>
        /// BD添加审批任务
        /// </summary>
        /// <param name="applyUserBu"></param>
        /// <param name="applyUserId"></param>
        /// <param name="draftId"></param>
        /// <param name="totalAmount"></param>
        /// <returns></returns>
        private async Task<bool> CreateBDWorkflowAsync(Guid applyUserBu, Guid applyUserId, Guid draftId, string draftCode, bool isFull, decimal totalAmountRMB, string remark)
        {
            var approvalRecord = LazyServiceProvider.LazyGetService<IInitializeApprovalRecordService>();
            var dataverseService = LazyServiceProvider.LazyGetService<IDataverseService>();
            var rate = dataverseService.GetCurrencyConfig(DictionaryType.CurrencyItems.USD).Result
               .FirstOrDefault(a => a.CurrencyCode.Equals(DictionaryType.CurrencyItems.USD, StringComparison.OrdinalIgnoreCase));
            //创建审批任务
            var exemptType = EnumUtil.GetEnumIdValues<WorkflowTypeName>().ToDictionary(a => (WorkflowTypeName)a.Key, a => a.Value);
            var name = exemptType[isFull ? WorkflowTypeName.ComparePricesToBuy : WorkflowTypeName.ComparePricesToBuySpecialType];
            var createApproval = new CreateApprovalDto
            {
                Name = name,
                Department = applyUserBu.ToString(),
                BusinessFormId = draftId.ToString(),
                BusinessFormNo = draftCode,
                BusinessFormName = EntitiesNameConsts.NameConsts.BDApplication,
                Submitter = CurrentUser.Id?.ToString(),
                OriginalApprovalId = applyUserId,
                FormData = JsonSerializer.Serialize(new { PriceComparisonAmount = totalAmountRMB / rate.PlanRate }),
                WorkflowType = isFull ? WorkflowTypeName.ComparePricesToBuy : WorkflowTypeName.ComparePricesToBuySpecialType,
                InstanceName = $"{name}-{DateTime.Now.ToString("yyMMdd-hhmmss")}",
                Remark = remark
            };

            await approvalRecord.InsertInitApprovalRecordAsync(new InitializeApprovalRecordDto
            {
                BusinessFormName = EntitiesNameConsts.NameConsts.BDApplication,
                BusinessId = draftId,
                InstanceId = Guid.NewGuid(),
                Status = InitApprovalRecordStatus.Pending,
                MaxRetries = 3,
                RequestContent = JsonSerializer.Serialize(createApproval)
            });
            return true;
        }

        /// <summary>
        /// 提交时必填字段校验
        /// </summary>
        /// <param name="pOCreateRequest"></param>
        /// <returns>(bool,string)=》1、验证结果状态；2、验证结果Message</returns>
        private async Task<(bool, string)> CheckCreateRequiredFieldsAsync(BDCreateOrUpdateRequestDto bdCreateRequest)
        {
            var queryVendor = await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync();
            var queryPR = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
            var queryPRDetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            var queryBdDetail = await LazyServiceProvider.LazyGetService<IPurBDApplicationDetailsRepository>().GetQueryableAsync();
            var queryPO = await LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>().GetQueryableAsync();
            var queryPODetail = await LazyServiceProvider.LazyGetService<IPurPOApplicationDetailsRepository>().GetQueryableAsync();
            var bdRepository = await LazyServiceProvider.LazyGetService<IPurBDApplicationRepository>().GetQueryableAsync();

            if (!CheckIdIsGuid(bdCreateRequest.PRApplicationDetailId))
            {
                return (false, "PR信息有误");
            }
            if (bdCreateRequest.BDApplicationDetails.Any() && bdCreateRequest.PRCorrespond == CorrespondType.PRCorrespond)
            {
                foreach (var item in bdCreateRequest.BDApplicationDetails)
                {
                    if (!item.PRDetailId.HasValue)
                        return (false, "未关联PR明细行");
                }
            }
            var prDetailIds = bdCreateRequest.PRApplicationDetailId.Split(',').Select(Guid.Parse).OrderBy(a => a).ToList();
            //检查是否是同一个PR单
            var prQuery = queryPR.Join(queryPRDetail, a => a.Id, b => b.PRApplicationId, (a, b) => new { PR = a, PRD = b })
                .Where(w => prDetailIds.Contains(w.PRD.Id))
                .GroupJoin(queryBdDetail.Where(a => a.BDApplicationId == bdCreateRequest.BDId), a => a.PRD.Id, a => a.PRDetailId, (a, b) => new { a.PR, a.PRD, BdDetails = b })
                .SelectMany(a => a.BdDetails.DefaultIfEmpty(), (a, b) => new { a.PR, a.PRD, BdDetail = b })
                .ToList();
            if (prQuery.Select(s => s.PR.Id).Distinct().ToList().Count != 1)
                return (false, "您所选的采购信息来自不同的采购订单，无法发起比价");

            var pos = queryPO.Select(a => new { a.Id, a.Status })
                .GroupJoin(queryPODetail.Select(a => new { a.Id, a.PRDetailId, a.POApplicationId }), a => a.Id, b => b.POApplicationId, (a, b) => new { po = a, pods = b })
                .SelectMany(a => a.pods.DefaultIfEmpty(), (a, b) => new { a.po, pod = b })
                .Where(a => a.pod.PRDetailId.HasValue && prDetailIds.Contains(a.pod.PRDetailId.Value))
                .Where(a => a.po.Status != PurOrderStatus.Invalid && a.po.Status != PurOrderStatus.Draft && a.po.Status != PurOrderStatus.Rejected)
                .ToList();
            if (pos.Any())
                return (false, "您所选的采购信息中包含已发起采购订单（PO），不能提交比价单");

            //检查状态是否可以发起比价
            //if (prQuery.Any(w => w.PRD.OrderStatusFlag != null && w.PRD.OrderStatusFlag != OrderStatusFlag.BiddingReject && w.PRD.OrderStatusFlag != OrderStatusFlag.OrderReject))
            //{
            //    var bds = queryBdDetail.GroupJoin(bdRepository, a => a.BDApplicationId, b => b.Id, (a, b) => new { bdd = a, bds = b })
            //        .SelectMany(a => a.bds.DefaultIfEmpty(), (a, b) => new { a.bdd, bd = b })
            //        .Where(a=> a.bdd.PRDetailId.HasValue && prDetailIds.Contains(a.bdd.PRDetailId.Value))
            //        .ToList();
            //    var bd = bds.FirstOrDefault();
            //    foreach (var item in bds)
            //    {
            //        if (bd.bd.Id != item.bd.Id)
            //            return (false, "您所选的采购信息中包含已发起的比价或采购订单，不能重复发起");
            //    }
            //}
            //检查状态是否可以发起比价
            //if (prQuery.Any(w => w.PRD.OrderStatusFlag != null && w.PRD.OrderStatusFlag != OrderStatusFlag.BiddingReject && w.PRD.OrderStatusFlag != OrderStatusFlag.OrderReject))
            //    return (false, "您所选的采购信息中包含已发起的比价或采购订单，不能重复发起");

            if (bdCreateRequest.BDId != null)
            {
                var entity = bdRepository.FirstOrDefault(g => g.Id == bdCreateRequest.BDId);
                if (entity != null)
                {
                    if (entity.Status != PurBDStatus.Return && entity.Status != PurBDStatus.Recall && entity.Status != PurBDStatus.Draft)
                        return (false, "当前比价单状态无法重新提交比价");
                }
                else
                {
                    return (false, "未查询相关数据");
                }
            }
            else
            {
                //前面bdCreateRequest.BDId 已经验证 重新提交 ,此处校验是否有发起过BD（作废和拒绝的可以重新发起）
                PurBDStatus[] bdStatuses = [PurBDStatus.Invalid, PurBDStatus.Rejected];
                var bds = queryBdDetail.GroupJoin(bdRepository, a => a.BDApplicationId, b => b.Id, (a, b) => new { bdd = a, bds = b })
                        .SelectMany(a => a.bds.DefaultIfEmpty(), (a, b) => new { a.bdd, bd = b })
                        .Where(a => a.bdd.PRDetailId.HasValue && prDetailIds.Contains(a.bdd.PRDetailId.Value))
                        .Where(a => !bdStatuses.Contains(a.bd.Status))
                        .ToList();
                if (bds.Any())
                    return (false, "您所选的采购信息中包含已发起的比价，不能重复发起");
            }

            return (true, prQuery.First().PR.Id.ToString());
        }
        /// <summary>
        /// 检查总金额
        /// </summary>
        /// <param name="pOCreateRequest"></param>
        /// <returns>(bool,string)=》1、验证结果状态；2、验证结果Message</returns>
        private async Task<(bool, string)> CheckPRAndBDMatchAsync(BDCreateOrUpdateRequestDto bdCreateRequest)
        {
            var queryable = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
            //var prDetailIds = pOCreateRequest.PRApplicationDetailId.Split(',').Select(Guid.Parse).ToList();
            //var prDetails = queryable.Where(a => prDetailIds.Contains(a.Id)).ToList();
            var bdDetails = bdCreateRequest.BDApplicationDetails;
            decimal amount = bdDetails.Sum(s => s.TotalAmount);

            //获取汇率
            if (string.IsNullOrEmpty(bdCreateRequest.CompanyCode))
                return (false, "公司Code必填");

            var currency = await GetCurrencyAndExchangeRateAsync(bdCreateRequest.CompanyCode, bdCreateRequest.VendorCode);
            var amountRMB = amount * currency.ExchangeRate;
            //获取货币信息配置
            var _dataverseRepository = LazyServiceProvider.GetService<IDataverseRepository>();
            var queryExt = new QueryExpression("spk_currencyconfig");
            queryExt.ColumnSet.AddColumns("spk_currency", "spk_rmbplanrate", "spk_currencysymbol");
            var entitieExt = await _dataverseRepository.DataverseClient.GetAllEntitiesAsync(queryExt);
            var entity = entitieExt.Select(x => new
            {
                currencyId = x.GetAttributeValue<EntityReference>("spk_currency").Id,
                currencyName = x.GetAttributeValue<EntityReference>("spk_currency").Name,
                rmbplanrate = x.GetAttributeValue<decimal>("spk_rmbplanrate"),
                currencysymbol = x.GetAttributeValue<string>("spk_currencysymbol"),
            });
            var rmbplanrate = entity.Where(w => w.currencyName == "USD").FirstOrDefault()?.rmbplanrate ?? 0;
            var currencysymbol = entity.Where(w => w.currencyName == "USD").FirstOrDefault()?.currencysymbol ?? "";

            if ((amountRMB / rmbplanrate) > 50000 && bdCreateRequest.BDApplicationSupplierDetails.Count < 3 && string.IsNullOrEmpty(bdCreateRequest.Explain))
            {
                return (false, $"采购超过{currencysymbol}50,000且其他供应商信息小于两家请输入说明");
            }
            //bdCreateRequest.TotalAmount = amount;
            return (true, "Success");
        }
        /// <summary>
        /// 检查Id是否是GUID
        /// </summary>
        /// <param name="detailIds">逗号隔开的Guid</param>
        /// <returns></returns>
        private bool CheckIdIsGuid(string detailIds)
        {
            var ids = detailIds.Split(',');
            // 使用 LINQ 查询所有字符串是否都能成功转换为 Guid
            return !ids.Any(id => !Guid.TryParse(id, out _));
        }
        #endregion

        /// <summary>
        ///追加附件
        /// </summary>
        /// <param name="Id"></param>
        /// <param name="uploads"></param>
        /// <returns></returns>
        public async Task AppendAttachment(UpdateBusinessAttachmentDto attachmentDto)
        {
            var POApplication = LazyServiceProvider.GetService<IPurPOApplicationRepository>();
            var query = await POApplication.GetQueryableAsync();
            var attachmentids = attachmentDto.uploads.Select(a => a.AttachmentId.ToString()).JoinAsString(",");
            var po = await POApplication.FirstOrDefaultAsync(a => a.Id == attachmentDto.Id);
            po.AttachmentFile = attachmentids;
            await POApplication.UpdateAsync(po);
            //var res = POApplication.UpdateManyAsync(entities, true);
        }

        /// <summary>
        /// 打印生成BD
        /// </summary>
        /// <returns></returns>
        //public async Task<Stream> GenerateBDAsync(Guid Id)
        //{

        //    var PORepository = LazyServiceProvider.LazyGetService<IPurPOApplicationRepository>();
        //    var queryablePo = await PORepository.GetQueryableAsync();

        //    var PddRepository = LazyServiceProvider.LazyGetService<IPurPOApplicationDetailsRepository>();
        //    var queryablePod = await PddRepository.GetQueryableAsync();

        //    var queryablePR = await LazyServiceProvider.LazyGetService<IPurPRApplicationRepository>().GetQueryableAsync();
        //    var queryablePRdetail = await LazyServiceProvider.LazyGetService<IPurPRApplicationDetailRepository>().GetQueryableAsync();
        //    var commonService = LazyServiceProvider.LazyGetService<ICommonService>();
        //    var approveService = LazyServiceProvider.LazyGetService<IApproveService>();
        //    var po = queryablePo.First(s => s.Id == Id);
        //    var PodDatas = queryablePod.AsNoTracking().Where(po => po.POApplicationId == Id)
        //        .GroupJoin(queryablePRdetail, a => a.PRDetailId, b => b.Id, (a, b) => new { pod = a, prd = b })
        //        .SelectMany(a => a.prd.DefaultIfEmpty(), (a, b) => new { pod = a.pod, prd = b })
        //        .GroupJoin(queryablePR, a => a.prd.PRApplicationId, b => b.Id, (a, b) => new { pod = a.pod, prd = a.prd, pr = b })
        //        .SelectMany(a => a.pr.DefaultIfEmpty(), (a, b) => new { pod = a.pod, PrdRceNo = a.prd.RceNo, PrNo = b.ApplicationCode })
        //        ;
        //    //查找审批记录
        //    var approvalRecords = await approveService.GetApprovalRecordAsync(Id);
        //    var approvalData = (List<ApprovalRecordDto>)approvalRecords.Data;

        //    var diclist = new List<Dictionary<string, object>>();
        //    //填充PDF
        //    if (PodDatas?.Count() > 0)
        //    {
        //        foreach (var item in PodDatas)
        //        {
        //            var subDic = new Dictionary<string, object>
        //            {
        //                ["Project"] = item.pod.ApplicationCode,
        //                ["Number"] = item.pod.Quantity,
        //                ["Description"] = item.pod.Content,
        //                ["Uom"] = item.pod.Unit,
        //                ["Price"] = item.pod.Price,
        //                ["Amount"] = item.pod.TotalAmount,
        //            };
        //            diclist.Add(subDic);
        //        }
        //    }
        //    else
        //    {
        //        var subDic = new Dictionary<string, object>
        //        {
        //            ["Project"] = "",
        //            ["Number"] = "",
        //            ["Description"] = "",
        //            ["Uom"] = "",
        //            ["Price"] = "",
        //            ["Amount"] = "",
        //        };
        //        diclist.Add(subDic);
        //    }
        //    var tabDics = new Dictionary<string, List<Dictionary<string, object>>>() { { "Podetails", diclist } };
        //    //组装数据
        //    var dics = new Dictionary<string, object>
        //    {
        //        {"VendorName", po.VendorName },
        //        {"PoNo", po.ApplicationCode },
        //        {"Address1", po.RegCertificateAddress },
        //        {"Contact", po.ContactName },
        //        {"Tel", po.ContactPhone },
        //        {"Fax", po.FaxNumber },
        //        {"PrNo", PodDatas.FirstOrDefault()?.PrNo??"" },
        //        {"RCENo", "" },
        //        {"Date", po.ApplyTime.ToString("yyyy-MM-dd")},
        //        {"Tel2", po.PhoneNumber },
        //        {"InvoiceTitle", po.InvoiceTitle },
        //        {"Address2", po.InvoiceAddress },
        //        {"Fax2", po.InvoiceFax },
        //        {"Special",po.AttentionNote },
        //        {"ShippingMode",po.DeliveryType },
        //        {"DeliveryTo",po.DeliveryAddress },
        //        {"PaymentTerms",po.PaymentTerm },
        //        {"DeliveryDate",po.DeliveryDate.ToString("yyyy-MM-dd")},
        //        {"Quality",po.Qualitystandard },
        //        {"Others",po.Others },
        //        {"Currency",po.Currency },
        //        {"Freight","" },
        //        {"Total",$"{po.CurrencySymbol}{po.TotalAmount}"},
        //        {"Approved5","" },
        //        {"Approved4","" },
        //        {"Approved3","" },
        //        {"Approved2","" },
        //        {"Approved1","" },
        //    };
        //    var i = 5;
        //    foreach (var item in approvalData)
        //    {
        //        string key = $"Approved{i}";
        //        dics[key] = item.UserName;
        //        i--;
        //    }
        //    var pdfStream = await commonService.GeneratePDFAsync(dics, "POTemplate.docx", tabDics, "POTemplateTK.pdf");
        //    using FileStream fileStream = new FileStream(@"C:\Users\<USER>\Desktop\test.pdf", FileMode.Create);
        //    //pdfStream.Seek(0, SeekOrigin.Begin);
        //    //pdfStream.CopyTo(fileStream);
        //    po.Status = PurOrderStatus.InitiateReceipt;
        //    await PORepository.UpdateAsync(po);
        //    return pdfStream;
        //}
    }
}
