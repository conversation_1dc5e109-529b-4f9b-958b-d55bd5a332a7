SELECT	
 TRY_CONVERT(UNIQUEIDENTIFIER, [Id]) [Id]
,TRY_CONVERT(UNIQUEIDENTIFIER, iif([VendorId] is NULL,'********-0000-0000-0000-************',[VendorId])) [VendorId]
,Company AS [Company]
,Currency AS [Currency]
,VendorCode AS [VendorCode]
,AbbottBank AS [AbbottBank]
,VendorType AS [VendorType]
,Division AS [Division]
,PayType AS [PayType]
,CountryCode AS [CountryCode]
,BankType AS [BankType]
,[DpoCategory]
,[PaymentTerm]
,[BankNo]
,'{}' AS [ExtraProperties]
,'NULL' AS [ConcurrencyStamp]
,GETDATE() AS [CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CreatorId]) [CreatorId]
,GETDATE() AS [LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [LastModifierId]) [LastModifierId]
,0 AS [IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, [DeleterId]) [DeleterId]
,GETDATE() AS [DeletionTime]
,[SpendingCategory]
,GETDATE() AS [BpcsCreationTime]
,[BpcsVmid]
,[BpcsVnstat]
,0 AS [FinancialVendorStatus]
INTO #VendorFinancials
FROM (SELECT DISTINCT *,ROW_NUMBER() OVER(PARTITION BY Id ORDER BY Id) RK FROM PLATFORM_ABBOTT_STG.dbo.[VendorFinancials])a
WHERE RK = 1;


--select max(len(Company)) from PLATFORM_ABBOTT_Stg.dbo.VendorFinancials
--
--ALTER TABLE Speaker_Portal_Stg.dbo.VendorFinancials ALTER COLUMN Company nvarchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL;
--
--
--drop table #VendorFinancials;

USE Speaker_Portal_STG;

UPDATE a
SET
 a.[Id] = b.[Id]
,a.[VendorId] = b.[VendorId]
,a.[Company] = b.[Company]
,a.[Currency] = b.[Currency]
,a.[VendorCode] = b.[VendorCode]
,a.[AbbottBank] = b.[AbbottBank]
,a.[VendorType] = b.[VendorType]
,a.[Division] = b.[Division]
,a.[PayType] = b.[PayType]
,a.[CountryCode] = b.[CountryCode]
,a.[BankType] = b.[BankType]
,a.[DpoCategory] = b.[DpoCategory]
,a.[PaymentTerm] = b.[PaymentTerm]
,a.[BankNo] = b.[BankNo]
,a.[ExtraProperties] = b.[ExtraProperties]
,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = b.[CreatorId]
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = b.[LastModifierId]
,a.[IsDeleted] = b.[IsDeleted]
,a.[DeleterId] = b.[DeleterId]
,a.[DeletionTime] = b.[DeletionTime]
,a.[SpendingCategory] = b.[SpendingCategory]
,a.[BpcsCreationTime] = b.[BpcsCreationTime]
,a.[BpcsVmid] = b.[BpcsVmid]
,a.[BpcsVnstat] = b.[BpcsVnstat]
,a.[FinancialVendorStatus] = b.[FinancialVendorStatus]
FROM dbo.VendorFinancials a
left join #VendorFinancials  b
ON a.id=b.id;


INSERT INTO dbo.VendorFinancials
SELECT
 [Id]
,[VendorId]
,[Company]
,[Currency]
,[VendorCode]
,[AbbottBank]
,[VendorType]
,[Division]
,[PayType]
,[CountryCode]
,[BankType]
,[DpoCategory]
,[PaymentTerm]
,[BankNo]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[SpendingCategory]
,[BpcsCreationTime]
,[BpcsVmid]
,[BpcsVnstat]
,[FinancialVendorStatus]
FROM #VendorFinancials a
WHERE not exists (select * from dbo.VendorFinancials where id=a.id);

--truncate table dbo.VendorFinancials