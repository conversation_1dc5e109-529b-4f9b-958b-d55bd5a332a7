select newid() as spk_NexBPMCode,a.spk_BPMCode,a.spk_name,a.spk_englishname,a.spk_number,iif(a.spk_isspecial=0,N'是',N'否') as spk_isspecial,iif(a.spk_isvendortype=0,N'是',N'否') as spk_isvendortype
,iif(a.spk_isassets=0,N'是',N'否') as spk_isassets
,iif(a.spk_isconferencefees=0,N'是',N'否') as  spk_isconferencefees,iif(a.spk_isdspot=0,'Yes','No') as spk_isdspot,a.spk_precode,b.spk_NexBPMCode as spk_organizational
,a.flg 
into #spk_consume
from spk_consume_Tmp a
LEFT JOIN spk_organizationalmasterdata b on a.spk_organizational=b.spk_bpmcode

IF OBJECT_ID(N'dbo.spk_consume', N'U') IS NOT NULL
BEGIN
	update a 
	set a.spk_BPMCode           = b.spk_BPMCode
       ,a.spk_name              = b.spk_name
       ,a.spk_englishname       = b.spk_englishname
       ,a.spk_number            = b.spk_number
       ,a.spk_isspecial         = b.spk_isspecial
       ,a.spk_isvendortype      = b.spk_isvendortype
       ,a.spk_isassets          = b.spk_isassets
       ,a.spk_isconferencefees  = b.spk_isconferencefees
       ,a.spk_isdspot           = b.spk_isdspot
       ,a.spk_precode           = b.spk_precode
       ,a.spk_organizational    = b.spk_organizational
       ,a.flg                   = b.flg 
    from dbo.spk_consume a
    join #spk_consume b on a.spk_BPMCode = b.spk_BPMCode
    
    insert into dbo.spk_consume
     select  a.spk_NexBPMCode
        ,a.spk_BPMCode
        ,a.spk_name
        ,a.spk_englishname
        ,a.spk_number
        ,a.spk_isspecial
        ,a.spk_isvendortype
        ,a.spk_isassets
        ,a.spk_isconferencefees
        ,a.spk_isdspot
        ,a.spk_precode
        ,a.spk_organizational
        ,a.flg 
	from #spk_consume a
	where NOT EXISTS (SELECT * FROM dbo.spk_consume where a.spk_BPMCode = spk_BPMCode)
PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into dbo.spk_consume from #spk_consume
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
