SELECT
 TRY_CONVERT(UNIQUEIDENTIFIER, [Id]) [Id]
,TRY_CONVERT(UNIQUEIDENTIFIER, [PurPAApplicationId]) [PurPAApplicationId]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([PRId],'00000000-0000-0000-0000-000000000000')) [PRId]
,TRY_CONVERT(UNIQUEIDENTIFIER, [POId]) [POId]
,TRY_CONVERT(UNIQUEIDENTIFIER, ISNULL([PRDetailId],'00000000-0000-0000-0000-000000000000')) [PRDetailId]
,TRY_CONVERT(UNIQUEIDENTIFIER, [PODetailId]) [PODetailId]
,TRY_CONVERT(UNIQUEIDENTIFIER, [GRHistoryId]) [GRHistoryId]
,TRY_CONVERT(UNIQUEIDENTIFIER, [ProductId]) [ProductId]
,[ProductName]
,[InvoiceType]
,[TaxRate]
,ISNULL([PaymentAmount],'0.0') [PaymentAmount]
,[ExtraProperties]
,'NULL' [ConcurrencyStamp]
,[CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CreatorId]) [CreatorId]
,[LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [LastModifierId]) [LastModifierId]
,[IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, [DeleterId]) [DeleterId]
,[DeletionTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [GRApplicationDetailId]) [GRApplicationDetailId]
INTO #PurPAApplicationDetails
FROM (SELECT *,ROW_NUMBER() OVER(PARTITION BY Id ORDER BY Id) RK FROM PLATFORM_ABBOTT_Dev.dbo.PurPAApplicationDetails)a
WHERE RK = 1
;
--drop table #PurPAApplicationInvoices

USE Speaker_Portal_Dev;

UPDATE a 
SET 
 a.[Id] = b.[Id]
,a.[PurPAApplicationId] = b.[PurPAApplicationId]
,a.[PRId] = b.[PRId]
,a.[POId] = b.[POId]
,a.[PRDetailId] = b.[PRDetailId]
,a.[PODetailId] = b.[PODetailId]
,a.[GRHistoryId] = b.[GRHistoryId]
,a.[ProductId] = b.[ProductId]
,a.[ProductName] = b.[ProductName]
,a.[InvoiceType] = b.[InvoiceType]
,a.[TaxRate] = b.[TaxRate]
,a.[PaymentAmount] = b.[PaymentAmount]
,a.[ExtraProperties] = b.[ExtraProperties]
,a.[ConcurrencyStamp] = b.[ConcurrencyStamp]
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = b.[CreatorId]
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = b.[LastModifierId]
,a.[IsDeleted] = b.[IsDeleted]
,a.[DeleterId] = b.[DeleterId]
,a.[DeletionTime] = b.[DeletionTime]
,a.[GRApplicationDetailId] = b.[GRApplicationDetailId]
FROM dbo.PurPAApplicationDetails a
left join #PurPAApplicationDetails  b
ON a.id=b.id;


INSERT INTO dbo.PurPAApplicationDetails
(
 [Id]
,[PurPAApplicationId]
,[PRId]
,[POId]
,[PRDetailId]
,[PODetailId]
,[GRHistoryId]
,[ProductId]
,[ProductName]
,[InvoiceType]
,[TaxRate]
,[PaymentAmount]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[GRApplicationDetailId]
)
SELECT
 [Id]
,[PurPAApplicationId]
,[PRId]
,[POId]
,[PRDetailId]
,[PODetailId]
,[GRHistoryId]
,[ProductId]
,[ProductName]
,[InvoiceType]
,[TaxRate]
,[PaymentAmount]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[GRApplicationDetailId]
FROM #PurPAApplicationDetails a
WHERE not exists (select * from dbo.PurPAApplicationDetails where id=a.id);


--truncate table dbo.PurPAApplicationDetails
