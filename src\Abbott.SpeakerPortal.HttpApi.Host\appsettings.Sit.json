{
	"App": {
		"SelfUrl": "https://speaker-portal-api-s.oneabbott.com",
		"CorsOrigins": "https://*.oneabbott.com,https://*.abbott.com.cn",
		"RedirectAllowedUrls": ""
	},
	"ConnectionStrings": {
		"Default": "Server=speakerstage.database.chinacloudapi.cn;Database=Speaker_Portal_Stg2;TrustServerCertificate=True;User ID=PENGGX4;Password=********************************",
		//"DefaultReadonly": "Server=xlildwxackeakuvb.oneabbott.com;Database=Speaker_Portal_Stg2;TrustServerCertificate=True;User ID=svc_ca191_speaker_stg;Password=********************************;ApplicationIntent=ReadOnly",
		"DefaultIntermediate": "Server=xlildwxackeakuvb.oneabbott.com;Database=Speaker_BPCS_Interface_Dev;TrustServerCertificate=True;User ID=svc_ca191_speaker_stg;Password=********************************",
		"Dataverse": "AuthType=ClientSecret;Url=https://abt-ca191-s.crm.dynamics.cn;ClientId=fe7c9d22-3e35-4d6a-a6a5-5bdbc23346d7;ClientSecret=_xB.dA2Cn9-D-k__CHqxRt.84_4AYMYht8",
		//"Redis": "Redis-CORP-CA191-S.privatelink.redis.cache.chinacloudapi.cn:6380,ssl=True,abortConnect=False,defaultDatabase=10", //password=TSk8Kdr6JhPbQeERJea3jRYknIE9aN5T9AzCaPcfYKE=
		//"Redis": "Redis-CORP-CA191-S.redis.cache.chinacloudapi.cn:6380,password=TSk8Kdr6JhPbQeERJea3jRYknIE9aN5T9AzCaPcfYKE=,ssl=True,abortConnect=False,defaultDatabase=10",
		//以下是连DEV环境的Redis，便于调试STG数据库时，能借用DEV的Redis，代码能跑，但DEV的Redis的数据可能与STG的Redis不一致
		"Redis": "RD-CORP-CA191-D.redis.cache.chinacloudapi.cn:6380,password=bVTMIsWImWsd0zaNLuwEDqQeMVD6z4zvwAzCaIuSInI=,ssl=True,abortConnect=False,defaultDatabase=7",
		"ApplicationInsights": "InstrumentationKey=d2df879d-f7b6-c0d1-82f7-260797341c7e;EndpointSuffix=applicationinsights.azure.cn;IngestionEndpoint=https://chinaeast2-0.in.applicationinsights.azure.cn/;AADAudience=https://monitor.azure.cn/",
		"BpmDb": "Server=WQ00261Q;Database=PLATFORM_ABBOTT_BUSINESSDATA;TrustServerCertificate=True;User ID=svc_ca191_nexbpm_s;Password=********************************"
	},
	"AzureOcr": {
		"Endpoint": "https://ocr-ca191-speaker-s.cognitiveservices.azure.cn/",
		"Key": "fed5eb12b7ff464b87807cf173c0e659"
	},
	"StringEncryption": {
		"DefaultPassPhrase": "L4WyYEEAIRFWZXwd"
	},
	"KeyVaultHost": "https://kv-corp-ca191-speaker-s.vault.azure.cn/",
	"ApplicationInsightKey": "QzvjraSkir0Ai7KlqK2Qk7gxOjP7MSSP",
	"AzureAd": {
		"Instance": "https://login.microsoftonline.com/",
		"TenantId": "5b268d57-2a6f-4e04-b0de-6938583d5ebc",
		"ClientId": "b0142c74-d83a-4982-8851-c463715dfbc0",
		"Scope": "511,openid,email,profile,offline_access,User.Read",
		"CallbackPath": "/login",
		"JwksUrl": "https://login.microsoftonline.com/{0}/discovery/v2.0/keys"
	},
	"AuthServer": {
		"Authority": "https://speaker-portal-api-s.oneabbott.com",
		"RequireHttpsMetadata": "false",
		"SwaggerClientId": "SpeakerPortal_Swagger"
	},
	"Consent": {
		"BaseUrl": "https://consent-portal-api-s.oneabbott.com/",
		"clientId": "06106afa-d79d-4fb0-9add-afd8f22f2fb9",
		"secret": "35f38902-621b-456d-b989-26e20a263228",
		"ConsentCode": "S00000001", //签署版本
		"ConsentName": "个人信息保护政策"
	},
	//微信小程序
	"WxApp": {
		"WxToken": "https://api.weixin.qq.com",
		"AppId": "wx429668899289706a",
		"AppSecret": "********************************",
		"WeChatUrl": "pages/informationConfirmation/index",
		"QRwidth": "300",
		"Enversion": "develop"
	},
	"WcApp": {
		"WcToken": "https://qyapi.weixin.qq.com"
	},
	//"SpeakerAuth": {
	//	"ClientId": "",
	//	"ClientSecret": ""
	//},
	"Blob": {
		"ContainerName": "speaker-portal-file",
		"Address": "https://corpspeakerca191stage.blob.core.chinacloudapi.cn/"
	},
	"SpeakerEmail": {
		"Env": "S",
		"WebHost": "https://speaker-portal-s.oneabbott.com/",
		"SmtpServer": "mail.oneabbott.com",
		"FromEmail": "<EMAIL>",
		"FromName": "NexBPM Admin Test",
		"HelpdeskEmail": "<EMAIL>",
		"BPMHelpdeskEmail": "<EMAIL>",
		"VeevaTimeoutCCEmail": "<EMAIL>,<EMAIL>"
	},
	"SMS": {
		"Url": "http://www.btom.cn:8080",
		"AppId": "EUCP-EMY-SMS1-14R1Z",
		"SecretKey": "4E665C54897BCEE4"
	},
	"Integrations": {
		"DSpot": {
			"Url_WholeProcessReport": "https://oec-drsp-api-s.oneabbott.com/drsp/api/sampling/whole",
			"Url_WholeNotify": "https://speaker-portal-api-s.oneabbott.com/api/integrationnotify/whole",
			"WholeProcessReport_PublicKey": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCHQC7wMlmpSWaHIYXI/Q9h3+qvB5/ITVB983Rx/QAQ6l71sxQIbKhUj3u9dQ3EEJnNma5Ijh5akruC2OGRjiIvMe/edpKdObOE7hdg6o84AmxBJHaVIhDZlPcrQUf/Z+OrhtLihufCMfw7R1cgz8OTxHbfiZWyTj0A3fIFofvELwIDAQAB",
			"Push_Count": "3",

			"TwoElementsAppKey": "0E5D1CBF-1111-41FC-8F53-9DB4E9339B75",
			"TwoElementsAppSecret": "7CEA1F0F-2222-479A-A1BC-432D5ACB8259",
			"TwoElementsUrl": "https://oec-drsp-api-s.oneabbott.com/drsp/api/twoElements/getResult",
			"TwoElementsBatchUrl": "https://oec-drsp-api-s.oneabbott.com/drsp/api/twoElements/getBatchResult"
		},
		"BPCS": {
			"SftpIP": "*************",
			"SftpUser": "FIN_SP_S",
			"SftpPwd": "oxfozPb9c[",
			"SftpFolderLvl1": "SP_ALL_TEST",
			/*
			正式环境:
			"SftpIP": "*************",
			"SftpUser": "FIN_SP",
			"SftpPwd": "9sjjes*nQy"
			"SftpFolderLvl1": "SP_ALL",
			*/

			//"SftpFolderLvl2Prefixes": "AD,JV,JX,RO,SF",

			"SftpFolderLvl3Archive": "ARCHIVE",
			"SftpFolderLvl3Error": "ERROR",
			"SftpFolderLvl3Upload": "WIP",
			"SftpFolderLvl4Error": "ERROR"
		},
		"EPD_HCP_Portal": {
			"AesKey256": "3j7oK2T84U7yE6nG3yB9j4eR2xZ7y2D8"
		},
		"OM": {
			"PrintUrl": "https://speaker-portal-s.oneabbott.com/",
			"BaseUrl": "https://epd-hcpportal-s.oneabbott.com",
			"AppId": "next-bpm",
			"AppSecret": "E53ssz3PnbQSXkLdFv",
			"AppVersion": "1.0"
		},
		"BPMOM": {
			"BaseUrl": "https://epd-olmt-integration-api-s.oneabbott.com",
			"AppSecret": "********************************"
		},
		"Veeva": {
			"ApiUrlHost": "https://atlas.veeva.com:9320/",
			"ApiUrlToken": "api/v1/security/login",
			"ApiUrlEntrance": "api/v1/opendataapi/entrance/",

			"UserName": "abbott.tester",
			"Password": "#GHxrj!W),zWX8c",
			"Provider": "db", //[ db, ldap ]
			"Refresh": true,

			"SftpIP": "abtcnsftpstg.blob.core.chinacloudapi.cn",
			"SftpUser": "abtcnsftpstg.ca191-corp-nbpm.ca191nbpmappuse",
			"SftpPwd": "QGWA8Fi7l8+gn/6QhadF7I7zc4o7ZZUz",
			"SftpPort": "22",

			"AES256Key": "2ab6e6a92beaeea4a4ac1dc68def66e1de7fc0cd77d6656e4f0f0e8f61fe6d2f",
			"AES256IV": "775a0276322d5c9fe7765cdb0e03bf9b"
		},
		"Graph": {
			"ClientId": "3a7444c3-5549-4ec0-9183-d041d8f6be28",
			"TenantId": "5b268d57-2a6f-4e04-b0de-6938583d5ebc",
			"CertName": "[7643] - GIS - China Speaker Portal Hub - Dev.pfx",
			"CertPassword": "fdgf#pJOySSvbfs",
			"Authority": "https://login.microsoftonline.com",
			"Resource": "https://graph.microsoft.com"
		},
		"SOI": {
			"AppKey": "b7860726bfd14fa293679bc1d11e24e4",
			"AppSecret": "8/a5dinzgpjx+ff5c9u01hlNE6gzHa9MShy8mCdGCyg=",
			"BaseUrl": "https://ani-dynastytest.oneabbott.com"
		},
		"MDM": {
			"AppId": "UXCAO8ID4K2E83EW",
			"ApiKey": "84o53b9jEcahmSVkSJ5i6G883oMFLbOA",
			"AppSecret": "A420F74C3B18EC28A4F7A2AD4167EAD6E6A00F3F6BC6BD231A0518A267C4CC90902551F1584D6D20FCA4C89BFF55EA1A5BBBFD2F60B8A2B8187AFFFE7DCB44EE",
			"SubscriptionKey": "571c5262d31a4e52b7cc21a10b9d5a99",
			"BaseUrl": "https://apicenter-s.abbott.com.cn/mnd"
		},
		"CSS": {
			"AppSecret": "********************************",
			"BaseUrl": "https://mndcsstest.oneabbott.com/api"
		}
	},
	"Hangfire": {
		"UserName": "admin",
		"Password": "^Abbott@2024$",
		"DigestRealm": "Abbott hangfire dashboard",
		"Nonce": "cc0912446a063e882b938190297fd06a9c49c744",
		"Opaque": "4d9997009b1936c7ff4b611e1dc24ae151458fdf"
	}
}
