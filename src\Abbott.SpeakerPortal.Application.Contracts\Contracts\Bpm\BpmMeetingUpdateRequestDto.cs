﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Text.Json.Serialization;

namespace Abbott.SpeakerPortal.Contracts.Bpm
{
    public class BpmMeetingUpdateRequestDto
    {
        //[{"serialNumber":"P2412235018","meetingAddress":"常州市妇幼保健院腾讯会议","meetingDate":"2025-01-15","meetingType":"线上加线下",
        //"Speaker":
        [JsonPropertyName("serialNumber")]
        public string SerialNumber { get; set; }

        [JsonPropertyName("meetingAddress")]
        public string MeetingAddress { get; set; }

        [JsonPropertyName("meetingDate")]
        public string MeetingDate { get; set; }

        [JsonPropertyName("meetingType")]
        public string MeetingType { get; set; }

        [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("Speaker")]
        public List<BpmSpeakerDto> Speakers { get; set; }
    }

    public class BpmSpeakerDto
    {
        //"Speaker":
        //[{"vendorCode":"11930","executive":"葛陈","executiveMail":"<EMAIL>","no":"1"},
        //{"vendorCode":"68226","executive":"吴磊","executiveMail":"<EMAIL>","no":"2"}]}]
        [JsonPropertyName("vendorCode")]
        public string VendorCode { get; set; }

        [JsonPropertyName("executive")]
        public string Executive { get; set; }

        [JsonPropertyName("executiveMail")]
        public string ExecutiveMail { get; set; }

        [JsonPropertyName("no")]
        public string No { get; set; }
    }
}
