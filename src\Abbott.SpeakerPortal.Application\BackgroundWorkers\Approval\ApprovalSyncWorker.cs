﻿using System.Threading.Tasks;
using Abbott.SpeakerPortal.Contracts.Common.Approve;
using Abbott.SpeakerPortal.Contracts.Common.PowerApp;
using Volo.Abp.DependencyInjection;

namespace Abbott.SpeakerPortal.BackgroundWorkers.Approval
{
    public class ApprovalSyncWorker : ITransientDependency
    {

        private readonly IApproveService _approveService;

        public ApprovalSyncWorker(IApproveService approveService)
        {
            _approveService = approveService;
        }

        public async Task ExecuteAsync(ApprovalResultsRequestDto requestDto)
        {
            await _approveService.ApprovalResultsAsync(requestDto);
        }
    }
}
