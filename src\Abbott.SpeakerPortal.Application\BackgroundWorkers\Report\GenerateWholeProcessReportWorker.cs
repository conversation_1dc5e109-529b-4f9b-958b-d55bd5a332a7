﻿using Abbott.SpeakerPortal.Contracts.Report;

using Hangfire;
using System.Threading;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.BackgroundWorkers.Report
{
    /// <summary>
    /// 生成全流程报表数据Job
    /// </summary>
    public class GenerateWholeProcessReportWorker : SpeakerPortalBackgroundWorkerBase
    {
        public GenerateWholeProcessReportWorker()
        {
            CronExpression = Cron.Daily(4);
            //CronExpression = "0 7-23/3 * * *";
        }
        
        [AutomaticRetry(Attempts = 0)]
        public override async Task DoWorkAsync(CancellationToken cancellationToken = default)
        {
            await LazyServiceProvider.LazyGetService<IReportService>().GenerateReporWholeProcessAsync();
        }
    }
}
