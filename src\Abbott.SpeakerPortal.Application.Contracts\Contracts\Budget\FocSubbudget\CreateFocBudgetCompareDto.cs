﻿using System;

namespace Abbott.SpeakerPortal.Contracts.Budget.FocSubbudget
{
    public class CreateFocBudgetCompareDto
    {
        public Guid Id { get; set; }
        public string Code { get; set; }
        public Guid BuId { get; set; }
        public int MasterQty { get; set; }

        public int SubQtySum { get; set; }

        public bool IsAdjusted { get { return this.MasterQty >= this.SubQtySum; } }
    }
}
