﻿using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.Report.ProfessionalServiceTax;
using Abbott.SpeakerPortal.Entities.Integration.Dspot;
using Abbott.SpeakerPortal.Entities.Purchase.PurPAApplication;
using Abbott.SpeakerPortal.Entities.Report.ProfessionalServiceTax;
using Abbott.SpeakerPortal.Entities.VendorFinancials;
using Abbott.SpeakerPortal.Entities.Vendors;
using Abbott.SpeakerPortal.IntermediateDB.Models;
using DocumentFormat.OpenXml.Office.CustomUI;
using Hangfire.Logging;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Senparc.Weixin.WxOpen.Entities;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Abbott.SpeakerPortal.AppServices.Report.ProfessionalServiceTax
{
    /// <summary>
    /// 专业服务税费报表
    /// </summary>
    public class ProfessionalTaxReportService : SpeakerPortalAppService, IProfessionalTaxReportService
    {
        private readonly IServiceProvider _serviceProvider;

        private readonly ILogger<ProfessionalTaxReportService> _logger;

        private readonly IScheduleJobLogService _jobLogService;

        public ProfessionalTaxReportService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _logger = serviceProvider.GetService<ILogger<ProfessionalTaxReportService>>();
            _jobLogService = serviceProvider.GetService<IScheduleJobLogService>();
        }

        /// <summary>
        /// 获取Glh增量
        /// </summary>
        /// <returns></returns>
        public async Task GetGlhIncrement()
        {
            var log = _jobLogService.InitSyncLog("ProfessionalServiceTaxReport", "GlhIncrement");
            try
            {
                var incrementTime = DateTime.Now.AddDays(-7);//每天执行（可后续调整）前推7天的数据
                var glhGcr = await GetGlhGcrAsync(incrementTime);
                if (glhGcr.Any())
                    await GlhToProfessionalServiceTaxReportAsync(glhGcr);
            }
            catch (Exception ex)
            {
                log.IsSuccess = false;
                log.Remark = ex.ToString();
            }
            finally 
            {
                _jobLogService.SyncLog(log);
            }
        }

        /// <summary>
        /// 查询GLH Join GCR
        /// </summary>
        /// <returns></returns>
        private async Task<List<GlhJoinGcrDto>> GetGlhGcrAsync(DateTime incrementTime) 
        {
            var bpcsGlhQuery = (await LazyServiceProvider.LazyGetService<IBpcsGlhRepository>().GetQueryableAsync()).AsNoTracking();
            var bpcsGcrQuery = (await LazyServiceProvider.LazyGetService<IBpcsGcrRepository>().GetQueryableAsync()).AsNoTracking();
            //查询coa数据
            string[] crsg04 = [CostNatureCode.C_14492, CostNatureCode.C_14496, CostNatureCode.C_14498, CostNatureCode.C_15471];
            var glhGcr = bpcsGlhQuery.Join(bpcsGcrQuery.Where(x => x.Crsg05 != "9999"), a => a.Lhian, b => b.Crian, (a, b) => new GlhJoinGcrDto
            {
                GlhId = a.Id,
                COA = (b.Crsg01 != null ? b.Crsg01 + "." : "") +
                      (b.Crsg02 != null ? b.Crsg02 + "." : "") +
                      (b.Crsg03 != null ? b.Crsg03 + "." : "") +
                      (b.Crsg04 != null ? b.Crsg04 + "." : "") +
                      (b.Crsg05 != null ? b.Crsg05 + "." : "") +
                      (b.Crsg06 != null ? b.Crsg06 : "") +
                      (b.Crsg07 != null ? "." + b.Crsg07 : ""),
                Lhldes = a.Lhldes,//AP明细行描述
                Lhjnln = a.Lhjnln,//自增长的数字
                Amount = a.Lhdram != 0 ? a.Lhdram : a.Lhcram,
                Lhdref = a.Lhdref,
                Lhjnen = a.Lhjnen,
                UpdateTime = a.UpdateTime,
                Lhreas = a.Lhreas,
                Lhdate = a.Lhdate,
                Lhtime = a.Lhtime,
                Crsg05 = b.Crsg05,
                Crsg04 = b.Crsg04,
                Lhjrf1 = a.Lhjrf1
            }).Where(a => a.UpdateTime > incrementTime);
            _logger.LogWarning($"ProfessionalServiceTaxReport_glhGcr:{glhGcr.Count()}");
            return glhGcr.ToList();
        }

        /// <summary>
        /// 处理GLH增量数据到专业服务费用报表
        /// </summary>
        /// <param name="glhJoinGcrs"></param>
        /// <returns></returns>
        private async Task GlhToProfessionalServiceTaxReportAsync(List<GlhJoinGcrDto> glhJoinGcrs)
        {
            var paQueryable = (await LazyServiceProvider.LazyGetService<IPurPAApplicationRepository>().GetQueryableAsync()).AsNoTracking();
            var professionalTaxReportRepostory = LazyServiceProvider.LazyGetService<IProfessionalServiceTaxReportRepostory>();
            var taxReportQuery = await professionalTaxReportRepostory.GetQueryableAsync();
            var vendorQuery = (await LazyServiceProvider.LazyGetService<IVendorRepository>().GetQueryableAsync()).AsNoTracking();
            var vendorFinancialQuery = (await LazyServiceProvider.LazyGetService<IVendorFinancialRepository>().GetQueryableAsync()).AsNoTracking();
            var bpcsAvmQuery = (await LazyServiceProvider.LazyGetService<IBpcsAvmRepository>().GetQueryableAsync()).AsNoTracking();
            var bpcsPmfvmQuery = (await LazyServiceProvider.LazyGetService<IBpcsPmfvmRepository>().GetQueryableAsync()).AsNoTracking();

            var queryVendorInfo = vendorQuery.Join(vendorFinancialQuery, a => a.Id, b => b.VendorId, (a, b) => new { VendorApplicationId = a == null ? new Guid?() : a.Id, a.HandPhone, b.VendorCode, b.Company })
                .Join(bpcsAvmQuery, a => new { a.VendorCode, a.Company },
                b => new { VendorCode = b.Vendor.ToString(), Company = b.Vcmpny.ToString() }, (a, b) =>
                new { Id = b.Id, a.VendorApplicationId, a.HandPhone, a.VendorCode, a.Company, b.Vtype, b.Vmxcrt,b.Vendor,b.Vcmpny })
                .Join(bpcsPmfvmQuery, l => new { Vnd = l.Vendor, Cmp = l.Vcmpny }, r => new { Vnd = r.Vnderx, Cmp = r.Vmcmpy }, 
                (a, b) => new { a.Id, a.VendorApplicationId, a.HandPhone, a.VendorCode, a.Company, a.Vtype, a.Vmxcrt,b.Vextnm });

            var lhdrefLhjrf1 = glhJoinGcrs.Select(a => a.Lhdref + a.Lhjrf1?.TrimStart('0')).ToArray();
            //查询供应商相关数据
            var paQuery = paQueryable.GroupJoin(queryVendorInfo, a => a.VendorId, b => b.Id, (pa, vendor) => new { pa, vendor })
                .SelectMany(a => a.vendor.DefaultIfEmpty(), (a, b) => new { a.pa, vendor = b })
                .Select(s => new
                {
                    s.pa.Id,
                    s.pa.ApplyUserId,
                    s.pa.ApplicationCode,
                    OriCode = s.pa.ApplicationCode.Substring(1),
                    TraCode = string.Concat(s.pa.ApplicationCode.Substring(1).Substring(1), s.pa.ApplicationCode.Substring(1).Substring(0, 1)),
                    s.pa.ApplyUserName,
                    ApplyUserBuId = s.pa.ApplyUserBu,
                    s.pa.ApplyUserBuName,
                    s.pa.CompanyId,
                    s.pa.CompanyName,
                    s.pa.VendorId,
                    s.pa.VendorCode,
                    s.pa.VendorName,
                    s.vendor.VendorApplicationId,
                    s.vendor.HandPhone,
                    s.vendor.Vmxcrt,
                    s.vendor.Vtype,
                    s.pa.CreationTime,
                    s.vendor.Vextnm
                })//GLH 中Lhjrf1 供应商编码为0开头 更PA中供应商编码匹配不了，需要将中Lhjrf1 转为int 再转string比较
                .Where(p => lhdrefLhjrf1.Contains( p.OriCode + p.VendorCode) || lhdrefLhjrf1.Contains(p.TraCode + p.VendorCode));
            var pas = paQuery.ToList();
            _logger.LogWarning($"ProfessionalServiceTaxReport_PA:{pas.Count()}");
            if (!pas.Any())
                return;
            var taxReports = taxReportQuery.Where(a=>pas.Select(x=>x.ApplicationCode).Contains(a.ApplicationCode)).ToList();
            var updateTaxReports = new List<ProfessionalServiceTaxReport>();
            var insertTaxReports = new List<ProfessionalServiceTaxReport>();
            foreach (var item in pas)
            {
                var taxReport = taxReports.Where(a => a.ApplicationCode == item.ApplicationCode).ToList();
                //var glhGcrs = glhJoinGcrs.Where(a => a.Lhdref == item.OriCode || a.Lhdref == item.TraCode).ToList();
                var glhGcrs = glhJoinGcrs.Where(a => a.Lhdref == item.OriCode || a.Lhdref == item.TraCode).Select(a =>
                {
                    string dateTimeString = $"{a.Lhdate}{a.Lhtime}";
                    DateTime dateTime;
                    bool isParsed = DateTime.TryParseExact(dateTimeString, "yyyyMMddHHmmss", CultureInfo.InvariantCulture, DateTimeStyles.None, out dateTime);
                    return new { a, paidTime = dateTime };
                }).ToList();
                var paidTimeMax = glhGcrs.OrderByDescending(a => a.paidTime).FirstOrDefault();//最新Bpcs支付数据
                var paidTimeMin = glhGcrs.OrderBy(a => a.paidTime).FirstOrDefault();//最早的Bpcs支付数据

                var glhAPIILOrAPV2L = glhGcrs.Where(a => a.a.Lhreas == "APIIL").GroupBy(a => a.a.Lhjnln).Select(a => a.FirstOrDefault()).ToList();
                var glhAPV2L = glhGcrs.Where(a => a.a.Lhreas == "APV2L").GroupBy(a => a.a.Lhjnln).Select(a => a.FirstOrDefault()).ToList();
                if (glhAPV2L.Any())
                    glhAPIILOrAPV2L = glhAPV2L;
                if (taxReport.Any())
                {
                    string oldCoa = "";
                    foreach (var glh in glhAPIILOrAPV2L)
                    {
                        var tax = taxReport.FirstOrDefault(a => a.APNo == glh.a.Lhjnln);
                        if (tax != null)
                        {
                            tax.VendorApplicationId = item.VendorApplicationId;
                            tax.PhoneNumber = item.HandPhone;
                            tax.IDNumber = item.Vmxcrt;
                            oldCoa = tax.COA;

                            MappPiadAsync(tax, paidTimeMax.paidTime, paidTimeMax.a.Lhreas);

                            updateTaxReports.Add(tax);
                        }
                        else
                        {
                            var insertTaxReport = new ProfessionalServiceTaxReport();
                            insertTaxReport.PAId = item.Id;
                            insertTaxReport.ApplicationCode = item.ApplicationCode;
                            insertTaxReport.ApplyUserId = item.ApplyUserId;
                            insertTaxReport.ApplyUserName = item.ApplyUserName;
                            insertTaxReport.ApplyUserBuId = item.ApplyUserBuId;
                            insertTaxReport.ApplyUserBuName = item.ApplyUserBuName;
                            insertTaxReport.CompanyName = item.CompanyName;
                            insertTaxReport.CompanyId = item.CompanyId;
                            insertTaxReport.VendorCode = item.VendorCode;
                            insertTaxReport.VendorId = item.VendorId;
                            insertTaxReport.VendorName = string.IsNullOrWhiteSpace(item.Vextnm) ? item.VendorName : item.Vextnm;
                            insertTaxReport.VendorType = item.Vtype;
                            insertTaxReport.VendorApplicationId = item.VendorApplicationId;
                            insertTaxReport.PhoneNumber = item.HandPhone;
                            insertTaxReport.IDNumber = item.Vmxcrt;
                            insertTaxReport.COA = oldCoa;

                            insertTaxReport.GlhId = glh.a.GlhId;
                            insertTaxReport.CostNatureCode = glh.a.Crsg04;
                            insertTaxReport.APNo = glh.a.Lhjnln;
                            insertTaxReport.APDescribe = glh.a.Lhldes;
                            insertTaxReport.PaidAmount = glh.a.Amount;
                            insertTaxReport.Lhjnen = glh.a.Lhjnen;

                            MappPiadAsync(insertTaxReport, paidTimeMax.paidTime, paidTimeMax.a.Lhreas);

                            insertTaxReports.Add(insertTaxReport);
                        }
                    }
                    if (!glhAPIILOrAPV2L.Any()) 
                    {
                        foreach (var tax in taxReport)
                        {
                            tax.Lhjnen = paidTimeMax.a.Lhjnen;
                            MappPiadAsync(tax, paidTimeMax.paidTime, paidTimeMax.a.Lhreas);

                            updateTaxReports.Add(tax);
                        }
                    }
                }
                else
                {
                    foreach (var glh in glhAPIILOrAPV2L) //获取最新一组数据
                    {
                        var insertTaxReport = new ProfessionalServiceTaxReport();
                        insertTaxReport.PAId = item.Id;
                        insertTaxReport.ApplicationCode = item.ApplicationCode;
                        insertTaxReport.ApplyUserId = item.ApplyUserId;
                        insertTaxReport.ApplyUserName = item.ApplyUserName;
                        insertTaxReport.ApplyUserBuId = item.ApplyUserBuId;
                        insertTaxReport.ApplyUserBuName = item.ApplyUserBuName;
                        insertTaxReport.CompanyName = item.CompanyName;
                        insertTaxReport.CompanyId = item.CompanyId;
                        insertTaxReport.VendorCode = item.VendorCode;
                        insertTaxReport.VendorId = item.VendorId;
                        insertTaxReport.VendorName = string.IsNullOrWhiteSpace(item.Vextnm)? item.VendorName : item.Vextnm;
                        insertTaxReport.VendorType = item.Vtype;
                        insertTaxReport.VendorApplicationId = item.VendorApplicationId;
                        insertTaxReport.PhoneNumber = item.HandPhone;
                        insertTaxReport.IDNumber = item.Vmxcrt;
                        insertTaxReport.COA = paidTimeMin.a.COA?.TrimEnd('.');

                        insertTaxReport.GlhId = glh.a.GlhId;
                        insertTaxReport.CostNatureCode = glh.a.Crsg04;
                        insertTaxReport.APNo = glh.a.Lhjnln;
                        insertTaxReport.APDescribe = glh.a.Lhldes;
                        insertTaxReport.PaidAmount = glh.a.Amount;
                        insertTaxReport.Lhjnen = glh.a.Lhjnen;

                        MappPiadAsync(insertTaxReport,paidTimeMax.paidTime, paidTimeMax.a.Lhreas);

                        insertTaxReports.Add(insertTaxReport);
                    }
                }
            }
            if(updateTaxReports.Any())
                await professionalTaxReportRepostory.UpdateManyAsync(updateTaxReports);
            if(insertTaxReports.Any())
                await professionalTaxReportRepostory.InsertManyAsync(insertTaxReports);
            _logger.LogWarning($"ProfessionalServiceTaxReport_UpdateTaxReport:{updateTaxReports.Count()}");
            _logger.LogWarning($"ProfessionalServiceTaxReport_InsertTaxReport:{insertTaxReports.Count()}");
        }

        /// <summary>
        /// 映射Bpacs 支付类型
        /// </summary>
        /// <param name="taxReport"></param>
        /// <param name="glhJoinGcrs"></param>
        /// <param name="oriCode"></param>
        /// <param name="traCode"></param>
        /// <returns></returns>
        private void MappPiadAsync(ProfessionalServiceTaxReport taxReport,DateTime? paidTime,string lhreas) 
        {
            //APIIL: 通过系统对接生成的记录 A2410140017 => 2410140017 - 待打款
            //APC2L: 人工增加的记录 A2410140017 => 2410140017 => 4101400172 - 待打款
            //APMPL: make payment记录 -打款中
            //APPYL: 批量make payment记录 -打款中
            //APV2L: AP作废(void)记录 - 打款作废
            switch (lhreas)
            {
                case "APIIL":
                case "APC2L":
                    taxReport.PaidType = "Unpaid";
                    taxReport.BpcsUpdateTime = paidTime;
                    break;
                case "APMPL":
                case "APPYL":
                    taxReport.PaidType = "Paid";
                    taxReport.PaidTime = paidTime;//仅当状态Paid时需要填写，填写的即为APMPL/APPYL记录对应的GLH.LHDATE+GLH.LHTIME
                    taxReport.BpcsUpdateTime = paidTime;
                    break;
                case "APV2L"://作废
                    taxReport.PaidType = "Void";
                    taxReport.BpcsUpdateTime = paidTime;
                    break;
            }
        }
    }
}
