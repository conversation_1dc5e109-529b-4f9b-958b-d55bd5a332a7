CREATE PROCEDURE dbo.sp_PurPAFinancialVoucherInfos
AS 
BEGIN

--	drop table #PurPAFinancialVoucherInfos_tmp
select newid() id,* 
into #PurPAFinancialVoucherInfos_tmp
from (
select  
a.PROCID as ProcInstId,
PR_No,
PR_Row,
serialNumber AS PAId,--基于07-1迁移的申请单主信息，以该ID查询对应AUTO_BIZ_T_PaymentApplication_Info.ProcInstId，
--再以单号定位对应的PurPAApplications.ID"
LPVNDR AS VendorName,--
LPINV AS InvoiceReference,--
LPCINA AS InvoiceLineAmount,--
LPINVD AS InvoiceDate,--
LPDESC AS InvoiceDescription,--
LPINVR AS InvoiceReceiptReference,--
LPGLDT AS GeneralLedgerDate,--
'' AS ReasonCode,--留空
'' AS PayType,--留空
LPBANC AS Bank,--
LPIVRD AS InvoiceReceiptDate,--
LPCURR AS CurrencyCode,--
LPEXCH AS RecognitionRate,--
LSEG01 AS CompanyCode,--
LSEG02 AS DivisionCode,--
LSEG03 AS CostCenter,--
LSEG04 AS NatureAccount,--
LSEG05 AS SubAccount,--
LSEG06 AS Location,--
LSEG07 AS Currency,--
'' AS VendorTaxCode,--留空
LPUSER AS [User],
ROW_NUMBER() over(partition by a.PROCID,PR_No,PR_Row,LSEG05 order by a.INPTIME desc) rn
from PLATFORM_ABBOTT_Dev.dbo.ods_T_APInvioce_Data_Log a
left join  PLATFORM_ABBOTT_Dev.dbo.ods_AUTO_BIZ_T_PaymentApplication_Info b
on a.PROCID=b.ProcInstId 
where LPCINA<>0
)a
where rn=1
--drop table #PurPAFinancialVoucherInfos_tmp

IF OBJECT_ID(N'PLATFORM_ABBOTT_Dev.dbo.PurPAFinancialVoucherInfos_tmp', N'U') IS NOT NULL
BEGIN
	update a                      
	set a.ProcInstId              =  b.ProcInstId
       ,a.PR_No                   =  b.PR_No
       ,a.PR_Row                  =  b.PR_Row
       ,a.PAId                    =  b.PAId
       ,a.VendorName              =  b.VendorName
       ,a.InvoiceReference        =  b.InvoiceReference
       ,a.InvoiceLineAmount       =  b.InvoiceLineAmount
       ,a.InvoiceDate             =  b.InvoiceDate
       ,a.InvoiceDescription      =  b.InvoiceDescription
       ,a.InvoiceReceiptReference =  b.InvoiceReceiptReference
       ,a.GeneralLedgerDate       =  b.GeneralLedgerDate
       ,a.ReasonCode              =  b.ReasonCode
       ,a.PayType                 =  b.PayType
       ,a.Bank                    =  b.Bank
       ,a.InvoiceReceiptDate      =  b.InvoiceReceiptDate
       ,a.CurrencyCode            =  b.CurrencyCode
       ,a.RecognitionRate         =  b.RecognitionRate
       ,a.CompanyCode             =  b.CompanyCode
       ,a.DivisionCode            =  b.DivisionCode
       ,a.CostCenter              =  b.CostCenter
       ,a.NatureAccount           =  b.NatureAccount
       ,a.SubAccount              =  b.SubAccount
       ,a.Location                =  b.Location
       ,a.Currency                =  b.Currency
       ,a.VendorTaxCode           =  b.VendorTaxCode
       ,a.[User]                  =  b.[User]
       ,a.rn                      =  b.rn
    from PLATFORM_ABBOTT_Dev.dbo.PurPAFinancialVoucherInfos_tmp a
    left join #PurPAFinancialVoucherInfos_tmp b
    on a.ProcInstId = b.ProcInstId and a.PR_Row = b.PR_Row and a.PR_No = b.PR_No and isnull(a.PAId,'null') = isnull(b.PAId,'null') and a.SubAccount = b.SubAccount
    
    insert into PLATFORM_ABBOTT_Dev.dbo.PurPAFinancialVoucherInfos_tmp
    select a.id
          ,a.ProcInstId
          ,a.PR_No
          ,a.PR_Row
          ,a.PAId
          ,a.VendorName
          ,a.InvoiceReference
          ,a.InvoiceLineAmount
          ,a.InvoiceDate
          ,a.InvoiceDescription
          ,a.InvoiceReceiptReference
          ,a.GeneralLedgerDate
          ,a.ReasonCode
          ,a.PayType
          ,a.Bank
          ,a.InvoiceReceiptDate
          ,a.CurrencyCode
          ,a.RecognitionRate
          ,a.CompanyCode
          ,a.DivisionCode
          ,a.CostCenter
          ,a.NatureAccount
          ,a.SubAccount
          ,a.Location
          ,a.Currency
          ,a.VendorTaxCode
          ,a.[User]
          ,a.rn
    from #PurPAFinancialVoucherInfos_tmp a
    where not exists (select * from PLATFORM_ABBOTT_Dev.dbo.PurPAFinancialVoucherInfos_tmp 
    where a.ProcInstId = ProcInstId and a.PR_Row = PR_Row and a.PR_No = PR_No and isnull(a.PAId,'null') = isnull(PAId,'null') and a.SubAccount = SubAccount)
	PRINT(N'表已存在'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END
ELSE
BEGIN
	select  *  into PLATFORM_ABBOTT_Dev.dbo.PurPAFinancialVoucherInfos_tmp from #PurPAFinancialVoucherInfos_tmp
PRINT(N'落成实体表成功'+FORMAT(SYSDATETIME(), 'yyyy-MM-dd HH:mm:ss.fff'))
END 

END
