--原表数据量:96476
select count(1) from PLATFORM_ABBOTT_Stg.dbo.[ods_T_ERS_MeetingSettlementInfo]; 
--中间层数据量：96476
select count(1) from PLATFORM_ABBOTT_Stg.dbo.[InteOnlineMeetingSettlement];
--目标层数据量:96481
select count(1) from Speaker_Portal_Stg.dbo.[InteOnlineMeetingSettlement]; 
--主表去重数据量:96476
select count(1) from Speaker_Portal_Stg.dbo.[InteOnlineMeetingSettlement] a
join  PLATFORM_ABBOTT_Stg.dbo.[InteOnlineMeetingSettlement] b
on a.Id =b.Id ;
--字段对比： 
with A_into as (
select 
 iif(a.[SerialNumber]      	COLLATE SQL_Latin1_General_CP1_CI_AS   =b.[SerialNumber]          ,N'1','2') [SerialNumber]
,iif(a.[no]                                                        =b.[No]                    ,N'1','2') [No]
,iif(a.[vendorCode]         COLLATE SQL_Latin1_General_CP1_CI_AS   =b.[VendorCode]            ,N'1','2') [VendorCode]
,iif(a.[PdfUrl]             COLLATE SQL_Latin1_General_CP1_CI_AS   =b.[PdfUrl]                ,N'1','2') [PdfUrl]
,iif(a.[PayAmount]                                                 =b.[PayAmount]             ,N'1','2') [PayAmount]
,iif(a.[ModifyRemark]       COLLATE SQL_Latin1_General_CP1_CI_AS   =b.[ModifyRemark]          ,N'1','2') [ModifyRemark]
,iif(a.[ModifyAmountRemark] COLLATE SQL_Latin1_General_CP1_CI_AS   =b.[ModifyAmountRemark]    ,N'1','2') [ModifyAmountRemark]
,iif(a.[Executive]          COLLATE SQL_Latin1_General_CP1_CI_AS   =b.[Executive]             ,N'1','2') [Executive]
,iif(a.[ExecutiveMail]      COLLATE SQL_Latin1_General_CP1_CI_AS   =b.[ExecutiveMail]         ,N'1','2') [ExecutiveMail]
,iif(a.[StartDate]                                                 =b.[StartDate]             ,N'1','2') [StartDate]
,iif(a.[ActualNumber]                                              =b.[ActualNumber]          ,N'1','2') [ActualNumber]    
from ods_T_ERS_MeetingSettlementInfo a
join Speaker_Portal_Stg.dbo.[InteOnlineMeetingSettlement] b
on  a.[SerialNumber]      	    = b.[SerialNumber]          COLLATE SQL_Latin1_General_CP1_CI_AS
and a.[no]                      = b.[No]                   
and a.[vendorCode]              = b.[VendorCode]            COLLATE SQL_Latin1_General_CP1_CI_AS
and a.[PdfUrl]                  = b.[PdfUrl]                COLLATE SQL_Latin1_General_CP1_CI_AS
and a.[PayAmount]               = b.[PayAmount]            
and a.[ModifyRemark]            = b.[ModifyRemark]          COLLATE SQL_Latin1_General_CP1_CI_AS
and a.[ModifyAmountRemark]      = b.[ModifyAmountRemark]    COLLATE SQL_Latin1_General_CP1_CI_AS
and a.[Executive]               = b.[Executive]             COLLATE SQL_Latin1_General_CP1_CI_AS
and a.[ExecutiveMail]           = b.[ExecutiveMail]         COLLATE SQL_Latin1_General_CP1_CI_AS
and a.[StartDate]               = b.[StartDate]             
and a.[ActualNumber]            = b.[ActualNumber]     
)
select count(1) from A_into
where [SerialNumber]       <>1 
or [No]                    <>1
or [VendorCode]            <>1
or [PdfUrl]                <>1
or [PayAmount]             <>1
or [ModifyRemark]          <>1
or [ModifyAmountRemark]    <>1
or [Executive]             <>1
or [ExecutiveMail]         <>1
or [StartDate]             <>1
or [ActualNumber]          <>1

