﻿using Abbott.SpeakerPortal.Entities.Purchase.PurGRApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPOApplication;
using Abbott.SpeakerPortal.Entities.Purchase.PurPRApplication;
using System;

namespace Abbott.SpeakerPortal.Contracts.Integration.Dspot
{
    /// <summary>
    /// 全流程报表，一条满足条件的数据的每个阶段的Id（PR、PO、GR、PA、PAInvoice、出纳）
    /// </summary>
    public class DspotReportContentIdDto
    {
        public Guid PRId { get; set; }

        public Guid? POId { get; set; }

        public Guid? GRId { get; set; }

        public Guid? PAId { get; set; }

        public Guid? PAInvoiceId { get; set; }
    }
}