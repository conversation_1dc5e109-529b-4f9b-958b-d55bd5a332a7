﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Abbott.SpeakerPortal.Contracts.Budget.Subbudget
{
    public class MappingSubBudgetDto
    {
        /// <summary>
        /// 预算code
        /// </summary>
        public string Code { get; set; }
        /// <summary>
        /// 主预算
        /// </summary>
        public string Bu2 { get; set; }
        /// <summary>
        /// 责任人2
        /// </summary>
        public string Owner2 { get; set; }
        /// <summary>
        /// 大区经理
        /// </summary>
        public string RegionManager { get; set; }
        /// <summary>
        /// LMM
        /// </summary>
        public string LMM { get; set; }
        /// <summary>
        /// 大区助理
        /// </summary>
        public string RegionalAssistant { get; set; }
        /// <summary>
        /// 产品经理
        /// </summary>
        public string ProductManager { get; set; }
    }
}
