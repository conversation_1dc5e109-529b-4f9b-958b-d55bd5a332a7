SELECT
 [Id]
,[ApplicationId]
,[VendorCode]
,[OpenId]
,[UnionId]
,COALESCE(HandPhone,'***********')[HandPhone]
,[VendorType]
,COALESCE(BuCode,'0000')[BuCode]
,[Status]
,NULL [BpcsId]
,[EpdId]
,[MndId]
,[VendorId]
,[ApsPorperty]
,[CertificateCode]
,[SPLevel]
,[AcademicLevel]
,[AcademicPosition]
,[BankCode]
,[BankCardNo]
,[BankCity]
,[BankNo]
,[ExtraProperties]
,[ConcurrencyStamp]
, [CreationTime]
,[CreatorId]
,[LastModificationTime] 
,[LastModifierId]
,[IsDeleted]
,'********-0000-0000-0000-***********0'[DeleterId]
,CONVERT(datetime2, null) [DeletionTime]
,'********-0000-0000-0000-***********0'[UserId]
,[PTId]
,[StandardHosDepId]
,[HospitalId]
,[HosDepartment]
,[AttachmentInformation]
,[Description]
,[DraftVersion]
,[PaymentTerm]
,[BankCardImg]
,[DPSCheck]
,[SignedStatus]
,[SignedVersion]
,[HospitalName]
,[PTName]
,[StandardHosDepName]
, [BankSwiftCode]
, [IsAcademician]
, [FormerBPMAcademicPosition]
INTO #Vendors
FROM PLATFORM_ABBOTT.dbo.Vendors
;
--select max(len(ApsPorperty)) from Vendors


--EXEC sp_vendors_ns

USE Speaker_Portal;

UPDATE a 
SET 
 a.[Id] = TRY_CONVERT(UNIQUEIDENTIFIER, b.[Id])
,a.[ApplicationId] = TRY_CONVERT(UNIQUEIDENTIFIER, b.[ApplicationId])
,a.[VendorCode] = b.[VendorCode]
,a.[OpenId] = b.[OpenId]
,a.[UnionId] = b.[UnionId]
,a.[HandPhone] = b.[HandPhone]
,a.[VendorType] = b.[VendorType]
,a.[BuCode] = b.[BuCode]
,a.[Status] = b.[Status]
,a.[EpdId] = b.[EpdId]
,a.[MndId] = b.[MndId]
,a.[VendorId] = b.[VendorId]
,a.[ApsPorperty] = b.[ApsPorperty]
,a.[CertificateCode] = b.[CertificateCode]
,a.[SPLevel] = b.[SPLevel]
,a.[AcademicLevel] = b.[AcademicLevel]
,a.[AcademicPosition] = b.[AcademicPosition]
,a.[BankCode] = b.[BankCode]
,a.[BankCardNo] = b.[BankCardNo]
,a.[BankCity] = b.[BankCity]
,a.[BankNo] = b.[BankNo]
,a.[ExtraProperties] = b.[ExtraProperties]
,a.[ConcurrencyStamp] = ''
,a.[CreationTime] = b.[CreationTime]
,a.[CreatorId] = TRY_CONVERT(UNIQUEIDENTIFIER, b.[CreatorId])
,a.[LastModificationTime] = b.[LastModificationTime]
,a.[LastModifierId] = TRY_CONVERT(UNIQUEIDENTIFIER, b.[LastModifierId])
,a.[IsDeleted] = b.[IsDeleted]
,a.[DeleterId] = TRY_CONVERT(UNIQUEIDENTIFIER, b.[DeleterId])
,a.[DeletionTime] = b.[DeletionTime]
,a.[UserId] = TRY_CONVERT(UNIQUEIDENTIFIER, b.[UserId])
,a.[PTId] = TRY_CONVERT(UNIQUEIDENTIFIER, b.[PTId])
,a.[StandardHosDepId] = TRY_CONVERT(UNIQUEIDENTIFIER, b.[StandardHosDepId])
,a.[HospitalId] = TRY_CONVERT(UNIQUEIDENTIFIER, b.[HospitalId])
,a.[HosDepartment] = b.[HosDepartment]
,a.[AttachmentInformation] = b.[AttachmentInformation]
,a.[Description] = b.[Description]
,a.[DraftVersion] = b.[DraftVersion]
,a.[PaymentTerm] = b.[PaymentTerm]
,a.[BankCardImg] = b.[BankCardImg]
,a.[DPSCheck] = b.[DPSCheck]
,a.[SignedStatus] = b.[SignedStatus]
,a.[SignedVersion] = b.[SignedVersion]
,a.BankSwiftCode=b.BankSwiftCode
,a.[HospitalName] = b.[HospitalName]
,a.[PTName] = b.[PTName]
,a.[StandardHosDepName] = b.[StandardHosDepName]
,a.FormerBPMAcademicPosition=b.FormerBPMAcademicPosition
FROM dbo.Vendors a
left join #Vendors  b
ON a.id=b.id;


INSERT INTO dbo.Vendors
([Id]
,[ApplicationId]
,[VendorCode]
,[OpenId]
,[UnionId]
,[HandPhone]
,[VendorType]
,[BuCode]
,[Status]
,[BpcsId]
,[EpdId]
,[MndId]
,[VendorId]
,[ApsPorperty]
,[CertificateCode]
,[SPLevel]
,[AcademicLevel]
,[AcademicPosition]
,[BankCode]
,[BankCardNo]
,[BankCity]
,[BankNo]
,[ExtraProperties]
,[ConcurrencyStamp]
,[CreationTime]
,[CreatorId]
,[LastModificationTime]
,[LastModifierId]
,[IsDeleted]
,[DeleterId]
,[DeletionTime]
,[UserId]
,[PTId]
,[StandardHosDepId]
,[HospitalId]
,[HosDepartment]
,[AttachmentInformation]
,[Description]
,[DraftVersion]
,[PaymentTerm]
,[BankCardImg]
,[DPSCheck]
,[SignedStatus]
,[SignedVersion]
,[HospitalName]
,[PTName]
,[StandardHosDepName]
,[BankSwiftCode]
,[IsAcademician]
--,[FormerBPMAcademicPosition]
)
SELECT
 TRY_CONVERT(UNIQUEIDENTIFIER, [Id]) [Id]
,[ApplicationId]
,[VendorCode]
,[OpenId]
,[UnionId]
,[HandPhone]
,[VendorType]
,[BuCode]
,[Status]
,[BpcsId]
,[EpdId]
,[MndId]--目标表长度改为[nvarchar](36) NULL
,[VendorId]
,[ApsPorperty]
,[CertificateCode]
,[SPLevel]
,[AcademicLevel]
,[AcademicPosition]
,[BankCode]
,[BankCardNo]
,[BankCity]
,[BankNo]
,[ExtraProperties]
,''[ConcurrencyStamp]
,[CreationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [CreatorId])
,[LastModificationTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, [LastModifierId])
,[IsDeleted]
,TRY_CONVERT(UNIQUEIDENTIFIER, [DeleterId])
,[DeletionTime]
,TRY_CONVERT(UNIQUEIDENTIFIER, UserId) [UserId] --中间层为空
,TRY_CONVERT(UNIQUEIDENTIFIER, [PTId])
,TRY_CONVERT(UNIQUEIDENTIFIER, [StandardHosDepId])
,TRY_CONVERT(UNIQUEIDENTIFIER, [HospitalId])
,[HosDepartment] --目标表长度改为[nvarchar](100) NULL
,[AttachmentInformation] --目标表长度改为[nvarchar](max) NULL
,[Description]
,[DraftVersion]
,[PaymentTerm]
,[BankCardImg]
,[DPSCheck]
,[SignedStatus]
,[SignedVersion]
,[HospitalName]
,[PTName]
,[StandardHosDepName]
,[BankSwiftCode]
,0 [IsAcademician]
--,[FormerBPMAcademicPosition]
FROM #Vendors a
WHERE not exists (select * from dbo.Vendors where id=a.id);



--select [MndId],[HosDepartment],[AttachmentInformation],* from #Vendors

--alter table dbo.Vendors
--alter column [MndId] [nvarchar](36) NULL
--;
--alter table dbo.Vendors
--alter column [HosDepartment] [nvarchar](100) NULL
--;
--alter table Vendors
--alter column [AttachmentInformation] [nvarchar](max) NULL
--;