﻿using Abbott.SpeakerPortal.Contracts.Integration.Bpcs;
using Abbott.SpeakerPortal.Entities.Integration.Dspot;
using Abbott.SpeakerPortal.IntermediateDB.Models;
using Abbott.SpeakerPortal.IntermediateDB.Repositories;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Abbott.SpeakerPortal.AppServices.Integration.Bpcs
{
    public class IntermediateToSpeakerGcrService
        : IntermediateToSpeakerBaseService<Gcr, BpcsGcr, IIntermediateGcrRepository, IBpcsGcrRepository>
        , IIntermediateToSpeakerGcrService
    {
        /// <summary>
        /// The logger
        /// </summary>
        private readonly ILogger<IntermediateToSpeakerGcrService> _logger;

        public IntermediateToSpeakerGcrService(IServiceProvider serviceProvider)
            : base(serviceProvider)
        {
            _logger = _serviceProvider.GetService<ILogger<IntermediateToSpeakerGcrService>>();
        }

        protected override BpcsGcr GetTarByOri(List<BpcsGcr> tarList, Gcr ori)
        {
            if (tarList?.Any() != true)
            {
                return default;
            }

            return tarList.FirstOrDefault(a => a.Crid == ori.Crid
                        && a.Crcoa == ori.Crcoa
                        && a.Crian == ori.Crian);
        }
    }
}