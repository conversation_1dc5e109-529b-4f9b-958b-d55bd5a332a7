﻿using Abbott.SpeakerPortal.AppServices.Common;
using Abbott.SpeakerPortal.Contracts.Integration.Veeva;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Dataverse.Contracts;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.PowerPlatform.Dataverse.Client;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Messages;
using Newtonsoft.Json;
using Renci.SshNet;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.NetworkInformation;
using System.Text;
using System.Threading.Tasks;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace Abbott.SpeakerPortal.AppServices.Integration.Veeva
{
    public class InteVeevaEPDMapping : SpeakerPortalAppService, IInteVeevaEPDMapping
    {
        /// <summary>
        /// The service provider
        /// </summary>
        private readonly IServiceProvider _serviceProvider;

        /// <summary>
        /// The logger
        /// </summary>
        private readonly ILogger<InteVeevaEPDMapping> _logger;

        /// <summary>
        /// The configuration
        /// </summary>
        private readonly IConfiguration _configuration;

        /// <summary>
        /// The dataverseService
        /// </summary>
        private readonly IDataverseService _dataverseService;

        private readonly string _sftpIp;
        private readonly string _sftpPort;
        private readonly string _sftpUser;
        private readonly string _sftpPwd;

        public InteVeevaEPDMapping(IServiceProvider serviceProvider)
        {
            
           _serviceProvider = serviceProvider;
            _logger = serviceProvider.GetService<ILogger<InteVeevaEPDMapping>>();
            _configuration = serviceProvider.GetService<IConfiguration>();
            _dataverseService = serviceProvider.GetService<IDataverseService>();
            //读取veeva sftp配置
            _sftpIp = _configuration["Integrations:Veeva:SftpIP"];
            _sftpUser = _configuration["Integrations:Veeva:SftpUser"];
            _sftpPwd = _configuration["Integrations:Veeva:SftpPwd"];
            _sftpPort = _configuration["Integrations:Veeva:SftpPort"];
        }

        /// <summary>
        /// 更新EPD医院Mapping
        /// 返回错误信息和文件行数
        /// </summary>
        /// <param name="filePath"></param>
        public  (string,int) UpdateEPDMapping(string filePath = null)
        {
            
            int rowNumber = 1;
            int excelRowCount = 0;
            //默认的sftp文件路径
            string remotePath = @"/机构全量主数据_20240131-OD匹配_EPD Mapping.xlsx";
            try
            {                
                if (!string.IsNullOrEmpty(filePath))
                {
                    //指定了远程文件路径，以指定的为准
                    remotePath = filePath;
                }
                var fileByteArray = GetByteArrayFromSftp(remotePath);
                using (Stream stream = new MemoryStream(fileByteArray))
                {
                    //读取pp里主数据
                    var provinceList = GetAllProvince();
                    var cityList = GetAllCity();
                    var hcoList = GetHospitalsList();
                    var buHos = GetBUHospitalsList();
                    //读取excel sheet1数据,跳过表头从第二行开始读
                    var rows = MiniExcelLibs.MiniExcel.Query(stream, sheetName: "Sheet1", startCell: "A2").ToList();

                    OrganizationRequestCollection orgRequests = [];
                    excelRowCount = rows.Count;
                    //组装更新数据
                    foreach (var row in rows)
                    {

                        rowNumber++;
                        //如下字段对应pp里spk_buhospitalmasterdata，
                        //根据医院代码+来源系统识别唯一数据，如数据已存在则更新，如不存在则新增
                        string v_sourcesystem = "EPD HCP Portal";//对应spk_sourcesystem
                        string v_hospitalcode = Convert.ToString(row.B ?? ""); //对应spk_hospitalcode
                        string v_hospitalname = Convert.ToString(row.C ?? "");//对应spk_name
                        string v_spk_veevaid = Convert.ToString(row.E ?? "");//对应spk_veevaid
                        if (string.IsNullOrEmpty(v_spk_veevaid) || v_spk_veevaid.Length != 18)
                        {
                            // veevaid必须是18位，如果不是，跳过该记录
                            continue;
                        }
                        var existsBUHos = buHos.Where(b => string.Equals(b.SourceSystem, v_sourcesystem, StringComparison.OrdinalIgnoreCase)
                                                        && string.Equals(b.HospitalCode, v_hospitalcode, StringComparison.OrdinalIgnoreCase)).FirstOrDefault();
                        bool isProcess = true;
                        if (existsBUHos != null && string.Equals(existsBUHos.Name, v_hospitalname, StringComparison.OrdinalIgnoreCase)
                                             && string.Equals(existsBUHos.VeevaID, v_spk_veevaid, StringComparison.OrdinalIgnoreCase))
                        {
                            //与PP里数据一致，不需要处理
                            isProcess = false;
                        }
                        if (isProcess)
                        {
                            var BUHospitalInstances = new Entity("spk_buhospitalmasterdata");
                            BUHospitalInstances["spk_sourcesystem"] = v_sourcesystem;
                            BUHospitalInstances["spk_hospitalcode"] = v_hospitalcode;
                            BUHospitalInstances["spk_name"] = v_hospitalname;
                            BUHospitalInstances["spk_veevaid"] = v_spk_veevaid;
                            if (existsBUHos != null)
                            {
                                //更新
                                BUHospitalInstances.Id = existsBUHos.Id;
                                orgRequests.Add(new UpdateRequest() { Target = BUHospitalInstances });
                            }
                            else
                            {
                                //新增   
                                orgRequests.Add(new CreateRequest() { Target = BUHospitalInstances });
                            }
                        }


                        //如下字段对应PP里spk_hospitalmasterdata
                        //根据"HCO VID"匹配现有数据中的Veeva ID，如Veeva ID已存在则更新，如不存在则新增
                        string v_hco_name = Convert.ToString(row.F ?? "");//spk_name
                        string v_province = Convert.ToString(row.G ?? "");//spk_hcoprovince
                        string v_city = Convert.ToString(row.H ?? "");//spk_hcocity

                        isProcess = true;
                        var existsHco = hcoList.Where(h => string.Equals(h.HcoVeevaID, v_spk_veevaid)).FirstOrDefault();
                        if (existsHco != null && string.Equals(existsHco.ProvinceName, v_province)
                            && string.Equals(existsHco.CityName, v_city) && existsHco.Status == 923180001)
                        {
                            //与PP里数据一致，不做处理
                            isProcess = false;
                        }
                        if (isProcess)
                        {
                            var hospitalInstance = new Entity("spk_hospitalmasterdata");
                            hospitalInstance["spk_name"] = v_hco_name;
                            hospitalInstance["spk_hospitalstatus"] = new OptionSetValue(923180001);//状态为已验证
                            var pObj = provinceList.Where(p => p.Name == v_province).FirstOrDefault();
                            var cObj = cityList.Where(c => c.ProvinceId == pObj?.Id && c.Name == v_city).FirstOrDefault();
                            if (pObj != null && cObj != null)
                            {
                                //省份
                                hospitalInstance["spk_hcoprovince"] = new EntityReference("spk_province", (Guid)(pObj?.Id));
                                //城市
                                hospitalInstance["spk_hcocity"] = new EntityReference("spk_city", (Guid)(cObj?.Id));
                            }

                            if (existsHco != null)
                            {
                                //更新
                                hospitalInstance.Id = existsHco.Id;
                                UpdateRequest update = new UpdateRequest() { Target = hospitalInstance };
                                update.Parameters["BypassCustomPluginExecution"] = true;
                                orgRequests.Add(update);
                            }
                            else
                            {

                                CreateRequest create = new CreateRequest() { Target = hospitalInstance };
                                create.Parameters["BypassCustomPluginExecution"] = true;
                                //新增
                                orgRequests.Add(create);
                            }
                        }

                    }
                    //异常里不再记录行号
                    if (rowNumber - 1 == rows.Count) rowNumber = -1;
                    //批量更新
                    UpdatePP(orgRequests);
                    //删除redis里pp医院缓存
                    bool isDelSuccess = DeleteHospitalFromRedis();
                }                    
            }
            catch (Exception ex)
            {                
                string msg = "";
                if (rowNumber > 1)
                {
                    msg = $"处理第{rowNumber}行出现异常\r\n";
                }
                throw new Exception(msg, ex);
            }
            return ("", excelRowCount);
        }

        private void UpdatePP(OrganizationRequestCollection orgRequests)
        {
            var repo = _serviceProvider.GetService<IDataverseRepository>();
            int maxCount = ApproveService.ExecuteTransactionMaxmumBatchSize;
            ExecuteTransactionRequest multipleRequest = new ExecuteTransactionRequest()
            {
                Requests = new OrganizationRequestCollection(),
                ReturnResponses = true
            };
            //计算请求次数，每次可以处理1000条
            int executeCount = (orgRequests.Count / maxCount) + 1;

            for (int i = 0; i < executeCount; i++)
            {
                var currentReqs = orgRequests.Skip(i * maxCount).Take(maxCount);
                multipleRequest.Requests.Clear();
                multipleRequest.Requests.AddRange(currentReqs);
                if (currentReqs.Count() > 0)
                {
                   var response = repo.DataverseClient.Execute(multipleRequest);
                  
                }
                
            }
        }

        private (string, string) GetFilePath()
        {
            //默认 veeva放到sftp上的mapping文件
            string remote = @"/机构全量主数据_20240131-OD匹配_EPD Mapping.xlsx";

            var environment = LazyServiceProvider.GetService<IWebHostEnvironment>();
            var local = (Path.Combine(environment.ContentRootPath, @$"wwwroot\Templates\Veeva\机构全量主数据-OD匹配_EPD Mapping{DateTime.Now.ToString("yyyyMMddHHmmss")}.xlsx"));
            return (remote, local);
        }

        private bool DownFileFromSftp(string localPath, string remotePath)
        {

            bool result = false;
            try
            {
                //创建连接对象
                using (var client = new SftpClient(_sftpIp, Convert.ToInt32(_sftpPort), _sftpUser, _sftpPwd))
                {
                    client.Connect(); //连接
                    using (var fs = File.OpenWrite(localPath))
                    {
                        client.DownloadFile(remotePath, fs);
                    }
                    result = true;
                }
            }
            catch (Exception ex)
            {
                result = false;
                _logger.LogError($"Veeva Sync Annual Update Exception: {ex}");
            }
            return result;
        }

        private byte[] GetByteArrayFromSftp(string remotePath)
        {

            try
            {
                //创建连接对象
                using (var client = new SftpClient(_sftpIp, Convert.ToInt32(_sftpPort), _sftpUser, _sftpPwd))
                {
                    client.Connect(); //连接
                    return client.ReadAllBytes(remotePath);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Veeva EPD Mapping Update Exception: {ex}");
                throw;
            }
        }

        private List<HospitalDto> GetHospitalsList()
        {
            return _dataverseService.GetAllHospitals().GetAwaiter().GetResult();
        }

        private List<BUHospitalDto> GetBUHospitalsList()
        {
#if DEBUG
            return _dataverseService.GetAllBUHospitalsFromPP().GetAwaiter().GetResult();
#else
            return _dataverseService.GetAllBUHospitals().GetAwaiter().GetResult();
#endif
        }

        private List<ProvinceDto> GetAllProvince()
        {
            return _dataverseService.GetAllProvince().GetAwaiter().GetResult();
        }

        private List<CityDto> GetAllCity()
        {
            return _dataverseService.GetAllCity().GetAwaiter().GetResult();
        }

        private bool DeleteHospitalFromRedis()
        {
            return _dataverseService.DeleteHospitalFromRedis();
        }
    }
}
