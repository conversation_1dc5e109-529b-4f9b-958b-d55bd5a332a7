﻿using Abbott.SpeakerPortal.Enums;

using System;
using System.Collections.Generic;
using System.Text;

namespace Abbott.SpeakerPortal.Contracts.Agent
{
    public class GetAgencyHistoryListResponseDto
    {
        /// <summary>
        /// 流程类型
        /// </summary>
        //public WorkflowTypeName? WorkflowType { get; set; }
        /// <summary>
        /// 业务类型Id，可空，空则表示“全部”
        /// </summary>
        public Guid? BusinessTypeId { get; set; }
        /// <summary>
        /// 业务类型名称
        /// </summary>
        public string BusinessTypeName { get; set; }
        /// <summary>
        /// 流程类型名称
        /// </summary>
        public string WorkflowTypeName { get; set; }
        /// <summary>
        /// 申请单号
        /// </summary>
        public string FormNo { get; set; }
        /// <summary>
        /// 申请单Id
        /// </summary>
        public Guid FormId { get; set; }
        /// <summary>
        /// 代理时间
        /// </summary>
        public string AgencyTime { get; set; }
        /// <summary>
        /// 创建时间
        /// </summary>
        public string CreateTime { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        //public bool Status { get; set; }
    }
}
