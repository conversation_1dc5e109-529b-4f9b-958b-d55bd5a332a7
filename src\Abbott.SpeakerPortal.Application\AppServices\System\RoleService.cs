﻿using Abbott.SpeakerPortal.Consts;
using Abbott.SpeakerPortal.Contracts.Common;
using Abbott.SpeakerPortal.Contracts.System.Role;
using Abbott.SpeakerPortal.Dataverse;
using Abbott.SpeakerPortal.Domain.Shared.Models;
using Abbott.SpeakerPortal.Localization;
using Abbott.SpeakerPortal.Role;
using Abbott.SpeakerPortal.RoleDto;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Data;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using Volo.Abp.Localization;
using Volo.Abp.ObjectMapping;
using Volo.Abp.PermissionManagement;
using Abbott.SpeakerPortal.Extension;

namespace Abbott.SpeakerPortal.RoleServices
{
    /// <summary>
    /// RoleService
    /// </summary>
    public class RoleService : SpeakerPortalAppService, IRoleService
    {
        /// <summary>
        /// The identity role repository
        /// </summary>
        private readonly IIdentityRoleRepository _identityRoleRepository;

        /// <summary>
        /// The service provider
        /// </summary>
        private readonly IServiceProvider _serviceProvider;

        /// <summary>
        /// The logger
        /// </summary>
        private ILogger<RoleService> _logger;

        private readonly IStringLocalizer<SpeakerPortalResource> _localizer;

        /// <summary>
        /// Initializes a new instance of the <see cref="RoleService"/> class.
        /// </summary>
        /// <param name="serviceProvider">The service provider.</param>
        public RoleService(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
            _identityRoleRepository = serviceProvider.GetService<IIdentityRoleRepository>();
            _logger = serviceProvider.GetService<ILogger<RoleService>>();
            _localizer = serviceProvider.GetService<IStringLocalizer<SpeakerPortalResource>>();
        }

        /// <summary>
        /// Gets the role list.
        /// </summary>
        /// <returns></returns>
        public async Task<MessageResult> GetRoleList()
        {
            try
            {
                var roleList = await _identityRoleRepository.GetListAsync();
                var result = roleList.Where(a => !string.Equals(a.Name, RoleNames.Admin, StringComparison.CurrentCultureIgnoreCase)).Select(p => new RoleListDto
                {
                    Id = p.Id,
                    Name = p.Name,
                    DisplayName = p.GetProperty<string>("DisplayName"),
                    IsDeleted = p.GetProperty<bool>("IsDeleted")
                }).ToList();

                var res = result.Where(p => !p.IsDeleted).ToList();
                return MessageResult.SuccessResult(res);
            }
            catch (Exception ex)
            {
                _logger.LogError($"RoleService's GetRoleList has an error : {ex.Message}");
                MessageResult.FailureResult($"RoleService's GetRoleList has an error : {ex.Message}");
                return null;
            }

        }

        /// <summary>
        /// Gets the role user list.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<MessageResult> GetRoleUserList(RoleUserRequestDto request, CancellationToken cancellationToken)
        {
            try
            {
                var _identityUserRepository = _serviceProvider.GetService<IIdentityUserRepository>();
                var _dataverseService = _serviceProvider.GetService<IDataverseService>();
                var role = await _identityRoleRepository.FindByNormalizedNameAsync(request.Name, false, cancellationToken);
                if (role == null)
                {
                    _logger.LogWarning($"RoleService's GetRoleUserList has an error : The role was not found");
                    return MessageResult.FailureResult("The role was not found");
                }
                var userList = await _identityUserRepository.GetListByNormalizedRoleNameAsync(role.NormalizedName, false, cancellationToken);
                var Count = userList.Count;
                var orderUser = userList.OrderByDescending(b => b.LastModificationTime.HasValue ? b.LastModificationTime : b.CreationTime);
                var pageUsers = orderUser.PagingIf(request);
                var organizations = await _dataverseService.GetOrganizations();
                var users = pageUsers.Select(p =>
                {
                    var mainDepartmentId = p.GetProperty<Guid?>(EntityConsts.IdentityUser.MainDepartmentId);
                    return new RoleUserListDto
                    {
                        StaffCode = p.GetProperty(EntityConsts.IdentityUser.StaffCode).ToString(),
                        StaffName = p.Name,
                        Email = p.Email,
                        Department = mainDepartmentId.HasValue ? organizations.FirstOrDefault(f => f.Id == mainDepartmentId)?.DepartmentName : string.Empty,
                        ModifyTime = p.LastModificationTime.HasValue ? p.LastModificationTime?.ToString("yyyy-MM-dd hh:mm:ss") : p.CreationTime.ToString("yyyy-MM-dd hh:mm:ss")
                    };
                }).ToList();

                var res = new PagedResultDto<RoleUserListDto>()
                {
                    Items = users,
                    TotalCount = Count,
                };

                return MessageResult.SuccessResult(res);
            }
            catch (Exception ex)
            {
                _logger.LogError($"RoleService's GetRoleUserList has an error : {ex.Message}");
                return MessageResult.FailureResult($"RoleService's GetRoleUserList has an error : {ex.Message}");
            }

        }

        /// <summary>
        /// Saves the function permission.
        /// </summary>
        /// <param name="requests">The requests.</param>
        /// <returns></returns>
        public async Task<bool> SaveFunctionPermission(SavePermissionRequestDto request)
        {
            try
            {
                var _permissionManager = _serviceProvider.GetService<IPermissionManager>();
                var _permissionGrantRepository = _serviceProvider.GetService<IRepository<PermissionGrant, Guid>>();
                var permissionGrantQueryable = await _permissionGrantRepository.GetQueryableAsync();
                var rolePermissionIds = permissionGrantQueryable.Where(p => p.ProviderKey == request.RoleName)
                    .Select(d => d.Id);

                await _permissionGrantRepository.DeleteManyAsync(rolePermissionIds, true);
                foreach (var item in request.PermisssionList)
                {
                    await _permissionManager.SetForRoleAsync(request.RoleName, item, true);
                }
                //日志
                var dict = new Dictionary<string, string>
                    {
                        { "UserName", CurrentUser?.UserName },
                        { "Date", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fffffff") },
                        { "ChangeContent", JsonSerializer.Serialize(request)},
                    };
                LazyServiceProvider.LazyGetService<IApplicationInsightService>().TrackEncryptEvent(ApplicationInsightEventNames.UserRoleChange, dict);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"RoleService's SaveFunctionPermission has an error : {ex.Message}");
                return false;
            }

        }

        /// <summary>
        /// Gets the list permissions.
        /// </summary>
        /// <param name="roleName">Name of the role.</param>
        /// <returns></returns>
        public async Task<List<PermissionDefinitionDto>> GetPermissionList()
        {
            try
            {
                var _permissionDefinitionManager = _serviceProvider.GetService<IPermissionDefinitionManager>();
                var group = await _permissionDefinitionManager.GetGroupsAsync();
                var result = group.Where(a => ((LocalizableString)a.DisplayName).ResourceName?.StartsWith("Abp", StringComparison.CurrentCultureIgnoreCase) == false).ToList();
                var res = ObjectMapper.Map<List<PermissionGroupDefinition>, List<PermissionDefinitionDto>>(result);
                ReplaceNames(res);

                return res;
            }
            catch (Exception ex)
            {
                _logger.LogError($"RoleService's GetListPermissions has an error : {ex.Message}");
                return null;
            }

        }

        /// <summary>
        /// Enables the permissions.
        /// </summary>
        /// <param name="roleName">Name of the role.</param>
        /// <returns></returns>
        public async Task<string[]> EnablePermissions(string roleName)
        {
            var permissionGrantRepository = _serviceProvider.GetService<IRepository<PermissionGrant, Guid>>();
            var permissionGrantQueryable = await permissionGrantRepository.GetQueryableAsync();
            var result = permissionGrantQueryable.Where(p => p.ProviderKey == roleName).Select(d => d.Name).ToArray();
            return result;
        }

        /// <summary>
        /// Creates the role asynchronous.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns></returns>
        public async Task<MessageResult> CreateRoleAsync(CreateRoleDto request)
        {
            try
            {
                var _roleRepository = _serviceProvider.GetService<IRepository<IdentityRole, Guid>>();
                var roleQueryable = await _roleRepository.GetQueryableAsync();
                var _permissionManager = _serviceProvider.GetService<IPermissionManager>();
                var foundRole = roleQueryable.FirstOrDefault(r => r.Name == request.NameEn);
                if (foundRole != null)
                {
                    return MessageResult.FailureResult("Not found this role");
                }
                var role = new IdentityRole(Guid.NewGuid(), request.NameEn);
                role.SetProperty("DisplayName", request.NameChi);
                await _roleRepository.InsertAsync(role, true);
                //日志
                var dict = new Dictionary<string, string>
                                {
                                    { "UserName", CurrentUser?.UserName },
                                    { "Date", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fffffff") },
                                    { "ChangeContent", JsonSerializer.Serialize(request)},
                                };
                LazyServiceProvider.LazyGetService<IApplicationInsightService>().TrackEncryptEvent(ApplicationInsightEventNames.UserRoleChange, dict);
                return MessageResult.SuccessResult();
            }
            catch (Exception ex)
            {
                _logger.LogError($"RoleService's CreateRoleAsync has an error : {ex.Message}");
                return MessageResult.FailureResult($"RoleService's CreateRoleAsync has an error : {ex.Message}");
            }
        }

        /// <summary>
        /// Deletes the role asynchronous.
        /// </summary>
        /// <param name="roleName">Name of the role.</param>
        /// <returns></returns>
        public async Task<MessageResult> DeleteRoleAsync(string roleName)
        {
            var _roleRepository = _serviceProvider.GetService<IRepository<IdentityRole, Guid>>();
            var roleQueryable = await _roleRepository.GetQueryableAsync();
            var role = roleQueryable.FirstOrDefault(r => r.Name == roleName || r.NormalizedName == roleName);
            if (role == null)
            {
                return MessageResult.FailureResult("Not found this role");
            }
            await _roleRepository.DeleteAsync(role, true);
            //日志
            var dict = new Dictionary<string, string>
                                {
                                    { "UserName", CurrentUser?.UserName },
                                    { "Date", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fffffff") },
                                    { "ChangeContent", roleName }
                                };
            LazyServiceProvider.LazyGetService<IApplicationInsightService>().TrackEncryptEvent(ApplicationInsightEventNames.UserRoleChange, dict);
            return MessageResult.SuccessResult();
        }

        /// <summary>
        /// Replaces the names.
        /// </summary>
        /// <param name="items">The items.</param>
        public void ReplaceNames(List<PermissionDefinitionDto> items)
        {
            foreach (var item in items)
            {
                item.NameChi = _localizer[((LocalizableString)item.DisplayName).Name];
                ReplaceNames(item.Children);
            }
        }
    }
}
